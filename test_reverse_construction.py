#!/usr/bin/env python3
"""
Test the new reverse construction approach to minimize replace operations.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from diffusion_via_reasoning.trajectory.framework import Trajectory, TrajectoryStep, apply_step
import random
from dataclasses import dataclass
from typing import Dict, Optional, List

@dataclass
class DirectReverseConfig:
    """Configuration for direct reverse trajectory generation."""
    correct_action_prob: float = 0.7  # Probability of taking correct action (toward target)
    temperature: float = 0.8           # Controls randomness in trajectory length
    random_action_weights: Dict[str, float] = None
    seed: Optional[int] = None
    adaptive_correction: bool = True   # Use adaptive correction probability
    min_correct_prob: float = 0.7     # Minimum correct action probability
    max_correct_prob: float = 0.95    # Maximum correct action probability at target length
    
    def __post_init__(self):
        if self.random_action_weights is None:
            # For forward: favor additions, minimal deletions
            self.random_action_weights = {"add": 0.6, "delete": 0.2, "replace": 0.2}


class DirectReverseTrajectoryGenerator:
    """Generate trajectories that build from empty to target sequence (flow-matching style)."""

    def __init__(self, valid_tokens: List[str], config: DirectReverseConfig):
        self.valid_tokens = valid_tokens
        self.config = config

        # Initialize random state for this generator instance
        if config.seed is not None:
            self.random_state = random.Random(config.seed)
        else:
            self.random_state = random.Random()

    def _random_char(self, exclude: Optional[str] = None) -> str:
        """Get a random character from natural amino acids only, optionally excluding one."""
        # Use only the 20 standard amino acids (exclude X, Z and other non-standard ones)
        natural_amino_acids = ['A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L',
                              'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'V', 'W', 'Y']

        # Filter out excluded character and ensure we only use natural amino acids
        choices = [c for c in natural_amino_acids if c != exclude and c in self.valid_tokens]

        # Fallback to all natural amino acids if no valid choices
        if not choices:
            choices = [c for c in natural_amino_acids if c in self.valid_tokens]

        # Final fallback to valid tokens if no natural amino acids available
        if not choices:
            choices = [c for c in self.valid_tokens if c != exclude]

        return self.random_state.choice(choices) if choices else self.random_state.choice(self.valid_tokens)

    def generate(self, target: str) -> Trajectory:
        """Generate a forward trajectory using reverse construction to minimize replace operations."""
        # NEW APPROACH: Start from target and work backwards, then reverse
        # This guarantees minimal replace operations
        
        if not target:
            return Trajectory(initial_sequence="", steps=[], metadata={
                "approach": "reverse_construction",
                "target_length": 0,
                "total_steps": 0,
                "direction": "empty_to_target",
                "replace_operations": 0
            })
        
        # Phase 1: Create reverse trajectory (target -> empty)
        reverse_steps = []
        current = target
        
        # Calculate how many extra steps to add for randomness
        max_extra_steps = int(self.config.temperature * len(target))
        
        # Add some random operations before removing characters
        extra_operations = 0
        while extra_operations < max_extra_steps and len(current) > 0:
            if self.random_state.random() < 0.3:  # 30% chance of random operation
                # Add a random character at random position
                pos = self.random_state.randint(0, len(current))
                char = self._random_char()
                current = current[:pos] + char + current[pos:]
                reverse_steps.append(TrajectoryStep(action="add", position=pos, token=char))
                extra_operations += 1
            elif self.random_state.random() < 0.2 and len(current) > 1:  # 20% chance of replace
                pos = self.random_state.randint(0, len(current) - 1)
                old_char = current[pos]
                new_char = self._random_char(exclude=old_char)
                current = current[:pos] + new_char + current[pos+1:]
                reverse_steps.append(TrajectoryStep(action="replace", position=pos, token=new_char, replaced_token=old_char))
                extra_operations += 1
            else:
                break
        
        # Now remove characters one by one (with some randomness in order)
        positions_to_remove = list(range(len(current)))
        # Shuffle the removal order for more natural trajectories
        self.random_state.shuffle(positions_to_remove)
        
        for i, pos in enumerate(positions_to_remove):
            if len(current) == 0:
                break
            # Adjust position for already removed characters
            actual_pos = min(pos, len(current) - 1)
            char = current[actual_pos]
            current = current[:actual_pos] + current[actual_pos + 1:]
            reverse_steps.append(TrajectoryStep(action="remove", position=actual_pos, token=char))
        
        # Phase 2: Reverse the steps to create forward trajectory
        forward_steps = []
        for step in reversed(reverse_steps):
            if step.action == "add":
                # add becomes remove
                forward_steps.append(TrajectoryStep(action="remove", position=step.position, token=step.token))
            elif step.action == "remove":
                # remove becomes add
                forward_steps.append(TrajectoryStep(action="add", position=step.position, token=step.token))
            elif step.action == "replace":
                # replace with swapped tokens
                forward_steps.append(TrajectoryStep(
                    action="replace", 
                    position=step.position, 
                    token=step.replaced_token,  # swap
                    replaced_token=step.token   # swap
                ))
        
        # Verify the trajectory works correctly
        test_current = ""
        for step in forward_steps:
            test_current = apply_step(test_current, step)
        
        if test_current != target:
            # Fallback to simple deterministic approach if verification fails
            forward_steps = []
            for i, char in enumerate(target):
                forward_steps.append(TrajectoryStep(action="add", position=i, token=char))
        
        # Create metadata
        replace_count = len([s for s in forward_steps if s.action == "replace"])
        metadata = {
            "approach": "reverse_construction",
            "config": {
                "temperature": self.config.temperature,
                "random_action_weights": self.config.random_action_weights,
            },
            "target_length": len(target),
            "total_steps": len(forward_steps),
            "direction": "empty_to_target",
            "replace_operations": replace_count,
            "verification_passed": test_current == target
        }

        return Trajectory(initial_sequence="", steps=forward_steps, metadata=metadata)


def test_reverse_construction():
    """Test the reverse construction approach."""
    # Test protein sequence
    target = "MDPIRCFSCNKIMKSPNEKGMVFVRNMKKEDREQFFKKFNYTRLCCKRMYLSAVNFQDELFQYENARSTLNVDGTITKPF"
    
    # Standard amino acids
    charset = ['A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'V', 'W', 'Y']
    
    print(f"Testing reverse construction for target sequence of length {len(target)}")
    print(f"Target: {target}")
    print()
    
    # Test with different temperature settings
    for temp in [0.3, 0.6, 1.0]:
        print(f"=== Testing with temperature {temp} ===")
        config = DirectReverseConfig(
            temperature=temp,
            seed=42
        )
        
        generator = DirectReverseTrajectoryGenerator(charset, config)
        trajectory = generator.generate(target)
        
        print(f"Total steps: {trajectory.metadata['total_steps']}")
        print(f"Replace operations: {trajectory.metadata['replace_operations']}")
        print(f"Replace percentage: {trajectory.metadata['replace_operations']/trajectory.metadata['total_steps']*100:.1f}%")
        print(f"Verification passed: {trajectory.metadata['verification_passed']}")
        
        # Count action types
        action_counts = {"add": 0, "remove": 0, "replace": 0}
        for step in trajectory.steps:
            action_counts[step.action] += 1
        
        print(f"Action distribution: add={action_counts['add']}, remove={action_counts['remove']}, replace={action_counts['replace']}")
        print()


if __name__ == "__main__":
    test_reverse_construction()
