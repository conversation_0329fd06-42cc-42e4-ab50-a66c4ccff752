#!/usr/bin/env python3
"""
Test script to verify that the new guaranteed convergence trajectory generation works correctly.
"""

import sys
from pathlib import Path
from collections import Counter

# Add the scripts directory to the path
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

from integrated_protein_training import DirectReverseConfig, DirectReverseTrajectoryGenerator
from diffusion_via_reasoning.trajectory.framework import apply_step


def test_trajectory_convergence(target_sequences, config):
    """Test that trajectories converge exactly to their target sequences."""
    charset = set()
    for seq in target_sequences:
        charset.update(seq)
    charset = sorted(list(charset))
    
    generator = DirectReverseTrajectoryGenerator(charset, config)
    
    print(f"Testing trajectory convergence for {len(target_sequences)} sequences...")
    print(f"Config: correct_action_prob={config.correct_action_prob}, temperature={config.temperature}")
    print(f"Random action weights: {config.random_action_weights}")
    print()
    
    all_results = []
    
    for i, target in enumerate(target_sequences):
        print(f"Test {i+1}: Target sequence '{target}' (length: {len(target)})")
        
        try:
            # Generate trajectory
            trajectory = generator.generate(target)
            
            # Apply all steps to verify convergence
            current = ""
            for step_idx, step in enumerate(trajectory.steps):
                current = apply_step(current, step)
                
                # Debug: show first few and last few steps
                if step_idx < 3 or step_idx >= len(trajectory.steps) - 3:
                    print(f"  Step {step_idx+1}: {step.action} '{step.token}' at pos {step.position} -> '{current}'")
                elif step_idx == 3:
                    print(f"  ... ({len(trajectory.steps) - 6} more steps) ...")
            
            # Check convergence
            success = current == target
            print(f"  Final state: '{current}'")
            print(f"  Target state: '{target}'")
            print(f"  Convergence: {'✓ SUCCESS' if success else '✗ FAILED'}")
            print(f"  Total steps: {len(trajectory.steps)}")
            
            # Analyze operations
            operation_counts = Counter()
            for step in trajectory.steps:
                if step.action == "remove":
                    operation_counts["remove"] += 1
                else:
                    operation_counts[step.action] += 1
            
            total_ops = sum(operation_counts.values())
            if total_ops > 0:
                percentages = {op: count/total_ops*100 for op, count in operation_counts.items()}
                print(f"  Operations: {dict(operation_counts)}")
                print(f"  Percentages: {percentages}")
            
            all_results.append({
                'target': target,
                'success': success,
                'final_state': current,
                'steps': len(trajectory.steps),
                'operations': dict(operation_counts)
            })
            
        except Exception as e:
            print(f"  ✗ ERROR: {e}")
            all_results.append({
                'target': target,
                'success': False,
                'final_state': '',
                'steps': 0,
                'operations': {},
                'error': str(e)
            })
        
        print()
    
    # Overall statistics
    print("="*60)
    print("OVERALL RESULTS")
    print("="*60)
    
    success_count = sum(1 for r in all_results if r['success'])
    total_count = len(all_results)
    success_rate = success_count / total_count * 100
    
    print(f"Success rate: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    if success_rate == 100.0:
        print("🎉 ALL TRAJECTORIES CONVERGED SUCCESSFULLY!")
    else:
        print("⚠️  Some trajectories failed to converge:")
        for i, result in enumerate(all_results):
            if not result['success']:
                print(f"  - Test {i+1}: '{result['target']}' -> '{result['final_state']}'")
    
    # Operation balance analysis
    all_operations = Counter()
    for result in all_results:
        for op, count in result['operations'].items():
            all_operations[op] += count
    
    total_ops = sum(all_operations.values())
    if total_ops > 0:
        overall_percentages = {op: count/total_ops*100 for op, count in all_operations.items()}
        print(f"\nOverall operation distribution:")
        print(f"  Counts: {dict(all_operations)}")
        print(f"  Percentages: {overall_percentages}")
        
        # Compare with target weights
        target_weights = config.random_action_weights
        print(f"\nTarget vs Actual:")
        for op_target, op_actual in [("add", "add"), ("delete", "remove"), ("replace", "replace")]:
            target_pct = target_weights.get(op_target, 0) * 100
            actual_pct = overall_percentages.get(op_actual, 0)
            diff = abs(target_pct - actual_pct)
            status = "✓" if diff < 15 else "⚠" if diff < 25 else "✗"
            print(f"  {op_target}: target {target_pct:.1f}%, actual {actual_pct:.1f}%, diff {diff:.1f}% {status}")
    
    return all_results


def main():
    """Main test function."""
    print("Testing Guaranteed Convergence Trajectory Generation")
    print("="*60)
    
    # Test sequences of different lengths and complexities
    test_sequences = [
        "M",  # Single character
        "MK",  # Two characters
        "ACDEFGHIKLMNPQRSTVWY",  # 20 amino acids
        "MKLLVLSLCFATRQVCTQVQGQCGSCWAFGAVEATQGRIIGGQCSGGLGCNSFRY",  # 54 amino acids
        "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALR",  # 100 amino acids
        "",  # Empty sequence
        "AAA",  # Repeated characters
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[:10],  # Mixed characters
    ]
    
    # Test with different configurations
    configs_to_test = [
        ("Conservative", DirectReverseConfig(
            correct_action_prob=0.8,
            temperature=0.5,
            random_action_weights={"add": 0.4, "delete": 0.3, "replace": 0.3},
            seed=42
        )),
        ("Balanced", DirectReverseConfig(
            correct_action_prob=0.6,
            temperature=1.0,
            random_action_weights={"add": 0.4, "delete": 0.3, "replace": 0.3},
            seed=42
        )),
        ("High Temperature", DirectReverseConfig(
            correct_action_prob=0.5,
            temperature=2.0,
            random_action_weights={"add": 0.4, "delete": 0.3, "replace": 0.3},
            seed=42
        )),
    ]
    
    all_success = True
    
    for config_name, config in configs_to_test:
        print(f"\n{'='*60}")
        print(f"TESTING CONFIGURATION: {config_name}")
        print(f"{'='*60}")
        
        results = test_trajectory_convergence(test_sequences, config)
        
        # Check if all succeeded
        config_success = all(r['success'] for r in results)
        if not config_success:
            all_success = False
            print(f"❌ Configuration '{config_name}' had failures!")
        else:
            print(f"✅ Configuration '{config_name}' passed all tests!")
    
    print(f"\n{'='*60}")
    print("FINAL SUMMARY")
    print(f"{'='*60}")
    
    if all_success:
        print("🎉 ALL CONFIGURATIONS PASSED!")
        print("✅ Guaranteed convergence is working correctly")
        print("✅ Ready to generate training data with 100% accuracy")
    else:
        print("❌ Some configurations failed")
        print("⚠️  Need to fix trajectory generation algorithm")


if __name__ == "__main__":
    main()
