#!/usr/bin/env python3
"""
Test the improved trajectory generation with adaptive correction.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from diffusion_via_reasoning.trajectory.framework import Trajectory, TrajectoryStep, apply_step
import random
from dataclasses import dataclass
from typing import Dict, Optional, List

@dataclass
class DirectReverseConfig:
    """Configuration for direct reverse trajectory generation."""
    correct_action_prob: float = 0.7  # Probability of taking correct action (toward target)
    temperature: float = 0.8           # Controls randomness in trajectory length
    random_action_weights: Dict[str, float] = None
    seed: Optional[int] = None
    adaptive_correction: bool = True   # Use adaptive correction probability
    min_correct_prob: float = 0.7     # Minimum correct action probability
    max_correct_prob: float = 0.95    # Maximum correct action probability at target length
    
    def __post_init__(self):
        if self.random_action_weights is None:
            # For forward: favor additions, minimal deletions
            self.random_action_weights = {"add": 0.6, "delete": 0.2, "replace": 0.2}


class DirectReverseTrajectoryGenerator:
    """Generate trajectories that build from empty to target sequence (flow-matching style)."""

    def __init__(self, valid_tokens: List[str], config: DirectReverseConfig):
        self.valid_tokens = valid_tokens
        self.config = config

        # Initialize random state for this generator instance
        if config.seed is not None:
            self.random_state = random.Random(config.seed)
        else:
            self.random_state = random.Random()

    def _random_char(self, exclude: Optional[str] = None) -> str:
        """Get a random character from natural amino acids only, optionally excluding one."""
        # Use only the 20 standard amino acids (exclude X, Z and other non-standard ones)
        natural_amino_acids = ['A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L',
                              'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'V', 'W', 'Y']

        # Filter out excluded character and ensure we only use natural amino acids
        choices = [c for c in natural_amino_acids if c != exclude and c in self.valid_tokens]

        # Fallback to all natural amino acids if no valid choices
        if not choices:
            choices = [c for c in natural_amino_acids if c in self.valid_tokens]

        # Final fallback to valid tokens if no natural amino acids available
        if not choices:
            choices = [c for c in self.valid_tokens if c != exclude]

        return self.random_state.choice(choices) if choices else self.random_state.choice(self.valid_tokens)
    
    def _apply_correct_action(self, current: str, target: str) -> tuple[str, TrajectoryStep]:
        """Add the correct next amino acid from target sequence."""
        if len(current) >= len(target):
            return current, None  # already complete
        # Insert the next correct character at end (simple strategy)
        idx = len(current)
        char = target[idx]
        new_current = current + char
        step = TrajectoryStep(action="add", position=idx, token=char)
        return new_current, step
    
    def _apply_random_action(self, current: str, target: str) -> tuple[str, TrajectoryStep]:
        """Apply a random edit action that may diverge from target, but not too drastically."""
        # Limit random actions to be less disruptive when we're close to the target length
        target_len = len(target)
        current_len = len(current)

        # If we're very close to target length, prefer less disruptive actions
        if abs(current_len - target_len) <= 2:
            actions = ["add", "replace"]
            weights = [0.3, 0.7]  # Prefer replace over add when close to target
        else:
            actions = ["add", "delete", "replace"]
            weights = [
                self.config.random_action_weights["add"],
                self.config.random_action_weights["delete"],
                self.config.random_action_weights["replace"]
            ]

        action = self.random_state.choices(actions, weights=weights)[0]

        if action == "add":
            # When adding, prefer positions near the end to be less disruptive
            if len(current) == 0:
                idx = 0
            else:
                # 60% chance to add at end, 40% chance random position (increased randomness)
                if self.random_state.random() < 0.6:
                    idx = len(current)
                else:
                    idx = self.random_state.randint(0, len(current))
            char = self._random_char()
            new_current = current[:idx] + char + current[idx:]
            trajectory_step = TrajectoryStep(action="add", position=idx, token=char)

        elif action == "delete" and len(current) > 0:
            # When deleting, prefer positions near the end to be less disruptive
            if self.random_state.random() < 0.6:
                idx = len(current) - 1
            else:
                idx = self.random_state.randint(0, len(current) - 1)
            char = current[idx]
            new_current = current[:idx] + current[idx + 1:]
            trajectory_step = TrajectoryStep(action="remove", position=idx, token=char)

        elif action == "replace" and len(current) > 0:
            idx = self.random_state.randint(0, len(current) - 1)
            old_char = current[idx]
            new_char = self._random_char(exclude=old_char)
            new_current = current[:idx] + new_char + current[idx + 1:]
            trajectory_step = TrajectoryStep(
                action="replace",
                position=idx,
                token=new_char,
                replaced_token=old_char
            )
        else:
            # Fallback to add if other actions not possible
            idx = len(current)
            char = self._random_char()
            new_current = current[:idx] + char + current[idx:]
            trajectory_step = TrajectoryStep(action="add", position=idx, token=char)

        return new_current, trajectory_step

    def generate(self, target: str) -> Trajectory:
        """Generate a forward trajectory from empty string to target sequence."""
        current = ""
        steps = []
        total_steps = 0
        max_extra_steps = int(self.config.temperature * len(target))
        max_total_steps = len(target) + max_extra_steps

        # Phase 1: Build towards target with adaptive randomness
        while len(current) < len(target) and total_steps < max_total_steps:
            # Calculate adaptive correct action probability
            if self.config.adaptive_correction:
                progress = len(current) / len(target)
                # Increase correct action probability as we approach target length
                correct_prob = self.config.min_correct_prob + (
                    self.config.max_correct_prob - self.config.min_correct_prob
                ) * progress
            else:
                correct_prob = self.config.correct_action_prob
            
            if self.random_state.random() < correct_prob:
                new_current, step = self._apply_correct_action(current, target)
            else:
                new_current, step = self._apply_random_action(current, target)
            if step is not None:
                current = new_current
                steps.append(step)
                total_steps += 1

        # Phase 2: Ensure final string equals target (intelligent correction)
        # This is necessary because random actions may have caused deviations
        correction_steps_start = len(steps)

        # First, handle length differences
        while len(current) > len(target):
            # Remove extra characters from the end
            step = TrajectoryStep(action="remove", position=len(target), token=current[len(target)])
            current = current[:len(target)]
            steps.append(step)

        while len(current) < len(target):
            # Add missing characters at the end
            idx = len(current)
            char = target[idx]
            step = TrajectoryStep(action="add", position=idx, token=char)
            current = current + char
            steps.append(step)

        # Now fix any character mismatches with intelligent correction
        # Try to minimize replace operations by using add/remove when beneficial
        mismatches = []
        for idx, ch in enumerate(target):
            if idx < len(current) and current[idx] != ch:
                mismatches.append((idx, ch, current[idx]))
        
        # Apply corrections (for now, still use replace but track them)
        for idx, target_char, current_char in mismatches:
            step = TrajectoryStep(action="replace", position=idx, token=target_char, replaced_token=current_char)
            current = current[:idx] + target_char + current[idx+1:]
            steps.append(step)

        # Create metadata
        correction_steps_count = len(steps) - correction_steps_start
        metadata = {
            "approach": "adaptive_flow_matching",
            "config": {
                "correct_action_prob": self.config.correct_action_prob,
                "temperature": self.config.temperature,
                "random_action_weights": self.config.random_action_weights,
                "adaptive_correction": self.config.adaptive_correction,
                "min_correct_prob": self.config.min_correct_prob,
                "max_correct_prob": self.config.max_correct_prob,
            },
            "target_length": len(target),
            "total_steps": len(steps),
            "direction": "empty_to_target",
            "correction_steps": correction_steps_count,
            "building_steps": correction_steps_start,
            "replace_operations": len([s for s in steps[correction_steps_start:] if s.action == "replace"])
        }

        return Trajectory(initial_sequence="", steps=steps, metadata=metadata)


def test_trajectory_generation():
    """Test the improved trajectory generation."""
    # Test protein sequence
    target = "MDPIRCFSCNKIMKSPNEKGMVFVRNMKKEDREQFFKKFNYTRLCCKRMYLSAVNFQDELFQYENARSTLNVDGTITKPF"
    
    # Standard amino acids
    charset = ['A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'V', 'W', 'Y']
    
    print(f"Testing trajectory generation for target sequence of length {len(target)}")
    print(f"Target: {target}")
    print()
    
    # Test with adaptive correction
    print("=== Testing with ADAPTIVE correction ===")
    config_adaptive = DirectReverseConfig(
        adaptive_correction=True,
        min_correct_prob=0.7,
        max_correct_prob=0.95,
        temperature=0.8,
        seed=42
    )
    
    generator_adaptive = DirectReverseTrajectoryGenerator(charset, config_adaptive)
    trajectory_adaptive = generator_adaptive.generate(target)
    
    print(f"Total steps: {trajectory_adaptive.metadata['total_steps']}")
    print(f"Building steps: {trajectory_adaptive.metadata['building_steps']}")
    print(f"Correction steps: {trajectory_adaptive.metadata['correction_steps']}")
    print(f"Replace operations in correction: {trajectory_adaptive.metadata['replace_operations']}")
    print()
    
    # Test with fixed correction
    print("=== Testing with FIXED correction ===")
    config_fixed = DirectReverseConfig(
        adaptive_correction=False,
        correct_action_prob=0.7,
        temperature=0.8,
        seed=42
    )
    
    generator_fixed = DirectReverseTrajectoryGenerator(charset, config_fixed)
    trajectory_fixed = generator_fixed.generate(target)
    
    print(f"Total steps: {trajectory_fixed.metadata['total_steps']}")
    print(f"Building steps: {trajectory_fixed.metadata['building_steps']}")
    print(f"Correction steps: {trajectory_fixed.metadata['correction_steps']}")
    print(f"Replace operations in correction: {trajectory_fixed.metadata['replace_operations']}")
    print()
    
    # Compare results
    print("=== COMPARISON ===")
    print(f"Adaptive correction reduces replace operations by: {trajectory_fixed.metadata['replace_operations'] - trajectory_adaptive.metadata['replace_operations']}")
    print(f"Adaptive correction reduces total correction steps by: {trajectory_fixed.metadata['correction_steps'] - trajectory_adaptive.metadata['correction_steps']}")


if __name__ == "__main__":
    test_trajectory_generation()
