#!/usr/bin/env python3
"""
Test state tracking with the new guaranteed convergence algorithm.
"""

import sys
import os
from pathlib import Path

# Add the scripts directory to the path
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

# Import the necessary modules
from integrated_protein_training import (
    DirectReverseConfig, 
    DirectReverseTrajectoryGenerator,
    create_training_example,
    trajectory_to_states
)

# Add the src directory to the path for apply_step
sys.path.insert(0, str(Path(__file__).parent / "src"))
from diffusion_via_reasoning.trajectory.framework import apply_step


def parse_action_string(action_str):
    """Parse an action string into components."""
    if action_str.startswith("add "):
        parts = action_str.split()
        token = parts[1]
        position = int(parts[4])
        return "add", position, token, None
    elif action_str.startswith("remove "):
        parts = action_str.split()
        token = parts[1]
        position = int(parts[4])
        return "remove", position, token, None
    elif action_str.startswith("replace "):
        parts = action_str.split()
        old_token = parts[1]
        position = int(parts[4])
        new_token = parts[6]
        return "replace", position, new_token, old_token
    else:
        return None, None, None, None


def apply_action_string(current_state, action_str):
    """Apply an action string to the current state."""
    action, position, token, old_token = parse_action_string(action_str)
    
    if action == "add":
        return current_state[:position] + token + current_state[position:]
    elif action == "remove":
        if position < len(current_state):
            return current_state[:position] + current_state[position + 1:]
        else:
            return current_state
    elif action == "replace":
        if position < len(current_state):
            return current_state[:position] + token + current_state[position + 1:]
        else:
            return current_state
    else:
        return current_state


def test_state_tracking_with_new_algorithm():
    """Test state tracking with the new guaranteed convergence algorithm."""
    print("Testing State Tracking with New Algorithm")
    print("="*50)
    
    # Test with a simple sequence
    target = "MKLLVL"
    charset = ['A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'V', 'W', 'Y']
    
    config = DirectReverseConfig(
        correct_action_prob=0.8,
        temperature=0.5,
        random_action_weights={"add": 0.4, "delete": 0.3, "replace": 0.3},
        seed=42
    )
    
    generator = DirectReverseTrajectoryGenerator(charset, config)
    
    print(f"Target sequence: '{target}'")
    
    # Generate trajectory
    trajectory = generator.generate(target)
    print(f"Generated trajectory with {len(trajectory.steps)} steps")
    
    # Get states using the trajectory_to_states function
    states = trajectory_to_states(trajectory)
    print(f"Generated {len(states)} states (including initial)")
    
    # Create training example
    example = create_training_example(target, "test_protein", generator)
    
    print(f"\nTraining example created:")
    print(f"  Final state: '{example['final_state']}'")
    print(f"  Target:      '{target}'")
    print(f"  Match: {example['final_state'] == target}")
    print(f"  Number of steps: {example['num_steps']}")
    
    # Extract actions and state updates from the training example
    actions = []
    state_updates = {}
    
    for line in example['reverse_process']:
        if line.startswith("Current state after step "):
            parts = line.split(": ")
            step_info = parts[0]
            state = parts[1].strip("'")
            step_num = int(step_info.split()[-1])
            state_updates[step_num] = state
        elif not line.startswith("Final state"):
            actions.append(line)
    
    print(f"\nFound {len(actions)} actions and {len(state_updates)} state updates")
    
    # Manually apply actions and compare with recorded states
    current_state = ""
    all_match = True
    
    print("\nVerifying state tracking:")
    for i, action_str in enumerate(actions):
        step_num = i + 1
        
        # Apply action manually
        new_state = apply_action_string(current_state, action_str)
        current_state = new_state
        
        # Check against trajectory_to_states result
        expected_state = states[step_num]  # states[0] is initial, states[1] is after step 1, etc.
        
        if current_state != expected_state:
            print(f"  ❌ Mismatch at step {step_num}")
            print(f"    Action: {action_str}")
            print(f"    Manual:   '{current_state}'")
            print(f"    Expected: '{expected_state}'")
            all_match = False
            break
        
        # Check against recorded state updates
        if step_num in state_updates:
            recorded_state = state_updates[step_num]
            if current_state != recorded_state:
                print(f"  ❌ Mismatch with recorded state at step {step_num}")
                print(f"    Manual:   '{current_state}'")
                print(f"    Recorded: '{recorded_state}'")
                all_match = False
                break
            else:
                print(f"  ✅ Step {step_num}: '{current_state}' (matches recorded)")
    
    # Check final convergence
    final_match = current_state == target
    print(f"\nFinal verification:")
    print(f"  Final state: '{current_state}'")
    print(f"  Target:      '{target}'")
    print(f"  Match: {final_match}")
    
    if all_match and final_match:
        print("\n🎉 All state tracking is CORRECT!")
        return True
    else:
        print("\n❌ State tracking has issues!")
        return False


def main():
    """Main test function."""
    try:
        success = test_state_tracking_with_new_algorithm()
        
        if success:
            print("\n✅ State tracking is working correctly with the new algorithm")
            print("✅ The issue was with the old trajectory generation algorithm")
            print("✅ New training data will have correct intermediate states")
        else:
            print("\n❌ There are still issues with state tracking")
            
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
