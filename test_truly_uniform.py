#!/usr/bin/env python3
"""
Test the truly uniform distribution approach.
"""

def analyze_trajectory_distribution(steps, target_length):
    """Analyze how operations are distributed throughout the trajectory."""
    if not steps:
        return {}
    
    total_steps = len(steps)
    
    # Divide into 5 phases
    phase_size = total_steps // 5
    phases = {}
    
    for i in range(5):
        start_idx = i * phase_size
        if i == 4:  # Last phase gets remaining steps
            end_idx = total_steps
        else:
            end_idx = (i + 1) * phase_size
        
        phase_steps = steps[start_idx:end_idx]
        phase_name = f"Phase {i+1} ({start_idx}-{end_idx})"
        
        action_counts = {"add": 0, "remove": 0, "replace": 0}
        for step in phase_steps:
            action_counts[step[0]] += 1
        
        total_in_phase = len(phase_steps)
        if total_in_phase > 0:
            percentages = {action: count/total_in_phase*100 for action, count in action_counts.items()}
        else:
            percentages = {"add": 0, "remove": 0, "replace": 0}
        
        phases[phase_name] = {
            "counts": action_counts,
            "percentages": percentages,
            "total": total_in_phase
        }
    
    return phases


def simulate_interleaved_uniform(target, temperature=1.0, add_prob=0.3, replace_prob=0.3):
    """Simulate the interleaved uniform algorithm."""
    import random
    random.seed(42)
    
    target_length = len(target)
    extra_steps = int(temperature * target_length)
    
    # Create operation plan
    all_operations = []
    
    # Add target building operations
    for i in range(target_length):
        all_operations.append(("build", i, target[i]))
    
    # Add random operations
    for _ in range(extra_steps):
        rand_val = random.random()
        if rand_val < add_prob:
            all_operations.append(("add_random", None, None))
        elif rand_val < add_prob + replace_prob:
            all_operations.append(("replace_random", None, None))
        else:
            all_operations.append(("remove_random", None, None))
    
    # Add correction operations
    correction_operations = []
    for i in range(target_length):
        correction_operations.append(("correct_char", i, target[i]))
    
    # Interleave corrections
    total_ops = len(all_operations)
    correction_interval = max(1, total_ops // len(correction_operations))
    
    final_plan = []
    correction_index = 0
    
    for i, op in enumerate(all_operations):
        final_plan.append(op)
        if (i + 1) % correction_interval == 0 and correction_index < len(correction_operations):
            final_plan.append(correction_operations[correction_index])
            correction_index += 1
    
    # Add remaining corrections
    while correction_index < len(correction_operations):
        final_plan.append(correction_operations[correction_index])
        correction_index += 1
    
    # Shuffle for uniform distribution
    random.shuffle(final_plan)
    
    # Convert to simplified steps for analysis
    steps = []
    for op_type, _, _ in final_plan:
        if op_type in ["build", "add_random"]:
            steps.append(("add", "A"))
        elif op_type == "remove_random":
            steps.append(("remove", "A"))
        elif op_type in ["replace_random", "correct_char"]:
            steps.append(("replace", "A"))
    
    return steps


def test_distribution():
    """Test the distribution of operations."""
    target = "MDPIRCFSCNKIMKSPNEKGMVFVRNMKKEDREQFFKKFNYTRLCCKRMYLSAVNFQDELFQYENARSTLNVDGTITKPF"
    
    print(f"Testing truly uniform distribution for target of length {len(target)}")
    print()
    
    # Test different configurations
    configs = [
        {"name": "Balanced", "temp": 1.0, "add_prob": 0.3, "replace_prob": 0.3},
        {"name": "More Replace", "temp": 1.2, "add_prob": 0.2, "replace_prob": 0.5},
        {"name": "High Diversity", "temp": 1.5, "add_prob": 0.4, "replace_prob": 0.4}
    ]
    
    for config in configs:
        print(f"=== {config['name']} Configuration ===")
        print(f"Temperature: {config['temp']}, Add: {config['add_prob']}, Replace: {config['replace_prob']}")
        
        steps = simulate_interleaved_uniform(
            target, 
            temperature=config['temp'],
            add_prob=config['add_prob'], 
            replace_prob=config['replace_prob']
        )
        
        print(f"Total steps: {len(steps)}")
        
        # Overall distribution
        total_counts = {"add": 0, "remove": 0, "replace": 0}
        for action, _ in steps:
            total_counts[action] += 1
        
        print("Overall distribution:")
        for action, count in total_counts.items():
            percentage = count / len(steps) * 100 if steps else 0
            print(f"  {action}: {percentage:.1f}% ({count})")
        
        # Phase distribution
        phases = analyze_trajectory_distribution(steps, len(target))
        print("\nDistribution by phases:")
        for phase_name, data in phases.items():
            print(f"  {phase_name}: {data['total']} steps")
            for action in ["add", "remove", "replace"]:
                print(f"    {action}: {data['percentages'][action]:.1f}% ({data['counts'][action]})")
        
        # Check uniformity (standard deviation of percentages across phases)
        action_phase_percentages = {"add": [], "remove": [], "replace": []}
        for phase_data in phases.values():
            for action in ["add", "remove", "replace"]:
                action_phase_percentages[action].append(phase_data['percentages'][action])
        
        print("\nUniformity (lower std dev = more uniform):")
        for action, percentages in action_phase_percentages.items():
            if percentages:
                import statistics
                std_dev = statistics.stdev(percentages) if len(percentages) > 1 else 0
                print(f"  {action} std dev: {std_dev:.2f}")
        
        print()


if __name__ == "__main__":
    test_distribution()
