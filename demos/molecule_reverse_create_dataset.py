import os
import random

import numpy as np
import pandas as pd
from rdkit import Chem
from tqdm import tqdm

from diffusion_via_reasoning.trajectory import (
    TrajectoryGenerator,
    ReverseMoleculeTrajectory,
)

SEED = 1234
random.seed(SEED)
np.random.seed(SEED)

TOKENS = ['#', '(', ')', '+', '-', '/', '1', '2', '3', '4', '5', '6', '7', '=', '@', 'B', 'C', 'F', 'H', 'I', 'N', 'O', 'S', '[', ']', 'c', 'l', 'n', 'o', 'r', 's']

BASE_OUTPUT_DIR = "/gpfs/radev/home/<USER>/Desktop/Diffusion_via_Reasoning/example_data/molecules/diffusion_trajectories"

smiles_df = pd.read_csv("/gpfs/radev/home/<USER>/Desktop/Diffusion_via_Reasoning/example_data/molecules/smiles_1k_samples.csv")
smiles_df = smiles_df.sample(100)
smiles_strings = smiles_df['smiles'].tolist()

smiles_strings_canonicalized = []
for smiles in smiles_strings:
    smiles = smiles.strip()
    smiles = Chem.CanonSmiles(smiles)
    smiles_strings_canonicalized.append(smiles)

smiles_strings_canonicalized = list(set(smiles_strings_canonicalized))
print(f"Number of unique canonicalized smiles: {len(smiles_strings_canonicalized)}")


# Mindset: For a diffusion forward noising process, need delete probability to be higher than add probability
# Estimated length change per step: 0.5 * -1 + 0.25 * 1 + 0.25 * 0 = -0.25
for idx, smiles in enumerate(tqdm(smiles_strings_canonicalized, total=len(smiles_strings_canonicalized))):
    traj_len = len(smiles) * 4
    gen = TrajectoryGenerator(
        valid_tokens=TOKENS,
        action_probs={"add": 0.25, "remove": 0.5, "replace": 0.25},
        num_steps=None,
        seed=1234,
        generate_until_empty=True,
    )

    trajectory = gen.generate(smiles)
    log, states = trajectory.to_log()
    forward_trajectory_df = pd.DataFrame({"log": log, "state": states})
    forward_trajectory_df.to_csv(os.path.join(BASE_OUTPUT_DIR, f"smiles_forward_trajectory_{idx}.csv"), index=False)

    reverse = ReverseMoleculeTrajectory(trajectory)
    log, states = reverse.to_log()
    reverse_trajectory_df = pd.DataFrame({"log": log, "state": states})
    reverse_trajectory_df.to_csv(os.path.join(BASE_OUTPUT_DIR, f"smiles_reverse_trajectory_{idx}.csv"), index=False)
