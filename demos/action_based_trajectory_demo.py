#!/usr/bin/env python3
"""
Comprehensive demo of the action-based trajectory generation system.

This script demonstrates:
1. Molecular trajectory generation with different configurations
2. Protein sequence trajectory generation
3. Forward and backward trajectory creation for training
4. Different noise process types (fixed steps, absorbing state)
5. Training data generation for LLM training
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from diffusion_via_reasoning.trajectories.molecular import MolecularTrajectoryGenerator
from diffusion_via_reasoning.trajectories.protein import ProteinTrajectoryGenerator
from diffusion_via_reasoning.trajectories.base import TrajectoryConfig, NoiseProcessType
from diffusion_via_reasoning.corruption.molecular import MolecularCorruption

def demo_molecular_trajectories():
    """Demonstrate molecular trajectory generation."""
    print("=" * 60)
    print("MOLECULAR TRAJECTORY GENERATION DEMO")
    print("=" * 60)
    
    # Sample molecules
    molecules = [
        "CC(=O)O",           # Acetic acid
        "c1ccccc1C(=O)O",    # Benzoic acid
        "CCO",               # Ethanol
        "CC(C)O"             # Isopropanol
    ]
    
    # Configuration 1: Fixed steps with balanced probabilities
    config1 = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        max_steps=5,
        add_probability=0.2,
        remove_probability=0.4,
        replace_probability=0.4,
        enforce_validity=False,
        max_retries_per_step=10
    )
    
    print("\n1. Fixed Steps Configuration (5 steps, balanced probabilities)")
    print("-" * 50)
    
    corruption = MolecularCorruption()
    for molecule in molecules[:2]:  # Test first two molecules
        print(f"\nMolecule: {molecule}")
        trajectory = corruption.generate_action_trajectory(molecule, config1)
        
        print(f"  Initial: {trajectory.initial_state}")
        print(f"  Actions:")
        for i, action in enumerate(trajectory.actions):
            print(f"    Step {i+1}: {action.action_type} - {action}")
        print(f"  Final: {trajectory.final_state}")
        print(f"  Trajectory length: {len(trajectory.actions)}")
    
    # Configuration 2: Absorbing state (high remove probability)
    config2 = TrajectoryConfig(
        noise_process_type=NoiseProcessType.ABSORBING_STATE,
        max_steps=20,
        add_probability=0.1,
        remove_probability=0.7,
        replace_probability=0.2,
        enforce_validity=False,
        max_retries_per_step=10
    )
    
    print("\n\n2. Absorbing State Configuration (high remove probability)")
    print("-" * 50)
    
    molecule = molecules[0]  # Acetic acid
    trajectory = corruption.generate_action_trajectory(molecule, config2)
    
    print(f"\nMolecule: {molecule}")
    print(f"  Initial: {trajectory.initial_state}")
    print(f"  Actions:")
    for i, action in enumerate(trajectory.actions):
        print(f"    Step {i+1}: {action.action_type}")
    print(f"  Final: {trajectory.final_state}")
    print(f"  Reached absorbing state: {len(trajectory.final_state.strip()) == 0}")

    # Configuration 3: Motif operations demonstration
    motif_config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        max_steps=3,
        add_probability=0.1,
        remove_probability=0.1,
        replace_probability=0.1,
        motif_add_probability=0.4,
        motif_remove_probability=0.2,
        motif_replace_probability=0.2,
        enforce_validity=False,
    )

    print("\n\n3. Motif Operations Configuration")
    print("-" * 50)

    trajectory = corruption.generate_action_trajectory(molecules[2], motif_config)

    print(f"\nMolecule: {molecules[2]}")
    print(f"  Initial: {trajectory.initial_state}")
    print(f"  Actions:")
    for i, action in enumerate(trajectory.actions):
        if hasattr(action, 'motif_smiles'):
            print(f"    Step {i+1}: {action.action_type} - {action.motif_smiles}")
        else:
            print(f"    Step {i+1}: {action.action_type}")
    print(f"  Final: {trajectory.final_state}")
    
    # Demonstrate backward trajectory for training
    print("\n\n4. Backward Trajectory for Training")
    print("-" * 50)
    
    forward_traj = corruption.generate_action_trajectory(molecules[1], config1)
    backward_traj = corruption.generate_backward_trajectory(forward_traj)
    
    print(f"\nForward trajectory:")
    print(f"  {forward_traj.initial_state} -> {forward_traj.final_state}")
    print(f"  Actions: {[a.action_type for a in forward_traj.actions]}")
    
    print(f"\nBackward trajectory (for LLM training):")
    print(f"  Start with: {backward_traj.initial_state}")
    print(f"  Target: {backward_traj.final_state}")
    print(f"  Training sequence:")
    for i, action in enumerate(backward_traj.actions):
        intermediate = backward_traj.apply_partial(i + 1)
        print(f"    Step {i+1}: {action.action_type} -> {intermediate}")

def demo_protein_trajectories():
    """Demonstrate protein trajectory generation."""
    print("\n\n" + "=" * 60)
    print("PROTEIN TRAJECTORY GENERATION DEMO")
    print("=" * 60)
    
    # Sample protein sequences
    proteins = [
        "MKFLVLLFNILCLFPVLA",     # Signal peptide
        "ACDEFGHIKLMNPQRSTVWY",   # All 20 amino acids
        "MKATVKKKESSSKRKT",       # Basic protein
        "EEEEDDDDEEEEDDDD"        # Acidic protein
    ]
    
    # Configuration for protein trajectories
    protein_config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        max_steps=4,
        add_probability=0.3,
        remove_probability=0.4,
        replace_probability=0.3,
        enforce_validity=True,  # Ensure valid amino acid sequences
        max_retries_per_step=10
    )
    
    print("\n1. Protein Action-Based Trajectories")
    print("-" * 50)
    
    generator = ProteinTrajectoryGenerator(protein_config)
    
    for protein in proteins[:2]:  # Test first two proteins
        print(f"\nProtein: {protein}")
        trajectory = generator.generate_trajectory(protein)
        
        print(f"  Initial: {trajectory.initial_state}")
        print(f"  Actions:")
        for i, action in enumerate(trajectory.actions):
            print(f"    Step {i+1}: {action.action_type} - {action}")
        print(f"  Final: {trajectory.final_state}")
        print(f"  Valid sequence: {generator.is_valid_state(trajectory.final_state)}")
    
    # Demonstrate motif operations
    print("\n\n2. Protein Motif Operations")
    print("-" * 50)
    
    # Configuration that favors add operations (to show motifs)
    motif_config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        max_steps=3,
        add_probability=0.8,
        remove_probability=0.1,
        replace_probability=0.1,
        enforce_validity=True
    )
    
    motif_generator = ProteinTrajectoryGenerator(motif_config)
    protein = proteins[0]
    
    print(f"\nProtein: {protein}")
    trajectory = motif_generator.generate_trajectory(protein)
    
    print(f"  Initial: {trajectory.initial_state}")
    print(f"  Actions (add-focused to show motifs):")
    for i, action in enumerate(trajectory.actions):
        if hasattr(action, 'motif'):
            print(f"    Step {i+1}: {action.action_type} - motif '{action.motif}' at pos {action.position}")
        elif hasattr(action, 'amino_acid'):
            print(f"    Step {i+1}: {action.action_type} - '{action.amino_acid}' at pos {action.position}")
        else:
            print(f"    Step {i+1}: {action.action_type} - {action}")
    print(f"  Final: {trajectory.final_state}")

def demo_training_data_generation():
    """Demonstrate training data generation for both molecules and proteins."""
    print("\n\n" + "=" * 60)
    print("TRAINING DATA GENERATION DEMO")
    print("=" * 60)
    
    # Molecular training data
    print("\n1. Molecular Training Data")
    print("-" * 50)
    
    molecules = ["CC(=O)O", "CCO", "c1ccccc1"]
    config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        max_steps=3,
        add_probability=0.2,
        remove_probability=0.5,
        replace_probability=0.3,
        enforce_validity=False
    )
    
    corruption = MolecularCorruption()
    forward_trajs, backward_trajs = corruption.generate_training_data(
        molecules, num_trajectories_per_molecule=2, config=config
    )
    
    print(f"Generated {len(forward_trajs)} forward and {len(backward_trajs)} backward trajectories")
    print(f"Sample training trajectory:")
    sample = backward_trajs[0]
    print(f"  Start: {sample.initial_state}")
    print(f"  Target: {sample.final_state}")
    print(f"  Actions: {[a.action_type for a in sample.actions]}")
    
    # Protein training data
    print("\n\n2. Protein Training Data")
    print("-" * 50)
    
    proteins = ["MKFLVL", "ACDEFG", "KKKRRR"]
    protein_config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        max_steps=3,
        add_probability=0.3,
        remove_probability=0.4,
        replace_probability=0.3,
        enforce_validity=True
    )
    
    protein_generator = ProteinTrajectoryGenerator(protein_config)
    forward_trajs = protein_generator.generate_training_trajectories(
        proteins, num_trajectories_per_sequence=2
    )
    backward_trajs = protein_generator.generate_backward_trajectories(forward_trajs)
    
    print(f"Generated {len(forward_trajs)} forward and {len(backward_trajs)} backward trajectories")
    print(f"Sample training trajectory:")
    sample = backward_trajs[0]
    print(f"  Start: {sample.initial_state}")
    print(f"  Target: {sample.final_state}")
    print(f"  Actions: {[a.action_type for a in sample.actions]}")

def demo_serialization():
    """Demonstrate trajectory serialization for storage and loading."""
    print("\n\n" + "=" * 60)
    print("TRAJECTORY SERIALIZATION DEMO")
    print("=" * 60)
    
    config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        max_steps=3,
        add_probability=0.3,
        remove_probability=0.4,
        replace_probability=0.3
    )
    
    # Generate a molecular trajectory
    corruption = MolecularCorruption()
    trajectory = corruption.generate_action_trajectory("CC(=O)O", config)
    
    # Serialize to dictionary
    traj_dict = trajectory.to_dict()
    
    print("\nSerialized trajectory structure:")
    print(f"  Initial state: {traj_dict['initial_state']}")
    print(f"  Final state: {traj_dict['final_state']}")
    print(f"  Number of actions: {len(traj_dict['actions'])}")
    print(f"  Config: {traj_dict['config']}")
    print(f"  Sample action: {traj_dict['actions'][0] if traj_dict['actions'] else 'None'}")
    
    print("\nThis serialized format can be:")
    print("  - Saved to JSON files for dataset storage")
    print("  - Used for LLM training data preparation")
    print("  - Loaded back into Trajectory objects")
    print("  - Shared between different systems")

def main():
    """Run all demonstrations."""
    print("ACTION-BASED TRAJECTORY GENERATION SYSTEM DEMO")
    print("=" * 60)
    print("This demo showcases the complete trajectory generation system")
    print("for both molecular SMILES and protein sequences.")
    
    try:
        demo_molecular_trajectories()
        demo_protein_trajectories()
        demo_training_data_generation()
        demo_serialization()
        
        print("\n\n" + "=" * 60)
        print("DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("\nKey features demonstrated:")
        print("✓ Action-based trajectory generation")
        print("✓ Forward and backward trajectories")
        print("✓ Multiple noise process types")
        print("✓ Molecular and protein support")
        print("✓ Training data generation")
        print("✓ Trajectory serialization")
        print("✓ Configurable probabilities and constraints")
        
    except Exception as e:
        print(f"\nError during demo: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 