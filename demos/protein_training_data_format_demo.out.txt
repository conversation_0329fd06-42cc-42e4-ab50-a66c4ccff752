Residue vocabulary (20 standard amino acids):
   A R N D C Q E G H I L K M F P S T W Y V
Motif vocabulary (common motifs):
   KR EE DD RR KK PP GG AA CC WW FF ST TY SY

Legend (0-based indexing):
  A6B      = Replace A at 6 with B
  7C       = Insert C at 7
  -5F      = Remove F at 5
  A3/B7    = Swap A at 3 with B at 7
  7[KR]    = Insert motif KR at 7
  -[KR]5   = Remove motif KR at 5
  [KR]5[EE]= Replace motif KR at 5 with EE
  [KR]5[EE]= Replace motif KR at 5 with EE (motif replace)

=== Default (biologically realistic) ===

Move probabilities:
  replace_residue: 0.50
  insert_residue: 0.10
  remove_residue: 0.30
  insert_motif: 0.05
  remove_motif: 0.05

Noise process: fixed_steps
Input:  Reconstruct protein from: YANYDVLWQN
Output:
  Y3H
  8R
  V5A
  Y0M
  H3V
  [current state: MANVDALWRQN]
  W7H
  4[FFW]
  Q12L
  -2N
  2A
  [current state: MAAVFFWDALHRLN]
  R11Y
  11[RP]
  2A
  -6F
  W6N
  [current state: MAAAVFNDALHRPYLN]
  3L
  A4L
  10I
  Y15V
  -[AA]1
  [current state: MLLVFNDAILHRPVLN]
  L1F
  N5L
  H10C
  11L
  D6F
  [current state: MFLVFLFAILCLRPVLN]
  A7<->N16
  -4F
  R11F
  4L
  1K
  [current state: MKFLVLLFNILCLFPVLA]
Result: MKFLVLLFNILCLFPVLA

Move probabilities:
  replace_residue: 0.50
  insert_residue: 0.10
  remove_residue: 0.30
  insert_motif: 0.05
  remove_motif: 0.05

Noise process: fixed_steps
Input:  Reconstruct protein from: SVILKEIL
Output:
  I2C
  -5E
  2H
  L7L
  3I
  [current state: SVHICLKIL]
  0D
  C5<->K7
  3V
  -9I
  H4<->L7
  [current state: DSVVLIKHCL]
  D0M
  S1L
  L1<->K6
  5[CC]
  V2<->H9
  [current state: MKHVLCCILVCL]
  L4N
  4L
  5L
  9C
  C8L
  [current state: MKHVLLNCLCILVCL]
  I10<->L14
  11V
  C7<->I15
  -[CC]14
  V13A
  [current state: MKHVLLNILCLVLA]
  2F
  H3L
  12[FP]
  7F
Result: MKFLVLLFNILCLFPVLA

Move probabilities:
  replace_residue: 0.50
  insert_residue: 0.10
  remove_residue: 0.30
  insert_motif: 0.05
  remove_motif: 0.05

Noise process: fixed_steps
Input:  Reconstruct protein from: AQIENRSWI
Output:
  -2I
  S5A
  6[FR]
  F6S
  E2Q
  [current state: AQQNRASRWI]
  A5K
  A0F
  R4P
  2[GA]
  A3H
  [current state: FQGHQNPKSRWI]
  8V
  5L
  11T
  1[FK]
  8M
  [current state: FFKQGHQLMNPKVSTRWI]
  0K
  -[FF]1
  [KK]0[DE]
  V11<->R14
  Q5<->K10
  [current state: DEQGHKLMNPQRSTVWI]
  5I
  Q2R
  I17L
  L17Y
  0A
  [current state: ADERGHIKLMNPQRSTVWY]
  1C
  -4R
  4F
Result: ACDEFGHIKLMNPQRSTVWY

Move probabilities:
  replace_residue: 0.50
  insert_residue: 0.10
  remove_residue: 0.30
  insert_motif: 0.05
  remove_motif: 0.05

Noise process: fixed_steps
Input:  Reconstruct protein from: APPHRFTIFENPHAI
Output:
  P2C
  H12Q
  I14W
  2R
  R5<->F9
  [current state: APRCHFFTIRENPQAW]
  W15C
  16[FFD]
  [FF]16[KW]
  -14A
  1R
  [current state: ARPRCHFFTIRENPQCKWD]
  [FF]6[QS]
  C4<->C15
  -[ST]7
  5G
  C14C
  [current state: ARPRCGHQIRENPQCKWD]
  R9K
  K15C
  16[SAV]
  R3C
  R1<->C14
  [current state: ACPCCGHQIKENPQRCSAVWD]
  [CC]3[EF]
  P2D
  A17T
  E10L
  -15C
  [current state: ACDEFGHQIKLNPQRSTVWD]
  G5G
  -7Q
  10M
  D19Y
Result: ACDEFGHIKLMNPQRSTVWY

Move probabilities:
  replace_residue: 0.50
  insert_residue: 0.10
  remove_residue: 0.30
  insert_motif: 0.05
  remove_motif: 0.05

Noise process: fixed_steps
Input:  Reconstruct protein from: WKRVKL
Output:
  3K
  L6R
  W0<->R2
  K1<->R6
  R1S
  [current state: RSWKVKK]
  7[CT]
  -2W
  R0M
  6R
  K2<->T8
  [current state: MSTVKKRCK]
  C7I
  5[WA]
  4K
  6[AI]
  I12S
  [current state: MSTVKKAIWAKRSK]
  1C
  A7K
  C1<->S13
  14C
  -[CC]13
  [current state: MSSTVKKKIWAKRK]
  W9A
  14T
  S1<->I8
  I1K
  [AA]9[IS]
  [current state: MKSTVKKKSISKRKT]
  S10A
  I9S
  8E
  S2<->A11
Result: MKATVKKKESSSKRKT

Move probabilities:
  replace_residue: 0.50
  insert_residue: 0.10
  remove_residue: 0.30
  insert_motif: 0.05
  remove_motif: 0.05

Noise process: fixed_steps
Input:  Reconstruct protein from: SKHA
Output:
  1Y
  Y1E
  S0<->A4
  E1K
  5A
  [current state: AKKHSA]
  2I
  A6E
  A0<->K3
  K0V
  4T
  [current state: VKIATHSE]
  A3K
  T4<->S6
  4E
  E8P
  E4<->P8
  [current state: VKIKPSHTE]
  H6R
  -8E
  I2C
  7L
  P4E
  [current state: VKCKESRLT]
  L7K
  6V
  7[SD]
  0[AT]
  8S
  [current state: ATVKCKESSVSDRKT]
  -9V
  C4K
  0[MK]
  D12K
Result: MKATVKKKESSSKRKT

=== Point Mutations (residue replace) ===

Move probabilities:
  replace_residue: 1.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 0.00

Noise process: point_mutations
Input:  Reconstruct protein from: MLFSVLFCNSLCMHPPNV
Output:
  F6I
  N16I
  M12M
  N8H
  P15H
  [current state: MLFSVLICHSLCMHPHIV]
  H13K
  C7<->S9
  L1<->M12
  C9R
  H15<->V17
  [current state: MMFSVLISHRLCLKPVIH]
  H8F
  F8W
  I6F
  M0<->I16
  W8N
  [current state: IMFSVLFSNRLCLKPVMH]
  K13N
  M1Y
  S7L
  R9I
  L10<->M16
  [current state: IYFSVLFLNIMCLNPVLH]
  N13F
  H17T
  F6<->M10
  I0M
  M6L
  [current state: MYFSVLLLNIFCLFPVLT]
  T17A
  S3M
  L7<->F10
  M3L
  Y1K
  [current state: MKFLVLLFNILCLFPVLA]
Result: MKFLVLLFNILCLFPVLA

Move probabilities:
  replace_residue: 1.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 0.00

Noise process: point_mutations
Input:  Reconstruct protein from: KGWTPMYWVPFFPAADML
Output:
  K0L
  L17A
  [FF]10[QL]
  P4V
  D15D
  [current state: LGWTVMYWVPQLPAADMA]
  G1K
  [AA]13[ED]
  M5<->D15
  M15S
  P9I
  [current state: LKWTVDYWVIQLPEDSMA]
  Y6Q
  E13F
  V8<->S15
  F13I
  D14F
  [current state: LKWTVDQWSIQLPIFVMA]
  S8P
  P12L
  I13F
  T3W
  W3<->L11
  [current state: LKWLVDQWPIQWLFFVMA]
  Q10G
  D5L
  P8W
  [WW]7[FN]
  G10L
  [current state: LKWLVLQFNILWLFFVMA]
  Q6L
  W2F
  W11C
  F14P
  L0<->M16
  [current state: MKFLVLLFNILCLFPVLA]
Result: MKFLVLLFNILCLFPVLA

Move probabilities:
  replace_residue: 1.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 0.00

Noise process: point_mutations
Input:  Reconstruct protein from: HCFESDGRLQSWMVKNTFPM
Output:
  D5M
  K14D
  P18W
  S10<->W11
  D14P
  [current state: HCFESMGRLQWSMVPNTFWM]
  M19Y
  Q9T
  F2W
  L8K
  W2<->S4
  [current state: HCSEWMGRKTWSMVPNTFWY]
  M5D
  S2<->G6
  S6D
  W10M
  [DD]5[GD]
  [current state: HCGEWGDRKTMSMVPNTFWY]
  D6E
  S11<->N15
  T9V
  P14P
  H0<->E6
  [current state: ECGEWGHRKVMNMVPSTFWY]
  V13P
  G2D
  V9L
  E0W
  W0A
  [current state: ACDEWGHRKLMNMPPSTFWY]
  [PP]13[QR]
  M12P
  R7I
  W4F
  F17V
  [current state: ACDEFGHIKLMNPQRSTVWY]
Result: ACDEFGHIKLMNPQRSTVWY

Move probabilities:
  replace_residue: 1.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 0.00

Noise process: point_mutations
Input:  Reconstruct protein from: HHDKCIHWYDMKDVEDTNWM
Output:
  N17Y
  M10H
  K11S
  D15H
  Y8<->D12
  [current state: HHDKCIHWDDHSYVEHTYWM]
  V13Q
  I5F
  W7<->M19
  Y17C
  M7I
  [current state: HHDKCFHIDDHSYQEHTCWW]
  [SY]11[YY]
  F5E
  W18W
  H15E
  D9A
  [current state: HHDKCEHIDAHYYQEETCWW]
  Y12P
  [EE]14[RS]
  H0L
  K3E
  C17V
  [current state: LHDECEHIDAHYPQRSTVWW]
  Y11<->W19
  E5G
  D8K
  L0<->A9
  H10C
  [current state: AHDECGHIKLCWPQRSTVWY]
  C4F
  H1M
  W11N
  M1<->C10
Result: ACDEFGHIKLMNPQRSTVWY

Move probabilities:
  replace_residue: 1.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 0.00

Noise process: point_mutations
Input:  Reconstruct protein from: CDQVCFAMSRKALKYT
Output:
  V3S
  Y14E
  C4V
  Q2<->E14
  L12E
  [current state: CDESVFAMSRKAEKQT]
  S3<->E12
  D1<->K10
  S12W
  F5C
  R9K
  [current state: CKEEVCAMSKDAWKQT]
  D10D
  Q14V
  C5<->K13
  M7K
  C0G
  [current state: GKEEVKAKSKDAWCVT]
  E3T
  V14K
  D10S
  A6T
  E2<->S8
  [current state: GKSTVKTKEKSAWCKT]
  G0D
  D0P
  K5G
  C13R
  S2<->A11
  [current state: PKATVGTKEKSSWRKT]
  K9<->W12
  P0M
  W9S
  T6K
  G5K
  [current state: MKATVKKKESSSKRKT]
Result: MKATVKKKESSSKRKT

Move probabilities:
  replace_residue: 1.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 0.00

Noise process: point_mutations
Input:  Reconstruct protein from: ESGKVRQKATKSKCCT
Output:
  [CC]13[RH]
  V4E
  A8E
  Q6R
  E4Q
  [current state: ESGKQRRKETKSKRHT]
  E0<->Q4
  R5P
  S1A
  R6H
  K10<->S11
  [current state: QAGKEPHKETSKKRHT]
  T9H
  Q0F
  H9S
  K3T
  E8M
  [current state: FAGTEPHKMSSKKRHT]
  A1K
  G2E
  P5K
  K12K
  E4V
  [current state: FKETVKHKMSSKKRHT]
  H14G
  F0W
  G14K
  H6S
  S6<->K11
  [current state: WKETVKKKMSSSKRKT]
  E2A
  W0M
  M8E
Result: MKATVKKKESSSKRKT

=== Motif Dropout (motif remove) ===

Move probabilities:
  replace_residue: 0.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 1.00

Noise process: motif_dropout
Input:  Reconstruct protein from: 
Output:
  0L
  0I
  0N
  0L
  0M
  [current state: MLNIL]
  1[KF]
  7C
  8[FPL]
  4[VLF]
  14A
  [current state: MKFLVLFNILCFPLA]
  11L
  14V
  6L
Result: MKFLVLLFNILCLFPVLA

Move probabilities:
  replace_residue: 0.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 1.00

Noise process: motif_dropout
Input:  Reconstruct protein from: 
Output:
  0[CA]
  0I
  0L
  3V
  1L
  [current state: LLICVA]
  0V
  6L
  3F
  5L
  0M
  [current state: MVLLFILCVLA]
  1K
  9[LP]
  6N
  11F
  2L
  [current state: MKLVLLFNILCLFPVLA]
  2F
Result: MKFLVLLFNILCLFPVLA

Move probabilities:
  replace_residue: 0.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 1.00

Noise process: motif_dropout
Input:  Reconstruct protein from: 
Output:
  0D
  1[NR]
  1F
  3Q
  0A
  [current state: ADFNQR]
  4P
  3H
  1C
  9T
  5M
  [current state: ACDFHMNPQRT]
  5[IKL]
  4G
  15V
  16[WY]
  3E
  [current state: ACDEFGHIKLMNPQRTVWY]
  15S
Result: ACDEFGHIKLMNPQRSTVWY

Move probabilities:
  replace_residue: 0.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 1.00

Noise process: motif_dropout
Input:  Reconstruct protein from: 
Output:
  0W
  0Q
  1R
  3Y
  0[DFN]
  [current state: DFNQRWY]
  3P
  6T
  2H
  3L
  1E
  [current state: DEFHLNPQRTWY]
  10V
  3G
  10S
  5[IK]
  8M
  [current state: DEFGHIKLMNPQRSTVWY]
  0C
  0A
Result: ACDEFGHIKLMNPQRSTVWY

Move probabilities:
  replace_residue: 0.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 1.00

Noise process: motif_dropout
Input:  Reconstruct protein from: 
Output:
  0S
  1K
  0V
  1[KS]
  5[RK]
  [current state: VKSSKRK]
  7T
  0M
  2K
  4E
  5S
  [current state: MVKKESSSKRKT]
  1[KA]
  3T
  5K
Result: MKATVKKKESSSKRKT

Move probabilities:
  replace_residue: 0.00
  insert_residue: 0.00
  remove_residue: 0.00
  insert_motif: 0.00
  remove_motif: 1.00

Noise process: motif_dropout
Input:  Reconstruct protein from: 
Output:
  0M
  1T
  2R
  2[ESS]
  5S
  [current state: MTESSSR]
  7T
  6K
  2[VKK]
  1K
  2A
  [current state: MKATVKKESSSKRT]
  13K
  6K
Result: MKATVKKKESSSKRKT

=== Balanced (add/remove/replace, motifs allowed) ===

Move probabilities:
  replace_residue: 0.32
  insert_residue: 0.24
  remove_residue: 0.24
  insert_motif: 0.06
  remove_motif: 0.06

Noise process: fixed_steps
Input:  Reconstruct protein from: DEALIGGNTPTTNPILA
Output:
  T8F
  -9P
  -6G
  L3I
  -10N
  [current state: DEAIIGNFTTPILA]
  -4I
  0D
  10H
  H10F
  -[DD]0
  [current state: EAIGNFTTFPILA]
  T7<->I10
  G3V
  6F
  -0E
  0M
  [current state: MAIVNFFTIFPTLA]
  7I
  T12V
  A1N
  T8L
  I2F
  [current state: MNFVNFFILIFPVLA]
  6K
  I10C
  N4L
  11L
  3L
  [current state: MNFLVLFKFILCLFPVLA]
  -8F
  5L
  N1<->K8
Result: MKFLVLLFNILCLFPVLA

Move probabilities:
  replace_residue: 0.32
  insert_residue: 0.24
  remove_residue: 0.24
  insert_motif: 0.06
  remove_motif: 0.06

Noise process: fixed_steps
Input:  Reconstruct protein from: DGDHGAVGLA
Output:
  -7G
  A5F
  4S
  0E
  6D
  [current state: EDGDHSDGFVLA]
  2R
  2[MK]
  L13L
  4Y
  12Y
  [current state: EDMKYRGDHSDGYFVLA]
  E0<->D7
  -[DD]0
  -3R
  8[LD]
  -5H
  [current state: MKYGESDLDGYFVLA]
  S5L
  8[FNV]
  Y13E
  3F
  D7<->V11
  [current state: MKYFGELVLFNDDGEFVLA]
  13G
  -4G
  -2Y
  [GG]11[IE]
  -3E
  [current state: MKFLVLFNDDIEEFVLA]
  14P
  -[DD]8
  [EE]9[CL]
  5L
  10L
  [current state: MKFLVLLFNILCLFPVLA]
Result: MKFLVLLFNILCLFPVLA

Move probabilities:
  replace_residue: 0.32
  insert_residue: 0.24
  remove_residue: 0.24
  insert_motif: 0.06
  remove_motif: 0.06

Noise process: fixed_steps
Input:  Reconstruct protein from: CDLSIEPPRRFMMCEIKMLYSESQYPKSTWQW
Output:
  Q23<->K26
  E21<->K23
  -21K
  -17M
  I4E
  [current state: CDLSEEPPRRFMMCEIKLYSSEYPQSTWQW]
  -2L
  -27Q
  -20E
  C12N
  -[PP]5
  [current state: CDSEERRFMMNEIKLYSSYPQSTWW]
  -[RR]5
  -3E
  Y12<->W21
  8E
  -[SY]15
  [current state: CDSEFMMNEEIKLWSPQSTWY]
  -[EE]8
  S12V
  18W
  N7<->W17
  W11M
  [current state: CDSEFMMWIKLMVPQSTNWY]
  M5G
  3Y
  -8W
  0A
  V13<->N18
  [current state: ACDSYEFGMIKLMNPQSTVWY]
  M8H
  -[SY]3
  14R
Result: ACDEFGHIKLMNPQRSTVWY

Move probabilities:
  replace_residue: 0.32
  insert_residue: 0.24
  remove_residue: 0.24
  insert_motif: 0.06
  remove_motif: 0.06

Noise process: fixed_steps
Input:  Reconstruct protein from: DFHIKAALFRERSFNHPRGSYGQVWYG
Output:
  16P
  11W
  -28G
  -[PP]17
  -11W
  [current state: DFHIKAALFRERSFNHRGSYGQVWY]
  13D
  8F
  -23Q
  H17<->G22
  G17K
  [current state: DFHIKAALFFRERSDFNKRGSYHVWY]
  E11Q
  -[SY]20
  -16N
  -5A
  -[KR]15
  [current state: DFHIKALFFRQRSDFGHVWY]
  -13D
  1[PG]
  -7A
  8[NP]
  -[FF]10
  [current state: DPGFHIKLNPRQRSFGHVWY]
  0[AC]
  4F
  -13R
  G17W
  11M
  [current state: ACDPFGFHIKLMNPQRSFWHVWY]
  H19W
  -6F
  -[WW]17
  P3E
  F16T
  [current state: ACDEFGHIKLMNPQRSTVWY]
Result: ACDEFGHIKLMNPQRSTVWY

Move probabilities:
  replace_residue: 0.32
  insert_residue: 0.24
  remove_residue: 0.24
  insert_motif: 0.06
  remove_motif: 0.06

Noise process: fixed_steps
Input:  Reconstruct protein from: KCQYKERRT
Output:
  -7R
  -2Q
  -1C
  K0<->Y1
  0N
  [current state: NYKKERT]
  E4R
  6K
  3K
  K3H
  0C
  [current state: CNYKHKRRKT]
  6[EK]
  -[RR]8
  8[RL]
  Y2V
  N1M
  [current state: CMVKHKEKRLKT]
  2T
  7[KE]
  C0Q
  2C
  3A
  [current state: QMCATVKHKKEEKRLKT]
  11S
  -15L
  11S
  4R
  12S
  [current state: QMCARTVKHKKESSSEKRKT]
  C2K
  -8H
  -14E
  -4R
  -0Q
  [current state: MKATVKKKESSSKRKT]
Result: MKATVKKKESSSKRKT

Move probabilities:
  replace_residue: 0.32
  insert_residue: 0.24
  remove_residue: 0.24
  insert_motif: 0.06
  remove_motif: 0.06

Noise process: fixed_steps
Input:  Reconstruct protein from: WAMGDTRNSSVTF
Output:
  8P
  1P
  T6V
  F14K
  D5G
  [current state: WPAMGGVRNPSSVTK]
  8K
  -9N
  -6V
  -12T
  -5G
  [current state: WPAMGRKPSSVK]
  A2<->M3
  W0D
  7H
  -4G
  5I
  [current state: DPMARIKHPSSVK]
  4T
  H8E
  D0P
  3K
  I7K
  [current state: PPMKATRKKEPSSVK]
  -10P
  -[PP]0
  10Q
  Q10K
  13T
  [current state: MKATRKKESSKVKT]
  4T
  R5<->V12
  11S
  6K
  -3T
  [current state: MKATVKKKESSSKRKT]
Result: MKATVKKKESSSKRKT

=== Motif-biased (motif insert/remove favored) ===

Move probabilities:
  replace_residue: 0.16
  insert_residue: 0.12
  remove_residue: 0.12
  insert_motif: 0.30
  remove_motif: 0.30

Noise process: fixed_steps
Input:  Reconstruct protein from: FMLFGTSGGLYTYKFHS
Output:
  5F
  G8D
  -7S
  -4G
  [TY]10[AC]
  [current state: FMLFFTDGLYACKFHS]
  -7G
  D6E
  9I
  13M
  1E
  [current state: FEMLFFTELYIACKMFHS]
  -7E
  Y8V
  L3K
  V8<->F14
  12L
  [current state: FEMKFFTLFIACLKMVHS]
  14V
  -1E
  V13G
  18A
  A9L
  [current state: FMKFFTLFILCLKGMVHSA]
  -16H
  T5F
  M14P
  -0F
  -12G
  [current state: MKFFFLFILCLKPVSA]
  K11F
  7N
  -[FF]2
  S13L
  4[VLL]
  [current state: MKFLVLLFNILCLFPVLA]
Result: MKFLVLLFNILCLFPVLA

Move probabilities:
  replace_residue: 0.16
  insert_residue: 0.12
  remove_residue: 0.12
  insert_motif: 0.30
  remove_motif: 0.30

Noise process: fixed_steps
Input:  Reconstruct protein from: RMLVEFCPVCFNEVLYPSTP
Output:
  V8<->P19
  E12R
  E4L
  P8<->V19
  5[AA]
  [current state: RMLVLAAFCPVCFNRVLYPSTP]
  -[ST]19
  -5A
  N12E
  [PP]17[LD]
  -16Y
  [current state: RMLVLAFCPVCFERVLLD]
  A5<->L16
  14P
  C7F
  -18D
  -13R
  [current state: RMLVLLFFPVCFEPVLA]
  -1M
  7I
  1R
  V10L
  -9P
  [current state: RRLVLLFFILCFEPVLA]
  2F
  12L
  -14E
  15F
  0T
  [current state: TRRFLVLLFFILCLFPFVLA]
  -0T
  -15F
  [RR]0[MK]
  F8N
Result: MKFLVLLFNILCLFPVLA

Move probabilities:
  replace_residue: 0.16
  insert_residue: 0.12
  remove_residue: 0.12
  insert_motif: 0.30
  remove_motif: 0.30

Noise process: fixed_steps
Input:  Reconstruct protein from: AGMFDHQSALFY
Output:
  H5M
  11[NW]
  -1G
  1H
  9D
  [current state: AHMFDMQSADLFNWY]
  M2G
  D9<->L10
  -10D
  L9V
  A8E
  [current state: AHGFDMQSEVFNWY]
  G2<->F3
  6P
  4[CDN]
  7K
  2[LE]
  [current state: AHLEFGCDNKDMPQSEVFNWY]
  9F
  E16A
  H1<->C6
  15R
  -9F
  [current state: ACLEFGHDNKDMPQRSAVFNWY]
  N8I
  N19V
  -7D
  A15<->V18
  -18A
  [current state: ACLEFGHIKDMPQRSVVFWY]
  11N
  V16T
  -18F
  L2<->D9
Result: ACDEFGHIKLMNPQRSTVWY

Move probabilities:
  replace_residue: 0.16
  insert_residue: 0.12
  remove_residue: 0.12
  insert_motif: 0.30
  remove_motif: 0.30

Noise process: fixed_steps
Input:  Reconstruct protein from: AFPDCTPYEHIQKHRCCNDVCRWNYVD
Output:
  -23N
  -24V
  13Q
  Q13P
  13L
  [current state: AFPDCTPYEHIQKLPHRCCNDVCRWYD]
  -11Q
  14W
  [CC]17[DD]
  T5<->D20
  -2P
  [current state: AFDCDPYEHIKLPWHRDDNTVCRWYD]
  D2F
  R22C
  -[FF]1
  -11W
  -11H
  [current state: ACDPYEHIKLPRDDNTVCCWYD]
  -[CC]17
  -14N
  D18Q
  -18Q
  11T
  [current state: ACDPYEHIKLPTRDDTVWY]
  6G
  -[DD]14
  -3P
  13S
  -3Y
  [current state: ACDEGHIKLPTRSTVWY]
  4N
  10[MN]
  T13Q
  N4F
Result: ACDEFGHIKLMNPQRSTVWY

Move probabilities:
  replace_residue: 0.16
  insert_residue: 0.12
  remove_residue: 0.12
  insert_motif: 0.30
  remove_motif: 0.30

Noise process: fixed_steps
Input:  Reconstruct protein from: KSHKEEPDENSSFTFH
Output:
  D7T
  E5M
  6L
  F13<->T14
  F14R
  [current state: KSHKEMLPTENSSTRFH]
  3Y
  L7E
  -16F
  4V
  E6<->N12
  [current state: KSHYVKNMEPTEESSTRH]
  S1<->T10
  6K
  -[EE]12
  15[GK]
  14G
  [current state: KTHYVKKNMEPSSSGTGKRH]
  M8K
  K17G
  -16G
  -10P
  -15G
  [current state: KTHYVKKNKESSSGTRH]
  -7N
  K0<->T13
  16A
  -2H
  -2Y
  [current state: TTVKKKESSSGKRHA]
  S7<->S9
  H13K
  -10G
  T0<->A13
  0[MK]
  [current state: MKATVKKKESSSKRKT]
Result: MKATVKKKESSSKRKT

Move probabilities:
  replace_residue: 0.16
  insert_residue: 0.12
  remove_residue: 0.12
  insert_motif: 0.30
  remove_motif: 0.30

Noise process: fixed_steps
Input:  Reconstruct protein from: KAKGVKKSMWSVVDCKHESKMGVTTH
Output:
  -1A
  -21V
  1[HRM]
  2Y
  -19H
  [current state: KHYRMKGVKKSMWSVVDCKESKMGTTH]
  -17C
  Y2<->M11
  V15K
  -1H
  5G
  [current state: KMRMKGGVKKSYWSVKDKESKMGTTH]
  -25H
  K0<->G22
  -24T
  20T
  M3R
  [current state: GMRRKGGVKKSYWSVKDKESTKMKT]
  S13<->T20
  -[RR]2
  9M
  M21R
  -[KK]6
  [current state: GMKGGVSMYWTVKDKESSKRKT]
  -7M
  -0G
  -[GG]2
  -5W
  -[SY]3
  [current state: MKVTVKDKESSKRKT]
  3A
  -2V
  9S
  T15T
  D6K
  [current state: MKATVKKKESSSKRKT]
Result: MKATVKKKESSSKRKT

