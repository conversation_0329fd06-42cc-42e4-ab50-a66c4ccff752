/Users/<USER>/Projects/Diffusion_via_Reasoning/demos/../src/diffusion_via_reasoning/utils/graph_utils.py:39: UserWarning: This figure includes Axes that are not compatible with tight_layout, so results might be incorrect.
  plt.tight_layout()
=== Graph Training Data Demo ===

Loaded sample graphs:
   1-2 2-3 3-4 4-5 5-6 6-7 7-8 8-1 2-5 3-6 4-7 1-8 5-8 6-1 7-2 8-3
   1-2 2-3 3-4 4-5 5-6 6-1 1-3 2-4 3-5 4-6 5-1 6-2 1-4 2-5 3-6 4-1 5-2 6-3

=== Default (balanced) ===

[DEBUG] Forward initial state: 1-2 2-3 3-4 4-5 5-6 6-7 7-8 8-1 2-5 3-6 4-7 1-8 5-8 6-1 7-2 8-3
[DEBUG] Forward final state: 
[DEBUG] Backward initial state: 
[DEBUG] Backward final state: 1-2 2-3 3-4 4-5 5-6 6-7 7-8 8-1 2-5 3-6 4-7 1-8 5-8 6-1 7-2 8-3
Move probabilities:
  add_edge: 0.05
  remove_edge: 0.40

Input:  Reconstruct graph from: 
Trajectory length: 21
States length: 0
[Warning] Initial state is empty. Skipping trajectory.
=== Edge-focused ===

[DEBUG] Forward initial state: 1-2 2-3 3-4 4-5 5-6 6-7 7-8 8-1 2-5 3-6 4-7 1-8 5-8 6-1 7-2 8-3
[DEBUG] Forward final state: 3-4
[DEBUG] Backward initial state: 3-4
[DEBUG] Backward final state: 1-2 2-3 3-4 4-5 5-6 6-7 7-8 8-1 2-5 3-6 4-7 1-8 5-8 6-1 7-2 8-3
Move probabilities:
  add_edge: 0.05
  remove_edge: 0.40

Input:  Reconstruct graph from: 3-4
Trajectory length: 30
States length: 0
Output:
Result: 1-2 2-3 3-4 4-5 5-6 6-7 7-8 8-1 2-5 3-6 4-7 1-8 5-8 6-1 7-2 8-3

Graph visualization saved to /Users/<USER>/Projects/Diffusion_via_Reasoning/demos/graph_demo_edge-focused.png

=== Motif-heavy ===

[DEBUG] Forward initial state: 1-2 2-3 3-4 4-5 5-6 6-7 7-8 8-1 2-5 3-6 4-7 1-8 5-8 6-1 7-2 8-3
[DEBUG] Forward final state: 3-4 3-8 4-5 5-8
[DEBUG] Backward initial state: 3-4 3-8 4-5 5-8
[DEBUG] Backward final state: 1-2 2-3 3-4 4-5 5-6 6-7 7-8 8-1 2-5 3-6 4-7 1-8 5-8 6-1 7-2 8-3
Move probabilities:
  add_edge: 0.05
  remove_edge: 0.40

Input:  Reconstruct graph from: 3-4 3-8 4-5 5-8
Trajectory length: 23
States length: 0
Output:
Result: 1-2 2-3 3-4 4-5 5-6 6-7 7-8 8-1 2-5 3-6 4-7 1-8 5-8 6-1 7-2 8-3

Graph visualization saved to /Users/<USER>/Projects/Diffusion_via_Reasoning/demos/graph_demo_motif-heavy.png

