#!/usr/bin/env python3
"""
Protein Training Data Format Demo (Compact Notation, Multiple Noise Processes)

This script generates and prints several protein backward trajectory samples
in a compact, model-friendly notation for language model training.
It covers multiple noise process types and parameter settings, and includes
both residue and motif moves (insert, remove, replace, swap where applicable).
Output is suitable for saving to a file.

Legend (0-based indexing):
  A6B      = Replace A at 6 with B
  7C       = Insert C at 7
  -5F      = Remove F at 5
  A3/B7    = Swap A at 3 with B at 7
  7[KR]    = Insert motif KR at 7
  -[KR]5   = Remove motif KR at 5
  [KR]5[EE]= Replace motif KR at 5 with EE
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from diffusion_via_reasoning.trajectories.base import TrajectoryConfig, NoiseProcessType
from diffusion_via_reasoning.corruption.protein_actions import ProteinActionCorruption
from diffusion_via_reasoning.trajectories.protein import AMINO_ACIDS, ProteinTrajectoryGenerator

# Parameters for demo
SHOW_STATE_EVERY_K = 5
MAX_STEPS = 30

# Helper to format actions in compact notation, using the current state
def format_action_compact(action, state):
    t = action.action_type
    d = action.to_dict()
    if t == "add_residue":
        return f"{d['position']}{d['amino_acid']}"
    elif t == "remove_residue":
        pos = d['position']
        aa = state[pos] if 0 <= pos < len(state) else '?'
        return f"-{pos}{aa}"
    elif t == "replace_residue":
        pos = d['position']
        old = state[pos] if 0 <= pos < len(state) else '?'
        new = d.get('new_amino_acid', '?')
        return f"{old}{pos}{new}"
    elif t == "swap_residues":
        p1, p2 = d['position1'], d['position2']
        aa1 = state[p1] if 0 <= p1 < len(state) else '?'
        aa2 = state[p2] if 0 <= p2 < len(state) else '?'
        return f"{aa1}{p1}<->{aa2}{p2}"
    elif t == "insert_motif":
        return f"{d['position']}[{d['motif']}]"
    elif t == "remove_motif":
        pos = d['position']
        motif = state[pos:pos+d['length']] if 0 <= pos < len(state) else '?'
        return f"-[{motif}]{pos}"
    elif t == "replace_motif":
        pos = d['position']
        old = d['old_motif']
        new = d['new_motif']
        return f"[{old}]{pos}[{new}]"
    # Large-structure moves
    elif t == "insert_helix":
        return f"H{d['position']}[{d['helix_seq']}]"
    elif t == "remove_helix":
        pos = d['position']
        return f"-H{pos}"
    elif t == "replace_helix":
        pos = d['position']
        old = d['old_seq']
        new = d['new_seq']
        return f"[{old}]H{pos}[{new}]"
    elif t == "insert_strand":
        return f"E{d['position']}[{d['strand_seq']}]"
    elif t == "remove_strand":
        pos = d['position']
        return f"-E{pos}"
    elif t == "replace_strand":
        pos = d['position']
        old = d['old_seq']
        new = d['new_seq']
        return f"[{old}]E{pos}[{new}]"
    elif t == "insert_domain":
        return f"D{d['position']}[{d['domain_seq']}]"
    elif t == "remove_domain":
        pos = d['position']
        return f"-D{pos}"
    elif t == "replace_domain":
        pos = d['position']
        old = d['old_seq']
        new = d['new_seq']
        return f"[{old}]D{pos}[{new}]"
    elif t == "graft_segment":
        return f"G{d['position']}[{d['graft_seq']}]"
    elif t == "fuse_proteins":
        return f"FUSION"
    else:
        return f"?{t}?"

# Representative noise process settings with different probabilities
NOISE_SETTINGS = [
    (
        NoiseProcessType.FIXED_STEPS,
        "Default (biologically realistic)",
        dict(add_probability=0.1, remove_probability=0.3, replace_probability=0.5, swap_probability=0.0, motif_add_probability=0.05, motif_remove_probability=0.05, motif_swap_probability=0.0),
        {"replace_residue": 0.5, "insert_residue": 0.1, "remove_residue": 0.3, "insert_motif": 0.05, "remove_motif": 0.05, "swap_residues": 0.0, "swap_motifs": 0.0}
    ),
    # (noise_type, description, config_kwargs, (residue_probs, motif_probs))
    (NoiseProcessType.POINT_MUTATIONS, "Point Mutations (residue replace)",
        dict(add_probability=0.0, remove_probability=0.0, replace_probability=1.0, swap_probability=0.0, motif_add_probability=0.0, motif_remove_probability=0.0, motif_swap_probability=0.0),
        {"replace_residue": 1.0, "insert_residue": 0.0, "remove_residue": 0.0, "insert_motif": 0.0, "remove_motif": 0.0, "swap_residues": 0.0, "swap_motifs": 0.0}),
    (NoiseProcessType.MOTIF_DROPOUT, "Motif Dropout (motif remove)",
        dict(add_probability=0.0, remove_probability=1.0, replace_probability=0.0, swap_probability=0.0, motif_add_probability=0.0, motif_remove_probability=1.0, motif_swap_probability=0.0),
        {"replace_residue": 0.0, "insert_residue": 0.0, "remove_residue": 0.0, "insert_motif": 0.0, "remove_motif": 1.0, "swap_residues": 0.0, "swap_motifs": 0.0}),
    (NoiseProcessType.FIXED_STEPS, "Balanced (add/remove/replace, motifs allowed)",
        dict(add_probability=0.3, remove_probability=0.3, replace_probability=0.4, swap_probability=0.0, motif_add_probability=0.06, motif_remove_probability=0.06, motif_swap_probability=0.0),
        {"replace_residue": 0.32, "insert_residue": 0.24, "remove_residue": 0.24, "insert_motif": 0.06, "remove_motif": 0.06, "swap_residues": 0.0, "swap_motifs": 0.0}),
    (NoiseProcessType.FIXED_STEPS, "Motif-biased (motif insert/remove favored)",
        dict(add_probability=0.3, remove_probability=0.3, replace_probability=0.4, swap_probability=0.0, motif_add_probability=0.3, motif_remove_probability=0.3, motif_swap_probability=0.0),
        {"replace_residue": 0.16, "insert_residue": 0.12, "remove_residue": 0.12, "insert_motif": 0.3, "remove_motif": 0.3, "swap_residues": 0.0, "swap_motifs": 0.0}),
]

SEQUENCES = [
    "MKFLVLLFNILCLFPVLA",
    "ACDEFGHIKLMNPQRSTVWY",
    "MKATVKKKESSSKRKT",
]

NUM_TRAJ_PER_SEQ = 2


def print_move_probabilities(move_probs):
    print("Move probabilities:")
    for move, prob in move_probs.items():
        if prob > 0:
            print(f"  {move}: {prob:.2f}")
    print()

def main():
    # Print residue and motif vocabularies
    print("Residue vocabulary (20 standard amino acids):")
    print("  ", " ".join(AMINO_ACIDS))
    # Use the same motifs as ProteinTrajectoryGenerator
    motif_vocabulary = [
        'KR', 'EE', 'DD', 'RR', 'KK',  # Charged motifs
        'PP', 'GG', 'AA',              # Structural motifs
        'CC', 'WW', 'FF',              # Hydrophobic motifs
        'ST', 'TY', 'SY'               # Polar motifs
    ]
    print("Motif vocabulary (common motifs):")
    print("  ", " ".join(motif_vocabulary))
    print()
    print("Legend (0-based indexing):")
    print("  A6B      = Replace A at 6 with B")
    print("  7C       = Insert C at 7")
    print("  -5F      = Remove F at 5")
    print("  A3/B7    = Swap A at 3 with B at 7")
    print("  7[KR]    = Insert motif KR at 7")
    print("  -[KR]5   = Remove motif KR at 5")
    print("  [KR]5[EE]= Replace motif KR at 5 with EE")
    print("  [KR]5[EE]= Replace motif KR at 5 with EE (motif replace)\n")
    noise_settings_len = len(NOISE_SETTINGS)
    traj_counter = 0
    for noise_type, description, config_kwargs, move_probs in NOISE_SETTINGS:
        print(f"=== {description} ===\n")
        for seq in SEQUENCES:
            corruption = ProteinActionCorruption()
            config = TrajectoryConfig(
                noise_process_type=noise_type,
                max_steps=MAX_STEPS,
                add_probability=config_kwargs.get('add_probability', 0.3),
                remove_probability=config_kwargs.get('remove_probability', 0.4),
                replace_probability=config_kwargs.get('replace_probability', 0.3),
                swap_probability=config_kwargs.get('swap_probability', 0.0),
                motif_add_probability=config_kwargs.get('motif_add_probability', 0.0),
                motif_remove_probability=config_kwargs.get('motif_remove_probability', 0.0),
                motif_swap_probability=config_kwargs.get('motif_swap_probability', 0.0),
                enforce_validity=True,
            )
            forward, _ = corruption.generate_training_data(
                [seq], num_trajectories_per_sequence=NUM_TRAJ_PER_SEQ, config=config
            )
            for traj in forward:
                print_move_probabilities(move_probs)
                backward = traj.reverse()
                print(f"Input:  Reconstruct protein from: {backward.initial_state}")
                print("Output:")
                state = backward.initial_state
                for i, action in enumerate(backward.actions):
                    print(f"  {format_action_compact(action, state)}")
                    result = action.apply(state)
                    if getattr(result, 'success', False) and hasattr(result, 'new_state') and result.new_state is not None:
                        state = result.new_state
                    elif not getattr(result, 'success', False):
                        print(f"    [Warning: action failed to apply: {getattr(result, 'error_message', '')}")
                    if (i + 1) % SHOW_STATE_EVERY_K == 0:
                        print(f"  [current state: {state}]")
                print(f"Result: {backward.final_state}\n")
                traj_counter += 1

if __name__ == "__main__":
    main() 