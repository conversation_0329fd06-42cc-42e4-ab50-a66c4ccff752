#!/usr/bin/env python3
"""
Demo for graph training data generation.

This demo covers multiple process variants:
- Fixed steps, random walk (disconnected allowed)
- Absorbing state: empty graph
- Absorbing state: 2-node graph
- Absorbing state: 4-node cycle (connected only)
- Motif-heavy, fixed steps
- Rewiring only (fixed steps)

Each variant is documented in the config list.
"""

import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from diffusion_via_reasoning.corruption.graph import GraphCorruption
from diffusion_via_reasoning.trajectories.base import TrajectoryConfig
from diffusion_via_reasoning.data.graph_data import GraphDataLoader
from diffusion_via_reasoning.utils.graph_utils import draw_graph, graph_from_string, graph_to_string, find_motif_isomorphisms
from diffusion_via_reasoning.actions.graph import _motif_graph

SHOW_STATE_EVERY_K = 3

def format_graph_action_compact(action, state):
    t = action.action_type
    d = action.to_dict()
    if t == "add_edge":
        return f"+E({d['node_u']},{d['node_v']})"
    elif t == "remove_edge":
        return f"-E({d['node_u']},{d['node_v']})"
    elif t == "rewire_edge":
        return f"RW({d['old_u']},{d['old_v']},{d['new_u']},{d['new_v']})"
    elif t == "add_motif":
        nodes = d.get('added_nodes')
        if nodes:
            nodestr = ",".join(str(n) for n in nodes)
            return f"+M[{d['motif']}] at nodes {nodestr}"
        else:
            return f"+M[{d['motif']}]"
    elif t == "remove_motif":
        nodes = d.get('removed_nodes')
        if nodes:
            nodestr = ",".join(str(n) for n in nodes)
            return f"-M[{d['motif']}] at nodes {nodestr}"
        else:
            return f"-M[{d['motif']}]"
    elif t == "replace_motif":
        nodes = d.get('sub_nodes', [])
        nodestr = ",".join(str(n) for n in nodes) if nodes else "?"
        return f"R[{d['old']}:{nodestr}]->[{d['new']}]"
    else:
        return f"?{t}?"

def print_graph_move_probabilities(config):
    print("Move probabilities:")
    moves = [
        ("add_edge", getattr(config, 'add_edge_probability', 0)),
        ("remove_edge", getattr(config, 'remove_edge_probability', 0)),
        ("rewire_edge", getattr(config, 'rewire_edge_probability', 0)),
        ("add_motif", getattr(config, 'motif_add_probability', 0)),
        ("remove_motif", getattr(config, 'motif_remove_probability', 0)),
        ("replace_motif", getattr(config, 'motif_replace_probability', 0)),
    ]
    for move, prob in moves:
        if prob > 0:
            print(f"  {move}: {prob:.2f}")
    print()

def reconstruct_intermediate_states(initial_state, actions):
    """Reconstruct intermediate states by applying actions step by step.
    For remove_motif actions, ensure removed_nodes is set if possible.
    """
    states = [initial_state]
    state = initial_state
    for action in actions:
        # For remove_motif, ensure removed_nodes is set
        if action.action_type == "remove_motif":
            d = action.to_dict()
            if not d.get("removed_nodes"):
                G = graph_from_string(state)
                motif_graph = _motif_graph(d["motif"])
                if motif_graph is not None:
                    isomorphisms = find_motif_isomorphisms(G, motif_graph)
                    if isomorphisms:
                        # Use the first isomorphism mapping
                        nodes = list(isomorphisms[0].values())
                        action.removed_nodes = nodes
        result = action.apply(state)
        if getattr(result, 'success', False) and hasattr(result, 'new_state') and result.new_state is not None:
            state = result.new_state
        states.append(state)
    return states

# Define several move probability setups for demonstration
GRAPH_NOISE_SETTINGS = [
    ("Random Walk (fixed steps, disconnected allowed)", TrajectoryConfig(
        noise_process_type=TrajectoryConfig.noise_process_type,
        max_steps=30,
        add_edge_probability=0.2,
        remove_edge_probability=0.2,
        motif_add_probability=0.1,
        motif_remove_probability=0.1,
        motif_replace_probability=0.1,
        absorbing_state_type=None,
        allow_disconnected_components=True,
    )),
    ("Absorbing State: Empty Graph", TrajectoryConfig(
        noise_process_type=TrajectoryConfig.noise_process_type,
        max_steps=30,
        add_edge_probability=0.05,
        remove_edge_probability=0.4,
        motif_add_probability=0.0,
        motif_remove_probability=0.0,
        motif_replace_probability=0.0,
        absorbing_state_type='empty',
        allow_disconnected_components=True,
    )),
    ("Absorbing State: 2-Node Graph", TrajectoryConfig(
        noise_process_type=TrajectoryConfig.noise_process_type,
        max_steps=30,
        add_edge_probability=0.05,
        remove_edge_probability=0.4,
        motif_add_probability=0.0,
        motif_remove_probability=0.0,
        motif_replace_probability=0.0,
        absorbing_state_type='2node',
        allow_disconnected_components=True,
    )),
    ("Absorbing State: 4-Node Cycle (connected only)", TrajectoryConfig(
        noise_process_type=TrajectoryConfig.noise_process_type,
        max_steps=30,
        add_edge_probability=0.05,
        remove_edge_probability=0.4,
        motif_add_probability=0.0,
        motif_remove_probability=0.0,
        motif_replace_probability=0.0,
        absorbing_state_type='4cycle',
        allow_disconnected_components=False,
    )),
    ("Motif-Heavy (fixed steps, disconnected allowed)", TrajectoryConfig(
        noise_process_type=TrajectoryConfig.noise_process_type,
        max_steps=30,
        add_edge_probability=0.05,
        remove_edge_probability=0.05,
        motif_add_probability=0.3,
        motif_remove_probability=0.3,
        motif_replace_probability=0.3,
        absorbing_state_type=None,
        allow_disconnected_components=True,
    )),
]
# Add a rewiring-only config
GRAPH_NOISE_SETTINGS.append(
    ("Rewiring Only (fixed steps)", TrajectoryConfig(
        noise_process_type=TrajectoryConfig.noise_process_type,
        max_steps=30,
        add_edge_probability=0.0,
        remove_edge_probability=0.0,
        rewire_edge_probability=1.0,
        motif_add_probability=0.0,
        motif_remove_probability=0.0,
        motif_replace_probability=0.0,
        absorbing_state_type=None,
        allow_disconnected_components=True,
    ))
)

def main():
    print("=== Graph Training Data Demo ===\n")

    # Use only the new, smaller graph
    graphs = [
        "1-2 2-3 3-4 4-5 5-6 6-7 7-1"
    ]

    print("Loaded sample graphs:")
    for g in graphs:
        print("  ", g)
    print()

    corruption = GraphCorruption()
    for desc, config in GRAPH_NOISE_SETTINGS:
        print(f"=== {desc} ===\n")
        forward, backward = corruption.generate_training_data(graphs, 1, config)
        if backward:
            sample = backward[0]
            print_graph_move_probabilities(config)
            print("Input:  Reconstruct graph from:", sample.initial_state)
            print(f"Trajectory length: {len(sample.actions)}")
            states = sample.intermediate_states
            if not states or len(states) <= 1:
                states = reconstruct_intermediate_states(sample.initial_state, sample.actions)
            print("Output:")
            n = min(len(sample.actions), len(states) - 1)
            for i in range(n):
                act = sample.actions[i]
                print(f"  {format_graph_action_compact(act, states[i])}")
                if (i + 1) % SHOW_STATE_EVERY_K == 0:
                    print(f"  current state: {states[i+1]}")
            print(f"Result: {sample.final_state}\n")
            g = graph_from_string(sample.final_state)
            out_path = os.path.join(os.path.dirname(__file__), f"graph_demo_{desc.replace(' ','_').lower()}.png")
            draw_graph(g, out_path)
            print(f"Graph visualization saved to {out_path}\n")

if __name__ == "__main__":
    main()
