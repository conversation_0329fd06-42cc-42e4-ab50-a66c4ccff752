from diffusion_via_reasoning.zero_shot.prompting import ZeroShotPromptBuilder

builder = ZeroShotPromptBuilder()

molecule_prompt = builder.format_molecule_prompt(
    "Optimize solubility for CCO",
    tools=["tool_a: calculates properties", "tool_b: searches databases"]
)
print("Molecular prompt:\n", molecule_prompt, "\n")

protein_prompt = builder.format_protein_prompt(
    "Predict structure for ACDEFG",
    tools=["tool_a: calculates properties", "tool_b: searches databases"]
)
print("Protein prompt:\n", protein_prompt, "\n")

language_prompt = builder.format_nlp_prompt(
    "Continue the story: Once upon a time",
    tools=["tool_a: grammar checker", "tool_b: synonym suggester"]
)
print("Language prompt:\n", language_prompt)
