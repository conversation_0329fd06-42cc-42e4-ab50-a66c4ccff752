from diffusion_via_reasoning.trajectory import (
    TrajectoryGenerator,
    ReverseProteinTrajectory,
)

AMINO_ACIDS = list("ACDEFGHIKLMNPQRSTVWY")

gen = TrajectoryGenerator(
    valid_tokens=AMINO_ACIDS,
    action_probs={"add": 0.3, "remove": 0.3, "replace": 0.4},
    num_steps=5,
    seed=42,
)

trajectory = gen.generate("ACDE")
print("Forward trajectory:")
for line in trajectory.to_log():
    print(line)

reverse = ReverseProteinTrajectory(trajectory)
print("\nReverse trajectory:")
for line in reverse.to_log():
    print(line)
