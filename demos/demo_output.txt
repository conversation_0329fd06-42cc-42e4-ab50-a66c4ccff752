=== Graph Training Data Demo ===

Loaded sample graphs:
   1-2 2-3 3-4 4-5 5-6 6-7 7-1

=== Random Walk (fixed steps, disconnected allowed) ===

Move probabilities:
  add_edge: 0.20
  remove_edge: 0.20
  add_motif: 0.10
  remove_motif: 0.10
  replace_motif: 0.10

Input:  Reconstruct graph from: 1-2 1-5 1-14 2-6 2-9 3-7 4-5 5-6 5-7 5-11 5-12 5-14 4-6 6-7 6-13 7-10 3-9 8-9 9-13 8-10 8-11 8-12 12-13 12-15 8-13 12-14 13-14 15-16 15-17 15-18
Trajectory length: 29
Output:
  -E(12,15)
  +E(5,10)
  -E(9,13)
  current state: 1-2 1-5 1-14 2-6 2-9 3-7 4-5 5-6 5-7 5-10 5-11 5-12 5-14 4-6 6-7 6-13 7-10 3-9 8-9 8-10 8-11 8-12 12-13 8-13 12-14 13-14 15-16 15-17 15-18
  -E(1,5)
  -E(9,3)
  -M[star] at nodes 2,1,6,9
  current state: 3-7 4-5 5-10 5-11 5-12 5-7 7-10 8-10 8-11 8-12 12-13 8-13 5-14 12-14 13-14 15-16 15-17 15-18
  -E(14,5)
  -E(6,13)
  -E(1,14)
  current state: 3-7 4-5 5-10 5-11 5-12 5-7 7-10 8-13 8-10 8-11 8-12 12-13 12-14 13-14 15-16 15-17 15-18
  -E(8,13)
  +E(1,5)
  -E(5,10)
  current state: 3-7 4-5 1-5 5-11 5-12 5-7 7-10 8-10 8-11 8-12 12-13 12-14 13-14 15-16 15-17 15-18
  -E(5,12)
  +E(1,11)
  +E(2,3)
  current state: 1-5 1-11 2-3 3-7 4-5 5-7 7-10 8-12 8-10 5-11 8-11 12-13 12-14 13-14 15-16 15-17 15-18
  -E(8,12)
  +E(3,4)
  -M[triangle] at nodes 1,11,5
  current state: 2-3 3-4 3-7 7-10 8-10 12-13 12-14 13-14 15-16 15-17 15-18
  -E(2,6)
  -E(7,10)
  -E(5,7)
  current state: 2-3 3-4 3-7 8-10 12-13 12-14 13-14 15-16 15-17 15-18
  -E(4,6)
  -E(3,7)
  -E(5,11)
  current state: 2-3 3-4 8-10 12-13 12-14 13-14 15-16 15-17 15-18
  -E(1,11)
  -E(2,9)
  +E(1,7)
  current state: 1-7 2-3 3-4 8-10 12-13 12-14 13-14 15-16 15-17 15-18
  -E(1,5)
  -M[star] at nodes 15,16,17,18
Result: 1-2 2-3 3-4 4-5 5-6 6-7 7-1

Graph visualization saved to /Users/<USER>/Projects/Diffusion_via_Reasoning/demos/graph_demo_random_walk_(fixed_steps,_disconnected_allowed).png

=== Absorbing State: Empty Graph ===

Move probabilities:
  add_edge: 0.05
  remove_edge: 0.40

Input:  Reconstruct graph from: 
Trajectory length: 7
Output:
  +E(2,3)
  +E(5,6)
  +E(1,2)
  current state: 1-2 2-3 5-6
  +E(6,7)
  +E(3,4)
  +E(1,7)
  current state: 1-2 1-7 2-3 3-4 5-6 6-7
  +E(4,5)
Result: 1-2 2-3 3-4 4-5 5-6 6-7 7-1

Graph visualization saved to /Users/<USER>/Projects/Diffusion_via_Reasoning/demos/graph_demo_absorbing_state:_empty_graph.png

=== Absorbing State: 2-Node Graph ===

Move probabilities:
  add_edge: 0.05
  remove_edge: 0.40

Input:  Reconstruct graph from: 3-4
Trajectory length: 12
Output:
  +E(4,7)
  +E(5,6)
  +E(4,5)
  current state: 3-4 4-5 4-7 5-6
  +E(7,6)
  +E(4,6)
  +E(5,7)
  current state: 3-4 4-5 4-6 4-7 5-6 5-7 6-7
  -E(4,6)
  +E(1,7)
  -E(7,5)
  current state: 3-4 4-5 4-7 5-6 1-7 6-7
  +E(1,2)
  -E(7,4)
  +E(2,3)
  current state: 1-2 1-7 2-3 3-4 4-5 5-6 6-7
Result: 1-2 2-3 3-4 4-5 5-6 6-7 7-1

Graph visualization saved to /Users/<USER>/Projects/Diffusion_via_Reasoning/demos/graph_demo_absorbing_state:_2-node_graph.png

=== Absorbing State: 4-Node Cycle (connected only) ===

Move probabilities:
  add_edge: 0.05
  remove_edge: 0.40

Input:  Reconstruct graph from: 
Trajectory length: 13
Output:
  +E(1,2)
  +E(1,7)
  +E(5,6)
  current state: 1-2 1-7 5-6
  +E(2,3)
  +E(4,5)
  +E(7,5)
  current state: 1-2 1-7 2-3 4-5 5-6 5-7
  +E(2,4)
  +E(3,4)
  +E(7,6)
  current state: 1-2 1-7 2-3 2-4 3-4 4-5 5-6 5-7 6-7
  -E(7,5)
  -E(2,4)
  -E(4,5)
  current state: 1-2 1-7 2-3 3-4 5-6 6-7
  +E(4,5)
Result: 1-2 2-3 3-4 4-5 5-6 6-7 7-1

Graph visualization saved to /Users/<USER>/Projects/Diffusion_via_Reasoning/demos/graph_demo_absorbing_state:_4-node_cycle_(connected_only).png

=== Motif-Heavy (fixed steps, disconnected allowed) ===

Move probabilities:
  add_edge: 0.05
  remove_edge: 0.05
  add_motif: 0.30
  remove_motif: 0.30
  replace_motif: 0.30

Input:  Reconstruct graph from: 1-2 1-6 1-7 1-9 2-3 3-4 3-10 3-14 4-5 4-13 5-12 5-6 6-10 2-7 7-8 7-9 8-11 4-9 5-9 8-9 9-10 9-15 10-11 10-14 12-18 8-13 11-13 12-13 12-14 16-17 16-18 17-18
Trajectory length: 24
Output:
  -E(1,2)
  -E(12,18)
  -E(3,14)
  current state: 1-6 1-7 1-9 2-3 3-4 3-10 4-5 4-13 5-12 5-6 6-10 2-7 7-8 7-9 8-11 4-9 5-9 8-9 9-10 9-15 10-11 10-14 8-13 11-13 12-13 12-14 16-17 16-18 17-18
  -E(10,14)
  -E(7,2)
  -E(9,4)
  current state: 1-6 1-7 1-9 2-3 3-4 3-10 4-5 4-13 5-12 5-6 6-10 7-8 7-9 8-11 5-9 8-9 9-10 9-15 10-11 12-14 8-13 11-13 12-13 16-17 16-18 17-18
  +E(12,15)
  -M[triangle] at nodes 1,9,7
  -E(4,13)
  current state: 2-3 3-4 3-10 4-5 5-6 5-12 8-11 6-10 10-11 12-14 12-15 8-13 11-13 12-13 16-17 16-18 17-18
  -E(7,8)
  -E(5,12)
  +E(6,7)
  current state: 2-3 3-4 3-10 4-5 5-6 6-7 8-11 8-13 6-10 10-11 11-13 12-14 12-15 12-13 16-17 16-18 17-18
  -E(7,9)
  -E(3,10)
  -E(8,13)
  current state: 2-3 3-4 4-5 5-6 6-7 6-10 8-11 10-11 11-13 12-14 12-15 12-13 16-17 16-18 17-18
  -E(9,15)
  -E(9,5)
  -E(1,6)
  current state: 2-3 3-4 4-5 5-6 6-7 6-10 8-11 10-11 11-13 12-14 12-15 12-13 16-17 16-18 17-18
  -E(11,13)
  -E(1,9)
  -M[star] at nodes 6,10,5,7
  current state: 2-3 3-4 8-11 12-13 12-14 12-15 16-17 16-18 17-18
  -E(6,10)
  -M[square]
  +E(1,2)
  current state: 1-2 2-3 3-4 8-11 12-13 12-14 12-15 16-17 16-18 17-18
Result: 1-2 2-3 3-4 4-5 5-6 6-7 7-1

Graph visualization saved to /Users/<USER>/Projects/Diffusion_via_Reasoning/demos/graph_demo_motif-heavy_(fixed_steps,_disconnected_allowed).png

=== Rewiring Only (fixed steps) ===

Move probabilities:
  rewire_edge: 1.00

Input:  Reconstruct graph from: 1-3 1-4 1-6 3-5 4-5 4-6 5-6
Trajectory length: 30
Output:
  RW(4,6,3,6)
  RW(1,6,4,6)
  RW(6,5,1,5)
  current state: 1-3 1-4 1-5 3-5 3-6 4-5 4-6
  RW(3,5,3,4)
  RW(3,6,1,6)
  RW(3,4,3,6)
  current state: 1-3 1-4 1-5 1-6 3-6 4-5 4-6
  RW(3,6,5,6)
  RW(1,3,6,3)
  RW(1,5,3,5)
  current state: 1-4 1-6 4-5 4-6 3-5 5-6 3-6
  RW(4,5,4,3)
  RW(6,5,1,5)
  RW(3,6,5,6)
  current state: 1-4 1-5 1-6 3-4 4-6 3-5 5-6
  RW(6,4,6,3)
  RW(6,3,1,3)
  RW(1,6,3,6)
  current state: 1-3 1-4 1-5 3-4 3-5 3-6 5-6
  RW(5,6,4,6)
  RW(4,6,5,6)
  RW(4,1,4,6)
  current state: 1-3 1-5 3-4 3-5 3-6 4-6 5-6
  RW(3,4,3,2)
  RW(6,4,2,4)
  RW(3,6,1,6)
  current state: 1-3 1-5 1-6 2-4 2-3 3-5 5-6
  RW(3,2,6,2)
  RW(6,2,6,4)
  RW(3,5,3,4)
  current state: 1-3 1-5 1-6 2-4 3-4 5-6 4-6
  RW(6,4,5,4)
  RW(6,1,6,7)
  RW(3,4,3,2)
  current state: 1-3 1-5 2-4 2-3 4-5 5-6 6-7
  RW(2,4,3,4)
  RW(1,3,1,2)
  RW(1,5,1,7)
  current state: 1-2 1-7 2-3 3-4 4-5 5-6 6-7
Result: 1-2 2-3 3-4 4-5 5-6 6-7 7-1

Graph visualization saved to /Users/<USER>/Projects/Diffusion_via_Reasoning/demos/graph_demo_rewiring_only_(fixed_steps).png

