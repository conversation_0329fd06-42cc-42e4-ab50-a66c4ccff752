#!/usr/bin/env python3
"""
Comprehensive Molecular Training Data Generation Demo

This script demonstrates the molecular discrete diffusion training data pipeline
with various parameter configurations, showing both structured and character-level approaches.
"""

import sys
import logging
sys.path.append('.')

# Suppress RDKit warnings for cleaner demo output
from rdkit import RDLogger
RDLogger.DisableLog('rdApp.*')

from efficient_training_format import generate_training_data, format_training_example
from src.diffusion_via_reasoning.trajectories.base import TrajectoryConfig, NoiseProcessType

def print_section(title, width=80):
    """Print a formatted section header"""
    print(f"\n{'=' * width}")
    print(f"{title.center(width)}")
    print('=' * width)

def print_subsection(title, width=70):
    """Print a formatted subsection header"""
    print(f"\n{title}")
    print('-' * width)

def demo_scenario(title, description, molecules, config=None, approach='structured', 
                 show_intermediate=False, num_steps=15, show_forward_process=False):
    """Demo a specific training scenario"""
    print_subsection(f"🧪 {title}")
    print(f"📝 {description}")
    print()
    
    if config:
        print("⚙️  Configuration:")
        if approach == 'structured':
            print(f"   Action Probs: Add={config.add_probability:.2f}, Remove={config.remove_probability:.2f}, Replace={config.replace_probability:.2f}")
            print(f"   Target Selection: Atoms={config.atom_target_probability:.2f}, Bonds={config.bond_target_probability:.2f}")
        else:
            print(f"   Character Ops: Insert={config.char_insert_probability:.2f}, Delete={config.char_delete_probability:.2f}, Replace={config.char_replace_probability:.2f}")
        print()
    
    examples = generate_training_data(
        molecules=molecules,
        approach=approach,
        validate_intermediate_states=show_intermediate,
        show_intermediate_every_k_steps=2,  # Show every 2 steps for maximum visibility
        num_steps=num_steps,
        config=config
    )
    
    if examples:
        example = examples[0]
        success_rate = len(example['actions']) / num_steps * 100
        print(f"✅ Generated {len(example['actions'])}/{num_steps} successful actions ({success_rate:.1f}% success rate)")
        print(f"🔍 Noise Process: {example['noise_description']}")
        print()
        
        if show_forward_process:
            print("🔄 Forward Corruption Process (Clean → Corrupted):")
            print(f"   1. Started with clean molecule: {example['target_molecule']}")
            print(f"   2. Applied {len(example['actions'])} corruption steps")
            print(f"   3. Result: corrupted molecule: {example['start_molecule']}")
            print()
            print("🔄 Training Data (Backward Trajectory - Corrupted → Clean):")
        else:
            print("📋 Training Sample (Backward Trajectory - Denoising):")
        
        print(format_training_example(example, show_intermediate_every_k_steps=2))
        
        return len(example['actions'])
    return 0

def main():
    """Main demo function showing comprehensive molecular training pipeline"""
    
    print_section("🎯 COMPREHENSIVE MOLECULAR TRAINING DATA DEMO")
    print()
    print("This demo showcases the molecular discrete diffusion training pipeline")
    print("with various parameter configurations, approaches, and noise processes.")
    print()
    print("📊 DISCRETE DIFFUSION TRAINING PROCESS:")
    print("  1. Forward Noise:  Clean Molecule → Corrupted Molecule")
    print("                     CC(=O)O → C1N#O1 (example)")
    print()  
    print("  2. Reverse Trajectory: Corrupted → Clean (Training Data)")
    print("                         Start: C1N#O1")
    print("                         Actions: [remove bond, add O, replace atom, ...]")
    print("                         Target: CC(=O)O")
    print()
    print("Key Features:")
    print("• Both structured (atom/bond) and character-level approaches")
    print("• Configurable probability distributions") 
    print("• Intermediate state tracking every 5 steps")
    print("• Skip-on-failure logic (no retries)")
    print("• Dynamic noise process descriptions")
    print()
    print("⚠️  NOTE ON SUCCESS RATES:")
    print("   • Structured approach: Actions can fail (invalid indices, chemistry constraints)")
    print("   • When actions fail, they are skipped → lower success rate")
    print("   • Solution: Use more max_steps to ensure sufficient successful actions")
    print("   • Character approach: 100% success rate (string operations never fail)")
    
    # Test molecules optimized for good trajectory generation
    molecules = {
        'simple': ['CC(=O)O'],  # Acetic acid - 4 atoms
        'medium': ['CCCCCCCCCC'],  # Decane - 10 atoms (sweet spot!)
        'medium_branched': ['CC(C)CC(C)CC'],  # Branched octane - 8 atoms
        'medium_functional': ['CCCC(=O)CCC'],  # Heptanone - 8 atoms with carbonyl
        'complex': ['CC(C)(C)OC(=O)NC1=CC=C(C=C1)C(=O)O']  # Keep for comparison
    }
    
    # =================================================================
    # SCENARIO 1: Forward + Backward Process Demonstration
    # =================================================================
    demo_scenario(
        title="Forward Corruption + Backward Training Process",
        description="Shows complete pipeline: Clean → Corrupted (forward) → Training data (backward)",
        molecules=molecules['medium'],
        approach='structured',
        num_steps=100,  # Increased from 20 to 100 to handle action failures
        show_intermediate=True,
        show_forward_process=True
    )
    
    # =================================================================
    # SCENARIO 2: Character-Level Approach with Default Configuration
    # =================================================================
    demo_scenario(
        title="Character-Level Approach (Default Configuration)", 
        description="String operations with equal probabilities (33% each)",
        molecules=molecules['medium'],
        approach='character',
        num_steps=50,  # Increased for more training data
        show_intermediate=True
    )
    
    # =================================================================
    # SCENARIO 3: Character-Level with Intermediate States Demo
    # =================================================================
    # demo_scenario(
    #     title="Character-Level with Intermediate States (15 Steps)",
    #     description="Full demonstration showing intermediate molecular states every 5 steps",
    #     molecules=molecules['simple'],
    #     approach='character',
    #     show_intermediate=True,
    #     num_steps=15
    # )
    
    # =================================================================
    # SCENARIO 4: Custom High-Remove Configuration (Structured)
    # =================================================================
    high_remove_config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        add_probability=0.05,     # Very low add probability
        remove_probability=0.85,  # Very high removal probability
        replace_probability=0.1,
        atom_target_probability=0.3,  # Focus more on bonds (easier to remove)
        bond_target_probability=0.7,
        enforce_validity=False,
        # New validation controls - make actions more permissive
        validate_smiles=False,              # Don't validate SMILES (OFF by default)
        allow_chemical_instability=True,    # Allow unstable molecules (ON by default)
        skip_invalid_indices=False          # Don't skip invalid indices, clamp them instead
    )
    
    demo_scenario(
        title="Custom High-Remove Configuration (85% Remove Operations)", 
        description="Demonstrates configurable probabilities - favors removal operations for higher success rate",
        molecules=molecules['medium_branched'],
        config=high_remove_config,
        approach='structured',
        num_steps=100,  # Increased from 25 to 100 to ensure many successful actions
        show_intermediate=True
    )
    
    # =================================================================
    # SCENARIO 3: Balanced Configuration with Complex Molecule
    # =================================================================
    balanced_config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        add_probability=0.3,      # Higher add for more complex fragments
        remove_probability=0.4,   # Moderate remove
        replace_probability=0.3,  # Higher replace for heteroatom diversity
        atom_target_probability=0.7,  # Favor atoms for complex building
        bond_target_probability=0.3,
        enforce_validity=False,
        # New validation controls - make actions more permissive  
        validate_smiles=False,              # Don't validate SMILES (OFF by default)
        allow_chemical_instability=True,    # Allow unstable molecules (ON by default)
        skip_invalid_indices=False          # Don't skip invalid indices, clamp them instead
    )
    
    demo_scenario(
        title="Fragment Assembly Configuration", 
        description="Balanced probabilities to encourage complex fragment assembly and molecular building",
        molecules=molecules['medium'],
        config=balanced_config,
        approach='structured',
        num_steps=100,  # Increased from 20 to 100 to compensate for action failures
        show_intermediate=True
    )
    
    # =================================================================
    # SCENARIO 5: Ultra-Permissive Structured Configuration
    # =================================================================
    ultra_permissive_config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        add_probability=0.33,
        remove_probability=0.33,
        replace_probability=0.34,
        atom_target_probability=0.7,
        bond_target_probability=0.3,
        enforce_validity=False,
        # Ultra-permissive settings - should give ~100% success rate
        validate_smiles=False,              # NEVER validate SMILES
        allow_chemical_instability=True,    # ALWAYS allow instability
        skip_invalid_indices=False          # NEVER skip, always clamp indices
    )
    
    demo_scenario(
        title="Ultra-Permissive Structured Configuration (Never Fail Mode)",
        description="Structured approach with maximum permissiveness - should achieve ~100% success rate",
        molecules=molecules['medium'],
        config=ultra_permissive_config,
        approach='structured',
        num_steps=50,  # Should get close to 50/50 now
        show_intermediate=True
    )
    
    # =================================================================
    # SCENARIO 6: Custom Character Configuration
    # =================================================================
    custom_char_config = TrajectoryConfig(
        char_insert_probability=0.6,  # Favor insertions
        char_delete_probability=0.2,
        char_replace_probability=0.2,
        enforce_validity=False
    )
    
    demo_scenario(
        title="Custom Character Configuration (60% Insert Operations)",
        description="Character-level with custom probabilities - heavily favors insertions",
        molecules=molecules['medium_functional'],
        config=custom_char_config,
        approach='character',
        num_steps=50,  # Increased for more training data
        show_intermediate=True
    )
    
    # =================================================================
    # SUMMARY AND ANALYSIS
    # =================================================================
    print_section("📊 DEMO SUMMARY AND ANALYSIS")
    
    print_subsection("🎯 Training Data Pipeline Process")
    print("• Forward Noise Process (Data Corruption):")
    print("  1. Start with clean molecule (e.g., CC(=O)O)")
    print("  2. Apply forward corruption steps (add/remove/replace atoms/bonds)")
    print("  3. Generate corrupted molecule (e.g., C1N#O1)")
    print()
    print("• Backward Training Process (Denoising):")
    print("  1. Reverse the forward trajectory")
    print("  2. Training data: Corrupted → Clean")
    print("  3. Model learns to denoise/reconstruct molecules")
    print()
    print("• Approach Characteristics:")
    print("  - Structured: Chemically-informed operations, variable success rate")
    print("  - Character: String operations, 100% success rate, exact step control")
    
    print_subsection("⚙️ Configuration Flexibility")
    print("✅ Fully configurable probability distributions")
    print("✅ Dynamic noise process descriptions")
    print("✅ Support for custom experimental setups")
    print("✅ No hardcoded parameters - everything is transparent")
    
    print_subsection("🚀 Ready for Production")
    print("• Skip-on-failure logic: Fast, clean generation")
    print("• Intermediate state tracking for debugging")
    print("• Both approaches ready for discrete diffusion training")
    print("• Easy to experiment with different noise processes")
    
    print_subsection("💡 Usage Recommendations")
    print("1. Start with default configurations to understand baseline behavior")
    print("2. Use character-level for exact step control requirements")
    print("3. Use structured approach for chemistry-aware applications")
    print("4. Customize probabilities based on your specific training needs")
    print("5. Monitor intermediate states during debugging")
    
    print()
    print("🎉 Molecular training data pipeline ready for discrete diffusion!")

if __name__ == "__main__":
    main() 