"""Demonstration of advanced reward and metric utilities."""

from diffusion_via_reasoning.rewards.molecular_functions import (
    calculate_qed,
    calculate_sa_score,
    calculate_validity,
)
from diffusion_via_reasoning.rewards.protein_functions import (
    predict_foldability,
    is_valid_protein,
)


def main() -> None:
    smiles = "CCO"
    print("SMILES:", smiles)
    print("Valid:", calculate_validity(smiles))
    print("QED:", calculate_qed(smiles))
    print("SA Score:", calculate_sa_score(smiles))

    seq = "MKT"
    print("Sequence:", seq)
    print("Valid protein:", is_valid_protein(seq))
    print("Predicted foldability:", predict_foldability(seq))


if __name__ == "__main__":
    main()
