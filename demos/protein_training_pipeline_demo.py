#!/usr/bin/env python3
"""Protein Training Data Pipeline Demo

This script mirrors the molecular training pipeline demo but for protein
sequences. It demonstrates how to generate forward and backward
trajectories using different noise processes.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from diffusion_via_reasoning.corruption.protein_actions import ProteinActionCorruption
from diffusion_via_reasoning.trajectories.base import TrajectoryConfig, NoiseProcessType


def run_demo(noise_type: NoiseProcessType, description: str) -> None:
    print("=" * 60)
    print(description)
    print("=" * 60)

    sequences = [
        "MKFLVLLFNILCLFPVLA",
        "ACDEFGHIKLMNPQRSTVWY",
        "MKATVKKKESSSKRKT",
    ]

    config = TrajectoryConfig(
        noise_process_type=noise_type,
        max_steps=4,
        add_probability=0.3,
        remove_probability=0.4,
        replace_probability=0.3,
        enforce_validity=True,
    )

    corruption = ProteinActionCorruption()
    forward, backward = corruption.generate_training_data(
        sequences, num_trajectories_per_sequence=1, config=config
    )

    print(f"Generated {len(forward)} forward and {len(backward)} backward trajectories")
    sample = backward[0]
    print("\nSample backward trajectory:")
    print(f"Corrupted seq: {sample.initial_state}")
    print(f"Original seq: {sample.final_state}")
    for i, act in enumerate(sample.actions, 1):
        print(f"  Step {i}: {act}")
    print()


def main() -> None:
    run_demo(NoiseProcessType.POINT_MUTATIONS, "POINT MUTATION NOISE")
    run_demo(NoiseProcessType.MOTIF_DROPOUT, "MOTIF DROPOUT NOISE")


if __name__ == "__main__":
    main()
