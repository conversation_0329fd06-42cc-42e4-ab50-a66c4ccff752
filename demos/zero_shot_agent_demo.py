"""Demonstration of the ZeroShotAgent."""

from diffusion_via_reasoning.agents.zero_shot_agent import ZeroShotAgent, DummyLLM
from diffusion_via_reasoning.tools import WebSearchTool

class MolecularQEDTool:
    name = "calculate_qed"
    description = "Calculate QED for a molecule."
    def run(self, smiles: str) -> str:
        # Dummy QED calculation for demonstration
        if smiles == "CC(=O)OC1=CC=CC=C1C(=O)O":
            return "0.92"
        return "0.0"

def main() -> None:
    tools = [WebSearchTool(), MolecularQEDTool()]
    agent = ZeroShotAgent(DummyLLM(), tools)
    result = agent.run("What is the QED of aspirin?")
    print(result)

if __name__ == "__main__":
    main()
