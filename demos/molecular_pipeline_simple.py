#!/usr/bin/env python3

import sys
sys.path.append('.')

from src.diffusion_via_reasoning.corruption.molecular import MolecularCorruption
from src.diffusion_via_reasoning.trajectories.base import TrajectoryConfig, NoiseProcessType
import json

def main():
    print('=== Molecular Training Data Pipeline ===\n')

    # Sample molecules for training
    molecules = ['CC(=O)O', 'CCO', 'C1=CC=CC=C1', 'CC(C)O']
    print('1. Input Training Molecules:')
    for i, mol in enumerate(molecules, 1):
        print(f'   {i}. {mol}  ({get_molecule_name(mol)})')
    print()

    # Configure the trajectory generation
    print('2. Configuring Training Data Generation:')
    config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.ABSORBING_STATE,
        max_steps=20,                   # 20 corruption steps
        add_probability=0.25,           # 25% chance to add atoms
        remove_probability=0.5,         # 50% chance to remove atoms
        replace_probability=0.25,       # 25% chance to replace atoms
        enforce_validity=False,         # Allow chemically invalid intermediates
        max_retries_per_step=5
    )
    print(f'   Max steps per trajectory: {config.max_steps}')
    print(f'   Noise process: {config.noise_process_type.value}')
    print(f'   Action probabilities: Remove={config.remove_probability}, Add={config.add_probability}, Replace={config.replace_probability}')
    print()

    # Generate training data
    print('3. Generating Training Data...')
    corruption = MolecularCorruption()
    
    forward_trajectories, backward_trajectories = corruption.generate_training_data(
        molecules=molecules,
        num_trajectories_per_molecule=2,
        config=config
    )
    
    print(f'   ✓ Generated {len(forward_trajectories)} forward trajectories')
    print(f'   ✓ Generated {len(backward_trajectories)} backward trajectories')
    print()

    # Show detailed forward example with ALL steps
    print('4. Forward Process Example (Data Corruption - ALL STEPS):')
    if forward_trajectories:
        fwd = forward_trajectories[0]
        print(f'   Original molecule: {fwd.initial_state}')
        print(f'   Final corrupted result: {fwd.final_state}')
        print(f'   Number of actions: {len(fwd.actions)}')
        print('   DETAILED ACTION SEQUENCE:')
        
        # Apply each action step by step to show intermediate states
        from src.diffusion_via_reasoning.actions.molecular import (
            MolecularAddAtom, MolecularRemoveAtom, MolecularReplaceAtom,
            MolecularAddBond, MolecularRemoveBond, MolecularReplaceBond
        )
        
        current_state = fwd.initial_state
        print(f'     Start: {current_state}')
        
        for i, action in enumerate(fwd.actions, 1):
            action_type = action.action_type if hasattr(action, 'action_type') else str(type(action).__name__)
            
            # Get action details
            action_details = ""
            if hasattr(action, 'to_dict'):
                action_dict = action.to_dict()
                if action_type == "add_atom":
                    action_details = f"(add {action_dict.get('atom_symbol', '?')})"
                elif action_type == "remove_atom":
                    action_details = f"(remove idx {action_dict.get('atom_idx', '?')})"
                elif action_type == "replace_atom":
                    action_details = f"(replace idx {action_dict.get('atom_idx', '?')} with {action_dict.get('new_atom_symbol', '?')})"
                elif action_type == "add_bond":
                    action_details = f"(add {action_dict.get('bond_type', '?')} bond between {action_dict.get('atom1_idx', '?')}-{action_dict.get('atom2_idx', '?')})"
                elif action_type == "remove_bond":
                    action_details = f"(remove bond {action_dict.get('atom1_idx', '?')}-{action_dict.get('atom2_idx', '?')})"
                elif action_type == "replace_bond":
                    action_details = f"(change bond {action_dict.get('atom1_idx', '?')}-{action_dict.get('atom2_idx', '?')} to {action_dict.get('new_bond_type', '?')})"
            
            # Apply action and show result
            try:
                result = action.apply(current_state)
                if result.success:
                    current_state = result.new_state
                    print(f'     Step {i:2d}: {action_type} {action_details} → {current_state}')
                else:
                    print(f'     Step {i:2d}: {action_type} {action_details} → FAILED: {result.error_message}')
            except Exception as e:
                print(f'     Step {i:2d}: {action_type} {action_details} → ERROR: {str(e)}')
        
        print(f'     Final: {current_state}')
        print()

    # Show detailed backward example with ALL steps
    print('5. Backward Process Example (Reconstruction Training - ALL STEPS):')
    if backward_trajectories:
        bwd = backward_trajectories[0]
        print(f'   Starting from: {bwd.initial_state}')
        print(f'   Target reconstruction: {bwd.final_state}')
        print(f'   Number of actions: {len(bwd.actions)}')
        print('   DETAILED RECONSTRUCTION SEQUENCE:')
        
        current_state = bwd.initial_state
        print(f'     Start: {current_state}')
        
        for i, action in enumerate(bwd.actions, 1):
            action_type = action.action_type if hasattr(action, 'action_type') else str(type(action).__name__)
            
            # Get action details
            action_details = ""
            if hasattr(action, 'to_dict'):
                action_dict = action.to_dict()
                if action_type == "add_atom":
                    action_details = f"(add {action_dict.get('atom_symbol', '?')})"
                elif action_type == "remove_atom":
                    action_details = f"(remove idx {action_dict.get('atom_idx', '?')})"
                elif action_type == "replace_atom":
                    action_details = f"(replace idx {action_dict.get('atom_idx', '?')} with {action_dict.get('new_atom_symbol', '?')})"
                elif action_type == "add_bond":
                    action_details = f"(add {action_dict.get('bond_type', '?')} bond between {action_dict.get('atom1_idx', '?')}-{action_dict.get('atom2_idx', '?')})"
                elif action_type == "remove_bond":
                    action_details = f"(remove bond {action_dict.get('atom1_idx', '?')}-{action_dict.get('atom2_idx', '?')})"
                elif action_type == "replace_bond":
                    action_details = f"(change bond {action_dict.get('atom1_idx', '?')}-{action_dict.get('atom2_idx', '?')} to {action_dict.get('new_bond_type', '?')})"
            
            # Apply action and show result
            try:
                result = action.apply(current_state)
                if result.success:
                    current_state = result.new_state
                    print(f'     Step {i:2d}: {action_type} {action_details} → {current_state}')
                else:
                    print(f'     Step {i:2d}: {action_type} {action_details} → FAILED: {result.error_message}')
            except Exception as e:
                print(f'     Step {i:2d}: {action_type} {action_details} → ERROR: {str(e)}')
        
        print(f'     Final: {current_state}')
        print()

    # Training format for language models
    print('6. ACTUAL LANGUAGE MODEL TRAINING DATA:')
    print('='*60)
    print()
    print('   ⚠️  IMPORTANT: We only train on BACKWARD trajectories!')
    print('   📍 Forward process = Data generation (like noise in diffusion)')
    print('   🎯 Backward process = Model training (denoising/reconstruction)')
    print()
    
    if backward_trajectories:
        print('   📝 STEP-BY-STEP TRAINING EXAMPLES (Discrete Diffusion):')
        print('   ' + '-'*50)
        
        # Show step-by-step training from one trajectory
        bwd = backward_trajectories[0]
        print(f'\n   TRAJECTORY: {bwd.initial_state} → {bwd.final_state}')
        print('   Each training sample teaches ONE denoising step:')
        print()
        
        # Generate training examples for each step in the trajectory
        current_state = bwd.initial_state
        for step_idx, action in enumerate(bwd.actions):
            action_dict = action.to_dict()
            action_text = f"{action_dict['action_type']}("
            
            # Add action parameters
            params = []
            for key, value in action_dict.items():
                if key != 'action_type' and value is not None:
                    params.append(f"{key}={value}")
            action_text += ", ".join(params) + ")"
            
            # This is one training example
            training_prompt = f"""<|im_start|>system
You are a molecular denoising expert. Given the current molecular state, predict the next action to make it more realistic and valid.
<|im_end|>
<|im_start|>user
Current molecule: {current_state}
<|im_end|>
<|im_start|>assistant
{action_text}
<|im_end|>"""
            
            print(f'   STEP {step_idx + 1} TRAINING EXAMPLE:')
            print(training_prompt)
            
            # Apply action to get next state for next training example
            try:
                result = action.apply(current_state)
                if result.success:
                    next_state = result.new_state
                    print(f'   ✓ Result: {current_state} → {next_state}')
                    current_state = next_state
                else:
                    print(f'   ✗ Failed: {result.error_message}')
            except Exception as e:
                print(f'   ✗ Error: {str(e)}')
            print()
        
        print()
        print('   🔄 INFERENCE PROCESS:')
        print('   ' + '-'*50)
        print('   1. Start with random/corrupted molecule: C.O')
        print('   2. Model predicts: add_atom(atom_symbol=O, ...)')
        print('   3. Apply action → new state: C.O.O')
        print('   4. Model predicts next action: remove_bond(...)')
        print('   5. Apply action → new state: C.O.O (disconnected)')
        print('   6. Continue until valid molecule: CC(=O)O')
        print('   ⚡ Model NEVER sees the target - discovers it step by step!')
        print()
    
    # Show training dataset structure
    print('   📊 TRAINING DATASET STRUCTURE:')
    print('   ' + '-'*50)
    print('   Each training sample teaches the model:')
    print('   INPUT:  Current molecular state')
    print('   OUTPUT: Next single action (one denoising step)')
    print('   GOAL:   Learn P(next_action | current_state)')
    print('   ⚡ NO TARGET MOLECULE GIVEN - model discovers it!')
    print()
    
    # Show dataset generation format
    print('   📋 TRAINING DATASET GENERATION:')
    print('   ' + '-'*50)
    print('   From each backward trajectory, we generate multiple training samples:')
    print()
    if backward_trajectories:
        bwd = backward_trajectories[0]
        print(f'   Trajectory: {bwd.initial_state} → {bwd.final_state}')
        print(f'   Generates {len(bwd.actions)} training samples (one per step)')
        
        # Show how to create training dataset
        print('\n   Training samples:')
        current_state = bwd.initial_state
        for i, action in enumerate(bwd.actions[:2], 1):  # Show first 2 steps
            action_dict = action.to_dict()
            action_text = f"{action_dict['action_type']}("
            params = []
            for key, value in action_dict.items():
                if key != 'action_type' and value is not None:
                    params.append(f"{key}={value}")
            action_text += ", ".join(params) + ")"
            
            print(f'     Sample {i}: {current_state} → {action_text}')
            
            # Apply action for next sample
            try:
                result = action.apply(current_state)
                if result.success:
                    current_state = result.new_state
            except:
                pass
        print()
        
    print('   🎓 TRAINING OBJECTIVE:')
    print('   ' + '-'*50)
    print('   Learn P(next_action | current_molecular_state)')
    print('   Model learns ONE denoising step at a time')
    print('   At inference: iteratively apply actions until valid molecule')
    print('   🚀 DISCRETE DIFFUSION: random → valid through learned steps')
    print()
    
    print('   ⚙️ IMPLEMENTATION CHOICE:')
    print('   ' + '-'*50)
    print('   ✅ USING TEXT FORMAT: function_name(param=value)')
    print('   Benefits:')
    print('     • 20-40% fewer tokens than JSON')
    print('     • Natural for LLMs (function calling syntax)')
    print('     • Less syntax error prone')
    print('     • Human readable and debuggable')
    print('     • Compatible with modern LLM tool training')
    print()

    # Statistics
    print('7. Training Dataset Statistics:')
    total_forward = len(forward_trajectories)
    total_backward = len(backward_trajectories)
    total_actions = sum(len(t.actions) for t in forward_trajectories)
    
    print(f'   Total training examples: {total_forward + total_backward}')
    print(f'   Forward examples:        {total_forward}')
    print(f'   Backward examples:       {total_backward}')
    print(f'   Total actions generated: {total_actions}')
    if total_forward > 0:
        print(f'   Average actions per trajectory: {total_actions / total_forward:.1f}')
    print()

    # Action type distribution
    print('8. Action Type Distribution:')
    action_counts = {}
    for traj in forward_trajectories:
        for action in traj.actions:
            action_type = action.action_type if hasattr(action, 'action_type') else str(type(action).__name__)
            action_counts[action_type] = action_counts.get(action_type, 0) + 1
    
    if action_counts:
        for action_type, count in action_counts.items():
            percentage = (count / total_actions) * 100 if total_actions > 0 else 0
            print(f'   {action_type}: {count} ({percentage:.1f}%)')
    else:
        print('   No actions found')
    print()

    # Show training data structure
    print('9. Training Data Structure:')
    
    if forward_trajectories:
        print('   Forward Trajectory Structure:')
        sample_fwd = forward_trajectories[0]
        print(f'     - initial_state: "{sample_fwd.initial_state}"')
        print(f'     - final_state: "{sample_fwd.final_state}"')
        print(f'     - actions: list of {len(sample_fwd.actions)} action dictionaries')
        if sample_fwd.actions:
            first_action = sample_fwd.actions[0]
            action_dict = first_action.to_dict() if hasattr(first_action, 'to_dict') else str(first_action)
            if isinstance(action_dict, dict):
                print(f'     - sample action keys: {list(action_dict.keys())}')
            else:
                print(f'     - sample action: {action_dict}')
        print()
    
    if backward_trajectories:
        print('   Backward Trajectory Structure:')
        sample_bwd = backward_trajectories[0]
        print(f'     - initial_state: "{sample_bwd.initial_state}"')
        print(f'     - final_state: "{sample_bwd.final_state}"')
        print(f'     - actions: list of {len(sample_bwd.actions)} action dictionaries')
        print()

    print('10. Ready for Model Training!')
    print('    This pipeline generates data for:')
    print('    ✓ Language model fine-tuning on molecular tasks')
    print('    ✓ Reinforcement learning with chemical rewards')
    print('    ✓ Property-guided molecular optimization')
    print('    ✓ Drug discovery and lead compound generation')
    print('    ✓ Chemical space exploration and generation')

def get_molecule_name(smiles):
    """Get common name for molecule."""
    names = {
        'CC(=O)O': 'acetic acid',
        'CCO': 'ethanol',
        'C1=CC=CC=C1': 'benzene',
        'CC(C)O': 'isopropanol'
    }
    return names.get(smiles, 'unknown compound')

if __name__ == "__main__":
    main() 