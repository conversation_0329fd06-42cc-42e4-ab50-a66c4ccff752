#!/usr/bin/env python3
"""
Molecular Training Data Pipeline Demo

This demonstrates how to generate training data for language models using
molecular action-based trajectories. Shows the complete pipeline from
input molecules to structured training samples.
"""

import json
import os
import sys

# Allow running without installing the package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from diffusion_via_reasoning.corruption.molecular import MolecularCorruption
from diffusion_via_reasoning.trajectories.base import TrajectoryConfig
from diffusion_via_reasoning.actions.molecular import MolecularActionRegistry

def main():
    print("=== Molecular Training Data Pipeline Demo ===\n")
    
    # Original SMILES sequences (training data inputs)
    sample_molecules = [
        "CC(=O)O",      # Acetic acid (simple organic acid)
        "CCO",          # Ethanol (alcohol)
        "C1=CC=CC=C1",  # Benzene (aromatic)
        "CC(C)O",       # Isopropanol (branched alcohol)
    ]
    
    print("1. Original Training Molecules:")
    for i, smiles in enumerate(sample_molecules, 1):
        print(f"   {i}. {smiles}")
    print()
    
    # Generate training data using the pipeline
    print("2. Generating Training Data...")
    corruption = MolecularCorruption()
    config = TrajectoryConfig(max_steps=4, enforce_validity=False)
    forward_trajs, backward_trajs = corruption.generate_training_data(
        molecules=sample_molecules,
        num_trajectories_per_molecule=3,
        config=config,
    )

    training_data = {
        "forward_trajectories": forward_trajs,
        "backward_trajectories": backward_trajs,
    }
    
    print(f"   Generated {len(training_data['forward_trajectories'])} forward trajectories")
    print(f"   Generated {len(training_data['backward_trajectories'])} backward trajectories")
    print()
    
    # Show detailed forward trajectory example
    print("3. Forward Process Example (Data Corruption):")
    forward_example = training_data['forward_trajectories'][0]
    
    print(f"   Original: {forward_example.initial_state}")
    print(f"   Target:   {forward_example.final_state}")
    print("   Action Sequence:")
    
    current_state = forward_example.initial_state
    for i, action_data in enumerate(forward_example.actions, 1):
        # Apply action to show intermediate state
        action = MolecularActionRegistry.from_dict(action_data)
        try:
            result = action.apply(current_state)
            if result.success:
                current_state = result.state
                print(f"     Step {i}: {action_data['action_type']} -> {current_state}")
            else:
                print(f"     Step {i}: {action_data['action_type']} -> FAILED: {result.error}")
        except Exception as e:
            print(f"     Step {i}: {action_data['action_type']} -> ERROR: {e}")
    print()
    
    # Show detailed backward trajectory example
    print("4. Backward Process Example (Reconstruction Training):")
    backward_example = training_data['backward_trajectories'][0]
    
    print(f"   Start: {backward_example.initial_state}")
    print(f"   Goal:  {backward_example.final_state}")
    print("   Reconstruction Steps:")
    
    current_state = backward_example.initial_state
    for i, action_data in enumerate(backward_example.actions, 1):
        action = MolecularActionRegistry.from_dict(action_data)
        try:
            result = action.apply(current_state)
            if result.success:
                current_state = result.state
                print(f"     Step {i}: {action_data['action_type']} -> {current_state}")
            else:
                print(f"     Step {i}: {action_data['action_type']} -> FAILED: {result.error}")
        except Exception as e:
            print(f"     Step {i}: {action_data['action_type']} -> ERROR: {e}")
    print()
    
    # Training format for language models
    print("5. Language Model Training Format:")
    print("   Each trajectory becomes a sequence prediction task:")
    print()
    
    # Show forward training format
    print("   Forward Training Examples (Corruption):")
    for i, traj in enumerate(training_data['forward_trajectories'][:2], 1):
        print(f"     Example {i}:")
        print(f"       Input:  'Generate corrupted version of: {traj.initial_state}'")
        print(f"       Output: '{json.dumps(traj.actions)}'")
        print(f"       Result: '{traj.final_state}'")
        print()
    
    # Show backward training format
    print("   Backward Training Examples (Reconstruction):")
    for i, traj in enumerate(training_data['backward_trajectories'][:2], 1):
        print(f"     Example {i}:")
        print(f"       Input:  'Reconstruct molecule from: {traj.initial_state}'")
        print(f"       Output: '{json.dumps(traj.actions)}'")
        print(f"       Result: '{traj.final_state}'")
        print()
    
    # Statistics
    print("6. Training Dataset Statistics:")
    total_forward = len(training_data['forward_trajectories'])
    total_backward = len(training_data['backward_trajectories'])
    total_actions = sum(len(traj.actions) for traj in training_data['forward_trajectories'])
    
    print(f"   Total Training Examples: {total_forward + total_backward}")
    print(f"   Forward Examples:        {total_forward}")
    print(f"   Backward Examples:       {total_backward}")
    print(f"   Total Actions Generated: {total_actions}")
    print(f"   Avg Actions per Trajectory: {total_actions / total_forward:.1f}")
    print()
    
    # Action type distribution
    print("7. Action Type Distribution:")
    action_counts = {}
    for traj in training_data['forward_trajectories']:
        for action_data in traj.actions:
            action_type = action_data['action_type']
            action_counts[action_type] = action_counts.get(action_type, 0) + 1
    
    for action_type, count in action_counts.items():
        percentage = (count / total_actions) * 100
        print(f"   {action_type}: {count} ({percentage:.1f}%)")
    print()
    
    # Show how to prepare for model training
    print("8. Model Training Data Preparation:")
    print("   Convert to standard ML training format:")
    
    training_samples = []
    
    # Forward examples
    for traj in training_data['forward_trajectories']:
        sample = {
            "task": "corruption",
            "input": traj.initial_state,
            "target": traj.final_state,
            "actions": traj.actions,
            "prompt": f"Corrupt the molecule {traj.initial_state} using the given actions:",
            "response": json.dumps(traj.actions)
        }
        training_samples.append(sample)
    
    # Backward examples  
    for traj in training_data['backward_trajectories']:
        sample = {
            "task": "reconstruction",
            "input": traj.initial_state,
            "target": traj.final_state,
            "actions": traj.actions,
            "prompt": f"Reconstruct a valid molecule from {traj.initial_state}:",
            "response": json.dumps(traj.actions)
        }
        training_samples.append(sample)
    
    print(f"   Generated {len(training_samples)} training samples")
    print("   Sample format:")
    sample = training_samples[0]
    print(f"     Prompt: {sample['prompt']}")
    print(f"     Response: {sample['response'][:100]}...")
    print()
    
    print("9. Ready for Training!")
    print("   This data can now be used to train language models for:")
    print("   - Molecular generation from scratch")
    print("   - Property-guided molecular optimization")
    print("   - Chemical space exploration")
    print("   - Drug discovery pipelines")
    print("   - Fine-tuning with reinforcement learning")


if __name__ == "__main__":
    main() 
