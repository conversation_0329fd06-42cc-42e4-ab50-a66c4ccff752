#!/usr/bin/env python3
"""
Protein trajectory generation demo.

This script demonstrates the protein sequence trajectory generation system
without requiring RDKit dependencies.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from diffusion_via_reasoning.trajectories.protein import ProteinTrajectoryGenerator
from diffusion_via_reasoning.trajectories.base import TrajectoryConfig, NoiseProcessType

def demo_protein_trajectories():
    """Demonstrate protein trajectory generation."""
    print("=" * 60)
    print("PROTEIN TRAJECTORY GENERATION DEMO")
    print("=" * 60)
    
    # Sample protein sequences
    proteins = [
        "MKFLVLLFNILCLFPVLA",     # Signal peptide
        "ACDEFGHIKLMNPQRSTVWY",   # All 20 amino acids
        "MKATVKKKESSSKRKT",       # Basic protein
        "EEEEDDDDEEEEDDDD"        # Acidic protein
    ]
    
    # Configuration for protein trajectories
    protein_config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        max_steps=4,
        add_probability=0.3,
        remove_probability=0.4,
        replace_probability=0.3,
        enforce_validity=True,  # Ensure valid amino acid sequences
        max_retries_per_step=10
    )
    
    print("\n1. Protein Action-Based Trajectories")
    print("-" * 50)
    
    generator = ProteinTrajectoryGenerator(protein_config)
    
    for protein in proteins[:2]:  # Test first two proteins
        print(f"\nProtein: {protein}")
        trajectory = generator.generate_trajectory(protein)
        
        print(f"  Initial: {trajectory.initial_state}")
        print(f"  Actions:")
        for i, action in enumerate(trajectory.actions):
            print(f"    Step {i+1}: {action.action_type} - {action}")
        print(f"  Final: {trajectory.final_state}")
        print(f"  Valid sequence: {generator.is_valid_state(trajectory.final_state)}")
    
    # Demonstrate backward trajectory for training
    print("\n\n2. Backward Trajectory for Training")
    print("-" * 50)
    
    forward_traj = generator.generate_trajectory(proteins[2])
    backward_traj = forward_traj.reverse()
    
    print(f"\nForward trajectory:")
    print(f"  {forward_traj.initial_state} -> {forward_traj.final_state}")
    print(f"  Actions: {[a.action_type for a in forward_traj.actions]}")
    
    print(f"\nBackward trajectory (for LLM training):")
    print(f"  Start with: {backward_traj.initial_state}")
    print(f"  Target: {backward_traj.final_state}")
    print(f"  Training sequence:")
    for i, action in enumerate(backward_traj.actions):
        intermediate = backward_traj.apply_partial(i + 1)
        print(f"    Step {i+1}: {action.action_type} -> {intermediate}")

def demo_protein_motifs():
    """Demonstrate protein motif operations."""
    print("\n\n" + "=" * 60)
    print("PROTEIN MOTIF OPERATIONS DEMO")
    print("=" * 60)
    
    # Configuration that favors add operations (to show motifs)
    motif_config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        max_steps=3,
        add_probability=0.8,
        remove_probability=0.1,
        replace_probability=0.1,
        enforce_validity=True
    )
    
    generator = ProteinTrajectoryGenerator(motif_config)
    protein = "MKFLVLLFNILCLFPVLA"
    
    print(f"\nProtein: {protein}")
    trajectory = generator.generate_trajectory(protein)
    
    print(f"  Initial: {trajectory.initial_state}")
    print(f"  Actions (add-focused to show motifs):")
    for i, action in enumerate(trajectory.actions):
        if hasattr(action, 'motif'):
            print(f"    Step {i+1}: {action.action_type} - motif '{action.motif}' at pos {action.position}")
        elif hasattr(action, 'amino_acid'):
            print(f"    Step {i+1}: {action.action_type} - '{action.amino_acid}' at pos {action.position}")
        else:
            print(f"    Step {i+1}: {action.action_type} - {action}")
    print(f"  Final: {trajectory.final_state}")

def demo_training_data():
    """Demonstrate training data generation for proteins."""
    print("\n\n" + "=" * 60)
    print("PROTEIN TRAINING DATA GENERATION DEMO")
    print("=" * 60)
    
    proteins = ["MKFLVL", "ACDEFG", "KKKRRR"]
    protein_config = TrajectoryConfig(
        noise_process_type=NoiseProcessType.FIXED_STEPS,
        max_steps=3,
        add_probability=0.3,
        remove_probability=0.4,
        replace_probability=0.3,
        enforce_validity=True
    )
    
    generator = ProteinTrajectoryGenerator(protein_config)
    forward_trajs = generator.generate_training_trajectories(
        proteins, num_trajectories_per_sequence=2
    )
    backward_trajs = generator.generate_backward_trajectories(forward_trajs)
    
    print(f"Generated {len(forward_trajs)} forward and {len(backward_trajs)} backward trajectories")
    print(f"\nSample training trajectories:")
    
    for i, sample in enumerate(backward_trajs[:3]):  # Show first 3
        print(f"\nTrajectory {i+1}:")
        print(f"  Start: {sample.initial_state}")
        print(f"  Target: {sample.final_state}")
        print(f"  Actions: {[a.action_type for a in sample.actions]}")

def main():
    """Run all demonstrations."""
    print("PROTEIN TRAJECTORY GENERATION SYSTEM DEMO")
    print("=" * 60)
    print("This demo showcases the protein sequence trajectory generation system.")
    
    try:
        demo_protein_trajectories()
        demo_protein_motifs()
        demo_training_data()
        
        print("\n\n" + "=" * 60)
        print("DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("\nKey features demonstrated:")
        print("✓ Protein action-based trajectory generation")
        print("✓ Forward and backward trajectories")
        print("✓ Amino acid and motif operations")
        print("✓ Training data generation")
        print("✓ Validity enforcement")
        print("✓ Configurable probabilities")
        
    except Exception as e:
        print(f"\nError during demo: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 