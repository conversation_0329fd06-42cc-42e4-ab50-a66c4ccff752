from diffusion_via_reasoning.trajectory import (
    TrajectoryGenerator,
    ReverseMoleculeTrajectory,
)
import pandas as pd

TOKENS = ['#', '(', ')', '+', '-', '/', '1', '2', '3', '4', '5', '6', '7', '=', '@', 'B', 'C', 'F', 'H', 'I', 'N', 'O', 'S', '[', ']', 'c', 'l', 'n', 'o', 'r', 's']

# Mindset: For a diffusion forward noising process, need delete probability to be higher than add probability
# Estimated length change per step: 0.5 * -1 + 0.25 * 1 + 0.25 * 0 = -0.25
smiles = "CCOCC"
traj_len = len(smiles) * 4
gen = TrajectoryGenerator(
    valid_tokens=TOKENS,
    action_probs={"add": 0.25, "remove": 0.5, "replace": 0.25},
    num_steps=None,
    seed=1234,
    generate_until_empty=True,
)

trajectory = gen.generate(smiles)
print("Forward noise trajectory:")
log, states = trajectory.to_log()
# for line, state in zip(log, states):
#     print(line, "|", state)
forward_trajectory_df = pd.DataFrame({"log": log, "state": states})
print(forward_trajectory_df)

reverse = ReverseMoleculeTrajectory(trajectory)
print("\nReverse generation trajectory:")
log, states = reverse.to_log()
# for line, state in zip(log, states):
#     print(line, "|", state)
reverse_trajectory_df = pd.DataFrame({"log": log, "state": states})
print(reverse_trajectory_df)
