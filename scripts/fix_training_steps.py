#!/usr/bin/env python3
"""
Quick fix script to ensure save_steps is compatible with eval_steps.
This fixes the ValueError: --load_best_model_at_end requires the saving steps 
to be a round multiple of the evaluation steps.
"""

import argparse
import re

def fix_training_steps(file_path: str, save_steps: int = None, logging_steps: int = None):
    """Fix save_steps and logging_steps compatibility in training scripts."""
    
    print(f"🔧 Fixing training steps in: {file_path}")
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find current values
    save_steps_match = re.search(r'SAVE_STEPS=(\d+)', content)
    logging_steps_match = re.search(r'LOGGING_STEPS=(\d+)', content)
    
    if not save_steps_match or not logging_steps_match:
        print("❌ Could not find SAVE_STEPS or LOGGING_STEPS in the file")
        return False
    
    current_save_steps = int(save_steps_match.group(1))
    current_logging_steps = int(logging_steps_match.group(1))
    
    print(f"📊 Current values: SAVE_STEPS={current_save_steps}, LOGGING_STEPS={current_logging_steps}")
    
    # Determine new values
    if save_steps is None and logging_steps is None:
        # Auto-fix: make save_steps a multiple of logging_steps
        if current_save_steps % current_logging_steps != 0:
            new_save_steps = ((current_save_steps // current_logging_steps) + 1) * current_logging_steps
            new_logging_steps = current_logging_steps
        else:
            print("✅ Values are already compatible!")
            return True
    else:
        new_save_steps = save_steps if save_steps is not None else current_save_steps
        new_logging_steps = logging_steps if logging_steps is not None else current_logging_steps
        
        # Ensure compatibility
        if new_save_steps % new_logging_steps != 0:
            new_save_steps = ((new_save_steps // new_logging_steps) + 1) * new_logging_steps
            print(f"⚠️  Adjusted save_steps to {new_save_steps} to be compatible with logging_steps={new_logging_steps}")
    
    print(f"🔄 New values: SAVE_STEPS={new_save_steps}, LOGGING_STEPS={new_logging_steps}")
    
    # Replace values in content
    content = re.sub(r'SAVE_STEPS=\d+', f'SAVE_STEPS={new_save_steps}', content)
    content = re.sub(r'LOGGING_STEPS=\d+', f'LOGGING_STEPS={new_logging_steps}', content)
    
    # Write back to file
    with open(file_path, 'w') as f:
        f.write(content)
    
    print(f"✅ Fixed! SAVE_STEPS={new_save_steps}, LOGGING_STEPS={new_logging_steps}")
    print(f"📝 Updated file: {file_path}")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='Fix save_steps and logging_steps compatibility')
    parser.add_argument('--file', type=str, 
                        default='scripts/submit_diffusion_training.sh',
                        help='Path to the training script to fix')
    parser.add_argument('--save_steps', type=int, default=None,
                        help='New save_steps value (optional)')
    parser.add_argument('--logging_steps', type=int, default=None,
                        help='New logging_steps value (optional)')
    parser.add_argument('--auto', action='store_true',
                        help='Automatically fix without prompting')
    
    args = parser.parse_args()
    
    print("🔍 Training Steps Compatibility Fixer")
    print("=" * 40)
    
    if not args.auto:
        print(f"This will fix the compatibility issue between save_steps and logging_steps")
        print(f"in the file: {args.file}")
        print()
        response = input("Continue? (y/N): ")
        if response.lower() != 'y':
            print("Cancelled.")
            return
    
    success = fix_training_steps(args.file, args.save_steps, args.logging_steps)
    
    if success:
        print()
        print("🎉 Fix completed successfully!")
        print("💡 The training script should now work without the ValueError.")
        print()
        print("📋 What was fixed:")
        print("  - save_steps is now a multiple of logging_steps")
        print("  - This satisfies the requirement for --load_best_model_at_end")
        print()
        print("🚀 You can now run your training script:")
        print(f"  sbatch {args.file}")
    else:
        print("❌ Fix failed. Please check the file manually.")

if __name__ == "__main__":
    main()
