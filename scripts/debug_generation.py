#!/usr/bin/env python3
"""
Debug script to test basic model generation and identify bottlenecks.
"""

import os
import sys
import time
import torch
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent))

def test_basic_generation():
    """Test the most basic generation to identify bottlenecks."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_restructured_head1000_crop/training_runs/2025-06-14_17-27-45_GPT2_Diffusion_gpt2-large/checkpoint-668"
    
    print("🐛 Debug: Testing Basic Model Generation")
    print("=" * 50)
    
    try:
        print("📦 Loading libraries...")
        from transformers import AutoTokenizer, AutoModelForCausalLM
        print("✅ Libraries loaded")
        
        print("🔧 Checking device...")
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"   Device: {device}")
        if device == "cuda":
            print(f"   GPU memory available: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            print(f"   GPU memory free: {(torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated()) / 1024**3:.1f} GB")
        
        print("🔄 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        print(f"✅ Tokenizer loaded (vocab size: {len(tokenizer)})")
        
        print("🔄 Loading model...")
        start_time = time.time()
        if device == "cuda":
            model = AutoModelForCausalLM.from_pretrained(
                model_path, 
                torch_dtype=torch.float16,
                device_map="auto"
            )
        else:
            model = AutoModelForCausalLM.from_pretrained(model_path).to(device)
        
        model.eval()
        load_time = time.time() - start_time
        print(f"✅ Model loaded in {load_time:.2f} seconds")
        
        if device == "cuda":
            print(f"   GPU memory after loading: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
        
        # Test 1: Very basic generation
        print("\n🧪 Test 1: Very basic generation (max_length=50)")
        prompt = "Diffusion reverse process:\nInitial state: ''"
        inputs = tokenizer(prompt, return_tensors="pt").to(device)
        print(f"   Input shape: {inputs['input_ids'].shape}")
        print(f"   Input text: '{prompt}'")
        
        start_time = time.time()
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=50,  # Very short
                temperature=0.8,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
            )
        gen_time = time.time() - start_time
        
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"✅ Basic generation completed in {gen_time:.2f} seconds")
        print(f"   Generated length: {len(generated_text)} characters")
        print(f"   Generated text: '{generated_text[:200]}{'...' if len(generated_text) > 200 else ''}'")
        
        # Test 2: Medium generation
        print("\n🧪 Test 2: Medium generation (max_length=128)")
        start_time = time.time()
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=128,
                temperature=0.8,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
            )
        gen_time = time.time() - start_time
        
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"✅ Medium generation completed in {gen_time:.2f} seconds")
        print(f"   Generated length: {len(generated_text)} characters")
        
        # Test 3: Longer generation with more parameters
        print("\n🧪 Test 3: Longer generation (max_length=256)")
        start_time = time.time()
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=256,
                temperature=0.8,
                top_p=0.9,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.01,
            )
        gen_time = time.time() - start_time
        
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"✅ Longer generation completed in {gen_time:.2f} seconds")
        print(f"   Generated length: {len(generated_text)} characters")
        
        # Test 4: Test the problematic parameters
        print("\n🧪 Test 4: Testing with min_length parameter")
        start_time = time.time()
        try:
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_length=256,
                    min_length=inputs['input_ids'].shape[1] + 100,  # This might be the issue
                    temperature=0.8,
                    top_p=0.9,
                    do_sample=True,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                )
            gen_time = time.time() - start_time
            print(f"✅ Generation with min_length completed in {gen_time:.2f} seconds")
        except Exception as e:
            print(f"❌ Generation with min_length failed: {e}")
        
        print(f"\n🎉 All basic tests completed successfully!")
        print(f"   The model can generate text without hanging.")
        print(f"   The issue might be with specific parameter combinations.")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_basic_generation()
    if success:
        print("\n✅ Debug test passed! The model generation works.")
    else:
        print("\n❌ Debug test failed! Check the errors above.")
        sys.exit(1)
