"""
Enhanced training script supporting modern LLMs for diffusion via reasoning.
Supports: Llama, Mistral, Qwen, CodeLlama, and other modern architectures.
"""
import os
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["WANDB_PROJECT"] = "Diffusion_via_Reasoning"
os.environ["WANDB_CACHE_DIR"] = "/u/dzhang5/Diffusion_via_Reasoning/wandb"

# Set environment variables for single-node multi-GPU training
os.environ["MASTER_ADDR"] = os.environ.get("MASTER_ADDR", "localhost")
os.environ["MASTER_PORT"] = os.environ.get("MASTER_PORT", "12355")

# Python imports
import argparse
from copy import deepcopy
from datetime import datetime
import json
from pathlib import Path
import random
from typing import Dict, List, Any

# Third party imports
from datasets import Dataset
import numpy as np
import torch
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    TrainerCallback,
    BitsAndBytesConfig
)
import wandb


class BestModelCallback(TrainerCallback):
    """Custom callback to save the model with the best validation loss."""

    def __init__(self, output_dir, tokenizer, is_main_process=True):
        self.output_dir = output_dir
        self.tokenizer = tokenizer
        self.is_main_process = is_main_process
        self.best_eval_loss = float('inf')
        self.best_model_dir = os.path.join(output_dir, "best_model")

    def on_log(self, args, state, control, model=None, logs=None, **kwargs):
        """Called when logging occurs - this often contains eval metrics."""
        if not self.is_main_process or logs is None:
            return

        current_eval_loss = logs.get("eval_loss")
        if current_eval_loss is not None:
            print(f"[BestModelCallback] on_log - eval_loss: {current_eval_loss}, Best so far: {self.best_eval_loss}")
            self._save_if_best(current_eval_loss, state, model)

    def _save_if_best(self, current_eval_loss, state, model):
        """Helper method to save model if it's the best so far."""
        if current_eval_loss < self.best_eval_loss:
            previous_best = self.best_eval_loss
            self.best_eval_loss = current_eval_loss

            # Save the best model
            print(f"🎉 New best validation loss: {current_eval_loss:.6f} (previous: {previous_best:.6f})")
            print(f"📁 Saving best model to {self.best_model_dir}...")

            try:
                # Create directory if it doesn't exist
                os.makedirs(self.best_model_dir, exist_ok=True)

                # Save model and tokenizer
                model.save_pretrained(self.best_model_dir)
                self.tokenizer.save_pretrained(self.best_model_dir)

                # Save metrics
                best_metrics = {
                    "best_eval_loss": current_eval_loss,
                    "step": state.global_step,
                    "epoch": state.epoch,
                    "model_type": "best_validation_loss",
                    "saved_at_step": state.global_step
                }

                with open(os.path.join(self.best_model_dir, "best_metrics.json"), "w") as f:
                    json.dump(best_metrics, f, indent=2)

                print(f"✅ Best model saved successfully at step {state.global_step}")
            except Exception as e:
                print(f"❌ Error saving best model: {e}")
                import traceback
                traceback.print_exc()


def get_experiment_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--base_output_dir', type=str, 
                        default="/u/dzhang5/Diffusion_via_Reasoning/training_runs", 
                        help="Base output directory")
    parser.add_argument('--dataloader_num_workers', type=int, default=0,
                        help="Number of workers for dataloader")
    parser.add_argument('--gradient_checkpointing', action="store_true", 
                        help="Whether to use gradient checkpointing")
    parser.add_argument('--gradient_accumulation_steps', type=int, default=4, 
                        help="Number of gradient accumulation steps")
    parser.add_argument('--data_dir', type=str, 
                        default="./data/CreateProtein_restructured_head1000_crop", 
                        help="Path to directory containing train_data_restructured.json, val_data_restructured.json, test_data_restructured.json")
    parser.add_argument('--learning_rate', type=float, default=5e-5, 
                        help="Learning rate for training")
    parser.add_argument('--llm_cache_dir', type=str, 
                        default="/u/dzhang5/Diffusion_via_Reasoning/.cache", 
                        help="Directory for LLM cache files")
    
    # Enhanced model selection with modern LLMs
    parser.add_argument('--llm_model_name_or_path', type=str, 
                        default="microsoft/DialoGPT-medium", 
                        help="""Name or path of LLM model to use. Options include:
                        - GPT-2 family: gpt2, gpt2-medium, gpt2-large, gpt2-xl
                        - Llama family: meta-llama/Llama-2-7b-hf, meta-llama/Llama-2-13b-hf
                        - Code models: microsoft/DialoGPT-medium, Salesforce/codegen-350M-mono
                        - Mistral: mistralai/Mistral-7B-v0.1
                        - Qwen: Qwen/Qwen-1_8B, Qwen/Qwen-7B
                        - Small efficient models: microsoft/DialoGPT-small, distilgpt2""")
    
    parser.add_argument('--max_context_length', type=int, default=2048, 
                        help="Maximum context length for input text")
    parser.add_argument('--num_train_epochs', type=int, default=3, 
                        help="Number of training epochs")
    parser.add_argument('--per_device_batch_size', type=int, default=8,
                        help="Per device batch size")
    
    # Quantization and efficiency options
    parser.add_argument('--use_4bit_quantization', action="store_true",
                        help="Use 4-bit quantization for memory efficiency")
    parser.add_argument('--use_8bit_quantization', action="store_true",
                        help="Use 8-bit quantization for memory efficiency")
    parser.add_argument('--use_lora', action="store_true",
                        help="Use LoRA (Low-Rank Adaptation) for efficient fine-tuning")
    parser.add_argument('--lora_rank', type=int, default=16,
                        help="LoRA rank for parameter-efficient fine-tuning")
    parser.add_argument('--lora_alpha', type=int, default=32,
                        help="LoRA alpha parameter")
    parser.add_argument('--lora_dropout', type=float, default=0.1,
                        help="LoRA dropout rate")
    
    parser.add_argument('--resume_training', action="store_true", 
                        help="Whether to resume training")
    parser.add_argument('--resume_checkpoint_path', type=str, default=None,
                        help="Path to checkpoint to resume training from")
    parser.add_argument('--save_steps', type=int, default=500, 
                        help="Frequency of saving model checkpoints")
    parser.add_argument('--seed', type=int, default=1234, help="Random seed")
    parser.add_argument('--logging_steps', type=int, default=100, 
                        help="Frequency of logging training loss")
    parser.add_argument('--wandb_logging', action="store_true", 
                        help="Whether to log to Weights & Biases")
    parser.add_argument('--wandb_cache_dir', type=str, 
                        default="/u/dzhang5/Diffusion_via_Reasoning/wandb", 
                        help="Directory for Weights & Biases cache files")
    parser.add_argument('--warmup_ratio', type=float, default=0.1, 
                        help="Warmup ratio")
    parser.add_argument('--weight_decay', type=float, default=0.01, 
                        help="Weight decay in optimizer")
    parser.add_argument('--max_samples', type=int, default=None,
                        help="Maximum number of samples to use for training (for debugging)")
    
    # Add distributed training arguments
    parser.add_argument('--ddp_backend', type=str, default="nccl",
                        help="Backend for distributed training")
    parser.add_argument('--ddp_find_unused_parameters', action="store_true",
                        help="Whether to find unused parameters in DDP")
    parser.add_argument('--save_total_limit', type=int, default=10,
                        help="Maximum number of checkpoints to keep (None for unlimited)")
    
    # Model architecture specific options
    parser.add_argument('--trust_remote_code', action="store_true",
                        help="Trust remote code for custom model architectures")
    parser.add_argument('--use_flash_attention', action="store_true",
                        help="Use Flash Attention 2 for efficiency (if supported)")
    
    args = parser.parse_args()
    return args


def get_model_config(model_name: str, args):
    """Get model-specific configuration and recommendations."""
    config = {
        "quantization_config": None,
        "torch_dtype": torch.float16,
        "attn_implementation": "eager",
        "trust_remote_code": args.trust_remote_code,
        "recommended_batch_size": args.per_device_batch_size,
        "recommended_lr": args.learning_rate
    }
    
    # Model-specific optimizations
    if "llama" in model_name.lower():
        config.update({
            "torch_dtype": torch.bfloat16,
            "attn_implementation": "flash_attention_2" if args.use_flash_attention else "eager",
            "recommended_batch_size": max(1, args.per_device_batch_size // 2),  # Llama needs smaller batches
            "recommended_lr": 2e-5
        })
    elif "mistral" in model_name.lower():
        config.update({
            "torch_dtype": torch.bfloat16,
            "attn_implementation": "flash_attention_2" if args.use_flash_attention else "eager",
            "recommended_batch_size": max(1, args.per_device_batch_size // 2),
            "recommended_lr": 1e-5
        })
    elif "qwen" in model_name.lower():
        config.update({
            "torch_dtype": torch.bfloat16,
            "trust_remote_code": True,
            "recommended_lr": 3e-5
        })
    elif "codegen" in model_name.lower() or "code" in model_name.lower():
        config.update({
            "recommended_lr": 1e-5,
            "torch_dtype": torch.float16
        })
    elif "gpt2" in model_name.lower() or "dialogpt" in model_name.lower():
        config.update({
            "torch_dtype": torch.float16,
            "recommended_lr": 5e-5
        })
    
    # Add quantization if requested
    if args.use_4bit_quantization:
        config["quantization_config"] = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.bfloat16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type="nf4"
        )
        config["torch_dtype"] = None  # Let quantization handle dtype
    elif args.use_8bit_quantization:
        config["quantization_config"] = BitsAndBytesConfig(
            load_in_8bit=True
        )
        config["torch_dtype"] = None
    
    return config


def load_diffusion_data(data_dir: str, max_samples: int = None) -> Dict[str, List[Dict[str, Any]]]:
    """Load diffusion training data from restructured JSON files."""
    data_splits = {}
    
    for split in ['train', 'val', 'test']:
        file_path = os.path.join(data_dir, f"{split}_data_restructured.json")
        if os.path.exists(file_path):
            print(f"Loading {split} data from {file_path}...")
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            if max_samples and len(data) > max_samples:
                data = data[:max_samples]
                print(f"  Limited to {max_samples} samples")
            
            data_splits[split] = data
            print(f"  Loaded {len(data)} samples")
        else:
            print(f"Warning: {file_path} not found, skipping {split} split")
    
    return data_splits


def format_diffusion_training_text(reverse_process: List[str]) -> str:
    """Format diffusion data into training text with improved structure for modern LLMs."""
    # Enhanced formatting for better LLM understanding
    text = "# Protein Generation via Diffusion Process\n\n"
    text += "## Reverse Process Steps:\n\n"

    # Join all reverse process steps with better structure
    for i, step in enumerate(reverse_process):
        if i == 0:
            # First element is empty string (initial state)
            text += f"**Initial State:** ''\n"
        else:
            # Subsequent elements are actions with clear numbering
            text += f"**Step {i}:** {step}\n"
    
    text += "\n## Process Complete\n"
    return text


def create_dataset_from_diffusion_data(data: List[Dict[str, Any]]) -> Dataset:
    """Convert diffusion data to Huggingface Dataset format."""
    formatted_texts = []

    for sample in data:
        reverse_process = sample['reverse_process']

        # Format the text for training
        text = format_diffusion_training_text(reverse_process)
        formatted_texts.append(text)

    # Create Huggingface dataset
    dataset = Dataset.from_dict({"text": formatted_texts})
    return dataset


def setup_lora_model(model, args):
    """Setup LoRA (Low-Rank Adaptation) for parameter-efficient fine-tuning."""
    try:
        from peft import LoraConfig, get_peft_model, TaskType

        # Define LoRA configuration
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=args.lora_rank,
            lora_alpha=args.lora_alpha,
            lora_dropout=args.lora_dropout,
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]  # Common attention modules
        )

        # Apply LoRA to the model
        model = get_peft_model(model, lora_config)
        model.print_trainable_parameters()

        print(f"✅ LoRA setup complete with rank={args.lora_rank}, alpha={args.lora_alpha}")
        return model

    except ImportError:
        print("❌ PEFT library not found. Install with: pip install peft")
        print("Falling back to full fine-tuning...")
        return model
    except Exception as e:
        print(f"❌ Error setting up LoRA: {e}")
        print("Falling back to full fine-tuning...")
        return model


def main(args):
    print("#--- Modern LLM Diffusion Training Script Starting... ---#")
    print(f"🚀 Using model: {args.llm_model_name_or_path}")

    # Handle distributed training and W&B initialization
    local_rank = int(os.environ.get("LOCAL_RANK", 0))
    world_size = int(os.environ.get("WORLD_SIZE", 1))
    is_main_process = local_rank == 0

    print(f"Local rank: {local_rank}, World size: {world_size}, Is main process: {is_main_process}")

    # Set device based on local rank for proper GPU assignment
    if torch.cuda.is_available():
        device = f"cuda:{local_rank}"
        torch.cuda.set_device(local_rank)
        print(f"Using device: {device} (GPU {local_rank})")
    else:
        device = "cpu"
        print(f"Using device: {device}")

    # Get model-specific configuration
    model_config = get_model_config(args.llm_model_name_or_path, args)
    print(f"📋 Model configuration: {model_config}")

    # Adjust batch size and learning rate based on model recommendations
    if model_config["recommended_batch_size"] != args.per_device_batch_size:
        print(f"⚠️  Recommended batch size for this model: {model_config['recommended_batch_size']} (you set: {args.per_device_batch_size})")

    if model_config["recommended_lr"] != args.learning_rate:
        print(f"⚠️  Recommended learning rate for this model: {model_config['recommended_lr']} (you set: {args.learning_rate})")

    # Set W&B environment variables to prevent multiple runs
    if args.wandb_logging and not is_main_process:
        os.environ["WANDB_DISABLED"] = "true"
        print(f"W&B disabled for non-main process (rank {local_rank})")
    elif args.wandb_logging and is_main_process:
        os.environ["WANDB_DISABLED"] = "false"
        print("W&B enabled for main process")

    # Set up output directory and tokenizer
    if args.resume_training:
        assert args.resume_checkpoint_path is not None, \
            "Must specify checkpoint path to resume training."
        assert os.path.exists(args.resume_checkpoint_path), \
            f"Resume checkpoint path {args.resume_checkpoint_path} does not exist."

        output_dir = Path(args.resume_checkpoint_path).parent
        output_dir = str(output_dir)
        run_name = args.resume_checkpoint_path.split("/")[-2]
        run_name = str(run_name) + "_continue"
        print(f"Resuming training from checkpoint: {args.resume_checkpoint_path}")

        # Load tokenizer from checkpoint
        print(f"Loading tokenizer from {args.resume_checkpoint_path}...")
        tokenizer = AutoTokenizer.from_pretrained(args.resume_checkpoint_path)
    else:
        datetimestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        model_short_name = args.llm_model_name_or_path.split("/")[-1]
        run_name = datetimestamp + f"_ModernLLM_Diffusion_{model_short_name}"
        output_dir = os.path.join(args.base_output_dir, run_name)
        os.makedirs(output_dir, exist_ok=True)
        print(f"Output directory: {output_dir}")

        # Save experiment args
        with open(os.path.join(output_dir, "experiment_args.json"), "w") as f:
            json.dump(args.__dict__, f, indent=4)

        # Initialize tokenizer
        print(f"Loading tokenizer from {args.llm_model_name_or_path}...")
        tokenizer = AutoTokenizer.from_pretrained(
            args.llm_model_name_or_path,
            cache_dir=args.llm_cache_dir,
            trust_remote_code=model_config["trust_remote_code"]
        )

        # Set padding token correctly for tokenizer
        print("Setting up tokenizer...")
        if tokenizer.pad_token is None:
            if tokenizer.eos_token is not None:
                print("No pad token detected. Making EOS token the pad token.")
                tokenizer.pad_token = tokenizer.eos_token
            else:
                print("No pad token or EOS token detected. Adding pad token.")
                tokenizer.add_special_tokens({'pad_token': '[PAD]'})

        # Ensure pad_token_id is valid
        print(f"Tokenizer vocab size: {len(tokenizer)}")
        print(f"pad_token_id: {tokenizer.pad_token_id}")
        print(f"eos_token_id: {tokenizer.eos_token_id}")

    # Load diffusion data
    print("Loading diffusion data...")
    data_splits = load_diffusion_data(args.data_dir, args.max_samples)

    if 'train' not in data_splits:
        raise ValueError("No training data found!")

    # Create datasets
    print("Creating datasets...")
    train_dataset = create_dataset_from_diffusion_data(data_splits['train'])
    val_dataset = create_dataset_from_diffusion_data(data_splits['val']) if 'val' in data_splits else None

    print(f"Train dataset size: {len(train_dataset)}")
    if val_dataset:
        print(f"Validation dataset size: {len(val_dataset)}")

    # Print sample data
    print("\nSample training text:")
    print("=" * 80)
    print(train_dataset[0]['text'][:500] + "..." if len(train_dataset[0]['text']) > 500 else train_dataset[0]['text'])
    print("=" * 80)

    # Initialize model
    if args.resume_training:
        print("Resuming training:")
        print(f"Loading model from checkpoint: {args.resume_checkpoint_path}...")
        model = AutoModelForCausalLM.from_pretrained(
            pretrained_model_name_or_path=args.resume_checkpoint_path,
            cache_dir=args.llm_cache_dir,
            trust_remote_code=model_config["trust_remote_code"]
        )
        print(f"Successfully reloaded model: {model}")
        torch.cuda.empty_cache()
    else:
        print(f"Initializing model with base LLM: {args.llm_model_name_or_path}...")

        # Prepare model loading arguments
        model_kwargs = {
            "pretrained_model_name_or_path": args.llm_model_name_or_path,
            "cache_dir": args.llm_cache_dir,
            "trust_remote_code": model_config["trust_remote_code"],
            "attn_implementation": model_config["attn_implementation"],
        }

        # Add quantization config if specified
        if model_config["quantization_config"] is not None:
            model_kwargs["quantization_config"] = model_config["quantization_config"]
            print(f"🔧 Using quantization: {type(model_config['quantization_config']).__name__}")

        # Add torch_dtype if not using quantization
        if model_config["torch_dtype"] is not None:
            model_kwargs["torch_dtype"] = model_config["torch_dtype"]
            print(f"🔧 Using dtype: {model_config['torch_dtype']}")

        try:
            model = AutoModelForCausalLM.from_pretrained(**model_kwargs)
            print(f"✅ Successfully loaded model: {type(model).__name__}")
        except Exception as e:
            print(f"❌ Error loading model with optimized settings: {e}")
            print("🔄 Falling back to basic loading...")
            # Fallback to basic loading
            model = AutoModelForCausalLM.from_pretrained(
                args.llm_model_name_or_path,
                cache_dir=args.llm_cache_dir,
                trust_remote_code=model_config["trust_remote_code"]
            )

    # Resize model embeddings if needed
    original_vocab_size = model.config.vocab_size
    model.resize_token_embeddings(len(tokenizer))
    if len(tokenizer) != original_vocab_size:
        print(f"📏 Resized embeddings from {original_vocab_size} to {len(tokenizer)}")

    # Setup LoRA if requested
    if args.use_lora:
        print("🔧 Setting up LoRA for parameter-efficient fine-tuning...")
        model = setup_lora_model(model, args)

    # Initialize Weights & Biases
    if args.wandb_logging and is_main_process:
        wandb.init(
            project="Diffusion_via_Reasoning",
            name=run_name,
            config=args,
            dir=args.wandb_cache_dir
        )
        print("W&B initialized on main process")
    elif args.wandb_logging:
        print(f"W&B skipped on non-main process")
    else:
        print('Weights and biases tracking not turned on in config, no logging...')

    # Process dataset
    def tokenize_text(examples):
        """Tokenize the text data."""
        tokenizer_outputs = tokenizer(
            examples["text"],
            truncation=True,
            padding=False,  # We'll pad in the data collator
            max_length=args.max_context_length,
            return_tensors=None  # Return lists, not tensors
        )

        # Validate tokenized outputs
        vocab_size = len(tokenizer)
        validated_input_ids = []
        for input_ids in tokenizer_outputs.input_ids:
            # Check for out-of-bounds tokens
            max_token = max(input_ids) if input_ids else 0
            if max_token >= vocab_size:
                print(f"Warning: Found out-of-bounds token {max_token} >= {vocab_size}, clipping...")
                input_ids = [min(token_id, vocab_size - 1) for token_id in input_ids]
            validated_input_ids.append(input_ids)

        return {
            "input_ids": validated_input_ids,
            "attention_mask": tokenizer_outputs.attention_mask,
        }

    print("Tokenizing datasets...")
    train_dataset = train_dataset.map(
        tokenize_text,
        batched=True,
        remove_columns=["text"],
        desc="Tokenizing train dataset"
    )

    if val_dataset:
        val_dataset = val_dataset.map(
            tokenize_text,
            batched=True,
            remove_columns=["text"],
            desc="Tokenizing validation dataset"
        )

    def data_collator(examples):
        """Data collator for causal language modeling."""
        batch_input_ids = []
        batch_attention_mask = []
        batch_labels = []

        # Find max length in this batch
        max_length = max(len(example["input_ids"]) for example in examples)
        max_length = min(max_length, args.max_context_length)

        # Ensure we have a valid pad_token_id
        pad_token_id = tokenizer.pad_token_id
        if pad_token_id is None or pad_token_id >= len(tokenizer):
            pad_token_id = tokenizer.eos_token_id
            print(f"Warning: Using eos_token_id ({pad_token_id}) as pad_token_id")

        for example in examples:
            input_ids = example["input_ids"]
            attention_mask = example["attention_mask"]

            # Validate input_ids to ensure no out-of-bounds tokens
            max_token_id = max(input_ids) if input_ids else 0
            if max_token_id >= len(tokenizer):
                print(f"Warning: Found out-of-bounds token {max_token_id} >= {len(tokenizer)}")
                # Clip out-of-bounds tokens to unk_token_id
                input_ids = [min(token_id, len(tokenizer) - 1) for token_id in input_ids]

            # Truncate if too long
            if len(input_ids) > max_length:
                input_ids = input_ids[:max_length]
                attention_mask = attention_mask[:max_length]

            # Pad if too short
            pad_length = max_length - len(input_ids)
            if pad_length > 0:
                input_ids = input_ids + [pad_token_id] * pad_length
                attention_mask = attention_mask + [0] * pad_length

            # Labels are the same as input_ids for causal LM, but shifted
            labels = input_ids.copy()
            # Mask padding tokens in labels
            labels = [label if attention_mask[i] == 1 else -100 for i, label in enumerate(labels)]

            batch_input_ids.append(input_ids)
            batch_attention_mask.append(attention_mask)
            batch_labels.append(labels)

        return {
            "input_ids": torch.tensor(batch_input_ids, dtype=torch.long),
            "attention_mask": torch.tensor(batch_attention_mask, dtype=torch.long),
            "labels": torch.tensor(batch_labels, dtype=torch.long),
        }

    # Define Huggingface Trainer
    # Check if we should report to wandb (only on main process)
    should_report_wandb = args.wandb_logging and is_main_process

    # Check if CUDA is available and set precision accordingly
    use_cuda = torch.cuda.is_available()
    print(f"CUDA available: {use_cuda}")
    if use_cuda:
        print(f"CUDA device count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  Device {i}: {torch.cuda.get_device_name(i)}")

    # Validate save_steps and eval_steps compatibility
    if val_dataset and args.save_steps % args.logging_steps != 0:
        print(f"⚠️  Warning: save_steps ({args.save_steps}) is not a multiple of logging_steps ({args.logging_steps})")
        print(f"🔧 Adjusting save_steps to be compatible...")
        # Round save_steps to the nearest multiple of logging_steps
        adjusted_save_steps = ((args.save_steps // args.logging_steps) + 1) * args.logging_steps
        print(f"📝 Changed save_steps from {args.save_steps} to {adjusted_save_steps}")
        args.save_steps = adjusted_save_steps

    # Determine optimal precision based on model and hardware
    use_bf16 = False
    use_fp16 = False

    if use_cuda:
        # Check if bfloat16 is supported
        if torch.cuda.is_bf16_supported():
            use_bf16 = True
            print("🔧 Using bfloat16 precision (optimal for modern GPUs)")
        else:
            use_fp16 = True
            print("🔧 Using float16 precision")
    else:
        print("🔧 Using float32 precision (CPU)")

    train_args = TrainingArguments(
        bf16=use_bf16,
        fp16=use_fp16,
        data_seed=args.seed,
        eval_strategy="steps" if val_dataset else "no",
        eval_steps=args.logging_steps if val_dataset else None,
        eval_accumulation_steps=args.gradient_accumulation_steps,
        gradient_checkpointing=args.gradient_checkpointing,
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        learning_rate=args.learning_rate,
        logging_steps=args.logging_steps,
        lr_scheduler_type="cosine",
        num_train_epochs=args.num_train_epochs,
        output_dir=output_dir,
        per_device_train_batch_size=args.per_device_batch_size,
        per_device_eval_batch_size=args.per_device_batch_size,
        remove_unused_columns=False,
        report_to="wandb" if should_report_wandb else None,
        seed=args.seed,
        save_strategy="steps",
        save_steps=args.save_steps,
        save_total_limit=args.save_total_limit,
        save_on_each_node=False,  # Only save on main process
        dataloader_num_workers=args.dataloader_num_workers,
        warmup_ratio=args.warmup_ratio,
        weight_decay=args.weight_decay,
        load_best_model_at_end=True if val_dataset else False,
        metric_for_best_model="eval_loss" if val_dataset else None,
        greater_is_better=False if val_dataset else None,
        save_only_model=False,
        # Add distributed training parameters
        ddp_backend=args.ddp_backend,
        ddp_find_unused_parameters=args.ddp_find_unused_parameters,
        ddp_timeout=3600,  # 60 minutes timeout
        # Memory optimization
        max_grad_norm=1.0,  # Gradient clipping for stability
        dataloader_pin_memory=False,  # Disable pin memory to save GPU memory
        dataloader_persistent_workers=False,  # Disable persistent workers for memory
    )

    # Clear cache and set memory optimization
    torch.cuda.empty_cache()

    # Initialize callbacks
    callbacks = []
    print(f"🔧 Initializing callbacks - val_dataset: {val_dataset is not None}, is_main_process: {is_main_process}")

    if val_dataset and is_main_process:
        best_model_callback = BestModelCallback(
            output_dir=output_dir,
            tokenizer=tokenizer,
            is_main_process=is_main_process
        )
        callbacks.append(best_model_callback)
        print(f"✅ Added BestModelCallback - will save best validation model to: {os.path.join(output_dir, 'best_model')}")

    trainer = Trainer(
        model=model,
        args=train_args,
        data_collator=data_collator,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        tokenizer=tokenizer,
        callbacks=callbacks,
    )

    # Begin training
    print("🚀 Starting training...")
    if args.resume_training:
        torch.cuda.empty_cache()
        train_result = trainer.train(resume_from_checkpoint=args.resume_checkpoint_path)
    else:
        train_result = trainer.train()

    # Save final model (only on main process)
    if is_main_process:
        print("💾 Saving final model...")
        trainer.save_model()
        trainer.log_metrics("train", train_result.metrics)
        trainer.save_metrics("train", train_result.metrics)
        trainer.save_state()

        # Save tokenizer
        tokenizer.save_pretrained(output_dir)

        print("✅ Training complete! Exiting...")
        print(f"📁 Model saved to: {output_dir}")
        if val_dataset:
            print(f"🏆 Best model saved to: {os.path.join(output_dir, 'best_model')}")
    else:
        print(f"Training complete on rank {local_rank}! Main process will handle model saving.")


if __name__ == "__main__":
    # Get experiment parameters
    args = get_experiment_args()

    # Set random seed
    random.seed(args.seed)
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    torch.cuda.manual_seed(args.seed)

    main(args)
