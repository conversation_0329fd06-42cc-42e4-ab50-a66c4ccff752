#!/usr/bin/env python3
"""
Quick test script for the optimized inference code.
Tests the inference with a small example to verify the changes work correctly.
"""

import os
import sys
import json
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Import the inference class directly
sys.path.insert(0, str(Path(__file__).parent))
from inference_diffusion import ProteinDiffusionInference

def test_inference():
    """Test the inference with the specified model."""
    
    # Model path as specified in requirements
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_restructured_head1000_crop/training_runs/2025-06-14_17-27-45_GPT2_Diffusion_gpt2-large/checkpoint-668"
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        print("Please verify the model path is correct.")
        return False
    
    print(f"✅ Model path exists: {model_path}")
    
    try:
        # Initialize inference
        print("🔄 Loading model...")
        inference = ProteinDiffusionInference(model_path, device="cuda")
        print("✅ Model loaded successfully!")
        
        # Test generation
        print("🔄 Testing generation...")
        result = inference.generate_reverse_process(
            max_length=512,
            temperature=0.8,
            top_p=0.9,
            max_steps=50,  # Smaller for quick test
            print_steps=True
        )
        
        print(f"\n📊 Generation Results:")
        print(f"   Success: {result['success']}")
        print(f"   Final sequence: {result['final_sequence']}")
        print(f"   Sequence length: {len(result['final_sequence'])}")
        print(f"   Number of steps: {result['num_steps']}")
        
        if result['success']:
            # Test formatting
            clean_format = inference.format_output_like_training_data(result, include_step_info=False)
            with_steps_format = inference.format_output_like_training_data(result, include_step_info=True)
            
            print(f"\n📝 Clean Format (no step info):")
            for i, line in enumerate(clean_format[:10]):  # Show first 10 lines
                print(f"   {i}: {line}")
            if len(clean_format) > 10:
                print(f"   ... and {len(clean_format) - 10} more lines")
            
            print(f"\n📝 With Steps Format:")
            for i, line in enumerate(with_steps_format[:10]):  # Show first 10 lines
                print(f"   {i}: {line}")
            if len(with_steps_format) > 10:
                print(f"   ... and {len(with_steps_format) - 10} more lines")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during inference: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Optimized Inference Script")
    print("=" * 50)
    
    success = test_inference()
    
    if success:
        print("\n✅ Test completed successfully!")
        print("The inference script is working correctly with the optimized changes.")
    else:
        print("\n❌ Test failed!")
        print("Please check the error messages above.")
