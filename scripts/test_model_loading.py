#!/usr/bin/env python3
"""
Simple test to check if the model can be loaded.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent))

def test_model_loading():
    """Test if the model can be loaded."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_restructured_head1000_crop/training_runs/2025-06-14_17-27-45_GPT2_Diffusion_gpt2-large/checkpoint-668"
    
    print(f"🔍 Checking model path: {model_path}")
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist!")
        return False
    
    print(f"✅ Model path exists")
    
    # Check required files
    required_files = ["config.json", "model.safetensors", "tokenizer.json"]
    for file in required_files:
        file_path = os.path.join(model_path, file)
        if os.path.exists(file_path):
            print(f"✅ Found {file}")
        else:
            print(f"❌ Missing {file}")
            return False
    
    try:
        print("🔄 Testing transformers import...")
        from transformers import AutoTokenizer, AutoModelForCausalLM
        print("✅ Transformers imported successfully")
        
        print("🔄 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        print(f"✅ Tokenizer loaded, vocab size: {len(tokenizer)}")
        
        print("🔄 Loading model...")
        model = AutoModelForCausalLM.from_pretrained(model_path)
        print(f"✅ Model loaded successfully")
        print(f"   Model type: {type(model).__name__}")
        print(f"   Number of parameters: {model.num_parameters():,}")
        
        # Test tokenization
        test_text = "Diffusion reverse process:\nInitial state: ''"
        print(f"🔄 Testing tokenization with: '{test_text}'")
        tokens = tokenizer(test_text, return_tensors="pt")
        print(f"✅ Tokenization successful, input_ids shape: {tokens['input_ids'].shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing Model Loading")
    print("=" * 40)
    
    success = test_model_loading()
    
    if success:
        print("\n✅ Model loading test passed!")
    else:
        print("\n❌ Model loading test failed!")
