#!/bin/bash
#SBATCH --job-name=modern_llm_diffusion_crop
#SBATCH --output=modern_llm_diffusion_crop_%j.out
#SBATCH --error=modern_llm_diffusion_crop_%j.err
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gpus-per-node=4
#SBATCH --cpus-per-task=48
#SBATCH --mem=128G
#SBATCH --time=48:00:00
#SBATCH --partition=ghx4
#SBATCH --account=bdrw-dtai-gh

# Modern LLM Training Script for Diffusion via Reasoning
# Supports multiple modern architectures with optimized configurations

# Enhanced error handling and logging
set -e  # Exit on any error
set -u  # Exit on undefined variables

# Function for logging with timestamps
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function for error handling
error_exit() {
    log "ERROR: $1"
    exit 1
}

# Clean up any existing processes
log "Cleaning up existing processes..."
pkill -f "torchrun.*train_modern_llm_diffusion.py" || true
sleep 3

# Get hostname and generate a more unique port to avoid conflicts
NODE_ID=$(echo $SLURM_NODELIST | sed 's/.*\([0-9]\+\).*/\1/')
export MASTER_ADDR=$(hostname -s)
export MASTER_PORT=$((20000 + NODE_ID * 100 + RANDOM % 100))

log "Job ID: $SLURM_JOB_ID"
log "Node: $SLURM_NODELIST"
log "Using MASTER_ADDR=$MASTER_ADDR and MASTER_PORT=$MASTER_PORT"

# Switch to the correct directory and branch
log "Switching to project directory..."
cd /u/dzhang5/Diffusion_via_Reasoning || error_exit "Failed to change to project directory"

# Checkout to the correct branch
log "Switching to daiheng-dev branch..."
git checkout daiheng-dev || error_exit "Failed to checkout daiheng-dev branch"

# Activate conda environment
log "Activating conda environment..."
source /u/dzhang5/miniforge3/etc/profile.d/conda.sh || error_exit "Failed to source conda"
conda activate diffusion_via_reasoning || error_exit "Failed to activate conda environment"

# Verify environment
log "Verifying environment..."
log "Current conda environment: $CONDA_DEFAULT_ENV"
python --version || error_exit "Python not available"
python -c "import torch; print(f'PyTorch version: {torch.__version__}')" || error_exit "PyTorch not available"
python -c "import transformers; print(f'Transformers version: {transformers.__version__}')" || error_exit "Transformers not available"

# ============================================================================
# MODEL SELECTION - Choose your preferred model here
# ============================================================================

# Option 1: GPT-2 family (baseline, fast training)
# MODEL_NAME="gpt2"                    # 124M parameters
# MODEL_NAME="gpt2-medium"             # 355M parameters  
# MODEL_NAME="gpt2-large"              # 774M parameters
# MODEL_NAME="gpt2-xl"                 # 1.5B parameters

# Option 2: Modern efficient models (recommended for quick experiments)
MODEL_NAME="microsoft/DialoGPT-medium"  # 355M parameters, optimized for dialogue
# MODEL_NAME="microsoft/DialoGPT-small"   # 117M parameters, very fast
# MODEL_NAME="distilgpt2"                 # 82M parameters, distilled GPT-2

# Option 3: Code-specialized models (good for structured generation)
# MODEL_NAME="Salesforce/codegen-350M-mono"  # 350M parameters, code-focused
# MODEL_NAME="microsoft/CodeGPT-small-py"    # Code generation model

# Option 4: Larger modern models (requires more memory, better performance)
# MODEL_NAME="mistralai/Mistral-7B-v0.1"     # 7B parameters, state-of-the-art
# MODEL_NAME="Qwen/Qwen-1_8B"                # 1.8B parameters, efficient
# MODEL_NAME="Qwen/Qwen-7B"                  # 7B parameters, very capable

# Option 5: Llama family (requires HuggingFace access token)
# MODEL_NAME="meta-llama/Llama-2-7b-hf"      # 7B parameters, very capable
# MODEL_NAME="meta-llama/Llama-2-13b-hf"     # 13B parameters, high performance

log "Selected model: $MODEL_NAME"

# ============================================================================
# TRAINING CONFIGURATION
# ============================================================================

# Data configuration
DATA_DIR="./data/CreateProtein_restructured_head1000_crop"
BASE_OUTPUT_DIR="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_modern_llm/training_runs"

# Model-specific optimized parameters
if [[ "$MODEL_NAME" == *"gpt2"* ]] || [[ "$MODEL_NAME" == *"DialoGPT"* ]] || [[ "$MODEL_NAME" == *"distilgpt2"* ]]; then
    # GPT-2 family optimizations
    BATCH_SIZE=8
    GRADIENT_ACCUMULATION_STEPS=4
    LEARNING_RATE=5e-5
    MAX_CONTEXT_LENGTH=2048
    USE_QUANTIZATION=""
    USE_LORA=""
    log "Using GPT-2 family optimizations"
    
elif [[ "$MODEL_NAME" == *"codegen"* ]] || [[ "$MODEL_NAME" == *"CodeGPT"* ]]; then
    # Code model optimizations
    BATCH_SIZE=6
    GRADIENT_ACCUMULATION_STEPS=4
    LEARNING_RATE=1e-5
    MAX_CONTEXT_LENGTH=2048
    USE_QUANTIZATION=""
    USE_LORA=""
    log "Using code model optimizations"
    
elif [[ "$MODEL_NAME" == *"Mistral"* ]] || [[ "$MODEL_NAME" == *"Llama"* ]]; then
    # Large model optimizations with quantization
    BATCH_SIZE=2
    GRADIENT_ACCUMULATION_STEPS=8
    LEARNING_RATE=2e-5
    MAX_CONTEXT_LENGTH=4096
    USE_QUANTIZATION="--use_4bit_quantization"
    USE_LORA="--use_lora --lora_rank 16 --lora_alpha 32"
    log "Using large model optimizations with 4-bit quantization and LoRA"
    
elif [[ "$MODEL_NAME" == *"Qwen"* ]]; then
    # Qwen model optimizations
    BATCH_SIZE=4
    GRADIENT_ACCUMULATION_STEPS=4
    LEARNING_RATE=3e-5
    MAX_CONTEXT_LENGTH=2048
    USE_QUANTIZATION="--use_8bit_quantization"
    USE_LORA="--use_lora --lora_rank 8 --lora_alpha 16"
    log "Using Qwen optimizations with 8-bit quantization and LoRA"
    
else
    # Default optimizations for unknown models
    BATCH_SIZE=4
    GRADIENT_ACCUMULATION_STEPS=4
    LEARNING_RATE=3e-5
    MAX_CONTEXT_LENGTH=2048
    USE_QUANTIZATION=""
    USE_LORA=""
    log "Using default optimizations for unknown model"
fi

# General training parameters
NUM_EPOCHS=50
SAVE_STEPS=100
LOGGING_STEPS=25

log "Training configuration:"
log "  Model: $MODEL_NAME"
log "  Batch size: $BATCH_SIZE"
log "  Gradient accumulation: $GRADIENT_ACCUMULATION_STEPS"
log "  Learning rate: $LEARNING_RATE"
log "  Max context length: $MAX_CONTEXT_LENGTH"
log "  Quantization: ${USE_QUANTIZATION:-None}"
log "  LoRA: ${USE_LORA:-None}"

# Create output directory if it doesn't exist
log "Creating output directory..."
mkdir -p $BASE_OUTPUT_DIR || error_exit "Failed to create output directory"

# Set environment variables for distributed training and stability
log "Setting up environment variables..."
export CUDA_VISIBLE_DEVICES=0,1,2,3
export TOKENIZERS_PARALLELISM=false
export NCCL_DEBUG=WARN
export NCCL_IB_DISABLE=1
export NCCL_P2P_DISABLE=1

# Enhanced CUDA debugging and memory management
export CUDA_LAUNCH_BLOCKING=1
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# W&B configuration
export WANDB_PROJECT="Diffusion_via_Reasoning"
export WANDB_CACHE_DIR="/u/dzhang5/Diffusion_via_Reasoning/wandb"

# Comprehensive system and data validation
log "Checking GPU availability..."
nvidia-smi || error_exit "nvidia-smi failed - GPU not available"

log "GPU memory info:"
nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv

log "CUDA version info:"
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}'); print(f'GPU count: {torch.cuda.device_count()}')" || error_exit "CUDA check failed"

# Validate data directory and contents
log "Validating data directory..."
if [ ! -d "$DATA_DIR" ]; then
    error_exit "Data directory $DATA_DIR does not exist!"
fi

log "Data directory contents:"
ls -la $DATA_DIR/

# Check for required data files
for file in "train_data_restructured.json" "val_data_restructured.json" "test_data_restructured.json"; do
    if [ ! -f "$DATA_DIR/$file" ]; then
        error_exit "Required data file $DATA_DIR/$file not found!"
    fi
done

log "Data validation successful - all required files found"

# Display dataset statistics
log "Dataset statistics:"
python -c "
import json
with open('$DATA_DIR/data_stats_restructured.json', 'r') as f:
    stats = json.load(f)
print(f'Train samples: {stats[\"train_count\"]}')
print(f'Validation samples: {stats[\"val_count\"]}')
print(f'Test samples: {stats[\"test_count\"]}')
print(f'Average steps: {stats[\"avg_steps\"]:.1f}')
print(f'Max steps: {stats[\"max_steps\"]}')
print(f'Min steps: {stats[\"min_steps\"]}')
" || log "Warning: Could not read dataset statistics"

# Pre-training validation test
log "Running pre-training validation..."
python -c "
from scripts.train_modern_llm_diffusion import load_diffusion_data, format_diffusion_training_text, get_model_config
from transformers import AutoTokenizer
import argparse

# Create minimal args for testing
class Args:
    def __init__(self):
        self.trust_remote_code = True
        self.use_flash_attention = False
        self.use_4bit_quantization = False
        self.use_8bit_quantization = False

args = Args()

# Test data loading
data_splits = load_diffusion_data('$DATA_DIR', max_samples=3)
print(f'Successfully loaded {len(data_splits[\"train\"])} train samples')

# Test model config
config = get_model_config('$MODEL_NAME', args)
print(f'Model config: {config}')

# Test tokenizer setup
try:
    tokenizer = AutoTokenizer.from_pretrained('$MODEL_NAME', trust_remote_code=config['trust_remote_code'])
    if tokenizer.pad_token is None:
        if tokenizer.eos_token is not None:
            tokenizer.pad_token = tokenizer.eos_token
        else:
            tokenizer.add_special_tokens({'pad_token': '[PAD]'})
    print(f'Tokenizer vocab size: {len(tokenizer)}')
    print(f'pad_token_id: {tokenizer.pad_token_id}')
    
    # Test sample formatting and tokenization
    sample = data_splits['train'][0]
    text = format_diffusion_training_text(sample['reverse_process'])
    tokens = tokenizer(text, truncation=True, max_length=$MAX_CONTEXT_LENGTH)
    print(f'Sample token length: {len(tokens[\"input_ids\"])}')
    print('Pre-training validation successful!')
except Exception as e:
    print(f'Warning: Tokenizer test failed: {e}')
    print('This might be normal for some models that require authentication.')
" || log "Warning: Pre-training validation had issues (might be normal for some models)"

# Start training with enhanced monitoring
log "Starting modern LLM diffusion training..."
log "Training configuration summary:"
log "  - Model: $MODEL_NAME"
log "  - Data: $DATA_DIR"
log "  - Output: $BASE_OUTPUT_DIR"
log "  - Batch size: $BATCH_SIZE (per device)"
log "  - Gradient accumulation: $GRADIENT_ACCUMULATION_STEPS"
log "  - Effective batch size: $((BATCH_SIZE * GRADIENT_ACCUMULATION_STEPS * 4))"
log "  - Learning rate: $LEARNING_RATE"
log "  - Max context length: $MAX_CONTEXT_LENGTH"
log "  - Epochs: $NUM_EPOCHS"
log "  - Save frequency: every $SAVE_STEPS steps"
log "  - Log frequency: every $LOGGING_STEPS steps"

# Build training command
TRAINING_CMD="torchrun \
    --standalone \
    --nproc_per_node=4 \
    --master_addr=$MASTER_ADDR \
    --master_port=$MASTER_PORT \
    scripts/train_modern_llm_diffusion.py \
    --data_dir $DATA_DIR \
    --base_output_dir $BASE_OUTPUT_DIR \
    --llm_model_name_or_path $MODEL_NAME \
    --per_device_batch_size $BATCH_SIZE \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION_STEPS \
    --learning_rate $LEARNING_RATE \
    --num_train_epochs $NUM_EPOCHS \
    --max_context_length $MAX_CONTEXT_LENGTH \
    --save_steps $SAVE_STEPS \
    --logging_steps $LOGGING_STEPS \
    --wandb_logging \
    --gradient_checkpointing \
    --ddp_backend nccl \
    --dataloader_num_workers 2 \
    --save_total_limit 5 \
    --trust_remote_code"

# Add quantization and LoRA flags if specified
if [ -n "$USE_QUANTIZATION" ]; then
    TRAINING_CMD="$TRAINING_CMD $USE_QUANTIZATION"
    log "  - Quantization: enabled"
fi

if [ -n "$USE_LORA" ]; then
    TRAINING_CMD="$TRAINING_CMD $USE_LORA"
    log "  - LoRA: enabled"
fi

# Run training with comprehensive error handling
set +e  # Don't exit on training errors, handle them gracefully
log "Executing training command..."
eval $TRAINING_CMD

TRAINING_EXIT_CODE=$?
set -e  # Re-enable exit on error

# Post-training analysis and cleanup
if [ $TRAINING_EXIT_CODE -eq 0 ]; then
    log "Training completed successfully!"

    # Find the most recent training run
    LATEST_RUN=$(find $BASE_OUTPUT_DIR -maxdepth 1 -type d -name "*ModernLLM_Diffusion*" | sort | tail -1)

    if [ -n "$LATEST_RUN" ]; then
        log "Training output saved to: $LATEST_RUN"

        # Display training results
        log "Training run contents:"
        ls -la "$LATEST_RUN/"

        # Check for best model
        if [ -d "$LATEST_RUN/best_model" ]; then
            log "Best model saved to: $LATEST_RUN/best_model"
            ls -la "$LATEST_RUN/best_model/"
        fi

        # Display training metrics if available
        if [ -f "$LATEST_RUN/trainer_state.json" ]; then
            log "Final training metrics:"
            python -c "
import json
try:
    with open('$LATEST_RUN/trainer_state.json', 'r') as f:
        state = json.load(f)
    print(f'Total training steps: {state.get(\"global_step\", \"N/A\")}')
    print(f'Best metric: {state.get(\"best_metric\", \"N/A\")}')
    if 'log_history' in state and state['log_history']:
        last_log = state['log_history'][-1]
        print(f'Final train loss: {last_log.get(\"train_loss\", \"N/A\")}')
        print(f'Final eval loss: {last_log.get(\"eval_loss\", \"N/A\")}')
except Exception as e:
    print(f'Could not read training metrics: {e}')
            " || log "Could not parse training metrics"
        fi

        # Save job completion info
        echo "Job completed successfully at $(date)" > "$LATEST_RUN/job_completion.txt"
        echo "SLURM Job ID: $SLURM_JOB_ID" >> "$LATEST_RUN/job_completion.txt"
        echo "Node: $SLURM_NODELIST" >> "$LATEST_RUN/job_completion.txt"
        echo "Model: $MODEL_NAME" >> "$LATEST_RUN/job_completion.txt"
        echo "Quantization: ${USE_QUANTIZATION:-None}" >> "$LATEST_RUN/job_completion.txt"
        echo "LoRA: ${USE_LORA:-None}" >> "$LATEST_RUN/job_completion.txt"

        # Create a summary of the training configuration
        cat > "$LATEST_RUN/training_summary.md" << EOF
# Training Summary

## Model Configuration
- **Model**: $MODEL_NAME
- **Quantization**: ${USE_QUANTIZATION:-None}
- **LoRA**: ${USE_LORA:-None}

## Training Parameters
- **Batch Size**: $BATCH_SIZE (per device)
- **Gradient Accumulation**: $GRADIENT_ACCUMULATION_STEPS
- **Effective Batch Size**: $((BATCH_SIZE * GRADIENT_ACCUMULATION_STEPS * 4))
- **Learning Rate**: $LEARNING_RATE
- **Max Context Length**: $MAX_CONTEXT_LENGTH
- **Epochs**: $NUM_EPOCHS

## Data
- **Dataset**: $DATA_DIR
- **Training Samples**: $(python -c "import json; print(json.load(open('$DATA_DIR/data_stats_restructured.json'))['train_count'])" 2>/dev/null || echo "Unknown")
- **Validation Samples**: $(python -c "import json; print(json.load(open('$DATA_DIR/data_stats_restructured.json'))['val_count'])" 2>/dev/null || echo "Unknown")

## Job Information
- **Job ID**: $SLURM_JOB_ID
- **Node**: $SLURM_NODELIST
- **Completion Time**: $(date)
EOF

        log "Training summary saved to: $LATEST_RUN/training_summary.md"

    else
        log "Warning: Could not find training output directory"
    fi

else
    log "Training failed with exit code: $TRAINING_EXIT_CODE"
    log "Check the error logs above for details"

    # Save error info
    ERROR_LOG="$BASE_OUTPUT_DIR/training_error_$(date +%Y%m%d_%H%M%S).log"
    echo "Training failed at $(date)" > "$ERROR_LOG"
    echo "SLURM Job ID: $SLURM_JOB_ID" >> "$ERROR_LOG"
    echo "Exit code: $TRAINING_EXIT_CODE" >> "$ERROR_LOG"
    echo "Node: $SLURM_NODELIST" >> "$ERROR_LOG"
    echo "Model: $MODEL_NAME" >> "$ERROR_LOG"
    echo "Command: $TRAINING_CMD" >> "$ERROR_LOG"

    log "Error log saved to: $ERROR_LOG"
    exit $TRAINING_EXIT_CODE
fi

log "Job completed at $(date)"
log "Total job runtime: $SECONDS seconds"
