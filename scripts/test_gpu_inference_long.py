#!/usr/bin/env python3
"""
GPU-optimized inference testing for longer protein sequences.
Tests the model's capability to generate realistic protein lengths efficiently.
"""

import os
import sys
import json
import time
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent))

from inference_diffusion import ProteinDiffusionInference

def test_gpu_inference_long():
    """Test GPU inference with longer sequence generation."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_restructured_head1000_crop/training_runs/2025-06-14_17-27-45_GPT2_Diffusion_gpt2-large/checkpoint-668"
    
    print("🧪 GPU-Optimized Inference Test for Longer Protein Sequences")
    print("=" * 70)
    
    # Check model path
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return False
    
    try:
        # Initialize inference with GPU optimization
        print("🔄 Loading model with GPU optimization...")
        start_time = time.time()
        inference = ProteinDiffusionInference(model_path, device="cuda")
        load_time = time.time() - start_time
        print(f"✅ Model loaded in {load_time:.2f} seconds")
        
        # Test parameters for longer sequences
        test_configs = [
            {
                "name": "Medium Length",
                "max_length": 1024,
                "max_steps": 200,
                "num_sequences": 3
            },
            {
                "name": "Long Length", 
                "max_length": 2048,
                "max_steps": 250,
                "num_sequences": 2
            }
        ]
        
        all_results = []
        
        for config in test_configs:
            print(f"\n🔬 Testing {config['name']} Generation:")
            print(f"   Parameters: max_length={config['max_length']}, max_steps={config['max_steps']}")
            
            start_time = time.time()
            results = inference.generate_multiple_sequences(
                num_sequences=config['num_sequences'],
                max_length=config['max_length'],
                temperature=0.8,
                top_p=0.9,
                max_steps=config['max_steps'],
                print_steps=False  # Reduce output for cleaner testing
            )
            generation_time = time.time() - start_time
            
            # Analyze results
            successful = [r for r in results if r["success"]]
            print(f"   ⏱️  Generation time: {generation_time:.2f} seconds")
            print(f"   ✅ Success rate: {len(successful)}/{len(results)} ({len(successful)/len(results)*100:.1f}%)")
            
            if successful:
                lengths = [len(r["final_sequence"]) for r in successful]
                steps = [r["num_steps"] for r in successful]
                
                print(f"   📏 Sequence lengths: {lengths}")
                print(f"   📊 Average length: {sum(lengths)/len(lengths):.1f}")
                print(f"   🔄 Average steps: {sum(steps)/len(steps):.1f}")
                
                # Show biological validation for each sequence
                for i, result in enumerate(successful):
                    bio_val = inference.validate_sequence_biology(result["final_sequence"])
                    print(f"   🧬 Sequence {i+1}: {bio_val['biological_plausibility']} "
                          f"(len={bio_val['length']}, valid_aa={bio_val['valid_amino_acids']})")
                    
                    # Show a sample of the sequence
                    seq = result["final_sequence"]
                    if len(seq) > 60:
                        print(f"      Sample: {seq[:30]}...{seq[-30:]}")
                    else:
                        print(f"      Sequence: {seq}")
            
            all_results.extend(results)
        
        # Save comprehensive results
        output_file = "gpu_inference_test_results.json"
        with open(output_file, 'w') as f:
            json.dump({
                "test_timestamp": time.time(),
                "model_path": model_path,
                "total_sequences": len(all_results),
                "successful_sequences": sum(1 for r in all_results if r["success"]),
                "test_configs": test_configs,
                "results": all_results
            }, f, indent=2)
        
        print(f"\n💾 Detailed results saved to: {output_file}")
        
        # Final summary
        total_successful = sum(1 for r in all_results if r["success"])
        print(f"\n📊 Final Summary:")
        print(f"   Total sequences generated: {len(all_results)}")
        print(f"   Overall success rate: {total_successful}/{len(all_results)} ({total_successful/len(all_results)*100:.1f}%)")
        
        if total_successful > 0:
            successful_results = [r for r in all_results if r["success"]]
            all_lengths = [len(r["final_sequence"]) for r in successful_results]
            all_steps = [r["num_steps"] for r in successful_results]
            
            print(f"   Length range: {min(all_lengths)}-{max(all_lengths)} amino acids")
            print(f"   Average length: {sum(all_lengths)/len(all_lengths):.1f}")
            print(f"   Average steps: {sum(all_steps)/len(all_steps):.1f}")
            
            # Biological plausibility summary
            bio_results = [inference.validate_sequence_biology(r["final_sequence"]) for r in successful_results]
            plausible = sum(1 for b in bio_results if b["biological_plausibility"] == "plausible")
            valid_aa = sum(1 for b in bio_results if b["valid_amino_acids"])
            
            print(f"   Biologically plausible: {plausible}/{total_successful} ({plausible/total_successful*100:.1f}%)")
            print(f"   Valid amino acids: {valid_aa}/{total_successful} ({valid_aa/total_successful*100:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during GPU inference test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gpu_inference_long()
    
    if success:
        print("\n🎉 GPU inference test completed successfully!")
        print("The model can generate longer protein sequences efficiently on GPU.")
    else:
        print("\n💥 GPU inference test failed!")
        print("Please check the error messages above.")
