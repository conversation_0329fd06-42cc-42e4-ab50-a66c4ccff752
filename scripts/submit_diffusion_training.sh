#!/bin/bash
#SBATCH --job-name=gpt2_diffusion_truly_uniform_crop
#SBATCH --output=gpt2_diffusion_truly_uniform_crop_%j.out
#SBATCH --error=gpt2_diffusion_truly_uniform_crop_%j.err
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gpus-per-node=4
#SBATCH --cpus-per-task=4
#SBATCH --mem=32G
#SBATCH --time=12:00:00
#SBATCH --partition=ghx4
#SBATCH --account=bdrx-dtai-gh

# Production-ready GPT-2 Diffusion Training Script
# Incorporates all optimizations and fixes for ultra-long sequence training
# - Position embedding extension for sequences up to 15000 tokens
# - Tokenizer boundary checking and pad_token_id fixes
# - Optimized batch sizes and memory usage for ultra-long sequences
# - Enhanced error handling and logging
# - Best model callback system for automatic best checkpoint saving
# - I/O optimization to prevent storage-related training interruptions

# Enhanced error handling and logging
set -e  # Exit on any error
set -u  # Exit on undefined variables

# Function for logging with timestamps
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function for error handling
error_exit() {
    log "ERROR: $1"
    exit 1
}

# Clean up any existing processes
log "Cleaning up existing processes..."
pkill -f "torchrun.*train_gpt2_diffusion.py" || true
sleep 3

# Get hostname and generate a more unique port to avoid conflicts
NODE_ID=$(echo $SLURM_NODELIST | sed 's/.*\([0-9]\+\).*/\1/')
export MASTER_ADDR=$(hostname -s)
export MASTER_PORT=$((20000 + NODE_ID * 100 + RANDOM % 100))

log "Job ID: $SLURM_JOB_ID"
log "Node: $SLURM_NODELIST"
log "Using MASTER_ADDR=$MASTER_ADDR and MASTER_PORT=$MASTER_PORT"

# Switch to the correct directory and branch
log "Switching to project directory..."
cd /u/dzhang5/Diffusion_via_Reasoning || error_exit "Failed to change to project directory"

# Checkout to the correct branch
log "Switching to daiheng-dev branch..."
git checkout daiheng-dev || error_exit "Failed to checkout daiheng-dev branch"

# Activate conda environment
log "Activating conda environment..."
source /u/dzhang5/miniforge3/etc/profile.d/conda.sh || error_exit "Failed to source conda"
conda activate diffusion_via_reasoning || error_exit "Failed to activate conda environment"

# Verify environment
log "Verifying environment..."
log "Current conda environment: $CONDA_DEFAULT_ENV"
python --version || error_exit "Python not available"
python -c "import torch; print(f'PyTorch version: {torch.__version__}')" || error_exit "PyTorch not available"
python -c "import transformers; print(f'Transformers version: {transformers.__version__}')" || error_exit "Transformers not available"

# Optimized training parameters incorporating all fixes and latest optimizations
log "Setting up training parameters..."
DATA_DIR="./data/CreateProtein_truly_uniform_crop"
BASE_OUTPUT_DIR="/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_truly_uniform_crop/training_runs"  # Updated path
MODEL_NAME="gpt2-large"  # 或 "gpt2-xl"

# Latest optimized parameters for ultra-long sequences (up to 15k tokens)
BATCH_SIZE=1                    # Minimal batch size for ultra-long sequences (15k tokens)
GRADIENT_ACCUMULATION_STEPS=4   # Balanced gradient accumulation for effective training
LEARNING_RATE=2e-5              # Stable learning rate for long sequence training
NUM_EPOCHS=200                   # Appropriate epochs for dataset size
MAX_CONTEXT_LENGTH=8192       # Extended context length for full sequence support
SAVE_STEPS=4                  # Must be multiple of LOGGING_STEPS for load_best_model_at_end
LOGGING_STEPS=2                 # Balanced logging frequency for monitoring


# Create output directory if it doesn't exist
log "Creating output directory..."
mkdir -p $BASE_OUTPUT_DIR || error_exit "Failed to create output directory"

# Set environment variables for distributed training and stability
log "Setting up environment variables..."
export CUDA_VISIBLE_DEVICES=0,1,2,3
export TOKENIZERS_PARALLELISM=false
export NCCL_DEBUG=WARN
export NCCL_IB_DISABLE=1
export NCCL_P2P_DISABLE=1

# Enhanced CUDA debugging and memory management
export CUDA_LAUNCH_BLOCKING=1
export TORCH_USE_CUDA_DSA=1
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Disable problematic features for long sequences
export TORCH_CUDNN_ENABLED=false
export PYTORCH_DISABLE_CUDNN_ATTENTION=1

# W&B configuration
export WANDB_PROJECT="Diffusion_via_Reasoning"
export WANDB_CACHE_DIR="/u/dzhang5/Diffusion_via_Reasoning/wandb"

# Comprehensive system and data validation
log "Checking GPU availability..."
nvidia-smi || error_exit "nvidia-smi failed - GPU not available"

log "GPU memory info:"
nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv

log "CUDA version info:"
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}'); print(f'GPU count: {torch.cuda.device_count()}')" || error_exit "CUDA check failed"

# Validate data directory and contents
log "Validating data directory..."
if [ ! -d "$DATA_DIR" ]; then
    error_exit "Data directory $DATA_DIR does not exist!"
fi

log "Data directory contents:"
ls -la $DATA_DIR/

# Check for required data files
for file in "train_data_restructured.json" "val_data_restructured.json" "test_data_restructured.json"; do
    if [ ! -f "$DATA_DIR/$file" ]; then
        error_exit "Required data file $DATA_DIR/$file not found!"
    fi
done

log "Data validation successful - all required files found"

# Display dataset statistics
log "Dataset statistics:"
python -c "
import json
with open('$DATA_DIR/data_stats_restructured.json', 'r') as f:
    stats = json.load(f)
print(f'Train samples: {stats[\"train_count\"]}')
print(f'Validation samples: {stats[\"val_count\"]}')
print(f'Test samples: {stats[\"test_count\"]}')
print(f'Average steps: {stats[\"avg_steps\"]:.1f}')
print(f'Max steps: {stats[\"max_steps\"]}')
print(f'Min steps: {stats[\"min_steps\"]}')
" || log "Warning: Could not read dataset statistics"

# Pre-training validation test
log "Running pre-training validation..."
python -c "
from scripts.train_gpt2_diffusion import load_diffusion_data, format_diffusion_training_text
from transformers import AutoTokenizer

# Test data loading
data_splits = load_diffusion_data('$DATA_DIR', max_samples=3)
print(f'Successfully loaded {len(data_splits[\"train\"])} train samples')

# Test tokenizer setup
tokenizer = AutoTokenizer.from_pretrained('$MODEL_NAME')
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token
print(f'Tokenizer vocab size: {len(tokenizer)}')
print(f'pad_token_id: {tokenizer.pad_token_id}')

# Test sample formatting and tokenization
sample = data_splits['train'][0]
text = format_diffusion_training_text(sample['reverse_process'])
tokens = tokenizer(text, truncation=True, max_length=$MAX_CONTEXT_LENGTH)
print(f'Sample token length: {len(tokens[\"input_ids\"])}')
print('Pre-training validation successful!')
" || error_exit "Pre-training validation failed"

# Start training with enhanced monitoring
log "Starting ultra-optimized GPT-2 diffusion training..."
log "Training incorporates the following latest optimizations:"
log "  - Position embedding extension for sequences up to $MAX_CONTEXT_LENGTH tokens"
log "  - Tokenizer boundary checking and pad_token_id validation"
log "  - Ultra-small batch sizes (batch_size=$BATCH_SIZE) for 15k token sequences"
log "  - Enhanced error handling and memory management"
log "  - Best model callback system for automatic checkpoint saving"
log "  - I/O optimized save frequency (save_steps=$SAVE_STEPS, logging_steps=$LOGGING_STEPS)"
log "  - Attention mask extension for ultra-long sequences"

# Run training with comprehensive error handling
set +e  # Don't exit on training errors, handle them gracefully
torchrun \
    --standalone \
    --nproc_per_node=4 \
    --master_addr=$MASTER_ADDR \
    --master_port=$MASTER_PORT \
    scripts/train_gpt2_diffusion.py \
    --data_dir $DATA_DIR \
    --base_output_dir $BASE_OUTPUT_DIR \
    --llm_model_name_or_path $MODEL_NAME \
    --per_device_batch_size $BATCH_SIZE \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION_STEPS \
    --learning_rate $LEARNING_RATE \
    --num_train_epochs $NUM_EPOCHS \
    --max_context_length $MAX_CONTEXT_LENGTH \
    --save_steps $SAVE_STEPS \
    --logging_steps $LOGGING_STEPS \
    --wandb_logging \
    --gradient_checkpointing \
    --ddp_backend gloo \
    --dataloader_num_workers 2 \
    --save_total_limit 10

TRAINING_EXIT_CODE=$?
set -e  # Re-enable exit on error

# Post-training analysis and cleanup
if [ $TRAINING_EXIT_CODE -eq 0 ]; then
    log "Training completed successfully!"

    # Find the most recent training run
    LATEST_RUN=$(find $BASE_OUTPUT_DIR -maxdepth 1 -type d -name "*GPT2_Diffusion*" | sort | tail -1)

    if [ -n "$LATEST_RUN" ]; then
        log "Training output saved to: $LATEST_RUN"

        # Display training results
        log "Training run contents:"
        ls -la "$LATEST_RUN/"

        # Check for best model
        if [ -d "$LATEST_RUN/best_model" ]; then
            log "Best model saved to: $LATEST_RUN/best_model"
            ls -la "$LATEST_RUN/best_model/"
        fi

        # Display training metrics if available
        if [ -f "$LATEST_RUN/trainer_state.json" ]; then
            log "Final training metrics:"
            python -c "
import json
try:
    with open('$LATEST_RUN/trainer_state.json', 'r') as f:
        state = json.load(f)
    print(f'Total training steps: {state.get(\"global_step\", \"N/A\")}')
    print(f'Best metric: {state.get(\"best_metric\", \"N/A\")}')
    if 'log_history' in state and state['log_history']:
        last_log = state['log_history'][-1]
        print(f'Final train loss: {last_log.get(\"train_loss\", \"N/A\")}')
        print(f'Final eval loss: {last_log.get(\"eval_loss\", \"N/A\")}')
except Exception as e:
    print(f'Could not read training metrics: {e}')
            " || log "Could not parse training metrics"
        fi

        # Save job completion info
        echo "Job completed successfully at $(date)" > "$LATEST_RUN/job_completion.txt"
        echo "SLURM Job ID: $SLURM_JOB_ID" >> "$LATEST_RUN/job_completion.txt"
        echo "Node: $SLURM_NODELIST" >> "$LATEST_RUN/job_completion.txt"

    else
        log "Warning: Could not find training output directory"
    fi

else
    log "Training failed with exit code: $TRAINING_EXIT_CODE"
    log "Check the error logs above for details"

    # Save error info
    ERROR_LOG="$BASE_OUTPUT_DIR/training_error_$(date +%Y%m%d_%H%M%S).log"
    echo "Training failed at $(date)" > "$ERROR_LOG"
    echo "SLURM Job ID: $SLURM_JOB_ID" >> "$ERROR_LOG"
    echo "Exit code: $TRAINING_EXIT_CODE" >> "$ERROR_LOG"
    echo "Node: $SLURM_NODELIST" >> "$ERROR_LOG"

    log "Error log saved to: $ERROR_LOG"
    exit $TRAINING_EXIT_CODE
fi

log "Job completed at $(date)"
log "Total job runtime: $SECONDS seconds"
