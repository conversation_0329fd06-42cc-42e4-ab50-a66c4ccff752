#!/usr/bin/env python3
"""
Test script to validate modern LLM setup and configurations.
Run this before submitting training jobs to catch issues early.
"""

import os
import sys
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
import json
from pathlib import Path

def test_model_loading(model_name: str, use_quantization: bool = False):
    """Test if a model can be loaded successfully."""
    print(f"\n🧪 Testing model: {model_name}")
    
    try:
        # Test tokenizer loading
        print("  📝 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name, 
            trust_remote_code=True
        )
        
        # Setup pad token
        if tokenizer.pad_token is None:
            if tokenizer.eos_token is not None:
                tokenizer.pad_token = tokenizer.eos_token
            else:
                tokenizer.add_special_tokens({'pad_token': '[PAD]'})
        
        print(f"    ✅ Tokenizer loaded (vocab size: {len(tokenizer)})")
        
        # Test model loading
        print("  🤖 Loading model...")
        
        model_kwargs = {
            "pretrained_model_name_or_path": model_name,
            "trust_remote_code": True,
            "torch_dtype": torch.float16 if torch.cuda.is_available() else torch.float32,
        }
        
        if use_quantization and torch.cuda.is_available():
            try:
                from transformers import BitsAndBytesConfig
                model_kwargs["quantization_config"] = BitsAndBytesConfig(
                    load_in_4bit=True,
                    bnb_4bit_compute_dtype=torch.float16,
                )
                print("    🔧 Using 4-bit quantization")
            except ImportError:
                print("    ⚠️  BitsAndBytesConfig not available, skipping quantization")
        
        model = AutoModelForCausalLM.from_pretrained(**model_kwargs)
        
        # Get model info
        param_count = sum(p.numel() for p in model.parameters())
        param_count_str = f"{param_count/1e6:.1f}M" if param_count < 1e9 else f"{param_count/1e9:.1f}B"
        
        print(f"    ✅ Model loaded ({param_count_str} parameters)")
        
        # Test tokenization
        test_text = "Diffusion reverse process:\nStep 1: add M at position 0\nStep 2: add S at position 1"
        tokens = tokenizer(test_text, return_tensors="pt", truncation=True, max_length=512)
        print(f"    ✅ Tokenization test passed (tokens: {tokens['input_ids'].shape[1]})")
        
        # Test forward pass (small)
        if torch.cuda.is_available():
            model = model.cuda()
            tokens = {k: v.cuda() for k, v in tokens.items()}
        
        with torch.no_grad():
            outputs = model(**tokens)
        
        print(f"    ✅ Forward pass test passed (loss: {outputs.loss.item():.4f})")
        
        # Cleanup
        del model
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        return True, None
        
    except Exception as e:
        print(f"    ❌ Error: {str(e)}")
        return False, str(e)


def test_data_loading(data_dir: str):
    """Test if data can be loaded successfully."""
    print(f"\n📁 Testing data loading from: {data_dir}")
    
    try:
        # Check if directory exists
        if not os.path.exists(data_dir):
            print(f"    ❌ Data directory not found: {data_dir}")
            return False
        
        # Check required files
        required_files = ["train_data_restructured.json", "val_data_restructured.json", "data_stats_restructured.json"]
        for file in required_files:
            file_path = os.path.join(data_dir, file)
            if not os.path.exists(file_path):
                print(f"    ❌ Required file not found: {file}")
                return False
            print(f"    ✅ Found: {file}")
        
        # Load and validate data
        with open(os.path.join(data_dir, "train_data_restructured.json"), 'r') as f:
            train_data = json.load(f)
        
        with open(os.path.join(data_dir, "data_stats_restructured.json"), 'r') as f:
            stats = json.load(f)
        
        print(f"    ✅ Train samples: {len(train_data)}")
        print(f"    ✅ Average steps: {stats.get('avg_steps', 'N/A')}")
        print(f"    ✅ Max steps: {stats.get('max_steps', 'N/A')}")
        
        # Test sample format
        if train_data and 'reverse_process' in train_data[0]:
            sample_length = len(train_data[0]['reverse_process'])
            print(f"    ✅ Sample format valid (length: {sample_length})")
        else:
            print(f"    ❌ Invalid sample format")
            return False
        
        return True
        
    except Exception as e:
        print(f"    ❌ Error loading data: {str(e)}")
        return False


def test_dependencies():
    """Test if all required dependencies are available."""
    print("\n📦 Testing dependencies...")
    
    dependencies = [
        ("torch", "PyTorch"),
        ("transformers", "Transformers"),
        ("datasets", "Datasets"),
        ("numpy", "NumPy"),
        ("wandb", "Weights & Biases"),
    ]
    
    optional_dependencies = [
        ("peft", "PEFT (for LoRA)"),
        ("bitsandbytes", "BitsAndBytes (for quantization)"),
    ]
    
    all_good = True
    
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"    ✅ {name}")
        except ImportError:
            print(f"    ❌ {name} - REQUIRED")
            all_good = False
    
    for module, name in optional_dependencies:
        try:
            __import__(module)
            print(f"    ✅ {name}")
        except ImportError:
            print(f"    ⚠️  {name} - OPTIONAL")
    
    return all_good


def main():
    print("🔍 Modern LLM Setup Validation")
    print("=" * 50)
    
    # Test dependencies
    deps_ok = test_dependencies()
    if not deps_ok:
        print("\n❌ Missing required dependencies. Please install them first.")
        return False
    
    # Test CUDA
    print(f"\n🖥️  CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"    GPU count: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"    GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # Test data loading
    data_dirs = [
        "./data/CreateProtein_restructured_head1000_crop",
        "./data/CreateProtein_restructured_head1000",
        "./data/CreateProtein_restructured"
    ]
    
    data_ok = False
    for data_dir in data_dirs:
        if test_data_loading(data_dir):
            data_ok = True
            break
    
    if not data_ok:
        print("\n❌ No valid data directory found. Please check your data setup.")
        return False
    
    # Test model loading
    test_models = [
        ("distilgpt2", False),  # Small model, no quantization
        ("microsoft/DialoGPT-small", False),  # Small model
        ("gpt2", False),  # Standard GPT-2
    ]
    
    # Add larger models if CUDA is available
    if torch.cuda.is_available():
        test_models.extend([
            ("microsoft/DialoGPT-medium", False),
            ("microsoft/DialoGPT-medium", True),  # Test with quantization
        ])
    
    model_results = []
    for model_name, use_quant in test_models:
        success, error = test_model_loading(model_name, use_quant)
        model_results.append((model_name, use_quant, success, error))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    print(f"Dependencies: {'✅ PASS' if deps_ok else '❌ FAIL'}")
    print(f"Data loading: {'✅ PASS' if data_ok else '❌ FAIL'}")
    
    successful_models = [r for r in model_results if r[2]]
    print(f"Model loading: {len(successful_models)}/{len(model_results)} models successful")
    
    if successful_models:
        print("\n✅ Successfully tested models:")
        for model_name, use_quant, success, _ in successful_models:
            quant_str = " (quantized)" if use_quant else ""
            print(f"  - {model_name}{quant_str}")
    
    failed_models = [r for r in model_results if not r[2]]
    if failed_models:
        print("\n❌ Failed models:")
        for model_name, use_quant, success, error in failed_models:
            quant_str = " (quantized)" if use_quant else ""
            print(f"  - {model_name}{quant_str}: {error}")
    
    overall_success = deps_ok and data_ok and len(successful_models) > 0
    
    if overall_success:
        print(f"\n🎉 VALIDATION PASSED! You can proceed with training.")
        print(f"💡 Recommended starting model: {successful_models[0][0]}")
    else:
        print(f"\n❌ VALIDATION FAILED! Please fix the issues above before training.")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
