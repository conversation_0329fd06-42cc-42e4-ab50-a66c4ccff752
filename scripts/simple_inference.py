#!/usr/bin/env python3
"""
Simplified inference script to avoid hanging issues.
"""

import os
import sys
import json
import time
import argparse

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_path", required=True)
    parser.add_argument("--output_file", default="simple_inference_results.json")
    parser.add_argument("--device", default="cuda")
    parser.add_argument("--max_length", type=int, default=128)
    args = parser.parse_args()
    
    print(f"🚀 Simple Inference Test")
    print(f"Model: {args.model_path}")
    print(f"Device: {args.device}")
    print(f"Max length: {args.max_length}")
    
    try:
        # Import with timeout protection
        print("📦 Importing libraries...")
        import torch
        from transformers import AutoTokenizer, AutoModelForCausalLM
        print("✅ Libraries imported")
        
        # Check device
        if args.device == "cuda" and not torch.cuda.is_available():
            print("⚠️  CUDA not available, using CPU")
            args.device = "cpu"
        
        # Load tokenizer
        print("🔄 Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(args.model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        print("✅ Tokenizer loaded")
        
        # Load model
        print("🔄 Loading model...")
        start_time = time.time()
        
        if args.device == "cuda":
            model = AutoModelForCausalLM.from_pretrained(
                args.model_path,
                torch_dtype=torch.float16,
                low_cpu_mem_usage=True
            ).to(args.device)
        else:
            model = AutoModelForCausalLM.from_pretrained(args.model_path).to(args.device)
        
        model.eval()
        load_time = time.time() - start_time
        print(f"✅ Model loaded in {load_time:.2f} seconds")
        
        # Sequential generation test with guided prompt
        print("🔄 Testing sequential generation...")
        # Guide the model to start with position 0 for sequential building
        prompt = "Diffusion reverse process:\nInitial state: ''\nStep 1: add"
        inputs = tokenizer(prompt, return_tensors="pt").to(args.device)

        print(f"   Prompt: '{prompt}'")
        print(f"   Input tokens: {inputs['input_ids'].shape[1]}")

        start_time = time.time()
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=args.max_length,
                temperature=0.7,  # Slightly lower for more consistent sequential generation
                top_p=0.9,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                repetition_penalty=1.05,  # Slight penalty to avoid repetition
                early_stopping=True,
            )
        
        gen_time = time.time() - start_time
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        print(f"✅ Generation completed in {gen_time:.2f} seconds")
        print(f"Generated text length: {len(generated_text)} characters")
        print(f"Generated text preview: {generated_text[:200]}...")
        
        # Save results
        results = {
            "success": True,
            "model_path": args.model_path,
            "device": args.device,
            "generation_time": gen_time,
            "generated_text": generated_text,
            "text_length": len(generated_text)
        }
        
        with open(args.output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"✅ Results saved to {args.output_file}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
        # Save error results
        error_results = {
            "success": False,
            "error": str(e),
            "model_path": args.model_path,
            "device": args.device
        }
        
        with open(args.output_file, 'w') as f:
            json.dump(error_results, f, indent=2)
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
