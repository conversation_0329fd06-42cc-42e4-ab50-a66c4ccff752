#!/usr/bin/env python3
"""
Validation script to test the inference setup before running the full script.
"""

import os
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent))

def validate_setup():
    """Validate the inference setup."""
    
    print("🔍 Validating Inference Setup")
    print("=" * 40)
    
    # Check model path
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_restructured_head1000_crop/training_runs/2025-06-14_17-27-45_GPT2_Diffusion_gpt2-large/checkpoint-668"
    
    print(f"📁 Checking model path...")
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return False
    print(f"✅ Model path exists")
    
    # Check required files
    required_files = ["config.json", "model.safetensors", "tokenizer.json", "vocab.json"]
    for file in required_files:
        file_path = os.path.join(model_path, file)
        if os.path.exists(file_path):
            print(f"✅ Found {file}")
        else:
            print(f"❌ Missing {file}")
            return False
    
    # Test imports
    print(f"📦 Testing imports...")
    try:
        import torch
        print(f"✅ PyTorch imported")
        print(f"   CUDA available: {torch.cuda.is_available()}")
        
        from transformers import AutoTokenizer, AutoModelForCausalLM
        print(f"✅ Transformers imported")
        
        from inference_diffusion import ProteinDiffusionInference
        print(f"✅ Inference module imported")
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Test basic model loading
    print(f"🔄 Testing basic model loading...")
    try:
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"   Using device: {device}")
        
        # Just test tokenizer loading
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        print(f"✅ Tokenizer loaded (vocab size: {len(tokenizer)})")
        
        # Test prompt tokenization
        prompt = "Diffusion reverse process:\nInitial state: ''"
        tokens = tokenizer(prompt, return_tensors="pt")
        print(f"✅ Prompt tokenization successful (shape: {tokens['input_ids'].shape})")
        
    except Exception as e:
        print(f"❌ Model loading error: {e}")
        return False
    
    print(f"\n🎉 Setup validation completed successfully!")
    print(f"   The inference script should work correctly.")
    print(f"   You can now run: bash scripts/run_inference.sh")
    
    return True

if __name__ == "__main__":
    success = validate_setup()
    if not success:
        sys.exit(1)
