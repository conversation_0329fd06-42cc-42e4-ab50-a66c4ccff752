#!/bin/bash

# Multi-node GPU training script for GPT-2 diffusion model
# Supports both single-node and multi-node training

# Parse command line arguments
NODE_RANK=${1:-0}  # Default to node rank 0 if not specified
NNODES=${2:-1}     # Default to 1 node if not specified
MASTER_ADDR=${3:-"localhost"}  # Default to localhost if not specified

echo "Starting training with:"
echo "  NODE_RANK: $NODE_RANK"
echo "  NNODES: $NNODES"
echo "  MASTER_ADDR: $MASTER_ADDR"

# Set environment variables
export CUDA_VISIBLE_DEVICES=0,1,2,3
export MASTER_PORT=12355

# Training parameters
DATA_DIR="./data/CreateProtein_restructured_head100000"
BASE_OUTPUT_DIR="/tmp/dzhang5/Diffusion_via_Reasoning_restructured_head100000/training_runs"  # 使用本地存储避免网络I/O问题
MODEL_NAME="gpt2"
BATCH_SIZE=1   # 极小批次大小以适应长序列
GRADIENT_ACCUMULATION_STEPS=8  # 增加梯度累积以保持有效批次大小
LEARNING_RATE=2e-5  # 降低学习率以提高长序列训练稳定性
NUM_EPOCHS=500000  # 减少epoch数，因为数据集较小
MAX_CONTEXT_LENGTH=4096  # 减少到4k以避免OOM，仍然支持较长序列
SAVE_STEPS=10   # 每10步保存一次
LOGGING_STEPS=5  # 每5步记录一次日志

# Run training with torchrun for distributed training
torchrun \
    --nproc_per_node=4 \
    --nnodes=$NNODES \
    --node_rank=$NODE_RANK \
    --master_addr=$MASTER_ADDR \
    --master_port=12355 \
    scripts/train_gpt2_diffusion.py \
    --data_dir $DATA_DIR \
    --base_output_dir $BASE_OUTPUT_DIR \
    --llm_model_name_or_path $MODEL_NAME \
    --per_device_batch_size $BATCH_SIZE \
    --gradient_accumulation_steps $GRADIENT_ACCUMULATION_STEPS \
    --learning_rate $LEARNING_RATE \
    --num_train_epochs $NUM_EPOCHS \
    --max_context_length $MAX_CONTEXT_LENGTH \
    --save_steps $SAVE_STEPS \
    --logging_steps $LOGGING_STEPS \
    --wandb_logging \
    --gradient_checkpointing \
    --ddp_backend gloo \
    --dataloader_num_workers 2