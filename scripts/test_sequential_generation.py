#!/usr/bin/env python3
"""
Test script specifically for validating sequential protein generation.
"""

import os
import sys
import json
import re
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent))

def analyze_sequence_generation(generated_text):
    """Analyze the generated text for sequential position ordering."""
    
    print("🔍 Analyzing Sequential Generation")
    print("=" * 50)
    
    lines = generated_text.split('\n')
    actions = []
    positions = []
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Skip prompt lines
        if line.startswith("Diffusion reverse process:") or line.startswith("Initial state:"):
            continue
            
        # Parse action lines
        # Look for patterns like "add X at position Y", "remove X from position Y", etc.
        add_match = re.search(r"add\s+([A-Z])\s+at\s+position\s+(\d+)", line, re.IGNORECASE)
        remove_match = re.search(r"remove\s+([A-Z])\s+from\s+position\s+(\d+)", line, re.IGNORECASE)
        replace_match = re.search(r"replace\s+([A-Z])\s+at\s+position\s+(\d+)\s+with\s+([A-Z])", line, re.IGNORECASE)
        
        if add_match:
            amino_acid = add_match.group(1)
            position = int(add_match.group(2))
            actions.append(f"add {amino_acid} at position {position}")
            positions.append(position)
            
        elif remove_match:
            amino_acid = remove_match.group(1)
            position = int(remove_match.group(2))
            actions.append(f"remove {amino_acid} from position {position}")
            positions.append(position)
            
        elif replace_match:
            old_aa = replace_match.group(1)
            position = int(replace_match.group(2))
            new_aa = replace_match.group(3)
            actions.append(f"replace {old_aa} at position {position} with {new_aa}")
            positions.append(position)
    
    print(f"📊 Analysis Results:")
    print(f"   Total actions found: {len(actions)}")
    print(f"   Position range: {min(positions) if positions else 'N/A'} - {max(positions) if positions else 'N/A'}")
    
    # Check for sequential ordering
    if positions:
        is_sequential = all(positions[i] <= positions[i+1] + 5 for i in range(len(positions)-1))  # Allow some flexibility
        starts_from_zero = min(positions) <= 2  # Should start near position 0
        
        print(f"   Starts near position 0: {'✅' if starts_from_zero else '❌'} (min position: {min(positions)})")
        print(f"   Generally sequential: {'✅' if is_sequential else '❌'}")
        
        # Show first 10 actions with positions
        print(f"\n📝 First 10 actions:")
        for i, (action, pos) in enumerate(zip(actions[:10], positions[:10])):
            print(f"   {i+1:2d}. Position {pos:2d}: {action}")
        
        if len(actions) > 10:
            print(f"   ... and {len(actions) - 10} more actions")
        
        # Check for position jumps
        position_jumps = []
        for i in range(1, len(positions)):
            jump = positions[i] - positions[i-1]
            if abs(jump) > 5:  # Large position jump
                position_jumps.append((i, positions[i-1], positions[i], jump))
        
        if position_jumps:
            print(f"\n⚠️  Large position jumps detected:")
            for step, prev_pos, curr_pos, jump in position_jumps[:5]:
                print(f"   Step {step}: {prev_pos} → {curr_pos} (jump: {jump:+d})")
        else:
            print(f"\n✅ No large position jumps detected")
    
    return {
        "total_actions": len(actions),
        "positions": positions,
        "actions": actions,
        "starts_from_zero": starts_from_zero if positions else False,
        "is_sequential": is_sequential if positions else False,
        "position_range": (min(positions), max(positions)) if positions else (None, None)
    }

def test_sequential_inference():
    """Test the inference with sequential validation."""
    
    model_path = "/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_restructured_head1000_crop/training_runs/2025-06-14_17-27-45_GPT2_Diffusion_gpt2-large/checkpoint-668"
    
    print("🧬 Testing Sequential Protein Generation")
    print("=" * 50)
    
    # Check if we have a recent test result
    if os.path.exists("test_simple.json"):
        print("📁 Found existing test results, analyzing...")
        with open("test_simple.json", 'r') as f:
            data = json.load(f)
        
        if data.get("success") and "generated_text" in data:
            analysis = analyze_sequence_generation(data["generated_text"])
            
            print(f"\n📋 Sequential Generation Assessment:")
            if analysis["starts_from_zero"] and analysis["is_sequential"]:
                print(f"✅ GOOD: Generation follows sequential pattern")
            else:
                print(f"❌ ISSUE: Generation does not follow sequential pattern")
                print(f"   - Starts from position 0: {analysis['starts_from_zero']}")
                print(f"   - Sequential ordering: {analysis['is_sequential']}")
            
            return analysis
        else:
            print("❌ No valid generation results found")
            return None
    else:
        print("❌ No test results found. Please run the inference first.")
        return None

def main():
    """Main function to test and validate sequential generation."""
    
    analysis = test_sequential_inference()
    
    if analysis:
        # Save analysis results
        with open("sequential_analysis.json", 'w') as f:
            json.dump(analysis, f, indent=2)
        
        print(f"\n💾 Analysis saved to sequential_analysis.json")
        
        # Provide recommendations
        print(f"\n🔧 Recommendations:")
        if not analysis["starts_from_zero"]:
            print(f"   - Modify prompt to encourage starting from position 0")
            print(f"   - Add position validation in parsing logic")
        
        if not analysis["is_sequential"]:
            print(f"   - Implement position correction during inference")
            print(f"   - Use lower temperature for more consistent generation")
        
        if analysis["starts_from_zero"] and analysis["is_sequential"]:
            print(f"   - Current generation is good! Consider testing with longer sequences")
    
    return analysis is not None

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
