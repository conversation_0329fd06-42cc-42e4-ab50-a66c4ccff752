#!/usr/bin/env python3

# Simple inline test to verify state tracking
def apply_action(state, action_str):
    if action_str.startswith("add "):
        parts = action_str.split()
        token = parts[1]
        pos = int(parts[4])
        return state[:pos] + token + state[pos:]
    elif action_str.startswith("remove "):
        parts = action_str.split()
        pos = int(parts[4])
        if pos < len(state):
            return state[:pos] + state[pos + 1:]
        return state
    elif action_str.startswith("replace "):
        parts = action_str.split()
        pos = int(parts[4])
        new_token = parts[6]
        if pos < len(state):
            return state[:pos] + new_token + state[pos + 1:]
        return state
    return state

# Test the first few actions from the validation data
actions = [
    "add T at position 0",
    "remove T from position 0", 
    "add Z at position 0",
    "replace Z at position 0 with K",
    "remove K from position 0",
    "add M at position 0",
    "replace M at position 0 with C",
    "remove C from position 0",
    "add E at position 0",
    "replace E at position 0 with N"
]

state = ""
print("Testing first 10 actions:")
for i, action in enumerate(actions):
    new_state = apply_action(state, action)
    print(f"Step {i+1}: {action}")
    print(f"  '{state}' -> '{new_state}'")
    state = new_state

print(f"\nFinal state after 10 steps: '{state}'")
print("Expected from validation data: 'N'")
print(f"Match: {state == 'N'}")

# Test building the target sequence
print("\nTesting target sequence building:")
target = "MKLLVL"
state = ""
for i, char in enumerate(target):
    action = f"add {char} at position {i}"
    new_state = apply_action(state, action)
    print(f"Step {i+1}: {action}")
    print(f"  '{state}' -> '{new_state}'")
    state = new_state

print(f"\nFinal state: '{state}'")
print(f"Target:      '{target}'")
print(f"Match: {state == target}")
