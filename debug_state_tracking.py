#!/usr/bin/env python3
"""
Debug the state tracking issue in training data generation.
"""

import json
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

from diffusion_via_reasoning.trajectory.framework import apply_step, TrajectoryStep
from integrated_protein_training import DirectReverseConfig, DirectReverseTrajectoryGenerator


def parse_action_string(action_str):
    """Parse an action string into components."""
    if action_str.startswith("add "):
        # Format: "add X at position Y"
        parts = action_str.split()
        token = parts[1]
        position = int(parts[4])
        return "add", position, token, None
    elif action_str.startswith("remove "):
        # Format: "remove X from position Y"
        parts = action_str.split()
        token = parts[1]
        position = int(parts[4])
        return "remove", position, token, None
    elif action_str.startswith("replace "):
        # Format: "replace X at position Y with Z"
        parts = action_str.split()
        old_token = parts[1]
        position = int(parts[4])
        new_token = parts[6]
        return "replace", position, new_token, old_token
    else:
        return None, None, None, None


def apply_action_string(current_state, action_str):
    """Apply an action string to the current state."""
    action, position, token, old_token = parse_action_string(action_str)
    
    if action == "add":
        return current_state[:position] + token + current_state[position:]
    elif action == "remove":
        if position < len(current_state):
            return current_state[:position] + current_state[position + 1:]
        else:
            return current_state
    elif action == "replace":
        if position < len(current_state):
            return current_state[:position] + token + current_state[position + 1:]
        else:
            return current_state
    else:
        return current_state


def test_trajectory_generation():
    """Test trajectory generation and state tracking."""
    print("Testing trajectory generation and state tracking...")
    
    # Create a simple test case
    target = "ABCDE"
    charset = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    
    config = DirectReverseConfig(
        correct_action_prob=0.8,
        temperature=0.5,
        random_action_weights={"add": 0.4, "delete": 0.3, "replace": 0.3},
        seed=42
    )
    
    generator = DirectReverseTrajectoryGenerator(charset, config)
    
    print(f"Target sequence: '{target}'")
    
    # Generate trajectory
    trajectory = generator.generate(target)
    
    print(f"Generated trajectory with {len(trajectory.steps)} steps")
    print(f"Initial sequence: '{trajectory.initial_sequence}'")
    
    # Apply steps manually and track states
    current_state = trajectory.initial_sequence
    states = [current_state]
    
    print("\nApplying steps manually:")
    for i, step in enumerate(trajectory.steps):
        print(f"Step {i+1}: {step.action} '{step.token}' at position {step.position}")
        print(f"  Before: '{current_state}'")
        
        # Apply using apply_step function
        new_state = apply_step(current_state, step)
        states.append(new_state)
        current_state = new_state
        
        print(f"  After:  '{current_state}'")
        
        # Show first 10 and last 5 steps
        if i >= 10 and i < len(trajectory.steps) - 5:
            if i == 10:
                print(f"  ... (skipping steps 11-{len(trajectory.steps)-5}) ...")
            continue
    
    print(f"\nFinal state: '{current_state}'")
    print(f"Target:      '{target}'")
    print(f"Match: {current_state == target}")
    
    return trajectory, states


def test_existing_training_data():
    """Test the existing training data to find the state tracking bug."""
    print("\nTesting existing training data...")
    
    # Load a sample from the existing data
    data_file = "./data/CreateProtein_head1000_new/val_data.json"
    
    if not Path(data_file).exists():
        print(f"Data file {data_file} not found, skipping...")
        return
    
    with open(data_file, 'r') as f:
        examples = json.load(f)
    
    if not examples:
        print("No examples found in data file")
        return
    
    # Take the first example
    example = examples[0]
    print(f"Testing example: {example['protein_name']}")
    print(f"Target: '{example['final_state']}'")
    print(f"Number of steps: {example['num_steps']}")
    
    # Extract actions from reverse_process
    actions = []
    state_updates = {}
    
    for line in example['reverse_process']:
        if line.startswith("Current state after step "):
            # Extract step number and state
            parts = line.split(": ")
            step_info = parts[0]
            state = parts[1].strip("'")
            step_num = int(step_info.split()[-1])
            state_updates[step_num] = state
        elif not line.startswith("Final state"):
            actions.append(line)
    
    print(f"Found {len(actions)} actions and {len(state_updates)} state updates")
    
    # Apply actions manually and compare with recorded states
    current_state = ""
    print("\nManual application vs recorded states:")
    
    for i, action_str in enumerate(actions):
        step_num = i + 1
        
        # Apply action manually
        new_state = apply_action_string(current_state, action_str)
        current_state = new_state
        
        # Check if we have a recorded state for this step
        if step_num in state_updates:
            recorded_state = state_updates[step_num]
            match = current_state == recorded_state
            
            print(f"Step {step_num}: {action_str}")
            print(f"  Manual result:   '{current_state}'")
            print(f"  Recorded state:  '{recorded_state}'")
            print(f"  Match: {match}")
            
            if not match:
                print(f"  ❌ MISMATCH FOUND!")
                print(f"  Manual length: {len(current_state)}")
                print(f"  Recorded length: {len(recorded_state)}")
                
                # Show character-by-character diff
                min_len = min(len(current_state), len(recorded_state))
                for j in range(min_len):
                    if current_state[j] != recorded_state[j]:
                        print(f"    Diff at position {j}: manual='{current_state[j]}', recorded='{recorded_state[j]}'")
                        break
                
                return False
            else:
                print(f"  ✅ Match")
    
    print(f"\nFinal manual state: '{current_state}'")
    print(f"Target state:       '{example['final_state']}'")
    print(f"Final match: {current_state == example['final_state']}")
    
    return True


def main():
    """Main debug function."""
    print("Debugging State Tracking in Training Data Generation")
    print("="*60)
    
    # Test 1: Generate a new trajectory and verify state tracking
    print("TEST 1: Generate new trajectory")
    trajectory, states = test_trajectory_generation()
    
    # Test 2: Check existing training data for inconsistencies
    print("\n" + "="*60)
    print("TEST 2: Check existing training data")
    success = test_existing_training_data()
    
    if success:
        print("\n✅ State tracking appears to be working correctly")
    else:
        print("\n❌ State tracking bug found!")


if __name__ == "__main__":
    main()
