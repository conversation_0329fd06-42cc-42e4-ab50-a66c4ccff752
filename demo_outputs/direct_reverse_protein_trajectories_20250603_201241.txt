DIRECT REVERSE PROTEIN TRAJECTORIES (Target → Empty)
Generated on: 2025-06-03 20:12:41
Configuration: {'correct_action_prob': 0.6, 'temperature': 0.5, 'random_action_weights': {'add': 0.2, 'delete': 0.6, 'replace': 0.2}, 'seed': 123}
Total sequences: 3
Protein charset: ['A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'Y']

Note: These are direct reverse trajectories that go from complete protein to absorbing state.
Each trajectory shows the degradation process without intermediate forward generation.

================================================================================
DIRECT REVERSE TRAJECTORY: UniProtKB_P12345
Original Sequence: ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY
Original Length: 60
Total Steps: 70
Efficiency: 0.86 chars/step
Success: Yes

Full State Progression (Target → Empty):
  Step   0: 'ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY' (len=60) [START]
  Step   1: 'ACDEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY' (len=59) ← remove V from position 17
  Step   2: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY' (len=58) ← remove D from position 2
  Step   3: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVW' (len=57) ← remove Y from position 57
  Step   4: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTV' (len=56) ← remove W from position 56
  Step   5: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRST' (len=55) ← remove V from position 55
  Step   6: 'ACEFGHIKLMNPQRSTWYACDEFGHIKDLMNPQRSTVWYACDEFGHIKLMNPQRST' (len=56) ← add D at position 27
  Step   7: 'CEFGHIKLMNPQRSTWYACDEFGHIKDLMNPQRSTVWYACDEFGHIKLMNPQRST' (len=55) ← remove A from position 0
  Step   8: 'CEFGHIKLMNPQRSTWYACDEFGHIKDLMNPQRSTVWYACDEFGHIKLMNPQRS' (len=54) ← remove T from position 54
  Step   9: 'CEFGHIKLMFNPQRSTWYACDEFGHIKDLMNPQRSTVWYACDEFGHIKLMNPQRS' (len=55) ← add F at position 9
  Step  10: 'CEFGHIKLMFNPQRSTWYACDEFGHIKVDLMNPQRSTVWYACDEFGHIKLMNPQRS' (len=56) ← add V at position 27
  Step  11: 'CEFGHIKLMFNPQRSTWYACDEFGHIKVDLMNPQRSTVWYACDEFGHIKLMNPQR' (len=55) ← remove S from position 55
  Step  12: 'CEFGHIKLMFNPQRSTWYACDEFGHIKVDLMNPQRSTVWYACDEFGHIKLMNPQ' (len=54) ← remove R from position 54
  Step  13: 'CEFGHIKLMFNPQRSTWYACDEFGHIKVDLMNPQRSTVWYACDEFGHIKLMNP' (len=53) ← remove Q from position 53
  Step  14: 'CEFGHIKLMFNPQRSTWYACDEFGHIKVDLMNPQRSVWYACDEFGHIKLMNP' (len=52) ← remove T from position 36
  Step  15: 'CEFGHIKLMFNPQRSTWYACDEFGHKVDLMNPQRSVWYACDEFGHIKLMNP' (len=51) ← remove I from position 25
  Step  16: 'CEFGHIKLMFNPQRSTWYACDEFGHKVLMNPQRSVWYACDEFGHIKLMNP' (len=50) ← remove D from position 27
  Step  17: 'CEFGHIKLMFNPQRSTWYACDEFGHKVLMNPQRSVWYACDEFHIKLMNP' (len=49) ← remove G from position 42
  Step  18: 'CEFGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDEFHIKLMNP' (len=48) ← remove S from position 33
  Step  19: 'CEGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDEFHIKLMNP' (len=47) ← remove F from position 2
  Step  20: 'CEGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDEFHIKLMN' (len=46) ← remove P from position 46
  Step  21: 'CEGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDEFHILLMN' (len=46) ← replace K at position 42 with L
  Step  22: 'CEGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDEFHILLM' (len=45) ← remove N from position 45
  Step  23: 'CEGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDEFHILL' (len=44) ← remove M from position 44
  Step  24: 'CEGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDEFHIL' (len=43) ← remove L from position 43
  Step  25: 'CEGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDEFHI' (len=42) ← remove L from position 42
  Step  26: 'CEGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDEFH' (len=41) ← remove I from position 41
  Step  27: 'CEGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDEF' (len=40) ← remove H from position 40
  Step  28: 'CEGHIKLMFNPQRSTWYACDEFGHKVLMNPQRVWYACDE' (len=39) ← remove F from position 39
  Step  29: 'CEGHIKLMFNPQRSTWYATDEFGHKVLMNPQRVWYACDE' (len=39) ← replace C at position 18 with T
  Step  30: 'CEGHIKLMFNPQRSWYATDEFGHKVLMNPQRVWYACDE' (len=38) ← remove T from position 14
  Step  31: 'CEGHIKLMFPQRSWYATDEFGHKVLMNPQRVWYACDE' (len=37) ← remove N from position 9
  Step  32: 'CEGHIKLMFPQRSWYATDEFGHKVLMNPQVWYACDE' (len=36) ← remove R from position 29
  Step  33: 'CEGHIKLMFPQRSWYATDEFGHKVLMNPQVWYACD' (len=35) ← remove E from position 35
  Step  34: 'CEGHIKLMFPQRSWYATDEFGHKVLMNPQVWYAC' (len=34) ← remove D from position 34
  Step  35: 'CEGHIKLMFPQRSWYATDEFGHKVLMNPQVWYA' (len=33) ← remove C from position 33
  Step  36: 'CEGHIKLMFPQRSWYATDEFGHKVLMNPQVWY' (len=32) ← remove A from position 32
  Step  37: 'CEGHIKLMFPQRSWYATDEFGHKVLMNPQVW' (len=31) ← remove Y from position 31
  Step  38: 'CEGHIKLMFPQRSWYATDEFGHKVLMNPQV' (len=30) ← remove W from position 30
  Step  39: 'CEGHIKLMFPQRSWYATDEFGHKVLMNPQ' (len=29) ← remove V from position 29
  Step  40: 'CEGHIKLMFPQRSWYATDEFGHKVLMNP' (len=28) ← remove Q from position 28
  Step  41: 'CEGHIKLMFPRSWYATDEFGHKVLMNP' (len=27) ← remove Q from position 10
  Step  42: 'CEGHIKLMFPRSWYATDEFGHKVLMN' (len=26) ← remove P from position 26
  Step  43: 'CEGHIKLMFPRWYATDEFGHKVLMN' (len=25) ← remove S from position 11
  Step  44: 'CEGHIKLMFPRWYATDEFGHKVLM' (len=24) ← remove N from position 24
  Step  45: 'CEHIKLMFPRWYATDEFGHKVLM' (len=23) ← remove G from position 2
  Step  46: 'CEHIKLMFPRWATDEFGHKVLM' (len=22) ← remove Y from position 11
  Step  47: 'CEHIKLMFPRWATDEFGHKVM' (len=21) ← remove L from position 20
  Step  48: 'CEHIKLMFPRWATDEFGHKV' (len=20) ← remove M from position 20
  Step  49: 'CEHIKLMFPRWATDEFGHK' (len=19) ← remove V from position 19
  Step  50: 'CEHIKLMFPRWANDEFGHK' (len=19) ← replace T at position 12 with N
  Step  51: 'CEHIKLMFPRWANDEFHK' (len=18) ← remove G from position 16
  Step  52: 'CEHIKLMFPRWANDEFH' (len=17) ← remove K from position 17
  Step  53: 'CEHIKLMFPRWANDEF' (len=16) ← remove H from position 16
  Step  54: 'EHIKLMFPRWANDEF' (len=15) ← remove C from position 0
  Step  55: 'EHIKLMFPRWADEF' (len=14) ← remove N from position 11
  Step  56: 'EHIKLMFRWADEF' (len=13) ← remove P from position 7
  Step  57: 'HIKLMFRWADEF' (len=12) ← remove E from position 0
  Step  58: 'HIKLMFRWADE' (len=11) ← remove F from position 11
  Step  59: 'HIKLMFRWAD' (len=10) ← remove E from position 10
  Step  60: 'HIKLMFRWA' (len=9) ← remove D from position 9
  Step  61: 'HIKLMRWA' (len=8) ← remove F from position 5
  Step  62: 'HIKMRWA' (len=7) ← remove L from position 3
  Step  63: 'HIKMRW' (len=6) ← remove A from position 6
  Step  64: 'IKMRW' (len=5) ← remove H from position 0
  Step  65: 'KMRW' (len=4) ← remove I from position 0
  Step  66: 'KMR' (len=3) ← remove W from position 3
  Step  67: 'KM' (len=2) ← remove R from position 2
  Step  68: 'K' (len=1) ← remove M from position 1
  Step  69: 'T' (len=1) ← replace K at position 0 with T
  Step  70: '' (len=0) ← remove T from position 0 [ABSORBING STATE]

Trajectory Metadata:
  {
  "approach": "direct_reverse",
  "config": {
    "correct_action_prob": 0.6,
    "temperature": 0.5,
    "random_action_weights": {
      "add": 0.2,
      "delete": 0.6,
      "replace": 0.2
    }
  },
  "target_length": 60,
  "total_steps": 70,
  "direction": "target_to_absorbing"
}

================================================================================
DIRECT REVERSE TRAJECTORY: PDB_1ABC
Original Sequence: MPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKL
Original Length: 80
Total Steps: 108
Efficiency: 0.74 chars/step
Success: Yes

Full State Progression (Target → Empty):
  Step   0: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKL' (len=80) [START]
  Step   1: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDFGHIKL' (len=79) ← remove E from position 73
  Step   2: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDFGHIKL' (len=78) ← remove W from position 28
  Step   3: 'YPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDFGHIKL' (len=78) ← replace M at position 0 with Y
  Step   4: 'YPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDFGIKL' (len=77) ← remove H from position 74
  Step   5: 'YPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDFGIK' (len=76) ← remove L from position 76
  Step   6: 'YPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFGHIKLMPQRSTUVWYACDEGHIKLMPQRSTUVWYACDFGIK' (len=75) ← remove F from position 53
  Step   7: 'YPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFGHIKLMPQRSTUVWYACDEGHIKLMPQRSTUVWYACDFGI' (len=74) ← remove K from position 74
  Step   8: 'YPQRSTUVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPQRSTUVWYACDEGHIKLMPQRSTUVWYACDFGI' (len=73) ← remove S from position 24
  Step   9: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPQRSTUVWYACDEGHIKLMPQRSTUVWYACDFGI' (len=74) ← add Q at position 7
  Step  10: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPQRSTUVWYACDEGHIKLMPQRSTUVWYACDFG' (len=73) ← remove I from position 73
  Step  11: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPQRSTUVWNACDEGHIKLMPQRSTUVWYACDFG' (len=73) ← replace Y at position 48 with N
  Step  12: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPQRSTUVWNACDEGHIKLMPQRSTUVWYACDF' (len=72) ← remove G from position 72
  Step  13: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPRSTUVWNACDEGHIKLMPQRSTUVWYACDF' (len=71) ← remove Q from position 41
  Step  14: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPRSTUVWNACDEGHIKLMPQRSTUVWYACD' (len=70) ← remove F from position 70
  Step  15: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPRSTUVWNACDEGHIKLMPQRSTUVWYAC' (len=69) ← remove D from position 69
  Step  16: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPSTUVWNACDEGHIKLMPQRSTUVWYAC' (len=68) ← remove R from position 41
  Step  17: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPSTUVWNACDEGHIKLMPQRSTUVWYA' (len=67) ← remove C from position 67
  Step  18: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPSTUWNACDEGHIKLMPQRSTUVWYA' (len=66) ← remove V from position 44
  Step  19: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGHIKLMPSTUWNACDEGHIKLMPQRSTUVWY' (len=65) ← remove A from position 65
  Step  20: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGIKLMPSTUWNACDEGHIKLMPQRSTUVWY' (len=64) ← remove H from position 35
  Step  21: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGIKLMPSTUWNACDEGHIKLMPQRSTUVW' (len=63) ← remove Y from position 63
  Step  22: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGIKLMPSTUWNACDEGHIKLMPQRSTUV' (len=62) ← remove W from position 62
  Step  23: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGIKLMPSTUWNACDEGHIKLMPQRSTU' (len=61) ← remove V from position 61
  Step  24: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGIKLMPSTUWNACDEGHIKLMPQRST' (len=60) ← remove U from position 60
  Step  25: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGIKLMPSTUWNACDEGHIKLMPQRS' (len=59) ← remove T from position 59
  Step  26: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGIKLPSTUWNACDEGHIKLMPQRS' (len=58) ← remove M from position 38
  Step  27: 'YPQRSTUQVWYACDEFGHIKLMPQRTUVYACDEFGIKLPSTUWNACDEGHIKLMPQR' (len=57) ← remove S from position 57
  Step  28: 'YPQRSTUQVWYACDEFGHKLMPQRTUVYACDEFGIKLPSTUWNACDEGHIKLMPQR' (len=56) ← remove I from position 18
  Step  29: 'MPQRSTUQVWYACDEFGHKLMPQRTUVYACDEFGIKLPSTUWNACDEGHIKLMPQR' (len=56) ← replace Y at position 0 with M
  Step  30: 'MPQRTUQVWYACDEFGHKLMPQRTUVYACDEFGIKLPSTUWNACDEGHIKLMPQR' (len=55) ← remove S from position 4
  Step  31: 'MPQRTUQVWYACDEFGHKLMPQRTUVACDEFGIKLPSTUWNACDEGHIKLMPQR' (len=54) ← remove Y from position 26
  Step  32: 'MPQRTUQVWYACDEFGHKLMPQRTUVACDEFGIKLPSTUWNACDEGHIKLMPQ' (len=53) ← remove R from position 53
  Step  33: 'MPQRTUQVWYACDEFGHKLMPQRTUVACDEFGIKLPSTUWNACDEGHIKLMP' (len=52) ← remove Q from position 52
  Step  34: 'MPQRTUQVWYACDEFGHKLMPQRTUVACDEFGIKLPSTUWNACDEGHIKLM' (len=51) ← remove P from position 51
  Step  35: 'MPQRTUQVWYACDEFGHKLMPQRTUVACDEFGILPSTUWNACDEGHIKLM' (len=50) ← remove K from position 33
  Step  36: 'MPQRTUQVWYACDEFGHKLMPQRTUVACDFGILPSTUWNACDEGHIKLM' (len=49) ← remove E from position 29
  Step  37: 'MPQRTUQVWYACDEFGHKLMPQRTUVACDFGILPSTUWNACDEGHIKL' (len=48) ← remove M from position 48
  Step  38: 'MPQRTUQVWYACDEFGHKLMPQRTUVACDFGILPSTUWNACDEGHIK' (len=47) ← remove L from position 47
  Step  39: 'MPQRTUQVWYACDEFGHKLMPQRTUVACDFGILSTUWNACDEGHIK' (len=46) ← remove P from position 33
  Step  40: 'MPQRTUQVWYACDEFGHKLMPQRTUQVACDFGILSTUWNACDEGHIK' (len=47) ← add Q at position 25
  Step  41: 'MPQTUQVWYACDEFGHKLMPQRTUQVACDFGILSTUWNACDEGHIK' (len=46) ← remove R from position 3
  Step  42: 'MPQTUQVWYACDEFGHKLMPQRTUQVADFGILSTUWNACDEGHIK' (len=45) ← remove C from position 27
  Step  43: 'MPQTUQVWYACDEFGHKLMPQRTUQVADFGILSTUWNACDEGHI' (len=44) ← remove K from position 44
  Step  44: 'MPQTUQVWYACDEFGKLMPQRTUQVADFGILSTUWNACDEGHI' (len=43) ← remove H from position 15
  Step  45: 'MPQTUQVWYACDEGKLMPQRTUQVADFGILSTUWNACDEGHI' (len=42) ← remove F from position 13
  Step  46: 'PQTUQVWYACDEGKLMPQRTUQVADFGILSTUWNACDEGHI' (len=41) ← remove M from position 0
  Step  47: 'PQTUQVWYACDEGKLMPQRTUQVADFGILSTUWNACDEGH' (len=40) ← remove I from position 40
  Step  48: 'PQTUQVWYACDEGKLPQRTUQVADFGILSTUWNACDEGH' (len=39) ← remove M from position 15
  Step  49: 'PQTUQVWYACDEGKLPQRTUQVADFGIMSTUWNACDEGH' (len=39) ← replace L at position 27 with M
  Step  50: 'PQTUQVWYACDEGKLPQRUQVADFGIMSTUWNACDEGH' (len=38) ← remove T from position 18
  Step  51: 'PQTUQVWYACDEGULPQRUQVADFGIMSTUWNACDEGH' (len=38) ← replace K at position 13 with U
  Step  52: 'PQTUQVWYACDEGULPQRUQVADFGIMSTUWNACDEG' (len=37) ← remove H from position 37
  Step  53: 'PQTUQVWYACDEGULPQRUQVADFGIMSTUWNACDE' (len=36) ← remove G from position 36
  Step  54: 'PQTUQVWYACDEGULPQRUQVADFGIMSTUWNACD' (len=35) ← remove E from position 35
  Step  55: 'PQTUQVWYACDEGULPQRUQVADFGIMSTUWNAC' (len=34) ← remove D from position 34
  Step  56: 'PQTUQVWYACDEGULPQRUQVADFGIMSTUWAC' (len=33) ← remove N from position 31
  Step  57: 'PQTUQVWYACDEGULPQRUQVADFGIMSTUAC' (len=32) ← remove W from position 30
  Step  58: 'PQTUQVWYFCDEGULPQRUQVADFGIMSTUAC' (len=32) ← replace A at position 8 with F
  Step  59: 'PTUQVWYFCDEGULPQRUQVADFGIMSTUAC' (len=31) ← remove Q from position 1
  Step  60: 'PTUQVWYFCDEGULPQRUQVADFGIMSTUA' (len=30) ← remove C from position 30
  Step  61: 'PTUQVWYFCDEGULPQRUQVADFGIMSTU' (len=29) ← remove A from position 29
  Step  62: 'PTUQVWYFCDEGULPQRUQVADFGIMST' (len=28) ← remove U from position 28
  Step  63: 'PTUQVWYFCDEGULPQRUQVAMDFGIMST' (len=29) ← add M at position 21
  Step  64: 'PTUQVWFCDEGULPQRUQVAMDFGIMST' (len=28) ← remove Y from position 6
  Step  65: 'PTUQSVWFCDEGULPQRUQVAMDFGIMST' (len=29) ← add S at position 4
  Step  66: 'PTUQSWFCDEGULPQRUQVAMDFGIMST' (len=28) ← remove V from position 5
  Step  67: 'PETUQSWFCDEGULPQRUQVAMDFGIMST' (len=29) ← add E at position 1
  Step  68: 'PETUQSWFCDEGULPQRUQVAMDFGIMS' (len=28) ← remove T from position 28
  Step  69: 'PTUQSWFCDEGULPQRUQVAMDFGIMS' (len=27) ← remove E from position 1
  Step  70: 'PTUQSWFCDEGULPQRUQVAMDFGIM' (len=26) ← remove S from position 26
  Step  71: 'PNTUQSWFCDEGULPQRUQVAMDFGIM' (len=27) ← add N at position 1
  Step  72: 'PNTUQSFCDEGULPQRUQVAMDFGIM' (len=26) ← remove W from position 6
  Step  73: 'PNTUQSFCDEGULQRUQVAMDFGIM' (len=25) ← remove P from position 13
  Step  74: 'PNTUQSFCDEGULQRUQVAMDFGI' (len=24) ← remove M from position 24
  Step  75: 'PNTUQSFCDEGULQRUQVAMDGI' (len=23) ← remove F from position 21
  Step  76: 'PTUQSFCDEGULQRUQVAMDGI' (len=22) ← remove N from position 1
  Step  77: 'PTUQSFCDEGULQRUQVAMDG' (len=21) ← remove I from position 21
  Step  78: 'PTUQFCDEGULQRUQVAMDG' (len=20) ← remove S from position 4
  Step  79: 'PTUQFCDEGULQRUQVAMD' (len=19) ← remove G from position 19
  Step  80: 'PTUQFCDEGULQRUQVAKD' (len=19) ← replace M at position 17 with K
  Step  81: 'PTUQFCDEGULQRUQVAPD' (len=19) ← replace K at position 17 with P
  Step  82: 'PTUQFCDEGULQRUQVAP' (len=18) ← remove D from position 18
  Step  83: 'PTUQFCDEGULQRUQVA' (len=17) ← remove P from position 17
  Step  84: 'PTUQFCDEGULQRUQV' (len=16) ← remove A from position 16
  Step  85: 'LTUQFCDEGULQRUQV' (len=16) ← replace P at position 0 with L
  Step  86: 'LTUQFCDEGUQRUQV' (len=15) ← remove L from position 10
  Step  87: 'LTUQFCDEGUQRUQ' (len=14) ← remove V from position 14
  Step  88: 'LTUFCDEGUQRUQ' (len=13) ← remove Q from position 3
  Step  89: 'LTUFCDEGUQRU' (len=12) ← remove Q from position 12
  Step  90: 'LTUFCDGUQRU' (len=11) ← remove E from position 6
  Step  91: 'LTUFCDGURU' (len=10) ← remove Q from position 8
  Step  92: 'KLTUFCDGURU' (len=11) ← add K at position 0
  Step  93: 'KLTUFCDGUR' (len=10) ← remove U from position 10
  Step  94: 'KLTUFDGUR' (len=9) ← remove C from position 5
  Step  95: 'KLTUFDGU' (len=8) ← remove R from position 8
  Step  96: 'KDLTUFDGU' (len=9) ← add D at position 1
  Step  97: 'KDLTUDGU' (len=8) ← remove F from position 5
  Step  98: 'KDLTUDG' (len=7) ← remove U from position 7
  Step  99: 'KDLTDG' (len=6) ← remove U from position 4
  Step 100: 'DLTDG' (len=5) ← remove K from position 0
  Step 101: 'DLDG' (len=4) ← remove T from position 2
  Step 102: 'DLD' (len=3) ← remove G from position 3
  Step 103: 'DL' (len=2) ← remove D from position 2
  Step 104: 'D' (len=1) ← remove L from position 1
  Step 105: 'H' (len=1) ← replace D at position 0 with H
  Step 106: 'QH' (len=2) ← add Q at position 0
  Step 107: 'Q' (len=1) ← remove H from position 1
  Step 108: '' (len=0) ← remove Q from position 0 [ABSORBING STATE]

Trajectory Metadata:
  {
  "approach": "direct_reverse",
  "config": {
    "correct_action_prob": 0.6,
    "temperature": 0.5,
    "random_action_weights": {
      "add": 0.2,
      "delete": 0.6,
      "replace": 0.2
    }
  },
  "target_length": 80,
  "total_steps": 108,
  "direction": "target_to_absorbing"
}

================================================================================
DIRECT REVERSE TRAJECTORY: SwissProt_Q8XYZ
Original Sequence: ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKL
Original Length: 150
Total Steps: 197
Efficiency: 0.76 chars/step
Success: Yes

Full State Progression (Target → Empty):
  Step   0: 'ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKL' (len=150) [START]
  Step   1: 'ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIK' (len=149) ← remove L from position 149
  Step   2: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIK' (len=149) ← replace T at position 16 with A
  Step   3: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHI' (len=148) ← remove K from position 148
  Step   4: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGH' (len=147) ← remove I from position 147
  Step   5: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGH' (len=146) ← remove Y from position 79
  Step   6: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFG' (len=145) ← remove H from position 145
  Step   7: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTLWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFG' (len=145) ← replace V at position 37 with L
  Step   8: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTLWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFG' (len=144) ← remove M from position 109
  Step   9: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTLWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEF' (len=143) ← remove G from position 143
  Step  10: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEF' (len=142) ← remove Y from position 39
  Step  11: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDE' (len=141) ← remove F from position 141
  Step  12: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQRSTWYACDEFGHIKLMNPQRSTVWYACDE' (len=140) ← remove V from position 114
  Step  13: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQRSTWYACDEFGHIKLMNPQRSTVWYACD' (len=139) ← remove E from position 139
  Step  14: 'ACDEFGHIKLMNPQRSAVWYACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQSTWYACDEFGHIKLMNPQRSTVWYACD' (len=138) ← remove R from position 111
  Step  15: 'ACDEFGHIKLMNPQRSAVWHACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQSTWYACDEFGHIKLMNPQRSTVWYACD' (len=138) ← replace Y at position 19 with H
  Step  16: 'ACDEFGHIKLMNPQRSAVWHACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQSTWYACDEFGHIKLMNPQRSTVWYAC' (len=137) ← remove D from position 137
  Step  17: 'ACDEFGHIKLMNPQRSAVWHACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQSTWYACDEFGHIKLMNPQRSTVWYA' (len=136) ← remove C from position 136
  Step  18: 'ACDEFGHIKLMNPQRSAVWHACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQSTWYACDEFGHIKLMNPQRSTVWY' (len=135) ← remove A from position 135
  Step  19: 'ACDEFGHIKLMNPQRSAVWHACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHIKLNPQSTWYACDEFGHIKLMNPQRSTVW' (len=134) ← remove Y from position 134
  Step  20: 'ACDEFGHIKLMNPQRSAVWHACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYACDEFGHKLNPQSTWYACDEFGHIKLMNPQRSTVW' (len=133) ← remove I from position 105
  Step  21: 'ACDEFGHIKLMNPQRSAVWHACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYNCDEFGHKLNPQSTWYACDEFGHIKLMNPQRSTVW' (len=133) ← replace A at position 98 with N
  Step  22: 'ACDEFGHIKLMNPQRSAVWHACDEFGHIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTWYACDEFGHIKLMNPQRSTVW' (len=132) ← remove G from position 103
  Step  23: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTWYACDEFGHIKLMNPQRSTVW' (len=131) ← remove H from position 26
  Step  24: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWACDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTWYACDEFGHIKLMNPQRSTV' (len=130) ← remove W from position 130
  Step  25: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWCDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTWYACDEFGHIKLMNPQRSTV' (len=129) ← remove A from position 77
  Step  26: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWCDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTWYACDEFGHIKLMNPQRST' (len=128) ← remove V from position 128
  Step  27: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWCDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTWYACDEFGHIKLMNPQRS' (len=127) ← remove T from position 127
  Step  28: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWCDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTWYACDEFGHIKLMNPQR' (len=126) ← remove S from position 126
  Step  29: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWCDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTYACDEFGHIKLMNPQR' (len=125) ← remove W from position 109
  Step  30: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWGDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTYACDEFGHIKLMNPQR' (len=125) ← replace C at position 77 with G
  Step  31: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWGDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTYACDEFGHIKLMNPQ' (len=124) ← remove R from position 124
  Step  32: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWGDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTYACDEFGYHIKLMNPQ' (len=125) ← add Y at position 116
  Step  33: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWGDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTYACDEFGYHIKLMNPQ' (len=124) ← remove P from position 70
  Step  34: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWGDEFGHIKLMNPQRSTVWYNCDEFHKLNPQSTYACDEFGYHIKLMNP' (len=123) ← remove Q from position 123
  Step  35: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWGDEFGHKLMNPQRSTVWYNCDEFHKLNPQSTYACDEFGYHIKLMNP' (len=122) ← remove I from position 82
  Step  36: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWGDEFGHKLMNPQRSTVWYNCDWFHKLNPQSTYACDEFGYHIKLMNP' (len=122) ← replace E at position 97 with W
  Step  37: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWGDEFGHKLMNPQRSTVWYNCDWFHKLNPQSTYACDEFGYHIKLMNP' (len=121) ← remove G from position 43
  Step  38: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWGDEFGHKLMNPQRSTVWYNCDWFHKLNPQSTYACDEFGYHIKLMN' (len=120) ← remove P from position 120
  Step  39: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWGDEFGHKLMNPQRSTVWYNCDWFHKLNPQSTYACDEFGYHIKLM' (len=119) ← remove N from position 119
  Step  40: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWGDEFGHKLMPQRSTVWYNCDWFHKLNPQSTYACDEFGYHIKLM' (len=118) ← remove N from position 84
  Step  41: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWGDEFGHKLMPQRSTVWYNCDWFHKLNPQSTYACDEFGYHIKL' (len=117) ← remove M from position 117
  Step  42: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLMNQRSTPVWGDEFGHKLMPQRSTVWYNCDWFHKLNPQSTYACDEFGYHIKL' (len=118) ← add P at position 73
  Step  43: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLMNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNPQSTYACDEFGYHIKL' (len=117) ← remove W from position 91
  Step  44: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNPQSTYACDEFGYHIKL' (len=116) ← remove M from position 67
  Step  45: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNPUSTYACDEFGYHIKL' (len=116) ← replace Q at position 101 with U
  Step  46: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNPUSTYACDEFGYHIK' (len=115) ← remove L from position 115
  Step  47: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNKUSTYACDEFGYHIK' (len=115) ← replace P at position 100 with K
  Step  48: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNKUSTYACDEFGYHI' (len=114) ← remove K from position 114
  Step  49: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNKUSTYACEFGYHI' (len=113) ← remove D from position 107
  Step  50: 'ACDEFGHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNKUSTYACEFGYH' (len=112) ← remove I from position 112
  Step  51: 'ACDEFHIKLMNPQRSAVWHACDEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNKUSTYACEFGYH' (len=111) ← remove G from position 5
  Step  52: 'ACDEFHIKLMNPQRSAVWHACEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNKUSTYACEFGYH' (len=110) ← remove D from position 21
  Step  53: 'ACDEFHIKLMNPQRSAVWHACEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNKUSTYCEFGYH' (len=109) ← remove A from position 103
  Step  54: 'ACDEFHIKLMNPQRSAVWHACEFGIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNKUSTYCEFGY' (len=108) ← remove H from position 108
  Step  55: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSTVYNCDWFHKLNKUSTYCEFGY' (len=107) ← remove G from position 23
  Step  56: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWFHKLNKUSTYCEFGY' (len=106) ← remove T from position 85
  Step  57: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWFHKLNKUSTYCEFG' (len=105) ← remove Y from position 105
  Step  58: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYACDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWFHKLNKUSTYCEF' (len=104) ← remove G from position 104
  Step  59: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMNPQRSTVWYCDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWFHKLNKUSTYCEF' (len=103) ← remove A from position 54
  Step  60: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMNPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWFHKLNKUSTYCEF' (len=103) ← replace T at position 50 with M
  Step  61: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMNPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWFHKLNKUSTYCE' (len=102) ← remove F from position 102
  Step  62: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMNPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWFHKLNKUSTYC' (len=101) ← remove E from position 101
  Step  63: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMNPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWFHKLNKUSTY' (len=100) ← remove C from position 100
  Step  64: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWFHKLNKUSTY' (len=99) ← remove N from position 45
  Step  65: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWHKLNKUSTY' (len=98) ← remove F from position 89
  Step  66: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGHKLMPQRSVYNCDWHKLNKUST' (len=97) ← remove Y from position 97
  Step  67: 'ACDEFHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGKLMPQRSVYNCDWHKLNKUST' (len=96) ← remove H from position 75
  Step  68: 'ACDEHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGKLMPQRSVYNCDWHKLNKUST' (len=95) ← remove F from position 4
  Step  69: 'ACDEHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGKLMPQRSVYNCDWHKLNKUS' (len=94) ← remove T from position 94
  Step  70: 'ACDEHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGKLMPQRSVYNCDWHKLNKU' (len=93) ← remove S from position 93
  Step  71: 'ACDEHIKLMNPQRSAVWHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGKLMPQRSVYNCDWHKLNK' (len=92) ← remove U from position 92
  Step  72: 'ACDEHIKLMNPQRSAVHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGDEFGKLMPQRSVYNCDWHKLNK' (len=91) ← remove W from position 16
  Step  73: 'ACDEHIKLMNPQRSAVHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDWHKLNK' (len=90) ← remove D from position 69
  Step  74: 'ACDEHIKLMNPQRSAVHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDWHKLN' (len=89) ← remove K from position 89
  Step  75: 'ACDEHIKLMNPQRSAVHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDWHKL' (len=88) ← remove N from position 88
  Step  76: 'ADEHIKLMNPQRSAVHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDWHKL' (len=87) ← remove C from position 1
  Step  77: 'ADEHIKLMNPQRSAVHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDWHK' (len=86) ← remove L from position 86
  Step  78: 'ADEHIKLMNPQRSAVHACEFIKLMNPQRSTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDWH' (len=85) ← remove K from position 85
  Step  79: 'ADEHIKLMNPQRSAVHACEFIKLMNPQRSMTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDWH' (len=86) ← add M at position 29
  Step  80: 'ADEHIKLMNPQRSAHACEFIKLMNPQRSMTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDWH' (len=85) ← remove V from position 14
  Step  81: 'ADEHIKLMNPQRSAHACEFIKLMNPQRSMTLWACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDW' (len=84) ← remove H from position 84
  Step  82: 'ADEHIKLMNPQRSAHACEFIKLMNPQRSMTLWDACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDW' (len=85) ← add D at position 32
  Step  83: 'ADEHIKLMNPQRSAHACEFIKLMNQRSMTLWDACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCDW' (len=84) ← remove P from position 24
  Step  84: 'ADEHIKLMNPQRSAHACEFIKLMNQRSMTLWDACDEFHIKLMPQRSMVWYCDEFGHIKLNQRSTPVWGEFGKLMPQRSVYNCD' (len=83) ← remove W from position 83
  Step  85: 'ADEHIKLMNPQRSAHACEFIKLMNQRSMTLWDACDEFHIKLMPQRSMVWYCDEFGHILNQRSTPVWGEFGKLMPQRSVYNCD' (len=82) ← remove K from position 57
  Step  86: 'ADEHIKLMNPQRSAHACEFIKLMNQRSMTLWDACDEFHIKLMPQRSMVWYCDEFGHINQRSTPVWGEFGKLMPQRSVYNCD' (len=81) ← remove L from position 57
  Step  87: 'ADEHIKLMNPQRAHACEFIKLMNQRSMTLWDACDEFHIKLMPQRSMVWYCDEFGHINQRSTPVWGEFGKLMPQRSVYNCD' (len=80) ← remove S from position 12
  Step  88: 'ADEHIKLMNPQRAHACEFIKLMNQRSMTLWDACDEFHIKLMPQRSMVWYCQEFGHINQRSTPVWGEFGKLMPQRSVYNCD' (len=80) ← replace D at position 50 with Q
  Step  89: 'ADEHIKLMNPQRAHACEFIKLMNQRSMTLWDACDEFHIKLMPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQRSVYNCD' (len=79) ← remove W from position 47
  Step  90: 'ADEHIKLMNPQRAHACEFIKLMNQRSMTLWDACDEFHIKLMPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQRSVYNC' (len=78) ← remove D from position 78
  Step  91: 'ADEHIKLMNPQRAHACEFIKLMNQRSMTLWDACDEFHIKLMPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQRSVYN' (len=77) ← remove C from position 77
  Step  92: 'ADEHIKLMNPQRAHACEFIKLMNQRSMTLWDACDEFHIKLMPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQRSVY' (len=76) ← remove N from position 76
  Step  93: 'ADEHIKLMNPQRAHACEFIKLMNQRSMTLWDACDEFHIKLLPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQRSVY' (len=76) ← replace M at position 40 with L
  Step  94: 'ADEHIKLMNPQRAHACEFIKLMNQRSMTLWDACDEFHIKLLPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQRSV' (len=75) ← remove Y from position 75
  Step  95: 'ADEHIKLMNPQRAHACEFIKLMNQRSMTLWDACDEFHIKLLPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQRS' (len=74) ← remove V from position 74
  Step  96: 'ADEHIKLMNPQRAHACEFILMNQRSMTLWDACDEFHIKLLPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQRS' (len=73) ← remove K from position 19
  Step  97: 'ADEHIKLMNPQRAHACEFILMNQRSMTLWDACDEFHIKLLPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQR' (len=72) ← remove S from position 72
  Step  98: 'ADEHIKLMNPQRAHACEFILMNQRSMTLWDACDEFHIKLLPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQ' (len=71) ← remove R from position 71
  Step  99: 'ADEHIKLMNPQRAHACEFILMNQRSTLWDACDEFHIKLLPQRSMVYCQEFGHINQRSTPVWGEFGKLMPQ' (len=70) ← remove M from position 25
  Step 100: 'ADEHIKLMNPQRAHACEFILMNQRSTLWDACDEFHIKLLPQRSMVYCQEFGHINQRSTPVGEFGKLMPQ' (len=69) ← remove W from position 60
  Step 101: 'ADEHIKLMNPQRAHACEFILMNQRSTLWDACDEFIKLLPQRSMVYCQEFGHINQRSTPVGEFGKLMPQ' (len=68) ← remove H from position 34
  Step 102: 'ADEHIKLMNPRAHACEFILMNQRSTLWDACDEFIKLLPQRSMVYCQEFGHINQRSTPVGEFGKLMPQ' (len=67) ← remove Q from position 10
  Step 103: 'ADEHIKLMNPRAYACEFILMNQRSTLWDACDEFIKLLPQRSMVYCQEFGHINQRSTPVGEFGKLMPQ' (len=67) ← replace H at position 12 with Y
  Step 104: 'ADEHIKLMNPRAYACEILMNQRSTLWDACDEFIKLLPQRSMVYCQEFGHINQRSTPVGEFGKLMPQ' (len=66) ← remove F from position 16
  Step 105: 'ADEHIKLMNPRAYACEILMNQRSTLWDACDEFIKLLPQRSMVYCQEFGHINQRSTPVGEFGKLMP' (len=65) ← remove Q from position 65
  Step 106: 'ADEHIKLMNPRAYACEILMNQRSTLWDACDEFIKLLPQRSMVYCQEFGHINQRSTPGEFGKLMP' (len=64) ← remove V from position 56
  Step 107: 'ADEHIKLMNPRAYACEILMNQRSTLWDACDFIKLLPQRSMVYCQEFGHINQRSTPGEFGKLMP' (len=63) ← remove E from position 30
  Step 108: 'ADEHIKLMNPRAYACEILMNQRSTLWDACDQFIKLLPQRSMVYCQEFGHINQRSTPGEFGKLMP' (len=64) ← add Q at position 30
  Step 109: 'ADEHIKLMNPRAYACEILMNQRSTLWDACDQFIKLLPQRSMVYCQEFGHVNQRSTPGEFGKLMP' (len=64) ← replace I at position 49 with V
  Step 110: 'ADEHIKLMNPRAYACEILMNQRSTLWDACDQFIKLLPQRSMVYCQEFGHVNQRSTPGEFGKLM' (len=63) ← remove P from position 63
  Step 111: 'ADEHIKLMNPRAYACEILMNQNRSTLWDACDQFIKLLPQRSMVYCQEFGHVNQRSTPGEFGKLM' (len=64) ← add N at position 21
  Step 112: 'ADEHIKLMNPRAYACEILMNQNRSTLWDACDQFIKLLPQQRSMVYCQEFGHVNQRSTPGEFGKLM' (len=65) ← add Q at position 38
  Step 113: 'ADEHIKLMNPRAYACEILMNQNRSTLWDACDQFIKLLPQQRSMVYCQEFGHVNQRSTPGEFGKL' (len=64) ← remove M from position 64
  Step 114: 'ADEHIKLMNPRAYACEILMNQNRSTWDACDQFIKLLPQQRSMVYCQEFGHVNQRSTPGEFGKL' (len=63) ← remove L from position 25
  Step 115: 'ADEHIKLMNPRAYACEILMNQNRSTWDACDQFIKLLPQQRSMVYCQEFGHVNQRSTPGEFGK' (len=62) ← remove L from position 62
  Step 116: 'ADEHIKLMNPRAYACEILMNQNRSTWDACDQFIKLLPQQRSMVYCQEFGHVNQRSTPGEGK' (len=61) ← remove F from position 59
  Step 117: 'ADEHIKLMNPRAYACEILMNQNRSTWDAACDQFIKLLPQQRSMVYCQEFGHVNQRSTPGEGK' (len=62) ← add A at position 28
  Step 118: 'ADEHIKLMNPRAYACEILMNQNRSTWDAACDQFIKLLPQRSMVYCQEFGHVNQRSTPGEGK' (len=61) ← remove Q from position 39
  Step 119: 'ADEEHIKLMNPRAYACEILMNQNRSTWDAACDQFIKLLPQRSMVYCQEFGHVNQRSTPGEGK' (len=62) ← add E at position 2
  Step 120: 'ADEEHIKLMNPRAYACEILMNQNRSTWDAACDQFIKLLPQRSMVYCQEFGHVNQRSTPGEG' (len=61) ← remove K from position 61
  Step 121: 'ADEEHIKLMNPRAYACEILMNQNRSTWAACDQFIKLLPQRSMVYCQEFGHVNQRSTPGEG' (len=60) ← remove D from position 27
  Step 122: 'ADEEHIKLMNPRAYACEILMNQNRSTWAACDQFKLLPQRSMVYCQEFGHVNQRSTPGEG' (len=59) ← remove I from position 33
  Step 123: 'ADEEHIKLMNPRAYACEILMNQNRSTWAACDQFKLLPQRSMVYCQEFGHVNQRSTPGE' (len=58) ← remove G from position 58
  Step 124: 'ADEEHIKLMPRAYACEILMNQNRSTWAACDQFKLLPQRSMVYCQEFGHVNQRSTPGE' (len=57) ← remove N from position 9
  Step 125: 'ADEEHIKLMPRAYACEILMNQNRSTWAACDQFKLLPQRSMVYCQEFGHVNQRSTPG' (len=56) ← remove E from position 56
  Step 126: 'ADEEHIKLMPRAYACEILMNQNRSTWAACDQFKLLPQRSMVYCQEFGHVNQRSTP' (len=55) ← remove G from position 55
  Step 127: 'ADEEHIKLMPRAYACEILMNQNRSTWAACDQFKLLPQRSMVYCQEFGHVNQRST' (len=54) ← remove P from position 54
  Step 128: 'ADEEHIKLMPRAYACEILMNQNRSTWAACDQFKLLPQRSMVYCQEFGHVNQRS' (len=53) ← remove T from position 53
  Step 129: 'ADEEHIKLMPAYACEILMNQNRSTWAACDQFKLLPQRSMVYCQEFGHVNQRS' (len=52) ← remove R from position 10
  Step 130: 'ADEEHIKLMPAYACLEILMNQNRSTWAACDQFKLLPQRSMVYCQEFGHVNQRS' (len=53) ← add L at position 14
  Step 131: 'ADEEHIKLMPAYACLEILMNQNRSTWAACDQFLLPQRSMVYCQEFGHVNQRS' (len=52) ← remove K from position 32
  Step 132: 'ADEEHIKLMPAYACLEILMNQNRSTWAACDQFLLPQRSMVYCQEFGHVNQR' (len=51) ← remove S from position 51
  Step 133: 'ADEEHIKLMPYACLEILMNQNRSTWAACDQFLLPQRSMVYCQEFGHVNQR' (len=50) ← remove A from position 10
  Step 134: 'ADEEHIKLMPYACLEILMNQNRSTWAACDQFLLPQRSMVYCQEFGHVNQ' (len=49) ← remove R from position 49
  Step 135: 'ADEEHIKLMPYACLEILMNYQNRSTWAACDQFLLPQRSMVYCQEFGHVNQ' (len=50) ← add Y at position 19
  Step 136: 'ADEEHIKLMPYACLEILMNYQNRSTWAACDQFLLPQRSMVYCQEFGHVN' (len=49) ← remove Q from position 49
  Step 137: 'ADEEHIKLMPYACLEILMNYQNRSTWAACDQFLLPQRSMVYCEFGHVN' (len=48) ← remove Q from position 42
  Step 138: 'ADEEHIKLMPYACLEILMNYQNRSTWAACDQFLLPQRSMVYCEFGHV' (len=47) ← remove N from position 47
  Step 139: 'ADEEHIKLMPYACLEILMNYQNRSTWACDQFLLPQRSMVYCEFGHV' (len=46) ← remove A from position 27
  Step 140: 'ADEELIKLMPYACLEILMNYQNRSTWACDQFLLPQRSMVYCEFGHV' (len=46) ← replace H at position 4 with L
  Step 141: 'ADEELIKLMPYACLEILMNYQNRSTWACDQFLLPQRSMVYCEFGH' (len=45) ← remove V from position 45
  Step 142: 'ADEELIKLMPYACLEILMNYQNRSTWACDQFLLPQRSMVYCEFG' (len=44) ← remove H from position 44
  Step 143: 'ADEELIKLMPYACLEILMNYQNRSTWACDQFLLPQRSMVYCEF' (len=43) ← remove G from position 43
  Step 144: 'ADEELIKLMPYACLEILMNYQNRSTWUCDQFLLPQRSMVYCEF' (len=43) ← replace A at position 26 with U
  Step 145: 'ADEELIKLMPYACLEILMNYQNRSTWUCDQFLLPQRSMVYCE' (len=42) ← remove F from position 42
  Step 146: 'ADEELIKLMPYACLEILMNYQNRSTWUCDQFLLPQRSMVYC' (len=41) ← remove E from position 41
  Step 147: 'ADEELIKLMPYACLEILMNYQNRSTWUCDQFLLPQRSMVY' (len=40) ← remove C from position 40
  Step 148: 'ADEELIKLMPYACLEILMNYQNRSTWUCDQFLLPQRSMV' (len=39) ← remove Y from position 39
  Step 149: 'ADEELIKLMPYACLEILMNYQNRSTWUCDQFLLPQRSM' (len=38) ← remove V from position 38
  Step 150: 'ADEELIKLMPYACLEILMNYQNRSTWUCDFLLPQRSM' (len=37) ← remove Q from position 29
  Step 151: 'ADEELIKLMPYACLEILMNYQNRSTWUCDFLSLPQRSM' (len=38) ← add S at position 31
  Step 152: 'ADEELIKMPYACLEILMNYQNRSTWUCDFLSLPQRSM' (len=37) ← remove L from position 7
  Step 153: 'ADEELIKMPYACLEILMNYQNRSTWUCDFLSLPQRS' (len=36) ← remove M from position 36
  Step 154: 'ADEELIKMPYACLEILMNYQNRSTWUCDFLSLPRS' (len=35) ← remove Q from position 33
  Step 155: 'ADEELIKMPYACLEILMNYQNRSTWUCDFLSLPR' (len=34) ← remove S from position 34
  Step 156: 'ADEELIKMPYACLEILMNYQNRSTWUCDLSLPR' (len=33) ← remove F from position 28
  Step 157: 'ADEELIKMPYACLEILMNYQNRSTWUCDLSLP' (len=32) ← remove R from position 32
  Step 158: 'ADEELIKMPYACLEILMNYQNRSTWUCDLSL' (len=31) ← remove P from position 31
  Step 159: 'ADEELIKMPYACLEILMNYQNRSTUCDLSL' (len=30) ← remove W from position 24
  Step 160: 'ADEELIKMPYACLEILMYQNRSTUCDLSL' (len=29) ← remove N from position 17
  Step 161: 'ADEELIKMPYALEILMYQNRSTUCDLSL' (len=28) ← remove C from position 11
  Step 162: 'ADEELIKMPYALEILMYQNRSTUCDLS' (len=27) ← remove L from position 27
  Step 163: 'ADEELIKMPYALEILMYQNRSTUCLS' (len=26) ← remove D from position 24
  Step 164: 'ADEELIKMPYALEILMYQNRSUCLS' (len=25) ← remove T from position 21
  Step 165: 'ADEELIKPYALEILMYQNRSUCLS' (len=24) ← remove M from position 7
  Step 166: 'ADEELIKPYALEILMYQNRUCLS' (len=23) ← remove S from position 19
  Step 167: 'ADEELIKPYALILMYQNRUCLS' (len=22) ← remove E from position 11
  Step 168: 'ADEELIKPYAILMYQNRUCLS' (len=21) ← remove L from position 10
  Step 169: 'ADEELIKPYAILMYMQNRUCLS' (len=22) ← add M at position 14
  Step 170: 'ADEELIKPYAILMYMQNRUCL' (len=21) ← remove S from position 21
  Step 171: 'ADEELIKPYAILMYMQNRUC' (len=20) ← remove L from position 20
  Step 172: 'ADEELYIKPYAILMYMQNRUC' (len=21) ← add Y at position 5
  Step 173: 'ADEEYIKPYAILMYMQNRUC' (len=20) ← remove L from position 4
  Step 174: 'ADEEYIKPYAILMYMQNRU' (len=19) ← remove C from position 19
  Step 175: 'ADEEYIKPYAILMYMQNR' (len=18) ← remove U from position 18
  Step 176: 'ADEEYIKPYAILMYMQN' (len=17) ← remove R from position 17
  Step 177: 'ADEEYIKPYALMYMQN' (len=16) ← remove I from position 10
  Step 178: 'ADEEYIKPYALMYMYQN' (len=17) ← add Y at position 14
  Step 179: 'ADEEYIKPYALMYMYQ' (len=16) ← remove N from position 16
  Step 180: 'ADEEYIKPALMYMYQ' (len=15) ← remove Y from position 8
  Step 181: 'ADEEYIKPALMMYQ' (len=14) ← remove Y from position 11
  Step 182: 'ADEEYIKPALMMY' (len=13) ← remove Q from position 13
  Step 183: 'ADEEYIKPALMM' (len=12) ← remove Y from position 12
  Step 184: 'ADEEYIKPALM' (len=11) ← remove M from position 11
  Step 185: 'ADEEYIKPAM' (len=10) ← remove L from position 9
  Step 186: 'ADEEYFKPAM' (len=10) ← replace I at position 5 with F
  Step 187: 'AEEYFKPAM' (len=9) ← remove D from position 1
  Step 188: 'AEEYFKPA' (len=8) ← remove M from position 8
  Step 189: 'AEEYFKP' (len=7) ← remove A from position 7
  Step 190: 'AEYFKP' (len=6) ← remove E from position 2
  Step 191: 'AEYFKH' (len=6) ← replace P at position 5 with H
  Step 192: 'AEYFK' (len=5) ← remove H from position 5
  Step 193: 'AEYF' (len=4) ← remove K from position 4
  Step 194: 'AEY' (len=3) ← remove F from position 3
  Step 195: 'AY' (len=2) ← remove E from position 1
  Step 196: 'A' (len=1) ← remove Y from position 1
  Step 197: '' (len=0) ← remove A from position 0 [ABSORBING STATE]

Trajectory Metadata:
  {
  "approach": "direct_reverse",
  "config": {
    "correct_action_prob": 0.6,
    "temperature": 0.5,
    "random_action_weights": {
      "add": 0.2,
      "delete": 0.6,
      "replace": 0.2
    }
  },
  "target_length": 150,
  "total_steps": 197,
  "direction": "target_to_absorbing"
}

================================================================================
SUMMARY STATISTICS
================================================================================
Total sequences processed: 3
Success rate: 100.0% (3/3)
Average efficiency: 0.79 chars/step
Min efficiency: 0.74 chars/step
Max efficiency: 0.86 chars/step

Configuration used:
  correct_action_prob: 0.6
  temperature: 0.5
  random_action_weights: {'add': 0.2, 'delete': 0.6, 'replace': 0.2}
  seed: 123

Note: Direct reverse trajectories simulate the discrete diffusion process where 
complete protein sequences are directly degraded to reach an absorbing 
state (empty sequence). This approach avoids the forward-then-reverse method.
