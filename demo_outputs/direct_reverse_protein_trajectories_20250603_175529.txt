DIRECT REVERSE PROTEIN TRAJECTORIES (Target → Empty)
Generated on: 2025-06-03 17:55:29
Configuration: {'correct_action_prob': 0.8, 'temperature': 0.5, 'random_action_weights': {'add': 0.05, 'delete': 0.85, 'replace': 0.1}, 'seed': 123}
Total sequences: 3
Protein charset: ['A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'Y']

Note: These are direct reverse trajectories that go from complete protein to absorbing state.
Each trajectory shows the degradation process without intermediate forward generation.

================================================================================
DIRECT REVERSE TRAJECTORY: UniProtKB_P12345
Original Sequence: ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY
Original Length: 60
Total Steps: 61
Efficiency: 0.98 chars/step
Success: Yes

Full State Progression (Target → Empty):
  Step   0: 'ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY' (len=60) [START]
  Step   1: 'ACDEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY' (len=59) ← remove V from position 17
  Step   2: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY' (len=58) ← remove D from position 2
  Step   3: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVW' (len=57) ← remove Y from position 57
  Step   4: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTV' (len=56) ← remove W from position 56
  Step   5: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRST' (len=55) ← remove V from position 55
  Step   6: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRS' (len=54) ← remove T from position 54
  Step   7: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYCDEFGHIKLMNPQRS' (len=53) ← remove A from position 38
  Step   8: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYCDEFGHIKLMNPQR' (len=52) ← remove S from position 52
  Step   9: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYCDEFGHIKLMNPQ' (len=51) ← remove R from position 51
  Step  10: 'ACEFGHIKLMNPQRSTWYACDEFGHIKLMNPQRSTVWYCDEFGHIKLMNP' (len=50) ← remove Q from position 50
  Step  11: 'ACEFGHIKLMNPQRSTWYCDEFGHIKLMNPQRSTVWYCDEFGHIKLMNP' (len=49) ← remove A from position 18
  Step  12: 'ACEFGHIKLMNPQRSTYCDEFGHIKLMNPQRSTVWYCDEFGHIKLMNP' (len=48) ← remove W from position 16
  Step  13: 'ACEFGHIKLMNPQRSTYCDEFGHIKLMNPQRSTVWYCDEFGHIKLMN' (len=47) ← remove P from position 47
  Step  14: 'ACEFGHIKLMNPQRSTYCDEFGHIKLMNPQRSTVWYCDEFGHIKLM' (len=46) ← remove N from position 46
  Step  15: 'ACEFGHIKLMNPQRSTYCDEFGHIKLMNPQRSTVWYCDEFHIKLM' (len=45) ← remove G from position 40
  Step  16: 'ACEFGHIKLMNPQRSTYCDEGHIKLMNPQRSTVWYCDEFHIKLM' (len=44) ← remove F from position 20
  Step  17: 'ACEFGHIKLMNPQRSTYCDEGHIKLMNPQRSTVWYCDEFHILM' (len=43) ← remove K from position 41
  Step  18: 'ACEFGHIKLMNPQRSTYCDEGHIKLMNPQRSTVWYCDEFHIL' (len=42) ← remove M from position 42
  Step  19: 'ACEFGHIKLMNPQRSTYCDEGHIKLMNPQRSTVWYCDEFHI' (len=41) ← remove L from position 41
  Step  20: 'ACEFGHIKLMNPQRSTYCDEGHIKLMPQRSTVWYCDEFHI' (len=40) ← remove N from position 26
  Step  21: 'ACEFGHIKLMNPQRSTYCDEGHIKLMPQRSTVWYCDEFH' (len=39) ← remove I from position 39
  Step  22: 'ACEFGHIKLMNPQRSTYCDEGHIKLMPQRSTVWYCDEF' (len=38) ← remove H from position 38
  Step  23: 'ACEFGHIKLMNPQRSTGCDEGHIKLMPQRSTVWYCDEF' (len=38) ← replace Y at position 16 with G
  Step  24: 'ACEFGHIKLMNPQRSTGCDEGHIKLMPQRSTVWYCDE' (len=37) ← remove F from position 37
  Step  25: 'ACEFGHIKLMNPQRSTGCDEGHIKMPQRSTVWYCDE' (len=36) ← remove L from position 24
  Step  26: 'ACEFGHIKLMNPQRSTGCDEGHIKMPQRSTVWYCD' (len=35) ← remove E from position 35
  Step  27: 'ACEFGHIKLMNPQRSTGCDEGHIKMPQRSTVWYC' (len=34) ← remove D from position 34
  Step  28: 'ACEFGHIKLMNPQRSTGCDEGHIKMPQRSTVWY' (len=33) ← remove C from position 33
  Step  29: 'ACEFGHIKLMNPQRSTGCDEGHIKMPQRSTVW' (len=32) ← remove Y from position 32
  Step  30: 'ACEGHIKLMNPQRSTGCDEGHIKMPQRSTVW' (len=31) ← remove F from position 3
  Step  31: 'ACEGHIKLMNPQRSTGCDEGHIKMPQRSTV' (len=30) ← remove W from position 30
  Step  32: 'ACEGHIKLMNPQRSTGCEGHIKMPQRSTV' (len=29) ← remove D from position 17
  Step  33: 'ACEGHIKLMNPQRSTGCEGHIKMPQRST' (len=28) ← remove V from position 28
  Step  34: 'ACEGHIKLMNPQRSTGCEGHIKMPRST' (len=27) ← remove Q from position 24
  Step  35: 'ACEGHIKLMNPQRSTGCEGHIKMPRS' (len=26) ← remove T from position 26
  Step  36: 'ACEGHIKLMNPQRSTGCEGHIKMPR' (len=25) ← remove S from position 25
  Step  37: 'ACEGHIKLMNPQRSTGCEGHIKMP' (len=24) ← remove R from position 24
  Step  38: 'CEGHIKLMNPQRSTGCEGHIKMP' (len=23) ← remove A from position 0
  Step  39: 'CEGHIKLMNPQRSTGCEGHIKM' (len=22) ← remove P from position 22
  Step  40: 'CEGHIKLMNPQRSTGCEGHIK' (len=21) ← remove M from position 21
  Step  41: 'CEGHIKLMNPQRSTGCEGHI' (len=20) ← remove K from position 20
  Step  42: 'CEGHIKLMNPQRSTGCEGH' (len=19) ← remove I from position 19
  Step  43: 'CEGHIKLMNPQRSTGCEG' (len=18) ← remove H from position 18
  Step  44: 'CEGHIKLMNPQRSTGEG' (len=17) ← remove C from position 15
  Step  45: 'CEGHIKLMNPQRSTGE' (len=16) ← remove G from position 16
  Step  46: 'CEGHIKLMNPQRSTG' (len=15) ← remove E from position 15
  Step  47: 'CEGHIKLMNPQRST' (len=14) ← remove G from position 14
  Step  48: 'CEGHIKLMNPQRS' (len=13) ← remove T from position 13
  Step  49: 'CEGHIKLMNPQR' (len=12) ← remove S from position 12
  Step  50: 'CEGHIKLMNPQ' (len=11) ← remove R from position 11
  Step  51: 'CEGHIKLMPQ' (len=10) ← remove N from position 8
  Step  52: 'CEGHIKLMP' (len=9) ← remove Q from position 9
  Step  53: 'CEGHKLMP' (len=8) ← remove I from position 4
  Step  54: 'CEGHKLM' (len=7) ← remove P from position 7
  Step  55: 'CEGHLM' (len=6) ← remove K from position 4
  Step  56: 'CEGHL' (len=5) ← remove M from position 5
  Step  57: 'CEGH' (len=4) ← remove L from position 4
  Step  58: 'CEG' (len=3) ← remove H from position 3
  Step  59: 'CE' (len=2) ← remove G from position 2
  Step  60: 'C' (len=1) ← remove E from position 1
  Step  61: '' (len=0) ← remove C from position 0 [ABSORBING STATE]

Trajectory Metadata:
  {
  "approach": "direct_reverse",
  "config": {
    "correct_action_prob": 0.8,
    "temperature": 0.5,
    "random_action_weights": {
      "add": 0.05,
      "delete": 0.85,
      "replace": 0.1
    }
  },
  "target_length": 60,
  "total_steps": 61,
  "direction": "target_to_absorbing"
}

================================================================================
DIRECT REVERSE TRAJECTORY: PDB_1ABC
Original Sequence: MPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKL
Original Length: 80
Total Steps: 81
Efficiency: 0.99 chars/step
Success: Yes

Full State Progression (Target → Empty):
  Step   0: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKL' (len=80) [START]
  Step   1: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSUVWYACDEFGHIKL' (len=79) ← remove T from position 65
  Step   2: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSUVWYACDEFGHIK' (len=78) ← remove L from position 78
  Step   3: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFGHIKLMPQRSTUVWYACDEFGHIKLMPQRSUVWYACDEFGHIK' (len=77) ← remove W from position 28
  Step   4: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFHIKLMPQRSTUVWYACDEFGHIKLMPQRSUVWYACDEFGHIK' (len=76) ← remove G from position 34
  Step   5: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFHIKLMPQSTUVWYACDEFGHIKLMPQRSUVWYACDEFGHIK' (len=75) ← remove R from position 41
  Step   6: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFHIKLMPQSTUVWYACDEFGHIKLMPQRSUVWYACDEFGHI' (len=74) ← remove K from position 74
  Step   7: 'MPQRSTUVWYACDEFGHIKLMPQRSTUVYACDEFHIKLMPQSTUVWYACDEFGHIKLMPQRSUVYACDEFGHI' (len=73) ← remove W from position 64
  Step   8: 'MPRSTUVWYACDEFGHIKLMPQRSTUVYACDEFHIKLMPQSTUVWYACDEFGHIKLMPQRSUVYACDEFGHI' (len=72) ← remove Q from position 2
  Step   9: 'MPRSTUVWYACDEFGHIKLMPQRSTUVYACDEFHIKLMPQSTUVWYACDEFGHIKLMPQRSUVYACDEFGH' (len=71) ← remove I from position 71
  Step  10: 'MPRSTUVWYACDEFGHIKLMPQRSTUVYACDEFHIKLMPQSTUVWYACDEFGHIKLMPQRSUVYACDEFG' (len=70) ← remove H from position 70
  Step  11: 'MPRSTUVWYACDEFGHIKLMPQRSTUVYACDEFHIKLMPQSTUVWYACDEFGHIKLMPQRSUVYACDEF' (len=69) ← remove G from position 69
  Step  12: 'MPRSTUVWYACDEFGHIKLMPQRSTUVYACDEFHIKLMPQSTUVWYACDEFGHIKLMPQRSUVYACDE' (len=68) ← remove F from position 68
  Step  13: 'MPRSTUVWYACDEFGHIKLMPQRSTUVYACDEFHIKLMPQSTUVWYACDEFGHIKLMPQRSUVACDE' (len=67) ← remove Y from position 63
  Step  14: 'MPRSTUVWYACDEFGHIKLMPQRSTUVYACDEHIKLMPQSTUVWYACDEFGHIKLMPQRSUVACDE' (len=66) ← remove F from position 32
  Step  15: 'MPRSTUVWYACDEFGHIKLMPQRSTUVYACDEHIKLMPQSTUVWYACDEFGHIKLMPQRSUVCDE' (len=65) ← remove A from position 62
  Step  16: 'MPRSTUVWYACDEFGHIKLMPQRSTUVYACDEHIKLMPQSTUVWYACDEFGHIKLMPQRSUVCD' (len=64) ← remove E from position 64
  Step  17: 'PRSTUVWYACDEFGHIKLMPQRSTUVYACDEHIKLMPQSTUVWYACDEFGHIKLMPQRSUVCD' (len=63) ← remove M from position 0
  Step  18: 'PRSTUVWYACDEFGHIKLMPQRSTUVYACDEHIKLMPQTUVWYACDEFGHIKLMPQRSUVCD' (len=62) ← remove S from position 38
  Step  19: 'PRSTUVWYACDEFGHIKLMPQRSTUVYACDEHIKLMPTUVWYACDEFGHIKLMPQRSUVCD' (len=61) ← remove Q from position 37
  Step  20: 'PRSTUVWYACDEFGHIKLMPQRSTUVYACDEHIKLMPTUVWYACDEFGHIKLMPQRSUVC' (len=60) ← remove D from position 60
  Step  21: 'PRSTUVWYACDEFGHIKLMPQRSTUVYACDEHIKLMPTUVWYCDEFGHIKLMPQRSUVC' (len=59) ← remove A from position 42
  Step  22: 'PRSTUVWYACDEFHIKLMPQRSTUVYACDEHIKLMPTUVWYCDEFGHIKLMPQRSUVC' (len=58) ← remove G from position 13
  Step  23: 'PRSTUVWYACDEFHIKLMPQRSTUVYACDEHIKLMPTUVWYCDEFGHIKLMPQRSUV' (len=57) ← remove C from position 57
  Step  24: 'PRSTUVWYACDFHIKLMPQRSTUVYACDEHIKLMPTUVWYCDEFGHIKLMPQRSUV' (len=56) ← remove E from position 11
  Step  25: 'PRSTUVWYACDFHIKLMPQRSTUVYACDEHIKLMPTUVWYCDEFGHIKLMPQRUV' (len=55) ← remove S from position 53
  Step  26: 'PRSTUVWYACDFHIKLMPQRSTUVYACDEHIKLMPTUVWYCDEFGHIKLMPQRU' (len=54) ← remove V from position 54
  Step  27: 'PRSTUVWYACDFHIKLMPQRSTUVYACDEHIKLMPUVWYCDEFGHIKLMPQRU' (len=53) ← remove T from position 35
  Step  28: 'PRSTUVWYACDFHIKLMPQRSTUVYACDEHIKLMPUVWYCDEFGHIKLMPQR' (len=52) ← remove U from position 52
  Step  29: 'PRSTUVWYACDFHIKLMPQRSTUVYACDEHIKMPUVWYCDEFGHIKLMPQR' (len=51) ← remove L from position 32
  Step  30: 'PRSTUVWYACDFHIKLMPQRSTUVACDEHIKMPUVWYCDEFGHIKLMPQR' (len=50) ← remove Y from position 24
  Step  31: 'PRSTUVWYACDFHIKLMPQRSTUVACDEHIKMPUVWYCDEFGHIKLMPQ' (len=49) ← remove R from position 49
  Step  32: 'PRSTUVWYACDFHIKLMPQRSTUVACDEHIKMPUVWYCDEFGHIKLMP' (len=48) ← remove Q from position 48
  Step  33: 'PRSTUVWYACDFHIKLMPQRSTUVACDEHIKMPUVWYCDEFHIKLMP' (len=47) ← remove G from position 41
  Step  34: 'PRSTUVWYACDFHIKLMPQRSTUVACDEHIKMPUVWCDEFHIKLMP' (len=46) ← remove Y from position 36
  Step  35: 'PRSTUVWYACDFHIKLMPQRSTUVACDEHIKMPUVWCDEFHIKLM' (len=45) ← remove P from position 45
  Step  36: 'PRSTUVWYACDFHIKLMPQRSTUVACDEHIKMPUVWCDEFHIKL' (len=44) ← remove M from position 44
  Step  37: 'PRSUVWYACDFHIKLMPQRSTUVACDEHIKMPUVWCDEFHIKL' (len=43) ← remove T from position 3
  Step  38: 'PRSUVWYACDFHIKLMPQRSTUVACDEHIKMPUVWCDEFHIK' (len=42) ← remove L from position 42
  Step  39: 'PRSUVWYACDFHIKLMPQRSTUVACDEHIKPUVWCDEFHIK' (len=41) ← remove M from position 30
  Step  40: 'PRSUVWYACDFHIKLMPQRSTUVACDEHIKPUVWCDEFHI' (len=40) ← remove K from position 40
  Step  41: 'PRSUVWYACDFHIKLMPQRSTUVACDEHIKPUVWCDEFH' (len=39) ← remove I from position 39
  Step  42: 'PRSUVWYACDFHIKLMPQRSTUVACDEHIKPUVWCDEF' (len=38) ← remove H from position 38
  Step  43: 'PRSUVWYACDFHIKLMPQSTUVACDEHIKPUVWCDEF' (len=37) ← remove R from position 18
  Step  44: 'RSUVWYACDFHIKLMPQSTUVACDEHIKPUVWCDEF' (len=36) ← remove P from position 0
  Step  45: 'RSUVWYACDFHIKLMPQSTUVACDEHIKPUVWCDE' (len=35) ← remove F from position 35
  Step  46: 'RSUVWYACDFHIKLMPQSTUVACDEHIKPUVWCD' (len=34) ← remove E from position 34
  Step  47: 'RSUVWYACDFHIKLMPQSTUVACDEHIKPUVWC' (len=33) ← remove D from position 33
  Step  48: 'RSUVWYACDFHIKLMPQSTUVACDEHIKPUVW' (len=32) ← remove C from position 32
  Step  49: 'RSUVWYACDFHIKLMPQSTUVACDEHIKPUV' (len=31) ← remove W from position 31
  Step  50: 'RSUVWYACDFHIKLMPQSTUVACDEHIKPU' (len=30) ← remove V from position 30
  Step  51: 'RSUVWYACDFHIKLMPQSTUVACDEHIKP' (len=29) ← remove U from position 29
  Step  52: 'RSUVWYACDFHIKLMPQSTUVACDEHIK' (len=28) ← remove P from position 28
  Step  53: 'RSUVWYADFHIKLMPQSTUVACDEHIK' (len=27) ← remove C from position 7
  Step  54: 'RSUVWYADFHIKLMPQSTUVACDEHI' (len=26) ← remove K from position 26
  Step  55: 'RSUVWYADFHIKLMPQSTUVACDEH' (len=25) ← remove I from position 25
  Step  56: 'RSUVWYADFHIKLMPQSTUVACDE' (len=24) ← remove H from position 24
  Step  57: 'RSUVWYADFHIKMPQSTUVACDE' (len=23) ← remove L from position 12
  Step  58: 'RSUWYADFHIKMPQSTUVACDE' (len=22) ← remove V from position 3
  Step  59: 'RSUWYADFHIKMPQSTUVACD' (len=21) ← remove E from position 21
  Step  60: 'RSUWYADFHIKMPQSTUVAC' (len=20) ← remove D from position 20
  Step  61: 'RSUWYADFHIKMPQSTUVA' (len=19) ← remove C from position 19
  Step  62: 'RIUWYADFHIKMPQSTUVA' (len=19) ← replace S at position 1 with I
  Step  63: 'IUWYADFHIKMPQSTUVA' (len=18) ← remove R from position 0
  Step  64: 'IUWYADFHIKMPQSTUV' (len=17) ← remove A from position 17
  Step  65: 'IUWYADFHIKMPQSTU' (len=16) ← remove V from position 16
  Step  66: 'IUWYADFHIKMPQTU' (len=15) ← remove S from position 13
  Step  67: 'IUWYDFHIKMPQTU' (len=14) ← remove A from position 4
  Step  68: 'IUWYDFHIKMPQT' (len=13) ← remove U from position 13
  Step  69: 'UWYDFHIKMPQT' (len=12) ← remove I from position 0
  Step  70: 'UWYDFHIMPQT' (len=11) ← remove K from position 7
  Step  71: 'UWYDFHIMPQ' (len=10) ← remove T from position 10
  Step  72: 'UWYDFHIMP' (len=9) ← remove Q from position 9
  Step  73: 'UWYDFHIM' (len=8) ← remove P from position 8
  Step  74: 'WYDFHIM' (len=7) ← remove U from position 0
  Step  75: 'WYDFHI' (len=6) ← remove M from position 6
  Step  76: 'WYDFH' (len=5) ← remove I from position 5
  Step  77: 'WYFH' (len=4) ← remove D from position 2
  Step  78: 'WYF' (len=3) ← remove H from position 3
  Step  79: 'WY' (len=2) ← remove F from position 2
  Step  80: 'Y' (len=1) ← remove W from position 0
  Step  81: '' (len=0) ← remove Y from position 0 [ABSORBING STATE]

Trajectory Metadata:
  {
  "approach": "direct_reverse",
  "config": {
    "correct_action_prob": 0.8,
    "temperature": 0.5,
    "random_action_weights": {
      "add": 0.05,
      "delete": 0.85,
      "replace": 0.1
    }
  },
  "target_length": 80,
  "total_steps": 81,
  "direction": "target_to_absorbing"
}

================================================================================
DIRECT REVERSE TRAJECTORY: SwissProt_Q8XYZ
Original Sequence: ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKL
Original Length: 150
Total Steps: 155
Efficiency: 0.97 chars/step
Success: Yes

Full State Progression (Target → Empty):
  Step   0: 'ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKL' (len=150) [START]
  Step   1: 'ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKL' (len=149) ← remove P from position 52
  Step   2: 'ACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKL' (len=148) ← remove P from position 32
  Step   3: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKL' (len=147) ← remove C from position 1
  Step   4: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKL' (len=146) ← remove I from position 84
  Step   5: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIK' (len=145) ← remove L from position 145
  Step   6: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHIK' (len=144) ← remove I from position 103
  Step   7: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHI' (len=143) ← remove K from position 143
  Step   8: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGH' (len=142) ← remove I from position 142
  Step   9: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFG' (len=141) ← remove H from position 141
  Step  10: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEFG' (len=140) ← remove F from position 61
  Step  11: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDEF' (len=139) ← remove G from position 139
  Step  12: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACDE' (len=138) ← remove F from position 138
  Step  13: 'ADEFGHIKLMNPQRSTVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACD' (len=137) ← remove E from position 137
  Step  14: 'ADEFGHIKLMNPQRSVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYACD' (len=136) ← remove T from position 15
  Step  15: 'ADEFGHIKLMNPQRSVWYACDEFGHIKLMNQRSTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYAC' (len=135) ← remove D from position 135
  Step  16: 'ADEFGHIKLMNPQRSVWYACDEFGHIKLMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYAC' (len=134) ← remove S from position 32
  Step  17: 'ADEFGHIKLMNPQRSVWYACDEFGHIKLMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYA' (len=133) ← remove C from position 133
  Step  18: 'ADEFGHIKLMNPQRSVWYACDEFGHIKLMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYA' (len=132) ← remove Q from position 67
  Step  19: 'ADEFGHIKLMNPQRSVWYACDEFGHIKLMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPRSTVWYACDEFGHKLMNPQRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYA' (len=132) ← replace D at position 94 with P
  Step  20: 'ADEFGHIKMNPQRSVWYACDEFGHIKLMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPRSTVWYACDEFGHKLMNPQRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWYA' (len=131) ← remove L from position 8
  Step  21: 'ADEFGHIKMNPQRSVWYACDEFGHIKLMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPRSTVWYACDEFGHKLMNPQRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVWY' (len=130) ← remove A from position 130
  Step  22: 'ADEFGHIKMNPQRSVWYACDEFGHIKLMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPRSTVWYACDEFGHKLMNPQRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVW' (len=129) ← remove Y from position 129
  Step  23: 'ADEFGHIKMNPQRSVWYACDEFGHIKLMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPRSTVWYACDFGHKLMNPQRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVW' (len=128) ← remove E from position 75
  Step  24: 'ADEFGHIKMNPQRSVWYACDEFGHIKLMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPRSTVWYACDFGHKLMNPRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTVW' (len=127) ← remove Q from position 83
  Step  25: 'ADEFGHIKMNPQRSVWYACDEFGHIKLMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPRSTVWYACDFGHKLMNPRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTV' (len=126) ← remove W from position 126
  Step  26: 'ADEFGHIKMNPQRSVWYACDEFGHILMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPRSTVWYACDFGHKLMNPRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRSTV' (len=125) ← remove K from position 25
  Step  27: 'ADEFGHIKMNPQRSVWYACDEFGHILMNQRTVWYACDEFGHIKLMNQRSTVWYACDEGHIKLMNPRSTVWYACDFGHKLMNPRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRST' (len=124) ← remove V from position 124
  Step  28: 'ADEFGHIKMNPQRSVWYACDEFGHILMNQRTVWYACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLMNPRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRST' (len=123) ← remove C from position 54
  Step  29: 'ADEFGHIKMNPQRSVWYACDEFGHILMNQRTVWYACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLMNPRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRS' (len=122) ← remove T from position 122
  Step  30: 'ADEFGHIKMNPQRSVWYACDQFGHILMNQRTVWYACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLMNPRSTVWYACPEFGHKLMNPQRSTVWYACDEFGHIKLMNPQRS' (len=122) ← replace E at position 20 with Q
  Step  31: 'ADEFGHIKMNPQRSVWYACDQFGHILMNQRTVWYACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLMNPRSTVWYACPEFGHKLMPQRSTVWYACDEFGHIKLMNPQRS' (len=121) ← remove N from position 97
  Step  32: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWYACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLMNPRSTVWYACPEFGHKLMPQRSTVWYACDEFGHIKLMNPQRS' (len=120) ← remove Q from position 20
  Step  33: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWYACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLMNPRSTVWYACPEFGHKLMPQRSTVWYACDEFGHIKLMNPQR' (len=119) ← remove S from position 119
  Step  34: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWYACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYACPEFGHKLMPQRSTVWYACDEFGHIKLMNPQR' (len=118) ← remove M from position 77
  Step  35: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWYACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYACPEFGHKLMPQRSTVWYACDEFGHIKLMNPQ' (len=117) ← remove R from position 117
  Step  36: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYACPEFGHKLMPQRSTVWYACDEFGHIKLMNPQ' (len=116) ← remove Y from position 32
  Step  37: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYACPEFGHKLMPQRSTVWYACDEFGHIKLMNP' (len=115) ← remove Q from position 115
  Step  38: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYACPEFGHKLMPQRSTVWYACDEFGHIKLMN' (len=114) ← remove P from position 114
  Step  39: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLMNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEFGHIKLMN' (len=113) ← remove C from position 85
  Step  40: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEFGHIKLMN' (len=112) ← remove M from position 42
  Step  41: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEFGHIKLN' (len=111) ← remove M from position 110
  Step  42: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEFGHIKL' (len=110) ← remove N from position 110
  Step  43: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEFGHIK' (len=109) ← remove L from position 109
  Step  44: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEFGHI' (len=108) ← remove K from position 108
  Step  45: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLNQRSTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEFGH' (len=107) ← remove I from position 107
  Step  46: 'ADEFGHIKMNPQRSVWYACDFGHILMNQRTVWACDEFGHIKLNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEFGH' (len=106) ← remove S from position 45
  Step  47: 'ADEFGHIKMNPQRSVWACDFGHILMNQRTVWACDEFGHIKLNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEFGH' (len=105) ← remove Y from position 16
  Step  48: 'ADEFGHIKMNPQRSVWACDFGHILMNQRTVWACDEFGHIKLNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEFG' (len=104) ← remove H from position 104
  Step  49: 'ADEFGHIKMNPQRSVWACDFGHILMNQRTVWACDEFGHIKLNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEF' (len=103) ← remove G from position 103
  Step  50: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKLNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDEF' (len=102) ← remove P from position 10
  Step  51: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKLNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDE' (len=101) ← remove F from position 101
  Step  52: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACDE' (len=100) ← remove L from position 39
  Step  53: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYACD' (len=99) ← remove E from position 99
  Step  54: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYAC' (len=98) ← remove D from position 98
  Step  55: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWYA' (len=97) ← remove C from position 97
  Step  56: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVWY' (len=96) ← remove A from position 96
  Step  57: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTVW' (len=95) ← remove Y from position 95
  Step  58: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRSTV' (len=94) ← remove W from position 94
  Step  59: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLMNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRST' (len=93) ← remove V from position 93
  Step  60: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRST' (len=92) ← remove M from position 54
  Step  61: 'ADEFGHIKMNQRSVWACDFGHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRS' (len=91) ← remove T from position 91
  Step  62: 'ADEFGHIKMNQRSVWACDFHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQRS' (len=90) ← remove G from position 19
  Step  63: 'ADEFGHIKMNQRSVWACDFHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLNPRSTVWYACDFGHKLNPRSTVWYAPEFGHKLMPQR' (len=89) ← remove S from position 89
  Step  64: 'ADEFGHIKMNQRSVWACDFHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLNPRSVWYACDFGHKLNPRSTVWYAPEFGHKLMPQR' (len=88) ← remove T from position 57
  Step  65: 'ADEFGHIKMNQRSVWACDFHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLNPRSVWYACDFGHKLNPRSTVWYAPEFGHKLMPQ' (len=87) ← remove R from position 87
  Step  66: 'ADEFGHIKMNQRSVWACDFHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLNPSVWYACDFGHKLNPRSTVWYAPEFGHKLMPQ' (len=86) ← remove R from position 55
  Step  67: 'ADEFGHIKMQRSVWACDFHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLNPSVWYACDFGHKLNPRSTVWYAPEFGHKLMPQ' (len=85) ← remove N from position 9
  Step  68: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRTVWYADEGHIKLNPSVWYACDFGHKLNPRSTVWYAPEFGHKLMPQ' (len=84) ← remove S from position 11
  Step  69: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPSVWYACDFGHKLNPRSTVWYAPEFGHKLMPQ' (len=83) ← remove T from position 39
  Step  70: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPSVWYACDFGHKLNPRSTVWYAPEFGHKLMP' (len=82) ← remove Q from position 82
  Step  71: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPSVWYACDFGHKLNPRTVWYAPEFGHKLMP' (len=81) ← remove S from position 67
  Step  72: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWYAPEFGHKLMP' (len=80) ← remove S from position 52
  Step  73: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWYAPEFGHKLP' (len=79) ← remove M from position 78
  Step  74: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWYAPEFGHKL' (len=78) ← remove P from position 78
  Step  75: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWYAPEFGHK' (len=77) ← remove L from position 77
  Step  76: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWYAPEFGH' (len=76) ← remove K from position 76
  Step  77: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWYAPEFG' (len=75) ← remove H from position 75
  Step  78: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWYAPEF' (len=74) ← remove G from position 74
  Step  79: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWYAPE' (len=73) ← remove F from position 73
  Step  80: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWYAP' (len=72) ← remove E from position 72
  Step  81: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWYA' (len=71) ← remove P from position 71
  Step  82: 'ADEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWY' (len=70) ← remove A from position 70
  Step  83: 'DEFGHIKMQRVWACDFHILMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWY' (len=69) ← remove A from position 0
  Step  84: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVWY' (len=68) ← remove L from position 18
  Step  85: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTVW' (len=67) ← remove Y from position 67
  Step  86: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLNPRTV' (len=66) ← remove W from position 66
  Step  87: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLPRTV' (len=65) ← remove N from position 61
  Step  88: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLPRT' (len=64) ← remove V from position 64
  Step  89: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLNPVWYACDFGHKLPR' (len=63) ← remove T from position 63
  Step  90: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLPVWYACDFGHKLPR' (len=62) ← remove N from position 48
  Step  91: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLPVWYACDFGHKLP' (len=61) ← remove R from position 61
  Step  92: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLPVWYACDFGHKL' (len=60) ← remove P from position 60
  Step  93: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLPVWYACDFGHK' (len=59) ← remove L from position 59
  Step  94: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLPVWYACDFGH' (len=58) ← remove K from position 58
  Step  95: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLPVWYACDFG' (len=57) ← remove H from position 57
  Step  96: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLPVWYACDF' (len=56) ← remove G from position 56
  Step  97: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHIKNQRVWYADEGHIKLPVWYACD' (len=55) ← remove F from position 55
  Step  98: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHKNQRVWYADEGHIKLPVWYACD' (len=54) ← remove I from position 32
  Step  99: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHKNQRVWYADEGHIKLPVWYAC' (len=53) ← remove D from position 53
  Step 100: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGHKNQRVWYADEGHIKLPIWYAC' (len=53) ← replace V at position 48 with I
  Step 101: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGKNQRVWYADEGHIKLPIWYAC' (len=52) ← remove H from position 31
  Step 102: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGKNQRVWYADGHIKLPIWYAC' (len=51) ← remove E from position 40
  Step 103: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGKNQRVWYADGHIKLPIWAC' (len=50) ← remove Y from position 48
  Step 104: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGKNQRVWYADGHIKLPIWA' (len=49) ← remove C from position 49
  Step 105: 'DEFGHIKMQRVWACDFHIMNQRTVWACDEFGKNQRVWYADGHIKLPIW' (len=48) ← remove A from position 48
  Step 106: 'DEFHIKMQRVWACDFHIMNQRTVWACDEFGKNQRVWYADGHIKLPIW' (len=47) ← remove G from position 3
  Step 107: 'DEFHIKMQRWACDFHIMNQRTVWACDEFGKNQRVWYADGHIKLPIW' (len=46) ← remove V from position 9
  Step 108: 'DEFHIKMQRWACDFHIMNQRTVWACDEFGKNQRVWYADGHIKLPI' (len=45) ← remove W from position 45
  Step 109: 'DEFHIKMQRWACDFHIMNQRTVWACDEFGKNQRVWYADGHIKLP' (len=44) ← remove I from position 44
  Step 110: 'DEFHIKMQRWACDFHIMNQRTVWACDEFGKNQRVWYADGHIKL' (len=43) ← remove P from position 43
  Step 111: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNQRVWYADGHIKL' (len=42) ← remove I from position 15
  Step 112: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNQRVWYADGHIK' (len=41) ← remove L from position 41
  Step 113: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNQRVWYADGHI' (len=40) ← remove K from position 40
  Step 114: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNQRVWYADGH' (len=39) ← remove I from position 39
  Step 115: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNQRWYADGH' (len=38) ← remove V from position 32
  Step 116: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNQRWYADG' (len=37) ← remove H from position 37
  Step 117: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNRWYADG' (len=36) ← remove Q from position 30
  Step 118: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNRWYAD' (len=35) ← remove G from position 35
  Step 119: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNRWYA' (len=34) ← remove D from position 34
  Step 120: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNRWY' (len=33) ← remove A from position 33
  Step 121: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNRW' (len=32) ← remove Y from position 32
  Step 122: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKNR' (len=31) ← remove W from position 31
  Step 123: 'DEFHIKMQRWACDFHMNQRTVWACDEFGKN' (len=30) ← remove R from position 30
  Step 124: 'DEFHIKMQRWACDFHMNQRTVWACDEFGK' (len=29) ← remove N from position 29
  Step 125: 'DEFHIKMQRWACDFHMNQRTVWACEFGK' (len=28) ← remove D from position 24
  Step 126: 'DEFHIKMQRWACDFHMNQRTVACEFGK' (len=27) ← remove W from position 21
  Step 127: 'DEFHIKMQRWACDFHMNQRTVACEFG' (len=26) ← remove K from position 26
  Step 128: 'DEFHIKMQRWACDFHMNQRTVACEF' (len=25) ← remove G from position 25
  Step 129: 'DEFHIKMQRWACDFHMNQRTVACE' (len=24) ← remove F from position 24
  Step 130: 'DEFHIKMQRWACDFHMNQRTVAC' (len=23) ← remove E from position 23
  Step 131: 'DEFHIKMQNWACDFHMNQRTVAC' (len=23) ← replace R at position 8 with N
  Step 132: 'DEFHIKMQNWACDFHMNQRTVA' (len=22) ← remove C from position 22
  Step 133: 'DEFIKMQNWACDFHMNQRTVA' (len=21) ← remove H from position 3
  Step 134: 'EFIKMQNWACDFHMNQRTVA' (len=20) ← remove D from position 0
  Step 135: 'EFIKMQNWACDFHMNQRTV' (len=19) ← remove A from position 19
  Step 136: 'EFIKMQNWACDFHMNQRT' (len=18) ← remove V from position 18
  Step 137: 'EFIKMQNWACDFHMNQR' (len=17) ← remove T from position 17
  Step 138: 'EFIKMQNWACDFHMNQ' (len=16) ← remove R from position 16
  Step 139: 'EFIKMQNWACDFHMN' (len=15) ← remove Q from position 15
  Step 140: 'EFIKMQNWACDFHM' (len=14) ← remove N from position 14
  Step 141: 'EFIKMQNWACDFH' (len=13) ← remove M from position 13
  Step 142: 'EFIKMQNWACDF' (len=12) ← remove H from position 12
  Step 143: 'EFIKMQNWACD' (len=11) ← remove F from position 11
  Step 144: 'EFIKMQNWAC' (len=10) ← remove D from position 10
  Step 145: 'EFIKMQSWAC' (len=10) ← replace N at position 6 with S
  Step 146: 'EFIKMQSWA' (len=9) ← remove C from position 9
  Step 147: 'EFIKMQSW' (len=8) ← remove A from position 8
  Step 148: 'EFIKMQS' (len=7) ← remove W from position 7
  Step 149: 'EFKMQS' (len=6) ← remove I from position 2
  Step 150: 'EFKMQ' (len=5) ← remove S from position 5
  Step 151: 'EFKM' (len=4) ← remove Q from position 4
  Step 152: 'EFK' (len=3) ← remove M from position 3
  Step 153: 'EF' (len=2) ← remove K from position 2
  Step 154: 'E' (len=1) ← remove F from position 1
  Step 155: '' (len=0) ← remove E from position 0 [ABSORBING STATE]

Trajectory Metadata:
  {
  "approach": "direct_reverse",
  "config": {
    "correct_action_prob": 0.8,
    "temperature": 0.5,
    "random_action_weights": {
      "add": 0.05,
      "delete": 0.85,
      "replace": 0.1
    }
  },
  "target_length": 150,
  "total_steps": 155,
  "direction": "target_to_absorbing"
}

================================================================================
SUMMARY STATISTICS
================================================================================
Total sequences processed: 3
Success rate: 100.0% (3/3)
Average efficiency: 0.98 chars/step
Min efficiency: 0.97 chars/step
Max efficiency: 0.99 chars/step

Configuration used:
  correct_action_prob: 0.8
  temperature: 0.5
  random_action_weights: {'add': 0.05, 'delete': 0.85, 'replace': 0.1}
  seed: 123

Note: Direct reverse trajectories simulate the discrete diffusion process where 
complete protein sequences are directly degraded to reach an absorbing 
state (empty sequence). This approach avoids the forward-then-reverse method.
