============================================================
POINT MUTATION NOISE
============================================================
Generated 3 forward and 3 backward trajectories

Sample backward trajectory:
Corrupted seq: MKFQVLLFNIHILFPVLA
Original seq: MKFLVLLFNILCLFPVLA
  Step 1: replace_residue({'action_type': 'replace_residue', 'position': 3, 'new_amino_acid': 'L', 'old_amino_acid': None})
  Step 2: replace_residue({'action_type': 'replace_residue', 'position': 10, 'new_amino_acid': 'L', 'old_amino_acid': None})
  Step 3: replace_residue({'action_type': 'replace_residue', 'position': 11, 'new_amino_acid': 'S', 'old_amino_acid': None})
  Step 4: replace_residue({'action_type': 'replace_residue', 'position': 11, 'new_amino_acid': 'C', 'old_amino_acid': None})

============================================================
MOTIF DROPOUT NOISE
============================================================
Generated 3 forward and 3 backward trajectories

Sample backward trajectory:
Corrupted seq: FA
Original seq: MKFLVLLFNILCLFPVLA
  Step 1: insert_motif({'action_type': 'insert_motif', 'position': 0, 'motif': 'LVLLL'})
  Step 2: insert_motif({'action_type': 'insert_motif', 'position': 0, 'motif': 'MKF'})
  Step 3: insert_motif({'action_type': 'insert_motif', 'position': 9, 'motif': 'PVL'})
  Step 4: insert_motif({'action_type': 'insert_motif', 'position': 7, 'motif': 'FNILC'})

