FORWARD PROTEIN TRAJECTORIES (Empty → Random Target)
Generated on: 2025-06-03 17:46:47
Configuration: {'correct_action_prob': 0.4, 'temperature': 1.5, 'random_action_weights': {'add': 0.6, 'delete': 0.2, 'replace': 0.2}, 'seed': 42}
Total sequences: 6
Natural amino acids used: ['A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'V', 'W', 'Y']
Sequence generation seed: 42

Note: These are randomly generated protein sequences using natural amino acids.
Flow-matching trajectories show how sequences can be built from empty state.

================================================================================
FORWARD TRAJECTORY: Random_Protein_1_Len20
Target Sequence: DRFDDRMNACFPNRRKKGVS
Target Length: 20
Total Steps: 76
Efficiency: 0.26 chars/step
Success: Yes

Full State Progression:
  Step   0: '' (len=0) [START]
  Step   1: 'D' (len=1) ← add D at position 0
  Step   2: 'ED' (len=2) ← add E at position 0
  Step   3: 'YD' (len=2) ← replace E at position 0 with Y
  Step   4: 'IYD' (len=3) ← add I at position 0
  Step   5: 'IVYD' (len=4) ← add V at position 1
  Step   6: 'IVAYD' (len=5) ← add A at position 2
  Step   7: 'IVAMYD' (len=6) ← add M at position 3
  Step   8: 'DVAMYD' (len=6) ← replace I at position 0 with D
  Step   9: 'DRAMYD' (len=6) ← replace V at position 1 with R
  Step  10: 'DRAEMYD' (len=7) ← add E at position 3
  Step  11: 'DRFEMYD' (len=7) ← replace A at position 2 with F
  Step  12: 'DRFDMYD' (len=7) ← replace E at position 3 with D
  Step  13: 'DRFDDYD' (len=7) ← replace M at position 4 with D
  Step  14: 'DRFDDRD' (len=7) ← replace Y at position 5 with R
  Step  15: 'DRFDDRDD' (len=8) ← add D at position 6
  Step  16: 'DRFDDYDD' (len=8) ← replace R at position 5 with Y
  Step  17: 'DRFDDRDD' (len=8) ← replace Y at position 5 with R
  Step  18: 'DRFDDRMD' (len=8) ← replace D at position 6 with M
  Step  19: 'DFDDRMD' (len=7) ← remove R from position 1
  Step  20: 'DFDLRMD' (len=7) ← replace D at position 3 with L
  Step  21: 'DPDLRMD' (len=7) ← replace F at position 1 with P
  Step  22: 'DRDLRMD' (len=7) ← replace P at position 1 with R
  Step  23: 'DRDLRD' (len=6) ← remove M from position 5
  Step  24: 'DDLRD' (len=5) ← remove R from position 1
  Step  25: 'DDLPRD' (len=6) ← add P at position 3
  Step  26: 'DRLPRD' (len=6) ← replace D at position 1 with R
  Step  27: 'DLPRD' (len=5) ← remove R from position 1
  Step  28: 'KLPRD' (len=5) ← replace D at position 0 with K
  Step  29: 'KLPKD' (len=5) ← replace R at position 3 with K
  Step  30: 'DLPKD' (len=5) ← replace K at position 0 with D
  Step  31: 'DLPKDM' (len=6) ← add M at position 5
  Step  32: 'DRPKDM' (len=6) ← replace L at position 1 with R
  Step  33: 'DRPKDS' (len=6) ← replace M at position 5 with S
  Step  34: 'DRFKDS' (len=6) ← replace P at position 2 with F
  Step  35: 'DRFDDS' (len=6) ← replace K at position 3 with D
  Step  36: 'DRFDDWS' (len=7) ← add W at position 5
  Step  37: 'DRFDDIWS' (len=8) ← add I at position 5
  Step  38: 'DRFDDIWDS' (len=9) ← add D at position 7
  Step  39: 'DRHDDIWDS' (len=9) ← replace F at position 2 with H
  Step  40: 'DPRHDDIWDS' (len=10) ← add P at position 1
  Step  41: 'DRRHDDIWDS' (len=10) ← replace P at position 1 with R
  Step  42: 'DRRHDDIWADS' (len=11) ← add A at position 8
  Step  43: 'DRRHDDIWKADS' (len=12) ← add K at position 8
  Step  44: 'DRRHQDDIWKADS' (len=13) ← add Q at position 4
  Step  45: 'DRFHQDDIWKADS' (len=13) ← replace R at position 2 with F
  Step  46: 'DRFDQDDIWKADS' (len=13) ← replace H at position 3 with D
  Step  47: 'DRFDQDDIKADS' (len=12) ← remove W from position 8
  Step  48: 'DLRFDQDDIKADS' (len=13) ← add L at position 1
  Step  49: 'DLRFFDQDDIKADS' (len=14) ← add F at position 3
  Step  50: 'DRRFFDQDDIKADS' (len=14) ← replace L at position 1 with R
  Step  51: 'DRFFFDQDDIKADS' (len=14) ← replace R at position 2 with F
  Step  52: 'NRFFFDQDDIKADS' (len=14) ← replace D at position 0 with N
  Step  53: 'NRFFFLDQDDIKADS' (len=15) ← add L at position 5
  Step  54: 'DRFFFLDQDDIKADS' (len=15) ← replace N at position 0 with D
  Step  55: 'DRFDFLDQDDIKADS' (len=15) ← replace F at position 3 with D
  Step  56: 'DRFDFLDQDDIKADSD' (len=16) ← add D at position 15
  Step  57: 'DRFDFFLDQDDIKADSD' (len=17) ← add F at position 4
  Step  58: 'DRFDFLLDQDDIKADSD' (len=17) ← replace F at position 5 with L
  Step  59: 'DRFDFLDQDDIKADSD' (len=16) ← remove L from position 6
  Step  60: 'DRFDFLQDDIKADSD' (len=15) ← remove D from position 6
  Step  61: 'DRFDDLQDDIKADSD' (len=15) ← replace F at position 4 with D
  Step  62: 'DRFDDRQDDIKADSD' (len=15) ← replace L at position 5 with R
  Step  63: 'DRFDDRMDDIKADSD' (len=15) ← replace Q at position 6 with M
  Step  64: 'DRFDDRMNDIKADSD' (len=15) ← replace D at position 7 with N
  Step  65: 'DRFDDRMNAIKADSD' (len=15) ← replace D at position 8 with A
  Step  66: 'DRFDDRMNACKADSD' (len=15) ← replace I at position 9 with C
  Step  67: 'DRFDDRMNACFADSD' (len=15) ← replace K at position 10 with F
  Step  68: 'DRFDDRMNACFPDSD' (len=15) ← replace A at position 11 with P
  Step  69: 'DRFDDRMNACFPNSD' (len=15) ← replace D at position 12 with N
  Step  70: 'DRFDDRMNACFPNRD' (len=15) ← replace S at position 13 with R
  Step  71: 'DRFDDRMNACFPNRR' (len=15) ← replace D at position 14 with R
  Step  72: 'DRFDDRMNACFPNRRK' (len=16) ← add K at position 15
  Step  73: 'DRFDDRMNACFPNRRKK' (len=17) ← add K at position 16
  Step  74: 'DRFDDRMNACFPNRRKKG' (len=18) ← add G at position 17
  Step  75: 'DRFDDRMNACFPNRRKKGV' (len=19) ← add V at position 18
  Step  76: 'DRFDDRMNACFPNRRKKGVS' (len=20) ← add S at position 19

Trajectory Metadata:
  {
  "approach": "flow_matching",
  "config": {
    "correct_action_prob": 0.4,
    "temperature": 1.5,
    "random_action_weights": {
      "add": 0.6,
      "delete": 0.2,
      "replace": 0.2
    }
  },
  "target_length": 20,
  "total_steps": 76
}

================================================================================
FORWARD TRAJECTORY: Random_Protein_2_Len35
Target Sequence: QHEYHCCTPTRMYINTPVNRAFGCFDGPIIFGWPP
Target Length: 35
Total Steps: 105
Efficiency: 0.33 chars/step
Success: Yes

Full State Progression:
  Step   0: '' (len=0) [START]
  Step   1: 'Q' (len=1) ← add Q at position 0
  Step   2: 'QE' (len=2) ← add E at position 1
  Step   3: 'QH' (len=2) ← replace E at position 1 with H
  Step   4: 'QHE' (len=3) ← add E at position 2
  Step   5: 'QHEY' (len=4) ← add Y at position 3
  Step   6: 'DQHEY' (len=5) ← add D at position 0
  Step   7: 'CDQHEY' (len=6) ← add C at position 0
  Step   8: 'CKDQHEY' (len=7) ← add K at position 1
  Step   9: 'CKWDQHEY' (len=8) ← add W at position 2
  Step  10: 'CKWDQHEQY' (len=9) ← add Q at position 7
  Step  11: 'QKWDQHEQY' (len=9) ← replace C at position 0 with Q
  Step  12: 'QHWDQHEQY' (len=9) ← replace K at position 1 with H
  Step  13: 'QHWDQHECQY' (len=10) ← add C at position 7
  Step  14: 'QCWDQHECQY' (len=10) ← replace H at position 1 with C
  Step  15: 'QICWDQHECQY' (len=11) ← add I at position 1
  Step  16: 'QHCWDQHECQY' (len=11) ← replace I at position 1 with H
  Step  17: 'QHKCWDQHECQY' (len=12) ← add K at position 2
  Step  18: 'QSKCWDQHECQY' (len=12) ← replace H at position 1 with S
  Step  19: 'QCKCWDQHECQY' (len=12) ← replace S at position 1 with C
  Step  20: 'DQCKCWDQHECQY' (len=13) ← add D at position 0
  Step  21: 'DQRKCWDQHECQY' (len=13) ← replace C at position 2 with R
  Step  22: 'DQRKCWCDQHECQY' (len=14) ← add C at position 6
  Step  23: 'QQRKCWCDQHECQY' (len=14) ← replace D at position 0 with Q
  Step  24: 'QHRKCWCDQHECQY' (len=14) ← replace Q at position 1 with H
  Step  25: 'QHEKCWCDQHECQY' (len=14) ← replace R at position 2 with E
  Step  26: 'QHEKWCDQHECQY' (len=13) ← remove C from position 4
  Step  27: 'QHEKWCDQHECQV' (len=13) ← replace Y at position 12 with V
  Step  28: 'QHELKWCDQHECQV' (len=14) ← add L at position 3
  Step  29: 'QHEYKWCDQHECQV' (len=14) ← replace L at position 3 with Y
  Step  30: 'QHEYHWCDQHECQV' (len=14) ← replace K at position 4 with H
  Step  31: 'QHEYHCWCDQHECQV' (len=15) ← add C at position 5
  Step  32: 'QHEYHCCCDQHECQV' (len=15) ← replace W at position 6 with C
  Step  33: 'QHEYHCCCHQHECQV' (len=15) ← replace D at position 8 with H
  Step  34: 'QHEYHCCTHQHECQV' (len=15) ← replace C at position 7 with T
  Step  35: 'QDEYHCCTHQHECQV' (len=15) ← replace H at position 1 with D
  Step  36: 'QDEWYHCCTHQHECQV' (len=16) ← add W at position 3
  Step  37: 'QHEWYHCCTHQHECQV' (len=16) ← replace D at position 1 with H
  Step  38: 'QHEWYHCCTHQHEQV' (len=15) ← remove C from position 13
  Step  39: 'QHEWYHCCTHKQHEQV' (len=16) ← add K at position 10
  Step  40: 'QHEYYHCCTHKQHEQV' (len=16) ← replace W at position 3 with Y
  Step  41: 'QHEYYHCCTHKQFHEQV' (len=17) ← add F at position 12
  Step  42: 'QHEYYHCCTHDKQFHEQV' (len=18) ← add D at position 10
  Step  43: 'QHEYHHCCTHDKQFHEQV' (len=18) ← replace Y at position 4 with H
  Step  44: 'QHEDYHHCCTHDKQFHEQV' (len=19) ← add D at position 3
  Step  45: 'QHEDNYHHCCTHDKQFHEQV' (len=20) ← add N at position 4
  Step  46: 'QHEDNYHHCCTMDKQFHEQV' (len=20) ← replace H at position 11 with M
  Step  47: 'QHEYNYHHCCTMDKQFHEQV' (len=20) ← replace D at position 3 with Y
  Step  48: 'QHEYNYHHCCTMDKQFHEQ' (len=19) ← remove V from position 19
  Step  49: 'QHEYNYHHCCTMDKQFAEQ' (len=19) ← replace H at position 16 with A
  Step  50: 'QHEFYNYHHCCTMDKQFAEQ' (len=20) ← add F at position 3
  Step  51: 'QHEYYNYHHCCTMDKQFAEQ' (len=20) ← replace F at position 3 with Y
  Step  52: 'QHEYNYHHCCTMDKQFAEQ' (len=19) ← remove Y from position 4
  Step  53: 'QHEYHYHHCCTMDKQFAEQ' (len=19) ← replace N at position 4 with H
  Step  54: 'QHEYHYHCCTMDKQFAEQ' (len=18) ← remove H from position 6
  Step  55: 'QHEYHYHCCTMDKQFATQ' (len=18) ← replace E at position 16 with T
  Step  56: 'QHEYHCHCCTMDKQFATQ' (len=18) ← replace Y at position 5 with C
  Step  57: 'QHEYHCHCCTMDKKQFATQ' (len=19) ← add K at position 13
  Step  58: 'QHEYHCCCCTMDKKQFATQ' (len=19) ← replace H at position 6 with C
  Step  59: 'QHEYHCCTCTMDKKQFATQ' (len=19) ← replace C at position 7 with T
  Step  60: 'QHEYHCCTPTMDKKQFATQ' (len=19) ← replace C at position 8 with P
  Step  61: 'QHEYHCCTPTMDKKVQFATQ' (len=20) ← add V at position 14
  Step  62: 'QHEDYHCCTPTMDKKVQFATQ' (len=21) ← add D at position 3
  Step  63: 'QHEDHCCTPTMDKKVQFATQ' (len=20) ← remove Y from position 4
  Step  64: 'QHEDHCCTPTMDKKVQFAWQ' (len=20) ← replace T at position 18 with W
  Step  65: 'QHEYHCCTPTMDKKVQFAWQ' (len=20) ← replace D at position 3 with Y
  Step  66: 'QHEYHCCTPTRDKKVQFAWQ' (len=20) ← replace M at position 10 with R
  Step  67: 'QHEYHCCTPTRMKKVQFAWQ' (len=20) ← replace D at position 11 with M
  Step  68: 'QEYHCCTPTRMKKVQFAWQ' (len=19) ← remove H from position 1
  Step  69: 'QEYHCCTEPTRMKKVQFAWQ' (len=20) ← add E at position 7
  Step  70: 'QHYHCCTEPTRMKKVQFAWQ' (len=20) ← replace E at position 1 with H
  Step  71: 'QHYHCCTEPTRMKKVQFAWF' (len=20) ← replace Q at position 19 with F
  Step  72: 'QHYHCGCTEPTRMKKVQFAWF' (len=21) ← add G at position 5
  Step  73: 'QHYHCGCTEPQTRMKKVQFAWF' (len=22) ← add Q at position 10
  Step  74: 'QHYHCGCKEPQTRMKKVQFAWF' (len=22) ← replace T at position 7 with K
  Step  75: 'QHEHCGCKEPQTRMKKVQFAWF' (len=22) ← replace Y at position 2 with E
  Step  76: 'QHEYCGCKEPQTRMKKVQFAWF' (len=22) ← replace H at position 3 with Y
  Step  77: 'QHEYHGCKEPQTRMKKVQFAWF' (len=22) ← replace C at position 4 with H
  Step  78: 'QHEYHCCKEPQTRMKKVQFAWF' (len=22) ← replace G at position 5 with C
  Step  79: 'QHEYHCCTEPQTRMKKVQFAWF' (len=22) ← replace K at position 7 with T
  Step  80: 'QHEYHCCTPPQTRMKKVQFAWF' (len=22) ← replace E at position 8 with P
  Step  81: 'QHEYHCCTPTQTRMKKVQFAWF' (len=22) ← replace P at position 9 with T
  Step  82: 'QHEYHCCTPTRTRMKKVQFAWF' (len=22) ← replace Q at position 10 with R
  Step  83: 'QHEYHCCTPTRMRMKKVQFAWF' (len=22) ← replace T at position 11 with M
  Step  84: 'QHEYHCCTPTRMYMKKVQFAWF' (len=22) ← replace R at position 12 with Y
  Step  85: 'QHEYHCCTPTRMYIKKVQFAWF' (len=22) ← replace M at position 13 with I
  Step  86: 'QHEYHCCTPTRMYINKVQFAWF' (len=22) ← replace K at position 14 with N
  Step  87: 'QHEYHCCTPTRMYINTVQFAWF' (len=22) ← replace K at position 15 with T
  Step  88: 'QHEYHCCTPTRMYINTPQFAWF' (len=22) ← replace V at position 16 with P
  Step  89: 'QHEYHCCTPTRMYINTPVFAWF' (len=22) ← replace Q at position 17 with V
  Step  90: 'QHEYHCCTPTRMYINTPVNAWF' (len=22) ← replace F at position 18 with N
  Step  91: 'QHEYHCCTPTRMYINTPVNRWF' (len=22) ← replace A at position 19 with R
  Step  92: 'QHEYHCCTPTRMYINTPVNRAF' (len=22) ← replace W at position 20 with A
  Step  93: 'QHEYHCCTPTRMYINTPVNRAFG' (len=23) ← add G at position 22
  Step  94: 'QHEYHCCTPTRMYINTPVNRAFGC' (len=24) ← add C at position 23
  Step  95: 'QHEYHCCTPTRMYINTPVNRAFGCF' (len=25) ← add F at position 24
  Step  96: 'QHEYHCCTPTRMYINTPVNRAFGCFD' (len=26) ← add D at position 25
  Step  97: 'QHEYHCCTPTRMYINTPVNRAFGCFDG' (len=27) ← add G at position 26
  Step  98: 'QHEYHCCTPTRMYINTPVNRAFGCFDGP' (len=28) ← add P at position 27
  Step  99: 'QHEYHCCTPTRMYINTPVNRAFGCFDGPI' (len=29) ← add I at position 28
  Step 100: 'QHEYHCCTPTRMYINTPVNRAFGCFDGPII' (len=30) ← add I at position 29
  Step 101: 'QHEYHCCTPTRMYINTPVNRAFGCFDGPIIF' (len=31) ← add F at position 30
  Step 102: 'QHEYHCCTPTRMYINTPVNRAFGCFDGPIIFG' (len=32) ← add G at position 31
  Step 103: 'QHEYHCCTPTRMYINTPVNRAFGCFDGPIIFGW' (len=33) ← add W at position 32
  Step 104: 'QHEYHCCTPTRMYINTPVNRAFGCFDGPIIFGWP' (len=34) ← add P at position 33
  Step 105: 'QHEYHCCTPTRMYINTPVNRAFGCFDGPIIFGWPP' (len=35) ← add P at position 34

Trajectory Metadata:
  {
  "approach": "flow_matching",
  "config": {
    "correct_action_prob": 0.4,
    "temperature": 1.5,
    "random_action_weights": {
      "add": 0.6,
      "delete": 0.2,
      "replace": 0.2
    }
  },
  "target_length": 35,
  "total_steps": 105
}

================================================================================
FORWARD TRAJECTORY: Random_Protein_3_Len70
Target Sequence: MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPTMEIEYWWNL
Target Length: 70
Total Steps: 168
Efficiency: 0.42 chars/step
Success: Yes

Full State Progression:
  Step   0: '' (len=0) [START]
  Step   1: 'P' (len=1) ← add P at position 0
  Step   2: 'H' (len=1) ← replace P at position 0 with H
  Step   3: 'HI' (len=2) ← add I at position 1
  Step   4: 'MI' (len=2) ← replace H at position 0 with M
  Step   5: 'MDI' (len=3) ← add D at position 1
  Step   6: 'MDIV' (len=4) ← add V at position 3
  Step   7: 'MFIV' (len=4) ← replace D at position 1 with F
  Step   8: 'MFLV' (len=4) ← replace I at position 2 with L
  Step   9: 'MFLVK' (len=5) ← add K at position 4
  Step  10: 'MFLGK' (len=5) ← replace V at position 3 with G
  Step  11: 'MFQLGK' (len=6) ← add Q at position 2
  Step  12: 'MFQWLGK' (len=7) ← add W at position 3
  Step  13: 'MFLWLGK' (len=7) ← replace Q at position 2 with L
  Step  14: 'MFLGLGK' (len=7) ← replace W at position 3 with G
  Step  15: 'MFLNGLGK' (len=8) ← add N at position 3
  Step  16: 'MFLNGNGK' (len=8) ← replace L at position 5 with N
  Step  17: 'MFLNTGNGK' (len=9) ← add T at position 4
  Step  18: 'MFLGTGNGK' (len=9) ← replace N at position 3 with G
  Step  19: 'MFLGVTGNGK' (len=10) ← add V at position 4
  Step  20: 'MFLGWTGNGK' (len=10) ← replace V at position 4 with W
  Step  21: 'MFYGWTGNGK' (len=10) ← replace L at position 2 with Y
  Step  22: 'MFLGWTGNGK' (len=10) ← replace Y at position 2 with L
  Step  23: 'MFLGHWTGNGK' (len=11) ← add H at position 4
  Step  24: 'MFLGHWTGNGMK' (len=12) ← add M at position 10
  Step  25: 'MFLTGHWTGNGMK' (len=13) ← add T at position 3
  Step  26: 'MFLTGHWTGNGMG' (len=13) ← replace K at position 12 with G
  Step  27: 'MFLTGHWTGNYGMG' (len=14) ← add Y at position 10
  Step  28: 'MFLGGHWTGNYGMG' (len=14) ← replace T at position 3 with G
  Step  29: 'MFLGGHWTGNGMG' (len=13) ← remove Y from position 10
  Step  30: 'MFLGWHWTGNGMG' (len=13) ← replace G at position 4 with W
  Step  31: 'IMFLGWHWTGNGMG' (len=14) ← add I at position 0
  Step  32: 'IMFLGWHWTGNGG' (len=13) ← remove M from position 12
  Step  33: 'MMFLGWHWTGNGG' (len=13) ← replace I at position 0 with M
  Step  34: 'MMFGWHWTGNGG' (len=12) ← remove L from position 3
  Step  35: 'MMFGWHIWTGNGG' (len=13) ← add I at position 6
  Step  36: 'MFFGWHIWTGNGG' (len=13) ← replace M at position 1 with F
  Step  37: 'MFFGWHIWTGNGE' (len=13) ← replace G at position 12 with E
  Step  38: 'MFFGWHIWTGNGTE' (len=14) ← add T at position 12
  Step  39: 'MFFGWHIWTGNGTEE' (len=15) ← add E at position 14
  Step  40: 'MFFGWHIWTGVGTEE' (len=15) ← replace N at position 10 with V
  Step  41: 'MFFGWHIWTGVGTEYE' (len=16) ← add Y at position 14
  Step  42: 'MFFGWHIWTGVGTWYE' (len=16) ← replace E at position 13 with W
  Step  43: 'MFFGWHIWTGVGTWYRE' (len=17) ← add R at position 15
  Step  44: 'MFLGWHIWTGVGTWYRE' (len=17) ← replace F at position 2 with L
  Step  45: 'MFLGWQIWTGVGTWYRE' (len=17) ← replace H at position 5 with Q
  Step  46: 'MFLGWQIWTGVGTWYR' (len=16) ← remove E from position 16
  Step  47: 'MFLGWQIWTGVGTWDYR' (len=17) ← add D at position 14
  Step  48: 'MFLGWQIWTGMVGTWDYR' (len=18) ← add M at position 10
  Step  49: 'MFLGIWQIWTGMVGTWDYR' (len=19) ← add I at position 4
  Step  50: 'MFLGWWQIWTGMVGTWDYR' (len=19) ← replace I at position 4 with W
  Step  51: 'MFLGWQQIWTGMVGTWDYR' (len=19) ← replace W at position 5 with Q
  Step  52: 'MFLGWQFIWTGMVGTWDYR' (len=19) ← replace Q at position 6 with F
  Step  53: 'MFLGWQFIWTGMVGQTWDYR' (len=20) ← add Q at position 14
  Step  54: 'MFLGWQFHWTGMVGQTWDYR' (len=20) ← replace I at position 7 with H
  Step  55: 'MFLGWQFHWTGMVGQTWDAYR' (len=21) ← add A at position 18
  Step  56: 'MFLGWQFHWTGMGQTWDAYR' (len=20) ← remove V from position 12
  Step  57: 'MFLGWQFHWPGMGQTWDAYR' (len=20) ← replace T at position 9 with P
  Step  58: 'MFLGWQFHWPGMGWTWDAYR' (len=20) ← replace Q at position 13 with W
  Step  59: 'MFLGWQFHWPGMGWTWDAYIR' (len=21) ← add I at position 19
  Step  60: 'MFLGWQFHWPGMGWTAWDAYIR' (len=22) ← add A at position 15
  Step  61: 'MFLGWQFHSPGMGWTAWDAYIR' (len=22) ← replace W at position 8 with S
  Step  62: 'MFLGWFHSPGMGWTAWDAYIR' (len=21) ← remove Q from position 5
  Step  63: 'MFLGWFHSPGMGWTAWDAYWR' (len=21) ← replace I at position 19 with W
  Step  64: 'MFLGWQHSPGMGWTAWDAYWR' (len=21) ← replace F at position 5 with Q
  Step  65: 'MFLGWQFSPGMGWTAWDAYWR' (len=21) ← replace H at position 6 with F
  Step  66: 'MFLGWQFSPGMGWTAWDAYWQR' (len=22) ← add Q at position 20
  Step  67: 'MFLGWQFHPGMGWTAWDAYWQR' (len=22) ← replace S at position 7 with H
  Step  68: 'MFLGWQFHPGMGMWTAWDAYWQR' (len=23) ← add M at position 12
  Step  69: 'MFLGWQFHSGMGMWTAWDAYWQR' (len=23) ← replace P at position 8 with S
  Step  70: 'MFLGWQFHSCMGMWTAWDAYWQR' (len=23) ← replace G at position 9 with C
  Step  71: 'MFLGWQFHSCMGMKWTAWDAYWQR' (len=24) ← add K at position 13
  Step  72: 'MFLGWQFHSCMGMKWTAWDAYWQVR' (len=25) ← add V at position 23
  Step  73: 'MFLGWQFHSCTGMKWTAWDAYWQVR' (len=25) ← replace M at position 10 with T
  Step  74: 'MFCLGWQFHSCTGMKWTAWDAYWQVR' (len=26) ← add C at position 2
  Step  75: 'MFCLGWAFHSCTGMKWTAWDAYWQVR' (len=26) ← replace Q at position 6 with A
  Step  76: 'MFCLGWAFHSCTGMKEWTAWDAYWQVR' (len=27) ← add E at position 15
  Step  77: 'MFCLGWAFHSCTGMKEWTAWDAKYWQVR' (len=28) ← add K at position 22
  Step  78: 'MFCLGWAFHSCTGMKEWTAEWDAKYWQVR' (len=29) ← add E at position 19
  Step  79: 'MFCLGWAFHESCTGMKEWTAEWDAKYWQVR' (len=30) ← add E at position 9
  Step  80: 'MFCLGWAFHESCTGMKEWPAEWDAKYWQVR' (len=30) ← replace T at position 18 with P
  Step  81: 'MFLLGWAFHESCTGMKEWPAEWDAKYWQVR' (len=30) ← replace C at position 2 with L
  Step  82: 'MFLLGWAFHESCTGMKEWPAEWIDAKYWQVR' (len=31) ← add I at position 22
  Step  83: 'MFLGGWAFHESCTGMKEWPAEWIDAKYWQVR' (len=31) ← replace L at position 3 with G
  Step  84: 'MFLGGWAFHESCTGMKEWPFEWIDAKYWQVR' (len=31) ← replace A at position 19 with F
  Step  85: 'MFNLGGWAFHESCTGMKEWPFEWIDAKYWQVR' (len=32) ← add N at position 2
  Step  86: 'MFNLGWAFHESCTGMKEWPFEWIDAKYWQVR' (len=31) ← remove G from position 4
  Step  87: 'MFNLGWAFHESCTGMKEWPFEWIDAKSYWQVR' (len=32) ← add S at position 26
  Step  88: 'MFLLGWAFHESCTGMKEWPFEWIDAKSYWQVR' (len=32) ← replace N at position 2 with L
  Step  89: 'MFLLGWAFHESCTGMKEWPFEWIDAKSYWVR' (len=31) ← remove Q from position 29
  Step  90: 'MFLLGWAFHESCTGMKEYWPFEWIDAKSYWVR' (len=32) ← add Y at position 17
  Step  91: 'MFLLGWAFHESCTGMKEYWPFEWIDAKSYWRVR' (len=33) ← add R at position 30
  Step  92: 'MFLLGWAFHESCTGMKEWPFEWIDAKSYWRVR' (len=32) ← remove Y from position 17
  Step  93: 'MFLGGWAFHESCTGMKEWPFEWIDAKSYWRVR' (len=32) ← replace L at position 3 with G
  Step  94: 'MFLGWWAFHESCTGMKEWPFEWIDAKSYWRVR' (len=32) ← replace G at position 4 with W
  Step  95: 'MFLGWWAFHESCTGMKEWPFEWIDAKSYIWRVR' (len=33) ← add I at position 28
  Step  96: 'MFLGWWAFHESCTGMKEWPFEWIDMAKSYIWRVR' (len=34) ← add M at position 24
  Step  97: 'MFLGWQAFHESCTGMKEWPFEWIDMAKSYIWRVR' (len=34) ← replace W at position 5 with Q
  Step  98: 'MFLGWQAFHESCTNGMKEWPFEWIDMAKSYIWRVR' (len=35) ← add N at position 13
  Step  99: 'MFLGWQAFHESCTNGMKVEWPFEWIDMAKSYIWRVR' (len=36) ← add V at position 17
  Step 100: 'MFLGWQFFHESCTNGMKVEWPFEWIDMAKSYIWRVR' (len=36) ← replace A at position 6 with F
  Step 101: 'MFLGWQFFHESCTNGMKVEWPFEWIDSMAKSYIWRVR' (len=37) ← add S at position 26
  Step 102: 'MFLGWQFFHESCTNGMKVEWPFEWIDSMAKSSYIWRVR' (len=38) ← add S at position 30
  Step 103: 'MFLGWQFFHESCTNGMKVIEWPFEWIDSMAKSSYIWRVR' (len=39) ← add I at position 18
  Step 104: 'MFLGWQFFHESCTNGMKVIEWPFEWIDSMAKSSYIWRNVR' (len=40) ← add N at position 37
  Step 105: 'MFLGWQFFHESCTNGMKVIEWPFEWIDVSMAKSSYIWRNVR' (len=41) ← add V at position 27
  Step 106: 'MFLGWQFHHESCTNGMKVIEWPFEWIDVSMAKSSYIWRNVR' (len=41) ← replace F at position 7 with H
  Step 107: 'MFLGWQFHHESCTNGMIKVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← add I at position 16
  Step 108: 'MFLGWQFHSESCTNGMIKVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace H at position 8 with S
  Step 109: 'MFLGWQFHSCSCTNGMIKVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace E at position 9 with C
  Step 110: 'MFLGWQFHSCTCTNGMIKVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace S at position 10 with T
  Step 111: 'MFLGWQFHSCTTTNGMIKVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace C at position 11 with T
  Step 112: 'MFLGWQFHSCTTKNGMIKVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace T at position 12 with K
  Step 113: 'MFLGWQFHSCTTKCGMIKVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace N at position 13 with C
  Step 114: 'MFLGWQFHSCTTKCWMIKVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace G at position 14 with W
  Step 115: 'MFLGWQFHSCTTKCWNIKVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace M at position 15 with N
  Step 116: 'MFLGWQFHSCTTKCWNRKVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace I at position 16 with R
  Step 117: 'MFLGWQFHSCTTKCWNRFVIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace K at position 17 with F
  Step 118: 'MFLGWQFHSCTTKCWNRFLIEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace V at position 18 with L
  Step 119: 'MFLGWQFHSCTTKCWNRFLVEWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace I at position 19 with V
  Step 120: 'MFLGWQFHSCTTKCWNRFLVPWPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace E at position 20 with P
  Step 121: 'MFLGWQFHSCTTKCWNRFLVPDPFEWIDVSMAKSSYIWRNVR' (len=42) ← replace W at position 21 with D
  Step 122: 'MFLGWQFHSCTTKCWNRFLVPDDFEWIDVSMAKSSYIWRNVR' (len=42) ← replace P at position 22 with D
  Step 123: 'MFLGWQFHSCTTKCWNRFLVPDDREWIDVSMAKSSYIWRNVR' (len=42) ← replace F at position 23 with R
  Step 124: 'MFLGWQFHSCTTKCWNRFLVPDDRMWIDVSMAKSSYIWRNVR' (len=42) ← replace E at position 24 with M
  Step 125: 'MFLGWQFHSCTTKCWNRFLVPDDRMRIDVSMAKSSYIWRNVR' (len=42) ← replace W at position 25 with R
  Step 126: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKDVSMAKSSYIWRNVR' (len=42) ← replace I at position 26 with K
  Step 127: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNVSMAKSSYIWRNVR' (len=42) ← replace D at position 27 with N
  Step 128: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNISMAKSSYIWRNVR' (len=42) ← replace V at position 28 with I
  Step 129: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYMAKSSYIWRNVR' (len=42) ← replace S at position 29 with Y
  Step 130: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDAKSSYIWRNVR' (len=42) ← replace M at position 30 with D
  Step 131: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLKSSYIWRNVR' (len=42) ← replace A at position 31 with L
  Step 132: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSSSYIWRNVR' (len=42) ← replace K at position 32 with S
  Step 133: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVSYIWRNVR' (len=42) ← replace S at position 33 with V
  Step 134: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEYIWRNVR' (len=42) ← replace S at position 34 with E
  Step 135: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEIWRNVR' (len=42) ← replace Y at position 35 with E
  Step 136: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQWRNVR' (len=42) ← replace I at position 36 with Q
  Step 137: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNRNVR' (len=42) ← replace W at position 37 with N
  Step 138: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINVR' (len=42) ← replace R at position 38 with I
  Step 139: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLR' (len=42) ← replace V at position 40 with L
  Step 140: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLG' (len=42) ← replace R at position 41 with G
  Step 141: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGN' (len=43) ← add N at position 42
  Step 142: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNW' (len=44) ← add W at position 43
  Step 143: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQ' (len=45) ← add Q at position 44
  Step 144: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQD' (len=46) ← add D at position 45
  Step 145: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDV' (len=47) ← add V at position 46
  Step 146: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVS' (len=48) ← add S at position 47
  Step 147: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSS' (len=49) ← add S at position 48
  Step 148: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSH' (len=50) ← add H at position 49
  Step 149: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHG' (len=51) ← add G at position 50
  Step 150: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGE' (len=52) ← add E at position 51
  Step 151: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEA' (len=53) ← add A at position 52
  Step 152: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEAR' (len=54) ← add R at position 53
  Step 153: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARR' (len=55) ← add R at position 54
  Step 154: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRY' (len=56) ← add Y at position 55
  Step 155: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYS' (len=57) ← add S at position 56
  Step 156: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSM' (len=58) ← add M at position 57
  Step 157: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMD' (len=59) ← add D at position 58
  Step 158: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDP' (len=60) ← add P at position 59
  Step 159: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPT' (len=61) ← add T at position 60
  Step 160: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPTM' (len=62) ← add M at position 61
  Step 161: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPTME' (len=63) ← add E at position 62
  Step 162: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPTMEI' (len=64) ← add I at position 63
  Step 163: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPTMEIE' (len=65) ← add E at position 64
  Step 164: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPTMEIEY' (len=66) ← add Y at position 65
  Step 165: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPTMEIEYW' (len=67) ← add W at position 66
  Step 166: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPTMEIEYWW' (len=68) ← add W at position 67
  Step 167: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPTMEIEYWWN' (len=69) ← add N at position 68
  Step 168: 'MFLGWQFHSCTTKCWNRFLVPDDRMRKNIYDLSVEEQNINLGNWQDVSSHGEARRYSMDPTMEIEYWWNL' (len=70) ← add L at position 69

Trajectory Metadata:
  {
  "approach": "flow_matching",
  "config": {
    "correct_action_prob": 0.4,
    "temperature": 1.5,
    "random_action_weights": {
      "add": 0.6,
      "delete": 0.2,
      "replace": 0.2
    }
  },
  "target_length": 70,
  "total_steps": 168
}

================================================================================
FORWARD TRAJECTORY: Random_Protein_4_Len23
Target Sequence: WVTHCVWCLCSSDLMGVKFMRFH
Target Length: 23
Total Steps: 74
Efficiency: 0.31 chars/step
Success: Yes

Full State Progression:
  Step   0: '' (len=0) [START]
  Step   1: 'H' (len=1) ← add H at position 0
  Step   2: 'W' (len=1) ← replace H at position 0 with W
  Step   3: '' (len=0) ← remove W from position 0
  Step   4: 'L' (len=1) ← add L at position 0
  Step   5: 'W' (len=1) ← replace L at position 0 with W
  Step   6: 'WV' (len=2) ← add V at position 1
  Step   7: 'WVT' (len=3) ← add T at position 2
  Step   8: 'VWVT' (len=4) ← add V at position 0
  Step   9: 'WWVT' (len=4) ← replace V at position 0 with W
  Step  10: 'WWV' (len=3) ← remove T from position 3
  Step  11: 'WVV' (len=3) ← replace W at position 1 with V
  Step  12: 'WVT' (len=3) ← replace V at position 2 with T
  Step  13: 'WVTH' (len=4) ← add H at position 3
  Step  14: 'KWVTH' (len=5) ← add K at position 0
  Step  15: 'PKWVTH' (len=6) ← add P at position 0
  Step  16: 'PKWVTCH' (len=7) ← add C at position 5
  Step  17: 'WKWVTCH' (len=7) ← replace P at position 0 with W
  Step  18: 'IKWVTCH' (len=7) ← replace W at position 0 with I
  Step  19: 'WKWVTCH' (len=7) ← replace I at position 0 with W
  Step  20: 'WKWVTC' (len=6) ← remove H from position 6
  Step  21: 'WKWTC' (len=5) ← remove V from position 3
  Step  22: 'WKWTQC' (len=6) ← add Q at position 4
  Step  23: 'WVWTQC' (len=6) ← replace K at position 1 with V
  Step  24: 'WVWTQ' (len=5) ← remove C from position 5
  Step  25: 'WVTTQ' (len=5) ← replace W at position 2 with T
  Step  26: 'WVTQ' (len=4) ← remove T from position 2
  Step  27: 'WVVTQ' (len=5) ← add V at position 1
  Step  28: 'WVTTQ' (len=5) ← replace V at position 2 with T
  Step  29: 'WVTHQ' (len=5) ← replace T at position 3 with H
  Step  30: 'WVCTHQ' (len=6) ← add C at position 2
  Step  31: 'WVTTHQ' (len=6) ← replace C at position 2 with T
  Step  32: 'WVTHQ' (len=5) ← remove T from position 3
  Step  33: 'WVTHC' (len=5) ← replace Q at position 4 with C
  Step  34: 'WVTHCV' (len=6) ← add V at position 5
  Step  35: 'WVTHCVW' (len=7) ← add W at position 6
  Step  36: 'WVTQHCVW' (len=8) ← add Q at position 3
  Step  37: 'WVTHHCVW' (len=8) ← replace Q at position 3 with H
  Step  38: 'WVTHCCVW' (len=8) ← replace H at position 4 with C
  Step  39: 'WVTHCVVW' (len=8) ← replace C at position 5 with V
  Step  40: 'WVTHCVWW' (len=8) ← replace V at position 6 with W
  Step  41: 'WVTHCVWC' (len=8) ← replace W at position 7 with C
  Step  42: 'WVTHCVWCL' (len=9) ← add L at position 8
  Step  43: 'WVTHCVWCLL' (len=10) ← add L at position 9
  Step  44: 'WVTHPCVWCLL' (len=11) ← add P at position 4
  Step  45: 'WVTHPCVRWCLL' (len=12) ← add R at position 7
  Step  46: 'WVTHCCVRWCLL' (len=12) ← replace P at position 4 with C
  Step  47: 'WVTHCVVRWCLL' (len=12) ← replace C at position 5 with V
  Step  48: 'WVTHACVVRWCLL' (len=13) ← add A at position 4
  Step  49: 'WVTHCCVVRWCLL' (len=13) ← replace A at position 4 with C
  Step  50: 'WVTHCCVVRYCLL' (len=13) ← replace W at position 9 with Y
  Step  51: 'WVTHCCVVRYLLL' (len=13) ← replace C at position 10 with L
  Step  52: 'WVHCCVVRYLLL' (len=12) ← remove T from position 2
  Step  53: 'WVHCVVRYLLL' (len=11) ← remove C from position 4
  Step  54: 'WVTCVVRYLLL' (len=11) ← replace H at position 2 with T
  Step  55: 'WVTCVVRLLL' (len=10) ← remove Y from position 7
  Step  56: 'WVTCVVMRLLL' (len=11) ← add M at position 6
  Step  57: 'WVTHVVMRLLL' (len=11) ← replace C at position 3 with H
  Step  58: 'WVTHCVMRLLL' (len=11) ← replace V at position 4 with C
  Step  59: 'WVTHCVWRLLL' (len=11) ← replace M at position 6 with W
  Step  60: 'WVTHCVWCLLL' (len=11) ← replace R at position 7 with C
  Step  61: 'WVTHCVWCLCL' (len=11) ← replace L at position 9 with C
  Step  62: 'WVTHCVWCLCS' (len=11) ← replace L at position 10 with S
  Step  63: 'WVTHCVWCLCSS' (len=12) ← add S at position 11
  Step  64: 'WVTHCVWCLCSSD' (len=13) ← add D at position 12
  Step  65: 'WVTHCVWCLCSSDL' (len=14) ← add L at position 13
  Step  66: 'WVTHCVWCLCSSDLM' (len=15) ← add M at position 14
  Step  67: 'WVTHCVWCLCSSDLMG' (len=16) ← add G at position 15
  Step  68: 'WVTHCVWCLCSSDLMGV' (len=17) ← add V at position 16
  Step  69: 'WVTHCVWCLCSSDLMGVK' (len=18) ← add K at position 17
  Step  70: 'WVTHCVWCLCSSDLMGVKF' (len=19) ← add F at position 18
  Step  71: 'WVTHCVWCLCSSDLMGVKFM' (len=20) ← add M at position 19
  Step  72: 'WVTHCVWCLCSSDLMGVKFMR' (len=21) ← add R at position 20
  Step  73: 'WVTHCVWCLCSSDLMGVKFMRF' (len=22) ← add F at position 21
  Step  74: 'WVTHCVWCLCSSDLMGVKFMRFH' (len=23) ← add H at position 22

Trajectory Metadata:
  {
  "approach": "flow_matching",
  "config": {
    "correct_action_prob": 0.4,
    "temperature": 1.5,
    "random_action_weights": {
      "add": 0.6,
      "delete": 0.2,
      "replace": 0.2
    }
  },
  "target_length": 23,
  "total_steps": 74
}

================================================================================
FORWARD TRAJECTORY: Random_Protein_5_Len103
Target Sequence: KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFRFIQGHSCLY
Target Length: 103
Total Steps: 252
Efficiency: 0.41 chars/step
Success: Yes

Full State Progression:
  Step   0: '' (len=0) [START]
  Step   1: 'Q' (len=1) ← add Q at position 0
  Step   2: 'QV' (len=2) ← add V at position 1
  Step   3: 'KV' (len=2) ← replace Q at position 0 with K
  Step   4: 'KM' (len=2) ← replace V at position 1 with M
  Step   5: 'KMD' (len=3) ← add D at position 2
  Step   6: 'KMDF' (len=4) ← add F at position 3
  Step   7: 'WMDF' (len=4) ← replace K at position 0 with W
  Step   8: 'WMDFN' (len=5) ← add N at position 4
  Step   9: 'WMDCFN' (len=6) ← add C at position 3
  Step  10: 'KMDCFN' (len=6) ← replace W at position 0 with K
  Step  11: 'KMDTFN' (len=6) ← replace C at position 3 with T
  Step  12: 'KMDFFN' (len=6) ← replace T at position 3 with F
  Step  13: 'KDFFN' (len=5) ← remove M from position 1
  Step  14: 'VKDFFN' (len=6) ← add V at position 0
  Step  15: 'VKDFFNE' (len=7) ← add E at position 6
  Step  16: 'VKSDFFNE' (len=8) ← add S at position 2
  Step  17: 'VKSDQFFNE' (len=9) ← add Q at position 4
  Step  18: 'VKSSQFFNE' (len=9) ← replace D at position 3 with S
  Step  19: 'VKSSQFFNET' (len=10) ← add T at position 9
  Step  20: 'VKKSSQFFNET' (len=11) ← add K at position 1
  Step  21: 'VKKSSVFFNET' (len=11) ← replace Q at position 5 with V
  Step  22: 'KKKSSVFFNET' (len=11) ← replace V at position 0 with K
  Step  23: 'KMKSSVFFNET' (len=11) ← replace K at position 1 with M
  Step  24: 'KMKSSVFFNTT' (len=11) ← replace E at position 9 with T
  Step  25: 'KMKSSVFNFNTT' (len=12) ← add N at position 7
  Step  26: 'KMDSSVFNFNTT' (len=12) ← replace K at position 2 with D
  Step  27: 'KMDSSHVFNFNTT' (len=13) ← add H at position 5
  Step  28: 'KMDSSHIVFNFNTT' (len=14) ← add I at position 6
  Step  29: 'KMDSSSHIVFNFNTT' (len=15) ← add S at position 5
  Step  30: 'KMDSSSFIVFNFNTT' (len=15) ← replace H at position 6 with F
  Step  31: 'KMDSSSFIVFENFNTT' (len=16) ← add E at position 10
  Step  32: 'KMDSSSFIVFENFNTTR' (len=17) ← add R at position 16
  Step  33: 'KMDFSSFIVFENFNTTR' (len=17) ← replace S at position 3 with F
  Step  34: 'KMDFHSFIVFENFNTTR' (len=17) ← replace S at position 4 with H
  Step  35: 'KMTFHSFIVFENFNTTR' (len=17) ← replace D at position 2 with T
  Step  36: 'KMTFHSFIVFENDFNTTR' (len=18) ← add D at position 12
  Step  37: 'KMTFHSFIVFENDFNTTP' (len=18) ← replace R at position 17 with P
  Step  38: 'KMTFHSFIVFENDFNTP' (len=17) ← remove T from position 15
  Step  39: 'KMITFHSFIVFENDFNTP' (len=18) ← add I at position 2
  Step  40: 'KMITFHSDIVFENDFNTP' (len=18) ← replace F at position 7 with D
  Step  41: 'KMIRTFHSDIVFENDFNTP' (len=19) ← add R at position 3
  Step  42: 'KMDRTFHSDIVFENDFNTP' (len=19) ← replace I at position 2 with D
  Step  43: 'KMDFTFHSDIVFENDFNTP' (len=19) ← replace R at position 3 with F
  Step  44: 'KMDFHFHSDIVFENDFNTP' (len=19) ← replace T at position 4 with H
  Step  45: 'KMDFHNHSDIVFENDFNTP' (len=19) ← replace F at position 5 with N
  Step  46: 'KMDFHNFSDIVFENDFNTP' (len=19) ← replace H at position 6 with F
  Step  47: 'KMDFHNFFDIVFENDFNTP' (len=19) ← replace S at position 7 with F
  Step  48: 'KMDFHNFFDIVFEWNDFNTP' (len=20) ← add W at position 13
  Step  49: 'KMDFHDNFFDIVFEWNDFNTP' (len=21) ← add D at position 5
  Step  50: 'KMDFHDNFFDIVFEWNDFNTPI' (len=22) ← add I at position 21
  Step  51: 'KMDFHDNRFFDIVFEWNDFNTPI' (len=23) ← add R at position 7
  Step  52: 'KMDFHDNRFFDIVFEWNDFNTAPI' (len=24) ← add A at position 21
  Step  53: 'KMDFHDNRFVFDIVFEWNDFNTAPI' (len=25) ← add V at position 9
  Step  54: 'KMDFHNNRFVFDIVFEWNDFNTAPI' (len=25) ← replace D at position 5 with N
  Step  55: 'KMDFHNNRFVFDIVFEWNLDFNTAPI' (len=26) ← add L at position 18
  Step  56: 'KMDFHNNRRFVFDIVFEWNLDFNTAPI' (len=27) ← add R at position 8
  Step  57: 'KMDFHNNRRFVFSDIVFEWNLDFNTAPI' (len=28) ← add S at position 12
  Step  58: 'KMDFHNFRRFVFSDIVFEWNLDFNTAPI' (len=28) ← replace N at position 6 with F
  Step  59: 'KMDFHNFFRFVFSDIVFEWNLDFNTAPI' (len=28) ← replace R at position 7 with F
  Step  60: 'KMDFHNFFCFVFSDIVFEWNLDFNTAPI' (len=28) ← replace R at position 8 with C
  Step  61: 'KMDFHNFFCPVFSDIVFEWNLDFNTAPI' (len=28) ← replace F at position 9 with P
  Step  62: 'KMDFHNFFCPVFSDIVFEWNLDFNTAPPI' (len=29) ← add P at position 26
  Step  63: 'KMDFHNFFCPFFSDIVFEWNLDFNTAPPI' (len=29) ← replace V at position 10 with F
  Step  64: 'KMDFHNFFCPFFSDIVFEWNLDFTAPPI' (len=28) ← remove N from position 23
  Step  65: 'KMDFHNFFCPFFSDIVFEWNLDFSAPPI' (len=28) ← replace T at position 23 with S
  Step  66: 'KMDFHNFFCPFFSDIVFEWNLDFSKPPI' (len=28) ← replace A at position 24 with K
  Step  67: 'KMDFHNFFCPFFSDIVFEWNHLDFSKPPI' (len=29) ← add H at position 20
  Step  68: 'KMDFHNFFCPFFSDIVFEWNHLDSKPPI' (len=28) ← remove F from position 23
  Step  69: 'KMDFNFFCPFFSDIVFEWNHLDSKPPI' (len=27) ← remove H from position 4
  Step  70: 'KMDFNFFCPFFSDIVFEWNHCDSKPPI' (len=27) ← replace L at position 20 with C
  Step  71: 'KMDFHFFCPFFSDIVFEWNHCDSKPPI' (len=27) ← replace N at position 4 with H
  Step  72: 'KMDFHFFCPFFSDIVFEWNHCDSFKPPI' (len=28) ← add F at position 23
  Step  73: 'KMDFHNFCPFFSDIVFEWNHCDSFKPPI' (len=28) ← replace F at position 5 with N
  Step  74: 'KMDFHNFFPFFSDIVFEWNHCDSFKPPI' (len=28) ← replace C at position 7 with F
  Step  75: 'KMDFVHNFFPFFSDIVFEWNHCDSFKPPI' (len=29) ← add V at position 4
  Step  76: 'KMDFVHNFFPFFSDIVKFEWNHCDSFKPPI' (len=30) ← add K at position 16
  Step  77: 'KMDFVHNFFPFFSDIVKFEWNHCDSFSKPPI' (len=31) ← add S at position 26
  Step  78: 'KMDFVHNFFPFFSDIVKFEWNEHCDSFSKPPI' (len=32) ← add E at position 21
  Step  79: 'KMDFVHNFFPFFSDPIVKFEWNEHCDSFSKPPI' (len=33) ← add P at position 14
  Step  80: 'KMDFVHNFFPFFSDPIVKFEWNEDCDSFSKPPI' (len=33) ← replace H at position 23 with D
  Step  81: 'KMDFVHNRFFPFFSDPIVKFEWNEDCDSFSKPPI' (len=34) ← add R at position 7
  Step  82: 'KMDFHHNRFFPFFSDPIVKFEWNEDCDSFSKPPI' (len=34) ← replace V at position 4 with H
  Step  83: 'KMDFHHNRFFPFFSDPIVKFEWNENDCDSFSKPPI' (len=35) ← add N at position 24
  Step  84: 'KMDFHNNRFFPFFSDPIVKFEWNENDCDSFSKPPI' (len=35) ← replace H at position 5 with N
  Step  85: 'KMDFHNFRFFPFFSDPIVKFEWNENDCDSFSKPPI' (len=35) ← replace N at position 6 with F
  Step  86: 'KMDFHNFFFFPFFSDPIVKFEWNENDCDSFSKPPI' (len=35) ← replace R at position 7 with F
  Step  87: 'KMDFHNFFFFPFFSDDPIVKFEWNENDCDSFSKPPI' (len=36) ← add D at position 14
  Step  88: 'KMDFHNFFFFPFFSDDPIVQKFEWNENDCDSFSKPPI' (len=37) ← add Q at position 19
  Step  89: 'KMDFHNFFCFPFFSDDPIVQKFEWNENDCDSFSKPPI' (len=37) ← replace F at position 8 with C
  Step  90: 'KMDFHNFFCPPFFSDDPIVQKFEWNENDCDSFSKPPI' (len=37) ← replace F at position 9 with P
  Step  91: 'KMDFHNFFCPFFFSDDPIVQKFEWNENDCDSFSKPPI' (len=37) ← replace P at position 10 with F
  Step  92: 'KMDFHNFFCPFFFSDVDPIVQKFEWNENDCDSFSKPPI' (len=38) ← add V at position 15
  Step  93: 'KMDFHNFFCPFWFSDVDPIVQKFEWNENDCDSFSKPPI' (len=38) ← replace F at position 11 with W
  Step  94: 'KMDFHNFFCPFWFSDVDPIVQKFEWNENDCDSFSPPI' (len=37) ← remove K from position 34
  Step  95: 'KMDFHNFFCFWFSDVDPIVQKFEWNENDCDSFSPPI' (len=36) ← remove P from position 9
  Step  96: 'KMDFHNFFCFWFSDVDPIVQKFEWNENDCDSSPPI' (len=35) ← remove F from position 31
  Step  97: 'KMDFHNFFCFWFSDVDPCVQKFEWNENDCDSSPPI' (len=35) ← replace I at position 17 with C
  Step  98: 'KMDFHNFFCFWFSDVDPCVQKFEWNENDICDSSPPI' (len=36) ← add I at position 28
  Step  99: 'KMDFHNFFCFWFSDVDPCVQKFEVWNENDICDSSPPI' (len=37) ← add V at position 23
  Step 100: 'KMDHNFFCFWFSDVDPCVQKFEVWNENDICDSSPPI' (len=36) ← remove F from position 3
  Step 101: 'KMDFNFFCFWFSDVDPCVQKFEVWNENDICDSSPPI' (len=36) ← replace H at position 3 with F
  Step 102: 'KMDFHFFCFWFSDVDPCVQKFEVWNENDICDSSPPI' (len=36) ← replace N at position 4 with H
  Step 103: 'KMDFHNFCFWFSDVDPCVQKFEVWNENDICDSSPPI' (len=36) ← replace F at position 5 with N
  Step 104: 'KMDFHNFCFWFSDYVDPCVQKFEVWNENDICDSSPPI' (len=37) ← add Y at position 13
  Step 105: 'KMDFHNFCFWFSDYVDPCVQKIFEVWNENDICDSSPPI' (len=38) ← add I at position 21
  Step 106: 'KMDFHNFCFWFSDVDPCVQKIFEVWNENDICDSSPPI' (len=37) ← remove Y from position 13
  Step 107: 'KMDFHNFFFWFSDVDPCVQKIFEVWNENDICDSSPPI' (len=37) ← replace C at position 7 with F
  Step 108: 'KMDFHNFFFWFSDIVDPCVQKIFEVWNENDICDSSPPI' (len=38) ← add I at position 13
  Step 109: 'KMDFHNFFCWFSDIVDPCVQKIFEVWNENDICDSSPPI' (len=38) ← replace F at position 8 with C
  Step 110: 'KMDFHNFFCPFSDIVDPCVQKIFEVWNENDICDSSPPI' (len=38) ← replace W at position 9 with P
  Step 111: 'KMDFHNFFCFPFSDIVDPCVQKIFEVWNENDICDSSPPI' (len=39) ← add F at position 9
  Step 112: 'KMDFHNFCFPFSDIVDPCVQKIFEVWNENDICDSSPPI' (len=38) ← remove F from position 7
  Step 113: 'NKMDFHNFCFPFSDIVDPCVQKIFEVWNENDICDSSPPI' (len=39) ← add N at position 0
  Step 114: 'NKMDFHNFCFPFSDIVDPCVAQKIFEVWNENDICDSSPPI' (len=40) ← add A at position 20
  Step 115: 'KKMDFHNFCFPFSDIVDPCVAQKIFEVWNENDICDSSPPI' (len=40) ← replace N at position 0 with K
  Step 116: 'KMMDFHNFCFPFSDIVDPCVAQKIFEVWNENDICDSSPPI' (len=40) ← replace K at position 1 with M
  Step 117: 'KMMDSFHNFCFPFSDIVDPCVAQKIFEVWNENDICDSSPPI' (len=41) ← add S at position 4
  Step 118: 'KMMDSFHNFCFPFSDIVDPCVAQKIFEVWNENDICDSESPPI' (len=42) ← add E at position 37
  Step 119: 'KMMDSFHNFCFPFSDIVDPCVAQKIFEVWNENDICDSESCPPI' (len=43) ← add C at position 39
  Step 120: 'KMMDSFHNFCFPFSDIVDPCVAQKIFEVWNENDICDSESCPPV' (len=43) ← replace I at position 42 with V
  Step 121: 'KMDDSFHNFCFPFSDIVDPCVAQKIFEVWNENDICDSESCPPV' (len=43) ← replace M at position 2 with D
  Step 122: 'KMDDSFHNFCFPFSDIVDPCVAQKIFEVWNPENDICDSESCPPV' (len=44) ← add P at position 30
  Step 123: 'KMDDSFHNFCFPFSDIVDPCVAQKIFEVDWNPENDICDSESCPPV' (len=45) ← add D at position 28
  Step 124: 'KMDDSFHNFDCFPFSDIVDPCVAQKIFEVDWNPENDICDSESCPPV' (len=46) ← add D at position 9
  Step 125: 'KMDFSFHNFDCFPFSDIVDPCVAQKIFEVDWNPENDICDSESCPPV' (len=46) ← replace D at position 3 with F
  Step 126: 'KMDFSFHNFDCFPFSDIVDPCVAQKIFEVDWNPENDICDSESCPPMV' (len=47) ← add M at position 45
  Step 127: 'KMDFHFHNFDCFPFSDIVDPCVAQKIFEVDWNPENDICDSESCPPMV' (len=47) ← replace S at position 4 with H
  Step 128: 'KMDFHFHNFDCFPFSDIVDPCVAQKIFEVDWNYPENDICDSESCPPMV' (len=48) ← add Y at position 32
  Step 129: 'KMDFHFHFDCFPFSDIVDPCVAQKIFEVDWNYPENDICDSESCPPMV' (len=47) ← remove N from position 7
  Step 130: 'KMDFHFHFDCFPFSDIVDPCVAQKIFEVDWNYPENICDSESCPPMV' (len=46) ← remove D from position 35
  Step 131: 'KMDFHFHFDCFPFSDIVDPCVAQKIFEVIDWNYPENICDSESCPPMV' (len=47) ← add I at position 28
  Step 132: 'KMDFHFHFDCFPFSDIVDPCVAQKIREVIDWNYPENICDSESCPPMV' (len=47) ← replace F at position 25 with R
  Step 133: 'KMDFHFHFDCFPFSDIVDPCKVAQKIREVIDWNYPENICDSESCPPMV' (len=48) ← add K at position 20
  Step 134: 'KMDFHNHFDCFPFSDIVDPCKVAQKIREVIDWNYPENICDSESCPPMV' (len=48) ← replace F at position 5 with N
  Step 135: 'KMDFHNFFDCFPFSDIVDPCKVAQKIREVIDWNYPENICDSESCPPMV' (len=48) ← replace H at position 6 with F
  Step 136: 'KMDFHDNFFDCFPFSDIVDPCKVAQKIREVIDWNYPENICDSESCPPMV' (len=49) ← add D at position 5
  Step 137: 'KMDFHDNFFDCFPFSDIVDPCKVQKIREVIDWNYPENICDSESCPPMV' (len=48) ← remove A from position 23
  Step 138: 'KMDFHDNFFDCFPFSDIVDPCKVQKIREVIDWNYPENVICDSESCPPMV' (len=49) ← add V at position 37
  Step 139: 'KMDFHDNFFDCFPFSDIVDPCKVQKIEVIDWNYPENVICDSESCPPMV' (len=48) ← remove R from position 26
  Step 140: 'KMDFHNNFFDCFPFSDIVDPCKVQKIEVIDWNYPENVICDSESCPPMV' (len=48) ← replace D at position 5 with N
  Step 141: 'KMDFHNNFFDCFPFSDIVDPCKVQKIEVIDWNYPENVICDSESCPPV' (len=47) ← remove M from position 46
  Step 142: 'KMDFHNFFFDCFPFSDIVDPCKVQKIEVIDWNYPENVICDSESCPPV' (len=47) ← replace N at position 6 with F
  Step 143: 'KMDFHNFFCDCFPFSDIVDPCKVQKIEVIDWNYPENVICDSESCPPV' (len=47) ← replace F at position 8 with C
  Step 144: 'KMDFHNFFCPCFPFSDIVDPCKVQKIEVIDWNYPENVICDSESCPPV' (len=47) ← replace D at position 9 with P
  Step 145: 'KMDFHNFFCPFFPFSDIVDPCKVQKIEVIDWNYPENVICDSESCPPV' (len=47) ← replace C at position 10 with F
  Step 146: 'KMDFHNFFCPFFPFSDIVDPCKVQKIEVIDIWNYPENVICDSESCPPV' (len=48) ← add I at position 30
  Step 147: 'KMDFHNFFCPFFPFSDIVDPCKVQKIEVIDIWNYPNENVICDSESCPPV' (len=49) ← add N at position 35
  Step 148: 'KMDFHNFFCPFWPFSDIVDPCKVQKIEVIDIWNYPNENVICDSESCPPV' (len=49) ← replace F at position 11 with W
  Step 149: 'KMDFHNFFCPFWVFSDIVDPCKVQKIEVIDIWNYPNENVICDSESCPPV' (len=49) ← replace P at position 12 with V
  Step 150: 'KMDFHNFFCPFWVCSDIVDPCKVQKIEVIDIWNYPNENVICDSESCPPV' (len=49) ← replace F at position 13 with C
  Step 151: 'KMDFHNFFCPFWVCSDIVDPCKVQKIEVIDIWNYPNENVICDSESCPPVY' (len=50) ← add Y at position 49
  Step 152: 'KDFHNFFCPFWVCSDIVDPCKVQKIEVIDIWNYPNENVICDSESCPPVY' (len=49) ← remove M from position 1
  Step 153: 'KDFHNFFCPFWVCSDIVDPCKVQKIEVIDIWNYPNENVICDSESPPVY' (len=48) ← remove C from position 44
  Step 154: 'KMFHNFFCPFWVCSDIVDPCKVQKIEVIDIWNYPNENVICDSESPPVY' (len=48) ← replace D at position 1 with M
  Step 155: 'KMDHNFFCPFWVCSDIVDPCKVQKIEVIDIWNYPNENVICDSESPPVY' (len=48) ← replace F at position 2 with D
  Step 156: 'KMDHNFFCPFWVCSDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← add N at position 21
  Step 157: 'KMDFNFFCPFWVCSDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace H at position 3 with F
  Step 158: 'KMDFHFFCPFWVCSDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace N at position 4 with H
  Step 159: 'KMDFHNFCPFWVCSDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace F at position 5 with N
  Step 160: 'KMDFHNFFPFWVCSDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace C at position 7 with F
  Step 161: 'KMDFHNFFCFWVCSDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace P at position 8 with C
  Step 162: 'KMDFHNFFCPWVCSDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace F at position 9 with P
  Step 163: 'KMDFHNFFCPFVCSDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace W at position 10 with F
  Step 164: 'KMDFHNFFCPFWCSDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace V at position 11 with W
  Step 165: 'KMDFHNFFCPFWVSDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace C at position 12 with V
  Step 166: 'KMDFHNFFCPFWVCDIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace S at position 13 with C
  Step 167: 'KMDFHNFFCPFWVCFIVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace D at position 14 with F
  Step 168: 'KMDFHNFFCPFWVCFQVDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace I at position 15 with Q
  Step 169: 'KMDFHNFFCPFWVCFQFDPCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace V at position 16 with F
  Step 170: 'KMDFHNFFCPFWVCFQFDWCKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace P at position 18 with W
  Step 171: 'KMDFHNFFCPFWVCFQFDWNKNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace C at position 19 with N
  Step 172: 'KMDFHNFFCPFWVCFQFDWNLNVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace K at position 20 with L
  Step 173: 'KMDFHNFFCPFWVCFQFDWNLSVQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace N at position 21 with S
  Step 174: 'KMDFHNFFCPFWVCFQFDWNLSTQKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace V at position 22 with T
  Step 175: 'KMDFHNFFCPFWVCFQFDWNLSTEKIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace Q at position 23 with E
  Step 176: 'KMDFHNFFCPFWVCFQFDWNLSTECIEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace K at position 24 with C
  Step 177: 'KMDFHNFFCPFWVCFQFDWNLSTECKEVIDIWNYPNENVICDSESPPVY' (len=49) ← replace I at position 25 with K
  Step 178: 'KMDFHNFFCPFWVCFQFDWNLSTECKKVIDIWNYPNENVICDSESPPVY' (len=49) ← replace E at position 26 with K
  Step 179: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLIDIWNYPNENVICDSESPPVY' (len=49) ← replace V at position 27 with L
  Step 180: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRDIWNYPNENVICDSESPPVY' (len=49) ← replace I at position 28 with R
  Step 181: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQIWNYPNENVICDSESPPVY' (len=49) ← replace D at position 29 with Q
  Step 182: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYWNYPNENVICDSESPPVY' (len=49) ← replace I at position 30 with Y
  Step 183: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCNYPNENVICDSESPPVY' (len=49) ← replace W at position 31 with C
  Step 184: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKYPNENVICDSESPPVY' (len=49) ← replace N at position 32 with K
  Step 185: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHPNENVICDSESPPVY' (len=49) ← replace Y at position 33 with H
  Step 186: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVNENVICDSESPPVY' (len=49) ← replace P at position 34 with V
  Step 187: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFENVICDSESPPVY' (len=49) ← replace N at position 35 with F
  Step 188: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKVICDSESPPVY' (len=49) ← replace N at position 37 with K
  Step 189: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKICDSESPPVY' (len=49) ← replace V at position 38 with K
  Step 190: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGCDSESPPVY' (len=49) ← replace I at position 39 with G
  Step 191: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFDSESPPVY' (len=49) ← replace C at position 40 with F
  Step 192: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWSESPPVY' (len=49) ← replace D at position 41 with W
  Step 193: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKESPPVY' (len=49) ← replace S at position 42 with K
  Step 194: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVSPPVY' (len=49) ← replace E at position 43 with V
  Step 195: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNPPVY' (len=49) ← replace S at position 44 with N
  Step 196: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCPVY' (len=49) ← replace P at position 45 with C
  Step 197: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYVY' (len=49) ← replace P at position 46 with Y
  Step 198: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTY' (len=49) ← replace V at position 47 with T
  Step 199: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYW' (len=50) ← add W at position 49
  Step 200: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWT' (len=51) ← add T at position 50
  Step 201: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTE' (len=52) ← add E at position 51
  Step 202: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTEL' (len=53) ← add L at position 52
  Step 203: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELF' (len=54) ← add F at position 53
  Step 204: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFK' (len=55) ← add K at position 54
  Step 205: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKC' (len=56) ← add C at position 55
  Step 206: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCI' (len=57) ← add I at position 56
  Step 207: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIY' (len=58) ← add Y at position 57
  Step 208: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYG' (len=59) ← add G at position 58
  Step 209: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGS' (len=60) ← add S at position 59
  Step 210: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSL' (len=61) ← add L at position 60
  Step 211: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLK' (len=62) ← add K at position 61
  Step 212: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKY' (len=63) ← add Y at position 62
  Step 213: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYY' (len=64) ← add Y at position 63
  Step 214: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYN' (len=65) ← add N at position 64
  Step 215: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNR' (len=66) ← add R at position 65
  Step 216: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNRE' (len=67) ← add E at position 66
  Step 217: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREG' (len=68) ← add G at position 67
  Step 218: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGY' (len=69) ← add Y at position 68
  Step 219: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYN' (len=70) ← add N at position 69
  Step 220: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNM' (len=71) ← add M at position 70
  Step 221: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMR' (len=72) ← add R at position 71
  Step 222: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRC' (len=73) ← add C at position 72
  Step 223: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCN' (len=74) ← add N at position 73
  Step 224: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNM' (len=75) ← add M at position 74
  Step 225: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMV' (len=76) ← add V at position 75
  Step 226: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVE' (len=77) ← add E at position 76
  Step 227: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEY' (len=78) ← add Y at position 77
  Step 228: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYC' (len=79) ← add C at position 78
  Step 229: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCE' (len=80) ← add E at position 79
  Step 230: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCEN' (len=81) ← add N at position 80
  Step 231: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQ' (len=82) ← add Q at position 81
  Step 232: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQF' (len=83) ← add F at position 82
  Step 233: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFD' (len=84) ← add D at position 83
  Step 234: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDV' (len=85) ← add V at position 84
  Step 235: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVF' (len=86) ← add F at position 85
  Step 236: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFN' (len=87) ← add N at position 86
  Step 237: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNP' (len=88) ← add P at position 87
  Step 238: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPK' (len=89) ← add K at position 88
  Step 239: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKN' (len=90) ← add N at position 89
  Step 240: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNM' (len=91) ← add M at position 90
  Step 241: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMW' (len=92) ← add W at position 91
  Step 242: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWF' (len=93) ← add F at position 92
  Step 243: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFR' (len=94) ← add R at position 93
  Step 244: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFRF' (len=95) ← add F at position 94
  Step 245: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFRFI' (len=96) ← add I at position 95
  Step 246: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFRFIQ' (len=97) ← add Q at position 96
  Step 247: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFRFIQG' (len=98) ← add G at position 97
  Step 248: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFRFIQGH' (len=99) ← add H at position 98
  Step 249: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFRFIQGHS' (len=100) ← add S at position 99
  Step 250: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFRFIQGHSC' (len=101) ← add C at position 100
  Step 251: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFRFIQGHSCL' (len=102) ← add L at position 101
  Step 252: 'KMDFHNFFCPFWVCFQFDWNLSTECKKLRQYCKHVFEKKGFWKVNCYTYWTELFKCIYGSLKYYNREGYNMRCNMVEYCENQFDVFNPKNMWFRFIQGHSCLY' (len=103) ← add Y at position 102

Trajectory Metadata:
  {
  "approach": "flow_matching",
  "config": {
    "correct_action_prob": 0.4,
    "temperature": 1.5,
    "random_action_weights": {
      "add": 0.6,
      "delete": 0.2,
      "replace": 0.2
    }
  },
  "target_length": 103,
  "total_steps": 252
}

================================================================================
FORWARD TRAJECTORY: Random_Protein_6_Len11
Target Sequence: CFGWVVIETRP
Target Length: 11
Total Steps: 17
Efficiency: 0.65 chars/step
Success: Yes

Full State Progression:
  Step   0: '' (len=0) [START]
  Step   1: 'C' (len=1) ← add C at position 0
  Step   2: 'CF' (len=2) ← add F at position 1
  Step   3: 'CFA' (len=3) ← add A at position 2
  Step   4: 'CFG' (len=3) ← replace A at position 2 with G
  Step   5: 'CFGM' (len=4) ← add M at position 3
  Step   6: 'CFGW' (len=4) ← replace M at position 3 with W
  Step   7: 'CFGWV' (len=5) ← add V at position 4
  Step   8: 'CFGWVV' (len=6) ← add V at position 5
  Step   9: 'FCFGWVV' (len=7) ← add F at position 0
  Step  10: 'CCFGWVV' (len=7) ← replace F at position 0 with C
  Step  11: 'CCGWVV' (len=6) ← remove F from position 2
  Step  12: 'CFGWVV' (len=6) ← replace C at position 1 with F
  Step  13: 'CFGWVVI' (len=7) ← add I at position 6
  Step  14: 'CFGWVVIE' (len=8) ← add E at position 7
  Step  15: 'CFGWVVIET' (len=9) ← add T at position 8
  Step  16: 'CFGWVVIETR' (len=10) ← add R at position 9
  Step  17: 'CFGWVVIETRP' (len=11) ← add P at position 10

Trajectory Metadata:
  {
  "approach": "flow_matching",
  "config": {
    "correct_action_prob": 0.4,
    "temperature": 1.5,
    "random_action_weights": {
      "add": 0.6,
      "delete": 0.2,
      "replace": 0.2
    }
  },
  "target_length": 11,
  "total_steps": 17
}

================================================================================
SUMMARY STATISTICS
================================================================================
Total sequences processed: 6
Success rate: 100.0% (6/6)
Average efficiency: 0.40 chars/step
Min efficiency: 0.26
Max efficiency: 0.65

Sequence Statistics:
Average length: 43.7
Min length: 11
Max length: 103
Length range: 11-103

Configuration used:
  correct_action_prob: 0.4
  temperature: 1.5
  random_action_weights: {'add': 0.6, 'delete': 0.2, 'replace': 0.2}
  seed: 42

Natural amino acids: ['A', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'P', 'Q', 'R', 'S', 'T', 'V', 'W', 'Y']
