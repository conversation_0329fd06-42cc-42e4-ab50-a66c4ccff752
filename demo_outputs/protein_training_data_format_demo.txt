Legend (0-based indexing):
  A6B      = Replace A at 6 with B
  7C       = Insert C at 7
  -5F      = Remove F at 5
  A3/B7    = Swap A at 3 with B at 7
  7[KR]    = Insert motif KR at 7
  -[KR]5   = Remove motif KR at 5
  [KR]5[EE]= Replace motif KR at 5 with EE

=== Point Mutations (residue replace) ===

Noise process: point_mutations
Input:  Reconstruct protein from: MRQHFPNYMFYPLLWHQH
Output:
  Y7F
  Y10K
  F4V
  Y10A
  P5L
  [current state: MRQHFPNYMFYPLLWHQH]
  N6L
  H15S
  F9G
  H15I
  Q16D
  [current state: MRQHFPNYMFYPLLWHQH]
  P11C
  H15V
  Y10T
  Q2F
  F9I
  [current state: MRQHFPNYMFYPLLWHQH]
  L13F
  H3F
  W14F
  Q16K
  Q16P
  [current state: MRQHFPNYMFYPLLWHQH]
  Y10L
  M8N
  Y7F
  Q16F
  R1K
  [current state: MRQHFPNYMFYPLLWHQH]
  W14Y
  H17A
  W14P
  Q16L
  H3L
  [current state: MRQHFPNYMFYPLLWHQH]
Result: MKFLVLLFNILCLFPVLA

Noise process: point_mutations
Input:  Reconstruct protein from: KVFTVLKQVTVSTTTVEA
Output:
  V1S
  V8L
  T3G
  V1N
  V1V
  [current state: KVFTVLKQVTVSTTTVEA]
  T3L
  K6I
  T12L
  E16G
  V10L
  [current state: KVFTVLKQVTVSTTTVEA]
  E16I
  F2A
  T9I
  E16L
  V1K
  [current state: KVFTVLKQVTVSTTTVEA]
  T13F
  V1Q
  Q7F
  F2K
  F2M
  [current state: KVFTVLKQVTVSTTTVEA]
  T14V
  F2F
  V1I
  V1K
  V4V
  [current state: KVFTVLKQVTVSTTTVEA]
  K6L
  T14P
  S11C
  V8N
  K0M
  [current state: KVFTVLKQVTVSTTTVEA]
Result: MKFLVLLFNILCLFPVLA

Noise process: point_mutations
Input:  Reconstruct protein from: YDGEPGHCRMYHPQKKTHQP
Output:
  M9P
  H11S
  K14Y
  P4F
  K14S
  [current state: YDGEPGHCRMYHPQKKTHQP]
  P19M
  D1C
  H11Q
  Y0N
  K15S
  [current state: YDGEPGHCRMYHPQKKTHQP]
  M9Y
  C7H
  Q18I
  C7P
  Y0A
  [current state: YDGEPGHCRMYHPQKKTHQP]
  Q18M
  H17V
  K14P
  H11S
  R8K
  [current state: YDGEPGHCRMYHPQKKTHQP]
  Y10M
  H11M
  K14H
  G2D
  C7I
  [current state: YDGEPGHCRMYHPQKKTHQP]
  M9L
  Q18W
  H11N
  P19Y
  K14R
  [current state: YDGEPGHCRMYHPQKKTHQP]
Result: ACDEFGHIKLMNPQRSTVWY

Noise process: point_mutations
Input:  Reconstruct protein from: DKDSLNHQVLDFYTMELVPY
Output:
  V8K
  D0R
  P18Y
  P18S
  P18F
  [current state: DKDSLNHQVLDFYTMELVPY]
  K1R
  T13Q
  D10E
  M14M
  Q7Y
  [current state: DKDSLNHQVLDFYTMELVPY]
  Q7I
  E15H
  N5G
  S3L
  M14R
  [current state: DKDSLNHQVLDFYTMELVPY]
  K1R
  L16T
  D0V
  H6H
  Y12P
  [current state: DKDSLNHQVLDFYTMELVPY]
  L4F
  D0A
  E15S
  F11K
  K1C
  [current state: DKDSLNHQVLDFYTMELVPY]
  F11N
  S3E
  P18M
  D10M
  P18W
  [current state: DKDSLNHQVLDFYTMELVPY]
Result: ACDEFGHIKLMNPQRSTVWY

Noise process: point_mutations
Input:  Reconstruct protein from: MFIDDKAKYKPYAWTE
Output:
  D3V
  A6D
  D3T
  M0I
  K5F
  [current state: MFIDDKAKYKPYAWTE]
  Y11W
  P10G
  K9R
  A12P
  F1C
  [current state: MFIDDKAKYKPYAWTE]
  A12K
  F1K
  Y11S
  Y8E
  P10S
  [current state: MFIDDKAKYKPYAWTE]
  K9I
  K5K
  E15W
  T14V
  W13R
  [current state: MFIDDKAKYKPYAWTE]
  T14K
  D4V
  A6I
  K9R
  K9S
  [current state: MFIDDKAKYKPYAWTE]
  A6K
  E15V
  M0M
  I2A
  E15T
  [current state: MFIDDKAKYKPYAWTE]
Result: MKATVKKKESSSKRKT

Noise process: point_mutations
Input:  Reconstruct protein from: YYRPFKPCKYGSCTIL
Output:
  R2C
  P3V
  K8I
  Y0M
  K5G
  [current state: YYRPFKPCKYGSCTIL]
  K5N
  T13Y
  C7K
  K5T
  Y9S
  [current state: YYRPFKPCKYGSCTIL]
  L15T
  F4K
  K5A
  C12K
  T13H
  [current state: YYRPFKPCKYGSCTIL]
  P6T
  G10H
  G10K
  K8E
  P6K
  [current state: YYRPFKPCKYGSCTIL]
  F4R
  L15T
  P3T
  R2A
  Y1K
  [current state: YYRPFKPCKYGSCTIL]
  K5K
  T13R
  F4V
  I14K
  G10S
  [current state: YYRPFKPCKYGSCTIL]
Result: MKATVKKKESSSKRKT

=== Motif Dropout (motif remove) ===

Noise process: motif_dropout
Input:  Reconstruct protein from: M
Output:
  1[KPV]
  2[FNF]
  4[ILCL]
  3[LVLLF]
  16[LA]
  [current state: M]
Result: MKFLVLLFNILCLFPVLA

Noise process: motif_dropout
Input:  Reconstruct protein from: L
Output:
  1[LLF]
  4[LA]
  0[MKFLV]
  9[PV]
  7[FNILC]
  [current state: L]
Result: MKFLVLLFNILCLFPVLA

Noise process: motif_dropout
Input:  Reconstruct protein from: 
Output:
  0[DE]
  0[AC]
  4[FL]
  6[MTV]
  9[WY]
  [current state: ]
  5[GHIK]
  11[NPQRS]
Result: ACDEFGHIKLMNPQRSTVWY

Noise process: motif_dropout
Input:  Reconstruct protein from: V
Output:
  1[WY]
  0[MRST]
  1[NPQ]
  0[AHIKL]
  1[CDEFG]
  [current state: V]
Result: ACDEFGHIKLMNPQRSTVWY

Noise process: motif_dropout
Input:  Reconstruct protein from: 
Output:
  0[ESRKT]
  0[MKK]
  2[ATVKK]
  10[SSK]
Result: MKATVKKKESSSKRKT

Noise process: motif_dropout
Input:  Reconstruct protein from: 
Output:
  0[MK]
  2[AT]
  4[VKKK]
  8[ESSS]
  12[KRKT]
  [current state: ]
Result: MKATVKKKESSSKRKT

=== Balanced (add/remove/replace, motifs allowed) ===

Noise process: fixed_steps
Input:  Reconstruct protein from: WYVRQSFILRELTCEETG
Output:
  9L
  -13C
  Y1F
  0W
  19C
  [current state: WYVRQSFILRELTCEETG]
  12F
  -6F
  E10C
  -14E
  -10E
  [current state: WYVRQSFILRELTCEETG]
  -15E
  -[CE]13
  L11D
  14G
  -[CE]13
  [current state: WYVRQSFILRELTCEETG]
  S5L
  E10/L11
  Y1K
  14[PV]
  W0H
  [current state: WYVRQSFILRELTCEETG]
  -10E
  Q4L
  -11L
  W0M
  3L
  [current state: WYVRQSFILRELTCEETG]
  8N
  -13C
  15[LV]
  T16A
  11C
  [current state: WYVRQSFILRELTCEETG]
Result: MKFLVLLFNILCLFPVLA

Noise process: fixed_steps
Input:  Reconstruct protein from: DKKVICNQVLLKNGLCVCALWM
Output:
  -18A
  2M
  V8T
  C5F
  -7Q
  [current state: DKKVICNQVLLKNGLCVCALWM]
  W20M
  8R
  18L
  V16Y
  -17C
  [current state: DKKVICNQVLLKNGLCVCALWM]
  N12K
  I4R
  D0M
  -3V
  -2K
  [current state: DKKVICNQVLLKNGLCVCALWM]
  N6/G13
  -13G
  L10F
  -17C
  -2K
  [current state: DKKVICNQVLLKNGLCVCALWM]
  -3V
  4Y
  K11I
  -12N
  15F
  [current state: DKKVICNQVLLKNGLCVCALWM]
  -[VI]3
  15[PA]
  16[CL]
  V16V
  -14L
  [current state: DKKVICNQVLLKNGLCVCALWM]
Result: MKFLVLLFNILCLFPVLA

Noise process: fixed_steps
Input:  Reconstruct protein from: ACSACGHFICTFQMNPSQRTW
Output:
  19S
  Q12K
  T10L
  -2S
  C1E
  [current state: ACSACGHFICTFQMNPSQRTW]
  20P
  S2C
  -9C
  T19V
  I8C
  [current state: ACSACGHFICTFQMNPSQRTW]
  1T
  12L
  C1D
  S2/I8
  11H
  [current state: ACSACGHFICTFQMNPSQRTW]
  13Q
  H6H
  I8C
  -18R
  2E
  [current state: ACSACGHFICTFQMNPSQRTW]
  -12Q
  -[CG]4
  1C
  -6H
  -11F
  [current state: ACSACGHFICTFQMNPSQRTW]
  -7F
  -6H
  C4/F7
  H6H
  19Y
  [current state: ACSACGHFICTFQMNPSQRTW]
Result: ACDEFGHIKLMNPQRSTVWY

Noise process: fixed_steps
Input:  Reconstruct protein from: ASERHCIPAPMNYNK
Output:
  10G
  ?15P
  16C
  S1/?16
  -8A
  [current state: ASERHCIPAPMNYNK]
  ?15V
  -12Y
  -9P
  7S
  4G
  [current state: ASERHCIPAPMNYNK]
  N11H
  15F
  5R
  17[NMT]
  17C
  [current state: ASERHCIPAPMNYNK]
  ?16Q
  2D
  H4/?19
  N13L
  ?18K
  [current state: ASERHCIPAPMNYNK]
  M10K
  23Y
  23W
  -18?
  -6I
  [current state: ASERHCIPAPMNYNK]
  -[MN]10
  N11S
  N11/?16
  -7P
  H4F
  [current state: ASERHCIPAPMNYNK]
Result: ACDEFGHIKLMNPQRSTVWY

Noise process: fixed_steps
Input:  Reconstruct protein from: QQKESSTSKIADV
Output:
  13M
  7E
  -11D
  S7Y
  1K
  [current state: QQKESSTSKIADV]
  12W
  D11T
  Q0M
  8Y
  -13?
  [current state: QQKESSTSKIADV]
  S4E
  3V
  ?15W
  -8K
  V12M
  [current state: QQKESSTSKIADV]
  K2A
  -[SK]7
  13[KT]
  3T
  D11N
  [current state: QQKESSTSKIADV]
  -8K
  11D
  ?16C
  -16?
  6[KK]
  [current state: QQKESSTSKIADV]
  9R
  ?13S
  -[??]14
  -14?
  I9/?13
  [current state: QQKESSTSKIADV]
Result: MKATVKKKESSSKRKT

Noise process: fixed_steps
Input:  Reconstruct protein from: FDYREAII
Output:
  -2Y
  A5W
  5I
  E4T
  3K
  [current state: FDYREAII]
  8N
  -8?
  I6/?8
  I7F
  F0/?8
  [current state: FDYREAII]
  2[WAV]
  -10?
  6S
  D1A
  -0F
  [current state: FDYREAII]
  -9?
  5K
  D1T
  9D
  2A
  [current state: FDYREAII]
  -[YR]2
  -10?
  R3/I7
  R3/E4
  0M
  [current state: FDYREAII]
  A5K
  6[ESS]
  ?12K
  6K
  1K
  [current state: FDYREAII]
Result: MKATVKKKESSSKRKT

