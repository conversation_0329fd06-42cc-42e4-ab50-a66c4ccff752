#!/usr/bin/env python3
"""
Analyze the distribution of operations across different stages of the diffusion trajectories
to identify if there are still imbalances in the final steps.
"""

import json
from collections import Counter, defaultdict
from pathlib import Path

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False


def parse_action(action_str):
    """Parse an action string to extract operation type."""
    if action_str.startswith("add"):
        return "add"
    elif action_str.startswith("remove"):
        return "remove"
    elif action_str.startswith("replace"):
        return "replace"
    else:
        return "other"


def analyze_trajectory_stages(example, num_stages=5):
    """Analyze operation distribution across different stages of a trajectory."""
    actions = []
    
    # Extract only action strings (skip state updates)
    for item in example["reverse_process"]:
        if not item.startswith("Current state") and not item.startswith("Final state"):
            actions.append(parse_action(item))
    
    total_actions = len(actions)
    if total_actions == 0:
        return {}
    
    # Divide trajectory into stages
    stage_size = max(1, total_actions // num_stages)
    stages = {}
    
    for stage in range(num_stages):
        start_idx = stage * stage_size
        end_idx = min((stage + 1) * stage_size, total_actions)
        
        if start_idx >= total_actions:
            break
            
        stage_actions = actions[start_idx:end_idx]
        stage_counts = Counter(stage_actions)
        
        # Calculate percentages
        stage_total = sum(stage_counts.values())
        stage_percentages = {op: count/stage_total*100 for op, count in stage_counts.items()}
        
        stages[f"stage_{stage+1}"] = {
            "counts": dict(stage_counts),
            "percentages": stage_percentages,
            "range": f"{start_idx+1}-{end_idx}",
            "total": stage_total
        }
    
    return stages


def analyze_all_trajectories(data_file):
    """Analyze all trajectories in the data file."""
    with open(data_file, 'r') as f:
        examples = json.load(f)
    
    print(f"Analyzing {len(examples)} trajectories from {data_file}")
    print("="*80)
    
    all_stage_data = defaultdict(lambda: defaultdict(list))
    
    for i, example in enumerate(examples):
        print(f"\nTrajectory {i+1}: {example['protein_name']}")
        print(f"Sequence length: {len(example['final_state'])}")
        print(f"Total steps: {example['num_steps']}")
        
        stages = analyze_trajectory_stages(example)
        
        for stage_name, stage_data in stages.items():
            print(f"\n  {stage_name.upper()} (steps {stage_data['range']}):")
            print(f"    Total actions: {stage_data['total']}")
            print(f"    Counts: {stage_data['counts']}")
            print(f"    Percentages: {stage_data['percentages']}")
            
            # Collect data for overall analysis
            for op, pct in stage_data['percentages'].items():
                all_stage_data[stage_name][op].append(pct)
    
    # Overall stage analysis
    print(f"\n" + "="*80)
    print("OVERALL STAGE ANALYSIS")
    print("="*80)
    
    target_weights = {"add": 40.0, "delete": 30.0, "replace": 30.0}
    
    for stage_name in sorted(all_stage_data.keys()):
        print(f"\n{stage_name.upper()}:")
        stage_ops = all_stage_data[stage_name]
        
        for op in ["add", "remove", "replace"]:
            if op in stage_ops:
                values = stage_ops[op]
                avg_pct = sum(values) / len(values)
                min_pct = min(values)
                max_pct = max(values)
                
                # Map operation names for comparison
                target_op = "delete" if op == "remove" else op
                target_pct = target_weights.get(target_op, 0)
                diff = abs(avg_pct - target_pct)
                
                status = "✓" if diff < 10 else "⚠" if diff < 20 else "✗"
                
                print(f"  {op}: avg {avg_pct:.1f}% (range: {min_pct:.1f}-{max_pct:.1f}%), "
                      f"target {target_pct:.1f}%, diff {diff:.1f}% {status}")
    
    return all_stage_data


def plot_stage_analysis(stage_data, output_file="stage_analysis.png"):
    """Create a plot showing operation distribution across stages."""
    if not HAS_MATPLOTLIB:
        print("matplotlib not available, skipping plot generation")
        return

    stages = sorted(stage_data.keys())
    operations = ["add", "remove", "replace"]

    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle("Operation Distribution Across Trajectory Stages")

    for i, op in enumerate(operations):
        ax = axes[i]

        stage_avgs = []
        stage_errors = []

        for stage in stages:
            if op in stage_data[stage]:
                values = stage_data[stage][op]
                avg = sum(values) / len(values)
                std = (sum((x - avg) ** 2 for x in values) / len(values)) ** 0.5
                stage_avgs.append(avg)
                stage_errors.append(std)
            else:
                stage_avgs.append(0)
                stage_errors.append(0)

        x_pos = range(len(stages))
        bars = ax.bar(x_pos, stage_avgs, yerr=stage_errors, capsize=5, alpha=0.7)

        # Add target line
        target_op = "delete" if op == "remove" else op
        target_pct = {"add": 40, "delete": 30, "replace": 30}[target_op]
        ax.axhline(y=target_pct, color='red', linestyle='--', alpha=0.7, label=f'Target ({target_pct}%)')

        ax.set_xlabel("Trajectory Stage")
        ax.set_ylabel("Percentage")
        ax.set_title(f"{op.capitalize()} Operations")
        ax.set_xticks(x_pos)
        ax.set_xticklabels([s.replace("stage_", "Stage ") for s in stages])
        ax.legend()
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"\nPlot saved to {output_file}")


def main():
    """Main analysis function."""
    print("Analyzing Trajectory Stages for Operation Balance")
    print("="*60)
    
    data_file = "./data/test_balanced_diffusion_v3/test_data.json"
    
    if not Path(data_file).exists():
        print(f"Error: Data file {data_file} not found!")
        return
    
    # Analyze trajectories
    stage_data = analyze_all_trajectories(data_file)
    
    # Create visualization
    plot_stage_analysis(stage_data)
    
    # Summary and recommendations
    print(f"\n" + "="*80)
    print("ANALYSIS SUMMARY AND RECOMMENDATIONS")
    print("="*80)
    
    # Check for imbalances in final stages
    final_stage = max(stage_data.keys()) if stage_data else None
    
    if final_stage:
        print(f"\nFinal Stage Analysis ({final_stage}):")
        final_ops = stage_data[final_stage]
        
        imbalances = []
        for op in ["add", "remove", "replace"]:
            if op in final_ops:
                values = final_ops[op]
                avg_pct = sum(values) / len(values)
                target_op = "delete" if op == "remove" else op
                target_pct = {"add": 40, "delete": 30, "replace": 30}[target_op]
                diff = abs(avg_pct - target_pct)
                
                if diff > 15:  # Significant imbalance
                    imbalances.append((op, avg_pct, target_pct, diff))
        
        if imbalances:
            print("\n⚠ SIGNIFICANT IMBALANCES DETECTED IN FINAL STAGE:")
            for op, actual, target, diff in imbalances:
                print(f"  - {op}: {actual:.1f}% (target: {target:.1f}%, diff: {diff:.1f}%)")
            
            print("\nRECOMMENDATIONS:")
            print("1. Increase the weight adjustment factor in _apply_balanced_random_action")
            print("2. Apply stronger balance correction in the final completion phase")
            print("3. Consider separate balance tracking for completion vs. random steps")
            print("4. Implement stage-aware operation weighting")
        else:
            print("\n✓ Final stage operation balance is within acceptable range")
    
    print(f"\n" + "="*80)
    print("ROOT CAUSE ANALYSIS:")
    print("The imbalance likely occurs because:")
    print("1. Completion steps (force completion) may not use balanced random actions")
    print("2. The balance adjustment may not be aggressive enough")
    print("3. Early stage imbalances compound in later stages")
    print("4. The target sequence structure may inherently favor certain operations")


if __name__ == "__main__":
    main()
