#!/usr/bin/env python3
"""
Test the uniform distribution of operations in trajectory generation.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from diffusion_via_reasoning.trajectory.framework import Trajectory, TrajectoryStep, apply_step
import random
from dataclasses import dataclass
from typing import Dict, Optional, List

@dataclass
class DirectReverseConfig:
    """Configuration for direct reverse trajectory generation."""
    correct_action_prob: float = 0.7  # Probability of taking correct action (toward target)
    temperature: float = 0.8           # Controls randomness in trajectory length
    random_action_weights: Dict[str, float] = None
    seed: Optional[int] = None
    adaptive_correction: bool = True   # Use adaptive correction probability
    min_correct_prob: float = 0.7     # Minimum correct action probability
    max_correct_prob: float = 0.95    # Maximum correct action probability at target length
    replace_prob: float = 0.5         # Probability of replace operations in random phase
    add_prob: float = 0.4             # Probability of add operations in random phase
    
    def __post_init__(self):
        if self.random_action_weights is None:
            # For forward: favor additions, minimal deletions
            self.random_action_weights = {"add": 0.6, "delete": 0.2, "replace": 0.2}


def analyze_operation_distribution(steps: List[TrajectoryStep]) -> Dict:
    """Analyze the distribution of operations throughout the trajectory."""
    total_steps = len(steps)
    if total_steps == 0:
        return {}
    
    # Divide trajectory into 4 quarters
    quarter_size = total_steps // 4
    quarters = {
        "Q1 (0-25%)": steps[:quarter_size],
        "Q2 (25-50%)": steps[quarter_size:2*quarter_size],
        "Q3 (50-75%)": steps[2*quarter_size:3*quarter_size],
        "Q4 (75-100%)": steps[3*quarter_size:]
    }
    
    analysis = {}
    for quarter_name, quarter_steps in quarters.items():
        action_counts = {"add": 0, "remove": 0, "replace": 0}
        for step in quarter_steps:
            action_counts[step.action] += 1
        
        total_in_quarter = len(quarter_steps)
        if total_in_quarter > 0:
            action_percentages = {
                action: count / total_in_quarter * 100 
                for action, count in action_counts.items()
            }
        else:
            action_percentages = {"add": 0, "remove": 0, "replace": 0}
        
        analysis[quarter_name] = {
            "counts": action_counts,
            "percentages": action_percentages,
            "total": total_in_quarter
        }
    
    return analysis


def test_uniform_distribution():
    """Test the uniform distribution approach."""
    # Test protein sequence
    target = "MDPIRCFSCNKIMKSPNEKGMVFVRNMKKEDREQFFKKFNYTRLCCKRMYLSAVNFQDELFQYENARSTLNVDGTITKPF"
    
    print(f"Testing uniform distribution for target sequence of length {len(target)}")
    print(f"Target: {target}")
    print()
    
    # Test with different configurations
    configs = [
        {
            "name": "Balanced Operations",
            "temperature": 1.0,
            "replace_prob": 0.3,
            "add_prob": 0.3
        },
        {
            "name": "More Replace/Remove",
            "temperature": 1.5,
            "replace_prob": 0.5,
            "add_prob": 0.3
        },
        {
            "name": "High Diversity",
            "temperature": 2.0,
            "replace_prob": 0.4,
            "add_prob": 0.4
        }
    ]
    
    for config_info in configs:
        print(f"=== {config_info['name']} ===")
        print(f"Temperature: {config_info['temperature']}")
        print(f"Replace prob: {config_info['replace_prob']}")
        print(f"Add prob: {config_info['add_prob']}")
        print()
        
        # Create a simple mock trajectory generator for testing
        steps = []
        current = ""
        
        # Simulate the uniform distribution algorithm
        target_length = len(target)
        base_steps = target_length
        extra_steps = int(config_info['temperature'] * target_length)
        total_planned_steps = base_steps + extra_steps
        
        # Create operation plan
        operation_plan = []
        
        # Add target operations
        for i in range(target_length):
            operation_plan.append(("add_target", i, target[i]))
        
        # Add random operations
        random.seed(42)  # For reproducible results
        for _ in range(extra_steps):
            rand_val = random.random()
            if rand_val < config_info['add_prob']:
                operation_plan.append(("add_random", None, None))
            elif rand_val < config_info['add_prob'] + config_info['replace_prob']:
                operation_plan.append(("replace", None, None))
            else:
                operation_plan.append(("remove", None, None))
        
        # Shuffle for uniform distribution
        random.shuffle(operation_plan)
        
        # Convert to steps (simplified simulation)
        for i, (op_type, _, _) in enumerate(operation_plan):
            if op_type == "add_target" or op_type == "add_random":
                steps.append(TrajectoryStep(action="add", position=0, token="A"))
            elif op_type == "remove":
                steps.append(TrajectoryStep(action="remove", position=0, token="A"))
            elif op_type == "replace":
                steps.append(TrajectoryStep(action="replace", position=0, token="A", replaced_token="B"))
        
        # Analyze distribution
        analysis = analyze_operation_distribution(steps)
        
        print(f"Total steps: {len(steps)}")
        print("Distribution by quarters:")
        for quarter, data in analysis.items():
            print(f"  {quarter}: {data['total']} steps")
            print(f"    Add: {data['percentages']['add']:.1f}% ({data['counts']['add']})")
            print(f"    Remove: {data['percentages']['remove']:.1f}% ({data['counts']['remove']})")
            print(f"    Replace: {data['percentages']['replace']:.1f}% ({data['counts']['replace']})")
        
        # Calculate overall distribution
        total_counts = {"add": 0, "remove": 0, "replace": 0}
        for step in steps:
            total_counts[step.action] += 1
        
        print("Overall distribution:")
        for action, count in total_counts.items():
            percentage = count / len(steps) * 100 if steps else 0
            print(f"  {action}: {percentage:.1f}% ({count})")
        
        print()


if __name__ == "__main__":
    test_uniform_distribution()
