#!/usr/bin/env python3
"""
Simple debug script to check state tracking.
"""

import json
from pathlib import Path


def parse_action_string(action_str):
    """Parse an action string into components."""
    if action_str.startswith("add "):
        # Format: "add X at position Y"
        parts = action_str.split()
        token = parts[1]
        position = int(parts[4])
        return "add", position, token, None
    elif action_str.startswith("remove "):
        # Format: "remove X from position Y"
        parts = action_str.split()
        token = parts[1]
        position = int(parts[4])
        return "remove", position, token, None
    elif action_str.startswith("replace "):
        # Format: "replace X at position Y with Z"
        parts = action_str.split()
        old_token = parts[1]
        position = int(parts[4])
        new_token = parts[6]
        return "replace", position, new_token, old_token
    else:
        return None, None, None, None


def apply_action_string(current_state, action_str):
    """Apply an action string to the current state."""
    action, position, token, old_token = parse_action_string(action_str)
    
    if action == "add":
        return current_state[:position] + token + current_state[position:]
    elif action == "remove":
        if position < len(current_state):
            return current_state[:position] + current_state[position + 1:]
        else:
            return current_state
    elif action == "replace":
        if position < len(current_state):
            return current_state[:position] + token + current_state[position + 1:]
        else:
            return current_state
    else:
        return current_state


def test_existing_training_data():
    """Test the existing training data to find the state tracking bug."""
    print("Testing existing training data...")
    
    # Load a sample from the existing data
    data_file = "./data/CreateProtein_head1000_new/val_data.json"
    
    if not Path(data_file).exists():
        print(f"Data file {data_file} not found")
        return False
    
    with open(data_file, 'r') as f:
        examples = json.load(f)
    
    if not examples:
        print("No examples found in data file")
        return False
    
    # Take the first example
    example = examples[0]
    print(f"Testing example: {example['protein_name']}")
    print(f"Target: '{example['final_state'][:50]}...' (length: {len(example['final_state'])})")
    print(f"Number of steps: {example['num_steps']}")
    
    # Extract actions from reverse_process
    actions = []
    state_updates = {}
    
    for line in example['reverse_process']:
        if line.startswith("Current state after step "):
            # Extract step number and state
            parts = line.split(": ")
            step_info = parts[0]
            state = parts[1].strip("'")
            step_num = int(step_info.split()[-1])
            state_updates[step_num] = state
        elif not line.startswith("Final state"):
            actions.append(line)
    
    print(f"Found {len(actions)} actions and {len(state_updates)} state updates")
    
    # Apply actions manually and compare with recorded states
    current_state = ""
    mismatches = 0
    
    print("\nChecking first few state updates:")
    
    for i, action_str in enumerate(actions):
        step_num = i + 1
        
        # Apply action manually
        new_state = apply_action_string(current_state, action_str)
        current_state = new_state
        
        # Check if we have a recorded state for this step
        if step_num in state_updates:
            recorded_state = state_updates[step_num]
            match = current_state == recorded_state
            
            if step_num <= 20 or not match:  # Show first 20 or any mismatches
                print(f"Step {step_num}: {action_str}")
                print(f"  Manual result:   '{current_state[:50]}{'...' if len(current_state) > 50 else ''}'")
                print(f"  Recorded state:  '{recorded_state[:50]}{'...' if len(recorded_state) > 50 else ''}'")
                print(f"  Match: {match}")
            
            if not match:
                mismatches += 1
                if mismatches == 1:  # Show details for first mismatch
                    print(f"  ❌ FIRST MISMATCH FOUND!")
                    print(f"  Manual length: {len(current_state)}")
                    print(f"  Recorded length: {len(recorded_state)}")
                    
                    # Show character-by-character diff
                    min_len = min(len(current_state), len(recorded_state))
                    for j in range(min_len):
                        if current_state[j] != recorded_state[j]:
                            print(f"    First diff at position {j}: manual='{current_state[j]}', recorded='{recorded_state[j]}'")
                            break
                    
                    if len(current_state) != len(recorded_state):
                        print(f"    Length difference: manual={len(current_state)}, recorded={len(recorded_state)}")
                    
                    return False
    
    print(f"\nFinal manual state: '{current_state[:50]}{'...' if len(current_state) > 50 else ''}'")
    print(f"Target state:       '{example['final_state'][:50]}{'...' if len(example['final_state']) > 50 else ''}'")
    print(f"Final match: {current_state == example['final_state']}")
    print(f"Total mismatches found: {mismatches}")
    
    return mismatches == 0


def main():
    """Main debug function."""
    print("Debugging State Tracking in Training Data")
    print("="*50)
    
    success = test_existing_training_data()
    
    if success:
        print("\n✅ State tracking appears to be working correctly")
    else:
        print("\n❌ State tracking bug found!")


if __name__ == "__main__":
    main()
