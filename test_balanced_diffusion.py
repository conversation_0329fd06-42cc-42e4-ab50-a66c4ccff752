#!/usr/bin/env python3
"""
Test script to verify that the optimized protein diffusion training code
maintains balanced operation types throughout the diffusion process.
"""

import sys
import random
from pathlib import Path
from collections import Counter
from typing import List, Dict

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from diffusion_via_reasoning.trajectory.framework import Trajectory, TrajectoryStep, apply_step

# Import the classes from the integrated training script
sys.path.insert(0, str(Path(__file__).parent / "scripts"))
from integrated_protein_training import DirectReverseConfig, DirectReverseTrajectoryGenerator


def analyze_trajectory_operations(trajectory: Trajectory) -> Dict[str, int]:
    """Analyze the distribution of operations in a trajectory."""
    operation_counts = Counter()
    
    for step in trajectory.steps:
        operation_counts[step.action] += 1
    
    return dict(operation_counts)


def test_trajectory_generation(target_sequences: List[str], config: DirectReverseConfig):
    """Test trajectory generation for multiple sequences and analyze operation balance."""
    charset = set()
    for seq in target_sequences:
        charset.update(seq)
    charset = sorted(list(charset))
    
    generator = DirectReverseTrajectoryGenerator(charset, config)
    
    all_operations = Counter()
    results = []
    
    print(f"Testing trajectory generation for {len(target_sequences)} sequences...")
    print(f"Config: correct_action_prob={config.correct_action_prob}, temperature={config.temperature}")
    print(f"Random action weights: {config.random_action_weights}")
    print()
    
    for i, target in enumerate(target_sequences):
        print(f"Sequence {i+1}: '{target}' (length: {len(target)})")
        
        # Generate trajectory
        trajectory = generator.generate(target)
        
        # Verify trajectory reaches target
        current = ""
        for step in trajectory.steps:
            current = apply_step(current, step)
        
        success = current == target
        print(f"  Trajectory success: {success}")
        print(f"  Final state: '{current}'")
        print(f"  Total steps: {len(trajectory.steps)}")
        
        # Analyze operations
        ops = analyze_trajectory_operations(trajectory)
        print(f"  Operations: {ops}")
        
        # Calculate operation percentages
        total_ops = sum(ops.values())
        if total_ops > 0:
            percentages = {op: count/total_ops*100 for op, count in ops.items()}
            print(f"  Percentages: {percentages}")
        
        all_operations.update(ops)
        results.append({
            'target': target,
            'success': success,
            'final_state': current,
            'steps': len(trajectory.steps),
            'operations': ops
        })
        print()
    
    # Overall statistics
    print("="*60)
    print("OVERALL STATISTICS")
    print("="*60)
    total_ops = sum(all_operations.values())
    print(f"Total operations across all trajectories: {total_ops}")
    print(f"Operation distribution: {dict(all_operations)}")
    
    if total_ops > 0:
        overall_percentages = {op: count/total_ops*100 for op, count in all_operations.items()}
        print(f"Overall percentages: {overall_percentages}")
    
    success_rate = sum(1 for r in results if r['success']) / len(results) * 100
    print(f"Success rate: {success_rate:.1f}%")
    
    return results, dict(all_operations)


def main():
    """Main test function."""
    print("Testing Balanced Protein Diffusion Training")
    print("="*50)
    
    # Test sequences of different lengths
    test_sequences = [
        "ACDEFGHIKLMNPQRSTVWY",  # 20 amino acids
        "MKLLVLSLCFATRQVCTQVQGQCGSCWAFGAVEATQGRIIGGQCSGGLGCNSFRY",  # 54 amino acids
        "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPKALFWAKRHLVGKLNPXVQWAQTHQWLLTDWVKFQFQKLDLHAHFLLQWLKRHMMPKIIFKPKVAINQYEHD",  # 300+ amino acids (truncated for testing)
        "ACGT",  # Short sequence
        "MKTAYIAKQRQISFVKSHFSRQLEERLGLIEVQAPILSRVGDGTQDNLSGAEKAVQVKVKALPDAQFEVVHSLAKWKRQTLGQHDFSAGEGLYTHMKALRPDEDRLSPLHSVYVDQWDWERVMGDGERQFSTLKSTVEAIWAGIKATEAAVSEEFGLAPFLPDQIHFVHSQELLSRYPDLDAKGRERAIAKDLGAVFLVGIGGKLSDGHRHDVRAPDYDDWUQTPKALFWAKRHLVGKLNPXVQWAQTHQWLLTDWVKFQFQKLDLHAHFLLQWLKRHMMPKIIFKPKVAINQYEHD"[:100]  # 100 amino acids
    ]
    
    # Test with balanced action weights
    config = DirectReverseConfig(
        correct_action_prob=0.6,
        temperature=1.2,
        random_action_weights={"add": 0.4, "delete": 0.3, "replace": 0.3},
        seed=42
    )
    
    results, operations = test_trajectory_generation(test_sequences, config)
    
    # Check if operations are reasonably balanced
    total_ops = sum(operations.values())
    if total_ops > 0:
        add_pct = operations.get('add', 0) / total_ops * 100
        delete_pct = operations.get('remove', 0) / total_ops * 100
        replace_pct = operations.get('replace', 0) / total_ops * 100
        
        print(f"\nBalance Analysis:")
        print(f"Add operations: {add_pct:.1f}%")
        print(f"Delete operations: {delete_pct:.1f}%") 
        print(f"Replace operations: {replace_pct:.1f}%")
        
        # Check if the distribution is reasonable (not dominated by one operation)
        max_pct = max(add_pct, delete_pct, replace_pct)
        if max_pct > 80:
            print(f"WARNING: Operations are imbalanced - {max_pct:.1f}% dominated by one operation type")
        else:
            print("✓ Operations appear reasonably balanced")


if __name__ == "__main__":
    main()
