# Two-Node Distributed Training Guide

This guide explains how to run distributed training across two nodes for the GPT-2 diffusion model.

## Available Scripts

### 1. `scripts/manual_two_node_start.sh` (Recommended for your current setup)
Use this when you already have nodes allocated through SLURM (like your current job on gh092-093).

**Usage:**
```bash
# On both nodes, run the same command:
./scripts/manual_two_node_start.sh
```

The script automatically detects which node it's running on and assigns the appropriate rank.

### 2. `scripts/run_two_node_training.sh`
Manual script that requires you to specify node rank and master address.

**Usage:**
```bash
# On master node (gh092):
./scripts/run_two_node_training.sh 0 gh092

# On worker node (gh093):
./scripts/run_two_node_training.sh 1 gh092
```

### 3. `scripts/submit_two_node_training.sh`
SLURM submission script for automatic two-node job allocation.

**Usage:**
```bash
sbatch scripts/submit_two_node_training.sh
```

## Quick Start (For Your Current Job)

Since you already have nodes gh092 and gh093 allocated, follow these steps:

### Step 1: Diagnose the Setup (Optional but Recommended)

```bash
# On both nodes, run diagnostics first:
ssh gh092
cd /u/dzhang5/Diffusion_via_Reasoning
./scripts/diagnose_multi_node.sh

ssh gh093
cd /u/dzhang5/Diffusion_via_Reasoning
./scripts/diagnose_multi_node.sh
```

### Step 2: Test GPU Assignment (Recommended)

```bash
# On both nodes, test GPU assignment first:
ssh gh092
cd /u/dzhang5/Diffusion_via_Reasoning
./scripts/test_two_node_setup.sh

ssh gh093
cd /u/dzhang5/Diffusion_via_Reasoning
./scripts/test_two_node_setup.sh
```

### Step 3: Start Training

**Option A: Safe Mode (2 GPUs per node - Recommended)**
```bash
# Terminal 1 - SSH to gh092 (Master)
ssh gh092
cd /u/dzhang5/Diffusion_via_Reasoning
./scripts/manual_two_node_start_safe.sh

# Terminal 2 - SSH to gh093 (Worker)
ssh gh093
cd /u/dzhang5/Diffusion_via_Reasoning
./scripts/manual_two_node_start_safe.sh
```

**Option B: Standard Mode (4 GPUs per node)**
```bash
# Terminal 1 - SSH to gh092 (Master)
ssh gh092
cd /u/dzhang5/Diffusion_via_Reasoning
./scripts/manual_two_node_start.sh

# Terminal 2 - SSH to gh093 (Worker)
ssh gh093
cd /u/dzhang5/Diffusion_via_Reasoning
./scripts/manual_two_node_start.sh
```

**Option C: TCP Backend (If other options fail)**
```bash
# Terminal 1 - SSH to gh092 (Master)
ssh gh092
cd /u/dzhang5/Diffusion_via_Reasoning
./scripts/manual_two_node_start_tcp.sh

# Terminal 2 - SSH to gh093 (Worker)
ssh gh093
cd /u/dzhang5/Diffusion_via_Reasoning
./scripts/manual_two_node_start_tcp.sh
```

### Step 3: Monitor Progress
- Start the master node first, then the worker node within 30 seconds
- Both processes should connect and start training automatically

## Configuration Details

### Network Settings
- **Master Address**: gh092 (automatically detected)
- **Master Port**: 12355
- **Backend**: NCCL (optimized for multi-node GPU communication)
- **InfiniBand**: Enabled for high-speed inter-node communication

### Training Parameters
- **Nodes**: 2
- **GPUs per node**: 4 (total 8 GPUs)
- **Batch size per device**: 1
- **Gradient accumulation**: 4 steps
- **Max context length**: 15,000 tokens
- **Learning rate**: 2e-5

### Data and Output
- **Data**: `./data/CreateProtein_restructured_head100000`
- **Output**: `/tmp/dzhang5/Diffusion_via_Reasoning_restructured_head100000/training_runs`

## Troubleshooting

### Common Issues and Solutions

1. **GPU Indexing Errors (IndexError: list index out of range)**
   ```
   IndexError: list index out of range
   ```
   **Solutions:**
   - Use the safe mode script: `./scripts/manual_two_node_start_safe.sh` (2 GPUs per node)
   - Test GPU assignment first: `./scripts/test_two_node_setup.sh`
   - Check CUDA_VISIBLE_DEVICES matches available GPUs

2. **NCCL Communication Errors**
   ```
   torch.distributed.DistBackendError: failed to recv, got 0 bytes
   ```
   **Solutions:**
   - Use the TCP backend script: `./scripts/manual_two_node_start_tcp.sh`
   - Check network connectivity: `./scripts/diagnose_multi_node.sh`
   - Ensure both nodes start within 30 seconds of each other

2. **Port conflicts**
   - Change MASTER_PORT in the script if 12355 is in use
   - Check with: `netstat -tulpn | grep 12355`

3. **Different conda environments**
   - Ensure both nodes have the same conda environment activated
   - Verify with: `conda list | grep torch`

4. **Network Interface Issues**
   - The scripts now use `eth0` instead of `ib0` for better compatibility
   - InfiniBand is disabled to avoid connection issues

5. **Timing Issues**
   - Start master node first, then worker node immediately
   - If one fails, restart both nodes

### Monitoring

- **Check GPU usage**: `nvidia-smi`
- **Monitor network**: `iftop -i ib0`
- **View logs**: Check the output in your terminal or log files

## Performance Expectations

With 2 nodes × 4 GPUs = 8 total GPUs, you should see:
- ~8x speedup compared to single GPU
- ~2x speedup compared to single node (4 GPUs)
- Effective batch size: 1 × 8 × 4 = 32 (with gradient accumulation)

## Next Steps

After starting the training:
1. Monitor the initial logs to ensure all nodes connect properly
2. Check W&B dashboard for training metrics
3. Verify checkpoints are being saved to the output directory
4. Monitor GPU utilization across both nodes
