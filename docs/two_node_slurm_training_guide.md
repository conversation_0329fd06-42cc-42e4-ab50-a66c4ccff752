# Two-Node SLURM Distributed Training Guide

This guide covers the production-ready SLURM submission system for two-node distributed training of the GPT-2 diffusion model.

## Overview

The two-node SLURM system provides:
- **Automatic resource allocation**: 2 nodes × 4 GPUs = 8 total GPUs
- **Distributed coordination**: Automatic node rank assignment and communication setup
- **Production stability**: All network optimizations and GPU indexing fixes included
- **Comprehensive monitoring**: Built-in logging, error handling, and job management
- **Easy submission**: Simple commands for job submission and monitoring

## Files

### Core Scripts
- `scripts/submit_diffusion_training_2node.sh` - Main SLURM submission script
- `scripts/submit_and_monitor_2node.sh` - Helper script for job management
- `scripts/train_gpt2_diffusion.py` - Training script (with multi-node fixes)

### Documentation
- `docs/two_node_slurm_training_guide.md` - This guide
- `docs/two_node_training_guide.md` - Manual training guide

## Quick Start

### 1. Submit a Job

```bash
# Simple submission
sbatch scripts/submit_diffusion_training_2node.sh

# Or use the helper script
./scripts/submit_and_monitor_2node.sh submit
```

### 2. Monitor Jobs

```bash
# Check job status
./scripts/submit_and_monitor_2node.sh status

# View logs for a specific job
./scripts/submit_and_monitor_2node.sh logs <job_id>

# Cancel a job
./scripts/submit_and_monitor_2node.sh cancel <job_id>
```

## Configuration

### SLURM Resource Requirements

```bash
#SBATCH --nodes=2                # Exactly 2 nodes
#SBATCH --ntasks-per-node=1      # 1 task per node
#SBATCH --gpus-per-node=4        # 4 GPUs per node (8 total)
#SBATCH --cpus-per-task=48       # 48 CPUs per node
#SBATCH --mem=128G               # 128GB RAM per node
#SBATCH --time=48:00:00          # 48 hour time limit
#SBATCH --partition=ghx4-inte    # Multi-node partition
```

### Training Parameters

Optimized for 8 total GPUs:
- **Per-device batch size**: 1 (for 15k token sequences)
- **Gradient accumulation**: 2 steps
- **Effective batch size**: 1 × 8 × 2 = 16
- **Learning rate**: 2e-5
- **Max context length**: 15,000 tokens

### Network Configuration

Stable multi-node communication:
- **Backend**: GLOO (more stable than NCCL for multi-node)
- **Network interface**: eth0 (Ethernet)
- **InfiniBand**: Disabled for stability
- **Timeouts**: Extended for long sequences

## Features

### Automatic Node Management
- **Node detection**: Automatically identifies master and worker nodes
- **Rank assignment**: Uses SLURM environment variables for node ranks
- **Coordination**: Built-in synchronization between nodes

### Enhanced Error Handling
- **Pre-flight checks**: Validates environment, data, and GPU availability
- **Network testing**: Tests connectivity between nodes
- **Graceful failure**: Comprehensive error logging and cleanup

### Monitoring and Logging
- **Timestamped logs**: All output includes timestamps and node information
- **Separate streams**: STDOUT and STDERR logs for each job
- **Job completion tracking**: Automatic success/failure reporting

### GPU Assignment Fixes
- **Device mapping**: Correct GPU assignment for each process
- **Index validation**: Prevents GPU indexing errors
- **Memory optimization**: CUDA memory management for long sequences

## Output Structure

### Log Files
```
gpt2_diffusion_2node_optimized_15k_head1000_<job_id>.out  # STDOUT
gpt2_diffusion_2node_optimized_15k_head1000_<job_id>.err  # STDERR
```

### Training Output
```
/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_restructured_head1000/training_runs_2node/
├── <timestamp>_GPT2_Diffusion_gpt2/
│   ├── config.json
│   ├── model.safetensors
│   ├── tokenizer.json
│   ├── best_model/           # Best validation checkpoint
│   ├── checkpoint-*/         # Regular checkpoints
│   ├── trainer_state.json
│   └── job_completion.txt    # Job metadata
```

## Troubleshooting

### Common Issues

1. **Job Pending**
   ```bash
   # Check queue status
   squeue -u $USER
   
   # Check partition availability
   sinfo -p ghx4-inte
   ```

2. **Node Communication Errors**
   - Check network connectivity between allocated nodes
   - Verify firewall settings allow communication on port 12355
   - Review NCCL debug output in logs

3. **GPU Memory Errors**
   - Reduce batch size in the script
   - Check GPU memory usage: `nvidia-smi`
   - Verify CUDA_VISIBLE_DEVICES setting

4. **Data Loading Issues**
   - Ensure data directory exists on all nodes
   - Check file permissions and accessibility
   - Verify network storage connectivity

### Debug Commands

```bash
# Check allocated nodes
scontrol show job <job_id>

# Test node connectivity
ssh <node_name> "hostname && nvidia-smi"

# Monitor GPU usage
watch -n 1 nvidia-smi

# Follow logs in real-time
tail -f gpt2_diffusion_2node_optimized_15k_head1000_<job_id>.out
```

## Performance Expectations

### Scaling Benefits
- **8 GPUs vs 4 GPUs**: ~2x speedup
- **8 GPUs vs 1 GPU**: ~6-7x speedup (due to communication overhead)
- **Memory**: Can handle longer sequences with distributed memory

### Typical Metrics
- **Training speed**: ~2-3 steps/minute for 15k token sequences
- **Memory usage**: ~20-30GB per GPU for batch_size=1
- **Network bandwidth**: ~1-10 GB/s between nodes during training

## Best Practices

1. **Resource Planning**
   - Submit jobs during off-peak hours
   - Use appropriate time limits
   - Monitor queue status before submission

2. **Data Management**
   - Use fast storage for training data
   - Pre-validate data before job submission
   - Consider data locality for multi-node jobs

3. **Monitoring**
   - Check logs regularly during training
   - Monitor GPU utilization across all nodes
   - Watch for network communication issues

4. **Checkpointing**
   - Regular checkpoints every 10 steps
   - Best model saving enabled
   - Automatic cleanup of old checkpoints

## Advanced Usage

### Custom Parameters

Edit the script to modify training parameters:
```bash
# In submit_diffusion_training_2node.sh
BATCH_SIZE=2                    # Increase if you have more GPU memory
GRADIENT_ACCUMULATION_STEPS=1   # Adjust based on effective batch size needs
MAX_CONTEXT_LENGTH=10000        # Reduce for faster training
```

### Different Data Sets

Change the data directory:
```bash
DATA_DIR="./data/your_custom_dataset"
```

### Extended Training

Modify time limits and epochs:
```bash
#SBATCH --time=72:00:00         # 72 hours
NUM_EPOCHS=1000                 # More epochs
```

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the log files for specific error messages
3. Test with the manual training scripts first
4. Consult the SLURM documentation for cluster-specific issues
