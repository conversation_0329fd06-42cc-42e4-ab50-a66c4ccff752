# Delta AI vs Manual Two-Node Training Comparison

This document compares the different approaches for two-node distributed training.

## Available Scripts

### 1. `scripts/submit_diffusion_training_2node_delta.sh` (Recommended)
**Delta AI Best Practices Implementation**

**Key Features:**
- Uses `srun torchrun` for proper SLURM coordination
- Implements Delta AI recommended rendezvous setup
- Uses `rdzv_endpoint` instead of `master_addr/master_port`
- Optimized for Delta AI infrastructure

**Network Configuration:**
```bash
export NCCL_SOCKET_IFNAME=hsn          # Delta AI high-speed network
export NCCL_IB_DISABLE=0               # Enable InfiniBand
export NCCL_NET_GDR_LEVEL=2            # Enable GPU Direct RDMA
```

**Execution Pattern:**
```bash
srun torchrun \
    --nnodes ${SLURM_NNODES} \
    --nproc_per_node ${SLURM_GPUS_PER_NODE} \
    --rdzv_id $RANDOM \
    --rdzv_backend c10d \
    --rdzv_endpoint="$head_node_ip:29500" \
    scripts/train_gpt2_diffusion.py
```

### 2. `scripts/submit_diffusion_training_2node_working.sh`
**Manual Multi-Node Approach**

**Key Features:**
- Manual node rank assignment using `SLURM_PROCID`
- Direct torchrun calls on each node
- Separate output files per node
- Manual synchronization logic

**Network Configuration:**
```bash
export NCCL_SOCKET_IFNAME=eth0         # Standard Ethernet
export NCCL_IB_DISABLE=1               # Disable InfiniBand
export NCCL_P2P_DISABLE=1              # Disable P2P
```

**Execution Pattern:**
```bash
torchrun \
    --nproc_per_node=4 \
    --nnodes=2 \
    --node_rank=$NODE_RANK \
    --master_addr=$MASTER_ADDR \
    --master_port=$MASTER_PORT \
    scripts/train_gpt2_diffusion.py
```

### 3. `scripts/submit_diffusion_training_2node_simple.sh`
**Simplified Manual Approach**

**Key Features:**
- Basic two-node setup
- Single output stream
- Minimal coordination logic

## Comparison Matrix

| Feature | Delta AI | Working | Simple |
|---------|----------|---------|---------|
| **SLURM Integration** | ✅ Full srun | ⚠️ Manual | ⚠️ Manual |
| **Rendezvous** | ✅ c10d endpoint | ❌ master/port | ❌ master/port |
| **Network Optimization** | ✅ InfiniBand/HSN | ⚠️ Ethernet only | ⚠️ Ethernet only |
| **Error Handling** | ✅ Comprehensive | ✅ Good | ⚠️ Basic |
| **Logging** | ✅ Unified | ✅ Per-node | ⚠️ Single stream |
| **Stability** | ✅ High | ✅ Good | ⚠️ Variable |
| **Performance** | ✅ Optimized | ✅ Good | ⚠️ Standard |

## Recommendations

### For Production Use
**Use `scripts/submit_diffusion_training_2node_delta.sh`**

**Advantages:**
- Follows NCSA Delta AI best practices
- Better performance with InfiniBand/HSN
- More robust rendezvous mechanism
- Automatic SLURM coordination
- Future-proof approach

### For Debugging
**Use `scripts/submit_diffusion_training_2node_working.sh`**

**Advantages:**
- Separate log files per node
- Manual control over coordination
- Easier to debug communication issues
- Fallback to Ethernet if InfiniBand issues

### For Quick Testing
**Use `scripts/submit_diffusion_training_2node_simple.sh`**

**Advantages:**
- Minimal setup
- Quick to modify
- Good for initial testing

## Migration Path

If you're currently using the manual approaches and want to migrate to Delta AI:

1. **Test with Delta AI script first:**
   ```bash
   sbatch scripts/submit_diffusion_training_2node_delta.sh
   ```

2. **If InfiniBand issues occur, fallback to working script:**
   ```bash
   sbatch scripts/submit_diffusion_training_2node_working.sh
   ```

3. **Compare performance and stability**

## Performance Expectations

### Delta AI Script
- **Best performance** due to InfiniBand/HSN
- **Lower latency** between nodes
- **Higher bandwidth** for gradient synchronization
- **Better scaling** for larger models

### Manual Scripts
- **Good performance** with Ethernet
- **Higher latency** but more stable
- **Broader compatibility** across different clusters
- **Easier debugging** of network issues

## Troubleshooting

### Delta AI Script Issues
1. **InfiniBand not available**: Fallback to working script
2. **HSN interface issues**: Check `ip link show` for available interfaces
3. **Rendezvous timeout**: Check head node IP detection

### Manual Script Issues
1. **Node coordination failure**: Check SLURM task assignment
2. **Network timeout**: Verify port availability
3. **GPU indexing errors**: Check CUDA_VISIBLE_DEVICES

## Usage Examples

### Submit Delta AI Job
```bash
# Recommended approach
sbatch scripts/submit_diffusion_training_2node_delta.sh

# Monitor
./scripts/submit_and_monitor_2node.sh status
./scripts/submit_and_monitor_2node.sh logs <job_id>
```

### Submit Working Job (Fallback)
```bash
# If Delta AI approach has issues
sbatch scripts/submit_diffusion_training_2node_working.sh

# Monitor (separate logs per node)
tail -f gpt2_diffusion_2node_working_<job_id>_0.out  # Master
tail -f gpt2_diffusion_2node_working_<job_id>_1.out  # Worker
```

## Conclusion

The Delta AI script (`submit_diffusion_training_2node_delta.sh`) is the recommended approach for production training, offering the best performance and following established best practices. The manual scripts serve as reliable fallbacks and debugging tools.
