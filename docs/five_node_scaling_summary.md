# Five-Node Distributed Training Scaling Summary

This document summarizes the scaling from 2-node to 5-node distributed training for the GPT-2 diffusion model.

## Overview

The script `scripts/submit_diffusion_training_5node_delta.sh` scales our proven Delta AI approach from 2 nodes (8 GPUs) to 5 nodes (20 GPUs), maintaining all optimizations while adjusting parameters for the larger scale.

## Key Changes Made

### 1. SLURM Resource Allocation
```bash
# Before (2-node)
#SBATCH --nodes=2
#SBATCH --job-name=gpt2_diffusion_2node_delta

# After (5-node)
#SBATCH --nodes=5
#SBATCH --job-name=gpt2_diffusion_5node_delta
```

### 2. Training Parameters Adjustment

| Parameter | 2-Node (8 GPUs) | 5-Node (20 GPUs) | Reasoning |
|-----------|------------------|-------------------|-----------|
| **Per-device batch size** | 1 | 1 | Unchanged (limited by 15k sequence length) |
| **Gradient accumulation** | 2 | 2 | Unchanged (good balance) |
| **Effective batch size** | 16 | 40 | Scales with GPU count (1×8×2 → 1×20×2) |
| **Learning rate** | 2e-5 | 3e-5 | Increased 50% for larger batch size |
| **Save/logging steps** | 10/5 | 10/5 | Unchanged (appropriate frequency) |

### 3. Output Directory Structure
```bash
# Before
/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_restructured_head1000/training_runs_2node_delta/

# After  
/work/hdd/bdrx/dzhang5/Diffusion_via_Reasoning_restructured_head1000/training_runs_5node_delta/
```

### 4. Validation Logic
```bash
# Before
if [ "$SLURM_NNODES" -ne 2 ]; then
    log "ERROR: Expected 2 nodes, got $SLURM_NNODES"

# After
if [ "$SLURM_NNODES" -ne 5 ]; then
    log "ERROR: Expected 5 nodes, got $SLURM_NNODES"
```

### 5. Logging and Documentation Updates
- All log messages updated from "two-node" to "five-node"
- W&B project name: `Diffusion_via_Reasoning_5Node_Delta`
- Error log naming: `training_error_5node_delta_*`
- Job completion metadata includes 5-node specific information

## Performance Expectations

### Scaling Benefits
| Metric | 2-Node (8 GPUs) | 5-Node (20 GPUs) | Expected Improvement |
|--------|------------------|-------------------|---------------------|
| **Training Speed** | Baseline | ~2.5x faster | Linear scaling with communication overhead |
| **Effective Batch Size** | 16 | 40 | 2.5x larger batches |
| **Memory Capacity** | 8 × 120GB = 960GB | 20 × 120GB = 2.4TB | 2.5x more GPU memory |
| **Convergence** | Baseline | Potentially faster | Larger batches may improve convergence |

### Resource Utilization
- **Total GPUs**: 20 NVIDIA GH200 120GB
- **Total GPU Memory**: 2.4TB
- **Total CPU Cores**: 240 (48 per node)
- **Total System Memory**: 640GB (128GB per node)

## Network and Communication

### Delta AI Optimizations Maintained
```bash
export NCCL_SOCKET_IFNAME=hsn          # High-speed network
export NCCL_IB_DISABLE=0               # Enable InfiniBand
export NCCL_NET_GDR_LEVEL=2            # Enable GPU Direct RDMA
```

### Rendezvous Configuration
- **Endpoint**: Automatically detected head node IP
- **Backend**: c10d (scales well to 5 nodes)
- **Port**: 29500 (standard)
- **ID**: Random per job (avoids conflicts)

## Usage Instructions

### Submit 5-Node Job
```bash
# Direct submission
sbatch scripts/submit_diffusion_training_5node_delta.sh

# Using helper script (auto-detects 5-node version)
./scripts/submit_and_monitor_2node.sh submit
```

### Monitor Progress
```bash
# Check status
./scripts/submit_and_monitor_2node.sh status

# View logs
./scripts/submit_and_monitor_2node.sh logs <job_id>

# Real-time monitoring
tail -f gpt2_diffusion_5node_delta_<job_id>.out
```

## Learning Rate Adjustment Rationale

The learning rate was increased from `2e-5` to `3e-5` (50% increase) based on:

1. **Linear Scaling Rule**: For batch size increases, learning rate often scales proportionally
2. **Effective Batch Size**: Increased from 16 to 40 (2.5x)
3. **Conservative Approach**: 50% increase is more conservative than full linear scaling
4. **Long Sequence Stability**: Maintains stability for 15k token sequences

## Potential Challenges and Mitigations

### 1. Communication Overhead
**Challenge**: More nodes = more inter-node communication
**Mitigation**: 
- Delta AI InfiniBand/HSN optimization
- Efficient gradient accumulation
- NCCL optimizations

### 2. Resource Availability
**Challenge**: 5 nodes may have longer queue times
**Mitigation**:
- Submit during off-peak hours
- Consider 2-node fallback if needed
- Monitor partition availability

### 3. Fault Tolerance
**Challenge**: More nodes = higher probability of node failure
**Mitigation**:
- Regular checkpointing (every 10 steps)
- Best model saving
- Comprehensive error logging

### 4. Memory Scaling
**Challenge**: Model replication across 20 GPUs
**Mitigation**:
- Gradient checkpointing enabled
- Memory optimization settings
- Per-device batch size = 1

## Validation Checklist

Before running 5-node training:

- [ ] Verify 5 nodes are available in partition
- [ ] Check data accessibility from all nodes
- [ ] Confirm output directory permissions
- [ ] Test head node IP detection
- [ ] Validate NCCL/InfiniBand configuration

## Fallback Strategy

If 5-node training encounters issues:

1. **Check logs** for specific error patterns
2. **Reduce to 2-node** using existing proven script
3. **Test network connectivity** between nodes
4. **Verify resource allocation** in SLURM

## Expected Training Timeline

With 5 nodes (20 GPUs):
- **Setup time**: ~5-10 minutes (environment, data loading)
- **Training speed**: ~2.5x faster than 2-node
- **Convergence**: Potentially faster due to larger effective batch size
- **Total time**: Significantly reduced for same number of epochs

## Monitoring Key Metrics

Watch for:
- **GPU utilization**: Should be high across all 20 GPUs
- **Communication efficiency**: Monitor NCCL debug output
- **Memory usage**: Should be consistent across nodes
- **Training loss**: Should converge faster with larger batches
- **Validation metrics**: Monitor for overfitting with larger batches

## Success Criteria

The 5-node training is successful if:
- All 20 GPUs are utilized effectively
- Training speed is 2-3x faster than 2-node
- Model convergence is maintained or improved
- No significant communication bottlenecks
- Checkpoints and best models are saved correctly
