#!/usr/bin/env python3
"""
Test state consistency in training data.
"""

import json
import sys
from pathlib import Path

def parse_action_string(action_str):
    """Parse an action string into components."""
    if action_str.startswith("add "):
        parts = action_str.split()
        token = parts[1]
        position = int(parts[4])
        return "add", position, token, None
    elif action_str.startswith("remove "):
        parts = action_str.split()
        token = parts[1]
        position = int(parts[4])
        return "remove", position, token, None
    elif action_str.startswith("replace "):
        parts = action_str.split()
        old_token = parts[1]
        position = int(parts[4])
        new_token = parts[6]
        return "replace", position, new_token, old_token
    else:
        return None, None, None, None

def apply_action_string(current_state, action_str):
    """Apply an action string to the current state."""
    action, position, token, old_token = parse_action_string(action_str)
    
    if action == "add":
        return current_state[:position] + token + current_state[position:]
    elif action == "remove":
        if position < len(current_state):
            return current_state[:position] + current_state[position + 1:]
        else:
            return current_state
    elif action == "replace":
        if position < len(current_state):
            return current_state[:position] + token + current_state[position + 1:]
        else:
            return current_state
    else:
        return current_state

def test_example_consistency(example):
    """Test consistency of a single example."""
    print(f"Testing: {example['protein_name'][:50]}...")
    
    # Extract actions and state updates
    actions = []
    state_updates = {}
    
    for line in example['reverse_process']:
        if line.startswith("Current state after step "):
            parts = line.split(": ")
            step_info = parts[0]
            state = parts[1].strip("'")
            step_num = int(step_info.split()[-1])
            state_updates[step_num] = state
        elif not line.startswith("Final state"):
            actions.append(line)
    
    # Apply actions manually
    current_state = ""
    mismatches = 0
    
    for i, action_str in enumerate(actions):
        step_num = i + 1
        new_state = apply_action_string(current_state, action_str)
        current_state = new_state
        
        # Check recorded states
        if step_num in state_updates:
            recorded_state = state_updates[step_num]
            if current_state != recorded_state:
                print(f"  ❌ Mismatch at step {step_num}")
                print(f"    Action: {action_str}")
                print(f"    Manual:   '{current_state[:50]}{'...' if len(current_state) > 50 else ''}'")
                print(f"    Recorded: '{recorded_state[:50]}{'...' if len(recorded_state) > 50 else ''}'")
                mismatches += 1
                if mismatches >= 3:  # Stop after 3 mismatches
                    break
    
    # Check final state
    final_match = current_state == example['final_state']
    
    print(f"  Final state match: {final_match}")
    print(f"  Intermediate mismatches: {mismatches}")
    
    return mismatches == 0 and final_match

def main():
    """Test all examples in validation data."""
    print("Testing State Consistency in Training Data")
    print("="*50)
    
    # Test the validation data
    data_file = "./data/CreateProtein_head1000_new/val_data.json"
    
    if not Path(data_file).exists():
        print(f"Data file {data_file} not found")
        
        # Try the guaranteed convergence data instead
        data_file = "./data/test_guaranteed_convergence/val_data.json"
        if not Path(data_file).exists():
            print(f"Alternative data file {data_file} also not found")
            return
        else:
            print(f"Using alternative data file: {data_file}")
    
    try:
        with open(data_file, 'r') as f:
            examples = json.load(f)
    except Exception as e:
        print(f"Error reading data file: {e}")
        return
    
    if not examples:
        print("No examples found in data file")
        return
    
    print(f"Found {len(examples)} examples to test")
    
    all_consistent = True
    for i, example in enumerate(examples):
        print(f"\nExample {i+1}/{len(examples)}:")
        consistent = test_example_consistency(example)
        if not consistent:
            all_consistent = False
    
    print(f"\n{'='*50}")
    if all_consistent:
        print("✅ All examples have consistent state tracking!")
    else:
        print("❌ Some examples have inconsistent state tracking!")
    
    return all_consistent

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
