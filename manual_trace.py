#!/usr/bin/env python3
"""
Manually trace through the first example to find the bug.
"""

def apply_action(current_state, action_str):
    """Apply an action string to the current state."""
    if action_str.startswith("add "):
        # Format: "add X at position Y"
        parts = action_str.split()
        token = parts[1]
        position = int(parts[4])
        return current_state[:position] + token + current_state[position:]
    elif action_str.startswith("remove "):
        # Format: "remove X from position Y"
        parts = action_str.split()
        token = parts[1]
        position = int(parts[4])
        if position < len(current_state):
            return current_state[:position] + current_state[position + 1:]
        else:
            return current_state
    elif action_str.startswith("replace "):
        # Format: "replace X at position Y with Z"
        parts = action_str.split()
        old_token = parts[1]
        position = int(parts[4])
        new_token = parts[6]
        if position < len(current_state):
            return current_state[:position] + new_token + current_state[position + 1:]
        else:
            return current_state
    else:
        return current_state


def main():
    """Manually trace the first example."""
    
    # Actions from the first example
    actions = [
        "add T at position 0",
        "remove T from position 0", 
        "add Z at position 0",
        "replace Z at position 0 with K",
        "remove K from position 0",
        "add M at position 0",
        "replace M at position 0 with C",
        "remove C from position 0",
        "add E at position 0",
        "replace E at position 0 with N",  # Step 10
        "remove N from position 0",
        "add Q at position 0",
        "remove Q from position 0",
        "add Y at position 0",
        "replace Y at position 0 with F",
        "remove F from position 0",
        "add G at position 0",
        "replace G at position 0 with N",
        "remove N from position 0",
        "add V at position 0",  # Step 20
        "replace V at position 0 with K",
        "remove K from position 0",
        "add T at position 0",
        "remove T from position 0",
        "add E at position 0",
        "replace E at position 0 with Y",
        "remove Y from position 0",
        "add A at position 0",
        "replace A at position 0 with R",
        "remove R from position 0",  # Step 30
    ]
    
    # Expected states from the data
    expected_states = {
        10: 'N',
        20: 'V', 
        30: ''
    }
    
    current_state = ""
    print("Manual trace of first 30 steps:")
    print(f"Initial state: '{current_state}'")
    
    for i, action in enumerate(actions):
        step_num = i + 1
        new_state = apply_action(current_state, action)
        
        print(f"Step {step_num}: {action}")
        print(f"  '{current_state}' -> '{new_state}'")
        
        current_state = new_state
        
        # Check against expected states
        if step_num in expected_states:
            expected = expected_states[step_num]
            match = current_state == expected
            print(f"  Expected after step {step_num}: '{expected}'")
            print(f"  Match: {match}")
            if not match:
                print(f"  ❌ MISMATCH!")
                return False
            else:
                print(f"  ✅ Correct")
        
        print()
    
    print("✅ All checked states match!")
    return True


if __name__ == "__main__":
    main()
