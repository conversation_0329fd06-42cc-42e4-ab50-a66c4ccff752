log,state
initialize: CCc1ccc(-c2nc(N)ccc2[N+](=O)[O-])cc1,CCc1ccc(-c2nc(N)ccc2[N+](=O)[O-])cc1
replace [ at position 28 with +,CCc1ccc(-c2nc(N)ccc2[N+](=O)+O-])cc1
add O at position 2,CCOc1ccc(-c2nc(N)ccc2[N+](=O)+O-])cc1
remove c from position 6,CCOc1cc(-c2nc(N)ccc2[N+](=O)+O-])cc1
replace ) at position 15 with #,CCOc1cc(-c2nc(N#ccc2[N+](=O)+O-])cc1
add 6 at position 1,C6COc1cc(-c2nc(N#ccc2[N+](=O)+O-])cc1
remove O from position 30,C6COc1cc(-c2nc(N#ccc2[N+](=O)+-])cc1
remove - from position 9,C6COc1cc(c2nc(N#ccc2[N+](=O)+-])cc1
add n at position 7,C6COc1cnc(c2nc(N#ccc2[N+](=O)+-])cc1
add o at position 31,C6COc1cnc(c2nc(N#ccc2[N+](=O)+-o])cc1
replace c at position 4 with O,C6COO1cnc(c2nc(N#ccc2[N+](=O)+-o])cc1
remove + from position 29,C6COO1cnc(c2nc(N#ccc2[N+](=O)-o])cc1
add C at position 5,C6COOC1cnc(c2nc(N#ccc2[N+](=O)-o])cc1
remove # from position 17,C6COOC1cnc(c2nc(Nccc2[N+](=O)-o])cc1
replace O at position 4 with N,C6CONC1cnc(c2nc(Nccc2[N+](=O)-o])cc1
remove N from position 22,C6CONC1cnc(c2nc(Nccc2[+](=O)-o])cc1
remove 2 from position 12,C6CONC1cnc(cnc(Nccc2[+](=O)-o])cc1
remove 1 from position 33,C6CONC1cnc(cnc(Nccc2[+](=O)-o])cc
remove c from position 32,C6CONC1cnc(cnc(Nccc2[+](=O)-o])c
add - at position 21,C6CONC1cnc(cnc(Nccc2[-+](=O)-o])c
remove ( from position 24,C6CONC1cnc(cnc(Nccc2[-+]=O)-o])c
add H at position 4,C6COHNC1cnc(cnc(Nccc2[-+]=O)-o])c
remove H from position 4,C6CONC1cnc(cnc(Nccc2[-+]=O)-o])c
remove c from position 9,C6CONC1cn(cnc(Nccc2[-+]=O)-o])c
add ] at position 22,C6CONC1cn(cnc(Nccc2[-+]]=O)-o])c
add O at position 9,C6CONC1cnO(cnc(Nccc2[-+]]=O)-o])c
remove C from position 2,C6ONC1cnO(cnc(Nccc2[-+]]=O)-o])c
remove O from position 2,C6NC1cnO(cnc(Nccc2[-+]]=O)-o])c
add 4 at position 9,C6NC1cnO(4cnc(Nccc2[-+]]=O)-o])c
replace 4 at position 9 with c,C6NC1cnO(ccnc(Nccc2[-+]]=O)-o])c
replace N at position 2 with H,C6HC1cnO(ccnc(Nccc2[-+]]=O)-o])c
remove ] from position 22,C6HC1cnO(ccnc(Nccc2[-+]=O)-o])c
remove 2 from position 18,C6HC1cnO(ccnc(Nccc[-+]=O)-o])c
replace c at position 15 with (,C6HC1cnO(ccnc(N(cc[-+]=O)-o])c
replace ( at position 15 with o,C6HC1cnO(ccnc(Nocc[-+]=O)-o])c
replace ) at position 28 with 1,C6HC1cnO(ccnc(Nocc[-+]=O)-o]1c
remove ) from position 24,C6HC1cnO(ccnc(Nocc[-+]=O-o]1c
remove N from position 14,C6HC1cnO(ccnc(occ[-+]=O-o]1c
remove C from position 3,C6H1cnO(ccnc(occ[-+]=O-o]1c
replace o at position 13 with =,C6H1cnO(ccnc(=cc[-+]=O-o]1c
replace [ at position 16 with +,C6H1cnO(ccnc(=cc+-+]=O-o]1c
replace c at position 15 with 7,C6H1cnO(ccnc(=c7+-+]=O-o]1c
replace O at position 21 with 3,C6H1cnO(ccnc(=c7+-+]=3-o]1c
remove c from position 11,C6H1cnO(ccn(=c7+-+]=3-o]1c
add c at position 22,C6H1cnO(ccn(=c7+-+]=3-co]1c
replace ] at position 18 with s,C6H1cnO(ccn(=c7+-+s=3-co]1c
replace c at position 4 with o,C6H1onO(ccn(=c7+-+s=3-co]1c
replace o at position 23 with 5,C6H1onO(ccn(=c7+-+s=3-c5]1c
replace 7 at position 14 with ],C6H1onO(ccn(=c]+-+s=3-c5]1c
add 1 at position 12,C6H1onO(ccn(1=c]+-+s=3-c5]1c
add c at position 9,C6H1onO(cccn(1=c]+-+s=3-c5]1c
add l at position 16,C6H1onO(cccn(1=cl]+-+s=3-c5]1c
add s at position 29,C6H1onO(cccn(1=cl]+-+s=3-c5]1sc
remove c from position 10,C6H1onO(ccn(1=cl]+-+s=3-c5]1sc
remove c from position 14,C6H1onO(ccn(1=l]+-+s=3-c5]1sc
add n at position 8,C6H1onO(nccn(1=l]+-+s=3-c5]1sc
add s at position 13,C6H1onO(nccn(s1=l]+-+s=3-c5]1sc
add 1 at position 25,C6H1onO(nccn(s1=l]+-+s=3-1c5]1sc
remove 1 from position 29,C6H1onO(nccn(s1=l]+-+s=3-1c5]sc
remove c from position 26,C6H1onO(nccn(s1=l]+-+s=3-15]sc
replace c at position 29 with o,C6H1onO(nccn(s1=l]+-+s=3-15]so
add S at position 28,C6H1onO(nccn(s1=l]+-+s=3-15]Sso
remove o from position 30,C6H1onO(nccn(s1=l]+-+s=3-15]Ss
remove c from position 9,C6H1onO(ncn(s1=l]+-+s=3-15]Ss
add F at position 11,C6H1onO(ncnF(s1=l]+-+s=3-15]Ss
add ] at position 29,C6H1onO(ncnF(s1=l]+-+s=3-15]S]s
replace = at position 15 with 5,C6H1onO(ncnF(s15l]+-+s=3-15]S]s
add I at position 3,C6HI1onO(ncnF(s15l]+-+s=3-15]S]s
add l at position 9,C6HI1onO(lncnF(s15l]+-+s=3-15]S]s
remove + from position 20,C6HI1onO(lncnF(s15l]-+s=3-15]S]s
add N at position 3,C6HNI1onO(lncnF(s15l]-+s=3-15]S]s
add r at position 9,C6HNI1onOr(lncnF(s15l]-+s=3-15]S]s
add 7 at position 27,C6HNI1onOr(lncnF(s15l]-+s=37-15]S]s
remove c from position 13,C6HNI1onOr(lnnF(s15l]-+s=37-15]S]s
replace = at position 24 with s,C6HNI1onOr(lnnF(s15l]-+ss37-15]S]s
replace 1 at position 17 with H,C6HNI1onOr(lnnF(sH5l]-+ss37-15]S]s
remove l from position 19,C6HNI1onOr(lnnF(sH5]-+ss37-15]S]s
remove r from position 9,C6HNI1onO(lnnF(sH5]-+ss37-15]S]s
replace 1 at position 5 with c,C6HNIconO(lnnF(sH5]-+ss37-15]S]s
remove 1 from position 26,C6HNIconO(lnnF(sH5]-+ss37-5]S]s
remove ] from position 18,C6HNIconO(lnnF(sH5-+ss37-5]S]s
remove N from position 3,C6HIconO(lnnF(sH5-+ss37-5]S]s
remove ( from position 8,C6HIconOlnnF(sH5-+ss37-5]S]s
add l at position 22,C6HIconOlnnF(sH5-+ss37l-5]S]s
remove 6 from position 1,CHIconOlnnF(sH5-+ss37l-5]S]s
remove ] from position 26,CHIconOlnnF(sH5-+ss37l-5]Ss
remove ] from position 24,CHIconOlnnF(sH5-+ss37l-5Ss
remove s from position 12,CHIconOlnnF(H5-+ss37l-5Ss
remove I from position 2,CHconOlnnF(H5-+ss37l-5Ss
remove F from position 9,CHconOlnn(H5-+ss37l-5Ss
remove S from position 21,CHconOlnn(H5-+ss37l-5s
replace O at position 5 with 7,CHcon7lnn(H5-+ss37l-5s
remove 7 from position 5,CHconlnn(H5-+ss37l-5s
remove - from position 11,CHconlnn(H5+ss37l-5s
remove + from position 11,CHconlnn(H5ss37l-5s
add r at position 16,CHconlnn(H5ss37lr-5s
remove l from position 15,CHconlnn(H5ss37r-5s
remove 7 from position 14,CHconlnn(H5ss3r-5s
remove o from position 3,CHcnlnn(H5ss3r-5s
add ) at position 15,CHcnlnn(H5ss3r-)5s
replace s at position 10 with 6,CHcnlnn(H56s3r-)5s
replace 5 at position 9 with 4,CHcnlnn(H46s3r-)5s
remove 6 from position 10,CHcnlnn(H4s3r-)5s
remove 4 from position 9,CHcnlnn(Hs3r-)5s
remove H from position 8,CHcnlnn(s3r-)5s
add 5 at position 6,CHcnln5n(s3r-)5s
remove 3 from position 10,CHcnln5n(sr-)5s
remove H from position 1,Ccnln5n(sr-)5s
replace r at position 9 with H,Ccnln5n(sH-)5s
remove s from position 13,Ccnln5n(sH-)5
add - at position 5,Ccnln-5n(sH-)5
replace 5 at position 6 with (,Ccnln-(n(sH-)5
remove C from position 0,cnln-(n(sH-)5
replace - at position 10 with /,cnln-(n(sH/)5
replace s at position 8 with 6,cnln-(n(6H/)5
remove 6 from position 8,cnln-(n(H/)5
replace - at position 4 with 4,cnln4(n(H/)5
remove / from position 9,cnln4(n(H)5
add ) at position 8,cnln4(n()H)5
remove c from position 0,nln4(n()H)5
remove n from position 5,nln4(()H)5
replace 4 at position 3 with /,nln/(()H)5
remove n from position 2,nl/(()H)5
replace l at position 1 with -,n-/(()H)5
remove ( from position 3,n-/()H)5
remove ) from position 6,n-/()H5
replace / at position 2 with -,n--()H5
remove ) from position 4,n--(H5
remove H from position 4,n--(5
remove ( from position 3,n--5
add r at position 0,rn--5
remove - from position 3,rn-5
replace r at position 0 with 5,5n-5
add 6 at position 1,56n-5
replace 5 at position 4 with [,56n-[
replace [ at position 4 with B,56n-B
replace 5 at position 0 with B,B6n-B
replace n at position 2 with =,B6=-B
replace 6 at position 1 with C,BC=-B
remove C from position 1,B=-B
remove B from position 0,=-B
replace - at position 1 with I,=IB
remove = from position 0,IB
add 1 at position 0,1IB
replace B at position 2 with N,1IN
remove 1 from position 0,IN
remove I from position 0,N
remove N from position 0,
final: ,
