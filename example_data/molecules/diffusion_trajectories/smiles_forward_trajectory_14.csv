log,state
initialize: O=C1c2ccccc2N[C@H](CSC2=NC=NC3=NC=N[C@@H]32)N1c1ccc(Cl)cc1,O=C1c2ccccc2N[C@H](CSC2=NC=NC3=NC=N[C@@H]32)N1c1ccc(Cl)cc1
replace C at position 28 with +,O=C1c2ccccc2N[C@H](CSC2=NC=N+3=NC=N[C@@H]32)N1c1ccc(Cl)cc1
add s at position 58,O=C1c2ccccc2N[C@H](CSC2=NC=N+3=NC=N[C@@H]32)N1c1ccc(Cl)cc1s
replace @ at position 37 with (,O=C1c2ccccc2N[C@H](CSC2=NC=N+3=NC=N[C(@H]32)N1c1ccc(Cl)cc1s
remove 2 from position 5,O=C1cccccc2N[C@H](CSC2=NC=N+3=NC=N[C(@H]32)N1c1ccc(Cl)cc1s
add 2 at position 22,O=C1cccccc2N[C@H](CSC22=NC=N+3=NC=N[C(@H]32)N1c1ccc(Cl)cc1s
add # at position 50,O=C1cccccc2N[C@H](CSC22=NC=N+3=NC=N[C(@H]32)N1c1cc#c(Cl)cc1s
remove H from position 39,O=C1cccccc2N[C@H](CSC22=NC=N+3=NC=N[C(@]32)N1c1cc#c(Cl)cc1s
remove 3 from position 29,O=C1cccccc2N[C@H](CSC22=NC=N+=NC=N[C(@]32)N1c1cc#c(Cl)cc1s
add S at position 11,O=C1cccccc2SN[C@H](CSC22=NC=N+=NC=N[C(@]32)N1c1cc#c(Cl)cc1s
add C at position 0,CO=C1cccccc2SN[C@H](CSC22=NC=N+=NC=N[C(@]32)N1c1cc#c(Cl)cc1s
remove C from position 15,CO=C1cccccc2SN[@H](CSC22=NC=N+=NC=N[C(@]32)N1c1cc#c(Cl)cc1s
add r at position 34,CO=C1cccccc2SN[@H](CSC22=NC=N+=NC=rN[C(@]32)N1c1cc#c(Cl)cc1s
remove ( from position 38,CO=C1cccccc2SN[@H](CSC22=NC=N+=NC=rN[C@]32)N1c1cc#c(Cl)cc1s
remove C from position 32,CO=C1cccccc2SN[@H](CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)cc1s
remove ] from position 17,CO=C1cccccc2SN[@H(CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)cc1s
replace 1 at position 4 with O,CO=COcccccc2SN[@H(CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)cc1s
remove c from position 54,CO=COcccccc2SN[@H(CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)c1s
remove @ from position 15,CO=COcccccc2SN[H(CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)c1s
remove S from position 12,CO=COcccccc2N[H(CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)c1s
remove C from position 33,CO=COcccccc2N[H(CSC22=NC=N+=N=rN[@]32)N1c1cc#c(Cl)c1s
remove [ from position 32,CO=COcccccc2N[H(CSC22=NC=N+=N=rN@]32)N1c1cc#c(Cl)c1s
add - at position 21,CO=COcccccc2N[H(CSC22-=NC=N+=N=rN@]32)N1c1cc#c(Cl)c1s
remove c from position 42,CO=COcccccc2N[H(CSC22-=NC=N+=N=rN@]32)N1c1c#c(Cl)c1s
remove C from position 3,CO=Occcccc2N[H(CSC22-=NC=N+=N=rN@]32)N1c1c#c(Cl)c1s
add / at position 31,CO=Occcccc2N[H(CSC22-=NC=N+=N=r/N@]32)N1c1c#c(Cl)c1s
add - at position 13,CO=Occcccc2N[-H(CSC22-=NC=N+=N=r/N@]32)N1c1c#c(Cl)c1s
add ] at position 22,CO=Occcccc2N[-H(CSC22-]=NC=N+=N=r/N@]32)N1c1c#c(Cl)c1s
add - at position 33,CO=Occcccc2N[-H(CSC22-]=NC=N+=N=r-/N@]32)N1c1c#c(Cl)c1s
remove = from position 29,CO=Occcccc2N[-H(CSC22-]=NC=N+N=r-/N@]32)N1c1c#c(Cl)c1s
add O at position 17,CO=Occcccc2N[-H(COSC22-]=NC=N+N=r-/N@]32)N1c1c#c(Cl)c1s
replace N at position 11 with 3,CO=Occcccc23[-H(COSC22-]=NC=N+N=r-/N@]32)N1c1c#c(Cl)c1s
add - at position 54,CO=Occcccc23[-H(COSC22-]=NC=N+N=r-/N@]32)N1c1c#c(Cl)c1-s
replace + at position 29 with (,CO=Occcccc23[-H(COSC22-]=NC=N(N=r-/N@]32)N1c1c#c(Cl)c1-s
remove ( from position 48,CO=Occcccc23[-H(COSC22-]=NC=N(N=r-/N@]32)N1c1c#cCl)c1-s
remove 2 from position 21,CO=Occcccc23[-H(COSC2-]=NC=N(N=r-/N@]32)N1c1c#cCl)c1-s
remove N from position 29,CO=Occcccc23[-H(COSC2-]=NC=N(=r-/N@]32)N1c1c#cCl)c1-s
remove 1 from position 50,CO=Occcccc23[-H(COSC2-]=NC=N(=r-/N@]32)N1c1c#cCl)c-s
replace ) at position 38 with r,CO=Occcccc23[-H(COSC2-]=NC=N(=r-/N@]32rN1c1c#cCl)c-s
add ] at position 46,CO=Occcccc23[-H(COSC2-]=NC=N(=r-/N@]32rN1c1c#c]Cl)c-s
remove ( from position 28,CO=Occcccc23[-H(COSC2-]=NC=N=r-/N@]32rN1c1c#c]Cl)c-s
remove c from position 7,CO=Occccc23[-H(COSC2-]=NC=N=r-/N@]32rN1c1c#c]Cl)c-s
replace N at position 26 with =,CO=Occccc23[-H(COSC2-]=NC===r-/N@]32rN1c1c#c]Cl)c-s
replace ] at position 33 with +,CO=Occccc23[-H(COSC2-]=NC===r-/N@+32rN1c1c#c]Cl)c-s
replace N at position 31 with 7,CO=Occccc23[-H(COSC2-]=NC===r-/7@+32rN1c1c#c]Cl)c-s
replace # at position 42 with 4,CO=Occccc23[-H(COSC2-]=NC===r-/7@+32rN1c1c4c]Cl)c-s
remove = from position 22,CO=Occccc23[-H(COSC2-]NC===r-/7@+32rN1c1c4c]Cl)c-s
add c at position 44,CO=Occccc23[-H(COSC2-]NC===r-/7@+32rN1c1c4c]cCl)c-s
replace 1 at position 37 with s,CO=Occccc23[-H(COSC2-]NC===r-/7@+32rNsc1c4c]cCl)c-s
replace 2 at position 9 with o,CO=Occccco3[-H(COSC2-]NC===r-/7@+32rNsc1c4c]cCl)c-s
replace l at position 46 with 5,CO=Occccco3[-H(COSC2-]NC===r-/7@+32rNsc1c4c]cC5)c-s
replace - at position 28 with ],CO=Occccco3[-H(COSC2-]NC===r]/7@+32rNsc1c4c]cC5)c-s
add 1 at position 25,CO=Occccco3[-H(COSC2-]NC=1==r]/7@+32rNsc1c4c]cC5)c-s
add c at position 18,CO=Occccco3[-H(COScC2-]NC=1==r]/7@+32rNsc1c4c]cC5)c-s
add l at position 32,CO=Occccco3[-H(COScC2-]NC=1==r]/l7@+32rNsc1c4c]cC5)c-s
add 5 at position 44,CO=Occccco3[-H(COScC2-]NC=1==r]/l7@+32rNsc1c54c]cC5)c-s
remove r from position 29,CO=Occccco3[-H(COScC2-]NC=1==]/l7@+32rNsc1c54c]cC5)c-s
add n at position 16,CO=Occccco3[-H(CnOScC2-]NC=1==]/l7@+32rNsc1c54c]cC5)c-s
add s at position 27,CO=Occccco3[-H(CnOScC2-]NC=s1==]/l7@+32rNsc1c54c]cC5)c-s
add 1 at position 25,CO=Occccco3[-H(CnOScC2-]N1C=s1==]/l7@+32rNsc1c54c]cC5)c-s
remove s from position 56,CO=Occccco3[-H(CnOScC2-]N1C=s1==]/l7@+32rNsc1c54c]cC5)c-
remove C from position 15,CO=Occccco3[-H(nOScC2-]N1C=s1==]/l7@+32rNsc1c54c]cC5)c-
replace N at position 23 with s,CO=Occccco3[-H(nOScC2-]s1C=s1==]/l7@+32rNsc1c54c]cC5)c-
replace / at position 32 with r,CO=Occccco3[-H(nOScC2-]s1C=s1==]rl7@+32rNsc1c54c]cC5)c-
remove [ from position 11,CO=Occccco3-H(nOScC2-]s1C=s1==]rl7@+32rNsc1c54c]cC5)c-
replace ( at position 13 with 5,CO=Occccco3-H5nOScC2-]s1C=s1==]rl7@+32rNsc1c54c]cC5)c-
add F at position 22,CO=Occccco3-H5nOScC2-]Fs1C=s1==]rl7@+32rNsc1c54c]cC5)c-
add s at position 49,CO=Occccco3-H5nOScC2-]Fs1C=s1==]rl7@+32rNsc1c54c]scC5)c-
remove ] from position 21,CO=Occccco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc1c54c]scC5)c-
add I at position 3,CO=IOccccco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc1c54c]scC5)c-
add l at position 9,CO=IOcccclco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc1c54c]scC5)c-
remove c from position 45,CO=IOcccclco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc154c]scC5)c-
remove C from position 0,O=IOcccclco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc154c]scC5)c-
remove O from position 3,O=Icccclco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc154c]scC5)c-
remove c from position 48,O=Icccclco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc154c]sC5)c-
replace 5 at position 13 with (,O=Icccclco3-H(nOScC2-Fs1C=s1==]rl7@+32rNsc154c]sC5)c-
remove s from position 47,O=Icccclco3-H(nOScC2-Fs1C=s1==]rl7@+32rNsc154c]C5)c-
remove 5 from position 43,O=Icccclco3-H(nOScC2-Fs1C=s1==]rl7@+32rNsc14c]C5)c-
remove c from position 17,O=Icccclco3-H(nOSC2-Fs1C=s1==]rl7@+32rNsc14c]C5)c-
remove - from position 19,O=Icccclco3-H(nOSC2Fs1C=s1==]rl7@+32rNsc14c]C5)c-
remove ] from position 43,O=Icccclco3-H(nOSC2Fs1C=s1==]rl7@+32rNsc14cC5)c-
remove o from position 9,O=Icccclc3-H(nOSC2Fs1C=s1==]rl7@+32rNsc14cC5)c-
replace c at position 5 with ],O=Icc]clc3-H(nOSC2Fs1C=s1==]rl7@+32rNsc14cC5)c-
remove = from position 26,O=Icc]clc3-H(nOSC2Fs1C=s1=]rl7@+32rNsc14cC5)c-
remove s from position 36,O=Icc]clc3-H(nOSC2Fs1C=s1=]rl7@+32rNc14cC5)c-
remove l from position 7,O=Icc]cc3-H(nOSC2Fs1C=s1=]rl7@+32rNc14cC5)c-
remove F from position 17,O=Icc]cc3-H(nOSC2s1C=s1=]rl7@+32rNc14cC5)c-
add 7 at position 37,O=Icc]cc3-H(nOSC2s1C=s1=]rl7@+32rNc147cC5)c-
add 2 at position 28,O=Icc]cc3-H(nOSC2s1C=s1=]rl72@+32rNc147cC5)c-
replace H at position 10 with 7,O=Icc]cc3-7(nOSC2s1C=s1=]rl72@+32rNc147cC5)c-
remove c from position 4,O=Ic]cc3-7(nOSC2s1C=s1=]rl72@+32rNc147cC5)c-
remove = from position 19,O=Ic]cc3-7(nOSC2s1Cs1=]rl72@+32rNc147cC5)c-
remove 2 from position 30,O=Ic]cc3-7(nOSC2s1Cs1=]rl72@+3rNc147cC5)c-
add B at position 40,O=Ic]cc3-7(nOSC2s1Cs1=]rl72@+3rNc147cC5)Bc-
add 6 at position 37,O=Ic]cc3-7(nOSC2s1Cs1=]rl72@+3rNc147c6C5)Bc-
remove ] from position 22,O=Ic]cc3-7(nOSC2s1Cs1=rl72@+3rNc147c6C5)Bc-
add r at position 33,O=Ic]cc3-7(nOSC2s1Cs1=rl72@+3rNc1r47c6C5)Bc-
remove c from position 42,O=Ic]cc3-7(nOSC2s1Cs1=rl72@+3rNc1r47c6C5)B-
remove = from position 1,OIc]cc3-7(nOSC2s1Cs1=rl72@+3rNc1r47c6C5)B-
replace N at position 29 with S,OIc]cc3-7(nOSC2s1Cs1=rl72@+3rSc1r47c6C5)B-
replace I at position 1 with 6,O6c]cc3-7(nOSC2s1Cs1=rl72@+3rSc1r47c6C5)B-
replace c at position 30 with ),O6c]cc3-7(nOSC2s1Cs1=rl72@+3rS)1r47c6C5)B-
replace C at position 37 with 5,O6c]cc3-7(nOSC2s1Cs1=rl72@+3rS)1r47c655)B-
replace s at position 18 with 4,O6c]cc3-7(nOSC2s1C41=rl72@+3rS)1r47c655)B-
remove 5 from position 37,O6c]cc3-7(nOSC2s1C41=rl72@+3rS)1r47c65)B-
remove 4 from position 18,O6c]cc3-7(nOSC2s1C1=rl72@+3rS)1r47c65)B-
remove 4 from position 32,O6c]cc3-7(nOSC2s1C1=rl72@+3rS)1r7c65)B-
replace C at position 17 with ),O6c]cc3-7(nOSC2s1)1=rl72@+3rS)1r7c65)B-
remove - from position 38,O6c]cc3-7(nOSC2s1)1=rl72@+3rS)1r7c65)B
add 5 at position 32,O6c]cc3-7(nOSC2s1)1=rl72@+3rS)1r57c65)B
replace ) at position 29 with 4,O6c]cc3-7(nOSC2s1)1=rl72@+3rS41r57c65)B
add H at position 33,O6c]cc3-7(nOSC2s1)1=rl72@+3rS41r5H7c65)B
remove - from position 7,O6c]cc37(nOSC2s1)1=rl72@+3rS41r5H7c65)B
replace ) at position 16 with 7,O6c]cc37(nOSC2s171=rl72@+3rS41r5H7c65)B
add 7 at position 1,O76c]cc37(nOSC2s171=rl72@+3rS41r5H7c65)B
add 1 at position 2,O716c]cc37(nOSC2s171=rl72@+3rS41r5H7c65)B
remove = from position 20,O716c]cc37(nOSC2s171rl72@+3rS41r5H7c65)B
remove r from position 27,O716c]cc37(nOSC2s171rl72@+3S41r5H7c65)B
replace c at position 4 with o,O716o]cc37(nOSC2s171rl72@+3S41r5H7c65)B
replace 1 at position 19 with 4,O716o]cc37(nOSC2s174rl72@+3S41r5H7c65)B
remove ) from position 37,O716o]cc37(nOSC2s174rl72@+3S41r5H7c65B
add ) at position 32,O716o]cc37(nOSC2s174rl72@+3S41r5)H7c65B
remove 7 from position 1,O16o]cc37(nOSC2s174rl72@+3S41r5)H7c65B
remove 7 from position 21,O16o]cc37(nOSC2s174rl2@+3S41r5)H7c65B
replace 2 at position 14 with /,O16o]cc37(nOSC/s174rl2@+3S41r5)H7c65B
remove 7 from position 8,O16o]cc3(nOSC/s174rl2@+3S41r5)H7c65B
replace ] at position 4 with -,O16o-cc3(nOSC/s174rl2@+3S41r5)H7c65B
remove 1 from position 15,O16o-cc3(nOSC/s74rl2@+3S41r5)H7c65B
remove 6 from position 32,O16o-cc3(nOSC/s74rl2@+3S41r5)H7c5B
replace 1 at position 25 with o,O16o-cc3(nOSC/s74rl2@+3S4or5)H7c5B
replace ( at position 8 with [,O16o-cc3[nOSC/s74rl2@+3S4or5)H7c5B
replace ) at position 28 with (,O16o-cc3[nOSC/s74rl2@+3S4or5(H7c5B
remove S from position 23,O16o-cc3[nOSC/s74rl2@+34or5(H7c5B
replace 5 at position 26 with r,O16o-cc3[nOSC/s74rl2@+34orr(H7c5B
add 5 at position 4,O16o5-cc3[nOSC/s74rl2@+34orr(H7c5B
add 6 at position 12,O16o5-cc3[nO6SC/s74rl2@+34orr(H7c5B
replace 5 at position 33 with [,O16o5-cc3[nO6SC/s74rl2@+34orr(H7c[B
replace 7 at position 31 with (,O16o5-cc3[nO6SC/s74rl2@+34orr(H(c[B
add l at position 5,O16o5l-cc3[nO6SC/s74rl2@+34orr(H(c[B
remove l from position 21,O16o5l-cc3[nO6SC/s74r2@+34orr(H(c[B
add S at position 15,O16o5l-cc3[nO6SSC/s74r2@+34orr(H(c[B
replace 1 at position 1 with o,Oo6o5l-cc3[nO6SSC/s74r2@+34orr(H(c[B
remove @ from position 23,Oo6o5l-cc3[nO6SSC/s74r2+34orr(H(c[B
remove 5 from position 4,Oo6ol-cc3[nO6SSC/s74r2+34orr(H(c[B
remove S from position 13,Oo6ol-cc3[nO6SC/s74r2+34orr(H(c[B
replace [ at position 9 with C,Oo6ol-cc3CnO6SC/s74r2+34orr(H(c[B
replace s at position 16 with N,Oo6ol-cc3CnO6SC/N74r2+34orr(H(c[B
remove ( from position 29,Oo6ol-cc3CnO6SC/N74r2+34orr(Hc[B
remove 4 from position 23,Oo6ol-cc3CnO6SC/N74r2+3orr(Hc[B
remove S from position 13,Oo6ol-cc3CnO6C/N74r2+3orr(Hc[B
replace c at position 7 with 4,Oo6ol-c43CnO6C/N74r2+3orr(Hc[B
add H at position 10,Oo6ol-c43CHnO6C/N74r2+3orr(Hc[B
replace o at position 3 with F,Oo6Fl-c43CHnO6C/N74r2+3orr(Hc[B
remove H from position 27,Oo6Fl-c43CHnO6C/N74r2+3orr(c[B
add H at position 18,Oo6Fl-c43CHnO6C/N7H4r2+3orr(c[B
remove - from position 5,Oo6Flc43CHnO6C/N7H4r2+3orr(c[B
remove O from position 11,Oo6Flc43CHn6C/N7H4r2+3orr(c[B
replace C at position 8 with [,Oo6Flc43[Hn6C/N7H4r2+3orr(c[B
remove c from position 5,Oo6Fl43[Hn6C/N7H4r2+3orr(c[B
remove 4 from position 5,Oo6Fl3[Hn6C/N7H4r2+3orr(c[B
replace 2 at position 17 with [,Oo6Fl3[Hn6C/N7H4r[+3orr(c[B
remove [ from position 6,Oo6Fl3Hn6C/N7H4r[+3orr(c[B
remove c from position 23,Oo6Fl3Hn6C/N7H4r[+3orr([B
add H at position 23,Oo6Fl3Hn6C/N7H4r[+3orr(H[B
replace H at position 13 with C,Oo6Fl3Hn6C/N7C4r[+3orr(H[B
remove o from position 19,Oo6Fl3Hn6C/N7C4r[+3rr(H[B
remove r from position 15,Oo6Fl3Hn6C/N7C4[+3rr(H[B
remove B from position 23,Oo6Fl3Hn6C/N7C4[+3rr(H[
remove N from position 11,Oo6Fl3Hn6C/7C4[+3rr(H[
remove [ from position 21,Oo6Fl3Hn6C/7C4[+3rr(H
remove C from position 12,Oo6Fl3Hn6C/74[+3rr(H
replace l at position 4 with +,Oo6F+3Hn6C/74[+3rr(H
replace 3 at position 15 with +,Oo6F+3Hn6C/74[++rr(H
remove + from position 4,Oo6F3Hn6C/74[++rr(H
replace C at position 8 with 2,Oo6F3Hn62/74[++rr(H
replace 6 at position 2 with 5,Oo5F3Hn62/74[++rr(H
remove / from position 9,Oo5F3Hn6274[++rr(H
remove [ from position 11,Oo5F3Hn6274++rr(H
remove n from position 6,Oo5F3H6274++rr(H
remove ( from position 14,Oo5F3H6274++rrH
remove 2 from position 7,Oo5F3H674++rrH
replace o at position 1 with F,OF5F3H674++rrH
replace 7 at position 7 with ),OF5F3H6)4++rrH
add I at position 2,OFI5F3H6)4++rrH
replace I at position 2 with 7,OF75F3H6)4++rrH
remove H from position 14,OF75F3H6)4++rr
add / at position 0,/OF75F3H6)4++rr
replace F at position 5 with N,/OF75N3H6)4++rr
add c at position 1,/cOF75N3H6)4++rr
remove + from position 13,/cOF75N3H6)4+rr
remove 7 from position 4,/cOF5N3H6)4+rr
replace 3 at position 6 with [,/cOF5N[H6)4+rr
remove 4 from position 10,/cOF5N[H6)+rr
add # at position 6,/cOF5N#[H6)+rr
remove 6 from position 9,/cOF5N#[H)+rr
replace r at position 11 with 5,/cOF5N#[H)+5r
replace ) at position 9 with 3,/cOF5N#[H3+5r
add 1 at position 13,/cOF5N#[H3+5r1
remove 3 from position 9,/cOF5N#[H+5r1
remove H from position 8,/cOF5N#[+5r1
replace + at position 8 with 4,/cOF5N#[45r1
replace # at position 6 with =,/cOF5N=[45r1
remove 5 from position 9,/cOF5N=[4r1
remove F from position 3,/cO5N=[4r1
replace O at position 2 with /,/c/5N=[4r1
replace / at position 2 with (,/c(5N=[4r1
add r at position 8,/c(5N=[4rr1
replace 1 at position 10 with H,/c(5N=[4rrH
add # at position 7,/c(5N=[#4rrH
remove c from position 1,/(5N=[#4rrH
remove ( from position 1,/5N=[#4rrH
add l at position 3,/5Nl=[#4rrH
remove 4 from position 7,/5Nl=[#rrH
remove l from position 3,/5N=[#rrH
remove H from position 8,/5N=[#rr
remove r from position 7,/5N=[#r
replace # at position 5 with +,/5N=[+r
remove r from position 6,/5N=[+
add C at position 6,/5N=[+C
add 7 at position 2,/57N=[+C
remove 7 from position 2,/5N=[+C
replace / at position 0 with C,C5N=[+C
remove N from position 2,C5=[+C
remove = from position 2,C5[+C
replace + at position 3 with o,C5[oC
remove [ from position 2,C5oC
replace 5 at position 1 with ),C)oC
replace ) at position 1 with B,CBoC
add 3 at position 0,3CBoC
replace C at position 4 with 3,3CBo3
add 1 at position 3,3CB1o3
add - at position 4,3CB1-o3
replace B at position 2 with ],3C]1-o3
remove 1 from position 3,3C]-o3
remove o from position 4,3C]-3
replace C at position 1 with O,3O]-3
add # at position 4,3O]-#3
add l at position 2,3Ol]-#3
remove # from position 5,3Ol]-3
replace ] at position 3 with =,3Ol=-3
remove l from position 2,3O=-3
replace 3 at position 4 with F,3O=-F
add ] at position 4,3O=-]F
remove F from position 5,3O=-]
remove = from position 2,3O-]
remove 3 from position 0,O-]
add r at position 0,rO-]
replace O at position 1 with 7,r7-]
remove r from position 0,7-]
remove 7 from position 0,-]
replace - at position 0 with o,o]
add r at position 0,ro]
replace o at position 1 with c,rc]
add c at position 1,rcc]
add ( at position 3,rcc(]
add C at position 5,rcc(]C
add ( at position 4,rcc((]C
replace ] at position 5 with C,rcc((CC
add = at position 0,=rcc((CC
replace = at position 0 with [,[rcc((CC
replace c at position 2 with 2,[r2c((CC
replace C at position 7 with I,[r2c((CI
add 2 at position 4,[r2c2((CI
remove 2 from position 2,[rc2((CI
remove C from position 6,[rc2((I
remove c from position 2,[r2((I
remove [ from position 0,r2((I
replace ( at position 2 with O,r2O(I
remove 2 from position 1,rO(I
remove ( from position 2,rOI
remove I from position 2,rO
remove r from position 0,O
remove O from position 0,
final: ,
