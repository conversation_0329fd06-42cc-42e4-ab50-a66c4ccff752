log,state
initialize: Cc1ccc2ncc(C(=O)Nc3ncccc3OCc3ccncc3)n2c1,Cc1ccc2ncc(C(=O)Nc3ncccc3OCc3ccncc3)n2c1
replace 3 at position 28 with +,Cc1ccc2ncc(C(=O)Nc3ncccc3OCc+ccncc3)n2c1
add ( at position 37,Cc1ccc2ncc(C(=O)Nc3ncccc3OCc+ccncc3)n(2c1
remove c from position 5,Cc1cc2ncc(C(=O)Nc3ncccc3OCc+ccncc3)n(2c1
add 2 at position 22,Cc1cc2ncc(C(=O)Nc3nccc2c3OCc+ccncc3)n(2c1
add 6 at position 1,C6c1cc2ncc(C(=O)Nc3nccc2c3OCc+ccncc3)n(2c1
remove c from position 30,C6c1cc2ncc(C(=O)Nc3nccc2c3OCc+cncc3)n(2c1
remove c from position 9,C6c1cc2nc(C(=O)Nc3nccc2c3OCc+cncc3)n(2c1
add n at position 7,C6c1cc2nnc(C(=O)Nc3nccc2c3OCc+cncc3)n(2c1
add o at position 31,C6c1cc2nnc(C(=O)Nc3nccc2c3OCc+concc3)n(2c1
replace c at position 4 with O,C6c1Oc2nnc(C(=O)Nc3nccc2c3OCc+concc3)n(2c1
remove + from position 29,C6c1Oc2nnc(C(=O)Nc3nccc2c3OCcconcc3)n(2c1
add C at position 5,C6c1OCc2nnc(C(=O)Nc3nccc2c3OCcconcc3)n(2c1
remove N from position 17,C6c1OCc2nnc(C(=O)c3nccc2c3OCcconcc3)n(2c1
replace O at position 4 with N,C6c1NCc2nnc(C(=O)c3nccc2c3OCcconcc3)n(2c1
remove c from position 22,C6c1NCc2nnc(C(=O)c3ncc2c3OCcconcc3)n(2c1
remove C from position 12,C6c1NCc2nnc((=O)c3ncc2c3OCcconcc3)n(2c1
remove ) from position 33,C6c1NCc2nnc((=O)c3ncc2c3OCcconcc3n(2c1
remove 3 from position 32,C6c1NCc2nnc((=O)c3ncc2c3OCcconccn(2c1
add - at position 21,C6c1NCc2nnc((=O)c3ncc-2c3OCcconccn(2c1
remove 3 from position 24,C6c1NCc2nnc((=O)c3ncc-2cOCcconccn(2c1
add H at position 4,C6c1HNCc2nnc((=O)c3ncc-2cOCcconccn(2c1
remove H from position 4,C6c1NCc2nnc((=O)c3ncc-2cOCcconccn(2c1
remove n from position 9,C6c1NCc2nc((=O)c3ncc-2cOCcconccn(2c1
add ] at position 22,C6c1NCc2nc((=O)c3ncc-2]cOCcconccn(2c1
add - at position 33,C6c1NCc2nc((=O)c3ncc-2]cOCcconccn-(2c1
remove n from position 29,C6c1NCc2nc((=O)c3ncc-2]cOCccoccn-(2c1
add O at position 17,C6c1NCc2nc((=O)c3Oncc-2]cOCccoccn-(2c1
replace ( at position 11 with 4,C6c1NCc2nc(4=O)c3Oncc-2]cOCccoccn-(2c1
add ] at position 9,C6c1NCc2n]c(4=O)c3Oncc-2]cOCccoccn-(2c1
replace c at position 2 with H,C6H1NCc2n]c(4=O)c3Oncc-2]cOCccoccn-(2c1
remove - from position 22,C6H1NCc2n]c(4=O)c3Oncc2]cOCccoccn-(2c1
remove 1 from position 37,C6H1NCc2n]c(4=O)c3Oncc2]cOCccoccn-(2c
replace c at position 30 with (,C6H1NCc2n]c(4=O)c3Oncc2]cOCcco(cn-(2c
replace ( at position 30 with o,C6H1NCc2n]c(4=O)c3Oncc2]cOCccoocn-(2c
replace c at position 10 with @,C6H1NCc2n]@(4=O)c3Oncc2]cOCccoocn-(2c
remove - from position 33,C6H1NCc2n]@(4=O)c3Oncc2]cOCccoocn(2c
replace O at position 18 with 3,C6H1NCc2n]@(4=O)c33ncc2]cOCccoocn(2c
add = at position 11,C6H1NCc2n]@=(4=O)c33ncc2]cOCccoocn(2c
remove 2 from position 7,C6H1NCcn]@=(4=O)c33ncc2]cOCccoocn(2c
replace c at position 6 with n,C6H1NCnn]@=(4=O)c33ncc2]cOCccoocn(2c
replace c at position 24 with n,C6H1NCnn]@=(4=O)c33ncc2]nOCccoocn(2c
replace c at position 16 with N,C6H1NCnn]@=(4=O)N33ncc2]nOCccoocn(2c
add S at position 6,C6H1NCSnn]@=(4=O)N33ncc2]nOCccoocn(2c
remove @ from position 10,C6H1NCSnn]=(4=O)N33ncc2]nOCccoocn(2c
remove 3 from position 17,C6H1NCSnn]=(4=O)N3ncc2]nOCccoocn(2c
replace c at position 20 with l,C6H1NCSnn]=(4=O)N3ncl2]nOCccoocn(2c
replace N at position 4 with ),C6H1)CSnn]=(4=O)N3ncl2]nOCccoocn(2c
remove 1 from position 3,C6H)CSnn]=(4=O)N3ncl2]nOCccoocn(2c
replace H at position 2 with 2,C62)CSnn]=(4=O)N3ncl2]nOCccoocn(2c
remove S from position 5,C62)Cnn]=(4=O)N3ncl2]nOCccoocn(2c
replace ] at position 20 with 4,C62)Cnn]=(4=O)N3ncl24nOCccoocn(2c
replace C at position 4 with I,C62)Inn]=(4=O)N3ncl24nOCccoocn(2c
remove I from position 4,C62)nn]=(4=O)N3ncl24nOCccoocn(2c
remove ) from position 12,C62)nn]=(4=ON3ncl24nOCccoocn(2c
add S at position 12,C62)nn]=(4=OSN3ncl24nOCccoocn(2c
replace ( at position 29 with F,C62)nn]=(4=OSN3ncl24nOCccoocnF2c
add r at position 23,C62)nn]=(4=OSN3ncl24nOCrccoocnF2c
replace c at position 32 with r,C62)nn]=(4=OSN3ncl24nOCrccoocnF2r
remove O from position 11,C62)nn]=(4=SN3ncl24nOCrccoocnF2r
replace 3 at position 13 with 5,C62)nn]=(4=SN5ncl24nOCrccoocnF2r
add F at position 22,C62)nn]=(4=SN5ncl24nOCFrccoocnF2r
add 5 at position 30,C62)nn]=(4=SN5ncl24nOCFrccoocn5F2r
add I at position 3,C62I)nn]=(4=SN5ncl24nOCFrccoocn5F2r
add l at position 9,C62I)nn]=l(4=SN5ncl24nOCFrccoocn5F2r
remove 4 from position 20,C62I)nn]=l(4=SN5ncl2nOCFrccoocn5F2r
add N at position 3,C62NI)nn]=l(4=SN5ncl2nOCFrccoocn5F2r
add r at position 9,C62NI)nn]r=l(4=SN5ncl2nOCFrccoocn5F2r
add 7 at position 27,C62NI)nn]r=l(4=SN5ncl2nOCFr7ccoocn5F2r
remove 4 from position 13,C62NI)nn]r=l(=SN5ncl2nOCFr7ccoocn5F2r
replace F at position 24 with s,C62NI)nn]r=l(=SN5ncl2nOCsr7ccoocn5F2r
replace n at position 17 with F,C62NI)nn]r=l(=SN5Fcl2nOCsr7ccoocn5F2r
remove l from position 19,C62NI)nn]r=l(=SN5Fc2nOCsr7ccoocn5F2r
remove r from position 9,C62NI)nn]=l(=SN5Fc2nOCsr7ccoocn5F2r
replace ) at position 5 with c,C62NIcnn]=l(=SN5Fc2nOCsr7ccoocn5F2r
remove c from position 26,C62NIcnn]=l(=SN5Fc2nOCsr7coocn5F2r
remove c from position 17,C62NIcnn]=l(=SN5F2nOCsr7coocn5F2r
remove c from position 24,C62NIcnn]=l(=SN5F2nOCsr7oocn5F2r
remove O from position 19,C62NIcnn]=l(=SN5F2nCsr7oocn5F2r
remove n from position 18,C62NIcnn]=l(=SN5F2Csr7oocn5F2r
remove ( from position 11,C62NIcnn]=l=SN5F2Csr7oocn5F2r
replace 5 at position 14 with 2,C62NIcnn]=l=SN2F2Csr7oocn5F2r
replace c at position 5 with 7,C62NI7nn]=l=SN2F2Csr7oocn5F2r
remove 2 from position 2,C6NI7nn]=l=SN2F2Csr7oocn5F2r
remove l from position 9,C6NI7nn]==SN2F2Csr7oocn5F2r
remove c from position 21,C6NI7nn]==SN2F2Csr7oon5F2r
replace n at position 5 with 7,C6NI77n]==SN2F2Csr7oon5F2r
remove 7 from position 5,C6NI7n]==SN2F2Csr7oon5F2r
remove 2 from position 11,C6NI7n]==SNF2Csr7oon5F2r
remove F from position 11,C6NI7n]==SN2Csr7oon5F2r
add r at position 16,C6NI7n]==SN2Csr7roon5F2r
remove F from position 21,C6NI7n]==SN2Csr7roon52r
remove C from position 0,6NI7n]==SN2Csr7roon52r
replace 7 at position 14 with S,6NI7n]==SN2CsrSroon52r
replace 6 at position 0 with 7,7NI7n]==SN2CsrSroon52r
replace r at position 15 with ),7NI7n]==SN2CsrS)oon52r
replace n at position 18 with 5,7NI7n]==SN2CsrS)oo552r
replace N at position 9 with 4,7NI7n]==S42CsrS)oo552r
remove 5 from position 18,7NI7n]==S42CsrS)oo52r
remove 4 from position 9,7NI7n]==S2CsrS)oo52r
remove o from position 16,7NI7n]==S2CsrS)o52r
replace S at position 8 with ),7NI7n]==)2CsrS)o52r
remove = from position 6,7NI7n]=)2CsrS)o52r
remove s from position 10,7NI7n]=)2CrS)o52r
replace 5 at position 14 with 3,7NI7n]=)2CrS)o32r
add H at position 16,7NI7n]=)2CrS)o32Hr
remove 7 from position 3,7NIn]=)2CrS)o32Hr
replace C at position 8 with 6,7NIn]=)26rS)o32Hr
add 7 at position 0,77NIn]=)26rS)o32Hr
add 1 at position 1,717NIn]=)26rS)o32Hr
remove 6 from position 10,717NIn]=)2rS)o32Hr
remove o from position 13,717NIn]=)2rS)32Hr
replace 7 at position 2 with o,71oNIn]=)2rS)32Hr
replace 2 at position 9 with 4,71oNIn]=)4rS)32Hr
remove = from position 7,71oNIn])4rS)32Hr
remove o from position 2,71NIn])4rS)32Hr
remove 2 from position 12,71NIn])4rS)3Hr
add 5 at position 9,71NIn])4r5S)3Hr
replace I at position 3 with /,71N/n])4r5S)3Hr
remove S from position 10,71N/n])4r5)3Hr
replace N at position 2 with r,71r/n])4r5)3Hr
remove 5 from position 9,71r/n])4r)3Hr
add s at position 8,71r/n])4sr)3Hr
replace 3 at position 11 with /,71r/n])4sr)/Hr
replace H at position 12 with C,71r/n])4sr)/Cr
remove ) from position 6,71r/n]4sr)/Cr
replace ] at position 5 with -,71r/n-4sr)/Cr
remove r from position 8,71r/n-4s)/Cr
remove / from position 9,71r/n-4s)Cr
remove s from position 7,71r/n-4)Cr
add r at position 1,7r1r/n-4)Cr
remove r from position 10,7r1r/n-4)C
remove 7 from position 0,r1r/n-4)C
remove 1 from position 1,rr/n-4)C
remove n from position 3,rr/-4)C
remove 4 from position 4,rr/-)C
remove - from position 3,rr/)C
remove r from position 0,r/)C
add l at position 0,lr/)C
remove / from position 2,lr)C
add S at position 1,lSr)C
replace l at position 0 with o,oSr)C
remove C from position 4,oSr)
remove S from position 1,or)
add 1 at position 0,1or)
replace o at position 1 with C,1Cr)
replace r at position 2 with N,1CN)
remove ) from position 3,1CN
remove N from position 2,1C
remove C from position 1,1
remove 1 from position 0,
final: ,
