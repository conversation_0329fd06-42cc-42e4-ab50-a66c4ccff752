log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove <PERSON> from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add [ at position 6,oH3H-][N)
add [ at position 2,oH[3H-][N)
add O at position 3,oH[O3H-][N)
replace [ at position 8 with C,oH[O3H-]CN)
add c at position 2,oHc[O3H-]CN)
add 4 at position 2,oH4c[O3H-]CN)
replace [ at position 4 with H,oH4cHO3H-]CN)
add ] at position 5,oH4cH]O3H-]CN)
add O at position 2,oHO4cH]O3H-]CN)
remove H from position 9,oHO4cH]O3-]CN)
add 6 at position 13,oHO4cH]O3-]CN6)
replace H at position 1 with 6,o6O4cH]O3-]CN6)
remove H from position 5,o6O4c]O3-]CN6)
replace 4 at position 3 with r,o6Orc]O3-]CN6)
add S at position 6,o6Orc]SO3-]CN6)
add s at position 11,o6Orc]SO3-]sCN6)
remove 6 from position 14,o6Orc]SO3-]sCN)
replace ] at position 10 with H,o6Orc]SO3-HsCN)
remove O from position 2,o6rc]SO3-HsCN)
add c at position 11,o6rc]SO3-HscCN)
replace N at position 13 with /,o6rc]SO3-HscC/)
add S at position 6,o6rc]SSO3-HscC/)
add l at position 2,o6lrc]SSO3-HscC/)
add F at position 11,o6lrc]SSO3-FHscC/)
replace o at position 0 with S,S6lrc]SSO3-FHscC/)
remove S from position 7,S6lrc]SO3-FHscC/)
add - at position 10,S6lrc]SO3--FHscC/)
remove l from position 2,S6rc]SO3--FHscC/)
add r at position 1,Sr6rc]SO3--FHscC/)
add ) at position 12,Sr6rc]SO3--F)HscC/)
replace F at position 11 with 3,Sr6rc]SO3--3)HscC/)
add O at position 19,Sr6rc]SO3--3)HscC/)O
add 1 at position 2,Sr16rc]SO3--3)HscC/)O
add C at position 1,SCr16rc]SO3--3)HscC/)O
add C at position 21,SCr16rc]SO3--3)HscC/)CO
remove r from position 2,SC16rc]SO3--3)HscC/)CO
add B at position 14,SC16rc]SO3--3)BHscC/)CO
add 1 at position 19,SC16rc]SO3--3)BHscC1/)CO
add o at position 17,SC16rc]SO3--3)BHsocC1/)CO
replace - at position 10 with 6,SC16rc]SO36-3)BHsocC1/)CO
add r at position 12,SC16rc]SO36-r3)BHsocC1/)CO
replace C at position 24 with r,SC16rc]SO36-r3)BHsocC1/)rO
replace / at position 22 with =,SC16rc]SO36-r3)BHsocC1=)rO
remove s from position 17,SC16rc]SO36-r3)BHocC1=)rO
add 2 at position 19,SC16rc]SO36-r3)BHoc2C1=)rO
replace r at position 4 with @,SC16@c]SO36-r3)BHoc2C1=)rO
add / at position 21,SC16@c]SO36-r3)BHoc2C/1=)rO
remove B from position 15,SC16@c]SO36-r3)Hoc2C/1=)rO
add 2 at position 18,SC16@c]SO36-r3)Hoc22C/1=)rO
replace S at position 0 with 7,7C16@c]SO36-r3)Hoc22C/1=)rO
add ) at position 20,7C16@c]SO36-r3)Hoc22)C/1=)rO
add l at position 11,7C16@c]SO36l-r3)Hoc22)C/1=)rO
add ) at position 29,7C16@c]SO36l-r3)Hoc22)C/1=)rO)
replace 3 at position 9 with =,7C16@c]SO=6l-r3)Hoc22)C/1=)rO)
replace ) at position 29 with 7,7C16@c]SO=6l-r3)Hoc22)C/1=)rO7
add = at position 13,7C16@c]SO=6l-=r3)Hoc22)C/1=)rO7
add r at position 20,7C16@c]SO=6l-=r3)Hocr22)C/1=)rO7
remove 1 from position 2,7C6@c]SO=6l-=r3)Hocr22)C/1=)rO7
remove 7 from position 0,C6@c]SO=6l-=r3)Hocr22)C/1=)rO7
replace 6 at position 8 with =,C6@c]SO==l-=r3)Hocr22)C/1=)rO7
add ) at position 3,C6@)c]SO==l-=r3)Hocr22)C/1=)rO7
remove H from position 16,C6@)c]SO==l-=r3)ocr22)C/1=)rO7
replace 3 at position 14 with C,C6@)c]SO==l-=rC)ocr22)C/1=)rO7
add B at position 10,C6@)c]SO==Bl-=rC)ocr22)C/1=)rO7
add 4 at position 13,C6@)c]SO==Bl-4=rC)ocr22)C/1=)rO7
replace ) at position 17 with C,C6@)c]SO==Bl-4=rCCocr22)C/1=)rO7
add ( at position 32,C6@)c]SO==Bl-4=rCCocr22)C/1=)rO7(
add 4 at position 18,C6@)c]SO==Bl-4=rCC4ocr22)C/1=)rO7(
add 6 at position 21,C6@)c]SO==Bl-4=rCC4oc6r22)C/1=)rO7(
replace 4 at position 18 with r,C6@)c]SO==Bl-4=rCCroc6r22)C/1=)rO7(
replace 6 at position 21 with C,C6@)c]SO==Bl-4=rCCrocCr22)C/1=)rO7(
remove ) from position 30,C6@)c]SO==Bl-4=rCCrocCr22)C/1=rO7(
add 4 at position 6,C6@)c]4SO==Bl-4=rCCrocCr22)C/1=rO7(
add ) at position 29,C6@)c]4SO==Bl-4=rCCrocCr22)C/)1=rO7(
add ) at position 30,C6@)c]4SO==Bl-4=rCCrocCr22)C/))1=rO7(
remove r from position 33,C6@)c]4SO==Bl-4=rCCrocCr22)C/))1=O7(
add C at position 22,C6@)c]4SO==Bl-4=rCCrocCCr22)C/))1=O7(
add F at position 23,C6@)c]4SO==Bl-4=rCCrocCFCr22)C/))1=O7(
remove / from position 30,C6@)c]4SO==Bl-4=rCCrocCFCr22)C))1=O7(
add B at position 30,C6@)c]4SO==Bl-4=rCCrocCFCr22)CB))1=O7(
add O at position 19,C6@)c]4SO==Bl-4=rCCOrocCFCr22)CB))1=O7(
add n at position 4,C6@)nc]4SO==Bl-4=rCCOrocCFCr22)CB))1=O7(
replace = at position 10 with 5,C6@)nc]4SO5=Bl-4=rCCOrocCFCr22)CB))1=O7(
remove 2 from position 28,C6@)nc]4SO5=Bl-4=rCCOrocCFCr2)CB))1=O7(
remove 7 from position 37,C6@)nc]4SO5=Bl-4=rCCOrocCFCr2)CB))1=O(
add N at position 17,C6@)nc]4SO5=Bl-4=NrCCOrocCFCr2)CB))1=O(
add = at position 7,C6@)nc]=4SO5=Bl-4=NrCCOrocCFCr2)CB))1=O(
add ) at position 36,C6@)nc]=4SO5=Bl-4=NrCCOrocCFCr2)CB)))1=O(
replace = at position 17 with F,C6@)nc]=4SO5=Bl-4FNrCCOrocCFCr2)CB)))1=O(
replace ) at position 34 with l,C6@)nc]=4SO5=Bl-4FNrCCOrocCFCr2)CBl))1=O(
add C at position 36,C6@)nc]=4SO5=Bl-4FNrCCOrocCFCr2)CBl)C)1=O(
add ( at position 19,C6@)nc]=4SO5=Bl-4FN(rCCOrocCFCr2)CBl)C)1=O(
replace F at position 17 with n,C6@)nc]=4SO5=Bl-4nN(rCCOrocCFCr2)CBl)C)1=O(
remove r from position 24,C6@)nc]=4SO5=Bl-4nN(rCCOocCFCr2)CBl)C)1=O(
add F at position 20,C6@)nc]=4SO5=Bl-4nN(FrCCOocCFCr2)CBl)C)1=O(
replace @ at position 2 with 2,C62)nc]=4SO5=Bl-4nN(FrCCOocCFCr2)CBl)C)1=O(
add + at position 9,C62)nc]=4+SO5=Bl-4nN(FrCCOocCFCr2)CBl)C)1=O(
remove ( from position 43,C62)nc]=4+SO5=Bl-4nN(FrCCOocCFCr2)CBl)C)1=O
add r at position 10,C62)nc]=4+rSO5=Bl-4nN(FrCCOocCFCr2)CBl)C)1=O
remove ) from position 34,C62)nc]=4+rSO5=Bl-4nN(FrCCOocCFCr2CBl)C)1=O
remove r from position 10,C62)nc]=4+SO5=Bl-4nN(FrCCOocCFCr2CBl)C)1=O
add c at position 8,C62)nc]=c4+SO5=Bl-4nN(FrCCOocCFCr2CBl)C)1=O
remove B from position 35,C62)nc]=c4+SO5=Bl-4nN(FrCCOocCFCr2Cl)C)1=O
remove F from position 22,C62)nc]=c4+SO5=Bl-4nN(rCCOocCFCr2Cl)C)1=O
replace 5 at position 13 with 3,C62)nc]=c4+SO3=Bl-4nN(rCCOocCFCr2Cl)C)1=O
add - at position 11,C62)nc]=c4+-SO3=Bl-4nN(rCCOocCFCr2Cl)C)1=O
replace r at position 32 with (,C62)nc]=c4+-SO3=Bl-4nN(rCCOocCFC(2Cl)C)1=O
remove r from position 23,C62)nc]=c4+-SO3=Bl-4nN(CCOocCFC(2Cl)C)1=O
replace F at position 29 with /,C62)nc]=c4+-SO3=Bl-4nN(CCOocC/C(2Cl)C)1=O
remove S from position 12,C62)nc]=c4+-O3=Bl-4nN(CCOocC/C(2Cl)C)1=O
add N at position 12,C62)nc]=c4+-NO3=Bl-4nN(CCOocC/C(2Cl)C)1=O
add I at position 4,C62)Inc]=c4+-NO3=Bl-4nN(CCOocC/C(2Cl)C)1=O
replace I at position 4 with (,C62)(nc]=c4+-NO3=Bl-4nN(CCOocC/C(2Cl)C)1=O
replace 4 at position 20 with ],C62)(nc]=c4+-NO3=Bl-]nN(CCOocC/C(2Cl)C)1=O
add S at position 5,C62)(Snc]=c4+-NO3=Bl-]nN(CCOocC/C(2Cl)C)1=O
replace 2 at position 2 with H,C6H)(Snc]=c4+-NO3=Bl-]nN(CCOocC/C(2Cl)C)1=O
add @ at position 3,C6H@)(Snc]=c4+-NO3=Bl-]nN(CCOocC/C(2Cl)C)1=O
replace ) at position 4 with 1,C6H@1(Snc]=c4+-NO3=Bl-]nN(CCOocC/C(2Cl)C)1=O
replace l at position 20 with O,C6H@1(Snc]=c4+-NO3=BO-]nN(CCOocC/C(2Cl)C)1=O
add 3 at position 17,C6H@1(Snc]=c4+-NO33=BO-]nN(CCOocC/C(2Cl)C)1=O
add @ at position 10,C6H@1(Snc]@=c4+-NO33=BO-]nN(CCOocC/C(2Cl)C)1=O
remove S from position 6,C6H@1(nc]@=c4+-NO33=BO-]nN(CCOocC/C(2Cl)C)1=O
replace O at position 16 with C,C6H@1(nc]@=c4+-NC33=BO-]nN(CCOocC/C(2Cl)C)1=O
replace n at position 24 with 2,C6H@1(nc]@=c4+-NC33=BO-]2N(CCOocC/C(2Cl)C)1=O
replace n at position 6 with c,C6H@1(cc]@=c4+-NC33=BO-]2N(CCOocC/C(2Cl)C)1=O
add c at position 7,C6H@1(ccc]@=c4+-NC33=BO-]2N(CCOocC/C(2Cl)C)1=O
remove = from position 11,C6H@1(ccc]@c4+-NC33=BO-]2N(CCOocC/C(2Cl)C)1=O
replace 3 at position 18 with O,C6H@1(ccc]@c4+-NC3O=BO-]2N(CCOocC/C(2Cl)C)1=O
add - at position 33,C6H@1(ccc]@c4+-NC3O=BO-]2N(CCOocC-/C(2Cl)C)1=O
replace @ at position 10 with c,C6H@1(ccc]cc4+-NC3O=BO-]2N(CCOocC-/C(2Cl)C)1=O
replace o at position 30 with (,C6H@1(ccc]cc4+-NC3O=BO-]2N(CCO(cC-/C(2Cl)C)1=O
replace ( at position 30 with c,C6H@1(ccc]cc4+-NC3O=BO-]2N(CCOccC-/C(2Cl)C)1=O
add c at position 37,C6H@1(ccc]cc4+-NC3O=BO-]2N(CCOccC-/C(c2Cl)C)1=O
add ) at position 22,C6H@1(ccc]cc4+-NC3O=BO)-]2N(CCOccC-/C(c2Cl)C)1=O
replace H at position 2 with [,C6[@1(ccc]cc4+-NC3O=BO)-]2N(CCOccC-/C(c2Cl)C)1=O
remove ] from position 9,C6[@1(ccccc4+-NC3O=BO)-]2N(CCOccC-/C(c2Cl)C)1=O
replace 4 at position 11 with 2,C6[@1(ccccc2+-NC3O=BO)-]2N(CCOccC-/C(c2Cl)C)1=O
remove O from position 17,C6[@1(ccccc2+-NC3=BO)-]2N(CCOccC-/C(c2Cl)C)1=O
add + at position 29,C6[@1(ccccc2+-NC3=BO)-]2N(CCO+ccC-/C(c2Cl)C)1=O
remove - from position 33,C6[@1(ccccc2+-NC3=BO)-]2N(CCO+ccC/C(c2Cl)C)1=O
remove ] from position 22,C6[@1(ccccc2+-NC3=BO)-2N(CCO+ccC/C(c2Cl)C)1=O
remove - from position 13,C6[@1(ccccc2+NC3=BO)-2N(CCO+ccC/C(c2Cl)C)1=O
remove / from position 31,C6[@1(ccccc2+NC3=BO)-2N(CCO+ccCC(c2Cl)C)1=O
add C at position 3,C6[C@1(ccccc2+NC3=BO)-2N(CCO+ccCC(c2Cl)C)1=O
add + at position 42,C6[C@1(ccccc2+NC3=BO)-2N(CCO+ccCC(c2Cl)C)1+=O
remove - from position 21,C6[C@1(ccccc2+NC3=BO)2N(CCO+ccCC(c2Cl)C)1+=O
add l at position 32,C6[C@1(ccccc2+NC3=BO)2N(CCO+ccCCl(c2Cl)C)1+=O
add ) at position 33,C6[C@1(ccccc2+NC3=BO)2N(CCO+ccCCl)(c2Cl)C)1+=O
add c at position 12,C6[C@1(cccccc2+NC3=BO)2N(CCO+ccCCl)(c2Cl)C)1+=O
replace 3 at position 17 with (,C6[C@1(cccccc2+NC(=BO)2N(CCO+ccCCl)(c2Cl)C)1+=O
remove B from position 19,C6[C@1(cccccc2+NC(=O)2N(CCO+ccCCl)(c2Cl)C)1+=O
add c at position 35,C6[C@1(cccccc2+NC(=O)2N(CCO+ccCCl)(cc2Cl)C)1+=O
add C at position 2,C6C[C@1(cccccc2+NC(=O)2N(CCO+ccCCl)(cc2Cl)C)1+=O
remove ) from position 43,C6C[C@1(cccccc2+NC(=O)2N(CCO+ccCCl)(cc2Cl)C1+=O
add c at position 29,C6C[C@1(cccccc2+NC(=O)2N(CCO+cccCCl)(cc2Cl)C1+=O
replace + at position 15 with ),C6C[C@1(cccccc2)NC(=O)2N(CCO+cccCCl)(cc2Cl)C1+=O
replace C at position 32 with (,C6C[C@1(cccccc2)NC(=O)2N(CCO+ccc(Cl)(cc2Cl)C1+=O
remove + from position 45,C6C[C@1(cccccc2)NC(=O)2N(CCO+ccc(Cl)(cc2Cl)C1=O
add 2 at position 9,C6C[C@1(c2ccccc2)NC(=O)2N(CCO+ccc(Cl)(cc2Cl)C1=O
add 2 at position 30,C6C[C@1(c2ccccc2)NC(=O)2N(CCO+2ccc(Cl)(cc2Cl)C1=O
remove 6 from position 1,CC[C@1(c2ccccc2)NC(=O)2N(CCO+2ccc(Cl)(cc2Cl)C1=O
remove 2 from position 22,CC[C@1(c2ccccc2)NC(=O)N(CCO+2ccc(Cl)(cc2Cl)C1=O
add ] at position 5,CC[C@]1(c2ccccc2)NC(=O)N(CCO+2ccc(Cl)(cc2Cl)C1=O
remove ( from position 37,CC[C@]1(c2ccccc2)NC(=O)N(CCO+2ccc(Cl)cc2Cl)C1=O
replace + at position 28 with c,CC[C@]1(c2ccccc2)NC(=O)N(CCOc2ccc(Cl)cc2Cl)C1=O
final: CC[C@]1(c2ccccc2)NC(=O)N(CCOc2ccc(Cl)cc2Cl)C1=O,CC[C@]1(c2ccccc2)NC(=O)N(CCOc2ccc(Cl)cc2Cl)C1=O
