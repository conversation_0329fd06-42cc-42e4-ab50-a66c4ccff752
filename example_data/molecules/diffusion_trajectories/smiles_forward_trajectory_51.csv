log,state
initialize: O=C(Cn1nnn(-c2cccs2)c1=O)OC1CCCCC1,O=C(Cn1nnn(-c2cccs2)c1=O)OC1CCCCC1
replace C at position 28 with +,O=C(Cn1nnn(-c2cccs2)c1=O)OC1+CCCC1
add O at position 2,O=OC(Cn1nnn(-c2cccs2)c1=O)OC1+CCCC1
remove n from position 6,O=OC(C1nnn(-c2cccs2)c1=O)OC1+CCCC1
replace c at position 15 with #,O=OC(C1nnn(-c2c#cs2)c1=O)OC1+CCCC1
add 6 at position 1,O6=OC(C1nnn(-c2c#cs2)c1=O)OC1+CCCC1
remove C from position 30,O6=OC(C1nnn(-c2c#cs2)c1=O)OC1+CCC1
remove n from position 9,O6=OC(C1nn(-c2c#cs2)c1=O)OC1+CCC1
add n at position 7,O6=OC(Cn1nn(-c2c#cs2)c1=O)OC1+CCC1
add o at position 31,O6=OC(Cn1nn(-c2c#cs2)c1=O)OC1+CoCC1
replace C at position 4 with S,O6=OS(Cn1nn(-c2c#cs2)c1=O)OC1+CoCC1
remove + from position 29,O6=OS(Cn1nn(-c2c#cs2)c1=O)OC1CoCC1
add C at position 5,O6=OSC(Cn1nn(-c2c#cs2)c1=O)OC1CoCC1
remove # from position 17,O6=OSC(Cn1nn(-c2ccs2)c1=O)OC1CoCC1
replace S at position 4 with N,O6=ONC(Cn1nn(-c2ccs2)c1=O)OC1CoCC1
remove 1 from position 22,O6=ONC(Cn1nn(-c2ccs2)c=O)OC1CoCC1
remove ( from position 12,O6=ONC(Cn1nn-c2ccs2)c=O)OC1CoCC1
remove 6 from position 1,O=ONC(Cn1nn-c2ccs2)c=O)OC1CoCC1
replace O at position 2 with #,O=#NC(Cn1nn-c2ccs2)c=O)OC1CoCC1
remove ) from position 22,O=#NC(Cn1nn-c2ccs2)c=OOC1CoCC1
remove c from position 12,O=#NC(Cn1nn-2ccs2)c=OOC1CoCC1
add H at position 2,O=H#NC(Cn1nn-2ccs2)c=OOC1CoCC1
remove H from position 2,O=#NC(Cn1nn-2ccs2)c=OOC1CoCC1
remove C from position 4,O=#N(Cn1nn-2ccs2)c=OOC1CoCC1
add ] at position 11,O=#N(Cn1nn-]2ccs2)c=OOC1CoCC1
add - at position 16,O=#N(Cn1nn-]2ccs-2)c=OOC1CoCC1
remove c from position 14,O=#N(Cn1nn-]2cs-2)c=OOC1CoCC1
add O at position 8,O=#N(Cn1Onn-]2cs-2)c=OOC1CoCC1
replace C at position 5 with 3,O=#N(3n1Onn-]2cs-2)c=OOC1CoCC1
add n at position 30,O=#N(3n1Onn-]2cs-2)c=OOC1CoCC1n
add ( at position 29,O=#N(3n1Onn-]2cs-2)c=OOC1CoCC(1n
remove O from position 22,O=#N(3n1Onn-]2cs-2)c=OC1CoCC(1n
remove ) from position 18,O=#N(3n1Onn-]2cs-2c=OC1CoCC(1n
replace s at position 15 with (,O=#N(3n1Onn-]2c(-2c=OC1CoCC(1n
replace ( at position 15 with o,O=#N(3n1Onn-]2co-2c=OC1CoCC(1n
replace 1 at position 28 with /,O=#N(3n1Onn-]2co-2c=OC1CoCC(/n
remove o from position 24,O=#N(3n1Onn-]2co-2c=OC1CCC(/n
remove c from position 14,O=#N(3n1Onn-]2o-2c=OC1CCC(/n
remove N from position 3,O=#(3n1Onn-]2o-2c=OC1CCC(/n
replace o at position 13 with =,O=#(3n1Onn-]2=-2c=OC1CCC(/n
replace c at position 16 with +,O=#(3n1Onn-]2=-2+=OC1CCC(/n
replace 2 at position 15 with =,O=#(3n1Onn-]2=-=+=OC1CCC(/n
replace C at position 21 with 3,O=#(3n1Onn-]2=-=+=OC13CC(/n
remove ] from position 11,O=#(3n1Onn-2=-=+=OC13CC(/n
add c at position 22,O=#(3n1Onn-2=-=+=OC13CcC(/n
replace C at position 18 with s,O=#(3n1Onn-2=-=+=Os13CcC(/n
replace 3 at position 4 with o,O=#(on1Onn-2=-=+=Os13CcC(/n
replace C at position 23 with 5,O=#(on1Onn-2=-=+=Os13Cc5(/n
replace = at position 14 with ],O=#(on1Onn-2=-]+=Os13Cc5(/n
add 1 at position 12,O=#(on1Onn-21=-]+=Os13Cc5(/n
add c at position 9,O=#(on1Oncn-21=-]+=Os13Cc5(/n
add l at position 16,O=#(on1Oncn-21=-l]+=Os13Cc5(/n
add s at position 29,O=#(on1Oncn-21=-l]+=Os13Cc5(/sn
remove n from position 10,O=#(on1Onc-21=-l]+=Os13Cc5(/sn
remove - from position 14,O=#(on1Onc-21=l]+=Os13Cc5(/sn
add n at position 8,O=#(on1Onnc-21=l]+=Os13Cc5(/sn
add s at position 13,O=#(on1Onnc-2s1=l]+=Os13Cc5(/sn
add 1 at position 25,O=#(on1Onnc-2s1=l]+=Os13C1c5(/sn
remove / from position 29,O=#(on1Onnc-2s1=l]+=Os13C1c5(sn
remove c from position 26,O=#(on1Onnc-2s1=l]+=Os13C15(sn
replace n at position 29 with o,O=#(on1Onnc-2s1=l]+=Os13C15(so
add S at position 28,O=#(on1Onnc-2s1=l]+=Os13C15(Sso
remove o from position 30,O=#(on1Onnc-2s1=l]+=Os13C15(Ss
remove n from position 9,O=#(on1Onc-2s1=l]+=Os13C15(Ss
add F at position 11,O=#(on1Onc-F2s1=l]+=Os13C15(Ss
add ] at position 29,O=#(on1Onc-F2s1=l]+=Os13C15(S]s
replace = at position 15 with 5,O=#(on1Onc-F2s15l]+=Os13C15(S]s
add I at position 3,O=#I(on1Onc-F2s15l]+=Os13C15(S]s
add l at position 9,O=#I(on1Olnc-F2s15l]+=Os13C15(S]s
remove + from position 20,O=#I(on1Olnc-F2s15l]=Os13C15(S]s
add N at position 3,O=#NI(on1Olnc-F2s15l]=Os13C15(S]s
add r at position 9,O=#NI(on1rOlnc-F2s15l]=Os13C15(S]s
add 7 at position 27,O=#NI(on1rOlnc-F2s15l]=Os137C15(S]s
remove c from position 13,O=#NI(on1rOln-F2s15l]=Os137C15(S]s
replace 1 at position 24 with s,O=#NI(on1rOln-F2s15l]=Oss37C15(S]s
replace 1 at position 17 with H,O=#NI(on1rOln-F2sH5l]=Oss37C15(S]s
remove l from position 19,O=#NI(on1rOln-F2sH5]=Oss37C15(S]s
remove r from position 9,O=#NI(on1Oln-F2sH5]=Oss37C15(S]s
replace ( at position 5 with c,O=#NIcon1Oln-F2sH5]=Oss37C15(S]s
remove 1 from position 26,O=#NIcon1Oln-F2sH5]=Oss37C5(S]s
remove ] from position 18,O=#NIcon1Oln-F2sH5=Oss37C5(S]s
remove N from position 3,O=#Icon1Oln-F2sH5=Oss37C5(S]s
remove O from position 8,O=#Icon1ln-F2sH5=Oss37C5(S]s
add l at position 22,O=#Icon1ln-F2sH5=Oss37lC5(S]s
remove = from position 1,O#Icon1ln-F2sH5=Oss37lC5(S]s
remove ] from position 26,O#Icon1ln-F2sH5=Oss37lC5(Ss
remove ( from position 24,O#Icon1ln-F2sH5=Oss37lC5Ss
remove s from position 12,O#Icon1ln-F2H5=Oss37lC5Ss
remove I from position 2,O#con1ln-F2H5=Oss37lC5Ss
remove F from position 9,O#con1ln-2H5=Oss37lC5Ss
remove S from position 21,O#con1ln-2H5=Oss37lC5s
replace 1 at position 5 with =,O#con=ln-2H5=Oss37lC5s
remove = from position 5,O#conln-2H5=Oss37lC5s
remove = from position 11,O#conln-2H5Oss37lC5s
remove O from position 11,O#conln-2H5ss37lC5s
add r at position 16,O#conln-2H5ss37lrC5s
remove l from position 15,O#conln-2H5ss37rC5s
remove 7 from position 14,O#conln-2H5ss3rC5s
remove o from position 3,O#cnln-2H5ss3rC5s
add ) at position 15,O#cnln-2H5ss3rC)5s
replace s at position 10 with 6,O#cnln-2H56s3rC)5s
replace 5 at position 9 with 4,O#cnln-2H46s3rC)5s
remove 6 from position 10,O#cnln-2H4s3rC)5s
remove 4 from position 9,O#cnln-2Hs3rC)5s
remove H from position 8,O#cnln-2s3rC)5s
add 5 at position 6,O#cnln5-2s3rC)5s
remove 3 from position 10,O#cnln5-2srC)5s
remove # from position 1,Ocnln5-2srC)5s
replace r at position 9 with H,Ocnln5-2sHC)5s
remove s from position 13,Ocnln5-2sHC)5
add - at position 5,Ocnln-5-2sHC)5
replace 5 at position 6 with (,Ocnln-(-2sHC)5
remove O from position 0,cnln-(-2sHC)5
replace C at position 10 with -,cnln-(-2sH-)5
replace s at position 8 with 6,cnln-(-26H-)5
remove 6 from position 8,cnln-(-2H-)5
replace - at position 4 with 4,cnln4(-2H-)5
remove - from position 9,cnln4(-2H)5
add ) at position 8,cnln4(-2)H)5
remove c from position 0,nln4(-2)H)5
remove - from position 5,nln4(2)H)5
replace 4 at position 3 with /,nln/(2)H)5
remove n from position 2,nl/(2)H)5
replace l at position 1 with -,n-/(2)H)5
remove ( from position 3,n-/2)H)5
remove ) from position 6,n-/2)H5
replace / at position 2 with -,n--2)H5
remove ) from position 4,n--2H5
remove H from position 4,n--25
remove 2 from position 3,n--5
add r at position 0,rn--5
remove - from position 3,rn-5
replace r at position 0 with 5,5n-5
add 6 at position 1,56n-5
replace 5 at position 4 with [,56n-[
replace [ at position 4 with B,56n-B
replace 5 at position 0 with B,B6n-B
replace n at position 2 with =,B6=-B
replace 6 at position 1 with C,BC=-B
remove C from position 1,B=-B
remove B from position 0,=-B
replace - at position 1 with I,=IB
remove = from position 0,IB
add 1 at position 0,1IB
replace B at position 2 with N,1IN
remove 1 from position 0,IN
remove I from position 0,N
remove N from position 0,
final: ,
