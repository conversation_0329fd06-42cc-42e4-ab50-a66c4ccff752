log,state
initialize: CC(=O)C1=C([O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1c1ccccc1F,CC(=O)C1=C([O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1c1ccccc1F
replace c at position 56 with +,CC(=O)C1=C([O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1+1ccccc1F
add O at position 4,CC(=OO)C1=C([O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1+1ccccc1F
remove [ from position 12,CC(=OO)C1=C(O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1+1ccccc1F
replace c at position 30 with #,CC(=OO)C1=C(O-])C(=O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1ccccc1F
add 6 at position 2,CC6(=OO)C1=C(O-])C(=O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1ccccc1F
remove c from position 61,CC6(=OO)C1=C(O-])C(=O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1cccc1F
remove = from position 19,CC6(=OO)C1=C(O-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1cccc1F
add n at position 14,CC6(=OO)C1=C(On-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1cccc1F
add o at position 62,CC6(=OO)C1=C(On-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1cccoc1F
replace C at position 8 with S,CC6(=OO)S1=C(On-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1cccoc1F
remove c from position 59,CC6(=OO)S1=C(On-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1ccoc1F
add C at position 11,CC6(=OO)S1=CC(On-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1ccoc1F
remove c from position 34,CC6(=OO)S1=CC(On-])C(O)N(CCC2=c3#ccc3=[NH+][C@H]2C)[C@H]1+1ccoc1F
replace S at position 8 with N,CC6(=OO)N1=CC(On-])C(O)N(CCC2=c3#ccc3=[NH+][C@H]2C)[C@H]1+1ccoc1F
remove @ from position 45,CC6(=OO)N1=CC(On-])C(O)N(CCC2=c3#ccc3=[NH+][CH]2C)[C@H]1+1ccoc1F
remove C from position 25,CC6(=OO)N1=CC(On-])C(O)N(CC2=c3#ccc3=[NH+][CH]2C)[C@H]1+1ccoc1F
remove c from position 33,CC6(=OO)N1=CC(On-])C(O)N(CC2=c3#cc3=[NH+][CH]2C)[C@H]1+1ccoc1F
remove 1 from position 60,CC6(=OO)N1=CC(On-])C(O)N(CC2=c3#cc3=[NH+][CH]2C)[C@H]1+1ccocF
remove C from position 0,C6(=OO)N1=CC(On-])C(O)N(CC2=c3#cc3=[NH+][CH]2C)[C@H]1+1ccocF
remove C from position 45,C6(=OO)N1=CC(On-])C(O)N(CC2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
remove C from position 24,C6(=OO)N1=CC(On-])C(O)N(C2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
add H at position 4,C6(=HOO)N1=CC(On-])C(O)N(C2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
remove H from position 4,C6(=OO)N1=CC(On-])C(O)N(C2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
remove = from position 9,C6(=OO)N1CC(On-])C(O)N(C2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
add ] at position 22,C6(=OO)N1CC(On-])C(O)N](C2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
add - at position 33,C6(=OO)N1CC(On-])C(O)N](C2=c3#cc3-=[NH+][CH]2)[C@H]1+1ccocF
remove # from position 29,C6(=OO)N1CC(On-])C(O)N](C2=c3cc3-=[NH+][CH]2)[C@H]1+1ccocF
add O at position 17,C6(=OO)N1CC(On-])OC(O)N](C2=c3cc3-=[NH+][CH]2)[C@H]1+1ccocF
replace ( at position 11 with 4,C6(=OO)N1CC4On-])OC(O)N](C2=c3cc3-=[NH+][CH]2)[C@H]1+1ccocF
add - at position 54,C6(=OO)N1CC4On-])OC(O)N](C2=c3cc3-=[NH+][CH]2)[C@H]1+1-ccocF
replace 3 at position 29 with (,C6(=OO)N1CC4On-])OC(O)N](C2=c(cc3-=[NH+][CH]2)[C@H]1+1-ccocF
remove @ from position 48,C6(=OO)N1CC4On-])OC(O)N](C2=c(cc3-=[NH+][CH]2)[CH]1+1-ccocF
remove ) from position 21,C6(=OO)N1CC4On-])OC(ON](C2=c(cc3-=[NH+][CH]2)[CH]1+1-ccocF
remove c from position 29,C6(=OO)N1CC4On-])OC(ON](C2=c(c3-=[NH+][CH]2)[CH]1+1-ccocF
remove 1 from position 50,C6(=OO)N1CC4On-])OC(ON](C2=c(c3-=[NH+][CH]2)[CH]1+-ccocF
replace F at position 55 with n,C6(=OO)N1CC4On-])OC(ON](C2=c(c3-=[NH+][CH]2)[CH]1+-ccocn
remove C from position 10,C6(=OO)N1C4On-])OC(ON](C2=c(c3-=[NH+][CH]2)[CH]1+-ccocn
remove - from position 49,C6(=OO)N1C4On-])OC(ON](C2=c(c3-=[NH+][CH]2)[CH]1+ccocn
remove c from position 28,C6(=OO)N1C4On-])OC(ON](C2=c(3-=[NH+][CH]2)[CH]1+ccocn
remove N from position 7,C6(=OO)1C4On-])OC(ON](C2=c(3-=[NH+][CH]2)[CH]1+ccocn
replace ( at position 26 with @,C6(=OO)1C4On-])OC(ON](C2=c@3-=[NH+][CH]2)[CH]1+ccocn
replace + at position 33 with -,C6(=OO)1C4On-])OC(ON](C2=c@3-=[NH-][CH]2)[CH]1+ccocn
replace N at position 31 with 7,C6(=OO)1C4On-])OC(ON](C2=c@3-=[7H-][CH]2)[CH]1+ccocn
replace C at position 42 with 3,C6(=OO)1C4On-])OC(ON](C2=c@3-=[7H-][CH]2)[3H]1+ccocn
remove C from position 22,C6(=OO)1C4On-])OC(ON](2=c@3-=[7H-][CH]2)[3H]1+ccocn
add c at position 44,C6(=OO)1C4On-])OC(ON](2=c@3-=[7H-][CH]2)[3H]c1+ccocn
replace ] at position 37 with s,C6(=OO)1C4On-])OC(ON](2=c@3-=[7H-][CHs2)[3H]c1+ccocn
replace 4 at position 9 with o,C6(=OO)1CoOn-])OC(ON](2=c@3-=[7H-][CHs2)[3H]c1+ccocn
replace + at position 46 with 6,C6(=OO)1CoOn-])OC(ON](2=c@3-=[7H-][CHs2)[3H]c16ccocn
replace = at position 28 with ],C6(=OO)1CoOn-])OC(ON](2=c@3-][7H-][CHs2)[3H]c16ccocn
add 1 at position 25,C6(=OO)1CoOn-])OC(ON](2=c1@3-][7H-][CHs2)[3H]c16ccocn
add c at position 18,C6(=OO)1CoOn-])OC(cON](2=c1@3-][7H-][CHs2)[3H]c16ccocn
add l at position 32,C6(=OO)1CoOn-])OC(cON](2=c1@3-][l7H-][CHs2)[3H]c16ccocn
add 5 at position 44,C6(=OO)1CoOn-])OC(cON](2=c1@3-][l7H-][CHs2)[53H]c16ccocn
remove - from position 29,C6(=OO)1CoOn-])OC(cON](2=c1@3][l7H-][CHs2)[53H]c16ccocn
add n at position 16,C6(=OO)1CoOn-])OnC(cON](2=c1@3][l7H-][CHs2)[53H]c16ccocn
add s at position 27,C6(=OO)1CoOn-])OnC(cON](2=cs1@3][l7H-][CHs2)[53H]c16ccocn
add 1 at position 25,C6(=OO)1CoOn-])OnC(cON](21=cs1@3][l7H-][CHs2)[53H]c16ccocn
remove c from position 56,C6(=OO)1CoOn-])OnC(cON](21=cs1@3][l7H-][CHs2)[53H]c16ccon
remove O from position 15,C6(=OO)1CoOn-])nC(cON](21=cs1@3][l7H-][CHs2)[53H]c16ccon
replace 2 at position 23 with s,C6(=OO)1CoOn-])nC(cON](s1=cs1@3][l7H-][CHs2)[53H]c16ccon
replace [ at position 32 with r,C6(=OO)1CoOn-])nC(cON](s1=cs1@3]rl7H-][CHs2)[53H]c16ccon
remove n from position 11,C6(=OO)1CoO-])nC(cON](s1=cs1@3]rl7H-][CHs2)[53H]c16ccon
replace ) at position 13 with 5,C6(=OO)1CoO-]5nC(cON](s1=cs1@3]rl7H-][CHs2)[53H]c16ccon
add F at position 22,C6(=OO)1CoO-]5nC(cON](Fs1=cs1@3]rl7H-][CHs2)[53H]c16ccon
add s at position 49,C6(=OO)1CoO-]5nC(cON](Fs1=cs1@3]rl7H-][CHs2)[53H]sc16ccon
remove ( from position 21,C6(=OO)1CoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)[53H]sc16ccon
add I at position 3,C6(I=OO)1CoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)[53H]sc16ccon
add l at position 9,C6(I=OO)1lCoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)[53H]sc16ccon
remove [ from position 45,C6(I=OO)1lCoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]sc16ccon
remove C from position 0,6(I=OO)1lCoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]sc16ccon
remove = from position 3,6(IOO)1lCoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]sc16ccon
remove c from position 48,6(IOO)1lCoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]s16ccon
replace 5 at position 13 with (,6(IOO)1lCoO-](nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]s16ccon
remove s from position 47,6(IOO)1lCoO-](nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]16ccon
remove n from position 52,6(IOO)1lCoO-](nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]16cco
remove c from position 17,6(IOO)1lCoO-](nC(ON]Fs1=cs1@3]rl7H-][CHs2)53H]16cco
remove ] from position 19,6(IOO)1lCoO-](nC(ONFs1=cs1@3]rl7H-][CHs2)53H]16cco
remove H from position 43,6(IOO)1lCoO-](nC(ONFs1=cs1@3]rl7H-][CHs2)53]16cco
remove o from position 9,6(IOO)1lCO-](nC(ONFs1=cs1@3]rl7H-][CHs2)53]16cco
replace ) at position 5 with c,6(IOOc1lCO-](nC(ONFs1=cs1@3]rl7H-][CHs2)53]16cco
remove 3 from position 26,6(IOOc1lCO-](nC(ONFs1=cs1@]rl7H-][CHs2)53]16cco
remove s from position 36,6(IOOc1lCO-](nC(ONFs1=cs1@]rl7H-][CH2)53]16cco
remove l from position 7,6(IOOc1CO-](nC(ONFs1=cs1@]rl7H-][CH2)53]16cco
remove F from position 17,6(IOOc1CO-](nC(ONs1=cs1@]rl7H-][CH2)53]16cco
add 7 at position 37,6(IOOc1CO-](nC(ONs1=cs1@]rl7H-][CH2)573]16cco
add 2 at position 28,6(IOOc1CO-](nC(ONs1=cs1@]rl72H-][CH2)573]16cco
replace ] at position 10 with 7,6(IOOc1CO-7(nC(ONs1=cs1@]rl72H-][CH2)573]16cco
remove O from position 4,6(IOc1CO-7(nC(ONs1=cs1@]rl72H-][CH2)573]16cco
remove c from position 19,6(IOc1CO-7(nC(ONs1=s1@]rl72H-][CH2)573]16cco
remove o from position 43,6(IOc1CO-7(nC(ONs1=s1@]rl72H-][CH2)573]16cc
replace n at position 11 with 7,6(IOc1CO-7(7C(ONs1=s1@]rl72H-][CH2)573]16cc
remove ( from position 10,6(IOc1CO-77C(ONs1=s1@]rl72H-][CH2)573]16cc
remove l from position 23,6(IOc1CO-77C(ONs1=s1@]r72H-][CH2)573]16cc
remove r from position 22,6(IOc1CO-77C(ONs1=s1@]72H-][CH2)573]16cc
add r at position 33,6(IOc1CO-77C(ONs1=s1@]72H-][CH2)5r73]16cc
remove 2 from position 30,6(IOc1CO-77C(ONs1=s1@]72H-][CH)5r73]16cc
remove H from position 29,6(IOc1CO-77C(ONs1=s1@]72H-][C)5r73]16cc
remove C from position 6,6(IOc1O-77C(ONs1=s1@]72H-][C)5r73]16cc
add ) at position 30,6(IOc1O-77C(ONs1=s1@]72H-][C)5)r73]16cc
replace c at position 37 with 5,6(IOc1O-77C(ONs1=s1@]72H-][C)5)r73]165c
replace 1 at position 18 with 5,6(IOc1O-77C(ONs1=s5@]72H-][C)5)r73]165c
remove 5 from position 37,6(IOc1O-77C(ONs1=s5@]72H-][C)5)r73]16c
remove 5 from position 18,6(IOc1O-77C(ONs1=s@]72H-][C)5)r73]16c
remove 3 from position 32,6(IOc1O-77C(ONs1=s@]72H-][C)5)r7]16c
replace s at position 17 with ),6(IOc1O-77C(ONs1=)@]72H-][C)5)r7]16c
remove N from position 13,6(IOc1O-77C(Os1=)@]72H-][C)5)r7]16c
remove H from position 21,6(IOc1O-77C(Os1=)@]72-][C)5)r7]16c
replace 7 at position 29 with 3,6(IOc1O-77C(Os1=)@]72-][C)5)r3]16c
add H at position 33,6(IOc1O-77C(Os1=)@]72-][C)5)r3]16Hc
remove - from position 7,6(IOc1O77C(Os1=)@]72-][C)5)r3]16Hc
replace @ at position 16 with 6,6(IOc1O77C(Os1=)6]72-][C)5)r3]16Hc
add 7 at position 1,67(IOc1O77C(Os1=)6]72-][C)5)r3]16Hc
add 1 at position 2,671(IOc1O77C(Os1=)6]72-][C)5)r3]16Hc
remove 7 from position 20,671(IOc1O77C(Os1=)6]2-][C)5)r3]16Hc
remove ) from position 27,671(IOc1O77C(Os1=)6]2-][C)5r3]16Hc
replace I at position 4 with o,671(oOc1O77C(Os1=)6]2-][C)5r3]16Hc
replace ] at position 19 with 3,671(oOc1O77C(Os1=)632-][C)5r3]16Hc
remove s from position 14,671(oOc1O77C(O1=)632-][C)5r3]16Hc
remove O from position 5,671(oc1O77C(O1=)632-][C)5r3]16Hc
remove 7 from position 1,61(oc1O77C(O1=)632-][C)5r3]16Hc
remove ( from position 10,61(oc1O77CO1=)632-][C)5r3]16Hc
replace 7 at position 7 with /,61(oc1O/7CO1=)632-][C)5r3]16Hc
remove ) from position 21,61(oc1O/7CO1=)632-][C5r3]16Hc
replace c at position 4 with r,61(or1O/7CO1=)632-][C5r3]16Hc
remove [ from position 19,61(or1O/7CO1=)632-]C5r3]16Hc
add s at position 17,61(or1O/7CO1=)632s-]C5r3]16Hc
replace r at position 22 with /,61(or1O/7CO1=)632s-]C5/3]16Hc
replace ] at position 24 with C,61(or1O/7CO1=)632s-]C5/3C16Hc
remove = from position 12,61(or1O/7CO1)632s-]C5/3C16Hc
replace O at position 10 with -,61(or1O/7C-1)632s-]C5/3C16Hc
remove - from position 17,61(or1O/7C-1)632s]C5/3C16Hc
remove 5 from position 19,61(or1O/7C-1)632s]C/3C16Hc
remove 3 from position 14,61(or1O/7C-1)62s]C/3C16Hc
add r at position 2,61r(or1O/7C-1)62s]C/3C16Hc
remove C from position 21,61r(or1O/7C-1)62s]C/316Hc
remove 1 from position 1,6r(or1O/7C-1)62s]C/316Hc
remove ( from position 2,6ror1O/7C-1)62s]C/316Hc
remove 1 from position 19,6ror1O/7C-1)62s]C/36Hc
replace ) at position 11 with F,6ror1O/7C-1F62s]C/36Hc
remove 6 from position 12,6ror1O/7C-1F2s]C/36Hc
remove r from position 1,6or1O/7C-1F2s]C/36Hc
add l at position 2,6olr1O/7C-1F2s]C/36Hc
remove 1 from position 10,6olr1O/7C-F2s]C/36Hc
add 2 at position 20,6olr1O/7C-F2s]C/36Hc2
remove 6 from position 0,olr1O/7C-F2s]C/36Hc2
replace s at position 11 with H,olr1O/7C-F2H]C/36Hc2
remove / from position 5,olr1O7C-F2H]C/36Hc2
add 1 at position 0,1olr1O7C-F2H]C/36Hc2
replace 2 at position 19 with N,1olr1O7C-F2H]C/36HcN
remove 1 from position 4,1olrO7C-F2H]C/36HcN
remove O from position 4,1olr7C-F2H]C/36HcN
remove 2 from position 8,1olr7C-FH]C/36HcN
remove o from position 1,1lr7C-FH]C/36HcN
replace c at position 14 with 6,1lr7C-FH]C/36H6N
remove 3 from position 11,1lr7C-FH]C/6H6N
remove F from position 6,1lr7C-H]C/6H6N
replace 7 at position 3 with 4,1lr4C-H]C/6H6N
add H at position 5,1lr4CH-H]C/6H6N
replace l at position 1 with F,1Fr4CH-H]C/6H6N
remove 6 from position 13,1Fr4CH-H]C/6HN
add H at position 9,1Fr4CH-H]HC/6HN
remove r from position 2,1F4CH-H]HC/6HN
remove - from position 5,1F4CHH]HC/6HN
replace H at position 4 with [,1F4C[H]HC/6HN
remove 4 from position 2,1FC[H]HC/6HN
remove C from position 2,1F[H]HC/6HN
replace 6 at position 8 with [,1F[H]HC/[HN
remove H from position 3,1F[]HC/[HN
remove [ from position 2,1F]HC/[HN
remove [ from position 6,1F]HC/HN
remove N from position 7,1F]HC/H
replace C at position 4 with @,1F]H@/H
remove H from position 6,1F]H@/
remove @ from position 4,1F]H/
add C at position 5,1F]H/C
replace / at position 4 with -,1F]H-C
add B at position 3,1F]BH-C
add c at position 2,1Fc]BH-C
replace ] at position 3 with S,1FcSBH-C
add s at position 6,1FcSBHs-C
remove C from position 8,1FcSBHs-
remove S from position 3,1FcBHs-
remove s from position 5,1FcBH-
replace B at position 3 with 5,1Fc5H-
replace H at position 4 with +,1Fc5+-
remove 5 from position 3,1Fc+-
add / at position 1,1/Fc+-
remove F from position 2,1/c+-
add S at position 2,1/Sc+-
replace S at position 2 with #,1/#c+-
add 5 at position 5,1/#c+5-
remove 1 from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
