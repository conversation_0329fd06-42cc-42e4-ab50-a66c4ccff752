log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with ),oHc)BH-C
remove c from position 2,oH)BH-C
remove B from position 3,oH)H-C
replace - at position 4 with [,oH)H[C
remove C from position 5,oH)H[
add B at position 4,oH)HB[
add N at position 6,oH)HB[N
replace B at position 4 with -,oH)H-[N
add r at position 7,oH)H-[Nr
add [ at position 6,oH)H-[[Nr
add [ at position 2,oH[)H-[[Nr
add 6 at position 3,oH[6)H-[[Nr
replace [ at position 8 with B,oH[6)H-[BNr
add l at position 2,oHl[6)H-[BNr
add 4 at position 2,oH4l[6)H-[BNr
replace [ at position 4 with H,oH4lH6)H-[BNr
add 6 at position 5,oH4lH66)H-[BNr
add O at position 2,oHO4lH66)H-[BNr
remove H from position 9,oHO4lH66)-[BNr
add 6 at position 13,oHO4lH66)-[BN6r
replace H at position 1 with (,o(O4lH66)-[BN6r
remove H from position 5,o(O4l66)-[BN6r
replace 4 at position 3 with r,o(Orl66)-[BN6r
add = at position 6,o(Orl6=6)-[BN6r
add s at position 11,o(Orl6=6)-[sBN6r
remove 6 from position 14,o(Orl6=6)-[sBNr
replace [ at position 10 with c,o(Orl6=6)-csBNr
remove O from position 2,o(rl6=6)-csBNr
add 2 at position 11,o(rl6=6)-cs2BNr
replace N at position 13 with /,o(rl6=6)-cs2B/r
add S at position 6,o(rl6=S6)-cs2B/r
add l at position 2,o(lrl6=S6)-cs2B/r
add F at position 11,o(lrl6=S6)-Fcs2B/r
replace o at position 0 with S,S(lrl6=S6)-Fcs2B/r
remove S from position 7,S(lrl6=6)-Fcs2B/r
add H at position 10,S(lrl6=6)-HFcs2B/r
remove l from position 2,S(rl6=6)-HFcs2B/r
add r at position 1,Sr(rl6=6)-HFcs2B/r
add ) at position 12,Sr(rl6=6)-HF)cs2B/r
replace F at position 11 with 3,Sr(rl6=6)-H3)cs2B/r
add 4 at position 19,Sr(rl6=6)-H3)cs2B/r4
add + at position 2,Sr+(rl6=6)-H3)cs2B/r4
add [ at position 1,S[r+(rl6=6)-H3)cs2B/r4
add F at position 21,S[r+(rl6=6)-H3)cs2B/rF4
remove r from position 2,S[+(rl6=6)-H3)cs2B/rF4
add c at position 14,S[+(rl6=6)-H3)ccs2B/rF4
add C at position 19,S[+(rl6=6)-H3)ccs2BC/rF4
add ) at position 17,S[+(rl6=6)-H3)ccs)2BC/rF4
replace - at position 10 with l,S[+(rl6=6)lH3)ccs)2BC/rF4
add = at position 12,S[+(rl6=6)lH=3)ccs)2BC/rF4
replace F at position 24 with (,S[+(rl6=6)lH=3)ccs)2BC/r(4
replace / at position 22 with 1,S[+(rl6=6)lH=3)ccs)2BC1r(4
remove s from position 17,S[+(rl6=6)lH=3)cc)2BC1r(4
add - at position 19,S[+(rl6=6)lH=3)cc)2-BC1r(4
replace r at position 4 with ],S[+(]l6=6)lH=3)cc)2-BC1r(4
add ( at position 21,S[+(]l6=6)lH=3)cc)2-B(C1r(4
add S at position 5,S[+(]Sl6=6)lH=3)cc)2-B(C1r(4
add ) at position 27,S[+(]Sl6=6)lH=3)cc)2-B(C1r()4
replace S at position 0 with C,C[+(]Sl6=6)lH=3)cc)2-B(C1r()4
add O at position 20,C[+(]Sl6=6)lH=3)cc)2O-B(C1r()4
replace 6 at position 7 with O,C[+(]SlO=6)lH=3)cc)2O-B(C1r()4
add N at position 15,C[+(]SlO=6)lH=3N)cc)2O-B(C1r()4
replace 4 at position 30 with 7,C[+(]SlO=6)lH=3N)cc)2O-B(C1r()7
replace ) at position 29 with 7,C[+(]SlO=6)lH=3N)cc)2O-B(C1r(77
add ) at position 27,C[+(]SlO=6)lH=3N)cc)2O-B(C1)r(77
replace 6 at position 9 with 1,C[+(]SlO=1)lH=3N)cc)2O-B(C1)r(77
add @ at position 2,C[@+(]SlO=1)lH=3N)cc)2O-B(C1)r(77
replace ( at position 25 with /,C[@+(]SlO=1)lH=3N)cc)2O-B/C1)r(77
remove - from position 23,C[@+(]SlO=1)lH=3N)cc)2OB/C1)r(77
remove l from position 7,C[@+(]SO=1)lH=3N)cc)2OB/C1)r(77
replace 3 at position 14 with r,C[@+(]SO=1)lH=rN)cc)2OB/C1)r(77
add ) at position 21,C[@+(]SO=1)lH=rN)cc)2)OB/C1)r(77
add 4 at position 13,C[@+(]SO=1)lH4=rN)cc)2)OB/C1)r(77
replace ) at position 17 with r,C[@+(]SO=1)lH4=rNrcc)2)OB/C1)r(77
add H at position 32,C[@+(]SO=1)lH4=rNrcc)2)OB/C1)r(7H7
add 4 at position 18,C[@+(]SO=1)lH4=rNr4cc)2)OB/C1)r(7H7
add 6 at position 21,C[@+(]SO=1)lH4=rNr4cc6)2)OB/C1)r(7H7
replace 4 at position 18 with o,C[@+(]SO=1)lH4=rNrocc6)2)OB/C1)r(7H7
replace 6 at position 21 with r,C[@+(]SO=1)lH4=rNroccr)2)OB/C1)r(7H7
remove ) from position 30,C[@+(]SO=1)lH4=rNroccr)2)OB/C1r(7H7
add 4 at position 6,C[@+(]4SO=1)lH4=rNroccr)2)OB/C1r(7H7
add ) at position 29,C[@+(]4SO=1)lH4=rNroccr)2)OB/)C1r(7H7
add 3 at position 30,C[@+(]4SO=1)lH4=rNroccr)2)OB/)3C1r(7H7
remove r from position 33,C[@+(]4SO=1)lH4=rNroccr)2)OB/)3C1(7H7
add F at position 22,C[@+(]4SO=1)lH4=rNroccFr)2)OB/)3C1(7H7
add ( at position 23,C[@+(]4SO=1)lH4=rNroccF(r)2)OB/)3C1(7H7
remove / from position 30,C[@+(]4SO=1)lH4=rNroccF(r)2)OB)3C1(7H7
add C at position 30,C[@+(]4SO=1)lH4=rNroccF(r)2)OBC)3C1(7H7
add c at position 19,C[@+(]4SO=1)lH4=rNrcoccF(r)2)OBC)3C1(7H7
add n at position 4,C[@+n(]4SO=1)lH4=rNrcoccF(r)2)OBC)3C1(7H7
replace = at position 10 with 5,C[@+n(]4SO51)lH4=rNrcoccF(r)2)OBC)3C1(7H7
remove 2 from position 28,C[@+n(]4SO51)lH4=rNrcoccF(r))OBC)3C1(7H7
remove 7 from position 37,C[@+n(]4SO51)lH4=rNrcoccF(r))OBC)3C1(H7
add 1 at position 17,C[@+n(]4SO51)lH4=1rNrcoccF(r))OBC)3C1(H7
add = at position 7,C[@+n(]=4SO51)lH4=1rNrcoccF(r))OBC)3C1(H7
add # at position 36,C[@+n(]=4SO51)lH4=1rNrcoccF(r))OBC)3#C1(H7
replace = at position 17 with F,C[@+n(]=4SO51)lH4F1rNrcoccF(r))OBC)3#C1(H7
replace ) at position 34 with O,C[@+n(]=4SO51)lH4F1rNrcoccF(r))OBCO3#C1(H7
add ) at position 36,C[@+n(]=4SO51)lH4F1rNrcoccF(r))OBCO3)#C1(H7
add C at position 19,C[@+n(]=4SO51)lH4F1CrNrcoccF(r))OBCO3)#C1(H7
replace F at position 17 with n,C[@+n(]=4SO51)lH4n1CrNrcoccF(r))OBCO3)#C1(H7
remove 7 from position 43,C[@+n(]=4SO51)lH4n1CrNrcoccF(r))OBCO3)#C1(H
add F at position 20,C[@+n(]=4SO51)lH4n1CFrNrcoccF(r))OBCO3)#C1(H
replace @ at position 2 with 2,C[2+n(]=4SO51)lH4n1CFrNrcoccF(r))OBCO3)#C1(H
add N at position 9,C[2+n(]=4NSO51)lH4n1CFrNrcoccF(r))OBCO3)#C1(H
remove ( from position 43,C[2+n(]=4NSO51)lH4n1CFrNrcoccF(r))OBCO3)#C1H
add r at position 10,C[2+n(]=4NrSO51)lH4n1CFrNrcoccF(r))OBCO3)#C1H
remove ) from position 34,C[2+n(]=4NrSO51)lH4n1CFrNrcoccF(r)OBCO3)#C1H
remove r from position 10,C[2+n(]=4NSO51)lH4n1CFrNrcoccF(r)OBCO3)#C1H
add S at position 8,C[2+n(]=S4NSO51)lH4n1CFrNrcoccF(r)OBCO3)#C1H
remove B from position 35,C[2+n(]=S4NSO51)lH4n1CFrNrcoccF(r)OCO3)#C1H
remove F from position 22,C[2+n(]=S4NSO51)lH4n1CrNrcoccF(r)OCO3)#C1H
replace 5 at position 13 with 3,C[2+n(]=S4NSO31)lH4n1CrNrcoccF(r)OCO3)#C1H
add C at position 11,C[2+n(]=S4NCSO31)lH4n1CrNrcoccF(r)OCO3)#C1H
replace r at position 32 with c,C[2+n(]=S4NCSO31)lH4n1CrNrcoccF(c)OCO3)#C1H
remove r from position 23,C[2+n(]=S4NCSO31)lH4n1CNrcoccF(c)OCO3)#C1H
replace F at position 29 with 3,C[2+n(]=S4NCSO31)lH4n1CNrcocc3(c)OCO3)#C1H
remove S from position 12,C[2+n(]=S4NCO31)lH4n1CNrcocc3(c)OCO3)#C1H
add C at position 12,C[2+n(]=S4NCCO31)lH4n1CNrcocc3(c)OCO3)#C1H
add H at position 4,C[2+Hn(]=S4NCCO31)lH4n1CNrcocc3(c)OCO3)#C1H
replace H at position 4 with ],C[2+]n(]=S4NCCO31)lH4n1CNrcocc3(c)OCO3)#C1H
replace 4 at position 20 with ],C[2+]n(]=S4NCCO31)lH]n1CNrcocc3(c)OCO3)#C1H
add S at position 5,C[2+]Sn(]=S4NCCO31)lH]n1CNrcocc3(c)OCO3)#C1H
replace 2 at position 2 with I,C[I+]Sn(]=S4NCCO31)lH]n1CNrcocc3(c)OCO3)#C1H
add O at position 3,C[IO+]Sn(]=S4NCCO31)lH]n1CNrcocc3(c)OCO3)#C1H
replace + at position 4 with #,C[IO#]Sn(]=S4NCCO31)lH]n1CNrcocc3(c)OCO3)#C1H
replace l at position 20 with [,C[IO#]Sn(]=S4NCCO31)[H]n1CNrcocc3(c)OCO3)#C1H
add C at position 17,C[IO#]Sn(]=S4NCCOC31)[H]n1CNrcocc3(c)OCO3)#C1H
add @ at position 10,C[IO#]Sn(]@=S4NCCOC31)[H]n1CNrcocc3(c)OCO3)#C1H
remove S from position 6,C[IO#]n(]@=S4NCCOC31)[H]n1CNrcocc3(c)OCO3)#C1H
replace O at position 16 with C,C[IO#]n(]@=S4NCCCC31)[H]n1CNrcocc3(c)OCO3)#C1H
replace n at position 24 with +,C[IO#]n(]@=S4NCCCC31)[H]+1CNrcocc3(c)OCO3)#C1H
replace n at position 6 with (,C[IO#]((]@=S4NCCCC31)[H]+1CNrcocc3(c)OCO3)#C1H
add C at position 7,C[IO#](C(]@=S4NCCCC31)[H]+1CNrcocc3(c)OCO3)#C1H
remove = from position 11,C[IO#](C(]@S4NCCCC31)[H]+1CNrcocc3(c)OCO3)#C1H
replace 3 at position 18 with O,C[IO#](C(]@S4NCCCCO1)[H]+1CNrcocc3(c)OCO3)#C1H
add - at position 33,C[IO#](C(]@S4NCCCCO1)[H]+1CNrcocc-3(c)OCO3)#C1H
replace @ at position 10 with O,C[IO#](C(]OS4NCCCCO1)[H]+1CNrcocc-3(c)OCO3)#C1H
replace o at position 30 with (,C[IO#](C(]OS4NCCCCO1)[H]+1CNrc(cc-3(c)OCO3)#C1H
replace ( at position 30 with 2,C[IO#](C(]OS4NCCCCO1)[H]+1CNrc2cc-3(c)OCO3)#C1H
add 2 at position 37,C[IO#](C(]OS4NCCCCO1)[H]+1CNrc2cc-3(c2)OCO3)#C1H
add N at position 22,C[IO#](C(]OS4NCCCCO1)[NH]+1CNrc2cc-3(c2)OCO3)#C1H
replace I at position 2 with C,C[CO#](C(]OS4NCCCCO1)[NH]+1CNrc2cc-3(c2)OCO3)#C1H
remove ] from position 9,C[CO#](C(OS4NCCCCO1)[NH]+1CNrc2cc-3(c2)OCO3)#C1H
replace 4 at position 11 with ),C[CO#](C(OS)NCCCCO1)[NH]+1CNrc2cc-3(c2)OCO3)#C1H
remove O from position 17,C[CO#](C(OS)NCCCC1)[NH]+1CNrc2cc-3(c2)OCO3)#C1H
add r at position 29,C[CO#](C(OS)NCCCC1)[NH]+1CNrcr2cc-3(c2)OCO3)#C1H
remove - from position 33,C[CO#](C(OS)NCCCC1)[NH]+1CNrcr2cc3(c2)OCO3)#C1H
remove ] from position 22,C[CO#](C(OS)NCCCC1)[NH+1CNrcr2cc3(c2)OCO3)#C1H
add = at position 9,C[CO#](C(=OS)NCCCC1)[NH+1CNrcr2cc3(c2)OCO3)#C1H
add H at position 4,C[COH#](C(=OS)NCCCC1)[NH+1CNrcr2cc3(c2)OCO3)#C1H
remove H from position 4,C[CO#](C(=OS)NCCCC1)[NH+1CNrcr2cc3(c2)OCO3)#C1H
add ] at position 24,C[CO#](C(=OS)NCCCC1)[NH+]1CNrcr2cc3(c2)OCO3)#C1H
add C at position 45,C[CO#](C(=OS)NCCCC1)[NH+]1CNrcr2cc3(c2)OCO3)#CC1H
replace # at position 4 with H,C[COH](C(=OS)NCCCC1)[NH+]1CNrcr2cc3(c2)OCO3)#CC1H
add C at position 1,CC[COH](C(=OS)NCCCC1)[NH+]1CNrcr2cc3(c2)OCO3)#CC1H
replace r at position 29 with (,CC[COH](C(=OS)NCCCC1)[NH+]1CN(cr2cc3(c2)OCO3)#CC1H
add ) at position 41,CC[COH](C(=OS)NCCCC1)[NH+]1CN(cr2cc3(c2)O)CO3)#CC1H
add 2 at position 22,CC[COH](C(=OS)NCCCC1)[2NH+]1CN(cr2cc3(c2)O)CO3)#CC1H
replace O at position 4 with @,CC[C@H](C(=OS)NCCCC1)[2NH+]1CN(cr2cc3(c2)O)CO3)#CC1H
add O at position 17,CC[C@H](C(=OS)NCCOCC1)[2NH+]1CN(cr2cc3(c2)O)CO3)#CC1H
add C at position 32,CC[C@H](C(=OS)NCCOCC1)[2NH+]1CN(Ccr2cc3(c2)O)CO3)#CC1H
add c at position 38,CC[C@H](C(=OS)NCCOCC1)[2NH+]1CN(Ccr2ccc3(c2)O)CO3)#CC1H
remove r from position 34,CC[C@H](C(=OS)NCCOCC1)[2NH+]1CN(Cc2ccc3(c2)O)CO3)#CC1H
add 1 at position 15,CC[C@H](C(=OS)N1CCOCC1)[2NH+]1CN(Cc2ccc3(c2)O)CO3)#CC1H
remove C from position 0,C[C@H](C(=OS)N1CCOCC1)[2NH+]1CN(Cc2ccc3(c2)O)CO3)#CC1H
remove S from position 11,C[C@H](C(=O)N1CCOCC1)[2NH+]1CN(Cc2ccc3(c2)O)CO3)#CC1H
add + at position 29,C[C@H](C(=O)N1CCOCC1)[2NH+]1C+N(Cc2ccc3(c2)O)CO3)#CC1H
add c at position 39,C[C@H](C(=O)N1CCOCC1)[2NH+]1C+N(Cc2ccc3c(c2)O)CO3)#CC1H
remove # from position 50,C[C@H](C(=O)N1CCOCC1)[2NH+]1C+N(Cc2ccc3c(c2)O)CO3)CC1H
remove 2 from position 22,C[C@H](C(=O)N1CCOCC1)[NH+]1C+N(Cc2ccc3c(c2)O)CO3)CC1H
remove ) from position 44,C[C@H](C(=O)N1CCOCC1)[NH+]1C+N(Cc2ccc3c(c2)OCO3)CC1H
remove H from position 51,C[C@H](C(=O)N1CCOCC1)[NH+]1C+N(Cc2ccc3c(c2)OCO3)CC1
replace + at position 28 with C,C[C@H](C(=O)N1CCOCC1)[NH+]1CCN(Cc2ccc3c(c2)OCO3)CC1
final: C[C@H](C(=O)N1CCOCC1)[NH+]1CCN(Cc2ccc3c(c2)OCO3)CC1,C[C@H](C(=O)N1CCOCC1)[NH+]1CCN(Cc2ccc3c(c2)OCO3)CC1
