log,state
initialize: ,
add [ at position 0,[
add O at position 0,O[
add o at position 0,oO[
replace [ at position 2 with S,o<PERSON>
add [ at position 1,o[OS
add 1 at position 1,o1[OS
replace [ at position 2 with 6,o16<PERSON>
remove 6 from position 2,o1OS
add I at position 4,o1OSI
replace I at position 4 with (,o1OS(
remove ( from position 4,o1OS
add 5 at position 2,o15OS
remove o from position 0,15OS
add F at position 2,15FOS
add 1 at position 3,15F1OS
add I at position 5,15F1<PERSON><PERSON>
add B at position 7,15F1OISB
replace O at position 4 with 3,15F13ISB
replace F at position 2 with B,15B13ISB
remove 1 from position 0,5B13ISB
add = at position 2,5B=13ISB
replace I at position 5 with 3,5B=133SB
add B at position 0,B5B=133SB
add 6 at position 3,B5B6=133SB
replace B at position 2 with o,B5o6=133SB
replace = at position 4 with r,B5o6r133SB
replace B at position 0 with 7,75o6r133SB
replace B at position 9 with C,75o6r133SC
replace <PERSON> at position 8 with r,75o6r133r<PERSON>
remove 6 from position 3,75or13<PERSON>r<PERSON>
replace 5 at position 1 with r,7ror13<PERSON>r<PERSON>
add / at position 6,7ror13/3rC
remove r from position 1,7or13/3rC
add s at position 7,7or13/3srC
add 1 at position 9,7or13/3sr1C
add ) at position 8,7or13/3s)r1C
replace / at position 5 with ),7or13)3s)r1C
add s at position 6,7or13)s3s)r1C
replace C at position 12 with c,7or13)s3s)r1c
replace 1 at position 11 with (,7or13)s3s)r(c
remove s from position 8,7or13)s3)r(c
add 5 at position 9,7or13)s3)5r(c
replace r at position 2 with C,7oC13)s3)5r(c
add H at position 10,7oC13)s3)5Hr(c
replace 1 at position 3 with ),7oC)3)s3)5Hr(c
remove 5 from position 9,7oC)3)s3)Hr(c
add ) at position 12,7oC)3)s3)Hr()c
remove ) from position 8,7oC)3)s3Hr()c
add C at position 9,7oC)3)s3HCr()c
replace 3 at position 4 with n,7oC)n)s3HCr()c
replace o at position 1 with 1,71C)n)s3HCr()c
add 6 at position 6,71C)n)6s3HCr()c
add 5 at position 10,71C)n)6s3H5Cr()c
remove 1 from position 1,7C)n)6s3H5Cr()c
remove 7 from position 0,C)n)6s3H5Cr()c
replace 6 at position 4 with C,C)n)Cs3H5Cr()c
add c at position 1,Cc)n)Cs3H5Cr()c
remove H from position 8,Cc)n)Cs35Cr()c
replace 3 at position 7 with ],Cc)n)Cs]5Cr()c
remove 5 from position 8,Cc)n)Cs]Cr()c
add 7 at position 9,Cc)n)Cs]C7r()c
replace ) at position 4 with l,Cc)nlCs]C7r()c
add 5 at position 8,Cc)nlCs]5C7r()c
add 4 at position 9,Cc)nlCs]54C7r()c
add 6 at position 10,Cc)nlCs]546C7r()c
replace 4 at position 9 with [,Cc)nlCs]5[6C7r()c
replace 6 at position 10 with s,Cc)nlCs]5[sC7r()c
remove ) from position 15,Cc)nlCs]5[sC7r(c
add c at position 3,Cc)cnlCs]5[sC7r(c
add c at position 14,Cc)cnlCs]5[sC7cr(c
add l at position 15,Cc)cnlCs]5[sC7clr(c
remove r from position 16,Cc)cnlCs]5[sC7cl(c
add ] at position 11,Cc)cnlCs]5[]sC7cl(c
add 2 at position 11,Cc)cnlCs]5[2]sC7cl(c
add = at position 5,Cc)cn=lCs]5[2]sC7cl(c
replace = at position 5 with 4,Cc)cn4lCs]5[2]sC7cl(c
add - at position 21,Cc)cn4lCs]5[2]sC7cl(c-
add H at position 9,Cc)cn4lCsH]5[2]sC7cl(c-
add I at position 2,CcI)cn4lCsH]5[2]sC7cl(c-
add H at position 12,CcI)cn4lCsH]H5[2]sC7cl(c-
add C at position 24,CcI)cn4lCsH]H5[2]sC7cl(cC-
add ] at position 26,CcI)cn4lCsH]H5[2]sC7cl(cC-]
add I at position 1,CIcI)cn4lCsH]H5[2]sC7cl(cC-]
remove l from position 22,CIcI)cn4lCsH]H5[2]sC7c(cC-]
add = at position 8,CIcI)cn4=lCsH]H5[2]sC7c(cC-]
add N at position 3,CIcNI)cn4=lCsH]H5[2]sC7c(cC-]
add 4 at position 18,CIcNI)cn4=lCsH]H5[42]sC7c(cC-]
add F at position 13,CIcNI)cn4=lCsFH]H5[42]sC7c(cC-]
replace c at position 2 with 6,CI6NI)cn4=lCsFH]H5[42]sC7c(cC-]
add r at position 9,CI6NI)cn4r=lCsFH]H5[42]sC7c(cC-]
add r at position 19,CI6NI)cn4r=lCsFH]H5r[42]sC7c(cC-]
replace H at position 17 with 1,CI6NI)cn4r=lCsFH]15r[42]sC7c(cC-]
replace s at position 24 with n,CI6NI)cn4r=lCsFH]15r[42]nC7c(cC-]
add 3 at position 13,CI6NI)cn4r=lC3sFH]15r[42]nC7c(cC-]
remove 7 from position 27,CI6NI)cn4r=lC3sFH]15r[42]nCc(cC-]
remove r from position 9,CI6NI)cn4=lC3sFH]15r[42]nCc(cC-]
remove N from position 3,CI6I)cn4=lC3sFH]15r[42]nCc(cC-]
add N at position 20,CI6I)cn4=lC3sFH]15r[N42]nCc(cC-]
remove l from position 9,CI6I)cn4=C3sFH]15r[N42]nCc(cC-]
remove I from position 1,C6I)cn4=C3sFH]15r[N42]nCc(cC-]
replace 5 at position 15 with O,C6I)cn4=C3sFH]1Or[N42]nCc(cC-]
remove ] from position 29,C6I)cn4=C3sFH]1Or[N42]nCc(cC-
remove F from position 11,C6I)cn4=C3sH]1Or[N42]nCc(cC-
replace 4 at position 6 with ],C6I)cn]=C3sH]1Or[N42]nCc(cC-
add S at position 5,C6I)cSn]=C3sH]1Or[N42]nCc(cC-
replace r at position 16 with 3,C6I)cSn]=C3sH]1O3[N42]nCc(cC-
replace s at position 11 with 1,C6I)cSn]=C31H]1O3[N42]nCc(cC-
add n at position 7,C6I)cSnn]=C31H]1O3[N42]nCc(cC-
add c at position 28,C6I)cSnn]=C31H]1O3[N42]nCc(ccC-
remove 1 from position 12,C6I)cSnn]=C3H]1O3[N42]nCc(ccC-
add ( at position 30,C6I)cSnn]=C3H]1O3[N42]nCc(ccC-(
add I at position 4,C6I)IcSnn]=C3H]1O3[N42]nCc(ccC-(
replace I at position 4 with C,C6I)CcSnn]=C3H]1O3[N42]nCc(ccC-(
replace 4 at position 20 with l,C6I)CcSnn]=C3H]1O3[Nl2]nCc(ccC-(
remove c from position 5,C6I)CSnn]=C3H]1O3[Nl2]nCc(ccC-(
replace ( at position 25 with 2,C6I)CSnn]=C3H]1O3[Nl2]nCc2ccC-(
add C at position 3,C6IC)CSnn]=C3H]1O3[Nl2]nCc2ccC-(
replace ) at position 4 with N,C6ICNCSnn]=C3H]1O3[Nl2]nCc2ccC-(
replace l at position 20 with H,C6ICNCSnn]=C3H]1O3[NH2]nCc2ccC-(
add C at position 17,C6ICNCSnn]=C3H]1OC3[NH2]nCc2ccC-(
add @ at position 10,C6ICNCSnn]@=C3H]1OC3[NH2]nCc2ccC-(
remove S from position 6,C6ICNCnn]@=C3H]1OC3[NH2]nCc2ccC-(
replace O at position 16 with C,C6ICNCnn]@=C3H]1CC3[NH2]nCc2ccC-(
replace n at position 24 with ],C6ICNCnn]@=C3H]1CC3[NH2]]Cc2ccC-(
replace n at position 6 with (,C6ICNC(n]@=C3H]1CC3[NH2]]Cc2ccC-(
add O at position 7,C6ICNC(On]@=C3H]1CC3[NH2]]Cc2ccC-(
remove = from position 11,C6ICNC(On]@C3H]1CC3[NH2]]Cc2ccC-(
replace 3 at position 18 with O,C6ICNC(On]@C3H]1CCO[NH2]]Cc2ccC-(
add 1 at position 33,C6ICNC(On]@C3H]1CCO[NH2]]Cc2ccC-(1
replace @ at position 10 with N,C6ICNC(On]NC3H]1CCO[NH2]]Cc2ccC-(1
add 2 at position 30,C6ICNC(On]NC3H]1CCO[NH2]]Cc2cc2C-(1
add o at position 29,C6ICNC(On]NC3H]1CCO[NH2]]Cc2coc2C-(1
add - at position 22,C6ICNC(On]NC3H]1CCO[NH-2]]Cc2coc2C-(1
replace I at position 2 with C,C6CCNC(On]NC3H]1CCO[NH-2]]Cc2coc2C-(1
remove ] from position 9,C6CCNC(OnNC3H]1CCO[NH-2]]Cc2coc2C-(1
replace 3 at position 11 with @,C6CCNC(OnNC@H]1CCO[NH-2]]Cc2coc2C-(1
remove O from position 17,C6CCNC(OnNC@H]1CC[NH-2]]Cc2coc2C-(1
add c at position 29,C6CCNC(OnNC@H]1CC[NH-2]]Cc2cocc2C-(1
remove - from position 33,C6CCNC(OnNC@H]1CC[NH-2]]Cc2cocc2C(1
remove ] from position 22,C6CCNC(OnNC@H]1CC[NH-2]Cc2cocc2C(1
add ) at position 9,C6CCNC(On)NC@H]1CC[NH-2]Cc2cocc2C(1
add H at position 4,C6CCHNC(On)NC@H]1CC[NH-2]Cc2cocc2C(1
remove H from position 4,C6CCNC(On)NC@H]1CC[NH-2]Cc2cocc2C(1
add ( at position 24,C6CCNC(On)NC@H]1CC[NH-2](Cc2cocc2C(1
remove - from position 21,C6CCNC(On)NC@H]1CC[NH2](Cc2cocc2C(1
add C at position 32,C6CCNC(On)NC@H]1CC[NH2](Cc2cocc2CC(1
add ) at position 33,C6CCNC(On)NC@H]1CC[NH2](Cc2cocc2C)C(1
add @ at position 12,C6CCNC(On)NC@@H]1CC[NH2](Cc2cocc2C)C(1
add + at position 22,C6CCNC(On)NC@@H]1CC[NH+2](Cc2cocc2C)C(1
replace N at position 4 with S,C6CCSC(On)NC@@H]1CC[NH+2](Cc2cocc2C)C(1
add C at position 17,C6CCSC(On)NC@@H]1CCC[NH+2](Cc2cocc2C)C(1
remove C from position 5,C6CCS(On)NC@@H]1CCC[NH+2](Cc2cocc2C)C(1
add + at position 29,C6CCS(On)NC@@H]1CCC[NH+2](Cc2+cocc2C)C(1
replace S at position 4 with C,C6CCC(On)NC@@H]1CCC[NH+2](Cc2+cocc2C)C(1
remove o from position 31,C6CCC(On)NC@@H]1CCC[NH+2](Cc2+ccc2C)C(1
remove n from position 7,C6CCC(O)NC@@H]1CCC[NH+2](Cc2+ccc2C)C(1
add [ at position 9,C6CCC(O)N[C@@H]1CCC[NH+2](Cc2+ccc2C)C(1
add c at position 30,C6CCC(O)N[C@@H]1CCC[NH+2](Cc2+cccc2C)C(1
remove 6 from position 1,CCCC(O)N[C@@H]1CCC[NH+2](Cc2+cccc2C)C(1
remove 2 from position 22,CCCC(O)N[C@@H]1CCC[NH+](Cc2+cccc2C)C(1
add = at position 5,CCCC(=O)N[C@@H]1CCC[NH+](Cc2+cccc2C)C(1
remove ( from position 37,CCCC(=O)N[C@@H]1CCC[NH+](Cc2+cccc2C)C1
replace + at position 28 with n,CCCC(=O)N[C@@H]1CCC[NH+](Cc2ncccc2C)C1
final: CCCC(=O)N[C@@H]1CCC[NH+](Cc2ncccc2C)C1,CCCC(=O)N[C@@H]1CCC[NH+](Cc2ncccc2C)C1
