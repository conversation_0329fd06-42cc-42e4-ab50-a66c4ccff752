log,state
initialize: CCc1noc(C)c1C[NH+](C[C@@H]1CCCCO1)C(C)C,CCc1noc(C)c1C[NH+](C[C@@H]1CCCCO1)C(C)C
replace C at position 28 with +,CCc1noc(C)c1C[NH+](C[C@@H]1C+CCO1)C(C)C
add ( at position 37,CCc1noc(C)c1C[NH+](C[C@@H]1C+CCO1)C(C()C
remove o from position 5,CCc1nc(C)c1C[NH+](C[C@@H]1C+CCO1)C(C()C
add 2 at position 22,CCc1nc(C)c1C[NH+](C[C@2@H]1C+CCO1)C(C()C
add 6 at position 1,C6Cc1nc(C)c1C[NH+](C[C@2@H]1C+CCO1)C(C()C
remove C from position 30,C6Cc1nc(C)c1C[NH+](C[C@2@H]1C+CO1)C(C()C
remove ) from position 9,C6Cc1nc(Cc1C[NH+](C[C@2@H]1C+CO1)C(C()C
add n at position 7,C6Cc1ncn(Cc1C[NH+](C[C@2@H]1C+CO1)C(C()C
add o at position 31,C6Cc1ncn(Cc1C[NH+](C[C@2@H]1C+CoO1)C(C()C
replace 1 at position 4 with S,C6CcSncn(Cc1C[NH+](C[C@2@H]1C+CoO1)C(C()C
remove + from position 29,C6CcSncn(Cc1C[NH+](C[C@2@H]1CCoO1)C(C()C
add C at position 5,C6CcSCncn(Cc1C[NH+](C[C@2@H]1CCoO1)C(C()C
remove + from position 17,C6CcSCncn(Cc1C[NH](C[C@2@H]1CCoO1)C(C()C
replace S at position 4 with N,C6CcNCncn(Cc1C[NH](C[C@2@H]1CCoO1)C(C()C
remove @ from position 22,C6CcNCncn(Cc1C[NH](C[C2@H]1CCoO1)C(C()C
remove 1 from position 12,C6CcNCncn(CcC[NH](C[C2@H]1CCoO1)C(C()C
remove ( from position 33,C6CcNCncn(CcC[NH](C[C2@H]1CCoO1)CC()C
remove C from position 32,C6CcNCncn(CcC[NH](C[C2@H]1CCoO1)C()C
add - at position 21,C6CcNCncn(CcC[NH](C[C-2@H]1CCoO1)C()C
remove H from position 24,C6CcNCncn(CcC[NH](C[C-2@]1CCoO1)C()C
add H at position 4,C6CcHNCncn(CcC[NH](C[C-2@]1CCoO1)C()C
remove H from position 4,C6CcNCncn(CcC[NH](C[C-2@]1CCoO1)C()C
remove ( from position 9,C6CcNCncnCcC[NH](C[C-2@]1CCoO1)C()C
add ] at position 22,C6CcNCncnCcC[NH](C[C-2]@]1CCoO1)C()C
add - at position 33,C6CcNCncnCcC[NH](C[C-2]@]1CCoO1)C-()C
remove O from position 29,C6CcNCncnCcC[NH](C[C-2]@]1CCo1)C-()C
add O at position 17,C6CcNCncnCcC[NH](OC[C-2]@]1CCo1)C-()C
replace C at position 11 with 3,C6CcNCncnCc3[NH](OC[C-2]@]1CCo1)C-()C
add ] at position 9,C6CcNCncn]Cc3[NH](OC[C-2]@]1CCo1)C-()C
replace C at position 2 with I,C6IcNCncn]Cc3[NH](OC[C-2]@]1CCo1)C-()C
remove - from position 22,C6IcNCncn]Cc3[NH](OC[C2]@]1CCo1)C-()C
remove o from position 29,C6IcNCncn]Cc3[NH](OC[C2]@]1CC1)C-()C
remove ) from position 30,C6IcNCncn]Cc3[NH](OC[C2]@]1CC1C-()C
replace C at position 10 with @,C6IcNCncn]@c3[NH](OC[C2]@]1CC1C-()C
remove ) from position 33,C6IcNCncn]@c3[NH](OC[C2]@]1CC1C-(C
replace O at position 18 with 3,C6IcNCncn]@c3[NH](3C[C2]@]1CC1C-(C
add = at position 11,C6IcNCncn]@=c3[NH](3C[C2]@]1CC1C-(C
remove c from position 7,C6IcNCnn]@=c3[NH](3C[C2]@]1CC1C-(C
replace n at position 6 with l,C6IcNCln]@=c3[NH](3C[C2]@]1CC1C-(C
replace @ at position 24 with n,C6IcNCln]@=c3[NH](3C[C2]n]1CC1C-(C
replace ] at position 16 with N,C6IcNCln]@=c3[NHN(3C[C2]n]1CC1C-(C
add S at position 6,C6IcNCSln]@=c3[NHN(3C[C2]n]1CC1C-(C
remove @ from position 10,C6IcNCSln]=c3[NHN(3C[C2]n]1CC1C-(C
remove ( from position 17,C6IcNCSln]=c3[NHN3C[C2]n]1CC1C-(C
replace C at position 20 with l,C6IcNCSln]=c3[NHN3C[l2]n]1CC1C-(C
replace N at position 4 with ),C6Ic)CSln]=c3[NHN3C[l2]n]1CC1C-(C
remove c from position 3,C6I)CSln]=c3[NHN3C[l2]n]1CC1C-(C
replace I at position 2 with 2,C62)CSln]=c3[NHN3C[l2]n]1CC1C-(C
remove S from position 5,C62)Cln]=c3[NHN3C[l2]n]1CC1C-(C
replace C at position 30 with [,C62)Cln]=c3[NHN3C[l2]n]1CC1C-([
replace c at position 9 with @,C62)Cln]=@3[NHN3C[l2]n]1CC1C-([
add n at position 16,C62)Cln]=@3[NHN3nC[l2]n]1CC1C-([
add s at position 27,C62)Cln]=@3[NHN3nC[l2]n]1CCs1C-([
add 1 at position 25,C62)Cln]=@3[NHN3nC[l2]n]11CCs1C-([
remove 1 from position 29,C62)Cln]=@3[NHN3nC[l2]n]11CCsC-([
remove ] from position 23,C62)Cln]=@3[NHN3nC[l2]n11CCsC-([
replace H at position 13 with C,C62)Cln]=@3[NCN3nC[l2]n11CCsC-([
replace C at position 26 with /,C62)Cln]=@3[NCN3nC[l2]n11C/sC-([
replace C at position 13 with 4,C62)Cln]=@3[N4N3nC[l2]n11C/sC-([
add F at position 22,C62)Cln]=@3[N4N3nC[l2]Fn11C/sC-([
add 5 at position 30,C62)Cln]=@3[N4N3nC[l2]Fn11C/sC5-([
add I at position 3,C62I)Cln]=@3[N4N3nC[l2]Fn11C/sC5-([
add l at position 9,C62I)Cln]l=@3[N4N3nC[l2]Fn11C/sC5-([
remove [ from position 20,C62I)Cln]l=@3[N4N3nCl2]Fn11C/sC5-([
add N at position 3,C62NI)Cln]l=@3[N4N3nCl2]Fn11C/sC5-([
add r at position 9,C62NI)Clnr]l=@3[N4N3nCl2]Fn11C/sC5-([
add 7 at position 27,C62NI)Clnr]l=@3[N4N3nCl2]Fn711C/sC5-([
remove @ from position 13,C62NI)Clnr]l=3[N4N3nCl2]Fn711C/sC5-([
replace F at position 24 with s,C62NI)Clnr]l=3[N4N3nCl2]sn711C/sC5-([
replace N at position 17 with F,C62NI)Clnr]l=3[N4F3nCl2]sn711C/sC5-([
remove n from position 19,C62NI)Clnr]l=3[N4F3Cl2]sn711C/sC5-([
remove r from position 9,C62NI)Cln]l=3[N4F3Cl2]sn711C/sC5-([
replace ) at position 5 with c,C62NIcCln]l=3[N4F3Cl2]sn711C/sC5-([
remove 1 from position 26,C62NIcCln]l=3[N4F3Cl2]sn71C/sC5-([
remove 3 from position 17,C62NIcCln]l=3[N4FCl2]sn71C/sC5-([
remove 1 from position 24,C62NIcCln]l=3[N4FCl2]sn7C/sC5-([
remove 2 from position 19,C62NIcCln]l=3[N4FCl]sn7C/sC5-([
remove l from position 18,C62NIcCln]l=3[N4FC]sn7C/sC5-([
remove = from position 11,C62NIcCln]l3[N4FC]sn7C/sC5-([
replace 4 at position 14 with 2,C62NIcCln]l3[N2FC]sn7C/sC5-([
replace c at position 5 with 7,C62NI7Cln]l3[N2FC]sn7C/sC5-([
remove 2 from position 2,C6NI7Cln]l3[N2FC]sn7C/sC5-([
remove l from position 9,C6NI7Cln]3[N2FC]sn7C/sC5-([
remove s from position 21,C6NI7Cln]3[N2FC]sn7C/C5-([
replace C at position 5 with 7,C6NI77ln]3[N2FC]sn7C/C5-([
remove 7 from position 5,C6NI7ln]3[N2FC]sn7C/C5-([
remove 2 from position 11,C6NI7ln]3[NFC]sn7C/C5-([
remove F from position 11,C6NI7ln]3[NC]sn7C/C5-([
add r at position 16,C6NI7ln]3[NC]sn7rC/C5-([
remove - from position 21,C6NI7ln]3[NC]sn7rC/C5([
remove C from position 0,6NI7ln]3[NC]sn7rC/C5([
replace 7 at position 14 with S,6NI7ln]3[NC]snSrC/C5([
replace 6 at position 0 with 7,7NI7ln]3[NC]snSrC/C5([
replace r at position 15 with ),7NI7ln]3[NC]snS)C/C5([
replace C at position 18 with 5,7NI7ln]3[NC]snS)C/55([
replace N at position 9 with 4,7NI7ln]3[4C]snS)C/55([
remove 5 from position 18,7NI7ln]3[4C]snS)C/5([
remove 4 from position 9,7NI7ln]3[C]snS)C/5([
remove / from position 16,7NI7ln]3[C]snS)C5([
replace [ at position 8 with ),7NI7ln]3)C]snS)C5([
remove ] from position 6,7NI7ln3)C]snS)C5([
remove s from position 10,7NI7ln3)C]nS)C5([
replace 5 at position 14 with 3,7NI7ln3)C]nS)C3([
add H at position 16,7NI7ln3)C]nS)C3(H[
remove 7 from position 3,7NIln3)C]nS)C3(H[
replace ] at position 8 with 6,7NIln3)C6nS)C3(H[
add 7 at position 0,77NIln3)C6nS)C3(H[
add 1 at position 1,717NIln3)C6nS)C3(H[
remove 6 from position 10,717NIln3)CnS)C3(H[
remove C from position 13,717NIln3)CnS)3(H[
replace 7 at position 2 with o,71oNIln3)CnS)3(H[
replace C at position 9 with 3,71oNIln3)3nS)3(H[
remove 3 from position 7,71oNIln)3nS)3(H[
remove o from position 2,71NIln)3nS)3(H[
remove ( from position 12,71NIln)3nS)3H[
add 5 at position 9,71NIln)3n5S)3H[
replace I at position 3 with /,71N/ln)3n5S)3H[
remove S from position 10,71N/ln)3n5)3H[
replace N at position 2 with r,71r/ln)3n5)3H[
remove 5 from position 9,71r/ln)3n)3H[
add s at position 8,71r/ln)3sn)3H[
replace 3 at position 11 with /,71r/ln)3sn)/H[
replace H at position 12 with C,71r/ln)3sn)/C[
remove ) from position 6,71r/ln3sn)/C[
replace n at position 5 with -,71r/l-3sn)/C[
remove n from position 8,71r/l-3s)/C[
remove / from position 9,71r/l-3s)C[
remove s from position 7,71r/l-3)C[
add r at position 1,7r1r/l-3)C[
remove [ from position 10,7r1r/l-3)C
remove 7 from position 0,r1r/l-3)C
remove 1 from position 1,rr/l-3)C
remove l from position 3,rr/-3)C
remove 3 from position 4,rr/-)C
remove - from position 3,rr/)C
remove r from position 0,r/)C
add l at position 0,lr/)C
remove / from position 2,lr)C
add S at position 1,lSr)C
replace l at position 0 with o,oSr)C
remove C from position 4,oSr)
remove S from position 1,or)
add 1 at position 0,1or)
replace o at position 1 with C,1Cr)
replace r at position 2 with N,1CN)
remove ) from position 3,1CN
remove N from position 2,1C
remove C from position 1,1
remove 1 from position 0,
final: ,
