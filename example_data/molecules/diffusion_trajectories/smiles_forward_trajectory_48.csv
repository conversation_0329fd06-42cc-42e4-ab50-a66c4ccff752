log,state
initialize: O=C(Cc1csc(NC(=O)Nc2ccc(Cl)cc2)n1)NCCc1ccc(Cl)cc1,O=C(Cc1csc(NC(=O)Nc2ccc(Cl)cc2)n1)NCCc1ccc(Cl)cc1
replace c at position 28 with +,O=C(Cc1csc(NC(=O)Nc2ccc(Cl)c+2)n1)NCCc1ccc(Cl)cc1
add ( at position 37,O=C(Cc1csc(NC(=O)Nc2ccc(Cl)c+2)n1)NCC(c1ccc(Cl)cc1
remove c from position 5,O=C(C1csc(NC(=O)Nc2ccc(Cl)c+2)n1)NCC(c1ccc(Cl)cc1
add 2 at position 22,O=C(C1csc(NC(=O)Nc2ccc2(Cl)c+2)n1)NCC(c1ccc(Cl)cc1
add # at position 50,O=C(C1csc(NC(=O)Nc2ccc2(Cl)c+2)n1)NCC(c1ccc(Cl)cc1#
remove 1 from position 39,O=C(C1csc(NC(=O)Nc2ccc2(Cl)c+2)n1)NCC(cccc(Cl)cc1#
remove 2 from position 29,O=C(C1csc(NC(=O)Nc2ccc2(Cl)c+)n1)NCC(cccc(Cl)cc1#
add S at position 11,O=C(C1csc(NSC(=O)Nc2ccc2(Cl)c+)n1)NCC(cccc(Cl)cc1#
add C at position 0,CO=C(C1csc(NSC(=O)Nc2ccc2(Cl)c+)n1)NCC(cccc(Cl)cc1#
remove = from position 15,CO=C(C1csc(NSC(O)Nc2ccc2(Cl)c+)n1)NCC(cccc(Cl)cc1#
add r at position 34,CO=C(C1csc(NSC(O)Nc2ccc2(Cl)c+)n1)rNCC(cccc(Cl)cc1#
remove ( from position 38,CO=C(C1csc(NSC(O)Nc2ccc2(Cl)c+)n1)rNCCcccc(Cl)cc1#
remove 1 from position 32,CO=C(C1csc(NSC(O)Nc2ccc2(Cl)c+)n)rNCCcccc(Cl)cc1#
remove N from position 17,CO=C(C1csc(NSC(O)c2ccc2(Cl)c+)n)rNCCcccc(Cl)cc1#
replace ( at position 4 with O,CO=COC1csc(NSC(O)c2ccc2(Cl)c+)n)rNCCcccc(Cl)cc1#
remove 2 from position 22,CO=COC1csc(NSC(O)c2ccc(Cl)c+)n)rNCCcccc(Cl)cc1#
remove l from position 41,CO=COC1csc(NSC(O)c2ccc(Cl)c+)n)rNCCcccc(C)cc1#
replace n at position 29 with r,CO=COC1csc(NSC(O)c2ccc(Cl)c+)r)rNCCcccc(C)cc1#
remove O from position 1,C=COC1csc(NSC(O)c2ccc(Cl)c+)r)rNCCcccc(C)cc1#
replace C at position 4 with #,C=CO#1csc(NSC(O)c2ccc(Cl)c+)r)rNCCcccc(C)cc1#
remove r from position 30,C=CO#1csc(NSC(O)c2ccc(Cl)c+)r)NCCcccc(C)cc1#
remove N from position 10,C=CO#1csc(SC(O)c2ccc(Cl)c+)r)NCCcccc(C)cc1#
add B at position 37,C=CO#1csc(SC(O)c2ccc(Cl)c+)r)NCCcccc(BC)cc1#
add 1 at position 35,C=CO#1csc(SC(O)c2ccc(Cl)c+)r)NCCccc1c(BC)cc1#
add ] at position 22,C=CO#1csc(SC(O)c2ccc(C]l)c+)r)NCCccc1c(BC)cc1#
add - at position 33,C=CO#1csc(SC(O)c2ccc(C]l)c+)r)NCC-ccc1c(BC)cc1#
remove ) from position 29,C=CO#1csc(SC(O)c2ccc(C]l)c+)rNCC-ccc1c(BC)cc1#
add O at position 17,C=CO#1csc(SC(O)c2Occc(C]l)c+)rNCC-ccc1c(BC)cc1#
replace C at position 11 with 3,C=CO#1csc(S3(O)c2Occc(C]l)c+)rNCC-ccc1c(BC)cc1#
add ] at position 9,C=CO#1csc](S3(O)c2Occc(C]l)c+)rNCC-ccc1c(BC)cc1#
replace C at position 2 with I,C=IO#1csc](S3(O)c2Occc(C]l)c+)rNCC-ccc1c(BC)cc1#
remove ( from position 22,C=IO#1csc](S3(O)c2OcccC]l)c+)rNCC-ccc1c(BC)cc1#
remove 1 from position 37,C=IO#1csc](S3(O)c2OcccC]l)c+)rNCC-cccc(BC)cc1#
replace N at position 30 with (,C=IO#1csc](S3(O)c2OcccC]l)c+)r(CC-cccc(BC)cc1#
replace ( at position 30 with o,C=IO#1csc](S3(O)c2OcccC]l)c+)roCC-cccc(BC)cc1#
replace ( at position 10 with B,C=IO#1csc]BS3(O)c2OcccC]l)c+)roCC-cccc(BC)cc1#
remove - from position 33,C=IO#1csc]BS3(O)c2OcccC]l)c+)roCCcccc(BC)cc1#
replace O at position 18 with 3,C=IO#1csc]BS3(O)c23cccC]l)c+)roCCcccc(BC)cc1#
add = at position 11,C=IO#1csc]B=S3(O)c23cccC]l)c+)roCCcccc(BC)cc1#
remove s from position 7,C=IO#1cc]B=S3(O)c23cccC]l)c+)roCCcccc(BC)cc1#
replace c at position 6 with n,C=IO#1nc]B=S3(O)c23cccC]l)c+)roCCcccc(BC)cc1#
replace l at position 24 with n,C=IO#1nc]B=S3(O)c23cccC]n)c+)roCCcccc(BC)cc1#
replace c at position 16 with N,C=IO#1nc]B=S3(O)N23cccC]n)c+)roCCcccc(BC)cc1#
add S at position 6,C=IO#1Snc]B=S3(O)N23cccC]n)c+)roCCcccc(BC)cc1#
remove B from position 10,C=IO#1Snc]=S3(O)N23cccC]n)c+)roCCcccc(BC)cc1#
remove 2 from position 17,C=IO#1Snc]=S3(O)N3cccC]n)c+)roCCcccc(BC)cc1#
replace c at position 20 with l,C=IO#1Snc]=S3(O)N3cclC]n)c+)roCCcccc(BC)cc1#
replace # at position 4 with +,C=IO+1Snc]=S3(O)N3cclC]n)c+)roCCcccc(BC)cc1#
remove O from position 3,C=I+1Snc]=S3(O)N3cclC]n)c+)roCCcccc(BC)cc1#
replace I at position 2 with 2,C=2+1Snc]=S3(O)N3cclC]n)c+)roCCcccc(BC)cc1#
remove S from position 5,C=2+1nc]=S3(O)N3cclC]n)c+)roCCcccc(BC)cc1#
replace ] at position 20 with 4,C=2+1nc]=S3(O)N3cclC4n)c+)roCCcccc(BC)cc1#
replace 1 at position 4 with I,C=2+Inc]=S3(O)N3cclC4n)c+)roCCcccc(BC)cc1#
remove I from position 4,C=2+nc]=S3(O)N3cclC4n)c+)roCCcccc(BC)cc1#
remove ) from position 12,C=2+nc]=S3(ON3cclC4n)c+)roCCcccc(BC)cc1#
add S at position 12,C=2+nc]=S3(OSN3cclC4n)c+)roCCcccc(BC)cc1#
replace c at position 29 with C,C=2+nc]=S3(OSN3cclC4n)c+)roCCCccc(BC)cc1#
add r at position 23,C=2+nc]=S3(OSN3cclC4n)cr+)roCCCccc(BC)cc1#
replace c at position 32 with r,C=2+nc]=S3(OSN3cclC4n)cr+)roCCCcrc(BC)cc1#
remove O from position 11,C=2+nc]=S3(SN3cclC4n)cr+)roCCCcrc(BC)cc1#
replace 3 at position 13 with 5,C=2+nc]=S3(SN5cclC4n)cr+)roCCCcrc(BC)cc1#
add F at position 22,C=2+nc]=S3(SN5cclC4n)cFr+)roCCCcrc(BC)cc1#
add B at position 35,C=2+nc]=S3(SN5cclC4n)cFr+)roCCCcrc(BBC)cc1#
remove S from position 8,C=2+nc]=3(SN5cclC4n)cFr+)roCCCcrc(BBC)cc1#
add r at position 10,C=2+nc]=3(rSN5cclC4n)cFr+)roCCCcrc(BBC)cc1#
add ) at position 34,C=2+nc]=3(rSN5cclC4n)cFr+)roCCCcrc)(BBC)cc1#
remove r from position 10,C=2+nc]=3(SN5cclC4n)cFr+)roCCCcrc)(BBC)cc1#
add ( at position 43,C=2+nc]=3(SN5cclC4n)cFr+)roCCCcrc)(BBC)cc1#(
remove ( from position 9,C=2+nc]=3SN5cclC4n)cFr+)roCCCcrc)(BBC)cc1#(
replace 2 at position 2 with @,C=@+nc]=3SN5cclC4n)cFr+)roCCCcrc)(BBC)cc1#(
remove F from position 20,C=@+nc]=3SN5cclC4n)cr+)roCCCcrc)(BBC)cc1#(
add r at position 24,C=@+nc]=3SN5cclC4n)cr+)rroCCCcrc)(BBC)cc1#(
replace n at position 17 with F,C=@+nc]=3SN5cclC4F)cr+)rroCCCcrc)(BBC)cc1#(
remove c from position 19,C=@+nc]=3SN5cclC4F)r+)rroCCCcrc)(BBC)cc1#(
remove ) from position 36,C=@+nc]=3SN5cclC4F)r+)rroCCCcrc)(BBCcc1#(
replace B at position 34 with ),C=@+nc]=3SN5cclC4F)r+)rroCCCcrc)(B)Ccc1#(
replace F at position 17 with =,C=@+nc]=3SN5cclC4=)r+)rroCCCcrc)(B)Ccc1#(
remove c from position 36,C=@+nc]=3SN5cclC4=)r+)rroCCCcrc)(B)Cc1#(
remove = from position 7,C=@+nc]3SN5cclC4=)r+)rroCCCcrc)(B)Cc1#(
remove ) from position 17,C=@+nc]3SN5cclC4=r+)rroCCCcrc)(B)Cc1#(
add 7 at position 37,C=@+nc]3SN5cclC4=r+)rroCCCcrc)(B)Cc1#7(
add 2 at position 28,C=@+nc]3SN5cclC4=r+)rroCCCcr2c)(B)Cc1#7(
replace 5 at position 10 with =,C=@+nc]3SN=cclC4=r+)rroCCCcr2c)(B)Cc1#7(
remove n from position 4,C=@+c]3SN=cclC4=r+)rroCCCcr2c)(B)Cc1#7(
remove r from position 19,C=@+c]3SN=cclC4=r+)roCCCcr2c)(B)Cc1#7(
remove B from position 30,C=@+c]3SN=cclC4=r+)roCCCcr2c)()Cc1#7(
add / at position 30,C=@+c]3SN=cclC4=r+)roCCCcr2c)(/)Cc1#7(
remove C from position 23,C=@+c]3SN=cclC4=r+)roCCcr2c)(/)Cc1#7(
remove C from position 22,C=@+c]3SN=cclC4=r+)roCcr2c)(/)Cc1#7(
add r at position 33,C=@+c]3SN=cclC4=r+)roCcr2c)(/)Cc1r#7(
remove C from position 30,C=@+c]3SN=cclC4=r+)roCcr2c)(/)c1r#7(
remove ) from position 29,C=@+c]3SN=cclC4=r+)roCcr2c)(/c1r#7(
remove 3 from position 6,C=@+c]SN=cclC4=r+)roCcr2c)(/c1r#7(
add ) at position 30,C=@+c]SN=cclC4=r+)roCcr2c)(/c1)r#7(
replace c at position 21 with 6,C=@+c]SN=cclC4=r+)roC6r2c)(/c1)r#7(
replace r at position 18 with 4,C=@+c]SN=cclC4=r+)4oC6r2c)(/c1)r#7(
remove 6 from position 21,C=@+c]SN=cclC4=r+)4oCr2c)(/c1)r#7(
remove 4 from position 18,C=@+c]SN=cclC4=r+)oCr2c)(/c1)r#7(
remove ( from position 32,C=@+c]SN=cclC4=r+)oCr2c)(/c1)r#7
replace ) at position 17 with +,C=@+c]SN=cclC4=r++oCr2c)(/c1)r#7
remove 4 from position 13,C=@+c]SN=cclC=r++oCr2c)(/c1)r#7
remove c from position 10,C=@+c]SN=clC=r++oCr2c)(/c1)r#7
replace + at position 14 with 4,C=@+c]SN=clC=r4+oCr2c)(/c1)r#7
add H at position 16,C=@+c]SN=clC=r4+HoCr2c)(/c1)r#7
remove + from position 3,C=@c]SN=clC=r4+HoCr2c)(/c1)r#7
replace c at position 8 with 6,C=@c]SN=6lC=r4+HoCr2c)(/c1)r#7
add 7 at position 0,7C=@c]SN=6lC=r4+HoCr2c)(/c1)r#7
add 1 at position 2,7C1=@c]SN=6lC=r4+HoCr2c)(/c1)r#7
remove r from position 20,7C1=@c]SN=6lC=r4+HoC2c)(/c1)r#7
remove = from position 13,7C1=@c]SN=6lCr4+HoC2c)(/c1)r#7
replace 7 at position 29 with ),7C1=@c]SN=6lCr4+HoC2c)(/c1)r#)
replace = at position 9 with 3,7C1=@c]SN36lCr4+HoC2c)(/c1)r#)
remove ) from position 29,7C1=@c]SN36lCr4+HoC2c)(/c1)r#
remove l from position 11,7C1=@c]SN36Cr4+HoC2c)(/c1)r#
remove ) from position 20,7C1=@c]SN36Cr4+HoC2c(/c1)r#
replace 7 at position 0 with S,SC1=@c]SN36Cr4+HoC2c(/c1)r#
remove 2 from position 18,SC1=@c]SN36Cr4+HoCc(/c1)r#
add B at position 15,SC1=@c]SN36Cr4+BHoCc(/c1)r#
remove / from position 21,SC1=@c]SN36Cr4+BHoCc(c1)r#
replace @ at position 4 with r,SC1=rc]SN36Cr4+BHoCc(c1)r#
remove c from position 19,SC1=rc]SN36Cr4+BHoC(c1)r#
add s at position 17,SC1=rc]SN36Cr4+BHsoC(c1)r#
replace 1 at position 22 with /,SC1=rc]SN36Cr4+BHsoC(c/)r#
replace r at position 24 with C,SC1=rc]SN36Cr4+BHsoC(c/)C#
remove r from position 12,SC1=rc]SN36C4+BHsoC(c/)C#
replace 6 at position 10 with -,SC1=rc]SN3-C4+BHsoC(c/)C#
remove o from position 17,SC1=rc]SN3-C4+BHsC(c/)C#
remove c from position 19,SC1=rc]SN3-C4+BHsC(/)C#
remove B from position 14,SC1=rc]SN3-C4+HsC(/)C#
add r at position 2,SCr1=rc]SN3-C4+HsC(/)C#
remove C from position 21,SCr1=rc]SN3-C4+HsC(/)#
remove C from position 1,Sr1=rc]SN3-C4+HsC(/)#
remove 1 from position 2,Sr=rc]SN3-C4+HsC(/)#
remove # from position 19,Sr=rc]SN3-C4+HsC(/)
replace 4 at position 11 with F,Sr=rc]SN3-CF+HsC(/)
remove + from position 12,Sr=rc]SN3-CFHsC(/)
remove r from position 1,S=rc]SN3-CFHsC(/)
add l at position 2,S=lrc]SN3-CFHsC(/)
remove C from position 10,S=lrc]SN3-FHsC(/)
add S at position 7,S=lrc]SSN3-FHsC(/)
replace S at position 0 with o,o=lrc]SSN3-FHsC(/)
remove F from position 11,o=lrc]SSN3-HsC(/)
remove l from position 2,o=rc]SSN3-HsC(/)
remove S from position 6,o=rc]SN3-HsC(/)
replace / at position 13 with N,o=rc]SN3-HsC(N)
remove C from position 11,o=rc]SN3-Hs(N)
add O at position 2,o=Orc]SN3-Hs(N)
replace H at position 10 with ],o=Orc]SN3-]s(N)
add 6 at position 14,o=Orc]SN3-]s(N6)
remove s from position 11,o=Orc]SN3-](N6)
remove S from position 6,o=Orc]N3-](N6)
replace r at position 3 with 4,o=O4c]N3-](N6)
add H at position 5,o=O4cH]N3-](N6)
replace = at position 1 with H,oHO4cH]N3-](N6)
remove 6 from position 13,oHO4cH]N3-](N)
add H at position 9,oHO4cH]N3H-](N)
remove O from position 2,oH4cH]N3H-](N)
remove ] from position 5,oH4cHN3H-](N)
replace H at position 4 with [,oH4c[N3H-](N)
remove 4 from position 2,oHc[N3H-](N)
remove c from position 2,oH[N3H-](N)
replace ( at position 8 with [,oH[N3H-][N)
remove N from position 3,oH[3H-][N)
remove [ from position 2,oH3H-][N)
remove [ from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
