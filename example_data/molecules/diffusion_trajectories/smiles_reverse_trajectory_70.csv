log,state
initialize: ,
add O at position 0,O
add r at position 0,r<PERSON>
add I at position 2,r<PERSON><PERSON>
add ( at position 2,rO(I
add 2 at position 1,r2O(I
replace O at position 2 with (,r2((I
add [ at position 0,[r2((I
add c at position 2,[rc2((I
add C at position 6,[rc2((CI
add 2 at position 2,[r2c2((CI
remove 2 from position 4,[r2c((CI
replace I at position 7 with C,[r2c((CC
replace 2 at position 2 with c,[rcc((CC
replace [ at position 0 with =,=rcc((CC
remove = from position 0,rcc((CC
replace C at position 5 with ],rcc((]C
remove ( from position 4,rcc(]C
remove C from position 5,rcc(]
remove ( from position 3,rcc]
remove c from position 1,rc]
replace c at position 1 with o,ro]
remove r from position 0,o]
replace o at position 0 with -,-]
add 7 at position 0,7-]
add r at position 0,r7-]
replace 7 at position 1 with O,rO-]
remove r from position 0,O-]
add 3 at position 0,3O-]
add = at position 2,3O=-]
add F at position 5,3O=-]F
remove ] from position 4,3O=-F
replace F at position 4 with 3,3O=-3
add l at position 2,3Ol=-3
replace = at position 3 with ],3Ol]-3
add # at position 5,3Ol]-#3
remove l from position 2,3O]-#3
remove # from position 4,3O]-3
replace O at position 1 with C,3C]-3
add o at position 4,3C]-o3
add 1 at position 3,3C]1-o3
replace ] at position 2 with B,3CB1-o3
remove - from position 4,3CB1o3
remove 1 from position 3,3CBo3
replace 3 at position 4 with C,3CBoC
remove 3 from position 0,CBoC
replace B at position 1 with ),C)oC
replace ) at position 1 with 5,C5oC
add [ at position 2,C5[oC
replace o at position 3 with +,C5[+C
add = at position 2,C5=[+C
add N at position 2,C5N=[+C
replace C at position 0 with /,/5N=[+C
add 7 at position 2,/57N=[+C
remove 7 from position 2,/5N=[+C
remove C from position 6,/5N=[+
add r at position 6,/5N=[+r
replace + at position 5 with #,/5N=[#r
add r at position 7,/5N=[#rr
add H at position 8,/5N=[#rrH
add l at position 3,/5Nl=[#rrH
add 4 at position 7,/5Nl=[#4rrH
remove l from position 3,/5N=[#4rrH
add ( at position 1,/(5N=[#4rrH
add c at position 1,/c(5N=[#4rrH
remove # from position 7,/c(5N=[4rrH
replace H at position 10 with 1,/c(5N=[4rr1
remove r from position 8,/c(5N=[4r1
replace ( at position 2 with /,/c/5N=[4r1
replace / at position 2 with C,/cC5N=[4r1
add F at position 3,/cCF5N=[4r1
add 5 at position 9,/cCF5N=[45r1
replace = at position 6 with #,/cCF5N#[45r1
replace 4 at position 8 with +,/cCF5N#[+5r1
add H at position 8,/cCF5N#[H+5r1
add 3 at position 9,/cCF5N#[H3+5r1
remove 1 from position 13,/cCF5N#[H3+5r
replace 3 at position 9 with ),/cCF5N#[H)+5r
replace 5 at position 11 with r,/cCF5N#[H)+rr
add 6 at position 9,/cCF5N#[H6)+rr
remove # from position 6,/cCF5N[H6)+rr
add 4 at position 10,/cCF5N[H6)4+rr
replace [ at position 6 with 4,/cCF5N4H6)4+rr
add 7 at position 4,/cCF75N4H6)4+rr
add + at position 13,/cCF75N4H6)4++rr
remove c from position 1,/CF75N4H6)4++rr
replace N at position 5 with F,/CF75F4H6)4++rr
remove / from position 0,CF75F4H6)4++rr
add H at position 14,CF75F4H6)4++rrH
replace 7 at position 2 with I,CFI5F4H6)4++rrH
remove I from position 2,CF5F4H6)4++rrH
replace ) at position 7 with 7,CF5F4H674++rrH
replace F at position 1 with o,Co5F4H674++rrH
add 3 at position 7,Co5F4H6374++rrH
add ( at position 14,Co5F4H6374++rr(H
add n at position 6,Co5F4Hn6374++rr(H
add [ at position 11,Co5F4Hn6374[++rr(H
add / at position 9,Co5F4Hn63/74[++rr(H
replace 5 at position 2 with 6,Co6F4Hn63/74[++rr(H
replace 3 at position 8 with (,Co6F4Hn6(/74[++rr(H
add + at position 4,Co6F+4Hn6(/74[++rr(H
replace + at position 15 with c,Co6F+4Hn6(/74[+crr(H
replace + at position 4 with l,Co6Fl4Hn6(/74[+crr(H
add C at position 12,Co6Fl4Hn6(/7C4[+crr(H
add [ at position 21,Co6Fl4Hn6(/7C4[+crr(H[
add N at position 11,Co6Fl4Hn6(/N7C4[+crr(H[
add B at position 23,Co6Fl4Hn6(/N7C4[+crr(H[B
add r at position 15,Co6Fl4Hn6(/N7C4r[+crr(H[B
add o at position 19,Co6Fl4Hn6(/N7C4r[+corr(H[B
replace C at position 13 with H,Co6Fl4Hn6(/N7H4r[+corr(H[B
remove H from position 23,Co6Fl4Hn6(/N7H4r[+corr([B
add H at position 23,Co6Fl4Hn6(/N7H4r[+corr(H[B
add [ at position 6,Co6Fl4[Hn6(/N7H4r[+corr(H[B
replace [ at position 17 with 2,Co6Fl4[Hn6(/N7H4r2+corr(H[B
add 4 at position 5,Co6Fl44[Hn6(/N7H4r2+corr(H[B
add ( at position 5,Co6Fl(44[Hn6(/N7H4r2+corr(H[B
replace [ at position 8 with C,Co6Fl(44CHn6(/N7H4r2+corr(H[B
add O at position 11,Co6Fl(44CHnO6(/N7H4r2+corr(H[B
add - at position 5,Co6Fl-(44CHnO6(/N7H4r2+corr(H[B
remove H from position 18,Co6Fl-(44CHnO6(/N74r2+corr(H[B
add H at position 27,Co6Fl-(44CHnO6(/N74r2+corr(HH[B
replace F at position 3 with o,Co6ol-(44CHnO6(/N74r2+corr(HH[B
remove H from position 10,Co6ol-(44CnO6(/N74r2+corr(HH[B
replace 4 at position 7 with =,Co6ol-(=4CnO6(/N74r2+corr(HH[B
add S at position 13,Co6ol-(=4CnO6S(/N74r2+corr(HH[B
add 4 at position 23,Co6ol-(=4CnO6S(/N74r2+c4orr(HH[B
add ( at position 29,Co6ol-(=4CnO6S(/N74r2+c4orr(H(H[B
replace N at position 16 with s,Co6ol-(=4CnO6S(/s74r2+c4orr(H(H[B
replace C at position 9 with [,Co6ol-(=4[nO6S(/s74r2+c4orr(H(H[B
add C at position 13,Co6ol-(=4[nO6CS(/s74r2+c4orr(H(H[B
add 5 at position 4,Co6o5l-(=4[nO6CS(/s74r2+c4orr(H(H[B
add c at position 23,Co6o5l-(=4[nO6CS(/s74r2c+c4orr(H(H[B
replace o at position 1 with 1,C16o5l-(=4[nO6CS(/s74r2c+c4orr(H(H[B
remove S from position 15,C16o5l-(=4[nO6C(/s74r2c+c4orr(H(H[B
add l at position 21,C16o5l-(=4[nO6C(/s74rl2c+c4orr(H(H[B
remove l from position 5,C16o5-(=4[nO6C(/s74rl2c+c4orr(H(H[B
replace ( at position 31 with 7,C16o5-(=4[nO6C(/s74rl2c+c4orr(H7H[B
replace [ at position 33 with 5,C16o5-(=4[nO6C(/s74rl2c+c4orr(H7H5B
remove 6 from position 12,C16o5-(=4[nOC(/s74rl2c+c4orr(H7H5B
remove 5 from position 4,C16o-(=4[nOC(/s74rl2c+c4orr(H7H5B
replace r at position 26 with 5,C16o-(=4[nOC(/s74rl2c+c4or5(H7H5B
add S at position 23,C16o-(=4[nOC(/s74rl2c+cS4or5(H7H5B
replace ( at position 28 with ),C16o-(=4[nOC(/s74rl2c+cS4or5)H7H5B
replace [ at position 8 with (,C16o-(=4(nOC(/s74rl2c+cS4or5)H7H5B
replace o at position 25 with [,C16o-(=4(nOC(/s74rl2c+cS4[r5)H7H5B
add 6 at position 32,C16o-(=4(nOC(/s74rl2c+cS4[r5)H7H65B
add 1 at position 15,C16o-(=4(nOC(/s174rl2c+cS4[r5)H7H65B
replace - at position 4 with c,C16oc(=4(nOC(/s174rl2c+cS4[r5)H7H65B
add 7 at position 8,C16oc(=47(nOC(/s174rl2c+cS4[r5)H7H65B
replace / at position 14 with C,C16oc(=47(nOC(Cs174rl2c+cS4[r5)H7H65B
add = at position 21,C16oc(=47(nOC(Cs174rl=2c+cS4[r5)H7H65B
add 7 at position 1,C716oc(=47(nOC(Cs174rl=2c+cS4[r5)H7H65B
remove ) from position 32,C716oc(=47(nOC(Cs174rl=2c+cS4[r5H7H65B
add ) at position 37,C716oc(=47(nOC(Cs174rl=2c+cS4[r5H7H65)B
replace 4 at position 19 with 1,C716oc(=47(nOC(Cs171rl=2c+cS4[r5H7H65)B
replace o at position 4 with C,C716Cc(=47(nOC(Cs171rl=2c+cS4[r5H7H65)B
add r at position 27,C716Cc(=47(nOC(Cs171rl=2c+crS4[r5H7H65)B
add = at position 20,C716Cc(=47(nOC(Cs171=rl=2c+crS4[r5H7H65)B
remove 1 from position 2,C76Cc(=47(nOC(Cs171=rl=2c+crS4[r5H7H65)B
remove 7 from position 1,C6Cc(=47(nOC(Cs171=rl=2c+crS4[r5H7H65)B
replace 7 at position 16 with ),C6Cc(=47(nOC(Cs1)1=rl=2c+crS4[r5H7H65)B
add - at position 7,C6Cc(=4-7(nOC(Cs1)1=rl=2c+crS4[r5H7H65)B
remove H from position 33,C6Cc(=4-7(nOC(Cs1)1=rl=2c+crS4[r57H65)B
replace 4 at position 29 with +,C6Cc(=4-7(nOC(Cs1)1=rl=2c+crS+[r57H65)B
remove 5 from position 32,C6Cc(=4-7(nOC(Cs1)1=rl=2c+crS+[r7H65)B
add - at position 38,C6Cc(=4-7(nOC(Cs1)1=rl=2c+crS+[r7H65)B-
replace ) at position 17 with O,C6Cc(=4-7(nOC(Cs1O1=rl=2c+crS+[r7H65)B-
add 4 at position 32,C6Cc(=4-7(nOC(Cs1O1=rl=2c+crS+[r47H65)B-
add 4 at position 18,C6Cc(=4-7(nOC(Cs1O41=rl=2c+crS+[r47H65)B-
add 6 at position 37,C6Cc(=4-7(nOC(Cs1O41=rl=2c+crS+[r47H665)B-
replace 4 at position 18 with s,C6Cc(=4-7(nOC(Cs1Os1=rl=2c+crS+[r47H665)B-
replace 6 at position 37 with 2,C6Cc(=4-7(nOC(Cs1Os1=rl=2c+crS+[r47H625)B-
replace + at position 30 with ),C6Cc(=4-7(nOC(Cs1Os1=rl=2c+crS)[r47H625)B-
replace 6 at position 1 with I,CICc(=4-7(nOC(Cs1Os1=rl=2c+crS)[r47H625)B-
replace S at position 29 with C,CICc(=4-7(nOC(Cs1Os1=rl=2c+crC)[r47H625)B-
add C at position 1,CCICc(=4-7(nOC(Cs1Os1=rl=2c+crC)[r47H625)B-
add C at position 42,CCICc(=4-7(nOC(Cs1Os1=rl=2c+crC)[r47H625)BC-
remove r from position 33,CCICc(=4-7(nOC(Cs1Os1=rl=2c+crC)[47H625)BC-
add ] at position 22,CCICc(=4-7(nOC(Cs1Os1=]rl=2c+crC)[47H625)BC-
remove 6 from position 37,CCICc(=4-7(nOC(Cs1Os1=]rl=2c+crC)[47H25)BC-
remove B from position 40,CCICc(=4-7(nOC(Cs1Os1=]rl=2c+crC)[47H25)C-
add 4 at position 30,CCICc(=4-7(nOC(Cs1Os1=]rl=2c+c4rC)[47H25)C-
add ) at position 19,CCICc(=4-7(nOC(Cs1O)s1=]rl=2c+c4rC)[47H25)C-
add ( at position 4,CCIC(c(=4-7(nOC(Cs1O)s1=]rl=2c+c4rC)[47H25)C-
replace 7 at position 10 with N,CCIC(c(=4-N(nOC(Cs1O)s1=]rl=2c+c4rC)[47H25)C-
remove 2 from position 28,CCIC(c(=4-N(nOC(Cs1O)s1=]rl=c+c4rC)[47H25)C-
remove 7 from position 37,CCIC(c(=4-N(nOC(Cs1O)s1=]rl=c+c4rC)[4H25)C-
add F at position 17,CCIC(c(=4-N(nOC(CFs1O)s1=]rl=c+c4rC)[4H25)C-
add l at position 7,CCIC(c(l=4-N(nOC(CFs1O)s1=]rl=c+c4rC)[4H25)C-
add s at position 36,CCIC(c(l=4-N(nOC(CFs1O)s1=]rl=c+c4rCs)[4H25)C-
add ] at position 26,CCIC(c(l=4-N(nOC(CFs1O)s1=]]rl=c+c4rCs)[4H25)C-
replace c at position 5 with O,CCIC(O(l=4-N(nOC(CFs1O)s1=]]rl=c+c4rCs)[4H25)C-
add o at position 9,CCIC(O(l=o4-N(nOC(CFs1O)s1=]]rl=c+c4rCs)[4H25)C-
add ] at position 43,CCIC(O(l=o4-N(nOC(CFs1O)s1=]]rl=c+c4rCs)[4H]25)C-
add - at position 19,CCIC(O(l=o4-N(nOC(C-Fs1O)s1=]]rl=c+c4rCs)[4H]25)C-
add c at position 17,CCIC(O(l=o4-N(nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[4H]25)C-
add 5 at position 43,CCIC(O(l=o4-N(nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]25)C-
add s at position 47,CCIC(O(l=o4-N(nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]s25)C-
replace ( at position 13 with 5,CCIC(O(l=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]s25)C-
add c at position 48,CCIC(O(l=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]sc25)C-
add O at position 3,CCIOC(O(l=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]sc25)C-
add C at position 0,CCCIOC(O(l=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]sc25)C-
add @ at position 45,CCCIOC(O(l=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[@54H]sc25)C-
remove l from position 9,CCCIOC(O(=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[@54H]sc25)C-
remove I from position 3,CCCOC(O(=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[@54H]sc25)C-
add ] at position 21,CCCOC(O(=o4-N5nOCc(C-]Fs1O)s1=]]rl=c+c4rCs)[@54H]sc25)C-
remove s from position 49,CCCOC(O(=o4-N5nOCc(C-]Fs1O)s1=]]rl=c+c4rCs)[@54H]c25)C-
remove F from position 22,CCCOC(O(=o4-N5nOCc(C-]s1O)s1=]]rl=c+c4rCs)[@54H]c25)C-
replace 5 at position 13 with 3,CCCOC(O(=o4-N3nOCc(C-]s1O)s1=]]rl=c+c4rCs)[@54H]c25)C-
add C at position 11,CCCOC(O(=o4C-N3nOCc(C-]s1O)s1=]]rl=c+c4rCs)[@54H]c25)C-
replace r at position 32 with /,CCCOC(O(=o4C-N3nOCc(C-]s1O)s1=]]/l=c+c4rCs)[@54H]c25)C-
replace s at position 23 with =,CCCOC(O(=o4C-N3nOCc(C-]=1O)s1=]]/l=c+c4rCs)[@54H]c25)C-
add = at position 15,CCCOC(O(=o4C-N3=nOCc(C-]=1O)s1=]]/l=c+c4rCs)[@54H]c25)C-
add s at position 56,CCCOC(O(=o4C-N3=nOCc(C-]=1O)s1=]]/l=c+c4rCs)[@54H]c25)C-s
remove 1 from position 25,CCCOC(O(=o4C-N3=nOCc(C-]=O)s1=]]/l=c+c4rCs)[@54H]c25)C-s
remove s from position 27,CCCOC(O(=o4C-N3=nOCc(C-]=O)1=]]/l=c+c4rCs)[@54H]c25)C-s
remove n from position 16,CCCOC(O(=o4C-N3=OCc(C-]=O)1=]]/l=c+c4rCs)[@54H]c25)C-s
add r at position 29,CCCOC(O(=o4C-N3=OCc(C-]=O)1=]r]/l=c+c4rCs)[@54H]c25)C-s
remove 5 from position 44,CCCOC(O(=o4C-N3=OCc(C-]=O)1=]r]/l=c+c4rCs)[@4H]c25)C-s
remove l from position 32,CCCOC(O(=o4C-N3=OCc(C-]=O)1=]r]/=c+c4rCs)[@4H]c25)C-s
remove c from position 18,CCCOC(O(=o4C-N3=OC(C-]=O)1=]r]/=c+c4rCs)[@4H]c25)C-s
remove 1 from position 25,CCCOC(O(=o4C-N3=OC(C-]=O)=]r]/=c+c4rCs)[@4H]c25)C-s
replace ] at position 28 with -,CCCOC(O(=o4C-N3=OC(C-]=O)=]r-/=c+c4rCs)[@4H]c25)C-s
replace 5 at position 46 with C,CCCOC(O(=o4C-N3=OC(C-]=O)=]r-/=c+c4rCs)[@4H]c2C)C-s
replace o at position 9 with O,CCCOC(O(=O4C-N3=OC(C-]=O)=]r-/=c+c4rCs)[@4H]c2C)C-s
replace s at position 37 with 3,CCCOC(O(=O4C-N3=OC(C-]=O)=]r-/=c+c4rC3)[@4H]c2C)C-s
remove c from position 44,CCCOC(O(=O4C-N3=OC(C-]=O)=]r-/=c+c4rC3)[@4H]2C)C-s
add ( at position 22,CCCOC(O(=O4C-N3=OC(C-](=O)=]r-/=c+c4rC3)[@4H]2C)C-s
replace 4 at position 42 with #,CCCOC(O(=O4C-N3=OC(C-](=O)=]r-/=c+c4rC3)[@#H]2C)C-s
replace = at position 31 with (,CCCOC(O(=O4C-N3=OC(C-](=O)=]r-/(c+c4rC3)[@#H]2C)C-s
replace + at position 33 with c,CCCOC(O(=O4C-N3=OC(C-](=O)=]r-/(ccc4rC3)[@#H]2C)C-s
replace = at position 26 with C,CCCOC(O(=O4C-N3=OC(C-](=O)C]r-/(ccc4rC3)[@#H]2C)C-s
add C at position 7,CCCOC(OC(=O4C-N3=OC(C-](=O)C]r-/(ccc4rC3)[@#H]2C)C-s
add ( at position 28,CCCOC(OC(=O4C-N3=OC(C-](=O)C(]r-/(ccc4rC3)[@#H]2C)C-s
remove ] from position 46,CCCOC(OC(=O4C-N3=OC(C-](=O)C(]r-/(ccc4rC3)[@#H2C)C-s
replace r at position 38 with ),CCCOC(OC(=O4C-N3=OC(C-](=O)C(]r-/(ccc4)C3)[@#H2C)C-s
add 1 at position 50,CCCOC(OC(=O4C-N3=OC(C-](=O)C(]r-/(ccc4)C3)[@#H2C)C1-s
add @ at position 29,CCCOC(OC(=O4C-N3=OC(C-](=O)C(@]r-/(ccc4)C3)[@#H2C)C1-s
add 2 at position 21,CCCOC(OC(=O4C-N3=OC(C2-](=O)C(@]r-/(ccc4)C3)[@#H2C)C1-s
add ] at position 48,CCCOC(OC(=O4C-N3=OC(C2-](=O)C(@]r-/(ccc4)C3)[@#H]2C)C1-s
replace ( at position 29 with +,CCCOC(OC(=O4C-N3=OC(C2-](=O)C+@]r-/(ccc4)C3)[@#H]2C)C1-s
remove - from position 54,CCCOC(OC(=O4C-N3=OC(C2-](=O)C+@]r-/(ccc4)C3)[@#H]2C)C1s
replace 4 at position 11 with ),CCCOC(OC(=O)C-N3=OC(C2-](=O)C+@]r-/(ccc4)C3)[@#H]2C)C1s
remove O from position 17,CCCOC(OC(=O)C-N3=C(C2-](=O)C+@]r-/(ccc4)C3)[@#H]2C)C1s
add @ at position 29,CCCOC(OC(=O)C-N3=C(C2-](=O)C+@@]r-/(ccc4)C3)[@#H]2C)C1s
remove - from position 33,CCCOC(OC(=O)C-N3=C(C2-](=O)C+@@]r/(ccc4)C3)[@#H]2C)C1s
remove ] from position 22,CCCOC(OC(=O)C-N3=C(C2-(=O)C+@@]r/(ccc4)C3)[@#H]2C)C1s
remove - from position 13,CCCOC(OC(=O)CN3=C(C2-(=O)C+@@]r/(ccc4)C3)[@#H]2C)C1s
remove / from position 31,CCCOC(OC(=O)CN3=C(C2-(=O)C+@@]r(ccc4)C3)[@#H]2C)C1s
add 1 at position 3,CCC1OC(OC(=O)CN3=C(C2-(=O)C+@@]r(ccc4)C3)[@#H]2C)C1s
add C at position 42,CCC1OC(OC(=O)CN3=C(C2-(=O)C+@@]r(ccc4)C3)[C@#H]2C)C1s
remove - from position 21,CCC1OC(OC(=O)CN3=C(C2(=O)C+@@]r(ccc4)C3)[C@#H]2C)C1s
add c at position 32,CCC1OC(OC(=O)CN3=C(C2(=O)C+@@]r(cccc4)C3)[C@#H]2C)C1s
add 4 at position 33,CCC1OC(OC(=O)CN3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)C1s
add S at position 12,CCC1OC(OC(=OS)CN3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)C1s
add = at position 15,CCC1OC(OC(=OS)C=N3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)C1s
add C at position 54,CCC1OC(OC(=OS)C=N3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)CC1s
replace O at position 4 with C,CCC1CC(OC(=OS)C=N3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)CC1s
add C at position 17,CCC1CC(OC(=OS)C=NC3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)CC1s
add H at position 32,CCC1CC(OC(=OS)C=NC3=C(C2(=O)C+@@H]r(c4ccc4)C3)[C@#H]2C)CC1s
add ( at position 38,CCC1CC(OC(=OS)C=NC3=C(C2(=O)C+@@H]r(c4(ccc4)C3)[C@#H]2C)CC1s
remove r from position 34,CCC1CC(OC(=OS)C=NC3=C(C2(=O)C+@@H](c4(ccc4)C3)[C@#H]2C)CC1s
add 2 at position 15,CCC1CC(OC(=OS)C2=NC3=C(C2(=O)C+@@H](c4(ccc4)C3)[C@#H]2C)CC1s
remove C from position 0,CC1CC(OC(=OS)C2=NC3=C(C2(=O)C+@@H](c4(ccc4)C3)[C@#H]2C)CC1s
remove S from position 11,CC1CC(OC(=O)C2=NC3=C(C2(=O)C+@@H](c4(ccc4)C3)[C@#H]2C)CC1s
add C at position 29,CC1CC(OC(=O)C2=NC3=C(C2(=O)C+C@@H](c4(ccc4)C3)[C@#H]2C)CC1s
add c at position 39,CC1CC(OC(=O)C2=NC3=C(C2(=O)C+C@@H](c4(cccc4)C3)[C@#H]2C)CC1s
remove # from position 50,CC1CC(OC(=O)C2=NC3=C(C2(=O)C+C@@H](c4(cccc4)C3)[C@H]2C)CC1s
remove 2 from position 22,CC1CC(OC(=O)C2=NC3=C(C(=O)C+C@@H](c4(cccc4)C3)[C@H]2C)CC1s
add C at position 5,CC1CCC(OC(=O)C2=NC3=C(C(=O)C+C@@H](c4(cccc4)C3)[C@H]2C)CC1s
replace ( at position 37 with c,CC1CCC(OC(=O)C2=NC3=C(C(=O)C+C@@H](c4ccccc4)C3)[C@H]2C)CC1s
remove s from position 58,CC1CCC(OC(=O)C2=NC3=C(C(=O)C+C@@H](c4ccccc4)C3)[C@H]2C)CC1
replace + at position 28 with [,CC1CCC(OC(=O)C2=NC3=C(C(=O)C[C@@H](c4ccccc4)C3)[C@H]2C)CC1
final: CC1CCC(OC(=O)C2=NC3=C(C(=O)C[C@@H](c4ccccc4)C3)[C@H]2C)CC1,CC1CCC(OC(=O)C2=NC3=C(C(=O)C[C@@H](c4ccccc4)C3)[C@H]2C)CC1
