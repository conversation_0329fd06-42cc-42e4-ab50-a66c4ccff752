log,state
initialize: Cc1cccc(-c2nn(C[NH+]3CCCCC3)c(=S)n2-c2ccccc2)c1,Cc1cccc(-c2nn(C[NH+]3CCCCC3)c(=S)n2-c2ccccc2)c1
replace c at position 28 with +,Cc1cccc(-c2nn(C[NH+]3CCCCC3)+(=S)n2-c2ccccc2)c1
add ( at position 37,Cc1cccc(-c2nn(C[NH+]3CCCCC3)+(=S)n2-c(2ccccc2)c1
remove c from position 5,Cc1ccc(-c2nn(C[NH+]3CCCCC3)+(=S)n2-c(2ccccc2)c1
add 2 at position 22,Cc1ccc(-c2nn(C[NH+]3CC2CCC3)+(=S)n2-c(2ccccc2)c1
add 6 at position 1,C6c1ccc(-c2nn(C[NH+]3CC2CCC3)+(=S)n2-c(2ccccc2)c1
remove ( from position 30,C6c1ccc(-c2nn(C[NH+]3CC2CCC3)+=S)n2-c(2ccccc2)c1
remove c from position 9,C6c1ccc(-2nn(C[NH+]3CC2CCC3)+=S)n2-c(2ccccc2)c1
add + at position 45,C6c1ccc(-2nn(C[NH+]3CC2CCC3)+=S)n2-c(2ccccc2)+c1
replace n at position 32 with B,C6c1ccc(-2nn(C[NH+]3CC2CCC3)+=S)B2-c(2ccccc2)+c1
replace N at position 15 with ),C6c1ccc(-2nn(C[)H+]3CC2CCC3)+=S)B2-c(2ccccc2)+c1
remove = from position 29,C6c1ccc(-2nn(C[)H+]3CC2CCC3)+S)B2-c(2ccccc2)+c1
add ) at position 43,C6c1ccc(-2nn(C[)H+]3CC2CCC3)+S)B2-c(2ccccc2))+c1
remove c from position 2,C61ccc(-2nn(C[)H+]3CC2CCC3)+S)B2-c(2ccccc2))+c1
remove 2 from position 35,C61ccc(-2nn(C[)H+]3CC2CCC3)+S)B2-c(ccccc2))+c1
add B at position 19,C61ccc(-2nn(C[)H+]3BCC2CCC3)+S)B2-c(ccccc2))+c1
replace ] at position 17 with 2,C61ccc(-2nn(C[)H+23BCC2CCC3)+S)B2-c(ccccc2))+c1
remove C from position 12,C61ccc(-2nn([)H+23BCC2CCC3)+S)B2-c(ccccc2))+c1
remove c from position 33,C61ccc(-2nn([)H+23BCC2CCC3)+S)B2-(ccccc2))+c1
remove - from position 32,C61ccc(-2nn([)H+23BCC2CCC3)+S)B2(ccccc2))+c1
add - at position 21,C61ccc(-2nn([)H+23BCC-2CCC3)+S)B2(ccccc2))+c1
remove + from position 42,C61ccc(-2nn([)H+23BCC-2CCC3)+S)B2(ccccc2))c1
remove c from position 3,C61cc(-2nn([)H+23BCC-2CCC3)+S)B2(ccccc2))c1
add / at position 31,C61cc(-2nn([)H+23BCC-2CCC3)+S)B/2(ccccc2))c1
add - at position 13,C61cc(-2nn([)-H+23BCC-2CCC3)+S)B/2(ccccc2))c1
add ] at position 22,C61cc(-2nn([)-H+23BCC-]2CCC3)+S)B/2(ccccc2))c1
add - at position 33,C61cc(-2nn([)-H+23BCC-]2CCC3)+S)B-/2(ccccc2))c1
remove + from position 29,C61cc(-2nn([)-H+23BCC-]2CCC3)S)B-/2(ccccc2))c1
add O at position 17,C61cc(-2nn([)-H+2O3BCC-]2CCC3)S)B-/2(ccccc2))c1
replace [ at position 11 with 3,C61cc(-2nn(3)-H+2O3BCC-]2CCC3)S)B-/2(ccccc2))c1
add ] at position 9,C61cc(-2n]n(3)-H+2O3BCC-]2CCC3)S)B-/2(ccccc2))c1
replace 1 at position 2 with I,C6Icc(-2n]n(3)-H+2O3BCC-]2CCC3)S)B-/2(ccccc2))c1
remove C from position 22,C6Icc(-2n]n(3)-H+2O3BC-]2CCC3)S)B-/2(ccccc2))c1
remove c from position 37,C6Icc(-2n]n(3)-H+2O3BC-]2CCC3)S)B-/2(cccc2))c1
replace S at position 30 with (,C6Icc(-2n]n(3)-H+2O3BC-]2CCC3)()B-/2(cccc2))c1
replace ( at position 30 with o,C6Icc(-2n]n(3)-H+2O3BC-]2CCC3)o)B-/2(cccc2))c1
replace n at position 10 with @,C6Icc(-2n]@(3)-H+2O3BC-]2CCC3)o)B-/2(cccc2))c1
remove - from position 33,C6Icc(-2n]@(3)-H+2O3BC-]2CCC3)o)B/2(cccc2))c1
replace O at position 18 with 3,C6Icc(-2n]@(3)-H+233BC-]2CCC3)o)B/2(cccc2))c1
add = at position 11,C6Icc(-2n]@=(3)-H+233BC-]2CCC3)o)B/2(cccc2))c1
remove 2 from position 7,C6Icc(-n]@=(3)-H+233BC-]2CCC3)o)B/2(cccc2))c1
replace - at position 6 with n,C6Icc(nn]@=(3)-H+233BC-]2CCC3)o)B/2(cccc2))c1
replace 2 at position 24 with n,C6Icc(nn]@=(3)-H+233BC-]nCCC3)o)B/2(cccc2))c1
replace + at position 16 with O,C6Icc(nn]@=(3)-HO233BC-]nCCC3)o)B/2(cccc2))c1
add S at position 6,C6Icc(Snn]@=(3)-HO233BC-]nCCC3)o)B/2(cccc2))c1
remove @ from position 10,C6Icc(Snn]=(3)-HO233BC-]nCCC3)o)B/2(cccc2))c1
remove 2 from position 17,C6Icc(Snn]=(3)-HO33BC-]nCCC3)o)B/2(cccc2))c1
replace C at position 20 with l,C6Icc(Snn]=(3)-HO33Bl-]nCCC3)o)B/2(cccc2))c1
replace c at position 4 with ),C6Ic)(Snn]=(3)-HO33Bl-]nCCC3)o)B/2(cccc2))c1
remove c from position 3,C6I)(Snn]=(3)-HO33Bl-]nCCC3)o)B/2(cccc2))c1
replace I at position 2 with 2,C62)(Snn]=(3)-HO33Bl-]nCCC3)o)B/2(cccc2))c1
remove S from position 5,C62)(nn]=(3)-HO33Bl-]nCCC3)o)B/2(cccc2))c1
replace ] at position 20 with 4,C62)(nn]=(3)-HO33Bl-4nCCC3)o)B/2(cccc2))c1
replace ( at position 4 with I,C62)Inn]=(3)-HO33Bl-4nCCC3)o)B/2(cccc2))c1
remove I from position 4,C62)nn]=(3)-HO33Bl-4nCCC3)o)B/2(cccc2))c1
remove H from position 12,C62)nn]=(3)-O33Bl-4nCCC3)o)B/2(cccc2))c1
add S at position 12,C62)nn]=(3)-SO33Bl-4nCCC3)o)B/2(cccc2))c1
replace / at position 29 with F,C62)nn]=(3)-SO33Bl-4nCCC3)o)BF2(cccc2))c1
add r at position 23,C62)nn]=(3)-SO33Bl-4nCCrC3)o)BF2(cccc2))c1
replace ( at position 32 with r,C62)nn]=(3)-SO33Bl-4nCCrC3)o)BF2rcccc2))c1
remove - from position 11,C62)nn]=(3)SO33Bl-4nCCrC3)o)BF2rcccc2))c1
replace 3 at position 13 with 5,C62)nn]=(3)SO53Bl-4nCCrC3)o)BF2rcccc2))c1
add F at position 22,C62)nn]=(3)SO53Bl-4nCCFrC3)o)BF2rcccc2))c1
add B at position 35,C62)nn]=(3)SO53Bl-4nCCFrC3)o)BF2rccBcc2))c1
remove ( from position 8,C62)nn]=3)SO53Bl-4nCCFrC3)o)BF2rccBcc2))c1
add r at position 10,C62)nn]=3)rSO53Bl-4nCCFrC3)o)BF2rccBcc2))c1
add ) at position 34,C62)nn]=3)rSO53Bl-4nCCFrC3)o)BF2rc)cBcc2))c1
remove r from position 10,C62)nn]=3)SO53Bl-4nCCFrC3)o)BF2rc)cBcc2))c1
add ( at position 43,C62)nn]=3)SO53Bl-4nCCFrC3)o)BF2rc)cBcc2))c1(
remove ) from position 9,C62)nn]=3SO53Bl-4nCCFrC3)o)BF2rc)cBcc2))c1(
replace 2 at position 2 with @,C6@)nn]=3SO53Bl-4nCCFrC3)o)BF2rc)cBcc2))c1(
remove F from position 20,C6@)nn]=3SO53Bl-4nCCrC3)o)BF2rc)cBcc2))c1(
add r at position 24,C6@)nn]=3SO53Bl-4nCCrC3)ro)BF2rc)cBcc2))c1(
replace n at position 17 with F,C6@)nn]=3SO53Bl-4FCCrC3)ro)BF2rc)cBcc2))c1(
remove C from position 19,C6@)nn]=3SO53Bl-4FCrC3)ro)BF2rc)cBcc2))c1(
remove 2 from position 36,C6@)nn]=3SO53Bl-4FCrC3)ro)BF2rc)cBcc))c1(
replace c at position 34 with ),C6@)nn]=3SO53Bl-4FCrC3)ro)BF2rc)cB)c))c1(
replace F at position 17 with =,C6@)nn]=3SO53Bl-4=CrC3)ro)BF2rc)cB)c))c1(
remove ) from position 36,C6@)nn]=3SO53Bl-4=CrC3)ro)BF2rc)cB)c)c1(
remove = from position 7,C6@)nn]3SO53Bl-4=CrC3)ro)BF2rc)cB)c)c1(
remove C from position 17,C6@)nn]3SO53Bl-4=rC3)ro)BF2rc)cB)c)c1(
add 7 at position 37,C6@)nn]3SO53Bl-4=rC3)ro)BF2rc)cB)c)c17(
add 2 at position 28,C6@)nn]3SO53Bl-4=rC3)ro)BF2r2c)cB)c)c17(
replace 5 at position 10 with =,C6@)nn]3SO=3Bl-4=rC3)ro)BF2r2c)cB)c)c17(
remove n from position 4,C6@)n]3SO=3Bl-4=rC3)ro)BF2r2c)cB)c)c17(
remove ) from position 19,C6@)n]3SO=3Bl-4=rC3ro)BF2r2c)cB)c)c17(
remove B from position 30,C6@)n]3SO=3Bl-4=rC3ro)BF2r2c)c)c)c17(
add / at position 30,C6@)n]3SO=3Bl-4=rC3ro)BF2r2c)c/)c)c17(
remove F from position 23,C6@)n]3SO=3Bl-4=rC3ro)B2r2c)c/)c)c17(
remove B from position 22,C6@)n]3SO=3Bl-4=rC3ro)2r2c)c/)c)c17(
add r at position 33,C6@)n]3SO=3Bl-4=rC3ro)2r2c)c/)c)cr17(
remove c from position 30,C6@)n]3SO=3Bl-4=rC3ro)2r2c)c/))cr17(
remove ) from position 29,C6@)n]3SO=3Bl-4=rC3ro)2r2c)c/)cr17(
remove 3 from position 6,C6@)n]SO=3Bl-4=rC3ro)2r2c)c/)cr17(
add ) at position 30,C6@)n]SO=3Bl-4=rC3ro)2r2c)c/)c)r17(
replace 2 at position 21 with 7,C6@)n]SO=3Bl-4=rC3ro)7r2c)c/)c)r17(
replace r at position 18 with 4,C6@)n]SO=3Bl-4=rC34o)7r2c)c/)c)r17(
remove 7 from position 21,C6@)n]SO=3Bl-4=rC34o)r2c)c/)c)r17(
remove 4 from position 18,C6@)n]SO=3Bl-4=rC3o)r2c)c/)c)r17(
remove ( from position 32,C6@)n]SO=3Bl-4=rC3o)r2c)c/)c)r17
replace 3 at position 17 with ),C6@)n]SO=3Bl-4=rC)o)r2c)c/)c)r17
remove 4 from position 13,C6@)n]SO=3Bl-=rC)o)r2c)c/)c)r17
remove B from position 10,C6@)n]SO=3l-=rC)o)r2c)c/)c)r17
replace C at position 14 with 3,C6@)n]SO=3l-=r3)o)r2c)c/)c)r17
add H at position 16,C6@)n]SO=3l-=r3)Ho)r2c)c/)c)r17
remove ) from position 3,C6@n]SO=3l-=r3)Ho)r2c)c/)c)r17
replace 3 at position 8 with 7,C6@n]SO=7l-=r3)Ho)r2c)c/)c)r17
add 7 at position 0,7C6@n]SO=7l-=r3)Ho)r2c)c/)c)r17
add 1 at position 2,7C16@n]SO=7l-=r3)Ho)r2c)c/)c)r17
remove r from position 20,7C16@n]SO=7l-=r3)Ho)2c)c/)c)r17
remove = from position 13,7C16@n]SO=7l-r3)Ho)2c)c/)c)r17
replace 7 at position 29 with ),7C16@n]SO=7l-r3)Ho)2c)c/)c)r1)
replace = at position 9 with 3,7C16@n]SO37l-r3)Ho)2c)c/)c)r1)
remove ) from position 29,7C16@n]SO37l-r3)Ho)2c)c/)c)r1
remove l from position 11,7C16@n]SO37-r3)Ho)2c)c/)c)r1
remove ) from position 20,7C16@n]SO37-r3)Ho)2cc/)c)r1
replace 7 at position 0 with S,SC16@n]SO37-r3)Ho)2cc/)c)r1
remove 2 from position 18,SC16@n]SO37-r3)Ho)cc/)c)r1
add B at position 15,SC16@n]SO37-r3)BHo)cc/)c)r1
remove / from position 21,SC16@n]SO37-r3)BHo)cc)c)r1
replace @ at position 4 with r,SC16rn]SO37-r3)BHo)cc)c)r1
remove c from position 19,SC16rn]SO37-r3)BHo)c)c)r1
add s at position 17,SC16rn]SO37-r3)BHso)c)c)r1
replace c at position 22 with /,SC16rn]SO37-r3)BHso)c)/)r1
replace r at position 24 with C,SC16rn]SO37-r3)BHso)c)/)C1
remove r from position 12,SC16rn]SO37-3)BHso)c)/)C1
replace 7 at position 10 with -,SC16rn]SO3--3)BHso)c)/)C1
remove o from position 17,SC16rn]SO3--3)BHs)c)/)C1
remove ) from position 19,SC16rn]SO3--3)BHs)c/)C1
remove B from position 14,SC16rn]SO3--3)Hs)c/)C1
add r at position 2,SCr16rn]SO3--3)Hs)c/)C1
remove C from position 21,SCr16rn]SO3--3)Hs)c/)1
remove C from position 1,Sr16rn]SO3--3)Hs)c/)1
remove 1 from position 2,Sr6rn]SO3--3)Hs)c/)1
remove 1 from position 19,Sr6rn]SO3--3)Hs)c/)
replace 3 at position 11 with F,Sr6rn]SO3--F)Hs)c/)
remove ) from position 12,Sr6rn]SO3--FHs)c/)
remove r from position 1,S6rn]SO3--FHs)c/)
add l at position 2,S6lrn]SO3--FHs)c/)
remove - from position 10,S6lrn]SO3-FHs)c/)
add S at position 7,S6lrn]SSO3-FHs)c/)
replace S at position 0 with o,o6lrn]SSO3-FHs)c/)
remove F from position 11,o6lrn]SSO3-Hs)c/)
remove l from position 2,o6rn]SSO3-Hs)c/)
remove S from position 6,o6rn]SO3-Hs)c/)
replace / at position 13 with N,o6rn]SO3-Hs)cN)
remove ) from position 11,o6rn]SO3-HscN)
add O at position 2,o6Orn]SO3-HscN)
replace H at position 10 with ],o6Orn]SO3-]scN)
add 6 at position 14,o6Orn]SO3-]scN6)
remove s from position 11,o6Orn]SO3-]cN6)
remove S from position 6,o6Orn]O3-]cN6)
replace r at position 3 with 4,o6O4n]O3-]cN6)
add H at position 5,o6O4nH]O3-]cN6)
replace 6 at position 1 with H,oHO4nH]O3-]cN6)
remove 6 from position 13,oHO4nH]O3-]cN)
add H at position 9,oHO4nH]O3H-]cN)
remove O from position 2,oH4nH]O3H-]cN)
remove ] from position 5,oH4nHO3H-]cN)
replace H at position 4 with [,oH4n[O3H-]cN)
remove 4 from position 2,oHn[O3H-]cN)
remove n from position 2,oH[O3H-]cN)
replace c at position 8 with S,oH[O3H-]SN)
remove O from position 3,oH[3H-]SN)
remove [ from position 2,oH3H-]SN)
remove S from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
