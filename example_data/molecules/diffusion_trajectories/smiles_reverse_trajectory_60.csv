log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove <PERSON> from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add S at position 6,oH3H-]SN)
add [ at position 2,oH[3H-]SN)
add 4 at position 3,oH[43H-]SN)
replace S at position 8 with r,oH[43H-]rN)
add n at position 2,oHn[43H-]rN)
add 4 at position 2,oH4n[43H-]rN)
replace [ at position 4 with H,oH4nH43H-]rN)
add C at position 5,oH4nHC43H-]rN)
add O at position 2,oHO4nHC43H-]rN)
remove H from position 9,oHO4nHC43-]rN)
add 6 at position 13,oHO4nHC43-]rN6)
replace H at position 1 with 6,o6O4nHC43-]rN6)
remove H from position 5,o6O4nC43-]rN6)
replace 4 at position 3 with r,o6OrnC43-]rN6)
add r at position 6,o6OrnCr43-]rN6)
add s at position 11,o6OrnCr43-]srN6)
remove 6 from position 14,o6OrnCr43-]srN)
replace ] at position 10 with H,o6OrnCr43-HsrN)
remove O from position 2,o6rnCr43-HsrN)
add 7 at position 11,o6rnCr43-Hs7rN)
replace N at position 13 with /,o6rnCr43-Hs7r/)
add S at position 6,o6rnCrS43-Hs7r/)
add l at position 2,o6lrnCrS43-Hs7r/)
add F at position 11,o6lrnCrS43-FHs7r/)
replace o at position 0 with S,S6lrnCrS43-FHs7r/)
remove S from position 7,S6lrnCr43-FHs7r/)
add B at position 10,S6lrnCr43-BFHs7r/)
remove l from position 2,S6rnCr43-BFHs7r/)
add r at position 1,Sr6rnCr43-BFHs7r/)
add ) at position 12,Sr6rnCr43-BF)Hs7r/)
replace F at position 11 with 3,Sr6rnCr43-B3)Hs7r/)
add ) at position 19,Sr6rnCr43-B3)Hs7r/))
add 1 at position 2,Sr16rnCr43-B3)Hs7r/))
add C at position 1,SCr16rnCr43-B3)Hs7r/))
add C at position 21,SCr16rnCr43-B3)Hs7r/)C)
remove r from position 2,SC16rnCr43-B3)Hs7r/)C)
add B at position 14,SC16rnCr43-B3)BHs7r/)C)
add C at position 19,SC16rnCr43-B3)BHs7rC/)C)
add c at position 17,SC16rnCr43-B3)BHsc7rC/)C)
replace - at position 10 with 6,SC16rnCr436B3)BHsc7rC/)C)
add ( at position 12,SC16rnCr436B(3)BHsc7rC/)C)
replace C at position 24 with r,SC16rnCr436B(3)BHsc7rC/)r)
replace / at position 22 with C,SC16rnCr436B(3)BHsc7rCC)r)
remove s from position 17,SC16rnCr436B(3)BHc7rCC)r)
add F at position 19,SC16rnCr436B(3)BHc7FrCC)r)
replace r at position 4 with 2,SC162nCr436B(3)BHc7FrCC)r)
add / at position 21,SC162nCr436B(3)BHc7Fr/CC)r)
remove B from position 15,SC162nCr436B(3)Hc7Fr/CC)r)
add 2 at position 18,SC162nCr436B(3)Hc72Fr/CC)r)
replace S at position 0 with 7,7C162nCr436B(3)Hc72Fr/CC)r)
add 2 at position 20,7C162nCr436B(3)Hc72F2r/CC)r)
add N at position 11,7C162nCr436NB(3)Hc72F2r/CC)r)
add ) at position 29,7C162nCr436NB(3)Hc72F2r/CC)r))
replace 3 at position 9 with =,7C162nCr4=6NB(3)Hc72F2r/CC)r))
replace ) at position 29 with 7,7C162nCr4=6NB(3)Hc72F2r/CC)r)7
add = at position 13,7C162nCr4=6NB=(3)Hc72F2r/CC)r)7
add B at position 20,7C162nCr4=6NB=(3)Hc7B2F2r/CC)r)7
remove 1 from position 2,7C62nCr4=6NB=(3)Hc7B2F2r/CC)r)7
remove 7 from position 0,C62nCr4=6NB=(3)Hc7B2F2r/CC)r)7
replace 6 at position 8 with S,C62nCr4=SNB=(3)Hc7B2F2r/CC)r)7
add N at position 3,C62NnCr4=SNB=(3)Hc7B2F2r/CC)r)7
remove H from position 16,C62NnCr4=SNB=(3)c7B2F2r/CC)r)7
replace 3 at position 14 with c,C62NnCr4=SNB=(c)c7B2F2r/CC)r)7
add 5 at position 10,C62NnCr4=S5NB=(c)c7B2F2r/CC)r)7
add l at position 13,C62NnCr4=S5NBl=(c)c7B2F2r/CC)r)7
replace ) at position 17 with F,C62NnCr4=S5NBl=(cFc7B2F2r/CC)r)7
add 1 at position 32,C62NnCr4=S5NBl=(cFc7B2F2r/CC)r)71
add 4 at position 18,C62NnCr4=S5NBl=(cF4c7B2F2r/CC)r)71
add 7 at position 21,C62NnCr4=S5NBl=(cF4c77B2F2r/CC)r)71
replace 4 at position 18 with s,C62NnCr4=S5NBl=(cFsc77B2F2r/CC)r)71
replace 7 at position 21 with ),C62NnCr4=S5NBl=(cFsc7)B2F2r/CC)r)71
remove ) from position 30,C62NnCr4=S5NBl=(cFsc7)B2F2r/CCr)71
add = at position 6,C62NnC=r4=S5NBl=(cFsc7)B2F2r/CCr)71
add + at position 29,C62NnC=r4=S5NBl=(cFsc7)B2F2r/+CCr)71
add 2 at position 30,C62NnC=r4=S5NBl=(cFsc7)B2F2r/+2CCr)71
remove r from position 33,C62NnC=r4=S5NBl=(cFsc7)B2F2r/+2CC)71
add c at position 22,C62NnC=r4=S5NBl=(cFsc7c)B2F2r/+2CC)71
add o at position 23,C62NnC=r4=S5NBl=(cFsc7co)B2F2r/+2CC)71
remove / from position 30,C62NnC=r4=S5NBl=(cFsc7co)B2F2r+2CC)71
add c at position 30,C62NnC=r4=S5NBl=(cFsc7co)B2F2rc+2CC)71
add r at position 19,C62NnC=r4=S5NBl=(cFrsc7co)B2F2rc+2CC)71
add ) at position 4,C62N)nC=r4=S5NBl=(cFrsc7co)B2F2rc+2CC)71
replace = at position 10 with ),C62N)nC=r4)S5NBl=(cFrsc7co)B2F2rc+2CC)71
remove 2 from position 28,C62N)nC=r4)S5NBl=(cFrsc7co)BF2rc+2CC)71
remove 7 from position 37,C62N)nC=r4)S5NBl=(cFrsc7co)BF2rc+2CC)1
add 4 at position 17,C62N)nC=r4)S5NBl=4(cFrsc7co)BF2rc+2CC)1
add ] at position 7,C62N)nC]=r4)S5NBl=4(cFrsc7co)BF2rc+2CC)1
add ) at position 36,C62N)nC]=r4)S5NBl=4(cFrsc7co)BF2rc+2)CC)1
replace = at position 17 with H,C62N)nC]=r4)S5NBlH4(cFrsc7co)BF2rc+2)CC)1
replace + at position 34 with ),C62N)nC]=r4)S5NBlH4(cFrsc7co)BF2rc)2)CC)1
add B at position 36,C62N)nC]=r4)S5NBlH4(cFrsc7co)BF2rc)2B)CC)1
add n at position 19,C62N)nC]=r4)S5NBlH4n(cFrsc7co)BF2rc)2B)CC)1
replace H at position 17 with -,C62N)nC]=r4)S5NBl-4n(cFrsc7co)BF2rc)2B)CC)1
replace s at position 24 with 2,C62N)nC]=r4)S5NBl-4n(cFr2c7co)BF2rc)2B)CC)1
add O at position 13,C62N)nC]=r4)SO5NBl-4n(cFr2c7co)BF2rc)2B)CC)1
remove 7 from position 27,C62N)nC]=r4)SO5NBl-4n(cFr2cco)BF2rc)2B)CC)1
remove r from position 9,C62N)nC]=4)SO5NBl-4n(cFr2cco)BF2rc)2B)CC)1
remove N from position 3,C62)nC]=4)SO5NBl-4n(cFr2cco)BF2rc)2B)CC)1
add r at position 10,C62)nC]=4)rSO5NBl-4n(cFr2cco)BF2rc)2B)CC)1
remove ) from position 34,C62)nC]=4)rSO5NBl-4n(cFr2cco)BF2rc2B)CC)1
remove r from position 10,C62)nC]=4)SO5NBl-4n(cFr2cco)BF2rc2B)CC)1
add ( at position 8,C62)nC]=(4)SO5NBl-4n(cFr2cco)BF2rc2B)CC)1
remove B from position 35,C62)nC]=(4)SO5NBl-4n(cFr2cco)BF2rc2)CC)1
remove F from position 22,C62)nC]=(4)SO5NBl-4n(cr2cco)BF2rc2)CC)1
replace 5 at position 13 with 3,C62)nC]=(4)SO3NBl-4n(cr2cco)BF2rc2)CC)1
add - at position 11,C62)nC]=(4)-SO3NBl-4n(cr2cco)BF2rc2)CC)1
replace r at position 32 with (,C62)nC]=(4)-SO3NBl-4n(cr2cco)BF2(c2)CC)1
remove r from position 23,C62)nC]=(4)-SO3NBl-4n(c2cco)BF2(c2)CC)1
replace F at position 29 with /,C62)nC]=(4)-SO3NBl-4n(c2cco)B/2(c2)CC)1
remove S from position 12,C62)nC]=(4)-O3NBl-4n(c2cco)B/2(c2)CC)1
add O at position 12,C62)nC]=(4)-OO3NBl-4n(c2cco)B/2(c2)CC)1
add I at position 4,C62)InC]=(4)-OO3NBl-4n(c2cco)B/2(c2)CC)1
replace I at position 4 with ),C62))nC]=(4)-OO3NBl-4n(c2cco)B/2(c2)CC)1
replace 4 at position 20 with ],C62))nC]=(4)-OO3NBl-]n(c2cco)B/2(c2)CC)1
add S at position 5,C62))SnC]=(4)-OO3NBl-]n(c2cco)B/2(c2)CC)1
replace 2 at position 2 with I,C6I))SnC]=(4)-OO3NBl-]n(c2cco)B/2(c2)CC)1
add ( at position 3,C6I())SnC]=(4)-OO3NBl-]n(c2cco)B/2(c2)CC)1
replace ) at position 4 with O,C6I(O)SnC]=(4)-OO3NBl-]n(c2cco)B/2(c2)CC)1
replace l at position 20 with =,C6I(O)SnC]=(4)-OO3NB=-]n(c2cco)B/2(c2)CC)1
add 2 at position 17,C6I(O)SnC]=(4)-OO23NB=-]n(c2cco)B/2(c2)CC)1
add @ at position 10,C6I(O)SnC]@=(4)-OO23NB=-]n(c2cco)B/2(c2)CC)1
remove S from position 6,C6I(O)nC]@=(4)-OO23NB=-]n(c2cco)B/2(c2)CC)1
replace O at position 16 with ),C6I(O)nC]@=(4)-O)23NB=-]n(c2cco)B/2(c2)CC)1
replace n at position 24 with 2,C6I(O)nC]@=(4)-O)23NB=-]2(c2cco)B/2(c2)CC)1
replace n at position 6 with N,C6I(O)NC]@=(4)-O)23NB=-]2(c2cco)B/2(c2)CC)1
add C at position 7,C6I(O)NCC]@=(4)-O)23NB=-]2(c2cco)B/2(c2)CC)1
remove = from position 11,C6I(O)NCC]@(4)-O)23NB=-]2(c2cco)B/2(c2)CC)1
replace 3 at position 18 with O,C6I(O)NCC]@(4)-O)2ONB=-]2(c2cco)B/2(c2)CC)1
add - at position 33,C6I(O)NCC]@(4)-O)2ONB=-]2(c2cco)B-/2(c2)CC)1
replace @ at position 10 with C,C6I(O)NCC]C(4)-O)2ONB=-]2(c2cco)B-/2(c2)CC)1
replace o at position 30 with (,C6I(O)NCC]C(4)-O)2ONB=-]2(c2cc()B-/2(c2)CC)1
replace ( at position 30 with 2,C6I(O)NCC]C(4)-O)2ONB=-]2(c2cc2)B-/2(c2)CC)1
add c at position 37,C6I(O)NCC]C(4)-O)2ONB=-]2(c2cc2)B-/2(cc2)CC)1
add C at position 22,C6I(O)NCC]C(4)-O)2ONB=C-]2(c2cc2)B-/2(cc2)CC)1
replace I at position 2 with C,C6C(O)NCC]C(4)-O)2ONB=C-]2(c2cc2)B-/2(cc2)CC)1
remove ] from position 9,C6C(O)NCCC(4)-O)2ONB=C-]2(c2cc2)B-/2(cc2)CC)1
replace 4 at position 11 with (,C6C(O)NCCC(()-O)2ONB=C-]2(c2cc2)B-/2(cc2)CC)1
remove O from position 17,C6C(O)NCCC(()-O)2NB=C-]2(c2cc2)B-/2(cc2)CC)1
add + at position 29,C6C(O)NCCC(()-O)2NB=C-]2(c2cc+2)B-/2(cc2)CC)1
remove - from position 33,C6C(O)NCCC(()-O)2NB=C-]2(c2cc+2)B/2(cc2)CC)1
remove ] from position 22,C6C(O)NCCC(()-O)2NB=C-2(c2cc+2)B/2(cc2)CC)1
remove - from position 13,C6C(O)NCCC(()O)2NB=C-2(c2cc+2)B/2(cc2)CC)1
remove / from position 31,C6C(O)NCCC(()O)2NB=C-2(c2cc+2)B2(cc2)CC)1
add C at position 3,C6CC(O)NCCC(()O)2NB=C-2(c2cc+2)B2(cc2)CC)1
add + at position 42,C6CC(O)NCCC(()O)2NB=C-2(c2cc+2)B2(cc2)CC)1+
remove - from position 21,C6CC(O)NCCC(()O)2NB=C2(c2cc+2)B2(cc2)CC)1+
add c at position 32,C6CC(O)NCCC(()O)2NB=C2(c2cc+2)B2c(cc2)CC)1+
add c at position 33,C6CC(O)NCCC(()O)2NB=C2(c2cc+2)B2cc(cc2)CC)1+
add C at position 12,C6CC(O)NCCC(C()O)2NB=C2(c2cc+2)B2cc(cc2)CC)1+
replace 2 at position 17 with N,C6CC(O)NCCC(C()O)NNB=C2(c2cc+2)B2cc(cc2)CC)1+
remove B from position 19,C6CC(O)NCCC(C()O)NN=C2(c2cc+2)B2cc(cc2)CC)1+
add c at position 35,C6CC(O)NCCC(C()O)NN=C2(c2cc+2)B2cc(ccc2)CC)1+
add C at position 2,C6CCC(O)NCCC(C()O)NN=C2(c2cc+2)B2cc(ccc2)CC)1+
remove ) from position 43,C6CCC(O)NCCC(C()O)NN=C2(c2cc+2)B2cc(ccc2)CC1+
add c at position 29,C6CCC(O)NCCC(C()O)NN=C2(c2cc+c2)B2cc(ccc2)CC1+
replace ) at position 15 with =,C6CCC(O)NCCC(C(=O)NN=C2(c2cc+c2)B2cc(ccc2)CC1+
replace B at position 32 with c,C6CCC(O)NCCC(C(=O)NN=C2(c2cc+c2)c2cc(ccc2)CC1+
remove + from position 45,C6CCC(O)NCCC(C(=O)NN=C2(c2cc+c2)c2cc(ccc2)CC1
add 1 at position 9,C6CCC(O)N1CCC(C(=O)NN=C2(c2cc+c2)c2cc(ccc2)CC1
add c at position 30,C6CCC(O)N1CCC(C(=O)NN=C2(c2cc+cc2)c2cc(ccc2)CC1
remove 6 from position 1,CCCC(O)N1CCC(C(=O)NN=C2(c2cc+cc2)c2cc(ccc2)CC1
remove 2 from position 22,CCCC(O)N1CCC(C(=O)NN=C(c2cc+cc2)c2cc(ccc2)CC1
add = at position 5,CCCC(=O)N1CCC(C(=O)NN=C(c2cc+cc2)c2cc(ccc2)CC1
remove ( from position 37,CCCC(=O)N1CCC(C(=O)NN=C(c2cc+cc2)c2ccccc2)CC1
replace + at position 28 with c,CCCC(=O)N1CCC(C(=O)NN=C(c2ccccc2)c2ccccc2)CC1
final: CCCC(=O)N1CCC(C(=O)NN=C(c2ccccc2)c2ccccc2)CC1,CCCC(=O)N1CCC(C(=O)NN=C(c2ccccc2)c2ccccc2)CC1
