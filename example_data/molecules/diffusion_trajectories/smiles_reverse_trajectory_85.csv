log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with B,oFcBBH-C
remove c from position 2,oFBBH-C
remove B from position 3,oFBH-C
replace - at position 4 with ],oFBH]C
remove C from position 5,oFBH]
add B at position 4,oFBHB]
add N at position 6,oFBHB]N
replace B at position 4 with -,oFBH-]N
add r at position 7,oFBH-]Nr
add [ at position 6,oFBH-][Nr
add [ at position 2,oF[BH-][Nr
add 5 at position 3,oF[5BH-][Nr
replace [ at position 8 with B,oF[5BH-]BNr
add l at position 2,oFl[5BH-]BNr
add 4 at position 2,oF4l[5BH-]BNr
replace [ at position 4 with H,oF4lH5BH-]BNr
add 6 at position 5,oF4lH65BH-]BNr
add O at position 2,oFO4lH65BH-]BNr
remove H from position 9,oFO4lH65B-]BNr
add 6 at position 13,oFO4lH65B-]BN6r
replace F at position 1 with c,ocO4lH65B-]BN6r
remove H from position 5,ocO4l65B-]BN6r
replace 4 at position 3 with r,ocOrl65B-]BN6r
add = at position 6,ocOrl6=5B-]BN6r
add s at position 11,ocOrl6=5B-]sBN6r
remove 6 from position 14,ocOrl6=5B-]sBNr
replace ] at position 10 with C,ocOrl6=5B-CsBNr
remove O from position 2,ocrl6=5B-CsBNr
add 2 at position 11,ocrl6=5B-Cs2BNr
replace N at position 13 with /,ocrl6=5B-Cs2B/r
add S at position 6,ocrl6=S5B-Cs2B/r
add l at position 2,oclrl6=S5B-Cs2B/r
add F at position 11,oclrl6=S5B-FCs2B/r
replace o at position 0 with S,Sclrl6=S5B-FCs2B/r
remove S from position 7,Sclrl6=5B-FCs2B/r
add - at position 10,Sclrl6=5B--FCs2B/r
remove l from position 2,Scrl6=5B--FCs2B/r
add r at position 1,Srcrl6=5B--FCs2B/r
add ) at position 12,Srcrl6=5B--F)Cs2B/r
replace F at position 11 with 3,Srcrl6=5B--3)Cs2B/r
add 4 at position 19,Srcrl6=5B--3)Cs2B/r4
add ) at position 2,Sr)crl6=5B--3)Cs2B/r4
add 6 at position 1,S6r)crl6=5B--3)Cs2B/r4
add F at position 21,S6r)crl6=5B--3)Cs2B/rF4
remove r from position 2,S6)crl6=5B--3)Cs2B/rF4
add H at position 14,S6)crl6=5B--3)HCs2B/rF4
add ) at position 19,S6)crl6=5B--3)HCs2B)/rF4
add C at position 17,S6)crl6=5B--3)HCsC2B)/rF4
replace - at position 10 with l,S6)crl6=5Bl-3)HCsC2B)/rF4
add = at position 12,S6)crl6=5Bl-=3)HCsC2B)/rF4
replace F at position 24 with (,S6)crl6=5Bl-=3)HCsC2B)/r(4
replace / at position 22 with c,S6)crl6=5Bl-=3)HCsC2B)cr(4
remove s from position 17,S6)crl6=5Bl-=3)HCC2B)cr(4
add - at position 19,S6)crl6=5Bl-=3)HCC2-B)cr(4
replace r at position 4 with ],S6)c]l6=5Bl-=3)HCC2-B)cr(4
add ( at position 21,S6)c]l6=5Bl-=3)HCC2-B()cr(4
add S at position 5,S6)c]Sl6=5Bl-=3)HCC2-B()cr(4
add ) at position 27,S6)c]Sl6=5Bl-=3)HCC2-B()cr()4
replace S at position 0 with C,C6)c]Sl6=5Bl-=3)HCC2-B()cr()4
add C at position 20,C6)c]Sl6=5Bl-=3)HCC2C-B()cr()4
replace 6 at position 7 with N,C6)c]SlN=5Bl-=3)HCC2C-B()cr()4
add H at position 15,C6)c]SlN=5Bl-=3H)HCC2C-B()cr()4
replace 4 at position 30 with 7,C6)c]SlN=5Bl-=3H)HCC2C-B()cr()7
replace ) at position 29 with 7,C6)c]SlN=5Bl-=3H)HCC2C-B()cr(77
add ) at position 27,C6)c]SlN=5Bl-=3H)HCC2C-B()c)r(77
replace 5 at position 9 with N,C6)c]SlN=NBl-=3H)HCC2C-B()c)r(77
add @ at position 2,C6@)c]SlN=NBl-=3H)HCC2C-B()c)r(77
replace ( at position 25 with /,C6@)c]SlN=NBl-=3H)HCC2C-B/)c)r(77
remove - from position 23,C6@)c]SlN=NBl-=3H)HCC2CB/)c)r(77
remove l from position 7,C6@)c]SN=NBl-=3H)HCC2CB/)c)r(77
replace 3 at position 14 with r,C6@)c]SN=NBl-=rH)HCC2CB/)c)r(77
add ) at position 21,C6@)c]SN=NBl-=rH)HCC2)CB/)c)r(77
add 4 at position 13,C6@)c]SN=NBl-4=rH)HCC2)CB/)c)r(77
replace ) at position 17 with ],C6@)c]SN=NBl-4=rH]HCC2)CB/)c)r(77
add 1 at position 32,C6@)c]SN=NBl-4=rH]HCC2)CB/)c)r(717
add 4 at position 18,C6@)c]SN=NBl-4=rH]4HCC2)CB/)c)r(717
add 6 at position 21,C6@)c]SN=NBl-4=rH]4HC6C2)CB/)c)r(717
replace 4 at position 18 with o,C6@)c]SN=NBl-4=rH]oHC6C2)CB/)c)r(717
replace 6 at position 21 with r,C6@)c]SN=NBl-4=rH]oHCrC2)CB/)c)r(717
remove ) from position 30,C6@)c]SN=NBl-4=rH]oHCrC2)CB/)cr(717
add 3 at position 6,C6@)c]3SN=NBl-4=rH]oHCrC2)CB/)cr(717
add + at position 29,C6@)c]3SN=NBl-4=rH]oHCrC2)CB/+)cr(717
add C at position 30,C6@)c]3SN=NBl-4=rH]oHCrC2)CB/+C)cr(717
remove r from position 33,C6@)c]3SN=NBl-4=rH]oHCrC2)CB/+C)c(717
add F at position 22,C6@)c]3SN=NBl-4=rH]oHCFrC2)CB/+C)c(717
add ] at position 23,C6@)c]3SN=NBl-4=rH]oHCF]rC2)CB/+C)c(717
remove / from position 30,C6@)c]3SN=NBl-4=rH]oHCF]rC2)CB+C)c(717
add 3 at position 30,C6@)c]3SN=NBl-4=rH]oHCF]rC2)CB3+C)c(717
add 2 at position 19,C6@)c]3SN=NBl-4=rH]2oHCF]rC2)CB3+C)c(717
add n at position 4,C6@)nc]3SN=NBl-4=rH]2oHCF]rC2)CB3+C)c(717
replace = at position 10 with 5,C6@)nc]3SN5NBl-4=rH]2oHCF]rC2)CB3+C)c(717
remove 2 from position 28,C6@)nc]3SN5NBl-4=rH]2oHCF]rC)CB3+C)c(717
remove 7 from position 37,C6@)nc]3SN5NBl-4=rH]2oHCF]rC)CB3+C)c(17
add @ at position 17,C6@)nc]3SN5NBl-4=@rH]2oHCF]rC)CB3+C)c(17
add = at position 7,C6@)nc]=3SN5NBl-4=@rH]2oHCF]rC)CB3+C)c(17
add 2 at position 36,C6@)nc]=3SN5NBl-4=@rH]2oHCF]rC)CB3+C2)c(17
replace = at position 17 with F,C6@)nc]=3SN5NBl-4F@rH]2oHCF]rC)CB3+C2)c(17
replace + at position 34 with ),C6@)nc]=3SN5NBl-4F@rH]2oHCF]rC)CB3)C2)c(17
add ) at position 36,C6@)nc]=3SN5NBl-4F@rH]2oHCF]rC)CB3)C)2)c(17
add @ at position 19,C6@)nc]=3SN5NBl-4F@@rH]2oHCF]rC)CB3)C)2)c(17
replace F at position 17 with n,C6@)nc]=3SN5NBl-4n@@rH]2oHCF]rC)CB3)C)2)c(17
remove 7 from position 43,C6@)nc]=3SN5NBl-4n@@rH]2oHCF]rC)CB3)C)2)c(1
add F at position 20,C6@)nc]=3SN5NBl-4n@@FrH]2oHCF]rC)CB3)C)2)c(1
replace @ at position 2 with 2,C62)nc]=3SN5NBl-4n@@FrH]2oHCF]rC)CB3)C)2)c(1
add + at position 9,C62)nc]=3+SN5NBl-4n@@FrH]2oHCF]rC)CB3)C)2)c(1
remove ( from position 43,C62)nc]=3+SN5NBl-4n@@FrH]2oHCF]rC)CB3)C)2)c1
add r at position 10,C62)nc]=3+rSN5NBl-4n@@FrH]2oHCF]rC)CB3)C)2)c1
remove ) from position 34,C62)nc]=3+rSN5NBl-4n@@FrH]2oHCF]rCCB3)C)2)c1
remove r from position 10,C62)nc]=3+SN5NBl-4n@@FrH]2oHCF]rCCB3)C)2)c1
add C at position 8,C62)nc]=C3+SN5NBl-4n@@FrH]2oHCF]rCCB3)C)2)c1
remove B from position 35,C62)nc]=C3+SN5NBl-4n@@FrH]2oHCF]rCC3)C)2)c1
remove F from position 22,C62)nc]=C3+SN5NBl-4n@@rH]2oHCF]rCC3)C)2)c1
replace 5 at position 13 with 3,C62)nc]=C3+SN3NBl-4n@@rH]2oHCF]rCC3)C)2)c1
add - at position 11,C62)nc]=C3+-SN3NBl-4n@@rH]2oHCF]rCC3)C)2)c1
replace r at position 32 with (,C62)nc]=C3+-SN3NBl-4n@@rH]2oHCF](CC3)C)2)c1
remove r from position 23,C62)nc]=C3+-SN3NBl-4n@@H]2oHCF](CC3)C)2)c1
replace F at position 29 with /,C62)nc]=C3+-SN3NBl-4n@@H]2oHC/](CC3)C)2)c1
remove S from position 12,C62)nc]=C3+-N3NBl-4n@@H]2oHC/](CC3)C)2)c1
add = at position 12,C62)nc]=C3+-=N3NBl-4n@@H]2oHC/](CC3)C)2)c1
add I at position 4,C62)Inc]=C3+-=N3NBl-4n@@H]2oHC/](CC3)C)2)c1
replace I at position 4 with 1,C62)1nc]=C3+-=N3NBl-4n@@H]2oHC/](CC3)C)2)c1
replace 4 at position 20 with ],C62)1nc]=C3+-=N3NBl-]n@@H]2oHC/](CC3)C)2)c1
add S at position 5,C62)1Snc]=C3+-=N3NBl-]n@@H]2oHC/](CC3)C)2)c1
replace 2 at position 2 with I,C6I)1Snc]=C3+-=N3NBl-]n@@H]2oHC/](CC3)C)2)c1
add ) at position 3,C6I))1Snc]=C3+-=N3NBl-]n@@H]2oHC/](CC3)C)2)c1
replace ) at position 4 with c,C6I)c1Snc]=C3+-=N3NBl-]n@@H]2oHC/](CC3)C)2)c1
replace l at position 20 with [,C6I)c1Snc]=C3+-=N3NB[-]n@@H]2oHC/](CC3)C)2)c1
add 3 at position 17,C6I)c1Snc]=C3+-=N33NB[-]n@@H]2oHC/](CC3)C)2)c1
add B at position 10,C6I)c1Snc]B=C3+-=N33NB[-]n@@H]2oHC/](CC3)C)2)c1
remove S from position 6,C6I)c1nc]B=C3+-=N33NB[-]n@@H]2oHC/](CC3)C)2)c1
replace N at position 16 with O,C6I)c1nc]B=C3+-=O33NB[-]n@@H]2oHC/](CC3)C)2)c1
replace n at position 24 with 2,C6I)c1nc]B=C3+-=O33NB[-]2@@H]2oHC/](CC3)C)2)c1
replace n at position 6 with c,C6I)c1cc]B=C3+-=O33NB[-]2@@H]2oHC/](CC3)C)2)c1
add c at position 7,C6I)c1ccc]B=C3+-=O33NB[-]2@@H]2oHC/](CC3)C)2)c1
remove = from position 11,C6I)c1ccc]BC3+-=O33NB[-]2@@H]2oHC/](CC3)C)2)c1
replace 3 at position 18 with O,C6I)c1ccc]BC3+-=O3ONB[-]2@@H]2oHC/](CC3)C)2)c1
add - at position 33,C6I)c1ccc]BC3+-=O3ONB[-]2@@H]2oHC-/](CC3)C)2)c1
replace B at position 10 with (,C6I)c1ccc](C3+-=O3ONB[-]2@@H]2oHC-/](CC3)C)2)c1
replace o at position 30 with (,C6I)c1ccc](C3+-=O3ONB[-]2@@H]2(HC-/](CC3)C)2)c1
replace ( at position 30 with N,C6I)c1ccc](C3+-=O3ONB[-]2@@H]2NHC-/](CC3)C)2)c1
add 3 at position 37,C6I)c1ccc](C3+-=O3ONB[-]2@@H]2NHC-/](3CC3)C)2)c1
add C at position 22,C6I)c1ccc](C3+-=O3ONB[C-]2@@H]2NHC-/](3CC3)C)2)c1
replace I at position 2 with (,C6()c1ccc](C3+-=O3ONB[C-]2@@H]2NHC-/](3CC3)C)2)c1
remove ] from position 9,C6()c1ccc(C3+-=O3ONB[C-]2@@H]2NHC-/](3CC3)C)2)c1
replace 3 at position 11 with C,C6()c1ccc(CC+-=O3ONB[C-]2@@H]2NHC-/](3CC3)C)2)c1
remove O from position 17,C6()c1ccc(CC+-=O3NB[C-]2@@H]2NHC-/](3CC3)C)2)c1
add + at position 29,C6()c1ccc(CC+-=O3NB[C-]2@@H]2+NHC-/](3CC3)C)2)c1
remove - from position 33,C6()c1ccc(CC+-=O3NB[C-]2@@H]2+NHC/](3CC3)C)2)c1
remove ] from position 22,C6()c1ccc(CC+-=O3NB[C-2@@H]2+NHC/](3CC3)C)2)c1
remove - from position 13,C6()c1ccc(CC+=O3NB[C-2@@H]2+NHC/](3CC3)C)2)c1
remove / from position 31,C6()c1ccc(CC+=O3NB[C-2@@H]2+NHC](3CC3)C)2)c1
add C at position 3,C6(C)c1ccc(CC+=O3NB[C-2@@H]2+NHC](3CC3)C)2)c1
add + at position 42,C6(C)c1ccc(CC+=O3NB[C-2@@H]2+NHC](3CC3)C)2+)c1
remove - from position 21,C6(C)c1ccc(CC+=O3NB[C2@@H]2+NHC](3CC3)C)2+)c1
add ( at position 32,C6(C)c1ccc(CC+=O3NB[C2@@H]2+NHC]((3CC3)C)2+)c1
add C at position 33,C6(C)c1ccc(CC+=O3NB[C2@@H]2+NHC](C(3CC3)C)2+)c1
add N at position 12,C6(C)c1ccc(CNC+=O3NB[C2@@H]2+NHC](C(3CC3)C)2+)c1
replace 3 at position 17 with ),C6(C)c1ccc(CNC+=O)NB[C2@@H]2+NHC](C(3CC3)C)2+)c1
remove B from position 19,C6(C)c1ccc(CNC+=O)N[C2@@H]2+NHC](C(3CC3)C)2+)c1
add C at position 35,C6(C)c1ccc(CNC+=O)N[C2@@H]2+NHC](C(C3CC3)C)2+)c1
add C at position 2,C6C(C)c1ccc(CNC+=O)N[C2@@H]2+NHC](C(C3CC3)C)2+)c1
remove ) from position 43,C6C(C)c1ccc(CNC+=O)N[C2@@H]2+NHC](C(C3CC3)C2+)c1
add [ at position 29,C6C(C)c1ccc(CNC+=O)N[C2@@H]2+[NHC](C(C3CC3)C2+)c1
replace + at position 15 with (,C6C(C)c1ccc(CNC(=O)N[C2@@H]2+[NHC](C(C3CC3)C2+)c1
replace C at position 32 with +,C6C(C)c1ccc(CNC(=O)N[C2@@H]2+[NH+](C(C3CC3)C2+)c1
remove + from position 45,C6C(C)c1ccc(CNC(=O)N[C2@@H]2+[NH+](C(C3CC3)C2)c1
add c at position 9,C6C(C)c1cccc(CNC(=O)N[C2@@H]2+[NH+](C(C3CC3)C2)c1
add C at position 30,C6C(C)c1cccc(CNC(=O)N[C2@@H]2+C[NH+](C(C3CC3)C2)c1
remove 6 from position 1,CC(C)c1cccc(CNC(=O)N[C2@@H]2+C[NH+](C(C3CC3)C2)c1
remove 2 from position 22,CC(C)c1cccc(CNC(=O)N[C@@H]2+C[NH+](C(C3CC3)C2)c1
add N at position 5,CC(C)Nc1cccc(CNC(=O)N[C@@H]2+C[NH+](C(C3CC3)C2)c1
remove ( from position 37,CC(C)Nc1cccc(CNC(=O)N[C@@H]2+C[NH+](CC3CC3)C2)c1
replace + at position 28 with C,CC(C)Nc1cccc(CNC(=O)N[C@@H]2CC[NH+](CC3CC3)C2)c1
final: CC(C)Nc1cccc(CNC(=O)N[C@@H]2CC[NH+](CC3CC3)C2)c1,CC(C)Nc1cccc(CNC(=O)N[C@@H]2CC[NH+](CC3CC3)C2)c1
