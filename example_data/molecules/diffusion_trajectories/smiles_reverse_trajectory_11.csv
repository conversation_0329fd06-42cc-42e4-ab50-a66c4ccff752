log,state
initialize: ,
add S at position 0,S
add O at position 0,OS
add o at position 0,o<PERSON>
replace S at position 2 with [,oO[
add [ at position 1,o[O[
add 1 at position 1,o1[O[
replace [ at position 2 with 6,o16O[
remove 6 from position 2,o1O[
add I at position 4,o1O[I
replace I at position 4 with (,o1O[(
remove ( from position 4,o1O[
add 5 at position 2,o15O[
remove o from position 0,15O[
add F at position 2,15FO[
add / at position 3,15F/O[
add I at position 5,15F/OI[
add C at position 7,15F/OI[C
replace O at position 4 with 4,15F/4I[C
replace F at position 2 with B,15B/4I[C
remove 1 from position 0,5B/4I[C
add = at position 2,5B=/4I[C
replace I at position 5 with 3,5B=/43[C
add B at position 0,B5B=/43[C
add 6 at position 3,B5B6=/43[C
replace B at position 2 with o,B5o6=/43[C
replace = at position 4 with r,B5o6r/43[C
replace B at position 0 with 1,15o6r/43[C
replace C at position 9 with /,15o6r/43[/
replace [ at position 8 with ),15o6r/43)/
remove 6 from position 3,15or/43)/
replace 5 at position 1 with r,1ror/43)/
add - at position 6,1ror/4-3)/
remove r from position 1,1or/4-3)/
add H at position 7,1or/4-3H)/
add B at position 9,1or/4-3H)B/
add S at position 8,1or/4-3HS)B/
replace - at position 5 with 7,1or/473HS)B/
replace S at position 8 with s,1or/473Hs)B/
replace / at position 11 with c,1or/473Hs)Bc
remove s from position 8,1or/473H)Bc
add 5 at position 9,1or/473H)5Bc
replace r at position 2 with ],1o]/473H)5Bc
add 5 at position 10,1o]/473H)55Bc
replace / at position 3 with @,1o]@473H)55Bc
remove 5 from position 9,1o]@473H)5Bc
add s at position 12,1o]@473H)5Bcs
remove ) from position 8,1o]@473H5Bcs
add @ at position 9,1o]@473H5@Bcs
replace 4 at position 4 with (,1o]@(73H5@Bcs
replace o at position 1 with 7,17]@(73H5@Bcs
add 1 at position 6,17]@(713H5@Bcs
add ) at position 5,17]@()713H5@Bcs
remove 1 from position 0,7]@()713H5@Bcs
remove 7 from position 0,]@()713H5@Bcs
replace 7 at position 4 with (,]@()(13H5@Bcs
add ( at position 1,](@()(13H5@Bcs
remove H from position 8,](@()(135@Bcs
replace 3 at position 7 with r,](@()(1r5@Bcs
remove 5 from position 8,](@()(1r@Bcs
add / at position 9,](@()(1r@/Bcs
replace ) at position 4 with ],](@(](1r@/Bcs
add 4 at position 8,](@(](1r4@/Bcs
add o at position 4,](@(o](1r4@/Bcs
add 7 at position 10,](@(o](1r47@/Bcs
replace 4 at position 9 with ],](@(o](1r]7@/Bcs
replace 7 at position 10 with +,](@(o](1r]+@/Bcs
remove o from position 4,](@(](1r]+@/Bcs
remove B from position 12,](@(](1r]+@/cs
add C at position 1,]C(@(](1r]+@/cs
replace @ at position 11 with s,]C(@(](1r]+s/cs
add # at position 0,#]C(@(](1r]+s/cs
remove c from position 14,#]C(@(](1r]+s/s
add 4 at position 11,#]C(@(](1r]4+s/s
add ( at position 11,#]C(@(](1r](4+s/s
remove / from position 15,#]C(@(](1r](4+ss
add / at position 15,#]C(@(](1r](4+s/s
remove 4 from position 12,#]C(@(](1r](+s/s
add I at position 0,I#]C(@(](1r](+s/s
remove / from position 15,I#]C(@(](1r](+ss
add r at position 14,I#]C(@(](1r](+rss
remove ( from position 12,I#]C(@(](1r]+rss
add s at position 8,I#]C(@(]s(1r]+rss
add C at position 3,I#]CC(@(]s(1r]+rss
add o at position 18,I#]CC(@(]s(1r]+rsso
add l at position 13,I#]CC(@(]s(1rl]+rsso
replace ] at position 2 with l,I#lCC(@(]s(1rl]+rsso
add o at position 4,I#lCoC(@(]s(1rl]+rsso
add 6 at position 21,I#lCoC(@(]s(1rl]+rsso6
add F at position 9,I#lCoC(@(F]s(1rl]+rsso6
add c at position 8,I#lCoC(@c(F]s(1rl]+rsso6
add 5 at position 21,I#lCoC(@c(F]s(1rl]+rs5so6
add B at position 23,I#lCoC(@c(F]s(1rl]+rs5sBo6
replace ( at position 6 with 4,I#lCoC4@c(F]s(1rl]+rs5sBo6
add 3 at position 24,I#lCoC4@c(F]s(1rl]+rs5sB3o6
add = at position 1,I=#lCoC4@c(F]s(1rl]+rs5sB3o6
add 6 at position 0,6I=#lCoC4@c(F]s(1rl]+rs5sB3o6
add F at position 22,6I=#lCoC4@c(F]s(1rl]+rFs5sB3o6
remove l from position 4,6I=#CoC4@c(F]s(1rl]+rFs5sB3o6
remove I from position 1,6=#CoC4@c(F]s(1rl]+rFs5sB3o6
add s at position 10,6=#CoC4@c(sF]s(1rl]+rFs5sB3o6
remove s from position 24,6=#CoC4@c(sF]s(1rl]+rFs5B3o6
remove F from position 11,6=#CoC4@c(s]s(1rl]+rFs5B3o6
replace 4 at position 6 with n,6=#CoCn@c(s]s(1rl]+rFs5B3o6
add n at position 5,6=#ConCn@c(s]s(1rl]+rFs5B3o6
replace r at position 16 with N,6=#ConCn@c(s]s(1Nl]+rFs5B3o6
replace s at position 11 with 1,6=#ConCn@c(1]s(1Nl]+rFs5B3o6
add O at position 7,6=#ConCOn@c(1]s(1Nl]+rFs5B3o6
add c at position 28,6=#ConCOn@c(1]s(1Nl]+rFs5B3oc6
remove 1 from position 12,6=#ConCOn@c(]s(1Nl]+rFs5B3oc6
remove s from position 13,6=#ConCOn@c(](1Nl]+rFs5B3oc6
remove n from position 8,6=#ConCO@c(](1Nl]+rFs5B3oc6
add @ at position 14,6=#ConCO@c(](1@Nl]+rFs5B3oc6
remove 5 from position 22,6=#ConCO@c(](1@Nl]+rFsB3oc6
remove l from position 16,6=#ConCO@c(](1@N]+rFsB3oc6
remove c from position 9,6=#ConCO@(](1@N]+rFsB3oc6
remove 1 from position 12,6=#ConCO@(](@N]+rFsB3oc6
replace ] at position 14 with =,6=#ConCO@(](@N=+rFsB3oc6
replace 6 at position 23 with -,6=#ConCO@(](@N=+rFsB3oc-
replace o at position 4 with 3,6=#C3nCO@(](@N=+rFsB3oc-
replace s at position 18 with ),6=#C3nCO@(](@N=+rF)B3oc-
remove c from position 22,6=#C3nCO@(](@N=+rF)B3o-
add r at position 11,6=#C3nCO@(]r(@N=+rF)B3o-
replace 3 at position 21 with ],6=#C3nCO@(]r(@N=+rF)B]o-
replace = at position 15 with 1,6=#C3nCO@(]r(@N1+rF)B]o-
replace + at position 16 with c,6=#C3nCO@(]r(@N1crF)B]o-
replace @ at position 13 with ),6=#C3nCO@(]r()N1crF)B]o-
add N at position 3,6=#NC3nCO@(]r()N1crF)B]o-
add - at position 14,6=#NC3nCO@(]r(-)N1crF)B]o-
remove ] from position 23,6=#NC3nCO@(]r(-)N1crF)Bo-
replace r at position 19 with c,6=#NC3nCO@(]r(-)N1ccF)Bo-
add r at position 25,6=#NC3nCO@(]r(-)N1ccF)Bo-r
add O at position 14,6=#NC3nCO@(]r(O-)N1ccF)Bo-r
add ] at position 10,6=#NC3nCO@](]r(O-)N1ccF)Bo-r
add c at position 24,6=#NC3nCO@](]r(O-)N1ccF)cBo-r
replace ( at position 14 with C,6=#NC3nCO@](]rCO-)N1ccF)cBo-r
remove - from position 27,6=#NC3nCO@](]rCO-)N1ccF)cBor
replace 3 at position 5 with O,6=#NCOnCO@](]rCO-)N1ccF)cBor
remove O from position 8,6=#NCOnC@](]rCO-)N1ccF)cBor
add ( at position 14,6=#NCOnC@](]rC(O-)N1ccF)cBor
remove - from position 16,6=#NCOnC@](]rC(O)N1ccF)cBor
remove ] from position 11,6=#NCOnC@](rC(O)N1ccF)cBor
add C at position 4,6=#NCCOnC@](rC(O)N1ccF)cBor
add H at position 2,6=H#NCCOnC@](rC(O)N1ccF)cBor
remove H from position 2,6=#NCCOnC@](rC(O)N1ccF)cBor
add C at position 12,6=#NCCOnC@](CrC(O)N1ccF)cBor
add ( at position 22,6=#NCCOnC@](CrC(O)N1cc(F)cBor
replace # at position 2 with O,6=ONCCOnC@](CrC(O)N1cc(F)cBor
add C at position 0,C6=ONCCOnC@](CrC(O)N1cc(F)cBor
replace r at position 14 with ),C6=ONCCOnC@](C)C(O)N1cc(F)cBor
add c at position 20,C6=ONCCOnC@](C)C(O)Nc1cc(F)cBor
add c at position 22,C6=ONCCOnC@](C)C(O)Nc1ccc(F)cBor
replace N at position 4 with S,C6=OSCCOnC@](C)C(O)Nc1ccc(F)cBor
add # at position 17,C6=OSCCOnC@](C)C(#O)Nc1ccc(F)cBor
remove C from position 5,C6=OSCOnC@](C)C(#O)Nc1ccc(F)cBor
add + at position 29,C6=OSCOnC@](C)C(#O)Nc1ccc(F)c+Bor
replace S at position 4 with C,C6=OCCOnC@](C)C(#O)Nc1ccc(F)c+Bor
remove o from position 31,C6=OCCOnC@](C)C(#O)Nc1ccc(F)c+Br
remove n from position 7,C6=OCCOC@](C)C(#O)Nc1ccc(F)c+Br
add H at position 9,C6=OCCOC@H](C)C(#O)Nc1ccc(F)c+Br
add 1 at position 30,C6=OCCOC@H](C)C(#O)Nc1ccc(F)c+1Br
remove 6 from position 1,C=OCCOC@H](C)C(#O)Nc1ccc(F)c+1Br
replace # at position 15 with =,C=OCCOC@H](C)C(=O)Nc1ccc(F)c+1Br
add [ at position 6,C=OCCO[C@H](C)C(=O)Nc1ccc(F)c+1Br
remove O from position 2,C=CCO[C@H](C)C(=O)Nc1ccc(F)c+1Br
replace + at position 28 with c,C=CCO[C@H](C)C(=O)Nc1ccc(F)cc1Br
final: C=CCO[C@H](C)C(=O)Nc1ccc(F)cc1Br,C=CCO[C@H](C)C(=O)Nc1ccc(F)cc1Br
