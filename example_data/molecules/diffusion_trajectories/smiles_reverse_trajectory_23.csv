log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove C from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add S at position 6,oH3H-]SN)
add [ at position 2,oH[3H-]SN)
add O at position 3,oH[O3H-]SN)
replace S at position 8 with n,oH[O3H-]nN)
add ) at position 2,oH)[O3H-]nN)
add 4 at position 2,oH4)[O3H-]nN)
replace [ at position 4 with H,oH4)HO3H-]nN)
add ] at position 5,oH4)H]O3H-]nN)
add O at position 2,oHO4)H]O3H-]nN)
remove H from position 9,oHO4)H]O3-]nN)
add 6 at position 13,oHO4)H]O3-]nN6)
replace H at position 1 with 6,o6O4)H]O3-]nN6)
remove H from position 5,o6O4)]O3-]nN6)
replace 4 at position 3 with r,o6Or)]O3-]nN6)
add S at position 6,o6Or)]SO3-]nN6)
add s at position 11,o6Or)]SO3-]snN6)
remove 6 from position 14,o6Or)]SO3-]snN)
replace ] at position 10 with H,o6Or)]SO3-HsnN)
remove O from position 2,o6r)]SO3-HsnN)
add c at position 11,o6r)]SO3-HscnN)
replace N at position 13 with 1,o6r)]SO3-Hscn1)
add S at position 6,o6r)]SSO3-Hscn1)
add l at position 2,o6lr)]SSO3-Hscn1)
add F at position 11,o6lr)]SSO3-FHscn1)
replace o at position 0 with S,S6lr)]SSO3-FHscn1)
remove S from position 7,S6lr)]SO3-FHscn1)
add - at position 10,S6lr)]SO3--FHscn1)
remove l from position 2,S6r)]SO3--FHscn1)
add r at position 1,Sr6r)]SO3--FHscn1)
add ) at position 12,Sr6r)]SO3--F)Hscn1)
replace F at position 11 with 4,Sr6r)]SO3--4)Hscn1)
add C at position 19,Sr6r)]SO3--4)Hscn1)C
add 1 at position 2,Sr16r)]SO3--4)Hscn1)C
add C at position 1,SCr16r)]SO3--4)Hscn1)C
add C at position 21,SCr16r)]SO3--4)Hscn1)CC
remove r from position 2,SC16r)]SO3--4)Hscn1)CC
add B at position 14,SC16r)]SO3--4)BHscn1)CC
add C at position 19,SC16r)]SO3--4)BHscnC1)CC
add o at position 17,SC16r)]SO3--4)BHsocnC1)CC
replace - at position 10 with 6,SC16r)]SO36-4)BHsocnC1)CC
add r at position 12,SC16r)]SO36-r4)BHsocnC1)CC
replace C at position 24 with r,SC16r)]SO36-r4)BHsocnC1)rC
replace 1 at position 22 with ),SC16r)]SO36-r4)BHsocnC))rC
remove s from position 17,SC16r)]SO36-r4)BHocnC))rC
add n at position 19,SC16r)]SO36-r4)BHocnnC))rC
replace r at position 4 with @,SC16@)]SO36-r4)BHocnnC))rC
add / at position 21,SC16@)]SO36-r4)BHocnn/C))rC
remove B from position 15,SC16@)]SO36-r4)Hocnn/C))rC
add 2 at position 18,SC16@)]SO36-r4)Hoc2nn/C))rC
replace S at position 0 with 7,7C16@)]SO36-r4)Hoc2nn/C))rC
add ) at position 20,7C16@)]SO36-r4)Hoc2n)n/C))rC
add l at position 11,7C16@)]SO36l-r4)Hoc2n)n/C))rC
add ) at position 29,7C16@)]SO36l-r4)Hoc2n)n/C))rC)
replace 3 at position 9 with =,7C16@)]SO=6l-r4)Hoc2n)n/C))rC)
replace ) at position 29 with 7,7C16@)]SO=6l-r4)Hoc2n)n/C))rC7
add = at position 13,7C16@)]SO=6l-=r4)Hoc2n)n/C))rC7
add r at position 20,7C16@)]SO=6l-=r4)Hocr2n)n/C))rC7
remove 1 from position 2,7C6@)]SO=6l-=r4)Hocr2n)n/C))rC7
remove 7 from position 0,C6@)]SO=6l-=r4)Hocr2n)n/C))rC7
replace 6 at position 8 with =,C6@)]SO==l-=r4)Hocr2n)n/C))rC7
add ) at position 3,C6@))]SO==l-=r4)Hocr2n)n/C))rC7
remove H from position 16,C6@))]SO==l-=r4)ocr2n)n/C))rC7
replace 4 at position 14 with 1,C6@))]SO==l-=r1)ocr2n)n/C))rC7
add B at position 10,C6@))]SO==Bl-=r1)ocr2n)n/C))rC7
add 4 at position 13,C6@))]SO==Bl-4=r1)ocr2n)n/C))rC7
replace ) at position 17 with c,C6@))]SO==Bl-4=r1cocr2n)n/C))rC7
add ( at position 32,C6@))]SO==Bl-4=r1cocr2n)n/C))rC7(
add 4 at position 18,C6@))]SO==Bl-4=r1c4ocr2n)n/C))rC7(
add 6 at position 21,C6@))]SO==Bl-4=r1c4oc6r2n)n/C))rC7(
replace 4 at position 18 with r,C6@))]SO==Bl-4=r1croc6r2n)n/C))rC7(
replace 6 at position 21 with c,C6@))]SO==Bl-4=r1croccr2n)n/C))rC7(
remove ) from position 30,C6@))]SO==Bl-4=r1croccr2n)n/C)rC7(
add 3 at position 6,C6@))]3SO==Bl-4=r1croccr2n)n/C)rC7(
add ) at position 29,C6@))]3SO==Bl-4=r1croccr2n)n/)C)rC7(
add C at position 30,C6@))]3SO==Bl-4=r1croccr2n)n/)CC)rC7(
remove r from position 33,C6@))]3SO==Bl-4=r1croccr2n)n/)CC)C7(
add B at position 22,C6@))]3SO==Bl-4=r1crocBcr2n)n/)CC)C7(
add F at position 23,C6@))]3SO==Bl-4=r1crocBFcr2n)n/)CC)C7(
remove / from position 30,C6@))]3SO==Bl-4=r1crocBFcr2n)n)CC)C7(
add B at position 30,C6@))]3SO==Bl-4=r1crocBFcr2n)nB)CC)C7(
add c at position 19,C6@))]3SO==Bl-4=r1ccrocBFcr2n)nB)CC)C7(
add n at position 4,C6@)n)]3SO==Bl-4=r1ccrocBFcr2n)nB)CC)C7(
replace = at position 10 with 5,C6@)n)]3SO5=Bl-4=r1ccrocBFcr2n)nB)CC)C7(
remove 2 from position 28,C6@)n)]3SO5=Bl-4=r1ccrocBFcrn)nB)CC)C7(
remove 7 from position 37,C6@)n)]3SO5=Bl-4=r1ccrocBFcrn)nB)CC)C(
add N at position 17,C6@)n)]3SO5=Bl-4=Nr1ccrocBFcrn)nB)CC)C(
add = at position 7,C6@)n)]=3SO5=Bl-4=Nr1ccrocBFcrn)nB)CC)C(
add ) at position 36,C6@)n)]=3SO5=Bl-4=Nr1ccrocBFcrn)nB)C)C)C(
replace = at position 17 with F,C6@)n)]=3SO5=Bl-4FNr1ccrocBFcrn)nB)C)C)C(
replace ) at position 34 with 1,C6@)n)]=3SO5=Bl-4FNr1ccrocBFcrn)nB1C)C)C(
add ( at position 36,C6@)n)]=3SO5=Bl-4FNr1ccrocBFcrn)nB1C()C)C(
add c at position 19,C6@)n)]=3SO5=Bl-4FNcr1ccrocBFcrn)nB1C()C)C(
replace F at position 17 with n,C6@)n)]=3SO5=Bl-4nNcr1ccrocBFcrn)nB1C()C)C(
remove r from position 24,C6@)n)]=3SO5=Bl-4nNcr1ccocBFcrn)nB1C()C)C(
add F at position 20,C6@)n)]=3SO5=Bl-4nNcFr1ccocBFcrn)nB1C()C)C(
replace @ at position 2 with 2,C62)n)]=3SO5=Bl-4nNcFr1ccocBFcrn)nB1C()C)C(
add ) at position 9,C62)n)]=3)SO5=Bl-4nNcFr1ccocBFcrn)nB1C()C)C(
remove ( from position 43,C62)n)]=3)SO5=Bl-4nNcFr1ccocBFcrn)nB1C()C)C
add r at position 10,C62)n)]=3)rSO5=Bl-4nNcFr1ccocBFcrn)nB1C()C)C
remove ) from position 34,C62)n)]=3)rSO5=Bl-4nNcFr1ccocBFcrnnB1C()C)C
remove r from position 10,C62)n)]=3)SO5=Bl-4nNcFr1ccocBFcrnnB1C()C)C
add C at position 8,C62)n)]=C3)SO5=Bl-4nNcFr1ccocBFcrnnB1C()C)C
remove B from position 35,C62)n)]=C3)SO5=Bl-4nNcFr1ccocBFcrnn1C()C)C
remove F from position 22,C62)n)]=C3)SO5=Bl-4nNcr1ccocBFcrnn1C()C)C
replace 5 at position 13 with 3,C62)n)]=C3)SO3=Bl-4nNcr1ccocBFcrnn1C()C)C
add - at position 11,C62)n)]=C3)-SO3=Bl-4nNcr1ccocBFcrnn1C()C)C
replace r at position 32 with (,C62)n)]=C3)-SO3=Bl-4nNcr1ccocBFc(nn1C()C)C
remove r from position 23,C62)n)]=C3)-SO3=Bl-4nNc1ccocBFc(nn1C()C)C
replace F at position 29 with /,C62)n)]=C3)-SO3=Bl-4nNc1ccocB/c(nn1C()C)C
remove S from position 12,C62)n)]=C3)-O3=Bl-4nNc1ccocB/c(nn1C()C)C
add ) at position 12,C62)n)]=C3)-)O3=Bl-4nNc1ccocB/c(nn1C()C)C
add I at position 4,C62)In)]=C3)-)O3=Bl-4nNc1ccocB/c(nn1C()C)C
replace I at position 4 with H,C62)Hn)]=C3)-)O3=Bl-4nNc1ccocB/c(nn1C()C)C
replace 4 at position 20 with ],C62)Hn)]=C3)-)O3=Bl-]nNc1ccocB/c(nn1C()C)C
add S at position 5,C62)HSn)]=C3)-)O3=Bl-]nNc1ccocB/c(nn1C()C)C
replace 2 at position 2 with I,C6I)HSn)]=C3)-)O3=Bl-]nNc1ccocB/c(nn1C()C)C
add C at position 3,C6IC)HSn)]=C3)-)O3=Bl-]nNc1ccocB/c(nn1C()C)C
replace ) at position 4 with @,C6IC@HSn)]=C3)-)O3=Bl-]nNc1ccocB/c(nn1C()C)C
replace l at position 20 with O,C6IC@HSn)]=C3)-)O3=BO-]nNc1ccocB/c(nn1C()C)C
add 3 at position 17,C6IC@HSn)]=C3)-)O33=BO-]nNc1ccocB/c(nn1C()C)C
add @ at position 10,C6IC@HSn)]@=C3)-)O33=BO-]nNc1ccocB/c(nn1C()C)C
remove S from position 6,C6IC@Hn)]@=C3)-)O33=BO-]nNc1ccocB/c(nn1C()C)C
replace O at position 16 with C,C6IC@Hn)]@=C3)-)C33=BO-]nNc1ccocB/c(nn1C()C)C
replace n at position 24 with 2,C6IC@Hn)]@=C3)-)C33=BO-]2Nc1ccocB/c(nn1C()C)C
replace n at position 6 with ],C6IC@H])]@=C3)-)C33=BO-]2Nc1ccocB/c(nn1C()C)C
add C at position 7,C6IC@H]C)]@=C3)-)C33=BO-]2Nc1ccocB/c(nn1C()C)C
remove = from position 11,C6IC@H]C)]@C3)-)C33=BO-]2Nc1ccocB/c(nn1C()C)C
replace 3 at position 18 with O,C6IC@H]C)]@C3)-)C3O=BO-]2Nc1ccocB/c(nn1C()C)C
add - at position 33,C6IC@H]C)]@C3)-)C3O=BO-]2Nc1ccocB-/c(nn1C()C)C
replace @ at position 10 with N,C6IC@H]C)]NC3)-)C3O=BO-]2Nc1ccocB-/c(nn1C()C)C
replace o at position 30 with (,C6IC@H]C)]NC3)-)C3O=BO-]2Nc1cc(cB-/c(nn1C()C)C
replace ( at position 30 with 2,C6IC@H]C)]NC3)-)C3O=BO-]2Nc1cc2cB-/c(nn1C()C)C
add ) at position 37,C6IC@H]C)]NC3)-)C3O=BO-]2Nc1cc2cB-/c()nn1C()C)C
add ) at position 22,C6IC@H]C)]NC3)-)C3O=BO)-]2Nc1cc2cB-/c()nn1C()C)C
replace I at position 2 with C,C6CC@H]C)]NC3)-)C3O=BO)-]2Nc1cc2cB-/c()nn1C()C)C
remove ] from position 9,C6CC@H]C)NC3)-)C3O=BO)-]2Nc1cc2cB-/c()nn1C()C)C
replace 3 at position 11 with =,C6CC@H]C)NC=)-)C3O=BO)-]2Nc1cc2cB-/c()nn1C()C)C
remove O from position 17,C6CC@H]C)NC=)-)C3=BO)-]2Nc1cc2cB-/c()nn1C()C)C
add - at position 29,C6CC@H]C)NC=)-)C3=BO)-]2Nc1cc-2cB-/c()nn1C()C)C
remove - from position 33,C6CC@H]C)NC=)-)C3=BO)-]2Nc1cc-2cB/c()nn1C()C)C
remove ] from position 22,C6CC@H]C)NC=)-)C3=BO)-2Nc1cc-2cB/c()nn1C()C)C
remove - from position 13,C6CC@H]C)NC=))C3=BO)-2Nc1cc-2cB/c()nn1C()C)C
remove / from position 31,C6CC@H]C)NC=))C3=BO)-2Nc1cc-2cBc()nn1C()C)C
add [ at position 3,C6C[C@H]C)NC=))C3=BO)-2Nc1cc-2cBc()nn1C()C)C
add + at position 42,C6C[C@H]C)NC=))C3=BO)-2Nc1cc-2cBc()nn1C()C+)C
remove - from position 21,C6C[C@H]C)NC=))C3=BO)2Nc1cc-2cBc()nn1C()C+)C
add c at position 32,C6C[C@H]C)NC=))C3=BO)2Nc1cc-2cBcc()nn1C()C+)C
add c at position 33,C6C[C@H]C)NC=))C3=BO)2Nc1cc-2cBccc()nn1C()C+)C
add ( at position 12,C6C[C@H]C)NC(=))C3=BO)2Nc1cc-2cBccc()nn1C()C+)C
replace 3 at position 17 with (,C6C[C@H]C)NC(=))C(=BO)2Nc1cc-2cBccc()nn1C()C+)C
remove B from position 19,C6C[C@H]C)NC(=))C(=O)2Nc1cc-2cBccc()nn1C()C+)C
add 2 at position 35,C6C[C@H]C)NC(=))C(=O)2Nc1cc-2cBccc(2)nn1C()C+)C
add O at position 2,C6OC[C@H]C)NC(=))C(=O)2Nc1cc-2cBccc(2)nn1C()C+)C
remove ) from position 43,C6OC[C@H]C)NC(=))C(=O)2Nc1cc-2cBccc(2)nn1C(C+)C
add c at position 29,C6OC[C@H]C)NC(=))C(=O)2Nc1cc-c2cBccc(2)nn1C(C+)C
replace ) at position 15 with O,C6OC[C@H]C)NC(=O)C(=O)2Nc1cc-c2cBccc(2)nn1C(C+)C
replace B at position 32 with c,C6OC[C@H]C)NC(=O)C(=O)2Nc1cc-c2ccccc(2)nn1C(C+)C
remove + from position 45,C6OC[C@H]C)NC(=O)C(=O)2Nc1cc-c2ccccc(2)nn1C(C)C
add ( at position 9,C6OC[C@H](C)NC(=O)C(=O)2Nc1cc-c2ccccc(2)nn1C(C)C
add - at position 30,C6OC[C@H](C)NC(=O)C(=O)2Nc1cc--c2ccccc(2)nn1C(C)C
remove 6 from position 1,COC[C@H](C)NC(=O)C(=O)2Nc1cc--c2ccccc(2)nn1C(C)C
remove 2 from position 22,COC[C@H](C)NC(=O)C(=O)Nc1cc--c2ccccc(2)nn1C(C)C
add @ at position 5,COC[C@@H](C)NC(=O)C(=O)Nc1cc--c2ccccc(2)nn1C(C)C
remove ( from position 37,COC[C@@H](C)NC(=O)C(=O)Nc1cc--c2ccccc2)nn1C(C)C
replace - at position 28 with (,COC[C@@H](C)NC(=O)C(=O)Nc1cc(-c2ccccc2)nn1C(C)C
final: COC[C@@H](C)NC(=O)C(=O)Nc1cc(-c2ccccc2)nn1C(C)C,COC[C@@H](C)NC(=O)C(=O)Nc1cc(-c2ccccc2)nn1C(C)C
