log,state
initialize: CC(C)c1nc(CSCc2ccnn2C)no1,CC(C)c1nc(CSCc2ccnn2C)no1
replace 2 at position 14 with +,CC(C)c1nc(CSCc+ccnn2C)no1
add H at position 25,CC(C)c1nc(CSCc+ccnn2C)no1H
add ) at position 22,CC(C)c1nc(CSCc+ccnn2C))no1H
add 2 at position 11,CC(C)c1nc(C2SCc+ccnn2C))no1H
add # at position 25,CC(C)c1nc(C2SCc+ccnn2C))n#o1H
remove n from position 19,CC(C)c1nc(C2SCc+ccn2C))n#o1H
remove c from position 14,CC(C)c1nc(C2SC+ccn2C))n#o1H
add S at position 5,CC(C)Sc1nc(C2SC+ccn2C))n#o1H
add C at position 0,CCC(C)Sc1nc(C2SC+ccn2C))n#o1H
remove c from position 7,CCC(C)S1nc(C2SC+ccn2C))n#o1H
add r at position 17,CCC(C)S1nc(C2SC+crcn2C))n#o1H
remove n from position 19,CCC(C)S1nc(C2SC+crc2C))n#o1H
remove c from position 16,CCC(C)S1nc(C2SC+rc2C))n#o1H
remove n from position 8,CCC(C)S1c(C2SC+rc2C))n#o1H
replace C at position 2 with O,CCO(C)S1c(C2SC+rc2C))n#o1H
remove 2 from position 11,CCO(C)S1c(CSC+rc2C))n#o1H
remove n from position 20,CCO(C)S1c(CSC+rc2C))#o1H
replace r at position 14 with o,CCO(C)S1c(CSC+oc2C))#o1H
remove C from position 0,CO(C)S1c(CSC+oc2C))#o1H
replace ( at position 2 with #,CO#C)S1c(CSC+oc2C))#o1H
remove H from position 22,CO#C)S1c(CSC+oc2C))#o1
remove + from position 12,CO#C)S1c(CSCoc2C))#o1
add H at position 2,COH#C)S1c(CSCoc2C))#o1
remove H from position 2,CO#C)S1c(CSCoc2C))#o1
remove ) from position 4,CO#CS1c(CSCoc2C))#o1
add ] at position 11,CO#CS1c(CSC]oc2C))#o1
add - at position 16,CO#CS1c(CSC]oc2C-))#o1
remove 2 from position 14,CO#CS1c(CSC]ocC-))#o1
add O at position 8,CO#CS1c(OCSC]ocC-))#o1
replace 1 at position 5 with 4,CO#CS4c(OCSC]ocC-))#o1
add ] at position 4,CO#C]S4c(OCSC]ocC-))#o1
replace O at position 1 with H,CH#C]S4c(OCSC]ocC-))#o1
remove S from position 11,CH#C]S4c(OCC]ocC-))#o1
remove ) from position 18,CH#C]S4c(OCC]ocC-)#o1
replace C at position 15 with (,CH#C]S4c(OCC]oc(-)#o1
replace ( at position 15 with o,CH#C]S4c(OCC]oco-)#o1
replace S at position 5 with @,CH#C]@4c(OCC]oco-)#o1
remove - from position 16,CH#C]@4c(OCC]oco)#o1
replace O at position 9 with 3,CH#C]@4c(3CC]oco)#o1
add = at position 5,CH#C]=@4c(3CC]oco)#o1
remove C from position 3,CH#]=@4c(3CC]oco)#o1
replace ] at position 3 with n,CH#n=@4c(3CC]oco)#o1
replace ] at position 12 with n,CH#n=@4c(3CCnoco)#o1
replace ( at position 8 with O,CH#n=@4cO3CCnoco)#o1
add S at position 3,CH#Sn=@4cO3CCnoco)#o1
remove = from position 5,CH#Sn@4cO3CCnoco)#o1
remove O from position 8,CH#Sn@4c3CCnoco)#o1
replace C at position 10 with l,CH#Sn@4c3Clnoco)#o1
replace # at position 2 with +,CH+Sn@4c3Clnoco)#o1
remove H from position 1,C+Sn@4c3Clnoco)#o1
replace + at position 1 with 3,C3Sn@4c3Clnoco)#o1
remove S from position 2,C3n@4c3Clnoco)#o1
replace o at position 10 with 4,C3n@4c3Cln4co)#o1
replace n at position 2 with H,C3H@4c3Cln4co)#o1
remove H from position 2,C3@4c3Cln4co)#o1
remove C from position 6,C3@4c3ln4co)#o1
add S at position 6,C3@4c3Sln4co)#o1
replace o at position 14 with C,C3@4c3Sln4co)#C1
add r at position 11,C3@4c3Sln4cro)#C1
replace 1 at position 16 with r,C3@4c3Sln4cro)#Cr
remove 3 from position 5,C3@4cSln4cro)#Cr
replace l at position 6 with 4,C3@4cS4n4cro)#Cr
add F at position 11,C3@4cS4n4crFo)#Cr
add B at position 17,C3@4cS4n4crFo)#CrB
remove c from position 4,C3@4S4n4crFo)#CrB
add r at position 5,C3@4Sr4n4crFo)#CrB
add ) at position 17,C3@4Sr4n4crFo)#Cr)B
remove r from position 5,C3@4S4n4crFo)#Cr)B
add N at position 1,CN3@4S4n4crFo)#Cr)B
add r at position 4,CN3@r4S4n4crFo)#Cr)B
add 7 at position 13,CN3@r4S4n4crF7o)#Cr)B
remove S from position 6,CN3@r44n4crF7o)#Cr)B
replace 7 at position 12 with s,CN3@r44n4crFso)#Cr)B
replace 4 at position 8 with H,CN3@r44nHcrFso)#Cr)B
remove c from position 9,CN3@r44nHrFso)#Cr)B
remove B from position 18,CN3@r44nHrFso)#Cr)
replace ) at position 17 with +,CN3@r44nHrFso)#Cr+
replace H at position 8 with =,CN3@r44n=rFso)#Cr+
remove = from position 8,CN3@r44nrFso)#Cr+
remove ) from position 12,CN3@r44nrFso#Cr+
remove F from position 9,CN3@r44nrso#Cr+
remove s from position 9,CN3@r44nro#Cr+
remove 4 from position 5,CN3@r4nro#Cr+
replace r at position 7 with 2,CN3@r4n2o#Cr+
replace 3 at position 2 with =,CN=@r4n2o#Cr+
remove N from position 1,C=@r4n2o#Cr+
remove 4 from position 4,C=@rn2o#Cr+
remove + from position 10,C=@rn2o#Cr
replace @ at position 2 with 7,C=7rn2o#Cr
remove 7 from position 2,C=rn2o#Cr
remove o from position 5,C=rn2#Cr
remove # from position 5,C=rn2Cr
add c at position 7,C=rn2Crc
remove C from position 0,=rn2Crc
replace r at position 5 with @,=rn2C@c
remove = from position 0,rn2C@c
add B at position 6,rn2C@cB
add o at position 2,rno2C@cB
replace @ at position 5 with 6,rno2C6cB
replace C at position 4 with 4,rno246cB
remove 6 from position 5,rno24cB
remove o from position 2,rn24cB
remove c from position 4,rn24B
replace 2 at position 2 with ),rn)4B
remove B from position 4,rn)4
add 5 at position 4,rn)45
replace 4 at position 3 with 3,rn)35
add H at position 4,rn)3H5
remove r from position 0,n)3H5
replace 3 at position 2 with 7,n)7H5
add 7 at position 0,7n)7H5
add 1 at position 0,17n)7H5
remove n from position 2,17)7H5
remove 7 from position 3,17)H5
replace 1 at position 0 with o,o7)H5
replace ) at position 2 with 4,o74H5
remove 5 from position 4,o74H
add ) at position 4,o74H)
remove o from position 0,74H)
remove H from position 2,74)
replace 7 at position 0 with /,/4)
remove ) from position 2,/4
replace / at position 0 with r,r4
remove r from position 0,4
add S at position 0,S4
add ] at position 2,S4]
remove 4 from position 1,S]
replace ] at position 1 with -,S-
remove - from position 1,S
add r at position 0,rS
remove S from position 1,r
replace r at position 0 with 5,5
add 6 at position 0,65
replace 5 at position 1 with I,6I
remove 6 from position 0,I
add l at position 0,lI
remove I from position 1,l
add S at position 0,Sl
replace S at position 0 with o,ol
remove l from position 1,o
remove o from position 0,
final: ,
