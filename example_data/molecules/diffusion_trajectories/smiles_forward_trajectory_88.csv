log,state
initialize: Cc1[nH]c(=O)c(C(=O)N2CCN(c3ccccc3)C(=O)C2)c(C)c1C,Cc1[nH]c(=O)c(C(=O)N2CCN(c3ccccc3)C(=O)C2)c(C)c1C
replace c at position 28 with +,Cc1[nH]c(=O)c(C(=O)N2CCN(c3c+ccc3)C(=O)C2)c(C)c1C
add ( at position 37,Cc1[nH]c(=O)c(C(=O)N2CCN(c3c+ccc3)C(=(O)C2)c(C)c1C
remove H from position 5,Cc1[n]c(=O)c(C(=O)N2CCN(c3c+ccc3)C(=(O)C2)c(C)c1C
add 2 at position 22,Cc1[n]c(=O)c(C(=O)N2CC2N(c3c+ccc3)C(=(O)C2)c(C)c1C
add # at position 50,Cc1[n]c(=O)c(C(=O)N2CC2N(c3c+ccc3)C(=(O)C2)c(C)c1C#
remove ) from position 39,Cc1[n]c(=O)c(C(=O)N2CC2N(c3c+ccc3)C(=(OC2)c(C)c1C#
remove c from position 29,Cc1[n]c(=O)c(C(=O)N2CC2N(c3c+cc3)C(=(OC2)c(C)c1C#
add S at position 11,Cc1[n]c(=O)Sc(C(=O)N2CC2N(c3c+cc3)C(=(OC2)c(C)c1C#
add C at position 0,CCc1[n]c(=O)Sc(C(=O)N2CC2N(c3c+cc3)C(=(OC2)c(C)c1C#
remove C from position 15,CCc1[n]c(=O)Sc((=O)N2CC2N(c3c+cc3)C(=(OC2)c(C)c1C#
add r at position 34,CCc1[n]c(=O)Sc((=O)N2CC2N(c3c+cc3)rC(=(OC2)c(C)c1C#
remove ( from position 38,CCc1[n]c(=O)Sc((=O)N2CC2N(c3c+cc3)rC(=OC2)c(C)c1C#
remove 3 from position 32,CCc1[n]c(=O)Sc((=O)N2CC2N(c3c+cc)rC(=OC2)c(C)c1C#
remove O from position 17,CCc1[n]c(=O)Sc((=)N2CC2N(c3c+cc)rC(=OC2)c(C)c1C#
replace [ at position 4 with N,CCc1Nn]c(=O)Sc((=)N2CC2N(c3c+cc)rC(=OC2)c(C)c1C#
remove 2 from position 22,CCc1Nn]c(=O)Sc((=)N2CCN(c3c+cc)rC(=OC2)c(C)c1C#
remove C from position 41,CCc1Nn]c(=O)Sc((=)N2CCN(c3c+cc)rC(=OC2)c()c1C#
replace c at position 29 with r,CCc1Nn]c(=O)Sc((=)N2CCN(c3c+cr)rC(=OC2)c()c1C#
remove C from position 1,Cc1Nn]c(=O)Sc((=)N2CCN(c3c+cr)rC(=OC2)c()c1C#
replace n at position 4 with #,Cc1N#]c(=O)Sc((=)N2CCN(c3c+cr)rC(=OC2)c()c1C#
remove r from position 30,Cc1N#]c(=O)Sc((=)N2CCN(c3c+cr)C(=OC2)c()c1C#
remove ) from position 10,Cc1N#]c(=OSc((=)N2CCN(c3c+cr)C(=OC2)c()c1C#
add B at position 37,Cc1N#]c(=OSc((=)N2CCN(c3c+cr)C(=OC2)cB()c1C#
add 1 at position 35,Cc1N#]c(=OSc((=)N2CCN(c3c+cr)C(=OC21)cB()c1C#
add ] at position 22,Cc1N#]c(=OSc((=)N2CCN(]c3c+cr)C(=OC21)cB()c1C#
add - at position 33,Cc1N#]c(=OSc((=)N2CCN(]c3c+cr)C(=-OC21)cB()c1C#
remove ) from position 29,Cc1N#]c(=OSc((=)N2CCN(]c3c+crC(=-OC21)cB()c1C#
add O at position 17,Cc1N#]c(=OSc((=)NO2CCN(]c3c+crC(=-OC21)cB()c1C#
replace c at position 11 with 3,Cc1N#]c(=OS3((=)NO2CCN(]c3c+crC(=-OC21)cB()c1C#
add ] at position 9,Cc1N#]c(=]OS3((=)NO2CCN(]c3c+crC(=-OC21)cB()c1C#
replace 1 at position 2 with I,CcIN#]c(=]OS3((=)NO2CCN(]c3c+crC(=-OC21)cB()c1C#
remove N from position 22,CcIN#]c(=]OS3((=)NO2CC(]c3c+crC(=-OC21)cB()c1C#
remove 1 from position 37,CcIN#]c(=]OS3((=)NO2CC(]c3c+crC(=-OC2)cB()c1C#
replace C at position 30 with (,CcIN#]c(=]OS3((=)NO2CC(]c3c+cr((=-OC2)cB()c1C#
replace ( at position 30 with o,CcIN#]c(=]OS3((=)NO2CC(]c3c+cro(=-OC2)cB()c1C#
replace O at position 10 with @,CcIN#]c(=]@S3((=)NO2CC(]c3c+cro(=-OC2)cB()c1C#
remove - from position 33,CcIN#]c(=]@S3((=)NO2CC(]c3c+cro(=OC2)cB()c1C#
replace O at position 18 with 3,CcIN#]c(=]@S3((=)N32CC(]c3c+cro(=OC2)cB()c1C#
add = at position 11,CcIN#]c(=]@=S3((=)N32CC(]c3c+cro(=OC2)cB()c1C#
remove ( from position 7,CcIN#]c=]@=S3((=)N32CC(]c3c+cro(=OC2)cB()c1C#
replace c at position 6 with n,CcIN#]n=]@=S3((=)N32CC(]c3c+cro(=OC2)cB()c1C#
replace c at position 24 with n,CcIN#]n=]@=S3((=)N32CC(]n3c+cro(=OC2)cB()c1C#
replace ) at position 16 with O,CcIN#]n=]@=S3((=ON32CC(]n3c+cro(=OC2)cB()c1C#
add S at position 6,CcIN#]Sn=]@=S3((=ON32CC(]n3c+cro(=OC2)cB()c1C#
remove @ from position 10,CcIN#]Sn=]=S3((=ON32CC(]n3c+cro(=OC2)cB()c1C#
remove N from position 17,CcIN#]Sn=]=S3((=O32CC(]n3c+cro(=OC2)cB()c1C#
replace C at position 20 with l,CcIN#]Sn=]=S3((=O32Cl(]n3c+cro(=OC2)cB()c1C#
replace # at position 4 with +,CcIN+]Sn=]=S3((=O32Cl(]n3c+cro(=OC2)cB()c1C#
remove N from position 3,CcI+]Sn=]=S3((=O32Cl(]n3c+cro(=OC2)cB()c1C#
replace I at position 2 with 2,Cc2+]Sn=]=S3((=O32Cl(]n3c+cro(=OC2)cB()c1C#
remove S from position 5,Cc2+]n=]=S3((=O32Cl(]n3c+cro(=OC2)cB()c1C#
replace ] at position 20 with 4,Cc2+]n=]=S3((=O32Cl(4n3c+cro(=OC2)cB()c1C#
replace ] at position 4 with H,Cc2+Hn=]=S3((=O32Cl(4n3c+cro(=OC2)cB()c1C#
remove H from position 4,Cc2+n=]=S3((=O32Cl(4n3c+cro(=OC2)cB()c1C#
remove = from position 12,Cc2+n=]=S3((O32Cl(4n3c+cro(=OC2)cB()c1C#
add S at position 12,Cc2+n=]=S3((SO32Cl(4n3c+cro(=OC2)cB()c1C#
replace O at position 29 with C,Cc2+n=]=S3((SO32Cl(4n3c+cro(=CC2)cB()c1C#
add r at position 23,Cc2+n=]=S3((SO32Cl(4n3cr+cro(=CC2)cB()c1C#
replace 2 at position 32 with r,Cc2+n=]=S3((SO32Cl(4n3cr+cro(=CCr)cB()c1C#
remove ( from position 11,Cc2+n=]=S3(SO32Cl(4n3cr+cro(=CCr)cB()c1C#
replace 3 at position 13 with 5,Cc2+n=]=S3(SO52Cl(4n3cr+cro(=CCr)cB()c1C#
add F at position 22,Cc2+n=]=S3(SO52Cl(4n3cFr+cro(=CCr)cB()c1C#
add B at position 35,Cc2+n=]=S3(SO52Cl(4n3cFr+cro(=CCr)cBB()c1C#
remove S from position 8,Cc2+n=]=3(SO52Cl(4n3cFr+cro(=CCr)cBB()c1C#
add r at position 10,Cc2+n=]=3(rSO52Cl(4n3cFr+cro(=CCr)cBB()c1C#
add ) at position 34,Cc2+n=]=3(rSO52Cl(4n3cFr+cro(=CCr))cBB()c1C#
remove r from position 10,Cc2+n=]=3(SO52Cl(4n3cFr+cro(=CCr))cBB()c1C#
add ( at position 43,Cc2+n=]=3(SO52Cl(4n3cFr+cro(=CCr))cBB()c1C#(
remove ( from position 9,Cc2+n=]=3SO52Cl(4n3cFr+cro(=CCr))cBB()c1C#(
replace 2 at position 2 with @,Cc@+n=]=3SO52Cl(4n3cFr+cro(=CCr))cBB()c1C#(
remove F from position 20,Cc@+n=]=3SO52Cl(4n3cr+cro(=CCr))cBB()c1C#(
add r at position 24,Cc@+n=]=3SO52Cl(4n3cr+crro(=CCr))cBB()c1C#(
replace n at position 17 with F,Cc@+n=]=3SO52Cl(4F3cr+crro(=CCr))cBB()c1C#(
remove c from position 19,Cc@+n=]=3SO52Cl(4F3r+crro(=CCr))cBB()c1C#(
remove ) from position 36,Cc@+n=]=3SO52Cl(4F3r+crro(=CCr))cBB(c1C#(
replace B at position 34 with ),Cc@+n=]=3SO52Cl(4F3r+crro(=CCr))cB)(c1C#(
replace F at position 17 with =,Cc@+n=]=3SO52Cl(4=3r+crro(=CCr))cB)(c1C#(
remove c from position 36,Cc@+n=]=3SO52Cl(4=3r+crro(=CCr))cB)(1C#(
remove = from position 7,Cc@+n=]3SO52Cl(4=3r+crro(=CCr))cB)(1C#(
remove 3 from position 17,Cc@+n=]3SO52Cl(4=r+crro(=CCr))cB)(1C#(
add 7 at position 37,Cc@+n=]3SO52Cl(4=r+crro(=CCr))cB)(1C#7(
add 2 at position 28,Cc@+n=]3SO52Cl(4=r+crro(=CCr2))cB)(1C#7(
replace 5 at position 10 with =,Cc@+n=]3SO=2Cl(4=r+crro(=CCr2))cB)(1C#7(
remove n from position 4,Cc@+=]3SO=2Cl(4=r+crro(=CCr2))cB)(1C#7(
remove r from position 19,Cc@+=]3SO=2Cl(4=r+cro(=CCr2))cB)(1C#7(
remove B from position 30,Cc@+=]3SO=2Cl(4=r+cro(=CCr2))c)(1C#7(
add / at position 30,Cc@+=]3SO=2Cl(4=r+cro(=CCr2))c/)(1C#7(
remove C from position 23,Cc@+=]3SO=2Cl(4=r+cro(=Cr2))c/)(1C#7(
remove = from position 22,Cc@+=]3SO=2Cl(4=r+cro(Cr2))c/)(1C#7(
add r at position 33,Cc@+=]3SO=2Cl(4=r+cro(Cr2))c/)(1Cr#7(
remove ( from position 30,Cc@+=]3SO=2Cl(4=r+cro(Cr2))c/)1Cr#7(
remove ) from position 29,Cc@+=]3SO=2Cl(4=r+cro(Cr2))c/1Cr#7(
remove 3 from position 6,Cc@+=]SO=2Cl(4=r+cro(Cr2))c/1Cr#7(
add ) at position 30,Cc@+=]SO=2Cl(4=r+cro(Cr2))c/1C)r#7(
replace C at position 21 with 6,Cc@+=]SO=2Cl(4=r+cro(6r2))c/1C)r#7(
replace r at position 18 with 4,Cc@+=]SO=2Cl(4=r+c4o(6r2))c/1C)r#7(
remove 6 from position 21,Cc@+=]SO=2Cl(4=r+c4o(r2))c/1C)r#7(
remove 4 from position 18,Cc@+=]SO=2Cl(4=r+co(r2))c/1C)r#7(
remove ( from position 32,Cc@+=]SO=2Cl(4=r+co(r2))c/1C)r#7
replace c at position 17 with ),Cc@+=]SO=2Cl(4=r+)o(r2))c/1C)r#7
remove 4 from position 13,Cc@+=]SO=2Cl(=r+)o(r2))c/1C)r#7
remove C from position 10,Cc@+=]SO=2l(=r+)o(r2))c/1C)r#7
replace + at position 14 with 4,Cc@+=]SO=2l(=r4)o(r2))c/1C)r#7
add H at position 16,Cc@+=]SO=2l(=r4)Ho(r2))c/1C)r#7
remove + from position 3,Cc@=]SO=2l(=r4)Ho(r2))c/1C)r#7
replace 2 at position 8 with 7,Cc@=]SO=7l(=r4)Ho(r2))c/1C)r#7
add 7 at position 0,7Cc@=]SO=7l(=r4)Ho(r2))c/1C)r#7
add 1 at position 2,7C1c@=]SO=7l(=r4)Ho(r2))c/1C)r#7
remove r from position 20,7C1c@=]SO=7l(=r4)Ho(2))c/1C)r#7
remove = from position 13,7C1c@=]SO=7l(r4)Ho(2))c/1C)r#7
replace 7 at position 29 with ),7C1c@=]SO=7l(r4)Ho(2))c/1C)r#)
replace = at position 9 with 3,7C1c@=]SO37l(r4)Ho(2))c/1C)r#)
remove ) from position 29,7C1c@=]SO37l(r4)Ho(2))c/1C)r#
remove l from position 11,7C1c@=]SO37(r4)Ho(2))c/1C)r#
remove ) from position 20,7C1c@=]SO37(r4)Ho(2)c/1C)r#
replace 7 at position 0 with S,SC1c@=]SO37(r4)Ho(2)c/1C)r#
remove 2 from position 18,SC1c@=]SO37(r4)Ho()c/1C)r#
add B at position 15,SC1c@=]SO37(r4)BHo()c/1C)r#
remove / from position 21,SC1c@=]SO37(r4)BHo()c1C)r#
replace @ at position 4 with r,SC1cr=]SO37(r4)BHo()c1C)r#
remove ) from position 19,SC1cr=]SO37(r4)BHo(c1C)r#
add s at position 17,SC1cr=]SO37(r4)BHso(c1C)r#
replace C at position 22 with /,SC1cr=]SO37(r4)BHso(c1/)r#
replace r at position 24 with C,SC1cr=]SO37(r4)BHso(c1/)C#
remove r from position 12,SC1cr=]SO37(4)BHso(c1/)C#
replace 7 at position 10 with -,SC1cr=]SO3-(4)BHso(c1/)C#
remove o from position 17,SC1cr=]SO3-(4)BHs(c1/)C#
remove 1 from position 19,SC1cr=]SO3-(4)BHs(c/)C#
remove B from position 14,SC1cr=]SO3-(4)Hs(c/)C#
add r at position 2,SCr1cr=]SO3-(4)Hs(c/)C#
remove C from position 21,SCr1cr=]SO3-(4)Hs(c/)#
remove C from position 1,Sr1cr=]SO3-(4)Hs(c/)#
remove 1 from position 2,Srcr=]SO3-(4)Hs(c/)#
remove # from position 19,Srcr=]SO3-(4)Hs(c/)
replace 4 at position 11 with F,Srcr=]SO3-(F)Hs(c/)
remove ) from position 12,Srcr=]SO3-(FHs(c/)
remove r from position 1,Scr=]SO3-(FHs(c/)
add l at position 2,Sclr=]SO3-(FHs(c/)
remove ( from position 10,Sclr=]SO3-FHs(c/)
add S at position 7,Sclr=]SSO3-FHs(c/)
replace S at position 0 with o,oclr=]SSO3-FHs(c/)
remove F from position 11,oclr=]SSO3-Hs(c/)
remove l from position 2,ocr=]SSO3-Hs(c/)
remove S from position 6,ocr=]SO3-Hs(c/)
replace / at position 13 with N,ocr=]SO3-Hs(cN)
remove ( from position 11,ocr=]SO3-HscN)
add O at position 2,ocOr=]SO3-HscN)
replace H at position 10 with ],ocOr=]SO3-]scN)
add 6 at position 14,ocOr=]SO3-]scN6)
remove s from position 11,ocOr=]SO3-]cN6)
remove S from position 6,ocOr=]O3-]cN6)
replace r at position 3 with 4,ocO4=]O3-]cN6)
add H at position 5,ocO4=H]O3-]cN6)
replace c at position 1 with F,oFO4=H]O3-]cN6)
remove 6 from position 13,oFO4=H]O3-]cN)
add H at position 9,oFO4=H]O3H-]cN)
remove O from position 2,oF4=H]O3H-]cN)
remove ] from position 5,oF4=HO3H-]cN)
replace H at position 4 with [,oF4=[O3H-]cN)
remove 4 from position 2,oF=[O3H-]cN)
remove = from position 2,oF[O3H-]cN)
replace c at position 8 with S,oF[O3H-]SN)
remove O from position 3,oF[3H-]SN)
remove [ from position 2,oF3H-]SN)
remove S from position 6,oF3H-]N)
remove ) from position 7,oF3H-]N
replace - at position 4 with B,oF3HB]N
remove N from position 6,oF3HB]
remove B from position 4,oF3H]
add C at position 5,oF3H]C
replace ] at position 4 with -,oF3H-C
add B at position 3,oF3BH-C
add c at position 2,oFc3BH-C
replace 3 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
