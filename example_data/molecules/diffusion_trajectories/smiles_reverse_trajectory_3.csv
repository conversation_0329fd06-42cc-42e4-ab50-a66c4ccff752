log,state
initialize: ,
add c at position 0,c
add N at position 1,c<PERSON>
remove c from position 0,N
replace N at position 0 with ),)
add H at position 0,H)
add 7 at position 0,7H)
replace 7 at position 0 with I,IH)
remove I from position 0,H)
replace ) at position 1 with s,Hs
replace H at position 0 with 6,6s
add C at position 1,6Cs
add + at position 3,6Cs+
add s at position 1,6sCs+
add 4 at position 2,6s4<PERSON>+
add 2 at position 2,6s24Cs+
replace 6 at position 0 with 1,1s24<PERSON>+
replace 2 at position 2 with O,1sO4Cs+
add + at position 2,1s+O4Cs+
replace + at position 7 with [,1s+O4Cs[
replace + at position 2 with r,1srO4Cs[
add H at position 8,1srO4Cs[H
add S at position 5,1srO4SCs[H
add S at position 7,1srO4SCSs[H
add - at position 9,1srO4SCSs-[H
replace C at position 6 with H,1srO4SHSs-[H
remove H from position 11,1srO4SHSs-[
add ( at position 11,1srO4SHSs-[(
add F at position 3,1srFO4SHSs-[(
remove S from position 8,1srFO4SHs-[(
replace r at position 2 with o,1soFO4SHs-[(
add 6 at position 7,1soFO4S6Hs-[(
replace [ at position 11 with 5,1soFO4S6Hs-5(
add ] at position 13,1soFO4S6Hs-5(]
add 5 at position 5,1soFO54S6Hs-5(]
add n at position 11,1soFO54S6Hsn-5(]
add C at position 6,1soFO5C4S6Hsn-5(]
replace F at position 3 with l,1solO5C4S6Hsn-5(]
remove H from position 10,1solO5C4S6sn-5(]
replace 4 at position 7 with =,1solO5C=S6sn-5(]
add s at position 13,1solO5C=S6sn-s5(]
add 4 at position 11,1solO5C=S6s4n-s5(]
add / at position 11,1solO5C=S6s/4n-s5(]
replace s at position 1 with o,1oolO5C=S6s/4n-s5(]
remove O from position 4,1ool5C=S6s/4n-s5(]
replace ] at position 17 with [,1ool5C=S6s/4n-s5([
remove 1 from position 0,ool5C=S6s/4n-s5([
add H at position 5,ool5CH=S6s/4n-s5([
add I at position 18,ool5CH=S6s/4n-s5([I
replace o at position 0 with 1,1ol5CH=S6s/4n-s5([I
remove S from position 7,1ol5CH=6s/4n-s5([I
add 4 at position 10,1ol5CH=6s/44n-s5([I
remove l from position 2,1o5CH=6s/44n-s5([I
replace ( at position 15 with s,1o5CH=6s/44n-s5s[I
replace [ at position 16 with 5,1o5CH=6s/44n-s5s5I
remove 6 from position 6,1o5CH=s/44n-s5s5I
replace 5 at position 2 with r,1orCH=s/44n-s5s5I
add ) at position 13,1orCH=s/44n-s)5s5I
remove r from position 2,1oCH=s/44n-s)5s5I
add c at position 14,1oCH=s/44n-s)5cs5I
add c at position 17,1oCH=s/44n-s)5cs5cI
replace - at position 10 with 3,1oCH=s/44n3s)5cs5cI
add H at position 12,1oCH=s/44n3sH)5cs5cI
add c at position 16,1oCH=s/44n3sH)5ccs5cI
replace s at position 5 with r,1oCH=r/44n3sH)5ccs5cI
remove s from position 17,1oCH=r/44n3sH)5cc5cI
replace I at position 19 with r,1oCH=r/44n3sH)5cc5cr
add ( at position 4,1oCH(=r/44n3sH)5cc5cr
replace / at position 7 with 7,1oCH(=r744n3sH)5cc5cr
add O at position 10,1oCH(=r744On3sH)5cc5cr
add 7 at position 0,71oCH(=r744On3sH)5cc5cr
remove ) from position 16,71oCH(=r744On3sH5cc5cr
add c at position 18,71oCH(=r744On3sH5ccc5cr
replace 4 at position 9 with ),71oCH(=r7)4On3sH5ccc5cr
replace o at position 2 with C,71CCH(=r7)4On3sH5ccc5cr
add B at position 13,71CCH(=r7)4OnB3sH5ccc5cr
add 6 at position 10,71CCH(=r7)64OnB3sH5ccc5cr
remove 1 from position 1,7CCH(=r7)64OnB3sH5ccc5cr
remove 7 from position 0,CCH(=r7)64OnB3sH5ccc5cr
replace 6 at position 8 with c,CCH(=r7)c4OnB3sH5ccc5cr
add N at position 3,CCHN(=r7)c4OnB3sH5ccc5cr
remove H from position 16,CCHN(=r7)c4OnB3s5ccc5cr
replace 3 at position 14 with r,CCHN(=r7)c4OnBrs5ccc5cr
remove 5 from position 16,CCHN(=r7)c4OnBrsccc5cr
add 2 at position 19,CCHN(=r7)c4OnBrsccc25cr
replace ) at position 8 with l,CCHN(=r7lc4OnBrsccc25cr
add 5 at position 16,CCHN(=r7lc4OnBrs5ccc25cr
add S at position 9,CCHN(=r7lSc4OnBrs5ccc25cr
add c at position 10,CCHN(=r7lScc4OnBrs5ccc25cr
replace S at position 9 with B,CCHN(=r7lBcc4OnBrs5ccc25cr
replace c at position 24 with ),CCHN(=r7lBcc4OnBrs5ccc25)r
replace 5 at position 18 with r,CCHN(=r7lBcc4OnBrsrccc25)r
add # at position 4,CCHN#(=r7lBcc4OnBrsrccc25)r
remove c from position 11,CCHN#(=r7lBc4OnBrsrccc25)r
add [ at position 25,CCHN#(=r7lBc4OnBrsrccc25)[r
remove [ from position 25,CCHN#(=r7lBc4OnBrsrccc25)r
add c at position 21,CCHN#(=r7lBc4OnBrsrcccc25)r
add F at position 25,CCHN#(=r7lBc4OnBrsrcccc25F)r
remove r from position 16,CCHN#(=r7lBc4OnBsrcccc25F)r
add S at position 11,CCHN#(=r7lBSc4OnBsrcccc25F)r
add 1 at position 11,CCHN#(=r7lB1Sc4OnBsrcccc25F)r
add 7 at position 5,CCHN#7(=r7lB1Sc4OnBsrcccc25F)r
replace 7 at position 5 with n,CCHN#n(=r7lB1Sc4OnBsrcccc25F)r
add ] at position 21,CCHN#n(=r7lB1Sc4OnBsr]cccc25F)r
add 4 at position 19,CCHN#n(=r7lB1Sc4OnB4sr]cccc25F)r
add I at position 4,CCHNI#n(=r7lB1Sc4OnB4sr]cccc25F)r
replace 7 at position 10 with ],CCHNI#n(=r]lB1Sc4OnB4sr]cccc25F)r
remove 2 from position 28,CCHNI#n(=r]lB1Sc4OnB4sr]cccc5F)r
add 5 at position 25,CCHNI#n(=r]lB1Sc4OnB4sr]c5ccc5F)r
add s at position 19,CCHNI#n(=r]lB1Sc4OnsB4sr]c5ccc5F)r
add 7 at position 24,CCHNI#n(=r]lB1Sc4OnsB4sr7]c5ccc5F)r
add = at position 17,CCHNI#n(=r]lB1Sc4=OnsB4sr7]c5ccc5F)r
replace = at position 17 with H,CCHNI#n(=r]lB1Sc4HOnsB4sr7]c5ccc5F)r
replace ) at position 34 with C,CCHNI#n(=r]lB1Sc4HOnsB4sr7]c5ccc5FCr
add l at position 36,CCHNI#n(=r]lB1Sc4HOnsB4sr7]c5ccc5FCrl
add c at position 19,CCHNI#n(=r]lB1Sc4HOcnsB4sr7]c5ccc5FCrl
replace H at position 17 with -,CCHNI#n(=r]lB1Sc4-OcnsB4sr7]c5ccc5FCrl
replace s at position 24 with F,CCHNI#n(=r]lB1Sc4-OcnsB4Fr7]c5ccc5FCrl
add C at position 13,CCHNI#n(=r]lBC1Sc4-OcnsB4Fr7]c5ccc5FCrl
remove 7 from position 27,CCHNI#n(=r]lBC1Sc4-OcnsB4Fr]c5ccc5FCrl
remove r from position 9,CCHNI#n(=]lBC1Sc4-OcnsB4Fr]c5ccc5FCrl
remove N from position 3,CCHI#n(=]lBC1Sc4-OcnsB4Fr]c5ccc5FCrl
add 4 at position 20,CCHI#n(=]lBC1Sc4-Ocn4sB4Fr]c5ccc5FCrl
remove l from position 9,CCHI#n(=]BC1Sc4-Ocn4sB4Fr]c5ccc5FCrl
remove I from position 3,CCH#n(=]BC1Sc4-Ocn4sB4Fr]c5ccc5FCrl
remove 5 from position 30,CCH#n(=]BC1Sc4-Ocn4sB4Fr]c5cccFCrl
remove F from position 22,CCH#n(=]BC1Sc4-Ocn4sB4r]c5cccFCrl
replace 4 at position 13 with ],CCH#n(=]BC1Sc]-Ocn4sB4r]c5cccFCrl
add 1 at position 11,CCH#n(=]BC11Sc]-Ocn4sB4r]c5cccFCrl
replace r at position 32 with -,CCH#n(=]BC11Sc]-Ocn4sB4r]c5cccFC-l
remove r from position 23,CCH#n(=]BC11Sc]-Ocn4sB4]c5cccFC-l
replace F at position 29 with 2,CCH#n(=]BC11Sc]-Ocn4sB4]c5ccc2C-l
remove S from position 12,CCH#n(=]BC11c]-Ocn4sB4]c5ccc2C-l
add @ at position 12,CCH#n(=]BC11@c]-Ocn4sB4]c5ccc2C-l
add H at position 4,CCH#Hn(=]BC11@c]-Ocn4sB4]c5ccc2C-l
replace H at position 4 with o,CCH#on(=]BC11@c]-Ocn4sB4]c5ccc2C-l
remove 4 from position 20,CCH#on(=]BC11@c]-OcnsB4]c5ccc2C-l
remove l from position 32,CCH#on(=]BC11@c]-OcnsB4]c5ccc2C-
remove c from position 18,CCH#on(=]BC11@c]-OnsB4]c5ccc2C-
remove 1 from position 12,CCH#on(=]BC1@c]-OnsB4]c5ccc2C-
replace ] at position 14 with 7,CCH#on(=]BC1@c7-OnsB4]c5ccc2C-
replace 5 at position 23 with c,CCH#on(=]BC1@c7-OnsB4]ccccc2C-
replace o at position 4 with N,CCH#Nn(=]BC1@c7-OnsB4]ccccc2C-
replace s at position 18 with c,CCH#Nn(=]BC1@c7-OncB4]ccccc2C-
remove c from position 22,CCH#Nn(=]BC1@c7-OncB4]cccc2C-
add 3 at position 11,CCH#Nn(=]BC31@c7-OncB4]cccc2C-
replace 4 at position 21 with 3,CCH#Nn(=]BC31@c7-OncB3]cccc2C-
replace 7 at position 15 with C,CCH#Nn(=]BC31@cC-OncB3]cccc2C-
replace - at position 16 with ),CCH#Nn(=]BC31@cC)OncB3]cccc2C-
replace @ at position 13 with -,CCH#Nn(=]BC31-cC)OncB3]cccc2C-
add N at position 3,CCHN#Nn(=]BC31-cC)OncB3]cccc2C-
add c at position 28,CCHN#Nn(=]BC31-cC)OncB3]ccccc2C-
replace B at position 10 with ),CCHN#Nn(=])C31-cC)OncB3]ccccc2C-
add c at position 30,CCHN#Nn(=])C31-cC)OncB3]ccccc2cC-
add n at position 29,CCHN#Nn(=])C31-cC)OncB3]cccccn2cC-
add 2 at position 22,CCHN#Nn(=])C31-cC)OncB23]cccccn2cC-
replace H at position 2 with O,CCON#Nn(=])C31-cC)OncB23]cccccn2cC-
remove ] from position 9,CCON#Nn(=)C31-cC)OncB23]cccccn2cC-
replace 3 at position 11 with c,CCON#Nn(=)Cc1-cC)OncB23]cccccn2cC-
remove O from position 17,CCON#Nn(=)Cc1-cC)ncB23]cccccn2cC-
add o at position 29,CCON#Nn(=)Cc1-cC)ncB23]cccccno2cC-
remove - from position 33,CCON#Nn(=)Cc1-cC)ncB23]cccccno2cC
remove ] from position 22,CCON#Nn(=)Cc1-cC)ncB23cccccno2cC
remove - from position 13,CCON#Nn(=)Cc1cC)ncB23cccccno2cC
remove B from position 18,CCON#Nn(=)Cc1cC)nc23cccccno2cC
add C at position 5,CCON#CNn(=)Cc1cC)nc23cccccno2cC
add 1 at position 30,CCON#CNn(=)Cc1cC)nc23cccccno2c1C
replace # at position 4 with C,CCONCCNn(=)Cc1cC)nc23cccccno2c1C
add 6 at position 1,C6CONCCNn(=)Cc1cC)nc23cccccno2c1C
add C at position 12,C6CONCCNn(=)CCc1cC)nc23cccccno2c1C
add c at position 22,C6CONCCNn(=)CCc1cC)nc2c3cccccno2c1C
replace N at position 4 with S,C6COSCCNn(=)CCc1cC)nc2c3cccccno2c1C
add # at position 17,C6COSCCNn(=)CCc1c#C)nc2c3cccccno2c1C
remove C from position 5,C6COSCNn(=)CCc1c#C)nc2c3cccccno2c1C
add + at position 29,C6COSCNn(=)CCc1c#C)nc2c3ccccc+no2c1C
replace S at position 4 with C,C6COCCNn(=)CCc1c#C)nc2c3ccccc+no2c1C
remove o from position 31,C6COCCNn(=)CCc1c#C)nc2c3ccccc+n2c1C
remove n from position 7,C6COCCN(=)CCc1c#C)nc2c3ccccc+n2c1C
add O at position 9,C6COCCN(=O)CCc1c#C)nc2c3ccccc+n2c1C
add n at position 30,C6COCCN(=O)CCc1c#C)nc2c3ccccc+nn2c1C
remove 6 from position 1,CCOCCN(=O)CCc1c#C)nc2c3ccccc+nn2c1C
replace # at position 15 with (,CCOCCN(=O)CCc1c(C)nc2c3ccccc+nn2c1C
add C at position 6,CCOCCNC(=O)CCc1c(C)nc2c3ccccc+nn2c1C
remove O from position 2,CCCCNC(=O)CCc1c(C)nc2c3ccccc+nn2c1C
replace + at position 28 with 3,CCCCNC(=O)CCc1c(C)nc2c3ccccc3nn2c1C
final: CCCCNC(=O)CCc1c(C)nc2c3ccccc3nn2c1C,CCCCNC(=O)CCc1c(C)nc2c3ccccc3nn2c1C
