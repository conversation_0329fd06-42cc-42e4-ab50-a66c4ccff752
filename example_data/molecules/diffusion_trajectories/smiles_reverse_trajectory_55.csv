log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with /,/#+5/
add 3 at position 4,/#+53/
add c at position 2,/#c+53/
remove 3 from position 5,/#c+5/
add 1 at position 0,1/#c+5/
remove 5 from position 5,1/#c+/
replace # at position 2 with S,1/Sc+/
remove S from position 2,1/c+/
add F at position 2,1/Fc+/
remove / from position 1,1Fc+/
add 5 at position 3,1Fc5+/
replace + at position 4 with H,1Fc5H/
replace 5 at position 3 with B,1FcBH/
add s at position 5,1FcBHs/
add [ at position 3,1Fc[BHs/
add C at position 8,1Fc[BHs/C
remove s from position 6,1Fc[BH/C
replace [ at position 3 with <PERSON>,1Fc<PERSON>BH/C
remove c from position 2,1FO<PERSON><PERSON>/C
remove B from position 3,1FOH/C
replace / at position 4 with -,1FOH-<PERSON>
remove C from position 5,1FOH-
add @ at position 4,1FOH@-
add s at position 6,1FOH@-s
replace @ at position 4 with l,1FOHl-s
add C at position 7,1FOHl-sC
add [ at position 6,1FOHl-[sC
add [ at position 2,1F[OHl-[sC
add @ at position 3,1F[@OHl-[sC
replace [ at position 8 with ),1F[@OHl-)sC
add F at position 2,1FF[@OHl-)sC
add 5 at position 2,1F5F[@OHl-)sC
replace [ at position 4 with H,1F5FH@OHl-)sC
add r at position 5,1F5FHr@OHl-)sC
add 5 at position 2,1F55FHr@OHl-)sC
remove H from position 9,1F55FHr@Ol-)sC
add + at position 13,1F55FHr@Ol-)s+C
replace F at position 1 with o,1o55FHr@Ol-)s+C
remove H from position 5,1o55Fr@Ol-)s+C
replace 5 at position 3 with 2,1o52Fr@Ol-)s+C
add ) at position 6,1o52Fr)@Ol-)s+C
add I at position 11,1o52Fr)@Ol-I)s+C
add S at position 14,1o52Fr)@Ol-I)sS+C
replace O at position 8 with 3,1o52Fr)@3l-I)sS+C
replace F at position 4 with B,1o52Br)@3l-I)sS+C
remove 1 from position 0,o52Br)@3l-I)sS+C
add 6 at position 5,o52Br6)@3l-I)sS+C
replace I at position 11 with 3,o52Br6)@3l-3)sS+C
add B at position 0,Bo52Br6)@3l-3)sS+C
add s at position 7,Bo52Br6s)@3l-3)sS+C
replace B at position 4 with N,Bo52Nr6s)@3l-3)sS+C
replace @ at position 9 with /,Bo52Nr6s)/3l-3)sS+C
replace B at position 0 with 1,1o52Nr6s)/3l-3)sS+C
replace C at position 18 with ),1o52Nr6s)/3l-3)sS+)
replace S at position 16 with l,1o52Nr6s)/3l-3)sl+)
remove 6 from position 6,1o52Nrs)/3l-3)sl+)
replace 5 at position 2 with r,1or2Nrs)/3l-3)sl+)
add H at position 13,1or2Nrs)/3l-3H)sl+)
remove r from position 2,1o2Nrs)/3l-3H)sl+)
add c at position 14,1o2Nrs)/3l-3H)csl+)
add ) at position 17,1o2Nrs)/3l-3H)csl)+)
replace - at position 10 with 3,1o2Nrs)/3l33H)csl)+)
add s at position 12,1o2Nrs)/3l33sH)csl)+)
add 4 at position 16,1o2Nrs)/3l33sH)c4sl)+)
replace s at position 5 with N,1o2NrN)/3l33sH)c4sl)+)
remove s from position 17,1o2NrN)/3l33sH)c4l)+)
add / at position 19,1o2NrN)/3l33sH)c4l)/+)
replace r at position 4 with l,1o2NlN)/3l33sH)c4l)/+)
add 1 at position 21,1o2NlN)/3l33sH)c4l)/+1)
replace / at position 7 with S,1o2NlN)S3l33sH)c4l)/+1)
add = at position 10,1o2NlN)S3l=33sH)c4l)/+1)
add 7 at position 0,71o2NlN)S3l=33sH)c4l)/+1)
remove ) from position 16,71o2NlN)S3l=33sHc4l)/+1)
add o at position 18,71o2NlN)S3l=33sHc4ol)/+1)
replace 3 at position 9 with 5,71o2NlN)S5l=33sHc4ol)/+1)
replace o at position 2 with 6,7162NlN)S5l=33sHc4ol)/+1)
add c at position 13,7162NlN)S5l=3c3sHc4ol)/+1)
add 6 at position 10,7162NlN)S56l=3c3sHc4ol)/+1)
remove 1 from position 1,762NlN)S56l=3c3sHc4ol)/+1)
remove 7 from position 0,62NlN)S56l=3c3sHc4ol)/+1)
replace 6 at position 8 with B,62NlN)S5Bl=3c3sHc4ol)/+1)
add ) at position 3,62N)lN)S5Bl=3c3sHc4ol)/+1)
remove H from position 16,62N)lN)S5Bl=3c3sc4ol)/+1)
replace 3 at position 14 with F,62N)lN)S5Bl=3cFsc4ol)/+1)
add c at position 10,62N)lN)S5Bcl=3cFsc4ol)/+1)
add 3 at position 6,62N)lN3)S5Bcl=3cFsc4ol)/+1)
add 4 at position 17,62N)lN3)S5Bcl=3cF4sc4ol)/+1)
add F at position 23,62N)lN3)S5Bcl=3cF4sc4olF)/+1)
add r at position 6,62N)lNr3)S5Bcl=3cF4sc4olF)/+1)
add r at position 30,62N)lNr3)S5Bcl=3cF4sc4olF)/+1)r
add 7 at position 21,62N)lNr3)S5Bcl=3cF4sc74olF)/+1)r
replace 4 at position 18 with r,62N)lNr3)S5Bcl=3cFrsc74olF)/+1)r
replace 7 at position 21 with (,62N)lNr3)S5Bcl=3cFrsc(4olF)/+1)r
remove ) from position 30,62N)lNr3)S5Bcl=3cFrsc(4olF)/+1r
add = at position 6,62N)lN=r3)S5Bcl=3cFrsc(4olF)/+1r
add / at position 29,62N)lN=r3)S5Bcl=3cFrsc(4olF)//+1r
add O at position 30,62N)lN=r3)S5Bcl=3cFrsc(4olF)//O+1r
remove r from position 33,62N)lN=r3)S5Bcl=3cFrsc(4olF)//O+1
add 7 at position 22,62N)lN=r3)S5Bcl=3cFrsc7(4olF)//O+1
add 3 at position 23,62N)lN=r3)S5Bcl=3cFrsc73(4olF)//O+1
remove / from position 30,62N)lN=r3)S5Bcl=3cFrsc73(4olF)/O+1
add r at position 30,62N)lN=r3)S5Bcl=3cFrsc73(4olF)r/O+1
remove 4 from position 25,62N)lN=r3)S5Bcl=3cFrsc73(olF)r/O+1
add C at position 0,C62N)lN=r3)S5Bcl=3cFrsc73(olF)r/O+1
remove / from position 31,C62N)lN=r3)S5Bcl=3cFrsc73(olF)rO+1
add s at position 28,C62N)lN=r3)S5Bcl=3cFrsc73(olsF)rO+1
remove ( from position 25,C62N)lN=r3)S5Bcl=3cFrsc73olsF)rO+1
add 4 at position 17,C62N)lN=r3)S5Bcl=43cFrsc73olsF)rO+1
add ] at position 7,C62N)lN]=r3)S5Bcl=43cFrsc73olsF)rO+1
add ) at position 36,C62N)lN]=r3)S5Bcl=43cFrsc73olsF)rO+1)
replace = at position 17 with H,C62N)lN]=r3)S5BclH43cFrsc73olsF)rO+1)
replace + at position 34 with ),C62N)lN]=r3)S5BclH43cFrsc73olsF)rO)1)
add B at position 36,C62N)lN]=r3)S5BclH43cFrsc73olsF)rO)1B)
add n at position 19,C62N)lN]=r3)S5BclH4n3cFrsc73olsF)rO)1B)
replace H at position 17 with -,C62N)lN]=r3)S5Bcl-4n3cFrsc73olsF)rO)1B)
replace s at position 24 with c,C62N)lN]=r3)S5Bcl-4n3cFrcc73olsF)rO)1B)
add O at position 13,C62N)lN]=r3)SO5Bcl-4n3cFrcc73olsF)rO)1B)
remove 7 from position 27,C62N)lN]=r3)SO5Bcl-4n3cFrcc3olsF)rO)1B)
remove r from position 9,C62N)lN]=3)SO5Bcl-4n3cFrcc3olsF)rO)1B)
remove N from position 3,C62)lN]=3)SO5Bcl-4n3cFrcc3olsF)rO)1B)
add r at position 10,C62)lN]=3)rSO5Bcl-4n3cFrcc3olsF)rO)1B)
remove ) from position 34,C62)lN]=3)rSO5Bcl-4n3cFrcc3olsF)rO1B)
remove r from position 10,C62)lN]=3)SO5Bcl-4n3cFrcc3olsF)rO1B)
add ( at position 8,C62)lN]=(3)SO5Bcl-4n3cFrcc3olsF)rO1B)
remove B from position 35,C62)lN]=(3)SO5Bcl-4n3cFrcc3olsF)rO1)
remove F from position 22,C62)lN]=(3)SO5Bcl-4n3crcc3olsF)rO1)
replace 5 at position 13 with 3,C62)lN]=(3)SO3Bcl-4n3crcc3olsF)rO1)
add N at position 11,C62)lN]=(3)NSO3Bcl-4n3crcc3olsF)rO1)
replace r at position 32 with C,C62)lN]=(3)NSO3Bcl-4n3crcc3olsF)CO1)
remove r from position 23,C62)lN]=(3)NSO3Bcl-4n3ccc3olsF)CO1)
replace F at position 29 with (,C62)lN]=(3)NSO3Bcl-4n3ccc3ols()CO1)
remove S from position 12,C62)lN]=(3)NO3Bcl-4n3ccc3ols()CO1)
add c at position 12,C62)lN]=(3)NcO3Bcl-4n3ccc3ols()CO1)
add I at position 4,C62)IlN]=(3)NcO3Bcl-4n3ccc3ols()CO1)
replace I at position 4 with 1,C62)1lN]=(3)NcO3Bcl-4n3ccc3ols()CO1)
replace 4 at position 20 with ],C62)1lN]=(3)NcO3Bcl-]n3ccc3ols()CO1)
add S at position 5,C62)1SlN]=(3)NcO3Bcl-]n3ccc3ols()CO1)
replace 2 at position 2 with I,C6I)1SlN]=(3)NcO3Bcl-]n3ccc3ols()CO1)
add S at position 3,C6IS)1SlN]=(3)NcO3Bcl-]n3ccc3ols()CO1)
replace ) at position 4 with H,C6ISH1SlN]=(3)NcO3Bcl-]n3ccc3ols()CO1)
replace l at position 20 with (,C6ISH1SlN]=(3)NcO3Bc(-]n3ccc3ols()CO1)
add n at position 17,C6ISH1SlN]=(3)NcOn3Bc(-]n3ccc3ols()CO1)
add @ at position 10,C6ISH1SlN]@=(3)NcOn3Bc(-]n3ccc3ols()CO1)
remove S from position 6,C6ISH1lN]@=(3)NcOn3Bc(-]n3ccc3ols()CO1)
replace O at position 16 with 3,C6ISH1lN]@=(3)Nc3n3Bc(-]n3ccc3ols()CO1)
replace n at position 24 with 2,C6ISH1lN]@=(3)Nc3n3Bc(-]23ccc3ols()CO1)
replace l at position 6 with n,C6ISH1nN]@=(3)Nc3n3Bc(-]23ccc3ols()CO1)
add C at position 7,C6ISH1nCN]@=(3)Nc3n3Bc(-]23ccc3ols()CO1)
remove = from position 11,C6ISH1nCN]@(3)Nc3n3Bc(-]23ccc3ols()CO1)
replace 3 at position 18 with O,C6ISH1nCN]@(3)Nc3nOBc(-]23ccc3ols()CO1)
add - at position 33,C6ISH1nCN]@(3)Nc3nOBc(-]23ccc3ols-()CO1)
replace @ at position 10 with C,C6ISH1nCN]C(3)Nc3nOBc(-]23ccc3ols-()CO1)
replace o at position 30 with (,C6ISH1nCN]C(3)Nc3nOBc(-]23ccc3(ls-()CO1)
replace ( at position 30 with C,C6ISH1nCN]C(3)Nc3nOBc(-]23ccc3Cls-()CO1)
add C at position 37,C6ISH1nCN]C(3)Nc3nOBc(-]23ccc3Cls-()CCO1)
add - at position 22,C6ISH1nCN]C(3)Nc3nOBc(--]23ccc3Cls-()CCO1)
replace I at position 2 with C,C6CSH1nCN]C(3)Nc3nOBc(--]23ccc3Cls-()CCO1)
remove ] from position 9,C6CSH1nCNC(3)Nc3nOBc(--]23ccc3Cls-()CCO1)
replace 3 at position 11 with O,C6CSH1nCNC(O)Nc3nOBc(--]23ccc3Cls-()CCO1)
remove O from position 17,C6CSH1nCNC(O)Nc3nBc(--]23ccc3Cls-()CCO1)
add o at position 29,C6CSH1nCNC(O)Nc3nBc(--]23ccc3oCls-()CCO1)
remove - from position 33,C6CSH1nCNC(O)Nc3nBc(--]23ccc3oCls()CCO1)
remove ] from position 22,C6CSH1nCNC(O)Nc3nBc(--23ccc3oCls()CCO1)
add C at position 9,C6CSH1nCNCC(O)Nc3nBc(--23ccc3oCls()CCO1)
add H at position 4,C6CSHH1nCNCC(O)Nc3nBc(--23ccc3oCls()CCO1)
remove H from position 4,C6CSH1nCNCC(O)Nc3nBc(--23ccc3oCls()CCO1)
add c at position 24,C6CSH1nCNCC(O)Nc3nBc(--2c3ccc3oCls()CCO1)
remove - from position 21,C6CSH1nCNCC(O)Nc3nBc(-2c3ccc3oCls()CCO1)
add ) at position 32,C6CSH1nCNCC(O)Nc3nBc(-2c3ccc3oCl)s()CCO1)
add c at position 33,C6CSH1nCNCC(O)Nc3nBc(-2c3ccc3oCl)cs()CCO1)
add = at position 12,C6CSH1nCNCC(=O)Nc3nBc(-2c3ccc3oCl)cs()CCO1)
replace 3 at position 17 with 2,C6CSH1nCNCC(=O)Nc2nBc(-2c3ccc3oCl)cs()CCO1)
remove B from position 19,C6CSH1nCNCC(=O)Nc2nc(-2c3ccc3oCl)cs()CCO1)
add 2 at position 35,C6CSH1nCNCC(=O)Nc2nc(-2c3ccc3oCl)cs2()CCO1)
add [ at position 2,C6[CSH1nCNCC(=O)Nc2nc(-2c3ccc3oCl)cs2()CCO1)
remove ) from position 43,C6[CSH1nCNCC(=O)Nc2nc(-2c3ccc3oCl)cs2()CCO1
add + at position 29,C6[CSH1nCNCC(=O)Nc2nc(-2c3ccc+3oCl)cs2()CCO1
replace S at position 4 with @,C6[C@H1nCNCC(=O)Nc2nc(-2c3ccc+3oCl)cs2()CCO1
remove o from position 31,C6[C@H1nCNCC(=O)Nc2nc(-2c3ccc+3Cl)cs2()CCO1
remove n from position 7,C6[C@H1CNCC(=O)Nc2nc(-2c3ccc+3Cl)cs2()CCO1
add ( at position 9,C6[C@H1CN(CC(=O)Nc2nc(-2c3ccc+3Cl)cs2()CCO1
add c at position 30,C6[C@H1CN(CC(=O)Nc2nc(-2c3ccc+c3Cl)cs2()CCO1
remove 6 from position 1,C[C@H1CN(CC(=O)Nc2nc(-2c3ccc+c3Cl)cs2()CCO1
remove 2 from position 22,C[C@H1CN(CC(=O)Nc2nc(-c3ccc+c3Cl)cs2()CCO1
add ] at position 5,C[C@H]1CN(CC(=O)Nc2nc(-c3ccc+c3Cl)cs2()CCO1
remove ( from position 37,C[C@H]1CN(CC(=O)Nc2nc(-c3ccc+c3Cl)cs2)CCO1
replace + at position 28 with c,C[C@H]1CN(CC(=O)Nc2nc(-c3ccccc3Cl)cs2)CCO1
final: C[C@H]1CN(CC(=O)Nc2nc(-c3ccccc3Cl)cs2)CCO1,C[C@H]1CN(CC(=O)Nc2nc(-c3ccccc3Cl)cs2)CCO1
