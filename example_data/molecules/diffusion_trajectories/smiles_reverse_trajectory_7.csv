log,state
initialize: ,
add O at position 0,O
add r at position 0,r<PERSON>
add I at position 2,r<PERSON><PERSON>
add ( at position 2,rO(I
add 2 at position 1,r2O(I
replace O at position 2 with (,r2((I
add [ at position 0,[r2((I
add c at position 2,[rc2((I
add C at position 6,[rc2((CI
add 2 at position 2,[r2c2((CI
remove 2 from position 4,[r2c((CI
replace I at position 7 with C,[r2c((CC
replace 2 at position 2 with c,[rcc((CC
replace [ at position 0 with =,=rcc((CC
remove = from position 0,rcc((CC
replace C at position 5 with ],rcc((]C
remove ( from position 4,rcc(]C
remove C from position 5,rcc(]
remove ( from position 3,rcc]
remove c from position 1,rc]
replace c at position 1 with o,ro]
remove r from position 0,o]
replace o at position 0 with -,-]
add 7 at position 0,7-]
add r at position 0,r7-]
replace 7 at position 1 with O,rO-]
remove r from position 0,O-]
add 3 at position 0,3O-]
add = at position 2,3O=-]
add F at position 5,3O=-]F
remove ] from position 4,3O=-F
replace F at position 4 with 3,3O=-3
add l at position 2,3Ol=-3
replace = at position 3 with ],3Ol]-3
add # at position 5,3Ol]-#3
remove l from position 2,3O]-#3
remove # from position 4,3O]-3
replace O at position 1 with C,3C]-3
add o at position 4,3C]-o3
add 1 at position 3,3C]1-o3
replace ] at position 2 with B,3CB1-o3
remove - from position 4,3CB1o3
remove 1 from position 3,3CBo3
replace 3 at position 4 with C,3CBoC
remove 3 from position 0,CBoC
replace B at position 1 with ),C)oC
replace ) at position 1 with 5,C5oC
add [ at position 2,C5[oC
replace o at position 3 with +,C5[+C
add = at position 2,C5=[+C
add N at position 2,C5N=[+C
replace C at position 0 with /,/5N=[+C
add 7 at position 2,/57N=[+C
remove 7 from position 2,/5N=[+C
remove C from position 6,/5N=[+
add r at position 6,/5N=[+r
replace + at position 5 with #,/5N=[#r
add r at position 7,/5N=[#rr
add H at position 8,/5N=[#rrH
add l at position 3,/5Nl=[#rrH
add 3 at position 7,/5Nl=[#3rrH
remove l from position 3,/5N=[#3rrH
add ( at position 1,/(5N=[#3rrH
add c at position 1,/c(5N=[#3rrH
remove # from position 7,/c(5N=[3rrH
replace H at position 10 with 1,/c(5N=[3rr1
remove r from position 8,/c(5N=[3r1
replace ( at position 2 with /,/c/5N=[3r1
replace / at position 2 with C,/cC5N=[3r1
add F at position 3,/cCF5N=[3r1
add 5 at position 9,/cCF5N=[35r1
replace = at position 6 with #,/cCF5N#[35r1
replace 3 at position 8 with r,/cCF5N#[r5r1
add H at position 8,/cCF5N#[Hr5r1
add 3 at position 9,/cCF5N#[H3r5r1
remove 1 from position 13,/cCF5N#[H3r5r
replace 3 at position 9 with ),/cCF5N#[H)r5r
replace 5 at position 11 with r,/cCF5N#[H)rrr
add 6 at position 9,/cCF5N#[H6)rrr
remove # from position 6,/cCF5N[H6)rrr
add 3 at position 10,/cCF5N[H6)3rrr
replace [ at position 6 with =,/cCF5N=H6)3rrr
add 7 at position 4,/cCF75N=H6)3rrr
add + at position 13,/cCF75N=H6)3r+rr
remove c from position 1,/CF75N=H6)3r+rr
replace N at position 5 with F,/CF75F=H6)3r+rr
remove / from position 0,CF75F=H6)3r+rr
add H at position 14,CF75F=H6)3r+rrH
replace 7 at position 2 with I,CFI5F=H6)3r+rrH
remove I from position 2,CF5F=H6)3r+rrH
replace ) at position 7 with 7,CF5F=H673r+rrH
replace F at position 1 with o,Co5F=H673r+rrH
add 2 at position 7,Co5F=H6273r+rrH
add ( at position 14,Co5F=H6273r+rr(H
add - at position 6,Co5F=H-6273r+rr(H
add [ at position 11,Co5F=H-6273[r+rr(H
add / at position 9,Co5F=H-62/73[r+rr(H
replace 5 at position 2 with 6,Co6F=H-62/73[r+rr(H
replace 2 at position 8 with 3,Co6F=H-63/73[r+rr(H
add + at position 4,Co6F+=H-63/73[r+rr(H
replace + at position 15 with /,Co6F+=H-63/73[r/rr(H
replace + at position 4 with l,Co6Fl=H-63/73[r/rr(H
add C at position 12,Co6Fl=H-63/7C3[r/rr(H
add S at position 21,Co6Fl=H-63/7C3[r/rr(HS
add N at position 11,Co6Fl=H-63/N7C3[r/rr(HS
add B at position 23,Co6Fl=H-63/N7C3[r/rr(HSB
add ] at position 15,Co6Fl=H-63/N7C3][r/rr(HSB
add o at position 19,Co6Fl=H-63/N7C3][r/orr(HSB
replace C at position 13 with H,Co6Fl=H-63/N7H3][r/orr(HSB
remove H from position 23,Co6Fl=H-63/N7H3][r/orr(SB
add n at position 23,Co6Fl=H-63/N7H3][r/orr(nSB
add [ at position 6,Co6Fl=[H-63/N7H3][r/orr(nSB
replace [ at position 17 with 2,Co6Fl=[H-63/N7H3]2r/orr(nSB
add 5 at position 5,Co6Fl5=[H-63/N7H3]2r/orr(nSB
add n at position 5,Co6Fln5=[H-63/N7H3]2r/orr(nSB
replace [ at position 8 with C,Co6Fln5=CH-63/N7H3]2r/orr(nSB
add C at position 11,Co6Fln5=CH-C63/N7H3]2r/orr(nSB
add - at position 5,Co6Fl-n5=CH-C63/N7H3]2r/orr(nSB
remove H from position 18,Co6Fl-n5=CH-C63/N73]2r/orr(nSB
add H at position 27,Co6Fl-n5=CH-C63/N73]2r/orr(HnSB
replace F at position 3 with o,Co6ol-n5=CH-C63/N73]2r/orr(HnSB
remove H from position 10,Co6ol-n5=C-C63/N73]2r/orr(HnSB
replace 5 at position 7 with +,Co6ol-n+=C-C63/N73]2r/orr(HnSB
add S at position 13,Co6ol-n+=C-C6S3/N73]2r/orr(HnSB
add 4 at position 23,Co6ol-n+=C-C6S3/N73]2r/4orr(HnSB
add ( at position 29,Co6ol-n+=C-C6S3/N73]2r/4orr(H(nSB
replace N at position 16 with [,Co6ol-n+=C-C6S3/[73]2r/4orr(H(nSB
replace C at position 9 with [,Co6ol-n+=[-C6S3/[73]2r/4orr(H(nSB
add n at position 13,Co6ol-n+=[-C6nS3/[73]2r/4orr(H(nSB
add 5 at position 4,Co6o5l-n+=[-C6nS3/[73]2r/4orr(H(nSB
add 1 at position 23,Co6o5l-n+=[-C6nS3/[73]21r/4orr(H(nSB
replace o at position 1 with 1,C16o5l-n+=[-C6nS3/[73]21r/4orr(H(nSB
remove S from position 15,C16o5l-n+=[-C6n3/[73]21r/4orr(H(nSB
add o at position 21,C16o5l-n+=[-C6n3/[73]o21r/4orr(H(nSB
remove l from position 5,C16o5-n+=[-C6n3/[73]o21r/4orr(H(nSB
replace ( at position 31 with 7,C16o5-n+=[-C6n3/[73]o21r/4orr(H7nSB
replace S at position 33 with n,C16o5-n+=[-C6n3/[73]o21r/4orr(H7nnB
remove 6 from position 12,C16o5-n+=[-Cn3/[73]o21r/4orr(H7nnB
remove 5 from position 4,C16o-n+=[-Cn3/[73]o21r/4orr(H7nnB
replace r at position 26 with 5,C16o-n+=[-Cn3/[73]o21r/4or5(H7nnB
add O at position 23,C16o-n+=[-Cn3/[73]o21r/O4or5(H7nnB
replace ( at position 28 with ),C16o-n+=[-Cn3/[73]o21r/O4or5)H7nnB
replace [ at position 8 with (,C16o-n+=(-Cn3/[73]o21r/O4or5)H7nnB
replace o at position 25 with 2,C16o-n+=(-Cn3/[73]o21r/O42r5)H7nnB
add 6 at position 32,C16o-n+=(-Cn3/[73]o21r/O42r5)H7n6nB
add - at position 15,C16o-n+=(-Cn3/[-73]o21r/O42r5)H7n6nB
replace - at position 4 with c,C16ocn+=(-Cn3/[-73]o21r/O42r5)H7n6nB
add = at position 8,C16ocn+==(-Cn3/[-73]o21r/O42r5)H7n6nB
replace / at position 14 with c,C16ocn+==(-Cn3c[-73]o21r/O42r5)H7n6nB
add l at position 21,C16ocn+==(-Cn3c[-73]ol21r/O42r5)H7n6nB
add 7 at position 1,C716ocn+==(-Cn3c[-73]ol21r/O42r5)H7n6nB
remove ) from position 32,C716ocn+==(-Cn3c[-73]ol21r/O42r5H7n6nB
add 5 at position 37,C716ocn+==(-Cn3c[-73]ol21r/O42r5H7n6n5B
replace 3 at position 19 with @,C716ocn+==(-Cn3c[-7@]ol21r/O42r5H7n6n5B
replace o at position 4 with C,C716Ccn+==(-Cn3c[-7@]ol21r/O42r5H7n6n5B
add ) at position 27,C716Ccn+==(-Cn3c[-7@]ol21r/)O42r5H7n6n5B
add s at position 20,C716Ccn+==(-Cn3c[-7@s]ol21r/)O42r5H7n6n5B
remove 1 from position 2,C76Ccn+==(-Cn3c[-7@s]ol21r/)O42r5H7n6n5B
remove 7 from position 1,C6Ccn+==(-Cn3c[-7@s]ol21r/)O42r5H7n6n5B
replace 7 at position 16 with ),C6Ccn+==(-Cn3c[-)@s]ol21r/)O42r5H7n6n5B
add ( at position 7,C6Ccn+=(=(-Cn3c[-)@s]ol21r/)O42r5H7n6n5B
remove H from position 33,C6Ccn+=(=(-Cn3c[-)@s]ol21r/)O42r57n6n5B
replace 4 at position 29 with ),C6Ccn+=(=(-Cn3c[-)@s]ol21r/)O)2r57n6n5B
remove 5 from position 32,C6Ccn+=(=(-Cn3c[-)@s]ol21r/)O)2r7n6n5B
add c at position 38,C6Ccn+=(=(-Cn3c[-)@s]ol21r/)O)2r7n6n5Bc
replace ) at position 17 with ],C6Ccn+=(=(-Cn3c[-]@s]ol21r/)O)2r7n6n5Bc
add 5 at position 32,C6Ccn+=(=(-Cn3c[-]@s]ol21r/)O)2r57n6n5Bc
add 5 at position 18,C6Ccn+=(=(-Cn3c[-]5@s]ol21r/)O)2r57n6n5Bc
add 5 at position 37,C6Ccn+=(=(-Cn3c[-]5@s]ol21r/)O)2r57n65n5Bc
replace 5 at position 18 with /,C6Ccn+=(=(-Cn3c[-]/@s]ol21r/)O)2r57n65n5Bc
replace 5 at position 37 with n,C6Ccn+=(=(-Cn3c[-]/@s]ol21r/)O)2r57n6nn5Bc
replace ) at position 30 with c,C6Ccn+=(=(-Cn3c[-]/@s]ol21r/)Oc2r57n6nn5Bc
replace 6 at position 1 with I,CICcn+=(=(-Cn3c[-]/@s]ol21r/)Oc2r57n6nn5Bc
replace O at position 29 with c,CICcn+=(=(-Cn3c[-]/@s]ol21r/)cc2r57n6nn5Bc
add H at position 1,CHICcn+=(=(-Cn3c[-]/@s]ol21r/)cc2r57n6nn5Bc
add 2 at position 42,CHICcn+=(=(-Cn3c[-]/@s]ol21r/)cc2r57n6nn5B2c
remove r from position 33,CHICcn+=(=(-Cn3c[-]/@s]ol21r/)cc257n6nn5B2c
add ] at position 22,CHICcn+=(=(-Cn3c[-]/@s]]ol21r/)cc257n6nn5B2c
remove 6 from position 37,CHICcn+=(=(-Cn3c[-]/@s]]ol21r/)cc257nnn5B2c
remove B from position 40,CHICcn+=(=(-Cn3c[-]/@s]]ol21r/)cc257nnn52c
add C at position 30,CHICcn+=(=(-Cn3c[-]/@s]]ol21r/C)cc257nnn52c
add 1 at position 19,CHICcn+=(=(-Cn3c[-]1/@s]]ol21r/C)cc257nnn52c
add [ at position 4,CHIC[cn+=(=(-Cn3c[-]1/@s]]ol21r/C)cc257nnn52c
replace = at position 10 with 3,CHIC[cn+=(3(-Cn3c[-]1/@s]]ol21r/C)cc257nnn52c
remove 2 from position 28,CHIC[cn+=(3(-Cn3c[-]1/@s]]ol1r/C)cc257nnn52c
remove 7 from position 37,CHIC[cn+=(3(-Cn3c[-]1/@s]]ol1r/C)cc25nnn52c
add F at position 17,CHIC[cn+=(3(-Cn3cF[-]1/@s]]ol1r/C)cc25nnn52c
add l at position 7,CHIC[cnl+=(3(-Cn3cF[-]1/@s]]ol1r/C)cc25nnn52c
add c at position 36,CHIC[cnl+=(3(-Cn3cF[-]1/@s]]ol1r/C)ccc25nnn52c
add H at position 26,CHIC[cnl+=(3(-Cn3cF[-]1/@sH]]ol1r/C)ccc25nnn52c
replace c at position 5 with S,CHIC[Snl+=(3(-Cn3cF[-]1/@sH]]ol1r/C)ccc25nnn52c
add ] at position 9,CHIC[Snl+]=(3(-Cn3cF[-]1/@sH]]ol1r/C)ccc25nnn52c
add ] at position 43,CHIC[Snl+]=(3(-Cn3cF[-]1/@sH]]ol1r/C)ccc25n]nn52c
add ) at position 19,CHIC[Snl+]=(3(-Cn3c)F[-]1/@sH]]ol1r/C)ccc25n]nn52c
add O at position 17,CHIC[Snl+]=(3(-CnO3c)F[-]1/@sH]]ol1r/C)ccc25n]nn52c
add [ at position 43,CHIC[Snl+]=(3(-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]nn52c
add s at position 47,CHIC[Snl+]=(3(-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]snn52c
replace ( at position 13 with 4,CHIC[Snl+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]snn52c
add # at position 48,CHIC[Snl+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]s#nn52c
add O at position 3,CHIOC[Snl+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]s#nn52c
add C at position 0,CCHIOC[Snl+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]s#nn52c
add ) at position 45,CCHIOC[Snl+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2)[5n]s#nn52c
remove l from position 9,CCHIOC[Sn+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2)[5n]s#nn52c
remove I from position 3,CCHOC[Sn+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2)[5n]s#nn52c
add C at position 21,CCHOC[Sn+]=(34-CnO3c)CF[-]1/@sH]]ol1r/C)ccc2)[5n]s#nn52c
remove s from position 49,CCHOC[Sn+]=(34-CnO3c)CF[-]1/@sH]]ol1r/C)ccc2)[5n]#nn52c
remove F from position 22,CCHOC[Sn+]=(34-CnO3c)C[-]1/@sH]]ol1r/C)ccc2)[5n]#nn52c
replace 4 at position 13 with F,CCHOC[Sn+]=(3F-CnO3c)C[-]1/@sH]]ol1r/C)ccc2)[5n]#nn52c
replace / at position 26 with n,CCHOC[Sn+]=(3F-CnO3c)C[-]1n@sH]]ol1r/C)ccc2)[5n]#nn52c
replace F at position 13 with ),CCHOC[Sn+]=(3)-CnO3c)C[-]1n@sH]]ol1r/C)ccc2)[5n]#nn52c
add H at position 52,CCHOC[Sn+]=(3)-CnO3c)C[-]1n@sH]]ol1r/C)ccc2)[5n]#nn5H2c
add 1 at position 29,CCHOC[Sn+]=(3)-CnO3c)C[-]1n@s1H]]ol1r/C)ccc2)[5n]#nn5H2c
remove 1 from position 25,CCHOC[Sn+]=(3)-CnO3c)C[-]n@s1H]]ol1r/C)ccc2)[5n]#nn5H2c
remove s from position 27,CCHOC[Sn+]=(3)-CnO3c)C[-]n@1H]]ol1r/C)ccc2)[5n]#nn5H2c
remove n from position 16,CCHOC[Sn+]=(3)-CO3c)C[-]n@1H]]ol1r/C)ccc2)[5n]#nn5H2c
add 1 at position 29,CCHOC[Sn+]=(3)-CO3c)C[-]n@1H]1]ol1r/C)ccc2)[5n]#nn5H2c
remove 5 from position 44,CCHOC[Sn+]=(3)-CO3c)C[-]n@1H]1]ol1r/C)ccc2)[n]#nn5H2c
remove l from position 32,CCHOC[Sn+]=(3)-CO3c)C[-]n@1H]1]o1r/C)ccc2)[n]#nn5H2c
remove c from position 18,CCHOC[Sn+]=(3)-CO3)C[-]n@1H]1]o1r/C)ccc2)[n]#nn5H2c
remove 1 from position 25,CCHOC[Sn+]=(3)-CO3)C[-]n@H]1]o1r/C)ccc2)[n]#nn5H2c
replace ] at position 28 with C,CCHOC[Sn+]=(3)-CO3)C[-]n@H]1Co1r/C)ccc2)[n]#nn5H2c
replace 5 at position 46 with c,CCHOC[Sn+]=(3)-CO3)C[-]n@H]1Co1r/C)ccc2)[n]#nncH2c
add O at position 17,CCHOC[Sn+]=(3)-COO3)C[-]n@H]1Co1r/C)ccc2)[n]#nncH2c
add @ at position 10,CCHOC[Sn+]@=(3)-COO3)C[-]n@H]1Co1r/C)ccc2)[n]#nncH2c
remove S from position 6,CCHOC[n+]@=(3)-COO3)C[-]n@H]1Co1r/C)ccc2)[n]#nncH2c
replace O at position 16 with =,CCHOC[n+]@=(3)-C=O3)C[-]n@H]1Co1r/C)ccc2)[n]#nncH2c
replace n at position 24 with C,CCHOC[n+]@=(3)-C=O3)C[-]C@H]1Co1r/C)ccc2)[n]#nncH2c
replace n at position 6 with N,CCHOC[N+]@=(3)-C=O3)C[-]C@H]1Co1r/C)ccc2)[n]#nncH2c
add H at position 7,CCHOC[NH+]@=(3)-C=O3)C[-]C@H]1Co1r/C)ccc2)[n]#nncH2c
remove = from position 11,CCHOC[NH+]@(3)-C=O3)C[-]C@H]1Co1r/C)ccc2)[n]#nncH2c
replace 3 at position 18 with O,CCHOC[NH+]@(3)-C=OO)C[-]C@H]1Co1r/C)ccc2)[n]#nncH2c
add - at position 33,CCHOC[NH+]@(3)-C=OO)C[-]C@H]1Co1r-/C)ccc2)[n]#nncH2c
replace @ at position 10 with ],CCHOC[NH+]](3)-C=OO)C[-]C@H]1Co1r-/C)ccc2)[n]#nncH2c
replace o at position 30 with (,CCHOC[NH+]](3)-C=OO)C[-]C@H]1C(1r-/C)ccc2)[n]#nncH2c
replace ( at position 30 with C,CCHOC[NH+]](3)-C=OO)C[-]C@H]1CC1r-/C)ccc2)[n]#nncH2c
add 1 at position 37,CCHOC[NH+]](3)-C=OO)C[-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
add 2 at position 22,CCHOC[NH+]](3)-C=OO)C[2-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
replace H at position 2 with N,CCNOC[NH+]](3)-C=OO)C[2-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
remove ] from position 9,CCNOC[NH+](3)-C=OO)C[2-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
replace 3 at position 11 with C,CCNOC[NH+](C)-C=OO)C[2-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
remove O from position 17,CCNOC[NH+](C)-C=O)C[2-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
add C at position 29,CCNOC[NH+](C)-C=O)C[2-]C@H]1CCC1r-/C)1ccc2)[n]#nncH2c
remove - from position 33,CCNOC[NH+](C)-C=O)C[2-]C@H]1CCC1r/C)1ccc2)[n]#nncH2c
remove ] from position 22,CCNOC[NH+](C)-C=O)C[2-C@H]1CCC1r/C)1ccc2)[n]#nncH2c
remove - from position 13,CCNOC[NH+](C)C=O)C[2-C@H]1CCC1r/C)1ccc2)[n]#nncH2c
remove / from position 31,CCNOC[NH+](C)C=O)C[2-C@H]1CCC1rC)1ccc2)[n]#nncH2c
add ( at position 3,CCN(OC[NH+](C)C=O)C[2-C@H]1CCC1rC)1ccc2)[n]#nncH2c
add H at position 42,CCN(OC[NH+](C)C=O)C[2-C@H]1CCC1rC)1ccc2)[nH]#nncH2c
remove - from position 21,CCN(OC[NH+](C)C=O)C[2C@H]1CCC1rC)1ccc2)[nH]#nncH2c
add ( at position 32,CCN(OC[NH+](C)C=O)C[2C@H]1CCC1rC()1ccc2)[nH]#nncH2c
add = at position 33,CCN(OC[NH+](C)C=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c
add S at position 12,CCN(OC[NH+](SC)C=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c
add ) at position 15,CCN(OC[NH+](SC))C=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c
add 1 at position 54,CCN(OC[NH+](SC))C=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c1
replace O at position 4 with C,CCN(CC[NH+](SC))C=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c1
add ( at position 17,CCN(CC[NH+](SC))C(=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c1
add N at position 32,CCN(CC[NH+](SC))C(=O)C[2C@H]1CCCN1rC(=)1ccc2)[nH]#nncH2c1
add O at position 38,CCN(CC[NH+](SC))C(=O)C[2C@H]1CCCN1rC(=O)1ccc2)[nH]#nncH2c1
remove r from position 34,CCN(CC[NH+](SC))C(=O)C[2C@H]1CCCN1C(=O)1ccc2)[nH]#nncH2c1
add C at position 15,CCN(CC[NH+](SC)C)C(=O)C[2C@H]1CCCN1C(=O)1ccc2)[nH]#nncH2c1
remove C from position 0,CN(CC[NH+](SC)C)C(=O)C[2C@H]1CCCN1C(=O)1ccc2)[nH]#nncH2c1
remove S from position 11,CN(CC[NH+](C)C)C(=O)C[2C@H]1CCCN1C(=O)1ccc2)[nH]#nncH2c1
add + at position 29,CN(CC[NH+](C)C)C(=O)C[2C@H]1C+CCN1C(=O)1ccc2)[nH]#nncH2c1
add c at position 39,CN(CC[NH+](C)C)C(=O)C[2C@H]1C+CCN1C(=O)c1ccc2)[nH]#nncH2c1
remove # from position 50,CN(CC[NH+](C)C)C(=O)C[2C@H]1C+CCN1C(=O)c1ccc2)[nH]nncH2c1
remove 2 from position 22,CN(CC[NH+](C)C)C(=O)C[C@H]1C+CCN1C(=O)c1ccc2)[nH]nncH2c1
remove ) from position 44,CN(CC[NH+](C)C)C(=O)C[C@H]1C+CCN1C(=O)c1ccc2[nH]nncH2c1
remove H from position 51,CN(CC[NH+](C)C)C(=O)C[C@H]1C+CCN1C(=O)c1ccc2[nH]nnc2c1
replace + at position 28 with O,CN(CC[NH+](C)C)C(=O)C[C@H]1COCCN1C(=O)c1ccc2[nH]nnc2c1
final: CN(CC[NH+](C)C)C(=O)C[C@H]1COCCN1C(=O)c1ccc2[nH]nnc2c1,CN(CC[NH+](C)C)C(=O)C[C@H]1COCCN1C(=O)c1ccc2[nH]nnc2c1
