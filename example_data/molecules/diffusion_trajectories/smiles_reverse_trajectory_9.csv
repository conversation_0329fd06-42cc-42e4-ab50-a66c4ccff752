log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove C from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add S at position 6,oH3H-]SN)
add [ at position 2,oH[3H-]SN)
add 3 at position 3,oH[33H-]SN)
replace S at position 8 with r,oH[33H-]rN)
add n at position 2,oHn[33H-]rN)
add 4 at position 2,oH4n[33H-]rN)
replace [ at position 4 with H,oH4nH33H-]rN)
add 2 at position 5,oH4nH233H-]rN)
add O at position 2,oHO4nH233H-]rN)
remove H from position 9,oHO4nH233-]rN)
add 6 at position 13,oHO4nH233-]rN6)
replace H at position 1 with 6,o6O4nH233-]rN6)
remove H from position 5,o6O4n233-]rN6)
replace 4 at position 3 with r,o6Orn233-]rN6)
add r at position 6,o6Orn2r33-]rN6)
add s at position 11,o6Orn2r33-]srN6)
remove 6 from position 14,o6Orn2r33-]srN)
replace ] at position 10 with H,o6Orn2r33-HsrN)
remove O from position 2,o6rn2r33-HsrN)
add 7 at position 11,o6rn2r33-Hs7rN)
replace N at position 13 with /,o6rn2r33-Hs7r/)
add S at position 6,o6rn2rS33-Hs7r/)
add l at position 2,o6lrn2rS33-Hs7r/)
add F at position 11,o6lrn2rS33-FHs7r/)
replace o at position 0 with S,S6lrn2rS33-FHs7r/)
remove S from position 7,S6lrn2r33-FHs7r/)
add B at position 10,S6lrn2r33-BFHs7r/)
remove l from position 2,S6rn2r33-BFHs7r/)
add r at position 1,Sr6rn2r33-BFHs7r/)
add ) at position 12,Sr6rn2r33-BF)Hs7r/)
replace F at position 11 with 3,Sr6rn2r33-B3)Hs7r/)
add ) at position 19,Sr6rn2r33-B3)Hs7r/))
add 1 at position 2,Sr16rn2r33-B3)Hs7r/))
add O at position 1,SOr16rn2r33-B3)Hs7r/))
add C at position 21,SOr16rn2r33-B3)Hs7r/)C)
remove r from position 2,SO16rn2r33-B3)Hs7r/)C)
add B at position 14,SO16rn2r33-B3)BHs7r/)C)
add C at position 19,SO16rn2r33-B3)BHs7rC/)C)
add c at position 17,SO16rn2r33-B3)BHsc7rC/)C)
replace - at position 10 with 6,SO16rn2r336B3)BHsc7rC/)C)
add 1 at position 12,SO16rn2r336B13)BHsc7rC/)C)
replace C at position 24 with r,SO16rn2r336B13)BHsc7rC/)r)
replace / at position 22 with O,SO16rn2r336B13)BHsc7rCO)r)
remove s from position 17,SO16rn2r336B13)BHc7rCO)r)
add F at position 19,SO16rn2r336B13)BHc7FrCO)r)
replace r at position 4 with 2,SO162n2r336B13)BHc7FrCO)r)
add / at position 21,SO162n2r336B13)BHc7Fr/CO)r)
remove B from position 15,SO162n2r336B13)Hc7Fr/CO)r)
add 2 at position 18,SO162n2r336B13)Hc72Fr/CO)r)
replace S at position 0 with 7,7O162n2r336B13)Hc72Fr/CO)r)
add c at position 20,7O162n2r336B13)Hc72Fcr/CO)r)
add c at position 11,7O162n2r336cB13)Hc72Fcr/CO)r)
add ) at position 29,7O162n2r336cB13)Hc72Fcr/CO)r))
replace 3 at position 9 with =,7O162n2r3=6cB13)Hc72Fcr/CO)r))
replace ) at position 29 with 7,7O162n2r3=6cB13)Hc72Fcr/CO)r)7
add = at position 13,7O162n2r3=6cB=13)Hc72Fcr/CO)r)7
add B at position 20,7O162n2r3=6cB=13)Hc7B2Fcr/CO)r)7
remove 1 from position 2,7O62n2r3=6cB=13)Hc7B2Fcr/CO)r)7
remove 7 from position 0,O62n2r3=6cB=13)Hc7B2Fcr/CO)r)7
replace 6 at position 8 with S,O62n2r3=ScB=13)Hc7B2Fcr/CO)r)7
add N at position 3,O62Nn2r3=ScB=13)Hc7B2Fcr/CO)r)7
remove H from position 16,O62Nn2r3=ScB=13)c7B2Fcr/CO)r)7
replace 3 at position 14 with c,O62Nn2r3=ScB=1c)c7B2Fcr/CO)r)7
add 5 at position 10,O62Nn2r3=S5cB=1c)c7B2Fcr/CO)r)7
add l at position 13,O62Nn2r3=S5cBl=1c)c7B2Fcr/CO)r)7
replace ) at position 17 with F,O62Nn2r3=S5cBl=1cFc7B2Fcr/CO)r)7
add 1 at position 32,O62Nn2r3=S5cBl=1cFc7B2Fcr/CO)r)71
add 4 at position 18,O62Nn2r3=S5cBl=1cF4c7B2Fcr/CO)r)71
add 7 at position 21,O62Nn2r3=S5cBl=1cF4c77B2Fcr/CO)r)71
replace 4 at position 18 with s,O62Nn2r3=S5cBl=1cFsc77B2Fcr/CO)r)71
replace 7 at position 21 with (,O62Nn2r3=S5cBl=1cFsc7(B2Fcr/CO)r)71
remove ) from position 30,O62Nn2r3=S5cBl=1cFsc7(B2Fcr/COr)71
add = at position 6,O62Nn2=r3=S5cBl=1cFsc7(B2Fcr/COr)71
add + at position 29,O62Nn2=r3=S5cBl=1cFsc7(B2Fcr/+COr)71
add 2 at position 30,O62Nn2=r3=S5cBl=1cFsc7(B2Fcr/+2COr)71
remove r from position 33,O62Nn2=r3=S5cBl=1cFsc7(B2Fcr/+2CO)71
add 1 at position 22,O62Nn2=r3=S5cBl=1cFsc71(B2Fcr/+2CO)71
add o at position 23,O62Nn2=r3=S5cBl=1cFsc71o(B2Fcr/+2CO)71
remove / from position 30,O62Nn2=r3=S5cBl=1cFsc71o(B2Fcr+2CO)71
add s at position 30,O62Nn2=r3=S5cBl=1cFsc71o(B2Fcrs+2CO)71
add r at position 19,O62Nn2=r3=S5cBl=1cFrsc71o(B2Fcrs+2CO)71
add + at position 4,O62N+n2=r3=S5cBl=1cFrsc71o(B2Fcrs+2CO)71
replace = at position 10 with ),O62N+n2=r3)S5cBl=1cFrsc71o(B2Fcrs+2CO)71
remove 2 from position 28,O62N+n2=r3)S5cBl=1cFrsc71o(BFcrs+2CO)71
remove 7 from position 37,O62N+n2=r3)S5cBl=1cFrsc71o(BFcrs+2CO)1
add 4 at position 17,O62N+n2=r3)S5cBl=41cFrsc71o(BFcrs+2CO)1
add ] at position 7,O62N+n2]=r3)S5cBl=41cFrsc71o(BFcrs+2CO)1
add ) at position 36,O62N+n2]=r3)S5cBl=41cFrsc71o(BFcrs+2)CO)1
replace = at position 17 with H,O62N+n2]=r3)S5cBlH41cFrsc71o(BFcrs+2)CO)1
replace + at position 34 with ),O62N+n2]=r3)S5cBlH41cFrsc71o(BFcrs)2)CO)1
add B at position 36,O62N+n2]=r3)S5cBlH41cFrsc71o(BFcrs)2B)CO)1
add n at position 19,O62N+n2]=r3)S5cBlH4n1cFrsc71o(BFcrs)2B)CO)1
replace H at position 17 with -,O62N+n2]=r3)S5cBl-4n1cFrsc71o(BFcrs)2B)CO)1
replace s at position 24 with c,O62N+n2]=r3)S5cBl-4n1cFrcc71o(BFcrs)2B)CO)1
add O at position 13,O62N+n2]=r3)SO5cBl-4n1cFrcc71o(BFcrs)2B)CO)1
remove 7 from position 27,O62N+n2]=r3)SO5cBl-4n1cFrcc1o(BFcrs)2B)CO)1
remove r from position 9,O62N+n2]=3)SO5cBl-4n1cFrcc1o(BFcrs)2B)CO)1
remove N from position 3,O62+n2]=3)SO5cBl-4n1cFrcc1o(BFcrs)2B)CO)1
add r at position 10,O62+n2]=3)rSO5cBl-4n1cFrcc1o(BFcrs)2B)CO)1
remove ) from position 34,O62+n2]=3)rSO5cBl-4n1cFrcc1o(BFcrs2B)CO)1
remove r from position 10,O62+n2]=3)SO5cBl-4n1cFrcc1o(BFcrs2B)CO)1
add c at position 8,O62+n2]=c3)SO5cBl-4n1cFrcc1o(BFcrs2B)CO)1
remove B from position 35,O62+n2]=c3)SO5cBl-4n1cFrcc1o(BFcrs2)CO)1
remove F from position 22,O62+n2]=c3)SO5cBl-4n1crcc1o(BFcrs2)CO)1
replace 5 at position 13 with 3,O62+n2]=c3)SO3cBl-4n1crcc1o(BFcrs2)CO)1
add - at position 11,O62+n2]=c3)-SO3cBl-4n1crcc1o(BFcrs2)CO)1
replace r at position 32 with (,O62+n2]=c3)-SO3cBl-4n1crcc1o(BFc(s2)CO)1
remove r from position 23,O62+n2]=c3)-SO3cBl-4n1ccc1o(BFc(s2)CO)1
replace F at position 29 with /,O62+n2]=c3)-SO3cBl-4n1ccc1o(B/c(s2)CO)1
remove S from position 12,O62+n2]=c3)-O3cBl-4n1ccc1o(B/c(s2)CO)1
add 2 at position 12,O62+n2]=c3)-2O3cBl-4n1ccc1o(B/c(s2)CO)1
add I at position 4,O62+In2]=c3)-2O3cBl-4n1ccc1o(B/c(s2)CO)1
replace I at position 4 with =,O62+=n2]=c3)-2O3cBl-4n1ccc1o(B/c(s2)CO)1
replace 4 at position 20 with ],O62+=n2]=c3)-2O3cBl-]n1ccc1o(B/c(s2)CO)1
add S at position 5,O62+=Sn2]=c3)-2O3cBl-]n1ccc1o(B/c(s2)CO)1
replace 2 at position 2 with I,O6I+=Sn2]=c3)-2O3cBl-]n1ccc1o(B/c(s2)CO)1
add / at position 3,O6I/+=Sn2]=c3)-2O3cBl-]n1ccc1o(B/c(s2)CO)1
replace + at position 4 with (,O6I/(=Sn2]=c3)-2O3cBl-]n1ccc1o(B/c(s2)CO)1
replace l at position 20 with 2,O6I/(=Sn2]=c3)-2O3cB2-]n1ccc1o(B/c(s2)CO)1
add 2 at position 17,O6I/(=Sn2]=c3)-2O23cB2-]n1ccc1o(B/c(s2)CO)1
add @ at position 10,O6I/(=Sn2]@=c3)-2O23cB2-]n1ccc1o(B/c(s2)CO)1
remove S from position 6,O6I/(=n2]@=c3)-2O23cB2-]n1ccc1o(B/c(s2)CO)1
replace O at position 16 with ),O6I/(=n2]@=c3)-2)23cB2-]n1ccc1o(B/c(s2)CO)1
replace n at position 24 with 2,O6I/(=n2]@=c3)-2)23cB2-]21ccc1o(B/c(s2)CO)1
replace n at position 6 with C,O6I/(=C2]@=c3)-2)23cB2-]21ccc1o(B/c(s2)CO)1
add c at position 7,O6I/(=Cc2]@=c3)-2)23cB2-]21ccc1o(B/c(s2)CO)1
remove = from position 11,O6I/(=Cc2]@c3)-2)23cB2-]21ccc1o(B/c(s2)CO)1
replace 3 at position 18 with O,O6I/(=Cc2]@c3)-2)2OcB2-]21ccc1o(B/c(s2)CO)1
add - at position 33,O6I/(=Cc2]@c3)-2)2OcB2-]21ccc1o(B-/c(s2)CO)1
replace @ at position 10 with c,O6I/(=Cc2]cc3)-2)2OcB2-]21ccc1o(B-/c(s2)CO)1
replace o at position 30 with (,O6I/(=Cc2]cc3)-2)2OcB2-]21ccc1((B-/c(s2)CO)1
replace ( at position 30 with N,O6I/(=Cc2]cc3)-2)2OcB2-]21ccc1N(B-/c(s2)CO)1
add c at position 37,O6I/(=Cc2]cc3)-2)2OcB2-]21ccc1N(B-/c(cs2)CO)1
add c at position 22,O6I/(=Cc2]cc3)-2)2OcB2c-]21ccc1N(B-/c(cs2)CO)1
replace I at position 2 with C,O6C/(=Cc2]cc3)-2)2OcB2c-]21ccc1N(B-/c(cs2)CO)1
remove ] from position 9,O6C/(=Cc2cc3)-2)2OcB2c-]21ccc1N(B-/c(cs2)CO)1
replace 3 at position 11 with c,O6C/(=Cc2ccc)-2)2OcB2c-]21ccc1N(B-/c(cs2)CO)1
remove O from position 17,O6C/(=Cc2ccc)-2)2cB2c-]21ccc1N(B-/c(cs2)CO)1
add + at position 29,O6C/(=Cc2ccc)-2)2cB2c-]21ccc1+N(B-/c(cs2)CO)1
remove - from position 33,O6C/(=Cc2ccc)-2)2cB2c-]21ccc1+N(B/c(cs2)CO)1
remove ] from position 22,O6C/(=Cc2ccc)-2)2cB2c-21ccc1+N(B/c(cs2)CO)1
remove - from position 13,O6C/(=Cc2ccc)2)2cB2c-21ccc1+N(B/c(cs2)CO)1
remove / from position 31,O6C/(=Cc2ccc)2)2cB2c-21ccc1+N(Bc(cs2)CO)1
add 1 at position 3,O6C1/(=Cc2ccc)2)2cB2c-21ccc1+N(Bc(cs2)CO)1
add + at position 42,O6C1/(=Cc2ccc)2)2cB2c-21ccc1+N(Bc(cs2)CO)1+
remove - from position 21,O6C1/(=Cc2ccc)2)2cB2c21ccc1+N(Bc(cs2)CO)1+
add 2 at position 32,O6C1/(=Cc2ccc)2)2cB2c21ccc1+N(Bc2(cs2)CO)1+
add c at position 33,O6C1/(=Cc2ccc)2)2cB2c21ccc1+N(Bc2c(cs2)CO)1+
add c at position 12,O6C1/(=Cc2cccc)2)2cB2c21ccc1+N(Bc2c(cs2)CO)1+
replace 2 at position 17 with O,O6C1/(=Cc2cccc)2)OcB2c21ccc1+N(Bc2c(cs2)CO)1+
remove B from position 19,O6C1/(=Cc2cccc)2)Oc2c21ccc1+N(Bc2c(cs2)CO)1+
add c at position 35,O6C1/(=Cc2cccc)2)Oc2c21ccc1+N(Bc2c(ccs2)CO)1+
add = at position 2,O6=C1/(=Cc2cccc)2)Oc2c21ccc1+N(Bc2c(ccs2)CO)1+
remove ) from position 43,O6=C1/(=Cc2cccc)2)Oc2c21ccc1+N(Bc2c(ccs2)CO1+
add C at position 29,O6=C1/(=Cc2cccc)2)Oc2c21ccc1+CN(Bc2c(ccs2)CO1+
replace ) at position 15 with c,O6=C1/(=Cc2ccccc2)Oc2c21ccc1+CN(Bc2c(ccs2)CO1+
replace B at position 32 with C,O6=C1/(=Cc2ccccc2)Oc2c21ccc1+CN(Cc2c(ccs2)CO1+
remove + from position 45,O6=C1/(=Cc2ccccc2)Oc2c21ccc1+CN(Cc2c(ccs2)CO1
add / at position 9,O6=C1/(=C/c2ccccc2)Oc2c21ccc1+CN(Cc2c(ccs2)CO1
add 2 at position 30,O6=C1/(=C/c2ccccc2)Oc2c21ccc1+2CN(Cc2c(ccs2)CO1
remove 6 from position 1,O=C1/(=C/c2ccccc2)Oc2c21ccc1+2CN(Cc2c(ccs2)CO1
remove 2 from position 22,O=C1/(=C/c2ccccc2)Oc2c1ccc1+2CN(Cc2c(ccs2)CO1
add C at position 5,O=C1/C(=C/c2ccccc2)Oc2c1ccc1+2CN(Cc2c(ccs2)CO1
remove ( from position 37,O=C1/C(=C/c2ccccc2)Oc2c1ccc1+2CN(Cc2cccs2)CO1
replace + at position 28 with c,O=C1/C(=C/c2ccccc2)Oc2c1ccc1c2CN(Cc2cccs2)CO1
final: O=C1/C(=C/c2ccccc2)Oc2c1ccc1c2CN(Cc2cccs2)CO1,O=C1/C(=C/c2ccccc2)Oc2c1ccc1c2CN(Cc2cccs2)CO1
