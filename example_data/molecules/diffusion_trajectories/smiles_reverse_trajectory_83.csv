log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 5,oFc5BH-C
remove c from position 2,oF5BH-<PERSON>
remove B from position 3,oF5H-<PERSON>
replace - at position 4 with ],oF5H]C
remove <PERSON> from position 5,oF5H]
add B at position 4,oF5HB]
add N at position 6,oF5HB]N
replace B at position 4 with -,oF5H-]N
add r at position 7,oF5H-]Nr
add S at position 6,oF5H-]SNr
add [ at position 2,oF[5H-]SNr
add 5 at position 3,oF[55H-]SNr
replace S at position 8 with r,oF[55H-]rNr
add l at position 2,oFl[55H-]rNr
add 4 at position 2,oF4l[55H-]rNr
replace [ at position 4 with H,oF4lH55H-]rNr
add 7 at position 5,oF4lH755H-]rNr
add O at position 2,oFO4lH755H-]rNr
remove H from position 9,oFO4lH755-]rNr
add 6 at position 13,oFO4lH755-]rN6r
replace F at position 1 with n,onO4lH755-]rN6r
remove H from position 5,onO4l755-]rN6r
replace 4 at position 3 with r,onOrl755-]rN6r
add = at position 6,onOrl7=55-]rN6r
add s at position 11,onOrl7=55-]srN6r
remove 6 from position 14,onOrl7=55-]srNr
replace ] at position 10 with 7,onOrl7=55-7srNr
remove O from position 2,onrl7=55-7srNr
add 2 at position 11,onrl7=55-7s2rNr
replace N at position 13 with /,onrl7=55-7s2r/r
add S at position 6,onrl7=S55-7s2r/r
add l at position 2,onlrl7=S55-7s2r/r
add F at position 11,onlrl7=S55-F7s2r/r
replace o at position 0 with S,Snlrl7=S55-F7s2r/r
remove S from position 7,Snlrl7=55-F7s2r/r
add B at position 10,Snlrl7=55-BF7s2r/r
remove l from position 2,Snrl7=55-BF7s2r/r
add r at position 1,Srnrl7=55-BF7s2r/r
add ) at position 12,Srnrl7=55-BF)7s2r/r
replace F at position 11 with 3,Srnrl7=55-B3)7s2r/r
add 4 at position 19,Srnrl7=55-B3)7s2r/r4
add N at position 2,SrNnrl7=55-B3)7s2r/r4
add 6 at position 1,S6rNnrl7=55-B3)7s2r/r4
add F at position 21,S6rNnrl7=55-B3)7s2r/rF4
remove r from position 2,S6Nnrl7=55-B3)7s2r/rF4
add c at position 14,S6Nnrl7=55-B3)c7s2r/rF4
add c at position 19,S6Nnrl7=55-B3)c7s2rc/rF4
add B at position 17,S6Nnrl7=55-B3)c7sB2rc/rF4
replace - at position 10 with 1,S6Nnrl7=551B3)c7sB2rc/rF4
add = at position 12,S6Nnrl7=551B=3)c7sB2rc/rF4
replace F at position 24 with ),S6Nnrl7=551B=3)c7sB2rc/r)4
replace / at position 22 with n,S6Nnrl7=551B=3)c7sB2rcnr)4
remove s from position 17,S6Nnrl7=551B=3)c7B2rcnr)4
add - at position 19,S6Nnrl7=551B=3)c7B2-rcnr)4
replace r at position 4 with (,S6Nn(l7=551B=3)c7B2-rcnr)4
add ( at position 21,S6Nn(l7=551B=3)c7B2-r(cnr)4
add r at position 5,S6Nn(rl7=551B=3)c7B2-r(cnr)4
add ) at position 27,S6Nn(rl7=551B=3)c7B2-r(cnr))4
replace S at position 0 with C,C6Nn(rl7=551B=3)c7B2-r(cnr))4
add 2 at position 20,C6Nn(rl7=551B=3)c7B22-r(cnr))4
replace 7 at position 7 with 4,C6Nn(rl4=551B=3)c7B22-r(cnr))4
add 2 at position 15,C6Nn(rl4=551B=32)c7B22-r(cnr))4
replace 4 at position 30 with =,C6Nn(rl4=551B=32)c7B22-r(cnr))=
replace ) at position 29 with 7,C6Nn(rl4=551B=32)c7B22-r(cnr)7=
add ) at position 27,C6Nn(rl4=551B=32)c7B22-r(cn)r)7=
replace 5 at position 9 with S,C6Nn(rl4=S51B=32)c7B22-r(cn)r)7=
add 2 at position 2,C62Nn(rl4=S51B=32)c7B22-r(cn)r)7=
replace ( at position 25 with /,C62Nn(rl4=S51B=32)c7B22-r/cn)r)7=
remove - from position 23,C62Nn(rl4=S51B=32)c7B22r/cn)r)7=
remove l from position 7,C62Nn(r4=S51B=32)c7B22r/cn)r)7=
replace 3 at position 14 with c,C62Nn(r4=S51B=c2)c7B22r/cn)r)7=
add F at position 21,C62Nn(r4=S51B=c2)c7B2F2r/cn)r)7=
add l at position 13,C62Nn(r4=S51Bl=c2)c7B2F2r/cn)r)7=
replace ) at position 17 with F,C62Nn(r4=S51Bl=c2Fc7B2F2r/cn)r)7=
add c at position 32,C62Nn(r4=S51Bl=c2Fc7B2F2r/cn)r)7c=
add 4 at position 18,C62Nn(r4=S51Bl=c2F4c7B2F2r/cn)r)7c=
add 6 at position 21,C62Nn(r4=S51Bl=c2F4c76B2F2r/cn)r)7c=
replace 4 at position 18 with c,C62Nn(r4=S51Bl=c2Fcc76B2F2r/cn)r)7c=
replace 6 at position 21 with c,C62Nn(r4=S51Bl=c2Fcc7cB2F2r/cn)r)7c=
remove ) from position 30,C62Nn(r4=S51Bl=c2Fcc7cB2F2r/cnr)7c=
add = at position 6,C62Nn(=r4=S51Bl=c2Fcc7cB2F2r/cnr)7c=
add + at position 29,C62Nn(=r4=S51Bl=c2Fcc7cB2F2r/+cnr)7c=
add c at position 30,C62Nn(=r4=S51Bl=c2Fcc7cB2F2r/+ccnr)7c=
remove r from position 33,C62Nn(=r4=S51Bl=c2Fcc7cB2F2r/+ccn)7c=
add c at position 22,C62Nn(=r4=S51Bl=c2Fcc7ccB2F2r/+ccn)7c=
add o at position 23,C62Nn(=r4=S51Bl=c2Fcc7cocB2F2r/+ccn)7c=
remove / from position 30,C62Nn(=r4=S51Bl=c2Fcc7cocB2F2r+ccn)7c=
add 1 at position 30,C62Nn(=r4=S51Bl=c2Fcc7cocB2F2r1+ccn)7c=
add r at position 19,C62Nn(=r4=S51Bl=c2Frcc7cocB2F2r1+ccn)7c=
add ) at position 4,C62N)n(=r4=S51Bl=c2Frcc7cocB2F2r1+ccn)7c=
replace = at position 10 with ),C62N)n(=r4)S51Bl=c2Frcc7cocB2F2r1+ccn)7c=
remove 2 from position 28,C62N)n(=r4)S51Bl=c2Frcc7cocBF2r1+ccn)7c=
remove 7 from position 37,C62N)n(=r4)S51Bl=c2Frcc7cocBF2r1+ccn)c=
add 4 at position 17,C62N)n(=r4)S51Bl=4c2Frcc7cocBF2r1+ccn)c=
add ] at position 7,C62N)n(]=r4)S51Bl=4c2Frcc7cocBF2r1+ccn)c=
add c at position 36,C62N)n(]=r4)S51Bl=4c2Frcc7cocBF2r1+cccn)c=
replace = at position 17 with H,C62N)n(]=r4)S51BlH4c2Frcc7cocBF2r1+cccn)c=
replace + at position 34 with ),C62N)n(]=r4)S51BlH4c2Frcc7cocBF2r1)cccn)c=
add B at position 36,C62N)n(]=r4)S51BlH4c2Frcc7cocBF2r1)cBccn)c=
add n at position 19,C62N)n(]=r4)S51BlH4nc2Frcc7cocBF2r1)cBccn)c=
replace H at position 17 with -,C62N)n(]=r4)S51Bl-4nc2Frcc7cocBF2r1)cBccn)c=
replace = at position 43 with 1,C62N)n(]=r4)S51Bl-4nc2Frcc7cocBF2r1)cBccn)c1
add O at position 13,C62N)n(]=r4)SO51Bl-4nc2Frcc7cocBF2r1)cBccn)c1
remove 7 from position 27,C62N)n(]=r4)SO51Bl-4nc2FrcccocBF2r1)cBccn)c1
remove r from position 9,C62N)n(]=4)SO51Bl-4nc2FrcccocBF2r1)cBccn)c1
remove N from position 3,C62)n(]=4)SO51Bl-4nc2FrcccocBF2r1)cBccn)c1
add r at position 10,C62)n(]=4)rSO51Bl-4nc2FrcccocBF2r1)cBccn)c1
remove ) from position 34,C62)n(]=4)rSO51Bl-4nc2FrcccocBF2r1cBccn)c1
remove r from position 10,C62)n(]=4)SO51Bl-4nc2FrcccocBF2r1cBccn)c1
add N at position 8,C62)n(]=N4)SO51Bl-4nc2FrcccocBF2r1cBccn)c1
remove B from position 35,C62)n(]=N4)SO51Bl-4nc2FrcccocBF2r1cccn)c1
remove F from position 22,C62)n(]=N4)SO51Bl-4nc2rcccocBF2r1cccn)c1
replace 5 at position 13 with 3,C62)n(]=N4)SO31Bl-4nc2rcccocBF2r1cccn)c1
add - at position 11,C62)n(]=N4)-SO31Bl-4nc2rcccocBF2r1cccn)c1
replace r at position 32 with (,C62)n(]=N4)-SO31Bl-4nc2rcccocBF2(1cccn)c1
remove r from position 23,C62)n(]=N4)-SO31Bl-4nc2cccocBF2(1cccn)c1
replace F at position 29 with /,C62)n(]=N4)-SO31Bl-4nc2cccocB/2(1cccn)c1
remove S from position 12,C62)n(]=N4)-O31Bl-4nc2cccocB/2(1cccn)c1
add O at position 12,C62)n(]=N4)-OO31Bl-4nc2cccocB/2(1cccn)c1
add I at position 4,C62)In(]=N4)-OO31Bl-4nc2cccocB/2(1cccn)c1
replace I at position 4 with @,C62)@n(]=N4)-OO31Bl-4nc2cccocB/2(1cccn)c1
replace 4 at position 20 with ],C62)@n(]=N4)-OO31Bl-]nc2cccocB/2(1cccn)c1
add S at position 5,C62)@Sn(]=N4)-OO31Bl-]nc2cccocB/2(1cccn)c1
replace 2 at position 2 with I,C6I)@Sn(]=N4)-OO31Bl-]nc2cccocB/2(1cccn)c1
add ) at position 3,C6I))@Sn(]=N4)-OO31Bl-]nc2cccocB/2(1cccn)c1
replace ) at position 4 with C,C6I)C@Sn(]=N4)-OO31Bl-]nc2cccocB/2(1cccn)c1
replace l at position 20 with C,C6I)C@Sn(]=N4)-OO31BC-]nc2cccocB/2(1cccn)c1
add 2 at position 17,C6I)C@Sn(]=N4)-OO231BC-]nc2cccocB/2(1cccn)c1
add @ at position 10,C6I)C@Sn(]@=N4)-OO231BC-]nc2cccocB/2(1cccn)c1
remove S from position 6,C6I)C@n(]@=N4)-OO231BC-]nc2cccocB/2(1cccn)c1
replace O at position 16 with ),C6I)C@n(]@=N4)-O)231BC-]nc2cccocB/2(1cccn)c1
replace n at position 24 with 2,C6I)C@n(]@=N4)-O)231BC-]2c2cccocB/2(1cccn)c1
replace n at position 6 with @,C6I)C@@(]@=N4)-O)231BC-]2c2cccocB/2(1cccn)c1
add ] at position 7,C6I)C@@](]@=N4)-O)231BC-]2c2cccocB/2(1cccn)c1
remove = from position 11,C6I)C@@](]@N4)-O)231BC-]2c2cccocB/2(1cccn)c1
replace 3 at position 18 with O,C6I)C@@](]@N4)-O)2O1BC-]2c2cccocB/2(1cccn)c1
add - at position 33,C6I)C@@](]@N4)-O)2O1BC-]2c2cccocB-/2(1cccn)c1
replace @ at position 10 with C,C6I)C@@](]CN4)-O)2O1BC-]2c2cccocB-/2(1cccn)c1
replace o at position 30 with (,C6I)C@@](]CN4)-O)2O1BC-]2c2ccc(cB-/2(1cccn)c1
replace ( at position 30 with ),C6I)C@@](]CN4)-O)2O1BC-]2c2ccc)cB-/2(1cccn)c1
add c at position 37,C6I)C@@](]CN4)-O)2O1BC-]2c2ccc)cB-/2(c1cccn)c1
add C at position 22,C6I)C@@](]CN4)-O)2O1BCC-]2c2ccc)cB-/2(c1cccn)c1
replace I at position 2 with (,C6()C@@](]CN4)-O)2O1BCC-]2c2ccc)cB-/2(c1cccn)c1
remove ] from position 9,C6()C@@](CN4)-O)2O1BCC-]2c2ccc)cB-/2(c1cccn)c1
replace 4 at position 11 with (,C6()C@@](CN()-O)2O1BCC-]2c2ccc)cB-/2(c1cccn)c1
remove O from position 17,C6()C@@](CN()-O)21BCC-]2c2ccc)cB-/2(c1cccn)c1
add - at position 29,C6()C@@](CN()-O)21BCC-]2c2ccc-)cB-/2(c1cccn)c1
remove - from position 33,C6()C@@](CN()-O)21BCC-]2c2ccc-)cB/2(c1cccn)c1
remove ] from position 22,C6()C@@](CN()-O)21BCC-2c2ccc-)cB/2(c1cccn)c1
remove - from position 13,C6()C@@](CN()O)21BCC-2c2ccc-)cB/2(c1cccn)c1
remove / from position 31,C6()C@@](CN()O)21BCC-2c2ccc-)cB2(c1cccn)c1
add C at position 3,C6(C)C@@](CN()O)21BCC-2c2ccc-)cB2(c1cccn)c1
add + at position 42,C6(C)C@@](CN()O)21BCC-2c2ccc-)cB2(c1cccn)c+1
remove - from position 21,C6(C)C@@](CN()O)21BCC2c2ccc-)cB2(c1cccn)c+1
add C at position 32,C6(C)C@@](CN()O)21BCC2c2ccc-)cB2C(c1cccn)c+1
add 1 at position 33,C6(C)C@@](CN()O)21BCC2c2ccc-)cB2C1(c1cccn)c+1
add C at position 12,C6(C)C@@](CNC()O)21BCC2c2ccc-)cB2C1(c1cccn)c+1
replace 2 at position 17 with N,C6(C)C@@](CNC()O)N1BCC2c2ccc-)cB2C1(c1cccn)c+1
remove B from position 19,C6(C)C@@](CNC()O)N1CC2c2ccc-)cB2C1(c1cccn)c+1
add ) at position 35,C6(C)C@@](CNC()O)N1CC2c2ccc-)cB2C1()c1cccn)c+1
add C at position 2,C6C(C)C@@](CNC()O)N1CC2c2ccc-)cB2C1()c1cccn)c+1
remove ) from position 43,C6C(C)C@@](CNC()O)N1CC2c2ccc-)cB2C1()c1cccnc+1
add l at position 29,C6C(C)C@@](CNC()O)N1CC2c2ccc-l)cB2C1()c1cccnc+1
replace ) at position 15 with =,C6C(C)C@@](CNC(=O)N1CC2c2ccc-l)cB2C1()c1cccnc+1
replace B at position 32 with c,C6C(C)C@@](CNC(=O)N1CC2c2ccc-l)cc2C1()c1cccnc+1
remove + from position 45,C6C(C)C@@](CNC(=O)N1CC2c2ccc-l)cc2C1()c1cccnc1
add H at position 9,C6C(C)C@@H](CNC(=O)N1CC2c2ccc-l)cc2C1()c1cccnc1
add C at position 30,C6C(C)C@@H](CNC(=O)N1CC2c2ccc-Cl)cc2C1()c1cccnc1
remove 6 from position 1,CC(C)C@@H](CNC(=O)N1CC2c2ccc-Cl)cc2C1()c1cccnc1
remove 2 from position 22,CC(C)C@@H](CNC(=O)N1CCc2ccc-Cl)cc2C1()c1cccnc1
add [ at position 5,CC(C)[C@@H](CNC(=O)N1CCc2ccc-Cl)cc2C1()c1cccnc1
remove ( from position 37,CC(C)[C@@H](CNC(=O)N1CCc2ccc-Cl)cc2C1)c1cccnc1
replace - at position 28 with (,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccnc1
final: CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccnc1,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccnc1
