log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove <PERSON> from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add S at position 6,oH3H-]SN)
add [ at position 2,oH[3H-]SN)
add 4 at position 3,oH[43H-]SN)
replace S at position 8 with r,oH[43H-]rN)
add n at position 2,oHn[43H-]rN)
add 4 at position 2,oH4n[43H-]rN)
replace [ at position 4 with H,oH4nH43H-]rN)
add ] at position 5,oH4nH]43H-]rN)
add O at position 2,oHO4nH]43H-]rN)
remove H from position 9,oHO4nH]43-]rN)
add 6 at position 13,oHO4nH]43-]rN6)
replace H at position 1 with 6,o6O4nH]43-]rN6)
remove H from position 5,o6O4n]43-]rN6)
replace 4 at position 3 with r,o6Orn]43-]rN6)
add r at position 6,o6Orn]r43-]rN6)
add s at position 11,o6Orn]r43-]srN6)
remove 6 from position 14,o6Orn]r43-]srN)
replace ] at position 10 with H,o6Orn]r43-HsrN)
remove O from position 2,o6rn]r43-HsrN)
add 7 at position 11,o6rn]r43-Hs7rN)
replace N at position 13 with 1,o6rn]r43-Hs7r1)
add S at position 6,o6rn]rS43-Hs7r1)
add l at position 2,o6lrn]rS43-Hs7r1)
add F at position 11,o6lrn]rS43-FHs7r1)
replace o at position 0 with S,S6lrn]rS43-FHs7r1)
remove S from position 7,S6lrn]r43-FHs7r1)
add B at position 10,S6lrn]r43-BFHs7r1)
remove l from position 2,S6rn]r43-BFHs7r1)
add r at position 1,Sr6rn]r43-BFHs7r1)
add ) at position 12,Sr6rn]r43-BF)Hs7r1)
replace F at position 11 with 3,Sr6rn]r43-B3)Hs7r1)
add ) at position 19,Sr6rn]r43-B3)Hs7r1))
add 1 at position 2,Sr16rn]r43-B3)Hs7r1))
add C at position 1,SCr16rn]r43-B3)Hs7r1))
add C at position 21,SCr16rn]r43-B3)Hs7r1)C)
remove r from position 2,SC16rn]r43-B3)Hs7r1)C)
add B at position 14,SC16rn]r43-B3)BHs7r1)C)
add C at position 19,SC16rn]r43-B3)BHs7rC1)C)
add N at position 17,SC16rn]r43-B3)BHsN7rC1)C)
replace - at position 10 with 6,SC16rn]r436B3)BHsN7rC1)C)
add 2 at position 12,SC16rn]r436B23)BHsN7rC1)C)
replace C at position 24 with r,SC16rn]r436B23)BHsN7rC1)r)
replace 1 at position 22 with ),SC16rn]r436B23)BHsN7rC))r)
remove s from position 17,SC16rn]r436B23)BHN7rC))r)
add F at position 19,SC16rn]r436B23)BHN7FrC))r)
replace r at position 4 with 2,SC162n]r436B23)BHN7FrC))r)
add / at position 21,SC162n]r436B23)BHN7Fr/C))r)
remove B from position 15,SC162n]r436B23)HN7Fr/C))r)
add 2 at position 18,SC162n]r436B23)HN72Fr/C))r)
replace S at position 0 with 7,7C162n]r436B23)HN72Fr/C))r)
add O at position 20,7C162n]r436B23)HN72FOr/C))r)
add O at position 11,7C162n]r436OB23)HN72FOr/C))r)
add ) at position 29,7C162n]r436OB23)HN72FOr/C))r))
replace 3 at position 9 with 7,7C162n]r476OB23)HN72FOr/C))r))
replace ) at position 29 with 7,7C162n]r476OB23)HN72FOr/C))r)7
add = at position 13,7C162n]r476OB=23)HN72FOr/C))r)7
add ) at position 20,7C162n]r476OB=23)HN7)2FOr/C))r)7
remove 1 from position 2,7C62n]r476OB=23)HN7)2FOr/C))r)7
remove 7 from position 0,C62n]r476OB=23)HN7)2FOr/C))r)7
replace 6 at position 8 with S,C62n]r47SOB=23)HN7)2FOr/C))r)7
add N at position 3,C62Nn]r47SOB=23)HN7)2FOr/C))r)7
remove H from position 16,C62Nn]r47SOB=23)N7)2FOr/C))r)7
replace 3 at position 14 with C,C62Nn]r47SOB=2C)N7)2FOr/C))r)7
add 5 at position 10,C62Nn]r47S5OB=2C)N7)2FOr/C))r)7
add l at position 13,C62Nn]r47S5OBl=2C)N7)2FOr/C))r)7
replace ) at position 17 with F,C62Nn]r47S5OBl=2CFN7)2FOr/C))r)7
add C at position 32,C62Nn]r47S5OBl=2CFN7)2FOr/C))r)7C
add 4 at position 18,C62Nn]r47S5OBl=2CF4N7)2FOr/C))r)7C
add 6 at position 21,C62Nn]r47S5OBl=2CF4N76)2FOr/C))r)7C
replace 4 at position 18 with s,C62Nn]r47S5OBl=2CFsN76)2FOr/C))r)7C
replace 6 at position 21 with o,C62Nn]r47S5OBl=2CFsN7o)2FOr/C))r)7C
remove ) from position 30,C62Nn]r47S5OBl=2CFsN7o)2FOr/C)r)7C
add = at position 6,C62Nn]=r47S5OBl=2CFsN7o)2FOr/C)r)7C
add + at position 29,C62Nn]=r47S5OBl=2CFsN7o)2FOr/+C)r)7C
add C at position 30,C62Nn]=r47S5OBl=2CFsN7o)2FOr/+CC)r)7C
remove r from position 33,C62Nn]=r47S5OBl=2CFsN7o)2FOr/+CC))7C
add ( at position 22,C62Nn]=r47S5OBl=2CFsN7(o)2FOr/+CC))7C
add o at position 23,C62Nn]=r47S5OBl=2CFsN7(oo)2FOr/+CC))7C
remove / from position 30,C62Nn]=r47S5OBl=2CFsN7(oo)2FOr+CC))7C
add ( at position 30,C62Nn]=r47S5OBl=2CFsN7(oo)2FOr(+CC))7C
add r at position 19,C62Nn]=r47S5OBl=2CFrsN7(oo)2FOr(+CC))7C
add ) at position 4,C62N)n]=r47S5OBl=2CFrsN7(oo)2FOr(+CC))7C
replace 7 at position 10 with =,C62N)n]=r4=S5OBl=2CFrsN7(oo)2FOr(+CC))7C
remove 2 from position 28,C62N)n]=r4=S5OBl=2CFrsN7(oo)FOr(+CC))7C
remove 7 from position 37,C62N)n]=r4=S5OBl=2CFrsN7(oo)FOr(+CC))C
add 4 at position 17,C62N)n]=r4=S5OBl=42CFrsN7(oo)FOr(+CC))C
add ] at position 7,C62N)n]]=r4=S5OBl=42CFrsN7(oo)FOr(+CC))C
add ) at position 36,C62N)n]]=r4=S5OBl=42CFrsN7(oo)FOr(+C)C))C
replace = at position 17 with H,C62N)n]]=r4=S5OBlH42CFrsN7(oo)FOr(+C)C))C
replace + at position 34 with ),C62N)n]]=r4=S5OBlH42CFrsN7(oo)FOr()C)C))C
add B at position 36,C62N)n]]=r4=S5OBlH42CFrsN7(oo)FOr()CB)C))C
add n at position 19,C62N)n]]=r4=S5OBlH4n2CFrsN7(oo)FOr()CB)C))C
replace H at position 17 with -,C62N)n]]=r4=S5OBl-4n2CFrsN7(oo)FOr()CB)C))C
replace s at position 24 with C,C62N)n]]=r4=S5OBl-4n2CFrCN7(oo)FOr()CB)C))C
add O at position 13,C62N)n]]=r4=SO5OBl-4n2CFrCN7(oo)FOr()CB)C))C
remove 7 from position 27,C62N)n]]=r4=SO5OBl-4n2CFrCN(oo)FOr()CB)C))C
remove r from position 9,C62N)n]]=4=SO5OBl-4n2CFrCN(oo)FOr()CB)C))C
remove N from position 3,C62)n]]=4=SO5OBl-4n2CFrCN(oo)FOr()CB)C))C
add r at position 10,C62)n]]=4=rSO5OBl-4n2CFrCN(oo)FOr()CB)C))C
remove ) from position 34,C62)n]]=4=rSO5OBl-4n2CFrCN(oo)FOr(CB)C))C
remove r from position 10,C62)n]]=4=SO5OBl-4n2CFrCN(oo)FOr(CB)C))C
add ( at position 8,C62)n]]=(4=SO5OBl-4n2CFrCN(oo)FOr(CB)C))C
remove B from position 35,C62)n]]=(4=SO5OBl-4n2CFrCN(oo)FOr(C)C))C
remove F from position 22,C62)n]]=(4=SO5OBl-4n2CrCN(oo)FOr(C)C))C
replace 5 at position 13 with 3,C62)n]]=(4=SO3OBl-4n2CrCN(oo)FOr(C)C))C
add - at position 11,C62)n]]=(4=-SO3OBl-4n2CrCN(oo)FOr(C)C))C
replace r at position 32 with C,C62)n]]=(4=-SO3OBl-4n2CrCN(oo)FOC(C)C))C
remove r from position 23,C62)n]]=(4=-SO3OBl-4n2CCN(oo)FOC(C)C))C
replace F at position 29 with /,C62)n]]=(4=-SO3OBl-4n2CCN(oo)/OC(C)C))C
remove S from position 12,C62)n]]=(4=-O3OBl-4n2CCN(oo)/OC(C)C))C
add O at position 12,C62)n]]=(4=-OO3OBl-4n2CCN(oo)/OC(C)C))C
add H at position 4,C62)Hn]]=(4=-OO3OBl-4n2CCN(oo)/OC(C)C))C
replace H at position 4 with n,C62)nn]]=(4=-OO3OBl-4n2CCN(oo)/OC(C)C))C
replace 4 at position 20 with ],C62)nn]]=(4=-OO3OBl-]n2CCN(oo)/OC(C)C))C
add S at position 5,C62)nSn]]=(4=-OO3OBl-]n2CCN(oo)/OC(C)C))C
replace 2 at position 2 with I,C6I)nSn]]=(4=-OO3OBl-]n2CCN(oo)/OC(C)C))C
add C at position 3,C6IC)nSn]]=(4=-OO3OBl-]n2CCN(oo)/OC(C)C))C
replace ) at position 4 with C,C6ICCnSn]]=(4=-OO3OBl-]n2CCN(oo)/OC(C)C))C
replace l at position 20 with -,C6ICCnSn]]=(4=-OO3OB--]n2CCN(oo)/OC(C)C))C
add 2 at position 17,C6ICCnSn]]=(4=-OO23OB--]n2CCN(oo)/OC(C)C))C
add B at position 10,C6ICCnSn]]B=(4=-OO23OB--]n2CCN(oo)/OC(C)C))C
remove S from position 6,C6ICCnn]]B=(4=-OO23OB--]n2CCN(oo)/OC(C)C))C
replace O at position 16 with ),C6ICCnn]]B=(4=-O)23OB--]n2CCN(oo)/OC(C)C))C
replace n at position 24 with ),C6ICCnn]]B=(4=-O)23OB--])2CCN(oo)/OC(C)C))C
replace n at position 6 with [,C6ICCn[]]B=(4=-O)23OB--])2CCN(oo)/OC(C)C))C
add C at position 7,C6ICCn[C]]B=(4=-O)23OB--])2CCN(oo)/OC(C)C))C
remove = from position 11,C6ICCn[C]]B(4=-O)23OB--])2CCN(oo)/OC(C)C))C
replace 3 at position 18 with O,C6ICCn[C]]B(4=-O)2OOB--])2CCN(oo)/OC(C)C))C
add - at position 33,C6ICCn[C]]B(4=-O)2OOB--])2CCN(oo)-/OC(C)C))C
replace B at position 10 with 1,C6ICCn[C]]1(4=-O)2OOB--])2CCN(oo)-/OC(C)C))C
replace o at position 30 with (,C6ICCn[C]]1(4=-O)2OOB--])2CCN((o)-/OC(C)C))C
replace ( at position 30 with O,C6ICCn[C]]1(4=-O)2OOB--])2CCN(Oo)-/OC(C)C))C
add ( at position 37,C6ICCn[C]]1(4=-O)2OOB--])2CCN(Oo)-/OC((C)C))C
add ] at position 22,C6ICCn[C]]1(4=-O)2OOB-]-])2CCN(Oo)-/OC((C)C))C
replace I at position 2 with C,C6CCCn[C]]1(4=-O)2OOB-]-])2CCN(Oo)-/OC((C)C))C
remove ] from position 9,C6CCCn[C]1(4=-O)2OOB-]-])2CCN(Oo)-/OC((C)C))C
replace 4 at position 11 with (,C6CCCn[C]1((=-O)2OOB-]-])2CCN(Oo)-/OC((C)C))C
remove O from position 17,C6CCCn[C]1((=-O)2OB-]-])2CCN(Oo)-/OC((C)C))C
add C at position 29,C6CCCn[C]1((=-O)2OB-]-])2CCN(COo)-/OC((C)C))C
remove - from position 33,C6CCCn[C]1((=-O)2OB-]-])2CCN(COo)/OC((C)C))C
remove ] from position 22,C6CCCn[C]1((=-O)2OB-]-)2CCN(COo)/OC((C)C))C
remove - from position 13,C6CCCn[C]1((=O)2OB-]-)2CCN(COo)/OC((C)C))C
remove / from position 31,C6CCCn[C]1((=O)2OB-]-)2CCN(COo)OC((C)C))C
add S at position 3,C6CSCCn[C]1((=O)2OB-]-)2CCN(COo)OC((C)C))C
add 1 at position 42,C6CSCCn[C]1((=O)2OB-]-)2CCN(COo)OC((C)C))C1
remove - from position 21,C6CSCCn[C]1((=O)2OB-])2CCN(COo)OC((C)C))C1
add C at position 32,C6CSCCn[C]1((=O)2OB-])2CCN(COo)OCC((C)C))C1
add ( at position 33,C6CSCCn[C]1((=O)2OB-])2CCN(COo)OC(C((C)C))C1
add C at position 12,C6CSCCn[C]1(C(=O)2OB-])2CCN(COo)OC(C((C)C))C1
replace 2 at position 17 with [,C6CSCCn[C]1(C(=O)[OB-])2CCN(COo)OC(C((C)C))C1
remove B from position 19,C6CSCCn[C]1(C(=O)[O-])2CCN(COo)OC(C((C)C))C1
add ) at position 35,C6CSCCn[C]1(C(=O)[O-])2CCN(COo)OC(C)((C)C))C1
add / at position 2,C6/CSCCn[C]1(C(=O)[O-])2CCN(COo)OC(C)((C)C))C1
remove ) from position 43,C6/CSCCn[C]1(C(=O)[O-])2CCN(COo)OC(C)((C)C)C1
add - at position 29,C6/CSCCn[C]1(C(=O)[O-])2CCN(C-Oo)OC(C)((C)C)C1
replace S at position 4 with =,C6/C=CCn[C]1(C(=O)[O-])2CCN(C-Oo)OC(C)((C)C)C1
remove o from position 31,C6/C=CCn[C]1(C(=O)[O-])2CCN(C-O)OC(C)((C)C)C1
remove n from position 7,C6/C=CC[C]1(C(=O)[O-])2CCN(C-O)OC(C)((C)C)C1
add @ at position 9,C6/C=CC[C@]1(C(=O)[O-])2CCN(C-O)OC(C)((C)C)C1
add = at position 30,C6/C=CC[C@]1(C(=O)[O-])2CCN(C-=O)OC(C)((C)C)C1
remove 6 from position 1,C/C=CC[C@]1(C(=O)[O-])2CCN(C-=O)OC(C)((C)C)C1
remove 2 from position 22,C/C=CC[C@]1(C(=O)[O-])CCN(C-=O)OC(C)((C)C)C1
add / at position 5,C/C=C/C[C@]1(C(=O)[O-])CCN(C-=O)OC(C)((C)C)C1
remove ( from position 37,C/C=C/C[C@]1(C(=O)[O-])CCN(C-=O)OC(C)(C)C)C1
replace - at position 28 with (,C/C=C/C[C@]1(C(=O)[O-])CCN(C(=O)OC(C)(C)C)C1
final: C/C=C/C[C@]1(C(=O)[O-])CCN(C(=O)OC(C)(C)C)C1,C/C=C/C[C@]1(C(=O)[O-])CCN(C(=O)OC(C)(C)C)C1
