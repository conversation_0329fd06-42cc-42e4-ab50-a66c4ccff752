log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 3,oFc3BH-C
remove c from position 2,oF3BH-<PERSON>
remove B from position 3,oF3H-<PERSON>
replace - at position 4 with ],oF3H]C
remove C from position 5,oF3H]
add B at position 4,oF3HB]
add N at position 6,oF3HB]N
replace B at position 4 with -,oF3H-]N
add ) at position 7,oF3H-]N)
add S at position 6,oF3H-]SN)
add [ at position 2,oF[3H-]SN)
add N at position 3,oF[N3H-]SN)
replace S at position 8 with ],oF[N3H-]]N)
add C at position 2,oFC[N3H-]]N)
add 4 at position 2,oF4C[N3H-]]N)
replace [ at position 4 with H,oF4CHN3H-]]N)
add ] at position 5,oF4CH]N3H-]]N)
add O at position 2,oFO4CH]N3H-]]N)
remove H from position 9,oFO4CH]N3-]]N)
add 6 at position 13,oFO4CH]N3-]]N6)
replace F at position 1 with [,o[O4CH]N3-]]N6)
remove H from position 5,o[O4C]N3-]]N6)
replace 4 at position 3 with r,o[OrC]N3-]]N6)
add S at position 6,o[OrC]SN3-]]N6)
add s at position 11,o[OrC]SN3-]s]N6)
remove 6 from position 14,o[OrC]SN3-]s]N)
replace ] at position 10 with H,o[OrC]SN3-Hs]N)
remove O from position 2,o[rC]SN3-Hs]N)
add C at position 11,o[rC]SN3-HsC]N)
replace N at position 13 with /,o[rC]SN3-HsC]/)
add S at position 6,o[rC]SSN3-HsC]/)
add l at position 2,o[lrC]SSN3-HsC]/)
add F at position 11,o[lrC]SSN3-FHsC]/)
replace o at position 0 with S,S[lrC]SSN3-FHsC]/)
remove S from position 7,S[lrC]SN3-FHsC]/)
add H at position 10,S[lrC]SN3-HFHsC]/)
remove l from position 2,S[rC]SN3-HFHsC]/)
add r at position 1,Sr[rC]SN3-HFHsC]/)
add ) at position 12,Sr[rC]SN3-HF)HsC]/)
replace F at position 11 with 4,Sr[rC]SN3-H4)HsC]/)
add # at position 19,Sr[rC]SN3-H4)HsC]/)#
add 1 at position 2,Sr1[rC]SN3-H4)HsC]/)#
add C at position 1,SCr1[rC]SN3-H4)HsC]/)#
add C at position 21,SCr1[rC]SN3-H4)HsC]/)C#
remove r from position 2,SC1[rC]SN3-H4)HsC]/)C#
add B at position 14,SC1[rC]SN3-H4)BHsC]/)C#
add C at position 19,SC1[rC]SN3-H4)BHsC]C/)C#
add o at position 17,SC1[rC]SN3-H4)BHsoC]C/)C#
replace - at position 10 with 6,SC1[rC]SN36H4)BHsoC]C/)C#
add r at position 12,SC1[rC]SN36Hr4)BHsoC]C/)C#
replace C at position 24 with r,SC1[rC]SN36Hr4)BHsoC]C/)r#
replace / at position 22 with 1,SC1[rC]SN36Hr4)BHsoC]C1)r#
remove s from position 17,SC1[rC]SN36Hr4)BHoC]C1)r#
add + at position 19,SC1[rC]SN36Hr4)BHoC+]C1)r#
replace r at position 4 with @,SC1[@C]SN36Hr4)BHoC+]C1)r#
add / at position 21,SC1[@C]SN36Hr4)BHoC+]/C1)r#
remove B from position 15,SC1[@C]SN36Hr4)HoC+]/C1)r#
add 2 at position 18,SC1[@C]SN36Hr4)HoC2+]/C1)r#
replace S at position 0 with 7,7C1[@C]SN36Hr4)HoC2+]/C1)r#
add ) at position 20,7C1[@C]SN36Hr4)HoC2+)]/C1)r#
add l at position 11,7C1[@C]SN36lHr4)HoC2+)]/C1)r#
add ) at position 29,7C1[@C]SN36lHr4)HoC2+)]/C1)r#)
replace 3 at position 9 with =,7C1[@C]SN=6lHr4)HoC2+)]/C1)r#)
replace ) at position 29 with 7,7C1[@C]SN=6lHr4)HoC2+)]/C1)r#7
add = at position 13,7C1[@C]SN=6lH=r4)HoC2+)]/C1)r#7
add r at position 20,7C1[@C]SN=6lH=r4)HoCr2+)]/C1)r#7
remove 1 from position 2,7C[@C]SN=6lH=r4)HoCr2+)]/C1)r#7
remove 7 from position 0,C[@C]SN=6lH=r4)HoCr2+)]/C1)r#7
replace 6 at position 8 with C,C[@C]SN=ClH=r4)HoCr2+)]/C1)r#7
add + at position 3,C[@+C]SN=ClH=r4)HoCr2+)]/C1)r#7
remove H from position 16,C[@+C]SN=ClH=r4)oCr2+)]/C1)r#7
replace 4 at position 14 with +,C[@+C]SN=ClH=r+)oCr2+)]/C1)r#7
add C at position 10,C[@+C]SN=CClH=r+)oCr2+)]/C1)r#7
add 4 at position 13,C[@+C]SN=CClH4=r+)oCr2+)]/C1)r#7
replace ) at position 17 with @,C[@+C]SN=CClH4=r+@oCr2+)]/C1)r#7
add ( at position 32,C[@+C]SN=CClH4=r+@oCr2+)]/C1)r#7(
add 4 at position 18,C[@+C]SN=CClH4=r+@4oCr2+)]/C1)r#7(
add 6 at position 21,C[@+C]SN=CClH4=r+@4oC6r2+)]/C1)r#7(
replace 4 at position 18 with r,C[@+C]SN=CClH4=r+@roC6r2+)]/C1)r#7(
replace 6 at position 21 with N,C[@+C]SN=CClH4=r+@roCNr2+)]/C1)r#7(
remove ) from position 30,C[@+C]SN=CClH4=r+@roCNr2+)]/C1r#7(
add 3 at position 6,C[@+C]3SN=CClH4=r+@roCNr2+)]/C1r#7(
add ) at position 29,C[@+C]3SN=CClH4=r+@roCNr2+)]/)C1r#7(
add 1 at position 30,C[@+C]3SN=CClH4=r+@roCNr2+)]/)1C1r#7(
remove r from position 33,C[@+C]3SN=CClH4=r+@roCNr2+)]/)1C1#7(
add ) at position 22,C[@+C]3SN=CClH4=r+@roC)Nr2+)]/)1C1#7(
add F at position 23,C[@+C]3SN=CClH4=r+@roC)FNr2+)]/)1C1#7(
remove / from position 30,C[@+C]3SN=CClH4=r+@roC)FNr2+)])1C1#7(
add B at position 30,C[@+C]3SN=CClH4=r+@roC)FNr2+)]B)1C1#7(
add r at position 19,C[@+C]3SN=CClH4=r+@rroC)FNr2+)]B)1C1#7(
add n at position 4,C[@+nC]3SN=CClH4=r+@rroC)FNr2+)]B)1C1#7(
replace = at position 10 with 5,C[@+nC]3SN5CClH4=r+@rroC)FNr2+)]B)1C1#7(
remove 2 from position 28,C[@+nC]3SN5CClH4=r+@rroC)FNr+)]B)1C1#7(
remove 7 from position 37,C[@+nC]3SN5CClH4=r+@rroC)FNr+)]B)1C1#(
add + at position 17,C[@+nC]3SN5CClH4=+r+@rroC)FNr+)]B)1C1#(
add = at position 7,C[@+nC]=3SN5CClH4=+r+@rroC)FNr+)]B)1C1#(
add C at position 36,C[@+nC]=3SN5CClH4=+r+@rroC)FNr+)]B)1CC1#(
replace = at position 17 with F,C[@+nC]=3SN5CClH4F+r+@rroC)FNr+)]B)1CC1#(
replace ) at position 34 with B,C[@+nC]=3SN5CClH4F+r+@rroC)FNr+)]BB1CC1#(
add C at position 36,C[@+nC]=3SN5CClH4F+r+@rroC)FNr+)]BB1CCC1#(
add ] at position 19,C[@+nC]=3SN5CClH4F+]r+@rroC)FNr+)]BB1CCC1#(
replace F at position 17 with n,C[@+nC]=3SN5CClH4n+]r+@rroC)FNr+)]BB1CCC1#(
remove r from position 24,C[@+nC]=3SN5CClH4n+]r+@roC)FNr+)]BB1CCC1#(
add F at position 20,C[@+nC]=3SN5CClH4n+]Fr+@roC)FNr+)]BB1CCC1#(
replace @ at position 2 with 2,C[2+nC]=3SN5CClH4n+]Fr+@roC)FNr+)]BB1CCC1#(
add ) at position 9,C[2+nC]=3)SN5CClH4n+]Fr+@roC)FNr+)]BB1CCC1#(
remove ( from position 43,C[2+nC]=3)SN5CClH4n+]Fr+@roC)FNr+)]BB1CCC1#
add r at position 10,C[2+nC]=3)rSN5CClH4n+]Fr+@roC)FNr+)]BB1CCC1#
remove ) from position 34,C[2+nC]=3)rSN5CClH4n+]Fr+@roC)FNr+]BB1CCC1#
remove r from position 10,C[2+nC]=3)SN5CClH4n+]Fr+@roC)FNr+]BB1CCC1#
add S at position 8,C[2+nC]=S3)SN5CClH4n+]Fr+@roC)FNr+]BB1CCC1#
remove B from position 35,C[2+nC]=S3)SN5CClH4n+]Fr+@roC)FNr+]B1CCC1#
remove F from position 22,C[2+nC]=S3)SN5CClH4n+]r+@roC)FNr+]B1CCC1#
replace 5 at position 13 with 3,C[2+nC]=S3)SN3CClH4n+]r+@roC)FNr+]B1CCC1#
add C at position 11,C[2+nC]=S3)CSN3CClH4n+]r+@roC)FNr+]B1CCC1#
replace r at position 32 with H,C[2+nC]=S3)CSN3CClH4n+]r+@roC)FNH+]B1CCC1#
remove r from position 23,C[2+nC]=S3)CSN3CClH4n+]+@roC)FNH+]B1CCC1#
replace F at position 29 with C,C[2+nC]=S3)CSN3CClH4n+]+@roC)CNH+]B1CCC1#
remove S from position 12,C[2+nC]=S3)CN3CClH4n+]+@roC)CNH+]B1CCC1#
add @ at position 12,C[2+nC]=S3)C@N3CClH4n+]+@roC)CNH+]B1CCC1#
add I at position 4,C[2+InC]=S3)C@N3CClH4n+]+@roC)CNH+]B1CCC1#
replace I at position 4 with (,C[2+(nC]=S3)C@N3CClH4n+]+@roC)CNH+]B1CCC1#
replace 4 at position 20 with ],C[2+(nC]=S3)C@N3CClH]n+]+@roC)CNH+]B1CCC1#
add S at position 5,C[2+(SnC]=S3)C@N3CClH]n+]+@roC)CNH+]B1CCC1#
replace 2 at position 2 with I,C[I+(SnC]=S3)C@N3CClH]n+]+@roC)CNH+]B1CCC1#
add O at position 3,C[IO+(SnC]=S3)C@N3CClH]n+]+@roC)CNH+]B1CCC1#
replace + at position 4 with #,C[IO#(SnC]=S3)C@N3CClH]n+]+@roC)CNH+]B1CCC1#
replace l at position 20 with [,C[IO#(SnC]=S3)C@N3CC[H]n+]+@roC)CNH+]B1CCC1#
add 1 at position 17,C[IO#(SnC]=S3)C@N13CC[H]n+]+@roC)CNH+]B1CCC1#
add B at position 10,C[IO#(SnC]B=S3)C@N13CC[H]n+]+@roC)CNH+]B1CCC1#
remove S from position 6,C[IO#(nC]B=S3)C@N13CC[H]n+]+@roC)CNH+]B1CCC1#
replace N at position 16 with ],C[IO#(nC]B=S3)C@]13CC[H]n+]+@roC)CNH+]B1CCC1#
replace n at position 24 with 2,C[IO#(nC]B=S3)C@]13CC[H]2+]+@roC)CNH+]B1CCC1#
replace n at position 6 with C,C[IO#(CC]B=S3)C@]13CC[H]2+]+@roC)CNH+]B1CCC1#
add N at position 7,C[IO#(CNC]B=S3)C@]13CC[H]2+]+@roC)CNH+]B1CCC1#
remove = from position 11,C[IO#(CNC]BS3)C@]13CC[H]2+]+@roC)CNH+]B1CCC1#
replace 3 at position 18 with O,C[IO#(CNC]BS3)C@]1OCC[H]2+]+@roC)CNH+]B1CCC1#
add - at position 33,C[IO#(CNC]BS3)C@]1OCC[H]2+]+@roC)-CNH+]B1CCC1#
replace B at position 10 with (,C[IO#(CNC](S3)C@]1OCC[H]2+]+@roC)-CNH+]B1CCC1#
replace o at position 30 with (,C[IO#(CNC](S3)C@]1OCC[H]2+]+@r(C)-CNH+]B1CCC1#
replace ( at position 30 with 1,C[IO#(CNC](S3)C@]1OCC[H]2+]+@r1C)-CNH+]B1CCC1#
add 1 at position 37,C[IO#(CNC](S3)C@]1OCC[H]2+]+@r1C)-CNH1+]B1CCC1#
add N at position 22,C[IO#(CNC](S3)C@]1OCC[NH]2+]+@r1C)-CNH1+]B1CCC1#
replace I at position 2 with C,C[CO#(CNC](S3)C@]1OCC[NH]2+]+@r1C)-CNH1+]B1CCC1#
remove ] from position 9,C[CO#(CNC(S3)C@]1OCC[NH]2+]+@r1C)-CNH1+]B1CCC1#
replace 3 at position 11 with O,C[CO#(CNC(SO)C@]1OCC[NH]2+]+@r1C)-CNH1+]B1CCC1#
remove O from position 17,C[CO#(CNC(SO)C@]1CC[NH]2+]+@r1C)-CNH1+]B1CCC1#
add ] at position 29,C[CO#(CNC(SO)C@]1CC[NH]2+]+@r]1C)-CNH1+]B1CCC1#
remove - from position 33,C[CO#(CNC(SO)C@]1CC[NH]2+]+@r]1C)CNH1+]B1CCC1#
remove ] from position 22,C[CO#(CNC(SO)C@]1CC[NH2+]+@r]1C)CNH1+]B1CCC1#
remove 1 from position 35,C[CO#(CNC(SO)C@]1CC[NH2+]+@r]1C)CNH+]B1CCC1#
remove B from position 37,C[CO#(CNC(SO)C@]1CC[NH2+]+@r]1C)CNH+]1CCC1#
add = at position 10,C[CO#(CNC(=SO)C@]1CC[NH2+]+@r]1C)CNH+]1CCC1#
add r at position 30,C[CO#(CNC(=SO)C@]1CC[NH2+]+@r]r1C)CNH+]1CCC1#
replace # at position 4 with H,C[COH(CNC(=SO)C@]1CC[NH2+]+@r]r1C)CNH+]1CCC1#
add C at position 1,CC[COH(CNC(=SO)C@]1CC[NH2+]+@r]r1C)CNH+]1CCC1#
replace r at position 29 with @,CC[COH(CNC(=SO)C@]1CC[NH2+]+@@]r1C)CNH+]1CCC1#
add C at position 41,CC[COH(CNC(=SO)C@]1CC[NH2+]+@@]r1C)CNH+]1CCCC1#
add 2 at position 22,CC[COH(CNC(=SO)C@]1CC[2NH2+]+@@]r1C)CNH+]1CCCC1#
replace O at position 4 with @,CC[C@H(CNC(=SO)C@]1CC[2NH2+]+@@]r1C)CNH+]1CCCC1#
add H at position 17,CC[C@H(CNC(=SO)C@H]1CC[2NH2+]+@@]r1C)CNH+]1CCCC1#
add H at position 32,CC[C@H(CNC(=SO)C@H]1CC[2NH2+]+@@H]r1C)CNH+]1CCCC1#
add ( at position 38,CC[C@H(CNC(=SO)C@H]1CC[2NH2+]+@@H]r1C)(CNH+]1CCCC1#
remove r from position 34,CC[C@H(CNC(=SO)C@H]1CC[2NH2+]+@@H]1C)(CNH+]1CCCC1#
add [ at position 15,CC[C@H(CNC(=SO)[C@H]1CC[2NH2+]+@@H]1C)(CNH+]1CCCC1#
remove C from position 0,C[C@H(CNC(=SO)[C@H]1CC[2NH2+]+@@H]1C)(CNH+]1CCCC1#
remove S from position 11,C[C@H(CNC(=O)[C@H]1CC[2NH2+]+@@H]1C)(CNH+]1CCCC1#
add C at position 29,C[C@H(CNC(=O)[C@H]1CC[2NH2+]+C@@H]1C)(CNH+]1CCCC1#
add [ at position 39,C[C@H(CNC(=O)[C@H]1CC[2NH2+]+C@@H]1C)(C[NH+]1CCCC1#
remove # from position 50,C[C@H(CNC(=O)[C@H]1CC[2NH2+]+C@@H]1C)(C[NH+]1CCCC1
remove 2 from position 22,C[C@H(CNC(=O)[C@H]1CC[NH2+]+C@@H]1C)(C[NH+]1CCCC1
add ] at position 5,C[C@H](CNC(=O)[C@H]1CC[NH2+]+C@@H]1C)(C[NH+]1CCCC1
remove ( from position 37,C[C@H](CNC(=O)[C@H]1CC[NH2+]+C@@H]1C)C[NH+]1CCCC1
replace + at position 28 with [,C[C@H](CNC(=O)[C@H]1CC[NH2+][C@@H]1C)C[NH+]1CCCC1
final: C[C@H](CNC(=O)[C@H]1CC[NH2+][C@@H]1C)C[NH+]1CCCC1,C[C@H](CNC(=O)[C@H]1CC[NH2+][C@@H]1C)C[NH+]1CCCC1
