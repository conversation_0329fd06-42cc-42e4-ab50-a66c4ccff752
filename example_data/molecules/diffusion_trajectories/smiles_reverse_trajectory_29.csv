log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 4,oHc4BH-C
remove c from position 2,oH4BH-<PERSON>
remove B from position 3,oH4H-<PERSON>
replace - at position 4 with ],oH4H]C
remove <PERSON> from position 5,oH4H]
add B at position 4,oH4HB]
add N at position 6,oH4HB]N
replace B at position 4 with -,oH4H-]N
add c at position 7,oH4H-]Nc
add [ at position 6,oH4H-][Nc
add [ at position 2,oH[4H-][Nc
add 4 at position 3,oH[44H-][Nc
replace [ at position 8 with F,oH[44H-]FNc
add l at position 2,oHl[44H-]FNc
add 4 at position 2,oH4l[44H-]FNc
replace [ at position 4 with H,oH4lH44H-]FNc
add N at position 5,oH4lHN44H-]FNc
add O at position 2,oHO4lHN44H-]FNc
remove H from position 9,oHO4lHN44-]FNc
add 6 at position 13,oHO4lHN44-]FN6c
replace H at position 1 with 2,o2O4lHN44-]FN6c
remove H from position 5,o2O4lN44-]FN6c
replace 4 at position 3 with r,o2OrlN44-]FN6c
add r at position 6,o2OrlNr44-]FN6c
add s at position 11,o2OrlNr44-]sFN6c
remove 6 from position 14,o2OrlNr44-]sFNc
replace ] at position 10 with H,o2OrlNr44-HsFNc
remove O from position 2,o2rlNr44-HsFNc
add F at position 11,o2rlNr44-HsFFNc
replace N at position 13 with 1,o2rlNr44-HsFF1c
add S at position 6,o2rlNrS44-HsFF1c
add l at position 2,o2lrlNrS44-HsFF1c
add F at position 11,o2lrlNrS44-FHsFF1c
replace o at position 0 with S,S2lrlNrS44-FHsFF1c
remove S from position 7,S2lrlNr44-FHsFF1c
add B at position 10,S2lrlNr44-BFHsFF1c
remove l from position 2,S2rlNr44-BFHsFF1c
add r at position 1,Sr2rlNr44-BFHsFF1c
add O at position 12,Sr2rlNr44-BFOHsFF1c
replace F at position 11 with 3,Sr2rlNr44-B3OHsFF1c
add r at position 19,Sr2rlNr44-B3OHsFF1cr
add 1 at position 2,Sr12rlNr44-B3OHsFF1cr
add 6 at position 1,S6r12rlNr44-B3OHsFF1cr
add F at position 21,S6r12rlNr44-B3OHsFF1cFr
remove r from position 2,S612rlNr44-B3OHsFF1cFr
add B at position 14,S612rlNr44-B3OBHsFF1cFr
add / at position 19,S612rlNr44-B3OBHsFF/1cFr
add 5 at position 17,S612rlNr44-B3OBHs5FF/1cFr
replace - at position 10 with 6,S612rlNr446B3OBHs5FF/1cFr
add l at position 12,S612rlNr446Bl3OBHs5FF/1cFr
replace F at position 24 with ),S612rlNr446Bl3OBHs5FF/1c)r
replace 1 at position 22 with +,S612rlNr446Bl3OBHs5FF/+c)r
remove s from position 17,S612rlNr446Bl3OBH5FF/+c)r
add o at position 19,S612rlNr446Bl3OBH5FoF/+c)r
replace r at position 4 with N,S612NlNr446Bl3OBH5FoF/+c)r
add C at position 21,S612NlNr446Bl3OBH5FoFC/+c)r
remove B from position 15,S612NlNr446Bl3OH5FoFC/+c)r
add ( at position 18,S612NlNr446Bl3OH5F(oFC/+c)r
replace S at position 0 with 7,7612NlNr446Bl3OH5F(oFC/+c)r
add 2 at position 20,7612NlNr446Bl3OH5F(o2FC/+c)r
add 5 at position 11,7612NlNr4465Bl3OH5F(o2FC/+c)r
add ) at position 29,7612NlNr4465Bl3OH5F(o2FC/+c)r)
replace 4 at position 9 with ),7612NlNr4)65Bl3OH5F(o2FC/+c)r)
replace ) at position 29 with 1,7612NlNr4)65Bl3OH5F(o2FC/+c)r1
add c at position 13,7612NlNr4)65Bcl3OH5F(o2FC/+c)r1
add s at position 20,7612NlNr4)65Bcl3OH5Fs(o2FC/+c)r1
remove 1 from position 2,762NlNr4)65Bcl3OH5Fs(o2FC/+c)r1
remove 7 from position 0,62NlNr4)65Bcl3OH5Fs(o2FC/+c)r1
replace 6 at position 8 with S,62NlNr4)S5Bcl3OH5Fs(o2FC/+c)r1
add + at position 3,62N+lNr4)S5Bcl3OH5Fs(o2FC/+c)r1
remove H from position 16,62N+lNr4)S5Bcl3O5Fs(o2FC/+c)r1
replace 3 at position 14 with =,62N+lNr4)S5Bcl=O5Fs(o2FC/+c)r1
remove 5 from position 16,62N+lNr4)S5Bcl=OFs(o2FC/+c)r1
add 4 at position 19,62N+lNr4)S5Bcl=OFs(4o2FC/+c)r1
replace ) at position 8 with =,62N+lNr4=S5Bcl=OFs(4o2FC/+c)r1
add C at position 16,62N+lNr4=S5Bcl=OCFs(4o2FC/+c)r1
add 4 at position 18,62N+lNr4=S5Bcl=OCF4s(4o2FC/+c)r1
add 7 at position 21,62N+lNr4=S5Bcl=OCF4s(74o2FC/+c)r1
replace 4 at position 18 with r,62N+lNr4=S5Bcl=OCFrs(74o2FC/+c)r1
replace 7 at position 21 with (,62N+lNr4=S5Bcl=OCFrs((4o2FC/+c)r1
remove ) from position 30,62N+lNr4=S5Bcl=OCFrs((4o2FC/+cr1
add = at position 6,62N+lN=r4=S5Bcl=OCFrs((4o2FC/+cr1
add / at position 29,62N+lN=r4=S5Bcl=OCFrs((4o2FC//+cr1
add c at position 30,62N+lN=r4=S5Bcl=OCFrs((4o2FC//c+cr1
remove r from position 33,62N+lN=r4=S5Bcl=OCFrs((4o2FC//c+c1
add 7 at position 22,62N+lN=r4=S5Bcl=OCFrs(7(4o2FC//c+c1
add ) at position 23,62N+lN=r4=S5Bcl=OCFrs(7)(4o2FC//c+c1
remove / from position 30,62N+lN=r4=S5Bcl=OCFrs(7)(4o2FC/c+c1
add r at position 30,62N+lN=r4=S5Bcl=OCFrs(7)(4o2FCr/c+c1
remove 4 from position 25,62N+lN=r4=S5Bcl=OCFrs(7)(o2FCr/c+c1
add C at position 0,C62N+lN=r4=S5Bcl=OCFrs(7)(o2FCr/c+c1
remove / from position 31,C62N+lN=r4=S5Bcl=OCFrs(7)(o2FCrc+c1
add O at position 28,C62N+lN=r4=S5Bcl=OCFrs(7)(o2OFCrc+c1
remove ( from position 25,C62N+lN=r4=S5Bcl=OCFrs(7)o2OFCrc+c1
add 4 at position 17,C62N+lN=r4=S5Bcl=4OCFrs(7)o2OFCrc+c1
add ] at position 7,C62N+lN]=r4=S5Bcl=4OCFrs(7)o2OFCrc+c1
add ) at position 36,C62N+lN]=r4=S5Bcl=4OCFrs(7)o2OFCrc+c)1
replace = at position 17 with F,C62N+lN]=r4=S5BclF4OCFrs(7)o2OFCrc+c)1
replace + at position 34 with ),C62N+lN]=r4=S5BclF4OCFrs(7)o2OFCrc)c)1
add B at position 36,C62N+lN]=r4=S5BclF4OCFrs(7)o2OFCrc)cB)1
add n at position 19,C62N+lN]=r4=S5BclF4nOCFrs(7)o2OFCrc)cB)1
replace F at position 17 with c,C62N+lN]=r4=S5Bclc4nOCFrs(7)o2OFCrc)cB)1
replace s at position 24 with C,C62N+lN]=r4=S5Bclc4nOCFrC(7)o2OFCrc)cB)1
add O at position 13,C62N+lN]=r4=SO5Bclc4nOCFrC(7)o2OFCrc)cB)1
remove 7 from position 27,C62N+lN]=r4=SO5Bclc4nOCFrC()o2OFCrc)cB)1
remove r from position 9,C62N+lN]=4=SO5Bclc4nOCFrC()o2OFCrc)cB)1
remove N from position 3,C62+lN]=4=SO5Bclc4nOCFrC()o2OFCrc)cB)1
add r at position 10,C62+lN]=4=rSO5Bclc4nOCFrC()o2OFCrc)cB)1
remove ) from position 34,C62+lN]=4=rSO5Bclc4nOCFrC()o2OFCrccB)1
remove r from position 10,C62+lN]=4=SO5Bclc4nOCFrC()o2OFCrccB)1
add O at position 8,C62+lN]=O4=SO5Bclc4nOCFrC()o2OFCrccB)1
remove B from position 35,C62+lN]=O4=SO5Bclc4nOCFrC()o2OFCrcc)1
remove F from position 22,C62+lN]=O4=SO5Bclc4nOCrC()o2OFCrcc)1
replace 5 at position 13 with 3,C62+lN]=O4=SO3Bclc4nOCrC()o2OFCrcc)1
add O at position 11,C62+lN]=O4=OSO3Bclc4nOCrC()o2OFCrcc)1
replace r at position 32 with 2,C62+lN]=O4=OSO3Bclc4nOCrC()o2OFC2cc)1
remove r from position 23,C62+lN]=O4=OSO3Bclc4nOCC()o2OFC2cc)1
replace F at position 29 with (,C62+lN]=O4=OSO3Bclc4nOCC()o2O(C2cc)1
remove S from position 12,C62+lN]=O4=OO3Bclc4nOCC()o2O(C2cc)1
add ) at position 12,C62+lN]=O4=O)O3Bclc4nOCC()o2O(C2cc)1
add I at position 4,C62+IlN]=O4=O)O3Bclc4nOCC()o2O(C2cc)1
replace I at position 4 with C,C62+ClN]=O4=O)O3Bclc4nOCC()o2O(C2cc)1
replace 4 at position 20 with ],C62+ClN]=O4=O)O3Bclc]nOCC()o2O(C2cc)1
add S at position 5,C62+CSlN]=O4=O)O3Bclc]nOCC()o2O(C2cc)1
replace 2 at position 2 with I,C6I+CSlN]=O4=O)O3Bclc]nOCC()o2O(C2cc)1
add S at position 3,C6IS+CSlN]=O4=O)O3Bclc]nOCC()o2O(C2cc)1
replace + at position 4 with ),C6IS)CSlN]=O4=O)O3Bclc]nOCC()o2O(C2cc)1
replace l at position 20 with c,C6IS)CSlN]=O4=O)O3Bccc]nOCC()o2O(C2cc)1
add 1 at position 17,C6IS)CSlN]=O4=O)O13Bccc]nOCC()o2O(C2cc)1
add B at position 10,C6IS)CSlN]B=O4=O)O13Bccc]nOCC()o2O(C2cc)1
remove S from position 6,C6IS)ClN]B=O4=O)O13Bccc]nOCC()o2O(C2cc)1
replace O at position 16 with 2,C6IS)ClN]B=O4=O)213Bccc]nOCC()o2O(C2cc)1
replace n at position 24 with 2,C6IS)ClN]B=O4=O)213Bccc]2OCC()o2O(C2cc)1
replace l at position 6 with n,C6IS)CnN]B=O4=O)213Bccc]2OCC()o2O(C2cc)1
add ) at position 7,C6IS)Cn)N]B=O4=O)213Bccc]2OCC()o2O(C2cc)1
remove = from position 11,C6IS)Cn)N]BO4=O)213Bccc]2OCC()o2O(C2cc)1
replace 3 at position 18 with O,C6IS)Cn)N]BO4=O)21OBccc]2OCC()o2O(C2cc)1
add - at position 33,C6IS)Cn)N]BO4=O)21OBccc]2OCC()o2O-(C2cc)1
replace B at position 10 with =,C6IS)Cn)N]=O4=O)21OBccc]2OCC()o2O-(C2cc)1
replace o at position 30 with (,C6IS)Cn)N]=O4=O)21OBccc]2OCC()(2O-(C2cc)1
replace ( at position 30 with N,C6IS)Cn)N]=O4=O)21OBccc]2OCC()N2O-(C2cc)1
add ) at position 37,C6IS)Cn)N]=O4=O)21OBccc]2OCC()N2O-(C2)cc)1
add - at position 22,C6IS)Cn)N]=O4=O)21OBcc-c]2OCC()N2O-(C2)cc)1
replace I at position 2 with (,C6(S)Cn)N]=O4=O)21OBcc-c]2OCC()N2O-(C2)cc)1
remove ] from position 9,C6(S)Cn)N=O4=O)21OBcc-c]2OCC()N2O-(C2)cc)1
replace 4 at position 11 with (,C6(S)Cn)N=O(=O)21OBcc-c]2OCC()N2O-(C2)cc)1
remove O from position 17,C6(S)Cn)N=O(=O)21Bcc-c]2OCC()N2O-(C2)cc)1
add o at position 29,C6(S)Cn)N=O(=O)21Bcc-c]2OCC()oN2O-(C2)cc)1
remove - from position 33,C6(S)Cn)N=O(=O)21Bcc-c]2OCC()oN2O(C2)cc)1
remove ] from position 22,C6(S)Cn)N=O(=O)21Bcc-c2OCC()oN2O(C2)cc)1
add ( at position 9,C6(S)Cn)N(=O(=O)21Bcc-c2OCC()oN2O(C2)cc)1
add H at position 4,C6(SH)Cn)N(=O(=O)21Bcc-c2OCC()oN2O(C2)cc)1
remove H from position 4,C6(S)Cn)N(=O(=O)21Bcc-c2OCC()oN2O(C2)cc)1
add ( at position 24,C6(S)Cn)N(=O(=O)21Bcc-c2(OCC()oN2O(C2)cc)1
remove - from position 21,C6(S)Cn)N(=O(=O)21Bccc2(OCC()oN2O(C2)cc)1
add C at position 32,C6(S)Cn)N(=O(=O)21Bccc2(OCC()oN2CO(C2)cc)1
add C at position 33,C6(S)Cn)N(=O(=O)21Bccc2(OCC()oN2CCO(C2)cc)1
add ) at position 12,C6(S)Cn)N(=O)(=O)21Bccc2(OCC()oN2CCO(C2)cc)1
replace 2 at position 17 with c,C6(S)Cn)N(=O)(=O)c1Bccc2(OCC()oN2CCO(C2)cc)1
remove B from position 19,C6(S)Cn)N(=O)(=O)c1ccc2(OCC()oN2CCO(C2)cc)1
add C at position 35,C6(S)Cn)N(=O)(=O)c1ccc2(OCC()oN2CCOC(C2)cc)1
add C at position 2,C6C(S)Cn)N(=O)(=O)c1ccc2(OCC()oN2CCOC(C2)cc)1
remove ) from position 43,C6C(S)Cn)N(=O)(=O)c1ccc2(OCC()oN2CCOC(C2)cc1
add + at position 29,C6C(S)Cn)N(=O)(=O)c1ccc2(OCC(+)oN2CCOC(C2)cc1
replace S at position 4 with C,C6C(C)Cn)N(=O)(=O)c1ccc2(OCC(+)oN2CCOC(C2)cc1
remove o from position 31,C6C(C)Cn)N(=O)(=O)c1ccc2(OCC(+)N2CCOC(C2)cc1
remove n from position 7,C6C(C)C)N(=O)(=O)c1ccc2(OCC(+)N2CCOC(C2)cc1
add S at position 9,C6C(C)C)NS(=O)(=O)c1ccc2(OCC(+)N2CCOC(C2)cc1
add O at position 30,C6C(C)C)NS(=O)(=O)c1ccc2(OCC(+O)N2CCOC(C2)cc1
remove 6 from position 1,CC(C)C)NS(=O)(=O)c1ccc2(OCC(+O)N2CCOC(C2)cc1
remove 2 from position 22,CC(C)C)NS(=O)(=O)c1ccc(OCC(+O)N2CCOC(C2)cc1
add ( at position 5,CC(C)(C)NS(=O)(=O)c1ccc(OCC(+O)N2CCOC(C2)cc1
remove ( from position 37,CC(C)(C)NS(=O)(=O)c1ccc(OCC(+O)N2CCOCC2)cc1
replace + at position 28 with =,CC(C)(C)NS(=O)(=O)c1ccc(OCC(=O)N2CCOCC2)cc1
final: CC(C)(C)NS(=O)(=O)c1ccc(OCC(=O)N2CCOCC2)cc1,CC(C)(C)NS(=O)(=O)c1ccc(OCC(=O)N2CCOCC2)cc1
