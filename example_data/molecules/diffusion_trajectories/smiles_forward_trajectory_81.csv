log,state
initialize: <PERSON>Nc1ncc(COCC2CCCCC2)s1,<PERSON><PERSON><PERSON><PERSON>nc<PERSON>(COCC2CCCCC2)s1
replace C at position 14 with +,CCNc1ncc(COCC2+CCCC2)s1
add ( at position 18,CCNc1ncc(COCC2+CCC(C2)s1
remove N from position 2,CCc1ncc(COCC2+CCC(C2)s1
add 2 at position 11,CCc1ncc(COC2C2+CCC(C2)s1
add 6 at position 0,6CCc1ncc(COC2C2+CCC(C2)s1
remove + from position 15,6CCc1ncc(COC2C2CCC(C2)s1
remove 1 from position 4,6CCcncc(COC2C2CCC(C2)s1
add + at position 22,6CCcncc(COC2C2CCC(C2)s+1
replace C at position 16 with B,6CCcncc(COC2C2CCB(C2)s+1
replace ( at position 7 with +,6CCcncc+COC2C2CCB(C2)s+1
remove C from position 14,6CCcncc+COC2C2CB(C2)s+1
add ) at position 21,6CCcncc+COC2C2CB(C2)s)+1
remove C from position 1,6Ccncc+COC2C2CB(C2)s)+1
remove 2 from position 17,6Ccncc+COC2C2CB(C)s)+1
add B at position 9,6Ccncc+COBC2C2CB(C)s)+1
replace O at position 8 with 2,6Ccncc+C2BC2C2CB(C)s)+1
remove + from position 6,6CcnccC2BC2C2CB(C)s)+1
remove C from position 16,6CcnccC2BC2C2CB()s)+1
remove ) from position 16,6CcnccC2BC2C2CB(s)+1
add - at position 10,6CcnccC2BC-2C2CB(s)+1
remove C from position 12,6CcnccC2BC-22CB(s)+1
add H at position 2,6CHcnccC2BC-22CB(s)+1
remove H from position 2,6CcnccC2BC-22CB(s)+1
remove c from position 4,6CcncC2BC-22CB(s)+1
add ] at position 11,6CcncC2BC-2]2CB(s)+1
add - at position 16,6CcncC2BC-2]2CB(-s)+1
remove B from position 14,6CcncC2BC-2]2C(-s)+1
add O at position 8,6CcncC2BOC-2]2C(-s)+1
replace C at position 5 with 3,6Ccnc32BOC-2]2C(-s)+1
add ] at position 4,6Ccn]c32BOC-2]2C(-s)+1
replace C at position 1 with I,6Icn]c32BOC-2]2C(-s)+1
remove - from position 11,6Icn]c32BOC2]2C(-s)+1
remove ) from position 18,6Icn]c32BOC2]2C(-s+1
replace ( at position 15 with ),6Icn]c32BOC2]2C)-s+1
replace ) at position 15 with o,6Icn]c32BOC2]2Co-s+1
replace c at position 5 with @,6Icn]@32BOC2]2Co-s+1
remove - from position 16,6Icn]@32BOC2]2Cos+1
replace O at position 9 with 3,6Icn]@32B3C2]2Cos+1
add = at position 5,6Icn]=@32B3C2]2Cos+1
remove n from position 3,6Ic]=@32B3C2]2Cos+1
replace ] at position 3 with n,6Icn=@32B3C2]2Cos+1
replace ] at position 12 with n,6Icn=@32B3C2n2Cos+1
replace B at position 8 with O,6Icn=@32O3C2n2Cos+1
add S at position 3,6IcSn=@32O3C2n2Cos+1
remove = from position 5,6IcSn@32O3C2n2Cos+1
remove O from position 8,6IcSn@323C2n2Cos+1
replace 2 at position 10 with l,6IcSn@323Cln2Cos+1
replace c at position 2 with ),6I)Sn@323Cln2Cos+1
remove I from position 1,6)Sn@323Cln2Cos+1
replace ) at position 1 with 3,63Sn@323Cln2Cos+1
remove S from position 2,63n@323Cln2Cos+1
replace 2 at position 10 with 5,63n@323Cln5Cos+1
replace n at position 2 with H,63H@323Cln5Cos+1
remove H from position 2,63@323Cln5Cos+1
remove 3 from position 3,63@23Cln5Cos+1
add S at position 3,63@S23Cln5Cos+1
replace l at position 7 with C,63@S23CCn5Cos+1
add r at position 11,63@S23CCn5Cros+1
replace s at position 13 with /,63@S23CCn5Cro/+1
replace C at position 6 with 4,63@S234Cn5Cro/+1
add F at position 11,63@S234Cn5CFro/+1
add B at position 17,63@S234Cn5CFro/+1B
remove 2 from position 4,63@S34Cn5CFro/+1B
add r at position 5,63@S3r4Cn5CFro/+1B
add ) at position 17,63@S3r4Cn5CFro/+1)B
remove r from position 5,63@S34Cn5CFro/+1)B
add N at position 1,6N3@S34Cn5CFro/+1)B
add r at position 4,6N3@rS34Cn5CFro/+1)B
add 7 at position 13,6N3@rS34Cn5CF7ro/+1)B
remove 3 from position 6,6N3@rS4Cn5CF7ro/+1)B
replace 7 at position 12 with s,6N3@rS4Cn5CFsro/+1)B
replace n at position 8 with F,6N3@rS4CF5CFsro/+1)B
remove 5 from position 9,6N3@rS4CFCFsro/+1)B
remove B from position 18,6N3@rS4CFCFsro/+1)
replace ) at position 17 with +,6N3@rS4CFCFsro/+1+
replace F at position 8 with =,6N3@rS4C=CFsro/+1+
remove = from position 8,6N3@rS4CCFsro/+1+
remove o from position 12,6N3@rS4CCFsr/+1+
remove F from position 9,6N3@rS4CCsr/+1+
remove s from position 9,6N3@rS4CCr/+1+
remove S from position 5,6N3@r4CCr/+1+
replace C at position 7 with 2,6N3@r4C2r/+1+
replace 3 at position 2 with =,6N=@r4C2r/+1+
remove N from position 1,6=@r4C2r/+1+
remove 4 from position 4,6=@rC2r/+1+
remove + from position 10,6=@rC2r/+1
replace @ at position 2 with 7,6=7rC2r/+1
remove 7 from position 2,6=rC2r/+1
remove r from position 5,6=rC2/+1
remove / from position 5,6=rC2+1
add c at position 7,6=rC2+1c
remove 6 from position 0,=rC2+1c
replace 1 at position 5 with B,=rC2+Bc
remove = from position 0,rC2+Bc
add B at position 6,rC2+BcB
add o at position 2,rCo2+BcB
replace B at position 5 with 6,rCo2+6cB
replace + at position 4 with 5,rCo256cB
remove 6 from position 5,rCo25cB
remove o from position 2,rC25cB
remove c from position 4,rC25B
replace 2 at position 2 with ),rC)5B
remove B from position 4,rC)5
add 5 at position 4,rC)55
replace 5 at position 3 with 3,rC)35
add H at position 4,rC)3H5
remove r from position 0,C)3H5
replace 3 at position 2 with 7,C)7H5
add 7 at position 0,7C)7H5
add 1 at position 0,17C)7H5
remove C from position 2,17)7H5
remove 7 from position 3,17)H5
replace 1 at position 0 with o,o7)H5
replace ) at position 2 with 4,o74H5
remove 5 from position 4,o74H
add ) at position 4,o74H)
remove o from position 0,74H)
remove H from position 2,74)
replace 7 at position 0 with /,/4)
remove ) from position 2,/4
replace / at position 0 with r,r4
remove r from position 0,4
add S at position 0,S4
add ] at position 2,S4]
remove 4 from position 1,S]
replace ] at position 1 with -,S-
remove - from position 1,S
add r at position 0,rS
remove S from position 1,r
replace r at position 0 with 5,5
add 6 at position 0,65
replace 5 at position 1 with I,6I
remove 6 from position 0,I
add l at position 0,lI
remove I from position 1,l
add S at position 0,Sl
replace S at position 0 with o,ol
remove l from position 1,o
remove o from position 0,
final: ,
