log,state
initialize: ,
add o at position 0,o
add l at position 1,ol
replace o at position 0 with <PERSON>,Sl
remove S from position 0,l
add I at position 1,lI
remove l from position 0,I
add 6 at position 0,6I
replace I at position 1 with 5,65
remove 6 from position 0,5
replace 5 at position 0 with r,r
add S at position 1,rS
remove r from position 0,S
add - at position 1,S-
replace - at position 1 with ],S]
add 4 at position 1,S4]
remove ] from position 2,S4
remove S from position 0,4
add r at position 0,r4
replace r at position 0 with /,/4
add ) at position 2,/4)
replace / at position 0 with 7,74)
add H at position 2,74H)
add o at position 0,o74H)
remove ) from position 4,o74H
add 5 at position 4,o74H5
replace 4 at position 2 with ),o7)H5
replace o at position 0 with 1,17)H5
add 7 at position 3,17)7H5
add C at position 2,17C)7H5
remove 1 from position 0,7C)7H5
remove 7 from position 0,C)7H5
replace 7 at position 2 with 3,C)3H5
add r at position 0,rC)3H5
remove H from position 4,rC)35
replace 3 at position 3 with 5,rC)55
remove 5 from position 4,rC)5
add B at position 4,rC)5B
replace ) at position 2 with 2,rC25B
add c at position 4,rC25cB
add o at position 2,rCo25cB
add 6 at position 5,rCo256cB
replace 5 at position 4 with +,rCo2+6cB
replace 6 at position 5 with B,rCo2+BcB
remove o from position 2,rC2+BcB
remove B from position 6,rC2+Bc
add = at position 0,=rC2+Bc
replace B at position 5 with 1,=rC2+1c
add 6 at position 0,6=rC2+1c
remove c from position 7,6=rC2+1
add / at position 5,6=rC2/+1
add r at position 5,6=rC2r/+1
add 7 at position 2,6=7rC2r/+1
replace 7 at position 2 with @,6=@rC2r/+1
add + at position 10,6=@rC2r/+1+
add 4 at position 4,6=@r4C2r/+1+
add N at position 1,6N=@r4C2r/+1+
replace = at position 2 with 3,6N3@r4C2r/+1+
replace 2 at position 7 with C,6N3@r4CCr/+1+
add S at position 5,6N3@rS4CCr/+1+
add s at position 9,6N3@rS4CCsr/+1+
add F at position 9,6N3@rS4CCFsr/+1+
add o at position 12,6N3@rS4CCFsro/+1+
add = at position 8,6N3@rS4C=CFsro/+1+
replace = at position 8 with F,6N3@rS4CFCFsro/+1+
replace + at position 17 with ),6N3@rS4CFCFsro/+1)
add B at position 18,6N3@rS4CFCFsro/+1)B
add 5 at position 9,6N3@rS4CF5CFsro/+1)B
replace F at position 8 with n,6N3@rS4Cn5CFsro/+1)B
replace s at position 12 with 7,6N3@rS4Cn5CF7ro/+1)B
add 3 at position 6,6N3@rS34Cn5CF7ro/+1)B
remove 7 from position 13,6N3@rS34Cn5CFro/+1)B
remove r from position 4,6N3@S34Cn5CFro/+1)B
remove N from position 1,63@S34Cn5CFro/+1)B
add r at position 5,63@S3r4Cn5CFro/+1)B
remove ) from position 17,63@S3r4Cn5CFro/+1B
remove r from position 5,63@S34Cn5CFro/+1B
add 2 at position 4,63@S234Cn5CFro/+1B
remove B from position 17,63@S234Cn5CFro/+1
remove F from position 11,63@S234Cn5Cro/+1
replace 4 at position 6 with C,63@S23CCn5Cro/+1
replace / at position 13 with s,63@S23CCn5Cros+1
remove r from position 11,63@S23CCn5Cos+1
replace C at position 7 with l,63@S23Cln5Cos+1
remove S from position 3,63@23Cln5Cos+1
add 3 at position 3,63@323Cln5Cos+1
add H at position 2,63H@323Cln5Cos+1
replace H at position 2 with n,63n@323Cln5Cos+1
replace 5 at position 10 with 2,63n@323Cln2Cos+1
add S at position 2,63Sn@323Cln2Cos+1
replace 3 at position 1 with ),6)Sn@323Cln2Cos+1
add I at position 1,6I)Sn@323Cln2Cos+1
replace ) at position 2 with c,6IcSn@323Cln2Cos+1
replace l at position 10 with 2,6IcSn@323C2n2Cos+1
add O at position 8,6IcSn@32O3C2n2Cos+1
add = at position 5,6IcSn=@32O3C2n2Cos+1
remove S from position 3,6Icn=@32O3C2n2Cos+1
replace O at position 8 with B,6Icn=@32B3C2n2Cos+1
replace n at position 12 with ],6Icn=@32B3C2]2Cos+1
replace n at position 3 with ],6Ic]=@32B3C2]2Cos+1
add n at position 3,6Icn]=@32B3C2]2Cos+1
remove = from position 5,6Icn]@32B3C2]2Cos+1
replace 3 at position 9 with O,6Icn]@32BOC2]2Cos+1
add - at position 16,6Icn]@32BOC2]2Co-s+1
replace @ at position 5 with c,6Icn]c32BOC2]2Co-s+1
replace o at position 15 with ),6Icn]c32BOC2]2C)-s+1
replace ) at position 15 with (,6Icn]c32BOC2]2C(-s+1
add ) at position 18,6Icn]c32BOC2]2C(-s)+1
add - at position 11,6Icn]c32BOC-2]2C(-s)+1
replace I at position 1 with C,6Ccn]c32BOC-2]2C(-s)+1
remove ] from position 4,6Ccnc32BOC-2]2C(-s)+1
replace 3 at position 5 with C,6CcncC2BOC-2]2C(-s)+1
remove O from position 8,6CcncC2BC-2]2C(-s)+1
add B at position 14,6CcncC2BC-2]2CB(-s)+1
remove - from position 16,6CcncC2BC-2]2CB(s)+1
remove ] from position 11,6CcncC2BC-22CB(s)+1
add c at position 4,6CcnccC2BC-22CB(s)+1
add H at position 2,6CHcnccC2BC-22CB(s)+1
remove H from position 2,6CcnccC2BC-22CB(s)+1
add C at position 12,6CcnccC2BC-2C2CB(s)+1
remove - from position 10,6CcnccC2BC2C2CB(s)+1
add ) at position 16,6CcnccC2BC2C2CB()s)+1
add C at position 16,6CcnccC2BC2C2CB(C)s)+1
add + at position 6,6Ccncc+C2BC2C2CB(C)s)+1
replace 2 at position 8 with O,6Ccncc+COBC2C2CB(C)s)+1
remove B from position 9,6Ccncc+COC2C2CB(C)s)+1
add 2 at position 17,6Ccncc+COC2C2CB(C2)s)+1
add C at position 1,6CCcncc+COC2C2CB(C2)s)+1
remove ) from position 21,6CCcncc+COC2C2CB(C2)s+1
add C at position 14,6CCcncc+COC2C2CCB(C2)s+1
replace + at position 7 with (,6CCcncc(COC2C2CCB(C2)s+1
replace B at position 16 with C,6CCcncc(COC2C2CCC(C2)s+1
remove + from position 22,6CCcncc(COC2C2CCC(C2)s1
add 1 at position 4,6CCc1ncc(COC2C2CCC(C2)s1
add + at position 15,6CCc1ncc(COC2C2+CCC(C2)s1
remove 6 from position 0,CCc1ncc(COC2C2+CCC(C2)s1
remove 2 from position 11,CCc1ncc(COCC2+CCC(C2)s1
add N at position 2,CCNc1ncc(COCC2+CCC(C2)s1
remove ( from position 18,CCNc1ncc(COCC2+CCCC2)s1
replace + at position 14 with C,CCNc1ncc(COCC2CCCCC2)s1
final: CCNc1ncc(COCC2CCCCC2)s1,CCNc1ncc(COCC2CCCCC2)s1
