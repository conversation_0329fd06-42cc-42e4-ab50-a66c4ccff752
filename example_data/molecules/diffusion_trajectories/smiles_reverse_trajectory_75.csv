log,state
initialize: ,
add S at position 0,S
add O at position 0,OS
add o at position 0,o<PERSON>
replace S at position 2 with [,oO[
add [ at position 1,o[O[
add 1 at position 1,o1[O[
replace [ at position 2 with 6,o16O[
remove 6 from position 2,o1O[
add I at position 4,o1O[I
replace I at position 4 with (,o1O[(
remove ( from position 4,o1O[
add 5 at position 2,o15O[
remove o from position 0,15O[
add F at position 2,15FO[
add / at position 3,15F/O[
add I at position 5,15F/OI[
add C at position 7,15F/OI[C
replace O at position 4 with 3,15F/3I[C
replace F at position 2 with B,15B/3I[C
remove 1 from position 0,5B/3I[C
add = at position 2,5B=/3I[C
replace I at position 5 with 3,5B=/33[C
add B at position 0,B5B=/33[C
add 6 at position 3,B5B6=/33[C
replace B at position 2 with o,B5o6=/33[C
replace = at position 4 with r,B5o6r/33[C
replace B at position 0 with 1,15o6r/33[C
replace C at position 9 with /,15o6r/33[/
replace [ at position 8 with ),15o6r/33)/
remove 6 from position 3,15or/33)/
replace 5 at position 1 with r,1ror/33)/
add - at position 6,1ror/3-3)/
remove r from position 1,1or/3-3)/
add H at position 7,1or/3-3H)/
add B at position 9,1or/3-3H)B/
add S at position 8,1or/3-3HS)B/
replace - at position 5 with 6,1or/363HS)B/
replace S at position 8 with s,1or/363Hs)B/
replace / at position 11 with c,1or/363Hs)Bc
remove s from position 8,1or/363H)Bc
add 5 at position 9,1or/363H)5Bc
replace r at position 2 with 3,1o3/363H)5Bc
add 5 at position 10,1o3/363H)55Bc
replace / at position 3 with 4,1o34363H)55Bc
remove 5 from position 9,1o34363H)5Bc
add + at position 12,1o34363H)5Bc+
remove ) from position 8,1o34363H5Bc+
add @ at position 9,1o34363H5@Bc+
replace 3 at position 4 with n,1o34n63H5@Bc+
replace o at position 1 with 7,1734n63H5@Bc+
add s at position 6,1734n6s3H5@Bc+
add ) at position 5,1734n)6s3H5@Bc+
remove 1 from position 0,734n)6s3H5@Bc+
remove 7 from position 0,34n)6s3H5@Bc+
replace 6 at position 4 with F,34n)Fs3H5@Bc+
add 3 at position 1,334n)Fs3H5@Bc+
remove H from position 8,334n)Fs35@Bc+
replace 3 at position 7 with o,334n)Fso5@Bc+
remove 5 from position 8,334n)Fso@Bc+
add / at position 9,334n)Fso@/Bc+
replace ) at position 4 with =,334n=Fso@/Bc+
add 5 at position 8,334n=Fso5@/Bc+
add o at position 4,334no=Fso5@/Bc+
add 7 at position 10,334no=Fso57@/Bc+
replace 5 at position 9 with 2,334no=Fso27@/Bc+
replace 7 at position 10 with #,334no=Fso2#@/Bc+
remove o from position 4,334n=Fso2#@/Bc+
remove B from position 12,334n=Fso2#@/c+
add r at position 1,3r34n=Fso2#@/c+
replace @ at position 11 with o,3r34n=Fso2#o/c+
add N at position 0,N3r34n=Fso2#o/c+
remove c from position 14,N3r34n=Fso2#o/+
add 4 at position 11,N3r34n=Fso24#o/+
add ( at position 11,N3r34n=Fso2(4#o/+
remove / from position 15,N3r34n=Fso2(4#o+
add / at position 15,N3r34n=Fso2(4#o/+
remove 4 from position 12,N3r34n=Fso2(#o/+
add C at position 0,CN3r34n=Fso2(#o/+
remove / from position 15,CN3r34n=Fso2(#o+
add F at position 14,CN3r34n=Fso2(#Fo+
remove ( from position 12,CN3r34n=Fso2#Fo+
add r at position 8,CN3r34n=rFso2#Fo+
add @ at position 3,CN3@r34n=rFso2#Fo+
add 1 at position 18,CN3@r34n=rFso2#Fo+1
replace = at position 8 with H,CN3@r34nHrFso2#Fo+1
replace + at position 17 with ),CN3@r34nHrFso2#Fo)1
add B at position 18,CN3@r34nHrFso2#Fo)B1
add c at position 9,CN3@r34nHcrFso2#Fo)B1
replace H at position 8 with 4,CN3@r34n4crFso2#Fo)B1
replace s at position 12 with 7,CN3@r34n4crF7o2#Fo)B1
add S at position 6,CN3@r3S4n4crF7o2#Fo)B1
remove 7 from position 13,CN3@r3S4n4crFo2#Fo)B1
remove r from position 4,CN3@3S4n4crFo2#Fo)B1
remove N from position 1,C3@3S4n4crFo2#Fo)B1
add r at position 5,C3@3Sr4n4crFo2#Fo)B1
remove ) from position 17,C3@3Sr4n4crFo2#FoB1
remove r from position 5,C3@3S4n4crFo2#FoB1
add N at position 4,C3@3NS4n4crFo2#FoB1
remove B from position 17,C3@3NS4n4crFo2#Fo1
remove F from position 11,C3@3NS4n4cro2#Fo1
replace 4 at position 6 with l,C3@3NSln4cro2#Fo1
add 3 at position 5,C3@3N3Sln4cro2#Fo1
replace o at position 16 with s,C3@3N3Sln4cro2#Fs1
remove r from position 11,C3@3N3Sln4co2#Fs1
replace F at position 14 with ),C3@3N3Sln4co2#)s1
remove S from position 6,C3@3N3ln4co2#)s1
add ( at position 6,C3@3N3(ln4co2#)s1
add H at position 2,C3H@3N3(ln4co2#)s1
replace H at position 2 with n,C3n@3N3(ln4co2#)s1
replace 4 at position 10 with o,C3n@3N3(lnoco2#)s1
add S at position 2,C3Sn@3N3(lnoco2#)s1
replace 3 at position 1 with +,C+Sn@3N3(lnoco2#)s1
add H at position 1,CH+Sn@3N3(lnoco2#)s1
replace + at position 2 with #,CH#Sn@3N3(lnoco2#)s1
replace l at position 10 with O,CH#Sn@3N3(Onoco2#)s1
add O at position 8,CH#Sn@3NO3(Onoco2#)s1
add = at position 5,CH#Sn=@3NO3(Onoco2#)s1
remove S from position 3,CH#n=@3NO3(Onoco2#)s1
replace O at position 8 with C,CH#n=@3NC3(Onoco2#)s1
replace n at position 12 with ],CH#n=@3NC3(O]oco2#)s1
replace n at position 3 with ],CH#]=@3NC3(O]oco2#)s1
add 1 at position 3,CH#1]=@3NC3(O]oco2#)s1
remove = from position 5,CH#1]@3NC3(O]oco2#)s1
replace 3 at position 9 with O,CH#1]@3NCO(O]oco2#)s1
add - at position 16,CH#1]@3NCO(O]oco-2#)s1
replace @ at position 5 with S,CH#1]S3NCO(O]oco-2#)s1
replace o at position 15 with (,CH#1]S3NCO(O]oc(-2#)s1
replace ( at position 15 with c,CH#1]S3NCO(O]occ-2#)s1
add ) at position 18,CH#1]S3NCO(O]occ-2)#)s1
add = at position 11,CH#1]S3NCO(=O]occ-2)#)s1
replace H at position 1 with O,CO#1]S3NCO(=O]occ-2)#)s1
remove ] from position 4,CO#1S3NCO(=O]occ-2)#)s1
replace 3 at position 5 with c,CO#1ScNCO(=O]occ-2)#)s1
remove O from position 8,CO#1ScNC(=O]occ-2)#)s1
add c at position 14,CO#1ScNC(=O]occc-2)#)s1
remove - from position 16,CO#1ScNC(=O]occc2)#)s1
remove ] from position 11,CO#1ScNC(=Ooccc2)#)s1
add n at position 4,CO#1nScNC(=Ooccc2)#)s1
add H at position 2,COH#1nScNC(=Ooccc2)#)s1
remove H from position 2,CO#1nScNC(=Ooccc2)#)s1
add + at position 12,CO#1nScNC(=O+occc2)#)s1
add H at position 22,CO#1nScNC(=O+occc2)#)sH1
replace # at position 2 with c,COc1nScNC(=O+occc2)#)sH1
add C at position 0,CCOc1nScNC(=O+occc2)#)sH1
replace o at position 14 with r,CCOc1nScNC(=O+rccc2)#)sH1
add N at position 20,CCOc1nScNC(=O+rccc2)N#)sH1
add 2 at position 11,CCOc1nScNC(2=O+rccc2)N#)sH1
replace O at position 2 with C,CCCc1nScNC(2=O+rccc2)N#)sH1
add ( at position 8,CCCc1nSc(NC(2=O+rccc2)N#)sH1
add 2 at position 16,CCCc1nSc(NC(2=O+2rccc2)N#)sH1
add c at position 19,CCCc1nSc(NC(2=O+2rcccc2)N#)sH1
remove r from position 17,CCCc1nSc(NC(2=O+2cccc2)N#)sH1
add n at position 7,CCCc1nSnc(NC(2=O+2cccc2)N#)sH1
remove C from position 0,CCc1nSnc(NC(2=O+2cccc2)N#)sH1
remove S from position 5,CCc1nnc(NC(2=O+2cccc2)N#)sH1
add ) at position 14,CCc1nnc(NC(2=O)+2cccc2)N#)sH1
add c at position 19,CCc1nnc(NC(2=O)+2ccccc2)N#)sH1
remove # from position 25,CCc1nnc(NC(2=O)+2ccccc2)N)sH1
remove 2 from position 11,CCc1nnc(NC(=O)+2ccccc2)N)sH1
remove ) from position 22,CCc1nnc(NC(=O)+2ccccc2N)sH1
remove H from position 25,CCc1nnc(NC(=O)+2ccccc2N)s1
replace + at position 14 with c,CCc1nnc(NC(=O)c2ccccc2N)s1
final: CCc1nnc(NC(=O)c2ccccc2N)s1,CCc1nnc(NC(=O)c2ccccc2N)s1
