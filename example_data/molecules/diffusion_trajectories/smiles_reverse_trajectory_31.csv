log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with ),oFc)BH-C
remove c from position 2,oF)BH-C
remove B from position 3,oF)H-C
replace - at position 4 with ],oF)H]C
remove C from position 5,oF)H]
add B at position 4,oF)HB]
add N at position 6,oF)HB]N
replace B at position 4 with -,oF)H-]N
add r at position 7,oF)H-]Nr
add [ at position 6,oF)H-][Nr
add [ at position 2,oF[)H-][Nr
add 5 at position 3,oF[5)H-][Nr
replace [ at position 8 with B,oF[5)H-]BNr
add l at position 2,oFl[5)H-]BNr
add 4 at position 2,oF4l[5)H-]BNr
replace [ at position 4 with H,oF4lH5)H-]BNr
add 6 at position 5,oF4lH65)H-]BNr
add O at position 2,oFO4lH65)H-]BNr
remove H from position 9,oFO4lH65)-]BNr
add 6 at position 13,oFO4lH65)-]BN6r
replace F at position 1 with [,o[O4lH65)-]BN6r
remove H from position 5,o[O4l65)-]BN6r
replace 4 at position 3 with r,o[Orl65)-]BN6r
add = at position 6,o[Orl6=5)-]BN6r
add s at position 11,o[Orl6=5)-]sBN6r
remove 6 from position 14,o[Orl6=5)-]sBNr
replace ] at position 10 with ),o[Orl6=5)-)sBNr
remove O from position 2,o[rl6=5)-)sBNr
add 2 at position 11,o[rl6=5)-)s2BNr
replace N at position 13 with /,o[rl6=5)-)s2B/r
add S at position 6,o[rl6=S5)-)s2B/r
add l at position 2,o[lrl6=S5)-)s2B/r
add F at position 11,o[lrl6=S5)-F)s2B/r
replace o at position 0 with S,S[lrl6=S5)-F)s2B/r
remove S from position 7,S[lrl6=5)-F)s2B/r
add c at position 10,S[lrl6=5)-cF)s2B/r
remove l from position 2,S[rl6=5)-cF)s2B/r
add r at position 1,Sr[rl6=5)-cF)s2B/r
add ) at position 12,Sr[rl6=5)-cF))s2B/r
replace F at position 11 with 3,Sr[rl6=5)-c3))s2B/r
add 4 at position 19,Sr[rl6=5)-c3))s2B/r4
add + at position 2,Sr+[rl6=5)-c3))s2B/r4
add O at position 1,SOr+[rl6=5)-c3))s2B/r4
add F at position 21,SOr+[rl6=5)-c3))s2B/rF4
remove r from position 2,SO+[rl6=5)-c3))s2B/rF4
add 3 at position 14,SO+[rl6=5)-c3)3)s2B/rF4
add c at position 19,SO+[rl6=5)-c3)3)s2Bc/rF4
add C at position 17,SO+[rl6=5)-c3)3)sC2Bc/rF4
replace - at position 10 with l,SO+[rl6=5)lc3)3)sC2Bc/rF4
add = at position 12,SO+[rl6=5)lc=3)3)sC2Bc/rF4
replace F at position 24 with (,SO+[rl6=5)lc=3)3)sC2Bc/r(4
replace / at position 22 with 1,SO+[rl6=5)lc=3)3)sC2Bc1r(4
remove s from position 17,SO+[rl6=5)lc=3)3)C2Bc1r(4
add - at position 19,SO+[rl6=5)lc=3)3)C2-Bc1r(4
replace r at position 4 with ],SO+[]l6=5)lc=3)3)C2-Bc1r(4
add ( at position 21,SO+[]l6=5)lc=3)3)C2-B(c1r(4
add S at position 5,SO+[]Sl6=5)lc=3)3)C2-B(c1r(4
add ) at position 27,SO+[]Sl6=5)lc=3)3)C2-B(c1r()4
replace S at position 0 with C,CO+[]Sl6=5)lc=3)3)C2-B(c1r()4
add C at position 20,CO+[]Sl6=5)lc=3)3)C2C-B(c1r()4
replace 6 at position 7 with O,CO+[]SlO=5)lc=3)3)C2C-B(c1r()4
add c at position 15,CO+[]SlO=5)lc=3c)3)C2C-B(c1r()4
replace 4 at position 30 with 7,CO+[]SlO=5)lc=3c)3)C2C-B(c1r()7
replace ) at position 29 with 7,CO+[]SlO=5)lc=3c)3)C2C-B(c1r(77
add ) at position 27,CO+[]SlO=5)lc=3c)3)C2C-B(c1)r(77
replace 5 at position 9 with O,CO+[]SlO=O)lc=3c)3)C2C-B(c1)r(77
add @ at position 2,CO@+[]SlO=O)lc=3c)3)C2C-B(c1)r(77
replace ( at position 25 with /,CO@+[]SlO=O)lc=3c)3)C2C-B/c1)r(77
remove - from position 23,CO@+[]SlO=O)lc=3c)3)C2CB/c1)r(77
remove l from position 7,CO@+[]SO=O)lc=3c)3)C2CB/c1)r(77
replace 3 at position 14 with r,CO@+[]SO=O)lc=rc)3)C2CB/c1)r(77
add ) at position 21,CO@+[]SO=O)lc=rc)3)C2)CB/c1)r(77
add 4 at position 13,CO@+[]SO=O)lc4=rc)3)C2)CB/c1)r(77
replace ) at position 17 with r,CO@+[]SO=O)lc4=rcr3)C2)CB/c1)r(77
add H at position 32,CO@+[]SO=O)lc4=rcr3)C2)CB/c1)r(7H7
add 4 at position 18,CO@+[]SO=O)lc4=rcr43)C2)CB/c1)r(7H7
add 6 at position 21,CO@+[]SO=O)lc4=rcr43)6C2)CB/c1)r(7H7
replace 4 at position 18 with o,CO@+[]SO=O)lc4=rcro3)6C2)CB/c1)r(7H7
replace 6 at position 21 with r,CO@+[]SO=O)lc4=rcro3)rC2)CB/c1)r(7H7
remove ) from position 30,CO@+[]SO=O)lc4=rcro3)rC2)CB/c1r(7H7
add 3 at position 6,CO@+[]3SO=O)lc4=rcro3)rC2)CB/c1r(7H7
add ) at position 29,CO@+[]3SO=O)lc4=rcro3)rC2)CB/)c1r(7H7
add 2 at position 30,CO@+[]3SO=O)lc4=rcro3)rC2)CB/)2c1r(7H7
remove r from position 33,CO@+[]3SO=O)lc4=rcro3)rC2)CB/)2c1(7H7
add C at position 22,CO@+[]3SO=O)lc4=rcro3)CrC2)CB/)2c1(7H7
add + at position 23,CO@+[]3SO=O)lc4=rcro3)C+rC2)CB/)2c1(7H7
remove / from position 30,CO@+[]3SO=O)lc4=rcro3)C+rC2)CB)2c1(7H7
add C at position 30,CO@+[]3SO=O)lc4=rcro3)C+rC2)CBC)2c1(7H7
add c at position 19,CO@+[]3SO=O)lc4=rcrco3)C+rC2)CBC)2c1(7H7
add n at position 4,CO@+n[]3SO=O)lc4=rcrco3)C+rC2)CBC)2c1(7H7
replace = at position 10 with 5,CO@+n[]3SO5O)lc4=rcrco3)C+rC2)CBC)2c1(7H7
remove 2 from position 28,CO@+n[]3SO5O)lc4=rcrco3)C+rC)CBC)2c1(7H7
remove 7 from position 37,CO@+n[]3SO5O)lc4=rcrco3)C+rC)CBC)2c1(H7
add c at position 17,CO@+n[]3SO5O)lc4=crcrco3)C+rC)CBC)2c1(H7
add = at position 7,CO@+n[]=3SO5O)lc4=crcrco3)C+rC)CBC)2c1(H7
add # at position 36,CO@+n[]=3SO5O)lc4=crcrco3)C+rC)CBC)2#c1(H7
replace = at position 17 with F,CO@+n[]=3SO5O)lc4Fcrcrco3)C+rC)CBC)2#c1(H7
replace ) at position 34 with C,CO@+n[]=3SO5O)lc4Fcrcrco3)C+rC)CBCC2#c1(H7
add ) at position 36,CO@+n[]=3SO5O)lc4Fcrcrco3)C+rC)CBCC2)#c1(H7
add 3 at position 19,CO@+n[]=3SO5O)lc4Fc3rcrco3)C+rC)CBCC2)#c1(H7
replace F at position 17 with n,CO@+n[]=3SO5O)lc4nc3rcrco3)C+rC)CBCC2)#c1(H7
remove 7 from position 43,CO@+n[]=3SO5O)lc4nc3rcrco3)C+rC)CBCC2)#c1(H
add F at position 20,CO@+n[]=3SO5O)lc4nc3Frcrco3)C+rC)CBCC2)#c1(H
replace @ at position 2 with 2,CO2+n[]=3SO5O)lc4nc3Frcrco3)C+rC)CBCC2)#c1(H
add ] at position 9,CO2+n[]=3]SO5O)lc4nc3Frcrco3)C+rC)CBCC2)#c1(H
remove ( from position 43,CO2+n[]=3]SO5O)lc4nc3Frcrco3)C+rC)CBCC2)#c1H
add r at position 10,CO2+n[]=3]rSO5O)lc4nc3Frcrco3)C+rC)CBCC2)#c1H
remove ) from position 34,CO2+n[]=3]rSO5O)lc4nc3Frcrco3)C+rCCBCC2)#c1H
remove r from position 10,CO2+n[]=3]SO5O)lc4nc3Frcrco3)C+rCCBCC2)#c1H
add S at position 8,CO2+n[]=S3]SO5O)lc4nc3Frcrco3)C+rCCBCC2)#c1H
remove B from position 35,CO2+n[]=S3]SO5O)lc4nc3Frcrco3)C+rCCCC2)#c1H
remove F from position 22,CO2+n[]=S3]SO5O)lc4nc3rcrco3)C+rCCCC2)#c1H
replace 5 at position 13 with 3,CO2+n[]=S3]SO3O)lc4nc3rcrco3)C+rCCCC2)#c1H
add C at position 11,CO2+n[]=S3]CSO3O)lc4nc3rcrco3)C+rCCCC2)#c1H
replace r at position 32 with ],CO2+n[]=S3]CSO3O)lc4nc3rcrco3)C+]CCCC2)#c1H
remove r from position 23,CO2+n[]=S3]CSO3O)lc4nc3crco3)C+]CCCC2)#c1H
replace C at position 29 with N,CO2+n[]=S3]CSO3O)lc4nc3crco3)N+]CCCC2)#c1H
remove S from position 12,CO2+n[]=S3]CO3O)lc4nc3crco3)N+]CCCC2)#c1H
add N at position 12,CO2+n[]=S3]CNO3O)lc4nc3crco3)N+]CCCC2)#c1H
add H at position 4,CO2+Hn[]=S3]CNO3O)lc4nc3crco3)N+]CCCC2)#c1H
replace H at position 4 with c,CO2+cn[]=S3]CNO3O)lc4nc3crco3)N+]CCCC2)#c1H
replace 4 at position 20 with ],CO2+cn[]=S3]CNO3O)lc]nc3crco3)N+]CCCC2)#c1H
add S at position 5,CO2+cSn[]=S3]CNO3O)lc]nc3crco3)N+]CCCC2)#c1H
replace 2 at position 2 with H,COH+cSn[]=S3]CNO3O)lc]nc3crco3)N+]CCCC2)#c1H
add O at position 3,COHO+cSn[]=S3]CNO3O)lc]nc3crco3)N+]CCCC2)#c1H
replace + at position 4 with #,COHO#cSn[]=S3]CNO3O)lc]nc3crco3)N+]CCCC2)#c1H
replace l at position 20 with c,COHO#cSn[]=S3]CNO3O)cc]nc3crco3)N+]CCCC2)#c1H
add = at position 17,COHO#cSn[]=S3]CNO=3O)cc]nc3crco3)N+]CCCC2)#c1H
add B at position 10,COHO#cSn[]B=S3]CNO=3O)cc]nc3crco3)N+]CCCC2)#c1H
remove S from position 6,COHO#cn[]B=S3]CNO=3O)cc]nc3crco3)N+]CCCC2)#c1H
replace O at position 16 with (,COHO#cn[]B=S3]CN(=3O)cc]nc3crco3)N+]CCCC2)#c1H
replace n at position 24 with c,COHO#cn[]B=S3]CN(=3O)cc]cc3crco3)N+]CCCC2)#c1H
replace n at position 6 with c,COHO#cc[]B=S3]CN(=3O)cc]cc3crco3)N+]CCCC2)#c1H
add ( at position 7,COHO#cc([]B=S3]CN(=3O)cc]cc3crco3)N+]CCCC2)#c1H
remove = from position 11,COHO#cc([]BS3]CN(=3O)cc]cc3crco3)N+]CCCC2)#c1H
replace 3 at position 18 with O,COHO#cc([]BS3]CN(=OO)cc]cc3crco3)N+]CCCC2)#c1H
add - at position 33,COHO#cc([]BS3]CN(=OO)cc]cc3crco3)-N+]CCCC2)#c1H
replace B at position 10 with @,COHO#cc([]@S3]CN(=OO)cc]cc3crco3)-N+]CCCC2)#c1H
replace o at position 30 with (,COHO#cc([]@S3]CN(=OO)cc]cc3crc(3)-N+]CCCC2)#c1H
replace ( at position 30 with 2,COHO#cc([]@S3]CN(=OO)cc]cc3crc23)-N+]CCCC2)#c1H
add 2 at position 37,COHO#cc([]@S3]CN(=OO)cc]cc3crc23)-N+]2CCCC2)#c1H
add 2 at position 22,COHO#cc([]@S3]CN(=OO)c2c]cc3crc23)-N+]2CCCC2)#c1H
replace H at position 2 with c,COcO#cc([]@S3]CN(=OO)c2c]cc3crc23)-N+]2CCCC2)#c1H
remove ] from position 9,COcO#cc([@S3]CN(=OO)c2c]cc3crc23)-N+]2CCCC2)#c1H
replace 3 at position 11 with H,COcO#cc([@SH]CN(=OO)c2c]cc3crc23)-N+]2CCCC2)#c1H
remove O from position 17,COcO#cc([@SH]CN(=O)c2c]cc3crc23)-N+]2CCCC2)#c1H
add r at position 29,COcO#cc([@SH]CN(=O)c2c]cc3crcr23)-N+]2CCCC2)#c1H
remove - from position 33,COcO#cc([@SH]CN(=O)c2c]cc3crcr23)N+]2CCCC2)#c1H
remove ] from position 22,COcO#cc([@SH]CN(=O)c2ccc3crcr23)N+]2CCCC2)#c1H
add C at position 9,COcO#cc([C@SH]CN(=O)c2ccc3crcr23)N+]2CCCC2)#c1H
add H at position 4,COcOH#cc([C@SH]CN(=O)c2ccc3crcr23)N+]2CCCC2)#c1H
remove H from position 4,COcO#cc([C@SH]CN(=O)c2ccc3crcr23)N+]2CCCC2)#c1H
add c at position 24,COcO#cc([C@SH]CN(=O)c2cccc3crcr23)N+]2CCCC2)#c1H
add c at position 45,COcO#cc([C@SH]CN(=O)c2cccc3crcr23)N+]2CCCC2)#cc1H
replace # at position 4 with c,COcOccc([C@SH]CN(=O)c2cccc3crcr23)N+]2CCCC2)#cc1H
add C at position 1,CCOcOccc([C@SH]CN(=O)c2cccc3crcr23)N+]2CCCC2)#cc1H
replace r at position 29 with c,CCOcOccc([C@SH]CN(=O)c2cccc3cccr23)N+]2CCCC2)#cc1H
add ) at position 41,CCOcOccc([C@SH]CN(=O)c2cccc3cccr23)N+]2CC)CC2)#cc1H
add 2 at position 22,CCOcOccc([C@SH]CN(=O)c22cccc3cccr23)N+]2CC)CC2)#cc1H
replace O at position 4 with 1,CCOc1ccc([C@SH]CN(=O)c22cccc3cccr23)N+]2CC)CC2)#cc1H
add C at position 17,CCOc1ccc([C@SH]CNC(=O)c22cccc3cccr23)N+]2CC)CC2)#cc1H
add c at position 32,CCOc1ccc([C@SH]CNC(=O)c22cccc3ccccr23)N+]2CC)CC2)#cc1H
add [ at position 38,CCOc1ccc([C@SH]CNC(=O)c22cccc3ccccr23)[N+]2CC)CC2)#cc1H
remove r from position 34,CCOc1ccc([C@SH]CNC(=O)c22cccc3cccc23)[N+]2CC)CC2)#cc1H
add ( at position 15,CCOc1ccc([C@SH](CNC(=O)c22cccc3cccc23)[N+]2CC)CC2)#cc1H
remove C from position 0,COc1ccc([C@SH](CNC(=O)c22cccc3cccc23)[N+]2CC)CC2)#cc1H
remove S from position 11,COc1ccc([C@H](CNC(=O)c22cccc3cccc23)[N+]2CC)CC2)#cc1H
add + at position 29,COc1ccc([C@H](CNC(=O)c22cccc3+cccc23)[N+]2CC)CC2)#cc1H
add H at position 39,COc1ccc([C@H](CNC(=O)c22cccc3+cccc23)[NH+]2CC)CC2)#cc1H
remove # from position 50,COc1ccc([C@H](CNC(=O)c22cccc3+cccc23)[NH+]2CC)CC2)cc1H
remove 2 from position 22,COc1ccc([C@H](CNC(=O)c2cccc3+cccc23)[NH+]2CC)CC2)cc1H
remove ) from position 44,COc1ccc([C@H](CNC(=O)c2cccc3+cccc23)[NH+]2CCCC2)cc1H
remove H from position 51,COc1ccc([C@H](CNC(=O)c2cccc3+cccc23)[NH+]2CCCC2)cc1
replace + at position 28 with c,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)cc1
final: COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)cc1,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)cc1
