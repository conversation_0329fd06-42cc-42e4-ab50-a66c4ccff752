log,state
initialize: ,
add 1 at position 0,1
add C at position 1,1C
add N at position 2,1C<PERSON>
add ) at position 3,1CN)
replace N at position 2 with r,1Cr)
replace <PERSON> at position 1 with o,1or)
remove 1 from position 0,or)
add S at position 1,oSr)
add C at position 4,oSr)C
replace o at position 0 with l,lSr)C
remove S from position 1,lr)C
add / at position 2,lr/)C
remove l from position 0,r/)C
add r at position 0,rr/)C
add - at position 3,rr/-)C
add 3 at position 4,rr/-3)C
add l at position 3,rr/l-3)C
add 1 at position 1,r1r/l-3)C
add 7 at position 0,7r1r/l-3)C
add [ at position 10,7r1r/l-3)C[
remove r from position 1,71r/l-3)C[
add s at position 7,71r/l-3s)C[
add / at position 9,71r/l-3s)/C[
add n at position 8,71r/l-3sn)/C[
replace - at position 5 with n,71r/ln3sn)/C[
add ) at position 6,71r/ln)3sn)/C[
replace <PERSON> at position 12 with H,71r/ln)3sn)/H[
replace / at position 11 with 3,71r/ln)3sn)3H[
remove s from position 8,71r/ln)3n)3H[
add 5 at position 9,71r/ln)3n5)3H[
replace r at position 2 with N,71N/ln)3n5)3H[
add S at position 10,71N/ln)3n5S)3H[
replace / at position 3 with I,71NIln)3n5S)3H[
remove 5 from position 9,71NIln)3nS)3H[
add ( at position 12,71NIln)3nS)3(H[
add o at position 2,71oNIln)3nS)3(H[
add 3 at position 7,71oNIln3)3nS)3(H[
replace 3 at position 9 with C,71oNIln3)CnS)3(H[
replace o at position 2 with 7,717NIln3)CnS)3(H[
add C at position 13,717NIln3)CnS)C3(H[
add 6 at position 10,717NIln3)C6nS)C3(H[
remove 1 from position 1,77NIln3)C6nS)C3(H[
remove 7 from position 0,7NIln3)C6nS)C3(H[
replace 6 at position 8 with ],7NIln3)C]nS)C3(H[
add 7 at position 3,7NI7ln3)C]nS)C3(H[
remove H from position 16,7NI7ln3)C]nS)C3([
replace 3 at position 14 with 5,7NI7ln3)C]nS)C5([
add s at position 10,7NI7ln3)C]snS)C5([
add ] at position 6,7NI7ln]3)C]snS)C5([
replace ) at position 8 with [,7NI7ln]3[C]snS)C5([
add / at position 16,7NI7ln]3[C]snS)C/5([
add 4 at position 9,7NI7ln]3[4C]snS)C/5([
add 5 at position 18,7NI7ln]3[4C]snS)C/55([
replace 4 at position 9 with N,7NI7ln]3[NC]snS)C/55([
replace 5 at position 18 with C,7NI7ln]3[NC]snS)C/C5([
replace ) at position 15 with r,7NI7ln]3[NC]snSrC/C5([
replace 7 at position 0 with 6,6NI7ln]3[NC]snSrC/C5([
replace S at position 14 with 7,6NI7ln]3[NC]sn7rC/C5([
add C at position 0,C6NI7ln]3[NC]sn7rC/C5([
add - at position 21,C6NI7ln]3[NC]sn7rC/C5-([
remove r from position 16,C6NI7ln]3[NC]sn7C/C5-([
add F at position 11,C6NI7ln]3[NFC]sn7C/C5-([
add 2 at position 11,C6NI7ln]3[N2FC]sn7C/C5-([
add 7 at position 5,C6NI77ln]3[N2FC]sn7C/C5-([
replace 7 at position 5 with C,C6NI7Cln]3[N2FC]sn7C/C5-([
add s at position 21,C6NI7Cln]3[N2FC]sn7C/sC5-([
add l at position 9,C6NI7Cln]l3[N2FC]sn7C/sC5-([
add 2 at position 2,C62NI7Cln]l3[N2FC]sn7C/sC5-([
replace 7 at position 5 with c,C62NIcCln]l3[N2FC]sn7C/sC5-([
replace 2 at position 14 with 4,C62NIcCln]l3[N4FC]sn7C/sC5-([
add = at position 11,C62NIcCln]l=3[N4FC]sn7C/sC5-([
add l at position 18,C62NIcCln]l=3[N4FCl]sn7C/sC5-([
add 2 at position 19,C62NIcCln]l=3[N4FCl2]sn7C/sC5-([
add 1 at position 24,C62NIcCln]l=3[N4FCl2]sn71C/sC5-([
add 3 at position 17,C62NIcCln]l=3[N4F3Cl2]sn71C/sC5-([
add 1 at position 26,C62NIcCln]l=3[N4F3Cl2]sn711C/sC5-([
replace c at position 5 with ),C62NI)Cln]l=3[N4F3Cl2]sn711C/sC5-([
add r at position 9,C62NI)Clnr]l=3[N4F3Cl2]sn711C/sC5-([
add n at position 19,C62NI)Clnr]l=3[N4F3nCl2]sn711C/sC5-([
replace F at position 17 with N,C62NI)Clnr]l=3[N4N3nCl2]sn711C/sC5-([
replace s at position 24 with F,C62NI)Clnr]l=3[N4N3nCl2]Fn711C/sC5-([
add @ at position 13,C62NI)Clnr]l=@3[N4N3nCl2]Fn711C/sC5-([
remove 7 from position 27,C62NI)Clnr]l=@3[N4N3nCl2]Fn11C/sC5-([
remove r from position 9,C62NI)Cln]l=@3[N4N3nCl2]Fn11C/sC5-([
remove N from position 3,C62I)Cln]l=@3[N4N3nCl2]Fn11C/sC5-([
add [ at position 20,C62I)Cln]l=@3[N4N3nC[l2]Fn11C/sC5-([
remove l from position 9,C62I)Cln]=@3[N4N3nC[l2]Fn11C/sC5-([
remove I from position 3,C62)Cln]=@3[N4N3nC[l2]Fn11C/sC5-([
remove 5 from position 30,C62)Cln]=@3[N4N3nC[l2]Fn11C/sC-([
remove F from position 22,C62)Cln]=@3[N4N3nC[l2]n11C/sC-([
replace 4 at position 13 with C,C62)Cln]=@3[NCN3nC[l2]n11C/sC-([
replace / at position 26 with C,C62)Cln]=@3[NCN3nC[l2]n11CCsC-([
replace C at position 13 with H,C62)Cln]=@3[NHN3nC[l2]n11CCsC-([
add ] at position 23,C62)Cln]=@3[NHN3nC[l2]n]11CCsC-([
add 1 at position 29,C62)Cln]=@3[NHN3nC[l2]n]11CCs1C-([
remove 1 from position 25,C62)Cln]=@3[NHN3nC[l2]n]1CCs1C-([
remove s from position 27,C62)Cln]=@3[NHN3nC[l2]n]1CC1C-([
remove n from position 16,C62)Cln]=@3[NHN3C[l2]n]1CC1C-([
replace @ at position 9 with c,C62)Cln]=c3[NHN3C[l2]n]1CC1C-([
replace [ at position 30 with C,C62)Cln]=c3[NHN3C[l2]n]1CC1C-(C
add S at position 5,C62)CSln]=c3[NHN3C[l2]n]1CC1C-(C
replace 2 at position 2 with I,C6I)CSln]=c3[NHN3C[l2]n]1CC1C-(C
add c at position 3,C6Ic)CSln]=c3[NHN3C[l2]n]1CC1C-(C
replace ) at position 4 with N,C6IcNCSln]=c3[NHN3C[l2]n]1CC1C-(C
replace l at position 20 with C,C6IcNCSln]=c3[NHN3C[C2]n]1CC1C-(C
add ( at position 17,C6IcNCSln]=c3[NHN(3C[C2]n]1CC1C-(C
add @ at position 10,C6IcNCSln]@=c3[NHN(3C[C2]n]1CC1C-(C
remove S from position 6,C6IcNCln]@=c3[NHN(3C[C2]n]1CC1C-(C
replace N at position 16 with ],C6IcNCln]@=c3[NH](3C[C2]n]1CC1C-(C
replace n at position 24 with @,C6IcNCln]@=c3[NH](3C[C2]@]1CC1C-(C
replace l at position 6 with n,C6IcNCnn]@=c3[NH](3C[C2]@]1CC1C-(C
add c at position 7,C6IcNCncn]@=c3[NH](3C[C2]@]1CC1C-(C
remove = from position 11,C6IcNCncn]@c3[NH](3C[C2]@]1CC1C-(C
replace 3 at position 18 with O,C6IcNCncn]@c3[NH](OC[C2]@]1CC1C-(C
add ) at position 33,C6IcNCncn]@c3[NH](OC[C2]@]1CC1C-()C
replace @ at position 10 with C,C6IcNCncn]Cc3[NH](OC[C2]@]1CC1C-()C
add ) at position 30,C6IcNCncn]Cc3[NH](OC[C2]@]1CC1)C-()C
add o at position 29,C6IcNCncn]Cc3[NH](OC[C2]@]1CCo1)C-()C
add - at position 22,C6IcNCncn]Cc3[NH](OC[C-2]@]1CCo1)C-()C
replace I at position 2 with C,C6CcNCncn]Cc3[NH](OC[C-2]@]1CCo1)C-()C
remove ] from position 9,C6CcNCncnCc3[NH](OC[C-2]@]1CCo1)C-()C
replace 3 at position 11 with C,C6CcNCncnCcC[NH](OC[C-2]@]1CCo1)C-()C
remove O from position 17,C6CcNCncnCcC[NH](C[C-2]@]1CCo1)C-()C
add O at position 29,C6CcNCncnCcC[NH](C[C-2]@]1CCoO1)C-()C
remove - from position 33,C6CcNCncnCcC[NH](C[C-2]@]1CCoO1)C()C
remove ] from position 22,C6CcNCncnCcC[NH](C[C-2@]1CCoO1)C()C
add ( at position 9,C6CcNCncn(CcC[NH](C[C-2@]1CCoO1)C()C
add H at position 4,C6CcHNCncn(CcC[NH](C[C-2@]1CCoO1)C()C
remove H from position 4,C6CcNCncn(CcC[NH](C[C-2@]1CCoO1)C()C
add H at position 24,C6CcNCncn(CcC[NH](C[C-2@H]1CCoO1)C()C
remove - from position 21,C6CcNCncn(CcC[NH](C[C2@H]1CCoO1)C()C
add C at position 32,C6CcNCncn(CcC[NH](C[C2@H]1CCoO1)CC()C
add ( at position 33,C6CcNCncn(CcC[NH](C[C2@H]1CCoO1)C(C()C
add 1 at position 12,C6CcNCncn(Cc1C[NH](C[C2@H]1CCoO1)C(C()C
add @ at position 22,C6CcNCncn(Cc1C[NH](C[C@2@H]1CCoO1)C(C()C
replace N at position 4 with S,C6CcSCncn(Cc1C[NH](C[C@2@H]1CCoO1)C(C()C
add + at position 17,C6CcSCncn(Cc1C[NH+](C[C@2@H]1CCoO1)C(C()C
remove C from position 5,C6CcSncn(Cc1C[NH+](C[C@2@H]1CCoO1)C(C()C
add + at position 29,C6CcSncn(Cc1C[NH+](C[C@2@H]1C+CoO1)C(C()C
replace S at position 4 with 1,C6Cc1ncn(Cc1C[NH+](C[C@2@H]1C+CoO1)C(C()C
remove o from position 31,C6Cc1ncn(Cc1C[NH+](C[C@2@H]1C+CO1)C(C()C
remove n from position 7,C6Cc1nc(Cc1C[NH+](C[C@2@H]1C+CO1)C(C()C
add ) at position 9,C6Cc1nc(C)c1C[NH+](C[C@2@H]1C+CO1)C(C()C
add C at position 30,C6Cc1nc(C)c1C[NH+](C[C@2@H]1C+CCO1)C(C()C
remove 6 from position 1,CCc1nc(C)c1C[NH+](C[C@2@H]1C+CCO1)C(C()C
remove 2 from position 22,CCc1nc(C)c1C[NH+](C[C@@H]1C+CCO1)C(C()C
add o at position 5,CCc1noc(C)c1C[NH+](C[C@@H]1C+CCO1)C(C()C
remove ( from position 37,CCc1noc(C)c1C[NH+](C[C@@H]1C+CCO1)C(C)C
replace + at position 28 with C,CCc1noc(C)c1C[NH+](C[C@@H]1CCCCO1)C(C)C
final: CCc1noc(C)c1C[NH+](C[C@@H]1CCCCO1)C(C)C,CCc1noc(C)c1C[NH+](C[C@@H]1CCCCO1)C(C)C
