log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 3,oFc3BH-C
remove c from position 2,oF3BH-<PERSON>
remove B from position 3,oF3H-<PERSON>
replace - at position 4 with ],oF3H]C
remove C from position 5,oF3H]
add B at position 4,oF3HB]
add N at position 6,oF3HB]N
replace B at position 4 with -,oF3H-]N
add ) at position 7,oF3H-]N)
add [ at position 6,oF3H-][N)
add [ at position 2,oF[3H-][N)
add N at position 3,oF[N3H-][N)
replace [ at position 8 with C,oF[N3H-]CN)
add ( at position 2,oF([N3H-]CN)
add 4 at position 2,oF4([N3H-]CN)
replace [ at position 4 with H,oF4(HN3H-]CN)
add ] at position 5,oF4(H]N3H-]CN)
add O at position 2,oFO4(H]N3H-]CN)
remove H from position 9,oFO4(H]N3-]CN)
add 6 at position 13,oFO4(H]N3-]CN6)
replace F at position 1 with c,ocO4(H]N3-]CN6)
remove H from position 5,ocO4(]N3-]CN6)
replace 4 at position 3 with r,ocOr(]N3-]CN6)
add S at position 6,ocOr(]SN3-]CN6)
add s at position 11,ocOr(]SN3-]sCN6)
remove 6 from position 14,ocOr(]SN3-]sCN)
replace ] at position 10 with H,ocOr(]SN3-HsCN)
remove O from position 2,ocr(]SN3-HsCN)
add H at position 11,ocr(]SN3-HsHCN)
replace N at position 13 with /,ocr(]SN3-HsHC/)
add S at position 6,ocr(]SSN3-HsHC/)
add l at position 2,oclr(]SSN3-HsHC/)
add F at position 11,oclr(]SSN3-FHsHC/)
replace o at position 0 with S,Sclr(]SSN3-FHsHC/)
remove S from position 7,Sclr(]SN3-FHsHC/)
add @ at position 10,Sclr(]SN3-@FHsHC/)
remove l from position 2,Scr(]SN3-@FHsHC/)
add r at position 1,Srcr(]SN3-@FHsHC/)
add ) at position 12,Srcr(]SN3-@F)HsHC/)
replace F at position 11 with 4,Srcr(]SN3-@4)HsHC/)
add # at position 19,Srcr(]SN3-@4)HsHC/)#
add 1 at position 2,Sr1cr(]SN3-@4)HsHC/)#
add C at position 1,SCr1cr(]SN3-@4)HsHC/)#
add C at position 21,SCr1cr(]SN3-@4)HsHC/)C#
remove r from position 2,SC1cr(]SN3-@4)HsHC/)C#
add B at position 14,SC1cr(]SN3-@4)BHsHC/)C#
add 1 at position 19,SC1cr(]SN3-@4)BHsHC1/)C#
add o at position 17,SC1cr(]SN3-@4)BHsoHC1/)C#
replace - at position 10 with 6,SC1cr(]SN36@4)BHsoHC1/)C#
add r at position 12,SC1cr(]SN36@r4)BHsoHC1/)C#
replace C at position 24 with r,SC1cr(]SN36@r4)BHsoHC1/)r#
replace / at position 22 with 2,SC1cr(]SN36@r4)BHsoHC12)r#
remove s from position 17,SC1cr(]SN36@r4)BHoHC12)r#
add ) at position 19,SC1cr(]SN36@r4)BHoH)C12)r#
replace r at position 4 with @,SC1c@(]SN36@r4)BHoH)C12)r#
add / at position 21,SC1c@(]SN36@r4)BHoH)C/12)r#
remove B from position 15,SC1c@(]SN36@r4)HoH)C/12)r#
add 2 at position 18,SC1c@(]SN36@r4)HoH2)C/12)r#
replace S at position 0 with 7,7C1c@(]SN36@r4)HoH2)C/12)r#
add ) at position 20,7C1c@(]SN36@r4)HoH2))C/12)r#
add l at position 11,7C1c@(]SN36l@r4)HoH2))C/12)r#
add ) at position 29,7C1c@(]SN36l@r4)HoH2))C/12)r#)
replace 3 at position 9 with =,7C1c@(]SN=6l@r4)HoH2))C/12)r#)
replace ) at position 29 with 7,7C1c@(]SN=6l@r4)HoH2))C/12)r#7
add = at position 13,7C1c@(]SN=6l@=r4)HoH2))C/12)r#7
add r at position 20,7C1c@(]SN=6l@=r4)HoHr2))C/12)r#7
remove 1 from position 2,7Cc@(]SN=6l@=r4)HoHr2))C/12)r#7
remove 7 from position 0,Cc@(]SN=6l@=r4)HoHr2))C/12)r#7
replace 6 at position 8 with N,Cc@(]SN=Nl@=r4)HoHr2))C/12)r#7
add + at position 3,Cc@+(]SN=Nl@=r4)HoHr2))C/12)r#7
remove H from position 16,Cc@+(]SN=Nl@=r4)oHr2))C/12)r#7
replace 4 at position 14 with +,Cc@+(]SN=Nl@=r+)oHr2))C/12)r#7
add C at position 10,Cc@+(]SN=NCl@=r+)oHr2))C/12)r#7
add 4 at position 13,Cc@+(]SN=NCl@4=r+)oHr2))C/12)r#7
replace ) at position 17 with O,Cc@+(]SN=NCl@4=r+OoHr2))C/12)r#7
add ( at position 32,Cc@+(]SN=NCl@4=r+OoHr2))C/12)r#7(
add 4 at position 18,Cc@+(]SN=NCl@4=r+O4oHr2))C/12)r#7(
add 7 at position 21,Cc@+(]SN=NCl@4=r+O4oH7r2))C/12)r#7(
replace 4 at position 18 with r,Cc@+(]SN=NCl@4=r+OroH7r2))C/12)r#7(
replace 7 at position 21 with (,Cc@+(]SN=NCl@4=r+OroH(r2))C/12)r#7(
remove ) from position 30,Cc@+(]SN=NCl@4=r+OroH(r2))C/12r#7(
add 4 at position 6,Cc@+(]4SN=NCl@4=r+OroH(r2))C/12r#7(
add ) at position 29,Cc@+(]4SN=NCl@4=r+OroH(r2))C/)12r#7(
add ) at position 30,Cc@+(]4SN=NCl@4=r+OroH(r2))C/))12r#7(
remove r from position 33,Cc@+(]4SN=NCl@4=r+OroH(r2))C/))12#7(
add ] at position 22,Cc@+(]4SN=NCl@4=r+OroH](r2))C/))12#7(
add F at position 23,Cc@+(]4SN=NCl@4=r+OroH]F(r2))C/))12#7(
remove / from position 30,Cc@+(]4SN=NCl@4=r+OroH]F(r2))C))12#7(
add B at position 30,Cc@+(]4SN=NCl@4=r+OroH]F(r2))CB))12#7(
add r at position 19,Cc@+(]4SN=NCl@4=r+OrroH]F(r2))CB))12#7(
add n at position 4,Cc@+n(]4SN=NCl@4=r+OrroH]F(r2))CB))12#7(
replace = at position 10 with 5,Cc@+n(]4SN5NCl@4=r+OrroH]F(r2))CB))12#7(
remove 2 from position 28,Cc@+n(]4SN5NCl@4=r+OrroH]F(r))CB))12#7(
remove 7 from position 37,Cc@+n(]4SN5NCl@4=r+OrroH]F(r))CB))12#(
add ] at position 17,Cc@+n(]4SN5NCl@4=]r+OrroH]F(r))CB))12#(
add = at position 7,Cc@+n(]=4SN5NCl@4=]r+OrroH]F(r))CB))12#(
add c at position 36,Cc@+n(]=4SN5NCl@4=]r+OrroH]F(r))CB))c12#(
replace = at position 17 with F,Cc@+n(]=4SN5NCl@4F]r+OrroH]F(r))CB))c12#(
replace ) at position 34 with B,Cc@+n(]=4SN5NCl@4F]r+OrroH]F(r))CBB)c12#(
add n at position 36,Cc@+n(]=4SN5NCl@4F]r+OrroH]F(r))CBB)nc12#(
add 3 at position 19,Cc@+n(]=4SN5NCl@4F]3r+OrroH]F(r))CBB)nc12#(
replace F at position 17 with n,Cc@+n(]=4SN5NCl@4n]3r+OrroH]F(r))CBB)nc12#(
remove r from position 24,Cc@+n(]=4SN5NCl@4n]3r+OroH]F(r))CBB)nc12#(
add F at position 20,Cc@+n(]=4SN5NCl@4n]3Fr+OroH]F(r))CBB)nc12#(
replace @ at position 2 with 2,Cc2+n(]=4SN5NCl@4n]3Fr+OroH]F(r))CBB)nc12#(
add c at position 9,Cc2+n(]=4cSN5NCl@4n]3Fr+OroH]F(r))CBB)nc12#(
remove ( from position 43,Cc2+n(]=4cSN5NCl@4n]3Fr+OroH]F(r))CBB)nc12#
add r at position 10,Cc2+n(]=4crSN5NCl@4n]3Fr+OroH]F(r))CBB)nc12#
remove ) from position 34,Cc2+n(]=4crSN5NCl@4n]3Fr+OroH]F(r)CBB)nc12#
remove r from position 10,Cc2+n(]=4cSN5NCl@4n]3Fr+OroH]F(r)CBB)nc12#
add S at position 8,Cc2+n(]=S4cSN5NCl@4n]3Fr+OroH]F(r)CBB)nc12#
remove B from position 35,Cc2+n(]=S4cSN5NCl@4n]3Fr+OroH]F(r)CB)nc12#
remove F from position 22,Cc2+n(]=S4cSN5NCl@4n]3r+OroH]F(r)CB)nc12#
replace 5 at position 13 with 3,Cc2+n(]=S4cSN3NCl@4n]3r+OroH]F(r)CB)nc12#
add C at position 11,Cc2+n(]=S4cCSN3NCl@4n]3r+OroH]F(r)CB)nc12#
replace r at position 32 with C,Cc2+n(]=S4cCSN3NCl@4n]3r+OroH]F(C)CB)nc12#
remove r from position 23,Cc2+n(]=S4cCSN3NCl@4n]3+OroH]F(C)CB)nc12#
replace F at position 29 with 3,Cc2+n(]=S4cCSN3NCl@4n]3+OroH]3(C)CB)nc12#
remove S from position 12,Cc2+n(]=S4cCN3NCl@4n]3+OroH]3(C)CB)nc12#
add ( at position 12,Cc2+n(]=S4cC(N3NCl@4n]3+OroH]3(C)CB)nc12#
add H at position 4,Cc2+Hn(]=S4cC(N3NCl@4n]3+OroH]3(C)CB)nc12#
replace H at position 4 with n,Cc2+nn(]=S4cC(N3NCl@4n]3+OroH]3(C)CB)nc12#
replace 4 at position 20 with ],Cc2+nn(]=S4cC(N3NCl@]n]3+OroH]3(C)CB)nc12#
add S at position 5,Cc2+nSn(]=S4cC(N3NCl@]n]3+OroH]3(C)CB)nc12#
replace 2 at position 2 with I,CcI+nSn(]=S4cC(N3NCl@]n]3+OroH]3(C)CB)nc12#
add N at position 3,CcIN+nSn(]=S4cC(N3NCl@]n]3+OroH]3(C)CB)nc12#
replace + at position 4 with #,CcIN#nSn(]=S4cC(N3NCl@]n]3+OroH]3(C)CB)nc12#
replace l at position 20 with [,CcIN#nSn(]=S4cC(N3NC[@]n]3+OroH]3(C)CB)nc12#
add ) at position 17,CcIN#nSn(]=S4cC(N)3NC[@]n]3+OroH]3(C)CB)nc12#
add B at position 10,CcIN#nSn(]B=S4cC(N)3NC[@]n]3+OroH]3(C)CB)nc12#
remove S from position 6,CcIN#nn(]B=S4cC(N)3NC[@]n]3+OroH]3(C)CB)nc12#
replace N at position 16 with O,CcIN#nn(]B=S4cC(O)3NC[@]n]3+OroH]3(C)CB)nc12#
replace n at position 24 with H,CcIN#nn(]B=S4cC(O)3NC[@]H]3+OroH]3(C)CB)nc12#
replace n at position 6 with 2,CcIN#n2(]B=S4cC(O)3NC[@]H]3+OroH]3(C)CB)nc12#
add c at position 7,CcIN#n2c(]B=S4cC(O)3NC[@]H]3+OroH]3(C)CB)nc12#
remove = from position 11,CcIN#n2c(]BS4cC(O)3NC[@]H]3+OroH]3(C)CB)nc12#
replace 3 at position 18 with O,CcIN#n2c(]BS4cC(O)ONC[@]H]3+OroH]3(C)CB)nc12#
add - at position 33,CcIN#n2c(]BS4cC(O)ONC[@]H]3+OroH]-3(C)CB)nc12#
replace B at position 10 with =,CcIN#n2c(]=S4cC(O)ONC[@]H]3+OroH]-3(C)CB)nc12#
replace o at position 30 with (,CcIN#n2c(]=S4cC(O)ONC[@]H]3+Or(H]-3(C)CB)nc12#
replace ( at position 30 with @,CcIN#n2c(]=S4cC(O)ONC[@]H]3+Or@H]-3(C)CB)nc12#
add 1 at position 37,CcIN#n2c(]=S4cC(O)ONC[@]H]3+Or@H]-3(C1)CB)nc12#
add C at position 22,CcIN#n2c(]=S4cC(O)ONC[C@]H]3+Or@H]-3(C1)CB)nc12#
replace I at position 2 with 1,Cc1N#n2c(]=S4cC(O)ONC[C@]H]3+Or@H]-3(C1)CB)nc12#
remove ] from position 9,Cc1N#n2c(=S4cC(O)ONC[C@]H]3+Or@H]-3(C1)CB)nc12#
replace 4 at position 11 with ),Cc1N#n2c(=S)cC(O)ONC[C@]H]3+Or@H]-3(C1)CB)nc12#
remove O from position 17,Cc1N#n2c(=S)cC(O)NC[C@]H]3+Or@H]-3(C1)CB)nc12#
add @ at position 29,Cc1N#n2c(=S)cC(O)NC[C@]H]3+Or@@H]-3(C1)CB)nc12#
remove - from position 33,Cc1N#n2c(=S)cC(O)NC[C@]H]3+Or@@H]3(C1)CB)nc12#
remove ] from position 22,Cc1N#n2c(=S)cC(O)NC[C@H]3+Or@@H]3(C1)CB)nc12#
remove 1 from position 35,Cc1N#n2c(=S)cC(O)NC[C@H]3+Or@@H]3(C)CB)nc12#
remove B from position 37,Cc1N#n2c(=S)cC(O)NC[C@H]3+Or@@H]3(C)C)nc12#
add O at position 10,Cc1N#n2c(=OS)cC(O)NC[C@H]3+Or@@H]3(C)C)nc12#
add r at position 30,Cc1N#n2c(=OS)cC(O)NC[C@H]3+Or@r@H]3(C)C)nc12#
replace # at position 4 with c,Cc1Ncn2c(=OS)cC(O)NC[C@H]3+Or@r@H]3(C)C)nc12#
add C at position 1,CCc1Ncn2c(=OS)cC(O)NC[C@H]3+Or@r@H]3(C)C)nc12#
replace r at position 29 with [,CCc1Ncn2c(=OS)cC(O)NC[C@H]3+O[@r@H]3(C)C)nc12#
add c at position 41,CCc1Ncn2c(=OS)cC(O)NC[C@H]3+O[@r@H]3(C)C)cnc12#
add 2 at position 22,CCc1Ncn2c(=OS)cC(O)NC[2C@H]3+O[@r@H]3(C)C)cnc12#
replace N at position 4 with c,CCc1ccn2c(=OS)cC(O)NC[2C@H]3+O[@r@H]3(C)C)cnc12#
add = at position 17,CCc1ccn2c(=OS)cC(=O)NC[2C@H]3+O[@r@H]3(C)C)cnc12#
add C at position 32,CCc1ccn2c(=OS)cC(=O)NC[2C@H]3+O[C@r@H]3(C)C)cnc12#
add ( at position 38,CCc1ccn2c(=OS)cC(=O)NC[2C@H]3+O[C@r@H](3(C)C)cnc12#
remove r from position 34,CCc1ccn2c(=OS)cC(=O)NC[2C@H]3+O[C@@H](3(C)C)cnc12#
add ( at position 15,CCc1ccn2c(=OS)c(C(=O)NC[2C@H]3+O[C@@H](3(C)C)cnc12#
remove C from position 0,Cc1ccn2c(=OS)c(C(=O)NC[2C@H]3+O[C@@H](3(C)C)cnc12#
remove S from position 11,Cc1ccn2c(=O)c(C(=O)NC[2C@H]3+O[C@@H](3(C)C)cnc12#
add C at position 29,Cc1ccn2c(=O)c(C(=O)NC[2C@H]3+CO[C@@H](3(C)C)cnc12#
add C at position 39,Cc1ccn2c(=O)c(C(=O)NC[2C@H]3+CO[C@@H](3C(C)C)cnc12#
remove # from position 50,Cc1ccn2c(=O)c(C(=O)NC[2C@H]3+CO[C@@H](3C(C)C)cnc12
remove 2 from position 22,Cc1ccn2c(=O)c(C(=O)NC[C@H]3+CO[C@@H](3C(C)C)cnc12
add c at position 5,Cc1cccn2c(=O)c(C(=O)NC[C@H]3+CO[C@@H](3C(C)C)cnc12
remove ( from position 37,Cc1cccn2c(=O)c(C(=O)NC[C@H]3+CO[C@@H]3C(C)C)cnc12
replace + at position 28 with C,Cc1cccn2c(=O)c(C(=O)NC[C@H]3CCO[C@@H]3C(C)C)cnc12
final: Cc1cccn2c(=O)c(C(=O)NC[C@H]3CCO[C@@H]3C(C)C)cnc12,Cc1cccn2c(=O)c(C(=O)NC[C@H]3CCO[C@@H]3C(C)C)cnc12
