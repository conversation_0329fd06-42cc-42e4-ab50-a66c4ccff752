log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 3,oFc3BH-C
remove c from position 2,oF3BH-<PERSON>
remove B from position 3,oF3H-<PERSON>
replace - at position 4 with ],oF3H]C
remove <PERSON> from position 5,oF3H]
add B at position 4,oF3HB]
add N at position 6,oF3HB]N
replace B at position 4 with -,oF3H-]N
add ) at position 7,oF3H-]N)
add [ at position 6,oF3H-][N)
add [ at position 2,oF[3H-][N)
add O at position 3,oF[O3H-][N)
replace [ at position 8 with 3,oF[O3H-]3N)
add ( at position 2,oF([O3H-]3N)
add 4 at position 2,oF4([O3H-]3N)
replace [ at position 4 with H,oF4(HO3H-]3N)
add ] at position 5,oF4(H]O3H-]3N)
add O at position 2,oFO4(H]O3H-]3N)
remove H from position 9,oFO4(H]O3-]3N)
add 6 at position 13,oFO4(H]O3-]3N6)
replace F at position 1 with n,onO4(H]O3-]3N6)
remove H from position 5,onO4(]O3-]3N6)
replace 4 at position 3 with r,onOr(]O3-]3N6)
add S at position 6,onOr(]SO3-]3N6)
add s at position 11,onOr(]SO3-]s3N6)
remove 6 from position 14,onOr(]SO3-]s3N)
replace ] at position 10 with H,onOr(]SO3-Hs3N)
remove O from position 2,onr(]SO3-Hs3N)
add ) at position 11,onr(]SO3-Hs)3N)
replace N at position 13 with /,onr(]SO3-Hs)3/)
add S at position 6,onr(]SSO3-Hs)3/)
add l at position 2,onlr(]SSO3-Hs)3/)
add F at position 11,onlr(]SSO3-FHs)3/)
replace o at position 0 with S,Snlr(]SSO3-FHs)3/)
remove S from position 7,Snlr(]SO3-FHs)3/)
add # at position 10,Snlr(]SO3-#FHs)3/)
remove l from position 2,Snr(]SO3-#FHs)3/)
add r at position 1,Srnr(]SO3-#FHs)3/)
add ) at position 12,Srnr(]SO3-#F)Hs)3/)
replace F at position 11 with 4,Srnr(]SO3-#4)Hs)3/)
add # at position 19,Srnr(]SO3-#4)Hs)3/)#
add 1 at position 2,Sr1nr(]SO3-#4)Hs)3/)#
add C at position 1,SCr1nr(]SO3-#4)Hs)3/)#
add C at position 21,SCr1nr(]SO3-#4)Hs)3/)C#
remove r from position 2,SC1nr(]SO3-#4)Hs)3/)C#
add B at position 14,SC1nr(]SO3-#4)BHs)3/)C#
add 2 at position 19,SC1nr(]SO3-#4)BHs)32/)C#
add o at position 17,SC1nr(]SO3-#4)BHso)32/)C#
replace - at position 10 with 6,SC1nr(]SO36#4)BHso)32/)C#
add r at position 12,SC1nr(]SO36#r4)BHso)32/)C#
replace C at position 24 with r,SC1nr(]SO36#r4)BHso)32/)r#
replace / at position 22 with 1,SC1nr(]SO36#r4)BHso)321)r#
remove s from position 17,SC1nr(]SO36#r4)BHo)321)r#
add c at position 19,SC1nr(]SO36#r4)BHo)c321)r#
replace r at position 4 with @,SC1n@(]SO36#r4)BHo)c321)r#
add / at position 21,SC1n@(]SO36#r4)BHo)c3/21)r#
remove B from position 15,SC1n@(]SO36#r4)Ho)c3/21)r#
add 2 at position 18,SC1n@(]SO36#r4)Ho)2c3/21)r#
replace S at position 0 with 7,7C1n@(]SO36#r4)Ho)2c3/21)r#
add ) at position 20,7C1n@(]SO36#r4)Ho)2c)3/21)r#
add l at position 11,7C1n@(]SO36l#r4)Ho)2c)3/21)r#
add ) at position 29,7C1n@(]SO36l#r4)Ho)2c)3/21)r#)
replace 3 at position 9 with =,7C1n@(]SO=6l#r4)Ho)2c)3/21)r#)
replace ) at position 29 with 7,7C1n@(]SO=6l#r4)Ho)2c)3/21)r#7
add = at position 13,7C1n@(]SO=6l#=r4)Ho)2c)3/21)r#7
add r at position 20,7C1n@(]SO=6l#=r4)Ho)r2c)3/21)r#7
remove 1 from position 2,7Cn@(]SO=6l#=r4)Ho)r2c)3/21)r#7
remove 7 from position 0,Cn@(]SO=6l#=r4)Ho)r2c)3/21)r#7
replace 6 at position 8 with H,Cn@(]SO=Hl#=r4)Ho)r2c)3/21)r#7
add + at position 3,Cn@+(]SO=Hl#=r4)Ho)r2c)3/21)r#7
remove H from position 16,Cn@+(]SO=Hl#=r4)o)r2c)3/21)r#7
replace 4 at position 14 with +,Cn@+(]SO=Hl#=r+)o)r2c)3/21)r#7
add ] at position 10,Cn@+(]SO=H]l#=r+)o)r2c)3/21)r#7
add 4 at position 13,Cn@+(]SO=H]l#4=r+)o)r2c)3/21)r#7
replace ) at position 17 with c,Cn@+(]SO=H]l#4=r+co)r2c)3/21)r#7
add ( at position 32,Cn@+(]SO=H]l#4=r+co)r2c)3/21)r#7(
add 4 at position 18,Cn@+(]SO=H]l#4=r+c4o)r2c)3/21)r#7(
add 6 at position 21,Cn@+(]SO=H]l#4=r+c4o)6r2c)3/21)r#7(
replace 4 at position 18 with r,Cn@+(]SO=H]l#4=r+cro)6r2c)3/21)r#7(
replace 6 at position 21 with l,Cn@+(]SO=H]l#4=r+cro)lr2c)3/21)r#7(
remove ) from position 30,Cn@+(]SO=H]l#4=r+cro)lr2c)3/21r#7(
add 3 at position 6,Cn@+(]3SO=H]l#4=r+cro)lr2c)3/21r#7(
add ) at position 29,Cn@+(]3SO=H]l#4=r+cro)lr2c)3/)21r#7(
add ) at position 30,Cn@+(]3SO=H]l#4=r+cro)lr2c)3/))21r#7(
remove r from position 33,Cn@+(]3SO=H]l#4=r+cro)lr2c)3/))21#7(
add c at position 22,Cn@+(]3SO=H]l#4=r+cro)clr2c)3/))21#7(
add F at position 23,Cn@+(]3SO=H]l#4=r+cro)cFlr2c)3/))21#7(
remove / from position 30,Cn@+(]3SO=H]l#4=r+cro)cFlr2c)3))21#7(
add B at position 30,Cn@+(]3SO=H]l#4=r+cro)cFlr2c)3B))21#7(
add r at position 19,Cn@+(]3SO=H]l#4=r+crro)cFlr2c)3B))21#7(
add n at position 4,Cn@+n(]3SO=H]l#4=r+crro)cFlr2c)3B))21#7(
replace = at position 10 with 5,Cn@+n(]3SO5H]l#4=r+crro)cFlr2c)3B))21#7(
remove 2 from position 28,Cn@+n(]3SO5H]l#4=r+crro)cFlrc)3B))21#7(
remove 7 from position 37,Cn@+n(]3SO5H]l#4=r+crro)cFlrc)3B))21#(
add ) at position 17,Cn@+n(]3SO5H]l#4=)r+crro)cFlrc)3B))21#(
add = at position 7,Cn@+n(]=3SO5H]l#4=)r+crro)cFlrc)3B))21#(
add c at position 36,Cn@+n(]=3SO5H]l#4=)r+crro)cFlrc)3B))c21#(
replace = at position 17 with F,Cn@+n(]=3SO5H]l#4F)r+crro)cFlrc)3B))c21#(
replace ) at position 34 with B,Cn@+n(]=3SO5H]l#4F)r+crro)cFlrc)3BB)c21#(
add c at position 36,Cn@+n(]=3SO5H]l#4F)r+crro)cFlrc)3BB)cc21#(
add c at position 19,Cn@+n(]=3SO5H]l#4F)cr+crro)cFlrc)3BB)cc21#(
replace F at position 17 with n,Cn@+n(]=3SO5H]l#4n)cr+crro)cFlrc)3BB)cc21#(
remove r from position 24,Cn@+n(]=3SO5H]l#4n)cr+cro)cFlrc)3BB)cc21#(
add F at position 20,Cn@+n(]=3SO5H]l#4n)cFr+cro)cFlrc)3BB)cc21#(
replace @ at position 2 with 2,Cn2+n(]=3SO5H]l#4n)cFr+cro)cFlrc)3BB)cc21#(
add O at position 9,Cn2+n(]=3OSO5H]l#4n)cFr+cro)cFlrc)3BB)cc21#(
remove ( from position 43,Cn2+n(]=3OSO5H]l#4n)cFr+cro)cFlrc)3BB)cc21#
add r at position 10,Cn2+n(]=3OrSO5H]l#4n)cFr+cro)cFlrc)3BB)cc21#
remove ) from position 34,Cn2+n(]=3OrSO5H]l#4n)cFr+cro)cFlrc3BB)cc21#
remove r from position 10,Cn2+n(]=3OSO5H]l#4n)cFr+cro)cFlrc3BB)cc21#
add S at position 8,Cn2+n(]=S3OSO5H]l#4n)cFr+cro)cFlrc3BB)cc21#
remove B from position 35,Cn2+n(]=S3OSO5H]l#4n)cFr+cro)cFlrc3B)cc21#
remove F from position 22,Cn2+n(]=S3OSO5H]l#4n)cr+cro)cFlrc3B)cc21#
replace 5 at position 13 with 3,Cn2+n(]=S3OSO3H]l#4n)cr+cro)cFlrc3B)cc21#
add N at position 11,Cn2+n(]=S3ONSO3H]l#4n)cr+cro)cFlrc3B)cc21#
replace r at position 32 with ),Cn2+n(]=S3ONSO3H]l#4n)cr+cro)cFl)c3B)cc21#
remove r from position 23,Cn2+n(]=S3ONSO3H]l#4n)c+cro)cFl)c3B)cc21#
replace F at position 29 with (,Cn2+n(]=S3ONSO3H]l#4n)c+cro)c(l)c3B)cc21#
remove S from position 12,Cn2+n(]=S3ONO3H]l#4n)c+cro)c(l)c3B)cc21#
add [ at position 12,Cn2+n(]=S3ON[O3H]l#4n)c+cro)c(l)c3B)cc21#
add I at position 4,Cn2+In(]=S3ON[O3H]l#4n)c+cro)c(l)c3B)cc21#
replace I at position 4 with 2,Cn2+2n(]=S3ON[O3H]l#4n)c+cro)c(l)c3B)cc21#
replace 4 at position 20 with ],Cn2+2n(]=S3ON[O3H]l#]n)c+cro)c(l)c3B)cc21#
add S at position 5,Cn2+2Sn(]=S3ON[O3H]l#]n)c+cro)c(l)c3B)cc21#
replace 2 at position 2 with I,CnI+2Sn(]=S3ON[O3H]l#]n)c+cro)c(l)c3B)cc21#
add N at position 3,CnIN+2Sn(]=S3ON[O3H]l#]n)c+cro)c(l)c3B)cc21#
replace + at position 4 with #,CnIN#2Sn(]=S3ON[O3H]l#]n)c+cro)c(l)c3B)cc21#
replace l at position 20 with (,CnIN#2Sn(]=S3ON[O3H](#]n)c+cro)c(l)c3B)cc21#
add @ at position 17,CnIN#2Sn(]=S3ON[O@3H](#]n)c+cro)c(l)c3B)cc21#
add @ at position 10,CnIN#2Sn(]@=S3ON[O@3H](#]n)c+cro)c(l)c3B)cc21#
remove S from position 6,CnIN#2n(]@=S3ON[O@3H](#]n)c+cro)c(l)c3B)cc21#
replace O at position 16 with @,CnIN#2n(]@=S3ON[@@3H](#]n)c+cro)c(l)c3B)cc21#
replace n at position 24 with N,CnIN#2n(]@=S3ON[@@3H](#]N)c+cro)c(l)c3B)cc21#
replace n at position 6 with c,CnIN#2c(]@=S3ON[@@3H](#]N)c+cro)c(l)c3B)cc21#
add c at position 7,CnIN#2cc(]@=S3ON[@@3H](#]N)c+cro)c(l)c3B)cc21#
remove = from position 11,CnIN#2cc(]@S3ON[@@3H](#]N)c+cro)c(l)c3B)cc21#
replace 3 at position 18 with O,CnIN#2cc(]@S3ON[@@OH](#]N)c+cro)c(l)c3B)cc21#
add - at position 33,CnIN#2cc(]@S3ON[@@OH](#]N)c+cro)c-(l)c3B)cc21#
replace @ at position 10 with C,CnIN#2cc(]CS3ON[@@OH](#]N)c+cro)c-(l)c3B)cc21#
replace o at position 30 with (,CnIN#2cc(]CS3ON[@@OH](#]N)c+cr()c-(l)c3B)cc21#
replace ( at position 30 with l,CnIN#2cc(]CS3ON[@@OH](#]N)c+crl)c-(l)c3B)cc21#
add 1 at position 37,CnIN#2cc(]CS3ON[@@OH](#]N)c+crl)c-(l)1c3B)cc21#
add C at position 22,CnIN#2cc(]CS3ON[@@OH](C#]N)c+crl)c-(l)1c3B)cc21#
replace I at position 2 with 1,Cn1N#2cc(]CS3ON[@@OH](C#]N)c+crl)c-(l)1c3B)cc21#
remove ] from position 9,Cn1N#2cc(CS3ON[@@OH](C#]N)c+crl)c-(l)1c3B)cc21#
replace 3 at position 11 with =,Cn1N#2cc(CS=ON[@@OH](C#]N)c+crl)c-(l)1c3B)cc21#
remove O from position 17,Cn1N#2cc(CS=ON[@@H](C#]N)c+crl)c-(l)1c3B)cc21#
add C at position 29,Cn1N#2cc(CS=ON[@@H](C#]N)c+crCl)c-(l)1c3B)cc21#
remove - from position 33,Cn1N#2cc(CS=ON[@@H](C#]N)c+crCl)c(l)1c3B)cc21#
remove ] from position 22,Cn1N#2cc(CS=ON[@@H](C#N)c+crCl)c(l)1c3B)cc21#
remove 1 from position 35,Cn1N#2cc(CS=ON[@@H](C#N)c+crCl)c(l)c3B)cc21#
remove B from position 37,Cn1N#2cc(CS=ON[@@H](C#N)c+crCl)c(l)c3)cc21#
add ( at position 10,Cn1N#2cc(C(S=ON[@@H](C#N)c+crCl)c(l)c3)cc21#
add r at position 30,Cn1N#2cc(C(S=ON[@@H](C#N)c+crCrl)c(l)c3)cc21#
replace # at position 4 with n,Cn1Nn2cc(C(S=ON[@@H](C#N)c+crCrl)c(l)c3)cc21#
add C at position 1,CCn1Nn2cc(C(S=ON[@@H](C#N)c+crCrl)c(l)c3)cc21#
replace r at position 29 with c,CCn1Nn2cc(C(S=ON[@@H](C#N)c+ccCrl)c(l)c3)cc21#
add c at position 41,CCn1Nn2cc(C(S=ON[@@H](C#N)c+ccCrl)c(l)c3)ccc21#
add 2 at position 22,CCn1Nn2cc(C(S=ON[@@H](2C#N)c+ccCrl)c(l)c3)ccc21#
replace N at position 4 with n,CCn1nn2cc(C(S=ON[@@H](2C#N)c+ccCrl)c(l)c3)ccc21#
add C at position 17,CCn1nn2cc(C(S=ON[C@@H](2C#N)c+ccCrl)c(l)c3)ccc21#
add ( at position 32,CCn1nn2cc(C(S=ON[C@@H](2C#N)c+cc(Crl)c(l)c3)ccc21#
add ( at position 38,CCn1nn2cc(C(S=ON[C@@H](2C#N)c+cc(Crl)c((l)c3)ccc21#
remove r from position 34,CCn1nn2cc(C(S=ON[C@@H](2C#N)c+cc(Cl)c((l)c3)ccc21#
add ) at position 15,CCn1nn2cc(C(S=O)N[C@@H](2C#N)c+cc(Cl)c((l)c3)ccc21#
remove C from position 0,Cn1nn2cc(C(S=O)N[C@@H](2C#N)c+cc(Cl)c((l)c3)ccc21#
remove S from position 11,Cn1nn2cc(C(=O)N[C@@H](2C#N)c+cc(Cl)c((l)c3)ccc21#
add c at position 29,Cn1nn2cc(C(=O)N[C@@H](2C#N)c+ccc(Cl)c((l)c3)ccc21#
add C at position 39,Cn1nn2cc(C(=O)N[C@@H](2C#N)c+ccc(Cl)c((Cl)c3)ccc21#
remove # from position 50,Cn1nn2cc(C(=O)N[C@@H](2C#N)c+ccc(Cl)c((Cl)c3)ccc21
remove 2 from position 22,Cn1nn2cc(C(=O)N[C@@H](C#N)c+ccc(Cl)c((Cl)c3)ccc21
add c at position 5,Cn1nnc2cc(C(=O)N[C@@H](C#N)c+ccc(Cl)c((Cl)c3)ccc21
remove ( from position 37,Cn1nnc2cc(C(=O)N[C@@H](C#N)c+ccc(Cl)c(Cl)c3)ccc21
replace + at position 28 with 3,Cn1nnc2cc(C(=O)N[C@@H](C#N)c3ccc(Cl)c(Cl)c3)ccc21
final: Cn1nnc2cc(C(=O)N[C@@H](C#N)c3ccc(Cl)c(Cl)c3)ccc21,Cn1nnc2cc(C(=O)N[C@@H](C#N)c3ccc(Cl)c(Cl)c3)ccc21
