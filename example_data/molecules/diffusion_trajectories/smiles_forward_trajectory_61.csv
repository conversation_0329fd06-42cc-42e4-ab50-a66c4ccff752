log,state
initialize: Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)cs1,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)cs1
replace = at position 28 with +,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)+O)cs1
add O at position 2,CcO1nc(CCC[NH+]2CCC[C@H]2C(N)+O)cs1
remove ( from position 6,CcO1ncCCC[NH+]2CCC[C@H]2C(N)+O)cs1
replace C at position 15 with #,CcO1ncCCC[NH+]2#CC[C@H]2C(N)+O)cs1
add 6 at position 1,C6cO1ncCCC[NH+]2#CC[C@H]2C(N)+O)cs1
remove O from position 30,C6cO1ncCCC[NH+]2#CC[C@H]2C(N)+)cs1
remove C from position 9,C6cO1ncCC[NH+]2#CC[C@H]2C(N)+)cs1
add n at position 7,C6cO1ncnCC[NH+]2#CC[C@H]2C(N)+)cs1
add o at position 31,C6cO1ncnCC[NH+]2#CC[C@H]2C(N)+)ocs1
replace 1 at position 4 with S,C6cOSncnCC[NH+]2#CC[C@H]2C(N)+)ocs1
remove + from position 29,C6cOSncnCC[NH+]2#CC[C@H]2C(N))ocs1
add C at position 5,C6cOSCncnCC[NH+]2#CC[C@H]2C(N))ocs1
remove # from position 17,C6cOSCncnCC[NH+]2CC[C@H]2C(N))ocs1
replace S at position 4 with N,C6cONCncnCC[NH+]2CC[C@H]2C(N))ocs1
remove H from position 22,C6cONCncnCC[NH+]2CC[C@]2C(N))ocs1
remove N from position 12,C6cONCncnCC[H+]2CC[C@]2C(N))ocs1
remove 6 from position 1,CcONCncnCC[H+]2CC[C@]2C(N))ocs1
replace O at position 2 with #,Cc#NCncnCC[H+]2CC[C@]2C(N))ocs1
remove C from position 22,Cc#NCncnCC[H+]2CC[C@]2(N))ocs1
remove + from position 12,Cc#NCncnCC[H]2CC[C@]2(N))ocs1
add H at position 2,CcH#NCncnCC[H]2CC[C@]2(N))ocs1
remove H from position 2,Cc#NCncnCC[H]2CC[C@]2(N))ocs1
remove C from position 4,Cc#NncnCC[H]2CC[C@]2(N))ocs1
add ] at position 11,Cc#NncnCC[H]]2CC[C@]2(N))ocs1
add - at position 16,Cc#NncnCC[H]]2CC-[C@]2(N))ocs1
remove C from position 14,Cc#NncnCC[H]]2C-[C@]2(N))ocs1
add O at position 8,Cc#NncnCOC[H]]2C-[C@]2(N))ocs1
replace c at position 5 with 3,Cc#Nn3nCOC[H]]2C-[C@]2(N))ocs1
add n at position 30,Cc#Nn3nCOC[H]]2C-[C@]2(N))ocs1n
add ( at position 29,Cc#Nn3nCOC[H]]2C-[C@]2(N))ocs(1n
remove ( from position 22,Cc#Nn3nCOC[H]]2C-[C@]2N))ocs(1n
remove C from position 18,Cc#Nn3nCOC[H]]2C-[@]2N))ocs(1n
replace C at position 15 with (,Cc#Nn3nCOC[H]]2(-[@]2N))ocs(1n
replace ( at position 15 with o,Cc#Nn3nCOC[H]]2o-[@]2N))ocs(1n
replace 1 at position 28 with /,Cc#Nn3nCOC[H]]2o-[@]2N))ocs(/n
remove o from position 24,Cc#Nn3nCOC[H]]2o-[@]2N))cs(/n
remove 2 from position 14,Cc#Nn3nCOC[H]]o-[@]2N))cs(/n
remove N from position 3,Cc#n3nCOC[H]]o-[@]2N))cs(/n
replace o at position 13 with =,Cc#n3nCOC[H]]=-[@]2N))cs(/n
replace @ at position 16 with +,Cc#n3nCOC[H]]=-[+]2N))cs(/n
replace [ at position 15 with 7,Cc#n3nCOC[H]]=-7+]2N))cs(/n
replace ) at position 21 with 4,Cc#n3nCOC[H]]=-7+]2N)4cs(/n
remove ] from position 11,Cc#n3nCOC[H]=-7+]2N)4cs(/n
add c at position 22,Cc#n3nCOC[H]=-7+]2N)4ccs(/n
replace N at position 18 with s,Cc#n3nCOC[H]=-7+]2s)4ccs(/n
replace 3 at position 4 with o,Cc#nonCOC[H]=-7+]2s)4ccs(/n
replace s at position 23 with 5,Cc#nonCOC[H]=-7+]2s)4cc5(/n
replace 7 at position 14 with ],Cc#nonCOC[H]=-]+]2s)4cc5(/n
add 1 at position 12,Cc#nonCOC[H]1=-]+]2s)4cc5(/n
add c at position 9,Cc#nonCOCc[H]1=-]+]2s)4cc5(/n
add l at position 16,Cc#nonCOCc[H]1=-l]+]2s)4cc5(/n
add s at position 29,Cc#nonCOCc[H]1=-l]+]2s)4cc5(/sn
remove [ from position 10,Cc#nonCOCcH]1=-l]+]2s)4cc5(/sn
remove - from position 14,Cc#nonCOCcH]1=l]+]2s)4cc5(/sn
add n at position 8,Cc#nonCOnCcH]1=l]+]2s)4cc5(/sn
add s at position 13,Cc#nonCOnCcH]s1=l]+]2s)4cc5(/sn
add 1 at position 25,Cc#nonCOnCcH]s1=l]+]2s)4c1c5(/sn
remove / from position 29,Cc#nonCOnCcH]s1=l]+]2s)4c1c5(sn
remove c from position 26,Cc#nonCOnCcH]s1=l]+]2s)4c15(sn
replace n at position 29 with o,Cc#nonCOnCcH]s1=l]+]2s)4c15(so
add S at position 28,Cc#nonCOnCcH]s1=l]+]2s)4c15(Sso
remove o from position 30,Cc#nonCOnCcH]s1=l]+]2s)4c15(Ss
remove C from position 9,Cc#nonCOncH]s1=l]+]2s)4c15(Ss
add F at position 11,Cc#nonCOncHF]s1=l]+]2s)4c15(Ss
add ] at position 29,Cc#nonCOncHF]s1=l]+]2s)4c15(S]s
replace = at position 15 with 5,Cc#nonCOncHF]s15l]+]2s)4c15(S]s
add I at position 3,Cc#InonCOncHF]s15l]+]2s)4c15(S]s
add l at position 9,Cc#InonCOlncHF]s15l]+]2s)4c15(S]s
remove + from position 20,Cc#InonCOlncHF]s15l]]2s)4c15(S]s
add N at position 3,Cc#NInonCOlncHF]s15l]]2s)4c15(S]s
add r at position 9,Cc#NInonCrOlncHF]s15l]]2s)4c15(S]s
add 7 at position 27,Cc#NInonCrOlncHF]s15l]]2s)47c15(S]s
remove c from position 13,Cc#NInonCrOlnHF]s15l]]2s)47c15(S]s
replace ) at position 24 with s,Cc#NInonCrOlnHF]s15l]]2ss47c15(S]s
replace 1 at position 17 with H,Cc#NInonCrOlnHF]sH5l]]2ss47c15(S]s
remove l from position 19,Cc#NInonCrOlnHF]sH5]]2ss47c15(S]s
remove r from position 9,Cc#NInonCOlnHF]sH5]]2ss47c15(S]s
replace n at position 5 with ],Cc#NI]onCOlnHF]sH5]]2ss47c15(S]s
remove 1 from position 26,Cc#NI]onCOlnHF]sH5]]2ss47c5(S]s
remove ] from position 18,Cc#NI]onCOlnHF]sH5]2ss47c5(S]s
remove N from position 3,Cc#I]onCOlnHF]sH5]2ss47c5(S]s
remove O from position 8,Cc#I]onClnHF]sH5]2ss47c5(S]s
add l at position 22,Cc#I]onClnHF]sH5]2ss47lc5(S]s
remove c from position 1,C#I]onClnHF]sH5]2ss47lc5(S]s
remove ] from position 26,C#I]onClnHF]sH5]2ss47lc5(Ss
remove ( from position 24,C#I]onClnHF]sH5]2ss47lc5Ss
remove s from position 12,C#I]onClnHF]H5]2ss47lc5Ss
remove I from position 2,C#]onClnHF]H5]2ss47lc5Ss
remove F from position 9,C#]onClnH]H5]2ss47lc5Ss
remove S from position 21,C#]onClnH]H5]2ss47lc5s
replace C at position 5 with 7,C#]on7lnH]H5]2ss47lc5s
remove 7 from position 5,C#]onlnH]H5]2ss47lc5s
remove ] from position 11,C#]onlnH]H52ss47lc5s
remove 2 from position 11,C#]onlnH]H5ss47lc5s
add r at position 16,C#]onlnH]H5ss47lrc5s
remove l from position 15,C#]onlnH]H5ss47rc5s
remove 7 from position 14,C#]onlnH]H5ss4rc5s
remove o from position 3,C#]nlnH]H5ss4rc5s
add ) at position 15,C#]nlnH]H5ss4rc)5s
replace s at position 10 with 6,C#]nlnH]H56s4rc)5s
replace 5 at position 9 with 4,C#]nlnH]H46s4rc)5s
remove 6 from position 10,C#]nlnH]H4s4rc)5s
remove 4 from position 9,C#]nlnH]Hs4rc)5s
remove H from position 8,C#]nlnH]s4rc)5s
add 5 at position 6,C#]nln5H]s4rc)5s
remove 4 from position 10,C#]nln5H]src)5s
remove # from position 1,C]nln5H]src)5s
replace r at position 9 with H,C]nln5H]sHc)5s
remove s from position 13,C]nln5H]sHc)5
add - at position 5,C]nln-5H]sHc)5
replace 5 at position 6 with (,C]nln-(H]sHc)5
remove C from position 0,]nln-(H]sHc)5
replace c at position 10 with -,]nln-(H]sH-)5
replace s at position 8 with 6,]nln-(H]6H-)5
remove 6 from position 8,]nln-(H]H-)5
replace - at position 4 with 4,]nln4(H]H-)5
remove - from position 9,]nln4(H]H)5
add ) at position 8,]nln4(H])H)5
remove ] from position 0,nln4(H])H)5
remove H from position 5,nln4(])H)5
replace 4 at position 3 with /,nln/(])H)5
remove n from position 2,nl/(])H)5
replace l at position 1 with -,n-/(])H)5
remove ( from position 3,n-/])H)5
remove ) from position 6,n-/])H5
replace / at position 2 with -,n--])H5
remove ) from position 4,n--]H5
remove H from position 4,n--]5
remove ] from position 3,n--5
add r at position 0,rn--5
remove - from position 3,rn-5
replace r at position 0 with 5,5n-5
add 6 at position 1,56n-5
replace 5 at position 4 with [,56n-[
replace [ at position 4 with B,56n-B
replace 5 at position 0 with B,B6n-B
replace n at position 2 with =,B6=-B
replace 6 at position 1 with C,BC=-B
remove C from position 1,B=-B
remove B from position 0,=-B
replace - at position 1 with I,=IB
remove = from position 0,IB
add 1 at position 0,1IB
replace B at position 2 with N,1IN
remove 1 from position 0,IN
remove I from position 0,N
remove N from position 0,
final: ,
