log,state
initialize: Nc1cc(=O)[nH]c(SCC(=O)Nc2nc(-c3ccc(Br)cc3)cs2)n1,Nc1cc(=O)[nH]c(SCC(=O)Nc2nc(-c3ccc(Br)cc3)cs2)n1
replace - at position 28 with +,Nc1cc(=O)[nH]c(SCC(=O)Nc2nc(+c3ccc(Br)cc3)cs2)n1
add ( at position 37,Nc1cc(=O)[nH]c(SCC(=O)Nc2nc(+c3ccc(Br()cc3)cs2)n1
remove ( from position 5,Nc1cc=O)[nH]c(SCC(=O)Nc2nc(+c3ccc(Br()cc3)cs2)n1
add 2 at position 22,Nc1cc=O)[nH]c(SCC(=O)N2c2nc(+c3ccc(Br()cc3)cs2)n1
add 6 at position 1,N6c1cc=O)[nH]c(SCC(=O)N2c2nc(+c3ccc(Br()cc3)cs2)n1
remove c from position 30,N6c1cc=O)[nH]c(SCC(=O)N2c2nc(+3ccc(Br()cc3)cs2)n1
remove [ from position 9,N6c1cc=O)nH]c(SCC(=O)N2c2nc(+3ccc(Br()cc3)cs2)n1
add + at position 45,N6c1cc=O)nH]c(SCC(=O)N2c2nc(+3ccc(Br()cc3)cs2+)n1
replace c at position 32 with B,N6c1cc=O)nH]c(SCC(=O)N2c2nc(+3ccB(Br()cc3)cs2+)n1
replace C at position 15 with ),N6c1cc=O)nH]c(S)C(=O)N2c2nc(+3ccB(Br()cc3)cs2+)n1
remove 3 from position 29,N6c1cc=O)nH]c(S)C(=O)N2c2nc(+ccB(Br()cc3)cs2+)n1
add ) at position 43,N6c1cc=O)nH]c(S)C(=O)N2c2nc(+ccB(Br()cc3)cs)2+)n1
remove c from position 2,N61cc=O)nH]c(S)C(=O)N2c2nc(+ccB(Br()cc3)cs)2+)n1
remove ) from position 35,N61cc=O)nH]c(S)C(=O)N2c2nc(+ccB(Br(cc3)cs)2+)n1
add B at position 19,N61cc=O)nH]c(S)C(=OB)N2c2nc(+ccB(Br(cc3)cs)2+)n1
replace = at position 17 with 2,N61cc=O)nH]c(S)C(2OB)N2c2nc(+ccB(Br(cc3)cs)2+)n1
remove ( from position 12,N61cc=O)nH]cS)C(2OB)N2c2nc(+ccB(Br(cc3)cs)2+)n1
remove r from position 33,N61cc=O)nH]cS)C(2OB)N2c2nc(+ccB(B(cc3)cs)2+)n1
remove B from position 32,N61cc=O)nH]cS)C(2OB)N2c2nc(+ccB((cc3)cs)2+)n1
add - at position 21,N61cc=O)nH]cS)C(2OB)N-2c2nc(+ccB((cc3)cs)2+)n1
remove + from position 42,N61cc=O)nH]cS)C(2OB)N-2c2nc(+ccB((cc3)cs)2)n1
remove c from position 3,N61c=O)nH]cS)C(2OB)N-2c2nc(+ccB((cc3)cs)2)n1
add / at position 31,N61c=O)nH]cS)C(2OB)N-2c2nc(+ccB/((cc3)cs)2)n1
add - at position 13,N61c=O)nH]cS)-C(2OB)N-2c2nc(+ccB/((cc3)cs)2)n1
add ] at position 22,N61c=O)nH]cS)-C(2OB)N-]2c2nc(+ccB/((cc3)cs)2)n1
add - at position 33,N61c=O)nH]cS)-C(2OB)N-]2c2nc(+ccB-/((cc3)cs)2)n1
remove + from position 29,N61c=O)nH]cS)-C(2OB)N-]2c2nc(ccB-/((cc3)cs)2)n1
add O at position 17,N61c=O)nH]cS)-C(2OOB)N-]2c2nc(ccB-/((cc3)cs)2)n1
replace S at position 11 with 3,N61c=O)nH]c3)-C(2OOB)N-]2c2nc(ccB-/((cc3)cs)2)n1
add ] at position 9,N61c=O)nH]]c3)-C(2OOB)N-]2c2nc(ccB-/((cc3)cs)2)n1
replace 1 at position 2 with I,N6Ic=O)nH]]c3)-C(2OOB)N-]2c2nc(ccB-/((cc3)cs)2)n1
remove N from position 22,N6Ic=O)nH]]c3)-C(2OOB)-]2c2nc(ccB-/((cc3)cs)2)n1
remove c from position 37,N6Ic=O)nH]]c3)-C(2OOB)-]2c2nc(ccB-/((c3)cs)2)n1
replace c at position 30 with (,N6Ic=O)nH]]c3)-C(2OOB)-]2c2nc((cB-/((c3)cs)2)n1
replace ( at position 30 with o,N6Ic=O)nH]]c3)-C(2OOB)-]2c2nc(ocB-/((c3)cs)2)n1
replace ] at position 10 with @,N6Ic=O)nH]@c3)-C(2OOB)-]2c2nc(ocB-/((c3)cs)2)n1
remove - from position 33,N6Ic=O)nH]@c3)-C(2OOB)-]2c2nc(ocB/((c3)cs)2)n1
replace O at position 18 with 3,N6Ic=O)nH]@c3)-C(23OB)-]2c2nc(ocB/((c3)cs)2)n1
add = at position 11,N6Ic=O)nH]@=c3)-C(23OB)-]2c2nc(ocB/((c3)cs)2)n1
remove n from position 7,N6Ic=O)H]@=c3)-C(23OB)-]2c2nc(ocB/((c3)cs)2)n1
replace ) at position 6 with n,N6Ic=OnH]@=c3)-C(23OB)-]2c2nc(ocB/((c3)cs)2)n1
replace 2 at position 24 with n,N6Ic=OnH]@=c3)-C(23OB)-]nc2nc(ocB/((c3)cs)2)n1
replace ( at position 16 with O,N6Ic=OnH]@=c3)-CO23OB)-]nc2nc(ocB/((c3)cs)2)n1
add S at position 6,N6Ic=OSnH]@=c3)-CO23OB)-]nc2nc(ocB/((c3)cs)2)n1
remove @ from position 10,N6Ic=OSnH]=c3)-CO23OB)-]nc2nc(ocB/((c3)cs)2)n1
remove 2 from position 17,N6Ic=OSnH]=c3)-CO3OB)-]nc2nc(ocB/((c3)cs)2)n1
replace ) at position 20 with l,N6Ic=OSnH]=c3)-CO3OBl-]nc2nc(ocB/((c3)cs)2)n1
replace = at position 4 with ),N6Ic)OSnH]=c3)-CO3OBl-]nc2nc(ocB/((c3)cs)2)n1
remove c from position 3,N6I)OSnH]=c3)-CO3OBl-]nc2nc(ocB/((c3)cs)2)n1
replace I at position 2 with 2,N62)OSnH]=c3)-CO3OBl-]nc2nc(ocB/((c3)cs)2)n1
remove S from position 5,N62)OnH]=c3)-CO3OBl-]nc2nc(ocB/((c3)cs)2)n1
replace ] at position 20 with 4,N62)OnH]=c3)-CO3OBl-4nc2nc(ocB/((c3)cs)2)n1
replace O at position 4 with H,N62)HnH]=c3)-CO3OBl-4nc2nc(ocB/((c3)cs)2)n1
remove H from position 4,N62)nH]=c3)-CO3OBl-4nc2nc(ocB/((c3)cs)2)n1
remove C from position 12,N62)nH]=c3)-O3OBl-4nc2nc(ocB/((c3)cs)2)n1
add S at position 12,N62)nH]=c3)-SO3OBl-4nc2nc(ocB/((c3)cs)2)n1
replace / at position 29 with F,N62)nH]=c3)-SO3OBl-4nc2nc(ocBF((c3)cs)2)n1
add r at position 23,N62)nH]=c3)-SO3OBl-4nc2rnc(ocBF((c3)cs)2)n1
replace ( at position 32 with r,N62)nH]=c3)-SO3OBl-4nc2rnc(ocBF(rc3)cs)2)n1
remove - from position 11,N62)nH]=c3)SO3OBl-4nc2rnc(ocBF(rc3)cs)2)n1
replace 3 at position 13 with 5,N62)nH]=c3)SO5OBl-4nc2rnc(ocBF(rc3)cs)2)n1
add F at position 22,N62)nH]=c3)SO5OBl-4nc2Frnc(ocBF(rc3)cs)2)n1
add B at position 35,N62)nH]=c3)SO5OBl-4nc2Frnc(ocBF(rc3B)cs)2)n1
remove c from position 8,N62)nH]=3)SO5OBl-4nc2Frnc(ocBF(rc3B)cs)2)n1
add r at position 10,N62)nH]=3)rSO5OBl-4nc2Frnc(ocBF(rc3B)cs)2)n1
add ) at position 34,N62)nH]=3)rSO5OBl-4nc2Frnc(ocBF(rc)3B)cs)2)n1
remove r from position 10,N62)nH]=3)SO5OBl-4nc2Frnc(ocBF(rc)3B)cs)2)n1
add ( at position 43,N62)nH]=3)SO5OBl-4nc2Frnc(ocBF(rc)3B)cs)2)n(1
remove ) from position 9,N62)nH]=3SO5OBl-4nc2Frnc(ocBF(rc)3B)cs)2)n(1
replace 2 at position 2 with @,N6@)nH]=3SO5OBl-4nc2Frnc(ocBF(rc)3B)cs)2)n(1
remove F from position 20,N6@)nH]=3SO5OBl-4nc2rnc(ocBF(rc)3B)cs)2)n(1
add 7 at position 43,N6@)nH]=3SO5OBl-4nc2rnc(ocBF(rc)3B)cs)2)n(17
replace n at position 17 with F,N6@)nH]=3SO5OBl-4Fc2rnc(ocBF(rc)3B)cs)2)n(17
remove 2 from position 19,N6@)nH]=3SO5OBl-4Fcrnc(ocBF(rc)3B)cs)2)n(17
remove ) from position 36,N6@)nH]=3SO5OBl-4Fcrnc(ocBF(rc)3B)cs2)n(17
replace c at position 34 with ),N6@)nH]=3SO5OBl-4Fcrnc(ocBF(rc)3B))s2)n(17
replace F at position 17 with =,N6@)nH]=3SO5OBl-4=crnc(ocBF(rc)3B))s2)n(17
remove 2 from position 36,N6@)nH]=3SO5OBl-4=crnc(ocBF(rc)3B))s)n(17
remove = from position 7,N6@)nH]3SO5OBl-4=crnc(ocBF(rc)3B))s)n(17
remove c from position 17,N6@)nH]3SO5OBl-4=rnc(ocBF(rc)3B))s)n(17
add 7 at position 37,N6@)nH]3SO5OBl-4=rnc(ocBF(rc)3B))s)n(717
add 2 at position 28,N6@)nH]3SO5OBl-4=rnc(ocBF(rc2)3B))s)n(717
replace 5 at position 10 with =,N6@)nH]3SO=OBl-4=rnc(ocBF(rc2)3B))s)n(717
remove n from position 4,N6@)H]3SO=OBl-4=rnc(ocBF(rc2)3B))s)n(717
remove ( from position 19,N6@)H]3SO=OBl-4=rncocBF(rc2)3B))s)n(717
remove ) from position 30,N6@)H]3SO=OBl-4=rncocBF(rc2)3B)s)n(717
add / at position 30,N6@)H]3SO=OBl-4=rncocBF(rc2)3B/)s)n(717
remove ( from position 23,N6@)H]3SO=OBl-4=rncocBFrc2)3B/)s)n(717
remove F from position 22,N6@)H]3SO=OBl-4=rncocBrc2)3B/)s)n(717
add r at position 33,N6@)H]3SO=OBl-4=rncocBrc2)3B/)s)nr(717
remove s from position 30,N6@)H]3SO=OBl-4=rncocBrc2)3B/))nr(717
remove ) from position 29,N6@)H]3SO=OBl-4=rncocBrc2)3B/)nr(717
remove 3 from position 6,N6@)H]SO=OBl-4=rncocBrc2)3B/)nr(717
add ) at position 30,N6@)H]SO=OBl-4=rncocBrc2)3B/)n)r(717
replace r at position 21 with 6,N6@)H]SO=OBl-4=rncocB6c2)3B/)n)r(717
replace o at position 18 with 4,N6@)H]SO=OBl-4=rnc4cB6c2)3B/)n)r(717
remove 6 from position 21,N6@)H]SO=OBl-4=rnc4cBc2)3B/)n)r(717
remove 4 from position 18,N6@)H]SO=OBl-4=rnccBc2)3B/)n)r(717
remove 1 from position 32,N6@)H]SO=OBl-4=rnccBc2)3B/)n)r(77
replace c at position 17 with ),N6@)H]SO=OBl-4=rn)cBc2)3B/)n)r(77
remove 4 from position 13,N6@)H]SO=OBl-=rn)cBc2)3B/)n)r(77
remove ) from position 21,N6@)H]SO=OBl-=rn)cBc23B/)n)r(77
replace r at position 14 with 3,N6@)H]SO=OBl-=3n)cBc23B/)n)r(77
add l at position 7,N6@)H]SlO=OBl-=3n)cBc23B/)n)r(77
add - at position 23,N6@)H]SlO=OBl-=3n)cBc23-B/)n)r(77
replace / at position 25 with (,N6@)H]SlO=OBl-=3n)cBc23-B()n)r(77
remove @ from position 2,N6)H]SlO=OBl-=3n)cBc23-B()n)r(77
replace O at position 9 with 5,N6)H]SlO=5Bl-=3n)cBc23-B()n)r(77
remove ) from position 27,N6)H]SlO=5Bl-=3n)cBc23-B()nr(77
replace 7 at position 29 with ),N6)H]SlO=5Bl-=3n)cBc23-B()nr()7
replace 7 at position 30 with 4,N6)H]SlO=5Bl-=3n)cBc23-B()nr()4
remove n from position 15,N6)H]SlO=5Bl-=3)cBc23-B()nr()4
replace O at position 7 with 6,N6)H]Sl6=5Bl-=3)cBc23-B()nr()4
remove 3 from position 20,N6)H]Sl6=5Bl-=3)cBc2-B()nr()4
replace N at position 0 with S,S6)H]Sl6=5Bl-=3)cBc2-B()nr()4
remove ) from position 27,S6)H]Sl6=5Bl-=3)cBc2-B()nr(4
remove S from position 5,S6)H]l6=5Bl-=3)cBc2-B()nr(4
remove ( from position 21,S6)H]l6=5Bl-=3)cBc2-B)nr(4
replace ] at position 4 with r,S6)Hrl6=5Bl-=3)cBc2-B)nr(4
remove - from position 19,S6)Hrl6=5Bl-=3)cBc2B)nr(4
add s at position 17,S6)Hrl6=5Bl-=3)cBsc2B)nr(4
replace n at position 22 with /,S6)Hrl6=5Bl-=3)cBsc2B)/r(4
replace ( at position 24 with F,S6)Hrl6=5Bl-=3)cBsc2B)/rF4
remove = from position 12,S6)Hrl6=5Bl-3)cBsc2B)/rF4
replace l at position 10 with -,S6)Hrl6=5B--3)cBsc2B)/rF4
remove c from position 17,S6)Hrl6=5B--3)cBs2B)/rF4
remove ) from position 19,S6)Hrl6=5B--3)cBs2B/rF4
remove c from position 14,S6)Hrl6=5B--3)Bs2B/rF4
add r at position 2,S6r)Hrl6=5B--3)Bs2B/rF4
remove F from position 21,S6r)Hrl6=5B--3)Bs2B/r4
remove 6 from position 1,Sr)Hrl6=5B--3)Bs2B/r4
remove ) from position 2,SrHrl6=5B--3)Bs2B/r4
remove 4 from position 19,SrHrl6=5B--3)Bs2B/r
replace 3 at position 11 with F,SrHrl6=5B--F)Bs2B/r
remove ) from position 12,SrHrl6=5B--FBs2B/r
remove r from position 1,SHrl6=5B--FBs2B/r
add l at position 2,SHlrl6=5B--FBs2B/r
remove - from position 10,SHlrl6=5B-FBs2B/r
add S at position 7,SHlrl6=S5B-FBs2B/r
replace S at position 0 with o,oHlrl6=S5B-FBs2B/r
remove F from position 11,oHlrl6=S5B-Bs2B/r
remove l from position 2,oHrl6=S5B-Bs2B/r
remove S from position 6,oHrl6=5B-Bs2B/r
replace / at position 13 with N,oHrl6=5B-Bs2BNr
remove 2 from position 11,oHrl6=5B-BsBNr
add O at position 2,oHOrl6=5B-BsBNr
replace B at position 10 with ],oHOrl6=5B-]sBNr
add 6 at position 14,oHOrl6=5B-]sBN6r
remove s from position 11,oHOrl6=5B-]BN6r
remove = from position 6,oHOrl65B-]BN6r
replace r at position 3 with 4,oHO4l65B-]BN6r
add H at position 5,oHO4lH65B-]BN6r
replace H at position 1 with F,oFO4lH65B-]BN6r
remove 6 from position 13,oFO4lH65B-]BNr
add H at position 9,oFO4lH65BH-]BNr
remove O from position 2,oF4lH65BH-]BNr
remove 6 from position 5,oF4lH5BH-]BNr
replace H at position 4 with [,oF4l[5BH-]BNr
remove 4 from position 2,oFl[5BH-]BNr
remove l from position 2,oF[5BH-]BNr
replace B at position 8 with [,oF[5BH-][Nr
remove 5 from position 3,oF[BH-][Nr
remove [ from position 2,oFBH-][Nr
remove [ from position 6,oFBH-]Nr
remove r from position 7,oFBH-]N
replace - at position 4 with B,oFBHB]N
remove N from position 6,oFBHB]
remove B from position 4,oFBH]
add C at position 5,oFBH]C
replace ] at position 4 with -,oFBH-C
add B at position 3,oFBBH-C
add c at position 2,oFcBBH-C
replace B at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
