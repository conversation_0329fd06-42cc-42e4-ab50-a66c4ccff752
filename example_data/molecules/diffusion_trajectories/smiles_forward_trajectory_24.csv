log,state
initialize: Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2)cc1,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2)cc1
replace C at position 28 with +,Cc1noc(C)c1COc1ccc(C[NH2+]C[+@H]2CCCO2)cc1
add ( at position 37,Cc1noc(C)c1COc1ccc(C[NH2+]C[+@H]2CCCO(2)cc1
remove c from position 5,Cc1no(C)c1COc1ccc(C[NH2+]C[+@H]2CCCO(2)cc1
add 2 at position 22,Cc1no(C)c1COc1ccc(C[NH22+]C[+@H]2CCCO(2)cc1
add 6 at position 1,C6c1no(C)c1COc1ccc(C[NH22+]C[+@H]2CCCO(2)cc1
remove @ from position 30,C6c1no(C)c1COc1ccc(C[NH22+]C[+H]2CC<PERSON>(2)cc1
remove c from position 9,C6c1no(C)1COc1ccc(C[NH22+]C[+H]2CCCO(2)cc1
add n at position 7,C6c1no(nC)1COc1ccc(C[NH22+]C[+H]2CCCO(2)cc1
add o at position 31,C6c1no(nC)1COc1ccc(C[NH22+]C[+Ho]2CCCO(2)cc1
replace n at position 4 with O,C6c1Oo(nC)1COc1ccc(C[NH22+]C[+Ho]2CCCO(2)cc1
remove + from position 29,C6c1Oo(nC)1COc1ccc(C[NH22+]C[Ho]2CCCO(2)cc1
add ) at position 43,C6c1Oo(nC)1COc1ccc(C[NH22+]C[Ho]2CCCO(2)cc1)
remove c from position 2,C61Oo(nC)1COc1ccc(C[NH22+]C[Ho]2CCCO(2)cc1)
remove O from position 35,C61Oo(nC)1COc1ccc(C[NH22+]C[Ho]2CCC(2)cc1)
add B at position 19,C61Oo(nC)1COc1ccc(CB[NH22+]C[Ho]2CCC(2)cc1)
replace ( at position 17 with 3,C61Oo(nC)1COc1ccc3CB[NH22+]C[Ho]2CCC(2)cc1)
remove c from position 12,C61Oo(nC)1CO1ccc3CB[NH22+]C[Ho]2CCC(2)cc1)
remove C from position 33,C61Oo(nC)1CO1ccc3CB[NH22+]C[Ho]2CC(2)cc1)
remove C from position 32,C61Oo(nC)1CO1ccc3CB[NH22+]C[Ho]2C(2)cc1)
add - at position 21,C61Oo(nC)1CO1ccc3CB[N-H22+]C[Ho]2C(2)cc1)
remove 2 from position 24,C61Oo(nC)1CO1ccc3CB[N-H2+]C[Ho]2C(2)cc1)
add H at position 4,C61OHo(nC)1CO1ccc3CB[N-H2+]C[Ho]2C(2)cc1)
remove H from position 4,C61Oo(nC)1CO1ccc3CB[N-H2+]C[Ho]2C(2)cc1)
remove 1 from position 9,C61Oo(nC)CO1ccc3CB[N-H2+]C[Ho]2C(2)cc1)
add ] at position 22,C61Oo(nC)CO1ccc3CB[N-H]2+]C[Ho]2C(2)cc1)
add - at position 33,C61Oo(nC)CO1ccc3CB[N-H]2+]C[Ho]2C-(2)cc1)
remove o from position 29,C61Oo(nC)CO1ccc3CB[N-H]2+]C[H]2C-(2)cc1)
add O at position 17,C61Oo(nC)CO1ccc3COB[N-H]2+]C[H]2C-(2)cc1)
replace 1 at position 11 with 4,C61Oo(nC)CO4ccc3COB[N-H]2+]C[H]2C-(2)cc1)
add ] at position 9,C61Oo(nC)]CO4ccc3COB[N-H]2+]C[H]2C-(2)cc1)
replace 1 at position 2 with I,C6IOo(nC)]CO4ccc3COB[N-H]2+]C[H]2C-(2)cc1)
remove - from position 22,C6IOo(nC)]CO4ccc3COB[NH]2+]C[H]2C-(2)cc1)
remove c from position 37,C6IOo(nC)]CO4ccc3COB[NH]2+]C[H]2C-(2)c1)
replace ] at position 30 with (,C6IOo(nC)]CO4ccc3COB[NH]2+]C[H(2C-(2)c1)
replace ( at position 30 with o,C6IOo(nC)]CO4ccc3COB[NH]2+]C[Ho2C-(2)c1)
replace C at position 10 with @,C6IOo(nC)]@O4ccc3COB[NH]2+]C[Ho2C-(2)c1)
remove - from position 33,C6IOo(nC)]@O4ccc3COB[NH]2+]C[Ho2C(2)c1)
replace O at position 18 with 3,C6IOo(nC)]@O4ccc3C3B[NH]2+]C[Ho2C(2)c1)
add = at position 11,C6IOo(nC)]@=O4ccc3C3B[NH]2+]C[Ho2C(2)c1)
remove C from position 7,C6IOo(n)]@=O4ccc3C3B[NH]2+]C[Ho2C(2)c1)
replace n at position 6 with l,C6IOo(l)]@=O4ccc3C3B[NH]2+]C[Ho2C(2)c1)
replace 2 at position 24 with n,C6IOo(l)]@=O4ccc3C3B[NH]n+]C[Ho2C(2)c1)
replace 3 at position 16 with O,C6IOo(l)]@=O4cccOC3B[NH]n+]C[Ho2C(2)c1)
add S at position 6,C6IOo(Sl)]@=O4cccOC3B[NH]n+]C[Ho2C(2)c1)
remove @ from position 10,C6IOo(Sl)]=O4cccOC3B[NH]n+]C[Ho2C(2)c1)
remove C from position 17,C6IOo(Sl)]=O4cccO3B[NH]n+]C[Ho2C(2)c1)
replace N at position 20 with l,C6IOo(Sl)]=O4cccO3B[lH]n+]C[Ho2C(2)c1)
replace o at position 4 with ),C6IO)(Sl)]=O4cccO3B[lH]n+]C[Ho2C(2)c1)
remove O from position 3,C6I)(Sl)]=O4cccO3B[lH]n+]C[Ho2C(2)c1)
replace I at position 2 with 2,C62)(Sl)]=O4cccO3B[lH]n+]C[Ho2C(2)c1)
remove S from position 5,C62)(l)]=O4cccO3B[lH]n+]C[Ho2C(2)c1)
replace ] at position 20 with 4,C62)(l)]=O4cccO3B[lH4n+]C[Ho2C(2)c1)
replace ( at position 4 with I,C62)Il)]=O4cccO3B[lH4n+]C[Ho2C(2)c1)
remove I from position 4,C62)l)]=O4cccO3B[lH4n+]C[Ho2C(2)c1)
remove c from position 12,C62)l)]=O4ccO3B[lH4n+]C[Ho2C(2)c1)
add S at position 12,C62)l)]=O4ccSO3B[lH4n+]C[Ho2C(2)c1)
replace ( at position 29 with F,C62)l)]=O4ccSO3B[lH4n+]C[Ho2CF2)c1)
add r at position 23,C62)l)]=O4ccSO3B[lH4n+]rC[Ho2CF2)c1)
replace ) at position 32 with r,C62)l)]=O4ccSO3B[lH4n+]rC[Ho2CF2rc1)
remove c from position 11,C62)l)]=O4cSO3B[lH4n+]rC[Ho2CF2rc1)
replace 3 at position 13 with 5,C62)l)]=O4cSO5B[lH4n+]rC[Ho2CF2rc1)
add F at position 22,C62)l)]=O4cSO5B[lH4n+]FrC[Ho2CF2rc1)
add B at position 35,C62)l)]=O4cSO5B[lH4n+]FrC[Ho2CF2rc1B)
remove O from position 8,C62)l)]=4cSO5B[lH4n+]FrC[Ho2CF2rc1B)
add r at position 10,C62)l)]=4crSO5B[lH4n+]FrC[Ho2CF2rc1B)
add ) at position 34,C62)l)]=4crSO5B[lH4n+]FrC[Ho2CF2rc)1B)
remove r from position 10,C62)l)]=4cSO5B[lH4n+]FrC[Ho2CF2rc)1B)
add N at position 3,C62N)l)]=4cSO5B[lH4n+]FrC[Ho2CF2rc)1B)
add r at position 9,C62N)l)]=r4cSO5B[lH4n+]FrC[Ho2CF2rc)1B)
add 7 at position 27,C62N)l)]=r4cSO5B[lH4n+]FrC[7Ho2CF2rc)1B)
remove O from position 13,C62N)l)]=r4cS5B[lH4n+]FrC[7Ho2CF2rc)1B)
replace C at position 24 with s,C62N)l)]=r4cS5B[lH4n+]Frs[7Ho2CF2rc)1B)
replace H at position 17 with F,C62N)l)]=r4cS5B[lF4n+]Frs[7Ho2CF2rc)1B)
remove n from position 19,C62N)l)]=r4cS5B[lF4+]Frs[7Ho2CF2rc)1B)
remove B from position 36,C62N)l)]=r4cS5B[lF4+]Frs[7Ho2CF2rc)1)
replace ) at position 34 with +,C62N)l)]=r4cS5B[lF4+]Frs[7Ho2CF2rc+1)
replace F at position 17 with =,C62N)l)]=r4cS5B[l=4+]Frs[7Ho2CF2rc+1)
remove ) from position 36,C62N)l)]=r4cS5B[l=4+]Frs[7Ho2CF2rc+1
remove ] from position 7,C62N)l)=r4cS5B[l=4+]Frs[7Ho2CF2rc+1
remove 4 from position 17,C62N)l)=r4cS5B[l=+]Frs[7Ho2CF2rc+1
add ( at position 25,C62N)l)=r4cS5B[l=+]Frs[7H(o2CF2rc+1
remove C from position 28,C62N)l)=r4cS5B[l=+]Frs[7H(o2F2rc+1
add / at position 31,C62N)l)=r4cS5B[l=+]Frs[7H(o2F2r/c+1
remove C from position 0,62N)l)=r4cS5B[l=+]Frs[7H(o2F2r/c+1
add 4 at position 25,62N)l)=r4cS5B[l=+]Frs[7H(4o2F2r/c+1
remove r from position 30,62N)l)=r4cS5B[l=+]Frs[7H(4o2F2/c+1
add / at position 30,62N)l)=r4cS5B[l=+]Frs[7H(4o2F2//c+1
remove H from position 23,62N)l)=r4cS5B[l=+]Frs[7(4o2F2//c+1
remove 7 from position 22,62N)l)=r4cS5B[l=+]Frs[(4o2F2//c+1
add r at position 33,62N)l)=r4cS5B[l=+]Frs[(4o2F2//c+1r
remove c from position 30,62N)l)=r4cS5B[l=+]Frs[(4o2F2//+1r
remove / from position 29,62N)l)=r4cS5B[l=+]Frs[(4o2F2/+1r
remove = from position 6,62N)l)r4cS5B[l=+]Frs[(4o2F2/+1r
add ) at position 30,62N)l)r4cS5B[l=+]Frs[(4o2F2/+1)r
replace ( at position 21 with 7,62N)l)r4cS5B[l=+]Frs[74o2F2/+1)r
replace r at position 18 with 4,62N)l)r4cS5B[l=+]F4s[74o2F2/+1)r
remove 7 from position 21,62N)l)r4cS5B[l=+]F4s[4o2F2/+1)r
remove r from position 30,62N)l)r4cS5B[l=+]F4s[4o2F2/+1)
remove r from position 6,62N)l)4cS5B[l=+]F4s[4o2F2/+1)
remove F from position 23,62N)l)4cS5B[l=+]F4s[4o22/+1)
remove 4 from position 17,62N)l)4cS5B[l=+]Fs[4o22/+1)
remove 4 from position 6,62N)l)cS5B[l=+]Fs[4o22/+1)
remove [ from position 10,62N)l)cS5Bl=+]Fs[4o22/+1)
replace F at position 14 with 3,62N)l)cS5Bl=+]3s[4o22/+1)
add H at position 16,62N)l)cS5Bl=+]3sH[4o22/+1)
remove ) from position 3,62Nl)cS5Bl=+]3sH[4o22/+1)
replace B at position 8 with 6,62Nl)cS56l=+]3sH[4o22/+1)
add 7 at position 0,762Nl)cS56l=+]3sH[4o22/+1)
add 1 at position 1,7162Nl)cS56l=+]3sH[4o22/+1)
remove 6 from position 10,7162Nl)cS5l=+]3sH[4o22/+1)
remove ] from position 13,7162Nl)cS5l=+3sH[4o22/+1)
replace 6 at position 2 with o,71o2Nl)cS5l=+3sH[4o22/+1)
replace 5 at position 9 with 3,71o2Nl)cS3l=+3sH[4o22/+1)
remove o from position 18,71o2Nl)cS3l=+3sH[422/+1)
add ) at position 16,71o2Nl)cS3l=+3sH)[422/+1)
remove 7 from position 0,1o2Nl)cS3l=+3sH)[422/+1)
remove = from position 10,1o2Nl)cS3l+3sH)[422/+1)
replace S at position 7 with /,1o2Nl)c/3l+3sH)[422/+1)
remove 1 from position 21,1o2Nl)c/3l+3sH)[422/+)
replace l at position 4 with r,1o2Nr)c/3l+3sH)[422/+)
remove / from position 19,1o2Nr)c/3l+3sH)[422+)
add s at position 17,1o2Nr)c/3l+3sH)[4s22+)
replace ) at position 5 with s,1o2Nrsc/3l+3sH)[4s22+)
remove 4 from position 16,1o2Nrsc/3l+3sH)[s22+)
remove s from position 12,1o2Nrsc/3l+3H)[s22+)
replace + at position 10 with /,1o2Nrsc/3l/3H)[s22+)
remove 2 from position 17,1o2Nrsc/3l/3H)[s2+)
remove [ from position 14,1o2Nrsc/3l/3H)s2+)
add r at position 2,1or2Nrsc/3l/3H)s2+)
remove H from position 13,1or2Nrsc/3l/3)s2+)
replace r at position 2 with 5,1o52Nrsc/3l/3)s2+)
add 6 at position 6,1o52Nr6sc/3l/3)s2+)
replace 2 at position 16 with [,1o52Nr6sc/3l/3)s[+)
replace ) at position 18 with C,1o52Nr6sc/3l/3)s[+C
replace 1 at position 0 with B,Bo52Nr6sc/3l/3)s[+C
replace / at position 9 with @,Bo52Nr6sc@3l/3)s[+C
replace N at position 4 with B,Bo52Br6sc@3l/3)s[+C
remove s from position 7,Bo52Br6c@3l/3)s[+C
remove B from position 0,o52Br6c@3l/3)s[+C
replace 3 at position 11 with I,o52Br6c@3l/I)s[+C
remove 6 from position 5,o52Brc@3l/I)s[+C
add 1 at position 0,1o52Brc@3l/I)s[+C
replace B at position 4 with F,1o52Frc@3l/I)s[+C
replace 3 at position 8 with O,1o52Frc@Ol/I)s[+C
remove [ from position 14,1o52Frc@Ol/I)s+C
remove I from position 11,1o52Frc@Ol/)s+C
remove c from position 6,1o52Fr@Ol/)s+C
replace 2 at position 3 with 5,1o55Fr@Ol/)s+C
add H at position 5,1o55FHr@Ol/)s+C
replace o at position 1 with F,1F55FHr@Ol/)s+C
remove + from position 13,1F55FHr@Ol/)sC
add H at position 9,1F55FHr@OHl/)sC
remove 5 from position 2,1F5FHr@OHl/)sC
remove r from position 5,1F5FH@OHl/)sC
replace H at position 4 with [,1F5F[@OHl/)sC
remove 5 from position 2,1FF[@OHl/)sC
remove F from position 2,1F[@OHl/)sC
replace ) at position 8 with [,1F[@OHl/[sC
remove @ from position 3,1F[OHl/[sC
remove [ from position 2,1FOHl/[sC
remove [ from position 6,1FOHl/sC
remove C from position 7,1FOHl/s
replace l at position 4 with @,1FOH@/s
remove s from position 6,1FOH@/
remove @ from position 4,1FOH/
add C at position 5,1FOH/C
replace / at position 4 with -,1FOH-C
add B at position 3,1FOBH-C
add c at position 2,1FcOBH-C
replace O at position 3 with [,1Fc[BH-C
add s at position 6,1Fc[BHs-C
remove C from position 8,1Fc[BHs-
remove [ from position 3,1FcBHs-
remove s from position 5,1FcBH-
replace B at position 3 with 5,1Fc5H-
replace H at position 4 with +,1Fc5+-
remove 5 from position 3,1Fc+-
add / at position 1,1/Fc+-
remove F from position 2,1/c+-
add S at position 2,1/Sc+-
replace S at position 2 with #,1/#c+-
add 5 at position 5,1/#c+5-
remove 1 from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
