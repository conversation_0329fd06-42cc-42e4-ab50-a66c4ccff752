log,state
initialize: Cn1cc[nH+]c1C[C@H]1CCC[NH+](Cc2ncc(-c3ccccc3Cl)o2)C1,Cn1cc[nH+]c1C[C@H]1CCC[NH+](Cc2ncc(-c3ccccc3Cl)o2)C1
replace C at position 28 with +,Cn1cc[nH+]c1C[C@H]1CCC[NH+](+c2ncc(-c3ccccc3Cl)o2)C1
add H at position 51,Cn1cc[nH+]c1C[C@H]1CCC[NH+](+c2ncc(-c3ccccc3Cl)o2)CH1
add ) at position 44,Cn1cc[nH+]c1C[C@H]1CCC[NH+](+c2ncc(-c3ccccc3)Cl)o2)CH1
add 2 at position 22,Cn1cc[nH+]c1C[C@H]1CCC2[NH+](+c2ncc(-c3ccccc3)Cl)o2)CH1
add # at position 50,Cn1cc[nH+]c1C[C@H]1CCC2[NH+](+c2ncc(-c3ccccc3)Cl)o#2)CH1
remove c from position 39,Cn1cc[nH+]c1C[C@H]1CCC2[NH+](+c2ncc(-c3cccc3)Cl)o#2)CH1
remove + from position 29,Cn1cc[nH+]c1C[C@H]1CCC2[NH+](c2ncc(-c3cccc3)Cl)o#2)CH1
add S at position 11,Cn1cc[nH+]cS1C[C@H]1CCC2[NH+](c2ncc(-c3cccc3)Cl)o#2)CH1
add C at position 0,CCn1cc[nH+]cS1C[C@H]1CCC2[NH+](c2ncc(-c3cccc3)Cl)o#2)CH1
remove [ from position 15,CCn1cc[nH+]cS1CC@H]1CCC2[NH+](c2ncc(-c3cccc3)Cl)o#2)CH1
add r at position 34,CCn1cc[nH+]cS1CC@H]1CCC2[NH+](c2ncrc(-c3cccc3)Cl)o#2)CH1
remove c from position 38,CCn1cc[nH+]cS1CC@H]1CCC2[NH+](c2ncrc(-3cccc3)Cl)o#2)CH1
remove n from position 32,CCn1cc[nH+]cS1CC@H]1CCC2[NH+](c2crc(-3cccc3)Cl)o#2)CH1
remove H from position 17,CCn1cc[nH+]cS1CC@]1CCC2[NH+](c2crc(-3cccc3)Cl)o#2)CH1
replace c at position 4 with N,CCn1Nc[nH+]cS1CC@]1CCC2[NH+](c2crc(-3cccc3)Cl)o#2)CH1
remove 2 from position 22,CCn1Nc[nH+]cS1CC@]1CCC[NH+](c2crc(-3cccc3)Cl)o#2)CH1
remove ) from position 41,CCn1Nc[nH+]cS1CC@]1CCC[NH+](c2crc(-3cccc3Cl)o#2)CH1
replace 2 at position 29 with r,CCn1Nc[nH+]cS1CC@]1CCC[NH+](crcrc(-3cccc3Cl)o#2)CH1
remove C from position 1,Cn1Nc[nH+]cS1CC@]1CCC[NH+](crcrc(-3cccc3Cl)o#2)CH1
replace c at position 4 with #,Cn1N#[nH+]cS1CC@]1CCC[NH+](crcrc(-3cccc3Cl)o#2)CH1
remove 2 from position 45,Cn1N#[nH+]cS1CC@]1CCC[NH+](crcrc(-3cccc3Cl)o#)CH1
remove + from position 24,Cn1N#[nH+]cS1CC@]1CCC[NH](crcrc(-3cccc3Cl)o#)CH1
add H at position 4,Cn1NH#[nH+]cS1CC@]1CCC[NH](crcrc(-3cccc3Cl)o#)CH1
remove H from position 4,Cn1N#[nH+]cS1CC@]1CCC[NH](crcrc(-3cccc3Cl)o#)CH1
remove ] from position 9,Cn1N#[nH+cS1CC@]1CCC[NH](crcrc(-3cccc3Cl)o#)CH1
add ] at position 22,Cn1N#[nH+cS1CC@]1CCC[N]H](crcrc(-3cccc3Cl)o#)CH1
add - at position 33,Cn1N#[nH+cS1CC@]1CCC[N]H](crcrc(--3cccc3Cl)o#)CH1
remove r from position 29,Cn1N#[nH+cS1CC@]1CCC[N]H](crcc(--3cccc3Cl)o#)CH1
add O at position 17,Cn1N#[nH+cS1CC@]1OCCC[N]H](crcc(--3cccc3Cl)o#)CH1
replace 1 at position 11 with 4,Cn1N#[nH+cS4CC@]1OCCC[N]H](crcc(--3cccc3Cl)o#)CH1
add ] at position 9,Cn1N#[nH+]cS4CC@]1OCCC[N]H](crcc(--3cccc3Cl)o#)CH1
replace 1 at position 2 with I,CnIN#[nH+]cS4CC@]1OCCC[N]H](crcc(--3cccc3Cl)o#)CH1
remove [ from position 22,CnIN#[nH+]cS4CC@]1OCCCN]H](crcc(--3cccc3Cl)o#)CH1
remove c from position 37,CnIN#[nH+]cS4CC@]1OCCCN]H](crcc(--3ccc3Cl)o#)CH1
replace c at position 30 with (,CnIN#[nH+]cS4CC@]1OCCCN]H](crc((--3ccc3Cl)o#)CH1
replace ( at position 30 with o,CnIN#[nH+]cS4CC@]1OCCCN]H](crco(--3ccc3Cl)o#)CH1
replace c at position 10 with @,CnIN#[nH+]@S4CC@]1OCCCN]H](crco(--3ccc3Cl)o#)CH1
remove - from position 33,CnIN#[nH+]@S4CC@]1OCCCN]H](crco(-3ccc3Cl)o#)CH1
replace O at position 18 with 3,CnIN#[nH+]@S4CC@]13CCCN]H](crco(-3ccc3Cl)o#)CH1
add = at position 11,CnIN#[nH+]@=S4CC@]13CCCN]H](crco(-3ccc3Cl)o#)CH1
remove H from position 7,CnIN#[n+]@=S4CC@]13CCCN]H](crco(-3ccc3Cl)o#)CH1
replace n at position 6 with l,CnIN#[l+]@=S4CC@]13CCCN]H](crco(-3ccc3Cl)o#)CH1
replace H at position 24 with n,CnIN#[l+]@=S4CC@]13CCCN]n](crco(-3ccc3Cl)o#)CH1
replace ] at position 16 with N,CnIN#[l+]@=S4CC@N13CCCN]n](crco(-3ccc3Cl)o#)CH1
add S at position 6,CnIN#[Sl+]@=S4CC@N13CCCN]n](crco(-3ccc3Cl)o#)CH1
remove @ from position 10,CnIN#[Sl+]=S4CC@N13CCCN]n](crco(-3ccc3Cl)o#)CH1
remove 1 from position 17,CnIN#[Sl+]=S4CC@N3CCCN]n](crco(-3ccc3Cl)o#)CH1
replace C at position 20 with l,CnIN#[Sl+]=S4CC@N3CClN]n](crco(-3ccc3Cl)o#)CH1
replace # at position 4 with +,CnIN+[Sl+]=S4CC@N3CClN]n](crco(-3ccc3Cl)o#)CH1
remove N from position 3,CnI+[Sl+]=S4CC@N3CClN]n](crco(-3ccc3Cl)o#)CH1
replace I at position 2 with 2,Cn2+[Sl+]=S4CC@N3CClN]n](crco(-3ccc3Cl)o#)CH1
remove S from position 5,Cn2+[l+]=S4CC@N3CClN]n](crco(-3ccc3Cl)o#)CH1
replace ] at position 20 with 4,Cn2+[l+]=S4CC@N3CClN4n](crco(-3ccc3Cl)o#)CH1
replace [ at position 4 with H,Cn2+Hl+]=S4CC@N3CClN4n](crco(-3ccc3Cl)o#)CH1
remove H from position 4,Cn2+l+]=S4CC@N3CClN4n](crco(-3ccc3Cl)o#)CH1
remove @ from position 12,Cn2+l+]=S4CCN3CClN4n](crco(-3ccc3Cl)o#)CH1
add S at position 12,Cn2+l+]=S4CCSN3CClN4n](crco(-3ccc3Cl)o#)CH1
replace 3 at position 29 with F,Cn2+l+]=S4CCSN3CClN4n](crco(-Fccc3Cl)o#)CH1
add r at position 23,Cn2+l+]=S4CCSN3CClN4n](rcrco(-Fccc3Cl)o#)CH1
replace c at position 32 with r,Cn2+l+]=S4CCSN3CClN4n](rcrco(-Fcrc3Cl)o#)CH1
remove C from position 11,Cn2+l+]=S4CSN3CClN4n](rcrco(-Fcrc3Cl)o#)CH1
replace 3 at position 13 with 5,Cn2+l+]=S4CSN5CClN4n](rcrco(-Fcrc3Cl)o#)CH1
add F at position 22,Cn2+l+]=S4CSN5CClN4n](Frcrco(-Fcrc3Cl)o#)CH1
add B at position 35,Cn2+l+]=S4CSN5CClN4n](Frcrco(-Fcrc3BCl)o#)CH1
remove S from position 8,Cn2+l+]=4CSN5CClN4n](Frcrco(-Fcrc3BCl)o#)CH1
add r at position 10,Cn2+l+]=4CrSN5CClN4n](Frcrco(-Fcrc3BCl)o#)CH1
add ) at position 34,Cn2+l+]=4CrSN5CClN4n](Frcrco(-Fcrc)3BCl)o#)CH1
remove r from position 10,Cn2+l+]=4CSN5CClN4n](Frcrco(-Fcrc)3BCl)o#)CH1
add ( at position 43,Cn2+l+]=4CSN5CClN4n](Frcrco(-Fcrc)3BCl)o#)C(H1
remove C from position 9,Cn2+l+]=4SN5CClN4n](Frcrco(-Fcrc)3BCl)o#)C(H1
replace 2 at position 2 with @,Cn@+l+]=4SN5CClN4n](Frcrco(-Fcrc)3BCl)o#)C(H1
remove F from position 20,Cn@+l+]=4SN5CClN4n](rcrco(-Fcrc)3BCl)o#)C(H1
add 7 at position 43,Cn@+l+]=4SN5CClN4n](rcrco(-Fcrc)3BCl)o#)C(H71
replace n at position 17 with F,Cn@+l+]=4SN5CClN4F](rcrco(-Fcrc)3BCl)o#)C(H71
remove ( from position 19,Cn@+l+]=4SN5CClN4F]rcrco(-Fcrc)3BCl)o#)C(H71
remove 1 from position 43,Cn@+l+]=4SN5CClN4F]rcrco(-Fcrc)3BCl)o#)C(H7
remove S from position 9,Cn@+l+]=4N5CClN4F]rcrco(-Fcrc)3BCl)o#)C(H7
replace + at position 5 with c,Cn@+lc]=4N5CClN4F]rcrco(-Fcrc)3BCl)o#)C(H7
remove c from position 26,Cn@+lc]=4N5CClN4F]rcrco(-Frc)3BCl)o#)C(H7
remove ) from position 36,Cn@+lc]=4N5CClN4F]rcrco(-Frc)3BCl)o#C(H7
remove = from position 7,Cn@+lc]4N5CClN4F]rcrco(-Frc)3BCl)o#C(H7
remove r from position 17,Cn@+lc]4N5CClN4F]crco(-Frc)3BCl)o#C(H7
add 7 at position 37,Cn@+lc]4N5CClN4F]crco(-Frc)3BCl)o#C(H77
add 2 at position 28,Cn@+lc]4N5CClN4F]crco(-Frc)32BCl)o#C(H77
replace C at position 10 with 7,Cn@+lc]4N57ClN4F]crco(-Frc)32BCl)o#C(H77
remove l from position 4,Cn@+c]4N57ClN4F]crco(-Frc)32BCl)o#C(H77
remove o from position 19,Cn@+c]4N57ClN4F]crc(-Frc)32BCl)o#C(H77
remove ) from position 30,Cn@+c]4N57ClN4F]crc(-Frc)32BClo#C(H77
add / at position 30,Cn@+c]4N57ClN4F]crc(-Frc)32BCl/o#C(H77
remove c from position 23,Cn@+c]4N57ClN4F]crc(-Fr)32BCl/o#C(H77
remove r from position 22,Cn@+c]4N57ClN4F]crc(-F)32BCl/o#C(H77
add r at position 33,Cn@+c]4N57ClN4F]crc(-F)32BCl/o#C(rH77
remove # from position 30,Cn@+c]4N57ClN4F]crc(-F)32BCl/oC(rH77
remove o from position 29,Cn@+c]4N57ClN4F]crc(-F)32BCl/C(rH77
remove 4 from position 6,Cn@+c]N57ClN4F]crc(-F)32BCl/C(rH77
add ) at position 30,Cn@+c]N57ClN4F]crc(-F)32BCl/C()rH77
replace ) at position 21 with 7,Cn@+c]N57ClN4F]crc(-F732BCl/C()rH77
replace ( at position 18 with 5,Cn@+c]N57ClN4F]crc5-F732BCl/C()rH77
remove 7 from position 21,Cn@+c]N57ClN4F]crc5-F32BCl/C()rH77
remove 5 from position 18,Cn@+c]N57ClN4F]crc-F32BCl/C()rH77
remove 7 from position 32,Cn@+c]N57ClN4F]crc-F32BCl/C()rH7
replace c at position 17 with ),Cn@+c]N57ClN4F]cr)-F32BCl/C()rH7
remove F from position 13,Cn@+c]N57ClN4]cr)-F32BCl/C()rH7
remove l from position 10,Cn@+c]N57CN4]cr)-F32BCl/C()rH7
replace r at position 14 with 3,Cn@+c]N57CN4]c3)-F32BCl/C()rH7
add H at position 16,Cn@+c]N57CN4]c3)H-F32BCl/C()rH7
remove + from position 3,Cn@c]N57CN4]c3)H-F32BCl/C()rH7
replace C at position 8 with 6,Cn@c]N576N4]c3)H-F32BCl/C()rH7
add 7 at position 0,7Cn@c]N576N4]c3)H-F32BCl/C()rH7
add 1 at position 2,7C1n@c]N576N4]c3)H-F32BCl/C()rH7
remove 3 from position 20,7C1n@c]N576N4]c3)H-F2BCl/C()rH7
remove ] from position 13,7C1n@c]N576N4c3)H-F2BCl/C()rH7
replace 7 at position 29 with ),7C1n@c]N576N4c3)H-F2BCl/C()rH)
replace 7 at position 9 with 3,7C1n@c]N536N4c3)H-F2BCl/C()rH)
remove ) from position 29,7C1n@c]N536N4c3)H-F2BCl/C()rH
remove N from position 11,7C1n@c]N5364c3)H-F2BCl/C()rH
remove C from position 20,7C1n@c]N5364c3)H-F2Bl/C()rH
replace 7 at position 0 with S,SC1n@c]N5364c3)H-F2Bl/C()rH
remove 2 from position 18,SC1n@c]N5364c3)H-FBl/C()rH
add B at position 15,SC1n@c]N5364c3)BH-FBl/C()rH
remove / from position 21,SC1n@c]N5364c3)BH-FBlC()rH
replace @ at position 4 with r,SC1nrc]N5364c3)BH-FBlC()rH
remove B from position 19,SC1nrc]N5364c3)BH-FlC()rH
add s at position 17,SC1nrc]N5364c3)BHs-FlC()rH
replace ( at position 22 with 1,SC1nrc]N5364c3)BHs-FlC1)rH
replace r at position 24 with C,SC1nrc]N5364c3)BHs-FlC1)CH
remove c from position 12,SC1nrc]N53643)BHs-FlC1)CH
replace 6 at position 10 with -,SC1nrc]N53-43)BHs-FlC1)CH
remove - from position 17,SC1nrc]N53-43)BHsFlC1)CH
remove C from position 19,SC1nrc]N53-43)BHsFl1)CH
remove B from position 14,SC1nrc]N53-43)HsFl1)CH
add r at position 2,SCr1nrc]N53-43)HsFl1)CH
remove C from position 21,SCr1nrc]N53-43)HsFl1)H
remove C from position 1,Sr1nrc]N53-43)HsFl1)H
remove 1 from position 2,Srnrc]N53-43)HsFl1)H
remove H from position 19,Srnrc]N53-43)HsFl1)
replace 3 at position 11 with F,Srnrc]N53-4F)HsFl1)
remove ) from position 12,Srnrc]N53-4FHsFl1)
remove r from position 1,Snrc]N53-4FHsFl1)
add l at position 2,Snlrc]N53-4FHsFl1)
remove 4 from position 10,Snlrc]N53-FHsFl1)
add S at position 7,Snlrc]NS53-FHsFl1)
replace S at position 0 with o,onlrc]NS53-FHsFl1)
remove F from position 11,onlrc]NS53-HsFl1)
remove l from position 2,onrc]NS53-HsFl1)
remove S from position 6,onrc]N53-HsFl1)
replace 1 at position 13 with N,onrc]N53-HsFlN)
remove F from position 11,onrc]N53-HslN)
add O at position 2,onOrc]N53-HslN)
replace H at position 10 with ],onOrc]N53-]slN)
add 6 at position 14,onOrc]N53-]slN6)
remove s from position 11,onOrc]N53-]lN6)
remove N from position 6,onOrc]53-]lN6)
replace r at position 3 with 4,onO4c]53-]lN6)
add H at position 5,onO4cH]53-]lN6)
replace n at position 1 with F,oFO4cH]53-]lN6)
remove 6 from position 13,oFO4cH]53-]lN)
add H at position 9,oFO4cH]53H-]lN)
remove O from position 2,oF4cH]53H-]lN)
remove ] from position 5,oF4cH53H-]lN)
replace H at position 4 with [,oF4c[53H-]lN)
remove 4 from position 2,oFc[53H-]lN)
remove c from position 2,oF[53H-]lN)
replace l at position 8 with S,oF[53H-]SN)
remove 5 from position 3,oF[3H-]SN)
remove [ from position 2,oF3H-]SN)
remove S from position 6,oF3H-]N)
remove ) from position 7,oF3H-]N
replace - at position 4 with B,oF3HB]N
remove N from position 6,oF3HB]
remove B from position 4,oF3H]
add C at position 5,oF3H]C
replace ] at position 4 with -,oF3H-C
add B at position 3,oF3BH-C
add c at position 2,oFc3BH-C
replace 3 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
