log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 5,oFc5BH-C
remove c from position 2,oF5BH-<PERSON>
remove B from position 3,oF5H-<PERSON>
replace - at position 4 with ],oF5H]C
remove <PERSON> from position 5,oF5H]
add B at position 4,oF5HB]
add N at position 6,oF5HB]N
replace B at position 4 with -,oF5H-]N
add r at position 7,oF5H-]Nr
add S at position 6,oF5H-]SNr
add [ at position 2,oF[5H-]SNr
add 5 at position 3,oF[55H-]SNr
replace S at position 8 with r,oF[55H-]rNr
add l at position 2,oFl[55H-]rNr
add 4 at position 2,oF4l[55H-]rNr
replace [ at position 4 with H,oF4lH55H-]rNr
add 7 at position 5,oF4lH755H-]rNr
add O at position 2,oFO4lH755H-]rNr
remove H from position 9,oFO4lH755-]rNr
add 6 at position 13,oFO4lH755-]rN6r
replace F at position 1 with n,onO4lH755-]rN6r
remove H from position 5,onO4l755-]rN6r
replace 4 at position 3 with r,onOrl755-]rN6r
add = at position 6,onOrl7=55-]rN6r
add s at position 11,onOrl7=55-]srN6r
remove 6 from position 14,onOrl7=55-]srNr
replace ] at position 10 with 7,onOrl7=55-7srNr
remove O from position 2,onrl7=55-7srNr
add 2 at position 11,onrl7=55-7s2rNr
replace N at position 13 with 1,onrl7=55-7s2r1r
add S at position 6,onrl7=S55-7s2r1r
add l at position 2,onlrl7=S55-7s2r1r
add F at position 11,onlrl7=S55-F7s2r1r
replace o at position 0 with S,Snlrl7=S55-F7s2r1r
remove S from position 7,Snlrl7=55-F7s2r1r
add B at position 10,Snlrl7=55-BF7s2r1r
remove l from position 2,Snrl7=55-BF7s2r1r
add r at position 1,Srnrl7=55-BF7s2r1r
add ) at position 12,Srnrl7=55-BF)7s2r1r
replace F at position 11 with 4,Srnrl7=55-B4)7s2r1r
add 4 at position 19,Srnrl7=55-B4)7s2r1r4
add N at position 2,SrNnrl7=55-B4)7s2r1r4
add 6 at position 1,S6rNnrl7=55-B4)7s2r1r4
add F at position 21,S6rNnrl7=55-B4)7s2r1rF4
remove r from position 2,S6Nnrl7=55-B4)7s2r1rF4
add ( at position 14,S6Nnrl7=55-B4)(7s2r1rF4
add C at position 19,S6Nnrl7=55-B4)(7s2rC1rF4
add B at position 17,S6Nnrl7=55-B4)(7sB2rC1rF4
replace - at position 10 with 1,S6Nnrl7=551B4)(7sB2rC1rF4
add = at position 12,S6Nnrl7=551B=4)(7sB2rC1rF4
replace F at position 24 with ),S6Nnrl7=551B=4)(7sB2rC1r)4
replace 1 at position 22 with ),S6Nnrl7=551B=4)(7sB2rC)r)4
remove s from position 17,S6Nnrl7=551B=4)(7B2rC)r)4
add - at position 19,S6Nnrl7=551B=4)(7B2-rC)r)4
replace r at position 4 with c,S6Nncl7=551B=4)(7B2-rC)r)4
add ( at position 21,S6Nncl7=551B=4)(7B2-r(C)r)4
add r at position 5,S6Nncrl7=551B=4)(7B2-r(C)r)4
add ) at position 27,S6Nncrl7=551B=4)(7B2-r(C)r))4
replace S at position 0 with C,C6Nncrl7=551B=4)(7B2-r(C)r))4
add c at position 20,C6Nncrl7=551B=4)(7B2c-r(C)r))4
replace 7 at position 7 with 4,C6Nncrl4=551B=4)(7B2c-r(C)r))4
add N at position 15,C6Nncrl4=551B=4N)(7B2c-r(C)r))4
replace 4 at position 30 with =,C6Nncrl4=551B=4N)(7B2c-r(C)r))=
replace ) at position 29 with 7,C6Nncrl4=551B=4N)(7B2c-r(C)r)7=
add ) at position 27,C6Nncrl4=551B=4N)(7B2c-r(C))r)7=
replace 5 at position 9 with S,C6Nncrl4=S51B=4N)(7B2c-r(C))r)7=
add 2 at position 2,C62Nncrl4=S51B=4N)(7B2c-r(C))r)7=
replace ( at position 25 with /,C62Nncrl4=S51B=4N)(7B2c-r/C))r)7=
remove - from position 23,C62Nncrl4=S51B=4N)(7B2cr/C))r)7=
remove l from position 7,C62Nncr4=S51B=4N)(7B2cr/C))r)7=
replace 4 at position 14 with (,C62Nncr4=S51B=(N)(7B2cr/C))r)7=
add F at position 21,C62Nncr4=S51B=(N)(7B2Fcr/C))r)7=
add l at position 13,C62Nncr4=S51Bl=(N)(7B2Fcr/C))r)7=
replace ) at position 17 with F,C62Nncr4=S51Bl=(NF(7B2Fcr/C))r)7=
add n at position 32,C62Nncr4=S51Bl=(NF(7B2Fcr/C))r)7n=
add 4 at position 18,C62Nncr4=S51Bl=(NF4(7B2Fcr/C))r)7n=
add 7 at position 21,C62Nncr4=S51Bl=(NF4(77B2Fcr/C))r)7n=
replace 4 at position 18 with C,C62Nncr4=S51Bl=(NFC(77B2Fcr/C))r)7n=
replace 7 at position 21 with 2,C62Nncr4=S51Bl=(NFC(72B2Fcr/C))r)7n=
remove ) from position 30,C62Nncr4=S51Bl=(NFC(72B2Fcr/C)r)7n=
add = at position 6,C62Nnc=r4=S51Bl=(NFC(72B2Fcr/C)r)7n=
add + at position 29,C62Nnc=r4=S51Bl=(NFC(72B2Fcr/+C)r)7n=
add n at position 30,C62Nnc=r4=S51Bl=(NFC(72B2Fcr/+nC)r)7n=
remove r from position 33,C62Nnc=r4=S51Bl=(NFC(72B2Fcr/+nC))7n=
add = at position 22,C62Nnc=r4=S51Bl=(NFC(7=2B2Fcr/+nC))7n=
add o at position 23,C62Nnc=r4=S51Bl=(NFC(7=o2B2Fcr/+nC))7n=
remove / from position 30,C62Nnc=r4=S51Bl=(NFC(7=o2B2Fcr+nC))7n=
add ) at position 30,C62Nnc=r4=S51Bl=(NFC(7=o2B2Fcr)+nC))7n=
add r at position 19,C62Nnc=r4=S51Bl=(NFrC(7=o2B2Fcr)+nC))7n=
add ) at position 4,C62N)nc=r4=S51Bl=(NFrC(7=o2B2Fcr)+nC))7n=
replace = at position 10 with ),C62N)nc=r4)S51Bl=(NFrC(7=o2B2Fcr)+nC))7n=
remove 2 from position 28,C62N)nc=r4)S51Bl=(NFrC(7=o2BFcr)+nC))7n=
remove 7 from position 37,C62N)nc=r4)S51Bl=(NFrC(7=o2BFcr)+nC))n=
add 4 at position 17,C62N)nc=r4)S51Bl=4(NFrC(7=o2BFcr)+nC))n=
add ] at position 7,C62N)nc]=r4)S51Bl=4(NFrC(7=o2BFcr)+nC))n=
add ( at position 36,C62N)nc]=r4)S51Bl=4(NFrC(7=o2BFcr)+n(C))n=
replace = at position 17 with H,C62N)nc]=r4)S51BlH4(NFrC(7=o2BFcr)+n(C))n=
replace + at position 34 with ),C62N)nc]=r4)S51BlH4(NFrC(7=o2BFcr))n(C))n=
add B at position 36,C62N)nc]=r4)S51BlH4(NFrC(7=o2BFcr))nB(C))n=
add n at position 19,C62N)nc]=r4)S51BlH4n(NFrC(7=o2BFcr))nB(C))n=
replace H at position 17 with -,C62N)nc]=r4)S51Bl-4n(NFrC(7=o2BFcr))nB(C))n=
replace = at position 43 with 1,C62N)nc]=r4)S51Bl-4n(NFrC(7=o2BFcr))nB(C))n1
add O at position 13,C62N)nc]=r4)SO51Bl-4n(NFrC(7=o2BFcr))nB(C))n1
remove 7 from position 27,C62N)nc]=r4)SO51Bl-4n(NFrC(=o2BFcr))nB(C))n1
remove r from position 9,C62N)nc]=4)SO51Bl-4n(NFrC(=o2BFcr))nB(C))n1
remove N from position 3,C62)nc]=4)SO51Bl-4n(NFrC(=o2BFcr))nB(C))n1
add r at position 10,C62)nc]=4)rSO51Bl-4n(NFrC(=o2BFcr))nB(C))n1
remove ) from position 34,C62)nc]=4)rSO51Bl-4n(NFrC(=o2BFcr)nB(C))n1
remove r from position 10,C62)nc]=4)SO51Bl-4n(NFrC(=o2BFcr)nB(C))n1
add ) at position 8,C62)nc]=)4)SO51Bl-4n(NFrC(=o2BFcr)nB(C))n1
remove B from position 35,C62)nc]=)4)SO51Bl-4n(NFrC(=o2BFcr)n(C))n1
remove F from position 22,C62)nc]=)4)SO51Bl-4n(NrC(=o2BFcr)n(C))n1
replace 5 at position 13 with 3,C62)nc]=)4)SO31Bl-4n(NrC(=o2BFcr)n(C))n1
add - at position 11,C62)nc]=)4)-SO31Bl-4n(NrC(=o2BFcr)n(C))n1
replace r at position 32 with (,C62)nc]=)4)-SO31Bl-4n(NrC(=o2BFc()n(C))n1
remove r from position 23,C62)nc]=)4)-SO31Bl-4n(NC(=o2BFc()n(C))n1
replace F at position 29 with /,C62)nc]=)4)-SO31Bl-4n(NC(=o2B/c()n(C))n1
remove S from position 12,C62)nc]=)4)-O31Bl-4n(NC(=o2B/c()n(C))n1
add O at position 12,C62)nc]=)4)-OO31Bl-4n(NC(=o2B/c()n(C))n1
add H at position 4,C62)Hnc]=)4)-OO31Bl-4n(NC(=o2B/c()n(C))n1
replace H at position 4 with c,C62)cnc]=)4)-OO31Bl-4n(NC(=o2B/c()n(C))n1
replace 4 at position 20 with ],C62)cnc]=)4)-OO31Bl-]n(NC(=o2B/c()n(C))n1
add S at position 5,C62)cSnc]=)4)-OO31Bl-]n(NC(=o2B/c()n(C))n1
replace 2 at position 2 with H,C6H)cSnc]=)4)-OO31Bl-]n(NC(=o2B/c()n(C))n1
add C at position 3,C6HC)cSnc]=)4)-OO31Bl-]n(NC(=o2B/c()n(C))n1
replace ) at position 4 with 1,C6HC1cSnc]=)4)-OO31Bl-]n(NC(=o2B/c()n(C))n1
replace l at position 20 with c,C6HC1cSnc]=)4)-OO31Bc-]n(NC(=o2B/c()n(C))n1
add 2 at position 17,C6HC1cSnc]=)4)-OO231Bc-]n(NC(=o2B/c()n(C))n1
add B at position 10,C6HC1cSnc]B=)4)-OO231Bc-]n(NC(=o2B/c()n(C))n1
remove S from position 6,C6HC1cnc]B=)4)-OO231Bc-]n(NC(=o2B/c()n(C))n1
replace O at position 16 with ),C6HC1cnc]B=)4)-O)231Bc-]n(NC(=o2B/c()n(C))n1
replace n at position 24 with 2,C6HC1cnc]B=)4)-O)231Bc-]2(NC(=o2B/c()n(C))n1
replace n at position 6 with c,C6HC1ccc]B=)4)-O)231Bc-]2(NC(=o2B/c()n(C))n1
add c at position 7,C6HC1cccc]B=)4)-O)231Bc-]2(NC(=o2B/c()n(C))n1
remove = from position 11,C6HC1cccc]B)4)-O)231Bc-]2(NC(=o2B/c()n(C))n1
replace 3 at position 18 with O,C6HC1cccc]B)4)-O)2O1Bc-]2(NC(=o2B/c()n(C))n1
add - at position 33,C6HC1cccc]B)4)-O)2O1Bc-]2(NC(=o2B-/c()n(C))n1
replace B at position 10 with 1,C6HC1cccc]1)4)-O)2O1Bc-]2(NC(=o2B-/c()n(C))n1
replace o at position 30 with (,C6HC1cccc]1)4)-O)2O1Bc-]2(NC(=(2B-/c()n(C))n1
replace ( at position 30 with c,C6HC1cccc]1)4)-O)2O1Bc-]2(NC(=c2B-/c()n(C))n1
add 2 at position 37,C6HC1cccc]1)4)-O)2O1Bc-]2(NC(=c2B-/c(2)n(C))n1
add c at position 22,C6HC1cccc]1)4)-O)2O1Bcc-]2(NC(=c2B-/c(2)n(C))n1
replace H at position 2 with N,C6NC1cccc]1)4)-O)2O1Bcc-]2(NC(=c2B-/c(2)n(C))n1
remove ] from position 9,C6NC1cccc1)4)-O)2O1Bcc-]2(NC(=c2B-/c(2)n(C))n1
replace 4 at position 11 with (,C6NC1cccc1)()-O)2O1Bcc-]2(NC(=c2B-/c(2)n(C))n1
remove O from position 17,C6NC1cccc1)()-O)21Bcc-]2(NC(=c2B-/c(2)n(C))n1
add + at position 29,C6NC1cccc1)()-O)21Bcc-]2(NC(=+c2B-/c(2)n(C))n1
remove - from position 33,C6NC1cccc1)()-O)21Bcc-]2(NC(=+c2B/c(2)n(C))n1
remove ] from position 22,C6NC1cccc1)()-O)21Bcc-2(NC(=+c2B/c(2)n(C))n1
remove - from position 13,C6NC1cccc1)()O)21Bcc-2(NC(=+c2B/c(2)n(C))n1
remove / from position 31,C6NC1cccc1)()O)21Bcc-2(NC(=+c2Bc(2)n(C))n1
add ( at position 3,C6N(C1cccc1)()O)21Bcc-2(NC(=+c2Bc(2)n(C))n1
add + at position 42,C6N(C1cccc1)()O)21Bcc-2(NC(=+c2Bc(2)n(C))n+1
remove - from position 21,C6N(C1cccc1)()O)21Bcc2(NC(=+c2Bc(2)n(C))n+1
add c at position 32,C6N(C1cccc1)()O)21Bcc2(NC(=+c2Bcc(2)n(C))n+1
add c at position 33,C6N(C1cccc1)()O)21Bcc2(NC(=+c2Bccc(2)n(C))n+1
add C at position 12,C6N(C1cccc1)C()O)21Bcc2(NC(=+c2Bccc(2)n(C))n+1
replace 2 at position 17 with c,C6N(C1cccc1)C()O)c1Bcc2(NC(=+c2Bccc(2)n(C))n+1
remove B from position 19,C6N(C1cccc1)C()O)c1cc2(NC(=+c2Bccc(2)n(C))n+1
add c at position 35,C6N(C1cccc1)C()O)c1cc2(NC(=+c2Bccc(c2)n(C))n+1
add C at position 2,C6CN(C1cccc1)C()O)c1cc2(NC(=+c2Bccc(c2)n(C))n+1
remove ) from position 43,C6CN(C1cccc1)C()O)c1cc2(NC(=+c2Bccc(c2)n(C)n+1
add C at position 29,C6CN(C1cccc1)C()O)c1cc2(NC(=+Cc2Bccc(c2)n(C)n+1
replace ) at position 15 with =,C6CN(C1cccc1)C(=O)c1cc2(NC(=+Cc2Bccc(c2)n(C)n+1
replace B at position 32 with c,C6CN(C1cccc1)C(=O)c1cc2(NC(=+Cc2cccc(c2)n(C)n+1
remove + from position 45,C6CN(C1cccc1)C(=O)c1cc2(NC(=+Cc2cccc(c2)n(C)n1
add c at position 9,C6CN(C1ccccc1)C(=O)c1cc2(NC(=+Cc2cccc(c2)n(C)n1
add ) at position 30,C6CN(C1ccccc1)C(=O)c1cc2(NC(=+)Cc2cccc(c2)n(C)n1
remove 6 from position 1,CCN(C1ccccc1)C(=O)c1cc2(NC(=+)Cc2cccc(c2)n(C)n1
remove 2 from position 22,CCN(C1ccccc1)C(=O)c1cc(NC(=+)Cc2cccc(c2)n(C)n1
add c at position 5,CCN(Cc1ccccc1)C(=O)c1cc(NC(=+)Cc2cccc(c2)n(C)n1
remove ( from position 37,CCN(Cc1ccccc1)C(=O)c1cc(NC(=+)Cc2ccccc2)n(C)n1
replace + at position 28 with O,CCN(Cc1ccccc1)C(=O)c1cc(NC(=O)Cc2ccccc2)n(C)n1
final: CCN(Cc1ccccc1)C(=O)c1cc(NC(=O)Cc2ccccc2)n(C)n1,CCN(Cc1ccccc1)C(=O)c1cc(NC(=O)Cc2ccccc2)n(C)n1
