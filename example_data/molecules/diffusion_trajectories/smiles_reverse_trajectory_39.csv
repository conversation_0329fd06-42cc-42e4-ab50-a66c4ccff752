log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with B,oHcBBH-C
remove c from position 2,oHBB<PERSON>-<PERSON>
remove B from position 3,oHB<PERSON>-<PERSON>
replace - at position 4 with ],oHBH]C
remove C from position 5,oHBH]
add B at position 4,oHBHB]
add N at position 6,oHBHB]N
replace B at position 4 with -,oHBH-]N
add r at position 7,oHBH-]Nr
add [ at position 6,oHBH-][Nr
add [ at position 2,oH[BH-][Nr
add 5 at position 3,oH[5BH-][Nr
replace [ at position 8 with B,oH[5BH-]BNr
add l at position 2,oHl[5BH-]BNr
add 4 at position 2,oH4l[5BH-]BNr
replace [ at position 4 with H,oH4lH5BH-]BNr
add 6 at position 5,oH4lH65BH-]BNr
add O at position 2,oHO4lH65BH-]BNr
remove H from position 9,oHO4lH65B-]BNr
add 6 at position 13,oHO4lH65B-]BN6r
replace H at position 1 with =,o=O4lH65B-]BN6r
remove H from position 5,o=O4l65B-]BN6r
replace 4 at position 3 with r,o=Orl65B-]BN6r
add = at position 6,o=Orl6=5B-]BN6r
add s at position 11,o=Orl6=5B-]sBN6r
remove 6 from position 14,o=Orl6=5B-]sBNr
replace ] at position 10 with B,o=Orl6=5B-BsBNr
remove O from position 2,o=rl6=5B-BsBNr
add 2 at position 11,o=rl6=5B-Bs2BNr
replace N at position 13 with /,o=rl6=5B-Bs2B/r
add S at position 6,o=rl6=S5B-Bs2B/r
add l at position 2,o=lrl6=S5B-Bs2B/r
add F at position 11,o=lrl6=S5B-FBs2B/r
replace o at position 0 with S,S=lrl6=S5B-FBs2B/r
remove S from position 7,S=lrl6=5B-FBs2B/r
add - at position 10,S=lrl6=5B--FBs2B/r
remove l from position 2,S=rl6=5B--FBs2B/r
add r at position 1,Sr=rl6=5B--FBs2B/r
add ) at position 12,Sr=rl6=5B--F)Bs2B/r
replace F at position 11 with 3,Sr=rl6=5B--3)Bs2B/r
add 4 at position 19,Sr=rl6=5B--3)Bs2B/r4
add + at position 2,Sr+=rl6=5B--3)Bs2B/r4
add 6 at position 1,S6r+=rl6=5B--3)Bs2B/r4
add F at position 21,S6r+=rl6=5B--3)Bs2B/rF4
remove r from position 2,S6+=rl6=5B--3)Bs2B/rF4
add C at position 14,S6+=rl6=5B--3)CBs2B/rF4
add c at position 19,S6+=rl6=5B--3)CBs2Bc/rF4
add ( at position 17,S6+=rl6=5B--3)CBs(2Bc/rF4
replace - at position 10 with c,S6+=rl6=5Bc-3)CBs(2Bc/rF4
add = at position 12,S6+=rl6=5Bc-=3)CBs(2Bc/rF4
replace F at position 24 with (,S6+=rl6=5Bc-=3)CBs(2Bc/r(4
replace / at position 22 with 1,S6+=rl6=5Bc-=3)CBs(2Bc1r(4
remove s from position 17,S6+=rl6=5Bc-=3)CB(2Bc1r(4
add - at position 19,S6+=rl6=5Bc-=3)CB(2-Bc1r(4
replace r at position 4 with ],S6+=]l6=5Bc-=3)CB(2-Bc1r(4
add ( at position 21,S6+=]l6=5Bc-=3)CB(2-B(c1r(4
add S at position 5,S6+=]Sl6=5Bc-=3)CB(2-B(c1r(4
add ) at position 27,S6+=]Sl6=5Bc-=3)CB(2-B(c1r()4
replace S at position 0 with C,C6+=]Sl6=5Bc-=3)CB(2-B(c1r()4
add = at position 20,C6+=]Sl6=5Bc-=3)CB(2=-B(c1r()4
replace 6 at position 7 with O,C6+=]SlO=5Bc-=3)CB(2=-B(c1r()4
add ( at position 15,C6+=]SlO=5Bc-=3()CB(2=-B(c1r()4
replace 4 at position 30 with 7,C6+=]SlO=5Bc-=3()CB(2=-B(c1r()7
replace ) at position 29 with 7,C6+=]SlO=5Bc-=3()CB(2=-B(c1r(77
add ) at position 27,C6+=]SlO=5Bc-=3()CB(2=-B(c1)r(77
replace 5 at position 9 with c,C6+=]SlO=cBc-=3()CB(2=-B(c1)r(77
add @ at position 2,C6@+=]SlO=cBc-=3()CB(2=-B(c1)r(77
replace ( at position 25 with /,C6@+=]SlO=cBc-=3()CB(2=-B/c1)r(77
remove - from position 23,C6@+=]SlO=cBc-=3()CB(2=B/c1)r(77
remove l from position 7,C6@+=]SO=cBc-=3()CB(2=B/c1)r(77
replace 3 at position 14 with r,C6@+=]SO=cBc-=r()CB(2=B/c1)r(77
add ) at position 21,C6@+=]SO=cBc-=r()CB(2)=B/c1)r(77
add 4 at position 13,C6@+=]SO=cBc-4=r()CB(2)=B/c1)r(77
replace ) at position 17 with =,C6@+=]SO=cBc-4=r(=CB(2)=B/c1)r(77
add C at position 32,C6@+=]SO=cBc-4=r(=CB(2)=B/c1)r(7C7
add 4 at position 18,C6@+=]SO=cBc-4=r(=4CB(2)=B/c1)r(7C7
add 6 at position 21,C6@+=]SO=cBc-4=r(=4CB6(2)=B/c1)r(7C7
replace 4 at position 18 with o,C6@+=]SO=cBc-4=r(=oCB6(2)=B/c1)r(7C7
replace 6 at position 21 with r,C6@+=]SO=cBc-4=r(=oCBr(2)=B/c1)r(7C7
remove ) from position 30,C6@+=]SO=cBc-4=r(=oCBr(2)=B/c1r(7C7
add 4 at position 6,C6@+=]4SO=cBc-4=r(=oCBr(2)=B/c1r(7C7
add + at position 29,C6@+=]4SO=cBc-4=r(=oCBr(2)=B/+c1r(7C7
add c at position 30,C6@+=]4SO=cBc-4=r(=oCBr(2)=B/+cc1r(7C7
remove r from position 33,C6@+=]4SO=cBc-4=r(=oCBr(2)=B/+cc1(7C7
add F at position 22,C6@+=]4SO=cBc-4=r(=oCBFr(2)=B/+cc1(7C7
add C at position 23,C6@+=]4SO=cBc-4=r(=oCBFCr(2)=B/+cc1(7C7
remove / from position 30,C6@+=]4SO=cBc-4=r(=oCBFCr(2)=B+cc1(7C7
add O at position 30,C6@+=]4SO=cBc-4=r(=oCBFCr(2)=BO+cc1(7C7
add O at position 19,C6@+=]4SO=cBc-4=r(=OoCBFCr(2)=BO+cc1(7C7
add n at position 4,C6@+n=]4SO=cBc-4=r(=OoCBFCr(2)=BO+cc1(7C7
replace = at position 10 with 5,C6@+n=]4SO5cBc-4=r(=OoCBFCr(2)=BO+cc1(7C7
remove 2 from position 28,C6@+n=]4SO5cBc-4=r(=OoCBFCr()=BO+cc1(7C7
remove 7 from position 37,C6@+n=]4SO5cBc-4=r(=OoCBFCr()=BO+cc1(C7
add C at position 17,C6@+n=]4SO5cBc-4=Cr(=OoCBFCr()=BO+cc1(C7
add = at position 7,C6@+n=]=4SO5cBc-4=Cr(=OoCBFCr()=BO+cc1(C7
add 2 at position 36,C6@+n=]=4SO5cBc-4=Cr(=OoCBFCr()=BO+c2c1(C7
replace = at position 17 with F,C6@+n=]=4SO5cBc-4FCr(=OoCBFCr()=BO+c2c1(C7
replace + at position 34 with ),C6@+n=]=4SO5cBc-4FCr(=OoCBFCr()=BO)c2c1(C7
add ) at position 36,C6@+n=]=4SO5cBc-4FCr(=OoCBFCr()=BO)c)2c1(C7
add C at position 19,C6@+n=]=4SO5cBc-4FCCr(=OoCBFCr()=BO)c)2c1(C7
replace F at position 17 with n,C6@+n=]=4SO5cBc-4nCCr(=OoCBFCr()=BO)c)2c1(C7
remove 7 from position 43,C6@+n=]=4SO5cBc-4nCCr(=OoCBFCr()=BO)c)2c1(C
add F at position 20,C6@+n=]=4SO5cBc-4nCCFr(=OoCBFCr()=BO)c)2c1(C
replace @ at position 2 with 2,C62+n=]=4SO5cBc-4nCCFr(=OoCBFCr()=BO)c)2c1(C
add ) at position 9,C62+n=]=4)SO5cBc-4nCCFr(=OoCBFCr()=BO)c)2c1(C
remove ( from position 43,C62+n=]=4)SO5cBc-4nCCFr(=OoCBFCr()=BO)c)2c1C
add r at position 10,C62+n=]=4)rSO5cBc-4nCCFr(=OoCBFCr()=BO)c)2c1C
remove ) from position 34,C62+n=]=4)rSO5cBc-4nCCFr(=OoCBFCr(=BO)c)2c1C
remove r from position 10,C62+n=]=4)SO5cBc-4nCCFr(=OoCBFCr(=BO)c)2c1C
add ) at position 8,C62+n=]=)4)SO5cBc-4nCCFr(=OoCBFCr(=BO)c)2c1C
remove B from position 35,C62+n=]=)4)SO5cBc-4nCCFr(=OoCBFCr(=O)c)2c1C
remove F from position 22,C62+n=]=)4)SO5cBc-4nCCr(=OoCBFCr(=O)c)2c1C
replace 5 at position 13 with 3,C62+n=]=)4)SO3cBc-4nCCr(=OoCBFCr(=O)c)2c1C
add - at position 11,C62+n=]=)4)-SO3cBc-4nCCr(=OoCBFCr(=O)c)2c1C
replace r at position 32 with (,C62+n=]=)4)-SO3cBc-4nCCr(=OoCBFC((=O)c)2c1C
remove r from position 23,C62+n=]=)4)-SO3cBc-4nCC(=OoCBFC((=O)c)2c1C
replace F at position 29 with /,C62+n=]=)4)-SO3cBc-4nCC(=OoCB/C((=O)c)2c1C
remove S from position 12,C62+n=]=)4)-O3cBc-4nCC(=OoCB/C((=O)c)2c1C
add c at position 12,C62+n=]=)4)-cO3cBc-4nCC(=OoCB/C((=O)c)2c1C
add I at position 4,C62+In=]=)4)-cO3cBc-4nCC(=OoCB/C((=O)c)2c1C
replace I at position 4 with C,C62+Cn=]=)4)-cO3cBc-4nCC(=OoCB/C((=O)c)2c1C
replace 4 at position 20 with ],C62+Cn=]=)4)-cO3cBc-]nCC(=OoCB/C((=O)c)2c1C
add S at position 5,C62+CSn=]=)4)-cO3cBc-]nCC(=OoCB/C((=O)c)2c1C
replace 2 at position 2 with I,C6I+CSn=]=)4)-cO3cBc-]nCC(=OoCB/C((=O)c)2c1C
add = at position 3,C6I=+CSn=]=)4)-cO3cBc-]nCC(=OoCB/C((=O)c)2c1C
replace + at position 4 with ),C6I=)CSn=]=)4)-cO3cBc-]nCC(=OoCB/C((=O)c)2c1C
replace c at position 20 with n,C6I=)CSn=]=)4)-cO3cBn-]nCC(=OoCB/C((=O)c)2c1C
add 2 at position 17,C6I=)CSn=]=)4)-cO23cBn-]nCC(=OoCB/C((=O)c)2c1C
add @ at position 10,C6I=)CSn=]@=)4)-cO23cBn-]nCC(=OoCB/C((=O)c)2c1C
remove S from position 6,C6I=)Cn=]@=)4)-cO23cBn-]nCC(=OoCB/C((=O)c)2c1C
replace O at position 16 with 2,C6I=)Cn=]@=)4)-c223cBn-]nCC(=OoCB/C((=O)c)2c1C
replace n at position 24 with 2,C6I=)Cn=]@=)4)-c223cBn-]2CC(=OoCB/C((=O)c)2c1C
replace n at position 6 with N,C6I=)CN=]@=)4)-c223cBn-]2CC(=OoCB/C((=O)c)2c1C
add ( at position 7,C6I=)CN(=]@=)4)-c223cBn-]2CC(=OoCB/C((=O)c)2c1C
remove = from position 11,C6I=)CN(=]@)4)-c223cBn-]2CC(=OoCB/C((=O)c)2c1C
replace 3 at position 18 with O,C6I=)CN(=]@)4)-c22OcBn-]2CC(=OoCB/C((=O)c)2c1C
add - at position 33,C6I=)CN(=]@)4)-c22OcBn-]2CC(=OoCB-/C((=O)c)2c1C
replace @ at position 10 with O,C6I=)CN(=]O)4)-c22OcBn-]2CC(=OoCB-/C((=O)c)2c1C
replace o at position 30 with (,C6I=)CN(=]O)4)-c22OcBn-]2CC(=O(CB-/C((=O)c)2c1C
replace ( at position 30 with C,C6I=)CN(=]O)4)-c22OcBn-]2CC(=OCCB-/C((=O)c)2c1C
add c at position 37,C6I=)CN(=]O)4)-c22OcBn-]2CC(=OCCB-/C(c(=O)c)2c1C
add ( at position 22,C6I=)CN(=]O)4)-c22OcBn(-]2CC(=OCCB-/C(c(=O)c)2c1C
replace I at position 2 with C,C6C=)CN(=]O)4)-c22OcBn(-]2CC(=OCCB-/C(c(=O)c)2c1C
remove ] from position 9,C6C=)CN(=O)4)-c22OcBn(-]2CC(=OCCB-/C(c(=O)c)2c1C
replace 4 at position 11 with 1,C6C=)CN(=O)1)-c22OcBn(-]2CC(=OCCB-/C(c(=O)c)2c1C
remove O from position 17,C6C=)CN(=O)1)-c22cBn(-]2CC(=OCCB-/C(c(=O)c)2c1C
add - at position 29,C6C=)CN(=O)1)-c22cBn(-]2CC(=O-CCB-/C(c(=O)c)2c1C
remove - from position 33,C6C=)CN(=O)1)-c22cBn(-]2CC(=O-CCB/C(c(=O)c)2c1C
remove ] from position 22,C6C=)CN(=O)1)-c22cBn(-2CC(=O-CCB/C(c(=O)c)2c1C
remove - from position 13,C6C=)CN(=O)1)c22cBn(-2CC(=O-CCB/C(c(=O)c)2c1C
remove / from position 31,C6C=)CN(=O)1)c22cBn(-2CC(=O-CCBC(c(=O)c)2c1C
add ( at position 3,C6C(=)CN(=O)1)c22cBn(-2CC(=O-CCBC(c(=O)c)2c1C
add + at position 42,C6C(=)CN(=O)1)c22cBn(-2CC(=O-CCBC(c(=O)c)2+c1C
remove - from position 21,C6C(=)CN(=O)1)c22cBn(2CC(=O-CCBC(c(=O)c)2+c1C
add C at position 32,C6C(=)CN(=O)1)c22cBn(2CC(=O-CCBCC(c(=O)c)2+c1C
add 3 at position 33,C6C(=)CN(=O)1)c22cBn(2CC(=O-CCBCC3(c(=O)c)2+c1C
add c at position 12,C6C(=)CN(=O)c1)c22cBn(2CC(=O-CCBCC3(c(=O)c)2+c1C
replace 2 at position 17 with n,C6C(=)CN(=O)c1)c2ncBn(2CC(=O-CCBCC3(c(=O)c)2+c1C
remove B from position 19,C6C(=)CN(=O)c1)c2ncn(2CC(=O-CCBCC3(c(=O)c)2+c1C
add ) at position 35,C6C(=)CN(=O)c1)c2ncn(2CC(=O-CCBCC3()c(=O)c)2+c1C
add O at position 2,C6OC(=)CN(=O)c1)c2ncn(2CC(=O-CCBCC3()c(=O)c)2+c1C
remove ) from position 43,C6OC(=)CN(=O)c1)c2ncn(2CC(=O-CCBCC3()c(=O)c2+c1C
add 3 at position 29,C6OC(=)CN(=O)c1)c2ncn(2CC(=O-3CCBCC3()c(=O)c2+c1C
replace ) at position 15 with s,C6OC(=)CN(=O)c1sc2ncn(2CC(=O-3CCBCC3()c(=O)c2+c1C
replace B at position 32 with C,C6OC(=)CN(=O)c1sc2ncn(2CC(=O-3CCCCC3()c(=O)c2+c1C
remove + from position 45,C6OC(=)CN(=O)c1sc2ncn(2CC(=O-3CCCCC3()c(=O)c2c1C
add C at position 9,C6OC(=)CNC(=O)c1sc2ncn(2CC(=O-3CCCCC3()c(=O)c2c1C
add N at position 30,C6OC(=)CNC(=O)c1sc2ncn(2CC(=O-N3CCCCC3()c(=O)c2c1C
remove 6 from position 1,COC(=)CNC(=O)c1sc2ncn(2CC(=O-N3CCCCC3()c(=O)c2c1C
remove 2 from position 22,COC(=)CNC(=O)c1sc2ncn(CC(=O-N3CCCCC3()c(=O)c2c1C
add O at position 5,COC(=O)CNC(=O)c1sc2ncn(CC(=O-N3CCCCC3()c(=O)c2c1C
remove ( from position 37,COC(=O)CNC(=O)c1sc2ncn(CC(=O-N3CCCCC3)c(=O)c2c1C
replace - at position 28 with ),COC(=O)CNC(=O)c1sc2ncn(CC(=O)N3CCCCC3)c(=O)c2c1C
final: COC(=O)CNC(=O)c1sc2ncn(CC(=O)N3CCCCC3)c(=O)c2c1C,COC(=O)CNC(=O)c1sc2ncn(CC(=O)N3CCCCC3)c(=O)c2c1C
