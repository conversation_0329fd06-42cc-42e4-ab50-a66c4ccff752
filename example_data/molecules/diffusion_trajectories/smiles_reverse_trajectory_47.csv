log,state
initialize: ,
add N at position 0,N
add I at position 0,IN
add 1 at position 0,1IN
replace N at position 2 with B,1IB
remove 1 from position 0,IB
add = at position 0,=IB
replace I at position 1 with -,=-B
add B at position 0,B=-B
add C at position 1,BC=-B
replace <PERSON> at position 1 with 6,B6=-B
replace = at position 2 with n,B6n-B
replace <PERSON> at position 0 with 5,56n-<PERSON>
replace <PERSON> at position 4 with [,56n-[
replace [ at position 4 with 6,56n-6
remove 6 from position 1,5n-6
replace 5 at position 0 with r,rn-6
add - at position 3,rn--6
remove r from position 0,n--6
add ( at position 3,n--(6
add H at position 4,n--(H6
add ) at position 4,n--()H6
replace - at position 2 with /,n-/()H6
add ) at position 6,n-/()H)6
add ( at position 3,n-/(()H)6
replace - at position 1 with l,nl/(()H)6
add n at position 2,nln/(()H)6
replace / at position 3 with 4,nln4(()H)6
add C at position 5,nln4(C()H)6
add c at position 0,cnln4(C()H)6
remove ) from position 8,cnln4(C(H)6
add - at position 9,cnln4(C(H-)6
replace 4 at position 4 with -,cnln-(C(H-)6
add 6 at position 8,cnln-(C(6H-)6
replace 6 at position 8 with s,cnln-(C(sH-)6
replace - at position 10 with C,cnln-(C(sHC)6
add C at position 0,Ccnln-(C(sHC)6
replace ( at position 6 with 5,Ccnln-5C(sHC)6
remove - from position 5,Ccnln5C(sHC)6
add s at position 13,Ccnln5C(sHC)6s
replace H at position 9 with r,Ccnln5C(srC)6s
add # at position 1,C#cnln5C(srC)6s
add 4 at position 10,C#cnln5C(s4rC)6s
remove 5 from position 6,C#cnlnC(s4rC)6s
add H at position 8,C#cnlnC(Hs4rC)6s
add 4 at position 9,C#cnlnC(H4s4rC)6s
add 6 at position 10,C#cnlnC(H46s4rC)6s
replace 4 at position 9 with 5,C#cnlnC(H56s4rC)6s
replace 6 at position 10 with s,C#cnlnC(H5ss4rC)6s
remove ) from position 15,C#cnlnC(H5ss4rC6s
add o at position 3,C#conlnC(H5ss4rC6s
add 7 at position 14,C#conlnC(H5ss47rC6s
add l at position 15,C#conlnC(H5ss47lrC6s
remove r from position 16,C#conlnC(H5ss47lC6s
add C at position 11,C#conlnC(H5Css47lC6s
add ) at position 11,C#conlnC(H5)Css47lC6s
add 7 at position 5,C#con7lnC(H5)Css47lC6s
replace 7 at position 5 with [,C#con[lnC(H5)Css47lC6s
add S at position 21,C#con[lnC(H5)Css47lC6Ss
add F at position 9,C#con[lnCF(H5)Css47lC6Ss
add I at position 2,C#Icon[lnCF(H5)Css47lC6Ss
add s at position 12,C#Icon[lnCF(sH5)Css47lC6Ss
add ( at position 24,C#Icon[lnCF(sH5)Css47lC6(Ss
add ] at position 26,C#Icon[lnCF(sH5)Css47lC6(S]s
add C at position 1,CC#Icon[lnCF(sH5)Css47lC6(S]s
remove l from position 22,CC#Icon[lnCF(sH5)Css47C6(S]s
add O at position 8,CC#Icon[OlnCF(sH5)Css47C6(S]s
add N at position 3,CC#NIcon[OlnCF(sH5)Css47C6(S]s
add ] at position 18,CC#NIcon[OlnCF(sH5])Css47C6(S]s
add 1 at position 26,CC#NIcon[OlnCF(sH5])Css47C16(S]s
replace c at position 5 with C,CC#NICon[OlnCF(sH5])Css47C16(S]s
add r at position 9,CC#NICon[rOlnCF(sH5])Css47C16(S]s
add l at position 19,CC#NICon[rOlnCF(sH5l])Css47C16(S]s
replace H at position 17 with 1,CC#NICon[rOlnCF(s15l])Css47C16(S]s
replace s at position 24 with @,CC#NICon[rOlnCF(s15l])Cs@47C16(S]s
add c at position 13,CC#NICon[rOlncCF(s15l])Cs@47C16(S]s
remove 7 from position 27,CC#NICon[rOlncCF(s15l])Cs@4C16(S]s
remove r from position 9,CC#NICon[OlncCF(s15l])Cs@4C16(S]s
remove N from position 3,CC#ICon[OlncCF(s15l])Cs@4C16(S]s
add + at position 20,CC#ICon[OlncCF(s15l]+)Cs@4C16(S]s
remove l from position 9,CC#ICon[OncCF(s15l]+)Cs@4C16(S]s
remove I from position 3,CC#Con[OncCF(s15l]+)Cs@4C16(S]s
replace 5 at position 15 with =,CC#Con[OncCF(s1=l]+)Cs@4C16(S]s
remove ] from position 29,CC#Con[OncCF(s1=l]+)Cs@4C16(Ss
remove F from position 11,CC#Con[OncC(s1=l]+)Cs@4C16(Ss
add C at position 9,CC#Con[OnCcC(s1=l]+)Cs@4C16(Ss
add o at position 30,CC#Con[OnCcC(s1=l]+)Cs@4C16(Sso
remove S from position 28,CC#Con[OnCcC(s1=l]+)Cs@4C16(so
replace o at position 29 with n,CC#Con[OnCcC(s1=l]+)Cs@4C16(sn
add c at position 26,CC#Con[OnCcC(s1=l]+)Cs@4C1c6(sn
add / at position 29,CC#Con[OnCcC(s1=l]+)Cs@4C1c6(/sn
remove 1 from position 25,CC#Con[OnCcC(s1=l]+)Cs@4Cc6(/sn
remove s from position 13,CC#Con[OnCcC(1=l]+)Cs@4Cc6(/sn
remove n from position 8,CC#Con[OCcC(1=l]+)Cs@4Cc6(/sn
add - at position 14,CC#Con[OCcC(1=-l]+)Cs@4Cc6(/sn
add ] at position 10,CC#Con[OCc]C(1=-l]+)Cs@4Cc6(/sn
remove s from position 29,CC#Con[OCc]C(1=-l]+)Cs@4Cc6(/n
remove l from position 16,CC#Con[OCc]C(1=-]+)Cs@4Cc6(/n
remove c from position 9,CC#Con[OC]C(1=-]+)Cs@4Cc6(/n
remove 1 from position 12,CC#Con[OC]C(=-]+)Cs@4Cc6(/n
replace ] at position 14 with 7,CC#Con[OC]C(=-7+)Cs@4Cc6(/n
replace 6 at position 23 with ),CC#Con[OC]C(=-7+)Cs@4Cc)(/n
replace o at position 4 with 4,CC#C4n[OC]C(=-7+)Cs@4Cc)(/n
replace s at position 18 with C,CC#C4n[OC]C(=-7+)CC@4Cc)(/n
remove c from position 22,CC#C4n[OC]C(=-7+)CC@4C)(/n
add ] at position 11,CC#C4n[OC]C](=-7+)CC@4C)(/n
replace 4 at position 21 with (,CC#C4n[OC]C](=-7+)CC@(C)(/n
replace 7 at position 15 with C,CC#C4n[OC]C](=-C+)CC@(C)(/n
replace + at position 16 with =,CC#C4n[OC]C](=-C=)CC@(C)(/n
replace = at position 13 with o,CC#C4n[OC]C](o-C=)CC@(C)(/n
add N at position 3,CC#NC4n[OC]C](o-C=)CC@(C)(/n
add O at position 14,CC#NC4n[OC]C](Oo-C=)CC@(C)(/n
add o at position 24,CC#NC4n[OC]C](Oo-C=)CC@(oC)(/n
replace / at position 28 with O,CC#NC4n[OC]C](Oo-C=)CC@(oC)(On
replace o at position 15 with (,CC#NC4n[OC]C](O(-C=)CC@(oC)(On
replace ( at position 15 with N,CC#NC4n[OC]C](ON-C=)CC@(oC)(On
add ( at position 18,CC#NC4n[OC]C](ON-C(=)CC@(oC)(On
add [ at position 22,CC#NC4n[OC]C](ON-C(=)C[C@(oC)(On
remove ( from position 29,CC#NC4n[OC]C](ON-C(=)C[C@(oC)On
remove n from position 30,CC#NC4n[OC]C](ON-C(=)C[C@(oC)O
replace 4 at position 5 with ),CC#NC)n[OC]C](ON-C(=)C[C@(oC)O
remove O from position 8,CC#NC)n[C]C](ON-C(=)C[C@(oC)O
add C at position 14,CC#NC)n[C]C](OCN-C(=)C[C@(oC)O
remove - from position 16,CC#NC)n[C]C](OCNC(=)C[C@(oC)O
remove ] from position 11,CC#NC)n[C]C(OCNC(=)C[C@(oC)O
add C at position 4,CC#NCC)n[C]C(OCNC(=)C[C@(oC)O
add H at position 2,CCH#NCC)n[C]C(OCNC(=)C[C@(oC)O
remove H from position 2,CC#NCC)n[C]C(OCNC(=)C[C@(oC)O
add ) at position 12,CC#NCC)n[C]C)(OCNC(=)C[C@(oC)O
add C at position 22,CC#NCC)n[C]C)(OCNC(=)CC[C@(oC)O
replace # at position 2 with O,CCONCC)n[C]C)(OCNC(=)CC[C@(oC)O
add 6 at position 1,C6CONCC)n[C]C)(OCNC(=)CC[C@(oC)O
add ( at position 12,C6CONCC)n[C](C)(OCNC(=)CC[C@(oC)O
add O at position 22,C6CONCC)n[C](C)(OCNC(=O)CC[C@(oC)O
replace N at position 4 with S,C6COSCC)n[C](C)(OCNC(=O)CC[C@(oC)O
add # at position 17,C6COSCC)n[C](C)(O#CNC(=O)CC[C@(oC)O
remove C from position 5,C6COSC)n[C](C)(O#CNC(=O)CC[C@(oC)O
add + at position 29,C6COSC)n[C](C)(O#CNC(=O)CC[C@+(oC)O
replace S at position 4 with (,C6CO(C)n[C](C)(O#CNC(=O)CC[C@+(oC)O
remove o from position 31,C6CO(C)n[C](C)(O#CNC(=O)CC[C@+(C)O
remove n from position 7,C6CO(C)[C](C)(O#CNC(=O)CC[C@+(C)O
add @ at position 9,C6CO(C)[C@](C)(O#CNC(=O)CC[C@+(C)O
add ] at position 30,C6CO(C)[C@](C)(O#CNC(=O)CC[C@+](C)O
remove 6 from position 1,CCO(C)[C@](C)(O#CNC(=O)CC[C@+](C)O
replace # at position 15 with ),CCO(C)[C@](C)(O)CNC(=O)CC[C@+](C)O
add C at position 6,CCO(C)C[C@](C)(O)CNC(=O)CC[C@+](C)O
remove O from position 2,CC(C)C[C@](C)(O)CNC(=O)CC[C@+](C)O
replace + at position 28 with H,CC(C)C[C@](C)(O)CNC(=O)CC[C@H](C)O
final: CC(C)C[C@](C)(O)CNC(=O)CC[C@H](C)O,CC(C)C[C@](C)(O)CNC(=O)CC[C@H](C)O
