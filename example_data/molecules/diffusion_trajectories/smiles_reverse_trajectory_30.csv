log,state
initialize: ,
add S at position 0,S
add O at position 0,OS
add o at position 0,o<PERSON>
replace S at position 2 with [,oO[
add [ at position 1,o[O[
add 1 at position 1,o1[O[
replace [ at position 2 with 6,o16O[
remove 6 from position 2,o1O[
add I at position 4,o1O[I
replace I at position 4 with (,o1O[(
remove ( from position 4,o1O[
add 5 at position 2,o15O[
remove o from position 0,15O[
add F at position 2,15FO[
add / at position 3,15F/O[
add I at position 5,15F/OI[
add C at position 7,15F/OI[C
replace O at position 4 with 3,15F/3I[C
replace F at position 2 with B,15B/3I[C
remove 1 from position 0,5B/3I[C
add = at position 2,5B=/3I[C
replace I at position 5 with 3,5B=/33[C
add B at position 0,B5B=/33[C
add 6 at position 3,B5B6=/33[C
replace B at position 2 with o,B5o6=/33[C
replace = at position 4 with r,B5o6r/33[C
replace B at position 0 with 1,15o6r/33[C
replace C at position 9 with /,15o6r/33[/
replace [ at position 8 with ),15o6r/33)/
remove 6 from position 3,15or/33)/
replace 5 at position 1 with r,1ror/33)/
add - at position 6,1ror/3-3)/
remove r from position 1,1or/3-3)/
add H at position 7,1or/3-3H)/
add B at position 9,1or/3-3H)B/
add S at position 8,1or/3-3HS)B/
replace - at position 5 with 7,1or/373HS)B/
replace S at position 8 with s,1or/373Hs)B/
replace / at position 11 with c,1or/373Hs)Bc
remove s from position 8,1or/373H)Bc
add 5 at position 9,1or/373H)5Bc
replace r at position 2 with ],1o]/373H)5Bc
add 5 at position 10,1o]/373H)55Bc
replace / at position 3 with [,1o][373H)55Bc
remove 5 from position 9,1o][373H)5Bc
add s at position 12,1o][373H)5Bcs
remove ) from position 8,1o][373H5Bcs
add @ at position 9,1o][373H5@Bcs
replace 3 at position 4 with @,1o][@73H5@Bcs
replace o at position 1 with 7,17][@73H5@Bcs
add 1 at position 6,17][@713H5@Bcs
add ) at position 5,17][@)713H5@Bcs
remove 1 from position 0,7][@)713H5@Bcs
remove 7 from position 0,][@)713H5@Bcs
replace 7 at position 4 with ),][@))13H5@Bcs
add ( at position 1,]([@))13H5@Bcs
remove H from position 8,]([@))135@Bcs
replace 3 at position 7 with r,]([@))1r5@Bcs
remove 5 from position 8,]([@))1r@Bcs
add / at position 9,]([@))1r@/Bcs
replace ) at position 4 with ],]([@])1r@/Bcs
add 4 at position 8,]([@])1r4@/Bcs
add o at position 4,]([@o])1r4@/Bcs
add 7 at position 10,]([@o])1r47@/Bcs
replace 4 at position 9 with ],]([@o])1r]7@/Bcs
replace 7 at position 10 with +,]([@o])1r]+@/Bcs
remove o from position 4,]([@])1r]+@/Bcs
remove B from position 12,]([@])1r]+@/cs
add N at position 1,]N([@])1r]+@/cs
replace @ at position 11 with s,]N([@])1r]+s/cs
add # at position 0,#]N([@])1r]+s/cs
remove c from position 14,#]N([@])1r]+s/s
add 4 at position 11,#]N([@])1r]4+s/s
add ( at position 11,#]N([@])1r](4+s/s
remove / from position 15,#]N([@])1r](4+ss
add / at position 15,#]N([@])1r](4+s/s
remove 4 from position 12,#]N([@])1r](+s/s
add I at position 0,I#]N([@])1r](+s/s
remove / from position 15,I#]N([@])1r](+ss
add r at position 14,I#]N([@])1r](+rss
remove ( from position 12,I#]N([@])1r]+rss
add s at position 8,I#]N([@]s)1r]+rss
add = at position 3,I#]=N([@]s)1r]+rss
add o at position 18,I#]=N([@]s)1r]+rsso
add l at position 13,I#]=N([@]s)1rl]+rsso
replace ] at position 2 with l,I#l=N([@]s)1rl]+rsso
add o at position 4,I#l=oN([@]s)1rl]+rsso
add 6 at position 21,I#l=oN([@]s)1rl]+rsso6
add F at position 9,I#l=oN([@F]s)1rl]+rsso6
add c at position 8,I#l=oN([c@F]s)1rl]+rsso6
add 5 at position 21,I#l=oN([c@F]s)1rl]+rs5so6
add ) at position 23,I#l=oN([c@F]s)1rl]+rs5s)o6
replace ( at position 6 with 4,I#l=oN4[c@F]s)1rl]+rs5s)o6
add 3 at position 24,I#l=oN4[c@F]s)1rl]+rs5s)3o6
add C at position 1,IC#l=oN4[c@F]s)1rl]+rs5s)3o6
add 6 at position 0,6IC#l=oN4[c@F]s)1rl]+rs5s)3o6
add 1 at position 22,6IC#l=oN4[c@F]s)1rl]+r1s5s)3o6
remove l from position 4,6IC#=oN4[c@F]s)1rl]+r1s5s)3o6
remove I from position 1,6C#=oN4[c@F]s)1rl]+r1s5s)3o6
add s at position 10,6C#=oN4[c@sF]s)1rl]+r1s5s)3o6
remove s from position 24,6C#=oN4[c@sF]s)1rl]+r1s5)3o6
remove F from position 11,6C#=oN4[c@s]s)1rl]+r1s5)3o6
replace 4 at position 6 with n,6C#=oNn[c@s]s)1rl]+r1s5)3o6
add n at position 5,6C#=onNn[c@s]s)1rl]+r1s5)3o6
replace r at position 16 with ),6C#=onNn[c@s]s)1)l]+r1s5)3o6
replace s at position 11 with 1,6C#=onNn[c@1]s)1)l]+r1s5)3o6
add O at position 7,6C#=onNOn[c@1]s)1)l]+r1s5)3o6
add c at position 28,6C#=onNOn[c@1]s)1)l]+r1s5)3oc6
remove 1 from position 12,6C#=onNOn[c@]s)1)l]+r1s5)3oc6
remove s from position 13,6C#=onNOn[c@])1)l]+r1s5)3oc6
remove n from position 8,6C#=onNO[c@])1)l]+r1s5)3oc6
add = at position 14,6C#=onNO[c@])1=)l]+r1s5)3oc6
remove 5 from position 22,6C#=onNO[c@])1=)l]+r1s)3oc6
remove l from position 16,6C#=onNO[c@])1=)]+r1s)3oc6
remove c from position 9,6C#=onNO[@])1=)]+r1s)3oc6
remove 1 from position 12,6C#=onNO[@])=)]+r1s)3oc6
replace ] at position 14 with 7,6C#=onNO[@])=)7+r1s)3oc6
replace 6 at position 23 with -,6C#=onNO[@])=)7+r1s)3oc-
replace o at position 4 with 3,6C#=3nNO[@])=)7+r1s)3oc-
replace s at position 18 with ),6C#=3nNO[@])=)7+r1))3oc-
remove c from position 22,6C#=3nNO[@])=)7+r1))3o-
add r at position 11,6C#=3nNO[@]r)=)7+r1))3o-
replace 3 at position 21 with ],6C#=3nNO[@]r)=)7+r1))]o-
replace 7 at position 15 with C,6C#=3nNO[@]r)=)C+r1))]o-
replace + at position 16 with C,6C#=3nNO[@]r)=)CCr1))]o-
replace = at position 13 with O,6C#=3nNO[@]r)O)CCr1))]o-
add N at position 3,6C#N=3nNO[@]r)O)CCr1))]o-
add - at position 14,6C#N=3nNO[@]r)-O)CCr1))]o-
remove ] from position 23,6C#N=3nNO[@]r)-O)CCr1))o-
replace r at position 19 with O,6C#N=3nNO[@]r)-O)CCO1))o-
add C at position 25,6C#N=3nNO[@]r)-O)CCO1))o-C
add = at position 14,6C#N=3nNO[@]r)=-O)CCO1))o-C
add @ at position 10,6C#N=3nNO[@@]r)=-O)CCO1))o-C
add C at position 24,6C#N=3nNO[@@]r)=-O)CCO1)C)o-C
replace ) at position 14 with (,6C#N=3nNO[@@]r(=-O)CCO1)C)o-C
remove - from position 27,6C#N=3nNO[@@]r(=-O)CCO1)C)oC
replace 3 at position 5 with O,6C#N=OnNO[@@]r(=-O)CCO1)C)oC
remove O from position 8,6C#N=OnN[@@]r(=-O)CCO1)C)oC
add C at position 14,6C#N=OnN[@@]r(C=-O)CCO1)C)oC
remove - from position 16,6C#N=OnN[@@]r(C=O)CCO1)C)oC
remove ] from position 11,6C#N=OnN[@@r(C=O)CCO1)C)oC
add C at position 4,6C#NC=OnN[@@r(C=O)CCO1)C)oC
add H at position 2,6CH#NC=OnN[@@r(C=O)CCO1)C)oC
remove H from position 2,6C#NC=OnN[@@r(C=O)CCO1)C)oC
add H at position 12,6C#NC=OnN[@@Hr(C=O)CCO1)C)oC
add C at position 22,6C#NC=OnN[@@Hr(C=O)CCOC1)C)oC
replace # at position 2 with O,6CONC=OnN[@@Hr(C=O)CCOC1)C)oC
add C at position 0,C6CONC=OnN[@@Hr(C=O)CCOC1)C)oC
replace r at position 14 with ],C6CONC=OnN[@@H](C=O)CCOC1)C)oC
add N at position 20,C6CONC=OnN[@@H](C=O)NCCOC1)C)oC
add 1 at position 22,C6CONC=OnN[@@H](C=O)NC1COC1)C)oC
replace N at position 4 with S,C6COSC=OnN[@@H](C=O)NC1COC1)C)oC
add # at position 17,C6COSC=OnN[@@H](C#=O)NC1COC1)C)oC
remove C from position 5,C6COS=OnN[@@H](C#=O)NC1COC1)C)oC
add - at position 29,C6COS=OnN[@@H](C#=O)NC1COC1)C-)oC
replace S at position 4 with (,C6CO(=OnN[@@H](C#=O)NC1COC1)C-)oC
remove o from position 31,C6CO(=OnN[@@H](C#=O)NC1COC1)C-)C
remove n from position 7,C6CO(=ON[@@H](C#=O)NC1COC1)C-)C
add C at position 9,C6CO(=ON[C@@H](C#=O)NC1COC1)C-)C
add C at position 30,C6CO(=ON[C@@H](C#=O)NC1COC1)C-C)C
remove 6 from position 1,CCO(=ON[C@@H](C#=O)NC1COC1)C-C)C
replace # at position 15 with (,CCO(=ON[C@@H](C(=O)NC1COC1)C-C)C
add ) at position 6,CCO(=O)N[C@@H](C(=O)NC1COC1)C-C)C
remove O from position 2,CC(=O)N[C@@H](C(=O)NC1COC1)C-C)C
replace - at position 28 with (,CC(=O)N[C@@H](C(=O)NC1COC1)C(C)C
final: CC(=O)N[C@@H](C(=O)NC1COC1)C(C)C,CC(=O)N[C@@H](C(=O)NC1COC1)C(C)C
