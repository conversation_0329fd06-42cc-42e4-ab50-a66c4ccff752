log,state
initialize: CC(C)C[C@](C)(O)CNC(=O)CC[C@H](C)O,CC(C)C[C@](C)(O)CNC(=O)CC[C@H](C)O
replace H at position 28 with +,CC(C)C[C@](C)(O)CNC(=O)CC[C@+](C)O
add O at position 2,CCO(C)C[C@](C)(O)CNC(=O)CC[C@+](C)O
remove C from position 6,CCO(C)[C@](C)(O)CNC(=O)CC[C@+](C)O
replace ) at position 15 with #,CCO(C)[C@](C)(O#CNC(=O)CC[C@+](C)O
add 6 at position 1,C6CO(C)[C@](C)(O#CNC(=O)CC[C@+](C)O
remove ] from position 30,C6CO(C)[C@](C)(O#CNC(=O)CC[C@+(C)O
remove @ from position 9,C6CO(C)[C](C)(O#CNC(=O)CC[C@+(C)O
add n at position 7,C6CO(C)n[C](C)(O#CNC(=O)CC[C@+(C)O
add o at position 31,C6CO(C)n[C](C)(O#CNC(=O)CC[C@+(oC)O
replace ( at position 4 with S,C6COSC)n[C](C)(O#CNC(=O)CC[C@+(oC)O
remove + from position 29,C6COSC)n[C](C)(O#CNC(=O)CC[C@(oC)O
add C at position 5,C6COSCC)n[C](C)(O#CNC(=O)CC[C@(oC)O
remove # from position 17,C6COSCC)n[C](C)(OCNC(=O)CC[C@(oC)O
replace S at position 4 with N,C6CONCC)n[C](C)(OCNC(=O)CC[C@(oC)O
remove O from position 22,C6CONCC)n[C](C)(OCNC(=)CC[C@(oC)O
remove ( from position 12,C6CONCC)n[C]C)(OCNC(=)CC[C@(oC)O
remove 6 from position 1,CCONCC)n[C]C)(OCNC(=)CC[C@(oC)O
replace O at position 2 with #,CC#NCC)n[C]C)(OCNC(=)CC[C@(oC)O
remove C from position 22,CC#NCC)n[C]C)(OCNC(=)C[C@(oC)O
remove ) from position 12,CC#NCC)n[C]C(OCNC(=)C[C@(oC)O
add H at position 2,CCH#NCC)n[C]C(OCNC(=)C[C@(oC)O
remove H from position 2,CC#NCC)n[C]C(OCNC(=)C[C@(oC)O
remove C from position 4,CC#NC)n[C]C(OCNC(=)C[C@(oC)O
add ] at position 11,CC#NC)n[C]C](OCNC(=)C[C@(oC)O
add - at position 16,CC#NC)n[C]C](OCN-C(=)C[C@(oC)O
remove C from position 14,CC#NC)n[C]C](ON-C(=)C[C@(oC)O
add O at position 8,CC#NC)n[OC]C](ON-C(=)C[C@(oC)O
replace ) at position 5 with 4,CC#NC4n[OC]C](ON-C(=)C[C@(oC)O
add n at position 30,CC#NC4n[OC]C](ON-C(=)C[C@(oC)On
add ( at position 29,CC#NC4n[OC]C](ON-C(=)C[C@(oC)(On
remove [ from position 22,CC#NC4n[OC]C](ON-C(=)CC@(oC)(On
remove ( from position 18,CC#NC4n[OC]C](ON-C=)CC@(oC)(On
replace N at position 15 with (,CC#NC4n[OC]C](O(-C=)CC@(oC)(On
replace ( at position 15 with o,CC#NC4n[OC]C](Oo-C=)CC@(oC)(On
replace O at position 28 with /,CC#NC4n[OC]C](Oo-C=)CC@(oC)(/n
remove o from position 24,CC#NC4n[OC]C](Oo-C=)CC@(C)(/n
remove O from position 14,CC#NC4n[OC]C](o-C=)CC@(C)(/n
remove N from position 3,CC#C4n[OC]C](o-C=)CC@(C)(/n
replace o at position 13 with =,CC#C4n[OC]C](=-C=)CC@(C)(/n
replace = at position 16 with +,CC#C4n[OC]C](=-C+)CC@(C)(/n
replace C at position 15 with 7,CC#C4n[OC]C](=-7+)CC@(C)(/n
replace ( at position 21 with 4,CC#C4n[OC]C](=-7+)CC@4C)(/n
remove ] from position 11,CC#C4n[OC]C(=-7+)CC@4C)(/n
add c at position 22,CC#C4n[OC]C(=-7+)CC@4Cc)(/n
replace C at position 18 with s,CC#C4n[OC]C(=-7+)Cs@4Cc)(/n
replace 4 at position 4 with o,CC#Con[OC]C(=-7+)Cs@4Cc)(/n
replace ) at position 23 with 6,CC#Con[OC]C(=-7+)Cs@4Cc6(/n
replace 7 at position 14 with ],CC#Con[OC]C(=-]+)Cs@4Cc6(/n
add 1 at position 12,CC#Con[OC]C(1=-]+)Cs@4Cc6(/n
add c at position 9,CC#Con[OCc]C(1=-]+)Cs@4Cc6(/n
add l at position 16,CC#Con[OCc]C(1=-l]+)Cs@4Cc6(/n
add s at position 29,CC#Con[OCc]C(1=-l]+)Cs@4Cc6(/sn
remove ] from position 10,CC#Con[OCcC(1=-l]+)Cs@4Cc6(/sn
remove - from position 14,CC#Con[OCcC(1=l]+)Cs@4Cc6(/sn
add n at position 8,CC#Con[OnCcC(1=l]+)Cs@4Cc6(/sn
add s at position 13,CC#Con[OnCcC(s1=l]+)Cs@4Cc6(/sn
add 1 at position 25,CC#Con[OnCcC(s1=l]+)Cs@4C1c6(/sn
remove / from position 29,CC#Con[OnCcC(s1=l]+)Cs@4C1c6(sn
remove c from position 26,CC#Con[OnCcC(s1=l]+)Cs@4C16(sn
replace n at position 29 with o,CC#Con[OnCcC(s1=l]+)Cs@4C16(so
add S at position 28,CC#Con[OnCcC(s1=l]+)Cs@4C16(Sso
remove o from position 30,CC#Con[OnCcC(s1=l]+)Cs@4C16(Ss
remove C from position 9,CC#Con[OncC(s1=l]+)Cs@4C16(Ss
add F at position 11,CC#Con[OncCF(s1=l]+)Cs@4C16(Ss
add ] at position 29,CC#Con[OncCF(s1=l]+)Cs@4C16(S]s
replace = at position 15 with 5,CC#Con[OncCF(s15l]+)Cs@4C16(S]s
add I at position 3,CC#ICon[OncCF(s15l]+)Cs@4C16(S]s
add l at position 9,CC#ICon[OlncCF(s15l]+)Cs@4C16(S]s
remove + from position 20,CC#ICon[OlncCF(s15l])Cs@4C16(S]s
add N at position 3,CC#NICon[OlncCF(s15l])Cs@4C16(S]s
add r at position 9,CC#NICon[rOlncCF(s15l])Cs@4C16(S]s
add 7 at position 27,CC#NICon[rOlncCF(s15l])Cs@47C16(S]s
remove c from position 13,CC#NICon[rOlnCF(s15l])Cs@47C16(S]s
replace @ at position 24 with s,CC#NICon[rOlnCF(s15l])Css47C16(S]s
replace 1 at position 17 with H,CC#NICon[rOlnCF(sH5l])Css47C16(S]s
remove l from position 19,CC#NICon[rOlnCF(sH5])Css47C16(S]s
remove r from position 9,CC#NICon[OlnCF(sH5])Css47C16(S]s
replace C at position 5 with c,CC#NIcon[OlnCF(sH5])Css47C16(S]s
remove 1 from position 26,CC#NIcon[OlnCF(sH5])Css47C6(S]s
remove ] from position 18,CC#NIcon[OlnCF(sH5)Css47C6(S]s
remove N from position 3,CC#Icon[OlnCF(sH5)Css47C6(S]s
remove O from position 8,CC#Icon[lnCF(sH5)Css47C6(S]s
add l at position 22,CC#Icon[lnCF(sH5)Css47lC6(S]s
remove C from position 1,C#Icon[lnCF(sH5)Css47lC6(S]s
remove ] from position 26,C#Icon[lnCF(sH5)Css47lC6(Ss
remove ( from position 24,C#Icon[lnCF(sH5)Css47lC6Ss
remove s from position 12,C#Icon[lnCF(H5)Css47lC6Ss
remove I from position 2,C#con[lnCF(H5)Css47lC6Ss
remove F from position 9,C#con[lnC(H5)Css47lC6Ss
remove S from position 21,C#con[lnC(H5)Css47lC6s
replace [ at position 5 with 7,C#con7lnC(H5)Css47lC6s
remove 7 from position 5,C#conlnC(H5)Css47lC6s
remove ) from position 11,C#conlnC(H5Css47lC6s
remove C from position 11,C#conlnC(H5ss47lC6s
add r at position 16,C#conlnC(H5ss47lrC6s
remove l from position 15,C#conlnC(H5ss47rC6s
remove 7 from position 14,C#conlnC(H5ss4rC6s
remove o from position 3,C#cnlnC(H5ss4rC6s
add ) at position 15,C#cnlnC(H5ss4rC)6s
replace s at position 10 with 6,C#cnlnC(H56s4rC)6s
replace 5 at position 9 with 4,C#cnlnC(H46s4rC)6s
remove 6 from position 10,C#cnlnC(H4s4rC)6s
remove 4 from position 9,C#cnlnC(Hs4rC)6s
remove H from position 8,C#cnlnC(s4rC)6s
add 5 at position 6,C#cnln5C(s4rC)6s
remove 4 from position 10,C#cnln5C(srC)6s
remove # from position 1,Ccnln5C(srC)6s
replace r at position 9 with H,Ccnln5C(sHC)6s
remove s from position 13,Ccnln5C(sHC)6
add - at position 5,Ccnln-5C(sHC)6
replace 5 at position 6 with (,Ccnln-(C(sHC)6
remove C from position 0,cnln-(C(sHC)6
replace C at position 10 with -,cnln-(C(sH-)6
replace s at position 8 with 6,cnln-(C(6H-)6
remove 6 from position 8,cnln-(C(H-)6
replace - at position 4 with 4,cnln4(C(H-)6
remove - from position 9,cnln4(C(H)6
add ) at position 8,cnln4(C()H)6
remove c from position 0,nln4(C()H)6
remove C from position 5,nln4(()H)6
replace 4 at position 3 with /,nln/(()H)6
remove n from position 2,nl/(()H)6
replace l at position 1 with -,n-/(()H)6
remove ( from position 3,n-/()H)6
remove ) from position 6,n-/()H6
replace / at position 2 with -,n--()H6
remove ) from position 4,n--(H6
remove H from position 4,n--(6
remove ( from position 3,n--6
add r at position 0,rn--6
remove - from position 3,rn-6
replace r at position 0 with 5,5n-6
add 6 at position 1,56n-6
replace 6 at position 4 with [,56n-[
replace [ at position 4 with B,56n-B
replace 5 at position 0 with B,B6n-B
replace n at position 2 with =,B6=-B
replace 6 at position 1 with C,BC=-B
remove C from position 1,B=-B
remove B from position 0,=-B
replace - at position 1 with I,=IB
remove = from position 0,IB
add 1 at position 0,1IB
replace B at position 2 with N,1IN
remove 1 from position 0,IN
remove I from position 0,N
remove N from position 0,
final: ,
