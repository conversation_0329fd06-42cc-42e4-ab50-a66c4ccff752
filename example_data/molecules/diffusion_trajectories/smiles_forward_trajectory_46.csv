log,state
initialize: CC[NH+]1C[C@H](c2ccccc2)CC2(CCN(C(=O)c3ccon3)CC2)C1,CC[NH+]1C[C@H](c2ccccc2)CC2(CCN(C(=O)c3ccon3)CC2)C1
replace C at position 28 with +,CC[NH+]1C[C@H](c2ccccc2)CC2(+CN(C(=O)c3ccon3)CC2)C1
add H at position 51,CC[NH+]1C[C@H](c2ccccc2)CC2(+CN(C(=O)c3ccon3)CC2)C1H
add ) at position 44,CC[NH+]1C[C@H](c2ccccc2)CC2(+CN(C(=O)c3ccon3))CC2)C1H
add 2 at position 22,CC[NH+]1C[C@H](c2ccccc22)CC2(+CN(C(=O)c3ccon3))CC2)C1H
add # at position 50,CC[NH+]1C[C@H](c2ccccc22)CC2(+CN(C(=O)c3ccon3))CC2#)C1H
remove 3 from position 39,CC[NH+]1C[C@H](c2ccccc22)CC2(+CN(C(=O)cccon3))CC2#)C1H
remove + from position 29,CC[NH+]1C[C@H](c2ccccc22)CC2(CN(C(=O)cccon3))CC2#)C1H
add S at position 11,CC[NH+]1C[CS@H](c2ccccc22)CC2(CN(C(=O)cccon3))CC2#)C1H
add C at position 0,CCC[NH+]1C[CS@H](c2ccccc22)CC2(CN(C(=O)cccon3))CC2#)C1H
remove ] from position 15,CCC[NH+]1C[CS@H(c2ccccc22)CC2(CN(C(=O)cccon3))CC2#)C1H
add r at position 34,CCC[NH+]1C[CS@H(c2ccccc22)CC2(CN(Cr(=O)cccon3))CC2#)C1H
remove ) from position 38,CCC[NH+]1C[CS@H(c2ccccc22)CC2(CN(Cr(=Occcon3))CC2#)C1H
remove ( from position 32,CCC[NH+]1C[CS@H(c2ccccc22)CC2(CNCr(=Occcon3))CC2#)C1H
remove 2 from position 17,CCC[NH+]1C[CS@H(cccccc22)CC2(CNCr(=Occcon3))CC2#)C1H
replace N at position 4 with O,CCC[OH+]1C[CS@H(cccccc22)CC2(CNCr(=Occcon3))CC2#)C1H
remove 2 from position 22,CCC[OH+]1C[CS@H(cccccc2)CC2(CNCr(=Occcon3))CC2#)C1H
remove ) from position 41,CCC[OH+]1C[CS@H(cccccc2)CC2(CNCr(=Occcon3)CC2#)C1H
replace N at position 29 with r,CCC[OH+]1C[CS@H(cccccc2)CC2(CrCr(=Occcon3)CC2#)C1H
remove C from position 1,CC[OH+]1C[CS@H(cccccc2)CC2(CrCr(=Occcon3)CC2#)C1H
replace H at position 4 with #,CC[O#+]1C[CS@H(cccccc2)CC2(CrCr(=Occcon3)CC2#)C1H
remove ) from position 45,CC[O#+]1C[CS@H(cccccc2)CC2(CrCr(=Occcon3)CC2#C1H
remove C from position 24,CC[O#+]1C[CS@H(cccccc2)C2(CrCr(=Occcon3)CC2#C1H
add H at position 4,CC[OH#+]1C[CS@H(cccccc2)C2(CrCr(=Occcon3)CC2#C1H
remove H from position 4,CC[O#+]1C[CS@H(cccccc2)C2(CrCr(=Occcon3)CC2#C1H
remove [ from position 9,CC[O#+]1CCS@H(cccccc2)C2(CrCr(=Occcon3)CC2#C1H
add ] at position 22,CC[O#+]1CCS@H(cccccc2)]C2(CrCr(=Occcon3)CC2#C1H
add - at position 33,CC[O#+]1CCS@H(cccccc2)]C2(CrCr(=O-cccon3)CC2#C1H
remove r from position 29,CC[O#+]1CCS@H(cccccc2)]C2(CrC(=O-cccon3)CC2#C1H
add O at position 17,CC[O#+]1CCS@H(cccOccc2)]C2(CrC(=O-cccon3)CC2#C1H
replace @ at position 11 with 3,CC[O#+]1CCS3H(cccOccc2)]C2(CrC(=O-cccon3)CC2#C1H
add ] at position 9,CC[O#+]1C]CS3H(cccOccc2)]C2(CrC(=O-cccon3)CC2#C1H
replace [ at position 2 with H,CCHO#+]1C]CS3H(cccOccc2)]C2(CrC(=O-cccon3)CC2#C1H
remove 2 from position 22,CCHO#+]1C]CS3H(cccOccc)]C2(CrC(=O-cccon3)CC2#C1H
remove o from position 37,CCHO#+]1C]CS3H(cccOccc)]C2(CrC(=O-cccn3)CC2#C1H
replace ( at position 30 with ),CCHO#+]1C]CS3H(cccOccc)]C2(CrC)=O-cccn3)CC2#C1H
replace ) at position 30 with o,CCHO#+]1C]CS3H(cccOccc)]C2(CrCo=O-cccn3)CC2#C1H
replace C at position 10 with @,CCHO#+]1C]@S3H(cccOccc)]C2(CrCo=O-cccn3)CC2#C1H
remove - from position 33,CCHO#+]1C]@S3H(cccOccc)]C2(CrCo=Occcn3)CC2#C1H
replace O at position 18 with 3,CCHO#+]1C]@S3H(ccc3ccc)]C2(CrCo=Occcn3)CC2#C1H
add = at position 11,CCHO#+]1C]@=S3H(ccc3ccc)]C2(CrCo=Occcn3)CC2#C1H
remove 1 from position 7,CCHO#+]C]@=S3H(ccc3ccc)]C2(CrCo=Occcn3)CC2#C1H
replace ] at position 6 with n,CCHO#+nC]@=S3H(ccc3ccc)]C2(CrCo=Occcn3)CC2#C1H
replace C at position 24 with n,CCHO#+nC]@=S3H(ccc3ccc)]n2(CrCo=Occcn3)CC2#C1H
replace c at position 16 with N,CCHO#+nC]@=S3H(cNc3ccc)]n2(CrCo=Occcn3)CC2#C1H
add S at position 6,CCHO#+SnC]@=S3H(cNc3ccc)]n2(CrCo=Occcn3)CC2#C1H
remove @ from position 10,CCHO#+SnC]=S3H(cNc3ccc)]n2(CrCo=Occcn3)CC2#C1H
remove c from position 17,CCHO#+SnC]=S3H(cN3ccc)]n2(CrCo=Occcn3)CC2#C1H
replace c at position 20 with l,CCHO#+SnC]=S3H(cN3ccl)]n2(CrCo=Occcn3)CC2#C1H
replace # at position 4 with +,CCHO++SnC]=S3H(cN3ccl)]n2(CrCo=Occcn3)CC2#C1H
remove O from position 3,CCH++SnC]=S3H(cN3ccl)]n2(CrCo=Occcn3)CC2#C1H
replace H at position 2 with 2,CC2++SnC]=S3H(cN3ccl)]n2(CrCo=Occcn3)CC2#C1H
remove S from position 5,CC2++nC]=S3H(cN3ccl)]n2(CrCo=Occcn3)CC2#C1H
replace ] at position 20 with 4,CC2++nC]=S3H(cN3ccl)4n2(CrCo=Occcn3)CC2#C1H
replace + at position 4 with I,CC2+InC]=S3H(cN3ccl)4n2(CrCo=Occcn3)CC2#C1H
remove I from position 4,CC2+nC]=S3H(cN3ccl)4n2(CrCo=Occcn3)CC2#C1H
remove c from position 12,CC2+nC]=S3H(N3ccl)4n2(CrCo=Occcn3)CC2#C1H
add S at position 12,CC2+nC]=S3H(SN3ccl)4n2(CrCo=Occcn3)CC2#C1H
replace c at position 29 with C,CC2+nC]=S3H(SN3ccl)4n2(CrCo=OCccn3)CC2#C1H
add r at position 23,CC2+nC]=S3H(SN3ccl)4n2(rCrCo=OCccn3)CC2#C1H
replace c at position 32 with r,CC2+nC]=S3H(SN3ccl)4n2(rCrCo=OCcrn3)CC2#C1H
remove ( from position 11,CC2+nC]=S3HSN3ccl)4n2(rCrCo=OCcrn3)CC2#C1H
replace 3 at position 13 with 5,CC2+nC]=S3HSN5ccl)4n2(rCrCo=OCcrn3)CC2#C1H
add F at position 22,CC2+nC]=S3HSN5ccl)4n2(FrCrCo=OCcrn3)CC2#C1H
add B at position 35,CC2+nC]=S3HSN5ccl)4n2(FrCrCo=OCcrn3B)CC2#C1H
remove S from position 8,CC2+nC]=3HSN5ccl)4n2(FrCrCo=OCcrn3B)CC2#C1H
add r at position 10,CC2+nC]=3HrSN5ccl)4n2(FrCrCo=OCcrn3B)CC2#C1H
add ) at position 34,CC2+nC]=3HrSN5ccl)4n2(FrCrCo=OCcrn)3B)CC2#C1H
remove r from position 10,CC2+nC]=3HSN5ccl)4n2(FrCrCo=OCcrn)3B)CC2#C1H
add ( at position 43,CC2+nC]=3HSN5ccl)4n2(FrCrCo=OCcrn)3B)CC2#C1(H
remove H from position 9,CC2+nC]=3SN5ccl)4n2(FrCrCo=OCcrn)3B)CC2#C1(H
replace 2 at position 2 with @,CC@+nC]=3SN5ccl)4n2(FrCrCo=OCcrn)3B)CC2#C1(H
remove F from position 20,CC@+nC]=3SN5ccl)4n2(rCrCo=OCcrn)3B)CC2#C1(H
add 7 at position 43,CC@+nC]=3SN5ccl)4n2(rCrCo=OCcrn)3B)CC2#C1(H7
replace n at position 17 with F,CC@+nC]=3SN5ccl)4F2(rCrCo=OCcrn)3B)CC2#C1(H7
remove ( from position 19,CC@+nC]=3SN5ccl)4F2rCrCo=OCcrn)3B)CC2#C1(H7
remove 2 from position 36,CC@+nC]=3SN5ccl)4F2rCrCo=OCcrn)3B)CC#C1(H7
replace C at position 34 with ),CC@+nC]=3SN5ccl)4F2rCrCo=OCcrn)3B))C#C1(H7
replace F at position 17 with =,CC@+nC]=3SN5ccl)4=2rCrCo=OCcrn)3B))C#C1(H7
remove # from position 36,CC@+nC]=3SN5ccl)4=2rCrCo=OCcrn)3B))CC1(H7
remove = from position 7,CC@+nC]3SN5ccl)4=2rCrCo=OCcrn)3B))CC1(H7
remove 2 from position 17,CC@+nC]3SN5ccl)4=rCrCo=OCcrn)3B))CC1(H7
add 7 at position 37,CC@+nC]3SN5ccl)4=rCrCo=OCcrn)3B))CC1(7H7
add 2 at position 28,CC@+nC]3SN5ccl)4=rCrCo=OCcrn2)3B))CC1(7H7
replace 5 at position 10 with =,CC@+nC]3SN=ccl)4=rCrCo=OCcrn2)3B))CC1(7H7
remove n from position 4,CC@+C]3SN=ccl)4=rCrCo=OCcrn2)3B))CC1(7H7
remove C from position 19,CC@+C]3SN=ccl)4=rCro=OCcrn2)3B))CC1(7H7
remove ) from position 30,CC@+C]3SN=ccl)4=rCro=OCcrn2)3B)CC1(7H7
add / at position 30,CC@+C]3SN=ccl)4=rCro=OCcrn2)3B/)CC1(7H7
remove c from position 23,CC@+C]3SN=ccl)4=rCro=OCrn2)3B/)CC1(7H7
remove C from position 22,CC@+C]3SN=ccl)4=rCro=Orn2)3B/)CC1(7H7
add r at position 33,CC@+C]3SN=ccl)4=rCro=Orn2)3B/)CC1r(7H7
remove C from position 30,CC@+C]3SN=ccl)4=rCro=Orn2)3B/)C1r(7H7
remove ) from position 29,CC@+C]3SN=ccl)4=rCro=Orn2)3B/C1r(7H7
remove 3 from position 6,CC@+C]SN=ccl)4=rCro=Orn2)3B/C1r(7H7
add ) at position 30,CC@+C]SN=ccl)4=rCro=Orn2)3B/C1)r(7H7
replace r at position 21 with 6,CC@+C]SN=ccl)4=rCro=O6n2)3B/C1)r(7H7
replace o at position 18 with 4,CC@+C]SN=ccl)4=rCr4=O6n2)3B/C1)r(7H7
remove 6 from position 21,CC@+C]SN=ccl)4=rCr4=On2)3B/C1)r(7H7
remove 4 from position 18,CC@+C]SN=ccl)4=rCr=On2)3B/C1)r(7H7
remove H from position 32,CC@+C]SN=ccl)4=rCr=On2)3B/C1)r(77
replace r at position 17 with ),CC@+C]SN=ccl)4=rC)=On2)3B/C1)r(77
remove 4 from position 13,CC@+C]SN=ccl)=rC)=On2)3B/C1)r(77
remove ) from position 21,CC@+C]SN=ccl)=rC)=On23B/C1)r(77
replace r at position 14 with 3,CC@+C]SN=ccl)=3C)=On23B/C1)r(77
add l at position 7,CC@+C]SlN=ccl)=3C)=On23B/C1)r(77
add - at position 23,CC@+C]SlN=ccl)=3C)=On23-B/C1)r(77
replace / at position 25 with (,CC@+C]SlN=ccl)=3C)=On23-B(C1)r(77
remove @ from position 2,CC+C]SlN=ccl)=3C)=On23-B(C1)r(77
replace c at position 9 with 5,CC+C]SlN=5cl)=3C)=On23-B(C1)r(77
remove ) from position 27,CC+C]SlN=5cl)=3C)=On23-B(C1r(77
replace 7 at position 29 with ),CC+C]SlN=5cl)=3C)=On23-B(C1r()7
replace 7 at position 30 with 4,CC+C]SlN=5cl)=3C)=On23-B(C1r()4
remove C from position 15,CC+C]SlN=5cl)=3)=On23-B(C1r()4
replace N at position 7 with 6,CC+C]Sl6=5cl)=3)=On23-B(C1r()4
remove 3 from position 20,CC+C]Sl6=5cl)=3)=On2-B(C1r()4
replace C at position 0 with S,SC+C]Sl6=5cl)=3)=On2-B(C1r()4
remove ) from position 27,SC+C]Sl6=5cl)=3)=On2-B(C1r(4
remove S from position 5,SC+C]l6=5cl)=3)=On2-B(C1r(4
remove ( from position 21,SC+C]l6=5cl)=3)=On2-BC1r(4
replace ] at position 4 with r,SC+Crl6=5cl)=3)=On2-BC1r(4
remove - from position 19,SC+Crl6=5cl)=3)=On2BC1r(4
add s at position 17,SC+Crl6=5cl)=3)=Osn2BC1r(4
replace 1 at position 22 with /,SC+Crl6=5cl)=3)=Osn2BC/r(4
replace ( at position 24 with F,SC+Crl6=5cl)=3)=Osn2BC/rF4
remove = from position 12,SC+Crl6=5cl)3)=Osn2BC/rF4
replace l at position 10 with -,SC+Crl6=5c-)3)=Osn2BC/rF4
remove n from position 17,SC+Crl6=5c-)3)=Os2BC/rF4
remove C from position 19,SC+Crl6=5c-)3)=Os2B/rF4
remove = from position 14,SC+Crl6=5c-)3)Os2B/rF4
add r at position 2,SCr+Crl6=5c-)3)Os2B/rF4
remove F from position 21,SCr+Crl6=5c-)3)Os2B/r4
remove C from position 1,Sr+Crl6=5c-)3)Os2B/r4
remove + from position 2,SrCrl6=5c-)3)Os2B/r4
remove 4 from position 19,SrCrl6=5c-)3)Os2B/r
replace 3 at position 11 with F,SrCrl6=5c-)F)Os2B/r
remove ) from position 12,SrCrl6=5c-)FOs2B/r
remove r from position 1,SCrl6=5c-)FOs2B/r
add l at position 2,SClrl6=5c-)FOs2B/r
remove ) from position 10,SClrl6=5c-FOs2B/r
add S at position 7,SClrl6=S5c-FOs2B/r
replace S at position 0 with o,oClrl6=S5c-FOs2B/r
remove F from position 11,oClrl6=S5c-Os2B/r
remove l from position 2,oCrl6=S5c-Os2B/r
remove S from position 6,oCrl6=5c-Os2B/r
replace / at position 13 with N,oCrl6=5c-Os2BNr
remove 2 from position 11,oCrl6=5c-OsBNr
add O at position 2,oCOrl6=5c-OsBNr
replace O at position 10 with ],oCOrl6=5c-]sBNr
add 6 at position 14,oCOrl6=5c-]sBN6r
remove s from position 11,oCOrl6=5c-]BN6r
remove = from position 6,oCOrl65c-]BN6r
replace r at position 3 with 4,oCO4l65c-]BN6r
add H at position 5,oCO4lH65c-]BN6r
replace C at position 1 with H,oHO4lH65c-]BN6r
remove 6 from position 13,oHO4lH65c-]BNr
add H at position 9,oHO4lH65cH-]BNr
remove O from position 2,oH4lH65cH-]BNr
remove 6 from position 5,oH4lH5cH-]BNr
replace H at position 4 with [,oH4l[5cH-]BNr
remove 4 from position 2,oHl[5cH-]BNr
remove l from position 2,oH[5cH-]BNr
replace B at position 8 with [,oH[5cH-][Nr
remove 5 from position 3,oH[cH-][Nr
remove [ from position 2,oHcH-][Nr
remove [ from position 6,oHcH-]Nr
remove r from position 7,oHcH-]N
replace - at position 4 with B,oHcHB]N
remove N from position 6,oHcHB]
remove B from position 4,oHcH]
add C at position 5,oHcH]C
replace ] at position 4 with -,oHcH-C
add B at position 3,oHcBH-C
add c at position 2,oHccBH-C
replace c at position 3 with S,oHcSBH-C
add s at position 6,oHcSBHs-C
remove C from position 8,oHcSBHs-
remove S from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
