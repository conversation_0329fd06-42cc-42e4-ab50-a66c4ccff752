log,state
initialize: O=C(Cn1cccc1-c1nc(-c2ccc(OC(F)(F)F)cc2)no1)Nc1nccs1,O=C(Cn1cccc1-c1nc(-c2ccc(OC(F)(F)F)cc2)no1)Nc1nccs1
replace F at position 28 with +,O=C(Cn1cccc1-c1nc(-c2ccc(OC(+)(F)F)cc2)no1)Nc1nccs1
add H at position 51,O=C(Cn1cccc1-c1nc(-c2ccc(OC(+)(F)F)cc2)no1)Nc1nccs1H
add ) at position 44,O=C(Cn1cccc1-c1nc(-c2ccc(OC(+)(F)F)cc2)no1)N)c1nccs1H
add 2 at position 22,O=C(Cn1cccc1-c1nc(-c2c2cc(OC(+)(F)F)cc2)no1)N)c1nccs1H
add # at position 50,O=C(Cn1cccc1-c1nc(-c2c2cc(OC(+)(F)F)cc2)no1)N)c1nc#cs1H
remove ) from position 39,O=C(Cn1cccc1-c1nc(-c2c2cc(OC(+)(F)F)cc2no1)N)c1nc#cs1H
remove + from position 29,O=C(Cn1cccc1-c1nc(-c2c2cc(OC()(F)F)cc2no1)N)c1nc#cs1H
add S at position 11,O=C(Cn1ccccS1-c1nc(-c2c2cc(OC()(F)F)cc2no1)N)c1nc#cs1H
add C at position 0,CO=C(Cn1ccccS1-c1nc(-c2c2cc(OC()(F)F)cc2no1)N)c1nc#cs1H
remove c from position 15,CO=C(Cn1ccccS1-1nc(-c2c2cc(OC()(F)F)cc2no1)N)c1nc#cs1H
add r at position 34,CO=C(Cn1ccccS1-1nc(-c2c2cc(OC()(F)rF)cc2no1)N)c1nc#cs1H
remove c from position 38,CO=C(Cn1ccccS1-1nc(-c2c2cc(OC()(F)rF)c2no1)N)c1nc#cs1H
remove F from position 32,CO=C(Cn1ccccS1-1nc(-c2c2cc(OC()()rF)c2no1)N)c1nc#cs1H
remove c from position 17,CO=C(Cn1ccccS1-1n(-c2c2cc(OC()()rF)c2no1)N)c1nc#cs1H
replace ( at position 4 with O,CO=COCn1ccccS1-1n(-c2c2cc(OC()()rF)c2no1)N)c1nc#cs1H
remove 2 from position 22,CO=COCn1ccccS1-1n(-c2ccc(OC()()rF)c2no1)N)c1nc#cs1H
remove ) from position 41,CO=COCn1ccccS1-1n(-c2ccc(OC()()rF)c2no1)Nc1nc#cs1H
replace ( at position 29 with r,CO=COCn1ccccS1-1n(-c2ccc(OC()r)rF)c2no1)Nc1nc#cs1H
remove O from position 1,C=COCn1ccccS1-1n(-c2ccc(OC()r)rF)c2no1)Nc1nc#cs1H
replace C at position 4 with #,C=CO#n1ccccS1-1n(-c2ccc(OC()r)rF)c2no1)Nc1nc#cs1H
remove c from position 45,C=CO#n1ccccS1-1n(-c2ccc(OC()r)rF)c2no1)Nc1nc#s1H
remove O from position 24,C=CO#n1ccccS1-1n(-c2ccc(C()r)rF)c2no1)Nc1nc#s1H
add H at position 4,C=COH#n1ccccS1-1n(-c2ccc(C()r)rF)c2no1)Nc1nc#s1H
remove H from position 4,C=CO#n1ccccS1-1n(-c2ccc(C()r)rF)c2no1)Nc1nc#s1H
remove c from position 9,C=CO#n1cccS1-1n(-c2ccc(C()r)rF)c2no1)Nc1nc#s1H
add ] at position 22,C=CO#n1cccS1-1n(-c2ccc](C()r)rF)c2no1)Nc1nc#s1H
add - at position 33,C=CO#n1cccS1-1n(-c2ccc](C()r)rF)c-2no1)Nc1nc#s1H
remove r from position 29,C=CO#n1cccS1-1n(-c2ccc](C()r)F)c-2no1)Nc1nc#s1H
add O at position 17,C=CO#n1cccS1-1n(-Oc2ccc](C()r)F)c-2no1)Nc1nc#s1H
replace 1 at position 11 with 4,C=CO#n1cccS4-1n(-Oc2ccc](C()r)F)c-2no1)Nc1nc#s1H
add ] at position 9,C=CO#n1cc]cS4-1n(-Oc2ccc](C()r)F)c-2no1)Nc1nc#s1H
replace C at position 2 with I,C=IO#n1cc]cS4-1n(-Oc2ccc](C()r)F)c-2no1)Nc1nc#s1H
remove c from position 22,C=IO#n1cc]cS4-1n(-Oc2cc](C()r)F)c-2no1)Nc1nc#s1H
remove 1 from position 37,C=IO#n1cc]cS4-1n(-Oc2cc](C()r)F)c-2no)Nc1nc#s1H
replace F at position 30 with (,C=IO#n1cc]cS4-1n(-Oc2cc](C()r)()c-2no)Nc1nc#s1H
replace ( at position 30 with o,C=IO#n1cc]cS4-1n(-Oc2cc](C()r)o)c-2no)Nc1nc#s1H
replace c at position 10 with @,C=IO#n1cc]@S4-1n(-Oc2cc](C()r)o)c-2no)Nc1nc#s1H
remove - from position 33,C=IO#n1cc]@S4-1n(-Oc2cc](C()r)o)c2no)Nc1nc#s1H
replace O at position 18 with 3,C=IO#n1cc]@S4-1n(-3c2cc](C()r)o)c2no)Nc1nc#s1H
add = at position 11,C=IO#n1cc]@=S4-1n(-3c2cc](C()r)o)c2no)Nc1nc#s1H
remove c from position 7,C=IO#n1c]@=S4-1n(-3c2cc](C()r)o)c2no)Nc1nc#s1H
replace 1 at position 6 with n,C=IO#nnc]@=S4-1n(-3c2cc](C()r)o)c2no)Nc1nc#s1H
replace ( at position 24 with n,C=IO#nnc]@=S4-1n(-3c2cc]nC()r)o)c2no)Nc1nc#s1H
replace ( at position 16 with O,C=IO#nnc]@=S4-1nO-3c2cc]nC()r)o)c2no)Nc1nc#s1H
add S at position 6,C=IO#nSnc]@=S4-1nO-3c2cc]nC()r)o)c2no)Nc1nc#s1H
remove @ from position 10,C=IO#nSnc]=S4-1nO-3c2cc]nC()r)o)c2no)Nc1nc#s1H
remove - from position 17,C=IO#nSnc]=S4-1nO3c2cc]nC()r)o)c2no)Nc1nc#s1H
replace c at position 20 with l,C=IO#nSnc]=S4-1nO3c2lc]nC()r)o)c2no)Nc1nc#s1H
replace # at position 4 with +,C=IO+nSnc]=S4-1nO3c2lc]nC()r)o)c2no)Nc1nc#s1H
remove O from position 3,C=I+nSnc]=S4-1nO3c2lc]nC()r)o)c2no)Nc1nc#s1H
replace I at position 2 with 2,C=2+nSnc]=S4-1nO3c2lc]nC()r)o)c2no)Nc1nc#s1H
remove S from position 5,C=2+nnc]=S4-1nO3c2lc]nC()r)o)c2no)Nc1nc#s1H
replace ] at position 20 with 4,C=2+nnc]=S4-1nO3c2lc4nC()r)o)c2no)Nc1nc#s1H
replace n at position 4 with H,C=2+Hnc]=S4-1nO3c2lc4nC()r)o)c2no)Nc1nc#s1H
remove H from position 4,C=2+nc]=S4-1nO3c2lc4nC()r)o)c2no)Nc1nc#s1H
remove n from position 12,C=2+nc]=S4-1O3c2lc4nC()r)o)c2no)Nc1nc#s1H
add S at position 12,C=2+nc]=S4-1SO3c2lc4nC()r)o)c2no)Nc1nc#s1H
replace 2 at position 29 with F,C=2+nc]=S4-1SO3c2lc4nC()r)o)cFno)Nc1nc#s1H
add r at position 23,C=2+nc]=S4-1SO3c2lc4nC(r)r)o)cFno)Nc1nc#s1H
replace o at position 32 with r,C=2+nc]=S4-1SO3c2lc4nC(r)r)o)cFnr)Nc1nc#s1H
remove 1 from position 11,C=2+nc]=S4-SO3c2lc4nC(r)r)o)cFnr)Nc1nc#s1H
replace 3 at position 13 with 5,C=2+nc]=S4-SO5c2lc4nC(r)r)o)cFnr)Nc1nc#s1H
add F at position 22,C=2+nc]=S4-SO5c2lc4nC(Fr)r)o)cFnr)Nc1nc#s1H
add B at position 35,C=2+nc]=S4-SO5c2lc4nC(Fr)r)o)cFnr)NBc1nc#s1H
remove S from position 8,C=2+nc]=4-SO5c2lc4nC(Fr)r)o)cFnr)NBc1nc#s1H
add r at position 10,C=2+nc]=4-rSO5c2lc4nC(Fr)r)o)cFnr)NBc1nc#s1H
add ) at position 34,C=2+nc]=4-rSO5c2lc4nC(Fr)r)o)cFnr))NBc1nc#s1H
remove r from position 10,C=2+nc]=4-SO5c2lc4nC(Fr)r)o)cFnr))NBc1nc#s1H
add ( at position 43,C=2+nc]=4-SO5c2lc4nC(Fr)r)o)cFnr))NBc1nc#s1(H
remove - from position 9,C=2+nc]=4SO5c2lc4nC(Fr)r)o)cFnr))NBc1nc#s1(H
replace 2 at position 2 with @,C=@+nc]=4SO5c2lc4nC(Fr)r)o)cFnr))NBc1nc#s1(H
remove F from position 20,C=@+nc]=4SO5c2lc4nC(r)r)o)cFnr))NBc1nc#s1(H
add 7 at position 43,C=@+nc]=4SO5c2lc4nC(r)r)o)cFnr))NBc1nc#s1(H7
replace n at position 17 with F,C=@+nc]=4SO5c2lc4FC(r)r)o)cFnr))NBc1nc#s1(H7
remove ( from position 19,C=@+nc]=4SO5c2lc4FCr)r)o)cFnr))NBc1nc#s1(H7
remove c from position 36,C=@+nc]=4SO5c2lc4FCr)r)o)cFnr))NBc1n#s1(H7
replace 1 at position 34 with ),C=@+nc]=4SO5c2lc4FCr)r)o)cFnr))NBc)n#s1(H7
replace F at position 17 with =,C=@+nc]=4SO5c2lc4=Cr)r)o)cFnr))NBc)n#s1(H7
remove # from position 36,C=@+nc]=4SO5c2lc4=Cr)r)o)cFnr))NBc)ns1(H7
remove = from position 7,C=@+nc]4SO5c2lc4=Cr)r)o)cFnr))NBc)ns1(H7
remove C from position 17,C=@+nc]4SO5c2lc4=r)r)o)cFnr))NBc)ns1(H7
add 7 at position 37,C=@+nc]4SO5c2lc4=r)r)o)cFnr))NBc)ns1(7H7
add 2 at position 28,C=@+nc]4SO5c2lc4=r)r)o)cFnr)2)NBc)ns1(7H7
replace 5 at position 10 with =,C=@+nc]4SO=c2lc4=r)r)o)cFnr)2)NBc)ns1(7H7
remove n from position 4,C=@+c]4SO=c2lc4=r)r)o)cFnr)2)NBc)ns1(7H7
remove ) from position 19,C=@+c]4SO=c2lc4=r)ro)cFnr)2)NBc)ns1(7H7
remove c from position 30,C=@+c]4SO=c2lc4=r)ro)cFnr)2)NB)ns1(7H7
add / at position 30,C=@+c]4SO=c2lc4=r)ro)cFnr)2)NB/)ns1(7H7
remove n from position 23,C=@+c]4SO=c2lc4=r)ro)cFr)2)NB/)ns1(7H7
remove F from position 22,C=@+c]4SO=c2lc4=r)ro)cr)2)NB/)ns1(7H7
add r at position 33,C=@+c]4SO=c2lc4=r)ro)cr)2)NB/)ns1r(7H7
remove n from position 30,C=@+c]4SO=c2lc4=r)ro)cr)2)NB/)s1r(7H7
remove ) from position 29,C=@+c]4SO=c2lc4=r)ro)cr)2)NB/s1r(7H7
remove 4 from position 6,C=@+c]SO=c2lc4=r)ro)cr)2)NB/s1r(7H7
add ) at position 30,C=@+c]SO=c2lc4=r)ro)cr)2)NB/s1)r(7H7
replace r at position 21 with 6,C=@+c]SO=c2lc4=r)ro)c6)2)NB/s1)r(7H7
replace o at position 18 with 4,C=@+c]SO=c2lc4=r)r4)c6)2)NB/s1)r(7H7
remove 6 from position 21,C=@+c]SO=c2lc4=r)r4)c)2)NB/s1)r(7H7
remove 4 from position 18,C=@+c]SO=c2lc4=r)r)c)2)NB/s1)r(7H7
remove H from position 32,C=@+c]SO=c2lc4=r)r)c)2)NB/s1)r(77
replace r at position 17 with ),C=@+c]SO=c2lc4=r)))c)2)NB/s1)r(77
remove 4 from position 13,C=@+c]SO=c2lc=r)))c)2)NB/s1)r(77
remove ) from position 21,C=@+c]SO=c2lc=r)))c)2NB/s1)r(77
replace r at position 14 with 3,C=@+c]SO=c2lc=3)))c)2NB/s1)r(77
add l at position 7,C=@+c]SlO=c2lc=3)))c)2NB/s1)r(77
add - at position 23,C=@+c]SlO=c2lc=3)))c)2N-B/s1)r(77
replace / at position 25 with (,C=@+c]SlO=c2lc=3)))c)2N-B(s1)r(77
remove @ from position 2,C=+c]SlO=c2lc=3)))c)2N-B(s1)r(77
replace c at position 9 with 5,C=+c]SlO=52lc=3)))c)2N-B(s1)r(77
remove ) from position 27,C=+c]SlO=52lc=3)))c)2N-B(s1r(77
replace 7 at position 29 with ),C=+c]SlO=52lc=3)))c)2N-B(s1r()7
replace 7 at position 30 with 4,C=+c]SlO=52lc=3)))c)2N-B(s1r()4
remove ) from position 15,C=+c]SlO=52lc=3))c)2N-B(s1r()4
replace O at position 7 with 6,C=+c]Sl6=52lc=3))c)2N-B(s1r()4
remove N from position 20,C=+c]Sl6=52lc=3))c)2-B(s1r()4
replace C at position 0 with S,S=+c]Sl6=52lc=3))c)2-B(s1r()4
remove ) from position 27,S=+c]Sl6=52lc=3))c)2-B(s1r(4
remove S from position 5,S=+c]l6=52lc=3))c)2-B(s1r(4
remove ( from position 21,S=+c]l6=52lc=3))c)2-Bs1r(4
replace ] at position 4 with r,S=+crl6=52lc=3))c)2-Bs1r(4
remove - from position 19,S=+crl6=52lc=3))c)2Bs1r(4
add s at position 17,S=+crl6=52lc=3))cs)2Bs1r(4
replace 1 at position 22 with /,S=+crl6=52lc=3))cs)2Bs/r(4
replace ( at position 24 with F,S=+crl6=52lc=3))cs)2Bs/rF4
remove = from position 12,S=+crl6=52lc3))cs)2Bs/rF4
replace l at position 10 with -,S=+crl6=52-c3))cs)2Bs/rF4
remove ) from position 17,S=+crl6=52-c3))cs2Bs/rF4
remove s from position 19,S=+crl6=52-c3))cs2B/rF4
remove ) from position 14,S=+crl6=52-c3)cs2B/rF4
add r at position 2,S=r+crl6=52-c3)cs2B/rF4
remove F from position 21,S=r+crl6=52-c3)cs2B/r4
remove = from position 1,Sr+crl6=52-c3)cs2B/r4
remove + from position 2,Srcrl6=52-c3)cs2B/r4
remove 4 from position 19,Srcrl6=52-c3)cs2B/r
replace 3 at position 11 with F,Srcrl6=52-cF)cs2B/r
remove ) from position 12,Srcrl6=52-cFcs2B/r
remove r from position 1,Scrl6=52-cFcs2B/r
add l at position 2,Sclrl6=52-cFcs2B/r
remove c from position 10,Sclrl6=52-Fcs2B/r
add S at position 7,Sclrl6=S52-Fcs2B/r
replace S at position 0 with o,oclrl6=S52-Fcs2B/r
remove F from position 11,oclrl6=S52-cs2B/r
remove l from position 2,ocrl6=S52-cs2B/r
remove S from position 6,ocrl6=52-cs2B/r
replace / at position 13 with N,ocrl6=52-cs2BNr
remove 2 from position 11,ocrl6=52-csBNr
add O at position 2,ocOrl6=52-csBNr
replace c at position 10 with [,ocOrl6=52-[sBNr
add 6 at position 14,ocOrl6=52-[sBN6r
remove s from position 11,ocOrl6=52-[BN6r
remove = from position 6,ocOrl652-[BN6r
replace r at position 3 with 4,ocO4l652-[BN6r
add H at position 5,ocO4lH652-[BN6r
replace c at position 1 with F,oFO4lH652-[BN6r
remove 6 from position 13,oFO4lH652-[BNr
add H at position 9,oFO4lH652H-[BNr
remove O from position 2,oF4lH652H-[BNr
remove 6 from position 5,oF4lH52H-[BNr
replace H at position 4 with [,oF4l[52H-[BNr
remove 4 from position 2,oFl[52H-[BNr
remove l from position 2,oF[52H-[BNr
replace B at position 8 with [,oF[52H-[[Nr
remove 5 from position 3,oF[2H-[[Nr
remove [ from position 2,oF2H-[[Nr
remove [ from position 6,oF2H-[Nr
remove r from position 7,oF2H-[N
replace - at position 4 with B,oF2HB[N
remove N from position 6,oF2HB[
remove B from position 4,oF2H[
add C at position 5,oF2H[C
replace [ at position 4 with -,oF2H-C
add B at position 3,oF2BH-C
add c at position 2,oFc2BH-C
replace 2 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
