log,state
initialize: CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc1F,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc1F
replace C at position 28 with +,CC[C@@H](C)C(=O)NCC(=O)N(C)[+@@H](C)c1cc(F)ccc1F
add ( at position 37,CC[C@@H](C)C(=O)NCC(=O)N(C)[+@@H](C)c(1cc(F)ccc1F
remove @ from position 5,CC[C@H](C)C(=O)NCC(=O)N(C)[+@@H](C)c(1cc(F)ccc1F
add 2 at position 22,CC[C@H](C)C(=O)NCC(=O)2N(C)[+@@H](C)c(1cc(F)ccc1F
add 6 at position 1,C6C[C@H](C)C(=O)NCC(=O)2N(C)[+@@H](C)c(1cc(F)ccc1F
remove @ from position 30,C6C[C@H](C)C(=O)NCC(=O)2N(C)[+@H](C)c(1cc(F)ccc1F
remove C from position 9,C6C[C@H]()C(=O)NCC(=O)2N(C)[+@H](C)c(1cc(F)ccc1F
add + at position 45,C6C[C@H]()C(=O)NCC(=O)2N(C)[+@H](C)c(1cc(F)cc+c1F
replace ( at position 32 with C,C6C[C@H]()C(=O)NCC(=O)2N(C)[+@H]CC)c(1cc(F)cc+c1F
replace N at position 15 with ),C6C[C@H]()C(=O))CC(=O)2N(C)[+@H]CC)c(1cc(F)cc+c1F
remove @ from position 29,C6C[C@H]()C(=O))CC(=O)2N(C)[+H]CC)c(1cc(F)cc+c1F
add ) at position 43,C6C[C@H]()C(=O))CC(=O)2N(C)[+H]CC)c(1cc(F)c)c+c1F
remove C from position 2,C6[C@H]()C(=O))CC(=O)2N(C)[+H]CC)c(1cc(F)c)c+c1F
remove 1 from position 35,C6[C@H]()C(=O))CC(=O)2N(C)[+H]CC)c(cc(F)c)c+c1F
add B at position 19,C6[C@H]()C(=O))CC(=BO)2N(C)[+H]CC)c(cc(F)c)c+c1F
replace ( at position 17 with 3,C6[C@H]()C(=O))CC3=BO)2N(C)[+H]CC)c(cc(F)c)c+c1F
remove O from position 12,C6[C@H]()C(=))CC3=BO)2N(C)[+H]CC)c(cc(F)c)c+c1F
remove c from position 33,C6[C@H]()C(=))CC3=BO)2N(C)[+H]CC)(cc(F)c)c+c1F
remove ) from position 32,C6[C@H]()C(=))CC3=BO)2N(C)[+H]CC(cc(F)c)c+c1F
add - at position 21,C6[C@H]()C(=))CC3=BO)-2N(C)[+H]CC(cc(F)c)c+c1F
remove + from position 42,C6[C@H]()C(=))CC3=BO)-2N(C)[+H]CC(cc(F)c)cc1F
remove C from position 3,C6[@H]()C(=))CC3=BO)-2N(C)[+H]CC(cc(F)c)cc1F
add / at position 31,C6[@H]()C(=))CC3=BO)-2N(C)[+H]C/C(cc(F)c)cc1F
add - at position 13,C6[@H]()C(=))-CC3=BO)-2N(C)[+H]C/C(cc(F)c)cc1F
add ] at position 22,C6[@H]()C(=))-CC3=BO)-]2N(C)[+H]C/C(cc(F)c)cc1F
add - at position 33,C6[@H]()C(=))-CC3=BO)-]2N(C)[+H]C-/C(cc(F)c)cc1F
remove + from position 29,C6[@H]()C(=))-CC3=BO)-]2N(C)[H]C-/C(cc(F)c)cc1F
add O at position 17,C6[@H]()C(=))-CC3O=BO)-]2N(C)[H]C-/C(cc(F)c)cc1F
replace ) at position 11 with 4,C6[@H]()C(=4)-CC3O=BO)-]2N(C)[H]C-/C(cc(F)c)cc1F
add ] at position 9,C6[@H]()C](=4)-CC3O=BO)-]2N(C)[H]C-/C(cc(F)c)cc1F
replace [ at position 2 with H,C6H@H]()C](=4)-CC3O=BO)-]2N(C)[H]C-/C(cc(F)c)cc1F
remove ) from position 22,C6H@H]()C](=4)-CC3O=BO-]2N(C)[H]C-/C(cc(F)c)cc1F
remove c from position 37,C6H@H]()C](=4)-CC3O=BO-]2N(C)[H]C-/C(c(F)c)cc1F
replace H at position 30 with (,C6H@H]()C](=4)-CC3O=BO-]2N(C)[(]C-/C(c(F)c)cc1F
replace ( at position 30 with o,C6H@H]()C](=4)-CC3O=BO-]2N(C)[o]C-/C(c(F)c)cc1F
replace ( at position 10 with B,C6H@H]()C]B=4)-CC3O=BO-]2N(C)[o]C-/C(c(F)c)cc1F
remove - from position 33,C6H@H]()C]B=4)-CC3O=BO-]2N(C)[o]C/C(c(F)c)cc1F
replace O at position 18 with 3,C6H@H]()C]B=4)-CC33=BO-]2N(C)[o]C/C(c(F)c)cc1F
add = at position 11,C6H@H]()C]B==4)-CC33=BO-]2N(C)[o]C/C(c(F)c)cc1F
remove ) from position 7,C6H@H](C]B==4)-CC33=BO-]2N(C)[o]C/C(c(F)c)cc1F
replace ( at position 6 with n,C6H@H]nC]B==4)-CC33=BO-]2N(C)[o]C/C(c(F)c)cc1F
replace 2 at position 24 with n,C6H@H]nC]B==4)-CC33=BO-]nN(C)[o]C/C(c(F)c)cc1F
replace C at position 16 with O,C6H@H]nC]B==4)-CO33=BO-]nN(C)[o]C/C(c(F)c)cc1F
add S at position 6,C6H@H]SnC]B==4)-CO33=BO-]nN(C)[o]C/C(c(F)c)cc1F
remove B from position 10,C6H@H]SnC]==4)-CO33=BO-]nN(C)[o]C/C(c(F)c)cc1F
remove 3 from position 17,C6H@H]SnC]==4)-CO3=BO-]nN(C)[o]C/C(c(F)c)cc1F
replace O at position 20 with l,C6H@H]SnC]==4)-CO3=Bl-]nN(C)[o]C/C(c(F)c)cc1F
replace H at position 4 with ),C6H@)]SnC]==4)-CO3=Bl-]nN(C)[o]C/C(c(F)c)cc1F
remove @ from position 3,C6H)]SnC]==4)-CO3=Bl-]nN(C)[o]C/C(c(F)c)cc1F
replace H at position 2 with 2,C62)]SnC]==4)-CO3=Bl-]nN(C)[o]C/C(c(F)c)cc1F
remove S from position 5,C62)]nC]==4)-CO3=Bl-]nN(C)[o]C/C(c(F)c)cc1F
replace ] at position 20 with 4,C62)]nC]==4)-CO3=Bl-4nN(C)[o]C/C(c(F)c)cc1F
replace ] at position 4 with H,C62)HnC]==4)-CO3=Bl-4nN(C)[o]C/C(c(F)c)cc1F
remove H from position 4,C62)nC]==4)-CO3=Bl-4nN(C)[o]C/C(c(F)c)cc1F
remove C from position 12,C62)nC]==4)-O3=Bl-4nN(C)[o]C/C(c(F)c)cc1F
add S at position 12,C62)nC]==4)-SO3=Bl-4nN(C)[o]C/C(c(F)c)cc1F
replace / at position 29 with F,C62)nC]==4)-SO3=Bl-4nN(C)[o]CFC(c(F)c)cc1F
add r at position 23,C62)nC]==4)-SO3=Bl-4nN(rC)[o]CFC(c(F)c)cc1F
replace ( at position 32 with r,C62)nC]==4)-SO3=Bl-4nN(rC)[o]CFCrc(F)c)cc1F
remove - from position 11,C62)nC]==4)SO3=Bl-4nN(rC)[o]CFCrc(F)c)cc1F
replace 3 at position 13 with 5,C62)nC]==4)SO5=Bl-4nN(rC)[o]CFCrc(F)c)cc1F
add F at position 22,C62)nC]==4)SO5=Bl-4nN(FrC)[o]CFCrc(F)c)cc1F
add B at position 35,C62)nC]==4)SO5=Bl-4nN(FrC)[o]CFCrc(BF)c)cc1F
remove = from position 8,C62)nC]=4)SO5=Bl-4nN(FrC)[o]CFCrc(BF)c)cc1F
add r at position 10,C62)nC]=4)rSO5=Bl-4nN(FrC)[o]CFCrc(BF)c)cc1F
add ) at position 34,C62)nC]=4)rSO5=Bl-4nN(FrC)[o]CFCrc)(BF)c)cc1F
remove r from position 10,C62)nC]=4)SO5=Bl-4nN(FrC)[o]CFCrc)(BF)c)cc1F
add ( at position 43,C62)nC]=4)SO5=Bl-4nN(FrC)[o]CFCrc)(BF)c)cc1(F
remove ) from position 9,C62)nC]=4SO5=Bl-4nN(FrC)[o]CFCrc)(BF)c)cc1(F
replace 2 at position 2 with @,C6@)nC]=4SO5=Bl-4nN(FrC)[o]CFCrc)(BF)c)cc1(F
remove F from position 20,C6@)nC]=4SO5=Bl-4nN(rC)[o]CFCrc)(BF)c)cc1(F
add 7 at position 43,C6@)nC]=4SO5=Bl-4nN(rC)[o]CFCrc)(BF)c)cc1(F7
replace n at position 17 with F,C6@)nC]=4SO5=Bl-4FN(rC)[o]CFCrc)(BF)c)cc1(F7
remove ( from position 19,C6@)nC]=4SO5=Bl-4FNrC)[o]CFCrc)(BF)c)cc1(F7
remove ) from position 36,C6@)nC]=4SO5=Bl-4FNrC)[o]CFCrc)(BF)ccc1(F7
replace ) at position 34 with +,C6@)nC]=4SO5=Bl-4FNrC)[o]CFCrc)(BF+ccc1(F7
replace F at position 17 with =,C6@)nC]=4SO5=Bl-4=NrC)[o]CFCrc)(BF+ccc1(F7
remove c from position 36,C6@)nC]=4SO5=Bl-4=NrC)[o]CFCrc)(BF+cc1(F7
remove = from position 7,C6@)nC]4SO5=Bl-4=NrC)[o]CFCrc)(BF+cc1(F7
remove N from position 17,C6@)nC]4SO5=Bl-4=rC)[o]CFCrc)(BF+cc1(F7
add 7 at position 37,C6@)nC]4SO5=Bl-4=rC)[o]CFCrc)(BF+cc1(7F7
add 2 at position 28,C6@)nC]4SO5=Bl-4=rC)[o]CFCrc2)(BF+cc1(7F7
replace 5 at position 10 with =,C6@)nC]4SO==Bl-4=rC)[o]CFCrc2)(BF+cc1(7F7
remove n from position 4,C6@)C]4SO==Bl-4=rC)[o]CFCrc2)(BF+cc1(7F7
remove [ from position 19,C6@)C]4SO==Bl-4=rC)o]CFCrc2)(BF+cc1(7F7
remove F from position 30,C6@)C]4SO==Bl-4=rC)o]CFCrc2)(B+cc1(7F7
add / at position 30,C6@)C]4SO==Bl-4=rC)o]CFCrc2)(B/+cc1(7F7
remove C from position 23,C6@)C]4SO==Bl-4=rC)o]CFrc2)(B/+cc1(7F7
remove F from position 22,C6@)C]4SO==Bl-4=rC)o]Crc2)(B/+cc1(7F7
add r at position 33,C6@)C]4SO==Bl-4=rC)o]Crc2)(B/+cc1r(7F7
remove c from position 30,C6@)C]4SO==Bl-4=rC)o]Crc2)(B/+c1r(7F7
remove + from position 29,C6@)C]4SO==Bl-4=rC)o]Crc2)(B/c1r(7F7
remove 4 from position 6,C6@)C]SO==Bl-4=rC)o]Crc2)(B/c1r(7F7
add ) at position 30,C6@)C]SO==Bl-4=rC)o]Crc2)(B/c1)r(7F7
replace r at position 21 with 6,C6@)C]SO==Bl-4=rC)o]C6c2)(B/c1)r(7F7
replace o at position 18 with 4,C6@)C]SO==Bl-4=rC)4]C6c2)(B/c1)r(7F7
remove 6 from position 21,C6@)C]SO==Bl-4=rC)4]Cc2)(B/c1)r(7F7
remove 4 from position 18,C6@)C]SO==Bl-4=rC)]Cc2)(B/c1)r(7F7
remove F from position 32,C6@)C]SO==Bl-4=rC)]Cc2)(B/c1)r(77
replace ) at position 17 with +,C6@)C]SO==Bl-4=rC+]Cc2)(B/c1)r(77
remove 4 from position 13,C6@)C]SO==Bl-=rC+]Cc2)(B/c1)r(77
remove ) from position 21,C6@)C]SO==Bl-=rC+]Cc2(B/c1)r(77
replace r at position 14 with 3,C6@)C]SO==Bl-=3C+]Cc2(B/c1)r(77
add l at position 7,C6@)C]SlO==Bl-=3C+]Cc2(B/c1)r(77
add - at position 23,C6@)C]SlO==Bl-=3C+]Cc2(-B/c1)r(77
replace / at position 25 with (,C6@)C]SlO==Bl-=3C+]Cc2(-B(c1)r(77
remove @ from position 2,C6)C]SlO==Bl-=3C+]Cc2(-B(c1)r(77
replace = at position 9 with 5,C6)C]SlO=5Bl-=3C+]Cc2(-B(c1)r(77
remove ) from position 27,C6)C]SlO=5Bl-=3C+]Cc2(-B(c1r(77
replace 7 at position 29 with ),C6)C]SlO=5Bl-=3C+]Cc2(-B(c1r()7
replace 7 at position 30 with 4,C6)C]SlO=5Bl-=3C+]Cc2(-B(c1r()4
remove C from position 15,C6)C]SlO=5Bl-=3+]Cc2(-B(c1r()4
replace O at position 7 with 6,C6)C]Sl6=5Bl-=3+]Cc2(-B(c1r()4
remove ( from position 20,C6)C]Sl6=5Bl-=3+]Cc2-B(c1r()4
replace C at position 0 with S,S6)C]Sl6=5Bl-=3+]Cc2-B(c1r()4
remove ) from position 27,S6)C]Sl6=5Bl-=3+]Cc2-B(c1r(4
remove S from position 5,S6)C]l6=5Bl-=3+]Cc2-B(c1r(4
remove ( from position 21,S6)C]l6=5Bl-=3+]Cc2-Bc1r(4
replace ] at position 4 with r,S6)Crl6=5Bl-=3+]Cc2-Bc1r(4
remove - from position 19,S6)Crl6=5Bl-=3+]Cc2Bc1r(4
add s at position 17,S6)Crl6=5Bl-=3+]Csc2Bc1r(4
replace 1 at position 22 with /,S6)Crl6=5Bl-=3+]Csc2Bc/r(4
replace ( at position 24 with F,S6)Crl6=5Bl-=3+]Csc2Bc/rF4
remove = from position 12,S6)Crl6=5Bl-3+]Csc2Bc/rF4
replace l at position 10 with -,S6)Crl6=5B--3+]Csc2Bc/rF4
remove c from position 17,S6)Crl6=5B--3+]Cs2Bc/rF4
remove c from position 19,S6)Crl6=5B--3+]Cs2B/rF4
remove ] from position 14,S6)Crl6=5B--3+Cs2B/rF4
add r at position 2,S6r)Crl6=5B--3+Cs2B/rF4
remove F from position 21,S6r)Crl6=5B--3+Cs2B/r4
remove 6 from position 1,Sr)Crl6=5B--3+Cs2B/r4
remove ) from position 2,SrCrl6=5B--3+Cs2B/r4
remove 4 from position 19,SrCrl6=5B--3+Cs2B/r
replace 3 at position 11 with F,SrCrl6=5B--F+Cs2B/r
remove + from position 12,SrCrl6=5B--FCs2B/r
remove r from position 1,SCrl6=5B--FCs2B/r
add l at position 2,SClrl6=5B--FCs2B/r
remove - from position 10,SClrl6=5B-FCs2B/r
add S at position 7,SClrl6=S5B-FCs2B/r
replace S at position 0 with o,oClrl6=S5B-FCs2B/r
remove F from position 11,oClrl6=S5B-Cs2B/r
remove l from position 2,oCrl6=S5B-Cs2B/r
remove S from position 6,oCrl6=5B-Cs2B/r
replace / at position 13 with N,oCrl6=5B-Cs2BNr
remove 2 from position 11,oCrl6=5B-CsBNr
add O at position 2,oCOrl6=5B-CsBNr
replace C at position 10 with ],oCOrl6=5B-]sBNr
add 6 at position 14,oCOrl6=5B-]sBN6r
remove s from position 11,oCOrl6=5B-]BN6r
remove = from position 6,oCOrl65B-]BN6r
replace r at position 3 with 4,oCO4l65B-]BN6r
add H at position 5,oCO4lH65B-]BN6r
replace C at position 1 with H,oHO4lH65B-]BN6r
remove 6 from position 13,oHO4lH65B-]BNr
add H at position 9,oHO4lH65BH-]BNr
remove O from position 2,oH4lH65BH-]BNr
remove 6 from position 5,oH4lH5BH-]BNr
replace H at position 4 with [,oH4l[5BH-]BNr
remove 4 from position 2,oHl[5BH-]BNr
remove l from position 2,oH[5BH-]BNr
replace B at position 8 with [,oH[5BH-][Nr
remove 5 from position 3,oH[BH-][Nr
remove [ from position 2,oHBH-][Nr
remove [ from position 6,oHBH-]Nr
remove r from position 7,oHBH-]N
replace - at position 4 with B,oHBHB]N
remove N from position 6,oHBHB]
remove B from position 4,oHBH]
add C at position 5,oHBH]C
replace ] at position 4 with -,oHBH-C
add B at position 3,oHBBH-C
add c at position 2,oHcBBH-C
replace B at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
