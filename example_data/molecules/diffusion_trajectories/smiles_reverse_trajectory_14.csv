log,state
initialize: ,
add O at position 0,O
add r at position 0,r<PERSON>
add I at position 2,r<PERSON><PERSON>
add ( at position 2,rO(I
add 2 at position 1,r2O(I
replace O at position 2 with (,r2((I
add [ at position 0,[r2((I
add c at position 2,[rc2((I
add C at position 6,[rc2((CI
add 2 at position 2,[r2c2((CI
remove 2 from position 4,[r2c((CI
replace I at position 7 with C,[r2c((CC
replace 2 at position 2 with c,[rcc((CC
replace [ at position 0 with =,=rcc((CC
remove = from position 0,rcc((CC
replace C at position 5 with ],rcc((]C
remove ( from position 4,rcc(]C
remove C from position 5,rcc(]
remove ( from position 3,rcc]
remove c from position 1,rc]
replace c at position 1 with o,ro]
remove r from position 0,o]
replace o at position 0 with -,-]
add 7 at position 0,7-]
add r at position 0,r7-]
replace 7 at position 1 with O,rO-]
remove r from position 0,O-]
add 3 at position 0,3O-]
add = at position 2,3O=-]
add F at position 5,3O=-]F
remove ] from position 4,3O=-F
replace F at position 4 with 3,3O=-3
add l at position 2,3Ol=-3
replace = at position 3 with ],3Ol]-3
add # at position 5,3Ol]-#3
remove l from position 2,3O]-#3
remove # from position 4,3O]-3
replace O at position 1 with C,3C]-3
add o at position 4,3C]-o3
add 1 at position 3,3C]1-o3
replace ] at position 2 with B,3CB1-o3
remove - from position 4,3CB1o3
remove 1 from position 3,3CBo3
replace 3 at position 4 with C,3CBoC
remove 3 from position 0,CBoC
replace B at position 1 with ),C)oC
replace ) at position 1 with 5,C5oC
add [ at position 2,C5[oC
replace o at position 3 with +,C5[+C
add = at position 2,C5=[+C
add N at position 2,C5N=[+C
replace C at position 0 with /,/5N=[+C
add 7 at position 2,/57N=[+C
remove 7 from position 2,/5N=[+C
remove C from position 6,/5N=[+
add r at position 6,/5N=[+r
replace + at position 5 with #,/5N=[#r
add r at position 7,/5N=[#rr
add H at position 8,/5N=[#rrH
add l at position 3,/5Nl=[#rrH
add 4 at position 7,/5Nl=[#4rrH
remove l from position 3,/5N=[#4rrH
add ( at position 1,/(5N=[#4rrH
add c at position 1,/c(5N=[#4rrH
remove # from position 7,/c(5N=[4rrH
replace H at position 10 with 1,/c(5N=[4rr1
remove r from position 8,/c(5N=[4r1
replace ( at position 2 with /,/c/5N=[4r1
replace / at position 2 with O,/cO5N=[4r1
add F at position 3,/cOF5N=[4r1
add 5 at position 9,/cOF5N=[45r1
replace = at position 6 with #,/cOF5N#[45r1
replace 4 at position 8 with +,/cOF5N#[+5r1
add H at position 8,/cOF5N#[H+5r1
add 3 at position 9,/cOF5N#[H3+5r1
remove 1 from position 13,/cOF5N#[H3+5r
replace 3 at position 9 with ),/cOF5N#[H)+5r
replace 5 at position 11 with r,/cOF5N#[H)+rr
add 6 at position 9,/cOF5N#[H6)+rr
remove # from position 6,/cOF5N[H6)+rr
add 4 at position 10,/cOF5N[H6)4+rr
replace [ at position 6 with 3,/cOF5N3H6)4+rr
add 7 at position 4,/cOF75N3H6)4+rr
add + at position 13,/cOF75N3H6)4++rr
remove c from position 1,/OF75N3H6)4++rr
replace N at position 5 with F,/OF75F3H6)4++rr
remove / from position 0,OF75F3H6)4++rr
add H at position 14,OF75F3H6)4++rrH
replace 7 at position 2 with I,OFI5F3H6)4++rrH
remove I from position 2,OF5F3H6)4++rrH
replace ) at position 7 with 7,OF5F3H674++rrH
replace F at position 1 with o,Oo5F3H674++rrH
add 2 at position 7,Oo5F3H6274++rrH
add ( at position 14,Oo5F3H6274++rr(H
add n at position 6,Oo5F3Hn6274++rr(H
add [ at position 11,Oo5F3Hn6274[++rr(H
add / at position 9,Oo5F3Hn62/74[++rr(H
replace 5 at position 2 with 6,Oo6F3Hn62/74[++rr(H
replace 2 at position 8 with C,Oo6F3Hn6C/74[++rr(H
add + at position 4,Oo6F+3Hn6C/74[++rr(H
replace + at position 15 with 3,Oo6F+3Hn6C/74[+3rr(H
replace + at position 4 with l,Oo6Fl3Hn6C/74[+3rr(H
add C at position 12,Oo6Fl3Hn6C/7C4[+3rr(H
add [ at position 21,Oo6Fl3Hn6C/7C4[+3rr(H[
add N at position 11,Oo6Fl3Hn6C/N7C4[+3rr(H[
add B at position 23,Oo6Fl3Hn6C/N7C4[+3rr(H[B
add r at position 15,Oo6Fl3Hn6C/N7C4r[+3rr(H[B
add o at position 19,Oo6Fl3Hn6C/N7C4r[+3orr(H[B
replace C at position 13 with H,Oo6Fl3Hn6C/N7H4r[+3orr(H[B
remove H from position 23,Oo6Fl3Hn6C/N7H4r[+3orr([B
add c at position 23,Oo6Fl3Hn6C/N7H4r[+3orr(c[B
add [ at position 6,Oo6Fl3[Hn6C/N7H4r[+3orr(c[B
replace [ at position 17 with 2,Oo6Fl3[Hn6C/N7H4r2+3orr(c[B
add 4 at position 5,Oo6Fl43[Hn6C/N7H4r2+3orr(c[B
add c at position 5,Oo6Flc43[Hn6C/N7H4r2+3orr(c[B
replace [ at position 8 with C,Oo6Flc43CHn6C/N7H4r2+3orr(c[B
add O at position 11,Oo6Flc43CHnO6C/N7H4r2+3orr(c[B
add - at position 5,Oo6Fl-c43CHnO6C/N7H4r2+3orr(c[B
remove H from position 18,Oo6Fl-c43CHnO6C/N74r2+3orr(c[B
add H at position 27,Oo6Fl-c43CHnO6C/N74r2+3orr(Hc[B
replace F at position 3 with o,Oo6ol-c43CHnO6C/N74r2+3orr(Hc[B
remove H from position 10,Oo6ol-c43CnO6C/N74r2+3orr(Hc[B
replace 4 at position 7 with c,Oo6ol-cc3CnO6C/N74r2+3orr(Hc[B
add S at position 13,Oo6ol-cc3CnO6SC/N74r2+3orr(Hc[B
add 4 at position 23,Oo6ol-cc3CnO6SC/N74r2+34orr(Hc[B
add ( at position 29,Oo6ol-cc3CnO6SC/N74r2+34orr(H(c[B
replace N at position 16 with s,Oo6ol-cc3CnO6SC/s74r2+34orr(H(c[B
replace C at position 9 with [,Oo6ol-cc3[nO6SC/s74r2+34orr(H(c[B
add S at position 13,Oo6ol-cc3[nO6SSC/s74r2+34orr(H(c[B
add 5 at position 4,Oo6o5l-cc3[nO6SSC/s74r2+34orr(H(c[B
add @ at position 23,Oo6o5l-cc3[nO6SSC/s74r2@+34orr(H(c[B
replace o at position 1 with 1,O16o5l-cc3[nO6SSC/s74r2@+34orr(H(c[B
remove S from position 15,O16o5l-cc3[nO6SC/s74r2@+34orr(H(c[B
add l at position 21,O16o5l-cc3[nO6SC/s74rl2@+34orr(H(c[B
remove l from position 5,O16o5-cc3[nO6SC/s74rl2@+34orr(H(c[B
replace ( at position 31 with 7,O16o5-cc3[nO6SC/s74rl2@+34orr(H7c[B
replace [ at position 33 with 5,O16o5-cc3[nO6SC/s74rl2@+34orr(H7c5B
remove 6 from position 12,O16o5-cc3[nOSC/s74rl2@+34orr(H7c5B
remove 5 from position 4,O16o-cc3[nOSC/s74rl2@+34orr(H7c5B
replace r at position 26 with 5,O16o-cc3[nOSC/s74rl2@+34or5(H7c5B
add S at position 23,O16o-cc3[nOSC/s74rl2@+3S4or5(H7c5B
replace ( at position 28 with ),O16o-cc3[nOSC/s74rl2@+3S4or5)H7c5B
replace [ at position 8 with (,O16o-cc3(nOSC/s74rl2@+3S4or5)H7c5B
replace o at position 25 with 1,O16o-cc3(nOSC/s74rl2@+3S41r5)H7c5B
add 6 at position 32,O16o-cc3(nOSC/s74rl2@+3S41r5)H7c65B
add 1 at position 15,O16o-cc3(nOSC/s174rl2@+3S41r5)H7c65B
replace - at position 4 with ],O16o]cc3(nOSC/s174rl2@+3S41r5)H7c65B
add 7 at position 8,O16o]cc37(nOSC/s174rl2@+3S41r5)H7c65B
replace / at position 14 with 2,O16o]cc37(nOSC2s174rl2@+3S41r5)H7c65B
add 7 at position 21,O16o]cc37(nOSC2s174rl72@+3S41r5)H7c65B
add 7 at position 1,O716o]cc37(nOSC2s174rl72@+3S41r5)H7c65B
remove ) from position 32,O716o]cc37(nOSC2s174rl72@+3S41r5H7c65B
add ) at position 37,O716o]cc37(nOSC2s174rl72@+3S41r5H7c65)B
replace 4 at position 19 with 1,O716o]cc37(nOSC2s171rl72@+3S41r5H7c65)B
replace o at position 4 with c,O716c]cc37(nOSC2s171rl72@+3S41r5H7c65)B
add r at position 27,O716c]cc37(nOSC2s171rl72@+3rS41r5H7c65)B
add = at position 20,O716c]cc37(nOSC2s171=rl72@+3rS41r5H7c65)B
remove 1 from position 2,O76c]cc37(nOSC2s171=rl72@+3rS41r5H7c65)B
remove 7 from position 1,O6c]cc37(nOSC2s171=rl72@+3rS41r5H7c65)B
replace 7 at position 16 with ),O6c]cc37(nOSC2s1)1=rl72@+3rS41r5H7c65)B
add - at position 7,O6c]cc3-7(nOSC2s1)1=rl72@+3rS41r5H7c65)B
remove H from position 33,O6c]cc3-7(nOSC2s1)1=rl72@+3rS41r57c65)B
replace 4 at position 29 with ),O6c]cc3-7(nOSC2s1)1=rl72@+3rS)1r57c65)B
remove 5 from position 32,O6c]cc3-7(nOSC2s1)1=rl72@+3rS)1r7c65)B
add - at position 38,O6c]cc3-7(nOSC2s1)1=rl72@+3rS)1r7c65)B-
replace ) at position 17 with C,O6c]cc3-7(nOSC2s1C1=rl72@+3rS)1r7c65)B-
add 4 at position 32,O6c]cc3-7(nOSC2s1C1=rl72@+3rS)1r47c65)B-
add 4 at position 18,O6c]cc3-7(nOSC2s1C41=rl72@+3rS)1r47c65)B-
add 5 at position 37,O6c]cc3-7(nOSC2s1C41=rl72@+3rS)1r47c655)B-
replace 4 at position 18 with s,O6c]cc3-7(nOSC2s1Cs1=rl72@+3rS)1r47c655)B-
replace 5 at position 37 with C,O6c]cc3-7(nOSC2s1Cs1=rl72@+3rS)1r47c6C5)B-
replace ) at position 30 with c,O6c]cc3-7(nOSC2s1Cs1=rl72@+3rSc1r47c6C5)B-
replace 6 at position 1 with I,OIc]cc3-7(nOSC2s1Cs1=rl72@+3rSc1r47c6C5)B-
replace S at position 29 with N,OIc]cc3-7(nOSC2s1Cs1=rl72@+3rNc1r47c6C5)B-
add = at position 1,O=Ic]cc3-7(nOSC2s1Cs1=rl72@+3rNc1r47c6C5)B-
add c at position 42,O=Ic]cc3-7(nOSC2s1Cs1=rl72@+3rNc1r47c6C5)Bc-
remove r from position 33,O=Ic]cc3-7(nOSC2s1Cs1=rl72@+3rNc147c6C5)Bc-
add ] at position 22,O=Ic]cc3-7(nOSC2s1Cs1=]rl72@+3rNc147c6C5)Bc-
remove 6 from position 37,O=Ic]cc3-7(nOSC2s1Cs1=]rl72@+3rNc147cC5)Bc-
remove B from position 40,O=Ic]cc3-7(nOSC2s1Cs1=]rl72@+3rNc147cC5)c-
add 2 at position 30,O=Ic]cc3-7(nOSC2s1Cs1=]rl72@+32rNc147cC5)c-
add = at position 19,O=Ic]cc3-7(nOSC2s1C=s1=]rl72@+32rNc147cC5)c-
add c at position 4,O=Icc]cc3-7(nOSC2s1C=s1=]rl72@+32rNc147cC5)c-
replace 7 at position 10 with H,O=Icc]cc3-H(nOSC2s1C=s1=]rl72@+32rNc147cC5)c-
remove 2 from position 28,O=Icc]cc3-H(nOSC2s1C=s1=]rl7@+32rNc147cC5)c-
remove 7 from position 37,O=Icc]cc3-H(nOSC2s1C=s1=]rl7@+32rNc14cC5)c-
add F at position 17,O=Icc]cc3-H(nOSC2Fs1C=s1=]rl7@+32rNc14cC5)c-
add l at position 7,O=Icc]clc3-H(nOSC2Fs1C=s1=]rl7@+32rNc14cC5)c-
add s at position 36,O=Icc]clc3-H(nOSC2Fs1C=s1=]rl7@+32rNsc14cC5)c-
add = at position 26,O=Icc]clc3-H(nOSC2Fs1C=s1==]rl7@+32rNsc14cC5)c-
replace ] at position 5 with c,O=Icccclc3-H(nOSC2Fs1C=s1==]rl7@+32rNsc14cC5)c-
add o at position 9,O=Icccclco3-H(nOSC2Fs1C=s1==]rl7@+32rNsc14cC5)c-
add ] at position 43,O=Icccclco3-H(nOSC2Fs1C=s1==]rl7@+32rNsc14c]C5)c-
add - at position 19,O=Icccclco3-H(nOSC2-Fs1C=s1==]rl7@+32rNsc14c]C5)c-
add c at position 17,O=Icccclco3-H(nOScC2-Fs1C=s1==]rl7@+32rNsc14c]C5)c-
add 5 at position 43,O=Icccclco3-H(nOScC2-Fs1C=s1==]rl7@+32rNsc154c]C5)c-
add s at position 47,O=Icccclco3-H(nOScC2-Fs1C=s1==]rl7@+32rNsc154c]sC5)c-
replace ( at position 13 with 5,O=Icccclco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc154c]sC5)c-
add c at position 48,O=Icccclco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc154c]scC5)c-
add O at position 3,O=IOcccclco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc154c]scC5)c-
add C at position 0,CO=IOcccclco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc154c]scC5)c-
add c at position 45,CO=IOcccclco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc1c54c]scC5)c-
remove l from position 9,CO=IOccccco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc1c54c]scC5)c-
remove I from position 3,CO=Occccco3-H5nOScC2-Fs1C=s1==]rl7@+32rNsc1c54c]scC5)c-
add ] at position 21,CO=Occccco3-H5nOScC2-]Fs1C=s1==]rl7@+32rNsc1c54c]scC5)c-
remove s from position 49,CO=Occccco3-H5nOScC2-]Fs1C=s1==]rl7@+32rNsc1c54c]cC5)c-
remove F from position 22,CO=Occccco3-H5nOScC2-]s1C=s1==]rl7@+32rNsc1c54c]cC5)c-
replace 5 at position 13 with (,CO=Occccco3-H(nOScC2-]s1C=s1==]rl7@+32rNsc1c54c]cC5)c-
add [ at position 11,CO=Occccco3[-H(nOScC2-]s1C=s1==]rl7@+32rNsc1c54c]cC5)c-
replace r at position 32 with /,CO=Occccco3[-H(nOScC2-]s1C=s1==]/l7@+32rNsc1c54c]cC5)c-
replace s at position 23 with N,CO=Occccco3[-H(nOScC2-]N1C=s1==]/l7@+32rNsc1c54c]cC5)c-
add C at position 15,CO=Occccco3[-H(CnOScC2-]N1C=s1==]/l7@+32rNsc1c54c]cC5)c-
add s at position 56,CO=Occccco3[-H(CnOScC2-]N1C=s1==]/l7@+32rNsc1c54c]cC5)c-s
remove 1 from position 25,CO=Occccco3[-H(CnOScC2-]NC=s1==]/l7@+32rNsc1c54c]cC5)c-s
remove s from position 27,CO=Occccco3[-H(CnOScC2-]NC=1==]/l7@+32rNsc1c54c]cC5)c-s
remove n from position 16,CO=Occccco3[-H(COScC2-]NC=1==]/l7@+32rNsc1c54c]cC5)c-s
add r at position 29,CO=Occccco3[-H(COScC2-]NC=1==r]/l7@+32rNsc1c54c]cC5)c-s
remove 5 from position 44,CO=Occccco3[-H(COScC2-]NC=1==r]/l7@+32rNsc1c4c]cC5)c-s
remove l from position 32,CO=Occccco3[-H(COScC2-]NC=1==r]/7@+32rNsc1c4c]cC5)c-s
remove c from position 18,CO=Occccco3[-H(COSC2-]NC=1==r]/7@+32rNsc1c4c]cC5)c-s
remove 1 from position 25,CO=Occccco3[-H(COSC2-]NC===r]/7@+32rNsc1c4c]cC5)c-s
replace ] at position 28 with -,CO=Occccco3[-H(COSC2-]NC===r-/7@+32rNsc1c4c]cC5)c-s
replace 5 at position 46 with l,CO=Occccco3[-H(COSC2-]NC===r-/7@+32rNsc1c4c]cCl)c-s
replace o at position 9 with 2,CO=Occccc23[-H(COSC2-]NC===r-/7@+32rNsc1c4c]cCl)c-s
replace s at position 37 with 1,CO=Occccc23[-H(COSC2-]NC===r-/7@+32rN1c1c4c]cCl)c-s
remove c from position 44,CO=Occccc23[-H(COSC2-]NC===r-/7@+32rN1c1c4c]Cl)c-s
add = at position 22,CO=Occccc23[-H(COSC2-]=NC===r-/7@+32rN1c1c4c]Cl)c-s
replace 4 at position 42 with #,CO=Occccc23[-H(COSC2-]=NC===r-/7@+32rN1c1c#c]Cl)c-s
replace 7 at position 31 with N,CO=Occccc23[-H(COSC2-]=NC===r-/N@+32rN1c1c#c]Cl)c-s
replace + at position 33 with ],CO=Occccc23[-H(COSC2-]=NC===r-/N@]32rN1c1c#c]Cl)c-s
replace = at position 26 with N,CO=Occccc23[-H(COSC2-]=NC=N=r-/N@]32rN1c1c#c]Cl)c-s
add c at position 7,CO=Occcccc23[-H(COSC2-]=NC=N=r-/N@]32rN1c1c#c]Cl)c-s
add ( at position 28,CO=Occcccc23[-H(COSC2-]=NC=N(=r-/N@]32rN1c1c#c]Cl)c-s
remove ] from position 46,CO=Occcccc23[-H(COSC2-]=NC=N(=r-/N@]32rN1c1c#cCl)c-s
replace r at position 38 with ),CO=Occcccc23[-H(COSC2-]=NC=N(=r-/N@]32)N1c1c#cCl)c-s
add 1 at position 50,CO=Occcccc23[-H(COSC2-]=NC=N(=r-/N@]32)N1c1c#cCl)c1-s
add N at position 29,CO=Occcccc23[-H(COSC2-]=NC=N(N=r-/N@]32)N1c1c#cCl)c1-s
add 2 at position 21,CO=Occcccc23[-H(COSC22-]=NC=N(N=r-/N@]32)N1c1c#cCl)c1-s
add ( at position 48,CO=Occcccc23[-H(COSC22-]=NC=N(N=r-/N@]32)N1c1c#c(Cl)c1-s
replace ( at position 29 with +,CO=Occcccc23[-H(COSC22-]=NC=N+N=r-/N@]32)N1c1c#c(Cl)c1-s
remove - from position 54,CO=Occcccc23[-H(COSC22-]=NC=N+N=r-/N@]32)N1c1c#c(Cl)c1s
replace 3 at position 11 with N,CO=Occcccc2N[-H(COSC22-]=NC=N+N=r-/N@]32)N1c1c#c(Cl)c1s
remove O from position 17,CO=Occcccc2N[-H(CSC22-]=NC=N+N=r-/N@]32)N1c1c#c(Cl)c1s
add = at position 29,CO=Occcccc2N[-H(CSC22-]=NC=N+=N=r-/N@]32)N1c1c#c(Cl)c1s
remove - from position 33,CO=Occcccc2N[-H(CSC22-]=NC=N+=N=r/N@]32)N1c1c#c(Cl)c1s
remove ] from position 22,CO=Occcccc2N[-H(CSC22-=NC=N+=N=r/N@]32)N1c1c#c(Cl)c1s
remove - from position 13,CO=Occcccc2N[H(CSC22-=NC=N+=N=r/N@]32)N1c1c#c(Cl)c1s
remove / from position 31,CO=Occcccc2N[H(CSC22-=NC=N+=N=rN@]32)N1c1c#c(Cl)c1s
add C at position 3,CO=COcccccc2N[H(CSC22-=NC=N+=N=rN@]32)N1c1c#c(Cl)c1s
add c at position 42,CO=COcccccc2N[H(CSC22-=NC=N+=N=rN@]32)N1c1cc#c(Cl)c1s
remove - from position 21,CO=COcccccc2N[H(CSC22=NC=N+=N=rN@]32)N1c1cc#c(Cl)c1s
add [ at position 32,CO=COcccccc2N[H(CSC22=NC=N+=N=rN[@]32)N1c1cc#c(Cl)c1s
add C at position 33,CO=COcccccc2N[H(CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)c1s
add S at position 12,CO=COcccccc2SN[H(CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)c1s
add @ at position 15,CO=COcccccc2SN[@H(CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)c1s
add c at position 54,CO=COcccccc2SN[@H(CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)cc1s
replace O at position 4 with 1,CO=C1cccccc2SN[@H(CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)cc1s
add ] at position 17,CO=C1cccccc2SN[@H](CSC22=NC=N+=N=rN[C@]32)N1c1cc#c(Cl)cc1s
add C at position 32,CO=C1cccccc2SN[@H](CSC22=NC=N+=NC=rN[C@]32)N1c1cc#c(Cl)cc1s
add ( at position 38,CO=C1cccccc2SN[@H](CSC22=NC=N+=NC=rN[C(@]32)N1c1cc#c(Cl)cc1s
remove r from position 34,CO=C1cccccc2SN[@H](CSC22=NC=N+=NC=N[C(@]32)N1c1cc#c(Cl)cc1s
add C at position 15,CO=C1cccccc2SN[C@H](CSC22=NC=N+=NC=N[C(@]32)N1c1cc#c(Cl)cc1s
remove C from position 0,O=C1cccccc2SN[C@H](CSC22=NC=N+=NC=N[C(@]32)N1c1cc#c(Cl)cc1s
remove S from position 11,O=C1cccccc2N[C@H](CSC22=NC=N+=NC=N[C(@]32)N1c1cc#c(Cl)cc1s
add 3 at position 29,O=C1cccccc2N[C@H](CSC22=NC=N+3=NC=N[C(@]32)N1c1cc#c(Cl)cc1s
add H at position 39,O=C1cccccc2N[C@H](CSC22=NC=N+3=NC=N[C(@H]32)N1c1cc#c(Cl)cc1s
remove # from position 50,O=C1cccccc2N[C@H](CSC22=NC=N+3=NC=N[C(@H]32)N1c1ccc(Cl)cc1s
remove 2 from position 22,O=C1cccccc2N[C@H](CSC2=NC=N+3=NC=N[C(@H]32)N1c1ccc(Cl)cc1s
add 2 at position 5,O=C1c2ccccc2N[C@H](CSC2=NC=N+3=NC=N[C(@H]32)N1c1ccc(Cl)cc1s
replace ( at position 37 with @,O=C1c2ccccc2N[C@H](CSC2=NC=N+3=NC=N[C@@H]32)N1c1ccc(Cl)cc1s
remove s from position 58,O=C1c2ccccc2N[C@H](CSC2=NC=N+3=NC=N[C@@H]32)N1c1ccc(Cl)cc1
replace + at position 28 with C,O=C1c2ccccc2N[C@H](CSC2=NC=NC3=NC=N[C@@H]32)N1c1ccc(Cl)cc1
final: O=C1c2ccccc2N[C@H](CSC2=NC=NC3=NC=N[C@@H]32)N1c1ccc(Cl)cc1,O=C1c2ccccc2N[C@H](CSC2=NC=NC3=NC=N[C@@H]32)N1c1ccc(Cl)cc1
