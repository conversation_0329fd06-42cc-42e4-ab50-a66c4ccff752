log,state
initialize: C#CC(C)(C)NC(=O)c1ccc(OC)c(O)c1,C#CC(C)(C)NC(=O)c1ccc(OC)c(O)c1
replace O at position 14 with +,C#CC(C)(C)NC(=+)c1ccc(OC)c(O)c1
add O at position 2,C#OCC(C)(C)NC(=+)c1ccc(OC)c(O)c1
remove C from position 6,C#OCC()(C)NC(=+)c1ccc(OC)c(O)c1
replace ( at position 7 with #,C#OCC()#C)NC(=+)c1ccc(OC)c(O)c1
add 6 at position 1,C6#OCC()#C)NC(=+)c1ccc(OC)c(O)c1
remove c from position 30,C6#OCC()#C)NC(=+)c1ccc(OC)c(O)1
remove C from position 4,C6#OC()#C)NC(=+)c1ccc(OC)c(O)1
add + at position 22,C6#OC()#C)NC(=+)c1ccc(+OC)c(O)1
replace c at position 16 with B,C6#OC()#C)NC(=+)B1ccc(+OC)c(O)1
replace # at position 7 with +,C6#OC()+C)NC(=+)B1ccc(+OC)c(O)1
remove ) from position 29,C6#OC()+C)NC(=+)B1ccc(+OC)c(O1
remove c from position 19,C6#OC()+C)NC(=+)B1cc(+OC)c(O1
remove B from position 16,C6#OC()+C)NC(=+)1cc(+OC)c(O1
remove C from position 8,C6#OC()+)NC(=+)1cc(+OC)c(O1
replace # at position 2 with O,C6OOC()+)NC(=+)1cc(+OC)c(O1
remove ( from position 11,C6OOC()+)NC=+)1cc(+OC)c(O1
remove C from position 20,C6OOC()+)NC=+)1cc(+O)c(O1
replace 1 at position 14 with r,C6OOC()+)NC=+)rcc(+O)c(O1
remove C from position 0,6OOC()+)NC=+)rcc(+O)c(O1
replace O at position 2 with #,6O#C()+)NC=+)rcc(+O)c(O1
remove O from position 22,6O#C()+)NC=+)rcc(+O)c(1
remove ) from position 12,6O#C()+)NC=+rcc(+O)c(1
add H at position 2,6OH#C()+)NC=+rcc(+O)c(1
remove H from position 2,6O#C()+)NC=+rcc(+O)c(1
remove ( from position 4,6O#C)+)NC=+rcc(+O)c(1
add ] at position 11,6O#C)+)NC=+]rcc(+O)c(1
add - at position 16,6O#C)+)NC=+]rcc(-+O)c(1
remove c from position 14,6O#C)+)NC=+]rc(-+O)c(1
add O at position 8,6O#C)+)NOC=+]rc(-+O)c(1
replace + at position 5 with 4,6O#C)4)NOC=+]rc(-+O)c(1
add ] at position 4,6O#C])4)NOC=+]rc(-+O)c(1
replace O at position 1 with H,6H#C])4)NOC=+]rc(-+O)c(1
remove = from position 11,6H#C])4)NOC+]rc(-+O)c(1
remove O from position 18,6H#C])4)NOC+]rc(-+)c(1
replace ( at position 15 with ),6H#C])4)NOC+]rc)-+)c(1
replace ) at position 15 with o,6H#C])4)NOC+]rco-+)c(1
replace ) at position 5 with B,6H#C]B4)NOC+]rco-+)c(1
remove - from position 16,6H#C]B4)NOC+]rco+)c(1
replace O at position 9 with 3,6H#C]B4)N3C+]rco+)c(1
add = at position 5,6H#C]=B4)N3C+]rco+)c(1
remove C from position 3,6H#]=B4)N3C+]rco+)c(1
replace ] at position 3 with n,6H#n=B4)N3C+]rco+)c(1
replace ] at position 12 with n,6H#n=B4)N3C+nrco+)c(1
replace N at position 8 with O,6H#n=B4)O3C+nrco+)c(1
add S at position 3,6H#Sn=B4)O3C+nrco+)c(1
remove = from position 5,6H#SnB4)O3C+nrco+)c(1
remove O from position 8,6H#SnB4)3C+nrco+)c(1
replace + at position 10 with l,6H#SnB4)3Clnrco+)c(1
replace # at position 2 with +,6H+SnB4)3Clnrco+)c(1
remove H from position 1,6+SnB4)3Clnrco+)c(1
replace + at position 1 with 3,63SnB4)3Clnrco+)c(1
remove S from position 2,63nB4)3Clnrco+)c(1
replace r at position 10 with 4,63nB4)3Cln4co+)c(1
replace n at position 2 with H,63HB4)3Cln4co+)c(1
remove H from position 2,63B4)3Cln4co+)c(1
remove C from position 6,63B4)3ln4co+)c(1
add S at position 6,63B4)3Sln4co+)c(1
replace c at position 14 with C,63B4)3Sln4co+)C(1
add r at position 11,63B4)3Sln4cro+)C(1
replace ( at position 16 with r,63B4)3Sln4cro+)Cr1
remove 3 from position 5,63B4)Sln4cro+)Cr1
replace l at position 6 with 4,63B4)S4n4cro+)Cr1
add F at position 11,63B4)S4n4crFo+)Cr1
add B at position 17,63B4)S4n4crFo+)CrB1
remove ) from position 4,63B4S4n4crFo+)CrB1
add r at position 5,63B4Sr4n4crFo+)CrB1
add ) at position 17,63B4Sr4n4crFo+)Cr)B1
remove r from position 5,63B4S4n4crFo+)Cr)B1
add N at position 1,6N3B4S4n4crFo+)Cr)B1
add r at position 4,6N3Br4S4n4crFo+)Cr)B1
add 7 at position 13,6N3Br4S4n4crF7o+)Cr)B1
remove S from position 6,6N3Br44n4crF7o+)Cr)B1
replace 7 at position 12 with s,6N3Br44n4crFso+)Cr)B1
replace 4 at position 8 with H,6N3Br44nHcrFso+)Cr)B1
remove c from position 9,6N3Br44nHrFso+)Cr)B1
remove B from position 18,6N3Br44nHrFso+)Cr)1
replace ) at position 17 with +,6N3Br44nHrFso+)Cr+1
replace H at position 8 with =,6N3Br44n=rFso+)Cr+1
remove 1 from position 18,6N3Br44n=rFso+)Cr+
remove B from position 3,6N3r44n=rFso+)Cr+
remove r from position 8,6N3r44n=Fso+)Cr+
add ( at position 12,6N3r44n=Fso+()Cr+
remove C from position 14,6N3r44n=Fso+()r+
add / at position 15,6N3r44n=Fso+()r/+
remove 6 from position 0,N3r44n=Fso+()r/+
add 4 at position 12,N3r44n=Fso+(4)r/+
remove / from position 15,N3r44n=Fso+(4)r+
add / at position 15,N3r44n=Fso+(4)r/+
remove ( from position 11,N3r44n=Fso+4)r/+
remove 4 from position 11,N3r44n=Fso+)r/+
add c at position 14,N3r44n=Fso+)r/c+
remove N from position 0,3r44n=Fso+)r/c+
replace r at position 11 with @,3r44n=Fso+)@/c+
remove r from position 1,344n=Fso+)@/c+
add B at position 12,344n=Fso+)@/Bc+
add o at position 4,344no=Fso+)@/Bc+
replace ) at position 10 with 7,344no=Fso+7@/Bc+
replace + at position 9 with 5,344no=Fso57@/Bc+
remove 7 from position 10,344no=Fso5@/Bc+
remove o from position 4,344n=Fso5@/Bc+
remove 5 from position 8,344n=Fso@/Bc+
replace = at position 4 with ),344n)Fso@/Bc+
remove / from position 9,344n)Fso@Bc+
add 5 at position 8,344n)Fso5@Bc+
replace o at position 7 with 3,344n)Fs35@Bc+
add H at position 8,344n)Fs3H5@Bc+
remove 4 from position 1,34n)Fs3H5@Bc+
replace F at position 4 with 6,34n)6s3H5@Bc+
add 7 at position 0,734n)6s3H5@Bc+
add 1 at position 0,1734n)6s3H5@Bc+
remove ) from position 5,1734n6s3H5@Bc+
remove s from position 6,1734n63H5@Bc+
replace 7 at position 1 with o,1o34n63H5@Bc+
replace n at position 4 with 3,1o34363H5@Bc+
remove @ from position 9,1o34363H5Bc+
add ) at position 8,1o34363H)5Bc+
remove + from position 12,1o34363H)5Bc
add 5 at position 9,1o34363H)55Bc
replace 4 at position 3 with /,1o3/363H)55Bc
remove 5 from position 10,1o3/363H)5Bc
replace 3 at position 2 with r,1or/363H)5Bc
remove 5 from position 9,1or/363H)Bc
add s at position 8,1or/363Hs)Bc
replace c at position 11 with /,1or/363Hs)B/
replace s at position 8 with S,1or/363HS)B/
replace 6 at position 5 with -,1or/3-3HS)B/
remove S from position 8,1or/3-3H)B/
remove B from position 9,1or/3-3H)/
remove H from position 7,1or/3-3)/
add r at position 1,1ror/3-3)/
remove - from position 6,1ror/33)/
replace r at position 1 with 5,15or/33)/
add 6 at position 3,15o6r/33)/
replace ) at position 8 with [,15o6r/33[/
replace / at position 9 with C,15o6r/33[C
replace 1 at position 0 with B,B5o6r/33[C
replace r at position 4 with =,B5o6=/33[C
replace o at position 2 with B,B5B6=/33[C
remove 6 from position 3,B5B=/33[C
remove B from position 0,5B=/33[C
replace 3 at position 5 with I,5B=/3I[C
remove = from position 2,5B/3I[C
add 1 at position 0,15B/3I[C
replace B at position 2 with F,15F/3I[C
replace 3 at position 4 with O,15F/OI[C
remove C from position 7,15F/OI[
remove I from position 5,15F/O[
remove / from position 3,15FO[
remove F from position 2,15O[
add o at position 0,o15O[
remove 5 from position 2,o1O[
add ( at position 4,o1O[(
replace ( at position 4 with I,o1O[I
remove I from position 4,o1O[
add 6 at position 2,o16O[
replace 6 at position 2 with [,o1[O[
remove 1 from position 1,o[O[
remove [ from position 1,oO[
replace [ at position 2 with S,oOS
remove o from position 0,OS
remove O from position 0,S
remove S from position 0,
final: ,
