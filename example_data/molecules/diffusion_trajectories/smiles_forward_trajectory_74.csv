log,state
initialize: NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32)cc1,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32)cc1
replace C at position 28 with +,NC(=O)COc1ccc(C(=O)N[C@H]2CC+c3ccccc32)cc1
add ( at position 37,NC(=O)COc1ccc(C(=O)N[C@H]2CC+c3ccccc3(2)cc1
remove ) from position 5,NC(=OCOc1ccc(C(=O)N[C@H]2CC+c3ccccc3(2)cc1
add 2 at position 22,NC(=OCOc1ccc(C(=O)N[C@2H]2CC+c3ccccc3(2)cc1
add 6 at position 1,N6C(=OCOc1ccc(C(=O)N[C@2H]2CC+c3ccccc3(2)cc1
remove c from position 30,N6C(=OCOc1ccc(C(=O)N[C@2H]2CC+3ccccc3(2)cc1
remove 1 from position 9,N6C(=OCOcccc(C(=O)N[C@2H]2CC+3ccccc3(2)cc1
add n at position 7,N6C(=OCnOcccc(C(=O)N[C@2H]2CC+3ccccc3(2)cc1
add o at position 31,N6C(=OCnOcccc(C(=O)N[C@2H]2CC+3occccc3(2)cc1
replace = at position 4 with S,N6C(SOCnOcccc(C(=O)N[C@2H]2CC+3occccc3(2)cc1
remove + from position 29,N6C(SOCnOcccc(C(=O)N[C@2H]2CC3occccc3(2)cc1
add ) at position 43,N6C(SOCnOcccc(C(=O)N[C@2H]2CC3occccc3(2)cc1)
remove C from position 2,N6(SOCnOcccc(C(=O)N[C@2H]2CC3occccc3(2)cc1)
remove 3 from position 35,N6(SOCnOcccc(C(=O)N[C@2H]2CC3occccc(2)cc1)
add B at position 19,N6(SOCnOcccc(C(=O)NB[C@2H]2CC3occccc(2)cc1)
replace ) at position 17 with 3,N6(SOCnOcccc(C(=O3NB[C@2H]2CC3occccc(2)cc1)
remove ( from position 12,N6(SOCnOccccC(=O3NB[C@2H]2CC3occccc(2)cc1)
remove c from position 33,N6(SOCnOccccC(=O3NB[C@2H]2CC3occcc(2)cc1)
remove c from position 32,N6(SOCnOccccC(=O3NB[C@2H]2CC3occc(2)cc1)
add - at position 21,N6(SOCnOccccC(=O3NB[C-@2H]2CC3occc(2)cc1)
remove H from position 24,N6(SOCnOccccC(=O3NB[C-@2]2CC3occc(2)cc1)
add H at position 4,N6(SHOCnOccccC(=O3NB[C-@2]2CC3occc(2)cc1)
remove H from position 4,N6(SOCnOccccC(=O3NB[C-@2]2CC3occc(2)cc1)
remove c from position 9,N6(SOCnOcccC(=O3NB[C-@2]2CC3occc(2)cc1)
add ] at position 22,N6(SOCnOcccC(=O3NB[C-@]2]2CC3occc(2)cc1)
add - at position 33,N6(SOCnOcccC(=O3NB[C-@]2]2CC3occc-(2)cc1)
remove o from position 29,N6(SOCnOcccC(=O3NB[C-@]2]2CC3ccc-(2)cc1)
add O at position 17,N6(SOCnOcccC(=O3NOB[C-@]2]2CC3ccc-(2)cc1)
replace C at position 11 with 3,N6(SOCnOccc3(=O3NOB[C-@]2]2CC3ccc-(2)cc1)
add ] at position 9,N6(SOCnOc]cc3(=O3NOB[C-@]2]2CC3ccc-(2)cc1)
replace ( at position 2 with I,N6ISOCnOc]cc3(=O3NOB[C-@]2]2CC3ccc-(2)cc1)
remove - from position 22,N6ISOCnOc]cc3(=O3NOB[C@]2]2CC3ccc-(2)cc1)
remove c from position 37,N6ISOCnOc]cc3(=O3NOB[C@]2]2CC3ccc-(2)c1)
replace c at position 30 with (,N6ISOCnOc]cc3(=O3NOB[C@]2]2CC3(cc-(2)c1)
replace ( at position 30 with o,N6ISOCnOc]cc3(=O3NOB[C@]2]2CC3occ-(2)c1)
replace c at position 10 with @,N6ISOCnOc]@c3(=O3NOB[C@]2]2CC3occ-(2)c1)
remove - from position 33,N6ISOCnOc]@c3(=O3NOB[C@]2]2CC3occ(2)c1)
replace O at position 18 with 3,N6ISOCnOc]@c3(=O3N3B[C@]2]2CC3occ(2)c1)
add = at position 11,N6ISOCnOc]@=c3(=O3N3B[C@]2]2CC3occ(2)c1)
remove O from position 7,N6ISOCnc]@=c3(=O3N3B[C@]2]2CC3occ(2)c1)
replace n at position 6 with l,N6ISOClc]@=c3(=O3N3B[C@]2]2CC3occ(2)c1)
replace 2 at position 24 with n,N6ISOClc]@=c3(=O3N3B[C@]n]2CC3occ(2)c1)
replace 3 at position 16 with O,N6ISOClc]@=c3(=OON3B[C@]n]2CC3occ(2)c1)
add S at position 6,N6ISOCSlc]@=c3(=OON3B[C@]n]2CC3occ(2)c1)
remove @ from position 10,N6ISOCSlc]=c3(=OON3B[C@]n]2CC3occ(2)c1)
remove N from position 17,N6ISOCSlc]=c3(=OO3B[C@]n]2CC3occ(2)c1)
replace C at position 20 with l,N6ISOCSlc]=c3(=OO3B[l@]n]2CC3occ(2)c1)
replace O at position 4 with ),N6IS)CSlc]=c3(=OO3B[l@]n]2CC3occ(2)c1)
remove S from position 3,N6I)CSlc]=c3(=OO3B[l@]n]2CC3occ(2)c1)
replace I at position 2 with 2,N62)CSlc]=c3(=OO3B[l@]n]2CC3occ(2)c1)
remove S from position 5,N62)Clc]=c3(=OO3B[l@]n]2CC3occ(2)c1)
replace ] at position 20 with 4,N62)Clc]=c3(=OO3B[l@4n]2CC3occ(2)c1)
replace C at position 4 with I,N62)Ilc]=c3(=OO3B[l@4n]2CC3occ(2)c1)
remove I from position 4,N62)lc]=c3(=OO3B[l@4n]2CC3occ(2)c1)
remove O from position 12,N62)lc]=c3(=O3B[l@4n]2CC3occ(2)c1)
add S at position 12,N62)lc]=c3(=SO3B[l@4n]2CC3occ(2)c1)
replace ( at position 29 with F,N62)lc]=c3(=SO3B[l@4n]2CC3occF2)c1)
add r at position 23,N62)lc]=c3(=SO3B[l@4n]2rCC3occF2)c1)
replace ) at position 32 with r,N62)lc]=c3(=SO3B[l@4n]2rCC3occF2rc1)
remove = from position 11,N62)lc]=c3(SO3B[l@4n]2rCC3occF2rc1)
replace 3 at position 13 with 5,N62)lc]=c3(SO5B[l@4n]2rCC3occF2rc1)
add F at position 22,N62)lc]=c3(SO5B[l@4n]2FrCC3occF2rc1)
add B at position 35,N62)lc]=c3(SO5B[l@4n]2FrCC3occF2rc1B)
remove c from position 8,N62)lc]=3(SO5B[l@4n]2FrCC3occF2rc1B)
add r at position 10,N62)lc]=3(rSO5B[l@4n]2FrCC3occF2rc1B)
add ) at position 34,N62)lc]=3(rSO5B[l@4n]2FrCC3occF2rc)1B)
remove r from position 10,N62)lc]=3(SO5B[l@4n]2FrCC3occF2rc)1B)
add N at position 3,N62N)lc]=3(SO5B[l@4n]2FrCC3occF2rc)1B)
add r at position 9,N62N)lc]=r3(SO5B[l@4n]2FrCC3occF2rc)1B)
add 7 at position 27,N62N)lc]=r3(SO5B[l@4n]2FrCC73occF2rc)1B)
remove O from position 13,N62N)lc]=r3(S5B[l@4n]2FrCC73occF2rc)1B)
replace C at position 24 with s,N62N)lc]=r3(S5B[l@4n]2FrsC73occF2rc)1B)
replace @ at position 17 with H,N62N)lc]=r3(S5B[lH4n]2FrsC73occF2rc)1B)
remove n from position 19,N62N)lc]=r3(S5B[lH4]2FrsC73occF2rc)1B)
remove B from position 36,N62N)lc]=r3(S5B[lH4]2FrsC73occF2rc)1)
replace ) at position 34 with +,N62N)lc]=r3(S5B[lH4]2FrsC73occF2rc+1)
replace H at position 17 with =,N62N)lc]=r3(S5B[l=4]2FrsC73occF2rc+1)
remove ) from position 36,N62N)lc]=r3(S5B[l=4]2FrsC73occF2rc+1
remove ] from position 7,N62N)lc=r3(S5B[l=4]2FrsC73occF2rc+1
remove 4 from position 17,N62N)lc=r3(S5B[l=]2FrsC73occF2rc+1
add ( at position 25,N62N)lc=r3(S5B[l=]2FrsC73(occF2rc+1
remove c from position 28,N62N)lc=r3(S5B[l=]2FrsC73(ocF2rc+1
add / at position 31,N62N)lc=r3(S5B[l=]2FrsC73(ocF2r/c+1
remove N from position 0,62N)lc=r3(S5B[l=]2FrsC73(ocF2r/c+1
add 4 at position 25,62N)lc=r3(S5B[l=]2FrsC73(4ocF2r/c+1
remove r from position 30,62N)lc=r3(S5B[l=]2FrsC73(4ocF2/c+1
add / at position 30,62N)lc=r3(S5B[l=]2FrsC73(4ocF2//c+1
remove 3 from position 23,62N)lc=r3(S5B[l=]2FrsC7(4ocF2//c+1
remove 7 from position 22,62N)lc=r3(S5B[l=]2FrsC(4ocF2//c+1
add r at position 33,62N)lc=r3(S5B[l=]2FrsC(4ocF2//c+1r
remove c from position 30,62N)lc=r3(S5B[l=]2FrsC(4ocF2//+1r
remove / from position 29,62N)lc=r3(S5B[l=]2FrsC(4ocF2/+1r
remove = from position 6,62N)lcr3(S5B[l=]2FrsC(4ocF2/+1r
add ) at position 30,62N)lcr3(S5B[l=]2FrsC(4ocF2/+1)r
replace ( at position 21 with 7,62N)lcr3(S5B[l=]2FrsC74ocF2/+1)r
replace r at position 18 with 4,62N)lcr3(S5B[l=]2F4sC74ocF2/+1)r
remove 7 from position 21,62N)lcr3(S5B[l=]2F4sC4ocF2/+1)r
remove r from position 30,62N)lcr3(S5B[l=]2F4sC4ocF2/+1)
remove r from position 6,62N)lc3(S5B[l=]2F4sC4ocF2/+1)
remove F from position 23,62N)lc3(S5B[l=]2F4sC4oc2/+1)
remove 4 from position 17,62N)lc3(S5B[l=]2FsC4oc2/+1)
remove 3 from position 6,62N)lc(S5B[l=]2FsC4oc2/+1)
remove [ from position 10,62N)lc(S5Bl=]2FsC4oc2/+1)
replace F at position 14 with 3,62N)lc(S5Bl=]23sC4oc2/+1)
add H at position 16,62N)lc(S5Bl=]23sHC4oc2/+1)
remove ) from position 3,62Nlc(S5Bl=]23sHC4oc2/+1)
replace B at position 8 with 6,62Nlc(S56l=]23sHC4oc2/+1)
add 7 at position 0,762Nlc(S56l=]23sHC4oc2/+1)
add 1 at position 1,7162Nlc(S56l=]23sHC4oc2/+1)
remove 6 from position 10,7162Nlc(S5l=]23sHC4oc2/+1)
remove 2 from position 13,7162Nlc(S5l=]3sHC4oc2/+1)
replace 6 at position 2 with o,71o2Nlc(S5l=]3sHC4oc2/+1)
replace 5 at position 9 with 3,71o2Nlc(S3l=]3sHC4oc2/+1)
remove o from position 18,71o2Nlc(S3l=]3sHC4c2/+1)
add ) at position 16,71o2Nlc(S3l=]3sH)C4c2/+1)
remove 7 from position 0,1o2Nlc(S3l=]3sH)C4c2/+1)
remove = from position 10,1o2Nlc(S3l]3sH)C4c2/+1)
replace S at position 7 with /,1o2Nlc(/3l]3sH)C4c2/+1)
remove 1 from position 21,1o2Nlc(/3l]3sH)C4c2/+)
replace l at position 4 with r,1o2Nrc(/3l]3sH)C4c2/+)
remove / from position 19,1o2Nrc(/3l]3sH)C4c2+)
add s at position 17,1o2Nrc(/3l]3sH)C4sc2+)
replace c at position 5 with s,1o2Nrs(/3l]3sH)C4sc2+)
remove 4 from position 16,1o2Nrs(/3l]3sH)Csc2+)
remove s from position 12,1o2Nrs(/3l]3H)Csc2+)
replace ] at position 10 with -,1o2Nrs(/3l-3H)Csc2+)
remove 2 from position 17,1o2Nrs(/3l-3H)Csc+)
remove C from position 14,1o2Nrs(/3l-3H)sc+)
add r at position 2,1or2Nrs(/3l-3H)sc+)
remove H from position 13,1or2Nrs(/3l-3)sc+)
replace r at position 2 with 5,1o52Nrs(/3l-3)sc+)
add 6 at position 6,1o52Nr6s(/3l-3)sc+)
replace c at position 16 with S,1o52Nr6s(/3l-3)sS+)
replace ) at position 18 with C,1o52Nr6s(/3l-3)sS+C
replace 1 at position 0 with B,Bo52Nr6s(/3l-3)sS+C
replace / at position 9 with @,Bo52Nr6s(@3l-3)sS+C
replace N at position 4 with B,Bo52Br6s(@3l-3)sS+C
remove s from position 7,Bo52Br6(@3l-3)sS+C
remove B from position 0,o52Br6(@3l-3)sS+C
replace 3 at position 11 with I,o52Br6(@3l-I)sS+C
remove 6 from position 5,o52Br(@3l-I)sS+C
add 1 at position 0,1o52Br(@3l-I)sS+C
replace B at position 4 with F,1o52Fr(@3l-I)sS+C
replace 3 at position 8 with O,1o52Fr(@Ol-I)sS+C
remove S from position 14,1o52Fr(@Ol-I)s+C
remove I from position 11,1o52Fr(@Ol-)s+C
remove ( from position 6,1o52Fr@Ol-)s+C
replace 2 at position 3 with 5,1o55Fr@Ol-)s+C
add H at position 5,1o55FHr@Ol-)s+C
replace o at position 1 with F,1F55FHr@Ol-)s+C
remove + from position 13,1F55FHr@Ol-)sC
add H at position 9,1F55FHr@OHl-)sC
remove 5 from position 2,1F5FHr@OHl-)sC
remove r from position 5,1F5FH@OHl-)sC
replace H at position 4 with [,1F5F[@OHl-)sC
remove 5 from position 2,1FF[@OHl-)sC
remove F from position 2,1F[@OHl-)sC
replace ) at position 8 with [,1F[@OHl-[sC
remove @ from position 3,1F[OHl-[sC
remove [ from position 2,1FOHl-[sC
remove [ from position 6,1FOHl-sC
remove C from position 7,1FOHl-s
replace l at position 4 with @,1FOH@-s
remove s from position 6,1FOH@-
remove @ from position 4,1FOH-
add C at position 5,1FOH-C
replace - at position 4 with /,1FOH/C
add B at position 3,1FOBH/C
add c at position 2,1FcOBH/C
replace O at position 3 with [,1Fc[BH/C
add s at position 6,1Fc[BHs/C
remove C from position 8,1Fc[BHs/
remove [ from position 3,1FcBHs/
remove s from position 5,1FcBH/
replace B at position 3 with 5,1Fc5H/
replace H at position 4 with +,1Fc5+/
remove 5 from position 3,1Fc+/
add / at position 1,1/Fc+/
remove F from position 2,1/c+/
add S at position 2,1/Sc+/
replace S at position 2 with #,1/#c+/
add 5 at position 5,1/#c+5/
remove 1 from position 0,/#c+5/
add 3 at position 5,/#c+53/
remove c from position 2,/#+53/
remove 3 from position 4,/#+5/
replace / at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
