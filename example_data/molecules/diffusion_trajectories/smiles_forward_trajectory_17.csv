log,state
initialize: C/C=C/C[C@]1(C(=O)[O-])CCN(C(=O)OC(C)(C)C)C1,C/C=C/C[C@]1(C(=O)[O-])CCN(C(=O)OC(C)(C)C)C1
replace ( at position 28 with -,C/C=C/C[C@]1(C(=O)[O-])CCN(C-=O)OC(C)(C)C)C1
add ( at position 37,C/C=C/C[C@]1(C(=O)[O-])CCN(C-=O)OC(C)((C)C)C1
remove / from position 5,C/C=CC[C@]1(C(=O)[O-])CCN(C-=O)OC(C)((C)C)C1
add 2 at position 22,C/C=CC[C@]1(C(=O)[O-])2CCN(C-=O)OC(C)((C)C)C1
add 6 at position 1,C6/C=CC[C@]1(C(=O)[O-])2CCN(C-=O)OC(C)((C)C)C1
remove = from position 30,C6/C=CC[C@]1(C(=O)[O-])2CCN(C-O)OC(C)((C)C)C1
remove @ from position 9,C6/C=CC[C]1(C(=O)[O-])2CCN(C-O)OC(C)((C)C)C1
add n at position 7,C6/C=CCn[C]1(C(=O)[O-])2CCN(C-O)OC(C)((C)C)C1
add o at position 31,C6/C=CCn[C]1(C(=O)[O-])2CCN(C-Oo)OC(C)((C)C)C1
replace = at position 4 with S,C6/CSCCn[C]1(C(=O)[O-])2CCN(C-Oo)OC(C)((C)C)C1
remove - from position 29,C6/CSCCn[C]1(C(=O)[O-])2CCN(COo)OC(C)((C)C)C1
add ) at position 43,C6/CSCCn[C]1(C(=O)[O-])2CCN(COo)OC(C)((C)C))C1
remove / from position 2,C6CSCCn[C]1(C(=O)[O-])2CCN(COo)OC(C)((C)C))C1
remove ) from position 35,C6CSCCn[C]1(C(=O)[O-])2CCN(COo)OC(C((C)C))C1
add B at position 19,C6CSCCn[C]1(C(=O)[OB-])2CCN(COo)OC(C((C)C))C1
replace [ at position 17 with 2,C6CSCCn[C]1(C(=O)2OB-])2CCN(COo)OC(C((C)C))C1
remove C from position 12,C6CSCCn[C]1((=O)2OB-])2CCN(COo)OC(C((C)C))C1
remove ( from position 33,C6CSCCn[C]1((=O)2OB-])2CCN(COo)OCC((C)C))C1
remove C from position 32,C6CSCCn[C]1((=O)2OB-])2CCN(COo)OC((C)C))C1
add - at position 21,C6CSCCn[C]1((=O)2OB-]-)2CCN(COo)OC((C)C))C1
remove 1 from position 42,C6CSCCn[C]1((=O)2OB-]-)2CCN(COo)OC((C)C))C
remove S from position 3,C6CCCn[C]1((=O)2OB-]-)2CCN(COo)OC((C)C))C
add / at position 31,C6CCCn[C]1((=O)2OB-]-)2CCN(COo)/OC((C)C))C
add - at position 13,C6CCCn[C]1((=-O)2OB-]-)2CCN(COo)/OC((C)C))C
add ] at position 22,C6CCCn[C]1((=-O)2OB-]-])2CCN(COo)/OC((C)C))C
add - at position 33,C6CCCn[C]1((=-O)2OB-]-])2CCN(COo)-/OC((C)C))C
remove C from position 29,C6CCCn[C]1((=-O)2OB-]-])2CCN(Oo)-/OC((C)C))C
add O at position 17,C6CCCn[C]1((=-O)2OOB-]-])2CCN(Oo)-/OC((C)C))C
replace ( at position 11 with 4,C6CCCn[C]1(4=-O)2OOB-]-])2CCN(Oo)-/OC((C)C))C
add ] at position 9,C6CCCn[C]]1(4=-O)2OOB-]-])2CCN(Oo)-/OC((C)C))C
replace C at position 2 with I,C6ICCn[C]]1(4=-O)2OOB-]-])2CCN(Oo)-/OC((C)C))C
remove ] from position 22,C6ICCn[C]]1(4=-O)2OOB--])2CCN(Oo)-/OC((C)C))C
remove ( from position 37,C6ICCn[C]]1(4=-O)2OOB--])2CCN(Oo)-/OC(C)C))C
replace O at position 30 with (,C6ICCn[C]]1(4=-O)2OOB--])2CCN((o)-/OC(C)C))C
replace ( at position 30 with o,C6ICCn[C]]1(4=-O)2OOB--])2CCN(oo)-/OC(C)C))C
replace 1 at position 10 with B,C6ICCn[C]]B(4=-O)2OOB--])2CCN(oo)-/OC(C)C))C
remove - from position 33,C6ICCn[C]]B(4=-O)2OOB--])2CCN(oo)/OC(C)C))C
replace O at position 18 with 3,C6ICCn[C]]B(4=-O)23OB--])2CCN(oo)/OC(C)C))C
add = at position 11,C6ICCn[C]]B=(4=-O)23OB--])2CCN(oo)/OC(C)C))C
remove C from position 7,C6ICCn[]]B=(4=-O)23OB--])2CCN(oo)/OC(C)C))C
replace [ at position 6 with n,C6ICCnn]]B=(4=-O)23OB--])2CCN(oo)/OC(C)C))C
replace ) at position 24 with n,C6ICCnn]]B=(4=-O)23OB--]n2CCN(oo)/OC(C)C))C
replace ) at position 16 with O,C6ICCnn]]B=(4=-OO23OB--]n2CCN(oo)/OC(C)C))C
add S at position 6,C6ICCnSn]]B=(4=-OO23OB--]n2CCN(oo)/OC(C)C))C
remove B from position 10,C6ICCnSn]]=(4=-OO23OB--]n2CCN(oo)/OC(C)C))C
remove 2 from position 17,C6ICCnSn]]=(4=-OO3OB--]n2CCN(oo)/OC(C)C))C
replace - at position 20 with l,C6ICCnSn]]=(4=-OO3OBl-]n2CCN(oo)/OC(C)C))C
replace C at position 4 with ),C6IC)nSn]]=(4=-OO3OBl-]n2CCN(oo)/OC(C)C))C
remove C from position 3,C6I)nSn]]=(4=-OO3OBl-]n2CCN(oo)/OC(C)C))C
replace I at position 2 with 2,C62)nSn]]=(4=-OO3OBl-]n2CCN(oo)/OC(C)C))C
remove S from position 5,C62)nn]]=(4=-OO3OBl-]n2CCN(oo)/OC(C)C))C
replace ] at position 20 with 4,C62)nn]]=(4=-OO3OBl-4n2CCN(oo)/OC(C)C))C
replace n at position 4 with H,C62)Hn]]=(4=-OO3OBl-4n2CCN(oo)/OC(C)C))C
remove H from position 4,C62)n]]=(4=-OO3OBl-4n2CCN(oo)/OC(C)C))C
remove O from position 12,C62)n]]=(4=-O3OBl-4n2CCN(oo)/OC(C)C))C
add S at position 12,C62)n]]=(4=-SO3OBl-4n2CCN(oo)/OC(C)C))C
replace / at position 29 with F,C62)n]]=(4=-SO3OBl-4n2CCN(oo)FOC(C)C))C
add r at position 23,C62)n]]=(4=-SO3OBl-4n2CrCN(oo)FOC(C)C))C
replace C at position 32 with r,C62)n]]=(4=-SO3OBl-4n2CrCN(oo)FOr(C)C))C
remove - from position 11,C62)n]]=(4=SO3OBl-4n2CrCN(oo)FOr(C)C))C
replace 3 at position 13 with 5,C62)n]]=(4=SO5OBl-4n2CrCN(oo)FOr(C)C))C
add F at position 22,C62)n]]=(4=SO5OBl-4n2CFrCN(oo)FOr(C)C))C
add B at position 35,C62)n]]=(4=SO5OBl-4n2CFrCN(oo)FOr(CB)C))C
remove ( from position 8,C62)n]]=4=SO5OBl-4n2CFrCN(oo)FOr(CB)C))C
add r at position 10,C62)n]]=4=rSO5OBl-4n2CFrCN(oo)FOr(CB)C))C
add ) at position 34,C62)n]]=4=rSO5OBl-4n2CFrCN(oo)FOr()CB)C))C
remove r from position 10,C62)n]]=4=SO5OBl-4n2CFrCN(oo)FOr()CB)C))C
add N at position 3,C62N)n]]=4=SO5OBl-4n2CFrCN(oo)FOr()CB)C))C
add r at position 9,C62N)n]]=r4=SO5OBl-4n2CFrCN(oo)FOr()CB)C))C
add 7 at position 27,C62N)n]]=r4=SO5OBl-4n2CFrCN7(oo)FOr()CB)C))C
remove O from position 13,C62N)n]]=r4=S5OBl-4n2CFrCN7(oo)FOr()CB)C))C
replace C at position 24 with s,C62N)n]]=r4=S5OBl-4n2CFrsN7(oo)FOr()CB)C))C
replace - at position 17 with H,C62N)n]]=r4=S5OBlH4n2CFrsN7(oo)FOr()CB)C))C
remove n from position 19,C62N)n]]=r4=S5OBlH42CFrsN7(oo)FOr()CB)C))C
remove B from position 36,C62N)n]]=r4=S5OBlH42CFrsN7(oo)FOr()C)C))C
replace ) at position 34 with +,C62N)n]]=r4=S5OBlH42CFrsN7(oo)FOr(+C)C))C
replace H at position 17 with =,C62N)n]]=r4=S5OBl=42CFrsN7(oo)FOr(+C)C))C
remove ) from position 36,C62N)n]]=r4=S5OBl=42CFrsN7(oo)FOr(+CC))C
remove ] from position 7,C62N)n]=r4=S5OBl=42CFrsN7(oo)FOr(+CC))C
remove 4 from position 17,C62N)n]=r4=S5OBl=2CFrsN7(oo)FOr(+CC))C
add 7 at position 37,C62N)n]=r4=S5OBl=2CFrsN7(oo)FOr(+CC))7C
add 2 at position 28,C62N)n]=r4=S5OBl=2CFrsN7(oo)2FOr(+CC))7C
replace = at position 10 with 7,C62N)n]=r47S5OBl=2CFrsN7(oo)2FOr(+CC))7C
remove ) from position 4,C62Nn]=r47S5OBl=2CFrsN7(oo)2FOr(+CC))7C
remove r from position 19,C62Nn]=r47S5OBl=2CFsN7(oo)2FOr(+CC))7C
remove ( from position 30,C62Nn]=r47S5OBl=2CFsN7(oo)2FOr+CC))7C
add / at position 30,C62Nn]=r47S5OBl=2CFsN7(oo)2FOr/+CC))7C
remove o from position 23,C62Nn]=r47S5OBl=2CFsN7(o)2FOr/+CC))7C
remove ( from position 22,C62Nn]=r47S5OBl=2CFsN7o)2FOr/+CC))7C
add r at position 33,C62Nn]=r47S5OBl=2CFsN7o)2FOr/+CC)r)7C
remove C from position 30,C62Nn]=r47S5OBl=2CFsN7o)2FOr/+C)r)7C
remove + from position 29,C62Nn]=r47S5OBl=2CFsN7o)2FOr/C)r)7C
remove = from position 6,C62Nn]r47S5OBl=2CFsN7o)2FOr/C)r)7C
add ) at position 30,C62Nn]r47S5OBl=2CFsN7o)2FOr/C))r)7C
replace o at position 21 with 6,C62Nn]r47S5OBl=2CFsN76)2FOr/C))r)7C
replace s at position 18 with 4,C62Nn]r47S5OBl=2CF4N76)2FOr/C))r)7C
remove 6 from position 21,C62Nn]r47S5OBl=2CF4N7)2FOr/C))r)7C
remove 4 from position 18,C62Nn]r47S5OBl=2CFN7)2FOr/C))r)7C
remove C from position 32,C62Nn]r47S5OBl=2CFN7)2FOr/C))r)7
replace F at position 17 with ),C62Nn]r47S5OBl=2C)N7)2FOr/C))r)7
remove l from position 13,C62Nn]r47S5OB=2C)N7)2FOr/C))r)7
remove 5 from position 10,C62Nn]r47SOB=2C)N7)2FOr/C))r)7
replace C at position 14 with 3,C62Nn]r47SOB=23)N7)2FOr/C))r)7
add H at position 16,C62Nn]r47SOB=23)HN7)2FOr/C))r)7
remove N from position 3,C62n]r47SOB=23)HN7)2FOr/C))r)7
replace S at position 8 with 6,C62n]r476OB=23)HN7)2FOr/C))r)7
add 7 at position 0,7C62n]r476OB=23)HN7)2FOr/C))r)7
add 1 at position 2,7C162n]r476OB=23)HN7)2FOr/C))r)7
remove ) from position 20,7C162n]r476OB=23)HN72FOr/C))r)7
remove = from position 13,7C162n]r476OB23)HN72FOr/C))r)7
replace 7 at position 29 with ),7C162n]r476OB23)HN72FOr/C))r))
replace 7 at position 9 with 3,7C162n]r436OB23)HN72FOr/C))r))
remove ) from position 29,7C162n]r436OB23)HN72FOr/C))r)
remove O from position 11,7C162n]r436B23)HN72FOr/C))r)
remove O from position 20,7C162n]r436B23)HN72Fr/C))r)
replace 7 at position 0 with S,SC162n]r436B23)HN72Fr/C))r)
remove 2 from position 18,SC162n]r436B23)HN7Fr/C))r)
add B at position 15,SC162n]r436B23)BHN7Fr/C))r)
remove / from position 21,SC162n]r436B23)BHN7FrC))r)
replace 2 at position 4 with r,SC16rn]r436B23)BHN7FrC))r)
remove F from position 19,SC16rn]r436B23)BHN7rC))r)
add s at position 17,SC16rn]r436B23)BHsN7rC))r)
replace ) at position 22 with 1,SC16rn]r436B23)BHsN7rC1)r)
replace r at position 24 with C,SC16rn]r436B23)BHsN7rC1)C)
remove 2 from position 12,SC16rn]r436B3)BHsN7rC1)C)
replace 6 at position 10 with -,SC16rn]r43-B3)BHsN7rC1)C)
remove N from position 17,SC16rn]r43-B3)BHs7rC1)C)
remove C from position 19,SC16rn]r43-B3)BHs7r1)C)
remove B from position 14,SC16rn]r43-B3)Hs7r1)C)
add r at position 2,SCr16rn]r43-B3)Hs7r1)C)
remove C from position 21,SCr16rn]r43-B3)Hs7r1))
remove C from position 1,Sr16rn]r43-B3)Hs7r1))
remove 1 from position 2,Sr6rn]r43-B3)Hs7r1))
remove ) from position 19,Sr6rn]r43-B3)Hs7r1)
replace 3 at position 11 with F,Sr6rn]r43-BF)Hs7r1)
remove ) from position 12,Sr6rn]r43-BFHs7r1)
remove r from position 1,S6rn]r43-BFHs7r1)
add l at position 2,S6lrn]r43-BFHs7r1)
remove B from position 10,S6lrn]r43-FHs7r1)
add S at position 7,S6lrn]rS43-FHs7r1)
replace S at position 0 with o,o6lrn]rS43-FHs7r1)
remove F from position 11,o6lrn]rS43-Hs7r1)
remove l from position 2,o6rn]rS43-Hs7r1)
remove S from position 6,o6rn]r43-Hs7r1)
replace 1 at position 13 with N,o6rn]r43-Hs7rN)
remove 7 from position 11,o6rn]r43-HsrN)
add O at position 2,o6Orn]r43-HsrN)
replace H at position 10 with ],o6Orn]r43-]srN)
add 6 at position 14,o6Orn]r43-]srN6)
remove s from position 11,o6Orn]r43-]rN6)
remove r from position 6,o6Orn]43-]rN6)
replace r at position 3 with 4,o6O4n]43-]rN6)
add H at position 5,o6O4nH]43-]rN6)
replace 6 at position 1 with H,oHO4nH]43-]rN6)
remove 6 from position 13,oHO4nH]43-]rN)
add H at position 9,oHO4nH]43H-]rN)
remove O from position 2,oH4nH]43H-]rN)
remove ] from position 5,oH4nH43H-]rN)
replace H at position 4 with [,oH4n[43H-]rN)
remove 4 from position 2,oHn[43H-]rN)
remove n from position 2,oH[43H-]rN)
replace r at position 8 with S,oH[43H-]SN)
remove 4 from position 3,oH[3H-]SN)
remove [ from position 2,oH3H-]SN)
remove S from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
