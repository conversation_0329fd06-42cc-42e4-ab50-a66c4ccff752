log,state
initialize: ,
add N at position 0,N
add I at position 0,IN
add 1 at position 0,1IN
replace N at position 2 with B,1IB
remove 1 from position 0,IB
add = at position 0,=IB
replace I at position 1 with -,=-B
add B at position 0,B=-B
add C at position 1,BC=-B
replace <PERSON> at position 1 with 6,B6=-B
replace = at position 2 with n,B6n-B
replace <PERSON> at position 0 with 5,56n-<PERSON>
replace <PERSON> at position 4 with [,56n-[
replace [ at position 4 with 5,56n-5
remove 6 from position 1,5n-5
replace 5 at position 0 with r,rn-5
add - at position 3,rn--5
remove r from position 0,n--5
add 2 at position 3,n--25
add H at position 4,n--2H5
add ) at position 4,n--2)H5
replace - at position 2 with /,n-/2)H5
add ) at position 6,n-/2)H)5
add ( at position 3,n-/(2)H)5
replace - at position 1 with l,nl/(2)H)5
add n at position 2,nln/(2)H)5
replace / at position 3 with 4,nln4(2)H)5
add - at position 5,nln4(-2)H)5
add c at position 0,cnln4(-2)H)5
remove ) from position 8,cnln4(-2H)5
add - at position 9,cnln4(-2H-)5
replace 4 at position 4 with -,cnln-(-2H-)5
add 6 at position 8,cnln-(-26H-)5
replace 6 at position 8 with s,cnln-(-2sH-)5
replace - at position 10 with C,cnln-(-2sHC)5
add O at position 0,Ocnln-(-2sHC)5
replace ( at position 6 with 5,Ocnln-5-2sHC)5
remove - from position 5,Ocnln5-2sHC)5
add s at position 13,Ocnln5-2sHC)5s
replace H at position 9 with r,Ocnln5-2srC)5s
add # at position 1,O#cnln5-2srC)5s
add 3 at position 10,O#cnln5-2s3rC)5s
remove 5 from position 6,O#cnln-2s3rC)5s
add H at position 8,O#cnln-2Hs3rC)5s
add 4 at position 9,O#cnln-2H4s3rC)5s
add 6 at position 10,O#cnln-2H46s3rC)5s
replace 4 at position 9 with 5,O#cnln-2H56s3rC)5s
replace 6 at position 10 with s,O#cnln-2H5ss3rC)5s
remove ) from position 15,O#cnln-2H5ss3rC5s
add o at position 3,O#conln-2H5ss3rC5s
add 7 at position 14,O#conln-2H5ss37rC5s
add l at position 15,O#conln-2H5ss37lrC5s
remove r from position 16,O#conln-2H5ss37lC5s
add O at position 11,O#conln-2H5Oss37lC5s
add = at position 11,O#conln-2H5=Oss37lC5s
add = at position 5,O#con=ln-2H5=Oss37lC5s
replace = at position 5 with 1,O#con1ln-2H5=Oss37lC5s
add S at position 21,O#con1ln-2H5=Oss37lC5Ss
add F at position 9,O#con1ln-F2H5=Oss37lC5Ss
add I at position 2,O#Icon1ln-F2H5=Oss37lC5Ss
add s at position 12,O#Icon1ln-F2sH5=Oss37lC5Ss
add ( at position 24,O#Icon1ln-F2sH5=Oss37lC5(Ss
add ] at position 26,O#Icon1ln-F2sH5=Oss37lC5(S]s
add = at position 1,O=#Icon1ln-F2sH5=Oss37lC5(S]s
remove l from position 22,O=#Icon1ln-F2sH5=Oss37C5(S]s
add O at position 8,O=#Icon1Oln-F2sH5=Oss37C5(S]s
add N at position 3,O=#NIcon1Oln-F2sH5=Oss37C5(S]s
add ] at position 18,O=#NIcon1Oln-F2sH5]=Oss37C5(S]s
add 1 at position 26,O=#NIcon1Oln-F2sH5]=Oss37C15(S]s
replace c at position 5 with (,O=#NI(on1Oln-F2sH5]=Oss37C15(S]s
add r at position 9,O=#NI(on1rOln-F2sH5]=Oss37C15(S]s
add l at position 19,O=#NI(on1rOln-F2sH5l]=Oss37C15(S]s
replace H at position 17 with 1,O=#NI(on1rOln-F2s15l]=Oss37C15(S]s
replace s at position 24 with 1,O=#NI(on1rOln-F2s15l]=Os137C15(S]s
add c at position 13,O=#NI(on1rOlnc-F2s15l]=Os137C15(S]s
remove 7 from position 27,O=#NI(on1rOlnc-F2s15l]=Os13C15(S]s
remove r from position 9,O=#NI(on1Olnc-F2s15l]=Os13C15(S]s
remove N from position 3,O=#I(on1Olnc-F2s15l]=Os13C15(S]s
add + at position 20,O=#I(on1Olnc-F2s15l]+=Os13C15(S]s
remove l from position 9,O=#I(on1Onc-F2s15l]+=Os13C15(S]s
remove I from position 3,O=#(on1Onc-F2s15l]+=Os13C15(S]s
replace 5 at position 15 with =,O=#(on1Onc-F2s1=l]+=Os13C15(S]s
remove ] from position 29,O=#(on1Onc-F2s1=l]+=Os13C15(Ss
remove F from position 11,O=#(on1Onc-2s1=l]+=Os13C15(Ss
add n at position 9,O=#(on1Onnc-2s1=l]+=Os13C15(Ss
add o at position 30,O=#(on1Onnc-2s1=l]+=Os13C15(Sso
remove S from position 28,O=#(on1Onnc-2s1=l]+=Os13C15(so
replace o at position 29 with n,O=#(on1Onnc-2s1=l]+=Os13C15(sn
add c at position 26,O=#(on1Onnc-2s1=l]+=Os13C1c5(sn
add / at position 29,O=#(on1Onnc-2s1=l]+=Os13C1c5(/sn
remove 1 from position 25,O=#(on1Onnc-2s1=l]+=Os13Cc5(/sn
remove s from position 13,O=#(on1Onnc-21=l]+=Os13Cc5(/sn
remove n from position 8,O=#(on1Onc-21=l]+=Os13Cc5(/sn
add - at position 14,O=#(on1Onc-21=-l]+=Os13Cc5(/sn
add n at position 10,O=#(on1Oncn-21=-l]+=Os13Cc5(/sn
remove s from position 29,O=#(on1Oncn-21=-l]+=Os13Cc5(/n
remove l from position 16,O=#(on1Oncn-21=-]+=Os13Cc5(/n
remove c from position 9,O=#(on1Onn-21=-]+=Os13Cc5(/n
remove 1 from position 12,O=#(on1Onn-2=-]+=Os13Cc5(/n
replace ] at position 14 with =,O=#(on1Onn-2=-=+=Os13Cc5(/n
replace 5 at position 23 with C,O=#(on1Onn-2=-=+=Os13CcC(/n
replace o at position 4 with 3,O=#(3n1Onn-2=-=+=Os13CcC(/n
replace s at position 18 with C,O=#(3n1Onn-2=-=+=OC13CcC(/n
remove c from position 22,O=#(3n1Onn-2=-=+=OC13CC(/n
add ] at position 11,O=#(3n1Onn-]2=-=+=OC13CC(/n
replace 3 at position 21 with C,O=#(3n1Onn-]2=-=+=OC1CCC(/n
replace = at position 15 with 2,O=#(3n1Onn-]2=-2+=OC1CCC(/n
replace + at position 16 with c,O=#(3n1Onn-]2=-2c=OC1CCC(/n
replace = at position 13 with o,O=#(3n1Onn-]2o-2c=OC1CCC(/n
add N at position 3,O=#N(3n1Onn-]2o-2c=OC1CCC(/n
add c at position 14,O=#N(3n1Onn-]2co-2c=OC1CCC(/n
add o at position 24,O=#N(3n1Onn-]2co-2c=OC1CoCC(/n
replace / at position 28 with 1,O=#N(3n1Onn-]2co-2c=OC1CoCC(1n
replace o at position 15 with (,O=#N(3n1Onn-]2c(-2c=OC1CoCC(1n
replace ( at position 15 with s,O=#N(3n1Onn-]2cs-2c=OC1CoCC(1n
add ) at position 18,O=#N(3n1Onn-]2cs-2)c=OC1CoCC(1n
add O at position 22,O=#N(3n1Onn-]2cs-2)c=OOC1CoCC(1n
remove ( from position 29,O=#N(3n1Onn-]2cs-2)c=OOC1CoCC1n
remove n from position 30,O=#N(3n1Onn-]2cs-2)c=OOC1CoCC1
replace 3 at position 5 with C,O=#N(Cn1Onn-]2cs-2)c=OOC1CoCC1
remove O from position 8,O=#N(Cn1nn-]2cs-2)c=OOC1CoCC1
add c at position 14,O=#N(Cn1nn-]2ccs-2)c=OOC1CoCC1
remove - from position 16,O=#N(Cn1nn-]2ccs2)c=OOC1CoCC1
remove ] from position 11,O=#N(Cn1nn-2ccs2)c=OOC1CoCC1
add C at position 4,O=#NC(Cn1nn-2ccs2)c=OOC1CoCC1
add H at position 2,O=H#NC(Cn1nn-2ccs2)c=OOC1CoCC1
remove H from position 2,O=#NC(Cn1nn-2ccs2)c=OOC1CoCC1
add c at position 12,O=#NC(Cn1nn-c2ccs2)c=OOC1CoCC1
add ) at position 22,O=#NC(Cn1nn-c2ccs2)c=O)OC1CoCC1
replace # at position 2 with O,O=ONC(Cn1nn-c2ccs2)c=O)OC1CoCC1
add 6 at position 1,O6=ONC(Cn1nn-c2ccs2)c=O)OC1CoCC1
add ( at position 12,O6=ONC(Cn1nn(-c2ccs2)c=O)OC1CoCC1
add 1 at position 22,O6=ONC(Cn1nn(-c2ccs2)c1=O)OC1CoCC1
replace N at position 4 with S,O6=OSC(Cn1nn(-c2ccs2)c1=O)OC1CoCC1
add # at position 17,O6=OSC(Cn1nn(-c2c#cs2)c1=O)OC1CoCC1
remove C from position 5,O6=OS(Cn1nn(-c2c#cs2)c1=O)OC1CoCC1
add + at position 29,O6=OS(Cn1nn(-c2c#cs2)c1=O)OC1+CoCC1
replace S at position 4 with C,O6=OC(Cn1nn(-c2c#cs2)c1=O)OC1+CoCC1
remove o from position 31,O6=OC(Cn1nn(-c2c#cs2)c1=O)OC1+CCC1
remove n from position 7,O6=OC(C1nn(-c2c#cs2)c1=O)OC1+CCC1
add n at position 9,O6=OC(C1nnn(-c2c#cs2)c1=O)OC1+CCC1
add C at position 30,O6=OC(C1nnn(-c2c#cs2)c1=O)OC1+CCCC1
remove 6 from position 1,O=OC(C1nnn(-c2c#cs2)c1=O)OC1+CCCC1
replace # at position 15 with c,O=OC(C1nnn(-c2cccs2)c1=O)OC1+CCCC1
add n at position 6,O=OC(Cn1nnn(-c2cccs2)c1=O)OC1+CCCC1
remove O from position 2,O=C(Cn1nnn(-c2cccs2)c1=O)OC1+CCCC1
replace + at position 28 with C,O=C(Cn1nnn(-c2cccs2)c1=O)OC1CCCCC1
final: O=C(Cn1nnn(-c2cccs2)c1=O)OC1CCCCC1,O=C(Cn1nnn(-c2cccs2)c1=O)OC1CCCCC1
