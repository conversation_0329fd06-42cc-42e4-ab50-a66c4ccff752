log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove C from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add S at position 6,oH3H-]SN)
add [ at position 2,oH[3H-]SN)
add O at position 3,oH[O3H-]SN)
replace S at position 8 with c,oH[O3H-]cN)
add n at position 2,oHn[O3H-]cN)
add 4 at position 2,oH4n[O3H-]cN)
replace [ at position 4 with H,oH4nHO3H-]cN)
add ] at position 5,oH4nH]O3H-]cN)
add O at position 2,oHO4nH]O3H-]cN)
remove H from position 9,oHO4nH]O3-]cN)
add 6 at position 13,oHO4nH]O3-]cN6)
replace H at position 1 with 6,o6O4nH]O3-]cN6)
remove H from position 5,o6O4n]O3-]cN6)
replace 4 at position 3 with r,o6Orn]O3-]cN6)
add S at position 6,o6Orn]SO3-]cN6)
add s at position 11,o6Orn]SO3-]scN6)
remove 6 from position 14,o6Orn]SO3-]scN)
replace ] at position 10 with H,o6Orn]SO3-HscN)
remove O from position 2,o6rn]SO3-HscN)
add ) at position 11,o6rn]SO3-Hs)cN)
replace N at position 13 with /,o6rn]SO3-Hs)c/)
add S at position 6,o6rn]SSO3-Hs)c/)
add l at position 2,o6lrn]SSO3-Hs)c/)
add F at position 11,o6lrn]SSO3-FHs)c/)
replace o at position 0 with S,S6lrn]SSO3-FHs)c/)
remove S from position 7,S6lrn]SO3-FHs)c/)
add - at position 10,S6lrn]SO3--FHs)c/)
remove l from position 2,S6rn]SO3--FHs)c/)
add r at position 1,Sr6rn]SO3--FHs)c/)
add ) at position 12,Sr6rn]SO3--F)Hs)c/)
replace F at position 11 with 3,Sr6rn]SO3--3)Hs)c/)
add 1 at position 19,Sr6rn]SO3--3)Hs)c/)1
add 1 at position 2,Sr16rn]SO3--3)Hs)c/)1
add C at position 1,SCr16rn]SO3--3)Hs)c/)1
add C at position 21,SCr16rn]SO3--3)Hs)c/)C1
remove r from position 2,SC16rn]SO3--3)Hs)c/)C1
add B at position 14,SC16rn]SO3--3)BHs)c/)C1
add ) at position 19,SC16rn]SO3--3)BHs)c)/)C1
add o at position 17,SC16rn]SO3--3)BHso)c)/)C1
replace - at position 10 with 7,SC16rn]SO37-3)BHso)c)/)C1
add r at position 12,SC16rn]SO37-r3)BHso)c)/)C1
replace C at position 24 with r,SC16rn]SO37-r3)BHso)c)/)r1
replace / at position 22 with c,SC16rn]SO37-r3)BHso)c)c)r1
remove s from position 17,SC16rn]SO37-r3)BHo)c)c)r1
add c at position 19,SC16rn]SO37-r3)BHo)cc)c)r1
replace r at position 4 with @,SC16@n]SO37-r3)BHo)cc)c)r1
add / at position 21,SC16@n]SO37-r3)BHo)cc/)c)r1
remove B from position 15,SC16@n]SO37-r3)Ho)cc/)c)r1
add 2 at position 18,SC16@n]SO37-r3)Ho)2cc/)c)r1
replace S at position 0 with 7,7C16@n]SO37-r3)Ho)2cc/)c)r1
add ) at position 20,7C16@n]SO37-r3)Ho)2c)c/)c)r1
add l at position 11,7C16@n]SO37l-r3)Ho)2c)c/)c)r1
add ) at position 29,7C16@n]SO37l-r3)Ho)2c)c/)c)r1)
replace 3 at position 9 with =,7C16@n]SO=7l-r3)Ho)2c)c/)c)r1)
replace ) at position 29 with 7,7C16@n]SO=7l-r3)Ho)2c)c/)c)r17
add = at position 13,7C16@n]SO=7l-=r3)Ho)2c)c/)c)r17
add r at position 20,7C16@n]SO=7l-=r3)Ho)r2c)c/)c)r17
remove 1 from position 2,7C6@n]SO=7l-=r3)Ho)r2c)c/)c)r17
remove 7 from position 0,C6@n]SO=7l-=r3)Ho)r2c)c/)c)r17
replace 7 at position 8 with 3,C6@n]SO=3l-=r3)Ho)r2c)c/)c)r17
add ) at position 3,C6@)n]SO=3l-=r3)Ho)r2c)c/)c)r17
remove H from position 16,C6@)n]SO=3l-=r3)o)r2c)c/)c)r17
replace 3 at position 14 with C,C6@)n]SO=3l-=rC)o)r2c)c/)c)r17
add B at position 10,C6@)n]SO=3Bl-=rC)o)r2c)c/)c)r17
add 4 at position 13,C6@)n]SO=3Bl-4=rC)o)r2c)c/)c)r17
replace ) at position 17 with 3,C6@)n]SO=3Bl-4=rC3o)r2c)c/)c)r17
add ( at position 32,C6@)n]SO=3Bl-4=rC3o)r2c)c/)c)r17(
add 4 at position 18,C6@)n]SO=3Bl-4=rC34o)r2c)c/)c)r17(
add 7 at position 21,C6@)n]SO=3Bl-4=rC34o)7r2c)c/)c)r17(
replace 4 at position 18 with r,C6@)n]SO=3Bl-4=rC3ro)7r2c)c/)c)r17(
replace 7 at position 21 with 2,C6@)n]SO=3Bl-4=rC3ro)2r2c)c/)c)r17(
remove ) from position 30,C6@)n]SO=3Bl-4=rC3ro)2r2c)c/)cr17(
add 3 at position 6,C6@)n]3SO=3Bl-4=rC3ro)2r2c)c/)cr17(
add ) at position 29,C6@)n]3SO=3Bl-4=rC3ro)2r2c)c/))cr17(
add c at position 30,C6@)n]3SO=3Bl-4=rC3ro)2r2c)c/)c)cr17(
remove r from position 33,C6@)n]3SO=3Bl-4=rC3ro)2r2c)c/)c)c17(
add B at position 22,C6@)n]3SO=3Bl-4=rC3ro)B2r2c)c/)c)c17(
add F at position 23,C6@)n]3SO=3Bl-4=rC3ro)BF2r2c)c/)c)c17(
remove / from position 30,C6@)n]3SO=3Bl-4=rC3ro)BF2r2c)c)c)c17(
add B at position 30,C6@)n]3SO=3Bl-4=rC3ro)BF2r2c)cB)c)c17(
add ) at position 19,C6@)n]3SO=3Bl-4=rC3)ro)BF2r2c)cB)c)c17(
add n at position 4,C6@)nn]3SO=3Bl-4=rC3)ro)BF2r2c)cB)c)c17(
replace = at position 10 with 5,C6@)nn]3SO53Bl-4=rC3)ro)BF2r2c)cB)c)c17(
remove 2 from position 28,C6@)nn]3SO53Bl-4=rC3)ro)BF2rc)cB)c)c17(
remove 7 from position 37,C6@)nn]3SO53Bl-4=rC3)ro)BF2rc)cB)c)c1(
add C at position 17,C6@)nn]3SO53Bl-4=CrC3)ro)BF2rc)cB)c)c1(
add = at position 7,C6@)nn]=3SO53Bl-4=CrC3)ro)BF2rc)cB)c)c1(
add ) at position 36,C6@)nn]=3SO53Bl-4=CrC3)ro)BF2rc)cB)c))c1(
replace = at position 17 with F,C6@)nn]=3SO53Bl-4FCrC3)ro)BF2rc)cB)c))c1(
replace ) at position 34 with c,C6@)nn]=3SO53Bl-4FCrC3)ro)BF2rc)cBcc))c1(
add 2 at position 36,C6@)nn]=3SO53Bl-4FCrC3)ro)BF2rc)cBcc2))c1(
add C at position 19,C6@)nn]=3SO53Bl-4FCCrC3)ro)BF2rc)cBcc2))c1(
replace F at position 17 with n,C6@)nn]=3SO53Bl-4nCCrC3)ro)BF2rc)cBcc2))c1(
remove r from position 24,C6@)nn]=3SO53Bl-4nCCrC3)o)BF2rc)cBcc2))c1(
add F at position 20,C6@)nn]=3SO53Bl-4nCCFrC3)o)BF2rc)cBcc2))c1(
replace @ at position 2 with 2,C62)nn]=3SO53Bl-4nCCFrC3)o)BF2rc)cBcc2))c1(
add ) at position 9,C62)nn]=3)SO53Bl-4nCCFrC3)o)BF2rc)cBcc2))c1(
remove ( from position 43,C62)nn]=3)SO53Bl-4nCCFrC3)o)BF2rc)cBcc2))c1
add r at position 10,C62)nn]=3)rSO53Bl-4nCCFrC3)o)BF2rc)cBcc2))c1
remove ) from position 34,C62)nn]=3)rSO53Bl-4nCCFrC3)o)BF2rccBcc2))c1
remove r from position 10,C62)nn]=3)SO53Bl-4nCCFrC3)o)BF2rccBcc2))c1
add ( at position 8,C62)nn]=(3)SO53Bl-4nCCFrC3)o)BF2rccBcc2))c1
remove B from position 35,C62)nn]=(3)SO53Bl-4nCCFrC3)o)BF2rcccc2))c1
remove F from position 22,C62)nn]=(3)SO53Bl-4nCCrC3)o)BF2rcccc2))c1
replace 5 at position 13 with 3,C62)nn]=(3)SO33Bl-4nCCrC3)o)BF2rcccc2))c1
add - at position 11,C62)nn]=(3)-SO33Bl-4nCCrC3)o)BF2rcccc2))c1
replace r at position 32 with (,C62)nn]=(3)-SO33Bl-4nCCrC3)o)BF2(cccc2))c1
remove r from position 23,C62)nn]=(3)-SO33Bl-4nCCC3)o)BF2(cccc2))c1
replace F at position 29 with /,C62)nn]=(3)-SO33Bl-4nCCC3)o)B/2(cccc2))c1
remove S from position 12,C62)nn]=(3)-O33Bl-4nCCC3)o)B/2(cccc2))c1
add H at position 12,C62)nn]=(3)-HO33Bl-4nCCC3)o)B/2(cccc2))c1
add I at position 4,C62)Inn]=(3)-HO33Bl-4nCCC3)o)B/2(cccc2))c1
replace I at position 4 with (,C62)(nn]=(3)-HO33Bl-4nCCC3)o)B/2(cccc2))c1
replace 4 at position 20 with ],C62)(nn]=(3)-HO33Bl-]nCCC3)o)B/2(cccc2))c1
add S at position 5,C62)(Snn]=(3)-HO33Bl-]nCCC3)o)B/2(cccc2))c1
replace 2 at position 2 with I,C6I)(Snn]=(3)-HO33Bl-]nCCC3)o)B/2(cccc2))c1
add c at position 3,C6Ic)(Snn]=(3)-HO33Bl-]nCCC3)o)B/2(cccc2))c1
replace ) at position 4 with c,C6Icc(Snn]=(3)-HO33Bl-]nCCC3)o)B/2(cccc2))c1
replace l at position 20 with C,C6Icc(Snn]=(3)-HO33BC-]nCCC3)o)B/2(cccc2))c1
add 2 at position 17,C6Icc(Snn]=(3)-HO233BC-]nCCC3)o)B/2(cccc2))c1
add @ at position 10,C6Icc(Snn]@=(3)-HO233BC-]nCCC3)o)B/2(cccc2))c1
remove S from position 6,C6Icc(nn]@=(3)-HO233BC-]nCCC3)o)B/2(cccc2))c1
replace O at position 16 with +,C6Icc(nn]@=(3)-H+233BC-]nCCC3)o)B/2(cccc2))c1
replace n at position 24 with 2,C6Icc(nn]@=(3)-H+233BC-]2CCC3)o)B/2(cccc2))c1
replace n at position 6 with -,C6Icc(-n]@=(3)-H+233BC-]2CCC3)o)B/2(cccc2))c1
add 2 at position 7,C6Icc(-2n]@=(3)-H+233BC-]2CCC3)o)B/2(cccc2))c1
remove = from position 11,C6Icc(-2n]@(3)-H+233BC-]2CCC3)o)B/2(cccc2))c1
replace 3 at position 18 with O,C6Icc(-2n]@(3)-H+2O3BC-]2CCC3)o)B/2(cccc2))c1
add - at position 33,C6Icc(-2n]@(3)-H+2O3BC-]2CCC3)o)B-/2(cccc2))c1
replace @ at position 10 with n,C6Icc(-2n]n(3)-H+2O3BC-]2CCC3)o)B-/2(cccc2))c1
replace o at position 30 with (,C6Icc(-2n]n(3)-H+2O3BC-]2CCC3)()B-/2(cccc2))c1
replace ( at position 30 with S,C6Icc(-2n]n(3)-H+2O3BC-]2CCC3)S)B-/2(cccc2))c1
add c at position 37,C6Icc(-2n]n(3)-H+2O3BC-]2CCC3)S)B-/2(ccccc2))c1
add C at position 22,C6Icc(-2n]n(3)-H+2O3BCC-]2CCC3)S)B-/2(ccccc2))c1
replace I at position 2 with 1,C61cc(-2n]n(3)-H+2O3BCC-]2CCC3)S)B-/2(ccccc2))c1
remove ] from position 9,C61cc(-2nn(3)-H+2O3BCC-]2CCC3)S)B-/2(ccccc2))c1
replace 3 at position 11 with [,C61cc(-2nn([)-H+2O3BCC-]2CCC3)S)B-/2(ccccc2))c1
remove O from position 17,C61cc(-2nn([)-H+23BCC-]2CCC3)S)B-/2(ccccc2))c1
add + at position 29,C61cc(-2nn([)-H+23BCC-]2CCC3)+S)B-/2(ccccc2))c1
remove - from position 33,C61cc(-2nn([)-H+23BCC-]2CCC3)+S)B/2(ccccc2))c1
remove ] from position 22,C61cc(-2nn([)-H+23BCC-2CCC3)+S)B/2(ccccc2))c1
remove - from position 13,C61cc(-2nn([)H+23BCC-2CCC3)+S)B/2(ccccc2))c1
remove / from position 31,C61cc(-2nn([)H+23BCC-2CCC3)+S)B2(ccccc2))c1
add c at position 3,C61ccc(-2nn([)H+23BCC-2CCC3)+S)B2(ccccc2))c1
add + at position 42,C61ccc(-2nn([)H+23BCC-2CCC3)+S)B2(ccccc2))+c1
remove - from position 21,C61ccc(-2nn([)H+23BCC2CCC3)+S)B2(ccccc2))+c1
add - at position 32,C61ccc(-2nn([)H+23BCC2CCC3)+S)B2-(ccccc2))+c1
add c at position 33,C61ccc(-2nn([)H+23BCC2CCC3)+S)B2-c(ccccc2))+c1
add C at position 12,C61ccc(-2nn(C[)H+23BCC2CCC3)+S)B2-c(ccccc2))+c1
replace 2 at position 17 with ],C61ccc(-2nn(C[)H+]3BCC2CCC3)+S)B2-c(ccccc2))+c1
remove B from position 19,C61ccc(-2nn(C[)H+]3CC2CCC3)+S)B2-c(ccccc2))+c1
add 2 at position 35,C61ccc(-2nn(C[)H+]3CC2CCC3)+S)B2-c(2ccccc2))+c1
add c at position 2,C6c1ccc(-2nn(C[)H+]3CC2CCC3)+S)B2-c(2ccccc2))+c1
remove ) from position 43,C6c1ccc(-2nn(C[)H+]3CC2CCC3)+S)B2-c(2ccccc2)+c1
add = at position 29,C6c1ccc(-2nn(C[)H+]3CC2CCC3)+=S)B2-c(2ccccc2)+c1
replace ) at position 15 with N,C6c1ccc(-2nn(C[NH+]3CC2CCC3)+=S)B2-c(2ccccc2)+c1
replace B at position 32 with n,C6c1ccc(-2nn(C[NH+]3CC2CCC3)+=S)n2-c(2ccccc2)+c1
remove + from position 45,C6c1ccc(-2nn(C[NH+]3CC2CCC3)+=S)n2-c(2ccccc2)c1
add c at position 9,C6c1ccc(-c2nn(C[NH+]3CC2CCC3)+=S)n2-c(2ccccc2)c1
add ( at position 30,C6c1ccc(-c2nn(C[NH+]3CC2CCC3)+(=S)n2-c(2ccccc2)c1
remove 6 from position 1,Cc1ccc(-c2nn(C[NH+]3CC2CCC3)+(=S)n2-c(2ccccc2)c1
remove 2 from position 22,Cc1ccc(-c2nn(C[NH+]3CCCCC3)+(=S)n2-c(2ccccc2)c1
add c at position 5,Cc1cccc(-c2nn(C[NH+]3CCCCC3)+(=S)n2-c(2ccccc2)c1
remove ( from position 37,Cc1cccc(-c2nn(C[NH+]3CCCCC3)+(=S)n2-c2ccccc2)c1
replace + at position 28 with c,Cc1cccc(-c2nn(C[NH+]3CCCCC3)c(=S)n2-c2ccccc2)c1
final: Cc1cccc(-c2nn(C[NH+]3CCCCC3)c(=S)n2-c2ccccc2)c1,Cc1cccc(-c2nn(C[NH+]3CCCCC3)c(=S)n2-c2ccccc2)c1
