log,state
initialize: CN(CC[NH+](C)C)C(=O)C[C@H]1COCCN1C(=O)c1ccc2[nH]nnc2c1,CN(CC[NH+](C)C)C(=O)C[C@H]1COCCN1C(=O)c1ccc2[nH]nnc2c1
replace O at position 28 with +,CN(CC[NH+](C)C)C(=O)C[C@H]1C+CCN1C(=O)c1ccc2[nH]nnc2c1
add H at position 51,CN(CC[NH+](C)C)C(=O)C[C@H]1C+CCN1C(=O)c1ccc2[nH]nncH2c1
add ) at position 44,CN(CC[NH+](C)C)C(=O)C[C@H]1C+CCN1C(=O)c1ccc2)[nH]nncH2c1
add 2 at position 22,CN(CC[NH+](C)C)C(=O)C[2C@H]1C+CCN1C(=O)c1ccc2)[nH]nncH2c1
add # at position 50,CN(CC[NH+](C)C)C(=O)C[2C@H]1C+CCN1C(=O)c1ccc2)[nH]#nncH2c1
remove c from position 39,CN(CC[NH+](C)C)C(=O)C[2C@H]1C+CCN1C(=O)1ccc2)[nH]#nncH2c1
remove + from position 29,CN(CC[NH+](C)C)C(=O)C[2C@H]1CCCN1C(=O)1ccc2)[nH]#nncH2c1
add S at position 11,CN(CC[NH+](SC)C)C(=O)C[2C@H]1CCCN1C(=O)1ccc2)[nH]#nncH2c1
add C at position 0,CCN(CC[NH+](SC)C)C(=O)C[2C@H]1CCCN1C(=O)1ccc2)[nH]#nncH2c1
remove C from position 15,CCN(CC[NH+](SC))C(=O)C[2C@H]1CCCN1C(=O)1ccc2)[nH]#nncH2c1
add r at position 34,CCN(CC[NH+](SC))C(=O)C[2C@H]1CCCN1rC(=O)1ccc2)[nH]#nncH2c1
remove O from position 38,CCN(CC[NH+](SC))C(=O)C[2C@H]1CCCN1rC(=)1ccc2)[nH]#nncH2c1
remove N from position 32,CCN(CC[NH+](SC))C(=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c1
remove ( from position 17,CCN(CC[NH+](SC))C=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c1
replace C at position 4 with O,CCN(OC[NH+](SC))C=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c1
remove 1 from position 54,CCN(OC[NH+](SC))C=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c
remove ) from position 15,CCN(OC[NH+](SC)C=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c
remove S from position 12,CCN(OC[NH+](C)C=O)C[2C@H]1CCC1rC(=)1ccc2)[nH]#nncH2c
remove = from position 33,CCN(OC[NH+](C)C=O)C[2C@H]1CCC1rC()1ccc2)[nH]#nncH2c
remove ( from position 32,CCN(OC[NH+](C)C=O)C[2C@H]1CCC1rC)1ccc2)[nH]#nncH2c
add - at position 21,CCN(OC[NH+](C)C=O)C[2-C@H]1CCC1rC)1ccc2)[nH]#nncH2c
remove H from position 42,CCN(OC[NH+](C)C=O)C[2-C@H]1CCC1rC)1ccc2)[n]#nncH2c
remove ( from position 3,CCNOC[NH+](C)C=O)C[2-C@H]1CCC1rC)1ccc2)[n]#nncH2c
add / at position 31,CCNOC[NH+](C)C=O)C[2-C@H]1CCC1r/C)1ccc2)[n]#nncH2c
add - at position 13,CCNOC[NH+](C)-C=O)C[2-C@H]1CCC1r/C)1ccc2)[n]#nncH2c
add ] at position 22,CCNOC[NH+](C)-C=O)C[2-]C@H]1CCC1r/C)1ccc2)[n]#nncH2c
add - at position 33,CCNOC[NH+](C)-C=O)C[2-]C@H]1CCC1r-/C)1ccc2)[n]#nncH2c
remove C from position 29,CCNOC[NH+](C)-C=O)C[2-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
add O at position 17,CCNOC[NH+](C)-C=OO)C[2-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
replace C at position 11 with 3,CCNOC[NH+](3)-C=OO)C[2-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
add ] at position 9,CCNOC[NH+]](3)-C=OO)C[2-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
replace N at position 2 with H,CCHOC[NH+]](3)-C=OO)C[2-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
remove 2 from position 22,CCHOC[NH+]](3)-C=OO)C[-]C@H]1CC1r-/C)1ccc2)[n]#nncH2c
remove 1 from position 37,CCHOC[NH+]](3)-C=OO)C[-]C@H]1CC1r-/C)ccc2)[n]#nncH2c
replace C at position 30 with (,CCHOC[NH+]](3)-C=OO)C[-]C@H]1C(1r-/C)ccc2)[n]#nncH2c
replace ( at position 30 with o,CCHOC[NH+]](3)-C=OO)C[-]C@H]1Co1r-/C)ccc2)[n]#nncH2c
replace ] at position 10 with @,CCHOC[NH+]@(3)-C=OO)C[-]C@H]1Co1r-/C)ccc2)[n]#nncH2c
remove - from position 33,CCHOC[NH+]@(3)-C=OO)C[-]C@H]1Co1r/C)ccc2)[n]#nncH2c
replace O at position 18 with 3,CCHOC[NH+]@(3)-C=O3)C[-]C@H]1Co1r/C)ccc2)[n]#nncH2c
add = at position 11,CCHOC[NH+]@=(3)-C=O3)C[-]C@H]1Co1r/C)ccc2)[n]#nncH2c
remove H from position 7,CCHOC[N+]@=(3)-C=O3)C[-]C@H]1Co1r/C)ccc2)[n]#nncH2c
replace N at position 6 with n,CCHOC[n+]@=(3)-C=O3)C[-]C@H]1Co1r/C)ccc2)[n]#nncH2c
replace C at position 24 with n,CCHOC[n+]@=(3)-C=O3)C[-]n@H]1Co1r/C)ccc2)[n]#nncH2c
replace = at position 16 with O,CCHOC[n+]@=(3)-COO3)C[-]n@H]1Co1r/C)ccc2)[n]#nncH2c
add S at position 6,CCHOC[Sn+]@=(3)-COO3)C[-]n@H]1Co1r/C)ccc2)[n]#nncH2c
remove @ from position 10,CCHOC[Sn+]=(3)-COO3)C[-]n@H]1Co1r/C)ccc2)[n]#nncH2c
remove O from position 17,CCHOC[Sn+]=(3)-CO3)C[-]n@H]1Co1r/C)ccc2)[n]#nncH2c
replace c at position 46 with 5,CCHOC[Sn+]=(3)-CO3)C[-]n@H]1Co1r/C)ccc2)[n]#nn5H2c
replace C at position 28 with ],CCHOC[Sn+]=(3)-CO3)C[-]n@H]1]o1r/C)ccc2)[n]#nn5H2c
add 1 at position 25,CCHOC[Sn+]=(3)-CO3)C[-]n@1H]1]o1r/C)ccc2)[n]#nn5H2c
add c at position 18,CCHOC[Sn+]=(3)-CO3c)C[-]n@1H]1]o1r/C)ccc2)[n]#nn5H2c
add l at position 32,CCHOC[Sn+]=(3)-CO3c)C[-]n@1H]1]ol1r/C)ccc2)[n]#nn5H2c
add 5 at position 44,CCHOC[Sn+]=(3)-CO3c)C[-]n@1H]1]ol1r/C)ccc2)[5n]#nn5H2c
remove 1 from position 29,CCHOC[Sn+]=(3)-CO3c)C[-]n@1H]]ol1r/C)ccc2)[5n]#nn5H2c
add n at position 16,CCHOC[Sn+]=(3)-CnO3c)C[-]n@1H]]ol1r/C)ccc2)[5n]#nn5H2c
add s at position 27,CCHOC[Sn+]=(3)-CnO3c)C[-]n@s1H]]ol1r/C)ccc2)[5n]#nn5H2c
add 1 at position 25,CCHOC[Sn+]=(3)-CnO3c)C[-]1n@s1H]]ol1r/C)ccc2)[5n]#nn5H2c
remove 1 from position 29,CCHOC[Sn+]=(3)-CnO3c)C[-]1n@sH]]ol1r/C)ccc2)[5n]#nn5H2c
remove H from position 52,CCHOC[Sn+]=(3)-CnO3c)C[-]1n@sH]]ol1r/C)ccc2)[5n]#nn52c
replace ) at position 13 with F,CCHOC[Sn+]=(3F-CnO3c)C[-]1n@sH]]ol1r/C)ccc2)[5n]#nn52c
replace n at position 26 with /,CCHOC[Sn+]=(3F-CnO3c)C[-]1/@sH]]ol1r/C)ccc2)[5n]#nn52c
replace F at position 13 with 4,CCHOC[Sn+]=(34-CnO3c)C[-]1/@sH]]ol1r/C)ccc2)[5n]#nn52c
add F at position 22,CCHOC[Sn+]=(34-CnO3c)CF[-]1/@sH]]ol1r/C)ccc2)[5n]#nn52c
add s at position 49,CCHOC[Sn+]=(34-CnO3c)CF[-]1/@sH]]ol1r/C)ccc2)[5n]s#nn52c
remove C from position 21,CCHOC[Sn+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2)[5n]s#nn52c
add I at position 3,CCHIOC[Sn+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2)[5n]s#nn52c
add l at position 9,CCHIOC[Snl+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2)[5n]s#nn52c
remove ) from position 45,CCHIOC[Snl+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]s#nn52c
remove C from position 0,CHIOC[Snl+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]s#nn52c
remove O from position 3,CHIC[Snl+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]s#nn52c
remove # from position 48,CHIC[Snl+]=(34-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]snn52c
replace 4 at position 13 with (,CHIC[Snl+]=(3(-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]snn52c
remove s from position 47,CHIC[Snl+]=(3(-CnO3c)F[-]1/@sH]]ol1r/C)ccc2[5n]nn52c
remove [ from position 43,CHIC[Snl+]=(3(-CnO3c)F[-]1/@sH]]ol1r/C)ccc25n]nn52c
remove O from position 17,CHIC[Snl+]=(3(-Cn3c)F[-]1/@sH]]ol1r/C)ccc25n]nn52c
remove ) from position 19,CHIC[Snl+]=(3(-Cn3cF[-]1/@sH]]ol1r/C)ccc25n]nn52c
remove ] from position 43,CHIC[Snl+]=(3(-Cn3cF[-]1/@sH]]ol1r/C)ccc25nnn52c
remove ] from position 9,CHIC[Snl+=(3(-Cn3cF[-]1/@sH]]ol1r/C)ccc25nnn52c
replace S at position 5 with c,CHIC[cnl+=(3(-Cn3cF[-]1/@sH]]ol1r/C)ccc25nnn52c
remove H from position 26,CHIC[cnl+=(3(-Cn3cF[-]1/@s]]ol1r/C)ccc25nnn52c
remove c from position 36,CHIC[cnl+=(3(-Cn3cF[-]1/@s]]ol1r/C)cc25nnn52c
remove l from position 7,CHIC[cn+=(3(-Cn3cF[-]1/@s]]ol1r/C)cc25nnn52c
remove F from position 17,CHIC[cn+=(3(-Cn3c[-]1/@s]]ol1r/C)cc25nnn52c
add 7 at position 37,CHIC[cn+=(3(-Cn3c[-]1/@s]]ol1r/C)cc257nnn52c
add 2 at position 28,CHIC[cn+=(3(-Cn3c[-]1/@s]]ol21r/C)cc257nnn52c
replace 3 at position 10 with =,CHIC[cn+=(=(-Cn3c[-]1/@s]]ol21r/C)cc257nnn52c
remove [ from position 4,CHICcn+=(=(-Cn3c[-]1/@s]]ol21r/C)cc257nnn52c
remove 1 from position 19,CHICcn+=(=(-Cn3c[-]/@s]]ol21r/C)cc257nnn52c
remove C from position 30,CHICcn+=(=(-Cn3c[-]/@s]]ol21r/)cc257nnn52c
add B at position 40,CHICcn+=(=(-Cn3c[-]/@s]]ol21r/)cc257nnn5B2c
add 6 at position 37,CHICcn+=(=(-Cn3c[-]/@s]]ol21r/)cc257n6nn5B2c
remove ] from position 22,CHICcn+=(=(-Cn3c[-]/@s]ol21r/)cc257n6nn5B2c
add r at position 33,CHICcn+=(=(-Cn3c[-]/@s]ol21r/)cc2r57n6nn5B2c
remove 2 from position 42,CHICcn+=(=(-Cn3c[-]/@s]ol21r/)cc2r57n6nn5Bc
remove H from position 1,CICcn+=(=(-Cn3c[-]/@s]ol21r/)cc2r57n6nn5Bc
replace c at position 29 with O,CICcn+=(=(-Cn3c[-]/@s]ol21r/)Oc2r57n6nn5Bc
replace I at position 1 with 6,C6Ccn+=(=(-Cn3c[-]/@s]ol21r/)Oc2r57n6nn5Bc
replace c at position 30 with ),C6Ccn+=(=(-Cn3c[-]/@s]ol21r/)O)2r57n6nn5Bc
replace n at position 37 with 5,C6Ccn+=(=(-Cn3c[-]/@s]ol21r/)O)2r57n65n5Bc
replace / at position 18 with 5,C6Ccn+=(=(-Cn3c[-]5@s]ol21r/)O)2r57n65n5Bc
remove 5 from position 37,C6Ccn+=(=(-Cn3c[-]5@s]ol21r/)O)2r57n6n5Bc
remove 5 from position 18,C6Ccn+=(=(-Cn3c[-]@s]ol21r/)O)2r57n6n5Bc
remove 5 from position 32,C6Ccn+=(=(-Cn3c[-]@s]ol21r/)O)2r7n6n5Bc
replace ] at position 17 with ),C6Ccn+=(=(-Cn3c[-)@s]ol21r/)O)2r7n6n5Bc
remove c from position 38,C6Ccn+=(=(-Cn3c[-)@s]ol21r/)O)2r7n6n5B
add 5 at position 32,C6Ccn+=(=(-Cn3c[-)@s]ol21r/)O)2r57n6n5B
replace ) at position 29 with 4,C6Ccn+=(=(-Cn3c[-)@s]ol21r/)O42r57n6n5B
add H at position 33,C6Ccn+=(=(-Cn3c[-)@s]ol21r/)O42r5H7n6n5B
remove ( from position 7,C6Ccn+==(-Cn3c[-)@s]ol21r/)O42r5H7n6n5B
replace ) at position 16 with 7,C6Ccn+==(-Cn3c[-7@s]ol21r/)O42r5H7n6n5B
add 7 at position 1,C76Ccn+==(-Cn3c[-7@s]ol21r/)O42r5H7n6n5B
add 1 at position 2,C716Ccn+==(-Cn3c[-7@s]ol21r/)O42r5H7n6n5B
remove s from position 20,C716Ccn+==(-Cn3c[-7@]ol21r/)O42r5H7n6n5B
remove ) from position 27,C716Ccn+==(-Cn3c[-7@]ol21r/O42r5H7n6n5B
replace C at position 4 with o,C716ocn+==(-Cn3c[-7@]ol21r/O42r5H7n6n5B
replace @ at position 19 with 3,C716ocn+==(-Cn3c[-73]ol21r/O42r5H7n6n5B
remove 5 from position 37,C716ocn+==(-Cn3c[-73]ol21r/O42r5H7n6nB
add ) at position 32,C716ocn+==(-Cn3c[-73]ol21r/O42r5)H7n6nB
remove 7 from position 1,C16ocn+==(-Cn3c[-73]ol21r/O42r5)H7n6nB
remove l from position 21,C16ocn+==(-Cn3c[-73]o21r/O42r5)H7n6nB
replace c at position 14 with /,C16ocn+==(-Cn3/[-73]o21r/O42r5)H7n6nB
remove = from position 8,C16ocn+=(-Cn3/[-73]o21r/O42r5)H7n6nB
replace c at position 4 with -,C16o-n+=(-Cn3/[-73]o21r/O42r5)H7n6nB
remove - from position 15,C16o-n+=(-Cn3/[73]o21r/O42r5)H7n6nB
remove 6 from position 32,C16o-n+=(-Cn3/[73]o21r/O42r5)H7nnB
replace 2 at position 25 with o,C16o-n+=(-Cn3/[73]o21r/O4or5)H7nnB
replace ( at position 8 with [,C16o-n+=[-Cn3/[73]o21r/O4or5)H7nnB
replace ) at position 28 with (,C16o-n+=[-Cn3/[73]o21r/O4or5(H7nnB
remove O from position 23,C16o-n+=[-Cn3/[73]o21r/4or5(H7nnB
replace 5 at position 26 with r,C16o-n+=[-Cn3/[73]o21r/4orr(H7nnB
add 5 at position 4,C16o5-n+=[-Cn3/[73]o21r/4orr(H7nnB
add 6 at position 12,C16o5-n+=[-C6n3/[73]o21r/4orr(H7nnB
replace n at position 33 with S,C16o5-n+=[-C6n3/[73]o21r/4orr(H7nSB
replace 7 at position 31 with (,C16o5-n+=[-C6n3/[73]o21r/4orr(H(nSB
add l at position 5,C16o5l-n+=[-C6n3/[73]o21r/4orr(H(nSB
remove o from position 21,C16o5l-n+=[-C6n3/[73]21r/4orr(H(nSB
add S at position 15,C16o5l-n+=[-C6nS3/[73]21r/4orr(H(nSB
replace 1 at position 1 with o,Co6o5l-n+=[-C6nS3/[73]21r/4orr(H(nSB
remove 1 from position 23,Co6o5l-n+=[-C6nS3/[73]2r/4orr(H(nSB
remove 5 from position 4,Co6ol-n+=[-C6nS3/[73]2r/4orr(H(nSB
remove n from position 13,Co6ol-n+=[-C6S3/[73]2r/4orr(H(nSB
replace [ at position 9 with C,Co6ol-n+=C-C6S3/[73]2r/4orr(H(nSB
replace [ at position 16 with N,Co6ol-n+=C-C6S3/N73]2r/4orr(H(nSB
remove ( from position 29,Co6ol-n+=C-C6S3/N73]2r/4orr(HnSB
remove 4 from position 23,Co6ol-n+=C-C6S3/N73]2r/orr(HnSB
remove S from position 13,Co6ol-n+=C-C63/N73]2r/orr(HnSB
replace + at position 7 with 5,Co6ol-n5=C-C63/N73]2r/orr(HnSB
add H at position 10,Co6ol-n5=CH-C63/N73]2r/orr(HnSB
replace o at position 3 with F,Co6Fl-n5=CH-C63/N73]2r/orr(HnSB
remove H from position 27,Co6Fl-n5=CH-C63/N73]2r/orr(nSB
add H at position 18,Co6Fl-n5=CH-C63/N7H3]2r/orr(nSB
remove - from position 5,Co6Fln5=CH-C63/N7H3]2r/orr(nSB
remove C from position 11,Co6Fln5=CH-63/N7H3]2r/orr(nSB
replace C at position 8 with [,Co6Fln5=[H-63/N7H3]2r/orr(nSB
remove n from position 5,Co6Fl5=[H-63/N7H3]2r/orr(nSB
remove 5 from position 5,Co6Fl=[H-63/N7H3]2r/orr(nSB
replace 2 at position 17 with [,Co6Fl=[H-63/N7H3][r/orr(nSB
remove [ from position 6,Co6Fl=H-63/N7H3][r/orr(nSB
remove n from position 23,Co6Fl=H-63/N7H3][r/orr(SB
add H at position 23,Co6Fl=H-63/N7H3][r/orr(HSB
replace H at position 13 with C,Co6Fl=H-63/N7C3][r/orr(HSB
remove o from position 19,Co6Fl=H-63/N7C3][r/rr(HSB
remove ] from position 15,Co6Fl=H-63/N7C3[r/rr(HSB
remove B from position 23,Co6Fl=H-63/N7C3[r/rr(HS
remove N from position 11,Co6Fl=H-63/7C3[r/rr(HS
remove S from position 21,Co6Fl=H-63/7C3[r/rr(H
remove C from position 12,Co6Fl=H-63/73[r/rr(H
replace l at position 4 with +,Co6F+=H-63/73[r/rr(H
replace / at position 15 with +,Co6F+=H-63/73[r+rr(H
remove + from position 4,Co6F=H-63/73[r+rr(H
replace 3 at position 8 with 2,Co6F=H-62/73[r+rr(H
replace 6 at position 2 with 5,Co5F=H-62/73[r+rr(H
remove / from position 9,Co5F=H-6273[r+rr(H
remove [ from position 11,Co5F=H-6273r+rr(H
remove - from position 6,Co5F=H6273r+rr(H
remove ( from position 14,Co5F=H6273r+rrH
remove 2 from position 7,Co5F=H673r+rrH
replace o at position 1 with F,CF5F=H673r+rrH
replace 7 at position 7 with ),CF5F=H6)3r+rrH
add I at position 2,CFI5F=H6)3r+rrH
replace I at position 2 with 7,CF75F=H6)3r+rrH
remove H from position 14,CF75F=H6)3r+rr
add / at position 0,/CF75F=H6)3r+rr
replace F at position 5 with N,/CF75N=H6)3r+rr
add c at position 1,/cCF75N=H6)3r+rr
remove + from position 13,/cCF75N=H6)3rrr
remove 7 from position 4,/cCF5N=H6)3rrr
replace = at position 6 with [,/cCF5N[H6)3rrr
remove 3 from position 10,/cCF5N[H6)rrr
add # at position 6,/cCF5N#[H6)rrr
remove 6 from position 9,/cCF5N#[H)rrr
replace r at position 11 with 5,/cCF5N#[H)r5r
replace ) at position 9 with 3,/cCF5N#[H3r5r
add 1 at position 13,/cCF5N#[H3r5r1
remove 3 from position 9,/cCF5N#[Hr5r1
remove H from position 8,/cCF5N#[r5r1
replace r at position 8 with 3,/cCF5N#[35r1
replace # at position 6 with =,/cCF5N=[35r1
remove 5 from position 9,/cCF5N=[3r1
remove F from position 3,/cC5N=[3r1
replace C at position 2 with /,/c/5N=[3r1
replace / at position 2 with (,/c(5N=[3r1
add r at position 8,/c(5N=[3rr1
replace 1 at position 10 with H,/c(5N=[3rrH
add # at position 7,/c(5N=[#3rrH
remove c from position 1,/(5N=[#3rrH
remove ( from position 1,/5N=[#3rrH
add l at position 3,/5Nl=[#3rrH
remove 3 from position 7,/5Nl=[#rrH
remove l from position 3,/5N=[#rrH
remove H from position 8,/5N=[#rr
remove r from position 7,/5N=[#r
replace # at position 5 with +,/5N=[+r
remove r from position 6,/5N=[+
add C at position 6,/5N=[+C
add 7 at position 2,/57N=[+C
remove 7 from position 2,/5N=[+C
replace / at position 0 with C,C5N=[+C
remove N from position 2,C5=[+C
remove = from position 2,C5[+C
replace + at position 3 with o,C5[oC
remove [ from position 2,C5oC
replace 5 at position 1 with ),C)oC
replace ) at position 1 with B,CBoC
add 3 at position 0,3CBoC
replace C at position 4 with 3,3CBo3
add 1 at position 3,3CB1o3
add - at position 4,3CB1-o3
replace B at position 2 with ],3C]1-o3
remove 1 from position 3,3C]-o3
remove o from position 4,3C]-3
replace C at position 1 with O,3O]-3
add # at position 4,3O]-#3
add l at position 2,3Ol]-#3
remove # from position 5,3Ol]-3
replace ] at position 3 with =,3Ol=-3
remove l from position 2,3O=-3
replace 3 at position 4 with F,3O=-F
add ] at position 4,3O=-]F
remove F from position 5,3O=-]
remove = from position 2,3O-]
remove 3 from position 0,O-]
add r at position 0,rO-]
replace O at position 1 with 7,r7-]
remove r from position 0,7-]
remove 7 from position 0,-]
replace - at position 0 with o,o]
add r at position 0,ro]
replace o at position 1 with c,rc]
add c at position 1,rcc]
add ( at position 3,rcc(]
add C at position 5,rcc(]C
add ( at position 4,rcc((]C
replace ] at position 5 with C,rcc((CC
add = at position 0,=rcc((CC
replace = at position 0 with [,[rcc((CC
replace c at position 2 with 2,[r2c((CC
replace C at position 7 with I,[r2c((CI
add 2 at position 4,[r2c2((CI
remove 2 from position 2,[rc2((CI
remove C from position 6,[rc2((I
remove c from position 2,[r2((I
remove [ from position 0,r2((I
replace ( at position 2 with O,r2O(I
remove 2 from position 1,rO(I
remove ( from position 2,rOI
remove I from position 2,rO
remove r from position 0,O
remove O from position 0,
final: ,
