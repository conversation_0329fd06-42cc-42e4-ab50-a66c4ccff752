log,state
initialize: CN(C)C(=O)CCCNC(=O)c1ccnc(OC(C)(C)C)c1,CN(C)C(=O)CCCNC(=O)c1ccnc(OC(C)(C)C)c1
replace ( at position 28 with -,CN(C)C(=O)CCCNC(=O)c1ccnc(OC-C)(C)C)c1
add ( at position 37,CN(C)C(=O)CCCNC(=O)c1ccnc(OC-C)(C)C)c(1
remove C from position 5,CN(C)(=O)CCCNC(=O)c1ccnc(OC-C)(C)C)c(1
add 2 at position 22,CN(C)(=O)CCCNC(=O)c1cc2nc(OC-C)(C)C)c(1
add 6 at position 1,C6N(C)(=O)CCCNC(=O)c1cc2nc(OC-C)(C)C)c(1
remove C from position 30,C6N(C)(=O)CCCNC(=O)c1cc2nc(OC-)(C)C)c(1
remove ) from position 9,C6N(C)(=OCCCNC(=O)c1cc2nc(OC-)(C)C)c(1
add n at position 7,C6N(C)(n=OCCCNC(=O)c1cc2nc(OC-)(C)C)c(1
add o at position 31,C6N(C)(n=OCCCNC(=O)c1cc2nc(OC-)o(C)C)c(1
replace C at position 4 with S,C6N(S)(n=OCCCNC(=O)c1cc2nc(OC-)o(C)C)c(1
remove - from position 29,C6N(S)(n=OCCCNC(=O)c1cc2nc(OC)o(C)C)c(1
add C at position 5,C6N(SC)(n=OCCCNC(=O)c1cc2nc(OC)o(C)C)c(1
remove = from position 17,C6N(SC)(n=OCCCNC(O)c1cc2nc(OC)o(C)C)c(1
replace S at position 4 with N,C6N(NC)(n=OCCCNC(O)c1cc2nc(OC)o(C)C)c(1
remove c from position 22,C6N(NC)(n=OCCCNC(O)c1c2nc(OC)o(C)C)c(1
remove C from position 12,C6N(NC)(n=OCCNC(O)c1c2nc(OC)o(C)C)c(1
remove ) from position 33,C6N(NC)(n=OCCNC(O)c1c2nc(OC)o(C)Cc(1
remove C from position 32,C6N(NC)(n=OCCNC(O)c1c2nc(OC)o(C)c(1
add - at position 21,C6N(NC)(n=OCCNC(O)c1c-2nc(OC)o(C)c(1
remove c from position 24,C6N(NC)(n=OCCNC(O)c1c-2n(OC)o(C)c(1
add H at position 4,C6N(HNC)(n=OCCNC(O)c1c-2n(OC)o(C)c(1
remove H from position 4,C6N(NC)(n=OCCNC(O)c1c-2n(OC)o(C)c(1
remove = from position 9,C6N(NC)(nOCCNC(O)c1c-2n(OC)o(C)c(1
add ] at position 22,C6N(NC)(nOCCNC(O)c1c-2]n(OC)o(C)c(1
add - at position 33,C6N(NC)(nOCCNC(O)c1c-2]n(OC)o(C)c-(1
remove ( from position 29,C6N(NC)(nOCCNC(O)c1c-2]n(OC)oC)c-(1
add O at position 17,C6N(NC)(nOCCNC(O)Oc1c-2]n(OC)oC)c-(1
replace C at position 11 with 3,C6N(NC)(nOC3NC(O)Oc1c-2]n(OC)oC)c-(1
add ] at position 9,C6N(NC)(n]OC3NC(O)Oc1c-2]n(OC)oC)c-(1
replace N at position 2 with H,C6H(NC)(n]OC3NC(O)Oc1c-2]n(OC)oC)c-(1
remove - from position 22,C6H(NC)(n]OC3NC(O)Oc1c2]n(OC)oC)c-(1
remove o from position 29,C6H(NC)(n]OC3NC(O)Oc1c2]n(OC)C)c-(1
remove ) from position 30,C6H(NC)(n]OC3NC(O)Oc1c2]n(OC)Cc-(1
replace O at position 10 with @,C6H(NC)(n]@C3NC(O)Oc1c2]n(OC)Cc-(1
remove 1 from position 33,C6H(NC)(n]@C3NC(O)Oc1c2]n(OC)Cc-(
replace O at position 18 with 3,C6H(NC)(n]@C3NC(O)3c1c2]n(OC)Cc-(
add = at position 11,C6H(NC)(n]@=C3NC(O)3c1c2]n(OC)Cc-(
remove ( from position 7,C6H(NC)n]@=C3NC(O)3c1c2]n(OC)Cc-(
replace ) at position 6 with n,C6H(NCnn]@=C3NC(O)3c1c2]n(OC)Cc-(
replace n at position 24 with l,C6H(NCnn]@=C3NC(O)3c1c2]l(OC)Cc-(
replace O at position 16 with N,C6H(NCnn]@=C3NC(N)3c1c2]l(OC)Cc-(
add S at position 6,C6H(NCSnn]@=C3NC(N)3c1c2]l(OC)Cc-(
remove @ from position 10,C6H(NCSnn]=C3NC(N)3c1c2]l(OC)Cc-(
remove ) from position 17,C6H(NCSnn]=C3NC(N3c1c2]l(OC)Cc-(
replace c at position 20 with l,C6H(NCSnn]=C3NC(N3c1l2]l(OC)Cc-(
replace N at position 4 with ),C6H()CSnn]=C3NC(N3c1l2]l(OC)Cc-(
remove ( from position 3,C6H)CSnn]=C3NC(N3c1l2]l(OC)Cc-(
replace C at position 25 with (,C6H)CSnn]=C3NC(N3c1l2]l(O()Cc-(
add c at position 5,C6H)CcSnn]=C3NC(N3c1l2]l(O()Cc-(
replace l at position 20 with 4,C6H)CcSnn]=C3NC(N3c142]l(O()Cc-(
replace C at position 4 with I,C6H)IcSnn]=C3NC(N3c142]l(O()Cc-(
remove I from position 4,C6H)cSnn]=C3NC(N3c142]l(O()Cc-(
remove ( from position 30,C6H)cSnn]=C3NC(N3c142]l(O()Cc-
add 1 at position 12,C6H)cSnn]=C31NC(N3c142]l(O()Cc-
remove C from position 28,C6H)cSnn]=C31NC(N3c142]l(O()c-
remove n from position 7,C6H)cSn]=C31NC(N3c142]l(O()c-
replace 1 at position 11 with s,C6H)cSn]=C3sNC(N3c142]l(O()c-
replace 3 at position 16 with r,C6H)cSn]=C3sNC(Nrc142]l(O()c-
remove S from position 5,C6H)cn]=C3sNC(Nrc142]l(O()c-
replace ] at position 6 with 4,C6H)cn4=C3sNC(Nrc142]l(O()c-
add F at position 11,C6H)cn4=C3sFNC(Nrc142]l(O()c-
add ] at position 29,C6H)cn4=C3sFNC(Nrc142]l(O()c-]
replace N at position 15 with 5,C6H)cn4=C3sFNC(5rc142]l(O()c-]
add I at position 1,CI6H)cn4=C3sFNC(5rc142]l(O()c-]
add l at position 9,CI6H)cn4=lC3sFNC(5rc142]l(O()c-]
remove 1 from position 20,CI6H)cn4=lC3sFNC(5rc42]l(O()c-]
add N at position 3,CI6NH)cn4=lC3sFNC(5rc42]l(O()c-]
add r at position 9,CI6NH)cn4r=lC3sFNC(5rc42]l(O()c-]
add 7 at position 27,CI6NH)cn4r=lC3sFNC(5rc42]l(7O()c-]
remove 3 from position 13,CI6NH)cn4r=lCsFNC(5rc42]l(7O()c-]
replace l at position 24 with s,CI6NH)cn4r=lCsFNC(5rc42]s(7O()c-]
replace ( at position 17 with H,CI6NH)cn4r=lCsFNCH5rc42]s(7O()c-]
remove r from position 19,CI6NH)cn4r=lCsFNCH5c42]s(7O()c-]
remove r from position 9,CI6NH)cn4=lCsFNCH5c42]s(7O()c-]
replace 6 at position 2 with c,CIcNH)cn4=lCsFNCH5c42]s(7O()c-]
remove F from position 13,CIcNH)cn4=lCsNCH5c42]s(7O()c-]
remove 4 from position 18,CIcNH)cn4=lCsNCH5c2]s(7O()c-]
remove N from position 3,CIcH)cn4=lCsNCH5c2]s(7O()c-]
remove = from position 8,CIcH)cn4lCsNCH5c2]s(7O()c-]
add l at position 22,CIcH)cn4lCsNCH5c2]s(7Ol()c-]
remove I from position 1,CcH)cn4lCsNCH5c2]s(7Ol()c-]
remove ] from position 26,CcH)cn4lCsNCH5c2]s(7Ol()c-
remove c from position 24,CcH)cn4lCsNCH5c2]s(7Ol()-
remove H from position 12,CcH)cn4lCsNC5c2]s(7Ol()-
remove H from position 2,Cc)cn4lCsNC5c2]s(7Ol()-
remove N from position 9,Cc)cn4lCsC5c2]s(7Ol()-
remove - from position 21,Cc)cn4lCsC5c2]s(7Ol()
replace 4 at position 5 with =,Cc)cn=lCsC5c2]s(7Ol()
remove = from position 5,Cc)cnlCsC5c2]s(7Ol()
remove 2 from position 11,Cc)cnlCsC5c]s(7Ol()
remove ] from position 11,Cc)cnlCsC5cs(7Ol()
add r at position 16,Cc)cnlCsC5cs(7Olr()
remove l from position 15,Cc)cnlCsC5cs(7Or()
remove O from position 14,Cc)cnlCsC5cs(7r()
remove c from position 3,Cc)nlCsC5cs(7r()
add ) at position 15,Cc)nlCsC5cs(7r())
replace s at position 10 with 6,Cc)nlCsC5c6(7r())
replace c at position 9 with 4,Cc)nlCsC546(7r())
remove 6 from position 10,Cc)nlCsC54(7r())
remove 4 from position 9,Cc)nlCsC5(7r())
remove 5 from position 8,Cc)nlCsC(7r())
replace l at position 4 with ),Cc)n)CsC(7r())
remove 7 from position 9,Cc)n)CsC(r())
add 5 at position 8,Cc)n)CsC5(r())
replace C at position 7 with 3,Cc)n)Cs35(r())
add H at position 8,Cc)n)Cs3H5(r())
remove c from position 1,C)n)Cs3H5(r())
replace C at position 4 with 6,C)n)6s3H5(r())
add 7 at position 0,7C)n)6s3H5(r())
add 1 at position 1,71C)n)6s3H5(r())
remove 5 from position 10,71C)n)6s3H(r())
remove 6 from position 6,71C)n)s3H(r())
replace 1 at position 1 with o,7oC)n)s3H(r())
replace n at position 4 with 3,7oC)3)s3H(r())
remove ( from position 9,7oC)3)s3Hr())
add ) at position 8,7oC)3)s3)Hr())
remove ) from position 12,7oC)3)s3)Hr()
add 5 at position 9,7oC)3)s3)5Hr()
replace ) at position 3 with 1,7oC13)s3)5Hr()
remove H from position 10,7oC13)s3)5r()
replace C at position 2 with r,7or13)s3)5r()
remove 5 from position 9,7or13)s3)r()
add s at position 8,7or13)s3s)r()
replace ( at position 11 with 1,7or13)s3s)r1)
replace ) at position 12 with F,7or13)s3s)r1F
remove s from position 6,7or13)3s)r1F
replace ) at position 5 with /,7or13/3s)r1F
remove ) from position 8,7or13/3sr1F
remove 1 from position 9,7or13/3srF
remove s from position 7,7or13/3rF
add r at position 1,7ror13/3rF
remove / from position 6,7ror133rF
replace r at position 1 with 5,75or133rF
add 6 at position 3,75o6r133rF
replace r at position 8 with S,75o6r133SF
replace F at position 9 with B,75o6r133SB
replace 7 at position 0 with B,B5o6r133SB
replace r at position 4 with =,B5o6=133SB
replace o at position 2 with B,B5B6=133SB
remove 6 from position 3,B5B=133SB
remove B from position 0,5B=133SB
replace 3 at position 5 with I,5B=13ISB
remove = from position 2,5B13ISB
add 1 at position 0,15B13ISB
replace B at position 2 with F,15F13ISB
replace 3 at position 4 with O,15F1OISB
remove B from position 7,15F1OIS
remove I from position 5,15F1OS
remove 1 from position 3,15FOS
remove F from position 2,15OS
add o at position 0,o15OS
remove 5 from position 2,o1OS
add ( at position 4,o1OS(
replace ( at position 4 with I,o1OSI
remove I from position 4,o1OS
add 6 at position 2,o16OS
replace 6 at position 2 with [,o1[OS
remove 1 from position 1,o[OS
remove [ from position 1,oOS
replace S at position 2 with [,oO[
remove o from position 0,O[
remove O from position 0,[
remove [ from position 0,
final: ,
