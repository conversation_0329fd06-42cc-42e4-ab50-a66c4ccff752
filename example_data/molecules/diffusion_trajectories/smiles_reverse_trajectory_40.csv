log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove <PERSON> from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add S at position 6,oH3H-]SN)
add [ at position 2,oH[3H-]SN)
add 4 at position 3,oH[43H-]SN)
replace S at position 8 with r,oH[43H-]rN)
add n at position 2,oHn[43H-]rN)
add 4 at position 2,oH4n[43H-]rN)
replace [ at position 4 with H,oH4nH43H-]rN)
add c at position 5,oH4nHc43H-]rN)
add O at position 2,oHO4nHc43H-]rN)
remove H from position 9,oHO4nHc43-]rN)
add 6 at position 13,oHO4nHc43-]rN6)
replace H at position 1 with 6,o6O4nHc43-]rN6)
remove H from position 5,o6O4nc43-]rN6)
replace 4 at position 3 with r,o6Ornc43-]rN6)
add r at position 6,o6Orncr43-]rN6)
add s at position 11,o6Orncr43-]srN6)
remove 6 from position 14,o6Orncr43-]srN)
replace ] at position 10 with H,o6Orncr43-HsrN)
remove O from position 2,o6rncr43-HsrN)
add 7 at position 11,o6rncr43-Hs7rN)
replace N at position 13 with /,o6rncr43-Hs7r/)
add S at position 6,o6rncrS43-Hs7r/)
add l at position 2,o6lrncrS43-Hs7r/)
add F at position 11,o6lrncrS43-FHs7r/)
replace o at position 0 with S,S6lrncrS43-FHs7r/)
remove S from position 7,S6lrncr43-FHs7r/)
add B at position 10,S6lrncr43-BFHs7r/)
remove l from position 2,S6rncr43-BFHs7r/)
add r at position 1,Sr6rncr43-BFHs7r/)
add ) at position 12,Sr6rncr43-BF)Hs7r/)
replace F at position 11 with 3,Sr6rncr43-B3)Hs7r/)
add ) at position 19,Sr6rncr43-B3)Hs7r/))
add 1 at position 2,Sr16rncr43-B3)Hs7r/))
add C at position 1,SCr16rncr43-B3)Hs7r/))
add C at position 21,SCr16rncr43-B3)Hs7r/)C)
remove r from position 2,SC16rncr43-B3)Hs7r/)C)
add B at position 14,SC16rncr43-B3)BHs7r/)C)
add c at position 19,SC16rncr43-B3)BHs7rc/)C)
add ) at position 17,SC16rncr43-B3)BHs)7rc/)C)
replace - at position 10 with 6,SC16rncr436B3)BHs)7rc/)C)
add ) at position 12,SC16rncr436B)3)BHs)7rc/)C)
replace C at position 24 with r,SC16rncr436B)3)BHs)7rc/)r)
replace / at position 22 with 2,SC16rncr436B)3)BHs)7rc2)r)
remove s from position 17,SC16rncr436B)3)BH)7rc2)r)
add F at position 19,SC16rncr436B)3)BH)7Frc2)r)
replace r at position 4 with 2,SC162ncr436B)3)BH)7Frc2)r)
add / at position 21,SC162ncr436B)3)BH)7Fr/c2)r)
remove B from position 15,SC162ncr436B)3)H)7Fr/c2)r)
add 2 at position 18,SC162ncr436B)3)H)72Fr/c2)r)
replace S at position 0 with 7,7C162ncr436B)3)H)72Fr/c2)r)
add O at position 20,7C162ncr436B)3)H)72FOr/c2)r)
add ( at position 11,7C162ncr436(B)3)H)72FOr/c2)r)
add ) at position 29,7C162ncr436(B)3)H)72FOr/c2)r))
replace 3 at position 9 with =,7C162ncr4=6(B)3)H)72FOr/c2)r))
replace ) at position 29 with 7,7C162ncr4=6(B)3)H)72FOr/c2)r)7
add = at position 13,7C162ncr4=6(B=)3)H)72FOr/c2)r)7
add C at position 20,7C162ncr4=6(B=)3)H)7C2FOr/c2)r)7
remove 1 from position 2,7C62ncr4=6(B=)3)H)7C2FOr/c2)r)7
remove 7 from position 0,C62ncr4=6(B=)3)H)7C2FOr/c2)r)7
replace 6 at position 8 with S,C62ncr4=S(B=)3)H)7C2FOr/c2)r)7
add N at position 3,C62Nncr4=S(B=)3)H)7C2FOr/c2)r)7
remove H from position 16,C62Nncr4=S(B=)3))7C2FOr/c2)r)7
replace 3 at position 14 with c,C62Nncr4=S(B=)c))7C2FOr/c2)r)7
add 5 at position 10,C62Nncr4=S5(B=)c))7C2FOr/c2)r)7
add l at position 13,C62Nncr4=S5(Bl=)c))7C2FOr/c2)r)7
replace ) at position 17 with F,C62Nncr4=S5(Bl=)cF)7C2FOr/c2)r)7
add 1 at position 32,C62Nncr4=S5(Bl=)cF)7C2FOr/c2)r)71
add 4 at position 18,C62Nncr4=S5(Bl=)cF4)7C2FOr/c2)r)71
add 7 at position 21,C62Nncr4=S5(Bl=)cF4)77C2FOr/c2)r)71
replace 4 at position 18 with s,C62Nncr4=S5(Bl=)cFs)77C2FOr/c2)r)71
replace 7 at position 21 with (,C62Nncr4=S5(Bl=)cFs)7(C2FOr/c2)r)71
remove ) from position 30,C62Nncr4=S5(Bl=)cFs)7(C2FOr/c2r)71
add = at position 6,C62Nnc=r4=S5(Bl=)cFs)7(C2FOr/c2r)71
add + at position 29,C62Nnc=r4=S5(Bl=)cFs)7(C2FOr/+c2r)71
add c at position 30,C62Nnc=r4=S5(Bl=)cFs)7(C2FOr/+cc2r)71
remove r from position 33,C62Nnc=r4=S5(Bl=)cFs)7(C2FOr/+cc2)71
add n at position 22,C62Nnc=r4=S5(Bl=)cFs)7n(C2FOr/+cc2)71
add o at position 23,C62Nnc=r4=S5(Bl=)cFs)7no(C2FOr/+cc2)71
remove / from position 30,C62Nnc=r4=S5(Bl=)cFs)7no(C2FOr+cc2)71
add c at position 30,C62Nnc=r4=S5(Bl=)cFs)7no(C2FOrc+cc2)71
add r at position 19,C62Nnc=r4=S5(Bl=)cFrs)7no(C2FOrc+cc2)71
add + at position 4,C62N+nc=r4=S5(Bl=)cFrs)7no(C2FOrc+cc2)71
replace = at position 10 with ),C62N+nc=r4)S5(Bl=)cFrs)7no(C2FOrc+cc2)71
remove 2 from position 28,C62N+nc=r4)S5(Bl=)cFrs)7no(CFOrc+cc2)71
remove 7 from position 37,C62N+nc=r4)S5(Bl=)cFrs)7no(CFOrc+cc2)1
add 4 at position 17,C62N+nc=r4)S5(Bl=4)cFrs)7no(CFOrc+cc2)1
add ] at position 7,C62N+nc]=r4)S5(Bl=4)cFrs)7no(CFOrc+cc2)1
add c at position 36,C62N+nc]=r4)S5(Bl=4)cFrs)7no(CFOrc+ccc2)1
replace = at position 17 with H,C62N+nc]=r4)S5(BlH4)cFrs)7no(CFOrc+ccc2)1
replace + at position 34 with ),C62N+nc]=r4)S5(BlH4)cFrs)7no(CFOrc)ccc2)1
add B at position 36,C62N+nc]=r4)S5(BlH4)cFrs)7no(CFOrc)cBcc2)1
add n at position 19,C62N+nc]=r4)S5(BlH4n)cFrs)7no(CFOrc)cBcc2)1
replace H at position 17 with -,C62N+nc]=r4)S5(Bl-4n)cFrs)7no(CFOrc)cBcc2)1
replace s at position 24 with 3,C62N+nc]=r4)S5(Bl-4n)cFr3)7no(CFOrc)cBcc2)1
add N at position 13,C62N+nc]=r4)SN5(Bl-4n)cFr3)7no(CFOrc)cBcc2)1
remove 7 from position 27,C62N+nc]=r4)SN5(Bl-4n)cFr3)no(CFOrc)cBcc2)1
remove r from position 9,C62N+nc]=4)SN5(Bl-4n)cFr3)no(CFOrc)cBcc2)1
remove N from position 3,C62+nc]=4)SN5(Bl-4n)cFr3)no(CFOrc)cBcc2)1
add r at position 10,C62+nc]=4)rSN5(Bl-4n)cFr3)no(CFOrc)cBcc2)1
remove ) from position 34,C62+nc]=4)rSN5(Bl-4n)cFr3)no(CFOrccBcc2)1
remove r from position 10,C62+nc]=4)SN5(Bl-4n)cFr3)no(CFOrccBcc2)1
add - at position 8,C62+nc]=-4)SN5(Bl-4n)cFr3)no(CFOrccBcc2)1
remove B from position 35,C62+nc]=-4)SN5(Bl-4n)cFr3)no(CFOrcccc2)1
remove F from position 22,C62+nc]=-4)SN5(Bl-4n)cr3)no(CFOrcccc2)1
replace 5 at position 13 with 3,C62+nc]=-4)SN3(Bl-4n)cr3)no(CFOrcccc2)1
add - at position 11,C62+nc]=-4)-SN3(Bl-4n)cr3)no(CFOrcccc2)1
replace r at position 32 with (,C62+nc]=-4)-SN3(Bl-4n)cr3)no(CFO(cccc2)1
remove r from position 23,C62+nc]=-4)-SN3(Bl-4n)c3)no(CFO(cccc2)1
replace F at position 29 with /,C62+nc]=-4)-SN3(Bl-4n)c3)no(C/O(cccc2)1
remove S from position 12,C62+nc]=-4)-N3(Bl-4n)c3)no(C/O(cccc2)1
add c at position 12,C62+nc]=-4)-cN3(Bl-4n)c3)no(C/O(cccc2)1
add I at position 4,C62+Inc]=-4)-cN3(Bl-4n)c3)no(C/O(cccc2)1
replace I at position 4 with -,C62+-nc]=-4)-cN3(Bl-4n)c3)no(C/O(cccc2)1
replace 4 at position 20 with ],C62+-nc]=-4)-cN3(Bl-]n)c3)no(C/O(cccc2)1
add S at position 5,C62+-Snc]=-4)-cN3(Bl-]n)c3)no(C/O(cccc2)1
replace 2 at position 2 with H,C6H+-Snc]=-4)-cN3(Bl-]n)c3)no(C/O(cccc2)1
add c at position 3,C6Hc+-Snc]=-4)-cN3(Bl-]n)c3)no(C/O(cccc2)1
replace + at position 4 with (,C6Hc(-Snc]=-4)-cN3(Bl-]n)c3)no(C/O(cccc2)1
replace l at position 20 with C,C6Hc(-Snc]=-4)-cN3(BC-]n)c3)no(C/O(cccc2)1
add 2 at position 17,C6Hc(-Snc]=-4)-cN23(BC-]n)c3)no(C/O(cccc2)1
add B at position 10,C6Hc(-Snc]B=-4)-cN23(BC-]n)c3)no(C/O(cccc2)1
remove S from position 6,C6Hc(-nc]B=-4)-cN23(BC-]n)c3)no(C/O(cccc2)1
replace N at position 16 with c,C6Hc(-nc]B=-4)-cc23(BC-]n)c3)no(C/O(cccc2)1
replace n at position 24 with 2,C6Hc(-nc]B=-4)-cc23(BC-]2)c3)no(C/O(cccc2)1
replace n at position 6 with c,C6Hc(-cc]B=-4)-cc23(BC-]2)c3)no(C/O(cccc2)1
add n at position 7,C6Hc(-cnc]B=-4)-cc23(BC-]2)c3)no(C/O(cccc2)1
remove = from position 11,C6Hc(-cnc]B-4)-cc23(BC-]2)c3)no(C/O(cccc2)1
replace 3 at position 18 with O,C6Hc(-cnc]B-4)-cc2O(BC-]2)c3)no(C/O(cccc2)1
add - at position 33,C6Hc(-cnc]B-4)-cc2O(BC-]2)c3)no(C-/O(cccc2)1
replace B at position 10 with (,C6Hc(-cnc](-4)-cc2O(BC-]2)c3)no(C-/O(cccc2)1
replace o at position 30 with (,C6Hc(-cnc](-4)-cc2O(BC-]2)c3)n((C-/O(cccc2)1
replace ( at position 30 with c,C6Hc(-cnc](-4)-cc2O(BC-]2)c3)nc(C-/O(cccc2)1
add c at position 37,C6Hc(-cnc](-4)-cc2O(BC-]2)c3)nc(C-/O(ccccc2)1
add l at position 22,C6Hc(-cnc](-4)-cc2O(BCl-]2)c3)nc(C-/O(ccccc2)1
replace H at position 2 with n,C6nc(-cnc](-4)-cc2O(BCl-]2)c3)nc(C-/O(ccccc2)1
remove ] from position 9,C6nc(-cnc(-4)-cc2O(BCl-]2)c3)nc(C-/O(ccccc2)1
replace 4 at position 11 with 3,C6nc(-cnc(-3)-cc2O(BCl-]2)c3)nc(C-/O(ccccc2)1
remove O from position 17,C6nc(-cnc(-3)-cc2(BCl-]2)c3)nc(C-/O(ccccc2)1
add + at position 29,C6nc(-cnc(-3)-cc2(BCl-]2)c3)n+c(C-/O(ccccc2)1
remove - from position 33,C6nc(-cnc(-3)-cc2(BCl-]2)c3)n+c(C/O(ccccc2)1
remove ] from position 22,C6nc(-cnc(-3)-cc2(BCl-2)c3)n+c(C/O(ccccc2)1
remove - from position 13,C6nc(-cnc(-3)cc2(BCl-2)c3)n+c(C/O(ccccc2)1
remove / from position 31,C6nc(-cnc(-3)cc2(BCl-2)c3)n+c(CO(ccccc2)1
add 1 at position 3,C6n1c(-cnc(-3)cc2(BCl-2)c3)n+c(CO(ccccc2)1
add + at position 42,C6n1c(-cnc(-3)cc2(BCl-2)c3)n+c(CO(ccccc2)1+
remove - from position 21,C6n1c(-cnc(-3)cc2(BCl2)c3)n+c(CO(ccccc2)1+
add ) at position 32,C6n1c(-cnc(-3)cc2(BCl2)c3)n+c(CO)(ccccc2)1+
add c at position 33,C6n1c(-cnc(-3)cc2(BCl2)c3)n+c(CO)c(ccccc2)1+
add c at position 12,C6n1c(-cnc(-c3)cc2(BCl2)c3)n+c(CO)c(ccccc2)1+
replace 2 at position 17 with c,C6n1c(-cnc(-c3)ccc(BCl2)c3)n+c(CO)c(ccccc2)1+
remove B from position 19,C6n1c(-cnc(-c3)ccc(Cl2)c3)n+c(CO)c(ccccc2)1+
add 2 at position 35,C6n1c(-cnc(-c3)ccc(Cl2)c3)n+c(CO)c(2ccccc2)1+
add C at position 2,C6Cn1c(-cnc(-c3)ccc(Cl2)c3)n+c(CO)c(2ccccc2)1+
remove ) from position 43,C6Cn1c(-cnc(-c3)ccc(Cl2)c3)n+c(CO)c(2ccccc21+
add ) at position 29,C6Cn1c(-cnc(-c3)ccc(Cl2)c3)n+)c(CO)c(2ccccc21+
replace ) at position 15 with c,C6Cn1c(-cnc(-c3cccc(Cl2)c3)n+)c(CO)c(2ccccc21+
replace C at position 32 with =,C6Cn1c(-cnc(-c3cccc(Cl2)c3)n+)c(=O)c(2ccccc21+
remove + from position 45,C6Cn1c(-cnc(-c3cccc(Cl2)c3)n+)c(=O)c(2ccccc21
add 2 at position 9,C6Cn1c(-c2nc(-c3cccc(Cl2)c3)n+)c(=O)c(2ccccc21
add 2 at position 30,C6Cn1c(-c2nc(-c3cccc(Cl2)c3)n+2)c(=O)c(2ccccc21
remove 6 from position 1,CCn1c(-c2nc(-c3cccc(Cl2)c3)n+2)c(=O)c(2ccccc21
remove 2 from position 22,CCn1c(-c2nc(-c3cccc(Cl)c3)n+2)c(=O)c(2ccccc21
add c at position 5,CCn1cc(-c2nc(-c3cccc(Cl)c3)n+2)c(=O)c(2ccccc21
remove ( from position 37,CCn1cc(-c2nc(-c3cccc(Cl)c3)n+2)c(=O)c2ccccc21
replace + at position 28 with o,CCn1cc(-c2nc(-c3cccc(Cl)c3)no2)c(=O)c2ccccc21
final: CCn1cc(-c2nc(-c3cccc(Cl)c3)no2)c(=O)c2ccccc21,CCn1cc(-c2nc(-c3cccc(Cl)c3)no2)c(=O)c2ccccc21
