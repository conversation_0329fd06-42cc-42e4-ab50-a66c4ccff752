log,state
initialize: CC(C)(C)NS(=O)(=O)c1ccc(OCC(=O)N2CCOCC2)cc1,CC(C)(C)NS(=O)(=O)c1ccc(OCC(=O)N2CCOCC2)cc1
replace = at position 28 with +,CC(C)(C)NS(=O)(=O)c1ccc(OCC(+O)N2CCOCC2)cc1
add ( at position 37,CC(C)(C)NS(=O)(=O)c1ccc(OCC(+O)N2CCOC(C2)cc1
remove ( from position 5,CC(C)C)NS(=O)(=O)c1ccc(OCC(+O)N2CCOC(C2)cc1
add 2 at position 22,CC(C)C)NS(=O)(=O)c1ccc2(OCC(+O)N2CCOC(C2)cc1
add 6 at position 1,C6C(C)C)NS(=O)(=O)c1ccc2(OCC(+O)N2CCOC(C2)cc1
remove O from position 30,C6C(C)C)NS(=O)(=O)c1ccc2(OCC(+)N2CCOC(C2)cc1
remove S from position 9,C6C(C)C)N(=O)(=O)c1ccc2(OCC(+)N2CCOC(C2)cc1
add n at position 7,C6C(C)Cn)N(=O)(=O)c1ccc2(OCC(+)N2CCOC(C2)cc1
add o at position 31,C6C(C)Cn)N(=O)(=O)c1ccc2(OCC(+)oN2CCOC(C2)cc1
replace C at position 4 with S,C6C(S)Cn)N(=O)(=O)c1ccc2(OCC(+)oN2CCOC(C2)cc1
remove + from position 29,C6C(S)Cn)N(=O)(=O)c1ccc2(OCC()oN2CCOC(C2)cc1
add ) at position 43,C6C(S)Cn)N(=O)(=O)c1ccc2(OCC()oN2CCOC(C2)cc)1
remove C from position 2,C6(S)Cn)N(=O)(=O)c1ccc2(OCC()oN2CCOC(C2)cc)1
remove C from position 35,C6(S)Cn)N(=O)(=O)c1ccc2(OCC()oN2CCO(C2)cc)1
add B at position 19,C6(S)Cn)N(=O)(=O)c1Bccc2(OCC()oN2CCO(C2)cc)1
replace c at position 17 with 2,C6(S)Cn)N(=O)(=O)21Bccc2(OCC()oN2CCO(C2)cc)1
remove ) from position 12,C6(S)Cn)N(=O(=O)21Bccc2(OCC()oN2CCO(C2)cc)1
remove C from position 33,C6(S)Cn)N(=O(=O)21Bccc2(OCC()oN2CO(C2)cc)1
remove C from position 32,C6(S)Cn)N(=O(=O)21Bccc2(OCC()oN2O(C2)cc)1
add - at position 21,C6(S)Cn)N(=O(=O)21Bcc-c2(OCC()oN2O(C2)cc)1
remove ( from position 24,C6(S)Cn)N(=O(=O)21Bcc-c2OCC()oN2O(C2)cc)1
add H at position 4,C6(SH)Cn)N(=O(=O)21Bcc-c2OCC()oN2O(C2)cc)1
remove H from position 4,C6(S)Cn)N(=O(=O)21Bcc-c2OCC()oN2O(C2)cc)1
remove ( from position 9,C6(S)Cn)N=O(=O)21Bcc-c2OCC()oN2O(C2)cc)1
add ] at position 22,C6(S)Cn)N=O(=O)21Bcc-c]2OCC()oN2O(C2)cc)1
add - at position 33,C6(S)Cn)N=O(=O)21Bcc-c]2OCC()oN2O-(C2)cc)1
remove o from position 29,C6(S)Cn)N=O(=O)21Bcc-c]2OCC()N2O-(C2)cc)1
add O at position 17,C6(S)Cn)N=O(=O)21OBcc-c]2OCC()N2O-(C2)cc)1
replace ( at position 11 with 4,C6(S)Cn)N=O4=O)21OBcc-c]2OCC()N2O-(C2)cc)1
add ] at position 9,C6(S)Cn)N]=O4=O)21OBcc-c]2OCC()N2O-(C2)cc)1
replace ( at position 2 with I,C6IS)Cn)N]=O4=O)21OBcc-c]2OCC()N2O-(C2)cc)1
remove - from position 22,C6IS)Cn)N]=O4=O)21OBccc]2OCC()N2O-(C2)cc)1
remove ) from position 37,C6IS)Cn)N]=O4=O)21OBccc]2OCC()N2O-(C2cc)1
replace N at position 30 with (,C6IS)Cn)N]=O4=O)21OBccc]2OCC()(2O-(C2cc)1
replace ( at position 30 with o,C6IS)Cn)N]=O4=O)21OBccc]2OCC()o2O-(C2cc)1
replace = at position 10 with B,C6IS)Cn)N]BO4=O)21OBccc]2OCC()o2O-(C2cc)1
remove - from position 33,C6IS)Cn)N]BO4=O)21OBccc]2OCC()o2O(C2cc)1
replace O at position 18 with 3,C6IS)Cn)N]BO4=O)213Bccc]2OCC()o2O(C2cc)1
add = at position 11,C6IS)Cn)N]B=O4=O)213Bccc]2OCC()o2O(C2cc)1
remove ) from position 7,C6IS)CnN]B=O4=O)213Bccc]2OCC()o2O(C2cc)1
replace n at position 6 with l,C6IS)ClN]B=O4=O)213Bccc]2OCC()o2O(C2cc)1
replace 2 at position 24 with n,C6IS)ClN]B=O4=O)213Bccc]nOCC()o2O(C2cc)1
replace 2 at position 16 with O,C6IS)ClN]B=O4=O)O13Bccc]nOCC()o2O(C2cc)1
add S at position 6,C6IS)CSlN]B=O4=O)O13Bccc]nOCC()o2O(C2cc)1
remove B from position 10,C6IS)CSlN]=O4=O)O13Bccc]nOCC()o2O(C2cc)1
remove 1 from position 17,C6IS)CSlN]=O4=O)O3Bccc]nOCC()o2O(C2cc)1
replace c at position 20 with l,C6IS)CSlN]=O4=O)O3Bclc]nOCC()o2O(C2cc)1
replace ) at position 4 with +,C6IS+CSlN]=O4=O)O3Bclc]nOCC()o2O(C2cc)1
remove S from position 3,C6I+CSlN]=O4=O)O3Bclc]nOCC()o2O(C2cc)1
replace I at position 2 with 2,C62+CSlN]=O4=O)O3Bclc]nOCC()o2O(C2cc)1
remove S from position 5,C62+ClN]=O4=O)O3Bclc]nOCC()o2O(C2cc)1
replace ] at position 20 with 4,C62+ClN]=O4=O)O3Bclc4nOCC()o2O(C2cc)1
replace C at position 4 with I,C62+IlN]=O4=O)O3Bclc4nOCC()o2O(C2cc)1
remove I from position 4,C62+lN]=O4=O)O3Bclc4nOCC()o2O(C2cc)1
remove ) from position 12,C62+lN]=O4=OO3Bclc4nOCC()o2O(C2cc)1
add S at position 12,C62+lN]=O4=OSO3Bclc4nOCC()o2O(C2cc)1
replace ( at position 29 with F,C62+lN]=O4=OSO3Bclc4nOCC()o2OFC2cc)1
add r at position 23,C62+lN]=O4=OSO3Bclc4nOCrC()o2OFC2cc)1
replace 2 at position 32 with r,C62+lN]=O4=OSO3Bclc4nOCrC()o2OFCrcc)1
remove O from position 11,C62+lN]=O4=SO3Bclc4nOCrC()o2OFCrcc)1
replace 3 at position 13 with 5,C62+lN]=O4=SO5Bclc4nOCrC()o2OFCrcc)1
add F at position 22,C62+lN]=O4=SO5Bclc4nOCFrC()o2OFCrcc)1
add B at position 35,C62+lN]=O4=SO5Bclc4nOCFrC()o2OFCrccB)1
remove O from position 8,C62+lN]=4=SO5Bclc4nOCFrC()o2OFCrccB)1
add r at position 10,C62+lN]=4=rSO5Bclc4nOCFrC()o2OFCrccB)1
add ) at position 34,C62+lN]=4=rSO5Bclc4nOCFrC()o2OFCrc)cB)1
remove r from position 10,C62+lN]=4=SO5Bclc4nOCFrC()o2OFCrc)cB)1
add N at position 3,C62N+lN]=4=SO5Bclc4nOCFrC()o2OFCrc)cB)1
add r at position 9,C62N+lN]=r4=SO5Bclc4nOCFrC()o2OFCrc)cB)1
add 7 at position 27,C62N+lN]=r4=SO5Bclc4nOCFrC(7)o2OFCrc)cB)1
remove O from position 13,C62N+lN]=r4=S5Bclc4nOCFrC(7)o2OFCrc)cB)1
replace C at position 24 with s,C62N+lN]=r4=S5Bclc4nOCFrs(7)o2OFCrc)cB)1
replace c at position 17 with F,C62N+lN]=r4=S5BclF4nOCFrs(7)o2OFCrc)cB)1
remove n from position 19,C62N+lN]=r4=S5BclF4OCFrs(7)o2OFCrc)cB)1
remove B from position 36,C62N+lN]=r4=S5BclF4OCFrs(7)o2OFCrc)c)1
replace ) at position 34 with +,C62N+lN]=r4=S5BclF4OCFrs(7)o2OFCrc+c)1
replace F at position 17 with =,C62N+lN]=r4=S5Bcl=4OCFrs(7)o2OFCrc+c)1
remove ) from position 36,C62N+lN]=r4=S5Bcl=4OCFrs(7)o2OFCrc+c1
remove ] from position 7,C62N+lN=r4=S5Bcl=4OCFrs(7)o2OFCrc+c1
remove 4 from position 17,C62N+lN=r4=S5Bcl=OCFrs(7)o2OFCrc+c1
add ( at position 25,C62N+lN=r4=S5Bcl=OCFrs(7)(o2OFCrc+c1
remove O from position 28,C62N+lN=r4=S5Bcl=OCFrs(7)(o2FCrc+c1
add / at position 31,C62N+lN=r4=S5Bcl=OCFrs(7)(o2FCr/c+c1
remove C from position 0,62N+lN=r4=S5Bcl=OCFrs(7)(o2FCr/c+c1
add 4 at position 25,62N+lN=r4=S5Bcl=OCFrs(7)(4o2FCr/c+c1
remove r from position 30,62N+lN=r4=S5Bcl=OCFrs(7)(4o2FC/c+c1
add / at position 30,62N+lN=r4=S5Bcl=OCFrs(7)(4o2FC//c+c1
remove ) from position 23,62N+lN=r4=S5Bcl=OCFrs(7(4o2FC//c+c1
remove 7 from position 22,62N+lN=r4=S5Bcl=OCFrs((4o2FC//c+c1
add r at position 33,62N+lN=r4=S5Bcl=OCFrs((4o2FC//c+cr1
remove c from position 30,62N+lN=r4=S5Bcl=OCFrs((4o2FC//+cr1
remove / from position 29,62N+lN=r4=S5Bcl=OCFrs((4o2FC/+cr1
remove = from position 6,62N+lNr4=S5Bcl=OCFrs((4o2FC/+cr1
add ) at position 30,62N+lNr4=S5Bcl=OCFrs((4o2FC/+c)r1
replace ( at position 21 with 7,62N+lNr4=S5Bcl=OCFrs(74o2FC/+c)r1
replace r at position 18 with 4,62N+lNr4=S5Bcl=OCF4s(74o2FC/+c)r1
remove 7 from position 21,62N+lNr4=S5Bcl=OCF4s(4o2FC/+c)r1
remove 4 from position 18,62N+lNr4=S5Bcl=OCFs(4o2FC/+c)r1
remove C from position 16,62N+lNr4=S5Bcl=OFs(4o2FC/+c)r1
replace = at position 8 with ),62N+lNr4)S5Bcl=OFs(4o2FC/+c)r1
remove 4 from position 19,62N+lNr4)S5Bcl=OFs(o2FC/+c)r1
add 5 at position 16,62N+lNr4)S5Bcl=O5Fs(o2FC/+c)r1
replace = at position 14 with 3,62N+lNr4)S5Bcl3O5Fs(o2FC/+c)r1
add H at position 16,62N+lNr4)S5Bcl3OH5Fs(o2FC/+c)r1
remove + from position 3,62NlNr4)S5Bcl3OH5Fs(o2FC/+c)r1
replace S at position 8 with 6,62NlNr4)65Bcl3OH5Fs(o2FC/+c)r1
add 7 at position 0,762NlNr4)65Bcl3OH5Fs(o2FC/+c)r1
add 1 at position 2,7612NlNr4)65Bcl3OH5Fs(o2FC/+c)r1
remove s from position 20,7612NlNr4)65Bcl3OH5F(o2FC/+c)r1
remove c from position 13,7612NlNr4)65Bl3OH5F(o2FC/+c)r1
replace 1 at position 29 with ),7612NlNr4)65Bl3OH5F(o2FC/+c)r)
replace ) at position 9 with 4,7612NlNr4465Bl3OH5F(o2FC/+c)r)
remove ) from position 29,7612NlNr4465Bl3OH5F(o2FC/+c)r
remove 5 from position 11,7612NlNr446Bl3OH5F(o2FC/+c)r
remove 2 from position 20,7612NlNr446Bl3OH5F(oFC/+c)r
replace 7 at position 0 with S,S612NlNr446Bl3OH5F(oFC/+c)r
remove ( from position 18,S612NlNr446Bl3OH5FoFC/+c)r
add B at position 15,S612NlNr446Bl3OBH5FoFC/+c)r
remove C from position 21,S612NlNr446Bl3OBH5FoF/+c)r
replace N at position 4 with r,S612rlNr446Bl3OBH5FoF/+c)r
remove o from position 19,S612rlNr446Bl3OBH5FF/+c)r
add s at position 17,S612rlNr446Bl3OBHs5FF/+c)r
replace + at position 22 with 1,S612rlNr446Bl3OBHs5FF/1c)r
replace ) at position 24 with F,S612rlNr446Bl3OBHs5FF/1cFr
remove l from position 12,S612rlNr446B3OBHs5FF/1cFr
replace 6 at position 10 with -,S612rlNr44-B3OBHs5FF/1cFr
remove 5 from position 17,S612rlNr44-B3OBHsFF/1cFr
remove / from position 19,S612rlNr44-B3OBHsFF1cFr
remove B from position 14,S612rlNr44-B3OHsFF1cFr
add r at position 2,S6r12rlNr44-B3OHsFF1cFr
remove F from position 21,S6r12rlNr44-B3OHsFF1cr
remove 6 from position 1,Sr12rlNr44-B3OHsFF1cr
remove 1 from position 2,Sr2rlNr44-B3OHsFF1cr
remove r from position 19,Sr2rlNr44-B3OHsFF1c
replace 3 at position 11 with F,Sr2rlNr44-BFOHsFF1c
remove O from position 12,Sr2rlNr44-BFHsFF1c
remove r from position 1,S2rlNr44-BFHsFF1c
add l at position 2,S2lrlNr44-BFHsFF1c
remove B from position 10,S2lrlNr44-FHsFF1c
add S at position 7,S2lrlNrS44-FHsFF1c
replace S at position 0 with o,o2lrlNrS44-FHsFF1c
remove F from position 11,o2lrlNrS44-HsFF1c
remove l from position 2,o2rlNrS44-HsFF1c
remove S from position 6,o2rlNr44-HsFF1c
replace 1 at position 13 with N,o2rlNr44-HsFFNc
remove F from position 11,o2rlNr44-HsFNc
add O at position 2,o2OrlNr44-HsFNc
replace H at position 10 with ],o2OrlNr44-]sFNc
add 6 at position 14,o2OrlNr44-]sFN6c
remove s from position 11,o2OrlNr44-]FN6c
remove r from position 6,o2OrlN44-]FN6c
replace r at position 3 with 4,o2O4lN44-]FN6c
add H at position 5,o2O4lHN44-]FN6c
replace 2 at position 1 with H,oHO4lHN44-]FN6c
remove 6 from position 13,oHO4lHN44-]FNc
add H at position 9,oHO4lHN44H-]FNc
remove O from position 2,oH4lHN44H-]FNc
remove N from position 5,oH4lH44H-]FNc
replace H at position 4 with [,oH4l[44H-]FNc
remove 4 from position 2,oHl[44H-]FNc
remove l from position 2,oH[44H-]FNc
replace F at position 8 with [,oH[44H-][Nc
remove 4 from position 3,oH[4H-][Nc
remove [ from position 2,oH4H-][Nc
remove [ from position 6,oH4H-]Nc
remove c from position 7,oH4H-]N
replace - at position 4 with B,oH4HB]N
remove N from position 6,oH4HB]
remove B from position 4,oH4H]
add C at position 5,oH4H]C
replace ] at position 4 with -,oH4H-C
add B at position 3,oH4BH-C
add c at position 2,oHc4BH-C
replace 4 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
