log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove C from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add S at position 6,oH3H-]SN)
add [ at position 2,oH[3H-]SN)
add O at position 3,oH[O3H-]SN)
replace S at position 8 with c,oH[O3H-]cN)
add c at position 2,oHc[O3H-]cN)
add 4 at position 2,oH4c[O3H-]cN)
replace [ at position 4 with H,oH4cHO3H-]cN)
add ] at position 5,oH4cH]O3H-]cN)
add O at position 2,oHO4cH]O3H-]cN)
remove H from position 9,oHO4cH]O3-]cN)
add 6 at position 13,oHO4cH]O3-]cN6)
replace H at position 1 with 6,o6O4cH]O3-]cN6)
remove H from position 5,o6O4c]O3-]cN6)
replace 4 at position 3 with r,o6Orc]O3-]cN6)
add S at position 6,o6Orc]SO3-]cN6)
add s at position 11,o6Orc]SO3-]scN6)
remove 6 from position 14,o6Orc]SO3-]scN)
replace ] at position 10 with H,o6Orc]SO3-HscN)
remove O from position 2,o6rc]SO3-HscN)
add O at position 11,o6rc]SO3-HsOcN)
replace N at position 13 with /,o6rc]SO3-HsOc/)
add S at position 6,o6rc]SSO3-HsOc/)
add l at position 2,o6lrc]SSO3-HsOc/)
add F at position 11,o6lrc]SSO3-FHsOc/)
replace o at position 0 with S,S6lrc]SSO3-FHsOc/)
remove S from position 7,S6lrc]SO3-FHsOc/)
add - at position 10,S6lrc]SO3--FHsOc/)
remove l from position 2,S6rc]SO3--FHsOc/)
add r at position 1,Sr6rc]SO3--FHsOc/)
add ) at position 12,Sr6rc]SO3--F)HsOc/)
replace F at position 11 with 3,Sr6rc]SO3--3)HsOc/)
add 2 at position 19,Sr6rc]SO3--3)HsOc/)2
add 1 at position 2,Sr16rc]SO3--3)HsOc/)2
add C at position 1,SCr16rc]SO3--3)HsOc/)2
add C at position 21,SCr16rc]SO3--3)HsOc/)C2
remove r from position 2,SC16rc]SO3--3)HsOc/)C2
add B at position 14,SC16rc]SO3--3)BHsOc/)C2
add ) at position 19,SC16rc]SO3--3)BHsOc)/)C2
add o at position 17,SC16rc]SO3--3)BHsoOc)/)C2
replace - at position 10 with 7,SC16rc]SO37-3)BHsoOc)/)C2
add r at position 12,SC16rc]SO37-r3)BHsoOc)/)C2
replace C at position 24 with r,SC16rc]SO37-r3)BHsoOc)/)r2
replace / at position 22 with C,SC16rc]SO37-r3)BHsoOc)C)r2
remove s from position 17,SC16rc]SO37-r3)BHoOc)C)r2
add c at position 19,SC16rc]SO37-r3)BHoOcc)C)r2
replace r at position 4 with @,SC16@c]SO37-r3)BHoOcc)C)r2
add / at position 21,SC16@c]SO37-r3)BHoOcc/)C)r2
remove B from position 15,SC16@c]SO37-r3)HoOcc/)C)r2
add 2 at position 18,SC16@c]SO37-r3)HoO2cc/)C)r2
replace S at position 0 with 7,7C16@c]SO37-r3)HoO2cc/)C)r2
add ) at position 20,7C16@c]SO37-r3)HoO2c)c/)C)r2
add l at position 11,7C16@c]SO37l-r3)HoO2c)c/)C)r2
add ) at position 29,7C16@c]SO37l-r3)HoO2c)c/)C)r2)
replace 3 at position 9 with =,7C16@c]SO=7l-r3)HoO2c)c/)C)r2)
replace ) at position 29 with 7,7C16@c]SO=7l-r3)HoO2c)c/)C)r27
add = at position 13,7C16@c]SO=7l-=r3)HoO2c)c/)C)r27
add r at position 20,7C16@c]SO=7l-=r3)HoOr2c)c/)C)r27
remove 1 from position 2,7C6@c]SO=7l-=r3)HoOr2c)c/)C)r27
remove 7 from position 0,C6@c]SO=7l-=r3)HoOr2c)c/)C)r27
replace 7 at position 8 with 3,C6@c]SO=3l-=r3)HoOr2c)c/)C)r27
add ) at position 3,C6@)c]SO=3l-=r3)HoOr2c)c/)C)r27
remove H from position 16,C6@)c]SO=3l-=r3)oOr2c)c/)C)r27
replace 3 at position 14 with C,C6@)c]SO=3l-=rC)oOr2c)c/)C)r27
add B at position 10,C6@)c]SO=3Bl-=rC)oOr2c)c/)C)r27
add 4 at position 13,C6@)c]SO=3Bl-4=rC)oOr2c)c/)C)r27
replace ) at position 17 with C,C6@)c]SO=3Bl-4=rCCoOr2c)c/)C)r27
add ( at position 32,C6@)c]SO=3Bl-4=rCCoOr2c)c/)C)r27(
add 4 at position 18,C6@)c]SO=3Bl-4=rCC4oOr2c)c/)C)r27(
add 6 at position 21,C6@)c]SO=3Bl-4=rCC4oO6r2c)c/)C)r27(
replace 4 at position 18 with r,C6@)c]SO=3Bl-4=rCCroO6r2c)c/)C)r27(
replace 6 at position 21 with C,C6@)c]SO=3Bl-4=rCCroOCr2c)c/)C)r27(
remove ) from position 30,C6@)c]SO=3Bl-4=rCCroOCr2c)c/)Cr27(
add 3 at position 6,C6@)c]3SO=3Bl-4=rCCroOCr2c)c/)Cr27(
add ) at position 29,C6@)c]3SO=3Bl-4=rCCroOCr2c)c/))Cr27(
add c at position 30,C6@)c]3SO=3Bl-4=rCCroOCr2c)c/)c)Cr27(
remove r from position 33,C6@)c]3SO=3Bl-4=rCCroOCr2c)c/)c)C27(
add C at position 22,C6@)c]3SO=3Bl-4=rCCroOCCr2c)c/)c)C27(
add F at position 23,C6@)c]3SO=3Bl-4=rCCroOCFCr2c)c/)c)C27(
remove / from position 30,C6@)c]3SO=3Bl-4=rCCroOCFCr2c)c)c)C27(
add B at position 30,C6@)c]3SO=3Bl-4=rCCroOCFCr2c)cB)c)C27(
add N at position 19,C6@)c]3SO=3Bl-4=rCCNroOCFCr2c)cB)c)C27(
add n at position 4,C6@)nc]3SO=3Bl-4=rCCNroOCFCr2c)cB)c)C27(
replace = at position 10 with 5,C6@)nc]3SO53Bl-4=rCCNroOCFCr2c)cB)c)C27(
remove 2 from position 28,C6@)nc]3SO53Bl-4=rCCNroOCFCrc)cB)c)C27(
remove 7 from position 37,C6@)nc]3SO53Bl-4=rCCNroOCFCrc)cB)c)C2(
add 1 at position 17,C6@)nc]3SO53Bl-4=1rCCNroOCFCrc)cB)c)C2(
add = at position 7,C6@)nc]=3SO53Bl-4=1rCCNroOCFCrc)cB)c)C2(
add ) at position 36,C6@)nc]=3SO53Bl-4=1rCCNroOCFCrc)cB)c))C2(
replace = at position 17 with F,C6@)nc]=3SO53Bl-4F1rCCNroOCFCrc)cB)c))C2(
replace ) at position 34 with c,C6@)nc]=3SO53Bl-4F1rCCNroOCFCrc)cBcc))C2(
add 1 at position 36,C6@)nc]=3SO53Bl-4F1rCCNroOCFCrc)cBcc1))C2(
add ) at position 19,C6@)nc]=3SO53Bl-4F1)rCCNroOCFCrc)cBcc1))C2(
replace F at position 17 with n,C6@)nc]=3SO53Bl-4n1)rCCNroOCFCrc)cBcc1))C2(
remove r from position 24,C6@)nc]=3SO53Bl-4n1)rCCNoOCFCrc)cBcc1))C2(
add F at position 20,C6@)nc]=3SO53Bl-4n1)FrCCNoOCFCrc)cBcc1))C2(
replace @ at position 2 with 2,C62)nc]=3SO53Bl-4n1)FrCCNoOCFCrc)cBcc1))C2(
add + at position 9,C62)nc]=3+SO53Bl-4n1)FrCCNoOCFCrc)cBcc1))C2(
remove ( from position 43,C62)nc]=3+SO53Bl-4n1)FrCCNoOCFCrc)cBcc1))C2
add r at position 10,C62)nc]=3+rSO53Bl-4n1)FrCCNoOCFCrc)cBcc1))C2
remove ) from position 34,C62)nc]=3+rSO53Bl-4n1)FrCCNoOCFCrccBcc1))C2
remove r from position 10,C62)nc]=3+SO53Bl-4n1)FrCCNoOCFCrccBcc1))C2
add n at position 8,C62)nc]=n3+SO53Bl-4n1)FrCCNoOCFCrccBcc1))C2
remove B from position 35,C62)nc]=n3+SO53Bl-4n1)FrCCNoOCFCrcccc1))C2
remove F from position 22,C62)nc]=n3+SO53Bl-4n1)rCCNoOCFCrcccc1))C2
replace 5 at position 13 with 3,C62)nc]=n3+SO33Bl-4n1)rCCNoOCFCrcccc1))C2
add - at position 11,C62)nc]=n3+-SO33Bl-4n1)rCCNoOCFCrcccc1))C2
replace r at position 32 with (,C62)nc]=n3+-SO33Bl-4n1)rCCNoOCFC(cccc1))C2
remove r from position 23,C62)nc]=n3+-SO33Bl-4n1)CCNoOCFC(cccc1))C2
replace F at position 29 with /,C62)nc]=n3+-SO33Bl-4n1)CCNoOC/C(cccc1))C2
remove S from position 12,C62)nc]=n3+-O33Bl-4n1)CCNoOC/C(cccc1))C2
add C at position 12,C62)nc]=n3+-CO33Bl-4n1)CCNoOC/C(cccc1))C2
add I at position 4,C62)Inc]=n3+-CO33Bl-4n1)CCNoOC/C(cccc1))C2
replace I at position 4 with (,C62)(nc]=n3+-CO33Bl-4n1)CCNoOC/C(cccc1))C2
replace 4 at position 20 with ],C62)(nc]=n3+-CO33Bl-]n1)CCNoOC/C(cccc1))C2
add S at position 5,C62)(Snc]=n3+-CO33Bl-]n1)CCNoOC/C(cccc1))C2
replace 2 at position 2 with I,C6I)(Snc]=n3+-CO33Bl-]n1)CCNoOC/C(cccc1))C2
add c at position 3,C6Ic)(Snc]=n3+-CO33Bl-]n1)CCNoOC/C(cccc1))C2
replace ) at position 4 with c,C6Icc(Snc]=n3+-CO33Bl-]n1)CCNoOC/C(cccc1))C2
replace l at position 20 with ),C6Icc(Snc]=n3+-CO33B)-]n1)CCNoOC/C(cccc1))C2
add 2 at position 17,C6Icc(Snc]=n3+-CO233B)-]n1)CCNoOC/C(cccc1))C2
add B at position 10,C6Icc(Snc]B=n3+-CO233B)-]n1)CCNoOC/C(cccc1))C2
remove S from position 6,C6Icc(nc]B=n3+-CO233B)-]n1)CCNoOC/C(cccc1))C2
replace O at position 16 with ),C6Icc(nc]B=n3+-C)233B)-]n1)CCNoOC/C(cccc1))C2
replace n at position 24 with 2,C6Icc(nc]B=n3+-C)233B)-]21)CCNoOC/C(cccc1))C2
replace n at position 6 with c,C6Icc(cc]B=n3+-C)233B)-]21)CCNoOC/C(cccc1))C2
add N at position 7,C6Icc(cNc]B=n3+-C)233B)-]21)CCNoOC/C(cccc1))C2
remove = from position 11,C6Icc(cNc]Bn3+-C)233B)-]21)CCNoOC/C(cccc1))C2
replace 3 at position 18 with O,C6Icc(cNc]Bn3+-C)2O3B)-]21)CCNoOC/C(cccc1))C2
add - at position 33,C6Icc(cNc]Bn3+-C)2O3B)-]21)CCNoOC-/C(cccc1))C2
replace B at position 10 with 3,C6Icc(cNc]3n3+-C)2O3B)-]21)CCNoOC-/C(cccc1))C2
replace o at position 30 with (,C6Icc(cNc]3n3+-C)2O3B)-]21)CCN(OC-/C(cccc1))C2
replace ( at position 30 with =,C6Icc(cNc]3n3+-C)2O3B)-]21)CCN=OC-/C(cccc1))C2
add c at position 37,C6Icc(cNc]3n3+-C)2O3B)-]21)CCN=OC-/C(ccccc1))C2
add n at position 22,C6Icc(cNc]3n3+-C)2O3B)n-]21)CCN=OC-/C(ccccc1))C2
replace I at position 2 with 1,C61cc(cNc]3n3+-C)2O3B)n-]21)CCN=OC-/C(ccccc1))C2
remove ] from position 9,C61cc(cNc3n3+-C)2O3B)n-]21)CCN=OC-/C(ccccc1))C2
replace 3 at position 11 with c,C61cc(cNc3nc+-C)2O3B)n-]21)CCN=OC-/C(ccccc1))C2
remove O from position 17,C61cc(cNc3nc+-C)23B)n-]21)CCN=OC-/C(ccccc1))C2
add - at position 29,C61cc(cNc3nc+-C)23B)n-]21)CCN-=OC-/C(ccccc1))C2
remove - from position 33,C61cc(cNc3nc+-C)23B)n-]21)CCN-=OC/C(ccccc1))C2
remove ] from position 22,C61cc(cNc3nc+-C)23B)n-21)CCN-=OC/C(ccccc1))C2
remove - from position 13,C61cc(cNc3nc+C)23B)n-21)CCN-=OC/C(ccccc1))C2
remove / from position 31,C61cc(cNc3nc+C)23B)n-21)CCN-=OCC(ccccc1))C2
add n at position 3,C61ncc(cNc3nc+C)23B)n-21)CCN-=OCC(ccccc1))C2
add + at position 42,C61ncc(cNc3nc+C)23B)n-21)CCN-=OCC(ccccc1))+C2
remove - from position 21,C61ncc(cNc3nc+C)23B)n21)CCN-=OCC(ccccc1))+C2
add C at position 32,C61ncc(cNc3nc+C)23B)n21)CCN-=OCCC(ccccc1))+C2
add c at position 33,C61ncc(cNc3nc+C)23B)n21)CCN-=OCCCc(ccccc1))+C2
add c at position 12,C61ncc(cNc3ncc+C)23B)n21)CCN-=OCCCc(ccccc1))+C2
replace 2 at position 17 with s,C61ncc(cNc3ncc+C)s3B)n21)CCN-=OCCCc(ccccc1))+C2
remove B from position 19,C61ncc(cNc3ncc+C)s3)n21)CCN-=OCCCc(ccccc1))+C2
add 1 at position 35,C61ncc(cNc3ncc+C)s3)n21)CCN-=OCCCc(1ccccc1))+C2
add c at position 2,C6c1ncc(cNc3ncc+C)s3)n21)CCN-=OCCCc(1ccccc1))+C2
remove ) from position 43,C6c1ncc(cNc3ncc+C)s3)n21)CCN-=OCCCc(1ccccc1)+C2
add ( at position 29,C6c1ncc(cNc3ncc+C)s3)n21)CCN-(=OCCCc(1ccccc1)+C2
replace + at position 15 with (,C6c1ncc(cNc3ncc(C)s3)n21)CCN-(=OCCCc(1ccccc1)+C2
replace C at position 32 with ),C6c1ncc(cNc3ncc(C)s3)n21)CCN-(=O)CCc(1ccccc1)+C2
remove + from position 45,C6c1ncc(cNc3ncc(C)s3)n21)CCN-(=O)CCc(1ccccc1)C2
add ( at position 9,C6c1ncc(c(Nc3ncc(C)s3)n21)CCN-(=O)CCc(1ccccc1)C2
add C at position 30,C6c1ncc(c(Nc3ncc(C)s3)n21)CCN-C(=O)CCc(1ccccc1)C2
remove 6 from position 1,Cc1ncc(c(Nc3ncc(C)s3)n21)CCN-C(=O)CCc(1ccccc1)C2
remove 2 from position 22,Cc1ncc(c(Nc3ncc(C)s3)n1)CCN-C(=O)CCc(1ccccc1)C2
add 2 at position 5,Cc1nc2c(c(Nc3ncc(C)s3)n1)CCN-C(=O)CCc(1ccccc1)C2
remove ( from position 37,Cc1nc2c(c(Nc3ncc(C)s3)n1)CCN-C(=O)CCc1ccccc1)C2
replace - at position 28 with (,Cc1nc2c(c(Nc3ncc(C)s3)n1)CCN(C(=O)CCc1ccccc1)C2
final: Cc1nc2c(c(Nc3ncc(C)s3)n1)CCN(C(=O)CCc1ccccc1)C2,Cc1nc2c(c(Nc3ncc(C)s3)n1)CCN(C(=O)CCc1ccccc1)C2
