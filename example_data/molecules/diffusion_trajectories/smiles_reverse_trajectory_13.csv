log,state
initialize: ,
add o at position 0,o
add l at position 1,ol
replace o at position 0 with <PERSON>,Sl
remove S from position 0,l
add I at position 1,lI
remove l from position 0,I
add 6 at position 0,6I
replace I at position 1 with 5,65
remove 6 from position 0,5
replace 5 at position 0 with r,r
add S at position 1,rS
remove r from position 0,S
add - at position 1,S-
replace - at position 1 with ],S]
add 4 at position 1,S4]
remove ] from position 2,S4
remove S from position 0,4
add r at position 0,r4
replace r at position 0 with /,/4
add ) at position 2,/4)
replace / at position 0 with 7,74)
add H at position 2,74H)
add o at position 0,o74H)
remove ) from position 4,o74H
add 5 at position 4,o74H5
replace 4 at position 2 with ),o7)H5
replace o at position 0 with 1,17)H5
add 7 at position 3,17)7H5
add n at position 2,17n)7H5
remove 1 from position 0,7n)7H5
remove 7 from position 0,n)7H5
replace 7 at position 2 with 3,n)3H5
add r at position 0,rn)3H5
remove H from position 4,rn)35
replace 3 at position 3 with 4,rn)45
remove 5 from position 4,rn)4
add B at position 4,rn)4B
replace ) at position 2 with 2,rn24B
add c at position 4,rn24cB
add o at position 2,rno24cB
add 6 at position 5,rno246cB
replace 4 at position 4 with C,rno2C6cB
replace 6 at position 5 with @,rno2C@cB
remove o from position 2,rn2C@cB
remove B from position 6,rn2C@c
add = at position 0,=rn2C@c
replace @ at position 5 with r,=rn2Crc
add C at position 0,C=rn2Crc
remove c from position 7,C=rn2Cr
add # at position 5,C=rn2#Cr
add o at position 5,C=rn2o#Cr
add 7 at position 2,C=7rn2o#Cr
replace 7 at position 2 with @,C=@rn2o#Cr
add + at position 10,C=@rn2o#Cr+
add 4 at position 4,C=@r4n2o#Cr+
add N at position 1,CN=@r4n2o#Cr+
replace = at position 2 with 3,CN3@r4n2o#Cr+
replace 2 at position 7 with r,CN3@r4nro#Cr+
add 4 at position 5,CN3@r44nro#Cr+
add s at position 9,CN3@r44nrso#Cr+
add F at position 9,CN3@r44nrFso#Cr+
add ) at position 12,CN3@r44nrFso)#Cr+
add = at position 8,CN3@r44n=rFso)#Cr+
replace = at position 8 with H,CN3@r44nHrFso)#Cr+
replace + at position 17 with ),CN3@r44nHrFso)#Cr)
add B at position 18,CN3@r44nHrFso)#Cr)B
add c at position 9,CN3@r44nHcrFso)#Cr)B
replace H at position 8 with 4,CN3@r44n4crFso)#Cr)B
replace s at position 12 with 7,CN3@r44n4crF7o)#Cr)B
add S at position 6,CN3@r4S4n4crF7o)#Cr)B
remove 7 from position 13,CN3@r4S4n4crFo)#Cr)B
remove r from position 4,CN3@4S4n4crFo)#Cr)B
remove N from position 1,C3@4S4n4crFo)#Cr)B
add r at position 5,C3@4Sr4n4crFo)#Cr)B
remove ) from position 17,C3@4Sr4n4crFo)#CrB
remove r from position 5,C3@4S4n4crFo)#CrB
add c at position 4,C3@4cS4n4crFo)#CrB
remove B from position 17,C3@4cS4n4crFo)#Cr
remove F from position 11,C3@4cS4n4cro)#Cr
replace 4 at position 6 with l,C3@4cSln4cro)#Cr
add 3 at position 5,C3@4c3Sln4cro)#Cr
replace r at position 16 with 1,C3@4c3Sln4cro)#C1
remove r from position 11,C3@4c3Sln4co)#C1
replace C at position 14 with o,C3@4c3Sln4co)#o1
remove S from position 6,C3@4c3ln4co)#o1
add C at position 6,C3@4c3Cln4co)#o1
add H at position 2,C3H@4c3Cln4co)#o1
replace H at position 2 with n,C3n@4c3Cln4co)#o1
replace 4 at position 10 with o,C3n@4c3Clnoco)#o1
add S at position 2,C3Sn@4c3Clnoco)#o1
replace 3 at position 1 with +,C+Sn@4c3Clnoco)#o1
add H at position 1,CH+Sn@4c3Clnoco)#o1
replace + at position 2 with #,CH#Sn@4c3Clnoco)#o1
replace l at position 10 with C,CH#Sn@4c3CCnoco)#o1
add O at position 8,CH#Sn@4cO3CCnoco)#o1
add = at position 5,CH#Sn=@4cO3CCnoco)#o1
remove S from position 3,CH#n=@4cO3CCnoco)#o1
replace O at position 8 with (,CH#n=@4c(3CCnoco)#o1
replace n at position 12 with ],CH#n=@4c(3CC]oco)#o1
replace n at position 3 with ],CH#]=@4c(3CC]oco)#o1
add C at position 3,CH#C]=@4c(3CC]oco)#o1
remove = from position 5,CH#C]@4c(3CC]oco)#o1
replace 3 at position 9 with O,CH#C]@4c(OCC]oco)#o1
add - at position 16,CH#C]@4c(OCC]oco-)#o1
replace @ at position 5 with S,CH#C]S4c(OCC]oco-)#o1
replace o at position 15 with (,CH#C]S4c(OCC]oc(-)#o1
replace ( at position 15 with C,CH#C]S4c(OCC]ocC-)#o1
add ) at position 18,CH#C]S4c(OCC]ocC-))#o1
add S at position 11,CH#C]S4c(OCSC]ocC-))#o1
replace H at position 1 with O,CO#C]S4c(OCSC]ocC-))#o1
remove ] from position 4,CO#CS4c(OCSC]ocC-))#o1
replace 4 at position 5 with 1,CO#CS1c(OCSC]ocC-))#o1
remove O from position 8,CO#CS1c(CSC]ocC-))#o1
add 2 at position 14,CO#CS1c(CSC]oc2C-))#o1
remove - from position 16,CO#CS1c(CSC]oc2C))#o1
remove ] from position 11,CO#CS1c(CSCoc2C))#o1
add ) at position 4,CO#C)S1c(CSCoc2C))#o1
add H at position 2,COH#C)S1c(CSCoc2C))#o1
remove H from position 2,CO#C)S1c(CSCoc2C))#o1
add + at position 12,CO#C)S1c(CSC+oc2C))#o1
add H at position 22,CO#C)S1c(CSC+oc2C))#o1H
replace # at position 2 with (,CO(C)S1c(CSC+oc2C))#o1H
add C at position 0,CCO(C)S1c(CSC+oc2C))#o1H
replace o at position 14 with r,CCO(C)S1c(CSC+rc2C))#o1H
add n at position 20,CCO(C)S1c(CSC+rc2C))n#o1H
add 2 at position 11,CCO(C)S1c(C2SC+rc2C))n#o1H
replace O at position 2 with C,CCC(C)S1c(C2SC+rc2C))n#o1H
add n at position 8,CCC(C)S1nc(C2SC+rc2C))n#o1H
add c at position 16,CCC(C)S1nc(C2SC+crc2C))n#o1H
add n at position 19,CCC(C)S1nc(C2SC+crcn2C))n#o1H
remove r from position 17,CCC(C)S1nc(C2SC+ccn2C))n#o1H
add c at position 7,CCC(C)Sc1nc(C2SC+ccn2C))n#o1H
remove C from position 0,CC(C)Sc1nc(C2SC+ccn2C))n#o1H
remove S from position 5,CC(C)c1nc(C2SC+ccn2C))n#o1H
add c at position 14,CC(C)c1nc(C2SCc+ccn2C))n#o1H
add n at position 19,CC(C)c1nc(C2SCc+ccnn2C))n#o1H
remove # from position 25,CC(C)c1nc(C2SCc+ccnn2C))no1H
remove 2 from position 11,CC(C)c1nc(CSCc+ccnn2C))no1H
remove ) from position 22,CC(C)c1nc(CSCc+ccnn2C)no1H
remove H from position 25,CC(C)c1nc(CSCc+ccnn2C)no1
replace + at position 14 with 2,CC(C)c1nc(CSCc2ccnn2C)no1
final: CC(C)c1nc(CSCc2ccnn2C)no1,CC(C)c1nc(CSCc2ccnn2C)no1
