log,state
initialize: CC(C)(C)c1ccc(C(=O)N[C@H]2CCN3CCCc4cccc2c43)cc1,CC(C)(C)c1ccc(C(=O)N[C@H]2CCN3CCCc4cccc2c43)cc1
replace N at position 28 with +,CC(C)(C)c1ccc(C(=O)N[C@H]2CC+3CCCc4cccc2c43)cc1
add ( at position 37,CC(C)(C)c1ccc(C(=O)N[C@H]2CC+3CCCc4cc(cc2c43)cc1
remove ( from position 5,CC(C)C)c1ccc(C(=O)N[C@H]2CC+3CCCc4cc(cc2c43)cc1
add 2 at position 22,CC(C)C)c1ccc(C(=O)N[C@2H]2CC+3CCCc4cc(cc2c43)cc1
add 6 at position 1,C6C(C)C)c1ccc(C(=O)N[C@2H]2CC+3CCCc4cc(cc2c43)cc1
remove 3 from position 30,C6C(C)C)c1ccc(C(=O)N[C@2H]2CC+CCCc4cc(cc2c43)cc1
remove 1 from position 9,C6C(C)C)cccc(C(=O)N[C@2H]2CC+CCCc4cc(cc2c43)cc1
add + at position 45,C6C(C)C)cccc(C(=O)N[C@2H]2CC+CCCc4cc(cc2c43)c+c1
replace c at position 32 with B,C6C(C)C)cccc(C(=O)N[C@2H]2CC+CCCB4cc(cc2c43)c+c1
replace = at position 15 with ),C6C(C)C)cccc(C()O)N[C@2H]2CC+CCCB4cc(cc2c43)c+c1
remove C from position 29,C6C(C)C)cccc(C()O)N[C@2H]2CC+CCB4cc(cc2c43)c+c1
add ) at position 43,C6C(C)C)cccc(C()O)N[C@2H]2CC+CCB4cc(cc2c43))c+c1
remove C from position 2,C6(C)C)cccc(C()O)N[C@2H]2CC+CCB4cc(cc2c43))c+c1
remove c from position 35,C6(C)C)cccc(C()O)N[C@2H]2CC+CCB4cc(c2c43))c+c1
add B at position 19,C6(C)C)cccc(C()O)N[BC@2H]2CC+CCB4cc(c2c43))c+c1
replace N at position 17 with 2,C6(C)C)cccc(C()O)2[BC@2H]2CC+CCB4cc(c2c43))c+c1
remove C from position 12,C6(C)C)cccc(()O)2[BC@2H]2CC+CCB4cc(c2c43))c+c1
remove c from position 33,C6(C)C)cccc(()O)2[BC@2H]2CC+CCB4c(c2c43))c+c1
remove c from position 32,C6(C)C)cccc(()O)2[BC@2H]2CC+CCB4(c2c43))c+c1
add - at position 21,C6(C)C)cccc(()O)2[BC@-2H]2CC+CCB4(c2c43))c+c1
remove + from position 42,C6(C)C)cccc(()O)2[BC@-2H]2CC+CCB4(c2c43))cc1
remove C from position 3,C6()C)cccc(()O)2[BC@-2H]2CC+CCB4(c2c43))cc1
add / at position 31,C6()C)cccc(()O)2[BC@-2H]2CC+CCB/4(c2c43))cc1
add - at position 13,C6()C)cccc(()-O)2[BC@-2H]2CC+CCB/4(c2c43))cc1
add ] at position 22,C6()C)cccc(()-O)2[BC@-]2H]2CC+CCB/4(c2c43))cc1
add - at position 33,C6()C)cccc(()-O)2[BC@-]2H]2CC+CCB-/4(c2c43))cc1
remove + from position 29,C6()C)cccc(()-O)2[BC@-]2H]2CCCCB-/4(c2c43))cc1
add O at position 17,C6()C)cccc(()-O)2O[BC@-]2H]2CCCCB-/4(c2c43))cc1
replace ( at position 11 with 4,C6()C)cccc(4)-O)2O[BC@-]2H]2CCCCB-/4(c2c43))cc1
add ] at position 9,C6()C)ccc]c(4)-O)2O[BC@-]2H]2CCCCB-/4(c2c43))cc1
replace ( at position 2 with I,C6I)C)ccc]c(4)-O)2O[BC@-]2H]2CCCCB-/4(c2c43))cc1
remove @ from position 22,C6I)C)ccc]c(4)-O)2O[BC-]2H]2CCCCB-/4(c2c43))cc1
remove c from position 37,C6I)C)ccc]c(4)-O)2O[BC-]2H]2CCCCB-/4(2c43))cc1
replace C at position 30 with (,C6I)C)ccc]c(4)-O)2O[BC-]2H]2CC(CB-/4(2c43))cc1
replace ( at position 30 with o,C6I)C)ccc]c(4)-O)2O[BC-]2H]2CCoCB-/4(2c43))cc1
replace c at position 10 with @,C6I)C)ccc]@(4)-O)2O[BC-]2H]2CCoCB-/4(2c43))cc1
remove - from position 33,C6I)C)ccc]@(4)-O)2O[BC-]2H]2CCoCB/4(2c43))cc1
replace O at position 18 with 3,C6I)C)ccc]@(4)-O)23[BC-]2H]2CCoCB/4(2c43))cc1
add = at position 11,C6I)C)ccc]@=(4)-O)23[BC-]2H]2CCoCB/4(2c43))cc1
remove c from position 7,C6I)C)cc]@=(4)-O)23[BC-]2H]2CCoCB/4(2c43))cc1
replace c at position 6 with n,C6I)C)nc]@=(4)-O)23[BC-]2H]2CCoCB/4(2c43))cc1
replace 2 at position 24 with n,C6I)C)nc]@=(4)-O)23[BC-]nH]2CCoCB/4(2c43))cc1
replace ) at position 16 with O,C6I)C)nc]@=(4)-OO23[BC-]nH]2CCoCB/4(2c43))cc1
add S at position 6,C6I)C)Snc]@=(4)-OO23[BC-]nH]2CCoCB/4(2c43))cc1
remove @ from position 10,C6I)C)Snc]=(4)-OO23[BC-]nH]2CCoCB/4(2c43))cc1
remove 2 from position 17,C6I)C)Snc]=(4)-OO3[BC-]nH]2CCoCB/4(2c43))cc1
replace C at position 20 with l,C6I)C)Snc]=(4)-OO3[Bl-]nH]2CCoCB/4(2c43))cc1
replace C at position 4 with ),C6I)))Snc]=(4)-OO3[Bl-]nH]2CCoCB/4(2c43))cc1
remove ) from position 3,C6I))Snc]=(4)-OO3[Bl-]nH]2CCoCB/4(2c43))cc1
replace I at position 2 with 2,C62))Snc]=(4)-OO3[Bl-]nH]2CCoCB/4(2c43))cc1
remove S from position 5,C62))nc]=(4)-OO3[Bl-]nH]2CCoCB/4(2c43))cc1
replace ] at position 20 with 4,C62))nc]=(4)-OO3[Bl-4nH]2CCoCB/4(2c43))cc1
replace ) at position 4 with I,C62)Inc]=(4)-OO3[Bl-4nH]2CCoCB/4(2c43))cc1
remove I from position 4,C62)nc]=(4)-OO3[Bl-4nH]2CCoCB/4(2c43))cc1
remove O from position 12,C62)nc]=(4)-O3[Bl-4nH]2CCoCB/4(2c43))cc1
add S at position 12,C62)nc]=(4)-SO3[Bl-4nH]2CCoCB/4(2c43))cc1
replace / at position 29 with F,C62)nc]=(4)-SO3[Bl-4nH]2CCoCBF4(2c43))cc1
add r at position 23,C62)nc]=(4)-SO3[Bl-4nH]r2CCoCBF4(2c43))cc1
replace ( at position 32 with r,C62)nc]=(4)-SO3[Bl-4nH]r2CCoCBF4r2c43))cc1
remove - from position 11,C62)nc]=(4)SO3[Bl-4nH]r2CCoCBF4r2c43))cc1
replace 3 at position 13 with 5,C62)nc]=(4)SO5[Bl-4nH]r2CCoCBF4r2c43))cc1
add F at position 22,C62)nc]=(4)SO5[Bl-4nH]Fr2CCoCBF4r2c43))cc1
add B at position 35,C62)nc]=(4)SO5[Bl-4nH]Fr2CCoCBF4r2cB43))cc1
remove ( from position 8,C62)nc]=4)SO5[Bl-4nH]Fr2CCoCBF4r2cB43))cc1
add r at position 10,C62)nc]=4)rSO5[Bl-4nH]Fr2CCoCBF4r2cB43))cc1
add ) at position 34,C62)nc]=4)rSO5[Bl-4nH]Fr2CCoCBF4r2)cB43))cc1
remove r from position 10,C62)nc]=4)SO5[Bl-4nH]Fr2CCoCBF4r2)cB43))cc1
add ( at position 43,C62)nc]=4)SO5[Bl-4nH]Fr2CCoCBF4r2)cB43))cc1(
remove ) from position 9,C62)nc]=4SO5[Bl-4nH]Fr2CCoCBF4r2)cB43))cc1(
replace 2 at position 2 with @,C6@)nc]=4SO5[Bl-4nH]Fr2CCoCBF4r2)cB43))cc1(
remove F from position 20,C6@)nc]=4SO5[Bl-4nH]r2CCoCBF4r2)cB43))cc1(
add r at position 24,C6@)nc]=4SO5[Bl-4nH]r2CCroCBF4r2)cB43))cc1(
replace n at position 17 with F,C6@)nc]=4SO5[Bl-4FH]r2CCroCBF4r2)cB43))cc1(
remove ] from position 19,C6@)nc]=4SO5[Bl-4FHr2CCroCBF4r2)cB43))cc1(
remove ) from position 36,C6@)nc]=4SO5[Bl-4FHr2CCroCBF4r2)cB43)cc1(
replace 4 at position 34 with ),C6@)nc]=4SO5[Bl-4FHr2CCroCBF4r2)cB)3)cc1(
replace F at position 17 with =,C6@)nc]=4SO5[Bl-4=Hr2CCroCBF4r2)cB)3)cc1(
remove ) from position 36,C6@)nc]=4SO5[Bl-4=Hr2CCroCBF4r2)cB)3cc1(
remove = from position 7,C6@)nc]4SO5[Bl-4=Hr2CCroCBF4r2)cB)3cc1(
remove H from position 17,C6@)nc]4SO5[Bl-4=r2CCroCBF4r2)cB)3cc1(
add 7 at position 37,C6@)nc]4SO5[Bl-4=r2CCroCBF4r2)cB)3cc17(
add 2 at position 28,C6@)nc]4SO5[Bl-4=r2CCroCBF4r22)cB)3cc17(
replace 5 at position 10 with =,C6@)nc]4SO=[Bl-4=r2CCroCBF4r22)cB)3cc17(
remove n from position 4,C6@)c]4SO=[Bl-4=r2CCroCBF4r22)cB)3cc17(
remove C from position 19,C6@)c]4SO=[Bl-4=r2CroCBF4r22)cB)3cc17(
remove B from position 30,C6@)c]4SO=[Bl-4=r2CroCBF4r22)c)3cc17(
add / at position 30,C6@)c]4SO=[Bl-4=r2CroCBF4r22)c/)3cc17(
remove F from position 23,C6@)c]4SO=[Bl-4=r2CroCB4r22)c/)3cc17(
remove B from position 22,C6@)c]4SO=[Bl-4=r2CroC4r22)c/)3cc17(
add r at position 33,C6@)c]4SO=[Bl-4=r2CroC4r22)c/)3ccr17(
remove 3 from position 30,C6@)c]4SO=[Bl-4=r2CroC4r22)c/)ccr17(
remove ) from position 29,C6@)c]4SO=[Bl-4=r2CroC4r22)c/ccr17(
remove 4 from position 6,C6@)c]SO=[Bl-4=r2CroC4r22)c/ccr17(
add ) at position 30,C6@)c]SO=[Bl-4=r2CroC4r22)c/cc)r17(
replace 4 at position 21 with 7,C6@)c]SO=[Bl-4=r2CroC7r22)c/cc)r17(
replace r at position 18 with 4,C6@)c]SO=[Bl-4=r2C4oC7r22)c/cc)r17(
remove 7 from position 21,C6@)c]SO=[Bl-4=r2C4oCr22)c/cc)r17(
remove 4 from position 18,C6@)c]SO=[Bl-4=r2CoCr22)c/cc)r17(
remove ( from position 32,C6@)c]SO=[Bl-4=r2CoCr22)c/cc)r17
replace C at position 17 with ),C6@)c]SO=[Bl-4=r2)oCr22)c/cc)r17
remove 4 from position 13,C6@)c]SO=[Bl-=r2)oCr22)c/cc)r17
remove B from position 10,C6@)c]SO=[l-=r2)oCr22)c/cc)r17
replace 2 at position 14 with 4,C6@)c]SO=[l-=r4)oCr22)c/cc)r17
add H at position 16,C6@)c]SO=[l-=r4)HoCr22)c/cc)r17
remove ) from position 3,C6@c]SO=[l-=r4)HoCr22)c/cc)r17
replace [ at position 8 with 6,C6@c]SO=6l-=r4)HoCr22)c/cc)r17
add 7 at position 0,7C6@c]SO=6l-=r4)HoCr22)c/cc)r17
add 1 at position 2,7C16@c]SO=6l-=r4)HoCr22)c/cc)r17
remove r from position 20,7C16@c]SO=6l-=r4)HoC22)c/cc)r17
remove = from position 13,7C16@c]SO=6l-r4)HoC22)c/cc)r17
replace 7 at position 29 with ),7C16@c]SO=6l-r4)HoC22)c/cc)r1)
replace = at position 9 with 3,7C16@c]SO36l-r4)HoC22)c/cc)r1)
remove ) from position 29,7C16@c]SO36l-r4)HoC22)c/cc)r1
remove l from position 11,7C16@c]SO36-r4)HoC22)c/cc)r1
remove ) from position 20,7C16@c]SO36-r4)HoC22c/cc)r1
replace 7 at position 0 with S,SC16@c]SO36-r4)HoC22c/cc)r1
remove 2 from position 18,SC16@c]SO36-r4)HoC2c/cc)r1
add B at position 15,SC16@c]SO36-r4)BHoC2c/cc)r1
remove / from position 21,SC16@c]SO36-r4)BHoC2ccc)r1
replace @ at position 4 with r,SC16rc]SO36-r4)BHoC2ccc)r1
remove 2 from position 19,SC16rc]SO36-r4)BHoCccc)r1
add s at position 17,SC16rc]SO36-r4)BHsoCccc)r1
replace c at position 22 with /,SC16rc]SO36-r4)BHsoCcc/)r1
replace r at position 24 with C,SC16rc]SO36-r4)BHsoCcc/)C1
remove r from position 12,SC16rc]SO36-4)BHsoCcc/)C1
replace 6 at position 10 with -,SC16rc]SO3--4)BHsoCcc/)C1
remove o from position 17,SC16rc]SO3--4)BHsCcc/)C1
remove c from position 19,SC16rc]SO3--4)BHsCc/)C1
remove B from position 14,SC16rc]SO3--4)HsCc/)C1
add r at position 2,SCr16rc]SO3--4)HsCc/)C1
remove C from position 21,SCr16rc]SO3--4)HsCc/)1
remove C from position 1,Sr16rc]SO3--4)HsCc/)1
remove 1 from position 2,Sr6rc]SO3--4)HsCc/)1
remove 1 from position 19,Sr6rc]SO3--4)HsCc/)
replace 4 at position 11 with F,Sr6rc]SO3--F)HsCc/)
remove ) from position 12,Sr6rc]SO3--FHsCc/)
remove r from position 1,S6rc]SO3--FHsCc/)
add l at position 2,S6lrc]SO3--FHsCc/)
remove - from position 10,S6lrc]SO3-FHsCc/)
add S at position 7,S6lrc]SSO3-FHsCc/)
replace S at position 0 with o,o6lrc]SSO3-FHsCc/)
remove F from position 11,o6lrc]SSO3-HsCc/)
remove l from position 2,o6rc]SSO3-HsCc/)
remove S from position 6,o6rc]SO3-HsCc/)
replace / at position 13 with N,o6rc]SO3-HsCcN)
remove C from position 11,o6rc]SO3-HscN)
add O at position 2,o6Orc]SO3-HscN)
replace H at position 10 with ],o6Orc]SO3-]scN)
add 6 at position 14,o6Orc]SO3-]scN6)
remove s from position 11,o6Orc]SO3-]cN6)
remove S from position 6,o6Orc]O3-]cN6)
replace r at position 3 with 4,o6O4c]O3-]cN6)
add H at position 5,o6O4cH]O3-]cN6)
replace 6 at position 1 with H,oHO4cH]O3-]cN6)
remove 6 from position 13,oHO4cH]O3-]cN)
add H at position 9,oHO4cH]O3H-]cN)
remove O from position 2,oH4cH]O3H-]cN)
remove ] from position 5,oH4cHO3H-]cN)
replace H at position 4 with [,oH4c[O3H-]cN)
remove 4 from position 2,oHc[O3H-]cN)
remove c from position 2,oH[O3H-]cN)
replace c at position 8 with S,oH[O3H-]SN)
remove O from position 3,oH[3H-]SN)
remove [ from position 2,oH3H-]SN)
remove S from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
