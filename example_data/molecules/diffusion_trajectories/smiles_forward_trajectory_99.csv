log,state
initialize: C[NH+](C)CCSc1ccc(NC(=O)C2CC2)nn1,C[NH+](C)CCSc1ccc(NC(=O)C2CC2)nn1
replace 2 at position 28 with +,C[NH+](C)CCSc1ccc(NC(=O)C2CC+)nn1
add O at position 2,C[ONH+](C)CCSc1ccc(NC(=O)C2CC+)nn1
remove ] from position 6,C[ONH+(C)CCSc1ccc(NC(=O)C2CC+)nn1
replace c at position 15 with #,C[ONH+(C)CCSc1c#c(NC(=O)C2CC+)nn1
add 6 at position 1,C6[ONH+(C)CCSc1c#c(NC(=O)C2CC+)nn1
remove ) from position 30,C6[ONH+(C)CCSc1c#c(NC(=O)C2CC+nn1
remove ) from position 9,C6[ONH+(CCCSc1c#c(NC(=O)C2CC+nn1
add n at position 7,C6[ONH+n(CCCSc1c#c(NC(=O)C2CC+nn1
add o at position 31,C6[ONH+n(CCCSc1c#c(NC(=O)C2CC+non1
replace N at position 4 with S,C6[OSH+n(CCCSc1c#c(NC(=O)C2CC+non1
remove + from position 29,C6[OSH+n(CCCSc1c#c(NC(=O)C2CCnon1
add C at position 5,C6[OSCH+n(CCCSc1c#c(NC(=O)C2CCnon1
remove # from position 17,C6[OSCH+n(CCCSc1cc(NC(=O)C2CCnon1
replace S at position 4 with N,C6[ONCH+n(CCCSc1cc(NC(=O)C2CCnon1
remove = from position 22,C6[ONCH+n(CCCSc1cc(NC(O)C2CCnon1
remove C from position 12,C6[ONCH+n(CCSc1cc(NC(O)C2CCnon1
remove c from position 16,C6[ONCH+n(CCSc1c(NC(O)C2CCnon1
remove ( from position 16,C6[ONCH+n(CCSc1cNC(O)C2CCnon1
add - at position 10,C6[ONCH+n(-CCSc1cNC(O)C2CCnon1
remove ) from position 21,C6[ONCH+n(-CCSc1cNC(OC2CCnon1
remove 6 from position 1,C[ONCH+n(-CCSc1cNC(OC2CCnon1
add / at position 15,C[ONCH+n(-CCSc1/cNC(OC2CCnon1
add - at position 6,C[ONCH-+n(-CCSc1/cNC(OC2CCnon1
add ] at position 11,C[ONCH-+n(-]CCSc1/cNC(OC2CCnon1
add O at position 9,C[ONCH-+nO(-]CCSc1/cNC(OC2CCnon1
remove O from position 2,C[NCH-+nO(-]CCSc1/cNC(OC2CCnon1
remove ( from position 21,C[NCH-+nO(-]CCSc1/cNCOC2CCnon1
replace - at position 5 with 4,C[NCH4+nO(-]CCSc1/cNCOC2CCnon1
add n at position 30,C[NCH4+nO(-]CCSc1/cNCOC2CCnon1n
add ( at position 29,C[NCH4+nO(-]CCSc1/cNCOC2CCnon(1n
remove C from position 22,C[NCH4+nO(-]CCSc1/cNCO2CCnon(1n
remove c from position 18,C[NCH4+nO(-]CCSc1/NCO2CCnon(1n
replace c at position 15 with (,C[NCH4+nO(-]CCS(1/NCO2CCnon(1n
replace ( at position 15 with o,C[NCH4+nO(-]CCSo1/NCO2CCnon(1n
replace 1 at position 28 with /,C[NCH4+nO(-]CCSo1/NCO2CCnon(/n
remove n from position 24,C[NCH4+nO(-]CCSo1/NCO2CCon(/n
remove S from position 14,C[NCH4+nO(-]CCo1/NCO2CCon(/n
remove C from position 3,C[NH4+nO(-]CCo1/NCO2CCon(/n
replace o at position 13 with =,C[NH4+nO(-]CC=1/NCO2CCon(/n
replace N at position 16 with +,C[NH4+nO(-]CC=1/+CO2CCon(/n
replace / at position 15 with =,C[NH4+nO(-]CC=1=+CO2CCon(/n
replace C at position 21 with 3,C[NH4+nO(-]CC=1=+CO2C3on(/n
remove C from position 11,C[NH4+nO(-]C=1=+CO2C3on(/n
add c at position 22,C[NH4+nO(-]C=1=+CO2C3ocn(/n
replace 2 at position 18 with s,C[NH4+nO(-]C=1=+COsC3ocn(/n
replace 4 at position 4 with o,C[NHo+nO(-]C=1=+COsC3ocn(/n
replace n at position 23 with 5,C[NHo+nO(-]C=1=+COsC3oc5(/n
replace = at position 14 with ],C[NHo+nO(-]C=1]+COsC3oc5(/n
add 1 at position 12,C[NHo+nO(-]C1=1]+COsC3oc5(/n
add c at position 9,C[NHo+nO(c-]C1=1]+COsC3oc5(/n
add l at position 16,C[NHo+nO(c-]C1=1l]+COsC3oc5(/n
add s at position 29,C[NHo+nO(c-]C1=1l]+COsC3oc5(/sn
remove - from position 10,C[NHo+nO(c]C1=1l]+COsC3oc5(/sn
remove 1 from position 14,C[NHo+nO(c]C1=l]+COsC3oc5(/sn
add n at position 8,C[NHo+nOn(c]C1=l]+COsC3oc5(/sn
add s at position 13,C[NHo+nOn(c]Cs1=l]+COsC3oc5(/sn
add 1 at position 25,C[NHo+nOn(c]Cs1=l]+COsC3o1c5(/sn
remove / from position 29,C[NHo+nOn(c]Cs1=l]+COsC3o1c5(sn
remove c from position 26,C[NHo+nOn(c]Cs1=l]+COsC3o15(sn
replace n at position 29 with o,C[NHo+nOn(c]Cs1=l]+COsC3o15(so
add S at position 28,C[NHo+nOn(c]Cs1=l]+COsC3o15(Sso
remove o from position 30,C[NHo+nOn(c]Cs1=l]+COsC3o15(Ss
remove ( from position 9,C[NHo+nOnc]Cs1=l]+COsC3o15(Ss
add F at position 11,C[NHo+nOnc]FCs1=l]+COsC3o15(Ss
add ] at position 29,C[NHo+nOnc]FCs1=l]+COsC3o15(S]s
replace = at position 15 with 5,C[NHo+nOnc]FCs15l]+COsC3o15(S]s
add I at position 3,C[NIHo+nOnc]FCs15l]+COsC3o15(S]s
add l at position 9,C[NIHo+nOlnc]FCs15l]+COsC3o15(S]s
remove + from position 20,C[NIHo+nOlnc]FCs15l]COsC3o15(S]s
add N at position 3,C[NNIHo+nOlnc]FCs15l]COsC3o15(S]s
add r at position 9,C[NNIHo+nrOlnc]FCs15l]COsC3o15(S]s
add 7 at position 27,C[NNIHo+nrOlnc]FCs15l]COsC37o15(S]s
remove c from position 13,C[NNIHo+nrOln]FCs15l]COsC37o15(S]s
replace C at position 24 with s,C[NNIHo+nrOln]FCs15l]COss37o15(S]s
replace 1 at position 17 with H,C[NNIHo+nrOln]FCsH5l]COss37o15(S]s
remove l from position 19,C[NNIHo+nrOln]FCsH5]COss37o15(S]s
remove r from position 9,C[NNIHo+nOln]FCsH5]COss37o15(S]s
replace H at position 5 with c,C[NNIco+nOln]FCsH5]COss37o15(S]s
remove 1 from position 26,C[NNIco+nOln]FCsH5]COss37o5(S]s
remove ] from position 18,C[NNIco+nOln]FCsH5COss37o5(S]s
remove N from position 3,C[NIco+nOln]FCsH5COss37o5(S]s
remove O from position 8,C[NIco+nln]FCsH5COss37o5(S]s
add l at position 22,C[NIco+nln]FCsH5COss37lo5(S]s
remove [ from position 1,CNIco+nln]FCsH5COss37lo5(S]s
remove ] from position 26,CNIco+nln]FCsH5COss37lo5(Ss
remove ( from position 24,CNIco+nln]FCsH5COss37lo5Ss
remove s from position 12,CNIco+nln]FCH5COss37lo5Ss
remove I from position 2,CNco+nln]FCH5COss37lo5Ss
remove F from position 9,CNco+nln]CH5COss37lo5Ss
remove S from position 21,CNco+nln]CH5COss37lo5s
replace n at position 5 with 7,CNco+7ln]CH5COss37lo5s
remove 7 from position 5,CNco+ln]CH5COss37lo5s
remove C from position 11,CNco+ln]CH5Oss37lo5s
remove O from position 11,CNco+ln]CH5ss37lo5s
add r at position 16,CNco+ln]CH5ss37lro5s
remove l from position 15,CNco+ln]CH5ss37ro5s
remove 7 from position 14,CNco+ln]CH5ss3ro5s
remove o from position 3,CNc+ln]CH5ss3ro5s
add ) at position 15,CNc+ln]CH5ss3ro)5s
replace s at position 10 with 6,CNc+ln]CH56s3ro)5s
replace 5 at position 9 with 4,CNc+ln]CH46s3ro)5s
remove 6 from position 10,CNc+ln]CH4s3ro)5s
remove 4 from position 9,CNc+ln]CHs3ro)5s
remove H from position 8,CNc+ln]Cs3ro)5s
add 5 at position 6,CNc+ln5]Cs3ro)5s
remove 3 from position 10,CNc+ln5]Csro)5s
remove N from position 1,Cc+ln5]Csro)5s
replace r at position 9 with H,Cc+ln5]CsHo)5s
remove s from position 13,Cc+ln5]CsHo)5
add - at position 5,Cc+ln-5]CsHo)5
replace 5 at position 6 with (,Cc+ln-(]CsHo)5
remove C from position 0,c+ln-(]CsHo)5
replace o at position 10 with -,c+ln-(]CsH-)5
replace s at position 8 with 6,c+ln-(]C6H-)5
remove 6 from position 8,c+ln-(]CH-)5
replace - at position 4 with 4,c+ln4(]CH-)5
remove - from position 9,c+ln4(]CH)5
add ) at position 8,c+ln4(]C)H)5
remove c from position 0,+ln4(]C)H)5
remove ] from position 5,+ln4(C)H)5
replace 4 at position 3 with /,+ln/(C)H)5
remove n from position 2,+l/(C)H)5
replace l at position 1 with -,+-/(C)H)5
remove ( from position 3,+-/C)H)5
remove ) from position 6,+-/C)H5
replace / at position 2 with -,+--C)H5
remove ) from position 4,+--CH5
remove H from position 4,+--C5
remove C from position 3,+--5
add r at position 0,r+--5
remove - from position 3,r+-5
replace r at position 0 with 5,5+-5
add 6 at position 1,56+-5
replace 5 at position 4 with [,56+-[
replace [ at position 4 with B,56+-B
replace 5 at position 0 with B,B6+-B
replace + at position 2 with @,B6@-B
replace 6 at position 1 with C,BC@-B
remove C from position 1,B@-B
remove B from position 0,@-B
replace - at position 1 with I,@IB
remove @ from position 0,IB
add 1 at position 0,1IB
replace B at position 2 with N,1IN
remove 1 from position 0,IN
remove I from position 0,N
remove N from position 0,
final: ,
