log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove <PERSON> from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add [ at position 6,oH3H-][N)
add [ at position 2,oH[3H-][N)
add 5 at position 3,oH[53H-][N)
replace [ at position 8 with O,oH[53H-]ON)
add c at position 2,oHc[53H-]ON)
add 4 at position 2,oH4c[53H-]ON)
replace [ at position 4 with H,oH4cH53H-]ON)
add ] at position 5,oH4cH]53H-]ON)
add O at position 2,oHO4cH]53H-]ON)
remove H from position 9,oHO4cH]53-]ON)
add 6 at position 13,oHO4cH]53-]ON6)
replace H at position 1 with =,o=O4cH]53-]ON6)
remove H from position 5,o=O4c]53-]ON6)
replace 4 at position 3 with r,o=Orc]53-]ON6)
add O at position 6,o=Orc]O53-]ON6)
add s at position 11,o=Orc]O53-]sON6)
remove 6 from position 14,o=Orc]O53-]sON)
replace ] at position 10 with H,o=Orc]O53-HsON)
remove O from position 2,o=rc]O53-HsON)
add C at position 11,o=rc]O53-HsCON)
replace N at position 13 with 1,o=rc]O53-HsCO1)
add S at position 6,o=rc]OS53-HsCO1)
add l at position 2,o=lrc]OS53-HsCO1)
add F at position 11,o=lrc]OS53-FHsCO1)
replace o at position 0 with S,S=lrc]OS53-FHsCO1)
remove S from position 7,S=lrc]O53-FHsCO1)
add 4 at position 10,S=lrc]O53-4FHsCO1)
remove l from position 2,S=rc]O53-4FHsCO1)
add r at position 1,Sr=rc]O53-4FHsCO1)
add ) at position 12,Sr=rc]O53-4F)HsCO1)
replace F at position 11 with 3,Sr=rc]O53-43)HsCO1)
add H at position 19,Sr=rc]O53-43)HsCO1)H
add 1 at position 2,Sr1=rc]O53-43)HsCO1)H
add C at position 1,SCr1=rc]O53-43)HsCO1)H
add C at position 21,SCr1=rc]O53-43)HsCO1)CH
remove r from position 2,SC1=rc]O53-43)HsCO1)CH
add B at position 14,SC1=rc]O53-43)BHsCO1)CH
add c at position 19,SC1=rc]O53-43)BHsCOc1)CH
add ( at position 17,SC1=rc]O53-43)BHs(COc1)CH
replace - at position 10 with 7,SC1=rc]O53743)BHs(COc1)CH
add ) at position 12,SC1=rc]O5374)3)BHs(COc1)CH
replace C at position 24 with r,SC1=rc]O5374)3)BHs(COc1)rH
replace 1 at position 22 with (,SC1=rc]O5374)3)BHs(COc()rH
remove s from position 17,SC1=rc]O5374)3)BH(COc()rH
add B at position 19,SC1=rc]O5374)3)BH(CBOc()rH
replace r at position 4 with @,SC1=@c]O5374)3)BH(CBOc()rH
add / at position 21,SC1=@c]O5374)3)BH(CBO/c()rH
remove B from position 15,SC1=@c]O5374)3)H(CBO/c()rH
add 2 at position 18,SC1=@c]O5374)3)H(C2BO/c()rH
replace S at position 0 with 7,7C1=@c]O5374)3)H(C2BO/c()rH
add [ at position 20,7C1=@c]O5374)3)H(C2B[O/c()rH
add 1 at position 11,7C1=@c]O53714)3)H(C2B[O/c()rH
add ) at position 29,7C1=@c]O53714)3)H(C2B[O/c()rH)
replace 3 at position 9 with 7,7C1=@c]O57714)3)H(C2B[O/c()rH)
replace ) at position 29 with 7,7C1=@c]O57714)3)H(C2B[O/c()rH7
add C at position 13,7C1=@c]O57714C)3)H(C2B[O/c()rH7
add ) at position 20,7C1=@c]O57714C)3)H(C)2B[O/c()rH7
remove 1 from position 2,7C=@c]O57714C)3)H(C)2B[O/c()rH7
remove 7 from position 0,C=@c]O57714C)3)H(C)2B[O/c()rH7
replace 7 at position 8 with 1,C=@c]O57114C)3)H(C)2B[O/c()rH7
add + at position 3,C=@+c]O57114C)3)H(C)2B[O/c()rH7
remove H from position 16,C=@+c]O57114C)3)(C)2B[O/c()rH7
replace 3 at position 14 with r,C=@+c]O57114C)r)(C)2B[O/c()rH7
add l at position 10,C=@+c]O571l14C)r)(C)2B[O/c()rH7
add F at position 13,C=@+c]O571l14FC)r)(C)2B[O/c()rH7
replace ) at position 17 with c,C=@+c]O571l14FC)rc(C)2B[O/c()rH7
add 7 at position 32,C=@+c]O571l14FC)rc(C)2B[O/c()rH77
add 4 at position 18,C=@+c]O571l14FC)rc4(C)2B[O/c()rH77
add 7 at position 21,C=@+c]O571l14FC)rc4(C7)2B[O/c()rH77
replace 4 at position 18 with c,C=@+c]O571l14FC)rcc(C7)2B[O/c()rH77
replace 7 at position 21 with ),C=@+c]O571l14FC)rcc(C))2B[O/c()rH77
remove ) from position 30,C=@+c]O571l14FC)rcc(C))2B[O/c(rH77
add 3 at position 6,C=@+c]3O571l14FC)rcc(C))2B[O/c(rH77
add ] at position 29,C=@+c]3O571l14FC)rcc(C))2B[O/]c(rH77
add # at position 30,C=@+c]3O571l14FC)rcc(C))2B[O/]#c(rH77
remove r from position 33,C=@+c]3O571l14FC)rcc(C))2B[O/]#c(H77
add r at position 22,C=@+c]3O571l14FC)rcc(Cr))2B[O/]#c(H77
add O at position 23,C=@+c]3O571l14FC)rcc(CrO))2B[O/]#c(H77
remove / from position 30,C=@+c]3O571l14FC)rcc(CrO))2B[O]#c(H77
add - at position 30,C=@+c]3O571l14FC)rcc(CrO))2B[O-]#c(H77
add o at position 19,C=@+c]3O571l14FC)rcoc(CrO))2B[O-]#c(H77
add n at position 4,C=@+nc]3O571l14FC)rcoc(CrO))2B[O-]#c(H77
replace 7 at position 10 with ],C=@+nc]3O5]1l14FC)rcoc(CrO))2B[O-]#c(H77
remove 2 from position 28,C=@+nc]3O5]1l14FC)rcoc(CrO))B[O-]#c(H77
remove 7 from position 37,C=@+nc]3O5]1l14FC)rcoc(CrO))B[O-]#c(H7
add r at position 17,C=@+nc]3O5]1l14FCr)rcoc(CrO))B[O-]#c(H7
add = at position 7,C=@+nc]=3O5]1l14FCr)rcoc(CrO))B[O-]#c(H7
add c at position 36,C=@+nc]=3O5]1l14FCr)rcoc(CrO))B[O-]#cc(H7
add ] at position 26,C=@+nc]=3O5]1l14FCr)rcoc(C]rO))B[O-]#cc(H7
replace c at position 5 with H,C=@+nH]=3O5]1l14FCr)rcoc(C]rO))B[O-]#cc(H7
add S at position 9,C=@+nH]=3SO5]1l14FCr)rcoc(C]rO))B[O-]#cc(H7
add 1 at position 43,C=@+nH]=3SO5]1l14FCr)rcoc(C]rO))B[O-]#cc(H71
add C at position 19,C=@+nH]=3SO5]1l14FCCr)rcoc(C]rO))B[O-]#cc(H71
replace F at position 17 with n,C=@+nH]=3SO5]1l14nCCr)rcoc(C]rO))B[O-]#cc(H71
remove 7 from position 43,C=@+nH]=3SO5]1l14nCCr)rcoc(C]rO))B[O-]#cc(H1
add F at position 20,C=@+nH]=3SO5]1l14nCCFr)rcoc(C]rO))B[O-]#cc(H1
replace @ at position 2 with 2,C=2+nH]=3SO5]1l14nCCFr)rcoc(C]rO))B[O-]#cc(H1
add C at position 9,C=2+nH]=3CSO5]1l14nCCFr)rcoc(C]rO))B[O-]#cc(H1
remove ( from position 43,C=2+nH]=3CSO5]1l14nCCFr)rcoc(C]rO))B[O-]#ccH1
add r at position 10,C=2+nH]=3CrSO5]1l14nCCFr)rcoc(C]rO))B[O-]#ccH1
remove ) from position 34,C=2+nH]=3CrSO5]1l14nCCFr)rcoc(C]rO)B[O-]#ccH1
remove r from position 10,C=2+nH]=3CSO5]1l14nCCFr)rcoc(C]rO)B[O-]#ccH1
add S at position 8,C=2+nH]=S3CSO5]1l14nCCFr)rcoc(C]rO)B[O-]#ccH1
remove B from position 35,C=2+nH]=S3CSO5]1l14nCCFr)rcoc(C]rO)[O-]#ccH1
remove F from position 22,C=2+nH]=S3CSO5]1l14nCCr)rcoc(C]rO)[O-]#ccH1
replace 5 at position 13 with 3,C=2+nH]=S3CSO3]1l14nCCr)rcoc(C]rO)[O-]#ccH1
add C at position 11,C=2+nH]=S3CCSO3]1l14nCCr)rcoc(C]rO)[O-]#ccH1
replace r at position 32 with (,C=2+nH]=S3CCSO3]1l14nCCr)rcoc(C](O)[O-]#ccH1
remove r from position 23,C=2+nH]=S3CCSO3]1l14nCC)rcoc(C](O)[O-]#ccH1
replace C at position 29 with N,C=2+nH]=S3CCSO3]1l14nCC)rcoc(N](O)[O-]#ccH1
remove S from position 12,C=2+nH]=S3CCO3]1l14nCC)rcoc(N](O)[O-]#ccH1
add [ at position 12,C=2+nH]=S3CC[O3]1l14nCC)rcoc(N](O)[O-]#ccH1
add H at position 4,C=2+HnH]=S3CC[O3]1l14nCC)rcoc(N](O)[O-]#ccH1
replace H at position 4 with [,C=2+[nH]=S3CC[O3]1l14nCC)rcoc(N](O)[O-]#ccH1
replace 4 at position 20 with ],C=2+[nH]=S3CC[O3]1l1]nCC)rcoc(N](O)[O-]#ccH1
add S at position 5,C=2+[SnH]=S3CC[O3]1l1]nCC)rcoc(N](O)[O-]#ccH1
replace 2 at position 2 with I,C=I+[SnH]=S3CC[O3]1l1]nCC)rcoc(N](O)[O-]#ccH1
add O at position 3,C=IO+[SnH]=S3CC[O3]1l1]nCC)rcoc(N](O)[O-]#ccH1
replace + at position 4 with #,C=IO#[SnH]=S3CC[O3]1l1]nCC)rcoc(N](O)[O-]#ccH1
replace l at position 20 with O,C=IO#[SnH]=S3CC[O3]1O1]nCC)rcoc(N](O)[O-]#ccH1
add H at position 17,C=IO#[SnH]=S3CC[OH3]1O1]nCC)rcoc(N](O)[O-]#ccH1
add B at position 10,C=IO#[SnH]B=S3CC[OH3]1O1]nCC)rcoc(N](O)[O-]#ccH1
remove S from position 6,C=IO#[nH]B=S3CC[OH3]1O1]nCC)rcoc(N](O)[O-]#ccH1
replace O at position 16 with @,C=IO#[nH]B=S3CC[@H3]1O1]nCC)rcoc(N](O)[O-]#ccH1
replace n at position 24 with C,C=IO#[nH]B=S3CC[@H3]1O1]CCC)rcoc(N](O)[O-]#ccH1
replace n at position 6 with C,C=IO#[CH]B=S3CC[@H3]1O1]CCC)rcoc(N](O)[O-]#ccH1
add @ at position 7,C=IO#[C@H]B=S3CC[@H3]1O1]CCC)rcoc(N](O)[O-]#ccH1
remove = from position 11,C=IO#[C@H]BS3CC[@H3]1O1]CCC)rcoc(N](O)[O-]#ccH1
replace 3 at position 18 with O,C=IO#[C@H]BS3CC[@HO]1O1]CCC)rcoc(N](O)[O-]#ccH1
add - at position 33,C=IO#[C@H]BS3CC[@HO]1O1]CCC)rcoc(-N](O)[O-]#ccH1
replace B at position 10 with 1,C=IO#[C@H]1S3CC[@HO]1O1]CCC)rcoc(-N](O)[O-]#ccH1
replace o at position 30 with (,C=IO#[C@H]1S3CC[@HO]1O1]CCC)rc(c(-N](O)[O-]#ccH1
replace ( at position 30 with c,C=IO#[C@H]1S3CC[@HO]1O1]CCC)rccc(-N](O)[O-]#ccH1
add = at position 37,C=IO#[C@H]1S3CC[@HO]1O1]CCC)rccc(-N](=O)[O-]#ccH1
add C at position 22,C=IO#[C@H]1S3CC[@HO]1OC1]CCC)rccc(-N](=O)[O-]#ccH1
replace I at position 2 with C,C=CO#[C@H]1S3CC[@HO]1OC1]CCC)rccc(-N](=O)[O-]#ccH1
remove ] from position 9,C=CO#[C@H1S3CC[@HO]1OC1]CCC)rccc(-N](=O)[O-]#ccH1
replace 3 at position 11 with C,C=CO#[C@H1SCCC[@HO]1OC1]CCC)rccc(-N](=O)[O-]#ccH1
remove O from position 17,C=CO#[C@H1SCCC[@H]1OC1]CCC)rccc(-N](=O)[O-]#ccH1
add r at position 29,C=CO#[C@H1SCCC[@H]1OC1]CCC)rcrcc(-N](=O)[O-]#ccH1
remove - from position 33,C=CO#[C@H1SCCC[@H]1OC1]CCC)rcrcc(N](=O)[O-]#ccH1
remove ] from position 22,C=CO#[C@H1SCCC[@H]1OC1CCC)rcrcc(N](=O)[O-]#ccH1
add ] at position 9,C=CO#[C@H]1SCCC[@H]1OC1CCC)rcrcc(N](=O)[O-]#ccH1
add H at position 4,C=COH#[C@H]1SCCC[@H]1OC1CCC)rcrcc(N](=O)[O-]#ccH1
remove H from position 4,C=CO#[C@H]1SCCC[@H]1OC1CCC)rcrcc(N](=O)[O-]#ccH1
add C at position 24,C=CO#[C@H]1SCCC[@H]1OC1CCCC)rcrcc(N](=O)[O-]#ccH1
add ) at position 45,C=CO#[C@H]1SCCC[@H]1OC1CCCC)rcrcc(N](=O)[O-]#)ccH1
replace # at position 4 with N,C=CON[C@H]1SCCC[@H]1OC1CCCC)rcrcc(N](=O)[O-]#)ccH1
add O at position 1,CO=CON[C@H]1SCCC[@H]1OC1CCCC)rcrcc(N](=O)[O-]#)ccH1
replace r at position 29 with c,CO=CON[C@H]1SCCC[@H]1OC1CCCC)ccrcc(N](=O)[O-]#)ccH1
add ) at position 41,CO=CON[C@H]1SCCC[@H]1OC1CCCC)ccrcc(N](=O))[O-]#)ccH1
add 2 at position 22,CO=CON[C@H]1SCCC[@H]1O2C1CCCC)ccrcc(N](=O))[O-]#)ccH1
replace O at position 4 with (,CO=C(N[C@H]1SCCC[@H]1O2C1CCCC)ccrcc(N](=O))[O-]#)ccH1
add C at position 17,CO=C(N[C@H]1SCCC[C@H]1O2C1CCCC)ccrcc(N](=O))[O-]#)ccH1
add 1 at position 32,CO=C(N[C@H]1SCCC[C@H]1O2C1CCCC)c1crcc(N](=O))[O-]#)ccH1
add [ at position 38,CO=C(N[C@H]1SCCC[C@H]1O2C1CCCC)c1crcc([N](=O))[O-]#)ccH1
remove r from position 34,CO=C(N[C@H]1SCCC[C@H]1O2C1CCCC)c1ccc([N](=O))[O-]#)ccH1
add C at position 15,CO=C(N[C@H]1SCCCC[C@H]1O2C1CCCC)c1ccc([N](=O))[O-]#)ccH1
remove C from position 0,O=C(N[C@H]1SCCCC[C@H]1O2C1CCCC)c1ccc([N](=O))[O-]#)ccH1
remove S from position 11,O=C(N[C@H]1CCCC[C@H]1O2C1CCCC)c1ccc([N](=O))[O-]#)ccH1
add + at position 29,O=C(N[C@H]1CCCC[C@H]1O2C1CCCC+)c1ccc([N](=O))[O-]#)ccH1
add + at position 39,O=C(N[C@H]1CCCC[C@H]1O2C1CCCC+)c1ccc([N+](=O))[O-]#)ccH1
remove # from position 50,O=C(N[C@H]1CCCC[C@H]1O2C1CCCC+)c1ccc([N+](=O))[O-])ccH1
remove 2 from position 22,O=C(N[C@H]1CCCC[C@H]1OC1CCCC+)c1ccc([N+](=O))[O-])ccH1
remove ) from position 44,O=C(N[C@H]1CCCC[C@H]1OC1CCCC+)c1ccc([N+](=O)[O-])ccH1
remove H from position 51,O=C(N[C@H]1CCCC[C@H]1OC1CCCC+)c1ccc([N+](=O)[O-])cc1
replace + at position 28 with 1,O=C(N[C@H]1CCCC[C@H]1OC1CCCC1)c1ccc([N+](=O)[O-])cc1
final: O=C(N[C@H]1CCCC[C@H]1OC1CCCC1)c1ccc([N+](=O)[O-])cc1,O=C(N[C@H]1CCCC[C@H]1OC1CCCC1)c1ccc([N+](=O)[O-])cc1
