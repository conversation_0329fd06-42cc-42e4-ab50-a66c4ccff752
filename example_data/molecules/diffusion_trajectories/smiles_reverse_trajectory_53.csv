log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 5,oFc5BH-C
remove c from position 2,oF5BH-<PERSON>
remove B from position 3,oF5H-<PERSON>
replace - at position 4 with ],oF5H]C
remove <PERSON> from position 5,oF5H]
add B at position 4,oF5HB]
add N at position 6,oF5HB]N
replace B at position 4 with -,oF5H-]N
add r at position 7,oF5H-]Nr
add S at position 6,oF5H-]SNr
add [ at position 2,oF[5H-]SNr
add 5 at position 3,oF[55H-]SNr
replace S at position 8 with r,oF[55H-]rNr
add l at position 2,oFl[55H-]rNr
add 4 at position 2,oF4l[55H-]rNr
replace [ at position 4 with H,oF4lH55H-]rNr
add 7 at position 5,oF4lH755H-]rNr
add O at position 2,oFO4lH755H-]rNr
remove H from position 9,oFO4lH755-]rNr
add 6 at position 13,oFO4lH755-]rN6r
replace F at position 1 with n,onO4lH755-]rN6r
remove H from position 5,onO4l755-]rN6r
replace 4 at position 3 with r,onOrl755-]rN6r
add = at position 6,onOrl7=55-]rN6r
add s at position 11,onOrl7=55-]srN6r
remove 6 from position 14,onOrl7=55-]srNr
replace ] at position 10 with 7,onOrl7=55-7srNr
remove O from position 2,onrl7=55-7srNr
add 2 at position 11,onrl7=55-7s2rNr
replace N at position 13 with /,onrl7=55-7s2r/r
add S at position 6,onrl7=S55-7s2r/r
add l at position 2,onlrl7=S55-7s2r/r
add F at position 11,onlrl7=S55-F7s2r/r
replace o at position 0 with S,Snlrl7=S55-F7s2r/r
remove S from position 7,Snlrl7=55-F7s2r/r
add B at position 10,Snlrl7=55-BF7s2r/r
remove l from position 2,Snrl7=55-BF7s2r/r
add r at position 1,Srnrl7=55-BF7s2r/r
add ) at position 12,Srnrl7=55-BF)7s2r/r
replace F at position 11 with 3,Srnrl7=55-B3)7s2r/r
add 4 at position 19,Srnrl7=55-B3)7s2r/r4
add N at position 2,SrNnrl7=55-B3)7s2r/r4
add 6 at position 1,S6rNnrl7=55-B3)7s2r/r4
add F at position 21,S6rNnrl7=55-B3)7s2r/rF4
remove r from position 2,S6Nnrl7=55-B3)7s2r/rF4
add ( at position 14,S6Nnrl7=55-B3)(7s2r/rF4
add ) at position 19,S6Nnrl7=55-B3)(7s2r)/rF4
add B at position 17,S6Nnrl7=55-B3)(7sB2r)/rF4
replace - at position 10 with c,S6Nnrl7=55cB3)(7sB2r)/rF4
add = at position 12,S6Nnrl7=55cB=3)(7sB2r)/rF4
replace F at position 24 with ),S6Nnrl7=55cB=3)(7sB2r)/r)4
replace / at position 22 with C,S6Nnrl7=55cB=3)(7sB2r)Cr)4
remove s from position 17,S6Nnrl7=55cB=3)(7B2r)Cr)4
add - at position 19,S6Nnrl7=55cB=3)(7B2-r)Cr)4
replace r at position 4 with 1,S6Nn1l7=55cB=3)(7B2-r)Cr)4
add ( at position 21,S6Nn1l7=55cB=3)(7B2-r()Cr)4
add r at position 5,S6Nn1rl7=55cB=3)(7B2-r()Cr)4
add ) at position 27,S6Nn1rl7=55cB=3)(7B2-r()Cr))4
replace S at position 0 with C,C6Nn1rl7=55cB=3)(7B2-r()Cr))4
add C at position 20,C6Nn1rl7=55cB=3)(7B2C-r()Cr))4
replace 7 at position 7 with 4,C6Nn1rl4=55cB=3)(7B2C-r()Cr))4
add ) at position 15,C6Nn1rl4=55cB=3))(7B2C-r()Cr))4
replace 4 at position 30 with =,C6Nn1rl4=55cB=3))(7B2C-r()Cr))=
replace ) at position 29 with 7,C6Nn1rl4=55cB=3))(7B2C-r()Cr)7=
add ) at position 27,C6Nn1rl4=55cB=3))(7B2C-r()C)r)7=
replace 5 at position 9 with S,C6Nn1rl4=S5cB=3))(7B2C-r()C)r)7=
add 2 at position 2,C62Nn1rl4=S5cB=3))(7B2C-r()C)r)7=
replace ( at position 25 with /,C62Nn1rl4=S5cB=3))(7B2C-r/)C)r)7=
remove - from position 23,C62Nn1rl4=S5cB=3))(7B2Cr/)C)r)7=
remove l from position 7,C62Nn1r4=S5cB=3))(7B2Cr/)C)r)7=
replace 3 at position 14 with O,C62Nn1r4=S5cB=O))(7B2Cr/)C)r)7=
add F at position 21,C62Nn1r4=S5cB=O))(7B2FCr/)C)r)7=
add l at position 13,C62Nn1r4=S5cBl=O))(7B2FCr/)C)r)7=
replace ) at position 17 with F,C62Nn1r4=S5cBl=O)F(7B2FCr/)C)r)7=
add C at position 32,C62Nn1r4=S5cBl=O)F(7B2FCr/)C)r)7C=
add 4 at position 18,C62Nn1r4=S5cBl=O)F4(7B2FCr/)C)r)7C=
add 7 at position 21,C62Nn1r4=S5cBl=O)F4(77B2FCr/)C)r)7C=
replace 4 at position 18 with n,C62Nn1r4=S5cBl=O)Fn(77B2FCr/)C)r)7C=
replace 7 at position 21 with ),C62Nn1r4=S5cBl=O)Fn(7)B2FCr/)C)r)7C=
remove ) from position 30,C62Nn1r4=S5cBl=O)Fn(7)B2FCr/)Cr)7C=
add = at position 6,C62Nn1=r4=S5cBl=O)Fn(7)B2FCr/)Cr)7C=
add + at position 29,C62Nn1=r4=S5cBl=O)Fn(7)B2FCr/+)Cr)7C=
add n at position 30,C62Nn1=r4=S5cBl=O)Fn(7)B2FCr/+n)Cr)7C=
remove r from position 33,C62Nn1=r4=S5cBl=O)Fn(7)B2FCr/+n)C)7C=
add C at position 22,C62Nn1=r4=S5cBl=O)Fn(7C)B2FCr/+n)C)7C=
add o at position 23,C62Nn1=r4=S5cBl=O)Fn(7Co)B2FCr/+n)C)7C=
remove / from position 30,C62Nn1=r4=S5cBl=O)Fn(7Co)B2FCr+n)C)7C=
add ) at position 30,C62Nn1=r4=S5cBl=O)Fn(7Co)B2FCr)+n)C)7C=
add r at position 19,C62Nn1=r4=S5cBl=O)Frn(7Co)B2FCr)+n)C)7C=
add ) at position 4,C62N)n1=r4=S5cBl=O)Frn(7Co)B2FCr)+n)C)7C=
replace = at position 10 with ),C62N)n1=r4)S5cBl=O)Frn(7Co)B2FCr)+n)C)7C=
remove 2 from position 28,C62N)n1=r4)S5cBl=O)Frn(7Co)BFCr)+n)C)7C=
remove 7 from position 37,C62N)n1=r4)S5cBl=O)Frn(7Co)BFCr)+n)C)C=
add 4 at position 17,C62N)n1=r4)S5cBl=4O)Frn(7Co)BFCr)+n)C)C=
add ] at position 7,C62N)n1]=r4)S5cBl=4O)Frn(7Co)BFCr)+n)C)C=
add 2 at position 36,C62N)n1]=r4)S5cBl=4O)Frn(7Co)BFCr)+n2)C)C=
replace = at position 17 with H,C62N)n1]=r4)S5cBlH4O)Frn(7Co)BFCr)+n2)C)C=
replace + at position 34 with ),C62N)n1]=r4)S5cBlH4O)Frn(7Co)BFCr))n2)C)C=
add B at position 36,C62N)n1]=r4)S5cBlH4O)Frn(7Co)BFCr))nB2)C)C=
add n at position 19,C62N)n1]=r4)S5cBlH4nO)Frn(7Co)BFCr))nB2)C)C=
replace H at position 17 with -,C62N)n1]=r4)S5cBl-4nO)Frn(7Co)BFCr))nB2)C)C=
replace = at position 43 with 1,C62N)n1]=r4)S5cBl-4nO)Frn(7Co)BFCr))nB2)C)C1
add N at position 13,C62N)n1]=r4)SN5cBl-4nO)Frn(7Co)BFCr))nB2)C)C1
remove 7 from position 27,C62N)n1]=r4)SN5cBl-4nO)Frn(Co)BFCr))nB2)C)C1
remove r from position 9,C62N)n1]=4)SN5cBl-4nO)Frn(Co)BFCr))nB2)C)C1
remove N from position 3,C62)n1]=4)SN5cBl-4nO)Frn(Co)BFCr))nB2)C)C1
add r at position 10,C62)n1]=4)rSN5cBl-4nO)Frn(Co)BFCr))nB2)C)C1
remove ) from position 34,C62)n1]=4)rSN5cBl-4nO)Frn(Co)BFCr)nB2)C)C1
remove r from position 10,C62)n1]=4)SN5cBl-4nO)Frn(Co)BFCr)nB2)C)C1
add C at position 8,C62)n1]=C4)SN5cBl-4nO)Frn(Co)BFCr)nB2)C)C1
remove B from position 35,C62)n1]=C4)SN5cBl-4nO)Frn(Co)BFCr)n2)C)C1
remove F from position 22,C62)n1]=C4)SN5cBl-4nO)rn(Co)BFCr)n2)C)C1
replace 5 at position 13 with 3,C62)n1]=C4)SN3cBl-4nO)rn(Co)BFCr)n2)C)C1
add - at position 11,C62)n1]=C4)-SN3cBl-4nO)rn(Co)BFCr)n2)C)C1
replace r at position 32 with (,C62)n1]=C4)-SN3cBl-4nO)rn(Co)BFC()n2)C)C1
remove r from position 23,C62)n1]=C4)-SN3cBl-4nO)n(Co)BFC()n2)C)C1
replace F at position 29 with /,C62)n1]=C4)-SN3cBl-4nO)n(Co)B/C()n2)C)C1
remove S from position 12,C62)n1]=C4)-N3cBl-4nO)n(Co)B/C()n2)C)C1
add 2 at position 12,C62)n1]=C4)-2N3cBl-4nO)n(Co)B/C()n2)C)C1
add H at position 4,C62)Hn1]=C4)-2N3cBl-4nO)n(Co)B/C()n2)C)C1
replace H at position 4 with c,C62)cn1]=C4)-2N3cBl-4nO)n(Co)B/C()n2)C)C1
replace 4 at position 20 with ],C62)cn1]=C4)-2N3cBl-]nO)n(Co)B/C()n2)C)C1
add S at position 5,C62)cSn1]=C4)-2N3cBl-]nO)n(Co)B/C()n2)C)C1
replace 2 at position 2 with H,C6H)cSn1]=C4)-2N3cBl-]nO)n(Co)B/C()n2)C)C1
add c at position 3,C6Hc)cSn1]=C4)-2N3cBl-]nO)n(Co)B/C()n2)C)C1
replace ) at position 4 with c,C6HcccSn1]=C4)-2N3cBl-]nO)n(Co)B/C()n2)C)C1
replace l at position 20 with (,C6HcccSn1]=C4)-2N3cB(-]nO)n(Co)B/C()n2)C)C1
add 2 at position 17,C6HcccSn1]=C4)-2N23cB(-]nO)n(Co)B/C()n2)C)C1
add @ at position 10,C6HcccSn1]@=C4)-2N23cB(-]nO)n(Co)B/C()n2)C)C1
remove S from position 6,C6Hcccn1]@=C4)-2N23cB(-]nO)n(Co)B/C()n2)C)C1
replace N at position 16 with c,C6Hcccn1]@=C4)-2c23cB(-]nO)n(Co)B/C()n2)C)C1
replace n at position 24 with 2,C6Hcccn1]@=C4)-2c23cB(-]2O)n(Co)B/C()n2)C)C1
replace n at position 6 with c,C6Hcccc1]@=C4)-2c23cB(-]2O)n(Co)B/C()n2)C)C1
add N at position 7,C6HccccN1]@=C4)-2c23cB(-]2O)n(Co)B/C()n2)C)C1
remove = from position 11,C6HccccN1]@C4)-2c23cB(-]2O)n(Co)B/C()n2)C)C1
replace 3 at position 18 with O,C6HccccN1]@C4)-2c2OcB(-]2O)n(Co)B/C()n2)C)C1
add - at position 33,C6HccccN1]@C4)-2c2OcB(-]2O)n(Co)B-/C()n2)C)C1
replace @ at position 10 with C,C6HccccN1]CC4)-2c2OcB(-]2O)n(Co)B-/C()n2)C)C1
replace o at position 30 with (,C6HccccN1]CC4)-2c2OcB(-]2O)n(C()B-/C()n2)C)C1
replace ( at position 30 with O,C6HccccN1]CC4)-2c2OcB(-]2O)n(CO)B-/C()n2)C)C1
add 3 at position 37,C6HccccN1]CC4)-2c2OcB(-]2O)n(CO)B-/C(3)n2)C)C1
add = at position 22,C6HccccN1]CC4)-2c2OcB(=-]2O)n(CO)B-/C(3)n2)C)C1
replace H at position 2 with c,C6cccccN1]CC4)-2c2OcB(=-]2O)n(CO)B-/C(3)n2)C)C1
remove ] from position 9,C6cccccN1CC4)-2c2OcB(=-]2O)n(CO)B-/C(3)n2)C)C1
replace 4 at position 11 with (,C6cccccN1CC()-2c2OcB(=-]2O)n(CO)B-/C(3)n2)C)C1
remove O from position 17,C6cccccN1CC()-2c2cB(=-]2O)n(CO)B-/C(3)n2)C)C1
add + at position 29,C6cccccN1CC()-2c2cB(=-]2O)n(C+O)B-/C(3)n2)C)C1
remove - from position 33,C6cccccN1CC()-2c2cB(=-]2O)n(C+O)B/C(3)n2)C)C1
remove ] from position 22,C6cccccN1CC()-2c2cB(=-2O)n(C+O)B/C(3)n2)C)C1
remove - from position 13,C6cccccN1CC()2c2cB(=-2O)n(C+O)B/C(3)n2)C)C1
remove / from position 31,C6cccccN1CC()2c2cB(=-2O)n(C+O)BC(3)n2)C)C1
add 1 at position 3,C6c1ccccN1CC()2c2cB(=-2O)n(C+O)BC(3)n2)C)C1
add + at position 42,C6c1ccccN1CC()2c2cB(=-2O)n(C+O)BC(3)n2)C)C+1
remove - from position 21,C6c1ccccN1CC()2c2cB(=2O)n(C+O)BC(3)n2)C)C+1
add 3 at position 32,C6c1ccccN1CC()2c2cB(=2O)n(C+O)BC3(3)n2)C)C+1
add C at position 33,C6c1ccccN1CC()2c2cB(=2O)n(C+O)BC3C(3)n2)C)C+1
add N at position 12,C6c1ccccN1CCN()2c2cB(=2O)n(C+O)BC3C(3)n2)C)C+1
replace 2 at position 17 with c,C6c1ccccN1CCN()2cccB(=2O)n(C+O)BC3C(3)n2)C)C+1
remove B from position 19,C6c1ccccN1CCN()2ccc(=2O)n(C+O)BC3C(3)n2)C)C+1
add C at position 35,C6c1ccccN1CCN()2ccc(=2O)n(C+O)BC3C(C3)n2)C)C+1
add O at position 2,C6Oc1ccccN1CCN()2ccc(=2O)n(C+O)BC3C(C3)n2)C)C+1
remove ) from position 43,C6Oc1ccccN1CCN()2ccc(=2O)n(C+O)BC3C(C3)n2)CC+1
add = at position 29,C6Oc1ccccN1CCN()2ccc(=2O)n(C+=O)BC3C(C3)n2)CC+1
replace ) at position 15 with c,C6Oc1ccccN1CCN(c2ccc(=2O)n(C+=O)BC3C(C3)n2)CC+1
replace B at position 32 with N,C6Oc1ccccN1CCN(c2ccc(=2O)n(C+=O)NC3C(C3)n2)CC+1
remove + from position 45,C6Oc1ccccN1CCN(c2ccc(=2O)n(C+=O)NC3C(C3)n2)CC1
add 1 at position 9,C6Oc1cccc1N1CCN(c2ccc(=2O)n(C+=O)NC3C(C3)n2)CC1
add ( at position 30,C6Oc1cccc1N1CCN(c2ccc(=2O)n(C+(=O)NC3C(C3)n2)CC1
remove 6 from position 1,COc1cccc1N1CCN(c2ccc(=2O)n(C+(=O)NC3C(C3)n2)CC1
remove 2 from position 22,COc1cccc1N1CCN(c2ccc(=O)n(C+(=O)NC3C(C3)n2)CC1
add c at position 5,COc1ccccc1N1CCN(c2ccc(=O)n(C+(=O)NC3C(C3)n2)CC1
remove ( from position 37,COc1ccccc1N1CCN(c2ccc(=O)n(C+(=O)NC3CC3)n2)CC1
replace + at position 28 with C,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)CC1
final: COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)CC1,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)CC1
