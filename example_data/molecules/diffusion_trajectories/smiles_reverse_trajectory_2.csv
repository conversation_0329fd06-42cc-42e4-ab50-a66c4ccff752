log,state
initialize: ,
add [ at position 0,[
add O at position 0,O[
add o at position 0,oO[
replace [ at position 2 with S,o<PERSON>
add [ at position 1,o[OS
add 1 at position 1,o1[OS
replace [ at position 2 with 6,o16<PERSON>
remove 6 from position 2,o1OS
add I at position 4,o1OSI
replace I at position 4 with (,o1OS(
remove ( from position 4,o1OS
add 5 at position 2,o15OS
remove o from position 0,15OS
add F at position 2,15FOS
add 1 at position 3,15F1OS
add I at position 5,15F1<PERSON><PERSON>
add B at position 7,15F1OISB
replace O at position 4 with 3,15F13ISB
replace F at position 2 with B,15B13ISB
remove 1 from position 0,5B13ISB
add = at position 2,5B=13ISB
replace I at position 5 with 3,5B=133SB
add B at position 0,B5B=133SB
add 6 at position 3,B5B6=133SB
replace B at position 2 with o,B5o6=133SB
replace = at position 4 with r,B5o6r133SB
replace B at position 0 with 7,75o6r133SB
replace <PERSON> at position 9 with F,75o6r133SF
replace <PERSON> at position 8 with r,75o6r133r<PERSON>
remove 6 from position 3,75or13<PERSON>r<PERSON>
replace 5 at position 1 with r,7ror133r<PERSON>
add / at position 6,7ror13/3rF
remove r from position 1,7or13/3rF
add s at position 7,7or13/3srF
add 1 at position 9,7or13/3sr1F
add ) at position 8,7or13/3s)r1F
replace / at position 5 with ),7or13)3s)r1F
add s at position 6,7or13)s3s)r1F
replace F at position 12 with ),7or13)s3s)r1)
replace 1 at position 11 with (,7or13)s3s)r()
remove s from position 8,7or13)s3)r()
add 5 at position 9,7or13)s3)5r()
replace r at position 2 with C,7oC13)s3)5r()
add H at position 10,7oC13)s3)5Hr()
replace 1 at position 3 with ),7oC)3)s3)5Hr()
remove 5 from position 9,7oC)3)s3)Hr()
add ) at position 12,7oC)3)s3)Hr())
remove ) from position 8,7oC)3)s3Hr())
add ( at position 9,7oC)3)s3H(r())
replace 3 at position 4 with n,7oC)n)s3H(r())
replace o at position 1 with 1,71C)n)s3H(r())
add 6 at position 6,71C)n)6s3H(r())
add 5 at position 10,71C)n)6s3H5(r())
remove 1 from position 1,7C)n)6s3H5(r())
remove 7 from position 0,C)n)6s3H5(r())
replace 6 at position 4 with C,C)n)Cs3H5(r())
add c at position 1,Cc)n)Cs3H5(r())
remove H from position 8,Cc)n)Cs35(r())
replace 3 at position 7 with C,Cc)n)CsC5(r())
remove 5 from position 8,Cc)n)CsC(r())
add 7 at position 9,Cc)n)CsC(7r())
replace ) at position 4 with l,Cc)nlCsC(7r())
add 5 at position 8,Cc)nlCsC5(7r())
add 4 at position 9,Cc)nlCsC54(7r())
add 6 at position 10,Cc)nlCsC546(7r())
replace 4 at position 9 with c,Cc)nlCsC5c6(7r())
replace 6 at position 10 with s,Cc)nlCsC5cs(7r())
remove ) from position 15,Cc)nlCsC5cs(7r()
add c at position 3,Cc)cnlCsC5cs(7r()
add O at position 14,Cc)cnlCsC5cs(7Or()
add l at position 15,Cc)cnlCsC5cs(7Olr()
remove r from position 16,Cc)cnlCsC5cs(7Ol()
add ] at position 11,Cc)cnlCsC5c]s(7Ol()
add 2 at position 11,Cc)cnlCsC5c2]s(7Ol()
add = at position 5,Cc)cn=lCsC5c2]s(7Ol()
replace = at position 5 with 4,Cc)cn4lCsC5c2]s(7Ol()
add - at position 21,Cc)cn4lCsC5c2]s(7Ol()-
add N at position 9,Cc)cn4lCsNC5c2]s(7Ol()-
add H at position 2,CcH)cn4lCsNC5c2]s(7Ol()-
add H at position 12,CcH)cn4lCsNCH5c2]s(7Ol()-
add c at position 24,CcH)cn4lCsNCH5c2]s(7Ol()c-
add ] at position 26,CcH)cn4lCsNCH5c2]s(7Ol()c-]
add I at position 1,CIcH)cn4lCsNCH5c2]s(7Ol()c-]
remove l from position 22,CIcH)cn4lCsNCH5c2]s(7O()c-]
add = at position 8,CIcH)cn4=lCsNCH5c2]s(7O()c-]
add N at position 3,CIcNH)cn4=lCsNCH5c2]s(7O()c-]
add 4 at position 18,CIcNH)cn4=lCsNCH5c42]s(7O()c-]
add F at position 13,CIcNH)cn4=lCsFNCH5c42]s(7O()c-]
replace c at position 2 with 6,CI6NH)cn4=lCsFNCH5c42]s(7O()c-]
add r at position 9,CI6NH)cn4r=lCsFNCH5c42]s(7O()c-]
add r at position 19,CI6NH)cn4r=lCsFNCH5rc42]s(7O()c-]
replace H at position 17 with (,CI6NH)cn4r=lCsFNC(5rc42]s(7O()c-]
replace s at position 24 with l,CI6NH)cn4r=lCsFNC(5rc42]l(7O()c-]
add 3 at position 13,CI6NH)cn4r=lC3sFNC(5rc42]l(7O()c-]
remove 7 from position 27,CI6NH)cn4r=lC3sFNC(5rc42]l(O()c-]
remove r from position 9,CI6NH)cn4=lC3sFNC(5rc42]l(O()c-]
remove N from position 3,CI6H)cn4=lC3sFNC(5rc42]l(O()c-]
add 1 at position 20,CI6H)cn4=lC3sFNC(5rc142]l(O()c-]
remove l from position 9,CI6H)cn4=C3sFNC(5rc142]l(O()c-]
remove I from position 1,C6H)cn4=C3sFNC(5rc142]l(O()c-]
replace 5 at position 15 with N,C6H)cn4=C3sFNC(Nrc142]l(O()c-]
remove ] from position 29,C6H)cn4=C3sFNC(Nrc142]l(O()c-
remove F from position 11,C6H)cn4=C3sNC(Nrc142]l(O()c-
replace 4 at position 6 with ],C6H)cn]=C3sNC(Nrc142]l(O()c-
add S at position 5,C6H)cSn]=C3sNC(Nrc142]l(O()c-
replace r at position 16 with 3,C6H)cSn]=C3sNC(N3c142]l(O()c-
replace s at position 11 with 1,C6H)cSn]=C31NC(N3c142]l(O()c-
add n at position 7,C6H)cSnn]=C31NC(N3c142]l(O()c-
add C at position 28,C6H)cSnn]=C31NC(N3c142]l(O()Cc-
remove 1 from position 12,C6H)cSnn]=C3NC(N3c142]l(O()Cc-
add ( at position 30,C6H)cSnn]=C3NC(N3c142]l(O()Cc-(
add I at position 4,C6H)IcSnn]=C3NC(N3c142]l(O()Cc-(
replace I at position 4 with C,C6H)CcSnn]=C3NC(N3c142]l(O()Cc-(
replace 4 at position 20 with l,C6H)CcSnn]=C3NC(N3c1l2]l(O()Cc-(
remove c from position 5,C6H)CSnn]=C3NC(N3c1l2]l(O()Cc-(
replace ( at position 25 with C,C6H)CSnn]=C3NC(N3c1l2]l(OC)Cc-(
add ( at position 3,C6H()CSnn]=C3NC(N3c1l2]l(OC)Cc-(
replace ) at position 4 with N,C6H(NCSnn]=C3NC(N3c1l2]l(OC)Cc-(
replace l at position 20 with c,C6H(NCSnn]=C3NC(N3c1c2]l(OC)Cc-(
add ) at position 17,C6H(NCSnn]=C3NC(N)3c1c2]l(OC)Cc-(
add @ at position 10,C6H(NCSnn]@=C3NC(N)3c1c2]l(OC)Cc-(
remove S from position 6,C6H(NCnn]@=C3NC(N)3c1c2]l(OC)Cc-(
replace N at position 16 with O,C6H(NCnn]@=C3NC(O)3c1c2]l(OC)Cc-(
replace l at position 24 with n,C6H(NCnn]@=C3NC(O)3c1c2]n(OC)Cc-(
replace n at position 6 with ),C6H(NC)n]@=C3NC(O)3c1c2]n(OC)Cc-(
add ( at position 7,C6H(NC)(n]@=C3NC(O)3c1c2]n(OC)Cc-(
remove = from position 11,C6H(NC)(n]@C3NC(O)3c1c2]n(OC)Cc-(
replace 3 at position 18 with O,C6H(NC)(n]@C3NC(O)Oc1c2]n(OC)Cc-(
add 1 at position 33,C6H(NC)(n]@C3NC(O)Oc1c2]n(OC)Cc-(1
replace @ at position 10 with O,C6H(NC)(n]OC3NC(O)Oc1c2]n(OC)Cc-(1
add ) at position 30,C6H(NC)(n]OC3NC(O)Oc1c2]n(OC)C)c-(1
add o at position 29,C6H(NC)(n]OC3NC(O)Oc1c2]n(OC)oC)c-(1
add - at position 22,C6H(NC)(n]OC3NC(O)Oc1c-2]n(OC)oC)c-(1
replace H at position 2 with N,C6N(NC)(n]OC3NC(O)Oc1c-2]n(OC)oC)c-(1
remove ] from position 9,C6N(NC)(nOC3NC(O)Oc1c-2]n(OC)oC)c-(1
replace 3 at position 11 with C,C6N(NC)(nOCCNC(O)Oc1c-2]n(OC)oC)c-(1
remove O from position 17,C6N(NC)(nOCCNC(O)c1c-2]n(OC)oC)c-(1
add ( at position 29,C6N(NC)(nOCCNC(O)c1c-2]n(OC)o(C)c-(1
remove - from position 33,C6N(NC)(nOCCNC(O)c1c-2]n(OC)o(C)c(1
remove ] from position 22,C6N(NC)(nOCCNC(O)c1c-2n(OC)o(C)c(1
add = at position 9,C6N(NC)(n=OCCNC(O)c1c-2n(OC)o(C)c(1
add H at position 4,C6N(HNC)(n=OCCNC(O)c1c-2n(OC)o(C)c(1
remove H from position 4,C6N(NC)(n=OCCNC(O)c1c-2n(OC)o(C)c(1
add c at position 24,C6N(NC)(n=OCCNC(O)c1c-2nc(OC)o(C)c(1
remove - from position 21,C6N(NC)(n=OCCNC(O)c1c2nc(OC)o(C)c(1
add C at position 32,C6N(NC)(n=OCCNC(O)c1c2nc(OC)o(C)Cc(1
add ) at position 33,C6N(NC)(n=OCCNC(O)c1c2nc(OC)o(C)C)c(1
add C at position 12,C6N(NC)(n=OCCCNC(O)c1c2nc(OC)o(C)C)c(1
add c at position 22,C6N(NC)(n=OCCCNC(O)c1cc2nc(OC)o(C)C)c(1
replace N at position 4 with S,C6N(SC)(n=OCCCNC(O)c1cc2nc(OC)o(C)C)c(1
add = at position 17,C6N(SC)(n=OCCCNC(=O)c1cc2nc(OC)o(C)C)c(1
remove C from position 5,C6N(S)(n=OCCCNC(=O)c1cc2nc(OC)o(C)C)c(1
add - at position 29,C6N(S)(n=OCCCNC(=O)c1cc2nc(OC-)o(C)C)c(1
replace S at position 4 with C,C6N(C)(n=OCCCNC(=O)c1cc2nc(OC-)o(C)C)c(1
remove o from position 31,C6N(C)(n=OCCCNC(=O)c1cc2nc(OC-)(C)C)c(1
remove n from position 7,C6N(C)(=OCCCNC(=O)c1cc2nc(OC-)(C)C)c(1
add ) at position 9,C6N(C)(=O)CCCNC(=O)c1cc2nc(OC-)(C)C)c(1
add C at position 30,C6N(C)(=O)CCCNC(=O)c1cc2nc(OC-C)(C)C)c(1
remove 6 from position 1,CN(C)(=O)CCCNC(=O)c1cc2nc(OC-C)(C)C)c(1
remove 2 from position 22,CN(C)(=O)CCCNC(=O)c1ccnc(OC-C)(C)C)c(1
add C at position 5,CN(C)C(=O)CCCNC(=O)c1ccnc(OC-C)(C)C)c(1
remove ( from position 37,CN(C)C(=O)CCCNC(=O)c1ccnc(OC-C)(C)C)c1
replace - at position 28 with (,CN(C)C(=O)CCCNC(=O)c1ccnc(OC(C)(C)C)c1
final: CN(C)C(=O)CCCNC(=O)c1ccnc(OC(C)(C)C)c1,CN(C)C(=O)CCCNC(=O)c1ccnc(OC(C)(C)C)c1
