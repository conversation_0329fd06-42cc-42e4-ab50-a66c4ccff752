log,state
initialize: ,
add c at position 0,c
add N at position 1,c<PERSON>
remove c from position 0,N
replace N at position 0 with ),)
add H at position 0,H)
add 7 at position 0,7H)
replace 7 at position 0 with I,IH)
remove I from position 0,H)
replace ) at position 1 with s,Hs
replace H at position 0 with 6,6s
add C at position 1,6Cs
add + at position 3,6Cs+
add s at position 1,6sCs+
add 4 at position 2,6s4<PERSON>+
add 2 at position 2,6s24Cs+
replace 6 at position 0 with 1,1s24<PERSON>+
replace 2 at position 2 with O,1sO4Cs+
add + at position 2,1s+O4Cs+
replace + at position 7 with [,1s+O4Cs[
replace + at position 2 with r,1srO4Cs[
add H at position 8,1srO4Cs[H
add S at position 5,1srO4SCs[H
add S at position 7,1srO4SCSs[H
add - at position 9,1srO4SCSs-[H
replace C at position 6 with H,1srO4SHSs-[H
remove H from position 11,1srO4SHSs-[
add ( at position 11,1srO4SHSs-[(
add F at position 3,1srFO4SHSs-[(
remove S from position 8,1srFO4SHs-[(
replace r at position 2 with o,1soFO4SHs-[(
add 6 at position 7,1soFO4S6Hs-[(
replace [ at position 11 with 5,1soFO4S6Hs-5(
add ] at position 13,1soFO4S6Hs-5(]
add 5 at position 5,1soFO54S6Hs-5(]
add 2 at position 11,1soFO54S6Hs2-5(]
add 6 at position 6,1soFO564S6Hs2-5(]
replace F at position 3 with l,1solO564S6Hs2-5(]
remove H from position 10,1solO564S6s2-5(]
replace 4 at position 7 with ],1solO56]S6s2-5(]
add s at position 13,1solO56]S6s2-s5(]
add 5 at position 11,1solO56]S6s52-s5(]
add / at position 11,1solO56]S6s/52-s5(]
replace s at position 1 with o,1oolO56]S6s/52-s5(]
remove O from position 4,1ool56]S6s/52-s5(]
replace ] at position 17 with [,1ool56]S6s/52-s5([
remove 1 from position 0,ool56]S6s/52-s5([
add 2 at position 5,ool562]S6s/52-s5([
add I at position 18,ool562]S6s/52-s5([I
replace o at position 0 with 1,1ol562]S6s/52-s5([I
remove S from position 7,1ol562]6s/52-s5([I
add 4 at position 10,1ol562]6s/452-s5([I
remove l from position 2,1o562]6s/452-s5([I
replace ( at position 15 with s,1o562]6s/452-s5s[I
replace [ at position 16 with 5,1o562]6s/452-s5s5I
remove 6 from position 6,1o562]s/452-s5s5I
replace 5 at position 2 with r,1or62]s/452-s5s5I
add ) at position 13,1or62]s/452-s)5s5I
remove r from position 2,1o62]s/452-s)5s5I
add c at position 14,1o62]s/452-s)5cs5I
add c at position 17,1o62]s/452-s)5cs5cI
replace - at position 10 with 3,1o62]s/4523s)5cs5cI
add H at position 12,1o62]s/4523sH)5cs5cI
add 2 at position 16,1o62]s/4523sH)5c2s5cI
replace s at position 5 with r,1o62]r/4523sH)5c2s5cI
remove s from position 17,1o62]r/4523sH)5c25cI
replace I at position 19 with r,1o62]r/4523sH)5c25cr
add n at position 4,1o62n]r/4523sH)5c25cr
replace / at position 7 with 7,1o62n]r74523sH)5c25cr
add O at position 10,1o62n]r745O23sH)5c25cr
add 7 at position 0,71o62n]r745O23sH)5c25cr
remove ) from position 16,71o62n]r745O23sH5c25cr
add o at position 18,71o62n]r745O23sH5co25cr
replace 4 at position 9 with ),71o62n]r7)5O23sH5co25cr
replace o at position 2 with O,71O62n]r7)5O23sH5co25cr
add c at position 13,71O62n]r7)5O2c3sH5co25cr
add 6 at position 10,71O62n]r7)65O2c3sH5co25cr
remove 1 from position 1,7O62n]r7)65O2c3sH5co25cr
remove 7 from position 0,O62n]r7)65O2c3sH5co25cr
replace 6 at position 8 with N,O62n]r7)N5O2c3sH5co25cr
add N at position 3,O62Nn]r7)N5O2c3sH5co25cr
remove H from position 16,O62Nn]r7)N5O2c3s5co25cr
replace 3 at position 14 with r,O62Nn]r7)N5O2crs5co25cr
remove 5 from position 16,O62Nn]r7)N5O2crsco25cr
add 2 at position 19,O62Nn]r7)N5O2crsco225cr
replace ) at position 8 with l,O62Nn]r7lN5O2crsco225cr
add 5 at position 16,O62Nn]r7lN5O2crs5co225cr
add S at position 9,O62Nn]r7lSN5O2crs5co225cr
add c at position 10,O62Nn]r7lScN5O2crs5co225cr
replace S at position 9 with H,O62Nn]r7lHcN5O2crs5co225cr
replace c at position 24 with +,O62Nn]r7lHcN5O2crs5co225+r
replace 5 at position 18 with r,O62Nn]r7lHcN5O2crsrco225+r
add ) at position 4,O62N)n]r7lHcN5O2crsrco225+r
remove c from position 11,O62N)n]r7lHN5O2crsrco225+r
add [ at position 25,O62N)n]r7lHN5O2crsrco225+[r
remove [ from position 25,O62N)n]r7lHN5O2crsrco225+r
add l at position 21,O62N)n]r7lHN5O2crsrcol225+r
add F at position 25,O62N)n]r7lHN5O2crsrcol225F+r
remove r from position 16,O62N)n]r7lHN5O2csrcol225F+r
add S at position 11,O62N)n]r7lHSN5O2csrcol225F+r
add N at position 11,O62N)n]r7lHNSN5O2csrcol225F+r
add 7 at position 5,O62N)7n]r7lHNSN5O2csrcol225F+r
replace 7 at position 5 with n,O62N)nn]r7lHNSN5O2csrcol225F+r
add C at position 21,O62N)nn]r7lHNSN5O2csrCcol225F+r
add ( at position 19,O62N)nn]r7lHNSN5O2c(srCcol225F+r
add I at position 4,O62NI)nn]r7lHNSN5O2c(srCcol225F+r
replace 7 at position 10 with =,O62NI)nn]r=lHNSN5O2c(srCcol225F+r
remove 2 from position 28,O62NI)nn]r=lHNSN5O2c(srCcol25F+r
add o at position 25,O62NI)nn]r=lHNSN5O2c(srCcool25F+r
add n at position 19,O62NI)nn]r=lHNSN5O2nc(srCcool25F+r
add 7 at position 24,O62NI)nn]r=lHNSN5O2nc(sr7Ccool25F+r
add = at position 17,O62NI)nn]r=lHNSN5=O2nc(sr7Ccool25F+r
replace = at position 17 with H,O62NI)nn]r=lHNSN5HO2nc(sr7Ccool25F+r
replace + at position 34 with ),O62NI)nn]r=lHNSN5HO2nc(sr7Ccool25F)r
add 1 at position 36,O62NI)nn]r=lHNSN5HO2nc(sr7Ccool25F)r1
add l at position 19,O62NI)nn]r=lHNSN5HOl2nc(sr7Ccool25F)r1
replace H at position 17 with C,O62NI)nn]r=lHNSN5COl2nc(sr7Ccool25F)r1
replace s at position 24 with F,O62NI)nn]r=lHNSN5COl2nc(Fr7Ccool25F)r1
add 4 at position 13,O62NI)nn]r=lH4NSN5COl2nc(Fr7Ccool25F)r1
remove 7 from position 27,O62NI)nn]r=lH4NSN5COl2nc(FrCcool25F)r1
remove r from position 9,O62NI)nn]=lH4NSN5COl2nc(FrCcool25F)r1
remove N from position 3,O62I)nn]=lH4NSN5COl2nc(FrCcool25F)r1
add 4 at position 20,O62I)nn]=lH4NSN5COl24nc(FrCcool25F)r1
remove l from position 9,O62I)nn]=H4NSN5COl24nc(FrCcool25F)r1
remove I from position 3,O62)nn]=H4NSN5COl24nc(FrCcool25F)r1
remove 5 from position 30,O62)nn]=H4NSN5COl24nc(FrCcool2F)r1
remove F from position 22,O62)nn]=H4NSN5COl24nc(rCcool2F)r1
replace 5 at position 13 with 3,O62)nn]=H4NSN3COl24nc(rCcool2F)r1
add C at position 11,O62)nn]=H4NCSN3COl24nc(rCcool2F)r1
replace r at position 32 with C,O62)nn]=H4NCSN3COl24nc(rCcool2F)C1
remove r from position 23,O62)nn]=H4NCSN3COl24nc(Ccool2F)C1
replace F at position 29 with (,O62)nn]=H4NCSN3COl24nc(Ccool2()C1
remove S from position 12,O62)nn]=H4NCN3COl24nc(Ccool2()C1
add ( at position 12,O62)nn]=H4NC(N3COl24nc(Ccool2()C1
add I at position 4,O62)Inn]=H4NC(N3COl24nc(Ccool2()C1
replace I at position 4 with C,O62)Cnn]=H4NC(N3COl24nc(Ccool2()C1
replace 4 at position 20 with ],O62)Cnn]=H4NC(N3COl2]nc(Ccool2()C1
add S at position 5,O62)CSnn]=H4NC(N3COl2]nc(Ccool2()C1
replace 2 at position 2 with I,O6I)CSnn]=H4NC(N3COl2]nc(Ccool2()C1
add C at position 3,O6IC)CSnn]=H4NC(N3COl2]nc(Ccool2()C1
replace ) at position 4 with N,O6ICNCSnn]=H4NC(N3COl2]nc(Ccool2()C1
replace l at position 20 with c,O6ICNCSnn]=H4NC(N3COc2]nc(Ccool2()C1
add ) at position 17,O6ICNCSnn]=H4NC(N)3COc2]nc(Ccool2()C1
add B at position 10,O6ICNCSnn]B=H4NC(N)3COc2]nc(Ccool2()C1
remove S from position 6,O6ICNCnn]B=H4NC(N)3COc2]nc(Ccool2()C1
replace N at position 16 with O,O6ICNCnn]B=H4NC(O)3COc2]nc(Ccool2()C1
replace n at position 24 with c,O6ICNCnn]B=H4NC(O)3COc2]cc(Ccool2()C1
replace n at position 6 with C,O6ICNCCn]B=H4NC(O)3COc2]cc(Ccool2()C1
add [ at position 7,O6ICNCC[n]B=H4NC(O)3COc2]cc(Ccool2()C1
remove = from position 11,O6ICNCC[n]BH4NC(O)3COc2]cc(Ccool2()C1
replace 3 at position 18 with O,O6ICNCC[n]BH4NC(O)OCOc2]cc(Ccool2()C1
add - at position 33,O6ICNCC[n]BH4NC(O)OCOc2]cc(Ccool2-()C1
replace B at position 10 with @,O6ICNCC[n]@H4NC(O)OCOc2]cc(Ccool2-()C1
replace o at position 30 with (,O6ICNCC[n]@H4NC(O)OCOc2]cc(Cco(l2-()C1
replace ( at position 30 with C,O6ICNCC[n]@H4NC(O)OCOc2]cc(CcoCl2-()C1
add N at position 37,O6ICNCC[n]@H4NC(O)OCOc2]cc(CcoCl2-()CN1
add - at position 22,O6ICNCC[n]@H4NC(O)OCOc-2]cc(CcoCl2-()CN1
replace I at position 2 with =,O6=CNCC[n]@H4NC(O)OCOc-2]cc(CcoCl2-()CN1
remove ] from position 9,O6=CNCC[n@H4NC(O)OCOc-2]cc(CcoCl2-()CN1
replace 4 at position 11 with (,O6=CNCC[n@H(NC(O)OCOc-2]cc(CcoCl2-()CN1
remove O from position 17,O6=CNCC[n@H(NC(O)COc-2]cc(CcoCl2-()CN1
add ( at position 29,O6=CNCC[n@H(NC(O)COc-2]cc(Cco(Cl2-()CN1
remove - from position 33,O6=CNCC[n@H(NC(O)COc-2]cc(Cco(Cl2()CN1
remove ] from position 22,O6=CNCC[n@H(NC(O)COc-2cc(Cco(Cl2()CN1
add C at position 9,O6=CNCC[nC@H(NC(O)COc-2cc(Cco(Cl2()CN1
add H at position 4,O6=CHNCC[nC@H(NC(O)COc-2cc(Cco(Cl2()CN1
remove H from position 4,O6=CNCC[nC@H(NC(O)COc-2cc(Cco(Cl2()CN1
add c at position 24,O6=CNCC[nC@H(NC(O)COc-2ccc(Cco(Cl2()CN1
remove - from position 21,O6=CNCC[nC@H(NC(O)COc2ccc(Cco(Cl2()CN1
add ) at position 32,O6=CNCC[nC@H(NC(O)COc2ccc(Cco(Cl)2()CN1
add c at position 33,O6=CNCC[nC@H(NC(O)COc2ccc(Cco(Cl)c2()CN1
add ] at position 12,O6=CNCC[nC@H](NC(O)COc2ccc(Cco(Cl)c2()CN1
add 2 at position 22,O6=CNCC[nC@H](NC(O)COc22ccc(Cco(Cl)c2()CN1
replace N at position 4 with S,O6=CSCC[nC@H](NC(O)COc22ccc(Cco(Cl)c2()CN1
add = at position 17,O6=CSCC[nC@H](NC(=O)COc22ccc(Cco(Cl)c2()CN1
remove C from position 5,O6=CSC[nC@H](NC(=O)COc22ccc(Cco(Cl)c2()CN1
add + at position 29,O6=CSC[nC@H](NC(=O)COc22ccc(C+co(Cl)c2()CN1
replace S at position 4 with 1,O6=C1C[nC@H](NC(=O)COc22ccc(C+co(Cl)c2()CN1
remove o from position 31,O6=C1C[nC@H](NC(=O)COc22ccc(C+c(Cl)c2()CN1
remove n from position 7,O6=C1C[C@H](NC(=O)COc22ccc(C+c(Cl)c2()CN1
add @ at position 9,O6=C1C[C@@H](NC(=O)COc22ccc(C+c(Cl)c2()CN1
add ) at position 30,O6=C1C[C@@H](NC(=O)COc22ccc(C+)c(Cl)c2()CN1
remove 6 from position 1,O=C1C[C@@H](NC(=O)COc22ccc(C+)c(Cl)c2()CN1
remove 2 from position 22,O=C1C[C@@H](NC(=O)COc2ccc(C+)c(Cl)c2()CN1
add C at position 5,O=C1CC[C@@H](NC(=O)COc2ccc(C+)c(Cl)c2()CN1
remove ( from position 37,O=C1CC[C@@H](NC(=O)COc2ccc(C+)c(Cl)c2)CN1
replace + at position 28 with l,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2)CN1
final: O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2)CN1,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2)CN1
