log,state
initialize: COC(=O)CNC(=O)c1sc2ncn(CC(=O)N3CCCCC3)c(=O)c2c1C,COC(=O)CNC(=O)c1sc2ncn(CC(=O)N3CCCCC3)c(=O)c2c1C
replace ) at position 28 with -,COC(=O)CNC(=O)c1sc2ncn(CC(=O-N3CCCCC3)c(=O)c2c1C
add ( at position 37,COC(=O)CNC(=O)c1sc2ncn(CC(=O-N3CCCCC3()c(=O)c2c1C
remove O from position 5,COC(=)CNC(=O)c1sc2ncn(CC(=O-N3CCCCC3()c(=O)c2c1C
add 2 at position 22,COC(=)CNC(=O)c1sc2ncn(2CC(=O-N3CCCCC3()c(=O)c2c1C
add 6 at position 1,C6OC(=)CNC(=O)c1sc2ncn(2CC(=O-N3CCCCC3()c(=O)c2c1C
remove N from position 30,C6OC(=)CNC(=O)c1sc2ncn(2CC(=O-3CCCCC3()c(=O)c2c1C
remove C from position 9,C6OC(=)CN(=O)c1sc2ncn(2CC(=O-3CCCCC3()c(=O)c2c1C
add + at position 45,C6OC(=)CN(=O)c1sc2ncn(2CC(=O-3CCCCC3()c(=O)c2+c1C
replace C at position 32 with B,C6OC(=)CN(=O)c1sc2ncn(2CC(=O-3CCBCC3()c(=O)c2+c1C
replace s at position 15 with ),C6OC(=)CN(=O)c1)c2ncn(2CC(=O-3CCBCC3()c(=O)c2+c1C
remove 3 from position 29,C6OC(=)CN(=O)c1)c2ncn(2CC(=O-CCBCC3()c(=O)c2+c1C
add ) at position 43,C6OC(=)CN(=O)c1)c2ncn(2CC(=O-CCBCC3()c(=O)c)2+c1C
remove O from position 2,C6C(=)CN(=O)c1)c2ncn(2CC(=O-CCBCC3()c(=O)c)2+c1C
remove ) from position 35,C6C(=)CN(=O)c1)c2ncn(2CC(=O-CCBCC3(c(=O)c)2+c1C
add B at position 19,C6C(=)CN(=O)c1)c2ncBn(2CC(=O-CCBCC3(c(=O)c)2+c1C
replace n at position 17 with 2,C6C(=)CN(=O)c1)c22cBn(2CC(=O-CCBCC3(c(=O)c)2+c1C
remove c from position 12,C6C(=)CN(=O)1)c22cBn(2CC(=O-CCBCC3(c(=O)c)2+c1C
remove 3 from position 33,C6C(=)CN(=O)1)c22cBn(2CC(=O-CCBCC(c(=O)c)2+c1C
remove C from position 32,C6C(=)CN(=O)1)c22cBn(2CC(=O-CCBC(c(=O)c)2+c1C
add - at position 21,C6C(=)CN(=O)1)c22cBn(-2CC(=O-CCBC(c(=O)c)2+c1C
remove + from position 42,C6C(=)CN(=O)1)c22cBn(-2CC(=O-CCBC(c(=O)c)2c1C
remove ( from position 3,C6C=)CN(=O)1)c22cBn(-2CC(=O-CCBC(c(=O)c)2c1C
add / at position 31,C6C=)CN(=O)1)c22cBn(-2CC(=O-CCB/C(c(=O)c)2c1C
add - at position 13,C6C=)CN(=O)1)-c22cBn(-2CC(=O-CCB/C(c(=O)c)2c1C
add ] at position 22,C6C=)CN(=O)1)-c22cBn(-]2CC(=O-CCB/C(c(=O)c)2c1C
add - at position 33,C6C=)CN(=O)1)-c22cBn(-]2CC(=O-CCB-/C(c(=O)c)2c1C
remove - from position 29,C6C=)CN(=O)1)-c22cBn(-]2CC(=OCCB-/C(c(=O)c)2c1C
add O at position 17,C6C=)CN(=O)1)-c22OcBn(-]2CC(=OCCB-/C(c(=O)c)2c1C
replace 1 at position 11 with 4,C6C=)CN(=O)4)-c22OcBn(-]2CC(=OCCB-/C(c(=O)c)2c1C
add ] at position 9,C6C=)CN(=]O)4)-c22OcBn(-]2CC(=OCCB-/C(c(=O)c)2c1C
replace C at position 2 with I,C6I=)CN(=]O)4)-c22OcBn(-]2CC(=OCCB-/C(c(=O)c)2c1C
remove ( from position 22,C6I=)CN(=]O)4)-c22OcBn-]2CC(=OCCB-/C(c(=O)c)2c1C
remove c from position 37,C6I=)CN(=]O)4)-c22OcBn-]2CC(=OCCB-/C((=O)c)2c1C
replace C at position 30 with (,C6I=)CN(=]O)4)-c22OcBn-]2CC(=O(CB-/C((=O)c)2c1C
replace ( at position 30 with o,C6I=)CN(=]O)4)-c22OcBn-]2CC(=OoCB-/C((=O)c)2c1C
replace O at position 10 with @,C6I=)CN(=]@)4)-c22OcBn-]2CC(=OoCB-/C((=O)c)2c1C
remove - from position 33,C6I=)CN(=]@)4)-c22OcBn-]2CC(=OoCB/C((=O)c)2c1C
replace O at position 18 with 3,C6I=)CN(=]@)4)-c223cBn-]2CC(=OoCB/C((=O)c)2c1C
add = at position 11,C6I=)CN(=]@=)4)-c223cBn-]2CC(=OoCB/C((=O)c)2c1C
remove ( from position 7,C6I=)CN=]@=)4)-c223cBn-]2CC(=OoCB/C((=O)c)2c1C
replace N at position 6 with n,C6I=)Cn=]@=)4)-c223cBn-]2CC(=OoCB/C((=O)c)2c1C
replace 2 at position 24 with n,C6I=)Cn=]@=)4)-c223cBn-]nCC(=OoCB/C((=O)c)2c1C
replace 2 at position 16 with O,C6I=)Cn=]@=)4)-cO23cBn-]nCC(=OoCB/C((=O)c)2c1C
add S at position 6,C6I=)CSn=]@=)4)-cO23cBn-]nCC(=OoCB/C((=O)c)2c1C
remove @ from position 10,C6I=)CSn=]=)4)-cO23cBn-]nCC(=OoCB/C((=O)c)2c1C
remove 2 from position 17,C6I=)CSn=]=)4)-cO3cBn-]nCC(=OoCB/C((=O)c)2c1C
replace n at position 20 with c,C6I=)CSn=]=)4)-cO3cBc-]nCC(=OoCB/C((=O)c)2c1C
replace ) at position 4 with +,C6I=+CSn=]=)4)-cO3cBc-]nCC(=OoCB/C((=O)c)2c1C
remove = from position 3,C6I+CSn=]=)4)-cO3cBc-]nCC(=OoCB/C((=O)c)2c1C
replace I at position 2 with 2,C62+CSn=]=)4)-cO3cBc-]nCC(=OoCB/C((=O)c)2c1C
remove S from position 5,C62+Cn=]=)4)-cO3cBc-]nCC(=OoCB/C((=O)c)2c1C
replace ] at position 20 with 4,C62+Cn=]=)4)-cO3cBc-4nCC(=OoCB/C((=O)c)2c1C
replace C at position 4 with I,C62+In=]=)4)-cO3cBc-4nCC(=OoCB/C((=O)c)2c1C
remove I from position 4,C62+n=]=)4)-cO3cBc-4nCC(=OoCB/C((=O)c)2c1C
remove c from position 12,C62+n=]=)4)-O3cBc-4nCC(=OoCB/C((=O)c)2c1C
add S at position 12,C62+n=]=)4)-SO3cBc-4nCC(=OoCB/C((=O)c)2c1C
replace / at position 29 with F,C62+n=]=)4)-SO3cBc-4nCC(=OoCBFC((=O)c)2c1C
add r at position 23,C62+n=]=)4)-SO3cBc-4nCCr(=OoCBFC((=O)c)2c1C
replace ( at position 32 with r,C62+n=]=)4)-SO3cBc-4nCCr(=OoCBFCr(=O)c)2c1C
remove - from position 11,C62+n=]=)4)SO3cBc-4nCCr(=OoCBFCr(=O)c)2c1C
replace 3 at position 13 with 5,C62+n=]=)4)SO5cBc-4nCCr(=OoCBFCr(=O)c)2c1C
add F at position 22,C62+n=]=)4)SO5cBc-4nCCFr(=OoCBFCr(=O)c)2c1C
add B at position 35,C62+n=]=)4)SO5cBc-4nCCFr(=OoCBFCr(=BO)c)2c1C
remove ) from position 8,C62+n=]=4)SO5cBc-4nCCFr(=OoCBFCr(=BO)c)2c1C
add r at position 10,C62+n=]=4)rSO5cBc-4nCCFr(=OoCBFCr(=BO)c)2c1C
add ) at position 34,C62+n=]=4)rSO5cBc-4nCCFr(=OoCBFCr()=BO)c)2c1C
remove r from position 10,C62+n=]=4)SO5cBc-4nCCFr(=OoCBFCr()=BO)c)2c1C
add ( at position 43,C62+n=]=4)SO5cBc-4nCCFr(=OoCBFCr()=BO)c)2c1(C
remove ) from position 9,C62+n=]=4SO5cBc-4nCCFr(=OoCBFCr()=BO)c)2c1(C
replace 2 at position 2 with @,C6@+n=]=4SO5cBc-4nCCFr(=OoCBFCr()=BO)c)2c1(C
remove F from position 20,C6@+n=]=4SO5cBc-4nCCr(=OoCBFCr()=BO)c)2c1(C
add 7 at position 43,C6@+n=]=4SO5cBc-4nCCr(=OoCBFCr()=BO)c)2c1(C7
replace n at position 17 with F,C6@+n=]=4SO5cBc-4FCCr(=OoCBFCr()=BO)c)2c1(C7
remove C from position 19,C6@+n=]=4SO5cBc-4FCr(=OoCBFCr()=BO)c)2c1(C7
remove ) from position 36,C6@+n=]=4SO5cBc-4FCr(=OoCBFCr()=BO)c2c1(C7
replace ) at position 34 with +,C6@+n=]=4SO5cBc-4FCr(=OoCBFCr()=BO+c2c1(C7
replace F at position 17 with =,C6@+n=]=4SO5cBc-4=Cr(=OoCBFCr()=BO+c2c1(C7
remove 2 from position 36,C6@+n=]=4SO5cBc-4=Cr(=OoCBFCr()=BO+cc1(C7
remove = from position 7,C6@+n=]4SO5cBc-4=Cr(=OoCBFCr()=BO+cc1(C7
remove C from position 17,C6@+n=]4SO5cBc-4=r(=OoCBFCr()=BO+cc1(C7
add 7 at position 37,C6@+n=]4SO5cBc-4=r(=OoCBFCr()=BO+cc1(7C7
add 2 at position 28,C6@+n=]4SO5cBc-4=r(=OoCBFCr(2)=BO+cc1(7C7
replace 5 at position 10 with =,C6@+n=]4SO=cBc-4=r(=OoCBFCr(2)=BO+cc1(7C7
remove n from position 4,C6@+=]4SO=cBc-4=r(=OoCBFCr(2)=BO+cc1(7C7
remove O from position 19,C6@+=]4SO=cBc-4=r(=oCBFCr(2)=BO+cc1(7C7
remove O from position 30,C6@+=]4SO=cBc-4=r(=oCBFCr(2)=B+cc1(7C7
add / at position 30,C6@+=]4SO=cBc-4=r(=oCBFCr(2)=B/+cc1(7C7
remove C from position 23,C6@+=]4SO=cBc-4=r(=oCBFr(2)=B/+cc1(7C7
remove F from position 22,C6@+=]4SO=cBc-4=r(=oCBr(2)=B/+cc1(7C7
add r at position 33,C6@+=]4SO=cBc-4=r(=oCBr(2)=B/+cc1r(7C7
remove c from position 30,C6@+=]4SO=cBc-4=r(=oCBr(2)=B/+c1r(7C7
remove + from position 29,C6@+=]4SO=cBc-4=r(=oCBr(2)=B/c1r(7C7
remove 4 from position 6,C6@+=]SO=cBc-4=r(=oCBr(2)=B/c1r(7C7
add ) at position 30,C6@+=]SO=cBc-4=r(=oCBr(2)=B/c1)r(7C7
replace r at position 21 with 6,C6@+=]SO=cBc-4=r(=oCB6(2)=B/c1)r(7C7
replace o at position 18 with 4,C6@+=]SO=cBc-4=r(=4CB6(2)=B/c1)r(7C7
remove 6 from position 21,C6@+=]SO=cBc-4=r(=4CB(2)=B/c1)r(7C7
remove 4 from position 18,C6@+=]SO=cBc-4=r(=CB(2)=B/c1)r(7C7
remove C from position 32,C6@+=]SO=cBc-4=r(=CB(2)=B/c1)r(77
replace = at position 17 with ),C6@+=]SO=cBc-4=r()CB(2)=B/c1)r(77
remove 4 from position 13,C6@+=]SO=cBc-=r()CB(2)=B/c1)r(77
remove ) from position 21,C6@+=]SO=cBc-=r()CB(2=B/c1)r(77
replace r at position 14 with 3,C6@+=]SO=cBc-=3()CB(2=B/c1)r(77
add l at position 7,C6@+=]SlO=cBc-=3()CB(2=B/c1)r(77
add - at position 23,C6@+=]SlO=cBc-=3()CB(2=-B/c1)r(77
replace / at position 25 with (,C6@+=]SlO=cBc-=3()CB(2=-B(c1)r(77
remove @ from position 2,C6+=]SlO=cBc-=3()CB(2=-B(c1)r(77
replace c at position 9 with 5,C6+=]SlO=5Bc-=3()CB(2=-B(c1)r(77
remove ) from position 27,C6+=]SlO=5Bc-=3()CB(2=-B(c1r(77
replace 7 at position 29 with ),C6+=]SlO=5Bc-=3()CB(2=-B(c1r()7
replace 7 at position 30 with 4,C6+=]SlO=5Bc-=3()CB(2=-B(c1r()4
remove ( from position 15,C6+=]SlO=5Bc-=3)CB(2=-B(c1r()4
replace O at position 7 with 6,C6+=]Sl6=5Bc-=3)CB(2=-B(c1r()4
remove = from position 20,C6+=]Sl6=5Bc-=3)CB(2-B(c1r()4
replace C at position 0 with S,S6+=]Sl6=5Bc-=3)CB(2-B(c1r()4
remove ) from position 27,S6+=]Sl6=5Bc-=3)CB(2-B(c1r(4
remove S from position 5,S6+=]l6=5Bc-=3)CB(2-B(c1r(4
remove ( from position 21,S6+=]l6=5Bc-=3)CB(2-Bc1r(4
replace ] at position 4 with r,S6+=rl6=5Bc-=3)CB(2-Bc1r(4
remove - from position 19,S6+=rl6=5Bc-=3)CB(2Bc1r(4
add s at position 17,S6+=rl6=5Bc-=3)CBs(2Bc1r(4
replace 1 at position 22 with /,S6+=rl6=5Bc-=3)CBs(2Bc/r(4
replace ( at position 24 with F,S6+=rl6=5Bc-=3)CBs(2Bc/rF4
remove = from position 12,S6+=rl6=5Bc-3)CBs(2Bc/rF4
replace c at position 10 with -,S6+=rl6=5B--3)CBs(2Bc/rF4
remove ( from position 17,S6+=rl6=5B--3)CBs2Bc/rF4
remove c from position 19,S6+=rl6=5B--3)CBs2B/rF4
remove C from position 14,S6+=rl6=5B--3)Bs2B/rF4
add r at position 2,S6r+=rl6=5B--3)Bs2B/rF4
remove F from position 21,S6r+=rl6=5B--3)Bs2B/r4
remove 6 from position 1,Sr+=rl6=5B--3)Bs2B/r4
remove + from position 2,Sr=rl6=5B--3)Bs2B/r4
remove 4 from position 19,Sr=rl6=5B--3)Bs2B/r
replace 3 at position 11 with F,Sr=rl6=5B--F)Bs2B/r
remove ) from position 12,Sr=rl6=5B--FBs2B/r
remove r from position 1,S=rl6=5B--FBs2B/r
add l at position 2,S=lrl6=5B--FBs2B/r
remove - from position 10,S=lrl6=5B-FBs2B/r
add S at position 7,S=lrl6=S5B-FBs2B/r
replace S at position 0 with o,o=lrl6=S5B-FBs2B/r
remove F from position 11,o=lrl6=S5B-Bs2B/r
remove l from position 2,o=rl6=S5B-Bs2B/r
remove S from position 6,o=rl6=5B-Bs2B/r
replace / at position 13 with N,o=rl6=5B-Bs2BNr
remove 2 from position 11,o=rl6=5B-BsBNr
add O at position 2,o=Orl6=5B-BsBNr
replace B at position 10 with ],o=Orl6=5B-]sBNr
add 6 at position 14,o=Orl6=5B-]sBN6r
remove s from position 11,o=Orl6=5B-]BN6r
remove = from position 6,o=Orl65B-]BN6r
replace r at position 3 with 4,o=O4l65B-]BN6r
add H at position 5,o=O4lH65B-]BN6r
replace = at position 1 with H,oHO4lH65B-]BN6r
remove 6 from position 13,oHO4lH65B-]BNr
add H at position 9,oHO4lH65BH-]BNr
remove O from position 2,oH4lH65BH-]BNr
remove 6 from position 5,oH4lH5BH-]BNr
replace H at position 4 with [,oH4l[5BH-]BNr
remove 4 from position 2,oHl[5BH-]BNr
remove l from position 2,oH[5BH-]BNr
replace B at position 8 with [,oH[5BH-][Nr
remove 5 from position 3,oH[BH-][Nr
remove [ from position 2,oHBH-][Nr
remove [ from position 6,oHBH-]Nr
remove r from position 7,oHBH-]N
replace - at position 4 with B,oHBHB]N
remove N from position 6,oHBHB]
remove B from position 4,oHBH]
add C at position 5,oHBH]C
replace ] at position 4 with -,oHBH-C
add B at position 3,oHBBH-C
add c at position 2,oHcBBH-C
replace B at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
