log,state
initialize: FC(F)(F)c1cccc2c1CCCC2,FC(F)(F)c1cccc2c1CCCC2
replace 2 at position 14 with +,FC(F)(F)c1cccc+c1CCCC2
add ( at position 18,FC(F)(F)c1cccc+c1C(CCC2
remove ( from position 2,FCF)(F)c1cccc+c1C(CCC2
add 2 at position 11,FCF)(F)c1cc2cc+c1C(CCC2
add 6 at position 0,6FCF)(F)c1cc2cc+c1C(CCC2
remove + from position 15,6FCF)(F)c1cc2ccc1C(CCC2
remove ) from position 4,6FCF(F)c1cc2ccc1C(CCC2
add + at position 22,6FCF(F)c1cc2ccc1C(CCC2+
replace C at position 16 with B,6FCF(F)c1cc2ccc1B(CCC2+
replace c at position 7 with ),6FCF(F))1cc2ccc1B(CCC2+
remove c from position 14,6FCF(F))1cc2cc1B(CCC2+
add ) at position 21,6FCF(F))1cc2cc1B(CCC2)+
remove F from position 1,6CF(F))1cc2cc1B(CCC2)+
remove C from position 17,6CF(F))1cc2cc1B(CC2)+
add B at position 9,6CF(F))1cBc2cc1B(CC2)+
replace c at position 8 with 2,6CF(F))12Bc2cc1B(CC2)+
remove ) from position 6,6CF(F)12Bc2cc1B(CC2)+
remove C from position 16,6CF(F)12Bc2cc1B(C2)+
remove C from position 16,6CF(F)12Bc2cc1B(2)+
add - at position 10,6CF(F)12Bc-2cc1B(2)+
remove c from position 12,6CF(F)12Bc-2c1B(2)+
add H at position 2,6CHF(F)12Bc-2c1B(2)+
remove H from position 2,6CF(F)12Bc-2c1B(2)+
remove F from position 4,6CF()12Bc-2c1B(2)+
add ] at position 11,6CF()12Bc-2]c1B(2)+
add - at position 16,6CF()12Bc-2]c1B(-2)+
remove B from position 14,6CF()12Bc-2]c1(-2)+
add O at position 8,6CF()12BOc-2]c1(-2)+
replace 1 at position 5 with 4,6CF()42BOc-2]c1(-2)+
add ] at position 4,6CF(])42BOc-2]c1(-2)+
replace C at position 1 with I,6IF(])42BOc-2]c1(-2)+
remove - from position 11,6IF(])42BOc2]c1(-2)+
remove ) from position 18,6IF(])42BOc2]c1(-2+
replace ( at position 15 with ),6IF(])42BOc2]c1)-2+
replace ) at position 15 with o,6IF(])42BOc2]c1o-2+
replace ) at position 5 with B,6IF(]B42BOc2]c1o-2+
remove - from position 16,6IF(]B42BOc2]c1o2+
replace O at position 9 with 3,6IF(]B42B3c2]c1o2+
add = at position 5,6IF(]=B42B3c2]c1o2+
remove ( from position 3,6IF]=B42B3c2]c1o2+
replace ] at position 3 with n,6IFn=B42B3c2]c1o2+
replace ] at position 12 with n,6IFn=B42B3c2nc1o2+
replace B at position 8 with O,6IFn=B42O3c2nc1o2+
add S at position 3,6IFSn=B42O3c2nc1o2+
remove = from position 5,6IFSnB42O3c2nc1o2+
remove O from position 8,6IFSnB423c2nc1o2+
replace 2 at position 10 with l,6IFSnB423clnc1o2+
replace F at position 2 with ),6I)SnB423clnc1o2+
remove I from position 1,6)SnB423clnc1o2+
replace ) at position 1 with 3,63SnB423clnc1o2+
remove S from position 2,63nB423clnc1o2+
replace 1 at position 11 with 6,63nB423clnc6o2+
remove c from position 7,63nB423lnc6o2+
add n at position 4,63nBn423lnc6o2+
add s at position 13,63nBn423lnc6os2+
add 1 at position 12,63nBn423lnc61os2+
remove s from position 14,63nBn423lnc61o2+
remove 6 from position 11,63nBn423lnc1o2+
replace B at position 3 with F,63nFn423lnc1o2+
replace 2 at position 6 with /,63nFn4/3lnc1o2+
replace F at position 3 with 4,63n4n4/3lnc1o2+
add F at position 11,63n4n4/3lncF1o2+
add 5 at position 15,63n4n4/3lncF1o25+
add I at position 1,6I3n4n4/3lncF1o25+
add l at position 4,6I3nl4n4/3lncF1o25+
remove l from position 10,6I3nl4n4/3ncF1o25+
add N at position 1,6NI3nl4n4/3ncF1o25+
add r at position 4,6NI3rnl4n4/3ncF1o25+
add 7 at position 13,6NI3rnl4n4/3n7cF1o25+
remove l from position 6,6NI3rn4n4/3n7cF1o25+
replace 7 at position 12 with s,6NI3rn4n4/3nscF1o25+
replace 4 at position 8 with H,6NI3rn4nH/3nscF1o25+
remove / from position 9,6NI3rn4nH3nscF1o25+
remove + from position 18,6NI3rn4nH3nscF1o25
replace 5 at position 17 with ),6NI3rn4nH3nscF1o2)
replace H at position 8 with =,6NI3rn4n=3nscF1o2)
remove = from position 8,6NI3rn4n3nscF1o2)
remove F from position 12,6NI3rn4n3nsc1o2)
remove n from position 9,6NI3rn4n3sc1o2)
remove s from position 9,6NI3rn4n3c1o2)
remove n from position 5,6NI3r4n3c1o2)
replace 3 at position 7 with 2,6NI3r4n2c1o2)
replace I at position 2 with 7,6N73r4n2c1o2)
remove N from position 1,673r4n2c1o2)
remove 4 from position 4,673rn2c1o2)
remove ) from position 10,673rn2c1o2
replace 3 at position 2 with =,67=rn2c1o2
remove = from position 2,67rn2c1o2
remove c from position 5,67rn21o2
remove 1 from position 5,67rn2o2
add c at position 7,67rn2o2c
remove 6 from position 0,7rn2o2c
replace 2 at position 5 with B,7rn2oBc
remove 7 from position 0,rn2oBc
add B at position 6,rn2oBcB
add o at position 2,rno2oBcB
replace B at position 5 with 6,rno2o6cB
replace o at position 4 with 4,rno246cB
remove 6 from position 5,rno24cB
remove o from position 2,rn24cB
remove c from position 4,rn24B
replace 2 at position 2 with ),rn)4B
remove B from position 4,rn)4
add 5 at position 4,rn)45
replace 4 at position 3 with 3,rn)35
add H at position 4,rn)3H5
remove r from position 0,n)3H5
replace 3 at position 2 with 7,n)7H5
add 7 at position 0,7n)7H5
add 1 at position 0,17n)7H5
remove n from position 2,17)7H5
remove 7 from position 3,17)H5
replace 1 at position 0 with o,o7)H5
replace ) at position 2 with 4,o74H5
remove 5 from position 4,o74H
add ) at position 4,o74H)
remove o from position 0,74H)
remove H from position 2,74)
replace 7 at position 0 with /,/4)
remove ) from position 2,/4
replace / at position 0 with r,r4
remove r from position 0,4
add S at position 0,S4
add ] at position 2,S4]
remove 4 from position 1,S]
replace ] at position 1 with -,S-
remove - from position 1,S
add r at position 0,rS
remove S from position 1,r
replace r at position 0 with 5,5
add 6 at position 0,65
replace 5 at position 1 with I,6I
remove 6 from position 0,I
add l at position 0,lI
remove I from position 1,l
add S at position 0,Sl
replace S at position 0 with o,ol
remove l from position 1,o
remove o from position 0,
final: ,
