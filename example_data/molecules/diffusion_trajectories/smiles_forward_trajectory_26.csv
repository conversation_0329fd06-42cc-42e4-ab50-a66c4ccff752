log,state
initialize: CC1=NC(SCC(=O)Nc2ccccc2C(F)(F)F)=NC(=O)[C@H]1Cc1ccccc1,CC1=NC(SCC(=O)Nc2ccccc2C(F)(F)F)=NC(=O)[C@H]1Cc1ccccc1
replace F at position 28 with +,CC1=NC(SCC(=O)Nc2ccccc2C(F)(+)F)=NC(=O)[C@H]1Cc1ccccc1
add H at position 51,CC1=NC(SCC(=O)Nc2ccccc2C(F)(+)F)=NC(=O)[C@H]1Cc1cccHcc1
add ) at position 44,CC1=NC(SCC(=O)Nc2ccccc2C(F)(+)F)=NC(=O)[C@H])1Cc1cccHcc1
add 2 at position 22,CC1=NC(SCC(=O)Nc2ccccc22C(F)(+)F)=NC(=O)[C@H])1Cc1cccHcc1
add # at position 50,CC1=NC(SCC(=O)Nc2ccccc22C(F)(+)F)=NC(=O)[C@H])1Cc1#cccHcc1
remove ) from position 39,CC1=NC(SCC(=O)Nc2ccccc22C(F)(+)F)=NC(=O[C@H])1Cc1#cccHcc1
remove + from position 29,CC1=NC(SCC(=O)Nc2ccccc22C(F)()F)=NC(=O[C@H])1Cc1#cccHcc1
add S at position 11,CC1=NC(SCC(S=O)Nc2ccccc22C(F)()F)=NC(=O[C@H])1Cc1#cccHcc1
add C at position 0,CCC1=NC(SCC(S=O)Nc2ccccc22C(F)()F)=NC(=O[C@H])1Cc1#cccHcc1
remove ) from position 15,CCC1=NC(SCC(S=ONc2ccccc22C(F)()F)=NC(=O[C@H])1Cc1#cccHcc1
add r at position 34,CCC1=NC(SCC(S=ONc2ccccc22C(F)()F)=rNC(=O[C@H])1Cc1#cccHcc1
remove = from position 38,CCC1=NC(SCC(S=ONc2ccccc22C(F)()F)=rNC(O[C@H])1Cc1#cccHcc1
remove ) from position 32,CCC1=NC(SCC(S=ONc2ccccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc1
remove 2 from position 17,CCC1=NC(SCC(S=ONcccccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc1
replace = at position 4 with O,CCC1ONC(SCC(S=ONcccccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc1
remove 1 from position 54,CCC1ONC(SCC(S=ONcccccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc
remove N from position 15,CCC1ONC(SCC(S=Occcccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc
remove S from position 12,CCC1ONC(SCC(=Occcccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc
remove ( from position 33,CCC1ONC(SCC(=Occcccc22C(F)()F=rNCO[C@H])1Cc1#cccHcc
remove C from position 32,CCC1ONC(SCC(=Occcccc22C(F)()F=rNO[C@H])1Cc1#cccHcc
add - at position 21,CCC1ONC(SCC(=Occcccc2-2C(F)()F=rNO[C@H])1Cc1#cccHcc
remove c from position 42,CCC1ONC(SCC(=Occcccc2-2C(F)()F=rNO[C@H])1C1#cccHcc
remove 1 from position 3,CCCONC(SCC(=Occcccc2-2C(F)()F=rNO[C@H])1C1#cccHcc
add / at position 31,CCCONC(SCC(=Occcccc2-2C(F)()F=r/NO[C@H])1C1#cccHcc
add - at position 13,CCCONC(SCC(=O-cccccc2-2C(F)()F=r/NO[C@H])1C1#cccHcc
add ] at position 22,CCCONC(SCC(=O-cccccc2-]2C(F)()F=r/NO[C@H])1C1#cccHcc
add - at position 33,CCCONC(SCC(=O-cccccc2-]2C(F)()F=r-/NO[C@H])1C1#cccHcc
remove ) from position 29,CCCONC(SCC(=O-cccccc2-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
add O at position 17,CCCONC(SCC(=O-cccOccc2-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
replace = at position 11 with 3,CCCONC(SCC(3O-cccOccc2-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
add ] at position 9,CCCONC(SC]C(3O-cccOccc2-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
replace C at position 2 with I,CCIONC(SC]C(3O-cccOccc2-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
remove 2 from position 22,CCIONC(SC]C(3O-cccOccc-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
remove [ from position 37,CCIONC(SC]C(3O-cccOccc-]2C(F)(F=r-/NOC@H])1C1#cccHcc
replace F at position 30 with (,CCIONC(SC]C(3O-cccOccc-]2C(F)((=r-/NOC@H])1C1#cccHcc
replace ( at position 30 with o,CCIONC(SC]C(3O-cccOccc-]2C(F)(o=r-/NOC@H])1C1#cccHcc
replace C at position 10 with @,CCIONC(SC]@(3O-cccOccc-]2C(F)(o=r-/NOC@H])1C1#cccHcc
remove - from position 33,CCIONC(SC]@(3O-cccOccc-]2C(F)(o=r/NOC@H])1C1#cccHcc
replace O at position 18 with 3,CCIONC(SC]@(3O-ccc3ccc-]2C(F)(o=r/NOC@H])1C1#cccHcc
add = at position 11,CCIONC(SC]@=(3O-ccc3ccc-]2C(F)(o=r/NOC@H])1C1#cccHcc
remove S from position 7,CCIONC(C]@=(3O-ccc3ccc-]2C(F)(o=r/NOC@H])1C1#cccHcc
replace ( at position 6 with n,CCIONCnC]@=(3O-ccc3ccc-]2C(F)(o=r/NOC@H])1C1#cccHcc
replace 2 at position 24 with n,CCIONCnC]@=(3O-ccc3ccc-]nC(F)(o=r/NOC@H])1C1#cccHcc
replace c at position 16 with N,CCIONCnC]@=(3O-cNc3ccc-]nC(F)(o=r/NOC@H])1C1#cccHcc
add S at position 6,CCIONCSnC]@=(3O-cNc3ccc-]nC(F)(o=r/NOC@H])1C1#cccHcc
remove @ from position 10,CCIONCSnC]=(3O-cNc3ccc-]nC(F)(o=r/NOC@H])1C1#cccHcc
remove c from position 17,CCIONCSnC]=(3O-cN3ccc-]nC(F)(o=r/NOC@H])1C1#cccHcc
replace c at position 46 with 5,CCIONCSnC]=(3O-cN3ccc-]nC(F)(o=r/NOC@H])1C1#cc5Hcc
replace ( at position 28 with ],CCIONCSnC]=(3O-cN3ccc-]nC(F)]o=r/NOC@H])1C1#cc5Hcc
add 1 at position 25,CCIONCSnC]=(3O-cN3ccc-]nC1(F)]o=r/NOC@H])1C1#cc5Hcc
add c at position 18,CCIONCSnC]=(3O-cN3cccc-]nC1(F)]o=r/NOC@H])1C1#cc5Hcc
add l at position 32,CCIONCSnC]=(3O-cN3cccc-]nC1(F)]ol=r/NOC@H])1C1#cc5Hcc
add 5 at position 44,CCIONCSnC]=(3O-cN3cccc-]nC1(F)]ol=r/NOC@H])15C1#cc5Hcc
remove ) from position 29,CCIONCSnC]=(3O-cN3cccc-]nC1(F]ol=r/NOC@H])15C1#cc5Hcc
add n at position 16,CCIONCSnC]=(3O-cnN3cccc-]nC1(F]ol=r/NOC@H])15C1#cc5Hcc
add s at position 27,CCIONCSnC]=(3O-cnN3cccc-]nCs1(F]ol=r/NOC@H])15C1#cc5Hcc
add 1 at position 25,CCIONCSnC]=(3O-cnN3cccc-]1nCs1(F]ol=r/NOC@H])15C1#cc5Hcc
remove 1 from position 29,CCIONCSnC]=(3O-cnN3cccc-]1nCs(F]ol=r/NOC@H])15C1#cc5Hcc
remove H from position 52,CCIONCSnC]=(3O-cnN3cccc-]1nCs(F]ol=r/NOC@H])15C1#cc5cc
replace O at position 13 with C,CCIONCSnC]=(3C-cnN3cccc-]1nCs(F]ol=r/NOC@H])15C1#cc5cc
replace n at position 26 with /,CCIONCSnC]=(3C-cnN3cccc-]1/Cs(F]ol=r/NOC@H])15C1#cc5cc
replace C at position 13 with 4,CCIONCSnC]=(34-cnN3cccc-]1/Cs(F]ol=r/NOC@H])15C1#cc5cc
add F at position 22,CCIONCSnC]=(34-cnN3cccFc-]1/Cs(F]ol=r/NOC@H])15C1#cc5cc
add s at position 49,CCIONCSnC]=(34-cnN3cccFc-]1/Cs(F]ol=r/NOC@H])15C1s#cc5cc
remove c from position 21,CCIONCSnC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H])15C1s#cc5cc
add I at position 3,CCIIONCSnC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H])15C1s#cc5cc
add l at position 9,CCIIONCSnlC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H])15C1s#cc5cc
remove ) from position 45,CCIIONCSnlC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1s#cc5cc
remove C from position 0,CIIONCSnlC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1s#cc5cc
remove O from position 3,CIINCSnlC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1s#cc5cc
remove # from position 48,CIINCSnlC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1scc5cc
replace 4 at position 13 with (,CIINCSnlC]=(3(-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1scc5cc
remove s from position 47,CIINCSnlC]=(3(-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1cc5cc
remove 1 from position 43,CIINCSnlC]=(3(-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]5C1cc5cc
remove N from position 17,CIINCSnlC]=(3(-cn3ccFc-]1/Cs(F]ol=r/NOC@H]5C1cc5cc
remove c from position 19,CIINCSnlC]=(3(-cn3cFc-]1/Cs(F]ol=r/NOC@H]5C1cc5cc
remove 1 from position 43,CIINCSnlC]=(3(-cn3cFc-]1/Cs(F]ol=r/NOC@H]5Ccc5cc
remove ] from position 9,CIINCSnlC=(3(-cn3cFc-]1/Cs(F]ol=r/NOC@H]5Ccc5cc
replace S at position 5 with c,CIINCcnlC=(3(-cn3cFc-]1/Cs(F]ol=r/NOC@H]5Ccc5cc
remove ( from position 26,CIINCcnlC=(3(-cn3cFc-]1/CsF]ol=r/NOC@H]5Ccc5cc
remove @ from position 36,CIINCcnlC=(3(-cn3cFc-]1/CsF]ol=r/NOCH]5Ccc5cc
remove l from position 7,CIINCcnC=(3(-cn3cFc-]1/CsF]ol=r/NOCH]5Ccc5cc
remove F from position 17,CIINCcnC=(3(-cn3cc-]1/CsF]ol=r/NOCH]5Ccc5cc
add 7 at position 37,CIINCcnC=(3(-cn3cc-]1/CsF]ol=r/NOCH]57Ccc5cc
add 2 at position 28,CIINCcnC=(3(-cn3cc-]1/CsF]ol2=r/NOCH]57Ccc5cc
replace 3 at position 10 with =,CIINCcnC=(=(-cn3cc-]1/CsF]ol2=r/NOCH]57Ccc5cc
remove C from position 4,CIINcnC=(=(-cn3cc-]1/CsF]ol2=r/NOCH]57Ccc5cc
remove 1 from position 19,CIINcnC=(=(-cn3cc-]/CsF]ol2=r/NOCH]57Ccc5cc
remove N from position 30,CIINcnC=(=(-cn3cc-]/CsF]ol2=r/OCH]57Ccc5cc
add B at position 40,CIINcnC=(=(-cn3cc-]/CsF]ol2=r/OCH]57Ccc5Bcc
add 6 at position 37,CIINcnC=(=(-cn3cc-]/CsF]ol2=r/OCH]57C6cc5Bcc
remove F from position 22,CIINcnC=(=(-cn3cc-]/Cs]ol2=r/OCH]57C6cc5Bcc
add r at position 33,CIINcnC=(=(-cn3cc-]/Cs]ol2=r/OCH]r57C6cc5Bcc
remove c from position 42,CIINcnC=(=(-cn3cc-]/Cs]ol2=r/OCH]r57C6cc5Bc
remove I from position 1,CINcnC=(=(-cn3cc-]/Cs]ol2=r/OCH]r57C6cc5Bc
replace C at position 29 with S,CINcnC=(=(-cn3cc-]/Cs]ol2=r/OSH]r57C6cc5Bc
replace I at position 1 with 6,C6NcnC=(=(-cn3cc-]/Cs]ol2=r/OSH]r57C6cc5Bc
replace H at position 30 with ),C6NcnC=(=(-cn3cc-]/Cs]ol2=r/OS)]r57C6cc5Bc
replace c at position 37 with 5,C6NcnC=(=(-cn3cc-]/Cs]ol2=r/OS)]r57C65c5Bc
replace / at position 18 with 5,C6NcnC=(=(-cn3cc-]5Cs]ol2=r/OS)]r57C65c5Bc
remove 5 from position 37,C6NcnC=(=(-cn3cc-]5Cs]ol2=r/OS)]r57C6c5Bc
remove 5 from position 18,C6NcnC=(=(-cn3cc-]Cs]ol2=r/OS)]r57C6c5Bc
remove 5 from position 32,C6NcnC=(=(-cn3cc-]Cs]ol2=r/OS)]r7C6c5Bc
replace ] at position 17 with ),C6NcnC=(=(-cn3cc-)Cs]ol2=r/OS)]r7C6c5Bc
remove c from position 38,C6NcnC=(=(-cn3cc-)Cs]ol2=r/OS)]r7C6c5B
add 5 at position 32,C6NcnC=(=(-cn3cc-)Cs]ol2=r/OS)]r57C6c5B
replace ) at position 29 with 4,C6NcnC=(=(-cn3cc-)Cs]ol2=r/OS4]r57C6c5B
add H at position 33,C6NcnC=(=(-cn3cc-)Cs]ol2=r/OS4]r5H7C6c5B
remove ( from position 7,C6NcnC==(-cn3cc-)Cs]ol2=r/OS4]r5H7C6c5B
replace ) at position 16 with 7,C6NcnC==(-cn3cc-7Cs]ol2=r/OS4]r5H7C6c5B
add 7 at position 1,C76NcnC==(-cn3cc-7Cs]ol2=r/OS4]r5H7C6c5B
add 1 at position 2,C716NcnC==(-cn3cc-7Cs]ol2=r/OS4]r5H7C6c5B
remove s from position 20,C716NcnC==(-cn3cc-7C]ol2=r/OS4]r5H7C6c5B
remove O from position 27,C716NcnC==(-cn3cc-7C]ol2=r/S4]r5H7C6c5B
replace N at position 4 with o,C716ocnC==(-cn3cc-7C]ol2=r/S4]r5H7C6c5B
replace C at position 19 with 3,C716ocnC==(-cn3cc-73]ol2=r/S4]r5H7C6c5B
remove 5 from position 37,C716ocnC==(-cn3cc-73]ol2=r/S4]r5H7C6cB
add ) at position 32,C716ocnC==(-cn3cc-73]ol2=r/S4]r5)H7C6cB
remove 7 from position 1,C16ocnC==(-cn3cc-73]ol2=r/S4]r5)H7C6cB
remove l from position 21,C16ocnC==(-cn3cc-73]o2=r/S4]r5)H7C6cB
replace c at position 14 with /,C16ocnC==(-cn3/c-73]o2=r/S4]r5)H7C6cB
remove = from position 8,C16ocnC=(-cn3/c-73]o2=r/S4]r5)H7C6cB
replace c at position 4 with -,C16o-nC=(-cn3/c-73]o2=r/S4]r5)H7C6cB
remove - from position 15,C16o-nC=(-cn3/c73]o2=r/S4]r5)H7C6cB
remove 6 from position 32,C16o-nC=(-cn3/c73]o2=r/S4]r5)H7CcB
replace ] at position 25 with o,C16o-nC=(-cn3/c73]o2=r/S4or5)H7CcB
replace ( at position 8 with [,C16o-nC=[-cn3/c73]o2=r/S4or5)H7CcB
replace ) at position 28 with (,C16o-nC=[-cn3/c73]o2=r/S4or5(H7CcB
remove S from position 23,C16o-nC=[-cn3/c73]o2=r/4or5(H7CcB
replace 5 at position 26 with r,C16o-nC=[-cn3/c73]o2=r/4orr(H7CcB
add 5 at position 4,C16o5-nC=[-cn3/c73]o2=r/4orr(H7CcB
add 6 at position 12,C16o5-nC=[-c6n3/c73]o2=r/4orr(H7CcB
replace c at position 33 with S,C16o5-nC=[-c6n3/c73]o2=r/4orr(H7CSB
replace 7 at position 31 with (,C16o5-nC=[-c6n3/c73]o2=r/4orr(H(CSB
add l at position 5,C16o5l-nC=[-c6n3/c73]o2=r/4orr(H(CSB
remove o from position 21,C16o5l-nC=[-c6n3/c73]2=r/4orr(H(CSB
add S at position 15,C16o5l-nC=[-c6nS3/c73]2=r/4orr(H(CSB
replace 1 at position 1 with o,Co6o5l-nC=[-c6nS3/c73]2=r/4orr(H(CSB
remove = from position 23,Co6o5l-nC=[-c6nS3/c73]2r/4orr(H(CSB
remove 5 from position 4,Co6ol-nC=[-c6nS3/c73]2r/4orr(H(CSB
remove n from position 13,Co6ol-nC=[-c6S3/c73]2r/4orr(H(CSB
replace [ at position 9 with C,Co6ol-nC=C-c6S3/c73]2r/4orr(H(CSB
replace c at position 16 with N,Co6ol-nC=C-c6S3/N73]2r/4orr(H(CSB
remove ( from position 29,Co6ol-nC=C-c6S3/N73]2r/4orr(HCSB
remove 4 from position 23,Co6ol-nC=C-c6S3/N73]2r/orr(HCSB
remove S from position 13,Co6ol-nC=C-c63/N73]2r/orr(HCSB
replace C at position 7 with 4,Co6ol-n4=C-c63/N73]2r/orr(HCSB
add H at position 10,Co6ol-n4=CH-c63/N73]2r/orr(HCSB
replace o at position 3 with F,Co6Fl-n4=CH-c63/N73]2r/orr(HCSB
remove H from position 27,Co6Fl-n4=CH-c63/N73]2r/orr(CSB
add H at position 18,Co6Fl-n4=CH-c63/N7H3]2r/orr(CSB
remove - from position 5,Co6Fln4=CH-c63/N7H3]2r/orr(CSB
remove c from position 11,Co6Fln4=CH-63/N7H3]2r/orr(CSB
replace C at position 8 with [,Co6Fln4=[H-63/N7H3]2r/orr(CSB
remove n from position 5,Co6Fl4=[H-63/N7H3]2r/orr(CSB
remove 4 from position 5,Co6Fl=[H-63/N7H3]2r/orr(CSB
replace 2 at position 17 with [,Co6Fl=[H-63/N7H3][r/orr(CSB
remove [ from position 6,Co6Fl=H-63/N7H3][r/orr(CSB
remove C from position 23,Co6Fl=H-63/N7H3][r/orr(SB
add H at position 23,Co6Fl=H-63/N7H3][r/orr(HSB
replace H at position 13 with C,Co6Fl=H-63/N7C3][r/orr(HSB
remove o from position 19,Co6Fl=H-63/N7C3][r/rr(HSB
remove ] from position 15,Co6Fl=H-63/N7C3[r/rr(HSB
remove B from position 23,Co6Fl=H-63/N7C3[r/rr(HS
remove N from position 11,Co6Fl=H-63/7C3[r/rr(HS
remove S from position 21,Co6Fl=H-63/7C3[r/rr(H
remove C from position 12,Co6Fl=H-63/73[r/rr(H
replace l at position 4 with +,Co6F+=H-63/73[r/rr(H
replace / at position 15 with +,Co6F+=H-63/73[r+rr(H
remove + from position 4,Co6F=H-63/73[r+rr(H
replace 3 at position 8 with 2,Co6F=H-62/73[r+rr(H
replace 6 at position 2 with 5,Co5F=H-62/73[r+rr(H
remove / from position 9,Co5F=H-6273[r+rr(H
remove [ from position 11,Co5F=H-6273r+rr(H
remove - from position 6,Co5F=H6273r+rr(H
remove ( from position 14,Co5F=H6273r+rrH
remove 2 from position 7,Co5F=H673r+rrH
replace o at position 1 with F,CF5F=H673r+rrH
replace 7 at position 7 with ),CF5F=H6)3r+rrH
add I at position 2,CFI5F=H6)3r+rrH
replace I at position 2 with 7,CF75F=H6)3r+rrH
remove H from position 14,CF75F=H6)3r+rr
add / at position 0,/CF75F=H6)3r+rr
replace F at position 5 with N,/CF75N=H6)3r+rr
add c at position 1,/cCF75N=H6)3r+rr
remove + from position 13,/cCF75N=H6)3rrr
remove 7 from position 4,/cCF5N=H6)3rrr
replace = at position 6 with [,/cCF5N[H6)3rrr
remove 3 from position 10,/cCF5N[H6)rrr
add # at position 6,/cCF5N#[H6)rrr
remove 6 from position 9,/cCF5N#[H)rrr
replace r at position 11 with 5,/cCF5N#[H)r5r
replace ) at position 9 with 3,/cCF5N#[H3r5r
add 1 at position 13,/cCF5N#[H3r5r1
remove 3 from position 9,/cCF5N#[Hr5r1
remove H from position 8,/cCF5N#[r5r1
replace r at position 8 with 3,/cCF5N#[35r1
replace # at position 6 with =,/cCF5N=[35r1
remove 5 from position 9,/cCF5N=[3r1
remove F from position 3,/cC5N=[3r1
replace C at position 2 with /,/c/5N=[3r1
replace / at position 2 with (,/c(5N=[3r1
add r at position 8,/c(5N=[3rr1
replace 1 at position 10 with H,/c(5N=[3rrH
add # at position 7,/c(5N=[#3rrH
remove c from position 1,/(5N=[#3rrH
remove ( from position 1,/5N=[#3rrH
add l at position 3,/5Nl=[#3rrH
remove 3 from position 7,/5Nl=[#rrH
remove l from position 3,/5N=[#rrH
remove H from position 8,/5N=[#rr
remove r from position 7,/5N=[#r
replace # at position 5 with +,/5N=[+r
remove r from position 6,/5N=[+
add C at position 6,/5N=[+C
add 7 at position 2,/57N=[+C
remove 7 from position 2,/5N=[+C
replace / at position 0 with C,C5N=[+C
remove N from position 2,C5=[+C
remove = from position 2,C5[+C
replace + at position 3 with o,C5[oC
remove [ from position 2,C5oC
replace 5 at position 1 with ),C)oC
replace ) at position 1 with B,CBoC
add 3 at position 0,3CBoC
replace C at position 4 with 3,3CBo3
add 1 at position 3,3CB1o3
add - at position 4,3CB1-o3
replace B at position 2 with ],3C]1-o3
remove 1 from position 3,3C]-o3
remove o from position 4,3C]-3
replace C at position 1 with O,3O]-3
add # at position 4,3O]-#3
add l at position 2,3Ol]-#3
remove # from position 5,3Ol]-3
replace ] at position 3 with =,3Ol=-3
remove l from position 2,3O=-3
replace 3 at position 4 with F,3O=-F
add ] at position 4,3O=-]F
remove F from position 5,3O=-]
remove = from position 2,3O-]
remove 3 from position 0,O-]
add r at position 0,rO-]
replace O at position 1 with 7,r7-]
remove r from position 0,7-]
remove 7 from position 0,-]
replace - at position 0 with o,o]
add r at position 0,ro]
replace o at position 1 with c,rc]
add c at position 1,rcc]
add ( at position 3,rcc(]
add C at position 5,rcc(]C
add ( at position 4,rcc((]C
replace ] at position 5 with C,rcc((CC
add = at position 0,=rcc((CC
replace = at position 0 with [,[rcc((CC
replace c at position 2 with 2,[r2c((CC
replace C at position 7 with I,[r2c((CI
add 2 at position 4,[r2c2((CI
remove 2 from position 2,[rc2((CI
remove C from position 6,[rc2((I
remove c from position 2,[r2((I
remove [ from position 0,r2((I
replace ( at position 2 with O,r2O(I
remove 2 from position 1,rO(I
remove ( from position 2,rOI
remove I from position 2,rO
remove r from position 0,O
remove O from position 0,
final: ,
