log,state
initialize: O=[N+]([O-])c1c(Nc2cccc3ncccc23)ncnc1N1CCN(c2cccc(Cl)c2)CC1,O=[N+]([O-])c1c(Nc2cccc3ncccc23)ncnc1N1CCN(c2cccc(Cl)c2)CC1
replace c at position 28 with +,O=[N+]([O-])c1c(Nc2cccc3nccc+23)ncnc1N1CCN(c2cccc(Cl)c2)CC1
add s at position 58,O=[N+]([O-])c1c(Nc2cccc3nccc+23)ncnc1N1CCN(c2cccc(Cl)c2)CCs1
replace N at position 37 with (,O=[N+]([O-])c1c(Nc2cccc3nccc+23)ncnc1(1CCN(c2cccc(Cl)c2)CCs1
remove ] from position 5,O=[N+([O-])c1c(Nc2cccc3nccc+23)ncnc1(1CCN(c2cccc(Cl)c2)CCs1
add 2 at position 22,O=[N+([O-])c1c(Nc2cccc23nccc+23)ncnc1(1CCN(c2cccc(Cl)c2)CCs1
add # at position 50,O=[N+([O-])c1c(Nc2cccc23nccc+23)ncnc1(1CCN(c2cccc(#Cl)c2)CCs1
remove C from position 39,O=[N+([O-])c1c(Nc2cccc23nccc+23)ncnc1(1CN(c2cccc(#Cl)c2)CCs1
remove 2 from position 29,O=[N+([O-])c1c(Nc2cccc23nccc+3)ncnc1(1CN(c2cccc(#Cl)c2)CCs1
add S at position 11,O=[N+([O-])Sc1c(Nc2cccc23nccc+3)ncnc1(1CN(c2cccc(#Cl)c2)CCs1
add C at position 0,CO=[N+([O-])Sc1c(Nc2cccc23nccc+3)ncnc1(1CN(c2cccc(#Cl)c2)CCs1
remove c from position 15,CO=[N+([O-])Sc1(Nc2cccc23nccc+3)ncnc1(1CN(c2cccc(#Cl)c2)CCs1
add r at position 34,CO=[N+([O-])Sc1(Nc2cccc23nccc+3)ncrnc1(1CN(c2cccc(#Cl)c2)CCs1
remove ( from position 38,CO=[N+([O-])Sc1(Nc2cccc23nccc+3)ncrnc11CN(c2cccc(#Cl)c2)CCs1
remove n from position 32,CO=[N+([O-])Sc1(Nc2cccc23nccc+3)crnc11CN(c2cccc(#Cl)c2)CCs1
remove c from position 17,CO=[N+([O-])Sc1(N2cccc23nccc+3)crnc11CN(c2cccc(#Cl)c2)CCs1
replace N at position 4 with O,CO=[O+([O-])Sc1(N2cccc23nccc+3)crnc11CN(c2cccc(#Cl)c2)CCs1
remove C from position 54,CO=[O+([O-])Sc1(N2cccc23nccc+3)crnc11CN(c2cccc(#Cl)c2)Cs1
remove ( from position 15,CO=[O+([O-])Sc1N2cccc23nccc+3)crnc11CN(c2cccc(#Cl)c2)Cs1
remove S from position 12,CO=[O+([O-])c1N2cccc23nccc+3)crnc11CN(c2cccc(#Cl)c2)Cs1
remove 1 from position 33,CO=[O+([O-])c1N2cccc23nccc+3)crnc1CN(c2cccc(#Cl)c2)Cs1
remove c from position 32,CO=[O+([O-])c1N2cccc23nccc+3)crn1CN(c2cccc(#Cl)c2)Cs1
add - at position 21,CO=[O+([O-])c1N2cccc2-3nccc+3)crn1CN(c2cccc(#Cl)c2)Cs1
remove c from position 42,CO=[O+([O-])c1N2cccc2-3nccc+3)crn1CN(c2ccc(#Cl)c2)Cs1
remove [ from position 3,CO=O+([O-])c1N2cccc2-3nccc+3)crn1CN(c2ccc(#Cl)c2)Cs1
add / at position 31,CO=O+([O-])c1N2cccc2-3nccc+3)cr/n1CN(c2ccc(#Cl)c2)Cs1
add - at position 13,CO=O+([O-])c1-N2cccc2-3nccc+3)cr/n1CN(c2ccc(#Cl)c2)Cs1
add ] at position 22,CO=O+([O-])c1-N2cccc2-]3nccc+3)cr/n1CN(c2ccc(#Cl)c2)Cs1
add - at position 33,CO=O+([O-])c1-N2cccc2-]3nccc+3)cr-/n1CN(c2ccc(#Cl)c2)Cs1
remove 3 from position 29,CO=O+([O-])c1-N2cccc2-]3nccc+)cr-/n1CN(c2ccc(#Cl)c2)Cs1
add O at position 17,CO=O+([O-])c1-N2cOccc2-]3nccc+)cr-/n1CN(c2ccc(#Cl)c2)Cs1
replace c at position 11 with 3,CO=O+([O-])31-N2cOccc2-]3nccc+)cr-/n1CN(c2ccc(#Cl)c2)Cs1
add - at position 54,CO=O+([O-])31-N2cOccc2-]3nccc+)cr-/n1CN(c2ccc(#Cl)c2)C-s1
replace + at position 29 with (,CO=O+([O-])31-N2cOccc2-]3nccc()cr-/n1CN(c2ccc(#Cl)c2)C-s1
remove l from position 48,CO=O+([O-])31-N2cOccc2-]3nccc()cr-/n1CN(c2ccc(#C)c2)C-s1
remove 2 from position 21,CO=O+([O-])31-N2cOccc-]3nccc()cr-/n1CN(c2ccc(#C)c2)C-s1
remove ) from position 29,CO=O+([O-])31-N2cOccc-]3nccc(cr-/n1CN(c2ccc(#C)c2)C-s1
remove C from position 50,CO=O+([O-])31-N2cOccc-]3nccc(cr-/n1CN(c2ccc(#C)c2)-s1
replace 1 at position 52 with N,CO=O+([O-])31-N2cOccc-]3nccc(cr-/n1CN(c2ccc(#C)c2)-sN
replace ( at position 28 with ],CO=O+([O-])31-N2cOccc-]3nccc]cr-/n1CN(c2ccc(#C)c2)-sN
replace N at position 52 with @,CO=O+([O-])31-N2cOccc-]3nccc]cr-/n1CN(c2ccc(#C)c2)-s@
remove O from position 7,CO=O+([-])31-N2cOccc-]3nccc]cr-/n1CN(c2ccc(#C)c2)-s@
replace c at position 26 with =,CO=O+([-])31-N2cOccc-]3ncc=]cr-/n1CN(c2ccc(#C)c2)-s@
replace 1 at position 33 with +,CO=O+([-])31-N2cOccc-]3ncc=]cr-/n+CN(c2ccc(#C)c2)-s@
replace / at position 31 with =,CO=O+([-])31-N2cOccc-]3ncc=]cr-=n+CN(c2ccc(#C)c2)-s@
replace ( at position 42 with 4,CO=O+([-])31-N2cOccc-]3ncc=]cr-=n+CN(c2ccc4#C)c2)-s@
remove 3 from position 22,CO=O+([-])31-N2cOccc-]ncc=]cr-=n+CN(c2ccc4#C)c2)-s@
add c at position 44,CO=O+([-])31-N2cOccc-]ncc=]cr-=n+CN(c2ccc4#Cc)c2)-s@
replace 2 at position 37 with s,CO=O+([-])31-N2cOccc-]ncc=]cr-=n+CN(csccc4#Cc)c2)-s@
replace ) at position 9 with o,CO=O+([-]o31-N2cOccc-]ncc=]cr-=n+CN(csccc4#Cc)c2)-s@
replace c at position 46 with 5,CO=O+([-]o31-N2cOccc-]ncc=]cr-=n+CN(csccc4#Cc)52)-s@
replace r at position 28 with [,CO=O+([-]o31-N2cOccc-]ncc=]c[-=n+CN(csccc4#Cc)52)-s@
add 1 at position 25,CO=O+([-]o31-N2cOccc-]ncc1=]c[-=n+CN(csccc4#Cc)52)-s@
add c at position 18,CO=O+([-]o31-N2cOcccc-]ncc1=]c[-=n+CN(csccc4#Cc)52)-s@
add l at position 32,CO=O+([-]o31-N2cOcccc-]ncc1=]c[-l=n+CN(csccc4#Cc)52)-s@
add 5 at position 44,CO=O+([-]o31-N2cOcccc-]ncc1=]c[-l=n+CN(csccc54#Cc)52)-s@
remove c from position 29,CO=O+([-]o31-N2cOcccc-]ncc1=][-l=n+CN(csccc54#Cc)52)-s@
add n at position 16,CO=O+([-]o31-N2cnOcccc-]ncc1=][-l=n+CN(csccc54#Cc)52)-s@
add s at position 27,CO=O+([-]o31-N2cnOcccc-]nccs1=][-l=n+CN(csccc54#Cc)52)-s@
add 1 at position 25,CO=O+([-]o31-N2cnOcccc-]n1ccs1=][-l=n+CN(csccc54#Cc)52)-s@
remove s from position 56,CO=O+([-]o31-N2cnOcccc-]n1ccs1=][-l=n+CN(csccc54#Cc)52)-@
remove c from position 15,CO=O+([-]o31-N2nOcccc-]n1ccs1=][-l=n+CN(csccc54#Cc)52)-@
replace n at position 23 with s,CO=O+([-]o31-N2nOcccc-]s1ccs1=][-l=n+CN(csccc54#Cc)52)-@
replace - at position 32 with r,CO=O+([-]o31-N2nOcccc-]s1ccs1=][rl=n+CN(csccc54#Cc)52)-@
remove 1 from position 11,CO=O+([-]o3-N2nOcccc-]s1ccs1=][rl=n+CN(csccc54#Cc)52)-@
replace 2 at position 13 with 5,CO=O+([-]o3-N5nOcccc-]s1ccs1=][rl=n+CN(csccc54#Cc)52)-@
add F at position 22,CO=O+([-]o3-N5nOcccc-]Fs1ccs1=][rl=n+CN(csccc54#Cc)52)-@
add s at position 49,CO=O+([-]o3-N5nOcccc-]Fs1ccs1=][rl=n+CN(csccc54#Csc)52)-@
remove ] from position 21,CO=O+([-]o3-N5nOcccc-Fs1ccs1=][rl=n+CN(csccc54#Csc)52)-@
add I at position 3,CO=IO+([-]o3-N5nOcccc-Fs1ccs1=][rl=n+CN(csccc54#Csc)52)-@
add l at position 9,CO=IO+([-l]o3-N5nOcccc-Fs1ccs1=][rl=n+CN(csccc54#Csc)52)-@
remove c from position 45,CO=IO+([-l]o3-N5nOcccc-Fs1ccs1=][rl=n+CN(cscc54#Csc)52)-@
remove C from position 0,O=IO+([-l]o3-N5nOcccc-Fs1ccs1=][rl=n+CN(cscc54#Csc)52)-@
remove O from position 3,O=I+([-l]o3-N5nOcccc-Fs1ccs1=][rl=n+CN(cscc54#Csc)52)-@
remove c from position 48,O=I+([-l]o3-N5nOcccc-Fs1ccs1=][rl=n+CN(cscc54#Cs)52)-@
replace 5 at position 13 with (,O=I+([-l]o3-N(nOcccc-Fs1ccs1=][rl=n+CN(cscc54#Cs)52)-@
remove s from position 47,O=I+([-l]o3-N(nOcccc-Fs1ccs1=][rl=n+CN(cscc54#C)52)-@
remove @ from position 52,O=I+([-l]o3-N(nOcccc-Fs1ccs1=][rl=n+CN(cscc54#C)52)-
remove c from position 17,O=I+([-l]o3-N(nOccc-Fs1ccs1=][rl=n+CN(cscc54#C)52)-
remove - from position 19,O=I+([-l]o3-N(nOcccFs1ccs1=][rl=n+CN(cscc54#C)52)-
remove # from position 43,O=I+([-l]o3-N(nOcccFs1ccs1=][rl=n+CN(cscc54C)52)-
remove o from position 9,O=I+([-l]3-N(nOcccFs1ccs1=][rl=n+CN(cscc54C)52)-
replace [ at position 5 with c,O=I+(c-l]3-N(nOcccFs1ccs1=][rl=n+CN(cscc54C)52)-
remove ] from position 26,O=I+(c-l]3-N(nOcccFs1ccs1=[rl=n+CN(cscc54C)52)-
remove s from position 36,O=I+(c-l]3-N(nOcccFs1ccs1=[rl=n+CN(ccc54C)52)-
remove l from position 7,O=I+(c-]3-N(nOcccFs1ccs1=[rl=n+CN(ccc54C)52)-
remove F from position 17,O=I+(c-]3-N(nOcccs1ccs1=[rl=n+CN(ccc54C)52)-
add 7 at position 37,O=I+(c-]3-N(nOcccs1ccs1=[rl=n+CN(ccc574C)52)-
add 2 at position 28,O=I+(c-]3-N(nOcccs1ccs1=[rl=2n+CN(ccc574C)52)-
replace N at position 10 with 7,O=I+(c-]3-7(nOcccs1ccs1=[rl=2n+CN(ccc574C)52)-
remove ( from position 4,O=I+c-]3-7(nOcccs1ccs1=[rl=2n+CN(ccc574C)52)-
remove c from position 19,O=I+c-]3-7(nOcccs1cs1=[rl=2n+CN(ccc574C)52)-
remove - from position 43,O=I+c-]3-7(nOcccs1cs1=[rl=2n+CN(ccc574C)52)
replace n at position 11 with 7,O=I+c-]3-7(7Occcs1cs1=[rl=2n+CN(ccc574C)52)
remove ( from position 10,O=I+c-]3-77Occcs1cs1=[rl=2n+CN(ccc574C)52)
remove l from position 23,O=I+c-]3-77Occcs1cs1=[r=2n+CN(ccc574C)52)
remove r from position 22,O=I+c-]3-77Occcs1cs1=[=2n+CN(ccc574C)52)
add r at position 33,O=I+c-]3-77Occcs1cs1=[=2n+CN(ccc5r74C)52)
remove c from position 30,O=I+c-]3-77Occcs1cs1=[=2n+CN(cc5r74C)52)
remove c from position 29,O=I+c-]3-77Occcs1cs1=[=2n+CN(c5r74C)52)
remove ] from position 6,O=I+c-3-77Occcs1cs1=[=2n+CN(c5r74C)52)
add ) at position 30,O=I+c-3-77Occcs1cs1=[=2n+CN(c5)r74C)52)
replace 2 at position 37 with 6,O=I+c-3-77Occcs1cs1=[=2n+CN(c5)r74C)56)
replace 1 at position 18 with 5,O=I+c-3-77Occcs1cs5=[=2n+CN(c5)r74C)56)
remove 6 from position 37,O=I+c-3-77Occcs1cs5=[=2n+CN(c5)r74C)5)
remove 5 from position 18,O=I+c-3-77Occcs1cs=[=2n+CN(c5)r74C)5)
remove 4 from position 32,O=I+c-3-77Occcs1cs=[=2n+CN(c5)r7C)5)
replace s at position 17 with ),O=I+c-3-77Occcs1c)=[=2n+CN(c5)r7C)5)
remove c from position 13,O=I+c-3-77Occs1c)=[=2n+CN(c5)r7C)5)
remove n from position 21,O=I+c-3-77Occs1c)=[=2+CN(c5)r7C)5)
replace 7 at position 29 with 3,O=I+c-3-77Occs1c)=[=2+CN(c5)r3C)5)
add H at position 33,O=I+c-3-77Occs1c)=[=2+CN(c5)r3C)5H)
remove - from position 7,O=I+c-377Occs1c)=[=2+CN(c5)r3C)5H)
replace = at position 16 with 6,O=I+c-377Occs1c)6[=2+CN(c5)r3C)5H)
add 7 at position 1,O7=I+c-377Occs1c)6[=2+CN(c5)r3C)5H)
add 1 at position 2,O71=I+c-377Occs1c)6[=2+CN(c5)r3C)5H)
remove = from position 20,O71=I+c-377Occs1c)6[2+CN(c5)r3C)5H)
remove ) from position 27,O71=I+c-377Occs1c)6[2+CN(c5r3C)5H)
replace I at position 4 with o,O71=o+c-377Occs1c)6[2+CN(c5r3C)5H)
replace [ at position 19 with 3,O71=o+c-377Occs1c)632+CN(c5r3C)5H)
remove s from position 14,O71=o+c-377Occ1c)632+CN(c5r3C)5H)
remove + from position 5,O71=oc-377Occ1c)632+CN(c5r3C)5H)
remove 7 from position 1,O1=oc-377Occ1c)632+CN(c5r3C)5H)
remove c from position 10,O1=oc-377Oc1c)632+CN(c5r3C)5H)
replace 7 at position 7 with /,O1=oc-3/7Oc1c)632+CN(c5r3C)5H)
remove c from position 21,O1=oc-3/7Oc1c)632+CN(5r3C)5H)
replace c at position 4 with r,O1=or-3/7Oc1c)632+CN(5r3C)5H)
remove N from position 19,O1=or-3/7Oc1c)632+C(5r3C)5H)
add s at position 17,O1=or-3/7Oc1c)632s+C(5r3C)5H)
replace r at position 22 with /,O1=or-3/7Oc1c)632s+C(5/3C)5H)
replace C at position 24 with F,O1=or-3/7Oc1c)632s+C(5/3F)5H)
remove c from position 12,O1=or-3/7Oc1)632s+C(5/3F)5H)
replace c at position 10 with -,O1=or-3/7O-1)632s+C(5/3F)5H)
remove + from position 17,O1=or-3/7O-1)632sC(5/3F)5H)
remove 5 from position 19,O1=or-3/7O-1)632sC(/3F)5H)
remove 3 from position 14,O1=or-3/7O-1)62sC(/3F)5H)
add r at position 2,O1r=or-3/7O-1)62sC(/3F)5H)
remove F from position 21,O1r=or-3/7O-1)62sC(/3)5H)
remove 1 from position 1,Or=or-3/7O-1)62sC(/3)5H)
remove = from position 2,Oror-3/7O-1)62sC(/3)5H)
remove ) from position 19,Oror-3/7O-1)62sC(/35H)
replace ) at position 11 with F,Oror-3/7O-1F62sC(/35H)
remove 6 from position 12,Oror-3/7O-1F2sC(/35H)
remove r from position 1,Oor-3/7O-1F2sC(/35H)
add l at position 2,Oolr-3/7O-1F2sC(/35H)
remove 1 from position 10,Oolr-3/7O-F2sC(/35H)
add 2 at position 20,Oolr-3/7O-F2sC(/35H)2
remove O from position 0,olr-3/7O-F2sC(/35H)2
replace s at position 11 with H,olr-3/7O-F2HC(/35H)2
remove / from position 5,olr-37O-F2HC(/35H)2
add 1 at position 0,1olr-37O-F2HC(/35H)2
replace 2 at position 19 with N,1olr-37O-F2HC(/35H)N
remove - from position 4,1olr37O-F2HC(/35H)N
remove 3 from position 4,1olr7O-F2HC(/35H)N
remove 2 from position 8,1olr7O-FHC(/35H)N
remove o from position 1,1lr7O-FHC(/35H)N
replace ) at position 14 with 7,1lr7O-FHC(/35H7N
remove 3 from position 11,1lr7O-FHC(/5H7N
remove F from position 6,1lr7O-HC(/5H7N
replace 7 at position 3 with 4,1lr4O-HC(/5H7N
add H at position 5,1lr4OH-HC(/5H7N
replace l at position 1 with F,1Fr4OH-HC(/5H7N
remove 7 from position 13,1Fr4OH-HC(/5HN
add H at position 9,1Fr4OH-HCH(/5HN
remove r from position 2,1F4OH-HCH(/5HN
remove - from position 5,1F4OHHCH(/5HN
replace H at position 4 with [,1F4O[HCH(/5HN
remove 4 from position 2,1FO[HCH(/5HN
remove O from position 2,1F[HCH(/5HN
replace 5 at position 8 with [,1F[HCH(/[HN
remove H from position 3,1F[CH(/[HN
remove [ from position 2,1FCH(/[HN
remove [ from position 6,1FCH(/HN
remove N from position 7,1FCH(/H
replace ( at position 4 with B,1FCHB/H
remove H from position 6,1FCHB/
remove B from position 4,1FCH/
add C at position 5,1FCH/C
replace / at position 4 with -,1FCH-C
add B at position 3,1FCBH-C
add c at position 2,1FcCBH-C
replace C at position 3 with [,1Fc[BH-C
add s at position 6,1Fc[BHs-C
remove C from position 8,1Fc[BHs-
remove [ from position 3,1FcBHs-
remove s from position 5,1FcBH-
replace B at position 3 with 5,1Fc5H-
replace H at position 4 with +,1Fc5+-
remove 5 from position 3,1Fc+-
add / at position 1,1/Fc+-
remove F from position 2,1/c+-
add S at position 2,1/Sc+-
replace S at position 2 with #,1/#c+-
add 5 at position 5,1/#c+5-
remove 1 from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
