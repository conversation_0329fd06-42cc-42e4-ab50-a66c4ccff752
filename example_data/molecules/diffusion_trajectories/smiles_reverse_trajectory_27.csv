log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 5,oFc5BH-C
remove c from position 2,oF5BH-<PERSON>
remove B from position 3,oF5H-<PERSON>
replace - at position 4 with ],oF5H]C
remove <PERSON> from position 5,oF5H]
add B at position 4,oF5HB]
add N at position 6,oF5HB]N
replace B at position 4 with -,oF5H-]N
add r at position 7,oF5H-]Nr
add S at position 6,oF5H-]SNr
add [ at position 2,oF[5H-]SNr
add 5 at position 3,oF[55H-]SNr
replace S at position 8 with r,oF[55H-]rNr
add l at position 2,oFl[55H-]rNr
add 4 at position 2,oF4l[55H-]rNr
replace [ at position 4 with H,oF4lH55H-]rNr
add 7 at position 5,oF4lH755H-]rNr
add O at position 2,oFO4lH755H-]rNr
remove H from position 9,oFO4lH755-]rNr
add 6 at position 13,oFO4lH755-]rN6r
replace F at position 1 with n,onO4lH755-]rN6r
remove H from position 5,onO4l755-]rN6r
replace 4 at position 3 with r,onOrl755-]rN6r
add = at position 6,onOrl7=55-]rN6r
add s at position 11,onOrl7=55-]srN6r
remove 6 from position 14,onOrl7=55-]srNr
replace ] at position 10 with 7,onOrl7=55-7srNr
remove O from position 2,onrl7=55-7srNr
add 2 at position 11,onrl7=55-7s2rNr
replace N at position 13 with /,onrl7=55-7s2r/r
add S at position 6,onrl7=S55-7s2r/r
add l at position 2,onlrl7=S55-7s2r/r
add F at position 11,onlrl7=S55-F7s2r/r
replace o at position 0 with S,Snlrl7=S55-F7s2r/r
remove S from position 7,Snlrl7=55-F7s2r/r
add B at position 10,Snlrl7=55-BF7s2r/r
remove l from position 2,Snrl7=55-BF7s2r/r
add r at position 1,Srnrl7=55-BF7s2r/r
add ) at position 12,Srnrl7=55-BF)7s2r/r
replace F at position 11 with 3,Srnrl7=55-B3)7s2r/r
add 4 at position 19,Srnrl7=55-B3)7s2r/r4
add N at position 2,SrNnrl7=55-B3)7s2r/r4
add 6 at position 1,S6rNnrl7=55-B3)7s2r/r4
add F at position 21,S6rNnrl7=55-B3)7s2r/rF4
remove r from position 2,S6Nnrl7=55-B3)7s2r/rF4
add 3 at position 14,S6Nnrl7=55-B3)37s2r/rF4
add n at position 19,S6Nnrl7=55-B3)37s2rn/rF4
add B at position 17,S6Nnrl7=55-B3)37sB2rn/rF4
replace - at position 10 with 2,S6Nnrl7=552B3)37sB2rn/rF4
add = at position 12,S6Nnrl7=552B=3)37sB2rn/rF4
replace F at position 24 with ),S6Nnrl7=552B=3)37sB2rn/r)4
replace / at position 22 with n,S6Nnrl7=552B=3)37sB2rnnr)4
remove s from position 17,S6Nnrl7=552B=3)37B2rnnr)4
add - at position 19,S6Nnrl7=552B=3)37B2-rnnr)4
replace r at position 4 with (,S6Nn(l7=552B=3)37B2-rnnr)4
add ( at position 21,S6Nn(l7=552B=3)37B2-r(nnr)4
add r at position 5,S6Nn(rl7=552B=3)37B2-r(nnr)4
add ) at position 27,S6Nn(rl7=552B=3)37B2-r(nnr))4
replace S at position 0 with C,C6Nn(rl7=552B=3)37B2-r(nnr))4
add l at position 20,C6Nn(rl7=552B=3)37B2l-r(nnr))4
replace 7 at position 7 with 3,C6Nn(rl3=552B=3)37B2l-r(nnr))4
add ( at position 15,C6Nn(rl3=552B=3()37B2l-r(nnr))4
replace 4 at position 30 with 7,C6Nn(rl3=552B=3()37B2l-r(nnr))7
replace ) at position 29 with 7,C6Nn(rl3=552B=3()37B2l-r(nnr)77
add ) at position 27,C6Nn(rl3=552B=3()37B2l-r(nn)r)77
replace 5 at position 9 with S,C6Nn(rl3=S52B=3()37B2l-r(nn)r)77
add 2 at position 2,C62Nn(rl3=S52B=3()37B2l-r(nn)r)77
replace ( at position 25 with /,C62Nn(rl3=S52B=3()37B2l-r/nn)r)77
remove - from position 23,C62Nn(rl3=S52B=3()37B2lr/nn)r)77
remove l from position 7,C62Nn(r3=S52B=3()37B2lr/nn)r)77
replace 3 at position 14 with N,C62Nn(r3=S52B=N()37B2lr/nn)r)77
add F at position 21,C62Nn(r3=S52B=N()37B2Flr/nn)r)77
add l at position 13,C62Nn(r3=S52Bl=N()37B2Flr/nn)r)77
replace ) at position 17 with F,C62Nn(r3=S52Bl=N(F37B2Flr/nn)r)77
add 1 at position 32,C62Nn(r3=S52Bl=N(F37B2Flr/nn)r)717
add 4 at position 18,C62Nn(r3=S52Bl=N(F437B2Flr/nn)r)717
add 7 at position 21,C62Nn(r3=S52Bl=N(F4377B2Flr/nn)r)717
replace 4 at position 18 with c,C62Nn(r3=S52Bl=N(Fc377B2Flr/nn)r)717
replace 7 at position 21 with 3,C62Nn(r3=S52Bl=N(Fc373B2Flr/nn)r)717
remove ) from position 30,C62Nn(r3=S52Bl=N(Fc373B2Flr/nnr)717
add = at position 6,C62Nn(=r3=S52Bl=N(Fc373B2Flr/nnr)717
add + at position 29,C62Nn(=r3=S52Bl=N(Fc373B2Flr/+nnr)717
add ) at position 30,C62Nn(=r3=S52Bl=N(Fc373B2Flr/+)nnr)717
remove r from position 33,C62Nn(=r3=S52Bl=N(Fc373B2Flr/+)nn)717
add c at position 22,C62Nn(=r3=S52Bl=N(Fc37c3B2Flr/+)nn)717
add o at position 23,C62Nn(=r3=S52Bl=N(Fc37co3B2Flr/+)nn)717
remove / from position 30,C62Nn(=r3=S52Bl=N(Fc37co3B2Flr+)nn)717
add O at position 30,C62Nn(=r3=S52Bl=N(Fc37co3B2FlrO+)nn)717
add r at position 19,C62Nn(=r3=S52Bl=N(Frc37co3B2FlrO+)nn)717
add + at position 4,C62N+n(=r3=S52Bl=N(Frc37co3B2FlrO+)nn)717
replace = at position 10 with ),C62N+n(=r3)S52Bl=N(Frc37co3B2FlrO+)nn)717
remove 2 from position 28,C62N+n(=r3)S52Bl=N(Frc37co3BFlrO+)nn)717
remove 7 from position 37,C62N+n(=r3)S52Bl=N(Frc37co3BFlrO+)nn)17
add 4 at position 17,C62N+n(=r3)S52Bl=4N(Frc37co3BFlrO+)nn)17
add ] at position 7,C62N+n(]=r3)S52Bl=4N(Frc37co3BFlrO+)nn)17
add c at position 36,C62N+n(]=r3)S52Bl=4N(Frc37co3BFlrO+)cnn)17
replace = at position 17 with H,C62N+n(]=r3)S52BlH4N(Frc37co3BFlrO+)cnn)17
replace + at position 34 with ),C62N+n(]=r3)S52BlH4N(Frc37co3BFlrO))cnn)17
add B at position 36,C62N+n(]=r3)S52BlH4N(Frc37co3BFlrO))Bcnn)17
add n at position 19,C62N+n(]=r3)S52BlH4nN(Frc37co3BFlrO))Bcnn)17
replace H at position 17 with -,C62N+n(]=r3)S52Bl-4nN(Frc37co3BFlrO))Bcnn)17
replace 7 at position 43 with C,C62N+n(]=r3)S52Bl-4nN(Frc37co3BFlrO))Bcnn)1C
add O at position 13,C62N+n(]=r3)SO52Bl-4nN(Frc37co3BFlrO))Bcnn)1C
remove 7 from position 27,C62N+n(]=r3)SO52Bl-4nN(Frc3co3BFlrO))Bcnn)1C
remove r from position 9,C62N+n(]=3)SO52Bl-4nN(Frc3co3BFlrO))Bcnn)1C
remove N from position 3,C62+n(]=3)SO52Bl-4nN(Frc3co3BFlrO))Bcnn)1C
add r at position 10,C62+n(]=3)rSO52Bl-4nN(Frc3co3BFlrO))Bcnn)1C
remove ) from position 34,C62+n(]=3)rSO52Bl-4nN(Frc3co3BFlrO)Bcnn)1C
remove r from position 10,C62+n(]=3)SO52Bl-4nN(Frc3co3BFlrO)Bcnn)1C
add ) at position 8,C62+n(]=)3)SO52Bl-4nN(Frc3co3BFlrO)Bcnn)1C
remove B from position 35,C62+n(]=)3)SO52Bl-4nN(Frc3co3BFlrO)cnn)1C
remove F from position 22,C62+n(]=)3)SO52Bl-4nN(rc3co3BFlrO)cnn)1C
replace 5 at position 13 with 3,C62+n(]=)3)SO32Bl-4nN(rc3co3BFlrO)cnn)1C
add - at position 11,C62+n(]=)3)-SO32Bl-4nN(rc3co3BFlrO)cnn)1C
replace r at position 32 with (,C62+n(]=)3)-SO32Bl-4nN(rc3co3BFl(O)cnn)1C
remove r from position 23,C62+n(]=)3)-SO32Bl-4nN(c3co3BFl(O)cnn)1C
replace F at position 29 with /,C62+n(]=)3)-SO32Bl-4nN(c3co3B/l(O)cnn)1C
remove S from position 12,C62+n(]=)3)-O32Bl-4nN(c3co3B/l(O)cnn)1C
add @ at position 12,C62+n(]=)3)-@O32Bl-4nN(c3co3B/l(O)cnn)1C
add I at position 4,C62+In(]=)3)-@O32Bl-4nN(c3co3B/l(O)cnn)1C
replace I at position 4 with =,C62+=n(]=)3)-@O32Bl-4nN(c3co3B/l(O)cnn)1C
replace 4 at position 20 with ],C62+=n(]=)3)-@O32Bl-]nN(c3co3B/l(O)cnn)1C
add S at position 5,C62+=Sn(]=)3)-@O32Bl-]nN(c3co3B/l(O)cnn)1C
replace 2 at position 2 with I,C6I+=Sn(]=)3)-@O32Bl-]nN(c3co3B/l(O)cnn)1C
add ( at position 3,C6I(+=Sn(]=)3)-@O32Bl-]nN(c3co3B/l(O)cnn)1C
replace + at position 4 with (,C6I((=Sn(]=)3)-@O32Bl-]nN(c3co3B/l(O)cnn)1C
replace l at position 20 with C,C6I((=Sn(]=)3)-@O32BC-]nN(c3co3B/l(O)cnn)1C
add 2 at position 17,C6I((=Sn(]=)3)-@O232BC-]nN(c3co3B/l(O)cnn)1C
add @ at position 10,C6I((=Sn(]@=)3)-@O232BC-]nN(c3co3B/l(O)cnn)1C
remove S from position 6,C6I((=n(]@=)3)-@O232BC-]nN(c3co3B/l(O)cnn)1C
replace O at position 16 with H,C6I((=n(]@=)3)-@H232BC-]nN(c3co3B/l(O)cnn)1C
replace n at position 24 with 2,C6I((=n(]@=)3)-@H232BC-]2N(c3co3B/l(O)cnn)1C
replace n at position 6 with O,C6I((=O(]@=)3)-@H232BC-]2N(c3co3B/l(O)cnn)1C
add N at position 7,C6I((=ON(]@=)3)-@H232BC-]2N(c3co3B/l(O)cnn)1C
remove = from position 11,C6I((=ON(]@)3)-@H232BC-]2N(c3co3B/l(O)cnn)1C
replace 3 at position 18 with O,C6I((=ON(]@)3)-@H2O2BC-]2N(c3co3B/l(O)cnn)1C
add - at position 33,C6I((=ON(]@)3)-@H2O2BC-]2N(c3co3B-/l(O)cnn)1C
replace @ at position 10 with C,C6I((=ON(]C)3)-@H2O2BC-]2N(c3co3B-/l(O)cnn)1C
replace o at position 30 with (,C6I((=ON(]C)3)-@H2O2BC-]2N(c3c(3B-/l(O)cnn)1C
replace ( at position 30 with c,C6I((=ON(]C)3)-@H2O2BC-]2N(c3cc3B-/l(O)cnn)1C
add = at position 37,C6I((=ON(]C)3)-@H2O2BC-]2N(c3cc3B-/l(=O)cnn)1C
add C at position 22,C6I((=ON(]C)3)-@H2O2BCC-]2N(c3cc3B-/l(=O)cnn)1C
replace I at position 2 with 1,C61((=ON(]C)3)-@H2O2BCC-]2N(c3cc3B-/l(=O)cnn)1C
remove ] from position 9,C61((=ON(C)3)-@H2O2BCC-]2N(c3cc3B-/l(=O)cnn)1C
replace 3 at position 11 with C,C61((=ON(C)C)-@H2O2BCC-]2N(c3cc3B-/l(=O)cnn)1C
remove O from position 17,C61((=ON(C)C)-@H22BCC-]2N(c3cc3B-/l(=O)cnn)1C
add + at position 29,C61((=ON(C)C)-@H22BCC-]2N(c3c+c3B-/l(=O)cnn)1C
remove - from position 33,C61((=ON(C)C)-@H22BCC-]2N(c3c+c3B/l(=O)cnn)1C
remove ] from position 22,C61((=ON(C)C)-@H22BCC-2N(c3c+c3B/l(=O)cnn)1C
remove - from position 13,C61((=ON(C)C)@H22BCC-2N(c3c+c3B/l(=O)cnn)1C
remove / from position 31,C61((=ON(C)C)@H22BCC-2N(c3c+c3Bl(=O)cnn)1C
add c at position 3,C61c((=ON(C)C)@H22BCC-2N(c3c+c3Bl(=O)cnn)1C
add + at position 42,C61c((=ON(C)C)@H22BCC-2N(c3c+c3Bl(=O)cnn)1+C
remove - from position 21,C61c((=ON(C)C)@H22BCC2N(c3c+c3Bl(=O)cnn)1+C
add ) at position 32,C61c((=ON(C)C)@H22BCC2N(c3c+c3Bl)(=O)cnn)1+C
add C at position 33,C61c((=ON(C)C)@H22BCC2N(c3c+c3Bl)C(=O)cnn)1+C
add [ at position 12,C61c((=ON(C)[C)@H22BCC2N(c3c+c3Bl)C(=O)cnn)1+C
replace 2 at position 17 with ],C61c((=ON(C)[C)@H]2BCC2N(c3c+c3Bl)C(=O)cnn)1+C
remove B from position 19,C61c((=ON(C)[C)@H]2CC2N(c3c+c3Bl)C(=O)cnn)1+C
add 2 at position 35,C61c((=ON(C)[C)@H]2CC2N(c3c+c3Bl)C(2=O)cnn)1+C
add c at position 2,C6c1c((=ON(C)[C)@H]2CC2N(c3c+c3Bl)C(2=O)cnn)1+C
remove ) from position 43,C6c1c((=ON(C)[C)@H]2CC2N(c3c+c3Bl)C(2=O)cnn1+C
add c at position 29,C6c1c((=ON(C)[C)@H]2CC2N(c3c+cc3Bl)C(2=O)cnn1+C
replace ) at position 15 with @,C6c1c((=ON(C)[C@@H]2CC2N(c3c+cc3Bl)C(2=O)cnn1+C
replace B at position 32 with C,C6c1c((=ON(C)[C@@H]2CC2N(c3c+cc3Cl)C(2=O)cnn1+C
remove + from position 45,C6c1c((=ON(C)[C@@H]2CC2N(c3c+cc3Cl)C(2=O)cnn1C
add ) at position 9,C6c1c((=O)N(C)[C@@H]2CC2N(c3c+cc3Cl)C(2=O)cnn1C
add c at position 30,C6c1c((=O)N(C)[C@@H]2CC2N(c3c+ccc3Cl)C(2=O)cnn1C
remove 6 from position 1,Cc1c((=O)N(C)[C@@H]2CC2N(c3c+ccc3Cl)C(2=O)cnn1C
remove 2 from position 22,Cc1c((=O)N(C)[C@@H]2CCN(c3c+ccc3Cl)C(2=O)cnn1C
add C at position 5,Cc1c(C(=O)N(C)[C@@H]2CCN(c3c+ccc3Cl)C(2=O)cnn1C
remove ( from position 37,Cc1c(C(=O)N(C)[C@@H]2CCN(c3c+ccc3Cl)C2=O)cnn1C
replace + at position 28 with c,Cc1c(C(=O)N(C)[C@@H]2CCN(c3ccccc3Cl)C2=O)cnn1C
final: Cc1c(C(=O)N(C)[C@@H]2CCN(c3ccccc3Cl)C2=O)cnn1C,Cc1c(C(=O)N(C)[C@@H]2CCN(c3ccccc3Cl)C2=O)cnn1C
