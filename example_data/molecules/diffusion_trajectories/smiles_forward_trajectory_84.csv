log,state
initialize: Cn1nnc2cc(C(=O)N[C@@H](C#N)c3ccc(Cl)c(Cl)c3)ccc21,Cn1nnc2cc(C(=O)N[C@@H](C#N)c3ccc(Cl)c(Cl)c3)ccc21
replace 3 at position 28 with +,Cn1nnc2cc(C(=O)N[C@@H](C#N)c+ccc(Cl)c(Cl)c3)ccc21
add ( at position 37,Cn1nnc2cc(C(=O)N[C@@H](C#N)c+ccc(Cl)c((Cl)c3)ccc21
remove c from position 5,Cn1nn2cc(C(=O)N[C@@H](C#N)c+ccc(Cl)c((Cl)c3)ccc21
add 2 at position 22,Cn1nn2cc(C(=O)N[C@@H](2C#N)c+ccc(Cl)c((Cl)c3)ccc21
add # at position 50,Cn1nn2cc(C(=O)N[C@@H](2C#N)c+ccc(Cl)c((Cl)c3)ccc21#
remove C from position 39,Cn1nn2cc(C(=O)N[C@@H](2C#N)c+ccc(Cl)c((l)c3)ccc21#
remove c from position 29,Cn1nn2cc(C(=O)N[C@@H](2C#N)c+cc(Cl)c((l)c3)ccc21#
add S at position 11,Cn1nn2cc(C(S=O)N[C@@H](2C#N)c+cc(Cl)c((l)c3)ccc21#
add C at position 0,CCn1nn2cc(C(S=O)N[C@@H](2C#N)c+cc(Cl)c((l)c3)ccc21#
remove ) from position 15,CCn1nn2cc(C(S=ON[C@@H](2C#N)c+cc(Cl)c((l)c3)ccc21#
add r at position 34,CCn1nn2cc(C(S=ON[C@@H](2C#N)c+cc(Crl)c((l)c3)ccc21#
remove ( from position 38,CCn1nn2cc(C(S=ON[C@@H](2C#N)c+cc(Crl)c(l)c3)ccc21#
remove ( from position 32,CCn1nn2cc(C(S=ON[C@@H](2C#N)c+ccCrl)c(l)c3)ccc21#
remove C from position 17,CCn1nn2cc(C(S=ON[@@H](2C#N)c+ccCrl)c(l)c3)ccc21#
replace n at position 4 with N,CCn1Nn2cc(C(S=ON[@@H](2C#N)c+ccCrl)c(l)c3)ccc21#
remove 2 from position 22,CCn1Nn2cc(C(S=ON[@@H](C#N)c+ccCrl)c(l)c3)ccc21#
remove c from position 41,CCn1Nn2cc(C(S=ON[@@H](C#N)c+ccCrl)c(l)c3)cc21#
replace c at position 29 with r,CCn1Nn2cc(C(S=ON[@@H](C#N)c+crCrl)c(l)c3)cc21#
remove C from position 1,Cn1Nn2cc(C(S=ON[@@H](C#N)c+crCrl)c(l)c3)cc21#
replace n at position 4 with #,Cn1N#2cc(C(S=ON[@@H](C#N)c+crCrl)c(l)c3)cc21#
remove r from position 30,Cn1N#2cc(C(S=ON[@@H](C#N)c+crCl)c(l)c3)cc21#
remove ( from position 10,Cn1N#2cc(CS=ON[@@H](C#N)c+crCl)c(l)c3)cc21#
add B at position 37,Cn1N#2cc(CS=ON[@@H](C#N)c+crCl)c(l)c3B)cc21#
add 1 at position 35,Cn1N#2cc(CS=ON[@@H](C#N)c+crCl)c(l)1c3B)cc21#
add ] at position 22,Cn1N#2cc(CS=ON[@@H](C#]N)c+crCl)c(l)1c3B)cc21#
add - at position 33,Cn1N#2cc(CS=ON[@@H](C#]N)c+crCl)c-(l)1c3B)cc21#
remove C from position 29,Cn1N#2cc(CS=ON[@@H](C#]N)c+crl)c-(l)1c3B)cc21#
add O at position 17,Cn1N#2cc(CS=ON[@@OH](C#]N)c+crl)c-(l)1c3B)cc21#
replace = at position 11 with 3,Cn1N#2cc(CS3ON[@@OH](C#]N)c+crl)c-(l)1c3B)cc21#
add ] at position 9,Cn1N#2cc(]CS3ON[@@OH](C#]N)c+crl)c-(l)1c3B)cc21#
replace 1 at position 2 with I,CnIN#2cc(]CS3ON[@@OH](C#]N)c+crl)c-(l)1c3B)cc21#
remove C from position 22,CnIN#2cc(]CS3ON[@@OH](#]N)c+crl)c-(l)1c3B)cc21#
remove 1 from position 37,CnIN#2cc(]CS3ON[@@OH](#]N)c+crl)c-(l)c3B)cc21#
replace l at position 30 with (,CnIN#2cc(]CS3ON[@@OH](#]N)c+cr()c-(l)c3B)cc21#
replace ( at position 30 with o,CnIN#2cc(]CS3ON[@@OH](#]N)c+cro)c-(l)c3B)cc21#
replace C at position 10 with @,CnIN#2cc(]@S3ON[@@OH](#]N)c+cro)c-(l)c3B)cc21#
remove - from position 33,CnIN#2cc(]@S3ON[@@OH](#]N)c+cro)c(l)c3B)cc21#
replace O at position 18 with 3,CnIN#2cc(]@S3ON[@@3H](#]N)c+cro)c(l)c3B)cc21#
add = at position 11,CnIN#2cc(]@=S3ON[@@3H](#]N)c+cro)c(l)c3B)cc21#
remove c from position 7,CnIN#2c(]@=S3ON[@@3H](#]N)c+cro)c(l)c3B)cc21#
replace c at position 6 with n,CnIN#2n(]@=S3ON[@@3H](#]N)c+cro)c(l)c3B)cc21#
replace N at position 24 with n,CnIN#2n(]@=S3ON[@@3H](#]n)c+cro)c(l)c3B)cc21#
replace @ at position 16 with O,CnIN#2n(]@=S3ON[O@3H](#]n)c+cro)c(l)c3B)cc21#
add S at position 6,CnIN#2Sn(]@=S3ON[O@3H](#]n)c+cro)c(l)c3B)cc21#
remove @ from position 10,CnIN#2Sn(]=S3ON[O@3H](#]n)c+cro)c(l)c3B)cc21#
remove @ from position 17,CnIN#2Sn(]=S3ON[O3H](#]n)c+cro)c(l)c3B)cc21#
replace ( at position 20 with l,CnIN#2Sn(]=S3ON[O3H]l#]n)c+cro)c(l)c3B)cc21#
replace # at position 4 with +,CnIN+2Sn(]=S3ON[O3H]l#]n)c+cro)c(l)c3B)cc21#
remove N from position 3,CnI+2Sn(]=S3ON[O3H]l#]n)c+cro)c(l)c3B)cc21#
replace I at position 2 with 2,Cn2+2Sn(]=S3ON[O3H]l#]n)c+cro)c(l)c3B)cc21#
remove S from position 5,Cn2+2n(]=S3ON[O3H]l#]n)c+cro)c(l)c3B)cc21#
replace ] at position 20 with 4,Cn2+2n(]=S3ON[O3H]l#4n)c+cro)c(l)c3B)cc21#
replace 2 at position 4 with I,Cn2+In(]=S3ON[O3H]l#4n)c+cro)c(l)c3B)cc21#
remove I from position 4,Cn2+n(]=S3ON[O3H]l#4n)c+cro)c(l)c3B)cc21#
remove [ from position 12,Cn2+n(]=S3ONO3H]l#4n)c+cro)c(l)c3B)cc21#
add S at position 12,Cn2+n(]=S3ONSO3H]l#4n)c+cro)c(l)c3B)cc21#
replace ( at position 29 with F,Cn2+n(]=S3ONSO3H]l#4n)c+cro)cFl)c3B)cc21#
add r at position 23,Cn2+n(]=S3ONSO3H]l#4n)cr+cro)cFl)c3B)cc21#
replace ) at position 32 with r,Cn2+n(]=S3ONSO3H]l#4n)cr+cro)cFlrc3B)cc21#
remove N from position 11,Cn2+n(]=S3OSO3H]l#4n)cr+cro)cFlrc3B)cc21#
replace 3 at position 13 with 5,Cn2+n(]=S3OSO5H]l#4n)cr+cro)cFlrc3B)cc21#
add F at position 22,Cn2+n(]=S3OSO5H]l#4n)cFr+cro)cFlrc3B)cc21#
add B at position 35,Cn2+n(]=S3OSO5H]l#4n)cFr+cro)cFlrc3BB)cc21#
remove S from position 8,Cn2+n(]=3OSO5H]l#4n)cFr+cro)cFlrc3BB)cc21#
add r at position 10,Cn2+n(]=3OrSO5H]l#4n)cFr+cro)cFlrc3BB)cc21#
add ) at position 34,Cn2+n(]=3OrSO5H]l#4n)cFr+cro)cFlrc)3BB)cc21#
remove r from position 10,Cn2+n(]=3OSO5H]l#4n)cFr+cro)cFlrc)3BB)cc21#
add ( at position 43,Cn2+n(]=3OSO5H]l#4n)cFr+cro)cFlrc)3BB)cc21#(
remove O from position 9,Cn2+n(]=3SO5H]l#4n)cFr+cro)cFlrc)3BB)cc21#(
replace 2 at position 2 with @,Cn@+n(]=3SO5H]l#4n)cFr+cro)cFlrc)3BB)cc21#(
remove F from position 20,Cn@+n(]=3SO5H]l#4n)cr+cro)cFlrc)3BB)cc21#(
add r at position 24,Cn@+n(]=3SO5H]l#4n)cr+crro)cFlrc)3BB)cc21#(
replace n at position 17 with F,Cn@+n(]=3SO5H]l#4F)cr+crro)cFlrc)3BB)cc21#(
remove c from position 19,Cn@+n(]=3SO5H]l#4F)r+crro)cFlrc)3BB)cc21#(
remove c from position 36,Cn@+n(]=3SO5H]l#4F)r+crro)cFlrc)3BB)c21#(
replace B at position 34 with ),Cn@+n(]=3SO5H]l#4F)r+crro)cFlrc)3B))c21#(
replace F at position 17 with =,Cn@+n(]=3SO5H]l#4=)r+crro)cFlrc)3B))c21#(
remove c from position 36,Cn@+n(]=3SO5H]l#4=)r+crro)cFlrc)3B))21#(
remove = from position 7,Cn@+n(]3SO5H]l#4=)r+crro)cFlrc)3B))21#(
remove ) from position 17,Cn@+n(]3SO5H]l#4=r+crro)cFlrc)3B))21#(
add 7 at position 37,Cn@+n(]3SO5H]l#4=r+crro)cFlrc)3B))21#7(
add 2 at position 28,Cn@+n(]3SO5H]l#4=r+crro)cFlr2c)3B))21#7(
replace 5 at position 10 with =,Cn@+n(]3SO=H]l#4=r+crro)cFlr2c)3B))21#7(
remove n from position 4,Cn@+(]3SO=H]l#4=r+crro)cFlr2c)3B))21#7(
remove r from position 19,Cn@+(]3SO=H]l#4=r+cro)cFlr2c)3B))21#7(
remove B from position 30,Cn@+(]3SO=H]l#4=r+cro)cFlr2c)3))21#7(
add / at position 30,Cn@+(]3SO=H]l#4=r+cro)cFlr2c)3/))21#7(
remove F from position 23,Cn@+(]3SO=H]l#4=r+cro)clr2c)3/))21#7(
remove c from position 22,Cn@+(]3SO=H]l#4=r+cro)lr2c)3/))21#7(
add r at position 33,Cn@+(]3SO=H]l#4=r+cro)lr2c)3/))21r#7(
remove ) from position 30,Cn@+(]3SO=H]l#4=r+cro)lr2c)3/)21r#7(
remove ) from position 29,Cn@+(]3SO=H]l#4=r+cro)lr2c)3/21r#7(
remove 3 from position 6,Cn@+(]SO=H]l#4=r+cro)lr2c)3/21r#7(
add ) at position 30,Cn@+(]SO=H]l#4=r+cro)lr2c)3/21)r#7(
replace l at position 21 with 6,Cn@+(]SO=H]l#4=r+cro)6r2c)3/21)r#7(
replace r at position 18 with 4,Cn@+(]SO=H]l#4=r+c4o)6r2c)3/21)r#7(
remove 6 from position 21,Cn@+(]SO=H]l#4=r+c4o)r2c)3/21)r#7(
remove 4 from position 18,Cn@+(]SO=H]l#4=r+co)r2c)3/21)r#7(
remove ( from position 32,Cn@+(]SO=H]l#4=r+co)r2c)3/21)r#7
replace c at position 17 with ),Cn@+(]SO=H]l#4=r+)o)r2c)3/21)r#7
remove 4 from position 13,Cn@+(]SO=H]l#=r+)o)r2c)3/21)r#7
remove ] from position 10,Cn@+(]SO=Hl#=r+)o)r2c)3/21)r#7
replace + at position 14 with 4,Cn@+(]SO=Hl#=r4)o)r2c)3/21)r#7
add H at position 16,Cn@+(]SO=Hl#=r4)Ho)r2c)3/21)r#7
remove + from position 3,Cn@(]SO=Hl#=r4)Ho)r2c)3/21)r#7
replace H at position 8 with 6,Cn@(]SO=6l#=r4)Ho)r2c)3/21)r#7
add 7 at position 0,7Cn@(]SO=6l#=r4)Ho)r2c)3/21)r#7
add 1 at position 2,7C1n@(]SO=6l#=r4)Ho)r2c)3/21)r#7
remove r from position 20,7C1n@(]SO=6l#=r4)Ho)2c)3/21)r#7
remove = from position 13,7C1n@(]SO=6l#r4)Ho)2c)3/21)r#7
replace 7 at position 29 with ),7C1n@(]SO=6l#r4)Ho)2c)3/21)r#)
replace = at position 9 with 3,7C1n@(]SO36l#r4)Ho)2c)3/21)r#)
remove ) from position 29,7C1n@(]SO36l#r4)Ho)2c)3/21)r#
remove l from position 11,7C1n@(]SO36#r4)Ho)2c)3/21)r#
remove ) from position 20,7C1n@(]SO36#r4)Ho)2c3/21)r#
replace 7 at position 0 with S,SC1n@(]SO36#r4)Ho)2c3/21)r#
remove 2 from position 18,SC1n@(]SO36#r4)Ho)c3/21)r#
add B at position 15,SC1n@(]SO36#r4)BHo)c3/21)r#
remove / from position 21,SC1n@(]SO36#r4)BHo)c321)r#
replace @ at position 4 with r,SC1nr(]SO36#r4)BHo)c321)r#
remove c from position 19,SC1nr(]SO36#r4)BHo)321)r#
add s at position 17,SC1nr(]SO36#r4)BHso)321)r#
replace 1 at position 22 with /,SC1nr(]SO36#r4)BHso)32/)r#
replace r at position 24 with C,SC1nr(]SO36#r4)BHso)32/)C#
remove r from position 12,SC1nr(]SO36#4)BHso)32/)C#
replace 6 at position 10 with -,SC1nr(]SO3-#4)BHso)32/)C#
remove o from position 17,SC1nr(]SO3-#4)BHs)32/)C#
remove 2 from position 19,SC1nr(]SO3-#4)BHs)3/)C#
remove B from position 14,SC1nr(]SO3-#4)Hs)3/)C#
add r at position 2,SCr1nr(]SO3-#4)Hs)3/)C#
remove C from position 21,SCr1nr(]SO3-#4)Hs)3/)#
remove C from position 1,Sr1nr(]SO3-#4)Hs)3/)#
remove 1 from position 2,Srnr(]SO3-#4)Hs)3/)#
remove # from position 19,Srnr(]SO3-#4)Hs)3/)
replace 4 at position 11 with F,Srnr(]SO3-#F)Hs)3/)
remove ) from position 12,Srnr(]SO3-#FHs)3/)
remove r from position 1,Snr(]SO3-#FHs)3/)
add l at position 2,Snlr(]SO3-#FHs)3/)
remove # from position 10,Snlr(]SO3-FHs)3/)
add S at position 7,Snlr(]SSO3-FHs)3/)
replace S at position 0 with o,onlr(]SSO3-FHs)3/)
remove F from position 11,onlr(]SSO3-Hs)3/)
remove l from position 2,onr(]SSO3-Hs)3/)
remove S from position 6,onr(]SO3-Hs)3/)
replace / at position 13 with N,onr(]SO3-Hs)3N)
remove ) from position 11,onr(]SO3-Hs3N)
add O at position 2,onOr(]SO3-Hs3N)
replace H at position 10 with ],onOr(]SO3-]s3N)
add 6 at position 14,onOr(]SO3-]s3N6)
remove s from position 11,onOr(]SO3-]3N6)
remove S from position 6,onOr(]O3-]3N6)
replace r at position 3 with 4,onO4(]O3-]3N6)
add H at position 5,onO4(H]O3-]3N6)
replace n at position 1 with F,oFO4(H]O3-]3N6)
remove 6 from position 13,oFO4(H]O3-]3N)
add H at position 9,oFO4(H]O3H-]3N)
remove O from position 2,oF4(H]O3H-]3N)
remove ] from position 5,oF4(HO3H-]3N)
replace H at position 4 with [,oF4([O3H-]3N)
remove 4 from position 2,oF([O3H-]3N)
remove ( from position 2,oF[O3H-]3N)
replace 3 at position 8 with [,oF[O3H-][N)
remove O from position 3,oF[3H-][N)
remove [ from position 2,oF3H-][N)
remove [ from position 6,oF3H-]N)
remove ) from position 7,oF3H-]N
replace - at position 4 with B,oF3HB]N
remove N from position 6,oF3HB]
remove B from position 4,oF3H]
add C at position 5,oF3H]C
replace ] at position 4 with -,oF3H-C
add B at position 3,oF3BH-C
add c at position 2,oFc3BH-C
replace 3 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
