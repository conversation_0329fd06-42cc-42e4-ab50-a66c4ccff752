log,state
initialize: ,
add S at position 0,S
add O at position 0,OS
add o at position 0,o<PERSON>
replace S at position 2 with [,oO[
add [ at position 1,o[O[
add 1 at position 1,o1[O[
replace [ at position 2 with 6,o16O[
remove 6 from position 2,o1O[
add I at position 4,o1O[I
replace I at position 4 with (,o1O[(
remove ( from position 4,o1O[
add 5 at position 2,o15O[
remove o from position 0,15O[
add F at position 2,15FO[
add / at position 3,15F/O[
add I at position 5,15F/OI[
add C at position 7,15F/OI[C
replace O at position 4 with 3,15F/3I[C
replace F at position 2 with B,15B/3I[C
remove 1 from position 0,5B/3I[C
add = at position 2,5B=/3I[C
replace I at position 5 with 3,5B=/33[C
add B at position 0,B5B=/33[C
add 6 at position 3,B5B6=/33[C
replace B at position 2 with o,B5o6=/33[C
replace = at position 4 with r,B5o6r/33[C
replace B at position 0 with 1,15o6r/33[C
replace C at position 9 with /,15o6r/33[/
replace [ at position 8 with ),15o6r/33)/
remove 6 from position 3,15or/33)/
replace 5 at position 1 with r,1ror/33)/
add - at position 6,1ror/3-3)/
remove r from position 1,1or/3-3)/
add H at position 7,1or/3-3H)/
add B at position 9,1or/3-3H)B/
add S at position 8,1or/3-3HS)B/
replace - at position 5 with 6,1or/363HS)B/
replace S at position 8 with s,1or/363Hs)B/
replace / at position 11 with c,1or/363Hs)Bc
remove s from position 8,1or/363H)Bc
add 5 at position 9,1or/363H)5Bc
replace r at position 2 with 3,1o3/363H)5Bc
add 5 at position 10,1o3/363H)55Bc
replace / at position 3 with 4,1o34363H)55Bc
remove 5 from position 9,1o34363H)5Bc
add + at position 12,1o34363H)5Bc+
remove ) from position 8,1o34363H5Bc+
add @ at position 9,1o34363H5@Bc+
replace 3 at position 4 with n,1o34n63H5@Bc+
replace o at position 1 with 7,1734n63H5@Bc+
add s at position 6,1734n6s3H5@Bc+
add ) at position 5,1734n)6s3H5@Bc+
remove 1 from position 0,734n)6s3H5@Bc+
remove 7 from position 0,34n)6s3H5@Bc+
replace 6 at position 4 with F,34n)Fs3H5@Bc+
add 4 at position 1,344n)Fs3H5@Bc+
remove H from position 8,344n)Fs35@Bc+
replace 3 at position 7 with o,344n)Fso5@Bc+
remove 5 from position 8,344n)Fso@Bc+
add / at position 9,344n)Fso@/Bc+
replace ) at position 4 with =,344n=Fso@/Bc+
add 5 at position 8,344n=Fso5@/Bc+
add o at position 4,344no=Fso5@/Bc+
add 7 at position 10,344no=Fso57@/Bc+
replace 5 at position 9 with +,344no=Fso+7@/Bc+
replace 7 at position 10 with ),344no=Fso+)@/Bc+
remove o from position 4,344n=Fso+)@/Bc+
remove B from position 12,344n=Fso+)@/c+
add r at position 1,3r44n=Fso+)@/c+
replace @ at position 11 with r,3r44n=Fso+)r/c+
add N at position 0,N3r44n=Fso+)r/c+
remove c from position 14,N3r44n=Fso+)r/+
add 4 at position 11,N3r44n=Fso+4)r/+
add ( at position 11,N3r44n=Fso+(4)r/+
remove / from position 15,N3r44n=Fso+(4)r+
add / at position 15,N3r44n=Fso+(4)r/+
remove 4 from position 12,N3r44n=Fso+()r/+
add 6 at position 0,6N3r44n=Fso+()r/+
remove / from position 15,6N3r44n=Fso+()r+
add F at position 14,6N3r44n=Fso+()Fr+
remove ( from position 12,6N3r44n=Fso+)Fr+
add r at position 8,6N3r44n=rFso+)Fr+
add B at position 3,6N3Br44n=rFso+)Fr+
add 1 at position 18,6N3Br44n=rFso+)Fr+1
replace = at position 8 with H,6N3Br44nHrFso+)Fr+1
replace + at position 17 with ),6N3Br44nHrFso+)Fr)1
add B at position 18,6N3Br44nHrFso+)Fr)B1
add ) at position 9,6N3Br44nH)rFso+)Fr)B1
replace H at position 8 with 4,6N3Br44n4)rFso+)Fr)B1
replace s at position 12 with 7,6N3Br44n4)rF7o+)Fr)B1
add S at position 6,6N3Br4S4n4)rF7o+)Fr)B1
remove 7 from position 13,6N3Br4S4n4)rFo+)Fr)B1
remove r from position 4,6N3B4S4n4)rFo+)Fr)B1
remove N from position 1,63B4S4n4)rFo+)Fr)B1
add r at position 5,63B4Sr4n4)rFo+)Fr)B1
remove ) from position 17,63B4Sr4n4)rFo+)FrB1
remove r from position 5,63B4S4n4)rFo+)FrB1
add C at position 4,63B4CS4n4)rFo+)FrB1
remove B from position 17,63B4CS4n4)rFo+)Fr1
remove F from position 11,63B4CS4n4)ro+)Fr1
replace 4 at position 6 with l,63B4CSln4)ro+)Fr1
add 3 at position 5,63B4C3Sln4)ro+)Fr1
replace r at position 16 with C,63B4C3Sln4)ro+)FC1
remove r from position 11,63B4C3Sln4)o+)FC1
replace F at position 14 with C,63B4C3Sln4)o+)CC1
remove S from position 6,63B4C3ln4)o+)CC1
add 2 at position 6,63B4C32ln4)o+)CC1
add H at position 2,63HB4C32ln4)o+)CC1
replace H at position 2 with n,63nB4C32ln4)o+)CC1
replace 4 at position 10 with r,63nB4C32lnr)o+)CC1
add S at position 2,63SnB4C32lnr)o+)CC1
replace 3 at position 1 with +,6+SnB4C32lnr)o+)CC1
add H at position 1,6H+SnB4C32lnr)o+)CC1
replace + at position 2 with #,6H#SnB4C32lnr)o+)CC1
replace l at position 10 with +,6H#SnB4C32+nr)o+)CC1
add O at position 8,6H#SnB4CO32+nr)o+)CC1
add = at position 5,6H#Sn=B4CO32+nr)o+)CC1
remove S from position 3,6H#n=B4CO32+nr)o+)CC1
replace O at position 8 with 1,6H#n=B4C132+nr)o+)CC1
replace n at position 12 with ],6H#n=B4C132+]r)o+)CC1
replace n at position 3 with ],6H#]=B4C132+]r)o+)CC1
add C at position 3,6H#C]=B4C132+]r)o+)CC1
remove = from position 5,6H#C]B4C132+]r)o+)CC1
replace 3 at position 9 with O,6H#C]B4C1O2+]r)o+)CC1
add - at position 16,6H#C]B4C1O2+]r)o-+)CC1
replace B at position 5 with =,6H#C]=4C1O2+]r)o-+)CC1
replace o at position 15 with (,6H#C]=4C1O2+]r)(-+)CC1
replace ( at position 15 with ),6H#C]=4C1O2+]r))-+)CC1
add C at position 18,6H#C]=4C1O2+]r))-+C)CC1
add [ at position 11,6H#C]=4C1O2[+]r))-+C)CC1
replace H at position 1 with O,6O#C]=4C1O2[+]r))-+C)CC1
remove ] from position 4,6O#C=4C1O2[+]r))-+C)CC1
replace 4 at position 5 with ),6O#C=)C1O2[+]r))-+C)CC1
remove O from position 8,6O#C=)C12[+]r))-+C)CC1
add C at position 14,6O#C=)C12[+]r)C)-+C)CC1
remove - from position 16,6O#C=)C12[+]r)C)+C)CC1
remove ] from position 11,6O#C=)C12[+r)C)+C)CC1
add ( at position 4,6O#C(=)C12[+r)C)+C)CC1
add H at position 2,6OH#C(=)C12[+r)C)+C)CC1
remove H from position 2,6O#C(=)C12[+r)C)+C)CC1
add H at position 12,6O#C(=)C12[+Hr)C)+C)CC1
add C at position 22,6O#C(=)C12[+Hr)C)+C)CCC1
replace # at position 2 with C,6OCC(=)C12[+Hr)C)+C)CCC1
add C at position 0,C6OCC(=)C12[+Hr)C)+C)CCC1
replace r at position 14 with ],C6OCC(=)C12[+H])C)+C)CCC1
add C at position 20,C6OCC(=)C12[+H])C)+CC)CCC1
add ( at position 11,C6OCC(=)C12([+H])C)+CC)CCC1
replace O at position 2 with C,C6CCC(=)C12([+H])C)+CC)CCC1
add ) at position 8,C6CCC(=))C12([+H])C)+CC)CCC1
add C at position 16,C6CCC(=))C12([+HC])C)+CC)CCC1
add C at position 19,C6CCC(=))C12([+HC])CC)+CC)CCC1
add C at position 29,C6CCC(=))C12([+HC])CC)+CC)CCCC1
replace ) at position 7 with O,C6CCC(=O)C12([+HC])CC)+CC)CCCC1
replace C at position 16 with +,C6CCC(=O)C12([+H+])CC)+CC)CCCC1
remove + from position 22,C6CCC(=O)C12([+H+])CC)CC)CCCC1
add C at position 4,C6CCCC(=O)C12([+H+])CC)CC)CCCC1
add s at position 30,C6CCCC(=O)C12([+H+])CC)CC)CCCCs1
remove 6 from position 1,CCCCC(=O)C12([+H+])CC)CC)CCCCs1
remove 2 from position 11,CCCCC(=O)C1([+H+])CC)CC)CCCCs1
add # at position 2,CC#CCC(=O)C1([+H+])CC)CC)CCCCs1
replace ) at position 18 with (,CC#CCC(=O)C1([+H+](CC)CC)CCCCs1
remove s from position 29,CC#CCC(=O)C1([+H+](CC)CC)CCCC1
replace + at position 14 with N,CC#CCC(=O)C1([NH+](CC)CC)CCCC1
final: CC#CCC(=O)C1([NH+](CC)CC)CCCC1,CC#CCC(=O)C1([NH+](CC)CC)CCCC1
