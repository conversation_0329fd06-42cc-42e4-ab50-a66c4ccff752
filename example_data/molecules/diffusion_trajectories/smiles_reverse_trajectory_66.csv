log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 3,oFc3BH-C
remove c from position 2,oF3BH-<PERSON>
remove B from position 3,oF3H-<PERSON>
replace - at position 4 with ],oF3H]C
remove C from position 5,oF3H]
add B at position 4,oF3HB]
add N at position 6,oF3HB]N
replace B at position 4 with -,oF3H-]N
add ) at position 7,oF3H-]N)
add S at position 6,oF3H-]SN)
add [ at position 2,oF[3H-]SN)
add 5 at position 3,oF[53H-]SN)
replace S at position 8 with l,oF[53H-]lN)
add c at position 2,oFc[53H-]lN)
add 4 at position 2,oF4c[53H-]lN)
replace [ at position 4 with H,oF4cH53H-]lN)
add ] at position 5,oF4cH]53H-]lN)
add O at position 2,oFO4cH]53H-]lN)
remove H from position 9,oFO4cH]53-]lN)
add 6 at position 13,oFO4cH]53-]lN6)
replace F at position 1 with n,onO4cH]53-]lN6)
remove H from position 5,onO4c]53-]lN6)
replace 4 at position 3 with r,onOrc]53-]lN6)
add N at position 6,onOrc]N53-]lN6)
add s at position 11,onOrc]N53-]slN6)
remove 6 from position 14,onOrc]N53-]slN)
replace ] at position 10 with H,onOrc]N53-HslN)
remove O from position 2,onrc]N53-HslN)
add F at position 11,onrc]N53-HsFlN)
replace N at position 13 with 1,onrc]N53-HsFl1)
add S at position 6,onrc]NS53-HsFl1)
add l at position 2,onlrc]NS53-HsFl1)
add F at position 11,onlrc]NS53-FHsFl1)
replace o at position 0 with S,Snlrc]NS53-FHsFl1)
remove S from position 7,Snlrc]N53-FHsFl1)
add 4 at position 10,Snlrc]N53-4FHsFl1)
remove l from position 2,Snrc]N53-4FHsFl1)
add r at position 1,Srnrc]N53-4FHsFl1)
add ) at position 12,Srnrc]N53-4F)HsFl1)
replace F at position 11 with 3,Srnrc]N53-43)HsFl1)
add H at position 19,Srnrc]N53-43)HsFl1)H
add 1 at position 2,Sr1nrc]N53-43)HsFl1)H
add C at position 1,SCr1nrc]N53-43)HsFl1)H
add C at position 21,SCr1nrc]N53-43)HsFl1)CH
remove r from position 2,SC1nrc]N53-43)HsFl1)CH
add B at position 14,SC1nrc]N53-43)BHsFl1)CH
add C at position 19,SC1nrc]N53-43)BHsFlC1)CH
add - at position 17,SC1nrc]N53-43)BHs-FlC1)CH
replace - at position 10 with 6,SC1nrc]N53643)BHs-FlC1)CH
add c at position 12,SC1nrc]N5364c3)BHs-FlC1)CH
replace C at position 24 with r,SC1nrc]N5364c3)BHs-FlC1)rH
replace 1 at position 22 with (,SC1nrc]N5364c3)BHs-FlC()rH
remove s from position 17,SC1nrc]N5364c3)BH-FlC()rH
add B at position 19,SC1nrc]N5364c3)BH-FBlC()rH
replace r at position 4 with @,SC1n@c]N5364c3)BH-FBlC()rH
add / at position 21,SC1n@c]N5364c3)BH-FBl/C()rH
remove B from position 15,SC1n@c]N5364c3)H-FBl/C()rH
add 2 at position 18,SC1n@c]N5364c3)H-F2Bl/C()rH
replace S at position 0 with 7,7C1n@c]N5364c3)H-F2Bl/C()rH
add C at position 20,7C1n@c]N5364c3)H-F2BCl/C()rH
add N at position 11,7C1n@c]N536N4c3)H-F2BCl/C()rH
add ) at position 29,7C1n@c]N536N4c3)H-F2BCl/C()rH)
replace 3 at position 9 with 7,7C1n@c]N576N4c3)H-F2BCl/C()rH)
replace ) at position 29 with 7,7C1n@c]N576N4c3)H-F2BCl/C()rH7
add ] at position 13,7C1n@c]N576N4]c3)H-F2BCl/C()rH7
add 3 at position 20,7C1n@c]N576N4]c3)H-F32BCl/C()rH7
remove 1 from position 2,7Cn@c]N576N4]c3)H-F32BCl/C()rH7
remove 7 from position 0,Cn@c]N576N4]c3)H-F32BCl/C()rH7
replace 6 at position 8 with C,Cn@c]N57CN4]c3)H-F32BCl/C()rH7
add + at position 3,Cn@+c]N57CN4]c3)H-F32BCl/C()rH7
remove H from position 16,Cn@+c]N57CN4]c3)-F32BCl/C()rH7
replace 3 at position 14 with r,Cn@+c]N57CN4]cr)-F32BCl/C()rH7
add l at position 10,Cn@+c]N57ClN4]cr)-F32BCl/C()rH7
add F at position 13,Cn@+c]N57ClN4F]cr)-F32BCl/C()rH7
replace ) at position 17 with c,Cn@+c]N57ClN4F]crc-F32BCl/C()rH7
add 7 at position 32,Cn@+c]N57ClN4F]crc-F32BCl/C()rH77
add 5 at position 18,Cn@+c]N57ClN4F]crc5-F32BCl/C()rH77
add 7 at position 21,Cn@+c]N57ClN4F]crc5-F732BCl/C()rH77
replace 5 at position 18 with (,Cn@+c]N57ClN4F]crc(-F732BCl/C()rH77
replace 7 at position 21 with ),Cn@+c]N57ClN4F]crc(-F)32BCl/C()rH77
remove ) from position 30,Cn@+c]N57ClN4F]crc(-F)32BCl/C(rH77
add 4 at position 6,Cn@+c]4N57ClN4F]crc(-F)32BCl/C(rH77
add o at position 29,Cn@+c]4N57ClN4F]crc(-F)32BCl/oC(rH77
add # at position 30,Cn@+c]4N57ClN4F]crc(-F)32BCl/o#C(rH77
remove r from position 33,Cn@+c]4N57ClN4F]crc(-F)32BCl/o#C(H77
add r at position 22,Cn@+c]4N57ClN4F]crc(-Fr)32BCl/o#C(H77
add c at position 23,Cn@+c]4N57ClN4F]crc(-Frc)32BCl/o#C(H77
remove / from position 30,Cn@+c]4N57ClN4F]crc(-Frc)32BClo#C(H77
add ) at position 30,Cn@+c]4N57ClN4F]crc(-Frc)32BCl)o#C(H77
add o at position 19,Cn@+c]4N57ClN4F]crco(-Frc)32BCl)o#C(H77
add l at position 4,Cn@+lc]4N57ClN4F]crco(-Frc)32BCl)o#C(H77
replace 7 at position 10 with C,Cn@+lc]4N5CClN4F]crco(-Frc)32BCl)o#C(H77
remove 2 from position 28,Cn@+lc]4N5CClN4F]crco(-Frc)3BCl)o#C(H77
remove 7 from position 37,Cn@+lc]4N5CClN4F]crco(-Frc)3BCl)o#C(H7
add r at position 17,Cn@+lc]4N5CClN4F]rcrco(-Frc)3BCl)o#C(H7
add = at position 7,Cn@+lc]=4N5CClN4F]rcrco(-Frc)3BCl)o#C(H7
add ) at position 36,Cn@+lc]=4N5CClN4F]rcrco(-Frc)3BCl)o#)C(H7
add c at position 26,Cn@+lc]=4N5CClN4F]rcrco(-Fcrc)3BCl)o#)C(H7
replace c at position 5 with +,Cn@+l+]=4N5CClN4F]rcrco(-Fcrc)3BCl)o#)C(H7
add S at position 9,Cn@+l+]=4SN5CClN4F]rcrco(-Fcrc)3BCl)o#)C(H7
add 1 at position 43,Cn@+l+]=4SN5CClN4F]rcrco(-Fcrc)3BCl)o#)C(H71
add ( at position 19,Cn@+l+]=4SN5CClN4F](rcrco(-Fcrc)3BCl)o#)C(H71
replace F at position 17 with n,Cn@+l+]=4SN5CClN4n](rcrco(-Fcrc)3BCl)o#)C(H71
remove 7 from position 43,Cn@+l+]=4SN5CClN4n](rcrco(-Fcrc)3BCl)o#)C(H1
add F at position 20,Cn@+l+]=4SN5CClN4n](Frcrco(-Fcrc)3BCl)o#)C(H1
replace @ at position 2 with 2,Cn2+l+]=4SN5CClN4n](Frcrco(-Fcrc)3BCl)o#)C(H1
add C at position 9,Cn2+l+]=4CSN5CClN4n](Frcrco(-Fcrc)3BCl)o#)C(H1
remove ( from position 43,Cn2+l+]=4CSN5CClN4n](Frcrco(-Fcrc)3BCl)o#)CH1
add r at position 10,Cn2+l+]=4CrSN5CClN4n](Frcrco(-Fcrc)3BCl)o#)CH1
remove ) from position 34,Cn2+l+]=4CrSN5CClN4n](Frcrco(-Fcrc3BCl)o#)CH1
remove r from position 10,Cn2+l+]=4CSN5CClN4n](Frcrco(-Fcrc3BCl)o#)CH1
add S at position 8,Cn2+l+]=S4CSN5CClN4n](Frcrco(-Fcrc3BCl)o#)CH1
remove B from position 35,Cn2+l+]=S4CSN5CClN4n](Frcrco(-Fcrc3Cl)o#)CH1
remove F from position 22,Cn2+l+]=S4CSN5CClN4n](rcrco(-Fcrc3Cl)o#)CH1
replace 5 at position 13 with 3,Cn2+l+]=S4CSN3CClN4n](rcrco(-Fcrc3Cl)o#)CH1
add C at position 11,Cn2+l+]=S4CCSN3CClN4n](rcrco(-Fcrc3Cl)o#)CH1
replace r at position 32 with c,Cn2+l+]=S4CCSN3CClN4n](rcrco(-Fccc3Cl)o#)CH1
remove r from position 23,Cn2+l+]=S4CCSN3CClN4n](crco(-Fccc3Cl)o#)CH1
replace F at position 29 with 3,Cn2+l+]=S4CCSN3CClN4n](crco(-3ccc3Cl)o#)CH1
remove S from position 12,Cn2+l+]=S4CCN3CClN4n](crco(-3ccc3Cl)o#)CH1
add @ at position 12,Cn2+l+]=S4CC@N3CClN4n](crco(-3ccc3Cl)o#)CH1
add H at position 4,Cn2+Hl+]=S4CC@N3CClN4n](crco(-3ccc3Cl)o#)CH1
replace H at position 4 with [,Cn2+[l+]=S4CC@N3CClN4n](crco(-3ccc3Cl)o#)CH1
replace 4 at position 20 with ],Cn2+[l+]=S4CC@N3CClN]n](crco(-3ccc3Cl)o#)CH1
add S at position 5,Cn2+[Sl+]=S4CC@N3CClN]n](crco(-3ccc3Cl)o#)CH1
replace 2 at position 2 with I,CnI+[Sl+]=S4CC@N3CClN]n](crco(-3ccc3Cl)o#)CH1
add N at position 3,CnIN+[Sl+]=S4CC@N3CClN]n](crco(-3ccc3Cl)o#)CH1
replace + at position 4 with #,CnIN#[Sl+]=S4CC@N3CClN]n](crco(-3ccc3Cl)o#)CH1
replace l at position 20 with C,CnIN#[Sl+]=S4CC@N3CCCN]n](crco(-3ccc3Cl)o#)CH1
add 1 at position 17,CnIN#[Sl+]=S4CC@N13CCCN]n](crco(-3ccc3Cl)o#)CH1
add @ at position 10,CnIN#[Sl+]@=S4CC@N13CCCN]n](crco(-3ccc3Cl)o#)CH1
remove S from position 6,CnIN#[l+]@=S4CC@N13CCCN]n](crco(-3ccc3Cl)o#)CH1
replace N at position 16 with ],CnIN#[l+]@=S4CC@]13CCCN]n](crco(-3ccc3Cl)o#)CH1
replace n at position 24 with H,CnIN#[l+]@=S4CC@]13CCCN]H](crco(-3ccc3Cl)o#)CH1
replace l at position 6 with n,CnIN#[n+]@=S4CC@]13CCCN]H](crco(-3ccc3Cl)o#)CH1
add H at position 7,CnIN#[nH+]@=S4CC@]13CCCN]H](crco(-3ccc3Cl)o#)CH1
remove = from position 11,CnIN#[nH+]@S4CC@]13CCCN]H](crco(-3ccc3Cl)o#)CH1
replace 3 at position 18 with O,CnIN#[nH+]@S4CC@]1OCCCN]H](crco(-3ccc3Cl)o#)CH1
add - at position 33,CnIN#[nH+]@S4CC@]1OCCCN]H](crco(--3ccc3Cl)o#)CH1
replace @ at position 10 with c,CnIN#[nH+]cS4CC@]1OCCCN]H](crco(--3ccc3Cl)o#)CH1
replace o at position 30 with (,CnIN#[nH+]cS4CC@]1OCCCN]H](crc((--3ccc3Cl)o#)CH1
replace ( at position 30 with c,CnIN#[nH+]cS4CC@]1OCCCN]H](crcc(--3ccc3Cl)o#)CH1
add c at position 37,CnIN#[nH+]cS4CC@]1OCCCN]H](crcc(--3cccc3Cl)o#)CH1
add [ at position 22,CnIN#[nH+]cS4CC@]1OCCC[N]H](crcc(--3cccc3Cl)o#)CH1
replace I at position 2 with 1,Cn1N#[nH+]cS4CC@]1OCCC[N]H](crcc(--3cccc3Cl)o#)CH1
remove ] from position 9,Cn1N#[nH+cS4CC@]1OCCC[N]H](crcc(--3cccc3Cl)o#)CH1
replace 4 at position 11 with 1,Cn1N#[nH+cS1CC@]1OCCC[N]H](crcc(--3cccc3Cl)o#)CH1
remove O from position 17,Cn1N#[nH+cS1CC@]1CCC[N]H](crcc(--3cccc3Cl)o#)CH1
add r at position 29,Cn1N#[nH+cS1CC@]1CCC[N]H](crcrc(--3cccc3Cl)o#)CH1
remove - from position 33,Cn1N#[nH+cS1CC@]1CCC[N]H](crcrc(-3cccc3Cl)o#)CH1
remove ] from position 22,Cn1N#[nH+cS1CC@]1CCC[NH](crcrc(-3cccc3Cl)o#)CH1
add ] at position 9,Cn1N#[nH+]cS1CC@]1CCC[NH](crcrc(-3cccc3Cl)o#)CH1
add H at position 4,Cn1NH#[nH+]cS1CC@]1CCC[NH](crcrc(-3cccc3Cl)o#)CH1
remove H from position 4,Cn1N#[nH+]cS1CC@]1CCC[NH](crcrc(-3cccc3Cl)o#)CH1
add + at position 24,Cn1N#[nH+]cS1CC@]1CCC[NH+](crcrc(-3cccc3Cl)o#)CH1
add 2 at position 45,Cn1N#[nH+]cS1CC@]1CCC[NH+](crcrc(-3cccc3Cl)o#2)CH1
replace # at position 4 with c,Cn1Nc[nH+]cS1CC@]1CCC[NH+](crcrc(-3cccc3Cl)o#2)CH1
add C at position 1,CCn1Nc[nH+]cS1CC@]1CCC[NH+](crcrc(-3cccc3Cl)o#2)CH1
replace r at position 29 with 2,CCn1Nc[nH+]cS1CC@]1CCC[NH+](c2crc(-3cccc3Cl)o#2)CH1
add ) at position 41,CCn1Nc[nH+]cS1CC@]1CCC[NH+](c2crc(-3cccc3)Cl)o#2)CH1
add 2 at position 22,CCn1Nc[nH+]cS1CC@]1CCC2[NH+](c2crc(-3cccc3)Cl)o#2)CH1
replace N at position 4 with c,CCn1cc[nH+]cS1CC@]1CCC2[NH+](c2crc(-3cccc3)Cl)o#2)CH1
add H at position 17,CCn1cc[nH+]cS1CC@H]1CCC2[NH+](c2crc(-3cccc3)Cl)o#2)CH1
add n at position 32,CCn1cc[nH+]cS1CC@H]1CCC2[NH+](c2ncrc(-3cccc3)Cl)o#2)CH1
add c at position 38,CCn1cc[nH+]cS1CC@H]1CCC2[NH+](c2ncrc(-c3cccc3)Cl)o#2)CH1
remove r from position 34,CCn1cc[nH+]cS1CC@H]1CCC2[NH+](c2ncc(-c3cccc3)Cl)o#2)CH1
add [ at position 15,CCn1cc[nH+]cS1C[C@H]1CCC2[NH+](c2ncc(-c3cccc3)Cl)o#2)CH1
remove C from position 0,Cn1cc[nH+]cS1C[C@H]1CCC2[NH+](c2ncc(-c3cccc3)Cl)o#2)CH1
remove S from position 11,Cn1cc[nH+]c1C[C@H]1CCC2[NH+](c2ncc(-c3cccc3)Cl)o#2)CH1
add + at position 29,Cn1cc[nH+]c1C[C@H]1CCC2[NH+](+c2ncc(-c3cccc3)Cl)o#2)CH1
add c at position 39,Cn1cc[nH+]c1C[C@H]1CCC2[NH+](+c2ncc(-c3ccccc3)Cl)o#2)CH1
remove # from position 50,Cn1cc[nH+]c1C[C@H]1CCC2[NH+](+c2ncc(-c3ccccc3)Cl)o2)CH1
remove 2 from position 22,Cn1cc[nH+]c1C[C@H]1CCC[NH+](+c2ncc(-c3ccccc3)Cl)o2)CH1
remove ) from position 44,Cn1cc[nH+]c1C[C@H]1CCC[NH+](+c2ncc(-c3ccccc3Cl)o2)CH1
remove H from position 51,Cn1cc[nH+]c1C[C@H]1CCC[NH+](+c2ncc(-c3ccccc3Cl)o2)C1
replace + at position 28 with C,Cn1cc[nH+]c1C[C@H]1CCC[NH+](Cc2ncc(-c3ccccc3Cl)o2)C1
final: Cn1cc[nH+]c1C[C@H]1CCC[NH+](Cc2ncc(-c3ccccc3Cl)o2)C1,Cn1cc[nH+]c1C[C@H]1CCC[NH+](Cc2ncc(-c3ccccc3Cl)o2)C1
