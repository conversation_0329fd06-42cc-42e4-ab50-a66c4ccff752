log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with B,oFcBBH-C
remove c from position 2,oFBBH-C
remove B from position 3,oFBH-C
replace - at position 4 with ],oFBH]C
remove C from position 5,oFBH]
add B at position 4,oFBHB]
add N at position 6,oFBHB]N
replace B at position 4 with -,oFBH-]N
add r at position 7,oFBH-]Nr
add [ at position 6,oFBH-][Nr
add [ at position 2,oF[BH-][Nr
add 5 at position 3,oF[5BH-][Nr
replace [ at position 8 with B,oF[5BH-]BNr
add l at position 2,oFl[5BH-]BNr
add 4 at position 2,oF4l[5BH-]BNr
replace [ at position 4 with H,oF4lH5BH-]BNr
add 6 at position 5,oF4lH65BH-]BNr
add O at position 2,oFO4lH65BH-]BNr
remove H from position 9,oFO4lH65B-]BNr
add 6 at position 13,oFO4lH65B-]BN6r
replace F at position 1 with H,oHO4lH65B-]BN6r
remove H from position 5,oHO4l65B-]BN6r
replace 4 at position 3 with r,oHOrl65B-]BN6r
add = at position 6,oHOrl6=5B-]BN6r
add s at position 11,oHOrl6=5B-]sBN6r
remove 6 from position 14,oHOrl6=5B-]sBNr
replace ] at position 10 with B,oHOrl6=5B-BsBNr
remove O from position 2,oHrl6=5B-BsBNr
add 2 at position 11,oHrl6=5B-Bs2BNr
replace N at position 13 with /,oHrl6=5B-Bs2B/r
add S at position 6,oHrl6=S5B-Bs2B/r
add l at position 2,oHlrl6=S5B-Bs2B/r
add F at position 11,oHlrl6=S5B-FBs2B/r
replace o at position 0 with S,SHlrl6=S5B-FBs2B/r
remove S from position 7,SHlrl6=5B-FBs2B/r
add - at position 10,SHlrl6=5B--FBs2B/r
remove l from position 2,SHrl6=5B--FBs2B/r
add r at position 1,SrHrl6=5B--FBs2B/r
add ) at position 12,SrHrl6=5B--F)Bs2B/r
replace F at position 11 with 3,SrHrl6=5B--3)Bs2B/r
add 4 at position 19,SrHrl6=5B--3)Bs2B/r4
add ) at position 2,Sr)Hrl6=5B--3)Bs2B/r4
add 6 at position 1,S6r)Hrl6=5B--3)Bs2B/r4
add F at position 21,S6r)Hrl6=5B--3)Bs2B/rF4
remove r from position 2,S6)Hrl6=5B--3)Bs2B/rF4
add c at position 14,S6)Hrl6=5B--3)cBs2B/rF4
add ) at position 19,S6)Hrl6=5B--3)cBs2B)/rF4
add c at position 17,S6)Hrl6=5B--3)cBsc2B)/rF4
replace - at position 10 with l,S6)Hrl6=5Bl-3)cBsc2B)/rF4
add = at position 12,S6)Hrl6=5Bl-=3)cBsc2B)/rF4
replace F at position 24 with (,S6)Hrl6=5Bl-=3)cBsc2B)/r(4
replace / at position 22 with n,S6)Hrl6=5Bl-=3)cBsc2B)nr(4
remove s from position 17,S6)Hrl6=5Bl-=3)cBc2B)nr(4
add - at position 19,S6)Hrl6=5Bl-=3)cBc2-B)nr(4
replace r at position 4 with ],S6)H]l6=5Bl-=3)cBc2-B)nr(4
add ( at position 21,S6)H]l6=5Bl-=3)cBc2-B()nr(4
add S at position 5,S6)H]Sl6=5Bl-=3)cBc2-B()nr(4
add ) at position 27,S6)H]Sl6=5Bl-=3)cBc2-B()nr()4
replace S at position 0 with N,N6)H]Sl6=5Bl-=3)cBc2-B()nr()4
add 3 at position 20,N6)H]Sl6=5Bl-=3)cBc23-B()nr()4
replace 6 at position 7 with O,N6)H]SlO=5Bl-=3)cBc23-B()nr()4
add n at position 15,N6)H]SlO=5Bl-=3n)cBc23-B()nr()4
replace 4 at position 30 with 7,N6)H]SlO=5Bl-=3n)cBc23-B()nr()7
replace ) at position 29 with 7,N6)H]SlO=5Bl-=3n)cBc23-B()nr(77
add ) at position 27,N6)H]SlO=5Bl-=3n)cBc23-B()n)r(77
replace 5 at position 9 with O,N6)H]SlO=OBl-=3n)cBc23-B()n)r(77
add @ at position 2,N6@)H]SlO=OBl-=3n)cBc23-B()n)r(77
replace ( at position 25 with /,N6@)H]SlO=OBl-=3n)cBc23-B/)n)r(77
remove - from position 23,N6@)H]SlO=OBl-=3n)cBc23B/)n)r(77
remove l from position 7,N6@)H]SO=OBl-=3n)cBc23B/)n)r(77
replace 3 at position 14 with r,N6@)H]SO=OBl-=rn)cBc23B/)n)r(77
add ) at position 21,N6@)H]SO=OBl-=rn)cBc2)3B/)n)r(77
add 4 at position 13,N6@)H]SO=OBl-4=rn)cBc2)3B/)n)r(77
replace ) at position 17 with c,N6@)H]SO=OBl-4=rnccBc2)3B/)n)r(77
add 1 at position 32,N6@)H]SO=OBl-4=rnccBc2)3B/)n)r(717
add 4 at position 18,N6@)H]SO=OBl-4=rnc4cBc2)3B/)n)r(717
add 6 at position 21,N6@)H]SO=OBl-4=rnc4cB6c2)3B/)n)r(717
replace 4 at position 18 with o,N6@)H]SO=OBl-4=rncocB6c2)3B/)n)r(717
replace 6 at position 21 with r,N6@)H]SO=OBl-4=rncocBrc2)3B/)n)r(717
remove ) from position 30,N6@)H]SO=OBl-4=rncocBrc2)3B/)nr(717
add 3 at position 6,N6@)H]3SO=OBl-4=rncocBrc2)3B/)nr(717
add ) at position 29,N6@)H]3SO=OBl-4=rncocBrc2)3B/))nr(717
add s at position 30,N6@)H]3SO=OBl-4=rncocBrc2)3B/)s)nr(717
remove r from position 33,N6@)H]3SO=OBl-4=rncocBrc2)3B/)s)n(717
add F at position 22,N6@)H]3SO=OBl-4=rncocBFrc2)3B/)s)n(717
add ( at position 23,N6@)H]3SO=OBl-4=rncocBF(rc2)3B/)s)n(717
remove / from position 30,N6@)H]3SO=OBl-4=rncocBF(rc2)3B)s)n(717
add ) at position 30,N6@)H]3SO=OBl-4=rncocBF(rc2)3B))s)n(717
add ( at position 19,N6@)H]3SO=OBl-4=rnc(ocBF(rc2)3B))s)n(717
add n at position 4,N6@)nH]3SO=OBl-4=rnc(ocBF(rc2)3B))s)n(717
replace = at position 10 with 5,N6@)nH]3SO5OBl-4=rnc(ocBF(rc2)3B))s)n(717
remove 2 from position 28,N6@)nH]3SO5OBl-4=rnc(ocBF(rc)3B))s)n(717
remove 7 from position 37,N6@)nH]3SO5OBl-4=rnc(ocBF(rc)3B))s)n(17
add c at position 17,N6@)nH]3SO5OBl-4=crnc(ocBF(rc)3B))s)n(17
add = at position 7,N6@)nH]=3SO5OBl-4=crnc(ocBF(rc)3B))s)n(17
add 2 at position 36,N6@)nH]=3SO5OBl-4=crnc(ocBF(rc)3B))s2)n(17
replace = at position 17 with F,N6@)nH]=3SO5OBl-4Fcrnc(ocBF(rc)3B))s2)n(17
replace ) at position 34 with c,N6@)nH]=3SO5OBl-4Fcrnc(ocBF(rc)3B)cs2)n(17
add ) at position 36,N6@)nH]=3SO5OBl-4Fcrnc(ocBF(rc)3B)cs)2)n(17
add 2 at position 19,N6@)nH]=3SO5OBl-4Fc2rnc(ocBF(rc)3B)cs)2)n(17
replace F at position 17 with n,N6@)nH]=3SO5OBl-4nc2rnc(ocBF(rc)3B)cs)2)n(17
remove 7 from position 43,N6@)nH]=3SO5OBl-4nc2rnc(ocBF(rc)3B)cs)2)n(1
add F at position 20,N6@)nH]=3SO5OBl-4nc2Frnc(ocBF(rc)3B)cs)2)n(1
replace @ at position 2 with 2,N62)nH]=3SO5OBl-4nc2Frnc(ocBF(rc)3B)cs)2)n(1
add ) at position 9,N62)nH]=3)SO5OBl-4nc2Frnc(ocBF(rc)3B)cs)2)n(1
remove ( from position 43,N62)nH]=3)SO5OBl-4nc2Frnc(ocBF(rc)3B)cs)2)n1
add r at position 10,N62)nH]=3)rSO5OBl-4nc2Frnc(ocBF(rc)3B)cs)2)n1
remove ) from position 34,N62)nH]=3)rSO5OBl-4nc2Frnc(ocBF(rc3B)cs)2)n1
remove r from position 10,N62)nH]=3)SO5OBl-4nc2Frnc(ocBF(rc3B)cs)2)n1
add c at position 8,N62)nH]=c3)SO5OBl-4nc2Frnc(ocBF(rc3B)cs)2)n1
remove B from position 35,N62)nH]=c3)SO5OBl-4nc2Frnc(ocBF(rc3)cs)2)n1
remove F from position 22,N62)nH]=c3)SO5OBl-4nc2rnc(ocBF(rc3)cs)2)n1
replace 5 at position 13 with 3,N62)nH]=c3)SO3OBl-4nc2rnc(ocBF(rc3)cs)2)n1
add - at position 11,N62)nH]=c3)-SO3OBl-4nc2rnc(ocBF(rc3)cs)2)n1
replace r at position 32 with (,N62)nH]=c3)-SO3OBl-4nc2rnc(ocBF((c3)cs)2)n1
remove r from position 23,N62)nH]=c3)-SO3OBl-4nc2nc(ocBF((c3)cs)2)n1
replace F at position 29 with /,N62)nH]=c3)-SO3OBl-4nc2nc(ocB/((c3)cs)2)n1
remove S from position 12,N62)nH]=c3)-O3OBl-4nc2nc(ocB/((c3)cs)2)n1
add C at position 12,N62)nH]=c3)-CO3OBl-4nc2nc(ocB/((c3)cs)2)n1
add H at position 4,N62)HnH]=c3)-CO3OBl-4nc2nc(ocB/((c3)cs)2)n1
replace H at position 4 with O,N62)OnH]=c3)-CO3OBl-4nc2nc(ocB/((c3)cs)2)n1
replace 4 at position 20 with ],N62)OnH]=c3)-CO3OBl-]nc2nc(ocB/((c3)cs)2)n1
add S at position 5,N62)OSnH]=c3)-CO3OBl-]nc2nc(ocB/((c3)cs)2)n1
replace 2 at position 2 with I,N6I)OSnH]=c3)-CO3OBl-]nc2nc(ocB/((c3)cs)2)n1
add c at position 3,N6Ic)OSnH]=c3)-CO3OBl-]nc2nc(ocB/((c3)cs)2)n1
replace ) at position 4 with =,N6Ic=OSnH]=c3)-CO3OBl-]nc2nc(ocB/((c3)cs)2)n1
replace l at position 20 with ),N6Ic=OSnH]=c3)-CO3OB)-]nc2nc(ocB/((c3)cs)2)n1
add 2 at position 17,N6Ic=OSnH]=c3)-CO23OB)-]nc2nc(ocB/((c3)cs)2)n1
add @ at position 10,N6Ic=OSnH]@=c3)-CO23OB)-]nc2nc(ocB/((c3)cs)2)n1
remove S from position 6,N6Ic=OnH]@=c3)-CO23OB)-]nc2nc(ocB/((c3)cs)2)n1
replace O at position 16 with (,N6Ic=OnH]@=c3)-C(23OB)-]nc2nc(ocB/((c3)cs)2)n1
replace n at position 24 with 2,N6Ic=OnH]@=c3)-C(23OB)-]2c2nc(ocB/((c3)cs)2)n1
replace n at position 6 with ),N6Ic=O)H]@=c3)-C(23OB)-]2c2nc(ocB/((c3)cs)2)n1
add n at position 7,N6Ic=O)nH]@=c3)-C(23OB)-]2c2nc(ocB/((c3)cs)2)n1
remove = from position 11,N6Ic=O)nH]@c3)-C(23OB)-]2c2nc(ocB/((c3)cs)2)n1
replace 3 at position 18 with O,N6Ic=O)nH]@c3)-C(2OOB)-]2c2nc(ocB/((c3)cs)2)n1
add - at position 33,N6Ic=O)nH]@c3)-C(2OOB)-]2c2nc(ocB-/((c3)cs)2)n1
replace @ at position 10 with ],N6Ic=O)nH]]c3)-C(2OOB)-]2c2nc(ocB-/((c3)cs)2)n1
replace o at position 30 with (,N6Ic=O)nH]]c3)-C(2OOB)-]2c2nc((cB-/((c3)cs)2)n1
replace ( at position 30 with c,N6Ic=O)nH]]c3)-C(2OOB)-]2c2nc(ccB-/((c3)cs)2)n1
add c at position 37,N6Ic=O)nH]]c3)-C(2OOB)-]2c2nc(ccB-/((cc3)cs)2)n1
add N at position 22,N6Ic=O)nH]]c3)-C(2OOB)N-]2c2nc(ccB-/((cc3)cs)2)n1
replace I at position 2 with 1,N61c=O)nH]]c3)-C(2OOB)N-]2c2nc(ccB-/((cc3)cs)2)n1
remove ] from position 9,N61c=O)nH]c3)-C(2OOB)N-]2c2nc(ccB-/((cc3)cs)2)n1
replace 3 at position 11 with S,N61c=O)nH]cS)-C(2OOB)N-]2c2nc(ccB-/((cc3)cs)2)n1
remove O from position 17,N61c=O)nH]cS)-C(2OB)N-]2c2nc(ccB-/((cc3)cs)2)n1
add + at position 29,N61c=O)nH]cS)-C(2OB)N-]2c2nc(+ccB-/((cc3)cs)2)n1
remove - from position 33,N61c=O)nH]cS)-C(2OB)N-]2c2nc(+ccB/((cc3)cs)2)n1
remove ] from position 22,N61c=O)nH]cS)-C(2OB)N-2c2nc(+ccB/((cc3)cs)2)n1
remove - from position 13,N61c=O)nH]cS)C(2OB)N-2c2nc(+ccB/((cc3)cs)2)n1
remove / from position 31,N61c=O)nH]cS)C(2OB)N-2c2nc(+ccB((cc3)cs)2)n1
add c at position 3,N61cc=O)nH]cS)C(2OB)N-2c2nc(+ccB((cc3)cs)2)n1
add + at position 42,N61cc=O)nH]cS)C(2OB)N-2c2nc(+ccB((cc3)cs)2+)n1
remove - from position 21,N61cc=O)nH]cS)C(2OB)N2c2nc(+ccB((cc3)cs)2+)n1
add B at position 32,N61cc=O)nH]cS)C(2OB)N2c2nc(+ccB(B(cc3)cs)2+)n1
add r at position 33,N61cc=O)nH]cS)C(2OB)N2c2nc(+ccB(Br(cc3)cs)2+)n1
add ( at position 12,N61cc=O)nH]c(S)C(2OB)N2c2nc(+ccB(Br(cc3)cs)2+)n1
replace 2 at position 17 with =,N61cc=O)nH]c(S)C(=OB)N2c2nc(+ccB(Br(cc3)cs)2+)n1
remove B from position 19,N61cc=O)nH]c(S)C(=O)N2c2nc(+ccB(Br(cc3)cs)2+)n1
add ) at position 35,N61cc=O)nH]c(S)C(=O)N2c2nc(+ccB(Br()cc3)cs)2+)n1
add c at position 2,N6c1cc=O)nH]c(S)C(=O)N2c2nc(+ccB(Br()cc3)cs)2+)n1
remove ) from position 43,N6c1cc=O)nH]c(S)C(=O)N2c2nc(+ccB(Br()cc3)cs2+)n1
add 3 at position 29,N6c1cc=O)nH]c(S)C(=O)N2c2nc(+3ccB(Br()cc3)cs2+)n1
replace ) at position 15 with C,N6c1cc=O)nH]c(SCC(=O)N2c2nc(+3ccB(Br()cc3)cs2+)n1
replace B at position 32 with c,N6c1cc=O)nH]c(SCC(=O)N2c2nc(+3ccc(Br()cc3)cs2+)n1
remove + from position 45,N6c1cc=O)nH]c(SCC(=O)N2c2nc(+3ccc(Br()cc3)cs2)n1
add [ at position 9,N6c1cc=O)[nH]c(SCC(=O)N2c2nc(+3ccc(Br()cc3)cs2)n1
add c at position 30,N6c1cc=O)[nH]c(SCC(=O)N2c2nc(+c3ccc(Br()cc3)cs2)n1
remove 6 from position 1,Nc1cc=O)[nH]c(SCC(=O)N2c2nc(+c3ccc(Br()cc3)cs2)n1
remove 2 from position 22,Nc1cc=O)[nH]c(SCC(=O)Nc2nc(+c3ccc(Br()cc3)cs2)n1
add ( at position 5,Nc1cc(=O)[nH]c(SCC(=O)Nc2nc(+c3ccc(Br()cc3)cs2)n1
remove ( from position 37,Nc1cc(=O)[nH]c(SCC(=O)Nc2nc(+c3ccc(Br)cc3)cs2)n1
replace + at position 28 with -,Nc1cc(=O)[nH]c(SCC(=O)Nc2nc(-c3ccc(Br)cc3)cs2)n1
final: Nc1cc(=O)[nH]c(SCC(=O)Nc2nc(-c3ccc(Br)cc3)cs2)n1,Nc1cc(=O)[nH]c(SCC(=O)Nc2nc(-c3ccc(Br)cc3)cs2)n1
