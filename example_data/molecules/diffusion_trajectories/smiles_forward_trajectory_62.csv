log,state
initialize: CC[C@]1(c2ccccc2)NC(=O)N(CCOc2ccc(Cl)cc2Cl)C1=O,CC[C@]1(c2ccccc2)NC(=O)N(CCOc2ccc(Cl)cc2Cl)C1=O
replace c at position 28 with +,CC[C@]1(c2ccccc2)NC(=O)N(CCO+2ccc(Cl)cc2Cl)C1=O
add ( at position 37,CC[C@]1(c2ccccc2)NC(=O)N(CCO+2ccc(Cl)(cc2Cl)C1=O
remove ] from position 5,CC[C@1(c2ccccc2)NC(=O)N(CCO+2ccc(Cl)(cc2Cl)C1=O
add 2 at position 22,CC[C@1(c2ccccc2)NC(=O)2N(CCO+2ccc(Cl)(cc2Cl)C1=O
add 6 at position 1,C6C[C@1(c2ccccc2)NC(=O)2N(CCO+2ccc(Cl)(cc2Cl)C1=O
remove 2 from position 30,C6C[C@1(c2ccccc2)NC(=O)2N(CCO+ccc(Cl)(cc2Cl)C1=O
remove 2 from position 9,C6C[C@1(cccccc2)NC(=O)2N(CCO+ccc(Cl)(cc2Cl)C1=O
add + at position 45,C6C[C@1(cccccc2)NC(=O)2N(CCO+ccc(Cl)(cc2Cl)C1+=O
replace ( at position 32 with C,C6C[C@1(cccccc2)NC(=O)2N(CCO+cccCCl)(cc2Cl)C1+=O
replace ) at position 15 with +,C6C[C@1(cccccc2+NC(=O)2N(CCO+cccCCl)(cc2Cl)C1+=O
remove c from position 29,C6C[C@1(cccccc2+NC(=O)2N(CCO+ccCCl)(cc2Cl)C1+=O
add ) at position 43,C6C[C@1(cccccc2+NC(=O)2N(CCO+ccCCl)(cc2Cl)C)1+=O
remove C from position 2,C6[C@1(cccccc2+NC(=O)2N(CCO+ccCCl)(cc2Cl)C)1+=O
remove c from position 35,C6[C@1(cccccc2+NC(=O)2N(CCO+ccCCl)(c2Cl)C)1+=O
add B at position 19,C6[C@1(cccccc2+NC(=BO)2N(CCO+ccCCl)(c2Cl)C)1+=O
replace ( at position 17 with 3,C6[C@1(cccccc2+NC3=BO)2N(CCO+ccCCl)(c2Cl)C)1+=O
remove c from position 12,C6[C@1(ccccc2+NC3=BO)2N(CCO+ccCCl)(c2Cl)C)1+=O
remove ) from position 33,C6[C@1(ccccc2+NC3=BO)2N(CCO+ccCCl(c2Cl)C)1+=O
remove l from position 32,C6[C@1(ccccc2+NC3=BO)2N(CCO+ccCC(c2Cl)C)1+=O
add - at position 21,C6[C@1(ccccc2+NC3=BO)-2N(CCO+ccCC(c2Cl)C)1+=O
remove + from position 42,C6[C@1(ccccc2+NC3=BO)-2N(CCO+ccCC(c2Cl)C)1=O
remove C from position 3,C6[@1(ccccc2+NC3=BO)-2N(CCO+ccCC(c2Cl)C)1=O
add / at position 31,C6[@1(ccccc2+NC3=BO)-2N(CCO+ccC/C(c2Cl)C)1=O
add - at position 13,C6[@1(ccccc2+-NC3=BO)-2N(CCO+ccC/C(c2Cl)C)1=O
add ] at position 22,C6[@1(ccccc2+-NC3=BO)-]2N(CCO+ccC/C(c2Cl)C)1=O
add - at position 33,C6[@1(ccccc2+-NC3=BO)-]2N(CCO+ccC-/C(c2Cl)C)1=O
remove + from position 29,C6[@1(ccccc2+-NC3=BO)-]2N(CCOccC-/C(c2Cl)C)1=O
add O at position 17,C6[@1(ccccc2+-NC3O=BO)-]2N(CCOccC-/C(c2Cl)C)1=O
replace 2 at position 11 with 4,C6[@1(ccccc4+-NC3O=BO)-]2N(CCOccC-/C(c2Cl)C)1=O
add ] at position 9,C6[@1(ccc]cc4+-NC3O=BO)-]2N(CCOccC-/C(c2Cl)C)1=O
replace [ at position 2 with H,C6H@1(ccc]cc4+-NC3O=BO)-]2N(CCOccC-/C(c2Cl)C)1=O
remove ) from position 22,C6H@1(ccc]cc4+-NC3O=BO-]2N(CCOccC-/C(c2Cl)C)1=O
remove c from position 37,C6H@1(ccc]cc4+-NC3O=BO-]2N(CCOccC-/C(2Cl)C)1=O
replace c at position 30 with (,C6H@1(ccc]cc4+-NC3O=BO-]2N(CCO(cC-/C(2Cl)C)1=O
replace ( at position 30 with o,C6H@1(ccc]cc4+-NC3O=BO-]2N(CCOocC-/C(2Cl)C)1=O
replace c at position 10 with @,C6H@1(ccc]@c4+-NC3O=BO-]2N(CCOocC-/C(2Cl)C)1=O
remove - from position 33,C6H@1(ccc]@c4+-NC3O=BO-]2N(CCOocC/C(2Cl)C)1=O
replace O at position 18 with 3,C6H@1(ccc]@c4+-NC33=BO-]2N(CCOocC/C(2Cl)C)1=O
add = at position 11,C6H@1(ccc]@=c4+-NC33=BO-]2N(CCOocC/C(2Cl)C)1=O
remove c from position 7,C6H@1(cc]@=c4+-NC33=BO-]2N(CCOocC/C(2Cl)C)1=O
replace c at position 6 with n,C6H@1(nc]@=c4+-NC33=BO-]2N(CCOocC/C(2Cl)C)1=O
replace 2 at position 24 with n,C6H@1(nc]@=c4+-NC33=BO-]nN(CCOocC/C(2Cl)C)1=O
replace C at position 16 with O,C6H@1(nc]@=c4+-NO33=BO-]nN(CCOocC/C(2Cl)C)1=O
add S at position 6,C6H@1(Snc]@=c4+-NO33=BO-]nN(CCOocC/C(2Cl)C)1=O
remove @ from position 10,C6H@1(Snc]=c4+-NO33=BO-]nN(CCOocC/C(2Cl)C)1=O
remove 3 from position 17,C6H@1(Snc]=c4+-NO3=BO-]nN(CCOocC/C(2Cl)C)1=O
replace O at position 20 with l,C6H@1(Snc]=c4+-NO3=Bl-]nN(CCOocC/C(2Cl)C)1=O
replace 1 at position 4 with ),C6H@)(Snc]=c4+-NO3=Bl-]nN(CCOocC/C(2Cl)C)1=O
remove @ from position 3,C6H)(Snc]=c4+-NO3=Bl-]nN(CCOocC/C(2Cl)C)1=O
replace H at position 2 with 2,C62)(Snc]=c4+-NO3=Bl-]nN(CCOocC/C(2Cl)C)1=O
remove S from position 5,C62)(nc]=c4+-NO3=Bl-]nN(CCOocC/C(2Cl)C)1=O
replace ] at position 20 with 4,C62)(nc]=c4+-NO3=Bl-4nN(CCOocC/C(2Cl)C)1=O
replace ( at position 4 with I,C62)Inc]=c4+-NO3=Bl-4nN(CCOocC/C(2Cl)C)1=O
remove I from position 4,C62)nc]=c4+-NO3=Bl-4nN(CCOocC/C(2Cl)C)1=O
remove N from position 12,C62)nc]=c4+-O3=Bl-4nN(CCOocC/C(2Cl)C)1=O
add S at position 12,C62)nc]=c4+-SO3=Bl-4nN(CCOocC/C(2Cl)C)1=O
replace / at position 29 with F,C62)nc]=c4+-SO3=Bl-4nN(CCOocCFC(2Cl)C)1=O
add r at position 23,C62)nc]=c4+-SO3=Bl-4nN(rCCOocCFC(2Cl)C)1=O
replace ( at position 32 with r,C62)nc]=c4+-SO3=Bl-4nN(rCCOocCFCr2Cl)C)1=O
remove - from position 11,C62)nc]=c4+SO3=Bl-4nN(rCCOocCFCr2Cl)C)1=O
replace 3 at position 13 with 5,C62)nc]=c4+SO5=Bl-4nN(rCCOocCFCr2Cl)C)1=O
add F at position 22,C62)nc]=c4+SO5=Bl-4nN(FrCCOocCFCr2Cl)C)1=O
add B at position 35,C62)nc]=c4+SO5=Bl-4nN(FrCCOocCFCr2CBl)C)1=O
remove c from position 8,C62)nc]=4+SO5=Bl-4nN(FrCCOocCFCr2CBl)C)1=O
add r at position 10,C62)nc]=4+rSO5=Bl-4nN(FrCCOocCFCr2CBl)C)1=O
add ) at position 34,C62)nc]=4+rSO5=Bl-4nN(FrCCOocCFCr2)CBl)C)1=O
remove r from position 10,C62)nc]=4+SO5=Bl-4nN(FrCCOocCFCr2)CBl)C)1=O
add ( at position 43,C62)nc]=4+SO5=Bl-4nN(FrCCOocCFCr2)CBl)C)1=O(
remove + from position 9,C62)nc]=4SO5=Bl-4nN(FrCCOocCFCr2)CBl)C)1=O(
replace 2 at position 2 with @,C6@)nc]=4SO5=Bl-4nN(FrCCOocCFCr2)CBl)C)1=O(
remove F from position 20,C6@)nc]=4SO5=Bl-4nN(rCCOocCFCr2)CBl)C)1=O(
add r at position 24,C6@)nc]=4SO5=Bl-4nN(rCCOrocCFCr2)CBl)C)1=O(
replace n at position 17 with F,C6@)nc]=4SO5=Bl-4FN(rCCOrocCFCr2)CBl)C)1=O(
remove ( from position 19,C6@)nc]=4SO5=Bl-4FNrCCOrocCFCr2)CBl)C)1=O(
remove C from position 36,C6@)nc]=4SO5=Bl-4FNrCCOrocCFCr2)CBl))1=O(
replace l at position 34 with ),C6@)nc]=4SO5=Bl-4FNrCCOrocCFCr2)CB)))1=O(
replace F at position 17 with =,C6@)nc]=4SO5=Bl-4=NrCCOrocCFCr2)CB)))1=O(
remove ) from position 36,C6@)nc]=4SO5=Bl-4=NrCCOrocCFCr2)CB))1=O(
remove = from position 7,C6@)nc]4SO5=Bl-4=NrCCOrocCFCr2)CB))1=O(
remove N from position 17,C6@)nc]4SO5=Bl-4=rCCOrocCFCr2)CB))1=O(
add 7 at position 37,C6@)nc]4SO5=Bl-4=rCCOrocCFCr2)CB))1=O7(
add 2 at position 28,C6@)nc]4SO5=Bl-4=rCCOrocCFCr22)CB))1=O7(
replace 5 at position 10 with =,C6@)nc]4SO==Bl-4=rCCOrocCFCr22)CB))1=O7(
remove n from position 4,C6@)c]4SO==Bl-4=rCCOrocCFCr22)CB))1=O7(
remove O from position 19,C6@)c]4SO==Bl-4=rCCrocCFCr22)CB))1=O7(
remove B from position 30,C6@)c]4SO==Bl-4=rCCrocCFCr22)C))1=O7(
add / at position 30,C6@)c]4SO==Bl-4=rCCrocCFCr22)C/))1=O7(
remove F from position 23,C6@)c]4SO==Bl-4=rCCrocCCr22)C/))1=O7(
remove C from position 22,C6@)c]4SO==Bl-4=rCCrocCr22)C/))1=O7(
add r at position 33,C6@)c]4SO==Bl-4=rCCrocCr22)C/))1=rO7(
remove ) from position 30,C6@)c]4SO==Bl-4=rCCrocCr22)C/)1=rO7(
remove ) from position 29,C6@)c]4SO==Bl-4=rCCrocCr22)C/1=rO7(
remove 4 from position 6,C6@)c]SO==Bl-4=rCCrocCr22)C/1=rO7(
add ) at position 30,C6@)c]SO==Bl-4=rCCrocCr22)C/1=)rO7(
replace C at position 21 with 6,C6@)c]SO==Bl-4=rCCroc6r22)C/1=)rO7(
replace r at position 18 with 4,C6@)c]SO==Bl-4=rCC4oc6r22)C/1=)rO7(
remove 6 from position 21,C6@)c]SO==Bl-4=rCC4ocr22)C/1=)rO7(
remove 4 from position 18,C6@)c]SO==Bl-4=rCCocr22)C/1=)rO7(
remove ( from position 32,C6@)c]SO==Bl-4=rCCocr22)C/1=)rO7
replace C at position 17 with ),C6@)c]SO==Bl-4=rC)ocr22)C/1=)rO7
remove 4 from position 13,C6@)c]SO==Bl-=rC)ocr22)C/1=)rO7
remove B from position 10,C6@)c]SO==l-=rC)ocr22)C/1=)rO7
replace C at position 14 with 3,C6@)c]SO==l-=r3)ocr22)C/1=)rO7
add H at position 16,C6@)c]SO==l-=r3)Hocr22)C/1=)rO7
remove ) from position 3,C6@c]SO==l-=r3)Hocr22)C/1=)rO7
replace = at position 8 with 6,C6@c]SO=6l-=r3)Hocr22)C/1=)rO7
add 7 at position 0,7C6@c]SO=6l-=r3)Hocr22)C/1=)rO7
add 1 at position 2,7C16@c]SO=6l-=r3)Hocr22)C/1=)rO7
remove r from position 20,7C16@c]SO=6l-=r3)Hoc22)C/1=)rO7
remove = from position 13,7C16@c]SO=6l-r3)Hoc22)C/1=)rO7
replace 7 at position 29 with ),7C16@c]SO=6l-r3)Hoc22)C/1=)rO)
replace = at position 9 with 3,7C16@c]SO36l-r3)Hoc22)C/1=)rO)
remove ) from position 29,7C16@c]SO36l-r3)Hoc22)C/1=)rO
remove l from position 11,7C16@c]SO36-r3)Hoc22)C/1=)rO
remove ) from position 20,7C16@c]SO36-r3)Hoc22C/1=)rO
replace 7 at position 0 with S,SC16@c]SO36-r3)Hoc22C/1=)rO
remove 2 from position 18,SC16@c]SO36-r3)Hoc2C/1=)rO
add B at position 15,SC16@c]SO36-r3)BHoc2C/1=)rO
remove / from position 21,SC16@c]SO36-r3)BHoc2C1=)rO
replace @ at position 4 with r,SC16rc]SO36-r3)BHoc2C1=)rO
remove 2 from position 19,SC16rc]SO36-r3)BHocC1=)rO
add s at position 17,SC16rc]SO36-r3)BHsocC1=)rO
replace = at position 22 with /,SC16rc]SO36-r3)BHsocC1/)rO
replace r at position 24 with C,SC16rc]SO36-r3)BHsocC1/)CO
remove r from position 12,SC16rc]SO36-3)BHsocC1/)CO
replace 6 at position 10 with -,SC16rc]SO3--3)BHsocC1/)CO
remove o from position 17,SC16rc]SO3--3)BHscC1/)CO
remove 1 from position 19,SC16rc]SO3--3)BHscC/)CO
remove B from position 14,SC16rc]SO3--3)HscC/)CO
add r at position 2,SCr16rc]SO3--3)HscC/)CO
remove C from position 21,SCr16rc]SO3--3)HscC/)O
remove C from position 1,Sr16rc]SO3--3)HscC/)O
remove 1 from position 2,Sr6rc]SO3--3)HscC/)O
remove O from position 19,Sr6rc]SO3--3)HscC/)
replace 3 at position 11 with F,Sr6rc]SO3--F)HscC/)
remove ) from position 12,Sr6rc]SO3--FHscC/)
remove r from position 1,S6rc]SO3--FHscC/)
add l at position 2,S6lrc]SO3--FHscC/)
remove - from position 10,S6lrc]SO3-FHscC/)
add S at position 7,S6lrc]SSO3-FHscC/)
replace S at position 0 with o,o6lrc]SSO3-FHscC/)
remove F from position 11,o6lrc]SSO3-HscC/)
remove l from position 2,o6rc]SSO3-HscC/)
remove S from position 6,o6rc]SO3-HscC/)
replace / at position 13 with N,o6rc]SO3-HscCN)
remove c from position 11,o6rc]SO3-HsCN)
add O at position 2,o6Orc]SO3-HsCN)
replace H at position 10 with ],o6Orc]SO3-]sCN)
add 6 at position 14,o6Orc]SO3-]sCN6)
remove s from position 11,o6Orc]SO3-]CN6)
remove S from position 6,o6Orc]O3-]CN6)
replace r at position 3 with 4,o6O4c]O3-]CN6)
add H at position 5,o6O4cH]O3-]CN6)
replace 6 at position 1 with H,oHO4cH]O3-]CN6)
remove 6 from position 13,oHO4cH]O3-]CN)
add H at position 9,oHO4cH]O3H-]CN)
remove O from position 2,oH4cH]O3H-]CN)
remove ] from position 5,oH4cHO3H-]CN)
replace H at position 4 with [,oH4c[O3H-]CN)
remove 4 from position 2,oHc[O3H-]CN)
remove c from position 2,oH[O3H-]CN)
replace C at position 8 with [,oH[O3H-][N)
remove O from position 3,oH[3H-][N)
remove [ from position 2,oH3H-][N)
remove [ from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
