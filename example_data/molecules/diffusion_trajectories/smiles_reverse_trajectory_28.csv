log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 3,oFc3BH-C
remove c from position 2,oF3BH-<PERSON>
remove B from position 3,oF3H-<PERSON>
replace - at position 4 with ],oF3H]C
remove C from position 5,oF3H]
add B at position 4,oF3HB]
add N at position 6,oF3HB]N
replace B at position 4 with -,oF3H-]N
add ) at position 7,oF3H-]N)
add S at position 6,oF3H-]SN)
add [ at position 2,oF[3H-]SN)
add 3 at position 3,oF[33H-]SN)
replace S at position 8 with r,oF[33H-]rN)
add n at position 2,oFn[33H-]rN)
add 4 at position 2,oF4n[33H-]rN)
replace [ at position 4 with H,oF4nH33H-]rN)
add + at position 5,oF4nH+33H-]rN)
add O at position 2,oFO4nH+33H-]rN)
remove H from position 9,oFO4nH+33-]rN)
add 6 at position 13,oFO4nH+33-]rN6)
replace F at position 1 with c,ocO4nH+33-]rN6)
remove H from position 5,ocO4n+33-]rN6)
replace 4 at position 3 with r,ocOrn+33-]rN6)
add r at position 6,ocOrn+r33-]rN6)
add s at position 11,ocOrn+r33-]srN6)
remove 6 from position 14,ocOrn+r33-]srN)
replace ] at position 10 with H,ocOrn+r33-HsrN)
remove O from position 2,ocrn+r33-HsrN)
add 7 at position 11,ocrn+r33-Hs7rN)
replace N at position 13 with /,ocrn+r33-Hs7r/)
add S at position 6,ocrn+rS33-Hs7r/)
add l at position 2,oclrn+rS33-Hs7r/)
add F at position 11,oclrn+rS33-FHs7r/)
replace o at position 0 with S,Sclrn+rS33-FHs7r/)
remove S from position 7,Sclrn+r33-FHs7r/)
add c at position 10,Sclrn+r33-cFHs7r/)
remove l from position 2,Scrn+r33-cFHs7r/)
add r at position 1,Srcrn+r33-cFHs7r/)
add ) at position 12,Srcrn+r33-cF)Hs7r/)
replace F at position 11 with 4,Srcrn+r33-c4)Hs7r/)
add c at position 19,Srcrn+r33-c4)Hs7r/)c
add 1 at position 2,Sr1crn+r33-c4)Hs7r/)c
add C at position 1,SCr1crn+r33-c4)Hs7r/)c
add C at position 21,SCr1crn+r33-c4)Hs7r/)Cc
remove r from position 2,SC1crn+r33-c4)Hs7r/)Cc
add B at position 14,SC1crn+r33-c4)BHs7r/)Cc
add ) at position 19,SC1crn+r33-c4)BHs7r)/)Cc
add r at position 17,SC1crn+r33-c4)BHsr7r)/)Cc
replace - at position 10 with 6,SC1crn+r336c4)BHsr7r)/)Cc
add ) at position 12,SC1crn+r336c)4)BHsr7r)/)Cc
replace C at position 24 with r,SC1crn+r336c)4)BHsr7r)/)rc
replace / at position 22 with c,SC1crn+r336c)4)BHsr7r)c)rc
remove s from position 17,SC1crn+r336c)4)BHr7r)c)rc
add F at position 19,SC1crn+r336c)4)BHr7Fr)c)rc
replace r at position 4 with 2,SC1c2n+r336c)4)BHr7Fr)c)rc
add / at position 21,SC1c2n+r336c)4)BHr7Fr/)c)rc
remove B from position 15,SC1c2n+r336c)4)Hr7Fr/)c)rc
add 2 at position 18,SC1c2n+r336c)4)Hr72Fr/)c)rc
replace S at position 0 with 7,7C1c2n+r336c)4)Hr72Fr/)c)rc
add ) at position 20,7C1c2n+r336c)4)Hr72F)r/)c)rc
add c at position 11,7C1c2n+r336cc)4)Hr72F)r/)c)rc
add ) at position 29,7C1c2n+r336cc)4)Hr72F)r/)c)rc)
replace 3 at position 9 with 7,7C1c2n+r376cc)4)Hr72F)r/)c)rc)
replace ) at position 29 with 7,7C1c2n+r376cc)4)Hr72F)r/)c)rc7
add = at position 13,7C1c2n+r376cc=)4)Hr72F)r/)c)rc7
add ( at position 20,7C1c2n+r376cc=)4)Hr7(2F)r/)c)rc7
remove 1 from position 2,7Cc2n+r376cc=)4)Hr7(2F)r/)c)rc7
remove 7 from position 0,Cc2n+r376cc=)4)Hr7(2F)r/)c)rc7
replace 6 at position 8 with S,Cc2n+r37Scc=)4)Hr7(2F)r/)c)rc7
add N at position 3,Cc2Nn+r37Scc=)4)Hr7(2F)r/)c)rc7
remove H from position 16,Cc2Nn+r37Scc=)4)r7(2F)r/)c)rc7
replace 4 at position 14 with +,Cc2Nn+r37Scc=)+)r7(2F)r/)c)rc7
add 5 at position 10,Cc2Nn+r37S5cc=)+)r7(2F)r/)c)rc7
add l at position 13,Cc2Nn+r37S5ccl=)+)r7(2F)r/)c)rc7
replace ) at position 17 with F,Cc2Nn+r37S5ccl=)+Fr7(2F)r/)c)rc7
add # at position 32,Cc2Nn+r37S5ccl=)+Fr7(2F)r/)c)rc7#
add 4 at position 18,Cc2Nn+r37S5ccl=)+F4r7(2F)r/)c)rc7#
add 6 at position 21,Cc2Nn+r37S5ccl=)+F4r76(2F)r/)c)rc7#
replace 4 at position 18 with s,Cc2Nn+r37S5ccl=)+Fsr76(2F)r/)c)rc7#
replace 6 at position 21 with C,Cc2Nn+r37S5ccl=)+Fsr7C(2F)r/)c)rc7#
remove ) from position 30,Cc2Nn+r37S5ccl=)+Fsr7C(2F)r/)crc7#
add = at position 6,Cc2Nn+=r37S5ccl=)+Fsr7C(2F)r/)crc7#
add + at position 29,Cc2Nn+=r37S5ccl=)+Fsr7C(2F)r/+)crc7#
add C at position 30,Cc2Nn+=r37S5ccl=)+Fsr7C(2F)r/+C)crc7#
remove r from position 33,Cc2Nn+=r37S5ccl=)+Fsr7C(2F)r/+C)cc7#
add c at position 22,Cc2Nn+=r37S5ccl=)+Fsr7cC(2F)r/+C)cc7#
add o at position 23,Cc2Nn+=r37S5ccl=)+Fsr7coC(2F)r/+C)cc7#
remove / from position 30,Cc2Nn+=r37S5ccl=)+Fsr7coC(2F)r+C)cc7#
add ( at position 30,Cc2Nn+=r37S5ccl=)+Fsr7coC(2F)r(+C)cc7#
add r at position 19,Cc2Nn+=r37S5ccl=)+Frsr7coC(2F)r(+C)cc7#
add + at position 4,Cc2N+n+=r37S5ccl=)+Frsr7coC(2F)r(+C)cc7#
replace 7 at position 10 with O,Cc2N+n+=r3OS5ccl=)+Frsr7coC(2F)r(+C)cc7#
remove 2 from position 28,Cc2N+n+=r3OS5ccl=)+Frsr7coC(F)r(+C)cc7#
remove 7 from position 37,Cc2N+n+=r3OS5ccl=)+Frsr7coC(F)r(+C)cc#
add 4 at position 17,Cc2N+n+=r3OS5ccl=4)+Frsr7coC(F)r(+C)cc#
add ] at position 7,Cc2N+n+]=r3OS5ccl=4)+Frsr7coC(F)r(+C)cc#
add C at position 36,Cc2N+n+]=r3OS5ccl=4)+Frsr7coC(F)r(+CC)cc#
replace = at position 17 with H,Cc2N+n+]=r3OS5cclH4)+Frsr7coC(F)r(+CC)cc#
replace + at position 34 with ),Cc2N+n+]=r3OS5cclH4)+Frsr7coC(F)r()CC)cc#
add B at position 36,Cc2N+n+]=r3OS5cclH4)+Frsr7coC(F)r()CBC)cc#
add n at position 19,Cc2N+n+]=r3OS5cclH4n)+Frsr7coC(F)r()CBC)cc#
replace H at position 17 with (,Cc2N+n+]=r3OS5ccl(4n)+Frsr7coC(F)r()CBC)cc#
replace s at position 24 with 1,Cc2N+n+]=r3OS5ccl(4n)+Fr1r7coC(F)r()CBC)cc#
add N at position 13,Cc2N+n+]=r3OSN5ccl(4n)+Fr1r7coC(F)r()CBC)cc#
remove 7 from position 27,Cc2N+n+]=r3OSN5ccl(4n)+Fr1rcoC(F)r()CBC)cc#
remove r from position 9,Cc2N+n+]=3OSN5ccl(4n)+Fr1rcoC(F)r()CBC)cc#
remove N from position 3,Cc2+n+]=3OSN5ccl(4n)+Fr1rcoC(F)r()CBC)cc#
add r at position 10,Cc2+n+]=3OrSN5ccl(4n)+Fr1rcoC(F)r()CBC)cc#
remove ) from position 34,Cc2+n+]=3OrSN5ccl(4n)+Fr1rcoC(F)r(CBC)cc#
remove r from position 10,Cc2+n+]=3OSN5ccl(4n)+Fr1rcoC(F)r(CBC)cc#
add S at position 8,Cc2+n+]=S3OSN5ccl(4n)+Fr1rcoC(F)r(CBC)cc#
remove B from position 35,Cc2+n+]=S3OSN5ccl(4n)+Fr1rcoC(F)r(CC)cc#
remove F from position 22,Cc2+n+]=S3OSN5ccl(4n)+r1rcoC(F)r(CC)cc#
replace 5 at position 13 with 3,Cc2+n+]=S3OSN3ccl(4n)+r1rcoC(F)r(CC)cc#
add [ at position 11,Cc2+n+]=S3O[SN3ccl(4n)+r1rcoC(F)r(CC)cc#
replace r at position 32 with N,Cc2+n+]=S3O[SN3ccl(4n)+r1rcoC(F)N(CC)cc#
remove r from position 23,Cc2+n+]=S3O[SN3ccl(4n)+1rcoC(F)N(CC)cc#
replace F at position 29 with =,Cc2+n+]=S3O[SN3ccl(4n)+1rcoC(=)N(CC)cc#
remove S from position 12,Cc2+n+]=S3O[N3ccl(4n)+1rcoC(=)N(CC)cc#
add O at position 12,Cc2+n+]=S3O[ON3ccl(4n)+1rcoC(=)N(CC)cc#
add I at position 4,Cc2+In+]=S3O[ON3ccl(4n)+1rcoC(=)N(CC)cc#
replace I at position 4 with (,Cc2+(n+]=S3O[ON3ccl(4n)+1rcoC(=)N(CC)cc#
replace 4 at position 20 with ],Cc2+(n+]=S3O[ON3ccl(]n)+1rcoC(=)N(CC)cc#
add S at position 5,Cc2+(Sn+]=S3O[ON3ccl(]n)+1rcoC(=)N(CC)cc#
replace 2 at position 2 with I,CcI+(Sn+]=S3O[ON3ccl(]n)+1rcoC(=)N(CC)cc#
add N at position 3,CcIN+(Sn+]=S3O[ON3ccl(]n)+1rcoC(=)N(CC)cc#
replace + at position 4 with #,CcIN#(Sn+]=S3O[ON3ccl(]n)+1rcoC(=)N(CC)cc#
replace l at position 20 with 1,CcIN#(Sn+]=S3O[ON3cc1(]n)+1rcoC(=)N(CC)cc#
add ) at position 17,CcIN#(Sn+]=S3O[ON)3cc1(]n)+1rcoC(=)N(CC)cc#
add B at position 10,CcIN#(Sn+]B=S3O[ON)3cc1(]n)+1rcoC(=)N(CC)cc#
remove S from position 6,CcIN#(n+]B=S3O[ON)3cc1(]n)+1rcoC(=)N(CC)cc#
replace N at position 16 with ],CcIN#(n+]B=S3O[O])3cc1(]n)+1rcoC(=)N(CC)cc#
replace n at position 24 with =,CcIN#(n+]B=S3O[O])3cc1(]=)+1rcoC(=)N(CC)cc#
replace n at position 6 with [,CcIN#([+]B=S3O[O])3cc1(]=)+1rcoC(=)N(CC)cc#
add N at position 7,CcIN#([N+]B=S3O[O])3cc1(]=)+1rcoC(=)N(CC)cc#
remove = from position 11,CcIN#([N+]BS3O[O])3cc1(]=)+1rcoC(=)N(CC)cc#
replace 3 at position 18 with O,CcIN#([N+]BS3O[O])Occ1(]=)+1rcoC(=)N(CC)cc#
add - at position 33,CcIN#([N+]BS3O[O])Occ1(]=)+1rcoC(-=)N(CC)cc#
replace B at position 10 with (,CcIN#([N+](S3O[O])Occ1(]=)+1rcoC(-=)N(CC)cc#
replace o at position 30 with ),CcIN#([N+](S3O[O])Occ1(]=)+1rc)C(-=)N(CC)cc#
replace ) at position 30 with (,CcIN#([N+](S3O[O])Occ1(]=)+1rc(C(-=)N(CC)cc#
add C at position 37,CcIN#([N+](S3O[O])Occ1(]=)+1rc(C(-=)NC(CC)cc#
add C at position 22,CcIN#([N+](S3O[O])Occ1C(]=)+1rc(C(-=)NC(CC)cc#
replace I at position 2 with 1,Cc1N#([N+](S3O[O])Occ1C(]=)+1rc(C(-=)NC(CC)cc#
remove ] from position 9,Cc1N#([N+(S3O[O])Occ1C(]=)+1rc(C(-=)NC(CC)cc#
replace 3 at position 11 with =,Cc1N#([N+(S=O[O])Occ1C(]=)+1rc(C(-=)NC(CC)cc#
remove O from position 17,Cc1N#([N+(S=O[O])cc1C(]=)+1rc(C(-=)NC(CC)cc#
add r at position 29,Cc1N#([N+(S=O[O])cc1C(]=)+1rcr(C(-=)NC(CC)cc#
remove - from position 33,Cc1N#([N+(S=O[O])cc1C(]=)+1rcr(C(=)NC(CC)cc#
remove ] from position 22,Cc1N#([N+(S=O[O])cc1C(=)+1rcr(C(=)NC(CC)cc#
add ] at position 9,Cc1N#([N+](S=O[O])cc1C(=)+1rcr(C(=)NC(CC)cc#
add H at position 4,Cc1NH#([N+](S=O[O])cc1C(=)+1rcr(C(=)NC(CC)cc#
remove H from position 4,Cc1N#([N+](S=O[O])cc1C(=)+1rcr(C(=)NC(CC)cc#
add O at position 24,Cc1N#([N+](S=O[O])cc1C(=O)+1rcr(C(=)NC(CC)cc#
add 1 at position 45,Cc1N#([N+](S=O[O])cc1C(=O)+1rcr(C(=)NC(CC)cc#1
replace # at position 4 with c,Cc1Nc([N+](S=O[O])cc1C(=O)+1rcr(C(=)NC(CC)cc#1
add C at position 1,CCc1Nc([N+](S=O[O])cc1C(=O)+1rcr(C(=)NC(CC)cc#1
replace r at position 29 with c,CCc1Nc([N+](S=O[O])cc1C(=O)+1ccr(C(=)NC(CC)cc#1
add ) at position 41,CCc1Nc([N+](S=O[O])cc1C(=O)+1ccr(C(=)NC(C)C)cc#1
add 2 at position 22,CCc1Nc([N+](S=O[O])cc12C(=O)+1ccr(C(=)NC(C)C)cc#1
replace N at position 4 with c,CCc1cc([N+](S=O[O])cc12C(=O)+1ccr(C(=)NC(C)C)cc#1
add - at position 17,CCc1cc([N+](S=O[O-])cc12C(=O)+1ccr(C(=)NC(C)C)cc#1
add c at position 32,CCc1cc([N+](S=O[O-])cc12C(=O)+1cccr(C(=)NC(C)C)cc#1
add ( at position 38,CCc1cc([N+](S=O[O-])cc12C(=O)+1cccr(C((=)NC(C)C)cc#1
remove r from position 34,CCc1cc([N+](S=O[O-])cc12C(=O)+1ccc(C((=)NC(C)C)cc#1
add ) at position 15,CCc1cc([N+](S=O)[O-])cc12C(=O)+1ccc(C((=)NC(C)C)cc#1
remove C from position 0,Cc1cc([N+](S=O)[O-])cc12C(=O)+1ccc(C((=)NC(C)C)cc#1
remove S from position 11,Cc1cc([N+](=O)[O-])cc12C(=O)+1ccc(C((=)NC(C)C)cc#1
add c at position 29,Cc1cc([N+](=O)[O-])cc12C(=O)+c1ccc(C((=)NC(C)C)cc#1
add O at position 39,Cc1cc([N+](=O)[O-])cc12C(=O)+c1ccc(C((=O)NC(C)C)cc#1
remove # from position 50,Cc1cc([N+](=O)[O-])cc12C(=O)+c1ccc(C((=O)NC(C)C)cc1
remove 2 from position 22,Cc1cc([N+](=O)[O-])cc1C(=O)+c1ccc(C((=O)NC(C)C)cc1
add c at position 5,Cc1ccc([N+](=O)[O-])cc1C(=O)+c1ccc(C((=O)NC(C)C)cc1
remove ( from position 37,Cc1ccc([N+](=O)[O-])cc1C(=O)+c1ccc(C(=O)NC(C)C)cc1
replace + at position 28 with N,Cc1ccc([N+](=O)[O-])cc1C(=O)Nc1ccc(C(=O)NC(C)C)cc1
final: Cc1ccc([N+](=O)[O-])cc1C(=O)Nc1ccc(C(=O)NC(C)C)cc1,Cc1ccc([N+](=O)[O-])cc1C(=O)Nc1ccc(C(=O)NC(C)C)cc1
