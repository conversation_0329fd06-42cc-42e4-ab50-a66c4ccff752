log,state
initialize: Cc1cccn2c(=O)c(C(=O)NC[C@H]3CCO[C@@H]3C(C)C)cnc12,Cc1cccn2c(=O)c(C(=O)NC[C@H]3CCO[C@@H]3C(C)C)cnc12
replace C at position 28 with +,Cc1cccn2c(=O)c(C(=O)NC[C@H]3+CO[C@@H]3C(C)C)cnc12
add ( at position 37,Cc1cccn2c(=O)c(C(=O)NC[C@H]3+CO[C@@H](3C(C)C)cnc12
remove c from position 5,Cc1ccn2c(=O)c(C(=O)NC[C@H]3+CO[C@@H](3C(C)C)cnc12
add 2 at position 22,Cc1ccn2c(=O)c(C(=O)NC[2C@H]3+CO[C@@H](3C(C)C)cnc12
add # at position 50,Cc1ccn2c(=O)c(C(=O)NC[2C@H]3+CO[C@@H](3C(C)C)cnc12#
remove C from position 39,Cc1ccn2c(=O)c(C(=O)NC[2C@H]3+CO[C@@H](3(C)C)cnc12#
remove C from position 29,Cc1ccn2c(=O)c(C(=O)NC[2C@H]3+O[C@@H](3(C)C)cnc12#
add S at position 11,Cc1ccn2c(=OS)c(C(=O)NC[2C@H]3+O[C@@H](3(C)C)cnc12#
add C at position 0,CCc1ccn2c(=OS)c(C(=O)NC[2C@H]3+O[C@@H](3(C)C)cnc12#
remove ( from position 15,CCc1ccn2c(=OS)cC(=O)NC[2C@H]3+O[C@@H](3(C)C)cnc12#
add r at position 34,CCc1ccn2c(=OS)cC(=O)NC[2C@H]3+O[C@r@H](3(C)C)cnc12#
remove ( from position 38,CCc1ccn2c(=OS)cC(=O)NC[2C@H]3+O[C@r@H]3(C)C)cnc12#
remove C from position 32,CCc1ccn2c(=OS)cC(=O)NC[2C@H]3+O[@r@H]3(C)C)cnc12#
remove = from position 17,CCc1ccn2c(=OS)cC(O)NC[2C@H]3+O[@r@H]3(C)C)cnc12#
replace c at position 4 with N,CCc1Ncn2c(=OS)cC(O)NC[2C@H]3+O[@r@H]3(C)C)cnc12#
remove 2 from position 22,CCc1Ncn2c(=OS)cC(O)NC[C@H]3+O[@r@H]3(C)C)cnc12#
remove c from position 41,CCc1Ncn2c(=OS)cC(O)NC[C@H]3+O[@r@H]3(C)C)nc12#
replace [ at position 29 with r,CCc1Ncn2c(=OS)cC(O)NC[C@H]3+Or@r@H]3(C)C)nc12#
remove C from position 1,Cc1Ncn2c(=OS)cC(O)NC[C@H]3+Or@r@H]3(C)C)nc12#
replace c at position 4 with #,Cc1N#n2c(=OS)cC(O)NC[C@H]3+Or@r@H]3(C)C)nc12#
remove r from position 30,Cc1N#n2c(=OS)cC(O)NC[C@H]3+Or@@H]3(C)C)nc12#
remove O from position 10,Cc1N#n2c(=S)cC(O)NC[C@H]3+Or@@H]3(C)C)nc12#
add B at position 37,Cc1N#n2c(=S)cC(O)NC[C@H]3+Or@@H]3(C)CB)nc12#
add 1 at position 35,Cc1N#n2c(=S)cC(O)NC[C@H]3+Or@@H]3(C1)CB)nc12#
add ] at position 22,Cc1N#n2c(=S)cC(O)NC[C@]H]3+Or@@H]3(C1)CB)nc12#
add - at position 33,Cc1N#n2c(=S)cC(O)NC[C@]H]3+Or@@H]-3(C1)CB)nc12#
remove @ from position 29,Cc1N#n2c(=S)cC(O)NC[C@]H]3+Or@H]-3(C1)CB)nc12#
add O at position 17,Cc1N#n2c(=S)cC(O)ONC[C@]H]3+Or@H]-3(C1)CB)nc12#
replace ) at position 11 with 4,Cc1N#n2c(=S4cC(O)ONC[C@]H]3+Or@H]-3(C1)CB)nc12#
add ] at position 9,Cc1N#n2c(]=S4cC(O)ONC[C@]H]3+Or@H]-3(C1)CB)nc12#
replace 1 at position 2 with I,CcIN#n2c(]=S4cC(O)ONC[C@]H]3+Or@H]-3(C1)CB)nc12#
remove C from position 22,CcIN#n2c(]=S4cC(O)ONC[@]H]3+Or@H]-3(C1)CB)nc12#
remove 1 from position 37,CcIN#n2c(]=S4cC(O)ONC[@]H]3+Or@H]-3(C)CB)nc12#
replace @ at position 30 with (,CcIN#n2c(]=S4cC(O)ONC[@]H]3+Or(H]-3(C)CB)nc12#
replace ( at position 30 with o,CcIN#n2c(]=S4cC(O)ONC[@]H]3+OroH]-3(C)CB)nc12#
replace = at position 10 with B,CcIN#n2c(]BS4cC(O)ONC[@]H]3+OroH]-3(C)CB)nc12#
remove - from position 33,CcIN#n2c(]BS4cC(O)ONC[@]H]3+OroH]3(C)CB)nc12#
replace O at position 18 with 3,CcIN#n2c(]BS4cC(O)3NC[@]H]3+OroH]3(C)CB)nc12#
add = at position 11,CcIN#n2c(]B=S4cC(O)3NC[@]H]3+OroH]3(C)CB)nc12#
remove c from position 7,CcIN#n2(]B=S4cC(O)3NC[@]H]3+OroH]3(C)CB)nc12#
replace 2 at position 6 with n,CcIN#nn(]B=S4cC(O)3NC[@]H]3+OroH]3(C)CB)nc12#
replace H at position 24 with n,CcIN#nn(]B=S4cC(O)3NC[@]n]3+OroH]3(C)CB)nc12#
replace O at position 16 with N,CcIN#nn(]B=S4cC(N)3NC[@]n]3+OroH]3(C)CB)nc12#
add S at position 6,CcIN#nSn(]B=S4cC(N)3NC[@]n]3+OroH]3(C)CB)nc12#
remove B from position 10,CcIN#nSn(]=S4cC(N)3NC[@]n]3+OroH]3(C)CB)nc12#
remove ) from position 17,CcIN#nSn(]=S4cC(N3NC[@]n]3+OroH]3(C)CB)nc12#
replace [ at position 20 with l,CcIN#nSn(]=S4cC(N3NCl@]n]3+OroH]3(C)CB)nc12#
replace # at position 4 with +,CcIN+nSn(]=S4cC(N3NCl@]n]3+OroH]3(C)CB)nc12#
remove N from position 3,CcI+nSn(]=S4cC(N3NCl@]n]3+OroH]3(C)CB)nc12#
replace I at position 2 with 2,Cc2+nSn(]=S4cC(N3NCl@]n]3+OroH]3(C)CB)nc12#
remove S from position 5,Cc2+nn(]=S4cC(N3NCl@]n]3+OroH]3(C)CB)nc12#
replace ] at position 20 with 4,Cc2+nn(]=S4cC(N3NCl@4n]3+OroH]3(C)CB)nc12#
replace n at position 4 with H,Cc2+Hn(]=S4cC(N3NCl@4n]3+OroH]3(C)CB)nc12#
remove H from position 4,Cc2+n(]=S4cC(N3NCl@4n]3+OroH]3(C)CB)nc12#
remove ( from position 12,Cc2+n(]=S4cCN3NCl@4n]3+OroH]3(C)CB)nc12#
add S at position 12,Cc2+n(]=S4cCSN3NCl@4n]3+OroH]3(C)CB)nc12#
replace 3 at position 29 with F,Cc2+n(]=S4cCSN3NCl@4n]3+OroH]F(C)CB)nc12#
add r at position 23,Cc2+n(]=S4cCSN3NCl@4n]3r+OroH]F(C)CB)nc12#
replace C at position 32 with r,Cc2+n(]=S4cCSN3NCl@4n]3r+OroH]F(r)CB)nc12#
remove C from position 11,Cc2+n(]=S4cSN3NCl@4n]3r+OroH]F(r)CB)nc12#
replace 3 at position 13 with 5,Cc2+n(]=S4cSN5NCl@4n]3r+OroH]F(r)CB)nc12#
add F at position 22,Cc2+n(]=S4cSN5NCl@4n]3Fr+OroH]F(r)CB)nc12#
add B at position 35,Cc2+n(]=S4cSN5NCl@4n]3Fr+OroH]F(r)CBB)nc12#
remove S from position 8,Cc2+n(]=4cSN5NCl@4n]3Fr+OroH]F(r)CBB)nc12#
add r at position 10,Cc2+n(]=4crSN5NCl@4n]3Fr+OroH]F(r)CBB)nc12#
add ) at position 34,Cc2+n(]=4crSN5NCl@4n]3Fr+OroH]F(r))CBB)nc12#
remove r from position 10,Cc2+n(]=4cSN5NCl@4n]3Fr+OroH]F(r))CBB)nc12#
add ( at position 43,Cc2+n(]=4cSN5NCl@4n]3Fr+OroH]F(r))CBB)nc12#(
remove c from position 9,Cc2+n(]=4SN5NCl@4n]3Fr+OroH]F(r))CBB)nc12#(
replace 2 at position 2 with @,Cc@+n(]=4SN5NCl@4n]3Fr+OroH]F(r))CBB)nc12#(
remove F from position 20,Cc@+n(]=4SN5NCl@4n]3r+OroH]F(r))CBB)nc12#(
add r at position 24,Cc@+n(]=4SN5NCl@4n]3r+OrroH]F(r))CBB)nc12#(
replace n at position 17 with F,Cc@+n(]=4SN5NCl@4F]3r+OrroH]F(r))CBB)nc12#(
remove 3 from position 19,Cc@+n(]=4SN5NCl@4F]r+OrroH]F(r))CBB)nc12#(
remove n from position 36,Cc@+n(]=4SN5NCl@4F]r+OrroH]F(r))CBB)c12#(
replace B at position 34 with ),Cc@+n(]=4SN5NCl@4F]r+OrroH]F(r))CB))c12#(
replace F at position 17 with =,Cc@+n(]=4SN5NCl@4=]r+OrroH]F(r))CB))c12#(
remove c from position 36,Cc@+n(]=4SN5NCl@4=]r+OrroH]F(r))CB))12#(
remove = from position 7,Cc@+n(]4SN5NCl@4=]r+OrroH]F(r))CB))12#(
remove ] from position 17,Cc@+n(]4SN5NCl@4=r+OrroH]F(r))CB))12#(
add 7 at position 37,Cc@+n(]4SN5NCl@4=r+OrroH]F(r))CB))12#7(
add 2 at position 28,Cc@+n(]4SN5NCl@4=r+OrroH]F(r2))CB))12#7(
replace 5 at position 10 with =,Cc@+n(]4SN=NCl@4=r+OrroH]F(r2))CB))12#7(
remove n from position 4,Cc@+(]4SN=NCl@4=r+OrroH]F(r2))CB))12#7(
remove r from position 19,Cc@+(]4SN=NCl@4=r+OroH]F(r2))CB))12#7(
remove B from position 30,Cc@+(]4SN=NCl@4=r+OroH]F(r2))C))12#7(
add / at position 30,Cc@+(]4SN=NCl@4=r+OroH]F(r2))C/))12#7(
remove F from position 23,Cc@+(]4SN=NCl@4=r+OroH](r2))C/))12#7(
remove ] from position 22,Cc@+(]4SN=NCl@4=r+OroH(r2))C/))12#7(
add r at position 33,Cc@+(]4SN=NCl@4=r+OroH(r2))C/))12r#7(
remove ) from position 30,Cc@+(]4SN=NCl@4=r+OroH(r2))C/)12r#7(
remove ) from position 29,Cc@+(]4SN=NCl@4=r+OroH(r2))C/12r#7(
remove 4 from position 6,Cc@+(]SN=NCl@4=r+OroH(r2))C/12r#7(
add ) at position 30,Cc@+(]SN=NCl@4=r+OroH(r2))C/12)r#7(
replace ( at position 21 with 7,Cc@+(]SN=NCl@4=r+OroH7r2))C/12)r#7(
replace r at position 18 with 4,Cc@+(]SN=NCl@4=r+O4oH7r2))C/12)r#7(
remove 7 from position 21,Cc@+(]SN=NCl@4=r+O4oHr2))C/12)r#7(
remove 4 from position 18,Cc@+(]SN=NCl@4=r+OoHr2))C/12)r#7(
remove ( from position 32,Cc@+(]SN=NCl@4=r+OoHr2))C/12)r#7
replace O at position 17 with ),Cc@+(]SN=NCl@4=r+)oHr2))C/12)r#7
remove 4 from position 13,Cc@+(]SN=NCl@=r+)oHr2))C/12)r#7
remove C from position 10,Cc@+(]SN=Nl@=r+)oHr2))C/12)r#7
replace + at position 14 with 4,Cc@+(]SN=Nl@=r4)oHr2))C/12)r#7
add H at position 16,Cc@+(]SN=Nl@=r4)HoHr2))C/12)r#7
remove + from position 3,Cc@(]SN=Nl@=r4)HoHr2))C/12)r#7
replace N at position 8 with 6,Cc@(]SN=6l@=r4)HoHr2))C/12)r#7
add 7 at position 0,7Cc@(]SN=6l@=r4)HoHr2))C/12)r#7
add 1 at position 2,7C1c@(]SN=6l@=r4)HoHr2))C/12)r#7
remove r from position 20,7C1c@(]SN=6l@=r4)HoH2))C/12)r#7
remove = from position 13,7C1c@(]SN=6l@r4)HoH2))C/12)r#7
replace 7 at position 29 with ),7C1c@(]SN=6l@r4)HoH2))C/12)r#)
replace = at position 9 with 3,7C1c@(]SN36l@r4)HoH2))C/12)r#)
remove ) from position 29,7C1c@(]SN36l@r4)HoH2))C/12)r#
remove l from position 11,7C1c@(]SN36@r4)HoH2))C/12)r#
remove ) from position 20,7C1c@(]SN36@r4)HoH2)C/12)r#
replace 7 at position 0 with S,SC1c@(]SN36@r4)HoH2)C/12)r#
remove 2 from position 18,SC1c@(]SN36@r4)HoH)C/12)r#
add B at position 15,SC1c@(]SN36@r4)BHoH)C/12)r#
remove / from position 21,SC1c@(]SN36@r4)BHoH)C12)r#
replace @ at position 4 with r,SC1cr(]SN36@r4)BHoH)C12)r#
remove ) from position 19,SC1cr(]SN36@r4)BHoHC12)r#
add s at position 17,SC1cr(]SN36@r4)BHsoHC12)r#
replace 2 at position 22 with /,SC1cr(]SN36@r4)BHsoHC1/)r#
replace r at position 24 with C,SC1cr(]SN36@r4)BHsoHC1/)C#
remove r from position 12,SC1cr(]SN36@4)BHsoHC1/)C#
replace 6 at position 10 with -,SC1cr(]SN3-@4)BHsoHC1/)C#
remove o from position 17,SC1cr(]SN3-@4)BHsHC1/)C#
remove 1 from position 19,SC1cr(]SN3-@4)BHsHC/)C#
remove B from position 14,SC1cr(]SN3-@4)HsHC/)C#
add r at position 2,SCr1cr(]SN3-@4)HsHC/)C#
remove C from position 21,SCr1cr(]SN3-@4)HsHC/)#
remove C from position 1,Sr1cr(]SN3-@4)HsHC/)#
remove 1 from position 2,Srcr(]SN3-@4)HsHC/)#
remove # from position 19,Srcr(]SN3-@4)HsHC/)
replace 4 at position 11 with F,Srcr(]SN3-@F)HsHC/)
remove ) from position 12,Srcr(]SN3-@FHsHC/)
remove r from position 1,Scr(]SN3-@FHsHC/)
add l at position 2,Sclr(]SN3-@FHsHC/)
remove @ from position 10,Sclr(]SN3-FHsHC/)
add S at position 7,Sclr(]SSN3-FHsHC/)
replace S at position 0 with o,oclr(]SSN3-FHsHC/)
remove F from position 11,oclr(]SSN3-HsHC/)
remove l from position 2,ocr(]SSN3-HsHC/)
remove S from position 6,ocr(]SN3-HsHC/)
replace / at position 13 with N,ocr(]SN3-HsHCN)
remove H from position 11,ocr(]SN3-HsCN)
add O at position 2,ocOr(]SN3-HsCN)
replace H at position 10 with ],ocOr(]SN3-]sCN)
add 6 at position 14,ocOr(]SN3-]sCN6)
remove s from position 11,ocOr(]SN3-]CN6)
remove S from position 6,ocOr(]N3-]CN6)
replace r at position 3 with 4,ocO4(]N3-]CN6)
add H at position 5,ocO4(H]N3-]CN6)
replace c at position 1 with F,oFO4(H]N3-]CN6)
remove 6 from position 13,oFO4(H]N3-]CN)
add H at position 9,oFO4(H]N3H-]CN)
remove O from position 2,oF4(H]N3H-]CN)
remove ] from position 5,oF4(HN3H-]CN)
replace H at position 4 with [,oF4([N3H-]CN)
remove 4 from position 2,oF([N3H-]CN)
remove ( from position 2,oF[N3H-]CN)
replace C at position 8 with [,oF[N3H-][N)
remove N from position 3,oF[3H-][N)
remove [ from position 2,oF3H-][N)
remove [ from position 6,oF3H-]N)
remove ) from position 7,oF3H-]N
replace - at position 4 with B,oF3HB]N
remove N from position 6,oF3HB]
remove B from position 4,oF3H]
add C at position 5,oF3H]C
replace ] at position 4 with -,oF3H-C
add B at position 3,oF3BH-C
add c at position 2,oFc3BH-C
replace 3 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
