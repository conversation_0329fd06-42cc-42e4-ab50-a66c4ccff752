log,state
initialize: ,
add N at position 0,N
add I at position 0,IN
add 1 at position 0,1IN
replace N at position 2 with B,1IB
remove 1 from position 0,IB
add = at position 0,=IB
replace I at position 1 with -,=-B
add B at position 0,B=-B
add C at position 1,BC=-B
replace <PERSON> at position 1 with 6,B6=-B
replace = at position 2 with n,B6n-B
replace B at position 0 with 5,56n-<PERSON>
replace <PERSON> at position 4 with [,56n-[
replace [ at position 4 with 5,56n-5
remove 6 from position 1,5n-5
replace 5 at position 0 with r,rn-5
add - at position 3,rn--5
remove r from position 0,n--5
add ( at position 3,n--(5
add H at position 4,n--(H5
add ) at position 4,n--()H5
replace - at position 2 with /,n-/()H5
add ) at position 6,n-/()H)5
add ( at position 3,n-/(()H)5
replace - at position 1 with l,nl/(()H)5
add n at position 2,nln/(()H)5
replace / at position 3 with 4,nln4(()H)5
add n at position 5,nln4(n()H)5
add c at position 0,cnln4(n()H)5
remove ) from position 8,cnln4(n(H)5
add / at position 9,cnln4(n(H/)5
replace 4 at position 4 with -,cnln-(n(H/)5
add 6 at position 8,cnln-(n(6H/)5
replace 6 at position 8 with s,cnln-(n(sH/)5
replace / at position 10 with -,cnln-(n(sH-)5
add C at position 0,Ccnln-(n(sH-)5
replace ( at position 6 with 5,Ccnln-5n(sH-)5
remove - from position 5,Ccnln5n(sH-)5
add s at position 13,Ccnln5n(sH-)5s
replace H at position 9 with r,Ccnln5n(sr-)5s
add H at position 1,CHcnln5n(sr-)5s
add 3 at position 10,CHcnln5n(s3r-)5s
remove 5 from position 6,CHcnlnn(s3r-)5s
add H at position 8,CHcnlnn(Hs3r-)5s
add 4 at position 9,CHcnlnn(H4s3r-)5s
add 6 at position 10,CHcnlnn(H46s3r-)5s
replace 4 at position 9 with 5,CHcnlnn(H56s3r-)5s
replace 6 at position 10 with s,CHcnlnn(H5ss3r-)5s
remove ) from position 15,CHcnlnn(H5ss3r-5s
add o at position 3,CHconlnn(H5ss3r-5s
add 7 at position 14,CHconlnn(H5ss37r-5s
add l at position 15,CHconlnn(H5ss37lr-5s
remove r from position 16,CHconlnn(H5ss37l-5s
add + at position 11,CHconlnn(H5+ss37l-5s
add - at position 11,CHconlnn(H5-+ss37l-5s
add 7 at position 5,CHcon7lnn(H5-+ss37l-5s
replace 7 at position 5 with O,CHconOlnn(H5-+ss37l-5s
add S at position 21,CHconOlnn(H5-+ss37l-5Ss
add F at position 9,CHconOlnnF(H5-+ss37l-5Ss
add I at position 2,CHIconOlnnF(H5-+ss37l-5Ss
add s at position 12,CHIconOlnnF(sH5-+ss37l-5Ss
add ] at position 24,CHIconOlnnF(sH5-+ss37l-5]Ss
add ] at position 26,CHIconOlnnF(sH5-+ss37l-5]S]s
add 6 at position 1,C6HIconOlnnF(sH5-+ss37l-5]S]s
remove l from position 22,C6HIconOlnnF(sH5-+ss37-5]S]s
add ( at position 8,C6HIconO(lnnF(sH5-+ss37-5]S]s
add N at position 3,C6HNIconO(lnnF(sH5-+ss37-5]S]s
add ] at position 18,C6HNIconO(lnnF(sH5]-+ss37-5]S]s
add 1 at position 26,C6HNIconO(lnnF(sH5]-+ss37-15]S]s
replace c at position 5 with 1,C6HNI1onO(lnnF(sH5]-+ss37-15]S]s
add r at position 9,C6HNI1onOr(lnnF(sH5]-+ss37-15]S]s
add l at position 19,C6HNI1onOr(lnnF(sH5l]-+ss37-15]S]s
replace H at position 17 with 1,C6HNI1onOr(lnnF(s15l]-+ss37-15]S]s
replace s at position 24 with =,C6HNI1onOr(lnnF(s15l]-+s=37-15]S]s
add c at position 13,C6HNI1onOr(lncnF(s15l]-+s=37-15]S]s
remove 7 from position 27,C6HNI1onOr(lncnF(s15l]-+s=3-15]S]s
remove r from position 9,C6HNI1onO(lncnF(s15l]-+s=3-15]S]s
remove N from position 3,C6HI1onO(lncnF(s15l]-+s=3-15]S]s
add + at position 20,C6HI1onO(lncnF(s15l]+-+s=3-15]S]s
remove l from position 9,C6HI1onO(ncnF(s15l]+-+s=3-15]S]s
remove I from position 3,C6H1onO(ncnF(s15l]+-+s=3-15]S]s
replace 5 at position 15 with =,C6H1onO(ncnF(s1=l]+-+s=3-15]S]s
remove ] from position 29,C6H1onO(ncnF(s1=l]+-+s=3-15]Ss
remove F from position 11,C6H1onO(ncn(s1=l]+-+s=3-15]Ss
add c at position 9,C6H1onO(nccn(s1=l]+-+s=3-15]Ss
add o at position 30,C6H1onO(nccn(s1=l]+-+s=3-15]Sso
remove S from position 28,C6H1onO(nccn(s1=l]+-+s=3-15]so
replace o at position 29 with c,C6H1onO(nccn(s1=l]+-+s=3-15]sc
add c at position 26,C6H1onO(nccn(s1=l]+-+s=3-1c5]sc
add 1 at position 29,C6H1onO(nccn(s1=l]+-+s=3-1c5]1sc
remove 1 from position 25,C6H1onO(nccn(s1=l]+-+s=3-c5]1sc
remove s from position 13,C6H1onO(nccn(1=l]+-+s=3-c5]1sc
remove n from position 8,C6H1onO(ccn(1=l]+-+s=3-c5]1sc
add c at position 14,C6H1onO(ccn(1=cl]+-+s=3-c5]1sc
add c at position 10,C6H1onO(cccn(1=cl]+-+s=3-c5]1sc
remove s from position 29,C6H1onO(cccn(1=cl]+-+s=3-c5]1c
remove l from position 16,C6H1onO(cccn(1=c]+-+s=3-c5]1c
remove c from position 9,C6H1onO(ccn(1=c]+-+s=3-c5]1c
remove 1 from position 12,C6H1onO(ccn(=c]+-+s=3-c5]1c
replace ] at position 14 with 7,C6H1onO(ccn(=c7+-+s=3-c5]1c
replace 5 at position 23 with o,C6H1onO(ccn(=c7+-+s=3-co]1c
replace o at position 4 with c,C6H1cnO(ccn(=c7+-+s=3-co]1c
replace s at position 18 with ],C6H1cnO(ccn(=c7+-+]=3-co]1c
remove c from position 22,C6H1cnO(ccn(=c7+-+]=3-o]1c
add c at position 11,C6H1cnO(ccnc(=c7+-+]=3-o]1c
replace 3 at position 21 with O,C6H1cnO(ccnc(=c7+-+]=O-o]1c
replace 7 at position 15 with c,C6H1cnO(ccnc(=cc+-+]=O-o]1c
replace + at position 16 with [,C6H1cnO(ccnc(=cc[-+]=O-o]1c
replace = at position 13 with o,C6H1cnO(ccnc(occ[-+]=O-o]1c
add C at position 3,C6HC1cnO(ccnc(occ[-+]=O-o]1c
add N at position 14,C6HC1cnO(ccnc(Nocc[-+]=O-o]1c
add ) at position 24,C6HC1cnO(ccnc(Nocc[-+]=O)-o]1c
replace 1 at position 28 with ),C6HC1cnO(ccnc(Nocc[-+]=O)-o])c
replace o at position 15 with (,C6HC1cnO(ccnc(N(cc[-+]=O)-o])c
replace ( at position 15 with c,C6HC1cnO(ccnc(Nccc[-+]=O)-o])c
add 2 at position 18,C6HC1cnO(ccnc(Nccc2[-+]=O)-o])c
add ] at position 22,C6HC1cnO(ccnc(Nccc2[-+]]=O)-o])c
replace H at position 2 with N,C6NC1cnO(ccnc(Nccc2[-+]]=O)-o])c
replace c at position 9 with 4,C6NC1cnO(4cnc(Nccc2[-+]]=O)-o])c
remove 4 from position 9,C6NC1cnO(cnc(Nccc2[-+]]=O)-o])c
add O at position 2,C6ONC1cnO(cnc(Nccc2[-+]]=O)-o])c
add C at position 2,C6CONC1cnO(cnc(Nccc2[-+]]=O)-o])c
remove O from position 9,C6CONC1cn(cnc(Nccc2[-+]]=O)-o])c
remove ] from position 22,C6CONC1cn(cnc(Nccc2[-+]=O)-o])c
add c at position 9,C6CONC1cnc(cnc(Nccc2[-+]=O)-o])c
add H at position 4,C6COHNC1cnc(cnc(Nccc2[-+]=O)-o])c
remove H from position 4,C6CONC1cnc(cnc(Nccc2[-+]=O)-o])c
add ( at position 24,C6CONC1cnc(cnc(Nccc2[-+](=O)-o])c
remove - from position 21,C6CONC1cnc(cnc(Nccc2[+](=O)-o])c
add c at position 32,C6CONC1cnc(cnc(Nccc2[+](=O)-o])cc
add 1 at position 33,C6CONC1cnc(cnc(Nccc2[+](=O)-o])cc1
add 2 at position 12,C6CONC1cnc(c2nc(Nccc2[+](=O)-o])cc1
add N at position 22,C6CONC1cnc(c2nc(Nccc2[N+](=O)-o])cc1
replace N at position 4 with O,C6COOC1cnc(c2nc(Nccc2[N+](=O)-o])cc1
add # at position 17,C6COOC1cnc(c2nc(N#ccc2[N+](=O)-o])cc1
remove C from position 5,C6COO1cnc(c2nc(N#ccc2[N+](=O)-o])cc1
add + at position 29,C6COO1cnc(c2nc(N#ccc2[N+](=O)+-o])cc1
replace O at position 4 with c,C6COc1cnc(c2nc(N#ccc2[N+](=O)+-o])cc1
remove o from position 31,C6COc1cnc(c2nc(N#ccc2[N+](=O)+-])cc1
remove n from position 7,C6COc1cc(c2nc(N#ccc2[N+](=O)+-])cc1
add - at position 9,C6COc1cc(-c2nc(N#ccc2[N+](=O)+-])cc1
add O at position 30,C6COc1cc(-c2nc(N#ccc2[N+](=O)+O-])cc1
remove 6 from position 1,CCOc1cc(-c2nc(N#ccc2[N+](=O)+O-])cc1
replace # at position 15 with ),CCOc1cc(-c2nc(N)ccc2[N+](=O)+O-])cc1
add c at position 6,CCOc1ccc(-c2nc(N)ccc2[N+](=O)+O-])cc1
remove O from position 2,CCc1ccc(-c2nc(N)ccc2[N+](=O)+O-])cc1
replace + at position 28 with [,CCc1ccc(-c2nc(N)ccc2[N+](=O)[O-])cc1
final: CCc1ccc(-c2nc(N)ccc2[N+](=O)[O-])cc1,CCc1ccc(-c2nc(N)ccc2[N+](=O)[O-])cc1
