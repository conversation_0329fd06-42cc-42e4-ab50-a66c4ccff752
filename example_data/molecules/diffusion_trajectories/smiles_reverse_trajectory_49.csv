log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with B,oHcBBH-C
remove c from position 2,oHBB<PERSON>-<PERSON>
remove B from position 3,oHB<PERSON>-<PERSON>
replace - at position 4 with ],oHBH]C
remove C from position 5,oHBH]
add B at position 4,oHBHB]
add N at position 6,oHBHB]N
replace B at position 4 with -,oHBH-]N
add r at position 7,oHBH-]Nr
add [ at position 6,oHBH-][Nr
add [ at position 2,oH[BH-][Nr
add 5 at position 3,oH[5BH-][Nr
replace [ at position 8 with B,oH[5BH-]BNr
add l at position 2,oHl[5BH-]BNr
add 4 at position 2,oH4l[5BH-]BNr
replace [ at position 4 with H,oH4lH5BH-]BNr
add 6 at position 5,oH4lH65BH-]BNr
add O at position 2,oHO4lH65BH-]BNr
remove H from position 9,oHO4lH65B-]BNr
add 6 at position 13,oHO4lH65B-]BN6r
replace H at position 1 with C,oCO4lH65B-]BN6r
remove H from position 5,oCO4l65B-]BN6r
replace 4 at position 3 with r,oCOrl65B-]BN6r
add = at position 6,oCOrl6=5B-]BN6r
add s at position 11,oCOrl6=5B-]sBN6r
remove 6 from position 14,oCOrl6=5B-]sBNr
replace ] at position 10 with C,oCOrl6=5B-CsBNr
remove O from position 2,oCrl6=5B-CsBNr
add 2 at position 11,oCrl6=5B-Cs2BNr
replace N at position 13 with /,oCrl6=5B-Cs2B/r
add S at position 6,oCrl6=S5B-Cs2B/r
add l at position 2,oClrl6=S5B-Cs2B/r
add F at position 11,oClrl6=S5B-FCs2B/r
replace o at position 0 with S,SClrl6=S5B-FCs2B/r
remove S from position 7,SClrl6=5B-FCs2B/r
add - at position 10,SClrl6=5B--FCs2B/r
remove l from position 2,SCrl6=5B--FCs2B/r
add r at position 1,SrCrl6=5B--FCs2B/r
add + at position 12,SrCrl6=5B--F+Cs2B/r
replace F at position 11 with 3,SrCrl6=5B--3+Cs2B/r
add 4 at position 19,SrCrl6=5B--3+Cs2B/r4
add ) at position 2,Sr)Crl6=5B--3+Cs2B/r4
add 6 at position 1,S6r)Crl6=5B--3+Cs2B/r4
add F at position 21,S6r)Crl6=5B--3+Cs2B/rF4
remove r from position 2,S6)Crl6=5B--3+Cs2B/rF4
add ] at position 14,S6)Crl6=5B--3+]Cs2B/rF4
add c at position 19,S6)Crl6=5B--3+]Cs2Bc/rF4
add c at position 17,S6)Crl6=5B--3+]Csc2Bc/rF4
replace - at position 10 with l,S6)Crl6=5Bl-3+]Csc2Bc/rF4
add = at position 12,S6)Crl6=5Bl-=3+]Csc2Bc/rF4
replace F at position 24 with (,S6)Crl6=5Bl-=3+]Csc2Bc/r(4
replace / at position 22 with 1,S6)Crl6=5Bl-=3+]Csc2Bc1r(4
remove s from position 17,S6)Crl6=5Bl-=3+]Cc2Bc1r(4
add - at position 19,S6)Crl6=5Bl-=3+]Cc2-Bc1r(4
replace r at position 4 with ],S6)C]l6=5Bl-=3+]Cc2-Bc1r(4
add ( at position 21,S6)C]l6=5Bl-=3+]Cc2-B(c1r(4
add S at position 5,S6)C]Sl6=5Bl-=3+]Cc2-B(c1r(4
add ) at position 27,S6)C]Sl6=5Bl-=3+]Cc2-B(c1r()4
replace S at position 0 with C,C6)C]Sl6=5Bl-=3+]Cc2-B(c1r()4
add ( at position 20,C6)C]Sl6=5Bl-=3+]Cc2(-B(c1r()4
replace 6 at position 7 with O,C6)C]SlO=5Bl-=3+]Cc2(-B(c1r()4
add C at position 15,C6)C]SlO=5Bl-=3C+]Cc2(-B(c1r()4
replace 4 at position 30 with 7,C6)C]SlO=5Bl-=3C+]Cc2(-B(c1r()7
replace ) at position 29 with 7,C6)C]SlO=5Bl-=3C+]Cc2(-B(c1r(77
add ) at position 27,C6)C]SlO=5Bl-=3C+]Cc2(-B(c1)r(77
replace 5 at position 9 with =,C6)C]SlO==Bl-=3C+]Cc2(-B(c1)r(77
add @ at position 2,C6@)C]SlO==Bl-=3C+]Cc2(-B(c1)r(77
replace ( at position 25 with /,C6@)C]SlO==Bl-=3C+]Cc2(-B/c1)r(77
remove - from position 23,C6@)C]SlO==Bl-=3C+]Cc2(B/c1)r(77
remove l from position 7,C6@)C]SO==Bl-=3C+]Cc2(B/c1)r(77
replace 3 at position 14 with r,C6@)C]SO==Bl-=rC+]Cc2(B/c1)r(77
add ) at position 21,C6@)C]SO==Bl-=rC+]Cc2)(B/c1)r(77
add 4 at position 13,C6@)C]SO==Bl-4=rC+]Cc2)(B/c1)r(77
replace + at position 17 with ),C6@)C]SO==Bl-4=rC)]Cc2)(B/c1)r(77
add F at position 32,C6@)C]SO==Bl-4=rC)]Cc2)(B/c1)r(7F7
add 4 at position 18,C6@)C]SO==Bl-4=rC)4]Cc2)(B/c1)r(7F7
add 6 at position 21,C6@)C]SO==Bl-4=rC)4]C6c2)(B/c1)r(7F7
replace 4 at position 18 with o,C6@)C]SO==Bl-4=rC)o]C6c2)(B/c1)r(7F7
replace 6 at position 21 with r,C6@)C]SO==Bl-4=rC)o]Crc2)(B/c1)r(7F7
remove ) from position 30,C6@)C]SO==Bl-4=rC)o]Crc2)(B/c1r(7F7
add 4 at position 6,C6@)C]4SO==Bl-4=rC)o]Crc2)(B/c1r(7F7
add + at position 29,C6@)C]4SO==Bl-4=rC)o]Crc2)(B/+c1r(7F7
add c at position 30,C6@)C]4SO==Bl-4=rC)o]Crc2)(B/+cc1r(7F7
remove r from position 33,C6@)C]4SO==Bl-4=rC)o]Crc2)(B/+cc1(7F7
add F at position 22,C6@)C]4SO==Bl-4=rC)o]CFrc2)(B/+cc1(7F7
add C at position 23,C6@)C]4SO==Bl-4=rC)o]CFCrc2)(B/+cc1(7F7
remove / from position 30,C6@)C]4SO==Bl-4=rC)o]CFCrc2)(B+cc1(7F7
add F at position 30,C6@)C]4SO==Bl-4=rC)o]CFCrc2)(BF+cc1(7F7
add [ at position 19,C6@)C]4SO==Bl-4=rC)[o]CFCrc2)(BF+cc1(7F7
add n at position 4,C6@)nC]4SO==Bl-4=rC)[o]CFCrc2)(BF+cc1(7F7
replace = at position 10 with 5,C6@)nC]4SO5=Bl-4=rC)[o]CFCrc2)(BF+cc1(7F7
remove 2 from position 28,C6@)nC]4SO5=Bl-4=rC)[o]CFCrc)(BF+cc1(7F7
remove 7 from position 37,C6@)nC]4SO5=Bl-4=rC)[o]CFCrc)(BF+cc1(F7
add N at position 17,C6@)nC]4SO5=Bl-4=NrC)[o]CFCrc)(BF+cc1(F7
add = at position 7,C6@)nC]=4SO5=Bl-4=NrC)[o]CFCrc)(BF+cc1(F7
add c at position 36,C6@)nC]=4SO5=Bl-4=NrC)[o]CFCrc)(BF+ccc1(F7
replace = at position 17 with F,C6@)nC]=4SO5=Bl-4FNrC)[o]CFCrc)(BF+ccc1(F7
replace + at position 34 with ),C6@)nC]=4SO5=Bl-4FNrC)[o]CFCrc)(BF)ccc1(F7
add ) at position 36,C6@)nC]=4SO5=Bl-4FNrC)[o]CFCrc)(BF)c)cc1(F7
add ( at position 19,C6@)nC]=4SO5=Bl-4FN(rC)[o]CFCrc)(BF)c)cc1(F7
replace F at position 17 with n,C6@)nC]=4SO5=Bl-4nN(rC)[o]CFCrc)(BF)c)cc1(F7
remove 7 from position 43,C6@)nC]=4SO5=Bl-4nN(rC)[o]CFCrc)(BF)c)cc1(F
add F at position 20,C6@)nC]=4SO5=Bl-4nN(FrC)[o]CFCrc)(BF)c)cc1(F
replace @ at position 2 with 2,C62)nC]=4SO5=Bl-4nN(FrC)[o]CFCrc)(BF)c)cc1(F
add ) at position 9,C62)nC]=4)SO5=Bl-4nN(FrC)[o]CFCrc)(BF)c)cc1(F
remove ( from position 43,C62)nC]=4)SO5=Bl-4nN(FrC)[o]CFCrc)(BF)c)cc1F
add r at position 10,C62)nC]=4)rSO5=Bl-4nN(FrC)[o]CFCrc)(BF)c)cc1F
remove ) from position 34,C62)nC]=4)rSO5=Bl-4nN(FrC)[o]CFCrc(BF)c)cc1F
remove r from position 10,C62)nC]=4)SO5=Bl-4nN(FrC)[o]CFCrc(BF)c)cc1F
add = at position 8,C62)nC]==4)SO5=Bl-4nN(FrC)[o]CFCrc(BF)c)cc1F
remove B from position 35,C62)nC]==4)SO5=Bl-4nN(FrC)[o]CFCrc(F)c)cc1F
remove F from position 22,C62)nC]==4)SO5=Bl-4nN(rC)[o]CFCrc(F)c)cc1F
replace 5 at position 13 with 3,C62)nC]==4)SO3=Bl-4nN(rC)[o]CFCrc(F)c)cc1F
add - at position 11,C62)nC]==4)-SO3=Bl-4nN(rC)[o]CFCrc(F)c)cc1F
replace r at position 32 with (,C62)nC]==4)-SO3=Bl-4nN(rC)[o]CFC(c(F)c)cc1F
remove r from position 23,C62)nC]==4)-SO3=Bl-4nN(C)[o]CFC(c(F)c)cc1F
replace F at position 29 with /,C62)nC]==4)-SO3=Bl-4nN(C)[o]C/C(c(F)c)cc1F
remove S from position 12,C62)nC]==4)-O3=Bl-4nN(C)[o]C/C(c(F)c)cc1F
add C at position 12,C62)nC]==4)-CO3=Bl-4nN(C)[o]C/C(c(F)c)cc1F
add H at position 4,C62)HnC]==4)-CO3=Bl-4nN(C)[o]C/C(c(F)c)cc1F
replace H at position 4 with ],C62)]nC]==4)-CO3=Bl-4nN(C)[o]C/C(c(F)c)cc1F
replace 4 at position 20 with ],C62)]nC]==4)-CO3=Bl-]nN(C)[o]C/C(c(F)c)cc1F
add S at position 5,C62)]SnC]==4)-CO3=Bl-]nN(C)[o]C/C(c(F)c)cc1F
replace 2 at position 2 with H,C6H)]SnC]==4)-CO3=Bl-]nN(C)[o]C/C(c(F)c)cc1F
add @ at position 3,C6H@)]SnC]==4)-CO3=Bl-]nN(C)[o]C/C(c(F)c)cc1F
replace ) at position 4 with H,C6H@H]SnC]==4)-CO3=Bl-]nN(C)[o]C/C(c(F)c)cc1F
replace l at position 20 with O,C6H@H]SnC]==4)-CO3=BO-]nN(C)[o]C/C(c(F)c)cc1F
add 3 at position 17,C6H@H]SnC]==4)-CO33=BO-]nN(C)[o]C/C(c(F)c)cc1F
add B at position 10,C6H@H]SnC]B==4)-CO33=BO-]nN(C)[o]C/C(c(F)c)cc1F
remove S from position 6,C6H@H]nC]B==4)-CO33=BO-]nN(C)[o]C/C(c(F)c)cc1F
replace O at position 16 with C,C6H@H]nC]B==4)-CC33=BO-]nN(C)[o]C/C(c(F)c)cc1F
replace n at position 24 with 2,C6H@H]nC]B==4)-CC33=BO-]2N(C)[o]C/C(c(F)c)cc1F
replace n at position 6 with (,C6H@H](C]B==4)-CC33=BO-]2N(C)[o]C/C(c(F)c)cc1F
add ) at position 7,C6H@H]()C]B==4)-CC33=BO-]2N(C)[o]C/C(c(F)c)cc1F
remove = from position 11,C6H@H]()C]B=4)-CC33=BO-]2N(C)[o]C/C(c(F)c)cc1F
replace 3 at position 18 with O,C6H@H]()C]B=4)-CC3O=BO-]2N(C)[o]C/C(c(F)c)cc1F
add - at position 33,C6H@H]()C]B=4)-CC3O=BO-]2N(C)[o]C-/C(c(F)c)cc1F
replace B at position 10 with (,C6H@H]()C](=4)-CC3O=BO-]2N(C)[o]C-/C(c(F)c)cc1F
replace o at position 30 with (,C6H@H]()C](=4)-CC3O=BO-]2N(C)[(]C-/C(c(F)c)cc1F
replace ( at position 30 with H,C6H@H]()C](=4)-CC3O=BO-]2N(C)[H]C-/C(c(F)c)cc1F
add c at position 37,C6H@H]()C](=4)-CC3O=BO-]2N(C)[H]C-/C(cc(F)c)cc1F
add ) at position 22,C6H@H]()C](=4)-CC3O=BO)-]2N(C)[H]C-/C(cc(F)c)cc1F
replace H at position 2 with [,C6[@H]()C](=4)-CC3O=BO)-]2N(C)[H]C-/C(cc(F)c)cc1F
remove ] from position 9,C6[@H]()C(=4)-CC3O=BO)-]2N(C)[H]C-/C(cc(F)c)cc1F
replace 4 at position 11 with ),C6[@H]()C(=))-CC3O=BO)-]2N(C)[H]C-/C(cc(F)c)cc1F
remove O from position 17,C6[@H]()C(=))-CC3=BO)-]2N(C)[H]C-/C(cc(F)c)cc1F
add + at position 29,C6[@H]()C(=))-CC3=BO)-]2N(C)[+H]C-/C(cc(F)c)cc1F
remove - from position 33,C6[@H]()C(=))-CC3=BO)-]2N(C)[+H]C/C(cc(F)c)cc1F
remove ] from position 22,C6[@H]()C(=))-CC3=BO)-2N(C)[+H]C/C(cc(F)c)cc1F
remove - from position 13,C6[@H]()C(=))CC3=BO)-2N(C)[+H]C/C(cc(F)c)cc1F
remove / from position 31,C6[@H]()C(=))CC3=BO)-2N(C)[+H]CC(cc(F)c)cc1F
add C at position 3,C6[C@H]()C(=))CC3=BO)-2N(C)[+H]CC(cc(F)c)cc1F
add + at position 42,C6[C@H]()C(=))CC3=BO)-2N(C)[+H]CC(cc(F)c)c+c1F
remove - from position 21,C6[C@H]()C(=))CC3=BO)2N(C)[+H]CC(cc(F)c)c+c1F
add ) at position 32,C6[C@H]()C(=))CC3=BO)2N(C)[+H]CC)(cc(F)c)c+c1F
add c at position 33,C6[C@H]()C(=))CC3=BO)2N(C)[+H]CC)c(cc(F)c)c+c1F
add O at position 12,C6[C@H]()C(=O))CC3=BO)2N(C)[+H]CC)c(cc(F)c)c+c1F
replace 3 at position 17 with (,C6[C@H]()C(=O))CC(=BO)2N(C)[+H]CC)c(cc(F)c)c+c1F
remove B from position 19,C6[C@H]()C(=O))CC(=O)2N(C)[+H]CC)c(cc(F)c)c+c1F
add 1 at position 35,C6[C@H]()C(=O))CC(=O)2N(C)[+H]CC)c(1cc(F)c)c+c1F
add C at position 2,C6C[C@H]()C(=O))CC(=O)2N(C)[+H]CC)c(1cc(F)c)c+c1F
remove ) from position 43,C6C[C@H]()C(=O))CC(=O)2N(C)[+H]CC)c(1cc(F)cc+c1F
add @ at position 29,C6C[C@H]()C(=O))CC(=O)2N(C)[+@H]CC)c(1cc(F)cc+c1F
replace ) at position 15 with N,C6C[C@H]()C(=O)NCC(=O)2N(C)[+@H]CC)c(1cc(F)cc+c1F
replace C at position 32 with (,C6C[C@H]()C(=O)NCC(=O)2N(C)[+@H](C)c(1cc(F)cc+c1F
remove + from position 45,C6C[C@H]()C(=O)NCC(=O)2N(C)[+@H](C)c(1cc(F)ccc1F
add C at position 9,C6C[C@H](C)C(=O)NCC(=O)2N(C)[+@H](C)c(1cc(F)ccc1F
add @ at position 30,C6C[C@H](C)C(=O)NCC(=O)2N(C)[+@@H](C)c(1cc(F)ccc1F
remove 6 from position 1,CC[C@H](C)C(=O)NCC(=O)2N(C)[+@@H](C)c(1cc(F)ccc1F
remove 2 from position 22,CC[C@H](C)C(=O)NCC(=O)N(C)[+@@H](C)c(1cc(F)ccc1F
add @ at position 5,CC[C@@H](C)C(=O)NCC(=O)N(C)[+@@H](C)c(1cc(F)ccc1F
remove ( from position 37,CC[C@@H](C)C(=O)NCC(=O)N(C)[+@@H](C)c1cc(F)ccc1F
replace + at position 28 with C,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc1F
final: CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc1F,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc1F
