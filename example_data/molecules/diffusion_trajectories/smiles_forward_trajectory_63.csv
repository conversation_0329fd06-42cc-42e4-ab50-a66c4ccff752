log,state
initialize: CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O-],CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O-]
replace C at position 28 with +,CCCCOc1ccccc1C[C@@H]([NH3+])+(=O)[O-]
add ( at position 37,CCCCOc1ccccc1C[C@@H]([NH3+])+(=O)[O-](
remove c from position 5,CCCCO1ccccc1C[C@@H]([NH3+])+(=O)[O-](
add 2 at position 22,CCCCO1ccccc1C[C@@H]([N2H3+])+(=O)[O-](
add 6 at position 1,C6CCCO1ccccc1C[C@@H]([N2H3+])+(=O)[O-](
remove ( from position 30,C6CCCO1ccccc1C[C@@H]([N2H3+])+=O)[O-](
remove c from position 9,C6CCCO1cccc1C[C@@H]([N2H3+])+=O)[O-](
add n at position 7,C6CCCO1ncccc1C[C@@H]([N2H3+])+=O)[O-](
add o at position 31,C6CCCO1ncccc1C[C@@H]([N2H3+])+=oO)[O-](
replace C at position 4 with S,C6CCSO1ncccc1C[C@@H]([N2H3+])+=oO)[O-](
remove + from position 29,C6CCSO1ncccc1C[C@@H]([N2H3+])=oO)[O-](
add C at position 5,C6CCSCO1ncccc1C[C@@H]([N2H3+])=oO)[O-](
remove @ from position 17,C6CCSCO1ncccc1C[C@H]([N2H3+])=oO)[O-](
replace S at position 4 with N,C6CCNCO1ncccc1C[C@H]([N2H3+])=oO)[O-](
remove N from position 22,C6CCNCO1ncccc1C[C@H]([2H3+])=oO)[O-](
remove c from position 12,C6CCNCO1nccc1C[C@H]([2H3+])=oO)[O-](
remove - from position 33,C6CCNCO1nccc1C[C@H]([2H3+])=oO)[O](
remove O from position 32,C6CCNCO1nccc1C[C@H]([2H3+])=oO)[](
add - at position 21,C6CCNCO1nccc1C[C@H]([-2H3+])=oO)[](
remove 3 from position 24,C6CCNCO1nccc1C[C@H]([-2H+])=oO)[](
add H at position 4,C6CCHNCO1nccc1C[C@H]([-2H+])=oO)[](
remove H from position 4,C6CCNCO1nccc1C[C@H]([-2H+])=oO)[](
remove c from position 9,C6CCNCO1ncc1C[C@H]([-2H+])=oO)[](
add ] at position 22,C6CCNCO1ncc1C[C@H]([-2]H+])=oO)[](
add - at position 33,C6CCNCO1ncc1C[C@H]([-2]H+])=oO)[]-(
remove O from position 29,C6CCNCO1ncc1C[C@H]([-2]H+])=o)[]-(
add O at position 17,C6CCNCO1ncc1C[C@HO]([-2]H+])=o)[]-(
replace 1 at position 11 with 4,C6CCNCO1ncc4C[C@HO]([-2]H+])=o)[]-(
add ] at position 9,C6CCNCO1n]cc4C[C@HO]([-2]H+])=o)[]-(
replace C at position 2 with I,C6ICNCO1n]cc4C[C@HO]([-2]H+])=o)[]-(
remove - from position 22,C6ICNCO1n]cc4C[C@HO]([2]H+])=o)[]-(
remove o from position 29,C6ICNCO1n]cc4C[C@HO]([2]H+])=)[]-(
remove [ from position 30,C6ICNCO1n]cc4C[C@HO]([2]H+])=)]-(
replace c at position 10 with @,C6ICNCO1n]@c4C[C@HO]([2]H+])=)]-(
remove = from position 28,C6ICNCO1n]@c4C[C@HO]([2]H+]))]-(
remove 1 from position 7,C6ICNCOn]@c4C[C@HO]([2]H+]))]-(
replace [ at position 13 with =,C6ICNCOn]@c4C=C@HO]([2]H+]))]-(
replace ( at position 30 with F,C6ICNCOn]@c4C=C@HO]([2]H+]))]-F
add 7 at position 31,C6ICNCOn]@c4C=C@HO]([2]H+]))]-F7
replace H at position 16 with O,C6ICNCOn]@c4C=C@OO]([2]H+]))]-F7
add S at position 6,C6ICNCSOn]@c4C=C@OO]([2]H+]))]-F7
remove @ from position 10,C6ICNCSOn]c4C=C@OO]([2]H+]))]-F7
remove O from position 17,C6ICNCSOn]c4C=C@O]([2]H+]))]-F7
replace ] at position 27 with n,C6ICNCSOn]c4C=C@O]([2]H+]))n-F7
replace c at position 10 with l,C6ICNCSOn]l4C=C@O]([2]H+]))n-F7
replace + at position 23 with ),C6ICNCSOn]l4C=C@O]([2]H)]))n-F7
add ( at position 13,C6ICNCSOn]l4C(=C@O]([2]H)]))n-F7
replace I at position 2 with 2,C62CNCSOn]l4C(=C@O]([2]H)]))n-F7
remove C from position 5,C62CNSOn]l4C(=C@O]([2]H)]))n-F7
replace 7 at position 30 with [,C62CNSOn]l4C(=C@O]([2]H)]))n-F[
replace l at position 9 with @,C62CNSOn]@4C(=C@O]([2]H)]))n-F[
add n at position 16,C62CNSOn]@4C(=C@nO]([2]H)]))n-F[
add s at position 27,C62CNSOn]@4C(=C@nO]([2]H)])s)n-F[
add 1 at position 25,C62CNSOn]@4C(=C@nO]([2]H)1])s)n-F[
remove ) from position 29,C62CNSOn]@4C(=C@nO]([2]H)1])sn-F[
remove H from position 23,C62CNSOn]@4C(=C@nO]([2])1])sn-F[
replace = at position 13 with F,C62CNSOn]@4C(FC@nO]([2])1])sn-F[
replace ) at position 26 with 1,C62CNSOn]@4C(FC@nO]([2])1]1sn-F[
replace F at position 13 with 4,C62CNSOn]@4C(4C@nO]([2])1]1sn-F[
add F at position 22,C62CNSOn]@4C(4C@nO]([2F])1]1sn-F[
add 5 at position 30,C62CNSOn]@4C(4C@nO]([2F])1]1sn5-F[
add I at position 3,C62ICNSOn]@4C(4C@nO]([2F])1]1sn5-F[
add l at position 9,C62ICNSOnl]@4C(4C@nO]([2F])1]1sn5-F[
remove ] from position 20,C62ICNSOnl]@4C(4C@nO([2F])1]1sn5-F[
add N at position 3,C62NICNSOnl]@4C(4C@nO([2F])1]1sn5-F[
add r at position 9,C62NICNSOrnl]@4C(4C@nO([2F])1]1sn5-F[
add 7 at position 27,C62NICNSOrnl]@4C(4C@nO([2F]7)1]1sn5-F[
remove @ from position 13,C62NICNSOrnl]4C(4C@nO([2F]7)1]1sn5-F[
replace F at position 24 with s,C62NICNSOrnl]4C(4C@nO([2s]7)1]1sn5-F[
replace C at position 17 with H,C62NICNSOrnl]4C(4H@nO([2s]7)1]1sn5-F[
remove n from position 19,C62NICNSOrnl]4C(4H@O([2s]7)1]1sn5-F[
remove r from position 9,C62NICNSOnl]4C(4H@O([2s]7)1]1sn5-F[
replace C at position 5 with c,C62NIcNSOnl]4C(4H@O([2s]7)1]1sn5-F[
remove 1 from position 26,C62NIcNSOnl]4C(4H@O([2s]7)]1sn5-F[
remove @ from position 17,C62NIcNSOnl]4C(4HO([2s]7)]1sn5-F[
remove ) from position 24,C62NIcNSOnl]4C(4HO([2s]7]1sn5-F[
remove [ from position 19,C62NIcNSOnl]4C(4HO(2s]7]1sn5-F[
remove ( from position 18,C62NIcNSOnl]4C(4HO2s]7]1sn5-F[
remove ] from position 11,C62NIcNSOnl4C(4HO2s]7]1sn5-F[
replace 4 at position 14 with 2,C62NIcNSOnl4C(2HO2s]7]1sn5-F[
replace c at position 5 with 7,C62NI7NSOnl4C(2HO2s]7]1sn5-F[
remove 2 from position 2,C6NI7NSOnl4C(2HO2s]7]1sn5-F[
remove l from position 9,C6NI7NSOn4C(2HO2s]7]1sn5-F[
remove s from position 21,C6NI7NSOn4C(2HO2s]7]1n5-F[
replace N at position 5 with 7,C6NI77SOn4C(2HO2s]7]1n5-F[
remove 7 from position 5,C6NI7SOn4C(2HO2s]7]1n5-F[
remove 2 from position 11,C6NI7SOn4C(HO2s]7]1n5-F[
remove H from position 11,C6NI7SOn4C(O2s]7]1n5-F[
add r at position 16,C6NI7SOn4C(O2s]7r]1n5-F[
remove - from position 21,C6NI7SOn4C(O2s]7r]1n5F[
remove C from position 0,6NI7SOn4C(O2s]7r]1n5F[
replace 7 at position 14 with S,6NI7SOn4C(O2s]Sr]1n5F[
replace 6 at position 0 with 7,7NI7SOn4C(O2s]Sr]1n5F[
replace r at position 15 with ),7NI7SOn4C(O2s]S)]1n5F[
replace n at position 18 with 5,7NI7SOn4C(O2s]S)]155F[
replace ( at position 9 with 5,7NI7SOn4C5O2s]S)]155F[
remove 5 from position 18,7NI7SOn4C5O2s]S)]15F[
remove 5 from position 9,7NI7SOn4CO2s]S)]15F[
remove 1 from position 16,7NI7SOn4CO2s]S)]5F[
replace C at position 8 with ),7NI7SOn4)O2s]S)]5F[
remove n from position 6,7NI7SO4)O2s]S)]5F[
remove s from position 10,7NI7SO4)O2]S)]5F[
replace 5 at position 14 with 3,7NI7SO4)O2]S)]3F[
add H at position 16,7NI7SO4)O2]S)]3FH[
remove 7 from position 3,7NISO4)O2]S)]3FH[
replace 2 at position 8 with 7,7NISO4)O7]S)]3FH[
add 7 at position 0,77NISO4)O7]S)]3FH[
add 1 at position 1,717NISO4)O7]S)]3FH[
remove 7 from position 10,717NISO4)O]S)]3FH[
remove ] from position 13,717NISO4)O]S)3FH[
replace 7 at position 2 with o,71oNISO4)O]S)3FH[
replace O at position 9 with 3,71oNISO4)3]S)3FH[
remove 4 from position 7,71oNISO)3]S)3FH[
remove o from position 2,71NISO)3]S)3FH[
remove F from position 12,71NISO)3]S)3H[
add 5 at position 9,71NISO)3]5S)3H[
replace I at position 3 with /,71N/SO)3]5S)3H[
remove S from position 10,71N/SO)3]5)3H[
replace N at position 2 with r,71r/SO)3]5)3H[
remove 5 from position 9,71r/SO)3])3H[
add s at position 8,71r/SO)3s])3H[
replace 3 at position 11 with /,71r/SO)3s])/H[
replace H at position 12 with C,71r/SO)3s])/C[
remove ) from position 6,71r/SO3s])/C[
replace O at position 5 with -,71r/S-3s])/C[
remove ] from position 8,71r/S-3s)/C[
remove / from position 9,71r/S-3s)C[
remove s from position 7,71r/S-3)C[
add r at position 1,7r1r/S-3)C[
remove [ from position 10,7r1r/S-3)C
remove 7 from position 0,r1r/S-3)C
remove 1 from position 1,rr/S-3)C
remove S from position 3,rr/-3)C
remove 3 from position 4,rr/-)C
remove - from position 3,rr/)C
remove r from position 0,r/)C
add l at position 0,lr/)C
remove / from position 2,lr)C
add S at position 1,lSr)C
replace l at position 0 with o,oSr)C
remove C from position 4,oSr)
remove S from position 1,or)
add 1 at position 0,1or)
replace o at position 1 with C,1Cr)
replace r at position 2 with N,1CN)
remove ) from position 3,1CN
remove N from position 2,1C
remove C from position 1,1
remove 1 from position 0,
final: ,
