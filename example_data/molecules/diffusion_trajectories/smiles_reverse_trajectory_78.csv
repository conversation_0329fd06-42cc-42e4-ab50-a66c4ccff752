log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add 1 at position 0,1/#c+5-
remove 5 from position 5,1/#c+-
replace # at position 2 with S,1/Sc+-
remove S from position 2,1/c+-
add F at position 2,1/Fc+-
remove / from position 1,1Fc+-
add 5 at position 3,1Fc5+-
replace + at position 4 with H,1Fc5H-
replace 5 at position 3 with B,1FcBH-
add s at position 5,1FcBHs-
add S at position 3,1FcSBHs-
add C at position 8,1FcSBHs-C
remove s from position 6,1FcSBH-C
replace <PERSON> at position 3 with ],1Fc]BH-C
remove c from position 2,1F]BH-<PERSON>
remove B from position 3,1F]H-<PERSON>
replace - at position 4 with /,1F]H/<PERSON>
remove C from position 5,1F]H/
add @ at position 4,1F]H@/
add H at position 6,1F]H@/H
replace @ at position 4 with C,1F]HC/H
add N at position 7,1F]HC/HN
add [ at position 6,1F]HC/[HN
add [ at position 2,1F[]HC/[HN
add H at position 3,1F[H]HC/[HN
replace [ at position 8 with 6,1F[H]HC/6HN
add C at position 2,1FC[H]HC/6HN
add 4 at position 2,1F4C[H]HC/6HN
replace [ at position 4 with H,1F4CHH]HC/6HN
add - at position 5,1F4CH-H]HC/6HN
add r at position 2,1Fr4CH-H]HC/6HN
remove H from position 9,1Fr4CH-H]C/6HN
add 6 at position 13,1Fr4CH-H]C/6H6N
replace F at position 1 with l,1lr4CH-H]C/6H6N
remove H from position 5,1lr4C-H]C/6H6N
replace 4 at position 3 with 7,1lr7C-H]C/6H6N
add F at position 6,1lr7C-FH]C/6H6N
add 3 at position 11,1lr7C-FH]C/36H6N
replace 6 at position 14 with c,1lr7C-FH]C/36HcN
add o at position 1,1olr7C-FH]C/36HcN
add 2 at position 8,1olr7C-F2H]C/36HcN
add O at position 4,1olrO7C-F2H]C/36HcN
add 1 at position 4,1olr1O7C-F2H]C/36HcN
replace N at position 19 with 2,1olr1O7C-F2H]C/36Hc2
remove 1 from position 0,olr1O7C-F2H]C/36Hc2
add / at position 5,olr1O/7C-F2H]C/36Hc2
replace H at position 11 with s,olr1O/7C-F2s]C/36Hc2
add 6 at position 0,6olr1O/7C-F2s]C/36Hc2
remove 2 from position 20,6olr1O/7C-F2s]C/36Hc
add 1 at position 10,6olr1O/7C-1F2s]C/36Hc
remove l from position 2,6or1O/7C-1F2s]C/36Hc
add r at position 1,6ror1O/7C-1F2s]C/36Hc
add 6 at position 12,6ror1O/7C-1F62s]C/36Hc
replace F at position 11 with ),6ror1O/7C-1)62s]C/36Hc
add 1 at position 19,6ror1O/7C-1)62s]C/316Hc
add ( at position 2,6r(or1O/7C-1)62s]C/316Hc
add 1 at position 1,61r(or1O/7C-1)62s]C/316Hc
add C at position 21,61r(or1O/7C-1)62s]C/3C16Hc
remove r from position 2,61(or1O/7C-1)62s]C/3C16Hc
add 3 at position 14,61(or1O/7C-1)632s]C/3C16Hc
add 5 at position 19,61(or1O/7C-1)632s]C5/3C16Hc
add - at position 17,61(or1O/7C-1)632s-]C5/3C16Hc
replace - at position 10 with O,61(or1O/7CO1)632s-]C5/3C16Hc
add = at position 12,61(or1O/7CO1=)632s-]C5/3C16Hc
replace C at position 24 with ],61(or1O/7CO1=)632s-]C5/3]16Hc
replace / at position 22 with r,61(or1O/7CO1=)632s-]C5r3]16Hc
remove s from position 17,61(or1O/7CO1=)632-]C5r3]16Hc
add [ at position 19,61(or1O/7CO1=)632-][C5r3]16Hc
replace r at position 4 with c,61(oc1O/7CO1=)632-][C5r3]16Hc
add ) at position 21,61(oc1O/7CO1=)632-][C)5r3]16Hc
replace / at position 7 with 7,61(oc1O77CO1=)632-][C)5r3]16Hc
add ( at position 10,61(oc1O77C(O1=)632-][C)5r3]16Hc
add 7 at position 1,671(oc1O77C(O1=)632-][C)5r3]16Hc
add O at position 5,671(oOc1O77C(O1=)632-][C)5r3]16Hc
add s at position 14,671(oOc1O77C(Os1=)632-][C)5r3]16Hc
replace 3 at position 19 with ],671(oOc1O77C(Os1=)6]2-][C)5r3]16Hc
replace o at position 4 with I,671(IOc1O77C(Os1=)6]2-][C)5r3]16Hc
add ) at position 27,671(IOc1O77C(Os1=)6]2-][C)5)r3]16Hc
add 7 at position 20,671(IOc1O77C(Os1=)6]72-][C)5)r3]16Hc
remove 1 from position 2,67(IOc1O77C(Os1=)6]72-][C)5)r3]16Hc
remove 7 from position 1,6(IOc1O77C(Os1=)6]72-][C)5)r3]16Hc
replace 6 at position 16 with @,6(IOc1O77C(Os1=)@]72-][C)5)r3]16Hc
add - at position 7,6(IOc1O-77C(Os1=)@]72-][C)5)r3]16Hc
remove H from position 33,6(IOc1O-77C(Os1=)@]72-][C)5)r3]16c
replace 3 at position 29 with 7,6(IOc1O-77C(Os1=)@]72-][C)5)r7]16c
add H at position 21,6(IOc1O-77C(Os1=)@]72H-][C)5)r7]16c
add N at position 13,6(IOc1O-77C(ONs1=)@]72H-][C)5)r7]16c
replace ) at position 17 with s,6(IOc1O-77C(ONs1=s@]72H-][C)5)r7]16c
add 3 at position 32,6(IOc1O-77C(ONs1=s@]72H-][C)5)r73]16c
add 5 at position 18,6(IOc1O-77C(ONs1=s5@]72H-][C)5)r73]16c
add 5 at position 37,6(IOc1O-77C(ONs1=s5@]72H-][C)5)r73]165c
replace 5 at position 18 with 1,6(IOc1O-77C(ONs1=s1@]72H-][C)5)r73]165c
replace 5 at position 37 with c,6(IOc1O-77C(ONs1=s1@]72H-][C)5)r73]16cc
remove ) from position 30,6(IOc1O-77C(ONs1=s1@]72H-][C)5r73]16cc
add C at position 6,6(IOc1CO-77C(ONs1=s1@]72H-][C)5r73]16cc
add H at position 29,6(IOc1CO-77C(ONs1=s1@]72H-][CH)5r73]16cc
add 2 at position 30,6(IOc1CO-77C(ONs1=s1@]72H-][CH2)5r73]16cc
remove r from position 33,6(IOc1CO-77C(ONs1=s1@]72H-][CH2)573]16cc
add r at position 22,6(IOc1CO-77C(ONs1=s1@]r72H-][CH2)573]16cc
add l at position 23,6(IOc1CO-77C(ONs1=s1@]rl72H-][CH2)573]16cc
add ( at position 10,6(IOc1CO-7(7C(ONs1=s1@]rl72H-][CH2)573]16cc
replace 7 at position 11 with n,6(IOc1CO-7(nC(ONs1=s1@]rl72H-][CH2)573]16cc
add o at position 43,6(IOc1CO-7(nC(ONs1=s1@]rl72H-][CH2)573]16cco
add c at position 19,6(IOc1CO-7(nC(ONs1=cs1@]rl72H-][CH2)573]16cco
add O at position 4,6(IOOc1CO-7(nC(ONs1=cs1@]rl72H-][CH2)573]16cco
replace 7 at position 10 with ],6(IOOc1CO-](nC(ONs1=cs1@]rl72H-][CH2)573]16cco
remove 2 from position 28,6(IOOc1CO-](nC(ONs1=cs1@]rl7H-][CH2)573]16cco
remove 7 from position 37,6(IOOc1CO-](nC(ONs1=cs1@]rl7H-][CH2)53]16cco
add F at position 17,6(IOOc1CO-](nC(ONFs1=cs1@]rl7H-][CH2)53]16cco
add l at position 7,6(IOOc1lCO-](nC(ONFs1=cs1@]rl7H-][CH2)53]16cco
add s at position 36,6(IOOc1lCO-](nC(ONFs1=cs1@]rl7H-][CHs2)53]16cco
add 3 at position 26,6(IOOc1lCO-](nC(ONFs1=cs1@3]rl7H-][CHs2)53]16cco
replace c at position 5 with ),6(IOO)1lCO-](nC(ONFs1=cs1@3]rl7H-][CHs2)53]16cco
add o at position 9,6(IOO)1lCoO-](nC(ONFs1=cs1@3]rl7H-][CHs2)53]16cco
add H at position 43,6(IOO)1lCoO-](nC(ONFs1=cs1@3]rl7H-][CHs2)53H]16cco
add ] at position 19,6(IOO)1lCoO-](nC(ON]Fs1=cs1@3]rl7H-][CHs2)53H]16cco
add c at position 17,6(IOO)1lCoO-](nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]16cco
add n at position 52,6(IOO)1lCoO-](nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]16ccon
add s at position 47,6(IOO)1lCoO-](nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]s16ccon
replace ( at position 13 with 5,6(IOO)1lCoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]s16ccon
add c at position 48,6(IOO)1lCoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]sc16ccon
add = at position 3,6(I=OO)1lCoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]sc16ccon
add C at position 0,C6(I=OO)1lCoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)53H]sc16ccon
add [ at position 45,C6(I=OO)1lCoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)[53H]sc16ccon
remove l from position 9,C6(I=OO)1CoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)[53H]sc16ccon
remove I from position 3,C6(=OO)1CoO-]5nC(cON]Fs1=cs1@3]rl7H-][CHs2)[53H]sc16ccon
add ( at position 21,C6(=OO)1CoO-]5nC(cON](Fs1=cs1@3]rl7H-][CHs2)[53H]sc16ccon
remove s from position 49,C6(=OO)1CoO-]5nC(cON](Fs1=cs1@3]rl7H-][CHs2)[53H]c16ccon
remove F from position 22,C6(=OO)1CoO-]5nC(cON](s1=cs1@3]rl7H-][CHs2)[53H]c16ccon
replace 5 at position 13 with ),C6(=OO)1CoO-])nC(cON](s1=cs1@3]rl7H-][CHs2)[53H]c16ccon
add n at position 11,C6(=OO)1CoOn-])nC(cON](s1=cs1@3]rl7H-][CHs2)[53H]c16ccon
replace r at position 32 with [,C6(=OO)1CoOn-])nC(cON](s1=cs1@3][l7H-][CHs2)[53H]c16ccon
replace s at position 23 with 2,C6(=OO)1CoOn-])nC(cON](21=cs1@3][l7H-][CHs2)[53H]c16ccon
add O at position 15,C6(=OO)1CoOn-])OnC(cON](21=cs1@3][l7H-][CHs2)[53H]c16ccon
add c at position 56,C6(=OO)1CoOn-])OnC(cON](21=cs1@3][l7H-][CHs2)[53H]c16ccocn
remove 1 from position 25,C6(=OO)1CoOn-])OnC(cON](2=cs1@3][l7H-][CHs2)[53H]c16ccocn
remove s from position 27,C6(=OO)1CoOn-])OnC(cON](2=c1@3][l7H-][CHs2)[53H]c16ccocn
remove n from position 16,C6(=OO)1CoOn-])OC(cON](2=c1@3][l7H-][CHs2)[53H]c16ccocn
add - at position 29,C6(=OO)1CoOn-])OC(cON](2=c1@3-][l7H-][CHs2)[53H]c16ccocn
remove 5 from position 44,C6(=OO)1CoOn-])OC(cON](2=c1@3-][l7H-][CHs2)[3H]c16ccocn
remove l from position 32,C6(=OO)1CoOn-])OC(cON](2=c1@3-][7H-][CHs2)[3H]c16ccocn
remove c from position 18,C6(=OO)1CoOn-])OC(ON](2=c1@3-][7H-][CHs2)[3H]c16ccocn
remove 1 from position 25,C6(=OO)1CoOn-])OC(ON](2=c@3-][7H-][CHs2)[3H]c16ccocn
replace ] at position 28 with =,C6(=OO)1CoOn-])OC(ON](2=c@3-=[7H-][CHs2)[3H]c16ccocn
replace 6 at position 46 with +,C6(=OO)1CoOn-])OC(ON](2=c@3-=[7H-][CHs2)[3H]c1+ccocn
replace o at position 9 with 4,C6(=OO)1C4On-])OC(ON](2=c@3-=[7H-][CHs2)[3H]c1+ccocn
replace s at position 37 with ],C6(=OO)1C4On-])OC(ON](2=c@3-=[7H-][CH]2)[3H]c1+ccocn
remove c from position 44,C6(=OO)1C4On-])OC(ON](2=c@3-=[7H-][CH]2)[3H]1+ccocn
add C at position 22,C6(=OO)1C4On-])OC(ON](C2=c@3-=[7H-][CH]2)[3H]1+ccocn
replace 3 at position 42 with C,C6(=OO)1C4On-])OC(ON](C2=c@3-=[7H-][CH]2)[CH]1+ccocn
replace 7 at position 31 with N,C6(=OO)1C4On-])OC(ON](C2=c@3-=[NH-][CH]2)[CH]1+ccocn
replace - at position 33 with +,C6(=OO)1C4On-])OC(ON](C2=c@3-=[NH+][CH]2)[CH]1+ccocn
replace @ at position 26 with (,C6(=OO)1C4On-])OC(ON](C2=c(3-=[NH+][CH]2)[CH]1+ccocn
add N at position 7,C6(=OO)N1C4On-])OC(ON](C2=c(3-=[NH+][CH]2)[CH]1+ccocn
add c at position 28,C6(=OO)N1C4On-])OC(ON](C2=c(c3-=[NH+][CH]2)[CH]1+ccocn
add - at position 49,C6(=OO)N1C4On-])OC(ON](C2=c(c3-=[NH+][CH]2)[CH]1+-ccocn
add C at position 10,C6(=OO)N1CC4On-])OC(ON](C2=c(c3-=[NH+][CH]2)[CH]1+-ccocn
replace n at position 55 with F,C6(=OO)N1CC4On-])OC(ON](C2=c(c3-=[NH+][CH]2)[CH]1+-ccocF
add 1 at position 50,C6(=OO)N1CC4On-])OC(ON](C2=c(c3-=[NH+][CH]2)[CH]1+1-ccocF
add c at position 29,C6(=OO)N1CC4On-])OC(ON](C2=c(cc3-=[NH+][CH]2)[CH]1+1-ccocF
add ) at position 21,C6(=OO)N1CC4On-])OC(O)N](C2=c(cc3-=[NH+][CH]2)[CH]1+1-ccocF
add @ at position 48,C6(=OO)N1CC4On-])OC(O)N](C2=c(cc3-=[NH+][CH]2)[C@H]1+1-ccocF
replace ( at position 29 with 3,C6(=OO)N1CC4On-])OC(O)N](C2=c3cc3-=[NH+][CH]2)[C@H]1+1-ccocF
remove - from position 54,C6(=OO)N1CC4On-])OC(O)N](C2=c3cc3-=[NH+][CH]2)[C@H]1+1ccocF
replace 4 at position 11 with (,C6(=OO)N1CC(On-])OC(O)N](C2=c3cc3-=[NH+][CH]2)[C@H]1+1ccocF
remove O from position 17,C6(=OO)N1CC(On-])C(O)N](C2=c3cc3-=[NH+][CH]2)[C@H]1+1ccocF
add # at position 29,C6(=OO)N1CC(On-])C(O)N](C2=c3#cc3-=[NH+][CH]2)[C@H]1+1ccocF
remove - from position 33,C6(=OO)N1CC(On-])C(O)N](C2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
remove ] from position 22,C6(=OO)N1CC(On-])C(O)N(C2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
add = at position 9,C6(=OO)N1=CC(On-])C(O)N(C2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
add H at position 4,C6(=HOO)N1=CC(On-])C(O)N(C2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
remove H from position 4,C6(=OO)N1=CC(On-])C(O)N(C2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
add C at position 24,C6(=OO)N1=CC(On-])C(O)N(CC2=c3#cc3=[NH+][CH]2)[C@H]1+1ccocF
add C at position 45,C6(=OO)N1=CC(On-])C(O)N(CC2=c3#cc3=[NH+][CH]2C)[C@H]1+1ccocF
add C at position 0,CC6(=OO)N1=CC(On-])C(O)N(CC2=c3#cc3=[NH+][CH]2C)[C@H]1+1ccocF
add 1 at position 60,CC6(=OO)N1=CC(On-])C(O)N(CC2=c3#cc3=[NH+][CH]2C)[C@H]1+1ccoc1F
add c at position 33,CC6(=OO)N1=CC(On-])C(O)N(CC2=c3#ccc3=[NH+][CH]2C)[C@H]1+1ccoc1F
add C at position 25,CC6(=OO)N1=CC(On-])C(O)N(CCC2=c3#ccc3=[NH+][CH]2C)[C@H]1+1ccoc1F
add @ at position 45,CC6(=OO)N1=CC(On-])C(O)N(CCC2=c3#ccc3=[NH+][C@H]2C)[C@H]1+1ccoc1F
replace N at position 8 with S,CC6(=OO)S1=CC(On-])C(O)N(CCC2=c3#ccc3=[NH+][C@H]2C)[C@H]1+1ccoc1F
add c at position 34,CC6(=OO)S1=CC(On-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1ccoc1F
remove C from position 11,CC6(=OO)S1=C(On-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1ccoc1F
add c at position 59,CC6(=OO)S1=C(On-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1cccoc1F
replace S at position 8 with C,CC6(=OO)C1=C(On-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1cccoc1F
remove o from position 62,CC6(=OO)C1=C(On-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1cccc1F
remove n from position 14,CC6(=OO)C1=C(O-])C(O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1cccc1F
add = at position 19,CC6(=OO)C1=C(O-])C(=O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1cccc1F
add c at position 61,CC6(=OO)C1=C(O-])C(=O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1ccccc1F
remove 6 from position 2,CC(=OO)C1=C(O-])C(=O)N(CCC2=c3#cccc3=[NH+][C@H]2C)[C@H]1+1ccccc1F
replace # at position 30 with c,CC(=OO)C1=C(O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1+1ccccc1F
add [ at position 12,CC(=OO)C1=C([O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1+1ccccc1F
remove O from position 4,CC(=O)C1=C([O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1+1ccccc1F
replace + at position 56 with c,CC(=O)C1=C([O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1c1ccccc1F
final: CC(=O)C1=C([O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1c1ccccc1F,CC(=O)C1=C([O-])C(=O)N(CCC2=c3ccccc3=[NH+][C@H]2C)[C@H]1c1ccccc1F
