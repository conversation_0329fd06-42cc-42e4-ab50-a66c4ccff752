log,state
initialize: ,
add 1 at position 0,1
add C at position 1,1C
add N at position 2,1<PERSON><PERSON>
add ) at position 3,1CN)
replace N at position 2 with r,1Cr)
replace <PERSON> at position 1 with o,1or)
remove 1 from position 0,or)
add S at position 1,oSr)
add C at position 4,oSr)C
replace o at position 0 with l,lSr)C
remove S from position 1,lr)C
add / at position 2,lr/)C
remove l from position 0,r/)C
add r at position 0,rr/)C
add - at position 3,rr/-)C
add 4 at position 4,rr/-4)C
add n at position 3,rr/n-4)C
add 1 at position 1,r1r/n-4)C
add 7 at position 0,7r1r/n-4)C
add r at position 10,7r1r/n-4)Cr
remove r from position 1,71r/n-4)Cr
add s at position 7,71r/n-4s)Cr
add / at position 9,71r/n-4s)/Cr
add r at position 8,71r/n-4sr)/Cr
replace - at position 5 with ],71r/n]4sr)/Cr
add ) at position 6,71r/n])4sr)/Cr
replace C at position 12 with H,71r/n])4sr)/Hr
replace / at position 11 with 3,71r/n])4sr)3Hr
remove s from position 8,71r/n])4r)3Hr
add 5 at position 9,71r/n])4r5)3Hr
replace r at position 2 with N,71N/n])4r5)3Hr
add S at position 10,71N/n])4r5S)3Hr
replace / at position 3 with I,71NIn])4r5S)3Hr
remove 5 from position 9,71NIn])4rS)3Hr
add 2 at position 12,71NIn])4rS)32Hr
add o at position 2,71oNIn])4rS)32Hr
add = at position 7,71oNIn]=)4rS)32Hr
replace 4 at position 9 with 2,71oNIn]=)2rS)32Hr
replace o at position 2 with 7,717NIn]=)2rS)32Hr
add o at position 13,717NIn]=)2rS)o32Hr
add 6 at position 10,717NIn]=)26rS)o32Hr
remove 1 from position 1,77NIn]=)26rS)o32Hr
remove 7 from position 0,7NIn]=)26rS)o32Hr
replace 6 at position 8 with C,7NIn]=)2CrS)o32Hr
add 7 at position 3,7NI7n]=)2CrS)o32Hr
remove H from position 16,7NI7n]=)2CrS)o32r
replace 3 at position 14 with 5,7NI7n]=)2CrS)o52r
add s at position 10,7NI7n]=)2CsrS)o52r
add = at position 6,7NI7n]==)2CsrS)o52r
replace ) at position 8 with S,7NI7n]==S2CsrS)o52r
add o at position 16,7NI7n]==S2CsrS)oo52r
add 4 at position 9,7NI7n]==S42CsrS)oo52r
add 5 at position 18,7NI7n]==S42CsrS)oo552r
replace 4 at position 9 with N,7NI7n]==SN2CsrS)oo552r
replace 5 at position 18 with n,7NI7n]==SN2CsrS)oon52r
replace ) at position 15 with r,7NI7n]==SN2CsrSroon52r
replace 7 at position 0 with 6,6NI7n]==SN2CsrSroon52r
replace S at position 14 with 7,6NI7n]==SN2Csr7roon52r
add C at position 0,C6NI7n]==SN2Csr7roon52r
add F at position 21,C6NI7n]==SN2Csr7roon5F2r
remove r from position 16,C6NI7n]==SN2Csr7oon5F2r
add F at position 11,C6NI7n]==SNF2Csr7oon5F2r
add 2 at position 11,C6NI7n]==SN2F2Csr7oon5F2r
add 7 at position 5,C6NI77n]==SN2F2Csr7oon5F2r
replace 7 at position 5 with n,C6NI7nn]==SN2F2Csr7oon5F2r
add c at position 21,C6NI7nn]==SN2F2Csr7oocn5F2r
add l at position 9,C6NI7nn]=l=SN2F2Csr7oocn5F2r
add 2 at position 2,C62NI7nn]=l=SN2F2Csr7oocn5F2r
replace 7 at position 5 with c,C62NIcnn]=l=SN2F2Csr7oocn5F2r
replace 2 at position 14 with 5,C62NIcnn]=l=SN5F2Csr7oocn5F2r
add ( at position 11,C62NIcnn]=l(=SN5F2Csr7oocn5F2r
add n at position 18,C62NIcnn]=l(=SN5F2nCsr7oocn5F2r
add O at position 19,C62NIcnn]=l(=SN5F2nOCsr7oocn5F2r
add c at position 24,C62NIcnn]=l(=SN5F2nOCsr7coocn5F2r
add c at position 17,C62NIcnn]=l(=SN5Fc2nOCsr7coocn5F2r
add c at position 26,C62NIcnn]=l(=SN5Fc2nOCsr7ccoocn5F2r
replace c at position 5 with ),C62NI)nn]=l(=SN5Fc2nOCsr7ccoocn5F2r
add r at position 9,C62NI)nn]r=l(=SN5Fc2nOCsr7ccoocn5F2r
add l at position 19,C62NI)nn]r=l(=SN5Fcl2nOCsr7ccoocn5F2r
replace F at position 17 with n,C62NI)nn]r=l(=SN5ncl2nOCsr7ccoocn5F2r
replace s at position 24 with F,C62NI)nn]r=l(=SN5ncl2nOCFr7ccoocn5F2r
add 4 at position 13,C62NI)nn]r=l(4=SN5ncl2nOCFr7ccoocn5F2r
remove 7 from position 27,C62NI)nn]r=l(4=SN5ncl2nOCFrccoocn5F2r
remove r from position 9,C62NI)nn]=l(4=SN5ncl2nOCFrccoocn5F2r
remove N from position 3,C62I)nn]=l(4=SN5ncl2nOCFrccoocn5F2r
add 4 at position 20,C62I)nn]=l(4=SN5ncl24nOCFrccoocn5F2r
remove l from position 9,C62I)nn]=(4=SN5ncl24nOCFrccoocn5F2r
remove I from position 3,C62)nn]=(4=SN5ncl24nOCFrccoocn5F2r
remove 5 from position 30,C62)nn]=(4=SN5ncl24nOCFrccoocnF2r
remove F from position 22,C62)nn]=(4=SN5ncl24nOCrccoocnF2r
replace 5 at position 13 with 3,C62)nn]=(4=SN3ncl24nOCrccoocnF2r
add O at position 11,C62)nn]=(4=OSN3ncl24nOCrccoocnF2r
replace r at position 32 with c,C62)nn]=(4=OSN3ncl24nOCrccoocnF2c
remove r from position 23,C62)nn]=(4=OSN3ncl24nOCccoocnF2c
replace F at position 29 with (,C62)nn]=(4=OSN3ncl24nOCccoocn(2c
remove S from position 12,C62)nn]=(4=ON3ncl24nOCccoocn(2c
add ) at position 12,C62)nn]=(4=O)N3ncl24nOCccoocn(2c
add I at position 4,C62)Inn]=(4=O)N3ncl24nOCccoocn(2c
replace I at position 4 with C,C62)Cnn]=(4=O)N3ncl24nOCccoocn(2c
replace 4 at position 20 with ],C62)Cnn]=(4=O)N3ncl2]nOCccoocn(2c
add S at position 5,C62)CSnn]=(4=O)N3ncl2]nOCccoocn(2c
replace 2 at position 2 with H,C6H)CSnn]=(4=O)N3ncl2]nOCccoocn(2c
add 1 at position 3,C6H1)CSnn]=(4=O)N3ncl2]nOCccoocn(2c
replace ) at position 4 with N,C6H1NCSnn]=(4=O)N3ncl2]nOCccoocn(2c
replace l at position 20 with c,C6H1NCSnn]=(4=O)N3ncc2]nOCccoocn(2c
add 3 at position 17,C6H1NCSnn]=(4=O)N33ncc2]nOCccoocn(2c
add @ at position 10,C6H1NCSnn]@=(4=O)N33ncc2]nOCccoocn(2c
remove S from position 6,C6H1NCnn]@=(4=O)N33ncc2]nOCccoocn(2c
replace N at position 16 with c,C6H1NCnn]@=(4=O)c33ncc2]nOCccoocn(2c
replace n at position 24 with c,C6H1NCnn]@=(4=O)c33ncc2]cOCccoocn(2c
replace n at position 6 with c,C6H1NCcn]@=(4=O)c33ncc2]cOCccoocn(2c
add 2 at position 7,C6H1NCc2n]@=(4=O)c33ncc2]cOCccoocn(2c
remove = from position 11,C6H1NCc2n]@(4=O)c33ncc2]cOCccoocn(2c
replace 3 at position 18 with O,C6H1NCc2n]@(4=O)c3Oncc2]cOCccoocn(2c
add - at position 33,C6H1NCc2n]@(4=O)c3Oncc2]cOCccoocn-(2c
replace @ at position 10 with c,C6H1NCc2n]c(4=O)c3Oncc2]cOCccoocn-(2c
replace o at position 30 with (,C6H1NCc2n]c(4=O)c3Oncc2]cOCcco(cn-(2c
replace ( at position 30 with c,C6H1NCc2n]c(4=O)c3Oncc2]cOCccoccn-(2c
add 1 at position 37,C6H1NCc2n]c(4=O)c3Oncc2]cOCccoccn-(2c1
add - at position 22,C6H1NCc2n]c(4=O)c3Oncc-2]cOCccoccn-(2c1
replace H at position 2 with c,C6c1NCc2n]c(4=O)c3Oncc-2]cOCccoccn-(2c1
remove ] from position 9,C6c1NCc2nc(4=O)c3Oncc-2]cOCccoccn-(2c1
replace 4 at position 11 with (,C6c1NCc2nc((=O)c3Oncc-2]cOCccoccn-(2c1
remove O from position 17,C6c1NCc2nc((=O)c3ncc-2]cOCccoccn-(2c1
add n at position 29,C6c1NCc2nc((=O)c3ncc-2]cOCcconccn-(2c1
remove - from position 33,C6c1NCc2nc((=O)c3ncc-2]cOCcconccn(2c1
remove ] from position 22,C6c1NCc2nc((=O)c3ncc-2cOCcconccn(2c1
add n at position 9,C6c1NCc2nnc((=O)c3ncc-2cOCcconccn(2c1
add H at position 4,C6c1HNCc2nnc((=O)c3ncc-2cOCcconccn(2c1
remove H from position 4,C6c1NCc2nnc((=O)c3ncc-2cOCcconccn(2c1
add 3 at position 24,C6c1NCc2nnc((=O)c3ncc-2c3OCcconccn(2c1
remove - from position 21,C6c1NCc2nnc((=O)c3ncc2c3OCcconccn(2c1
add 3 at position 32,C6c1NCc2nnc((=O)c3ncc2c3OCcconcc3n(2c1
add ) at position 33,C6c1NCc2nnc((=O)c3ncc2c3OCcconcc3)n(2c1
add C at position 12,C6c1NCc2nnc(C(=O)c3ncc2c3OCcconcc3)n(2c1
add c at position 22,C6c1NCc2nnc(C(=O)c3nccc2c3OCcconcc3)n(2c1
replace N at position 4 with O,C6c1OCc2nnc(C(=O)c3nccc2c3OCcconcc3)n(2c1
add N at position 17,C6c1OCc2nnc(C(=O)Nc3nccc2c3OCcconcc3)n(2c1
remove C from position 5,C6c1Oc2nnc(C(=O)Nc3nccc2c3OCcconcc3)n(2c1
add + at position 29,C6c1Oc2nnc(C(=O)Nc3nccc2c3OCc+concc3)n(2c1
replace O at position 4 with c,C6c1cc2nnc(C(=O)Nc3nccc2c3OCc+concc3)n(2c1
remove o from position 31,C6c1cc2nnc(C(=O)Nc3nccc2c3OCc+cncc3)n(2c1
remove n from position 7,C6c1cc2nc(C(=O)Nc3nccc2c3OCc+cncc3)n(2c1
add c at position 9,C6c1cc2ncc(C(=O)Nc3nccc2c3OCc+cncc3)n(2c1
add c at position 30,C6c1cc2ncc(C(=O)Nc3nccc2c3OCc+ccncc3)n(2c1
remove 6 from position 1,Cc1cc2ncc(C(=O)Nc3nccc2c3OCc+ccncc3)n(2c1
remove 2 from position 22,Cc1cc2ncc(C(=O)Nc3ncccc3OCc+ccncc3)n(2c1
add c at position 5,Cc1ccc2ncc(C(=O)Nc3ncccc3OCc+ccncc3)n(2c1
remove ( from position 37,Cc1ccc2ncc(C(=O)Nc3ncccc3OCc+ccncc3)n2c1
replace + at position 28 with 3,Cc1ccc2ncc(C(=O)Nc3ncccc3OCc3ccncc3)n2c1
final: Cc1ccc2ncc(C(=O)Nc3ncccc3OCc3ccncc3)n2c1,Cc1ccc2ncc(C(=O)Nc3ncccc3OCc3ccncc3)n2c1
