log,state
initialize: ,
add 1 at position 0,1
add C at position 1,1C
add N at position 2,1C<PERSON>
add ) at position 3,1CN)
replace N at position 2 with r,1Cr)
replace <PERSON> at position 1 with o,1or)
remove 1 from position 0,or)
add S at position 1,oSr)
add C at position 4,oSr)C
replace o at position 0 with l,lSr)C
remove S from position 1,lr)C
add / at position 2,lr/)C
remove l from position 0,r/)C
add r at position 0,rr/)C
add - at position 3,rr/-)C
add 3 at position 4,rr/-3)C
add S at position 3,rr/S-3)C
add 1 at position 1,r1r/S-3)C
add 7 at position 0,7r1r/S-3)C
add [ at position 10,7r1r/S-3)C[
remove r from position 1,71r/S-3)C[
add s at position 7,71r/S-3s)C[
add / at position 9,71r/S-3s)/C[
add ] at position 8,71r/S-3s])/C[
replace - at position 5 with O,71r/SO3s])/C[
add ) at position 6,71r/SO)3s])/C[
replace <PERSON> at position 12 with H,71r/SO)3s])/H[
replace / at position 11 with 3,71r/SO)3s])3H[
remove s from position 8,71r/SO)3])3H[
add 5 at position 9,71r/SO)3]5)3H[
replace r at position 2 with N,71N/SO)3]5)3H[
add S at position 10,71N/SO)3]5S)3H[
replace / at position 3 with I,71NISO)3]5S)3H[
remove 5 from position 9,71NISO)3]S)3H[
add F at position 12,71NISO)3]S)3FH[
add o at position 2,71oNISO)3]S)3FH[
add 4 at position 7,71oNISO4)3]S)3FH[
replace 3 at position 9 with O,71oNISO4)O]S)3FH[
replace o at position 2 with 7,717NISO4)O]S)3FH[
add ] at position 13,717NISO4)O]S)]3FH[
add 7 at position 10,717NISO4)O7]S)]3FH[
remove 1 from position 1,77NISO4)O7]S)]3FH[
remove 7 from position 0,7NISO4)O7]S)]3FH[
replace 7 at position 8 with 2,7NISO4)O2]S)]3FH[
add 7 at position 3,7NI7SO4)O2]S)]3FH[
remove H from position 16,7NI7SO4)O2]S)]3F[
replace 3 at position 14 with 5,7NI7SO4)O2]S)]5F[
add s at position 10,7NI7SO4)O2s]S)]5F[
add n at position 6,7NI7SOn4)O2s]S)]5F[
replace ) at position 8 with C,7NI7SOn4CO2s]S)]5F[
add 1 at position 16,7NI7SOn4CO2s]S)]15F[
add 5 at position 9,7NI7SOn4C5O2s]S)]15F[
add 5 at position 18,7NI7SOn4C5O2s]S)]155F[
replace 5 at position 9 with (,7NI7SOn4C(O2s]S)]155F[
replace 5 at position 18 with n,7NI7SOn4C(O2s]S)]1n5F[
replace ) at position 15 with r,7NI7SOn4C(O2s]Sr]1n5F[
replace 7 at position 0 with 6,6NI7SOn4C(O2s]Sr]1n5F[
replace S at position 14 with 7,6NI7SOn4C(O2s]7r]1n5F[
add C at position 0,C6NI7SOn4C(O2s]7r]1n5F[
add - at position 21,C6NI7SOn4C(O2s]7r]1n5-F[
remove r from position 16,C6NI7SOn4C(O2s]7]1n5-F[
add H at position 11,C6NI7SOn4C(HO2s]7]1n5-F[
add 2 at position 11,C6NI7SOn4C(2HO2s]7]1n5-F[
add 7 at position 5,C6NI77SOn4C(2HO2s]7]1n5-F[
replace 7 at position 5 with N,C6NI7NSOn4C(2HO2s]7]1n5-F[
add s at position 21,C6NI7NSOn4C(2HO2s]7]1sn5-F[
add l at position 9,C6NI7NSOnl4C(2HO2s]7]1sn5-F[
add 2 at position 2,C62NI7NSOnl4C(2HO2s]7]1sn5-F[
replace 7 at position 5 with c,C62NIcNSOnl4C(2HO2s]7]1sn5-F[
replace 2 at position 14 with 4,C62NIcNSOnl4C(4HO2s]7]1sn5-F[
add ] at position 11,C62NIcNSOnl]4C(4HO2s]7]1sn5-F[
add ( at position 18,C62NIcNSOnl]4C(4HO(2s]7]1sn5-F[
add [ at position 19,C62NIcNSOnl]4C(4HO([2s]7]1sn5-F[
add ) at position 24,C62NIcNSOnl]4C(4HO([2s]7)]1sn5-F[
add @ at position 17,C62NIcNSOnl]4C(4H@O([2s]7)]1sn5-F[
add 1 at position 26,C62NIcNSOnl]4C(4H@O([2s]7)1]1sn5-F[
replace c at position 5 with C,C62NICNSOnl]4C(4H@O([2s]7)1]1sn5-F[
add r at position 9,C62NICNSOrnl]4C(4H@O([2s]7)1]1sn5-F[
add n at position 19,C62NICNSOrnl]4C(4H@nO([2s]7)1]1sn5-F[
replace H at position 17 with C,C62NICNSOrnl]4C(4C@nO([2s]7)1]1sn5-F[
replace s at position 24 with F,C62NICNSOrnl]4C(4C@nO([2F]7)1]1sn5-F[
add @ at position 13,C62NICNSOrnl]@4C(4C@nO([2F]7)1]1sn5-F[
remove 7 from position 27,C62NICNSOrnl]@4C(4C@nO([2F])1]1sn5-F[
remove r from position 9,C62NICNSOnl]@4C(4C@nO([2F])1]1sn5-F[
remove N from position 3,C62ICNSOnl]@4C(4C@nO([2F])1]1sn5-F[
add ] at position 20,C62ICNSOnl]@4C(4C@nO]([2F])1]1sn5-F[
remove l from position 9,C62ICNSOn]@4C(4C@nO]([2F])1]1sn5-F[
remove I from position 3,C62CNSOn]@4C(4C@nO]([2F])1]1sn5-F[
remove 5 from position 30,C62CNSOn]@4C(4C@nO]([2F])1]1sn-F[
remove F from position 22,C62CNSOn]@4C(4C@nO]([2])1]1sn-F[
replace 4 at position 13 with F,C62CNSOn]@4C(FC@nO]([2])1]1sn-F[
replace 1 at position 26 with ),C62CNSOn]@4C(FC@nO]([2])1])sn-F[
replace F at position 13 with =,C62CNSOn]@4C(=C@nO]([2])1])sn-F[
add H at position 23,C62CNSOn]@4C(=C@nO]([2]H)1])sn-F[
add ) at position 29,C62CNSOn]@4C(=C@nO]([2]H)1])s)n-F[
remove 1 from position 25,C62CNSOn]@4C(=C@nO]([2]H)])s)n-F[
remove s from position 27,C62CNSOn]@4C(=C@nO]([2]H)]))n-F[
remove n from position 16,C62CNSOn]@4C(=C@O]([2]H)]))n-F[
replace @ at position 9 with l,C62CNSOn]l4C(=C@O]([2]H)]))n-F[
replace [ at position 30 with 7,C62CNSOn]l4C(=C@O]([2]H)]))n-F7
add C at position 5,C62CNCSOn]l4C(=C@O]([2]H)]))n-F7
replace 2 at position 2 with I,C6ICNCSOn]l4C(=C@O]([2]H)]))n-F7
remove ( from position 13,C6ICNCSOn]l4C=C@O]([2]H)]))n-F7
replace ) at position 23 with +,C6ICNCSOn]l4C=C@O]([2]H+]))n-F7
replace l at position 10 with c,C6ICNCSOn]c4C=C@O]([2]H+]))n-F7
replace n at position 27 with ],C6ICNCSOn]c4C=C@O]([2]H+]))]-F7
add O at position 17,C6ICNCSOn]c4C=C@OO]([2]H+]))]-F7
add @ at position 10,C6ICNCSOn]@c4C=C@OO]([2]H+]))]-F7
remove S from position 6,C6ICNCOn]@c4C=C@OO]([2]H+]))]-F7
replace O at position 16 with H,C6ICNCOn]@c4C=C@HO]([2]H+]))]-F7
remove 7 from position 31,C6ICNCOn]@c4C=C@HO]([2]H+]))]-F
replace F at position 30 with (,C6ICNCOn]@c4C=C@HO]([2]H+]))]-(
replace = at position 13 with [,C6ICNCOn]@c4C[C@HO]([2]H+]))]-(
add 1 at position 7,C6ICNCO1n]@c4C[C@HO]([2]H+]))]-(
add = at position 28,C6ICNCO1n]@c4C[C@HO]([2]H+])=)]-(
replace @ at position 10 with c,C6ICNCO1n]cc4C[C@HO]([2]H+])=)]-(
add [ at position 30,C6ICNCO1n]cc4C[C@HO]([2]H+])=)[]-(
add o at position 29,C6ICNCO1n]cc4C[C@HO]([2]H+])=o)[]-(
add - at position 22,C6ICNCO1n]cc4C[C@HO]([-2]H+])=o)[]-(
replace I at position 2 with C,C6CCNCO1n]cc4C[C@HO]([-2]H+])=o)[]-(
remove ] from position 9,C6CCNCO1ncc4C[C@HO]([-2]H+])=o)[]-(
replace 4 at position 11 with 1,C6CCNCO1ncc1C[C@HO]([-2]H+])=o)[]-(
remove O from position 17,C6CCNCO1ncc1C[C@H]([-2]H+])=o)[]-(
add O at position 29,C6CCNCO1ncc1C[C@H]([-2]H+])=oO)[]-(
remove - from position 33,C6CCNCO1ncc1C[C@H]([-2]H+])=oO)[](
remove ] from position 22,C6CCNCO1ncc1C[C@H]([-2H+])=oO)[](
add c at position 9,C6CCNCO1nccc1C[C@H]([-2H+])=oO)[](
add H at position 4,C6CCHNCO1nccc1C[C@H]([-2H+])=oO)[](
remove H from position 4,C6CCNCO1nccc1C[C@H]([-2H+])=oO)[](
add 3 at position 24,C6CCNCO1nccc1C[C@H]([-2H3+])=oO)[](
remove - from position 21,C6CCNCO1nccc1C[C@H]([2H3+])=oO)[](
add O at position 32,C6CCNCO1nccc1C[C@H]([2H3+])=oO)[O](
add - at position 33,C6CCNCO1nccc1C[C@H]([2H3+])=oO)[O-](
add c at position 12,C6CCNCO1ncccc1C[C@H]([2H3+])=oO)[O-](
add N at position 22,C6CCNCO1ncccc1C[C@H]([N2H3+])=oO)[O-](
replace N at position 4 with S,C6CCSCO1ncccc1C[C@H]([N2H3+])=oO)[O-](
add @ at position 17,C6CCSCO1ncccc1C[C@@H]([N2H3+])=oO)[O-](
remove C from position 5,C6CCSO1ncccc1C[C@@H]([N2H3+])=oO)[O-](
add + at position 29,C6CCSO1ncccc1C[C@@H]([N2H3+])+=oO)[O-](
replace S at position 4 with C,C6CCCO1ncccc1C[C@@H]([N2H3+])+=oO)[O-](
remove o from position 31,C6CCCO1ncccc1C[C@@H]([N2H3+])+=O)[O-](
remove n from position 7,C6CCCO1cccc1C[C@@H]([N2H3+])+=O)[O-](
add c at position 9,C6CCCO1ccccc1C[C@@H]([N2H3+])+=O)[O-](
add ( at position 30,C6CCCO1ccccc1C[C@@H]([N2H3+])+(=O)[O-](
remove 6 from position 1,CCCCO1ccccc1C[C@@H]([N2H3+])+(=O)[O-](
remove 2 from position 22,CCCCO1ccccc1C[C@@H]([NH3+])+(=O)[O-](
add c at position 5,CCCCOc1ccccc1C[C@@H]([NH3+])+(=O)[O-](
remove ( from position 37,CCCCOc1ccccc1C[C@@H]([NH3+])+(=O)[O-]
replace + at position 28 with C,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O-]
final: CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O-],CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O-]
