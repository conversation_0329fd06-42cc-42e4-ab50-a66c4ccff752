log,state
initialize: CC(=O)N[C@@H](C(=O)NC1COC1)C(C)C,CC(=O)N[C@@H](C(=O)NC1COC1)C(C)C
replace ( at position 28 with -,CC(=O)N[C@@H](C(=O)NC1COC1)C-C)C
add O at position 2,CCO(=O)N[C@@H](C(=O)NC1COC1)C-C)C
remove ) from position 6,CCO(=ON[C@@H](C(=O)NC1COC1)C-C)C
replace ( at position 15 with #,CCO(=ON[C@@H](C#=O)NC1COC1)C-C)C
add 6 at position 1,C6CO(=ON[C@@H](C#=O)NC1COC1)C-C)C
remove C from position 30,C6CO(=ON[C@@H](C#=O)NC1COC1)C-)C
remove C from position 9,C6CO(=ON[@@H](C#=O)NC1COC1)C-)C
add n at position 7,C6CO(=OnN[@@H](C#=O)NC1COC1)C-)C
add o at position 31,C6CO(=OnN[@@H](C#=O)NC1COC1)C-)oC
replace ( at position 4 with S,C6COS=OnN[@@H](C#=O)NC1COC1)C-)oC
remove - from position 29,C6COS=OnN[@@H](C#=O)NC1COC1)C)oC
add C at position 5,C6COSC=OnN[@@H](C#=O)NC1COC1)C)oC
remove # from position 17,C6COSC=OnN[@@H](C=O)NC1COC1)C)oC
replace S at position 4 with N,C6CONC=OnN[@@H](C=O)NC1COC1)C)oC
remove 1 from position 22,C6CONC=OnN[@@H](C=O)NCCOC1)C)oC
remove N from position 20,C6CONC=OnN[@@H](C=O)CCOC1)C)oC
replace ] at position 14 with r,C6CONC=OnN[@@Hr(C=O)CCOC1)C)oC
remove C from position 0,6CONC=OnN[@@Hr(C=O)CCOC1)C)oC
replace O at position 2 with #,6C#NC=OnN[@@Hr(C=O)CCOC1)C)oC
remove C from position 22,6C#NC=OnN[@@Hr(C=O)CCO1)C)oC
remove H from position 12,6C#NC=OnN[@@r(C=O)CCO1)C)oC
add H at position 2,6CH#NC=OnN[@@r(C=O)CCO1)C)oC
remove H from position 2,6C#NC=OnN[@@r(C=O)CCO1)C)oC
remove C from position 4,6C#N=OnN[@@r(C=O)CCO1)C)oC
add ] at position 11,6C#N=OnN[@@]r(C=O)CCO1)C)oC
add - at position 16,6C#N=OnN[@@]r(C=-O)CCO1)C)oC
remove C from position 14,6C#N=OnN[@@]r(=-O)CCO1)C)oC
add O at position 8,6C#N=OnNO[@@]r(=-O)CCO1)C)oC
replace O at position 5 with 3,6C#N=3nNO[@@]r(=-O)CCO1)C)oC
add - at position 27,6C#N=3nNO[@@]r(=-O)CCO1)C)o-C
replace ( at position 14 with ),6C#N=3nNO[@@]r)=-O)CCO1)C)o-C
remove C from position 24,6C#N=3nNO[@@]r)=-O)CCO1))o-C
remove @ from position 10,6C#N=3nNO[@]r)=-O)CCO1))o-C
remove = from position 14,6C#N=3nNO[@]r)-O)CCO1))o-C
remove C from position 25,6C#N=3nNO[@]r)-O)CCO1))o-
replace O at position 19 with r,6C#N=3nNO[@]r)-O)CCr1))o-
add ] at position 23,6C#N=3nNO[@]r)-O)CCr1))]o-
remove - from position 14,6C#N=3nNO[@]r)O)CCr1))]o-
remove N from position 3,6C#=3nNO[@]r)O)CCr1))]o-
replace O at position 13 with =,6C#=3nNO[@]r)=)CCr1))]o-
replace C at position 16 with +,6C#=3nNO[@]r)=)C+r1))]o-
replace C at position 15 with 7,6C#=3nNO[@]r)=)7+r1))]o-
replace ] at position 21 with 3,6C#=3nNO[@]r)=)7+r1))3o-
remove r from position 11,6C#=3nNO[@])=)7+r1))3o-
add c at position 22,6C#=3nNO[@])=)7+r1))3oc-
replace ) at position 18 with s,6C#=3nNO[@])=)7+r1s)3oc-
replace 3 at position 4 with o,6C#=onNO[@])=)7+r1s)3oc-
replace - at position 23 with 6,6C#=onNO[@])=)7+r1s)3oc6
replace 7 at position 14 with ],6C#=onNO[@])=)]+r1s)3oc6
add 1 at position 12,6C#=onNO[@])1=)]+r1s)3oc6
add c at position 9,6C#=onNO[c@])1=)]+r1s)3oc6
add l at position 16,6C#=onNO[c@])1=)l]+r1s)3oc6
add 5 at position 22,6C#=onNO[c@])1=)l]+r1s5)3oc6
remove = from position 14,6C#=onNO[c@])1)l]+r1s5)3oc6
add n at position 8,6C#=onNOn[c@])1)l]+r1s5)3oc6
add s at position 13,6C#=onNOn[c@]s)1)l]+r1s5)3oc6
add 1 at position 12,6C#=onNOn[c@1]s)1)l]+r1s5)3oc6
remove c from position 28,6C#=onNOn[c@1]s)1)l]+r1s5)3o6
remove O from position 7,6C#=onNn[c@1]s)1)l]+r1s5)3o6
replace 1 at position 11 with s,6C#=onNn[c@s]s)1)l]+r1s5)3o6
replace ) at position 16 with r,6C#=onNn[c@s]s)1rl]+r1s5)3o6
remove n from position 5,6C#=oNn[c@s]s)1rl]+r1s5)3o6
replace n at position 6 with 4,6C#=oN4[c@s]s)1rl]+r1s5)3o6
add F at position 11,6C#=oN4[c@sF]s)1rl]+r1s5)3o6
add s at position 24,6C#=oN4[c@sF]s)1rl]+r1s5s)3o6
remove s from position 10,6C#=oN4[c@F]s)1rl]+r1s5s)3o6
add I at position 1,6IC#=oN4[c@F]s)1rl]+r1s5s)3o6
add l at position 4,6IC#l=oN4[c@F]s)1rl]+r1s5s)3o6
remove 1 from position 22,6IC#l=oN4[c@F]s)1rl]+rs5s)3o6
remove 6 from position 0,IC#l=oN4[c@F]s)1rl]+rs5s)3o6
remove C from position 1,I#l=oN4[c@F]s)1rl]+rs5s)3o6
remove 3 from position 24,I#l=oN4[c@F]s)1rl]+rs5s)o6
replace 4 at position 6 with (,I#l=oN([c@F]s)1rl]+rs5s)o6
remove ) from position 23,I#l=oN([c@F]s)1rl]+rs5so6
remove 5 from position 21,I#l=oN([c@F]s)1rl]+rsso6
remove c from position 8,I#l=oN([@F]s)1rl]+rsso6
remove F from position 9,I#l=oN([@]s)1rl]+rsso6
remove 6 from position 21,I#l=oN([@]s)1rl]+rsso
remove o from position 4,I#l=N([@]s)1rl]+rsso
replace l at position 2 with ],I#]=N([@]s)1rl]+rsso
remove l from position 13,I#]=N([@]s)1r]+rsso
remove o from position 18,I#]=N([@]s)1r]+rss
remove = from position 3,I#]N([@]s)1r]+rss
remove s from position 8,I#]N([@])1r]+rss
add ( at position 12,I#]N([@])1r](+rss
remove r from position 14,I#]N([@])1r](+ss
add / at position 15,I#]N([@])1r](+s/s
remove I from position 0,#]N([@])1r](+s/s
add 4 at position 12,#]N([@])1r](4+s/s
remove / from position 15,#]N([@])1r](4+ss
add / at position 15,#]N([@])1r](4+s/s
remove ( from position 11,#]N([@])1r]4+s/s
remove 4 from position 11,#]N([@])1r]+s/s
add c at position 14,#]N([@])1r]+s/cs
remove # from position 0,]N([@])1r]+s/cs
replace s at position 11 with @,]N([@])1r]+@/cs
remove N from position 1,]([@])1r]+@/cs
add B at position 12,]([@])1r]+@/Bcs
add o at position 4,]([@o])1r]+@/Bcs
replace + at position 10 with 7,]([@o])1r]7@/Bcs
replace ] at position 9 with 4,]([@o])1r47@/Bcs
remove 7 from position 10,]([@o])1r4@/Bcs
remove o from position 4,]([@])1r4@/Bcs
remove 4 from position 8,]([@])1r@/Bcs
replace ] at position 4 with ),]([@))1r@/Bcs
remove / from position 9,]([@))1r@Bcs
add 5 at position 8,]([@))1r5@Bcs
replace r at position 7 with 3,]([@))135@Bcs
add H at position 8,]([@))13H5@Bcs
remove ( from position 1,][@))13H5@Bcs
replace ) at position 4 with 7,][@)713H5@Bcs
add 7 at position 0,7][@)713H5@Bcs
add 1 at position 0,17][@)713H5@Bcs
remove ) from position 5,17][@713H5@Bcs
remove 1 from position 6,17][@73H5@Bcs
replace 7 at position 1 with o,1o][@73H5@Bcs
replace @ at position 4 with 3,1o][373H5@Bcs
remove @ from position 9,1o][373H5Bcs
add ) at position 8,1o][373H)5Bcs
remove s from position 12,1o][373H)5Bc
add 5 at position 9,1o][373H)55Bc
replace [ at position 3 with /,1o]/373H)55Bc
remove 5 from position 10,1o]/373H)5Bc
replace ] at position 2 with r,1or/373H)5Bc
remove 5 from position 9,1or/373H)Bc
add s at position 8,1or/373Hs)Bc
replace c at position 11 with /,1or/373Hs)B/
replace s at position 8 with S,1or/373HS)B/
replace 7 at position 5 with -,1or/3-3HS)B/
remove S from position 8,1or/3-3H)B/
remove B from position 9,1or/3-3H)/
remove H from position 7,1or/3-3)/
add r at position 1,1ror/3-3)/
remove - from position 6,1ror/33)/
replace r at position 1 with 5,15or/33)/
add 6 at position 3,15o6r/33)/
replace ) at position 8 with [,15o6r/33[/
replace / at position 9 with C,15o6r/33[C
replace 1 at position 0 with B,B5o6r/33[C
replace r at position 4 with =,B5o6=/33[C
replace o at position 2 with B,B5B6=/33[C
remove 6 from position 3,B5B=/33[C
remove B from position 0,5B=/33[C
replace 3 at position 5 with I,5B=/3I[C
remove = from position 2,5B/3I[C
add 1 at position 0,15B/3I[C
replace B at position 2 with F,15F/3I[C
replace 3 at position 4 with O,15F/OI[C
remove C from position 7,15F/OI[
remove I from position 5,15F/O[
remove / from position 3,15FO[
remove F from position 2,15O[
add o at position 0,o15O[
remove 5 from position 2,o1O[
add ( at position 4,o1O[(
replace ( at position 4 with I,o1O[I
remove I from position 4,o1O[
add 6 at position 2,o16O[
replace 6 at position 2 with [,o1[O[
remove 1 from position 1,o[O[
remove [ from position 1,oO[
replace [ at position 2 with S,oOS
remove o from position 0,OS
remove O from position 0,S
remove S from position 0,
final: ,
