log,state
initialize: Cc1nc2c(c(Nc3ncc(C)s3)n1)CCN(C(=O)CCc1ccccc1)C2,Cc1nc2c(c(Nc3ncc(C)s3)n1)CCN(C(=O)CCc1ccccc1)C2
replace ( at position 28 with -,Cc1nc2c(c(Nc3ncc(C)s3)n1)CCN-C(=O)CCc1ccccc1)C2
add ( at position 37,Cc1nc2c(c(Nc3ncc(C)s3)n1)CCN-C(=O)CCc(1ccccc1)C2
remove 2 from position 5,Cc1ncc(c(Nc3ncc(C)s3)n1)CCN-C(=O)CCc(1ccccc1)C2
add 2 at position 22,Cc1ncc(c(Nc3ncc(C)s3)n21)CCN-C(=O)CCc(1ccccc1)C2
add 6 at position 1,C6c1ncc(c(Nc3ncc(C)s3)n21)CCN-C(=O)CCc(1ccccc1)C2
remove C from position 30,C6c1ncc(c(Nc3ncc(C)s3)n21)CCN-(=O)CCc(1ccccc1)C2
remove ( from position 9,C6c1ncc(cNc3ncc(C)s3)n21)CCN-(=O)CCc(1ccccc1)C2
add + at position 45,C6c1ncc(cNc3ncc(C)s3)n21)CCN-(=O)CCc(1ccccc1)+C2
replace ) at position 32 with C,C6c1ncc(cNc3ncc(C)s3)n21)CCN-(=OCCCc(1ccccc1)+C2
replace ( at position 15 with +,C6c1ncc(cNc3ncc+C)s3)n21)CCN-(=OCCCc(1ccccc1)+C2
remove ( from position 29,C6c1ncc(cNc3ncc+C)s3)n21)CCN-=OCCCc(1ccccc1)+C2
add ) at position 43,C6c1ncc(cNc3ncc+C)s3)n21)CCN-=OCCCc(1ccccc1))+C2
remove c from position 2,C61ncc(cNc3ncc+C)s3)n21)CCN-=OCCCc(1ccccc1))+C2
remove 1 from position 35,C61ncc(cNc3ncc+C)s3)n21)CCN-=OCCCc(ccccc1))+C2
add B at position 19,C61ncc(cNc3ncc+C)s3B)n21)CCN-=OCCCc(ccccc1))+C2
replace s at position 17 with 2,C61ncc(cNc3ncc+C)23B)n21)CCN-=OCCCc(ccccc1))+C2
remove c from position 12,C61ncc(cNc3nc+C)23B)n21)CCN-=OCCCc(ccccc1))+C2
remove c from position 33,C61ncc(cNc3nc+C)23B)n21)CCN-=OCCC(ccccc1))+C2
remove C from position 32,C61ncc(cNc3nc+C)23B)n21)CCN-=OCC(ccccc1))+C2
add - at position 21,C61ncc(cNc3nc+C)23B)n-21)CCN-=OCC(ccccc1))+C2
remove + from position 42,C61ncc(cNc3nc+C)23B)n-21)CCN-=OCC(ccccc1))C2
remove n from position 3,C61cc(cNc3nc+C)23B)n-21)CCN-=OCC(ccccc1))C2
add / at position 31,C61cc(cNc3nc+C)23B)n-21)CCN-=OC/C(ccccc1))C2
add - at position 13,C61cc(cNc3nc+-C)23B)n-21)CCN-=OC/C(ccccc1))C2
add ] at position 22,C61cc(cNc3nc+-C)23B)n-]21)CCN-=OC/C(ccccc1))C2
add - at position 33,C61cc(cNc3nc+-C)23B)n-]21)CCN-=OC-/C(ccccc1))C2
remove - from position 29,C61cc(cNc3nc+-C)23B)n-]21)CCN=OC-/C(ccccc1))C2
add O at position 17,C61cc(cNc3nc+-C)2O3B)n-]21)CCN=OC-/C(ccccc1))C2
replace c at position 11 with 3,C61cc(cNc3n3+-C)2O3B)n-]21)CCN=OC-/C(ccccc1))C2
add ] at position 9,C61cc(cNc]3n3+-C)2O3B)n-]21)CCN=OC-/C(ccccc1))C2
replace 1 at position 2 with I,C6Icc(cNc]3n3+-C)2O3B)n-]21)CCN=OC-/C(ccccc1))C2
remove n from position 22,C6Icc(cNc]3n3+-C)2O3B)-]21)CCN=OC-/C(ccccc1))C2
remove c from position 37,C6Icc(cNc]3n3+-C)2O3B)-]21)CCN=OC-/C(cccc1))C2
replace = at position 30 with (,C6Icc(cNc]3n3+-C)2O3B)-]21)CCN(OC-/C(cccc1))C2
replace ( at position 30 with o,C6Icc(cNc]3n3+-C)2O3B)-]21)CCNoOC-/C(cccc1))C2
replace 3 at position 10 with B,C6Icc(cNc]Bn3+-C)2O3B)-]21)CCNoOC-/C(cccc1))C2
remove - from position 33,C6Icc(cNc]Bn3+-C)2O3B)-]21)CCNoOC/C(cccc1))C2
replace O at position 18 with 3,C6Icc(cNc]Bn3+-C)233B)-]21)CCNoOC/C(cccc1))C2
add = at position 11,C6Icc(cNc]B=n3+-C)233B)-]21)CCNoOC/C(cccc1))C2
remove N from position 7,C6Icc(cc]B=n3+-C)233B)-]21)CCNoOC/C(cccc1))C2
replace c at position 6 with n,C6Icc(nc]B=n3+-C)233B)-]21)CCNoOC/C(cccc1))C2
replace 2 at position 24 with n,C6Icc(nc]B=n3+-C)233B)-]n1)CCNoOC/C(cccc1))C2
replace ) at position 16 with O,C6Icc(nc]B=n3+-CO233B)-]n1)CCNoOC/C(cccc1))C2
add S at position 6,C6Icc(Snc]B=n3+-CO233B)-]n1)CCNoOC/C(cccc1))C2
remove B from position 10,C6Icc(Snc]=n3+-CO233B)-]n1)CCNoOC/C(cccc1))C2
remove 2 from position 17,C6Icc(Snc]=n3+-CO33B)-]n1)CCNoOC/C(cccc1))C2
replace ) at position 20 with l,C6Icc(Snc]=n3+-CO33Bl-]n1)CCNoOC/C(cccc1))C2
replace c at position 4 with ),C6Ic)(Snc]=n3+-CO33Bl-]n1)CCNoOC/C(cccc1))C2
remove c from position 3,C6I)(Snc]=n3+-CO33Bl-]n1)CCNoOC/C(cccc1))C2
replace I at position 2 with 2,C62)(Snc]=n3+-CO33Bl-]n1)CCNoOC/C(cccc1))C2
remove S from position 5,C62)(nc]=n3+-CO33Bl-]n1)CCNoOC/C(cccc1))C2
replace ] at position 20 with 4,C62)(nc]=n3+-CO33Bl-4n1)CCNoOC/C(cccc1))C2
replace ( at position 4 with I,C62)Inc]=n3+-CO33Bl-4n1)CCNoOC/C(cccc1))C2
remove I from position 4,C62)nc]=n3+-CO33Bl-4n1)CCNoOC/C(cccc1))C2
remove C from position 12,C62)nc]=n3+-O33Bl-4n1)CCNoOC/C(cccc1))C2
add S at position 12,C62)nc]=n3+-SO33Bl-4n1)CCNoOC/C(cccc1))C2
replace / at position 29 with F,C62)nc]=n3+-SO33Bl-4n1)CCNoOCFC(cccc1))C2
add r at position 23,C62)nc]=n3+-SO33Bl-4n1)rCCNoOCFC(cccc1))C2
replace ( at position 32 with r,C62)nc]=n3+-SO33Bl-4n1)rCCNoOCFCrcccc1))C2
remove - from position 11,C62)nc]=n3+SO33Bl-4n1)rCCNoOCFCrcccc1))C2
replace 3 at position 13 with 5,C62)nc]=n3+SO53Bl-4n1)rCCNoOCFCrcccc1))C2
add F at position 22,C62)nc]=n3+SO53Bl-4n1)FrCCNoOCFCrcccc1))C2
add B at position 35,C62)nc]=n3+SO53Bl-4n1)FrCCNoOCFCrccBcc1))C2
remove n from position 8,C62)nc]=3+SO53Bl-4n1)FrCCNoOCFCrccBcc1))C2
add r at position 10,C62)nc]=3+rSO53Bl-4n1)FrCCNoOCFCrccBcc1))C2
add ) at position 34,C62)nc]=3+rSO53Bl-4n1)FrCCNoOCFCrc)cBcc1))C2
remove r from position 10,C62)nc]=3+SO53Bl-4n1)FrCCNoOCFCrc)cBcc1))C2
add ( at position 43,C62)nc]=3+SO53Bl-4n1)FrCCNoOCFCrc)cBcc1))C2(
remove + from position 9,C62)nc]=3SO53Bl-4n1)FrCCNoOCFCrc)cBcc1))C2(
replace 2 at position 2 with @,C6@)nc]=3SO53Bl-4n1)FrCCNoOCFCrc)cBcc1))C2(
remove F from position 20,C6@)nc]=3SO53Bl-4n1)rCCNoOCFCrc)cBcc1))C2(
add r at position 24,C6@)nc]=3SO53Bl-4n1)rCCNroOCFCrc)cBcc1))C2(
replace n at position 17 with F,C6@)nc]=3SO53Bl-4F1)rCCNroOCFCrc)cBcc1))C2(
remove ) from position 19,C6@)nc]=3SO53Bl-4F1rCCNroOCFCrc)cBcc1))C2(
remove 1 from position 36,C6@)nc]=3SO53Bl-4F1rCCNroOCFCrc)cBcc))C2(
replace c at position 34 with ),C6@)nc]=3SO53Bl-4F1rCCNroOCFCrc)cB)c))C2(
replace F at position 17 with =,C6@)nc]=3SO53Bl-4=1rCCNroOCFCrc)cB)c))C2(
remove ) from position 36,C6@)nc]=3SO53Bl-4=1rCCNroOCFCrc)cB)c)C2(
remove = from position 7,C6@)nc]3SO53Bl-4=1rCCNroOCFCrc)cB)c)C2(
remove 1 from position 17,C6@)nc]3SO53Bl-4=rCCNroOCFCrc)cB)c)C2(
add 7 at position 37,C6@)nc]3SO53Bl-4=rCCNroOCFCrc)cB)c)C27(
add 2 at position 28,C6@)nc]3SO53Bl-4=rCCNroOCFCr2c)cB)c)C27(
replace 5 at position 10 with =,C6@)nc]3SO=3Bl-4=rCCNroOCFCr2c)cB)c)C27(
remove n from position 4,C6@)c]3SO=3Bl-4=rCCNroOCFCr2c)cB)c)C27(
remove N from position 19,C6@)c]3SO=3Bl-4=rCCroOCFCr2c)cB)c)C27(
remove B from position 30,C6@)c]3SO=3Bl-4=rCCroOCFCr2c)c)c)C27(
add / at position 30,C6@)c]3SO=3Bl-4=rCCroOCFCr2c)c/)c)C27(
remove F from position 23,C6@)c]3SO=3Bl-4=rCCroOCCr2c)c/)c)C27(
remove C from position 22,C6@)c]3SO=3Bl-4=rCCroOCr2c)c/)c)C27(
add r at position 33,C6@)c]3SO=3Bl-4=rCCroOCr2c)c/)c)Cr27(
remove c from position 30,C6@)c]3SO=3Bl-4=rCCroOCr2c)c/))Cr27(
remove ) from position 29,C6@)c]3SO=3Bl-4=rCCroOCr2c)c/)Cr27(
remove 3 from position 6,C6@)c]SO=3Bl-4=rCCroOCr2c)c/)Cr27(
add ) at position 30,C6@)c]SO=3Bl-4=rCCroOCr2c)c/)C)r27(
replace C at position 21 with 6,C6@)c]SO=3Bl-4=rCCroO6r2c)c/)C)r27(
replace r at position 18 with 4,C6@)c]SO=3Bl-4=rCC4oO6r2c)c/)C)r27(
remove 6 from position 21,C6@)c]SO=3Bl-4=rCC4oOr2c)c/)C)r27(
remove 4 from position 18,C6@)c]SO=3Bl-4=rCCoOr2c)c/)C)r27(
remove ( from position 32,C6@)c]SO=3Bl-4=rCCoOr2c)c/)C)r27
replace C at position 17 with ),C6@)c]SO=3Bl-4=rC)oOr2c)c/)C)r27
remove 4 from position 13,C6@)c]SO=3Bl-=rC)oOr2c)c/)C)r27
remove B from position 10,C6@)c]SO=3l-=rC)oOr2c)c/)C)r27
replace C at position 14 with 3,C6@)c]SO=3l-=r3)oOr2c)c/)C)r27
add H at position 16,C6@)c]SO=3l-=r3)HoOr2c)c/)C)r27
remove ) from position 3,C6@c]SO=3l-=r3)HoOr2c)c/)C)r27
replace 3 at position 8 with 7,C6@c]SO=7l-=r3)HoOr2c)c/)C)r27
add 7 at position 0,7C6@c]SO=7l-=r3)HoOr2c)c/)C)r27
add 1 at position 2,7C16@c]SO=7l-=r3)HoOr2c)c/)C)r27
remove r from position 20,7C16@c]SO=7l-=r3)HoO2c)c/)C)r27
remove = from position 13,7C16@c]SO=7l-r3)HoO2c)c/)C)r27
replace 7 at position 29 with ),7C16@c]SO=7l-r3)HoO2c)c/)C)r2)
replace = at position 9 with 3,7C16@c]SO37l-r3)HoO2c)c/)C)r2)
remove ) from position 29,7C16@c]SO37l-r3)HoO2c)c/)C)r2
remove l from position 11,7C16@c]SO37-r3)HoO2c)c/)C)r2
remove ) from position 20,7C16@c]SO37-r3)HoO2cc/)C)r2
replace 7 at position 0 with S,SC16@c]SO37-r3)HoO2cc/)C)r2
remove 2 from position 18,SC16@c]SO37-r3)HoOcc/)C)r2
add B at position 15,SC16@c]SO37-r3)BHoOcc/)C)r2
remove / from position 21,SC16@c]SO37-r3)BHoOcc)C)r2
replace @ at position 4 with r,SC16rc]SO37-r3)BHoOcc)C)r2
remove c from position 19,SC16rc]SO37-r3)BHoOc)C)r2
add s at position 17,SC16rc]SO37-r3)BHsoOc)C)r2
replace C at position 22 with /,SC16rc]SO37-r3)BHsoOc)/)r2
replace r at position 24 with C,SC16rc]SO37-r3)BHsoOc)/)C2
remove r from position 12,SC16rc]SO37-3)BHsoOc)/)C2
replace 7 at position 10 with -,SC16rc]SO3--3)BHsoOc)/)C2
remove o from position 17,SC16rc]SO3--3)BHsOc)/)C2
remove ) from position 19,SC16rc]SO3--3)BHsOc/)C2
remove B from position 14,SC16rc]SO3--3)HsOc/)C2
add r at position 2,SCr16rc]SO3--3)HsOc/)C2
remove C from position 21,SCr16rc]SO3--3)HsOc/)2
remove C from position 1,Sr16rc]SO3--3)HsOc/)2
remove 1 from position 2,Sr6rc]SO3--3)HsOc/)2
remove 2 from position 19,Sr6rc]SO3--3)HsOc/)
replace 3 at position 11 with F,Sr6rc]SO3--F)HsOc/)
remove ) from position 12,Sr6rc]SO3--FHsOc/)
remove r from position 1,S6rc]SO3--FHsOc/)
add l at position 2,S6lrc]SO3--FHsOc/)
remove - from position 10,S6lrc]SO3-FHsOc/)
add S at position 7,S6lrc]SSO3-FHsOc/)
replace S at position 0 with o,o6lrc]SSO3-FHsOc/)
remove F from position 11,o6lrc]SSO3-HsOc/)
remove l from position 2,o6rc]SSO3-HsOc/)
remove S from position 6,o6rc]SO3-HsOc/)
replace / at position 13 with N,o6rc]SO3-HsOcN)
remove O from position 11,o6rc]SO3-HscN)
add O at position 2,o6Orc]SO3-HscN)
replace H at position 10 with ],o6Orc]SO3-]scN)
add 6 at position 14,o6Orc]SO3-]scN6)
remove s from position 11,o6Orc]SO3-]cN6)
remove S from position 6,o6Orc]O3-]cN6)
replace r at position 3 with 4,o6O4c]O3-]cN6)
add H at position 5,o6O4cH]O3-]cN6)
replace 6 at position 1 with H,oHO4cH]O3-]cN6)
remove 6 from position 13,oHO4cH]O3-]cN)
add H at position 9,oHO4cH]O3H-]cN)
remove O from position 2,oH4cH]O3H-]cN)
remove ] from position 5,oH4cHO3H-]cN)
replace H at position 4 with [,oH4c[O3H-]cN)
remove 4 from position 2,oHc[O3H-]cN)
remove c from position 2,oH[O3H-]cN)
replace c at position 8 with S,oH[O3H-]SN)
remove O from position 3,oH[3H-]SN)
remove [ from position 2,oH3H-]SN)
remove S from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
