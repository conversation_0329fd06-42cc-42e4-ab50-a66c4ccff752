log,state
initialize: NC(=O)[C@H](Nc1cccc(Oc2ccccc2)c1)c1ccc(F)cc1,NC(=O)[C@H](Nc1cccc(Oc2ccccc2)c1)c1ccc(F)cc1
replace 2 at position 28 with +,NC(=O)[C@H](Nc1cccc(Oc2ccccc+)c1)c1ccc(F)cc1
add ( at position 37,NC(=O)[C@H](Nc1cccc(Oc2ccccc+)c1)c1cc(c(F)cc1
remove ) from position 5,NC(=O[C@H](Nc1cccc(Oc2ccccc+)c1)c1cc(c(F)cc1
add 2 at position 22,NC(=O[C@H](Nc1cccc(Oc22ccccc+)c1)c1cc(c(F)cc1
add 6 at position 1,N6C(=O[C@H](Nc1cccc(Oc22ccccc+)c1)c1cc(c(F)cc1
remove ) from position 30,N6C(=O[C@H](Nc1cccc(Oc22ccccc+c1)c1cc(c(F)cc1
remove H from position 9,N6C(=O[C@](Nc1cccc(Oc22ccccc+c1)c1cc(c(F)cc1
add n at position 7,N6C(=O[nC@](Nc1cccc(Oc22ccccc+c1)c1cc(c(F)cc1
add o at position 31,N6C(=O[nC@](Nc1cccc(Oc22ccccc+co1)c1cc(c(F)cc1
replace = at position 4 with S,N6C(SO[nC@](Nc1cccc(Oc22ccccc+co1)c1cc(c(F)cc1
remove + from position 29,N6C(SO[nC@](Nc1cccc(Oc22cccccco1)c1cc(c(F)cc1
add ) at position 43,N6C(SO[nC@](Nc1cccc(Oc22cccccco1)c1cc(c(F)c)c1
remove C from position 2,N6(SO[nC@](Nc1cccc(Oc22cccccco1)c1cc(c(F)c)c1
remove c from position 35,N6(SO[nC@](Nc1cccc(Oc22cccccco1)c1c(c(F)c)c1
add B at position 19,N6(SO[nC@](Nc1cccc(BOc22cccccco1)c1c(c(F)c)c1
replace c at position 17 with 2,N6(SO[nC@](Nc1ccc2(BOc22cccccco1)c1c(c(F)c)c1
remove c from position 12,N6(SO[nC@](N1ccc2(BOc22cccccco1)c1c(c(F)c)c1
remove 1 from position 33,N6(SO[nC@](N1ccc2(BOc22cccccco1)cc(c(F)c)c1
remove c from position 32,N6(SO[nC@](N1ccc2(BOc22cccccco1)c(c(F)c)c1
add - at position 21,N6(SO[nC@](N1ccc2(BOc-22cccccco1)c(c(F)c)c1
remove 1 from position 42,N6(SO[nC@](N1ccc2(BOc-22cccccco1)c(c(F)c)c
remove S from position 3,N6(O[nC@](N1ccc2(BOc-22cccccco1)c(c(F)c)c
add / at position 31,N6(O[nC@](N1ccc2(BOc-22cccccco1/)c(c(F)c)c
add - at position 13,N6(O[nC@](N1c-cc2(BOc-22cccccco1/)c(c(F)c)c
add ] at position 22,N6(O[nC@](N1c-cc2(BOc-]22cccccco1/)c(c(F)c)c
add - at position 33,N6(O[nC@](N1c-cc2(BOc-]22cccccco1-/)c(c(F)c)c
remove c from position 29,N6(O[nC@](N1c-cc2(BOc-]22ccccco1-/)c(c(F)c)c
add O at position 17,N6(O[nC@](N1c-cc2O(BOc-]22ccccco1-/)c(c(F)c)c
replace 1 at position 11 with 4,N6(O[nC@](N4c-cc2O(BOc-]22ccccco1-/)c(c(F)c)c
add ] at position 9,N6(O[nC@]](N4c-cc2O(BOc-]22ccccco1-/)c(c(F)c)c
replace ( at position 2 with I,N6IO[nC@]](N4c-cc2O(BOc-]22ccccco1-/)c(c(F)c)c
remove c from position 22,N6IO[nC@]](N4c-cc2O(BO-]22ccccco1-/)c(c(F)c)c
remove ( from position 37,N6IO[nC@]](N4c-cc2O(BO-]22ccccco1-/)cc(F)c)c
replace c at position 30 with (,N6IO[nC@]](N4c-cc2O(BO-]22cccc(o1-/)cc(F)c)c
replace ( at position 30 with o,N6IO[nC@]](N4c-cc2O(BO-]22ccccoo1-/)cc(F)c)c
replace ( at position 10 with B,N6IO[nC@]]BN4c-cc2O(BO-]22ccccoo1-/)cc(F)c)c
remove - from position 33,N6IO[nC@]]BN4c-cc2O(BO-]22ccccoo1/)cc(F)c)c
replace O at position 18 with 3,N6IO[nC@]]BN4c-cc23(BO-]22ccccoo1/)cc(F)c)c
add = at position 11,N6IO[nC@]]B=N4c-cc23(BO-]22ccccoo1/)cc(F)c)c
remove @ from position 7,N6IO[nC]]B=N4c-cc23(BO-]22ccccoo1/)cc(F)c)c
replace C at position 6 with n,N6IO[nn]]B=N4c-cc23(BO-]22ccccoo1/)cc(F)c)c
replace 2 at position 24 with n,N6IO[nn]]B=N4c-cc23(BO-]n2ccccoo1/)cc(F)c)c
replace c at position 16 with N,N6IO[nn]]B=N4c-cN23(BO-]n2ccccoo1/)cc(F)c)c
add S at position 6,N6IO[nSn]]B=N4c-cN23(BO-]n2ccccoo1/)cc(F)c)c
remove B from position 10,N6IO[nSn]]=N4c-cN23(BO-]n2ccccoo1/)cc(F)c)c
remove 2 from position 17,N6IO[nSn]]=N4c-cN3(BO-]n2ccccoo1/)cc(F)c)c
replace O at position 20 with l,N6IO[nSn]]=N4c-cN3(Bl-]n2ccccoo1/)cc(F)c)c
replace [ at position 4 with ),N6IO)nSn]]=N4c-cN3(Bl-]n2ccccoo1/)cc(F)c)c
remove O from position 3,N6I)nSn]]=N4c-cN3(Bl-]n2ccccoo1/)cc(F)c)c
replace I at position 2 with 2,N62)nSn]]=N4c-cN3(Bl-]n2ccccoo1/)cc(F)c)c
remove S from position 5,N62)nn]]=N4c-cN3(Bl-]n2ccccoo1/)cc(F)c)c
replace ] at position 20 with 4,N62)nn]]=N4c-cN3(Bl-4n2ccccoo1/)cc(F)c)c
replace n at position 4 with H,N62)Hn]]=N4c-cN3(Bl-4n2ccccoo1/)cc(F)c)c
remove H from position 4,N62)n]]=N4c-cN3(Bl-4n2ccccoo1/)cc(F)c)c
remove c from position 12,N62)n]]=N4c-N3(Bl-4n2ccccoo1/)cc(F)c)c
add S at position 12,N62)n]]=N4c-SN3(Bl-4n2ccccoo1/)cc(F)c)c
replace / at position 29 with F,N62)n]]=N4c-SN3(Bl-4n2ccccoo1F)cc(F)c)c
add r at position 23,N62)n]]=N4c-SN3(Bl-4n2crcccoo1F)cc(F)c)c
replace c at position 32 with r,N62)n]]=N4c-SN3(Bl-4n2crcccoo1F)rc(F)c)c
remove - from position 11,N62)n]]=N4cSN3(Bl-4n2crcccoo1F)rc(F)c)c
replace 3 at position 13 with 5,N62)n]]=N4cSN5(Bl-4n2crcccoo1F)rc(F)c)c
add F at position 22,N62)n]]=N4cSN5(Bl-4n2cFrcccoo1F)rc(F)c)c
add B at position 35,N62)n]]=N4cSN5(Bl-4n2cFrcccoo1F)rc(BF)c)c
remove N from position 8,N62)n]]=4cSN5(Bl-4n2cFrcccoo1F)rc(BF)c)c
add r at position 10,N62)n]]=4crSN5(Bl-4n2cFrcccoo1F)rc(BF)c)c
add ) at position 34,N62)n]]=4crSN5(Bl-4n2cFrcccoo1F)rc)(BF)c)c
remove r from position 10,N62)n]]=4cSN5(Bl-4n2cFrcccoo1F)rc)(BF)c)c
add N at position 3,N62N)n]]=4cSN5(Bl-4n2cFrcccoo1F)rc)(BF)c)c
add r at position 9,N62N)n]]=r4cSN5(Bl-4n2cFrcccoo1F)rc)(BF)c)c
add 7 at position 27,N62N)n]]=r4cSN5(Bl-4n2cFrcc7coo1F)rc)(BF)c)c
remove N from position 13,N62N)n]]=r4cS5(Bl-4n2cFrcc7coo1F)rc)(BF)c)c
replace c at position 24 with s,N62N)n]]=r4cS5(Bl-4n2cFrsc7coo1F)rc)(BF)c)c
replace - at position 17 with H,N62N)n]]=r4cS5(BlH4n2cFrsc7coo1F)rc)(BF)c)c
remove n from position 19,N62N)n]]=r4cS5(BlH42cFrsc7coo1F)rc)(BF)c)c
remove B from position 36,N62N)n]]=r4cS5(BlH42cFrsc7coo1F)rc)(F)c)c
replace ) at position 34 with +,N62N)n]]=r4cS5(BlH42cFrsc7coo1F)rc+(F)c)c
replace H at position 17 with =,N62N)n]]=r4cS5(Bl=42cFrsc7coo1F)rc+(F)c)c
remove F from position 36,N62N)n]]=r4cS5(Bl=42cFrsc7coo1F)rc+()c)c
remove ] from position 7,N62N)n]=r4cS5(Bl=42cFrsc7coo1F)rc+()c)c
remove 4 from position 17,N62N)n]=r4cS5(Bl=2cFrsc7coo1F)rc+()c)c
add 7 at position 37,N62N)n]=r4cS5(Bl=2cFrsc7coo1F)rc+()c)7c
add 2 at position 28,N62N)n]=r4cS5(Bl=2cFrsc7coo12F)rc+()c)7c
replace c at position 10 with 7,N62N)n]=r47S5(Bl=2cFrsc7coo12F)rc+()c)7c
remove ) from position 4,N62Nn]=r47S5(Bl=2cFrsc7coo12F)rc+()c)7c
remove r from position 19,N62Nn]=r47S5(Bl=2cFsc7coo12F)rc+()c)7c
remove c from position 30,N62Nn]=r47S5(Bl=2cFsc7coo12F)r+()c)7c
add / at position 30,N62Nn]=r47S5(Bl=2cFsc7coo12F)r/+()c)7c
remove o from position 23,N62Nn]=r47S5(Bl=2cFsc7co12F)r/+()c)7c
remove c from position 22,N62Nn]=r47S5(Bl=2cFsc7o12F)r/+()c)7c
add r at position 33,N62Nn]=r47S5(Bl=2cFsc7o12F)r/+()cr)7c
remove ( from position 30,N62Nn]=r47S5(Bl=2cFsc7o12F)r/+)cr)7c
remove + from position 29,N62Nn]=r47S5(Bl=2cFsc7o12F)r/)cr)7c
remove = from position 6,N62Nn]r47S5(Bl=2cFsc7o12F)r/)cr)7c
add ) at position 30,N62Nn]r47S5(Bl=2cFsc7o12F)r/)c)r)7c
replace o at position 21 with 6,N62Nn]r47S5(Bl=2cFsc7612F)r/)c)r)7c
replace s at position 18 with 4,N62Nn]r47S5(Bl=2cF4c7612F)r/)c)r)7c
remove 6 from position 21,N62Nn]r47S5(Bl=2cF4c712F)r/)c)r)7c
remove 4 from position 18,N62Nn]r47S5(Bl=2cFc712F)r/)c)r)7c
remove c from position 32,N62Nn]r47S5(Bl=2cFc712F)r/)c)r)7
replace F at position 17 with ),N62Nn]r47S5(Bl=2c)c712F)r/)c)r)7
remove l from position 13,N62Nn]r47S5(B=2c)c712F)r/)c)r)7
remove 5 from position 10,N62Nn]r47S(B=2c)c712F)r/)c)r)7
replace c at position 14 with 3,N62Nn]r47S(B=23)c712F)r/)c)r)7
add H at position 16,N62Nn]r47S(B=23)Hc712F)r/)c)r)7
remove N from position 3,N62n]r47S(B=23)Hc712F)r/)c)r)7
replace S at position 8 with 6,N62n]r476(B=23)Hc712F)r/)c)r)7
add 7 at position 0,7N62n]r476(B=23)Hc712F)r/)c)r)7
add 1 at position 2,7N162n]r476(B=23)Hc712F)r/)c)r)7
remove 1 from position 20,7N162n]r476(B=23)Hc72F)r/)c)r)7
remove = from position 13,7N162n]r476(B23)Hc72F)r/)c)r)7
replace 7 at position 29 with ),7N162n]r476(B23)Hc72F)r/)c)r))
replace 7 at position 9 with 3,7N162n]r436(B23)Hc72F)r/)c)r))
remove ) from position 29,7N162n]r436(B23)Hc72F)r/)c)r)
remove ( from position 11,7N162n]r436B23)Hc72F)r/)c)r)
remove ) from position 20,7N162n]r436B23)Hc72Fr/)c)r)
replace 7 at position 0 with S,SN162n]r436B23)Hc72Fr/)c)r)
remove 2 from position 18,SN162n]r436B23)Hc7Fr/)c)r)
add B at position 15,SN162n]r436B23)BHc7Fr/)c)r)
remove / from position 21,SN162n]r436B23)BHc7Fr)c)r)
replace 2 at position 4 with r,SN16rn]r436B23)BHc7Fr)c)r)
remove F from position 19,SN16rn]r436B23)BHc7r)c)r)
add s at position 17,SN16rn]r436B23)BHsc7r)c)r)
replace c at position 22 with /,SN16rn]r436B23)BHsc7r)/)r)
replace r at position 24 with C,SN16rn]r436B23)BHsc7r)/)C)
remove 2 from position 12,SN16rn]r436B3)BHsc7r)/)C)
replace 6 at position 10 with -,SN16rn]r43-B3)BHsc7r)/)C)
remove c from position 17,SN16rn]r43-B3)BHs7r)/)C)
remove ) from position 19,SN16rn]r43-B3)BHs7r/)C)
remove B from position 14,SN16rn]r43-B3)Hs7r/)C)
add r at position 2,SNr16rn]r43-B3)Hs7r/)C)
remove C from position 21,SNr16rn]r43-B3)Hs7r/))
remove N from position 1,Sr16rn]r43-B3)Hs7r/))
remove 1 from position 2,Sr6rn]r43-B3)Hs7r/))
remove ) from position 19,Sr6rn]r43-B3)Hs7r/)
replace 3 at position 11 with F,Sr6rn]r43-BF)Hs7r/)
remove ) from position 12,Sr6rn]r43-BFHs7r/)
remove r from position 1,S6rn]r43-BFHs7r/)
add l at position 2,S6lrn]r43-BFHs7r/)
remove B from position 10,S6lrn]r43-FHs7r/)
add S at position 7,S6lrn]rS43-FHs7r/)
replace S at position 0 with o,o6lrn]rS43-FHs7r/)
remove F from position 11,o6lrn]rS43-Hs7r/)
remove l from position 2,o6rn]rS43-Hs7r/)
remove S from position 6,o6rn]r43-Hs7r/)
replace / at position 13 with N,o6rn]r43-Hs7rN)
remove 7 from position 11,o6rn]r43-HsrN)
add O at position 2,o6Orn]r43-HsrN)
replace H at position 10 with ],o6Orn]r43-]srN)
add 6 at position 14,o6Orn]r43-]srN6)
remove s from position 11,o6Orn]r43-]rN6)
remove r from position 6,o6Orn]43-]rN6)
replace r at position 3 with 4,o6O4n]43-]rN6)
add H at position 5,o6O4nH]43-]rN6)
replace 6 at position 1 with H,oHO4nH]43-]rN6)
remove 6 from position 13,oHO4nH]43-]rN)
add H at position 9,oHO4nH]43H-]rN)
remove O from position 2,oH4nH]43H-]rN)
remove ] from position 5,oH4nH43H-]rN)
replace H at position 4 with [,oH4n[43H-]rN)
remove 4 from position 2,oHn[43H-]rN)
remove n from position 2,oH[43H-]rN)
replace r at position 8 with S,oH[43H-]SN)
remove 4 from position 3,oH[3H-]SN)
remove [ from position 2,oH3H-]SN)
remove S from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
