log,state
initialize: O=C(N[C@H]1CCCC[C@H]1OC1CCCC1)c1ccc([N+](=O)[O-])cc1,O=C(N[C@H]1CCCC[C@H]1OC1CCCC1)c1ccc([N+](=O)[O-])cc1
replace 1 at position 28 with +,O=C(N[C@H]1CCCC[C@H]1OC1CCCC+)c1ccc([N+](=O)[O-])cc1
add H at position 51,O=C(N[C@H]1CCCC[C@H]1OC1CCCC+)c1ccc([N+](=O)[O-])ccH1
add ) at position 44,O=C(N[C@H]1CCCC[C@H]1OC1CCCC+)c1ccc([N+](=O))[O-])ccH1
add 2 at position 22,O=C(N[C@H]1CCCC[C@H]1O2C1CCCC+)c1ccc([N+](=O))[O-])ccH1
add # at position 50,O=C(N[C@H]1CCCC[C@H]1O2C1CCCC+)c1ccc([N+](=O))[O-]#)ccH1
remove + from position 39,O=C(N[C@H]1CCCC[C@H]1O2C1CCCC+)c1ccc([N](=O))[O-]#)ccH1
remove + from position 29,O=C(N[C@H]1CCCC[C@H]1O2C1CCCC)c1ccc([N](=O))[O-]#)ccH1
add S at position 11,O=C(N[C@H]1SCCCC[C@H]1O2C1CCCC)c1ccc([N](=O))[O-]#)ccH1
add C at position 0,CO=C(N[C@H]1SCCCC[C@H]1O2C1CCCC)c1ccc([N](=O))[O-]#)ccH1
remove C from position 15,CO=C(N[C@H]1SCCC[C@H]1O2C1CCCC)c1ccc([N](=O))[O-]#)ccH1
add r at position 34,CO=C(N[C@H]1SCCC[C@H]1O2C1CCCC)c1crcc([N](=O))[O-]#)ccH1
remove [ from position 38,CO=C(N[C@H]1SCCC[C@H]1O2C1CCCC)c1crcc(N](=O))[O-]#)ccH1
remove 1 from position 32,CO=C(N[C@H]1SCCC[C@H]1O2C1CCCC)ccrcc(N](=O))[O-]#)ccH1
remove C from position 17,CO=C(N[C@H]1SCCC[@H]1O2C1CCCC)ccrcc(N](=O))[O-]#)ccH1
replace ( at position 4 with O,CO=CON[C@H]1SCCC[@H]1O2C1CCCC)ccrcc(N](=O))[O-]#)ccH1
remove 2 from position 22,CO=CON[C@H]1SCCC[@H]1OC1CCCC)ccrcc(N](=O))[O-]#)ccH1
remove ) from position 41,CO=CON[C@H]1SCCC[@H]1OC1CCCC)ccrcc(N](=O)[O-]#)ccH1
replace c at position 29 with r,CO=CON[C@H]1SCCC[@H]1OC1CCCC)rcrcc(N](=O)[O-]#)ccH1
remove O from position 1,C=CON[C@H]1SCCC[@H]1OC1CCCC)rcrcc(N](=O)[O-]#)ccH1
replace N at position 4 with #,C=CO#[C@H]1SCCC[@H]1OC1CCCC)rcrcc(N](=O)[O-]#)ccH1
remove ) from position 45,C=CO#[C@H]1SCCC[@H]1OC1CCCC)rcrcc(N](=O)[O-]#ccH1
remove C from position 24,C=CO#[C@H]1SCCC[@H]1OC1CCC)rcrcc(N](=O)[O-]#ccH1
add H at position 4,C=COH#[C@H]1SCCC[@H]1OC1CCC)rcrcc(N](=O)[O-]#ccH1
remove H from position 4,C=CO#[C@H]1SCCC[@H]1OC1CCC)rcrcc(N](=O)[O-]#ccH1
remove ] from position 9,C=CO#[C@H1SCCC[@H]1OC1CCC)rcrcc(N](=O)[O-]#ccH1
add ] at position 22,C=CO#[C@H1SCCC[@H]1OC1]CCC)rcrcc(N](=O)[O-]#ccH1
add - at position 33,C=CO#[C@H1SCCC[@H]1OC1]CCC)rcrcc(-N](=O)[O-]#ccH1
remove r from position 29,C=CO#[C@H1SCCC[@H]1OC1]CCC)rccc(-N](=O)[O-]#ccH1
add O at position 17,C=CO#[C@H1SCCC[@HO]1OC1]CCC)rccc(-N](=O)[O-]#ccH1
replace C at position 11 with 3,C=CO#[C@H1S3CC[@HO]1OC1]CCC)rccc(-N](=O)[O-]#ccH1
add ] at position 9,C=CO#[C@H]1S3CC[@HO]1OC1]CCC)rccc(-N](=O)[O-]#ccH1
replace C at position 2 with I,C=IO#[C@H]1S3CC[@HO]1OC1]CCC)rccc(-N](=O)[O-]#ccH1
remove C from position 22,C=IO#[C@H]1S3CC[@HO]1O1]CCC)rccc(-N](=O)[O-]#ccH1
remove = from position 37,C=IO#[C@H]1S3CC[@HO]1O1]CCC)rccc(-N](O)[O-]#ccH1
replace c at position 30 with (,C=IO#[C@H]1S3CC[@HO]1O1]CCC)rc(c(-N](O)[O-]#ccH1
replace ( at position 30 with o,C=IO#[C@H]1S3CC[@HO]1O1]CCC)rcoc(-N](O)[O-]#ccH1
replace 1 at position 10 with B,C=IO#[C@H]BS3CC[@HO]1O1]CCC)rcoc(-N](O)[O-]#ccH1
remove - from position 33,C=IO#[C@H]BS3CC[@HO]1O1]CCC)rcoc(N](O)[O-]#ccH1
replace O at position 18 with 3,C=IO#[C@H]BS3CC[@H3]1O1]CCC)rcoc(N](O)[O-]#ccH1
add = at position 11,C=IO#[C@H]B=S3CC[@H3]1O1]CCC)rcoc(N](O)[O-]#ccH1
remove @ from position 7,C=IO#[CH]B=S3CC[@H3]1O1]CCC)rcoc(N](O)[O-]#ccH1
replace C at position 6 with n,C=IO#[nH]B=S3CC[@H3]1O1]CCC)rcoc(N](O)[O-]#ccH1
replace C at position 24 with n,C=IO#[nH]B=S3CC[@H3]1O1]nCC)rcoc(N](O)[O-]#ccH1
replace @ at position 16 with O,C=IO#[nH]B=S3CC[OH3]1O1]nCC)rcoc(N](O)[O-]#ccH1
add S at position 6,C=IO#[SnH]B=S3CC[OH3]1O1]nCC)rcoc(N](O)[O-]#ccH1
remove B from position 10,C=IO#[SnH]=S3CC[OH3]1O1]nCC)rcoc(N](O)[O-]#ccH1
remove H from position 17,C=IO#[SnH]=S3CC[O3]1O1]nCC)rcoc(N](O)[O-]#ccH1
replace O at position 20 with l,C=IO#[SnH]=S3CC[O3]1l1]nCC)rcoc(N](O)[O-]#ccH1
replace # at position 4 with +,C=IO+[SnH]=S3CC[O3]1l1]nCC)rcoc(N](O)[O-]#ccH1
remove O from position 3,C=I+[SnH]=S3CC[O3]1l1]nCC)rcoc(N](O)[O-]#ccH1
replace I at position 2 with 2,C=2+[SnH]=S3CC[O3]1l1]nCC)rcoc(N](O)[O-]#ccH1
remove S from position 5,C=2+[nH]=S3CC[O3]1l1]nCC)rcoc(N](O)[O-]#ccH1
replace ] at position 20 with 4,C=2+[nH]=S3CC[O3]1l14nCC)rcoc(N](O)[O-]#ccH1
replace [ at position 4 with H,C=2+HnH]=S3CC[O3]1l14nCC)rcoc(N](O)[O-]#ccH1
remove H from position 4,C=2+nH]=S3CC[O3]1l14nCC)rcoc(N](O)[O-]#ccH1
remove [ from position 12,C=2+nH]=S3CCO3]1l14nCC)rcoc(N](O)[O-]#ccH1
add S at position 12,C=2+nH]=S3CCSO3]1l14nCC)rcoc(N](O)[O-]#ccH1
replace N at position 29 with C,C=2+nH]=S3CCSO3]1l14nCC)rcoc(C](O)[O-]#ccH1
add r at position 23,C=2+nH]=S3CCSO3]1l14nCCr)rcoc(C](O)[O-]#ccH1
replace ( at position 32 with r,C=2+nH]=S3CCSO3]1l14nCCr)rcoc(C]rO)[O-]#ccH1
remove C from position 11,C=2+nH]=S3CSO3]1l14nCCr)rcoc(C]rO)[O-]#ccH1
replace 3 at position 13 with 5,C=2+nH]=S3CSO5]1l14nCCr)rcoc(C]rO)[O-]#ccH1
add F at position 22,C=2+nH]=S3CSO5]1l14nCCFr)rcoc(C]rO)[O-]#ccH1
add B at position 35,C=2+nH]=S3CSO5]1l14nCCFr)rcoc(C]rO)B[O-]#ccH1
remove S from position 8,C=2+nH]=3CSO5]1l14nCCFr)rcoc(C]rO)B[O-]#ccH1
add r at position 10,C=2+nH]=3CrSO5]1l14nCCFr)rcoc(C]rO)B[O-]#ccH1
add ) at position 34,C=2+nH]=3CrSO5]1l14nCCFr)rcoc(C]rO))B[O-]#ccH1
remove r from position 10,C=2+nH]=3CSO5]1l14nCCFr)rcoc(C]rO))B[O-]#ccH1
add ( at position 43,C=2+nH]=3CSO5]1l14nCCFr)rcoc(C]rO))B[O-]#cc(H1
remove C from position 9,C=2+nH]=3SO5]1l14nCCFr)rcoc(C]rO))B[O-]#cc(H1
replace 2 at position 2 with @,C=@+nH]=3SO5]1l14nCCFr)rcoc(C]rO))B[O-]#cc(H1
remove F from position 20,C=@+nH]=3SO5]1l14nCCr)rcoc(C]rO))B[O-]#cc(H1
add 7 at position 43,C=@+nH]=3SO5]1l14nCCr)rcoc(C]rO))B[O-]#cc(H71
replace n at position 17 with F,C=@+nH]=3SO5]1l14FCCr)rcoc(C]rO))B[O-]#cc(H71
remove C from position 19,C=@+nH]=3SO5]1l14FCr)rcoc(C]rO))B[O-]#cc(H71
remove 1 from position 43,C=@+nH]=3SO5]1l14FCr)rcoc(C]rO))B[O-]#cc(H7
remove S from position 9,C=@+nH]=3O5]1l14FCr)rcoc(C]rO))B[O-]#cc(H7
replace H at position 5 with c,C=@+nc]=3O5]1l14FCr)rcoc(C]rO))B[O-]#cc(H7
remove ] from position 26,C=@+nc]=3O5]1l14FCr)rcoc(CrO))B[O-]#cc(H7
remove c from position 36,C=@+nc]=3O5]1l14FCr)rcoc(CrO))B[O-]#c(H7
remove = from position 7,C=@+nc]3O5]1l14FCr)rcoc(CrO))B[O-]#c(H7
remove r from position 17,C=@+nc]3O5]1l14FC)rcoc(CrO))B[O-]#c(H7
add 7 at position 37,C=@+nc]3O5]1l14FC)rcoc(CrO))B[O-]#c(H77
add 2 at position 28,C=@+nc]3O5]1l14FC)rcoc(CrO))2B[O-]#c(H77
replace ] at position 10 with 7,C=@+nc]3O571l14FC)rcoc(CrO))2B[O-]#c(H77
remove n from position 4,C=@+c]3O571l14FC)rcoc(CrO))2B[O-]#c(H77
remove o from position 19,C=@+c]3O571l14FC)rcc(CrO))2B[O-]#c(H77
remove - from position 30,C=@+c]3O571l14FC)rcc(CrO))2B[O]#c(H77
add / at position 30,C=@+c]3O571l14FC)rcc(CrO))2B[O/]#c(H77
remove O from position 23,C=@+c]3O571l14FC)rcc(Cr))2B[O/]#c(H77
remove r from position 22,C=@+c]3O571l14FC)rcc(C))2B[O/]#c(H77
add r at position 33,C=@+c]3O571l14FC)rcc(C))2B[O/]#c(rH77
remove # from position 30,C=@+c]3O571l14FC)rcc(C))2B[O/]c(rH77
remove ] from position 29,C=@+c]3O571l14FC)rcc(C))2B[O/c(rH77
remove 3 from position 6,C=@+c]O571l14FC)rcc(C))2B[O/c(rH77
add ) at position 30,C=@+c]O571l14FC)rcc(C))2B[O/c()rH77
replace ) at position 21 with 7,C=@+c]O571l14FC)rcc(C7)2B[O/c()rH77
replace c at position 18 with 4,C=@+c]O571l14FC)rc4(C7)2B[O/c()rH77
remove 7 from position 21,C=@+c]O571l14FC)rc4(C)2B[O/c()rH77
remove 4 from position 18,C=@+c]O571l14FC)rc(C)2B[O/c()rH77
remove 7 from position 32,C=@+c]O571l14FC)rc(C)2B[O/c()rH7
replace c at position 17 with ),C=@+c]O571l14FC)r)(C)2B[O/c()rH7
remove F from position 13,C=@+c]O571l14C)r)(C)2B[O/c()rH7
remove l from position 10,C=@+c]O57114C)r)(C)2B[O/c()rH7
replace r at position 14 with 3,C=@+c]O57114C)3)(C)2B[O/c()rH7
add H at position 16,C=@+c]O57114C)3)H(C)2B[O/c()rH7
remove + from position 3,C=@c]O57114C)3)H(C)2B[O/c()rH7
replace 1 at position 8 with 7,C=@c]O57714C)3)H(C)2B[O/c()rH7
add 7 at position 0,7C=@c]O57714C)3)H(C)2B[O/c()rH7
add 1 at position 2,7C1=@c]O57714C)3)H(C)2B[O/c()rH7
remove ) from position 20,7C1=@c]O57714C)3)H(C2B[O/c()rH7
remove C from position 13,7C1=@c]O57714)3)H(C2B[O/c()rH7
replace 7 at position 29 with ),7C1=@c]O57714)3)H(C2B[O/c()rH)
replace 7 at position 9 with 3,7C1=@c]O53714)3)H(C2B[O/c()rH)
remove ) from position 29,7C1=@c]O53714)3)H(C2B[O/c()rH
remove 1 from position 11,7C1=@c]O5374)3)H(C2B[O/c()rH
remove [ from position 20,7C1=@c]O5374)3)H(C2BO/c()rH
replace 7 at position 0 with S,SC1=@c]O5374)3)H(C2BO/c()rH
remove 2 from position 18,SC1=@c]O5374)3)H(CBO/c()rH
add B at position 15,SC1=@c]O5374)3)BH(CBO/c()rH
remove / from position 21,SC1=@c]O5374)3)BH(CBOc()rH
replace @ at position 4 with r,SC1=rc]O5374)3)BH(CBOc()rH
remove B from position 19,SC1=rc]O5374)3)BH(COc()rH
add s at position 17,SC1=rc]O5374)3)BHs(COc()rH
replace ( at position 22 with 1,SC1=rc]O5374)3)BHs(COc1)rH
replace r at position 24 with C,SC1=rc]O5374)3)BHs(COc1)CH
remove ) from position 12,SC1=rc]O53743)BHs(COc1)CH
replace 7 at position 10 with -,SC1=rc]O53-43)BHs(COc1)CH
remove ( from position 17,SC1=rc]O53-43)BHsCOc1)CH
remove c from position 19,SC1=rc]O53-43)BHsCO1)CH
remove B from position 14,SC1=rc]O53-43)HsCO1)CH
add r at position 2,SCr1=rc]O53-43)HsCO1)CH
remove C from position 21,SCr1=rc]O53-43)HsCO1)H
remove C from position 1,Sr1=rc]O53-43)HsCO1)H
remove 1 from position 2,Sr=rc]O53-43)HsCO1)H
remove H from position 19,Sr=rc]O53-43)HsCO1)
replace 3 at position 11 with F,Sr=rc]O53-4F)HsCO1)
remove ) from position 12,Sr=rc]O53-4FHsCO1)
remove r from position 1,S=rc]O53-4FHsCO1)
add l at position 2,S=lrc]O53-4FHsCO1)
remove 4 from position 10,S=lrc]O53-FHsCO1)
add S at position 7,S=lrc]OS53-FHsCO1)
replace S at position 0 with o,o=lrc]OS53-FHsCO1)
remove F from position 11,o=lrc]OS53-HsCO1)
remove l from position 2,o=rc]OS53-HsCO1)
remove S from position 6,o=rc]O53-HsCO1)
replace 1 at position 13 with N,o=rc]O53-HsCON)
remove C from position 11,o=rc]O53-HsON)
add O at position 2,o=Orc]O53-HsON)
replace H at position 10 with ],o=Orc]O53-]sON)
add 6 at position 14,o=Orc]O53-]sON6)
remove s from position 11,o=Orc]O53-]ON6)
remove O from position 6,o=Orc]53-]ON6)
replace r at position 3 with 4,o=O4c]53-]ON6)
add H at position 5,o=O4cH]53-]ON6)
replace = at position 1 with H,oHO4cH]53-]ON6)
remove 6 from position 13,oHO4cH]53-]ON)
add H at position 9,oHO4cH]53H-]ON)
remove O from position 2,oH4cH]53H-]ON)
remove ] from position 5,oH4cH53H-]ON)
replace H at position 4 with [,oH4c[53H-]ON)
remove 4 from position 2,oHc[53H-]ON)
remove c from position 2,oH[53H-]ON)
replace O at position 8 with [,oH[53H-][N)
remove 5 from position 3,oH[3H-][N)
remove [ from position 2,oH3H-][N)
remove [ from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
