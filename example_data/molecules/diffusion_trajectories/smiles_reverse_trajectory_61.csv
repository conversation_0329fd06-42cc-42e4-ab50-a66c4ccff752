log,state
initialize: ,
add N at position 0,N
add I at position 0,IN
add 1 at position 0,1IN
replace N at position 2 with B,1IB
remove 1 from position 0,IB
add = at position 0,=IB
replace I at position 1 with -,=-B
add B at position 0,B=-B
add C at position 1,BC=-B
replace <PERSON> at position 1 with 6,B6=-B
replace = at position 2 with n,B6n-B
replace <PERSON> at position 0 with 5,56n-<PERSON>
replace <PERSON> at position 4 with [,56n-[
replace [ at position 4 with 5,56n-5
remove 6 from position 1,5n-5
replace 5 at position 0 with r,rn-5
add - at position 3,rn--5
remove r from position 0,n--5
add ] at position 3,n--]5
add H at position 4,n--]H5
add ) at position 4,n--])H5
replace - at position 2 with /,n-/])H5
add ) at position 6,n-/])H)5
add ( at position 3,n-/(])H)5
replace - at position 1 with l,nl/(])H)5
add n at position 2,nln/(])H)5
replace / at position 3 with 4,nln4(])H)5
add H at position 5,nln4(H])H)5
add ] at position 0,]nln4(H])H)5
remove ) from position 8,]nln4(H]H)5
add - at position 9,]nln4(H]H-)5
replace 4 at position 4 with -,]nln-(H]H-)5
add 6 at position 8,]nln-(H]6H-)5
replace 6 at position 8 with s,]nln-(H]sH-)5
replace - at position 10 with c,]nln-(H]sHc)5
add C at position 0,C]nln-(H]sHc)5
replace ( at position 6 with 5,C]nln-5H]sHc)5
remove - from position 5,C]nln5H]sHc)5
add s at position 13,C]nln5H]sHc)5s
replace H at position 9 with r,C]nln5H]src)5s
add # at position 1,C#]nln5H]src)5s
add 4 at position 10,C#]nln5H]s4rc)5s
remove 5 from position 6,C#]nlnH]s4rc)5s
add H at position 8,C#]nlnH]Hs4rc)5s
add 4 at position 9,C#]nlnH]H4s4rc)5s
add 6 at position 10,C#]nlnH]H46s4rc)5s
replace 4 at position 9 with 5,C#]nlnH]H56s4rc)5s
replace 6 at position 10 with s,C#]nlnH]H5ss4rc)5s
remove ) from position 15,C#]nlnH]H5ss4rc5s
add o at position 3,C#]onlnH]H5ss4rc5s
add 7 at position 14,C#]onlnH]H5ss47rc5s
add l at position 15,C#]onlnH]H5ss47lrc5s
remove r from position 16,C#]onlnH]H5ss47lc5s
add 2 at position 11,C#]onlnH]H52ss47lc5s
add ] at position 11,C#]onlnH]H5]2ss47lc5s
add 7 at position 5,C#]on7lnH]H5]2ss47lc5s
replace 7 at position 5 with C,C#]onClnH]H5]2ss47lc5s
add S at position 21,C#]onClnH]H5]2ss47lc5Ss
add F at position 9,C#]onClnHF]H5]2ss47lc5Ss
add I at position 2,C#I]onClnHF]H5]2ss47lc5Ss
add s at position 12,C#I]onClnHF]sH5]2ss47lc5Ss
add ( at position 24,C#I]onClnHF]sH5]2ss47lc5(Ss
add ] at position 26,C#I]onClnHF]sH5]2ss47lc5(S]s
add c at position 1,Cc#I]onClnHF]sH5]2ss47lc5(S]s
remove l from position 22,Cc#I]onClnHF]sH5]2ss47c5(S]s
add O at position 8,Cc#I]onCOlnHF]sH5]2ss47c5(S]s
add N at position 3,Cc#NI]onCOlnHF]sH5]2ss47c5(S]s
add ] at position 18,Cc#NI]onCOlnHF]sH5]]2ss47c5(S]s
add 1 at position 26,Cc#NI]onCOlnHF]sH5]]2ss47c15(S]s
replace ] at position 5 with n,Cc#NInonCOlnHF]sH5]]2ss47c15(S]s
add r at position 9,Cc#NInonCrOlnHF]sH5]]2ss47c15(S]s
add l at position 19,Cc#NInonCrOlnHF]sH5l]]2ss47c15(S]s
replace H at position 17 with 1,Cc#NInonCrOlnHF]s15l]]2ss47c15(S]s
replace s at position 24 with ),Cc#NInonCrOlnHF]s15l]]2s)47c15(S]s
add c at position 13,Cc#NInonCrOlncHF]s15l]]2s)47c15(S]s
remove 7 from position 27,Cc#NInonCrOlncHF]s15l]]2s)4c15(S]s
remove r from position 9,Cc#NInonCOlncHF]s15l]]2s)4c15(S]s
remove N from position 3,Cc#InonCOlncHF]s15l]]2s)4c15(S]s
add + at position 20,Cc#InonCOlncHF]s15l]+]2s)4c15(S]s
remove l from position 9,Cc#InonCOncHF]s15l]+]2s)4c15(S]s
remove I from position 3,Cc#nonCOncHF]s15l]+]2s)4c15(S]s
replace 5 at position 15 with =,Cc#nonCOncHF]s1=l]+]2s)4c15(S]s
remove ] from position 29,Cc#nonCOncHF]s1=l]+]2s)4c15(Ss
remove F from position 11,Cc#nonCOncH]s1=l]+]2s)4c15(Ss
add C at position 9,Cc#nonCOnCcH]s1=l]+]2s)4c15(Ss
add o at position 30,Cc#nonCOnCcH]s1=l]+]2s)4c15(Sso
remove S from position 28,Cc#nonCOnCcH]s1=l]+]2s)4c15(so
replace o at position 29 with n,Cc#nonCOnCcH]s1=l]+]2s)4c15(sn
add c at position 26,Cc#nonCOnCcH]s1=l]+]2s)4c1c5(sn
add / at position 29,Cc#nonCOnCcH]s1=l]+]2s)4c1c5(/sn
remove 1 from position 25,Cc#nonCOnCcH]s1=l]+]2s)4cc5(/sn
remove s from position 13,Cc#nonCOnCcH]1=l]+]2s)4cc5(/sn
remove n from position 8,Cc#nonCOCcH]1=l]+]2s)4cc5(/sn
add - at position 14,Cc#nonCOCcH]1=-l]+]2s)4cc5(/sn
add [ at position 10,Cc#nonCOCc[H]1=-l]+]2s)4cc5(/sn
remove s from position 29,Cc#nonCOCc[H]1=-l]+]2s)4cc5(/n
remove l from position 16,Cc#nonCOCc[H]1=-]+]2s)4cc5(/n
remove c from position 9,Cc#nonCOC[H]1=-]+]2s)4cc5(/n
remove 1 from position 12,Cc#nonCOC[H]=-]+]2s)4cc5(/n
replace ] at position 14 with 7,Cc#nonCOC[H]=-7+]2s)4cc5(/n
replace 5 at position 23 with s,Cc#nonCOC[H]=-7+]2s)4ccs(/n
replace o at position 4 with 3,Cc#n3nCOC[H]=-7+]2s)4ccs(/n
replace s at position 18 with N,Cc#n3nCOC[H]=-7+]2N)4ccs(/n
remove c from position 22,Cc#n3nCOC[H]=-7+]2N)4cs(/n
add ] at position 11,Cc#n3nCOC[H]]=-7+]2N)4cs(/n
replace 4 at position 21 with ),Cc#n3nCOC[H]]=-7+]2N))cs(/n
replace 7 at position 15 with [,Cc#n3nCOC[H]]=-[+]2N))cs(/n
replace + at position 16 with @,Cc#n3nCOC[H]]=-[@]2N))cs(/n
replace = at position 13 with o,Cc#n3nCOC[H]]o-[@]2N))cs(/n
add N at position 3,Cc#Nn3nCOC[H]]o-[@]2N))cs(/n
add 2 at position 14,Cc#Nn3nCOC[H]]2o-[@]2N))cs(/n
add o at position 24,Cc#Nn3nCOC[H]]2o-[@]2N))ocs(/n
replace / at position 28 with 1,Cc#Nn3nCOC[H]]2o-[@]2N))ocs(1n
replace o at position 15 with (,Cc#Nn3nCOC[H]]2(-[@]2N))ocs(1n
replace ( at position 15 with C,Cc#Nn3nCOC[H]]2C-[@]2N))ocs(1n
add C at position 18,Cc#Nn3nCOC[H]]2C-[C@]2N))ocs(1n
add ( at position 22,Cc#Nn3nCOC[H]]2C-[C@]2(N))ocs(1n
remove ( from position 29,Cc#Nn3nCOC[H]]2C-[C@]2(N))ocs1n
remove n from position 30,Cc#Nn3nCOC[H]]2C-[C@]2(N))ocs1
replace 3 at position 5 with c,Cc#NncnCOC[H]]2C-[C@]2(N))ocs1
remove O from position 8,Cc#NncnCC[H]]2C-[C@]2(N))ocs1
add C at position 14,Cc#NncnCC[H]]2CC-[C@]2(N))ocs1
remove - from position 16,Cc#NncnCC[H]]2CC[C@]2(N))ocs1
remove ] from position 11,Cc#NncnCC[H]2CC[C@]2(N))ocs1
add C at position 4,Cc#NCncnCC[H]2CC[C@]2(N))ocs1
add H at position 2,CcH#NCncnCC[H]2CC[C@]2(N))ocs1
remove H from position 2,Cc#NCncnCC[H]2CC[C@]2(N))ocs1
add + at position 12,Cc#NCncnCC[H+]2CC[C@]2(N))ocs1
add C at position 22,Cc#NCncnCC[H+]2CC[C@]2C(N))ocs1
replace # at position 2 with O,CcONCncnCC[H+]2CC[C@]2C(N))ocs1
add 6 at position 1,C6cONCncnCC[H+]2CC[C@]2C(N))ocs1
add N at position 12,C6cONCncnCC[NH+]2CC[C@]2C(N))ocs1
add H at position 22,C6cONCncnCC[NH+]2CC[C@H]2C(N))ocs1
replace N at position 4 with S,C6cOSCncnCC[NH+]2CC[C@H]2C(N))ocs1
add # at position 17,C6cOSCncnCC[NH+]2#CC[C@H]2C(N))ocs1
remove C from position 5,C6cOSncnCC[NH+]2#CC[C@H]2C(N))ocs1
add + at position 29,C6cOSncnCC[NH+]2#CC[C@H]2C(N)+)ocs1
replace S at position 4 with 1,C6cO1ncnCC[NH+]2#CC[C@H]2C(N)+)ocs1
remove o from position 31,C6cO1ncnCC[NH+]2#CC[C@H]2C(N)+)cs1
remove n from position 7,C6cO1ncCC[NH+]2#CC[C@H]2C(N)+)cs1
add C at position 9,C6cO1ncCCC[NH+]2#CC[C@H]2C(N)+)cs1
add O at position 30,C6cO1ncCCC[NH+]2#CC[C@H]2C(N)+O)cs1
remove 6 from position 1,CcO1ncCCC[NH+]2#CC[C@H]2C(N)+O)cs1
replace # at position 15 with C,CcO1ncCCC[NH+]2CCC[C@H]2C(N)+O)cs1
add ( at position 6,CcO1nc(CCC[NH+]2CCC[C@H]2C(N)+O)cs1
remove O from position 2,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)+O)cs1
replace + at position 28 with =,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)cs1
final: Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)cs1,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)cs1
