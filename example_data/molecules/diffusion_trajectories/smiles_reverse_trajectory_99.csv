log,state
initialize: ,
add N at position 0,N
add I at position 0,IN
add 1 at position 0,1IN
replace N at position 2 with B,1IB
remove 1 from position 0,IB
add @ at position 0,@IB
replace I at position 1 with -,@-B
add B at position 0,B@-B
add C at position 1,BC@-B
replace <PERSON> at position 1 with 6,B6@-B
replace @ at position 2 with +,B6+-<PERSON>
replace <PERSON> at position 0 with 5,56+-<PERSON>
replace <PERSON> at position 4 with [,56+-[
replace [ at position 4 with 5,56+-5
remove 6 from position 1,5+-5
replace 5 at position 0 with r,r+-5
add - at position 3,r+--5
remove r from position 0,+--5
add C at position 3,+--C5
add H at position 4,+--CH5
add ) at position 4,+--C)H5
replace - at position 2 with /,+-/C)H5
add ) at position 6,+-/C)H)5
add ( at position 3,+-/(C)H)5
replace - at position 1 with l,+l/(C)H)5
add n at position 2,+ln/(C)H)5
replace / at position 3 with 4,+ln4(C)H)5
add ] at position 5,+ln4(]C)H)5
add c at position 0,c+ln4(]C)H)5
remove ) from position 8,c+ln4(]CH)5
add - at position 9,c+ln4(]CH-)5
replace 4 at position 4 with -,c+ln-(]CH-)5
add 6 at position 8,c+ln-(]C6H-)5
replace 6 at position 8 with s,c+ln-(]CsH-)5
replace - at position 10 with o,c+ln-(]CsHo)5
add C at position 0,Cc+ln-(]CsHo)5
replace ( at position 6 with 5,Cc+ln-5]CsHo)5
remove - from position 5,Cc+ln5]CsHo)5
add s at position 13,Cc+ln5]CsHo)5s
replace H at position 9 with r,Cc+ln5]Csro)5s
add N at position 1,CNc+ln5]Csro)5s
add 3 at position 10,CNc+ln5]Cs3ro)5s
remove 5 from position 6,CNc+ln]Cs3ro)5s
add H at position 8,CNc+ln]CHs3ro)5s
add 4 at position 9,CNc+ln]CH4s3ro)5s
add 6 at position 10,CNc+ln]CH46s3ro)5s
replace 4 at position 9 with 5,CNc+ln]CH56s3ro)5s
replace 6 at position 10 with s,CNc+ln]CH5ss3ro)5s
remove ) from position 15,CNc+ln]CH5ss3ro5s
add o at position 3,CNco+ln]CH5ss3ro5s
add 7 at position 14,CNco+ln]CH5ss37ro5s
add l at position 15,CNco+ln]CH5ss37lro5s
remove r from position 16,CNco+ln]CH5ss37lo5s
add O at position 11,CNco+ln]CH5Oss37lo5s
add C at position 11,CNco+ln]CH5COss37lo5s
add 7 at position 5,CNco+7ln]CH5COss37lo5s
replace 7 at position 5 with n,CNco+nln]CH5COss37lo5s
add S at position 21,CNco+nln]CH5COss37lo5Ss
add F at position 9,CNco+nln]FCH5COss37lo5Ss
add I at position 2,CNIco+nln]FCH5COss37lo5Ss
add s at position 12,CNIco+nln]FCsH5COss37lo5Ss
add ( at position 24,CNIco+nln]FCsH5COss37lo5(Ss
add ] at position 26,CNIco+nln]FCsH5COss37lo5(S]s
add [ at position 1,C[NIco+nln]FCsH5COss37lo5(S]s
remove l from position 22,C[NIco+nln]FCsH5COss37o5(S]s
add O at position 8,C[NIco+nOln]FCsH5COss37o5(S]s
add N at position 3,C[NNIco+nOln]FCsH5COss37o5(S]s
add ] at position 18,C[NNIco+nOln]FCsH5]COss37o5(S]s
add 1 at position 26,C[NNIco+nOln]FCsH5]COss37o15(S]s
replace c at position 5 with H,C[NNIHo+nOln]FCsH5]COss37o15(S]s
add r at position 9,C[NNIHo+nrOln]FCsH5]COss37o15(S]s
add l at position 19,C[NNIHo+nrOln]FCsH5l]COss37o15(S]s
replace H at position 17 with 1,C[NNIHo+nrOln]FCs15l]COss37o15(S]s
replace s at position 24 with C,C[NNIHo+nrOln]FCs15l]COsC37o15(S]s
add c at position 13,C[NNIHo+nrOlnc]FCs15l]COsC37o15(S]s
remove 7 from position 27,C[NNIHo+nrOlnc]FCs15l]COsC3o15(S]s
remove r from position 9,C[NNIHo+nOlnc]FCs15l]COsC3o15(S]s
remove N from position 3,C[NIHo+nOlnc]FCs15l]COsC3o15(S]s
add + at position 20,C[NIHo+nOlnc]FCs15l]+COsC3o15(S]s
remove l from position 9,C[NIHo+nOnc]FCs15l]+COsC3o15(S]s
remove I from position 3,C[NHo+nOnc]FCs15l]+COsC3o15(S]s
replace 5 at position 15 with =,C[NHo+nOnc]FCs1=l]+COsC3o15(S]s
remove ] from position 29,C[NHo+nOnc]FCs1=l]+COsC3o15(Ss
remove F from position 11,C[NHo+nOnc]Cs1=l]+COsC3o15(Ss
add ( at position 9,C[NHo+nOn(c]Cs1=l]+COsC3o15(Ss
add o at position 30,C[NHo+nOn(c]Cs1=l]+COsC3o15(Sso
remove S from position 28,C[NHo+nOn(c]Cs1=l]+COsC3o15(so
replace o at position 29 with n,C[NHo+nOn(c]Cs1=l]+COsC3o15(sn
add c at position 26,C[NHo+nOn(c]Cs1=l]+COsC3o1c5(sn
add / at position 29,C[NHo+nOn(c]Cs1=l]+COsC3o1c5(/sn
remove 1 from position 25,C[NHo+nOn(c]Cs1=l]+COsC3oc5(/sn
remove s from position 13,C[NHo+nOn(c]C1=l]+COsC3oc5(/sn
remove n from position 8,C[NHo+nO(c]C1=l]+COsC3oc5(/sn
add 1 at position 14,C[NHo+nO(c]C1=1l]+COsC3oc5(/sn
add - at position 10,C[NHo+nO(c-]C1=1l]+COsC3oc5(/sn
remove s from position 29,C[NHo+nO(c-]C1=1l]+COsC3oc5(/n
remove l from position 16,C[NHo+nO(c-]C1=1]+COsC3oc5(/n
remove c from position 9,C[NHo+nO(-]C1=1]+COsC3oc5(/n
remove 1 from position 12,C[NHo+nO(-]C=1]+COsC3oc5(/n
replace ] at position 14 with =,C[NHo+nO(-]C=1=+COsC3oc5(/n
replace 5 at position 23 with n,C[NHo+nO(-]C=1=+COsC3ocn(/n
replace o at position 4 with 4,C[NH4+nO(-]C=1=+COsC3ocn(/n
replace s at position 18 with 2,C[NH4+nO(-]C=1=+CO2C3ocn(/n
remove c from position 22,C[NH4+nO(-]C=1=+CO2C3on(/n
add C at position 11,C[NH4+nO(-]CC=1=+CO2C3on(/n
replace 3 at position 21 with C,C[NH4+nO(-]CC=1=+CO2CCon(/n
replace = at position 15 with /,C[NH4+nO(-]CC=1/+CO2CCon(/n
replace + at position 16 with N,C[NH4+nO(-]CC=1/NCO2CCon(/n
replace = at position 13 with o,C[NH4+nO(-]CCo1/NCO2CCon(/n
add C at position 3,C[NCH4+nO(-]CCo1/NCO2CCon(/n
add S at position 14,C[NCH4+nO(-]CCSo1/NCO2CCon(/n
add n at position 24,C[NCH4+nO(-]CCSo1/NCO2CCnon(/n
replace / at position 28 with 1,C[NCH4+nO(-]CCSo1/NCO2CCnon(1n
replace o at position 15 with (,C[NCH4+nO(-]CCS(1/NCO2CCnon(1n
replace ( at position 15 with c,C[NCH4+nO(-]CCSc1/NCO2CCnon(1n
add c at position 18,C[NCH4+nO(-]CCSc1/cNCO2CCnon(1n
add C at position 22,C[NCH4+nO(-]CCSc1/cNCOC2CCnon(1n
remove ( from position 29,C[NCH4+nO(-]CCSc1/cNCOC2CCnon1n
remove n from position 30,C[NCH4+nO(-]CCSc1/cNCOC2CCnon1
replace 4 at position 5 with -,C[NCH-+nO(-]CCSc1/cNCOC2CCnon1
add ( at position 21,C[NCH-+nO(-]CCSc1/cNC(OC2CCnon1
add O at position 2,C[ONCH-+nO(-]CCSc1/cNC(OC2CCnon1
remove O from position 9,C[ONCH-+n(-]CCSc1/cNC(OC2CCnon1
remove ] from position 11,C[ONCH-+n(-CCSc1/cNC(OC2CCnon1
remove - from position 6,C[ONCH+n(-CCSc1/cNC(OC2CCnon1
remove / from position 15,C[ONCH+n(-CCSc1cNC(OC2CCnon1
add 6 at position 1,C6[ONCH+n(-CCSc1cNC(OC2CCnon1
add ) at position 21,C6[ONCH+n(-CCSc1cNC(O)C2CCnon1
remove - from position 10,C6[ONCH+n(CCSc1cNC(O)C2CCnon1
add ( at position 16,C6[ONCH+n(CCSc1c(NC(O)C2CCnon1
add c at position 16,C6[ONCH+n(CCSc1cc(NC(O)C2CCnon1
add C at position 12,C6[ONCH+n(CCCSc1cc(NC(O)C2CCnon1
add = at position 22,C6[ONCH+n(CCCSc1cc(NC(=O)C2CCnon1
replace N at position 4 with S,C6[OSCH+n(CCCSc1cc(NC(=O)C2CCnon1
add # at position 17,C6[OSCH+n(CCCSc1c#c(NC(=O)C2CCnon1
remove C from position 5,C6[OSH+n(CCCSc1c#c(NC(=O)C2CCnon1
add + at position 29,C6[OSH+n(CCCSc1c#c(NC(=O)C2CC+non1
replace S at position 4 with N,C6[ONH+n(CCCSc1c#c(NC(=O)C2CC+non1
remove o from position 31,C6[ONH+n(CCCSc1c#c(NC(=O)C2CC+nn1
remove n from position 7,C6[ONH+(CCCSc1c#c(NC(=O)C2CC+nn1
add ) at position 9,C6[ONH+(C)CCSc1c#c(NC(=O)C2CC+nn1
add ) at position 30,C6[ONH+(C)CCSc1c#c(NC(=O)C2CC+)nn1
remove 6 from position 1,C[ONH+(C)CCSc1c#c(NC(=O)C2CC+)nn1
replace # at position 15 with c,C[ONH+(C)CCSc1ccc(NC(=O)C2CC+)nn1
add ] at position 6,C[ONH+](C)CCSc1ccc(NC(=O)C2CC+)nn1
remove O from position 2,C[NH+](C)CCSc1ccc(NC(=O)C2CC+)nn1
replace + at position 28 with 2,C[NH+](C)CCSc1ccc(NC(=O)C2CC2)nn1
final: C[NH+](C)CCSc1ccc(NC(=O)C2CC2)nn1,C[NH+](C)CCSc1ccc(NC(=O)C2CC2)nn1
