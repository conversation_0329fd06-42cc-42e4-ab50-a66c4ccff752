log,state
initialize: ,
add 1 at position 0,1
add C at position 1,1C
add N at position 2,1<PERSON><PERSON>
add ) at position 3,1CN)
replace N at position 2 with r,1Cr)
replace <PERSON> at position 1 with o,1or)
remove 1 from position 0,or)
add S at position 1,oSr)
add C at position 4,oSr)C
replace o at position 0 with l,lSr)C
remove S from position 1,lr)C
add / at position 2,lr/)C
remove l from position 0,r/)C
add r at position 0,rr/)C
add - at position 3,rr/-)C
add 3 at position 4,rr/-3)C
add S at position 3,rr/S-3)C
add 1 at position 1,r1r/S-3)C
add 7 at position 0,7r1r/S-3)C
add [ at position 10,7r1r/S-3)C[
remove r from position 1,71r/S-3)C[
add s at position 7,71r/S-3s)C[
add / at position 9,71r/S-3s)/C[
add ] at position 8,71r/S-3s])/C[
replace - at position 5 with @,71r/S@3s])/C[
add ) at position 6,71r/S@)3s])/C[
replace <PERSON> at position 12 with H,71r/S@)3s])/H[
replace / at position 11 with 3,71r/S@)3s])3H[
remove s from position 8,71r/S@)3])3H[
add 5 at position 9,71r/S@)3]5)3H[
replace r at position 2 with N,71N/S@)3]5)3H[
add S at position 10,71N/S@)3]5S)3H[
replace / at position 3 with I,71NIS@)3]5S)3H[
remove 5 from position 9,71NIS@)3]S)3H[
add F at position 12,71NIS@)3]S)3FH[
add o at position 2,71oNIS@)3]S)3FH[
add 3 at position 7,71oNIS@3)3]S)3FH[
replace 3 at position 9 with N,71oNIS@3)N]S)3FH[
replace o at position 2 with 7,717NIS@3)N]S)3FH[
add c at position 13,717NIS@3)N]S)c3FH[
add 7 at position 10,717NIS@3)N7]S)c3FH[
remove 1 from position 1,77NIS@3)N7]S)c3FH[
remove 7 from position 0,7NIS@3)N7]S)c3FH[
replace 7 at position 8 with 2,7NIS@3)N2]S)c3FH[
add 7 at position 3,7NI7S@3)N2]S)c3FH[
remove H from position 16,7NI7S@3)N2]S)c3F[
replace 3 at position 14 with 5,7NI7S@3)N2]S)c5F[
add s at position 10,7NI7S@3)N2s]S)c5F[
add n at position 6,7NI7S@n3)N2s]S)c5F[
replace ) at position 8 with N,7NI7S@n3NN2s]S)c5F[
add / at position 16,7NI7S@n3NN2s]S)c/5F[
add 5 at position 9,7NI7S@n3N5N2s]S)c/5F[
add 5 at position 18,7NI7S@n3N5N2s]S)c/55F[
replace 5 at position 9 with (,7NI7S@n3N(N2s]S)c/55F[
replace 5 at position 18 with n,7NI7S@n3N(N2s]S)c/n5F[
replace ) at position 15 with r,7NI7S@n3N(N2s]Src/n5F[
replace 7 at position 0 with 6,6NI7S@n3N(N2s]Src/n5F[
replace S at position 14 with 7,6NI7S@n3N(N2s]7rc/n5F[
add C at position 0,C6NI7S@n3N(N2s]7rc/n5F[
add - at position 21,C6NI7S@n3N(N2s]7rc/n5-F[
remove r from position 16,C6NI7S@n3N(N2s]7c/n5-F[
add H at position 11,C6NI7S@n3N(HN2s]7c/n5-F[
add 2 at position 11,C6NI7S@n3N(2HN2s]7c/n5-F[
add 7 at position 5,C6NI77S@n3N(2HN2s]7c/n5-F[
replace 7 at position 5 with N,C6NI7NS@n3N(2HN2s]7c/n5-F[
add s at position 21,C6NI7NS@n3N(2HN2s]7c/sn5-F[
add l at position 9,C6NI7NS@nl3N(2HN2s]7c/sn5-F[
add 2 at position 2,C62NI7NS@nl3N(2HN2s]7c/sn5-F[
replace 7 at position 5 with c,C62NIcNS@nl3N(2HN2s]7c/sn5-F[
replace 2 at position 14 with 4,C62NIcNS@nl3N(4HN2s]7c/sn5-F[
add ] at position 11,C62NIcNS@nl]3N(4HN2s]7c/sn5-F[
add N at position 18,C62NIcNS@nl]3N(4HNN2s]7c/sn5-F[
add C at position 19,C62NIcNS@nl]3N(4HNNC2s]7c/sn5-F[
add + at position 24,C62NIcNS@nl]3N(4HNNC2s]7+c/sn5-F[
add = at position 17,C62NIcNS@nl]3N(4H=NNC2s]7+c/sn5-F[
add 1 at position 26,C62NIcNS@nl]3N(4H=NNC2s]7+1c/sn5-F[
replace c at position 5 with [,C62NI[NS@nl]3N(4H=NNC2s]7+1c/sn5-F[
add r at position 9,C62NI[NS@rnl]3N(4H=NNC2s]7+1c/sn5-F[
add n at position 19,C62NI[NS@rnl]3N(4H=nNNC2s]7+1c/sn5-F[
replace H at position 17 with C,C62NI[NS@rnl]3N(4C=nNNC2s]7+1c/sn5-F[
replace s at position 24 with F,C62NI[NS@rnl]3N(4C=nNNC2F]7+1c/sn5-F[
add @ at position 13,C62NI[NS@rnl]@3N(4C=nNNC2F]7+1c/sn5-F[
remove 7 from position 27,C62NI[NS@rnl]@3N(4C=nNNC2F]+1c/sn5-F[
remove r from position 9,C62NI[NS@nl]@3N(4C=nNNC2F]+1c/sn5-F[
remove N from position 3,C62I[NS@nl]@3N(4C=nNNC2F]+1c/sn5-F[
add ) at position 20,C62I[NS@nl]@3N(4C=nN)NC2F]+1c/sn5-F[
remove l from position 9,C62I[NS@n]@3N(4C=nN)NC2F]+1c/sn5-F[
remove I from position 3,C62[NS@n]@3N(4C=nN)NC2F]+1c/sn5-F[
remove 5 from position 30,C62[NS@n]@3N(4C=nN)NC2F]+1c/sn-F[
remove F from position 22,C62[NS@n]@3N(4C=nN)NC2]+1c/sn-F[
replace 4 at position 13 with F,C62[NS@n]@3N(FC=nN)NC2]+1c/sn-F[
replace / at position 26 with 1,C62[NS@n]@3N(FC=nN)NC2]+1c1sn-F[
replace F at position 13 with @,C62[NS@n]@3N(@C=nN)NC2]+1c1sn-F[
add = at position 23,C62[NS@n]@3N(@C=nN)NC2]=+1c1sn-F[
add C at position 29,C62[NS@n]@3N(@C=nN)NC2]=+1c1sCn-F[
remove 1 from position 25,C62[NS@n]@3N(@C=nN)NC2]=+c1sCn-F[
remove s from position 27,C62[NS@n]@3N(@C=nN)NC2]=+c1Cn-F[
remove n from position 16,C62[NS@n]@3N(@C=N)NC2]=+c1Cn-F[
replace @ at position 9 with l,C62[NS@n]l3N(@C=N)NC2]=+c1Cn-F[
replace [ at position 30 with 7,C62[NS@n]l3N(@C=N)NC2]=+c1Cn-F7
add C at position 5,C62[NCS@n]l3N(@C=N)NC2]=+c1Cn-F7
replace 2 at position 2 with I,C6I[NCS@n]l3N(@C=N)NC2]=+c1Cn-F7
remove ( from position 13,C6I[NCS@n]l3N@C=N)NC2]=+c1Cn-F7
replace + at position 23 with ),C6I[NCS@n]l3N@C=N)NC2]=)c1Cn-F7
replace l at position 10 with C,C6I[NCS@n]C3N@C=N)NC2]=)c1Cn-F7
replace n at position 27 with 1,C6I[NCS@n]C3N@C=N)NC2]=)c1C1-F7
add O at position 17,C6I[NCS@n]C3N@C=NO)NC2]=)c1C1-F7
add B at position 10,C6I[NCS@n]BC3N@C=NO)NC2]=)c1C1-F7
remove S from position 6,C6I[NC@n]BC3N@C=NO)NC2]=)c1C1-F7
replace N at position 16 with S,C6I[NC@n]BC3N@C=SO)NC2]=)c1C1-F7
remove 7 from position 31,C6I[NC@n]BC3N@C=SO)NC2]=)c1C1-F
replace F at position 30 with (,C6I[NC@n]BC3N@C=SO)NC2]=)c1C1-(
replace @ at position 13 with 1,C6I[NC@n]BC3N1C=SO)NC2]=)c1C1-(
add H at position 7,C6I[NC@Hn]BC3N1C=SO)NC2]=)c1C1-(
add c at position 28,C6I[NC@Hn]BC3N1C=SO)NC2]=)c1cC1-(
replace B at position 10 with 1,C6I[NC@Hn]1C3N1C=SO)NC2]=)c1cC1-(
add ) at position 30,C6I[NC@Hn]1C3N1C=SO)NC2]=)c1cC)1-(
add o at position 29,C6I[NC@Hn]1C3N1C=SO)NC2]=)c1coC)1-(
add - at position 22,C6I[NC@Hn]1C3N1C=SO)NC-2]=)c1coC)1-(
replace I at position 2 with C,C6C[NC@Hn]1C3N1C=SO)NC-2]=)c1coC)1-(
remove ] from position 9,C6C[NC@Hn1C3N1C=SO)NC-2]=)c1coC)1-(
replace 3 at position 11 with C,C6C[NC@Hn1CCN1C=SO)NC-2]=)c1coC)1-(
remove O from position 17,C6C[NC@Hn1CCN1C=S)NC-2]=)c1coC)1-(
add ( at position 29,C6C[NC@Hn1CCN1C=S)NC-2]=)c1co(C)1-(
remove - from position 33,C6C[NC@Hn1CCN1C=S)NC-2]=)c1co(C)1(
remove ] from position 22,C6C[NC@Hn1CCN1C=S)NC-2=)c1co(C)1(
add ] at position 9,C6C[NC@Hn]1CCN1C=S)NC-2=)c1co(C)1(
add H at position 4,C6C[HNC@Hn]1CCN1C=S)NC-2=)c1co(C)1(
remove H from position 4,C6C[NC@Hn]1CCN1C=S)NC-2=)c1co(C)1(
add O at position 24,C6C[NC@Hn]1CCN1C=S)NC-2=O)c1co(C)1(
remove - from position 21,C6C[NC@Hn]1CCN1C=S)NC2=O)c1co(C)1(
add c at position 32,C6C[NC@Hn]1CCN1C=S)NC2=O)c1co(C)c1(
add c at position 33,C6C[NC@Hn]1CCN1C=S)NC2=O)c1co(C)cc1(
add C at position 12,C6C[NC@Hn]1CCCN1C=S)NC2=O)c1co(C)cc1(
add ( at position 22,C6C[NC@Hn]1CCCN1C=S)NC(2=O)c1co(C)cc1(
replace N at position 4 with S,C6C[SC@Hn]1CCCN1C=S)NC(2=O)c1co(C)cc1(
add ( at position 17,C6C[SC@Hn]1CCCN1C(=S)NC(2=O)c1co(C)cc1(
remove C from position 5,C6C[S@Hn]1CCCN1C(=S)NC(2=O)c1co(C)cc1(
add + at position 29,C6C[S@Hn]1CCCN1C(=S)NC(2=O)c1+co(C)cc1(
replace S at position 4 with C,C6C[C@Hn]1CCCN1C(=S)NC(2=O)c1+co(C)cc1(
remove o from position 31,C6C[C@Hn]1CCCN1C(=S)NC(2=O)c1+c(C)cc1(
remove n from position 7,C6C[C@H]1CCCN1C(=S)NC(2=O)c1+c(C)cc1(
add C at position 9,C6C[C@H]1CCCCN1C(=S)NC(2=O)c1+c(C)cc1(
add c at position 30,C6C[C@H]1CCCCN1C(=S)NC(2=O)c1+cc(C)cc1(
remove 6 from position 1,CC[C@H]1CCCCN1C(=S)NC(2=O)c1+cc(C)cc1(
remove 2 from position 22,CC[C@H]1CCCCN1C(=S)NC(=O)c1+cc(C)cc1(
add @ at position 5,CC[C@@H]1CCCCN1C(=S)NC(=O)c1+cc(C)cc1(
remove ( from position 37,CC[C@@H]1CCCCN1C(=S)NC(=O)c1+cc(C)cc1
replace + at position 28 with c,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C)cc1
final: CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C)cc1,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C)cc1
