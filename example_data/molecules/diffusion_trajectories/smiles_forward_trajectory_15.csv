log,state
initialize: C[C@H](CNC(=O)[C@H]1CC[NH2+][C@@H]1C)C[NH+]1CCCC1,C[C@H](CNC(=O)[C@H]1CC[NH2+][C@@H]1C)C[NH+]1CCCC1
replace [ at position 28 with +,C[C@H](CNC(=O)[C@H]1CC[NH2+]+C@@H]1C)C[NH+]1CCCC1
add ( at position 37,C[C@H](CNC(=O)[C@H]1CC[NH2+]+C@@H]1C)(C[NH+]1CCCC1
remove ] from position 5,C[C@H(CNC(=O)[C@H]1CC[NH2+]+C@@H]1C)(C[NH+]1CCCC1
add 2 at position 22,C[C@H(CNC(=O)[C@H]1CC[2NH2+]+C@@H]1C)(C[NH+]1CCCC1
add # at position 50,C[C@H(CNC(=O)[C@H]1CC[2NH2+]+C@@H]1C)(C[NH+]1CCCC1#
remove [ from position 39,C[C@H(CNC(=O)[C@H]1CC[2NH2+]+C@@H]1C)(CNH+]1CCCC1#
remove C from position 29,C[C@H(CNC(=O)[C@H]1CC[2NH2+]+@@H]1C)(CNH+]1CCCC1#
add S at position 11,C[C@H(CNC(=SO)[C@H]1CC[2NH2+]+@@H]1C)(CNH+]1CCCC1#
add C at position 0,CC[C@H(CNC(=SO)[C@H]1CC[2NH2+]+@@H]1C)(CNH+]1CCCC1#
remove [ from position 15,CC[C@H(CNC(=SO)C@H]1CC[2NH2+]+@@H]1C)(CNH+]1CCCC1#
add r at position 34,CC[C@H(CNC(=SO)C@H]1CC[2NH2+]+@@H]r1C)(CNH+]1CCCC1#
remove ( from position 38,CC[C@H(CNC(=SO)C@H]1CC[2NH2+]+@@H]r1C)CNH+]1CCCC1#
remove H from position 32,CC[C@H(CNC(=SO)C@H]1CC[2NH2+]+@@]r1C)CNH+]1CCCC1#
remove H from position 17,CC[C@H(CNC(=SO)C@]1CC[2NH2+]+@@]r1C)CNH+]1CCCC1#
replace @ at position 4 with O,CC[COH(CNC(=SO)C@]1CC[2NH2+]+@@]r1C)CNH+]1CCCC1#
remove 2 from position 22,CC[COH(CNC(=SO)C@]1CC[NH2+]+@@]r1C)CNH+]1CCCC1#
remove C from position 41,CC[COH(CNC(=SO)C@]1CC[NH2+]+@@]r1C)CNH+]1CCC1#
replace @ at position 29 with r,CC[COH(CNC(=SO)C@]1CC[NH2+]+@r]r1C)CNH+]1CCC1#
remove C from position 1,C[COH(CNC(=SO)C@]1CC[NH2+]+@r]r1C)CNH+]1CCC1#
replace H at position 4 with #,C[CO#(CNC(=SO)C@]1CC[NH2+]+@r]r1C)CNH+]1CCC1#
remove r from position 30,C[CO#(CNC(=SO)C@]1CC[NH2+]+@r]1C)CNH+]1CCC1#
remove = from position 10,C[CO#(CNC(SO)C@]1CC[NH2+]+@r]1C)CNH+]1CCC1#
add B at position 37,C[CO#(CNC(SO)C@]1CC[NH2+]+@r]1C)CNH+]B1CCC1#
add 1 at position 35,C[CO#(CNC(SO)C@]1CC[NH2+]+@r]1C)CNH1+]B1CCC1#
add ] at position 22,C[CO#(CNC(SO)C@]1CC[NH]2+]+@r]1C)CNH1+]B1CCC1#
add - at position 33,C[CO#(CNC(SO)C@]1CC[NH]2+]+@r]1C)-CNH1+]B1CCC1#
remove ] from position 29,C[CO#(CNC(SO)C@]1CC[NH]2+]+@r1C)-CNH1+]B1CCC1#
add O at position 17,C[CO#(CNC(SO)C@]1OCC[NH]2+]+@r1C)-CNH1+]B1CCC1#
replace O at position 11 with 3,C[CO#(CNC(S3)C@]1OCC[NH]2+]+@r1C)-CNH1+]B1CCC1#
add ] at position 9,C[CO#(CNC](S3)C@]1OCC[NH]2+]+@r1C)-CNH1+]B1CCC1#
replace C at position 2 with I,C[IO#(CNC](S3)C@]1OCC[NH]2+]+@r1C)-CNH1+]B1CCC1#
remove N from position 22,C[IO#(CNC](S3)C@]1OCC[H]2+]+@r1C)-CNH1+]B1CCC1#
remove 1 from position 37,C[IO#(CNC](S3)C@]1OCC[H]2+]+@r1C)-CNH+]B1CCC1#
replace 1 at position 30 with (,C[IO#(CNC](S3)C@]1OCC[H]2+]+@r(C)-CNH+]B1CCC1#
replace ( at position 30 with o,C[IO#(CNC](S3)C@]1OCC[H]2+]+@roC)-CNH+]B1CCC1#
replace ( at position 10 with B,C[IO#(CNC]BS3)C@]1OCC[H]2+]+@roC)-CNH+]B1CCC1#
remove - from position 33,C[IO#(CNC]BS3)C@]1OCC[H]2+]+@roC)CNH+]B1CCC1#
replace O at position 18 with 3,C[IO#(CNC]BS3)C@]13CC[H]2+]+@roC)CNH+]B1CCC1#
add = at position 11,C[IO#(CNC]B=S3)C@]13CC[H]2+]+@roC)CNH+]B1CCC1#
remove N from position 7,C[IO#(CC]B=S3)C@]13CC[H]2+]+@roC)CNH+]B1CCC1#
replace C at position 6 with n,C[IO#(nC]B=S3)C@]13CC[H]2+]+@roC)CNH+]B1CCC1#
replace 2 at position 24 with n,C[IO#(nC]B=S3)C@]13CC[H]n+]+@roC)CNH+]B1CCC1#
replace ] at position 16 with N,C[IO#(nC]B=S3)C@N13CC[H]n+]+@roC)CNH+]B1CCC1#
add S at position 6,C[IO#(SnC]B=S3)C@N13CC[H]n+]+@roC)CNH+]B1CCC1#
remove B from position 10,C[IO#(SnC]=S3)C@N13CC[H]n+]+@roC)CNH+]B1CCC1#
remove 1 from position 17,C[IO#(SnC]=S3)C@N3CC[H]n+]+@roC)CNH+]B1CCC1#
replace [ at position 20 with l,C[IO#(SnC]=S3)C@N3CClH]n+]+@roC)CNH+]B1CCC1#
replace # at position 4 with +,C[IO+(SnC]=S3)C@N3CClH]n+]+@roC)CNH+]B1CCC1#
remove O from position 3,C[I+(SnC]=S3)C@N3CClH]n+]+@roC)CNH+]B1CCC1#
replace I at position 2 with 2,C[2+(SnC]=S3)C@N3CClH]n+]+@roC)CNH+]B1CCC1#
remove S from position 5,C[2+(nC]=S3)C@N3CClH]n+]+@roC)CNH+]B1CCC1#
replace ] at position 20 with 4,C[2+(nC]=S3)C@N3CClH4n+]+@roC)CNH+]B1CCC1#
replace ( at position 4 with I,C[2+InC]=S3)C@N3CClH4n+]+@roC)CNH+]B1CCC1#
remove I from position 4,C[2+nC]=S3)C@N3CClH4n+]+@roC)CNH+]B1CCC1#
remove @ from position 12,C[2+nC]=S3)CN3CClH4n+]+@roC)CNH+]B1CCC1#
add S at position 12,C[2+nC]=S3)CSN3CClH4n+]+@roC)CNH+]B1CCC1#
replace C at position 29 with F,C[2+nC]=S3)CSN3CClH4n+]+@roC)FNH+]B1CCC1#
add r at position 23,C[2+nC]=S3)CSN3CClH4n+]r+@roC)FNH+]B1CCC1#
replace H at position 32 with r,C[2+nC]=S3)CSN3CClH4n+]r+@roC)FNr+]B1CCC1#
remove C from position 11,C[2+nC]=S3)SN3CClH4n+]r+@roC)FNr+]B1CCC1#
replace 3 at position 13 with 5,C[2+nC]=S3)SN5CClH4n+]r+@roC)FNr+]B1CCC1#
add F at position 22,C[2+nC]=S3)SN5CClH4n+]Fr+@roC)FNr+]B1CCC1#
add B at position 35,C[2+nC]=S3)SN5CClH4n+]Fr+@roC)FNr+]BB1CCC1#
remove S from position 8,C[2+nC]=3)SN5CClH4n+]Fr+@roC)FNr+]BB1CCC1#
add r at position 10,C[2+nC]=3)rSN5CClH4n+]Fr+@roC)FNr+]BB1CCC1#
add ) at position 34,C[2+nC]=3)rSN5CClH4n+]Fr+@roC)FNr+)]BB1CCC1#
remove r from position 10,C[2+nC]=3)SN5CClH4n+]Fr+@roC)FNr+)]BB1CCC1#
add ( at position 43,C[2+nC]=3)SN5CClH4n+]Fr+@roC)FNr+)]BB1CCC1#(
remove ) from position 9,C[2+nC]=3SN5CClH4n+]Fr+@roC)FNr+)]BB1CCC1#(
replace 2 at position 2 with @,C[@+nC]=3SN5CClH4n+]Fr+@roC)FNr+)]BB1CCC1#(
remove F from position 20,C[@+nC]=3SN5CClH4n+]r+@roC)FNr+)]BB1CCC1#(
add r at position 24,C[@+nC]=3SN5CClH4n+]r+@rroC)FNr+)]BB1CCC1#(
replace n at position 17 with F,C[@+nC]=3SN5CClH4F+]r+@rroC)FNr+)]BB1CCC1#(
remove ] from position 19,C[@+nC]=3SN5CClH4F+r+@rroC)FNr+)]BB1CCC1#(
remove C from position 36,C[@+nC]=3SN5CClH4F+r+@rroC)FNr+)]BB1CC1#(
replace B at position 34 with ),C[@+nC]=3SN5CClH4F+r+@rroC)FNr+)]B)1CC1#(
replace F at position 17 with =,C[@+nC]=3SN5CClH4=+r+@rroC)FNr+)]B)1CC1#(
remove C from position 36,C[@+nC]=3SN5CClH4=+r+@rroC)FNr+)]B)1C1#(
remove = from position 7,C[@+nC]3SN5CClH4=+r+@rroC)FNr+)]B)1C1#(
remove + from position 17,C[@+nC]3SN5CClH4=r+@rroC)FNr+)]B)1C1#(
add 7 at position 37,C[@+nC]3SN5CClH4=r+@rroC)FNr+)]B)1C1#7(
add 2 at position 28,C[@+nC]3SN5CClH4=r+@rroC)FNr2+)]B)1C1#7(
replace 5 at position 10 with =,C[@+nC]3SN=CClH4=r+@rroC)FNr2+)]B)1C1#7(
remove n from position 4,C[@+C]3SN=CClH4=r+@rroC)FNr2+)]B)1C1#7(
remove r from position 19,C[@+C]3SN=CClH4=r+@roC)FNr2+)]B)1C1#7(
remove B from position 30,C[@+C]3SN=CClH4=r+@roC)FNr2+)])1C1#7(
add / at position 30,C[@+C]3SN=CClH4=r+@roC)FNr2+)]/)1C1#7(
remove F from position 23,C[@+C]3SN=CClH4=r+@roC)Nr2+)]/)1C1#7(
remove ) from position 22,C[@+C]3SN=CClH4=r+@roCNr2+)]/)1C1#7(
add r at position 33,C[@+C]3SN=CClH4=r+@roCNr2+)]/)1C1r#7(
remove 1 from position 30,C[@+C]3SN=CClH4=r+@roCNr2+)]/)C1r#7(
remove ) from position 29,C[@+C]3SN=CClH4=r+@roCNr2+)]/C1r#7(
remove 3 from position 6,C[@+C]SN=CClH4=r+@roCNr2+)]/C1r#7(
add ) at position 30,C[@+C]SN=CClH4=r+@roCNr2+)]/C1)r#7(
replace N at position 21 with 6,C[@+C]SN=CClH4=r+@roC6r2+)]/C1)r#7(
replace r at position 18 with 4,C[@+C]SN=CClH4=r+@4oC6r2+)]/C1)r#7(
remove 6 from position 21,C[@+C]SN=CClH4=r+@4oCr2+)]/C1)r#7(
remove 4 from position 18,C[@+C]SN=CClH4=r+@oCr2+)]/C1)r#7(
remove ( from position 32,C[@+C]SN=CClH4=r+@oCr2+)]/C1)r#7
replace @ at position 17 with ),C[@+C]SN=CClH4=r+)oCr2+)]/C1)r#7
remove 4 from position 13,C[@+C]SN=CClH=r+)oCr2+)]/C1)r#7
remove C from position 10,C[@+C]SN=ClH=r+)oCr2+)]/C1)r#7
replace + at position 14 with 4,C[@+C]SN=ClH=r4)oCr2+)]/C1)r#7
add H at position 16,C[@+C]SN=ClH=r4)HoCr2+)]/C1)r#7
remove + from position 3,C[@C]SN=ClH=r4)HoCr2+)]/C1)r#7
replace C at position 8 with 6,C[@C]SN=6lH=r4)HoCr2+)]/C1)r#7
add 7 at position 0,7C[@C]SN=6lH=r4)HoCr2+)]/C1)r#7
add 1 at position 2,7C1[@C]SN=6lH=r4)HoCr2+)]/C1)r#7
remove r from position 20,7C1[@C]SN=6lH=r4)HoC2+)]/C1)r#7
remove = from position 13,7C1[@C]SN=6lHr4)HoC2+)]/C1)r#7
replace 7 at position 29 with ),7C1[@C]SN=6lHr4)HoC2+)]/C1)r#)
replace = at position 9 with 3,7C1[@C]SN36lHr4)HoC2+)]/C1)r#)
remove ) from position 29,7C1[@C]SN36lHr4)HoC2+)]/C1)r#
remove l from position 11,7C1[@C]SN36Hr4)HoC2+)]/C1)r#
remove ) from position 20,7C1[@C]SN36Hr4)HoC2+]/C1)r#
replace 7 at position 0 with S,SC1[@C]SN36Hr4)HoC2+]/C1)r#
remove 2 from position 18,SC1[@C]SN36Hr4)HoC+]/C1)r#
add B at position 15,SC1[@C]SN36Hr4)BHoC+]/C1)r#
remove / from position 21,SC1[@C]SN36Hr4)BHoC+]C1)r#
replace @ at position 4 with r,SC1[rC]SN36Hr4)BHoC+]C1)r#
remove + from position 19,SC1[rC]SN36Hr4)BHoC]C1)r#
add s at position 17,SC1[rC]SN36Hr4)BHsoC]C1)r#
replace 1 at position 22 with /,SC1[rC]SN36Hr4)BHsoC]C/)r#
replace r at position 24 with C,SC1[rC]SN36Hr4)BHsoC]C/)C#
remove r from position 12,SC1[rC]SN36H4)BHsoC]C/)C#
replace 6 at position 10 with -,SC1[rC]SN3-H4)BHsoC]C/)C#
remove o from position 17,SC1[rC]SN3-H4)BHsC]C/)C#
remove C from position 19,SC1[rC]SN3-H4)BHsC]/)C#
remove B from position 14,SC1[rC]SN3-H4)HsC]/)C#
add r at position 2,SCr1[rC]SN3-H4)HsC]/)C#
remove C from position 21,SCr1[rC]SN3-H4)HsC]/)#
remove C from position 1,Sr1[rC]SN3-H4)HsC]/)#
remove 1 from position 2,Sr[rC]SN3-H4)HsC]/)#
remove # from position 19,Sr[rC]SN3-H4)HsC]/)
replace 4 at position 11 with F,Sr[rC]SN3-HF)HsC]/)
remove ) from position 12,Sr[rC]SN3-HFHsC]/)
remove r from position 1,S[rC]SN3-HFHsC]/)
add l at position 2,S[lrC]SN3-HFHsC]/)
remove H from position 10,S[lrC]SN3-FHsC]/)
add S at position 7,S[lrC]SSN3-FHsC]/)
replace S at position 0 with o,o[lrC]SSN3-FHsC]/)
remove F from position 11,o[lrC]SSN3-HsC]/)
remove l from position 2,o[rC]SSN3-HsC]/)
remove S from position 6,o[rC]SN3-HsC]/)
replace / at position 13 with N,o[rC]SN3-HsC]N)
remove C from position 11,o[rC]SN3-Hs]N)
add O at position 2,o[OrC]SN3-Hs]N)
replace H at position 10 with ],o[OrC]SN3-]s]N)
add 6 at position 14,o[OrC]SN3-]s]N6)
remove s from position 11,o[OrC]SN3-]]N6)
remove S from position 6,o[OrC]N3-]]N6)
replace r at position 3 with 4,o[O4C]N3-]]N6)
add H at position 5,o[O4CH]N3-]]N6)
replace [ at position 1 with F,oFO4CH]N3-]]N6)
remove 6 from position 13,oFO4CH]N3-]]N)
add H at position 9,oFO4CH]N3H-]]N)
remove O from position 2,oF4CH]N3H-]]N)
remove ] from position 5,oF4CHN3H-]]N)
replace H at position 4 with [,oF4C[N3H-]]N)
remove 4 from position 2,oFC[N3H-]]N)
remove C from position 2,oF[N3H-]]N)
replace ] at position 8 with S,oF[N3H-]SN)
remove N from position 3,oF[3H-]SN)
remove [ from position 2,oF3H-]SN)
remove S from position 6,oF3H-]N)
remove ) from position 7,oF3H-]N
replace - at position 4 with B,oF3HB]N
remove N from position 6,oF3HB]
remove B from position 4,oF3H]
add C at position 5,oF3H]C
replace ] at position 4 with -,oF3H-C
add B at position 3,oF3BH-C
add c at position 2,oFc3BH-C
replace 3 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
