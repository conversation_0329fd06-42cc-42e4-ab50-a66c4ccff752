log,state
initialize: COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)CC1,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)CC1
replace C at position 28 with +,COc1ccccc1N1CCN(c2ccc(=O)n(C+(=O)NC3CC3)n2)CC1
add ( at position 37,COc1ccccc1N1CCN(c2ccc(=O)n(C+(=O)NC3C(C3)n2)CC1
remove c from position 5,COc1cccc1N1CCN(c2ccc(=O)n(C+(=O)NC3C(C3)n2)CC1
add 2 at position 22,COc1cccc1N1CCN(c2ccc(=2O)n(C+(=O)NC3C(C3)n2)CC1
add 6 at position 1,C6Oc1cccc1N1CCN(c2ccc(=2O)n(C+(=O)NC3C(C3)n2)CC1
remove ( from position 30,C6Oc1cccc1N1CCN(c2ccc(=2O)n(C+=O)NC3C(C3)n2)CC1
remove 1 from position 9,C6Oc1ccccN1CCN(c2ccc(=2O)n(C+=O)NC3C(C3)n2)CC1
add + at position 45,C6Oc1ccccN1CCN(c2ccc(=2O)n(C+=O)NC3C(C3)n2)CC+1
replace N at position 32 with B,C6Oc1ccccN1CCN(c2ccc(=2O)n(C+=O)BC3C(C3)n2)CC+1
replace c at position 15 with ),C6Oc1ccccN1CCN()2ccc(=2O)n(C+=O)BC3C(C3)n2)CC+1
remove = from position 29,C6Oc1ccccN1CCN()2ccc(=2O)n(C+O)BC3C(C3)n2)CC+1
add ) at position 43,C6Oc1ccccN1CCN()2ccc(=2O)n(C+O)BC3C(C3)n2)C)C+1
remove O from position 2,C6c1ccccN1CCN()2ccc(=2O)n(C+O)BC3C(C3)n2)C)C+1
remove C from position 35,C6c1ccccN1CCN()2ccc(=2O)n(C+O)BC3C(3)n2)C)C+1
add B at position 19,C6c1ccccN1CCN()2cccB(=2O)n(C+O)BC3C(3)n2)C)C+1
replace c at position 17 with 2,C6c1ccccN1CCN()2c2cB(=2O)n(C+O)BC3C(3)n2)C)C+1
remove N from position 12,C6c1ccccN1CC()2c2cB(=2O)n(C+O)BC3C(3)n2)C)C+1
remove C from position 33,C6c1ccccN1CC()2c2cB(=2O)n(C+O)BC3(3)n2)C)C+1
remove 3 from position 32,C6c1ccccN1CC()2c2cB(=2O)n(C+O)BC(3)n2)C)C+1
add - at position 21,C6c1ccccN1CC()2c2cB(=-2O)n(C+O)BC(3)n2)C)C+1
remove + from position 42,C6c1ccccN1CC()2c2cB(=-2O)n(C+O)BC(3)n2)C)C1
remove 1 from position 3,C6cccccN1CC()2c2cB(=-2O)n(C+O)BC(3)n2)C)C1
add / at position 31,C6cccccN1CC()2c2cB(=-2O)n(C+O)B/C(3)n2)C)C1
add - at position 13,C6cccccN1CC()-2c2cB(=-2O)n(C+O)B/C(3)n2)C)C1
add ] at position 22,C6cccccN1CC()-2c2cB(=-]2O)n(C+O)B/C(3)n2)C)C1
add - at position 33,C6cccccN1CC()-2c2cB(=-]2O)n(C+O)B-/C(3)n2)C)C1
remove + from position 29,C6cccccN1CC()-2c2cB(=-]2O)n(CO)B-/C(3)n2)C)C1
add O at position 17,C6cccccN1CC()-2c2OcB(=-]2O)n(CO)B-/C(3)n2)C)C1
replace ( at position 11 with 4,C6cccccN1CC4)-2c2OcB(=-]2O)n(CO)B-/C(3)n2)C)C1
add ] at position 9,C6cccccN1]CC4)-2c2OcB(=-]2O)n(CO)B-/C(3)n2)C)C1
replace c at position 2 with H,C6HccccN1]CC4)-2c2OcB(=-]2O)n(CO)B-/C(3)n2)C)C1
remove = from position 22,C6HccccN1]CC4)-2c2OcB(-]2O)n(CO)B-/C(3)n2)C)C1
remove 3 from position 37,C6HccccN1]CC4)-2c2OcB(-]2O)n(CO)B-/C()n2)C)C1
replace O at position 30 with (,C6HccccN1]CC4)-2c2OcB(-]2O)n(C()B-/C()n2)C)C1
replace ( at position 30 with o,C6HccccN1]CC4)-2c2OcB(-]2O)n(Co)B-/C()n2)C)C1
replace C at position 10 with @,C6HccccN1]@C4)-2c2OcB(-]2O)n(Co)B-/C()n2)C)C1
remove - from position 33,C6HccccN1]@C4)-2c2OcB(-]2O)n(Co)B/C()n2)C)C1
replace O at position 18 with 3,C6HccccN1]@C4)-2c23cB(-]2O)n(Co)B/C()n2)C)C1
add = at position 11,C6HccccN1]@=C4)-2c23cB(-]2O)n(Co)B/C()n2)C)C1
remove N from position 7,C6Hcccc1]@=C4)-2c23cB(-]2O)n(Co)B/C()n2)C)C1
replace c at position 6 with n,C6Hcccn1]@=C4)-2c23cB(-]2O)n(Co)B/C()n2)C)C1
replace 2 at position 24 with n,C6Hcccn1]@=C4)-2c23cB(-]nO)n(Co)B/C()n2)C)C1
replace c at position 16 with N,C6Hcccn1]@=C4)-2N23cB(-]nO)n(Co)B/C()n2)C)C1
add S at position 6,C6HcccSn1]@=C4)-2N23cB(-]nO)n(Co)B/C()n2)C)C1
remove @ from position 10,C6HcccSn1]=C4)-2N23cB(-]nO)n(Co)B/C()n2)C)C1
remove 2 from position 17,C6HcccSn1]=C4)-2N3cB(-]nO)n(Co)B/C()n2)C)C1
replace ( at position 20 with l,C6HcccSn1]=C4)-2N3cBl-]nO)n(Co)B/C()n2)C)C1
replace c at position 4 with ),C6Hc)cSn1]=C4)-2N3cBl-]nO)n(Co)B/C()n2)C)C1
remove c from position 3,C6H)cSn1]=C4)-2N3cBl-]nO)n(Co)B/C()n2)C)C1
replace H at position 2 with 2,C62)cSn1]=C4)-2N3cBl-]nO)n(Co)B/C()n2)C)C1
remove S from position 5,C62)cn1]=C4)-2N3cBl-]nO)n(Co)B/C()n2)C)C1
replace ] at position 20 with 4,C62)cn1]=C4)-2N3cBl-4nO)n(Co)B/C()n2)C)C1
replace c at position 4 with H,C62)Hn1]=C4)-2N3cBl-4nO)n(Co)B/C()n2)C)C1
remove H from position 4,C62)n1]=C4)-2N3cBl-4nO)n(Co)B/C()n2)C)C1
remove 2 from position 12,C62)n1]=C4)-N3cBl-4nO)n(Co)B/C()n2)C)C1
add S at position 12,C62)n1]=C4)-SN3cBl-4nO)n(Co)B/C()n2)C)C1
replace / at position 29 with F,C62)n1]=C4)-SN3cBl-4nO)n(Co)BFC()n2)C)C1
add r at position 23,C62)n1]=C4)-SN3cBl-4nO)rn(Co)BFC()n2)C)C1
replace ( at position 32 with r,C62)n1]=C4)-SN3cBl-4nO)rn(Co)BFCr)n2)C)C1
remove - from position 11,C62)n1]=C4)SN3cBl-4nO)rn(Co)BFCr)n2)C)C1
replace 3 at position 13 with 5,C62)n1]=C4)SN5cBl-4nO)rn(Co)BFCr)n2)C)C1
add F at position 22,C62)n1]=C4)SN5cBl-4nO)Frn(Co)BFCr)n2)C)C1
add B at position 35,C62)n1]=C4)SN5cBl-4nO)Frn(Co)BFCr)nB2)C)C1
remove C from position 8,C62)n1]=4)SN5cBl-4nO)Frn(Co)BFCr)nB2)C)C1
add r at position 10,C62)n1]=4)rSN5cBl-4nO)Frn(Co)BFCr)nB2)C)C1
add ) at position 34,C62)n1]=4)rSN5cBl-4nO)Frn(Co)BFCr))nB2)C)C1
remove r from position 10,C62)n1]=4)SN5cBl-4nO)Frn(Co)BFCr))nB2)C)C1
add N at position 3,C62N)n1]=4)SN5cBl-4nO)Frn(Co)BFCr))nB2)C)C1
add r at position 9,C62N)n1]=r4)SN5cBl-4nO)Frn(Co)BFCr))nB2)C)C1
add 7 at position 27,C62N)n1]=r4)SN5cBl-4nO)Frn(7Co)BFCr))nB2)C)C1
remove N from position 13,C62N)n1]=r4)S5cBl-4nO)Frn(7Co)BFCr))nB2)C)C1
replace 1 at position 43 with =,C62N)n1]=r4)S5cBl-4nO)Frn(7Co)BFCr))nB2)C)C=
replace - at position 17 with H,C62N)n1]=r4)S5cBlH4nO)Frn(7Co)BFCr))nB2)C)C=
remove n from position 19,C62N)n1]=r4)S5cBlH4O)Frn(7Co)BFCr))nB2)C)C=
remove B from position 36,C62N)n1]=r4)S5cBlH4O)Frn(7Co)BFCr))n2)C)C=
replace ) at position 34 with +,C62N)n1]=r4)S5cBlH4O)Frn(7Co)BFCr)+n2)C)C=
replace H at position 17 with =,C62N)n1]=r4)S5cBl=4O)Frn(7Co)BFCr)+n2)C)C=
remove 2 from position 36,C62N)n1]=r4)S5cBl=4O)Frn(7Co)BFCr)+n)C)C=
remove ] from position 7,C62N)n1=r4)S5cBl=4O)Frn(7Co)BFCr)+n)C)C=
remove 4 from position 17,C62N)n1=r4)S5cBl=O)Frn(7Co)BFCr)+n)C)C=
add 7 at position 37,C62N)n1=r4)S5cBl=O)Frn(7Co)BFCr)+n)C)7C=
add 2 at position 28,C62N)n1=r4)S5cBl=O)Frn(7Co)B2FCr)+n)C)7C=
replace ) at position 10 with =,C62N)n1=r4=S5cBl=O)Frn(7Co)B2FCr)+n)C)7C=
remove ) from position 4,C62Nn1=r4=S5cBl=O)Frn(7Co)B2FCr)+n)C)7C=
remove r from position 19,C62Nn1=r4=S5cBl=O)Fn(7Co)B2FCr)+n)C)7C=
remove ) from position 30,C62Nn1=r4=S5cBl=O)Fn(7Co)B2FCr+n)C)7C=
add / at position 30,C62Nn1=r4=S5cBl=O)Fn(7Co)B2FCr/+n)C)7C=
remove o from position 23,C62Nn1=r4=S5cBl=O)Fn(7C)B2FCr/+n)C)7C=
remove C from position 22,C62Nn1=r4=S5cBl=O)Fn(7)B2FCr/+n)C)7C=
add r at position 33,C62Nn1=r4=S5cBl=O)Fn(7)B2FCr/+n)Cr)7C=
remove n from position 30,C62Nn1=r4=S5cBl=O)Fn(7)B2FCr/+)Cr)7C=
remove + from position 29,C62Nn1=r4=S5cBl=O)Fn(7)B2FCr/)Cr)7C=
remove = from position 6,C62Nn1r4=S5cBl=O)Fn(7)B2FCr/)Cr)7C=
add ) at position 30,C62Nn1r4=S5cBl=O)Fn(7)B2FCr/)C)r)7C=
replace ) at position 21 with 7,C62Nn1r4=S5cBl=O)Fn(77B2FCr/)C)r)7C=
replace n at position 18 with 4,C62Nn1r4=S5cBl=O)F4(77B2FCr/)C)r)7C=
remove 7 from position 21,C62Nn1r4=S5cBl=O)F4(7B2FCr/)C)r)7C=
remove 4 from position 18,C62Nn1r4=S5cBl=O)F(7B2FCr/)C)r)7C=
remove C from position 32,C62Nn1r4=S5cBl=O)F(7B2FCr/)C)r)7=
replace F at position 17 with ),C62Nn1r4=S5cBl=O))(7B2FCr/)C)r)7=
remove l from position 13,C62Nn1r4=S5cB=O))(7B2FCr/)C)r)7=
remove F from position 21,C62Nn1r4=S5cB=O))(7B2Cr/)C)r)7=
replace O at position 14 with 3,C62Nn1r4=S5cB=3))(7B2Cr/)C)r)7=
add l at position 7,C62Nn1rl4=S5cB=3))(7B2Cr/)C)r)7=
add - at position 23,C62Nn1rl4=S5cB=3))(7B2C-r/)C)r)7=
replace / at position 25 with (,C62Nn1rl4=S5cB=3))(7B2C-r()C)r)7=
remove 2 from position 2,C6Nn1rl4=S5cB=3))(7B2C-r()C)r)7=
replace S at position 9 with 5,C6Nn1rl4=55cB=3))(7B2C-r()C)r)7=
remove ) from position 27,C6Nn1rl4=55cB=3))(7B2C-r()Cr)7=
replace 7 at position 29 with ),C6Nn1rl4=55cB=3))(7B2C-r()Cr))=
replace = at position 30 with 4,C6Nn1rl4=55cB=3))(7B2C-r()Cr))4
remove ) from position 15,C6Nn1rl4=55cB=3)(7B2C-r()Cr))4
replace 4 at position 7 with 7,C6Nn1rl7=55cB=3)(7B2C-r()Cr))4
remove C from position 20,C6Nn1rl7=55cB=3)(7B2-r()Cr))4
replace C at position 0 with S,S6Nn1rl7=55cB=3)(7B2-r()Cr))4
remove ) from position 27,S6Nn1rl7=55cB=3)(7B2-r()Cr)4
remove r from position 5,S6Nn1l7=55cB=3)(7B2-r()Cr)4
remove ( from position 21,S6Nn1l7=55cB=3)(7B2-r)Cr)4
replace 1 at position 4 with r,S6Nnrl7=55cB=3)(7B2-r)Cr)4
remove - from position 19,S6Nnrl7=55cB=3)(7B2r)Cr)4
add s at position 17,S6Nnrl7=55cB=3)(7sB2r)Cr)4
replace C at position 22 with /,S6Nnrl7=55cB=3)(7sB2r)/r)4
replace ) at position 24 with F,S6Nnrl7=55cB=3)(7sB2r)/rF4
remove = from position 12,S6Nnrl7=55cB3)(7sB2r)/rF4
replace c at position 10 with -,S6Nnrl7=55-B3)(7sB2r)/rF4
remove B from position 17,S6Nnrl7=55-B3)(7s2r)/rF4
remove ) from position 19,S6Nnrl7=55-B3)(7s2r/rF4
remove ( from position 14,S6Nnrl7=55-B3)7s2r/rF4
add r at position 2,S6rNnrl7=55-B3)7s2r/rF4
remove F from position 21,S6rNnrl7=55-B3)7s2r/r4
remove 6 from position 1,SrNnrl7=55-B3)7s2r/r4
remove N from position 2,Srnrl7=55-B3)7s2r/r4
remove 4 from position 19,Srnrl7=55-B3)7s2r/r
replace 3 at position 11 with F,Srnrl7=55-BF)7s2r/r
remove ) from position 12,Srnrl7=55-BF7s2r/r
remove r from position 1,Snrl7=55-BF7s2r/r
add l at position 2,Snlrl7=55-BF7s2r/r
remove B from position 10,Snlrl7=55-F7s2r/r
add S at position 7,Snlrl7=S55-F7s2r/r
replace S at position 0 with o,onlrl7=S55-F7s2r/r
remove F from position 11,onlrl7=S55-7s2r/r
remove l from position 2,onrl7=S55-7s2r/r
remove S from position 6,onrl7=55-7s2r/r
replace / at position 13 with N,onrl7=55-7s2rNr
remove 2 from position 11,onrl7=55-7srNr
add O at position 2,onOrl7=55-7srNr
replace 7 at position 10 with ],onOrl7=55-]srNr
add 6 at position 14,onOrl7=55-]srN6r
remove s from position 11,onOrl7=55-]rN6r
remove = from position 6,onOrl755-]rN6r
replace r at position 3 with 4,onO4l755-]rN6r
add H at position 5,onO4lH755-]rN6r
replace n at position 1 with F,oFO4lH755-]rN6r
remove 6 from position 13,oFO4lH755-]rNr
add H at position 9,oFO4lH755H-]rNr
remove O from position 2,oF4lH755H-]rNr
remove 7 from position 5,oF4lH55H-]rNr
replace H at position 4 with [,oF4l[55H-]rNr
remove 4 from position 2,oFl[55H-]rNr
remove l from position 2,oF[55H-]rNr
replace r at position 8 with S,oF[55H-]SNr
remove 5 from position 3,oF[5H-]SNr
remove [ from position 2,oF5H-]SNr
remove S from position 6,oF5H-]Nr
remove r from position 7,oF5H-]N
replace - at position 4 with B,oF5HB]N
remove N from position 6,oF5HB]
remove B from position 4,oF5H]
add C at position 5,oF5H]C
replace ] at position 4 with -,oF5H-C
add B at position 3,oF5BH-C
add c at position 2,oFc5BH-C
replace 5 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
