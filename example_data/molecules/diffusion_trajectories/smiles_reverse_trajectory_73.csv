log,state
initialize: ,
add 1 at position 0,1
add C at position 1,1C
add N at position 2,1<PERSON><PERSON>
add ) at position 3,1CN)
replace N at position 2 with r,1Cr)
replace <PERSON> at position 1 with o,1or)
remove 1 from position 0,or)
add S at position 1,oSr)
add C at position 4,oSr)C
replace o at position 0 with l,lSr)C
remove S from position 1,lr)C
add / at position 2,lr/)C
remove l from position 0,r/)C
add r at position 0,rr/)C
add - at position 3,rr/-)C
add 4 at position 4,rr/-4)C
add n at position 3,rr/n-4)C
add 1 at position 1,r1r/n-4)C
add 7 at position 0,7r1r/n-4)C
add r at position 10,7r1r/n-4)Cr
remove r from position 1,71r/n-4)Cr
add s at position 7,71r/n-4s)Cr
add / at position 9,71r/n-4s)/Cr
add r at position 8,71r/n-4sr)/Cr
replace - at position 5 with ],71r/n]4sr)/Cr
add ) at position 6,71r/n])4sr)/Cr
replace C at position 12 with H,71r/n])4sr)/Hr
replace / at position 11 with 3,71r/n])4sr)3Hr
remove s from position 8,71r/n])4r)3Hr
add 5 at position 9,71r/n])4r5)3Hr
replace r at position 2 with N,71N/n])4r5)3Hr
add S at position 10,71N/n])4r5S)3Hr
replace / at position 3 with I,71NIn])4r5S)3Hr
remove 5 from position 9,71NIn])4rS)3Hr
add c at position 12,71NIn])4rS)3cHr
add o at position 2,71oNIn])4rS)3cHr
add ) at position 7,71oNIn]))4rS)3cHr
replace 4 at position 9 with 2,71oNIn]))2rS)3cHr
replace o at position 2 with 7,717NIn]))2rS)3cHr
add o at position 13,717NIn]))2rS)o3cHr
add 6 at position 10,717NIn]))26rS)o3cHr
remove 1 from position 1,77NIn]))26rS)o3cHr
remove 7 from position 0,7NIn]))26rS)o3cHr
replace 6 at position 8 with [,7NIn]))2[rS)o3cHr
add 7 at position 3,7NI7n]))2[rS)o3cHr
remove H from position 16,7NI7n]))2[rS)o3cr
replace 3 at position 14 with 5,7NI7n]))2[rS)o5cr
add s at position 10,7NI7n]))2[srS)o5cr
add = at position 6,7NI7n]=))2[srS)o5cr
replace ) at position 8 with S,7NI7n]=)S2[srS)o5cr
add o at position 16,7NI7n]=)S2[srS)oo5cr
add 4 at position 9,7NI7n]=)S42[srS)oo5cr
add 6 at position 18,7NI7n]=)S42[srS)oo65cr
replace 4 at position 9 with O,7NI7n]=)SO2[srS)oo65cr
replace 6 at position 18 with ),7NI7n]=)SO2[srS)oo)5cr
replace ) at position 15 with r,7NI7n]=)SO2[srSroo)5cr
replace 7 at position 0 with 6,6NI7n]=)SO2[srSroo)5cr
replace S at position 14 with 7,6NI7n]=)SO2[sr7roo)5cr
add C at position 0,C6NI7n]=)SO2[sr7roo)5cr
add F at position 21,C6NI7n]=)SO2[sr7roo)5Fcr
remove r from position 16,C6NI7n]=)SO2[sr7oo)5Fcr
add H at position 11,C6NI7n]=)SOH2[sr7oo)5Fcr
add 2 at position 11,C6NI7n]=)SO2H2[sr7oo)5Fcr
add 7 at position 5,C6NI77n]=)SO2H2[sr7oo)5Fcr
replace 7 at position 5 with n,C6NI7nn]=)SO2H2[sr7oo)5Fcr
add ) at position 21,C6NI7nn]=)SO2H2[sr7oo))5Fcr
add l at position 9,C6NI7nn]=l)SO2H2[sr7oo))5Fcr
add 2 at position 2,C62NI7nn]=l)SO2H2[sr7oo))5Fcr
replace 7 at position 5 with c,C62NIcnn]=l)SO2H2[sr7oo))5Fcr
replace 2 at position 14 with 5,C62NIcnn]=l)SO5H2[sr7oo))5Fcr
add ( at position 11,C62NIcnn]=l()SO5H2[sr7oo))5Fcr
add n at position 18,C62NIcnn]=l()SO5H2n[sr7oo))5Fcr
add ) at position 19,C62NIcnn]=l()SO5H2n)[sr7oo))5Fcr
add C at position 24,C62NIcnn]=l()SO5H2n)[sr7Coo))5Fcr
add @ at position 17,C62NIcnn]=l()SO5H@2n)[sr7Coo))5Fcr
add ] at position 26,C62NIcnn]=l()SO5H@2n)[sr7C]oo))5Fcr
replace c at position 5 with ),C62NI)nn]=l()SO5H@2n)[sr7C]oo))5Fcr
add r at position 9,C62NI)nn]r=l()SO5H@2n)[sr7C]oo))5Fcr
add l at position 19,C62NI)nn]r=l()SO5H@l2n)[sr7C]oo))5Fcr
replace H at position 17 with C,C62NI)nn]r=l()SO5C@l2n)[sr7C]oo))5Fcr
replace s at position 24 with F,C62NI)nn]r=l()SO5C@l2n)[Fr7C]oo))5Fcr
add 3 at position 13,C62NI)nn]r=l(3)SO5C@l2n)[Fr7C]oo))5Fcr
remove 7 from position 27,C62NI)nn]r=l(3)SO5C@l2n)[FrC]oo))5Fcr
remove r from position 9,C62NI)nn]=l(3)SO5C@l2n)[FrC]oo))5Fcr
remove N from position 3,C62I)nn]=l(3)SO5C@l2n)[FrC]oo))5Fcr
add 4 at position 20,C62I)nn]=l(3)SO5C@l24n)[FrC]oo))5Fcr
remove l from position 9,C62I)nn]=(3)SO5C@l24n)[FrC]oo))5Fcr
remove I from position 3,C62)nn]=(3)SO5C@l24n)[FrC]oo))5Fcr
remove 5 from position 30,C62)nn]=(3)SO5C@l24n)[FrC]oo))Fcr
remove F from position 22,C62)nn]=(3)SO5C@l24n)[rC]oo))Fcr
replace 5 at position 13 with 3,C62)nn]=(3)SO3C@l24n)[rC]oo))Fcr
add N at position 11,C62)nn]=(3)NSO3C@l24n)[rC]oo))Fcr
replace r at position 32 with n,C62)nn]=(3)NSO3C@l24n)[rC]oo))Fcn
remove r from position 23,C62)nn]=(3)NSO3C@l24n)[C]oo))Fcn
replace F at position 29 with (,C62)nn]=(3)NSO3C@l24n)[C]oo))(cn
remove S from position 12,C62)nn]=(3)NO3C@l24n)[C]oo))(cn
add 2 at position 12,C62)nn]=(3)N2O3C@l24n)[C]oo))(cn
add I at position 4,C62)Inn]=(3)N2O3C@l24n)[C]oo))(cn
replace I at position 4 with C,C62)Cnn]=(3)N2O3C@l24n)[C]oo))(cn
replace 4 at position 20 with ],C62)Cnn]=(3)N2O3C@l2]n)[C]oo))(cn
add S at position 5,C62)CSnn]=(3)N2O3C@l2]n)[C]oo))(cn
replace 2 at position 2 with I,C6I)CSnn]=(3)N2O3C@l2]n)[C]oo))(cn
add S at position 3,C6IS)CSnn]=(3)N2O3C@l2]n)[C]oo))(cn
replace ) at position 4 with N,C6ISNCSnn]=(3)N2O3C@l2]n)[C]oo))(cn
replace l at position 20 with H,C6ISNCSnn]=(3)N2O3C@H2]n)[C]oo))(cn
add [ at position 17,C6ISNCSnn]=(3)N2O[3C@H2]n)[C]oo))(cn
add B at position 10,C6ISNCSnn]B=(3)N2O[3C@H2]n)[C]oo))(cn
remove S from position 6,C6ISNCnn]B=(3)N2O[3C@H2]n)[C]oo))(cn
replace O at position 16 with C,C6ISNCnn]B=(3)N2C[3C@H2]n)[C]oo))(cn
replace n at position 24 with (,C6ISNCnn]B=(3)N2C[3C@H2]()[C]oo))(cn
replace n at position 6 with 1,C6ISNC1n]B=(3)N2C[3C@H2]()[C]oo))(cn
add c at position 7,C6ISNC1cn]B=(3)N2C[3C@H2]()[C]oo))(cn
remove = from position 11,C6ISNC1cn]B(3)N2C[3C@H2]()[C]oo))(cn
replace 3 at position 18 with O,C6ISNC1cn]B(3)N2C[OC@H2]()[C]oo))(cn
add - at position 33,C6ISNC1cn]B(3)N2C[OC@H2]()[C]oo))-(cn
replace B at position 10 with (,C6ISNC1cn]((3)N2C[OC@H2]()[C]oo))-(cn
replace o at position 30 with (,C6ISNC1cn]((3)N2C[OC@H2]()[C]o())-(cn
replace ( at position 30 with O,C6ISNC1cn]((3)N2C[OC@H2]()[C]oO))-(cn
add 1 at position 37,C6ISNC1cn]((3)N2C[OC@H2]()[C]oO))-(cn1
add - at position 22,C6ISNC1cn]((3)N2C[OC@H-2]()[C]oO))-(cn1
replace I at position 2 with C,C6CSNC1cn]((3)N2C[OC@H-2]()[C]oO))-(cn1
remove ] from position 9,C6CSNC1cn((3)N2C[OC@H-2]()[C]oO))-(cn1
replace 3 at position 11 with O,C6CSNC1cn((O)N2C[OC@H-2]()[C]oO))-(cn1
remove O from position 17,C6CSNC1cn((O)N2C[C@H-2]()[C]oO))-(cn1
add ( at position 29,C6CSNC1cn((O)N2C[C@H-2]()[C]o(O))-(cn1
remove - from position 33,C6CSNC1cn((O)N2C[C@H-2]()[C]o(O))(cn1
remove ] from position 22,C6CSNC1cn((O)N2C[C@H-2()[C]o(O))(cn1
add c at position 9,C6CSNC1cnc((O)N2C[C@H-2()[C]o(O))(cn1
add H at position 4,C6CSHNC1cnc((O)N2C[C@H-2()[C]o(O))(cn1
remove H from position 4,C6CSNC1cnc((O)N2C[C@H-2()[C]o(O))(cn1
add C at position 24,C6CSNC1cnc((O)N2C[C@H-2(C)[C]o(O))(cn1
remove - from position 21,C6CSNC1cnc((O)N2C[C@H2(C)[C]o(O))(cn1
add C at position 32,C6CSNC1cnc((O)N2C[C@H2(C)[C]o(O)C)(cn1
add 2 at position 33,C6CSNC1cnc((O)N2C[C@H2(C)[C]o(O)C2)(cn1
add = at position 12,C6CSNC1cnc((=O)N2C[C@H2(C)[C]o(O)C2)(cn1
add ] at position 22,C6CSNC1cnc((=O)N2C[C@H]2(C)[C]o(O)C2)(cn1
replace N at position 4 with O,C6CSOC1cnc((=O)N2C[C@H]2(C)[C]o(O)C2)(cn1
add C at position 17,C6CSOC1cnc((=O)N2CC[C@H]2(C)[C]o(O)C2)(cn1
remove C from position 5,C6CSO1cnc((=O)N2CC[C@H]2(C)[C]o(O)C2)(cn1
add + at position 29,C6CSO1cnc((=O)N2CC[C@H]2(C)[C+]o(O)C2)(cn1
replace O at position 4 with c,C6CSc1cnc((=O)N2CC[C@H]2(C)[C+]o(O)C2)(cn1
remove o from position 31,C6CSc1cnc((=O)N2CC[C@H]2(C)[C+](O)C2)(cn1
remove n from position 7,C6CSc1cc((=O)N2CC[C@H]2(C)[C+](O)C2)(cn1
add C at position 9,C6CSc1cc(C(=O)N2CC[C@H]2(C)[C+](O)C2)(cn1
add H at position 30,C6CSc1cc(C(=O)N2CC[C@H]2(C)[C+H](O)C2)(cn1
remove 6 from position 1,CCSc1cc(C(=O)N2CC[C@H]2(C)[C+H](O)C2)(cn1
remove 2 from position 22,CCSc1cc(C(=O)N2CC[C@H](C)[C+H](O)C2)(cn1
add c at position 5,CCSc1ccc(C(=O)N2CC[C@H](C)[C+H](O)C2)(cn1
remove ( from position 37,CCSc1ccc(C(=O)N2CC[C@H](C)[C+H](O)C2)cn1
replace + at position 28 with @,CCSc1ccc(C(=O)N2CC[C@H](C)[C@H](O)C2)cn1
final: CCSc1ccc(C(=O)N2CC[C@H](C)[C@H](O)C2)cn1,CCSc1ccc(C(=O)N2CC[C@H](C)[C@H](O)C2)cn1
