log,state
initialize: Cc1ccc([N+](=O)[O-])cc1C(=O)Nc1ccc(C(=O)NC(C)C)cc1,Cc1ccc([N+](=O)[O-])cc1C(=O)Nc1ccc(C(=O)NC(C)C)cc1
replace N at position 28 with +,Cc1ccc([N+](=O)[O-])cc1C(=O)+c1ccc(C(=O)NC(C)C)cc1
add ( at position 37,Cc1ccc([N+](=O)[O-])cc1C(=O)+c1ccc(C((=O)NC(C)C)cc1
remove c from position 5,Cc1cc([N+](=O)[O-])cc1C(=O)+c1ccc(C((=O)NC(C)C)cc1
add 2 at position 22,Cc1cc([N+](=O)[O-])cc12C(=O)+c1ccc(C((=O)NC(C)C)cc1
add # at position 50,Cc1cc([N+](=O)[O-])cc12C(=O)+c1ccc(C((=O)NC(C)C)cc#1
remove O from position 39,Cc1cc([N+](=O)[O-])cc12C(=O)+c1ccc(C((=)NC(C)C)cc#1
remove c from position 29,Cc1cc([N+](=O)[O-])cc12C(=O)+1ccc(C((=)NC(C)C)cc#1
add S at position 11,Cc1cc([N+](S=O)[O-])cc12C(=O)+1ccc(C((=)NC(C)C)cc#1
add C at position 0,CCc1cc([N+](S=O)[O-])cc12C(=O)+1ccc(C((=)NC(C)C)cc#1
remove ) from position 15,CCc1cc([N+](S=O[O-])cc12C(=O)+1ccc(C((=)NC(C)C)cc#1
add r at position 34,CCc1cc([N+](S=O[O-])cc12C(=O)+1cccr(C((=)NC(C)C)cc#1
remove ( from position 38,CCc1cc([N+](S=O[O-])cc12C(=O)+1cccr(C(=)NC(C)C)cc#1
remove c from position 32,CCc1cc([N+](S=O[O-])cc12C(=O)+1ccr(C(=)NC(C)C)cc#1
remove - from position 17,CCc1cc([N+](S=O[O])cc12C(=O)+1ccr(C(=)NC(C)C)cc#1
replace c at position 4 with N,CCc1Nc([N+](S=O[O])cc12C(=O)+1ccr(C(=)NC(C)C)cc#1
remove 2 from position 22,CCc1Nc([N+](S=O[O])cc1C(=O)+1ccr(C(=)NC(C)C)cc#1
remove ) from position 41,CCc1Nc([N+](S=O[O])cc1C(=O)+1ccr(C(=)NC(CC)cc#1
replace c at position 29 with r,CCc1Nc([N+](S=O[O])cc1C(=O)+1rcr(C(=)NC(CC)cc#1
remove C from position 1,Cc1Nc([N+](S=O[O])cc1C(=O)+1rcr(C(=)NC(CC)cc#1
replace c at position 4 with #,Cc1N#([N+](S=O[O])cc1C(=O)+1rcr(C(=)NC(CC)cc#1
remove 1 from position 45,Cc1N#([N+](S=O[O])cc1C(=O)+1rcr(C(=)NC(CC)cc#
remove O from position 24,Cc1N#([N+](S=O[O])cc1C(=)+1rcr(C(=)NC(CC)cc#
add H at position 4,Cc1NH#([N+](S=O[O])cc1C(=)+1rcr(C(=)NC(CC)cc#
remove H from position 4,Cc1N#([N+](S=O[O])cc1C(=)+1rcr(C(=)NC(CC)cc#
remove ] from position 9,Cc1N#([N+(S=O[O])cc1C(=)+1rcr(C(=)NC(CC)cc#
add ] at position 22,Cc1N#([N+(S=O[O])cc1C(]=)+1rcr(C(=)NC(CC)cc#
add - at position 33,Cc1N#([N+(S=O[O])cc1C(]=)+1rcr(C(-=)NC(CC)cc#
remove r from position 29,Cc1N#([N+(S=O[O])cc1C(]=)+1rc(C(-=)NC(CC)cc#
add O at position 17,Cc1N#([N+(S=O[O])Occ1C(]=)+1rc(C(-=)NC(CC)cc#
replace = at position 11 with 3,Cc1N#([N+(S3O[O])Occ1C(]=)+1rc(C(-=)NC(CC)cc#
add ] at position 9,Cc1N#([N+](S3O[O])Occ1C(]=)+1rc(C(-=)NC(CC)cc#
replace 1 at position 2 with I,CcIN#([N+](S3O[O])Occ1C(]=)+1rc(C(-=)NC(CC)cc#
remove C from position 22,CcIN#([N+](S3O[O])Occ1(]=)+1rc(C(-=)NC(CC)cc#
remove C from position 37,CcIN#([N+](S3O[O])Occ1(]=)+1rc(C(-=)N(CC)cc#
replace ( at position 30 with ),CcIN#([N+](S3O[O])Occ1(]=)+1rc)C(-=)N(CC)cc#
replace ) at position 30 with o,CcIN#([N+](S3O[O])Occ1(]=)+1rcoC(-=)N(CC)cc#
replace ( at position 10 with B,CcIN#([N+]BS3O[O])Occ1(]=)+1rcoC(-=)N(CC)cc#
remove - from position 33,CcIN#([N+]BS3O[O])Occ1(]=)+1rcoC(=)N(CC)cc#
replace O at position 18 with 3,CcIN#([N+]BS3O[O])3cc1(]=)+1rcoC(=)N(CC)cc#
add = at position 11,CcIN#([N+]B=S3O[O])3cc1(]=)+1rcoC(=)N(CC)cc#
remove N from position 7,CcIN#([+]B=S3O[O])3cc1(]=)+1rcoC(=)N(CC)cc#
replace [ at position 6 with n,CcIN#(n+]B=S3O[O])3cc1(]=)+1rcoC(=)N(CC)cc#
replace = at position 24 with n,CcIN#(n+]B=S3O[O])3cc1(]n)+1rcoC(=)N(CC)cc#
replace ] at position 16 with N,CcIN#(n+]B=S3O[ON)3cc1(]n)+1rcoC(=)N(CC)cc#
add S at position 6,CcIN#(Sn+]B=S3O[ON)3cc1(]n)+1rcoC(=)N(CC)cc#
remove B from position 10,CcIN#(Sn+]=S3O[ON)3cc1(]n)+1rcoC(=)N(CC)cc#
remove ) from position 17,CcIN#(Sn+]=S3O[ON3cc1(]n)+1rcoC(=)N(CC)cc#
replace 1 at position 20 with l,CcIN#(Sn+]=S3O[ON3ccl(]n)+1rcoC(=)N(CC)cc#
replace # at position 4 with +,CcIN+(Sn+]=S3O[ON3ccl(]n)+1rcoC(=)N(CC)cc#
remove N from position 3,CcI+(Sn+]=S3O[ON3ccl(]n)+1rcoC(=)N(CC)cc#
replace I at position 2 with 2,Cc2+(Sn+]=S3O[ON3ccl(]n)+1rcoC(=)N(CC)cc#
remove S from position 5,Cc2+(n+]=S3O[ON3ccl(]n)+1rcoC(=)N(CC)cc#
replace ] at position 20 with 4,Cc2+(n+]=S3O[ON3ccl(4n)+1rcoC(=)N(CC)cc#
replace ( at position 4 with I,Cc2+In+]=S3O[ON3ccl(4n)+1rcoC(=)N(CC)cc#
remove I from position 4,Cc2+n+]=S3O[ON3ccl(4n)+1rcoC(=)N(CC)cc#
remove O from position 12,Cc2+n+]=S3O[N3ccl(4n)+1rcoC(=)N(CC)cc#
add S at position 12,Cc2+n+]=S3O[SN3ccl(4n)+1rcoC(=)N(CC)cc#
replace = at position 29 with F,Cc2+n+]=S3O[SN3ccl(4n)+1rcoC(F)N(CC)cc#
add r at position 23,Cc2+n+]=S3O[SN3ccl(4n)+r1rcoC(F)N(CC)cc#
replace N at position 32 with r,Cc2+n+]=S3O[SN3ccl(4n)+r1rcoC(F)r(CC)cc#
remove [ from position 11,Cc2+n+]=S3OSN3ccl(4n)+r1rcoC(F)r(CC)cc#
replace 3 at position 13 with 5,Cc2+n+]=S3OSN5ccl(4n)+r1rcoC(F)r(CC)cc#
add F at position 22,Cc2+n+]=S3OSN5ccl(4n)+Fr1rcoC(F)r(CC)cc#
add B at position 35,Cc2+n+]=S3OSN5ccl(4n)+Fr1rcoC(F)r(CBC)cc#
remove S from position 8,Cc2+n+]=3OSN5ccl(4n)+Fr1rcoC(F)r(CBC)cc#
add r at position 10,Cc2+n+]=3OrSN5ccl(4n)+Fr1rcoC(F)r(CBC)cc#
add ) at position 34,Cc2+n+]=3OrSN5ccl(4n)+Fr1rcoC(F)r()CBC)cc#
remove r from position 10,Cc2+n+]=3OSN5ccl(4n)+Fr1rcoC(F)r()CBC)cc#
add N at position 3,Cc2N+n+]=3OSN5ccl(4n)+Fr1rcoC(F)r()CBC)cc#
add r at position 9,Cc2N+n+]=r3OSN5ccl(4n)+Fr1rcoC(F)r()CBC)cc#
add 7 at position 27,Cc2N+n+]=r3OSN5ccl(4n)+Fr1r7coC(F)r()CBC)cc#
remove N from position 13,Cc2N+n+]=r3OS5ccl(4n)+Fr1r7coC(F)r()CBC)cc#
replace 1 at position 24 with s,Cc2N+n+]=r3OS5ccl(4n)+Frsr7coC(F)r()CBC)cc#
replace ( at position 17 with H,Cc2N+n+]=r3OS5cclH4n)+Frsr7coC(F)r()CBC)cc#
remove n from position 19,Cc2N+n+]=r3OS5cclH4)+Frsr7coC(F)r()CBC)cc#
remove B from position 36,Cc2N+n+]=r3OS5cclH4)+Frsr7coC(F)r()CC)cc#
replace ) at position 34 with +,Cc2N+n+]=r3OS5cclH4)+Frsr7coC(F)r(+CC)cc#
replace H at position 17 with =,Cc2N+n+]=r3OS5ccl=4)+Frsr7coC(F)r(+CC)cc#
remove C from position 36,Cc2N+n+]=r3OS5ccl=4)+Frsr7coC(F)r(+C)cc#
remove ] from position 7,Cc2N+n+=r3OS5ccl=4)+Frsr7coC(F)r(+C)cc#
remove 4 from position 17,Cc2N+n+=r3OS5ccl=)+Frsr7coC(F)r(+C)cc#
add 7 at position 37,Cc2N+n+=r3OS5ccl=)+Frsr7coC(F)r(+C)cc7#
add 2 at position 28,Cc2N+n+=r3OS5ccl=)+Frsr7coC(2F)r(+C)cc7#
replace O at position 10 with 7,Cc2N+n+=r37S5ccl=)+Frsr7coC(2F)r(+C)cc7#
remove + from position 4,Cc2Nn+=r37S5ccl=)+Frsr7coC(2F)r(+C)cc7#
remove r from position 19,Cc2Nn+=r37S5ccl=)+Fsr7coC(2F)r(+C)cc7#
remove ( from position 30,Cc2Nn+=r37S5ccl=)+Fsr7coC(2F)r+C)cc7#
add / at position 30,Cc2Nn+=r37S5ccl=)+Fsr7coC(2F)r/+C)cc7#
remove o from position 23,Cc2Nn+=r37S5ccl=)+Fsr7cC(2F)r/+C)cc7#
remove c from position 22,Cc2Nn+=r37S5ccl=)+Fsr7C(2F)r/+C)cc7#
add r at position 33,Cc2Nn+=r37S5ccl=)+Fsr7C(2F)r/+C)crc7#
remove C from position 30,Cc2Nn+=r37S5ccl=)+Fsr7C(2F)r/+)crc7#
remove + from position 29,Cc2Nn+=r37S5ccl=)+Fsr7C(2F)r/)crc7#
remove = from position 6,Cc2Nn+r37S5ccl=)+Fsr7C(2F)r/)crc7#
add ) at position 30,Cc2Nn+r37S5ccl=)+Fsr7C(2F)r/)c)rc7#
replace C at position 21 with 6,Cc2Nn+r37S5ccl=)+Fsr76(2F)r/)c)rc7#
replace s at position 18 with 4,Cc2Nn+r37S5ccl=)+F4r76(2F)r/)c)rc7#
remove 6 from position 21,Cc2Nn+r37S5ccl=)+F4r7(2F)r/)c)rc7#
remove 4 from position 18,Cc2Nn+r37S5ccl=)+Fr7(2F)r/)c)rc7#
remove # from position 32,Cc2Nn+r37S5ccl=)+Fr7(2F)r/)c)rc7
replace F at position 17 with ),Cc2Nn+r37S5ccl=)+)r7(2F)r/)c)rc7
remove l from position 13,Cc2Nn+r37S5cc=)+)r7(2F)r/)c)rc7
remove 5 from position 10,Cc2Nn+r37Scc=)+)r7(2F)r/)c)rc7
replace + at position 14 with 4,Cc2Nn+r37Scc=)4)r7(2F)r/)c)rc7
add H at position 16,Cc2Nn+r37Scc=)4)Hr7(2F)r/)c)rc7
remove N from position 3,Cc2n+r37Scc=)4)Hr7(2F)r/)c)rc7
replace S at position 8 with 6,Cc2n+r376cc=)4)Hr7(2F)r/)c)rc7
add 7 at position 0,7Cc2n+r376cc=)4)Hr7(2F)r/)c)rc7
add 1 at position 2,7C1c2n+r376cc=)4)Hr7(2F)r/)c)rc7
remove ( from position 20,7C1c2n+r376cc=)4)Hr72F)r/)c)rc7
remove = from position 13,7C1c2n+r376cc)4)Hr72F)r/)c)rc7
replace 7 at position 29 with ),7C1c2n+r376cc)4)Hr72F)r/)c)rc)
replace 7 at position 9 with 3,7C1c2n+r336cc)4)Hr72F)r/)c)rc)
remove ) from position 29,7C1c2n+r336cc)4)Hr72F)r/)c)rc
remove c from position 11,7C1c2n+r336c)4)Hr72F)r/)c)rc
remove ) from position 20,7C1c2n+r336c)4)Hr72Fr/)c)rc
replace 7 at position 0 with S,SC1c2n+r336c)4)Hr72Fr/)c)rc
remove 2 from position 18,SC1c2n+r336c)4)Hr7Fr/)c)rc
add B at position 15,SC1c2n+r336c)4)BHr7Fr/)c)rc
remove / from position 21,SC1c2n+r336c)4)BHr7Fr)c)rc
replace 2 at position 4 with r,SC1crn+r336c)4)BHr7Fr)c)rc
remove F from position 19,SC1crn+r336c)4)BHr7r)c)rc
add s at position 17,SC1crn+r336c)4)BHsr7r)c)rc
replace c at position 22 with /,SC1crn+r336c)4)BHsr7r)/)rc
replace r at position 24 with C,SC1crn+r336c)4)BHsr7r)/)Cc
remove ) from position 12,SC1crn+r336c4)BHsr7r)/)Cc
replace 6 at position 10 with -,SC1crn+r33-c4)BHsr7r)/)Cc
remove r from position 17,SC1crn+r33-c4)BHs7r)/)Cc
remove ) from position 19,SC1crn+r33-c4)BHs7r/)Cc
remove B from position 14,SC1crn+r33-c4)Hs7r/)Cc
add r at position 2,SCr1crn+r33-c4)Hs7r/)Cc
remove C from position 21,SCr1crn+r33-c4)Hs7r/)c
remove C from position 1,Sr1crn+r33-c4)Hs7r/)c
remove 1 from position 2,Srcrn+r33-c4)Hs7r/)c
remove c from position 19,Srcrn+r33-c4)Hs7r/)
replace 4 at position 11 with F,Srcrn+r33-cF)Hs7r/)
remove ) from position 12,Srcrn+r33-cFHs7r/)
remove r from position 1,Scrn+r33-cFHs7r/)
add l at position 2,Sclrn+r33-cFHs7r/)
remove c from position 10,Sclrn+r33-FHs7r/)
add S at position 7,Sclrn+rS33-FHs7r/)
replace S at position 0 with o,oclrn+rS33-FHs7r/)
remove F from position 11,oclrn+rS33-Hs7r/)
remove l from position 2,ocrn+rS33-Hs7r/)
remove S from position 6,ocrn+r33-Hs7r/)
replace / at position 13 with N,ocrn+r33-Hs7rN)
remove 7 from position 11,ocrn+r33-HsrN)
add O at position 2,ocOrn+r33-HsrN)
replace H at position 10 with ],ocOrn+r33-]srN)
add 6 at position 14,ocOrn+r33-]srN6)
remove s from position 11,ocOrn+r33-]rN6)
remove r from position 6,ocOrn+33-]rN6)
replace r at position 3 with 4,ocO4n+33-]rN6)
add H at position 5,ocO4nH+33-]rN6)
replace c at position 1 with F,oFO4nH+33-]rN6)
remove 6 from position 13,oFO4nH+33-]rN)
add H at position 9,oFO4nH+33H-]rN)
remove O from position 2,oF4nH+33H-]rN)
remove + from position 5,oF4nH33H-]rN)
replace H at position 4 with [,oF4n[33H-]rN)
remove 4 from position 2,oFn[33H-]rN)
remove n from position 2,oF[33H-]rN)
replace r at position 8 with S,oF[33H-]SN)
remove 3 from position 3,oF[3H-]SN)
remove [ from position 2,oF3H-]SN)
remove S from position 6,oF3H-]N)
remove ) from position 7,oF3H-]N
replace - at position 4 with B,oF3HB]N
remove N from position 6,oF3HB]
remove B from position 4,oF3H]
add C at position 5,oF3H]C
replace ] at position 4 with -,oF3H-C
add B at position 3,oF3BH-C
add c at position 2,oFc3BH-C
replace 3 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
