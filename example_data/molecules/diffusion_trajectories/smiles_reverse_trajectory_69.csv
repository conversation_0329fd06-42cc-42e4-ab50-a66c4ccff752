log,state
initialize: ,
add S at position 0,S
add O at position 0,OS
add o at position 0,o<PERSON>
replace S at position 2 with [,oO[
add [ at position 1,o[O[
add 1 at position 1,o1[O[
replace [ at position 2 with 6,o16O[
remove 6 from position 2,o1O[
add I at position 4,o1O[I
replace I at position 4 with (,o1O[(
remove ( from position 4,o1O[
add 5 at position 2,o15O[
remove o from position 0,15O[
add F at position 2,15FO[
add / at position 3,15F/O[
add I at position 5,15F/OI[
add C at position 7,15F/OI[C
replace O at position 4 with 3,15F/3I[C
replace F at position 2 with B,15B/3I[C
remove 1 from position 0,5B/3I[C
add = at position 2,5B=/3I[C
replace I at position 5 with 3,5B=/33[C
add B at position 0,B5B=/33[C
add 6 at position 3,B5B6=/33[C
replace B at position 2 with o,B5o6=/33[C
replace = at position 4 with r,B5o6r/33[C
replace B at position 0 with 1,15o6r/33[C
replace C at position 9 with /,15o6r/33[/
replace [ at position 8 with ),15o6r/33)/
remove 6 from position 3,15or/33)/
replace 5 at position 1 with r,1ror/33)/
add - at position 6,1ror/3-3)/
remove r from position 1,1or/3-3)/
add H at position 7,1or/3-3H)/
add B at position 9,1or/3-3H)B/
add S at position 8,1or/3-3HS)B/
replace - at position 5 with 7,1or/373HS)B/
replace S at position 8 with s,1or/373Hs)B/
replace / at position 11 with c,1or/373Hs)Bc
remove s from position 8,1or/373H)Bc
add 5 at position 9,1or/373H)5Bc
replace r at position 2 with ],1o]/373H)5Bc
add 5 at position 10,1o]/373H)55Bc
replace / at position 3 with c,1o]c373H)55Bc
remove 5 from position 9,1o]c373H)5Bc
add s at position 12,1o]c373H)5Bcs
remove ) from position 8,1o]c373H5Bcs
add @ at position 9,1o]c373H5@Bcs
replace 3 at position 4 with N,1o]cN73H5@Bcs
replace o at position 1 with 7,17]cN73H5@Bcs
add 1 at position 6,17]cN713H5@Bcs
add ) at position 5,17]cN)713H5@Bcs
remove 1 from position 0,7]cN)713H5@Bcs
remove 7 from position 0,]cN)713H5@Bcs
replace 7 at position 4 with (,]cN)(13H5@Bcs
add ( at position 1,](cN)(13H5@Bcs
remove H from position 8,](cN)(135@Bcs
replace 3 at position 7 with r,](cN)(1r5@Bcs
remove 5 from position 8,](cN)(1r@Bcs
add / at position 9,](cN)(1r@/Bcs
replace ) at position 4 with ],](cN](1r@/Bcs
add 4 at position 8,](cN](1r4@/Bcs
add o at position 4,](cNo](1r4@/Bcs
add 7 at position 10,](cNo](1r47@/Bcs
replace 4 at position 9 with ],](cNo](1r]7@/Bcs
replace 7 at position 10 with +,](cNo](1r]+@/Bcs
remove o from position 4,](cN](1r]+@/Bcs
remove B from position 12,](cN](1r]+@/cs
add c at position 1,]c(cN](1r]+@/cs
replace @ at position 11 with s,]c(cN](1r]+s/cs
add # at position 0,#]c(cN](1r]+s/cs
remove c from position 14,#]c(cN](1r]+s/s
add 4 at position 11,#]c(cN](1r]4+s/s
add ( at position 11,#]c(cN](1r](4+s/s
remove / from position 15,#]c(cN](1r](4+ss
add / at position 15,#]c(cN](1r](4+s/s
remove 4 from position 12,#]c(cN](1r](+s/s
add I at position 0,I#]c(cN](1r](+s/s
remove / from position 15,I#]c(cN](1r](+ss
add r at position 14,I#]c(cN](1r](+rss
remove ( from position 12,I#]c(cN](1r]+rss
add s at position 8,I#]c(cN]s(1r]+rss
add c at position 3,I#]cc(cN]s(1r]+rss
add o at position 18,I#]cc(cN]s(1r]+rsso
add l at position 13,I#]cc(cN]s(1rl]+rsso
replace ] at position 2 with l,I#lcc(cN]s(1rl]+rsso
add o at position 4,I#lcoc(cN]s(1rl]+rsso
add 6 at position 21,I#lcoc(cN]s(1rl]+rsso6
add F at position 9,I#lcoc(cNF]s(1rl]+rsso6
add c at position 8,I#lcoc(ccNF]s(1rl]+rsso6
add 5 at position 21,I#lcoc(ccNF]s(1rl]+rs5so6
add c at position 23,I#lcoc(ccNF]s(1rl]+rs5sco6
replace ( at position 6 with 4,I#lcoc4ccNF]s(1rl]+rs5sco6
add 3 at position 24,I#lcoc4ccNF]s(1rl]+rs5sc3o6
add C at position 1,IC#lcoc4ccNF]s(1rl]+rs5sc3o6
add 6 at position 0,6IC#lcoc4ccNF]s(1rl]+rs5sc3o6
add ) at position 22,6IC#lcoc4ccNF]s(1rl]+r)s5sc3o6
remove l from position 4,6IC#coc4ccNF]s(1rl]+r)s5sc3o6
remove I from position 1,6C#coc4ccNF]s(1rl]+r)s5sc3o6
add s at position 10,6C#coc4ccNsF]s(1rl]+r)s5sc3o6
remove s from position 24,6C#coc4ccNsF]s(1rl]+r)s5c3o6
remove F from position 11,6C#coc4ccNs]s(1rl]+r)s5c3o6
replace 4 at position 6 with n,6C#cocnccNs]s(1rl]+r)s5c3o6
add n at position 5,6C#concnccNs]s(1rl]+r)s5c3o6
replace r at position 16 with c,6C#concnccNs]s(1cl]+r)s5c3o6
replace s at position 11 with 1,6C#concnccN1]s(1cl]+r)s5c3o6
add O at position 7,6C#concOnccN1]s(1cl]+r)s5c3o6
add c at position 28,6C#concOnccN1]s(1cl]+r)s5c3oc6
remove 1 from position 12,6C#concOnccN]s(1cl]+r)s5c3oc6
remove s from position 13,6C#concOnccN](1cl]+r)s5c3oc6
remove n from position 8,6C#concOccN](1cl]+r)s5c3oc6
add = at position 14,6C#concOccN](1=cl]+r)s5c3oc6
remove 5 from position 22,6C#concOccN](1=cl]+r)sc3oc6
remove l from position 16,6C#concOccN](1=c]+r)sc3oc6
remove c from position 9,6C#concOcN](1=c]+r)sc3oc6
remove 1 from position 12,6C#concOcN](=c]+r)sc3oc6
replace ] at position 14 with 7,6C#concOcN](=c7+r)sc3oc6
replace 6 at position 23 with -,6C#concOcN](=c7+r)sc3oc-
replace o at position 4 with 4,6C#c4ncOcN](=c7+r)sc3oc-
replace s at position 18 with c,6C#c4ncOcN](=c7+r)cc3oc-
remove c from position 22,6C#c4ncOcN](=c7+r)cc3o-
add r at position 11,6C#c4ncOcN]r(=c7+r)cc3o-
replace 3 at position 21 with ],6C#c4ncOcN]r(=c7+r)cc]o-
replace 7 at position 15 with c,6C#c4ncOcN]r(=cc+r)cc]o-
replace + at position 16 with c,6C#c4ncOcN]r(=cccr)cc]o-
replace = at position 13 with C,6C#c4ncOcN]r(Ccccr)cc]o-
add N at position 3,6C#Nc4ncOcN]r(Ccccr)cc]o-
add - at position 14,6C#Nc4ncOcN]r(-Ccccr)cc]o-
remove ] from position 23,6C#Nc4ncOcN]r(-Ccccr)cco-
replace r at position 19 with (,6C#Nc4ncOcN]r(-Cccc()cco-
add 1 at position 25,6C#Nc4ncOcN]r(-Cccc()cco-1
add C at position 14,6C#Nc4ncOcN]r(C-Cccc()cco-1
add ( at position 10,6C#Nc4ncOc(N]r(C-Cccc()cco-1
add c at position 24,6C#Nc4ncOc(N]r(C-Cccc()ccco-1
replace ( at position 14 with =,6C#Nc4ncOc(N]r=C-Cccc()ccco-1
remove - from position 27,6C#Nc4ncOc(N]r=C-Cccc()ccco1
replace 4 at position 5 with 1,6C#Nc1ncOc(N]r=C-Cccc()ccco1
remove O from position 8,6C#Nc1ncc(N]r=C-Cccc()ccco1
add O at position 14,6C#Nc1ncc(N]r=OC-Cccc()ccco1
remove - from position 16,6C#Nc1ncc(N]r=OCCccc()ccco1
remove ] from position 11,6C#Nc1ncc(Nr=OCCccc()ccco1
add C at position 4,6C#NCc1ncc(Nr=OCCccc()ccco1
add H at position 2,6CH#NCc1ncc(Nr=OCCccc()ccco1
remove H from position 2,6C#NCc1ncc(Nr=OCCccc()ccco1
add C at position 12,6C#NCc1ncc(NCr=OCCccc()ccco1
add N at position 22,6C#NCc1ncc(NCr=OCCccc(N)ccco1
replace # at position 2 with O,6CONCc1ncc(NCr=OCCccc(N)ccco1
add C at position 0,C6CONCc1ncc(NCr=OCCccc(N)ccco1
replace r at position 14 with (,C6CONCc1ncc(NC(=OCCccc(N)ccco1
add 2 at position 20,C6CONCc1ncc(NC(=OCCc2cc(N)ccco1
add c at position 22,C6CONCc1ncc(NC(=OCCc2ccc(N)ccco1
replace N at position 4 with S,C6COSCc1ncc(NC(=OCCc2ccc(N)ccco1
add # at position 17,C6COSCc1ncc(NC(=O#CCc2ccc(N)ccco1
remove C from position 5,C6COSc1ncc(NC(=O#CCc2ccc(N)ccco1
add + at position 29,C6COSc1ncc(NC(=O#CCc2ccc(N)cc+co1
replace S at position 4 with O,C6COOc1ncc(NC(=O#CCc2ccc(N)cc+co1
remove o from position 31,C6COOc1ncc(NC(=O#CCc2ccc(N)cc+c1
remove n from position 7,C6COOc1cc(NC(=O#CCc2ccc(N)cc+c1
add c at position 9,C6COOc1ccc(NC(=O#CCc2ccc(N)cc+c1
add ) at position 30,C6COOc1ccc(NC(=O#CCc2ccc(N)cc+)c1
remove 6 from position 1,CCOOc1ccc(NC(=O#CCc2ccc(N)cc+)c1
replace # at position 15 with ),CCOOc1ccc(NC(=O)CCc2ccc(N)cc+)c1
add c at position 6,CCOOc1cccc(NC(=O)CCc2ccc(N)cc+)c1
remove O from position 2,CCOc1cccc(NC(=O)CCc2ccc(N)cc+)c1
replace + at position 28 with 2,CCOc1cccc(NC(=O)CCc2ccc(N)cc2)c1
final: CCOc1cccc(NC(=O)CCc2ccc(N)cc2)c1,CCOc1cccc(NC(=O)CCc2ccc(N)cc2)c1
