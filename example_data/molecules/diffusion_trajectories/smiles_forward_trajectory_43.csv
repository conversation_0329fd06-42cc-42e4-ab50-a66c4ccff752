log,state
initialize: CC[C@@H](NC(=O)[C@H](C)n1cccn1)c1ccc(C)c(F)c1,CC[C@@H](NC(=O)[C@H](C)n1cccn1)c1ccc(C)c(F)c1
replace n at position 28 with +,CC[C@@H](NC(=O)[C@H](C)n1ccc+1)c1ccc(C)c(F)c1
add ( at position 37,CC[C@@H](NC(=O)[C@H](C)n1ccc+1)c1ccc((C)c(F)c1
remove @ from position 5,CC[C@H](NC(=O)[C@H](C)n1ccc+1)c1ccc((C)c(F)c1
add 2 at position 22,CC[C@H](NC(=O)[C@H](C)2n1ccc+1)c1ccc((C)c(F)c1
add 6 at position 1,C6C[C@H](NC(=O)[C@H](C)2n1ccc+1)c1ccc((C)c(F)c1
remove 1 from position 30,C6C[C@H](NC(=O)[C@H](C)2n1ccc+)c1ccc((C)c(F)c1
remove N from position 9,C6C[C@H](C(=O)[C@H](C)2n1ccc+)c1ccc((C)c(F)c1
add + at position 45,C6C[C@H](C(=O)[C@H](C)2n1ccc+)c1ccc((C)c(F)c1+
replace c at position 32 with B,C6C[C@H](C(=O)[C@H](C)2n1ccc+)c1Bcc((C)c(F)c1+
replace C at position 15 with ),C6C[C@H](C(=O)[)@H](C)2n1ccc+)c1Bcc((C)c(F)c1+
remove ) from position 29,C6C[C@H](C(=O)[)@H](C)2n1ccc+c1Bcc((C)c(F)c1+
add ) at position 43,C6C[C@H](C(=O)[)@H](C)2n1ccc+c1Bcc((C)c(F)c)1+
remove C from position 2,C6[C@H](C(=O)[)@H](C)2n1ccc+c1Bcc((C)c(F)c)1+
remove C from position 35,C6[C@H](C(=O)[)@H](C)2n1ccc+c1Bcc(()c(F)c)1+
add B at position 19,C6[C@H](C(=O)[)@H](BC)2n1ccc+c1Bcc(()c(F)c)1+
replace ] at position 17 with 2,C6[C@H](C(=O)[)@H2(BC)2n1ccc+c1Bcc(()c(F)c)1+
remove ) from position 12,C6[C@H](C(=O[)@H2(BC)2n1ccc+c1Bcc(()c(F)c)1+
remove ( from position 33,C6[C@H](C(=O[)@H2(BC)2n1ccc+c1Bcc()c(F)c)1+
remove c from position 32,C6[C@H](C(=O[)@H2(BC)2n1ccc+c1Bc()c(F)c)1+
add - at position 21,C6[C@H](C(=O[)@H2(BC)-2n1ccc+c1Bc()c(F)c)1+
remove + from position 42,C6[C@H](C(=O[)@H2(BC)-2n1ccc+c1Bc()c(F)c)1
remove C from position 3,C6[@H](C(=O[)@H2(BC)-2n1ccc+c1Bc()c(F)c)1
add / at position 31,C6[@H](C(=O[)@H2(BC)-2n1ccc+c1B/c()c(F)c)1
add - at position 13,C6[@H](C(=O[)-@H2(BC)-2n1ccc+c1B/c()c(F)c)1
add ] at position 22,C6[@H](C(=O[)-@H2(BC)-]2n1ccc+c1B/c()c(F)c)1
add - at position 33,C6[@H](C(=O[)-@H2(BC)-]2n1ccc+c1B-/c()c(F)c)1
remove + from position 29,C6[@H](C(=O[)-@H2(BC)-]2n1cccc1B-/c()c(F)c)1
add O at position 17,C6[@H](C(=O[)-@H2O(BC)-]2n1cccc1B-/c()c(F)c)1
replace [ at position 11 with 3,C6[@H](C(=O3)-@H2O(BC)-]2n1cccc1B-/c()c(F)c)1
add ] at position 9,C6[@H](C(]=O3)-@H2O(BC)-]2n1cccc1B-/c()c(F)c)1
replace [ at position 2 with H,C6H@H](C(]=O3)-@H2O(BC)-]2n1cccc1B-/c()c(F)c)1
remove ) from position 22,C6H@H](C(]=O3)-@H2O(BC-]2n1cccc1B-/c()c(F)c)1
remove ) from position 37,C6H@H](C(]=O3)-@H2O(BC-]2n1cccc1B-/c(c(F)c)1
replace c at position 30 with (,C6H@H](C(]=O3)-@H2O(BC-]2n1ccc(1B-/c(c(F)c)1
replace ( at position 30 with o,C6H@H](C(]=O3)-@H2O(BC-]2n1ccco1B-/c(c(F)c)1
replace = at position 10 with B,C6H@H](C(]BO3)-@H2O(BC-]2n1ccco1B-/c(c(F)c)1
remove - from position 33,C6H@H](C(]BO3)-@H2O(BC-]2n1ccco1B/c(c(F)c)1
replace O at position 18 with 3,C6H@H](C(]BO3)-@H23(BC-]2n1ccco1B/c(c(F)c)1
add = at position 11,C6H@H](C(]B=O3)-@H23(BC-]2n1ccco1B/c(c(F)c)1
remove C from position 7,C6H@H]((]B=O3)-@H23(BC-]2n1ccco1B/c(c(F)c)1
replace ( at position 6 with n,C6H@H]n(]B=O3)-@H23(BC-]2n1ccco1B/c(c(F)c)1
replace 2 at position 24 with n,C6H@H]n(]B=O3)-@H23(BC-]nn1ccco1B/c(c(F)c)1
replace H at position 16 with O,C6H@H]n(]B=O3)-@O23(BC-]nn1ccco1B/c(c(F)c)1
add S at position 6,C6H@H]Sn(]B=O3)-@O23(BC-]nn1ccco1B/c(c(F)c)1
remove B from position 10,C6H@H]Sn(]=O3)-@O23(BC-]nn1ccco1B/c(c(F)c)1
remove 2 from position 17,C6H@H]Sn(]=O3)-@O3(BC-]nn1ccco1B/c(c(F)c)1
replace C at position 20 with l,C6H@H]Sn(]=O3)-@O3(Bl-]nn1ccco1B/c(c(F)c)1
replace H at position 4 with ),C6H@)]Sn(]=O3)-@O3(Bl-]nn1ccco1B/c(c(F)c)1
remove @ from position 3,C6H)]Sn(]=O3)-@O3(Bl-]nn1ccco1B/c(c(F)c)1
replace H at position 2 with 2,C62)]Sn(]=O3)-@O3(Bl-]nn1ccco1B/c(c(F)c)1
remove S from position 5,C62)]n(]=O3)-@O3(Bl-]nn1ccco1B/c(c(F)c)1
replace ] at position 20 with 4,C62)]n(]=O3)-@O3(Bl-4nn1ccco1B/c(c(F)c)1
replace ] at position 4 with H,C62)Hn(]=O3)-@O3(Bl-4nn1ccco1B/c(c(F)c)1
remove H from position 4,C62)n(]=O3)-@O3(Bl-4nn1ccco1B/c(c(F)c)1
remove @ from position 12,C62)n(]=O3)-O3(Bl-4nn1ccco1B/c(c(F)c)1
add S at position 12,C62)n(]=O3)-SO3(Bl-4nn1ccco1B/c(c(F)c)1
replace / at position 29 with F,C62)n(]=O3)-SO3(Bl-4nn1ccco1BFc(c(F)c)1
add r at position 23,C62)n(]=O3)-SO3(Bl-4nn1rccco1BFc(c(F)c)1
replace ( at position 32 with r,C62)n(]=O3)-SO3(Bl-4nn1rccco1BFcrc(F)c)1
remove - from position 11,C62)n(]=O3)SO3(Bl-4nn1rccco1BFcrc(F)c)1
replace 3 at position 13 with 5,C62)n(]=O3)SO5(Bl-4nn1rccco1BFcrc(F)c)1
add F at position 22,C62)n(]=O3)SO5(Bl-4nn1Frccco1BFcrc(F)c)1
add B at position 35,C62)n(]=O3)SO5(Bl-4nn1Frccco1BFcrc(BF)c)1
remove O from position 8,C62)n(]=3)SO5(Bl-4nn1Frccco1BFcrc(BF)c)1
add r at position 10,C62)n(]=3)rSO5(Bl-4nn1Frccco1BFcrc(BF)c)1
add ) at position 34,C62)n(]=3)rSO5(Bl-4nn1Frccco1BFcrc)(BF)c)1
remove r from position 10,C62)n(]=3)SO5(Bl-4nn1Frccco1BFcrc)(BF)c)1
add N at position 3,C62N)n(]=3)SO5(Bl-4nn1Frccco1BFcrc)(BF)c)1
add r at position 9,C62N)n(]=r3)SO5(Bl-4nn1Frccco1BFcrc)(BF)c)1
add 7 at position 27,C62N)n(]=r3)SO5(Bl-4nn1Frcc7co1BFcrc)(BF)c)1
remove O from position 13,C62N)n(]=r3)S5(Bl-4nn1Frcc7co1BFcrc)(BF)c)1
replace c at position 24 with s,C62N)n(]=r3)S5(Bl-4nn1Frsc7co1BFcrc)(BF)c)1
replace - at position 17 with H,C62N)n(]=r3)S5(BlH4nn1Frsc7co1BFcrc)(BF)c)1
remove n from position 19,C62N)n(]=r3)S5(BlH4n1Frsc7co1BFcrc)(BF)c)1
remove B from position 36,C62N)n(]=r3)S5(BlH4n1Frsc7co1BFcrc)(F)c)1
replace ) at position 34 with +,C62N)n(]=r3)S5(BlH4n1Frsc7co1BFcrc+(F)c)1
replace H at position 17 with =,C62N)n(]=r3)S5(Bl=4n1Frsc7co1BFcrc+(F)c)1
remove F from position 36,C62N)n(]=r3)S5(Bl=4n1Frsc7co1BFcrc+()c)1
remove ] from position 7,C62N)n(=r3)S5(Bl=4n1Frsc7co1BFcrc+()c)1
remove 4 from position 17,C62N)n(=r3)S5(Bl=n1Frsc7co1BFcrc+()c)1
add 7 at position 37,C62N)n(=r3)S5(Bl=n1Frsc7co1BFcrc+()c)71
add 2 at position 28,C62N)n(=r3)S5(Bl=n1Frsc7co1B2Fcrc+()c)71
replace ) at position 10 with =,C62N)n(=r3=S5(Bl=n1Frsc7co1B2Fcrc+()c)71
remove ) from position 4,C62Nn(=r3=S5(Bl=n1Frsc7co1B2Fcrc+()c)71
remove r from position 19,C62Nn(=r3=S5(Bl=n1Fsc7co1B2Fcrc+()c)71
remove c from position 30,C62Nn(=r3=S5(Bl=n1Fsc7co1B2Fcr+()c)71
add / at position 30,C62Nn(=r3=S5(Bl=n1Fsc7co1B2Fcr/+()c)71
remove o from position 23,C62Nn(=r3=S5(Bl=n1Fsc7c1B2Fcr/+()c)71
remove c from position 22,C62Nn(=r3=S5(Bl=n1Fsc71B2Fcr/+()c)71
add r at position 33,C62Nn(=r3=S5(Bl=n1Fsc71B2Fcr/+()cr)71
remove ( from position 30,C62Nn(=r3=S5(Bl=n1Fsc71B2Fcr/+)cr)71
remove + from position 29,C62Nn(=r3=S5(Bl=n1Fsc71B2Fcr/)cr)71
remove = from position 6,C62Nn(r3=S5(Bl=n1Fsc71B2Fcr/)cr)71
add ) at position 30,C62Nn(r3=S5(Bl=n1Fsc71B2Fcr/)c)r)71
replace 1 at position 21 with 7,C62Nn(r3=S5(Bl=n1Fsc77B2Fcr/)c)r)71
replace s at position 18 with 4,C62Nn(r3=S5(Bl=n1F4c77B2Fcr/)c)r)71
remove 7 from position 21,C62Nn(r3=S5(Bl=n1F4c7B2Fcr/)c)r)71
remove 4 from position 18,C62Nn(r3=S5(Bl=n1Fc7B2Fcr/)c)r)71
remove 1 from position 32,C62Nn(r3=S5(Bl=n1Fc7B2Fcr/)c)r)7
replace F at position 17 with ),C62Nn(r3=S5(Bl=n1)c7B2Fcr/)c)r)7
remove l from position 13,C62Nn(r3=S5(B=n1)c7B2Fcr/)c)r)7
remove 5 from position 10,C62Nn(r3=S(B=n1)c7B2Fcr/)c)r)7
replace 1 at position 14 with 4,C62Nn(r3=S(B=n4)c7B2Fcr/)c)r)7
add H at position 16,C62Nn(r3=S(B=n4)Hc7B2Fcr/)c)r)7
remove N from position 3,C62n(r3=S(B=n4)Hc7B2Fcr/)c)r)7
replace S at position 8 with 6,C62n(r3=6(B=n4)Hc7B2Fcr/)c)r)7
add 7 at position 0,7C62n(r3=6(B=n4)Hc7B2Fcr/)c)r)7
add 1 at position 2,7C162n(r3=6(B=n4)Hc7B2Fcr/)c)r)7
remove B from position 20,7C162n(r3=6(B=n4)Hc72Fcr/)c)r)7
remove = from position 13,7C162n(r3=6(Bn4)Hc72Fcr/)c)r)7
replace 7 at position 29 with ),7C162n(r3=6(Bn4)Hc72Fcr/)c)r))
replace = at position 9 with 3,7C162n(r336(Bn4)Hc72Fcr/)c)r))
remove ) from position 29,7C162n(r336(Bn4)Hc72Fcr/)c)r)
remove ( from position 11,7C162n(r336Bn4)Hc72Fcr/)c)r)
remove c from position 20,7C162n(r336Bn4)Hc72Fr/)c)r)
replace 7 at position 0 with S,SC162n(r336Bn4)Hc72Fr/)c)r)
remove 2 from position 18,SC162n(r336Bn4)Hc7Fr/)c)r)
add B at position 15,SC162n(r336Bn4)BHc7Fr/)c)r)
remove / from position 21,SC162n(r336Bn4)BHc7Fr)c)r)
replace 2 at position 4 with r,SC16rn(r336Bn4)BHc7Fr)c)r)
remove F from position 19,SC16rn(r336Bn4)BHc7r)c)r)
add s at position 17,SC16rn(r336Bn4)BHsc7r)c)r)
replace c at position 22 with /,SC16rn(r336Bn4)BHsc7r)/)r)
replace r at position 24 with C,SC16rn(r336Bn4)BHsc7r)/)C)
remove n from position 12,SC16rn(r336B4)BHsc7r)/)C)
replace 6 at position 10 with -,SC16rn(r33-B4)BHsc7r)/)C)
remove c from position 17,SC16rn(r33-B4)BHs7r)/)C)
remove ) from position 19,SC16rn(r33-B4)BHs7r/)C)
remove B from position 14,SC16rn(r33-B4)Hs7r/)C)
add r at position 2,SCr16rn(r33-B4)Hs7r/)C)
remove C from position 21,SCr16rn(r33-B4)Hs7r/))
remove C from position 1,Sr16rn(r33-B4)Hs7r/))
remove 1 from position 2,Sr6rn(r33-B4)Hs7r/))
remove ) from position 19,Sr6rn(r33-B4)Hs7r/)
replace 4 at position 11 with F,Sr6rn(r33-BF)Hs7r/)
remove ) from position 12,Sr6rn(r33-BFHs7r/)
remove r from position 1,S6rn(r33-BFHs7r/)
add l at position 2,S6lrn(r33-BFHs7r/)
remove B from position 10,S6lrn(r33-FHs7r/)
add S at position 7,S6lrn(rS33-FHs7r/)
replace S at position 0 with o,o6lrn(rS33-FHs7r/)
remove F from position 11,o6lrn(rS33-Hs7r/)
remove l from position 2,o6rn(rS33-Hs7r/)
remove S from position 6,o6rn(r33-Hs7r/)
replace / at position 13 with N,o6rn(r33-Hs7rN)
remove 7 from position 11,o6rn(r33-HsrN)
add O at position 2,o6Orn(r33-HsrN)
replace H at position 10 with ],o6Orn(r33-]srN)
add 6 at position 14,o6Orn(r33-]srN6)
remove s from position 11,o6Orn(r33-]rN6)
remove r from position 6,o6Orn(33-]rN6)
replace r at position 3 with 4,o6O4n(33-]rN6)
add H at position 5,o6O4nH(33-]rN6)
replace 6 at position 1 with H,oHO4nH(33-]rN6)
remove 6 from position 13,oHO4nH(33-]rN)
add H at position 9,oHO4nH(33H-]rN)
remove O from position 2,oH4nH(33H-]rN)
remove ( from position 5,oH4nH33H-]rN)
replace H at position 4 with [,oH4n[33H-]rN)
remove 4 from position 2,oHn[33H-]rN)
remove n from position 2,oH[33H-]rN)
replace r at position 8 with S,oH[33H-]SN)
remove 3 from position 3,oH[3H-]SN)
remove [ from position 2,oH3H-]SN)
remove S from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
