log,state
initialize: CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)cc1,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)cc1
replace c at position 28 with +,CCc1ccc(CNC(=O)c2ccc(-c3nccn+3N3CCCCC3)cc2)cc1
add ( at position 37,CCc1ccc(CNC(=O)c2ccc(-c3nccn+3N3CCCCC(3)cc2)cc1
remove c from position 5,CCc1cc(CNC(=O)c2ccc(-c3nccn+3N3CCCCC(3)cc2)cc1
add 2 at position 22,CCc1cc(CNC(=O)c2ccc(-c23nccn+3N3CCCCC(3)cc2)cc1
add 6 at position 1,C6Cc1cc(CNC(=O)c2ccc(-c23nccn+3N3CCCCC(3)cc2)cc1
remove 3 from position 30,C6Cc1cc(CNC(=O)c2ccc(-c23nccn+N3CCCCC(3)cc2)cc1
remove N from position 9,C6Cc1cc(CC(=O)c2ccc(-c23nccn+N3CCCCC(3)cc2)cc1
add + at position 45,C6Cc1cc(CC(=O)c2ccc(-c23nccn+N3CCCCC(3)cc2)cc+1
replace C at position 32 with B,C6Cc1cc(CC(=O)c2ccc(-c23nccn+N3CBCCC(3)cc2)cc+1
replace 2 at position 15 with ),C6Cc1cc(CC(=O)c)ccc(-c23nccn+N3CBCCC(3)cc2)cc+1
remove N from position 29,C6Cc1cc(CC(=O)c)ccc(-c23nccn+3CBCCC(3)cc2)cc+1
add ) at position 43,C6Cc1cc(CC(=O)c)ccc(-c23nccn+3CBCCC(3)cc2)c)c+1
remove C from position 2,C6c1cc(CC(=O)c)ccc(-c23nccn+3CBCCC(3)cc2)c)c+1
remove 3 from position 35,C6c1cc(CC(=O)c)ccc(-c23nccn+3CBCCC()cc2)c)c+1
add B at position 19,C6c1cc(CC(=O)c)ccc(B-c23nccn+3CBCCC()cc2)c)c+1
replace c at position 17 with 2,C6c1cc(CC(=O)c)cc2(B-c23nccn+3CBCCC()cc2)c)c+1
remove ) from position 12,C6c1cc(CC(=Oc)cc2(B-c23nccn+3CBCCC()cc2)c)c+1
remove C from position 33,C6c1cc(CC(=Oc)cc2(B-c23nccn+3CBCC()cc2)c)c+1
remove C from position 32,C6c1cc(CC(=Oc)cc2(B-c23nccn+3CBC()cc2)c)c+1
add - at position 21,C6c1cc(CC(=Oc)cc2(B-c-23nccn+3CBC()cc2)c)c+1
remove + from position 42,C6c1cc(CC(=Oc)cc2(B-c-23nccn+3CBC()cc2)c)c1
remove 1 from position 3,C6ccc(CC(=Oc)cc2(B-c-23nccn+3CBC()cc2)c)c1
add / at position 31,C6ccc(CC(=Oc)cc2(B-c-23nccn+3CB/C()cc2)c)c1
add - at position 13,C6ccc(CC(=Oc)-cc2(B-c-23nccn+3CB/C()cc2)c)c1
add ] at position 22,C6ccc(CC(=Oc)-cc2(B-c-]23nccn+3CB/C()cc2)c)c1
add - at position 33,C6ccc(CC(=Oc)-cc2(B-c-]23nccn+3CB-/C()cc2)c)c1
remove + from position 29,C6ccc(CC(=Oc)-cc2(B-c-]23nccn3CB-/C()cc2)c)c1
add O at position 17,C6ccc(CC(=Oc)-cc2O(B-c-]23nccn3CB-/C()cc2)c)c1
replace c at position 11 with 3,C6ccc(CC(=O3)-cc2O(B-c-]23nccn3CB-/C()cc2)c)c1
add ] at position 9,C6ccc(CC(]=O3)-cc2O(B-c-]23nccn3CB-/C()cc2)c)c1
replace c at position 2 with H,C6Hcc(CC(]=O3)-cc2O(B-c-]23nccn3CB-/C()cc2)c)c1
remove c from position 22,C6Hcc(CC(]=O3)-cc2O(B--]23nccn3CB-/C()cc2)c)c1
remove ) from position 37,C6Hcc(CC(]=O3)-cc2O(B--]23nccn3CB-/C(cc2)c)c1
replace 3 at position 30 with (,C6Hcc(CC(]=O3)-cc2O(B--]23nccn(CB-/C(cc2)c)c1
replace ( at position 30 with o,C6Hcc(CC(]=O3)-cc2O(B--]23nccnoCB-/C(cc2)c)c1
replace = at position 10 with B,C6Hcc(CC(]BO3)-cc2O(B--]23nccnoCB-/C(cc2)c)c1
remove - from position 33,C6Hcc(CC(]BO3)-cc2O(B--]23nccnoCB/C(cc2)c)c1
replace O at position 18 with 3,C6Hcc(CC(]BO3)-cc23(B--]23nccnoCB/C(cc2)c)c1
add = at position 11,C6Hcc(CC(]B=O3)-cc23(B--]23nccnoCB/C(cc2)c)c1
remove C from position 7,C6Hcc(C(]B=O3)-cc23(B--]23nccnoCB/C(cc2)c)c1
replace C at position 6 with n,C6Hcc(n(]B=O3)-cc23(B--]23nccnoCB/C(cc2)c)c1
replace 2 at position 24 with n,C6Hcc(n(]B=O3)-cc23(B--]n3nccnoCB/C(cc2)c)c1
replace c at position 16 with N,C6Hcc(n(]B=O3)-cN23(B--]n3nccnoCB/C(cc2)c)c1
add S at position 6,C6Hcc(Sn(]B=O3)-cN23(B--]n3nccnoCB/C(cc2)c)c1
remove B from position 10,C6Hcc(Sn(]=O3)-cN23(B--]n3nccnoCB/C(cc2)c)c1
remove 2 from position 17,C6Hcc(Sn(]=O3)-cN3(B--]n3nccnoCB/C(cc2)c)c1
replace - at position 20 with l,C6Hcc(Sn(]=O3)-cN3(Bl-]n3nccnoCB/C(cc2)c)c1
replace c at position 4 with ),C6Hc)(Sn(]=O3)-cN3(Bl-]n3nccnoCB/C(cc2)c)c1
remove c from position 3,C6H)(Sn(]=O3)-cN3(Bl-]n3nccnoCB/C(cc2)c)c1
replace H at position 2 with 2,C62)(Sn(]=O3)-cN3(Bl-]n3nccnoCB/C(cc2)c)c1
remove S from position 5,C62)(n(]=O3)-cN3(Bl-]n3nccnoCB/C(cc2)c)c1
replace ] at position 20 with 4,C62)(n(]=O3)-cN3(Bl-4n3nccnoCB/C(cc2)c)c1
replace ( at position 4 with I,C62)In(]=O3)-cN3(Bl-4n3nccnoCB/C(cc2)c)c1
remove I from position 4,C62)n(]=O3)-cN3(Bl-4n3nccnoCB/C(cc2)c)c1
remove c from position 12,C62)n(]=O3)-N3(Bl-4n3nccnoCB/C(cc2)c)c1
add S at position 12,C62)n(]=O3)-SN3(Bl-4n3nccnoCB/C(cc2)c)c1
replace / at position 29 with F,C62)n(]=O3)-SN3(Bl-4n3nccnoCBFC(cc2)c)c1
add r at position 23,C62)n(]=O3)-SN3(Bl-4n3nrccnoCBFC(cc2)c)c1
replace ( at position 32 with r,C62)n(]=O3)-SN3(Bl-4n3nrccnoCBFCrcc2)c)c1
remove - from position 11,C62)n(]=O3)SN3(Bl-4n3nrccnoCBFCrcc2)c)c1
replace 3 at position 13 with 5,C62)n(]=O3)SN5(Bl-4n3nrccnoCBFCrcc2)c)c1
add F at position 22,C62)n(]=O3)SN5(Bl-4n3nFrccnoCBFCrcc2)c)c1
add B at position 35,C62)n(]=O3)SN5(Bl-4n3nFrccnoCBFCrccB2)c)c1
remove O from position 8,C62)n(]=3)SN5(Bl-4n3nFrccnoCBFCrccB2)c)c1
add r at position 10,C62)n(]=3)rSN5(Bl-4n3nFrccnoCBFCrccB2)c)c1
add ) at position 34,C62)n(]=3)rSN5(Bl-4n3nFrccnoCBFCrc)cB2)c)c1
remove r from position 10,C62)n(]=3)SN5(Bl-4n3nFrccnoCBFCrc)cB2)c)c1
add N at position 3,C62N)n(]=3)SN5(Bl-4n3nFrccnoCBFCrc)cB2)c)c1
add r at position 9,C62N)n(]=r3)SN5(Bl-4n3nFrccnoCBFCrc)cB2)c)c1
add 7 at position 27,C62N)n(]=r3)SN5(Bl-4n3nFrcc7noCBFCrc)cB2)c)c1
remove N from position 13,C62N)n(]=r3)S5(Bl-4n3nFrcc7noCBFCrc)cB2)c)c1
replace 1 at position 43 with =,C62N)n(]=r3)S5(Bl-4n3nFrcc7noCBFCrc)cB2)c)c=
replace - at position 17 with H,C62N)n(]=r3)S5(BlH4n3nFrcc7noCBFCrc)cB2)c)c=
remove n from position 19,C62N)n(]=r3)S5(BlH43nFrcc7noCBFCrc)cB2)c)c=
remove B from position 36,C62N)n(]=r3)S5(BlH43nFrcc7noCBFCrc)c2)c)c=
replace ) at position 34 with +,C62N)n(]=r3)S5(BlH43nFrcc7noCBFCrc+c2)c)c=
replace H at position 17 with =,C62N)n(]=r3)S5(Bl=43nFrcc7noCBFCrc+c2)c)c=
remove 2 from position 36,C62N)n(]=r3)S5(Bl=43nFrcc7noCBFCrc+c)c)c=
remove ] from position 7,C62N)n(=r3)S5(Bl=43nFrcc7noCBFCrc+c)c)c=
remove 4 from position 17,C62N)n(=r3)S5(Bl=3nFrcc7noCBFCrc+c)c)c=
add 7 at position 37,C62N)n(=r3)S5(Bl=3nFrcc7noCBFCrc+c)c)7c=
add 2 at position 28,C62N)n(=r3)S5(Bl=3nFrcc7noCB2FCrc+c)c)7c=
replace ) at position 10 with =,C62N)n(=r3=S5(Bl=3nFrcc7noCB2FCrc+c)c)7c=
remove ) from position 4,C62Nn(=r3=S5(Bl=3nFrcc7noCB2FCrc+c)c)7c=
remove r from position 19,C62Nn(=r3=S5(Bl=3nFcc7noCB2FCrc+c)c)7c=
remove c from position 30,C62Nn(=r3=S5(Bl=3nFcc7noCB2FCr+c)c)7c=
add / at position 30,C62Nn(=r3=S5(Bl=3nFcc7noCB2FCr/+c)c)7c=
remove o from position 23,C62Nn(=r3=S5(Bl=3nFcc7nCB2FCr/+c)c)7c=
remove n from position 22,C62Nn(=r3=S5(Bl=3nFcc7CB2FCr/+c)c)7c=
add r at position 33,C62Nn(=r3=S5(Bl=3nFcc7CB2FCr/+c)cr)7c=
remove c from position 30,C62Nn(=r3=S5(Bl=3nFcc7CB2FCr/+)cr)7c=
remove + from position 29,C62Nn(=r3=S5(Bl=3nFcc7CB2FCr/)cr)7c=
remove = from position 6,C62Nn(r3=S5(Bl=3nFcc7CB2FCr/)cr)7c=
add ) at position 30,C62Nn(r3=S5(Bl=3nFcc7CB2FCr/)c)r)7c=
replace C at position 21 with 6,C62Nn(r3=S5(Bl=3nFcc76B2FCr/)c)r)7c=
replace c at position 18 with 4,C62Nn(r3=S5(Bl=3nF4c76B2FCr/)c)r)7c=
remove 6 from position 21,C62Nn(r3=S5(Bl=3nF4c7B2FCr/)c)r)7c=
remove 4 from position 18,C62Nn(r3=S5(Bl=3nFc7B2FCr/)c)r)7c=
remove c from position 32,C62Nn(r3=S5(Bl=3nFc7B2FCr/)c)r)7=
replace F at position 17 with ),C62Nn(r3=S5(Bl=3n)c7B2FCr/)c)r)7=
remove l from position 13,C62Nn(r3=S5(B=3n)c7B2FCr/)c)r)7=
remove F from position 21,C62Nn(r3=S5(B=3n)c7B2Cr/)c)r)7=
replace 3 at position 14 with 4,C62Nn(r3=S5(B=4n)c7B2Cr/)c)r)7=
add l at position 7,C62Nn(rl3=S5(B=4n)c7B2Cr/)c)r)7=
add - at position 23,C62Nn(rl3=S5(B=4n)c7B2C-r/)c)r)7=
replace / at position 25 with (,C62Nn(rl3=S5(B=4n)c7B2C-r()c)r)7=
remove 2 from position 2,C6Nn(rl3=S5(B=4n)c7B2C-r()c)r)7=
replace S at position 9 with 5,C6Nn(rl3=55(B=4n)c7B2C-r()c)r)7=
remove ) from position 27,C6Nn(rl3=55(B=4n)c7B2C-r()cr)7=
replace 7 at position 29 with ),C6Nn(rl3=55(B=4n)c7B2C-r()cr))=
replace = at position 30 with 4,C6Nn(rl3=55(B=4n)c7B2C-r()cr))4
remove n from position 15,C6Nn(rl3=55(B=4)c7B2C-r()cr))4
replace 3 at position 7 with 7,C6Nn(rl7=55(B=4)c7B2C-r()cr))4
remove C from position 20,C6Nn(rl7=55(B=4)c7B2-r()cr))4
replace C at position 0 with S,S6Nn(rl7=55(B=4)c7B2-r()cr))4
remove ) from position 27,S6Nn(rl7=55(B=4)c7B2-r()cr)4
remove r from position 5,S6Nn(l7=55(B=4)c7B2-r()cr)4
remove ( from position 21,S6Nn(l7=55(B=4)c7B2-r)cr)4
replace ( at position 4 with r,S6Nnrl7=55(B=4)c7B2-r)cr)4
remove - from position 19,S6Nnrl7=55(B=4)c7B2r)cr)4
add s at position 17,S6Nnrl7=55(B=4)c7sB2r)cr)4
replace c at position 22 with /,S6Nnrl7=55(B=4)c7sB2r)/r)4
replace ) at position 24 with F,S6Nnrl7=55(B=4)c7sB2r)/rF4
remove = from position 12,S6Nnrl7=55(B4)c7sB2r)/rF4
replace ( at position 10 with /,S6Nnrl7=55/B4)c7sB2r)/rF4
remove B from position 17,S6Nnrl7=55/B4)c7s2r)/rF4
remove ) from position 19,S6Nnrl7=55/B4)c7s2r/rF4
remove c from position 14,S6Nnrl7=55/B4)7s2r/rF4
add r at position 2,S6rNnrl7=55/B4)7s2r/rF4
remove F from position 21,S6rNnrl7=55/B4)7s2r/r4
remove 6 from position 1,SrNnrl7=55/B4)7s2r/r4
remove N from position 2,Srnrl7=55/B4)7s2r/r4
remove 4 from position 19,Srnrl7=55/B4)7s2r/r
replace 4 at position 11 with F,Srnrl7=55/BF)7s2r/r
remove ) from position 12,Srnrl7=55/BF7s2r/r
remove r from position 1,Snrl7=55/BF7s2r/r
add l at position 2,Snlrl7=55/BF7s2r/r
remove B from position 10,Snlrl7=55/F7s2r/r
add S at position 7,Snlrl7=S55/F7s2r/r
replace S at position 0 with o,onlrl7=S55/F7s2r/r
remove F from position 11,onlrl7=S55/7s2r/r
remove l from position 2,onrl7=S55/7s2r/r
remove S from position 6,onrl7=55/7s2r/r
replace / at position 13 with N,onrl7=55/7s2rNr
remove 2 from position 11,onrl7=55/7srNr
add O at position 2,onOrl7=55/7srNr
replace 7 at position 10 with ],onOrl7=55/]srNr
add 6 at position 14,onOrl7=55/]srN6r
remove s from position 11,onOrl7=55/]rN6r
remove = from position 6,onOrl755/]rN6r
replace r at position 3 with 4,onO4l755/]rN6r
add H at position 5,onO4lH755/]rN6r
replace n at position 1 with F,oFO4lH755/]rN6r
remove 6 from position 13,oFO4lH755/]rNr
add H at position 9,oFO4lH755H/]rNr
remove O from position 2,oF4lH755H/]rNr
remove 7 from position 5,oF4lH55H/]rNr
replace H at position 4 with [,oF4l[55H/]rNr
remove 4 from position 2,oFl[55H/]rNr
remove l from position 2,oF[55H/]rNr
replace r at position 8 with S,oF[55H/]SNr
remove 5 from position 3,oF[5H/]SNr
remove [ from position 2,oF5H/]SNr
remove S from position 6,oF5H/]Nr
remove r from position 7,oF5H/]N
replace / at position 4 with B,oF5HB]N
remove N from position 6,oF5HB]
remove B from position 4,oF5H]
add C at position 5,oF5H]C
replace ] at position 4 with -,oF5H-C
add B at position 3,oF5BH-C
add c at position 2,oFc5BH-C
replace 5 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
