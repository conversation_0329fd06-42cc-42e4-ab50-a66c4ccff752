log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 5,oFc5BH-C
remove c from position 2,oF5BH-<PERSON>
remove B from position 3,oF5H-<PERSON>
replace - at position 4 with ],oF5H]C
remove C from position 5,oF5H]
add B at position 4,oF5HB]
add N at position 6,oF5HB]N
replace B at position 4 with /,oF5H/]N
add r at position 7,oF5H/]Nr
add S at position 6,oF5H/]SNr
add [ at position 2,oF[5H/]SNr
add 5 at position 3,oF[55H/]SNr
replace S at position 8 with r,oF[55H/]rNr
add l at position 2,oFl[55H/]rNr
add 4 at position 2,oF4l[55H/]rNr
replace [ at position 4 with H,oF4lH55H/]rNr
add 7 at position 5,oF4lH755H/]rNr
add O at position 2,oFO4lH755H/]rNr
remove H from position 9,oFO4lH755/]rNr
add 6 at position 13,oFO4lH755/]rN6r
replace F at position 1 with n,onO4lH755/]rN6r
remove H from position 5,onO4l755/]rN6r
replace 4 at position 3 with r,onOrl755/]rN6r
add = at position 6,onOrl7=55/]rN6r
add s at position 11,onOrl7=55/]srN6r
remove 6 from position 14,onOrl7=55/]srNr
replace ] at position 10 with 7,onOrl7=55/7srNr
remove O from position 2,onrl7=55/7srNr
add 2 at position 11,onrl7=55/7s2rNr
replace N at position 13 with /,onrl7=55/7s2r/r
add S at position 6,onrl7=S55/7s2r/r
add l at position 2,onlrl7=S55/7s2r/r
add F at position 11,onlrl7=S55/F7s2r/r
replace o at position 0 with S,Snlrl7=S55/F7s2r/r
remove S from position 7,Snlrl7=55/F7s2r/r
add B at position 10,Snlrl7=55/BF7s2r/r
remove l from position 2,Snrl7=55/BF7s2r/r
add r at position 1,Srnrl7=55/BF7s2r/r
add ) at position 12,Srnrl7=55/BF)7s2r/r
replace F at position 11 with 4,Srnrl7=55/B4)7s2r/r
add 4 at position 19,Srnrl7=55/B4)7s2r/r4
add N at position 2,SrNnrl7=55/B4)7s2r/r4
add 6 at position 1,S6rNnrl7=55/B4)7s2r/r4
add F at position 21,S6rNnrl7=55/B4)7s2r/rF4
remove r from position 2,S6Nnrl7=55/B4)7s2r/rF4
add c at position 14,S6Nnrl7=55/B4)c7s2r/rF4
add ) at position 19,S6Nnrl7=55/B4)c7s2r)/rF4
add B at position 17,S6Nnrl7=55/B4)c7sB2r)/rF4
replace / at position 10 with (,S6Nnrl7=55(B4)c7sB2r)/rF4
add = at position 12,S6Nnrl7=55(B=4)c7sB2r)/rF4
replace F at position 24 with ),S6Nnrl7=55(B=4)c7sB2r)/r)4
replace / at position 22 with c,S6Nnrl7=55(B=4)c7sB2r)cr)4
remove s from position 17,S6Nnrl7=55(B=4)c7B2r)cr)4
add - at position 19,S6Nnrl7=55(B=4)c7B2-r)cr)4
replace r at position 4 with (,S6Nn(l7=55(B=4)c7B2-r)cr)4
add ( at position 21,S6Nn(l7=55(B=4)c7B2-r()cr)4
add r at position 5,S6Nn(rl7=55(B=4)c7B2-r()cr)4
add ) at position 27,S6Nn(rl7=55(B=4)c7B2-r()cr))4
replace S at position 0 with C,C6Nn(rl7=55(B=4)c7B2-r()cr))4
add C at position 20,C6Nn(rl7=55(B=4)c7B2C-r()cr))4
replace 7 at position 7 with 3,C6Nn(rl3=55(B=4)c7B2C-r()cr))4
add n at position 15,C6Nn(rl3=55(B=4n)c7B2C-r()cr))4
replace 4 at position 30 with =,C6Nn(rl3=55(B=4n)c7B2C-r()cr))=
replace ) at position 29 with 7,C6Nn(rl3=55(B=4n)c7B2C-r()cr)7=
add ) at position 27,C6Nn(rl3=55(B=4n)c7B2C-r()c)r)7=
replace 5 at position 9 with S,C6Nn(rl3=S5(B=4n)c7B2C-r()c)r)7=
add 2 at position 2,C62Nn(rl3=S5(B=4n)c7B2C-r()c)r)7=
replace ( at position 25 with /,C62Nn(rl3=S5(B=4n)c7B2C-r/)c)r)7=
remove - from position 23,C62Nn(rl3=S5(B=4n)c7B2Cr/)c)r)7=
remove l from position 7,C62Nn(r3=S5(B=4n)c7B2Cr/)c)r)7=
replace 4 at position 14 with 3,C62Nn(r3=S5(B=3n)c7B2Cr/)c)r)7=
add F at position 21,C62Nn(r3=S5(B=3n)c7B2FCr/)c)r)7=
add l at position 13,C62Nn(r3=S5(Bl=3n)c7B2FCr/)c)r)7=
replace ) at position 17 with F,C62Nn(r3=S5(Bl=3nFc7B2FCr/)c)r)7=
add c at position 32,C62Nn(r3=S5(Bl=3nFc7B2FCr/)c)r)7c=
add 4 at position 18,C62Nn(r3=S5(Bl=3nF4c7B2FCr/)c)r)7c=
add 6 at position 21,C62Nn(r3=S5(Bl=3nF4c76B2FCr/)c)r)7c=
replace 4 at position 18 with c,C62Nn(r3=S5(Bl=3nFcc76B2FCr/)c)r)7c=
replace 6 at position 21 with C,C62Nn(r3=S5(Bl=3nFcc7CB2FCr/)c)r)7c=
remove ) from position 30,C62Nn(r3=S5(Bl=3nFcc7CB2FCr/)cr)7c=
add = at position 6,C62Nn(=r3=S5(Bl=3nFcc7CB2FCr/)cr)7c=
add + at position 29,C62Nn(=r3=S5(Bl=3nFcc7CB2FCr/+)cr)7c=
add c at position 30,C62Nn(=r3=S5(Bl=3nFcc7CB2FCr/+c)cr)7c=
remove r from position 33,C62Nn(=r3=S5(Bl=3nFcc7CB2FCr/+c)c)7c=
add n at position 22,C62Nn(=r3=S5(Bl=3nFcc7nCB2FCr/+c)c)7c=
add o at position 23,C62Nn(=r3=S5(Bl=3nFcc7noCB2FCr/+c)c)7c=
remove / from position 30,C62Nn(=r3=S5(Bl=3nFcc7noCB2FCr+c)c)7c=
add c at position 30,C62Nn(=r3=S5(Bl=3nFcc7noCB2FCrc+c)c)7c=
add r at position 19,C62Nn(=r3=S5(Bl=3nFrcc7noCB2FCrc+c)c)7c=
add ) at position 4,C62N)n(=r3=S5(Bl=3nFrcc7noCB2FCrc+c)c)7c=
replace = at position 10 with ),C62N)n(=r3)S5(Bl=3nFrcc7noCB2FCrc+c)c)7c=
remove 2 from position 28,C62N)n(=r3)S5(Bl=3nFrcc7noCBFCrc+c)c)7c=
remove 7 from position 37,C62N)n(=r3)S5(Bl=3nFrcc7noCBFCrc+c)c)c=
add 4 at position 17,C62N)n(=r3)S5(Bl=43nFrcc7noCBFCrc+c)c)c=
add ] at position 7,C62N)n(]=r3)S5(Bl=43nFrcc7noCBFCrc+c)c)c=
add 2 at position 36,C62N)n(]=r3)S5(Bl=43nFrcc7noCBFCrc+c2)c)c=
replace = at position 17 with H,C62N)n(]=r3)S5(BlH43nFrcc7noCBFCrc+c2)c)c=
replace + at position 34 with ),C62N)n(]=r3)S5(BlH43nFrcc7noCBFCrc)c2)c)c=
add B at position 36,C62N)n(]=r3)S5(BlH43nFrcc7noCBFCrc)cB2)c)c=
add n at position 19,C62N)n(]=r3)S5(BlH4n3nFrcc7noCBFCrc)cB2)c)c=
replace H at position 17 with -,C62N)n(]=r3)S5(Bl-4n3nFrcc7noCBFCrc)cB2)c)c=
replace = at position 43 with 1,C62N)n(]=r3)S5(Bl-4n3nFrcc7noCBFCrc)cB2)c)c1
add N at position 13,C62N)n(]=r3)SN5(Bl-4n3nFrcc7noCBFCrc)cB2)c)c1
remove 7 from position 27,C62N)n(]=r3)SN5(Bl-4n3nFrccnoCBFCrc)cB2)c)c1
remove r from position 9,C62N)n(]=3)SN5(Bl-4n3nFrccnoCBFCrc)cB2)c)c1
remove N from position 3,C62)n(]=3)SN5(Bl-4n3nFrccnoCBFCrc)cB2)c)c1
add r at position 10,C62)n(]=3)rSN5(Bl-4n3nFrccnoCBFCrc)cB2)c)c1
remove ) from position 34,C62)n(]=3)rSN5(Bl-4n3nFrccnoCBFCrccB2)c)c1
remove r from position 10,C62)n(]=3)SN5(Bl-4n3nFrccnoCBFCrccB2)c)c1
add O at position 8,C62)n(]=O3)SN5(Bl-4n3nFrccnoCBFCrccB2)c)c1
remove B from position 35,C62)n(]=O3)SN5(Bl-4n3nFrccnoCBFCrcc2)c)c1
remove F from position 22,C62)n(]=O3)SN5(Bl-4n3nrccnoCBFCrcc2)c)c1
replace 5 at position 13 with 3,C62)n(]=O3)SN3(Bl-4n3nrccnoCBFCrcc2)c)c1
add - at position 11,C62)n(]=O3)-SN3(Bl-4n3nrccnoCBFCrcc2)c)c1
replace r at position 32 with (,C62)n(]=O3)-SN3(Bl-4n3nrccnoCBFC(cc2)c)c1
remove r from position 23,C62)n(]=O3)-SN3(Bl-4n3nccnoCBFC(cc2)c)c1
replace F at position 29 with /,C62)n(]=O3)-SN3(Bl-4n3nccnoCB/C(cc2)c)c1
remove S from position 12,C62)n(]=O3)-N3(Bl-4n3nccnoCB/C(cc2)c)c1
add c at position 12,C62)n(]=O3)-cN3(Bl-4n3nccnoCB/C(cc2)c)c1
add I at position 4,C62)In(]=O3)-cN3(Bl-4n3nccnoCB/C(cc2)c)c1
replace I at position 4 with (,C62)(n(]=O3)-cN3(Bl-4n3nccnoCB/C(cc2)c)c1
replace 4 at position 20 with ],C62)(n(]=O3)-cN3(Bl-]n3nccnoCB/C(cc2)c)c1
add S at position 5,C62)(Sn(]=O3)-cN3(Bl-]n3nccnoCB/C(cc2)c)c1
replace 2 at position 2 with H,C6H)(Sn(]=O3)-cN3(Bl-]n3nccnoCB/C(cc2)c)c1
add c at position 3,C6Hc)(Sn(]=O3)-cN3(Bl-]n3nccnoCB/C(cc2)c)c1
replace ) at position 4 with c,C6Hcc(Sn(]=O3)-cN3(Bl-]n3nccnoCB/C(cc2)c)c1
replace l at position 20 with -,C6Hcc(Sn(]=O3)-cN3(B--]n3nccnoCB/C(cc2)c)c1
add 2 at position 17,C6Hcc(Sn(]=O3)-cN23(B--]n3nccnoCB/C(cc2)c)c1
add B at position 10,C6Hcc(Sn(]B=O3)-cN23(B--]n3nccnoCB/C(cc2)c)c1
remove S from position 6,C6Hcc(n(]B=O3)-cN23(B--]n3nccnoCB/C(cc2)c)c1
replace N at position 16 with c,C6Hcc(n(]B=O3)-cc23(B--]n3nccnoCB/C(cc2)c)c1
replace n at position 24 with 2,C6Hcc(n(]B=O3)-cc23(B--]23nccnoCB/C(cc2)c)c1
replace n at position 6 with C,C6Hcc(C(]B=O3)-cc23(B--]23nccnoCB/C(cc2)c)c1
add C at position 7,C6Hcc(CC(]B=O3)-cc23(B--]23nccnoCB/C(cc2)c)c1
remove = from position 11,C6Hcc(CC(]BO3)-cc23(B--]23nccnoCB/C(cc2)c)c1
replace 3 at position 18 with O,C6Hcc(CC(]BO3)-cc2O(B--]23nccnoCB/C(cc2)c)c1
add - at position 33,C6Hcc(CC(]BO3)-cc2O(B--]23nccnoCB-/C(cc2)c)c1
replace B at position 10 with =,C6Hcc(CC(]=O3)-cc2O(B--]23nccnoCB-/C(cc2)c)c1
replace o at position 30 with (,C6Hcc(CC(]=O3)-cc2O(B--]23nccn(CB-/C(cc2)c)c1
replace ( at position 30 with 3,C6Hcc(CC(]=O3)-cc2O(B--]23nccn3CB-/C(cc2)c)c1
add ) at position 37,C6Hcc(CC(]=O3)-cc2O(B--]23nccn3CB-/C()cc2)c)c1
add c at position 22,C6Hcc(CC(]=O3)-cc2O(B-c-]23nccn3CB-/C()cc2)c)c1
replace H at position 2 with c,C6ccc(CC(]=O3)-cc2O(B-c-]23nccn3CB-/C()cc2)c)c1
remove ] from position 9,C6ccc(CC(=O3)-cc2O(B-c-]23nccn3CB-/C()cc2)c)c1
replace 3 at position 11 with c,C6ccc(CC(=Oc)-cc2O(B-c-]23nccn3CB-/C()cc2)c)c1
remove O from position 17,C6ccc(CC(=Oc)-cc2(B-c-]23nccn3CB-/C()cc2)c)c1
add + at position 29,C6ccc(CC(=Oc)-cc2(B-c-]23nccn+3CB-/C()cc2)c)c1
remove - from position 33,C6ccc(CC(=Oc)-cc2(B-c-]23nccn+3CB/C()cc2)c)c1
remove ] from position 22,C6ccc(CC(=Oc)-cc2(B-c-23nccn+3CB/C()cc2)c)c1
remove - from position 13,C6ccc(CC(=Oc)cc2(B-c-23nccn+3CB/C()cc2)c)c1
remove / from position 31,C6ccc(CC(=Oc)cc2(B-c-23nccn+3CBC()cc2)c)c1
add 1 at position 3,C6c1cc(CC(=Oc)cc2(B-c-23nccn+3CBC()cc2)c)c1
add + at position 42,C6c1cc(CC(=Oc)cc2(B-c-23nccn+3CBC()cc2)c)c+1
remove - from position 21,C6c1cc(CC(=Oc)cc2(B-c23nccn+3CBC()cc2)c)c+1
add C at position 32,C6c1cc(CC(=Oc)cc2(B-c23nccn+3CBCC()cc2)c)c+1
add C at position 33,C6c1cc(CC(=Oc)cc2(B-c23nccn+3CBCCC()cc2)c)c+1
add ) at position 12,C6c1cc(CC(=O)c)cc2(B-c23nccn+3CBCCC()cc2)c)c+1
replace 2 at position 17 with c,C6c1cc(CC(=O)c)ccc(B-c23nccn+3CBCCC()cc2)c)c+1
remove B from position 19,C6c1cc(CC(=O)c)ccc(-c23nccn+3CBCCC()cc2)c)c+1
add 3 at position 35,C6c1cc(CC(=O)c)ccc(-c23nccn+3CBCCC(3)cc2)c)c+1
add C at position 2,C6Cc1cc(CC(=O)c)ccc(-c23nccn+3CBCCC(3)cc2)c)c+1
remove ) from position 43,C6Cc1cc(CC(=O)c)ccc(-c23nccn+3CBCCC(3)cc2)cc+1
add N at position 29,C6Cc1cc(CC(=O)c)ccc(-c23nccn+N3CBCCC(3)cc2)cc+1
replace ) at position 15 with 2,C6Cc1cc(CC(=O)c2ccc(-c23nccn+N3CBCCC(3)cc2)cc+1
replace B at position 32 with C,C6Cc1cc(CC(=O)c2ccc(-c23nccn+N3CCCCC(3)cc2)cc+1
remove + from position 45,C6Cc1cc(CC(=O)c2ccc(-c23nccn+N3CCCCC(3)cc2)cc1
add N at position 9,C6Cc1cc(CNC(=O)c2ccc(-c23nccn+N3CCCCC(3)cc2)cc1
add 3 at position 30,C6Cc1cc(CNC(=O)c2ccc(-c23nccn+3N3CCCCC(3)cc2)cc1
remove 6 from position 1,CCc1cc(CNC(=O)c2ccc(-c23nccn+3N3CCCCC(3)cc2)cc1
remove 2 from position 22,CCc1cc(CNC(=O)c2ccc(-c3nccn+3N3CCCCC(3)cc2)cc1
add c at position 5,CCc1ccc(CNC(=O)c2ccc(-c3nccn+3N3CCCCC(3)cc2)cc1
remove ( from position 37,CCc1ccc(CNC(=O)c2ccc(-c3nccn+3N3CCCCC3)cc2)cc1
replace + at position 28 with c,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)cc1
final: CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)cc1,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)cc1
