log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 3,oFc3BH-C
remove c from position 2,oF3BH-<PERSON>
remove B from position 3,oF3H-<PERSON>
replace - at position 4 with ],oF3H]C
remove C from position 5,oF3H]
add B at position 4,oF3HB]
add N at position 6,oF3HB]N
replace B at position 4 with -,oF3H-]N
add ) at position 7,oF3H-]N)
add S at position 6,oF3H-]SN)
add [ at position 2,oF[3H-]SN)
add O at position 3,oF[O3H-]SN)
replace S at position 8 with c,oF[O3H-]cN)
add = at position 2,oF=[O3H-]cN)
add 4 at position 2,oF4=[O3H-]cN)
replace [ at position 4 with H,oF4=HO3H-]cN)
add ] at position 5,oF4=H]O3H-]cN)
add O at position 2,oFO4=H]O3H-]cN)
remove H from position 9,oFO4=H]O3-]cN)
add 6 at position 13,oFO4=H]O3-]cN6)
replace F at position 1 with c,ocO4=H]O3-]cN6)
remove H from position 5,ocO4=]O3-]cN6)
replace 4 at position 3 with r,ocOr=]O3-]cN6)
add S at position 6,ocOr=]SO3-]cN6)
add s at position 11,ocOr=]SO3-]scN6)
remove 6 from position 14,ocOr=]SO3-]scN)
replace ] at position 10 with H,ocOr=]SO3-HscN)
remove O from position 2,ocr=]SO3-HscN)
add ( at position 11,ocr=]SO3-Hs(cN)
replace N at position 13 with /,ocr=]SO3-Hs(c/)
add S at position 6,ocr=]SSO3-Hs(c/)
add l at position 2,oclr=]SSO3-Hs(c/)
add F at position 11,oclr=]SSO3-FHs(c/)
replace o at position 0 with S,Sclr=]SSO3-FHs(c/)
remove S from position 7,Sclr=]SO3-FHs(c/)
add ( at position 10,Sclr=]SO3-(FHs(c/)
remove l from position 2,Scr=]SO3-(FHs(c/)
add r at position 1,Srcr=]SO3-(FHs(c/)
add ) at position 12,Srcr=]SO3-(F)Hs(c/)
replace F at position 11 with 4,Srcr=]SO3-(4)Hs(c/)
add # at position 19,Srcr=]SO3-(4)Hs(c/)#
add 1 at position 2,Sr1cr=]SO3-(4)Hs(c/)#
add C at position 1,SCr1cr=]SO3-(4)Hs(c/)#
add C at position 21,SCr1cr=]SO3-(4)Hs(c/)C#
remove r from position 2,SC1cr=]SO3-(4)Hs(c/)C#
add B at position 14,SC1cr=]SO3-(4)BHs(c/)C#
add 1 at position 19,SC1cr=]SO3-(4)BHs(c1/)C#
add o at position 17,SC1cr=]SO3-(4)BHso(c1/)C#
replace - at position 10 with 7,SC1cr=]SO37(4)BHso(c1/)C#
add r at position 12,SC1cr=]SO37(r4)BHso(c1/)C#
replace C at position 24 with r,SC1cr=]SO37(r4)BHso(c1/)r#
replace / at position 22 with C,SC1cr=]SO37(r4)BHso(c1C)r#
remove s from position 17,SC1cr=]SO37(r4)BHo(c1C)r#
add ) at position 19,SC1cr=]SO37(r4)BHo()c1C)r#
replace r at position 4 with @,SC1c@=]SO37(r4)BHo()c1C)r#
add / at position 21,SC1c@=]SO37(r4)BHo()c/1C)r#
remove B from position 15,SC1c@=]SO37(r4)Ho()c/1C)r#
add 2 at position 18,SC1c@=]SO37(r4)Ho(2)c/1C)r#
replace S at position 0 with 7,7C1c@=]SO37(r4)Ho(2)c/1C)r#
add ) at position 20,7C1c@=]SO37(r4)Ho(2))c/1C)r#
add l at position 11,7C1c@=]SO37l(r4)Ho(2))c/1C)r#
add ) at position 29,7C1c@=]SO37l(r4)Ho(2))c/1C)r#)
replace 3 at position 9 with =,7C1c@=]SO=7l(r4)Ho(2))c/1C)r#)
replace ) at position 29 with 7,7C1c@=]SO=7l(r4)Ho(2))c/1C)r#7
add = at position 13,7C1c@=]SO=7l(=r4)Ho(2))c/1C)r#7
add r at position 20,7C1c@=]SO=7l(=r4)Ho(r2))c/1C)r#7
remove 1 from position 2,7Cc@=]SO=7l(=r4)Ho(r2))c/1C)r#7
remove 7 from position 0,Cc@=]SO=7l(=r4)Ho(r2))c/1C)r#7
replace 7 at position 8 with 2,Cc@=]SO=2l(=r4)Ho(r2))c/1C)r#7
add + at position 3,Cc@+=]SO=2l(=r4)Ho(r2))c/1C)r#7
remove H from position 16,Cc@+=]SO=2l(=r4)o(r2))c/1C)r#7
replace 4 at position 14 with +,Cc@+=]SO=2l(=r+)o(r2))c/1C)r#7
add C at position 10,Cc@+=]SO=2Cl(=r+)o(r2))c/1C)r#7
add 4 at position 13,Cc@+=]SO=2Cl(4=r+)o(r2))c/1C)r#7
replace ) at position 17 with c,Cc@+=]SO=2Cl(4=r+co(r2))c/1C)r#7
add ( at position 32,Cc@+=]SO=2Cl(4=r+co(r2))c/1C)r#7(
add 4 at position 18,Cc@+=]SO=2Cl(4=r+c4o(r2))c/1C)r#7(
add 6 at position 21,Cc@+=]SO=2Cl(4=r+c4o(6r2))c/1C)r#7(
replace 4 at position 18 with r,Cc@+=]SO=2Cl(4=r+cro(6r2))c/1C)r#7(
replace 6 at position 21 with C,Cc@+=]SO=2Cl(4=r+cro(Cr2))c/1C)r#7(
remove ) from position 30,Cc@+=]SO=2Cl(4=r+cro(Cr2))c/1Cr#7(
add 3 at position 6,Cc@+=]3SO=2Cl(4=r+cro(Cr2))c/1Cr#7(
add ) at position 29,Cc@+=]3SO=2Cl(4=r+cro(Cr2))c/)1Cr#7(
add ( at position 30,Cc@+=]3SO=2Cl(4=r+cro(Cr2))c/)(1Cr#7(
remove r from position 33,Cc@+=]3SO=2Cl(4=r+cro(Cr2))c/)(1C#7(
add = at position 22,Cc@+=]3SO=2Cl(4=r+cro(=Cr2))c/)(1C#7(
add C at position 23,Cc@+=]3SO=2Cl(4=r+cro(=CCr2))c/)(1C#7(
remove / from position 30,Cc@+=]3SO=2Cl(4=r+cro(=CCr2))c)(1C#7(
add B at position 30,Cc@+=]3SO=2Cl(4=r+cro(=CCr2))cB)(1C#7(
add r at position 19,Cc@+=]3SO=2Cl(4=r+crro(=CCr2))cB)(1C#7(
add n at position 4,Cc@+n=]3SO=2Cl(4=r+crro(=CCr2))cB)(1C#7(
replace = at position 10 with 5,Cc@+n=]3SO52Cl(4=r+crro(=CCr2))cB)(1C#7(
remove 2 from position 28,Cc@+n=]3SO52Cl(4=r+crro(=CCr))cB)(1C#7(
remove 7 from position 37,Cc@+n=]3SO52Cl(4=r+crro(=CCr))cB)(1C#(
add 3 at position 17,Cc@+n=]3SO52Cl(4=3r+crro(=CCr))cB)(1C#(
add = at position 7,Cc@+n=]=3SO52Cl(4=3r+crro(=CCr))cB)(1C#(
add c at position 36,Cc@+n=]=3SO52Cl(4=3r+crro(=CCr))cB)(c1C#(
replace = at position 17 with F,Cc@+n=]=3SO52Cl(4F3r+crro(=CCr))cB)(c1C#(
replace ) at position 34 with B,Cc@+n=]=3SO52Cl(4F3r+crro(=CCr))cBB(c1C#(
add ) at position 36,Cc@+n=]=3SO52Cl(4F3r+crro(=CCr))cBB()c1C#(
add c at position 19,Cc@+n=]=3SO52Cl(4F3cr+crro(=CCr))cBB()c1C#(
replace F at position 17 with n,Cc@+n=]=3SO52Cl(4n3cr+crro(=CCr))cBB()c1C#(
remove r from position 24,Cc@+n=]=3SO52Cl(4n3cr+cro(=CCr))cBB()c1C#(
add F at position 20,Cc@+n=]=3SO52Cl(4n3cFr+cro(=CCr))cBB()c1C#(
replace @ at position 2 with 2,Cc2+n=]=3SO52Cl(4n3cFr+cro(=CCr))cBB()c1C#(
add ( at position 9,Cc2+n=]=3(SO52Cl(4n3cFr+cro(=CCr))cBB()c1C#(
remove ( from position 43,Cc2+n=]=3(SO52Cl(4n3cFr+cro(=CCr))cBB()c1C#
add r at position 10,Cc2+n=]=3(rSO52Cl(4n3cFr+cro(=CCr))cBB()c1C#
remove ) from position 34,Cc2+n=]=3(rSO52Cl(4n3cFr+cro(=CCr)cBB()c1C#
remove r from position 10,Cc2+n=]=3(SO52Cl(4n3cFr+cro(=CCr)cBB()c1C#
add S at position 8,Cc2+n=]=S3(SO52Cl(4n3cFr+cro(=CCr)cBB()c1C#
remove B from position 35,Cc2+n=]=S3(SO52Cl(4n3cFr+cro(=CCr)cB()c1C#
remove F from position 22,Cc2+n=]=S3(SO52Cl(4n3cr+cro(=CCr)cB()c1C#
replace 5 at position 13 with 3,Cc2+n=]=S3(SO32Cl(4n3cr+cro(=CCr)cB()c1C#
add ( at position 11,Cc2+n=]=S3((SO32Cl(4n3cr+cro(=CCr)cB()c1C#
replace r at position 32 with 2,Cc2+n=]=S3((SO32Cl(4n3cr+cro(=CC2)cB()c1C#
remove r from position 23,Cc2+n=]=S3((SO32Cl(4n3c+cro(=CC2)cB()c1C#
replace C at position 29 with O,Cc2+n=]=S3((SO32Cl(4n3c+cro(=OC2)cB()c1C#
remove S from position 12,Cc2+n=]=S3((O32Cl(4n3c+cro(=OC2)cB()c1C#
add = at position 12,Cc2+n=]=S3((=O32Cl(4n3c+cro(=OC2)cB()c1C#
add H at position 4,Cc2+Hn=]=S3((=O32Cl(4n3c+cro(=OC2)cB()c1C#
replace H at position 4 with ],Cc2+]n=]=S3((=O32Cl(4n3c+cro(=OC2)cB()c1C#
replace 4 at position 20 with ],Cc2+]n=]=S3((=O32Cl(]n3c+cro(=OC2)cB()c1C#
add S at position 5,Cc2+]Sn=]=S3((=O32Cl(]n3c+cro(=OC2)cB()c1C#
replace 2 at position 2 with I,CcI+]Sn=]=S3((=O32Cl(]n3c+cro(=OC2)cB()c1C#
add N at position 3,CcIN+]Sn=]=S3((=O32Cl(]n3c+cro(=OC2)cB()c1C#
replace + at position 4 with #,CcIN#]Sn=]=S3((=O32Cl(]n3c+cro(=OC2)cB()c1C#
replace l at position 20 with C,CcIN#]Sn=]=S3((=O32CC(]n3c+cro(=OC2)cB()c1C#
add N at position 17,CcIN#]Sn=]=S3((=ON32CC(]n3c+cro(=OC2)cB()c1C#
add @ at position 10,CcIN#]Sn=]@=S3((=ON32CC(]n3c+cro(=OC2)cB()c1C#
remove S from position 6,CcIN#]n=]@=S3((=ON32CC(]n3c+cro(=OC2)cB()c1C#
replace O at position 16 with ),CcIN#]n=]@=S3((=)N32CC(]n3c+cro(=OC2)cB()c1C#
replace n at position 24 with c,CcIN#]n=]@=S3((=)N32CC(]c3c+cro(=OC2)cB()c1C#
replace n at position 6 with c,CcIN#]c=]@=S3((=)N32CC(]c3c+cro(=OC2)cB()c1C#
add ( at position 7,CcIN#]c(=]@=S3((=)N32CC(]c3c+cro(=OC2)cB()c1C#
remove = from position 11,CcIN#]c(=]@S3((=)N32CC(]c3c+cro(=OC2)cB()c1C#
replace 3 at position 18 with O,CcIN#]c(=]@S3((=)NO2CC(]c3c+cro(=OC2)cB()c1C#
add - at position 33,CcIN#]c(=]@S3((=)NO2CC(]c3c+cro(=-OC2)cB()c1C#
replace @ at position 10 with O,CcIN#]c(=]OS3((=)NO2CC(]c3c+cro(=-OC2)cB()c1C#
replace o at position 30 with (,CcIN#]c(=]OS3((=)NO2CC(]c3c+cr((=-OC2)cB()c1C#
replace ( at position 30 with C,CcIN#]c(=]OS3((=)NO2CC(]c3c+crC(=-OC2)cB()c1C#
add 1 at position 37,CcIN#]c(=]OS3((=)NO2CC(]c3c+crC(=-OC21)cB()c1C#
add N at position 22,CcIN#]c(=]OS3((=)NO2CCN(]c3c+crC(=-OC21)cB()c1C#
replace I at position 2 with 1,Cc1N#]c(=]OS3((=)NO2CCN(]c3c+crC(=-OC21)cB()c1C#
remove ] from position 9,Cc1N#]c(=OS3((=)NO2CCN(]c3c+crC(=-OC21)cB()c1C#
replace 3 at position 11 with c,Cc1N#]c(=OSc((=)NO2CCN(]c3c+crC(=-OC21)cB()c1C#
remove O from position 17,Cc1N#]c(=OSc((=)N2CCN(]c3c+crC(=-OC21)cB()c1C#
add ) at position 29,Cc1N#]c(=OSc((=)N2CCN(]c3c+cr)C(=-OC21)cB()c1C#
remove - from position 33,Cc1N#]c(=OSc((=)N2CCN(]c3c+cr)C(=OC21)cB()c1C#
remove ] from position 22,Cc1N#]c(=OSc((=)N2CCN(c3c+cr)C(=OC21)cB()c1C#
remove 1 from position 35,Cc1N#]c(=OSc((=)N2CCN(c3c+cr)C(=OC2)cB()c1C#
remove B from position 37,Cc1N#]c(=OSc((=)N2CCN(c3c+cr)C(=OC2)c()c1C#
add ) at position 10,Cc1N#]c(=O)Sc((=)N2CCN(c3c+cr)C(=OC2)c()c1C#
add r at position 30,Cc1N#]c(=O)Sc((=)N2CCN(c3c+cr)rC(=OC2)c()c1C#
replace # at position 4 with n,Cc1Nn]c(=O)Sc((=)N2CCN(c3c+cr)rC(=OC2)c()c1C#
add C at position 1,CCc1Nn]c(=O)Sc((=)N2CCN(c3c+cr)rC(=OC2)c()c1C#
replace r at position 29 with c,CCc1Nn]c(=O)Sc((=)N2CCN(c3c+cc)rC(=OC2)c()c1C#
add C at position 41,CCc1Nn]c(=O)Sc((=)N2CCN(c3c+cc)rC(=OC2)c(C)c1C#
add 2 at position 22,CCc1Nn]c(=O)Sc((=)N2CC2N(c3c+cc)rC(=OC2)c(C)c1C#
replace N at position 4 with [,CCc1[n]c(=O)Sc((=)N2CC2N(c3c+cc)rC(=OC2)c(C)c1C#
add O at position 17,CCc1[n]c(=O)Sc((=O)N2CC2N(c3c+cc)rC(=OC2)c(C)c1C#
add 3 at position 32,CCc1[n]c(=O)Sc((=O)N2CC2N(c3c+cc3)rC(=OC2)c(C)c1C#
add ( at position 38,CCc1[n]c(=O)Sc((=O)N2CC2N(c3c+cc3)rC(=(OC2)c(C)c1C#
remove r from position 34,CCc1[n]c(=O)Sc((=O)N2CC2N(c3c+cc3)C(=(OC2)c(C)c1C#
add C at position 15,CCc1[n]c(=O)Sc(C(=O)N2CC2N(c3c+cc3)C(=(OC2)c(C)c1C#
remove C from position 0,Cc1[n]c(=O)Sc(C(=O)N2CC2N(c3c+cc3)C(=(OC2)c(C)c1C#
remove S from position 11,Cc1[n]c(=O)c(C(=O)N2CC2N(c3c+cc3)C(=(OC2)c(C)c1C#
add c at position 29,Cc1[n]c(=O)c(C(=O)N2CC2N(c3c+ccc3)C(=(OC2)c(C)c1C#
add ) at position 39,Cc1[n]c(=O)c(C(=O)N2CC2N(c3c+ccc3)C(=(O)C2)c(C)c1C#
remove # from position 50,Cc1[n]c(=O)c(C(=O)N2CC2N(c3c+ccc3)C(=(O)C2)c(C)c1C
remove 2 from position 22,Cc1[n]c(=O)c(C(=O)N2CCN(c3c+ccc3)C(=(O)C2)c(C)c1C
add H at position 5,Cc1[nH]c(=O)c(C(=O)N2CCN(c3c+ccc3)C(=(O)C2)c(C)c1C
remove ( from position 37,Cc1[nH]c(=O)c(C(=O)N2CCN(c3c+ccc3)C(=O)C2)c(C)c1C
replace + at position 28 with c,Cc1[nH]c(=O)c(C(=O)N2CCN(c3ccccc3)C(=O)C2)c(C)c1C
final: Cc1[nH]c(=O)c(C(=O)N2CCN(c3ccccc3)C(=O)C2)c(C)c1C,Cc1[nH]c(=O)c(C(=O)N2CCN(c3ccccc3)C(=O)C2)c(C)c1C
