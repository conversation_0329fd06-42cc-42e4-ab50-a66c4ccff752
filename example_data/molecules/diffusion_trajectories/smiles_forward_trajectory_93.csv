log,state
initialize: COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2=O,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2=O
replace - at position 28 with +,COc1cc2c(cc1OC)[C@H](C(=O)[O+])[C@H](c1cccc(Cl)c1)N(C)C2=O
add s at position 58,COc1cc2c(cc1OC)[C@H](C(=O)[O+])[C@H](c1cccc(Cl)c1)N(C)C2=Os
replace c at position 37 with (,COc1cc2c(cc1OC)[C@H](C(=O)[O+])[C@H]((1cccc(Cl)c1)N(C)C2=Os
remove c from position 5,COc1c2c(cc1OC)[C@H](C(=O)[O+])[C@H]((1cccc(Cl)c1)N(C)C2=Os
add 2 at position 22,COc1c2c(cc1OC)[C@H](C(2=O)[O+])[C@H]((1cccc(Cl)c1)N(C)C2=Os
add # at position 50,COc1c2c(cc1OC)[C@H](C(2=O)[O+])[C@H]((1cccc(Cl)c1)#N(C)C2=Os
remove c from position 39,COc1c2c(cc1OC)[C@H](C(2=O)[O+])[C@H]((1ccc(Cl)c1)#N(C)C2=Os
remove ] from position 29,COc1c2c(cc1OC)[C@H](C(2=O)[O+)[C@H]((1ccc(Cl)c1)#N(C)C2=Os
add S at position 11,COc1c2c(cc1SOC)[C@H](C(2=O)[O+)[C@H]((1ccc(Cl)c1)#N(C)C2=Os
add C at position 0,CCOc1c2c(cc1SOC)[C@H](C(2=O)[O+)[C@H]((1ccc(Cl)c1)#N(C)C2=Os
remove ) from position 15,CCOc1c2c(cc1SOC[C@H](C(2=O)[O+)[C@H]((1ccc(Cl)c1)#N(C)C2=Os
add r at position 34,CCOc1c2c(cc1SOC[C@H](C(2=O)[O+)[C@rH]((1ccc(Cl)c1)#N(C)C2=Os
remove ( from position 38,CCOc1c2c(cc1SOC[C@H](C(2=O)[O+)[C@rH](1ccc(Cl)c1)#N(C)C2=Os
remove C from position 32,CCOc1c2c(cc1SOC[C@H](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2=Os
remove @ from position 17,CCOc1c2c(cc1SOC[CH](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2=Os
replace 1 at position 4 with O,CCOcOc2c(cc1SOC[CH](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2=Os
remove = from position 54,CCOcOc2c(cc1SOC[CH](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2Os
remove [ from position 15,CCOcOc2c(cc1SOCCH](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2Os
remove S from position 12,CCOcOc2c(cc1OCCH](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2Os
remove ( from position 33,CCOcOc2c(cc1OCCH](C(2=O)[O+)[@rH]1ccc(Cl)c1)#N(C)C2Os
remove ] from position 32,CCOcOc2c(cc1OCCH](C(2=O)[O+)[@rH1ccc(Cl)c1)#N(C)C2Os
add - at position 21,CCOcOc2c(cc1OCCH](C(2-=O)[O+)[@rH1ccc(Cl)c1)#N(C)C2Os
remove 1 from position 42,CCOcOc2c(cc1OCCH](C(2-=O)[O+)[@rH1ccc(Cl)c)#N(C)C2Os
remove c from position 3,CCOOc2c(cc1OCCH](C(2-=O)[O+)[@rH1ccc(Cl)c)#N(C)C2Os
add / at position 31,CCOOc2c(cc1OCCH](C(2-=O)[O+)[@r/H1ccc(Cl)c)#N(C)C2Os
add - at position 13,CCOOc2c(cc1OC-CH](C(2-=O)[O+)[@r/H1ccc(Cl)c)#N(C)C2Os
add ] at position 22,CCOOc2c(cc1OC-CH](C(2-]=O)[O+)[@r/H1ccc(Cl)c)#N(C)C2Os
add - at position 33,CCOOc2c(cc1OC-CH](C(2-]=O)[O+)[@r-/H1ccc(Cl)c)#N(C)C2Os
remove ) from position 29,CCOOc2c(cc1OC-CH](C(2-]=O)[O+[@r-/H1ccc(Cl)c)#N(C)C2Os
add O at position 17,CCOOc2c(cc1OC-CH]O(C(2-]=O)[O+[@r-/H1ccc(Cl)c)#N(C)C2Os
replace O at position 11 with 3,CCOOc2c(cc13C-CH]O(C(2-]=O)[O+[@r-/H1ccc(Cl)c)#N(C)C2Os
add - at position 54,CCOOc2c(cc13C-CH]O(C(2-]=O)[O+[@r-/H1ccc(Cl)c)#N(C)C2O-s
replace + at position 29 with (,CCOOc2c(cc13C-CH]O(C(2-]=O)[O([@r-/H1ccc(Cl)c)#N(C)C2O-s
remove ( from position 48,CCOOc2c(cc13C-CH]O(C(2-]=O)[O([@r-/H1ccc(Cl)c)#NC)C2O-s
remove 2 from position 21,CCOOc2c(cc13C-CH]O(C(-]=O)[O([@r-/H1ccc(Cl)c)#NC)C2O-s
remove [ from position 29,CCOOc2c(cc13C-CH]O(C(-]=O)[O(@r-/H1ccc(Cl)c)#NC)C2O-s
remove O from position 50,CCOOc2c(cc13C-CH]O(C(-]=O)[O(@r-/H1ccc(Cl)c)#NC)C2-s
replace ( at position 38 with r,CCOOc2c(cc13C-CH]O(C(-]=O)[O(@r-/H1cccrCl)c)#NC)C2-s
add ] at position 46,CCOOc2c(cc13C-CH]O(C(-]=O)[O(@r-/H1cccrCl)c)#N]C)C2-s
remove ( from position 28,CCOOc2c(cc13C-CH]O(C(-]=O)[O@r-/H1cccrCl)c)#N]C)C2-s
remove ( from position 7,CCOOc2ccc13C-CH]O(C(-]=O)[O@r-/H1cccrCl)c)#N]C)C2-s
replace O at position 26 with =,CCOOc2ccc13C-CH]O(C(-]=O)[=@r-/H1cccrCl)c)#N]C)C2-s
replace c at position 33 with +,CCOOc2ccc13C-CH]O(C(-]=O)[=@r-/H1+ccrCl)c)#N]C)C2-s
replace H at position 31 with 7,CCOOc2ccc13C-CH]O(C(-]=O)[=@r-/71+ccrCl)c)#N]C)C2-s
replace # at position 42 with 4,CCOOc2ccc13C-CH]O(C(-]=O)[=@r-/71+ccrCl)c)4N]C)C2-s
remove = from position 22,CCOOc2ccc13C-CH]O(C(-]O)[=@r-/71+ccrCl)c)4N]C)C2-s
add c at position 44,CCOOc2ccc13C-CH]O(C(-]O)[=@r-/71+ccrCl)c)4N]cC)C2-s
replace l at position 37 with s,CCOOc2ccc13C-CH]O(C(-]O)[=@r-/71+ccrCs)c)4N]cC)C2-s
replace 1 at position 9 with o,CCOOc2ccco3C-CH]O(C(-]O)[=@r-/71+ccrCs)c)4N]cC)C2-s
replace ) at position 46 with 6,CCOOc2ccco3C-CH]O(C(-]O)[=@r-/71+ccrCs)c)4N]cC6C2-s
replace - at position 28 with ],CCOOc2ccco3C-CH]O(C(-]O)[=@r]/71+ccrCs)c)4N]cC6C2-s
add 1 at position 25,CCOOc2ccco3C-CH]O(C(-]O)[1=@r]/71+ccrCs)c)4N]cC6C2-s
add c at position 18,CCOOc2ccco3C-CH]O(cC(-]O)[1=@r]/71+ccrCs)c)4N]cC6C2-s
add l at position 32,CCOOc2ccco3C-CH]O(cC(-]O)[1=@r]/l71+ccrCs)c)4N]cC6C2-s
add 5 at position 44,CCOOc2ccco3C-CH]O(cC(-]O)[1=@r]/l71+ccrCs)c)54N]cC6C2-s
remove r from position 29,CCOOc2ccco3C-CH]O(cC(-]O)[1=@]/l71+ccrCs)c)54N]cC6C2-s
add n at position 16,CCOOc2ccco3C-CH]nO(cC(-]O)[1=@]/l71+ccrCs)c)54N]cC6C2-s
add s at position 27,CCOOc2ccco3C-CH]nO(cC(-]O)[s1=@]/l71+ccrCs)c)54N]cC6C2-s
add 1 at position 25,CCOOc2ccco3C-CH]nO(cC(-]O1)[s1=@]/l71+ccrCs)c)54N]cC6C2-s
remove s from position 56,CCOOc2ccco3C-CH]nO(cC(-]O1)[s1=@]/l71+ccrCs)c)54N]cC6C2-
remove ] from position 15,CCOOc2ccco3C-CHnO(cC(-]O1)[s1=@]/l71+ccrCs)c)54N]cC6C2-
replace O at position 23 with s,CCOOc2ccco3C-CHnO(cC(-]s1)[s1=@]/l71+ccrCs)c)54N]cC6C2-
replace / at position 32 with r,CCOOc2ccco3C-CHnO(cC(-]s1)[s1=@]rl71+ccrCs)c)54N]cC6C2-
remove C from position 11,CCOOc2ccco3-CHnO(cC(-]s1)[s1=@]rl71+ccrCs)c)54N]cC6C2-
replace H at position 13 with 4,CCOOc2ccco3-C4nO(cC(-]s1)[s1=@]rl71+ccrCs)c)54N]cC6C2-
add F at position 22,CCOOc2ccco3-C4nO(cC(-]Fs1)[s1=@]rl71+ccrCs)c)54N]cC6C2-
add s at position 49,CCOOc2ccco3-C4nO(cC(-]Fs1)[s1=@]rl71+ccrCs)c)54N]scC6C2-
remove ] from position 21,CCOOc2ccco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c)54N]scC6C2-
add I at position 3,CCOIOc2ccco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c)54N]scC6C2-
add l at position 9,CCOIOc2cclco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c)54N]scC6C2-
remove ) from position 45,CCOIOc2cclco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]scC6C2-
remove C from position 0,COIOc2cclco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]scC6C2-
remove O from position 3,COIc2cclco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]scC6C2-
remove c from position 48,COIc2cclco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]sC6C2-
replace 4 at position 13 with (,COIc2cclco3-C(nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]sC6C2-
remove s from position 47,COIc2cclco3-C(nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]C6C2-
remove 5 from position 43,COIc2cclco3-C(nO(cC(-Fs1)[s1=@]rl71+ccrCs)c4N]C6C2-
remove c from position 17,COIc2cclco3-C(nO(C(-Fs1)[s1=@]rl71+ccrCs)c4N]C6C2-
remove - from position 19,COIc2cclco3-C(nO(C(Fs1)[s1=@]rl71+ccrCs)c4N]C6C2-
remove ] from position 43,COIc2cclco3-C(nO(C(Fs1)[s1=@]rl71+ccrCs)c4NC6C2-
remove o from position 9,COIc2cclc3-C(nO(C(Fs1)[s1=@]rl71+ccrCs)c4NC6C2-
replace c at position 5 with ],COIc2]clc3-C(nO(C(Fs1)[s1=@]rl71+ccrCs)c4NC6C2-
remove @ from position 26,COIc2]clc3-C(nO(C(Fs1)[s1=]rl71+ccrCs)c4NC6C2-
remove s from position 36,COIc2]clc3-C(nO(C(Fs1)[s1=]rl71+ccrC)c4NC6C2-
remove l from position 7,COIc2]cc3-C(nO(C(Fs1)[s1=]rl71+ccrC)c4NC6C2-
remove F from position 17,COIc2]cc3-C(nO(C(s1)[s1=]rl71+ccrC)c4NC6C2-
add 7 at position 37,COIc2]cc3-C(nO(C(s1)[s1=]rl71+ccrC)c47NC6C2-
add 2 at position 28,COIc2]cc3-C(nO(C(s1)[s1=]rl721+ccrC)c47NC6C2-
replace C at position 10 with 7,COIc2]cc3-7(nO(C(s1)[s1=]rl721+ccrC)c47NC6C2-
remove 2 from position 4,COIc]cc3-7(nO(C(s1)[s1=]rl721+ccrC)c47NC6C2-
remove [ from position 19,COIc]cc3-7(nO(C(s1)s1=]rl721+ccrC)c47NC6C2-
remove c from position 30,COIc]cc3-7(nO(C(s1)s1=]rl721+crC)c47NC6C2-
add B at position 40,COIc]cc3-7(nO(C(s1)s1=]rl721+crC)c47NC6CB2-
add 6 at position 37,COIc]cc3-7(nO(C(s1)s1=]rl721+crC)c47N6C6CB2-
remove ] from position 22,COIc]cc3-7(nO(C(s1)s1=rl721+crC)c47N6C6CB2-
add r at position 33,COIc]cc3-7(nO(C(s1)s1=rl721+crC)cr47N6C6CB2-
remove 2 from position 42,COIc]cc3-7(nO(C(s1)s1=rl721+crC)cr47N6C6CB-
remove O from position 1,CIc]cc3-7(nO(C(s1)s1=rl721+crC)cr47N6C6CB-
replace C at position 29 with S,CIc]cc3-7(nO(C(s1)s1=rl721+crS)cr47N6C6CB-
replace I at position 1 with 6,C6c]cc3-7(nO(C(s1)s1=rl721+crS)cr47N6C6CB-
replace ) at position 30 with +,C6c]cc3-7(nO(C(s1)s1=rl721+crS+cr47N6C6CB-
replace C at position 37 with 5,C6c]cc3-7(nO(C(s1)s1=rl721+crS+cr47N656CB-
replace s at position 18 with 4,C6c]cc3-7(nO(C(s1)41=rl721+crS+cr47N656CB-
remove 5 from position 37,C6c]cc3-7(nO(C(s1)41=rl721+crS+cr47N66CB-
remove 4 from position 18,C6c]cc3-7(nO(C(s1)1=rl721+crS+cr47N66CB-
remove 4 from position 32,C6c]cc3-7(nO(C(s1)1=rl721+crS+cr7N66CB-
replace ) at position 17 with +,C6c]cc3-7(nO(C(s1+1=rl721+crS+cr7N66CB-
remove - from position 38,C6c]cc3-7(nO(C(s1+1=rl721+crS+cr7N66CB
add 5 at position 32,C6c]cc3-7(nO(C(s1+1=rl721+crS+cr57N66CB
replace + at position 29 with 4,C6c]cc3-7(nO(C(s1+1=rl721+crS4cr57N66CB
add H at position 33,C6c]cc3-7(nO(C(s1+1=rl721+crS4cr5H7N66CB
remove - from position 7,C6c]cc37(nO(C(s1+1=rl721+crS4cr5H7N66CB
replace + at position 16 with 7,C6c]cc37(nO(C(s171=rl721+crS4cr5H7N66CB
add 7 at position 1,C76c]cc37(nO(C(s171=rl721+crS4cr5H7N66CB
add 1 at position 2,C716c]cc37(nO(C(s171=rl721+crS4cr5H7N66CB
remove = from position 20,C716c]cc37(nO(C(s171rl721+crS4cr5H7N66CB
remove r from position 27,C716c]cc37(nO(C(s171rl721+cS4cr5H7N66CB
replace c at position 4 with o,C716o]cc37(nO(C(s171rl721+cS4cr5H7N66CB
replace 1 at position 19 with 4,C716o]cc37(nO(C(s174rl721+cS4cr5H7N66CB
remove C from position 37,C716o]cc37(nO(C(s174rl721+cS4cr5H7N66B
add ) at position 32,C716o]cc37(nO(C(s174rl721+cS4cr5)H7N66B
remove 7 from position 1,C16o]cc37(nO(C(s174rl721+cS4cr5)H7N66B
remove 7 from position 21,C16o]cc37(nO(C(s174rl21+cS4cr5)H7N66B
replace ( at position 14 with 1,C16o]cc37(nO(C1s174rl21+cS4cr5)H7N66B
remove 7 from position 8,C16o]cc3(nO(C1s174rl21+cS4cr5)H7N66B
replace ] at position 4 with -,C16o-cc3(nO(C1s174rl21+cS4cr5)H7N66B
remove 1 from position 15,C16o-cc3(nO(C1s74rl21+cS4cr5)H7N66B
remove 6 from position 32,C16o-cc3(nO(C1s74rl21+cS4cr5)H7N6B
replace c at position 25 with o,C16o-cc3(nO(C1s74rl21+cS4or5)H7N6B
replace ( at position 8 with [,C16o-cc3[nO(C1s74rl21+cS4or5)H7N6B
replace ) at position 28 with (,C16o-cc3[nO(C1s74rl21+cS4or5(H7N6B
remove S from position 23,C16o-cc3[nO(C1s74rl21+c4or5(H7N6B
replace 5 at position 26 with r,C16o-cc3[nO(C1s74rl21+c4orr(H7N6B
add 5 at position 4,C16o5-cc3[nO(C1s74rl21+c4orr(H7N6B
add 6 at position 12,C16o5-cc3[nO6(C1s74rl21+c4orr(H7N6B
replace 6 at position 33 with [,C16o5-cc3[nO6(C1s74rl21+c4orr(H7N[B
replace 7 at position 31 with (,C16o5-cc3[nO6(C1s74rl21+c4orr(H(N[B
add l at position 5,C16o5l-cc3[nO6(C1s74rl21+c4orr(H(N[B
remove l from position 21,C16o5l-cc3[nO6(C1s74r21+c4orr(H(N[B
add S at position 15,C16o5l-cc3[nO6(SC1s74r21+c4orr(H(N[B
replace 1 at position 1 with o,Co6o5l-cc3[nO6(SC1s74r21+c4orr(H(N[B
remove 1 from position 23,Co6o5l-cc3[nO6(SC1s74r2+c4orr(H(N[B
remove 5 from position 4,Co6ol-cc3[nO6(SC1s74r2+c4orr(H(N[B
remove ( from position 13,Co6ol-cc3[nO6SC1s74r2+c4orr(H(N[B
replace [ at position 9 with C,Co6ol-cc3CnO6SC1s74r2+c4orr(H(N[B
replace s at position 16 with N,Co6ol-cc3CnO6SC1N74r2+c4orr(H(N[B
remove ( from position 29,Co6ol-cc3CnO6SC1N74r2+c4orr(HN[B
remove 4 from position 23,Co6ol-cc3CnO6SC1N74r2+corr(HN[B
remove S from position 13,Co6ol-cc3CnO6C1N74r2+corr(HN[B
replace c at position 7 with 4,Co6ol-c43CnO6C1N74r2+corr(HN[B
add H at position 10,Co6ol-c43CHnO6C1N74r2+corr(HN[B
replace o at position 3 with F,Co6Fl-c43CHnO6C1N74r2+corr(HN[B
remove H from position 27,Co6Fl-c43CHnO6C1N74r2+corr(N[B
add H at position 18,Co6Fl-c43CHnO6C1N7H4r2+corr(N[B
remove - from position 5,Co6Flc43CHnO6C1N7H4r2+corr(N[B
remove O from position 11,Co6Flc43CHn6C1N7H4r2+corr(N[B
replace C at position 8 with [,Co6Flc43[Hn6C1N7H4r2+corr(N[B
remove c from position 5,Co6Fl43[Hn6C1N7H4r2+corr(N[B
remove 4 from position 5,Co6Fl3[Hn6C1N7H4r2+corr(N[B
replace 2 at position 17 with [,Co6Fl3[Hn6C1N7H4r[+corr(N[B
remove [ from position 6,Co6Fl3Hn6C1N7H4r[+corr(N[B
remove N from position 23,Co6Fl3Hn6C1N7H4r[+corr([B
add H at position 23,Co6Fl3Hn6C1N7H4r[+corr(H[B
replace H at position 13 with C,Co6Fl3Hn6C1N7C4r[+corr(H[B
remove o from position 19,Co6Fl3Hn6C1N7C4r[+crr(H[B
remove r from position 15,Co6Fl3Hn6C1N7C4[+crr(H[B
remove B from position 23,Co6Fl3Hn6C1N7C4[+crr(H[
remove N from position 11,Co6Fl3Hn6C17C4[+crr(H[
remove [ from position 21,Co6Fl3Hn6C17C4[+crr(H
remove C from position 12,Co6Fl3Hn6C174[+crr(H
replace l at position 4 with +,Co6F+3Hn6C174[+crr(H
replace c at position 15 with +,Co6F+3Hn6C174[++rr(H
remove + from position 4,Co6F3Hn6C174[++rr(H
replace C at position 8 with 2,Co6F3Hn62174[++rr(H
replace 6 at position 2 with 5,Co5F3Hn62174[++rr(H
remove 1 from position 9,Co5F3Hn6274[++rr(H
remove [ from position 11,Co5F3Hn6274++rr(H
remove n from position 6,Co5F3H6274++rr(H
remove ( from position 14,Co5F3H6274++rrH
remove 2 from position 7,Co5F3H674++rrH
replace o at position 1 with F,CF5F3H674++rrH
replace 7 at position 7 with ),CF5F3H6)4++rrH
add I at position 2,CFI5F3H6)4++rrH
replace I at position 2 with 7,CF75F3H6)4++rrH
remove H from position 14,CF75F3H6)4++rr
add / at position 0,/CF75F3H6)4++rr
replace F at position 5 with N,/CF75N3H6)4++rr
add c at position 1,/cCF75N3H6)4++rr
remove + from position 13,/cCF75N3H6)4+rr
remove 7 from position 4,/cCF5N3H6)4+rr
replace 3 at position 6 with [,/cCF5N[H6)4+rr
remove 4 from position 10,/cCF5N[H6)+rr
add # at position 6,/cCF5N#[H6)+rr
remove 6 from position 9,/cCF5N#[H)+rr
replace r at position 11 with 5,/cCF5N#[H)+5r
replace ) at position 9 with 3,/cCF5N#[H3+5r
add 1 at position 13,/cCF5N#[H3+5r1
remove 3 from position 9,/cCF5N#[H+5r1
remove H from position 8,/cCF5N#[+5r1
replace + at position 8 with 4,/cCF5N#[45r1
replace # at position 6 with =,/cCF5N=[45r1
remove 5 from position 9,/cCF5N=[4r1
remove F from position 3,/cC5N=[4r1
replace C at position 2 with /,/c/5N=[4r1
replace / at position 2 with (,/c(5N=[4r1
add r at position 8,/c(5N=[4rr1
replace 1 at position 10 with H,/c(5N=[4rrH
add # at position 7,/c(5N=[#4rrH
remove c from position 1,/(5N=[#4rrH
remove ( from position 1,/5N=[#4rrH
add l at position 3,/5Nl=[#4rrH
remove 4 from position 7,/5Nl=[#rrH
remove l from position 3,/5N=[#rrH
remove H from position 8,/5N=[#rr
remove r from position 7,/5N=[#r
replace # at position 5 with +,/5N=[+r
remove r from position 6,/5N=[+
add C at position 6,/5N=[+C
add 7 at position 2,/57N=[+C
remove 7 from position 2,/5N=[+C
replace / at position 0 with C,C5N=[+C
remove N from position 2,C5=[+C
remove = from position 2,C5[+C
replace + at position 3 with o,C5[oC
remove [ from position 2,C5oC
replace 5 at position 1 with ),C)oC
replace ) at position 1 with B,CBoC
add 3 at position 0,3CBoC
replace C at position 4 with 3,3CBo3
add 1 at position 3,3CB1o3
add - at position 4,3CB1-o3
replace B at position 2 with ],3C]1-o3
remove 1 from position 3,3C]-o3
remove o from position 4,3C]-3
replace C at position 1 with O,3O]-3
add # at position 4,3O]-#3
add l at position 2,3Ol]-#3
remove # from position 5,3Ol]-3
replace ] at position 3 with =,3Ol=-3
remove l from position 2,3O=-3
replace 3 at position 4 with F,3O=-F
add ] at position 4,3O=-]F
remove F from position 5,3O=-]
remove = from position 2,3O-]
remove 3 from position 0,O-]
add r at position 0,rO-]
replace O at position 1 with 7,r7-]
remove r from position 0,7-]
remove 7 from position 0,-]
replace - at position 0 with o,o]
add r at position 0,ro]
replace o at position 1 with c,rc]
add c at position 1,rcc]
add ( at position 3,rcc(]
add C at position 5,rcc(]C
add ( at position 4,rcc((]C
replace ] at position 5 with C,rcc((CC
add = at position 0,=rcc((CC
replace = at position 0 with [,[rcc((CC
replace c at position 2 with 2,[r2c((CC
replace C at position 7 with I,[r2c((CI
add 2 at position 4,[r2c2((CI
remove 2 from position 2,[rc2((CI
remove C from position 6,[rc2((I
remove c from position 2,[r2((I
remove [ from position 0,r2((I
replace ( at position 2 with O,r2O(I
remove 2 from position 1,rO(I
remove ( from position 2,rOI
remove I from position 2,rO
remove r from position 0,O
remove O from position 0,
final: ,
