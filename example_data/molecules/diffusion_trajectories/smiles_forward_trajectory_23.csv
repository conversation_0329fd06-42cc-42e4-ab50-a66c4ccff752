log,state
initialize: COC[C@@H](C)NC(=O)C(=O)Nc1cc(-c2ccccc2)nn1C(C)C,COC[C@@H](C)NC(=O)C(=O)Nc1cc(-c2ccccc2)nn1C(C)C
replace ( at position 28 with -,COC[C@@H](C)NC(=O)C(=O)Nc1cc--c2ccccc2)nn1C(C)C
add ( at position 37,COC[C@@H](C)NC(=O)C(=O)Nc1cc--c2ccccc(2)nn1C(C)C
remove @ from position 5,COC[C@H](C)NC(=O)C(=O)Nc1cc--c2ccccc(2)nn1C(C)C
add 2 at position 22,COC[C@H](C)NC(=O)C(=O)2Nc1cc--c2ccccc(2)nn1C(C)C
add 6 at position 1,C6OC[C@H](C)NC(=O)C(=O)2Nc1cc--c2ccccc(2)nn1C(C)C
remove - from position 30,C6OC[C@H](C)NC(=O)C(=O)2Nc1cc-c2ccccc(2)nn1C(C)C
remove ( from position 9,C6OC[C@H]C)NC(=O)C(=O)2Nc1cc-c2ccccc(2)nn1C(C)C
add + at position 45,C6OC[C@H]C)NC(=O)C(=O)2Nc1cc-c2ccccc(2)nn1C(C+)C
replace c at position 32 with B,C6OC[C@H]C)NC(=O)C(=O)2Nc1cc-c2cBccc(2)nn1C(C+)C
replace O at position 15 with ),C6OC[C@H]C)NC(=))C(=O)2Nc1cc-c2cBccc(2)nn1C(C+)C
remove c from position 29,C6OC[C@H]C)NC(=))C(=O)2Nc1cc-2cBccc(2)nn1C(C+)C
add ) at position 43,C6OC[C@H]C)NC(=))C(=O)2Nc1cc-2cBccc(2)nn1C()C+)C
remove O from position 2,C6C[C@H]C)NC(=))C(=O)2Nc1cc-2cBccc(2)nn1C()C+)C
remove 2 from position 35,C6C[C@H]C)NC(=))C(=O)2Nc1cc-2cBccc()nn1C()C+)C
add B at position 19,C6C[C@H]C)NC(=))C(=BO)2Nc1cc-2cBccc()nn1C()C+)C
replace ( at position 17 with 3,C6C[C@H]C)NC(=))C3=BO)2Nc1cc-2cBccc()nn1C()C+)C
remove ( from position 12,C6C[C@H]C)NC=))C3=BO)2Nc1cc-2cBccc()nn1C()C+)C
remove c from position 33,C6C[C@H]C)NC=))C3=BO)2Nc1cc-2cBcc()nn1C()C+)C
remove c from position 32,C6C[C@H]C)NC=))C3=BO)2Nc1cc-2cBc()nn1C()C+)C
add - at position 21,C6C[C@H]C)NC=))C3=BO)-2Nc1cc-2cBc()nn1C()C+)C
remove + from position 42,C6C[C@H]C)NC=))C3=BO)-2Nc1cc-2cBc()nn1C()C)C
remove [ from position 3,C6CC@H]C)NC=))C3=BO)-2Nc1cc-2cBc()nn1C()C)C
add / at position 31,C6CC@H]C)NC=))C3=BO)-2Nc1cc-2cB/c()nn1C()C)C
add - at position 13,C6CC@H]C)NC=)-)C3=BO)-2Nc1cc-2cB/c()nn1C()C)C
add ] at position 22,C6CC@H]C)NC=)-)C3=BO)-]2Nc1cc-2cB/c()nn1C()C)C
add - at position 33,C6CC@H]C)NC=)-)C3=BO)-]2Nc1cc-2cB-/c()nn1C()C)C
remove - from position 29,C6CC@H]C)NC=)-)C3=BO)-]2Nc1cc2cB-/c()nn1C()C)C
add O at position 17,C6CC@H]C)NC=)-)C3O=BO)-]2Nc1cc2cB-/c()nn1C()C)C
replace = at position 11 with 3,C6CC@H]C)NC3)-)C3O=BO)-]2Nc1cc2cB-/c()nn1C()C)C
add ] at position 9,C6CC@H]C)]NC3)-)C3O=BO)-]2Nc1cc2cB-/c()nn1C()C)C
replace C at position 2 with I,C6IC@H]C)]NC3)-)C3O=BO)-]2Nc1cc2cB-/c()nn1C()C)C
remove ) from position 22,C6IC@H]C)]NC3)-)C3O=BO-]2Nc1cc2cB-/c()nn1C()C)C
remove ) from position 37,C6IC@H]C)]NC3)-)C3O=BO-]2Nc1cc2cB-/c(nn1C()C)C
replace 2 at position 30 with (,C6IC@H]C)]NC3)-)C3O=BO-]2Nc1cc(cB-/c(nn1C()C)C
replace ( at position 30 with o,C6IC@H]C)]NC3)-)C3O=BO-]2Nc1ccocB-/c(nn1C()C)C
replace N at position 10 with @,C6IC@H]C)]@C3)-)C3O=BO-]2Nc1ccocB-/c(nn1C()C)C
remove - from position 33,C6IC@H]C)]@C3)-)C3O=BO-]2Nc1ccocB/c(nn1C()C)C
replace O at position 18 with 3,C6IC@H]C)]@C3)-)C33=BO-]2Nc1ccocB/c(nn1C()C)C
add = at position 11,C6IC@H]C)]@=C3)-)C33=BO-]2Nc1ccocB/c(nn1C()C)C
remove C from position 7,C6IC@H])]@=C3)-)C33=BO-]2Nc1ccocB/c(nn1C()C)C
replace ] at position 6 with n,C6IC@Hn)]@=C3)-)C33=BO-]2Nc1ccocB/c(nn1C()C)C
replace 2 at position 24 with n,C6IC@Hn)]@=C3)-)C33=BO-]nNc1ccocB/c(nn1C()C)C
replace C at position 16 with O,C6IC@Hn)]@=C3)-)O33=BO-]nNc1ccocB/c(nn1C()C)C
add S at position 6,C6IC@HSn)]@=C3)-)O33=BO-]nNc1ccocB/c(nn1C()C)C
remove @ from position 10,C6IC@HSn)]=C3)-)O33=BO-]nNc1ccocB/c(nn1C()C)C
remove 3 from position 17,C6IC@HSn)]=C3)-)O3=BO-]nNc1ccocB/c(nn1C()C)C
replace O at position 20 with l,C6IC@HSn)]=C3)-)O3=Bl-]nNc1ccocB/c(nn1C()C)C
replace @ at position 4 with ),C6IC)HSn)]=C3)-)O3=Bl-]nNc1ccocB/c(nn1C()C)C
remove C from position 3,C6I)HSn)]=C3)-)O3=Bl-]nNc1ccocB/c(nn1C()C)C
replace I at position 2 with 2,C62)HSn)]=C3)-)O3=Bl-]nNc1ccocB/c(nn1C()C)C
remove S from position 5,C62)Hn)]=C3)-)O3=Bl-]nNc1ccocB/c(nn1C()C)C
replace ] at position 20 with 4,C62)Hn)]=C3)-)O3=Bl-4nNc1ccocB/c(nn1C()C)C
replace H at position 4 with I,C62)In)]=C3)-)O3=Bl-4nNc1ccocB/c(nn1C()C)C
remove I from position 4,C62)n)]=C3)-)O3=Bl-4nNc1ccocB/c(nn1C()C)C
remove ) from position 12,C62)n)]=C3)-O3=Bl-4nNc1ccocB/c(nn1C()C)C
add S at position 12,C62)n)]=C3)-SO3=Bl-4nNc1ccocB/c(nn1C()C)C
replace / at position 29 with F,C62)n)]=C3)-SO3=Bl-4nNc1ccocBFc(nn1C()C)C
add r at position 23,C62)n)]=C3)-SO3=Bl-4nNcr1ccocBFc(nn1C()C)C
replace ( at position 32 with r,C62)n)]=C3)-SO3=Bl-4nNcr1ccocBFcrnn1C()C)C
remove - from position 11,C62)n)]=C3)SO3=Bl-4nNcr1ccocBFcrnn1C()C)C
replace 3 at position 13 with 5,C62)n)]=C3)SO5=Bl-4nNcr1ccocBFcrnn1C()C)C
add F at position 22,C62)n)]=C3)SO5=Bl-4nNcFr1ccocBFcrnn1C()C)C
add B at position 35,C62)n)]=C3)SO5=Bl-4nNcFr1ccocBFcrnnB1C()C)C
remove C from position 8,C62)n)]=3)SO5=Bl-4nNcFr1ccocBFcrnnB1C()C)C
add r at position 10,C62)n)]=3)rSO5=Bl-4nNcFr1ccocBFcrnnB1C()C)C
add ) at position 34,C62)n)]=3)rSO5=Bl-4nNcFr1ccocBFcrn)nB1C()C)C
remove r from position 10,C62)n)]=3)SO5=Bl-4nNcFr1ccocBFcrn)nB1C()C)C
add ( at position 43,C62)n)]=3)SO5=Bl-4nNcFr1ccocBFcrn)nB1C()C)C(
remove ) from position 9,C62)n)]=3SO5=Bl-4nNcFr1ccocBFcrn)nB1C()C)C(
replace 2 at position 2 with @,C6@)n)]=3SO5=Bl-4nNcFr1ccocBFcrn)nB1C()C)C(
remove F from position 20,C6@)n)]=3SO5=Bl-4nNcr1ccocBFcrn)nB1C()C)C(
add r at position 24,C6@)n)]=3SO5=Bl-4nNcr1ccrocBFcrn)nB1C()C)C(
replace n at position 17 with F,C6@)n)]=3SO5=Bl-4FNcr1ccrocBFcrn)nB1C()C)C(
remove c from position 19,C6@)n)]=3SO5=Bl-4FNr1ccrocBFcrn)nB1C()C)C(
remove ( from position 36,C6@)n)]=3SO5=Bl-4FNr1ccrocBFcrn)nB1C)C)C(
replace 1 at position 34 with ),C6@)n)]=3SO5=Bl-4FNr1ccrocBFcrn)nB)C)C)C(
replace F at position 17 with =,C6@)n)]=3SO5=Bl-4=Nr1ccrocBFcrn)nB)C)C)C(
remove ) from position 36,C6@)n)]=3SO5=Bl-4=Nr1ccrocBFcrn)nB)CC)C(
remove = from position 7,C6@)n)]3SO5=Bl-4=Nr1ccrocBFcrn)nB)CC)C(
remove N from position 17,C6@)n)]3SO5=Bl-4=r1ccrocBFcrn)nB)CC)C(
add 7 at position 37,C6@)n)]3SO5=Bl-4=r1ccrocBFcrn)nB)CC)C7(
add 2 at position 28,C6@)n)]3SO5=Bl-4=r1ccrocBFcr2n)nB)CC)C7(
replace 5 at position 10 with =,C6@)n)]3SO==Bl-4=r1ccrocBFcr2n)nB)CC)C7(
remove n from position 4,C6@))]3SO==Bl-4=r1ccrocBFcr2n)nB)CC)C7(
remove c from position 19,C6@))]3SO==Bl-4=r1crocBFcr2n)nB)CC)C7(
remove B from position 30,C6@))]3SO==Bl-4=r1crocBFcr2n)n)CC)C7(
add / at position 30,C6@))]3SO==Bl-4=r1crocBFcr2n)n/)CC)C7(
remove F from position 23,C6@))]3SO==Bl-4=r1crocBcr2n)n/)CC)C7(
remove B from position 22,C6@))]3SO==Bl-4=r1croccr2n)n/)CC)C7(
add r at position 33,C6@))]3SO==Bl-4=r1croccr2n)n/)CC)rC7(
remove C from position 30,C6@))]3SO==Bl-4=r1croccr2n)n/)C)rC7(
remove ) from position 29,C6@))]3SO==Bl-4=r1croccr2n)n/C)rC7(
remove 3 from position 6,C6@))]SO==Bl-4=r1croccr2n)n/C)rC7(
add ) at position 30,C6@))]SO==Bl-4=r1croccr2n)n/C))rC7(
replace c at position 21 with 6,C6@))]SO==Bl-4=r1croc6r2n)n/C))rC7(
replace r at position 18 with 4,C6@))]SO==Bl-4=r1c4oc6r2n)n/C))rC7(
remove 6 from position 21,C6@))]SO==Bl-4=r1c4ocr2n)n/C))rC7(
remove 4 from position 18,C6@))]SO==Bl-4=r1cocr2n)n/C))rC7(
remove ( from position 32,C6@))]SO==Bl-4=r1cocr2n)n/C))rC7
replace c at position 17 with ),C6@))]SO==Bl-4=r1)ocr2n)n/C))rC7
remove 4 from position 13,C6@))]SO==Bl-=r1)ocr2n)n/C))rC7
remove B from position 10,C6@))]SO==l-=r1)ocr2n)n/C))rC7
replace 1 at position 14 with 4,C6@))]SO==l-=r4)ocr2n)n/C))rC7
add H at position 16,C6@))]SO==l-=r4)Hocr2n)n/C))rC7
remove ) from position 3,C6@)]SO==l-=r4)Hocr2n)n/C))rC7
replace = at position 8 with 6,C6@)]SO=6l-=r4)Hocr2n)n/C))rC7
add 7 at position 0,7C6@)]SO=6l-=r4)Hocr2n)n/C))rC7
add 1 at position 2,7C16@)]SO=6l-=r4)Hocr2n)n/C))rC7
remove r from position 20,7C16@)]SO=6l-=r4)Hoc2n)n/C))rC7
remove = from position 13,7C16@)]SO=6l-r4)Hoc2n)n/C))rC7
replace 7 at position 29 with ),7C16@)]SO=6l-r4)Hoc2n)n/C))rC)
replace = at position 9 with 3,7C16@)]SO36l-r4)Hoc2n)n/C))rC)
remove ) from position 29,7C16@)]SO36l-r4)Hoc2n)n/C))rC
remove l from position 11,7C16@)]SO36-r4)Hoc2n)n/C))rC
remove ) from position 20,7C16@)]SO36-r4)Hoc2nn/C))rC
replace 7 at position 0 with S,SC16@)]SO36-r4)Hoc2nn/C))rC
remove 2 from position 18,SC16@)]SO36-r4)Hocnn/C))rC
add B at position 15,SC16@)]SO36-r4)BHocnn/C))rC
remove / from position 21,SC16@)]SO36-r4)BHocnnC))rC
replace @ at position 4 with r,SC16r)]SO36-r4)BHocnnC))rC
remove n from position 19,SC16r)]SO36-r4)BHocnC))rC
add s at position 17,SC16r)]SO36-r4)BHsocnC))rC
replace ) at position 22 with 1,SC16r)]SO36-r4)BHsocnC1)rC
replace r at position 24 with C,SC16r)]SO36-r4)BHsocnC1)CC
remove r from position 12,SC16r)]SO36-4)BHsocnC1)CC
replace 6 at position 10 with -,SC16r)]SO3--4)BHsocnC1)CC
remove o from position 17,SC16r)]SO3--4)BHscnC1)CC
remove C from position 19,SC16r)]SO3--4)BHscn1)CC
remove B from position 14,SC16r)]SO3--4)Hscn1)CC
add r at position 2,SCr16r)]SO3--4)Hscn1)CC
remove C from position 21,SCr16r)]SO3--4)Hscn1)C
remove C from position 1,Sr16r)]SO3--4)Hscn1)C
remove 1 from position 2,Sr6r)]SO3--4)Hscn1)C
remove C from position 19,Sr6r)]SO3--4)Hscn1)
replace 4 at position 11 with F,Sr6r)]SO3--F)Hscn1)
remove ) from position 12,Sr6r)]SO3--FHscn1)
remove r from position 1,S6r)]SO3--FHscn1)
add l at position 2,S6lr)]SO3--FHscn1)
remove - from position 10,S6lr)]SO3-FHscn1)
add S at position 7,S6lr)]SSO3-FHscn1)
replace S at position 0 with o,o6lr)]SSO3-FHscn1)
remove F from position 11,o6lr)]SSO3-Hscn1)
remove l from position 2,o6r)]SSO3-Hscn1)
remove S from position 6,o6r)]SO3-Hscn1)
replace 1 at position 13 with N,o6r)]SO3-HscnN)
remove c from position 11,o6r)]SO3-HsnN)
add O at position 2,o6Or)]SO3-HsnN)
replace H at position 10 with ],o6Or)]SO3-]snN)
add 6 at position 14,o6Or)]SO3-]snN6)
remove s from position 11,o6Or)]SO3-]nN6)
remove S from position 6,o6Or)]O3-]nN6)
replace r at position 3 with 4,o6O4)]O3-]nN6)
add H at position 5,o6O4)H]O3-]nN6)
replace 6 at position 1 with H,oHO4)H]O3-]nN6)
remove 6 from position 13,oHO4)H]O3-]nN)
add H at position 9,oHO4)H]O3H-]nN)
remove O from position 2,oH4)H]O3H-]nN)
remove ] from position 5,oH4)HO3H-]nN)
replace H at position 4 with [,oH4)[O3H-]nN)
remove 4 from position 2,oH)[O3H-]nN)
remove ) from position 2,oH[O3H-]nN)
replace n at position 8 with S,oH[O3H-]SN)
remove O from position 3,oH[3H-]SN)
remove [ from position 2,oH3H-]SN)
remove S from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
