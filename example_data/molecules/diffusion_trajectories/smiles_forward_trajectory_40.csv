log,state
initialize: CCn1cc(-c2nc(-c3cccc(Cl)c3)no2)c(=O)c2ccccc21,CCn1cc(-c2nc(-c3cccc(Cl)c3)no2)c(=O)c2ccccc21
replace o at position 28 with +,CCn1cc(-c2nc(-c3cccc(Cl)c3)n+2)c(=O)c2ccccc21
add ( at position 37,CCn1cc(-c2nc(-c3cccc(Cl)c3)n+2)c(=O)c(2ccccc21
remove c from position 5,CCn1c(-c2nc(-c3cccc(Cl)c3)n+2)c(=O)c(2ccccc21
add 2 at position 22,CCn1c(-c2nc(-c3cccc(Cl2)c3)n+2)c(=O)c(2ccccc21
add 6 at position 1,C6Cn1c(-c2nc(-c3cccc(Cl2)c3)n+2)c(=O)c(2ccccc21
remove 2 from position 30,C6Cn1c(-c2nc(-c3cccc(Cl2)c3)n+)c(=O)c(2ccccc21
remove 2 from position 9,C6Cn1c(-cnc(-c3cccc(Cl2)c3)n+)c(=O)c(2ccccc21
add + at position 45,C6Cn1c(-cnc(-c3cccc(Cl2)c3)n+)c(=O)c(2ccccc21+
replace = at position 32 with C,C6Cn1c(-cnc(-c3cccc(Cl2)c3)n+)c(CO)c(2ccccc21+
replace c at position 15 with ),C6Cn1c(-cnc(-c3)ccc(Cl2)c3)n+)c(CO)c(2ccccc21+
remove ) from position 29,C6Cn1c(-cnc(-c3)ccc(Cl2)c3)n+c(CO)c(2ccccc21+
add ) at position 43,C6Cn1c(-cnc(-c3)ccc(Cl2)c3)n+c(CO)c(2ccccc2)1+
remove C from position 2,C6n1c(-cnc(-c3)ccc(Cl2)c3)n+c(CO)c(2ccccc2)1+
remove 2 from position 35,C6n1c(-cnc(-c3)ccc(Cl2)c3)n+c(CO)c(ccccc2)1+
add B at position 19,C6n1c(-cnc(-c3)ccc(BCl2)c3)n+c(CO)c(ccccc2)1+
replace c at position 17 with 2,C6n1c(-cnc(-c3)cc2(BCl2)c3)n+c(CO)c(ccccc2)1+
remove c from position 12,C6n1c(-cnc(-3)cc2(BCl2)c3)n+c(CO)c(ccccc2)1+
remove c from position 33,C6n1c(-cnc(-3)cc2(BCl2)c3)n+c(CO)(ccccc2)1+
remove ) from position 32,C6n1c(-cnc(-3)cc2(BCl2)c3)n+c(CO(ccccc2)1+
add - at position 21,C6n1c(-cnc(-3)cc2(BCl-2)c3)n+c(CO(ccccc2)1+
remove + from position 42,C6n1c(-cnc(-3)cc2(BCl-2)c3)n+c(CO(ccccc2)1
remove 1 from position 3,C6nc(-cnc(-3)cc2(BCl-2)c3)n+c(CO(ccccc2)1
add / at position 31,C6nc(-cnc(-3)cc2(BCl-2)c3)n+c(C/O(ccccc2)1
add - at position 13,C6nc(-cnc(-3)-cc2(BCl-2)c3)n+c(C/O(ccccc2)1
add ] at position 22,C6nc(-cnc(-3)-cc2(BCl-]2)c3)n+c(C/O(ccccc2)1
add - at position 33,C6nc(-cnc(-3)-cc2(BCl-]2)c3)n+c(C-/O(ccccc2)1
remove + from position 29,C6nc(-cnc(-3)-cc2(BCl-]2)c3)nc(C-/O(ccccc2)1
add O at position 17,C6nc(-cnc(-3)-cc2O(BCl-]2)c3)nc(C-/O(ccccc2)1
replace 3 at position 11 with 4,C6nc(-cnc(-4)-cc2O(BCl-]2)c3)nc(C-/O(ccccc2)1
add ] at position 9,C6nc(-cnc](-4)-cc2O(BCl-]2)c3)nc(C-/O(ccccc2)1
replace n at position 2 with H,C6Hc(-cnc](-4)-cc2O(BCl-]2)c3)nc(C-/O(ccccc2)1
remove l from position 22,C6Hc(-cnc](-4)-cc2O(BC-]2)c3)nc(C-/O(ccccc2)1
remove c from position 37,C6Hc(-cnc](-4)-cc2O(BC-]2)c3)nc(C-/O(cccc2)1
replace c at position 30 with (,C6Hc(-cnc](-4)-cc2O(BC-]2)c3)n((C-/O(cccc2)1
replace ( at position 30 with o,C6Hc(-cnc](-4)-cc2O(BC-]2)c3)no(C-/O(cccc2)1
replace ( at position 10 with B,C6Hc(-cnc]B-4)-cc2O(BC-]2)c3)no(C-/O(cccc2)1
remove - from position 33,C6Hc(-cnc]B-4)-cc2O(BC-]2)c3)no(C/O(cccc2)1
replace O at position 18 with 3,C6Hc(-cnc]B-4)-cc23(BC-]2)c3)no(C/O(cccc2)1
add = at position 11,C6Hc(-cnc]B=-4)-cc23(BC-]2)c3)no(C/O(cccc2)1
remove n from position 7,C6Hc(-cc]B=-4)-cc23(BC-]2)c3)no(C/O(cccc2)1
replace c at position 6 with n,C6Hc(-nc]B=-4)-cc23(BC-]2)c3)no(C/O(cccc2)1
replace 2 at position 24 with n,C6Hc(-nc]B=-4)-cc23(BC-]n)c3)no(C/O(cccc2)1
replace c at position 16 with N,C6Hc(-nc]B=-4)-cN23(BC-]n)c3)no(C/O(cccc2)1
add S at position 6,C6Hc(-Snc]B=-4)-cN23(BC-]n)c3)no(C/O(cccc2)1
remove B from position 10,C6Hc(-Snc]=-4)-cN23(BC-]n)c3)no(C/O(cccc2)1
remove 2 from position 17,C6Hc(-Snc]=-4)-cN3(BC-]n)c3)no(C/O(cccc2)1
replace C at position 20 with l,C6Hc(-Snc]=-4)-cN3(Bl-]n)c3)no(C/O(cccc2)1
replace ( at position 4 with +,C6Hc+-Snc]=-4)-cN3(Bl-]n)c3)no(C/O(cccc2)1
remove c from position 3,C6H+-Snc]=-4)-cN3(Bl-]n)c3)no(C/O(cccc2)1
replace H at position 2 with 2,C62+-Snc]=-4)-cN3(Bl-]n)c3)no(C/O(cccc2)1
remove S from position 5,C62+-nc]=-4)-cN3(Bl-]n)c3)no(C/O(cccc2)1
replace ] at position 20 with 4,C62+-nc]=-4)-cN3(Bl-4n)c3)no(C/O(cccc2)1
replace - at position 4 with I,C62+Inc]=-4)-cN3(Bl-4n)c3)no(C/O(cccc2)1
remove I from position 4,C62+nc]=-4)-cN3(Bl-4n)c3)no(C/O(cccc2)1
remove c from position 12,C62+nc]=-4)-N3(Bl-4n)c3)no(C/O(cccc2)1
add S at position 12,C62+nc]=-4)-SN3(Bl-4n)c3)no(C/O(cccc2)1
replace / at position 29 with F,C62+nc]=-4)-SN3(Bl-4n)c3)no(CFO(cccc2)1
add r at position 23,C62+nc]=-4)-SN3(Bl-4n)cr3)no(CFO(cccc2)1
replace ( at position 32 with r,C62+nc]=-4)-SN3(Bl-4n)cr3)no(CFOrcccc2)1
remove - from position 11,C62+nc]=-4)SN3(Bl-4n)cr3)no(CFOrcccc2)1
replace 3 at position 13 with 5,C62+nc]=-4)SN5(Bl-4n)cr3)no(CFOrcccc2)1
add F at position 22,C62+nc]=-4)SN5(Bl-4n)cFr3)no(CFOrcccc2)1
add B at position 35,C62+nc]=-4)SN5(Bl-4n)cFr3)no(CFOrccBcc2)1
remove - from position 8,C62+nc]=4)SN5(Bl-4n)cFr3)no(CFOrccBcc2)1
add r at position 10,C62+nc]=4)rSN5(Bl-4n)cFr3)no(CFOrccBcc2)1
add ) at position 34,C62+nc]=4)rSN5(Bl-4n)cFr3)no(CFOrc)cBcc2)1
remove r from position 10,C62+nc]=4)SN5(Bl-4n)cFr3)no(CFOrc)cBcc2)1
add N at position 3,C62N+nc]=4)SN5(Bl-4n)cFr3)no(CFOrc)cBcc2)1
add r at position 9,C62N+nc]=r4)SN5(Bl-4n)cFr3)no(CFOrc)cBcc2)1
add 7 at position 27,C62N+nc]=r4)SN5(Bl-4n)cFr3)7no(CFOrc)cBcc2)1
remove N from position 13,C62N+nc]=r4)S5(Bl-4n)cFr3)7no(CFOrc)cBcc2)1
replace 3 at position 24 with s,C62N+nc]=r4)S5(Bl-4n)cFrs)7no(CFOrc)cBcc2)1
replace - at position 17 with H,C62N+nc]=r4)S5(BlH4n)cFrs)7no(CFOrc)cBcc2)1
remove n from position 19,C62N+nc]=r4)S5(BlH4)cFrs)7no(CFOrc)cBcc2)1
remove B from position 36,C62N+nc]=r4)S5(BlH4)cFrs)7no(CFOrc)ccc2)1
replace ) at position 34 with +,C62N+nc]=r4)S5(BlH4)cFrs)7no(CFOrc+ccc2)1
replace H at position 17 with =,C62N+nc]=r4)S5(Bl=4)cFrs)7no(CFOrc+ccc2)1
remove c from position 36,C62N+nc]=r4)S5(Bl=4)cFrs)7no(CFOrc+cc2)1
remove ] from position 7,C62N+nc=r4)S5(Bl=4)cFrs)7no(CFOrc+cc2)1
remove 4 from position 17,C62N+nc=r4)S5(Bl=)cFrs)7no(CFOrc+cc2)1
add 7 at position 37,C62N+nc=r4)S5(Bl=)cFrs)7no(CFOrc+cc2)71
add 2 at position 28,C62N+nc=r4)S5(Bl=)cFrs)7no(C2FOrc+cc2)71
replace ) at position 10 with =,C62N+nc=r4=S5(Bl=)cFrs)7no(C2FOrc+cc2)71
remove + from position 4,C62Nnc=r4=S5(Bl=)cFrs)7no(C2FOrc+cc2)71
remove r from position 19,C62Nnc=r4=S5(Bl=)cFs)7no(C2FOrc+cc2)71
remove c from position 30,C62Nnc=r4=S5(Bl=)cFs)7no(C2FOr+cc2)71
add / at position 30,C62Nnc=r4=S5(Bl=)cFs)7no(C2FOr/+cc2)71
remove o from position 23,C62Nnc=r4=S5(Bl=)cFs)7n(C2FOr/+cc2)71
remove n from position 22,C62Nnc=r4=S5(Bl=)cFs)7(C2FOr/+cc2)71
add r at position 33,C62Nnc=r4=S5(Bl=)cFs)7(C2FOr/+cc2r)71
remove c from position 30,C62Nnc=r4=S5(Bl=)cFs)7(C2FOr/+c2r)71
remove + from position 29,C62Nnc=r4=S5(Bl=)cFs)7(C2FOr/c2r)71
remove = from position 6,C62Nncr4=S5(Bl=)cFs)7(C2FOr/c2r)71
add ) at position 30,C62Nncr4=S5(Bl=)cFs)7(C2FOr/c2)r)71
replace ( at position 21 with 7,C62Nncr4=S5(Bl=)cFs)77C2FOr/c2)r)71
replace s at position 18 with 4,C62Nncr4=S5(Bl=)cF4)77C2FOr/c2)r)71
remove 7 from position 21,C62Nncr4=S5(Bl=)cF4)7C2FOr/c2)r)71
remove 4 from position 18,C62Nncr4=S5(Bl=)cF)7C2FOr/c2)r)71
remove 1 from position 32,C62Nncr4=S5(Bl=)cF)7C2FOr/c2)r)7
replace F at position 17 with ),C62Nncr4=S5(Bl=)c))7C2FOr/c2)r)7
remove l from position 13,C62Nncr4=S5(B=)c))7C2FOr/c2)r)7
remove 5 from position 10,C62Nncr4=S(B=)c))7C2FOr/c2)r)7
replace c at position 14 with 3,C62Nncr4=S(B=)3))7C2FOr/c2)r)7
add H at position 16,C62Nncr4=S(B=)3)H)7C2FOr/c2)r)7
remove N from position 3,C62ncr4=S(B=)3)H)7C2FOr/c2)r)7
replace S at position 8 with 6,C62ncr4=6(B=)3)H)7C2FOr/c2)r)7
add 7 at position 0,7C62ncr4=6(B=)3)H)7C2FOr/c2)r)7
add 1 at position 2,7C162ncr4=6(B=)3)H)7C2FOr/c2)r)7
remove C from position 20,7C162ncr4=6(B=)3)H)72FOr/c2)r)7
remove = from position 13,7C162ncr4=6(B)3)H)72FOr/c2)r)7
replace 7 at position 29 with ),7C162ncr4=6(B)3)H)72FOr/c2)r))
replace = at position 9 with 3,7C162ncr436(B)3)H)72FOr/c2)r))
remove ) from position 29,7C162ncr436(B)3)H)72FOr/c2)r)
remove ( from position 11,7C162ncr436B)3)H)72FOr/c2)r)
remove O from position 20,7C162ncr436B)3)H)72Fr/c2)r)
replace 7 at position 0 with S,SC162ncr436B)3)H)72Fr/c2)r)
remove 2 from position 18,SC162ncr436B)3)H)7Fr/c2)r)
add B at position 15,SC162ncr436B)3)BH)7Fr/c2)r)
remove / from position 21,SC162ncr436B)3)BH)7Frc2)r)
replace 2 at position 4 with r,SC16rncr436B)3)BH)7Frc2)r)
remove F from position 19,SC16rncr436B)3)BH)7rc2)r)
add s at position 17,SC16rncr436B)3)BHs)7rc2)r)
replace 2 at position 22 with /,SC16rncr436B)3)BHs)7rc/)r)
replace r at position 24 with C,SC16rncr436B)3)BHs)7rc/)C)
remove ) from position 12,SC16rncr436B3)BHs)7rc/)C)
replace 6 at position 10 with -,SC16rncr43-B3)BHs)7rc/)C)
remove ) from position 17,SC16rncr43-B3)BHs7rc/)C)
remove c from position 19,SC16rncr43-B3)BHs7r/)C)
remove B from position 14,SC16rncr43-B3)Hs7r/)C)
add r at position 2,SCr16rncr43-B3)Hs7r/)C)
remove C from position 21,SCr16rncr43-B3)Hs7r/))
remove C from position 1,Sr16rncr43-B3)Hs7r/))
remove 1 from position 2,Sr6rncr43-B3)Hs7r/))
remove ) from position 19,Sr6rncr43-B3)Hs7r/)
replace 3 at position 11 with F,Sr6rncr43-BF)Hs7r/)
remove ) from position 12,Sr6rncr43-BFHs7r/)
remove r from position 1,S6rncr43-BFHs7r/)
add l at position 2,S6lrncr43-BFHs7r/)
remove B from position 10,S6lrncr43-FHs7r/)
add S at position 7,S6lrncrS43-FHs7r/)
replace S at position 0 with o,o6lrncrS43-FHs7r/)
remove F from position 11,o6lrncrS43-Hs7r/)
remove l from position 2,o6rncrS43-Hs7r/)
remove S from position 6,o6rncr43-Hs7r/)
replace / at position 13 with N,o6rncr43-Hs7rN)
remove 7 from position 11,o6rncr43-HsrN)
add O at position 2,o6Orncr43-HsrN)
replace H at position 10 with ],o6Orncr43-]srN)
add 6 at position 14,o6Orncr43-]srN6)
remove s from position 11,o6Orncr43-]rN6)
remove r from position 6,o6Ornc43-]rN6)
replace r at position 3 with 4,o6O4nc43-]rN6)
add H at position 5,o6O4nHc43-]rN6)
replace 6 at position 1 with H,oHO4nHc43-]rN6)
remove 6 from position 13,oHO4nHc43-]rN)
add H at position 9,oHO4nHc43H-]rN)
remove O from position 2,oH4nHc43H-]rN)
remove c from position 5,oH4nH43H-]rN)
replace H at position 4 with [,oH4n[43H-]rN)
remove 4 from position 2,oHn[43H-]rN)
remove n from position 2,oH[43H-]rN)
replace r at position 8 with S,oH[43H-]SN)
remove 4 from position 3,oH[3H-]SN)
remove [ from position 2,oH3H-]SN)
remove S from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
