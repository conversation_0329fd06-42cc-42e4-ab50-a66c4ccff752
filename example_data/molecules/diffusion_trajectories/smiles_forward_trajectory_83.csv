log,state
initialize: CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccnc1,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccnc1
replace ( at position 28 with -,CC(C)[C@@H](CNC(=O)N1CCc2ccc-Cl)cc2C1)c1cccnc1
add ( at position 37,CC(C)[C@@H](CNC(=O)N1CCc2ccc-Cl)cc2C1()c1cccnc1
remove [ from position 5,CC(C)C@@H](CNC(=O)N1CCc2ccc-Cl)cc2C1()c1cccnc1
add 2 at position 22,CC(C)C@@H](CNC(=O)N1CC2c2ccc-Cl)cc2C1()c1cccnc1
add 6 at position 1,C6C(C)C@@H](CNC(=O)N1CC2c2ccc-Cl)cc2C1()c1cccnc1
remove C from position 30,C6C(C)C@@H](CNC(=O)N1CC2c2ccc-l)cc2C1()c1cccnc1
remove H from position 9,C6C(C)C@@](CNC(=O)N1CC2c2ccc-l)cc2C1()c1cccnc1
add + at position 45,C6C(C)C@@](CNC(=O)N1CC2c2ccc-l)cc2C1()c1cccnc+1
replace c at position 32 with B,C6C(C)C@@](CNC(=O)N1CC2c2ccc-l)cB2C1()c1cccnc+1
replace = at position 15 with ),C6C(C)C@@](CNC()O)N1CC2c2ccc-l)cB2C1()c1cccnc+1
remove l from position 29,C6C(C)C@@](CNC()O)N1CC2c2ccc-)cB2C1()c1cccnc+1
add ) at position 43,C6C(C)C@@](CNC()O)N1CC2c2ccc-)cB2C1()c1cccn)c+1
remove C from position 2,C6(C)C@@](CNC()O)N1CC2c2ccc-)cB2C1()c1cccn)c+1
remove ) from position 35,C6(C)C@@](CNC()O)N1CC2c2ccc-)cB2C1(c1cccn)c+1
add B at position 19,C6(C)C@@](CNC()O)N1BCC2c2ccc-)cB2C1(c1cccn)c+1
replace N at position 17 with 2,C6(C)C@@](CNC()O)21BCC2c2ccc-)cB2C1(c1cccn)c+1
remove C from position 12,C6(C)C@@](CN()O)21BCC2c2ccc-)cB2C1(c1cccn)c+1
remove 1 from position 33,C6(C)C@@](CN()O)21BCC2c2ccc-)cB2C(c1cccn)c+1
remove C from position 32,C6(C)C@@](CN()O)21BCC2c2ccc-)cB2(c1cccn)c+1
add - at position 21,C6(C)C@@](CN()O)21BCC-2c2ccc-)cB2(c1cccn)c+1
remove + from position 42,C6(C)C@@](CN()O)21BCC-2c2ccc-)cB2(c1cccn)c1
remove C from position 3,C6()C@@](CN()O)21BCC-2c2ccc-)cB2(c1cccn)c1
add / at position 31,C6()C@@](CN()O)21BCC-2c2ccc-)cB/2(c1cccn)c1
add - at position 13,C6()C@@](CN()-O)21BCC-2c2ccc-)cB/2(c1cccn)c1
add ] at position 22,C6()C@@](CN()-O)21BCC-]2c2ccc-)cB/2(c1cccn)c1
add - at position 33,C6()C@@](CN()-O)21BCC-]2c2ccc-)cB-/2(c1cccn)c1
remove - from position 29,C6()C@@](CN()-O)21BCC-]2c2ccc)cB-/2(c1cccn)c1
add O at position 17,C6()C@@](CN()-O)2O1BCC-]2c2ccc)cB-/2(c1cccn)c1
replace ( at position 11 with 4,C6()C@@](CN4)-O)2O1BCC-]2c2ccc)cB-/2(c1cccn)c1
add ] at position 9,C6()C@@](]CN4)-O)2O1BCC-]2c2ccc)cB-/2(c1cccn)c1
replace ( at position 2 with I,C6I)C@@](]CN4)-O)2O1BCC-]2c2ccc)cB-/2(c1cccn)c1
remove C from position 22,C6I)C@@](]CN4)-O)2O1BC-]2c2ccc)cB-/2(c1cccn)c1
remove c from position 37,C6I)C@@](]CN4)-O)2O1BC-]2c2ccc)cB-/2(1cccn)c1
replace ) at position 30 with (,C6I)C@@](]CN4)-O)2O1BC-]2c2ccc(cB-/2(1cccn)c1
replace ( at position 30 with o,C6I)C@@](]CN4)-O)2O1BC-]2c2cccocB-/2(1cccn)c1
replace C at position 10 with @,C6I)C@@](]@N4)-O)2O1BC-]2c2cccocB-/2(1cccn)c1
remove - from position 33,C6I)C@@](]@N4)-O)2O1BC-]2c2cccocB/2(1cccn)c1
replace O at position 18 with 3,C6I)C@@](]@N4)-O)231BC-]2c2cccocB/2(1cccn)c1
add = at position 11,C6I)C@@](]@=N4)-O)231BC-]2c2cccocB/2(1cccn)c1
remove ] from position 7,C6I)C@@(]@=N4)-O)231BC-]2c2cccocB/2(1cccn)c1
replace @ at position 6 with n,C6I)C@n(]@=N4)-O)231BC-]2c2cccocB/2(1cccn)c1
replace 2 at position 24 with n,C6I)C@n(]@=N4)-O)231BC-]nc2cccocB/2(1cccn)c1
replace ) at position 16 with O,C6I)C@n(]@=N4)-OO231BC-]nc2cccocB/2(1cccn)c1
add S at position 6,C6I)C@Sn(]@=N4)-OO231BC-]nc2cccocB/2(1cccn)c1
remove @ from position 10,C6I)C@Sn(]=N4)-OO231BC-]nc2cccocB/2(1cccn)c1
remove 2 from position 17,C6I)C@Sn(]=N4)-OO31BC-]nc2cccocB/2(1cccn)c1
replace C at position 20 with l,C6I)C@Sn(]=N4)-OO31Bl-]nc2cccocB/2(1cccn)c1
replace C at position 4 with ),C6I))@Sn(]=N4)-OO31Bl-]nc2cccocB/2(1cccn)c1
remove ) from position 3,C6I)@Sn(]=N4)-OO31Bl-]nc2cccocB/2(1cccn)c1
replace I at position 2 with 2,C62)@Sn(]=N4)-OO31Bl-]nc2cccocB/2(1cccn)c1
remove S from position 5,C62)@n(]=N4)-OO31Bl-]nc2cccocB/2(1cccn)c1
replace ] at position 20 with 4,C62)@n(]=N4)-OO31Bl-4nc2cccocB/2(1cccn)c1
replace @ at position 4 with I,C62)In(]=N4)-OO31Bl-4nc2cccocB/2(1cccn)c1
remove I from position 4,C62)n(]=N4)-OO31Bl-4nc2cccocB/2(1cccn)c1
remove O from position 12,C62)n(]=N4)-O31Bl-4nc2cccocB/2(1cccn)c1
add S at position 12,C62)n(]=N4)-SO31Bl-4nc2cccocB/2(1cccn)c1
replace / at position 29 with F,C62)n(]=N4)-SO31Bl-4nc2cccocBF2(1cccn)c1
add r at position 23,C62)n(]=N4)-SO31Bl-4nc2rcccocBF2(1cccn)c1
replace ( at position 32 with r,C62)n(]=N4)-SO31Bl-4nc2rcccocBF2r1cccn)c1
remove - from position 11,C62)n(]=N4)SO31Bl-4nc2rcccocBF2r1cccn)c1
replace 3 at position 13 with 5,C62)n(]=N4)SO51Bl-4nc2rcccocBF2r1cccn)c1
add F at position 22,C62)n(]=N4)SO51Bl-4nc2FrcccocBF2r1cccn)c1
add B at position 35,C62)n(]=N4)SO51Bl-4nc2FrcccocBF2r1cBccn)c1
remove N from position 8,C62)n(]=4)SO51Bl-4nc2FrcccocBF2r1cBccn)c1
add r at position 10,C62)n(]=4)rSO51Bl-4nc2FrcccocBF2r1cBccn)c1
add ) at position 34,C62)n(]=4)rSO51Bl-4nc2FrcccocBF2r1)cBccn)c1
remove r from position 10,C62)n(]=4)SO51Bl-4nc2FrcccocBF2r1)cBccn)c1
add N at position 3,C62N)n(]=4)SO51Bl-4nc2FrcccocBF2r1)cBccn)c1
add r at position 9,C62N)n(]=r4)SO51Bl-4nc2FrcccocBF2r1)cBccn)c1
add 7 at position 27,C62N)n(]=r4)SO51Bl-4nc2Frcc7cocBF2r1)cBccn)c1
remove O from position 13,C62N)n(]=r4)S51Bl-4nc2Frcc7cocBF2r1)cBccn)c1
replace 1 at position 43 with =,C62N)n(]=r4)S51Bl-4nc2Frcc7cocBF2r1)cBccn)c=
replace - at position 17 with H,C62N)n(]=r4)S51BlH4nc2Frcc7cocBF2r1)cBccn)c=
remove n from position 19,C62N)n(]=r4)S51BlH4c2Frcc7cocBF2r1)cBccn)c=
remove B from position 36,C62N)n(]=r4)S51BlH4c2Frcc7cocBF2r1)cccn)c=
replace ) at position 34 with +,C62N)n(]=r4)S51BlH4c2Frcc7cocBF2r1+cccn)c=
replace H at position 17 with =,C62N)n(]=r4)S51Bl=4c2Frcc7cocBF2r1+cccn)c=
remove c from position 36,C62N)n(]=r4)S51Bl=4c2Frcc7cocBF2r1+ccn)c=
remove ] from position 7,C62N)n(=r4)S51Bl=4c2Frcc7cocBF2r1+ccn)c=
remove 4 from position 17,C62N)n(=r4)S51Bl=c2Frcc7cocBF2r1+ccn)c=
add 7 at position 37,C62N)n(=r4)S51Bl=c2Frcc7cocBF2r1+ccn)7c=
add 2 at position 28,C62N)n(=r4)S51Bl=c2Frcc7cocB2F2r1+ccn)7c=
replace ) at position 10 with =,C62N)n(=r4=S51Bl=c2Frcc7cocB2F2r1+ccn)7c=
remove ) from position 4,C62Nn(=r4=S51Bl=c2Frcc7cocB2F2r1+ccn)7c=
remove r from position 19,C62Nn(=r4=S51Bl=c2Fcc7cocB2F2r1+ccn)7c=
remove 1 from position 30,C62Nn(=r4=S51Bl=c2Fcc7cocB2F2r+ccn)7c=
add / at position 30,C62Nn(=r4=S51Bl=c2Fcc7cocB2F2r/+ccn)7c=
remove o from position 23,C62Nn(=r4=S51Bl=c2Fcc7ccB2F2r/+ccn)7c=
remove c from position 22,C62Nn(=r4=S51Bl=c2Fcc7cB2F2r/+ccn)7c=
add r at position 33,C62Nn(=r4=S51Bl=c2Fcc7cB2F2r/+ccnr)7c=
remove c from position 30,C62Nn(=r4=S51Bl=c2Fcc7cB2F2r/+cnr)7c=
remove + from position 29,C62Nn(=r4=S51Bl=c2Fcc7cB2F2r/cnr)7c=
remove = from position 6,C62Nn(r4=S51Bl=c2Fcc7cB2F2r/cnr)7c=
add ) at position 30,C62Nn(r4=S51Bl=c2Fcc7cB2F2r/cn)r)7c=
replace c at position 21 with 6,C62Nn(r4=S51Bl=c2Fcc76B2F2r/cn)r)7c=
replace c at position 18 with 4,C62Nn(r4=S51Bl=c2F4c76B2F2r/cn)r)7c=
remove 6 from position 21,C62Nn(r4=S51Bl=c2F4c7B2F2r/cn)r)7c=
remove 4 from position 18,C62Nn(r4=S51Bl=c2Fc7B2F2r/cn)r)7c=
remove c from position 32,C62Nn(r4=S51Bl=c2Fc7B2F2r/cn)r)7=
replace F at position 17 with ),C62Nn(r4=S51Bl=c2)c7B2F2r/cn)r)7=
remove l from position 13,C62Nn(r4=S51B=c2)c7B2F2r/cn)r)7=
remove F from position 21,C62Nn(r4=S51B=c2)c7B22r/cn)r)7=
replace c at position 14 with 3,C62Nn(r4=S51B=32)c7B22r/cn)r)7=
add l at position 7,C62Nn(rl4=S51B=32)c7B22r/cn)r)7=
add - at position 23,C62Nn(rl4=S51B=32)c7B22-r/cn)r)7=
replace / at position 25 with (,C62Nn(rl4=S51B=32)c7B22-r(cn)r)7=
remove 2 from position 2,C6Nn(rl4=S51B=32)c7B22-r(cn)r)7=
replace S at position 9 with 5,C6Nn(rl4=551B=32)c7B22-r(cn)r)7=
remove ) from position 27,C6Nn(rl4=551B=32)c7B22-r(cnr)7=
replace 7 at position 29 with ),C6Nn(rl4=551B=32)c7B22-r(cnr))=
replace = at position 30 with 4,C6Nn(rl4=551B=32)c7B22-r(cnr))4
remove 2 from position 15,C6Nn(rl4=551B=3)c7B22-r(cnr))4
replace 4 at position 7 with 7,C6Nn(rl7=551B=3)c7B22-r(cnr))4
remove 2 from position 20,C6Nn(rl7=551B=3)c7B2-r(cnr))4
replace C at position 0 with S,S6Nn(rl7=551B=3)c7B2-r(cnr))4
remove ) from position 27,S6Nn(rl7=551B=3)c7B2-r(cnr)4
remove r from position 5,S6Nn(l7=551B=3)c7B2-r(cnr)4
remove ( from position 21,S6Nn(l7=551B=3)c7B2-rcnr)4
replace ( at position 4 with r,S6Nnrl7=551B=3)c7B2-rcnr)4
remove - from position 19,S6Nnrl7=551B=3)c7B2rcnr)4
add s at position 17,S6Nnrl7=551B=3)c7sB2rcnr)4
replace n at position 22 with /,S6Nnrl7=551B=3)c7sB2rc/r)4
replace ) at position 24 with F,S6Nnrl7=551B=3)c7sB2rc/rF4
remove = from position 12,S6Nnrl7=551B3)c7sB2rc/rF4
replace 1 at position 10 with -,S6Nnrl7=55-B3)c7sB2rc/rF4
remove B from position 17,S6Nnrl7=55-B3)c7s2rc/rF4
remove c from position 19,S6Nnrl7=55-B3)c7s2r/rF4
remove c from position 14,S6Nnrl7=55-B3)7s2r/rF4
add r at position 2,S6rNnrl7=55-B3)7s2r/rF4
remove F from position 21,S6rNnrl7=55-B3)7s2r/r4
remove 6 from position 1,SrNnrl7=55-B3)7s2r/r4
remove N from position 2,Srnrl7=55-B3)7s2r/r4
remove 4 from position 19,Srnrl7=55-B3)7s2r/r
replace 3 at position 11 with F,Srnrl7=55-BF)7s2r/r
remove ) from position 12,Srnrl7=55-BF7s2r/r
remove r from position 1,Snrl7=55-BF7s2r/r
add l at position 2,Snlrl7=55-BF7s2r/r
remove B from position 10,Snlrl7=55-F7s2r/r
add S at position 7,Snlrl7=S55-F7s2r/r
replace S at position 0 with o,onlrl7=S55-F7s2r/r
remove F from position 11,onlrl7=S55-7s2r/r
remove l from position 2,onrl7=S55-7s2r/r
remove S from position 6,onrl7=55-7s2r/r
replace / at position 13 with N,onrl7=55-7s2rNr
remove 2 from position 11,onrl7=55-7srNr
add O at position 2,onOrl7=55-7srNr
replace 7 at position 10 with ],onOrl7=55-]srNr
add 6 at position 14,onOrl7=55-]srN6r
remove s from position 11,onOrl7=55-]rN6r
remove = from position 6,onOrl755-]rN6r
replace r at position 3 with 4,onO4l755-]rN6r
add H at position 5,onO4lH755-]rN6r
replace n at position 1 with F,oFO4lH755-]rN6r
remove 6 from position 13,oFO4lH755-]rNr
add H at position 9,oFO4lH755H-]rNr
remove O from position 2,oF4lH755H-]rNr
remove 7 from position 5,oF4lH55H-]rNr
replace H at position 4 with [,oF4l[55H-]rNr
remove 4 from position 2,oFl[55H-]rNr
remove l from position 2,oF[55H-]rNr
replace r at position 8 with S,oF[55H-]SNr
remove 5 from position 3,oF[5H-]SNr
remove [ from position 2,oF5H-]SNr
remove S from position 6,oF5H-]Nr
remove r from position 7,oF5H-]N
replace - at position 4 with B,oF5HB]N
remove N from position 6,oF5HB]
remove B from position 4,oF5H]
add C at position 5,oF5H]C
replace ] at position 4 with -,oF5H-C
add B at position 3,oF5BH-C
add c at position 2,oFc5BH-C
replace 5 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
