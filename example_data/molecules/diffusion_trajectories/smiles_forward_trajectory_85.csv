log,state
initialize: CC(C)Nc1cccc(CNC(=O)N[C@@H]2CC[NH+](CC3CC3)C2)c1,CC(C)Nc1cccc(CNC(=O)N[C@@H]2CC[NH+](CC3CC3)C2)c1
replace C at position 28 with +,CC(C)Nc1cccc(CNC(=O)N[C@@H]2+C[NH+](CC3CC3)C2)c1
add ( at position 37,CC(C)Nc1cccc(CNC(=O)N[C@@H]2+C[NH+](C(C3CC3)C2)c1
remove N from position 5,CC(C)c1cccc(CNC(=O)N[C@@H]2+C[NH+](C(C3CC3)C2)c1
add 2 at position 22,CC(C)c1cccc(CNC(=O)N[C2@@H]2+C[NH+](C(C3CC3)C2)c1
add 6 at position 1,C6C(C)c1cccc(CNC(=O)N[C2@@H]2+C[NH+](C(C3CC3)C2)c1
remove C from position 30,C6C(C)c1cccc(CNC(=O)N[C2@@H]2+[NH+](C(C3CC3)C2)c1
remove c from position 9,C6C(C)c1ccc(CNC(=O)N[C2@@H]2+[NH+](C(C3CC3)C2)c1
add + at position 45,C6C(C)c1ccc(CNC(=O)N[C2@@H]2+[NH+](C(C3CC3)C2+)c1
replace + at position 32 with C,C6C(C)c1ccc(CNC(=O)N[C2@@H]2+[NHC](C(C3CC3)C2+)c1
replace ( at position 15 with +,C6C(C)c1ccc(CNC+=O)N[C2@@H]2+[NHC](C(C3CC3)C2+)c1
remove [ from position 29,C6C(C)c1ccc(CNC+=O)N[C2@@H]2+NHC](C(C3CC3)C2+)c1
add ) at position 43,C6C(C)c1ccc(CNC+=O)N[C2@@H]2+NHC](C(C3CC3)C)2+)c1
remove C from position 2,C6(C)c1ccc(CNC+=O)N[C2@@H]2+NHC](C(C3CC3)C)2+)c1
remove C from position 35,C6(C)c1ccc(CNC+=O)N[C2@@H]2+NHC](C(3CC3)C)2+)c1
add B at position 19,C6(C)c1ccc(CNC+=O)NB[C2@@H]2+NHC](C(3CC3)C)2+)c1
replace ) at position 17 with 3,C6(C)c1ccc(CNC+=O3NB[C2@@H]2+NHC](C(3CC3)C)2+)c1
remove N from position 12,C6(C)c1ccc(CC+=O3NB[C2@@H]2+NHC](C(3CC3)C)2+)c1
remove C from position 33,C6(C)c1ccc(CC+=O3NB[C2@@H]2+NHC]((3CC3)C)2+)c1
remove ( from position 32,C6(C)c1ccc(CC+=O3NB[C2@@H]2+NHC](3CC3)C)2+)c1
add - at position 21,C6(C)c1ccc(CC+=O3NB[C-2@@H]2+NHC](3CC3)C)2+)c1
remove + from position 42,C6(C)c1ccc(CC+=O3NB[C-2@@H]2+NHC](3CC3)C)2)c1
remove C from position 3,C6()c1ccc(CC+=O3NB[C-2@@H]2+NHC](3CC3)C)2)c1
add / at position 31,C6()c1ccc(CC+=O3NB[C-2@@H]2+NHC/](3CC3)C)2)c1
add - at position 13,C6()c1ccc(CC+-=O3NB[C-2@@H]2+NHC/](3CC3)C)2)c1
add ] at position 22,C6()c1ccc(CC+-=O3NB[C-]2@@H]2+NHC/](3CC3)C)2)c1
add - at position 33,C6()c1ccc(CC+-=O3NB[C-]2@@H]2+NHC-/](3CC3)C)2)c1
remove + from position 29,C6()c1ccc(CC+-=O3NB[C-]2@@H]2NHC-/](3CC3)C)2)c1
add O at position 17,C6()c1ccc(CC+-=O3ONB[C-]2@@H]2NHC-/](3CC3)C)2)c1
replace C at position 11 with 3,C6()c1ccc(C3+-=O3ONB[C-]2@@H]2NHC-/](3CC3)C)2)c1
add ] at position 9,C6()c1ccc](C3+-=O3ONB[C-]2@@H]2NHC-/](3CC3)C)2)c1
replace ( at position 2 with I,C6I)c1ccc](C3+-=O3ONB[C-]2@@H]2NHC-/](3CC3)C)2)c1
remove C from position 22,C6I)c1ccc](C3+-=O3ONB[-]2@@H]2NHC-/](3CC3)C)2)c1
remove 3 from position 37,C6I)c1ccc](C3+-=O3ONB[-]2@@H]2NHC-/](CC3)C)2)c1
replace N at position 30 with (,C6I)c1ccc](C3+-=O3ONB[-]2@@H]2(HC-/](CC3)C)2)c1
replace ( at position 30 with o,C6I)c1ccc](C3+-=O3ONB[-]2@@H]2oHC-/](CC3)C)2)c1
replace ( at position 10 with B,C6I)c1ccc]BC3+-=O3ONB[-]2@@H]2oHC-/](CC3)C)2)c1
remove - from position 33,C6I)c1ccc]BC3+-=O3ONB[-]2@@H]2oHC/](CC3)C)2)c1
replace O at position 18 with 3,C6I)c1ccc]BC3+-=O33NB[-]2@@H]2oHC/](CC3)C)2)c1
add = at position 11,C6I)c1ccc]B=C3+-=O33NB[-]2@@H]2oHC/](CC3)C)2)c1
remove c from position 7,C6I)c1cc]B=C3+-=O33NB[-]2@@H]2oHC/](CC3)C)2)c1
replace c at position 6 with n,C6I)c1nc]B=C3+-=O33NB[-]2@@H]2oHC/](CC3)C)2)c1
replace 2 at position 24 with n,C6I)c1nc]B=C3+-=O33NB[-]n@@H]2oHC/](CC3)C)2)c1
replace O at position 16 with N,C6I)c1nc]B=C3+-=N33NB[-]n@@H]2oHC/](CC3)C)2)c1
add S at position 6,C6I)c1Snc]B=C3+-=N33NB[-]n@@H]2oHC/](CC3)C)2)c1
remove B from position 10,C6I)c1Snc]=C3+-=N33NB[-]n@@H]2oHC/](CC3)C)2)c1
remove 3 from position 17,C6I)c1Snc]=C3+-=N3NB[-]n@@H]2oHC/](CC3)C)2)c1
replace [ at position 20 with l,C6I)c1Snc]=C3+-=N3NBl-]n@@H]2oHC/](CC3)C)2)c1
replace c at position 4 with ),C6I))1Snc]=C3+-=N3NBl-]n@@H]2oHC/](CC3)C)2)c1
remove ) from position 3,C6I)1Snc]=C3+-=N3NBl-]n@@H]2oHC/](CC3)C)2)c1
replace I at position 2 with 2,C62)1Snc]=C3+-=N3NBl-]n@@H]2oHC/](CC3)C)2)c1
remove S from position 5,C62)1nc]=C3+-=N3NBl-]n@@H]2oHC/](CC3)C)2)c1
replace ] at position 20 with 4,C62)1nc]=C3+-=N3NBl-4n@@H]2oHC/](CC3)C)2)c1
replace 1 at position 4 with I,C62)Inc]=C3+-=N3NBl-4n@@H]2oHC/](CC3)C)2)c1
remove I from position 4,C62)nc]=C3+-=N3NBl-4n@@H]2oHC/](CC3)C)2)c1
remove = from position 12,C62)nc]=C3+-N3NBl-4n@@H]2oHC/](CC3)C)2)c1
add S at position 12,C62)nc]=C3+-SN3NBl-4n@@H]2oHC/](CC3)C)2)c1
replace / at position 29 with F,C62)nc]=C3+-SN3NBl-4n@@H]2oHCF](CC3)C)2)c1
add r at position 23,C62)nc]=C3+-SN3NBl-4n@@rH]2oHCF](CC3)C)2)c1
replace ( at position 32 with r,C62)nc]=C3+-SN3NBl-4n@@rH]2oHCF]rCC3)C)2)c1
remove - from position 11,C62)nc]=C3+SN3NBl-4n@@rH]2oHCF]rCC3)C)2)c1
replace 3 at position 13 with 5,C62)nc]=C3+SN5NBl-4n@@rH]2oHCF]rCC3)C)2)c1
add F at position 22,C62)nc]=C3+SN5NBl-4n@@FrH]2oHCF]rCC3)C)2)c1
add B at position 35,C62)nc]=C3+SN5NBl-4n@@FrH]2oHCF]rCCB3)C)2)c1
remove C from position 8,C62)nc]=3+SN5NBl-4n@@FrH]2oHCF]rCCB3)C)2)c1
add r at position 10,C62)nc]=3+rSN5NBl-4n@@FrH]2oHCF]rCCB3)C)2)c1
add ) at position 34,C62)nc]=3+rSN5NBl-4n@@FrH]2oHCF]rC)CB3)C)2)c1
remove r from position 10,C62)nc]=3+SN5NBl-4n@@FrH]2oHCF]rC)CB3)C)2)c1
add ( at position 43,C62)nc]=3+SN5NBl-4n@@FrH]2oHCF]rC)CB3)C)2)c(1
remove + from position 9,C62)nc]=3SN5NBl-4n@@FrH]2oHCF]rC)CB3)C)2)c(1
replace 2 at position 2 with @,C6@)nc]=3SN5NBl-4n@@FrH]2oHCF]rC)CB3)C)2)c(1
remove F from position 20,C6@)nc]=3SN5NBl-4n@@rH]2oHCF]rC)CB3)C)2)c(1
add 7 at position 43,C6@)nc]=3SN5NBl-4n@@rH]2oHCF]rC)CB3)C)2)c(17
replace n at position 17 with F,C6@)nc]=3SN5NBl-4F@@rH]2oHCF]rC)CB3)C)2)c(17
remove @ from position 19,C6@)nc]=3SN5NBl-4F@rH]2oHCF]rC)CB3)C)2)c(17
remove ) from position 36,C6@)nc]=3SN5NBl-4F@rH]2oHCF]rC)CB3)C2)c(17
replace ) at position 34 with +,C6@)nc]=3SN5NBl-4F@rH]2oHCF]rC)CB3+C2)c(17
replace F at position 17 with =,C6@)nc]=3SN5NBl-4=@rH]2oHCF]rC)CB3+C2)c(17
remove 2 from position 36,C6@)nc]=3SN5NBl-4=@rH]2oHCF]rC)CB3+C)c(17
remove = from position 7,C6@)nc]3SN5NBl-4=@rH]2oHCF]rC)CB3+C)c(17
remove @ from position 17,C6@)nc]3SN5NBl-4=rH]2oHCF]rC)CB3+C)c(17
add 7 at position 37,C6@)nc]3SN5NBl-4=rH]2oHCF]rC)CB3+C)c(717
add 2 at position 28,C6@)nc]3SN5NBl-4=rH]2oHCF]rC2)CB3+C)c(717
replace 5 at position 10 with =,C6@)nc]3SN=NBl-4=rH]2oHCF]rC2)CB3+C)c(717
remove n from position 4,C6@)c]3SN=NBl-4=rH]2oHCF]rC2)CB3+C)c(717
remove 2 from position 19,C6@)c]3SN=NBl-4=rH]oHCF]rC2)CB3+C)c(717
remove 3 from position 30,C6@)c]3SN=NBl-4=rH]oHCF]rC2)CB+C)c(717
add / at position 30,C6@)c]3SN=NBl-4=rH]oHCF]rC2)CB/+C)c(717
remove ] from position 23,C6@)c]3SN=NBl-4=rH]oHCFrC2)CB/+C)c(717
remove F from position 22,C6@)c]3SN=NBl-4=rH]oHCrC2)CB/+C)c(717
add r at position 33,C6@)c]3SN=NBl-4=rH]oHCrC2)CB/+C)cr(717
remove C from position 30,C6@)c]3SN=NBl-4=rH]oHCrC2)CB/+)cr(717
remove + from position 29,C6@)c]3SN=NBl-4=rH]oHCrC2)CB/)cr(717
remove 3 from position 6,C6@)c]SN=NBl-4=rH]oHCrC2)CB/)cr(717
add ) at position 30,C6@)c]SN=NBl-4=rH]oHCrC2)CB/)c)r(717
replace r at position 21 with 6,C6@)c]SN=NBl-4=rH]oHC6C2)CB/)c)r(717
replace o at position 18 with 4,C6@)c]SN=NBl-4=rH]4HC6C2)CB/)c)r(717
remove 6 from position 21,C6@)c]SN=NBl-4=rH]4HCC2)CB/)c)r(717
remove 4 from position 18,C6@)c]SN=NBl-4=rH]HCC2)CB/)c)r(717
remove 1 from position 32,C6@)c]SN=NBl-4=rH]HCC2)CB/)c)r(77
replace ] at position 17 with ),C6@)c]SN=NBl-4=rH)HCC2)CB/)c)r(77
remove 4 from position 13,C6@)c]SN=NBl-=rH)HCC2)CB/)c)r(77
remove ) from position 21,C6@)c]SN=NBl-=rH)HCC2CB/)c)r(77
replace r at position 14 with 3,C6@)c]SN=NBl-=3H)HCC2CB/)c)r(77
add l at position 7,C6@)c]SlN=NBl-=3H)HCC2CB/)c)r(77
add - at position 23,C6@)c]SlN=NBl-=3H)HCC2C-B/)c)r(77
replace / at position 25 with (,C6@)c]SlN=NBl-=3H)HCC2C-B()c)r(77
remove @ from position 2,C6)c]SlN=NBl-=3H)HCC2C-B()c)r(77
replace N at position 9 with 5,C6)c]SlN=5Bl-=3H)HCC2C-B()c)r(77
remove ) from position 27,C6)c]SlN=5Bl-=3H)HCC2C-B()cr(77
replace 7 at position 29 with ),C6)c]SlN=5Bl-=3H)HCC2C-B()cr()7
replace 7 at position 30 with 4,C6)c]SlN=5Bl-=3H)HCC2C-B()cr()4
remove H from position 15,C6)c]SlN=5Bl-=3)HCC2C-B()cr()4
replace N at position 7 with 6,C6)c]Sl6=5Bl-=3)HCC2C-B()cr()4
remove C from position 20,C6)c]Sl6=5Bl-=3)HCC2-B()cr()4
replace C at position 0 with S,S6)c]Sl6=5Bl-=3)HCC2-B()cr()4
remove ) from position 27,S6)c]Sl6=5Bl-=3)HCC2-B()cr(4
remove S from position 5,S6)c]l6=5Bl-=3)HCC2-B()cr(4
remove ( from position 21,S6)c]l6=5Bl-=3)HCC2-B)cr(4
replace ] at position 4 with r,S6)crl6=5Bl-=3)HCC2-B)cr(4
remove - from position 19,S6)crl6=5Bl-=3)HCC2B)cr(4
add s at position 17,S6)crl6=5Bl-=3)HCsC2B)cr(4
replace c at position 22 with /,S6)crl6=5Bl-=3)HCsC2B)/r(4
replace ( at position 24 with F,S6)crl6=5Bl-=3)HCsC2B)/rF4
remove = from position 12,S6)crl6=5Bl-3)HCsC2B)/rF4
replace l at position 10 with -,S6)crl6=5B--3)HCsC2B)/rF4
remove C from position 17,S6)crl6=5B--3)HCs2B)/rF4
remove ) from position 19,S6)crl6=5B--3)HCs2B/rF4
remove H from position 14,S6)crl6=5B--3)Cs2B/rF4
add r at position 2,S6r)crl6=5B--3)Cs2B/rF4
remove F from position 21,S6r)crl6=5B--3)Cs2B/r4
remove 6 from position 1,Sr)crl6=5B--3)Cs2B/r4
remove ) from position 2,Srcrl6=5B--3)Cs2B/r4
remove 4 from position 19,Srcrl6=5B--3)Cs2B/r
replace 3 at position 11 with F,Srcrl6=5B--F)Cs2B/r
remove ) from position 12,Srcrl6=5B--FCs2B/r
remove r from position 1,Scrl6=5B--FCs2B/r
add l at position 2,Sclrl6=5B--FCs2B/r
remove - from position 10,Sclrl6=5B-FCs2B/r
add S at position 7,Sclrl6=S5B-FCs2B/r
replace S at position 0 with o,oclrl6=S5B-FCs2B/r
remove F from position 11,oclrl6=S5B-Cs2B/r
remove l from position 2,ocrl6=S5B-Cs2B/r
remove S from position 6,ocrl6=5B-Cs2B/r
replace / at position 13 with N,ocrl6=5B-Cs2BNr
remove 2 from position 11,ocrl6=5B-CsBNr
add O at position 2,ocOrl6=5B-CsBNr
replace C at position 10 with ],ocOrl6=5B-]sBNr
add 6 at position 14,ocOrl6=5B-]sBN6r
remove s from position 11,ocOrl6=5B-]BN6r
remove = from position 6,ocOrl65B-]BN6r
replace r at position 3 with 4,ocO4l65B-]BN6r
add H at position 5,ocO4lH65B-]BN6r
replace c at position 1 with F,oFO4lH65B-]BN6r
remove 6 from position 13,oFO4lH65B-]BNr
add H at position 9,oFO4lH65BH-]BNr
remove O from position 2,oF4lH65BH-]BNr
remove 6 from position 5,oF4lH5BH-]BNr
replace H at position 4 with [,oF4l[5BH-]BNr
remove 4 from position 2,oFl[5BH-]BNr
remove l from position 2,oF[5BH-]BNr
replace B at position 8 with [,oF[5BH-][Nr
remove 5 from position 3,oF[BH-][Nr
remove [ from position 2,oFBH-][Nr
remove [ from position 6,oFBH-]Nr
remove r from position 7,oFBH-]N
replace - at position 4 with B,oFBHB]N
remove N from position 6,oFBHB]
remove B from position 4,oFBH]
add C at position 5,oFBH]C
replace ] at position 4 with -,oFBH-C
add B at position 3,oFBBH-C
add c at position 2,oFcBBH-C
replace B at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
