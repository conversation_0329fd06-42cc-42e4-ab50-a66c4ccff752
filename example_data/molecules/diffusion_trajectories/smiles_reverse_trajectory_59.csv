log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 3,oFc3BH-C
remove c from position 2,oF3BH-<PERSON>
remove B from position 3,oF3H-<PERSON>
replace - at position 4 with [,oF3H[C
remove C from position 5,oF3H[
add B at position 4,oF3HB[
add N at position 6,oF3HB[N
replace B at position 4 with -,oF3H-[N
add r at position 7,oF3H-[Nr
add [ at position 6,oF3H-[[Nr
add [ at position 2,oF[3H-[[Nr
add 5 at position 3,oF[53H-[[Nr
replace [ at position 8 with (,oF[53H-[(Nr
add l at position 2,oFl[53H-[(Nr
add 4 at position 2,oF4l[53H-[(Nr
replace [ at position 4 with H,oF4lH53H-[(Nr
add 7 at position 5,oF4lH753H-[(Nr
add O at position 2,oFO4lH753H-[(Nr
remove H from position 9,oFO4lH753-[(Nr
add 6 at position 13,oFO4lH753-[(N6r
replace F at position 1 with c,ocO4lH753-[(N6r
remove H from position 5,ocO4l753-[(N6r
replace 4 at position 3 with r,ocOrl753-[(N6r
add 7 at position 6,ocOrl7753-[(N6r
add s at position 11,ocOrl7753-[s(N6r
remove 6 from position 14,ocOrl7753-[s(Nr
replace [ at position 10 with o,ocOrl7753-os(Nr
remove O from position 2,ocrl7753-os(Nr
add 2 at position 11,ocrl7753-os2(Nr
replace N at position 13 with 1,ocrl7753-os2(1r
add S at position 6,ocrl77S53-os2(1r
add l at position 2,oclrl77S53-os2(1r
add F at position 11,oclrl77S53-Fos2(1r
replace o at position 0 with S,Sclrl77S53-Fos2(1r
remove S from position 7,Sclrl7753-Fos2(1r
add [ at position 10,Sclrl7753-[Fos2(1r
remove l from position 2,Scrl7753-[Fos2(1r
add r at position 1,Srcrl7753-[Fos2(1r
add ) at position 12,Srcrl7753-[F)os2(1r
replace F at position 11 with 3,Srcrl7753-[3)os2(1r
add 5 at position 19,Srcrl7753-[3)os2(1r5
add l at position 2,Srlcrl7753-[3)os2(1r5
add O at position 1,SOrlcrl7753-[3)os2(1r5
add C at position 21,SOrlcrl7753-[3)os2(1rC5
remove r from position 2,SOlcrl7753-[3)os2(1rC5
add [ at position 14,SOlcrl7753-[3)[os2(1rC5
add [ at position 19,SOlcrl7753-[3)[os2([1rC5
add N at position 17,SOlcrl7753-[3)[osN2([1rC5
replace - at position 10 with c,SOlcrl7753c[3)[osN2([1rC5
add @ at position 12,SOlcrl7753c[@3)[osN2([1rC5
replace C at position 24 with O,SOlcrl7753c[@3)[osN2([1rO5
replace 1 at position 22 with (,SOlcrl7753c[@3)[osN2([(rO5
remove s from position 17,SOlcrl7753c[@3)[oN2([(rO5
add - at position 19,SOlcrl7753c[@3)[oN2-([(rO5
replace r at position 4 with c,SOlccl7753c[@3)[oN2-([(rO5
add ( at position 21,SOlccl7753c[@3)[oN2-(([(rO5
add C at position 5,SOlccCl7753c[@3)[oN2-(([(rO5
add ) at position 27,SOlccCl7753c[@3)[oN2-(([(rO)5
replace S at position 0 with C,COlccCl7753c[@3)[oN2-(([(rO)5
add ] at position 20,COlccCl7753c[@3)[oN2]-(([(rO)5
replace 7 at position 7 with (,COlccCl(753c[@3)[oN2]-(([(rO)5
add C at position 15,COlccCl(753c[@3C)[oN2]-(([(rO)5
replace 5 at position 30 with -,COlccCl(753c[@3C)[oN2]-(([(rO)-
replace ) at position 29 with 7,COlccCl(753c[@3C)[oN2]-(([(rO7-
add ) at position 27,COlccCl(753c[@3C)[oN2]-(([()rO7-
replace 5 at position 9 with N,COlccCl(7N3c[@3C)[oN2]-(([()rO7-
add = at position 2,CO=lccCl(7N3c[@3C)[oN2]-(([()rO7-
replace ( at position 25 with /,CO=lccCl(7N3c[@3C)[oN2]-(/[()rO7-
remove - from position 23,CO=lccCl(7N3c[@3C)[oN2](/[()rO7-
remove l from position 7,CO=lccC(7N3c[@3C)[oN2](/[()rO7-
replace 3 at position 14 with n,CO=lccC(7N3c[@nC)[oN2](/[()rO7-
add + at position 21,CO=lccC(7N3c[@nC)[oN2+](/[()rO7-
add H at position 13,CO=lccC(7N3c[H@nC)[oN2+](/[()rO7-
replace ) at position 17 with s,CO=lccC(7N3c[H@nCs[oN2+](/[()rO7-
add 7 at position 32,CO=lccC(7N3c[H@nCs[oN2+](/[()rO77-
add 5 at position 18,CO=lccC(7N3c[H@nCs5[oN2+](/[()rO77-
add 7 at position 21,CO=lccC(7N3c[H@nCs5[o7N2+](/[()rO77-
replace 5 at position 18 with 1,CO=lccC(7N3c[H@nCs1[o7N2+](/[()rO77-
replace 7 at position 21 with ),CO=lccC(7N3c[H@nCs1[o)N2+](/[()rO77-
remove ) from position 30,CO=lccC(7N3c[H@nCs1[o)N2+](/[(rO77-
add ] at position 6,CO=lcc]C(7N3c[H@nCs1[o)N2+](/[(rO77-
add = at position 29,CO=lcc]C(7N3c[H@nCs1[o)N2+](/=[(rO77-
add O at position 30,CO=lcc]C(7N3c[H@nCs1[o)N2+](/=O[(rO77-
remove r from position 33,CO=lcc]C(7N3c[H@nCs1[o)N2+](/=O[(O77-
add ( at position 22,CO=lcc]C(7N3c[H@nCs1[o()N2+](/=O[(O77-
add C at position 23,CO=lcc]C(7N3c[H@nCs1[o(C)N2+](/=O[(O77-
remove / from position 30,CO=lcc]C(7N3c[H@nCs1[o(C)N2+](=O[(O77-
add / at position 30,CO=lcc]C(7N3c[H@nCs1[o(C)N2+](/=O[(O77-
add C at position 19,CO=lcc]C(7N3c[H@nCsC1[o(C)N2+](/=O[(O77-
add O at position 4,CO=lOcc]C(7N3c[H@nCsC1[o(C)N2+](/=O[(O77-
replace 7 at position 10 with n,CO=lOcc]C(nN3c[H@nCsC1[o(C)N2+](/=O[(O77-
remove 2 from position 28,CO=lOcc]C(nN3c[H@nCsC1[o(C)N+](/=O[(O77-
remove 7 from position 37,CO=lOcc]C(nN3c[H@nCsC1[o(C)N+](/=O[(O7-
add 1 at position 17,CO=lOcc]C(nN3c[H@1nCsC1[o(C)N+](/=O[(O7-
add S at position 7,CO=lOccS]C(nN3c[H@1nCsC1[o(C)N+](/=O[(O7-
add # at position 36,CO=lOccS]C(nN3c[H@1nCsC1[o(C)N+](/=O#[(O7-
add l at position 26,CO=lOccS]C(nN3c[H@1nCsC1[ol(C)N+](/=O#[(O7-
replace c at position 5 with #,CO=lO#cS]C(nN3c[H@1nCsC1[ol(C)N+](/=O#[(O7-
add 3 at position 9,CO=lO#cS]3C(nN3c[H@1nCsC1[ol(C)N+](/=O#[(O7-
add H at position 43,CO=lO#cS]3C(nN3c[H@1nCsC1[ol(C)N+](/=O#[(O7H-
add ] at position 19,CO=lO#cS]3C(nN3c[H@]1nCsC1[ol(C)N+](/=O#[(O7H-
replace H at position 17 with C,CO=lO#cS]3C(nN3c[C@]1nCsC1[ol(C)N+](/=O#[(O7H-
remove 7 from position 43,CO=lO#cS]3C(nN3c[C@]1nCsC1[ol(C)N+](/=O#[(OH-
add ] at position 20,CO=lO#cS]3C(nN3c[C@]]1nCsC1[ol(C)N+](/=O#[(OH-
replace = at position 2 with H,COHlO#cS]3C(nN3c[C@]]1nCsC1[ol(C)N+](/=O#[(OH-
add = at position 9,COHlO#cS]=3C(nN3c[C@]]1nCsC1[ol(C)N+](/=O#[(OH-
remove ( from position 43,COHlO#cS]=3C(nN3c[C@]]1nCsC1[ol(C)N+](/=O#[OH-
add S at position 10,COHlO#cS]=S3C(nN3c[C@]]1nCsC1[ol(C)N+](/=O#[OH-
replace ) at position 34 with c,COHlO#cS]=S3C(nN3c[C@]]1nCsC1[ol(CcN+](/=O#[OH-
remove / from position 39,COHlO#cS]=S3C(nN3c[C@]]1nCsC1[ol(CcN+](=O#[OH-
add n at position 8,COHlO#cSn]=S3C(nN3c[C@]]1nCsC1[ol(CcN+](=O#[OH-
add c at position 35,COHlO#cSn]=S3C(nN3c[C@]]1nCsC1[ol(CccN+](=O#[OH-
add C at position 9,COHlO#cSnC]=S3C(nN3c[C@]]1nCsC1[ol(CccN+](=O#[OH-
remove l from position 3,COHO#cSnC]=S3C(nN3c[C@]]1nCsC1[ol(CccN+](=O#[OH-
add 1 at position 37,COHO#cSnC]=S3C(nN3c[C@]]1nCsC1[ol(Ccc1N+](=O#[OH-
add 5 at position 44,COHO#cSnC]=S3C(nN3c[C@]]1nCsC1[ol(Ccc1N+](=O5#[OH-
add 1 at position 13,COHO#cSnC]=S31C(nN3c[C@]]1nCsC1[ol(Ccc1N+](=O5#[OH-
add 5 at position 51,COHO#cSnC]=S31C(nN3c[C@]]1nCsC1[ol(Ccc1N+](=O5#[OH-5
add 1 at position 29,COHO#cSnC]=S31C(nN3c[C@]]1nCs1C1[ol(Ccc1N+](=O5#[OH-5
remove 1 from position 25,COHO#cSnC]=S31C(nN3c[C@]]nCs1C1[ol(Ccc1N+](=O5#[OH-5
remove s from position 27,COHO#cSnC]=S31C(nN3c[C@]]nC1C1[ol(Ccc1N+](=O5#[OH-5
remove n from position 16,COHO#cSnC]=S31C(N3c[C@]]nC1C1[ol(Ccc1N+](=O5#[OH-5
add r at position 29,COHO#cSnC]=S31C(N3c[C@]]nC1C1r[ol(Ccc1N+](=O5#[OH-5
remove 5 from position 44,COHO#cSnC]=S31C(N3c[C@]]nC1C1r[ol(Ccc1N+](=O#[OH-5
remove l from position 32,COHO#cSnC]=S31C(N3c[C@]]nC1C1r[o(Ccc1N+](=O#[OH-5
remove c from position 18,COHO#cSnC]=S31C(N3[C@]]nC1C1r[o(Ccc1N+](=O#[OH-5
remove 1 from position 25,COHO#cSnC]=S31C(N3[C@]]nCC1r[o(Ccc1N+](=O#[OH-5
replace [ at position 28 with c,COHO#cSnC]=S31C(N3[C@]]nCC1rco(Ccc1N+](=O#[OH-5
replace 5 at position 46 with ],COHO#cSnC]=S31C(N3[C@]]nCC1rco(Ccc1N+](=O#[OH-]
add ) at position 17,COHO#cSnC]=S31C(N)3[C@]]nCC1rco(Ccc1N+](=O#[OH-]
add @ at position 10,COHO#cSnC]@=S31C(N)3[C@]]nCC1rco(Ccc1N+](=O#[OH-]
remove S from position 6,COHO#cnC]@=S31C(N)3[C@]]nCC1rco(Ccc1N+](=O#[OH-]
replace N at position 16 with O,COHO#cnC]@=S31C(O)3[C@]]nCC1rco(Ccc1N+](=O#[OH-]
replace n at position 24 with 1,COHO#cnC]@=S31C(O)3[C@]]1CC1rco(Ccc1N+](=O#[OH-]
replace n at position 6 with c,COHO#ccC]@=S31C(O)3[C@]]1CC1rco(Ccc1N+](=O#[OH-]
add ( at position 7,COHO#cc(C]@=S31C(O)3[C@]]1CC1rco(Ccc1N+](=O#[OH-]
remove = from position 11,COHO#cc(C]@S31C(O)3[C@]]1CC1rco(Ccc1N+](=O#[OH-]
replace 3 at position 18 with O,COHO#cc(C]@S31C(O)O[C@]]1CC1rco(Ccc1N+](=O#[OH-]
add - at position 33,COHO#cc(C]@S31C(O)O[C@]]1CC1rco(C-cc1N+](=O#[OH-]
replace @ at position 10 with c,COHO#cc(C]cS31C(O)O[C@]]1CC1rco(C-cc1N+](=O#[OH-]
replace o at position 30 with (,COHO#cc(C]cS31C(O)O[C@]]1CC1rc((C-cc1N+](=O#[OH-]
replace ( at position 30 with c,COHO#cc(C]cS31C(O)O[C@]]1CC1rcc(C-cc1N+](=O#[OH-]
add [ at position 37,COHO#cc(C]cS31C(O)O[C@]]1CC1rcc(C-cc1[N+](=O#[OH-]
add H at position 22,COHO#cc(C]cS31C(O)O[C@H]]1CC1rcc(C-cc1[N+](=O#[OH-]
replace H at position 2 with c,COcO#cc(C]cS31C(O)O[C@H]]1CC1rcc(C-cc1[N+](=O#[OH-]
remove ] from position 9,COcO#cc(CcS31C(O)O[C@H]]1CC1rcc(C-cc1[N+](=O#[OH-]
replace 3 at position 11 with c,COcO#cc(CcSc1C(O)O[C@H]]1CC1rcc(C-cc1[N+](=O#[OH-]
remove O from position 17,COcO#cc(CcSc1C(O)[C@H]]1CC1rcc(C-cc1[N+](=O#[OH-]
add r at position 29,COcO#cc(CcSc1C(O)[C@H]]1CC1rcrc(C-cc1[N+](=O#[OH-]
remove - from position 33,COcO#cc(CcSc1C(O)[C@H]]1CC1rcrc(Ccc1[N+](=O#[OH-]
remove ] from position 22,COcO#cc(CcSc1C(O)[C@H]1CC1rcrc(Ccc1[N+](=O#[OH-]
add ) at position 9,COcO#cc(C)cSc1C(O)[C@H]1CC1rcrc(Ccc1[N+](=O#[OH-]
add H at position 4,COcOH#cc(C)cSc1C(O)[C@H]1CC1rcrc(Ccc1[N+](=O#[OH-]
remove H from position 4,COcO#cc(C)cSc1C(O)[C@H]1CC1rcrc(Ccc1[N+](=O#[OH-]
add C at position 24,COcO#cc(C)cSc1C(O)[C@H]1CCC1rcrc(Ccc1[N+](=O#[OH-]
add ) at position 45,COcO#cc(C)cSc1C(O)[C@H]1CCC1rcrc(Ccc1[N+](=O#)[OH-]
replace # at position 4 with c,COcOccc(C)cSc1C(O)[C@H]1CCC1rcrc(Ccc1[N+](=O#)[OH-]
add C at position 1,CCOcOccc(C)cSc1C(O)[C@H]1CCC1rcrc(Ccc1[N+](=O#)[OH-]
replace r at position 29 with c,CCOcOccc(C)cSc1C(O)[C@H]1CCC1ccrc(Ccc1[N+](=O#)[OH-]
add ) at position 41,CCOcOccc(C)cSc1C(O)[C@H]1CCC1ccrc(Ccc1[N+)](=O#)[OH-]
add 2 at position 22,CCOcOccc(C)cSc1C(O)[C@2H]1CCC1ccrc(Ccc1[N+)](=O#)[OH-]
replace O at position 4 with 1,CCOc1ccc(C)cSc1C(O)[C@2H]1CCC1ccrc(Ccc1[N+)](=O#)[OH-]
add = at position 17,CCOc1ccc(C)cSc1C(=O)[C@2H]1CCC1ccrc(Ccc1[N+)](=O#)[OH-]
add 1 at position 32,CCOc1ccc(C)cSc1C(=O)[C@2H]1CCC1c1crc(Ccc1[N+)](=O#)[OH-]
add ) at position 38,CCOc1ccc(C)cSc1C(=O)[C@2H]1CCC1c1crc(C)cc1[N+)](=O#)[OH-]
remove r from position 34,CCOc1ccc(C)cSc1C(=O)[C@2H]1CCC1c1cc(C)cc1[N+)](=O#)[OH-]
add N at position 15,CCOc1ccc(C)cSc1NC(=O)[C@2H]1CCC1c1cc(C)cc1[N+)](=O#)[OH-]
remove C from position 0,COc1ccc(C)cSc1NC(=O)[C@2H]1CCC1c1cc(C)cc1[N+)](=O#)[OH-]
remove S from position 11,COc1ccc(C)cc1NC(=O)[C@2H]1CCC1c1cc(C)cc1[N+)](=O#)[OH-]
add + at position 29,COc1ccc(C)cc1NC(=O)[C@2H]1CCC+1c1cc(C)cc1[N+)](=O#)[OH-]
add c at position 39,COc1ccc(C)cc1NC(=O)[C@2H]1CCC+1c1cc(C)ccc1[N+)](=O#)[OH-]
remove # from position 50,COc1ccc(C)cc1NC(=O)[C@2H]1CCC+1c1cc(C)ccc1[N+)](=O)[OH-]
remove 2 from position 22,COc1ccc(C)cc1NC(=O)[C@H]1CCC+1c1cc(C)ccc1[N+)](=O)[OH-]
remove ) from position 44,COc1ccc(C)cc1NC(=O)[C@H]1CCC+1c1cc(C)ccc1[N+](=O)[OH-]
remove H from position 51,COc1ccc(C)cc1NC(=O)[C@H]1CCC+1c1cc(C)ccc1[N+](=O)[O-]
replace + at position 28 with N,COc1ccc(C)cc1NC(=O)[C@H]1CCCN1c1cc(C)ccc1[N+](=O)[O-]
final: COc1ccc(C)cc1NC(=O)[C@H]1CCCN1c1cc(C)ccc1[N+](=O)[O-],COc1ccc(C)cc1NC(=O)[C@H]1CCCN1c1cc(C)ccc1[N+](=O)[O-]
