log,state
initialize: CCCC(=O)N1CCC(C(=O)NN=C(c2ccccc2)c2ccccc2)CC1,CCCC(=O)N1CCC(C(=O)NN=C(c2ccccc2)c2ccccc2)CC1
replace c at position 28 with +,CCCC(=O)N1CCC(C(=O)NN=C(c2cc+cc2)c2ccccc2)CC1
add ( at position 37,CCCC(=O)N1CCC(C(=O)NN=C(c2cc+cc2)c2cc(ccc2)CC1
remove = from position 5,CCCC(O)N1CCC(C(=O)NN=C(c2cc+cc2)c2cc(ccc2)CC1
add 2 at position 22,CCCC(O)N1CCC(C(=O)NN=C2(c2cc+cc2)c2cc(ccc2)CC1
add 6 at position 1,C6CCC(O)N1CCC(C(=O)NN=C2(c2cc+cc2)c2cc(ccc2)CC1
remove c from position 30,C6CCC(O)N1CCC(C(=O)NN=C2(c2cc+c2)c2cc(ccc2)CC1
remove 1 from position 9,C6CCC(O)NCCC(C(=O)NN=C2(c2cc+c2)c2cc(ccc2)CC1
add + at position 45,C6CCC(O)NCCC(C(=O)NN=C2(c2cc+c2)c2cc(ccc2)CC1+
replace c at position 32 with B,C6CCC(O)NCCC(C(=O)NN=C2(c2cc+c2)B2cc(ccc2)CC1+
replace = at position 15 with ),C6CCC(O)NCCC(C()O)NN=C2(c2cc+c2)B2cc(ccc2)CC1+
remove c from position 29,C6CCC(O)NCCC(C()O)NN=C2(c2cc+2)B2cc(ccc2)CC1+
add ) at position 43,C6CCC(O)NCCC(C()O)NN=C2(c2cc+2)B2cc(ccc2)CC)1+
remove C from position 2,C6CC(O)NCCC(C()O)NN=C2(c2cc+2)B2cc(ccc2)CC)1+
remove c from position 35,C6CC(O)NCCC(C()O)NN=C2(c2cc+2)B2cc(cc2)CC)1+
add B at position 19,C6CC(O)NCCC(C()O)NNB=C2(c2cc+2)B2cc(cc2)CC)1+
replace N at position 17 with 2,C6CC(O)NCCC(C()O)2NB=C2(c2cc+2)B2cc(cc2)CC)1+
remove C from position 12,C6CC(O)NCCC(()O)2NB=C2(c2cc+2)B2cc(cc2)CC)1+
remove c from position 33,C6CC(O)NCCC(()O)2NB=C2(c2cc+2)B2c(cc2)CC)1+
remove c from position 32,C6CC(O)NCCC(()O)2NB=C2(c2cc+2)B2(cc2)CC)1+
add - at position 21,C6CC(O)NCCC(()O)2NB=C-2(c2cc+2)B2(cc2)CC)1+
remove + from position 42,C6CC(O)NCCC(()O)2NB=C-2(c2cc+2)B2(cc2)CC)1
remove C from position 3,C6C(O)NCCC(()O)2NB=C-2(c2cc+2)B2(cc2)CC)1
add / at position 31,C6C(O)NCCC(()O)2NB=C-2(c2cc+2)B/2(cc2)CC)1
add - at position 13,C6C(O)NCCC(()-O)2NB=C-2(c2cc+2)B/2(cc2)CC)1
add ] at position 22,C6C(O)NCCC(()-O)2NB=C-]2(c2cc+2)B/2(cc2)CC)1
add - at position 33,C6C(O)NCCC(()-O)2NB=C-]2(c2cc+2)B-/2(cc2)CC)1
remove + from position 29,C6C(O)NCCC(()-O)2NB=C-]2(c2cc2)B-/2(cc2)CC)1
add O at position 17,C6C(O)NCCC(()-O)2ONB=C-]2(c2cc2)B-/2(cc2)CC)1
replace ( at position 11 with 4,C6C(O)NCCC(4)-O)2ONB=C-]2(c2cc2)B-/2(cc2)CC)1
add ] at position 9,C6C(O)NCC]C(4)-O)2ONB=C-]2(c2cc2)B-/2(cc2)CC)1
replace C at position 2 with I,C6I(O)NCC]C(4)-O)2ONB=C-]2(c2cc2)B-/2(cc2)CC)1
remove C from position 22,C6I(O)NCC]C(4)-O)2ONB=-]2(c2cc2)B-/2(cc2)CC)1
remove c from position 37,C6I(O)NCC]C(4)-O)2ONB=-]2(c2cc2)B-/2(c2)CC)1
replace 2 at position 30 with (,C6I(O)NCC]C(4)-O)2ONB=-]2(c2cc()B-/2(c2)CC)1
replace ( at position 30 with o,C6I(O)NCC]C(4)-O)2ONB=-]2(c2cco)B-/2(c2)CC)1
replace C at position 10 with @,C6I(O)NCC]@(4)-O)2ONB=-]2(c2cco)B-/2(c2)CC)1
remove - from position 33,C6I(O)NCC]@(4)-O)2ONB=-]2(c2cco)B/2(c2)CC)1
replace O at position 18 with 3,C6I(O)NCC]@(4)-O)23NB=-]2(c2cco)B/2(c2)CC)1
add = at position 11,C6I(O)NCC]@=(4)-O)23NB=-]2(c2cco)B/2(c2)CC)1
remove C from position 7,C6I(O)NC]@=(4)-O)23NB=-]2(c2cco)B/2(c2)CC)1
replace N at position 6 with n,C6I(O)nC]@=(4)-O)23NB=-]2(c2cco)B/2(c2)CC)1
replace 2 at position 24 with n,C6I(O)nC]@=(4)-O)23NB=-]n(c2cco)B/2(c2)CC)1
replace ) at position 16 with O,C6I(O)nC]@=(4)-OO23NB=-]n(c2cco)B/2(c2)CC)1
add S at position 6,C6I(O)SnC]@=(4)-OO23NB=-]n(c2cco)B/2(c2)CC)1
remove @ from position 10,C6I(O)SnC]=(4)-OO23NB=-]n(c2cco)B/2(c2)CC)1
remove 2 from position 17,C6I(O)SnC]=(4)-OO3NB=-]n(c2cco)B/2(c2)CC)1
replace = at position 20 with l,C6I(O)SnC]=(4)-OO3NBl-]n(c2cco)B/2(c2)CC)1
replace O at position 4 with ),C6I())SnC]=(4)-OO3NBl-]n(c2cco)B/2(c2)CC)1
remove ( from position 3,C6I))SnC]=(4)-OO3NBl-]n(c2cco)B/2(c2)CC)1
replace I at position 2 with 2,C62))SnC]=(4)-OO3NBl-]n(c2cco)B/2(c2)CC)1
remove S from position 5,C62))nC]=(4)-OO3NBl-]n(c2cco)B/2(c2)CC)1
replace ] at position 20 with 4,C62))nC]=(4)-OO3NBl-4n(c2cco)B/2(c2)CC)1
replace ) at position 4 with I,C62)InC]=(4)-OO3NBl-4n(c2cco)B/2(c2)CC)1
remove I from position 4,C62)nC]=(4)-OO3NBl-4n(c2cco)B/2(c2)CC)1
remove O from position 12,C62)nC]=(4)-O3NBl-4n(c2cco)B/2(c2)CC)1
add S at position 12,C62)nC]=(4)-SO3NBl-4n(c2cco)B/2(c2)CC)1
replace / at position 29 with F,C62)nC]=(4)-SO3NBl-4n(c2cco)BF2(c2)CC)1
add r at position 23,C62)nC]=(4)-SO3NBl-4n(cr2cco)BF2(c2)CC)1
replace ( at position 32 with r,C62)nC]=(4)-SO3NBl-4n(cr2cco)BF2rc2)CC)1
remove - from position 11,C62)nC]=(4)SO3NBl-4n(cr2cco)BF2rc2)CC)1
replace 3 at position 13 with 5,C62)nC]=(4)SO5NBl-4n(cr2cco)BF2rc2)CC)1
add F at position 22,C62)nC]=(4)SO5NBl-4n(cFr2cco)BF2rc2)CC)1
add B at position 35,C62)nC]=(4)SO5NBl-4n(cFr2cco)BF2rc2B)CC)1
remove ( from position 8,C62)nC]=4)SO5NBl-4n(cFr2cco)BF2rc2B)CC)1
add r at position 10,C62)nC]=4)rSO5NBl-4n(cFr2cco)BF2rc2B)CC)1
add ) at position 34,C62)nC]=4)rSO5NBl-4n(cFr2cco)BF2rc)2B)CC)1
remove r from position 10,C62)nC]=4)SO5NBl-4n(cFr2cco)BF2rc)2B)CC)1
add N at position 3,C62N)nC]=4)SO5NBl-4n(cFr2cco)BF2rc)2B)CC)1
add r at position 9,C62N)nC]=r4)SO5NBl-4n(cFr2cco)BF2rc)2B)CC)1
add 7 at position 27,C62N)nC]=r4)SO5NBl-4n(cFr2c7co)BF2rc)2B)CC)1
remove O from position 13,C62N)nC]=r4)S5NBl-4n(cFr2c7co)BF2rc)2B)CC)1
replace 2 at position 24 with s,C62N)nC]=r4)S5NBl-4n(cFrsc7co)BF2rc)2B)CC)1
replace - at position 17 with H,C62N)nC]=r4)S5NBlH4n(cFrsc7co)BF2rc)2B)CC)1
remove n from position 19,C62N)nC]=r4)S5NBlH4(cFrsc7co)BF2rc)2B)CC)1
remove B from position 36,C62N)nC]=r4)S5NBlH4(cFrsc7co)BF2rc)2)CC)1
replace ) at position 34 with +,C62N)nC]=r4)S5NBlH4(cFrsc7co)BF2rc+2)CC)1
replace H at position 17 with =,C62N)nC]=r4)S5NBl=4(cFrsc7co)BF2rc+2)CC)1
remove ) from position 36,C62N)nC]=r4)S5NBl=4(cFrsc7co)BF2rc+2CC)1
remove ] from position 7,C62N)nC=r4)S5NBl=4(cFrsc7co)BF2rc+2CC)1
remove 4 from position 17,C62N)nC=r4)S5NBl=(cFrsc7co)BF2rc+2CC)1
add 7 at position 37,C62N)nC=r4)S5NBl=(cFrsc7co)BF2rc+2CC)71
add 2 at position 28,C62N)nC=r4)S5NBl=(cFrsc7co)B2F2rc+2CC)71
replace ) at position 10 with =,C62N)nC=r4=S5NBl=(cFrsc7co)B2F2rc+2CC)71
remove ) from position 4,C62NnC=r4=S5NBl=(cFrsc7co)B2F2rc+2CC)71
remove r from position 19,C62NnC=r4=S5NBl=(cFsc7co)B2F2rc+2CC)71
remove c from position 30,C62NnC=r4=S5NBl=(cFsc7co)B2F2r+2CC)71
add / at position 30,C62NnC=r4=S5NBl=(cFsc7co)B2F2r/+2CC)71
remove o from position 23,C62NnC=r4=S5NBl=(cFsc7c)B2F2r/+2CC)71
remove c from position 22,C62NnC=r4=S5NBl=(cFsc7)B2F2r/+2CC)71
add r at position 33,C62NnC=r4=S5NBl=(cFsc7)B2F2r/+2CCr)71
remove 2 from position 30,C62NnC=r4=S5NBl=(cFsc7)B2F2r/+CCr)71
remove + from position 29,C62NnC=r4=S5NBl=(cFsc7)B2F2r/CCr)71
remove = from position 6,C62NnCr4=S5NBl=(cFsc7)B2F2r/CCr)71
add ) at position 30,C62NnCr4=S5NBl=(cFsc7)B2F2r/CC)r)71
replace ) at position 21 with 7,C62NnCr4=S5NBl=(cFsc77B2F2r/CC)r)71
replace s at position 18 with 4,C62NnCr4=S5NBl=(cF4c77B2F2r/CC)r)71
remove 7 from position 21,C62NnCr4=S5NBl=(cF4c7B2F2r/CC)r)71
remove 4 from position 18,C62NnCr4=S5NBl=(cFc7B2F2r/CC)r)71
remove 1 from position 32,C62NnCr4=S5NBl=(cFc7B2F2r/CC)r)7
replace F at position 17 with ),C62NnCr4=S5NBl=(c)c7B2F2r/CC)r)7
remove l from position 13,C62NnCr4=S5NB=(c)c7B2F2r/CC)r)7
remove 5 from position 10,C62NnCr4=SNB=(c)c7B2F2r/CC)r)7
replace c at position 14 with 3,C62NnCr4=SNB=(3)c7B2F2r/CC)r)7
add H at position 16,C62NnCr4=SNB=(3)Hc7B2F2r/CC)r)7
remove N from position 3,C62nCr4=SNB=(3)Hc7B2F2r/CC)r)7
replace S at position 8 with 6,C62nCr4=6NB=(3)Hc7B2F2r/CC)r)7
add 7 at position 0,7C62nCr4=6NB=(3)Hc7B2F2r/CC)r)7
add 1 at position 2,7C162nCr4=6NB=(3)Hc7B2F2r/CC)r)7
remove B from position 20,7C162nCr4=6NB=(3)Hc72F2r/CC)r)7
remove = from position 13,7C162nCr4=6NB(3)Hc72F2r/CC)r)7
replace 7 at position 29 with ),7C162nCr4=6NB(3)Hc72F2r/CC)r))
replace = at position 9 with 3,7C162nCr436NB(3)Hc72F2r/CC)r))
remove ) from position 29,7C162nCr436NB(3)Hc72F2r/CC)r)
remove N from position 11,7C162nCr436B(3)Hc72F2r/CC)r)
remove 2 from position 20,7C162nCr436B(3)Hc72Fr/CC)r)
replace 7 at position 0 with S,SC162nCr436B(3)Hc72Fr/CC)r)
remove 2 from position 18,SC162nCr436B(3)Hc7Fr/CC)r)
add B at position 15,SC162nCr436B(3)BHc7Fr/CC)r)
remove / from position 21,SC162nCr436B(3)BHc7FrCC)r)
replace 2 at position 4 with r,SC16rnCr436B(3)BHc7FrCC)r)
remove F from position 19,SC16rnCr436B(3)BHc7rCC)r)
add s at position 17,SC16rnCr436B(3)BHsc7rCC)r)
replace C at position 22 with /,SC16rnCr436B(3)BHsc7rC/)r)
replace r at position 24 with C,SC16rnCr436B(3)BHsc7rC/)C)
remove ( from position 12,SC16rnCr436B3)BHsc7rC/)C)
replace 6 at position 10 with -,SC16rnCr43-B3)BHsc7rC/)C)
remove c from position 17,SC16rnCr43-B3)BHs7rC/)C)
remove C from position 19,SC16rnCr43-B3)BHs7r/)C)
remove B from position 14,SC16rnCr43-B3)Hs7r/)C)
add r at position 2,SCr16rnCr43-B3)Hs7r/)C)
remove C from position 21,SCr16rnCr43-B3)Hs7r/))
remove C from position 1,Sr16rnCr43-B3)Hs7r/))
remove 1 from position 2,Sr6rnCr43-B3)Hs7r/))
remove ) from position 19,Sr6rnCr43-B3)Hs7r/)
replace 3 at position 11 with F,Sr6rnCr43-BF)Hs7r/)
remove ) from position 12,Sr6rnCr43-BFHs7r/)
remove r from position 1,S6rnCr43-BFHs7r/)
add l at position 2,S6lrnCr43-BFHs7r/)
remove B from position 10,S6lrnCr43-FHs7r/)
add S at position 7,S6lrnCrS43-FHs7r/)
replace S at position 0 with o,o6lrnCrS43-FHs7r/)
remove F from position 11,o6lrnCrS43-Hs7r/)
remove l from position 2,o6rnCrS43-Hs7r/)
remove S from position 6,o6rnCr43-Hs7r/)
replace / at position 13 with N,o6rnCr43-Hs7rN)
remove 7 from position 11,o6rnCr43-HsrN)
add O at position 2,o6OrnCr43-HsrN)
replace H at position 10 with ],o6OrnCr43-]srN)
add 6 at position 14,o6OrnCr43-]srN6)
remove s from position 11,o6OrnCr43-]rN6)
remove r from position 6,o6OrnC43-]rN6)
replace r at position 3 with 4,o6O4nC43-]rN6)
add H at position 5,o6O4nHC43-]rN6)
replace 6 at position 1 with H,oHO4nHC43-]rN6)
remove 6 from position 13,oHO4nHC43-]rN)
add H at position 9,oHO4nHC43H-]rN)
remove O from position 2,oH4nHC43H-]rN)
remove C from position 5,oH4nH43H-]rN)
replace H at position 4 with [,oH4n[43H-]rN)
remove 4 from position 2,oHn[43H-]rN)
remove n from position 2,oH[43H-]rN)
replace r at position 8 with S,oH[43H-]SN)
remove 4 from position 3,oH[3H-]SN)
remove [ from position 2,oH3H-]SN)
remove S from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
