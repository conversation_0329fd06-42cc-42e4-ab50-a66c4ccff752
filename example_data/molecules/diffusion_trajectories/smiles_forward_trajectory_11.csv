log,state
initialize: C=CCO[C@H](C)C(=O)Nc1ccc(F)cc1Br,C=CCO[C@H](C)C(=O)Nc1ccc(F)cc1Br
replace c at position 28 with +,C=CCO[C@H](C)C(=O)Nc1ccc(F)c+1Br
add O at position 2,C=OCCO[C@H](C)C(=O)Nc1ccc(F)c+1Br
remove [ from position 6,C=OCCOC@H](C)C(=O)Nc1ccc(F)c+1Br
replace = at position 15 with #,C=OCCOC@H](C)C(#O)Nc1ccc(F)c+1Br
add 6 at position 1,C6=OCCOC@H](C)C(#O)Nc1ccc(F)c+1Br
remove 1 from position 30,C6=OCCOC@H](C)C(#O)Nc1ccc(F)c+Br
remove H from position 9,C6=OCCOC@](C)C(#O)Nc1ccc(F)c+Br
add n at position 7,C6=OCCOnC@](C)C(#O)Nc1ccc(F)c+Br
add o at position 31,C6=OCCOnC@](C)C(#O)Nc1ccc(F)c+Bor
replace C at position 4 with S,C6=OSCOnC@](C)C(#O)Nc1ccc(F)c+Bor
remove + from position 29,C6=OSCOnC@](C)C(#O)Nc1ccc(F)cBor
add C at position 5,C6=OSCCOnC@](C)C(#O)Nc1ccc(F)cBor
remove # from position 17,C6=OSCCOnC@](C)C(O)Nc1ccc(F)cBor
replace S at position 4 with N,C6=ONCCOnC@](C)C(O)Nc1ccc(F)cBor
remove c from position 22,C6=ONCCOnC@](C)C(O)Nc1cc(F)cBor
remove c from position 20,C6=ONCCOnC@](C)C(O)N1cc(F)cBor
replace ) at position 14 with r,C6=ONCCOnC@](CrC(O)N1cc(F)cBor
remove C from position 0,6=ONCCOnC@](CrC(O)N1cc(F)cBor
replace O at position 2 with #,6=#NCCOnC@](CrC(O)N1cc(F)cBor
remove ( from position 22,6=#NCCOnC@](CrC(O)N1ccF)cBor
remove C from position 12,6=#NCCOnC@](rC(O)N1ccF)cBor
add H at position 2,6=H#NCCOnC@](rC(O)N1ccF)cBor
remove H from position 2,6=#NCCOnC@](rC(O)N1ccF)cBor
remove C from position 4,6=#NCOnC@](rC(O)N1ccF)cBor
add ] at position 11,6=#NCOnC@](]rC(O)N1ccF)cBor
add - at position 16,6=#NCOnC@](]rC(O-)N1ccF)cBor
remove ( from position 14,6=#NCOnC@](]rCO-)N1ccF)cBor
add O at position 8,6=#NCOnCO@](]rCO-)N1ccF)cBor
replace O at position 5 with 3,6=#NC3nCO@](]rCO-)N1ccF)cBor
add - at position 27,6=#NC3nCO@](]rCO-)N1ccF)cBo-r
replace C at position 14 with (,6=#NC3nCO@](]r(O-)N1ccF)cBo-r
remove c from position 24,6=#NC3nCO@](]r(O-)N1ccF)Bo-r
remove ] from position 10,6=#NC3nCO@(]r(O-)N1ccF)Bo-r
remove O from position 14,6=#NC3nCO@(]r(-)N1ccF)Bo-r
remove r from position 25,6=#NC3nCO@(]r(-)N1ccF)Bo-
replace c at position 19 with r,6=#NC3nCO@(]r(-)N1crF)Bo-
add ] at position 23,6=#NC3nCO@(]r(-)N1crF)B]o-
remove - from position 14,6=#NC3nCO@(]r()N1crF)B]o-
remove N from position 3,6=#C3nCO@(]r()N1crF)B]o-
replace ) at position 13 with @,6=#C3nCO@(]r(@N1crF)B]o-
replace c at position 16 with +,6=#C3nCO@(]r(@N1+rF)B]o-
replace 1 at position 15 with =,6=#C3nCO@(]r(@N=+rF)B]o-
replace ] at position 21 with 3,6=#C3nCO@(]r(@N=+rF)B3o-
remove r from position 11,6=#C3nCO@(](@N=+rF)B3o-
add c at position 22,6=#C3nCO@(](@N=+rF)B3oc-
replace ) at position 18 with s,6=#C3nCO@(](@N=+rFsB3oc-
replace 3 at position 4 with o,6=#ConCO@(](@N=+rFsB3oc-
replace - at position 23 with 6,6=#ConCO@(](@N=+rFsB3oc6
replace = at position 14 with ],6=#ConCO@(](@N]+rFsB3oc6
add 1 at position 12,6=#ConCO@(](1@N]+rFsB3oc6
add c at position 9,6=#ConCO@c(](1@N]+rFsB3oc6
add l at position 16,6=#ConCO@c(](1@Nl]+rFsB3oc6
add 5 at position 22,6=#ConCO@c(](1@Nl]+rFs5B3oc6
remove @ from position 14,6=#ConCO@c(](1Nl]+rFs5B3oc6
add n at position 8,6=#ConCOn@c(](1Nl]+rFs5B3oc6
add s at position 13,6=#ConCOn@c(]s(1Nl]+rFs5B3oc6
add 1 at position 12,6=#ConCOn@c(1]s(1Nl]+rFs5B3oc6
remove c from position 28,6=#ConCOn@c(1]s(1Nl]+rFs5B3o6
remove O from position 7,6=#ConCn@c(1]s(1Nl]+rFs5B3o6
replace 1 at position 11 with s,6=#ConCn@c(s]s(1Nl]+rFs5B3o6
replace N at position 16 with r,6=#ConCn@c(s]s(1rl]+rFs5B3o6
remove n from position 5,6=#CoCn@c(s]s(1rl]+rFs5B3o6
replace n at position 6 with 4,6=#CoC4@c(s]s(1rl]+rFs5B3o6
add F at position 11,6=#CoC4@c(sF]s(1rl]+rFs5B3o6
add s at position 24,6=#CoC4@c(sF]s(1rl]+rFs5sB3o6
remove s from position 10,6=#CoC4@c(F]s(1rl]+rFs5sB3o6
add I at position 1,6I=#CoC4@c(F]s(1rl]+rFs5sB3o6
add l at position 4,6I=#lCoC4@c(F]s(1rl]+rFs5sB3o6
remove F from position 22,6I=#lCoC4@c(F]s(1rl]+rs5sB3o6
remove 6 from position 0,I=#lCoC4@c(F]s(1rl]+rs5sB3o6
remove = from position 1,I#lCoC4@c(F]s(1rl]+rs5sB3o6
remove 3 from position 24,I#lCoC4@c(F]s(1rl]+rs5sBo6
replace 4 at position 6 with (,I#lCoC(@c(F]s(1rl]+rs5sBo6
remove B from position 23,I#lCoC(@c(F]s(1rl]+rs5so6
remove 5 from position 21,I#lCoC(@c(F]s(1rl]+rsso6
remove c from position 8,I#lCoC(@(F]s(1rl]+rsso6
remove F from position 9,I#lCoC(@(]s(1rl]+rsso6
remove 6 from position 21,I#lCoC(@(]s(1rl]+rsso
remove o from position 4,I#lCC(@(]s(1rl]+rsso
replace l at position 2 with ],I#]CC(@(]s(1rl]+rsso
remove l from position 13,I#]CC(@(]s(1r]+rsso
remove o from position 18,I#]CC(@(]s(1r]+rss
remove C from position 3,I#]C(@(]s(1r]+rss
remove s from position 8,I#]C(@(](1r]+rss
add ( at position 12,I#]C(@(](1r](+rss
remove r from position 14,I#]C(@(](1r](+ss
add / at position 15,I#]C(@(](1r](+s/s
remove I from position 0,#]C(@(](1r](+s/s
add 4 at position 12,#]C(@(](1r](4+s/s
remove / from position 15,#]C(@(](1r](4+ss
add / at position 15,#]C(@(](1r](4+s/s
remove ( from position 11,#]C(@(](1r]4+s/s
remove 4 from position 11,#]C(@(](1r]+s/s
add c at position 14,#]C(@(](1r]+s/cs
remove # from position 0,]C(@(](1r]+s/cs
replace s at position 11 with @,]C(@(](1r]+@/cs
remove C from position 1,](@(](1r]+@/cs
add B at position 12,](@(](1r]+@/Bcs
add o at position 4,](@(o](1r]+@/Bcs
replace + at position 10 with 7,](@(o](1r]7@/Bcs
replace ] at position 9 with 4,](@(o](1r47@/Bcs
remove 7 from position 10,](@(o](1r4@/Bcs
remove o from position 4,](@(](1r4@/Bcs
remove 4 from position 8,](@(](1r@/Bcs
replace ] at position 4 with ),](@()(1r@/Bcs
remove / from position 9,](@()(1r@Bcs
add 5 at position 8,](@()(1r5@Bcs
replace r at position 7 with 3,](@()(135@Bcs
add H at position 8,](@()(13H5@Bcs
remove ( from position 1,]@()(13H5@Bcs
replace ( at position 4 with 7,]@()713H5@Bcs
add 7 at position 0,7]@()713H5@Bcs
add 1 at position 0,17]@()713H5@Bcs
remove ) from position 5,17]@(713H5@Bcs
remove 1 from position 6,17]@(73H5@Bcs
replace 7 at position 1 with o,1o]@(73H5@Bcs
replace ( at position 4 with 4,1o]@473H5@Bcs
remove @ from position 9,1o]@473H5Bcs
add ) at position 8,1o]@473H)5Bcs
remove s from position 12,1o]@473H)5Bc
add 5 at position 9,1o]@473H)55Bc
replace @ at position 3 with /,1o]/473H)55Bc
remove 5 from position 10,1o]/473H)5Bc
replace ] at position 2 with r,1or/473H)5Bc
remove 5 from position 9,1or/473H)Bc
add s at position 8,1or/473Hs)Bc
replace c at position 11 with /,1or/473Hs)B/
replace s at position 8 with S,1or/473HS)B/
replace 7 at position 5 with -,1or/4-3HS)B/
remove S from position 8,1or/4-3H)B/
remove B from position 9,1or/4-3H)/
remove H from position 7,1or/4-3)/
add r at position 1,1ror/4-3)/
remove - from position 6,1ror/43)/
replace r at position 1 with 5,15or/43)/
add 6 at position 3,15o6r/43)/
replace ) at position 8 with [,15o6r/43[/
replace / at position 9 with C,15o6r/43[C
replace 1 at position 0 with B,B5o6r/43[C
replace r at position 4 with =,B5o6=/43[C
replace o at position 2 with B,B5B6=/43[C
remove 6 from position 3,B5B=/43[C
remove B from position 0,5B=/43[C
replace 3 at position 5 with I,5B=/4I[C
remove = from position 2,5B/4I[C
add 1 at position 0,15B/4I[C
replace B at position 2 with F,15F/4I[C
replace 4 at position 4 with O,15F/OI[C
remove C from position 7,15F/OI[
remove I from position 5,15F/O[
remove / from position 3,15FO[
remove F from position 2,15O[
add o at position 0,o15O[
remove 5 from position 2,o1O[
add ( at position 4,o1O[(
replace ( at position 4 with I,o1O[I
remove I from position 4,o1O[
add 6 at position 2,o16O[
replace 6 at position 2 with [,o1[O[
remove 1 from position 1,o[O[
remove [ from position 1,oO[
replace [ at position 2 with S,oOS
remove o from position 0,OS
remove O from position 0,S
remove S from position 0,
final: ,
