log,state
initialize: ,
add 1 at position 0,1
add C at position 1,1C
add N at position 2,1C<PERSON>
add ) at position 3,1CN)
replace N at position 2 with r,1Cr)
replace <PERSON> at position 1 with o,1or)
remove 1 from position 0,or)
add S at position 1,oSr)
add C at position 4,oSr)C
replace o at position 0 with l,lSr)C
remove S from position 1,lr)C
add / at position 2,lr/)C
remove l from position 0,r/)C
add r at position 0,rr/)C
add - at position 3,rr/-)C
add 4 at position 4,rr/-4)C
add n at position 3,rr/n-4)C
add 1 at position 1,r1r/n-4)C
add 7 at position 0,7r1r/n-4)C
add [ at position 10,7r1r/n-4)C[
remove r from position 1,71r/n-4)C[
add s at position 7,71r/n-4s)C[
add / at position 9,71r/n-4s)/C[
add n at position 8,71r/n-4sn)/C[
replace - at position 5 with n,71r/nn4sn)/C[
add ) at position 6,71r/nn)4sn)/C[
replace <PERSON> at position 12 with H,71r/nn)4sn)/H[
replace / at position 11 with 3,71r/nn)4sn)3H[
remove s from position 8,71r/nn)4n)3H[
add 5 at position 9,71r/nn)4n5)3H[
replace r at position 2 with N,71N/nn)4n5)3H[
add S at position 10,71N/nn)4n5S)3H[
replace / at position 3 with I,71NInn)4n5S)3H[
remove 5 from position 9,71NInn)4nS)3H[
add ( at position 12,71NInn)4nS)3(H[
add o at position 2,71oNInn)4nS)3(H[
add 3 at position 7,71oNInn3)4nS)3(H[
replace 4 at position 9 with 2,71oNInn3)2nS)3(H[
replace o at position 2 with 7,717NInn3)2nS)3(H[
add c at position 13,717NInn3)2nS)c3(H[
add 6 at position 10,717NInn3)26nS)c3(H[
remove 1 from position 1,77NInn3)26nS)c3(H[
remove 7 from position 0,7NInn3)26nS)c3(H[
replace 6 at position 8 with ],7NInn3)2]nS)c3(H[
add 7 at position 3,7NI7nn3)2]nS)c3(H[
remove H from position 16,7NI7nn3)2]nS)c3([
replace 3 at position 14 with 5,7NI7nn3)2]nS)c5([
add s at position 10,7NI7nn3)2]snS)c5([
add ] at position 6,7NI7nn]3)2]snS)c5([
replace ) at position 8 with C,7NI7nn]3C2]snS)c5([
add / at position 16,7NI7nn]3C2]snS)c/5([
add 5 at position 9,7NI7nn]3C52]snS)c/5([
add 6 at position 18,7NI7nn]3C52]snS)c/65([
replace 5 at position 9 with (,7NI7nn]3C(2]snS)c/65([
replace 6 at position 18 with ),7NI7nn]3C(2]snS)c/)5([
replace ) at position 15 with r,7NI7nn]3C(2]snSrc/)5([
replace 7 at position 0 with 6,6NI7nn]3C(2]snSrc/)5([
replace S at position 14 with 7,6NI7nn]3C(2]sn7rc/)5([
add C at position 0,C6NI7nn]3C(2]sn7rc/)5([
add - at position 21,C6NI7nn]3C(2]sn7rc/)5-([
remove r from position 16,C6NI7nn]3C(2]sn7c/)5-([
add F at position 11,C6NI7nn]3C(F2]sn7c/)5-([
add 2 at position 11,C6NI7nn]3C(2F2]sn7c/)5-([
add 7 at position 5,C6NI77nn]3C(2F2]sn7c/)5-([
replace 7 at position 5 with C,C6NI7Cnn]3C(2F2]sn7c/)5-([
add s at position 21,C6NI7Cnn]3C(2F2]sn7c/s)5-([
add l at position 9,C6NI7Cnn]l3C(2F2]sn7c/s)5-([
add 2 at position 2,C62NI7Cnn]l3C(2F2]sn7c/s)5-([
replace 7 at position 5 with c,C62NIcCnn]l3C(2F2]sn7c/s)5-([
replace 2 at position 14 with 4,C62NIcCnn]l3C(4F2]sn7c/s)5-([
add = at position 11,C62NIcCnn]l=3C(4F2]sn7c/s)5-([
add l at position 18,C62NIcCnn]l=3C(4F2l]sn7c/s)5-([
add 2 at position 19,C62NIcCnn]l=3C(4F2l2]sn7c/s)5-([
add c at position 24,C62NIcCnn]l=3C(4F2l2]sn7cc/s)5-([
add 3 at position 17,C62NIcCnn]l=3C(4F32l2]sn7cc/s)5-([
add 1 at position 26,C62NIcCnn]l=3C(4F32l2]sn7c1c/s)5-([
replace c at position 5 with ),C62NI)Cnn]l=3C(4F32l2]sn7c1c/s)5-([
add r at position 9,C62NI)Cnnr]l=3C(4F32l2]sn7c1c/s)5-([
add n at position 19,C62NI)Cnnr]l=3C(4F3n2l2]sn7c1c/s)5-([
replace F at position 17 with O,C62NI)Cnnr]l=3C(4O3n2l2]sn7c1c/s)5-([
replace s at position 24 with F,C62NI)Cnnr]l=3C(4O3n2l2]Fn7c1c/s)5-([
add B at position 13,C62NI)Cnnr]l=B3C(4O3n2l2]Fn7c1c/s)5-([
remove 7 from position 27,C62NI)Cnnr]l=B3C(4O3n2l2]Fnc1c/s)5-([
remove r from position 9,C62NI)Cnn]l=B3C(4O3n2l2]Fnc1c/s)5-([
remove N from position 3,C62I)Cnn]l=B3C(4O3n2l2]Fnc1c/s)5-([
add c at position 20,C62I)Cnn]l=B3C(4O3n2cl2]Fnc1c/s)5-([
remove l from position 9,C62I)Cnn]=B3C(4O3n2cl2]Fnc1c/s)5-([
remove I from position 3,C62)Cnn]=B3C(4O3n2cl2]Fnc1c/s)5-([
remove 5 from position 30,C62)Cnn]=B3C(4O3n2cl2]Fnc1c/s)-([
remove F from position 22,C62)Cnn]=B3C(4O3n2cl2]nc1c/s)-([
replace 4 at position 13 with F,C62)Cnn]=B3C(FO3n2cl2]nc1c/s)-([
replace / at position 26 with 2,C62)Cnn]=B3C(FO3n2cl2]nc1c2s)-([
replace F at position 13 with =,C62)Cnn]=B3C(=O3n2cl2]nc1c2s)-([
add c at position 23,C62)Cnn]=B3C(=O3n2cl2]ncc1c2s)-([
add ) at position 29,C62)Cnn]=B3C(=O3n2cl2]ncc1c2s))-([
remove 1 from position 25,C62)Cnn]=B3C(=O3n2cl2]nccc2s))-([
remove s from position 27,C62)Cnn]=B3C(=O3n2cl2]nccc2))-([
remove n from position 16,C62)Cnn]=B3C(=O32cl2]nccc2))-([
replace B at position 9 with ),C62)Cnn]=)3C(=O32cl2]nccc2))-([
replace [ at position 30 with 1,C62)Cnn]=)3C(=O32cl2]nccc2))-(1
add S at position 5,C62)CSnn]=)3C(=O32cl2]nccc2))-(1
replace 2 at position 2 with H,C6H)CSnn]=)3C(=O32cl2]nccc2))-(1
add 1 at position 3,C6H1)CSnn]=)3C(=O32cl2]nccc2))-(1
replace ) at position 4 with N,C6H1NCSnn]=)3C(=O32cl2]nccc2))-(1
replace l at position 20 with c,C6H1NCSnn]=)3C(=O32cc2]nccc2))-(1
add c at position 17,C6H1NCSnn]=)3C(=Oc32cc2]nccc2))-(1
add B at position 10,C6H1NCSnn]B=)3C(=Oc32cc2]nccc2))-(1
remove S from position 6,C6H1NCnn]B=)3C(=Oc32cc2]nccc2))-(1
replace O at position 16 with ),C6H1NCnn]B=)3C(=)c32cc2]nccc2))-(1
replace n at position 24 with c,C6H1NCnn]B=)3C(=)c32cc2]cccc2))-(1
replace n at position 6 with c,C6H1NCcn]B=)3C(=)c32cc2]cccc2))-(1
add C at position 7,C6H1NCcCn]B=)3C(=)c32cc2]cccc2))-(1
remove = from position 11,C6H1NCcCn]B)3C(=)c32cc2]cccc2))-(1
replace 3 at position 18 with O,C6H1NCcCn]B)3C(=)cO2cc2]cccc2))-(1
add o at position 33,C6H1NCcCn]B)3C(=)cO2cc2]cccc2))-(o1
replace B at position 10 with =,C6H1NCcCn]=)3C(=)cO2cc2]cccc2))-(o1
add c at position 30,C6H1NCcCn]=)3C(=)cO2cc2]cccc2)c)-(o1
add o at position 29,C6H1NCcCn]=)3C(=)cO2cc2]cccc2o)c)-(o1
add - at position 22,C6H1NCcCn]=)3C(=)cO2cc-2]cccc2o)c)-(o1
replace H at position 2 with c,C6c1NCcCn]=)3C(=)cO2cc-2]cccc2o)c)-(o1
remove ] from position 9,C6c1NCcCn=)3C(=)cO2cc-2]cccc2o)c)-(o1
replace 3 at position 11 with N,C6c1NCcCn=)NC(=)cO2cc-2]cccc2o)c)-(o1
remove O from position 17,C6c1NCcCn=)NC(=)c2cc-2]cccc2o)c)-(o1
add 3 at position 29,C6c1NCcCn=)NC(=)c2cc-2]cccc2o3)c)-(o1
remove - from position 33,C6c1NCcCn=)NC(=)c2cc-2]cccc2o3)c)(o1
remove ] from position 22,C6c1NCcCn=)NC(=)c2cc-2cccc2o3)c)(o1
add ( at position 9,C6c1NCcCn(=)NC(=)c2cc-2cccc2o3)c)(o1
add H at position 4,C6c1HNCcCn(=)NC(=)c2cc-2cccc2o3)c)(o1
remove H from position 4,C6c1NCcCn(=)NC(=)c2cc-2cccc2o3)c)(o1
add 3 at position 24,C6c1NCcCn(=)NC(=)c2cc-2c3ccc2o3)c)(o1
remove - from position 21,C6c1NCcCn(=)NC(=)c2cc2c3ccc2o3)c)(o1
add ( at position 32,C6c1NCcCn(=)NC(=)c2cc2c3ccc2o3)c()(o1
add C at position 33,C6c1NCcCn(=)NC(=)c2cc2c3ccc2o3)c(C)(o1
add N at position 12,C6c1NCcCn(=)NNC(=)c2cc2c3ccc2o3)c(C)(o1
add c at position 22,C6c1NCcCn(=)NNC(=)c2ccc2c3ccc2o3)c(C)(o1
replace N at position 4 with O,C6c1OCcCn(=)NNC(=)c2ccc2c3ccc2o3)c(C)(o1
add O at position 17,C6c1OCcCn(=)NNC(=O)c2ccc2c3ccc2o3)c(C)(o1
remove C from position 5,C6c1OcCn(=)NNC(=O)c2ccc2c3ccc2o3)c(C)(o1
add + at position 29,C6c1OcCn(=)NNC(=O)c2ccc2c3ccc+2o3)c(C)(o1
replace O at position 4 with c,C6c1ccCn(=)NNC(=O)c2ccc2c3ccc+2o3)c(C)(o1
remove o from position 31,C6c1ccCn(=)NNC(=O)c2ccc2c3ccc+23)c(C)(o1
remove n from position 7,C6c1ccC(=)NNC(=O)c2ccc2c3ccc+23)c(C)(o1
add O at position 9,C6c1ccC(=O)NNC(=O)c2ccc2c3ccc+23)c(C)(o1
add c at position 30,C6c1ccC(=O)NNC(=O)c2ccc2c3ccc+c23)c(C)(o1
remove 6 from position 1,Cc1ccC(=O)NNC(=O)c2ccc2c3ccc+c23)c(C)(o1
remove 2 from position 22,Cc1ccC(=O)NNC(=O)c2cccc3ccc+c23)c(C)(o1
add ( at position 5,Cc1cc(C(=O)NNC(=O)c2cccc3ccc+c23)c(C)(o1
remove ( from position 37,Cc1cc(C(=O)NNC(=O)c2cccc3ccc+c23)c(C)o1
replace + at position 28 with c,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C)o1
final: Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C)o1,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C)o1
