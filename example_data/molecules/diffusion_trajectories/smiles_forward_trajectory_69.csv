log,state
initialize: CCOc1cccc(NC(=O)CCc2ccc(N)cc2)c1,CCOc1cccc(NC(=O)CCc2ccc(N)cc2)c1
replace 2 at position 28 with +,CCOc1cccc(NC(=O)CCc2ccc(N)cc+)c1
add O at position 2,CCOOc1cccc(NC(=O)CCc2ccc(N)cc+)c1
remove c from position 6,CCOOc1ccc(NC(=O)CCc2ccc(N)cc+)c1
replace ) at position 15 with #,CCOOc1ccc(NC(=O#CCc2ccc(N)cc+)c1
add 6 at position 1,C6COOc1ccc(NC(=O#CCc2ccc(N)cc+)c1
remove ) from position 30,C6COOc1ccc(NC(=O#CCc2ccc(N)cc+c1
remove c from position 9,C6COOc1cc(NC(=O#CCc2ccc(N)cc+c1
add n at position 7,C6COOc1ncc(NC(=O#CCc2ccc(N)cc+c1
add o at position 31,C6COOc1ncc(NC(=O#CCc2ccc(N)cc+co1
replace O at position 4 with S,C6COSc1ncc(NC(=O#CCc2ccc(N)cc+co1
remove + from position 29,C6COSc1ncc(NC(=O#CCc2ccc(N)ccco1
add C at position 5,C6COSCc1ncc(NC(=O#CCc2ccc(N)ccco1
remove # from position 17,C6COSCc1ncc(NC(=OCCc2ccc(N)ccco1
replace S at position 4 with N,C6CONCc1ncc(NC(=OCCc2ccc(N)ccco1
remove c from position 22,C6CONCc1ncc(NC(=OCCc2cc(N)ccco1
remove 2 from position 20,C6CONCc1ncc(NC(=OCCccc(N)ccco1
replace ( at position 14 with r,C6CONCc1ncc(NCr=OCCccc(N)ccco1
remove C from position 0,6CONCc1ncc(NCr=OCCccc(N)ccco1
replace O at position 2 with #,6C#NCc1ncc(NCr=OCCccc(N)ccco1
remove N from position 22,6C#NCc1ncc(NCr=OCCccc()ccco1
remove C from position 12,6C#NCc1ncc(Nr=OCCccc()ccco1
add H at position 2,6CH#NCc1ncc(Nr=OCCccc()ccco1
remove H from position 2,6C#NCc1ncc(Nr=OCCccc()ccco1
remove C from position 4,6C#Nc1ncc(Nr=OCCccc()ccco1
add ] at position 11,6C#Nc1ncc(N]r=OCCccc()ccco1
add - at position 16,6C#Nc1ncc(N]r=OC-Cccc()ccco1
remove O from position 14,6C#Nc1ncc(N]r=C-Cccc()ccco1
add O at position 8,6C#Nc1ncOc(N]r=C-Cccc()ccco1
replace 1 at position 5 with 4,6C#Nc4ncOc(N]r=C-Cccc()ccco1
add - at position 27,6C#Nc4ncOc(N]r=C-Cccc()ccco-1
replace = at position 14 with (,6C#Nc4ncOc(N]r(C-Cccc()ccco-1
remove c from position 24,6C#Nc4ncOc(N]r(C-Cccc()cco-1
remove ( from position 10,6C#Nc4ncOcN]r(C-Cccc()cco-1
remove C from position 14,6C#Nc4ncOcN]r(-Cccc()cco-1
remove 1 from position 25,6C#Nc4ncOcN]r(-Cccc()cco-
replace ( at position 19 with r,6C#Nc4ncOcN]r(-Ccccr)cco-
add ] at position 23,6C#Nc4ncOcN]r(-Ccccr)cc]o-
remove - from position 14,6C#Nc4ncOcN]r(Ccccr)cc]o-
remove N from position 3,6C#c4ncOcN]r(Ccccr)cc]o-
replace C at position 13 with =,6C#c4ncOcN]r(=cccr)cc]o-
replace c at position 16 with +,6C#c4ncOcN]r(=cc+r)cc]o-
replace c at position 15 with 7,6C#c4ncOcN]r(=c7+r)cc]o-
replace ] at position 21 with 3,6C#c4ncOcN]r(=c7+r)cc3o-
remove r from position 11,6C#c4ncOcN](=c7+r)cc3o-
add c at position 22,6C#c4ncOcN](=c7+r)cc3oc-
replace c at position 18 with s,6C#c4ncOcN](=c7+r)sc3oc-
replace 4 at position 4 with o,6C#concOcN](=c7+r)sc3oc-
replace - at position 23 with 6,6C#concOcN](=c7+r)sc3oc6
replace 7 at position 14 with ],6C#concOcN](=c]+r)sc3oc6
add 1 at position 12,6C#concOcN](1=c]+r)sc3oc6
add c at position 9,6C#concOccN](1=c]+r)sc3oc6
add l at position 16,6C#concOccN](1=cl]+r)sc3oc6
add 5 at position 22,6C#concOccN](1=cl]+r)s5c3oc6
remove = from position 14,6C#concOccN](1cl]+r)s5c3oc6
add n at position 8,6C#concOnccN](1cl]+r)s5c3oc6
add s at position 13,6C#concOnccN]s(1cl]+r)s5c3oc6
add 1 at position 12,6C#concOnccN1]s(1cl]+r)s5c3oc6
remove c from position 28,6C#concOnccN1]s(1cl]+r)s5c3o6
remove O from position 7,6C#concnccN1]s(1cl]+r)s5c3o6
replace 1 at position 11 with s,6C#concnccNs]s(1cl]+r)s5c3o6
replace c at position 16 with r,6C#concnccNs]s(1rl]+r)s5c3o6
remove n from position 5,6C#cocnccNs]s(1rl]+r)s5c3o6
replace n at position 6 with 4,6C#coc4ccNs]s(1rl]+r)s5c3o6
add F at position 11,6C#coc4ccNsF]s(1rl]+r)s5c3o6
add s at position 24,6C#coc4ccNsF]s(1rl]+r)s5sc3o6
remove s from position 10,6C#coc4ccNF]s(1rl]+r)s5sc3o6
add I at position 1,6IC#coc4ccNF]s(1rl]+r)s5sc3o6
add l at position 4,6IC#lcoc4ccNF]s(1rl]+r)s5sc3o6
remove ) from position 22,6IC#lcoc4ccNF]s(1rl]+rs5sc3o6
remove 6 from position 0,IC#lcoc4ccNF]s(1rl]+rs5sc3o6
remove C from position 1,I#lcoc4ccNF]s(1rl]+rs5sc3o6
remove 3 from position 24,I#lcoc4ccNF]s(1rl]+rs5sco6
replace 4 at position 6 with (,I#lcoc(ccNF]s(1rl]+rs5sco6
remove c from position 23,I#lcoc(ccNF]s(1rl]+rs5so6
remove 5 from position 21,I#lcoc(ccNF]s(1rl]+rsso6
remove c from position 8,I#lcoc(cNF]s(1rl]+rsso6
remove F from position 9,I#lcoc(cN]s(1rl]+rsso6
remove 6 from position 21,I#lcoc(cN]s(1rl]+rsso
remove o from position 4,I#lcc(cN]s(1rl]+rsso
replace l at position 2 with ],I#]cc(cN]s(1rl]+rsso
remove l from position 13,I#]cc(cN]s(1r]+rsso
remove o from position 18,I#]cc(cN]s(1r]+rss
remove c from position 3,I#]c(cN]s(1r]+rss
remove s from position 8,I#]c(cN](1r]+rss
add ( at position 12,I#]c(cN](1r](+rss
remove r from position 14,I#]c(cN](1r](+ss
add / at position 15,I#]c(cN](1r](+s/s
remove I from position 0,#]c(cN](1r](+s/s
add 4 at position 12,#]c(cN](1r](4+s/s
remove / from position 15,#]c(cN](1r](4+ss
add / at position 15,#]c(cN](1r](4+s/s
remove ( from position 11,#]c(cN](1r]4+s/s
remove 4 from position 11,#]c(cN](1r]+s/s
add c at position 14,#]c(cN](1r]+s/cs
remove # from position 0,]c(cN](1r]+s/cs
replace s at position 11 with @,]c(cN](1r]+@/cs
remove c from position 1,](cN](1r]+@/cs
add B at position 12,](cN](1r]+@/Bcs
add o at position 4,](cNo](1r]+@/Bcs
replace + at position 10 with 7,](cNo](1r]7@/Bcs
replace ] at position 9 with 4,](cNo](1r47@/Bcs
remove 7 from position 10,](cNo](1r4@/Bcs
remove o from position 4,](cN](1r4@/Bcs
remove 4 from position 8,](cN](1r@/Bcs
replace ] at position 4 with ),](cN)(1r@/Bcs
remove / from position 9,](cN)(1r@Bcs
add 5 at position 8,](cN)(1r5@Bcs
replace r at position 7 with 3,](cN)(135@Bcs
add H at position 8,](cN)(13H5@Bcs
remove ( from position 1,]cN)(13H5@Bcs
replace ( at position 4 with 7,]cN)713H5@Bcs
add 7 at position 0,7]cN)713H5@Bcs
add 1 at position 0,17]cN)713H5@Bcs
remove ) from position 5,17]cN713H5@Bcs
remove 1 from position 6,17]cN73H5@Bcs
replace 7 at position 1 with o,1o]cN73H5@Bcs
replace N at position 4 with 3,1o]c373H5@Bcs
remove @ from position 9,1o]c373H5Bcs
add ) at position 8,1o]c373H)5Bcs
remove s from position 12,1o]c373H)5Bc
add 5 at position 9,1o]c373H)55Bc
replace c at position 3 with /,1o]/373H)55Bc
remove 5 from position 10,1o]/373H)5Bc
replace ] at position 2 with r,1or/373H)5Bc
remove 5 from position 9,1or/373H)Bc
add s at position 8,1or/373Hs)Bc
replace c at position 11 with /,1or/373Hs)B/
replace s at position 8 with S,1or/373HS)B/
replace 7 at position 5 with -,1or/3-3HS)B/
remove S from position 8,1or/3-3H)B/
remove B from position 9,1or/3-3H)/
remove H from position 7,1or/3-3)/
add r at position 1,1ror/3-3)/
remove - from position 6,1ror/33)/
replace r at position 1 with 5,15or/33)/
add 6 at position 3,15o6r/33)/
replace ) at position 8 with [,15o6r/33[/
replace / at position 9 with C,15o6r/33[C
replace 1 at position 0 with B,B5o6r/33[C
replace r at position 4 with =,B5o6=/33[C
replace o at position 2 with B,B5B6=/33[C
remove 6 from position 3,B5B=/33[C
remove B from position 0,5B=/33[C
replace 3 at position 5 with I,5B=/3I[C
remove = from position 2,5B/3I[C
add 1 at position 0,15B/3I[C
replace B at position 2 with F,15F/3I[C
replace 3 at position 4 with O,15F/OI[C
remove C from position 7,15F/OI[
remove I from position 5,15F/O[
remove / from position 3,15FO[
remove F from position 2,15O[
add o at position 0,o15O[
remove 5 from position 2,o1O[
add ( at position 4,o1O[(
replace ( at position 4 with I,o1O[I
remove I from position 4,o1O[
add 6 at position 2,o16O[
replace 6 at position 2 with [,o1[O[
remove 1 from position 1,o[O[
remove [ from position 1,oO[
replace [ at position 2 with S,oOS
remove o from position 0,OS
remove O from position 0,S
remove S from position 0,
final: ,
