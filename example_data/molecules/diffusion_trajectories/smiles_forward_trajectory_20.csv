log,state
initialize: CC[C@@H]1CCCCN1C(=O)NC1CCN(C(=O)OC(C)(C)C)CC1,CC[C@@H]1CCCCN1C(=O)NC1CCN(C(=O)OC(C)(C)C)CC1
replace ( at position 28 with -,CC[C@@H]1CCCCN1C(=O)NC1CCN(C-=O)OC(C)(C)C)CC1
add ( at position 37,CC[C@@H]1CCCCN1C(=O)NC1CCN(C-=O)OC(C)((C)C)CC1
remove @ from position 5,CC[C@H]1CCCCN1C(=O)NC1CCN(C-=O)OC(C)((C)C)CC1
add 2 at position 22,CC[C@H]1CCCCN1C(=O)NC12CCN(C-=O)OC(C)((C)C)CC1
add 6 at position 1,C6C[C@H]1CCCCN1C(=O)NC12CCN(C-=O)OC(C)((C)C)CC1
remove = from position 30,C6C[C@H]1CCCCN1C(=O)NC12CCN(C-O)OC(C)((C)C)CC1
remove C from position 9,C6C[C@H]1CCCN1C(=O)NC12CCN(C-O)OC(C)((C)C)CC1
add + at position 45,C6C[C@H]1CCCN1C(=O)NC12CCN(C-O)OC(C)((C)C)CC1+
replace C at position 32 with B,C6C[C@H]1CCCN1C(=O)NC12CCN(C-O)OB(C)((C)C)CC1+
replace ( at position 15 with +,C6C[C@H]1CCCN1C+=O)NC12CCN(C-O)OB(C)((C)C)CC1+
remove O from position 29,C6C[C@H]1CCCN1C+=O)NC12CCN(C-)OB(C)((C)C)CC1+
add ) at position 43,C6C[C@H]1CCCN1C+=O)NC12CCN(C-)OB(C)((C)C)CC)1+
remove C from position 2,C6[C@H]1CCCN1C+=O)NC12CCN(C-)OB(C)((C)C)CC)1+
remove ( from position 35,C6[C@H]1CCCN1C+=O)NC12CCN(C-)OB(C)(C)C)CC)1+
add B at position 19,C6[C@H]1CCCN1C+=O)NBC12CCN(C-)OB(C)(C)C)CC)1+
replace ) at position 17 with 3,C6[C@H]1CCCN1C+=O3NBC12CCN(C-)OB(C)(C)C)CC)1+
remove 1 from position 12,C6[C@H]1CCCNC+=O3NBC12CCN(C-)OB(C)(C)C)CC)1+
remove ) from position 33,C6[C@H]1CCCNC+=O3NBC12CCN(C-)OB(C(C)C)CC)1+
remove C from position 32,C6[C@H]1CCCNC+=O3NBC12CCN(C-)OB((C)C)CC)1+
add - at position 21,C6[C@H]1CCCNC+=O3NBC1-2CCN(C-)OB((C)C)CC)1+
remove + from position 42,C6[C@H]1CCCNC+=O3NBC1-2CCN(C-)OB((C)C)CC)1
remove C from position 3,C6[@H]1CCCNC+=O3NBC1-2CCN(C-)OB((C)C)CC)1
add / at position 31,C6[@H]1CCCNC+=O3NBC1-2CCN(C-)OB/((C)C)CC)1
add - at position 13,C6[@H]1CCCNC+-=O3NBC1-2CCN(C-)OB/((C)C)CC)1
add ] at position 22,C6[@H]1CCCNC+-=O3NBC1-]2CCN(C-)OB/((C)C)CC)1
add - at position 33,C6[@H]1CCCNC+-=O3NBC1-]2CCN(C-)OB-/((C)C)CC)1
remove - from position 29,C6[@H]1CCCNC+-=O3NBC1-]2CCN(C)OB-/((C)C)CC)1
add O at position 17,C6[@H]1CCCNC+-=O3ONBC1-]2CCN(C)OB-/((C)C)CC)1
replace C at position 11 with 3,C6[@H]1CCCN3+-=O3ONBC1-]2CCN(C)OB-/((C)C)CC)1
add ] at position 9,C6[@H]1CC]CN3+-=O3ONBC1-]2CCN(C)OB-/((C)C)CC)1
replace [ at position 2 with H,C6H@H]1CC]CN3+-=O3ONBC1-]2CCN(C)OB-/((C)C)CC)1
remove 1 from position 22,C6H@H]1CC]CN3+-=O3ONBC-]2CCN(C)OB-/((C)C)CC)1
remove C from position 37,C6H@H]1CC]CN3+-=O3ONBC-]2CCN(C)OB-/(()C)CC)1
replace ) at position 30 with (,C6H@H]1CC]CN3+-=O3ONBC-]2CCN(C(OB-/(()C)CC)1
replace ( at position 30 with o,C6H@H]1CC]CN3+-=O3ONBC-]2CCN(CoOB-/(()C)CC)1
replace C at position 10 with @,C6H@H]1CC]@N3+-=O3ONBC-]2CCN(CoOB-/(()C)CC)1
remove - from position 33,C6H@H]1CC]@N3+-=O3ONBC-]2CCN(CoOB/(()C)CC)1
replace O at position 18 with 3,C6H@H]1CC]@N3+-=O33NBC-]2CCN(CoOB/(()C)CC)1
add = at position 11,C6H@H]1CC]@=N3+-=O33NBC-]2CCN(CoOB/(()C)CC)1
remove C from position 7,C6H@H]1C]@=N3+-=O33NBC-]2CCN(CoOB/(()C)CC)1
replace 1 at position 6 with n,C6H@H]nC]@=N3+-=O33NBC-]2CCN(CoOB/(()C)CC)1
replace 2 at position 24 with n,C6H@H]nC]@=N3+-=O33NBC-]nCCN(CoOB/(()C)CC)1
replace O at position 16 with N,C6H@H]nC]@=N3+-=N33NBC-]nCCN(CoOB/(()C)CC)1
add S at position 6,C6H@H]SnC]@=N3+-=N33NBC-]nCCN(CoOB/(()C)CC)1
remove @ from position 10,C6H@H]SnC]=N3+-=N33NBC-]nCCN(CoOB/(()C)CC)1
remove 3 from position 17,C6H@H]SnC]=N3+-=N3NBC-]nCCN(CoOB/(()C)CC)1
replace C at position 20 with l,C6H@H]SnC]=N3+-=N3NBl-]nCCN(CoOB/(()C)CC)1
replace H at position 4 with ),C6H@)]SnC]=N3+-=N3NBl-]nCCN(CoOB/(()C)CC)1
remove @ from position 3,C6H)]SnC]=N3+-=N3NBl-]nCCN(CoOB/(()C)CC)1
replace H at position 2 with 2,C62)]SnC]=N3+-=N3NBl-]nCCN(CoOB/(()C)CC)1
remove S from position 5,C62)]nC]=N3+-=N3NBl-]nCCN(CoOB/(()C)CC)1
replace ] at position 20 with 4,C62)]nC]=N3+-=N3NBl-4nCCN(CoOB/(()C)CC)1
replace ] at position 4 with H,C62)HnC]=N3+-=N3NBl-4nCCN(CoOB/(()C)CC)1
remove H from position 4,C62)nC]=N3+-=N3NBl-4nCCN(CoOB/(()C)CC)1
remove = from position 12,C62)nC]=N3+-N3NBl-4nCCN(CoOB/(()C)CC)1
add S at position 12,C62)nC]=N3+-SN3NBl-4nCCN(CoOB/(()C)CC)1
replace / at position 29 with F,C62)nC]=N3+-SN3NBl-4nCCN(CoOBF(()C)CC)1
add r at position 23,C62)nC]=N3+-SN3NBl-4nCCrN(CoOBF(()C)CC)1
replace ( at position 32 with r,C62)nC]=N3+-SN3NBl-4nCCrN(CoOBF(r)C)CC)1
remove - from position 11,C62)nC]=N3+SN3NBl-4nCCrN(CoOBF(r)C)CC)1
replace 3 at position 13 with 5,C62)nC]=N3+SN5NBl-4nCCrN(CoOBF(r)C)CC)1
add F at position 22,C62)nC]=N3+SN5NBl-4nCCFrN(CoOBF(r)C)CC)1
add B at position 35,C62)nC]=N3+SN5NBl-4nCCFrN(CoOBF(r)CB)CC)1
remove N from position 8,C62)nC]=3+SN5NBl-4nCCFrN(CoOBF(r)CB)CC)1
add r at position 10,C62)nC]=3+rSN5NBl-4nCCFrN(CoOBF(r)CB)CC)1
add ) at position 34,C62)nC]=3+rSN5NBl-4nCCFrN(CoOBF(r))CB)CC)1
remove r from position 10,C62)nC]=3+SN5NBl-4nCCFrN(CoOBF(r))CB)CC)1
add N at position 3,C62N)nC]=3+SN5NBl-4nCCFrN(CoOBF(r))CB)CC)1
add r at position 9,C62N)nC]=r3+SN5NBl-4nCCFrN(CoOBF(r))CB)CC)1
add 7 at position 27,C62N)nC]=r3+SN5NBl-4nCCFrN(7CoOBF(r))CB)CC)1
remove N from position 13,C62N)nC]=r3+S5NBl-4nCCFrN(7CoOBF(r))CB)CC)1
replace N at position 24 with s,C62N)nC]=r3+S5NBl-4nCCFrs(7CoOBF(r))CB)CC)1
replace - at position 17 with H,C62N)nC]=r3+S5NBlH4nCCFrs(7CoOBF(r))CB)CC)1
remove n from position 19,C62N)nC]=r3+S5NBlH4CCFrs(7CoOBF(r))CB)CC)1
remove B from position 36,C62N)nC]=r3+S5NBlH4CCFrs(7CoOBF(r))C)CC)1
replace ) at position 34 with +,C62N)nC]=r3+S5NBlH4CCFrs(7CoOBF(r)+C)CC)1
replace H at position 17 with =,C62N)nC]=r3+S5NBl=4CCFrs(7CoOBF(r)+C)CC)1
remove ) from position 36,C62N)nC]=r3+S5NBl=4CCFrs(7CoOBF(r)+CCC)1
remove ] from position 7,C62N)nC=r3+S5NBl=4CCFrs(7CoOBF(r)+CCC)1
remove 4 from position 17,C62N)nC=r3+S5NBl=CCFrs(7CoOBF(r)+CCC)1
add 7 at position 37,C62N)nC=r3+S5NBl=CCFrs(7CoOBF(r)+CCC)71
add 2 at position 28,C62N)nC=r3+S5NBl=CCFrs(7CoOB2F(r)+CCC)71
replace + at position 10 with =,C62N)nC=r3=S5NBl=CCFrs(7CoOB2F(r)+CCC)71
remove ) from position 4,C62NnC=r3=S5NBl=CCFrs(7CoOB2F(r)+CCC)71
remove r from position 19,C62NnC=r3=S5NBl=CCFs(7CoOB2F(r)+CCC)71
remove ) from position 30,C62NnC=r3=S5NBl=CCFs(7CoOB2F(r+CCC)71
add / at position 30,C62NnC=r3=S5NBl=CCFs(7CoOB2F(r/+CCC)71
remove o from position 23,C62NnC=r3=S5NBl=CCFs(7COB2F(r/+CCC)71
remove C from position 22,C62NnC=r3=S5NBl=CCFs(7OB2F(r/+CCC)71
add r at position 33,C62NnC=r3=S5NBl=CCFs(7OB2F(r/+CCCr)71
remove C from position 30,C62NnC=r3=S5NBl=CCFs(7OB2F(r/+CCr)71
remove + from position 29,C62NnC=r3=S5NBl=CCFs(7OB2F(r/CCr)71
remove = from position 6,C62NnCr3=S5NBl=CCFs(7OB2F(r/CCr)71
add ) at position 30,C62NnCr3=S5NBl=CCFs(7OB2F(r/CC)r)71
replace O at position 21 with 6,C62NnCr3=S5NBl=CCFs(76B2F(r/CC)r)71
replace s at position 18 with 4,C62NnCr3=S5NBl=CCF4(76B2F(r/CC)r)71
remove 6 from position 21,C62NnCr3=S5NBl=CCF4(7B2F(r/CC)r)71
remove 4 from position 18,C62NnCr3=S5NBl=CCF(7B2F(r/CC)r)71
remove 1 from position 32,C62NnCr3=S5NBl=CCF(7B2F(r/CC)r)7
replace F at position 17 with ),C62NnCr3=S5NBl=CC)(7B2F(r/CC)r)7
remove l from position 13,C62NnCr3=S5NB=CC)(7B2F(r/CC)r)7
remove 5 from position 10,C62NnCr3=SNB=CC)(7B2F(r/CC)r)7
replace C at position 14 with 3,C62NnCr3=SNB=C3)(7B2F(r/CC)r)7
add H at position 16,C62NnCr3=SNB=C3)H(7B2F(r/CC)r)7
remove N from position 3,C62nCr3=SNB=C3)H(7B2F(r/CC)r)7
replace S at position 8 with 6,C62nCr3=6NB=C3)H(7B2F(r/CC)r)7
add 7 at position 0,7C62nCr3=6NB=C3)H(7B2F(r/CC)r)7
add 1 at position 2,7C162nCr3=6NB=C3)H(7B2F(r/CC)r)7
remove B from position 20,7C162nCr3=6NB=C3)H(72F(r/CC)r)7
remove = from position 13,7C162nCr3=6NBC3)H(72F(r/CC)r)7
replace 7 at position 29 with ),7C162nCr3=6NBC3)H(72F(r/CC)r))
replace = at position 9 with 3,7C162nCr336NBC3)H(72F(r/CC)r))
remove ) from position 29,7C162nCr336NBC3)H(72F(r/CC)r)
remove N from position 11,7C162nCr336BC3)H(72F(r/CC)r)
remove ( from position 20,7C162nCr336BC3)H(72Fr/CC)r)
replace 7 at position 0 with S,SC162nCr336BC3)H(72Fr/CC)r)
remove 2 from position 18,SC162nCr336BC3)H(7Fr/CC)r)
add B at position 15,SC162nCr336BC3)BH(7Fr/CC)r)
remove / from position 21,SC162nCr336BC3)BH(7FrCC)r)
replace 2 at position 4 with r,SC16rnCr336BC3)BH(7FrCC)r)
remove F from position 19,SC16rnCr336BC3)BH(7rCC)r)
add s at position 17,SC16rnCr336BC3)BHs(7rCC)r)
replace C at position 22 with /,SC16rnCr336BC3)BHs(7rC/)r)
replace r at position 24 with C,SC16rnCr336BC3)BHs(7rC/)C)
remove C from position 12,SC16rnCr336B3)BHs(7rC/)C)
replace 6 at position 10 with -,SC16rnCr33-B3)BHs(7rC/)C)
remove ( from position 17,SC16rnCr33-B3)BHs7rC/)C)
remove C from position 19,SC16rnCr33-B3)BHs7r/)C)
remove B from position 14,SC16rnCr33-B3)Hs7r/)C)
add r at position 2,SCr16rnCr33-B3)Hs7r/)C)
remove C from position 21,SCr16rnCr33-B3)Hs7r/))
remove C from position 1,Sr16rnCr33-B3)Hs7r/))
remove 1 from position 2,Sr6rnCr33-B3)Hs7r/))
remove ) from position 19,Sr6rnCr33-B3)Hs7r/)
replace 3 at position 11 with F,Sr6rnCr33-BF)Hs7r/)
remove ) from position 12,Sr6rnCr33-BFHs7r/)
remove r from position 1,S6rnCr33-BFHs7r/)
add l at position 2,S6lrnCr33-BFHs7r/)
remove B from position 10,S6lrnCr33-FHs7r/)
add S at position 7,S6lrnCrS33-FHs7r/)
replace S at position 0 with o,o6lrnCrS33-FHs7r/)
remove F from position 11,o6lrnCrS33-Hs7r/)
remove l from position 2,o6rnCrS33-Hs7r/)
remove S from position 6,o6rnCr33-Hs7r/)
replace / at position 13 with N,o6rnCr33-Hs7rN)
remove 7 from position 11,o6rnCr33-HsrN)
add O at position 2,o6OrnCr33-HsrN)
replace H at position 10 with ],o6OrnCr33-]srN)
add 6 at position 14,o6OrnCr33-]srN6)
remove s from position 11,o6OrnCr33-]rN6)
remove r from position 6,o6OrnC33-]rN6)
replace r at position 3 with 4,o6O4nC33-]rN6)
add H at position 5,o6O4nHC33-]rN6)
replace 6 at position 1 with H,oHO4nHC33-]rN6)
remove 6 from position 13,oHO4nHC33-]rN)
add H at position 9,oHO4nHC33H-]rN)
remove O from position 2,oH4nHC33H-]rN)
remove C from position 5,oH4nH33H-]rN)
replace H at position 4 with [,oH4n[33H-]rN)
remove 4 from position 2,oHn[33H-]rN)
remove n from position 2,oH[33H-]rN)
replace r at position 8 with S,oH[33H-]SN)
remove 3 from position 3,oH[3H-]SN)
remove [ from position 2,oH3H-]SN)
remove S from position 6,oH3H-]N)
remove ) from position 7,oH3H-]N
replace - at position 4 with B,oH3HB]N
remove N from position 6,oH3HB]
remove B from position 4,oH3H]
add C at position 5,oH3H]C
replace ] at position 4 with -,oH3H-C
add B at position 3,oH3BH-C
add c at position 2,oHc3BH-C
replace 3 at position 3 with [,oHc[BH-C
add s at position 6,oHc[BHs-C
remove C from position 8,oHc[BHs-
remove [ from position 3,oHcBHs-
remove s from position 5,oHcBH-
replace B at position 3 with 5,oHc5H-
replace H at position 4 with +,oHc5+-
remove 5 from position 3,oHc+-
add / at position 1,o/Hc+-
remove H from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
