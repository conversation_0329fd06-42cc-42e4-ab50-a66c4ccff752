log,state
initialize: ,
add S at position 0,S
add O at position 0,OS
add o at position 0,o<PERSON>
replace S at position 2 with [,oO[
add [ at position 1,o[O[
add 1 at position 1,o1[O[
replace [ at position 2 with 6,o16O[
remove 6 from position 2,o1O[
add I at position 4,o1O[I
replace I at position 4 with (,o1O[(
remove ( from position 4,o1O[
add 5 at position 2,o15O[
remove o from position 0,15O[
add F at position 2,15FO[
add / at position 3,15F/O[
add I at position 5,15F/OI[
add C at position 7,15F/OI[C
replace O at position 4 with 3,15F/3I[C
replace F at position 2 with B,15B/3I[C
remove 1 from position 0,5B/3I[C
add = at position 2,5B=/3I[C
replace I at position 5 with 3,5B=/33[C
add B at position 0,B5B=/33[C
add 6 at position 3,B5B6=/33[C
replace B at position 2 with o,B5o6=/33[C
replace = at position 4 with r,B5o6r/33[C
replace B at position 0 with 1,15o6r/33[C
replace C at position 9 with /,15o6r/33[/
replace [ at position 8 with ),15o6r/33)/
remove 6 from position 3,15or/33)/
replace 5 at position 1 with r,1ror/33)/
add - at position 6,1ror/3-3)/
remove r from position 1,1or/3-3)/
add H at position 7,1or/3-3H)/
add B at position 9,1or/3-3H)B/
add S at position 8,1or/3-3HS)B/
replace - at position 5 with 6,1or/363HS)B/
replace S at position 8 with s,1or/363Hs)B/
replace / at position 11 with c,1or/363Hs)Bc
remove s from position 8,1or/363H)Bc
add 5 at position 9,1or/363H)5Bc
replace r at position 2 with 3,1o3/363H)5Bc
add 5 at position 10,1o3/363H)55Bc
replace / at position 3 with 4,1o34363H)55Bc
remove 5 from position 9,1o34363H)5Bc
add + at position 12,1o34363H)5Bc+
remove ) from position 8,1o34363H5Bc+
add @ at position 9,1o34363H5@Bc+
replace 3 at position 4 with n,1o34n63H5@Bc+
replace o at position 1 with 7,1734n63H5@Bc+
add s at position 6,1734n6s3H5@Bc+
add ) at position 5,1734n)6s3H5@Bc+
remove 1 from position 0,734n)6s3H5@Bc+
remove 7 from position 0,34n)6s3H5@Bc+
replace 6 at position 4 with F,34n)Fs3H5@Bc+
add 4 at position 1,344n)Fs3H5@Bc+
remove H from position 8,344n)Fs35@Bc+
replace 3 at position 7 with o,344n)Fso5@Bc+
remove 5 from position 8,344n)Fso@Bc+
add / at position 9,344n)Fso@/Bc+
replace ) at position 4 with =,344n=Fso@/Bc+
add 5 at position 8,344n=Fso5@/Bc+
add o at position 4,344no=Fso5@/Bc+
add 7 at position 10,344no=Fso57@/Bc+
replace 5 at position 9 with +,344no=Fso+7@/Bc+
replace 7 at position 10 with ),344no=Fso+)@/Bc+
remove o from position 4,344n=Fso+)@/Bc+
remove B from position 12,344n=Fso+)@/c+
add r at position 1,3r44n=Fso+)@/c+
replace @ at position 11 with r,3r44n=Fso+)r/c+
add N at position 0,N3r44n=Fso+)r/c+
remove c from position 14,N3r44n=Fso+)r/+
add 4 at position 11,N3r44n=Fso+4)r/+
add ( at position 11,N3r44n=Fso+(4)r/+
remove / from position 15,N3r44n=Fso+(4)r+
add / at position 15,N3r44n=Fso+(4)r/+
remove 4 from position 12,N3r44n=Fso+()r/+
add 6 at position 0,6N3r44n=Fso+()r/+
remove / from position 15,6N3r44n=Fso+()r+
add F at position 14,6N3r44n=Fso+()Fr+
remove ( from position 12,6N3r44n=Fso+)Fr+
add r at position 8,6N3r44n=rFso+)Fr+
add B at position 3,6N3Br44n=rFso+)Fr+
add C at position 18,6N3Br44n=rFso+)Fr+C
replace = at position 8 with H,6N3Br44nHrFso+)Fr+C
replace + at position 17 with ),6N3Br44nHrFso+)Fr)C
add B at position 18,6N3Br44nHrFso+)Fr)BC
add ( at position 9,6N3Br44nH(rFso+)Fr)BC
replace H at position 8 with 4,6N3Br44n4(rFso+)Fr)BC
replace s at position 12 with 7,6N3Br44n4(rF7o+)Fr)BC
add S at position 6,6N3Br4S4n4(rF7o+)Fr)BC
remove 7 from position 13,6N3Br4S4n4(rFo+)Fr)BC
remove r from position 4,6N3B4S4n4(rFo+)Fr)BC
remove N from position 1,63B4S4n4(rFo+)Fr)BC
add r at position 5,63B4Sr4n4(rFo+)Fr)BC
remove ) from position 17,63B4Sr4n4(rFo+)FrBC
remove r from position 5,63B4S4n4(rFo+)FrBC
add 1 at position 4,63B41S4n4(rFo+)FrBC
remove B from position 17,63B41S4n4(rFo+)FrC
remove F from position 11,63B41S4n4(ro+)FrC
replace 4 at position 6 with l,63B41Sln4(ro+)FrC
add 3 at position 5,63B413Sln4(ro+)FrC
replace r at position 16 with ),63B413Sln4(ro+)F)C
remove r from position 11,63B413Sln4(o+)F)C
replace F at position 14 with C,63B413Sln4(o+)C)C
remove S from position 6,63B413ln4(o+)C)C
add 2 at position 6,63B4132ln4(o+)C)C
add H at position 2,63HB4132ln4(o+)C)C
replace H at position 2 with n,63nB4132ln4(o+)C)C
replace 4 at position 10 with r,63nB4132lnr(o+)C)C
add S at position 2,63SnB4132lnr(o+)C)C
replace 3 at position 1 with +,6+SnB4132lnr(o+)C)C
add H at position 1,6H+SnB4132lnr(o+)C)C
replace + at position 2 with #,6H#SnB4132lnr(o+)C)C
replace l at position 10 with -,6H#SnB4132-nr(o+)C)C
add N at position 8,6H#SnB41N32-nr(o+)C)C
add = at position 5,6H#Sn=B41N32-nr(o+)C)C
remove S from position 3,6H#n=B41N32-nr(o+)C)C
replace N at position 8 with n,6H#n=B41n32-nr(o+)C)C
replace n at position 12 with ],6H#n=B41n32-]r(o+)C)C
replace n at position 3 with ],6H#]=B41n32-]r(o+)C)C
add n at position 3,6H#n]=B41n32-]r(o+)C)C
remove = from position 5,6H#n]B41n32-]r(o+)C)C
replace 3 at position 9 with O,6H#n]B41nO2-]r(o+)C)C
add - at position 16,6H#n]B41nO2-]r(o-+)C)C
replace B at position 5 with 1,6H#n]141nO2-]r(o-+)C)C
replace o at position 15 with (,6H#n]141nO2-]r((-+)C)C
replace ( at position 15 with C,6H#n]141nO2-]r(C-+)C)C
add ( at position 18,6H#n]141nO2-]r(C-+()C)C
add c at position 11,6H#n]141nO2c-]r(C-+()C)C
replace H at position 1 with N,6N#n]141nO2c-]r(C-+()C)C
remove ] from position 4,6N#n141nO2c-]r(C-+()C)C
replace 4 at position 5 with ),6N#n1)1nO2c-]r(C-+()C)C
remove O from position 8,6N#n1)1n2c-]r(C-+()C)C
add N at position 14,6N#n1)1n2c-]r(NC-+()C)C
remove - from position 16,6N#n1)1n2c-]r(NC+()C)C
remove ] from position 11,6N#n1)1n2c-r(NC+()C)C
add c at position 4,6N#nc1)1n2c-r(NC+()C)C
add H at position 2,6NH#nc1)1n2c-r(NC+()C)C
remove H from position 2,6N#nc1)1n2c-r(NC+()C)C
add C at position 12,6N#nc1)1n2c-Cr(NC+()C)C
add c at position 22,6N#nc1)1n2c-Cr(NC+()C)cC
replace # at position 2 with n,6Nnnc1)1n2c-Cr(NC+()C)cC
add C at position 0,C6Nnnc1)1n2c-Cr(NC+()C)cC
replace r at position 14 with =,C6Nnnc1)1n2c-C=(NC+()C)cC
add C at position 20,C6Nnnc1)1n2c-C=(NC+(C)C)cC
add n at position 11,C6Nnnc1)1n2nc-C=(NC+(C)C)cC
replace N at position 2 with c,C6cnnc1)1n2nc-C=(NC+(C)C)cC
add n at position 8,C6cnnc1)n1n2nc-C=(NC+(C)C)cC
add C at position 16,C6cnnc1)n1n2nc-CC=(NC+(C)C)cC
add ) at position 19,C6cnnc1)n1n2nc-CC=()NC+(C)C)cC
add 1 at position 29,C6cnnc1)n1n2nc-CC=()NC+(C)C)c1C
replace ) at position 7 with C,C6cnnc1Cn1n2nc-CC=()NC+(C)C)c1C
replace C at position 16 with (,C6cnnc1Cn1n2nc-C(=()NC+(C)C)c1C
remove + from position 22,C6cnnc1Cn1n2nc-C(=()NC(C)C)c1C
add s at position 4,C6cnsnc1Cn1n2nc-C(=()NC(C)C)c1C
add s at position 30,C6cnsnc1Cn1n2nc-C(=()NC(C)C)c1sC
remove 6 from position 1,Ccnsnc1Cn1n2nc-C(=()NC(C)C)c1sC
remove 2 from position 11,Ccnsnc1Cn1nnc-C(=()NC(C)C)c1sC
add 1 at position 2,Cc1nsnc1Cn1nnc-C(=()NC(C)C)c1sC
replace ( at position 18 with O,Cc1nsnc1Cn1nnc-C(=O)NC(C)C)c1sC
remove s from position 29,Cc1nsnc1Cn1nnc-C(=O)NC(C)C)c1C
replace - at position 14 with (,Cc1nsnc1Cn1nnc(C(=O)NC(C)C)c1C
final: Cc1nsnc1Cn1nnc(C(=O)NC(C)C)c1C,Cc1nsnc1Cn1nnc(C(=O)NC(C)C)c1C
