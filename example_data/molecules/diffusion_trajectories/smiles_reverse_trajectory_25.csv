log,state
initialize: ,
add o at position 0,o
add l at position 1,ol
replace o at position 0 with <PERSON>,Sl
remove S from position 0,l
add I at position 1,lI
remove l from position 0,I
add 6 at position 0,6I
replace I at position 1 with 5,65
remove 6 from position 0,5
replace 5 at position 0 with r,r
add S at position 1,rS
remove r from position 0,S
add - at position 1,S-
replace - at position 1 with ],S]
add 4 at position 1,S4]
remove ] from position 2,S4
remove S from position 0,4
add r at position 0,r4
replace r at position 0 with /,/4
add ) at position 2,/4)
replace / at position 0 with 7,74)
add H at position 2,74H)
add o at position 0,o74H)
remove ) from position 4,o74H
add 5 at position 4,o74H5
replace 4 at position 2 with ),o7)H5
replace o at position 0 with 1,17)H5
add 7 at position 3,17)7H5
add n at position 2,17n)7H5
remove 1 from position 0,7n)7H5
remove 7 from position 0,n)7H5
replace 7 at position 2 with 3,n)3H5
add r at position 0,rn)3H5
remove H from position 4,rn)35
replace 3 at position 3 with 4,rn)45
remove 5 from position 4,rn)4
add B at position 4,rn)4B
replace ) at position 2 with 2,rn24B
add c at position 4,rn24cB
add o at position 2,rno24cB
add 6 at position 5,rno246cB
replace 4 at position 4 with o,rno2o6cB
replace 6 at position 5 with B,rno2oBcB
remove o from position 2,rn2oBcB
remove B from position 6,rn2oBc
add 7 at position 0,7rn2oBc
replace B at position 5 with 2,7rn2o2c
add 6 at position 0,67rn2o2c
remove c from position 7,67rn2o2
add 1 at position 5,67rn21o2
add c at position 5,67rn2c1o2
add = at position 2,67=rn2c1o2
replace = at position 2 with 3,673rn2c1o2
add ) at position 10,673rn2c1o2)
add 4 at position 4,673r4n2c1o2)
add N at position 1,6N73r4n2c1o2)
replace 7 at position 2 with I,6NI3r4n2c1o2)
replace 2 at position 7 with 3,6NI3r4n3c1o2)
add n at position 5,6NI3rn4n3c1o2)
add s at position 9,6NI3rn4n3sc1o2)
add n at position 9,6NI3rn4n3nsc1o2)
add F at position 12,6NI3rn4n3nscF1o2)
add = at position 8,6NI3rn4n=3nscF1o2)
replace = at position 8 with H,6NI3rn4nH3nscF1o2)
replace ) at position 17 with 5,6NI3rn4nH3nscF1o25
add + at position 18,6NI3rn4nH3nscF1o25+
add / at position 9,6NI3rn4nH/3nscF1o25+
replace H at position 8 with 4,6NI3rn4n4/3nscF1o25+
replace s at position 12 with 7,6NI3rn4n4/3n7cF1o25+
add l at position 6,6NI3rnl4n4/3n7cF1o25+
remove 7 from position 13,6NI3rnl4n4/3ncF1o25+
remove r from position 4,6NI3nl4n4/3ncF1o25+
remove N from position 1,6I3nl4n4/3ncF1o25+
add l at position 10,6I3nl4n4/3lncF1o25+
remove l from position 4,6I3n4n4/3lncF1o25+
remove I from position 1,63n4n4/3lncF1o25+
remove 5 from position 15,63n4n4/3lncF1o2+
remove F from position 11,63n4n4/3lnc1o2+
replace 4 at position 3 with F,63nFn4/3lnc1o2+
replace / at position 6 with 2,63nFn423lnc1o2+
replace F at position 3 with B,63nBn423lnc1o2+
add 6 at position 11,63nBn423lnc61o2+
add s at position 14,63nBn423lnc61os2+
remove 1 from position 12,63nBn423lnc6os2+
remove s from position 13,63nBn423lnc6o2+
remove n from position 4,63nB423lnc6o2+
add c at position 7,63nB423clnc6o2+
replace 6 at position 11 with 1,63nB423clnc1o2+
add S at position 2,63SnB423clnc1o2+
replace 3 at position 1 with ),6)SnB423clnc1o2+
add I at position 1,6I)SnB423clnc1o2+
replace ) at position 2 with F,6IFSnB423clnc1o2+
replace l at position 10 with 2,6IFSnB423c2nc1o2+
add O at position 8,6IFSnB42O3c2nc1o2+
add = at position 5,6IFSn=B42O3c2nc1o2+
remove S from position 3,6IFn=B42O3c2nc1o2+
replace O at position 8 with B,6IFn=B42B3c2nc1o2+
replace n at position 12 with ],6IFn=B42B3c2]c1o2+
replace n at position 3 with ],6IF]=B42B3c2]c1o2+
add ( at position 3,6IF(]=B42B3c2]c1o2+
remove = from position 5,6IF(]B42B3c2]c1o2+
replace 3 at position 9 with O,6IF(]B42BOc2]c1o2+
add - at position 16,6IF(]B42BOc2]c1o-2+
replace B at position 5 with ),6IF(])42BOc2]c1o-2+
replace o at position 15 with ),6IF(])42BOc2]c1)-2+
replace ) at position 15 with (,6IF(])42BOc2]c1(-2+
add ) at position 18,6IF(])42BOc2]c1(-2)+
add - at position 11,6IF(])42BOc-2]c1(-2)+
replace I at position 1 with C,6CF(])42BOc-2]c1(-2)+
remove ] from position 4,6CF()42BOc-2]c1(-2)+
replace 4 at position 5 with 1,6CF()12BOc-2]c1(-2)+
remove O from position 8,6CF()12Bc-2]c1(-2)+
add B at position 14,6CF()12Bc-2]c1B(-2)+
remove - from position 16,6CF()12Bc-2]c1B(2)+
remove ] from position 11,6CF()12Bc-2c1B(2)+
add F at position 4,6CF(F)12Bc-2c1B(2)+
add H at position 2,6CHF(F)12Bc-2c1B(2)+
remove H from position 2,6CF(F)12Bc-2c1B(2)+
add c at position 12,6CF(F)12Bc-2cc1B(2)+
remove - from position 10,6CF(F)12Bc2cc1B(2)+
add C at position 16,6CF(F)12Bc2cc1B(C2)+
add C at position 16,6CF(F)12Bc2cc1B(CC2)+
add ) at position 6,6CF(F))12Bc2cc1B(CC2)+
replace 2 at position 8 with c,6CF(F))1cBc2cc1B(CC2)+
remove B from position 9,6CF(F))1cc2cc1B(CC2)+
add C at position 17,6CF(F))1cc2cc1B(CCC2)+
add F at position 1,6FCF(F))1cc2cc1B(CCC2)+
remove ) from position 21,6FCF(F))1cc2cc1B(CCC2+
add c at position 14,6FCF(F))1cc2ccc1B(CCC2+
replace ) at position 7 with c,6FCF(F)c1cc2ccc1B(CCC2+
replace B at position 16 with C,6FCF(F)c1cc2ccc1C(CCC2+
remove + from position 22,6FCF(F)c1cc2ccc1C(CCC2
add ) at position 4,6FCF)(F)c1cc2ccc1C(CCC2
add + at position 15,6FCF)(F)c1cc2cc+c1C(CCC2
remove 6 from position 0,FCF)(F)c1cc2cc+c1C(CCC2
remove 2 from position 11,FCF)(F)c1cccc+c1C(CCC2
add ( at position 2,FC(F)(F)c1cccc+c1C(CCC2
remove ( from position 18,FC(F)(F)c1cccc+c1CCCC2
replace + at position 14 with 2,FC(F)(F)c1cccc2c1CCCC2
final: FC(F)(F)c1cccc2c1CCCC2,FC(F)(F)c1cccc2c1CCCC2
