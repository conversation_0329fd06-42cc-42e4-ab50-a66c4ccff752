log,state
initialize: ,
add O at position 0,O
add r at position 0,r<PERSON>
add I at position 2,r<PERSON><PERSON>
add ( at position 2,rO(I
add 2 at position 1,r2O(I
replace O at position 2 with (,r2((I
add [ at position 0,[r2((I
add c at position 2,[rc2((I
add C at position 6,[rc2((CI
add 2 at position 2,[r2c2((CI
remove 2 from position 4,[r2c((CI
replace I at position 7 with C,[r2c((CC
replace 2 at position 2 with c,[rcc((CC
replace [ at position 0 with =,=rcc((CC
remove = from position 0,rcc((CC
replace C at position 5 with ],rcc((]C
remove ( from position 4,rcc(]C
remove C from position 5,rcc(]
remove ( from position 3,rcc]
remove c from position 1,rc]
replace c at position 1 with o,ro]
remove r from position 0,o]
replace o at position 0 with -,-]
add 7 at position 0,7-]
add r at position 0,r7-]
replace 7 at position 1 with O,rO-]
remove r from position 0,O-]
add 3 at position 0,3O-]
add = at position 2,3O=-]
add F at position 5,3O=-]F
remove ] from position 4,3O=-F
replace F at position 4 with 3,3O=-3
add l at position 2,3Ol=-3
replace = at position 3 with ],3Ol]-3
add # at position 5,3Ol]-#3
remove l from position 2,3O]-#3
remove # from position 4,3O]-3
replace O at position 1 with C,3C]-3
add o at position 4,3C]-o3
add 1 at position 3,3C]1-o3
replace ] at position 2 with B,3CB1-o3
remove - from position 4,3CB1o3
remove 1 from position 3,3CBo3
replace 3 at position 4 with C,3CBoC
remove 3 from position 0,CBoC
replace B at position 1 with ),C)oC
replace ) at position 1 with 5,C5oC
add [ at position 2,C5[oC
replace o at position 3 with +,C5[+C
add = at position 2,C5=[+C
add N at position 2,C5N=[+C
replace C at position 0 with /,/5N=[+C
add 7 at position 2,/57N=[+C
remove 7 from position 2,/5N=[+C
remove C from position 6,/5N=[+
add r at position 6,/5N=[+r
replace + at position 5 with #,/5N=[#r
add r at position 7,/5N=[#rr
add H at position 8,/5N=[#rrH
add l at position 3,/5Nl=[#rrH
add 4 at position 7,/5Nl=[#4rrH
remove l from position 3,/5N=[#4rrH
add ( at position 1,/(5N=[#4rrH
add c at position 1,/c(5N=[#4rrH
remove # from position 7,/c(5N=[4rrH
replace H at position 10 with 1,/c(5N=[4rr1
remove r from position 8,/c(5N=[4r1
replace ( at position 2 with /,/c/5N=[4r1
replace / at position 2 with C,/cC5N=[4r1
add F at position 3,/cCF5N=[4r1
add 5 at position 9,/cCF5N=[45r1
replace = at position 6 with #,/cCF5N#[45r1
replace 4 at position 8 with +,/cCF5N#[+5r1
add H at position 8,/cCF5N#[H+5r1
add 3 at position 9,/cCF5N#[H3+5r1
remove 1 from position 13,/cCF5N#[H3+5r
replace 3 at position 9 with ),/cCF5N#[H)+5r
replace 5 at position 11 with r,/cCF5N#[H)+rr
add 6 at position 9,/cCF5N#[H6)+rr
remove # from position 6,/cCF5N[H6)+rr
add 4 at position 10,/cCF5N[H6)4+rr
replace [ at position 6 with 3,/cCF5N3H6)4+rr
add 7 at position 4,/cCF75N3H6)4+rr
add + at position 13,/cCF75N3H6)4++rr
remove c from position 1,/CF75N3H6)4++rr
replace N at position 5 with F,/CF75F3H6)4++rr
remove / from position 0,CF75F3H6)4++rr
add H at position 14,CF75F3H6)4++rrH
replace 7 at position 2 with I,CFI5F3H6)4++rrH
remove I from position 2,CF5F3H6)4++rrH
replace ) at position 7 with 7,CF5F3H674++rrH
replace F at position 1 with o,Co5F3H674++rrH
add 2 at position 7,Co5F3H6274++rrH
add ( at position 14,Co5F3H6274++rr(H
add n at position 6,Co5F3Hn6274++rr(H
add [ at position 11,Co5F3Hn6274[++rr(H
add 1 at position 9,Co5F3Hn62174[++rr(H
replace 5 at position 2 with 6,Co6F3Hn62174[++rr(H
replace 2 at position 8 with C,Co6F3Hn6C174[++rr(H
add + at position 4,Co6F+3Hn6C174[++rr(H
replace + at position 15 with c,Co6F+3Hn6C174[+crr(H
replace + at position 4 with l,Co6Fl3Hn6C174[+crr(H
add C at position 12,Co6Fl3Hn6C17C4[+crr(H
add [ at position 21,Co6Fl3Hn6C17C4[+crr(H[
add N at position 11,Co6Fl3Hn6C1N7C4[+crr(H[
add B at position 23,Co6Fl3Hn6C1N7C4[+crr(H[B
add r at position 15,Co6Fl3Hn6C1N7C4r[+crr(H[B
add o at position 19,Co6Fl3Hn6C1N7C4r[+corr(H[B
replace C at position 13 with H,Co6Fl3Hn6C1N7H4r[+corr(H[B
remove H from position 23,Co6Fl3Hn6C1N7H4r[+corr([B
add N at position 23,Co6Fl3Hn6C1N7H4r[+corr(N[B
add [ at position 6,Co6Fl3[Hn6C1N7H4r[+corr(N[B
replace [ at position 17 with 2,Co6Fl3[Hn6C1N7H4r2+corr(N[B
add 4 at position 5,Co6Fl43[Hn6C1N7H4r2+corr(N[B
add c at position 5,Co6Flc43[Hn6C1N7H4r2+corr(N[B
replace [ at position 8 with C,Co6Flc43CHn6C1N7H4r2+corr(N[B
add O at position 11,Co6Flc43CHnO6C1N7H4r2+corr(N[B
add - at position 5,Co6Fl-c43CHnO6C1N7H4r2+corr(N[B
remove H from position 18,Co6Fl-c43CHnO6C1N74r2+corr(N[B
add H at position 27,Co6Fl-c43CHnO6C1N74r2+corr(HN[B
replace F at position 3 with o,Co6ol-c43CHnO6C1N74r2+corr(HN[B
remove H from position 10,Co6ol-c43CnO6C1N74r2+corr(HN[B
replace 4 at position 7 with c,Co6ol-cc3CnO6C1N74r2+corr(HN[B
add S at position 13,Co6ol-cc3CnO6SC1N74r2+corr(HN[B
add 4 at position 23,Co6ol-cc3CnO6SC1N74r2+c4orr(HN[B
add ( at position 29,Co6ol-cc3CnO6SC1N74r2+c4orr(H(N[B
replace N at position 16 with s,Co6ol-cc3CnO6SC1s74r2+c4orr(H(N[B
replace C at position 9 with [,Co6ol-cc3[nO6SC1s74r2+c4orr(H(N[B
add ( at position 13,Co6ol-cc3[nO6(SC1s74r2+c4orr(H(N[B
add 5 at position 4,Co6o5l-cc3[nO6(SC1s74r2+c4orr(H(N[B
add 1 at position 23,Co6o5l-cc3[nO6(SC1s74r21+c4orr(H(N[B
replace o at position 1 with 1,C16o5l-cc3[nO6(SC1s74r21+c4orr(H(N[B
remove S from position 15,C16o5l-cc3[nO6(C1s74r21+c4orr(H(N[B
add l at position 21,C16o5l-cc3[nO6(C1s74rl21+c4orr(H(N[B
remove l from position 5,C16o5-cc3[nO6(C1s74rl21+c4orr(H(N[B
replace ( at position 31 with 7,C16o5-cc3[nO6(C1s74rl21+c4orr(H7N[B
replace [ at position 33 with 6,C16o5-cc3[nO6(C1s74rl21+c4orr(H7N6B
remove 6 from position 12,C16o5-cc3[nO(C1s74rl21+c4orr(H7N6B
remove 5 from position 4,C16o-cc3[nO(C1s74rl21+c4orr(H7N6B
replace r at position 26 with 5,C16o-cc3[nO(C1s74rl21+c4or5(H7N6B
add S at position 23,C16o-cc3[nO(C1s74rl21+cS4or5(H7N6B
replace ( at position 28 with ),C16o-cc3[nO(C1s74rl21+cS4or5)H7N6B
replace [ at position 8 with (,C16o-cc3(nO(C1s74rl21+cS4or5)H7N6B
replace o at position 25 with c,C16o-cc3(nO(C1s74rl21+cS4cr5)H7N6B
add 6 at position 32,C16o-cc3(nO(C1s74rl21+cS4cr5)H7N66B
add 1 at position 15,C16o-cc3(nO(C1s174rl21+cS4cr5)H7N66B
replace - at position 4 with ],C16o]cc3(nO(C1s174rl21+cS4cr5)H7N66B
add 7 at position 8,C16o]cc37(nO(C1s174rl21+cS4cr5)H7N66B
replace 1 at position 14 with (,C16o]cc37(nO(C(s174rl21+cS4cr5)H7N66B
add 7 at position 21,C16o]cc37(nO(C(s174rl721+cS4cr5)H7N66B
add 7 at position 1,C716o]cc37(nO(C(s174rl721+cS4cr5)H7N66B
remove ) from position 32,C716o]cc37(nO(C(s174rl721+cS4cr5H7N66B
add C at position 37,C716o]cc37(nO(C(s174rl721+cS4cr5H7N66CB
replace 4 at position 19 with 1,C716o]cc37(nO(C(s171rl721+cS4cr5H7N66CB
replace o at position 4 with c,C716c]cc37(nO(C(s171rl721+cS4cr5H7N66CB
add r at position 27,C716c]cc37(nO(C(s171rl721+crS4cr5H7N66CB
add = at position 20,C716c]cc37(nO(C(s171=rl721+crS4cr5H7N66CB
remove 1 from position 2,C76c]cc37(nO(C(s171=rl721+crS4cr5H7N66CB
remove 7 from position 1,C6c]cc37(nO(C(s171=rl721+crS4cr5H7N66CB
replace 7 at position 16 with +,C6c]cc37(nO(C(s1+1=rl721+crS4cr5H7N66CB
add - at position 7,C6c]cc3-7(nO(C(s1+1=rl721+crS4cr5H7N66CB
remove H from position 33,C6c]cc3-7(nO(C(s1+1=rl721+crS4cr57N66CB
replace 4 at position 29 with +,C6c]cc3-7(nO(C(s1+1=rl721+crS+cr57N66CB
remove 5 from position 32,C6c]cc3-7(nO(C(s1+1=rl721+crS+cr7N66CB
add - at position 38,C6c]cc3-7(nO(C(s1+1=rl721+crS+cr7N66CB-
replace + at position 17 with ),C6c]cc3-7(nO(C(s1)1=rl721+crS+cr7N66CB-
add 4 at position 32,C6c]cc3-7(nO(C(s1)1=rl721+crS+cr47N66CB-
add 4 at position 18,C6c]cc3-7(nO(C(s1)41=rl721+crS+cr47N66CB-
add 5 at position 37,C6c]cc3-7(nO(C(s1)41=rl721+crS+cr47N656CB-
replace 4 at position 18 with s,C6c]cc3-7(nO(C(s1)s1=rl721+crS+cr47N656CB-
replace 5 at position 37 with C,C6c]cc3-7(nO(C(s1)s1=rl721+crS+cr47N6C6CB-
replace + at position 30 with ),C6c]cc3-7(nO(C(s1)s1=rl721+crS)cr47N6C6CB-
replace 6 at position 1 with I,CIc]cc3-7(nO(C(s1)s1=rl721+crS)cr47N6C6CB-
replace S at position 29 with C,CIc]cc3-7(nO(C(s1)s1=rl721+crC)cr47N6C6CB-
add O at position 1,COIc]cc3-7(nO(C(s1)s1=rl721+crC)cr47N6C6CB-
add 2 at position 42,COIc]cc3-7(nO(C(s1)s1=rl721+crC)cr47N6C6CB2-
remove r from position 33,COIc]cc3-7(nO(C(s1)s1=rl721+crC)c47N6C6CB2-
add ] at position 22,COIc]cc3-7(nO(C(s1)s1=]rl721+crC)c47N6C6CB2-
remove 6 from position 37,COIc]cc3-7(nO(C(s1)s1=]rl721+crC)c47NC6CB2-
remove B from position 40,COIc]cc3-7(nO(C(s1)s1=]rl721+crC)c47NC6C2-
add c at position 30,COIc]cc3-7(nO(C(s1)s1=]rl721+ccrC)c47NC6C2-
add [ at position 19,COIc]cc3-7(nO(C(s1)[s1=]rl721+ccrC)c47NC6C2-
add 2 at position 4,COIc2]cc3-7(nO(C(s1)[s1=]rl721+ccrC)c47NC6C2-
replace 7 at position 10 with C,COIc2]cc3-C(nO(C(s1)[s1=]rl721+ccrC)c47NC6C2-
remove 2 from position 28,COIc2]cc3-C(nO(C(s1)[s1=]rl71+ccrC)c47NC6C2-
remove 7 from position 37,COIc2]cc3-C(nO(C(s1)[s1=]rl71+ccrC)c4NC6C2-
add F at position 17,COIc2]cc3-C(nO(C(Fs1)[s1=]rl71+ccrC)c4NC6C2-
add l at position 7,COIc2]clc3-C(nO(C(Fs1)[s1=]rl71+ccrC)c4NC6C2-
add s at position 36,COIc2]clc3-C(nO(C(Fs1)[s1=]rl71+ccrCs)c4NC6C2-
add @ at position 26,COIc2]clc3-C(nO(C(Fs1)[s1=@]rl71+ccrCs)c4NC6C2-
replace ] at position 5 with c,COIc2cclc3-C(nO(C(Fs1)[s1=@]rl71+ccrCs)c4NC6C2-
add o at position 9,COIc2cclco3-C(nO(C(Fs1)[s1=@]rl71+ccrCs)c4NC6C2-
add ] at position 43,COIc2cclco3-C(nO(C(Fs1)[s1=@]rl71+ccrCs)c4N]C6C2-
add - at position 19,COIc2cclco3-C(nO(C(-Fs1)[s1=@]rl71+ccrCs)c4N]C6C2-
add c at position 17,COIc2cclco3-C(nO(cC(-Fs1)[s1=@]rl71+ccrCs)c4N]C6C2-
add 5 at position 43,COIc2cclco3-C(nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]C6C2-
add s at position 47,COIc2cclco3-C(nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]sC6C2-
replace ( at position 13 with 4,COIc2cclco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]sC6C2-
add c at position 48,COIc2cclco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]scC6C2-
add O at position 3,COIOc2cclco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]scC6C2-
add C at position 0,CCOIOc2cclco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c54N]scC6C2-
add ) at position 45,CCOIOc2cclco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c)54N]scC6C2-
remove l from position 9,CCOIOc2ccco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c)54N]scC6C2-
remove I from position 3,CCOOc2ccco3-C4nO(cC(-Fs1)[s1=@]rl71+ccrCs)c)54N]scC6C2-
add ] at position 21,CCOOc2ccco3-C4nO(cC(-]Fs1)[s1=@]rl71+ccrCs)c)54N]scC6C2-
remove s from position 49,CCOOc2ccco3-C4nO(cC(-]Fs1)[s1=@]rl71+ccrCs)c)54N]cC6C2-
remove F from position 22,CCOOc2ccco3-C4nO(cC(-]s1)[s1=@]rl71+ccrCs)c)54N]cC6C2-
replace 4 at position 13 with H,CCOOc2ccco3-CHnO(cC(-]s1)[s1=@]rl71+ccrCs)c)54N]cC6C2-
add C at position 11,CCOOc2ccco3C-CHnO(cC(-]s1)[s1=@]rl71+ccrCs)c)54N]cC6C2-
replace r at position 32 with /,CCOOc2ccco3C-CHnO(cC(-]s1)[s1=@]/l71+ccrCs)c)54N]cC6C2-
replace s at position 23 with O,CCOOc2ccco3C-CHnO(cC(-]O1)[s1=@]/l71+ccrCs)c)54N]cC6C2-
add ] at position 15,CCOOc2ccco3C-CH]nO(cC(-]O1)[s1=@]/l71+ccrCs)c)54N]cC6C2-
add s at position 56,CCOOc2ccco3C-CH]nO(cC(-]O1)[s1=@]/l71+ccrCs)c)54N]cC6C2-s
remove 1 from position 25,CCOOc2ccco3C-CH]nO(cC(-]O)[s1=@]/l71+ccrCs)c)54N]cC6C2-s
remove s from position 27,CCOOc2ccco3C-CH]nO(cC(-]O)[1=@]/l71+ccrCs)c)54N]cC6C2-s
remove n from position 16,CCOOc2ccco3C-CH]O(cC(-]O)[1=@]/l71+ccrCs)c)54N]cC6C2-s
add r at position 29,CCOOc2ccco3C-CH]O(cC(-]O)[1=@r]/l71+ccrCs)c)54N]cC6C2-s
remove 5 from position 44,CCOOc2ccco3C-CH]O(cC(-]O)[1=@r]/l71+ccrCs)c)4N]cC6C2-s
remove l from position 32,CCOOc2ccco3C-CH]O(cC(-]O)[1=@r]/71+ccrCs)c)4N]cC6C2-s
remove c from position 18,CCOOc2ccco3C-CH]O(C(-]O)[1=@r]/71+ccrCs)c)4N]cC6C2-s
remove 1 from position 25,CCOOc2ccco3C-CH]O(C(-]O)[=@r]/71+ccrCs)c)4N]cC6C2-s
replace ] at position 28 with -,CCOOc2ccco3C-CH]O(C(-]O)[=@r-/71+ccrCs)c)4N]cC6C2-s
replace 6 at position 46 with ),CCOOc2ccco3C-CH]O(C(-]O)[=@r-/71+ccrCs)c)4N]cC)C2-s
replace o at position 9 with 1,CCOOc2ccc13C-CH]O(C(-]O)[=@r-/71+ccrCs)c)4N]cC)C2-s
replace s at position 37 with l,CCOOc2ccc13C-CH]O(C(-]O)[=@r-/71+ccrCl)c)4N]cC)C2-s
remove c from position 44,CCOOc2ccc13C-CH]O(C(-]O)[=@r-/71+ccrCl)c)4N]C)C2-s
add = at position 22,CCOOc2ccc13C-CH]O(C(-]=O)[=@r-/71+ccrCl)c)4N]C)C2-s
replace 4 at position 42 with #,CCOOc2ccc13C-CH]O(C(-]=O)[=@r-/71+ccrCl)c)#N]C)C2-s
replace 7 at position 31 with H,CCOOc2ccc13C-CH]O(C(-]=O)[=@r-/H1+ccrCl)c)#N]C)C2-s
replace + at position 33 with c,CCOOc2ccc13C-CH]O(C(-]=O)[=@r-/H1cccrCl)c)#N]C)C2-s
replace = at position 26 with O,CCOOc2ccc13C-CH]O(C(-]=O)[O@r-/H1cccrCl)c)#N]C)C2-s
add ( at position 7,CCOOc2c(cc13C-CH]O(C(-]=O)[O@r-/H1cccrCl)c)#N]C)C2-s
add ( at position 28,CCOOc2c(cc13C-CH]O(C(-]=O)[O(@r-/H1cccrCl)c)#N]C)C2-s
remove ] from position 46,CCOOc2c(cc13C-CH]O(C(-]=O)[O(@r-/H1cccrCl)c)#NC)C2-s
replace r at position 38 with (,CCOOc2c(cc13C-CH]O(C(-]=O)[O(@r-/H1ccc(Cl)c)#NC)C2-s
add O at position 50,CCOOc2c(cc13C-CH]O(C(-]=O)[O(@r-/H1ccc(Cl)c)#NC)C2O-s
add [ at position 29,CCOOc2c(cc13C-CH]O(C(-]=O)[O([@r-/H1ccc(Cl)c)#NC)C2O-s
add 2 at position 21,CCOOc2c(cc13C-CH]O(C(2-]=O)[O([@r-/H1ccc(Cl)c)#NC)C2O-s
add ( at position 48,CCOOc2c(cc13C-CH]O(C(2-]=O)[O([@r-/H1ccc(Cl)c)#N(C)C2O-s
replace ( at position 29 with +,CCOOc2c(cc13C-CH]O(C(2-]=O)[O+[@r-/H1ccc(Cl)c)#N(C)C2O-s
remove - from position 54,CCOOc2c(cc13C-CH]O(C(2-]=O)[O+[@r-/H1ccc(Cl)c)#N(C)C2Os
replace 3 at position 11 with O,CCOOc2c(cc1OC-CH]O(C(2-]=O)[O+[@r-/H1ccc(Cl)c)#N(C)C2Os
remove O from position 17,CCOOc2c(cc1OC-CH](C(2-]=O)[O+[@r-/H1ccc(Cl)c)#N(C)C2Os
add ) at position 29,CCOOc2c(cc1OC-CH](C(2-]=O)[O+)[@r-/H1ccc(Cl)c)#N(C)C2Os
remove - from position 33,CCOOc2c(cc1OC-CH](C(2-]=O)[O+)[@r/H1ccc(Cl)c)#N(C)C2Os
remove ] from position 22,CCOOc2c(cc1OC-CH](C(2-=O)[O+)[@r/H1ccc(Cl)c)#N(C)C2Os
remove - from position 13,CCOOc2c(cc1OCCH](C(2-=O)[O+)[@r/H1ccc(Cl)c)#N(C)C2Os
remove / from position 31,CCOOc2c(cc1OCCH](C(2-=O)[O+)[@rH1ccc(Cl)c)#N(C)C2Os
add c at position 3,CCOcOc2c(cc1OCCH](C(2-=O)[O+)[@rH1ccc(Cl)c)#N(C)C2Os
add 1 at position 42,CCOcOc2c(cc1OCCH](C(2-=O)[O+)[@rH1ccc(Cl)c1)#N(C)C2Os
remove - from position 21,CCOcOc2c(cc1OCCH](C(2=O)[O+)[@rH1ccc(Cl)c1)#N(C)C2Os
add ] at position 32,CCOcOc2c(cc1OCCH](C(2=O)[O+)[@rH]1ccc(Cl)c1)#N(C)C2Os
add ( at position 33,CCOcOc2c(cc1OCCH](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2Os
add S at position 12,CCOcOc2c(cc1SOCCH](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2Os
add [ at position 15,CCOcOc2c(cc1SOC[CH](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2Os
add = at position 54,CCOcOc2c(cc1SOC[CH](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2=Os
replace O at position 4 with 1,CCOc1c2c(cc1SOC[CH](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2=Os
add @ at position 17,CCOc1c2c(cc1SOC[C@H](C(2=O)[O+)[@rH](1ccc(Cl)c1)#N(C)C2=Os
add C at position 32,CCOc1c2c(cc1SOC[C@H](C(2=O)[O+)[C@rH](1ccc(Cl)c1)#N(C)C2=Os
add ( at position 38,CCOc1c2c(cc1SOC[C@H](C(2=O)[O+)[C@rH]((1ccc(Cl)c1)#N(C)C2=Os
remove r from position 34,CCOc1c2c(cc1SOC[C@H](C(2=O)[O+)[C@H]((1ccc(Cl)c1)#N(C)C2=Os
add ) at position 15,CCOc1c2c(cc1SOC)[C@H](C(2=O)[O+)[C@H]((1ccc(Cl)c1)#N(C)C2=Os
remove C from position 0,COc1c2c(cc1SOC)[C@H](C(2=O)[O+)[C@H]((1ccc(Cl)c1)#N(C)C2=Os
remove S from position 11,COc1c2c(cc1OC)[C@H](C(2=O)[O+)[C@H]((1ccc(Cl)c1)#N(C)C2=Os
add ] at position 29,COc1c2c(cc1OC)[C@H](C(2=O)[O+])[C@H]((1ccc(Cl)c1)#N(C)C2=Os
add c at position 39,COc1c2c(cc1OC)[C@H](C(2=O)[O+])[C@H]((1cccc(Cl)c1)#N(C)C2=Os
remove # from position 50,COc1c2c(cc1OC)[C@H](C(2=O)[O+])[C@H]((1cccc(Cl)c1)N(C)C2=Os
remove 2 from position 22,COc1c2c(cc1OC)[C@H](C(=O)[O+])[C@H]((1cccc(Cl)c1)N(C)C2=Os
add c at position 5,COc1cc2c(cc1OC)[C@H](C(=O)[O+])[C@H]((1cccc(Cl)c1)N(C)C2=Os
replace ( at position 37 with c,COc1cc2c(cc1OC)[C@H](C(=O)[O+])[C@H](c1cccc(Cl)c1)N(C)C2=Os
remove s from position 58,COc1cc2c(cc1OC)[C@H](C(=O)[O+])[C@H](c1cccc(Cl)c1)N(C)C2=O
replace + at position 28 with -,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2=O
final: COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2=O,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2=O
