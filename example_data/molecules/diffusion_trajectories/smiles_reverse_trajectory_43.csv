log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove C from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add S at position 6,oH3H-]SN)
add [ at position 2,oH[3H-]SN)
add 3 at position 3,oH[33H-]SN)
replace S at position 8 with r,oH[33H-]rN)
add n at position 2,oHn[33H-]rN)
add 4 at position 2,oH4n[33H-]rN)
replace [ at position 4 with H,oH4nH33H-]rN)
add ( at position 5,oH4nH(33H-]rN)
add O at position 2,oHO4nH(33H-]rN)
remove H from position 9,oHO4nH(33-]rN)
add 6 at position 13,oHO4nH(33-]rN6)
replace H at position 1 with 6,o6O4nH(33-]rN6)
remove H from position 5,o6O4n(33-]rN6)
replace 4 at position 3 with r,o6Orn(33-]rN6)
add r at position 6,o6Orn(r33-]rN6)
add s at position 11,o6Orn(r33-]srN6)
remove 6 from position 14,o6Orn(r33-]srN)
replace ] at position 10 with H,o6Orn(r33-HsrN)
remove O from position 2,o6rn(r33-HsrN)
add 7 at position 11,o6rn(r33-Hs7rN)
replace N at position 13 with /,o6rn(r33-Hs7r/)
add S at position 6,o6rn(rS33-Hs7r/)
add l at position 2,o6lrn(rS33-Hs7r/)
add F at position 11,o6lrn(rS33-FHs7r/)
replace o at position 0 with S,S6lrn(rS33-FHs7r/)
remove S from position 7,S6lrn(r33-FHs7r/)
add B at position 10,S6lrn(r33-BFHs7r/)
remove l from position 2,S6rn(r33-BFHs7r/)
add r at position 1,Sr6rn(r33-BFHs7r/)
add ) at position 12,Sr6rn(r33-BF)Hs7r/)
replace F at position 11 with 4,Sr6rn(r33-B4)Hs7r/)
add ) at position 19,Sr6rn(r33-B4)Hs7r/))
add 1 at position 2,Sr16rn(r33-B4)Hs7r/))
add C at position 1,SCr16rn(r33-B4)Hs7r/))
add C at position 21,SCr16rn(r33-B4)Hs7r/)C)
remove r from position 2,SC16rn(r33-B4)Hs7r/)C)
add B at position 14,SC16rn(r33-B4)BHs7r/)C)
add ) at position 19,SC16rn(r33-B4)BHs7r)/)C)
add c at position 17,SC16rn(r33-B4)BHsc7r)/)C)
replace - at position 10 with 6,SC16rn(r336B4)BHsc7r)/)C)
add n at position 12,SC16rn(r336Bn4)BHsc7r)/)C)
replace C at position 24 with r,SC16rn(r336Bn4)BHsc7r)/)r)
replace / at position 22 with c,SC16rn(r336Bn4)BHsc7r)c)r)
remove s from position 17,SC16rn(r336Bn4)BHc7r)c)r)
add F at position 19,SC16rn(r336Bn4)BHc7Fr)c)r)
replace r at position 4 with 2,SC162n(r336Bn4)BHc7Fr)c)r)
add / at position 21,SC162n(r336Bn4)BHc7Fr/)c)r)
remove B from position 15,SC162n(r336Bn4)Hc7Fr/)c)r)
add 2 at position 18,SC162n(r336Bn4)Hc72Fr/)c)r)
replace S at position 0 with 7,7C162n(r336Bn4)Hc72Fr/)c)r)
add c at position 20,7C162n(r336Bn4)Hc72Fcr/)c)r)
add ( at position 11,7C162n(r336(Bn4)Hc72Fcr/)c)r)
add ) at position 29,7C162n(r336(Bn4)Hc72Fcr/)c)r))
replace 3 at position 9 with =,7C162n(r3=6(Bn4)Hc72Fcr/)c)r))
replace ) at position 29 with 7,7C162n(r3=6(Bn4)Hc72Fcr/)c)r)7
add = at position 13,7C162n(r3=6(B=n4)Hc72Fcr/)c)r)7
add B at position 20,7C162n(r3=6(B=n4)Hc7B2Fcr/)c)r)7
remove 1 from position 2,7C62n(r3=6(B=n4)Hc7B2Fcr/)c)r)7
remove 7 from position 0,C62n(r3=6(B=n4)Hc7B2Fcr/)c)r)7
replace 6 at position 8 with S,C62n(r3=S(B=n4)Hc7B2Fcr/)c)r)7
add N at position 3,C62Nn(r3=S(B=n4)Hc7B2Fcr/)c)r)7
remove H from position 16,C62Nn(r3=S(B=n4)c7B2Fcr/)c)r)7
replace 4 at position 14 with 1,C62Nn(r3=S(B=n1)c7B2Fcr/)c)r)7
add 5 at position 10,C62Nn(r3=S5(B=n1)c7B2Fcr/)c)r)7
add l at position 13,C62Nn(r3=S5(Bl=n1)c7B2Fcr/)c)r)7
replace ) at position 17 with F,C62Nn(r3=S5(Bl=n1Fc7B2Fcr/)c)r)7
add 1 at position 32,C62Nn(r3=S5(Bl=n1Fc7B2Fcr/)c)r)71
add 4 at position 18,C62Nn(r3=S5(Bl=n1F4c7B2Fcr/)c)r)71
add 7 at position 21,C62Nn(r3=S5(Bl=n1F4c77B2Fcr/)c)r)71
replace 4 at position 18 with s,C62Nn(r3=S5(Bl=n1Fsc77B2Fcr/)c)r)71
replace 7 at position 21 with 1,C62Nn(r3=S5(Bl=n1Fsc71B2Fcr/)c)r)71
remove ) from position 30,C62Nn(r3=S5(Bl=n1Fsc71B2Fcr/)cr)71
add = at position 6,C62Nn(=r3=S5(Bl=n1Fsc71B2Fcr/)cr)71
add + at position 29,C62Nn(=r3=S5(Bl=n1Fsc71B2Fcr/+)cr)71
add ( at position 30,C62Nn(=r3=S5(Bl=n1Fsc71B2Fcr/+()cr)71
remove r from position 33,C62Nn(=r3=S5(Bl=n1Fsc71B2Fcr/+()c)71
add c at position 22,C62Nn(=r3=S5(Bl=n1Fsc7c1B2Fcr/+()c)71
add o at position 23,C62Nn(=r3=S5(Bl=n1Fsc7co1B2Fcr/+()c)71
remove / from position 30,C62Nn(=r3=S5(Bl=n1Fsc7co1B2Fcr+()c)71
add c at position 30,C62Nn(=r3=S5(Bl=n1Fsc7co1B2Fcrc+()c)71
add r at position 19,C62Nn(=r3=S5(Bl=n1Frsc7co1B2Fcrc+()c)71
add ) at position 4,C62N)n(=r3=S5(Bl=n1Frsc7co1B2Fcrc+()c)71
replace = at position 10 with ),C62N)n(=r3)S5(Bl=n1Frsc7co1B2Fcrc+()c)71
remove 2 from position 28,C62N)n(=r3)S5(Bl=n1Frsc7co1BFcrc+()c)71
remove 7 from position 37,C62N)n(=r3)S5(Bl=n1Frsc7co1BFcrc+()c)1
add 4 at position 17,C62N)n(=r3)S5(Bl=4n1Frsc7co1BFcrc+()c)1
add ] at position 7,C62N)n(]=r3)S5(Bl=4n1Frsc7co1BFcrc+()c)1
add F at position 36,C62N)n(]=r3)S5(Bl=4n1Frsc7co1BFcrc+(F)c)1
replace = at position 17 with H,C62N)n(]=r3)S5(BlH4n1Frsc7co1BFcrc+(F)c)1
replace + at position 34 with ),C62N)n(]=r3)S5(BlH4n1Frsc7co1BFcrc)(F)c)1
add B at position 36,C62N)n(]=r3)S5(BlH4n1Frsc7co1BFcrc)(BF)c)1
add n at position 19,C62N)n(]=r3)S5(BlH4nn1Frsc7co1BFcrc)(BF)c)1
replace H at position 17 with -,C62N)n(]=r3)S5(Bl-4nn1Frsc7co1BFcrc)(BF)c)1
replace s at position 24 with c,C62N)n(]=r3)S5(Bl-4nn1Frcc7co1BFcrc)(BF)c)1
add O at position 13,C62N)n(]=r3)SO5(Bl-4nn1Frcc7co1BFcrc)(BF)c)1
remove 7 from position 27,C62N)n(]=r3)SO5(Bl-4nn1Frccco1BFcrc)(BF)c)1
remove r from position 9,C62N)n(]=3)SO5(Bl-4nn1Frccco1BFcrc)(BF)c)1
remove N from position 3,C62)n(]=3)SO5(Bl-4nn1Frccco1BFcrc)(BF)c)1
add r at position 10,C62)n(]=3)rSO5(Bl-4nn1Frccco1BFcrc)(BF)c)1
remove ) from position 34,C62)n(]=3)rSO5(Bl-4nn1Frccco1BFcrc(BF)c)1
remove r from position 10,C62)n(]=3)SO5(Bl-4nn1Frccco1BFcrc(BF)c)1
add O at position 8,C62)n(]=O3)SO5(Bl-4nn1Frccco1BFcrc(BF)c)1
remove B from position 35,C62)n(]=O3)SO5(Bl-4nn1Frccco1BFcrc(F)c)1
remove F from position 22,C62)n(]=O3)SO5(Bl-4nn1rccco1BFcrc(F)c)1
replace 5 at position 13 with 3,C62)n(]=O3)SO3(Bl-4nn1rccco1BFcrc(F)c)1
add - at position 11,C62)n(]=O3)-SO3(Bl-4nn1rccco1BFcrc(F)c)1
replace r at position 32 with (,C62)n(]=O3)-SO3(Bl-4nn1rccco1BFc(c(F)c)1
remove r from position 23,C62)n(]=O3)-SO3(Bl-4nn1ccco1BFc(c(F)c)1
replace F at position 29 with /,C62)n(]=O3)-SO3(Bl-4nn1ccco1B/c(c(F)c)1
remove S from position 12,C62)n(]=O3)-O3(Bl-4nn1ccco1B/c(c(F)c)1
add @ at position 12,C62)n(]=O3)-@O3(Bl-4nn1ccco1B/c(c(F)c)1
add H at position 4,C62)Hn(]=O3)-@O3(Bl-4nn1ccco1B/c(c(F)c)1
replace H at position 4 with ],C62)]n(]=O3)-@O3(Bl-4nn1ccco1B/c(c(F)c)1
replace 4 at position 20 with ],C62)]n(]=O3)-@O3(Bl-]nn1ccco1B/c(c(F)c)1
add S at position 5,C62)]Sn(]=O3)-@O3(Bl-]nn1ccco1B/c(c(F)c)1
replace 2 at position 2 with H,C6H)]Sn(]=O3)-@O3(Bl-]nn1ccco1B/c(c(F)c)1
add @ at position 3,C6H@)]Sn(]=O3)-@O3(Bl-]nn1ccco1B/c(c(F)c)1
replace ) at position 4 with H,C6H@H]Sn(]=O3)-@O3(Bl-]nn1ccco1B/c(c(F)c)1
replace l at position 20 with C,C6H@H]Sn(]=O3)-@O3(BC-]nn1ccco1B/c(c(F)c)1
add 2 at position 17,C6H@H]Sn(]=O3)-@O23(BC-]nn1ccco1B/c(c(F)c)1
add B at position 10,C6H@H]Sn(]B=O3)-@O23(BC-]nn1ccco1B/c(c(F)c)1
remove S from position 6,C6H@H]n(]B=O3)-@O23(BC-]nn1ccco1B/c(c(F)c)1
replace O at position 16 with H,C6H@H]n(]B=O3)-@H23(BC-]nn1ccco1B/c(c(F)c)1
replace n at position 24 with 2,C6H@H]n(]B=O3)-@H23(BC-]2n1ccco1B/c(c(F)c)1
replace n at position 6 with (,C6H@H]((]B=O3)-@H23(BC-]2n1ccco1B/c(c(F)c)1
add C at position 7,C6H@H](C(]B=O3)-@H23(BC-]2n1ccco1B/c(c(F)c)1
remove = from position 11,C6H@H](C(]BO3)-@H23(BC-]2n1ccco1B/c(c(F)c)1
replace 3 at position 18 with O,C6H@H](C(]BO3)-@H2O(BC-]2n1ccco1B/c(c(F)c)1
add - at position 33,C6H@H](C(]BO3)-@H2O(BC-]2n1ccco1B-/c(c(F)c)1
replace B at position 10 with =,C6H@H](C(]=O3)-@H2O(BC-]2n1ccco1B-/c(c(F)c)1
replace o at position 30 with (,C6H@H](C(]=O3)-@H2O(BC-]2n1ccc(1B-/c(c(F)c)1
replace ( at position 30 with c,C6H@H](C(]=O3)-@H2O(BC-]2n1cccc1B-/c(c(F)c)1
add ) at position 37,C6H@H](C(]=O3)-@H2O(BC-]2n1cccc1B-/c()c(F)c)1
add ) at position 22,C6H@H](C(]=O3)-@H2O(BC)-]2n1cccc1B-/c()c(F)c)1
replace H at position 2 with [,C6[@H](C(]=O3)-@H2O(BC)-]2n1cccc1B-/c()c(F)c)1
remove ] from position 9,C6[@H](C(=O3)-@H2O(BC)-]2n1cccc1B-/c()c(F)c)1
replace 3 at position 11 with [,C6[@H](C(=O[)-@H2O(BC)-]2n1cccc1B-/c()c(F)c)1
remove O from position 17,C6[@H](C(=O[)-@H2(BC)-]2n1cccc1B-/c()c(F)c)1
add + at position 29,C6[@H](C(=O[)-@H2(BC)-]2n1ccc+c1B-/c()c(F)c)1
remove - from position 33,C6[@H](C(=O[)-@H2(BC)-]2n1ccc+c1B/c()c(F)c)1
remove ] from position 22,C6[@H](C(=O[)-@H2(BC)-2n1ccc+c1B/c()c(F)c)1
remove - from position 13,C6[@H](C(=O[)@H2(BC)-2n1ccc+c1B/c()c(F)c)1
remove / from position 31,C6[@H](C(=O[)@H2(BC)-2n1ccc+c1Bc()c(F)c)1
add C at position 3,C6[C@H](C(=O[)@H2(BC)-2n1ccc+c1Bc()c(F)c)1
add + at position 42,C6[C@H](C(=O[)@H2(BC)-2n1ccc+c1Bc()c(F)c)1+
remove - from position 21,C6[C@H](C(=O[)@H2(BC)2n1ccc+c1Bc()c(F)c)1+
add c at position 32,C6[C@H](C(=O[)@H2(BC)2n1ccc+c1Bcc()c(F)c)1+
add ( at position 33,C6[C@H](C(=O[)@H2(BC)2n1ccc+c1Bcc(()c(F)c)1+
add ) at position 12,C6[C@H](C(=O)[)@H2(BC)2n1ccc+c1Bcc(()c(F)c)1+
replace 2 at position 17 with ],C6[C@H](C(=O)[)@H](BC)2n1ccc+c1Bcc(()c(F)c)1+
remove B from position 19,C6[C@H](C(=O)[)@H](C)2n1ccc+c1Bcc(()c(F)c)1+
add C at position 35,C6[C@H](C(=O)[)@H](C)2n1ccc+c1Bcc((C)c(F)c)1+
add C at position 2,C6C[C@H](C(=O)[)@H](C)2n1ccc+c1Bcc((C)c(F)c)1+
remove ) from position 43,C6C[C@H](C(=O)[)@H](C)2n1ccc+c1Bcc((C)c(F)c1+
add ) at position 29,C6C[C@H](C(=O)[)@H](C)2n1ccc+)c1Bcc((C)c(F)c1+
replace ) at position 15 with C,C6C[C@H](C(=O)[C@H](C)2n1ccc+)c1Bcc((C)c(F)c1+
replace B at position 32 with c,C6C[C@H](C(=O)[C@H](C)2n1ccc+)c1ccc((C)c(F)c1+
remove + from position 45,C6C[C@H](C(=O)[C@H](C)2n1ccc+)c1ccc((C)c(F)c1
add N at position 9,C6C[C@H](NC(=O)[C@H](C)2n1ccc+)c1ccc((C)c(F)c1
add 1 at position 30,C6C[C@H](NC(=O)[C@H](C)2n1ccc+1)c1ccc((C)c(F)c1
remove 6 from position 1,CC[C@H](NC(=O)[C@H](C)2n1ccc+1)c1ccc((C)c(F)c1
remove 2 from position 22,CC[C@H](NC(=O)[C@H](C)n1ccc+1)c1ccc((C)c(F)c1
add @ at position 5,CC[C@@H](NC(=O)[C@H](C)n1ccc+1)c1ccc((C)c(F)c1
remove ( from position 37,CC[C@@H](NC(=O)[C@H](C)n1ccc+1)c1ccc(C)c(F)c1
replace + at position 28 with n,CC[C@@H](NC(=O)[C@H](C)n1cccn1)c1ccc(C)c(F)c1
final: CC[C@@H](NC(=O)[C@H](C)n1cccn1)c1ccc(C)c(F)c1,CC[C@@H](NC(=O)[C@H](C)n1cccn1)c1ccc(C)c(F)c1
