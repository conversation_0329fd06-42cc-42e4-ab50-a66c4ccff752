log,state
initialize: CCCC(=O)N[C@@H]1CCC[NH+](Cc2ncccc2C)C1,CCCC(=O)N[C@@H]1CCC[NH+](Cc2ncccc2C)C1
replace n at position 28 with +,CCCC(=O)N[C@@H]1CCC[NH+](Cc2+cccc2C)C1
add ( at position 37,CCCC(=O)N[C@@H]1CCC[NH+](Cc2+cccc2C)C(1
remove = from position 5,CCCC(O)N[C@@H]1CCC[NH+](Cc2+cccc2C)C(1
add 2 at position 22,CCCC(O)N[C@@H]1CCC[NH+2](Cc2+cccc2C)C(1
add 6 at position 1,C6CCC(O)N[C@@H]1CCC[NH+2](Cc2+cccc2C)C(1
remove c from position 30,C6CCC(O)N[C@@H]1CCC[NH+2](Cc2+ccc2C)C(1
remove [ from position 9,C6CC<PERSON>(O)NC@@H]1CCC[NH+2](Cc2+ccc2C)C(1
add n at position 7,C6CCC(On)NC@@H]1CCC[NH+2](Cc2+ccc2C)C(1
add o at position 31,C6CCC(On)NC@@H]1CCC[NH+2](Cc2+cocc2C)C(1
replace C at position 4 with S,C6CCS(On)NC@@H]1CCC[NH+2](Cc2+cocc2C)C(1
remove + from position 29,C6CCS(On)NC@@H]1CCC[NH+2](Cc2cocc2C)C(1
add C at position 5,C6CCSC(On)NC@@H]1CCC[NH+2](Cc2cocc2C)C(1
remove C from position 17,C6CCSC(On)NC@@H]1CC[NH+2](Cc2cocc2C)C(1
replace S at position 4 with N,C6CCNC(On)NC@@H]1CC[NH+2](Cc2cocc2C)C(1
remove + from position 22,C6CCNC(On)NC@@H]1CC[NH2](Cc2cocc2C)C(1
remove @ from position 12,C6CCNC(On)NC@H]1CC[NH2](Cc2cocc2C)C(1
remove ) from position 33,C6CCNC(On)NC@H]1CC[NH2](Cc2cocc2CC(1
remove C from position 32,C6CCNC(On)NC@H]1CC[NH2](Cc2cocc2C(1
add - at position 21,C6CCNC(On)NC@H]1CC[NH-2](Cc2cocc2C(1
remove ( from position 24,C6CCNC(On)NC@H]1CC[NH-2]Cc2cocc2C(1
add H at position 4,C6CCHNC(On)NC@H]1CC[NH-2]Cc2cocc2C(1
remove H from position 4,C6CCNC(On)NC@H]1CC[NH-2]Cc2cocc2C(1
remove ) from position 9,C6CCNC(OnNC@H]1CC[NH-2]Cc2cocc2C(1
add ] at position 22,C6CCNC(OnNC@H]1CC[NH-2]]Cc2cocc2C(1
add - at position 33,C6CCNC(OnNC@H]1CC[NH-2]]Cc2cocc2C-(1
remove c from position 29,C6CCNC(OnNC@H]1CC[NH-2]]Cc2coc2C-(1
add O at position 17,C6CCNC(OnNC@H]1CCO[NH-2]]Cc2coc2C-(1
replace @ at position 11 with 3,C6CCNC(OnNC3H]1CCO[NH-2]]Cc2coc2C-(1
add ] at position 9,C6CCNC(On]NC3H]1CCO[NH-2]]Cc2coc2C-(1
replace C at position 2 with I,C6ICNC(On]NC3H]1CCO[NH-2]]Cc2coc2C-(1
remove - from position 22,C6ICNC(On]NC3H]1CCO[NH2]]Cc2coc2C-(1
remove o from position 29,C6ICNC(On]NC3H]1CCO[NH2]]Cc2cc2C-(1
remove 2 from position 30,C6ICNC(On]NC3H]1CCO[NH2]]Cc2ccC-(1
replace N at position 10 with @,C6ICNC(On]@C3H]1CCO[NH2]]Cc2ccC-(1
remove 1 from position 33,C6ICNC(On]@C3H]1CCO[NH2]]Cc2ccC-(
replace O at position 18 with 3,C6ICNC(On]@C3H]1CC3[NH2]]Cc2ccC-(
add = at position 11,C6ICNC(On]@=C3H]1CC3[NH2]]Cc2ccC-(
remove O from position 7,C6ICNC(n]@=C3H]1CC3[NH2]]Cc2ccC-(
replace ( at position 6 with n,C6ICNCnn]@=C3H]1CC3[NH2]]Cc2ccC-(
replace ] at position 24 with n,C6ICNCnn]@=C3H]1CC3[NH2]nCc2ccC-(
replace C at position 16 with O,C6ICNCnn]@=C3H]1OC3[NH2]nCc2ccC-(
add S at position 6,C6ICNCSnn]@=C3H]1OC3[NH2]nCc2ccC-(
remove @ from position 10,C6ICNCSnn]=C3H]1OC3[NH2]nCc2ccC-(
remove C from position 17,C6ICNCSnn]=C3H]1O3[NH2]nCc2ccC-(
replace H at position 20 with l,C6ICNCSnn]=C3H]1O3[Nl2]nCc2ccC-(
replace N at position 4 with ),C6IC)CSnn]=C3H]1O3[Nl2]nCc2ccC-(
remove C from position 3,C6I)CSnn]=C3H]1O3[Nl2]nCc2ccC-(
replace 2 at position 25 with (,C6I)CSnn]=C3H]1O3[Nl2]nCc(ccC-(
add c at position 5,C6I)CcSnn]=C3H]1O3[Nl2]nCc(ccC-(
replace l at position 20 with 4,C6I)CcSnn]=C3H]1O3[N42]nCc(ccC-(
replace C at position 4 with I,C6I)IcSnn]=C3H]1O3[N42]nCc(ccC-(
remove I from position 4,C6I)cSnn]=C3H]1O3[N42]nCc(ccC-(
remove ( from position 30,C6I)cSnn]=C3H]1O3[N42]nCc(ccC-
add 1 at position 12,C6I)cSnn]=C31H]1O3[N42]nCc(ccC-
remove c from position 28,C6I)cSnn]=C31H]1O3[N42]nCc(cC-
remove n from position 7,C6I)cSn]=C31H]1O3[N42]nCc(cC-
replace 1 at position 11 with s,C6I)cSn]=C3sH]1O3[N42]nCc(cC-
replace 3 at position 16 with r,C6I)cSn]=C3sH]1Or[N42]nCc(cC-
remove S from position 5,C6I)cn]=C3sH]1Or[N42]nCc(cC-
replace ] at position 6 with 4,C6I)cn4=C3sH]1Or[N42]nCc(cC-
add F at position 11,C6I)cn4=C3sFH]1Or[N42]nCc(cC-
add ] at position 29,C6I)cn4=C3sFH]1Or[N42]nCc(cC-]
replace O at position 15 with 5,C6I)cn4=C3sFH]15r[N42]nCc(cC-]
add I at position 1,CI6I)cn4=C3sFH]15r[N42]nCc(cC-]
add l at position 9,CI6I)cn4=lC3sFH]15r[N42]nCc(cC-]
remove N from position 20,CI6I)cn4=lC3sFH]15r[42]nCc(cC-]
add N at position 3,CI6NI)cn4=lC3sFH]15r[42]nCc(cC-]
add r at position 9,CI6NI)cn4r=lC3sFH]15r[42]nCc(cC-]
add 7 at position 27,CI6NI)cn4r=lC3sFH]15r[42]nC7c(cC-]
remove 3 from position 13,CI6NI)cn4r=lCsFH]15r[42]nC7c(cC-]
replace n at position 24 with s,CI6NI)cn4r=lCsFH]15r[42]sC7c(cC-]
replace 1 at position 17 with H,CI6NI)cn4r=lCsFH]H5r[42]sC7c(cC-]
remove r from position 19,CI6NI)cn4r=lCsFH]H5[42]sC7c(cC-]
remove r from position 9,CI6NI)cn4=lCsFH]H5[42]sC7c(cC-]
replace 6 at position 2 with c,CIcNI)cn4=lCsFH]H5[42]sC7c(cC-]
remove F from position 13,CIcNI)cn4=lCsH]H5[42]sC7c(cC-]
remove 4 from position 18,CIcNI)cn4=lCsH]H5[2]sC7c(cC-]
remove N from position 3,CIcI)cn4=lCsH]H5[2]sC7c(cC-]
remove = from position 8,CIcI)cn4lCsH]H5[2]sC7c(cC-]
add l at position 22,CIcI)cn4lCsH]H5[2]sC7cl(cC-]
remove I from position 1,CcI)cn4lCsH]H5[2]sC7cl(cC-]
remove ] from position 26,CcI)cn4lCsH]H5[2]sC7cl(cC-
remove C from position 24,CcI)cn4lCsH]H5[2]sC7cl(c-
remove H from position 12,CcI)cn4lCsH]5[2]sC7cl(c-
remove I from position 2,Cc)cn4lCsH]5[2]sC7cl(c-
remove H from position 9,Cc)cn4lCs]5[2]sC7cl(c-
remove - from position 21,Cc)cn4lCs]5[2]sC7cl(c
replace 4 at position 5 with =,Cc)cn=lCs]5[2]sC7cl(c
remove = from position 5,Cc)cnlCs]5[2]sC7cl(c
remove 2 from position 11,Cc)cnlCs]5[]sC7cl(c
remove ] from position 11,Cc)cnlCs]5[sC7cl(c
add r at position 16,Cc)cnlCs]5[sC7clr(c
remove l from position 15,Cc)cnlCs]5[sC7cr(c
remove c from position 14,Cc)cnlCs]5[sC7r(c
remove c from position 3,Cc)nlCs]5[sC7r(c
add ) at position 15,Cc)nlCs]5[sC7r()c
replace s at position 10 with 6,Cc)nlCs]5[6C7r()c
replace [ at position 9 with 4,Cc)nlCs]546C7r()c
remove 6 from position 10,Cc)nlCs]54C7r()c
remove 4 from position 9,Cc)nlCs]5C7r()c
remove 5 from position 8,Cc)nlCs]C7r()c
replace l at position 4 with ),Cc)n)Cs]C7r()c
remove 7 from position 9,Cc)n)Cs]Cr()c
add 5 at position 8,Cc)n)Cs]5Cr()c
replace ] at position 7 with 3,Cc)n)Cs35Cr()c
add H at position 8,Cc)n)Cs3H5Cr()c
remove c from position 1,C)n)Cs3H5Cr()c
replace C at position 4 with 6,C)n)6s3H5Cr()c
add 7 at position 0,7C)n)6s3H5Cr()c
add 1 at position 1,71C)n)6s3H5Cr()c
remove 5 from position 10,71C)n)6s3HCr()c
remove 6 from position 6,71C)n)s3HCr()c
replace 1 at position 1 with o,7oC)n)s3HCr()c
replace n at position 4 with 3,7oC)3)s3HCr()c
remove C from position 9,7oC)3)s3Hr()c
add ) at position 8,7oC)3)s3)Hr()c
remove ) from position 12,7oC)3)s3)Hr(c
add 5 at position 9,7oC)3)s3)5Hr(c
replace ) at position 3 with 1,7oC13)s3)5Hr(c
remove H from position 10,7oC13)s3)5r(c
replace C at position 2 with r,7or13)s3)5r(c
remove 5 from position 9,7or13)s3)r(c
add s at position 8,7or13)s3s)r(c
replace ( at position 11 with 1,7or13)s3s)r1c
replace c at position 12 with C,7or13)s3s)r1C
remove s from position 6,7or13)3s)r1C
replace ) at position 5 with /,7or13/3s)r1C
remove ) from position 8,7or13/3sr1C
remove 1 from position 9,7or13/3srC
remove s from position 7,7or13/3rC
add r at position 1,7ror13/3rC
remove / from position 6,7ror133rC
replace r at position 1 with 5,75or133rC
add 6 at position 3,75o6r133rC
replace r at position 8 with S,75o6r133SC
replace C at position 9 with B,75o6r133SB
replace 7 at position 0 with B,B5o6r133SB
replace r at position 4 with =,B5o6=133SB
replace o at position 2 with B,B5B6=133SB
remove 6 from position 3,B5B=133SB
remove B from position 0,5B=133SB
replace 3 at position 5 with I,5B=13ISB
remove = from position 2,5B13ISB
add 1 at position 0,15B13ISB
replace B at position 2 with F,15F13ISB
replace 3 at position 4 with O,15F1OISB
remove B from position 7,15F1OIS
remove I from position 5,15F1OS
remove 1 from position 3,15FOS
remove F from position 2,15OS
add o at position 0,o15OS
remove 5 from position 2,o1OS
add ( at position 4,o1OS(
replace ( at position 4 with I,o1OSI
remove I from position 4,o1OS
add 6 at position 2,o16OS
replace 6 at position 2 with [,o1[OS
remove 1 from position 1,o[OS
remove [ from position 1,oOS
replace S at position 2 with [,oO[
remove o from position 0,O[
remove O from position 0,[
remove [ from position 0,
final: ,
