log,state
initialize: CCN(Cc1ccccc1)C(=O)c1cc(NC(=O)Cc2ccccc2)n(C)n1,CCN(Cc1ccccc1)C(=O)c1cc(NC(=O)Cc2ccccc2)n(C)n1
replace O at position 28 with +,CCN(Cc1ccccc1)C(=O)c1cc(NC(=+)Cc2ccccc2)n(C)n1
add ( at position 37,CCN(Cc1ccccc1)C(=O)c1cc(NC(=+)Cc2cccc(c2)n(C)n1
remove c from position 5,CCN(C1ccccc1)C(=O)c1cc(NC(=+)Cc2cccc(c2)n(C)n1
add 2 at position 22,CCN(C1ccccc1)C(=O)c1cc2(NC(=+)Cc2cccc(c2)n(C)n1
add 6 at position 1,C6CN(C1ccccc1)C(=O)c1cc2(NC(=+)Cc2cccc(c2)n(C)n1
remove ) from position 30,C6CN(C1ccccc1)C(=O)c1cc2(NC(=+Cc2cccc(c2)n(C)n1
remove c from position 9,C6CN(C1cccc1)C(=O)c1cc2(NC(=+Cc2cccc(c2)n(C)n1
add + at position 45,C6CN(C1cccc1)C(=O)c1cc2(NC(=+Cc2cccc(c2)n(C)n+1
replace c at position 32 with B,C6CN(C1cccc1)C(=O)c1cc2(NC(=+Cc2Bccc(c2)n(C)n+1
replace = at position 15 with ),C6CN(C1cccc1)C()O)c1cc2(NC(=+Cc2Bccc(c2)n(C)n+1
remove C from position 29,C6CN(C1cccc1)C()O)c1cc2(NC(=+c2Bccc(c2)n(C)n+1
add ) at position 43,C6CN(C1cccc1)C()O)c1cc2(NC(=+c2Bccc(c2)n(C))n+1
remove C from position 2,C6N(C1cccc1)C()O)c1cc2(NC(=+c2Bccc(c2)n(C))n+1
remove c from position 35,C6N(C1cccc1)C()O)c1cc2(NC(=+c2Bccc(2)n(C))n+1
add B at position 19,C6N(C1cccc1)C()O)c1Bcc2(NC(=+c2Bccc(2)n(C))n+1
replace c at position 17 with 2,C6N(C1cccc1)C()O)21Bcc2(NC(=+c2Bccc(2)n(C))n+1
remove C from position 12,C6N(C1cccc1)()O)21Bcc2(NC(=+c2Bccc(2)n(C))n+1
remove c from position 33,C6N(C1cccc1)()O)21Bcc2(NC(=+c2Bcc(2)n(C))n+1
remove c from position 32,C6N(C1cccc1)()O)21Bcc2(NC(=+c2Bc(2)n(C))n+1
add - at position 21,C6N(C1cccc1)()O)21Bcc-2(NC(=+c2Bc(2)n(C))n+1
remove + from position 42,C6N(C1cccc1)()O)21Bcc-2(NC(=+c2Bc(2)n(C))n1
remove ( from position 3,C6NC1cccc1)()O)21Bcc-2(NC(=+c2Bc(2)n(C))n1
add / at position 31,C6NC1cccc1)()O)21Bcc-2(NC(=+c2B/c(2)n(C))n1
add - at position 13,C6NC1cccc1)()-O)21Bcc-2(NC(=+c2B/c(2)n(C))n1
add ] at position 22,C6NC1cccc1)()-O)21Bcc-]2(NC(=+c2B/c(2)n(C))n1
add - at position 33,C6NC1cccc1)()-O)21Bcc-]2(NC(=+c2B-/c(2)n(C))n1
remove + from position 29,C6NC1cccc1)()-O)21Bcc-]2(NC(=c2B-/c(2)n(C))n1
add O at position 17,C6NC1cccc1)()-O)2O1Bcc-]2(NC(=c2B-/c(2)n(C))n1
replace ( at position 11 with 4,C6NC1cccc1)4)-O)2O1Bcc-]2(NC(=c2B-/c(2)n(C))n1
add ] at position 9,C6NC1cccc]1)4)-O)2O1Bcc-]2(NC(=c2B-/c(2)n(C))n1
replace N at position 2 with H,C6HC1cccc]1)4)-O)2O1Bcc-]2(NC(=c2B-/c(2)n(C))n1
remove c from position 22,C6HC1cccc]1)4)-O)2O1Bc-]2(NC(=c2B-/c(2)n(C))n1
remove 2 from position 37,C6HC1cccc]1)4)-O)2O1Bc-]2(NC(=c2B-/c()n(C))n1
replace c at position 30 with (,C6HC1cccc]1)4)-O)2O1Bc-]2(NC(=(2B-/c()n(C))n1
replace ( at position 30 with o,C6HC1cccc]1)4)-O)2O1Bc-]2(NC(=o2B-/c()n(C))n1
replace 1 at position 10 with B,C6HC1cccc]B)4)-O)2O1Bc-]2(NC(=o2B-/c()n(C))n1
remove - from position 33,C6HC1cccc]B)4)-O)2O1Bc-]2(NC(=o2B/c()n(C))n1
replace O at position 18 with 3,C6HC1cccc]B)4)-O)231Bc-]2(NC(=o2B/c()n(C))n1
add = at position 11,C6HC1cccc]B=)4)-O)231Bc-]2(NC(=o2B/c()n(C))n1
remove c from position 7,C6HC1ccc]B=)4)-O)231Bc-]2(NC(=o2B/c()n(C))n1
replace c at position 6 with n,C6HC1cnc]B=)4)-O)231Bc-]2(NC(=o2B/c()n(C))n1
replace 2 at position 24 with n,C6HC1cnc]B=)4)-O)231Bc-]n(NC(=o2B/c()n(C))n1
replace ) at position 16 with O,C6HC1cnc]B=)4)-OO231Bc-]n(NC(=o2B/c()n(C))n1
add S at position 6,C6HC1cSnc]B=)4)-OO231Bc-]n(NC(=o2B/c()n(C))n1
remove B from position 10,C6HC1cSnc]=)4)-OO231Bc-]n(NC(=o2B/c()n(C))n1
remove 2 from position 17,C6HC1cSnc]=)4)-OO31Bc-]n(NC(=o2B/c()n(C))n1
replace c at position 20 with l,C6HC1cSnc]=)4)-OO31Bl-]n(NC(=o2B/c()n(C))n1
replace 1 at position 4 with ),C6HC)cSnc]=)4)-OO31Bl-]n(NC(=o2B/c()n(C))n1
remove C from position 3,C6H)cSnc]=)4)-OO31Bl-]n(NC(=o2B/c()n(C))n1
replace H at position 2 with 2,C62)cSnc]=)4)-OO31Bl-]n(NC(=o2B/c()n(C))n1
remove S from position 5,C62)cnc]=)4)-OO31Bl-]n(NC(=o2B/c()n(C))n1
replace ] at position 20 with 4,C62)cnc]=)4)-OO31Bl-4n(NC(=o2B/c()n(C))n1
replace c at position 4 with H,C62)Hnc]=)4)-OO31Bl-4n(NC(=o2B/c()n(C))n1
remove H from position 4,C62)nc]=)4)-OO31Bl-4n(NC(=o2B/c()n(C))n1
remove O from position 12,C62)nc]=)4)-O31Bl-4n(NC(=o2B/c()n(C))n1
add S at position 12,C62)nc]=)4)-SO31Bl-4n(NC(=o2B/c()n(C))n1
replace / at position 29 with F,C62)nc]=)4)-SO31Bl-4n(NC(=o2BFc()n(C))n1
add r at position 23,C62)nc]=)4)-SO31Bl-4n(NrC(=o2BFc()n(C))n1
replace ( at position 32 with r,C62)nc]=)4)-SO31Bl-4n(NrC(=o2BFcr)n(C))n1
remove - from position 11,C62)nc]=)4)SO31Bl-4n(NrC(=o2BFcr)n(C))n1
replace 3 at position 13 with 5,C62)nc]=)4)SO51Bl-4n(NrC(=o2BFcr)n(C))n1
add F at position 22,C62)nc]=)4)SO51Bl-4n(NFrC(=o2BFcr)n(C))n1
add B at position 35,C62)nc]=)4)SO51Bl-4n(NFrC(=o2BFcr)nB(C))n1
remove ) from position 8,C62)nc]=4)SO51Bl-4n(NFrC(=o2BFcr)nB(C))n1
add r at position 10,C62)nc]=4)rSO51Bl-4n(NFrC(=o2BFcr)nB(C))n1
add ) at position 34,C62)nc]=4)rSO51Bl-4n(NFrC(=o2BFcr))nB(C))n1
remove r from position 10,C62)nc]=4)SO51Bl-4n(NFrC(=o2BFcr))nB(C))n1
add N at position 3,C62N)nc]=4)SO51Bl-4n(NFrC(=o2BFcr))nB(C))n1
add r at position 9,C62N)nc]=r4)SO51Bl-4n(NFrC(=o2BFcr))nB(C))n1
add 7 at position 27,C62N)nc]=r4)SO51Bl-4n(NFrC(7=o2BFcr))nB(C))n1
remove O from position 13,C62N)nc]=r4)S51Bl-4n(NFrC(7=o2BFcr))nB(C))n1
replace 1 at position 43 with =,C62N)nc]=r4)S51Bl-4n(NFrC(7=o2BFcr))nB(C))n=
replace - at position 17 with H,C62N)nc]=r4)S51BlH4n(NFrC(7=o2BFcr))nB(C))n=
remove n from position 19,C62N)nc]=r4)S51BlH4(NFrC(7=o2BFcr))nB(C))n=
remove B from position 36,C62N)nc]=r4)S51BlH4(NFrC(7=o2BFcr))n(C))n=
replace ) at position 34 with +,C62N)nc]=r4)S51BlH4(NFrC(7=o2BFcr)+n(C))n=
replace H at position 17 with =,C62N)nc]=r4)S51Bl=4(NFrC(7=o2BFcr)+n(C))n=
remove ( from position 36,C62N)nc]=r4)S51Bl=4(NFrC(7=o2BFcr)+nC))n=
remove ] from position 7,C62N)nc=r4)S51Bl=4(NFrC(7=o2BFcr)+nC))n=
remove 4 from position 17,C62N)nc=r4)S51Bl=(NFrC(7=o2BFcr)+nC))n=
add 7 at position 37,C62N)nc=r4)S51Bl=(NFrC(7=o2BFcr)+nC))7n=
add 2 at position 28,C62N)nc=r4)S51Bl=(NFrC(7=o2B2Fcr)+nC))7n=
replace ) at position 10 with =,C62N)nc=r4=S51Bl=(NFrC(7=o2B2Fcr)+nC))7n=
remove ) from position 4,C62Nnc=r4=S51Bl=(NFrC(7=o2B2Fcr)+nC))7n=
remove r from position 19,C62Nnc=r4=S51Bl=(NFC(7=o2B2Fcr)+nC))7n=
remove ) from position 30,C62Nnc=r4=S51Bl=(NFC(7=o2B2Fcr+nC))7n=
add / at position 30,C62Nnc=r4=S51Bl=(NFC(7=o2B2Fcr/+nC))7n=
remove o from position 23,C62Nnc=r4=S51Bl=(NFC(7=2B2Fcr/+nC))7n=
remove = from position 22,C62Nnc=r4=S51Bl=(NFC(72B2Fcr/+nC))7n=
add r at position 33,C62Nnc=r4=S51Bl=(NFC(72B2Fcr/+nC)r)7n=
remove n from position 30,C62Nnc=r4=S51Bl=(NFC(72B2Fcr/+C)r)7n=
remove + from position 29,C62Nnc=r4=S51Bl=(NFC(72B2Fcr/C)r)7n=
remove = from position 6,C62Nncr4=S51Bl=(NFC(72B2Fcr/C)r)7n=
add ) at position 30,C62Nncr4=S51Bl=(NFC(72B2Fcr/C))r)7n=
replace 2 at position 21 with 7,C62Nncr4=S51Bl=(NFC(77B2Fcr/C))r)7n=
replace C at position 18 with 4,C62Nncr4=S51Bl=(NF4(77B2Fcr/C))r)7n=
remove 7 from position 21,C62Nncr4=S51Bl=(NF4(7B2Fcr/C))r)7n=
remove 4 from position 18,C62Nncr4=S51Bl=(NF(7B2Fcr/C))r)7n=
remove n from position 32,C62Nncr4=S51Bl=(NF(7B2Fcr/C))r)7=
replace F at position 17 with ),C62Nncr4=S51Bl=(N)(7B2Fcr/C))r)7=
remove l from position 13,C62Nncr4=S51B=(N)(7B2Fcr/C))r)7=
remove F from position 21,C62Nncr4=S51B=(N)(7B2cr/C))r)7=
replace ( at position 14 with 4,C62Nncr4=S51B=4N)(7B2cr/C))r)7=
add l at position 7,C62Nncrl4=S51B=4N)(7B2cr/C))r)7=
add - at position 23,C62Nncrl4=S51B=4N)(7B2c-r/C))r)7=
replace / at position 25 with (,C62Nncrl4=S51B=4N)(7B2c-r(C))r)7=
remove 2 from position 2,C6Nncrl4=S51B=4N)(7B2c-r(C))r)7=
replace S at position 9 with 5,C6Nncrl4=551B=4N)(7B2c-r(C))r)7=
remove ) from position 27,C6Nncrl4=551B=4N)(7B2c-r(C)r)7=
replace 7 at position 29 with ),C6Nncrl4=551B=4N)(7B2c-r(C)r))=
replace = at position 30 with 4,C6Nncrl4=551B=4N)(7B2c-r(C)r))4
remove N from position 15,C6Nncrl4=551B=4)(7B2c-r(C)r))4
replace 4 at position 7 with 7,C6Nncrl7=551B=4)(7B2c-r(C)r))4
remove c from position 20,C6Nncrl7=551B=4)(7B2-r(C)r))4
replace C at position 0 with S,S6Nncrl7=551B=4)(7B2-r(C)r))4
remove ) from position 27,S6Nncrl7=551B=4)(7B2-r(C)r)4
remove r from position 5,S6Nncl7=551B=4)(7B2-r(C)r)4
remove ( from position 21,S6Nncl7=551B=4)(7B2-rC)r)4
replace c at position 4 with r,S6Nnrl7=551B=4)(7B2-rC)r)4
remove - from position 19,S6Nnrl7=551B=4)(7B2rC)r)4
add s at position 17,S6Nnrl7=551B=4)(7sB2rC)r)4
replace ) at position 22 with 1,S6Nnrl7=551B=4)(7sB2rC1r)4
replace ) at position 24 with F,S6Nnrl7=551B=4)(7sB2rC1rF4
remove = from position 12,S6Nnrl7=551B4)(7sB2rC1rF4
replace 1 at position 10 with -,S6Nnrl7=55-B4)(7sB2rC1rF4
remove B from position 17,S6Nnrl7=55-B4)(7s2rC1rF4
remove C from position 19,S6Nnrl7=55-B4)(7s2r1rF4
remove ( from position 14,S6Nnrl7=55-B4)7s2r1rF4
add r at position 2,S6rNnrl7=55-B4)7s2r1rF4
remove F from position 21,S6rNnrl7=55-B4)7s2r1r4
remove 6 from position 1,SrNnrl7=55-B4)7s2r1r4
remove N from position 2,Srnrl7=55-B4)7s2r1r4
remove 4 from position 19,Srnrl7=55-B4)7s2r1r
replace 4 at position 11 with F,Srnrl7=55-BF)7s2r1r
remove ) from position 12,Srnrl7=55-BF7s2r1r
remove r from position 1,Snrl7=55-BF7s2r1r
add l at position 2,Snlrl7=55-BF7s2r1r
remove B from position 10,Snlrl7=55-F7s2r1r
add S at position 7,Snlrl7=S55-F7s2r1r
replace S at position 0 with o,onlrl7=S55-F7s2r1r
remove F from position 11,onlrl7=S55-7s2r1r
remove l from position 2,onrl7=S55-7s2r1r
remove S from position 6,onrl7=55-7s2r1r
replace 1 at position 13 with N,onrl7=55-7s2rNr
remove 2 from position 11,onrl7=55-7srNr
add O at position 2,onOrl7=55-7srNr
replace 7 at position 10 with ],onOrl7=55-]srNr
add 6 at position 14,onOrl7=55-]srN6r
remove s from position 11,onOrl7=55-]rN6r
remove = from position 6,onOrl755-]rN6r
replace r at position 3 with 4,onO4l755-]rN6r
add H at position 5,onO4lH755-]rN6r
replace n at position 1 with F,oFO4lH755-]rN6r
remove 6 from position 13,oFO4lH755-]rNr
add H at position 9,oFO4lH755H-]rNr
remove O from position 2,oF4lH755H-]rNr
remove 7 from position 5,oF4lH55H-]rNr
replace H at position 4 with [,oF4l[55H-]rNr
remove 4 from position 2,oFl[55H-]rNr
remove l from position 2,oF[55H-]rNr
replace r at position 8 with S,oF[55H-]SNr
remove 5 from position 3,oF[5H-]SNr
remove [ from position 2,oF5H-]SNr
remove S from position 6,oF5H-]Nr
remove r from position 7,oF5H-]N
replace - at position 4 with B,oF5HB]N
remove N from position 6,oF5HB]
remove B from position 4,oF5H]
add C at position 5,oF5H]C
replace ] at position 4 with -,oF5H-C
add B at position 3,oF5BH-C
add c at position 2,oFc5BH-C
replace 5 at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
