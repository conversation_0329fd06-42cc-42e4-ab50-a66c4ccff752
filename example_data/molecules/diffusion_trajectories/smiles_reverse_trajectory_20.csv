log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove C from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add S at position 6,oH3H-]SN)
add [ at position 2,oH[3H-]SN)
add 3 at position 3,oH[33H-]SN)
replace S at position 8 with r,oH[33H-]rN)
add n at position 2,oHn[33H-]rN)
add 4 at position 2,oH4n[33H-]rN)
replace [ at position 4 with H,oH4nH33H-]rN)
add C at position 5,oH4nHC33H-]rN)
add O at position 2,oHO4nHC33H-]rN)
remove H from position 9,oHO4nHC33-]rN)
add 6 at position 13,oHO4nHC33-]rN6)
replace H at position 1 with 6,o6O4nHC33-]rN6)
remove H from position 5,o6O4nC33-]rN6)
replace 4 at position 3 with r,o6OrnC33-]rN6)
add r at position 6,o6OrnCr33-]rN6)
add s at position 11,o6OrnCr33-]srN6)
remove 6 from position 14,o6OrnCr33-]srN)
replace ] at position 10 with H,o6OrnCr33-HsrN)
remove O from position 2,o6rnCr33-HsrN)
add 7 at position 11,o6rnCr33-Hs7rN)
replace N at position 13 with /,o6rnCr33-Hs7r/)
add S at position 6,o6rnCrS33-Hs7r/)
add l at position 2,o6lrnCrS33-Hs7r/)
add F at position 11,o6lrnCrS33-FHs7r/)
replace o at position 0 with S,S6lrnCrS33-FHs7r/)
remove S from position 7,S6lrnCr33-FHs7r/)
add B at position 10,S6lrnCr33-BFHs7r/)
remove l from position 2,S6rnCr33-BFHs7r/)
add r at position 1,Sr6rnCr33-BFHs7r/)
add ) at position 12,Sr6rnCr33-BF)Hs7r/)
replace F at position 11 with 3,Sr6rnCr33-B3)Hs7r/)
add ) at position 19,Sr6rnCr33-B3)Hs7r/))
add 1 at position 2,Sr16rnCr33-B3)Hs7r/))
add C at position 1,SCr16rnCr33-B3)Hs7r/))
add C at position 21,SCr16rnCr33-B3)Hs7r/)C)
remove r from position 2,SC16rnCr33-B3)Hs7r/)C)
add B at position 14,SC16rnCr33-B3)BHs7r/)C)
add C at position 19,SC16rnCr33-B3)BHs7rC/)C)
add ( at position 17,SC16rnCr33-B3)BHs(7rC/)C)
replace - at position 10 with 6,SC16rnCr336B3)BHs(7rC/)C)
add C at position 12,SC16rnCr336BC3)BHs(7rC/)C)
replace C at position 24 with r,SC16rnCr336BC3)BHs(7rC/)r)
replace / at position 22 with C,SC16rnCr336BC3)BHs(7rCC)r)
remove s from position 17,SC16rnCr336BC3)BH(7rCC)r)
add F at position 19,SC16rnCr336BC3)BH(7FrCC)r)
replace r at position 4 with 2,SC162nCr336BC3)BH(7FrCC)r)
add / at position 21,SC162nCr336BC3)BH(7Fr/CC)r)
remove B from position 15,SC162nCr336BC3)H(7Fr/CC)r)
add 2 at position 18,SC162nCr336BC3)H(72Fr/CC)r)
replace S at position 0 with 7,7C162nCr336BC3)H(72Fr/CC)r)
add ( at position 20,7C162nCr336BC3)H(72F(r/CC)r)
add N at position 11,7C162nCr336NBC3)H(72F(r/CC)r)
add ) at position 29,7C162nCr336NBC3)H(72F(r/CC)r))
replace 3 at position 9 with =,7C162nCr3=6NBC3)H(72F(r/CC)r))
replace ) at position 29 with 7,7C162nCr3=6NBC3)H(72F(r/CC)r)7
add = at position 13,7C162nCr3=6NB=C3)H(72F(r/CC)r)7
add B at position 20,7C162nCr3=6NB=C3)H(7B2F(r/CC)r)7
remove 1 from position 2,7C62nCr3=6NB=C3)H(7B2F(r/CC)r)7
remove 7 from position 0,C62nCr3=6NB=C3)H(7B2F(r/CC)r)7
replace 6 at position 8 with S,C62nCr3=SNB=C3)H(7B2F(r/CC)r)7
add N at position 3,C62NnCr3=SNB=C3)H(7B2F(r/CC)r)7
remove H from position 16,C62NnCr3=SNB=C3)(7B2F(r/CC)r)7
replace 3 at position 14 with C,C62NnCr3=SNB=CC)(7B2F(r/CC)r)7
add 5 at position 10,C62NnCr3=S5NB=CC)(7B2F(r/CC)r)7
add l at position 13,C62NnCr3=S5NBl=CC)(7B2F(r/CC)r)7
replace ) at position 17 with F,C62NnCr3=S5NBl=CCF(7B2F(r/CC)r)7
add 1 at position 32,C62NnCr3=S5NBl=CCF(7B2F(r/CC)r)71
add 4 at position 18,C62NnCr3=S5NBl=CCF4(7B2F(r/CC)r)71
add 6 at position 21,C62NnCr3=S5NBl=CCF4(76B2F(r/CC)r)71
replace 4 at position 18 with s,C62NnCr3=S5NBl=CCFs(76B2F(r/CC)r)71
replace 6 at position 21 with O,C62NnCr3=S5NBl=CCFs(7OB2F(r/CC)r)71
remove ) from position 30,C62NnCr3=S5NBl=CCFs(7OB2F(r/CCr)71
add = at position 6,C62NnC=r3=S5NBl=CCFs(7OB2F(r/CCr)71
add + at position 29,C62NnC=r3=S5NBl=CCFs(7OB2F(r/+CCr)71
add C at position 30,C62NnC=r3=S5NBl=CCFs(7OB2F(r/+CCCr)71
remove r from position 33,C62NnC=r3=S5NBl=CCFs(7OB2F(r/+CCC)71
add C at position 22,C62NnC=r3=S5NBl=CCFs(7COB2F(r/+CCC)71
add o at position 23,C62NnC=r3=S5NBl=CCFs(7CoOB2F(r/+CCC)71
remove / from position 30,C62NnC=r3=S5NBl=CCFs(7CoOB2F(r+CCC)71
add ) at position 30,C62NnC=r3=S5NBl=CCFs(7CoOB2F(r)+CCC)71
add r at position 19,C62NnC=r3=S5NBl=CCFrs(7CoOB2F(r)+CCC)71
add ) at position 4,C62N)nC=r3=S5NBl=CCFrs(7CoOB2F(r)+CCC)71
replace = at position 10 with +,C62N)nC=r3+S5NBl=CCFrs(7CoOB2F(r)+CCC)71
remove 2 from position 28,C62N)nC=r3+S5NBl=CCFrs(7CoOBF(r)+CCC)71
remove 7 from position 37,C62N)nC=r3+S5NBl=CCFrs(7CoOBF(r)+CCC)1
add 4 at position 17,C62N)nC=r3+S5NBl=4CCFrs(7CoOBF(r)+CCC)1
add ] at position 7,C62N)nC]=r3+S5NBl=4CCFrs(7CoOBF(r)+CCC)1
add ) at position 36,C62N)nC]=r3+S5NBl=4CCFrs(7CoOBF(r)+C)CC)1
replace = at position 17 with H,C62N)nC]=r3+S5NBlH4CCFrs(7CoOBF(r)+C)CC)1
replace + at position 34 with ),C62N)nC]=r3+S5NBlH4CCFrs(7CoOBF(r))C)CC)1
add B at position 36,C62N)nC]=r3+S5NBlH4CCFrs(7CoOBF(r))CB)CC)1
add n at position 19,C62N)nC]=r3+S5NBlH4nCCFrs(7CoOBF(r))CB)CC)1
replace H at position 17 with -,C62N)nC]=r3+S5NBl-4nCCFrs(7CoOBF(r))CB)CC)1
replace s at position 24 with N,C62N)nC]=r3+S5NBl-4nCCFrN(7CoOBF(r))CB)CC)1
add N at position 13,C62N)nC]=r3+SN5NBl-4nCCFrN(7CoOBF(r))CB)CC)1
remove 7 from position 27,C62N)nC]=r3+SN5NBl-4nCCFrN(CoOBF(r))CB)CC)1
remove r from position 9,C62N)nC]=3+SN5NBl-4nCCFrN(CoOBF(r))CB)CC)1
remove N from position 3,C62)nC]=3+SN5NBl-4nCCFrN(CoOBF(r))CB)CC)1
add r at position 10,C62)nC]=3+rSN5NBl-4nCCFrN(CoOBF(r))CB)CC)1
remove ) from position 34,C62)nC]=3+rSN5NBl-4nCCFrN(CoOBF(r)CB)CC)1
remove r from position 10,C62)nC]=3+SN5NBl-4nCCFrN(CoOBF(r)CB)CC)1
add N at position 8,C62)nC]=N3+SN5NBl-4nCCFrN(CoOBF(r)CB)CC)1
remove B from position 35,C62)nC]=N3+SN5NBl-4nCCFrN(CoOBF(r)C)CC)1
remove F from position 22,C62)nC]=N3+SN5NBl-4nCCrN(CoOBF(r)C)CC)1
replace 5 at position 13 with 3,C62)nC]=N3+SN3NBl-4nCCrN(CoOBF(r)C)CC)1
add - at position 11,C62)nC]=N3+-SN3NBl-4nCCrN(CoOBF(r)C)CC)1
replace r at position 32 with (,C62)nC]=N3+-SN3NBl-4nCCrN(CoOBF(()C)CC)1
remove r from position 23,C62)nC]=N3+-SN3NBl-4nCCN(CoOBF(()C)CC)1
replace F at position 29 with /,C62)nC]=N3+-SN3NBl-4nCCN(CoOB/(()C)CC)1
remove S from position 12,C62)nC]=N3+-N3NBl-4nCCN(CoOB/(()C)CC)1
add = at position 12,C62)nC]=N3+-=N3NBl-4nCCN(CoOB/(()C)CC)1
add H at position 4,C62)HnC]=N3+-=N3NBl-4nCCN(CoOB/(()C)CC)1
replace H at position 4 with ],C62)]nC]=N3+-=N3NBl-4nCCN(CoOB/(()C)CC)1
replace 4 at position 20 with ],C62)]nC]=N3+-=N3NBl-]nCCN(CoOB/(()C)CC)1
add S at position 5,C62)]SnC]=N3+-=N3NBl-]nCCN(CoOB/(()C)CC)1
replace 2 at position 2 with H,C6H)]SnC]=N3+-=N3NBl-]nCCN(CoOB/(()C)CC)1
add @ at position 3,C6H@)]SnC]=N3+-=N3NBl-]nCCN(CoOB/(()C)CC)1
replace ) at position 4 with H,C6H@H]SnC]=N3+-=N3NBl-]nCCN(CoOB/(()C)CC)1
replace l at position 20 with C,C6H@H]SnC]=N3+-=N3NBC-]nCCN(CoOB/(()C)CC)1
add 3 at position 17,C6H@H]SnC]=N3+-=N33NBC-]nCCN(CoOB/(()C)CC)1
add @ at position 10,C6H@H]SnC]@=N3+-=N33NBC-]nCCN(CoOB/(()C)CC)1
remove S from position 6,C6H@H]nC]@=N3+-=N33NBC-]nCCN(CoOB/(()C)CC)1
replace N at position 16 with O,C6H@H]nC]@=N3+-=O33NBC-]nCCN(CoOB/(()C)CC)1
replace n at position 24 with 2,C6H@H]nC]@=N3+-=O33NBC-]2CCN(CoOB/(()C)CC)1
replace n at position 6 with 1,C6H@H]1C]@=N3+-=O33NBC-]2CCN(CoOB/(()C)CC)1
add C at position 7,C6H@H]1CC]@=N3+-=O33NBC-]2CCN(CoOB/(()C)CC)1
remove = from position 11,C6H@H]1CC]@N3+-=O33NBC-]2CCN(CoOB/(()C)CC)1
replace 3 at position 18 with O,C6H@H]1CC]@N3+-=O3ONBC-]2CCN(CoOB/(()C)CC)1
add - at position 33,C6H@H]1CC]@N3+-=O3ONBC-]2CCN(CoOB-/(()C)CC)1
replace @ at position 10 with C,C6H@H]1CC]CN3+-=O3ONBC-]2CCN(CoOB-/(()C)CC)1
replace o at position 30 with (,C6H@H]1CC]CN3+-=O3ONBC-]2CCN(C(OB-/(()C)CC)1
replace ( at position 30 with ),C6H@H]1CC]CN3+-=O3ONBC-]2CCN(C)OB-/(()C)CC)1
add C at position 37,C6H@H]1CC]CN3+-=O3ONBC-]2CCN(C)OB-/((C)C)CC)1
add 1 at position 22,C6H@H]1CC]CN3+-=O3ONBC1-]2CCN(C)OB-/((C)C)CC)1
replace H at position 2 with [,C6[@H]1CC]CN3+-=O3ONBC1-]2CCN(C)OB-/((C)C)CC)1
remove ] from position 9,C6[@H]1CCCN3+-=O3ONBC1-]2CCN(C)OB-/((C)C)CC)1
replace 3 at position 11 with C,C6[@H]1CCCNC+-=O3ONBC1-]2CCN(C)OB-/((C)C)CC)1
remove O from position 17,C6[@H]1CCCNC+-=O3NBC1-]2CCN(C)OB-/((C)C)CC)1
add - at position 29,C6[@H]1CCCNC+-=O3NBC1-]2CCN(C-)OB-/((C)C)CC)1
remove - from position 33,C6[@H]1CCCNC+-=O3NBC1-]2CCN(C-)OB/((C)C)CC)1
remove ] from position 22,C6[@H]1CCCNC+-=O3NBC1-2CCN(C-)OB/((C)C)CC)1
remove - from position 13,C6[@H]1CCCNC+=O3NBC1-2CCN(C-)OB/((C)C)CC)1
remove / from position 31,C6[@H]1CCCNC+=O3NBC1-2CCN(C-)OB((C)C)CC)1
add C at position 3,C6[C@H]1CCCNC+=O3NBC1-2CCN(C-)OB((C)C)CC)1
add + at position 42,C6[C@H]1CCCNC+=O3NBC1-2CCN(C-)OB((C)C)CC)1+
remove - from position 21,C6[C@H]1CCCNC+=O3NBC12CCN(C-)OB((C)C)CC)1+
add C at position 32,C6[C@H]1CCCNC+=O3NBC12CCN(C-)OB(C(C)C)CC)1+
add ) at position 33,C6[C@H]1CCCNC+=O3NBC12CCN(C-)OB(C)(C)C)CC)1+
add 1 at position 12,C6[C@H]1CCCN1C+=O3NBC12CCN(C-)OB(C)(C)C)CC)1+
replace 3 at position 17 with ),C6[C@H]1CCCN1C+=O)NBC12CCN(C-)OB(C)(C)C)CC)1+
remove B from position 19,C6[C@H]1CCCN1C+=O)NC12CCN(C-)OB(C)(C)C)CC)1+
add ( at position 35,C6[C@H]1CCCN1C+=O)NC12CCN(C-)OB(C)((C)C)CC)1+
add C at position 2,C6C[C@H]1CCCN1C+=O)NC12CCN(C-)OB(C)((C)C)CC)1+
remove ) from position 43,C6C[C@H]1CCCN1C+=O)NC12CCN(C-)OB(C)((C)C)CC1+
add O at position 29,C6C[C@H]1CCCN1C+=O)NC12CCN(C-O)OB(C)((C)C)CC1+
replace + at position 15 with (,C6C[C@H]1CCCN1C(=O)NC12CCN(C-O)OB(C)((C)C)CC1+
replace B at position 32 with C,C6C[C@H]1CCCN1C(=O)NC12CCN(C-O)OC(C)((C)C)CC1+
remove + from position 45,C6C[C@H]1CCCN1C(=O)NC12CCN(C-O)OC(C)((C)C)CC1
add C at position 9,C6C[C@H]1CCCCN1C(=O)NC12CCN(C-O)OC(C)((C)C)CC1
add = at position 30,C6C[C@H]1CCCCN1C(=O)NC12CCN(C-=O)OC(C)((C)C)CC1
remove 6 from position 1,CC[C@H]1CCCCN1C(=O)NC12CCN(C-=O)OC(C)((C)C)CC1
remove 2 from position 22,CC[C@H]1CCCCN1C(=O)NC1CCN(C-=O)OC(C)((C)C)CC1
add @ at position 5,CC[C@@H]1CCCCN1C(=O)NC1CCN(C-=O)OC(C)((C)C)CC1
remove ( from position 37,CC[C@@H]1CCCCN1C(=O)NC1CCN(C-=O)OC(C)(C)C)CC1
replace - at position 28 with (,CC[C@@H]1CCCCN1C(=O)NC1CCN(C(=O)OC(C)(C)C)CC1
final: CC[C@@H]1CCCCN1C(=O)NC1CCN(C(=O)OC(C)(C)C)CC1,CC[C@@H]1CCCCN1C(=O)NC1CCN(C(=O)OC(C)(C)C)CC1
