log,state
initialize: COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc2c1,COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc2c1
replace C at position 28 with +,COc1ccc2cc(COC(=O)COc3ccccc3+#N)ccc2c1
add ( at position 37,COc1ccc2cc(COC(=O)COc3ccccc3+#N)ccc2c(1
remove c from position 5,COc1cc2cc(COC(=O)COc3ccccc3+#N)ccc2c(1
add 2 at position 22,COc1cc2cc(COC(=O)COc3c2cccc3+#N)ccc2c(1
add 6 at position 1,C6Oc1cc2cc(COC(=O)COc3c2cccc3+#N)ccc2c(1
remove # from position 30,C6Oc1cc2cc(COC(=O)COc3c2cccc3+N)ccc2c(1
remove c from position 9,C6Oc1cc2c(COC(=O)COc3c2cccc3+N)ccc2c(1
add n at position 7,C6Oc1ccn2c(COC(=O)COc3c2cccc3+N)ccc2c(1
add o at position 31,C6Oc1ccn2c(COC(=O)COc3c2cccc3+No)ccc2c(1
replace 1 at position 4 with S,C6OcSccn2c(COC(=O)COc3c2cccc3+No)ccc2c(1
remove + from position 29,C6OcSccn2c(COC(=O)COc3c2cccc3No)ccc2c(1
add C at position 5,C6OcSCccn2c(COC(=O)COc3c2cccc3No)ccc2c(1
remove O from position 17,C6OcSCccn2c(COC(=)COc3c2cccc3No)ccc2c(1
replace S at position 4 with N,C6OcNCccn2c(COC(=)COc3c2cccc3No)ccc2c(1
remove c from position 22,C6OcNCccn2c(COC(=)COc32cccc3No)ccc2c(1
remove C from position 12,C6OcNCccn2c(OC(=)COc32cccc3No)ccc2c(1
remove 2 from position 33,C6OcNCccn2c(OC(=)COc32cccc3No)cccc(1
remove c from position 32,C6OcNCccn2c(OC(=)COc32cccc3No)ccc(1
add - at position 21,C6OcNCccn2c(OC(=)COc3-2cccc3No)ccc(1
remove c from position 24,C6OcNCccn2c(OC(=)COc3-2ccc3No)ccc(1
add H at position 4,C6OcHNCccn2c(OC(=)COc3-2ccc3No)ccc(1
remove H from position 4,C6OcNCccn2c(OC(=)COc3-2ccc3No)ccc(1
remove 2 from position 9,C6OcNCccnc(OC(=)COc3-2ccc3No)ccc(1
add ] at position 22,C6OcNCccnc(OC(=)COc3-2]ccc3No)ccc(1
add - at position 33,C6OcNCccnc(OC(=)COc3-2]ccc3No)ccc-(1
remove ) from position 29,C6OcNCccnc(OC(=)COc3-2]ccc3Noccc-(1
add O at position 17,C6OcNCccnc(OC(=)COOc3-2]ccc3Noccc-(1
replace O at position 11 with 3,C6OcNCccnc(3C(=)COOc3-2]ccc3Noccc-(1
add ] at position 9,C6OcNCccn]c(3C(=)COOc3-2]ccc3Noccc-(1
replace O at position 2 with H,C6HcNCccn]c(3C(=)COOc3-2]ccc3Noccc-(1
remove - from position 22,C6HcNCccn]c(3C(=)COOc32]ccc3Noccc-(1
remove o from position 29,C6HcNCccn]c(3C(=)COOc32]ccc3Nccc-(1
remove c from position 30,C6HcNCccn]c(3C(=)COOc32]ccc3Ncc-(1
replace c at position 10 with @,C6HcNCccn]@(3C(=)COOc32]ccc3Ncc-(1
remove 1 from position 33,C6HcNCccn]@(3C(=)COOc32]ccc3Ncc-(
replace O at position 18 with 3,C6HcNCccn]@(3C(=)C3Oc32]ccc3Ncc-(
add = at position 11,C6HcNCccn]@=(3C(=)C3Oc32]ccc3Ncc-(
remove c from position 7,C6HcNCcn]@=(3C(=)C3Oc32]ccc3Ncc-(
replace c at position 6 with n,C6HcNCnn]@=(3C(=)C3Oc32]ccc3Ncc-(
replace c at position 24 with n,C6HcNCnn]@=(3C(=)C3Oc32]ncc3Ncc-(
replace ) at position 16 with O,C6HcNCnn]@=(3C(=OC3Oc32]ncc3Ncc-(
add S at position 6,C6HcNCSnn]@=(3C(=OC3Oc32]ncc3Ncc-(
remove @ from position 10,C6HcNCSnn]=(3C(=OC3Oc32]ncc3Ncc-(
remove C from position 17,C6HcNCSnn]=(3C(=O3Oc32]ncc3Ncc-(
replace 3 at position 20 with l,C6HcNCSnn]=(3C(=O3Ocl2]ncc3Ncc-(
replace N at position 4 with ),C6Hc)CSnn]=(3C(=O3Ocl2]ncc3Ncc-(
remove c from position 3,C6H)CSnn]=(3C(=O3Ocl2]ncc3Ncc-(
replace 3 at position 25 with (,C6H)CSnn]=(3C(=O3Ocl2]ncc(Ncc-(
add c at position 5,C6H)CcSnn]=(3C(=O3Ocl2]ncc(Ncc-(
replace l at position 20 with 4,C6H)CcSnn]=(3C(=O3Oc42]ncc(Ncc-(
replace C at position 4 with I,C6H)IcSnn]=(3C(=O3Oc42]ncc(Ncc-(
remove I from position 4,C6H)cSnn]=(3C(=O3Oc42]ncc(Ncc-(
remove ( from position 30,C6H)cSnn]=(3C(=O3Oc42]ncc(Ncc-
add 1 at position 12,C6H)cSnn]=(31C(=O3Oc42]ncc(Ncc-
remove c from position 28,C6H)cSnn]=(31C(=O3Oc42]ncc(Nc-
remove n from position 7,C6H)cSn]=(31C(=O3Oc42]ncc(Nc-
replace 1 at position 11 with s,C6H)cSn]=(3sC(=O3Oc42]ncc(Nc-
replace 3 at position 16 with r,C6H)cSn]=(3sC(=OrOc42]ncc(Nc-
remove S from position 5,C6H)cn]=(3sC(=OrOc42]ncc(Nc-
replace ] at position 6 with 4,C6H)cn4=(3sC(=OrOc42]ncc(Nc-
add F at position 11,C6H)cn4=(3sFC(=OrOc42]ncc(Nc-
add ] at position 29,C6H)cn4=(3sFC(=OrOc42]ncc(Nc-]
replace O at position 15 with 5,C6H)cn4=(3sFC(=5rOc42]ncc(Nc-]
add I at position 1,CI6H)cn4=(3sFC(=5rOc42]ncc(Nc-]
add l at position 9,CI6H)cn4=l(3sFC(=5rOc42]ncc(Nc-]
remove c from position 20,CI6H)cn4=l(3sFC(=5rO42]ncc(Nc-]
add N at position 3,CI6NH)cn4=l(3sFC(=5rO42]ncc(Nc-]
add r at position 9,CI6NH)cn4r=l(3sFC(=5rO42]ncc(Nc-]
add 7 at position 27,CI6NH)cn4r=l(3sFC(=5rO42]nc7c(Nc-]
remove 3 from position 13,CI6NH)cn4r=l(sFC(=5rO42]nc7c(Nc-]
replace n at position 24 with s,CI6NH)cn4r=l(sFC(=5rO42]sc7c(Nc-]
replace = at position 17 with H,CI6NH)cn4r=l(sFC(H5rO42]sc7c(Nc-]
remove r from position 19,CI6NH)cn4r=l(sFC(H5O42]sc7c(Nc-]
remove r from position 9,CI6NH)cn4=l(sFC(H5O42]sc7c(Nc-]
replace 6 at position 2 with c,CIcNH)cn4=l(sFC(H5O42]sc7c(Nc-]
remove F from position 13,CIcNH)cn4=l(sC(H5O42]sc7c(Nc-]
remove 4 from position 18,CIcNH)cn4=l(sC(H5O2]sc7c(Nc-]
remove N from position 3,CIcH)cn4=l(sC(H5O2]sc7c(Nc-]
remove = from position 8,CIcH)cn4l(sC(H5O2]sc7c(Nc-]
add l at position 22,CIcH)cn4l(sC(H5O2]sc7cl(Nc-]
remove I from position 1,CcH)cn4l(sC(H5O2]sc7cl(Nc-]
remove ] from position 26,CcH)cn4l(sC(H5O2]sc7cl(Nc-
remove c from position 24,CcH)cn4l(sC(H5O2]sc7cl(N-
remove H from position 12,CcH)cn4l(sC(5O2]sc7cl(N-
remove H from position 2,Cc)cn4l(sC(5O2]sc7cl(N-
remove C from position 9,Cc)cn4l(s(5O2]sc7cl(N-
remove - from position 21,Cc)cn4l(s(5O2]sc7cl(N
replace 4 at position 5 with =,Cc)cn=l(s(5O2]sc7cl(N
remove = from position 5,Cc)cnl(s(5O2]sc7cl(N
remove 2 from position 11,Cc)cnl(s(5O]sc7cl(N
remove ] from position 11,Cc)cnl(s(5Osc7cl(N
add r at position 16,Cc)cnl(s(5Osc7clr(N
remove l from position 15,Cc)cnl(s(5Osc7cr(N
remove c from position 14,Cc)cnl(s(5Osc7r(N
remove c from position 3,Cc)nl(s(5Osc7r(N
add ) at position 15,Cc)nl(s(5Osc7r()N
replace s at position 10 with 6,Cc)nl(s(5O6c7r()N
replace O at position 9 with 4,Cc)nl(s(546c7r()N
remove 6 from position 10,Cc)nl(s(54c7r()N
remove 4 from position 9,Cc)nl(s(5c7r()N
remove 5 from position 8,Cc)nl(s(c7r()N
replace l at position 4 with ),Cc)n)(s(c7r()N
remove 7 from position 9,Cc)n)(s(cr()N
add 5 at position 8,Cc)n)(s(5cr()N
replace ( at position 7 with 4,Cc)n)(s45cr()N
add H at position 8,Cc)n)(s4H5cr()N
remove c from position 1,C)n)(s4H5cr()N
replace ( at position 4 with 7,C)n)7s4H5cr()N
add 7 at position 0,7C)n)7s4H5cr()N
add 1 at position 1,71C)n)7s4H5cr()N
remove 5 from position 10,71C)n)7s4Hcr()N
remove 7 from position 6,71C)n)s4Hcr()N
replace 1 at position 1 with o,7oC)n)s4Hcr()N
replace n at position 4 with 3,7oC)3)s4Hcr()N
remove c from position 9,7oC)3)s4Hr()N
add ) at position 8,7oC)3)s4)Hr()N
remove ) from position 12,7oC)3)s4)Hr(N
add 5 at position 9,7oC)3)s4)5Hr(N
replace ) at position 3 with 1,7oC13)s4)5Hr(N
remove H from position 10,7oC13)s4)5r(N
replace C at position 2 with r,7or13)s4)5r(N
remove 5 from position 9,7or13)s4)r(N
add s at position 8,7or13)s4s)r(N
replace ( at position 11 with 1,7or13)s4s)r1N
replace N at position 12 with C,7or13)s4s)r1C
remove s from position 6,7or13)4s)r1C
replace ) at position 5 with /,7or13/4s)r1C
remove ) from position 8,7or13/4sr1C
remove 1 from position 9,7or13/4srC
remove s from position 7,7or13/4rC
add r at position 1,7ror13/4rC
remove / from position 6,7ror134rC
replace r at position 1 with 5,75or134rC
add 6 at position 3,75o6r134rC
replace r at position 8 with S,75o6r134SC
replace C at position 9 with B,75o6r134SB
replace 7 at position 0 with B,B5o6r134SB
replace r at position 4 with =,B5o6=134SB
replace o at position 2 with B,B5B6=134SB
remove 6 from position 3,B5B=134SB
remove B from position 0,5B=134SB
replace 4 at position 5 with I,5B=13ISB
remove = from position 2,5B13ISB
add 1 at position 0,15B13ISB
replace B at position 2 with F,15F13ISB
replace 3 at position 4 with O,15F1OISB
remove B from position 7,15F1OIS
remove I from position 5,15F1OS
remove 1 from position 3,15FOS
remove F from position 2,15OS
add o at position 0,o15OS
remove 5 from position 2,o1OS
add ( at position 4,o1OS(
replace ( at position 4 with I,o1OSI
remove I from position 4,o1OS
add 6 at position 2,o16OS
replace 6 at position 2 with [,o1[OS
remove 1 from position 1,o[OS
remove [ from position 1,oOS
replace S at position 2 with [,oO[
remove o from position 0,O[
remove O from position 0,[
remove [ from position 0,
final: ,
