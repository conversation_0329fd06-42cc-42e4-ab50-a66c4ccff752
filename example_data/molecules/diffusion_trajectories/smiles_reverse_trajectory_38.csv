log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with H,oHcHBH-C
remove c from position 2,oHHBH-<PERSON>
remove B from position 3,oHHH-<PERSON>
replace - at position 4 with ],oHHH]C
remove C from position 5,oHHH]
add B at position 4,oHHHB]
add N at position 6,oHHHB]N
replace B at position 4 with -,oHHH-]N
add r at position 7,oHHH-]Nr
add [ at position 6,oHHH-][Nr
add [ at position 2,oH[HH-][Nr
add 5 at position 3,oH[5HH-][Nr
replace [ at position 8 with B,oH[5HH-]BNr
add l at position 2,oHl[5HH-]BNr
add 4 at position 2,oH4l[5HH-]BNr
replace [ at position 4 with H,oH4lH5HH-]BNr
add 6 at position 5,oH4lH65HH-]BNr
add O at position 2,oHO4lH65HH-]BNr
remove H from position 9,oHO4lH65H-]BNr
add 6 at position 13,oHO4lH65H-]BN6r
replace H at position 1 with ),o)O4lH65H-]BN6r
remove H from position 5,o)O4l65H-]BN6r
replace 4 at position 3 with r,o)Orl65H-]BN6r
add = at position 6,o)Orl6=5H-]BN6r
add s at position 11,o)Orl6=5H-]sBN6r
remove 6 from position 14,o)Orl6=5H-]sBNr
replace ] at position 10 with 3,o)Orl6=5H-3sBNr
remove O from position 2,o)rl6=5H-3sBNr
add 2 at position 11,o)rl6=5H-3s2BNr
replace N at position 13 with /,o)rl6=5H-3s2B/r
add S at position 6,o)rl6=S5H-3s2B/r
add l at position 2,o)lrl6=S5H-3s2B/r
add F at position 11,o)lrl6=S5H-F3s2B/r
replace o at position 0 with S,S)lrl6=S5H-F3s2B/r
remove S from position 7,S)lrl6=5H-F3s2B/r
add C at position 10,S)lrl6=5H-CF3s2B/r
remove l from position 2,S)rl6=5H-CF3s2B/r
add r at position 1,Sr)rl6=5H-CF3s2B/r
add ) at position 12,Sr)rl6=5H-CF)3s2B/r
replace F at position 11 with 3,Sr)rl6=5H-C3)3s2B/r
add 4 at position 19,Sr)rl6=5H-C3)3s2B/r4
add + at position 2,Sr+)rl6=5H-C3)3s2B/r4
add O at position 1,SOr+)rl6=5H-C3)3s2B/r4
add F at position 21,SOr+)rl6=5H-C3)3s2B/rF4
remove r from position 2,SO+)rl6=5H-C3)3s2B/rF4
add c at position 14,SO+)rl6=5H-C3)c3s2B/rF4
add O at position 19,SO+)rl6=5H-C3)c3s2BO/rF4
add ) at position 17,SO+)rl6=5H-C3)c3s)2BO/rF4
replace - at position 10 with l,SO+)rl6=5HlC3)c3s)2BO/rF4
add = at position 12,SO+)rl6=5HlC=3)c3s)2BO/rF4
replace F at position 24 with (,SO+)rl6=5HlC=3)c3s)2BO/r(4
replace / at position 22 with C,SO+)rl6=5HlC=3)c3s)2BOCr(4
remove s from position 17,SO+)rl6=5HlC=3)c3)2BOCr(4
add - at position 19,SO+)rl6=5HlC=3)c3)2-BOCr(4
replace r at position 4 with ],SO+)]l6=5HlC=3)c3)2-BOCr(4
add ( at position 21,SO+)]l6=5HlC=3)c3)2-B(OCr(4
add S at position 5,SO+)]Sl6=5HlC=3)c3)2-B(OCr(4
add ) at position 27,SO+)]Sl6=5HlC=3)c3)2-B(OCr()4
replace S at position 0 with C,CO+)]Sl6=5HlC=3)c3)2-B(OCr()4
add C at position 20,CO+)]Sl6=5HlC=3)c3)2C-B(OCr()4
replace 6 at position 7 with N,CO+)]SlN=5HlC=3)c3)2C-B(OCr()4
add ( at position 15,CO+)]SlN=5HlC=3()c3)2C-B(OCr()4
replace 4 at position 30 with 7,CO+)]SlN=5HlC=3()c3)2C-B(OCr()7
replace ) at position 29 with 7,CO+)]SlN=5HlC=3()c3)2C-B(OCr(77
add ) at position 27,CO+)]SlN=5HlC=3()c3)2C-B(OC)r(77
replace 5 at position 9 with @,CO+)]SlN=@HlC=3()c3)2C-B(OC)r(77
add @ at position 2,CO@+)]SlN=@HlC=3()c3)2C-B(OC)r(77
replace ( at position 25 with /,CO@+)]SlN=@HlC=3()c3)2C-B/OC)r(77
remove - from position 23,CO@+)]SlN=@HlC=3()c3)2CB/OC)r(77
remove l from position 7,CO@+)]SN=@HlC=3()c3)2CB/OC)r(77
replace 3 at position 14 with r,CO@+)]SN=@HlC=r()c3)2CB/OC)r(77
add ) at position 21,CO@+)]SN=@HlC=r()c3)2)CB/OC)r(77
add 4 at position 13,CO@+)]SN=@HlC4=r()c3)2)CB/OC)r(77
replace ) at position 17 with r,CO@+)]SN=@HlC4=r(rc3)2)CB/OC)r(77
add H at position 32,CO@+)]SN=@HlC4=r(rc3)2)CB/OC)r(7H7
add 4 at position 18,CO@+)]SN=@HlC4=r(r4c3)2)CB/OC)r(7H7
add 6 at position 21,CO@+)]SN=@HlC4=r(r4c36)2)CB/OC)r(7H7
replace 4 at position 18 with o,CO@+)]SN=@HlC4=r(roc36)2)CB/OC)r(7H7
replace 6 at position 21 with r,CO@+)]SN=@HlC4=r(roc3r)2)CB/OC)r(7H7
remove ) from position 30,CO@+)]SN=@HlC4=r(roc3r)2)CB/OCr(7H7
add 3 at position 6,CO@+)]3SN=@HlC4=r(roc3r)2)CB/OCr(7H7
add + at position 29,CO@+)]3SN=@HlC4=r(roc3r)2)CB/+OCr(7H7
add c at position 30,CO@+)]3SN=@HlC4=r(roc3r)2)CB/+cOCr(7H7
remove r from position 33,CO@+)]3SN=@HlC4=r(roc3r)2)CB/+cOC(7H7
add C at position 22,CO@+)]3SN=@HlC4=r(roc3Cr)2)CB/+cOC(7H7
add c at position 23,CO@+)]3SN=@HlC4=r(roc3Ccr)2)CB/+cOC(7H7
remove / from position 30,CO@+)]3SN=@HlC4=r(roc3Ccr)2)CB+cOC(7H7
add 2 at position 30,CO@+)]3SN=@HlC4=r(roc3Ccr)2)CB2+cOC(7H7
add N at position 19,CO@+)]3SN=@HlC4=r(rNoc3Ccr)2)CB2+cOC(7H7
add n at position 4,CO@+n)]3SN=@HlC4=r(rNoc3Ccr)2)CB2+cOC(7H7
replace = at position 10 with 5,CO@+n)]3SN5@HlC4=r(rNoc3Ccr)2)CB2+cOC(7H7
remove 2 from position 28,CO@+n)]3SN5@HlC4=r(rNoc3Ccr))CB2+cOC(7H7
remove 7 from position 37,CO@+n)]3SN5@HlC4=r(rNoc3Ccr))CB2+cOC(H7
add @ at position 17,CO@+n)]3SN5@HlC4=@r(rNoc3Ccr))CB2+cOC(H7
add = at position 7,CO@+n)]=3SN5@HlC4=@r(rNoc3Ccr))CB2+cOC(H7
add # at position 36,CO@+n)]=3SN5@HlC4=@r(rNoc3Ccr))CB2+c#OC(H7
replace = at position 17 with F,CO@+n)]=3SN5@HlC4F@r(rNoc3Ccr))CB2+c#OC(H7
replace + at position 34 with ),CO@+n)]=3SN5@HlC4F@r(rNoc3Ccr))CB2)c#OC(H7
add c at position 36,CO@+n)]=3SN5@HlC4F@r(rNoc3Ccr))CB2)cc#OC(H7
add H at position 19,CO@+n)]=3SN5@HlC4F@Hr(rNoc3Ccr))CB2)cc#OC(H7
replace F at position 17 with n,CO@+n)]=3SN5@HlC4n@Hr(rNoc3Ccr))CB2)cc#OC(H7
remove 7 from position 43,CO@+n)]=3SN5@HlC4n@Hr(rNoc3Ccr))CB2)cc#OC(H
add F at position 20,CO@+n)]=3SN5@HlC4n@HFr(rNoc3Ccr))CB2)cc#OC(H
replace @ at position 2 with 2,CO2+n)]=3SN5@HlC4n@HFr(rNoc3Ccr))CB2)cc#OC(H
add ( at position 9,CO2+n)]=3(SN5@HlC4n@HFr(rNoc3Ccr))CB2)cc#OC(H
remove ( from position 43,CO2+n)]=3(SN5@HlC4n@HFr(rNoc3Ccr))CB2)cc#OCH
add r at position 10,CO2+n)]=3(rSN5@HlC4n@HFr(rNoc3Ccr))CB2)cc#OCH
remove ) from position 34,CO2+n)]=3(rSN5@HlC4n@HFr(rNoc3Ccr)CB2)cc#OCH
remove r from position 10,CO2+n)]=3(SN5@HlC4n@HFr(rNoc3Ccr)CB2)cc#OCH
add S at position 8,CO2+n)]=S3(SN5@HlC4n@HFr(rNoc3Ccr)CB2)cc#OCH
remove B from position 35,CO2+n)]=S3(SN5@HlC4n@HFr(rNoc3Ccr)C2)cc#OCH
remove F from position 22,CO2+n)]=S3(SN5@HlC4n@Hr(rNoc3Ccr)C2)cc#OCH
replace 5 at position 13 with 3,CO2+n)]=S3(SN3@HlC4n@Hr(rNoc3Ccr)C2)cc#OCH
add O at position 11,CO2+n)]=S3(OSN3@HlC4n@Hr(rNoc3Ccr)C2)cc#OCH
replace r at position 32 with c,CO2+n)]=S3(OSN3@HlC4n@Hr(rNoc3Ccc)C2)cc#OCH
remove r from position 23,CO2+n)]=S3(OSN3@HlC4n@H(rNoc3Ccc)C2)cc#OCH
replace C at position 29 with c,CO2+n)]=S3(OSN3@HlC4n@H(rNoc3ccc)C2)cc#OCH
remove S from position 12,CO2+n)]=S3(ON3@HlC4n@H(rNoc3ccc)C2)cc#OCH
add ) at position 12,CO2+n)]=S3(O)N3@HlC4n@H(rNoc3ccc)C2)cc#OCH
add H at position 4,CO2+Hn)]=S3(O)N3@HlC4n@H(rNoc3ccc)C2)cc#OCH
replace H at position 4 with c,CO2+cn)]=S3(O)N3@HlC4n@H(rNoc3ccc)C2)cc#OCH
replace 4 at position 20 with ],CO2+cn)]=S3(O)N3@HlC]n@H(rNoc3ccc)C2)cc#OCH
add S at position 5,CO2+cSn)]=S3(O)N3@HlC]n@H(rNoc3ccc)C2)cc#OCH
replace 2 at position 2 with H,COH+cSn)]=S3(O)N3@HlC]n@H(rNoc3ccc)C2)cc#OCH
add O at position 3,COHO+cSn)]=S3(O)N3@HlC]n@H(rNoc3ccc)C2)cc#OCH
replace + at position 4 with #,COHO#cSn)]=S3(O)N3@HlC]n@H(rNoc3ccc)C2)cc#OCH
replace l at position 20 with ],COHO#cSn)]=S3(O)N3@H]C]n@H(rNoc3ccc)C2)cc#OCH
add C at position 17,COHO#cSn)]=S3(O)NC3@H]C]n@H(rNoc3ccc)C2)cc#OCH
add B at position 10,COHO#cSn)]B=S3(O)NC3@H]C]n@H(rNoc3ccc)C2)cc#OCH
remove S from position 6,COHO#cn)]B=S3(O)NC3@H]C]n@H(rNoc3ccc)C2)cc#OCH
replace N at position 16 with [,COHO#cn)]B=S3(O)[C3@H]C]n@H(rNoc3ccc)C2)cc#OCH
replace n at position 24 with [,COHO#cn)]B=S3(O)[C3@H]C][@H(rNoc3ccc)C2)cc#OCH
replace n at position 6 with (,COHO#c()]B=S3(O)[C3@H]C][@H(rNoc3ccc)C2)cc#OCH
add C at position 7,COHO#c(C)]B=S3(O)[C3@H]C][@H(rNoc3ccc)C2)cc#OCH
remove = from position 11,COHO#c(C)]BS3(O)[C3@H]C][@H(rNoc3ccc)C2)cc#OCH
replace 3 at position 18 with O,COHO#c(C)]BS3(O)[CO@H]C][@H(rNoc3ccc)C2)cc#OCH
add - at position 33,COHO#c(C)]BS3(O)[CO@H]C][@H(rNoc3-ccc)C2)cc#OCH
replace B at position 10 with (,COHO#c(C)](S3(O)[CO@H]C][@H(rNoc3-ccc)C2)cc#OCH
replace o at position 30 with ),COHO#c(C)](S3(O)[CO@H]C][@H(rN)c3-ccc)C2)cc#OCH
replace ) at position 30 with (,COHO#c(C)](S3(O)[CO@H]C][@H(rN(c3-ccc)C2)cc#OCH
add 3 at position 37,COHO#c(C)](S3(O)[CO@H]C][@H(rN(c3-ccc3)C2)cc#OCH
add 2 at position 22,COHO#c(C)](S3(O)[CO@H]2C][@H(rN(c3-ccc3)C2)cc#OCH
replace H at position 2 with c,COcO#c(C)](S3(O)[CO@H]2C][@H(rN(c3-ccc3)C2)cc#OCH
remove ] from position 9,COcO#c(C)(S3(O)[CO@H]2C][@H(rN(c3-ccc3)C2)cc#OCH
replace 3 at position 11 with C,COcO#c(C)(SC(O)[CO@H]2C][@H(rN(c3-ccc3)C2)cc#OCH
remove O from position 17,COcO#c(C)(SC(O)[C@H]2C][@H(rN(c3-ccc3)C2)cc#OCH
add r at position 29,COcO#c(C)(SC(O)[C@H]2C][@H(rNr(c3-ccc3)C2)cc#OCH
remove - from position 33,COcO#c(C)(SC(O)[C@H]2C][@H(rNr(c3ccc3)C2)cc#OCH
remove ] from position 22,COcO#c(C)(SC(O)[C@H]2C[@H(rNr(c3ccc3)C2)cc#OCH
add c at position 9,COcO#c(C)c(SC(O)[C@H]2C[@H(rNr(c3ccc3)C2)cc#OCH
add H at position 4,COcOH#c(C)c(SC(O)[C@H]2C[@H(rNr(c3ccc3)C2)cc#OCH
remove H from position 4,COcO#c(C)c(SC(O)[C@H]2C[@H(rNr(c3ccc3)C2)cc#OCH
add C at position 24,COcO#c(C)c(SC(O)[C@H]2C[C@H(rNr(c3ccc3)C2)cc#OCH
add 1 at position 45,COcO#c(C)c(SC(O)[C@H]2C[C@H(rNr(c3ccc3)C2)cc#1OCH
replace # at position 4 with c,COcOcc(C)c(SC(O)[C@H]2C[C@H(rNr(c3ccc3)C2)cc#1OCH
add C at position 1,CCOcOcc(C)c(SC(O)[C@H]2C[C@H(rNr(c3ccc3)C2)cc#1OCH
replace r at position 29 with C,CCOcOcc(C)c(SC(O)[C@H]2C[C@H(CNr(c3ccc3)C2)cc#1OCH
add ) at position 41,CCOcOcc(C)c(SC(O)[C@H]2C[C@H(CNr(c3ccc3)C)2)cc#1OCH
add 2 at position 22,CCOcOcc(C)c(SC(O)[C@H]22C[C@H(CNr(c3ccc3)C)2)cc#1OCH
replace O at position 4 with 1,CCOc1cc(C)c(SC(O)[C@H]22C[C@H(CNr(c3ccc3)C)2)cc#1OCH
add N at position 17,CCOc1cc(C)c(SC(O)N[C@H]22C[C@H(CNr(c3ccc3)C)2)cc#1OCH
add ) at position 32,CCOc1cc(C)c(SC(O)N[C@H]22C[C@H(C)Nr(c3ccc3)C)2)cc#1OCH
add c at position 38,CCOc1cc(C)c(SC(O)N[C@H]22C[C@H(C)Nr(c3cccc3)C)2)cc#1OCH
remove r from position 34,CCOc1cc(C)c(SC(O)N[C@H]22C[C@H(C)N(c3cccc3)C)2)cc#1OCH
add = at position 15,CCOc1cc(C)c(SC(=O)N[C@H]22C[C@H(C)N(c3cccc3)C)2)cc#1OCH
remove C from position 0,COc1cc(C)c(SC(=O)N[C@H]22C[C@H(C)N(c3cccc3)C)2)cc#1OCH
remove S from position 11,COc1cc(C)c(C(=O)N[C@H]22C[C@H(C)N(c3cccc3)C)2)cc#1OCH
add + at position 29,COc1cc(C)c(C(=O)N[C@H]22C[C@H+(C)N(c3cccc3)C)2)cc#1OCH
add c at position 39,COc1cc(C)c(C(=O)N[C@H]22C[C@H+(C)N(c3ccccc3)C)2)cc#1OCH
remove # from position 50,COc1cc(C)c(C(=O)N[C@H]22C[C@H+(C)N(c3ccccc3)C)2)cc1OCH
remove 2 from position 22,COc1cc(C)c(C(=O)N[C@H]2C[C@H+(C)N(c3ccccc3)C)2)cc1OCH
remove ) from position 44,COc1cc(C)c(C(=O)N[C@H]2C[C@H+(C)N(c3ccccc3)C2)cc1OCH
remove H from position 51,COc1cc(C)c(C(=O)N[C@H]2C[C@H+(C)N(c3ccccc3)C2)cc1OC
replace + at position 28 with ],COc1cc(C)c(C(=O)N[C@H]2C[C@H](C)N(c3ccccc3)C2)cc1OC
final: COc1cc(C)c(C(=O)N[C@H]2C[C@H](C)N(c3ccccc3)C2)cc1OC,COc1cc(C)c(C(=O)N[C@H]2C[C@H](C)N(c3ccccc3)C2)cc1OC
