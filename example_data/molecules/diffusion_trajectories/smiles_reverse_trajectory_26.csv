log,state
initialize: ,
add O at position 0,O
add r at position 0,r<PERSON>
add I at position 2,r<PERSON><PERSON>
add ( at position 2,rO(I
add 2 at position 1,r2O(I
replace O at position 2 with (,r2((I
add [ at position 0,[r2((I
add c at position 2,[rc2((I
add C at position 6,[rc2((CI
add 2 at position 2,[r2c2((CI
remove 2 from position 4,[r2c((CI
replace I at position 7 with C,[r2c((CC
replace 2 at position 2 with c,[rcc((CC
replace [ at position 0 with =,=rcc((CC
remove = from position 0,rcc((CC
replace C at position 5 with ],rcc((]C
remove ( from position 4,rcc(]C
remove C from position 5,rcc(]
remove ( from position 3,rcc]
remove c from position 1,rc]
replace c at position 1 with o,ro]
remove r from position 0,o]
replace o at position 0 with -,-]
add 7 at position 0,7-]
add r at position 0,r7-]
replace 7 at position 1 with O,rO-]
remove r from position 0,O-]
add 3 at position 0,3O-]
add = at position 2,3O=-]
add F at position 5,3O=-]F
remove ] from position 4,3O=-F
replace F at position 4 with 3,3O=-3
add l at position 2,3Ol=-3
replace = at position 3 with ],3Ol]-3
add # at position 5,3Ol]-#3
remove l from position 2,3O]-#3
remove # from position 4,3O]-3
replace O at position 1 with C,3C]-3
add o at position 4,3C]-o3
add 1 at position 3,3C]1-o3
replace ] at position 2 with B,3CB1-o3
remove - from position 4,3CB1o3
remove 1 from position 3,3CBo3
replace 3 at position 4 with C,3CBoC
remove 3 from position 0,CBoC
replace B at position 1 with ),C)oC
replace ) at position 1 with 5,C5oC
add [ at position 2,C5[oC
replace o at position 3 with +,C5[+C
add = at position 2,C5=[+C
add N at position 2,C5N=[+C
replace C at position 0 with /,/5N=[+C
add 7 at position 2,/57N=[+C
remove 7 from position 2,/5N=[+C
remove C from position 6,/5N=[+
add r at position 6,/5N=[+r
replace + at position 5 with #,/5N=[#r
add r at position 7,/5N=[#rr
add H at position 8,/5N=[#rrH
add l at position 3,/5Nl=[#rrH
add 3 at position 7,/5Nl=[#3rrH
remove l from position 3,/5N=[#3rrH
add ( at position 1,/(5N=[#3rrH
add c at position 1,/c(5N=[#3rrH
remove # from position 7,/c(5N=[3rrH
replace H at position 10 with 1,/c(5N=[3rr1
remove r from position 8,/c(5N=[3r1
replace ( at position 2 with /,/c/5N=[3r1
replace / at position 2 with C,/cC5N=[3r1
add F at position 3,/cCF5N=[3r1
add 5 at position 9,/cCF5N=[35r1
replace = at position 6 with #,/cCF5N#[35r1
replace 3 at position 8 with r,/cCF5N#[r5r1
add H at position 8,/cCF5N#[Hr5r1
add 3 at position 9,/cCF5N#[H3r5r1
remove 1 from position 13,/cCF5N#[H3r5r
replace 3 at position 9 with ),/cCF5N#[H)r5r
replace 5 at position 11 with r,/cCF5N#[H)rrr
add 6 at position 9,/cCF5N#[H6)rrr
remove # from position 6,/cCF5N[H6)rrr
add 3 at position 10,/cCF5N[H6)3rrr
replace [ at position 6 with =,/cCF5N=H6)3rrr
add 7 at position 4,/cCF75N=H6)3rrr
add + at position 13,/cCF75N=H6)3r+rr
remove c from position 1,/CF75N=H6)3r+rr
replace N at position 5 with F,/CF75F=H6)3r+rr
remove / from position 0,CF75F=H6)3r+rr
add H at position 14,CF75F=H6)3r+rrH
replace 7 at position 2 with I,CFI5F=H6)3r+rrH
remove I from position 2,CF5F=H6)3r+rrH
replace ) at position 7 with 7,CF5F=H673r+rrH
replace F at position 1 with o,Co5F=H673r+rrH
add 2 at position 7,Co5F=H6273r+rrH
add ( at position 14,Co5F=H6273r+rr(H
add - at position 6,Co5F=H-6273r+rr(H
add [ at position 11,Co5F=H-6273[r+rr(H
add / at position 9,Co5F=H-62/73[r+rr(H
replace 5 at position 2 with 6,Co6F=H-62/73[r+rr(H
replace 2 at position 8 with 3,Co6F=H-63/73[r+rr(H
add + at position 4,Co6F+=H-63/73[r+rr(H
replace + at position 15 with /,Co6F+=H-63/73[r/rr(H
replace + at position 4 with l,Co6Fl=H-63/73[r/rr(H
add C at position 12,Co6Fl=H-63/7C3[r/rr(H
add S at position 21,Co6Fl=H-63/7C3[r/rr(HS
add N at position 11,Co6Fl=H-63/N7C3[r/rr(HS
add B at position 23,Co6Fl=H-63/N7C3[r/rr(HSB
add ] at position 15,Co6Fl=H-63/N7C3][r/rr(HSB
add o at position 19,Co6Fl=H-63/N7C3][r/orr(HSB
replace C at position 13 with H,Co6Fl=H-63/N7H3][r/orr(HSB
remove H from position 23,Co6Fl=H-63/N7H3][r/orr(SB
add C at position 23,Co6Fl=H-63/N7H3][r/orr(CSB
add [ at position 6,Co6Fl=[H-63/N7H3][r/orr(CSB
replace [ at position 17 with 2,Co6Fl=[H-63/N7H3]2r/orr(CSB
add 4 at position 5,Co6Fl4=[H-63/N7H3]2r/orr(CSB
add n at position 5,Co6Fln4=[H-63/N7H3]2r/orr(CSB
replace [ at position 8 with C,Co6Fln4=CH-63/N7H3]2r/orr(CSB
add c at position 11,Co6Fln4=CH-c63/N7H3]2r/orr(CSB
add - at position 5,Co6Fl-n4=CH-c63/N7H3]2r/orr(CSB
remove H from position 18,Co6Fl-n4=CH-c63/N73]2r/orr(CSB
add H at position 27,Co6Fl-n4=CH-c63/N73]2r/orr(HCSB
replace F at position 3 with o,Co6ol-n4=CH-c63/N73]2r/orr(HCSB
remove H from position 10,Co6ol-n4=C-c63/N73]2r/orr(HCSB
replace 4 at position 7 with C,Co6ol-nC=C-c63/N73]2r/orr(HCSB
add S at position 13,Co6ol-nC=C-c6S3/N73]2r/orr(HCSB
add 4 at position 23,Co6ol-nC=C-c6S3/N73]2r/4orr(HCSB
add ( at position 29,Co6ol-nC=C-c6S3/N73]2r/4orr(H(CSB
replace N at position 16 with c,Co6ol-nC=C-c6S3/c73]2r/4orr(H(CSB
replace C at position 9 with [,Co6ol-nC=[-c6S3/c73]2r/4orr(H(CSB
add n at position 13,Co6ol-nC=[-c6nS3/c73]2r/4orr(H(CSB
add 5 at position 4,Co6o5l-nC=[-c6nS3/c73]2r/4orr(H(CSB
add = at position 23,Co6o5l-nC=[-c6nS3/c73]2=r/4orr(H(CSB
replace o at position 1 with 1,C16o5l-nC=[-c6nS3/c73]2=r/4orr(H(CSB
remove S from position 15,C16o5l-nC=[-c6n3/c73]2=r/4orr(H(CSB
add o at position 21,C16o5l-nC=[-c6n3/c73]o2=r/4orr(H(CSB
remove l from position 5,C16o5-nC=[-c6n3/c73]o2=r/4orr(H(CSB
replace ( at position 31 with 7,C16o5-nC=[-c6n3/c73]o2=r/4orr(H7CSB
replace S at position 33 with c,C16o5-nC=[-c6n3/c73]o2=r/4orr(H7CcB
remove 6 from position 12,C16o5-nC=[-cn3/c73]o2=r/4orr(H7CcB
remove 5 from position 4,C16o-nC=[-cn3/c73]o2=r/4orr(H7CcB
replace r at position 26 with 5,C16o-nC=[-cn3/c73]o2=r/4or5(H7CcB
add S at position 23,C16o-nC=[-cn3/c73]o2=r/S4or5(H7CcB
replace ( at position 28 with ),C16o-nC=[-cn3/c73]o2=r/S4or5)H7CcB
replace [ at position 8 with (,C16o-nC=(-cn3/c73]o2=r/S4or5)H7CcB
replace o at position 25 with ],C16o-nC=(-cn3/c73]o2=r/S4]r5)H7CcB
add 6 at position 32,C16o-nC=(-cn3/c73]o2=r/S4]r5)H7C6cB
add - at position 15,C16o-nC=(-cn3/c-73]o2=r/S4]r5)H7C6cB
replace - at position 4 with c,C16ocnC=(-cn3/c-73]o2=r/S4]r5)H7C6cB
add = at position 8,C16ocnC==(-cn3/c-73]o2=r/S4]r5)H7C6cB
replace / at position 14 with c,C16ocnC==(-cn3cc-73]o2=r/S4]r5)H7C6cB
add l at position 21,C16ocnC==(-cn3cc-73]ol2=r/S4]r5)H7C6cB
add 7 at position 1,C716ocnC==(-cn3cc-73]ol2=r/S4]r5)H7C6cB
remove ) from position 32,C716ocnC==(-cn3cc-73]ol2=r/S4]r5H7C6cB
add 5 at position 37,C716ocnC==(-cn3cc-73]ol2=r/S4]r5H7C6c5B
replace 3 at position 19 with C,C716ocnC==(-cn3cc-7C]ol2=r/S4]r5H7C6c5B
replace o at position 4 with N,C716NcnC==(-cn3cc-7C]ol2=r/S4]r5H7C6c5B
add O at position 27,C716NcnC==(-cn3cc-7C]ol2=r/OS4]r5H7C6c5B
add s at position 20,C716NcnC==(-cn3cc-7Cs]ol2=r/OS4]r5H7C6c5B
remove 1 from position 2,C76NcnC==(-cn3cc-7Cs]ol2=r/OS4]r5H7C6c5B
remove 7 from position 1,C6NcnC==(-cn3cc-7Cs]ol2=r/OS4]r5H7C6c5B
replace 7 at position 16 with ),C6NcnC==(-cn3cc-)Cs]ol2=r/OS4]r5H7C6c5B
add ( at position 7,C6NcnC=(=(-cn3cc-)Cs]ol2=r/OS4]r5H7C6c5B
remove H from position 33,C6NcnC=(=(-cn3cc-)Cs]ol2=r/OS4]r57C6c5B
replace 4 at position 29 with ),C6NcnC=(=(-cn3cc-)Cs]ol2=r/OS)]r57C6c5B
remove 5 from position 32,C6NcnC=(=(-cn3cc-)Cs]ol2=r/OS)]r7C6c5B
add c at position 38,C6NcnC=(=(-cn3cc-)Cs]ol2=r/OS)]r7C6c5Bc
replace ) at position 17 with ],C6NcnC=(=(-cn3cc-]Cs]ol2=r/OS)]r7C6c5Bc
add 5 at position 32,C6NcnC=(=(-cn3cc-]Cs]ol2=r/OS)]r57C6c5Bc
add 5 at position 18,C6NcnC=(=(-cn3cc-]5Cs]ol2=r/OS)]r57C6c5Bc
add 5 at position 37,C6NcnC=(=(-cn3cc-]5Cs]ol2=r/OS)]r57C65c5Bc
replace 5 at position 18 with /,C6NcnC=(=(-cn3cc-]/Cs]ol2=r/OS)]r57C65c5Bc
replace 5 at position 37 with c,C6NcnC=(=(-cn3cc-]/Cs]ol2=r/OS)]r57C6cc5Bc
replace ) at position 30 with H,C6NcnC=(=(-cn3cc-]/Cs]ol2=r/OSH]r57C6cc5Bc
replace 6 at position 1 with I,CINcnC=(=(-cn3cc-]/Cs]ol2=r/OSH]r57C6cc5Bc
replace S at position 29 with C,CINcnC=(=(-cn3cc-]/Cs]ol2=r/OCH]r57C6cc5Bc
add I at position 1,CIINcnC=(=(-cn3cc-]/Cs]ol2=r/OCH]r57C6cc5Bc
add c at position 42,CIINcnC=(=(-cn3cc-]/Cs]ol2=r/OCH]r57C6cc5Bcc
remove r from position 33,CIINcnC=(=(-cn3cc-]/Cs]ol2=r/OCH]57C6cc5Bcc
add F at position 22,CIINcnC=(=(-cn3cc-]/CsF]ol2=r/OCH]57C6cc5Bcc
remove 6 from position 37,CIINcnC=(=(-cn3cc-]/CsF]ol2=r/OCH]57Ccc5Bcc
remove B from position 40,CIINcnC=(=(-cn3cc-]/CsF]ol2=r/OCH]57Ccc5cc
add N at position 30,CIINcnC=(=(-cn3cc-]/CsF]ol2=r/NOCH]57Ccc5cc
add 1 at position 19,CIINcnC=(=(-cn3cc-]1/CsF]ol2=r/NOCH]57Ccc5cc
add C at position 4,CIINCcnC=(=(-cn3cc-]1/CsF]ol2=r/NOCH]57Ccc5cc
replace = at position 10 with 3,CIINCcnC=(3(-cn3cc-]1/CsF]ol2=r/NOCH]57Ccc5cc
remove 2 from position 28,CIINCcnC=(3(-cn3cc-]1/CsF]ol=r/NOCH]57Ccc5cc
remove 7 from position 37,CIINCcnC=(3(-cn3cc-]1/CsF]ol=r/NOCH]5Ccc5cc
add F at position 17,CIINCcnC=(3(-cn3cFc-]1/CsF]ol=r/NOCH]5Ccc5cc
add l at position 7,CIINCcnlC=(3(-cn3cFc-]1/CsF]ol=r/NOCH]5Ccc5cc
add @ at position 36,CIINCcnlC=(3(-cn3cFc-]1/CsF]ol=r/NOC@H]5Ccc5cc
add ( at position 26,CIINCcnlC=(3(-cn3cFc-]1/Cs(F]ol=r/NOC@H]5Ccc5cc
replace c at position 5 with S,CIINCSnlC=(3(-cn3cFc-]1/Cs(F]ol=r/NOC@H]5Ccc5cc
add ] at position 9,CIINCSnlC]=(3(-cn3cFc-]1/Cs(F]ol=r/NOC@H]5Ccc5cc
add 1 at position 43,CIINCSnlC]=(3(-cn3cFc-]1/Cs(F]ol=r/NOC@H]5C1cc5cc
add c at position 19,CIINCSnlC]=(3(-cn3ccFc-]1/Cs(F]ol=r/NOC@H]5C1cc5cc
add N at position 17,CIINCSnlC]=(3(-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]5C1cc5cc
add 1 at position 43,CIINCSnlC]=(3(-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1cc5cc
add s at position 47,CIINCSnlC]=(3(-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1scc5cc
replace ( at position 13 with 4,CIINCSnlC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1scc5cc
add # at position 48,CIINCSnlC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1s#cc5cc
add O at position 3,CIIONCSnlC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1s#cc5cc
add C at position 0,CCIIONCSnlC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H]15C1s#cc5cc
add ) at position 45,CCIIONCSnlC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H])15C1s#cc5cc
remove l from position 9,CCIIONCSnC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H])15C1s#cc5cc
remove I from position 3,CCIONCSnC]=(34-cnN3ccFc-]1/Cs(F]ol=r/NOC@H])15C1s#cc5cc
add c at position 21,CCIONCSnC]=(34-cnN3cccFc-]1/Cs(F]ol=r/NOC@H])15C1s#cc5cc
remove s from position 49,CCIONCSnC]=(34-cnN3cccFc-]1/Cs(F]ol=r/NOC@H])15C1#cc5cc
remove F from position 22,CCIONCSnC]=(34-cnN3cccc-]1/Cs(F]ol=r/NOC@H])15C1#cc5cc
replace 4 at position 13 with C,CCIONCSnC]=(3C-cnN3cccc-]1/Cs(F]ol=r/NOC@H])15C1#cc5cc
replace / at position 26 with n,CCIONCSnC]=(3C-cnN3cccc-]1nCs(F]ol=r/NOC@H])15C1#cc5cc
replace C at position 13 with O,CCIONCSnC]=(3O-cnN3cccc-]1nCs(F]ol=r/NOC@H])15C1#cc5cc
add H at position 52,CCIONCSnC]=(3O-cnN3cccc-]1nCs(F]ol=r/NOC@H])15C1#cc5Hcc
add 1 at position 29,CCIONCSnC]=(3O-cnN3cccc-]1nCs1(F]ol=r/NOC@H])15C1#cc5Hcc
remove 1 from position 25,CCIONCSnC]=(3O-cnN3cccc-]nCs1(F]ol=r/NOC@H])15C1#cc5Hcc
remove s from position 27,CCIONCSnC]=(3O-cnN3cccc-]nC1(F]ol=r/NOC@H])15C1#cc5Hcc
remove n from position 16,CCIONCSnC]=(3O-cN3cccc-]nC1(F]ol=r/NOC@H])15C1#cc5Hcc
add ) at position 29,CCIONCSnC]=(3O-cN3cccc-]nC1(F)]ol=r/NOC@H])15C1#cc5Hcc
remove 5 from position 44,CCIONCSnC]=(3O-cN3cccc-]nC1(F)]ol=r/NOC@H])1C1#cc5Hcc
remove l from position 32,CCIONCSnC]=(3O-cN3cccc-]nC1(F)]o=r/NOC@H])1C1#cc5Hcc
remove c from position 18,CCIONCSnC]=(3O-cN3ccc-]nC1(F)]o=r/NOC@H])1C1#cc5Hcc
remove 1 from position 25,CCIONCSnC]=(3O-cN3ccc-]nC(F)]o=r/NOC@H])1C1#cc5Hcc
replace ] at position 28 with (,CCIONCSnC]=(3O-cN3ccc-]nC(F)(o=r/NOC@H])1C1#cc5Hcc
replace 5 at position 46 with c,CCIONCSnC]=(3O-cN3ccc-]nC(F)(o=r/NOC@H])1C1#cccHcc
add c at position 17,CCIONCSnC]=(3O-cNc3ccc-]nC(F)(o=r/NOC@H])1C1#cccHcc
add @ at position 10,CCIONCSnC]@=(3O-cNc3ccc-]nC(F)(o=r/NOC@H])1C1#cccHcc
remove S from position 6,CCIONCnC]@=(3O-cNc3ccc-]nC(F)(o=r/NOC@H])1C1#cccHcc
replace N at position 16 with c,CCIONCnC]@=(3O-ccc3ccc-]nC(F)(o=r/NOC@H])1C1#cccHcc
replace n at position 24 with 2,CCIONCnC]@=(3O-ccc3ccc-]2C(F)(o=r/NOC@H])1C1#cccHcc
replace n at position 6 with (,CCIONC(C]@=(3O-ccc3ccc-]2C(F)(o=r/NOC@H])1C1#cccHcc
add S at position 7,CCIONC(SC]@=(3O-ccc3ccc-]2C(F)(o=r/NOC@H])1C1#cccHcc
remove = from position 11,CCIONC(SC]@(3O-ccc3ccc-]2C(F)(o=r/NOC@H])1C1#cccHcc
replace 3 at position 18 with O,CCIONC(SC]@(3O-cccOccc-]2C(F)(o=r/NOC@H])1C1#cccHcc
add - at position 33,CCIONC(SC]@(3O-cccOccc-]2C(F)(o=r-/NOC@H])1C1#cccHcc
replace @ at position 10 with C,CCIONC(SC]C(3O-cccOccc-]2C(F)(o=r-/NOC@H])1C1#cccHcc
replace o at position 30 with (,CCIONC(SC]C(3O-cccOccc-]2C(F)((=r-/NOC@H])1C1#cccHcc
replace ( at position 30 with F,CCIONC(SC]C(3O-cccOccc-]2C(F)(F=r-/NOC@H])1C1#cccHcc
add [ at position 37,CCIONC(SC]C(3O-cccOccc-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
add 2 at position 22,CCIONC(SC]C(3O-cccOccc2-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
replace I at position 2 with C,CCCONC(SC]C(3O-cccOccc2-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
remove ] from position 9,CCCONC(SCC(3O-cccOccc2-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
replace 3 at position 11 with =,CCCONC(SCC(=O-cccOccc2-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
remove O from position 17,CCCONC(SCC(=O-cccccc2-]2C(F)(F=r-/NO[C@H])1C1#cccHcc
add ) at position 29,CCCONC(SCC(=O-cccccc2-]2C(F)()F=r-/NO[C@H])1C1#cccHcc
remove - from position 33,CCCONC(SCC(=O-cccccc2-]2C(F)()F=r/NO[C@H])1C1#cccHcc
remove ] from position 22,CCCONC(SCC(=O-cccccc2-2C(F)()F=r/NO[C@H])1C1#cccHcc
remove - from position 13,CCCONC(SCC(=Occcccc2-2C(F)()F=r/NO[C@H])1C1#cccHcc
remove / from position 31,CCCONC(SCC(=Occcccc2-2C(F)()F=rNO[C@H])1C1#cccHcc
add 1 at position 3,CCC1ONC(SCC(=Occcccc2-2C(F)()F=rNO[C@H])1C1#cccHcc
add c at position 42,CCC1ONC(SCC(=Occcccc2-2C(F)()F=rNO[C@H])1Cc1#cccHcc
remove - from position 21,CCC1ONC(SCC(=Occcccc22C(F)()F=rNO[C@H])1Cc1#cccHcc
add C at position 32,CCC1ONC(SCC(=Occcccc22C(F)()F=rNCO[C@H])1Cc1#cccHcc
add ( at position 33,CCC1ONC(SCC(=Occcccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc
add S at position 12,CCC1ONC(SCC(S=Occcccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc
add N at position 15,CCC1ONC(SCC(S=ONcccccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc
add 1 at position 54,CCC1ONC(SCC(S=ONcccccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc1
replace O at position 4 with =,CCC1=NC(SCC(S=ONcccccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc1
add 2 at position 17,CCC1=NC(SCC(S=ONc2ccccc22C(F)()F=rNC(O[C@H])1Cc1#cccHcc1
add ) at position 32,CCC1=NC(SCC(S=ONc2ccccc22C(F)()F)=rNC(O[C@H])1Cc1#cccHcc1
add = at position 38,CCC1=NC(SCC(S=ONc2ccccc22C(F)()F)=rNC(=O[C@H])1Cc1#cccHcc1
remove r from position 34,CCC1=NC(SCC(S=ONc2ccccc22C(F)()F)=NC(=O[C@H])1Cc1#cccHcc1
add ) at position 15,CCC1=NC(SCC(S=O)Nc2ccccc22C(F)()F)=NC(=O[C@H])1Cc1#cccHcc1
remove C from position 0,CC1=NC(SCC(S=O)Nc2ccccc22C(F)()F)=NC(=O[C@H])1Cc1#cccHcc1
remove S from position 11,CC1=NC(SCC(=O)Nc2ccccc22C(F)()F)=NC(=O[C@H])1Cc1#cccHcc1
add + at position 29,CC1=NC(SCC(=O)Nc2ccccc22C(F)(+)F)=NC(=O[C@H])1Cc1#cccHcc1
add ) at position 39,CC1=NC(SCC(=O)Nc2ccccc22C(F)(+)F)=NC(=O)[C@H])1Cc1#cccHcc1
remove # from position 50,CC1=NC(SCC(=O)Nc2ccccc22C(F)(+)F)=NC(=O)[C@H])1Cc1cccHcc1
remove 2 from position 22,CC1=NC(SCC(=O)Nc2ccccc2C(F)(+)F)=NC(=O)[C@H])1Cc1cccHcc1
remove ) from position 44,CC1=NC(SCC(=O)Nc2ccccc2C(F)(+)F)=NC(=O)[C@H]1Cc1cccHcc1
remove H from position 51,CC1=NC(SCC(=O)Nc2ccccc2C(F)(+)F)=NC(=O)[C@H]1Cc1ccccc1
replace + at position 28 with F,CC1=NC(SCC(=O)Nc2ccccc2C(F)(F)F)=NC(=O)[C@H]1Cc1ccccc1
final: CC1=NC(SCC(=O)Nc2ccccc2C(F)(F)F)=NC(=O)[C@H]1Cc1ccccc1,CC1=NC(SCC(=O)Nc2ccccc2C(F)(F)F)=NC(=O)[C@H]1Cc1ccccc1
