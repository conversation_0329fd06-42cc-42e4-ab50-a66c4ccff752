log,state
initialize: COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)cc1,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)cc1
replace c at position 28 with +,COc1ccc([C@H](CNC(=O)c2cccc3+cccc23)[NH+]2CCCC2)cc1
add H at position 51,COc1ccc([C@H](CNC(=O)c2cccc3+cccc23)[NH+]2CCCC2)cc1H
add ) at position 44,COc1ccc([C@H](CNC(=O)c2cccc3+cccc23)[NH+]2CC)CC2)cc1H
add 2 at position 22,COc1ccc([C@H](CNC(=O)c22cccc3+cccc23)[NH+]2CC)CC2)cc1H
add # at position 50,COc1ccc([C@H](CNC(=O)c22cccc3+cccc23)[NH+]2CC)CC2)#cc1H
remove H from position 39,COc1ccc([C@H](CNC(=O)c22cccc3+cccc23)[N+]2CC)CC2)#cc1H
remove + from position 29,COc1ccc([C@H](CNC(=O)c22cccc3cccc23)[N+]2CC)CC2)#cc1H
add S at position 11,COc1ccc([C@SH](CNC(=O)c22cccc3cccc23)[N+]2CC)CC2)#cc1H
add C at position 0,CCOc1ccc([C@SH](CNC(=O)c22cccc3cccc23)[N+]2CC)CC2)#cc1H
remove ( from position 15,CCOc1ccc([C@SH]CNC(=O)c22cccc3cccc23)[N+]2CC)CC2)#cc1H
add r at position 34,CCOc1ccc([C@SH]CNC(=O)c22cccc3ccccr23)[N+]2CC)CC2)#cc1H
remove [ from position 38,CCOc1ccc([C@SH]CNC(=O)c22cccc3ccccr23)N+]2CC)CC2)#cc1H
remove c from position 32,CCOc1ccc([C@SH]CNC(=O)c22cccc3cccr23)N+]2CC)CC2)#cc1H
remove C from position 17,CCOc1ccc([C@SH]CN(=O)c22cccc3cccr23)N+]2CC)CC2)#cc1H
replace 1 at position 4 with O,CCOcOccc([C@SH]CN(=O)c22cccc3cccr23)N+]2CC)CC2)#cc1H
remove 2 from position 22,CCOcOccc([C@SH]CN(=O)c2cccc3cccr23)N+]2CC)CC2)#cc1H
remove ) from position 41,CCOcOccc([C@SH]CN(=O)c2cccc3cccr23)N+]2CCCC2)#cc1H
replace c at position 29 with r,CCOcOccc([C@SH]CN(=O)c2cccc3crcr23)N+]2CCCC2)#cc1H
remove C from position 1,COcOccc([C@SH]CN(=O)c2cccc3crcr23)N+]2CCCC2)#cc1H
replace c at position 4 with #,COcO#cc([C@SH]CN(=O)c2cccc3crcr23)N+]2CCCC2)#cc1H
remove c from position 45,COcO#cc([C@SH]CN(=O)c2cccc3crcr23)N+]2CCCC2)#c1H
remove c from position 24,COcO#cc([C@SH]CN(=O)c2ccc3crcr23)N+]2CCCC2)#c1H
add H at position 4,COcOH#cc([C@SH]CN(=O)c2ccc3crcr23)N+]2CCCC2)#c1H
remove H from position 4,COcO#cc([C@SH]CN(=O)c2ccc3crcr23)N+]2CCCC2)#c1H
remove C from position 9,COcO#cc([@SH]CN(=O)c2ccc3crcr23)N+]2CCCC2)#c1H
add ] at position 22,COcO#cc([@SH]CN(=O)c2c]cc3crcr23)N+]2CCCC2)#c1H
add - at position 33,COcO#cc([@SH]CN(=O)c2c]cc3crcr23)-N+]2CCCC2)#c1H
remove r from position 29,COcO#cc([@SH]CN(=O)c2c]cc3crc23)-N+]2CCCC2)#c1H
add O at position 17,COcO#cc([@SH]CN(=OO)c2c]cc3crc23)-N+]2CCCC2)#c1H
replace H at position 11 with 3,COcO#cc([@S3]CN(=OO)c2c]cc3crc23)-N+]2CCCC2)#c1H
add ] at position 9,COcO#cc([]@S3]CN(=OO)c2c]cc3crc23)-N+]2CCCC2)#c1H
replace c at position 2 with H,COHO#cc([]@S3]CN(=OO)c2c]cc3crc23)-N+]2CCCC2)#c1H
remove 2 from position 22,COHO#cc([]@S3]CN(=OO)cc]cc3crc23)-N+]2CCCC2)#c1H
remove 2 from position 37,COHO#cc([]@S3]CN(=OO)cc]cc3crc23)-N+]CCCC2)#c1H
replace 2 at position 30 with (,COHO#cc([]@S3]CN(=OO)cc]cc3crc(3)-N+]CCCC2)#c1H
replace ( at position 30 with o,COHO#cc([]@S3]CN(=OO)cc]cc3crco3)-N+]CCCC2)#c1H
replace @ at position 10 with B,COHO#cc([]BS3]CN(=OO)cc]cc3crco3)-N+]CCCC2)#c1H
remove - from position 33,COHO#cc([]BS3]CN(=OO)cc]cc3crco3)N+]CCCC2)#c1H
replace O at position 18 with 3,COHO#cc([]BS3]CN(=3O)cc]cc3crco3)N+]CCCC2)#c1H
add = at position 11,COHO#cc([]B=S3]CN(=3O)cc]cc3crco3)N+]CCCC2)#c1H
remove ( from position 7,COHO#cc[]B=S3]CN(=3O)cc]cc3crco3)N+]CCCC2)#c1H
replace c at position 6 with n,COHO#cn[]B=S3]CN(=3O)cc]cc3crco3)N+]CCCC2)#c1H
replace c at position 24 with n,COHO#cn[]B=S3]CN(=3O)cc]nc3crco3)N+]CCCC2)#c1H
replace ( at position 16 with O,COHO#cn[]B=S3]CNO=3O)cc]nc3crco3)N+]CCCC2)#c1H
add S at position 6,COHO#cSn[]B=S3]CNO=3O)cc]nc3crco3)N+]CCCC2)#c1H
remove B from position 10,COHO#cSn[]=S3]CNO=3O)cc]nc3crco3)N+]CCCC2)#c1H
remove = from position 17,COHO#cSn[]=S3]CNO3O)cc]nc3crco3)N+]CCCC2)#c1H
replace c at position 20 with l,COHO#cSn[]=S3]CNO3O)lc]nc3crco3)N+]CCCC2)#c1H
replace # at position 4 with +,COHO+cSn[]=S3]CNO3O)lc]nc3crco3)N+]CCCC2)#c1H
remove O from position 3,COH+cSn[]=S3]CNO3O)lc]nc3crco3)N+]CCCC2)#c1H
replace H at position 2 with 2,CO2+cSn[]=S3]CNO3O)lc]nc3crco3)N+]CCCC2)#c1H
remove S from position 5,CO2+cn[]=S3]CNO3O)lc]nc3crco3)N+]CCCC2)#c1H
replace ] at position 20 with 4,CO2+cn[]=S3]CNO3O)lc4nc3crco3)N+]CCCC2)#c1H
replace c at position 4 with H,CO2+Hn[]=S3]CNO3O)lc4nc3crco3)N+]CCCC2)#c1H
remove H from position 4,CO2+n[]=S3]CNO3O)lc4nc3crco3)N+]CCCC2)#c1H
remove N from position 12,CO2+n[]=S3]CO3O)lc4nc3crco3)N+]CCCC2)#c1H
add S at position 12,CO2+n[]=S3]CSO3O)lc4nc3crco3)N+]CCCC2)#c1H
replace N at position 29 with C,CO2+n[]=S3]CSO3O)lc4nc3crco3)C+]CCCC2)#c1H
add r at position 23,CO2+n[]=S3]CSO3O)lc4nc3rcrco3)C+]CCCC2)#c1H
replace ] at position 32 with r,CO2+n[]=S3]CSO3O)lc4nc3rcrco3)C+rCCCC2)#c1H
remove C from position 11,CO2+n[]=S3]SO3O)lc4nc3rcrco3)C+rCCCC2)#c1H
replace 3 at position 13 with 5,CO2+n[]=S3]SO5O)lc4nc3rcrco3)C+rCCCC2)#c1H
add F at position 22,CO2+n[]=S3]SO5O)lc4nc3Frcrco3)C+rCCCC2)#c1H
add B at position 35,CO2+n[]=S3]SO5O)lc4nc3Frcrco3)C+rCCBCC2)#c1H
remove S from position 8,CO2+n[]=3]SO5O)lc4nc3Frcrco3)C+rCCBCC2)#c1H
add r at position 10,CO2+n[]=3]rSO5O)lc4nc3Frcrco3)C+rCCBCC2)#c1H
add ) at position 34,CO2+n[]=3]rSO5O)lc4nc3Frcrco3)C+rC)CBCC2)#c1H
remove r from position 10,CO2+n[]=3]SO5O)lc4nc3Frcrco3)C+rC)CBCC2)#c1H
add ( at position 43,CO2+n[]=3]SO5O)lc4nc3Frcrco3)C+rC)CBCC2)#c1(H
remove ] from position 9,CO2+n[]=3SO5O)lc4nc3Frcrco3)C+rC)CBCC2)#c1(H
replace 2 at position 2 with @,CO@+n[]=3SO5O)lc4nc3Frcrco3)C+rC)CBCC2)#c1(H
remove F from position 20,CO@+n[]=3SO5O)lc4nc3rcrco3)C+rC)CBCC2)#c1(H
add 7 at position 43,CO@+n[]=3SO5O)lc4nc3rcrco3)C+rC)CBCC2)#c1(H7
replace n at position 17 with F,CO@+n[]=3SO5O)lc4Fc3rcrco3)C+rC)CBCC2)#c1(H7
remove 3 from position 19,CO@+n[]=3SO5O)lc4Fcrcrco3)C+rC)CBCC2)#c1(H7
remove ) from position 36,CO@+n[]=3SO5O)lc4Fcrcrco3)C+rC)CBCC2#c1(H7
replace C at position 34 with ),CO@+n[]=3SO5O)lc4Fcrcrco3)C+rC)CBC)2#c1(H7
replace F at position 17 with =,CO@+n[]=3SO5O)lc4=crcrco3)C+rC)CBC)2#c1(H7
remove # from position 36,CO@+n[]=3SO5O)lc4=crcrco3)C+rC)CBC)2c1(H7
remove = from position 7,CO@+n[]3SO5O)lc4=crcrco3)C+rC)CBC)2c1(H7
remove c from position 17,CO@+n[]3SO5O)lc4=rcrco3)C+rC)CBC)2c1(H7
add 7 at position 37,CO@+n[]3SO5O)lc4=rcrco3)C+rC)CBC)2c1(7H7
add 2 at position 28,CO@+n[]3SO5O)lc4=rcrco3)C+rC2)CBC)2c1(7H7
replace 5 at position 10 with =,CO@+n[]3SO=O)lc4=rcrco3)C+rC2)CBC)2c1(7H7
remove n from position 4,CO@+[]3SO=O)lc4=rcrco3)C+rC2)CBC)2c1(7H7
remove c from position 19,CO@+[]3SO=O)lc4=rcro3)C+rC2)CBC)2c1(7H7
remove C from position 30,CO@+[]3SO=O)lc4=rcro3)C+rC2)CB)2c1(7H7
add / at position 30,CO@+[]3SO=O)lc4=rcro3)C+rC2)CB/)2c1(7H7
remove + from position 23,CO@+[]3SO=O)lc4=rcro3)CrC2)CB/)2c1(7H7
remove C from position 22,CO@+[]3SO=O)lc4=rcro3)rC2)CB/)2c1(7H7
add r at position 33,CO@+[]3SO=O)lc4=rcro3)rC2)CB/)2c1r(7H7
remove 2 from position 30,CO@+[]3SO=O)lc4=rcro3)rC2)CB/)c1r(7H7
remove ) from position 29,CO@+[]3SO=O)lc4=rcro3)rC2)CB/c1r(7H7
remove 3 from position 6,CO@+[]SO=O)lc4=rcro3)rC2)CB/c1r(7H7
add ) at position 30,CO@+[]SO=O)lc4=rcro3)rC2)CB/c1)r(7H7
replace r at position 21 with 6,CO@+[]SO=O)lc4=rcro3)6C2)CB/c1)r(7H7
replace o at position 18 with 4,CO@+[]SO=O)lc4=rcr43)6C2)CB/c1)r(7H7
remove 6 from position 21,CO@+[]SO=O)lc4=rcr43)C2)CB/c1)r(7H7
remove 4 from position 18,CO@+[]SO=O)lc4=rcr3)C2)CB/c1)r(7H7
remove H from position 32,CO@+[]SO=O)lc4=rcr3)C2)CB/c1)r(77
replace r at position 17 with ),CO@+[]SO=O)lc4=rc)3)C2)CB/c1)r(77
remove 4 from position 13,CO@+[]SO=O)lc=rc)3)C2)CB/c1)r(77
remove ) from position 21,CO@+[]SO=O)lc=rc)3)C2CB/c1)r(77
replace r at position 14 with 3,CO@+[]SO=O)lc=3c)3)C2CB/c1)r(77
add l at position 7,CO@+[]SlO=O)lc=3c)3)C2CB/c1)r(77
add - at position 23,CO@+[]SlO=O)lc=3c)3)C2C-B/c1)r(77
replace / at position 25 with (,CO@+[]SlO=O)lc=3c)3)C2C-B(c1)r(77
remove @ from position 2,CO+[]SlO=O)lc=3c)3)C2C-B(c1)r(77
replace O at position 9 with 5,CO+[]SlO=5)lc=3c)3)C2C-B(c1)r(77
remove ) from position 27,CO+[]SlO=5)lc=3c)3)C2C-B(c1r(77
replace 7 at position 29 with ),CO+[]SlO=5)lc=3c)3)C2C-B(c1r()7
replace 7 at position 30 with 4,CO+[]SlO=5)lc=3c)3)C2C-B(c1r()4
remove c from position 15,CO+[]SlO=5)lc=3)3)C2C-B(c1r()4
replace O at position 7 with 6,CO+[]Sl6=5)lc=3)3)C2C-B(c1r()4
remove C from position 20,CO+[]Sl6=5)lc=3)3)C2-B(c1r()4
replace C at position 0 with S,SO+[]Sl6=5)lc=3)3)C2-B(c1r()4
remove ) from position 27,SO+[]Sl6=5)lc=3)3)C2-B(c1r(4
remove S from position 5,SO+[]l6=5)lc=3)3)C2-B(c1r(4
remove ( from position 21,SO+[]l6=5)lc=3)3)C2-Bc1r(4
replace ] at position 4 with r,SO+[rl6=5)lc=3)3)C2-Bc1r(4
remove - from position 19,SO+[rl6=5)lc=3)3)C2Bc1r(4
add s at position 17,SO+[rl6=5)lc=3)3)sC2Bc1r(4
replace 1 at position 22 with /,SO+[rl6=5)lc=3)3)sC2Bc/r(4
replace ( at position 24 with F,SO+[rl6=5)lc=3)3)sC2Bc/rF4
remove = from position 12,SO+[rl6=5)lc3)3)sC2Bc/rF4
replace l at position 10 with -,SO+[rl6=5)-c3)3)sC2Bc/rF4
remove C from position 17,SO+[rl6=5)-c3)3)s2Bc/rF4
remove c from position 19,SO+[rl6=5)-c3)3)s2B/rF4
remove 3 from position 14,SO+[rl6=5)-c3))s2B/rF4
add r at position 2,SOr+[rl6=5)-c3))s2B/rF4
remove F from position 21,SOr+[rl6=5)-c3))s2B/r4
remove O from position 1,Sr+[rl6=5)-c3))s2B/r4
remove + from position 2,Sr[rl6=5)-c3))s2B/r4
remove 4 from position 19,Sr[rl6=5)-c3))s2B/r
replace 3 at position 11 with F,Sr[rl6=5)-cF))s2B/r
remove ) from position 12,Sr[rl6=5)-cF)s2B/r
remove r from position 1,S[rl6=5)-cF)s2B/r
add l at position 2,S[lrl6=5)-cF)s2B/r
remove c from position 10,S[lrl6=5)-F)s2B/r
add S at position 7,S[lrl6=S5)-F)s2B/r
replace S at position 0 with o,o[lrl6=S5)-F)s2B/r
remove F from position 11,o[lrl6=S5)-)s2B/r
remove l from position 2,o[rl6=S5)-)s2B/r
remove S from position 6,o[rl6=5)-)s2B/r
replace / at position 13 with N,o[rl6=5)-)s2BNr
remove 2 from position 11,o[rl6=5)-)sBNr
add O at position 2,o[Orl6=5)-)sBNr
replace ) at position 10 with ],o[Orl6=5)-]sBNr
add 6 at position 14,o[Orl6=5)-]sBN6r
remove s from position 11,o[Orl6=5)-]BN6r
remove = from position 6,o[Orl65)-]BN6r
replace r at position 3 with 4,o[O4l65)-]BN6r
add H at position 5,o[O4lH65)-]BN6r
replace [ at position 1 with F,oFO4lH65)-]BN6r
remove 6 from position 13,oFO4lH65)-]BNr
add H at position 9,oFO4lH65)H-]BNr
remove O from position 2,oF4lH65)H-]BNr
remove 6 from position 5,oF4lH5)H-]BNr
replace H at position 4 with [,oF4l[5)H-]BNr
remove 4 from position 2,oFl[5)H-]BNr
remove l from position 2,oF[5)H-]BNr
replace B at position 8 with [,oF[5)H-][Nr
remove 5 from position 3,oF[)H-][Nr
remove [ from position 2,oF)H-][Nr
remove [ from position 6,oF)H-]Nr
remove r from position 7,oF)H-]N
replace - at position 4 with B,oF)HB]N
remove N from position 6,oF)HB]
remove B from position 4,oF)H]
add C at position 5,oF)H]C
replace ] at position 4 with -,oF)H-C
add B at position 3,oF)BH-C
add c at position 2,oFc)BH-C
replace ) at position 3 with [,oFc[BH-C
add s at position 6,oFc[BHs-C
remove C from position 8,oFc[BHs-
remove [ from position 3,oFcBHs-
remove s from position 5,oFcBH-
replace B at position 3 with 5,oFc5H-
replace H at position 4 with +,oFc5+-
remove 5 from position 3,oFc+-
add / at position 1,o/Fc+-
remove F from position 2,o/c+-
add S at position 2,o/Sc+-
replace S at position 2 with #,o/#c+-
add 5 at position 5,o/#c+5-
remove o from position 0,/#c+5-
add 3 at position 5,/#c+53-
remove c from position 2,/#+53-
remove 3 from position 4,/#+5-
replace - at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
