log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add [ at position 3,oHc[BHs-
add C at position 8,oHc[BHs-C
remove s from position 6,oHc[BH-C
replace [ at position 3 with 3,oHc3BH-C
remove c from position 2,oH3BH-<PERSON>
remove B from position 3,oH3H-<PERSON>
replace - at position 4 with ],oH3H]C
remove C from position 5,oH3H]
add B at position 4,oH3HB]
add N at position 6,oH3HB]N
replace B at position 4 with -,oH3H-]N
add ) at position 7,oH3H-]N)
add [ at position 6,oH3H-][N)
add [ at position 2,oH[3H-][N)
add N at position 3,oH[N3H-][N)
replace [ at position 8 with (,oH[N3H-](N)
add c at position 2,oHc[N3H-](N)
add 4 at position 2,oH4c[N3H-](N)
replace [ at position 4 with H,oH4cHN3H-](N)
add ] at position 5,oH4cH]N3H-](N)
add O at position 2,oHO4cH]N3H-](N)
remove H from position 9,oHO4cH]N3-](N)
add 6 at position 13,oHO4cH]N3-](N6)
replace H at position 1 with =,o=O4cH]N3-](N6)
remove H from position 5,o=O4c]N3-](N6)
replace 4 at position 3 with r,o=Orc]N3-](N6)
add S at position 6,o=Orc]SN3-](N6)
add s at position 11,o=Orc]SN3-]s(N6)
remove 6 from position 14,o=Orc]SN3-]s(N)
replace ] at position 10 with H,o=Orc]SN3-Hs(N)
remove O from position 2,o=rc]SN3-Hs(N)
add C at position 11,o=rc]SN3-HsC(N)
replace N at position 13 with /,o=rc]SN3-HsC(/)
add S at position 6,o=rc]SSN3-HsC(/)
add l at position 2,o=lrc]SSN3-HsC(/)
add F at position 11,o=lrc]SSN3-FHsC(/)
replace o at position 0 with S,S=lrc]SSN3-FHsC(/)
remove S from position 7,S=lrc]SN3-FHsC(/)
add C at position 10,S=lrc]SN3-CFHsC(/)
remove l from position 2,S=rc]SN3-CFHsC(/)
add r at position 1,Sr=rc]SN3-CFHsC(/)
add + at position 12,Sr=rc]SN3-CF+HsC(/)
replace F at position 11 with 4,Sr=rc]SN3-C4+HsC(/)
add # at position 19,Sr=rc]SN3-C4+HsC(/)#
add 1 at position 2,Sr1=rc]SN3-C4+HsC(/)#
add C at position 1,SCr1=rc]SN3-C4+HsC(/)#
add C at position 21,SCr1=rc]SN3-C4+HsC(/)C#
remove r from position 2,SC1=rc]SN3-C4+HsC(/)C#
add B at position 14,SC1=rc]SN3-C4+BHsC(/)C#
add c at position 19,SC1=rc]SN3-C4+BHsC(c/)C#
add o at position 17,SC1=rc]SN3-C4+BHsoC(c/)C#
replace - at position 10 with 6,SC1=rc]SN36C4+BHsoC(c/)C#
add r at position 12,SC1=rc]SN36Cr4+BHsoC(c/)C#
replace C at position 24 with r,SC1=rc]SN36Cr4+BHsoC(c/)r#
replace / at position 22 with 1,SC1=rc]SN36Cr4+BHsoC(c1)r#
remove s from position 17,SC1=rc]SN36Cr4+BHoC(c1)r#
add c at position 19,SC1=rc]SN36Cr4+BHoCc(c1)r#
replace r at position 4 with @,SC1=@c]SN36Cr4+BHoCc(c1)r#
add / at position 21,SC1=@c]SN36Cr4+BHoCc(/c1)r#
remove B from position 15,SC1=@c]SN36Cr4+HoCc(/c1)r#
add 2 at position 18,SC1=@c]SN36Cr4+HoC2c(/c1)r#
replace S at position 0 with 7,7C1=@c]SN36Cr4+HoC2c(/c1)r#
add ) at position 20,7C1=@c]SN36Cr4+HoC2c)(/c1)r#
add l at position 11,7C1=@c]SN36lCr4+HoC2c)(/c1)r#
add ) at position 29,7C1=@c]SN36lCr4+HoC2c)(/c1)r#)
replace 3 at position 9 with =,7C1=@c]SN=6lCr4+HoC2c)(/c1)r#)
replace ) at position 29 with 7,7C1=@c]SN=6lCr4+HoC2c)(/c1)r#7
add = at position 13,7C1=@c]SN=6lC=r4+HoC2c)(/c1)r#7
add r at position 20,7C1=@c]SN=6lC=r4+HoCr2c)(/c1)r#7
remove 1 from position 2,7C=@c]SN=6lC=r4+HoCr2c)(/c1)r#7
remove 7 from position 0,C=@c]SN=6lC=r4+HoCr2c)(/c1)r#7
replace 6 at position 8 with c,C=@c]SN=clC=r4+HoCr2c)(/c1)r#7
add + at position 3,C=@+c]SN=clC=r4+HoCr2c)(/c1)r#7
remove H from position 16,C=@+c]SN=clC=r4+oCr2c)(/c1)r#7
replace 4 at position 14 with +,C=@+c]SN=clC=r++oCr2c)(/c1)r#7
add c at position 10,C=@+c]SN=cclC=r++oCr2c)(/c1)r#7
add 4 at position 13,C=@+c]SN=cclC4=r++oCr2c)(/c1)r#7
replace + at position 17 with ),C=@+c]SN=cclC4=r+)oCr2c)(/c1)r#7
add ( at position 32,C=@+c]SN=cclC4=r+)oCr2c)(/c1)r#7(
add 4 at position 18,C=@+c]SN=cclC4=r+)4oCr2c)(/c1)r#7(
add 6 at position 21,C=@+c]SN=cclC4=r+)4oC6r2c)(/c1)r#7(
replace 4 at position 18 with r,C=@+c]SN=cclC4=r+)roC6r2c)(/c1)r#7(
replace 6 at position 21 with c,C=@+c]SN=cclC4=r+)roCcr2c)(/c1)r#7(
remove ) from position 30,C=@+c]SN=cclC4=r+)roCcr2c)(/c1r#7(
add 3 at position 6,C=@+c]3SN=cclC4=r+)roCcr2c)(/c1r#7(
add ) at position 29,C=@+c]3SN=cclC4=r+)roCcr2c)(/)c1r#7(
add C at position 30,C=@+c]3SN=cclC4=r+)roCcr2c)(/)Cc1r#7(
remove r from position 33,C=@+c]3SN=cclC4=r+)roCcr2c)(/)Cc1#7(
add C at position 22,C=@+c]3SN=cclC4=r+)roCCcr2c)(/)Cc1#7(
add C at position 23,C=@+c]3SN=cclC4=r+)roCCCcr2c)(/)Cc1#7(
remove / from position 30,C=@+c]3SN=cclC4=r+)roCCCcr2c)()Cc1#7(
add B at position 30,C=@+c]3SN=cclC4=r+)roCCCcr2c)(B)Cc1#7(
add r at position 19,C=@+c]3SN=cclC4=r+)rroCCCcr2c)(B)Cc1#7(
add n at position 4,C=@+nc]3SN=cclC4=r+)rroCCCcr2c)(B)Cc1#7(
replace = at position 10 with 5,C=@+nc]3SN5cclC4=r+)rroCCCcr2c)(B)Cc1#7(
remove 2 from position 28,C=@+nc]3SN5cclC4=r+)rroCCCcrc)(B)Cc1#7(
remove 7 from position 37,C=@+nc]3SN5cclC4=r+)rroCCCcrc)(B)Cc1#(
add ) at position 17,C=@+nc]3SN5cclC4=)r+)rroCCCcrc)(B)Cc1#(
add = at position 7,C=@+nc]=3SN5cclC4=)r+)rroCCCcrc)(B)Cc1#(
add c at position 36,C=@+nc]=3SN5cclC4=)r+)rroCCCcrc)(B)Ccc1#(
replace = at position 17 with F,C=@+nc]=3SN5cclC4F)r+)rroCCCcrc)(B)Ccc1#(
replace ) at position 34 with B,C=@+nc]=3SN5cclC4F)r+)rroCCCcrc)(BBCcc1#(
add ) at position 36,C=@+nc]=3SN5cclC4F)r+)rroCCCcrc)(BBC)cc1#(
add c at position 19,C=@+nc]=3SN5cclC4F)cr+)rroCCCcrc)(BBC)cc1#(
replace F at position 17 with n,C=@+nc]=3SN5cclC4n)cr+)rroCCCcrc)(BBC)cc1#(
remove r from position 24,C=@+nc]=3SN5cclC4n)cr+)roCCCcrc)(BBC)cc1#(
add F at position 20,C=@+nc]=3SN5cclC4n)cFr+)roCCCcrc)(BBC)cc1#(
replace @ at position 2 with 2,C=2+nc]=3SN5cclC4n)cFr+)roCCCcrc)(BBC)cc1#(
add ( at position 9,C=2+nc]=3(SN5cclC4n)cFr+)roCCCcrc)(BBC)cc1#(
remove ( from position 43,C=2+nc]=3(SN5cclC4n)cFr+)roCCCcrc)(BBC)cc1#
add r at position 10,C=2+nc]=3(rSN5cclC4n)cFr+)roCCCcrc)(BBC)cc1#
remove ) from position 34,C=2+nc]=3(rSN5cclC4n)cFr+)roCCCcrc(BBC)cc1#
remove r from position 10,C=2+nc]=3(SN5cclC4n)cFr+)roCCCcrc(BBC)cc1#
add S at position 8,C=2+nc]=S3(SN5cclC4n)cFr+)roCCCcrc(BBC)cc1#
remove B from position 35,C=2+nc]=S3(SN5cclC4n)cFr+)roCCCcrc(BC)cc1#
remove F from position 22,C=2+nc]=S3(SN5cclC4n)cr+)roCCCcrc(BC)cc1#
replace 5 at position 13 with 3,C=2+nc]=S3(SN3cclC4n)cr+)roCCCcrc(BC)cc1#
add O at position 11,C=2+nc]=S3(OSN3cclC4n)cr+)roCCCcrc(BC)cc1#
replace r at position 32 with c,C=2+nc]=S3(OSN3cclC4n)cr+)roCCCccc(BC)cc1#
remove r from position 23,C=2+nc]=S3(OSN3cclC4n)c+)roCCCccc(BC)cc1#
replace C at position 29 with c,C=2+nc]=S3(OSN3cclC4n)c+)roCCcccc(BC)cc1#
remove S from position 12,C=2+nc]=S3(ON3cclC4n)c+)roCCcccc(BC)cc1#
add ) at position 12,C=2+nc]=S3(O)N3cclC4n)c+)roCCcccc(BC)cc1#
add I at position 4,C=2+Inc]=S3(O)N3cclC4n)c+)roCCcccc(BC)cc1#
replace I at position 4 with 1,C=2+1nc]=S3(O)N3cclC4n)c+)roCCcccc(BC)cc1#
replace 4 at position 20 with ],C=2+1nc]=S3(O)N3cclC]n)c+)roCCcccc(BC)cc1#
add S at position 5,C=2+1Snc]=S3(O)N3cclC]n)c+)roCCcccc(BC)cc1#
replace 2 at position 2 with I,C=I+1Snc]=S3(O)N3cclC]n)c+)roCCcccc(BC)cc1#
add O at position 3,C=IO+1Snc]=S3(O)N3cclC]n)c+)roCCcccc(BC)cc1#
replace + at position 4 with #,C=IO#1Snc]=S3(O)N3cclC]n)c+)roCCcccc(BC)cc1#
replace l at position 20 with c,C=IO#1Snc]=S3(O)N3cccC]n)c+)roCCcccc(BC)cc1#
add 2 at position 17,C=IO#1Snc]=S3(O)N23cccC]n)c+)roCCcccc(BC)cc1#
add B at position 10,C=IO#1Snc]B=S3(O)N23cccC]n)c+)roCCcccc(BC)cc1#
remove S from position 6,C=IO#1nc]B=S3(O)N23cccC]n)c+)roCCcccc(BC)cc1#
replace N at position 16 with c,C=IO#1nc]B=S3(O)c23cccC]n)c+)roCCcccc(BC)cc1#
replace n at position 24 with l,C=IO#1nc]B=S3(O)c23cccC]l)c+)roCCcccc(BC)cc1#
replace n at position 6 with c,C=IO#1cc]B=S3(O)c23cccC]l)c+)roCCcccc(BC)cc1#
add s at position 7,C=IO#1csc]B=S3(O)c23cccC]l)c+)roCCcccc(BC)cc1#
remove = from position 11,C=IO#1csc]BS3(O)c23cccC]l)c+)roCCcccc(BC)cc1#
replace 3 at position 18 with O,C=IO#1csc]BS3(O)c2OcccC]l)c+)roCCcccc(BC)cc1#
add - at position 33,C=IO#1csc]BS3(O)c2OcccC]l)c+)roCC-cccc(BC)cc1#
replace B at position 10 with (,C=IO#1csc](S3(O)c2OcccC]l)c+)roCC-cccc(BC)cc1#
replace o at position 30 with (,C=IO#1csc](S3(O)c2OcccC]l)c+)r(CC-cccc(BC)cc1#
replace ( at position 30 with N,C=IO#1csc](S3(O)c2OcccC]l)c+)rNCC-cccc(BC)cc1#
add 1 at position 37,C=IO#1csc](S3(O)c2OcccC]l)c+)rNCC-ccc1c(BC)cc1#
add ( at position 22,C=IO#1csc](S3(O)c2Occc(C]l)c+)rNCC-ccc1c(BC)cc1#
replace I at position 2 with C,C=CO#1csc](S3(O)c2Occc(C]l)c+)rNCC-ccc1c(BC)cc1#
remove ] from position 9,C=CO#1csc(S3(O)c2Occc(C]l)c+)rNCC-ccc1c(BC)cc1#
replace 3 at position 11 with C,C=CO#1csc(SC(O)c2Occc(C]l)c+)rNCC-ccc1c(BC)cc1#
remove O from position 17,C=CO#1csc(SC(O)c2ccc(C]l)c+)rNCC-ccc1c(BC)cc1#
add ) at position 29,C=CO#1csc(SC(O)c2ccc(C]l)c+)r)NCC-ccc1c(BC)cc1#
remove - from position 33,C=CO#1csc(SC(O)c2ccc(C]l)c+)r)NCCccc1c(BC)cc1#
remove ] from position 22,C=CO#1csc(SC(O)c2ccc(Cl)c+)r)NCCccc1c(BC)cc1#
remove 1 from position 35,C=CO#1csc(SC(O)c2ccc(Cl)c+)r)NCCcccc(BC)cc1#
remove B from position 37,C=CO#1csc(SC(O)c2ccc(Cl)c+)r)NCCcccc(C)cc1#
add N at position 10,C=CO#1csc(NSC(O)c2ccc(Cl)c+)r)NCCcccc(C)cc1#
add r at position 30,C=CO#1csc(NSC(O)c2ccc(Cl)c+)r)rNCCcccc(C)cc1#
replace # at position 4 with C,C=COC1csc(NSC(O)c2ccc(Cl)c+)r)rNCCcccc(C)cc1#
add O at position 1,CO=COC1csc(NSC(O)c2ccc(Cl)c+)r)rNCCcccc(C)cc1#
replace r at position 29 with n,CO=COC1csc(NSC(O)c2ccc(Cl)c+)n)rNCCcccc(C)cc1#
add l at position 41,CO=COC1csc(NSC(O)c2ccc(Cl)c+)n)rNCCcccc(Cl)cc1#
add 2 at position 22,CO=COC1csc(NSC(O)c2ccc2(Cl)c+)n)rNCCcccc(Cl)cc1#
replace O at position 4 with (,CO=C(C1csc(NSC(O)c2ccc2(Cl)c+)n)rNCCcccc(Cl)cc1#
add N at position 17,CO=C(C1csc(NSC(O)Nc2ccc2(Cl)c+)n)rNCCcccc(Cl)cc1#
add 1 at position 32,CO=C(C1csc(NSC(O)Nc2ccc2(Cl)c+)n1)rNCCcccc(Cl)cc1#
add ( at position 38,CO=C(C1csc(NSC(O)Nc2ccc2(Cl)c+)n1)rNCC(cccc(Cl)cc1#
remove r from position 34,CO=C(C1csc(NSC(O)Nc2ccc2(Cl)c+)n1)NCC(cccc(Cl)cc1#
add = at position 15,CO=C(C1csc(NSC(=O)Nc2ccc2(Cl)c+)n1)NCC(cccc(Cl)cc1#
remove C from position 0,O=C(C1csc(NSC(=O)Nc2ccc2(Cl)c+)n1)NCC(cccc(Cl)cc1#
remove S from position 11,O=C(C1csc(NC(=O)Nc2ccc2(Cl)c+)n1)NCC(cccc(Cl)cc1#
add 2 at position 29,O=C(C1csc(NC(=O)Nc2ccc2(Cl)c+2)n1)NCC(cccc(Cl)cc1#
add 1 at position 39,O=C(C1csc(NC(=O)Nc2ccc2(Cl)c+2)n1)NCC(c1ccc(Cl)cc1#
remove # from position 50,O=C(C1csc(NC(=O)Nc2ccc2(Cl)c+2)n1)NCC(c1ccc(Cl)cc1
remove 2 from position 22,O=C(C1csc(NC(=O)Nc2ccc(Cl)c+2)n1)NCC(c1ccc(Cl)cc1
add c at position 5,O=C(Cc1csc(NC(=O)Nc2ccc(Cl)c+2)n1)NCC(c1ccc(Cl)cc1
remove ( from position 37,O=C(Cc1csc(NC(=O)Nc2ccc(Cl)c+2)n1)NCCc1ccc(Cl)cc1
replace + at position 28 with c,O=C(Cc1csc(NC(=O)Nc2ccc(Cl)cc2)n1)NCCc1ccc(Cl)cc1
final: O=C(Cc1csc(NC(=O)Nc2ccc(Cl)cc2)n1)NCCc1ccc(Cl)cc1,O=C(Cc1csc(NC(=O)Nc2ccc(Cl)cc2)n1)NCCc1ccc(Cl)cc1
