log,state
initialize: CC1CCC(OC(=O)C2=NC3=C(C(=O)C[C@@H](c4ccccc4)C3)[C@H]2C)CC1,CC1CCC(OC(=O)C2=NC3=C(C(=O)C[C@@H](c4ccccc4)C3)[C@H]2C)CC1
replace [ at position 28 with +,CC1CCC(OC(=O)C2=NC3=C(C(=O)C+C@@H](c4ccccc4)C3)[C@H]2C)CC1
add s at position 58,CC1CCC(OC(=O)C2=NC3=C(C(=O)C+C@@H](c4ccccc4)C3)[C@H]2C)CC1s
replace c at position 37 with (,CC1CCC(OC(=O)C2=NC3=C(C(=O)C+C@@H](c4(cccc4)C3)[C@H]2C)CC1s
remove C from position 5,CC1CC(OC(=O)C2=NC3=C(C(=O)C+C@@H](c4(cccc4)C3)[C@H]2C)CC1s
add 2 at position 22,CC1CC(OC(=O)C2=NC3=C(C2(=O)C+C@@H](c4(cccc4)C3)[C@H]2C)CC1s
add # at position 50,CC1CC(OC(=O)C2=NC3=C(C2(=O)C+C@@H](c4(cccc4)C3)[C@#H]2C)CC1s
remove c from position 39,CC1CC(OC(=O)C2=NC3=C(C2(=O)C+C@@H](c4(ccc4)C3)[C@#H]2C)CC1s
remove C from position 29,CC1CC(OC(=O)C2=NC3=C(C2(=O)C+@@H](c4(ccc4)C3)[C@#H]2C)CC1s
add S at position 11,CC1CC(OC(=OS)C2=NC3=C(C2(=O)C+@@H](c4(ccc4)C3)[C@#H]2C)CC1s
add C at position 0,CCC1CC(OC(=OS)C2=NC3=C(C2(=O)C+@@H](c4(ccc4)C3)[C@#H]2C)CC1s
remove 2 from position 15,CCC1CC(OC(=OS)C=NC3=C(C2(=O)C+@@H](c4(ccc4)C3)[C@#H]2C)CC1s
add r at position 34,CCC1CC(OC(=OS)C=NC3=C(C2(=O)C+@@H]r(c4(ccc4)C3)[C@#H]2C)CC1s
remove ( from position 38,CCC1CC(OC(=OS)C=NC3=C(C2(=O)C+@@H]r(c4ccc4)C3)[C@#H]2C)CC1s
remove H from position 32,CCC1CC(OC(=OS)C=NC3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)CC1s
remove C from position 17,CCC1CC(OC(=OS)C=N3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)CC1s
replace C at position 4 with O,CCC1OC(OC(=OS)C=N3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)CC1s
remove C from position 54,CCC1OC(OC(=OS)C=N3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)C1s
remove = from position 15,CCC1OC(OC(=OS)CN3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)C1s
remove S from position 12,CCC1OC(OC(=O)CN3=C(C2(=O)C+@@]r(c4ccc4)C3)[C@#H]2C)C1s
remove 4 from position 33,CCC1OC(OC(=O)CN3=C(C2(=O)C+@@]r(cccc4)C3)[C@#H]2C)C1s
remove c from position 32,CCC1OC(OC(=O)CN3=C(C2(=O)C+@@]r(ccc4)C3)[C@#H]2C)C1s
add - at position 21,CCC1OC(OC(=O)CN3=C(C2-(=O)C+@@]r(ccc4)C3)[C@#H]2C)C1s
remove C from position 42,CCC1OC(OC(=O)CN3=C(C2-(=O)C+@@]r(ccc4)C3)[@#H]2C)C1s
remove 1 from position 3,CCCOC(OC(=O)CN3=C(C2-(=O)C+@@]r(ccc4)C3)[@#H]2C)C1s
add / at position 31,CCCOC(OC(=O)CN3=C(C2-(=O)C+@@]r/(ccc4)C3)[@#H]2C)C1s
add - at position 13,CCCOC(OC(=O)C-N3=C(C2-(=O)C+@@]r/(ccc4)C3)[@#H]2C)C1s
add ] at position 22,CCCOC(OC(=O)C-N3=C(C2-](=O)C+@@]r/(ccc4)C3)[@#H]2C)C1s
add - at position 33,CCCOC(OC(=O)C-N3=C(C2-](=O)C+@@]r-/(ccc4)C3)[@#H]2C)C1s
remove @ from position 29,CCCOC(OC(=O)C-N3=C(C2-](=O)C+@]r-/(ccc4)C3)[@#H]2C)C1s
add O at position 17,CCCOC(OC(=O)C-N3=OC(C2-](=O)C+@]r-/(ccc4)C3)[@#H]2C)C1s
replace ) at position 11 with 4,CCCOC(OC(=O4C-N3=OC(C2-](=O)C+@]r-/(ccc4)C3)[@#H]2C)C1s
add - at position 54,CCCOC(OC(=O4C-N3=OC(C2-](=O)C+@]r-/(ccc4)C3)[@#H]2C)C1-s
replace + at position 29 with (,CCCOC(OC(=O4C-N3=OC(C2-](=O)C(@]r-/(ccc4)C3)[@#H]2C)C1-s
remove ] from position 48,CCCOC(OC(=O4C-N3=OC(C2-](=O)C(@]r-/(ccc4)C3)[@#H2C)C1-s
remove 2 from position 21,CCCOC(OC(=O4C-N3=OC(C-](=O)C(@]r-/(ccc4)C3)[@#H2C)C1-s
remove @ from position 29,CCCOC(OC(=O4C-N3=OC(C-](=O)C(]r-/(ccc4)C3)[@#H2C)C1-s
remove 1 from position 50,CCCOC(OC(=O4C-N3=OC(C-](=O)C(]r-/(ccc4)C3)[@#H2C)C-s
replace ) at position 38 with r,CCCOC(OC(=O4C-N3=OC(C-](=O)C(]r-/(ccc4rC3)[@#H2C)C-s
add ] at position 46,CCCOC(OC(=O4C-N3=OC(C-](=O)C(]r-/(ccc4rC3)[@#H]2C)C-s
remove ( from position 28,CCCOC(OC(=O4C-N3=OC(C-](=O)C]r-/(ccc4rC3)[@#H]2C)C-s
remove C from position 7,CCCOC(O(=O4C-N3=OC(C-](=O)C]r-/(ccc4rC3)[@#H]2C)C-s
replace C at position 26 with =,CCCOC(O(=O4C-N3=OC(C-](=O)=]r-/(ccc4rC3)[@#H]2C)C-s
replace c at position 33 with +,CCCOC(O(=O4C-N3=OC(C-](=O)=]r-/(c+c4rC3)[@#H]2C)C-s
replace ( at position 31 with =,CCCOC(O(=O4C-N3=OC(C-](=O)=]r-/=c+c4rC3)[@#H]2C)C-s
replace # at position 42 with 4,CCCOC(O(=O4C-N3=OC(C-](=O)=]r-/=c+c4rC3)[@4H]2C)C-s
remove ( from position 22,CCCOC(O(=O4C-N3=OC(C-]=O)=]r-/=c+c4rC3)[@4H]2C)C-s
add c at position 44,CCCOC(O(=O4C-N3=OC(C-]=O)=]r-/=c+c4rC3)[@4H]c2C)C-s
replace 3 at position 37 with s,CCCOC(O(=O4C-N3=OC(C-]=O)=]r-/=c+c4rCs)[@4H]c2C)C-s
replace O at position 9 with o,CCCOC(O(=o4C-N3=OC(C-]=O)=]r-/=c+c4rCs)[@4H]c2C)C-s
replace C at position 46 with 5,CCCOC(O(=o4C-N3=OC(C-]=O)=]r-/=c+c4rCs)[@4H]c25)C-s
replace - at position 28 with ],CCCOC(O(=o4C-N3=OC(C-]=O)=]r]/=c+c4rCs)[@4H]c25)C-s
add 1 at position 25,CCCOC(O(=o4C-N3=OC(C-]=O)1=]r]/=c+c4rCs)[@4H]c25)C-s
add c at position 18,CCCOC(O(=o4C-N3=OCc(C-]=O)1=]r]/=c+c4rCs)[@4H]c25)C-s
add l at position 32,CCCOC(O(=o4C-N3=OCc(C-]=O)1=]r]/l=c+c4rCs)[@4H]c25)C-s
add 5 at position 44,CCCOC(O(=o4C-N3=OCc(C-]=O)1=]r]/l=c+c4rCs)[@54H]c25)C-s
remove r from position 29,CCCOC(O(=o4C-N3=OCc(C-]=O)1=]]/l=c+c4rCs)[@54H]c25)C-s
add n at position 16,CCCOC(O(=o4C-N3=nOCc(C-]=O)1=]]/l=c+c4rCs)[@54H]c25)C-s
add s at position 27,CCCOC(O(=o4C-N3=nOCc(C-]=O)s1=]]/l=c+c4rCs)[@54H]c25)C-s
add 1 at position 25,CCCOC(O(=o4C-N3=nOCc(C-]=1O)s1=]]/l=c+c4rCs)[@54H]c25)C-s
remove s from position 56,CCCOC(O(=o4C-N3=nOCc(C-]=1O)s1=]]/l=c+c4rCs)[@54H]c25)C-
remove = from position 15,CCCOC(O(=o4C-N3nOCc(C-]=1O)s1=]]/l=c+c4rCs)[@54H]c25)C-
replace = at position 23 with s,CCCOC(O(=o4C-N3nOCc(C-]s1O)s1=]]/l=c+c4rCs)[@54H]c25)C-
replace / at position 32 with r,CCCOC(O(=o4C-N3nOCc(C-]s1O)s1=]]rl=c+c4rCs)[@54H]c25)C-
remove C from position 11,CCCOC(O(=o4-N3nOCc(C-]s1O)s1=]]rl=c+c4rCs)[@54H]c25)C-
replace 3 at position 13 with 5,CCCOC(O(=o4-N5nOCc(C-]s1O)s1=]]rl=c+c4rCs)[@54H]c25)C-
add F at position 22,CCCOC(O(=o4-N5nOCc(C-]Fs1O)s1=]]rl=c+c4rCs)[@54H]c25)C-
add s at position 49,CCCOC(O(=o4-N5nOCc(C-]Fs1O)s1=]]rl=c+c4rCs)[@54H]sc25)C-
remove ] from position 21,CCCOC(O(=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[@54H]sc25)C-
add I at position 3,CCCIOC(O(=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[@54H]sc25)C-
add l at position 9,CCCIOC(O(l=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[@54H]sc25)C-
remove @ from position 45,CCCIOC(O(l=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]sc25)C-
remove C from position 0,CCIOC(O(l=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]sc25)C-
remove O from position 3,CCIC(O(l=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]sc25)C-
remove c from position 48,CCIC(O(l=o4-N5nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]s25)C-
replace 5 at position 13 with (,CCIC(O(l=o4-N(nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]s25)C-
remove s from position 47,CCIC(O(l=o4-N(nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[54H]25)C-
remove 5 from position 43,CCIC(O(l=o4-N(nOCc(C-Fs1O)s1=]]rl=c+c4rCs)[4H]25)C-
remove c from position 17,CCIC(O(l=o4-N(nOC(C-Fs1O)s1=]]rl=c+c4rCs)[4H]25)C-
remove - from position 19,CCIC(O(l=o4-N(nOC(CFs1O)s1=]]rl=c+c4rCs)[4H]25)C-
remove ] from position 43,CCIC(O(l=o4-N(nOC(CFs1O)s1=]]rl=c+c4rCs)[4H25)C-
remove o from position 9,CCIC(O(l=4-N(nOC(CFs1O)s1=]]rl=c+c4rCs)[4H25)C-
replace O at position 5 with c,CCIC(c(l=4-N(nOC(CFs1O)s1=]]rl=c+c4rCs)[4H25)C-
remove ] from position 26,CCIC(c(l=4-N(nOC(CFs1O)s1=]rl=c+c4rCs)[4H25)C-
remove s from position 36,CCIC(c(l=4-N(nOC(CFs1O)s1=]rl=c+c4rC)[4H25)C-
remove l from position 7,CCIC(c(=4-N(nOC(CFs1O)s1=]rl=c+c4rC)[4H25)C-
remove F from position 17,CCIC(c(=4-N(nOC(Cs1O)s1=]rl=c+c4rC)[4H25)C-
add 7 at position 37,CCIC(c(=4-N(nOC(Cs1O)s1=]rl=c+c4rC)[47H25)C-
add 2 at position 28,CCIC(c(=4-N(nOC(Cs1O)s1=]rl=2c+c4rC)[47H25)C-
replace N at position 10 with 7,CCIC(c(=4-7(nOC(Cs1O)s1=]rl=2c+c4rC)[47H25)C-
remove ( from position 4,CCICc(=4-7(nOC(Cs1O)s1=]rl=2c+c4rC)[47H25)C-
remove ) from position 19,CCICc(=4-7(nOC(Cs1Os1=]rl=2c+c4rC)[47H25)C-
remove 4 from position 30,CCICc(=4-7(nOC(Cs1Os1=]rl=2c+crC)[47H25)C-
add B at position 40,CCICc(=4-7(nOC(Cs1Os1=]rl=2c+crC)[47H25)BC-
add 6 at position 37,CCICc(=4-7(nOC(Cs1Os1=]rl=2c+crC)[47H625)BC-
remove ] from position 22,CCICc(=4-7(nOC(Cs1Os1=rl=2c+crC)[47H625)BC-
add r at position 33,CCICc(=4-7(nOC(Cs1Os1=rl=2c+crC)[r47H625)BC-
remove C from position 42,CCICc(=4-7(nOC(Cs1Os1=rl=2c+crC)[r47H625)B-
remove C from position 1,CICc(=4-7(nOC(Cs1Os1=rl=2c+crC)[r47H625)B-
replace C at position 29 with S,CICc(=4-7(nOC(Cs1Os1=rl=2c+crS)[r47H625)B-
replace I at position 1 with 6,C6Cc(=4-7(nOC(Cs1Os1=rl=2c+crS)[r47H625)B-
replace ) at position 30 with +,C6Cc(=4-7(nOC(Cs1Os1=rl=2c+crS+[r47H625)B-
replace 2 at position 37 with 6,C6Cc(=4-7(nOC(Cs1Os1=rl=2c+crS+[r47H665)B-
replace s at position 18 with 4,C6Cc(=4-7(nOC(Cs1O41=rl=2c+crS+[r47H665)B-
remove 6 from position 37,C6Cc(=4-7(nOC(Cs1O41=rl=2c+crS+[r47H65)B-
remove 4 from position 18,C6Cc(=4-7(nOC(Cs1O1=rl=2c+crS+[r47H65)B-
remove 4 from position 32,C6Cc(=4-7(nOC(Cs1O1=rl=2c+crS+[r7H65)B-
replace O at position 17 with ),C6Cc(=4-7(nOC(Cs1)1=rl=2c+crS+[r7H65)B-
remove - from position 38,C6Cc(=4-7(nOC(Cs1)1=rl=2c+crS+[r7H65)B
add 5 at position 32,C6Cc(=4-7(nOC(Cs1)1=rl=2c+crS+[r57H65)B
replace + at position 29 with 4,C6Cc(=4-7(nOC(Cs1)1=rl=2c+crS4[r57H65)B
add H at position 33,C6Cc(=4-7(nOC(Cs1)1=rl=2c+crS4[r5H7H65)B
remove - from position 7,C6Cc(=47(nOC(Cs1)1=rl=2c+crS4[r5H7H65)B
replace ) at position 16 with 7,C6Cc(=47(nOC(Cs171=rl=2c+crS4[r5H7H65)B
add 7 at position 1,C76Cc(=47(nOC(Cs171=rl=2c+crS4[r5H7H65)B
add 1 at position 2,C716Cc(=47(nOC(Cs171=rl=2c+crS4[r5H7H65)B
remove = from position 20,C716Cc(=47(nOC(Cs171rl=2c+crS4[r5H7H65)B
remove r from position 27,C716Cc(=47(nOC(Cs171rl=2c+cS4[r5H7H65)B
replace C at position 4 with o,C716oc(=47(nOC(Cs171rl=2c+cS4[r5H7H65)B
replace 1 at position 19 with 4,C716oc(=47(nOC(Cs174rl=2c+cS4[r5H7H65)B
remove ) from position 37,C716oc(=47(nOC(Cs174rl=2c+cS4[r5H7H65B
add ) at position 32,C716oc(=47(nOC(Cs174rl=2c+cS4[r5)H7H65B
remove 7 from position 1,C16oc(=47(nOC(Cs174rl=2c+cS4[r5)H7H65B
remove = from position 21,C16oc(=47(nOC(Cs174rl2c+cS4[r5)H7H65B
replace C at position 14 with /,C16oc(=47(nOC(/s174rl2c+cS4[r5)H7H65B
remove 7 from position 8,C16oc(=4(nOC(/s174rl2c+cS4[r5)H7H65B
replace c at position 4 with -,C16o-(=4(nOC(/s174rl2c+cS4[r5)H7H65B
remove 1 from position 15,C16o-(=4(nOC(/s74rl2c+cS4[r5)H7H65B
remove 6 from position 32,C16o-(=4(nOC(/s74rl2c+cS4[r5)H7H5B
replace [ at position 25 with o,C16o-(=4(nOC(/s74rl2c+cS4or5)H7H5B
replace ( at position 8 with [,C16o-(=4[nOC(/s74rl2c+cS4or5)H7H5B
replace ) at position 28 with (,C16o-(=4[nOC(/s74rl2c+cS4or5(H7H5B
remove S from position 23,C16o-(=4[nOC(/s74rl2c+c4or5(H7H5B
replace 5 at position 26 with r,C16o-(=4[nOC(/s74rl2c+c4orr(H7H5B
add 5 at position 4,C16o5-(=4[nOC(/s74rl2c+c4orr(H7H5B
add 6 at position 12,C16o5-(=4[nO6C(/s74rl2c+c4orr(H7H5B
replace 5 at position 33 with [,C16o5-(=4[nO6C(/s74rl2c+c4orr(H7H[B
replace 7 at position 31 with (,C16o5-(=4[nO6C(/s74rl2c+c4orr(H(H[B
add l at position 5,C16o5l-(=4[nO6C(/s74rl2c+c4orr(H(H[B
remove l from position 21,C16o5l-(=4[nO6C(/s74r2c+c4orr(H(H[B
add S at position 15,C16o5l-(=4[nO6CS(/s74r2c+c4orr(H(H[B
replace 1 at position 1 with o,Co6o5l-(=4[nO6CS(/s74r2c+c4orr(H(H[B
remove c from position 23,Co6o5l-(=4[nO6CS(/s74r2+c4orr(H(H[B
remove 5 from position 4,Co6ol-(=4[nO6CS(/s74r2+c4orr(H(H[B
remove C from position 13,Co6ol-(=4[nO6S(/s74r2+c4orr(H(H[B
replace [ at position 9 with C,Co6ol-(=4CnO6S(/s74r2+c4orr(H(H[B
replace s at position 16 with N,Co6ol-(=4CnO6S(/N74r2+c4orr(H(H[B
remove ( from position 29,Co6ol-(=4CnO6S(/N74r2+c4orr(HH[B
remove 4 from position 23,Co6ol-(=4CnO6S(/N74r2+corr(HH[B
remove S from position 13,Co6ol-(=4CnO6(/N74r2+corr(HH[B
replace = at position 7 with 4,Co6ol-(44CnO6(/N74r2+corr(HH[B
add H at position 10,Co6ol-(44CHnO6(/N74r2+corr(HH[B
replace o at position 3 with F,Co6Fl-(44CHnO6(/N74r2+corr(HH[B
remove H from position 27,Co6Fl-(44CHnO6(/N74r2+corr(H[B
add H at position 18,Co6Fl-(44CHnO6(/N7H4r2+corr(H[B
remove - from position 5,Co6Fl(44CHnO6(/N7H4r2+corr(H[B
remove O from position 11,Co6Fl(44CHn6(/N7H4r2+corr(H[B
replace C at position 8 with [,Co6Fl(44[Hn6(/N7H4r2+corr(H[B
remove ( from position 5,Co6Fl44[Hn6(/N7H4r2+corr(H[B
remove 4 from position 5,Co6Fl4[Hn6(/N7H4r2+corr(H[B
replace 2 at position 17 with [,Co6Fl4[Hn6(/N7H4r[+corr(H[B
remove [ from position 6,Co6Fl4Hn6(/N7H4r[+corr(H[B
remove H from position 23,Co6Fl4Hn6(/N7H4r[+corr([B
add H at position 23,Co6Fl4Hn6(/N7H4r[+corr(H[B
replace H at position 13 with C,Co6Fl4Hn6(/N7C4r[+corr(H[B
remove o from position 19,Co6Fl4Hn6(/N7C4r[+crr(H[B
remove r from position 15,Co6Fl4Hn6(/N7C4[+crr(H[B
remove B from position 23,Co6Fl4Hn6(/N7C4[+crr(H[
remove N from position 11,Co6Fl4Hn6(/7C4[+crr(H[
remove [ from position 21,Co6Fl4Hn6(/7C4[+crr(H
remove C from position 12,Co6Fl4Hn6(/74[+crr(H
replace l at position 4 with +,Co6F+4Hn6(/74[+crr(H
replace c at position 15 with +,Co6F+4Hn6(/74[++rr(H
remove + from position 4,Co6F4Hn6(/74[++rr(H
replace ( at position 8 with 3,Co6F4Hn63/74[++rr(H
replace 6 at position 2 with 5,Co5F4Hn63/74[++rr(H
remove / from position 9,Co5F4Hn6374[++rr(H
remove [ from position 11,Co5F4Hn6374++rr(H
remove n from position 6,Co5F4H6374++rr(H
remove ( from position 14,Co5F4H6374++rrH
remove 3 from position 7,Co5F4H674++rrH
replace o at position 1 with F,CF5F4H674++rrH
replace 7 at position 7 with ),CF5F4H6)4++rrH
add I at position 2,CFI5F4H6)4++rrH
replace I at position 2 with 7,CF75F4H6)4++rrH
remove H from position 14,CF75F4H6)4++rr
add / at position 0,/CF75F4H6)4++rr
replace F at position 5 with N,/CF75N4H6)4++rr
add c at position 1,/cCF75N4H6)4++rr
remove + from position 13,/cCF75N4H6)4+rr
remove 7 from position 4,/cCF5N4H6)4+rr
replace 4 at position 6 with [,/cCF5N[H6)4+rr
remove 4 from position 10,/cCF5N[H6)+rr
add # at position 6,/cCF5N#[H6)+rr
remove 6 from position 9,/cCF5N#[H)+rr
replace r at position 11 with 5,/cCF5N#[H)+5r
replace ) at position 9 with 3,/cCF5N#[H3+5r
add 1 at position 13,/cCF5N#[H3+5r1
remove 3 from position 9,/cCF5N#[H+5r1
remove H from position 8,/cCF5N#[+5r1
replace + at position 8 with 4,/cCF5N#[45r1
replace # at position 6 with =,/cCF5N=[45r1
remove 5 from position 9,/cCF5N=[4r1
remove F from position 3,/cC5N=[4r1
replace C at position 2 with /,/c/5N=[4r1
replace / at position 2 with (,/c(5N=[4r1
add r at position 8,/c(5N=[4rr1
replace 1 at position 10 with H,/c(5N=[4rrH
add # at position 7,/c(5N=[#4rrH
remove c from position 1,/(5N=[#4rrH
remove ( from position 1,/5N=[#4rrH
add l at position 3,/5Nl=[#4rrH
remove 4 from position 7,/5Nl=[#rrH
remove l from position 3,/5N=[#rrH
remove H from position 8,/5N=[#rr
remove r from position 7,/5N=[#r
replace # at position 5 with +,/5N=[+r
remove r from position 6,/5N=[+
add C at position 6,/5N=[+C
add 7 at position 2,/57N=[+C
remove 7 from position 2,/5N=[+C
replace / at position 0 with C,C5N=[+C
remove N from position 2,C5=[+C
remove = from position 2,C5[+C
replace + at position 3 with o,C5[oC
remove [ from position 2,C5oC
replace 5 at position 1 with ),C)oC
replace ) at position 1 with B,CBoC
add 3 at position 0,3CBoC
replace C at position 4 with 3,3CBo3
add 1 at position 3,3CB1o3
add - at position 4,3CB1-o3
replace B at position 2 with ],3C]1-o3
remove 1 from position 3,3C]-o3
remove o from position 4,3C]-3
replace C at position 1 with O,3O]-3
add # at position 4,3O]-#3
add l at position 2,3Ol]-#3
remove # from position 5,3Ol]-3
replace ] at position 3 with =,3Ol=-3
remove l from position 2,3O=-3
replace 3 at position 4 with F,3O=-F
add ] at position 4,3O=-]F
remove F from position 5,3O=-]
remove = from position 2,3O-]
remove 3 from position 0,O-]
add r at position 0,rO-]
replace O at position 1 with 7,r7-]
remove r from position 0,7-]
remove 7 from position 0,-]
replace - at position 0 with o,o]
add r at position 0,ro]
replace o at position 1 with c,rc]
add c at position 1,rcc]
add ( at position 3,rcc(]
add C at position 5,rcc(]C
add ( at position 4,rcc((]C
replace ] at position 5 with C,rcc((CC
add = at position 0,=rcc((CC
replace = at position 0 with [,[rcc((CC
replace c at position 2 with 2,[r2c((CC
replace C at position 7 with I,[r2c((CI
add 2 at position 4,[r2c2((CI
remove 2 from position 2,[rc2((CI
remove C from position 6,[rc2((I
remove c from position 2,[r2((I
remove [ from position 0,r2((I
replace ( at position 2 with O,r2O(I
remove 2 from position 1,rO(I
remove ( from position 2,rOI
remove I from position 2,rO
remove r from position 0,O
remove O from position 0,
final: ,
