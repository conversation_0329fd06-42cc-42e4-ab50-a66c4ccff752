log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add F at position 2,o/Fc+-
remove / from position 1,oFc+-
add 5 at position 3,oFc5+-
replace + at position 4 with H,oFc5H-
replace 5 at position 3 with B,oFcBH-
add s at position 5,oFcBHs-
add [ at position 3,oFc[BHs-
add C at position 8,oFc[BHs-C
remove s from position 6,oFc[BH-C
replace [ at position 3 with 2,oFc2BH-C
remove c from position 2,oF2BH-<PERSON>
remove B from position 3,oF2H-<PERSON>
replace - at position 4 with [,oF2H[C
remove C from position 5,oF2H[
add B at position 4,oF2HB[
add N at position 6,oF2HB[N
replace B at position 4 with -,oF2H-[N
add r at position 7,oF2H-[Nr
add [ at position 6,oF2H-[[Nr
add [ at position 2,oF[2H-[[Nr
add 5 at position 3,oF[52H-[[Nr
replace [ at position 8 with B,oF[52H-[BNr
add l at position 2,oFl[52H-[BNr
add 4 at position 2,oF4l[52H-[BNr
replace [ at position 4 with H,oF4lH52H-[BNr
add 6 at position 5,oF4lH652H-[BNr
add O at position 2,oFO4lH652H-[BNr
remove H from position 9,oFO4lH652-[BNr
add 6 at position 13,oFO4lH652-[BN6r
replace F at position 1 with c,ocO4lH652-[BN6r
remove H from position 5,ocO4l652-[BN6r
replace 4 at position 3 with r,ocOrl652-[BN6r
add = at position 6,ocOrl6=52-[BN6r
add s at position 11,ocOrl6=52-[sBN6r
remove 6 from position 14,ocOrl6=52-[sBNr
replace [ at position 10 with c,ocOrl6=52-csBNr
remove O from position 2,ocrl6=52-csBNr
add 2 at position 11,ocrl6=52-cs2BNr
replace N at position 13 with /,ocrl6=52-cs2B/r
add S at position 6,ocrl6=S52-cs2B/r
add l at position 2,oclrl6=S52-cs2B/r
add F at position 11,oclrl6=S52-Fcs2B/r
replace o at position 0 with S,Sclrl6=S52-Fcs2B/r
remove S from position 7,Sclrl6=52-Fcs2B/r
add c at position 10,Sclrl6=52-cFcs2B/r
remove l from position 2,Scrl6=52-cFcs2B/r
add r at position 1,Srcrl6=52-cFcs2B/r
add ) at position 12,Srcrl6=52-cF)cs2B/r
replace F at position 11 with 3,Srcrl6=52-c3)cs2B/r
add 4 at position 19,Srcrl6=52-c3)cs2B/r4
add + at position 2,Sr+crl6=52-c3)cs2B/r4
add = at position 1,S=r+crl6=52-c3)cs2B/r4
add F at position 21,S=r+crl6=52-c3)cs2B/rF4
remove r from position 2,S=+crl6=52-c3)cs2B/rF4
add ) at position 14,S=+crl6=52-c3))cs2B/rF4
add s at position 19,S=+crl6=52-c3))cs2Bs/rF4
add ) at position 17,S=+crl6=52-c3))cs)2Bs/rF4
replace - at position 10 with l,S=+crl6=52lc3))cs)2Bs/rF4
add = at position 12,S=+crl6=52lc=3))cs)2Bs/rF4
replace F at position 24 with (,S=+crl6=52lc=3))cs)2Bs/r(4
replace / at position 22 with 1,S=+crl6=52lc=3))cs)2Bs1r(4
remove s from position 17,S=+crl6=52lc=3))c)2Bs1r(4
add - at position 19,S=+crl6=52lc=3))c)2-Bs1r(4
replace r at position 4 with ],S=+c]l6=52lc=3))c)2-Bs1r(4
add ( at position 21,S=+c]l6=52lc=3))c)2-B(s1r(4
add S at position 5,S=+c]Sl6=52lc=3))c)2-B(s1r(4
add ) at position 27,S=+c]Sl6=52lc=3))c)2-B(s1r()4
replace S at position 0 with C,C=+c]Sl6=52lc=3))c)2-B(s1r()4
add N at position 20,C=+c]Sl6=52lc=3))c)2N-B(s1r()4
replace 6 at position 7 with O,C=+c]SlO=52lc=3))c)2N-B(s1r()4
add ) at position 15,C=+c]SlO=52lc=3)))c)2N-B(s1r()4
replace 4 at position 30 with 7,C=+c]SlO=52lc=3)))c)2N-B(s1r()7
replace ) at position 29 with 7,C=+c]SlO=52lc=3)))c)2N-B(s1r(77
add ) at position 27,C=+c]SlO=52lc=3)))c)2N-B(s1)r(77
replace 5 at position 9 with c,C=+c]SlO=c2lc=3)))c)2N-B(s1)r(77
add @ at position 2,C=@+c]SlO=c2lc=3)))c)2N-B(s1)r(77
replace ( at position 25 with /,C=@+c]SlO=c2lc=3)))c)2N-B/s1)r(77
remove - from position 23,C=@+c]SlO=c2lc=3)))c)2NB/s1)r(77
remove l from position 7,C=@+c]SO=c2lc=3)))c)2NB/s1)r(77
replace 3 at position 14 with r,C=@+c]SO=c2lc=r)))c)2NB/s1)r(77
add ) at position 21,C=@+c]SO=c2lc=r)))c)2)NB/s1)r(77
add 4 at position 13,C=@+c]SO=c2lc4=r)))c)2)NB/s1)r(77
replace ) at position 17 with r,C=@+c]SO=c2lc4=r)r)c)2)NB/s1)r(77
add H at position 32,C=@+c]SO=c2lc4=r)r)c)2)NB/s1)r(7H7
add 4 at position 18,C=@+c]SO=c2lc4=r)r4)c)2)NB/s1)r(7H7
add 6 at position 21,C=@+c]SO=c2lc4=r)r4)c6)2)NB/s1)r(7H7
replace 4 at position 18 with o,C=@+c]SO=c2lc4=r)ro)c6)2)NB/s1)r(7H7
replace 6 at position 21 with r,C=@+c]SO=c2lc4=r)ro)cr)2)NB/s1)r(7H7
remove ) from position 30,C=@+c]SO=c2lc4=r)ro)cr)2)NB/s1r(7H7
add 4 at position 6,C=@+c]4SO=c2lc4=r)ro)cr)2)NB/s1r(7H7
add ) at position 29,C=@+c]4SO=c2lc4=r)ro)cr)2)NB/)s1r(7H7
add n at position 30,C=@+c]4SO=c2lc4=r)ro)cr)2)NB/)ns1r(7H7
remove r from position 33,C=@+c]4SO=c2lc4=r)ro)cr)2)NB/)ns1(7H7
add F at position 22,C=@+c]4SO=c2lc4=r)ro)cFr)2)NB/)ns1(7H7
add n at position 23,C=@+c]4SO=c2lc4=r)ro)cFnr)2)NB/)ns1(7H7
remove / from position 30,C=@+c]4SO=c2lc4=r)ro)cFnr)2)NB)ns1(7H7
add c at position 30,C=@+c]4SO=c2lc4=r)ro)cFnr)2)NBc)ns1(7H7
add ) at position 19,C=@+c]4SO=c2lc4=r)r)o)cFnr)2)NBc)ns1(7H7
add n at position 4,C=@+nc]4SO=c2lc4=r)r)o)cFnr)2)NBc)ns1(7H7
replace = at position 10 with 5,C=@+nc]4SO5c2lc4=r)r)o)cFnr)2)NBc)ns1(7H7
remove 2 from position 28,C=@+nc]4SO5c2lc4=r)r)o)cFnr))NBc)ns1(7H7
remove 7 from position 37,C=@+nc]4SO5c2lc4=r)r)o)cFnr))NBc)ns1(H7
add C at position 17,C=@+nc]4SO5c2lc4=Cr)r)o)cFnr))NBc)ns1(H7
add = at position 7,C=@+nc]=4SO5c2lc4=Cr)r)o)cFnr))NBc)ns1(H7
add # at position 36,C=@+nc]=4SO5c2lc4=Cr)r)o)cFnr))NBc)n#s1(H7
replace = at position 17 with F,C=@+nc]=4SO5c2lc4FCr)r)o)cFnr))NBc)n#s1(H7
replace ) at position 34 with 1,C=@+nc]=4SO5c2lc4FCr)r)o)cFnr))NBc1n#s1(H7
add c at position 36,C=@+nc]=4SO5c2lc4FCr)r)o)cFnr))NBc1nc#s1(H7
add ( at position 19,C=@+nc]=4SO5c2lc4FC(r)r)o)cFnr))NBc1nc#s1(H7
replace F at position 17 with n,C=@+nc]=4SO5c2lc4nC(r)r)o)cFnr))NBc1nc#s1(H7
remove 7 from position 43,C=@+nc]=4SO5c2lc4nC(r)r)o)cFnr))NBc1nc#s1(H
add F at position 20,C=@+nc]=4SO5c2lc4nC(Fr)r)o)cFnr))NBc1nc#s1(H
replace @ at position 2 with 2,C=2+nc]=4SO5c2lc4nC(Fr)r)o)cFnr))NBc1nc#s1(H
add - at position 9,C=2+nc]=4-SO5c2lc4nC(Fr)r)o)cFnr))NBc1nc#s1(H
remove ( from position 43,C=2+nc]=4-SO5c2lc4nC(Fr)r)o)cFnr))NBc1nc#s1H
add r at position 10,C=2+nc]=4-rSO5c2lc4nC(Fr)r)o)cFnr))NBc1nc#s1H
remove ) from position 34,C=2+nc]=4-rSO5c2lc4nC(Fr)r)o)cFnr)NBc1nc#s1H
remove r from position 10,C=2+nc]=4-SO5c2lc4nC(Fr)r)o)cFnr)NBc1nc#s1H
add S at position 8,C=2+nc]=S4-SO5c2lc4nC(Fr)r)o)cFnr)NBc1nc#s1H
remove B from position 35,C=2+nc]=S4-SO5c2lc4nC(Fr)r)o)cFnr)Nc1nc#s1H
remove F from position 22,C=2+nc]=S4-SO5c2lc4nC(r)r)o)cFnr)Nc1nc#s1H
replace 5 at position 13 with 3,C=2+nc]=S4-SO3c2lc4nC(r)r)o)cFnr)Nc1nc#s1H
add 1 at position 11,C=2+nc]=S4-1SO3c2lc4nC(r)r)o)cFnr)Nc1nc#s1H
replace r at position 32 with o,C=2+nc]=S4-1SO3c2lc4nC(r)r)o)cFno)Nc1nc#s1H
remove r from position 23,C=2+nc]=S4-1SO3c2lc4nC()r)o)cFno)Nc1nc#s1H
replace F at position 29 with 2,C=2+nc]=S4-1SO3c2lc4nC()r)o)c2no)Nc1nc#s1H
remove S from position 12,C=2+nc]=S4-1O3c2lc4nC()r)o)c2no)Nc1nc#s1H
add n at position 12,C=2+nc]=S4-1nO3c2lc4nC()r)o)c2no)Nc1nc#s1H
add H at position 4,C=2+Hnc]=S4-1nO3c2lc4nC()r)o)c2no)Nc1nc#s1H
replace H at position 4 with n,C=2+nnc]=S4-1nO3c2lc4nC()r)o)c2no)Nc1nc#s1H
replace 4 at position 20 with ],C=2+nnc]=S4-1nO3c2lc]nC()r)o)c2no)Nc1nc#s1H
add S at position 5,C=2+nSnc]=S4-1nO3c2lc]nC()r)o)c2no)Nc1nc#s1H
replace 2 at position 2 with I,C=I+nSnc]=S4-1nO3c2lc]nC()r)o)c2no)Nc1nc#s1H
add O at position 3,C=IO+nSnc]=S4-1nO3c2lc]nC()r)o)c2no)Nc1nc#s1H
replace + at position 4 with #,C=IO#nSnc]=S4-1nO3c2lc]nC()r)o)c2no)Nc1nc#s1H
replace l at position 20 with c,C=IO#nSnc]=S4-1nO3c2cc]nC()r)o)c2no)Nc1nc#s1H
add - at position 17,C=IO#nSnc]=S4-1nO-3c2cc]nC()r)o)c2no)Nc1nc#s1H
add @ at position 10,C=IO#nSnc]@=S4-1nO-3c2cc]nC()r)o)c2no)Nc1nc#s1H
remove S from position 6,C=IO#nnc]@=S4-1nO-3c2cc]nC()r)o)c2no)Nc1nc#s1H
replace O at position 16 with (,C=IO#nnc]@=S4-1n(-3c2cc]nC()r)o)c2no)Nc1nc#s1H
replace n at position 24 with (,C=IO#nnc]@=S4-1n(-3c2cc](C()r)o)c2no)Nc1nc#s1H
replace n at position 6 with 1,C=IO#n1c]@=S4-1n(-3c2cc](C()r)o)c2no)Nc1nc#s1H
add c at position 7,C=IO#n1cc]@=S4-1n(-3c2cc](C()r)o)c2no)Nc1nc#s1H
remove = from position 11,C=IO#n1cc]@S4-1n(-3c2cc](C()r)o)c2no)Nc1nc#s1H
replace 3 at position 18 with O,C=IO#n1cc]@S4-1n(-Oc2cc](C()r)o)c2no)Nc1nc#s1H
add - at position 33,C=IO#n1cc]@S4-1n(-Oc2cc](C()r)o)c-2no)Nc1nc#s1H
replace @ at position 10 with c,C=IO#n1cc]cS4-1n(-Oc2cc](C()r)o)c-2no)Nc1nc#s1H
replace o at position 30 with (,C=IO#n1cc]cS4-1n(-Oc2cc](C()r)()c-2no)Nc1nc#s1H
replace ( at position 30 with F,C=IO#n1cc]cS4-1n(-Oc2cc](C()r)F)c-2no)Nc1nc#s1H
add 1 at position 37,C=IO#n1cc]cS4-1n(-Oc2cc](C()r)F)c-2no1)Nc1nc#s1H
add c at position 22,C=IO#n1cc]cS4-1n(-Oc2ccc](C()r)F)c-2no1)Nc1nc#s1H
replace I at position 2 with C,C=CO#n1cc]cS4-1n(-Oc2ccc](C()r)F)c-2no1)Nc1nc#s1H
remove ] from position 9,C=CO#n1cccS4-1n(-Oc2ccc](C()r)F)c-2no1)Nc1nc#s1H
replace 4 at position 11 with 1,C=CO#n1cccS1-1n(-Oc2ccc](C()r)F)c-2no1)Nc1nc#s1H
remove O from position 17,C=CO#n1cccS1-1n(-c2ccc](C()r)F)c-2no1)Nc1nc#s1H
add r at position 29,C=CO#n1cccS1-1n(-c2ccc](C()r)rF)c-2no1)Nc1nc#s1H
remove - from position 33,C=CO#n1cccS1-1n(-c2ccc](C()r)rF)c2no1)Nc1nc#s1H
remove ] from position 22,C=CO#n1cccS1-1n(-c2ccc(C()r)rF)c2no1)Nc1nc#s1H
add c at position 9,C=CO#n1ccccS1-1n(-c2ccc(C()r)rF)c2no1)Nc1nc#s1H
add H at position 4,C=COH#n1ccccS1-1n(-c2ccc(C()r)rF)c2no1)Nc1nc#s1H
remove H from position 4,C=CO#n1ccccS1-1n(-c2ccc(C()r)rF)c2no1)Nc1nc#s1H
add O at position 24,C=CO#n1ccccS1-1n(-c2ccc(OC()r)rF)c2no1)Nc1nc#s1H
add c at position 45,C=CO#n1ccccS1-1n(-c2ccc(OC()r)rF)c2no1)Nc1nc#cs1H
replace # at position 4 with C,C=COCn1ccccS1-1n(-c2ccc(OC()r)rF)c2no1)Nc1nc#cs1H
add O at position 1,CO=COCn1ccccS1-1n(-c2ccc(OC()r)rF)c2no1)Nc1nc#cs1H
replace r at position 29 with (,CO=COCn1ccccS1-1n(-c2ccc(OC()()rF)c2no1)Nc1nc#cs1H
add ) at position 41,CO=COCn1ccccS1-1n(-c2ccc(OC()()rF)c2no1)N)c1nc#cs1H
add 2 at position 22,CO=COCn1ccccS1-1n(-c2c2cc(OC()()rF)c2no1)N)c1nc#cs1H
replace O at position 4 with (,CO=C(Cn1ccccS1-1n(-c2c2cc(OC()()rF)c2no1)N)c1nc#cs1H
add c at position 17,CO=C(Cn1ccccS1-1nc(-c2c2cc(OC()()rF)c2no1)N)c1nc#cs1H
add F at position 32,CO=C(Cn1ccccS1-1nc(-c2c2cc(OC()(F)rF)c2no1)N)c1nc#cs1H
add c at position 38,CO=C(Cn1ccccS1-1nc(-c2c2cc(OC()(F)rF)cc2no1)N)c1nc#cs1H
remove r from position 34,CO=C(Cn1ccccS1-1nc(-c2c2cc(OC()(F)F)cc2no1)N)c1nc#cs1H
add c at position 15,CO=C(Cn1ccccS1-c1nc(-c2c2cc(OC()(F)F)cc2no1)N)c1nc#cs1H
remove C from position 0,O=C(Cn1ccccS1-c1nc(-c2c2cc(OC()(F)F)cc2no1)N)c1nc#cs1H
remove S from position 11,O=C(Cn1cccc1-c1nc(-c2c2cc(OC()(F)F)cc2no1)N)c1nc#cs1H
add + at position 29,O=C(Cn1cccc1-c1nc(-c2c2cc(OC(+)(F)F)cc2no1)N)c1nc#cs1H
add ) at position 39,O=C(Cn1cccc1-c1nc(-c2c2cc(OC(+)(F)F)cc2)no1)N)c1nc#cs1H
remove # from position 50,O=C(Cn1cccc1-c1nc(-c2c2cc(OC(+)(F)F)cc2)no1)N)c1nccs1H
remove 2 from position 22,O=C(Cn1cccc1-c1nc(-c2ccc(OC(+)(F)F)cc2)no1)N)c1nccs1H
remove ) from position 44,O=C(Cn1cccc1-c1nc(-c2ccc(OC(+)(F)F)cc2)no1)Nc1nccs1H
remove H from position 51,O=C(Cn1cccc1-c1nc(-c2ccc(OC(+)(F)F)cc2)no1)Nc1nccs1
replace + at position 28 with F,O=C(Cn1cccc1-c1nc(-c2ccc(OC(F)(F)F)cc2)no1)Nc1nccs1
final: O=C(Cn1cccc1-c1nc(-c2ccc(OC(F)(F)F)cc2)no1)Nc1nccs1,O=C(Cn1cccc1-c1nc(-c2ccc(OC(F)(F)F)cc2)no1)Nc1nccs1
