log,state
initialize: ,
add 5 at position 0,5
add # at position 1,5#
add I at position 1,5I#
add 2 at position 3,5I#2
remove I from position 1,5#2
replace 2 at position 2 with 5,5#5
replace 5 at position 2 with r,5#r
add + at position 2,5#+r
replace 5 at position 0 with /,/#+r
add 5 at position 3,/#+5r
replace r at position 4 with -,/#+5-
add 3 at position 4,/#+53-
add c at position 2,/#c+53-
remove 3 from position 5,/#c+5-
add o at position 0,o/#c+5-
remove 5 from position 5,o/#c+-
replace # at position 2 with S,o/Sc+-
remove S from position 2,o/c+-
add H at position 2,o/Hc+-
remove / from position 1,oHc+-
add 5 at position 3,oHc5+-
replace + at position 4 with H,oHc5H-
replace 5 at position 3 with B,oHcBH-
add s at position 5,oHcBHs-
add S at position 3,oHcSBHs-
add C at position 8,oHcSBHs-C
remove s from position 6,oHcSBH-C
replace <PERSON> at position 3 with c,oHccBH-C
remove c from position 2,oHcBH-<PERSON>
remove B from position 3,oHc<PERSON>-<PERSON>
replace - at position 4 with ],oHcH]C
remove C from position 5,oHcH]
add B at position 4,oHcHB]
add N at position 6,oHcHB]N
replace B at position 4 with -,oHcH-]N
add r at position 7,oHcH-]Nr
add [ at position 6,oHcH-][Nr
add [ at position 2,oH[cH-][Nr
add 5 at position 3,oH[5cH-][Nr
replace [ at position 8 with B,oH[5cH-]BNr
add l at position 2,oHl[5cH-]BNr
add 4 at position 2,oH4l[5cH-]BNr
replace [ at position 4 with H,oH4lH5cH-]BNr
add 6 at position 5,oH4lH65cH-]BNr
add O at position 2,oHO4lH65cH-]BNr
remove H from position 9,oHO4lH65c-]BNr
add 6 at position 13,oHO4lH65c-]BN6r
replace H at position 1 with C,oCO4lH65c-]BN6r
remove H from position 5,oCO4l65c-]BN6r
replace 4 at position 3 with r,oCOrl65c-]BN6r
add = at position 6,oCOrl6=5c-]BN6r
add s at position 11,oCOrl6=5c-]sBN6r
remove 6 from position 14,oCOrl6=5c-]sBNr
replace ] at position 10 with O,oCOrl6=5c-OsBNr
remove O from position 2,oCrl6=5c-OsBNr
add 2 at position 11,oCrl6=5c-Os2BNr
replace N at position 13 with /,oCrl6=5c-Os2B/r
add S at position 6,oCrl6=S5c-Os2B/r
add l at position 2,oClrl6=S5c-Os2B/r
add F at position 11,oClrl6=S5c-FOs2B/r
replace o at position 0 with S,SClrl6=S5c-FOs2B/r
remove S from position 7,SClrl6=5c-FOs2B/r
add ) at position 10,SClrl6=5c-)FOs2B/r
remove l from position 2,SCrl6=5c-)FOs2B/r
add r at position 1,SrCrl6=5c-)FOs2B/r
add ) at position 12,SrCrl6=5c-)F)Os2B/r
replace F at position 11 with 3,SrCrl6=5c-)3)Os2B/r
add 4 at position 19,SrCrl6=5c-)3)Os2B/r4
add + at position 2,Sr+Crl6=5c-)3)Os2B/r4
add C at position 1,SCr+Crl6=5c-)3)Os2B/r4
add F at position 21,SCr+Crl6=5c-)3)Os2B/rF4
remove r from position 2,SC+Crl6=5c-)3)Os2B/rF4
add = at position 14,SC+Crl6=5c-)3)=Os2B/rF4
add C at position 19,SC+Crl6=5c-)3)=Os2BC/rF4
add n at position 17,SC+Crl6=5c-)3)=Osn2BC/rF4
replace - at position 10 with l,SC+Crl6=5cl)3)=Osn2BC/rF4
add = at position 12,SC+Crl6=5cl)=3)=Osn2BC/rF4
replace F at position 24 with (,SC+Crl6=5cl)=3)=Osn2BC/r(4
replace / at position 22 with 1,SC+Crl6=5cl)=3)=Osn2BC1r(4
remove s from position 17,SC+Crl6=5cl)=3)=On2BC1r(4
add - at position 19,SC+Crl6=5cl)=3)=On2-BC1r(4
replace r at position 4 with ],SC+C]l6=5cl)=3)=On2-BC1r(4
add ( at position 21,SC+C]l6=5cl)=3)=On2-B(C1r(4
add S at position 5,SC+C]Sl6=5cl)=3)=On2-B(C1r(4
add ) at position 27,SC+C]Sl6=5cl)=3)=On2-B(C1r()4
replace S at position 0 with C,CC+C]Sl6=5cl)=3)=On2-B(C1r()4
add 3 at position 20,CC+C]Sl6=5cl)=3)=On23-B(C1r()4
replace 6 at position 7 with N,CC+C]SlN=5cl)=3)=On23-B(C1r()4
add C at position 15,CC+C]SlN=5cl)=3C)=On23-B(C1r()4
replace 4 at position 30 with 7,CC+C]SlN=5cl)=3C)=On23-B(C1r()7
replace ) at position 29 with 7,CC+C]SlN=5cl)=3C)=On23-B(C1r(77
add ) at position 27,CC+C]SlN=5cl)=3C)=On23-B(C1)r(77
replace 5 at position 9 with c,CC+C]SlN=ccl)=3C)=On23-B(C1)r(77
add @ at position 2,CC@+C]SlN=ccl)=3C)=On23-B(C1)r(77
replace ( at position 25 with /,CC@+C]SlN=ccl)=3C)=On23-B/C1)r(77
remove - from position 23,CC@+C]SlN=ccl)=3C)=On23B/C1)r(77
remove l from position 7,CC@+C]SN=ccl)=3C)=On23B/C1)r(77
replace 3 at position 14 with r,CC@+C]SN=ccl)=rC)=On23B/C1)r(77
add ) at position 21,CC@+C]SN=ccl)=rC)=On2)3B/C1)r(77
add 4 at position 13,CC@+C]SN=ccl)4=rC)=On2)3B/C1)r(77
replace ) at position 17 with r,CC@+C]SN=ccl)4=rCr=On2)3B/C1)r(77
add H at position 32,CC@+C]SN=ccl)4=rCr=On2)3B/C1)r(7H7
add 4 at position 18,CC@+C]SN=ccl)4=rCr4=On2)3B/C1)r(7H7
add 6 at position 21,CC@+C]SN=ccl)4=rCr4=O6n2)3B/C1)r(7H7
replace 4 at position 18 with o,CC@+C]SN=ccl)4=rCro=O6n2)3B/C1)r(7H7
replace 6 at position 21 with r,CC@+C]SN=ccl)4=rCro=Orn2)3B/C1)r(7H7
remove ) from position 30,CC@+C]SN=ccl)4=rCro=Orn2)3B/C1r(7H7
add 3 at position 6,CC@+C]3SN=ccl)4=rCro=Orn2)3B/C1r(7H7
add ) at position 29,CC@+C]3SN=ccl)4=rCro=Orn2)3B/)C1r(7H7
add C at position 30,CC@+C]3SN=ccl)4=rCro=Orn2)3B/)CC1r(7H7
remove r from position 33,CC@+C]3SN=ccl)4=rCro=Orn2)3B/)CC1(7H7
add C at position 22,CC@+C]3SN=ccl)4=rCro=OCrn2)3B/)CC1(7H7
add c at position 23,CC@+C]3SN=ccl)4=rCro=OCcrn2)3B/)CC1(7H7
remove / from position 30,CC@+C]3SN=ccl)4=rCro=OCcrn2)3B)CC1(7H7
add ) at position 30,CC@+C]3SN=ccl)4=rCro=OCcrn2)3B))CC1(7H7
add C at position 19,CC@+C]3SN=ccl)4=rCrCo=OCcrn2)3B))CC1(7H7
add n at position 4,CC@+nC]3SN=ccl)4=rCrCo=OCcrn2)3B))CC1(7H7
replace = at position 10 with 5,CC@+nC]3SN5ccl)4=rCrCo=OCcrn2)3B))CC1(7H7
remove 2 from position 28,CC@+nC]3SN5ccl)4=rCrCo=OCcrn)3B))CC1(7H7
remove 7 from position 37,CC@+nC]3SN5ccl)4=rCrCo=OCcrn)3B))CC1(H7
add 2 at position 17,CC@+nC]3SN5ccl)4=2rCrCo=OCcrn)3B))CC1(H7
add = at position 7,CC@+nC]=3SN5ccl)4=2rCrCo=OCcrn)3B))CC1(H7
add # at position 36,CC@+nC]=3SN5ccl)4=2rCrCo=OCcrn)3B))C#C1(H7
replace = at position 17 with F,CC@+nC]=3SN5ccl)4F2rCrCo=OCcrn)3B))C#C1(H7
replace ) at position 34 with C,CC@+nC]=3SN5ccl)4F2rCrCo=OCcrn)3B)CC#C1(H7
add 2 at position 36,CC@+nC]=3SN5ccl)4F2rCrCo=OCcrn)3B)CC2#C1(H7
add ( at position 19,CC@+nC]=3SN5ccl)4F2(rCrCo=OCcrn)3B)CC2#C1(H7
replace F at position 17 with n,CC@+nC]=3SN5ccl)4n2(rCrCo=OCcrn)3B)CC2#C1(H7
remove 7 from position 43,CC@+nC]=3SN5ccl)4n2(rCrCo=OCcrn)3B)CC2#C1(H
add F at position 20,CC@+nC]=3SN5ccl)4n2(FrCrCo=OCcrn)3B)CC2#C1(H
replace @ at position 2 with 2,CC2+nC]=3SN5ccl)4n2(FrCrCo=OCcrn)3B)CC2#C1(H
add H at position 9,CC2+nC]=3HSN5ccl)4n2(FrCrCo=OCcrn)3B)CC2#C1(H
remove ( from position 43,CC2+nC]=3HSN5ccl)4n2(FrCrCo=OCcrn)3B)CC2#C1H
add r at position 10,CC2+nC]=3HrSN5ccl)4n2(FrCrCo=OCcrn)3B)CC2#C1H
remove ) from position 34,CC2+nC]=3HrSN5ccl)4n2(FrCrCo=OCcrn3B)CC2#C1H
remove r from position 10,CC2+nC]=3HSN5ccl)4n2(FrCrCo=OCcrn3B)CC2#C1H
add S at position 8,CC2+nC]=S3HSN5ccl)4n2(FrCrCo=OCcrn3B)CC2#C1H
remove B from position 35,CC2+nC]=S3HSN5ccl)4n2(FrCrCo=OCcrn3)CC2#C1H
remove F from position 22,CC2+nC]=S3HSN5ccl)4n2(rCrCo=OCcrn3)CC2#C1H
replace 5 at position 13 with 3,CC2+nC]=S3HSN3ccl)4n2(rCrCo=OCcrn3)CC2#C1H
add ( at position 11,CC2+nC]=S3H(SN3ccl)4n2(rCrCo=OCcrn3)CC2#C1H
replace r at position 32 with c,CC2+nC]=S3H(SN3ccl)4n2(rCrCo=OCccn3)CC2#C1H
remove r from position 23,CC2+nC]=S3H(SN3ccl)4n2(CrCo=OCccn3)CC2#C1H
replace C at position 29 with c,CC2+nC]=S3H(SN3ccl)4n2(CrCo=Occcn3)CC2#C1H
remove S from position 12,CC2+nC]=S3H(N3ccl)4n2(CrCo=Occcn3)CC2#C1H
add c at position 12,CC2+nC]=S3H(cN3ccl)4n2(CrCo=Occcn3)CC2#C1H
add I at position 4,CC2+InC]=S3H(cN3ccl)4n2(CrCo=Occcn3)CC2#C1H
replace I at position 4 with +,CC2++nC]=S3H(cN3ccl)4n2(CrCo=Occcn3)CC2#C1H
replace 4 at position 20 with ],CC2++nC]=S3H(cN3ccl)]n2(CrCo=Occcn3)CC2#C1H
add S at position 5,CC2++SnC]=S3H(cN3ccl)]n2(CrCo=Occcn3)CC2#C1H
replace 2 at position 2 with H,CCH++SnC]=S3H(cN3ccl)]n2(CrCo=Occcn3)CC2#C1H
add O at position 3,CCHO++SnC]=S3H(cN3ccl)]n2(CrCo=Occcn3)CC2#C1H
replace + at position 4 with #,CCHO#+SnC]=S3H(cN3ccl)]n2(CrCo=Occcn3)CC2#C1H
replace l at position 20 with c,CCHO#+SnC]=S3H(cN3ccc)]n2(CrCo=Occcn3)CC2#C1H
add c at position 17,CCHO#+SnC]=S3H(cNc3ccc)]n2(CrCo=Occcn3)CC2#C1H
add @ at position 10,CCHO#+SnC]@=S3H(cNc3ccc)]n2(CrCo=Occcn3)CC2#C1H
remove S from position 6,CCHO#+nC]@=S3H(cNc3ccc)]n2(CrCo=Occcn3)CC2#C1H
replace N at position 16 with c,CCHO#+nC]@=S3H(ccc3ccc)]n2(CrCo=Occcn3)CC2#C1H
replace n at position 24 with C,CCHO#+nC]@=S3H(ccc3ccc)]C2(CrCo=Occcn3)CC2#C1H
replace n at position 6 with ],CCHO#+]C]@=S3H(ccc3ccc)]C2(CrCo=Occcn3)CC2#C1H
add 1 at position 7,CCHO#+]1C]@=S3H(ccc3ccc)]C2(CrCo=Occcn3)CC2#C1H
remove = from position 11,CCHO#+]1C]@S3H(ccc3ccc)]C2(CrCo=Occcn3)CC2#C1H
replace 3 at position 18 with O,CCHO#+]1C]@S3H(cccOccc)]C2(CrCo=Occcn3)CC2#C1H
add - at position 33,CCHO#+]1C]@S3H(cccOccc)]C2(CrCo=O-cccn3)CC2#C1H
replace @ at position 10 with C,CCHO#+]1C]CS3H(cccOccc)]C2(CrCo=O-cccn3)CC2#C1H
replace o at position 30 with ),CCHO#+]1C]CS3H(cccOccc)]C2(CrC)=O-cccn3)CC2#C1H
replace ) at position 30 with (,CCHO#+]1C]CS3H(cccOccc)]C2(CrC(=O-cccn3)CC2#C1H
add o at position 37,CCHO#+]1C]CS3H(cccOccc)]C2(CrC(=O-cccon3)CC2#C1H
add 2 at position 22,CCHO#+]1C]CS3H(cccOccc2)]C2(CrC(=O-cccon3)CC2#C1H
replace H at position 2 with [,CC[O#+]1C]CS3H(cccOccc2)]C2(CrC(=O-cccon3)CC2#C1H
remove ] from position 9,CC[O#+]1CCS3H(cccOccc2)]C2(CrC(=O-cccon3)CC2#C1H
replace 3 at position 11 with @,CC[O#+]1CCS@H(cccOccc2)]C2(CrC(=O-cccon3)CC2#C1H
remove O from position 17,CC[O#+]1CCS@H(cccccc2)]C2(CrC(=O-cccon3)CC2#C1H
add r at position 29,CC[O#+]1CCS@H(cccccc2)]C2(CrCr(=O-cccon3)CC2#C1H
remove - from position 33,CC[O#+]1CCS@H(cccccc2)]C2(CrCr(=Occcon3)CC2#C1H
remove ] from position 22,CC[O#+]1CCS@H(cccccc2)C2(CrCr(=Occcon3)CC2#C1H
add [ at position 9,CC[O#+]1C[CS@H(cccccc2)C2(CrCr(=Occcon3)CC2#C1H
add H at position 4,CC[OH#+]1C[CS@H(cccccc2)C2(CrCr(=Occcon3)CC2#C1H
remove H from position 4,CC[O#+]1C[CS@H(cccccc2)C2(CrCr(=Occcon3)CC2#C1H
add C at position 24,CC[O#+]1C[CS@H(cccccc2)CC2(CrCr(=Occcon3)CC2#C1H
add ) at position 45,CC[O#+]1C[CS@H(cccccc2)CC2(CrCr(=Occcon3)CC2#)C1H
replace # at position 4 with H,CC[OH+]1C[CS@H(cccccc2)CC2(CrCr(=Occcon3)CC2#)C1H
add C at position 1,CCC[OH+]1C[CS@H(cccccc2)CC2(CrCr(=Occcon3)CC2#)C1H
replace r at position 29 with N,CCC[OH+]1C[CS@H(cccccc2)CC2(CNCr(=Occcon3)CC2#)C1H
add ) at position 41,CCC[OH+]1C[CS@H(cccccc2)CC2(CNCr(=Occcon3))CC2#)C1H
add 2 at position 22,CCC[OH+]1C[CS@H(cccccc22)CC2(CNCr(=Occcon3))CC2#)C1H
replace O at position 4 with N,CCC[NH+]1C[CS@H(cccccc22)CC2(CNCr(=Occcon3))CC2#)C1H
add 2 at position 17,CCC[NH+]1C[CS@H(c2ccccc22)CC2(CNCr(=Occcon3))CC2#)C1H
add ( at position 32,CCC[NH+]1C[CS@H(c2ccccc22)CC2(CN(Cr(=Occcon3))CC2#)C1H
add ) at position 38,CCC[NH+]1C[CS@H(c2ccccc22)CC2(CN(Cr(=O)cccon3))CC2#)C1H
remove r from position 34,CCC[NH+]1C[CS@H(c2ccccc22)CC2(CN(C(=O)cccon3))CC2#)C1H
add ] at position 15,CCC[NH+]1C[CS@H](c2ccccc22)CC2(CN(C(=O)cccon3))CC2#)C1H
remove C from position 0,CC[NH+]1C[CS@H](c2ccccc22)CC2(CN(C(=O)cccon3))CC2#)C1H
remove S from position 11,CC[NH+]1C[C@H](c2ccccc22)CC2(CN(C(=O)cccon3))CC2#)C1H
add + at position 29,CC[NH+]1C[C@H](c2ccccc22)CC2(+CN(C(=O)cccon3))CC2#)C1H
add 3 at position 39,CC[NH+]1C[C@H](c2ccccc22)CC2(+CN(C(=O)c3ccon3))CC2#)C1H
remove # from position 50,CC[NH+]1C[C@H](c2ccccc22)CC2(+CN(C(=O)c3ccon3))CC2)C1H
remove 2 from position 22,CC[NH+]1C[C@H](c2ccccc2)CC2(+CN(C(=O)c3ccon3))CC2)C1H
remove ) from position 44,CC[NH+]1C[C@H](c2ccccc2)CC2(+CN(C(=O)c3ccon3)CC2)C1H
remove H from position 51,CC[NH+]1C[C@H](c2ccccc2)CC2(+CN(C(=O)c3ccon3)CC2)C1
replace + at position 28 with C,CC[NH+]1C[C@H](c2ccccc2)CC2(CCN(C(=O)c3ccon3)CC2)C1
final: CC[NH+]1C[C@H](c2ccccc2)CC2(CCN(C(=O)c3ccon3)CC2)C1,CC[NH+]1C[C@H](c2ccccc2)CC2(CCN(C(=O)c3ccon3)CC2)C1
