log,state
initialize: C[C@H]1CN(CC(=O)Nc2nc(-c3ccccc3Cl)cs2)CCO1,C[C@H]1CN(CC(=O)Nc2nc(-c3ccccc3Cl)cs2)CCO1
replace c at position 28 with +,C[C@H]1CN(CC(=O)Nc2nc(-c3ccc+c3Cl)cs2)CCO1
add ( at position 37,C[C@H]1CN(CC(=O)Nc2nc(-c3ccc+c3Cl)cs2()CCO1
remove ] from position 5,C[C@H1CN(CC(=O)Nc2nc(-c3ccc+c3Cl)cs2()CCO1
add 2 at position 22,C[C@H1CN(CC(=O)Nc2nc(-2c3ccc+c3Cl)cs2()CCO1
add 6 at position 1,C6[C@H1CN(CC(=O)Nc2nc(-2c3ccc+c3Cl)cs2()CCO1
remove c from position 30,C6[C@H1CN(CC(=O)Nc2nc(-2c3ccc+3Cl)cs2()CCO1
remove ( from position 9,C6[C@H1CNCC(=O)Nc2nc(-2c3ccc+3Cl)cs2()CCO1
add n at position 7,C6[C@H1nCNCC(=O)Nc2nc(-2c3ccc+3Cl)cs2()CCO1
add o at position 31,C6[C@H1nCNCC(=O)Nc2nc(-2c3ccc+3oCl)cs2()CCO1
replace @ at position 4 with S,C6[CSH1nCNCC(=O)Nc2nc(-2c3ccc+3oCl)cs2()CCO1
remove + from position 29,C6[CSH1nCNCC(=O)Nc2nc(-2c3ccc3oCl)cs2()CCO1
add ) at position 43,C6[CSH1nCNCC(=O)Nc2nc(-2c3ccc3oCl)cs2()CCO1)
remove [ from position 2,C6CSH1nCNCC(=O)Nc2nc(-2c3ccc3oCl)cs2()CCO1)
remove 2 from position 35,C6CSH1nCNCC(=O)Nc2nc(-2c3ccc3oCl)cs()CCO1)
add B at position 19,C6CSH1nCNCC(=O)Nc2nBc(-2c3ccc3oCl)cs()CCO1)
replace 2 at position 17 with 3,C6CSH1nCNCC(=O)Nc3nBc(-2c3ccc3oCl)cs()CCO1)
remove = from position 12,C6CSH1nCNCC(O)Nc3nBc(-2c3ccc3oCl)cs()CCO1)
remove c from position 33,C6CSH1nCNCC(O)Nc3nBc(-2c3ccc3oCl)s()CCO1)
remove ) from position 32,C6CSH1nCNCC(O)Nc3nBc(-2c3ccc3oCls()CCO1)
add - at position 21,C6CSH1nCNCC(O)Nc3nBc(--2c3ccc3oCls()CCO1)
remove c from position 24,C6CSH1nCNCC(O)Nc3nBc(--23ccc3oCls()CCO1)
add H at position 4,C6CSHH1nCNCC(O)Nc3nBc(--23ccc3oCls()CCO1)
remove H from position 4,C6CSH1nCNCC(O)Nc3nBc(--23ccc3oCls()CCO1)
remove C from position 9,C6CSH1nCNC(O)Nc3nBc(--23ccc3oCls()CCO1)
add ] at position 22,C6CSH1nCNC(O)Nc3nBc(--]23ccc3oCls()CCO1)
add - at position 33,C6CSH1nCNC(O)Nc3nBc(--]23ccc3oCls-()CCO1)
remove o from position 29,C6CSH1nCNC(O)Nc3nBc(--]23ccc3Cls-()CCO1)
add O at position 17,C6CSH1nCNC(O)Nc3nOBc(--]23ccc3Cls-()CCO1)
replace O at position 11 with 3,C6CSH1nCNC(3)Nc3nOBc(--]23ccc3Cls-()CCO1)
add ] at position 9,C6CSH1nCN]C(3)Nc3nOBc(--]23ccc3Cls-()CCO1)
replace C at position 2 with I,C6ISH1nCN]C(3)Nc3nOBc(--]23ccc3Cls-()CCO1)
remove - from position 22,C6ISH1nCN]C(3)Nc3nOBc(-]23ccc3Cls-()CCO1)
remove C from position 37,C6ISH1nCN]C(3)Nc3nOBc(-]23ccc3Cls-()CO1)
replace C at position 30 with (,C6ISH1nCN]C(3)Nc3nOBc(-]23ccc3(ls-()CO1)
replace ( at position 30 with o,C6ISH1nCN]C(3)Nc3nOBc(-]23ccc3ols-()CO1)
replace C at position 10 with @,C6ISH1nCN]@(3)Nc3nOBc(-]23ccc3ols-()CO1)
remove - from position 33,C6ISH1nCN]@(3)Nc3nOBc(-]23ccc3ols()CO1)
replace O at position 18 with 3,C6ISH1nCN]@(3)Nc3n3Bc(-]23ccc3ols()CO1)
add = at position 11,C6ISH1nCN]@=(3)Nc3n3Bc(-]23ccc3ols()CO1)
remove C from position 7,C6ISH1nN]@=(3)Nc3n3Bc(-]23ccc3ols()CO1)
replace n at position 6 with l,C6ISH1lN]@=(3)Nc3n3Bc(-]23ccc3ols()CO1)
replace 2 at position 24 with n,C6ISH1lN]@=(3)Nc3n3Bc(-]n3ccc3ols()CO1)
replace 3 at position 16 with O,C6ISH1lN]@=(3)NcOn3Bc(-]n3ccc3ols()CO1)
add S at position 6,C6ISH1SlN]@=(3)NcOn3Bc(-]n3ccc3ols()CO1)
remove @ from position 10,C6ISH1SlN]=(3)NcOn3Bc(-]n3ccc3ols()CO1)
remove n from position 17,C6ISH1SlN]=(3)NcO3Bc(-]n3ccc3ols()CO1)
replace ( at position 20 with l,C6ISH1SlN]=(3)NcO3Bcl-]n3ccc3ols()CO1)
replace H at position 4 with ),C6IS)1SlN]=(3)NcO3Bcl-]n3ccc3ols()CO1)
remove S from position 3,C6I)1SlN]=(3)NcO3Bcl-]n3ccc3ols()CO1)
replace I at position 2 with 2,C62)1SlN]=(3)NcO3Bcl-]n3ccc3ols()CO1)
remove S from position 5,C62)1lN]=(3)NcO3Bcl-]n3ccc3ols()CO1)
replace ] at position 20 with 4,C62)1lN]=(3)NcO3Bcl-4n3ccc3ols()CO1)
replace 1 at position 4 with I,C62)IlN]=(3)NcO3Bcl-4n3ccc3ols()CO1)
remove I from position 4,C62)lN]=(3)NcO3Bcl-4n3ccc3ols()CO1)
remove c from position 12,C62)lN]=(3)NO3Bcl-4n3ccc3ols()CO1)
add S at position 12,C62)lN]=(3)NSO3Bcl-4n3ccc3ols()CO1)
replace ( at position 29 with F,C62)lN]=(3)NSO3Bcl-4n3ccc3olsF)CO1)
add r at position 23,C62)lN]=(3)NSO3Bcl-4n3crcc3olsF)CO1)
replace C at position 32 with r,C62)lN]=(3)NSO3Bcl-4n3crcc3olsF)rO1)
remove N from position 11,C62)lN]=(3)SO3Bcl-4n3crcc3olsF)rO1)
replace 3 at position 13 with 5,C62)lN]=(3)SO5Bcl-4n3crcc3olsF)rO1)
add F at position 22,C62)lN]=(3)SO5Bcl-4n3cFrcc3olsF)rO1)
add B at position 35,C62)lN]=(3)SO5Bcl-4n3cFrcc3olsF)rO1B)
remove ( from position 8,C62)lN]=3)SO5Bcl-4n3cFrcc3olsF)rO1B)
add r at position 10,C62)lN]=3)rSO5Bcl-4n3cFrcc3olsF)rO1B)
add ) at position 34,C62)lN]=3)rSO5Bcl-4n3cFrcc3olsF)rO)1B)
remove r from position 10,C62)lN]=3)SO5Bcl-4n3cFrcc3olsF)rO)1B)
add N at position 3,C62N)lN]=3)SO5Bcl-4n3cFrcc3olsF)rO)1B)
add r at position 9,C62N)lN]=r3)SO5Bcl-4n3cFrcc3olsF)rO)1B)
add 7 at position 27,C62N)lN]=r3)SO5Bcl-4n3cFrcc73olsF)rO)1B)
remove O from position 13,C62N)lN]=r3)S5Bcl-4n3cFrcc73olsF)rO)1B)
replace c at position 24 with s,C62N)lN]=r3)S5Bcl-4n3cFrsc73olsF)rO)1B)
replace - at position 17 with H,C62N)lN]=r3)S5BclH4n3cFrsc73olsF)rO)1B)
remove n from position 19,C62N)lN]=r3)S5BclH43cFrsc73olsF)rO)1B)
remove B from position 36,C62N)lN]=r3)S5BclH43cFrsc73olsF)rO)1)
replace ) at position 34 with +,C62N)lN]=r3)S5BclH43cFrsc73olsF)rO+1)
replace H at position 17 with =,C62N)lN]=r3)S5Bcl=43cFrsc73olsF)rO+1)
remove ) from position 36,C62N)lN]=r3)S5Bcl=43cFrsc73olsF)rO+1
remove ] from position 7,C62N)lN=r3)S5Bcl=43cFrsc73olsF)rO+1
remove 4 from position 17,C62N)lN=r3)S5Bcl=3cFrsc73olsF)rO+1
add ( at position 25,C62N)lN=r3)S5Bcl=3cFrsc73(olsF)rO+1
remove s from position 28,C62N)lN=r3)S5Bcl=3cFrsc73(olF)rO+1
add / at position 31,C62N)lN=r3)S5Bcl=3cFrsc73(olF)r/O+1
remove C from position 0,62N)lN=r3)S5Bcl=3cFrsc73(olF)r/O+1
add 4 at position 25,62N)lN=r3)S5Bcl=3cFrsc73(4olF)r/O+1
remove r from position 30,62N)lN=r3)S5Bcl=3cFrsc73(4olF)/O+1
add / at position 30,62N)lN=r3)S5Bcl=3cFrsc73(4olF)//O+1
remove 3 from position 23,62N)lN=r3)S5Bcl=3cFrsc7(4olF)//O+1
remove 7 from position 22,62N)lN=r3)S5Bcl=3cFrsc(4olF)//O+1
add r at position 33,62N)lN=r3)S5Bcl=3cFrsc(4olF)//O+1r
remove O from position 30,62N)lN=r3)S5Bcl=3cFrsc(4olF)//+1r
remove / from position 29,62N)lN=r3)S5Bcl=3cFrsc(4olF)/+1r
remove = from position 6,62N)lNr3)S5Bcl=3cFrsc(4olF)/+1r
add ) at position 30,62N)lNr3)S5Bcl=3cFrsc(4olF)/+1)r
replace ( at position 21 with 7,62N)lNr3)S5Bcl=3cFrsc74olF)/+1)r
replace r at position 18 with 4,62N)lNr3)S5Bcl=3cF4sc74olF)/+1)r
remove 7 from position 21,62N)lNr3)S5Bcl=3cF4sc4olF)/+1)r
remove r from position 30,62N)lNr3)S5Bcl=3cF4sc4olF)/+1)
remove r from position 6,62N)lN3)S5Bcl=3cF4sc4olF)/+1)
remove F from position 23,62N)lN3)S5Bcl=3cF4sc4ol)/+1)
remove 4 from position 17,62N)lN3)S5Bcl=3cFsc4ol)/+1)
remove 3 from position 6,62N)lN)S5Bcl=3cFsc4ol)/+1)
remove c from position 10,62N)lN)S5Bl=3cFsc4ol)/+1)
replace F at position 14 with 3,62N)lN)S5Bl=3c3sc4ol)/+1)
add H at position 16,62N)lN)S5Bl=3c3sHc4ol)/+1)
remove ) from position 3,62NlN)S5Bl=3c3sHc4ol)/+1)
replace B at position 8 with 6,62NlN)S56l=3c3sHc4ol)/+1)
add 7 at position 0,762NlN)S56l=3c3sHc4ol)/+1)
add 1 at position 1,7162NlN)S56l=3c3sHc4ol)/+1)
remove 6 from position 10,7162NlN)S5l=3c3sHc4ol)/+1)
remove c from position 13,7162NlN)S5l=33sHc4ol)/+1)
replace 6 at position 2 with o,71o2NlN)S5l=33sHc4ol)/+1)
replace 5 at position 9 with 3,71o2NlN)S3l=33sHc4ol)/+1)
remove o from position 18,71o2NlN)S3l=33sHc4l)/+1)
add ) at position 16,71o2NlN)S3l=33sH)c4l)/+1)
remove 7 from position 0,1o2NlN)S3l=33sH)c4l)/+1)
remove = from position 10,1o2NlN)S3l33sH)c4l)/+1)
replace S at position 7 with /,1o2NlN)/3l33sH)c4l)/+1)
remove 1 from position 21,1o2NlN)/3l33sH)c4l)/+)
replace l at position 4 with r,1o2NrN)/3l33sH)c4l)/+)
remove / from position 19,1o2NrN)/3l33sH)c4l)+)
add s at position 17,1o2NrN)/3l33sH)c4sl)+)
replace N at position 5 with s,1o2Nrs)/3l33sH)c4sl)+)
remove 4 from position 16,1o2Nrs)/3l33sH)csl)+)
remove s from position 12,1o2Nrs)/3l33H)csl)+)
replace 3 at position 10 with -,1o2Nrs)/3l-3H)csl)+)
remove ) from position 17,1o2Nrs)/3l-3H)csl+)
remove c from position 14,1o2Nrs)/3l-3H)sl+)
add r at position 2,1or2Nrs)/3l-3H)sl+)
remove H from position 13,1or2Nrs)/3l-3)sl+)
replace r at position 2 with 5,1o52Nrs)/3l-3)sl+)
add 6 at position 6,1o52Nr6s)/3l-3)sl+)
replace l at position 16 with S,1o52Nr6s)/3l-3)sS+)
replace ) at position 18 with C,1o52Nr6s)/3l-3)sS+C
replace 1 at position 0 with B,Bo52Nr6s)/3l-3)sS+C
replace / at position 9 with @,Bo52Nr6s)@3l-3)sS+C
replace N at position 4 with B,Bo52Br6s)@3l-3)sS+C
remove s from position 7,Bo52Br6)@3l-3)sS+C
remove B from position 0,o52Br6)@3l-3)sS+C
replace 3 at position 11 with I,o52Br6)@3l-I)sS+C
remove 6 from position 5,o52Br)@3l-I)sS+C
add 1 at position 0,1o52Br)@3l-I)sS+C
replace B at position 4 with F,1o52Fr)@3l-I)sS+C
replace 3 at position 8 with O,1o52Fr)@Ol-I)sS+C
remove S from position 14,1o52Fr)@Ol-I)s+C
remove I from position 11,1o52Fr)@Ol-)s+C
remove ) from position 6,1o52Fr@Ol-)s+C
replace 2 at position 3 with 5,1o55Fr@Ol-)s+C
add H at position 5,1o55FHr@Ol-)s+C
replace o at position 1 with F,1F55FHr@Ol-)s+C
remove + from position 13,1F55FHr@Ol-)sC
add H at position 9,1F55FHr@OHl-)sC
remove 5 from position 2,1F5FHr@OHl-)sC
remove r from position 5,1F5FH@OHl-)sC
replace H at position 4 with [,1F5F[@OHl-)sC
remove 5 from position 2,1FF[@OHl-)sC
remove F from position 2,1F[@OHl-)sC
replace ) at position 8 with [,1F[@OHl-[sC
remove @ from position 3,1F[OHl-[sC
remove [ from position 2,1FOHl-[sC
remove [ from position 6,1FOHl-sC
remove C from position 7,1FOHl-s
replace l at position 4 with @,1FOH@-s
remove s from position 6,1FOH@-
remove @ from position 4,1FOH-
add C at position 5,1FOH-C
replace - at position 4 with /,1FOH/C
add B at position 3,1FOBH/C
add c at position 2,1FcOBH/C
replace O at position 3 with [,1Fc[BH/C
add s at position 6,1Fc[BHs/C
remove C from position 8,1Fc[BHs/
remove [ from position 3,1FcBHs/
remove s from position 5,1FcBH/
replace B at position 3 with 5,1Fc5H/
replace H at position 4 with +,1Fc5+/
remove 5 from position 3,1Fc+/
add / at position 1,1/Fc+/
remove F from position 2,1/c+/
add S at position 2,1/Sc+/
replace S at position 2 with #,1/#c+/
add 5 at position 5,1/#c+5/
remove 1 from position 0,/#c+5/
add 3 at position 5,/#c+53/
remove c from position 2,/#+53/
remove 3 from position 4,/#+5/
replace / at position 4 with r,/#+5r
remove 5 from position 3,/#+r
replace / at position 0 with 5,5#+r
remove + from position 2,5#r
replace r at position 2 with 5,5#5
replace 5 at position 2 with 2,5#2
add I at position 1,5I#2
remove 2 from position 3,5I#
remove I from position 1,5#
remove # from position 1,5
remove 5 from position 0,
final: ,
