step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,47,188
1,add,0.0,5,,,5,1,add 5 at position 0,flow_matching,0.3,2.0,47,188
2,remove,0.0,5,,5,,0,remove 5 from position 0,flow_matching,0.3,2.0,47,188
3,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,47,188
4,replace,0.0,s,+,+,s,1,replace + at position 0 with s,flow_matching,0.3,2.0,47,188
5,replace,0.0,+,s,s,+,1,replace s at position 0 with +,flow_matching,0.3,2.0,47,188
6,remove,0.0,+,,+,,0,remove + from position 0,flow_matching,0.3,2.0,47,188
7,add,0.0,\,,,\,1,add \ at position 0,flow_matching,0.3,2.0,47,188
8,replace,0.0,F,\,\,F,1,replace \ at position 0 with F,flow_matching,0.3,2.0,47,188
9,add,1.0,),,F,F),2,add ) at position 1,flow_matching,0.3,2.0,47,188
10,add,2.0,(,,F),F)(,3,add ( at position 2,flow_matching,0.3,2.0,47,188
11,add,0.0,1,,F)(,1F)(,4,add 1 at position 0,flow_matching,0.3,2.0,47,188
12,replace,0.0,C,1,1F)(,CF)(,4,replace 1 at position 0 with C,flow_matching,0.3,2.0,47,188
13,remove,1.0,F,,CF)(,C)(,3,remove F from position 1,flow_matching,0.3,2.0,47,188
14,remove,1.0,),,C)(,C(,2,remove ) from position 1,flow_matching,0.3,2.0,47,188
15,add,0.0,F,,C(,FC(,3,add F at position 0,flow_matching,0.3,2.0,47,188
16,add,0.0,c,,FC(,cFC(,4,add c at position 0,flow_matching,0.3,2.0,47,188
17,add,1.0,6,,cFC(,c6FC(,5,add 6 at position 1,flow_matching,0.3,2.0,47,188
18,replace,0.0,C,c,c6FC(,C6FC(,5,replace c at position 0 with C,flow_matching,0.3,2.0,47,188
19,replace,1.0,O,6,C6FC(,COFC(,5,replace 6 at position 1 with O,flow_matching,0.3,2.0,47,188
20,remove,0.0,C,,COFC(,OFC(,4,remove C from position 0,flow_matching,0.3,2.0,47,188
21,replace,0.0,C,O,OFC(,CFC(,4,replace O at position 0 with C,flow_matching,0.3,2.0,47,188
22,add,3.0,=,,CFC(,CFC=(,5,add = at position 3,flow_matching,0.3,2.0,47,188
23,remove,1.0,F,,CFC=(,CC=(,4,remove F from position 1,flow_matching,0.3,2.0,47,188
24,replace,2.0,5,=,CC=(,CC5(,4,replace = at position 2 with 5,flow_matching,0.3,2.0,47,188
25,replace,1.0,O,C,CC5(,CO5(,4,replace C at position 1 with O,flow_matching,0.3,2.0,47,188
26,replace,2.0,7,5,CO5(,CO7(,4,replace 5 at position 2 with 7,flow_matching,0.3,2.0,47,188
27,add,4.0,N,,CO7(,CO7(N,5,add N at position 4,flow_matching,0.3,2.0,47,188
28,replace,2.0,c,7,CO7(N,COc(N,5,replace 7 at position 2 with c,flow_matching,0.3,2.0,47,188
29,replace,3.0,1,(,COc(N,COc1N,5,replace ( at position 3 with 1,flow_matching,0.3,2.0,47,188
30,remove,1.0,O,,COc1N,Cc1N,4,remove O from position 1,flow_matching,0.3,2.0,47,188
31,remove,3.0,N,,Cc1N,Cc1,3,remove N from position 3,flow_matching,0.3,2.0,47,188
32,remove,2.0,1,,Cc1,Cc,2,remove 1 from position 2,flow_matching,0.3,2.0,47,188
33,replace,1.0,O,c,Cc,CO,2,replace c at position 1 with O,flow_matching,0.3,2.0,47,188
34,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,47,188
35,remove,2.0,c,,COc,CO,2,remove c from position 2,flow_matching,0.3,2.0,47,188
36,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,47,188
37,add,0.0,s,,COc,sCOc,4,add s at position 0,flow_matching,0.3,2.0,47,188
38,remove,0.0,s,,sCOc,COc,3,remove s from position 0,flow_matching,0.3,2.0,47,188
39,add,2.0,F,,COc,COFc,4,add F at position 2,flow_matching,0.3,2.0,47,188
40,replace,0.0,/,C,COFc,/OFc,4,replace C at position 0 with /,flow_matching,0.3,2.0,47,188
41,replace,0.0,C,/,/OFc,COFc,4,replace / at position 0 with C,flow_matching,0.3,2.0,47,188
42,remove,3.0,c,,COFc,COF,3,remove c from position 3,flow_matching,0.3,2.0,47,188
43,add,0.0,-,,COF,-COF,4,add - at position 0,flow_matching,0.3,2.0,47,188
44,replace,2.0,c,O,-COF,-CcF,4,replace O at position 2 with c,flow_matching,0.3,2.0,47,188
45,remove,3.0,F,,-CcF,-Cc,3,remove F from position 3,flow_matching,0.3,2.0,47,188
46,remove,1.0,C,,-Cc,-c,2,remove C from position 1,flow_matching,0.3,2.0,47,188
47,add,2.0,r,,-c,-cr,3,add r at position 2,flow_matching,0.3,2.0,47,188
48,add,1.0,B,,-cr,-Bcr,4,add B at position 1,flow_matching,0.3,2.0,47,188
49,add,2.0,N,,-Bcr,-BNcr,5,add N at position 2,flow_matching,0.3,2.0,47,188
50,replace,0.0,C,-,-BNcr,CBNcr,5,replace - at position 0 with C,flow_matching,0.3,2.0,47,188
51,replace,4.0,/,r,CBNcr,CBNc/,5,replace r at position 4 with /,flow_matching,0.3,2.0,47,188
52,replace,1.0,O,B,CBNc/,CONc/,5,replace B at position 1 with O,flow_matching,0.3,2.0,47,188
53,remove,3.0,c,,CONc/,CON/,4,remove c from position 3,flow_matching,0.3,2.0,47,188
54,replace,1.0,B,O,CON/,CBN/,4,replace O at position 1 with B,flow_matching,0.3,2.0,47,188
55,replace,1.0,O,B,CBN/,CON/,4,replace B at position 1 with O,flow_matching,0.3,2.0,47,188
56,replace,0.0,n,C,CON/,nON/,4,replace C at position 0 with n,flow_matching,0.3,2.0,47,188
57,add,3.0,F,,nON/,nONF/,5,add F at position 3,flow_matching,0.3,2.0,47,188
58,add,5.0,I,,nONF/,nONF/I,6,add I at position 5,flow_matching,0.3,2.0,47,188
59,replace,0.0,C,n,nONF/I,CONF/I,6,replace n at position 0 with C,flow_matching,0.3,2.0,47,188
60,replace,5.0,\,I,CONF/I,CONF/\,6,replace I at position 5 with \,flow_matching,0.3,2.0,47,188
61,replace,5.0,6,\,CONF/\,CONF/6,6,replace \ at position 5 with 6,flow_matching,0.3,2.0,47,188
62,replace,2.0,c,N,CONF/6,COcF/6,6,replace N at position 2 with c,flow_matching,0.3,2.0,47,188
63,replace,1.0,C,O,COcF/6,CCcF/6,6,replace O at position 1 with C,flow_matching,0.3,2.0,47,188
64,add,3.0,o,,CCcF/6,CCcoF/6,7,add o at position 3,flow_matching,0.3,2.0,47,188
65,replace,0.0,2,C,CCcoF/6,2CcoF/6,7,replace C at position 0 with 2,flow_matching,0.3,2.0,47,188
66,add,0.0,C,,2CcoF/6,C2CcoF/6,8,add C at position 0,flow_matching,0.3,2.0,47,188
67,replace,1.0,O,2,C2CcoF/6,COCcoF/6,8,replace 2 at position 1 with O,flow_matching,0.3,2.0,47,188
68,add,7.0,/,,COCcoF/6,COCcoF//6,9,add / at position 7,flow_matching,0.3,2.0,47,188
69,remove,3.0,c,,COCcoF//6,COCoF//6,8,remove c from position 3,flow_matching,0.3,2.0,47,188
70,replace,5.0,I,/,COCoF//6,COCoFI/6,8,replace / at position 5 with I,flow_matching,0.3,2.0,47,188
71,add,7.0,#,,COCoFI/6,COCoFI/#6,9,add # at position 7,flow_matching,0.3,2.0,47,188
72,replace,7.0,s,#,COCoFI/#6,COCoFI/s6,9,replace # at position 7 with s,flow_matching,0.3,2.0,47,188
73,add,4.0,),,COCoFI/s6,COCo)FI/s6,10,add ) at position 4,flow_matching,0.3,2.0,47,188
74,replace,2.0,c,C,COCo)FI/s6,COco)FI/s6,10,replace C at position 2 with c,flow_matching,0.3,2.0,47,188
75,replace,4.0,(,),COco)FI/s6,COco(FI/s6,10,replace ) at position 4 with (,flow_matching,0.3,2.0,47,188
76,replace,3.0,3,o,COco(FI/s6,COc3(FI/s6,10,replace o at position 3 with 3,flow_matching,0.3,2.0,47,188
77,replace,1.0,F,O,COc3(FI/s6,CFc3(FI/s6,10,replace O at position 1 with F,flow_matching,0.3,2.0,47,188
78,replace,1.0,l,F,CFc3(FI/s6,Clc3(FI/s6,10,replace F at position 1 with l,flow_matching,0.3,2.0,47,188
79,add,0.0,+,,Clc3(FI/s6,+Clc3(FI/s6,11,add + at position 0,flow_matching,0.3,2.0,47,188
80,remove,8.0,/,,+Clc3(FI/s6,+Clc3(FIs6,10,remove / from position 8,flow_matching,0.3,2.0,47,188
81,add,3.0,4,,+Clc3(FIs6,+Cl4c3(FIs6,11,add 4 at position 3,flow_matching,0.3,2.0,47,188
82,add,1.0,6,,+Cl4c3(FIs6,+6Cl4c3(FIs6,12,add 6 at position 1,flow_matching,0.3,2.0,47,188
83,replace,0.0,C,+,+6Cl4c3(FIs6,C6Cl4c3(FIs6,12,replace + at position 0 with C,flow_matching,0.3,2.0,47,188
84,replace,1.0,O,6,C6Cl4c3(FIs6,COCl4c3(FIs6,12,replace 6 at position 1 with O,flow_matching,0.3,2.0,47,188
85,remove,10.0,s,,COCl4c3(FIs6,COCl4c3(FI6,11,remove s from position 10,flow_matching,0.3,2.0,47,188
86,replace,2.0,c,C,COCl4c3(FI6,COcl4c3(FI6,11,replace C at position 2 with c,flow_matching,0.3,2.0,47,188
87,remove,5.0,c,,COcl4c3(FI6,COcl43(FI6,10,remove c from position 5,flow_matching,0.3,2.0,47,188
88,add,10.0,s,,COcl43(FI6,COcl43(FI6s,11,add s at position 10,flow_matching,0.3,2.0,47,188
89,remove,0.0,C,,COcl43(FI6s,Ocl43(FI6s,10,remove C from position 0,flow_matching,0.3,2.0,47,188
90,add,7.0,[,,Ocl43(FI6s,Ocl43(F[I6s,11,add [ at position 7,flow_matching,0.3,2.0,47,188
91,add,4.0,6,,Ocl43(F[I6s,Ocl463(F[I6s,12,add 6 at position 4,flow_matching,0.3,2.0,47,188
92,add,2.0,O,,Ocl463(F[I6s,OcOl463(F[I6s,13,add O at position 2,flow_matching,0.3,2.0,47,188
93,add,5.0,l,,OcOl463(F[I6s,OcOl4l63(F[I6s,14,add l at position 5,flow_matching,0.3,2.0,47,188
94,replace,13.0,(,s,OcOl4l63(F[I6s,OcOl4l63(F[I6(,14,replace s at position 13 with (,flow_matching,0.3,2.0,47,188
95,replace,10.0,B,[,OcOl4l63(F[I6(,OcOl4l63(FBI6(,14,replace [ at position 10 with B,flow_matching,0.3,2.0,47,188
96,replace,0.0,C,O,OcOl4l63(FBI6(,CcOl4l63(FBI6(,14,replace O at position 0 with C,flow_matching,0.3,2.0,47,188
97,add,11.0,r,,CcOl4l63(FBI6(,CcOl4l63(FBrI6(,15,add r at position 11,flow_matching,0.3,2.0,47,188
98,add,1.0,#,,CcOl4l63(FBrI6(,C#cOl4l63(FBrI6(,16,add # at position 1,flow_matching,0.3,2.0,47,188
99,replace,1.0,O,#,C#cOl4l63(FBrI6(,COcOl4l63(FBrI6(,16,replace # at position 1 with O,flow_matching,0.3,2.0,47,188
100,add,2.0,B,,COcOl4l63(FBrI6(,COBcOl4l63(FBrI6(,17,add B at position 2,flow_matching,0.3,2.0,47,188
101,replace,3.0,),c,COBcOl4l63(FBrI6(,COB)Ol4l63(FBrI6(,17,replace c at position 3 with ),flow_matching,0.3,2.0,47,188
102,replace,10.0,N,(,COB)Ol4l63(FBrI6(,COB)Ol4l63NFBrI6(,17,replace ( at position 10 with N,flow_matching,0.3,2.0,47,188
103,replace,2.0,c,B,COB)Ol4l63NFBrI6(,COc)Ol4l63NFBrI6(,17,replace B at position 2 with c,flow_matching,0.3,2.0,47,188
104,replace,3.0,(,),COc)Ol4l63NFBrI6(,COc(Ol4l63NFBrI6(,17,replace ) at position 3 with (,flow_matching,0.3,2.0,47,188
105,replace,3.0,1,(,COc(Ol4l63NFBrI6(,COc1Ol4l63NFBrI6(,17,replace ( at position 3 with 1,flow_matching,0.3,2.0,47,188
106,add,16.0,/,,COc1Ol4l63NFBrI6(,COc1Ol4l63NFBrI6/(,18,add / at position 16,flow_matching,0.3,2.0,47,188
107,replace,4.0,c,O,COc1Ol4l63NFBrI6/(,COc1cl4l63NFBrI6/(,18,replace O at position 4 with c,flow_matching,0.3,2.0,47,188
108,replace,6.0,r,4,COc1cl4l63NFBrI6/(,COc1clrl63NFBrI6/(,18,replace 4 at position 6 with r,flow_matching,0.3,2.0,47,188
109,add,1.0,@,,COc1clrl63NFBrI6/(,C@Oc1clrl63NFBrI6/(,19,add @ at position 1,flow_matching,0.3,2.0,47,188
110,replace,17.0,5,/,C@Oc1clrl63NFBrI6/(,C@Oc1clrl63NFBrI65(,19,replace / at position 17 with 5,flow_matching,0.3,2.0,47,188
111,replace,1.0,O,@,C@Oc1clrl63NFBrI65(,COOc1clrl63NFBrI65(,19,replace @ at position 1 with O,flow_matching,0.3,2.0,47,188
112,remove,10.0,3,,COOc1clrl63NFBrI65(,COOc1clrl6NFBrI65(,18,remove 3 from position 10,flow_matching,0.3,2.0,47,188
113,replace,11.0,o,F,COOc1clrl6NFBrI65(,COOc1clrl6NoBrI65(,18,replace F at position 11 with o,flow_matching,0.3,2.0,47,188
114,add,2.0,[,,COOc1clrl6NoBrI65(,CO[Oc1clrl6NoBrI65(,19,add [ at position 2,flow_matching,0.3,2.0,47,188
115,add,2.0,-,,CO[Oc1clrl6NoBrI65(,CO-[Oc1clrl6NoBrI65(,20,add - at position 2,flow_matching,0.3,2.0,47,188
116,replace,6.0,-,1,CO-[Oc1clrl6NoBrI65(,CO-[Oc-clrl6NoBrI65(,20,replace 1 at position 6 with -,flow_matching,0.3,2.0,47,188
117,remove,1.0,O,,CO-[Oc-clrl6NoBrI65(,C-[Oc-clrl6NoBrI65(,19,remove O from position 1,flow_matching,0.3,2.0,47,188
118,add,7.0,c,,C-[Oc-clrl6NoBrI65(,C-[Oc-cclrl6NoBrI65(,20,add c at position 7,flow_matching,0.3,2.0,47,188
119,replace,7.0,[,c,C-[Oc-cclrl6NoBrI65(,C-[Oc-c[lrl6NoBrI65(,20,replace c at position 7 with [,flow_matching,0.3,2.0,47,188
120,replace,1.0,O,-,C-[Oc-c[lrl6NoBrI65(,CO[Oc-c[lrl6NoBrI65(,20,replace - at position 1 with O,flow_matching,0.3,2.0,47,188
121,remove,8.0,l,,CO[Oc-c[lrl6NoBrI65(,CO[Oc-c[rl6NoBrI65(,19,remove l from position 8,flow_matching,0.3,2.0,47,188
122,add,4.0,+,,CO[Oc-c[rl6NoBrI65(,CO[O+c-c[rl6NoBrI65(,20,add + at position 4,flow_matching,0.3,2.0,47,188
123,replace,2.0,c,[,CO[O+c-c[rl6NoBrI65(,COcO+c-c[rl6NoBrI65(,20,replace [ at position 2 with c,flow_matching,0.3,2.0,47,188
124,add,14.0,5,,COcO+c-c[rl6NoBrI65(,COcO+c-c[rl6No5BrI65(,21,add 5 at position 14,flow_matching,0.3,2.0,47,188
125,remove,12.0,N,,COcO+c-c[rl6No5BrI65(,COcO+c-c[rl6o5BrI65(,20,remove N from position 12,flow_matching,0.3,2.0,47,188
126,remove,4.0,+,,COcO+c-c[rl6o5BrI65(,COcOc-c[rl6o5BrI65(,19,remove + from position 4,flow_matching,0.3,2.0,47,188
127,replace,3.0,1,O,COcOc-c[rl6o5BrI65(,COc1c-c[rl6o5BrI65(,19,replace O at position 3 with 1,flow_matching,0.3,2.0,47,188
128,replace,5.0,c,-,COc1c-c[rl6o5BrI65(,COc1ccc[rl6o5BrI65(,19,replace - at position 5 with c,flow_matching,0.3,2.0,47,188
129,replace,1.0,+,O,COc1ccc[rl6o5BrI65(,C+c1ccc[rl6o5BrI65(,19,replace O at position 1 with +,flow_matching,0.3,2.0,47,188
130,remove,17.0,5,,C+c1ccc[rl6o5BrI65(,C+c1ccc[rl6o5BrI6(,18,remove 5 from position 17,flow_matching,0.3,2.0,47,188
131,remove,1.0,+,,C+c1ccc[rl6o5BrI6(,Cc1ccc[rl6o5BrI6(,17,remove + from position 1,flow_matching,0.3,2.0,47,188
132,replace,0.0,(,C,Cc1ccc[rl6o5BrI6(,(c1ccc[rl6o5BrI6(,17,replace C at position 0 with (,flow_matching,0.3,2.0,47,188
133,add,5.0,=,,(c1ccc[rl6o5BrI6(,(c1cc=c[rl6o5BrI6(,18,add = at position 5,flow_matching,0.3,2.0,47,188
134,remove,15.0,I,,(c1cc=c[rl6o5BrI6(,(c1cc=c[rl6o5Br6(,17,remove I from position 15,flow_matching,0.3,2.0,47,188
135,remove,15.0,6,,(c1cc=c[rl6o5Br6(,(c1cc=c[rl6o5Br(,16,remove 6 from position 15,flow_matching,0.3,2.0,47,188
136,remove,14.0,r,,(c1cc=c[rl6o5Br(,(c1cc=c[rl6o5B(,15,remove r from position 14,flow_matching,0.3,2.0,47,188
137,add,3.0,o,,(c1cc=c[rl6o5B(,(c1occ=c[rl6o5B(,16,add o at position 3,flow_matching,0.3,2.0,47,188
138,remove,14.0,B,,(c1occ=c[rl6o5B(,(c1occ=c[rl6o5(,15,remove B from position 14,flow_matching,0.3,2.0,47,188
139,remove,14.0,(,,(c1occ=c[rl6o5(,(c1occ=c[rl6o5,14,remove ( from position 14,flow_matching,0.3,2.0,47,188
140,replace,3.0,F,o,(c1occ=c[rl6o5,(c1Fcc=c[rl6o5,14,replace o at position 3 with F,flow_matching,0.3,2.0,47,188
141,replace,1.0,o,c,(c1Fcc=c[rl6o5,(o1Fcc=c[rl6o5,14,replace c at position 1 with o,flow_matching,0.3,2.0,47,188
142,replace,9.0,(,r,(o1Fcc=c[rl6o5,(o1Fcc=c[(l6o5,14,replace r at position 9 with (,flow_matching,0.3,2.0,47,188
143,remove,3.0,F,,(o1Fcc=c[(l6o5,(o1cc=c[(l6o5,13,remove F from position 3,flow_matching,0.3,2.0,47,188
144,replace,0.0,C,(,(o1cc=c[(l6o5,Co1cc=c[(l6o5,13,replace ( at position 0 with C,flow_matching,0.3,2.0,47,188
145,replace,1.0,O,o,Co1cc=c[(l6o5,CO1cc=c[(l6o5,13,replace o at position 1 with O,flow_matching,0.3,2.0,47,188
146,replace,2.0,c,1,CO1cc=c[(l6o5,COccc=c[(l6o5,13,replace 1 at position 2 with c,flow_matching,0.3,2.0,47,188
147,replace,3.0,1,c,COccc=c[(l6o5,COc1c=c[(l6o5,13,replace c at position 3 with 1,flow_matching,0.3,2.0,47,188
148,replace,5.0,c,=,COc1c=c[(l6o5,COc1ccc[(l6o5,13,replace = at position 5 with c,flow_matching,0.3,2.0,47,188
149,replace,7.0,c,[,COc1ccc[(l6o5,COc1cccc(l6o5,13,replace [ at position 7 with c,flow_matching,0.3,2.0,47,188
150,replace,8.0,c,(,COc1cccc(l6o5,COc1cccccl6o5,13,replace ( at position 8 with c,flow_matching,0.3,2.0,47,188
151,replace,9.0,1,l,COc1cccccl6o5,COc1ccccc16o5,13,replace l at position 9 with 1,flow_matching,0.3,2.0,47,188
152,replace,10.0,N,6,COc1ccccc16o5,COc1ccccc1No5,13,replace 6 at position 10 with N,flow_matching,0.3,2.0,47,188
153,replace,11.0,1,o,COc1ccccc1No5,COc1ccccc1N15,13,replace o at position 11 with 1,flow_matching,0.3,2.0,47,188
154,replace,12.0,C,5,COc1ccccc1N15,COc1ccccc1N1C,13,replace 5 at position 12 with C,flow_matching,0.3,2.0,47,188
155,add,13.0,C,,COc1ccccc1N1C,COc1ccccc1N1CC,14,add C at position 13,flow_matching,0.3,2.0,47,188
156,add,14.0,N,,COc1ccccc1N1CC,COc1ccccc1N1CCN,15,add N at position 14,flow_matching,0.3,2.0,47,188
157,add,15.0,(,,COc1ccccc1N1CCN,COc1ccccc1N1CCN(,16,add ( at position 15,flow_matching,0.3,2.0,47,188
158,add,16.0,c,,COc1ccccc1N1CCN(,COc1ccccc1N1CCN(c,17,add c at position 16,flow_matching,0.3,2.0,47,188
159,add,17.0,2,,COc1ccccc1N1CCN(c,COc1ccccc1N1CCN(c2,18,add 2 at position 17,flow_matching,0.3,2.0,47,188
160,add,18.0,c,,COc1ccccc1N1CCN(c2,COc1ccccc1N1CCN(c2c,19,add c at position 18,flow_matching,0.3,2.0,47,188
161,add,19.0,c,,COc1ccccc1N1CCN(c2c,COc1ccccc1N1CCN(c2cc,20,add c at position 19,flow_matching,0.3,2.0,47,188
162,add,20.0,c,,COc1ccccc1N1CCN(c2cc,COc1ccccc1N1CCN(c2ccc,21,add c at position 20,flow_matching,0.3,2.0,47,188
163,add,21.0,(,,COc1ccccc1N1CCN(c2ccc,COc1ccccc1N1CCN(c2ccc(,22,add ( at position 21,flow_matching,0.3,2.0,47,188
164,add,22.0,=,,COc1ccccc1N1CCN(c2ccc(,COc1ccccc1N1CCN(c2ccc(=,23,add = at position 22,flow_matching,0.3,2.0,47,188
165,add,23.0,O,,COc1ccccc1N1CCN(c2ccc(=,COc1ccccc1N1CCN(c2ccc(=O,24,add O at position 23,flow_matching,0.3,2.0,47,188
166,add,24.0,),,COc1ccccc1N1CCN(c2ccc(=O,COc1ccccc1N1CCN(c2ccc(=O),25,add ) at position 24,flow_matching,0.3,2.0,47,188
167,add,25.0,n,,COc1ccccc1N1CCN(c2ccc(=O),COc1ccccc1N1CCN(c2ccc(=O)n,26,add n at position 25,flow_matching,0.3,2.0,47,188
168,add,26.0,(,,COc1ccccc1N1CCN(c2ccc(=O)n,COc1ccccc1N1CCN(c2ccc(=O)n(,27,add ( at position 26,flow_matching,0.3,2.0,47,188
169,add,27.0,C,,COc1ccccc1N1CCN(c2ccc(=O)n(,COc1ccccc1N1CCN(c2ccc(=O)n(C,28,add C at position 27,flow_matching,0.3,2.0,47,188
170,add,28.0,C,,COc1ccccc1N1CCN(c2ccc(=O)n(C,COc1ccccc1N1CCN(c2ccc(=O)n(CC,29,add C at position 28,flow_matching,0.3,2.0,47,188
171,add,29.0,(,,COc1ccccc1N1CCN(c2ccc(=O)n(CC,COc1ccccc1N1CCN(c2ccc(=O)n(CC(,30,add ( at position 29,flow_matching,0.3,2.0,47,188
172,add,30.0,=,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=,31,add = at position 30,flow_matching,0.3,2.0,47,188
173,add,31.0,O,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O,32,add O at position 31,flow_matching,0.3,2.0,47,188
174,add,32.0,),,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O),33,add ) at position 32,flow_matching,0.3,2.0,47,188
175,add,33.0,N,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O),COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)N,34,add N at position 33,flow_matching,0.3,2.0,47,188
176,add,34.0,C,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)N,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC,35,add C at position 34,flow_matching,0.3,2.0,47,188
177,add,35.0,3,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3,36,add 3 at position 35,flow_matching,0.3,2.0,47,188
178,add,36.0,C,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3C,37,add C at position 36,flow_matching,0.3,2.0,47,188
179,add,37.0,C,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3C,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC,38,add C at position 37,flow_matching,0.3,2.0,47,188
180,add,38.0,3,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3,39,add 3 at position 38,flow_matching,0.3,2.0,47,188
181,add,39.0,),,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3),40,add ) at position 39,flow_matching,0.3,2.0,47,188
182,add,40.0,n,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3),COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n,41,add n at position 40,flow_matching,0.3,2.0,47,188
183,add,41.0,2,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2,42,add 2 at position 41,flow_matching,0.3,2.0,47,188
184,add,42.0,),,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2),43,add ) at position 42,flow_matching,0.3,2.0,47,188
185,add,43.0,C,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2),COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)C,44,add C at position 43,flow_matching,0.3,2.0,47,188
186,add,44.0,C,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)C,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)CC,45,add C at position 44,flow_matching,0.3,2.0,47,188
187,add,45.0,1,,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)CC,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)CC1,46,add 1 at position 45,flow_matching,0.3,2.0,47,188
188,add,46.0,"
",,COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)CC1,"COc1ccccc1N1CCN(c2ccc(=O)n(CC(=O)NC3CC3)n2)CC1
",47,"add 
 at position 46",flow_matching,0.3,2.0,47,188
