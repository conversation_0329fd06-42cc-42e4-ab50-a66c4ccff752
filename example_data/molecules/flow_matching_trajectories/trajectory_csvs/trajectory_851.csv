step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,52,109
1,add,0.0,1,,,1,1,add 1 at position 0,flow_matching,0.3,2.0,52,109
2,replace,0.0,r,1,1,r,1,replace 1 at position 0 with r,flow_matching,0.3,2.0,52,109
3,replace,0.0,s,r,r,s,1,replace r at position 0 with s,flow_matching,0.3,2.0,52,109
4,add,0.0,1,,s,1s,2,add 1 at position 0,flow_matching,0.3,2.0,52,109
5,replace,1.0,B,s,1s,1B,2,replace s at position 1 with B,flow_matching,0.3,2.0,52,109
6,add,2.0,4,,1B,1B4,3,add 4 at position 2,flow_matching,0.3,2.0,52,109
7,replace,0.0,C,1,1B4,CB4,3,replace 1 at position 0 with C,flow_matching,0.3,2.0,52,109
8,add,2.0,3,,CB4,CB34,4,add 3 at position 2,flow_matching,0.3,2.0,52,109
9,add,3.0,),,CB34,CB3)4,5,add ) at position 3,flow_matching,0.3,2.0,52,109
10,replace,2.0,O,3,CB3)4,CBO)4,5,replace 3 at position 2 with O,flow_matching,0.3,2.0,52,109
11,add,0.0,N,,CBO)4,NCBO)4,6,add N at position 0,flow_matching,0.3,2.0,52,109
12,remove,2.0,B,,NCBO)4,NCO)4,5,remove B from position 2,flow_matching,0.3,2.0,52,109
13,replace,0.0,C,N,NCO)4,CCO)4,5,replace N at position 0 with C,flow_matching,0.3,2.0,52,109
14,add,3.0,-,,CCO)4,CCO-)4,6,add - at position 3,flow_matching,0.3,2.0,52,109
15,add,0.0,#,,CCO-)4,#CCO-)4,7,add # at position 0,flow_matching,0.3,2.0,52,109
16,replace,4.0,(,-,#CCO-)4,#CCO()4,7,replace - at position 4 with (,flow_matching,0.3,2.0,52,109
17,replace,1.0,r,C,#CCO()4,#rCO()4,7,replace C at position 1 with r,flow_matching,0.3,2.0,52,109
18,remove,5.0,),,#rCO()4,#rCO(4,6,remove ) from position 5,flow_matching,0.3,2.0,52,109
19,remove,5.0,4,,#rCO(4,#rCO(,5,remove 4 from position 5,flow_matching,0.3,2.0,52,109
20,replace,2.0,N,C,#rCO(,#rNO(,5,replace C at position 2 with N,flow_matching,0.3,2.0,52,109
21,remove,3.0,O,,#rNO(,#rN(,4,remove O from position 3,flow_matching,0.3,2.0,52,109
22,replace,3.0,3,(,#rN(,#rN3,4,replace ( at position 3 with 3,flow_matching,0.3,2.0,52,109
23,add,3.0,5,,#rN3,#rN53,5,add 5 at position 3,flow_matching,0.3,2.0,52,109
24,replace,0.0,C,#,#rN53,CrN53,5,replace # at position 0 with C,flow_matching,0.3,2.0,52,109
25,add,1.0,+,,CrN53,C+rN53,6,add + at position 1,flow_matching,0.3,2.0,52,109
26,add,2.0,l,,C+rN53,C+lrN53,7,add l at position 2,flow_matching,0.3,2.0,52,109
27,add,1.0,s,,C+lrN53,Cs+lrN53,8,add s at position 1,flow_matching,0.3,2.0,52,109
28,replace,2.0,),+,Cs+lrN53,Cs)lrN53,8,replace + at position 2 with ),flow_matching,0.3,2.0,52,109
29,add,5.0,n,,Cs)lrN53,Cs)lrnN53,9,add n at position 5,flow_matching,0.3,2.0,52,109
30,remove,2.0,),,Cs)lrnN53,CslrnN53,8,remove ) from position 2,flow_matching,0.3,2.0,52,109
31,remove,6.0,5,,CslrnN53,CslrnN3,7,remove 5 from position 6,flow_matching,0.3,2.0,52,109
32,replace,1.0,c,s,CslrnN3,CclrnN3,7,replace s at position 1 with c,flow_matching,0.3,2.0,52,109
33,replace,2.0,c,l,CclrnN3,CccrnN3,7,replace l at position 2 with c,flow_matching,0.3,2.0,52,109
34,remove,2.0,c,,CccrnN3,CcrnN3,6,remove c from position 2,flow_matching,0.3,2.0,52,109
35,replace,2.0,1,r,CcrnN3,Cc1nN3,6,replace r at position 2 with 1,flow_matching,0.3,2.0,52,109
36,remove,3.0,n,,Cc1nN3,Cc1N3,5,remove n from position 3,flow_matching,0.3,2.0,52,109
37,replace,3.0,c,N,Cc1N3,Cc1c3,5,replace N at position 3 with c,flow_matching,0.3,2.0,52,109
38,add,1.0,7,,Cc1c3,C7c1c3,6,add 7 at position 1,flow_matching,0.3,2.0,52,109
39,replace,4.0,2,c,C7c1c3,C7c123,6,replace c at position 4 with 2,flow_matching,0.3,2.0,52,109
40,replace,5.0,n,3,C7c123,C7c12n,6,replace 3 at position 5 with n,flow_matching,0.3,2.0,52,109
41,replace,5.0,I,n,C7c12n,C7c12I,6,replace n at position 5 with I,flow_matching,0.3,2.0,52,109
42,add,3.0,2,,C7c12I,C7c212I,7,add 2 at position 3,flow_matching,0.3,2.0,52,109
43,remove,2.0,c,,C7c212I,C7212I,6,remove c from position 2,flow_matching,0.3,2.0,52,109
44,remove,0.0,C,,C7212I,7212I,5,remove C from position 0,flow_matching,0.3,2.0,52,109
45,replace,0.0,C,7,7212I,C212I,5,replace 7 at position 0 with C,flow_matching,0.3,2.0,52,109
46,remove,4.0,I,,C212I,C212,4,remove I from position 4,flow_matching,0.3,2.0,52,109
47,replace,0.0,I,C,C212,I212,4,replace C at position 0 with I,flow_matching,0.3,2.0,52,109
48,add,4.0,1,,I212,I2121,5,add 1 at position 4,flow_matching,0.3,2.0,52,109
49,replace,0.0,C,I,I2121,C2121,5,replace I at position 0 with C,flow_matching,0.3,2.0,52,109
50,replace,1.0,c,2,C2121,Cc121,5,replace 2 at position 1 with c,flow_matching,0.3,2.0,52,109
51,replace,4.0,3,1,Cc121,Cc123,5,replace 1 at position 4 with 3,flow_matching,0.3,2.0,52,109
52,add,5.0,2,,Cc123,Cc1232,6,add 2 at position 5,flow_matching,0.3,2.0,52,109
53,replace,3.0,c,2,Cc1232,Cc1c32,6,replace 2 at position 3 with c,flow_matching,0.3,2.0,52,109
54,replace,4.0,c,3,Cc1c32,Cc1cc2,6,replace 3 at position 4 with c,flow_matching,0.3,2.0,52,109
55,replace,5.0,c,2,Cc1cc2,Cc1ccc,6,replace 2 at position 5 with c,flow_matching,0.3,2.0,52,109
56,add,0.0,s,,Cc1ccc,sCc1ccc,7,add s at position 0,flow_matching,0.3,2.0,52,109
57,remove,0.0,s,,sCc1ccc,Cc1ccc,6,remove s from position 0,flow_matching,0.3,2.0,52,109
58,remove,4.0,c,,Cc1ccc,Cc1cc,5,remove c from position 4,flow_matching,0.3,2.0,52,109
59,replace,2.0,o,1,Cc1cc,Ccocc,5,replace 1 at position 2 with o,flow_matching,0.3,2.0,52,109
60,add,5.0,r,,Ccocc,Ccoccr,6,add r at position 5,flow_matching,0.3,2.0,52,109
61,add,6.0,1,,Ccoccr,Ccoccr1,7,add 1 at position 6,flow_matching,0.3,2.0,52,109
62,replace,2.0,1,o,Ccoccr1,Cc1ccr1,7,replace o at position 2 with 1,flow_matching,0.3,2.0,52,109
63,replace,5.0,c,r,Cc1ccr1,Cc1ccc1,7,replace r at position 5 with c,flow_matching,0.3,2.0,52,109
64,replace,6.0,2,1,Cc1ccc1,Cc1ccc2,7,replace 1 at position 6 with 2,flow_matching,0.3,2.0,52,109
65,add,7.0,n,,Cc1ccc2,Cc1ccc2n,8,add n at position 7,flow_matching,0.3,2.0,52,109
66,add,8.0,c,,Cc1ccc2n,Cc1ccc2nc,9,add c at position 8,flow_matching,0.3,2.0,52,109
67,add,9.0,(,,Cc1ccc2nc,Cc1ccc2nc(,10,add ( at position 9,flow_matching,0.3,2.0,52,109
68,add,10.0,N,,Cc1ccc2nc(,Cc1ccc2nc(N,11,add N at position 10,flow_matching,0.3,2.0,52,109
69,add,11.0,C,,Cc1ccc2nc(N,Cc1ccc2nc(NC,12,add C at position 11,flow_matching,0.3,2.0,52,109
70,add,12.0,(,,Cc1ccc2nc(NC,Cc1ccc2nc(NC(,13,add ( at position 12,flow_matching,0.3,2.0,52,109
71,add,13.0,=,,Cc1ccc2nc(NC(,Cc1ccc2nc(NC(=,14,add = at position 13,flow_matching,0.3,2.0,52,109
72,add,14.0,O,,Cc1ccc2nc(NC(=,Cc1ccc2nc(NC(=O,15,add O at position 14,flow_matching,0.3,2.0,52,109
73,add,15.0,),,Cc1ccc2nc(NC(=O,Cc1ccc2nc(NC(=O),16,add ) at position 15,flow_matching,0.3,2.0,52,109
74,add,16.0,c,,Cc1ccc2nc(NC(=O),Cc1ccc2nc(NC(=O)c,17,add c at position 16,flow_matching,0.3,2.0,52,109
75,add,17.0,3,,Cc1ccc2nc(NC(=O)c,Cc1ccc2nc(NC(=O)c3,18,add 3 at position 17,flow_matching,0.3,2.0,52,109
76,add,18.0,c,,Cc1ccc2nc(NC(=O)c3,Cc1ccc2nc(NC(=O)c3c,19,add c at position 18,flow_matching,0.3,2.0,52,109
77,add,19.0,c,,Cc1ccc2nc(NC(=O)c3c,Cc1ccc2nc(NC(=O)c3cc,20,add c at position 19,flow_matching,0.3,2.0,52,109
78,add,20.0,c,,Cc1ccc2nc(NC(=O)c3cc,Cc1ccc2nc(NC(=O)c3ccc,21,add c at position 20,flow_matching,0.3,2.0,52,109
79,add,21.0,(,,Cc1ccc2nc(NC(=O)c3ccc,Cc1ccc2nc(NC(=O)c3ccc(,22,add ( at position 21,flow_matching,0.3,2.0,52,109
80,add,22.0,O,,Cc1ccc2nc(NC(=O)c3ccc(,Cc1ccc2nc(NC(=O)c3ccc(O,23,add O at position 22,flow_matching,0.3,2.0,52,109
81,add,23.0,C,,Cc1ccc2nc(NC(=O)c3ccc(O,Cc1ccc2nc(NC(=O)c3ccc(OC,24,add C at position 23,flow_matching,0.3,2.0,52,109
82,add,24.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OC,Cc1ccc2nc(NC(=O)c3ccc(OCc,25,add c at position 24,flow_matching,0.3,2.0,52,109
83,add,25.0,4,,Cc1ccc2nc(NC(=O)c3ccc(OCc,Cc1ccc2nc(NC(=O)c3ccc(OCc4,26,add 4 at position 25,flow_matching,0.3,2.0,52,109
84,add,26.0,n,,Cc1ccc2nc(NC(=O)c3ccc(OCc4,Cc1ccc2nc(NC(=O)c3ccc(OCc4n,27,add n at position 26,flow_matching,0.3,2.0,52,109
85,add,27.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OCc4n,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc,28,add c at position 27,flow_matching,0.3,2.0,52,109
86,add,28.0,(,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(,29,add ( at position 28,flow_matching,0.3,2.0,52,109
87,add,29.0,-,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-,30,add - at position 29,flow_matching,0.3,2.0,52,109
88,add,30.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c,31,add c at position 30,flow_matching,0.3,2.0,52,109
89,add,31.0,5,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5,32,add 5 at position 31,flow_matching,0.3,2.0,52,109
90,add,32.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5c,33,add c at position 32,flow_matching,0.3,2.0,52,109
91,add,33.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5c,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5cc,34,add c at position 33,flow_matching,0.3,2.0,52,109
92,add,34.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5cc,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccc,35,add c at position 34,flow_matching,0.3,2.0,52,109
93,add,35.0,o,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccc,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco,36,add o at position 35,flow_matching,0.3,2.0,52,109
94,add,36.0,5,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5,37,add 5 at position 36,flow_matching,0.3,2.0,52,109
95,add,37.0,),,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5),38,add ) at position 37,flow_matching,0.3,2.0,52,109
96,add,38.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5),Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)c,39,add c at position 38,flow_matching,0.3,2.0,52,109
97,add,39.0,s,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)c,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs,40,add s at position 39,flow_matching,0.3,2.0,52,109
98,add,40.0,4,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4,41,add 4 at position 40,flow_matching,0.3,2.0,52,109
99,add,41.0,),,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4),42,add ) at position 41,flow_matching,0.3,2.0,52,109
100,add,42.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4),Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)c,43,add c at position 42,flow_matching,0.3,2.0,52,109
101,add,43.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)c,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc,44,add c at position 43,flow_matching,0.3,2.0,52,109
102,add,44.0,3,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3,45,add 3 at position 44,flow_matching,0.3,2.0,52,109
103,add,45.0,),,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3),46,add ) at position 45,flow_matching,0.3,2.0,52,109
104,add,46.0,s,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3),Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)s,47,add s at position 46,flow_matching,0.3,2.0,52,109
105,add,47.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)s,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)sc,48,add c at position 47,flow_matching,0.3,2.0,52,109
106,add,48.0,2,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)sc,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)sc2,49,add 2 at position 48,flow_matching,0.3,2.0,52,109
107,add,49.0,c,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)sc2,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)sc2c,50,add c at position 49,flow_matching,0.3,2.0,52,109
108,add,50.0,1,,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)sc2c,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)sc2c1,51,add 1 at position 50,flow_matching,0.3,2.0,52,109
109,add,51.0,"
",,Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)sc2c1,"Cc1ccc2nc(NC(=O)c3ccc(OCc4nc(-c5ccco5)cs4)cc3)sc2c1
",52,"add 
 at position 51",flow_matching,0.3,2.0,52,109
