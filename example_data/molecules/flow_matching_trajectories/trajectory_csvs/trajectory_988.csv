step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,50,228
1,add,0.0,C,,,C,1,add <PERSON> at position 0,flow_matching,0.3,2.0,50,228
2,add,1.0,C,,C,CC,2,add <PERSON> at position 1,flow_matching,0.3,2.0,50,228
3,add,2.0,N,,CC,CCN,3,add N at position 2,flow_matching,0.3,2.0,50,228
4,add,3.0,1,,CCN,CCN1,4,add 1 at position 3,flow_matching,0.3,2.0,50,228
5,add,4.0,C,,CCN1,CCN1C,5,add <PERSON> at position 4,flow_matching,0.3,2.0,50,228
6,add,5.0,<PERSON>,,<PERSON><PERSON>1<PERSON>,CCN1CC,6,add <PERSON> at position 5,flow_matching,0.3,2.0,50,228
7,remove,5.0,<PERSON>,,<PERSON>N1<PERSON>,CCN1C,5,remove <PERSON> from position 5,flow_matching,0.3,2.0,50,228
8,add,5.0,C,,CCN1C,CCN1CC,6,add C at position 5,flow_matching,0.3,2.0,50,228
9,remove,0.0,C,,CCN1CC,CN1CC,5,remove C from position 0,flow_matching,0.3,2.0,50,228
10,remove,0.0,C,,CN1CC,N1CC,4,remove C from position 0,flow_matching,0.3,2.0,50,228
11,add,3.0,@,,N1CC,N1C@C,5,add @ at position 3,flow_matching,0.3,2.0,50,228
12,replace,0.0,C,N,N1C@C,C1C@C,5,replace N at position 0 with C,flow_matching,0.3,2.0,50,228
13,replace,1.0,C,1,C1C@C,CCC@C,5,replace 1 at position 1 with C,flow_matching,0.3,2.0,50,228
14,replace,2.0,N,C,CCC@C,CCN@C,5,replace C at position 2 with N,flow_matching,0.3,2.0,50,228
15,replace,3.0,1,@,CCN@C,CCN1C,5,replace @ at position 3 with 1,flow_matching,0.3,2.0,50,228
16,add,5.0,C,,CCN1C,CCN1CC,6,add C at position 5,flow_matching,0.3,2.0,50,228
17,remove,0.0,C,,CCN1CC,CN1CC,5,remove C from position 0,flow_matching,0.3,2.0,50,228
18,remove,1.0,N,,CN1CC,C1CC,4,remove N from position 1,flow_matching,0.3,2.0,50,228
19,remove,2.0,C,,C1CC,C1C,3,remove C from position 2,flow_matching,0.3,2.0,50,228
20,replace,1.0,C,1,C1C,CCC,3,replace 1 at position 1 with C,flow_matching,0.3,2.0,50,228
21,replace,2.0,N,C,CCC,CCN,3,replace C at position 2 with N,flow_matching,0.3,2.0,50,228
22,add,1.0,3,,CCN,C3CN,4,add 3 at position 1,flow_matching,0.3,2.0,50,228
23,replace,1.0,C,3,C3CN,CCCN,4,replace 3 at position 1 with C,flow_matching,0.3,2.0,50,228
24,replace,2.0,N,C,CCCN,CCNN,4,replace C at position 2 with N,flow_matching,0.3,2.0,50,228
25,replace,3.0,1,N,CCNN,CCN1,4,replace N at position 3 with 1,flow_matching,0.3,2.0,50,228
26,remove,3.0,1,,CCN1,CCN,3,remove 1 from position 3,flow_matching,0.3,2.0,50,228
27,add,1.0,I,,CCN,CICN,4,add I at position 1,flow_matching,0.3,2.0,50,228
28,add,3.0,I,,CICN,CICIN,5,add I at position 3,flow_matching,0.3,2.0,50,228
29,replace,1.0,C,I,CICIN,CCCIN,5,replace I at position 1 with C,flow_matching,0.3,2.0,50,228
30,add,2.0,F,,CCCIN,CCFCIN,6,add F at position 2,flow_matching,0.3,2.0,50,228
31,replace,5.0,F,N,CCFCIN,CCFCIF,6,replace N at position 5 with F,flow_matching,0.3,2.0,50,228
32,remove,2.0,F,,CCFCIF,CCCIF,5,remove F from position 2,flow_matching,0.3,2.0,50,228
33,replace,2.0,N,C,CCCIF,CCNIF,5,replace C at position 2 with N,flow_matching,0.3,2.0,50,228
34,replace,3.0,1,I,CCNIF,CCN1F,5,replace I at position 3 with 1,flow_matching,0.3,2.0,50,228
35,replace,4.0,5,F,CCN1F,CCN15,5,replace F at position 4 with 5,flow_matching,0.3,2.0,50,228
36,replace,1.0,N,C,CCN15,CNN15,5,replace C at position 1 with N,flow_matching,0.3,2.0,50,228
37,replace,0.0,4,C,CNN15,4NN15,5,replace C at position 0 with 4,flow_matching,0.3,2.0,50,228
38,remove,4.0,5,,4NN15,4NN1,4,remove 5 from position 4,flow_matching,0.3,2.0,50,228
39,replace,2.0,(,N,4NN1,4N(1,4,replace N at position 2 with (,flow_matching,0.3,2.0,50,228
40,remove,3.0,1,,4N(1,4N(,3,remove 1 from position 3,flow_matching,0.3,2.0,50,228
41,add,0.0,@,,4N(,@4N(,4,add @ at position 0,flow_matching,0.3,2.0,50,228
42,add,2.0,7,,@4N(,@47N(,5,add 7 at position 2,flow_matching,0.3,2.0,50,228
43,add,1.0,O,,@47N(,@O47N(,6,add O at position 1,flow_matching,0.3,2.0,50,228
44,remove,1.0,O,,@O47N(,@47N(,5,remove O from position 1,flow_matching,0.3,2.0,50,228
45,remove,2.0,7,,@47N(,@4N(,4,remove 7 from position 2,flow_matching,0.3,2.0,50,228
46,add,4.0,2,,@4N(,@4N(2,5,add 2 at position 4,flow_matching,0.3,2.0,50,228
47,replace,0.0,C,@,@4N(2,C4N(2,5,replace @ at position 0 with C,flow_matching,0.3,2.0,50,228
48,add,2.0,O,,C4N(2,C4ON(2,6,add O at position 2,flow_matching,0.3,2.0,50,228
49,add,3.0,3,,C4ON(2,C4O3N(2,7,add 3 at position 3,flow_matching,0.3,2.0,50,228
50,remove,2.0,O,,C4O3N(2,C43N(2,6,remove O from position 2,flow_matching,0.3,2.0,50,228
51,remove,5.0,2,,C43N(2,C43N(,5,remove 2 from position 5,flow_matching,0.3,2.0,50,228
52,replace,1.0,C,4,C43N(,CC3N(,5,replace 4 at position 1 with C,flow_matching,0.3,2.0,50,228
53,add,0.0,6,,CC3N(,6CC3N(,6,add 6 at position 0,flow_matching,0.3,2.0,50,228
54,replace,1.0,c,C,6CC3N(,6cC3N(,6,replace C at position 1 with c,flow_matching,0.3,2.0,50,228
55,replace,0.0,C,6,6cC3N(,CcC3N(,6,replace 6 at position 0 with C,flow_matching,0.3,2.0,50,228
56,add,2.0,H,,CcC3N(,CcHC3N(,7,add H at position 2,flow_matching,0.3,2.0,50,228
57,replace,0.0,#,C,CcHC3N(,#cHC3N(,7,replace C at position 0 with #,flow_matching,0.3,2.0,50,228
58,replace,0.0,C,#,#cHC3N(,CcHC3N(,7,replace # at position 0 with C,flow_matching,0.3,2.0,50,228
59,add,0.0,B,,CcHC3N(,BCcHC3N(,8,add B at position 0,flow_matching,0.3,2.0,50,228
60,replace,0.0,C,B,BCcHC3N(,CCcHC3N(,8,replace B at position 0 with C,flow_matching,0.3,2.0,50,228
61,add,0.0,s,,CCcHC3N(,sCCcHC3N(,9,add s at position 0,flow_matching,0.3,2.0,50,228
62,replace,7.0,),N,sCCcHC3N(,sCCcHC3)(,9,replace N at position 7 with ),flow_matching,0.3,2.0,50,228
63,remove,7.0,),,sCCcHC3)(,sCCcHC3(,8,remove ) from position 7,flow_matching,0.3,2.0,50,228
64,replace,0.0,C,s,sCCcHC3(,CCCcHC3(,8,replace s at position 0 with C,flow_matching,0.3,2.0,50,228
65,remove,2.0,C,,CCCcHC3(,CCcHC3(,7,remove C from position 2,flow_matching,0.3,2.0,50,228
66,remove,0.0,C,,CCcHC3(,CcHC3(,6,remove C from position 0,flow_matching,0.3,2.0,50,228
67,replace,1.0,C,c,CcHC3(,CCHC3(,6,replace c at position 1 with C,flow_matching,0.3,2.0,50,228
68,add,5.0,N,,CCHC3(,CCHC3N(,7,add N at position 5,flow_matching,0.3,2.0,50,228
69,replace,3.0,c,C,CCHC3N(,CCHc3N(,7,replace C at position 3 with c,flow_matching,0.3,2.0,50,228
70,remove,2.0,H,,CCHc3N(,CCc3N(,6,remove H from position 2,flow_matching,0.3,2.0,50,228
71,add,0.0,l,,CCc3N(,lCCc3N(,7,add l at position 0,flow_matching,0.3,2.0,50,228
72,remove,1.0,C,,lCCc3N(,lCc3N(,6,remove C from position 1,flow_matching,0.3,2.0,50,228
73,replace,0.0,C,l,lCc3N(,CCc3N(,6,replace l at position 0 with C,flow_matching,0.3,2.0,50,228
74,replace,0.0,5,C,CCc3N(,5Cc3N(,6,replace C at position 0 with 5,flow_matching,0.3,2.0,50,228
75,replace,0.0,C,5,5Cc3N(,CCc3N(,6,replace 5 at position 0 with C,flow_matching,0.3,2.0,50,228
76,remove,5.0,(,,CCc3N(,CCc3N,5,remove ( from position 5,flow_matching,0.3,2.0,50,228
77,add,4.0,O,,CCc3N,CCc3ON,6,add O at position 4,flow_matching,0.3,2.0,50,228
78,replace,2.0,O,c,CCc3ON,CCO3ON,6,replace c at position 2 with O,flow_matching,0.3,2.0,50,228
79,replace,2.0,N,O,CCO3ON,CCN3ON,6,replace O at position 2 with N,flow_matching,0.3,2.0,50,228
80,replace,3.0,1,3,CCN3ON,CCN1ON,6,replace 3 at position 3 with 1,flow_matching,0.3,2.0,50,228
81,remove,1.0,C,,CCN1ON,CN1ON,5,remove C from position 1,flow_matching,0.3,2.0,50,228
82,add,3.0,1,,CN1ON,CN11ON,6,add 1 at position 3,flow_matching,0.3,2.0,50,228
83,remove,0.0,C,,CN11ON,N11ON,5,remove C from position 0,flow_matching,0.3,2.0,50,228
84,replace,0.0,#,N,N11ON,#11ON,5,replace N at position 0 with #,flow_matching,0.3,2.0,50,228
85,replace,0.0,C,#,#11ON,C11ON,5,replace # at position 0 with C,flow_matching,0.3,2.0,50,228
86,add,4.0,#,,C11ON,C11O#N,6,add # at position 4,flow_matching,0.3,2.0,50,228
87,replace,1.0,4,1,C11O#N,C41O#N,6,replace 1 at position 1 with 4,flow_matching,0.3,2.0,50,228
88,remove,2.0,1,,C41O#N,C4O#N,5,remove 1 from position 2,flow_matching,0.3,2.0,50,228
89,replace,3.0,n,#,C4O#N,C4OnN,5,replace # at position 3 with n,flow_matching,0.3,2.0,50,228
90,remove,4.0,N,,C4OnN,C4On,4,remove N from position 4,flow_matching,0.3,2.0,50,228
91,add,3.0,S,,C4On,C4OSn,5,add S at position 3,flow_matching,0.3,2.0,50,228
92,replace,1.0,I,4,C4OSn,CIOSn,5,replace 4 at position 1 with I,flow_matching,0.3,2.0,50,228
93,replace,4.0,S,n,CIOSn,CIOSS,5,replace n at position 4 with S,flow_matching,0.3,2.0,50,228
94,replace,3.0,N,S,CIOSS,CIONS,5,replace S at position 3 with N,flow_matching,0.3,2.0,50,228
95,add,2.0,C,,CIONS,CICONS,6,add C at position 2,flow_matching,0.3,2.0,50,228
96,add,4.0,6,,CICONS,CICO6NS,7,add 6 at position 4,flow_matching,0.3,2.0,50,228
97,add,3.0,[,,CICO6NS,CIC[O6NS,8,add [ at position 3,flow_matching,0.3,2.0,50,228
98,add,6.0,I,,CIC[O6NS,CIC[O6INS,9,add I at position 6,flow_matching,0.3,2.0,50,228
99,replace,1.0,C,I,CIC[O6INS,CCC[O6INS,9,replace I at position 1 with C,flow_matching,0.3,2.0,50,228
100,replace,8.0,5,S,CCC[O6INS,CCC[O6IN5,9,replace S at position 8 with 5,flow_matching,0.3,2.0,50,228
101,add,2.0,5,,CCC[O6IN5,CC5C[O6IN5,10,add 5 at position 2,flow_matching,0.3,2.0,50,228
102,remove,1.0,C,,CC5C[O6IN5,C5C[O6IN5,9,remove C from position 1,flow_matching,0.3,2.0,50,228
103,replace,1.0,C,5,C5C[O6IN5,CCC[O6IN5,9,replace 5 at position 1 with C,flow_matching,0.3,2.0,50,228
104,remove,0.0,C,,CCC[O6IN5,CC[O6IN5,8,remove C from position 0,flow_matching,0.3,2.0,50,228
105,add,6.0,n,,CC[O6IN5,CC[O6InN5,9,add n at position 6,flow_matching,0.3,2.0,50,228
106,replace,5.0,@,I,CC[O6InN5,CC[O6@nN5,9,replace I at position 5 with @,flow_matching,0.3,2.0,50,228
107,remove,5.0,@,,CC[O6@nN5,CC[O6nN5,8,remove @ from position 5,flow_matching,0.3,2.0,50,228
108,replace,5.0,B,n,CC[O6nN5,CC[O6BN5,8,replace n at position 5 with B,flow_matching,0.3,2.0,50,228
109,add,8.0,=,,CC[O6BN5,CC[O6BN5=,9,add = at position 8,flow_matching,0.3,2.0,50,228
110,remove,3.0,O,,CC[O6BN5=,CC[6BN5=,8,remove O from position 3,flow_matching,0.3,2.0,50,228
111,add,0.0,o,,CC[6BN5=,oCC[6BN5=,9,add o at position 0,flow_matching,0.3,2.0,50,228
112,remove,5.0,B,,oCC[6BN5=,oCC[6N5=,8,remove B from position 5,flow_matching,0.3,2.0,50,228
113,replace,0.0,C,o,oCC[6N5=,CCC[6N5=,8,replace o at position 0 with C,flow_matching,0.3,2.0,50,228
114,remove,0.0,C,,CCC[6N5=,CC[6N5=,7,remove C from position 0,flow_matching,0.3,2.0,50,228
115,replace,2.0,N,[,CC[6N5=,CCN6N5=,7,replace [ at position 2 with N,flow_matching,0.3,2.0,50,228
116,replace,4.0,F,N,CCN6N5=,CCN6F5=,7,replace N at position 4 with F,flow_matching,0.3,2.0,50,228
117,replace,3.0,1,6,CCN6F5=,CCN1F5=,7,replace 6 at position 3 with 1,flow_matching,0.3,2.0,50,228
118,add,2.0,=,,CCN1F5=,CC=N1F5=,8,add = at position 2,flow_matching,0.3,2.0,50,228
119,add,3.0,[,,CC=N1F5=,CC=[N1F5=,9,add [ at position 3,flow_matching,0.3,2.0,50,228
120,remove,3.0,[,,CC=[N1F5=,CC=N1F5=,8,remove [ from position 3,flow_matching,0.3,2.0,50,228
121,add,3.0,5,,CC=N1F5=,CC=5N1F5=,9,add 5 at position 3,flow_matching,0.3,2.0,50,228
122,replace,2.0,N,=,CC=5N1F5=,CCN5N1F5=,9,replace = at position 2 with N,flow_matching,0.3,2.0,50,228
123,replace,6.0,r,F,CCN5N1F5=,CCN5N1r5=,9,replace F at position 6 with r,flow_matching,0.3,2.0,50,228
124,replace,3.0,1,5,CCN5N1r5=,CCN1N1r5=,9,replace 5 at position 3 with 1,flow_matching,0.3,2.0,50,228
125,replace,4.0,C,N,CCN1N1r5=,CCN1C1r5=,9,replace N at position 4 with C,flow_matching,0.3,2.0,50,228
126,add,5.0,@,,CCN1C1r5=,CCN1C@1r5=,10,add @ at position 5,flow_matching,0.3,2.0,50,228
127,remove,2.0,N,,CCN1C@1r5=,CC1C@1r5=,9,remove N from position 2,flow_matching,0.3,2.0,50,228
128,add,4.0,s,,CC1C@1r5=,CC1Cs@1r5=,10,add s at position 4,flow_matching,0.3,2.0,50,228
129,replace,1.0,\,C,CC1Cs@1r5=,C\1Cs@1r5=,10,replace C at position 1 with \,flow_matching,0.3,2.0,50,228
130,replace,1.0,C,\,C\1Cs@1r5=,CC1Cs@1r5=,10,replace \ at position 1 with C,flow_matching,0.3,2.0,50,228
131,replace,2.0,N,1,CC1Cs@1r5=,CCNCs@1r5=,10,replace 1 at position 2 with N,flow_matching,0.3,2.0,50,228
132,add,10.0,@,,CCNCs@1r5=,CCNCs@1r5=@,11,add @ at position 10,flow_matching,0.3,2.0,50,228
133,add,8.0,),,CCNCs@1r5=@,CCNCs@1r)5=@,12,add ) at position 8,flow_matching,0.3,2.0,50,228
134,replace,3.0,1,C,CCNCs@1r)5=@,CCN1s@1r)5=@,12,replace C at position 3 with 1,flow_matching,0.3,2.0,50,228
135,remove,9.0,5,,CCN1s@1r)5=@,CCN1s@1r)=@,11,remove 5 from position 9,flow_matching,0.3,2.0,50,228
136,add,10.0,r,,CCN1s@1r)=@,CCN1s@1r)=r@,12,add r at position 10,flow_matching,0.3,2.0,50,228
137,replace,4.0,C,s,CCN1s@1r)=r@,CCN1C@1r)=r@,12,replace s at position 4 with C,flow_matching,0.3,2.0,50,228
138,add,3.0,n,,CCN1C@1r)=r@,CCNn1C@1r)=r@,13,add n at position 3,flow_matching,0.3,2.0,50,228
139,add,11.0,4,,CCNn1C@1r)=r@,CCNn1C@1r)=4r@,14,add 4 at position 11,flow_matching,0.3,2.0,50,228
140,replace,3.0,1,n,CCNn1C@1r)=4r@,CCN11C@1r)=4r@,14,replace n at position 3 with 1,flow_matching,0.3,2.0,50,228
141,replace,13.0,1,@,CCN11C@1r)=4r@,CCN11C@1r)=4r1,14,replace @ at position 13 with 1,flow_matching,0.3,2.0,50,228
142,add,10.0,[,,CCN11C@1r)=4r1,CCN11C@1r)[=4r1,15,add [ at position 10,flow_matching,0.3,2.0,50,228
143,remove,6.0,@,,CCN11C@1r)[=4r1,CCN11C1r)[=4r1,14,remove @ from position 6,flow_matching,0.3,2.0,50,228
144,replace,4.0,C,1,CCN11C1r)[=4r1,CCN1CC1r)[=4r1,14,replace 1 at position 4 with C,flow_matching,0.3,2.0,50,228
145,replace,6.0,(,1,CCN1CC1r)[=4r1,CCN1CC(r)[=4r1,14,replace 1 at position 6 with (,flow_matching,0.3,2.0,50,228
146,add,3.0,I,,CCN1CC(r)[=4r1,CCNI1CC(r)[=4r1,15,add I at position 3,flow_matching,0.3,2.0,50,228
147,add,8.0,=,,CCNI1CC(r)[=4r1,CCNI1CC(=r)[=4r1,16,add = at position 8,flow_matching,0.3,2.0,50,228
148,replace,5.0,S,C,CCNI1CC(=r)[=4r1,CCNI1SC(=r)[=4r1,16,replace C at position 5 with S,flow_matching,0.3,2.0,50,228
149,replace,1.0,F,C,CCNI1SC(=r)[=4r1,CFNI1SC(=r)[=4r1,16,replace C at position 1 with F,flow_matching,0.3,2.0,50,228
150,add,12.0,=,,CFNI1SC(=r)[=4r1,CFNI1SC(=r)[==4r1,17,add = at position 12,flow_matching,0.3,2.0,50,228
151,replace,8.0,#,=,CFNI1SC(=r)[==4r1,CFNI1SC(#r)[==4r1,17,replace = at position 8 with #,flow_matching,0.3,2.0,50,228
152,remove,1.0,F,,CFNI1SC(#r)[==4r1,CNI1SC(#r)[==4r1,16,remove F from position 1,flow_matching,0.3,2.0,50,228
153,add,6.0,F,,CNI1SC(#r)[==4r1,CNI1SCF(#r)[==4r1,17,add F at position 6,flow_matching,0.3,2.0,50,228
154,add,2.0,F,,CNI1SCF(#r)[==4r1,CNFI1SCF(#r)[==4r1,18,add F at position 2,flow_matching,0.3,2.0,50,228
155,add,8.0,C,,CNFI1SCF(#r)[==4r1,CNFI1SCFC(#r)[==4r1,19,add C at position 8,flow_matching,0.3,2.0,50,228
156,replace,1.0,C,N,CNFI1SCFC(#r)[==4r1,CCFI1SCFC(#r)[==4r1,19,replace N at position 1 with C,flow_matching,0.3,2.0,50,228
157,add,3.0,I,,CCFI1SCFC(#r)[==4r1,CCFII1SCFC(#r)[==4r1,20,add I at position 3,flow_matching,0.3,2.0,50,228
158,remove,16.0,=,,CCFII1SCFC(#r)[==4r1,CCFII1SCFC(#r)[=4r1,19,remove = from position 16,flow_matching,0.3,2.0,50,228
159,replace,2.0,N,F,CCFII1SCFC(#r)[=4r1,CCNII1SCFC(#r)[=4r1,19,replace F at position 2 with N,flow_matching,0.3,2.0,50,228
160,replace,4.0,=,I,CCNII1SCFC(#r)[=4r1,CCNI=1SCFC(#r)[=4r1,19,replace I at position 4 with =,flow_matching,0.3,2.0,50,228
161,remove,16.0,4,,CCNI=1SCFC(#r)[=4r1,CCNI=1SCFC(#r)[=r1,18,remove 4 from position 16,flow_matching,0.3,2.0,50,228
162,remove,15.0,=,,CCNI=1SCFC(#r)[=r1,CCNI=1SCFC(#r)[r1,17,remove = from position 15,flow_matching,0.3,2.0,50,228
163,remove,2.0,N,,CCNI=1SCFC(#r)[r1,CCI=1SCFC(#r)[r1,16,remove N from position 2,flow_matching,0.3,2.0,50,228
164,add,6.0,3,,CCI=1SCFC(#r)[r1,CCI=1S3CFC(#r)[r1,17,add 3 at position 6,flow_matching,0.3,2.0,50,228
165,replace,2.0,N,I,CCI=1S3CFC(#r)[r1,CCN=1S3CFC(#r)[r1,17,replace I at position 2 with N,flow_matching,0.3,2.0,50,228
166,replace,3.0,1,=,CCN=1S3CFC(#r)[r1,CCN11S3CFC(#r)[r1,17,replace = at position 3 with 1,flow_matching,0.3,2.0,50,228
167,add,16.0,r,,CCN11S3CFC(#r)[r1,CCN11S3CFC(#r)[rr1,18,add r at position 16,flow_matching,0.3,2.0,50,228
168,add,5.0,),,CCN11S3CFC(#r)[rr1,CCN11)S3CFC(#r)[rr1,19,add ) at position 5,flow_matching,0.3,2.0,50,228
169,add,2.0,6,,CCN11)S3CFC(#r)[rr1,CC6N11)S3CFC(#r)[rr1,20,add 6 at position 2,flow_matching,0.3,2.0,50,228
170,add,19.0,I,,CC6N11)S3CFC(#r)[rr1,CC6N11)S3CFC(#r)[rrI1,21,add I at position 19,flow_matching,0.3,2.0,50,228
171,remove,9.0,C,,CC6N11)S3CFC(#r)[rrI1,CC6N11)S3FC(#r)[rrI1,20,remove C from position 9,flow_matching,0.3,2.0,50,228
172,replace,2.0,N,6,CC6N11)S3FC(#r)[rrI1,CCNN11)S3FC(#r)[rrI1,20,replace 6 at position 2 with N,flow_matching,0.3,2.0,50,228
173,add,12.0,7,,CCNN11)S3FC(#r)[rrI1,CCNN11)S3FC(7#r)[rrI1,21,add 7 at position 12,flow_matching,0.3,2.0,50,228
174,remove,18.0,r,,CCNN11)S3FC(7#r)[rrI1,CCNN11)S3FC(7#r)[rI1,20,remove r from position 18,flow_matching,0.3,2.0,50,228
175,replace,3.0,1,N,CCNN11)S3FC(7#r)[rI1,CCN111)S3FC(7#r)[rI1,20,replace N at position 3 with 1,flow_matching,0.3,2.0,50,228
176,remove,9.0,F,,CCN111)S3FC(7#r)[rI1,CCN111)S3C(7#r)[rI1,19,remove F from position 9,flow_matching,0.3,2.0,50,228
177,replace,4.0,C,1,CCN111)S3C(7#r)[rI1,CCN1C1)S3C(7#r)[rI1,19,replace 1 at position 4 with C,flow_matching,0.3,2.0,50,228
178,replace,5.0,C,1,CCN1C1)S3C(7#r)[rI1,CCN1CC)S3C(7#r)[rI1,19,replace 1 at position 5 with C,flow_matching,0.3,2.0,50,228
179,remove,12.0,#,,CCN1CC)S3C(7#r)[rI1,CCN1CC)S3C(7r)[rI1,18,remove # from position 12,flow_matching,0.3,2.0,50,228
180,add,18.0,c,,CCN1CC)S3C(7r)[rI1,CCN1CC)S3C(7r)[rI1c,19,add c at position 18,flow_matching,0.3,2.0,50,228
181,add,8.0,F,,CCN1CC)S3C(7r)[rI1c,CCN1CC)SF3C(7r)[rI1c,20,add F at position 8,flow_matching,0.3,2.0,50,228
182,replace,9.0,[,3,CCN1CC)SF3C(7r)[rI1c,CCN1CC)SF[C(7r)[rI1c,20,replace 3 at position 9 with [,flow_matching,0.3,2.0,50,228
183,replace,6.0,(,),CCN1CC)SF[C(7r)[rI1c,CCN1CC(SF[C(7r)[rI1c,20,replace ) at position 6 with (,flow_matching,0.3,2.0,50,228
184,replace,6.0,S,(,CCN1CC(SF[C(7r)[rI1c,CCN1CCSSF[C(7r)[rI1c,20,replace ( at position 6 with S,flow_matching,0.3,2.0,50,228
185,replace,6.0,(,S,CCN1CCSSF[C(7r)[rI1c,CCN1CC(SF[C(7r)[rI1c,20,replace S at position 6 with (,flow_matching,0.3,2.0,50,228
186,replace,7.0,=,S,CCN1CC(SF[C(7r)[rI1c,CCN1CC(=F[C(7r)[rI1c,20,replace S at position 7 with =,flow_matching,0.3,2.0,50,228
187,replace,8.0,O,F,CCN1CC(=F[C(7r)[rI1c,CCN1CC(=O[C(7r)[rI1c,20,replace F at position 8 with O,flow_matching,0.3,2.0,50,228
188,replace,9.0,),[,CCN1CC(=O[C(7r)[rI1c,CCN1CC(=O)C(7r)[rI1c,20,replace [ at position 9 with ),flow_matching,0.3,2.0,50,228
189,replace,10.0,N,C,CCN1CC(=O)C(7r)[rI1c,CCN1CC(=O)N(7r)[rI1c,20,replace C at position 10 with N,flow_matching,0.3,2.0,50,228
190,replace,11.0,c,(,CCN1CC(=O)N(7r)[rI1c,CCN1CC(=O)Nc7r)[rI1c,20,replace ( at position 11 with c,flow_matching,0.3,2.0,50,228
191,replace,12.0,2,7,CCN1CC(=O)Nc7r)[rI1c,CCN1CC(=O)Nc2r)[rI1c,20,replace 7 at position 12 with 2,flow_matching,0.3,2.0,50,228
192,replace,13.0,c,r,CCN1CC(=O)Nc2r)[rI1c,CCN1CC(=O)Nc2c)[rI1c,20,replace r at position 13 with c,flow_matching,0.3,2.0,50,228
193,replace,14.0,c,),CCN1CC(=O)Nc2c)[rI1c,CCN1CC(=O)Nc2cc[rI1c,20,replace ) at position 14 with c,flow_matching,0.3,2.0,50,228
194,replace,15.0,(,[,CCN1CC(=O)Nc2cc[rI1c,CCN1CC(=O)Nc2cc(rI1c,20,replace [ at position 15 with (,flow_matching,0.3,2.0,50,228
195,replace,16.0,C,r,CCN1CC(=O)Nc2cc(rI1c,CCN1CC(=O)Nc2cc(CI1c,20,replace r at position 16 with C,flow_matching,0.3,2.0,50,228
196,replace,17.0,(,I,CCN1CC(=O)Nc2cc(CI1c,CCN1CC(=O)Nc2cc(C(1c,20,replace I at position 17 with (,flow_matching,0.3,2.0,50,228
197,replace,18.0,=,1,CCN1CC(=O)Nc2cc(C(1c,CCN1CC(=O)Nc2cc(C(=c,20,replace 1 at position 18 with =,flow_matching,0.3,2.0,50,228
198,replace,19.0,O,c,CCN1CC(=O)Nc2cc(C(=c,CCN1CC(=O)Nc2cc(C(=O,20,replace c at position 19 with O,flow_matching,0.3,2.0,50,228
199,add,20.0,),,CCN1CC(=O)Nc2cc(C(=O,CCN1CC(=O)Nc2cc(C(=O),21,add ) at position 20,flow_matching,0.3,2.0,50,228
200,add,21.0,N,,CCN1CC(=O)Nc2cc(C(=O),CCN1CC(=O)Nc2cc(C(=O)N,22,add N at position 21,flow_matching,0.3,2.0,50,228
201,add,22.0,C,,CCN1CC(=O)Nc2cc(C(=O)N,CCN1CC(=O)Nc2cc(C(=O)NC,23,add C at position 22,flow_matching,0.3,2.0,50,228
202,add,23.0,3,,CCN1CC(=O)Nc2cc(C(=O)NC,CCN1CC(=O)Nc2cc(C(=O)NC3,24,add 3 at position 23,flow_matching,0.3,2.0,50,228
203,add,24.0,C,,CCN1CC(=O)Nc2cc(C(=O)NC3,CCN1CC(=O)Nc2cc(C(=O)NC3C,25,add C at position 24,flow_matching,0.3,2.0,50,228
204,add,25.0,C,,CCN1CC(=O)Nc2cc(C(=O)NC3C,CCN1CC(=O)Nc2cc(C(=O)NC3CC,26,add C at position 25,flow_matching,0.3,2.0,50,228
205,add,26.0,[,,CCN1CC(=O)Nc2cc(C(=O)NC3CC,CCN1CC(=O)Nc2cc(C(=O)NC3CC[,27,add [ at position 26,flow_matching,0.3,2.0,50,228
206,add,27.0,N,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[,CCN1CC(=O)Nc2cc(C(=O)NC3CC[N,28,add N at position 27,flow_matching,0.3,2.0,50,228
207,add,28.0,H,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[N,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH,29,add H at position 28,flow_matching,0.3,2.0,50,228
208,add,29.0,+,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+,30,add + at position 29,flow_matching,0.3,2.0,50,228
209,add,30.0,],,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+],31,add ] at position 30,flow_matching,0.3,2.0,50,228
210,add,31.0,(,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+],CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](,32,add ( at position 31,flow_matching,0.3,2.0,50,228
211,add,32.0,C,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C,33,add C at position 32,flow_matching,0.3,2.0,50,228
212,add,33.0,4,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4,34,add 4 at position 33,flow_matching,0.3,2.0,50,228
213,add,34.0,C,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4C,35,add C at position 34,flow_matching,0.3,2.0,50,228
214,add,35.0,C,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4C,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CC,36,add C at position 35,flow_matching,0.3,2.0,50,228
215,add,36.0,C,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CC,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCC,37,add C at position 36,flow_matching,0.3,2.0,50,228
216,add,37.0,C,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCC,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC,38,add C at position 37,flow_matching,0.3,2.0,50,228
217,add,38.0,4,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4,39,add 4 at position 38,flow_matching,0.3,2.0,50,228
218,add,39.0,),,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4),40,add ) at position 39,flow_matching,0.3,2.0,50,228
219,add,40.0,C,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4),CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)C,41,add C at position 40,flow_matching,0.3,2.0,50,228
220,add,41.0,C,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)C,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC,42,add C at position 41,flow_matching,0.3,2.0,50,228
221,add,42.0,3,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3,43,add 3 at position 42,flow_matching,0.3,2.0,50,228
222,add,43.0,),,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3),44,add ) at position 43,flow_matching,0.3,2.0,50,228
223,add,44.0,c,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3),CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)c,45,add c at position 44,flow_matching,0.3,2.0,50,228
224,add,45.0,c,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)c,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)cc,46,add c at position 45,flow_matching,0.3,2.0,50,228
225,add,46.0,c,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)cc,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)ccc,47,add c at position 46,flow_matching,0.3,2.0,50,228
226,add,47.0,2,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)ccc,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)ccc2,48,add 2 at position 47,flow_matching,0.3,2.0,50,228
227,add,48.0,1,,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)ccc2,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)ccc21,49,add 1 at position 48,flow_matching,0.3,2.0,50,228
228,add,49.0,"
",,CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)ccc21,"CCN1CC(=O)Nc2cc(C(=O)NC3CC[NH+](C4CCCC4)CC3)ccc21
",50,"add 
 at position 49",flow_matching,0.3,2.0,50,228
