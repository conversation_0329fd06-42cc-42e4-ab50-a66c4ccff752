step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,47,175
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,47,175
2,add,1.0,5,,O,O5,2,add 5 at position 1,flow_matching,0.3,2.0,47,175
3,add,2.0,3,,O5,O53,3,add 3 at position 2,flow_matching,0.3,2.0,47,175
4,remove,0.0,O,,O53,53,2,remove O from position 0,flow_matching,0.3,2.0,47,175
5,replace,0.0,\,5,53,\3,2,replace 5 at position 0 with \,flow_matching,0.3,2.0,47,175
6,add,1.0,/,,\3,\/3,3,add / at position 1,flow_matching,0.3,2.0,47,175
7,add,1.0,2,,\/3,\2/3,4,add 2 at position 1,flow_matching,0.3,2.0,47,175
8,add,3.0,=,,\2/3,\2/=3,5,add = at position 3,flow_matching,0.3,2.0,47,175
9,replace,0.0,O,\,\2/=3,O2/=3,5,replace \ at position 0 with O,flow_matching,0.3,2.0,47,175
10,replace,1.0,=,2,O2/=3,O=/=3,5,replace 2 at position 1 with =,flow_matching,0.3,2.0,47,175
11,replace,2.0,C,/,O=/=3,O=C=3,5,replace / at position 2 with C,flow_matching,0.3,2.0,47,175
12,replace,3.0,(,=,O=C=3,O=C(3,5,replace = at position 3 with (,flow_matching,0.3,2.0,47,175
13,remove,3.0,(,,O=C(3,O=C3,4,remove ( from position 3,flow_matching,0.3,2.0,47,175
14,add,4.0,6,,O=C3,O=C36,5,add 6 at position 4,flow_matching,0.3,2.0,47,175
15,replace,3.0,(,3,O=C36,O=C(6,5,replace 3 at position 3 with (,flow_matching,0.3,2.0,47,175
16,add,1.0,o,,O=C(6,Oo=C(6,6,add o at position 1,flow_matching,0.3,2.0,47,175
17,remove,3.0,C,,Oo=C(6,Oo=(6,5,remove C from position 3,flow_matching,0.3,2.0,47,175
18,replace,1.0,=,o,Oo=(6,O==(6,5,replace o at position 1 with =,flow_matching,0.3,2.0,47,175
19,remove,4.0,6,,O==(6,O==(,4,remove 6 from position 4,flow_matching,0.3,2.0,47,175
20,replace,2.0,C,=,O==(,O=C(,4,replace = at position 2 with C,flow_matching,0.3,2.0,47,175
21,add,2.0,@,,O=C(,O=@C(,5,add @ at position 2,flow_matching,0.3,2.0,47,175
22,replace,2.0,C,@,O=@C(,O=CC(,5,replace @ at position 2 with C,flow_matching,0.3,2.0,47,175
23,replace,1.0,#,=,O=CC(,O#CC(,5,replace = at position 1 with #,flow_matching,0.3,2.0,47,175
24,add,0.0,o,,O#CC(,oO#CC(,6,add o at position 0,flow_matching,0.3,2.0,47,175
25,add,0.0,O,,oO#CC(,OoO#CC(,7,add O at position 0,flow_matching,0.3,2.0,47,175
26,add,0.0,@,,OoO#CC(,@OoO#CC(,8,add @ at position 0,flow_matching,0.3,2.0,47,175
27,remove,5.0,C,,@OoO#CC(,@OoO#C(,7,remove C from position 5,flow_matching,0.3,2.0,47,175
28,add,5.0,#,,@OoO#C(,@OoO##C(,8,add # at position 5,flow_matching,0.3,2.0,47,175
29,replace,1.0,S,O,@OoO##C(,@SoO##C(,8,replace O at position 1 with S,flow_matching,0.3,2.0,47,175
30,add,8.0,#,,@SoO##C(,@SoO##C(#,9,add # at position 8,flow_matching,0.3,2.0,47,175
31,add,3.0,7,,@SoO##C(#,@So7O##C(#,10,add 7 at position 3,flow_matching,0.3,2.0,47,175
32,replace,0.0,O,@,@So7O##C(#,OSo7O##C(#,10,replace @ at position 0 with O,flow_matching,0.3,2.0,47,175
33,remove,4.0,O,,OSo7O##C(#,OSo7##C(#,9,remove O from position 4,flow_matching,0.3,2.0,47,175
34,remove,2.0,o,,OSo7##C(#,OS7##C(#,8,remove o from position 2,flow_matching,0.3,2.0,47,175
35,replace,0.0,H,O,OS7##C(#,HS7##C(#,8,replace O at position 0 with H,flow_matching,0.3,2.0,47,175
36,replace,0.0,O,H,HS7##C(#,OS7##C(#,8,replace H at position 0 with O,flow_matching,0.3,2.0,47,175
37,add,4.0,@,,OS7##C(#,OS7#@#C(#,9,add @ at position 4,flow_matching,0.3,2.0,47,175
38,replace,5.0,3,#,OS7#@#C(#,OS7#@3C(#,9,replace # at position 5 with 3,flow_matching,0.3,2.0,47,175
39,replace,1.0,=,S,OS7#@3C(#,O=7#@3C(#,9,replace S at position 1 with =,flow_matching,0.3,2.0,47,175
40,replace,2.0,C,7,O=7#@3C(#,O=C#@3C(#,9,replace 7 at position 2 with C,flow_matching,0.3,2.0,47,175
41,add,0.0,N,,O=C#@3C(#,NO=C#@3C(#,10,add N at position 0,flow_matching,0.3,2.0,47,175
42,replace,9.0,5,#,NO=C#@3C(#,NO=C#@3C(5,10,replace # at position 9 with 5,flow_matching,0.3,2.0,47,175
43,replace,5.0,#,@,NO=C#@3C(5,NO=C##3C(5,10,replace @ at position 5 with #,flow_matching,0.3,2.0,47,175
44,add,10.0,\,,NO=C##3C(5,NO=C##3C(5\,11,add \ at position 10,flow_matching,0.3,2.0,47,175
45,remove,6.0,3,,NO=C##3C(5\,NO=C##C(5\,10,remove 3 from position 6,flow_matching,0.3,2.0,47,175
46,remove,5.0,#,,NO=C##C(5\,NO=C#C(5\,9,remove # from position 5,flow_matching,0.3,2.0,47,175
47,add,8.0,(,,NO=C#C(5\,NO=C#C(5(\,10,add ( at position 8,flow_matching,0.3,2.0,47,175
48,replace,0.0,O,N,NO=C#C(5(\,OO=C#C(5(\,10,replace N at position 0 with O,flow_matching,0.3,2.0,47,175
49,add,10.0,-,,OO=C#C(5(\,OO=C#C(5(\-,11,add - at position 10,flow_matching,0.3,2.0,47,175
50,replace,1.0,=,O,OO=C#C(5(\-,O==C#C(5(\-,11,replace O at position 1 with =,flow_matching,0.3,2.0,47,175
51,remove,10.0,-,,O==C#C(5(\-,O==C#C(5(\,10,remove - from position 10,flow_matching,0.3,2.0,47,175
52,replace,2.0,C,=,O==C#C(5(\,O=CC#C(5(\,10,replace = at position 2 with C,flow_matching,0.3,2.0,47,175
53,add,10.0,6,,O=CC#C(5(\,O=CC#C(5(\6,11,add 6 at position 10,flow_matching,0.3,2.0,47,175
54,replace,9.0,l,\,O=CC#C(5(\6,O=CC#C(5(l6,11,replace \ at position 9 with l,flow_matching,0.3,2.0,47,175
55,replace,8.0,-,(,O=CC#C(5(l6,O=CC#C(5-l6,11,replace ( at position 8 with -,flow_matching,0.3,2.0,47,175
56,replace,3.0,(,C,O=CC#C(5-l6,O=C(#C(5-l6,11,replace C at position 3 with (,flow_matching,0.3,2.0,47,175
57,add,6.0,2,,O=C(#C(5-l6,O=C(#C2(5-l6,12,add 2 at position 6,flow_matching,0.3,2.0,47,175
58,add,9.0,C,,O=C(#C2(5-l6,O=C(#C2(5C-l6,13,add C at position 9,flow_matching,0.3,2.0,47,175
59,add,8.0,\,,O=C(#C2(5C-l6,O=C(#C2(\5C-l6,14,add \ at position 8,flow_matching,0.3,2.0,47,175
60,replace,11.0,o,-,O=C(#C2(\5C-l6,O=C(#C2(\5Col6,14,replace - at position 11 with o,flow_matching,0.3,2.0,47,175
61,remove,6.0,2,,O=C(#C2(\5Col6,O=C(#C(\5Col6,13,remove 2 from position 6,flow_matching,0.3,2.0,47,175
62,remove,7.0,\,,O=C(#C(\5Col6,O=C(#C(5Col6,12,remove \ from position 7,flow_matching,0.3,2.0,47,175
63,replace,4.0,N,#,O=C(#C(5Col6,O=C(NC(5Col6,12,replace # at position 4 with N,flow_matching,0.3,2.0,47,175
64,replace,2.0,5,C,O=C(NC(5Col6,O=5(NC(5Col6,12,replace C at position 2 with 5,flow_matching,0.3,2.0,47,175
65,remove,8.0,C,,O=5(NC(5Col6,O=5(NC(5ol6,11,remove C from position 8,flow_matching,0.3,2.0,47,175
66,replace,9.0,/,l,O=5(NC(5ol6,O=5(NC(5o/6,11,replace l at position 9 with /,flow_matching,0.3,2.0,47,175
67,replace,2.0,C,5,O=5(NC(5o/6,O=C(NC(5o/6,11,replace 5 at position 2 with C,flow_matching,0.3,2.0,47,175
68,add,8.0,3,,O=C(NC(5o/6,O=C(NC(53o/6,12,add 3 at position 8,flow_matching,0.3,2.0,47,175
69,replace,6.0,[,(,O=C(NC(53o/6,O=C(NC[53o/6,12,replace ( at position 6 with [,flow_matching,0.3,2.0,47,175
70,remove,8.0,3,,O=C(NC[53o/6,O=C(NC[5o/6,11,remove 3 from position 8,flow_matching,0.3,2.0,47,175
71,remove,8.0,o,,O=C(NC[5o/6,O=C(NC[5/6,10,remove o from position 8,flow_matching,0.3,2.0,47,175
72,add,5.0,1,,O=C(NC[5/6,O=C(N1C[5/6,11,add 1 at position 5,flow_matching,0.3,2.0,47,175
73,add,10.0,n,,O=C(N1C[5/6,O=C(N1C[5/n6,12,add n at position 10,flow_matching,0.3,2.0,47,175
74,add,5.0,s,,O=C(N1C[5/n6,O=C(Ns1C[5/n6,13,add s at position 5,flow_matching,0.3,2.0,47,175
75,replace,5.0,C,s,O=C(Ns1C[5/n6,O=C(NC1C[5/n6,13,replace s at position 5 with C,flow_matching,0.3,2.0,47,175
76,replace,6.0,[,1,O=C(NC1C[5/n6,O=C(NC[C[5/n6,13,replace 1 at position 6 with [,flow_matching,0.3,2.0,47,175
77,replace,11.0,l,n,O=C(NC[C[5/n6,O=C(NC[C[5/l6,13,replace n at position 11 with l,flow_matching,0.3,2.0,47,175
78,remove,6.0,[,,O=C(NC[C[5/l6,O=C(NCC[5/l6,12,remove [ from position 6,flow_matching,0.3,2.0,47,175
79,replace,6.0,[,C,O=C(NCC[5/l6,O=C(NC[[5/l6,12,replace C at position 6 with [,flow_matching,0.3,2.0,47,175
80,remove,6.0,[,,O=C(NC[[5/l6,O=C(NC[5/l6,11,remove [ from position 6,flow_matching,0.3,2.0,47,175
81,add,11.0,r,,O=C(NC[5/l6,O=C(NC[5/l6r,12,add r at position 11,flow_matching,0.3,2.0,47,175
82,remove,8.0,/,,O=C(NC[5/l6r,O=C(NC[5l6r,11,remove / from position 8,flow_matching,0.3,2.0,47,175
83,replace,7.0,C,5,O=C(NC[5l6r,O=C(NC[Cl6r,11,replace 5 at position 7 with C,flow_matching,0.3,2.0,47,175
84,add,1.0,(,,O=C(NC[Cl6r,O(=C(NC[Cl6r,12,add ( at position 1,flow_matching,0.3,2.0,47,175
85,replace,1.0,=,(,O(=C(NC[Cl6r,O==C(NC[Cl6r,12,replace ( at position 1 with =,flow_matching,0.3,2.0,47,175
86,remove,7.0,[,,O==C(NC[Cl6r,O==C(NCCl6r,11,remove [ from position 7,flow_matching,0.3,2.0,47,175
87,add,2.0,F,,O==C(NCCl6r,O=F=C(NCCl6r,12,add F at position 2,flow_matching,0.3,2.0,47,175
88,remove,11.0,r,,O=F=C(NCCl6r,O=F=C(NCCl6,11,remove r from position 11,flow_matching,0.3,2.0,47,175
89,replace,2.0,C,F,O=F=C(NCCl6,O=C=C(NCCl6,11,replace F at position 2 with C,flow_matching,0.3,2.0,47,175
90,add,9.0,3,,O=C=C(NCCl6,O=C=C(NCC3l6,12,add 3 at position 9,flow_matching,0.3,2.0,47,175
91,replace,3.0,(,=,O=C=C(NCC3l6,O=C(C(NCC3l6,12,replace = at position 3 with (,flow_matching,0.3,2.0,47,175
92,replace,4.0,N,C,O=C(C(NCC3l6,O=C(N(NCC3l6,12,replace C at position 4 with N,flow_matching,0.3,2.0,47,175
93,remove,10.0,l,,O=C(N(NCC3l6,O=C(N(NCC36,11,remove l from position 10,flow_matching,0.3,2.0,47,175
94,replace,5.0,C,(,O=C(N(NCC36,O=C(NCNCC36,11,replace ( at position 5 with C,flow_matching,0.3,2.0,47,175
95,replace,9.0,5,3,O=C(NCNCC36,O=C(NCNCC56,11,replace 3 at position 9 with 5,flow_matching,0.3,2.0,47,175
96,remove,4.0,N,,O=C(NCNCC56,O=C(CNCC56,10,remove N from position 4,flow_matching,0.3,2.0,47,175
97,remove,2.0,C,,O=C(CNCC56,O=(CNCC56,9,remove C from position 2,flow_matching,0.3,2.0,47,175
98,replace,6.0,H,C,O=(CNCC56,O=(CNCH56,9,replace C at position 6 with H,flow_matching,0.3,2.0,47,175
99,replace,2.0,C,(,O=(CNCH56,O=CCNCH56,9,replace ( at position 2 with C,flow_matching,0.3,2.0,47,175
100,remove,7.0,5,,O=CCNCH56,O=CCNCH6,8,remove 5 from position 7,flow_matching,0.3,2.0,47,175
101,replace,3.0,#,C,O=CCNCH6,O=C#NCH6,8,replace C at position 3 with #,flow_matching,0.3,2.0,47,175
102,replace,1.0,C,=,O=C#NCH6,OCC#NCH6,8,replace = at position 1 with C,flow_matching,0.3,2.0,47,175
103,replace,1.0,=,C,OCC#NCH6,O=C#NCH6,8,replace C at position 1 with =,flow_matching,0.3,2.0,47,175
104,replace,3.0,(,#,O=C#NCH6,O=C(NCH6,8,replace # at position 3 with (,flow_matching,0.3,2.0,47,175
105,replace,6.0,[,H,O=C(NCH6,O=C(NC[6,8,replace H at position 6 with [,flow_matching,0.3,2.0,47,175
106,add,3.0,B,,O=C(NC[6,O=CB(NC[6,9,add B at position 3,flow_matching,0.3,2.0,47,175
107,replace,3.0,(,B,O=CB(NC[6,O=C((NC[6,9,replace B at position 3 with (,flow_matching,0.3,2.0,47,175
108,replace,4.0,N,(,O=C((NC[6,O=C(NNC[6,9,replace ( at position 4 with N,flow_matching,0.3,2.0,47,175
109,remove,5.0,N,,O=C(NNC[6,O=C(NC[6,8,remove N from position 5,flow_matching,0.3,2.0,47,175
110,remove,7.0,6,,O=C(NC[6,O=C(NC[,7,remove 6 from position 7,flow_matching,0.3,2.0,47,175
111,add,7.0,C,,O=C(NC[,O=C(NC[C,8,add C at position 7,flow_matching,0.3,2.0,47,175
112,remove,4.0,N,,O=C(NC[C,O=C(C[C,7,remove N from position 4,flow_matching,0.3,2.0,47,175
113,remove,0.0,O,,O=C(C[C,=C(C[C,6,remove O from position 0,flow_matching,0.3,2.0,47,175
114,replace,0.0,O,=,=C(C[C,OC(C[C,6,replace = at position 0 with O,flow_matching,0.3,2.0,47,175
115,remove,4.0,[,,OC(C[C,OC(CC,5,remove [ from position 4,flow_matching,0.3,2.0,47,175
116,replace,3.0,n,C,OC(CC,OC(nC,5,replace C at position 3 with n,flow_matching,0.3,2.0,47,175
117,add,2.0,@,,OC(nC,OC@(nC,6,add @ at position 2,flow_matching,0.3,2.0,47,175
118,replace,1.0,=,C,OC@(nC,O=@(nC,6,replace C at position 1 with =,flow_matching,0.3,2.0,47,175
119,replace,4.0,1,n,O=@(nC,O=@(1C,6,replace n at position 4 with 1,flow_matching,0.3,2.0,47,175
120,add,1.0,I,,O=@(1C,OI=@(1C,7,add I at position 1,flow_matching,0.3,2.0,47,175
121,remove,0.0,O,,OI=@(1C,I=@(1C,6,remove O from position 0,flow_matching,0.3,2.0,47,175
122,replace,2.0,F,@,I=@(1C,I=F(1C,6,replace @ at position 2 with F,flow_matching,0.3,2.0,47,175
123,replace,0.0,O,I,I=F(1C,O=F(1C,6,replace I at position 0 with O,flow_matching,0.3,2.0,47,175
124,remove,4.0,1,,O=F(1C,O=F(C,5,remove 1 from position 4,flow_matching,0.3,2.0,47,175
125,replace,2.0,C,F,O=F(C,O=C(C,5,replace F at position 2 with C,flow_matching,0.3,2.0,47,175
126,add,0.0,o,,O=C(C,oO=C(C,6,add o at position 0,flow_matching,0.3,2.0,47,175
127,add,6.0,=,,oO=C(C,oO=C(C=,7,add = at position 6,flow_matching,0.3,2.0,47,175
128,add,1.0,1,,oO=C(C=,o1O=C(C=,8,add 1 at position 1,flow_matching,0.3,2.0,47,175
129,replace,0.0,O,o,o1O=C(C=,O1O=C(C=,8,replace o at position 0 with O,flow_matching,0.3,2.0,47,175
130,replace,1.0,=,1,O1O=C(C=,O=O=C(C=,8,replace 1 at position 1 with =,flow_matching,0.3,2.0,47,175
131,replace,2.0,C,O,O=O=C(C=,O=C=C(C=,8,replace O at position 2 with C,flow_matching,0.3,2.0,47,175
132,replace,3.0,(,=,O=C=C(C=,O=C(C(C=,8,replace = at position 3 with (,flow_matching,0.3,2.0,47,175
133,replace,4.0,N,C,O=C(C(C=,O=C(N(C=,8,replace C at position 4 with N,flow_matching,0.3,2.0,47,175
134,replace,5.0,C,(,O=C(N(C=,O=C(NCC=,8,replace ( at position 5 with C,flow_matching,0.3,2.0,47,175
135,replace,6.0,[,C,O=C(NCC=,O=C(NC[=,8,replace C at position 6 with [,flow_matching,0.3,2.0,47,175
136,replace,7.0,C,=,O=C(NC[=,O=C(NC[C,8,replace = at position 7 with C,flow_matching,0.3,2.0,47,175
137,add,8.0,@,,O=C(NC[C,O=C(NC[C@,9,add @ at position 8,flow_matching,0.3,2.0,47,175
138,add,9.0,@,,O=C(NC[C@,O=C(NC[C@@,10,add @ at position 9,flow_matching,0.3,2.0,47,175
139,add,10.0,H,,O=C(NC[C@@,O=C(NC[C@@H,11,add H at position 10,flow_matching,0.3,2.0,47,175
140,add,11.0,],,O=C(NC[C@@H,O=C(NC[C@@H],12,add ] at position 11,flow_matching,0.3,2.0,47,175
141,add,12.0,1,,O=C(NC[C@@H],O=C(NC[C@@H]1,13,add 1 at position 12,flow_matching,0.3,2.0,47,175
142,add,13.0,C,,O=C(NC[C@@H]1,O=C(NC[C@@H]1C,14,add C at position 13,flow_matching,0.3,2.0,47,175
143,add,14.0,C,,O=C(NC[C@@H]1C,O=C(NC[C@@H]1CC,15,add C at position 14,flow_matching,0.3,2.0,47,175
144,add,15.0,C,,O=C(NC[C@@H]1CC,O=C(NC[C@@H]1CCC,16,add C at position 15,flow_matching,0.3,2.0,47,175
145,add,16.0,[,,O=C(NC[C@@H]1CCC,O=C(NC[C@@H]1CCC[,17,add [ at position 16,flow_matching,0.3,2.0,47,175
146,add,17.0,N,,O=C(NC[C@@H]1CCC[,O=C(NC[C@@H]1CCC[N,18,add N at position 17,flow_matching,0.3,2.0,47,175
147,add,18.0,H,,O=C(NC[C@@H]1CCC[N,O=C(NC[C@@H]1CCC[NH,19,add H at position 18,flow_matching,0.3,2.0,47,175
148,add,19.0,+,,O=C(NC[C@@H]1CCC[NH,O=C(NC[C@@H]1CCC[NH+,20,add + at position 19,flow_matching,0.3,2.0,47,175
149,add,20.0,],,O=C(NC[C@@H]1CCC[NH+,O=C(NC[C@@H]1CCC[NH+],21,add ] at position 20,flow_matching,0.3,2.0,47,175
150,add,21.0,(,,O=C(NC[C@@H]1CCC[NH+],O=C(NC[C@@H]1CCC[NH+](,22,add ( at position 21,flow_matching,0.3,2.0,47,175
151,add,22.0,C,,O=C(NC[C@@H]1CCC[NH+](,O=C(NC[C@@H]1CCC[NH+](C,23,add C at position 22,flow_matching,0.3,2.0,47,175
152,add,23.0,c,,O=C(NC[C@@H]1CCC[NH+](C,O=C(NC[C@@H]1CCC[NH+](Cc,24,add c at position 23,flow_matching,0.3,2.0,47,175
153,add,24.0,2,,O=C(NC[C@@H]1CCC[NH+](Cc,O=C(NC[C@@H]1CCC[NH+](Cc2,25,add 2 at position 24,flow_matching,0.3,2.0,47,175
154,add,25.0,c,,O=C(NC[C@@H]1CCC[NH+](Cc2,O=C(NC[C@@H]1CCC[NH+](Cc2c,26,add c at position 25,flow_matching,0.3,2.0,47,175
155,add,26.0,c,,O=C(NC[C@@H]1CCC[NH+](Cc2c,O=C(NC[C@@H]1CCC[NH+](Cc2cc,27,add c at position 26,flow_matching,0.3,2.0,47,175
156,add,27.0,c,,O=C(NC[C@@H]1CCC[NH+](Cc2cc,O=C(NC[C@@H]1CCC[NH+](Cc2ccc,28,add c at position 27,flow_matching,0.3,2.0,47,175
157,add,28.0,c,,O=C(NC[C@@H]1CCC[NH+](Cc2ccc,O=C(NC[C@@H]1CCC[NH+](Cc2cccc,29,add c at position 28,flow_matching,0.3,2.0,47,175
158,add,29.0,c,,O=C(NC[C@@H]1CCC[NH+](Cc2cccc,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc,30,add c at position 29,flow_matching,0.3,2.0,47,175
159,add,30.0,2,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2,31,add 2 at position 30,flow_matching,0.3,2.0,47,175
160,add,31.0,F,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F,32,add F at position 31,flow_matching,0.3,2.0,47,175
161,add,32.0,),,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F),33,add ) at position 32,flow_matching,0.3,2.0,47,175
162,add,33.0,C,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F),O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C,34,add C at position 33,flow_matching,0.3,2.0,47,175
163,add,34.0,1,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1,35,add 1 at position 34,flow_matching,0.3,2.0,47,175
164,add,35.0,),,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1),36,add ) at position 35,flow_matching,0.3,2.0,47,175
165,add,36.0,c,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1),O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c,37,add c at position 36,flow_matching,0.3,2.0,47,175
166,add,37.0,1,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1,38,add 1 at position 37,flow_matching,0.3,2.0,47,175
167,add,38.0,n,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1n,39,add n at position 38,flow_matching,0.3,2.0,47,175
168,add,39.0,c,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1n,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc,40,add c at position 39,flow_matching,0.3,2.0,47,175
169,add,40.0,[,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[,41,add [ at position 40,flow_matching,0.3,2.0,47,175
170,add,41.0,n,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[n,42,add n at position 41,flow_matching,0.3,2.0,47,175
171,add,42.0,H,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[n,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[nH,43,add H at position 42,flow_matching,0.3,2.0,47,175
172,add,43.0,],,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[nH,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[nH],44,add ] at position 43,flow_matching,0.3,2.0,47,175
173,add,44.0,n,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[nH],O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[nH]n,45,add n at position 44,flow_matching,0.3,2.0,47,175
174,add,45.0,1,,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[nH]n,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[nH]n1,46,add 1 at position 45,flow_matching,0.3,2.0,47,175
175,add,46.0,"
",,O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[nH]n1,"O=C(NC[C@@H]1CCC[NH+](Cc2ccccc2F)C1)c1nc[nH]n1
",47,"add 
 at position 46",flow_matching,0.3,2.0,47,175
