step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,33,162
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,33,162
2,add,1.0,1,,C,C1,2,add 1 at position 1,flow_matching,0.3,2.0,33,162
3,add,0.0,(,,C1,(C1,3,add ( at position 0,flow_matching,0.3,2.0,33,162
4,replace,0.0,4,(,(C1,4C1,3,replace ( at position 0 with 4,flow_matching,0.3,2.0,33,162
5,remove,0.0,4,,4C1,C1,2,remove 4 from position 0,flow_matching,0.3,2.0,33,162
6,replace,1.0,O,1,C1,CO,2,replace 1 at position 1 with O,flow_matching,0.3,2.0,33,162
7,add,2.0,C,,CO,COC,3,add <PERSON> at position 2,flow_matching,0.3,2.0,33,162
8,add,3.0,1,,COC,COC1,4,add 1 at position 3,flow_matching,0.3,2.0,33,162
9,add,4.0,C,,COC1,COC1C,5,add C at position 4,flow_matching,0.3,2.0,33,162
10,add,4.0,s,,COC1C,COC1sC,6,add s at position 4,flow_matching,0.3,2.0,33,162
11,replace,4.0,C,s,COC1sC,COC1CC,6,replace s at position 4 with C,flow_matching,0.3,2.0,33,162
12,remove,4.0,C,,COC1CC,COC1C,5,remove C from position 4,flow_matching,0.3,2.0,33,162
13,remove,1.0,O,,COC1C,CC1C,4,remove O from position 1,flow_matching,0.3,2.0,33,162
14,replace,1.0,O,C,CC1C,CO1C,4,replace C at position 1 with O,flow_matching,0.3,2.0,33,162
15,replace,3.0,/,C,CO1C,CO1/,4,replace C at position 3 with /,flow_matching,0.3,2.0,33,162
16,replace,2.0,C,1,CO1/,COC/,4,replace 1 at position 2 with C,flow_matching,0.3,2.0,33,162
17,add,4.0,4,,COC/,COC/4,5,add 4 at position 4,flow_matching,0.3,2.0,33,162
18,remove,2.0,C,,COC/4,CO/4,4,remove C from position 2,flow_matching,0.3,2.0,33,162
19,add,0.0,=,,CO/4,=CO/4,5,add = at position 0,flow_matching,0.3,2.0,33,162
20,add,4.0,c,,=CO/4,=CO/c4,6,add c at position 4,flow_matching,0.3,2.0,33,162
21,replace,2.0,C,O,=CO/c4,=CC/c4,6,replace O at position 2 with C,flow_matching,0.3,2.0,33,162
22,replace,1.0,=,C,=CC/c4,==C/c4,6,replace C at position 1 with =,flow_matching,0.3,2.0,33,162
23,remove,1.0,=,,==C/c4,=C/c4,5,remove = from position 1,flow_matching,0.3,2.0,33,162
24,remove,3.0,c,,=C/c4,=C/4,4,remove c from position 3,flow_matching,0.3,2.0,33,162
25,replace,1.0,-,C,=C/4,=-/4,4,replace C at position 1 with -,flow_matching,0.3,2.0,33,162
26,remove,0.0,=,,=-/4,-/4,3,remove = from position 0,flow_matching,0.3,2.0,33,162
27,replace,0.0,C,-,-/4,C/4,3,replace - at position 0 with C,flow_matching,0.3,2.0,33,162
28,replace,1.0,O,/,C/4,CO4,3,replace / at position 1 with O,flow_matching,0.3,2.0,33,162
29,replace,2.0,C,4,CO4,COC,3,replace 4 at position 2 with C,flow_matching,0.3,2.0,33,162
30,add,3.0,F,,COC,COCF,4,add F at position 3,flow_matching,0.3,2.0,33,162
31,replace,2.0,+,C,COCF,CO+F,4,replace C at position 2 with +,flow_matching,0.3,2.0,33,162
32,remove,0.0,C,,CO+F,O+F,3,remove C from position 0,flow_matching,0.3,2.0,33,162
33,replace,0.0,C,O,O+F,C+F,3,replace O at position 0 with C,flow_matching,0.3,2.0,33,162
34,add,2.0,S,,C+F,C+SF,4,add S at position 2,flow_matching,0.3,2.0,33,162
35,remove,3.0,F,,C+SF,C+S,3,remove F from position 3,flow_matching,0.3,2.0,33,162
36,add,2.0,I,,C+S,C+IS,4,add I at position 2,flow_matching,0.3,2.0,33,162
37,replace,1.0,O,+,C+IS,COIS,4,replace + at position 1 with O,flow_matching,0.3,2.0,33,162
38,replace,2.0,C,I,COIS,COCS,4,replace I at position 2 with C,flow_matching,0.3,2.0,33,162
39,add,0.0,[,,COCS,[COCS,5,add [ at position 0,flow_matching,0.3,2.0,33,162
40,remove,2.0,O,,[COCS,[CCS,4,remove O from position 2,flow_matching,0.3,2.0,33,162
41,replace,0.0,),[,[CCS,)CCS,4,replace [ at position 0 with ),flow_matching,0.3,2.0,33,162
42,add,0.0,I,,)CCS,I)CCS,5,add I at position 0,flow_matching,0.3,2.0,33,162
43,add,1.0,\,,I)CCS,I\)CCS,6,add \ at position 1,flow_matching,0.3,2.0,33,162
44,replace,0.0,C,I,I\)CCS,C\)CCS,6,replace I at position 0 with C,flow_matching,0.3,2.0,33,162
45,add,5.0,(,,C\)CCS,C\)CC(S,7,add ( at position 5,flow_matching,0.3,2.0,33,162
46,add,4.0,\,,C\)CC(S,C\)C\C(S,8,add \ at position 4,flow_matching,0.3,2.0,33,162
47,replace,2.0,\,),C\)C\C(S,C\\C\C(S,8,replace ) at position 2 with \,flow_matching,0.3,2.0,33,162
48,replace,6.0,F,(,C\\C\C(S,C\\C\CFS,8,replace ( at position 6 with F,flow_matching,0.3,2.0,33,162
49,replace,3.0,B,C,C\\C\CFS,C\\B\CFS,8,replace C at position 3 with B,flow_matching,0.3,2.0,33,162
50,replace,0.0,1,C,C\\B\CFS,1\\B\CFS,8,replace C at position 0 with 1,flow_matching,0.3,2.0,33,162
51,replace,0.0,C,1,1\\B\CFS,C\\B\CFS,8,replace 1 at position 0 with C,flow_matching,0.3,2.0,33,162
52,add,5.0,F,,C\\B\CFS,C\\B\FCFS,9,add F at position 5,flow_matching,0.3,2.0,33,162
53,replace,5.0,S,F,C\\B\FCFS,C\\B\SCFS,9,replace F at position 5 with S,flow_matching,0.3,2.0,33,162
54,remove,0.0,C,,C\\B\SCFS,\\B\SCFS,8,remove C from position 0,flow_matching,0.3,2.0,33,162
55,replace,0.0,C,\,\\B\SCFS,C\B\SCFS,8,replace \ at position 0 with C,flow_matching,0.3,2.0,33,162
56,replace,1.0,O,\,C\B\SCFS,COB\SCFS,8,replace \ at position 1 with O,flow_matching,0.3,2.0,33,162
57,replace,2.0,C,B,COB\SCFS,COC\SCFS,8,replace B at position 2 with C,flow_matching,0.3,2.0,33,162
58,remove,2.0,C,,COC\SCFS,CO\SCFS,7,remove C from position 2,flow_matching,0.3,2.0,33,162
59,replace,2.0,C,\,CO\SCFS,COCSCFS,7,replace \ at position 2 with C,flow_matching,0.3,2.0,33,162
60,replace,4.0,7,C,COCSCFS,COCS7FS,7,replace C at position 4 with 7,flow_matching,0.3,2.0,33,162
61,remove,2.0,C,,COCS7FS,COS7FS,6,remove C from position 2,flow_matching,0.3,2.0,33,162
62,remove,5.0,S,,COS7FS,COS7F,5,remove S from position 5,flow_matching,0.3,2.0,33,162
63,replace,3.0,\,7,COS7F,COS\F,5,replace 7 at position 3 with \,flow_matching,0.3,2.0,33,162
64,add,4.0,@,,COS\F,COS\@F,6,add @ at position 4,flow_matching,0.3,2.0,33,162
65,remove,0.0,C,,COS\@F,OS\@F,5,remove C from position 0,flow_matching,0.3,2.0,33,162
66,add,5.0,n,,OS\@F,OS\@Fn,6,add n at position 5,flow_matching,0.3,2.0,33,162
67,replace,0.0,C,O,OS\@Fn,CS\@Fn,6,replace O at position 0 with C,flow_matching,0.3,2.0,33,162
68,add,0.0,/,,CS\@Fn,/CS\@Fn,7,add / at position 0,flow_matching,0.3,2.0,33,162
69,replace,2.0,(,S,/CS\@Fn,/C(\@Fn,7,replace S at position 2 with (,flow_matching,0.3,2.0,33,162
70,add,6.0,C,,/C(\@Fn,/C(\@FCn,8,add C at position 6,flow_matching,0.3,2.0,33,162
71,replace,0.0,3,/,/C(\@FCn,3C(\@FCn,8,replace / at position 0 with 3,flow_matching,0.3,2.0,33,162
72,replace,0.0,C,3,3C(\@FCn,CC(\@FCn,8,replace 3 at position 0 with C,flow_matching,0.3,2.0,33,162
73,add,4.0,7,,CC(\@FCn,CC(\7@FCn,9,add 7 at position 4,flow_matching,0.3,2.0,33,162
74,replace,7.0,(,C,CC(\7@FCn,CC(\7@F(n,9,replace C at position 7 with (,flow_matching,0.3,2.0,33,162
75,replace,1.0,O,C,CC(\7@F(n,CO(\7@F(n,9,replace C at position 1 with O,flow_matching,0.3,2.0,33,162
76,replace,5.0,[,@,CO(\7@F(n,CO(\7[F(n,9,replace @ at position 5 with [,flow_matching,0.3,2.0,33,162
77,add,1.0,I,,CO(\7[F(n,CIO(\7[F(n,10,add I at position 1,flow_matching,0.3,2.0,33,162
78,remove,5.0,7,,CIO(\7[F(n,CIO(\[F(n,9,remove 7 from position 5,flow_matching,0.3,2.0,33,162
79,replace,1.0,O,I,CIO(\[F(n,COO(\[F(n,9,replace I at position 1 with O,flow_matching,0.3,2.0,33,162
80,add,9.0,O,,COO(\[F(n,COO(\[F(nO,10,add O at position 9,flow_matching,0.3,2.0,33,162
81,replace,2.0,C,O,COO(\[F(nO,COC(\[F(nO,10,replace O at position 2 with C,flow_matching,0.3,2.0,33,162
82,replace,7.0,l,(,COC(\[F(nO,COC(\[FlnO,10,replace ( at position 7 with l,flow_matching,0.3,2.0,33,162
83,replace,6.0,-,F,COC(\[FlnO,COC(\[-lnO,10,replace F at position 6 with -,flow_matching,0.3,2.0,33,162
84,add,0.0,#,,COC(\[-lnO,#COC(\[-lnO,11,add # at position 0,flow_matching,0.3,2.0,33,162
85,replace,0.0,C,#,#COC(\[-lnO,CCOC(\[-lnO,11,replace # at position 0 with C,flow_matching,0.3,2.0,33,162
86,replace,1.0,O,C,CCOC(\[-lnO,COOC(\[-lnO,11,replace C at position 1 with O,flow_matching,0.3,2.0,33,162
87,add,4.0,o,,COOC(\[-lnO,COOCo(\[-lnO,12,add o at position 4,flow_matching,0.3,2.0,33,162
88,replace,11.0,],O,COOCo(\[-lnO,COOCo(\[-ln],12,replace O at position 11 with ],flow_matching,0.3,2.0,33,162
89,add,5.0,2,,COOCo(\[-ln],COOCo2(\[-ln],13,add 2 at position 5,flow_matching,0.3,2.0,33,162
90,replace,8.0,2,[,COOCo2(\[-ln],COOCo2(\2-ln],13,replace [ at position 8 with 2,flow_matching,0.3,2.0,33,162
91,remove,11.0,n,,COOCo2(\2-ln],COOCo2(\2-l],12,remove n from position 11,flow_matching,0.3,2.0,33,162
92,remove,1.0,O,,COOCo2(\2-l],COCo2(\2-l],11,remove O from position 1,flow_matching,0.3,2.0,33,162
93,remove,0.0,C,,COCo2(\2-l],OCo2(\2-l],10,remove C from position 0,flow_matching,0.3,2.0,33,162
94,replace,0.0,C,O,OCo2(\2-l],CCo2(\2-l],10,replace O at position 0 with C,flow_matching,0.3,2.0,33,162
95,remove,1.0,C,,CCo2(\2-l],Co2(\2-l],9,remove C from position 1,flow_matching,0.3,2.0,33,162
96,replace,1.0,O,o,Co2(\2-l],CO2(\2-l],9,replace o at position 1 with O,flow_matching,0.3,2.0,33,162
97,add,5.0,n,,CO2(\2-l],CO2(\n2-l],10,add n at position 5,flow_matching,0.3,2.0,33,162
98,add,3.0,),,CO2(\n2-l],CO2)(\n2-l],11,add ) at position 3,flow_matching,0.3,2.0,33,162
99,replace,9.0,4,l,CO2)(\n2-l],CO2)(\n2-4],11,replace l at position 9 with 4,flow_matching,0.3,2.0,33,162
100,add,4.0,6,,CO2)(\n2-4],CO2)6(\n2-4],12,add 6 at position 4,flow_matching,0.3,2.0,33,162
101,replace,3.0,6,),CO2)6(\n2-4],CO266(\n2-4],12,replace ) at position 3 with 6,flow_matching,0.3,2.0,33,162
102,replace,8.0,1,2,CO266(\n2-4],CO266(\n1-4],12,replace 2 at position 8 with 1,flow_matching,0.3,2.0,33,162
103,remove,10.0,4,,CO266(\n1-4],CO266(\n1-],11,remove 4 from position 10,flow_matching,0.3,2.0,33,162
104,remove,5.0,(,,CO266(\n1-],CO266\n1-],10,remove ( from position 5,flow_matching,0.3,2.0,33,162
105,remove,9.0,],,CO266\n1-],CO266\n1-,9,remove ] from position 9,flow_matching,0.3,2.0,33,162
106,add,3.0,+,,CO266\n1-,CO2+66\n1-,10,add + at position 3,flow_matching,0.3,2.0,33,162
107,remove,5.0,6,,CO2+66\n1-,CO2+6\n1-,9,remove 6 from position 5,flow_matching,0.3,2.0,33,162
108,remove,7.0,1,,CO2+6\n1-,CO2+6\n-,8,remove 1 from position 7,flow_matching,0.3,2.0,33,162
109,replace,2.0,C,2,CO2+6\n-,COC+6\n-,8,replace 2 at position 2 with C,flow_matching,0.3,2.0,33,162
110,replace,3.0,1,+,COC+6\n-,COC16\n-,8,replace + at position 3 with 1,flow_matching,0.3,2.0,33,162
111,add,1.0,o,,COC16\n-,CoOC16\n-,9,add o at position 1,flow_matching,0.3,2.0,33,162
112,remove,8.0,-,,CoOC16\n-,CoOC16\n,8,remove - from position 8,flow_matching,0.3,2.0,33,162
113,add,0.0,s,,CoOC16\n,sCoOC16\n,9,add s at position 0,flow_matching,0.3,2.0,33,162
114,add,6.0,2,,sCoOC16\n,sCoOC126\n,10,add 2 at position 6,flow_matching,0.3,2.0,33,162
115,replace,0.0,C,s,sCoOC126\n,CCoOC126\n,10,replace s at position 0 with C,flow_matching,0.3,2.0,33,162
116,add,6.0,/,,CCoOC126\n,CCoOC1/26\n,11,add / at position 6,flow_matching,0.3,2.0,33,162
117,replace,8.0,2,6,CCoOC1/26\n,CCoOC1/22\n,11,replace 6 at position 8 with 2,flow_matching,0.3,2.0,33,162
118,replace,1.0,O,C,CCoOC1/22\n,COoOC1/22\n,11,replace C at position 1 with O,flow_matching,0.3,2.0,33,162
119,add,4.0,6,,COoOC1/22\n,COoO6C1/22\n,12,add 6 at position 4,flow_matching,0.3,2.0,33,162
120,add,7.0,=,,COoO6C1/22\n,COoO6C1=/22\n,13,add = at position 7,flow_matching,0.3,2.0,33,162
121,replace,10.0,F,2,COoO6C1=/22\n,COoO6C1=/2F\n,13,replace 2 at position 10 with F,flow_matching,0.3,2.0,33,162
122,replace,12.0,5,n,COoO6C1=/2F\n,COoO6C1=/2F\5,13,replace n at position 12 with 5,flow_matching,0.3,2.0,33,162
123,add,7.0,r,,COoO6C1=/2F\5,COoO6C1r=/2F\5,14,add r at position 7,flow_matching,0.3,2.0,33,162
124,remove,13.0,5,,COoO6C1r=/2F\5,COoO6C1r=/2F\,13,remove 5 from position 13,flow_matching,0.3,2.0,33,162
125,remove,0.0,C,,COoO6C1r=/2F\,OoO6C1r=/2F\,12,remove C from position 0,flow_matching,0.3,2.0,33,162
126,replace,0.0,C,O,OoO6C1r=/2F\,CoO6C1r=/2F\,12,replace O at position 0 with C,flow_matching,0.3,2.0,33,162
127,replace,10.0,-,F,CoO6C1r=/2F\,CoO6C1r=/2-\,12,replace F at position 10 with -,flow_matching,0.3,2.0,33,162
128,add,1.0,5,,CoO6C1r=/2-\,C5oO6C1r=/2-\,13,add 5 at position 1,flow_matching,0.3,2.0,33,162
129,replace,1.0,O,5,C5oO6C1r=/2-\,COoO6C1r=/2-\,13,replace 5 at position 1 with O,flow_matching,0.3,2.0,33,162
130,replace,0.0,H,C,COoO6C1r=/2-\,HOoO6C1r=/2-\,13,replace C at position 0 with H,flow_matching,0.3,2.0,33,162
131,replace,6.0,C,1,HOoO6C1r=/2-\,HOoO6CCr=/2-\,13,replace 1 at position 6 with C,flow_matching,0.3,2.0,33,162
132,replace,0.0,C,H,HOoO6CCr=/2-\,COoO6CCr=/2-\,13,replace H at position 0 with C,flow_matching,0.3,2.0,33,162
133,replace,2.0,C,o,COoO6CCr=/2-\,COCO6CCr=/2-\,13,replace o at position 2 with C,flow_matching,0.3,2.0,33,162
134,replace,3.0,1,O,COCO6CCr=/2-\,COC16CCr=/2-\,13,replace O at position 3 with 1,flow_matching,0.3,2.0,33,162
135,replace,4.0,C,6,COC16CCr=/2-\,COC1CCCr=/2-\,13,replace 6 at position 4 with C,flow_matching,0.3,2.0,33,162
136,replace,6.0,[,C,COC1CCCr=/2-\,COC1CC[r=/2-\,13,replace C at position 6 with [,flow_matching,0.3,2.0,33,162
137,replace,7.0,N,r,COC1CC[r=/2-\,COC1CC[N=/2-\,13,replace r at position 7 with N,flow_matching,0.3,2.0,33,162
138,replace,8.0,H,=,COC1CC[N=/2-\,COC1CC[NH/2-\,13,replace = at position 8 with H,flow_matching,0.3,2.0,33,162
139,replace,9.0,+,/,COC1CC[NH/2-\,COC1CC[NH+2-\,13,replace / at position 9 with +,flow_matching,0.3,2.0,33,162
140,replace,10.0,],2,COC1CC[NH+2-\,COC1CC[NH+]-\,13,replace 2 at position 10 with ],flow_matching,0.3,2.0,33,162
141,replace,11.0,(,-,COC1CC[NH+]-\,COC1CC[NH+](\,13,replace - at position 11 with (,flow_matching,0.3,2.0,33,162
142,replace,12.0,C,\,COC1CC[NH+](\,COC1CC[NH+](C,13,replace \ at position 12 with C,flow_matching,0.3,2.0,33,162
143,add,13.0,C,,COC1CC[NH+](C,COC1CC[NH+](CC,14,add C at position 13,flow_matching,0.3,2.0,33,162
144,add,14.0,N,,COC1CC[NH+](CC,COC1CC[NH+](CCN,15,add N at position 14,flow_matching,0.3,2.0,33,162
145,add,15.0,c,,COC1CC[NH+](CCN,COC1CC[NH+](CCNc,16,add c at position 15,flow_matching,0.3,2.0,33,162
146,add,16.0,2,,COC1CC[NH+](CCNc,COC1CC[NH+](CCNc2,17,add 2 at position 16,flow_matching,0.3,2.0,33,162
147,add,17.0,n,,COC1CC[NH+](CCNc2,COC1CC[NH+](CCNc2n,18,add n at position 17,flow_matching,0.3,2.0,33,162
148,add,18.0,c,,COC1CC[NH+](CCNc2n,COC1CC[NH+](CCNc2nc,19,add c at position 18,flow_matching,0.3,2.0,33,162
149,add,19.0,c,,COC1CC[NH+](CCNc2nc,COC1CC[NH+](CCNc2ncc,20,add c at position 19,flow_matching,0.3,2.0,33,162
150,add,20.0,n,,COC1CC[NH+](CCNc2ncc,COC1CC[NH+](CCNc2nccn,21,add n at position 20,flow_matching,0.3,2.0,33,162
151,add,21.0,(,,COC1CC[NH+](CCNc2nccn,COC1CC[NH+](CCNc2nccn(,22,add ( at position 21,flow_matching,0.3,2.0,33,162
152,add,22.0,C,,COC1CC[NH+](CCNc2nccn(,COC1CC[NH+](CCNc2nccn(C,23,add C at position 22,flow_matching,0.3,2.0,33,162
153,add,23.0,),,COC1CC[NH+](CCNc2nccn(C,COC1CC[NH+](CCNc2nccn(C),24,add ) at position 23,flow_matching,0.3,2.0,33,162
154,add,24.0,c,,COC1CC[NH+](CCNc2nccn(C),COC1CC[NH+](CCNc2nccn(C)c,25,add c at position 24,flow_matching,0.3,2.0,33,162
155,add,25.0,2,,COC1CC[NH+](CCNc2nccn(C)c,COC1CC[NH+](CCNc2nccn(C)c2,26,add 2 at position 25,flow_matching,0.3,2.0,33,162
156,add,26.0,=,,COC1CC[NH+](CCNc2nccn(C)c2,COC1CC[NH+](CCNc2nccn(C)c2=,27,add = at position 26,flow_matching,0.3,2.0,33,162
157,add,27.0,O,,COC1CC[NH+](CCNc2nccn(C)c2=,COC1CC[NH+](CCNc2nccn(C)c2=O,28,add O at position 27,flow_matching,0.3,2.0,33,162
158,add,28.0,),,COC1CC[NH+](CCNc2nccn(C)c2=O,COC1CC[NH+](CCNc2nccn(C)c2=O),29,add ) at position 28,flow_matching,0.3,2.0,33,162
159,add,29.0,C,,COC1CC[NH+](CCNc2nccn(C)c2=O),COC1CC[NH+](CCNc2nccn(C)c2=O)C,30,add C at position 29,flow_matching,0.3,2.0,33,162
160,add,30.0,C,,COC1CC[NH+](CCNc2nccn(C)c2=O)C,COC1CC[NH+](CCNc2nccn(C)c2=O)CC,31,add C at position 30,flow_matching,0.3,2.0,33,162
161,add,31.0,1,,COC1CC[NH+](CCNc2nccn(C)c2=O)CC,COC1CC[NH+](CCNc2nccn(C)c2=O)CC1,32,add 1 at position 31,flow_matching,0.3,2.0,33,162
162,add,32.0,"
",,COC1CC[NH+](CCNc2nccn(C)c2=O)CC1,"COC1CC[NH+](CCNc2nccn(C)c2=O)CC1
",33,"add 
 at position 32",flow_matching,0.3,2.0,33,162
