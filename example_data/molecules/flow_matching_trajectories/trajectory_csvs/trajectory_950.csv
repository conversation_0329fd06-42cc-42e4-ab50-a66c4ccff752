step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,62,288
1,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,62,288
2,replace,0.0,C,N,N,C,1,replace N at position 0 with C,flow_matching,0.3,2.0,62,288
3,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,62,288
4,add,2.0,1,,CC,CC1,3,add 1 at position 2,flow_matching,0.3,2.0,62,288
5,add,1.0,I,,CC1,CIC1,4,add I at position 1,flow_matching,0.3,2.0,62,288
6,add,1.0,+,,CIC1,C+IC1,5,add + at position 1,flow_matching,0.3,2.0,62,288
7,replace,2.0,r,I,C+IC1,C+rC1,5,replace I at position 2 with r,flow_matching,0.3,2.0,62,288
8,replace,1.0,C,+,C+rC1,CCrC1,5,replace + at position 1 with C,flow_matching,0.3,2.0,62,288
9,replace,2.0,1,r,CCrC1,CC1C1,5,replace r at position 2 with 1,flow_matching,0.3,2.0,62,288
10,add,5.0,(,,CC1C1,CC1C1(,6,add ( at position 5,flow_matching,0.3,2.0,62,288
11,replace,3.0,=,C,CC1C1(,CC1=1(,6,replace C at position 3 with =,flow_matching,0.3,2.0,62,288
12,replace,4.0,I,1,CC1=1(,CC1=I(,6,replace 1 at position 4 with I,flow_matching,0.3,2.0,62,288
13,replace,4.0,C,I,CC1=I(,CC1=C(,6,replace I at position 4 with C,flow_matching,0.3,2.0,62,288
14,replace,5.0,B,(,CC1=C(,CC1=CB,6,replace ( at position 5 with B,flow_matching,0.3,2.0,62,288
15,replace,5.0,(,B,CC1=CB,CC1=C(,6,replace B at position 5 with (,flow_matching,0.3,2.0,62,288
16,add,6.0,C,,CC1=C(,CC1=C(C,7,add C at position 6,flow_matching,0.3,2.0,62,288
17,add,3.0,-,,CC1=C(C,CC1-=C(C,8,add - at position 3,flow_matching,0.3,2.0,62,288
18,add,5.0,2,,CC1-=C(C,CC1-=2C(C,9,add 2 at position 5,flow_matching,0.3,2.0,62,288
19,remove,7.0,(,,CC1-=2C(C,CC1-=2CC,8,remove ( from position 7,flow_matching,0.3,2.0,62,288
20,remove,2.0,1,,CC1-=2CC,CC-=2CC,7,remove 1 from position 2,flow_matching,0.3,2.0,62,288
21,add,3.0,c,,CC-=2CC,CC-c=2CC,8,add c at position 3,flow_matching,0.3,2.0,62,288
22,replace,2.0,1,-,CC-c=2CC,CC1c=2CC,8,replace - at position 2 with 1,flow_matching,0.3,2.0,62,288
23,remove,7.0,C,,CC1c=2CC,CC1c=2C,7,remove C from position 7,flow_matching,0.3,2.0,62,288
24,replace,3.0,I,c,CC1c=2C,CC1I=2C,7,replace c at position 3 with I,flow_matching,0.3,2.0,62,288
25,remove,6.0,C,,CC1I=2C,CC1I=2,6,remove C from position 6,flow_matching,0.3,2.0,62,288
26,replace,3.0,=,I,CC1I=2,CC1==2,6,replace I at position 3 with =,flow_matching,0.3,2.0,62,288
27,add,5.0,n,,CC1==2,CC1==n2,7,add n at position 5,flow_matching,0.3,2.0,62,288
28,replace,3.0,r,=,CC1==n2,CC1r=n2,7,replace = at position 3 with r,flow_matching,0.3,2.0,62,288
29,remove,3.0,r,,CC1r=n2,CC1=n2,6,remove r from position 3,flow_matching,0.3,2.0,62,288
30,remove,3.0,=,,CC1=n2,CC1n2,5,remove = from position 3,flow_matching,0.3,2.0,62,288
31,add,3.0,+,,CC1n2,CC1+n2,6,add + at position 3,flow_matching,0.3,2.0,62,288
32,replace,3.0,=,+,CC1+n2,CC1=n2,6,replace + at position 3 with =,flow_matching,0.3,2.0,62,288
33,remove,4.0,n,,CC1=n2,CC1=2,5,remove n from position 4,flow_matching,0.3,2.0,62,288
34,add,0.0,3,,CC1=2,3CC1=2,6,add 3 at position 0,flow_matching,0.3,2.0,62,288
35,remove,5.0,2,,3CC1=2,3CC1=,5,remove 2 from position 5,flow_matching,0.3,2.0,62,288
36,remove,3.0,1,,3CC1=,3CC=,4,remove 1 from position 3,flow_matching,0.3,2.0,62,288
37,add,1.0,l,,3CC=,3lCC=,5,add l at position 1,flow_matching,0.3,2.0,62,288
38,replace,0.0,C,3,3lCC=,ClCC=,5,replace 3 at position 0 with C,flow_matching,0.3,2.0,62,288
39,add,3.0,F,,ClCC=,ClCFC=,6,add F at position 3,flow_matching,0.3,2.0,62,288
40,replace,1.0,C,l,ClCFC=,CCCFC=,6,replace l at position 1 with C,flow_matching,0.3,2.0,62,288
41,remove,2.0,C,,CCCFC=,CCFC=,5,remove C from position 2,flow_matching,0.3,2.0,62,288
42,remove,1.0,C,,CCFC=,CFC=,4,remove C from position 1,flow_matching,0.3,2.0,62,288
43,add,2.0,O,,CFC=,CFOC=,5,add O at position 2,flow_matching,0.3,2.0,62,288
44,replace,1.0,C,F,CFOC=,CCOC=,5,replace F at position 1 with C,flow_matching,0.3,2.0,62,288
45,replace,2.0,1,O,CCOC=,CC1C=,5,replace O at position 2 with 1,flow_matching,0.3,2.0,62,288
46,remove,2.0,1,,CC1C=,CCC=,4,remove 1 from position 2,flow_matching,0.3,2.0,62,288
47,remove,3.0,=,,CCC=,CCC,3,remove = from position 3,flow_matching,0.3,2.0,62,288
48,replace,1.0,H,C,CCC,CHC,3,replace C at position 1 with H,flow_matching,0.3,2.0,62,288
49,add,2.0,l,,CHC,CHlC,4,add l at position 2,flow_matching,0.3,2.0,62,288
50,replace,1.0,C,H,CHlC,CClC,4,replace H at position 1 with C,flow_matching,0.3,2.0,62,288
51,replace,2.0,1,l,CClC,CC1C,4,replace l at position 2 with 1,flow_matching,0.3,2.0,62,288
52,replace,3.0,=,C,CC1C,CC1=,4,replace C at position 3 with =,flow_matching,0.3,2.0,62,288
53,add,4.0,C,,CC1=,CC1=C,5,add C at position 4,flow_matching,0.3,2.0,62,288
54,add,5.0,(,,CC1=C,CC1=C(,6,add ( at position 5,flow_matching,0.3,2.0,62,288
55,remove,2.0,1,,CC1=C(,CC=C(,5,remove 1 from position 2,flow_matching,0.3,2.0,62,288
56,replace,2.0,1,=,CC=C(,CC1C(,5,replace = at position 2 with 1,flow_matching,0.3,2.0,62,288
57,add,5.0,1,,CC1C(,CC1C(1,6,add 1 at position 5,flow_matching,0.3,2.0,62,288
58,add,6.0,H,,CC1C(1,CC1C(1H,7,add H at position 6,flow_matching,0.3,2.0,62,288
59,add,7.0,H,,CC1C(1H,CC1C(1HH,8,add H at position 7,flow_matching,0.3,2.0,62,288
60,replace,3.0,S,C,CC1C(1HH,CC1S(1HH,8,replace C at position 3 with S,flow_matching,0.3,2.0,62,288
61,remove,6.0,H,,CC1S(1HH,CC1S(1H,7,remove H from position 6,flow_matching,0.3,2.0,62,288
62,replace,5.0,5,1,CC1S(1H,CC1S(5H,7,replace 1 at position 5 with 5,flow_matching,0.3,2.0,62,288
63,replace,3.0,=,S,CC1S(5H,CC1=(5H,7,replace S at position 3 with =,flow_matching,0.3,2.0,62,288
64,replace,5.0,\,5,CC1=(5H,CC1=(\H,7,replace 5 at position 5 with \,flow_matching,0.3,2.0,62,288
65,add,0.0,4,,CC1=(\H,4CC1=(\H,8,add 4 at position 0,flow_matching,0.3,2.0,62,288
66,replace,0.0,C,4,4CC1=(\H,CCC1=(\H,8,replace 4 at position 0 with C,flow_matching,0.3,2.0,62,288
67,replace,2.0,1,C,CCC1=(\H,CC11=(\H,8,replace C at position 2 with 1,flow_matching,0.3,2.0,62,288
68,replace,4.0,B,=,CC11=(\H,CC11B(\H,8,replace = at position 4 with B,flow_matching,0.3,2.0,62,288
69,replace,3.0,=,1,CC11B(\H,CC1=B(\H,8,replace 1 at position 3 with =,flow_matching,0.3,2.0,62,288
70,add,7.0,5,,CC1=B(\H,CC1=B(\5H,9,add 5 at position 7,flow_matching,0.3,2.0,62,288
71,replace,4.0,C,B,CC1=B(\5H,CC1=C(\5H,9,replace B at position 4 with C,flow_matching,0.3,2.0,62,288
72,replace,8.0,\,H,CC1=C(\5H,CC1=C(\5\,9,replace H at position 8 with \,flow_matching,0.3,2.0,62,288
73,replace,4.0,4,C,CC1=C(\5\,CC1=4(\5\,9,replace C at position 4 with 4,flow_matching,0.3,2.0,62,288
74,replace,1.0,l,C,CC1=4(\5\,Cl1=4(\5\,9,replace C at position 1 with l,flow_matching,0.3,2.0,62,288
75,remove,1.0,l,,Cl1=4(\5\,C1=4(\5\,8,remove l from position 1,flow_matching,0.3,2.0,62,288
76,add,5.0,c,,C1=4(\5\,C1=4(c\5\,9,add c at position 5,flow_matching,0.3,2.0,62,288
77,replace,1.0,C,1,C1=4(c\5\,CC=4(c\5\,9,replace 1 at position 1 with C,flow_matching,0.3,2.0,62,288
78,remove,2.0,=,,CC=4(c\5\,CC4(c\5\,8,remove = from position 2,flow_matching,0.3,2.0,62,288
79,replace,2.0,1,4,CC4(c\5\,CC1(c\5\,8,replace 4 at position 2 with 1,flow_matching,0.3,2.0,62,288
80,add,2.0,n,,CC1(c\5\,CCn1(c\5\,9,add n at position 2,flow_matching,0.3,2.0,62,288
81,replace,0.0,6,C,CCn1(c\5\,6Cn1(c\5\,9,replace C at position 0 with 6,flow_matching,0.3,2.0,62,288
82,replace,1.0,c,C,6Cn1(c\5\,6cn1(c\5\,9,replace C at position 1 with c,flow_matching,0.3,2.0,62,288
83,replace,2.0,/,n,6cn1(c\5\,6c/1(c\5\,9,replace n at position 2 with /,flow_matching,0.3,2.0,62,288
84,remove,0.0,6,,6c/1(c\5\,c/1(c\5\,8,remove 6 from position 0,flow_matching,0.3,2.0,62,288
85,replace,0.0,C,c,c/1(c\5\,C/1(c\5\,8,replace c at position 0 with C,flow_matching,0.3,2.0,62,288
86,remove,5.0,\,,C/1(c\5\,C/1(c5\,7,remove \ from position 5,flow_matching,0.3,2.0,62,288
87,add,0.0,/,,C/1(c5\,/C/1(c5\,8,add / at position 0,flow_matching,0.3,2.0,62,288
88,add,8.0,[,,/C/1(c5\,/C/1(c5\[,9,add [ at position 8,flow_matching,0.3,2.0,62,288
89,add,5.0,B,,/C/1(c5\[,/C/1(Bc5\[,10,add B at position 5,flow_matching,0.3,2.0,62,288
90,add,8.0,/,,/C/1(Bc5\[,/C/1(Bc5/\[,11,add / at position 8,flow_matching,0.3,2.0,62,288
91,replace,5.0,C,B,/C/1(Bc5/\[,/C/1(Cc5/\[,11,replace B at position 5 with C,flow_matching,0.3,2.0,62,288
92,add,1.0,6,,/C/1(Cc5/\[,/6C/1(Cc5/\[,12,add 6 at position 1,flow_matching,0.3,2.0,62,288
93,add,0.0,o,,/6C/1(Cc5/\[,o/6C/1(Cc5/\[,13,add o at position 0,flow_matching,0.3,2.0,62,288
94,add,5.0,4,,o/6C/1(Cc5/\[,o/6C/41(Cc5/\[,14,add 4 at position 5,flow_matching,0.3,2.0,62,288
95,replace,6.0,S,1,o/6C/41(Cc5/\[,o/6C/4S(Cc5/\[,14,replace 1 at position 6 with S,flow_matching,0.3,2.0,62,288
96,remove,10.0,5,,o/6C/4S(Cc5/\[,o/6C/4S(Cc/\[,13,remove 5 from position 10,flow_matching,0.3,2.0,62,288
97,replace,10.0,5,/,o/6C/4S(Cc/\[,o/6C/4S(Cc5\[,13,replace / at position 10 with 5,flow_matching,0.3,2.0,62,288
98,remove,5.0,4,,o/6C/4S(Cc5\[,o/6C/S(Cc5\[,12,remove 4 from position 5,flow_matching,0.3,2.0,62,288
99,replace,3.0,6,C,o/6C/S(Cc5\[,o/66/S(Cc5\[,12,replace C at position 3 with 6,flow_matching,0.3,2.0,62,288
100,replace,0.0,C,o,o/66/S(Cc5\[,C/66/S(Cc5\[,12,replace o at position 0 with C,flow_matching,0.3,2.0,62,288
101,add,8.0,s,,C/66/S(Cc5\[,C/66/S(Csc5\[,13,add s at position 8,flow_matching,0.3,2.0,62,288
102,replace,1.0,C,/,C/66/S(Csc5\[,CC66/S(Csc5\[,13,replace / at position 1 with C,flow_matching,0.3,2.0,62,288
103,remove,12.0,[,,CC66/S(Csc5\[,CC66/S(Csc5\,12,remove [ from position 12,flow_matching,0.3,2.0,62,288
104,replace,2.0,1,6,CC66/S(Csc5\,CC16/S(Csc5\,12,replace 6 at position 2 with 1,flow_matching,0.3,2.0,62,288
105,remove,6.0,(,,CC16/S(Csc5\,CC16/SCsc5\,11,remove ( from position 6,flow_matching,0.3,2.0,62,288
106,replace,3.0,=,6,CC16/SCsc5\,CC1=/SCsc5\,11,replace 6 at position 3 with =,flow_matching,0.3,2.0,62,288
107,replace,4.0,C,/,CC1=/SCsc5\,CC1=CSCsc5\,11,replace / at position 4 with C,flow_matching,0.3,2.0,62,288
108,add,10.0,2,,CC1=CSCsc5\,CC1=CSCsc52\,12,add 2 at position 10,flow_matching,0.3,2.0,62,288
109,add,1.0,3,,CC1=CSCsc52\,C3C1=CSCsc52\,13,add 3 at position 1,flow_matching,0.3,2.0,62,288
110,remove,4.0,=,,C3C1=CSCsc52\,C3C1CSCsc52\,12,remove = from position 4,flow_matching,0.3,2.0,62,288
111,add,11.0,],,C3C1CSCsc52\,C3C1CSCsc52]\,13,add ] at position 11,flow_matching,0.3,2.0,62,288
112,replace,1.0,C,3,C3C1CSCsc52]\,CCC1CSCsc52]\,13,replace 3 at position 1 with C,flow_matching,0.3,2.0,62,288
113,add,10.0,),,CCC1CSCsc52]\,CCC1CSCsc5)2]\,14,add ) at position 10,flow_matching,0.3,2.0,62,288
114,replace,2.0,1,C,CCC1CSCsc5)2]\,CC11CSCsc5)2]\,14,replace C at position 2 with 1,flow_matching,0.3,2.0,62,288
115,replace,6.0,r,C,CC11CSCsc5)2]\,CC11CSrsc5)2]\,14,replace C at position 6 with r,flow_matching,0.3,2.0,62,288
116,replace,3.0,=,1,CC11CSrsc5)2]\,CC1=CSrsc5)2]\,14,replace 1 at position 3 with =,flow_matching,0.3,2.0,62,288
117,replace,11.0,),2,CC1=CSrsc5)2]\,CC1=CSrsc5))]\,14,replace 2 at position 11 with ),flow_matching,0.3,2.0,62,288
118,remove,6.0,r,,CC1=CSrsc5))]\,CC1=CSsc5))]\,13,remove r from position 6,flow_matching,0.3,2.0,62,288
119,add,11.0,7,,CC1=CSsc5))]\,CC1=CSsc5))7]\,14,add 7 at position 11,flow_matching,0.3,2.0,62,288
120,replace,7.0,[,c,CC1=CSsc5))7]\,CC1=CSs[5))7]\,14,replace c at position 7 with [,flow_matching,0.3,2.0,62,288
121,replace,5.0,(,S,CC1=CSs[5))7]\,CC1=C(s[5))7]\,14,replace S at position 5 with (,flow_matching,0.3,2.0,62,288
122,replace,8.0,n,5,CC1=C(s[5))7]\,CC1=C(s[n))7]\,14,replace 5 at position 8 with n,flow_matching,0.3,2.0,62,288
123,add,10.0,5,,CC1=C(s[n))7]\,CC1=C(s[n)5)7]\,15,add 5 at position 10,flow_matching,0.3,2.0,62,288
124,remove,5.0,(,,CC1=C(s[n)5)7]\,CC1=Cs[n)5)7]\,14,remove ( from position 5,flow_matching,0.3,2.0,62,288
125,replace,8.0,o,),CC1=Cs[n)5)7]\,CC1=Cs[no5)7]\,14,replace ) at position 8 with o,flow_matching,0.3,2.0,62,288
126,replace,0.0,5,C,CC1=Cs[no5)7]\,5C1=Cs[no5)7]\,14,replace C at position 0 with 5,flow_matching,0.3,2.0,62,288
127,remove,3.0,=,,5C1=Cs[no5)7]\,5C1Cs[no5)7]\,13,remove = from position 3,flow_matching,0.3,2.0,62,288
128,add,11.0,s,,5C1Cs[no5)7]\,5C1Cs[no5)7s]\,14,add s at position 11,flow_matching,0.3,2.0,62,288
129,replace,12.0,1,],5C1Cs[no5)7s]\,5C1Cs[no5)7s1\,14,replace ] at position 12 with 1,flow_matching,0.3,2.0,62,288
130,replace,0.0,C,5,5C1Cs[no5)7s1\,CC1Cs[no5)7s1\,14,replace 5 at position 0 with C,flow_matching,0.3,2.0,62,288
131,replace,4.0,H,s,CC1Cs[no5)7s1\,CC1CH[no5)7s1\,14,replace s at position 4 with H,flow_matching,0.3,2.0,62,288
132,add,11.0,1,,CC1CH[no5)7s1\,CC1CH[no5)71s1\,15,add 1 at position 11,flow_matching,0.3,2.0,62,288
133,replace,3.0,=,C,CC1CH[no5)71s1\,CC1=H[no5)71s1\,15,replace C at position 3 with =,flow_matching,0.3,2.0,62,288
134,remove,12.0,s,,CC1=H[no5)71s1\,CC1=H[no5)711\,14,remove s from position 12,flow_matching,0.3,2.0,62,288
135,replace,4.0,C,H,CC1=H[no5)711\,CC1=C[no5)711\,14,replace H at position 4 with C,flow_matching,0.3,2.0,62,288
136,replace,5.0,(,[,CC1=C[no5)711\,CC1=C(no5)711\,14,replace [ at position 5 with (,flow_matching,0.3,2.0,62,288
137,remove,12.0,1,,CC1=C(no5)711\,CC1=C(no5)71\,13,remove 1 from position 12,flow_matching,0.3,2.0,62,288
138,replace,7.0,4,o,CC1=C(no5)71\,CC1=C(n45)71\,13,replace o at position 7 with 4,flow_matching,0.3,2.0,62,288
139,replace,6.0,C,n,CC1=C(n45)71\,CC1=C(C45)71\,13,replace n at position 6 with C,flow_matching,0.3,2.0,62,288
140,replace,1.0,2,C,CC1=C(C45)71\,C21=C(C45)71\,13,replace C at position 1 with 2,flow_matching,0.3,2.0,62,288
141,add,2.0,F,,C21=C(C45)71\,C2F1=C(C45)71\,14,add F at position 2,flow_matching,0.3,2.0,62,288
142,add,5.0,3,,C2F1=C(C45)71\,C2F1=3C(C45)71\,15,add 3 at position 5,flow_matching,0.3,2.0,62,288
143,remove,0.0,C,,C2F1=3C(C45)71\,2F1=3C(C45)71\,14,remove C from position 0,flow_matching,0.3,2.0,62,288
144,replace,0.0,[,2,2F1=3C(C45)71\,[F1=3C(C45)71\,14,replace 2 at position 0 with [,flow_matching,0.3,2.0,62,288
145,remove,4.0,3,,[F1=3C(C45)71\,[F1=C(C45)71\,13,remove 3 from position 4,flow_matching,0.3,2.0,62,288
146,replace,0.0,),[,[F1=C(C45)71\,)F1=C(C45)71\,13,replace [ at position 0 with ),flow_matching,0.3,2.0,62,288
147,replace,0.0,C,),)F1=C(C45)71\,CF1=C(C45)71\,13,replace ) at position 0 with C,flow_matching,0.3,2.0,62,288
148,replace,2.0,c,1,CF1=C(C45)71\,CFc=C(C45)71\,13,replace 1 at position 2 with c,flow_matching,0.3,2.0,62,288
149,replace,1.0,C,F,CFc=C(C45)71\,CCc=C(C45)71\,13,replace F at position 1 with C,flow_matching,0.3,2.0,62,288
150,replace,10.0,-,7,CCc=C(C45)71\,CCc=C(C45)-1\,13,replace 7 at position 10 with -,flow_matching,0.3,2.0,62,288
151,replace,2.0,1,c,CCc=C(C45)-1\,CC1=C(C45)-1\,13,replace c at position 2 with 1,flow_matching,0.3,2.0,62,288
152,add,0.0,H,,CC1=C(C45)-1\,HCC1=C(C45)-1\,14,add H at position 0,flow_matching,0.3,2.0,62,288
153,replace,0.0,C,H,HCC1=C(C45)-1\,CCC1=C(C45)-1\,14,replace H at position 0 with C,flow_matching,0.3,2.0,62,288
154,add,5.0,+,,CCC1=C(C45)-1\,CCC1=+C(C45)-1\,15,add + at position 5,flow_matching,0.3,2.0,62,288
155,replace,2.0,1,C,CCC1=+C(C45)-1\,CC11=+C(C45)-1\,15,replace C at position 2 with 1,flow_matching,0.3,2.0,62,288
156,add,9.0,],,CC11=+C(C45)-1\,CC11=+C(C]45)-1\,16,add ] at position 9,flow_matching,0.3,2.0,62,288
157,replace,9.0,C,],CC11=+C(C]45)-1\,CC11=+C(CC45)-1\,16,replace ] at position 9 with C,flow_matching,0.3,2.0,62,288
158,replace,3.0,=,1,CC11=+C(CC45)-1\,CC1==+C(CC45)-1\,16,replace 1 at position 3 with =,flow_matching,0.3,2.0,62,288
159,replace,4.0,C,=,CC1==+C(CC45)-1\,CC1=C+C(CC45)-1\,16,replace = at position 4 with C,flow_matching,0.3,2.0,62,288
160,remove,12.0,),,CC1=C+C(CC45)-1\,CC1=C+C(CC45-1\,15,remove ) from position 12,flow_matching,0.3,2.0,62,288
161,replace,5.0,(,+,CC1=C+C(CC45-1\,CC1=C(C(CC45-1\,15,replace + at position 5 with (,flow_matching,0.3,2.0,62,288
162,replace,8.0,=,C,CC1=C(C(CC45-1\,CC1=C(C(=C45-1\,15,replace C at position 8 with =,flow_matching,0.3,2.0,62,288
163,replace,2.0,H,1,CC1=C(C(=C45-1\,CCH=C(C(=C45-1\,15,replace 1 at position 2 with H,flow_matching,0.3,2.0,62,288
164,replace,2.0,1,H,CCH=C(C(=C45-1\,CC1=C(C(=C45-1\,15,replace H at position 2 with 1,flow_matching,0.3,2.0,62,288
165,remove,14.0,\,,CC1=C(C(=C45-1\,CC1=C(C(=C45-1,14,remove \ from position 14,flow_matching,0.3,2.0,62,288
166,replace,7.0,6,(,CC1=C(C(=C45-1,CC1=C(C6=C45-1,14,replace ( at position 7 with 6,flow_matching,0.3,2.0,62,288
167,replace,7.0,(,6,CC1=C(C6=C45-1,CC1=C(C(=C45-1,14,replace 6 at position 7 with (,flow_matching,0.3,2.0,62,288
168,replace,9.0,O,C,CC1=C(C(=C45-1,CC1=C(C(=O45-1,14,replace C at position 9 with O,flow_matching,0.3,2.0,62,288
169,add,10.0,/,,CC1=C(C(=O45-1,CC1=C(C(=O/45-1,15,add / at position 10,flow_matching,0.3,2.0,62,288
170,replace,10.0,),/,CC1=C(C(=O/45-1,CC1=C(C(=O)45-1,15,replace / at position 10 with ),flow_matching,0.3,2.0,62,288
171,add,15.0,H,,CC1=C(C(=O)45-1,CC1=C(C(=O)45-1H,16,add H at position 15,flow_matching,0.3,2.0,62,288
172,add,9.0,/,,CC1=C(C(=O)45-1H,CC1=C(C(=/O)45-1H,17,add / at position 9,flow_matching,0.3,2.0,62,288
173,add,5.0,s,,CC1=C(C(=/O)45-1H,CC1=Cs(C(=/O)45-1H,18,add s at position 5,flow_matching,0.3,2.0,62,288
174,add,16.0,(,,CC1=Cs(C(=/O)45-1H,CC1=Cs(C(=/O)45-(1H,19,add ( at position 16,flow_matching,0.3,2.0,62,288
175,replace,5.0,(,s,CC1=Cs(C(=/O)45-(1H,CC1=C((C(=/O)45-(1H,19,replace s at position 5 with (,flow_matching,0.3,2.0,62,288
176,add,6.0,N,,CC1=C((C(=/O)45-(1H,CC1=C(N(C(=/O)45-(1H,20,add N at position 6,flow_matching,0.3,2.0,62,288
177,add,1.0,@,,CC1=C(N(C(=/O)45-(1H,C@C1=C(N(C(=/O)45-(1H,21,add @ at position 1,flow_matching,0.3,2.0,62,288
178,remove,15.0,4,,C@C1=C(N(C(=/O)45-(1H,C@C1=C(N(C(=/O)5-(1H,20,remove 4 from position 15,flow_matching,0.3,2.0,62,288
179,remove,9.0,C,,C@C1=C(N(C(=/O)5-(1H,C@C1=C(N((=/O)5-(1H,19,remove C from position 9,flow_matching,0.3,2.0,62,288
180,add,17.0,\,,C@C1=C(N((=/O)5-(1H,C@C1=C(N((=/O)5-(\1H,20,add \ at position 17,flow_matching,0.3,2.0,62,288
181,add,2.0,n,,C@C1=C(N((=/O)5-(\1H,C@nC1=C(N((=/O)5-(\1H,21,add n at position 2,flow_matching,0.3,2.0,62,288
182,add,3.0,7,,C@nC1=C(N((=/O)5-(\1H,C@n7C1=C(N((=/O)5-(\1H,22,add 7 at position 3,flow_matching,0.3,2.0,62,288
183,replace,14.0,1,O,C@n7C1=C(N((=/O)5-(\1H,C@n7C1=C(N((=/1)5-(\1H,22,replace O at position 14 with 1,flow_matching,0.3,2.0,62,288
184,replace,1.0,C,@,C@n7C1=C(N((=/1)5-(\1H,CCn7C1=C(N((=/1)5-(\1H,22,replace @ at position 1 with C,flow_matching,0.3,2.0,62,288
185,remove,16.0,5,,CCn7C1=C(N((=/1)5-(\1H,CCn7C1=C(N((=/1)-(\1H,21,remove 5 from position 16,flow_matching,0.3,2.0,62,288
186,add,6.0,4,,CCn7C1=C(N((=/1)-(\1H,CCn7C14=C(N((=/1)-(\1H,22,add 4 at position 6,flow_matching,0.3,2.0,62,288
187,remove,21.0,H,,CCn7C14=C(N((=/1)-(\1H,CCn7C14=C(N((=/1)-(\1,21,remove H from position 21,flow_matching,0.3,2.0,62,288
188,add,9.0,@,,CCn7C14=C(N((=/1)-(\1,CCn7C14=C@(N((=/1)-(\1,22,add @ at position 9,flow_matching,0.3,2.0,62,288
189,remove,1.0,C,,CCn7C14=C@(N((=/1)-(\1,Cn7C14=C@(N((=/1)-(\1,21,remove C from position 1,flow_matching,0.3,2.0,62,288
190,replace,13.0,F,=,Cn7C14=C@(N((=/1)-(\1,Cn7C14=C@(N((F/1)-(\1,21,replace = at position 13 with F,flow_matching,0.3,2.0,62,288
191,replace,7.0,4,C,Cn7C14=C@(N((F/1)-(\1,Cn7C14=4@(N((F/1)-(\1,21,replace C at position 7 with 4,flow_matching,0.3,2.0,62,288
192,remove,16.0,),,Cn7C14=4@(N((F/1)-(\1,Cn7C14=4@(N((F/1-(\1,20,remove ) from position 16,flow_matching,0.3,2.0,62,288
193,replace,1.0,C,n,Cn7C14=4@(N((F/1-(\1,CC7C14=4@(N((F/1-(\1,20,replace n at position 1 with C,flow_matching,0.3,2.0,62,288
194,add,6.0,\,,CC7C14=4@(N((F/1-(\1,CC7C14\=4@(N((F/1-(\1,21,add \ at position 6,flow_matching,0.3,2.0,62,288
195,replace,2.0,1,7,CC7C14\=4@(N((F/1-(\1,CC1C14\=4@(N((F/1-(\1,21,replace 7 at position 2 with 1,flow_matching,0.3,2.0,62,288
196,add,9.0,B,,CC1C14\=4@(N((F/1-(\1,CC1C14\=4B@(N((F/1-(\1,22,add B at position 9,flow_matching,0.3,2.0,62,288
197,remove,17.0,1,,CC1C14\=4B@(N((F/1-(\1,CC1C14\=4B@(N((F/-(\1,21,remove 1 from position 17,flow_matching,0.3,2.0,62,288
198,replace,3.0,=,C,CC1C14\=4B@(N((F/-(\1,CC1=14\=4B@(N((F/-(\1,21,replace C at position 3 with =,flow_matching,0.3,2.0,62,288
199,replace,4.0,C,1,CC1=14\=4B@(N((F/-(\1,CC1=C4\=4B@(N((F/-(\1,21,replace 1 at position 4 with C,flow_matching,0.3,2.0,62,288
200,replace,5.0,(,4,CC1=C4\=4B@(N((F/-(\1,CC1=C(\=4B@(N((F/-(\1,21,replace 4 at position 5 with (,flow_matching,0.3,2.0,62,288
201,add,19.0,4,,CC1=C(\=4B@(N((F/-(\1,CC1=C(\=4B@(N((F/-(4\1,22,add 4 at position 19,flow_matching,0.3,2.0,62,288
202,replace,6.0,C,\,CC1=C(\=4B@(N((F/-(4\1,CC1=C(C=4B@(N((F/-(4\1,22,replace \ at position 6 with C,flow_matching,0.3,2.0,62,288
203,add,14.0,n,,CC1=C(C=4B@(N((F/-(4\1,CC1=C(C=4B@(N(n(F/-(4\1,23,add n at position 14,flow_matching,0.3,2.0,62,288
204,replace,13.0,F,(,CC1=C(C=4B@(N(n(F/-(4\1,CC1=C(C=4B@(NFn(F/-(4\1,23,replace ( at position 13 with F,flow_matching,0.3,2.0,62,288
205,replace,16.0,n,F,CC1=C(C=4B@(NFn(F/-(4\1,CC1=C(C=4B@(NFn(n/-(4\1,23,replace F at position 16 with n,flow_matching,0.3,2.0,62,288
206,remove,2.0,1,,CC1=C(C=4B@(NFn(n/-(4\1,CC=C(C=4B@(NFn(n/-(4\1,22,remove 1 from position 2,flow_matching,0.3,2.0,62,288
207,replace,2.0,1,=,CC=C(C=4B@(NFn(n/-(4\1,CC1C(C=4B@(NFn(n/-(4\1,22,replace = at position 2 with 1,flow_matching,0.3,2.0,62,288
208,replace,10.0,/,(,CC1C(C=4B@(NFn(n/-(4\1,CC1C(C=4B@/NFn(n/-(4\1,22,replace ( at position 10 with /,flow_matching,0.3,2.0,62,288
209,replace,19.0,[,4,CC1C(C=4B@/NFn(n/-(4\1,CC1C(C=4B@/NFn(n/-([\1,22,replace 4 at position 19 with [,flow_matching,0.3,2.0,62,288
210,replace,7.0,+,4,CC1C(C=4B@/NFn(n/-([\1,CC1C(C=+B@/NFn(n/-([\1,22,replace 4 at position 7 with +,flow_matching,0.3,2.0,62,288
211,replace,3.0,=,C,CC1C(C=+B@/NFn(n/-([\1,CC1=(C=+B@/NFn(n/-([\1,22,replace C at position 3 with =,flow_matching,0.3,2.0,62,288
212,replace,4.0,C,(,CC1=(C=+B@/NFn(n/-([\1,CC1=CC=+B@/NFn(n/-([\1,22,replace ( at position 4 with C,flow_matching,0.3,2.0,62,288
213,remove,1.0,C,,CC1=CC=+B@/NFn(n/-([\1,C1=CC=+B@/NFn(n/-([\1,21,remove C from position 1,flow_matching,0.3,2.0,62,288
214,add,7.0,=,,C1=CC=+B@/NFn(n/-([\1,C1=CC=+=B@/NFn(n/-([\1,22,add = at position 7,flow_matching,0.3,2.0,62,288
215,replace,12.0,(,F,C1=CC=+=B@/NFn(n/-([\1,C1=CC=+=B@/N(n(n/-([\1,22,replace F at position 12 with (,flow_matching,0.3,2.0,62,288
216,add,1.0,c,,C1=CC=+=B@/N(n(n/-([\1,Cc1=CC=+=B@/N(n(n/-([\1,23,add c at position 1,flow_matching,0.3,2.0,62,288
217,add,21.0,1,,Cc1=CC=+=B@/N(n(n/-([\1,Cc1=CC=+=B@/N(n(n/-([1\1,24,add 1 at position 21,flow_matching,0.3,2.0,62,288
218,remove,16.0,n,,Cc1=CC=+=B@/N(n(n/-([1\1,Cc1=CC=+=B@/N(n(/-([1\1,23,remove n from position 16,flow_matching,0.3,2.0,62,288
219,replace,16.0,o,/,Cc1=CC=+=B@/N(n(/-([1\1,Cc1=CC=+=B@/N(n(o-([1\1,23,replace / at position 16 with o,flow_matching,0.3,2.0,62,288
220,remove,16.0,o,,Cc1=CC=+=B@/N(n(o-([1\1,Cc1=CC=+=B@/N(n(-([1\1,22,remove o from position 16,flow_matching,0.3,2.0,62,288
221,replace,8.0,@,=,Cc1=CC=+=B@/N(n(-([1\1,Cc1=CC=+@B@/N(n(-([1\1,22,replace = at position 8 with @,flow_matching,0.3,2.0,62,288
222,remove,17.0,(,,Cc1=CC=+@B@/N(n(-([1\1,Cc1=CC=+@B@/N(n(-[1\1,21,remove ( from position 17,flow_matching,0.3,2.0,62,288
223,replace,1.0,C,c,Cc1=CC=+@B@/N(n(-[1\1,CC1=CC=+@B@/N(n(-[1\1,21,replace c at position 1 with C,flow_matching,0.3,2.0,62,288
224,add,3.0,O,,CC1=CC=+@B@/N(n(-[1\1,CC1O=CC=+@B@/N(n(-[1\1,22,add O at position 3,flow_matching,0.3,2.0,62,288
225,replace,3.0,@,O,CC1O=CC=+@B@/N(n(-[1\1,CC1@=CC=+@B@/N(n(-[1\1,22,replace O at position 3 with @,flow_matching,0.3,2.0,62,288
226,replace,3.0,=,@,CC1@=CC=+@B@/N(n(-[1\1,CC1==CC=+@B@/N(n(-[1\1,22,replace @ at position 3 with =,flow_matching,0.3,2.0,62,288
227,add,6.0,l,,CC1==CC=+@B@/N(n(-[1\1,CC1==ClC=+@B@/N(n(-[1\1,23,add l at position 6,flow_matching,0.3,2.0,62,288
228,replace,0.0,H,C,CC1==ClC=+@B@/N(n(-[1\1,HC1==ClC=+@B@/N(n(-[1\1,23,replace C at position 0 with H,flow_matching,0.3,2.0,62,288
229,add,4.0,1,,HC1==ClC=+@B@/N(n(-[1\1,HC1=1=ClC=+@B@/N(n(-[1\1,24,add 1 at position 4,flow_matching,0.3,2.0,62,288
230,replace,4.0,B,1,HC1=1=ClC=+@B@/N(n(-[1\1,HC1=B=ClC=+@B@/N(n(-[1\1,24,replace 1 at position 4 with B,flow_matching,0.3,2.0,62,288
231,replace,0.0,C,H,HC1=B=ClC=+@B@/N(n(-[1\1,CC1=B=ClC=+@B@/N(n(-[1\1,24,replace H at position 0 with C,flow_matching,0.3,2.0,62,288
232,replace,4.0,C,B,CC1=B=ClC=+@B@/N(n(-[1\1,CC1=C=ClC=+@B@/N(n(-[1\1,24,replace B at position 4 with C,flow_matching,0.3,2.0,62,288
233,replace,5.0,(,=,CC1=C=ClC=+@B@/N(n(-[1\1,CC1=C(ClC=+@B@/N(n(-[1\1,24,replace = at position 5 with (,flow_matching,0.3,2.0,62,288
234,replace,7.0,(,l,CC1=C(ClC=+@B@/N(n(-[1\1,CC1=C(C(C=+@B@/N(n(-[1\1,24,replace l at position 7 with (,flow_matching,0.3,2.0,62,288
235,replace,8.0,=,C,CC1=C(C(C=+@B@/N(n(-[1\1,CC1=C(C(==+@B@/N(n(-[1\1,24,replace C at position 8 with =,flow_matching,0.3,2.0,62,288
236,replace,9.0,O,=,CC1=C(C(==+@B@/N(n(-[1\1,CC1=C(C(=O+@B@/N(n(-[1\1,24,replace = at position 9 with O,flow_matching,0.3,2.0,62,288
237,replace,10.0,),+,CC1=C(C(=O+@B@/N(n(-[1\1,CC1=C(C(=O)@B@/N(n(-[1\1,24,replace + at position 10 with ),flow_matching,0.3,2.0,62,288
238,replace,11.0,O,@,CC1=C(C(=O)@B@/N(n(-[1\1,CC1=C(C(=O)OB@/N(n(-[1\1,24,replace @ at position 11 with O,flow_matching,0.3,2.0,62,288
239,replace,12.0,C,B,CC1=C(C(=O)OB@/N(n(-[1\1,CC1=C(C(=O)OC@/N(n(-[1\1,24,replace B at position 12 with C,flow_matching,0.3,2.0,62,288
240,replace,13.0,C,@,CC1=C(C(=O)OC@/N(n(-[1\1,CC1=C(C(=O)OCC/N(n(-[1\1,24,replace @ at position 13 with C,flow_matching,0.3,2.0,62,288
241,replace,14.0,(,/,CC1=C(C(=O)OCC/N(n(-[1\1,CC1=C(C(=O)OCC(N(n(-[1\1,24,replace / at position 14 with (,flow_matching,0.3,2.0,62,288
242,replace,15.0,C,N,CC1=C(C(=O)OCC(N(n(-[1\1,CC1=C(C(=O)OCC(C(n(-[1\1,24,replace N at position 15 with C,flow_matching,0.3,2.0,62,288
243,replace,16.0,),(,CC1=C(C(=O)OCC(C(n(-[1\1,CC1=C(C(=O)OCC(C)n(-[1\1,24,replace ( at position 16 with ),flow_matching,0.3,2.0,62,288
244,replace,17.0,C,n,CC1=C(C(=O)OCC(C)n(-[1\1,CC1=C(C(=O)OCC(C)C(-[1\1,24,replace n at position 17 with C,flow_matching,0.3,2.0,62,288
245,replace,18.0,),(,CC1=C(C(=O)OCC(C)C(-[1\1,CC1=C(C(=O)OCC(C)C)-[1\1,24,replace ( at position 18 with ),flow_matching,0.3,2.0,62,288
246,replace,19.0,[,-,CC1=C(C(=O)OCC(C)C)-[1\1,CC1=C(C(=O)OCC(C)C)[[1\1,24,replace - at position 19 with [,flow_matching,0.3,2.0,62,288
247,replace,20.0,C,[,CC1=C(C(=O)OCC(C)C)[[1\1,CC1=C(C(=O)OCC(C)C)[C1\1,24,replace [ at position 20 with C,flow_matching,0.3,2.0,62,288
248,replace,21.0,@,1,CC1=C(C(=O)OCC(C)C)[C1\1,CC1=C(C(=O)OCC(C)C)[C@\1,24,replace 1 at position 21 with @,flow_matching,0.3,2.0,62,288
249,replace,22.0,H,\,CC1=C(C(=O)OCC(C)C)[C@\1,CC1=C(C(=O)OCC(C)C)[C@H1,24,replace \ at position 22 with H,flow_matching,0.3,2.0,62,288
250,replace,23.0,],1,CC1=C(C(=O)OCC(C)C)[C@H1,CC1=C(C(=O)OCC(C)C)[C@H],24,replace 1 at position 23 with ],flow_matching,0.3,2.0,62,288
251,add,24.0,(,,CC1=C(C(=O)OCC(C)C)[C@H],CC1=C(C(=O)OCC(C)C)[C@H](,25,add ( at position 24,flow_matching,0.3,2.0,62,288
252,add,25.0,c,,CC1=C(C(=O)OCC(C)C)[C@H](,CC1=C(C(=O)OCC(C)C)[C@H](c,26,add c at position 25,flow_matching,0.3,2.0,62,288
253,add,26.0,2,,CC1=C(C(=O)OCC(C)C)[C@H](c,CC1=C(C(=O)OCC(C)C)[C@H](c2,27,add 2 at position 26,flow_matching,0.3,2.0,62,288
254,add,27.0,c,,CC1=C(C(=O)OCC(C)C)[C@H](c2,CC1=C(C(=O)OCC(C)C)[C@H](c2c,28,add c at position 27,flow_matching,0.3,2.0,62,288
255,add,28.0,c,,CC1=C(C(=O)OCC(C)C)[C@H](c2c,CC1=C(C(=O)OCC(C)C)[C@H](c2cc,29,add c at position 28,flow_matching,0.3,2.0,62,288
256,add,29.0,c,,CC1=C(C(=O)OCC(C)C)[C@H](c2cc,CC1=C(C(=O)OCC(C)C)[C@H](c2ccc,30,add c at position 29,flow_matching,0.3,2.0,62,288
257,add,30.0,c,,CC1=C(C(=O)OCC(C)C)[C@H](c2ccc,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc,31,add c at position 30,flow_matching,0.3,2.0,62,288
258,add,31.0,(,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(,32,add ( at position 31,flow_matching,0.3,2.0,62,288
259,add,32.0,F,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F,33,add F at position 32,flow_matching,0.3,2.0,62,288
260,add,33.0,),,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F),34,add ) at position 33,flow_matching,0.3,2.0,62,288
261,add,34.0,c,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F),CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c,35,add c at position 34,flow_matching,0.3,2.0,62,288
262,add,35.0,2,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2,36,add 2 at position 35,flow_matching,0.3,2.0,62,288
263,add,36.0,),,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2),37,add ) at position 36,flow_matching,0.3,2.0,62,288
264,add,37.0,c,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2),CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c,38,add c at position 37,flow_matching,0.3,2.0,62,288
265,add,38.0,2,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2,39,add 2 at position 38,flow_matching,0.3,2.0,62,288
266,add,39.0,c,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c,40,add c at position 39,flow_matching,0.3,2.0,62,288
267,add,40.0,(,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(,41,add ( at position 40,flow_matching,0.3,2.0,62,288
268,add,41.0,n,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n,42,add n at position 41,flow_matching,0.3,2.0,62,288
269,add,42.0,(,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(,43,add ( at position 42,flow_matching,0.3,2.0,62,288
270,add,43.0,C,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C,44,add C at position 43,flow_matching,0.3,2.0,62,288
271,add,44.0,),,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C),45,add ) at position 44,flow_matching,0.3,2.0,62,288
272,add,45.0,c,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C),CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c,46,add c at position 45,flow_matching,0.3,2.0,62,288
273,add,46.0,(,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(,47,add ( at position 46,flow_matching,0.3,2.0,62,288
274,add,47.0,=,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=,48,add = at position 47,flow_matching,0.3,2.0,62,288
275,add,48.0,O,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O,49,add O at position 48,flow_matching,0.3,2.0,62,288
276,add,49.0,),,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O),50,add ) at position 49,flow_matching,0.3,2.0,62,288
277,add,50.0,n,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O),CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n,51,add n at position 50,flow_matching,0.3,2.0,62,288
278,add,51.0,(,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(,52,add ( at position 51,flow_matching,0.3,2.0,62,288
279,add,52.0,C,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C,53,add C at position 52,flow_matching,0.3,2.0,62,288
280,add,53.0,),,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C),54,add ) at position 53,flow_matching,0.3,2.0,62,288
281,add,54.0,c,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C),CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c,55,add c at position 54,flow_matching,0.3,2.0,62,288
282,add,55.0,2,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2,56,add 2 at position 55,flow_matching,0.3,2.0,62,288
283,add,56.0,=,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=,57,add = at position 56,flow_matching,0.3,2.0,62,288
284,add,57.0,O,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=O,58,add O at position 57,flow_matching,0.3,2.0,62,288
285,add,58.0,),,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=O,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=O),59,add ) at position 58,flow_matching,0.3,2.0,62,288
286,add,59.0,N,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=O),CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=O)N,60,add N at position 59,flow_matching,0.3,2.0,62,288
287,add,60.0,1,,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=O)N,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=O)N1,61,add 1 at position 60,flow_matching,0.3,2.0,62,288
288,add,61.0,"
",,CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=O)N1,"CC1=C(C(=O)OCC(C)C)[C@H](c2cccc(F)c2)c2c(n(C)c(=O)n(C)c2=O)N1
",62,"add 
 at position 61",flow_matching,0.3,2.0,62,288
