step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,36,172
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,36,172
2,replace,0.0,6,C,C,6,1,replace <PERSON> at position 0 with 6,flow_matching,0.3,2.0,36,172
3,replace,0.0,C,6,6,C,1,replace 6 at position 0 with C,flow_matching,0.3,2.0,36,172
4,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,36,172
5,replace,0.0,@,C,Cc,@c,2,replace <PERSON> at position 0 with @,flow_matching,0.3,2.0,36,172
6,add,0.0,3,,@c,3@c,3,add 3 at position 0,flow_matching,0.3,2.0,36,172
7,remove,0.0,3,,3@c,@c,2,remove 3 from position 0,flow_matching,0.3,2.0,36,172
8,replace,0.0,C,@,@c,Cc,2,replace @ at position 0 with C,flow_matching,0.3,2.0,36,172
9,add,0.0,o,,Cc,oCc,3,add o at position 0,flow_matching,0.3,2.0,36,172
10,replace,0.0,C,o,oCc,CCc,3,replace o at position 0 with C,flow_matching,0.3,2.0,36,172
11,replace,1.0,c,C,CCc,Ccc,3,replace C at position 1 with c,flow_matching,0.3,2.0,36,172
12,add,2.0,+,,Ccc,Cc+c,4,add + at position 2,flow_matching,0.3,2.0,36,172
13,replace,0.0,\,C,Cc+c,\c+c,4,replace C at position 0 with \,flow_matching,0.3,2.0,36,172
14,remove,1.0,c,,\c+c,\+c,3,remove c from position 1,flow_matching,0.3,2.0,36,172
15,replace,1.0,),+,\+c,\)c,3,replace + at position 1 with ),flow_matching,0.3,2.0,36,172
16,remove,1.0,),,\)c,\c,2,remove ) from position 1,flow_matching,0.3,2.0,36,172
17,add,1.0,l,,\c,\lc,3,add l at position 1,flow_matching,0.3,2.0,36,172
18,add,3.0,l,,\lc,\lcl,4,add l at position 3,flow_matching,0.3,2.0,36,172
19,replace,0.0,C,\,\lcl,Clcl,4,replace \ at position 0 with C,flow_matching,0.3,2.0,36,172
20,add,0.0,O,,Clcl,OClcl,5,add O at position 0,flow_matching,0.3,2.0,36,172
21,replace,0.0,C,O,OClcl,CClcl,5,replace O at position 0 with C,flow_matching,0.3,2.0,36,172
22,replace,1.0,c,C,CClcl,Cclcl,5,replace C at position 1 with c,flow_matching,0.3,2.0,36,172
23,replace,2.0,1,l,Cclcl,Cc1cl,5,replace l at position 2 with 1,flow_matching,0.3,2.0,36,172
24,replace,3.0,l,c,Cc1cl,Cc1ll,5,replace c at position 3 with l,flow_matching,0.3,2.0,36,172
25,replace,1.0,o,c,Cc1ll,Co1ll,5,replace c at position 1 with o,flow_matching,0.3,2.0,36,172
26,add,3.0,l,,Co1ll,Co1lll,6,add l at position 3,flow_matching,0.3,2.0,36,172
27,add,4.0,5,,Co1lll,Co1l5ll,7,add 5 at position 4,flow_matching,0.3,2.0,36,172
28,add,1.0,n,,Co1l5ll,Cno1l5ll,8,add n at position 1,flow_matching,0.3,2.0,36,172
29,add,5.0,-,,Cno1l5ll,Cno1l-5ll,9,add - at position 5,flow_matching,0.3,2.0,36,172
30,replace,1.0,2,n,Cno1l-5ll,C2o1l-5ll,9,replace n at position 1 with 2,flow_matching,0.3,2.0,36,172
31,replace,1.0,c,2,C2o1l-5ll,Cco1l-5ll,9,replace 2 at position 1 with c,flow_matching,0.3,2.0,36,172
32,replace,2.0,1,o,Cco1l-5ll,Cc11l-5ll,9,replace o at position 2 with 1,flow_matching,0.3,2.0,36,172
33,replace,7.0,H,l,Cc11l-5ll,Cc11l-5Hl,9,replace l at position 7 with H,flow_matching,0.3,2.0,36,172
34,replace,3.0,c,1,Cc11l-5Hl,Cc1cl-5Hl,9,replace 1 at position 3 with c,flow_matching,0.3,2.0,36,172
35,remove,2.0,1,,Cc1cl-5Hl,Cccl-5Hl,8,remove 1 from position 2,flow_matching,0.3,2.0,36,172
36,add,5.0,6,,Cccl-5Hl,Cccl-65Hl,9,add 6 at position 5,flow_matching,0.3,2.0,36,172
37,remove,4.0,-,,Cccl-65Hl,Cccl65Hl,8,remove - from position 4,flow_matching,0.3,2.0,36,172
38,replace,2.0,1,c,Cccl65Hl,Cc1l65Hl,8,replace c at position 2 with 1,flow_matching,0.3,2.0,36,172
39,remove,3.0,l,,Cc1l65Hl,Cc165Hl,7,remove l from position 3,flow_matching,0.3,2.0,36,172
40,replace,4.0,/,5,Cc165Hl,Cc16/Hl,7,replace 5 at position 4 with /,flow_matching,0.3,2.0,36,172
41,replace,3.0,c,6,Cc16/Hl,Cc1c/Hl,7,replace 6 at position 3 with c,flow_matching,0.3,2.0,36,172
42,replace,5.0,@,H,Cc1c/Hl,Cc1c/@l,7,replace H at position 5 with @,flow_matching,0.3,2.0,36,172
43,add,3.0,l,,Cc1c/@l,Cc1lc/@l,8,add l at position 3,flow_matching,0.3,2.0,36,172
44,replace,5.0,r,/,Cc1lc/@l,Cc1lcr@l,8,replace / at position 5 with r,flow_matching,0.3,2.0,36,172
45,remove,4.0,c,,Cc1lcr@l,Cc1lr@l,7,remove c from position 4,flow_matching,0.3,2.0,36,172
46,add,3.0,3,,Cc1lr@l,Cc13lr@l,8,add 3 at position 3,flow_matching,0.3,2.0,36,172
47,remove,2.0,1,,Cc13lr@l,Cc3lr@l,7,remove 1 from position 2,flow_matching,0.3,2.0,36,172
48,replace,2.0,1,3,Cc3lr@l,Cc1lr@l,7,replace 3 at position 2 with 1,flow_matching,0.3,2.0,36,172
49,replace,3.0,c,l,Cc1lr@l,Cc1cr@l,7,replace l at position 3 with c,flow_matching,0.3,2.0,36,172
50,remove,1.0,c,,Cc1cr@l,C1cr@l,6,remove c from position 1,flow_matching,0.3,2.0,36,172
51,replace,1.0,c,1,C1cr@l,Cccr@l,6,replace 1 at position 1 with c,flow_matching,0.3,2.0,36,172
52,add,5.0,2,,Cccr@l,Cccr@2l,7,add 2 at position 5,flow_matching,0.3,2.0,36,172
53,replace,0.0,r,C,Cccr@2l,rccr@2l,7,replace C at position 0 with r,flow_matching,0.3,2.0,36,172
54,remove,6.0,l,,rccr@2l,rccr@2,6,remove l from position 6,flow_matching,0.3,2.0,36,172
55,remove,3.0,r,,rccr@2,rcc@2,5,remove r from position 3,flow_matching,0.3,2.0,36,172
56,replace,1.0,(,c,rcc@2,r(c@2,5,replace c at position 1 with (,flow_matching,0.3,2.0,36,172
57,add,1.0,r,,r(c@2,rr(c@2,6,add r at position 1,flow_matching,0.3,2.0,36,172
58,add,6.0,+,,rr(c@2,rr(c@2+,7,add + at position 6,flow_matching,0.3,2.0,36,172
59,replace,4.0,F,@,rr(c@2+,rr(cF2+,7,replace @ at position 4 with F,flow_matching,0.3,2.0,36,172
60,add,7.0,B,,rr(cF2+,rr(cF2+B,8,add B at position 7,flow_matching,0.3,2.0,36,172
61,replace,1.0,n,r,rr(cF2+B,rn(cF2+B,8,replace r at position 1 with n,flow_matching,0.3,2.0,36,172
62,replace,6.0,N,+,rn(cF2+B,rn(cF2NB,8,replace + at position 6 with N,flow_matching,0.3,2.0,36,172
63,remove,0.0,r,,rn(cF2NB,n(cF2NB,7,remove r from position 0,flow_matching,0.3,2.0,36,172
64,remove,1.0,(,,n(cF2NB,ncF2NB,6,remove ( from position 1,flow_matching,0.3,2.0,36,172
65,add,3.0,[,,ncF2NB,ncF[2NB,7,add [ at position 3,flow_matching,0.3,2.0,36,172
66,replace,0.0,C,n,ncF[2NB,CcF[2NB,7,replace n at position 0 with C,flow_matching,0.3,2.0,36,172
67,replace,2.0,1,F,CcF[2NB,Cc1[2NB,7,replace F at position 2 with 1,flow_matching,0.3,2.0,36,172
68,replace,3.0,c,[,Cc1[2NB,Cc1c2NB,7,replace [ at position 3 with c,flow_matching,0.3,2.0,36,172
69,add,2.0,B,,Cc1c2NB,CcB1c2NB,8,add B at position 2,flow_matching,0.3,2.0,36,172
70,remove,1.0,c,,CcB1c2NB,CB1c2NB,7,remove c from position 1,flow_matching,0.3,2.0,36,172
71,replace,0.0,r,C,CB1c2NB,rB1c2NB,7,replace C at position 0 with r,flow_matching,0.3,2.0,36,172
72,add,0.0,\,,rB1c2NB,\rB1c2NB,8,add \ at position 0,flow_matching,0.3,2.0,36,172
73,add,8.0,B,,\rB1c2NB,\rB1c2NBB,9,add B at position 8,flow_matching,0.3,2.0,36,172
74,add,1.0,H,,\rB1c2NBB,\HrB1c2NBB,10,add H at position 1,flow_matching,0.3,2.0,36,172
75,replace,0.0,C,\,\HrB1c2NBB,CHrB1c2NBB,10,replace \ at position 0 with C,flow_matching,0.3,2.0,36,172
76,replace,7.0,+,N,CHrB1c2NBB,CHrB1c2+BB,10,replace N at position 7 with +,flow_matching,0.3,2.0,36,172
77,remove,4.0,1,,CHrB1c2+BB,CHrBc2+BB,9,remove 1 from position 4,flow_matching,0.3,2.0,36,172
78,add,9.0,4,,CHrBc2+BB,CHrBc2+BB4,10,add 4 at position 9,flow_matching,0.3,2.0,36,172
79,add,10.0,o,,CHrBc2+BB4,CHrBc2+BB4o,11,add o at position 10,flow_matching,0.3,2.0,36,172
80,remove,7.0,B,,CHrBc2+BB4o,CHrBc2+B4o,10,remove B from position 7,flow_matching,0.3,2.0,36,172
81,remove,2.0,r,,CHrBc2+B4o,CHBc2+B4o,9,remove r from position 2,flow_matching,0.3,2.0,36,172
82,replace,8.0,c,o,CHBc2+B4o,CHBc2+B4c,9,replace o at position 8 with c,flow_matching,0.3,2.0,36,172
83,replace,3.0,C,c,CHBc2+B4c,CHBC2+B4c,9,replace c at position 3 with C,flow_matching,0.3,2.0,36,172
84,add,3.0,/,,CHBC2+B4c,CHB/C2+B4c,10,add / at position 3,flow_matching,0.3,2.0,36,172
85,replace,4.0,c,C,CHB/C2+B4c,CHB/c2+B4c,10,replace C at position 4 with c,flow_matching,0.3,2.0,36,172
86,replace,5.0,o,2,CHB/c2+B4c,CHB/co+B4c,10,replace 2 at position 5 with o,flow_matching,0.3,2.0,36,172
87,add,3.0,n,,CHB/co+B4c,CHBn/co+B4c,11,add n at position 3,flow_matching,0.3,2.0,36,172
88,replace,1.0,c,H,CHBn/co+B4c,CcBn/co+B4c,11,replace H at position 1 with c,flow_matching,0.3,2.0,36,172
89,add,10.0,2,,CcBn/co+B4c,CcBn/co+B42c,12,add 2 at position 10,flow_matching,0.3,2.0,36,172
90,add,12.0,[,,CcBn/co+B42c,CcBn/co+B42c[,13,add [ at position 12,flow_matching,0.3,2.0,36,172
91,remove,3.0,n,,CcBn/co+B42c[,CcB/co+B42c[,12,remove n from position 3,flow_matching,0.3,2.0,36,172
92,replace,2.0,1,B,CcB/co+B42c[,Cc1/co+B42c[,12,replace B at position 2 with 1,flow_matching,0.3,2.0,36,172
93,replace,11.0,1,[,Cc1/co+B42c[,Cc1/co+B42c1,12,replace [ at position 11 with 1,flow_matching,0.3,2.0,36,172
94,replace,4.0,s,c,Cc1/co+B42c1,Cc1/so+B42c1,12,replace c at position 4 with s,flow_matching,0.3,2.0,36,172
95,remove,10.0,c,,Cc1/so+B42c1,Cc1/so+B421,11,remove c from position 10,flow_matching,0.3,2.0,36,172
96,replace,10.0,/,1,Cc1/so+B421,Cc1/so+B42/,11,replace 1 at position 10 with /,flow_matching,0.3,2.0,36,172
97,replace,3.0,c,/,Cc1/so+B42/,Cc1cso+B42/,11,replace / at position 3 with c,flow_matching,0.3,2.0,36,172
98,add,7.0,(,,Cc1cso+B42/,Cc1cso+(B42/,12,add ( at position 7,flow_matching,0.3,2.0,36,172
99,add,10.0,I,,Cc1cso+(B42/,Cc1cso+(B4I2/,13,add I at position 10,flow_matching,0.3,2.0,36,172
100,add,7.0,F,,Cc1cso+(B4I2/,Cc1cso+F(B4I2/,14,add F at position 7,flow_matching,0.3,2.0,36,172
101,replace,7.0,H,F,Cc1cso+F(B4I2/,Cc1cso+H(B4I2/,14,replace F at position 7 with H,flow_matching,0.3,2.0,36,172
102,add,3.0,),,Cc1cso+H(B4I2/,Cc1)cso+H(B4I2/,15,add ) at position 3,flow_matching,0.3,2.0,36,172
103,replace,3.0,c,),Cc1)cso+H(B4I2/,Cc1ccso+H(B4I2/,15,replace ) at position 3 with c,flow_matching,0.3,2.0,36,172
104,add,12.0,5,,Cc1ccso+H(B4I2/,Cc1ccso+H(B45I2/,16,add 5 at position 12,flow_matching,0.3,2.0,36,172
105,replace,5.0,(,s,Cc1ccso+H(B45I2/,Cc1cc(o+H(B45I2/,16,replace s at position 5 with (,flow_matching,0.3,2.0,36,172
106,add,11.0,@,,Cc1cc(o+H(B45I2/,Cc1cc(o+H(B@45I2/,17,add @ at position 11,flow_matching,0.3,2.0,36,172
107,add,3.0,F,,Cc1cc(o+H(B@45I2/,Cc1Fcc(o+H(B@45I2/,18,add F at position 3,flow_matching,0.3,2.0,36,172
108,add,16.0,c,,Cc1Fcc(o+H(B@45I2/,Cc1Fcc(o+H(B@45Ic2/,19,add c at position 16,flow_matching,0.3,2.0,36,172
109,replace,3.0,c,F,Cc1Fcc(o+H(B@45Ic2/,Cc1ccc(o+H(B@45Ic2/,19,replace F at position 3 with c,flow_matching,0.3,2.0,36,172
110,remove,7.0,o,,Cc1ccc(o+H(B@45Ic2/,Cc1ccc(+H(B@45Ic2/,18,remove o from position 7,flow_matching,0.3,2.0,36,172
111,remove,2.0,1,,Cc1ccc(+H(B@45Ic2/,Ccccc(+H(B@45Ic2/,17,remove 1 from position 2,flow_matching,0.3,2.0,36,172
112,remove,16.0,/,,Ccccc(+H(B@45Ic2/,Ccccc(+H(B@45Ic2,16,remove / from position 16,flow_matching,0.3,2.0,36,172
113,replace,9.0,#,B,Ccccc(+H(B@45Ic2,Ccccc(+H(#@45Ic2,16,replace B at position 9 with #,flow_matching,0.3,2.0,36,172
114,add,1.0,[,,Ccccc(+H(#@45Ic2,C[cccc(+H(#@45Ic2,17,add [ at position 1,flow_matching,0.3,2.0,36,172
115,replace,1.0,c,[,C[cccc(+H(#@45Ic2,Cccccc(+H(#@45Ic2,17,replace [ at position 1 with c,flow_matching,0.3,2.0,36,172
116,remove,0.0,C,,Cccccc(+H(#@45Ic2,ccccc(+H(#@45Ic2,16,remove C from position 0,flow_matching,0.3,2.0,36,172
117,replace,15.0,[,2,ccccc(+H(#@45Ic2,ccccc(+H(#@45Ic[,16,replace 2 at position 15 with [,flow_matching,0.3,2.0,36,172
118,add,8.0,@,,ccccc(+H(#@45Ic[,ccccc(+H@(#@45Ic[,17,add @ at position 8,flow_matching,0.3,2.0,36,172
119,add,13.0,4,,ccccc(+H@(#@45Ic[,ccccc(+H@(#@445Ic[,18,add 4 at position 13,flow_matching,0.3,2.0,36,172
120,replace,0.0,C,c,ccccc(+H@(#@445Ic[,Ccccc(+H@(#@445Ic[,18,replace c at position 0 with C,flow_matching,0.3,2.0,36,172
121,add,3.0,5,,Ccccc(+H@(#@445Ic[,Ccc5cc(+H@(#@445Ic[,19,add 5 at position 3,flow_matching,0.3,2.0,36,172
122,add,2.0,/,,Ccc5cc(+H@(#@445Ic[,Cc/c5cc(+H@(#@445Ic[,20,add / at position 2,flow_matching,0.3,2.0,36,172
123,replace,9.0,I,H,Cc/c5cc(+H@(#@445Ic[,Cc/c5cc(+I@(#@445Ic[,20,replace H at position 9 with I,flow_matching,0.3,2.0,36,172
124,replace,2.0,1,/,Cc/c5cc(+I@(#@445Ic[,Cc1c5cc(+I@(#@445Ic[,20,replace / at position 2 with 1,flow_matching,0.3,2.0,36,172
125,add,15.0,S,,Cc1c5cc(+I@(#@445Ic[,Cc1c5cc(+I@(#@4S45Ic[,21,add S at position 15,flow_matching,0.3,2.0,36,172
126,add,16.0,#,,Cc1c5cc(+I@(#@4S45Ic[,Cc1c5cc(+I@(#@4S#45Ic[,22,add # at position 16,flow_matching,0.3,2.0,36,172
127,replace,4.0,c,5,Cc1c5cc(+I@(#@4S#45Ic[,Cc1cccc(+I@(#@4S#45Ic[,22,replace 5 at position 4 with c,flow_matching,0.3,2.0,36,172
128,remove,20.0,c,,Cc1cccc(+I@(#@4S#45Ic[,Cc1cccc(+I@(#@4S#45I[,21,remove c from position 20,flow_matching,0.3,2.0,36,172
129,replace,5.0,],c,Cc1cccc(+I@(#@4S#45I[,Cc1cc]c(+I@(#@4S#45I[,21,replace c at position 5 with ],flow_matching,0.3,2.0,36,172
130,add,0.0,s,,Cc1cc]c(+I@(#@4S#45I[,sCc1cc]c(+I@(#@4S#45I[,22,add s at position 0,flow_matching,0.3,2.0,36,172
131,remove,12.0,(,,sCc1cc]c(+I@(#@4S#45I[,sCc1cc]c(+I@#@4S#45I[,21,remove ( from position 12,flow_matching,0.3,2.0,36,172
132,replace,0.0,C,s,sCc1cc]c(+I@#@4S#45I[,CCc1cc]c(+I@#@4S#45I[,21,replace s at position 0 with C,flow_matching,0.3,2.0,36,172
133,add,0.0,],,CCc1cc]c(+I@#@4S#45I[,]CCc1cc]c(+I@#@4S#45I[,22,add ] at position 0,flow_matching,0.3,2.0,36,172
134,add,21.0,c,,]CCc1cc]c(+I@#@4S#45I[,]CCc1cc]c(+I@#@4S#45Ic[,23,add c at position 21,flow_matching,0.3,2.0,36,172
135,replace,0.0,C,],]CCc1cc]c(+I@#@4S#45Ic[,CCCc1cc]c(+I@#@4S#45Ic[,23,replace ] at position 0 with C,flow_matching,0.3,2.0,36,172
136,remove,6.0,c,,CCCc1cc]c(+I@#@4S#45Ic[,CCCc1c]c(+I@#@4S#45Ic[,22,remove c from position 6,flow_matching,0.3,2.0,36,172
137,remove,21.0,[,,CCCc1c]c(+I@#@4S#45Ic[,CCCc1c]c(+I@#@4S#45Ic,21,remove [ from position 21,flow_matching,0.3,2.0,36,172
138,add,12.0,S,,CCCc1c]c(+I@#@4S#45Ic,CCCc1c]c(+I@S#@4S#45Ic,22,add S at position 12,flow_matching,0.3,2.0,36,172
139,replace,1.0,c,C,CCCc1c]c(+I@S#@4S#45Ic,CcCc1c]c(+I@S#@4S#45Ic,22,replace C at position 1 with c,flow_matching,0.3,2.0,36,172
140,replace,2.0,1,C,CcCc1c]c(+I@S#@4S#45Ic,Cc1c1c]c(+I@S#@4S#45Ic,22,replace C at position 2 with 1,flow_matching,0.3,2.0,36,172
141,replace,4.0,c,1,Cc1c1c]c(+I@S#@4S#45Ic,Cc1ccc]c(+I@S#@4S#45Ic,22,replace 1 at position 4 with c,flow_matching,0.3,2.0,36,172
142,replace,5.0,(,c,Cc1ccc]c(+I@S#@4S#45Ic,Cc1cc(]c(+I@S#@4S#45Ic,22,replace c at position 5 with (,flow_matching,0.3,2.0,36,172
143,replace,6.0,F,],Cc1cc(]c(+I@S#@4S#45Ic,Cc1cc(Fc(+I@S#@4S#45Ic,22,replace ] at position 6 with F,flow_matching,0.3,2.0,36,172
144,replace,7.0,),c,Cc1cc(Fc(+I@S#@4S#45Ic,Cc1cc(F)(+I@S#@4S#45Ic,22,replace c at position 7 with ),flow_matching,0.3,2.0,36,172
145,replace,8.0,c,(,Cc1cc(F)(+I@S#@4S#45Ic,Cc1cc(F)c+I@S#@4S#45Ic,22,replace ( at position 8 with c,flow_matching,0.3,2.0,36,172
146,replace,9.0,c,+,Cc1cc(F)c+I@S#@4S#45Ic,Cc1cc(F)ccI@S#@4S#45Ic,22,replace + at position 9 with c,flow_matching,0.3,2.0,36,172
147,replace,10.0,c,I,Cc1cc(F)ccI@S#@4S#45Ic,Cc1cc(F)ccc@S#@4S#45Ic,22,replace I at position 10 with c,flow_matching,0.3,2.0,36,172
148,replace,11.0,1,@,Cc1cc(F)ccc@S#@4S#45Ic,Cc1cc(F)ccc1S#@4S#45Ic,22,replace @ at position 11 with 1,flow_matching,0.3,2.0,36,172
149,replace,12.0,N,S,Cc1cc(F)ccc1S#@4S#45Ic,Cc1cc(F)ccc1N#@4S#45Ic,22,replace S at position 12 with N,flow_matching,0.3,2.0,36,172
150,replace,13.0,C,#,Cc1cc(F)ccc1N#@4S#45Ic,Cc1cc(F)ccc1NC@4S#45Ic,22,replace # at position 13 with C,flow_matching,0.3,2.0,36,172
151,replace,14.0,(,@,Cc1cc(F)ccc1NC@4S#45Ic,Cc1cc(F)ccc1NC(4S#45Ic,22,replace @ at position 14 with (,flow_matching,0.3,2.0,36,172
152,replace,15.0,=,4,Cc1cc(F)ccc1NC(4S#45Ic,Cc1cc(F)ccc1NC(=S#45Ic,22,replace 4 at position 15 with =,flow_matching,0.3,2.0,36,172
153,replace,16.0,O,S,Cc1cc(F)ccc1NC(=S#45Ic,Cc1cc(F)ccc1NC(=O#45Ic,22,replace S at position 16 with O,flow_matching,0.3,2.0,36,172
154,replace,17.0,),#,Cc1cc(F)ccc1NC(=O#45Ic,Cc1cc(F)ccc1NC(=O)45Ic,22,replace # at position 17 with ),flow_matching,0.3,2.0,36,172
155,replace,18.0,C,4,Cc1cc(F)ccc1NC(=O)45Ic,Cc1cc(F)ccc1NC(=O)C5Ic,22,replace 4 at position 18 with C,flow_matching,0.3,2.0,36,172
156,replace,19.0,O,5,Cc1cc(F)ccc1NC(=O)C5Ic,Cc1cc(F)ccc1NC(=O)COIc,22,replace 5 at position 19 with O,flow_matching,0.3,2.0,36,172
157,replace,20.0,c,I,Cc1cc(F)ccc1NC(=O)COIc,Cc1cc(F)ccc1NC(=O)COcc,22,replace I at position 20 with c,flow_matching,0.3,2.0,36,172
158,replace,21.0,1,c,Cc1cc(F)ccc1NC(=O)COcc,Cc1cc(F)ccc1NC(=O)COc1,22,replace c at position 21 with 1,flow_matching,0.3,2.0,36,172
159,add,22.0,c,,Cc1cc(F)ccc1NC(=O)COc1,Cc1cc(F)ccc1NC(=O)COc1c,23,add c at position 22,flow_matching,0.3,2.0,36,172
160,add,23.0,c,,Cc1cc(F)ccc1NC(=O)COc1c,Cc1cc(F)ccc1NC(=O)COc1cc,24,add c at position 23,flow_matching,0.3,2.0,36,172
161,add,24.0,c,,Cc1cc(F)ccc1NC(=O)COc1cc,Cc1cc(F)ccc1NC(=O)COc1ccc,25,add c at position 24,flow_matching,0.3,2.0,36,172
162,add,25.0,2,,Cc1cc(F)ccc1NC(=O)COc1ccc,Cc1cc(F)ccc1NC(=O)COc1ccc2,26,add 2 at position 25,flow_matching,0.3,2.0,36,172
163,add,26.0,c,,Cc1cc(F)ccc1NC(=O)COc1ccc2,Cc1cc(F)ccc1NC(=O)COc1ccc2c,27,add c at position 26,flow_matching,0.3,2.0,36,172
164,add,27.0,(,,Cc1cc(F)ccc1NC(=O)COc1ccc2c,Cc1cc(F)ccc1NC(=O)COc1ccc2c(,28,add ( at position 27,flow_matching,0.3,2.0,36,172
165,add,28.0,c,,Cc1cc(F)ccc1NC(=O)COc1ccc2c(,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c,29,add c at position 28,flow_matching,0.3,2.0,36,172
166,add,29.0,1,,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1,30,add 1 at position 29,flow_matching,0.3,2.0,36,172
167,add,30.0,),,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1),31,add ) at position 30,flow_matching,0.3,2.0,36,172
168,add,31.0,C,,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1),Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1)C,32,add C at position 31,flow_matching,0.3,2.0,36,172
169,add,32.0,C,,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1)C,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1)CC,33,add C at position 32,flow_matching,0.3,2.0,36,172
170,add,33.0,C,,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1)CC,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1)CCC,34,add C at position 33,flow_matching,0.3,2.0,36,172
171,add,34.0,2,,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1)CCC,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1)CCC2,35,add 2 at position 34,flow_matching,0.3,2.0,36,172
172,add,35.0,"
",,Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1)CCC2,"Cc1cc(F)ccc1NC(=O)COc1ccc2c(c1)CCC2
",36,"add 
 at position 35",flow_matching,0.3,2.0,36,172
