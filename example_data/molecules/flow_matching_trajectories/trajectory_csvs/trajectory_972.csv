step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,41,201
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,41,201
2,add,0.0,N,,C,NC,2,add N at position 0,flow_matching,0.3,2.0,41,201
3,add,0.0,4,,NC,4NC,3,add 4 at position 0,flow_matching,0.3,2.0,41,201
4,replace,0.0,C,4,4NC,CNC,3,replace 4 at position 0 with C,flow_matching,0.3,2.0,41,201
5,add,2.0,5,,CNC,CN5C,4,add 5 at position 2,flow_matching,0.3,2.0,41,201
6,replace,2.0,7,5,<PERSON>N5<PERSON>,CN7C,4,replace 5 at position 2 with 7,flow_matching,0.3,2.0,41,201
7,replace,1.0,[,N,CN7<PERSON>,C[7C,4,replace N at position 1 with [,flow_matching,0.3,2.0,41,201
8,replace,1.0,\,[,C[7C,C\7C,4,replace [ at position 1 with \,flow_matching,0.3,2.0,41,201
9,add,2.0,N,,C\7C,C\N7C,5,add N at position 2,flow_matching,0.3,2.0,41,201
10,add,1.0,3,,C\N7C,C3\N7C,6,add 3 at position 1,flow_matching,0.3,2.0,41,201
11,replace,5.0,5,C,C3\N7C,C3\N75,6,replace C at position 5 with 5,flow_matching,0.3,2.0,41,201
12,replace,1.0,[,3,C3\N75,C[\N75,6,replace 3 at position 1 with [,flow_matching,0.3,2.0,41,201
13,remove,5.0,5,,C[\N75,C[\N7,5,remove 5 from position 5,flow_matching,0.3,2.0,41,201
14,replace,2.0,C,\,C[\N7,C[CN7,5,replace \ at position 2 with C,flow_matching,0.3,2.0,41,201
15,replace,3.0,@,N,C[CN7,C[C@7,5,replace N at position 3 with @,flow_matching,0.3,2.0,41,201
16,add,0.0,2,,C[C@7,2C[C@7,6,add 2 at position 0,flow_matching,0.3,2.0,41,201
17,replace,4.0,B,@,2C[C@7,2C[CB7,6,replace @ at position 4 with B,flow_matching,0.3,2.0,41,201
18,replace,0.0,@,2,2C[CB7,@C[CB7,6,replace 2 at position 0 with @,flow_matching,0.3,2.0,41,201
19,replace,0.0,+,@,@C[CB7,+C[CB7,6,replace @ at position 0 with +,flow_matching,0.3,2.0,41,201
20,replace,3.0,1,C,+C[CB7,+C[1B7,6,replace C at position 3 with 1,flow_matching,0.3,2.0,41,201
21,replace,0.0,C,+,+C[1B7,CC[1B7,6,replace + at position 0 with C,flow_matching,0.3,2.0,41,201
22,remove,2.0,[,,CC[1B7,CC1B7,5,remove [ from position 2,flow_matching,0.3,2.0,41,201
23,replace,1.0,[,C,CC1B7,C[1B7,5,replace C at position 1 with [,flow_matching,0.3,2.0,41,201
24,remove,2.0,1,,C[1B7,C[B7,4,remove 1 from position 2,flow_matching,0.3,2.0,41,201
25,replace,0.0,4,C,C[B7,4[B7,4,replace C at position 0 with 4,flow_matching,0.3,2.0,41,201
26,replace,3.0,I,7,4[B7,4[BI,4,replace 7 at position 3 with I,flow_matching,0.3,2.0,41,201
27,replace,0.0,C,4,4[BI,C[BI,4,replace 4 at position 0 with C,flow_matching,0.3,2.0,41,201
28,replace,2.0,C,B,C[BI,C[CI,4,replace B at position 2 with C,flow_matching,0.3,2.0,41,201
29,replace,0.0,),C,C[CI,)[CI,4,replace C at position 0 with ),flow_matching,0.3,2.0,41,201
30,add,0.0,@,,)[CI,@)[CI,5,add @ at position 0,flow_matching,0.3,2.0,41,201
31,add,1.0,],,@)[CI,@])[CI,6,add ] at position 1,flow_matching,0.3,2.0,41,201
32,replace,0.0,C,@,@])[CI,C])[CI,6,replace @ at position 0 with C,flow_matching,0.3,2.0,41,201
33,replace,2.0,S,),C])[CI,C]S[CI,6,replace ) at position 2 with S,flow_matching,0.3,2.0,41,201
34,replace,1.0,[,],C]S[CI,C[S[CI,6,replace ] at position 1 with [,flow_matching,0.3,2.0,41,201
35,replace,0.0,[,C,C[S[CI,[[S[CI,6,replace C at position 0 with [,flow_matching,0.3,2.0,41,201
36,add,1.0,),,[[S[CI,[)[S[CI,7,add ) at position 1,flow_matching,0.3,2.0,41,201
37,replace,2.0,4,[,[)[S[CI,[)4S[CI,7,replace [ at position 2 with 4,flow_matching,0.3,2.0,41,201
38,add,6.0,4,,[)4S[CI,[)4S[C4I,8,add 4 at position 6,flow_matching,0.3,2.0,41,201
39,remove,5.0,C,,[)4S[C4I,[)4S[4I,7,remove C from position 5,flow_matching,0.3,2.0,41,201
40,remove,4.0,[,,[)4S[4I,[)4S4I,6,remove [ from position 4,flow_matching,0.3,2.0,41,201
41,add,4.0,=,,[)4S4I,[)4S=4I,7,add = at position 4,flow_matching,0.3,2.0,41,201
42,replace,6.0,6,I,[)4S=4I,[)4S=46,7,replace I at position 6 with 6,flow_matching,0.3,2.0,41,201
43,add,7.0,B,,[)4S=46,[)4S=46B,8,add B at position 7,flow_matching,0.3,2.0,41,201
44,replace,4.0,2,=,[)4S=46B,[)4S246B,8,replace = at position 4 with 2,flow_matching,0.3,2.0,41,201
45,replace,1.0,r,),[)4S246B,[r4S246B,8,replace ) at position 1 with r,flow_matching,0.3,2.0,41,201
46,replace,0.0,C,[,[r4S246B,Cr4S246B,8,replace [ at position 0 with C,flow_matching,0.3,2.0,41,201
47,replace,1.0,[,r,Cr4S246B,C[4S246B,8,replace r at position 1 with [,flow_matching,0.3,2.0,41,201
48,remove,7.0,B,,C[4S246B,C[4S246,7,remove B from position 7,flow_matching,0.3,2.0,41,201
49,replace,2.0,C,4,C[4S246,C[CS246,7,replace 4 at position 2 with C,flow_matching,0.3,2.0,41,201
50,replace,3.0,@,S,C[CS246,C[C@246,7,replace S at position 3 with @,flow_matching,0.3,2.0,41,201
51,add,7.0,1,,C[C@246,C[C@2461,8,add 1 at position 7,flow_matching,0.3,2.0,41,201
52,remove,6.0,6,,C[C@2461,C[C@241,7,remove 6 from position 6,flow_matching,0.3,2.0,41,201
53,remove,4.0,2,,C[C@241,C[C@41,6,remove 2 from position 4,flow_matching,0.3,2.0,41,201
54,replace,2.0,I,C,C[C@41,C[I@41,6,replace C at position 2 with I,flow_matching,0.3,2.0,41,201
55,add,3.0,5,,C[I@41,C[I5@41,7,add 5 at position 3,flow_matching,0.3,2.0,41,201
56,add,3.0,2,,C[I5@41,C[I25@41,8,add 2 at position 3,flow_matching,0.3,2.0,41,201
57,add,4.0,@,,C[I25@41,C[I2@5@41,9,add @ at position 4,flow_matching,0.3,2.0,41,201
58,replace,2.0,C,I,C[I2@5@41,C[C2@5@41,9,replace I at position 2 with C,flow_matching,0.3,2.0,41,201
59,replace,3.0,@,2,C[C2@5@41,C[C@@5@41,9,replace 2 at position 3 with @,flow_matching,0.3,2.0,41,201
60,replace,4.0,H,@,C[C@@5@41,C[C@H5@41,9,replace @ at position 4 with H,flow_matching,0.3,2.0,41,201
61,add,8.0,F,,C[C@H5@41,C[C@H5@4F1,10,add F at position 8,flow_matching,0.3,2.0,41,201
62,add,7.0,S,,C[C@H5@4F1,C[C@H5@S4F1,11,add S at position 7,flow_matching,0.3,2.0,41,201
63,replace,5.0,],5,C[C@H5@S4F1,C[C@H]@S4F1,11,replace 5 at position 5 with ],flow_matching,0.3,2.0,41,201
64,replace,6.0,4,@,C[C@H]@S4F1,C[C@H]4S4F1,11,replace @ at position 6 with 4,flow_matching,0.3,2.0,41,201
65,remove,9.0,F,,C[C@H]4S4F1,C[C@H]4S41,10,remove F from position 9,flow_matching,0.3,2.0,41,201
66,replace,5.0,+,],C[C@H]4S41,C[C@H+4S41,10,replace ] at position 5 with +,flow_matching,0.3,2.0,41,201
67,replace,5.0,],+,C[C@H+4S41,C[C@H]4S41,10,replace + at position 5 with ],flow_matching,0.3,2.0,41,201
68,replace,6.0,(,4,C[C@H]4S41,C[C@H](S41,10,replace 4 at position 6 with (,flow_matching,0.3,2.0,41,201
69,replace,7.0,[,S,C[C@H](S41,C[C@H]([41,10,replace S at position 7 with [,flow_matching,0.3,2.0,41,201
70,add,2.0,(,,C[C@H]([41,C[(C@H]([41,11,add ( at position 2,flow_matching,0.3,2.0,41,201
71,replace,2.0,C,(,C[(C@H]([41,C[CC@H]([41,11,replace ( at position 2 with C,flow_matching,0.3,2.0,41,201
72,remove,5.0,H,,C[CC@H]([41,C[CC@]([41,10,remove H from position 5,flow_matching,0.3,2.0,41,201
73,replace,3.0,@,C,C[CC@]([41,C[C@@]([41,10,replace C at position 3 with @,flow_matching,0.3,2.0,41,201
74,replace,7.0,2,[,C[C@@]([41,C[C@@](241,10,replace [ at position 7 with 2,flow_matching,0.3,2.0,41,201
75,replace,4.0,H,@,C[C@@](241,C[C@H](241,10,replace @ at position 4 with H,flow_matching,0.3,2.0,41,201
76,replace,7.0,[,2,C[C@H](241,C[C@H]([41,10,replace 2 at position 7 with [,flow_matching,0.3,2.0,41,201
77,replace,4.0,c,H,C[C@H]([41,C[C@c]([41,10,replace H at position 4 with c,flow_matching,0.3,2.0,41,201
78,add,3.0,O,,C[C@c]([41,C[CO@c]([41,11,add O at position 3,flow_matching,0.3,2.0,41,201
79,replace,3.0,@,O,C[CO@c]([41,C[C@@c]([41,11,replace O at position 3 with @,flow_matching,0.3,2.0,41,201
80,replace,10.0,],1,C[C@@c]([41,C[C@@c]([4],11,replace 1 at position 10 with ],flow_matching,0.3,2.0,41,201
81,replace,2.0,c,C,C[C@@c]([4],C[c@@c]([4],11,replace C at position 2 with c,flow_matching,0.3,2.0,41,201
82,replace,7.0,6,(,C[c@@c]([4],C[c@@c]6[4],11,replace ( at position 7 with 6,flow_matching,0.3,2.0,41,201
83,replace,2.0,C,c,C[c@@c]6[4],C[C@@c]6[4],11,replace c at position 2 with C,flow_matching,0.3,2.0,41,201
84,add,10.0,2,,C[C@@c]6[4],C[C@@c]6[42],12,add 2 at position 10,flow_matching,0.3,2.0,41,201
85,remove,0.0,C,,C[C@@c]6[42],[C@@c]6[42],11,remove C from position 0,flow_matching,0.3,2.0,41,201
86,remove,10.0,],,[C@@c]6[42],[C@@c]6[42,10,remove ] from position 10,flow_matching,0.3,2.0,41,201
87,replace,1.0,+,C,[C@@c]6[42,[+@@c]6[42,10,replace C at position 1 with +,flow_matching,0.3,2.0,41,201
88,replace,0.0,=,[,[+@@c]6[42,=+@@c]6[42,10,replace [ at position 0 with =,flow_matching,0.3,2.0,41,201
89,replace,0.0,C,=,=+@@c]6[42,C+@@c]6[42,10,replace = at position 0 with C,flow_matching,0.3,2.0,41,201
90,replace,1.0,[,+,C+@@c]6[42,C[@@c]6[42,10,replace + at position 1 with [,flow_matching,0.3,2.0,41,201
91,replace,7.0,N,[,C[@@c]6[42,C[@@c]6N42,10,replace [ at position 7 with N,flow_matching,0.3,2.0,41,201
92,remove,3.0,@,,C[@@c]6N42,C[@c]6N42,9,remove @ from position 3,flow_matching,0.3,2.0,41,201
93,add,4.0,@,,C[@c]6N42,C[@c@]6N42,10,add @ at position 4,flow_matching,0.3,2.0,41,201
94,replace,0.0,l,C,C[@c@]6N42,l[@c@]6N42,10,replace C at position 0 with l,flow_matching,0.3,2.0,41,201
95,add,5.0,5,,l[@c@]6N42,l[@c@5]6N42,11,add 5 at position 5,flow_matching,0.3,2.0,41,201
96,replace,1.0,N,[,l[@c@5]6N42,lN@c@5]6N42,11,replace [ at position 1 with N,flow_matching,0.3,2.0,41,201
97,remove,1.0,N,,lN@c@5]6N42,l@c@5]6N42,10,remove N from position 1,flow_matching,0.3,2.0,41,201
98,replace,2.0,C,c,l@c@5]6N42,l@C@5]6N42,10,replace c at position 2 with C,flow_matching,0.3,2.0,41,201
99,replace,7.0,S,N,l@C@5]6N42,l@C@5]6S42,10,replace N at position 7 with S,flow_matching,0.3,2.0,41,201
100,remove,0.0,l,,l@C@5]6S42,@C@5]6S42,9,remove l from position 0,flow_matching,0.3,2.0,41,201
101,remove,5.0,6,,@C@5]6S42,@C@5]S42,8,remove 6 from position 5,flow_matching,0.3,2.0,41,201
102,add,3.0,o,,@C@5]S42,@C@o5]S42,9,add o at position 3,flow_matching,0.3,2.0,41,201
103,replace,5.0,7,],@C@o5]S42,@C@o57S42,9,replace ] at position 5 with 7,flow_matching,0.3,2.0,41,201
104,add,1.0,o,,@C@o57S42,@oC@o57S42,10,add o at position 1,flow_matching,0.3,2.0,41,201
105,remove,4.0,o,,@oC@o57S42,@oC@57S42,9,remove o from position 4,flow_matching,0.3,2.0,41,201
106,replace,0.0,C,@,@oC@57S42,CoC@57S42,9,replace @ at position 0 with C,flow_matching,0.3,2.0,41,201
107,remove,3.0,@,,CoC@57S42,CoC57S42,8,remove @ from position 3,flow_matching,0.3,2.0,41,201
108,replace,1.0,[,o,CoC57S42,C[C57S42,8,replace o at position 1 with [,flow_matching,0.3,2.0,41,201
109,replace,3.0,@,5,C[C57S42,C[C@7S42,8,replace 5 at position 3 with @,flow_matching,0.3,2.0,41,201
110,remove,5.0,S,,C[C@7S42,C[C@742,7,remove S from position 5,flow_matching,0.3,2.0,41,201
111,replace,2.0,N,C,C[C@742,C[N@742,7,replace C at position 2 with N,flow_matching,0.3,2.0,41,201
112,remove,1.0,[,,C[N@742,CN@742,6,remove [ from position 1,flow_matching,0.3,2.0,41,201
113,remove,5.0,2,,CN@742,CN@74,5,remove 2 from position 5,flow_matching,0.3,2.0,41,201
114,remove,2.0,@,,CN@74,CN74,4,remove @ from position 2,flow_matching,0.3,2.0,41,201
115,remove,1.0,N,,CN74,C74,3,remove N from position 1,flow_matching,0.3,2.0,41,201
116,add,2.0,],,C74,C7]4,4,add ] at position 2,flow_matching,0.3,2.0,41,201
117,replace,3.0,+,4,C7]4,C7]+,4,replace 4 at position 3 with +,flow_matching,0.3,2.0,41,201
118,remove,2.0,],,C7]+,C7+,3,remove ] from position 2,flow_matching,0.3,2.0,41,201
119,remove,0.0,C,,C7+,7+,2,remove C from position 0,flow_matching,0.3,2.0,41,201
120,add,1.0,N,,7+,7N+,3,add N at position 1,flow_matching,0.3,2.0,41,201
121,add,0.0,1,,7N+,17N+,4,add 1 at position 0,flow_matching,0.3,2.0,41,201
122,replace,0.0,C,1,17N+,C7N+,4,replace 1 at position 0 with C,flow_matching,0.3,2.0,41,201
123,replace,1.0,[,7,C7N+,C[N+,4,replace 7 at position 1 with [,flow_matching,0.3,2.0,41,201
124,replace,0.0,=,C,C[N+,=[N+,4,replace C at position 0 with =,flow_matching,0.3,2.0,41,201
125,replace,0.0,C,=,=[N+,C[N+,4,replace = at position 0 with C,flow_matching,0.3,2.0,41,201
126,replace,2.0,C,N,C[N+,C[C+,4,replace N at position 2 with C,flow_matching,0.3,2.0,41,201
127,replace,0.0,l,C,C[C+,l[C+,4,replace C at position 0 with l,flow_matching,0.3,2.0,41,201
128,remove,2.0,C,,l[C+,l[+,3,remove C from position 2,flow_matching,0.3,2.0,41,201
129,replace,0.0,C,l,l[+,C[+,3,replace l at position 0 with C,flow_matching,0.3,2.0,41,201
130,replace,2.0,C,+,C[+,C[C,3,replace + at position 2 with C,flow_matching,0.3,2.0,41,201
131,add,2.0,/,,C[C,C[/C,4,add / at position 2,flow_matching,0.3,2.0,41,201
132,remove,1.0,[,,C[/C,C/C,3,remove [ from position 1,flow_matching,0.3,2.0,41,201
133,replace,1.0,[,/,C/C,C[C,3,replace / at position 1 with [,flow_matching,0.3,2.0,41,201
134,add,0.0,(,,C[C,(C[C,4,add ( at position 0,flow_matching,0.3,2.0,41,201
135,replace,1.0,#,C,(C[C,(#[C,4,replace C at position 1 with #,flow_matching,0.3,2.0,41,201
136,add,0.0,7,,(#[C,7(#[C,5,add 7 at position 0,flow_matching,0.3,2.0,41,201
137,replace,1.0,s,(,7(#[C,7s#[C,5,replace ( at position 1 with s,flow_matching,0.3,2.0,41,201
138,replace,2.0,5,#,7s#[C,7s5[C,5,replace # at position 2 with 5,flow_matching,0.3,2.0,41,201
139,replace,0.0,C,7,7s5[C,Cs5[C,5,replace 7 at position 0 with C,flow_matching,0.3,2.0,41,201
140,add,0.0,O,,Cs5[C,OCs5[C,6,add O at position 0,flow_matching,0.3,2.0,41,201
141,add,6.0,],,OCs5[C,OCs5[C],7,add ] at position 6,flow_matching,0.3,2.0,41,201
142,replace,3.0,#,5,OCs5[C],OCs#[C],7,replace 5 at position 3 with #,flow_matching,0.3,2.0,41,201
143,replace,2.0,F,s,OCs#[C],OCF#[C],7,replace s at position 2 with F,flow_matching,0.3,2.0,41,201
144,replace,0.0,C,O,OCF#[C],CCF#[C],7,replace O at position 0 with C,flow_matching,0.3,2.0,41,201
145,add,2.0,1,,CCF#[C],CC1F#[C],8,add 1 at position 2,flow_matching,0.3,2.0,41,201
146,add,6.0,l,,CC1F#[C],CC1F#[lC],9,add l at position 6,flow_matching,0.3,2.0,41,201
147,replace,4.0,I,#,CC1F#[lC],CC1FI[lC],9,replace # at position 4 with I,flow_matching,0.3,2.0,41,201
148,add,9.0,5,,CC1FI[lC],CC1FI[lC]5,10,add 5 at position 9,flow_matching,0.3,2.0,41,201
149,replace,1.0,[,C,CC1FI[lC]5,C[1FI[lC]5,10,replace C at position 1 with [,flow_matching,0.3,2.0,41,201
150,add,0.0,2,,C[1FI[lC]5,2C[1FI[lC]5,11,add 2 at position 0,flow_matching,0.3,2.0,41,201
151,replace,9.0,C,],2C[1FI[lC]5,2C[1FI[lCC5,11,replace ] at position 9 with C,flow_matching,0.3,2.0,41,201
152,replace,5.0,6,I,2C[1FI[lCC5,2C[1F6[lCC5,11,replace I at position 5 with 6,flow_matching,0.3,2.0,41,201
153,add,11.0,r,,2C[1F6[lCC5,2C[1F6[lCC5r,12,add r at position 11,flow_matching,0.3,2.0,41,201
154,replace,0.0,C,2,2C[1F6[lCC5r,CC[1F6[lCC5r,12,replace 2 at position 0 with C,flow_matching,0.3,2.0,41,201
155,remove,3.0,1,,CC[1F6[lCC5r,CC[F6[lCC5r,11,remove 1 from position 3,flow_matching,0.3,2.0,41,201
156,add,0.0,N,,CC[F6[lCC5r,NCC[F6[lCC5r,12,add N at position 0,flow_matching,0.3,2.0,41,201
157,remove,4.0,F,,NCC[F6[lCC5r,NCC[6[lCC5r,11,remove F from position 4,flow_matching,0.3,2.0,41,201
158,replace,6.0,c,l,NCC[6[lCC5r,NCC[6[cCC5r,11,replace l at position 6 with c,flow_matching,0.3,2.0,41,201
159,remove,8.0,C,,NCC[6[cCC5r,NCC[6[cC5r,10,remove C from position 8,flow_matching,0.3,2.0,41,201
160,remove,6.0,c,,NCC[6[cC5r,NCC[6[C5r,9,remove c from position 6,flow_matching,0.3,2.0,41,201
161,replace,0.0,C,N,NCC[6[C5r,CCC[6[C5r,9,replace N at position 0 with C,flow_matching,0.3,2.0,41,201
162,add,7.0,s,,CCC[6[C5r,CCC[6[Cs5r,10,add s at position 7,flow_matching,0.3,2.0,41,201
163,replace,1.0,[,C,CCC[6[Cs5r,C[C[6[Cs5r,10,replace C at position 1 with [,flow_matching,0.3,2.0,41,201
164,replace,3.0,@,[,C[C[6[Cs5r,C[C@6[Cs5r,10,replace [ at position 3 with @,flow_matching,0.3,2.0,41,201
165,replace,4.0,H,6,C[C@6[Cs5r,C[C@H[Cs5r,10,replace 6 at position 4 with H,flow_matching,0.3,2.0,41,201
166,replace,5.0,],[,C[C@H[Cs5r,C[C@H]Cs5r,10,replace [ at position 5 with ],flow_matching,0.3,2.0,41,201
167,replace,6.0,(,C,C[C@H]Cs5r,C[C@H](s5r,10,replace C at position 6 with (,flow_matching,0.3,2.0,41,201
168,replace,7.0,[,s,C[C@H](s5r,C[C@H]([5r,10,replace s at position 7 with [,flow_matching,0.3,2.0,41,201
169,replace,8.0,N,5,C[C@H]([5r,C[C@H]([Nr,10,replace 5 at position 8 with N,flow_matching,0.3,2.0,41,201
170,replace,9.0,H,r,C[C@H]([Nr,C[C@H]([NH,10,replace r at position 9 with H,flow_matching,0.3,2.0,41,201
171,add,10.0,3,,C[C@H]([NH,C[C@H]([NH3,11,add 3 at position 10,flow_matching,0.3,2.0,41,201
172,add,11.0,+,,C[C@H]([NH3,C[C@H]([NH3+,12,add + at position 11,flow_matching,0.3,2.0,41,201
173,add,12.0,],,C[C@H]([NH3+,C[C@H]([NH3+],13,add ] at position 12,flow_matching,0.3,2.0,41,201
174,add,13.0,),,C[C@H]([NH3+],C[C@H]([NH3+]),14,add ) at position 13,flow_matching,0.3,2.0,41,201
175,add,14.0,[,,C[C@H]([NH3+]),C[C@H]([NH3+])[,15,add [ at position 14,flow_matching,0.3,2.0,41,201
176,add,15.0,C,,C[C@H]([NH3+])[,C[C@H]([NH3+])[C,16,add C at position 15,flow_matching,0.3,2.0,41,201
177,add,16.0,@,,C[C@H]([NH3+])[C,C[C@H]([NH3+])[C@,17,add @ at position 16,flow_matching,0.3,2.0,41,201
178,add,17.0,@,,C[C@H]([NH3+])[C@,C[C@H]([NH3+])[C@@,18,add @ at position 17,flow_matching,0.3,2.0,41,201
179,add,18.0,H,,C[C@H]([NH3+])[C@@,C[C@H]([NH3+])[C@@H,19,add H at position 18,flow_matching,0.3,2.0,41,201
180,add,19.0,],,C[C@H]([NH3+])[C@@H,C[C@H]([NH3+])[C@@H],20,add ] at position 19,flow_matching,0.3,2.0,41,201
181,add,20.0,(,,C[C@H]([NH3+])[C@@H],C[C@H]([NH3+])[C@@H](,21,add ( at position 20,flow_matching,0.3,2.0,41,201
182,add,21.0,C,,C[C@H]([NH3+])[C@@H](,C[C@H]([NH3+])[C@@H](C,22,add C at position 21,flow_matching,0.3,2.0,41,201
183,add,22.0,C,,C[C@H]([NH3+])[C@@H](C,C[C@H]([NH3+])[C@@H](CC,23,add C at position 22,flow_matching,0.3,2.0,41,201
184,add,23.0,(,,C[C@H]([NH3+])[C@@H](CC,C[C@H]([NH3+])[C@@H](CC(,24,add ( at position 23,flow_matching,0.3,2.0,41,201
185,add,24.0,=,,C[C@H]([NH3+])[C@@H](CC(,C[C@H]([NH3+])[C@@H](CC(=,25,add = at position 24,flow_matching,0.3,2.0,41,201
186,add,25.0,O,,C[C@H]([NH3+])[C@@H](CC(=,C[C@H]([NH3+])[C@@H](CC(=O,26,add O at position 25,flow_matching,0.3,2.0,41,201
187,add,26.0,),,C[C@H]([NH3+])[C@@H](CC(=O,C[C@H]([NH3+])[C@@H](CC(=O),27,add ) at position 26,flow_matching,0.3,2.0,41,201
188,add,27.0,[,,C[C@H]([NH3+])[C@@H](CC(=O),C[C@H]([NH3+])[C@@H](CC(=O)[,28,add [ at position 27,flow_matching,0.3,2.0,41,201
189,add,28.0,O,,C[C@H]([NH3+])[C@@H](CC(=O)[,C[C@H]([NH3+])[C@@H](CC(=O)[O,29,add O at position 28,flow_matching,0.3,2.0,41,201
190,add,29.0,-,,C[C@H]([NH3+])[C@@H](CC(=O)[O,C[C@H]([NH3+])[C@@H](CC(=O)[O-,30,add - at position 29,flow_matching,0.3,2.0,41,201
191,add,30.0,],,C[C@H]([NH3+])[C@@H](CC(=O)[O-,C[C@H]([NH3+])[C@@H](CC(=O)[O-],31,add ] at position 30,flow_matching,0.3,2.0,41,201
192,add,31.0,),,C[C@H]([NH3+])[C@@H](CC(=O)[O-],C[C@H]([NH3+])[C@@H](CC(=O)[O-]),32,add ) at position 31,flow_matching,0.3,2.0,41,201
193,add,32.0,c,,C[C@H]([NH3+])[C@@H](CC(=O)[O-]),C[C@H]([NH3+])[C@@H](CC(=O)[O-])c,33,add c at position 32,flow_matching,0.3,2.0,41,201
194,add,33.0,1,,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1,34,add 1 at position 33,flow_matching,0.3,2.0,41,201
195,add,34.0,c,,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1c,35,add c at position 34,flow_matching,0.3,2.0,41,201
196,add,35.0,c,,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1c,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1cc,36,add c at position 35,flow_matching,0.3,2.0,41,201
197,add,36.0,c,,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1cc,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1ccc,37,add c at position 36,flow_matching,0.3,2.0,41,201
198,add,37.0,c,,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1ccc,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1cccc,38,add c at position 37,flow_matching,0.3,2.0,41,201
199,add,38.0,c,,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1cccc,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1ccccc,39,add c at position 38,flow_matching,0.3,2.0,41,201
200,add,39.0,1,,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1ccccc,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1ccccc1,40,add 1 at position 39,flow_matching,0.3,2.0,41,201
201,add,40.0,"
",,C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1ccccc1,"C[C@H]([NH3+])[C@@H](CC(=O)[O-])c1ccccc1
",41,"add 
 at position 40",flow_matching,0.3,2.0,41,201
