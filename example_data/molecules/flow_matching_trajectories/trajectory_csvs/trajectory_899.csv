step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,40,86
1,add,0.0,l,,,l,1,add l at position 0,flow_matching,0.3,2.0,40,86
2,add,0.0,l,,l,ll,2,add l at position 0,flow_matching,0.3,2.0,40,86
3,add,1.0,\,,ll,l\l,3,add \ at position 1,flow_matching,0.3,2.0,40,86
4,remove,2.0,l,,l\l,l\,2,remove l from position 2,flow_matching,0.3,2.0,40,86
5,add,2.0,),,l\,l\),3,add ) at position 2,flow_matching,0.3,2.0,40,86
6,replace,0.0,1,l,l\),1\),3,replace l at position 0 with 1,flow_matching,0.3,2.0,40,86
7,replace,0.0,C,1,1\),C\),3,replace 1 at position 0 with C,flow_matching,0.3,2.0,40,86
8,replace,1.0,r,\,C\),Cr),3,replace \ at position 1 with r,flow_matching,0.3,2.0,40,86
9,remove,2.0,),,Cr),Cr,2,remove ) from position 2,flow_matching,0.3,2.0,40,86
10,replace,1.0,c,r,Cr,Cc,2,replace r at position 1 with c,flow_matching,0.3,2.0,40,86
11,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,40,86
12,add,3.0,5,,Cc1,Cc15,4,add 5 at position 3,flow_matching,0.3,2.0,40,86
13,add,2.0,6,,Cc15,Cc615,5,add 6 at position 2,flow_matching,0.3,2.0,40,86
14,add,0.0,#,,Cc615,#Cc615,6,add # at position 0,flow_matching,0.3,2.0,40,86
15,remove,4.0,1,,#Cc615,#Cc65,5,remove 1 from position 4,flow_matching,0.3,2.0,40,86
16,replace,3.0,(,6,#Cc65,#Cc(5,5,replace 6 at position 3 with (,flow_matching,0.3,2.0,40,86
17,replace,3.0,c,(,#Cc(5,#Ccc5,5,replace ( at position 3 with c,flow_matching,0.3,2.0,40,86
18,replace,0.0,C,#,#Ccc5,CCcc5,5,replace # at position 0 with C,flow_matching,0.3,2.0,40,86
19,replace,1.0,c,C,CCcc5,Cccc5,5,replace C at position 1 with c,flow_matching,0.3,2.0,40,86
20,add,5.0,\,,Cccc5,Cccc5\,6,add \ at position 5,flow_matching,0.3,2.0,40,86
21,replace,3.0,),c,Cccc5\,Ccc)5\,6,replace c at position 3 with ),flow_matching,0.3,2.0,40,86
22,add,3.0,n,,Ccc)5\,Cccn)5\,7,add n at position 3,flow_matching,0.3,2.0,40,86
23,replace,0.0,5,C,Cccn)5\,5ccn)5\,7,replace C at position 0 with 5,flow_matching,0.3,2.0,40,86
24,add,7.0,@,,5ccn)5\,5ccn)5\@,8,add @ at position 7,flow_matching,0.3,2.0,40,86
25,replace,3.0,@,n,5ccn)5\@,5cc@)5\@,8,replace n at position 3 with @,flow_matching,0.3,2.0,40,86
26,replace,0.0,C,5,5cc@)5\@,Ccc@)5\@,8,replace 5 at position 0 with C,flow_matching,0.3,2.0,40,86
27,remove,2.0,c,,Ccc@)5\@,Cc@)5\@,7,remove c from position 2,flow_matching,0.3,2.0,40,86
28,replace,2.0,1,@,Cc@)5\@,Cc1)5\@,7,replace @ at position 2 with 1,flow_matching,0.3,2.0,40,86
29,add,3.0,1,,Cc1)5\@,Cc11)5\@,8,add 1 at position 3,flow_matching,0.3,2.0,40,86
30,replace,1.0,N,c,Cc11)5\@,CN11)5\@,8,replace c at position 1 with N,flow_matching,0.3,2.0,40,86
31,replace,3.0,6,1,CN11)5\@,CN16)5\@,8,replace 1 at position 3 with 6,flow_matching,0.3,2.0,40,86
32,replace,1.0,c,N,CN16)5\@,Cc16)5\@,8,replace N at position 1 with c,flow_matching,0.3,2.0,40,86
33,replace,3.0,c,6,Cc16)5\@,Cc1c)5\@,8,replace 6 at position 3 with c,flow_matching,0.3,2.0,40,86
34,remove,5.0,5,,Cc1c)5\@,Cc1c)\@,7,remove 5 from position 5,flow_matching,0.3,2.0,40,86
35,remove,5.0,\,,Cc1c)\@,Cc1c)@,6,remove \ from position 5,flow_matching,0.3,2.0,40,86
36,add,3.0,H,,Cc1c)@,Cc1Hc)@,7,add H at position 3,flow_matching,0.3,2.0,40,86
37,replace,3.0,c,H,Cc1Hc)@,Cc1cc)@,7,replace H at position 3 with c,flow_matching,0.3,2.0,40,86
38,replace,5.0,=,),Cc1cc)@,Cc1cc=@,7,replace ) at position 5 with =,flow_matching,0.3,2.0,40,86
39,replace,5.0,(,=,Cc1cc=@,Cc1cc(@,7,replace = at position 5 with (,flow_matching,0.3,2.0,40,86
40,replace,1.0,H,c,Cc1cc(@,CH1cc(@,7,replace c at position 1 with H,flow_matching,0.3,2.0,40,86
41,remove,3.0,c,,CH1cc(@,CH1c(@,6,remove c from position 3,flow_matching,0.3,2.0,40,86
42,add,2.0,/,,CH1c(@,CH/1c(@,7,add / at position 2,flow_matching,0.3,2.0,40,86
43,replace,1.0,c,H,CH/1c(@,Cc/1c(@,7,replace H at position 1 with c,flow_matching,0.3,2.0,40,86
44,add,4.0,4,,Cc/1c(@,Cc/14c(@,8,add 4 at position 4,flow_matching,0.3,2.0,40,86
45,add,3.0,\,,Cc/14c(@,Cc/\14c(@,9,add \ at position 3,flow_matching,0.3,2.0,40,86
46,add,8.0,7,,Cc/\14c(@,Cc/\14c(7@,10,add 7 at position 8,flow_matching,0.3,2.0,40,86
47,add,8.0,3,,Cc/\14c(7@,Cc/\14c(37@,11,add 3 at position 8,flow_matching,0.3,2.0,40,86
48,add,3.0,S,,Cc/\14c(37@,Cc/S\14c(37@,12,add S at position 3,flow_matching,0.3,2.0,40,86
49,replace,2.0,1,/,Cc/S\14c(37@,Cc1S\14c(37@,12,replace / at position 2 with 1,flow_matching,0.3,2.0,40,86
50,replace,3.0,c,S,Cc1S\14c(37@,Cc1c\14c(37@,12,replace S at position 3 with c,flow_matching,0.3,2.0,40,86
51,replace,4.0,c,\,Cc1c\14c(37@,Cc1cc14c(37@,12,replace \ at position 4 with c,flow_matching,0.3,2.0,40,86
52,replace,5.0,(,1,Cc1cc14c(37@,Cc1cc(4c(37@,12,replace 1 at position 5 with (,flow_matching,0.3,2.0,40,86
53,replace,6.0,C,4,Cc1cc(4c(37@,Cc1cc(Cc(37@,12,replace 4 at position 6 with C,flow_matching,0.3,2.0,40,86
54,replace,7.0,(,c,Cc1cc(Cc(37@,Cc1cc(C((37@,12,replace c at position 7 with (,flow_matching,0.3,2.0,40,86
55,replace,8.0,=,(,Cc1cc(C((37@,Cc1cc(C(=37@,12,replace ( at position 8 with =,flow_matching,0.3,2.0,40,86
56,replace,9.0,O,3,Cc1cc(C(=37@,Cc1cc(C(=O7@,12,replace 3 at position 9 with O,flow_matching,0.3,2.0,40,86
57,replace,10.0,),7,Cc1cc(C(=O7@,Cc1cc(C(=O)@,12,replace 7 at position 10 with ),flow_matching,0.3,2.0,40,86
58,replace,11.0,N,@,Cc1cc(C(=O)@,Cc1cc(C(=O)N,12,replace @ at position 11 with N,flow_matching,0.3,2.0,40,86
59,add,12.0,N,,Cc1cc(C(=O)N,Cc1cc(C(=O)NN,13,add N at position 12,flow_matching,0.3,2.0,40,86
60,add,13.0,C,,Cc1cc(C(=O)NN,Cc1cc(C(=O)NNC,14,add C at position 13,flow_matching,0.3,2.0,40,86
61,add,14.0,(,,Cc1cc(C(=O)NNC,Cc1cc(C(=O)NNC(,15,add ( at position 14,flow_matching,0.3,2.0,40,86
62,add,15.0,=,,Cc1cc(C(=O)NNC(,Cc1cc(C(=O)NNC(=,16,add = at position 15,flow_matching,0.3,2.0,40,86
63,add,16.0,O,,Cc1cc(C(=O)NNC(=,Cc1cc(C(=O)NNC(=O,17,add O at position 16,flow_matching,0.3,2.0,40,86
64,add,17.0,),,Cc1cc(C(=O)NNC(=O,Cc1cc(C(=O)NNC(=O),18,add ) at position 17,flow_matching,0.3,2.0,40,86
65,add,18.0,c,,Cc1cc(C(=O)NNC(=O),Cc1cc(C(=O)NNC(=O)c,19,add c at position 18,flow_matching,0.3,2.0,40,86
66,add,19.0,2,,Cc1cc(C(=O)NNC(=O)c,Cc1cc(C(=O)NNC(=O)c2,20,add 2 at position 19,flow_matching,0.3,2.0,40,86
67,add,20.0,c,,Cc1cc(C(=O)NNC(=O)c2,Cc1cc(C(=O)NNC(=O)c2c,21,add c at position 20,flow_matching,0.3,2.0,40,86
68,add,21.0,c,,Cc1cc(C(=O)NNC(=O)c2c,Cc1cc(C(=O)NNC(=O)c2cc,22,add c at position 21,flow_matching,0.3,2.0,40,86
69,add,22.0,c,,Cc1cc(C(=O)NNC(=O)c2cc,Cc1cc(C(=O)NNC(=O)c2ccc,23,add c at position 22,flow_matching,0.3,2.0,40,86
70,add,23.0,c,,Cc1cc(C(=O)NNC(=O)c2ccc,Cc1cc(C(=O)NNC(=O)c2cccc,24,add c at position 23,flow_matching,0.3,2.0,40,86
71,add,24.0,3,,Cc1cc(C(=O)NNC(=O)c2cccc,Cc1cc(C(=O)NNC(=O)c2cccc3,25,add 3 at position 24,flow_matching,0.3,2.0,40,86
72,add,25.0,c,,Cc1cc(C(=O)NNC(=O)c2cccc3,Cc1cc(C(=O)NNC(=O)c2cccc3c,26,add c at position 25,flow_matching,0.3,2.0,40,86
73,add,26.0,c,,Cc1cc(C(=O)NNC(=O)c2cccc3c,Cc1cc(C(=O)NNC(=O)c2cccc3cc,27,add c at position 26,flow_matching,0.3,2.0,40,86
74,add,27.0,c,,Cc1cc(C(=O)NNC(=O)c2cccc3cc,Cc1cc(C(=O)NNC(=O)c2cccc3ccc,28,add c at position 27,flow_matching,0.3,2.0,40,86
75,add,28.0,c,,Cc1cc(C(=O)NNC(=O)c2cccc3ccc,Cc1cc(C(=O)NNC(=O)c2cccc3cccc,29,add c at position 28,flow_matching,0.3,2.0,40,86
76,add,29.0,c,,Cc1cc(C(=O)NNC(=O)c2cccc3cccc,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc,30,add c at position 29,flow_matching,0.3,2.0,40,86
77,add,30.0,2,,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc2,31,add 2 at position 30,flow_matching,0.3,2.0,40,86
78,add,31.0,3,,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc2,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23,32,add 3 at position 31,flow_matching,0.3,2.0,40,86
79,add,32.0,),,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23),33,add ) at position 32,flow_matching,0.3,2.0,40,86
80,add,33.0,c,,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23),Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c,34,add c at position 33,flow_matching,0.3,2.0,40,86
81,add,34.0,(,,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(,35,add ( at position 34,flow_matching,0.3,2.0,40,86
82,add,35.0,C,,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C,36,add C at position 35,flow_matching,0.3,2.0,40,86
83,add,36.0,),,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C),37,add ) at position 36,flow_matching,0.3,2.0,40,86
84,add,37.0,o,,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C),Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C)o,38,add o at position 37,flow_matching,0.3,2.0,40,86
85,add,38.0,1,,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C)o,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C)o1,39,add 1 at position 38,flow_matching,0.3,2.0,40,86
86,add,39.0,"
",,Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C)o1,"Cc1cc(C(=O)NNC(=O)c2cccc3ccccc23)c(C)o1
",40,"add 
 at position 39",flow_matching,0.3,2.0,40,86
