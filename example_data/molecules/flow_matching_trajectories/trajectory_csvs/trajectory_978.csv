step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,60,146
1,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,60,146
2,remove,0.0,],,],,0,remove ] from position 0,flow_matching,0.3,2.0,60,146
3,add,0.0,H,,,H,1,add H at position 0,flow_matching,0.3,2.0,60,146
4,add,0.0,1,,H,1H,2,add 1 at position 0,flow_matching,0.3,2.0,60,146
5,remove,0.0,1,,1H,H,1,remove 1 from position 0,flow_matching,0.3,2.0,60,146
6,add,0.0,[,,H,[H,2,add [ at position 0,flow_matching,0.3,2.0,60,146
7,replace,1.0,6,H,[H,[6,2,replace <PERSON> at position 1 with 6,flow_matching,0.3,2.0,60,146
8,replace,1.0,7,6,[6,[7,2,replace 6 at position 1 with 7,flow_matching,0.3,2.0,60,146
9,add,1.0,S,,[7,[S7,3,add S at position 1,flow_matching,0.3,2.0,60,146
10,replace,0.0,C,[,[S7,CS7,3,replace [ at position 0 with C,flow_matching,0.3,2.0,60,146
11,replace,1.0,2,S,CS7,C27,3,replace S at position 1 with 2,flow_matching,0.3,2.0,60,146
12,replace,1.0,O,2,C27,CO7,3,replace 2 at position 1 with O,flow_matching,0.3,2.0,60,146
13,remove,2.0,7,,CO7,CO,2,remove 7 from position 2,flow_matching,0.3,2.0,60,146
14,add,0.0,),,CO,)CO,3,add ) at position 0,flow_matching,0.3,2.0,60,146
15,remove,1.0,C,,)CO,)O,2,remove C from position 1,flow_matching,0.3,2.0,60,146
16,add,1.0,#,,)O,)#O,3,add # at position 1,flow_matching,0.3,2.0,60,146
17,add,1.0,6,,)#O,)6#O,4,add 6 at position 1,flow_matching,0.3,2.0,60,146
18,replace,0.0,O,),)6#O,O6#O,4,replace ) at position 0 with O,flow_matching,0.3,2.0,60,146
19,replace,1.0,),6,O6#O,O)#O,4,replace 6 at position 1 with ),flow_matching,0.3,2.0,60,146
20,replace,0.0,C,O,O)#O,C)#O,4,replace O at position 0 with C,flow_matching,0.3,2.0,60,146
21,remove,3.0,O,,C)#O,C)#,3,remove O from position 3,flow_matching,0.3,2.0,60,146
22,remove,1.0,),,C)#,C#,2,remove ) from position 1,flow_matching,0.3,2.0,60,146
23,remove,1.0,#,,C#,C,1,remove # from position 1,flow_matching,0.3,2.0,60,146
24,add,0.0,/,,C,/C,2,add / at position 0,flow_matching,0.3,2.0,60,146
25,replace,0.0,C,/,/C,CC,2,replace / at position 0 with C,flow_matching,0.3,2.0,60,146
26,add,1.0,O,,CC,COC,3,add O at position 1,flow_matching,0.3,2.0,60,146
27,replace,2.0,@,C,COC,CO@,3,replace C at position 2 with @,flow_matching,0.3,2.0,60,146
28,remove,2.0,@,,CO@,CO,2,remove @ from position 2,flow_matching,0.3,2.0,60,146
29,remove,1.0,O,,CO,C,1,remove O from position 1,flow_matching,0.3,2.0,60,146
30,add,1.0,3,,C,C3,2,add 3 at position 1,flow_matching,0.3,2.0,60,146
31,replace,1.0,O,3,C3,CO,2,replace 3 at position 1 with O,flow_matching,0.3,2.0,60,146
32,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,60,146
33,replace,0.0,C,O,O,C,1,replace O at position 0 with C,flow_matching,0.3,2.0,60,146
34,add,0.0,],,C,]C,2,add ] at position 0,flow_matching,0.3,2.0,60,146
35,add,2.0,],,]C,]C],3,add ] at position 2,flow_matching,0.3,2.0,60,146
36,replace,0.0,C,],]C],CC],3,replace ] at position 0 with C,flow_matching,0.3,2.0,60,146
37,replace,1.0,[,C,CC],C[],3,replace C at position 1 with [,flow_matching,0.3,2.0,60,146
38,replace,1.0,4,[,C[],C4],3,replace [ at position 1 with 4,flow_matching,0.3,2.0,60,146
39,replace,0.0,r,C,C4],r4],3,replace C at position 0 with r,flow_matching,0.3,2.0,60,146
40,remove,1.0,4,,r4],r],2,remove 4 from position 1,flow_matching,0.3,2.0,60,146
41,add,1.0,=,,r],r=],3,add = at position 1,flow_matching,0.3,2.0,60,146
42,remove,1.0,=,,r=],r],2,remove = from position 1,flow_matching,0.3,2.0,60,146
43,replace,1.0,B,],r],rB,2,replace ] at position 1 with B,flow_matching,0.3,2.0,60,146
44,remove,0.0,r,,rB,B,1,remove r from position 0,flow_matching,0.3,2.0,60,146
45,replace,0.0,(,B,B,(,1,replace B at position 0 with (,flow_matching,0.3,2.0,60,146
46,remove,0.0,(,,(,,0,remove ( from position 0,flow_matching,0.3,2.0,60,146
47,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,60,146
48,add,0.0,/,,C,/C,2,add / at position 0,flow_matching,0.3,2.0,60,146
49,add,1.0,=,,/C,/=C,3,add = at position 1,flow_matching,0.3,2.0,60,146
50,replace,0.0,C,/,/=C,C=C,3,replace / at position 0 with C,flow_matching,0.3,2.0,60,146
51,add,0.0,=,,C=C,=C=C,4,add = at position 0,flow_matching,0.3,2.0,60,146
52,add,4.0,o,,=C=C,=C=Co,5,add o at position 4,flow_matching,0.3,2.0,60,146
53,add,5.0,\,,=C=Co,=C=Co\,6,add \ at position 5,flow_matching,0.3,2.0,60,146
54,replace,1.0,@,C,=C=Co\,=@=Co\,6,replace C at position 1 with @,flow_matching,0.3,2.0,60,146
55,replace,2.0,+,=,=@=Co\,=@+Co\,6,replace = at position 2 with +,flow_matching,0.3,2.0,60,146
56,replace,0.0,C,=,=@+Co\,C@+Co\,6,replace = at position 0 with C,flow_matching,0.3,2.0,60,146
57,replace,1.0,O,@,C@+Co\,CO+Co\,6,replace @ at position 1 with O,flow_matching,0.3,2.0,60,146
58,replace,2.0,c,+,CO+Co\,COcCo\,6,replace + at position 2 with c,flow_matching,0.3,2.0,60,146
59,replace,3.0,1,C,COcCo\,COc1o\,6,replace C at position 3 with 1,flow_matching,0.3,2.0,60,146
60,replace,4.0,c,o,COc1o\,COc1c\,6,replace o at position 4 with c,flow_matching,0.3,2.0,60,146
61,remove,4.0,c,,COc1c\,COc1\,5,remove c from position 4,flow_matching,0.3,2.0,60,146
62,replace,3.0,H,1,COc1\,COcH\,5,replace 1 at position 3 with H,flow_matching,0.3,2.0,60,146
63,replace,3.0,1,H,COcH\,COc1\,5,replace H at position 3 with 1,flow_matching,0.3,2.0,60,146
64,replace,4.0,c,\,COc1\,COc1c,5,replace \ at position 4 with c,flow_matching,0.3,2.0,60,146
65,add,5.0,c,,COc1c,COc1cc,6,add c at position 5,flow_matching,0.3,2.0,60,146
66,replace,2.0,(,c,COc1cc,CO(1cc,6,replace c at position 2 with (,flow_matching,0.3,2.0,60,146
67,remove,4.0,c,,CO(1cc,CO(1c,5,remove c from position 4,flow_matching,0.3,2.0,60,146
68,replace,2.0,c,(,CO(1c,COc1c,5,replace ( at position 2 with c,flow_matching,0.3,2.0,60,146
69,add,5.0,c,,COc1c,COc1cc,6,add c at position 5,flow_matching,0.3,2.0,60,146
70,remove,0.0,C,,COc1cc,Oc1cc,5,remove C from position 0,flow_matching,0.3,2.0,60,146
71,replace,0.0,C,O,Oc1cc,Cc1cc,5,replace O at position 0 with C,flow_matching,0.3,2.0,60,146
72,replace,4.0,s,c,Cc1cc,Cc1cs,5,replace c at position 4 with s,flow_matching,0.3,2.0,60,146
73,add,2.0,N,,Cc1cs,CcN1cs,6,add N at position 2,flow_matching,0.3,2.0,60,146
74,remove,2.0,N,,CcN1cs,Cc1cs,5,remove N from position 2,flow_matching,0.3,2.0,60,146
75,add,1.0,r,,Cc1cs,Crc1cs,6,add r at position 1,flow_matching,0.3,2.0,60,146
76,remove,3.0,1,,Crc1cs,Crccs,5,remove 1 from position 3,flow_matching,0.3,2.0,60,146
77,replace,1.0,O,r,Crccs,COccs,5,replace r at position 1 with O,flow_matching,0.3,2.0,60,146
78,replace,4.0,4,s,COccs,COcc4,5,replace s at position 4 with 4,flow_matching,0.3,2.0,60,146
79,add,1.0,@,,COcc4,C@Occ4,6,add @ at position 1,flow_matching,0.3,2.0,60,146
80,replace,1.0,O,@,C@Occ4,COOcc4,6,replace @ at position 1 with O,flow_matching,0.3,2.0,60,146
81,replace,1.0,s,O,COOcc4,CsOcc4,6,replace O at position 1 with s,flow_matching,0.3,2.0,60,146
82,remove,0.0,C,,CsOcc4,sOcc4,5,remove C from position 0,flow_matching,0.3,2.0,60,146
83,replace,0.0,C,s,sOcc4,COcc4,5,replace s at position 0 with C,flow_matching,0.3,2.0,60,146
84,remove,4.0,4,,COcc4,COcc,4,remove 4 from position 4,flow_matching,0.3,2.0,60,146
85,remove,3.0,c,,COcc,COc,3,remove c from position 3,flow_matching,0.3,2.0,60,146
86,add,0.0,F,,COc,FCOc,4,add F at position 0,flow_matching,0.3,2.0,60,146
87,replace,0.0,C,F,FCOc,CCOc,4,replace F at position 0 with C,flow_matching,0.3,2.0,60,146
88,replace,1.0,O,C,CCOc,COOc,4,replace C at position 1 with O,flow_matching,0.3,2.0,60,146
89,replace,2.0,c,O,COOc,COcc,4,replace O at position 2 with c,flow_matching,0.3,2.0,60,146
90,replace,3.0,1,c,COcc,COc1,4,replace c at position 3 with 1,flow_matching,0.3,2.0,60,146
91,add,4.0,c,,COc1,COc1c,5,add c at position 4,flow_matching,0.3,2.0,60,146
92,add,5.0,c,,COc1c,COc1cc,6,add c at position 5,flow_matching,0.3,2.0,60,146
93,add,6.0,(,,COc1cc,COc1cc(,7,add ( at position 6,flow_matching,0.3,2.0,60,146
94,add,7.0,C,,COc1cc(,COc1cc(C,8,add C at position 7,flow_matching,0.3,2.0,60,146
95,add,8.0,),,COc1cc(C,COc1cc(C),9,add ) at position 8,flow_matching,0.3,2.0,60,146
96,add,9.0,c,,COc1cc(C),COc1cc(C)c,10,add c at position 9,flow_matching,0.3,2.0,60,146
97,add,10.0,(,,COc1cc(C)c,COc1cc(C)c(,11,add ( at position 10,flow_matching,0.3,2.0,60,146
98,add,11.0,[,,COc1cc(C)c(,COc1cc(C)c([,12,add [ at position 11,flow_matching,0.3,2.0,60,146
99,add,12.0,C,,COc1cc(C)c([,COc1cc(C)c([C,13,add C at position 12,flow_matching,0.3,2.0,60,146
100,add,13.0,@,,COc1cc(C)c([C,COc1cc(C)c([C@,14,add @ at position 13,flow_matching,0.3,2.0,60,146
101,add,14.0,@,,COc1cc(C)c([C@,COc1cc(C)c([C@@,15,add @ at position 14,flow_matching,0.3,2.0,60,146
102,add,15.0,H,,COc1cc(C)c([C@@,COc1cc(C)c([C@@H,16,add H at position 15,flow_matching,0.3,2.0,60,146
103,add,16.0,],,COc1cc(C)c([C@@H,COc1cc(C)c([C@@H],17,add ] at position 16,flow_matching,0.3,2.0,60,146
104,add,17.0,(,,COc1cc(C)c([C@@H],COc1cc(C)c([C@@H](,18,add ( at position 17,flow_matching,0.3,2.0,60,146
105,add,18.0,C,,COc1cc(C)c([C@@H](,COc1cc(C)c([C@@H](C,19,add C at position 18,flow_matching,0.3,2.0,60,146
106,add,19.0,),,COc1cc(C)c([C@@H](C,COc1cc(C)c([C@@H](C),20,add ) at position 19,flow_matching,0.3,2.0,60,146
107,add,20.0,N,,COc1cc(C)c([C@@H](C),COc1cc(C)c([C@@H](C)N,21,add N at position 20,flow_matching,0.3,2.0,60,146
108,add,21.0,C,,COc1cc(C)c([C@@H](C)N,COc1cc(C)c([C@@H](C)NC,22,add C at position 21,flow_matching,0.3,2.0,60,146
109,add,22.0,2,,COc1cc(C)c([C@@H](C)NC,COc1cc(C)c([C@@H](C)NC2,23,add 2 at position 22,flow_matching,0.3,2.0,60,146
110,add,23.0,C,,COc1cc(C)c([C@@H](C)NC2,COc1cc(C)c([C@@H](C)NC2C,24,add C at position 23,flow_matching,0.3,2.0,60,146
111,add,24.0,C,,COc1cc(C)c([C@@H](C)NC2C,COc1cc(C)c([C@@H](C)NC2CC,25,add C at position 24,flow_matching,0.3,2.0,60,146
112,add,25.0,[,,COc1cc(C)c([C@@H](C)NC2CC,COc1cc(C)c([C@@H](C)NC2CC[,26,add [ at position 25,flow_matching,0.3,2.0,60,146
113,add,26.0,N,,COc1cc(C)c([C@@H](C)NC2CC[,COc1cc(C)c([C@@H](C)NC2CC[N,27,add N at position 26,flow_matching,0.3,2.0,60,146
114,add,27.0,H,,COc1cc(C)c([C@@H](C)NC2CC[N,COc1cc(C)c([C@@H](C)NC2CC[NH,28,add H at position 27,flow_matching,0.3,2.0,60,146
115,add,28.0,+,,COc1cc(C)c([C@@H](C)NC2CC[NH,COc1cc(C)c([C@@H](C)NC2CC[NH+,29,add + at position 28,flow_matching,0.3,2.0,60,146
116,add,29.0,],,COc1cc(C)c([C@@H](C)NC2CC[NH+,COc1cc(C)c([C@@H](C)NC2CC[NH+],30,add ] at position 29,flow_matching,0.3,2.0,60,146
117,add,30.0,(,,COc1cc(C)c([C@@H](C)NC2CC[NH+],COc1cc(C)c([C@@H](C)NC2CC[NH+](,31,add ( at position 30,flow_matching,0.3,2.0,60,146
118,add,31.0,[,,COc1cc(C)c([C@@H](C)NC2CC[NH+](,COc1cc(C)c([C@@H](C)NC2CC[NH+]([,32,add [ at position 31,flow_matching,0.3,2.0,60,146
119,add,32.0,C,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C,33,add C at position 32,flow_matching,0.3,2.0,60,146
120,add,33.0,@,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@,34,add @ at position 33,flow_matching,0.3,2.0,60,146
121,add,34.0,H,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H,35,add H at position 34,flow_matching,0.3,2.0,60,146
122,add,35.0,],,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H],36,add ] at position 35,flow_matching,0.3,2.0,60,146
123,add,36.0,3,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H],COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3,37,add 3 at position 36,flow_matching,0.3,2.0,60,146
124,add,37.0,C,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3C,38,add C at position 37,flow_matching,0.3,2.0,60,146
125,add,38.0,C,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3C,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CC,39,add C at position 38,flow_matching,0.3,2.0,60,146
126,add,39.0,C,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CC,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCC,40,add C at position 39,flow_matching,0.3,2.0,60,146
127,add,40.0,C,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCC,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC,41,add C at position 40,flow_matching,0.3,2.0,60,146
128,add,41.0,[,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[,42,add [ at position 41,flow_matching,0.3,2.0,60,146
129,add,42.0,C,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C,43,add C at position 42,flow_matching,0.3,2.0,60,146
130,add,43.0,@,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@,44,add @ at position 43,flow_matching,0.3,2.0,60,146
131,add,44.0,@,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@,45,add @ at position 44,flow_matching,0.3,2.0,60,146
132,add,45.0,H,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H,46,add H at position 45,flow_matching,0.3,2.0,60,146
133,add,46.0,],,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H],47,add ] at position 46,flow_matching,0.3,2.0,60,146
134,add,47.0,3,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H],COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3,48,add 3 at position 47,flow_matching,0.3,2.0,60,146
135,add,48.0,O,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O,49,add O at position 48,flow_matching,0.3,2.0,60,146
136,add,49.0,),,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O),50,add ) at position 49,flow_matching,0.3,2.0,60,146
137,add,50.0,C,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O),COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)C,51,add C at position 50,flow_matching,0.3,2.0,60,146
138,add,51.0,C,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)C,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC,52,add C at position 51,flow_matching,0.3,2.0,60,146
139,add,52.0,2,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2,53,add 2 at position 52,flow_matching,0.3,2.0,60,146
140,add,53.0,),,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2),54,add ) at position 53,flow_matching,0.3,2.0,60,146
141,add,54.0,c,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2),COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)c,55,add c at position 54,flow_matching,0.3,2.0,60,146
142,add,55.0,c,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)c,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)cc,56,add c at position 55,flow_matching,0.3,2.0,60,146
143,add,56.0,1,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)cc,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)cc1,57,add 1 at position 56,flow_matching,0.3,2.0,60,146
144,add,57.0,O,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)cc1,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)cc1O,58,add O at position 57,flow_matching,0.3,2.0,60,146
145,add,58.0,C,,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)cc1O,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)cc1OC,59,add C at position 58,flow_matching,0.3,2.0,60,146
146,add,59.0,"
",,COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)cc1OC,"COc1cc(C)c([C@@H](C)NC2CC[NH+]([C@H]3CCCC[C@@H]3O)CC2)cc1OC
",60,"add 
 at position 59",flow_matching,0.3,2.0,60,146
