step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,35,76
1,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,35,76
2,remove,0.0,-,,-,,0,remove - from position 0,flow_matching,0.3,2.0,35,76
3,add,0.0,2,,,2,1,add 2 at position 0,flow_matching,0.3,2.0,35,76
4,add,0.0,#,,2,#2,2,add # at position 0,flow_matching,0.3,2.0,35,76
5,replace,0.0,C,#,#2,C2,2,replace # at position 0 with C,flow_matching,0.3,2.0,35,76
6,add,0.0,H,,C2,HC2,3,add H at position 0,flow_matching,0.3,2.0,35,76
7,add,3.0,I,,HC2,HC2I,4,add I at position 3,flow_matching,0.3,2.0,35,76
8,replace,0.0,O,H,HC2I,OC2I,4,replace H at position 0 with O,flow_matching,0.3,2.0,35,76
9,add,4.0,S,,OC2I,OC2IS,5,add S at position 4,flow_matching,0.3,2.0,35,76
10,replace,0.0,C,O,OC2IS,CC2IS,5,replace O at position 0 with C,flow_matching,0.3,2.0,35,76
11,replace,1.0,7,C,CC2IS,C72IS,5,replace C at position 1 with 7,flow_matching,0.3,2.0,35,76
12,add,4.0,@,,C72IS,C72I@S,6,add @ at position 4,flow_matching,0.3,2.0,35,76
13,replace,1.0,c,7,C72I@S,Cc2I@S,6,replace 7 at position 1 with c,flow_matching,0.3,2.0,35,76
14,add,3.0,=,,Cc2I@S,Cc2=I@S,7,add = at position 3,flow_matching,0.3,2.0,35,76
15,replace,4.0,r,I,Cc2=I@S,Cc2=r@S,7,replace I at position 4 with r,flow_matching,0.3,2.0,35,76
16,add,7.0,N,,Cc2=r@S,Cc2=r@SN,8,add N at position 7,flow_matching,0.3,2.0,35,76
17,replace,3.0,),=,Cc2=r@SN,Cc2)r@SN,8,replace = at position 3 with ),flow_matching,0.3,2.0,35,76
18,add,2.0,B,,Cc2)r@SN,CcB2)r@SN,9,add B at position 2,flow_matching,0.3,2.0,35,76
19,replace,2.0,r,B,CcB2)r@SN,Ccr2)r@SN,9,replace B at position 2 with r,flow_matching,0.3,2.0,35,76
20,remove,1.0,c,,Ccr2)r@SN,Cr2)r@SN,8,remove c from position 1,flow_matching,0.3,2.0,35,76
21,remove,1.0,r,,Cr2)r@SN,C2)r@SN,7,remove r from position 1,flow_matching,0.3,2.0,35,76
22,add,7.0,l,,C2)r@SN,C2)r@SNl,8,add l at position 7,flow_matching,0.3,2.0,35,76
23,add,4.0,1,,C2)r@SNl,C2)r1@SNl,9,add 1 at position 4,flow_matching,0.3,2.0,35,76
24,add,5.0,o,,C2)r1@SNl,C2)r1o@SNl,10,add o at position 5,flow_matching,0.3,2.0,35,76
25,replace,6.0,],@,C2)r1o@SNl,C2)r1o]SNl,10,replace @ at position 6 with ],flow_matching,0.3,2.0,35,76
26,replace,1.0,c,2,C2)r1o]SNl,Cc)r1o]SNl,10,replace 2 at position 1 with c,flow_matching,0.3,2.0,35,76
27,remove,4.0,1,,Cc)r1o]SNl,Cc)ro]SNl,9,remove 1 from position 4,flow_matching,0.3,2.0,35,76
28,remove,8.0,l,,Cc)ro]SNl,Cc)ro]SN,8,remove l from position 8,flow_matching,0.3,2.0,35,76
29,replace,6.0,B,S,Cc)ro]SN,Cc)ro]BN,8,replace S at position 6 with B,flow_matching,0.3,2.0,35,76
30,add,7.0,\,,Cc)ro]BN,Cc)ro]B\N,9,add \ at position 7,flow_matching,0.3,2.0,35,76
31,remove,6.0,B,,Cc)ro]B\N,Cc)ro]\N,8,remove B from position 6,flow_matching,0.3,2.0,35,76
32,replace,5.0,#,],Cc)ro]\N,Cc)ro#\N,8,replace ] at position 5 with #,flow_matching,0.3,2.0,35,76
33,remove,7.0,N,,Cc)ro#\N,Cc)ro#\,7,remove N from position 7,flow_matching,0.3,2.0,35,76
34,add,7.0,=,,Cc)ro#\,Cc)ro#\=,8,add = at position 7,flow_matching,0.3,2.0,35,76
35,replace,7.0,[,=,Cc)ro#\=,Cc)ro#\[,8,replace = at position 7 with [,flow_matching,0.3,2.0,35,76
36,replace,6.0,5,\,Cc)ro#\[,Cc)ro#5[,8,replace \ at position 6 with 5,flow_matching,0.3,2.0,35,76
37,replace,2.0,1,),Cc)ro#5[,Cc1ro#5[,8,replace ) at position 2 with 1,flow_matching,0.3,2.0,35,76
38,add,2.0,o,,Cc1ro#5[,Cco1ro#5[,9,add o at position 2,flow_matching,0.3,2.0,35,76
39,replace,2.0,1,o,Cco1ro#5[,Cc11ro#5[,9,replace o at position 2 with 1,flow_matching,0.3,2.0,35,76
40,add,1.0,o,,Cc11ro#5[,Coc11ro#5[,10,add o at position 1,flow_matching,0.3,2.0,35,76
41,replace,1.0,c,o,Coc11ro#5[,Ccc11ro#5[,10,replace o at position 1 with c,flow_matching,0.3,2.0,35,76
42,add,4.0,S,,Ccc11ro#5[,Ccc1S1ro#5[,11,add S at position 4,flow_matching,0.3,2.0,35,76
43,remove,2.0,c,,Ccc1S1ro#5[,Cc1S1ro#5[,10,remove c from position 2,flow_matching,0.3,2.0,35,76
44,add,8.0,3,,Cc1S1ro#5[,Cc1S1ro#35[,11,add 3 at position 8,flow_matching,0.3,2.0,35,76
45,replace,3.0,n,S,Cc1S1ro#35[,Cc1n1ro#35[,11,replace S at position 3 with n,flow_matching,0.3,2.0,35,76
46,replace,4.0,c,1,Cc1n1ro#35[,Cc1ncro#35[,11,replace 1 at position 4 with c,flow_matching,0.3,2.0,35,76
47,replace,5.0,(,r,Cc1ncro#35[,Cc1nc(o#35[,11,replace r at position 5 with (,flow_matching,0.3,2.0,35,76
48,replace,6.0,C,o,Cc1nc(o#35[,Cc1nc(C#35[,11,replace o at position 6 with C,flow_matching,0.3,2.0,35,76
49,replace,7.0,C,#,Cc1nc(C#35[,Cc1nc(CC35[,11,replace # at position 7 with C,flow_matching,0.3,2.0,35,76
50,replace,8.0,C,3,Cc1nc(CC35[,Cc1nc(CCC5[,11,replace 3 at position 8 with C,flow_matching,0.3,2.0,35,76
51,replace,9.0,[,5,Cc1nc(CCC5[,Cc1nc(CCC[[,11,replace 5 at position 9 with [,flow_matching,0.3,2.0,35,76
52,replace,10.0,N,[,Cc1nc(CCC[[,Cc1nc(CCC[N,11,replace [ at position 10 with N,flow_matching,0.3,2.0,35,76
53,add,11.0,H,,Cc1nc(CCC[N,Cc1nc(CCC[NH,12,add H at position 11,flow_matching,0.3,2.0,35,76
54,add,12.0,+,,Cc1nc(CCC[NH,Cc1nc(CCC[NH+,13,add + at position 12,flow_matching,0.3,2.0,35,76
55,add,13.0,],,Cc1nc(CCC[NH+,Cc1nc(CCC[NH+],14,add ] at position 13,flow_matching,0.3,2.0,35,76
56,add,14.0,2,,Cc1nc(CCC[NH+],Cc1nc(CCC[NH+]2,15,add 2 at position 14,flow_matching,0.3,2.0,35,76
57,add,15.0,C,,Cc1nc(CCC[NH+]2,Cc1nc(CCC[NH+]2C,16,add C at position 15,flow_matching,0.3,2.0,35,76
58,add,16.0,C,,Cc1nc(CCC[NH+]2C,Cc1nc(CCC[NH+]2CC,17,add C at position 16,flow_matching,0.3,2.0,35,76
59,add,17.0,C,,Cc1nc(CCC[NH+]2CC,Cc1nc(CCC[NH+]2CCC,18,add C at position 17,flow_matching,0.3,2.0,35,76
60,add,18.0,[,,Cc1nc(CCC[NH+]2CCC,Cc1nc(CCC[NH+]2CCC[,19,add [ at position 18,flow_matching,0.3,2.0,35,76
61,add,19.0,C,,Cc1nc(CCC[NH+]2CCC[,Cc1nc(CCC[NH+]2CCC[C,20,add C at position 19,flow_matching,0.3,2.0,35,76
62,add,20.0,@,,Cc1nc(CCC[NH+]2CCC[C,Cc1nc(CCC[NH+]2CCC[C@,21,add @ at position 20,flow_matching,0.3,2.0,35,76
63,add,21.0,H,,Cc1nc(CCC[NH+]2CCC[C@,Cc1nc(CCC[NH+]2CCC[C@H,22,add H at position 21,flow_matching,0.3,2.0,35,76
64,add,22.0,],,Cc1nc(CCC[NH+]2CCC[C@H,Cc1nc(CCC[NH+]2CCC[C@H],23,add ] at position 22,flow_matching,0.3,2.0,35,76
65,add,23.0,2,,Cc1nc(CCC[NH+]2CCC[C@H],Cc1nc(CCC[NH+]2CCC[C@H]2,24,add 2 at position 23,flow_matching,0.3,2.0,35,76
66,add,24.0,C,,Cc1nc(CCC[NH+]2CCC[C@H]2,Cc1nc(CCC[NH+]2CCC[C@H]2C,25,add C at position 24,flow_matching,0.3,2.0,35,76
67,add,25.0,(,,Cc1nc(CCC[NH+]2CCC[C@H]2C,Cc1nc(CCC[NH+]2CCC[C@H]2C(,26,add ( at position 25,flow_matching,0.3,2.0,35,76
68,add,26.0,N,,Cc1nc(CCC[NH+]2CCC[C@H]2C(,Cc1nc(CCC[NH+]2CCC[C@H]2C(N,27,add N at position 26,flow_matching,0.3,2.0,35,76
69,add,27.0,),,Cc1nc(CCC[NH+]2CCC[C@H]2C(N,Cc1nc(CCC[NH+]2CCC[C@H]2C(N),28,add ) at position 27,flow_matching,0.3,2.0,35,76
70,add,28.0,=,,Cc1nc(CCC[NH+]2CCC[C@H]2C(N),Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=,29,add = at position 28,flow_matching,0.3,2.0,35,76
71,add,29.0,O,,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O,30,add O at position 29,flow_matching,0.3,2.0,35,76
72,add,30.0,),,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O),31,add ) at position 30,flow_matching,0.3,2.0,35,76
73,add,31.0,c,,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O),Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)c,32,add c at position 31,flow_matching,0.3,2.0,35,76
74,add,32.0,s,,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)c,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)cs,33,add s at position 32,flow_matching,0.3,2.0,35,76
75,add,33.0,1,,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)cs,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)cs1,34,add 1 at position 33,flow_matching,0.3,2.0,35,76
76,add,34.0,"
",,Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)cs1,"Cc1nc(CCC[NH+]2CCC[C@H]2C(N)=O)cs1
",35,"add 
 at position 34",flow_matching,0.3,2.0,35,76
