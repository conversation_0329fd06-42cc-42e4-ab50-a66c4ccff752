step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,52,254
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,52,254
2,replace,0.0,C,O,O,C,1,replace <PERSON> at position 0 with C,flow_matching,0.3,2.0,52,254
3,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,52,254
4,replace,0.0,#,C,CC,#C,2,replace <PERSON> at position 0 with #,flow_matching,0.3,2.0,52,254
5,replace,0.0,C,#,#C,CC,2,replace # at position 0 with C,flow_matching,0.3,2.0,52,254
6,add,2.0,<PERSON>,,<PERSON>,<PERSON>O,3,add <PERSON> at position 2,flow_matching,0.3,2.0,52,254
7,replace,1.0,-,<PERSON>,<PERSON><PERSON>,C-<PERSON>,3,replace <PERSON> at position 1 with -,flow_matching,0.3,2.0,52,254
8,replace,1.0,C,-,C-O,CCO,3,replace - at position 1 with C,flow_matching,0.3,2.0,52,254
9,add,3.0,c,,CCO,CCOc,4,add c at position 3,flow_matching,0.3,2.0,52,254
10,add,2.0,o,,CCOc,CCoOc,5,add o at position 2,flow_matching,0.3,2.0,52,254
11,replace,2.0,O,o,CCoOc,CCOOc,5,replace o at position 2 with O,flow_matching,0.3,2.0,52,254
12,add,2.0,c,,CCOOc,CCcOOc,6,add c at position 2,flow_matching,0.3,2.0,52,254
13,add,4.0,N,,CCcOOc,CCcONOc,7,add N at position 4,flow_matching,0.3,2.0,52,254
14,replace,2.0,O,c,CCcONOc,CCOONOc,7,replace c at position 2 with O,flow_matching,0.3,2.0,52,254
15,replace,3.0,c,O,CCOONOc,CCOcNOc,7,replace O at position 3 with c,flow_matching,0.3,2.0,52,254
16,replace,4.0,1,N,CCOcNOc,CCOc1Oc,7,replace N at position 4 with 1,flow_matching,0.3,2.0,52,254
17,add,3.0,c,,CCOc1Oc,CCOcc1Oc,8,add c at position 3,flow_matching,0.3,2.0,52,254
18,replace,4.0,1,c,CCOcc1Oc,CCOc11Oc,8,replace c at position 4 with 1,flow_matching,0.3,2.0,52,254
19,remove,4.0,1,,CCOc11Oc,CCOc1Oc,7,remove 1 from position 4,flow_matching,0.3,2.0,52,254
20,replace,5.0,c,O,CCOc1Oc,CCOc1cc,7,replace O at position 5 with c,flow_matching,0.3,2.0,52,254
21,add,7.0,c,,CCOc1cc,CCOc1ccc,8,add c at position 7,flow_matching,0.3,2.0,52,254
22,remove,7.0,c,,CCOc1ccc,CCOc1cc,7,remove c from position 7,flow_matching,0.3,2.0,52,254
23,remove,5.0,c,,CCOc1cc,CCOc1c,6,remove c from position 5,flow_matching,0.3,2.0,52,254
24,add,6.0,c,,CCOc1c,CCOc1cc,7,add c at position 6,flow_matching,0.3,2.0,52,254
25,remove,4.0,1,,CCOc1cc,CCOccc,6,remove 1 from position 4,flow_matching,0.3,2.0,52,254
26,replace,4.0,1,c,CCOccc,CCOc1c,6,replace c at position 4 with 1,flow_matching,0.3,2.0,52,254
27,replace,5.0,6,c,CCOc1c,CCOc16,6,replace c at position 5 with 6,flow_matching,0.3,2.0,52,254
28,remove,2.0,O,,CCOc16,CCc16,5,remove O from position 2,flow_matching,0.3,2.0,52,254
29,add,0.0,H,,CCc16,HCCc16,6,add H at position 0,flow_matching,0.3,2.0,52,254
30,replace,3.0,l,c,HCCc16,HCCl16,6,replace c at position 3 with l,flow_matching,0.3,2.0,52,254
31,add,3.0,B,,HCCl16,HCCBl16,7,add B at position 3,flow_matching,0.3,2.0,52,254
32,replace,2.0,@,C,HCCBl16,HC@Bl16,7,replace C at position 2 with @,flow_matching,0.3,2.0,52,254
33,remove,0.0,H,,HC@Bl16,C@Bl16,6,remove H from position 0,flow_matching,0.3,2.0,52,254
34,add,1.0,/,,C@Bl16,C/@Bl16,7,add / at position 1,flow_matching,0.3,2.0,52,254
35,remove,6.0,6,,C/@Bl16,C/@Bl1,6,remove 6 from position 6,flow_matching,0.3,2.0,52,254
36,add,5.0,5,,C/@Bl1,C/@Bl51,7,add 5 at position 5,flow_matching,0.3,2.0,52,254
37,add,3.0,C,,C/@Bl51,C/@CBl51,8,add C at position 3,flow_matching,0.3,2.0,52,254
38,replace,1.0,C,/,C/@CBl51,CC@CBl51,8,replace / at position 1 with C,flow_matching,0.3,2.0,52,254
39,remove,1.0,C,,CC@CBl51,C@CBl51,7,remove C from position 1,flow_matching,0.3,2.0,52,254
40,add,3.0,3,,C@CBl51,C@C3Bl51,8,add 3 at position 3,flow_matching,0.3,2.0,52,254
41,replace,1.0,C,@,C@C3Bl51,CCC3Bl51,8,replace @ at position 1 with C,flow_matching,0.3,2.0,52,254
42,replace,5.0,2,l,CCC3Bl51,CCC3B251,8,replace l at position 5 with 2,flow_matching,0.3,2.0,52,254
43,add,2.0,),,CCC3B251,CC)C3B251,9,add ) at position 2,flow_matching,0.3,2.0,52,254
44,remove,8.0,1,,CC)C3B251,CC)C3B25,8,remove 1 from position 8,flow_matching,0.3,2.0,52,254
45,add,5.0,s,,CC)C3B25,CC)C3sB25,9,add s at position 5,flow_matching,0.3,2.0,52,254
46,remove,0.0,C,,CC)C3sB25,C)C3sB25,8,remove C from position 0,flow_matching,0.3,2.0,52,254
47,replace,1.0,C,),C)C3sB25,CCC3sB25,8,replace ) at position 1 with C,flow_matching,0.3,2.0,52,254
48,replace,0.0,1,C,CCC3sB25,1CC3sB25,8,replace C at position 0 with 1,flow_matching,0.3,2.0,52,254
49,replace,7.0,2,5,1CC3sB25,1CC3sB22,8,replace 5 at position 7 with 2,flow_matching,0.3,2.0,52,254
50,replace,0.0,C,1,1CC3sB22,CCC3sB22,8,replace 1 at position 0 with C,flow_matching,0.3,2.0,52,254
51,add,7.0,-,,CCC3sB22,CCC3sB2-2,9,add - at position 7,flow_matching,0.3,2.0,52,254
52,replace,2.0,O,C,CCC3sB2-2,CCO3sB2-2,9,replace C at position 2 with O,flow_matching,0.3,2.0,52,254
53,replace,3.0,@,3,CCO3sB2-2,CCO@sB2-2,9,replace 3 at position 3 with @,flow_matching,0.3,2.0,52,254
54,replace,3.0,c,@,CCO@sB2-2,CCOcsB2-2,9,replace @ at position 3 with c,flow_matching,0.3,2.0,52,254
55,replace,6.0,H,2,CCOcsB2-2,CCOcsBH-2,9,replace 2 at position 6 with H,flow_matching,0.3,2.0,52,254
56,replace,0.0,r,C,CCOcsBH-2,rCOcsBH-2,9,replace C at position 0 with r,flow_matching,0.3,2.0,52,254
57,add,1.0,6,,rCOcsBH-2,r6COcsBH-2,10,add 6 at position 1,flow_matching,0.3,2.0,52,254
58,replace,0.0,C,r,r6COcsBH-2,C6COcsBH-2,10,replace r at position 0 with C,flow_matching,0.3,2.0,52,254
59,remove,3.0,O,,C6COcsBH-2,C6CcsBH-2,9,remove O from position 3,flow_matching,0.3,2.0,52,254
60,replace,1.0,o,6,C6CcsBH-2,CoCcsBH-2,9,replace 6 at position 1 with o,flow_matching,0.3,2.0,52,254
61,replace,1.0,C,o,CoCcsBH-2,CCCcsBH-2,9,replace o at position 1 with C,flow_matching,0.3,2.0,52,254
62,remove,0.0,C,,CCCcsBH-2,CCcsBH-2,8,remove C from position 0,flow_matching,0.3,2.0,52,254
63,replace,2.0,O,c,CCcsBH-2,CCOsBH-2,8,replace c at position 2 with O,flow_matching,0.3,2.0,52,254
64,replace,3.0,c,s,CCOsBH-2,CCOcBH-2,8,replace s at position 3 with c,flow_matching,0.3,2.0,52,254
65,remove,7.0,2,,CCOcBH-2,CCOcBH-,7,remove 2 from position 7,flow_matching,0.3,2.0,52,254
66,remove,2.0,O,,CCOcBH-,CCcBH-,6,remove O from position 2,flow_matching,0.3,2.0,52,254
67,replace,1.0,B,C,CCcBH-,CBcBH-,6,replace C at position 1 with B,flow_matching,0.3,2.0,52,254
68,add,5.0,I,,CBcBH-,CBcBHI-,7,add I at position 5,flow_matching,0.3,2.0,52,254
69,replace,1.0,C,B,CBcBHI-,CCcBHI-,7,replace B at position 1 with C,flow_matching,0.3,2.0,52,254
70,replace,4.0,\,H,CCcBHI-,CCcB\I-,7,replace H at position 4 with \,flow_matching,0.3,2.0,52,254
71,replace,2.0,O,c,CCcB\I-,CCOB\I-,7,replace c at position 2 with O,flow_matching,0.3,2.0,52,254
72,replace,6.0,N,-,CCOB\I-,CCOB\IN,7,replace - at position 6 with N,flow_matching,0.3,2.0,52,254
73,add,0.0,-,,CCOB\IN,-CCOB\IN,8,add - at position 0,flow_matching,0.3,2.0,52,254
74,replace,0.0,C,-,-CCOB\IN,CCCOB\IN,8,replace - at position 0 with C,flow_matching,0.3,2.0,52,254
75,replace,2.0,O,C,CCCOB\IN,CCOOB\IN,8,replace C at position 2 with O,flow_matching,0.3,2.0,52,254
76,add,5.0,],,CCOOB\IN,CCOOB]\IN,9,add ] at position 5,flow_matching,0.3,2.0,52,254
77,add,8.0,n,,CCOOB]\IN,CCOOB]\InN,10,add n at position 8,flow_matching,0.3,2.0,52,254
78,replace,3.0,5,O,CCOOB]\InN,CCO5B]\InN,10,replace O at position 3 with 5,flow_matching,0.3,2.0,52,254
79,replace,9.0,[,N,CCO5B]\InN,CCO5B]\In[,10,replace N at position 9 with [,flow_matching,0.3,2.0,52,254
80,replace,3.0,H,5,CCO5B]\In[,CCOHB]\In[,10,replace 5 at position 3 with H,flow_matching,0.3,2.0,52,254
81,add,2.0,O,,CCOHB]\In[,CCOOHB]\In[,11,add O at position 2,flow_matching,0.3,2.0,52,254
82,replace,3.0,c,O,CCOOHB]\In[,CCOcHB]\In[,11,replace O at position 3 with c,flow_matching,0.3,2.0,52,254
83,replace,4.0,1,H,CCOcHB]\In[,CCOc1B]\In[,11,replace H at position 4 with 1,flow_matching,0.3,2.0,52,254
84,replace,5.0,c,B,CCOc1B]\In[,CCOc1c]\In[,11,replace B at position 5 with c,flow_matching,0.3,2.0,52,254
85,remove,2.0,O,,CCOc1c]\In[,CCc1c]\In[,10,remove O from position 2,flow_matching,0.3,2.0,52,254
86,remove,6.0,\,,CCc1c]\In[,CCc1c]In[,9,remove \ from position 6,flow_matching,0.3,2.0,52,254
87,replace,2.0,O,c,CCc1c]In[,CCO1c]In[,9,replace c at position 2 with O,flow_matching,0.3,2.0,52,254
88,remove,1.0,C,,CCO1c]In[,CO1c]In[,8,remove C from position 1,flow_matching,0.3,2.0,52,254
89,add,3.0,1,,CO1c]In[,CO11c]In[,9,add 1 at position 3,flow_matching,0.3,2.0,52,254
90,remove,4.0,c,,CO11c]In[,CO11]In[,8,remove c from position 4,flow_matching,0.3,2.0,52,254
91,remove,7.0,[,,CO11]In[,CO11]In,7,remove [ from position 7,flow_matching,0.3,2.0,52,254
92,replace,1.0,C,O,CO11]In,CC11]In,7,replace O at position 1 with C,flow_matching,0.3,2.0,52,254
93,replace,0.0,1,C,CC11]In,1C11]In,7,replace C at position 0 with 1,flow_matching,0.3,2.0,52,254
94,add,1.0,I,,1C11]In,1IC11]In,8,add I at position 1,flow_matching,0.3,2.0,52,254
95,remove,0.0,1,,1IC11]In,IC11]In,7,remove 1 from position 0,flow_matching,0.3,2.0,52,254
96,replace,0.0,C,I,IC11]In,CC11]In,7,replace I at position 0 with C,flow_matching,0.3,2.0,52,254
97,replace,2.0,O,1,CC11]In,CCO1]In,7,replace 1 at position 2 with O,flow_matching,0.3,2.0,52,254
98,add,0.0,@,,CCO1]In,@CCO1]In,8,add @ at position 0,flow_matching,0.3,2.0,52,254
99,replace,4.0,O,1,@CCO1]In,@CCOO]In,8,replace 1 at position 4 with O,flow_matching,0.3,2.0,52,254
100,remove,5.0,],,@CCOO]In,@CCOOIn,7,remove ] from position 5,flow_matching,0.3,2.0,52,254
101,add,5.0,l,,@CCOOIn,@CCOOlIn,8,add l at position 5,flow_matching,0.3,2.0,52,254
102,replace,2.0,S,C,@CCOOlIn,@CSOOlIn,8,replace C at position 2 with S,flow_matching,0.3,2.0,52,254
103,add,4.0,o,,@CSOOlIn,@CSOoOlIn,9,add o at position 4,flow_matching,0.3,2.0,52,254
104,replace,0.0,C,@,@CSOoOlIn,CCSOoOlIn,9,replace @ at position 0 with C,flow_matching,0.3,2.0,52,254
105,add,9.0,I,,CCSOoOlIn,CCSOoOlInI,10,add I at position 9,flow_matching,0.3,2.0,52,254
106,replace,7.0,O,I,CCSOoOlInI,CCSOoOlOnI,10,replace I at position 7 with O,flow_matching,0.3,2.0,52,254
107,remove,4.0,o,,CCSOoOlOnI,CCSOOlOnI,9,remove o from position 4,flow_matching,0.3,2.0,52,254
108,replace,8.0,B,I,CCSOOlOnI,CCSOOlOnB,9,replace I at position 8 with B,flow_matching,0.3,2.0,52,254
109,replace,4.0,N,O,CCSOOlOnB,CCSONlOnB,9,replace O at position 4 with N,flow_matching,0.3,2.0,52,254
110,replace,2.0,O,S,CCSONlOnB,CCOONlOnB,9,replace S at position 2 with O,flow_matching,0.3,2.0,52,254
111,remove,7.0,n,,CCOONlOnB,CCOONlOB,8,remove n from position 7,flow_matching,0.3,2.0,52,254
112,add,7.0,=,,CCOONlOB,CCOONlO=B,9,add = at position 7,flow_matching,0.3,2.0,52,254
113,replace,3.0,c,O,CCOONlO=B,CCOcNlO=B,9,replace O at position 3 with c,flow_matching,0.3,2.0,52,254
114,add,3.0,\,,CCOcNlO=B,CCO\cNlO=B,10,add \ at position 3,flow_matching,0.3,2.0,52,254
115,replace,4.0,7,c,CCO\cNlO=B,CCO\7NlO=B,10,replace c at position 4 with 7,flow_matching,0.3,2.0,52,254
116,replace,3.0,c,\,CCO\7NlO=B,CCOc7NlO=B,10,replace \ at position 3 with c,flow_matching,0.3,2.0,52,254
117,add,10.0,/,,CCOc7NlO=B,CCOc7NlO=B/,11,add / at position 10,flow_matching,0.3,2.0,52,254
118,remove,5.0,N,,CCOc7NlO=B/,CCOc7lO=B/,10,remove N from position 5,flow_matching,0.3,2.0,52,254
119,add,9.0,l,,CCOc7lO=B/,CCOc7lO=Bl/,11,add l at position 9,flow_matching,0.3,2.0,52,254
120,add,0.0,o,,CCOc7lO=Bl/,oCCOc7lO=Bl/,12,add o at position 0,flow_matching,0.3,2.0,52,254
121,replace,4.0,o,c,oCCOc7lO=Bl/,oCCOo7lO=Bl/,12,replace c at position 4 with o,flow_matching,0.3,2.0,52,254
122,add,12.0,],,oCCOo7lO=Bl/,oCCOo7lO=Bl/],13,add ] at position 12,flow_matching,0.3,2.0,52,254
123,add,2.0,r,,oCCOo7lO=Bl/],oCrCOo7lO=Bl/],14,add r at position 2,flow_matching,0.3,2.0,52,254
124,add,3.0,o,,oCrCOo7lO=Bl/],oCroCOo7lO=Bl/],15,add o at position 3,flow_matching,0.3,2.0,52,254
125,replace,0.0,C,o,oCroCOo7lO=Bl/],CCroCOo7lO=Bl/],15,replace o at position 0 with C,flow_matching,0.3,2.0,52,254
126,replace,6.0,+,o,CCroCOo7lO=Bl/],CCroCO+7lO=Bl/],15,replace o at position 6 with +,flow_matching,0.3,2.0,52,254
127,add,9.0,F,,CCroCO+7lO=Bl/],CCroCO+7lFO=Bl/],16,add F at position 9,flow_matching,0.3,2.0,52,254
128,add,13.0,\,,CCroCO+7lFO=Bl/],CCroCO+7lFO=B\l/],17,add \ at position 13,flow_matching,0.3,2.0,52,254
129,add,15.0,1,,CCroCO+7lFO=B\l/],CCroCO+7lFO=B\l1/],18,add 1 at position 15,flow_matching,0.3,2.0,52,254
130,add,14.0,O,,CCroCO+7lFO=B\l1/],CCroCO+7lFO=B\Ol1/],19,add O at position 14,flow_matching,0.3,2.0,52,254
131,remove,0.0,C,,CCroCO+7lFO=B\Ol1/],CroCO+7lFO=B\Ol1/],18,remove C from position 0,flow_matching,0.3,2.0,52,254
132,add,18.0,S,,CroCO+7lFO=B\Ol1/],CroCO+7lFO=B\Ol1/]S,19,add S at position 18,flow_matching,0.3,2.0,52,254
133,replace,17.0,l,],CroCO+7lFO=B\Ol1/]S,CroCO+7lFO=B\Ol1/lS,19,replace ] at position 17 with l,flow_matching,0.3,2.0,52,254
134,replace,1.0,C,r,CroCO+7lFO=B\Ol1/lS,CCoCO+7lFO=B\Ol1/lS,19,replace r at position 1 with C,flow_matching,0.3,2.0,52,254
135,remove,14.0,l,,CCoCO+7lFO=B\Ol1/lS,CCoCO+7lFO=B\O1/lS,18,remove l from position 14,flow_matching,0.3,2.0,52,254
136,add,17.0,(,,CCoCO+7lFO=B\O1/lS,CCoCO+7lFO=B\O1/l(S,19,add ( at position 17,flow_matching,0.3,2.0,52,254
137,replace,3.0,3,C,CCoCO+7lFO=B\O1/l(S,CCo3O+7lFO=B\O1/l(S,19,replace C at position 3 with 3,flow_matching,0.3,2.0,52,254
138,add,19.0,),,CCo3O+7lFO=B\O1/l(S,CCo3O+7lFO=B\O1/l(S),20,add ) at position 19,flow_matching,0.3,2.0,52,254
139,remove,3.0,3,,CCo3O+7lFO=B\O1/l(S),CCoO+7lFO=B\O1/l(S),19,remove 3 from position 3,flow_matching,0.3,2.0,52,254
140,replace,16.0,),(,CCoO+7lFO=B\O1/l(S),CCoO+7lFO=B\O1/l)S),19,replace ( at position 16 with ),flow_matching,0.3,2.0,52,254
141,add,18.0,B,,CCoO+7lFO=B\O1/l)S),CCoO+7lFO=B\O1/l)SB),20,add B at position 18,flow_matching,0.3,2.0,52,254
142,add,9.0,o,,CCoO+7lFO=B\O1/l)SB),CCoO+7lFOo=B\O1/l)SB),21,add o at position 9,flow_matching,0.3,2.0,52,254
143,replace,2.0,O,o,CCoO+7lFOo=B\O1/l)SB),CCOO+7lFOo=B\O1/l)SB),21,replace o at position 2 with O,flow_matching,0.3,2.0,52,254
144,replace,3.0,c,O,CCOO+7lFOo=B\O1/l)SB),CCOc+7lFOo=B\O1/l)SB),21,replace O at position 3 with c,flow_matching,0.3,2.0,52,254
145,add,8.0,@,,CCOc+7lFOo=B\O1/l)SB),CCOc+7lF@Oo=B\O1/l)SB),22,add @ at position 8,flow_matching,0.3,2.0,52,254
146,replace,4.0,1,+,CCOc+7lF@Oo=B\O1/l)SB),CCOc17lF@Oo=B\O1/l)SB),22,replace + at position 4 with 1,flow_matching,0.3,2.0,52,254
147,add,13.0,H,,CCOc17lF@Oo=B\O1/l)SB),CCOc17lF@Oo=BH\O1/l)SB),23,add H at position 13,flow_matching,0.3,2.0,52,254
148,replace,5.0,c,7,CCOc17lF@Oo=BH\O1/l)SB),CCOc1clF@Oo=BH\O1/l)SB),23,replace 7 at position 5 with c,flow_matching,0.3,2.0,52,254
149,replace,6.0,c,l,CCOc1clF@Oo=BH\O1/l)SB),CCOc1ccF@Oo=BH\O1/l)SB),23,replace l at position 6 with c,flow_matching,0.3,2.0,52,254
150,remove,11.0,=,,CCOc1ccF@Oo=BH\O1/l)SB),CCOc1ccF@OoBH\O1/l)SB),22,remove = from position 11,flow_matching,0.3,2.0,52,254
151,replace,7.0,c,F,CCOc1ccF@OoBH\O1/l)SB),CCOc1ccc@OoBH\O1/l)SB),22,replace F at position 7 with c,flow_matching,0.3,2.0,52,254
152,add,16.0,#,,CCOc1ccc@OoBH\O1/l)SB),CCOc1ccc@OoBH\O1#/l)SB),23,add # at position 16,flow_matching,0.3,2.0,52,254
153,replace,8.0,2,@,CCOc1ccc@OoBH\O1#/l)SB),CCOc1ccc2OoBH\O1#/l)SB),23,replace @ at position 8 with 2,flow_matching,0.3,2.0,52,254
154,replace,22.0,I,),CCOc1ccc2OoBH\O1#/l)SB),CCOc1ccc2OoBH\O1#/l)SBI,23,replace ) at position 22 with I,flow_matching,0.3,2.0,52,254
155,replace,8.0,(,2,CCOc1ccc2OoBH\O1#/l)SBI,CCOc1ccc(OoBH\O1#/l)SBI,23,replace 2 at position 8 with (,flow_matching,0.3,2.0,52,254
156,remove,9.0,O,,CCOc1ccc(OoBH\O1#/l)SBI,CCOc1ccc(oBH\O1#/l)SBI,22,remove O from position 9,flow_matching,0.3,2.0,52,254
157,replace,10.0,-,B,CCOc1ccc(oBH\O1#/l)SBI,CCOc1ccc(o-H\O1#/l)SBI,22,replace B at position 10 with -,flow_matching,0.3,2.0,52,254
158,replace,14.0,(,1,CCOc1ccc(o-H\O1#/l)SBI,CCOc1ccc(o-H\O(#/l)SBI,22,replace 1 at position 14 with (,flow_matching,0.3,2.0,52,254
159,replace,10.0,r,-,CCOc1ccc(o-H\O(#/l)SBI,CCOc1ccc(orH\O(#/l)SBI,22,replace - at position 10 with r,flow_matching,0.3,2.0,52,254
160,remove,11.0,H,,CCOc1ccc(orH\O(#/l)SBI,CCOc1ccc(or\O(#/l)SBI,21,remove H from position 11,flow_matching,0.3,2.0,52,254
161,remove,9.0,o,,CCOc1ccc(or\O(#/l)SBI,CCOc1ccc(r\O(#/l)SBI,20,remove o from position 9,flow_matching,0.3,2.0,52,254
162,replace,9.0,[,r,CCOc1ccc(r\O(#/l)SBI,CCOc1ccc([\O(#/l)SBI,20,replace r at position 9 with [,flow_matching,0.3,2.0,52,254
163,replace,15.0,c,l,CCOc1ccc([\O(#/l)SBI,CCOc1ccc([\O(#/c)SBI,20,replace l at position 15 with c,flow_matching,0.3,2.0,52,254
164,remove,15.0,c,,CCOc1ccc([\O(#/c)SBI,CCOc1ccc([\O(#/)SBI,19,remove c from position 15,flow_matching,0.3,2.0,52,254
165,add,5.0,],,CCOc1ccc([\O(#/)SBI,CCOc1]ccc([\O(#/)SBI,20,add ] at position 5,flow_matching,0.3,2.0,52,254
166,replace,7.0,6,c,CCOc1]ccc([\O(#/)SBI,CCOc1]c6c([\O(#/)SBI,20,replace c at position 7 with 6,flow_matching,0.3,2.0,52,254
167,replace,5.0,c,],CCOc1]c6c([\O(#/)SBI,CCOc1cc6c([\O(#/)SBI,20,replace ] at position 5 with c,flow_matching,0.3,2.0,52,254
168,replace,7.0,c,6,CCOc1cc6c([\O(#/)SBI,CCOc1cccc([\O(#/)SBI,20,replace 6 at position 7 with c,flow_matching,0.3,2.0,52,254
169,replace,18.0,3,B,CCOc1cccc([\O(#/)SBI,CCOc1cccc([\O(#/)S3I,20,replace B at position 18 with 3,flow_matching,0.3,2.0,52,254
170,replace,8.0,(,c,CCOc1cccc([\O(#/)S3I,CCOc1ccc(([\O(#/)S3I,20,replace c at position 8 with (,flow_matching,0.3,2.0,52,254
171,add,15.0,),,CCOc1ccc(([\O(#/)S3I,CCOc1ccc(([\O(#)/)S3I,21,add ) at position 15,flow_matching,0.3,2.0,52,254
172,replace,8.0,),(,CCOc1ccc(([\O(#)/)S3I,CCOc1ccc)([\O(#)/)S3I,21,replace ( at position 8 with ),flow_matching,0.3,2.0,52,254
173,replace,8.0,(,),CCOc1ccc)([\O(#)/)S3I,CCOc1ccc(([\O(#)/)S3I,21,replace ) at position 8 with (,flow_matching,0.3,2.0,52,254
174,remove,16.0,/,,CCOc1ccc(([\O(#)/)S3I,CCOc1ccc(([\O(#))S3I,20,remove / from position 16,flow_matching,0.3,2.0,52,254
175,remove,12.0,O,,CCOc1ccc(([\O(#))S3I,CCOc1ccc(([\(#))S3I,19,remove O from position 12,flow_matching,0.3,2.0,52,254
176,replace,9.0,[,(,CCOc1ccc(([\(#))S3I,CCOc1ccc([[\(#))S3I,19,replace ( at position 9 with [,flow_matching,0.3,2.0,52,254
177,replace,10.0,C,[,CCOc1ccc([[\(#))S3I,CCOc1ccc([C\(#))S3I,19,replace [ at position 10 with C,flow_matching,0.3,2.0,52,254
178,add,10.0,1,,CCOc1ccc([C\(#))S3I,CCOc1ccc([1C\(#))S3I,20,add 1 at position 10,flow_matching,0.3,2.0,52,254
179,remove,13.0,(,,CCOc1ccc([1C\(#))S3I,CCOc1ccc([1C\#))S3I,19,remove ( from position 13,flow_matching,0.3,2.0,52,254
180,remove,9.0,[,,CCOc1ccc([1C\#))S3I,CCOc1ccc(1C\#))S3I,18,remove [ from position 9,flow_matching,0.3,2.0,52,254
181,replace,14.0,C,),CCOc1ccc(1C\#))S3I,CCOc1ccc(1C\#)CS3I,18,replace ) at position 14 with C,flow_matching,0.3,2.0,52,254
182,replace,9.0,[,1,CCOc1ccc(1C\#)CS3I,CCOc1ccc([C\#)CS3I,18,replace 1 at position 9 with [,flow_matching,0.3,2.0,52,254
183,replace,11.0,@,\,CCOc1ccc([C\#)CS3I,CCOc1ccc([C@#)CS3I,18,replace \ at position 11 with @,flow_matching,0.3,2.0,52,254
184,replace,12.0,H,#,CCOc1ccc([C@#)CS3I,CCOc1ccc([C@H)CS3I,18,replace # at position 12 with H,flow_matching,0.3,2.0,52,254
185,remove,12.0,H,,CCOc1ccc([C@H)CS3I,CCOc1ccc([C@)CS3I,17,remove H from position 12,flow_matching,0.3,2.0,52,254
186,replace,12.0,H,),CCOc1ccc([C@)CS3I,CCOc1ccc([C@HCS3I,17,replace ) at position 12 with H,flow_matching,0.3,2.0,52,254
187,replace,13.0,],C,CCOc1ccc([C@HCS3I,CCOc1ccc([C@H]S3I,17,replace C at position 13 with ],flow_matching,0.3,2.0,52,254
188,replace,14.0,2,S,CCOc1ccc([C@H]S3I,CCOc1ccc([C@H]23I,17,replace S at position 14 with 2,flow_matching,0.3,2.0,52,254
189,add,2.0,n,,CCOc1ccc([C@H]23I,CCnOc1ccc([C@H]23I,18,add n at position 2,flow_matching,0.3,2.0,52,254
190,add,13.0,F,,CCnOc1ccc([C@H]23I,CCnOc1ccc([C@FH]23I,19,add F at position 13,flow_matching,0.3,2.0,52,254
191,add,15.0,1,,CCnOc1ccc([C@FH]23I,CCnOc1ccc([C@FH1]23I,20,add 1 at position 15,flow_matching,0.3,2.0,52,254
192,add,8.0,=,,CCnOc1ccc([C@FH1]23I,CCnOc1cc=c([C@FH1]23I,21,add = at position 8,flow_matching,0.3,2.0,52,254
193,replace,2.0,O,n,CCnOc1cc=c([C@FH1]23I,CCOOc1cc=c([C@FH1]23I,21,replace n at position 2 with O,flow_matching,0.3,2.0,52,254
194,replace,18.0,5,2,CCOOc1cc=c([C@FH1]23I,CCOOc1cc=c([C@FH1]53I,21,replace 2 at position 18 with 5,flow_matching,0.3,2.0,52,254
195,add,15.0,r,,CCOOc1cc=c([C@FH1]53I,CCOOc1cc=c([C@FrH1]53I,22,add r at position 15,flow_matching,0.3,2.0,52,254
196,add,1.0,6,,CCOOc1cc=c([C@FrH1]53I,C6COOc1cc=c([C@FrH1]53I,23,add 6 at position 1,flow_matching,0.3,2.0,52,254
197,add,6.0,N,,C6COOc1cc=c([C@FrH1]53I,C6COOcN1cc=c([C@FrH1]53I,24,add N at position 6,flow_matching,0.3,2.0,52,254
198,add,24.0,[,,C6COOcN1cc=c([C@FrH1]53I,C6COOcN1cc=c([C@FrH1]53I[,25,add [ at position 24,flow_matching,0.3,2.0,52,254
199,replace,2.0,o,C,C6COOcN1cc=c([C@FrH1]53I[,C6oOOcN1cc=c([C@FrH1]53I[,25,replace C at position 2 with o,flow_matching,0.3,2.0,52,254
200,replace,1.0,C,6,C6oOOcN1cc=c([C@FrH1]53I[,CCoOOcN1cc=c([C@FrH1]53I[,25,replace 6 at position 1 with C,flow_matching,0.3,2.0,52,254
201,replace,2.0,O,o,CCoOOcN1cc=c([C@FrH1]53I[,CCOOOcN1cc=c([C@FrH1]53I[,25,replace o at position 2 with O,flow_matching,0.3,2.0,52,254
202,replace,3.0,c,O,CCOOOcN1cc=c([C@FrH1]53I[,CCOcOcN1cc=c([C@FrH1]53I[,25,replace O at position 3 with c,flow_matching,0.3,2.0,52,254
203,add,25.0,[,,CCOcOcN1cc=c([C@FrH1]53I[,CCOcOcN1cc=c([C@FrH1]53I[[,26,add [ at position 25,flow_matching,0.3,2.0,52,254
204,replace,4.0,1,O,CCOcOcN1cc=c([C@FrH1]53I[[,CCOc1cN1cc=c([C@FrH1]53I[[,26,replace O at position 4 with 1,flow_matching,0.3,2.0,52,254
205,add,1.0,3,,CCOc1cN1cc=c([C@FrH1]53I[[,C3COc1cN1cc=c([C@FrH1]53I[[,27,add 3 at position 1,flow_matching,0.3,2.0,52,254
206,add,11.0,F,,C3COc1cN1cc=c([C@FrH1]53I[[,C3COc1cN1ccF=c([C@FrH1]53I[[,28,add F at position 11,flow_matching,0.3,2.0,52,254
207,replace,1.0,C,3,C3COc1cN1ccF=c([C@FrH1]53I[[,CCCOc1cN1ccF=c([C@FrH1]53I[[,28,replace 3 at position 1 with C,flow_matching,0.3,2.0,52,254
208,replace,2.0,O,C,CCCOc1cN1ccF=c([C@FrH1]53I[[,CCOOc1cN1ccF=c([C@FrH1]53I[[,28,replace C at position 2 with O,flow_matching,0.3,2.0,52,254
209,replace,3.0,c,O,CCOOc1cN1ccF=c([C@FrH1]53I[[,CCOcc1cN1ccF=c([C@FrH1]53I[[,28,replace O at position 3 with c,flow_matching,0.3,2.0,52,254
210,replace,4.0,1,c,CCOcc1cN1ccF=c([C@FrH1]53I[[,CCOc11cN1ccF=c([C@FrH1]53I[[,28,replace c at position 4 with 1,flow_matching,0.3,2.0,52,254
211,replace,5.0,c,1,CCOc11cN1ccF=c([C@FrH1]53I[[,CCOc1ccN1ccF=c([C@FrH1]53I[[,28,replace 1 at position 5 with c,flow_matching,0.3,2.0,52,254
212,replace,7.0,c,N,CCOc1ccN1ccF=c([C@FrH1]53I[[,CCOc1ccc1ccF=c([C@FrH1]53I[[,28,replace N at position 7 with c,flow_matching,0.3,2.0,52,254
213,replace,8.0,(,1,CCOc1ccc1ccF=c([C@FrH1]53I[[,CCOc1ccc(ccF=c([C@FrH1]53I[[,28,replace 1 at position 8 with (,flow_matching,0.3,2.0,52,254
214,replace,9.0,[,c,CCOc1ccc(ccF=c([C@FrH1]53I[[,CCOc1ccc([cF=c([C@FrH1]53I[[,28,replace c at position 9 with [,flow_matching,0.3,2.0,52,254
215,replace,10.0,C,c,CCOc1ccc([cF=c([C@FrH1]53I[[,CCOc1ccc([CF=c([C@FrH1]53I[[,28,replace c at position 10 with C,flow_matching,0.3,2.0,52,254
216,replace,11.0,@,F,CCOc1ccc([CF=c([C@FrH1]53I[[,CCOc1ccc([C@=c([C@FrH1]53I[[,28,replace F at position 11 with @,flow_matching,0.3,2.0,52,254
217,replace,12.0,H,=,CCOc1ccc([C@=c([C@FrH1]53I[[,CCOc1ccc([C@Hc([C@FrH1]53I[[,28,replace = at position 12 with H,flow_matching,0.3,2.0,52,254
218,replace,13.0,],c,CCOc1ccc([C@Hc([C@FrH1]53I[[,CCOc1ccc([C@H]([C@FrH1]53I[[,28,replace c at position 13 with ],flow_matching,0.3,2.0,52,254
219,replace,14.0,2,(,CCOc1ccc([C@H]([C@FrH1]53I[[,CCOc1ccc([C@H]2[C@FrH1]53I[[,28,replace ( at position 14 with 2,flow_matching,0.3,2.0,52,254
220,replace,15.0,C,[,CCOc1ccc([C@H]2[C@FrH1]53I[[,CCOc1ccc([C@H]2CC@FrH1]53I[[,28,replace [ at position 15 with C,flow_matching,0.3,2.0,52,254
221,replace,17.0,C,@,CCOc1ccc([C@H]2CC@FrH1]53I[[,CCOc1ccc([C@H]2CCCFrH1]53I[[,28,replace @ at position 17 with C,flow_matching,0.3,2.0,52,254
222,replace,18.0,N,F,CCOc1ccc([C@H]2CCCFrH1]53I[[,CCOc1ccc([C@H]2CCCNrH1]53I[[,28,replace F at position 18 with N,flow_matching,0.3,2.0,52,254
223,replace,19.0,2,r,CCOc1ccc([C@H]2CCCNrH1]53I[[,CCOc1ccc([C@H]2CCCN2H1]53I[[,28,replace r at position 19 with 2,flow_matching,0.3,2.0,52,254
224,replace,20.0,C,H,CCOc1ccc([C@H]2CCCN2H1]53I[[,CCOc1ccc([C@H]2CCCN2C1]53I[[,28,replace H at position 20 with C,flow_matching,0.3,2.0,52,254
225,replace,21.0,(,1,CCOc1ccc([C@H]2CCCN2C1]53I[[,CCOc1ccc([C@H]2CCCN2C(]53I[[,28,replace 1 at position 21 with (,flow_matching,0.3,2.0,52,254
226,replace,22.0,=,],CCOc1ccc([C@H]2CCCN2C(]53I[[,CCOc1ccc([C@H]2CCCN2C(=53I[[,28,replace ] at position 22 with =,flow_matching,0.3,2.0,52,254
227,replace,23.0,O,5,CCOc1ccc([C@H]2CCCN2C(=53I[[,CCOc1ccc([C@H]2CCCN2C(=O3I[[,28,replace 5 at position 23 with O,flow_matching,0.3,2.0,52,254
228,replace,24.0,),3,CCOc1ccc([C@H]2CCCN2C(=O3I[[,CCOc1ccc([C@H]2CCCN2C(=O)I[[,28,replace 3 at position 24 with ),flow_matching,0.3,2.0,52,254
229,replace,25.0,c,I,CCOc1ccc([C@H]2CCCN2C(=O)I[[,CCOc1ccc([C@H]2CCCN2C(=O)c[[,28,replace I at position 25 with c,flow_matching,0.3,2.0,52,254
230,replace,26.0,2,[,CCOc1ccc([C@H]2CCCN2C(=O)c[[,CCOc1ccc([C@H]2CCCN2C(=O)c2[,28,replace [ at position 26 with 2,flow_matching,0.3,2.0,52,254
231,add,28.0,n,,CCOc1ccc([C@H]2CCCN2C(=O)c2[,CCOc1ccc([C@H]2CCCN2C(=O)c2[n,29,add n at position 28,flow_matching,0.3,2.0,52,254
232,add,29.0,H,,CCOc1ccc([C@H]2CCCN2C(=O)c2[n,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH,30,add H at position 29,flow_matching,0.3,2.0,52,254
233,add,30.0,],,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH],31,add ] at position 30,flow_matching,0.3,2.0,52,254
234,add,31.0,c,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH],CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c,32,add c at position 31,flow_matching,0.3,2.0,52,254
235,add,32.0,(,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(,33,add ( at position 32,flow_matching,0.3,2.0,52,254
236,add,33.0,C,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C,34,add C at position 33,flow_matching,0.3,2.0,52,254
237,add,34.0,),,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C),35,add ) at position 34,flow_matching,0.3,2.0,52,254
238,add,35.0,c,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C),CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c,36,add c at position 35,flow_matching,0.3,2.0,52,254
239,add,36.0,(,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(,37,add ( at position 36,flow_matching,0.3,2.0,52,254
240,add,37.0,C,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C,38,add C at position 37,flow_matching,0.3,2.0,52,254
241,add,38.0,(,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(,39,add ( at position 38,flow_matching,0.3,2.0,52,254
242,add,39.0,C,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C,40,add C at position 39,flow_matching,0.3,2.0,52,254
243,add,40.0,),,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C),41,add ) at position 40,flow_matching,0.3,2.0,52,254
244,add,41.0,=,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C),CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=,42,add = at position 41,flow_matching,0.3,2.0,52,254
245,add,42.0,O,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O,43,add O at position 42,flow_matching,0.3,2.0,52,254
246,add,43.0,),,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O),44,add ) at position 43,flow_matching,0.3,2.0,52,254
247,add,44.0,c,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O),CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c,45,add c at position 44,flow_matching,0.3,2.0,52,254
248,add,45.0,2,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2,46,add 2 at position 45,flow_matching,0.3,2.0,52,254
249,add,46.0,C,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C,47,add C at position 46,flow_matching,0.3,2.0,52,254
250,add,47.0,),,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C),48,add ) at position 47,flow_matching,0.3,2.0,52,254
251,add,48.0,c,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C),CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C)c,49,add c at position 48,flow_matching,0.3,2.0,52,254
252,add,49.0,c,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C)c,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C)cc,50,add c at position 49,flow_matching,0.3,2.0,52,254
253,add,50.0,1,,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C)cc,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C)cc1,51,add 1 at position 50,flow_matching,0.3,2.0,52,254
254,add,51.0,"
",,CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C)cc1,"CCOc1ccc([C@H]2CCCN2C(=O)c2[nH]c(C)c(C(C)=O)c2C)cc1
",52,"add 
 at position 51",flow_matching,0.3,2.0,52,254
