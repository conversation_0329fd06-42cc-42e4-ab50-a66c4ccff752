step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,45,107
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,45,107
2,replace,0.0,5,C,C,5,1,replace <PERSON> at position 0 with 5,flow_matching,0.3,2.0,45,107
3,replace,0.0,/,5,5,/,1,replace 5 at position 0 with /,flow_matching,0.3,2.0,45,107
4,replace,0.0,C,/,/,C,1,replace / at position 0 with C,flow_matching,0.3,2.0,45,107
5,add,0.0,6,,C,6C,2,add 6 at position 0,flow_matching,0.3,2.0,45,107
6,replace,0.0,C,6,6C,CC,2,replace 6 at position 0 with <PERSON>,flow_matching,0.3,2.0,45,107
7,add,0.0,n,,CC,nCC,3,add n at position 0,flow_matching,0.3,2.0,45,107
8,replace,0.0,C,n,nCC,CCC,3,replace n at position 0 with C,flow_matching,0.3,2.0,45,107
9,replace,1.0,c,C,CCC,CcC,3,replace C at position 1 with c,flow_matching,0.3,2.0,45,107
10,remove,2.0,C,,CcC,Cc,2,remove C from position 2,flow_matching,0.3,2.0,45,107
11,remove,0.0,C,,Cc,c,1,remove C from position 0,flow_matching,0.3,2.0,45,107
12,replace,0.0,F,c,c,F,1,replace c at position 0 with F,flow_matching,0.3,2.0,45,107
13,remove,0.0,F,,F,,0,remove F from position 0,flow_matching,0.3,2.0,45,107
14,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,45,107
15,add,1.0,S,,O,OS,2,add S at position 1,flow_matching,0.3,2.0,45,107
16,replace,0.0,C,O,OS,CS,2,replace O at position 0 with C,flow_matching,0.3,2.0,45,107
17,remove,0.0,C,,CS,S,1,remove C from position 0,flow_matching,0.3,2.0,45,107
18,replace,0.0,#,S,S,#,1,replace S at position 0 with #,flow_matching,0.3,2.0,45,107
19,replace,0.0,C,#,#,C,1,replace # at position 0 with C,flow_matching,0.3,2.0,45,107
20,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,45,107
21,replace,1.0,2,c,Cc,C2,2,replace c at position 1 with 2,flow_matching,0.3,2.0,45,107
22,replace,1.0,c,2,C2,Cc,2,replace 2 at position 1 with c,flow_matching,0.3,2.0,45,107
23,add,1.0,/,,Cc,C/c,3,add / at position 1,flow_matching,0.3,2.0,45,107
24,replace,1.0,c,/,C/c,Ccc,3,replace / at position 1 with c,flow_matching,0.3,2.0,45,107
25,add,0.0,5,,Ccc,5Ccc,4,add 5 at position 0,flow_matching,0.3,2.0,45,107
26,replace,2.0,/,c,5Ccc,5C/c,4,replace c at position 2 with /,flow_matching,0.3,2.0,45,107
27,add,4.0,B,,5C/c,5C/cB,5,add B at position 4,flow_matching,0.3,2.0,45,107
28,replace,0.0,1,5,5C/cB,1C/cB,5,replace 5 at position 0 with 1,flow_matching,0.3,2.0,45,107
29,add,0.0,B,,1C/cB,B1C/cB,6,add B at position 0,flow_matching,0.3,2.0,45,107
30,add,3.0,5,,B1C/cB,B1C5/cB,7,add 5 at position 3,flow_matching,0.3,2.0,45,107
31,add,2.0,c,,B1C5/cB,B1cC5/cB,8,add c at position 2,flow_matching,0.3,2.0,45,107
32,add,7.0,2,,B1cC5/cB,B1cC5/c2B,9,add 2 at position 7,flow_matching,0.3,2.0,45,107
33,replace,0.0,C,B,B1cC5/c2B,C1cC5/c2B,9,replace B at position 0 with C,flow_matching,0.3,2.0,45,107
34,replace,4.0,],5,C1cC5/c2B,C1cC]/c2B,9,replace 5 at position 4 with ],flow_matching,0.3,2.0,45,107
35,add,5.0,4,,C1cC]/c2B,C1cC]4/c2B,10,add 4 at position 5,flow_matching,0.3,2.0,45,107
36,add,8.0,2,,C1cC]4/c2B,C1cC]4/c22B,11,add 2 at position 8,flow_matching,0.3,2.0,45,107
37,replace,4.0,2,],C1cC]4/c22B,C1cC24/c22B,11,replace ] at position 4 with 2,flow_matching,0.3,2.0,45,107
38,remove,2.0,c,,C1cC24/c22B,C1C24/c22B,10,remove c from position 2,flow_matching,0.3,2.0,45,107
39,add,6.0,3,,C1C24/c22B,C1C24/3c22B,11,add 3 at position 6,flow_matching,0.3,2.0,45,107
40,replace,3.0,#,2,C1C24/3c22B,C1C#4/3c22B,11,replace 2 at position 3 with #,flow_matching,0.3,2.0,45,107
41,add,1.0,n,,C1C#4/3c22B,Cn1C#4/3c22B,12,add n at position 1,flow_matching,0.3,2.0,45,107
42,replace,1.0,c,n,Cn1C#4/3c22B,Cc1C#4/3c22B,12,replace n at position 1 with c,flow_matching,0.3,2.0,45,107
43,add,5.0,/,,Cc1C#4/3c22B,Cc1C#/4/3c22B,13,add / at position 5,flow_matching,0.3,2.0,45,107
44,remove,0.0,C,,Cc1C#/4/3c22B,c1C#/4/3c22B,12,remove C from position 0,flow_matching,0.3,2.0,45,107
45,add,11.0,2,,c1C#/4/3c22B,c1C#/4/3c222B,13,add 2 at position 11,flow_matching,0.3,2.0,45,107
46,remove,6.0,/,,c1C#/4/3c222B,c1C#/43c222B,12,remove / from position 6,flow_matching,0.3,2.0,45,107
47,add,11.0,-,,c1C#/43c222B,c1C#/43c222-B,13,add - at position 11,flow_matching,0.3,2.0,45,107
48,add,1.0,1,,c1C#/43c222-B,c11C#/43c222-B,14,add 1 at position 1,flow_matching,0.3,2.0,45,107
49,remove,9.0,2,,c11C#/43c222-B,c11C#/43c22-B,13,remove 2 from position 9,flow_matching,0.3,2.0,45,107
50,replace,0.0,C,c,c11C#/43c22-B,C11C#/43c22-B,13,replace c at position 0 with C,flow_matching,0.3,2.0,45,107
51,add,7.0,/,,C11C#/43c22-B,C11C#/4/3c22-B,14,add / at position 7,flow_matching,0.3,2.0,45,107
52,replace,1.0,r,1,C11C#/4/3c22-B,Cr1C#/4/3c22-B,14,replace 1 at position 1 with r,flow_matching,0.3,2.0,45,107
53,remove,11.0,2,,Cr1C#/4/3c22-B,Cr1C#/4/3c2-B,13,remove 2 from position 11,flow_matching,0.3,2.0,45,107
54,replace,1.0,c,r,Cr1C#/4/3c2-B,Cc1C#/4/3c2-B,13,replace r at position 1 with c,flow_matching,0.3,2.0,45,107
55,replace,9.0,s,c,Cc1C#/4/3c2-B,Cc1C#/4/3s2-B,13,replace c at position 9 with s,flow_matching,0.3,2.0,45,107
56,replace,3.0,c,C,Cc1C#/4/3s2-B,Cc1c#/4/3s2-B,13,replace C at position 3 with c,flow_matching,0.3,2.0,45,107
57,replace,4.0,c,#,Cc1c#/4/3s2-B,Cc1cc/4/3s2-B,13,replace # at position 4 with c,flow_matching,0.3,2.0,45,107
58,replace,5.0,c,/,Cc1cc/4/3s2-B,Cc1ccc4/3s2-B,13,replace / at position 5 with c,flow_matching,0.3,2.0,45,107
59,replace,6.0,(,4,Cc1ccc4/3s2-B,Cc1ccc(/3s2-B,13,replace 4 at position 6 with (,flow_matching,0.3,2.0,45,107
60,add,2.0,I,,Cc1ccc(/3s2-B,CcI1ccc(/3s2-B,14,add I at position 2,flow_matching,0.3,2.0,45,107
61,replace,2.0,1,I,CcI1ccc(/3s2-B,Cc11ccc(/3s2-B,14,replace I at position 2 with 1,flow_matching,0.3,2.0,45,107
62,add,7.0,@,,Cc11ccc(/3s2-B,Cc11ccc@(/3s2-B,15,add @ at position 7,flow_matching,0.3,2.0,45,107
63,replace,3.0,c,1,Cc11ccc@(/3s2-B,Cc1cccc@(/3s2-B,15,replace 1 at position 3 with c,flow_matching,0.3,2.0,45,107
64,remove,4.0,c,,Cc1cccc@(/3s2-B,Cc1ccc@(/3s2-B,14,remove c from position 4,flow_matching,0.3,2.0,45,107
65,remove,4.0,c,,Cc1ccc@(/3s2-B,Cc1cc@(/3s2-B,13,remove c from position 4,flow_matching,0.3,2.0,45,107
66,add,9.0,o,,Cc1cc@(/3s2-B,Cc1cc@(/3os2-B,14,add o at position 9,flow_matching,0.3,2.0,45,107
67,remove,6.0,(,,Cc1cc@(/3os2-B,Cc1cc@/3os2-B,13,remove ( from position 6,flow_matching,0.3,2.0,45,107
68,replace,5.0,c,@,Cc1cc@/3os2-B,Cc1ccc/3os2-B,13,replace @ at position 5 with c,flow_matching,0.3,2.0,45,107
69,replace,6.0,(,/,Cc1ccc/3os2-B,Cc1ccc(3os2-B,13,replace / at position 6 with (,flow_matching,0.3,2.0,45,107
70,replace,7.0,[,3,Cc1ccc(3os2-B,Cc1ccc([os2-B,13,replace 3 at position 7 with [,flow_matching,0.3,2.0,45,107
71,replace,8.0,C,o,Cc1ccc([os2-B,Cc1ccc([Cs2-B,13,replace o at position 8 with C,flow_matching,0.3,2.0,45,107
72,replace,9.0,@,s,Cc1ccc([Cs2-B,Cc1ccc([C@2-B,13,replace s at position 9 with @,flow_matching,0.3,2.0,45,107
73,replace,10.0,H,2,Cc1ccc([C@2-B,Cc1ccc([C@H-B,13,replace 2 at position 10 with H,flow_matching,0.3,2.0,45,107
74,replace,11.0,],-,Cc1ccc([C@H-B,Cc1ccc([C@H]B,13,replace - at position 11 with ],flow_matching,0.3,2.0,45,107
75,replace,12.0,2,B,Cc1ccc([C@H]B,Cc1ccc([C@H]2,13,replace B at position 12 with 2,flow_matching,0.3,2.0,45,107
76,add,13.0,C,,Cc1ccc([C@H]2,Cc1ccc([C@H]2C,14,add C at position 13,flow_matching,0.3,2.0,45,107
77,add,14.0,3,,Cc1ccc([C@H]2C,Cc1ccc([C@H]2C3,15,add 3 at position 14,flow_matching,0.3,2.0,45,107
78,add,15.0,=,,Cc1ccc([C@H]2C3,Cc1ccc([C@H]2C3=,16,add = at position 15,flow_matching,0.3,2.0,45,107
79,add,16.0,C,,Cc1ccc([C@H]2C3=,Cc1ccc([C@H]2C3=C,17,add C at position 16,flow_matching,0.3,2.0,45,107
80,add,17.0,(,,Cc1ccc([C@H]2C3=C,Cc1ccc([C@H]2C3=C(,18,add ( at position 17,flow_matching,0.3,2.0,45,107
81,add,18.0,N,,Cc1ccc([C@H]2C3=C(,Cc1ccc([C@H]2C3=C(N,19,add N at position 18,flow_matching,0.3,2.0,45,107
82,add,19.0,C,,Cc1ccc([C@H]2C3=C(N,Cc1ccc([C@H]2C3=C(NC,20,add C at position 19,flow_matching,0.3,2.0,45,107
83,add,20.0,(,,Cc1ccc([C@H]2C3=C(NC,Cc1ccc([C@H]2C3=C(NC(,21,add ( at position 20,flow_matching,0.3,2.0,45,107
84,add,21.0,=,,Cc1ccc([C@H]2C3=C(NC(,Cc1ccc([C@H]2C3=C(NC(=,22,add = at position 21,flow_matching,0.3,2.0,45,107
85,add,22.0,O,,Cc1ccc([C@H]2C3=C(NC(=,Cc1ccc([C@H]2C3=C(NC(=O,23,add O at position 22,flow_matching,0.3,2.0,45,107
86,add,23.0,),,Cc1ccc([C@H]2C3=C(NC(=O,Cc1ccc([C@H]2C3=C(NC(=O),24,add ) at position 23,flow_matching,0.3,2.0,45,107
87,add,24.0,N,,Cc1ccc([C@H]2C3=C(NC(=O),Cc1ccc([C@H]2C3=C(NC(=O)N,25,add N at position 24,flow_matching,0.3,2.0,45,107
88,add,25.0,2,,Cc1ccc([C@H]2C3=C(NC(=O)N,Cc1ccc([C@H]2C3=C(NC(=O)N2,26,add 2 at position 25,flow_matching,0.3,2.0,45,107
89,add,26.0,C,,Cc1ccc([C@H]2C3=C(NC(=O)N2,Cc1ccc([C@H]2C3=C(NC(=O)N2C,27,add C at position 26,flow_matching,0.3,2.0,45,107
90,add,27.0,),,Cc1ccc([C@H]2C3=C(NC(=O)N2C,Cc1ccc([C@H]2C3=C(NC(=O)N2C),28,add ) at position 27,flow_matching,0.3,2.0,45,107
91,add,28.0,c,,Cc1ccc([C@H]2C3=C(NC(=O)N2C),Cc1ccc([C@H]2C3=C(NC(=O)N2C)c,29,add c at position 28,flow_matching,0.3,2.0,45,107
92,add,29.0,2,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2,30,add 2 at position 29,flow_matching,0.3,2.0,45,107
93,add,30.0,c,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2c,31,add c at position 30,flow_matching,0.3,2.0,45,107
94,add,31.0,c,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2c,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2cc,32,add c at position 31,flow_matching,0.3,2.0,45,107
95,add,32.0,c,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2cc,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccc,33,add c at position 32,flow_matching,0.3,2.0,45,107
96,add,33.0,c,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccc,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2cccc,34,add c at position 33,flow_matching,0.3,2.0,45,107
97,add,34.0,c,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2cccc,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc,35,add c at position 34,flow_matching,0.3,2.0,45,107
98,add,35.0,2,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2,36,add 2 at position 35,flow_matching,0.3,2.0,45,107
99,add,36.0,C,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C,37,add C at position 36,flow_matching,0.3,2.0,45,107
100,add,37.0,3,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3,38,add 3 at position 37,flow_matching,0.3,2.0,45,107
101,add,38.0,=,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=,39,add = at position 38,flow_matching,0.3,2.0,45,107
102,add,39.0,O,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O,40,add O at position 39,flow_matching,0.3,2.0,45,107
103,add,40.0,),,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O),41,add ) at position 40,flow_matching,0.3,2.0,45,107
104,add,41.0,c,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O),Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O)c,42,add c at position 41,flow_matching,0.3,2.0,45,107
105,add,42.0,c,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O)c,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O)cc,43,add c at position 42,flow_matching,0.3,2.0,45,107
106,add,43.0,1,,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O)cc,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O)cc1,44,add 1 at position 43,flow_matching,0.3,2.0,45,107
107,add,44.0,"
",,Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O)cc1,"Cc1ccc([C@H]2C3=C(NC(=O)N2C)c2ccccc2C3=O)cc1
",45,"add 
 at position 44",flow_matching,0.3,2.0,45,107
