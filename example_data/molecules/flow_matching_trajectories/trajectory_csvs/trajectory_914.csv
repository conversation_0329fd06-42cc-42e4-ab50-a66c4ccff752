step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,38,168
1,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,38,168
2,add,0.0,3,,-,3-,2,add 3 at position 0,flow_matching,0.3,2.0,38,168
3,add,1.0,O,,3-,3O-,3,add O at position 1,flow_matching,0.3,2.0,38,168
4,remove,1.0,O,,3O-,3-,2,remove O from position 1,flow_matching,0.3,2.0,38,168
5,replace,0.0,C,3,3-,C-,2,replace 3 at position 0 with C,flow_matching,0.3,2.0,38,168
6,replace,1.0,n,-,C-,Cn,2,replace - at position 1 with n,flow_matching,0.3,2.0,38,168
7,add,0.0,=,,Cn,=Cn,3,add = at position 0,flow_matching,0.3,2.0,38,168
8,replace,1.0,-,C,=Cn,=-n,3,replace C at position 1 with -,flow_matching,0.3,2.0,38,168
9,replace,0.0,C,=,=-n,C-n,3,replace = at position 0 with C,flow_matching,0.3,2.0,38,168
10,add,1.0,\,,C-n,C\-n,4,add \ at position 1,flow_matching,0.3,2.0,38,168
11,replace,0.0,),C,C\-n,)\-n,4,replace C at position 0 with ),flow_matching,0.3,2.0,38,168
12,remove,2.0,-,,)\-n,)\n,3,remove - from position 2,flow_matching,0.3,2.0,38,168
13,remove,0.0,),,)\n,\n,2,remove ) from position 0,flow_matching,0.3,2.0,38,168
14,add,2.0,1,,\n,\n1,3,add 1 at position 2,flow_matching,0.3,2.0,38,168
15,replace,0.0,C,\,\n1,Cn1,3,replace \ at position 0 with C,flow_matching,0.3,2.0,38,168
16,add,0.0,#,,Cn1,#Cn1,4,add # at position 0,flow_matching,0.3,2.0,38,168
17,remove,2.0,n,,#Cn1,#C1,3,remove n from position 2,flow_matching,0.3,2.0,38,168
18,replace,0.0,C,#,#C1,CC1,3,replace # at position 0 with C,flow_matching,0.3,2.0,38,168
19,remove,0.0,C,,CC1,C1,2,remove C from position 0,flow_matching,0.3,2.0,38,168
20,remove,0.0,C,,C1,1,1,remove C from position 0,flow_matching,0.3,2.0,38,168
21,remove,0.0,1,,1,,0,remove 1 from position 0,flow_matching,0.3,2.0,38,168
22,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,38,168
23,add,1.0,n,,C,Cn,2,add n at position 1,flow_matching,0.3,2.0,38,168
24,add,2.0,1,,Cn,Cn1,3,add 1 at position 2,flow_matching,0.3,2.0,38,168
25,add,1.0,),,Cn1,C)n1,4,add ) at position 1,flow_matching,0.3,2.0,38,168
26,replace,1.0,n,),C)n1,Cnn1,4,replace ) at position 1 with n,flow_matching,0.3,2.0,38,168
27,remove,3.0,1,,Cnn1,Cnn,3,remove 1 from position 3,flow_matching,0.3,2.0,38,168
28,add,2.0,l,,Cnn,Cnln,4,add l at position 2,flow_matching,0.3,2.0,38,168
29,replace,3.0,O,n,Cnln,CnlO,4,replace n at position 3 with O,flow_matching,0.3,2.0,38,168
30,replace,0.0,),C,CnlO,)nlO,4,replace C at position 0 with ),flow_matching,0.3,2.0,38,168
31,replace,0.0,C,),)nlO,CnlO,4,replace ) at position 0 with C,flow_matching,0.3,2.0,38,168
32,add,0.0,[,,CnlO,[CnlO,5,add [ at position 0,flow_matching,0.3,2.0,38,168
33,replace,0.0,C,[,[CnlO,CCnlO,5,replace [ at position 0 with C,flow_matching,0.3,2.0,38,168
34,replace,1.0,O,C,CCnlO,COnlO,5,replace C at position 1 with O,flow_matching,0.3,2.0,38,168
35,remove,4.0,O,,COnlO,COnl,4,remove O from position 4,flow_matching,0.3,2.0,38,168
36,add,0.0,l,,COnl,lCOnl,5,add l at position 0,flow_matching,0.3,2.0,38,168
37,replace,0.0,C,l,lCOnl,CCOnl,5,replace l at position 0 with C,flow_matching,0.3,2.0,38,168
38,replace,1.0,n,C,CCOnl,CnOnl,5,replace C at position 1 with n,flow_matching,0.3,2.0,38,168
39,add,3.0,#,,CnOnl,CnO#nl,6,add # at position 3,flow_matching,0.3,2.0,38,168
40,replace,2.0,1,O,CnO#nl,Cn1#nl,6,replace O at position 2 with 1,flow_matching,0.3,2.0,38,168
41,remove,1.0,n,,Cn1#nl,C1#nl,5,remove n from position 1,flow_matching,0.3,2.0,38,168
42,add,5.0,5,,C1#nl,C1#nl5,6,add 5 at position 5,flow_matching,0.3,2.0,38,168
43,replace,1.0,n,1,C1#nl5,Cn#nl5,6,replace 1 at position 1 with n,flow_matching,0.3,2.0,38,168
44,add,6.0,[,,Cn#nl5,Cn#nl5[,7,add [ at position 6,flow_matching,0.3,2.0,38,168
45,replace,0.0,5,C,Cn#nl5[,5n#nl5[,7,replace C at position 0 with 5,flow_matching,0.3,2.0,38,168
46,replace,0.0,C,5,5n#nl5[,Cn#nl5[,7,replace 5 at position 0 with C,flow_matching,0.3,2.0,38,168
47,replace,2.0,1,#,Cn#nl5[,Cn1nl5[,7,replace # at position 2 with 1,flow_matching,0.3,2.0,38,168
48,remove,0.0,C,,Cn1nl5[,n1nl5[,6,remove C from position 0,flow_matching,0.3,2.0,38,168
49,remove,0.0,n,,n1nl5[,1nl5[,5,remove n from position 0,flow_matching,0.3,2.0,38,168
50,add,0.0,=,,1nl5[,=1nl5[,6,add = at position 0,flow_matching,0.3,2.0,38,168
51,add,2.0,7,,=1nl5[,=17nl5[,7,add 7 at position 2,flow_matching,0.3,2.0,38,168
52,add,7.0,4,,=17nl5[,=17nl5[4,8,add 4 at position 7,flow_matching,0.3,2.0,38,168
53,replace,0.0,C,=,=17nl5[4,C17nl5[4,8,replace = at position 0 with C,flow_matching,0.3,2.0,38,168
54,replace,0.0,5,C,C17nl5[4,517nl5[4,8,replace C at position 0 with 5,flow_matching,0.3,2.0,38,168
55,replace,3.0,o,n,517nl5[4,517ol5[4,8,replace n at position 3 with o,flow_matching,0.3,2.0,38,168
56,add,6.0,n,,517ol5[4,517ol5n[4,9,add n at position 6,flow_matching,0.3,2.0,38,168
57,replace,8.0,n,4,517ol5n[4,517ol5n[n,9,replace 4 at position 8 with n,flow_matching,0.3,2.0,38,168
58,add,0.0,6,,517ol5n[n,6517ol5n[n,10,add 6 at position 0,flow_matching,0.3,2.0,38,168
59,replace,0.0,C,6,6517ol5n[n,C517ol5n[n,10,replace 6 at position 0 with C,flow_matching,0.3,2.0,38,168
60,remove,3.0,7,,C517ol5n[n,C51ol5n[n,9,remove 7 from position 3,flow_matching,0.3,2.0,38,168
61,replace,1.0,n,5,C51ol5n[n,Cn1ol5n[n,9,replace 5 at position 1 with n,flow_matching,0.3,2.0,38,168
62,remove,2.0,1,,Cn1ol5n[n,Cnol5n[n,8,remove 1 from position 2,flow_matching,0.3,2.0,38,168
63,replace,2.0,1,o,Cnol5n[n,Cn1l5n[n,8,replace o at position 2 with 1,flow_matching,0.3,2.0,38,168
64,remove,7.0,n,,Cn1l5n[n,Cn1l5n[,7,remove n from position 7,flow_matching,0.3,2.0,38,168
65,replace,3.0,c,l,Cn1l5n[,Cn1c5n[,7,replace l at position 3 with c,flow_matching,0.3,2.0,38,168
66,replace,4.0,n,5,Cn1c5n[,Cn1cnn[,7,replace 5 at position 4 with n,flow_matching,0.3,2.0,38,168
67,add,6.0,#,,Cn1cnn[,Cn1cnn#[,8,add # at position 6,flow_matching,0.3,2.0,38,168
68,remove,5.0,n,,Cn1cnn#[,Cn1cn#[,7,remove n from position 5,flow_matching,0.3,2.0,38,168
69,replace,5.0,n,#,Cn1cn#[,Cn1cnn[,7,replace # at position 5 with n,flow_matching,0.3,2.0,38,168
70,replace,6.0,(,[,Cn1cnn[,Cn1cnn(,7,replace [ at position 6 with (,flow_matching,0.3,2.0,38,168
71,replace,0.0,O,C,Cn1cnn(,On1cnn(,7,replace C at position 0 with O,flow_matching,0.3,2.0,38,168
72,remove,4.0,n,,On1cnn(,On1cn(,6,remove n from position 4,flow_matching,0.3,2.0,38,168
73,replace,5.0,H,(,On1cn(,On1cnH,6,replace ( at position 5 with H,flow_matching,0.3,2.0,38,168
74,add,4.0,4,,On1cnH,On1c4nH,7,add 4 at position 4,flow_matching,0.3,2.0,38,168
75,replace,0.0,C,O,On1c4nH,Cn1c4nH,7,replace O at position 0 with C,flow_matching,0.3,2.0,38,168
76,replace,4.0,n,4,Cn1c4nH,Cn1cnnH,7,replace 4 at position 4 with n,flow_matching,0.3,2.0,38,168
77,remove,6.0,H,,Cn1cnnH,Cn1cnn,6,remove H from position 6,flow_matching,0.3,2.0,38,168
78,add,6.0,(,,Cn1cnn,Cn1cnn(,7,add ( at position 6,flow_matching,0.3,2.0,38,168
79,add,3.0,N,,Cn1cnn(,Cn1Ncnn(,8,add N at position 3,flow_matching,0.3,2.0,38,168
80,replace,3.0,c,N,Cn1Ncnn(,Cn1ccnn(,8,replace N at position 3 with c,flow_matching,0.3,2.0,38,168
81,replace,0.0,+,C,Cn1ccnn(,+n1ccnn(,8,replace C at position 0 with +,flow_matching,0.3,2.0,38,168
82,add,2.0,N,,+n1ccnn(,+nN1ccnn(,9,add N at position 2,flow_matching,0.3,2.0,38,168
83,remove,8.0,(,,+nN1ccnn(,+nN1ccnn,8,remove ( from position 8,flow_matching,0.3,2.0,38,168
84,add,6.0,/,,+nN1ccnn,+nN1cc/nn,9,add / at position 6,flow_matching,0.3,2.0,38,168
85,remove,3.0,1,,+nN1cc/nn,+nNcc/nn,8,remove 1 from position 3,flow_matching,0.3,2.0,38,168
86,add,1.0,N,,+nNcc/nn,+NnNcc/nn,9,add N at position 1,flow_matching,0.3,2.0,38,168
87,remove,3.0,N,,+NnNcc/nn,+Nncc/nn,8,remove N from position 3,flow_matching,0.3,2.0,38,168
88,add,7.0,s,,+Nncc/nn,+Nncc/nsn,9,add s at position 7,flow_matching,0.3,2.0,38,168
89,replace,0.0,C,+,+Nncc/nsn,CNncc/nsn,9,replace + at position 0 with C,flow_matching,0.3,2.0,38,168
90,replace,6.0,\,n,CNncc/nsn,CNncc/\sn,9,replace n at position 6 with \,flow_matching,0.3,2.0,38,168
91,replace,1.0,4,N,CNncc/\sn,C4ncc/\sn,9,replace N at position 1 with 4,flow_matching,0.3,2.0,38,168
92,add,1.0,-,,C4ncc/\sn,C-4ncc/\sn,10,add - at position 1,flow_matching,0.3,2.0,38,168
93,remove,0.0,C,,C-4ncc/\sn,-4ncc/\sn,9,remove C from position 0,flow_matching,0.3,2.0,38,168
94,replace,0.0,C,-,-4ncc/\sn,C4ncc/\sn,9,replace - at position 0 with C,flow_matching,0.3,2.0,38,168
95,replace,1.0,n,4,C4ncc/\sn,Cnncc/\sn,9,replace 4 at position 1 with n,flow_matching,0.3,2.0,38,168
96,add,3.0,[,,Cnncc/\sn,Cnn[cc/\sn,10,add [ at position 3,flow_matching,0.3,2.0,38,168
97,remove,4.0,c,,Cnn[cc/\sn,Cnn[c/\sn,9,remove c from position 4,flow_matching,0.3,2.0,38,168
98,remove,3.0,[,,Cnn[c/\sn,Cnnc/\sn,8,remove [ from position 3,flow_matching,0.3,2.0,38,168
99,replace,1.0,O,n,Cnnc/\sn,COnc/\sn,8,replace n at position 1 with O,flow_matching,0.3,2.0,38,168
100,replace,1.0,n,O,COnc/\sn,Cnnc/\sn,8,replace O at position 1 with n,flow_matching,0.3,2.0,38,168
101,replace,2.0,(,n,Cnnc/\sn,Cn(c/\sn,8,replace n at position 2 with (,flow_matching,0.3,2.0,38,168
102,add,7.0,n,,Cn(c/\sn,Cn(c/\snn,9,add n at position 7,flow_matching,0.3,2.0,38,168
103,replace,2.0,1,(,Cn(c/\snn,Cn1c/\snn,9,replace ( at position 2 with 1,flow_matching,0.3,2.0,38,168
104,add,2.0,r,,Cn1c/\snn,Cnr1c/\snn,10,add r at position 2,flow_matching,0.3,2.0,38,168
105,remove,2.0,r,,Cnr1c/\snn,Cn1c/\snn,9,remove r from position 2,flow_matching,0.3,2.0,38,168
106,add,4.0,r,,Cn1c/\snn,Cn1cr/\snn,10,add r at position 4,flow_matching,0.3,2.0,38,168
107,replace,4.0,n,r,Cn1cr/\snn,Cn1cn/\snn,10,replace r at position 4 with n,flow_matching,0.3,2.0,38,168
108,remove,7.0,s,,Cn1cn/\snn,Cn1cn/\nn,9,remove s from position 7,flow_matching,0.3,2.0,38,168
109,replace,5.0,n,/,Cn1cn/\nn,Cn1cnn\nn,9,replace / at position 5 with n,flow_matching,0.3,2.0,38,168
110,replace,6.0,(,\,Cn1cnn\nn,Cn1cnn(nn,9,replace \ at position 6 with (,flow_matching,0.3,2.0,38,168
111,add,2.0,s,,Cn1cnn(nn,Cns1cnn(nn,10,add s at position 2,flow_matching,0.3,2.0,38,168
112,replace,4.0,s,c,Cns1cnn(nn,Cns1snn(nn,10,replace c at position 4 with s,flow_matching,0.3,2.0,38,168
113,replace,2.0,1,s,Cns1snn(nn,Cn11snn(nn,10,replace s at position 2 with 1,flow_matching,0.3,2.0,38,168
114,replace,4.0,+,s,Cn11snn(nn,Cn11+nn(nn,10,replace s at position 4 with +,flow_matching,0.3,2.0,38,168
115,replace,3.0,c,1,Cn11+nn(nn,Cn1c+nn(nn,10,replace 1 at position 3 with c,flow_matching,0.3,2.0,38,168
116,replace,3.0,+,c,Cn1c+nn(nn,Cn1++nn(nn,10,replace c at position 3 with +,flow_matching,0.3,2.0,38,168
117,replace,1.0,S,n,Cn1++nn(nn,CS1++nn(nn,10,replace n at position 1 with S,flow_matching,0.3,2.0,38,168
118,add,6.0,o,,CS1++nn(nn,CS1++non(nn,11,add o at position 6,flow_matching,0.3,2.0,38,168
119,replace,5.0,B,n,CS1++non(nn,CS1++Bon(nn,11,replace n at position 5 with B,flow_matching,0.3,2.0,38,168
120,remove,9.0,n,,CS1++Bon(nn,CS1++Bon(n,10,remove n from position 9,flow_matching,0.3,2.0,38,168
121,replace,9.0,B,n,CS1++Bon(n,CS1++Bon(B,10,replace n at position 9 with B,flow_matching,0.3,2.0,38,168
122,remove,9.0,B,,CS1++Bon(B,CS1++Bon(,9,remove B from position 9,flow_matching,0.3,2.0,38,168
123,replace,1.0,n,S,CS1++Bon(,Cn1++Bon(,9,replace S at position 1 with n,flow_matching,0.3,2.0,38,168
124,remove,4.0,+,,Cn1++Bon(,Cn1+Bon(,8,remove + from position 4,flow_matching,0.3,2.0,38,168
125,replace,3.0,5,+,Cn1+Bon(,Cn15Bon(,8,replace + at position 3 with 5,flow_matching,0.3,2.0,38,168
126,add,0.0,l,,Cn15Bon(,lCn15Bon(,9,add l at position 0,flow_matching,0.3,2.0,38,168
127,add,3.0,2,,lCn15Bon(,lCn215Bon(,10,add 2 at position 3,flow_matching,0.3,2.0,38,168
128,replace,0.0,C,l,lCn215Bon(,CCn215Bon(,10,replace l at position 0 with C,flow_matching,0.3,2.0,38,168
129,add,7.0,F,,CCn215Bon(,CCn215BFon(,11,add F at position 7,flow_matching,0.3,2.0,38,168
130,replace,6.0,r,B,CCn215BFon(,CCn215rFon(,11,replace B at position 6 with r,flow_matching,0.3,2.0,38,168
131,replace,1.0,n,C,CCn215rFon(,Cnn215rFon(,11,replace C at position 1 with n,flow_matching,0.3,2.0,38,168
132,replace,10.0,#,(,Cnn215rFon(,Cnn215rFon#,11,replace ( at position 10 with #,flow_matching,0.3,2.0,38,168
133,replace,2.0,1,n,Cnn215rFon#,Cn1215rFon#,11,replace n at position 2 with 1,flow_matching,0.3,2.0,38,168
134,replace,3.0,c,2,Cn1215rFon#,Cn1c15rFon#,11,replace 2 at position 3 with c,flow_matching,0.3,2.0,38,168
135,replace,4.0,n,1,Cn1c15rFon#,Cn1cn5rFon#,11,replace 1 at position 4 with n,flow_matching,0.3,2.0,38,168
136,replace,5.0,n,5,Cn1cn5rFon#,Cn1cnnrFon#,11,replace 5 at position 5 with n,flow_matching,0.3,2.0,38,168
137,replace,6.0,(,r,Cn1cnnrFon#,Cn1cnn(Fon#,11,replace r at position 6 with (,flow_matching,0.3,2.0,38,168
138,replace,7.0,C,F,Cn1cnn(Fon#,Cn1cnn(Con#,11,replace F at position 7 with C,flow_matching,0.3,2.0,38,168
139,replace,8.0,[,o,Cn1cnn(Con#,Cn1cnn(C[n#,11,replace o at position 8 with [,flow_matching,0.3,2.0,38,168
140,replace,9.0,N,n,Cn1cnn(C[n#,Cn1cnn(C[N#,11,replace n at position 9 with N,flow_matching,0.3,2.0,38,168
141,replace,10.0,H,#,Cn1cnn(C[N#,Cn1cnn(C[NH,11,replace # at position 10 with H,flow_matching,0.3,2.0,38,168
142,add,11.0,+,,Cn1cnn(C[NH,Cn1cnn(C[NH+,12,add + at position 11,flow_matching,0.3,2.0,38,168
143,add,12.0,],,Cn1cnn(C[NH+,Cn1cnn(C[NH+],13,add ] at position 12,flow_matching,0.3,2.0,38,168
144,add,13.0,(,,Cn1cnn(C[NH+],Cn1cnn(C[NH+](,14,add ( at position 13,flow_matching,0.3,2.0,38,168
145,add,14.0,C,,Cn1cnn(C[NH+](,Cn1cnn(C[NH+](C,15,add C at position 14,flow_matching,0.3,2.0,38,168
146,add,15.0,c,,Cn1cnn(C[NH+](C,Cn1cnn(C[NH+](Cc,16,add c at position 15,flow_matching,0.3,2.0,38,168
147,add,16.0,2,,Cn1cnn(C[NH+](Cc,Cn1cnn(C[NH+](Cc2,17,add 2 at position 16,flow_matching,0.3,2.0,38,168
148,add,17.0,c,,Cn1cnn(C[NH+](Cc2,Cn1cnn(C[NH+](Cc2c,18,add c at position 17,flow_matching,0.3,2.0,38,168
149,add,18.0,c,,Cn1cnn(C[NH+](Cc2c,Cn1cnn(C[NH+](Cc2cc,19,add c at position 18,flow_matching,0.3,2.0,38,168
150,add,19.0,c,,Cn1cnn(C[NH+](Cc2cc,Cn1cnn(C[NH+](Cc2ccc,20,add c at position 19,flow_matching,0.3,2.0,38,168
151,add,20.0,(,,Cn1cnn(C[NH+](Cc2ccc,Cn1cnn(C[NH+](Cc2ccc(,21,add ( at position 20,flow_matching,0.3,2.0,38,168
152,add,21.0,F,,Cn1cnn(C[NH+](Cc2ccc(,Cn1cnn(C[NH+](Cc2ccc(F,22,add F at position 21,flow_matching,0.3,2.0,38,168
153,add,22.0,),,Cn1cnn(C[NH+](Cc2ccc(F,Cn1cnn(C[NH+](Cc2ccc(F),23,add ) at position 22,flow_matching,0.3,2.0,38,168
154,add,23.0,c,,Cn1cnn(C[NH+](Cc2ccc(F),Cn1cnn(C[NH+](Cc2ccc(F)c,24,add c at position 23,flow_matching,0.3,2.0,38,168
155,add,24.0,c,,Cn1cnn(C[NH+](Cc2ccc(F)c,Cn1cnn(C[NH+](Cc2ccc(F)cc,25,add c at position 24,flow_matching,0.3,2.0,38,168
156,add,25.0,2,,Cn1cnn(C[NH+](Cc2ccc(F)cc,Cn1cnn(C[NH+](Cc2ccc(F)cc2,26,add 2 at position 25,flow_matching,0.3,2.0,38,168
157,add,26.0,),,Cn1cnn(C[NH+](Cc2ccc(F)cc2,Cn1cnn(C[NH+](Cc2ccc(F)cc2),27,add ) at position 26,flow_matching,0.3,2.0,38,168
158,add,27.0,C,,Cn1cnn(C[NH+](Cc2ccc(F)cc2),Cn1cnn(C[NH+](Cc2ccc(F)cc2)C,28,add C at position 27,flow_matching,0.3,2.0,38,168
159,add,28.0,2,,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2,29,add 2 at position 28,flow_matching,0.3,2.0,38,168
160,add,29.0,C,,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2C,30,add C at position 29,flow_matching,0.3,2.0,38,168
161,add,30.0,C,,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2C,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC,31,add C at position 30,flow_matching,0.3,2.0,38,168
162,add,31.0,2,,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2,32,add 2 at position 31,flow_matching,0.3,2.0,38,168
163,add,32.0,),,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2),33,add ) at position 32,flow_matching,0.3,2.0,38,168
164,add,33.0,c,,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2),Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2)c,34,add c at position 33,flow_matching,0.3,2.0,38,168
165,add,34.0,1,,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2)c,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2)c1,35,add 1 at position 34,flow_matching,0.3,2.0,38,168
166,add,35.0,=,,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2)c1,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2)c1=,36,add = at position 35,flow_matching,0.3,2.0,38,168
167,add,36.0,S,,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2)c1=,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2)c1=S,37,add S at position 36,flow_matching,0.3,2.0,38,168
168,add,37.0,"
",,Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2)c1=S,"Cn1cnn(C[NH+](Cc2ccc(F)cc2)C2CC2)c1=S
",38,"add 
 at position 37",flow_matching,0.3,2.0,38,168
