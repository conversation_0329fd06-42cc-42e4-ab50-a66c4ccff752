step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,51,223
1,add,0.0,n,,,n,1,add n at position 0,flow_matching,0.3,2.0,51,223
2,add,0.0,H,,n,Hn,2,add H at position 0,flow_matching,0.3,2.0,51,223
3,remove,0.0,H,,Hn,n,1,remove H from position 0,flow_matching,0.3,2.0,51,223
4,add,1.0,C,,n,nC,2,add <PERSON> at position 1,flow_matching,0.3,2.0,51,223
5,replace,0.0,C,n,nC,CC,2,replace n at position 0 with C,flow_matching,0.3,2.0,51,223
6,replace,1.0,<PERSON>,<PERSON>,<PERSON>,<PERSON>,2,replace <PERSON> at position 1 with <PERSON>,flow_matching,0.3,2.0,51,223
7,replace,1.0,<PERSON>,<PERSON>,<PERSON>,<PERSON>,2,replace <PERSON> at position 1 with C,flow_matching,0.3,2.0,51,223
8,add,2.0,[,,CC,CC[,3,add [ at position 2,flow_matching,0.3,2.0,51,223
9,add,3.0,C,,CC[,CC[C,4,add C at position 3,flow_matching,0.3,2.0,51,223
10,remove,1.0,C,,CC[C,C[C,3,remove C from position 1,flow_matching,0.3,2.0,51,223
11,replace,1.0,C,[,C[C,CCC,3,replace [ at position 1 with C,flow_matching,0.3,2.0,51,223
12,replace,2.0,H,C,CCC,CCH,3,replace C at position 2 with H,flow_matching,0.3,2.0,51,223
13,replace,2.0,[,H,CCH,CC[,3,replace H at position 2 with [,flow_matching,0.3,2.0,51,223
14,add,1.0,=,,CC[,C=C[,4,add = at position 1,flow_matching,0.3,2.0,51,223
15,replace,2.0,/,C,C=C[,C=/[,4,replace C at position 2 with /,flow_matching,0.3,2.0,51,223
16,replace,1.0,C,=,C=/[,CC/[,4,replace = at position 1 with C,flow_matching,0.3,2.0,51,223
17,add,4.0,I,,CC/[,CC/[I,5,add I at position 4,flow_matching,0.3,2.0,51,223
18,add,4.0,C,,CC/[I,CC/[CI,6,add C at position 4,flow_matching,0.3,2.0,51,223
19,add,1.0,=,,CC/[CI,C=C/[CI,7,add = at position 1,flow_matching,0.3,2.0,51,223
20,add,3.0,N,,C=C/[CI,C=CN/[CI,8,add N at position 3,flow_matching,0.3,2.0,51,223
21,remove,0.0,C,,C=CN/[CI,=CN/[CI,7,remove C from position 0,flow_matching,0.3,2.0,51,223
22,replace,0.0,#,=,=CN/[CI,#CN/[CI,7,replace = at position 0 with #,flow_matching,0.3,2.0,51,223
23,replace,6.0,+,I,#CN/[CI,#CN/[C+,7,replace I at position 6 with +,flow_matching,0.3,2.0,51,223
24,replace,0.0,C,#,#CN/[C+,CCN/[C+,7,replace # at position 0 with C,flow_matching,0.3,2.0,51,223
25,replace,2.0,[,N,CCN/[C+,CC[/[C+,7,replace N at position 2 with [,flow_matching,0.3,2.0,51,223
26,replace,3.0,C,/,CC[/[C+,CC[C[C+,7,replace / at position 3 with C,flow_matching,0.3,2.0,51,223
27,add,3.0,N,,CC[C[C+,CC[NC[C+,8,add N at position 3,flow_matching,0.3,2.0,51,223
28,replace,7.0,(,+,CC[NC[C+,CC[NC[C(,8,replace + at position 7 with (,flow_matching,0.3,2.0,51,223
29,replace,3.0,C,N,CC[NC[C(,CC[CC[C(,8,replace N at position 3 with C,flow_matching,0.3,2.0,51,223
30,add,8.0,2,,CC[CC[C(,CC[CC[C(2,9,add 2 at position 8,flow_matching,0.3,2.0,51,223
31,remove,0.0,C,,CC[CC[C(2,C[CC[C(2,8,remove C from position 0,flow_matching,0.3,2.0,51,223
32,replace,1.0,C,[,C[CC[C(2,CCCC[C(2,8,replace [ at position 1 with C,flow_matching,0.3,2.0,51,223
33,replace,2.0,[,C,CCCC[C(2,CC[C[C(2,8,replace C at position 2 with [,flow_matching,0.3,2.0,51,223
34,replace,1.0,3,C,CC[C[C(2,C3[C[C(2,8,replace C at position 1 with 3,flow_matching,0.3,2.0,51,223
35,add,7.0,C,,C3[C[C(2,C3[C[C(C2,9,add C at position 7,flow_matching,0.3,2.0,51,223
36,replace,1.0,C,3,C3[C[C(C2,CC[C[C(C2,9,replace 3 at position 1 with C,flow_matching,0.3,2.0,51,223
37,replace,1.0,(,C,CC[C[C(C2,C([C[C(C2,9,replace C at position 1 with (,flow_matching,0.3,2.0,51,223
38,replace,1.0,C,(,C([C[C(C2,CC[C[C(C2,9,replace ( at position 1 with C,flow_matching,0.3,2.0,51,223
39,replace,1.0,N,C,CC[C[C(C2,CN[C[C(C2,9,replace C at position 1 with N,flow_matching,0.3,2.0,51,223
40,replace,1.0,C,N,CN[C[C(C2,CC[C[C(C2,9,replace N at position 1 with C,flow_matching,0.3,2.0,51,223
41,replace,4.0,@,[,CC[C[C(C2,CC[C@C(C2,9,replace [ at position 4 with @,flow_matching,0.3,2.0,51,223
42,add,6.0,-,,CC[C@C(C2,CC[C@C-(C2,10,add - at position 6,flow_matching,0.3,2.0,51,223
43,replace,3.0,1,C,CC[C@C-(C2,CC[1@C-(C2,10,replace C at position 3 with 1,flow_matching,0.3,2.0,51,223
44,add,1.0,O,,CC[1@C-(C2,COC[1@C-(C2,11,add O at position 1,flow_matching,0.3,2.0,51,223
45,add,6.0,c,,COC[1@C-(C2,COC[1@cC-(C2,12,add c at position 6,flow_matching,0.3,2.0,51,223
46,add,1.0,4,,COC[1@cC-(C2,C4OC[1@cC-(C2,13,add 4 at position 1,flow_matching,0.3,2.0,51,223
47,remove,1.0,4,,C4OC[1@cC-(C2,COC[1@cC-(C2,12,remove 4 from position 1,flow_matching,0.3,2.0,51,223
48,add,3.0,3,,COC[1@cC-(C2,COC3[1@cC-(C2,13,add 3 at position 3,flow_matching,0.3,2.0,51,223
49,replace,1.0,C,O,COC3[1@cC-(C2,CCC3[1@cC-(C2,13,replace O at position 1 with C,flow_matching,0.3,2.0,51,223
50,remove,8.0,C,,CCC3[1@cC-(C2,CCC3[1@c-(C2,12,remove C from position 8,flow_matching,0.3,2.0,51,223
51,replace,2.0,[,C,CCC3[1@c-(C2,CC[3[1@c-(C2,12,replace C at position 2 with [,flow_matching,0.3,2.0,51,223
52,add,0.0,o,,CC[3[1@c-(C2,oCC[3[1@c-(C2,13,add o at position 0,flow_matching,0.3,2.0,51,223
53,replace,0.0,C,o,oCC[3[1@c-(C2,CCC[3[1@c-(C2,13,replace o at position 0 with C,flow_matching,0.3,2.0,51,223
54,remove,4.0,3,,CCC[3[1@c-(C2,CCC[[1@c-(C2,12,remove 3 from position 4,flow_matching,0.3,2.0,51,223
55,add,3.0,B,,CCC[[1@c-(C2,CCCB[[1@c-(C2,13,add B at position 3,flow_matching,0.3,2.0,51,223
56,replace,2.0,[,C,CCCB[[1@c-(C2,CC[B[[1@c-(C2,13,replace C at position 2 with [,flow_matching,0.3,2.0,51,223
57,remove,0.0,C,,CC[B[[1@c-(C2,C[B[[1@c-(C2,12,remove C from position 0,flow_matching,0.3,2.0,51,223
58,replace,4.0,7,[,C[B[[1@c-(C2,C[B[71@c-(C2,12,replace [ at position 4 with 7,flow_matching,0.3,2.0,51,223
59,replace,1.0,C,[,C[B[71@c-(C2,CCB[71@c-(C2,12,replace [ at position 1 with C,flow_matching,0.3,2.0,51,223
60,add,5.0,@,,CCB[71@c-(C2,CCB[7@1@c-(C2,13,add @ at position 5,flow_matching,0.3,2.0,51,223
61,replace,2.0,[,B,CCB[7@1@c-(C2,CC[[7@1@c-(C2,13,replace B at position 2 with [,flow_matching,0.3,2.0,51,223
62,replace,3.0,C,[,CC[[7@1@c-(C2,CC[C7@1@c-(C2,13,replace [ at position 3 with C,flow_matching,0.3,2.0,51,223
63,replace,12.0,\,2,CC[C7@1@c-(C2,CC[C7@1@c-(C\,13,replace 2 at position 12 with \,flow_matching,0.3,2.0,51,223
64,replace,4.0,@,7,CC[C7@1@c-(C\,CC[C@@1@c-(C\,13,replace 7 at position 4 with @,flow_matching,0.3,2.0,51,223
65,remove,10.0,(,,CC[C@@1@c-(C\,CC[C@@1@c-C\,12,remove ( from position 10,flow_matching,0.3,2.0,51,223
66,remove,10.0,C,,CC[C@@1@c-C\,CC[C@@1@c-\,11,remove C from position 10,flow_matching,0.3,2.0,51,223
67,add,2.0,H,,CC[C@@1@c-\,CCH[C@@1@c-\,12,add H at position 2,flow_matching,0.3,2.0,51,223
68,add,4.0,l,,CCH[C@@1@c-\,CCH[lC@@1@c-\,13,add l at position 4,flow_matching,0.3,2.0,51,223
69,replace,2.0,[,H,CCH[lC@@1@c-\,CC[[lC@@1@c-\,13,replace H at position 2 with [,flow_matching,0.3,2.0,51,223
70,add,9.0,=,,CC[[lC@@1@c-\,CC[[lC@@1=@c-\,14,add = at position 9,flow_matching,0.3,2.0,51,223
71,remove,6.0,@,,CC[[lC@@1=@c-\,CC[[lC@1=@c-\,13,remove @ from position 6,flow_matching,0.3,2.0,51,223
72,replace,3.0,C,[,CC[[lC@1=@c-\,CC[ClC@1=@c-\,13,replace [ at position 3 with C,flow_matching,0.3,2.0,51,223
73,remove,0.0,C,,CC[ClC@1=@c-\,C[ClC@1=@c-\,12,remove C from position 0,flow_matching,0.3,2.0,51,223
74,replace,11.0,-,\,C[ClC@1=@c-\,C[ClC@1=@c--,12,replace \ at position 11 with -,flow_matching,0.3,2.0,51,223
75,replace,1.0,C,[,C[ClC@1=@c--,CCClC@1=@c--,12,replace [ at position 1 with C,flow_matching,0.3,2.0,51,223
76,replace,2.0,[,C,CCClC@1=@c--,CC[lC@1=@c--,12,replace C at position 2 with [,flow_matching,0.3,2.0,51,223
77,remove,10.0,-,,CC[lC@1=@c--,CC[lC@1=@c-,11,remove - from position 10,flow_matching,0.3,2.0,51,223
78,replace,3.0,C,l,CC[lC@1=@c-,CC[CC@1=@c-,11,replace l at position 3 with C,flow_matching,0.3,2.0,51,223
79,remove,0.0,C,,CC[CC@1=@c-,C[CC@1=@c-,10,remove C from position 0,flow_matching,0.3,2.0,51,223
80,add,8.0,(,,C[CC@1=@c-,C[CC@1=@(c-,11,add ( at position 8,flow_matching,0.3,2.0,51,223
81,replace,1.0,C,[,C[CC@1=@(c-,CCCC@1=@(c-,11,replace [ at position 1 with C,flow_matching,0.3,2.0,51,223
82,replace,2.0,[,C,CCCC@1=@(c-,CC[C@1=@(c-,11,replace C at position 2 with [,flow_matching,0.3,2.0,51,223
83,remove,4.0,@,,CC[C@1=@(c-,CC[C1=@(c-,10,remove @ from position 4,flow_matching,0.3,2.0,51,223
84,replace,4.0,@,1,CC[C1=@(c-,CC[C@=@(c-,10,replace 1 at position 4 with @,flow_matching,0.3,2.0,51,223
85,add,5.0,r,,CC[C@=@(c-,CC[C@r=@(c-,11,add r at position 5,flow_matching,0.3,2.0,51,223
86,add,6.0,+,,CC[C@r=@(c-,CC[C@r+=@(c-,12,add + at position 6,flow_matching,0.3,2.0,51,223
87,replace,5.0,@,r,CC[C@r+=@(c-,CC[C@@+=@(c-,12,replace r at position 5 with @,flow_matching,0.3,2.0,51,223
88,replace,6.0,H,+,CC[C@@+=@(c-,CC[C@@H=@(c-,12,replace + at position 6 with H,flow_matching,0.3,2.0,51,223
89,add,7.0,@,,CC[C@@H=@(c-,CC[C@@H@=@(c-,13,add @ at position 7,flow_matching,0.3,2.0,51,223
90,replace,9.0,F,@,CC[C@@H@=@(c-,CC[C@@H@=F(c-,13,replace @ at position 9 with F,flow_matching,0.3,2.0,51,223
91,replace,7.0,],@,CC[C@@H@=F(c-,CC[C@@H]=F(c-,13,replace @ at position 7 with ],flow_matching,0.3,2.0,51,223
92,replace,3.0,),C,CC[C@@H]=F(c-,CC[)@@H]=F(c-,13,replace C at position 3 with ),flow_matching,0.3,2.0,51,223
93,remove,5.0,@,,CC[)@@H]=F(c-,CC[)@H]=F(c-,12,remove @ from position 5,flow_matching,0.3,2.0,51,223
94,remove,10.0,c,,CC[)@H]=F(c-,CC[)@H]=F(-,11,remove c from position 10,flow_matching,0.3,2.0,51,223
95,replace,6.0,H,],CC[)@H]=F(-,CC[)@HH=F(-,11,replace ] at position 6 with H,flow_matching,0.3,2.0,51,223
96,remove,3.0,),,CC[)@HH=F(-,CC[@HH=F(-,10,remove ) from position 3,flow_matching,0.3,2.0,51,223
97,add,7.0,n,,CC[@HH=F(-,CC[@HH=nF(-,11,add n at position 7,flow_matching,0.3,2.0,51,223
98,add,2.0,r,,CC[@HH=nF(-,CCr[@HH=nF(-,12,add r at position 2,flow_matching,0.3,2.0,51,223
99,add,1.0,6,,CCr[@HH=nF(-,C6Cr[@HH=nF(-,13,add 6 at position 1,flow_matching,0.3,2.0,51,223
100,replace,1.0,\,6,C6Cr[@HH=nF(-,C\Cr[@HH=nF(-,13,replace 6 at position 1 with \,flow_matching,0.3,2.0,51,223
101,add,2.0,2,,C\Cr[@HH=nF(-,C\2Cr[@HH=nF(-,14,add 2 at position 2,flow_matching,0.3,2.0,51,223
102,add,1.0,S,,C\2Cr[@HH=nF(-,CS\2Cr[@HH=nF(-,15,add S at position 1,flow_matching,0.3,2.0,51,223
103,replace,1.0,C,S,CS\2Cr[@HH=nF(-,CC\2Cr[@HH=nF(-,15,replace S at position 1 with C,flow_matching,0.3,2.0,51,223
104,replace,2.0,[,\,CC\2Cr[@HH=nF(-,CC[2Cr[@HH=nF(-,15,replace \ at position 2 with [,flow_matching,0.3,2.0,51,223
105,replace,3.0,C,2,CC[2Cr[@HH=nF(-,CC[CCr[@HH=nF(-,15,replace 2 at position 3 with C,flow_matching,0.3,2.0,51,223
106,remove,10.0,=,,CC[CCr[@HH=nF(-,CC[CCr[@HHnF(-,14,remove = from position 10,flow_matching,0.3,2.0,51,223
107,replace,1.0,\,C,CC[CCr[@HHnF(-,C\[CCr[@HHnF(-,14,replace C at position 1 with \,flow_matching,0.3,2.0,51,223
108,replace,1.0,2,\,C\[CCr[@HHnF(-,C2[CCr[@HHnF(-,14,replace \ at position 1 with 2,flow_matching,0.3,2.0,51,223
109,replace,1.0,C,2,C2[CCr[@HHnF(-,CC[CCr[@HHnF(-,14,replace 2 at position 1 with C,flow_matching,0.3,2.0,51,223
110,add,2.0,],,CC[CCr[@HHnF(-,CC][CCr[@HHnF(-,15,add ] at position 2,flow_matching,0.3,2.0,51,223
111,remove,12.0,F,,CC][CCr[@HHnF(-,CC][CCr[@HHn(-,14,remove F from position 12,flow_matching,0.3,2.0,51,223
112,replace,2.0,[,],CC][CCr[@HHn(-,CC[[CCr[@HHn(-,14,replace ] at position 2 with [,flow_matching,0.3,2.0,51,223
113,remove,13.0,-,,CC[[CCr[@HHn(-,CC[[CCr[@HHn(,13,remove - from position 13,flow_matching,0.3,2.0,51,223
114,add,8.0,2,,CC[[CCr[@HHn(,CC[[CCr[2@HHn(,14,add 2 at position 8,flow_matching,0.3,2.0,51,223
115,replace,3.0,C,[,CC[[CCr[2@HHn(,CC[CCCr[2@HHn(,14,replace [ at position 3 with C,flow_matching,0.3,2.0,51,223
116,add,1.0,(,,CC[CCCr[2@HHn(,C(C[CCCr[2@HHn(,15,add ( at position 1,flow_matching,0.3,2.0,51,223
117,add,2.0,l,,C(C[CCCr[2@HHn(,C(lC[CCCr[2@HHn(,16,add l at position 2,flow_matching,0.3,2.0,51,223
118,replace,2.0,#,l,C(lC[CCCr[2@HHn(,C(#C[CCCr[2@HHn(,16,replace l at position 2 with #,flow_matching,0.3,2.0,51,223
119,replace,1.0,C,(,C(#C[CCCr[2@HHn(,CC#C[CCCr[2@HHn(,16,replace ( at position 1 with C,flow_matching,0.3,2.0,51,223
120,replace,9.0,4,[,CC#C[CCCr[2@HHn(,CC#C[CCCr42@HHn(,16,replace [ at position 9 with 4,flow_matching,0.3,2.0,51,223
121,replace,2.0,[,#,CC#C[CCCr42@HHn(,CC[C[CCCr42@HHn(,16,replace # at position 2 with [,flow_matching,0.3,2.0,51,223
122,remove,8.0,r,,CC[C[CCCr42@HHn(,CC[C[CCC42@HHn(,15,remove r from position 8,flow_matching,0.3,2.0,51,223
123,replace,4.0,@,[,CC[C[CCC42@HHn(,CC[C@CCC42@HHn(,15,replace [ at position 4 with @,flow_matching,0.3,2.0,51,223
124,replace,8.0,@,4,CC[C@CCC42@HHn(,CC[C@CCC@2@HHn(,15,replace 4 at position 8 with @,flow_matching,0.3,2.0,51,223
125,remove,12.0,H,,CC[C@CCC@2@HHn(,CC[C@CCC@2@Hn(,14,remove H from position 12,flow_matching,0.3,2.0,51,223
126,replace,9.0,+,2,CC[C@CCC@2@Hn(,CC[C@CCC@+@Hn(,14,replace 2 at position 9 with +,flow_matching,0.3,2.0,51,223
127,remove,8.0,@,,CC[C@CCC@+@Hn(,CC[C@CCC+@Hn(,13,remove @ from position 8,flow_matching,0.3,2.0,51,223
128,replace,5.0,@,C,CC[C@CCC+@Hn(,CC[C@@CC+@Hn(,13,replace C at position 5 with @,flow_matching,0.3,2.0,51,223
129,replace,10.0,5,H,CC[C@@CC+@Hn(,CC[C@@CC+@5n(,13,replace H at position 10 with 5,flow_matching,0.3,2.0,51,223
130,remove,3.0,C,,CC[C@@CC+@5n(,CC[@@CC+@5n(,12,remove C from position 3,flow_matching,0.3,2.0,51,223
131,remove,4.0,@,,CC[@@CC+@5n(,CC[@CC+@5n(,11,remove @ from position 4,flow_matching,0.3,2.0,51,223
132,replace,8.0,6,5,CC[@CC+@5n(,CC[@CC+@6n(,11,replace 5 at position 8 with 6,flow_matching,0.3,2.0,51,223
133,replace,3.0,C,@,CC[@CC+@6n(,CC[CCC+@6n(,11,replace @ at position 3 with C,flow_matching,0.3,2.0,51,223
134,remove,0.0,C,,CC[CCC+@6n(,C[CCC+@6n(,10,remove C from position 0,flow_matching,0.3,2.0,51,223
135,remove,5.0,+,,C[CCC+@6n(,C[CCC@6n(,9,remove + from position 5,flow_matching,0.3,2.0,51,223
136,replace,1.0,C,[,C[CCC@6n(,CCCCC@6n(,9,replace [ at position 1 with C,flow_matching,0.3,2.0,51,223
137,replace,2.0,[,C,CCCCC@6n(,CC[CC@6n(,9,replace C at position 2 with [,flow_matching,0.3,2.0,51,223
138,replace,4.0,@,C,CC[CC@6n(,CC[C@@6n(,9,replace C at position 4 with @,flow_matching,0.3,2.0,51,223
139,replace,4.0,B,@,CC[C@@6n(,CC[CB@6n(,9,replace @ at position 4 with B,flow_matching,0.3,2.0,51,223
140,replace,4.0,@,B,CC[CB@6n(,CC[C@@6n(,9,replace B at position 4 with @,flow_matching,0.3,2.0,51,223
141,add,2.0,[,,CC[C@@6n(,CC[[C@@6n(,10,add [ at position 2,flow_matching,0.3,2.0,51,223
142,replace,9.0,F,(,CC[[C@@6n(,CC[[C@@6nF,10,replace ( at position 9 with F,flow_matching,0.3,2.0,51,223
143,replace,3.0,C,[,CC[[C@@6nF,CC[CC@@6nF,10,replace [ at position 3 with C,flow_matching,0.3,2.0,51,223
144,replace,4.0,@,C,CC[CC@@6nF,CC[C@@@6nF,10,replace C at position 4 with @,flow_matching,0.3,2.0,51,223
145,remove,8.0,n,,CC[C@@@6nF,CC[C@@@6F,9,remove n from position 8,flow_matching,0.3,2.0,51,223
146,replace,4.0,4,@,CC[C@@@6F,CC[C4@@6F,9,replace @ at position 4 with 4,flow_matching,0.3,2.0,51,223
147,replace,4.0,@,4,CC[C4@@6F,CC[C@@@6F,9,replace 4 at position 4 with @,flow_matching,0.3,2.0,51,223
148,remove,1.0,C,,CC[C@@@6F,C[C@@@6F,8,remove C from position 1,flow_matching,0.3,2.0,51,223
149,add,4.0,],,C[C@@@6F,C[C@]@@6F,9,add ] at position 4,flow_matching,0.3,2.0,51,223
150,remove,1.0,[,,C[C@]@@6F,CC@]@@6F,8,remove [ from position 1,flow_matching,0.3,2.0,51,223
151,replace,2.0,[,@,CC@]@@6F,CC[]@@6F,8,replace @ at position 2 with [,flow_matching,0.3,2.0,51,223
152,replace,4.0,4,@,CC[]@@6F,CC[]4@6F,8,replace @ at position 4 with 4,flow_matching,0.3,2.0,51,223
153,add,6.0,7,,CC[]4@6F,CC[]4@76F,9,add 7 at position 6,flow_matching,0.3,2.0,51,223
154,replace,3.0,C,],CC[]4@76F,CC[C4@76F,9,replace ] at position 3 with C,flow_matching,0.3,2.0,51,223
155,replace,4.0,@,4,CC[C4@76F,CC[C@@76F,9,replace 4 at position 4 with @,flow_matching,0.3,2.0,51,223
156,replace,6.0,H,7,CC[C@@76F,CC[C@@H6F,9,replace 7 at position 6 with H,flow_matching,0.3,2.0,51,223
157,add,9.0,S,,CC[C@@H6F,CC[C@@H6FS,10,add S at position 9,flow_matching,0.3,2.0,51,223
158,remove,8.0,F,,CC[C@@H6FS,CC[C@@H6S,9,remove F from position 8,flow_matching,0.3,2.0,51,223
159,replace,7.0,],6,CC[C@@H6S,CC[C@@H]S,9,replace 6 at position 7 with ],flow_matching,0.3,2.0,51,223
160,add,0.0,/,,CC[C@@H]S,/CC[C@@H]S,10,add / at position 0,flow_matching,0.3,2.0,51,223
161,add,9.0,B,,/CC[C@@H]S,/CC[C@@H]BS,11,add B at position 9,flow_matching,0.3,2.0,51,223
162,add,5.0,4,,/CC[C@@H]BS,/CC[C4@@H]BS,12,add 4 at position 5,flow_matching,0.3,2.0,51,223
163,add,12.0,B,,/CC[C4@@H]BS,/CC[C4@@H]BSB,13,add B at position 12,flow_matching,0.3,2.0,51,223
164,replace,0.0,C,/,/CC[C4@@H]BSB,CCC[C4@@H]BSB,13,replace / at position 0 with C,flow_matching,0.3,2.0,51,223
165,add,9.0,(,,CCC[C4@@H]BSB,CCC[C4@@H(]BSB,14,add ( at position 9,flow_matching,0.3,2.0,51,223
166,add,5.0,6,,CCC[C4@@H(]BSB,CCC[C64@@H(]BSB,15,add 6 at position 5,flow_matching,0.3,2.0,51,223
167,replace,0.0,/,C,CCC[C64@@H(]BSB,/CC[C64@@H(]BSB,15,replace C at position 0 with /,flow_matching,0.3,2.0,51,223
168,add,5.0,=,,/CC[C64@@H(]BSB,/CC[C=64@@H(]BSB,16,add = at position 5,flow_matching,0.3,2.0,51,223
169,replace,0.0,C,/,/CC[C=64@@H(]BSB,CCC[C=64@@H(]BSB,16,replace / at position 0 with C,flow_matching,0.3,2.0,51,223
170,remove,4.0,C,,CCC[C=64@@H(]BSB,CCC[=64@@H(]BSB,15,remove C from position 4,flow_matching,0.3,2.0,51,223
171,replace,13.0,r,S,CCC[=64@@H(]BSB,CCC[=64@@H(]BrB,15,replace S at position 13 with r,flow_matching,0.3,2.0,51,223
172,replace,2.0,F,C,CCC[=64@@H(]BrB,CCF[=64@@H(]BrB,15,replace C at position 2 with F,flow_matching,0.3,2.0,51,223
173,replace,2.0,[,F,CCF[=64@@H(]BrB,CC[[=64@@H(]BrB,15,replace F at position 2 with [,flow_matching,0.3,2.0,51,223
174,add,3.0,n,,CC[[=64@@H(]BrB,CC[n[=64@@H(]BrB,16,add n at position 3,flow_matching,0.3,2.0,51,223
175,remove,9.0,@,,CC[n[=64@@H(]BrB,CC[n[=64@H(]BrB,15,remove @ from position 9,flow_matching,0.3,2.0,51,223
176,replace,3.0,C,n,CC[n[=64@H(]BrB,CC[C[=64@H(]BrB,15,replace n at position 3 with C,flow_matching,0.3,2.0,51,223
177,replace,4.0,@,[,CC[C[=64@H(]BrB,CC[C@=64@H(]BrB,15,replace [ at position 4 with @,flow_matching,0.3,2.0,51,223
178,replace,5.0,@,=,CC[C@=64@H(]BrB,CC[C@@64@H(]BrB,15,replace = at position 5 with @,flow_matching,0.3,2.0,51,223
179,replace,6.0,H,6,CC[C@@64@H(]BrB,CC[C@@H4@H(]BrB,15,replace 6 at position 6 with H,flow_matching,0.3,2.0,51,223
180,replace,7.0,],4,CC[C@@H4@H(]BrB,CC[C@@H]@H(]BrB,15,replace 4 at position 7 with ],flow_matching,0.3,2.0,51,223
181,replace,8.0,(,@,CC[C@@H]@H(]BrB,CC[C@@H](H(]BrB,15,replace @ at position 8 with (,flow_matching,0.3,2.0,51,223
182,replace,9.0,C,H,CC[C@@H](H(]BrB,CC[C@@H](C(]BrB,15,replace H at position 9 with C,flow_matching,0.3,2.0,51,223
183,replace,10.0,),(,CC[C@@H](C(]BrB,CC[C@@H](C)]BrB,15,replace ( at position 10 with ),flow_matching,0.3,2.0,51,223
184,replace,11.0,c,],CC[C@@H](C)]BrB,CC[C@@H](C)cBrB,15,replace ] at position 11 with c,flow_matching,0.3,2.0,51,223
185,replace,12.0,1,B,CC[C@@H](C)cBrB,CC[C@@H](C)c1rB,15,replace B at position 12 with 1,flow_matching,0.3,2.0,51,223
186,replace,13.0,c,r,CC[C@@H](C)c1rB,CC[C@@H](C)c1cB,15,replace r at position 13 with c,flow_matching,0.3,2.0,51,223
187,replace,14.0,c,B,CC[C@@H](C)c1cB,CC[C@@H](C)c1cc,15,replace B at position 14 with c,flow_matching,0.3,2.0,51,223
188,add,15.0,c,,CC[C@@H](C)c1cc,CC[C@@H](C)c1ccc,16,add c at position 15,flow_matching,0.3,2.0,51,223
189,add,16.0,c,,CC[C@@H](C)c1ccc,CC[C@@H](C)c1cccc,17,add c at position 16,flow_matching,0.3,2.0,51,223
190,add,17.0,c,,CC[C@@H](C)c1cccc,CC[C@@H](C)c1ccccc,18,add c at position 17,flow_matching,0.3,2.0,51,223
191,add,18.0,1,,CC[C@@H](C)c1ccccc,CC[C@@H](C)c1ccccc1,19,add 1 at position 18,flow_matching,0.3,2.0,51,223
192,add,19.0,N,,CC[C@@H](C)c1ccccc1,CC[C@@H](C)c1ccccc1N,20,add N at position 19,flow_matching,0.3,2.0,51,223
193,add,20.0,1,,CC[C@@H](C)c1ccccc1N,CC[C@@H](C)c1ccccc1N1,21,add 1 at position 20,flow_matching,0.3,2.0,51,223
194,add,21.0,C,,CC[C@@H](C)c1ccccc1N1,CC[C@@H](C)c1ccccc1N1C,22,add C at position 21,flow_matching,0.3,2.0,51,223
195,add,22.0,[,,CC[C@@H](C)c1ccccc1N1C,CC[C@@H](C)c1ccccc1N1C[,23,add [ at position 22,flow_matching,0.3,2.0,51,223
196,add,23.0,C,,CC[C@@H](C)c1ccccc1N1C[,CC[C@@H](C)c1ccccc1N1C[C,24,add C at position 23,flow_matching,0.3,2.0,51,223
197,add,24.0,@,,CC[C@@H](C)c1ccccc1N1C[C,CC[C@@H](C)c1ccccc1N1C[C@,25,add @ at position 24,flow_matching,0.3,2.0,51,223
198,add,25.0,H,,CC[C@@H](C)c1ccccc1N1C[C@,CC[C@@H](C)c1ccccc1N1C[C@H,26,add H at position 25,flow_matching,0.3,2.0,51,223
199,add,26.0,],,CC[C@@H](C)c1ccccc1N1C[C@H,CC[C@@H](C)c1ccccc1N1C[C@H],27,add ] at position 26,flow_matching,0.3,2.0,51,223
200,add,27.0,(,,CC[C@@H](C)c1ccccc1N1C[C@H],CC[C@@H](C)c1ccccc1N1C[C@H](,28,add ( at position 27,flow_matching,0.3,2.0,51,223
201,add,28.0,C,,CC[C@@H](C)c1ccccc1N1C[C@H](,CC[C@@H](C)c1ccccc1N1C[C@H](C,29,add C at position 28,flow_matching,0.3,2.0,51,223
202,add,29.0,(,,CC[C@@H](C)c1ccccc1N1C[C@H](C,CC[C@@H](C)c1ccccc1N1C[C@H](C(,30,add ( at position 29,flow_matching,0.3,2.0,51,223
203,add,30.0,=,,CC[C@@H](C)c1ccccc1N1C[C@H](C(,CC[C@@H](C)c1ccccc1N1C[C@H](C(=,31,add = at position 30,flow_matching,0.3,2.0,51,223
204,add,31.0,O,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O,32,add O at position 31,flow_matching,0.3,2.0,51,223
205,add,32.0,),,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O),33,add ) at position 32,flow_matching,0.3,2.0,51,223
206,add,33.0,N,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O),CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N,34,add N at position 33,flow_matching,0.3,2.0,51,223
207,add,34.0,2,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2,35,add 2 at position 34,flow_matching,0.3,2.0,51,223
208,add,35.0,C,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2C,36,add C at position 35,flow_matching,0.3,2.0,51,223
209,add,36.0,C,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2C,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CC,37,add C at position 36,flow_matching,0.3,2.0,51,223
210,add,37.0,N,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CC,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN,38,add N at position 37,flow_matching,0.3,2.0,51,223
211,add,38.0,(,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(,39,add ( at position 38,flow_matching,0.3,2.0,51,223
212,add,39.0,C,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C,40,add C at position 39,flow_matching,0.3,2.0,51,223
213,add,40.0,),,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C),41,add ) at position 40,flow_matching,0.3,2.0,51,223
214,add,41.0,C,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C),CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)C,42,add C at position 41,flow_matching,0.3,2.0,51,223
215,add,42.0,C,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)C,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC,43,add C at position 42,flow_matching,0.3,2.0,51,223
216,add,43.0,2,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2,44,add 2 at position 43,flow_matching,0.3,2.0,51,223
217,add,44.0,),,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2),45,add ) at position 44,flow_matching,0.3,2.0,51,223
218,add,45.0,C,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2),CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)C,46,add C at position 45,flow_matching,0.3,2.0,51,223
219,add,46.0,C,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)C,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)CC,47,add C at position 46,flow_matching,0.3,2.0,51,223
220,add,47.0,1,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)CC,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)CC1,48,add 1 at position 47,flow_matching,0.3,2.0,51,223
221,add,48.0,=,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)CC1,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)CC1=,49,add = at position 48,flow_matching,0.3,2.0,51,223
222,add,49.0,O,,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)CC1=,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)CC1=O,50,add O at position 49,flow_matching,0.3,2.0,51,223
223,add,50.0,"
",,CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)CC1=O,"CC[C@@H](C)c1ccccc1N1C[C@H](C(=O)N2CCN(C)CC2)CC1=O
",51,"add 
 at position 50",flow_matching,0.3,2.0,51,223
