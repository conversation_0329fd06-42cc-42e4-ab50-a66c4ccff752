step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,46,199
1,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,46,199
2,add,1.0,C,,N,NC,2,add C at position 1,flow_matching,0.3,2.0,46,199
3,add,2.0,-,,NC,NC-,3,add - at position 2,flow_matching,0.3,2.0,46,199
4,replace,2.0,n,-,NC-,NCn,3,replace - at position 2 with n,flow_matching,0.3,2.0,46,199
5,add,1.0,s,,NCn,NsCn,4,add s at position 1,flow_matching,0.3,2.0,46,199
6,replace,1.0,<PERSON>,s,NsCn,NCCn,4,replace s at position 1 with <PERSON>,flow_matching,0.3,2.0,46,199
7,replace,1.0,s,C,NCCn,NsCn,4,replace <PERSON> at position 1 with s,flow_matching,0.3,2.0,46,199
8,replace,1.0,(,s,NsCn,N(Cn,4,replace s at position 1 with (,flow_matching,0.3,2.0,46,199
9,add,0.0,#,,N(Cn,#N(Cn,5,add # at position 0,flow_matching,0.3,2.0,46,199
10,remove,0.0,#,,#N(Cn,N(Cn,4,remove # from position 0,flow_matching,0.3,2.0,46,199
11,remove,0.0,N,,N(Cn,(Cn,3,remove N from position 0,flow_matching,0.3,2.0,46,199
12,add,0.0,@,,(Cn,@(Cn,4,add @ at position 0,flow_matching,0.3,2.0,46,199
13,replace,0.0,N,@,@(Cn,N(Cn,4,replace @ at position 0 with N,flow_matching,0.3,2.0,46,199
14,remove,3.0,n,,N(Cn,N(C,3,remove n from position 3,flow_matching,0.3,2.0,46,199
15,add,2.0,@,,N(C,N(@C,4,add @ at position 2,flow_matching,0.3,2.0,46,199
16,remove,0.0,N,,N(@C,(@C,3,remove N from position 0,flow_matching,0.3,2.0,46,199
17,add,1.0,l,,(@C,(l@C,4,add l at position 1,flow_matching,0.3,2.0,46,199
18,replace,1.0,[,l,(l@C,([@C,4,replace l at position 1 with [,flow_matching,0.3,2.0,46,199
19,remove,1.0,[,,([@C,(@C,3,remove [ from position 1,flow_matching,0.3,2.0,46,199
20,replace,0.0,2,(,(@C,2@C,3,replace ( at position 0 with 2,flow_matching,0.3,2.0,46,199
21,add,2.0,n,,2@C,2@nC,4,add n at position 2,flow_matching,0.3,2.0,46,199
22,add,1.0,=,,2@nC,2=@nC,5,add = at position 1,flow_matching,0.3,2.0,46,199
23,remove,2.0,@,,2=@nC,2=nC,4,remove @ from position 2,flow_matching,0.3,2.0,46,199
24,add,2.0,s,,2=nC,2=snC,5,add s at position 2,flow_matching,0.3,2.0,46,199
25,remove,4.0,C,,2=snC,2=sn,4,remove C from position 4,flow_matching,0.3,2.0,46,199
26,replace,3.0,\,n,2=sn,2=s\,4,replace n at position 3 with \,flow_matching,0.3,2.0,46,199
27,add,2.0,c,,2=s\,2=cs\,5,add c at position 2,flow_matching,0.3,2.0,46,199
28,add,5.0,5,,2=cs\,2=cs\5,6,add 5 at position 5,flow_matching,0.3,2.0,46,199
29,remove,5.0,5,,2=cs\5,2=cs\,5,remove 5 from position 5,flow_matching,0.3,2.0,46,199
30,replace,0.0,N,2,2=cs\,N=cs\,5,replace 2 at position 0 with N,flow_matching,0.3,2.0,46,199
31,replace,1.0,C,=,N=cs\,NCcs\,5,replace = at position 1 with C,flow_matching,0.3,2.0,46,199
32,remove,2.0,c,,NCcs\,NCs\,4,remove c from position 2,flow_matching,0.3,2.0,46,199
33,replace,2.0,(,s,NCs\,NC(\,4,replace s at position 2 with (,flow_matching,0.3,2.0,46,199
34,remove,3.0,\,,NC(\,NC(,3,remove \ from position 3,flow_matching,0.3,2.0,46,199
35,add,3.0,=,,NC(,NC(=,4,add = at position 3,flow_matching,0.3,2.0,46,199
36,add,4.0,O,,NC(=,NC(=O,5,add O at position 4,flow_matching,0.3,2.0,46,199
37,add,5.0,),,NC(=O,NC(=O),6,add ) at position 5,flow_matching,0.3,2.0,46,199
38,add,2.0,@,,NC(=O),NC@(=O),7,add @ at position 2,flow_matching,0.3,2.0,46,199
39,replace,2.0,(,@,NC@(=O),NC((=O),7,replace @ at position 2 with (,flow_matching,0.3,2.0,46,199
40,replace,2.0,4,(,NC((=O),NC4(=O),7,replace ( at position 2 with 4,flow_matching,0.3,2.0,46,199
41,remove,1.0,C,,NC4(=O),N4(=O),6,remove C from position 1,flow_matching,0.3,2.0,46,199
42,replace,1.0,C,4,N4(=O),NC(=O),6,replace 4 at position 1 with C,flow_matching,0.3,2.0,46,199
43,remove,4.0,O,,NC(=O),NC(=),5,remove O from position 4,flow_matching,0.3,2.0,46,199
44,remove,0.0,N,,NC(=),C(=),4,remove N from position 0,flow_matching,0.3,2.0,46,199
45,replace,0.0,N,C,C(=),N(=),4,replace C at position 0 with N,flow_matching,0.3,2.0,46,199
46,replace,0.0,(,N,N(=),((=),4,replace N at position 0 with (,flow_matching,0.3,2.0,46,199
47,replace,0.0,N,(,((=),N(=),4,replace ( at position 0 with N,flow_matching,0.3,2.0,46,199
48,replace,1.0,C,(,N(=),NC=),4,replace ( at position 1 with C,flow_matching,0.3,2.0,46,199
49,add,0.0,N,,NC=),NNC=),5,add N at position 0,flow_matching,0.3,2.0,46,199
50,add,3.0,H,,NNC=),NNCH=),6,add H at position 3,flow_matching,0.3,2.0,46,199
51,add,5.0,I,,NNCH=),NNCH=I),7,add I at position 5,flow_matching,0.3,2.0,46,199
52,add,6.0,5,,NNCH=I),NNCH=I5),8,add 5 at position 6,flow_matching,0.3,2.0,46,199
53,add,0.0,2,,NNCH=I5),2NNCH=I5),9,add 2 at position 0,flow_matching,0.3,2.0,46,199
54,replace,0.0,N,2,2NNCH=I5),NNNCH=I5),9,replace 2 at position 0 with N,flow_matching,0.3,2.0,46,199
55,replace,1.0,C,N,NNNCH=I5),NCNCH=I5),9,replace N at position 1 with C,flow_matching,0.3,2.0,46,199
56,remove,6.0,I,,NCNCH=I5),NCNCH=5),8,remove I from position 6,flow_matching,0.3,2.0,46,199
57,replace,4.0,1,H,NCNCH=5),NCNC1=5),8,replace H at position 4 with 1,flow_matching,0.3,2.0,46,199
58,remove,4.0,1,,NCNC1=5),NCNC=5),7,remove 1 from position 4,flow_matching,0.3,2.0,46,199
59,remove,4.0,=,,NCNC=5),NCNC5),6,remove = from position 4,flow_matching,0.3,2.0,46,199
60,add,5.0,#,,NCNC5),NCNC5#),7,add # at position 5,flow_matching,0.3,2.0,46,199
61,replace,5.0,B,#,NCNC5#),NCNC5B),7,replace # at position 5 with B,flow_matching,0.3,2.0,46,199
62,replace,2.0,7,N,NCNC5B),NC7C5B),7,replace N at position 2 with 7,flow_matching,0.3,2.0,46,199
63,add,1.0,o,,NC7C5B),NoC7C5B),8,add o at position 1,flow_matching,0.3,2.0,46,199
64,add,6.0,/,,NoC7C5B),NoC7C5/B),9,add / at position 6,flow_matching,0.3,2.0,46,199
65,replace,1.0,C,o,NoC7C5/B),NCC7C5/B),9,replace o at position 1 with C,flow_matching,0.3,2.0,46,199
66,replace,2.0,(,C,NCC7C5/B),NC(7C5/B),9,replace C at position 2 with (,flow_matching,0.3,2.0,46,199
67,remove,7.0,B,,NC(7C5/B),NC(7C5/),8,remove B from position 7,flow_matching,0.3,2.0,46,199
68,replace,5.0,-,5,NC(7C5/),NC(7C-/),8,replace 5 at position 5 with -,flow_matching,0.3,2.0,46,199
69,replace,1.0,1,C,NC(7C-/),N1(7C-/),8,replace C at position 1 with 1,flow_matching,0.3,2.0,46,199
70,add,2.0,/,,N1(7C-/),N1/(7C-/),9,add / at position 2,flow_matching,0.3,2.0,46,199
71,replace,3.0,1,(,N1/(7C-/),N1/17C-/),9,replace ( at position 3 with 1,flow_matching,0.3,2.0,46,199
72,remove,0.0,N,,N1/17C-/),1/17C-/),8,remove N from position 0,flow_matching,0.3,2.0,46,199
73,add,3.0,5,,1/17C-/),1/157C-/),9,add 5 at position 3,flow_matching,0.3,2.0,46,199
74,replace,0.0,N,1,1/157C-/),N/157C-/),9,replace 1 at position 0 with N,flow_matching,0.3,2.0,46,199
75,remove,3.0,5,,N/157C-/),N/17C-/),8,remove 5 from position 3,flow_matching,0.3,2.0,46,199
76,replace,1.0,C,/,N/17C-/),NC17C-/),8,replace / at position 1 with C,flow_matching,0.3,2.0,46,199
77,replace,2.0,(,1,NC17C-/),NC(7C-/),8,replace 1 at position 2 with (,flow_matching,0.3,2.0,46,199
78,replace,3.0,=,7,NC(7C-/),NC(=C-/),8,replace 7 at position 3 with =,flow_matching,0.3,2.0,46,199
79,replace,4.0,O,C,NC(=C-/),NC(=O-/),8,replace C at position 4 with O,flow_matching,0.3,2.0,46,199
80,replace,4.0,#,O,NC(=O-/),NC(=#-/),8,replace O at position 4 with #,flow_matching,0.3,2.0,46,199
81,replace,4.0,O,#,NC(=#-/),NC(=O-/),8,replace # at position 4 with O,flow_matching,0.3,2.0,46,199
82,remove,4.0,O,,NC(=O-/),NC(=-/),7,remove O from position 4,flow_matching,0.3,2.0,46,199
83,replace,1.0,S,C,NC(=-/),NS(=-/),7,replace C at position 1 with S,flow_matching,0.3,2.0,46,199
84,replace,1.0,C,S,NS(=-/),NC(=-/),7,replace S at position 1 with C,flow_matching,0.3,2.0,46,199
85,add,5.0,1,,NC(=-/),NC(=-1/),8,add 1 at position 5,flow_matching,0.3,2.0,46,199
86,replace,3.0,O,=,NC(=-1/),NC(O-1/),8,replace = at position 3 with O,flow_matching,0.3,2.0,46,199
87,add,2.0,2,,NC(O-1/),NC2(O-1/),9,add 2 at position 2,flow_matching,0.3,2.0,46,199
88,remove,0.0,N,,NC2(O-1/),C2(O-1/),8,remove N from position 0,flow_matching,0.3,2.0,46,199
89,replace,2.0,2,(,C2(O-1/),C22O-1/),8,replace ( at position 2 with 2,flow_matching,0.3,2.0,46,199
90,replace,0.0,N,C,C22O-1/),N22O-1/),8,replace C at position 0 with N,flow_matching,0.3,2.0,46,199
91,remove,5.0,1,,N22O-1/),N22O-/),7,remove 1 from position 5,flow_matching,0.3,2.0,46,199
92,replace,1.0,C,2,N22O-/),NC2O-/),7,replace 2 at position 1 with C,flow_matching,0.3,2.0,46,199
93,add,1.0,[,,NC2O-/),N[C2O-/),8,add [ at position 1,flow_matching,0.3,2.0,46,199
94,replace,0.0,S,N,N[C2O-/),S[C2O-/),8,replace N at position 0 with S,flow_matching,0.3,2.0,46,199
95,replace,0.0,s,S,S[C2O-/),s[C2O-/),8,replace S at position 0 with s,flow_matching,0.3,2.0,46,199
96,add,7.0,N,,s[C2O-/),s[C2O-/N),9,add N at position 7,flow_matching,0.3,2.0,46,199
97,add,5.0,],,s[C2O-/N),s[C2O]-/N),10,add ] at position 5,flow_matching,0.3,2.0,46,199
98,replace,0.0,N,s,s[C2O]-/N),N[C2O]-/N),10,replace s at position 0 with N,flow_matching,0.3,2.0,46,199
99,add,2.0,s,,N[C2O]-/N),N[sC2O]-/N),11,add s at position 2,flow_matching,0.3,2.0,46,199
100,add,1.0,S,,N[sC2O]-/N),NS[sC2O]-/N),12,add S at position 1,flow_matching,0.3,2.0,46,199
101,add,4.0,4,,NS[sC2O]-/N),NS[s4C2O]-/N),13,add 4 at position 4,flow_matching,0.3,2.0,46,199
102,remove,7.0,O,,NS[s4C2O]-/N),NS[s4C2]-/N),12,remove O from position 7,flow_matching,0.3,2.0,46,199
103,replace,1.0,C,S,NS[s4C2]-/N),NC[s4C2]-/N),12,replace S at position 1 with C,flow_matching,0.3,2.0,46,199
104,add,8.0,4,,NC[s4C2]-/N),NC[s4C2]4-/N),13,add 4 at position 8,flow_matching,0.3,2.0,46,199
105,remove,10.0,/,,NC[s4C2]4-/N),NC[s4C2]4-N),12,remove / from position 10,flow_matching,0.3,2.0,46,199
106,add,7.0,/,,NC[s4C2]4-N),NC[s4C2/]4-N),13,add / at position 7,flow_matching,0.3,2.0,46,199
107,remove,4.0,4,,NC[s4C2/]4-N),NC[sC2/]4-N),12,remove 4 from position 4,flow_matching,0.3,2.0,46,199
108,replace,2.0,(,[,NC[sC2/]4-N),NC(sC2/]4-N),12,replace [ at position 2 with (,flow_matching,0.3,2.0,46,199
109,add,2.0,S,,NC(sC2/]4-N),NCS(sC2/]4-N),13,add S at position 2,flow_matching,0.3,2.0,46,199
110,replace,2.0,(,S,NCS(sC2/]4-N),NC((sC2/]4-N),13,replace S at position 2 with (,flow_matching,0.3,2.0,46,199
111,replace,3.0,=,(,NC((sC2/]4-N),NC(=sC2/]4-N),13,replace ( at position 3 with =,flow_matching,0.3,2.0,46,199
112,add,4.0,],,NC(=sC2/]4-N),NC(=]sC2/]4-N),14,add ] at position 4,flow_matching,0.3,2.0,46,199
113,replace,4.0,O,],NC(=]sC2/]4-N),NC(=OsC2/]4-N),14,replace ] at position 4 with O,flow_matching,0.3,2.0,46,199
114,remove,6.0,C,,NC(=OsC2/]4-N),NC(=Os2/]4-N),13,remove C from position 6,flow_matching,0.3,2.0,46,199
115,replace,10.0,#,-,NC(=Os2/]4-N),NC(=Os2/]4#N),13,replace - at position 10 with #,flow_matching,0.3,2.0,46,199
116,replace,5.0,+,s,NC(=Os2/]4#N),NC(=O+2/]4#N),13,replace s at position 5 with +,flow_matching,0.3,2.0,46,199
117,replace,5.0,),+,NC(=O+2/]4#N),NC(=O)2/]4#N),13,replace + at position 5 with ),flow_matching,0.3,2.0,46,199
118,replace,6.0,C,2,NC(=O)2/]4#N),NC(=O)C/]4#N),13,replace 2 at position 6 with C,flow_matching,0.3,2.0,46,199
119,add,5.0,/,,NC(=O)C/]4#N),NC(=O/)C/]4#N),14,add / at position 5,flow_matching,0.3,2.0,46,199
120,remove,8.0,/,,NC(=O/)C/]4#N),NC(=O/)C]4#N),13,remove / from position 8,flow_matching,0.3,2.0,46,199
121,replace,5.0,),/,NC(=O/)C]4#N),NC(=O))C]4#N),13,replace / at position 5 with ),flow_matching,0.3,2.0,46,199
122,add,7.0,n,,NC(=O))C]4#N),NC(=O))nC]4#N),14,add n at position 7,flow_matching,0.3,2.0,46,199
123,add,0.0,+,,NC(=O))nC]4#N),+NC(=O))nC]4#N),15,add + at position 0,flow_matching,0.3,2.0,46,199
124,add,12.0,O,,+NC(=O))nC]4#N),+NC(=O))nC]4O#N),16,add O at position 12,flow_matching,0.3,2.0,46,199
125,remove,8.0,n,,+NC(=O))nC]4O#N),+NC(=O))C]4O#N),15,remove n from position 8,flow_matching,0.3,2.0,46,199
126,replace,3.0,F,(,+NC(=O))C]4O#N),+NCF=O))C]4O#N),15,replace ( at position 3 with F,flow_matching,0.3,2.0,46,199
127,add,9.0,F,,+NCF=O))C]4O#N),+NCF=O))CF]4O#N),16,add F at position 9,flow_matching,0.3,2.0,46,199
128,remove,15.0,),,+NCF=O))CF]4O#N),+NCF=O))CF]4O#N,15,remove ) from position 15,flow_matching,0.3,2.0,46,199
129,replace,14.0,3,N,+NCF=O))CF]4O#N,+NCF=O))CF]4O#3,15,replace N at position 14 with 3,flow_matching,0.3,2.0,46,199
130,add,9.0,1,,+NCF=O))CF]4O#3,+NCF=O))C1F]4O#3,16,add 1 at position 9,flow_matching,0.3,2.0,46,199
131,replace,0.0,N,+,+NCF=O))C1F]4O#3,NNCF=O))C1F]4O#3,16,replace + at position 0 with N,flow_matching,0.3,2.0,46,199
132,replace,15.0,+,3,NNCF=O))C1F]4O#3,NNCF=O))C1F]4O#+,16,replace 3 at position 15 with +,flow_matching,0.3,2.0,46,199
133,replace,1.0,C,N,NNCF=O))C1F]4O#+,NCCF=O))C1F]4O#+,16,replace N at position 1 with C,flow_matching,0.3,2.0,46,199
134,add,4.0,B,,NCCF=O))C1F]4O#+,NCCFB=O))C1F]4O#+,17,add B at position 4,flow_matching,0.3,2.0,46,199
135,add,13.0,5,,NCCFB=O))C1F]4O#+,NCCFB=O))C1F]54O#+,18,add 5 at position 13,flow_matching,0.3,2.0,46,199
136,remove,16.0,#,,NCCFB=O))C1F]54O#+,NCCFB=O))C1F]54O+,17,remove # from position 16,flow_matching,0.3,2.0,46,199
137,add,9.0,),,NCCFB=O))C1F]54O+,NCCFB=O)))C1F]54O+,18,add ) at position 9,flow_matching,0.3,2.0,46,199
138,replace,2.0,],C,NCCFB=O)))C1F]54O+,NC]FB=O)))C1F]54O+,18,replace C at position 2 with ],flow_matching,0.3,2.0,46,199
139,add,9.0,],,NC]FB=O)))C1F]54O+,NC]FB=O))])C1F]54O+,19,add ] at position 9,flow_matching,0.3,2.0,46,199
140,replace,6.0,N,O,NC]FB=O))])C1F]54O+,NC]FB=N))])C1F]54O+,19,replace O at position 6 with N,flow_matching,0.3,2.0,46,199
141,replace,18.0,\,+,NC]FB=N))])C1F]54O+,NC]FB=N))])C1F]54O\,19,replace + at position 18 with \,flow_matching,0.3,2.0,46,199
142,replace,17.0,o,O,NC]FB=N))])C1F]54O\,NC]FB=N))])C1F]54o\,19,replace O at position 17 with o,flow_matching,0.3,2.0,46,199
143,remove,2.0,],,NC]FB=N))])C1F]54o\,NCFB=N))])C1F]54o\,18,remove ] from position 2,flow_matching,0.3,2.0,46,199
144,replace,2.0,(,F,NCFB=N))])C1F]54o\,NC(B=N))])C1F]54o\,18,replace F at position 2 with (,flow_matching,0.3,2.0,46,199
145,replace,3.0,=,B,NC(B=N))])C1F]54o\,NC(==N))])C1F]54o\,18,replace B at position 3 with =,flow_matching,0.3,2.0,46,199
146,replace,5.0,1,N,NC(==N))])C1F]54o\,NC(==1))])C1F]54o\,18,replace N at position 5 with 1,flow_matching,0.3,2.0,46,199
147,replace,4.0,O,=,NC(==1))])C1F]54o\,NC(=O1))])C1F]54o\,18,replace = at position 4 with O,flow_matching,0.3,2.0,46,199
148,remove,5.0,1,,NC(=O1))])C1F]54o\,NC(=O))])C1F]54o\,17,remove 1 from position 5,flow_matching,0.3,2.0,46,199
149,replace,7.0,S,],NC(=O))])C1F]54o\,NC(=O))S)C1F]54o\,17,replace ] at position 7 with S,flow_matching,0.3,2.0,46,199
150,replace,6.0,C,),NC(=O))S)C1F]54o\,NC(=O)CS)C1F]54o\,17,replace ) at position 6 with C,flow_matching,0.3,2.0,46,199
151,remove,4.0,O,,NC(=O)CS)C1F]54o\,NC(=)CS)C1F]54o\,16,remove O from position 4,flow_matching,0.3,2.0,46,199
152,replace,4.0,O,),NC(=)CS)C1F]54o\,NC(=OCS)C1F]54o\,16,replace ) at position 4 with O,flow_matching,0.3,2.0,46,199
153,add,14.0,F,,NC(=OCS)C1F]54o\,NC(=OCS)C1F]54Fo\,17,add F at position 14,flow_matching,0.3,2.0,46,199
154,remove,11.0,],,NC(=OCS)C1F]54Fo\,NC(=OCS)C1F54Fo\,16,remove ] from position 11,flow_matching,0.3,2.0,46,199
155,remove,2.0,(,,NC(=OCS)C1F54Fo\,NC=OCS)C1F54Fo\,15,remove ( from position 2,flow_matching,0.3,2.0,46,199
156,replace,2.0,(,=,NC=OCS)C1F54Fo\,NC(OCS)C1F54Fo\,15,replace = at position 2 with (,flow_matching,0.3,2.0,46,199
157,replace,3.0,=,O,NC(OCS)C1F54Fo\,NC(=CS)C1F54Fo\,15,replace O at position 3 with =,flow_matching,0.3,2.0,46,199
158,replace,4.0,O,C,NC(=CS)C1F54Fo\,NC(=OS)C1F54Fo\,15,replace C at position 4 with O,flow_matching,0.3,2.0,46,199
159,replace,5.0,),S,NC(=OS)C1F54Fo\,NC(=O))C1F54Fo\,15,replace S at position 5 with ),flow_matching,0.3,2.0,46,199
160,replace,6.0,C,),NC(=O))C1F54Fo\,NC(=O)CC1F54Fo\,15,replace ) at position 6 with C,flow_matching,0.3,2.0,46,199
161,replace,7.0,O,C,NC(=O)CC1F54Fo\,NC(=O)CO1F54Fo\,15,replace C at position 7 with O,flow_matching,0.3,2.0,46,199
162,replace,8.0,c,1,NC(=O)CO1F54Fo\,NC(=O)COcF54Fo\,15,replace 1 at position 8 with c,flow_matching,0.3,2.0,46,199
163,replace,9.0,1,F,NC(=O)COcF54Fo\,NC(=O)COc154Fo\,15,replace F at position 9 with 1,flow_matching,0.3,2.0,46,199
164,replace,10.0,c,5,NC(=O)COc154Fo\,NC(=O)COc1c4Fo\,15,replace 5 at position 10 with c,flow_matching,0.3,2.0,46,199
165,replace,11.0,c,4,NC(=O)COc1c4Fo\,NC(=O)COc1ccFo\,15,replace 4 at position 11 with c,flow_matching,0.3,2.0,46,199
166,replace,12.0,c,F,NC(=O)COc1ccFo\,NC(=O)COc1ccco\,15,replace F at position 12 with c,flow_matching,0.3,2.0,46,199
167,replace,13.0,c,o,NC(=O)COc1ccco\,NC(=O)COc1cccc\,15,replace o at position 13 with c,flow_matching,0.3,2.0,46,199
168,replace,14.0,(,\,NC(=O)COc1cccc\,NC(=O)COc1cccc(,15,replace \ at position 14 with (,flow_matching,0.3,2.0,46,199
169,add,15.0,C,,NC(=O)COc1cccc(,NC(=O)COc1cccc(C,16,add C at position 15,flow_matching,0.3,2.0,46,199
170,add,16.0,N,,NC(=O)COc1cccc(C,NC(=O)COc1cccc(CN,17,add N at position 16,flow_matching,0.3,2.0,46,199
171,add,17.0,C,,NC(=O)COc1cccc(CN,NC(=O)COc1cccc(CNC,18,add C at position 17,flow_matching,0.3,2.0,46,199
172,add,18.0,(,,NC(=O)COc1cccc(CNC,NC(=O)COc1cccc(CNC(,19,add ( at position 18,flow_matching,0.3,2.0,46,199
173,add,19.0,=,,NC(=O)COc1cccc(CNC(,NC(=O)COc1cccc(CNC(=,20,add = at position 19,flow_matching,0.3,2.0,46,199
174,add,20.0,O,,NC(=O)COc1cccc(CNC(=,NC(=O)COc1cccc(CNC(=O,21,add O at position 20,flow_matching,0.3,2.0,46,199
175,add,21.0,),,NC(=O)COc1cccc(CNC(=O,NC(=O)COc1cccc(CNC(=O),22,add ) at position 21,flow_matching,0.3,2.0,46,199
176,add,22.0,c,,NC(=O)COc1cccc(CNC(=O),NC(=O)COc1cccc(CNC(=O)c,23,add c at position 22,flow_matching,0.3,2.0,46,199
177,add,23.0,2,,NC(=O)COc1cccc(CNC(=O)c,NC(=O)COc1cccc(CNC(=O)c2,24,add 2 at position 23,flow_matching,0.3,2.0,46,199
178,add,24.0,c,,NC(=O)COc1cccc(CNC(=O)c2,NC(=O)COc1cccc(CNC(=O)c2c,25,add c at position 24,flow_matching,0.3,2.0,46,199
179,add,25.0,c,,NC(=O)COc1cccc(CNC(=O)c2c,NC(=O)COc1cccc(CNC(=O)c2cc,26,add c at position 25,flow_matching,0.3,2.0,46,199
180,add,26.0,3,,NC(=O)COc1cccc(CNC(=O)c2cc,NC(=O)COc1cccc(CNC(=O)c2cc3,27,add 3 at position 26,flow_matching,0.3,2.0,46,199
181,add,27.0,c,,NC(=O)COc1cccc(CNC(=O)c2cc3,NC(=O)COc1cccc(CNC(=O)c2cc3c,28,add c at position 27,flow_matching,0.3,2.0,46,199
182,add,28.0,c,,NC(=O)COc1cccc(CNC(=O)c2cc3c,NC(=O)COc1cccc(CNC(=O)c2cc3cc,29,add c at position 28,flow_matching,0.3,2.0,46,199
183,add,29.0,(,,NC(=O)COc1cccc(CNC(=O)c2cc3cc,NC(=O)COc1cccc(CNC(=O)c2cc3cc(,30,add ( at position 29,flow_matching,0.3,2.0,46,199
184,add,30.0,C,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(,NC(=O)COc1cccc(CNC(=O)c2cc3cc(C,31,add C at position 30,flow_matching,0.3,2.0,46,199
185,add,31.0,l,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(C,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl,32,add l at position 31,flow_matching,0.3,2.0,46,199
186,add,32.0,),,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl),33,add ) at position 32,flow_matching,0.3,2.0,46,199
187,add,33.0,c,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl),NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)c,34,add c at position 33,flow_matching,0.3,2.0,46,199
188,add,34.0,c,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)c,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)cc,35,add c at position 34,flow_matching,0.3,2.0,46,199
189,add,35.0,c,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)cc,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc,36,add c at position 35,flow_matching,0.3,2.0,46,199
190,add,36.0,3,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3,37,add 3 at position 36,flow_matching,0.3,2.0,46,199
191,add,37.0,[,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[,38,add [ at position 37,flow_matching,0.3,2.0,46,199
192,add,38.0,n,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[n,39,add n at position 38,flow_matching,0.3,2.0,46,199
193,add,39.0,H,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[n,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH,40,add H at position 39,flow_matching,0.3,2.0,46,199
194,add,40.0,],,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH],41,add ] at position 40,flow_matching,0.3,2.0,46,199
195,add,41.0,2,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH],NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH]2,42,add 2 at position 41,flow_matching,0.3,2.0,46,199
196,add,42.0,),,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH]2,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH]2),43,add ) at position 42,flow_matching,0.3,2.0,46,199
197,add,43.0,c,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH]2),NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH]2)c,44,add c at position 43,flow_matching,0.3,2.0,46,199
198,add,44.0,1,,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH]2)c,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH]2)c1,45,add 1 at position 44,flow_matching,0.3,2.0,46,199
199,add,45.0,"
",,NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH]2)c1,"NC(=O)COc1cccc(CNC(=O)c2cc3cc(Cl)ccc3[nH]2)c1
",46,"add 
 at position 45",flow_matching,0.3,2.0,46,199
