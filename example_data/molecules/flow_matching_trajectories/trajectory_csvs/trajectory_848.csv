step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,41,153
1,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,41,153
2,remove,0.0,c,,c,,0,remove c from position 0,flow_matching,0.3,2.0,41,153
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,41,153
4,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,41,153
5,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,41,153
6,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,41,153
7,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,41,153
8,remove,0.0,-,,-,,0,remove - from position 0,flow_matching,0.3,2.0,41,153
9,add,0.0,n,,,n,1,add n at position 0,flow_matching,0.3,2.0,41,153
10,replace,0.0,=,n,n,=,1,replace n at position 0 with =,flow_matching,0.3,2.0,41,153
11,add,1.0,),,=,=),2,add ) at position 1,flow_matching,0.3,2.0,41,153
12,replace,0.0,C,=,=),C),2,replace = at position 0 with C,flow_matching,0.3,2.0,41,153
13,replace,1.0,c,),C),Cc,2,replace ) at position 1 with c,flow_matching,0.3,2.0,41,153
14,remove,0.0,C,,Cc,c,1,remove C from position 0,flow_matching,0.3,2.0,41,153
15,add,0.0,H,,c,Hc,2,add H at position 0,flow_matching,0.3,2.0,41,153
16,add,1.0,\,,Hc,H\c,3,add \ at position 1,flow_matching,0.3,2.0,41,153
17,replace,0.0,C,H,H\c,C\c,3,replace H at position 0 with C,flow_matching,0.3,2.0,41,153
18,remove,2.0,c,,C\c,C\,2,remove c from position 2,flow_matching,0.3,2.0,41,153
19,replace,1.0,4,\,C\,C4,2,replace \ at position 1 with 4,flow_matching,0.3,2.0,41,153
20,add,1.0,@,,C4,C@4,3,add @ at position 1,flow_matching,0.3,2.0,41,153
21,add,0.0,=,,C@4,=C@4,4,add = at position 0,flow_matching,0.3,2.0,41,153
22,replace,1.0,l,C,=C@4,=l@4,4,replace C at position 1 with l,flow_matching,0.3,2.0,41,153
23,remove,0.0,=,,=l@4,l@4,3,remove = from position 0,flow_matching,0.3,2.0,41,153
24,replace,0.0,),l,l@4,)@4,3,replace l at position 0 with ),flow_matching,0.3,2.0,41,153
25,add,3.0,n,,)@4,)@4n,4,add n at position 3,flow_matching,0.3,2.0,41,153
26,add,1.0,=,,)@4n,)=@4n,5,add = at position 1,flow_matching,0.3,2.0,41,153
27,add,2.0,6,,)=@4n,)=6@4n,6,add 6 at position 2,flow_matching,0.3,2.0,41,153
28,replace,0.0,C,),)=6@4n,C=6@4n,6,replace ) at position 0 with C,flow_matching,0.3,2.0,41,153
29,remove,1.0,=,,C=6@4n,C6@4n,5,remove = from position 1,flow_matching,0.3,2.0,41,153
30,add,4.0,),,C6@4n,C6@4)n,6,add ) at position 4,flow_matching,0.3,2.0,41,153
31,replace,1.0,3,6,C6@4)n,C3@4)n,6,replace 6 at position 1 with 3,flow_matching,0.3,2.0,41,153
32,remove,5.0,n,,C3@4)n,C3@4),5,remove n from position 5,flow_matching,0.3,2.0,41,153
33,remove,1.0,3,,C3@4),C@4),4,remove 3 from position 1,flow_matching,0.3,2.0,41,153
34,replace,1.0,c,@,C@4),Cc4),4,replace @ at position 1 with c,flow_matching,0.3,2.0,41,153
35,add,1.0,\,,Cc4),C\c4),5,add \ at position 1,flow_matching,0.3,2.0,41,153
36,add,2.0,n,,C\c4),C\nc4),6,add n at position 2,flow_matching,0.3,2.0,41,153
37,add,4.0,N,,C\nc4),C\ncN4),7,add N at position 4,flow_matching,0.3,2.0,41,153
38,replace,1.0,2,\,C\ncN4),C2ncN4),7,replace \ at position 1 with 2,flow_matching,0.3,2.0,41,153
39,replace,1.0,c,2,C2ncN4),CcncN4),7,replace 2 at position 1 with c,flow_matching,0.3,2.0,41,153
40,replace,1.0,2,c,CcncN4),C2ncN4),7,replace c at position 1 with 2,flow_matching,0.3,2.0,41,153
41,replace,1.0,c,2,C2ncN4),CcncN4),7,replace 2 at position 1 with c,flow_matching,0.3,2.0,41,153
42,add,5.0,B,,CcncN4),CcncNB4),8,add B at position 5,flow_matching,0.3,2.0,41,153
43,add,3.0,3,,CcncNB4),Ccn3cNB4),9,add 3 at position 3,flow_matching,0.3,2.0,41,153
44,replace,2.0,1,n,Ccn3cNB4),Cc13cNB4),9,replace n at position 2 with 1,flow_matching,0.3,2.0,41,153
45,replace,1.0,@,c,Cc13cNB4),C@13cNB4),9,replace c at position 1 with @,flow_matching,0.3,2.0,41,153
46,replace,7.0,o,4,C@13cNB4),C@13cNBo),9,replace 4 at position 7 with o,flow_matching,0.3,2.0,41,153
47,remove,8.0,),,C@13cNBo),C@13cNBo,8,remove ) from position 8,flow_matching,0.3,2.0,41,153
48,remove,4.0,c,,C@13cNBo,C@13NBo,7,remove c from position 4,flow_matching,0.3,2.0,41,153
49,add,3.0,(,,C@13NBo,C@1(3NBo,8,add ( at position 3,flow_matching,0.3,2.0,41,153
50,remove,4.0,3,,C@1(3NBo,C@1(NBo,7,remove 3 from position 4,flow_matching,0.3,2.0,41,153
51,replace,1.0,c,@,C@1(NBo,Cc1(NBo,7,replace @ at position 1 with c,flow_matching,0.3,2.0,41,153
52,add,5.0,-,,Cc1(NBo,Cc1(N-Bo,8,add - at position 5,flow_matching,0.3,2.0,41,153
53,remove,1.0,c,,Cc1(N-Bo,C1(N-Bo,7,remove c from position 1,flow_matching,0.3,2.0,41,153
54,add,1.0,),,C1(N-Bo,C)1(N-Bo,8,add ) at position 1,flow_matching,0.3,2.0,41,153
55,replace,1.0,c,),C)1(N-Bo,Cc1(N-Bo,8,replace ) at position 1 with c,flow_matching,0.3,2.0,41,153
56,add,5.0,=,,Cc1(N-Bo,Cc1(N=-Bo,9,add = at position 5,flow_matching,0.3,2.0,41,153
57,replace,0.0,s,C,Cc1(N=-Bo,sc1(N=-Bo,9,replace C at position 0 with s,flow_matching,0.3,2.0,41,153
58,replace,0.0,C,s,sc1(N=-Bo,Cc1(N=-Bo,9,replace s at position 0 with C,flow_matching,0.3,2.0,41,153
59,replace,3.0,c,(,Cc1(N=-Bo,Cc1cN=-Bo,9,replace ( at position 3 with c,flow_matching,0.3,2.0,41,153
60,replace,2.0,B,1,Cc1cN=-Bo,CcBcN=-Bo,9,replace 1 at position 2 with B,flow_matching,0.3,2.0,41,153
61,add,0.0,c,,CcBcN=-Bo,cCcBcN=-Bo,10,add c at position 0,flow_matching,0.3,2.0,41,153
62,replace,0.0,C,c,cCcBcN=-Bo,CCcBcN=-Bo,10,replace c at position 0 with C,flow_matching,0.3,2.0,41,153
63,remove,8.0,B,,CCcBcN=-Bo,CCcBcN=-o,9,remove B from position 8,flow_matching,0.3,2.0,41,153
64,add,4.0,6,,CCcBcN=-o,CCcB6cN=-o,10,add 6 at position 4,flow_matching,0.3,2.0,41,153
65,remove,9.0,o,,CCcB6cN=-o,CCcB6cN=-,9,remove o from position 9,flow_matching,0.3,2.0,41,153
66,add,3.0,],,CCcB6cN=-,CCc]B6cN=-,10,add ] at position 3,flow_matching,0.3,2.0,41,153
67,replace,1.0,c,C,CCc]B6cN=-,Ccc]B6cN=-,10,replace C at position 1 with c,flow_matching,0.3,2.0,41,153
68,replace,2.0,1,c,Ccc]B6cN=-,Cc1]B6cN=-,10,replace c at position 2 with 1,flow_matching,0.3,2.0,41,153
69,replace,3.0,c,],Cc1]B6cN=-,Cc1cB6cN=-,10,replace ] at position 3 with c,flow_matching,0.3,2.0,41,153
70,replace,4.0,c,B,Cc1cB6cN=-,Cc1cc6cN=-,10,replace B at position 4 with c,flow_matching,0.3,2.0,41,153
71,replace,5.0,(,6,Cc1cc6cN=-,Cc1cc(cN=-,10,replace 6 at position 5 with (,flow_matching,0.3,2.0,41,153
72,replace,6.0,F,c,Cc1cc(cN=-,Cc1cc(FN=-,10,replace c at position 6 with F,flow_matching,0.3,2.0,41,153
73,add,9.0,l,,Cc1cc(FN=-,Cc1cc(FN=l-,11,add l at position 9,flow_matching,0.3,2.0,41,153
74,add,3.0,H,,Cc1cc(FN=l-,Cc1Hcc(FN=l-,12,add H at position 3,flow_matching,0.3,2.0,41,153
75,add,3.0,3,,Cc1Hcc(FN=l-,Cc13Hcc(FN=l-,13,add 3 at position 3,flow_matching,0.3,2.0,41,153
76,replace,3.0,c,3,Cc13Hcc(FN=l-,Cc1cHcc(FN=l-,13,replace 3 at position 3 with c,flow_matching,0.3,2.0,41,153
77,replace,4.0,c,H,Cc1cHcc(FN=l-,Cc1cccc(FN=l-,13,replace H at position 4 with c,flow_matching,0.3,2.0,41,153
78,replace,5.0,(,c,Cc1cccc(FN=l-,Cc1cc(c(FN=l-,13,replace c at position 5 with (,flow_matching,0.3,2.0,41,153
79,replace,6.0,F,c,Cc1cc(c(FN=l-,Cc1cc(F(FN=l-,13,replace c at position 6 with F,flow_matching,0.3,2.0,41,153
80,replace,7.0,),(,Cc1cc(F(FN=l-,Cc1cc(F)FN=l-,13,replace ( at position 7 with ),flow_matching,0.3,2.0,41,153
81,remove,8.0,F,,Cc1cc(F)FN=l-,Cc1cc(F)N=l-,12,remove F from position 8,flow_matching,0.3,2.0,41,153
82,remove,7.0,),,Cc1cc(F)N=l-,Cc1cc(FN=l-,11,remove ) from position 7,flow_matching,0.3,2.0,41,153
83,add,3.0,N,,Cc1cc(FN=l-,Cc1Ncc(FN=l-,12,add N at position 3,flow_matching,0.3,2.0,41,153
84,remove,5.0,c,,Cc1Ncc(FN=l-,Cc1Nc(FN=l-,11,remove c from position 5,flow_matching,0.3,2.0,41,153
85,replace,3.0,c,N,Cc1Nc(FN=l-,Cc1cc(FN=l-,11,replace N at position 3 with c,flow_matching,0.3,2.0,41,153
86,replace,10.0,s,-,Cc1cc(FN=l-,Cc1cc(FN=ls,11,replace - at position 10 with s,flow_matching,0.3,2.0,41,153
87,add,10.0,=,,Cc1cc(FN=ls,Cc1cc(FN=l=s,12,add = at position 10,flow_matching,0.3,2.0,41,153
88,add,10.0,(,,Cc1cc(FN=l=s,Cc1cc(FN=l(=s,13,add ( at position 10,flow_matching,0.3,2.0,41,153
89,replace,1.0,+,c,Cc1cc(FN=l(=s,C+1cc(FN=l(=s,13,replace c at position 1 with +,flow_matching,0.3,2.0,41,153
90,replace,2.0,-,1,C+1cc(FN=l(=s,C+-cc(FN=l(=s,13,replace 1 at position 2 with -,flow_matching,0.3,2.0,41,153
91,replace,12.0,+,s,C+-cc(FN=l(=s,C+-cc(FN=l(=+,13,replace s at position 12 with +,flow_matching,0.3,2.0,41,153
92,add,8.0,S,,C+-cc(FN=l(=+,C+-cc(FNS=l(=+,14,add S at position 8,flow_matching,0.3,2.0,41,153
93,replace,1.0,c,+,C+-cc(FNS=l(=+,Cc-cc(FNS=l(=+,14,replace + at position 1 with c,flow_matching,0.3,2.0,41,153
94,add,0.0,/,,Cc-cc(FNS=l(=+,/Cc-cc(FNS=l(=+,15,add / at position 0,flow_matching,0.3,2.0,41,153
95,replace,0.0,C,/,/Cc-cc(FNS=l(=+,CCc-cc(FNS=l(=+,15,replace / at position 0 with C,flow_matching,0.3,2.0,41,153
96,replace,4.0,S,c,CCc-cc(FNS=l(=+,CCc-Sc(FNS=l(=+,15,replace c at position 4 with S,flow_matching,0.3,2.0,41,153
97,replace,0.0,l,C,CCc-Sc(FNS=l(=+,lCc-Sc(FNS=l(=+,15,replace C at position 0 with l,flow_matching,0.3,2.0,41,153
98,replace,0.0,C,l,lCc-Sc(FNS=l(=+,CCc-Sc(FNS=l(=+,15,replace l at position 0 with C,flow_matching,0.3,2.0,41,153
99,remove,11.0,l,,CCc-Sc(FNS=l(=+,CCc-Sc(FNS=(=+,14,remove l from position 11,flow_matching,0.3,2.0,41,153
100,replace,1.0,c,C,CCc-Sc(FNS=(=+,Ccc-Sc(FNS=(=+,14,replace C at position 1 with c,flow_matching,0.3,2.0,41,153
101,remove,2.0,c,,Ccc-Sc(FNS=(=+,Cc-Sc(FNS=(=+,13,remove c from position 2,flow_matching,0.3,2.0,41,153
102,add,13.0,-,,Cc-Sc(FNS=(=+,Cc-Sc(FNS=(=+-,14,add - at position 13,flow_matching,0.3,2.0,41,153
103,replace,5.0,1,(,Cc-Sc(FNS=(=+-,Cc-Sc1FNS=(=+-,14,replace ( at position 5 with 1,flow_matching,0.3,2.0,41,153
104,replace,11.0,@,=,Cc-Sc1FNS=(=+-,Cc-Sc1FNS=(@+-,14,replace = at position 11 with @,flow_matching,0.3,2.0,41,153
105,replace,8.0,3,S,Cc-Sc1FNS=(@+-,Cc-Sc1FN3=(@+-,14,replace S at position 8 with 3,flow_matching,0.3,2.0,41,153
106,replace,2.0,1,-,Cc-Sc1FN3=(@+-,Cc1Sc1FN3=(@+-,14,replace - at position 2 with 1,flow_matching,0.3,2.0,41,153
107,remove,5.0,1,,Cc1Sc1FN3=(@+-,Cc1ScFN3=(@+-,13,remove 1 from position 5,flow_matching,0.3,2.0,41,153
108,remove,12.0,-,,Cc1ScFN3=(@+-,Cc1ScFN3=(@+,12,remove - from position 12,flow_matching,0.3,2.0,41,153
109,replace,7.0,r,3,Cc1ScFN3=(@+,Cc1ScFNr=(@+,12,replace 3 at position 7 with r,flow_matching,0.3,2.0,41,153
110,replace,3.0,c,S,Cc1ScFNr=(@+,Cc1ccFNr=(@+,12,replace S at position 3 with c,flow_matching,0.3,2.0,41,153
111,add,8.0,5,,Cc1ccFNr=(@+,Cc1ccFNr5=(@+,13,add 5 at position 8,flow_matching,0.3,2.0,41,153
112,replace,5.0,(,F,Cc1ccFNr5=(@+,Cc1cc(Nr5=(@+,13,replace F at position 5 with (,flow_matching,0.3,2.0,41,153
113,remove,2.0,1,,Cc1cc(Nr5=(@+,Cccc(Nr5=(@+,12,remove 1 from position 2,flow_matching,0.3,2.0,41,153
114,remove,3.0,c,,Cccc(Nr5=(@+,Ccc(Nr5=(@+,11,remove c from position 3,flow_matching,0.3,2.0,41,153
115,replace,2.0,1,c,Ccc(Nr5=(@+,Cc1(Nr5=(@+,11,replace c at position 2 with 1,flow_matching,0.3,2.0,41,153
116,replace,3.0,c,(,Cc1(Nr5=(@+,Cc1cNr5=(@+,11,replace ( at position 3 with c,flow_matching,0.3,2.0,41,153
117,replace,4.0,c,N,Cc1cNr5=(@+,Cc1ccr5=(@+,11,replace N at position 4 with c,flow_matching,0.3,2.0,41,153
118,replace,5.0,(,r,Cc1ccr5=(@+,Cc1cc(5=(@+,11,replace r at position 5 with (,flow_matching,0.3,2.0,41,153
119,replace,6.0,F,5,Cc1cc(5=(@+,Cc1cc(F=(@+,11,replace 5 at position 6 with F,flow_matching,0.3,2.0,41,153
120,replace,7.0,),=,Cc1cc(F=(@+,Cc1cc(F)(@+,11,replace = at position 7 with ),flow_matching,0.3,2.0,41,153
121,replace,8.0,c,(,Cc1cc(F)(@+,Cc1cc(F)c@+,11,replace ( at position 8 with c,flow_matching,0.3,2.0,41,153
122,replace,9.0,c,@,Cc1cc(F)c@+,Cc1cc(F)cc+,11,replace @ at position 9 with c,flow_matching,0.3,2.0,41,153
123,replace,10.0,c,+,Cc1cc(F)cc+,Cc1cc(F)ccc,11,replace + at position 10 with c,flow_matching,0.3,2.0,41,153
124,add,11.0,1,,Cc1cc(F)ccc,Cc1cc(F)ccc1,12,add 1 at position 11,flow_matching,0.3,2.0,41,153
125,add,12.0,C,,Cc1cc(F)ccc1,Cc1cc(F)ccc1C,13,add C at position 12,flow_matching,0.3,2.0,41,153
126,add,13.0,C,,Cc1cc(F)ccc1C,Cc1cc(F)ccc1CC,14,add C at position 13,flow_matching,0.3,2.0,41,153
127,add,14.0,N,,Cc1cc(F)ccc1CC,Cc1cc(F)ccc1CCN,15,add N at position 14,flow_matching,0.3,2.0,41,153
128,add,15.0,C,,Cc1cc(F)ccc1CCN,Cc1cc(F)ccc1CCNC,16,add C at position 15,flow_matching,0.3,2.0,41,153
129,add,16.0,(,,Cc1cc(F)ccc1CCNC,Cc1cc(F)ccc1CCNC(,17,add ( at position 16,flow_matching,0.3,2.0,41,153
130,add,17.0,=,,Cc1cc(F)ccc1CCNC(,Cc1cc(F)ccc1CCNC(=,18,add = at position 17,flow_matching,0.3,2.0,41,153
131,add,18.0,O,,Cc1cc(F)ccc1CCNC(=,Cc1cc(F)ccc1CCNC(=O,19,add O at position 18,flow_matching,0.3,2.0,41,153
132,add,19.0,),,Cc1cc(F)ccc1CCNC(=O,Cc1cc(F)ccc1CCNC(=O),20,add ) at position 19,flow_matching,0.3,2.0,41,153
133,add,20.0,C,,Cc1cc(F)ccc1CCNC(=O),Cc1cc(F)ccc1CCNC(=O)C,21,add C at position 20,flow_matching,0.3,2.0,41,153
134,add,21.0,c,,Cc1cc(F)ccc1CCNC(=O)C,Cc1cc(F)ccc1CCNC(=O)Cc,22,add c at position 21,flow_matching,0.3,2.0,41,153
135,add,22.0,1,,Cc1cc(F)ccc1CCNC(=O)Cc,Cc1cc(F)ccc1CCNC(=O)Cc1,23,add 1 at position 22,flow_matching,0.3,2.0,41,153
136,add,23.0,c,,Cc1cc(F)ccc1CCNC(=O)Cc1,Cc1cc(F)ccc1CCNC(=O)Cc1c,24,add c at position 23,flow_matching,0.3,2.0,41,153
137,add,24.0,[,,Cc1cc(F)ccc1CCNC(=O)Cc1c,Cc1cc(F)ccc1CCNC(=O)Cc1c[,25,add [ at position 24,flow_matching,0.3,2.0,41,153
138,add,25.0,n,,Cc1cc(F)ccc1CCNC(=O)Cc1c[,Cc1cc(F)ccc1CCNC(=O)Cc1c[n,26,add n at position 25,flow_matching,0.3,2.0,41,153
139,add,26.0,H,,Cc1cc(F)ccc1CCNC(=O)Cc1c[n,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH,27,add H at position 26,flow_matching,0.3,2.0,41,153
140,add,27.0,],,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH],28,add ] at position 27,flow_matching,0.3,2.0,41,153
141,add,28.0,c,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH],Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c,29,add c at position 28,flow_matching,0.3,2.0,41,153
142,add,29.0,2,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2,30,add 2 at position 29,flow_matching,0.3,2.0,41,153
143,add,30.0,c,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c,31,add c at position 30,flow_matching,0.3,2.0,41,153
144,add,31.0,(,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(,32,add ( at position 31,flow_matching,0.3,2.0,41,153
145,add,32.0,C,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C,33,add C at position 32,flow_matching,0.3,2.0,41,153
146,add,33.0,),,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C),34,add ) at position 33,flow_matching,0.3,2.0,41,153
147,add,34.0,c,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C),Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)c,35,add c at position 34,flow_matching,0.3,2.0,41,153
148,add,35.0,c,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)c,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)cc,36,add c at position 35,flow_matching,0.3,2.0,41,153
149,add,36.0,c,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)cc,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)ccc,37,add c at position 36,flow_matching,0.3,2.0,41,153
150,add,37.0,c,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)ccc,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)cccc,38,add c at position 37,flow_matching,0.3,2.0,41,153
151,add,38.0,1,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)cccc,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)cccc1,39,add 1 at position 38,flow_matching,0.3,2.0,41,153
152,add,39.0,2,,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)cccc1,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)cccc12,40,add 2 at position 39,flow_matching,0.3,2.0,41,153
153,add,40.0,"
",,Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)cccc12,"Cc1cc(F)ccc1CCNC(=O)Cc1c[nH]c2c(C)cccc12
",41,"add 
 at position 40",flow_matching,0.3,2.0,41,153
