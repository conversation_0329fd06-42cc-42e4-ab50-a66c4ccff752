step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,57,110
1,add,0.0,4,,,4,1,add 4 at position 0,flow_matching,0.3,2.0,57,110
2,replace,0.0,C,4,4,C,1,replace 4 at position 0 with C,flow_matching,0.3,2.0,57,110
3,replace,0.0,c,C,C,c,1,replace <PERSON> at position 0 with c,flow_matching,0.3,2.0,57,110
4,add,0.0,3,,c,3c,2,add 3 at position 0,flow_matching,0.3,2.0,57,110
5,replace,1.0,H,c,3c,3H,2,replace c at position 1 with H,flow_matching,0.3,2.0,57,110
6,replace,0.0,<PERSON>,3,3H,CH,2,replace 3 at position 0 with <PERSON>,flow_matching,0.3,2.0,57,110
7,remove,0.0,<PERSON>,,CH,H,1,remove C from position 0,flow_matching,0.3,2.0,57,110
8,replace,0.0,<PERSON>,H,H,C,1,replace H at position 0 with C,flow_matching,0.3,2.0,57,110
9,replace,0.0,1,C,C,1,1,replace C at position 0 with 1,flow_matching,0.3,2.0,57,110
10,replace,0.0,O,1,1,O,1,replace 1 at position 0 with O,flow_matching,0.3,2.0,57,110
11,replace,0.0,C,O,O,C,1,replace O at position 0 with C,flow_matching,0.3,2.0,57,110
12,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,57,110
13,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,57,110
14,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,57,110
15,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,57,110
16,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,57,110
17,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,57,110
18,add,1.0,-,,/,/-,2,add - at position 1,flow_matching,0.3,2.0,57,110
19,replace,0.0,O,/,/-,O-,2,replace / at position 0 with O,flow_matching,0.3,2.0,57,110
20,add,2.0,-,,O-,O--,3,add - at position 2,flow_matching,0.3,2.0,57,110
21,add,3.0,O,,O--,O--O,4,add O at position 3,flow_matching,0.3,2.0,57,110
22,replace,0.0,C,O,O--O,C--O,4,replace O at position 0 with C,flow_matching,0.3,2.0,57,110
23,replace,1.0,O,-,C--O,CO-O,4,replace - at position 1 with O,flow_matching,0.3,2.0,57,110
24,add,0.0,N,,CO-O,NCO-O,5,add N at position 0,flow_matching,0.3,2.0,57,110
25,replace,1.0,N,C,NCO-O,NNO-O,5,replace C at position 1 with N,flow_matching,0.3,2.0,57,110
26,replace,3.0,F,-,NNO-O,NNOFO,5,replace - at position 3 with F,flow_matching,0.3,2.0,57,110
27,replace,0.0,C,N,NNOFO,CNOFO,5,replace N at position 0 with C,flow_matching,0.3,2.0,57,110
28,remove,4.0,O,,CNOFO,CNOF,4,remove O from position 4,flow_matching,0.3,2.0,57,110
29,replace,2.0,\,O,CNOF,CN\F,4,replace O at position 2 with \,flow_matching,0.3,2.0,57,110
30,replace,2.0,n,\,CN\F,CNnF,4,replace \ at position 2 with n,flow_matching,0.3,2.0,57,110
31,replace,0.0,3,C,CNnF,3NnF,4,replace C at position 0 with 3,flow_matching,0.3,2.0,57,110
32,add,3.0,3,,3NnF,3Nn3F,5,add 3 at position 3,flow_matching,0.3,2.0,57,110
33,replace,0.0,s,3,3Nn3F,sNn3F,5,replace 3 at position 0 with s,flow_matching,0.3,2.0,57,110
34,replace,4.0,1,F,sNn3F,sNn31,5,replace F at position 4 with 1,flow_matching,0.3,2.0,57,110
35,add,0.0,5,,sNn31,5sNn31,6,add 5 at position 0,flow_matching,0.3,2.0,57,110
36,remove,3.0,n,,5sNn31,5sN31,5,remove n from position 3,flow_matching,0.3,2.0,57,110
37,replace,2.0,I,N,5sN31,5sI31,5,replace N at position 2 with I,flow_matching,0.3,2.0,57,110
38,add,5.0,+,,5sI31,5sI31+,6,add + at position 5,flow_matching,0.3,2.0,57,110
39,add,0.0,1,,5sI31+,15sI31+,7,add 1 at position 0,flow_matching,0.3,2.0,57,110
40,replace,4.0,4,3,15sI31+,15sI41+,7,replace 3 at position 4 with 4,flow_matching,0.3,2.0,57,110
41,remove,1.0,5,,15sI41+,1sI41+,6,remove 5 from position 1,flow_matching,0.3,2.0,57,110
42,add,0.0,-,,1sI41+,-1sI41+,7,add - at position 0,flow_matching,0.3,2.0,57,110
43,replace,0.0,C,-,-1sI41+,C1sI41+,7,replace - at position 0 with C,flow_matching,0.3,2.0,57,110
44,replace,1.0,O,1,C1sI41+,COsI41+,7,replace 1 at position 1 with O,flow_matching,0.3,2.0,57,110
45,add,2.0,+,,COsI41+,CO+sI41+,8,add + at position 2,flow_matching,0.3,2.0,57,110
46,add,4.0,),,CO+sI41+,CO+s)I41+,9,add ) at position 4,flow_matching,0.3,2.0,57,110
47,add,9.0,r,,CO+s)I41+,CO+s)I41+r,10,add r at position 9,flow_matching,0.3,2.0,57,110
48,replace,2.0,c,+,CO+s)I41+r,COcs)I41+r,10,replace + at position 2 with c,flow_matching,0.3,2.0,57,110
49,remove,2.0,c,,COcs)I41+r,COs)I41+r,9,remove c from position 2,flow_matching,0.3,2.0,57,110
50,remove,2.0,s,,COs)I41+r,CO)I41+r,8,remove s from position 2,flow_matching,0.3,2.0,57,110
51,add,6.0,3,,CO)I41+r,CO)I413+r,9,add 3 at position 6,flow_matching,0.3,2.0,57,110
52,replace,6.0,@,3,CO)I413+r,CO)I41@+r,9,replace 3 at position 6 with @,flow_matching,0.3,2.0,57,110
53,replace,3.0,F,I,CO)I41@+r,CO)F41@+r,9,replace I at position 3 with F,flow_matching,0.3,2.0,57,110
54,replace,2.0,c,),CO)F41@+r,COcF41@+r,9,replace ) at position 2 with c,flow_matching,0.3,2.0,57,110
55,add,3.0,I,,COcF41@+r,COcIF41@+r,10,add I at position 3,flow_matching,0.3,2.0,57,110
56,replace,3.0,1,I,COcIF41@+r,COc1F41@+r,10,replace I at position 3 with 1,flow_matching,0.3,2.0,57,110
57,remove,5.0,4,,COc1F41@+r,COc1F1@+r,9,remove 4 from position 5,flow_matching,0.3,2.0,57,110
58,replace,4.0,c,F,COc1F1@+r,COc1c1@+r,9,replace F at position 4 with c,flow_matching,0.3,2.0,57,110
59,replace,5.0,c,1,COc1c1@+r,COc1cc@+r,9,replace 1 at position 5 with c,flow_matching,0.3,2.0,57,110
60,replace,6.0,c,@,COc1cc@+r,COc1ccc+r,9,replace @ at position 6 with c,flow_matching,0.3,2.0,57,110
61,replace,7.0,(,+,COc1ccc+r,COc1ccc(r,9,replace + at position 7 with (,flow_matching,0.3,2.0,57,110
62,replace,8.0,C,r,COc1ccc(r,COc1ccc(C,9,replace r at position 8 with C,flow_matching,0.3,2.0,57,110
63,add,9.0,N,,COc1ccc(C,COc1ccc(CN,10,add N at position 9,flow_matching,0.3,2.0,57,110
64,add,10.0,C,,COc1ccc(CN,COc1ccc(CNC,11,add C at position 10,flow_matching,0.3,2.0,57,110
65,add,11.0,(,,COc1ccc(CNC,COc1ccc(CNC(,12,add ( at position 11,flow_matching,0.3,2.0,57,110
66,add,12.0,=,,COc1ccc(CNC(,COc1ccc(CNC(=,13,add = at position 12,flow_matching,0.3,2.0,57,110
67,add,13.0,O,,COc1ccc(CNC(=,COc1ccc(CNC(=O,14,add O at position 13,flow_matching,0.3,2.0,57,110
68,add,14.0,),,COc1ccc(CNC(=O,COc1ccc(CNC(=O),15,add ) at position 14,flow_matching,0.3,2.0,57,110
69,add,15.0,c,,COc1ccc(CNC(=O),COc1ccc(CNC(=O)c,16,add c at position 15,flow_matching,0.3,2.0,57,110
70,add,16.0,2,,COc1ccc(CNC(=O)c,COc1ccc(CNC(=O)c2,17,add 2 at position 16,flow_matching,0.3,2.0,57,110
71,add,17.0,c,,COc1ccc(CNC(=O)c2,COc1ccc(CNC(=O)c2c,18,add c at position 17,flow_matching,0.3,2.0,57,110
72,add,18.0,c,,COc1ccc(CNC(=O)c2c,COc1ccc(CNC(=O)c2cc,19,add c at position 18,flow_matching,0.3,2.0,57,110
73,add,19.0,(,,COc1ccc(CNC(=O)c2cc,COc1ccc(CNC(=O)c2cc(,20,add ( at position 19,flow_matching,0.3,2.0,57,110
74,add,20.0,N,,COc1ccc(CNC(=O)c2cc(,COc1ccc(CNC(=O)c2cc(N,21,add N at position 20,flow_matching,0.3,2.0,57,110
75,add,21.0,3,,COc1ccc(CNC(=O)c2cc(N,COc1ccc(CNC(=O)c2cc(N3,22,add 3 at position 21,flow_matching,0.3,2.0,57,110
76,add,22.0,C,,COc1ccc(CNC(=O)c2cc(N3,COc1ccc(CNC(=O)c2cc(N3C,23,add C at position 22,flow_matching,0.3,2.0,57,110
77,add,23.0,(,,COc1ccc(CNC(=O)c2cc(N3C,COc1ccc(CNC(=O)c2cc(N3C(,24,add ( at position 23,flow_matching,0.3,2.0,57,110
78,add,24.0,=,,COc1ccc(CNC(=O)c2cc(N3C(,COc1ccc(CNC(=O)c2cc(N3C(=,25,add = at position 24,flow_matching,0.3,2.0,57,110
79,add,25.0,O,,COc1ccc(CNC(=O)c2cc(N3C(=,COc1ccc(CNC(=O)c2cc(N3C(=O,26,add O at position 25,flow_matching,0.3,2.0,57,110
80,add,26.0,),,COc1ccc(CNC(=O)c2cc(N3C(=O,COc1ccc(CNC(=O)c2cc(N3C(=O),27,add ) at position 26,flow_matching,0.3,2.0,57,110
81,add,27.0,C,,COc1ccc(CNC(=O)c2cc(N3C(=O),COc1ccc(CNC(=O)c2cc(N3C(=O)C,28,add C at position 27,flow_matching,0.3,2.0,57,110
82,add,28.0,(,,COc1ccc(CNC(=O)c2cc(N3C(=O)C,COc1ccc(CNC(=O)c2cc(N3C(=O)C(,29,add ( at position 28,flow_matching,0.3,2.0,57,110
83,add,29.0,C,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C,30,add C at position 29,flow_matching,0.3,2.0,57,110
84,add,30.0,),,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C),31,add ) at position 30,flow_matching,0.3,2.0,57,110
85,add,31.0,(,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C),COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(,32,add ( at position 31,flow_matching,0.3,2.0,57,110
86,add,32.0,C,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C,33,add C at position 32,flow_matching,0.3,2.0,57,110
87,add,33.0,),,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C),34,add ) at position 33,flow_matching,0.3,2.0,57,110
88,add,34.0,C,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C),COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)C,35,add C at position 34,flow_matching,0.3,2.0,57,110
89,add,35.0,S,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)C,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS,36,add S at position 35,flow_matching,0.3,2.0,57,110
90,add,36.0,3,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3,37,add 3 at position 36,flow_matching,0.3,2.0,57,110
91,add,37.0,(,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(,38,add ( at position 37,flow_matching,0.3,2.0,57,110
92,add,38.0,=,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=,39,add = at position 38,flow_matching,0.3,2.0,57,110
93,add,39.0,O,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O,40,add O at position 39,flow_matching,0.3,2.0,57,110
94,add,40.0,),,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O),41,add ) at position 40,flow_matching,0.3,2.0,57,110
95,add,41.0,=,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O),COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=,42,add = at position 41,flow_matching,0.3,2.0,57,110
96,add,42.0,O,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O,43,add O at position 42,flow_matching,0.3,2.0,57,110
97,add,43.0,),,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O),44,add ) at position 43,flow_matching,0.3,2.0,57,110
98,add,44.0,c,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O),COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)c,45,add c at position 44,flow_matching,0.3,2.0,57,110
99,add,45.0,c,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)c,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)cc,46,add c at position 45,flow_matching,0.3,2.0,57,110
100,add,46.0,c,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)cc,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc,47,add c at position 46,flow_matching,0.3,2.0,57,110
101,add,47.0,2,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2,48,add 2 at position 47,flow_matching,0.3,2.0,57,110
102,add,48.0,C,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2C,49,add C at position 48,flow_matching,0.3,2.0,57,110
103,add,49.0,l,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2C,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl,50,add l at position 49,flow_matching,0.3,2.0,57,110
104,add,50.0,),,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl),51,add ) at position 50,flow_matching,0.3,2.0,57,110
105,add,51.0,c,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl),COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)c,52,add c at position 51,flow_matching,0.3,2.0,57,110
106,add,52.0,c,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)c,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)cc,53,add c at position 52,flow_matching,0.3,2.0,57,110
107,add,53.0,1,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)cc,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)cc1,54,add 1 at position 53,flow_matching,0.3,2.0,57,110
108,add,54.0,O,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)cc1,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)cc1O,55,add O at position 54,flow_matching,0.3,2.0,57,110
109,add,55.0,C,,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)cc1O,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)cc1OC,56,add C at position 55,flow_matching,0.3,2.0,57,110
110,add,56.0,"
",,COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)cc1OC,"COc1ccc(CNC(=O)c2cc(N3C(=O)C(C)(C)CS3(=O)=O)ccc2Cl)cc1OC
",57,"add 
 at position 56",flow_matching,0.3,2.0,57,110
