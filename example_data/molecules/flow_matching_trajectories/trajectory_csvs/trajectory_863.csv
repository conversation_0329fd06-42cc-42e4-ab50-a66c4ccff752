step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,53,177
1,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,53,177
2,replace,0.0,5,3,3,5,1,replace 3 at position 0 with 5,flow_matching,0.3,2.0,53,177
3,add,1.0,4,,5,54,2,add 4 at position 1,flow_matching,0.3,2.0,53,177
4,replace,0.0,C,5,54,C4,2,replace 5 at position 0 with C,flow_matching,0.3,2.0,53,177
5,add,2.0,@,,C4,C4@,3,add @ at position 2,flow_matching,0.3,2.0,53,177
6,replace,2.0,=,@,C4@,C4=,3,replace @ at position 2 with =,flow_matching,0.3,2.0,53,177
7,replace,1.0,[,4,C4=,C[=,3,replace 4 at position 1 with [,flow_matching,0.3,2.0,53,177
8,remove,2.0,=,,C[=,C[,2,remove = from position 2,flow_matching,0.3,2.0,53,177
9,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,53,177
10,replace,1.0,N,[,C[C,CNC,3,replace [ at position 1 with N,flow_matching,0.3,2.0,53,177
11,add,1.0,+,,CNC,C+NC,4,add + at position 1,flow_matching,0.3,2.0,53,177
12,remove,2.0,N,,C+NC,C+C,3,remove N from position 2,flow_matching,0.3,2.0,53,177
13,replace,1.0,1,+,C+C,C1C,3,replace + at position 1 with 1,flow_matching,0.3,2.0,53,177
14,remove,0.0,C,,C1C,1C,2,remove C from position 0,flow_matching,0.3,2.0,53,177
15,replace,0.0,/,1,1C,/C,2,replace 1 at position 0 with /,flow_matching,0.3,2.0,53,177
16,replace,0.0,C,/,/C,CC,2,replace / at position 0 with C,flow_matching,0.3,2.0,53,177
17,add,2.0,=,,CC,CC=,3,add = at position 2,flow_matching,0.3,2.0,53,177
18,replace,1.0,[,C,CC=,C[=,3,replace C at position 1 with [,flow_matching,0.3,2.0,53,177
19,add,0.0,I,,C[=,IC[=,4,add I at position 0,flow_matching,0.3,2.0,53,177
20,replace,1.0,I,C,IC[=,II[=,4,replace C at position 1 with I,flow_matching,0.3,2.0,53,177
21,replace,0.0,C,I,II[=,CI[=,4,replace I at position 0 with C,flow_matching,0.3,2.0,53,177
22,replace,2.0,c,[,CI[=,CIc=,4,replace [ at position 2 with c,flow_matching,0.3,2.0,53,177
23,replace,3.0,],=,CIc=,CIc],4,replace = at position 3 with ],flow_matching,0.3,2.0,53,177
24,add,1.0,],,CIc],C]Ic],5,add ] at position 1,flow_matching,0.3,2.0,53,177
25,remove,0.0,C,,C]Ic],]Ic],4,remove C from position 0,flow_matching,0.3,2.0,53,177
26,replace,0.0,C,],]Ic],CIc],4,replace ] at position 0 with C,flow_matching,0.3,2.0,53,177
27,replace,1.0,[,I,CIc],C[c],4,replace I at position 1 with [,flow_matching,0.3,2.0,53,177
28,remove,1.0,[,,C[c],Cc],3,remove [ from position 1,flow_matching,0.3,2.0,53,177
29,add,2.0,O,,Cc],CcO],4,add O at position 2,flow_matching,0.3,2.0,53,177
30,add,0.0,n,,CcO],nCcO],5,add n at position 0,flow_matching,0.3,2.0,53,177
31,replace,2.0,F,c,nCcO],nCFO],5,replace c at position 2 with F,flow_matching,0.3,2.0,53,177
32,remove,4.0,],,nCFO],nCFO,4,remove ] from position 4,flow_matching,0.3,2.0,53,177
33,add,3.0,n,,nCFO,nCFnO,5,add n at position 3,flow_matching,0.3,2.0,53,177
34,add,3.0,o,,nCFnO,nCFonO,6,add o at position 3,flow_matching,0.3,2.0,53,177
35,replace,5.0,4,O,nCFonO,nCFon4,6,replace O at position 5 with 4,flow_matching,0.3,2.0,53,177
36,remove,1.0,C,,nCFon4,nFon4,5,remove C from position 1,flow_matching,0.3,2.0,53,177
37,replace,4.0,1,4,nFon4,nFon1,5,replace 4 at position 4 with 1,flow_matching,0.3,2.0,53,177
38,remove,4.0,1,,nFon1,nFon,4,remove 1 from position 4,flow_matching,0.3,2.0,53,177
39,remove,3.0,n,,nFon,nFo,3,remove n from position 3,flow_matching,0.3,2.0,53,177
40,replace,0.0,C,n,nFo,CFo,3,replace n at position 0 with C,flow_matching,0.3,2.0,53,177
41,remove,1.0,F,,CFo,Co,2,remove F from position 1,flow_matching,0.3,2.0,53,177
42,replace,1.0,[,o,Co,C[,2,replace o at position 1 with [,flow_matching,0.3,2.0,53,177
43,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,53,177
44,add,3.0,@,,C[C,C[C@,4,add @ at position 3,flow_matching,0.3,2.0,53,177
45,replace,1.0,o,[,C[C@,CoC@,4,replace [ at position 1 with o,flow_matching,0.3,2.0,53,177
46,replace,1.0,[,o,CoC@,C[C@,4,replace o at position 1 with [,flow_matching,0.3,2.0,53,177
47,add,4.0,H,,C[C@,C[C@H,5,add H at position 4,flow_matching,0.3,2.0,53,177
48,replace,1.0,=,[,C[C@H,C=C@H,5,replace [ at position 1 with =,flow_matching,0.3,2.0,53,177
49,add,3.0,7,,C=C@H,C=C7@H,6,add 7 at position 3,flow_matching,0.3,2.0,53,177
50,remove,4.0,@,,C=C7@H,C=C7H,5,remove @ from position 4,flow_matching,0.3,2.0,53,177
51,replace,1.0,n,=,C=C7H,CnC7H,5,replace = at position 1 with n,flow_matching,0.3,2.0,53,177
52,replace,3.0,r,7,CnC7H,CnCrH,5,replace 7 at position 3 with r,flow_matching,0.3,2.0,53,177
53,remove,1.0,n,,CnCrH,CCrH,4,remove n from position 1,flow_matching,0.3,2.0,53,177
54,add,1.0,5,,CCrH,C5CrH,5,add 5 at position 1,flow_matching,0.3,2.0,53,177
55,replace,0.0,(,C,C5CrH,(5CrH,5,replace C at position 0 with (,flow_matching,0.3,2.0,53,177
56,add,2.0,=,,(5CrH,(5=CrH,6,add = at position 2,flow_matching,0.3,2.0,53,177
57,replace,0.0,C,(,(5=CrH,C5=CrH,6,replace ( at position 0 with C,flow_matching,0.3,2.0,53,177
58,replace,1.0,[,5,C5=CrH,C[=CrH,6,replace 5 at position 1 with [,flow_matching,0.3,2.0,53,177
59,replace,4.0,1,r,C[=CrH,C[=C1H,6,replace r at position 4 with 1,flow_matching,0.3,2.0,53,177
60,replace,2.0,C,=,C[=C1H,C[CC1H,6,replace = at position 2 with C,flow_matching,0.3,2.0,53,177
61,add,1.0,o,,C[CC1H,Co[CC1H,7,add o at position 1,flow_matching,0.3,2.0,53,177
62,add,4.0,I,,Co[CC1H,Co[CIC1H,8,add I at position 4,flow_matching,0.3,2.0,53,177
63,replace,1.0,2,o,Co[CIC1H,C2[CIC1H,8,replace o at position 1 with 2,flow_matching,0.3,2.0,53,177
64,remove,7.0,H,,C2[CIC1H,C2[CIC1,7,remove H from position 7,flow_matching,0.3,2.0,53,177
65,replace,1.0,[,2,C2[CIC1,C[[CIC1,7,replace 2 at position 1 with [,flow_matching,0.3,2.0,53,177
66,replace,2.0,C,[,C[[CIC1,C[CCIC1,7,replace [ at position 2 with C,flow_matching,0.3,2.0,53,177
67,replace,3.0,@,C,C[CCIC1,C[C@IC1,7,replace C at position 3 with @,flow_matching,0.3,2.0,53,177
68,replace,4.0,H,I,C[C@IC1,C[C@HC1,7,replace I at position 4 with H,flow_matching,0.3,2.0,53,177
69,replace,3.0,5,@,C[C@HC1,C[C5HC1,7,replace @ at position 3 with 5,flow_matching,0.3,2.0,53,177
70,remove,6.0,1,,C[C5HC1,C[C5HC,6,remove 1 from position 6,flow_matching,0.3,2.0,53,177
71,replace,3.0,@,5,C[C5HC,C[C@HC,6,replace 5 at position 3 with @,flow_matching,0.3,2.0,53,177
72,replace,0.0,2,C,C[C@HC,2[C@HC,6,replace C at position 0 with 2,flow_matching,0.3,2.0,53,177
73,remove,0.0,2,,2[C@HC,[C@HC,5,remove 2 from position 0,flow_matching,0.3,2.0,53,177
74,replace,4.0,s,C,[C@HC,[C@Hs,5,replace C at position 4 with s,flow_matching,0.3,2.0,53,177
75,replace,0.0,C,[,[C@Hs,CC@Hs,5,replace [ at position 0 with C,flow_matching,0.3,2.0,53,177
76,add,4.0,o,,CC@Hs,CC@Hos,6,add o at position 4,flow_matching,0.3,2.0,53,177
77,replace,2.0,#,@,CC@Hos,CC#Hos,6,replace @ at position 2 with #,flow_matching,0.3,2.0,53,177
78,add,2.0,l,,CC#Hos,CCl#Hos,7,add l at position 2,flow_matching,0.3,2.0,53,177
79,replace,1.0,[,C,CCl#Hos,C[l#Hos,7,replace C at position 1 with [,flow_matching,0.3,2.0,53,177
80,add,3.0,2,,C[l#Hos,C[l2#Hos,8,add 2 at position 3,flow_matching,0.3,2.0,53,177
81,replace,2.0,C,l,C[l2#Hos,C[C2#Hos,8,replace l at position 2 with C,flow_matching,0.3,2.0,53,177
82,replace,1.0,c,[,C[C2#Hos,CcC2#Hos,8,replace [ at position 1 with c,flow_matching,0.3,2.0,53,177
83,replace,5.0,2,H,CcC2#Hos,CcC2#2os,8,replace H at position 5 with 2,flow_matching,0.3,2.0,53,177
84,add,0.0,S,,CcC2#2os,SCcC2#2os,9,add S at position 0,flow_matching,0.3,2.0,53,177
85,replace,0.0,C,S,SCcC2#2os,CCcC2#2os,9,replace S at position 0 with C,flow_matching,0.3,2.0,53,177
86,replace,0.0,2,C,CCcC2#2os,2CcC2#2os,9,replace C at position 0 with 2,flow_matching,0.3,2.0,53,177
87,add,2.0,F,,2CcC2#2os,2CFcC2#2os,10,add F at position 2,flow_matching,0.3,2.0,53,177
88,replace,3.0,B,c,2CFcC2#2os,2CFBC2#2os,10,replace c at position 3 with B,flow_matching,0.3,2.0,53,177
89,remove,4.0,C,,2CFBC2#2os,2CFB2#2os,9,remove C from position 4,flow_matching,0.3,2.0,53,177
90,replace,0.0,C,2,2CFB2#2os,CCFB2#2os,9,replace 2 at position 0 with C,flow_matching,0.3,2.0,53,177
91,replace,1.0,[,C,CCFB2#2os,C[FB2#2os,9,replace C at position 1 with [,flow_matching,0.3,2.0,53,177
92,replace,2.0,C,F,C[FB2#2os,C[CB2#2os,9,replace F at position 2 with C,flow_matching,0.3,2.0,53,177
93,add,2.0,o,,C[CB2#2os,C[oCB2#2os,10,add o at position 2,flow_matching,0.3,2.0,53,177
94,replace,2.0,C,o,C[oCB2#2os,C[CCB2#2os,10,replace o at position 2 with C,flow_matching,0.3,2.0,53,177
95,add,6.0,#,,C[CCB2#2os,C[CCB2##2os,11,add # at position 6,flow_matching,0.3,2.0,53,177
96,replace,3.0,@,C,C[CCB2##2os,C[C@B2##2os,11,replace C at position 3 with @,flow_matching,0.3,2.0,53,177
97,replace,4.0,H,B,C[C@B2##2os,C[C@H2##2os,11,replace B at position 4 with H,flow_matching,0.3,2.0,53,177
98,replace,7.0,l,#,C[C@H2##2os,C[C@H2#l2os,11,replace # at position 7 with l,flow_matching,0.3,2.0,53,177
99,replace,5.0,],2,C[C@H2#l2os,C[C@H]#l2os,11,replace 2 at position 5 with ],flow_matching,0.3,2.0,53,177
100,replace,6.0,(,#,C[C@H]#l2os,C[C@H](l2os,11,replace # at position 6 with (,flow_matching,0.3,2.0,53,177
101,remove,6.0,(,,C[C@H](l2os,C[C@H]l2os,10,remove ( from position 6,flow_matching,0.3,2.0,53,177
102,replace,7.0,C,2,C[C@H]l2os,C[C@H]lCos,10,replace 2 at position 7 with C,flow_matching,0.3,2.0,53,177
103,remove,3.0,@,,C[C@H]lCos,C[CH]lCos,9,remove @ from position 3,flow_matching,0.3,2.0,53,177
104,add,1.0,I,,C[CH]lCos,CI[CH]lCos,10,add I at position 1,flow_matching,0.3,2.0,53,177
105,replace,9.0,@,s,CI[CH]lCos,CI[CH]lCo@,10,replace s at position 9 with @,flow_matching,0.3,2.0,53,177
106,replace,7.0,],C,CI[CH]lCo@,CI[CH]l]o@,10,replace C at position 7 with ],flow_matching,0.3,2.0,53,177
107,replace,1.0,[,I,CI[CH]l]o@,C[[CH]l]o@,10,replace I at position 1 with [,flow_matching,0.3,2.0,53,177
108,add,7.0,l,,C[[CH]l]o@,C[[CH]ll]o@,11,add l at position 7,flow_matching,0.3,2.0,53,177
109,remove,0.0,C,,C[[CH]ll]o@,[[CH]ll]o@,10,remove C from position 0,flow_matching,0.3,2.0,53,177
110,replace,5.0,o,l,[[CH]ll]o@,[[CH]ol]o@,10,replace l at position 5 with o,flow_matching,0.3,2.0,53,177
111,add,5.0,\,,[[CH]ol]o@,[[CH]\ol]o@,11,add \ at position 5,flow_matching,0.3,2.0,53,177
112,remove,7.0,l,,[[CH]\ol]o@,[[CH]\o]o@,10,remove l from position 7,flow_matching,0.3,2.0,53,177
113,remove,2.0,C,,[[CH]\o]o@,[[H]\o]o@,9,remove C from position 2,flow_matching,0.3,2.0,53,177
114,add,2.0,2,,[[H]\o]o@,[[2H]\o]o@,10,add 2 at position 2,flow_matching,0.3,2.0,53,177
115,add,7.0,/,,[[2H]\o]o@,[[2H]\o/]o@,11,add / at position 7,flow_matching,0.3,2.0,53,177
116,replace,0.0,C,[,[[2H]\o/]o@,C[2H]\o/]o@,11,replace [ at position 0 with C,flow_matching,0.3,2.0,53,177
117,remove,9.0,o,,C[2H]\o/]o@,C[2H]\o/]@,10,remove o from position 9,flow_matching,0.3,2.0,53,177
118,replace,2.0,C,2,C[2H]\o/]@,C[CH]\o/]@,10,replace 2 at position 2 with C,flow_matching,0.3,2.0,53,177
119,remove,3.0,H,,C[CH]\o/]@,C[C]\o/]@,9,remove H from position 3,flow_matching,0.3,2.0,53,177
120,remove,1.0,[,,C[C]\o/]@,CC]\o/]@,8,remove [ from position 1,flow_matching,0.3,2.0,53,177
121,replace,5.0,\,/,CC]\o/]@,CC]\o\]@,8,replace / at position 5 with \,flow_matching,0.3,2.0,53,177
122,replace,1.0,[,C,CC]\o\]@,C[]\o\]@,8,replace C at position 1 with [,flow_matching,0.3,2.0,53,177
123,remove,6.0,],,C[]\o\]@,C[]\o\@,7,remove ] from position 6,flow_matching,0.3,2.0,53,177
124,add,4.0,5,,C[]\o\@,C[]\5o\@,8,add 5 at position 4,flow_matching,0.3,2.0,53,177
125,replace,2.0,C,],C[]\5o\@,C[C\5o\@,8,replace ] at position 2 with C,flow_matching,0.3,2.0,53,177
126,remove,2.0,C,,C[C\5o\@,C[\5o\@,7,remove C from position 2,flow_matching,0.3,2.0,53,177
127,replace,2.0,C,\,C[\5o\@,C[C5o\@,7,replace \ at position 2 with C,flow_matching,0.3,2.0,53,177
128,replace,3.0,@,5,C[C5o\@,C[C@o\@,7,replace 5 at position 3 with @,flow_matching,0.3,2.0,53,177
129,replace,4.0,H,o,C[C@o\@,C[C@H\@,7,replace o at position 4 with H,flow_matching,0.3,2.0,53,177
130,replace,5.0,],\,C[C@H\@,C[C@H]@,7,replace \ at position 5 with ],flow_matching,0.3,2.0,53,177
131,replace,6.0,(,@,C[C@H]@,C[C@H](,7,replace @ at position 6 with (,flow_matching,0.3,2.0,53,177
132,add,7.0,N,,C[C@H](,C[C@H](N,8,add N at position 7,flow_matching,0.3,2.0,53,177
133,add,8.0,C,,C[C@H](N,C[C@H](NC,9,add C at position 8,flow_matching,0.3,2.0,53,177
134,add,9.0,(,,C[C@H](NC,C[C@H](NC(,10,add ( at position 9,flow_matching,0.3,2.0,53,177
135,add,10.0,=,,C[C@H](NC(,C[C@H](NC(=,11,add = at position 10,flow_matching,0.3,2.0,53,177
136,add,11.0,O,,C[C@H](NC(=,C[C@H](NC(=O,12,add O at position 11,flow_matching,0.3,2.0,53,177
137,add,12.0,),,C[C@H](NC(=O,C[C@H](NC(=O),13,add ) at position 12,flow_matching,0.3,2.0,53,177
138,add,13.0,N,,C[C@H](NC(=O),C[C@H](NC(=O)N,14,add N at position 13,flow_matching,0.3,2.0,53,177
139,add,14.0,1,,C[C@H](NC(=O)N,C[C@H](NC(=O)N1,15,add 1 at position 14,flow_matching,0.3,2.0,53,177
140,add,15.0,C,,C[C@H](NC(=O)N1,C[C@H](NC(=O)N1C,16,add C at position 15,flow_matching,0.3,2.0,53,177
141,add,16.0,C,,C[C@H](NC(=O)N1C,C[C@H](NC(=O)N1CC,17,add C at position 16,flow_matching,0.3,2.0,53,177
142,add,17.0,C,,C[C@H](NC(=O)N1CC,C[C@H](NC(=O)N1CCC,18,add C at position 17,flow_matching,0.3,2.0,53,177
143,add,18.0,C,,C[C@H](NC(=O)N1CCC,C[C@H](NC(=O)N1CCCC,19,add C at position 18,flow_matching,0.3,2.0,53,177
144,add,19.0,[,,C[C@H](NC(=O)N1CCCC,C[C@H](NC(=O)N1CCCC[,20,add [ at position 19,flow_matching,0.3,2.0,53,177
145,add,20.0,C,,C[C@H](NC(=O)N1CCCC[,C[C@H](NC(=O)N1CCCC[C,21,add C at position 20,flow_matching,0.3,2.0,53,177
146,add,21.0,@,,C[C@H](NC(=O)N1CCCC[C,C[C@H](NC(=O)N1CCCC[C@,22,add @ at position 21,flow_matching,0.3,2.0,53,177
147,add,22.0,@,,C[C@H](NC(=O)N1CCCC[C@,C[C@H](NC(=O)N1CCCC[C@@,23,add @ at position 22,flow_matching,0.3,2.0,53,177
148,add,23.0,H,,C[C@H](NC(=O)N1CCCC[C@@,C[C@H](NC(=O)N1CCCC[C@@H,24,add H at position 23,flow_matching,0.3,2.0,53,177
149,add,24.0,],,C[C@H](NC(=O)N1CCCC[C@@H,C[C@H](NC(=O)N1CCCC[C@@H],25,add ] at position 24,flow_matching,0.3,2.0,53,177
150,add,25.0,1,,C[C@H](NC(=O)N1CCCC[C@@H],C[C@H](NC(=O)N1CCCC[C@@H]1,26,add 1 at position 25,flow_matching,0.3,2.0,53,177
151,add,26.0,C,,C[C@H](NC(=O)N1CCCC[C@@H]1,C[C@H](NC(=O)N1CCCC[C@@H]1C,27,add C at position 26,flow_matching,0.3,2.0,53,177
152,add,27.0,1,,C[C@H](NC(=O)N1CCCC[C@@H]1C,C[C@H](NC(=O)N1CCCC[C@@H]1C1,28,add 1 at position 27,flow_matching,0.3,2.0,53,177
153,add,28.0,O,,C[C@H](NC(=O)N1CCCC[C@@H]1C1,C[C@H](NC(=O)N1CCCC[C@@H]1C1O,29,add O at position 28,flow_matching,0.3,2.0,53,177
154,add,29.0,C,,C[C@H](NC(=O)N1CCCC[C@@H]1C1O,C[C@H](NC(=O)N1CCCC[C@@H]1C1OC,30,add C at position 29,flow_matching,0.3,2.0,53,177
155,add,30.0,C,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OC,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCC,31,add C at position 30,flow_matching,0.3,2.0,53,177
156,add,31.0,O,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCC,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO,32,add O at position 31,flow_matching,0.3,2.0,53,177
157,add,32.0,1,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1,33,add 1 at position 32,flow_matching,0.3,2.0,53,177
158,add,33.0,),,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1),34,add ) at position 33,flow_matching,0.3,2.0,53,177
159,add,34.0,c,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1),C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c,35,add c at position 34,flow_matching,0.3,2.0,53,177
160,add,35.0,1,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1,36,add 1 at position 35,flow_matching,0.3,2.0,53,177
161,add,36.0,c,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1c,37,add c at position 36,flow_matching,0.3,2.0,53,177
162,add,37.0,c,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1c,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cc,38,add c at position 37,flow_matching,0.3,2.0,53,177
163,add,38.0,c,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cc,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1ccc,39,add c at position 38,flow_matching,0.3,2.0,53,177
164,add,39.0,c,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1ccc,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc,40,add c at position 39,flow_matching,0.3,2.0,53,177
165,add,40.0,(,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(,41,add ( at position 40,flow_matching,0.3,2.0,53,177
166,add,41.0,-,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-,42,add - at position 41,flow_matching,0.3,2.0,53,177
167,add,42.0,n,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n,43,add n at position 42,flow_matching,0.3,2.0,53,177
168,add,43.0,2,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2,44,add 2 at position 43,flow_matching,0.3,2.0,53,177
169,add,44.0,c,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2c,45,add c at position 44,flow_matching,0.3,2.0,53,177
170,add,45.0,c,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2c,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2cc,46,add c at position 45,flow_matching,0.3,2.0,53,177
171,add,46.0,n,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2cc,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccn,47,add n at position 46,flow_matching,0.3,2.0,53,177
172,add,47.0,c,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccn,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc,48,add c at position 47,flow_matching,0.3,2.0,53,177
173,add,48.0,2,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc2,49,add 2 at position 48,flow_matching,0.3,2.0,53,177
174,add,49.0,),,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc2,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc2),50,add ) at position 49,flow_matching,0.3,2.0,53,177
175,add,50.0,c,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc2),C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc2)c,51,add c at position 50,flow_matching,0.3,2.0,53,177
176,add,51.0,1,,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc2)c,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc2)c1,52,add 1 at position 51,flow_matching,0.3,2.0,53,177
177,add,52.0,"
",,C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc2)c1,"C[C@H](NC(=O)N1CCCC[C@@H]1C1OCCO1)c1cccc(-n2ccnc2)c1
",53,"add 
 at position 52",flow_matching,0.3,2.0,53,177
