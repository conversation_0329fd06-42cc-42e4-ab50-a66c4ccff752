step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,48,141
1,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,48,141
2,add,0.0,#,,6,#6,2,add # at position 0,flow_matching,0.3,2.0,48,141
3,add,1.0,1,,#6,#16,3,add 1 at position 1,flow_matching,0.3,2.0,48,141
4,add,2.0,s,,#16,#1s6,4,add s at position 2,flow_matching,0.3,2.0,48,141
5,replace,0.0,C,#,#1s6,C1s6,4,replace # at position 0 with C,flow_matching,0.3,2.0,48,141
6,replace,1.0,n,1,C1s6,Cns6,4,replace 1 at position 1 with n,flow_matching,0.3,2.0,48,141
7,replace,1.0,c,n,Cns6,Ccs6,4,replace n at position 1 with c,flow_matching,0.3,2.0,48,141
8,add,1.0,(,,Ccs6,C(cs6,5,add ( at position 1,flow_matching,0.3,2.0,48,141
9,replace,1.0,c,(,C(cs6,Cccs6,5,replace ( at position 1 with c,flow_matching,0.3,2.0,48,141
10,replace,2.0,1,c,Cccs6,Cc1s6,5,replace c at position 2 with 1,flow_matching,0.3,2.0,48,141
11,replace,2.0,F,1,Cc1s6,CcFs6,5,replace 1 at position 2 with F,flow_matching,0.3,2.0,48,141
12,remove,4.0,6,,CcFs6,CcFs,4,remove 6 from position 4,flow_matching,0.3,2.0,48,141
13,replace,2.0,1,F,CcFs,Cc1s,4,replace F at position 2 with 1,flow_matching,0.3,2.0,48,141
14,replace,3.0,o,s,Cc1s,Cc1o,4,replace s at position 3 with o,flow_matching,0.3,2.0,48,141
15,replace,1.0,#,c,Cc1o,C#1o,4,replace c at position 1 with #,flow_matching,0.3,2.0,48,141
16,replace,1.0,c,#,C#1o,Cc1o,4,replace # at position 1 with c,flow_matching,0.3,2.0,48,141
17,add,4.0,c,,Cc1o,Cc1oc,5,add c at position 4,flow_matching,0.3,2.0,48,141
18,add,2.0,],,Cc1oc,Cc]1oc,6,add ] at position 2,flow_matching,0.3,2.0,48,141
19,remove,5.0,c,,Cc]1oc,Cc]1o,5,remove c from position 5,flow_matching,0.3,2.0,48,141
20,remove,1.0,c,,Cc]1o,C]1o,4,remove c from position 1,flow_matching,0.3,2.0,48,141
21,replace,1.0,c,],C]1o,Cc1o,4,replace ] at position 1 with c,flow_matching,0.3,2.0,48,141
22,replace,3.0,/,o,Cc1o,Cc1/,4,replace o at position 3 with /,flow_matching,0.3,2.0,48,141
23,add,1.0,2,,Cc1/,C2c1/,5,add 2 at position 1,flow_matching,0.3,2.0,48,141
24,replace,2.0,n,c,C2c1/,C2n1/,5,replace c at position 2 with n,flow_matching,0.3,2.0,48,141
25,add,5.0,1,,C2n1/,C2n1/1,6,add 1 at position 5,flow_matching,0.3,2.0,48,141
26,replace,1.0,c,2,C2n1/1,Ccn1/1,6,replace 2 at position 1 with c,flow_matching,0.3,2.0,48,141
27,add,0.0,/,,Ccn1/1,/Ccn1/1,7,add / at position 0,flow_matching,0.3,2.0,48,141
28,replace,0.0,C,/,/Ccn1/1,CCcn1/1,7,replace / at position 0 with C,flow_matching,0.3,2.0,48,141
29,remove,2.0,c,,CCcn1/1,CCn1/1,6,remove c from position 2,flow_matching,0.3,2.0,48,141
30,add,0.0,7,,CCn1/1,7CCn1/1,7,add 7 at position 0,flow_matching,0.3,2.0,48,141
31,replace,0.0,C,7,7CCn1/1,CCCn1/1,7,replace 7 at position 0 with C,flow_matching,0.3,2.0,48,141
32,remove,1.0,C,,CCCn1/1,CCn1/1,6,remove C from position 1,flow_matching,0.3,2.0,48,141
33,replace,1.0,c,C,CCn1/1,Ccn1/1,6,replace C at position 1 with c,flow_matching,0.3,2.0,48,141
34,replace,2.0,1,n,Ccn1/1,Cc11/1,6,replace n at position 2 with 1,flow_matching,0.3,2.0,48,141
35,replace,3.0,o,1,Cc11/1,Cc1o/1,6,replace 1 at position 3 with o,flow_matching,0.3,2.0,48,141
36,replace,5.0,[,1,Cc1o/1,Cc1o/[,6,replace 1 at position 5 with [,flow_matching,0.3,2.0,48,141
37,replace,4.0,1,/,Cc1o/[,Cc1o1[,6,replace / at position 4 with 1,flow_matching,0.3,2.0,48,141
38,replace,4.0,c,1,Cc1o1[,Cc1oc[,6,replace 1 at position 4 with c,flow_matching,0.3,2.0,48,141
39,remove,0.0,C,,Cc1oc[,c1oc[,5,remove C from position 0,flow_matching,0.3,2.0,48,141
40,add,1.0,1,,c1oc[,c11oc[,6,add 1 at position 1,flow_matching,0.3,2.0,48,141
41,add,0.0,(,,c11oc[,(c11oc[,7,add ( at position 0,flow_matching,0.3,2.0,48,141
42,add,5.0,C,,(c11oc[,(c11oCc[,8,add C at position 5,flow_matching,0.3,2.0,48,141
43,replace,3.0,4,1,(c11oCc[,(c14oCc[,8,replace 1 at position 3 with 4,flow_matching,0.3,2.0,48,141
44,remove,1.0,c,,(c14oCc[,(14oCc[,7,remove c from position 1,flow_matching,0.3,2.0,48,141
45,replace,5.0,I,c,(14oCc[,(14oCI[,7,replace c at position 5 with I,flow_matching,0.3,2.0,48,141
46,remove,0.0,(,,(14oCI[,14oCI[,6,remove ( from position 0,flow_matching,0.3,2.0,48,141
47,replace,0.0,C,1,14oCI[,C4oCI[,6,replace 1 at position 0 with C,flow_matching,0.3,2.0,48,141
48,add,2.0,n,,C4oCI[,C4noCI[,7,add n at position 2,flow_matching,0.3,2.0,48,141
49,replace,2.0,r,n,C4noCI[,C4roCI[,7,replace n at position 2 with r,flow_matching,0.3,2.0,48,141
50,add,0.0,2,,C4roCI[,2C4roCI[,8,add 2 at position 0,flow_matching,0.3,2.0,48,141
51,replace,0.0,C,2,2C4roCI[,CC4roCI[,8,replace 2 at position 0 with C,flow_matching,0.3,2.0,48,141
52,remove,4.0,o,,CC4roCI[,CC4rCI[,7,remove o from position 4,flow_matching,0.3,2.0,48,141
53,add,2.0,6,,CC4rCI[,CC64rCI[,8,add 6 at position 2,flow_matching,0.3,2.0,48,141
54,add,5.0,\,,CC64rCI[,CC64r\CI[,9,add \ at position 5,flow_matching,0.3,2.0,48,141
55,add,1.0,#,,CC64r\CI[,C#C64r\CI[,10,add # at position 1,flow_matching,0.3,2.0,48,141
56,remove,5.0,r,,C#C64r\CI[,C#C64\CI[,9,remove r from position 5,flow_matching,0.3,2.0,48,141
57,replace,2.0,-,C,C#C64\CI[,C#-64\CI[,9,replace C at position 2 with -,flow_matching,0.3,2.0,48,141
58,remove,5.0,\,,C#-64\CI[,C#-64CI[,8,remove \ from position 5,flow_matching,0.3,2.0,48,141
59,remove,7.0,[,,C#-64CI[,C#-64CI,7,remove [ from position 7,flow_matching,0.3,2.0,48,141
60,replace,1.0,c,#,C#-64CI,Cc-64CI,7,replace # at position 1 with c,flow_matching,0.3,2.0,48,141
61,remove,3.0,6,,Cc-64CI,Cc-4CI,6,remove 6 from position 3,flow_matching,0.3,2.0,48,141
62,add,5.0,\,,Cc-4CI,Cc-4C\I,7,add \ at position 5,flow_matching,0.3,2.0,48,141
63,add,0.0,7,,Cc-4C\I,7Cc-4C\I,8,add 7 at position 0,flow_matching,0.3,2.0,48,141
64,replace,3.0,+,-,7Cc-4C\I,7Cc+4C\I,8,replace - at position 3 with +,flow_matching,0.3,2.0,48,141
65,remove,5.0,C,,7Cc+4C\I,7Cc+4\I,7,remove C from position 5,flow_matching,0.3,2.0,48,141
66,replace,0.0,C,7,7Cc+4\I,CCc+4\I,7,replace 7 at position 0 with C,flow_matching,0.3,2.0,48,141
67,replace,1.0,c,C,CCc+4\I,Ccc+4\I,7,replace C at position 1 with c,flow_matching,0.3,2.0,48,141
68,replace,6.0,o,I,Ccc+4\I,Ccc+4\o,7,replace I at position 6 with o,flow_matching,0.3,2.0,48,141
69,replace,2.0,1,c,Ccc+4\o,Cc1+4\o,7,replace c at position 2 with 1,flow_matching,0.3,2.0,48,141
70,replace,3.0,o,+,Cc1+4\o,Cc1o4\o,7,replace + at position 3 with o,flow_matching,0.3,2.0,48,141
71,add,2.0,#,,Cc1o4\o,Cc#1o4\o,8,add # at position 2,flow_matching,0.3,2.0,48,141
72,add,1.0,r,,Cc#1o4\o,Crc#1o4\o,9,add r at position 1,flow_matching,0.3,2.0,48,141
73,add,2.0,4,,Crc#1o4\o,Cr4c#1o4\o,10,add 4 at position 2,flow_matching,0.3,2.0,48,141
74,replace,1.0,c,r,Cr4c#1o4\o,Cc4c#1o4\o,10,replace r at position 1 with c,flow_matching,0.3,2.0,48,141
75,replace,6.0,+,o,Cc4c#1o4\o,Cc4c#1+4\o,10,replace o at position 6 with +,flow_matching,0.3,2.0,48,141
76,add,0.0,s,,Cc4c#1+4\o,sCc4c#1+4\o,11,add s at position 0,flow_matching,0.3,2.0,48,141
77,replace,0.0,C,s,sCc4c#1+4\o,CCc4c#1+4\o,11,replace s at position 0 with C,flow_matching,0.3,2.0,48,141
78,remove,5.0,#,,CCc4c#1+4\o,CCc4c1+4\o,10,remove # from position 5,flow_matching,0.3,2.0,48,141
79,replace,3.0,-,4,CCc4c1+4\o,CCc-c1+4\o,10,replace 4 at position 3 with -,flow_matching,0.3,2.0,48,141
80,add,2.0,4,,CCc-c1+4\o,CC4c-c1+4\o,11,add 4 at position 2,flow_matching,0.3,2.0,48,141
81,add,5.0,C,,CC4c-c1+4\o,CC4c-Cc1+4\o,12,add C at position 5,flow_matching,0.3,2.0,48,141
82,add,2.0,l,,CC4c-Cc1+4\o,CCl4c-Cc1+4\o,13,add l at position 2,flow_matching,0.3,2.0,48,141
83,replace,6.0,#,C,CCl4c-Cc1+4\o,CCl4c-#c1+4\o,13,replace C at position 6 with #,flow_matching,0.3,2.0,48,141
84,add,12.0,6,,CCl4c-#c1+4\o,CCl4c-#c1+4\6o,14,add 6 at position 12,flow_matching,0.3,2.0,48,141
85,remove,6.0,#,,CCl4c-#c1+4\6o,CCl4c-c1+4\6o,13,remove # from position 6,flow_matching,0.3,2.0,48,141
86,replace,1.0,c,C,CCl4c-c1+4\6o,Ccl4c-c1+4\6o,13,replace C at position 1 with c,flow_matching,0.3,2.0,48,141
87,remove,11.0,6,,Ccl4c-c1+4\6o,Ccl4c-c1+4\o,12,remove 6 from position 11,flow_matching,0.3,2.0,48,141
88,replace,8.0,s,+,Ccl4c-c1+4\o,Ccl4c-c1s4\o,12,replace + at position 8 with s,flow_matching,0.3,2.0,48,141
89,replace,11.0,n,o,Ccl4c-c1s4\o,Ccl4c-c1s4\n,12,replace o at position 11 with n,flow_matching,0.3,2.0,48,141
90,add,4.0,o,,Ccl4c-c1s4\n,Ccl4oc-c1s4\n,13,add o at position 4,flow_matching,0.3,2.0,48,141
91,replace,8.0,5,1,Ccl4oc-c1s4\n,Ccl4oc-c5s4\n,13,replace 1 at position 8 with 5,flow_matching,0.3,2.0,48,141
92,add,9.0,o,,Ccl4oc-c5s4\n,Ccl4oc-c5os4\n,14,add o at position 9,flow_matching,0.3,2.0,48,141
93,replace,2.0,1,l,Ccl4oc-c5os4\n,Cc14oc-c5os4\n,14,replace l at position 2 with 1,flow_matching,0.3,2.0,48,141
94,replace,3.0,o,4,Cc14oc-c5os4\n,Cc1ooc-c5os4\n,14,replace 4 at position 3 with o,flow_matching,0.3,2.0,48,141
95,replace,9.0,),o,Cc1ooc-c5os4\n,Cc1ooc-c5)s4\n,14,replace o at position 9 with ),flow_matching,0.3,2.0,48,141
96,remove,10.0,s,,Cc1ooc-c5)s4\n,Cc1ooc-c5)4\n,13,remove s from position 10,flow_matching,0.3,2.0,48,141
97,add,3.0,H,,Cc1ooc-c5)4\n,Cc1Hooc-c5)4\n,14,add H at position 3,flow_matching,0.3,2.0,48,141
98,replace,3.0,o,H,Cc1Hooc-c5)4\n,Cc1oooc-c5)4\n,14,replace H at position 3 with o,flow_matching,0.3,2.0,48,141
99,replace,4.0,c,o,Cc1oooc-c5)4\n,Cc1ococ-c5)4\n,14,replace o at position 4 with c,flow_matching,0.3,2.0,48,141
100,replace,5.0,c,o,Cc1ococ-c5)4\n,Cc1occc-c5)4\n,14,replace o at position 5 with c,flow_matching,0.3,2.0,48,141
101,replace,7.0,1,-,Cc1occc-c5)4\n,Cc1occc1c5)4\n,14,replace - at position 7 with 1,flow_matching,0.3,2.0,48,141
102,replace,8.0,C,c,Cc1occc1c5)4\n,Cc1occc1C5)4\n,14,replace c at position 8 with C,flow_matching,0.3,2.0,48,141
103,replace,9.0,(,5,Cc1occc1C5)4\n,Cc1occc1C()4\n,14,replace 5 at position 9 with (,flow_matching,0.3,2.0,48,141
104,replace,10.0,=,),Cc1occc1C()4\n,Cc1occc1C(=4\n,14,replace ) at position 10 with =,flow_matching,0.3,2.0,48,141
105,replace,11.0,O,4,Cc1occc1C(=4\n,Cc1occc1C(=O\n,14,replace 4 at position 11 with O,flow_matching,0.3,2.0,48,141
106,replace,12.0,),\,Cc1occc1C(=O\n,Cc1occc1C(=O)n,14,replace \ at position 12 with ),flow_matching,0.3,2.0,48,141
107,replace,13.0,/,n,Cc1occc1C(=O)n,Cc1occc1C(=O)/,14,replace n at position 13 with /,flow_matching,0.3,2.0,48,141
108,add,14.0,C,,Cc1occc1C(=O)/,Cc1occc1C(=O)/C,15,add C at position 14,flow_matching,0.3,2.0,48,141
109,add,15.0,(,,Cc1occc1C(=O)/C,Cc1occc1C(=O)/C(,16,add ( at position 15,flow_matching,0.3,2.0,48,141
110,add,16.0,C,,Cc1occc1C(=O)/C(,Cc1occc1C(=O)/C(C,17,add C at position 16,flow_matching,0.3,2.0,48,141
111,add,17.0,#,,Cc1occc1C(=O)/C(C,Cc1occc1C(=O)/C(C#,18,add # at position 17,flow_matching,0.3,2.0,48,141
112,add,18.0,N,,Cc1occc1C(=O)/C(C#,Cc1occc1C(=O)/C(C#N,19,add N at position 18,flow_matching,0.3,2.0,48,141
113,add,19.0,),,Cc1occc1C(=O)/C(C#N,Cc1occc1C(=O)/C(C#N),20,add ) at position 19,flow_matching,0.3,2.0,48,141
114,add,20.0,=,,Cc1occc1C(=O)/C(C#N),Cc1occc1C(=O)/C(C#N)=,21,add = at position 20,flow_matching,0.3,2.0,48,141
115,add,21.0,C,,Cc1occc1C(=O)/C(C#N)=,Cc1occc1C(=O)/C(C#N)=C,22,add C at position 21,flow_matching,0.3,2.0,48,141
116,add,22.0,/,,Cc1occc1C(=O)/C(C#N)=C,Cc1occc1C(=O)/C(C#N)=C/,23,add / at position 22,flow_matching,0.3,2.0,48,141
117,add,23.0,c,,Cc1occc1C(=O)/C(C#N)=C/,Cc1occc1C(=O)/C(C#N)=C/c,24,add c at position 23,flow_matching,0.3,2.0,48,141
118,add,24.0,1,,Cc1occc1C(=O)/C(C#N)=C/c,Cc1occc1C(=O)/C(C#N)=C/c1,25,add 1 at position 24,flow_matching,0.3,2.0,48,141
119,add,25.0,c,,Cc1occc1C(=O)/C(C#N)=C/c1,Cc1occc1C(=O)/C(C#N)=C/c1c,26,add c at position 25,flow_matching,0.3,2.0,48,141
120,add,26.0,c,,Cc1occc1C(=O)/C(C#N)=C/c1c,Cc1occc1C(=O)/C(C#N)=C/c1cc,27,add c at position 26,flow_matching,0.3,2.0,48,141
121,add,27.0,c,,Cc1occc1C(=O)/C(C#N)=C/c1cc,Cc1occc1C(=O)/C(C#N)=C/c1ccc,28,add c at position 27,flow_matching,0.3,2.0,48,141
122,add,28.0,(,,Cc1occc1C(=O)/C(C#N)=C/c1ccc,Cc1occc1C(=O)/C(C#N)=C/c1ccc(,29,add ( at position 28,flow_matching,0.3,2.0,48,141
123,add,29.0,[,,Cc1occc1C(=O)/C(C#N)=C/c1ccc(,Cc1occc1C(=O)/C(C#N)=C/c1ccc([,30,add [ at position 29,flow_matching,0.3,2.0,48,141
124,add,30.0,C,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C,31,add C at position 30,flow_matching,0.3,2.0,48,141
125,add,31.0,@,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@,32,add @ at position 31,flow_matching,0.3,2.0,48,141
126,add,32.0,@,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@,33,add @ at position 32,flow_matching,0.3,2.0,48,141
127,add,33.0,H,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H,34,add H at position 33,flow_matching,0.3,2.0,48,141
128,add,34.0,],,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H],35,add ] at position 34,flow_matching,0.3,2.0,48,141
129,add,35.0,2,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H],Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2,36,add 2 at position 35,flow_matching,0.3,2.0,48,141
130,add,36.0,C,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C,37,add C at position 36,flow_matching,0.3,2.0,48,141
131,add,37.0,[,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[,38,add [ at position 37,flow_matching,0.3,2.0,48,141
132,add,38.0,C,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C,39,add C at position 38,flow_matching,0.3,2.0,48,141
133,add,39.0,@,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@,40,add @ at position 39,flow_matching,0.3,2.0,48,141
134,add,40.0,H,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H,41,add H at position 40,flow_matching,0.3,2.0,48,141
135,add,41.0,],,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H],42,add ] at position 41,flow_matching,0.3,2.0,48,141
136,add,42.0,2,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H],Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2,43,add 2 at position 42,flow_matching,0.3,2.0,48,141
137,add,43.0,C,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2C,44,add C at position 43,flow_matching,0.3,2.0,48,141
138,add,44.0,),,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2C,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2C),45,add ) at position 44,flow_matching,0.3,2.0,48,141
139,add,45.0,o,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2C),Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2C)o,46,add o at position 45,flow_matching,0.3,2.0,48,141
140,add,46.0,1,,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2C)o,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2C)o1,47,add 1 at position 46,flow_matching,0.3,2.0,48,141
141,add,47.0,"
",,Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2C)o1,"Cc1occc1C(=O)/C(C#N)=C/c1ccc([C@@H]2C[C@H]2C)o1
",48,"add 
 at position 47",flow_matching,0.3,2.0,48,141
