step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,41,151
1,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,41,151
2,replace,0.0,C,F,F,C,1,replace F at position 0 with C,flow_matching,0.3,2.0,41,151
3,add,1.0,1,,C,C1,2,add 1 at position 1,flow_matching,0.3,2.0,41,151
4,replace,1.0,F,1,C1,CF,2,replace 1 at position 1 with F,flow_matching,0.3,2.0,41,151
5,replace,1.0,C,F,CF,CC,2,replace F at position 1 with C,flow_matching,0.3,2.0,41,151
6,replace,1.0,c,<PERSON>,<PERSON>,Cc,2,replace <PERSON> at position 1 with c,flow_matching,0.3,2.0,41,151
7,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,41,151
8,add,1.0,B,,Cc1,CBc1,4,add B at position 1,flow_matching,0.3,2.0,41,151
9,add,4.0,5,,CBc1,CBc15,5,add 5 at position 4,flow_matching,0.3,2.0,41,151
10,remove,0.0,C,,CBc15,Bc15,4,remove C from position 0,flow_matching,0.3,2.0,41,151
11,remove,2.0,1,,Bc15,Bc5,3,remove 1 from position 2,flow_matching,0.3,2.0,41,151
12,replace,0.0,C,B,Bc5,Cc5,3,replace B at position 0 with C,flow_matching,0.3,2.0,41,151
13,replace,2.0,1,5,Cc5,Cc1,3,replace 5 at position 2 with 1,flow_matching,0.3,2.0,41,151
14,add,3.0,],,Cc1,Cc1],4,add ] at position 3,flow_matching,0.3,2.0,41,151
15,remove,1.0,c,,Cc1],C1],3,remove c from position 1,flow_matching,0.3,2.0,41,151
16,add,0.0,s,,C1],sC1],4,add s at position 0,flow_matching,0.3,2.0,41,151
17,add,4.0,(,,sC1],sC1](,5,add ( at position 4,flow_matching,0.3,2.0,41,151
18,replace,0.0,C,s,sC1](,CC1](,5,replace s at position 0 with C,flow_matching,0.3,2.0,41,151
19,replace,1.0,c,C,CC1](,Cc1](,5,replace C at position 1 with c,flow_matching,0.3,2.0,41,151
20,remove,3.0,],,Cc1](,Cc1(,4,remove ] from position 3,flow_matching,0.3,2.0,41,151
21,add,4.0,=,,Cc1(,Cc1(=,5,add = at position 4,flow_matching,0.3,2.0,41,151
22,add,4.0,2,,Cc1(=,Cc1(2=,6,add 2 at position 4,flow_matching,0.3,2.0,41,151
23,add,4.0,B,,Cc1(2=,Cc1(B2=,7,add B at position 4,flow_matching,0.3,2.0,41,151
24,add,5.0,n,,Cc1(B2=,Cc1(Bn2=,8,add n at position 5,flow_matching,0.3,2.0,41,151
25,replace,7.0,s,=,Cc1(Bn2=,Cc1(Bn2s,8,replace = at position 7 with s,flow_matching,0.3,2.0,41,151
26,replace,0.0,2,C,Cc1(Bn2s,2c1(Bn2s,8,replace C at position 0 with 2,flow_matching,0.3,2.0,41,151
27,replace,0.0,C,2,2c1(Bn2s,Cc1(Bn2s,8,replace 2 at position 0 with C,flow_matching,0.3,2.0,41,151
28,replace,0.0,F,C,Cc1(Bn2s,Fc1(Bn2s,8,replace C at position 0 with F,flow_matching,0.3,2.0,41,151
29,remove,6.0,2,,Fc1(Bn2s,Fc1(Bns,7,remove 2 from position 6,flow_matching,0.3,2.0,41,151
30,remove,2.0,1,,Fc1(Bns,Fc(Bns,6,remove 1 from position 2,flow_matching,0.3,2.0,41,151
31,add,5.0,B,,Fc(Bns,Fc(BnBs,7,add B at position 5,flow_matching,0.3,2.0,41,151
32,remove,6.0,s,,Fc(BnBs,Fc(BnB,6,remove s from position 6,flow_matching,0.3,2.0,41,151
33,add,4.0,+,,Fc(BnB,Fc(B+nB,7,add + at position 4,flow_matching,0.3,2.0,41,151
34,add,4.0,F,,Fc(B+nB,Fc(BF+nB,8,add F at position 4,flow_matching,0.3,2.0,41,151
35,replace,0.0,C,F,Fc(BF+nB,Cc(BF+nB,8,replace F at position 0 with C,flow_matching,0.3,2.0,41,151
36,replace,2.0,1,(,Cc(BF+nB,Cc1BF+nB,8,replace ( at position 2 with 1,flow_matching,0.3,2.0,41,151
37,replace,2.0,/,1,Cc1BF+nB,Cc/BF+nB,8,replace 1 at position 2 with /,flow_matching,0.3,2.0,41,151
38,replace,6.0,O,n,Cc/BF+nB,Cc/BF+OB,8,replace n at position 6 with O,flow_matching,0.3,2.0,41,151
39,remove,7.0,B,,Cc/BF+OB,Cc/BF+O,7,remove B from position 7,flow_matching,0.3,2.0,41,151
40,replace,0.0,F,C,Cc/BF+O,Fc/BF+O,7,replace C at position 0 with F,flow_matching,0.3,2.0,41,151
41,replace,0.0,C,F,Fc/BF+O,Cc/BF+O,7,replace F at position 0 with C,flow_matching,0.3,2.0,41,151
42,replace,6.0,s,O,Cc/BF+O,Cc/BF+s,7,replace O at position 6 with s,flow_matching,0.3,2.0,41,151
43,replace,0.0,2,C,Cc/BF+s,2c/BF+s,7,replace C at position 0 with 2,flow_matching,0.3,2.0,41,151
44,replace,3.0,n,B,2c/BF+s,2c/nF+s,7,replace B at position 3 with n,flow_matching,0.3,2.0,41,151
45,add,1.0,(,,2c/nF+s,2(c/nF+s,8,add ( at position 1,flow_matching,0.3,2.0,41,151
46,replace,4.0,O,n,2(c/nF+s,2(c/OF+s,8,replace n at position 4 with O,flow_matching,0.3,2.0,41,151
47,replace,0.0,C,2,2(c/OF+s,C(c/OF+s,8,replace 2 at position 0 with C,flow_matching,0.3,2.0,41,151
48,remove,3.0,/,,C(c/OF+s,C(cOF+s,7,remove / from position 3,flow_matching,0.3,2.0,41,151
49,replace,4.0,S,F,C(cOF+s,C(cOS+s,7,replace F at position 4 with S,flow_matching,0.3,2.0,41,151
50,replace,2.0,S,c,C(cOS+s,C(SOS+s,7,replace c at position 2 with S,flow_matching,0.3,2.0,41,151
51,add,6.0,N,,C(SOS+s,C(SOS+Ns,8,add N at position 6,flow_matching,0.3,2.0,41,151
52,replace,1.0,c,(,C(SOS+Ns,CcSOS+Ns,8,replace ( at position 1 with c,flow_matching,0.3,2.0,41,151
53,replace,5.0,/,+,CcSOS+Ns,CcSOS/Ns,8,replace + at position 5 with /,flow_matching,0.3,2.0,41,151
54,replace,2.0,1,S,CcSOS/Ns,Cc1OS/Ns,8,replace S at position 2 with 1,flow_matching,0.3,2.0,41,151
55,replace,3.0,c,O,Cc1OS/Ns,Cc1cS/Ns,8,replace O at position 3 with c,flow_matching,0.3,2.0,41,151
56,remove,2.0,1,,Cc1cS/Ns,CccS/Ns,7,remove 1 from position 2,flow_matching,0.3,2.0,41,151
57,replace,2.0,1,c,CccS/Ns,Cc1S/Ns,7,replace c at position 2 with 1,flow_matching,0.3,2.0,41,151
58,remove,3.0,S,,Cc1S/Ns,Cc1/Ns,6,remove S from position 3,flow_matching,0.3,2.0,41,151
59,replace,1.0,),c,Cc1/Ns,C)1/Ns,6,replace c at position 1 with ),flow_matching,0.3,2.0,41,151
60,remove,0.0,C,,C)1/Ns,)1/Ns,5,remove C from position 0,flow_matching,0.3,2.0,41,151
61,replace,3.0,\,N,)1/Ns,)1/\s,5,replace N at position 3 with \,flow_matching,0.3,2.0,41,151
62,replace,4.0,O,s,)1/\s,)1/\O,5,replace s at position 4 with O,flow_matching,0.3,2.0,41,151
63,remove,3.0,\,,)1/\O,)1/O,4,remove \ from position 3,flow_matching,0.3,2.0,41,151
64,replace,0.0,C,),)1/O,C1/O,4,replace ) at position 0 with C,flow_matching,0.3,2.0,41,151
65,replace,1.0,c,1,C1/O,Cc/O,4,replace 1 at position 1 with c,flow_matching,0.3,2.0,41,151
66,replace,3.0,/,O,Cc/O,Cc//,4,replace O at position 3 with /,flow_matching,0.3,2.0,41,151
67,replace,2.0,1,/,Cc//,Cc1/,4,replace / at position 2 with 1,flow_matching,0.3,2.0,41,151
68,replace,1.0,#,c,Cc1/,C#1/,4,replace c at position 1 with #,flow_matching,0.3,2.0,41,151
69,replace,0.0,+,C,C#1/,+#1/,4,replace C at position 0 with +,flow_matching,0.3,2.0,41,151
70,replace,0.0,C,+,+#1/,C#1/,4,replace + at position 0 with C,flow_matching,0.3,2.0,41,151
71,remove,2.0,1,,C#1/,C#/,3,remove 1 from position 2,flow_matching,0.3,2.0,41,151
72,replace,0.0,+,C,C#/,+#/,3,replace C at position 0 with +,flow_matching,0.3,2.0,41,151
73,add,2.0,#,,+#/,+##/,4,add # at position 2,flow_matching,0.3,2.0,41,151
74,replace,0.0,C,+,+##/,C##/,4,replace + at position 0 with C,flow_matching,0.3,2.0,41,151
75,remove,1.0,#,,C##/,C#/,3,remove # from position 1,flow_matching,0.3,2.0,41,151
76,add,1.0,+,,C#/,C+#/,4,add + at position 1,flow_matching,0.3,2.0,41,151
77,remove,2.0,#,,C+#/,C+/,3,remove # from position 2,flow_matching,0.3,2.0,41,151
78,replace,1.0,c,+,C+/,Cc/,3,replace + at position 1 with c,flow_matching,0.3,2.0,41,151
79,add,1.0,@,,Cc/,C@c/,4,add @ at position 1,flow_matching,0.3,2.0,41,151
80,add,3.0,S,,C@c/,C@cS/,5,add S at position 3,flow_matching,0.3,2.0,41,151
81,replace,1.0,c,@,C@cS/,CccS/,5,replace @ at position 1 with c,flow_matching,0.3,2.0,41,151
82,remove,2.0,c,,CccS/,CcS/,4,remove c from position 2,flow_matching,0.3,2.0,41,151
83,remove,1.0,c,,CcS/,CS/,3,remove c from position 1,flow_matching,0.3,2.0,41,151
84,replace,0.0,#,C,CS/,#S/,3,replace C at position 0 with #,flow_matching,0.3,2.0,41,151
85,add,3.0,3,,#S/,#S/3,4,add 3 at position 3,flow_matching,0.3,2.0,41,151
86,remove,0.0,#,,#S/3,S/3,3,remove # from position 0,flow_matching,0.3,2.0,41,151
87,add,2.0,r,,S/3,S/r3,4,add r at position 2,flow_matching,0.3,2.0,41,151
88,replace,0.0,C,S,S/r3,C/r3,4,replace S at position 0 with C,flow_matching,0.3,2.0,41,151
89,replace,3.0,2,3,C/r3,C/r2,4,replace 3 at position 3 with 2,flow_matching,0.3,2.0,41,151
90,remove,3.0,2,,C/r2,C/r,3,remove 2 from position 3,flow_matching,0.3,2.0,41,151
91,add,0.0,O,,C/r,OC/r,4,add O at position 0,flow_matching,0.3,2.0,41,151
92,replace,0.0,C,O,OC/r,CC/r,4,replace O at position 0 with C,flow_matching,0.3,2.0,41,151
93,replace,1.0,c,C,CC/r,Cc/r,4,replace C at position 1 with c,flow_matching,0.3,2.0,41,151
94,replace,2.0,1,/,Cc/r,Cc1r,4,replace / at position 2 with 1,flow_matching,0.3,2.0,41,151
95,replace,3.0,o,r,Cc1r,Cc1o,4,replace r at position 3 with o,flow_matching,0.3,2.0,41,151
96,remove,3.0,o,,Cc1o,Cc1,3,remove o from position 3,flow_matching,0.3,2.0,41,151
97,add,2.0,c,,Cc1,Ccc1,4,add c at position 2,flow_matching,0.3,2.0,41,151
98,replace,2.0,1,c,Ccc1,Cc11,4,replace c at position 2 with 1,flow_matching,0.3,2.0,41,151
99,replace,3.0,c,1,Cc11,Cc1c,4,replace 1 at position 3 with c,flow_matching,0.3,2.0,41,151
100,add,1.0,#,,Cc1c,C#c1c,5,add # at position 1,flow_matching,0.3,2.0,41,151
101,replace,1.0,c,#,C#c1c,Ccc1c,5,replace # at position 1 with c,flow_matching,0.3,2.0,41,151
102,remove,1.0,c,,Ccc1c,Cc1c,4,remove c from position 1,flow_matching,0.3,2.0,41,151
103,remove,2.0,1,,Cc1c,Ccc,3,remove 1 from position 2,flow_matching,0.3,2.0,41,151
104,replace,2.0,1,c,Ccc,Cc1,3,replace c at position 2 with 1,flow_matching,0.3,2.0,41,151
105,replace,0.0,/,C,Cc1,/c1,3,replace C at position 0 with /,flow_matching,0.3,2.0,41,151
106,add,3.0,4,,/c1,/c14,4,add 4 at position 3,flow_matching,0.3,2.0,41,151
107,remove,3.0,4,,/c14,/c1,3,remove 4 from position 3,flow_matching,0.3,2.0,41,151
108,replace,0.0,C,/,/c1,Cc1,3,replace / at position 0 with C,flow_matching,0.3,2.0,41,151
109,remove,2.0,1,,Cc1,Cc,2,remove 1 from position 2,flow_matching,0.3,2.0,41,151
110,replace,0.0,B,C,Cc,Bc,2,replace C at position 0 with B,flow_matching,0.3,2.0,41,151
111,replace,0.0,\,B,Bc,\c,2,replace B at position 0 with \,flow_matching,0.3,2.0,41,151
112,replace,0.0,C,\,\c,Cc,2,replace \ at position 0 with C,flow_matching,0.3,2.0,41,151
113,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,41,151
114,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,41,151
115,add,4.0,n,,Cc1c,Cc1cn,5,add n at position 4,flow_matching,0.3,2.0,41,151
116,add,5.0,c,,Cc1cn,Cc1cnc,6,add c at position 5,flow_matching,0.3,2.0,41,151
117,add,6.0,(,,Cc1cnc,Cc1cnc(,7,add ( at position 6,flow_matching,0.3,2.0,41,151
118,add,7.0,[,,Cc1cnc(,Cc1cnc([,8,add [ at position 7,flow_matching,0.3,2.0,41,151
119,add,8.0,C,,Cc1cnc([,Cc1cnc([C,9,add C at position 8,flow_matching,0.3,2.0,41,151
120,add,9.0,@,,Cc1cnc([C,Cc1cnc([C@,10,add @ at position 9,flow_matching,0.3,2.0,41,151
121,add,10.0,H,,Cc1cnc([C@,Cc1cnc([C@H,11,add H at position 10,flow_matching,0.3,2.0,41,151
122,add,11.0,],,Cc1cnc([C@H,Cc1cnc([C@H],12,add ] at position 11,flow_matching,0.3,2.0,41,151
123,add,12.0,(,,Cc1cnc([C@H],Cc1cnc([C@H](,13,add ( at position 12,flow_matching,0.3,2.0,41,151
124,add,13.0,C,,Cc1cnc([C@H](,Cc1cnc([C@H](C,14,add C at position 13,flow_matching,0.3,2.0,41,151
125,add,14.0,),,Cc1cnc([C@H](C,Cc1cnc([C@H](C),15,add ) at position 14,flow_matching,0.3,2.0,41,151
126,add,15.0,N,,Cc1cnc([C@H](C),Cc1cnc([C@H](C)N,16,add N at position 15,flow_matching,0.3,2.0,41,151
127,add,16.0,C,,Cc1cnc([C@H](C)N,Cc1cnc([C@H](C)NC,17,add C at position 16,flow_matching,0.3,2.0,41,151
128,add,17.0,(,,Cc1cnc([C@H](C)NC,Cc1cnc([C@H](C)NC(,18,add ( at position 17,flow_matching,0.3,2.0,41,151
129,add,18.0,=,,Cc1cnc([C@H](C)NC(,Cc1cnc([C@H](C)NC(=,19,add = at position 18,flow_matching,0.3,2.0,41,151
130,add,19.0,O,,Cc1cnc([C@H](C)NC(=,Cc1cnc([C@H](C)NC(=O,20,add O at position 19,flow_matching,0.3,2.0,41,151
131,add,20.0,),,Cc1cnc([C@H](C)NC(=O,Cc1cnc([C@H](C)NC(=O),21,add ) at position 20,flow_matching,0.3,2.0,41,151
132,add,21.0,N,,Cc1cnc([C@H](C)NC(=O),Cc1cnc([C@H](C)NC(=O)N,22,add N at position 21,flow_matching,0.3,2.0,41,151
133,add,22.0,N,,Cc1cnc([C@H](C)NC(=O)N,Cc1cnc([C@H](C)NC(=O)NN,23,add N at position 22,flow_matching,0.3,2.0,41,151
134,add,23.0,C,,Cc1cnc([C@H](C)NC(=O)NN,Cc1cnc([C@H](C)NC(=O)NNC,24,add C at position 23,flow_matching,0.3,2.0,41,151
135,add,24.0,(,,Cc1cnc([C@H](C)NC(=O)NNC,Cc1cnc([C@H](C)NC(=O)NNC(,25,add ( at position 24,flow_matching,0.3,2.0,41,151
136,add,25.0,=,,Cc1cnc([C@H](C)NC(=O)NNC(,Cc1cnc([C@H](C)NC(=O)NNC(=,26,add = at position 25,flow_matching,0.3,2.0,41,151
137,add,26.0,O,,Cc1cnc([C@H](C)NC(=O)NNC(=,Cc1cnc([C@H](C)NC(=O)NNC(=O,27,add O at position 26,flow_matching,0.3,2.0,41,151
138,add,27.0,),,Cc1cnc([C@H](C)NC(=O)NNC(=O,Cc1cnc([C@H](C)NC(=O)NNC(=O),28,add ) at position 27,flow_matching,0.3,2.0,41,151
139,add,28.0,N,,Cc1cnc([C@H](C)NC(=O)NNC(=O),Cc1cnc([C@H](C)NC(=O)NNC(=O)N,29,add N at position 28,flow_matching,0.3,2.0,41,151
140,add,29.0,c,,Cc1cnc([C@H](C)NC(=O)NNC(=O)N,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc,30,add c at position 29,flow_matching,0.3,2.0,41,151
141,add,30.0,2,,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2,31,add 2 at position 30,flow_matching,0.3,2.0,41,151
142,add,31.0,c,,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2c,32,add c at position 31,flow_matching,0.3,2.0,41,151
143,add,32.0,c,,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2c,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2cc,33,add c at position 32,flow_matching,0.3,2.0,41,151
144,add,33.0,c,,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2cc,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccc,34,add c at position 33,flow_matching,0.3,2.0,41,151
145,add,34.0,c,,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccc,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2cccc,35,add c at position 34,flow_matching,0.3,2.0,41,151
146,add,35.0,c,,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2cccc,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc,36,add c at position 35,flow_matching,0.3,2.0,41,151
147,add,36.0,2,,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc2,37,add 2 at position 36,flow_matching,0.3,2.0,41,151
148,add,37.0,),,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc2,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc2),38,add ) at position 37,flow_matching,0.3,2.0,41,151
149,add,38.0,s,,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc2),Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc2)s,39,add s at position 38,flow_matching,0.3,2.0,41,151
150,add,39.0,1,,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc2)s,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc2)s1,40,add 1 at position 39,flow_matching,0.3,2.0,41,151
151,add,40.0,"
",,Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc2)s1,"Cc1cnc([C@H](C)NC(=O)NNC(=O)Nc2ccccc2)s1
",41,"add 
 at position 40",flow_matching,0.3,2.0,41,151
