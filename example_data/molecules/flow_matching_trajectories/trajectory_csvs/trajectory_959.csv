step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,38,157
1,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,38,157
2,replace,0.0,s,+,+,s,1,replace + at position 0 with s,flow_matching,0.3,2.0,38,157
3,replace,0.0,c,s,s,c,1,replace s at position 0 with c,flow_matching,0.3,2.0,38,157
4,remove,0.0,c,,c,,0,remove c from position 0,flow_matching,0.3,2.0,38,157
5,add,0.0,[,,,[,1,add [ at position 0,flow_matching,0.3,2.0,38,157
6,add,1.0,3,,[,[3,2,add 3 at position 1,flow_matching,0.3,2.0,38,157
7,replace,0.0,c,[,[3,c3,2,replace [ at position 0 with c,flow_matching,0.3,2.0,38,157
8,add,1.0,(,,c3,c(3,3,add ( at position 1,flow_matching,0.3,2.0,38,157
9,remove,0.0,c,,c(3,(3,2,remove c from position 0,flow_matching,0.3,2.0,38,157
10,replace,0.0,c,(,(3,c3,2,replace ( at position 0 with c,flow_matching,0.3,2.0,38,157
11,replace,1.0,1,3,c3,c1,2,replace 3 at position 1 with 1,flow_matching,0.3,2.0,38,157
12,add,0.0,s,,c1,sc1,3,add s at position 0,flow_matching,0.3,2.0,38,157
13,replace,0.0,3,s,sc1,3c1,3,replace s at position 0 with 3,flow_matching,0.3,2.0,38,157
14,replace,0.0,c,3,3c1,cc1,3,replace 3 at position 0 with c,flow_matching,0.3,2.0,38,157
15,remove,2.0,1,,cc1,cc,2,remove 1 from position 2,flow_matching,0.3,2.0,38,157
16,replace,1.0,1,c,cc,c1,2,replace c at position 1 with 1,flow_matching,0.3,2.0,38,157
17,remove,1.0,1,,c1,c,1,remove 1 from position 1,flow_matching,0.3,2.0,38,157
18,replace,0.0,=,c,c,=,1,replace c at position 0 with =,flow_matching,0.3,2.0,38,157
19,remove,0.0,=,,=,,0,remove = from position 0,flow_matching,0.3,2.0,38,157
20,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,38,157
21,add,0.0,+,,7,+7,2,add + at position 0,flow_matching,0.3,2.0,38,157
22,replace,0.0,c,+,+7,c7,2,replace + at position 0 with c,flow_matching,0.3,2.0,38,157
23,replace,1.0,1,7,c7,c1,2,replace 7 at position 1 with 1,flow_matching,0.3,2.0,38,157
24,add,2.0,c,,c1,c1c,3,add c at position 2,flow_matching,0.3,2.0,38,157
25,replace,0.0,N,c,c1c,N1c,3,replace c at position 0 with N,flow_matching,0.3,2.0,38,157
26,add,2.0,@,,N1c,N1@c,4,add @ at position 2,flow_matching,0.3,2.0,38,157
27,remove,2.0,@,,N1@c,N1c,3,remove @ from position 2,flow_matching,0.3,2.0,38,157
28,replace,0.0,c,N,N1c,c1c,3,replace N at position 0 with c,flow_matching,0.3,2.0,38,157
29,remove,1.0,1,,c1c,cc,2,remove 1 from position 1,flow_matching,0.3,2.0,38,157
30,replace,0.0,\,c,cc,\c,2,replace c at position 0 with \,flow_matching,0.3,2.0,38,157
31,replace,0.0,c,\,\c,cc,2,replace \ at position 0 with c,flow_matching,0.3,2.0,38,157
32,remove,0.0,c,,cc,c,1,remove c from position 0,flow_matching,0.3,2.0,38,157
33,add,0.0,2,,c,2c,2,add 2 at position 0,flow_matching,0.3,2.0,38,157
34,replace,0.0,c,2,2c,cc,2,replace 2 at position 0 with c,flow_matching,0.3,2.0,38,157
35,replace,1.0,1,c,cc,c1,2,replace c at position 1 with 1,flow_matching,0.3,2.0,38,157
36,add,1.0,o,,c1,co1,3,add o at position 1,flow_matching,0.3,2.0,38,157
37,add,1.0,4,,co1,c4o1,4,add 4 at position 1,flow_matching,0.3,2.0,38,157
38,add,4.0,),,c4o1,c4o1),5,add ) at position 4,flow_matching,0.3,2.0,38,157
39,add,4.0,\,,c4o1),c4o1\),6,add \ at position 4,flow_matching,0.3,2.0,38,157
40,add,2.0,2,,c4o1\),c42o1\),7,add 2 at position 2,flow_matching,0.3,2.0,38,157
41,remove,4.0,1,,c42o1\),c42o\),6,remove 1 from position 4,flow_matching,0.3,2.0,38,157
42,add,2.0,=,,c42o\),c4=2o\),7,add = at position 2,flow_matching,0.3,2.0,38,157
43,remove,5.0,\,,c4=2o\),c4=2o),6,remove \ from position 5,flow_matching,0.3,2.0,38,157
44,remove,3.0,2,,c4=2o),c4=o),5,remove 2 from position 3,flow_matching,0.3,2.0,38,157
45,remove,3.0,o,,c4=o),c4=),4,remove o from position 3,flow_matching,0.3,2.0,38,157
46,replace,1.0,1,4,c4=),c1=),4,replace 4 at position 1 with 1,flow_matching,0.3,2.0,38,157
47,remove,0.0,c,,c1=),1=),3,remove c from position 0,flow_matching,0.3,2.0,38,157
48,add,3.0,1,,1=),1=)1,4,add 1 at position 3,flow_matching,0.3,2.0,38,157
49,replace,1.0,2,=,1=)1,12)1,4,replace = at position 1 with 2,flow_matching,0.3,2.0,38,157
50,replace,0.0,+,1,12)1,+2)1,4,replace 1 at position 0 with +,flow_matching,0.3,2.0,38,157
51,replace,2.0,F,),+2)1,+2F1,4,replace ) at position 2 with F,flow_matching,0.3,2.0,38,157
52,add,0.0,r,,+2F1,r+2F1,5,add r at position 0,flow_matching,0.3,2.0,38,157
53,add,5.0,-,,r+2F1,r+2F1-,6,add - at position 5,flow_matching,0.3,2.0,38,157
54,add,6.0,s,,r+2F1-,r+2F1-s,7,add s at position 6,flow_matching,0.3,2.0,38,157
55,add,7.0,=,,r+2F1-s,r+2F1-s=,8,add = at position 7,flow_matching,0.3,2.0,38,157
56,replace,0.0,c,r,r+2F1-s=,c+2F1-s=,8,replace r at position 0 with c,flow_matching,0.3,2.0,38,157
57,remove,3.0,F,,c+2F1-s=,c+21-s=,7,remove F from position 3,flow_matching,0.3,2.0,38,157
58,remove,5.0,s,,c+21-s=,c+21-=,6,remove s from position 5,flow_matching,0.3,2.0,38,157
59,replace,1.0,1,+,c+21-=,c121-=,6,replace + at position 1 with 1,flow_matching,0.3,2.0,38,157
60,add,4.0,r,,c121-=,c121r-=,7,add r at position 4,flow_matching,0.3,2.0,38,157
61,replace,2.0,c,2,c121r-=,c1c1r-=,7,replace 2 at position 2 with c,flow_matching,0.3,2.0,38,157
62,replace,3.0,c,1,c1c1r-=,c1ccr-=,7,replace 1 at position 3 with c,flow_matching,0.3,2.0,38,157
63,replace,4.0,c,r,c1ccr-=,c1ccc-=,7,replace r at position 4 with c,flow_matching,0.3,2.0,38,157
64,replace,3.0,],c,c1ccc-=,c1c]c-=,7,replace c at position 3 with ],flow_matching,0.3,2.0,38,157
65,replace,3.0,c,],c1c]c-=,c1ccc-=,7,replace ] at position 3 with c,flow_matching,0.3,2.0,38,157
66,replace,0.0,(,c,c1ccc-=,(1ccc-=,7,replace c at position 0 with (,flow_matching,0.3,2.0,38,157
67,add,2.0,S,,(1ccc-=,(1Sccc-=,8,add S at position 2,flow_matching,0.3,2.0,38,157
68,replace,1.0,O,1,(1Sccc-=,(OSccc-=,8,replace 1 at position 1 with O,flow_matching,0.3,2.0,38,157
69,replace,1.0,B,O,(OSccc-=,(BSccc-=,8,replace O at position 1 with B,flow_matching,0.3,2.0,38,157
70,add,8.0,2,,(BSccc-=,(BSccc-=2,9,add 2 at position 8,flow_matching,0.3,2.0,38,157
71,add,5.0,s,,(BSccc-=2,(BSccsc-=2,10,add s at position 5,flow_matching,0.3,2.0,38,157
72,remove,1.0,B,,(BSccsc-=2,(Sccsc-=2,9,remove B from position 1,flow_matching,0.3,2.0,38,157
73,replace,0.0,c,(,(Sccsc-=2,cSccsc-=2,9,replace ( at position 0 with c,flow_matching,0.3,2.0,38,157
74,remove,4.0,s,,cSccsc-=2,cSccc-=2,8,remove s from position 4,flow_matching,0.3,2.0,38,157
75,add,6.0,B,,cSccc-=2,cSccc-B=2,9,add B at position 6,flow_matching,0.3,2.0,38,157
76,replace,1.0,1,S,cSccc-B=2,c1ccc-B=2,9,replace S at position 1 with 1,flow_matching,0.3,2.0,38,157
77,replace,7.0,O,=,c1ccc-B=2,c1ccc-BO2,9,replace = at position 7 with O,flow_matching,0.3,2.0,38,157
78,remove,7.0,O,,c1ccc-BO2,c1ccc-B2,8,remove O from position 7,flow_matching,0.3,2.0,38,157
79,replace,5.0,(,-,c1ccc-B2,c1ccc(B2,8,replace - at position 5 with (,flow_matching,0.3,2.0,38,157
80,replace,5.0,5,(,c1ccc(B2,c1ccc5B2,8,replace ( at position 5 with 5,flow_matching,0.3,2.0,38,157
81,remove,3.0,c,,c1ccc5B2,c1cc5B2,7,remove c from position 3,flow_matching,0.3,2.0,38,157
82,replace,4.0,c,5,c1cc5B2,c1cccB2,7,replace 5 at position 4 with c,flow_matching,0.3,2.0,38,157
83,replace,5.0,(,B,c1cccB2,c1ccc(2,7,replace B at position 5 with (,flow_matching,0.3,2.0,38,157
84,remove,6.0,2,,c1ccc(2,c1ccc(,6,remove 2 from position 6,flow_matching,0.3,2.0,38,157
85,add,2.0,6,,c1ccc(,c16ccc(,7,add 6 at position 2,flow_matching,0.3,2.0,38,157
86,remove,4.0,c,,c16ccc(,c16cc(,6,remove c from position 4,flow_matching,0.3,2.0,38,157
87,replace,2.0,c,6,c16cc(,c1ccc(,6,replace 6 at position 2 with c,flow_matching,0.3,2.0,38,157
88,replace,0.0,+,c,c1ccc(,+1ccc(,6,replace c at position 0 with +,flow_matching,0.3,2.0,38,157
89,remove,5.0,(,,+1ccc(,+1ccc,5,remove ( from position 5,flow_matching,0.3,2.0,38,157
90,replace,0.0,c,+,+1ccc,c1ccc,5,replace + at position 0 with c,flow_matching,0.3,2.0,38,157
91,replace,2.0,\,c,c1ccc,c1\cc,5,replace c at position 2 with \,flow_matching,0.3,2.0,38,157
92,replace,2.0,c,\,c1\cc,c1ccc,5,replace \ at position 2 with c,flow_matching,0.3,2.0,38,157
93,replace,2.0,F,c,c1ccc,c1Fcc,5,replace c at position 2 with F,flow_matching,0.3,2.0,38,157
94,remove,4.0,c,,c1Fcc,c1Fc,4,remove c from position 4,flow_matching,0.3,2.0,38,157
95,remove,0.0,c,,c1Fc,1Fc,3,remove c from position 0,flow_matching,0.3,2.0,38,157
96,add,0.0,o,,1Fc,o1Fc,4,add o at position 0,flow_matching,0.3,2.0,38,157
97,replace,2.0,B,F,o1Fc,o1Bc,4,replace F at position 2 with B,flow_matching,0.3,2.0,38,157
98,replace,2.0,+,B,o1Bc,o1+c,4,replace B at position 2 with +,flow_matching,0.3,2.0,38,157
99,replace,0.0,c,o,o1+c,c1+c,4,replace o at position 0 with c,flow_matching,0.3,2.0,38,157
100,remove,0.0,c,,c1+c,1+c,3,remove c from position 0,flow_matching,0.3,2.0,38,157
101,replace,0.0,c,1,1+c,c+c,3,replace 1 at position 0 with c,flow_matching,0.3,2.0,38,157
102,add,0.0,r,,c+c,rc+c,4,add r at position 0,flow_matching,0.3,2.0,38,157
103,remove,1.0,c,,rc+c,r+c,3,remove c from position 1,flow_matching,0.3,2.0,38,157
104,replace,0.0,c,r,r+c,c+c,3,replace r at position 0 with c,flow_matching,0.3,2.0,38,157
105,replace,1.0,1,+,c+c,c1c,3,replace + at position 1 with 1,flow_matching,0.3,2.0,38,157
106,remove,0.0,c,,c1c,1c,2,remove c from position 0,flow_matching,0.3,2.0,38,157
107,add,1.0,-,,1c,1-c,3,add - at position 1,flow_matching,0.3,2.0,38,157
108,add,3.0,2,,1-c,1-c2,4,add 2 at position 3,flow_matching,0.3,2.0,38,157
109,replace,0.0,c,1,1-c2,c-c2,4,replace 1 at position 0 with c,flow_matching,0.3,2.0,38,157
110,add,3.0,O,,c-c2,c-cO2,5,add O at position 3,flow_matching,0.3,2.0,38,157
111,replace,2.0,(,c,c-cO2,c-(O2,5,replace c at position 2 with (,flow_matching,0.3,2.0,38,157
112,replace,0.0,=,c,c-(O2,=-(O2,5,replace c at position 0 with =,flow_matching,0.3,2.0,38,157
113,add,2.0,6,,=-(O2,=-6(O2,6,add 6 at position 2,flow_matching,0.3,2.0,38,157
114,remove,0.0,=,,=-6(O2,-6(O2,5,remove = from position 0,flow_matching,0.3,2.0,38,157
115,replace,0.0,c,-,-6(O2,c6(O2,5,replace - at position 0 with c,flow_matching,0.3,2.0,38,157
116,add,4.0,4,,c6(O2,c6(O42,6,add 4 at position 4,flow_matching,0.3,2.0,38,157
117,remove,0.0,c,,c6(O42,6(O42,5,remove c from position 0,flow_matching,0.3,2.0,38,157
118,remove,4.0,2,,6(O42,6(O4,4,remove 2 from position 4,flow_matching,0.3,2.0,38,157
119,add,2.0,N,,6(O4,6(NO4,5,add N at position 2,flow_matching,0.3,2.0,38,157
120,replace,0.0,c,6,6(NO4,c(NO4,5,replace 6 at position 0 with c,flow_matching,0.3,2.0,38,157
121,replace,1.0,1,(,c(NO4,c1NO4,5,replace ( at position 1 with 1,flow_matching,0.3,2.0,38,157
122,replace,2.0,c,N,c1NO4,c1cO4,5,replace N at position 2 with c,flow_matching,0.3,2.0,38,157
123,replace,3.0,c,O,c1cO4,c1cc4,5,replace O at position 3 with c,flow_matching,0.3,2.0,38,157
124,replace,4.0,c,4,c1cc4,c1ccc,5,replace 4 at position 4 with c,flow_matching,0.3,2.0,38,157
125,add,5.0,(,,c1ccc,c1ccc(,6,add ( at position 5,flow_matching,0.3,2.0,38,157
126,add,6.0,C,,c1ccc(,c1ccc(C,7,add C at position 6,flow_matching,0.3,2.0,38,157
127,add,7.0,n,,c1ccc(C,c1ccc(Cn,8,add n at position 7,flow_matching,0.3,2.0,38,157
128,add,8.0,2,,c1ccc(Cn,c1ccc(Cn2,9,add 2 at position 8,flow_matching,0.3,2.0,38,157
129,add,9.0,c,,c1ccc(Cn2,c1ccc(Cn2c,10,add c at position 9,flow_matching,0.3,2.0,38,157
130,add,10.0,(,,c1ccc(Cn2c,c1ccc(Cn2c(,11,add ( at position 10,flow_matching,0.3,2.0,38,157
131,add,11.0,S,,c1ccc(Cn2c(,c1ccc(Cn2c(S,12,add S at position 11,flow_matching,0.3,2.0,38,157
132,add,12.0,C,,c1ccc(Cn2c(S,c1ccc(Cn2c(SC,13,add C at position 12,flow_matching,0.3,2.0,38,157
133,add,13.0,c,,c1ccc(Cn2c(SC,c1ccc(Cn2c(SCc,14,add c at position 13,flow_matching,0.3,2.0,38,157
134,add,14.0,3,,c1ccc(Cn2c(SCc,c1ccc(Cn2c(SCc3,15,add 3 at position 14,flow_matching,0.3,2.0,38,157
135,add,15.0,n,,c1ccc(Cn2c(SCc3,c1ccc(Cn2c(SCc3n,16,add n at position 15,flow_matching,0.3,2.0,38,157
136,add,16.0,c,,c1ccc(Cn2c(SCc3n,c1ccc(Cn2c(SCc3nc,17,add c at position 16,flow_matching,0.3,2.0,38,157
137,add,17.0,o,,c1ccc(Cn2c(SCc3nc,c1ccc(Cn2c(SCc3nco,18,add o at position 17,flow_matching,0.3,2.0,38,157
138,add,18.0,n,,c1ccc(Cn2c(SCc3nco,c1ccc(Cn2c(SCc3ncon,19,add n at position 18,flow_matching,0.3,2.0,38,157
139,add,19.0,3,,c1ccc(Cn2c(SCc3ncon,c1ccc(Cn2c(SCc3ncon3,20,add 3 at position 19,flow_matching,0.3,2.0,38,157
140,add,20.0,),,c1ccc(Cn2c(SCc3ncon3,c1ccc(Cn2c(SCc3ncon3),21,add ) at position 20,flow_matching,0.3,2.0,38,157
141,add,21.0,n,,c1ccc(Cn2c(SCc3ncon3),c1ccc(Cn2c(SCc3ncon3)n,22,add n at position 21,flow_matching,0.3,2.0,38,157
142,add,22.0,n,,c1ccc(Cn2c(SCc3ncon3)n,c1ccc(Cn2c(SCc3ncon3)nn,23,add n at position 22,flow_matching,0.3,2.0,38,157
143,add,23.0,c,,c1ccc(Cn2c(SCc3ncon3)nn,c1ccc(Cn2c(SCc3ncon3)nnc,24,add c at position 23,flow_matching,0.3,2.0,38,157
144,add,24.0,2,,c1ccc(Cn2c(SCc3ncon3)nnc,c1ccc(Cn2c(SCc3ncon3)nnc2,25,add 2 at position 24,flow_matching,0.3,2.0,38,157
145,add,25.0,-,,c1ccc(Cn2c(SCc3ncon3)nnc2,c1ccc(Cn2c(SCc3ncon3)nnc2-,26,add - at position 25,flow_matching,0.3,2.0,38,157
146,add,26.0,c,,c1ccc(Cn2c(SCc3ncon3)nnc2-,c1ccc(Cn2c(SCc3ncon3)nnc2-c,27,add c at position 26,flow_matching,0.3,2.0,38,157
147,add,27.0,2,,c1ccc(Cn2c(SCc3ncon3)nnc2-c,c1ccc(Cn2c(SCc3ncon3)nnc2-c2,28,add 2 at position 27,flow_matching,0.3,2.0,38,157
148,add,28.0,c,,c1ccc(Cn2c(SCc3ncon3)nnc2-c2,c1ccc(Cn2c(SCc3ncon3)nnc2-c2c,29,add c at position 28,flow_matching,0.3,2.0,38,157
149,add,29.0,c,,c1ccc(Cn2c(SCc3ncon3)nnc2-c2c,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cc,30,add c at position 29,flow_matching,0.3,2.0,38,157
150,add,30.0,c,,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cc,c1ccc(Cn2c(SCc3ncon3)nnc2-c2ccc,31,add c at position 30,flow_matching,0.3,2.0,38,157
151,add,31.0,s,,c1ccc(Cn2c(SCc3ncon3)nnc2-c2ccc,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs,32,add s at position 31,flow_matching,0.3,2.0,38,157
152,add,32.0,2,,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2,33,add 2 at position 32,flow_matching,0.3,2.0,38,157
153,add,33.0,),,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2),34,add ) at position 33,flow_matching,0.3,2.0,38,157
154,add,34.0,c,,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2),c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2)c,35,add c at position 34,flow_matching,0.3,2.0,38,157
155,add,35.0,c,,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2)c,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2)cc,36,add c at position 35,flow_matching,0.3,2.0,38,157
156,add,36.0,1,,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2)cc,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2)cc1,37,add 1 at position 36,flow_matching,0.3,2.0,38,157
157,add,37.0,"
",,c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2)cc1,"c1ccc(Cn2c(SCc3ncon3)nnc2-c2cccs2)cc1
",38,"add 
 at position 37",flow_matching,0.3,2.0,38,157
