step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,33,76
1,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,33,76
2,add,1.0,I,,/,/I,2,add I at position 1,flow_matching,0.3,2.0,33,76
3,add,2.0,+,,/I,/I+,3,add + at position 2,flow_matching,0.3,2.0,33,76
4,remove,2.0,+,,/I+,/I,2,remove + from position 2,flow_matching,0.3,2.0,33,76
5,replace,0.0,N,/,/I,NI,2,replace / at position 0 with N,flow_matching,0.3,2.0,33,76
6,remove,1.0,<PERSON>,,NI,N,1,remove I from position 1,flow_matching,0.3,2.0,33,76
7,add,1.0,#,,N,N#,2,add # at position 1,flow_matching,0.3,2.0,33,76
8,add,2.0,C,,N#,N#C,3,add C at position 2,flow_matching,0.3,2.0,33,76
9,remove,2.0,C,,N#C,N#,2,remove C from position 2,flow_matching,0.3,2.0,33,76
10,add,2.0,C,,N#,N#C,3,add C at position 2,flow_matching,0.3,2.0,33,76
11,add,3.0,C,,N#C,N#CC,4,add C at position 3,flow_matching,0.3,2.0,33,76
12,add,3.0,=,,N#CC,N#C=C,5,add = at position 3,flow_matching,0.3,2.0,33,76
13,replace,3.0,C,=,N#C=C,N#CCC,5,replace = at position 3 with C,flow_matching,0.3,2.0,33,76
14,add,3.0,],,N#CCC,N#C]CC,6,add ] at position 3,flow_matching,0.3,2.0,33,76
15,replace,5.0,o,C,N#C]CC,N#C]Co,6,replace C at position 5 with o,flow_matching,0.3,2.0,33,76
16,add,3.0,/,,N#C]Co,N#C/]Co,7,add / at position 3,flow_matching,0.3,2.0,33,76
17,replace,3.0,C,/,N#C/]Co,N#CC]Co,7,replace / at position 3 with C,flow_matching,0.3,2.0,33,76
18,remove,4.0,],,N#CC]Co,N#CCCo,6,remove ] from position 4,flow_matching,0.3,2.0,33,76
19,replace,4.0,1,C,N#CCCo,N#CC1o,6,replace C at position 4 with 1,flow_matching,0.3,2.0,33,76
20,replace,5.0,(,o,N#CC1o,N#CC1(,6,replace o at position 5 with (,flow_matching,0.3,2.0,33,76
21,replace,2.0,(,C,N#CC1(,N#(C1(,6,replace C at position 2 with (,flow_matching,0.3,2.0,33,76
22,replace,0.0,2,N,N#(C1(,2#(C1(,6,replace N at position 0 with 2,flow_matching,0.3,2.0,33,76
23,replace,0.0,],2,2#(C1(,]#(C1(,6,replace 2 at position 0 with ],flow_matching,0.3,2.0,33,76
24,add,6.0,C,,]#(C1(,]#(C1(C,7,add C at position 6,flow_matching,0.3,2.0,33,76
25,remove,3.0,C,,]#(C1(C,]#(1(C,6,remove C from position 3,flow_matching,0.3,2.0,33,76
26,replace,0.0,N,],]#(1(C,N#(1(C,6,replace ] at position 0 with N,flow_matching,0.3,2.0,33,76
27,remove,3.0,1,,N#(1(C,N#((C,5,remove 1 from position 3,flow_matching,0.3,2.0,33,76
28,replace,3.0,B,(,N#((C,N#(BC,5,replace ( at position 3 with B,flow_matching,0.3,2.0,33,76
29,replace,2.0,[,(,N#(BC,N#[BC,5,replace ( at position 2 with [,flow_matching,0.3,2.0,33,76
30,remove,1.0,#,,N#[BC,N[BC,4,remove # from position 1,flow_matching,0.3,2.0,33,76
31,replace,1.0,#,[,N[BC,N#BC,4,replace [ at position 1 with #,flow_matching,0.3,2.0,33,76
32,replace,0.0,7,N,N#BC,7#BC,4,replace N at position 0 with 7,flow_matching,0.3,2.0,33,76
33,replace,0.0,N,7,7#BC,N#BC,4,replace 7 at position 0 with N,flow_matching,0.3,2.0,33,76
34,replace,2.0,C,B,N#BC,N#CC,4,replace B at position 2 with C,flow_matching,0.3,2.0,33,76
35,remove,0.0,N,,N#CC,#CC,3,remove N from position 0,flow_matching,0.3,2.0,33,76
36,replace,0.0,N,#,#CC,NCC,3,replace # at position 0 with N,flow_matching,0.3,2.0,33,76
37,remove,0.0,N,,NCC,CC,2,remove N from position 0,flow_matching,0.3,2.0,33,76
38,replace,1.0,n,C,CC,Cn,2,replace C at position 1 with n,flow_matching,0.3,2.0,33,76
39,replace,1.0,l,n,Cn,Cl,2,replace n at position 1 with l,flow_matching,0.3,2.0,33,76
40,add,1.0,o,,Cl,Col,3,add o at position 1,flow_matching,0.3,2.0,33,76
41,replace,0.0,N,C,Col,Nol,3,replace C at position 0 with N,flow_matching,0.3,2.0,33,76
42,remove,1.0,o,,Nol,Nl,2,remove o from position 1,flow_matching,0.3,2.0,33,76
43,replace,1.0,7,l,Nl,N7,2,replace l at position 1 with 7,flow_matching,0.3,2.0,33,76
44,add,2.0,3,,N7,N73,3,add 3 at position 2,flow_matching,0.3,2.0,33,76
45,replace,1.0,#,7,N73,N#3,3,replace 7 at position 1 with #,flow_matching,0.3,2.0,33,76
46,replace,2.0,C,3,N#3,N#C,3,replace 3 at position 2 with C,flow_matching,0.3,2.0,33,76
47,add,3.0,C,,N#C,N#CC,4,add C at position 3,flow_matching,0.3,2.0,33,76
48,add,4.0,1,,N#CC,N#CC1,5,add 1 at position 4,flow_matching,0.3,2.0,33,76
49,add,5.0,(,,N#CC1,N#CC1(,6,add ( at position 5,flow_matching,0.3,2.0,33,76
50,add,6.0,N,,N#CC1(,N#CC1(N,7,add N at position 6,flow_matching,0.3,2.0,33,76
51,add,7.0,C,,N#CC1(N,N#CC1(NC,8,add C at position 7,flow_matching,0.3,2.0,33,76
52,add,8.0,(,,N#CC1(NC,N#CC1(NC(,9,add ( at position 8,flow_matching,0.3,2.0,33,76
53,add,9.0,=,,N#CC1(NC(,N#CC1(NC(=,10,add = at position 9,flow_matching,0.3,2.0,33,76
54,add,10.0,O,,N#CC1(NC(=,N#CC1(NC(=O,11,add O at position 10,flow_matching,0.3,2.0,33,76
55,add,11.0,),,N#CC1(NC(=O,N#CC1(NC(=O),12,add ) at position 11,flow_matching,0.3,2.0,33,76
56,add,12.0,C,,N#CC1(NC(=O),N#CC1(NC(=O)C,13,add C at position 12,flow_matching,0.3,2.0,33,76
57,add,13.0,O,,N#CC1(NC(=O)C,N#CC1(NC(=O)CO,14,add O at position 13,flow_matching,0.3,2.0,33,76
58,add,14.0,c,,N#CC1(NC(=O)CO,N#CC1(NC(=O)COc,15,add c at position 14,flow_matching,0.3,2.0,33,76
59,add,15.0,2,,N#CC1(NC(=O)COc,N#CC1(NC(=O)COc2,16,add 2 at position 15,flow_matching,0.3,2.0,33,76
60,add,16.0,c,,N#CC1(NC(=O)COc2,N#CC1(NC(=O)COc2c,17,add c at position 16,flow_matching,0.3,2.0,33,76
61,add,17.0,c,,N#CC1(NC(=O)COc2c,N#CC1(NC(=O)COc2cc,18,add c at position 17,flow_matching,0.3,2.0,33,76
62,add,18.0,c,,N#CC1(NC(=O)COc2cc,N#CC1(NC(=O)COc2ccc,19,add c at position 18,flow_matching,0.3,2.0,33,76
63,add,19.0,c,,N#CC1(NC(=O)COc2ccc,N#CC1(NC(=O)COc2cccc,20,add c at position 19,flow_matching,0.3,2.0,33,76
64,add,20.0,(,,N#CC1(NC(=O)COc2cccc,N#CC1(NC(=O)COc2cccc(,21,add ( at position 20,flow_matching,0.3,2.0,33,76
65,add,21.0,C,,N#CC1(NC(=O)COc2cccc(,N#CC1(NC(=O)COc2cccc(C,22,add C at position 21,flow_matching,0.3,2.0,33,76
66,add,22.0,l,,N#CC1(NC(=O)COc2cccc(C,N#CC1(NC(=O)COc2cccc(Cl,23,add l at position 22,flow_matching,0.3,2.0,33,76
67,add,23.0,),,N#CC1(NC(=O)COc2cccc(Cl,N#CC1(NC(=O)COc2cccc(Cl),24,add ) at position 23,flow_matching,0.3,2.0,33,76
68,add,24.0,c,,N#CC1(NC(=O)COc2cccc(Cl),N#CC1(NC(=O)COc2cccc(Cl)c,25,add c at position 24,flow_matching,0.3,2.0,33,76
69,add,25.0,2,,N#CC1(NC(=O)COc2cccc(Cl)c,N#CC1(NC(=O)COc2cccc(Cl)c2,26,add 2 at position 25,flow_matching,0.3,2.0,33,76
70,add,26.0,),,N#CC1(NC(=O)COc2cccc(Cl)c2,N#CC1(NC(=O)COc2cccc(Cl)c2),27,add ) at position 26,flow_matching,0.3,2.0,33,76
71,add,27.0,C,,N#CC1(NC(=O)COc2cccc(Cl)c2),N#CC1(NC(=O)COc2cccc(Cl)c2)C,28,add C at position 27,flow_matching,0.3,2.0,33,76
72,add,28.0,C,,N#CC1(NC(=O)COc2cccc(Cl)c2)C,N#CC1(NC(=O)COc2cccc(Cl)c2)CC,29,add C at position 28,flow_matching,0.3,2.0,33,76
73,add,29.0,C,,N#CC1(NC(=O)COc2cccc(Cl)c2)CC,N#CC1(NC(=O)COc2cccc(Cl)c2)CCC,30,add C at position 29,flow_matching,0.3,2.0,33,76
74,add,30.0,C,,N#CC1(NC(=O)COc2cccc(Cl)c2)CCC,N#CC1(NC(=O)COc2cccc(Cl)c2)CCCC,31,add C at position 30,flow_matching,0.3,2.0,33,76
75,add,31.0,1,,N#CC1(NC(=O)COc2cccc(Cl)c2)CCCC,N#CC1(NC(=O)COc2cccc(Cl)c2)CCCC1,32,add 1 at position 31,flow_matching,0.3,2.0,33,76
76,add,32.0,"
",,N#CC1(NC(=O)COc2cccc(Cl)c2)CCCC1,"N#CC1(NC(=O)COc2cccc(Cl)c2)CCCC1
",33,"add 
 at position 32",flow_matching,0.3,2.0,33,76
