step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,244
1,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,49,244
2,add,0.0,s,,-,s-,2,add s at position 0,flow_matching,0.3,2.0,49,244
3,replace,0.0,O,s,s-,O-,2,replace s at position 0 with O,flow_matching,0.3,2.0,49,244
4,replace,1.0,=,-,O-,O=,2,replace - at position 1 with =,flow_matching,0.3,2.0,49,244
5,replace,0.0,I,O,O=,I=,2,replace O at position 0 with I,flow_matching,0.3,2.0,49,244
6,replace,0.0,],I,I=,]=,2,replace I at position 0 with ],flow_matching,0.3,2.0,49,244
7,replace,0.0,O,],]=,O=,2,replace ] at position 0 with O,flow_matching,0.3,2.0,49,244
8,add,2.0,C,,O=,O=C,3,add C at position 2,flow_matching,0.3,2.0,49,244
9,replace,0.0,+,O,O=C,+=C,3,replace O at position 0 with +,flow_matching,0.3,2.0,49,244
10,replace,0.0,F,+,+=C,F=C,3,replace + at position 0 with F,flow_matching,0.3,2.0,49,244
11,replace,1.0,/,=,F=C,F/C,3,replace = at position 1 with /,flow_matching,0.3,2.0,49,244
12,remove,2.0,C,,F/C,F/,2,remove C from position 2,flow_matching,0.3,2.0,49,244
13,replace,0.0,O,F,F/,O/,2,replace F at position 0 with O,flow_matching,0.3,2.0,49,244
14,remove,1.0,/,,O/,O,1,remove / from position 1,flow_matching,0.3,2.0,49,244
15,add,0.0,#,,O,#O,2,add # at position 0,flow_matching,0.3,2.0,49,244
16,add,0.0,o,,#O,o#O,3,add o at position 0,flow_matching,0.3,2.0,49,244
17,replace,0.0,O,o,o#O,O#O,3,replace o at position 0 with O,flow_matching,0.3,2.0,49,244
18,add,3.0,o,,O#O,O#Oo,4,add o at position 3,flow_matching,0.3,2.0,49,244
19,remove,3.0,o,,O#Oo,O#O,3,remove o from position 3,flow_matching,0.3,2.0,49,244
20,remove,1.0,#,,O#O,OO,2,remove # from position 1,flow_matching,0.3,2.0,49,244
21,remove,0.0,O,,OO,O,1,remove O from position 0,flow_matching,0.3,2.0,49,244
22,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,49,244
23,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,49,244
24,replace,0.0,/,=,=,/,1,replace = at position 0 with /,flow_matching,0.3,2.0,49,244
25,add,1.0,O,,/,/O,2,add O at position 1,flow_matching,0.3,2.0,49,244
26,remove,1.0,O,,/O,/,1,remove O from position 1,flow_matching,0.3,2.0,49,244
27,replace,0.0,5,/,/,5,1,replace / at position 0 with 5,flow_matching,0.3,2.0,49,244
28,add,1.0,r,,5,5r,2,add r at position 1,flow_matching,0.3,2.0,49,244
29,replace,0.0,O,5,5r,Or,2,replace 5 at position 0 with O,flow_matching,0.3,2.0,49,244
30,replace,1.0,=,r,Or,O=,2,replace r at position 1 with =,flow_matching,0.3,2.0,49,244
31,replace,0.0,B,O,O=,B=,2,replace O at position 0 with B,flow_matching,0.3,2.0,49,244
32,remove,0.0,B,,B=,=,1,remove B from position 0,flow_matching,0.3,2.0,49,244
33,replace,0.0,c,=,=,c,1,replace = at position 0 with c,flow_matching,0.3,2.0,49,244
34,replace,0.0,r,c,c,r,1,replace c at position 0 with r,flow_matching,0.3,2.0,49,244
35,replace,0.0,s,r,r,s,1,replace r at position 0 with s,flow_matching,0.3,2.0,49,244
36,add,0.0,],,s,]s,2,add ] at position 0,flow_matching,0.3,2.0,49,244
37,add,1.0,5,,]s,]5s,3,add 5 at position 1,flow_matching,0.3,2.0,49,244
38,add,2.0,],,]5s,]5]s,4,add ] at position 2,flow_matching,0.3,2.0,49,244
39,replace,1.0,7,5,]5]s,]7]s,4,replace 5 at position 1 with 7,flow_matching,0.3,2.0,49,244
40,add,1.0,),,]7]s,])7]s,5,add ) at position 1,flow_matching,0.3,2.0,49,244
41,add,5.0,N,,])7]s,])7]sN,6,add N at position 5,flow_matching,0.3,2.0,49,244
42,replace,0.0,O,],])7]sN,O)7]sN,6,replace ] at position 0 with O,flow_matching,0.3,2.0,49,244
43,remove,5.0,N,,O)7]sN,O)7]s,5,remove N from position 5,flow_matching,0.3,2.0,49,244
44,add,4.0,2,,O)7]s,O)7]2s,6,add 2 at position 4,flow_matching,0.3,2.0,49,244
45,replace,1.0,=,),O)7]2s,O=7]2s,6,replace ) at position 1 with =,flow_matching,0.3,2.0,49,244
46,add,2.0,\,,O=7]2s,O=\7]2s,7,add \ at position 2,flow_matching,0.3,2.0,49,244
47,replace,2.0,C,\,O=\7]2s,O=C7]2s,7,replace \ at position 2 with C,flow_matching,0.3,2.0,49,244
48,add,7.0,/,,O=C7]2s,O=C7]2s/,8,add / at position 7,flow_matching,0.3,2.0,49,244
49,add,5.0,I,,O=C7]2s/,O=C7]I2s/,9,add I at position 5,flow_matching,0.3,2.0,49,244
50,add,5.0,c,,O=C7]I2s/,O=C7]cI2s/,10,add c at position 5,flow_matching,0.3,2.0,49,244
51,replace,3.0,(,7,O=C7]cI2s/,O=C(]cI2s/,10,replace 7 at position 3 with (,flow_matching,0.3,2.0,49,244
52,remove,6.0,I,,O=C(]cI2s/,O=C(]c2s/,9,remove I from position 6,flow_matching,0.3,2.0,49,244
53,replace,4.0,C,],O=C(]c2s/,O=C(Cc2s/,9,replace ] at position 4 with C,flow_matching,0.3,2.0,49,244
54,replace,5.0,N,c,O=C(Cc2s/,O=C(CN2s/,9,replace c at position 5 with N,flow_matching,0.3,2.0,49,244
55,replace,6.0,c,2,O=C(CN2s/,O=C(CNcs/,9,replace 2 at position 6 with c,flow_matching,0.3,2.0,49,244
56,replace,7.0,1,s,O=C(CNcs/,O=C(CNc1/,9,replace s at position 7 with 1,flow_matching,0.3,2.0,49,244
57,replace,1.0,o,=,O=C(CNc1/,OoC(CNc1/,9,replace = at position 1 with o,flow_matching,0.3,2.0,49,244
58,replace,1.0,=,o,OoC(CNc1/,O=C(CNc1/,9,replace o at position 1 with =,flow_matching,0.3,2.0,49,244
59,add,8.0,I,,O=C(CNc1/,O=C(CNc1I/,10,add I at position 8,flow_matching,0.3,2.0,49,244
60,replace,8.0,c,I,O=C(CNc1I/,O=C(CNc1c/,10,replace I at position 8 with c,flow_matching,0.3,2.0,49,244
61,replace,6.0,O,c,O=C(CNc1c/,O=C(CNO1c/,10,replace c at position 6 with O,flow_matching,0.3,2.0,49,244
62,replace,6.0,c,O,O=C(CNO1c/,O=C(CNc1c/,10,replace O at position 6 with c,flow_matching,0.3,2.0,49,244
63,replace,5.0,5,N,O=C(CNc1c/,O=C(C5c1c/,10,replace N at position 5 with 5,flow_matching,0.3,2.0,49,244
64,remove,0.0,O,,O=C(C5c1c/,=C(C5c1c/,9,remove O from position 0,flow_matching,0.3,2.0,49,244
65,replace,0.0,O,=,=C(C5c1c/,OC(C5c1c/,9,replace = at position 0 with O,flow_matching,0.3,2.0,49,244
66,remove,1.0,C,,OC(C5c1c/,O(C5c1c/,8,remove C from position 1,flow_matching,0.3,2.0,49,244
67,replace,1.0,=,(,O(C5c1c/,O=C5c1c/,8,replace ( at position 1 with =,flow_matching,0.3,2.0,49,244
68,remove,7.0,/,,O=C5c1c/,O=C5c1c,7,remove / from position 7,flow_matching,0.3,2.0,49,244
69,remove,1.0,=,,O=C5c1c,OC5c1c,6,remove = from position 1,flow_matching,0.3,2.0,49,244
70,replace,0.0,+,O,OC5c1c,+C5c1c,6,replace O at position 0 with +,flow_matching,0.3,2.0,49,244
71,replace,0.0,O,+,+C5c1c,OC5c1c,6,replace + at position 0 with O,flow_matching,0.3,2.0,49,244
72,replace,0.0,7,O,OC5c1c,7C5c1c,6,replace O at position 0 with 7,flow_matching,0.3,2.0,49,244
73,replace,0.0,O,7,7C5c1c,OC5c1c,6,replace 7 at position 0 with O,flow_matching,0.3,2.0,49,244
74,add,2.0,F,,OC5c1c,OCF5c1c,7,add F at position 2,flow_matching,0.3,2.0,49,244
75,add,4.0,I,,OCF5c1c,OCF5Ic1c,8,add I at position 4,flow_matching,0.3,2.0,49,244
76,replace,0.0,\,O,OCF5Ic1c,\CF5Ic1c,8,replace O at position 0 with \,flow_matching,0.3,2.0,49,244
77,remove,1.0,C,,\CF5Ic1c,\F5Ic1c,7,remove C from position 1,flow_matching,0.3,2.0,49,244
78,replace,3.0,@,I,\F5Ic1c,\F5@c1c,7,replace I at position 3 with @,flow_matching,0.3,2.0,49,244
79,replace,4.0,-,c,\F5@c1c,\F5@-1c,7,replace c at position 4 with -,flow_matching,0.3,2.0,49,244
80,add,7.0,F,,\F5@-1c,\F5@-1cF,8,add F at position 7,flow_matching,0.3,2.0,49,244
81,add,7.0,H,,\F5@-1cF,\F5@-1cHF,9,add H at position 7,flow_matching,0.3,2.0,49,244
82,replace,0.0,O,\,\F5@-1cHF,OF5@-1cHF,9,replace \ at position 0 with O,flow_matching,0.3,2.0,49,244
83,remove,0.0,O,,OF5@-1cHF,F5@-1cHF,8,remove O from position 0,flow_matching,0.3,2.0,49,244
84,replace,0.0,O,F,F5@-1cHF,O5@-1cHF,8,replace F at position 0 with O,flow_matching,0.3,2.0,49,244
85,replace,1.0,=,5,O5@-1cHF,O=@-1cHF,8,replace 5 at position 1 with =,flow_matching,0.3,2.0,49,244
86,replace,2.0,C,@,O=@-1cHF,O=C-1cHF,8,replace @ at position 2 with C,flow_matching,0.3,2.0,49,244
87,replace,3.0,(,-,O=C-1cHF,O=C(1cHF,8,replace - at position 3 with (,flow_matching,0.3,2.0,49,244
88,add,6.0,H,,O=C(1cHF,O=C(1cHHF,9,add H at position 6,flow_matching,0.3,2.0,49,244
89,remove,0.0,O,,O=C(1cHHF,=C(1cHHF,8,remove O from position 0,flow_matching,0.3,2.0,49,244
90,replace,4.0,S,c,=C(1cHHF,=C(1SHHF,8,replace c at position 4 with S,flow_matching,0.3,2.0,49,244
91,replace,0.0,7,=,=C(1SHHF,7C(1SHHF,8,replace = at position 0 with 7,flow_matching,0.3,2.0,49,244
92,replace,1.0,],C,7C(1SHHF,7](1SHHF,8,replace C at position 1 with ],flow_matching,0.3,2.0,49,244
93,remove,7.0,F,,7](1SHHF,7](1SHH,7,remove F from position 7,flow_matching,0.3,2.0,49,244
94,replace,0.0,O,7,7](1SHH,O](1SHH,7,replace 7 at position 0 with O,flow_matching,0.3,2.0,49,244
95,remove,4.0,S,,O](1SHH,O](1HH,6,remove S from position 4,flow_matching,0.3,2.0,49,244
96,add,5.0,/,,O](1HH,O](1H/H,7,add / at position 5,flow_matching,0.3,2.0,49,244
97,replace,3.0,@,1,O](1H/H,O](@H/H,7,replace 1 at position 3 with @,flow_matching,0.3,2.0,49,244
98,replace,1.0,=,],O](@H/H,O=(@H/H,7,replace ] at position 1 with =,flow_matching,0.3,2.0,49,244
99,add,1.0,6,,O=(@H/H,O6=(@H/H,8,add 6 at position 1,flow_matching,0.3,2.0,49,244
100,remove,2.0,=,,O6=(@H/H,O6(@H/H,7,remove = from position 2,flow_matching,0.3,2.0,49,244
101,replace,1.0,=,6,O6(@H/H,O=(@H/H,7,replace 6 at position 1 with =,flow_matching,0.3,2.0,49,244
102,add,6.0,S,,O=(@H/H,O=(@H/SH,8,add S at position 6,flow_matching,0.3,2.0,49,244
103,replace,2.0,C,(,O=(@H/SH,O=C@H/SH,8,replace ( at position 2 with C,flow_matching,0.3,2.0,49,244
104,replace,3.0,(,@,O=C@H/SH,O=C(H/SH,8,replace @ at position 3 with (,flow_matching,0.3,2.0,49,244
105,replace,1.0,[,=,O=C(H/SH,O[C(H/SH,8,replace = at position 1 with [,flow_matching,0.3,2.0,49,244
106,add,5.0,-,,O[C(H/SH,O[C(H-/SH,9,add - at position 5,flow_matching,0.3,2.0,49,244
107,replace,7.0,O,S,O[C(H-/SH,O[C(H-/OH,9,replace S at position 7 with O,flow_matching,0.3,2.0,49,244
108,remove,2.0,C,,O[C(H-/OH,O[(H-/OH,8,remove C from position 2,flow_matching,0.3,2.0,49,244
109,replace,1.0,=,[,O[(H-/OH,O=(H-/OH,8,replace [ at position 1 with =,flow_matching,0.3,2.0,49,244
110,remove,7.0,H,,O=(H-/OH,O=(H-/O,7,remove H from position 7,flow_matching,0.3,2.0,49,244
111,add,0.0,O,,O=(H-/O,OO=(H-/O,8,add O at position 0,flow_matching,0.3,2.0,49,244
112,add,0.0,n,,OO=(H-/O,nOO=(H-/O,9,add n at position 0,flow_matching,0.3,2.0,49,244
113,replace,0.0,O,n,nOO=(H-/O,OOO=(H-/O,9,replace n at position 0 with O,flow_matching,0.3,2.0,49,244
114,remove,3.0,=,,OOO=(H-/O,OOO(H-/O,8,remove = from position 3,flow_matching,0.3,2.0,49,244
115,remove,3.0,(,,OOO(H-/O,OOOH-/O,7,remove ( from position 3,flow_matching,0.3,2.0,49,244
116,replace,1.0,=,O,OOOH-/O,O=OH-/O,7,replace O at position 1 with =,flow_matching,0.3,2.0,49,244
117,add,0.0,C,,O=OH-/O,CO=OH-/O,8,add C at position 0,flow_matching,0.3,2.0,49,244
118,replace,2.0,4,=,CO=OH-/O,CO4OH-/O,8,replace = at position 2 with 4,flow_matching,0.3,2.0,49,244
119,replace,0.0,O,C,CO4OH-/O,OO4OH-/O,8,replace C at position 0 with O,flow_matching,0.3,2.0,49,244
120,replace,3.0,+,O,OO4OH-/O,OO4+H-/O,8,replace O at position 3 with +,flow_matching,0.3,2.0,49,244
121,replace,3.0,),+,OO4+H-/O,OO4)H-/O,8,replace + at position 3 with ),flow_matching,0.3,2.0,49,244
122,add,5.0,2,,OO4)H-/O,OO4)H2-/O,9,add 2 at position 5,flow_matching,0.3,2.0,49,244
123,remove,8.0,O,,OO4)H2-/O,OO4)H2-/,8,remove O from position 8,flow_matching,0.3,2.0,49,244
124,replace,1.0,=,O,OO4)H2-/,O=4)H2-/,8,replace O at position 1 with =,flow_matching,0.3,2.0,49,244
125,add,8.0,B,,O=4)H2-/,O=4)H2-/B,9,add B at position 8,flow_matching,0.3,2.0,49,244
126,replace,2.0,C,4,O=4)H2-/B,O=C)H2-/B,9,replace 4 at position 2 with C,flow_matching,0.3,2.0,49,244
127,replace,3.0,(,),O=C)H2-/B,O=C(H2-/B,9,replace ) at position 3 with (,flow_matching,0.3,2.0,49,244
128,replace,7.0,\,/,O=C(H2-/B,O=C(H2-\B,9,replace / at position 7 with \,flow_matching,0.3,2.0,49,244
129,replace,4.0,C,H,O=C(H2-\B,O=C(C2-\B,9,replace H at position 4 with C,flow_matching,0.3,2.0,49,244
130,add,4.0,/,,O=C(C2-\B,O=C(/C2-\B,10,add / at position 4,flow_matching,0.3,2.0,49,244
131,replace,1.0,l,=,O=C(/C2-\B,OlC(/C2-\B,10,replace = at position 1 with l,flow_matching,0.3,2.0,49,244
132,replace,2.0,+,C,OlC(/C2-\B,Ol+(/C2-\B,10,replace C at position 2 with +,flow_matching,0.3,2.0,49,244
133,replace,5.0,+,C,Ol+(/C2-\B,Ol+(/+2-\B,10,replace C at position 5 with +,flow_matching,0.3,2.0,49,244
134,replace,1.0,=,l,Ol+(/+2-\B,O=+(/+2-\B,10,replace l at position 1 with =,flow_matching,0.3,2.0,49,244
135,replace,8.0,#,\,O=+(/+2-\B,O=+(/+2-#B,10,replace \ at position 8 with #,flow_matching,0.3,2.0,49,244
136,add,4.0,5,,O=+(/+2-#B,O=+(5/+2-#B,11,add 5 at position 4,flow_matching,0.3,2.0,49,244
137,replace,2.0,I,+,O=+(5/+2-#B,O=I(5/+2-#B,11,replace + at position 2 with I,flow_matching,0.3,2.0,49,244
138,remove,6.0,+,,O=I(5/+2-#B,O=I(5/2-#B,10,remove + from position 6,flow_matching,0.3,2.0,49,244
139,add,4.0,=,,O=I(5/2-#B,O=I(=5/2-#B,11,add = at position 4,flow_matching,0.3,2.0,49,244
140,replace,2.0,C,I,O=I(=5/2-#B,O=C(=5/2-#B,11,replace I at position 2 with C,flow_matching,0.3,2.0,49,244
141,remove,6.0,/,,O=C(=5/2-#B,O=C(=52-#B,10,remove / from position 6,flow_matching,0.3,2.0,49,244
142,add,9.0,n,,O=C(=52-#B,O=C(=52-#nB,11,add n at position 9,flow_matching,0.3,2.0,49,244
143,add,1.0,+,,O=C(=52-#nB,O+=C(=52-#nB,12,add + at position 1,flow_matching,0.3,2.0,49,244
144,add,7.0,B,,O+=C(=52-#nB,O+=C(=5B2-#nB,13,add B at position 7,flow_matching,0.3,2.0,49,244
145,replace,7.0,-,B,O+=C(=5B2-#nB,O+=C(=5-2-#nB,13,replace B at position 7 with -,flow_matching,0.3,2.0,49,244
146,add,1.0,=,,O+=C(=5-2-#nB,O=+=C(=5-2-#nB,14,add = at position 1,flow_matching,0.3,2.0,49,244
147,remove,10.0,-,,O=+=C(=5-2-#nB,O=+=C(=5-2#nB,13,remove - from position 10,flow_matching,0.3,2.0,49,244
148,replace,2.0,C,+,O=+=C(=5-2#nB,O=C=C(=5-2#nB,13,replace + at position 2 with C,flow_matching,0.3,2.0,49,244
149,remove,6.0,=,,O=C=C(=5-2#nB,O=C=C(5-2#nB,12,remove = from position 6,flow_matching,0.3,2.0,49,244
150,replace,6.0,N,5,O=C=C(5-2#nB,O=C=C(N-2#nB,12,replace 5 at position 6 with N,flow_matching,0.3,2.0,49,244
151,replace,1.0,4,=,O=C=C(N-2#nB,O4C=C(N-2#nB,12,replace = at position 1 with 4,flow_matching,0.3,2.0,49,244
152,remove,9.0,#,,O4C=C(N-2#nB,O4C=C(N-2nB,11,remove # from position 9,flow_matching,0.3,2.0,49,244
153,remove,4.0,C,,O4C=C(N-2nB,O4C=(N-2nB,10,remove C from position 4,flow_matching,0.3,2.0,49,244
154,replace,1.0,O,4,O4C=(N-2nB,OOC=(N-2nB,10,replace 4 at position 1 with O,flow_matching,0.3,2.0,49,244
155,replace,1.0,=,O,OOC=(N-2nB,O=C=(N-2nB,10,replace O at position 1 with =,flow_matching,0.3,2.0,49,244
156,replace,3.0,(,=,O=C=(N-2nB,O=C((N-2nB,10,replace = at position 3 with (,flow_matching,0.3,2.0,49,244
157,add,9.0,@,,O=C((N-2nB,O=C((N-2n@B,11,add @ at position 9,flow_matching,0.3,2.0,49,244
158,add,11.0,N,,O=C((N-2n@B,O=C((N-2n@BN,12,add N at position 11,flow_matching,0.3,2.0,49,244
159,add,5.0,S,,O=C((N-2n@BN,O=C((SN-2n@BN,13,add S at position 5,flow_matching,0.3,2.0,49,244
160,replace,4.0,C,(,O=C((SN-2n@BN,O=C(CSN-2n@BN,13,replace ( at position 4 with C,flow_matching,0.3,2.0,49,244
161,replace,5.0,N,S,O=C(CSN-2n@BN,O=C(CNN-2n@BN,13,replace S at position 5 with N,flow_matching,0.3,2.0,49,244
162,replace,2.0,+,C,O=C(CNN-2n@BN,O=+(CNN-2n@BN,13,replace C at position 2 with +,flow_matching,0.3,2.0,49,244
163,add,0.0,=,,O=+(CNN-2n@BN,=O=+(CNN-2n@BN,14,add = at position 0,flow_matching,0.3,2.0,49,244
164,replace,0.0,O,=,=O=+(CNN-2n@BN,OO=+(CNN-2n@BN,14,replace = at position 0 with O,flow_matching,0.3,2.0,49,244
165,replace,1.0,=,O,OO=+(CNN-2n@BN,O==+(CNN-2n@BN,14,replace O at position 1 with =,flow_matching,0.3,2.0,49,244
166,remove,11.0,@,,O==+(CNN-2n@BN,O==+(CNN-2nBN,13,remove @ from position 11,flow_matching,0.3,2.0,49,244
167,replace,2.0,C,=,O==+(CNN-2nBN,O=C+(CNN-2nBN,13,replace = at position 2 with C,flow_matching,0.3,2.0,49,244
168,replace,9.0,#,2,O=C+(CNN-2nBN,O=C+(CNN-#nBN,13,replace 2 at position 9 with #,flow_matching,0.3,2.0,49,244
169,remove,0.0,O,,O=C+(CNN-#nBN,=C+(CNN-#nBN,12,remove O from position 0,flow_matching,0.3,2.0,49,244
170,remove,6.0,N,,=C+(CNN-#nBN,=C+(CN-#nBN,11,remove N from position 6,flow_matching,0.3,2.0,49,244
171,add,5.0,5,,=C+(CN-#nBN,=C+(C5N-#nBN,12,add 5 at position 5,flow_matching,0.3,2.0,49,244
172,add,7.0,\,,=C+(C5N-#nBN,=C+(C5N\-#nBN,13,add \ at position 7,flow_matching,0.3,2.0,49,244
173,add,5.0,I,,=C+(C5N\-#nBN,=C+(CI5N\-#nBN,14,add I at position 5,flow_matching,0.3,2.0,49,244
174,remove,12.0,B,,=C+(CI5N\-#nBN,=C+(CI5N\-#nN,13,remove B from position 12,flow_matching,0.3,2.0,49,244
175,replace,0.0,O,=,=C+(CI5N\-#nN,OC+(CI5N\-#nN,13,replace = at position 0 with O,flow_matching,0.3,2.0,49,244
176,replace,0.0,5,O,OC+(CI5N\-#nN,5C+(CI5N\-#nN,13,replace O at position 0 with 5,flow_matching,0.3,2.0,49,244
177,replace,0.0,O,5,5C+(CI5N\-#nN,OC+(CI5N\-#nN,13,replace 5 at position 0 with O,flow_matching,0.3,2.0,49,244
178,replace,1.0,=,C,OC+(CI5N\-#nN,O=+(CI5N\-#nN,13,replace C at position 1 with =,flow_matching,0.3,2.0,49,244
179,replace,2.0,C,+,O=+(CI5N\-#nN,O=C(CI5N\-#nN,13,replace + at position 2 with C,flow_matching,0.3,2.0,49,244
180,remove,0.0,O,,O=C(CI5N\-#nN,=C(CI5N\-#nN,12,remove O from position 0,flow_matching,0.3,2.0,49,244
181,remove,3.0,C,,=C(CI5N\-#nN,=C(I5N\-#nN,11,remove C from position 3,flow_matching,0.3,2.0,49,244
182,remove,7.0,-,,=C(I5N\-#nN,=C(I5N\#nN,10,remove - from position 7,flow_matching,0.3,2.0,49,244
183,replace,2.0,@,(,=C(I5N\#nN,=C@I5N\#nN,10,replace ( at position 2 with @,flow_matching,0.3,2.0,49,244
184,remove,5.0,N,,=C@I5N\#nN,=C@I5\#nN,9,remove N from position 5,flow_matching,0.3,2.0,49,244
185,add,8.0,),,=C@I5\#nN,=C@I5\#n)N,10,add ) at position 8,flow_matching,0.3,2.0,49,244
186,replace,0.0,O,=,=C@I5\#n)N,OC@I5\#n)N,10,replace = at position 0 with O,flow_matching,0.3,2.0,49,244
187,add,4.0,],,OC@I5\#n)N,OC@I]5\#n)N,11,add ] at position 4,flow_matching,0.3,2.0,49,244
188,remove,0.0,O,,OC@I]5\#n)N,C@I]5\#n)N,10,remove O from position 0,flow_matching,0.3,2.0,49,244
189,remove,1.0,@,,C@I]5\#n)N,CI]5\#n)N,9,remove @ from position 1,flow_matching,0.3,2.0,49,244
190,replace,2.0,),],CI]5\#n)N,CI)5\#n)N,9,replace ] at position 2 with ),flow_matching,0.3,2.0,49,244
191,add,0.0,5,,CI)5\#n)N,5CI)5\#n)N,10,add 5 at position 0,flow_matching,0.3,2.0,49,244
192,add,9.0,l,,5CI)5\#n)N,5CI)5\#n)lN,11,add l at position 9,flow_matching,0.3,2.0,49,244
193,replace,6.0,F,#,5CI)5\#n)lN,5CI)5\Fn)lN,11,replace # at position 6 with F,flow_matching,0.3,2.0,49,244
194,remove,7.0,n,,5CI)5\Fn)lN,5CI)5\F)lN,10,remove n from position 7,flow_matching,0.3,2.0,49,244
195,remove,2.0,I,,5CI)5\F)lN,5C)5\F)lN,9,remove I from position 2,flow_matching,0.3,2.0,49,244
196,replace,0.0,O,5,5C)5\F)lN,OC)5\F)lN,9,replace 5 at position 0 with O,flow_matching,0.3,2.0,49,244
197,replace,1.0,=,C,OC)5\F)lN,O=)5\F)lN,9,replace C at position 1 with =,flow_matching,0.3,2.0,49,244
198,replace,2.0,C,),O=)5\F)lN,O=C5\F)lN,9,replace ) at position 2 with C,flow_matching,0.3,2.0,49,244
199,replace,3.0,(,5,O=C5\F)lN,O=C(\F)lN,9,replace 5 at position 3 with (,flow_matching,0.3,2.0,49,244
200,replace,4.0,C,\,O=C(\F)lN,O=C(CF)lN,9,replace \ at position 4 with C,flow_matching,0.3,2.0,49,244
201,replace,5.0,N,F,O=C(CF)lN,O=C(CN)lN,9,replace F at position 5 with N,flow_matching,0.3,2.0,49,244
202,replace,6.0,c,),O=C(CN)lN,O=C(CNclN,9,replace ) at position 6 with c,flow_matching,0.3,2.0,49,244
203,replace,7.0,1,l,O=C(CNclN,O=C(CNc1N,9,replace l at position 7 with 1,flow_matching,0.3,2.0,49,244
204,replace,8.0,c,N,O=C(CNc1N,O=C(CNc1c,9,replace N at position 8 with c,flow_matching,0.3,2.0,49,244
205,add,9.0,c,,O=C(CNc1c,O=C(CNc1cc,10,add c at position 9,flow_matching,0.3,2.0,49,244
206,add,10.0,c,,O=C(CNc1cc,O=C(CNc1ccc,11,add c at position 10,flow_matching,0.3,2.0,49,244
207,add,11.0,(,,O=C(CNc1ccc,O=C(CNc1ccc(,12,add ( at position 11,flow_matching,0.3,2.0,49,244
208,add,12.0,C,,O=C(CNc1ccc(,O=C(CNc1ccc(C,13,add C at position 12,flow_matching,0.3,2.0,49,244
209,add,13.0,l,,O=C(CNc1ccc(C,O=C(CNc1ccc(Cl,14,add l at position 13,flow_matching,0.3,2.0,49,244
210,add,14.0,),,O=C(CNc1ccc(Cl,O=C(CNc1ccc(Cl),15,add ) at position 14,flow_matching,0.3,2.0,49,244
211,add,15.0,c,,O=C(CNc1ccc(Cl),O=C(CNc1ccc(Cl)c,16,add c at position 15,flow_matching,0.3,2.0,49,244
212,add,16.0,c,,O=C(CNc1ccc(Cl)c,O=C(CNc1ccc(Cl)cc,17,add c at position 16,flow_matching,0.3,2.0,49,244
213,add,17.0,1,,O=C(CNc1ccc(Cl)cc,O=C(CNc1ccc(Cl)cc1,18,add 1 at position 17,flow_matching,0.3,2.0,49,244
214,add,18.0,N,,O=C(CNc1ccc(Cl)cc1,O=C(CNc1ccc(Cl)cc1N,19,add N at position 18,flow_matching,0.3,2.0,49,244
215,add,19.0,C,,O=C(CNc1ccc(Cl)cc1N,O=C(CNc1ccc(Cl)cc1NC,20,add C at position 19,flow_matching,0.3,2.0,49,244
216,add,20.0,(,,O=C(CNc1ccc(Cl)cc1NC,O=C(CNc1ccc(Cl)cc1NC(,21,add ( at position 20,flow_matching,0.3,2.0,49,244
217,add,21.0,=,,O=C(CNc1ccc(Cl)cc1NC(,O=C(CNc1ccc(Cl)cc1NC(=,22,add = at position 21,flow_matching,0.3,2.0,49,244
218,add,22.0,O,,O=C(CNc1ccc(Cl)cc1NC(=,O=C(CNc1ccc(Cl)cc1NC(=O,23,add O at position 22,flow_matching,0.3,2.0,49,244
219,add,23.0,),,O=C(CNc1ccc(Cl)cc1NC(=O,O=C(CNc1ccc(Cl)cc1NC(=O),24,add ) at position 23,flow_matching,0.3,2.0,49,244
220,add,24.0,c,,O=C(CNc1ccc(Cl)cc1NC(=O),O=C(CNc1ccc(Cl)cc1NC(=O)c,25,add c at position 24,flow_matching,0.3,2.0,49,244
221,add,25.0,1,,O=C(CNc1ccc(Cl)cc1NC(=O)c,O=C(CNc1ccc(Cl)cc1NC(=O)c1,26,add 1 at position 25,flow_matching,0.3,2.0,49,244
222,add,26.0,c,,O=C(CNc1ccc(Cl)cc1NC(=O)c1,O=C(CNc1ccc(Cl)cc1NC(=O)c1c,27,add c at position 26,flow_matching,0.3,2.0,49,244
223,add,27.0,c,,O=C(CNc1ccc(Cl)cc1NC(=O)c1c,O=C(CNc1ccc(Cl)cc1NC(=O)c1cc,28,add c at position 27,flow_matching,0.3,2.0,49,244
224,add,28.0,c,,O=C(CNc1ccc(Cl)cc1NC(=O)c1cc,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccc,29,add c at position 28,flow_matching,0.3,2.0,49,244
225,add,29.0,o,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccc,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco,30,add o at position 29,flow_matching,0.3,2.0,49,244
226,add,30.0,1,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1,31,add 1 at position 30,flow_matching,0.3,2.0,49,244
227,add,31.0,),,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1),32,add ) at position 31,flow_matching,0.3,2.0,49,244
228,add,32.0,N,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1),O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)N,33,add N at position 32,flow_matching,0.3,2.0,49,244
229,add,33.0,c,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)N,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc,34,add c at position 33,flow_matching,0.3,2.0,49,244
230,add,34.0,1,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1,35,add 1 at position 34,flow_matching,0.3,2.0,49,244
231,add,35.0,c,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1c,36,add c at position 35,flow_matching,0.3,2.0,49,244
232,add,36.0,c,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1c,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1cc,37,add c at position 36,flow_matching,0.3,2.0,49,244
233,add,37.0,c,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1cc,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc,38,add c at position 37,flow_matching,0.3,2.0,49,244
234,add,38.0,(,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(,39,add ( at position 38,flow_matching,0.3,2.0,49,244
235,add,39.0,F,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F,40,add F at position 39,flow_matching,0.3,2.0,49,244
236,add,40.0,),,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F),41,add ) at position 40,flow_matching,0.3,2.0,49,244
237,add,41.0,c,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F),O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c,42,add c at position 41,flow_matching,0.3,2.0,49,244
238,add,42.0,(,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(,43,add ( at position 42,flow_matching,0.3,2.0,49,244
239,add,43.0,C,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(C,44,add C at position 43,flow_matching,0.3,2.0,49,244
240,add,44.0,l,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(C,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(Cl,45,add l at position 44,flow_matching,0.3,2.0,49,244
241,add,45.0,),,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(Cl,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(Cl),46,add ) at position 45,flow_matching,0.3,2.0,49,244
242,add,46.0,c,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(Cl),O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(Cl)c,47,add c at position 46,flow_matching,0.3,2.0,49,244
243,add,47.0,1,,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(Cl)c,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(Cl)c1,48,add 1 at position 47,flow_matching,0.3,2.0,49,244
244,add,48.0,"
",,O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(Cl)c1,"O=C(CNc1ccc(Cl)cc1NC(=O)c1ccco1)Nc1ccc(F)c(Cl)c1
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,244
