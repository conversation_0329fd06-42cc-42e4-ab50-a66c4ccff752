step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,50,165
1,add,0.0,[,,,[,1,add [ at position 0,flow_matching,0.3,2.0,50,165
2,replace,0.0,F,[,[,F,1,replace [ at position 0 with F,flow_matching,0.3,2.0,50,165
3,add,1.0,c,,F,Fc,2,add c at position 1,flow_matching,0.3,2.0,50,165
4,add,2.0,1,,Fc,Fc1,3,add 1 at position 2,flow_matching,0.3,2.0,50,165
5,replace,0.0,-,F,Fc1,-c1,3,replace F at position 0 with -,flow_matching,0.3,2.0,50,165
6,replace,2.0,@,1,-c1,-c@,3,replace 1 at position 2 with @,flow_matching,0.3,2.0,50,165
7,replace,0.0,F,-,-c@,Fc@,3,replace - at position 0 with F,flow_matching,0.3,2.0,50,165
8,replace,1.0,],c,Fc@,F]@,3,replace c at position 1 with ],flow_matching,0.3,2.0,50,165
9,replace,1.0,F,],F]@,FF@,3,replace ] at position 1 with F,flow_matching,0.3,2.0,50,165
10,replace,1.0,c,F,FF@,Fc@,3,replace F at position 1 with c,flow_matching,0.3,2.0,50,165
11,remove,1.0,c,,Fc@,F@,2,remove c from position 1,flow_matching,0.3,2.0,50,165
12,replace,1.0,c,@,F@,Fc,2,replace @ at position 1 with c,flow_matching,0.3,2.0,50,165
13,add,2.0,I,,Fc,FcI,3,add I at position 2,flow_matching,0.3,2.0,50,165
14,add,0.0,7,,FcI,7FcI,4,add 7 at position 0,flow_matching,0.3,2.0,50,165
15,replace,0.0,F,7,7FcI,FFcI,4,replace 7 at position 0 with F,flow_matching,0.3,2.0,50,165
16,replace,1.0,c,F,FFcI,FccI,4,replace F at position 1 with c,flow_matching,0.3,2.0,50,165
17,add,3.0,B,,FccI,FccBI,5,add B at position 3,flow_matching,0.3,2.0,50,165
18,replace,2.0,1,c,FccBI,Fc1BI,5,replace c at position 2 with 1,flow_matching,0.3,2.0,50,165
19,add,5.0,C,,Fc1BI,Fc1BIC,6,add C at position 5,flow_matching,0.3,2.0,50,165
20,replace,4.0,4,I,Fc1BIC,Fc1B4C,6,replace I at position 4 with 4,flow_matching,0.3,2.0,50,165
21,add,3.0,4,,Fc1B4C,Fc14B4C,7,add 4 at position 3,flow_matching,0.3,2.0,50,165
22,add,3.0,),,Fc14B4C,Fc1)4B4C,8,add ) at position 3,flow_matching,0.3,2.0,50,165
23,replace,3.0,c,),Fc1)4B4C,Fc1c4B4C,8,replace ) at position 3 with c,flow_matching,0.3,2.0,50,165
24,add,0.0,(,,Fc1c4B4C,(Fc1c4B4C,9,add ( at position 0,flow_matching,0.3,2.0,50,165
25,remove,4.0,c,,(Fc1c4B4C,(Fc14B4C,8,remove c from position 4,flow_matching,0.3,2.0,50,165
26,add,0.0,/,,(Fc14B4C,/(Fc14B4C,9,add / at position 0,flow_matching,0.3,2.0,50,165
27,replace,0.0,F,/,/(Fc14B4C,F(Fc14B4C,9,replace / at position 0 with F,flow_matching,0.3,2.0,50,165
28,add,9.0,=,,F(Fc14B4C,F(Fc14B4C=,10,add = at position 9,flow_matching,0.3,2.0,50,165
29,replace,1.0,c,(,F(Fc14B4C=,FcFc14B4C=,10,replace ( at position 1 with c,flow_matching,0.3,2.0,50,165
30,replace,7.0,O,4,FcFc14B4C=,FcFc14BOC=,10,replace 4 at position 7 with O,flow_matching,0.3,2.0,50,165
31,add,9.0,],,FcFc14BOC=,FcFc14BOC]=,11,add ] at position 9,flow_matching,0.3,2.0,50,165
32,replace,5.0,],4,FcFc14BOC]=,FcFc1]BOC]=,11,replace 4 at position 5 with ],flow_matching,0.3,2.0,50,165
33,remove,8.0,C,,FcFc1]BOC]=,FcFc1]BO]=,10,remove C from position 8,flow_matching,0.3,2.0,50,165
34,remove,4.0,1,,FcFc1]BO]=,FcFc]BO]=,9,remove 1 from position 4,flow_matching,0.3,2.0,50,165
35,replace,2.0,1,F,FcFc]BO]=,Fc1c]BO]=,9,replace F at position 2 with 1,flow_matching,0.3,2.0,50,165
36,replace,1.0,3,c,Fc1c]BO]=,F31c]BO]=,9,replace c at position 1 with 3,flow_matching,0.3,2.0,50,165
37,add,2.0,),,F31c]BO]=,F3)1c]BO]=,10,add ) at position 2,flow_matching,0.3,2.0,50,165
38,add,9.0,@,,F3)1c]BO]=,F3)1c]BO]@=,11,add @ at position 9,flow_matching,0.3,2.0,50,165
39,add,0.0,6,,F3)1c]BO]@=,6F3)1c]BO]@=,12,add 6 at position 0,flow_matching,0.3,2.0,50,165
40,remove,9.0,],,6F3)1c]BO]@=,6F3)1c]BO@=,11,remove ] from position 9,flow_matching,0.3,2.0,50,165
41,replace,8.0,[,O,6F3)1c]BO@=,6F3)1c]B[@=,11,replace O at position 8 with [,flow_matching,0.3,2.0,50,165
42,replace,0.0,F,6,6F3)1c]B[@=,FF3)1c]B[@=,11,replace 6 at position 0 with F,flow_matching,0.3,2.0,50,165
43,replace,1.0,c,F,FF3)1c]B[@=,Fc3)1c]B[@=,11,replace F at position 1 with c,flow_matching,0.3,2.0,50,165
44,remove,10.0,=,,Fc3)1c]B[@=,Fc3)1c]B[@,10,remove = from position 10,flow_matching,0.3,2.0,50,165
45,remove,4.0,1,,Fc3)1c]B[@,Fc3)c]B[@,9,remove 1 from position 4,flow_matching,0.3,2.0,50,165
46,remove,6.0,B,,Fc3)c]B[@,Fc3)c][@,8,remove B from position 6,flow_matching,0.3,2.0,50,165
47,add,5.0,l,,Fc3)c][@,Fc3)cl][@,9,add l at position 5,flow_matching,0.3,2.0,50,165
48,replace,2.0,1,3,Fc3)cl][@,Fc1)cl][@,9,replace 3 at position 2 with 1,flow_matching,0.3,2.0,50,165
49,replace,7.0,s,[,Fc1)cl][@,Fc1)cl]s@,9,replace [ at position 7 with s,flow_matching,0.3,2.0,50,165
50,add,1.0,5,,Fc1)cl]s@,F5c1)cl]s@,10,add 5 at position 1,flow_matching,0.3,2.0,50,165
51,add,0.0,3,,F5c1)cl]s@,3F5c1)cl]s@,11,add 3 at position 0,flow_matching,0.3,2.0,50,165
52,remove,3.0,c,,3F5c1)cl]s@,3F51)cl]s@,10,remove c from position 3,flow_matching,0.3,2.0,50,165
53,remove,5.0,c,,3F51)cl]s@,3F51)l]s@,9,remove c from position 5,flow_matching,0.3,2.0,50,165
54,remove,8.0,@,,3F51)l]s@,3F51)l]s,8,remove @ from position 8,flow_matching,0.3,2.0,50,165
55,replace,0.0,F,3,3F51)l]s,FF51)l]s,8,replace 3 at position 0 with F,flow_matching,0.3,2.0,50,165
56,replace,1.0,c,F,FF51)l]s,Fc51)l]s,8,replace F at position 1 with c,flow_matching,0.3,2.0,50,165
57,add,3.0,#,,Fc51)l]s,Fc5#1)l]s,9,add # at position 3,flow_matching,0.3,2.0,50,165
58,add,8.0,r,,Fc5#1)l]s,Fc5#1)l]rs,10,add r at position 8,flow_matching,0.3,2.0,50,165
59,replace,2.0,1,5,Fc5#1)l]rs,Fc1#1)l]rs,10,replace 5 at position 2 with 1,flow_matching,0.3,2.0,50,165
60,replace,6.0,N,l,Fc1#1)l]rs,Fc1#1)N]rs,10,replace l at position 6 with N,flow_matching,0.3,2.0,50,165
61,add,5.0,B,,Fc1#1)N]rs,Fc1#1B)N]rs,11,add B at position 5,flow_matching,0.3,2.0,50,165
62,replace,6.0,],),Fc1#1B)N]rs,Fc1#1B]N]rs,11,replace ) at position 6 with ],flow_matching,0.3,2.0,50,165
63,replace,3.0,c,#,Fc1#1B]N]rs,Fc1c1B]N]rs,11,replace # at position 3 with c,flow_matching,0.3,2.0,50,165
64,remove,9.0,r,,Fc1c1B]N]rs,Fc1c1B]N]s,10,remove r from position 9,flow_matching,0.3,2.0,50,165
65,add,4.0,l,,Fc1c1B]N]s,Fc1cl1B]N]s,11,add l at position 4,flow_matching,0.3,2.0,50,165
66,add,11.0,@,,Fc1cl1B]N]s,Fc1cl1B]N]s@,12,add @ at position 11,flow_matching,0.3,2.0,50,165
67,add,12.0,],,Fc1cl1B]N]s@,Fc1cl1B]N]s@],13,add ] at position 12,flow_matching,0.3,2.0,50,165
68,add,1.0,[,,Fc1cl1B]N]s@],F[c1cl1B]N]s@],14,add [ at position 1,flow_matching,0.3,2.0,50,165
69,add,10.0,-,,F[c1cl1B]N]s@],F[c1cl1B]N-]s@],15,add - at position 10,flow_matching,0.3,2.0,50,165
70,replace,4.0,N,c,F[c1cl1B]N-]s@],F[c1Nl1B]N-]s@],15,replace c at position 4 with N,flow_matching,0.3,2.0,50,165
71,replace,5.0,(,l,F[c1Nl1B]N-]s@],F[c1N(1B]N-]s@],15,replace l at position 5 with (,flow_matching,0.3,2.0,50,165
72,replace,1.0,c,[,F[c1N(1B]N-]s@],Fcc1N(1B]N-]s@],15,replace [ at position 1 with c,flow_matching,0.3,2.0,50,165
73,replace,1.0,F,c,Fcc1N(1B]N-]s@],FFc1N(1B]N-]s@],15,replace c at position 1 with F,flow_matching,0.3,2.0,50,165
74,replace,3.0,),1,FFc1N(1B]N-]s@],FFc)N(1B]N-]s@],15,replace 1 at position 3 with ),flow_matching,0.3,2.0,50,165
75,replace,1.0,c,F,FFc)N(1B]N-]s@],Fcc)N(1B]N-]s@],15,replace F at position 1 with c,flow_matching,0.3,2.0,50,165
76,replace,2.0,1,c,Fcc)N(1B]N-]s@],Fc1)N(1B]N-]s@],15,replace c at position 2 with 1,flow_matching,0.3,2.0,50,165
77,remove,13.0,@,,Fc1)N(1B]N-]s@],Fc1)N(1B]N-]s],14,remove @ from position 13,flow_matching,0.3,2.0,50,165
78,replace,3.0,c,),Fc1)N(1B]N-]s],Fc1cN(1B]N-]s],14,replace ) at position 3 with c,flow_matching,0.3,2.0,50,165
79,replace,4.0,5,N,Fc1cN(1B]N-]s],Fc1c5(1B]N-]s],14,replace N at position 4 with 5,flow_matching,0.3,2.0,50,165
80,replace,1.0,(,c,Fc1c5(1B]N-]s],F(1c5(1B]N-]s],14,replace c at position 1 with (,flow_matching,0.3,2.0,50,165
81,replace,1.0,c,(,F(1c5(1B]N-]s],Fc1c5(1B]N-]s],14,replace ( at position 1 with c,flow_matching,0.3,2.0,50,165
82,replace,3.0,l,c,Fc1c5(1B]N-]s],Fc1l5(1B]N-]s],14,replace c at position 3 with l,flow_matching,0.3,2.0,50,165
83,remove,3.0,l,,Fc1l5(1B]N-]s],Fc15(1B]N-]s],13,remove l from position 3,flow_matching,0.3,2.0,50,165
84,add,1.0,(,,Fc15(1B]N-]s],F(c15(1B]N-]s],14,add ( at position 1,flow_matching,0.3,2.0,50,165
85,add,4.0,I,,F(c15(1B]N-]s],F(c1I5(1B]N-]s],15,add I at position 4,flow_matching,0.3,2.0,50,165
86,replace,1.0,c,(,F(c1I5(1B]N-]s],Fcc1I5(1B]N-]s],15,replace ( at position 1 with c,flow_matching,0.3,2.0,50,165
87,add,15.0,\,,Fcc1I5(1B]N-]s],Fcc1I5(1B]N-]s]\,16,add \ at position 15,flow_matching,0.3,2.0,50,165
88,add,16.0,#,,Fcc1I5(1B]N-]s]\,Fcc1I5(1B]N-]s]\#,17,add # at position 16,flow_matching,0.3,2.0,50,165
89,add,8.0,5,,Fcc1I5(1B]N-]s]\#,Fcc1I5(15B]N-]s]\#,18,add 5 at position 8,flow_matching,0.3,2.0,50,165
90,replace,13.0,r,],Fcc1I5(15B]N-]s]\#,Fcc1I5(15B]N-rs]\#,18,replace ] at position 13 with r,flow_matching,0.3,2.0,50,165
91,add,17.0,N,,Fcc1I5(15B]N-rs]\#,Fcc1I5(15B]N-rs]\N#,19,add N at position 17,flow_matching,0.3,2.0,50,165
92,add,13.0,1,,Fcc1I5(15B]N-rs]\N#,Fcc1I5(15B]N-1rs]\N#,20,add 1 at position 13,flow_matching,0.3,2.0,50,165
93,replace,2.0,1,c,Fcc1I5(15B]N-1rs]\N#,Fc11I5(15B]N-1rs]\N#,20,replace c at position 2 with 1,flow_matching,0.3,2.0,50,165
94,replace,4.0,4,I,Fc11I5(15B]N-1rs]\N#,Fc1145(15B]N-1rs]\N#,20,replace I at position 4 with 4,flow_matching,0.3,2.0,50,165
95,add,8.0,\,,Fc1145(15B]N-1rs]\N#,Fc1145(1\5B]N-1rs]\N#,21,add \ at position 8,flow_matching,0.3,2.0,50,165
96,replace,13.0,3,-,Fc1145(1\5B]N-1rs]\N#,Fc1145(1\5B]N31rs]\N#,21,replace - at position 13 with 3,flow_matching,0.3,2.0,50,165
97,replace,3.0,c,1,Fc1145(1\5B]N31rs]\N#,Fc1c45(1\5B]N31rs]\N#,21,replace 1 at position 3 with c,flow_matching,0.3,2.0,50,165
98,replace,4.0,c,4,Fc1c45(1\5B]N31rs]\N#,Fc1cc5(1\5B]N31rs]\N#,21,replace 4 at position 4 with c,flow_matching,0.3,2.0,50,165
99,remove,20.0,#,,Fc1cc5(1\5B]N31rs]\N#,Fc1cc5(1\5B]N31rs]\N,20,remove # from position 20,flow_matching,0.3,2.0,50,165
100,replace,5.0,c,5,Fc1cc5(1\5B]N31rs]\N,Fc1ccc(1\5B]N31rs]\N,20,replace 5 at position 5 with c,flow_matching,0.3,2.0,50,165
101,add,13.0,),,Fc1ccc(1\5B]N31rs]\N,Fc1ccc(1\5B]N)31rs]\N,21,add ) at position 13,flow_matching,0.3,2.0,50,165
102,add,0.0,l,,Fc1ccc(1\5B]N)31rs]\N,lFc1ccc(1\5B]N)31rs]\N,22,add l at position 0,flow_matching,0.3,2.0,50,165
103,replace,15.0,#,3,lFc1ccc(1\5B]N)31rs]\N,lFc1ccc(1\5B]N)#1rs]\N,22,replace 3 at position 15 with #,flow_matching,0.3,2.0,50,165
104,add,13.0,@,,lFc1ccc(1\5B]N)#1rs]\N,lFc1ccc(1\5B]@N)#1rs]\N,23,add @ at position 13,flow_matching,0.3,2.0,50,165
105,replace,0.0,F,l,lFc1ccc(1\5B]@N)#1rs]\N,FFc1ccc(1\5B]@N)#1rs]\N,23,replace l at position 0 with F,flow_matching,0.3,2.0,50,165
106,replace,1.0,c,F,FFc1ccc(1\5B]@N)#1rs]\N,Fcc1ccc(1\5B]@N)#1rs]\N,23,replace F at position 1 with c,flow_matching,0.3,2.0,50,165
107,add,7.0,),,Fcc1ccc(1\5B]@N)#1rs]\N,Fcc1ccc)(1\5B]@N)#1rs]\N,24,add ) at position 7,flow_matching,0.3,2.0,50,165
108,replace,22.0,/,\,Fcc1ccc)(1\5B]@N)#1rs]\N,Fcc1ccc)(1\5B]@N)#1rs]/N,24,replace \ at position 22 with /,flow_matching,0.3,2.0,50,165
109,add,14.0,+,,Fcc1ccc)(1\5B]@N)#1rs]/N,Fcc1ccc)(1\5B]+@N)#1rs]/N,25,add + at position 14,flow_matching,0.3,2.0,50,165
110,replace,23.0,I,/,Fcc1ccc)(1\5B]+@N)#1rs]/N,Fcc1ccc)(1\5B]+@N)#1rs]IN,25,replace / at position 23 with I,flow_matching,0.3,2.0,50,165
111,replace,4.0,),c,Fcc1ccc)(1\5B]+@N)#1rs]IN,Fcc1)cc)(1\5B]+@N)#1rs]IN,25,replace c at position 4 with ),flow_matching,0.3,2.0,50,165
112,add,11.0,],,Fcc1)cc)(1\5B]+@N)#1rs]IN,Fcc1)cc)(1\]5B]+@N)#1rs]IN,26,add ] at position 11,flow_matching,0.3,2.0,50,165
113,add,8.0,[,,Fcc1)cc)(1\]5B]+@N)#1rs]IN,Fcc1)cc)[(1\]5B]+@N)#1rs]IN,27,add [ at position 8,flow_matching,0.3,2.0,50,165
114,replace,2.0,1,c,Fcc1)cc)[(1\]5B]+@N)#1rs]IN,Fc11)cc)[(1\]5B]+@N)#1rs]IN,27,replace c at position 2 with 1,flow_matching,0.3,2.0,50,165
115,replace,3.0,c,1,Fc11)cc)[(1\]5B]+@N)#1rs]IN,Fc1c)cc)[(1\]5B]+@N)#1rs]IN,27,replace 1 at position 3 with c,flow_matching,0.3,2.0,50,165
116,remove,6.0,c,,Fc1c)cc)[(1\]5B]+@N)#1rs]IN,Fc1c)c)[(1\]5B]+@N)#1rs]IN,26,remove c from position 6,flow_matching,0.3,2.0,50,165
117,remove,15.0,+,,Fc1c)c)[(1\]5B]+@N)#1rs]IN,Fc1c)c)[(1\]5B]@N)#1rs]IN,25,remove + from position 15,flow_matching,0.3,2.0,50,165
118,add,19.0,+,,Fc1c)c)[(1\]5B]@N)#1rs]IN,Fc1c)c)[(1\]5B]@N)#+1rs]IN,26,add + at position 19,flow_matching,0.3,2.0,50,165
119,add,22.0,F,,Fc1c)c)[(1\]5B]@N)#+1rs]IN,Fc1c)c)[(1\]5B]@N)#+1rFs]IN,27,add F at position 22,flow_matching,0.3,2.0,50,165
120,replace,25.0,5,I,Fc1c)c)[(1\]5B]@N)#+1rFs]IN,Fc1c)c)[(1\]5B]@N)#+1rFs]5N,27,replace I at position 25 with 5,flow_matching,0.3,2.0,50,165
121,replace,4.0,c,),Fc1c)c)[(1\]5B]@N)#+1rFs]5N,Fc1ccc)[(1\]5B]@N)#+1rFs]5N,27,replace ) at position 4 with c,flow_matching,0.3,2.0,50,165
122,replace,6.0,(,),Fc1ccc)[(1\]5B]@N)#+1rFs]5N,Fc1ccc([(1\]5B]@N)#+1rFs]5N,27,replace ) at position 6 with (,flow_matching,0.3,2.0,50,165
123,replace,7.0,C,[,Fc1ccc([(1\]5B]@N)#+1rFs]5N,Fc1ccc(C(1\]5B]@N)#+1rFs]5N,27,replace [ at position 7 with C,flow_matching,0.3,2.0,50,165
124,replace,8.0,[,(,Fc1ccc(C(1\]5B]@N)#+1rFs]5N,Fc1ccc(C[1\]5B]@N)#+1rFs]5N,27,replace ( at position 8 with [,flow_matching,0.3,2.0,50,165
125,replace,9.0,N,1,Fc1ccc(C[1\]5B]@N)#+1rFs]5N,Fc1ccc(C[N\]5B]@N)#+1rFs]5N,27,replace 1 at position 9 with N,flow_matching,0.3,2.0,50,165
126,replace,10.0,H,\,Fc1ccc(C[N\]5B]@N)#+1rFs]5N,Fc1ccc(C[NH]5B]@N)#+1rFs]5N,27,replace \ at position 10 with H,flow_matching,0.3,2.0,50,165
127,replace,11.0,2,],Fc1ccc(C[NH]5B]@N)#+1rFs]5N,Fc1ccc(C[NH25B]@N)#+1rFs]5N,27,replace ] at position 11 with 2,flow_matching,0.3,2.0,50,165
128,replace,12.0,+,5,Fc1ccc(C[NH25B]@N)#+1rFs]5N,Fc1ccc(C[NH2+B]@N)#+1rFs]5N,27,replace 5 at position 12 with +,flow_matching,0.3,2.0,50,165
129,replace,13.0,],B,Fc1ccc(C[NH2+B]@N)#+1rFs]5N,Fc1ccc(C[NH2+]]@N)#+1rFs]5N,27,replace B at position 13 with ],flow_matching,0.3,2.0,50,165
130,replace,14.0,C,],Fc1ccc(C[NH2+]]@N)#+1rFs]5N,Fc1ccc(C[NH2+]C@N)#+1rFs]5N,27,replace ] at position 14 with C,flow_matching,0.3,2.0,50,165
131,replace,15.0,[,@,Fc1ccc(C[NH2+]C@N)#+1rFs]5N,Fc1ccc(C[NH2+]C[N)#+1rFs]5N,27,replace @ at position 15 with [,flow_matching,0.3,2.0,50,165
132,replace,16.0,C,N,Fc1ccc(C[NH2+]C[N)#+1rFs]5N,Fc1ccc(C[NH2+]C[C)#+1rFs]5N,27,replace N at position 16 with C,flow_matching,0.3,2.0,50,165
133,replace,17.0,@,),Fc1ccc(C[NH2+]C[C)#+1rFs]5N,Fc1ccc(C[NH2+]C[C@#+1rFs]5N,27,replace ) at position 17 with @,flow_matching,0.3,2.0,50,165
134,replace,18.0,@,#,Fc1ccc(C[NH2+]C[C@#+1rFs]5N,Fc1ccc(C[NH2+]C[C@@+1rFs]5N,27,replace # at position 18 with @,flow_matching,0.3,2.0,50,165
135,replace,19.0,H,+,Fc1ccc(C[NH2+]C[C@@+1rFs]5N,Fc1ccc(C[NH2+]C[C@@H1rFs]5N,27,replace + at position 19 with H,flow_matching,0.3,2.0,50,165
136,replace,20.0,],1,Fc1ccc(C[NH2+]C[C@@H1rFs]5N,Fc1ccc(C[NH2+]C[C@@H]rFs]5N,27,replace 1 at position 20 with ],flow_matching,0.3,2.0,50,165
137,replace,21.0,(,r,Fc1ccc(C[NH2+]C[C@@H]rFs]5N,Fc1ccc(C[NH2+]C[C@@H](Fs]5N,27,replace r at position 21 with (,flow_matching,0.3,2.0,50,165
138,replace,22.0,[,F,Fc1ccc(C[NH2+]C[C@@H](Fs]5N,Fc1ccc(C[NH2+]C[C@@H]([s]5N,27,replace F at position 22 with [,flow_matching,0.3,2.0,50,165
139,replace,23.0,C,s,Fc1ccc(C[NH2+]C[C@@H]([s]5N,Fc1ccc(C[NH2+]C[C@@H]([C]5N,27,replace s at position 23 with C,flow_matching,0.3,2.0,50,165
140,replace,24.0,@,],Fc1ccc(C[NH2+]C[C@@H]([C]5N,Fc1ccc(C[NH2+]C[C@@H]([C@5N,27,replace ] at position 24 with @,flow_matching,0.3,2.0,50,165
141,replace,25.0,H,5,Fc1ccc(C[NH2+]C[C@@H]([C@5N,Fc1ccc(C[NH2+]C[C@@H]([C@HN,27,replace 5 at position 25 with H,flow_matching,0.3,2.0,50,165
142,replace,26.0,],N,Fc1ccc(C[NH2+]C[C@@H]([C@HN,Fc1ccc(C[NH2+]C[C@@H]([C@H],27,replace N at position 26 with ],flow_matching,0.3,2.0,50,165
143,add,27.0,2,,Fc1ccc(C[NH2+]C[C@@H]([C@H],Fc1ccc(C[NH2+]C[C@@H]([C@H]2,28,add 2 at position 27,flow_matching,0.3,2.0,50,165
144,add,28.0,C,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2,Fc1ccc(C[NH2+]C[C@@H]([C@H]2C,29,add C at position 28,flow_matching,0.3,2.0,50,165
145,add,29.0,C,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2C,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CC,30,add C at position 29,flow_matching,0.3,2.0,50,165
146,add,30.0,O,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CC,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCO,31,add O at position 30,flow_matching,0.3,2.0,50,165
147,add,31.0,C,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCO,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC,32,add C at position 31,flow_matching,0.3,2.0,50,165
148,add,32.0,2,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2,33,add 2 at position 32,flow_matching,0.3,2.0,50,165
149,add,33.0,),,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2),34,add ) at position 33,flow_matching,0.3,2.0,50,165
150,add,34.0,N,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2),Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N,35,add N at position 34,flow_matching,0.3,2.0,50,165
151,add,35.0,2,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2,36,add 2 at position 35,flow_matching,0.3,2.0,50,165
152,add,36.0,C,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2C,37,add C at position 36,flow_matching,0.3,2.0,50,165
153,add,37.0,C,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2C,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CC,38,add C at position 37,flow_matching,0.3,2.0,50,165
154,add,38.0,O,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CC,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCO,39,add O at position 38,flow_matching,0.3,2.0,50,165
155,add,39.0,C,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCO,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOC,40,add C at position 39,flow_matching,0.3,2.0,50,165
156,add,40.0,C,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOC,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC,41,add C at position 40,flow_matching,0.3,2.0,50,165
157,add,41.0,2,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2,42,add 2 at position 41,flow_matching,0.3,2.0,50,165
158,add,42.0,),,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2),43,add ) at position 42,flow_matching,0.3,2.0,50,165
159,add,43.0,c,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2),Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c,44,add c at position 43,flow_matching,0.3,2.0,50,165
160,add,44.0,(,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(,45,add ( at position 44,flow_matching,0.3,2.0,50,165
161,add,45.0,F,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(F,46,add F at position 45,flow_matching,0.3,2.0,50,165
162,add,46.0,),,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(F,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(F),47,add ) at position 46,flow_matching,0.3,2.0,50,165
163,add,47.0,c,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(F),Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(F)c,48,add c at position 47,flow_matching,0.3,2.0,50,165
164,add,48.0,1,,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(F)c,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(F)c1,49,add 1 at position 48,flow_matching,0.3,2.0,50,165
165,add,49.0,"
",,Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(F)c1,"Fc1ccc(C[NH2+]C[C@@H]([C@H]2CCOC2)N2CCOCC2)c(F)c1
",50,"add 
 at position 49",flow_matching,0.3,2.0,50,165
