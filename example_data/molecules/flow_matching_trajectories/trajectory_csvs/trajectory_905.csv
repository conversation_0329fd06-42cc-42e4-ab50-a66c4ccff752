step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,27,53
1,add,0.0,),,,),1,add ) at position 0,flow_matching,0.3,2.0,27,53
2,replace,0.0,C,),),C,1,replace ) at position 0 with C,flow_matching,0.3,2.0,27,53
3,replace,0.0,2,C,C,2,1,replace <PERSON> at position 0 with 2,flow_matching,0.3,2.0,27,53
4,replace,0.0,C,2,2,C,1,replace 2 at position 0 with C,flow_matching,0.3,2.0,27,53
5,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,27,53
6,replace,0.0,<PERSON>,<PERSON>,<PERSON>,<PERSON>,2,replace <PERSON> at position 0 with H,flow_matching,0.3,2.0,27,53
7,remove,1.0,<PERSON>,,HC,H,1,remove C from position 1,flow_matching,0.3,2.0,27,53
8,replace,0.0,<PERSON>,<PERSON>,H,C,1,replace H at position 0 with C,flow_matching,0.3,2.0,27,53
9,replace,0.0,),C,C,),1,replace C at position 0 with ),flow_matching,0.3,2.0,27,53
10,replace,0.0,C,),),C,1,replace ) at position 0 with C,flow_matching,0.3,2.0,27,53
11,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,27,53
12,add,2.0,c,,CC,CCc,3,add c at position 2,flow_matching,0.3,2.0,27,53
13,add,3.0,1,,CCc,CCc1,4,add 1 at position 3,flow_matching,0.3,2.0,27,53
14,add,1.0,S,,CCc1,CSCc1,5,add S at position 1,flow_matching,0.3,2.0,27,53
15,add,3.0,o,,CSCc1,CSCoc1,6,add o at position 3,flow_matching,0.3,2.0,27,53
16,add,6.0,\,,CSCoc1,CSCoc1\,7,add \ at position 6,flow_matching,0.3,2.0,27,53
17,replace,3.0,(,o,CSCoc1\,CSC(c1\,7,replace o at position 3 with (,flow_matching,0.3,2.0,27,53
18,replace,1.0,C,S,CSC(c1\,CCC(c1\,7,replace S at position 1 with C,flow_matching,0.3,2.0,27,53
19,replace,2.0,c,C,CCC(c1\,CCc(c1\,7,replace C at position 2 with c,flow_matching,0.3,2.0,27,53
20,replace,4.0,B,c,CCc(c1\,CCc(B1\,7,replace c at position 4 with B,flow_matching,0.3,2.0,27,53
21,replace,3.0,1,(,CCc(B1\,CCc1B1\,7,replace ( at position 3 with 1,flow_matching,0.3,2.0,27,53
22,replace,4.0,c,B,CCc1B1\,CCc1c1\,7,replace B at position 4 with c,flow_matching,0.3,2.0,27,53
23,add,5.0,l,,CCc1c1\,CCc1cl1\,8,add l at position 5,flow_matching,0.3,2.0,27,53
24,replace,5.0,c,l,CCc1cl1\,CCc1cc1\,8,replace l at position 5 with c,flow_matching,0.3,2.0,27,53
25,replace,6.0,s,1,CCc1cc1\,CCc1ccs\,8,replace 1 at position 6 with s,flow_matching,0.3,2.0,27,53
26,replace,7.0,c,\,CCc1ccs\,CCc1ccsc,8,replace \ at position 7 with c,flow_matching,0.3,2.0,27,53
27,replace,0.0,5,C,CCc1ccsc,5Cc1ccsc,8,replace C at position 0 with 5,flow_matching,0.3,2.0,27,53
28,remove,4.0,c,,5Cc1ccsc,5Cc1csc,7,remove c from position 4,flow_matching,0.3,2.0,27,53
29,replace,0.0,C,5,5Cc1csc,CCc1csc,7,replace 5 at position 0 with C,flow_matching,0.3,2.0,27,53
30,remove,5.0,s,,CCc1csc,CCc1cc,6,remove s from position 5,flow_matching,0.3,2.0,27,53
31,replace,5.0,3,c,CCc1cc,CCc1c3,6,replace c at position 5 with 3,flow_matching,0.3,2.0,27,53
32,replace,5.0,c,3,CCc1c3,CCc1cc,6,replace 3 at position 5 with c,flow_matching,0.3,2.0,27,53
33,add,6.0,s,,CCc1cc,CCc1ccs,7,add s at position 6,flow_matching,0.3,2.0,27,53
34,add,7.0,c,,CCc1ccs,CCc1ccsc,8,add c at position 7,flow_matching,0.3,2.0,27,53
35,add,8.0,1,,CCc1ccsc,CCc1ccsc1,9,add 1 at position 8,flow_matching,0.3,2.0,27,53
36,add,9.0,-,,CCc1ccsc1,CCc1ccsc1-,10,add - at position 9,flow_matching,0.3,2.0,27,53
37,add,10.0,c,,CCc1ccsc1-,CCc1ccsc1-c,11,add c at position 10,flow_matching,0.3,2.0,27,53
38,add,11.0,1,,CCc1ccsc1-c,CCc1ccsc1-c1,12,add 1 at position 11,flow_matching,0.3,2.0,27,53
39,add,12.0,c,,CCc1ccsc1-c1,CCc1ccsc1-c1c,13,add c at position 12,flow_matching,0.3,2.0,27,53
40,add,13.0,n,,CCc1ccsc1-c1c,CCc1ccsc1-c1cn,14,add n at position 13,flow_matching,0.3,2.0,27,53
41,add,14.0,c,,CCc1ccsc1-c1cn,CCc1ccsc1-c1cnc,15,add c at position 14,flow_matching,0.3,2.0,27,53
42,add,15.0,(,,CCc1ccsc1-c1cnc,CCc1ccsc1-c1cnc(,16,add ( at position 15,flow_matching,0.3,2.0,27,53
43,add,16.0,C,,CCc1ccsc1-c1cnc(,CCc1ccsc1-c1cnc(C,17,add C at position 16,flow_matching,0.3,2.0,27,53
44,add,17.0,[,,CCc1ccsc1-c1cnc(C,CCc1ccsc1-c1cnc(C[,18,add [ at position 17,flow_matching,0.3,2.0,27,53
45,add,18.0,N,,CCc1ccsc1-c1cnc(C[,CCc1ccsc1-c1cnc(C[N,19,add N at position 18,flow_matching,0.3,2.0,27,53
46,add,19.0,H,,CCc1ccsc1-c1cnc(C[N,CCc1ccsc1-c1cnc(C[NH,20,add H at position 19,flow_matching,0.3,2.0,27,53
47,add,20.0,3,,CCc1ccsc1-c1cnc(C[NH,CCc1ccsc1-c1cnc(C[NH3,21,add 3 at position 20,flow_matching,0.3,2.0,27,53
48,add,21.0,+,,CCc1ccsc1-c1cnc(C[NH3,CCc1ccsc1-c1cnc(C[NH3+,22,add + at position 21,flow_matching,0.3,2.0,27,53
49,add,22.0,],,CCc1ccsc1-c1cnc(C[NH3+,CCc1ccsc1-c1cnc(C[NH3+],23,add ] at position 22,flow_matching,0.3,2.0,27,53
50,add,23.0,),,CCc1ccsc1-c1cnc(C[NH3+],CCc1ccsc1-c1cnc(C[NH3+]),24,add ) at position 23,flow_matching,0.3,2.0,27,53
51,add,24.0,o,,CCc1ccsc1-c1cnc(C[NH3+]),CCc1ccsc1-c1cnc(C[NH3+])o,25,add o at position 24,flow_matching,0.3,2.0,27,53
52,add,25.0,1,,CCc1ccsc1-c1cnc(C[NH3+])o,CCc1ccsc1-c1cnc(C[NH3+])o1,26,add 1 at position 25,flow_matching,0.3,2.0,27,53
53,add,26.0,"
",,CCc1ccsc1-c1cnc(C[NH3+])o1,"CCc1ccsc1-c1cnc(C[NH3+])o1
",27,"add 
 at position 26",flow_matching,0.3,2.0,27,53
