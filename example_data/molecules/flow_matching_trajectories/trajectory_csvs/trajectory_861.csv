step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,38,146
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,38,146
2,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,38,146
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,38,146
4,add,1.0,H,,C,CH,2,add H at position 1,flow_matching,0.3,2.0,38,146
5,add,2.0,7,,CH,CH7,3,add 7 at position 2,flow_matching,0.3,2.0,38,146
6,replace,1.0,<PERSON>,H,CH7,CC7,3,replace <PERSON> at position 1 with <PERSON>,flow_matching,0.3,2.0,38,146
7,add,1.0,(,,CC7,C(C7,4,add ( at position 1,flow_matching,0.3,2.0,38,146
8,remove,0.0,C,,C(C7,(C7,3,remove C from position 0,flow_matching,0.3,2.0,38,146
9,replace,2.0,O,7,(C7,(CO,3,replace 7 at position 2 with O,flow_matching,0.3,2.0,38,146
10,replace,0.0,C,(,(CO,CCO,3,replace ( at position 0 with C,flow_matching,0.3,2.0,38,146
11,replace,2.0,C,O,CCO,CCC,3,replace O at position 2 with C,flow_matching,0.3,2.0,38,146
12,add,3.0,[,,CCC,CCC[,4,add [ at position 3,flow_matching,0.3,2.0,38,146
13,add,4.0,C,,CCC[,CCC[C,5,add C at position 4,flow_matching,0.3,2.0,38,146
14,add,4.0,H,,CCC[C,CCC[HC,6,add H at position 4,flow_matching,0.3,2.0,38,146
15,replace,4.0,C,H,CCC[HC,CCC[CC,6,replace H at position 4 with C,flow_matching,0.3,2.0,38,146
16,replace,5.0,@,C,CCC[CC,CCC[C@,6,replace C at position 5 with @,flow_matching,0.3,2.0,38,146
17,add,6.0,H,,CCC[C@,CCC[C@H,7,add H at position 6,flow_matching,0.3,2.0,38,146
18,replace,4.0,),C,CCC[C@H,CCC[)@H,7,replace C at position 4 with ),flow_matching,0.3,2.0,38,146
19,replace,0.0,o,C,CCC[)@H,oCC[)@H,7,replace C at position 0 with o,flow_matching,0.3,2.0,38,146
20,replace,0.0,C,o,oCC[)@H,CCC[)@H,7,replace o at position 0 with C,flow_matching,0.3,2.0,38,146
21,remove,0.0,C,,CCC[)@H,CC[)@H,6,remove C from position 0,flow_matching,0.3,2.0,38,146
22,replace,4.0,),@,CC[)@H,CC[))H,6,replace @ at position 4 with ),flow_matching,0.3,2.0,38,146
23,replace,2.0,-,[,CC[))H,CC-))H,6,replace [ at position 2 with -,flow_matching,0.3,2.0,38,146
24,replace,0.0,O,C,CC-))H,OC-))H,6,replace C at position 0 with O,flow_matching,0.3,2.0,38,146
25,add,1.0,(,,OC-))H,O(C-))H,7,add ( at position 1,flow_matching,0.3,2.0,38,146
26,replace,6.0,@,H,O(C-))H,O(C-))@,7,replace H at position 6 with @,flow_matching,0.3,2.0,38,146
27,replace,0.0,C,O,O(C-))@,C(C-))@,7,replace O at position 0 with C,flow_matching,0.3,2.0,38,146
28,add,0.0,+,,C(C-))@,+C(C-))@,8,add + at position 0,flow_matching,0.3,2.0,38,146
29,replace,0.0,C,+,+C(C-))@,CC(C-))@,8,replace + at position 0 with C,flow_matching,0.3,2.0,38,146
30,add,8.0,/,,CC(C-))@,CC(C-))@/,9,add / at position 8,flow_matching,0.3,2.0,38,146
31,replace,2.0,C,(,CC(C-))@/,CCCC-))@/,9,replace ( at position 2 with C,flow_matching,0.3,2.0,38,146
32,remove,3.0,C,,CCCC-))@/,CCC-))@/,8,remove C from position 3,flow_matching,0.3,2.0,38,146
33,add,7.0,@,,CCC-))@/,CCC-))@@/,9,add @ at position 7,flow_matching,0.3,2.0,38,146
34,remove,4.0,),,CCC-))@@/,CCC-)@@/,8,remove ) from position 4,flow_matching,0.3,2.0,38,146
35,replace,4.0,/,),CCC-)@@/,CCC-/@@/,8,replace ) at position 4 with /,flow_matching,0.3,2.0,38,146
36,remove,1.0,C,,CCC-/@@/,CC-/@@/,7,remove C from position 1,flow_matching,0.3,2.0,38,146
37,add,3.0,#,,CC-/@@/,CC-#/@@/,8,add # at position 3,flow_matching,0.3,2.0,38,146
38,add,1.0,B,,CC-#/@@/,CBC-#/@@/,9,add B at position 1,flow_matching,0.3,2.0,38,146
39,replace,1.0,C,B,CBC-#/@@/,CCC-#/@@/,9,replace B at position 1 with C,flow_matching,0.3,2.0,38,146
40,add,7.0,4,,CCC-#/@@/,CCC-#/@4@/,10,add 4 at position 7,flow_matching,0.3,2.0,38,146
41,add,9.0,S,,CCC-#/@4@/,CCC-#/@4@S/,11,add S at position 9,flow_matching,0.3,2.0,38,146
42,replace,3.0,[,-,CCC-#/@4@S/,CCC[#/@4@S/,11,replace - at position 3 with [,flow_matching,0.3,2.0,38,146
43,remove,8.0,@,,CCC[#/@4@S/,CCC[#/@4S/,10,remove @ from position 8,flow_matching,0.3,2.0,38,146
44,replace,4.0,C,#,CCC[#/@4S/,CCC[C/@4S/,10,replace # at position 4 with C,flow_matching,0.3,2.0,38,146
45,add,5.0,3,,CCC[C/@4S/,CCC[C3/@4S/,11,add 3 at position 5,flow_matching,0.3,2.0,38,146
46,remove,5.0,3,,CCC[C3/@4S/,CCC[C/@4S/,10,remove 3 from position 5,flow_matching,0.3,2.0,38,146
47,replace,5.0,@,/,CCC[C/@4S/,CCC[C@@4S/,10,replace / at position 5 with @,flow_matching,0.3,2.0,38,146
48,replace,6.0,H,@,CCC[C@@4S/,CCC[C@H4S/,10,replace @ at position 6 with H,flow_matching,0.3,2.0,38,146
49,replace,7.0,],4,CCC[C@H4S/,CCC[C@H]S/,10,replace 4 at position 7 with ],flow_matching,0.3,2.0,38,146
50,add,4.0,],,CCC[C@H]S/,CCC[]C@H]S/,11,add ] at position 4,flow_matching,0.3,2.0,38,146
51,add,7.0,l,,CCC[]C@H]S/,CCC[]C@lH]S/,12,add l at position 7,flow_matching,0.3,2.0,38,146
52,replace,4.0,C,],CCC[]C@lH]S/,CCC[CC@lH]S/,12,replace ] at position 4 with C,flow_matching,0.3,2.0,38,146
53,remove,7.0,l,,CCC[CC@lH]S/,CCC[CC@H]S/,11,remove l from position 7,flow_matching,0.3,2.0,38,146
54,replace,2.0,H,C,CCC[CC@H]S/,CCH[CC@H]S/,11,replace C at position 2 with H,flow_matching,0.3,2.0,38,146
55,replace,2.0,C,H,CCH[CC@H]S/,CCC[CC@H]S/,11,replace H at position 2 with C,flow_matching,0.3,2.0,38,146
56,replace,5.0,@,C,CCC[CC@H]S/,CCC[C@@H]S/,11,replace C at position 5 with @,flow_matching,0.3,2.0,38,146
57,remove,3.0,[,,CCC[C@@H]S/,CCCC@@H]S/,10,remove [ from position 3,flow_matching,0.3,2.0,38,146
58,replace,2.0,r,C,CCCC@@H]S/,CCrC@@H]S/,10,replace C at position 2 with r,flow_matching,0.3,2.0,38,146
59,remove,4.0,@,,CCrC@@H]S/,CCrC@H]S/,9,remove @ from position 4,flow_matching,0.3,2.0,38,146
60,replace,2.0,C,r,CCrC@H]S/,CCCC@H]S/,9,replace r at position 2 with C,flow_matching,0.3,2.0,38,146
61,replace,8.0,#,/,CCCC@H]S/,CCCC@H]S#,9,replace / at position 8 with #,flow_matching,0.3,2.0,38,146
62,replace,3.0,[,C,CCCC@H]S#,CCC[@H]S#,9,replace C at position 3 with [,flow_matching,0.3,2.0,38,146
63,replace,4.0,C,@,CCC[@H]S#,CCC[CH]S#,9,replace @ at position 4 with C,flow_matching,0.3,2.0,38,146
64,replace,5.0,@,H,CCC[CH]S#,CCC[C@]S#,9,replace H at position 5 with @,flow_matching,0.3,2.0,38,146
65,replace,6.0,3,],CCC[C@]S#,CCC[C@3S#,9,replace ] at position 6 with 3,flow_matching,0.3,2.0,38,146
66,remove,4.0,C,,CCC[C@3S#,CCC[@3S#,8,remove C from position 4,flow_matching,0.3,2.0,38,146
67,remove,3.0,[,,CCC[@3S#,CCC@3S#,7,remove [ from position 3,flow_matching,0.3,2.0,38,146
68,replace,5.0,/,S,CCC@3S#,CCC@3/#,7,replace S at position 5 with /,flow_matching,0.3,2.0,38,146
69,replace,3.0,[,@,CCC@3/#,CCC[3/#,7,replace @ at position 3 with [,flow_matching,0.3,2.0,38,146
70,remove,2.0,C,,CCC[3/#,CC[3/#,6,remove C from position 2,flow_matching,0.3,2.0,38,146
71,replace,2.0,C,[,CC[3/#,CCC3/#,6,replace [ at position 2 with C,flow_matching,0.3,2.0,38,146
72,add,6.0,I,,CCC3/#,CCC3/#I,7,add I at position 6,flow_matching,0.3,2.0,38,146
73,replace,3.0,[,3,CCC3/#I,CCC[/#I,7,replace 3 at position 3 with [,flow_matching,0.3,2.0,38,146
74,replace,4.0,C,/,CCC[/#I,CCC[C#I,7,replace / at position 4 with C,flow_matching,0.3,2.0,38,146
75,replace,5.0,@,#,CCC[C#I,CCC[C@I,7,replace # at position 5 with @,flow_matching,0.3,2.0,38,146
76,replace,6.0,H,I,CCC[C@I,CCC[C@H,7,replace I at position 6 with H,flow_matching,0.3,2.0,38,146
77,add,7.0,],,CCC[C@H,CCC[C@H],8,add ] at position 7,flow_matching,0.3,2.0,38,146
78,add,4.0,C,,CCC[C@H],CCC[CC@H],9,add C at position 4,flow_matching,0.3,2.0,38,146
79,replace,5.0,@,C,CCC[CC@H],CCC[C@@H],9,replace C at position 5 with @,flow_matching,0.3,2.0,38,146
80,replace,6.0,H,@,CCC[C@@H],CCC[C@HH],9,replace @ at position 6 with H,flow_matching,0.3,2.0,38,146
81,remove,5.0,@,,CCC[C@HH],CCC[CHH],8,remove @ from position 5,flow_matching,0.3,2.0,38,146
82,remove,5.0,H,,CCC[CHH],CCC[CH],7,remove H from position 5,flow_matching,0.3,2.0,38,146
83,replace,2.0,S,C,CCC[CH],CCS[CH],7,replace C at position 2 with S,flow_matching,0.3,2.0,38,146
84,add,0.0,c,,CCS[CH],cCCS[CH],8,add c at position 0,flow_matching,0.3,2.0,38,146
85,remove,2.0,C,,cCCS[CH],cCS[CH],7,remove C from position 2,flow_matching,0.3,2.0,38,146
86,replace,2.0,3,S,cCS[CH],cC3[CH],7,replace S at position 2 with 3,flow_matching,0.3,2.0,38,146
87,replace,2.0,l,3,cC3[CH],cCl[CH],7,replace 3 at position 2 with l,flow_matching,0.3,2.0,38,146
88,replace,0.0,C,c,cCl[CH],CCl[CH],7,replace c at position 0 with C,flow_matching,0.3,2.0,38,146
89,replace,2.0,C,l,CCl[CH],CCC[CH],7,replace l at position 2 with C,flow_matching,0.3,2.0,38,146
90,remove,4.0,C,,CCC[CH],CCC[H],6,remove C from position 4,flow_matching,0.3,2.0,38,146
91,replace,4.0,C,H,CCC[H],CCC[C],6,replace H at position 4 with C,flow_matching,0.3,2.0,38,146
92,replace,5.0,2,],CCC[C],CCC[C2,6,replace ] at position 5 with 2,flow_matching,0.3,2.0,38,146
93,replace,5.0,@,2,CCC[C2,CCC[C@,6,replace 2 at position 5 with @,flow_matching,0.3,2.0,38,146
94,replace,1.0,6,C,CCC[C@,C6C[C@,6,replace C at position 1 with 6,flow_matching,0.3,2.0,38,146
95,replace,1.0,C,6,C6C[C@,CCC[C@,6,replace 6 at position 1 with C,flow_matching,0.3,2.0,38,146
96,remove,4.0,C,,CCC[C@,CCC[@,5,remove C from position 4,flow_matching,0.3,2.0,38,146
97,replace,4.0,l,@,CCC[@,CCC[l,5,replace @ at position 4 with l,flow_matching,0.3,2.0,38,146
98,add,2.0,N,,CCC[l,CCNC[l,6,add N at position 2,flow_matching,0.3,2.0,38,146
99,replace,2.0,C,N,CCNC[l,CCCC[l,6,replace N at position 2 with C,flow_matching,0.3,2.0,38,146
100,replace,3.0,[,C,CCCC[l,CCC[[l,6,replace C at position 3 with [,flow_matching,0.3,2.0,38,146
101,add,6.0,#,,CCC[[l,CCC[[l#,7,add # at position 6,flow_matching,0.3,2.0,38,146
102,replace,4.0,2,[,CCC[[l#,CCC[2l#,7,replace [ at position 4 with 2,flow_matching,0.3,2.0,38,146
103,add,3.0,o,,CCC[2l#,CCCo[2l#,8,add o at position 3,flow_matching,0.3,2.0,38,146
104,replace,5.0,I,2,CCCo[2l#,CCCo[Il#,8,replace 2 at position 5 with I,flow_matching,0.3,2.0,38,146
105,remove,6.0,l,,CCCo[Il#,CCCo[I#,7,remove l from position 6,flow_matching,0.3,2.0,38,146
106,replace,1.0,3,C,CCCo[I#,C3Co[I#,7,replace C at position 1 with 3,flow_matching,0.3,2.0,38,146
107,add,1.0,-,,C3Co[I#,C-3Co[I#,8,add - at position 1,flow_matching,0.3,2.0,38,146
108,replace,1.0,C,-,C-3Co[I#,CC3Co[I#,8,replace - at position 1 with C,flow_matching,0.3,2.0,38,146
109,replace,0.0,c,C,CC3Co[I#,cC3Co[I#,8,replace C at position 0 with c,flow_matching,0.3,2.0,38,146
110,replace,0.0,C,c,cC3Co[I#,CC3Co[I#,8,replace c at position 0 with C,flow_matching,0.3,2.0,38,146
111,remove,4.0,o,,CC3Co[I#,CC3C[I#,7,remove o from position 4,flow_matching,0.3,2.0,38,146
112,remove,2.0,3,,CC3C[I#,CCC[I#,6,remove 3 from position 2,flow_matching,0.3,2.0,38,146
113,replace,4.0,C,I,CCC[I#,CCC[C#,6,replace I at position 4 with C,flow_matching,0.3,2.0,38,146
114,replace,5.0,@,#,CCC[C#,CCC[C@,6,replace # at position 5 with @,flow_matching,0.3,2.0,38,146
115,add,6.0,H,,CCC[C@,CCC[C@H,7,add H at position 6,flow_matching,0.3,2.0,38,146
116,add,7.0,],,CCC[C@H,CCC[C@H],8,add ] at position 7,flow_matching,0.3,2.0,38,146
117,add,8.0,(,,CCC[C@H],CCC[C@H](,9,add ( at position 8,flow_matching,0.3,2.0,38,146
118,add,9.0,C,,CCC[C@H](,CCC[C@H](C,10,add C at position 9,flow_matching,0.3,2.0,38,146
119,add,10.0,),,CCC[C@H](C,CCC[C@H](C),11,add ) at position 10,flow_matching,0.3,2.0,38,146
120,add,11.0,C,,CCC[C@H](C),CCC[C@H](C)C,12,add C at position 11,flow_matching,0.3,2.0,38,146
121,add,12.0,(,,CCC[C@H](C)C,CCC[C@H](C)C(,13,add ( at position 12,flow_matching,0.3,2.0,38,146
122,add,13.0,=,,CCC[C@H](C)C(,CCC[C@H](C)C(=,14,add = at position 13,flow_matching,0.3,2.0,38,146
123,add,14.0,O,,CCC[C@H](C)C(=,CCC[C@H](C)C(=O,15,add O at position 14,flow_matching,0.3,2.0,38,146
124,add,15.0,),,CCC[C@H](C)C(=O,CCC[C@H](C)C(=O),16,add ) at position 15,flow_matching,0.3,2.0,38,146
125,add,16.0,N,,CCC[C@H](C)C(=O),CCC[C@H](C)C(=O)N,17,add N at position 16,flow_matching,0.3,2.0,38,146
126,add,17.0,[,,CCC[C@H](C)C(=O)N,CCC[C@H](C)C(=O)N[,18,add [ at position 17,flow_matching,0.3,2.0,38,146
127,add,18.0,C,,CCC[C@H](C)C(=O)N[,CCC[C@H](C)C(=O)N[C,19,add C at position 18,flow_matching,0.3,2.0,38,146
128,add,19.0,@,,CCC[C@H](C)C(=O)N[C,CCC[C@H](C)C(=O)N[C@,20,add @ at position 19,flow_matching,0.3,2.0,38,146
129,add,20.0,H,,CCC[C@H](C)C(=O)N[C@,CCC[C@H](C)C(=O)N[C@H,21,add H at position 20,flow_matching,0.3,2.0,38,146
130,add,21.0,],,CCC[C@H](C)C(=O)N[C@H,CCC[C@H](C)C(=O)N[C@H],22,add ] at position 21,flow_matching,0.3,2.0,38,146
131,add,22.0,(,,CCC[C@H](C)C(=O)N[C@H],CCC[C@H](C)C(=O)N[C@H](,23,add ( at position 22,flow_matching,0.3,2.0,38,146
132,add,23.0,C,,CCC[C@H](C)C(=O)N[C@H](,CCC[C@H](C)C(=O)N[C@H](C,24,add C at position 23,flow_matching,0.3,2.0,38,146
133,add,24.0,),,CCC[C@H](C)C(=O)N[C@H](C,CCC[C@H](C)C(=O)N[C@H](C),25,add ) at position 24,flow_matching,0.3,2.0,38,146
134,add,25.0,c,,CCC[C@H](C)C(=O)N[C@H](C),CCC[C@H](C)C(=O)N[C@H](C)c,26,add c at position 25,flow_matching,0.3,2.0,38,146
135,add,26.0,1,,CCC[C@H](C)C(=O)N[C@H](C)c,CCC[C@H](C)C(=O)N[C@H](C)c1,27,add 1 at position 26,flow_matching,0.3,2.0,38,146
136,add,27.0,c,,CCC[C@H](C)C(=O)N[C@H](C)c1,CCC[C@H](C)C(=O)N[C@H](C)c1c,28,add c at position 27,flow_matching,0.3,2.0,38,146
137,add,28.0,c,,CCC[C@H](C)C(=O)N[C@H](C)c1c,CCC[C@H](C)C(=O)N[C@H](C)c1cc,29,add c at position 28,flow_matching,0.3,2.0,38,146
138,add,29.0,c,,CCC[C@H](C)C(=O)N[C@H](C)c1cc,CCC[C@H](C)C(=O)N[C@H](C)c1ccc,30,add c at position 29,flow_matching,0.3,2.0,38,146
139,add,30.0,c,,CCC[C@H](C)C(=O)N[C@H](C)c1ccc,CCC[C@H](C)C(=O)N[C@H](C)c1cccc,31,add c at position 30,flow_matching,0.3,2.0,38,146
140,add,31.0,(,,CCC[C@H](C)C(=O)N[C@H](C)c1cccc,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(,32,add ( at position 31,flow_matching,0.3,2.0,38,146
141,add,32.0,B,,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(B,33,add B at position 32,flow_matching,0.3,2.0,38,146
142,add,33.0,r,,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(B,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(Br,34,add r at position 33,flow_matching,0.3,2.0,38,146
143,add,34.0,),,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(Br,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(Br),35,add ) at position 34,flow_matching,0.3,2.0,38,146
144,add,35.0,c,,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(Br),CCC[C@H](C)C(=O)N[C@H](C)c1cccc(Br)c,36,add c at position 35,flow_matching,0.3,2.0,38,146
145,add,36.0,1,,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(Br)c,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(Br)c1,37,add 1 at position 36,flow_matching,0.3,2.0,38,146
146,add,37.0,"
",,CCC[C@H](C)C(=O)N[C@H](C)c1cccc(Br)c1,"CCC[C@H](C)C(=O)N[C@H](C)c1cccc(Br)c1
",38,"add 
 at position 37",flow_matching,0.3,2.0,38,146
