step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,53,123
1,add,0.0,H,,,H,1,add H at position 0,flow_matching,0.3,2.0,53,123
2,replace,0.0,6,H,H,6,1,replace H at position 0 with 6,flow_matching,0.3,2.0,53,123
3,add,0.0,-,,6,-6,2,add - at position 0,flow_matching,0.3,2.0,53,123
4,replace,1.0,),6,-6,-),2,replace 6 at position 1 with ),flow_matching,0.3,2.0,53,123
5,replace,0.0,C,-,-),C),2,replace - at position 0 with C,flow_matching,0.3,2.0,53,123
6,add,2.0,B,,C),C)B,3,add B at position 2,flow_matching,0.3,2.0,53,123
7,replace,1.0,[,),C)B,C[B,3,replace ) at position 1 with [,flow_matching,0.3,2.0,53,123
8,add,1.0,o,,C[B,Co[B,4,add o at position 1,flow_matching,0.3,2.0,53,123
9,add,1.0,N,,Co[B,CNo[B,5,add N at position 1,flow_matching,0.3,2.0,53,123
10,replace,1.0,\,N,CNo[B,C\o[B,5,replace N at position 1 with \,flow_matching,0.3,2.0,53,123
11,replace,3.0,c,[,C\o[B,C\ocB,5,replace [ at position 3 with c,flow_matching,0.3,2.0,53,123
12,remove,3.0,c,,C\ocB,C\oB,4,remove c from position 3,flow_matching,0.3,2.0,53,123
13,replace,1.0,[,\,C\oB,C[oB,4,replace \ at position 1 with [,flow_matching,0.3,2.0,53,123
14,replace,2.0,C,o,C[oB,C[CB,4,replace o at position 2 with C,flow_matching,0.3,2.0,53,123
15,replace,2.0,-,C,C[CB,C[-B,4,replace C at position 2 with -,flow_matching,0.3,2.0,53,123
16,add,2.0,N,,C[-B,C[N-B,5,add N at position 2,flow_matching,0.3,2.0,53,123
17,add,0.0,7,,C[N-B,7C[N-B,6,add 7 at position 0,flow_matching,0.3,2.0,53,123
18,add,5.0,3,,7C[N-B,7C[N-3B,7,add 3 at position 5,flow_matching,0.3,2.0,53,123
19,replace,0.0,\,7,7C[N-3B,\C[N-3B,7,replace 7 at position 0 with \,flow_matching,0.3,2.0,53,123
20,add,1.0,2,,\C[N-3B,\2C[N-3B,8,add 2 at position 1,flow_matching,0.3,2.0,53,123
21,add,1.0,],,\2C[N-3B,\]2C[N-3B,9,add ] at position 1,flow_matching,0.3,2.0,53,123
22,add,4.0,B,,\]2C[N-3B,\]2CB[N-3B,10,add B at position 4,flow_matching,0.3,2.0,53,123
23,add,9.0,l,,\]2CB[N-3B,\]2CB[N-3lB,11,add l at position 9,flow_matching,0.3,2.0,53,123
24,remove,7.0,-,,\]2CB[N-3lB,\]2CB[N3lB,10,remove - from position 7,flow_matching,0.3,2.0,53,123
25,remove,1.0,],,\]2CB[N3lB,\2CB[N3lB,9,remove ] from position 1,flow_matching,0.3,2.0,53,123
26,remove,6.0,3,,\2CB[N3lB,\2CB[NlB,8,remove 3 from position 6,flow_matching,0.3,2.0,53,123
27,replace,0.0,C,\,\2CB[NlB,C2CB[NlB,8,replace \ at position 0 with C,flow_matching,0.3,2.0,53,123
28,add,5.0,5,,C2CB[NlB,C2CB[5NlB,9,add 5 at position 5,flow_matching,0.3,2.0,53,123
29,replace,1.0,[,2,C2CB[5NlB,C[CB[5NlB,9,replace 2 at position 1 with [,flow_matching,0.3,2.0,53,123
30,add,6.0,S,,C[CB[5NlB,C[CB[5SNlB,10,add S at position 6,flow_matching,0.3,2.0,53,123
31,replace,3.0,@,B,C[CB[5SNlB,C[C@[5SNlB,10,replace B at position 3 with @,flow_matching,0.3,2.0,53,123
32,add,9.0,F,,C[C@[5SNlB,C[C@[5SNlFB,11,add F at position 9,flow_matching,0.3,2.0,53,123
33,remove,4.0,[,,C[C@[5SNlFB,C[C@5SNlFB,10,remove [ from position 4,flow_matching,0.3,2.0,53,123
34,replace,4.0,H,5,C[C@5SNlFB,C[C@HSNlFB,10,replace 5 at position 4 with H,flow_matching,0.3,2.0,53,123
35,add,7.0,),,C[C@HSNlFB,C[C@HSN)lFB,11,add ) at position 7,flow_matching,0.3,2.0,53,123
36,remove,6.0,N,,C[C@HSN)lFB,C[C@HS)lFB,10,remove N from position 6,flow_matching,0.3,2.0,53,123
37,replace,5.0,F,S,C[C@HS)lFB,C[C@HF)lFB,10,replace S at position 5 with F,flow_matching,0.3,2.0,53,123
38,add,6.0,c,,C[C@HF)lFB,C[C@HFc)lFB,11,add c at position 6,flow_matching,0.3,2.0,53,123
39,replace,3.0,F,@,C[C@HFc)lFB,C[CFHFc)lFB,11,replace @ at position 3 with F,flow_matching,0.3,2.0,53,123
40,replace,3.0,@,F,C[CFHFc)lFB,C[C@HFc)lFB,11,replace F at position 3 with @,flow_matching,0.3,2.0,53,123
41,remove,1.0,[,,C[C@HFc)lFB,CC@HFc)lFB,10,remove [ from position 1,flow_matching,0.3,2.0,53,123
42,replace,1.0,[,C,CC@HFc)lFB,C[@HFc)lFB,10,replace C at position 1 with [,flow_matching,0.3,2.0,53,123
43,replace,2.0,C,@,C[@HFc)lFB,C[CHFc)lFB,10,replace @ at position 2 with C,flow_matching,0.3,2.0,53,123
44,add,1.0,I,,C[CHFc)lFB,CI[CHFc)lFB,11,add I at position 1,flow_matching,0.3,2.0,53,123
45,add,4.0,I,,CI[CHFc)lFB,CI[CIHFc)lFB,12,add I at position 4,flow_matching,0.3,2.0,53,123
46,replace,11.0,5,B,CI[CIHFc)lFB,CI[CIHFc)lF5,12,replace B at position 11 with 5,flow_matching,0.3,2.0,53,123
47,replace,1.0,[,I,CI[CIHFc)lF5,C[[CIHFc)lF5,12,replace I at position 1 with [,flow_matching,0.3,2.0,53,123
48,replace,3.0,(,C,C[[CIHFc)lF5,C[[(IHFc)lF5,12,replace C at position 3 with (,flow_matching,0.3,2.0,53,123
49,replace,2.0,C,[,C[[(IHFc)lF5,C[C(IHFc)lF5,12,replace [ at position 2 with C,flow_matching,0.3,2.0,53,123
50,add,12.0,o,,C[C(IHFc)lF5,C[C(IHFc)lF5o,13,add o at position 12,flow_matching,0.3,2.0,53,123
51,replace,3.0,@,(,C[C(IHFc)lF5o,C[C@IHFc)lF5o,13,replace ( at position 3 with @,flow_matching,0.3,2.0,53,123
52,replace,4.0,H,I,C[C@IHFc)lF5o,C[C@HHFc)lF5o,13,replace I at position 4 with H,flow_matching,0.3,2.0,53,123
53,replace,5.0,],H,C[C@HHFc)lF5o,C[C@H]Fc)lF5o,13,replace H at position 5 with ],flow_matching,0.3,2.0,53,123
54,replace,9.0,-,l,C[C@H]Fc)lF5o,C[C@H]Fc)-F5o,13,replace l at position 9 with -,flow_matching,0.3,2.0,53,123
55,replace,7.0,r,c,C[C@H]Fc)-F5o,C[C@H]Fr)-F5o,13,replace c at position 7 with r,flow_matching,0.3,2.0,53,123
56,replace,6.0,(,F,C[C@H]Fr)-F5o,C[C@H](r)-F5o,13,replace F at position 6 with (,flow_matching,0.3,2.0,53,123
57,replace,7.0,c,r,C[C@H](r)-F5o,C[C@H](c)-F5o,13,replace r at position 7 with c,flow_matching,0.3,2.0,53,123
58,replace,3.0,O,@,C[C@H](c)-F5o,C[COH](c)-F5o,13,replace @ at position 3 with O,flow_matching,0.3,2.0,53,123
59,add,3.0,+,,C[COH](c)-F5o,C[C+OH](c)-F5o,14,add + at position 3,flow_matching,0.3,2.0,53,123
60,replace,3.0,@,+,C[C+OH](c)-F5o,C[C@OH](c)-F5o,14,replace + at position 3 with @,flow_matching,0.3,2.0,53,123
61,add,12.0,7,,C[C@OH](c)-F5o,C[C@OH](c)-F75o,15,add 7 at position 12,flow_matching,0.3,2.0,53,123
62,replace,4.0,H,O,C[C@OH](c)-F75o,C[C@HH](c)-F75o,15,replace O at position 4 with H,flow_matching,0.3,2.0,53,123
63,remove,1.0,[,,C[C@HH](c)-F75o,CC@HH](c)-F75o,14,remove [ from position 1,flow_matching,0.3,2.0,53,123
64,add,9.0,@,,CC@HH](c)-F75o,CC@HH](c)@-F75o,15,add @ at position 9,flow_matching,0.3,2.0,53,123
65,replace,1.0,[,C,CC@HH](c)@-F75o,C[@HH](c)@-F75o,15,replace C at position 1 with [,flow_matching,0.3,2.0,53,123
66,add,14.0,\,,C[@HH](c)@-F75o,C[@HH](c)@-F75\o,16,add \ at position 14,flow_matching,0.3,2.0,53,123
67,replace,11.0,r,F,C[@HH](c)@-F75\o,C[@HH](c)@-r75\o,16,replace F at position 11 with r,flow_matching,0.3,2.0,53,123
68,replace,3.0,4,H,C[@HH](c)@-r75\o,C[@4H](c)@-r75\o,16,replace H at position 3 with 4,flow_matching,0.3,2.0,53,123
69,add,3.0,[,,C[@4H](c)@-r75\o,C[@[4H](c)@-r75\o,17,add [ at position 3,flow_matching,0.3,2.0,53,123
70,add,12.0,o,,C[@[4H](c)@-r75\o,C[@[4H](c)@-or75\o,18,add o at position 12,flow_matching,0.3,2.0,53,123
71,add,9.0,2,,C[@[4H](c)@-or75\o,C[@[4H](c2)@-or75\o,19,add 2 at position 9,flow_matching,0.3,2.0,53,123
72,replace,2.0,C,@,C[@[4H](c2)@-or75\o,C[C[4H](c2)@-or75\o,19,replace @ at position 2 with C,flow_matching,0.3,2.0,53,123
73,replace,3.0,@,[,C[C[4H](c2)@-or75\o,C[C@4H](c2)@-or75\o,19,replace [ at position 3 with @,flow_matching,0.3,2.0,53,123
74,replace,4.0,H,4,C[C@4H](c2)@-or75\o,C[C@HH](c2)@-or75\o,19,replace 4 at position 4 with H,flow_matching,0.3,2.0,53,123
75,remove,10.0,),,C[C@HH](c2)@-or75\o,C[C@HH](c2@-or75\o,18,remove ) from position 10,flow_matching,0.3,2.0,53,123
76,replace,5.0,],H,C[C@HH](c2@-or75\o,C[C@H]](c2@-or75\o,18,replace H at position 5 with ],flow_matching,0.3,2.0,53,123
77,replace,6.0,(,],C[C@H]](c2@-or75\o,C[C@H]((c2@-or75\o,18,replace ] at position 6 with (,flow_matching,0.3,2.0,53,123
78,replace,7.0,c,(,C[C@H]((c2@-or75\o,C[C@H](cc2@-or75\o,18,replace ( at position 7 with c,flow_matching,0.3,2.0,53,123
79,replace,8.0,1,c,C[C@H](cc2@-or75\o,C[C@H](c12@-or75\o,18,replace c at position 8 with 1,flow_matching,0.3,2.0,53,123
80,replace,9.0,n,2,C[C@H](c12@-or75\o,C[C@H](c1n@-or75\o,18,replace 2 at position 9 with n,flow_matching,0.3,2.0,53,123
81,replace,10.0,c,@,C[C@H](c1n@-or75\o,C[C@H](c1nc-or75\o,18,replace @ at position 10 with c,flow_matching,0.3,2.0,53,123
82,replace,11.0,(,-,C[C@H](c1nc-or75\o,C[C@H](c1nc(or75\o,18,replace - at position 11 with (,flow_matching,0.3,2.0,53,123
83,replace,12.0,C,o,C[C@H](c1nc(or75\o,C[C@H](c1nc(Cr75\o,18,replace o at position 12 with C,flow_matching,0.3,2.0,53,123
84,replace,13.0,(,r,C[C@H](c1nc(Cr75\o,C[C@H](c1nc(C(75\o,18,replace r at position 13 with (,flow_matching,0.3,2.0,53,123
85,replace,14.0,C,7,C[C@H](c1nc(C(75\o,C[C@H](c1nc(C(C5\o,18,replace 7 at position 14 with C,flow_matching,0.3,2.0,53,123
86,replace,15.0,),5,C[C@H](c1nc(C(C5\o,C[C@H](c1nc(C(C)\o,18,replace 5 at position 15 with ),flow_matching,0.3,2.0,53,123
87,replace,16.0,(,\,C[C@H](c1nc(C(C)\o,C[C@H](c1nc(C(C)(o,18,replace \ at position 16 with (,flow_matching,0.3,2.0,53,123
88,replace,17.0,C,o,C[C@H](c1nc(C(C)(o,C[C@H](c1nc(C(C)(C,18,replace o at position 17 with C,flow_matching,0.3,2.0,53,123
89,add,18.0,),,C[C@H](c1nc(C(C)(C,C[C@H](c1nc(C(C)(C),19,add ) at position 18,flow_matching,0.3,2.0,53,123
90,add,19.0,C,,C[C@H](c1nc(C(C)(C),C[C@H](c1nc(C(C)(C)C,20,add C at position 19,flow_matching,0.3,2.0,53,123
91,add,20.0,),,C[C@H](c1nc(C(C)(C)C,C[C@H](c1nc(C(C)(C)C),21,add ) at position 20,flow_matching,0.3,2.0,53,123
92,add,21.0,n,,C[C@H](c1nc(C(C)(C)C),C[C@H](c1nc(C(C)(C)C)n,22,add n at position 21,flow_matching,0.3,2.0,53,123
93,add,22.0,o,,C[C@H](c1nc(C(C)(C)C)n,C[C@H](c1nc(C(C)(C)C)no,23,add o at position 22,flow_matching,0.3,2.0,53,123
94,add,23.0,1,,C[C@H](c1nc(C(C)(C)C)no,C[C@H](c1nc(C(C)(C)C)no1,24,add 1 at position 23,flow_matching,0.3,2.0,53,123
95,add,24.0,),,C[C@H](c1nc(C(C)(C)C)no1,C[C@H](c1nc(C(C)(C)C)no1),25,add ) at position 24,flow_matching,0.3,2.0,53,123
96,add,25.0,[,,C[C@H](c1nc(C(C)(C)C)no1),C[C@H](c1nc(C(C)(C)C)no1)[,26,add [ at position 25,flow_matching,0.3,2.0,53,123
97,add,26.0,S,,C[C@H](c1nc(C(C)(C)C)no1)[,C[C@H](c1nc(C(C)(C)C)no1)[S,27,add S at position 26,flow_matching,0.3,2.0,53,123
98,add,27.0,@,,C[C@H](c1nc(C(C)(C)C)no1)[S,C[C@H](c1nc(C(C)(C)C)no1)[S@,28,add @ at position 27,flow_matching,0.3,2.0,53,123
99,add,28.0,],,C[C@H](c1nc(C(C)(C)C)no1)[S@,C[C@H](c1nc(C(C)(C)C)no1)[S@],29,add ] at position 28,flow_matching,0.3,2.0,53,123
100,add,29.0,(,,C[C@H](c1nc(C(C)(C)C)no1)[S@],C[C@H](c1nc(C(C)(C)C)no1)[S@](,30,add ( at position 29,flow_matching,0.3,2.0,53,123
101,add,30.0,=,,C[C@H](c1nc(C(C)(C)C)no1)[S@](,C[C@H](c1nc(C(C)(C)C)no1)[S@](=,31,add = at position 30,flow_matching,0.3,2.0,53,123
102,add,31.0,O,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O,32,add O at position 31,flow_matching,0.3,2.0,53,123
103,add,32.0,),,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O),33,add ) at position 32,flow_matching,0.3,2.0,53,123
104,add,33.0,C,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O),C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)C,34,add C at position 33,flow_matching,0.3,2.0,53,123
105,add,34.0,c,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)C,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc,35,add c at position 34,flow_matching,0.3,2.0,53,123
106,add,35.0,1,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1,36,add 1 at position 35,flow_matching,0.3,2.0,53,123
107,add,36.0,n,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1n,37,add n at position 36,flow_matching,0.3,2.0,53,123
108,add,37.0,c,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1n,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1nc,38,add c at position 37,flow_matching,0.3,2.0,53,123
109,add,38.0,n,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1nc,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn,39,add n at position 38,flow_matching,0.3,2.0,53,123
110,add,39.0,(,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(,40,add ( at position 39,flow_matching,0.3,2.0,53,123
111,add,40.0,-,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-,41,add - at position 40,flow_matching,0.3,2.0,53,123
112,add,41.0,c,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c,42,add c at position 41,flow_matching,0.3,2.0,53,123
113,add,42.0,2,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2,43,add 2 at position 42,flow_matching,0.3,2.0,53,123
114,add,43.0,c,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2c,44,add c at position 43,flow_matching,0.3,2.0,53,123
115,add,44.0,c,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2c,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2cc,45,add c at position 44,flow_matching,0.3,2.0,53,123
116,add,45.0,c,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2cc,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccc,46,add c at position 45,flow_matching,0.3,2.0,53,123
117,add,46.0,c,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccc,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2cccc,47,add c at position 46,flow_matching,0.3,2.0,53,123
118,add,47.0,c,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2cccc,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc,48,add c at position 47,flow_matching,0.3,2.0,53,123
119,add,48.0,2,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc2,49,add 2 at position 48,flow_matching,0.3,2.0,53,123
120,add,49.0,),,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc2,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc2),50,add ) at position 49,flow_matching,0.3,2.0,53,123
121,add,50.0,n,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc2),C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc2)n,51,add n at position 50,flow_matching,0.3,2.0,53,123
122,add,51.0,1,,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc2)n,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc2)n1,52,add 1 at position 51,flow_matching,0.3,2.0,53,123
123,add,52.0,"
",,C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc2)n1,"C[C@H](c1nc(C(C)(C)C)no1)[S@](=O)Cc1ncn(-c2ccccc2)n1
",53,"add 
 at position 52",flow_matching,0.3,2.0,53,123
