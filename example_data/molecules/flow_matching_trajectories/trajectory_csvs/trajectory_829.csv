step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,60,241
1,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,60,241
2,remove,0.0,N,,N,,0,remove N from position 0,flow_matching,0.3,2.0,60,241
3,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,60,241
4,replace,0.0,C,/,/,C,1,replace / at position 0 with C,flow_matching,0.3,2.0,60,241
5,add,1.0,5,,C,C5,2,add 5 at position 1,flow_matching,0.3,2.0,60,241
6,replace,1.0,[,5,C5,C[,2,replace 5 at position 1 with [,flow_matching,0.3,2.0,60,241
7,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,60,241
8,remove,2.0,C,,C[C,C[,2,remove C from position 2,flow_matching,0.3,2.0,60,241
9,add,1.0,/,,C[,C/[,3,add / at position 1,flow_matching,0.3,2.0,60,241
10,remove,0.0,C,,C/[,/[,2,remove C from position 0,flow_matching,0.3,2.0,60,241
11,remove,1.0,[,,/[,/,1,remove [ from position 1,flow_matching,0.3,2.0,60,241
12,add,0.0,N,,/,N/,2,add N at position 0,flow_matching,0.3,2.0,60,241
13,replace,0.0,C,N,N/,C/,2,replace N at position 0 with C,flow_matching,0.3,2.0,60,241
14,remove,1.0,/,,C/,C,1,remove / from position 1,flow_matching,0.3,2.0,60,241
15,add,0.0,I,,C,IC,2,add I at position 0,flow_matching,0.3,2.0,60,241
16,replace,0.0,C,I,IC,CC,2,replace I at position 0 with C,flow_matching,0.3,2.0,60,241
17,replace,1.0,[,C,CC,C[,2,replace C at position 1 with [,flow_matching,0.3,2.0,60,241
18,remove,0.0,C,,C[,[,1,remove C from position 0,flow_matching,0.3,2.0,60,241
19,replace,0.0,C,[,[,C,1,replace [ at position 0 with C,flow_matching,0.3,2.0,60,241
20,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,60,241
21,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,60,241
22,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,60,241
23,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,60,241
24,add,0.0,6,,C,6C,2,add 6 at position 0,flow_matching,0.3,2.0,60,241
25,replace,0.0,C,6,6C,CC,2,replace 6 at position 0 with C,flow_matching,0.3,2.0,60,241
26,replace,0.0,\,C,CC,\C,2,replace C at position 0 with \,flow_matching,0.3,2.0,60,241
27,replace,0.0,F,\,\C,FC,2,replace \ at position 0 with F,flow_matching,0.3,2.0,60,241
28,replace,0.0,C,F,FC,CC,2,replace F at position 0 with C,flow_matching,0.3,2.0,60,241
29,add,1.0,-,,CC,C-C,3,add - at position 1,flow_matching,0.3,2.0,60,241
30,add,1.0,o,,C-C,Co-C,4,add o at position 1,flow_matching,0.3,2.0,60,241
31,add,3.0,],,Co-C,Co-]C,5,add ] at position 3,flow_matching,0.3,2.0,60,241
32,replace,1.0,[,o,Co-]C,C[-]C,5,replace o at position 1 with [,flow_matching,0.3,2.0,60,241
33,replace,2.0,C,-,C[-]C,C[C]C,5,replace - at position 2 with C,flow_matching,0.3,2.0,60,241
34,replace,3.0,@,],C[C]C,C[C@C,5,replace ] at position 3 with @,flow_matching,0.3,2.0,60,241
35,replace,4.0,@,C,C[C@C,C[C@@,5,replace C at position 4 with @,flow_matching,0.3,2.0,60,241
36,add,0.0,#,,C[C@@,#C[C@@,6,add # at position 0,flow_matching,0.3,2.0,60,241
37,add,5.0,N,,#C[C@@,#C[C@N@,7,add N at position 5,flow_matching,0.3,2.0,60,241
38,remove,1.0,C,,#C[C@N@,#[C@N@,6,remove C from position 1,flow_matching,0.3,2.0,60,241
39,replace,0.0,C,#,#[C@N@,C[C@N@,6,replace # at position 0 with C,flow_matching,0.3,2.0,60,241
40,add,5.0,B,,C[C@N@,C[C@NB@,7,add B at position 5,flow_matching,0.3,2.0,60,241
41,replace,0.0,+,C,C[C@NB@,+[C@NB@,7,replace C at position 0 with +,flow_matching,0.3,2.0,60,241
42,add,3.0,F,,+[C@NB@,+[CF@NB@,8,add F at position 3,flow_matching,0.3,2.0,60,241
43,replace,2.0,r,C,+[CF@NB@,+[rF@NB@,8,replace C at position 2 with r,flow_matching,0.3,2.0,60,241
44,replace,4.0,5,@,+[rF@NB@,+[rF5NB@,8,replace @ at position 4 with 5,flow_matching,0.3,2.0,60,241
45,remove,3.0,F,,+[rF5NB@,+[r5NB@,7,remove F from position 3,flow_matching,0.3,2.0,60,241
46,replace,0.0,C,+,+[r5NB@,C[r5NB@,7,replace + at position 0 with C,flow_matching,0.3,2.0,60,241
47,remove,6.0,@,,C[r5NB@,C[r5NB,6,remove @ from position 6,flow_matching,0.3,2.0,60,241
48,add,1.0,c,,C[r5NB,Cc[r5NB,7,add c at position 1,flow_matching,0.3,2.0,60,241
49,remove,5.0,N,,Cc[r5NB,Cc[r5B,6,remove N from position 5,flow_matching,0.3,2.0,60,241
50,replace,3.0,4,r,Cc[r5B,Cc[45B,6,replace r at position 3 with 4,flow_matching,0.3,2.0,60,241
51,replace,1.0,[,c,Cc[45B,C[[45B,6,replace c at position 1 with [,flow_matching,0.3,2.0,60,241
52,add,1.0,),,C[[45B,C)[[45B,7,add ) at position 1,flow_matching,0.3,2.0,60,241
53,replace,1.0,[,),C)[[45B,C[[[45B,7,replace ) at position 1 with [,flow_matching,0.3,2.0,60,241
54,add,7.0,7,,C[[[45B,C[[[45B7,8,add 7 at position 7,flow_matching,0.3,2.0,60,241
55,replace,2.0,C,[,C[[[45B7,C[C[45B7,8,replace [ at position 2 with C,flow_matching,0.3,2.0,60,241
56,replace,6.0,N,B,C[C[45B7,C[C[45N7,8,replace B at position 6 with N,flow_matching,0.3,2.0,60,241
57,remove,0.0,C,,C[C[45N7,[C[45N7,7,remove C from position 0,flow_matching,0.3,2.0,60,241
58,remove,1.0,C,,[C[45N7,[[45N7,6,remove C from position 1,flow_matching,0.3,2.0,60,241
59,replace,1.0,C,[,[[45N7,[C45N7,6,replace [ at position 1 with C,flow_matching,0.3,2.0,60,241
60,replace,0.0,C,[,[C45N7,CC45N7,6,replace [ at position 0 with C,flow_matching,0.3,2.0,60,241
61,remove,1.0,C,,CC45N7,C45N7,5,remove C from position 1,flow_matching,0.3,2.0,60,241
62,remove,3.0,N,,C45N7,C457,4,remove N from position 3,flow_matching,0.3,2.0,60,241
63,replace,2.0,#,5,C457,C4#7,4,replace 5 at position 2 with #,flow_matching,0.3,2.0,60,241
64,replace,0.0,2,C,C4#7,24#7,4,replace C at position 0 with 2,flow_matching,0.3,2.0,60,241
65,remove,2.0,#,,24#7,247,3,remove # from position 2,flow_matching,0.3,2.0,60,241
66,remove,1.0,4,,247,27,2,remove 4 from position 1,flow_matching,0.3,2.0,60,241
67,replace,0.0,(,2,27,(7,2,replace 2 at position 0 with (,flow_matching,0.3,2.0,60,241
68,remove,0.0,(,,(7,7,1,remove ( from position 0,flow_matching,0.3,2.0,60,241
69,add,1.0,\,,7,7\,2,add \ at position 1,flow_matching,0.3,2.0,60,241
70,add,0.0,F,,7\,F7\,3,add F at position 0,flow_matching,0.3,2.0,60,241
71,add,2.0,O,,F7\,F7O\,4,add O at position 2,flow_matching,0.3,2.0,60,241
72,replace,0.0,C,F,F7O\,C7O\,4,replace F at position 0 with C,flow_matching,0.3,2.0,60,241
73,remove,3.0,\,,C7O\,C7O,3,remove \ from position 3,flow_matching,0.3,2.0,60,241
74,replace,1.0,[,7,C7O,C[O,3,replace 7 at position 1 with [,flow_matching,0.3,2.0,60,241
75,add,0.0,o,,C[O,oC[O,4,add o at position 0,flow_matching,0.3,2.0,60,241
76,replace,1.0,=,C,oC[O,o=[O,4,replace C at position 1 with =,flow_matching,0.3,2.0,60,241
77,replace,0.0,C,o,o=[O,C=[O,4,replace o at position 0 with C,flow_matching,0.3,2.0,60,241
78,replace,1.0,7,=,C=[O,C7[O,4,replace = at position 1 with 7,flow_matching,0.3,2.0,60,241
79,replace,1.0,[,7,C7[O,C[[O,4,replace 7 at position 1 with [,flow_matching,0.3,2.0,60,241
80,add,2.0,4,,C[[O,C[4[O,5,add 4 at position 2,flow_matching,0.3,2.0,60,241
81,add,1.0,I,,C[4[O,CI[4[O,6,add I at position 1,flow_matching,0.3,2.0,60,241
82,add,0.0,O,,CI[4[O,OCI[4[O,7,add O at position 0,flow_matching,0.3,2.0,60,241
83,remove,1.0,C,,OCI[4[O,OI[4[O,6,remove C from position 1,flow_matching,0.3,2.0,60,241
84,replace,5.0,3,O,OI[4[O,OI[4[3,6,replace O at position 5 with 3,flow_matching,0.3,2.0,60,241
85,replace,1.0,3,I,OI[4[3,O3[4[3,6,replace I at position 1 with 3,flow_matching,0.3,2.0,60,241
86,replace,5.0,O,3,O3[4[3,O3[4[O,6,replace 3 at position 5 with O,flow_matching,0.3,2.0,60,241
87,replace,0.0,),O,O3[4[O,)3[4[O,6,replace O at position 0 with ),flow_matching,0.3,2.0,60,241
88,replace,5.0,S,O,)3[4[O,)3[4[S,6,replace O at position 5 with S,flow_matching,0.3,2.0,60,241
89,remove,4.0,[,,)3[4[S,)3[4S,5,remove [ from position 4,flow_matching,0.3,2.0,60,241
90,add,3.0,#,,)3[4S,)3[#4S,6,add # at position 3,flow_matching,0.3,2.0,60,241
91,replace,0.0,C,),)3[#4S,C3[#4S,6,replace ) at position 0 with C,flow_matching,0.3,2.0,60,241
92,replace,2.0,I,[,C3[#4S,C3I#4S,6,replace [ at position 2 with I,flow_matching,0.3,2.0,60,241
93,remove,4.0,4,,C3I#4S,C3I#S,5,remove 4 from position 4,flow_matching,0.3,2.0,60,241
94,replace,4.0,N,S,C3I#S,C3I#N,5,replace S at position 4 with N,flow_matching,0.3,2.0,60,241
95,remove,0.0,C,,C3I#N,3I#N,4,remove C from position 0,flow_matching,0.3,2.0,60,241
96,replace,0.0,C,3,3I#N,CI#N,4,replace 3 at position 0 with C,flow_matching,0.3,2.0,60,241
97,add,2.0,+,,CI#N,CI+#N,5,add + at position 2,flow_matching,0.3,2.0,60,241
98,replace,2.0,=,+,CI+#N,CI=#N,5,replace + at position 2 with =,flow_matching,0.3,2.0,60,241
99,replace,1.0,B,I,CI=#N,CB=#N,5,replace I at position 1 with B,flow_matching,0.3,2.0,60,241
100,add,1.0,C,,CB=#N,CCB=#N,6,add C at position 1,flow_matching,0.3,2.0,60,241
101,add,6.0,n,,CCB=#N,CCB=#Nn,7,add n at position 6,flow_matching,0.3,2.0,60,241
102,add,2.0,-,,CCB=#Nn,CC-B=#Nn,8,add - at position 2,flow_matching,0.3,2.0,60,241
103,replace,1.0,[,C,CC-B=#Nn,C[-B=#Nn,8,replace C at position 1 with [,flow_matching,0.3,2.0,60,241
104,remove,4.0,=,,C[-B=#Nn,C[-B#Nn,7,remove = from position 4,flow_matching,0.3,2.0,60,241
105,replace,2.0,5,-,C[-B#Nn,C[5B#Nn,7,replace - at position 2 with 5,flow_matching,0.3,2.0,60,241
106,add,7.0,2,,C[5B#Nn,C[5B#Nn2,8,add 2 at position 7,flow_matching,0.3,2.0,60,241
107,remove,4.0,#,,C[5B#Nn2,C[5BNn2,7,remove # from position 4,flow_matching,0.3,2.0,60,241
108,add,7.0,4,,C[5BNn2,C[5BNn24,8,add 4 at position 7,flow_matching,0.3,2.0,60,241
109,add,2.0,S,,C[5BNn24,C[S5BNn24,9,add S at position 2,flow_matching,0.3,2.0,60,241
110,replace,3.0,O,5,C[S5BNn24,C[SOBNn24,9,replace 5 at position 3 with O,flow_matching,0.3,2.0,60,241
111,replace,2.0,C,S,C[SOBNn24,C[COBNn24,9,replace S at position 2 with C,flow_matching,0.3,2.0,60,241
112,remove,3.0,O,,C[COBNn24,C[CBNn24,8,remove O from position 3,flow_matching,0.3,2.0,60,241
113,add,0.0,s,,C[CBNn24,sC[CBNn24,9,add s at position 0,flow_matching,0.3,2.0,60,241
114,add,4.0,\,,sC[CBNn24,sC[C\BNn24,10,add \ at position 4,flow_matching,0.3,2.0,60,241
115,remove,2.0,[,,sC[C\BNn24,sCC\BNn24,9,remove [ from position 2,flow_matching,0.3,2.0,60,241
116,remove,5.0,N,,sCC\BNn24,sCC\Bn24,8,remove N from position 5,flow_matching,0.3,2.0,60,241
117,replace,0.0,C,s,sCC\Bn24,CCC\Bn24,8,replace s at position 0 with C,flow_matching,0.3,2.0,60,241
118,add,0.0,O,,CCC\Bn24,OCCC\Bn24,9,add O at position 0,flow_matching,0.3,2.0,60,241
119,remove,1.0,C,,OCCC\Bn24,OCC\Bn24,8,remove C from position 1,flow_matching,0.3,2.0,60,241
120,add,5.0,3,,OCC\Bn24,OCC\B3n24,9,add 3 at position 5,flow_matching,0.3,2.0,60,241
121,add,7.0,S,,OCC\B3n24,OCC\B3nS24,10,add S at position 7,flow_matching,0.3,2.0,60,241
122,replace,8.0,I,2,OCC\B3nS24,OCC\B3nSI4,10,replace 2 at position 8 with I,flow_matching,0.3,2.0,60,241
123,replace,0.0,C,O,OCC\B3nSI4,CCC\B3nSI4,10,replace O at position 0 with C,flow_matching,0.3,2.0,60,241
124,replace,3.0,4,\,CCC\B3nSI4,CCC4B3nSI4,10,replace \ at position 3 with 4,flow_matching,0.3,2.0,60,241
125,replace,5.0,1,3,CCC4B3nSI4,CCC4B1nSI4,10,replace 3 at position 5 with 1,flow_matching,0.3,2.0,60,241
126,replace,3.0,s,4,CCC4B1nSI4,CCCsB1nSI4,10,replace 4 at position 3 with s,flow_matching,0.3,2.0,60,241
127,add,8.0,4,,CCCsB1nSI4,CCCsB1nS4I4,11,add 4 at position 8,flow_matching,0.3,2.0,60,241
128,replace,1.0,[,C,CCCsB1nS4I4,C[CsB1nS4I4,11,replace C at position 1 with [,flow_matching,0.3,2.0,60,241
129,remove,6.0,n,,C[CsB1nS4I4,C[CsB1S4I4,10,remove n from position 6,flow_matching,0.3,2.0,60,241
130,add,7.0,N,,C[CsB1S4I4,C[CsB1SN4I4,11,add N at position 7,flow_matching,0.3,2.0,60,241
131,remove,6.0,S,,C[CsB1SN4I4,C[CsB1N4I4,10,remove S from position 6,flow_matching,0.3,2.0,60,241
132,replace,3.0,@,s,C[CsB1N4I4,C[C@B1N4I4,10,replace s at position 3 with @,flow_matching,0.3,2.0,60,241
133,replace,8.0,B,I,C[C@B1N4I4,C[C@B1N4B4,10,replace I at position 8 with B,flow_matching,0.3,2.0,60,241
134,replace,4.0,@,B,C[C@B1N4B4,C[C@@1N4B4,10,replace B at position 4 with @,flow_matching,0.3,2.0,60,241
135,replace,3.0,N,@,C[C@@1N4B4,C[CN@1N4B4,10,replace @ at position 3 with N,flow_matching,0.3,2.0,60,241
136,remove,3.0,N,,C[CN@1N4B4,C[C@1N4B4,9,remove N from position 3,flow_matching,0.3,2.0,60,241
137,replace,4.0,@,1,C[C@1N4B4,C[C@@N4B4,9,replace 1 at position 4 with @,flow_matching,0.3,2.0,60,241
138,replace,1.0,7,[,C[C@@N4B4,C7C@@N4B4,9,replace [ at position 1 with 7,flow_matching,0.3,2.0,60,241
139,replace,1.0,[,7,C7C@@N4B4,C[C@@N4B4,9,replace 7 at position 1 with [,flow_matching,0.3,2.0,60,241
140,remove,5.0,N,,C[C@@N4B4,C[C@@4B4,8,remove N from position 5,flow_matching,0.3,2.0,60,241
141,replace,3.0,S,@,C[C@@4B4,C[CS@4B4,8,replace @ at position 3 with S,flow_matching,0.3,2.0,60,241
142,replace,3.0,@,S,C[CS@4B4,C[C@@4B4,8,replace S at position 3 with @,flow_matching,0.3,2.0,60,241
143,remove,7.0,4,,C[C@@4B4,C[C@@4B,7,remove 4 from position 7,flow_matching,0.3,2.0,60,241
144,replace,5.0,H,4,C[C@@4B,C[C@@HB,7,replace 4 at position 5 with H,flow_matching,0.3,2.0,60,241
145,replace,2.0,N,C,C[C@@HB,C[N@@HB,7,replace C at position 2 with N,flow_matching,0.3,2.0,60,241
146,replace,2.0,C,N,C[N@@HB,C[C@@HB,7,replace N at position 2 with C,flow_matching,0.3,2.0,60,241
147,replace,6.0,],B,C[C@@HB,C[C@@H],7,replace B at position 6 with ],flow_matching,0.3,2.0,60,241
148,add,7.0,(,,C[C@@H],C[C@@H](,8,add ( at position 7,flow_matching,0.3,2.0,60,241
149,replace,2.0,],C,C[C@@H](,C[]@@H](,8,replace C at position 2 with ],flow_matching,0.3,2.0,60,241
150,add,5.0,c,,C[]@@H](,C[]@@cH](,9,add c at position 5,flow_matching,0.3,2.0,60,241
151,add,4.0,I,,C[]@@cH](,C[]@I@cH](,10,add I at position 4,flow_matching,0.3,2.0,60,241
152,add,10.0,(,,C[]@I@cH](,C[]@I@cH]((,11,add ( at position 10,flow_matching,0.3,2.0,60,241
153,replace,7.0,],H,C[]@I@cH]((,C[]@I@c]]((,11,replace H at position 7 with ],flow_matching,0.3,2.0,60,241
154,remove,6.0,c,,C[]@I@c]]((,C[]@I@]]((,10,remove c from position 6,flow_matching,0.3,2.0,60,241
155,replace,2.0,C,],C[]@I@]]((,C[C@I@]]((,10,replace ] at position 2 with C,flow_matching,0.3,2.0,60,241
156,replace,4.0,@,I,C[C@I@]]((,C[C@@@]]((,10,replace I at position 4 with @,flow_matching,0.3,2.0,60,241
157,replace,5.0,H,@,C[C@@@]]((,C[C@@H]]((,10,replace @ at position 5 with H,flow_matching,0.3,2.0,60,241
158,add,9.0,6,,C[C@@H]]((,C[C@@H]](6(,11,add 6 at position 9,flow_matching,0.3,2.0,60,241
159,add,1.0,\,,C[C@@H]](6(,C\[C@@H]](6(,12,add \ at position 1,flow_matching,0.3,2.0,60,241
160,add,12.0,/,,C\[C@@H]](6(,C\[C@@H]](6(/,13,add / at position 12,flow_matching,0.3,2.0,60,241
161,add,7.0,),,C\[C@@H]](6(/,C\[C@@H)]](6(/,14,add ) at position 7,flow_matching,0.3,2.0,60,241
162,add,13.0,l,,C\[C@@H)]](6(/,C\[C@@H)]](6(l/,15,add l at position 13,flow_matching,0.3,2.0,60,241
163,add,2.0,n,,C\[C@@H)]](6(l/,C\n[C@@H)]](6(l/,16,add n at position 2,flow_matching,0.3,2.0,60,241
164,remove,5.0,@,,C\n[C@@H)]](6(l/,C\n[C@H)]](6(l/,15,remove @ from position 5,flow_matching,0.3,2.0,60,241
165,replace,5.0,1,@,C\n[C@H)]](6(l/,C\n[C1H)]](6(l/,15,replace @ at position 5 with 1,flow_matching,0.3,2.0,60,241
166,remove,14.0,/,,C\n[C1H)]](6(l/,C\n[C1H)]](6(l,14,remove / from position 14,flow_matching,0.3,2.0,60,241
167,add,13.0,O,,C\n[C1H)]](6(l,C\n[C1H)]](6(Ol,15,add O at position 13,flow_matching,0.3,2.0,60,241
168,add,5.0,5,,C\n[C1H)]](6(Ol,C\n[C51H)]](6(Ol,16,add 5 at position 5,flow_matching,0.3,2.0,60,241
169,add,1.0,S,,C\n[C51H)]](6(Ol,CS\n[C51H)]](6(Ol,17,add S at position 1,flow_matching,0.3,2.0,60,241
170,replace,1.0,[,S,CS\n[C51H)]](6(Ol,C[\n[C51H)]](6(Ol,17,replace S at position 1 with [,flow_matching,0.3,2.0,60,241
171,replace,2.0,C,\,C[\n[C51H)]](6(Ol,C[Cn[C51H)]](6(Ol,17,replace \ at position 2 with C,flow_matching,0.3,2.0,60,241
172,add,1.0,H,,C[Cn[C51H)]](6(Ol,CH[Cn[C51H)]](6(Ol,18,add H at position 1,flow_matching,0.3,2.0,60,241
173,remove,10.0,),,CH[Cn[C51H)]](6(Ol,CH[Cn[C51H]](6(Ol,17,remove ) from position 10,flow_matching,0.3,2.0,60,241
174,replace,1.0,[,H,CH[Cn[C51H]](6(Ol,C[[Cn[C51H]](6(Ol,17,replace H at position 1 with [,flow_matching,0.3,2.0,60,241
175,replace,4.0,s,n,C[[Cn[C51H]](6(Ol,C[[Cs[C51H]](6(Ol,17,replace n at position 4 with s,flow_matching,0.3,2.0,60,241
176,replace,14.0,#,(,C[[Cs[C51H]](6(Ol,C[[Cs[C51H]](6#Ol,17,replace ( at position 14 with #,flow_matching,0.3,2.0,60,241
177,remove,3.0,C,,C[[Cs[C51H]](6#Ol,C[[s[C51H]](6#Ol,16,remove C from position 3,flow_matching,0.3,2.0,60,241
178,remove,9.0,],,C[[s[C51H]](6#Ol,C[[s[C51H](6#Ol,15,remove ] from position 9,flow_matching,0.3,2.0,60,241
179,remove,5.0,C,,C[[s[C51H](6#Ol,C[[s[51H](6#Ol,14,remove C from position 5,flow_matching,0.3,2.0,60,241
180,add,0.0,l,,C[[s[51H](6#Ol,lC[[s[51H](6#Ol,15,add l at position 0,flow_matching,0.3,2.0,60,241
181,add,0.0,[,,lC[[s[51H](6#Ol,[lC[[s[51H](6#Ol,16,add [ at position 0,flow_matching,0.3,2.0,60,241
182,replace,6.0,S,[,[lC[[s[51H](6#Ol,[lC[[sS51H](6#Ol,16,replace [ at position 6 with S,flow_matching,0.3,2.0,60,241
183,replace,0.0,C,[,[lC[[sS51H](6#Ol,ClC[[sS51H](6#Ol,16,replace [ at position 0 with C,flow_matching,0.3,2.0,60,241
184,replace,1.0,[,l,ClC[[sS51H](6#Ol,C[C[[sS51H](6#Ol,16,replace l at position 1 with [,flow_matching,0.3,2.0,60,241
185,replace,3.0,@,[,C[C[[sS51H](6#Ol,C[C@[sS51H](6#Ol,16,replace [ at position 3 with @,flow_matching,0.3,2.0,60,241
186,replace,4.0,@,[,C[C@[sS51H](6#Ol,C[C@@sS51H](6#Ol,16,replace [ at position 4 with @,flow_matching,0.3,2.0,60,241
187,replace,5.0,H,s,C[C@@sS51H](6#Ol,C[C@@HS51H](6#Ol,16,replace s at position 5 with H,flow_matching,0.3,2.0,60,241
188,replace,6.0,],S,C[C@@HS51H](6#Ol,C[C@@H]51H](6#Ol,16,replace S at position 6 with ],flow_matching,0.3,2.0,60,241
189,replace,7.0,(,5,C[C@@H]51H](6#Ol,C[C@@H](1H](6#Ol,16,replace 5 at position 7 with (,flow_matching,0.3,2.0,60,241
190,replace,8.0,S,1,C[C@@H](1H](6#Ol,C[C@@H](SH](6#Ol,16,replace 1 at position 8 with S,flow_matching,0.3,2.0,60,241
191,replace,9.0,c,H,C[C@@H](SH](6#Ol,C[C@@H](Sc](6#Ol,16,replace H at position 9 with c,flow_matching,0.3,2.0,60,241
192,replace,10.0,1,],C[C@@H](Sc](6#Ol,C[C@@H](Sc1(6#Ol,16,replace ] at position 10 with 1,flow_matching,0.3,2.0,60,241
193,replace,11.0,n,(,C[C@@H](Sc1(6#Ol,C[C@@H](Sc1n6#Ol,16,replace ( at position 11 with n,flow_matching,0.3,2.0,60,241
194,replace,12.0,n,6,C[C@@H](Sc1n6#Ol,C[C@@H](Sc1nn#Ol,16,replace 6 at position 12 with n,flow_matching,0.3,2.0,60,241
195,replace,13.0,c,#,C[C@@H](Sc1nn#Ol,C[C@@H](Sc1nncOl,16,replace # at position 13 with c,flow_matching,0.3,2.0,60,241
196,replace,14.0,(,O,C[C@@H](Sc1nncOl,C[C@@H](Sc1nnc(l,16,replace O at position 14 with (,flow_matching,0.3,2.0,60,241
197,replace,15.0,-,l,C[C@@H](Sc1nnc(l,C[C@@H](Sc1nnc(-,16,replace l at position 15 with -,flow_matching,0.3,2.0,60,241
198,add,16.0,c,,C[C@@H](Sc1nnc(-,C[C@@H](Sc1nnc(-c,17,add c at position 16,flow_matching,0.3,2.0,60,241
199,add,17.0,2,,C[C@@H](Sc1nnc(-c,C[C@@H](Sc1nnc(-c2,18,add 2 at position 17,flow_matching,0.3,2.0,60,241
200,add,18.0,c,,C[C@@H](Sc1nnc(-c2,C[C@@H](Sc1nnc(-c2c,19,add c at position 18,flow_matching,0.3,2.0,60,241
201,add,19.0,c,,C[C@@H](Sc1nnc(-c2c,C[C@@H](Sc1nnc(-c2cc,20,add c at position 19,flow_matching,0.3,2.0,60,241
202,add,20.0,c,,C[C@@H](Sc1nnc(-c2cc,C[C@@H](Sc1nnc(-c2ccc,21,add c at position 20,flow_matching,0.3,2.0,60,241
203,add,21.0,s,,C[C@@H](Sc1nnc(-c2ccc,C[C@@H](Sc1nnc(-c2cccs,22,add s at position 21,flow_matching,0.3,2.0,60,241
204,add,22.0,2,,C[C@@H](Sc1nnc(-c2cccs,C[C@@H](Sc1nnc(-c2cccs2,23,add 2 at position 22,flow_matching,0.3,2.0,60,241
205,add,23.0,),,C[C@@H](Sc1nnc(-c2cccs2,C[C@@H](Sc1nnc(-c2cccs2),24,add ) at position 23,flow_matching,0.3,2.0,60,241
206,add,24.0,n,,C[C@@H](Sc1nnc(-c2cccs2),C[C@@H](Sc1nnc(-c2cccs2)n,25,add n at position 24,flow_matching,0.3,2.0,60,241
207,add,25.0,1,,C[C@@H](Sc1nnc(-c2cccs2)n,C[C@@H](Sc1nnc(-c2cccs2)n1,26,add 1 at position 25,flow_matching,0.3,2.0,60,241
208,add,26.0,-,,C[C@@H](Sc1nnc(-c2cccs2)n1,C[C@@H](Sc1nnc(-c2cccs2)n1-,27,add - at position 26,flow_matching,0.3,2.0,60,241
209,add,27.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-,C[C@@H](Sc1nnc(-c2cccs2)n1-c,28,add c at position 27,flow_matching,0.3,2.0,60,241
210,add,28.0,1,,C[C@@H](Sc1nnc(-c2cccs2)n1-c,C[C@@H](Sc1nnc(-c2cccs2)n1-c1,29,add 1 at position 28,flow_matching,0.3,2.0,60,241
211,add,29.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1,C[C@@H](Sc1nnc(-c2cccs2)n1-c1c,30,add c at position 29,flow_matching,0.3,2.0,60,241
212,add,30.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1c,C[C@@H](Sc1nnc(-c2cccs2)n1-c1cc,31,add c at position 30,flow_matching,0.3,2.0,60,241
213,add,31.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1cc,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccc,32,add c at position 31,flow_matching,0.3,2.0,60,241
214,add,32.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccc,C[C@@H](Sc1nnc(-c2cccs2)n1-c1cccc,33,add c at position 32,flow_matching,0.3,2.0,60,241
215,add,33.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1cccc,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc,34,add c at position 33,flow_matching,0.3,2.0,60,241
216,add,34.0,1,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1,35,add 1 at position 34,flow_matching,0.3,2.0,60,241
217,add,35.0,),,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1),36,add ) at position 35,flow_matching,0.3,2.0,60,241
218,add,36.0,C,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1),C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C,37,add C at position 36,flow_matching,0.3,2.0,60,241
219,add,37.0,(,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(,38,add ( at position 37,flow_matching,0.3,2.0,60,241
220,add,38.0,=,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=,39,add = at position 38,flow_matching,0.3,2.0,60,241
221,add,39.0,O,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O,40,add O at position 39,flow_matching,0.3,2.0,60,241
222,add,40.0,),,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O),41,add ) at position 40,flow_matching,0.3,2.0,60,241
223,add,41.0,N,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O),C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N,42,add N at position 41,flow_matching,0.3,2.0,60,241
224,add,42.0,1,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1,43,add 1 at position 42,flow_matching,0.3,2.0,60,241
225,add,43.0,C,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1C,44,add C at position 43,flow_matching,0.3,2.0,60,241
226,add,44.0,C,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1C,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC,45,add C at position 44,flow_matching,0.3,2.0,60,241
227,add,45.0,(,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(,46,add ( at position 45,flow_matching,0.3,2.0,60,241
228,add,46.0,=,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=,47,add = at position 46,flow_matching,0.3,2.0,60,241
229,add,47.0,O,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O,48,add O at position 47,flow_matching,0.3,2.0,60,241
230,add,48.0,),,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O),49,add ) at position 48,flow_matching,0.3,2.0,60,241
231,add,49.0,N,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O),C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)N,50,add N at position 49,flow_matching,0.3,2.0,60,241
232,add,50.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)N,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc,51,add c at position 50,flow_matching,0.3,2.0,60,241
233,add,51.0,2,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2,52,add 2 at position 51,flow_matching,0.3,2.0,60,241
234,add,52.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2c,53,add c at position 52,flow_matching,0.3,2.0,60,241
235,add,53.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2c,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2cc,54,add c at position 53,flow_matching,0.3,2.0,60,241
236,add,54.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2cc,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2ccc,55,add c at position 54,flow_matching,0.3,2.0,60,241
237,add,55.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2ccc,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2cccc,56,add c at position 55,flow_matching,0.3,2.0,60,241
238,add,56.0,c,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2cccc,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2ccccc,57,add c at position 56,flow_matching,0.3,2.0,60,241
239,add,57.0,2,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2ccccc,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2ccccc2,58,add 2 at position 57,flow_matching,0.3,2.0,60,241
240,add,58.0,1,,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2ccccc2,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2ccccc21,59,add 1 at position 58,flow_matching,0.3,2.0,60,241
241,add,59.0,"
",,C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2ccccc21,"C[C@@H](Sc1nnc(-c2cccs2)n1-c1ccccc1)C(=O)N1CC(=O)Nc2ccccc21
",60,"add 
 at position 59",flow_matching,0.3,2.0,60,241
