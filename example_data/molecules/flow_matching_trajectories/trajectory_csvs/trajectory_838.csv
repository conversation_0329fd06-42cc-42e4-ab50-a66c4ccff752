step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,107
1,add,0.0,),,,),1,add ) at position 0,flow_matching,0.3,2.0,43,107
2,replace,0.0,C,),),C,1,replace ) at position 0 with C,flow_matching,0.3,2.0,43,107
3,replace,0.0,N,C,C,N,1,replace <PERSON> at position 0 with N,flow_matching,0.3,2.0,43,107
4,replace,0.0,2,N,N,2,1,replace N at position 0 with 2,flow_matching,0.3,2.0,43,107
5,replace,0.0,C,2,2,C,1,replace 2 at position 0 with C,flow_matching,0.3,2.0,43,107
6,replace,0.0,),C,C,),1,replace <PERSON> at position 0 with ),flow_matching,0.3,2.0,43,107
7,replace,0.0,C,),),C,1,replace ) at position 0 with C,flow_matching,0.3,2.0,43,107
8,add,1.0,-,,C,C-,2,add - at position 1,flow_matching,0.3,2.0,43,107
9,replace,1.0,O,-,C-,CO,2,replace - at position 1 with O,flow_matching,0.3,2.0,43,107
10,add,0.0,S,,CO,SCO,3,add S at position 0,flow_matching,0.3,2.0,43,107
11,add,1.0,S,,SCO,SSCO,4,add S at position 1,flow_matching,0.3,2.0,43,107
12,remove,2.0,C,,SSCO,SSO,3,remove C from position 2,flow_matching,0.3,2.0,43,107
13,remove,0.0,S,,SSO,SO,2,remove S from position 0,flow_matching,0.3,2.0,43,107
14,replace,0.0,C,S,SO,CO,2,replace S at position 0 with C,flow_matching,0.3,2.0,43,107
15,replace,0.0,=,C,CO,=O,2,replace C at position 0 with =,flow_matching,0.3,2.0,43,107
16,replace,0.0,C,=,=O,CO,2,replace = at position 0 with C,flow_matching,0.3,2.0,43,107
17,replace,0.0,/,C,CO,/O,2,replace C at position 0 with /,flow_matching,0.3,2.0,43,107
18,replace,0.0,S,/,/O,SO,2,replace / at position 0 with S,flow_matching,0.3,2.0,43,107
19,replace,0.0,C,S,SO,CO,2,replace S at position 0 with C,flow_matching,0.3,2.0,43,107
20,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,43,107
21,replace,0.0,6,O,O,6,1,replace O at position 0 with 6,flow_matching,0.3,2.0,43,107
22,replace,0.0,C,6,6,C,1,replace 6 at position 0 with C,flow_matching,0.3,2.0,43,107
23,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,43,107
24,add,1.0,S,,CC,CSC,3,add S at position 1,flow_matching,0.3,2.0,43,107
25,replace,1.0,C,S,CSC,CCC,3,replace S at position 1 with C,flow_matching,0.3,2.0,43,107
26,replace,2.0,#,C,CCC,CC#,3,replace C at position 2 with #,flow_matching,0.3,2.0,43,107
27,add,0.0,=,,CC#,=CC#,4,add = at position 0,flow_matching,0.3,2.0,43,107
28,replace,3.0,[,#,=CC#,=CC[,4,replace # at position 3 with [,flow_matching,0.3,2.0,43,107
29,add,3.0,n,,=CC[,=CCn[,5,add n at position 3,flow_matching,0.3,2.0,43,107
30,replace,0.0,C,=,=CCn[,CCCn[,5,replace = at position 0 with C,flow_matching,0.3,2.0,43,107
31,remove,1.0,C,,CCCn[,CCn[,4,remove C from position 1,flow_matching,0.3,2.0,43,107
32,add,3.0,n,,CCn[,CCnn[,5,add n at position 3,flow_matching,0.3,2.0,43,107
33,add,1.0,F,,CCnn[,CFCnn[,6,add F at position 1,flow_matching,0.3,2.0,43,107
34,replace,1.0,6,F,CFCnn[,C6Cnn[,6,replace F at position 1 with 6,flow_matching,0.3,2.0,43,107
35,replace,1.0,C,6,C6Cnn[,CCCnn[,6,replace 6 at position 1 with C,flow_matching,0.3,2.0,43,107
36,replace,2.0,O,C,CCCnn[,CCOnn[,6,replace C at position 2 with O,flow_matching,0.3,2.0,43,107
37,add,6.0,@,,CCOnn[,CCOnn[@,7,add @ at position 6,flow_matching,0.3,2.0,43,107
38,replace,3.0,C,n,CCOnn[@,CCOCn[@,7,replace n at position 3 with C,flow_matching,0.3,2.0,43,107
39,remove,1.0,C,,CCOCn[@,COCn[@,6,remove C from position 1,flow_matching,0.3,2.0,43,107
40,replace,0.0,3,C,COCn[@,3OCn[@,6,replace C at position 0 with 3,flow_matching,0.3,2.0,43,107
41,replace,1.0,s,O,3OCn[@,3sCn[@,6,replace O at position 1 with s,flow_matching,0.3,2.0,43,107
42,remove,3.0,n,,3sCn[@,3sC[@,5,remove n from position 3,flow_matching,0.3,2.0,43,107
43,remove,4.0,@,,3sC[@,3sC[,4,remove @ from position 4,flow_matching,0.3,2.0,43,107
44,remove,3.0,[,,3sC[,3sC,3,remove [ from position 3,flow_matching,0.3,2.0,43,107
45,add,1.0,O,,3sC,3OsC,4,add O at position 1,flow_matching,0.3,2.0,43,107
46,remove,3.0,C,,3OsC,3Os,3,remove C from position 3,flow_matching,0.3,2.0,43,107
47,replace,0.0,C,3,3Os,COs,3,replace 3 at position 0 with C,flow_matching,0.3,2.0,43,107
48,replace,1.0,C,O,COs,CCs,3,replace O at position 1 with C,flow_matching,0.3,2.0,43,107
49,add,2.0,l,,CCs,CCls,4,add l at position 2,flow_matching,0.3,2.0,43,107
50,replace,2.0,O,l,CCls,CCOs,4,replace l at position 2 with O,flow_matching,0.3,2.0,43,107
51,replace,3.0,C,s,CCOs,CCOC,4,replace s at position 3 with C,flow_matching,0.3,2.0,43,107
52,replace,0.0,],C,CCOC,]COC,4,replace C at position 0 with ],flow_matching,0.3,2.0,43,107
53,replace,0.0,C,],]COC,CCOC,4,replace ] at position 0 with C,flow_matching,0.3,2.0,43,107
54,add,0.0,#,,CCOC,#CCOC,5,add # at position 0,flow_matching,0.3,2.0,43,107
55,add,2.0,=,,#CCOC,#C=COC,6,add = at position 2,flow_matching,0.3,2.0,43,107
56,add,4.0,@,,#C=COC,#C=C@OC,7,add @ at position 4,flow_matching,0.3,2.0,43,107
57,remove,1.0,C,,#C=C@OC,#=C@OC,6,remove C from position 1,flow_matching,0.3,2.0,43,107
58,replace,0.0,C,#,#=C@OC,C=C@OC,6,replace # at position 0 with C,flow_matching,0.3,2.0,43,107
59,replace,0.0,F,C,C=C@OC,F=C@OC,6,replace C at position 0 with F,flow_matching,0.3,2.0,43,107
60,add,6.0,I,,F=C@OC,F=C@OCI,7,add I at position 6,flow_matching,0.3,2.0,43,107
61,replace,0.0,C,F,F=C@OCI,C=C@OCI,7,replace F at position 0 with C,flow_matching,0.3,2.0,43,107
62,replace,1.0,C,=,C=C@OCI,CCC@OCI,7,replace = at position 1 with C,flow_matching,0.3,2.0,43,107
63,replace,2.0,O,C,CCC@OCI,CCO@OCI,7,replace C at position 2 with O,flow_matching,0.3,2.0,43,107
64,add,7.0,4,,CCO@OCI,CCO@OCI4,8,add 4 at position 7,flow_matching,0.3,2.0,43,107
65,remove,3.0,@,,CCO@OCI4,CCOOCI4,7,remove @ from position 3,flow_matching,0.3,2.0,43,107
66,replace,2.0,s,O,CCOOCI4,CCsOCI4,7,replace O at position 2 with s,flow_matching,0.3,2.0,43,107
67,replace,2.0,O,s,CCsOCI4,CCOOCI4,7,replace s at position 2 with O,flow_matching,0.3,2.0,43,107
68,replace,3.0,C,O,CCOOCI4,CCOCCI4,7,replace O at position 3 with C,flow_matching,0.3,2.0,43,107
69,replace,4.0,1,C,CCOCCI4,CCOC1I4,7,replace C at position 4 with 1,flow_matching,0.3,2.0,43,107
70,replace,5.0,C,I,CCOC1I4,CCOC1C4,7,replace I at position 5 with C,flow_matching,0.3,2.0,43,107
71,replace,6.0,C,4,CCOC1C4,CCOC1CC,7,replace 4 at position 6 with C,flow_matching,0.3,2.0,43,107
72,add,7.0,[,,CCOC1CC,CCOC1CC[,8,add [ at position 7,flow_matching,0.3,2.0,43,107
73,add,8.0,N,,CCOC1CC[,CCOC1CC[N,9,add N at position 8,flow_matching,0.3,2.0,43,107
74,add,9.0,H,,CCOC1CC[N,CCOC1CC[NH,10,add H at position 9,flow_matching,0.3,2.0,43,107
75,add,10.0,+,,CCOC1CC[NH,CCOC1CC[NH+,11,add + at position 10,flow_matching,0.3,2.0,43,107
76,add,11.0,],,CCOC1CC[NH+,CCOC1CC[NH+],12,add ] at position 11,flow_matching,0.3,2.0,43,107
77,add,12.0,(,,CCOC1CC[NH+],CCOC1CC[NH+](,13,add ( at position 12,flow_matching,0.3,2.0,43,107
78,add,13.0,C,,CCOC1CC[NH+](,CCOC1CC[NH+](C,14,add C at position 13,flow_matching,0.3,2.0,43,107
79,add,14.0,C,,CCOC1CC[NH+](C,CCOC1CC[NH+](CC,15,add C at position 14,flow_matching,0.3,2.0,43,107
80,add,15.0,[,,CCOC1CC[NH+](CC,CCOC1CC[NH+](CC[,16,add [ at position 15,flow_matching,0.3,2.0,43,107
81,add,16.0,C,,CCOC1CC[NH+](CC[,CCOC1CC[NH+](CC[C,17,add C at position 16,flow_matching,0.3,2.0,43,107
82,add,17.0,@,,CCOC1CC[NH+](CC[C,CCOC1CC[NH+](CC[C@,18,add @ at position 17,flow_matching,0.3,2.0,43,107
83,add,18.0,@,,CCOC1CC[NH+](CC[C@,CCOC1CC[NH+](CC[C@@,19,add @ at position 18,flow_matching,0.3,2.0,43,107
84,add,19.0,H,,CCOC1CC[NH+](CC[C@@,CCOC1CC[NH+](CC[C@@H,20,add H at position 19,flow_matching,0.3,2.0,43,107
85,add,20.0,],,CCOC1CC[NH+](CC[C@@H,CCOC1CC[NH+](CC[C@@H],21,add ] at position 20,flow_matching,0.3,2.0,43,107
86,add,21.0,(,,CCOC1CC[NH+](CC[C@@H],CCOC1CC[NH+](CC[C@@H](,22,add ( at position 21,flow_matching,0.3,2.0,43,107
87,add,22.0,O,,CCOC1CC[NH+](CC[C@@H](,CCOC1CC[NH+](CC[C@@H](O,23,add O at position 22,flow_matching,0.3,2.0,43,107
88,add,23.0,),,CCOC1CC[NH+](CC[C@@H](O,CCOC1CC[NH+](CC[C@@H](O),24,add ) at position 23,flow_matching,0.3,2.0,43,107
89,add,24.0,c,,CCOC1CC[NH+](CC[C@@H](O),CCOC1CC[NH+](CC[C@@H](O)c,25,add c at position 24,flow_matching,0.3,2.0,43,107
90,add,25.0,2,,CCOC1CC[NH+](CC[C@@H](O)c,CCOC1CC[NH+](CC[C@@H](O)c2,26,add 2 at position 25,flow_matching,0.3,2.0,43,107
91,add,26.0,c,,CCOC1CC[NH+](CC[C@@H](O)c2,CCOC1CC[NH+](CC[C@@H](O)c2c,27,add c at position 26,flow_matching,0.3,2.0,43,107
92,add,27.0,c,,CCOC1CC[NH+](CC[C@@H](O)c2c,CCOC1CC[NH+](CC[C@@H](O)c2cc,28,add c at position 27,flow_matching,0.3,2.0,43,107
93,add,28.0,c,,CCOC1CC[NH+](CC[C@@H](O)c2cc,CCOC1CC[NH+](CC[C@@H](O)c2ccc,29,add c at position 28,flow_matching,0.3,2.0,43,107
94,add,29.0,(,,CCOC1CC[NH+](CC[C@@H](O)c2ccc,CCOC1CC[NH+](CC[C@@H](O)c2ccc(,30,add ( at position 29,flow_matching,0.3,2.0,43,107
95,add,30.0,C,,CCOC1CC[NH+](CC[C@@H](O)c2ccc(,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C,31,add C at position 30,flow_matching,0.3,2.0,43,107
96,add,31.0,),,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C),32,add ) at position 31,flow_matching,0.3,2.0,43,107
97,add,32.0,c,,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C),CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c,33,add c at position 32,flow_matching,0.3,2.0,43,107
98,add,33.0,(,,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(,34,add ( at position 33,flow_matching,0.3,2.0,43,107
99,add,34.0,F,,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F,35,add F at position 34,flow_matching,0.3,2.0,43,107
100,add,35.0,),,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F),36,add ) at position 35,flow_matching,0.3,2.0,43,107
101,add,36.0,c,,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F),CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c,37,add c at position 36,flow_matching,0.3,2.0,43,107
102,add,37.0,2,,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2,38,add 2 at position 37,flow_matching,0.3,2.0,43,107
103,add,38.0,),,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2),39,add ) at position 38,flow_matching,0.3,2.0,43,107
104,add,39.0,C,,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2),CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2)C,40,add C at position 39,flow_matching,0.3,2.0,43,107
105,add,40.0,C,,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2)C,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2)CC,41,add C at position 40,flow_matching,0.3,2.0,43,107
106,add,41.0,1,,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2)CC,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2)CC1,42,add 1 at position 41,flow_matching,0.3,2.0,43,107
107,add,42.0,"
",,CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2)CC1,"CCOC1CC[NH+](CC[C@@H](O)c2ccc(C)c(F)c2)CC1
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,107
