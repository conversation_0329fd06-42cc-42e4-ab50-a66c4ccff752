step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,54,215
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,54,215
2,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,54,215
3,replace,1.0,5,[,C[,C5,2,replace [ at position 1 with 5,flow_matching,0.3,2.0,54,215
4,replace,1.0,[,5,C5,C[,2,replace 5 at position 1 with [,flow_matching,0.3,2.0,54,215
5,remove,0.0,C,,C[,[,1,remove C from position 0,flow_matching,0.3,2.0,54,215
6,replace,0.0,C,[,[,C,1,replace [ at position 0 with C,flow_matching,0.3,2.0,54,215
7,add,1.0,7,,C,C7,2,add 7 at position 1,flow_matching,0.3,2.0,54,215
8,replace,1.0,[,7,C7,C[,2,replace 7 at position 1 with [,flow_matching,0.3,2.0,54,215
9,add,1.0,C,,C[,CC[,3,add C at position 1,flow_matching,0.3,2.0,54,215
10,remove,2.0,[,,CC[,CC,2,remove [ from position 2,flow_matching,0.3,2.0,54,215
11,remove,1.0,C,,CC,C,1,remove C from position 1,flow_matching,0.3,2.0,54,215
12,replace,0.0,3,C,C,3,1,replace C at position 0 with 3,flow_matching,0.3,2.0,54,215
13,remove,0.0,3,,3,,0,remove 3 from position 0,flow_matching,0.3,2.0,54,215
14,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,54,215
15,replace,0.0,(,C,C,(,1,replace C at position 0 with (,flow_matching,0.3,2.0,54,215
16,add,0.0,@,,(,@(,2,add @ at position 0,flow_matching,0.3,2.0,54,215
17,replace,0.0,C,@,@(,C(,2,replace @ at position 0 with C,flow_matching,0.3,2.0,54,215
18,replace,1.0,[,(,C(,C[,2,replace ( at position 1 with [,flow_matching,0.3,2.0,54,215
19,add,1.0,N,,C[,CN[,3,add N at position 1,flow_matching,0.3,2.0,54,215
20,remove,0.0,C,,CN[,N[,2,remove C from position 0,flow_matching,0.3,2.0,54,215
21,replace,1.0,@,[,N[,N@,2,replace [ at position 1 with @,flow_matching,0.3,2.0,54,215
22,remove,1.0,@,,N@,N,1,remove @ from position 1,flow_matching,0.3,2.0,54,215
23,replace,0.0,+,N,N,+,1,replace N at position 0 with +,flow_matching,0.3,2.0,54,215
24,add,0.0,C,,+,C+,2,add C at position 0,flow_matching,0.3,2.0,54,215
25,replace,1.0,[,+,C+,C[,2,replace + at position 1 with [,flow_matching,0.3,2.0,54,215
26,add,1.0,C,,C[,CC[,3,add C at position 1,flow_matching,0.3,2.0,54,215
27,add,0.0,n,,CC[,nCC[,4,add n at position 0,flow_matching,0.3,2.0,54,215
28,replace,0.0,C,n,nCC[,CCC[,4,replace n at position 0 with C,flow_matching,0.3,2.0,54,215
29,remove,3.0,[,,CCC[,CCC,3,remove [ from position 3,flow_matching,0.3,2.0,54,215
30,replace,1.0,[,C,CCC,C[C,3,replace C at position 1 with [,flow_matching,0.3,2.0,54,215
31,add,3.0,@,,C[C,C[C@,4,add @ at position 3,flow_matching,0.3,2.0,54,215
32,replace,2.0,#,C,C[C@,C[#@,4,replace C at position 2 with #,flow_matching,0.3,2.0,54,215
33,replace,2.0,C,#,C[#@,C[C@,4,replace # at position 2 with C,flow_matching,0.3,2.0,54,215
34,add,1.0,@,,C[C@,C@[C@,5,add @ at position 1,flow_matching,0.3,2.0,54,215
35,replace,1.0,O,@,C@[C@,CO[C@,5,replace @ at position 1 with O,flow_matching,0.3,2.0,54,215
36,add,1.0,N,,CO[C@,CNO[C@,6,add N at position 1,flow_matching,0.3,2.0,54,215
37,add,5.0,/,,CNO[C@,CNO[C/@,7,add / at position 5,flow_matching,0.3,2.0,54,215
38,replace,1.0,[,N,CNO[C/@,C[O[C/@,7,replace N at position 1 with [,flow_matching,0.3,2.0,54,215
39,remove,5.0,/,,C[O[C/@,C[O[C@,6,remove / from position 5,flow_matching,0.3,2.0,54,215
40,replace,2.0,C,O,C[O[C@,C[C[C@,6,replace O at position 2 with C,flow_matching,0.3,2.0,54,215
41,add,4.0,l,,C[C[C@,C[C[lC@,7,add l at position 4,flow_matching,0.3,2.0,54,215
42,replace,3.0,@,[,C[C[lC@,C[C@lC@,7,replace [ at position 3 with @,flow_matching,0.3,2.0,54,215
43,replace,5.0,r,C,C[C@lC@,C[C@lr@,7,replace C at position 5 with r,flow_matching,0.3,2.0,54,215
44,replace,6.0,S,@,C[C@lr@,C[C@lrS,7,replace @ at position 6 with S,flow_matching,0.3,2.0,54,215
45,replace,0.0,I,C,C[C@lrS,I[C@lrS,7,replace C at position 0 with I,flow_matching,0.3,2.0,54,215
46,replace,0.0,4,I,I[C@lrS,4[C@lrS,7,replace I at position 0 with 4,flow_matching,0.3,2.0,54,215
47,add,1.0,s,,4[C@lrS,4s[C@lrS,8,add s at position 1,flow_matching,0.3,2.0,54,215
48,remove,2.0,[,,4s[C@lrS,4sC@lrS,7,remove [ from position 2,flow_matching,0.3,2.0,54,215
49,add,7.0,r,,4sC@lrS,4sC@lrSr,8,add r at position 7,flow_matching,0.3,2.0,54,215
50,add,1.0,3,,4sC@lrSr,43sC@lrSr,9,add 3 at position 1,flow_matching,0.3,2.0,54,215
51,remove,4.0,@,,43sC@lrSr,43sClrSr,8,remove @ from position 4,flow_matching,0.3,2.0,54,215
52,replace,4.0,+,l,43sClrSr,43sC+rSr,8,replace l at position 4 with +,flow_matching,0.3,2.0,54,215
53,replace,0.0,C,4,43sC+rSr,C3sC+rSr,8,replace 4 at position 0 with C,flow_matching,0.3,2.0,54,215
54,add,8.0,[,,C3sC+rSr,C3sC+rSr[,9,add [ at position 8,flow_matching,0.3,2.0,54,215
55,replace,1.0,[,3,C3sC+rSr[,C[sC+rSr[,9,replace 3 at position 1 with [,flow_matching,0.3,2.0,54,215
56,remove,2.0,s,,C[sC+rSr[,C[C+rSr[,8,remove s from position 2,flow_matching,0.3,2.0,54,215
57,replace,7.0,=,[,C[C+rSr[,C[C+rSr=,8,replace [ at position 7 with =,flow_matching,0.3,2.0,54,215
58,add,1.0,7,,C[C+rSr=,C7[C+rSr=,9,add 7 at position 1,flow_matching,0.3,2.0,54,215
59,add,6.0,B,,C7[C+rSr=,C7[C+rBSr=,10,add B at position 6,flow_matching,0.3,2.0,54,215
60,replace,5.0,n,r,C7[C+rBSr=,C7[C+nBSr=,10,replace r at position 5 with n,flow_matching,0.3,2.0,54,215
61,replace,4.0,r,+,C7[C+nBSr=,C7[CrnBSr=,10,replace + at position 4 with r,flow_matching,0.3,2.0,54,215
62,replace,6.0,[,B,C7[CrnBSr=,C7[Crn[Sr=,10,replace B at position 6 with [,flow_matching,0.3,2.0,54,215
63,replace,1.0,[,7,C7[Crn[Sr=,C[[Crn[Sr=,10,replace 7 at position 1 with [,flow_matching,0.3,2.0,54,215
64,remove,0.0,C,,C[[Crn[Sr=,[[Crn[Sr=,9,remove C from position 0,flow_matching,0.3,2.0,54,215
65,replace,0.0,C,[,[[Crn[Sr=,C[Crn[Sr=,9,replace [ at position 0 with C,flow_matching,0.3,2.0,54,215
66,remove,5.0,[,,C[Crn[Sr=,C[CrnSr=,8,remove [ from position 5,flow_matching,0.3,2.0,54,215
67,replace,3.0,@,r,C[CrnSr=,C[C@nSr=,8,replace r at position 3 with @,flow_matching,0.3,2.0,54,215
68,add,3.0,n,,C[C@nSr=,C[Cn@nSr=,9,add n at position 3,flow_matching,0.3,2.0,54,215
69,replace,2.0,4,C,C[Cn@nSr=,C[4n@nSr=,9,replace C at position 2 with 4,flow_matching,0.3,2.0,54,215
70,add,5.0,I,,C[4n@nSr=,C[4n@InSr=,10,add I at position 5,flow_matching,0.3,2.0,54,215
71,remove,7.0,S,,C[4n@InSr=,C[4n@Inr=,9,remove S from position 7,flow_matching,0.3,2.0,54,215
72,add,4.0,5,,C[4n@Inr=,C[4n5@Inr=,10,add 5 at position 4,flow_matching,0.3,2.0,54,215
73,remove,9.0,=,,C[4n5@Inr=,C[4n5@Inr,9,remove = from position 9,flow_matching,0.3,2.0,54,215
74,replace,2.0,C,4,C[4n5@Inr,C[Cn5@Inr,9,replace 4 at position 2 with C,flow_matching,0.3,2.0,54,215
75,replace,6.0,s,I,C[Cn5@Inr,C[Cn5@snr,9,replace I at position 6 with s,flow_matching,0.3,2.0,54,215
76,replace,3.0,@,n,C[Cn5@snr,C[C@5@snr,9,replace n at position 3 with @,flow_matching,0.3,2.0,54,215
77,add,4.0,),,C[C@5@snr,C[C@)5@snr,10,add ) at position 4,flow_matching,0.3,2.0,54,215
78,remove,5.0,5,,C[C@)5@snr,C[C@)@snr,9,remove 5 from position 5,flow_matching,0.3,2.0,54,215
79,replace,0.0,4,C,C[C@)@snr,4[C@)@snr,9,replace C at position 0 with 4,flow_matching,0.3,2.0,54,215
80,add,6.0,6,,4[C@)@snr,4[C@)@6snr,10,add 6 at position 6,flow_matching,0.3,2.0,54,215
81,replace,0.0,C,4,4[C@)@6snr,C[C@)@6snr,10,replace 4 at position 0 with C,flow_matching,0.3,2.0,54,215
82,add,8.0,o,,C[C@)@6snr,C[C@)@6sonr,11,add o at position 8,flow_matching,0.3,2.0,54,215
83,replace,9.0,/,n,C[C@)@6sonr,C[C@)@6so/r,11,replace n at position 9 with /,flow_matching,0.3,2.0,54,215
84,add,11.0,4,,C[C@)@6so/r,C[C@)@6so/r4,12,add 4 at position 11,flow_matching,0.3,2.0,54,215
85,replace,4.0,#,),C[C@)@6so/r4,C[C@#@6so/r4,12,replace ) at position 4 with #,flow_matching,0.3,2.0,54,215
86,remove,5.0,@,,C[C@#@6so/r4,C[C@#6so/r4,11,remove @ from position 5,flow_matching,0.3,2.0,54,215
87,replace,5.0,S,6,C[C@#6so/r4,C[C@#Sso/r4,11,replace 6 at position 5 with S,flow_matching,0.3,2.0,54,215
88,remove,8.0,/,,C[C@#Sso/r4,C[C@#Ssor4,10,remove / from position 8,flow_matching,0.3,2.0,54,215
89,remove,2.0,C,,C[C@#Ssor4,C[@#Ssor4,9,remove C from position 2,flow_matching,0.3,2.0,54,215
90,add,0.0,1,,C[@#Ssor4,1C[@#Ssor4,10,add 1 at position 0,flow_matching,0.3,2.0,54,215
91,replace,0.0,C,1,1C[@#Ssor4,CC[@#Ssor4,10,replace 1 at position 0 with C,flow_matching,0.3,2.0,54,215
92,replace,1.0,[,C,CC[@#Ssor4,C[[@#Ssor4,10,replace C at position 1 with [,flow_matching,0.3,2.0,54,215
93,replace,2.0,C,[,C[[@#Ssor4,C[C@#Ssor4,10,replace [ at position 2 with C,flow_matching,0.3,2.0,54,215
94,replace,4.0,@,#,C[C@#Ssor4,C[C@@Ssor4,10,replace # at position 4 with @,flow_matching,0.3,2.0,54,215
95,replace,5.0,H,S,C[C@@Ssor4,C[C@@Hsor4,10,replace S at position 5 with H,flow_matching,0.3,2.0,54,215
96,replace,6.0,],s,C[C@@Hsor4,C[C@@H]or4,10,replace s at position 6 with ],flow_matching,0.3,2.0,54,215
97,add,8.0,3,,C[C@@H]or4,C[C@@H]o3r4,11,add 3 at position 8,flow_matching,0.3,2.0,54,215
98,replace,7.0,(,o,C[C@@H]o3r4,C[C@@H](3r4,11,replace o at position 7 with (,flow_matching,0.3,2.0,54,215
99,add,1.0,(,,C[C@@H](3r4,C([C@@H](3r4,12,add ( at position 1,flow_matching,0.3,2.0,54,215
100,replace,6.0,s,H,C([C@@H](3r4,C([C@@s](3r4,12,replace H at position 6 with s,flow_matching,0.3,2.0,54,215
101,add,12.0,c,,C([C@@s](3r4,C([C@@s](3r4c,13,add c at position 12,flow_matching,0.3,2.0,54,215
102,add,6.0,H,,C([C@@s](3r4c,C([C@@Hs](3r4c,14,add H at position 6,flow_matching,0.3,2.0,54,215
103,remove,0.0,C,,C([C@@Hs](3r4c,([C@@Hs](3r4c,13,remove C from position 0,flow_matching,0.3,2.0,54,215
104,replace,0.0,C,(,([C@@Hs](3r4c,C[C@@Hs](3r4c,13,replace ( at position 0 with C,flow_matching,0.3,2.0,54,215
105,replace,5.0,7,H,C[C@@Hs](3r4c,C[C@@7s](3r4c,13,replace H at position 5 with 7,flow_matching,0.3,2.0,54,215
106,replace,3.0,B,@,C[C@@7s](3r4c,C[CB@7s](3r4c,13,replace @ at position 3 with B,flow_matching,0.3,2.0,54,215
107,replace,1.0,=,[,C[CB@7s](3r4c,C=CB@7s](3r4c,13,replace [ at position 1 with =,flow_matching,0.3,2.0,54,215
108,remove,2.0,C,,C=CB@7s](3r4c,C=B@7s](3r4c,12,remove C from position 2,flow_matching,0.3,2.0,54,215
109,add,10.0,(,,C=B@7s](3r4c,C=B@7s](3r(4c,13,add ( at position 10,flow_matching,0.3,2.0,54,215
110,replace,1.0,[,=,C=B@7s](3r(4c,C[B@7s](3r(4c,13,replace = at position 1 with [,flow_matching,0.3,2.0,54,215
111,replace,11.0,5,4,C[B@7s](3r(4c,C[B@7s](3r(5c,13,replace 4 at position 11 with 5,flow_matching,0.3,2.0,54,215
112,add,8.0,s,,C[B@7s](3r(5c,C[B@7s](s3r(5c,14,add s at position 8,flow_matching,0.3,2.0,54,215
113,remove,1.0,[,,C[B@7s](s3r(5c,CB@7s](s3r(5c,13,remove [ from position 1,flow_matching,0.3,2.0,54,215
114,add,7.0,c,,CB@7s](s3r(5c,CB@7s](cs3r(5c,14,add c at position 7,flow_matching,0.3,2.0,54,215
115,add,10.0,@,,CB@7s](cs3r(5c,CB@7s](cs3@r(5c,15,add @ at position 10,flow_matching,0.3,2.0,54,215
116,replace,14.0,-,c,CB@7s](cs3@r(5c,CB@7s](cs3@r(5-,15,replace c at position 14 with -,flow_matching,0.3,2.0,54,215
117,add,4.0,l,,CB@7s](cs3@r(5-,CB@7ls](cs3@r(5-,16,add l at position 4,flow_matching,0.3,2.0,54,215
118,remove,12.0,r,,CB@7ls](cs3@r(5-,CB@7ls](cs3@(5-,15,remove r from position 12,flow_matching,0.3,2.0,54,215
119,replace,1.0,[,B,CB@7ls](cs3@(5-,C[@7ls](cs3@(5-,15,replace B at position 1 with [,flow_matching,0.3,2.0,54,215
120,replace,11.0,r,@,C[@7ls](cs3@(5-,C[@7ls](cs3r(5-,15,replace @ at position 11 with r,flow_matching,0.3,2.0,54,215
121,replace,2.0,C,@,C[@7ls](cs3r(5-,C[C7ls](cs3r(5-,15,replace @ at position 2 with C,flow_matching,0.3,2.0,54,215
122,add,12.0,#,,C[C7ls](cs3r(5-,C[C7ls](cs3r#(5-,16,add # at position 12,flow_matching,0.3,2.0,54,215
123,remove,10.0,3,,C[C7ls](cs3r#(5-,C[C7ls](csr#(5-,15,remove 3 from position 10,flow_matching,0.3,2.0,54,215
124,replace,5.0,#,s,C[C7ls](csr#(5-,C[C7l#](csr#(5-,15,replace s at position 5 with #,flow_matching,0.3,2.0,54,215
125,replace,3.0,@,7,C[C7l#](csr#(5-,C[C@l#](csr#(5-,15,replace 7 at position 3 with @,flow_matching,0.3,2.0,54,215
126,replace,4.0,@,l,C[C@l#](csr#(5-,C[C@@#](csr#(5-,15,replace l at position 4 with @,flow_matching,0.3,2.0,54,215
127,add,11.0,I,,C[C@@#](csr#(5-,C[C@@#](csrI#(5-,16,add I at position 11,flow_matching,0.3,2.0,54,215
128,add,13.0,),,C[C@@#](csrI#(5-,C[C@@#](csrI#)(5-,17,add ) at position 13,flow_matching,0.3,2.0,54,215
129,remove,11.0,I,,C[C@@#](csrI#)(5-,C[C@@#](csr#)(5-,16,remove I from position 11,flow_matching,0.3,2.0,54,215
130,replace,5.0,H,#,C[C@@#](csr#)(5-,C[C@@H](csr#)(5-,16,replace # at position 5 with H,flow_matching,0.3,2.0,54,215
131,add,13.0,),,C[C@@H](csr#)(5-,C[C@@H](csr#))(5-,17,add ) at position 13,flow_matching,0.3,2.0,54,215
132,replace,2.0,),C,C[C@@H](csr#))(5-,C[)@@H](csr#))(5-,17,replace C at position 2 with ),flow_matching,0.3,2.0,54,215
133,replace,6.0,\,],C[)@@H](csr#))(5-,C[)@@H\(csr#))(5-,17,replace ] at position 6 with \,flow_matching,0.3,2.0,54,215
134,add,9.0,\,,C[)@@H\(csr#))(5-,C[)@@H\(c\sr#))(5-,18,add \ at position 9,flow_matching,0.3,2.0,54,215
135,remove,0.0,C,,C[)@@H\(c\sr#))(5-,[)@@H\(c\sr#))(5-,17,remove C from position 0,flow_matching,0.3,2.0,54,215
136,add,4.0,n,,[)@@H\(c\sr#))(5-,[)@@nH\(c\sr#))(5-,18,add n at position 4,flow_matching,0.3,2.0,54,215
137,remove,3.0,@,,[)@@nH\(c\sr#))(5-,[)@nH\(c\sr#))(5-,17,remove @ from position 3,flow_matching,0.3,2.0,54,215
138,replace,10.0,7,r,[)@nH\(c\sr#))(5-,[)@nH\(c\s7#))(5-,17,replace r at position 10 with 7,flow_matching,0.3,2.0,54,215
139,replace,9.0,B,s,[)@nH\(c\s7#))(5-,[)@nH\(c\B7#))(5-,17,replace s at position 9 with B,flow_matching,0.3,2.0,54,215
140,replace,16.0,s,-,[)@nH\(c\B7#))(5-,[)@nH\(c\B7#))(5s,17,replace - at position 16 with s,flow_matching,0.3,2.0,54,215
141,replace,0.0,C,[,[)@nH\(c\B7#))(5s,C)@nH\(c\B7#))(5s,17,replace [ at position 0 with C,flow_matching,0.3,2.0,54,215
142,replace,3.0,B,n,C)@nH\(c\B7#))(5s,C)@BH\(c\B7#))(5s,17,replace n at position 3 with B,flow_matching,0.3,2.0,54,215
143,remove,13.0,),,C)@BH\(c\B7#))(5s,C)@BH\(c\B7#)(5s,16,remove ) from position 13,flow_matching,0.3,2.0,54,215
144,add,6.0,],,C)@BH\(c\B7#)(5s,C)@BH\](c\B7#)(5s,17,add ] at position 6,flow_matching,0.3,2.0,54,215
145,remove,1.0,),,C)@BH\](c\B7#)(5s,C@BH\](c\B7#)(5s,16,remove ) from position 1,flow_matching,0.3,2.0,54,215
146,add,3.0,s,,C@BH\](c\B7#)(5s,C@BsH\](c\B7#)(5s,17,add s at position 3,flow_matching,0.3,2.0,54,215
147,remove,13.0,),,C@BsH\](c\B7#)(5s,C@BsH\](c\B7#(5s,16,remove ) from position 13,flow_matching,0.3,2.0,54,215
148,add,12.0,N,,C@BsH\](c\B7#(5s,C@BsH\](c\B7N#(5s,17,add N at position 12,flow_matching,0.3,2.0,54,215
149,replace,1.0,[,@,C@BsH\](c\B7N#(5s,C[BsH\](c\B7N#(5s,17,replace @ at position 1 with [,flow_matching,0.3,2.0,54,215
150,replace,15.0,),5,C[BsH\](c\B7N#(5s,C[BsH\](c\B7N#()s,17,replace 5 at position 15 with ),flow_matching,0.3,2.0,54,215
151,replace,11.0,4,7,C[BsH\](c\B7N#()s,C[BsH\](c\B4N#()s,17,replace 7 at position 11 with 4,flow_matching,0.3,2.0,54,215
152,add,10.0,@,,C[BsH\](c\B4N#()s,C[BsH\](c\@B4N#()s,18,add @ at position 10,flow_matching,0.3,2.0,54,215
153,replace,2.0,C,B,C[BsH\](c\@B4N#()s,C[CsH\](c\@B4N#()s,18,replace B at position 2 with C,flow_matching,0.3,2.0,54,215
154,add,15.0,2,,C[CsH\](c\@B4N#()s,C[CsH\](c\@B4N#2()s,19,add 2 at position 15,flow_matching,0.3,2.0,54,215
155,add,1.0,c,,C[CsH\](c\@B4N#2()s,Cc[CsH\](c\@B4N#2()s,20,add c at position 1,flow_matching,0.3,2.0,54,215
156,remove,13.0,4,,Cc[CsH\](c\@B4N#2()s,Cc[CsH\](c\@BN#2()s,19,remove 4 from position 13,flow_matching,0.3,2.0,54,215
157,remove,10.0,\,,Cc[CsH\](c\@BN#2()s,Cc[CsH\](c@BN#2()s,18,remove \ from position 10,flow_matching,0.3,2.0,54,215
158,add,6.0,N,,Cc[CsH\](c@BN#2()s,Cc[CsHN\](c@BN#2()s,19,add N at position 6,flow_matching,0.3,2.0,54,215
159,add,15.0,F,,Cc[CsHN\](c@BN#2()s,Cc[CsHN\](c@BN#F2()s,20,add F at position 15,flow_matching,0.3,2.0,54,215
160,replace,1.0,[,c,Cc[CsHN\](c@BN#F2()s,C[[CsHN\](c@BN#F2()s,20,replace c at position 1 with [,flow_matching,0.3,2.0,54,215
161,remove,10.0,c,,C[[CsHN\](c@BN#F2()s,C[[CsHN\](@BN#F2()s,19,remove c from position 10,flow_matching,0.3,2.0,54,215
162,replace,2.0,C,[,C[[CsHN\](@BN#F2()s,C[CCsHN\](@BN#F2()s,19,replace [ at position 2 with C,flow_matching,0.3,2.0,54,215
163,add,8.0,S,,C[CCsHN\](@BN#F2()s,C[CCsHN\S](@BN#F2()s,20,add S at position 8,flow_matching,0.3,2.0,54,215
164,remove,14.0,#,,C[CCsHN\S](@BN#F2()s,C[CCsHN\S](@BNF2()s,19,remove # from position 14,flow_matching,0.3,2.0,54,215
165,add,19.0,r,,C[CCsHN\S](@BNF2()s,C[CCsHN\S](@BNF2()sr,20,add r at position 19,flow_matching,0.3,2.0,54,215
166,replace,3.0,@,C,C[CCsHN\S](@BNF2()sr,C[C@sHN\S](@BNF2()sr,20,replace C at position 3 with @,flow_matching,0.3,2.0,54,215
167,replace,4.0,@,s,C[C@sHN\S](@BNF2()sr,C[C@@HN\S](@BNF2()sr,20,replace s at position 4 with @,flow_matching,0.3,2.0,54,215
168,replace,6.0,],N,C[C@@HN\S](@BNF2()sr,C[C@@H]\S](@BNF2()sr,20,replace N at position 6 with ],flow_matching,0.3,2.0,54,215
169,replace,7.0,(,\,C[C@@H]\S](@BNF2()sr,C[C@@H](S](@BNF2()sr,20,replace \ at position 7 with (,flow_matching,0.3,2.0,54,215
170,replace,8.0,c,S,C[C@@H](S](@BNF2()sr,C[C@@H](c](@BNF2()sr,20,replace S at position 8 with c,flow_matching,0.3,2.0,54,215
171,replace,9.0,1,],C[C@@H](c](@BNF2()sr,C[C@@H](c1(@BNF2()sr,20,replace ] at position 9 with 1,flow_matching,0.3,2.0,54,215
172,replace,10.0,c,(,C[C@@H](c1(@BNF2()sr,C[C@@H](c1c@BNF2()sr,20,replace ( at position 10 with c,flow_matching,0.3,2.0,54,215
173,replace,11.0,c,@,C[C@@H](c1c@BNF2()sr,C[C@@H](c1ccBNF2()sr,20,replace @ at position 11 with c,flow_matching,0.3,2.0,54,215
174,replace,12.0,c,B,C[C@@H](c1ccBNF2()sr,C[C@@H](c1cccNF2()sr,20,replace B at position 12 with c,flow_matching,0.3,2.0,54,215
175,replace,13.0,(,N,C[C@@H](c1cccNF2()sr,C[C@@H](c1ccc(F2()sr,20,replace N at position 13 with (,flow_matching,0.3,2.0,54,215
176,replace,14.0,[,F,C[C@@H](c1ccc(F2()sr,C[C@@H](c1ccc([2()sr,20,replace F at position 14 with [,flow_matching,0.3,2.0,54,215
177,replace,15.0,S,2,C[C@@H](c1ccc([2()sr,C[C@@H](c1ccc([S()sr,20,replace 2 at position 15 with S,flow_matching,0.3,2.0,54,215
178,replace,16.0,@,(,C[C@@H](c1ccc([S()sr,C[C@@H](c1ccc([S@)sr,20,replace ( at position 16 with @,flow_matching,0.3,2.0,54,215
179,replace,17.0,],),C[C@@H](c1ccc([S@)sr,C[C@@H](c1ccc([S@]sr,20,replace ) at position 17 with ],flow_matching,0.3,2.0,54,215
180,replace,18.0,(,s,C[C@@H](c1ccc([S@]sr,C[C@@H](c1ccc([S@](r,20,replace s at position 18 with (,flow_matching,0.3,2.0,54,215
181,replace,19.0,C,r,C[C@@H](c1ccc([S@](r,C[C@@H](c1ccc([S@](C,20,replace r at position 19 with C,flow_matching,0.3,2.0,54,215
182,add,20.0,),,C[C@@H](c1ccc([S@](C,C[C@@H](c1ccc([S@](C),21,add ) at position 20,flow_matching,0.3,2.0,54,215
183,add,21.0,=,,C[C@@H](c1ccc([S@](C),C[C@@H](c1ccc([S@](C)=,22,add = at position 21,flow_matching,0.3,2.0,54,215
184,add,22.0,O,,C[C@@H](c1ccc([S@](C)=,C[C@@H](c1ccc([S@](C)=O,23,add O at position 22,flow_matching,0.3,2.0,54,215
185,add,23.0,),,C[C@@H](c1ccc([S@](C)=O,C[C@@H](c1ccc([S@](C)=O),24,add ) at position 23,flow_matching,0.3,2.0,54,215
186,add,24.0,c,,C[C@@H](c1ccc([S@](C)=O),C[C@@H](c1ccc([S@](C)=O)c,25,add c at position 24,flow_matching,0.3,2.0,54,215
187,add,25.0,c,,C[C@@H](c1ccc([S@](C)=O)c,C[C@@H](c1ccc([S@](C)=O)cc,26,add c at position 25,flow_matching,0.3,2.0,54,215
188,add,26.0,1,,C[C@@H](c1ccc([S@](C)=O)cc,C[C@@H](c1ccc([S@](C)=O)cc1,27,add 1 at position 26,flow_matching,0.3,2.0,54,215
189,add,27.0,),,C[C@@H](c1ccc([S@](C)=O)cc1,C[C@@H](c1ccc([S@](C)=O)cc1),28,add ) at position 27,flow_matching,0.3,2.0,54,215
190,add,28.0,N,,C[C@@H](c1ccc([S@](C)=O)cc1),C[C@@H](c1ccc([S@](C)=O)cc1)N,29,add N at position 28,flow_matching,0.3,2.0,54,215
191,add,29.0,(,,C[C@@H](c1ccc([S@](C)=O)cc1)N,C[C@@H](c1ccc([S@](C)=O)cc1)N(,30,add ( at position 29,flow_matching,0.3,2.0,54,215
192,add,30.0,C,,C[C@@H](c1ccc([S@](C)=O)cc1)N(,C[C@@H](c1ccc([S@](C)=O)cc1)N(C,31,add C at position 30,flow_matching,0.3,2.0,54,215
193,add,31.0,),,C[C@@H](c1ccc([S@](C)=O)cc1)N(C,C[C@@H](c1ccc([S@](C)=O)cc1)N(C),32,add ) at position 31,flow_matching,0.3,2.0,54,215
194,add,32.0,C,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C),C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C,33,add C at position 32,flow_matching,0.3,2.0,54,215
195,add,33.0,(,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(,34,add ( at position 33,flow_matching,0.3,2.0,54,215
196,add,34.0,=,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=,35,add = at position 34,flow_matching,0.3,2.0,54,215
197,add,35.0,O,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O,36,add O at position 35,flow_matching,0.3,2.0,54,215
198,add,36.0,),,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O),37,add ) at position 36,flow_matching,0.3,2.0,54,215
199,add,37.0,c,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O),C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c,38,add c at position 37,flow_matching,0.3,2.0,54,215
200,add,38.0,1,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1,39,add 1 at position 38,flow_matching,0.3,2.0,54,215
201,add,39.0,c,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1c,40,add c at position 39,flow_matching,0.3,2.0,54,215
202,add,40.0,c,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1c,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc,41,add c at position 40,flow_matching,0.3,2.0,54,215
203,add,41.0,2,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2,42,add 2 at position 41,flow_matching,0.3,2.0,54,215
204,add,42.0,c,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2c,43,add c at position 42,flow_matching,0.3,2.0,54,215
205,add,43.0,c,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2c,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cc,44,add c at position 43,flow_matching,0.3,2.0,54,215
206,add,44.0,c,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cc,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2ccc,45,add c at position 44,flow_matching,0.3,2.0,54,215
207,add,45.0,c,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2ccc,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc,46,add c at position 45,flow_matching,0.3,2.0,54,215
208,add,46.0,(,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(,47,add ( at position 46,flow_matching,0.3,2.0,54,215
209,add,47.0,F,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F,48,add F at position 47,flow_matching,0.3,2.0,54,215
210,add,48.0,),,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F),49,add ) at position 48,flow_matching,0.3,2.0,54,215
211,add,49.0,c,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F),C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F)c,50,add c at position 49,flow_matching,0.3,2.0,54,215
212,add,50.0,2,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F)c,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F)c2,51,add 2 at position 50,flow_matching,0.3,2.0,54,215
213,add,51.0,o,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F)c2,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F)c2o,52,add o at position 51,flow_matching,0.3,2.0,54,215
214,add,52.0,1,,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F)c2o,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F)c2o1,53,add 1 at position 52,flow_matching,0.3,2.0,54,215
215,add,53.0,"
",,C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F)c2o1,"C[C@@H](c1ccc([S@](C)=O)cc1)N(C)C(=O)c1cc2cccc(F)c2o1
",54,"add 
 at position 53",flow_matching,0.3,2.0,54,215
