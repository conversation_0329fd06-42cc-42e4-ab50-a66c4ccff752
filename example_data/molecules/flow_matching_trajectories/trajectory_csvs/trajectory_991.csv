step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,37,160
1,add,0.0,=,,,=,1,add = at position 0,flow_matching,0.3,2.0,37,160
2,add,1.0,+,,=,=+,2,add + at position 1,flow_matching,0.3,2.0,37,160
3,remove,0.0,=,,=+,+,1,remove = from position 0,flow_matching,0.3,2.0,37,160
4,add,0.0,/,,+,/+,2,add / at position 0,flow_matching,0.3,2.0,37,160
5,replace,0.0,O,/,/+,O+,2,replace / at position 0 with O,flow_matching,0.3,2.0,37,160
6,replace,1.0,=,+,O+,O=,2,replace + at position 1 with =,flow_matching,0.3,2.0,37,160
7,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,37,160
8,replace,0.0,O,=,=,O,1,replace = at position 0 with O,flow_matching,0.3,2.0,37,160
9,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,37,160
10,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,37,160
11,add,1.0,-,,=,=-,2,add - at position 1,flow_matching,0.3,2.0,37,160
12,remove,0.0,=,,=-,-,1,remove = from position 0,flow_matching,0.3,2.0,37,160
13,replace,0.0,O,-,-,O,1,replace - at position 0 with O,flow_matching,0.3,2.0,37,160
14,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,37,160
15,add,0.0,r,,,r,1,add r at position 0,flow_matching,0.3,2.0,37,160
16,remove,0.0,r,,r,,0,remove r from position 0,flow_matching,0.3,2.0,37,160
17,add,0.0,4,,,4,1,add 4 at position 0,flow_matching,0.3,2.0,37,160
18,add,1.0,o,,4,4o,2,add o at position 1,flow_matching,0.3,2.0,37,160
19,add,0.0,F,,4o,F4o,3,add F at position 0,flow_matching,0.3,2.0,37,160
20,add,2.0,],,F4o,F4]o,4,add ] at position 2,flow_matching,0.3,2.0,37,160
21,replace,2.0,2,],F4]o,F42o,4,replace ] at position 2 with 2,flow_matching,0.3,2.0,37,160
22,replace,2.0,),2,F42o,F4)o,4,replace 2 at position 2 with ),flow_matching,0.3,2.0,37,160
23,replace,0.0,O,F,F4)o,O4)o,4,replace F at position 0 with O,flow_matching,0.3,2.0,37,160
24,remove,2.0,),,O4)o,O4o,3,remove ) from position 2,flow_matching,0.3,2.0,37,160
25,replace,1.0,=,4,O4o,O=o,3,replace 4 at position 1 with =,flow_matching,0.3,2.0,37,160
26,add,2.0,\,,O=o,O=\o,4,add \ at position 2,flow_matching,0.3,2.0,37,160
27,add,0.0,\,,O=\o,\O=\o,5,add \ at position 0,flow_matching,0.3,2.0,37,160
28,add,5.0,H,,\O=\o,\O=\oH,6,add H at position 5,flow_matching,0.3,2.0,37,160
29,replace,0.0,O,\,\O=\oH,OO=\oH,6,replace \ at position 0 with O,flow_matching,0.3,2.0,37,160
30,remove,2.0,=,,OO=\oH,OO\oH,5,remove = from position 2,flow_matching,0.3,2.0,37,160
31,replace,1.0,+,O,OO\oH,O+\oH,5,replace O at position 1 with +,flow_matching,0.3,2.0,37,160
32,add,4.0,C,,O+\oH,O+\oCH,6,add C at position 4,flow_matching,0.3,2.0,37,160
33,replace,1.0,=,+,O+\oCH,O=\oCH,6,replace + at position 1 with =,flow_matching,0.3,2.0,37,160
34,replace,0.0,l,O,O=\oCH,l=\oCH,6,replace O at position 0 with l,flow_matching,0.3,2.0,37,160
35,replace,2.0,#,\,l=\oCH,l=#oCH,6,replace \ at position 2 with #,flow_matching,0.3,2.0,37,160
36,replace,0.0,O,l,l=#oCH,O=#oCH,6,replace l at position 0 with O,flow_matching,0.3,2.0,37,160
37,remove,4.0,C,,O=#oCH,O=#oH,5,remove C from position 4,flow_matching,0.3,2.0,37,160
38,remove,2.0,#,,O=#oH,O=oH,4,remove # from position 2,flow_matching,0.3,2.0,37,160
39,replace,2.0,C,o,O=oH,O=CH,4,replace o at position 2 with C,flow_matching,0.3,2.0,37,160
40,replace,2.0,H,C,O=CH,O=HH,4,replace C at position 2 with H,flow_matching,0.3,2.0,37,160
41,replace,2.0,C,H,O=HH,O=CH,4,replace H at position 2 with C,flow_matching,0.3,2.0,37,160
42,replace,3.0,(,H,O=CH,O=C(,4,replace H at position 3 with (,flow_matching,0.3,2.0,37,160
43,add,4.0,C,,O=C(,O=C(C,5,add C at position 4,flow_matching,0.3,2.0,37,160
44,add,5.0,O,,O=C(C,O=C(CO,6,add O at position 5,flow_matching,0.3,2.0,37,160
45,add,4.0,N,,O=C(CO,O=C(NCO,7,add N at position 4,flow_matching,0.3,2.0,37,160
46,replace,4.0,C,N,O=C(NCO,O=C(CCO,7,replace N at position 4 with C,flow_matching,0.3,2.0,37,160
47,add,5.0,H,,O=C(CCO,O=C(CHCO,8,add H at position 5,flow_matching,0.3,2.0,37,160
48,add,3.0,r,,O=C(CHCO,O=Cr(CHCO,9,add r at position 3,flow_matching,0.3,2.0,37,160
49,replace,2.0,3,C,O=Cr(CHCO,O=3r(CHCO,9,replace C at position 2 with 3,flow_matching,0.3,2.0,37,160
50,remove,6.0,H,,O=3r(CHCO,O=3r(CCO,8,remove H from position 6,flow_matching,0.3,2.0,37,160
51,remove,3.0,r,,O=3r(CCO,O=3(CCO,7,remove r from position 3,flow_matching,0.3,2.0,37,160
52,replace,0.0,5,O,O=3(CCO,5=3(CCO,7,replace O at position 0 with 5,flow_matching,0.3,2.0,37,160
53,add,5.0,5,,5=3(CCO,5=3(C5CO,8,add 5 at position 5,flow_matching,0.3,2.0,37,160
54,replace,7.0,c,O,5=3(C5CO,5=3(C5Cc,8,replace O at position 7 with c,flow_matching,0.3,2.0,37,160
55,replace,0.0,O,5,5=3(C5Cc,O=3(C5Cc,8,replace 5 at position 0 with O,flow_matching,0.3,2.0,37,160
56,replace,2.0,C,3,O=3(C5Cc,O=C(C5Cc,8,replace 3 at position 2 with C,flow_matching,0.3,2.0,37,160
57,add,0.0,2,,O=C(C5Cc,2O=C(C5Cc,9,add 2 at position 0,flow_matching,0.3,2.0,37,160
58,replace,0.0,O,2,2O=C(C5Cc,OO=C(C5Cc,9,replace 2 at position 0 with O,flow_matching,0.3,2.0,37,160
59,remove,5.0,C,,OO=C(C5Cc,OO=C(5Cc,8,remove C from position 5,flow_matching,0.3,2.0,37,160
60,replace,1.0,6,O,OO=C(5Cc,O6=C(5Cc,8,replace O at position 1 with 6,flow_matching,0.3,2.0,37,160
61,remove,3.0,C,,O6=C(5Cc,O6=(5Cc,7,remove C from position 3,flow_matching,0.3,2.0,37,160
62,replace,5.0,+,C,O6=(5Cc,O6=(5+c,7,replace C at position 5 with +,flow_matching,0.3,2.0,37,160
63,replace,1.0,=,6,O6=(5+c,O==(5+c,7,replace 6 at position 1 with =,flow_matching,0.3,2.0,37,160
64,remove,5.0,+,,O==(5+c,O==(5c,6,remove + from position 5,flow_matching,0.3,2.0,37,160
65,add,1.0,6,,O==(5c,O6==(5c,7,add 6 at position 1,flow_matching,0.3,2.0,37,160
66,remove,2.0,=,,O6==(5c,O6=(5c,6,remove = from position 2,flow_matching,0.3,2.0,37,160
67,replace,1.0,s,6,O6=(5c,Os=(5c,6,replace 6 at position 1 with s,flow_matching,0.3,2.0,37,160
68,add,2.0,F,,Os=(5c,OsF=(5c,7,add F at position 2,flow_matching,0.3,2.0,37,160
69,replace,1.0,=,s,OsF=(5c,O=F=(5c,7,replace s at position 1 with =,flow_matching,0.3,2.0,37,160
70,replace,3.0,[,=,O=F=(5c,O=F[(5c,7,replace = at position 3 with [,flow_matching,0.3,2.0,37,160
71,add,5.0,1,,O=F[(5c,O=F[(15c,8,add 1 at position 5,flow_matching,0.3,2.0,37,160
72,add,8.0,o,,O=F[(15c,O=F[(15co,9,add o at position 8,flow_matching,0.3,2.0,37,160
73,replace,2.0,C,F,O=F[(15co,O=C[(15co,9,replace F at position 2 with C,flow_matching,0.3,2.0,37,160
74,replace,3.0,4,[,O=C[(15co,O=C4(15co,9,replace [ at position 3 with 4,flow_matching,0.3,2.0,37,160
75,add,0.0,s,,O=C4(15co,sO=C4(15co,10,add s at position 0,flow_matching,0.3,2.0,37,160
76,remove,9.0,o,,sO=C4(15co,sO=C4(15c,9,remove o from position 9,flow_matching,0.3,2.0,37,160
77,remove,2.0,=,,sO=C4(15c,sOC4(15c,8,remove = from position 2,flow_matching,0.3,2.0,37,160
78,add,6.0,2,,sOC4(15c,sOC4(125c,9,add 2 at position 6,flow_matching,0.3,2.0,37,160
79,replace,6.0,(,2,sOC4(125c,sOC4(1(5c,9,replace 2 at position 6 with (,flow_matching,0.3,2.0,37,160
80,replace,0.0,O,s,sOC4(1(5c,OOC4(1(5c,9,replace s at position 0 with O,flow_matching,0.3,2.0,37,160
81,add,0.0,B,,OOC4(1(5c,BOOC4(1(5c,10,add B at position 0,flow_matching,0.3,2.0,37,160
82,add,2.0,o,,BOOC4(1(5c,BOoOC4(1(5c,11,add o at position 2,flow_matching,0.3,2.0,37,160
83,replace,0.0,O,B,BOoOC4(1(5c,OOoOC4(1(5c,11,replace B at position 0 with O,flow_matching,0.3,2.0,37,160
84,add,11.0,=,,OOoOC4(1(5c,OOoOC4(1(5c=,12,add = at position 11,flow_matching,0.3,2.0,37,160
85,remove,0.0,O,,OOoOC4(1(5c=,OoOC4(1(5c=,11,remove O from position 0,flow_matching,0.3,2.0,37,160
86,replace,3.0,4,C,OoOC4(1(5c=,OoO44(1(5c=,11,replace C at position 3 with 4,flow_matching,0.3,2.0,37,160
87,remove,6.0,1,,OoO44(1(5c=,OoO44((5c=,10,remove 1 from position 6,flow_matching,0.3,2.0,37,160
88,add,6.0,[,,OoO44((5c=,OoO44([(5c=,11,add [ at position 6,flow_matching,0.3,2.0,37,160
89,replace,1.0,=,o,OoO44([(5c=,O=O44([(5c=,11,replace o at position 1 with =,flow_matching,0.3,2.0,37,160
90,replace,9.0,#,c,O=O44([(5c=,O=O44([(5#=,11,replace c at position 9 with #,flow_matching,0.3,2.0,37,160
91,add,6.0,\,,O=O44([(5#=,O=O44(\[(5#=,12,add \ at position 6,flow_matching,0.3,2.0,37,160
92,remove,0.0,O,,O=O44(\[(5#=,=O44(\[(5#=,11,remove O from position 0,flow_matching,0.3,2.0,37,160
93,replace,0.0,O,=,=O44(\[(5#=,OO44(\[(5#=,11,replace = at position 0 with O,flow_matching,0.3,2.0,37,160
94,replace,1.0,=,O,OO44(\[(5#=,O=44(\[(5#=,11,replace O at position 1 with =,flow_matching,0.3,2.0,37,160
95,replace,2.0,),4,O=44(\[(5#=,O=)4(\[(5#=,11,replace 4 at position 2 with ),flow_matching,0.3,2.0,37,160
96,add,1.0,\,,O=)4(\[(5#=,O\=)4(\[(5#=,12,add \ at position 1,flow_matching,0.3,2.0,37,160
97,add,2.0,F,,O\=)4(\[(5#=,O\F=)4(\[(5#=,13,add F at position 2,flow_matching,0.3,2.0,37,160
98,replace,1.0,=,\,O\F=)4(\[(5#=,O=F=)4(\[(5#=,13,replace \ at position 1 with =,flow_matching,0.3,2.0,37,160
99,add,5.0,\,,O=F=)4(\[(5#=,O=F=)\4(\[(5#=,14,add \ at position 5,flow_matching,0.3,2.0,37,160
100,remove,5.0,\,,O=F=)\4(\[(5#=,O=F=)4(\[(5#=,13,remove \ from position 5,flow_matching,0.3,2.0,37,160
101,remove,3.0,=,,O=F=)4(\[(5#=,O=F)4(\[(5#=,12,remove = from position 3,flow_matching,0.3,2.0,37,160
102,replace,2.0,C,F,O=F)4(\[(5#=,O=C)4(\[(5#=,12,replace F at position 2 with C,flow_matching,0.3,2.0,37,160
103,replace,3.0,(,),O=C)4(\[(5#=,O=C(4(\[(5#=,12,replace ) at position 3 with (,flow_matching,0.3,2.0,37,160
104,remove,8.0,(,,O=C(4(\[(5#=,O=C(4(\[5#=,11,remove ( from position 8,flow_matching,0.3,2.0,37,160
105,add,4.0,4,,O=C(4(\[5#=,O=C(44(\[5#=,12,add 4 at position 4,flow_matching,0.3,2.0,37,160
106,add,12.0,],,O=C(44(\[5#=,O=C(44(\[5#=],13,add ] at position 12,flow_matching,0.3,2.0,37,160
107,replace,6.0,F,(,O=C(44(\[5#=],O=C(44F\[5#=],13,replace ( at position 6 with F,flow_matching,0.3,2.0,37,160
108,replace,2.0,),C,O=C(44F\[5#=],O=)(44F\[5#=],13,replace C at position 2 with ),flow_matching,0.3,2.0,37,160
109,replace,2.0,C,),O=)(44F\[5#=],O=C(44F\[5#=],13,replace ) at position 2 with C,flow_matching,0.3,2.0,37,160
110,replace,4.0,C,4,O=C(44F\[5#=],O=C(C4F\[5#=],13,replace 4 at position 4 with C,flow_matching,0.3,2.0,37,160
111,remove,6.0,F,,O=C(C4F\[5#=],O=C(C4\[5#=],12,remove F from position 6,flow_matching,0.3,2.0,37,160
112,replace,9.0,B,#,O=C(C4\[5#=],O=C(C4\[5B=],12,replace # at position 9 with B,flow_matching,0.3,2.0,37,160
113,replace,5.0,O,4,O=C(C4\[5B=],O=C(CO\[5B=],12,replace 4 at position 5 with O,flow_matching,0.3,2.0,37,160
114,replace,6.0,c,\,O=C(CO\[5B=],O=C(COc[5B=],12,replace \ at position 6 with c,flow_matching,0.3,2.0,37,160
115,remove,10.0,=,,O=C(COc[5B=],O=C(COc[5B],11,remove = from position 10,flow_matching,0.3,2.0,37,160
116,replace,7.0,1,[,O=C(COc[5B],O=C(COc15B],11,replace [ at position 7 with 1,flow_matching,0.3,2.0,37,160
117,replace,8.0,n,5,O=C(COc15B],O=C(COc1nB],11,replace 5 at position 8 with n,flow_matching,0.3,2.0,37,160
118,replace,9.0,c,B,O=C(COc1nB],O=C(COc1nc],11,replace B at position 9 with c,flow_matching,0.3,2.0,37,160
119,remove,3.0,(,,O=C(COc1nc],O=CCOc1nc],10,remove ( from position 3,flow_matching,0.3,2.0,37,160
120,replace,3.0,(,C,O=CCOc1nc],O=C(Oc1nc],10,replace C at position 3 with (,flow_matching,0.3,2.0,37,160
121,replace,4.0,C,O,O=C(Oc1nc],O=C(Cc1nc],10,replace O at position 4 with C,flow_matching,0.3,2.0,37,160
122,replace,5.0,O,c,O=C(Cc1nc],O=C(CO1nc],10,replace c at position 5 with O,flow_matching,0.3,2.0,37,160
123,add,4.0,1,,O=C(CO1nc],O=C(1CO1nc],11,add 1 at position 4,flow_matching,0.3,2.0,37,160
124,replace,4.0,5,1,O=C(1CO1nc],O=C(5CO1nc],11,replace 1 at position 4 with 5,flow_matching,0.3,2.0,37,160
125,add,11.0,-,,O=C(5CO1nc],O=C(5CO1nc]-,12,add - at position 11,flow_matching,0.3,2.0,37,160
126,replace,10.0,4,],O=C(5CO1nc]-,O=C(5CO1nc4-,12,replace ] at position 10 with 4,flow_matching,0.3,2.0,37,160
127,remove,2.0,C,,O=C(5CO1nc4-,O=(5CO1nc4-,11,remove C from position 2,flow_matching,0.3,2.0,37,160
128,replace,2.0,C,(,O=(5CO1nc4-,O=C5CO1nc4-,11,replace ( at position 2 with C,flow_matching,0.3,2.0,37,160
129,replace,3.0,(,5,O=C5CO1nc4-,O=C(CO1nc4-,11,replace 5 at position 3 with (,flow_matching,0.3,2.0,37,160
130,replace,6.0,c,1,O=C(CO1nc4-,O=C(COcnc4-,11,replace 1 at position 6 with c,flow_matching,0.3,2.0,37,160
131,replace,7.0,1,n,O=C(COcnc4-,O=C(COc1c4-,11,replace n at position 7 with 1,flow_matching,0.3,2.0,37,160
132,replace,8.0,n,c,O=C(COc1c4-,O=C(COc1n4-,11,replace c at position 8 with n,flow_matching,0.3,2.0,37,160
133,replace,9.0,c,4,O=C(COc1n4-,O=C(COc1nc-,11,replace 4 at position 9 with c,flow_matching,0.3,2.0,37,160
134,replace,10.0,n,-,O=C(COc1nc-,O=C(COc1ncn,11,replace - at position 10 with n,flow_matching,0.3,2.0,37,160
135,add,11.0,c,,O=C(COc1ncn,O=C(COc1ncnc,12,add c at position 11,flow_matching,0.3,2.0,37,160
136,add,12.0,2,,O=C(COc1ncnc,O=C(COc1ncnc2,13,add 2 at position 12,flow_matching,0.3,2.0,37,160
137,add,13.0,c,,O=C(COc1ncnc2,O=C(COc1ncnc2c,14,add c at position 13,flow_matching,0.3,2.0,37,160
138,add,14.0,c,,O=C(COc1ncnc2c,O=C(COc1ncnc2cc,15,add c at position 14,flow_matching,0.3,2.0,37,160
139,add,15.0,c,,O=C(COc1ncnc2cc,O=C(COc1ncnc2ccc,16,add c at position 15,flow_matching,0.3,2.0,37,160
140,add,16.0,(,,O=C(COc1ncnc2ccc,O=C(COc1ncnc2ccc(,17,add ( at position 16,flow_matching,0.3,2.0,37,160
141,add,17.0,B,,O=C(COc1ncnc2ccc(,O=C(COc1ncnc2ccc(B,18,add B at position 17,flow_matching,0.3,2.0,37,160
142,add,18.0,r,,O=C(COc1ncnc2ccc(B,O=C(COc1ncnc2ccc(Br,19,add r at position 18,flow_matching,0.3,2.0,37,160
143,add,19.0,),,O=C(COc1ncnc2ccc(Br,O=C(COc1ncnc2ccc(Br),20,add ) at position 19,flow_matching,0.3,2.0,37,160
144,add,20.0,c,,O=C(COc1ncnc2ccc(Br),O=C(COc1ncnc2ccc(Br)c,21,add c at position 20,flow_matching,0.3,2.0,37,160
145,add,21.0,c,,O=C(COc1ncnc2ccc(Br)c,O=C(COc1ncnc2ccc(Br)cc,22,add c at position 21,flow_matching,0.3,2.0,37,160
146,add,22.0,1,,O=C(COc1ncnc2ccc(Br)cc,O=C(COc1ncnc2ccc(Br)cc1,23,add 1 at position 22,flow_matching,0.3,2.0,37,160
147,add,23.0,2,,O=C(COc1ncnc2ccc(Br)cc1,O=C(COc1ncnc2ccc(Br)cc12,24,add 2 at position 23,flow_matching,0.3,2.0,37,160
148,add,24.0,),,O=C(COc1ncnc2ccc(Br)cc12,O=C(COc1ncnc2ccc(Br)cc12),25,add ) at position 24,flow_matching,0.3,2.0,37,160
149,add,25.0,N,,O=C(COc1ncnc2ccc(Br)cc12),O=C(COc1ncnc2ccc(Br)cc12)N,26,add N at position 25,flow_matching,0.3,2.0,37,160
150,add,26.0,c,,O=C(COc1ncnc2ccc(Br)cc12)N,O=C(COc1ncnc2ccc(Br)cc12)Nc,27,add c at position 26,flow_matching,0.3,2.0,37,160
151,add,27.0,1,,O=C(COc1ncnc2ccc(Br)cc12)Nc,O=C(COc1ncnc2ccc(Br)cc12)Nc1,28,add 1 at position 27,flow_matching,0.3,2.0,37,160
152,add,28.0,c,,O=C(COc1ncnc2ccc(Br)cc12)Nc1,O=C(COc1ncnc2ccc(Br)cc12)Nc1c,29,add c at position 28,flow_matching,0.3,2.0,37,160
153,add,29.0,c,,O=C(COc1ncnc2ccc(Br)cc12)Nc1c,O=C(COc1ncnc2ccc(Br)cc12)Nc1cc,30,add c at position 29,flow_matching,0.3,2.0,37,160
154,add,30.0,c,,O=C(COc1ncnc2ccc(Br)cc12)Nc1cc,O=C(COc1ncnc2ccc(Br)cc12)Nc1ccc,31,add c at position 30,flow_matching,0.3,2.0,37,160
155,add,31.0,c,,O=C(COc1ncnc2ccc(Br)cc12)Nc1ccc,O=C(COc1ncnc2ccc(Br)cc12)Nc1cccc,32,add c at position 31,flow_matching,0.3,2.0,37,160
156,add,32.0,c,,O=C(COc1ncnc2ccc(Br)cc12)Nc1cccc,O=C(COc1ncnc2ccc(Br)cc12)Nc1ccccc,33,add c at position 32,flow_matching,0.3,2.0,37,160
157,add,33.0,1,,O=C(COc1ncnc2ccc(Br)cc12)Nc1ccccc,O=C(COc1ncnc2ccc(Br)cc12)Nc1ccccc1,34,add 1 at position 33,flow_matching,0.3,2.0,37,160
158,add,34.0,C,,O=C(COc1ncnc2ccc(Br)cc12)Nc1ccccc1,O=C(COc1ncnc2ccc(Br)cc12)Nc1ccccc1C,35,add C at position 34,flow_matching,0.3,2.0,37,160
159,add,35.0,l,,O=C(COc1ncnc2ccc(Br)cc12)Nc1ccccc1C,O=C(COc1ncnc2ccc(Br)cc12)Nc1ccccc1Cl,36,add l at position 35,flow_matching,0.3,2.0,37,160
160,add,36.0,"
",,O=C(COc1ncnc2ccc(Br)cc12)Nc1ccccc1Cl,"O=C(COc1ncnc2ccc(Br)cc12)Nc1ccccc1Cl
",37,"add 
 at position 36",flow_matching,0.3,2.0,37,160
