step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,45,212
1,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,45,212
2,remove,0.0,7,,7,,0,remove 7 from position 0,flow_matching,0.3,2.0,45,212
3,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,45,212
4,replace,0.0,O,I,I,O,1,replace I at position 0 with O,flow_matching,0.3,2.0,45,212
5,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,45,212
6,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,45,212
7,replace,0.0,O,=,=,O,1,replace = at position 0 with O,flow_matching,0.3,2.0,45,212
8,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,45,212
9,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,45,212
10,remove,0.0,=,,=,,0,remove = from position 0,flow_matching,0.3,2.0,45,212
11,add,0.0,4,,,4,1,add 4 at position 0,flow_matching,0.3,2.0,45,212
12,add,1.0,S,,4,4S,2,add S at position 1,flow_matching,0.3,2.0,45,212
13,remove,1.0,S,,4S,4,1,remove S from position 1,flow_matching,0.3,2.0,45,212
14,remove,0.0,4,,4,,0,remove 4 from position 0,flow_matching,0.3,2.0,45,212
15,add,0.0,H,,,H,1,add H at position 0,flow_matching,0.3,2.0,45,212
16,replace,0.0,=,H,H,=,1,replace H at position 0 with =,flow_matching,0.3,2.0,45,212
17,replace,0.0,O,=,=,O,1,replace = at position 0 with O,flow_matching,0.3,2.0,45,212
18,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,45,212
19,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,45,212
20,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,45,212
21,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,45,212
22,replace,0.0,O,6,6,O,1,replace 6 at position 0 with O,flow_matching,0.3,2.0,45,212
23,add,0.0,o,,O,oO,2,add o at position 0,flow_matching,0.3,2.0,45,212
24,remove,0.0,o,,oO,O,1,remove o from position 0,flow_matching,0.3,2.0,45,212
25,add,0.0,C,,O,CO,2,add C at position 0,flow_matching,0.3,2.0,45,212
26,add,2.0,6,,CO,CO6,3,add 6 at position 2,flow_matching,0.3,2.0,45,212
27,remove,0.0,C,,CO6,O6,2,remove C from position 0,flow_matching,0.3,2.0,45,212
28,remove,1.0,6,,O6,O,1,remove 6 from position 1,flow_matching,0.3,2.0,45,212
29,replace,0.0,F,O,O,F,1,replace O at position 0 with F,flow_matching,0.3,2.0,45,212
30,replace,0.0,O,F,F,O,1,replace F at position 0 with O,flow_matching,0.3,2.0,45,212
31,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,45,212
32,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,45,212
33,add,1.0,N,,/,/N,2,add N at position 1,flow_matching,0.3,2.0,45,212
34,replace,0.0,O,/,/N,ON,2,replace / at position 0 with O,flow_matching,0.3,2.0,45,212
35,replace,1.0,F,N,ON,OF,2,replace N at position 1 with F,flow_matching,0.3,2.0,45,212
36,add,1.0,3,,OF,O3F,3,add 3 at position 1,flow_matching,0.3,2.0,45,212
37,add,0.0,r,,O3F,rO3F,4,add r at position 0,flow_matching,0.3,2.0,45,212
38,replace,0.0,3,r,rO3F,3O3F,4,replace r at position 0 with 3,flow_matching,0.3,2.0,45,212
39,add,0.0,2,,3O3F,23O3F,5,add 2 at position 0,flow_matching,0.3,2.0,45,212
40,replace,0.0,O,2,23O3F,O3O3F,5,replace 2 at position 0 with O,flow_matching,0.3,2.0,45,212
41,add,2.0,S,,O3O3F,O3SO3F,6,add S at position 2,flow_matching,0.3,2.0,45,212
42,replace,1.0,=,3,O3SO3F,O=SO3F,6,replace 3 at position 1 with =,flow_matching,0.3,2.0,45,212
43,remove,2.0,S,,O=SO3F,O=O3F,5,remove S from position 2,flow_matching,0.3,2.0,45,212
44,remove,2.0,O,,O=O3F,O=3F,4,remove O from position 2,flow_matching,0.3,2.0,45,212
45,add,4.0,r,,O=3F,O=3Fr,5,add r at position 4,flow_matching,0.3,2.0,45,212
46,replace,2.0,C,3,O=3Fr,O=CFr,5,replace 3 at position 2 with C,flow_matching,0.3,2.0,45,212
47,replace,3.0,1,F,O=CFr,O=C1r,5,replace F at position 3 with 1,flow_matching,0.3,2.0,45,212
48,add,1.0,F,,O=C1r,OF=C1r,6,add F at position 1,flow_matching,0.3,2.0,45,212
49,replace,1.0,N,F,OF=C1r,ON=C1r,6,replace F at position 1 with N,flow_matching,0.3,2.0,45,212
50,remove,0.0,O,,ON=C1r,N=C1r,5,remove O from position 0,flow_matching,0.3,2.0,45,212
51,replace,0.0,O,N,N=C1r,O=C1r,5,replace N at position 0 with O,flow_matching,0.3,2.0,45,212
52,replace,2.0,2,C,O=C1r,O=21r,5,replace C at position 2 with 2,flow_matching,0.3,2.0,45,212
53,replace,2.0,C,2,O=21r,O=C1r,5,replace 2 at position 2 with C,flow_matching,0.3,2.0,45,212
54,remove,4.0,r,,O=C1r,O=C1,4,remove r from position 4,flow_matching,0.3,2.0,45,212
55,replace,3.0,O,1,O=C1,O=CO,4,replace 1 at position 3 with O,flow_matching,0.3,2.0,45,212
56,replace,3.0,1,O,O=CO,O=C1,4,replace O at position 3 with 1,flow_matching,0.3,2.0,45,212
57,remove,3.0,1,,O=C1,O=C,3,remove 1 from position 3,flow_matching,0.3,2.0,45,212
58,add,1.0,2,,O=C,O2=C,4,add 2 at position 1,flow_matching,0.3,2.0,45,212
59,remove,3.0,C,,O2=C,O2=,3,remove C from position 3,flow_matching,0.3,2.0,45,212
60,add,3.0,@,,O2=,O2=@,4,add @ at position 3,flow_matching,0.3,2.0,45,212
61,remove,2.0,=,,O2=@,O2@,3,remove = from position 2,flow_matching,0.3,2.0,45,212
62,add,1.0,=,,O2@,O=2@,4,add = at position 1,flow_matching,0.3,2.0,45,212
63,replace,2.0,C,2,O=2@,O=C@,4,replace 2 at position 2 with C,flow_matching,0.3,2.0,45,212
64,replace,3.0,1,@,O=C@,O=C1,4,replace @ at position 3 with 1,flow_matching,0.3,2.0,45,212
65,add,4.0,N,,O=C1,O=C1N,5,add N at position 4,flow_matching,0.3,2.0,45,212
66,add,0.0,n,,O=C1N,nO=C1N,6,add n at position 0,flow_matching,0.3,2.0,45,212
67,replace,0.0,O,n,nO=C1N,OO=C1N,6,replace n at position 0 with O,flow_matching,0.3,2.0,45,212
68,remove,2.0,=,,OO=C1N,OOC1N,5,remove = from position 2,flow_matching,0.3,2.0,45,212
69,replace,4.0,1,N,OOC1N,OOC11,5,replace N at position 4 with 1,flow_matching,0.3,2.0,45,212
70,remove,2.0,C,,OOC11,OO11,4,remove C from position 2,flow_matching,0.3,2.0,45,212
71,replace,3.0,4,1,OO11,OO14,4,replace 1 at position 3 with 4,flow_matching,0.3,2.0,45,212
72,replace,1.0,=,O,OO14,O=14,4,replace O at position 1 with =,flow_matching,0.3,2.0,45,212
73,replace,2.0,C,1,O=14,O=C4,4,replace 1 at position 2 with C,flow_matching,0.3,2.0,45,212
74,remove,2.0,C,,O=C4,O=4,3,remove C from position 2,flow_matching,0.3,2.0,45,212
75,add,3.0,\,,O=4,O=4\,4,add \ at position 3,flow_matching,0.3,2.0,45,212
76,add,4.0,4,,O=4\,O=4\4,5,add 4 at position 4,flow_matching,0.3,2.0,45,212
77,replace,2.0,C,4,O=4\4,O=C\4,5,replace 4 at position 2 with C,flow_matching,0.3,2.0,45,212
78,replace,4.0,-,4,O=C\4,O=C\-,5,replace 4 at position 4 with -,flow_matching,0.3,2.0,45,212
79,replace,3.0,1,\,O=C\-,O=C1-,5,replace \ at position 3 with 1,flow_matching,0.3,2.0,45,212
80,add,4.0,c,,O=C1-,O=C1c-,6,add c at position 4,flow_matching,0.3,2.0,45,212
81,remove,3.0,1,,O=C1c-,O=Cc-,5,remove 1 from position 3,flow_matching,0.3,2.0,45,212
82,add,3.0,3,,O=Cc-,O=C3c-,6,add 3 at position 3,flow_matching,0.3,2.0,45,212
83,replace,1.0,N,=,O=C3c-,ONC3c-,6,replace = at position 1 with N,flow_matching,0.3,2.0,45,212
84,replace,1.0,=,N,ONC3c-,O=C3c-,6,replace N at position 1 with =,flow_matching,0.3,2.0,45,212
85,replace,5.0,],-,O=C3c-,O=C3c],6,replace - at position 5 with ],flow_matching,0.3,2.0,45,212
86,remove,3.0,3,,O=C3c],O=Cc],5,remove 3 from position 3,flow_matching,0.3,2.0,45,212
87,remove,0.0,O,,O=Cc],=Cc],4,remove O from position 0,flow_matching,0.3,2.0,45,212
88,remove,3.0,],,=Cc],=Cc,3,remove ] from position 3,flow_matching,0.3,2.0,45,212
89,replace,0.0,O,=,=Cc,OCc,3,replace = at position 0 with O,flow_matching,0.3,2.0,45,212
90,replace,1.0,=,C,OCc,O=c,3,replace C at position 1 with =,flow_matching,0.3,2.0,45,212
91,add,1.0,7,,O=c,O7=c,4,add 7 at position 1,flow_matching,0.3,2.0,45,212
92,replace,1.0,=,7,O7=c,O==c,4,replace 7 at position 1 with =,flow_matching,0.3,2.0,45,212
93,add,2.0,F,,O==c,O=F=c,5,add F at position 2,flow_matching,0.3,2.0,45,212
94,replace,0.0,+,O,O=F=c,+=F=c,5,replace O at position 0 with +,flow_matching,0.3,2.0,45,212
95,remove,3.0,=,,+=F=c,+=Fc,4,remove = from position 3,flow_matching,0.3,2.0,45,212
96,add,2.0,6,,+=Fc,+=6Fc,5,add 6 at position 2,flow_matching,0.3,2.0,45,212
97,replace,0.0,O,+,+=6Fc,O=6Fc,5,replace + at position 0 with O,flow_matching,0.3,2.0,45,212
98,replace,3.0,H,F,O=6Fc,O=6Hc,5,replace F at position 3 with H,flow_matching,0.3,2.0,45,212
99,remove,0.0,O,,O=6Hc,=6Hc,4,remove O from position 0,flow_matching,0.3,2.0,45,212
100,replace,1.0,c,6,=6Hc,=cHc,4,replace 6 at position 1 with c,flow_matching,0.3,2.0,45,212
101,remove,0.0,=,,=cHc,cHc,3,remove = from position 0,flow_matching,0.3,2.0,45,212
102,replace,0.0,O,c,cHc,OHc,3,replace c at position 0 with O,flow_matching,0.3,2.0,45,212
103,add,2.0,[,,OHc,OH[c,4,add [ at position 2,flow_matching,0.3,2.0,45,212
104,replace,2.0,H,[,OH[c,OHHc,4,replace [ at position 2 with H,flow_matching,0.3,2.0,45,212
105,add,4.0,B,,OHHc,OHHcB,5,add B at position 4,flow_matching,0.3,2.0,45,212
106,replace,0.0,1,O,OHHcB,1HHcB,5,replace O at position 0 with 1,flow_matching,0.3,2.0,45,212
107,remove,3.0,c,,1HHcB,1HHB,4,remove c from position 3,flow_matching,0.3,2.0,45,212
108,remove,3.0,B,,1HHB,1HH,3,remove B from position 3,flow_matching,0.3,2.0,45,212
109,remove,1.0,H,,1HH,1H,2,remove H from position 1,flow_matching,0.3,2.0,45,212
110,add,0.0,[,,1H,[1H,3,add [ at position 0,flow_matching,0.3,2.0,45,212
111,remove,0.0,[,,[1H,1H,2,remove [ from position 0,flow_matching,0.3,2.0,45,212
112,add,1.0,C,,1H,1CH,3,add C at position 1,flow_matching,0.3,2.0,45,212
113,add,2.0,@,,1CH,1C@H,4,add @ at position 2,flow_matching,0.3,2.0,45,212
114,replace,0.0,s,1,1C@H,sC@H,4,replace 1 at position 0 with s,flow_matching,0.3,2.0,45,212
115,remove,2.0,@,,sC@H,sCH,3,remove @ from position 2,flow_matching,0.3,2.0,45,212
116,replace,0.0,O,s,sCH,OCH,3,replace s at position 0 with O,flow_matching,0.3,2.0,45,212
117,replace,0.0,1,O,OCH,1CH,3,replace O at position 0 with 1,flow_matching,0.3,2.0,45,212
118,add,3.0,],,1CH,1CH],4,add ] at position 3,flow_matching,0.3,2.0,45,212
119,replace,0.0,O,1,1CH],OCH],4,replace 1 at position 0 with O,flow_matching,0.3,2.0,45,212
120,add,0.0,/,,OCH],/OCH],5,add / at position 0,flow_matching,0.3,2.0,45,212
121,replace,0.0,O,/,/OCH],OOCH],5,replace / at position 0 with O,flow_matching,0.3,2.0,45,212
122,replace,1.0,=,O,OOCH],O=CH],5,replace O at position 1 with =,flow_matching,0.3,2.0,45,212
123,replace,2.0,4,C,O=CH],O=4H],5,replace C at position 2 with 4,flow_matching,0.3,2.0,45,212
124,replace,2.0,C,4,O=4H],O=CH],5,replace 4 at position 2 with C,flow_matching,0.3,2.0,45,212
125,replace,3.0,1,H,O=CH],O=C1],5,replace H at position 3 with 1,flow_matching,0.3,2.0,45,212
126,remove,1.0,=,,O=C1],OC1],4,remove = from position 1,flow_matching,0.3,2.0,45,212
127,add,4.0,#,,OC1],OC1]#,5,add # at position 4,flow_matching,0.3,2.0,45,212
128,replace,1.0,=,C,OC1]#,O=1]#,5,replace C at position 1 with =,flow_matching,0.3,2.0,45,212
129,remove,3.0,],,O=1]#,O=1#,4,remove ] from position 3,flow_matching,0.3,2.0,45,212
130,replace,2.0,C,1,O=1#,O=C#,4,replace 1 at position 2 with C,flow_matching,0.3,2.0,45,212
131,replace,2.0,l,C,O=C#,O=l#,4,replace C at position 2 with l,flow_matching,0.3,2.0,45,212
132,remove,2.0,l,,O=l#,O=#,3,remove l from position 2,flow_matching,0.3,2.0,45,212
133,replace,2.0,N,#,O=#,O=N,3,replace # at position 2 with N,flow_matching,0.3,2.0,45,212
134,add,3.0,@,,O=N,O=N@,4,add @ at position 3,flow_matching,0.3,2.0,45,212
135,replace,3.0,B,@,O=N@,O=NB,4,replace @ at position 3 with B,flow_matching,0.3,2.0,45,212
136,remove,3.0,B,,O=NB,O=N,3,remove B from position 3,flow_matching,0.3,2.0,45,212
137,add,1.0,),,O=N,O)=N,4,add ) at position 1,flow_matching,0.3,2.0,45,212
138,add,0.0,6,,O)=N,6O)=N,5,add 6 at position 0,flow_matching,0.3,2.0,45,212
139,remove,3.0,=,,6O)=N,6O)N,4,remove = from position 3,flow_matching,0.3,2.0,45,212
140,remove,0.0,6,,6O)N,O)N,3,remove 6 from position 0,flow_matching,0.3,2.0,45,212
141,replace,2.0,H,N,O)N,O)H,3,replace N at position 2 with H,flow_matching,0.3,2.0,45,212
142,add,2.0,[,,O)H,O)[H,4,add [ at position 2,flow_matching,0.3,2.0,45,212
143,remove,2.0,[,,O)[H,O)H,3,remove [ from position 2,flow_matching,0.3,2.0,45,212
144,remove,2.0,H,,O)H,O),2,remove H from position 2,flow_matching,0.3,2.0,45,212
145,replace,1.0,=,),O),O=,2,replace ) at position 1 with =,flow_matching,0.3,2.0,45,212
146,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,45,212
147,replace,0.0,O,=,=,O,1,replace = at position 0 with O,flow_matching,0.3,2.0,45,212
148,add,0.0,o,,O,oO,2,add o at position 0,flow_matching,0.3,2.0,45,212
149,add,0.0,c,,oO,coO,3,add c at position 0,flow_matching,0.3,2.0,45,212
150,replace,0.0,O,c,coO,OoO,3,replace c at position 0 with O,flow_matching,0.3,2.0,45,212
151,remove,1.0,o,,OoO,OO,2,remove o from position 1,flow_matching,0.3,2.0,45,212
152,remove,1.0,O,,OO,O,1,remove O from position 1,flow_matching,0.3,2.0,45,212
153,add,1.0,r,,O,Or,2,add r at position 1,flow_matching,0.3,2.0,45,212
154,replace,1.0,=,r,Or,O=,2,replace r at position 1 with =,flow_matching,0.3,2.0,45,212
155,add,1.0,H,,O=,OH=,3,add H at position 1,flow_matching,0.3,2.0,45,212
156,replace,0.0,n,O,OH=,nH=,3,replace O at position 0 with n,flow_matching,0.3,2.0,45,212
157,add,3.0,+,,nH=,nH=+,4,add + at position 3,flow_matching,0.3,2.0,45,212
158,replace,0.0,O,n,nH=+,OH=+,4,replace n at position 0 with O,flow_matching,0.3,2.0,45,212
159,replace,1.0,=,H,OH=+,O==+,4,replace H at position 1 with =,flow_matching,0.3,2.0,45,212
160,replace,3.0,o,+,O==+,O==o,4,replace + at position 3 with o,flow_matching,0.3,2.0,45,212
161,replace,3.0,],o,O==o,O==],4,replace o at position 3 with ],flow_matching,0.3,2.0,45,212
162,add,2.0,n,,O==],O=n=],5,add n at position 2,flow_matching,0.3,2.0,45,212
163,add,4.0,C,,O=n=],O=n=C],6,add C at position 4,flow_matching,0.3,2.0,45,212
164,remove,3.0,=,,O=n=C],O=nC],5,remove = from position 3,flow_matching,0.3,2.0,45,212
165,add,5.0,B,,O=nC],O=nC]B,6,add B at position 5,flow_matching,0.3,2.0,45,212
166,replace,2.0,C,n,O=nC]B,O=CC]B,6,replace n at position 2 with C,flow_matching,0.3,2.0,45,212
167,add,2.0,),,O=CC]B,O=)CC]B,7,add ) at position 2,flow_matching,0.3,2.0,45,212
168,add,0.0,[,,O=)CC]B,[O=)CC]B,8,add [ at position 0,flow_matching,0.3,2.0,45,212
169,replace,0.0,O,[,[O=)CC]B,OO=)CC]B,8,replace [ at position 0 with O,flow_matching,0.3,2.0,45,212
170,replace,1.0,=,O,OO=)CC]B,O==)CC]B,8,replace O at position 1 with =,flow_matching,0.3,2.0,45,212
171,replace,2.0,C,=,O==)CC]B,O=C)CC]B,8,replace = at position 2 with C,flow_matching,0.3,2.0,45,212
172,replace,3.0,1,),O=C)CC]B,O=C1CC]B,8,replace ) at position 3 with 1,flow_matching,0.3,2.0,45,212
173,replace,4.0,N,C,O=C1CC]B,O=C1NC]B,8,replace C at position 4 with N,flow_matching,0.3,2.0,45,212
174,replace,6.0,(,],O=C1NC]B,O=C1NC(B,8,replace ] at position 6 with (,flow_matching,0.3,2.0,45,212
175,replace,7.0,=,B,O=C1NC(B,O=C1NC(=,8,replace B at position 7 with =,flow_matching,0.3,2.0,45,212
176,add,8.0,S,,O=C1NC(=,O=C1NC(=S,9,add S at position 8,flow_matching,0.3,2.0,45,212
177,add,9.0,),,O=C1NC(=S,O=C1NC(=S),10,add ) at position 9,flow_matching,0.3,2.0,45,212
178,add,10.0,N,,O=C1NC(=S),O=C1NC(=S)N,11,add N at position 10,flow_matching,0.3,2.0,45,212
179,add,11.0,C,,O=C1NC(=S)N,O=C1NC(=S)NC,12,add C at position 11,flow_matching,0.3,2.0,45,212
180,add,12.0,(,,O=C1NC(=S)NC,O=C1NC(=S)NC(,13,add ( at position 12,flow_matching,0.3,2.0,45,212
181,add,13.0,=,,O=C1NC(=S)NC(,O=C1NC(=S)NC(=,14,add = at position 13,flow_matching,0.3,2.0,45,212
182,add,14.0,O,,O=C1NC(=S)NC(=,O=C1NC(=S)NC(=O,15,add O at position 14,flow_matching,0.3,2.0,45,212
183,add,15.0,),,O=C1NC(=S)NC(=O,O=C1NC(=S)NC(=O),16,add ) at position 15,flow_matching,0.3,2.0,45,212
184,add,16.0,C,,O=C1NC(=S)NC(=O),O=C1NC(=S)NC(=O)C,17,add C at position 16,flow_matching,0.3,2.0,45,212
185,add,17.0,1,,O=C1NC(=S)NC(=O)C,O=C1NC(=S)NC(=O)C1,18,add 1 at position 17,flow_matching,0.3,2.0,45,212
186,add,18.0,=,,O=C1NC(=S)NC(=O)C1,O=C1NC(=S)NC(=O)C1=,19,add = at position 18,flow_matching,0.3,2.0,45,212
187,add,19.0,C,,O=C1NC(=S)NC(=O)C1=,O=C1NC(=S)NC(=O)C1=C,20,add C at position 19,flow_matching,0.3,2.0,45,212
188,add,20.0,N,,O=C1NC(=S)NC(=O)C1=C,O=C1NC(=S)NC(=O)C1=CN,21,add N at position 20,flow_matching,0.3,2.0,45,212
189,add,21.0,c,,O=C1NC(=S)NC(=O)C1=CN,O=C1NC(=S)NC(=O)C1=CNc,22,add c at position 21,flow_matching,0.3,2.0,45,212
190,add,22.0,1,,O=C1NC(=S)NC(=O)C1=CNc,O=C1NC(=S)NC(=O)C1=CNc1,23,add 1 at position 22,flow_matching,0.3,2.0,45,212
191,add,23.0,c,,O=C1NC(=S)NC(=O)C1=CNc1,O=C1NC(=S)NC(=O)C1=CNc1c,24,add c at position 23,flow_matching,0.3,2.0,45,212
192,add,24.0,c,,O=C1NC(=S)NC(=O)C1=CNc1c,O=C1NC(=S)NC(=O)C1=CNc1cc,25,add c at position 24,flow_matching,0.3,2.0,45,212
193,add,25.0,c,,O=C1NC(=S)NC(=O)C1=CNc1cc,O=C1NC(=S)NC(=O)C1=CNc1ccc,26,add c at position 25,flow_matching,0.3,2.0,45,212
194,add,26.0,(,,O=C1NC(=S)NC(=O)C1=CNc1ccc,O=C1NC(=S)NC(=O)C1=CNc1ccc(,27,add ( at position 26,flow_matching,0.3,2.0,45,212
195,add,27.0,[,,O=C1NC(=S)NC(=O)C1=CNc1ccc(,O=C1NC(=S)NC(=O)C1=CNc1ccc([,28,add [ at position 27,flow_matching,0.3,2.0,45,212
196,add,28.0,N,,O=C1NC(=S)NC(=O)C1=CNc1ccc([,O=C1NC(=S)NC(=O)C1=CNc1ccc([N,29,add N at position 28,flow_matching,0.3,2.0,45,212
197,add,29.0,+,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+,30,add + at position 29,flow_matching,0.3,2.0,45,212
198,add,30.0,],,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+],31,add ] at position 30,flow_matching,0.3,2.0,45,212
199,add,31.0,(,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+],O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](,32,add ( at position 31,flow_matching,0.3,2.0,45,212
200,add,32.0,=,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=,33,add = at position 32,flow_matching,0.3,2.0,45,212
201,add,33.0,O,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O,34,add O at position 33,flow_matching,0.3,2.0,45,212
202,add,34.0,),,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O),35,add ) at position 34,flow_matching,0.3,2.0,45,212
203,add,35.0,[,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O),O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[,36,add [ at position 35,flow_matching,0.3,2.0,45,212
204,add,36.0,O,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O,37,add O at position 36,flow_matching,0.3,2.0,45,212
205,add,37.0,-,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-,38,add - at position 37,flow_matching,0.3,2.0,45,212
206,add,38.0,],,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-],39,add ] at position 38,flow_matching,0.3,2.0,45,212
207,add,39.0,),,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-],O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-]),40,add ) at position 39,flow_matching,0.3,2.0,45,212
208,add,40.0,c,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-]),O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-])c,41,add c at position 40,flow_matching,0.3,2.0,45,212
209,add,41.0,c,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-])c,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-])cc,42,add c at position 41,flow_matching,0.3,2.0,45,212
210,add,42.0,1,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-])cc,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-])cc1,43,add 1 at position 42,flow_matching,0.3,2.0,45,212
211,add,43.0,O,,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-])cc1,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-])cc1O,44,add O at position 43,flow_matching,0.3,2.0,45,212
212,add,44.0,"
",,O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-])cc1O,"O=C1NC(=S)NC(=O)C1=CNc1ccc([N+](=O)[O-])cc1O
",45,"add 
 at position 44",flow_matching,0.3,2.0,45,212
