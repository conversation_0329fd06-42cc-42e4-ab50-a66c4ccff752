step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,38,76
1,add,0.0,o,,,o,1,add o at position 0,flow_matching,0.3,2.0,38,76
2,replace,0.0,(,o,o,(,1,replace o at position 0 with (,flow_matching,0.3,2.0,38,76
3,add,1.0,s,,(,(s,2,add s at position 1,flow_matching,0.3,2.0,38,76
4,add,0.0,O,,(s,O(s,3,add O at position 0,flow_matching,0.3,2.0,38,76
5,add,2.0,),,O(s,O()s,4,add ) at position 2,flow_matching,0.3,2.0,38,76
6,remove,2.0,),,O()s,O(s,3,remove ) from position 2,flow_matching,0.3,2.0,38,76
7,replace,2.0,N,s,O(s,O(N,3,replace s at position 2 with N,flow_matching,0.3,2.0,38,76
8,replace,0.0,C,O,O(N,C(N,3,replace O at position 0 with C,flow_matching,0.3,2.0,38,76
9,remove,2.0,N,,C(N,C(,2,remove N from position 2,flow_matching,0.3,2.0,38,76
10,replace,1.0,C,(,C(,CC,2,replace ( at position 1 with C,flow_matching,0.3,2.0,38,76
11,add,2.0,[,,CC,CC[,3,add [ at position 2,flow_matching,0.3,2.0,38,76
12,add,3.0,C,,CC[,CC[C,4,add C at position 3,flow_matching,0.3,2.0,38,76
13,add,4.0,-,,CC[C,CC[C-,5,add - at position 4,flow_matching,0.3,2.0,38,76
14,remove,3.0,C,,CC[C-,CC[-,4,remove C from position 3,flow_matching,0.3,2.0,38,76
15,remove,3.0,-,,CC[-,CC[,3,remove - from position 3,flow_matching,0.3,2.0,38,76
16,add,3.0,C,,CC[,CC[C,4,add C at position 3,flow_matching,0.3,2.0,38,76
17,add,4.0,n,,CC[C,CC[Cn,5,add n at position 4,flow_matching,0.3,2.0,38,76
18,remove,4.0,n,,CC[Cn,CC[C,4,remove n from position 4,flow_matching,0.3,2.0,38,76
19,add,1.0,4,,CC[C,C4C[C,5,add 4 at position 1,flow_matching,0.3,2.0,38,76
20,replace,1.0,C,4,C4C[C,CCC[C,5,replace 4 at position 1 with C,flow_matching,0.3,2.0,38,76
21,add,2.0,s,,CCC[C,CCsC[C,6,add s at position 2,flow_matching,0.3,2.0,38,76
22,replace,2.0,[,s,CCsC[C,CC[C[C,6,replace s at position 2 with [,flow_matching,0.3,2.0,38,76
23,add,3.0,r,,CC[C[C,CC[rC[C,7,add r at position 3,flow_matching,0.3,2.0,38,76
24,add,3.0,),,CC[rC[C,CC[)rC[C,8,add ) at position 3,flow_matching,0.3,2.0,38,76
25,replace,3.0,C,),CC[)rC[C,CC[CrC[C,8,replace ) at position 3 with C,flow_matching,0.3,2.0,38,76
26,add,4.0,S,,CC[CrC[C,CC[CSrC[C,9,add S at position 4,flow_matching,0.3,2.0,38,76
27,replace,4.0,@,S,CC[CSrC[C,CC[C@rC[C,9,replace S at position 4 with @,flow_matching,0.3,2.0,38,76
28,remove,5.0,r,,CC[C@rC[C,CC[C@C[C,8,remove r from position 5,flow_matching,0.3,2.0,38,76
29,add,8.0,I,,CC[C@C[C,CC[C@C[CI,9,add I at position 8,flow_matching,0.3,2.0,38,76
30,replace,5.0,@,C,CC[C@C[CI,CC[C@@[CI,9,replace C at position 5 with @,flow_matching,0.3,2.0,38,76
31,add,5.0,\,,CC[C@@[CI,CC[C@\@[CI,10,add \ at position 5,flow_matching,0.3,2.0,38,76
32,replace,5.0,@,\,CC[C@\@[CI,CC[C@@@[CI,10,replace \ at position 5 with @,flow_matching,0.3,2.0,38,76
33,remove,7.0,[,,CC[C@@@[CI,CC[C@@@CI,9,remove [ from position 7,flow_matching,0.3,2.0,38,76
34,replace,6.0,H,@,CC[C@@@CI,CC[C@@HCI,9,replace @ at position 6 with H,flow_matching,0.3,2.0,38,76
35,replace,8.0,@,I,CC[C@@HCI,CC[C@@HC@,9,replace I at position 8 with @,flow_matching,0.3,2.0,38,76
36,replace,7.0,],C,CC[C@@HC@,CC[C@@H]@,9,replace C at position 7 with ],flow_matching,0.3,2.0,38,76
37,replace,2.0,1,[,CC[C@@H]@,CC1C@@H]@,9,replace [ at position 2 with 1,flow_matching,0.3,2.0,38,76
38,replace,0.0,4,C,CC1C@@H]@,4C1C@@H]@,9,replace C at position 0 with 4,flow_matching,0.3,2.0,38,76
39,remove,0.0,4,,4C1C@@H]@,C1C@@H]@,8,remove 4 from position 0,flow_matching,0.3,2.0,38,76
40,remove,7.0,@,,C1C@@H]@,C1C@@H],7,remove @ from position 7,flow_matching,0.3,2.0,38,76
41,replace,1.0,C,1,C1C@@H],CCC@@H],7,replace 1 at position 1 with C,flow_matching,0.3,2.0,38,76
42,replace,2.0,[,C,CCC@@H],CC[@@H],7,replace C at position 2 with [,flow_matching,0.3,2.0,38,76
43,replace,3.0,C,@,CC[@@H],CC[C@H],7,replace @ at position 3 with C,flow_matching,0.3,2.0,38,76
44,replace,5.0,@,H,CC[C@H],CC[C@@],7,replace H at position 5 with @,flow_matching,0.3,2.0,38,76
45,replace,6.0,H,],CC[C@@],CC[C@@H,7,replace ] at position 6 with H,flow_matching,0.3,2.0,38,76
46,add,7.0,],,CC[C@@H,CC[C@@H],8,add ] at position 7,flow_matching,0.3,2.0,38,76
47,add,8.0,(,,CC[C@@H],CC[C@@H](,9,add ( at position 8,flow_matching,0.3,2.0,38,76
48,add,9.0,O,,CC[C@@H](,CC[C@@H](O,10,add O at position 9,flow_matching,0.3,2.0,38,76
49,add,10.0,),,CC[C@@H](O,CC[C@@H](O),11,add ) at position 10,flow_matching,0.3,2.0,38,76
50,add,11.0,C,,CC[C@@H](O),CC[C@@H](O)C,12,add C at position 11,flow_matching,0.3,2.0,38,76
51,add,12.0,(,,CC[C@@H](O)C,CC[C@@H](O)C(,13,add ( at position 12,flow_matching,0.3,2.0,38,76
52,add,13.0,=,,CC[C@@H](O)C(,CC[C@@H](O)C(=,14,add = at position 13,flow_matching,0.3,2.0,38,76
53,add,14.0,O,,CC[C@@H](O)C(=,CC[C@@H](O)C(=O,15,add O at position 14,flow_matching,0.3,2.0,38,76
54,add,15.0,),,CC[C@@H](O)C(=O,CC[C@@H](O)C(=O),16,add ) at position 15,flow_matching,0.3,2.0,38,76
55,add,16.0,N,,CC[C@@H](O)C(=O),CC[C@@H](O)C(=O)N,17,add N at position 16,flow_matching,0.3,2.0,38,76
56,add,17.0,C,,CC[C@@H](O)C(=O)N,CC[C@@H](O)C(=O)NC,18,add C at position 17,flow_matching,0.3,2.0,38,76
57,add,18.0,c,,CC[C@@H](O)C(=O)NC,CC[C@@H](O)C(=O)NCc,19,add c at position 18,flow_matching,0.3,2.0,38,76
58,add,19.0,1,,CC[C@@H](O)C(=O)NCc,CC[C@@H](O)C(=O)NCc1,20,add 1 at position 19,flow_matching,0.3,2.0,38,76
59,add,20.0,c,,CC[C@@H](O)C(=O)NCc1,CC[C@@H](O)C(=O)NCc1c,21,add c at position 20,flow_matching,0.3,2.0,38,76
60,add,21.0,c,,CC[C@@H](O)C(=O)NCc1c,CC[C@@H](O)C(=O)NCc1cc,22,add c at position 21,flow_matching,0.3,2.0,38,76
61,add,22.0,c,,CC[C@@H](O)C(=O)NCc1cc,CC[C@@H](O)C(=O)NCc1ccc,23,add c at position 22,flow_matching,0.3,2.0,38,76
62,add,23.0,n,,CC[C@@H](O)C(=O)NCc1ccc,CC[C@@H](O)C(=O)NCc1cccn,24,add n at position 23,flow_matching,0.3,2.0,38,76
63,add,24.0,c,,CC[C@@H](O)C(=O)NCc1cccn,CC[C@@H](O)C(=O)NCc1cccnc,25,add c at position 24,flow_matching,0.3,2.0,38,76
64,add,25.0,1,,CC[C@@H](O)C(=O)NCc1cccnc,CC[C@@H](O)C(=O)NCc1cccnc1,26,add 1 at position 25,flow_matching,0.3,2.0,38,76
65,add,26.0,O,,CC[C@@H](O)C(=O)NCc1cccnc1,CC[C@@H](O)C(=O)NCc1cccnc1O,27,add O at position 26,flow_matching,0.3,2.0,38,76
66,add,27.0,c,,CC[C@@H](O)C(=O)NCc1cccnc1O,CC[C@@H](O)C(=O)NCc1cccnc1Oc,28,add c at position 27,flow_matching,0.3,2.0,38,76
67,add,28.0,1,,CC[C@@H](O)C(=O)NCc1cccnc1Oc,CC[C@@H](O)C(=O)NCc1cccnc1Oc1,29,add 1 at position 28,flow_matching,0.3,2.0,38,76
68,add,29.0,c,,CC[C@@H](O)C(=O)NCc1cccnc1Oc1,CC[C@@H](O)C(=O)NCc1cccnc1Oc1c,30,add c at position 29,flow_matching,0.3,2.0,38,76
69,add,30.0,c,,CC[C@@H](O)C(=O)NCc1cccnc1Oc1c,CC[C@@H](O)C(=O)NCc1cccnc1Oc1cc,31,add c at position 30,flow_matching,0.3,2.0,38,76
70,add,31.0,c,,CC[C@@H](O)C(=O)NCc1cccnc1Oc1cc,CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccc,32,add c at position 31,flow_matching,0.3,2.0,38,76
71,add,32.0,c,,CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccc,CC[C@@H](O)C(=O)NCc1cccnc1Oc1cccc,33,add c at position 32,flow_matching,0.3,2.0,38,76
72,add,33.0,c,,CC[C@@H](O)C(=O)NCc1cccnc1Oc1cccc,CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccccc,34,add c at position 33,flow_matching,0.3,2.0,38,76
73,add,34.0,1,,CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccccc,CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccccc1,35,add 1 at position 34,flow_matching,0.3,2.0,38,76
74,add,35.0,O,,CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccccc1,CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccccc1O,36,add O at position 35,flow_matching,0.3,2.0,38,76
75,add,36.0,C,,CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccccc1O,CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccccc1OC,37,add C at position 36,flow_matching,0.3,2.0,38,76
76,add,37.0,"
",,CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccccc1OC,"CC[C@@H](O)C(=O)NCc1cccnc1Oc1ccccc1OC
",38,"add 
 at position 37",flow_matching,0.3,2.0,38,76
