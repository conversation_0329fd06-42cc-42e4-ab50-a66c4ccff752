step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,132
1,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,49,132
2,replace,0.0,C,-,-,C,1,replace - at position 0 with C,flow_matching,0.3,2.0,49,132
3,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,49,132
4,add,2.0,N,,C[,C[N,3,add N at position 2,flow_matching,0.3,2.0,49,132
5,remove,1.0,[,,C[N,CN,2,remove [ from position 1,flow_matching,0.3,2.0,49,132
6,replace,1.0,[,N,C<PERSON>,<PERSON>[,2,replace N at position 1 with [,flow_matching,0.3,2.0,49,132
7,replace,1.0,),[,C[,C),2,replace [ at position 1 with ),flow_matching,0.3,2.0,49,132
8,replace,1.0,[,),C),C[,2,replace ) at position 1 with [,flow_matching,0.3,2.0,49,132
9,add,2.0,N,,C[,C[N,3,add N at position 2,flow_matching,0.3,2.0,49,132
10,remove,1.0,[,,C[N,CN,2,remove [ from position 1,flow_matching,0.3,2.0,49,132
11,replace,1.0,[,N,CN,C[,2,replace N at position 1 with [,flow_matching,0.3,2.0,49,132
12,remove,0.0,C,,C[,[,1,remove C from position 0,flow_matching,0.3,2.0,49,132
13,add,0.0,O,,[,O[,2,add O at position 0,flow_matching,0.3,2.0,49,132
14,replace,0.0,C,O,O[,C[,2,replace O at position 0 with C,flow_matching,0.3,2.0,49,132
15,add,2.0,N,,C[,C[N,3,add N at position 2,flow_matching,0.3,2.0,49,132
16,remove,2.0,N,,C[N,C[,2,remove N from position 2,flow_matching,0.3,2.0,49,132
17,replace,0.0,6,C,C[,6[,2,replace C at position 0 with 6,flow_matching,0.3,2.0,49,132
18,replace,0.0,C,6,6[,C[,2,replace 6 at position 0 with C,flow_matching,0.3,2.0,49,132
19,add,2.0,F,,C[,C[F,3,add F at position 2,flow_matching,0.3,2.0,49,132
20,replace,2.0,N,F,C[F,C[N,3,replace F at position 2 with N,flow_matching,0.3,2.0,49,132
21,add,3.0,H,,C[N,C[NH,4,add H at position 3,flow_matching,0.3,2.0,49,132
22,add,0.0,n,,C[NH,nC[NH,5,add n at position 0,flow_matching,0.3,2.0,49,132
23,add,0.0,c,,nC[NH,cnC[NH,6,add c at position 0,flow_matching,0.3,2.0,49,132
24,add,5.0,s,,cnC[NH,cnC[NsH,7,add s at position 5,flow_matching,0.3,2.0,49,132
25,replace,0.0,C,c,cnC[NsH,CnC[NsH,7,replace c at position 0 with C,flow_matching,0.3,2.0,49,132
26,replace,1.0,[,n,CnC[NsH,C[C[NsH,7,replace n at position 1 with [,flow_matching,0.3,2.0,49,132
27,replace,2.0,N,C,C[C[NsH,C[N[NsH,7,replace C at position 2 with N,flow_matching,0.3,2.0,49,132
28,add,6.0,],,C[N[NsH,C[N[Ns]H,8,add ] at position 6,flow_matching,0.3,2.0,49,132
29,replace,3.0,H,[,C[N[Ns]H,C[NHNs]H,8,replace [ at position 3 with H,flow_matching,0.3,2.0,49,132
30,replace,4.0,+,N,C[NHNs]H,C[NH+s]H,8,replace N at position 4 with +,flow_matching,0.3,2.0,49,132
31,replace,5.0,],s,C[NH+s]H,C[NH+]]H,8,replace s at position 5 with ],flow_matching,0.3,2.0,49,132
32,remove,3.0,H,,C[NH+]]H,C[N+]]H,7,remove H from position 3,flow_matching,0.3,2.0,49,132
33,replace,3.0,H,+,C[N+]]H,C[NH]]H,7,replace + at position 3 with H,flow_matching,0.3,2.0,49,132
34,add,2.0,(,,C[NH]]H,C[(NH]]H,8,add ( at position 2,flow_matching,0.3,2.0,49,132
35,add,0.0,=,,C[(NH]]H,=C[(NH]]H,9,add = at position 0,flow_matching,0.3,2.0,49,132
36,replace,0.0,C,=,=C[(NH]]H,CC[(NH]]H,9,replace = at position 0 with C,flow_matching,0.3,2.0,49,132
37,replace,5.0,I,H,CC[(NH]]H,CC[(NI]]H,9,replace H at position 5 with I,flow_matching,0.3,2.0,49,132
38,replace,1.0,[,C,CC[(NI]]H,C[[(NI]]H,9,replace C at position 1 with [,flow_matching,0.3,2.0,49,132
39,add,9.0,l,,C[[(NI]]H,C[[(NI]]Hl,10,add l at position 9,flow_matching,0.3,2.0,49,132
40,remove,2.0,[,,C[[(NI]]Hl,C[(NI]]Hl,9,remove [ from position 2,flow_matching,0.3,2.0,49,132
41,replace,0.0,c,C,C[(NI]]Hl,c[(NI]]Hl,9,replace C at position 0 with c,flow_matching,0.3,2.0,49,132
42,replace,7.0,#,H,c[(NI]]Hl,c[(NI]]#l,9,replace H at position 7 with #,flow_matching,0.3,2.0,49,132
43,remove,2.0,(,,c[(NI]]#l,c[NI]]#l,8,remove ( from position 2,flow_matching,0.3,2.0,49,132
44,replace,5.0,c,],c[NI]]#l,c[NI]c#l,8,replace ] at position 5 with c,flow_matching,0.3,2.0,49,132
45,replace,0.0,C,c,c[NI]c#l,C[NI]c#l,8,replace c at position 0 with C,flow_matching,0.3,2.0,49,132
46,add,0.0,c,,C[NI]c#l,cC[NI]c#l,9,add c at position 0,flow_matching,0.3,2.0,49,132
47,replace,0.0,C,c,cC[NI]c#l,CC[NI]c#l,9,replace c at position 0 with C,flow_matching,0.3,2.0,49,132
48,replace,1.0,[,C,CC[NI]c#l,C[[NI]c#l,9,replace C at position 1 with [,flow_matching,0.3,2.0,49,132
49,add,8.0,l,,C[[NI]c#l,C[[NI]c#ll,10,add l at position 8,flow_matching,0.3,2.0,49,132
50,replace,2.0,N,[,C[[NI]c#ll,C[NNI]c#ll,10,replace [ at position 2 with N,flow_matching,0.3,2.0,49,132
51,add,3.0,C,,C[NNI]c#ll,C[NCNI]c#ll,11,add C at position 3,flow_matching,0.3,2.0,49,132
52,replace,3.0,H,C,C[NCNI]c#ll,C[NHNI]c#ll,11,replace C at position 3 with H,flow_matching,0.3,2.0,49,132
53,replace,7.0,F,c,C[NHNI]c#ll,C[NHNI]F#ll,11,replace c at position 7 with F,flow_matching,0.3,2.0,49,132
54,replace,4.0,+,N,C[NHNI]F#ll,C[NH+I]F#ll,11,replace N at position 4 with +,flow_matching,0.3,2.0,49,132
55,remove,10.0,l,,C[NH+I]F#ll,C[NH+I]F#l,10,remove l from position 10,flow_matching,0.3,2.0,49,132
56,add,8.0,2,,C[NH+I]F#l,C[NH+I]F2#l,11,add 2 at position 8,flow_matching,0.3,2.0,49,132
57,replace,5.0,[,I,C[NH+I]F2#l,C[NH+[]F2#l,11,replace I at position 5 with [,flow_matching,0.3,2.0,49,132
58,replace,4.0,),+,C[NH+[]F2#l,C[NH)[]F2#l,11,replace + at position 4 with ),flow_matching,0.3,2.0,49,132
59,remove,1.0,[,,C[NH)[]F2#l,CNH)[]F2#l,10,remove [ from position 1,flow_matching,0.3,2.0,49,132
60,add,5.0,I,,CNH)[]F2#l,CNH)[I]F2#l,11,add I at position 5,flow_matching,0.3,2.0,49,132
61,replace,1.0,[,N,CNH)[I]F2#l,C[H)[I]F2#l,11,replace N at position 1 with [,flow_matching,0.3,2.0,49,132
62,replace,2.0,N,H,C[H)[I]F2#l,C[N)[I]F2#l,11,replace H at position 2 with N,flow_matching,0.3,2.0,49,132
63,add,2.0,O,,C[N)[I]F2#l,C[ON)[I]F2#l,12,add O at position 2,flow_matching,0.3,2.0,49,132
64,replace,2.0,N,O,C[ON)[I]F2#l,C[NN)[I]F2#l,12,replace O at position 2 with N,flow_matching,0.3,2.0,49,132
65,add,7.0,H,,C[NN)[I]F2#l,C[NN)[IH]F2#l,13,add H at position 7,flow_matching,0.3,2.0,49,132
66,remove,5.0,[,,C[NN)[IH]F2#l,C[NN)IH]F2#l,12,remove [ from position 5,flow_matching,0.3,2.0,49,132
67,replace,3.0,H,N,C[NN)IH]F2#l,C[NH)IH]F2#l,12,replace N at position 3 with H,flow_matching,0.3,2.0,49,132
68,replace,4.0,+,),C[NH)IH]F2#l,C[NH+IH]F2#l,12,replace ) at position 4 with +,flow_matching,0.3,2.0,49,132
69,replace,5.0,],I,C[NH+IH]F2#l,C[NH+]H]F2#l,12,replace I at position 5 with ],flow_matching,0.3,2.0,49,132
70,replace,9.0,7,2,C[NH+]H]F2#l,C[NH+]H]F7#l,12,replace 2 at position 9 with 7,flow_matching,0.3,2.0,49,132
71,add,4.0,@,,C[NH+]H]F7#l,C[NH@+]H]F7#l,13,add @ at position 4,flow_matching,0.3,2.0,49,132
72,add,2.0,n,,C[NH@+]H]F7#l,C[nNH@+]H]F7#l,14,add n at position 2,flow_matching,0.3,2.0,49,132
73,replace,2.0,N,n,C[nNH@+]H]F7#l,C[NNH@+]H]F7#l,14,replace n at position 2 with N,flow_matching,0.3,2.0,49,132
74,replace,3.0,H,N,C[NNH@+]H]F7#l,C[NHH@+]H]F7#l,14,replace N at position 3 with H,flow_matching,0.3,2.0,49,132
75,replace,8.0,\,H,C[NHH@+]H]F7#l,C[NHH@+]\]F7#l,14,replace H at position 8 with \,flow_matching,0.3,2.0,49,132
76,add,8.0,N,,C[NHH@+]\]F7#l,C[NHH@+]N\]F7#l,15,add N at position 8,flow_matching,0.3,2.0,49,132
77,add,14.0,F,,C[NHH@+]N\]F7#l,C[NHH@+]N\]F7#Fl,16,add F at position 14,flow_matching,0.3,2.0,49,132
78,add,9.0,C,,C[NHH@+]N\]F7#Fl,C[NHH@+]NC\]F7#Fl,17,add C at position 9,flow_matching,0.3,2.0,49,132
79,add,2.0,I,,C[NHH@+]NC\]F7#Fl,C[INHH@+]NC\]F7#Fl,18,add I at position 2,flow_matching,0.3,2.0,49,132
80,add,1.0,6,,C[INHH@+]NC\]F7#Fl,C6[INHH@+]NC\]F7#Fl,19,add 6 at position 1,flow_matching,0.3,2.0,49,132
81,add,16.0,3,,C6[INHH@+]NC\]F7#Fl,C6[INHH@+]NC\]F73#Fl,20,add 3 at position 16,flow_matching,0.3,2.0,49,132
82,remove,13.0,],,C6[INHH@+]NC\]F73#Fl,C6[INHH@+]NC\F73#Fl,19,remove ] from position 13,flow_matching,0.3,2.0,49,132
83,replace,1.0,[,6,C6[INHH@+]NC\F73#Fl,C[[INHH@+]NC\F73#Fl,19,replace 6 at position 1 with [,flow_matching,0.3,2.0,49,132
84,replace,9.0,N,],C[[INHH@+]NC\F73#Fl,C[[INHH@+NNC\F73#Fl,19,replace ] at position 9 with N,flow_matching,0.3,2.0,49,132
85,add,13.0,5,,C[[INHH@+NNC\F73#Fl,C[[INHH@+NNC\5F73#Fl,20,add 5 at position 13,flow_matching,0.3,2.0,49,132
86,replace,2.0,N,[,C[[INHH@+NNC\5F73#Fl,C[NINHH@+NNC\5F73#Fl,20,replace [ at position 2 with N,flow_matching,0.3,2.0,49,132
87,replace,3.0,H,I,C[NINHH@+NNC\5F73#Fl,C[NHNHH@+NNC\5F73#Fl,20,replace I at position 3 with H,flow_matching,0.3,2.0,49,132
88,replace,4.0,+,N,C[NHNHH@+NNC\5F73#Fl,C[NH+HH@+NNC\5F73#Fl,20,replace N at position 4 with +,flow_matching,0.3,2.0,49,132
89,replace,5.0,],H,C[NH+HH@+NNC\5F73#Fl,C[NH+]H@+NNC\5F73#Fl,20,replace H at position 5 with ],flow_matching,0.3,2.0,49,132
90,replace,6.0,1,H,C[NH+]H@+NNC\5F73#Fl,C[NH+]1@+NNC\5F73#Fl,20,replace H at position 6 with 1,flow_matching,0.3,2.0,49,132
91,replace,7.0,C,@,C[NH+]1@+NNC\5F73#Fl,C[NH+]1C+NNC\5F73#Fl,20,replace @ at position 7 with C,flow_matching,0.3,2.0,49,132
92,replace,8.0,C,+,C[NH+]1C+NNC\5F73#Fl,C[NH+]1CCNNC\5F73#Fl,20,replace + at position 8 with C,flow_matching,0.3,2.0,49,132
93,replace,9.0,C,N,C[NH+]1CCNNC\5F73#Fl,C[NH+]1CCCNC\5F73#Fl,20,replace N at position 9 with C,flow_matching,0.3,2.0,49,132
94,replace,10.0,(,N,C[NH+]1CCCNC\5F73#Fl,C[NH+]1CCC(C\5F73#Fl,20,replace N at position 10 with (,flow_matching,0.3,2.0,49,132
95,replace,11.0,N,C,C[NH+]1CCC(C\5F73#Fl,C[NH+]1CCC(N\5F73#Fl,20,replace C at position 11 with N,flow_matching,0.3,2.0,49,132
96,replace,12.0,[,\,C[NH+]1CCC(N\5F73#Fl,C[NH+]1CCC(N[5F73#Fl,20,replace \ at position 12 with [,flow_matching,0.3,2.0,49,132
97,replace,13.0,C,5,C[NH+]1CCC(N[5F73#Fl,C[NH+]1CCC(N[CF73#Fl,20,replace 5 at position 13 with C,flow_matching,0.3,2.0,49,132
98,replace,14.0,@,F,C[NH+]1CCC(N[CF73#Fl,C[NH+]1CCC(N[C@73#Fl,20,replace F at position 14 with @,flow_matching,0.3,2.0,49,132
99,replace,15.0,@,7,C[NH+]1CCC(N[C@73#Fl,C[NH+]1CCC(N[C@@3#Fl,20,replace 7 at position 15 with @,flow_matching,0.3,2.0,49,132
100,replace,16.0,H,3,C[NH+]1CCC(N[C@@3#Fl,C[NH+]1CCC(N[C@@H#Fl,20,replace 3 at position 16 with H,flow_matching,0.3,2.0,49,132
101,replace,17.0,],#,C[NH+]1CCC(N[C@@H#Fl,C[NH+]1CCC(N[C@@H]Fl,20,replace # at position 17 with ],flow_matching,0.3,2.0,49,132
102,replace,18.0,2,F,C[NH+]1CCC(N[C@@H]Fl,C[NH+]1CCC(N[C@@H]2l,20,replace F at position 18 with 2,flow_matching,0.3,2.0,49,132
103,replace,19.0,C,l,C[NH+]1CCC(N[C@@H]2l,C[NH+]1CCC(N[C@@H]2C,20,replace l at position 19 with C,flow_matching,0.3,2.0,49,132
104,add,20.0,C,,C[NH+]1CCC(N[C@@H]2C,C[NH+]1CCC(N[C@@H]2CC,21,add C at position 20,flow_matching,0.3,2.0,49,132
105,add,21.0,(,,C[NH+]1CCC(N[C@@H]2CC,C[NH+]1CCC(N[C@@H]2CC(,22,add ( at position 21,flow_matching,0.3,2.0,49,132
106,add,22.0,=,,C[NH+]1CCC(N[C@@H]2CC(,C[NH+]1CCC(N[C@@H]2CC(=,23,add = at position 22,flow_matching,0.3,2.0,49,132
107,add,23.0,O,,C[NH+]1CCC(N[C@@H]2CC(=,C[NH+]1CCC(N[C@@H]2CC(=O,24,add O at position 23,flow_matching,0.3,2.0,49,132
108,add,24.0,),,C[NH+]1CCC(N[C@@H]2CC(=O,C[NH+]1CCC(N[C@@H]2CC(=O),25,add ) at position 24,flow_matching,0.3,2.0,49,132
109,add,25.0,N,,C[NH+]1CCC(N[C@@H]2CC(=O),C[NH+]1CCC(N[C@@H]2CC(=O)N,26,add N at position 25,flow_matching,0.3,2.0,49,132
110,add,26.0,(,,C[NH+]1CCC(N[C@@H]2CC(=O)N,C[NH+]1CCC(N[C@@H]2CC(=O)N(,27,add ( at position 26,flow_matching,0.3,2.0,49,132
111,add,27.0,C,,C[NH+]1CCC(N[C@@H]2CC(=O)N(,C[NH+]1CCC(N[C@@H]2CC(=O)N(C,28,add C at position 27,flow_matching,0.3,2.0,49,132
112,add,28.0,C,,C[NH+]1CCC(N[C@@H]2CC(=O)N(C,C[NH+]1CCC(N[C@@H]2CC(=O)N(CC,29,add C at position 28,flow_matching,0.3,2.0,49,132
113,add,29.0,c,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CC,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc,30,add c at position 29,flow_matching,0.3,2.0,49,132
114,add,30.0,3,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3,31,add 3 at position 30,flow_matching,0.3,2.0,49,132
115,add,31.0,c,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3c,32,add c at position 31,flow_matching,0.3,2.0,49,132
116,add,32.0,c,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3c,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cc,33,add c at position 32,flow_matching,0.3,2.0,49,132
117,add,33.0,c,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cc,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3ccc,34,add c at position 33,flow_matching,0.3,2.0,49,132
118,add,34.0,c,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3ccc,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc,35,add c at position 34,flow_matching,0.3,2.0,49,132
119,add,35.0,(,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(,36,add ( at position 35,flow_matching,0.3,2.0,49,132
120,add,36.0,C,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(C,37,add C at position 36,flow_matching,0.3,2.0,49,132
121,add,37.0,l,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(C,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl,38,add l at position 37,flow_matching,0.3,2.0,49,132
122,add,38.0,),,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl),39,add ) at position 38,flow_matching,0.3,2.0,49,132
123,add,39.0,c,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl),C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c,40,add c at position 39,flow_matching,0.3,2.0,49,132
124,add,40.0,3,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3,41,add 3 at position 40,flow_matching,0.3,2.0,49,132
125,add,41.0,),,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3),42,add ) at position 41,flow_matching,0.3,2.0,49,132
126,add,42.0,C,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3),C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C,43,add C at position 42,flow_matching,0.3,2.0,49,132
127,add,43.0,2,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2,44,add 2 at position 43,flow_matching,0.3,2.0,49,132
128,add,44.0,),,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2),45,add ) at position 44,flow_matching,0.3,2.0,49,132
129,add,45.0,C,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2),C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2)C,46,add C at position 45,flow_matching,0.3,2.0,49,132
130,add,46.0,C,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2)C,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2)CC,47,add C at position 46,flow_matching,0.3,2.0,49,132
131,add,47.0,1,,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2)CC,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2)CC1,48,add 1 at position 47,flow_matching,0.3,2.0,49,132
132,add,48.0,"
",,C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2)CC1,"C[NH+]1CCC(N[C@@H]2CC(=O)N(CCc3cccc(Cl)c3)C2)CC1
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,132
