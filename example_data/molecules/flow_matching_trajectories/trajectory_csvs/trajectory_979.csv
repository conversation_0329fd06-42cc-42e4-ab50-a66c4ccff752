step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,44,122
1,add,0.0,r,,,r,1,add r at position 0,flow_matching,0.3,2.0,44,122
2,replace,0.0,C,r,r,C,1,replace r at position 0 with C,flow_matching,0.3,2.0,44,122
3,replace,0.0,r,C,C,r,1,replace C at position 0 with r,flow_matching,0.3,2.0,44,122
4,replace,0.0,C,r,r,C,1,replace r at position 0 with C,flow_matching,0.3,2.0,44,122
5,replace,0.0,-,C,C,-,1,replace C at position 0 with -,flow_matching,0.3,2.0,44,122
6,add,1.0,I,,-,-I,2,add I at position 1,flow_matching,0.3,2.0,44,122
7,remove,0.0,-,,-I,I,1,remove - from position 0,flow_matching,0.3,2.0,44,122
8,remove,0.0,I,,I,,0,remove I from position 0,flow_matching,0.3,2.0,44,122
9,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,44,122
10,replace,0.0,7,6,6,7,1,replace 6 at position 0 with 7,flow_matching,0.3,2.0,44,122
11,add,1.0,],,7,7],2,add ] at position 1,flow_matching,0.3,2.0,44,122
12,add,1.0,@,,7],7@],3,add @ at position 1,flow_matching,0.3,2.0,44,122
13,remove,2.0,],,7@],7@,2,remove ] from position 2,flow_matching,0.3,2.0,44,122
14,replace,0.0,[,7,7@,[@,2,replace 7 at position 0 with [,flow_matching,0.3,2.0,44,122
15,replace,1.0,#,@,[@,[#,2,replace @ at position 1 with #,flow_matching,0.3,2.0,44,122
16,replace,0.0,C,[,[#,C#,2,replace [ at position 0 with C,flow_matching,0.3,2.0,44,122
17,add,1.0,2,,C#,C2#,3,add 2 at position 1,flow_matching,0.3,2.0,44,122
18,add,1.0,S,,C2#,CS2#,4,add S at position 1,flow_matching,0.3,2.0,44,122
19,remove,2.0,2,,CS2#,CS#,3,remove 2 from position 2,flow_matching,0.3,2.0,44,122
20,add,3.0,N,,CS#,CS#N,4,add N at position 3,flow_matching,0.3,2.0,44,122
21,replace,1.0,[,S,CS#N,C[#N,4,replace S at position 1 with [,flow_matching,0.3,2.0,44,122
22,remove,0.0,C,,C[#N,[#N,3,remove C from position 0,flow_matching,0.3,2.0,44,122
23,replace,0.0,C,[,[#N,C#N,3,replace [ at position 0 with C,flow_matching,0.3,2.0,44,122
24,remove,1.0,#,,C#N,CN,2,remove # from position 1,flow_matching,0.3,2.0,44,122
25,remove,0.0,C,,CN,N,1,remove C from position 0,flow_matching,0.3,2.0,44,122
26,replace,0.0,C,N,N,C,1,replace N at position 0 with C,flow_matching,0.3,2.0,44,122
27,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,44,122
28,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,44,122
29,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,44,122
30,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,44,122
31,replace,2.0,F,C,C[C,C[F,3,replace C at position 2 with F,flow_matching,0.3,2.0,44,122
32,replace,2.0,C,F,C[F,C[C,3,replace F at position 2 with C,flow_matching,0.3,2.0,44,122
33,replace,1.0,(,[,C[C,C(C,3,replace [ at position 1 with (,flow_matching,0.3,2.0,44,122
34,replace,0.0,#,C,C(C,#(C,3,replace C at position 0 with #,flow_matching,0.3,2.0,44,122
35,replace,0.0,C,#,#(C,C(C,3,replace # at position 0 with C,flow_matching,0.3,2.0,44,122
36,replace,0.0,3,C,C(C,3(C,3,replace C at position 0 with 3,flow_matching,0.3,2.0,44,122
37,remove,1.0,(,,3(C,3C,2,remove ( from position 1,flow_matching,0.3,2.0,44,122
38,replace,1.0,r,C,3C,3r,2,replace C at position 1 with r,flow_matching,0.3,2.0,44,122
39,replace,0.0,S,3,3r,Sr,2,replace 3 at position 0 with S,flow_matching,0.3,2.0,44,122
40,replace,1.0,2,r,Sr,S2,2,replace r at position 1 with 2,flow_matching,0.3,2.0,44,122
41,add,0.0,O,,S2,OS2,3,add O at position 0,flow_matching,0.3,2.0,44,122
42,remove,2.0,2,,OS2,OS,2,remove 2 from position 2,flow_matching,0.3,2.0,44,122
43,add,2.0,I,,OS,OSI,3,add I at position 2,flow_matching,0.3,2.0,44,122
44,replace,2.0,\,I,OSI,OS\,3,replace I at position 2 with \,flow_matching,0.3,2.0,44,122
45,remove,2.0,\,,OS\,OS,2,remove \ from position 2,flow_matching,0.3,2.0,44,122
46,replace,0.0,C,O,OS,CS,2,replace O at position 0 with C,flow_matching,0.3,2.0,44,122
47,replace,0.0,3,C,CS,3S,2,replace C at position 0 with 3,flow_matching,0.3,2.0,44,122
48,remove,0.0,3,,3S,S,1,remove 3 from position 0,flow_matching,0.3,2.0,44,122
49,add,0.0,\,,S,\S,2,add \ at position 0,flow_matching,0.3,2.0,44,122
50,add,0.0,=,,\S,=\S,3,add = at position 0,flow_matching,0.3,2.0,44,122
51,add,1.0,[,,=\S,=[\S,4,add [ at position 1,flow_matching,0.3,2.0,44,122
52,replace,0.0,5,=,=[\S,5[\S,4,replace = at position 0 with 5,flow_matching,0.3,2.0,44,122
53,replace,2.0,=,\,5[\S,5[=S,4,replace \ at position 2 with =,flow_matching,0.3,2.0,44,122
54,add,0.0,[,,5[=S,[5[=S,5,add [ at position 0,flow_matching,0.3,2.0,44,122
55,add,1.0,),,[5[=S,[)5[=S,6,add ) at position 1,flow_matching,0.3,2.0,44,122
56,add,6.0,B,,[)5[=S,[)5[=SB,7,add B at position 6,flow_matching,0.3,2.0,44,122
57,replace,5.0,1,S,[)5[=SB,[)5[=1B,7,replace S at position 5 with 1,flow_matching,0.3,2.0,44,122
58,remove,0.0,[,,[)5[=1B,)5[=1B,6,remove [ from position 0,flow_matching,0.3,2.0,44,122
59,replace,1.0,N,5,)5[=1B,)N[=1B,6,replace 5 at position 1 with N,flow_matching,0.3,2.0,44,122
60,replace,1.0,],N,)N[=1B,)][=1B,6,replace N at position 1 with ],flow_matching,0.3,2.0,44,122
61,replace,0.0,C,),)][=1B,C][=1B,6,replace ) at position 0 with C,flow_matching,0.3,2.0,44,122
62,replace,1.0,[,],C][=1B,C[[=1B,6,replace ] at position 1 with [,flow_matching,0.3,2.0,44,122
63,add,1.0,4,,C[[=1B,C4[[=1B,7,add 4 at position 1,flow_matching,0.3,2.0,44,122
64,replace,4.0,C,=,C4[[=1B,C4[[C1B,7,replace = at position 4 with C,flow_matching,0.3,2.0,44,122
65,add,2.0,r,,C4[[C1B,C4r[[C1B,8,add r at position 2,flow_matching,0.3,2.0,44,122
66,replace,1.0,[,4,C4r[[C1B,C[r[[C1B,8,replace 4 at position 1 with [,flow_matching,0.3,2.0,44,122
67,remove,4.0,[,,C[r[[C1B,C[r[C1B,7,remove [ from position 4,flow_matching,0.3,2.0,44,122
68,replace,2.0,C,r,C[r[C1B,C[C[C1B,7,replace r at position 2 with C,flow_matching,0.3,2.0,44,122
69,add,6.0,1,,C[C[C1B,C[C[C11B,8,add 1 at position 6,flow_matching,0.3,2.0,44,122
70,replace,3.0,@,[,C[C[C11B,C[C@C11B,8,replace [ at position 3 with @,flow_matching,0.3,2.0,44,122
71,replace,6.0,s,1,C[C@C11B,C[C@C1sB,8,replace 1 at position 6 with s,flow_matching,0.3,2.0,44,122
72,replace,4.0,@,C,C[C@C1sB,C[C@@1sB,8,replace C at position 4 with @,flow_matching,0.3,2.0,44,122
73,replace,4.0,6,@,C[C@@1sB,C[C@61sB,8,replace @ at position 4 with 6,flow_matching,0.3,2.0,44,122
74,add,4.0,F,,C[C@61sB,C[C@F61sB,9,add F at position 4,flow_matching,0.3,2.0,44,122
75,replace,4.0,@,F,C[C@F61sB,C[C@@61sB,9,replace F at position 4 with @,flow_matching,0.3,2.0,44,122
76,remove,1.0,[,,C[C@@61sB,CC@@61sB,8,remove [ from position 1,flow_matching,0.3,2.0,44,122
77,add,4.0,o,,CC@@61sB,CC@@o61sB,9,add o at position 4,flow_matching,0.3,2.0,44,122
78,add,7.0,3,,CC@@o61sB,CC@@o613sB,10,add 3 at position 7,flow_matching,0.3,2.0,44,122
79,add,2.0,r,,CC@@o613sB,CCr@@o613sB,11,add r at position 2,flow_matching,0.3,2.0,44,122
80,add,8.0,7,,CCr@@o613sB,CCr@@o6173sB,12,add 7 at position 8,flow_matching,0.3,2.0,44,122
81,replace,0.0,B,C,CCr@@o6173sB,BCr@@o6173sB,12,replace C at position 0 with B,flow_matching,0.3,2.0,44,122
82,replace,0.0,C,B,BCr@@o6173sB,CCr@@o6173sB,12,replace B at position 0 with C,flow_matching,0.3,2.0,44,122
83,replace,1.0,[,C,CCr@@o6173sB,C[r@@o6173sB,12,replace C at position 1 with [,flow_matching,0.3,2.0,44,122
84,replace,2.0,C,r,C[r@@o6173sB,C[C@@o6173sB,12,replace r at position 2 with C,flow_matching,0.3,2.0,44,122
85,replace,5.0,H,o,C[C@@o6173sB,C[C@@H6173sB,12,replace o at position 5 with H,flow_matching,0.3,2.0,44,122
86,replace,6.0,],6,C[C@@H6173sB,C[C@@H]173sB,12,replace 6 at position 6 with ],flow_matching,0.3,2.0,44,122
87,replace,8.0,C,7,C[C@@H]173sB,C[C@@H]1C3sB,12,replace 7 at position 8 with C,flow_matching,0.3,2.0,44,122
88,replace,9.0,C,3,C[C@@H]1C3sB,C[C@@H]1CCsB,12,replace 3 at position 9 with C,flow_matching,0.3,2.0,44,122
89,replace,10.0,C,s,C[C@@H]1CCsB,C[C@@H]1CCCB,12,replace s at position 10 with C,flow_matching,0.3,2.0,44,122
90,replace,11.0,[,B,C[C@@H]1CCCB,C[C@@H]1CCC[,12,replace B at position 11 with [,flow_matching,0.3,2.0,44,122
91,add,12.0,C,,C[C@@H]1CCC[,C[C@@H]1CCC[C,13,add C at position 12,flow_matching,0.3,2.0,44,122
92,add,13.0,@,,C[C@@H]1CCC[C,C[C@@H]1CCC[C@,14,add @ at position 13,flow_matching,0.3,2.0,44,122
93,add,14.0,@,,C[C@@H]1CCC[C@,C[C@@H]1CCC[C@@,15,add @ at position 14,flow_matching,0.3,2.0,44,122
94,add,15.0,H,,C[C@@H]1CCC[C@@,C[C@@H]1CCC[C@@H,16,add H at position 15,flow_matching,0.3,2.0,44,122
95,add,16.0,],,C[C@@H]1CCC[C@@H,C[C@@H]1CCC[C@@H],17,add ] at position 16,flow_matching,0.3,2.0,44,122
96,add,17.0,(,,C[C@@H]1CCC[C@@H],C[C@@H]1CCC[C@@H](,18,add ( at position 17,flow_matching,0.3,2.0,44,122
97,add,18.0,N,,C[C@@H]1CCC[C@@H](,C[C@@H]1CCC[C@@H](N,19,add N at position 18,flow_matching,0.3,2.0,44,122
98,add,19.0,S,,C[C@@H]1CCC[C@@H](N,C[C@@H]1CCC[C@@H](NS,20,add S at position 19,flow_matching,0.3,2.0,44,122
99,add,20.0,(,,C[C@@H]1CCC[C@@H](NS,C[C@@H]1CCC[C@@H](NS(,21,add ( at position 20,flow_matching,0.3,2.0,44,122
100,add,21.0,=,,C[C@@H]1CCC[C@@H](NS(,C[C@@H]1CCC[C@@H](NS(=,22,add = at position 21,flow_matching,0.3,2.0,44,122
101,add,22.0,O,,C[C@@H]1CCC[C@@H](NS(=,C[C@@H]1CCC[C@@H](NS(=O,23,add O at position 22,flow_matching,0.3,2.0,44,122
102,add,23.0,),,C[C@@H]1CCC[C@@H](NS(=O,C[C@@H]1CCC[C@@H](NS(=O),24,add ) at position 23,flow_matching,0.3,2.0,44,122
103,add,24.0,(,,C[C@@H]1CCC[C@@H](NS(=O),C[C@@H]1CCC[C@@H](NS(=O)(,25,add ( at position 24,flow_matching,0.3,2.0,44,122
104,add,25.0,=,,C[C@@H]1CCC[C@@H](NS(=O)(,C[C@@H]1CCC[C@@H](NS(=O)(=,26,add = at position 25,flow_matching,0.3,2.0,44,122
105,add,26.0,O,,C[C@@H]1CCC[C@@H](NS(=O)(=,C[C@@H]1CCC[C@@H](NS(=O)(=O,27,add O at position 26,flow_matching,0.3,2.0,44,122
106,add,27.0,),,C[C@@H]1CCC[C@@H](NS(=O)(=O,C[C@@H]1CCC[C@@H](NS(=O)(=O),28,add ) at position 27,flow_matching,0.3,2.0,44,122
107,add,28.0,C,,C[C@@H]1CCC[C@@H](NS(=O)(=O),C[C@@H]1CCC[C@@H](NS(=O)(=O)C,29,add C at position 28,flow_matching,0.3,2.0,44,122
108,add,29.0,c,,C[C@@H]1CCC[C@@H](NS(=O)(=O)C,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc,30,add c at position 29,flow_matching,0.3,2.0,44,122
109,add,30.0,2,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2,31,add 2 at position 30,flow_matching,0.3,2.0,44,122
110,add,31.0,c,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2c,32,add c at position 31,flow_matching,0.3,2.0,44,122
111,add,32.0,c,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2c,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cc,33,add c at position 32,flow_matching,0.3,2.0,44,122
112,add,33.0,c,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cc,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2ccc,34,add c at position 33,flow_matching,0.3,2.0,44,122
113,add,34.0,c,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2ccc,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc,35,add c at position 34,flow_matching,0.3,2.0,44,122
114,add,35.0,(,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(,36,add ( at position 35,flow_matching,0.3,2.0,44,122
115,add,36.0,N,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N,37,add N at position 36,flow_matching,0.3,2.0,44,122
116,add,37.0,),,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N),38,add ) at position 37,flow_matching,0.3,2.0,44,122
117,add,38.0,c,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N),C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c,39,add c at position 38,flow_matching,0.3,2.0,44,122
118,add,39.0,2,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c2,40,add 2 at position 39,flow_matching,0.3,2.0,44,122
119,add,40.0,),,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c2,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c2),41,add ) at position 40,flow_matching,0.3,2.0,44,122
120,add,41.0,C,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c2),C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c2)C,42,add C at position 41,flow_matching,0.3,2.0,44,122
121,add,42.0,1,,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c2)C,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c2)C1,43,add 1 at position 42,flow_matching,0.3,2.0,44,122
122,add,43.0,"
",,C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c2)C1,"C[C@@H]1CCC[C@@H](NS(=O)(=O)Cc2cccc(N)c2)C1
",44,"add 
 at position 43",flow_matching,0.3,2.0,44,122
