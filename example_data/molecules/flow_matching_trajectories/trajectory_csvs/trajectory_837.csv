step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,189
1,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,49,189
2,replace,0.0,C,+,+,C,1,replace + at position 0 with C,flow_matching,0.3,2.0,49,189
3,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,49,189
4,remove,1.0,C,,CC,C,1,remove C from position 1,flow_matching,0.3,2.0,49,189
5,add,1.0,B,,C,CB,2,add B at position 1,flow_matching,0.3,2.0,49,189
6,remove,1.0,B,,CB,C,1,remove B from position 1,flow_matching,0.3,2.0,49,189
7,add,0.0,H,,<PERSON>,HC,2,add H at position 0,flow_matching,0.3,2.0,49,189
8,replace,0.0,[,H,HC,[C,2,replace H at position 0 with [,flow_matching,0.3,2.0,49,189
9,replace,0.0,C,[,[C,CC,2,replace [ at position 0 with C,flow_matching,0.3,2.0,49,189
10,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,49,189
11,replace,1.0,s,C,CC(,Cs(,3,replace C at position 1 with s,flow_matching,0.3,2.0,49,189
12,add,1.0,[,,Cs(,C[s(,4,add [ at position 1,flow_matching,0.3,2.0,49,189
13,replace,2.0,-,s,C[s(,C[-(,4,replace s at position 2 with -,flow_matching,0.3,2.0,49,189
14,replace,1.0,B,[,C[-(,CB-(,4,replace [ at position 1 with B,flow_matching,0.3,2.0,49,189
15,remove,1.0,B,,CB-(,C-(,3,remove B from position 1,flow_matching,0.3,2.0,49,189
16,replace,1.0,C,-,C-(,CC(,3,replace - at position 1 with C,flow_matching,0.3,2.0,49,189
17,remove,2.0,(,,CC(,CC,2,remove ( from position 2,flow_matching,0.3,2.0,49,189
18,add,2.0,o,,CC,CCo,3,add o at position 2,flow_matching,0.3,2.0,49,189
19,remove,2.0,o,,CCo,CC,2,remove o from position 2,flow_matching,0.3,2.0,49,189
20,add,0.0,2,,CC,2CC,3,add 2 at position 0,flow_matching,0.3,2.0,49,189
21,add,0.0,O,,2CC,O2CC,4,add O at position 0,flow_matching,0.3,2.0,49,189
22,add,4.0,3,,O2CC,O2CC3,5,add 3 at position 4,flow_matching,0.3,2.0,49,189
23,add,5.0,r,,O2CC3,O2CC3r,6,add r at position 5,flow_matching,0.3,2.0,49,189
24,add,3.0,S,,O2CC3r,O2CSC3r,7,add S at position 3,flow_matching,0.3,2.0,49,189
25,replace,0.0,C,O,O2CSC3r,C2CSC3r,7,replace O at position 0 with C,flow_matching,0.3,2.0,49,189
26,add,3.0,1,,C2CSC3r,C2C1SC3r,8,add 1 at position 3,flow_matching,0.3,2.0,49,189
27,add,2.0,2,,C2C1SC3r,C22C1SC3r,9,add 2 at position 2,flow_matching,0.3,2.0,49,189
28,remove,4.0,1,,C22C1SC3r,C22CSC3r,8,remove 1 from position 4,flow_matching,0.3,2.0,49,189
29,remove,3.0,C,,C22CSC3r,C22SC3r,7,remove C from position 3,flow_matching,0.3,2.0,49,189
30,add,6.0,5,,C22SC3r,C22SC35r,8,add 5 at position 6,flow_matching,0.3,2.0,49,189
31,add,6.0,),,C22SC35r,C22SC3)5r,9,add ) at position 6,flow_matching,0.3,2.0,49,189
32,replace,1.0,C,2,C22SC3)5r,CC2SC3)5r,9,replace 2 at position 1 with C,flow_matching,0.3,2.0,49,189
33,add,2.0,3,,CC2SC3)5r,CC32SC3)5r,10,add 3 at position 2,flow_matching,0.3,2.0,49,189
34,replace,2.0,(,3,CC32SC3)5r,CC(2SC3)5r,10,replace 3 at position 2 with (,flow_matching,0.3,2.0,49,189
35,replace,3.0,=,2,CC(2SC3)5r,CC(=SC3)5r,10,replace 2 at position 3 with =,flow_matching,0.3,2.0,49,189
36,replace,7.0,I,),CC(=SC3)5r,CC(=SC3I5r,10,replace ) at position 7 with I,flow_matching,0.3,2.0,49,189
37,replace,4.0,O,S,CC(=SC3I5r,CC(=OC3I5r,10,replace S at position 4 with O,flow_matching,0.3,2.0,49,189
38,add,4.0,[,,CC(=OC3I5r,CC(=[OC3I5r,11,add [ at position 4,flow_matching,0.3,2.0,49,189
39,replace,3.0,O,=,CC(=[OC3I5r,CC(O[OC3I5r,11,replace = at position 3 with O,flow_matching,0.3,2.0,49,189
40,add,2.0,s,,CC(O[OC3I5r,CCs(O[OC3I5r,12,add s at position 2,flow_matching,0.3,2.0,49,189
41,replace,2.0,(,s,CCs(O[OC3I5r,CC((O[OC3I5r,12,replace s at position 2 with (,flow_matching,0.3,2.0,49,189
42,add,11.0,n,,CC((O[OC3I5r,CC((O[OC3I5nr,13,add n at position 11,flow_matching,0.3,2.0,49,189
43,replace,3.0,=,(,CC((O[OC3I5nr,CC(=O[OC3I5nr,13,replace ( at position 3 with =,flow_matching,0.3,2.0,49,189
44,replace,4.0,2,O,CC(=O[OC3I5nr,CC(=2[OC3I5nr,13,replace O at position 4 with 2,flow_matching,0.3,2.0,49,189
45,replace,4.0,O,2,CC(=2[OC3I5nr,CC(=O[OC3I5nr,13,replace 2 at position 4 with O,flow_matching,0.3,2.0,49,189
46,remove,9.0,I,,CC(=O[OC3I5nr,CC(=O[OC35nr,12,remove I from position 9,flow_matching,0.3,2.0,49,189
47,add,12.0,r,,CC(=O[OC35nr,CC(=O[OC35nrr,13,add r at position 12,flow_matching,0.3,2.0,49,189
48,add,7.0,#,,CC(=O[OC35nrr,CC(=O[O#C35nrr,14,add # at position 7,flow_matching,0.3,2.0,49,189
49,replace,7.0,C,#,CC(=O[O#C35nrr,CC(=O[OCC35nrr,14,replace # at position 7 with C,flow_matching,0.3,2.0,49,189
50,add,2.0,5,,CC(=O[OCC35nrr,CC5(=O[OCC35nrr,15,add 5 at position 2,flow_matching,0.3,2.0,49,189
51,replace,1.0,4,C,CC5(=O[OCC35nrr,C45(=O[OCC35nrr,15,replace C at position 1 with 4,flow_matching,0.3,2.0,49,189
52,add,10.0,-,,C45(=O[OCC35nrr,C45(=O[OCC-35nrr,16,add - at position 10,flow_matching,0.3,2.0,49,189
53,add,10.0,2,,C45(=O[OCC-35nrr,C45(=O[OCC2-35nrr,17,add 2 at position 10,flow_matching,0.3,2.0,49,189
54,remove,10.0,2,,C45(=O[OCC2-35nrr,C45(=O[OCC-35nrr,16,remove 2 from position 10,flow_matching,0.3,2.0,49,189
55,replace,15.0,I,r,C45(=O[OCC-35nrr,C45(=O[OCC-35nrI,16,replace r at position 15 with I,flow_matching,0.3,2.0,49,189
56,add,12.0,B,,C45(=O[OCC-35nrI,C45(=O[OCC-3B5nrI,17,add B at position 12,flow_matching,0.3,2.0,49,189
57,remove,14.0,n,,C45(=O[OCC-3B5nrI,C45(=O[OCC-3B5rI,16,remove n from position 14,flow_matching,0.3,2.0,49,189
58,replace,1.0,#,4,C45(=O[OCC-3B5rI,C#5(=O[OCC-3B5rI,16,replace 4 at position 1 with #,flow_matching,0.3,2.0,49,189
59,remove,0.0,C,,C#5(=O[OCC-3B5rI,#5(=O[OCC-3B5rI,15,remove C from position 0,flow_matching,0.3,2.0,49,189
60,replace,5.0,-,[,#5(=O[OCC-3B5rI,#5(=O-OCC-3B5rI,15,replace [ at position 5 with -,flow_matching,0.3,2.0,49,189
61,replace,0.0,C,#,#5(=O-OCC-3B5rI,C5(=O-OCC-3B5rI,15,replace # at position 0 with C,flow_matching,0.3,2.0,49,189
62,add,11.0,1,,C5(=O-OCC-3B5rI,C5(=O-OCC-31B5rI,16,add 1 at position 11,flow_matching,0.3,2.0,49,189
63,remove,12.0,B,,C5(=O-OCC-31B5rI,C5(=O-OCC-315rI,15,remove B from position 12,flow_matching,0.3,2.0,49,189
64,replace,1.0,C,5,C5(=O-OCC-315rI,CC(=O-OCC-315rI,15,replace 5 at position 1 with C,flow_matching,0.3,2.0,49,189
65,remove,11.0,1,,CC(=O-OCC-315rI,CC(=O-OCC-35rI,14,remove 1 from position 11,flow_matching,0.3,2.0,49,189
66,replace,5.0,),-,CC(=O-OCC-35rI,CC(=O)OCC-35rI,14,replace - at position 5 with ),flow_matching,0.3,2.0,49,189
67,add,12.0,c,,CC(=O)OCC-35rI,CC(=O)OCC-35crI,15,add c at position 12,flow_matching,0.3,2.0,49,189
68,replace,6.0,N,O,CC(=O)OCC-35crI,CC(=O)NCC-35crI,15,replace O at position 6 with N,flow_matching,0.3,2.0,49,189
69,replace,11.0,C,5,CC(=O)NCC-35crI,CC(=O)NCC-3CcrI,15,replace 5 at position 11 with C,flow_matching,0.3,2.0,49,189
70,replace,10.0,O,3,CC(=O)NCC-3CcrI,CC(=O)NCC-OCcrI,15,replace 3 at position 10 with O,flow_matching,0.3,2.0,49,189
71,replace,7.0,c,C,CC(=O)NCC-OCcrI,CC(=O)NcC-OCcrI,15,replace C at position 7 with c,flow_matching,0.3,2.0,49,189
72,add,9.0,S,,CC(=O)NcC-OCcrI,CC(=O)NcCS-OCcrI,16,add S at position 9,flow_matching,0.3,2.0,49,189
73,remove,15.0,I,,CC(=O)NcCS-OCcrI,CC(=O)NcCS-OCcr,15,remove I from position 15,flow_matching,0.3,2.0,49,189
74,add,15.0,),,CC(=O)NcCS-OCcr,CC(=O)NcCS-OCcr),16,add ) at position 15,flow_matching,0.3,2.0,49,189
75,replace,5.0,-,),CC(=O)NcCS-OCcr),CC(=O-NcCS-OCcr),16,replace ) at position 5 with -,flow_matching,0.3,2.0,49,189
76,replace,12.0,6,C,CC(=O-NcCS-OCcr),CC(=O-NcCS-O6cr),16,replace C at position 12 with 6,flow_matching,0.3,2.0,49,189
77,replace,5.0,),-,CC(=O-NcCS-O6cr),CC(=O)NcCS-O6cr),16,replace - at position 5 with ),flow_matching,0.3,2.0,49,189
78,replace,2.0,1,(,CC(=O)NcCS-O6cr),CC1=O)NcCS-O6cr),16,replace ( at position 2 with 1,flow_matching,0.3,2.0,49,189
79,replace,0.0,o,C,CC1=O)NcCS-O6cr),oC1=O)NcCS-O6cr),16,replace C at position 0 with o,flow_matching,0.3,2.0,49,189
80,replace,1.0,6,C,oC1=O)NcCS-O6cr),o61=O)NcCS-O6cr),16,replace C at position 1 with 6,flow_matching,0.3,2.0,49,189
81,add,16.0,3,,o61=O)NcCS-O6cr),o61=O)NcCS-O6cr)3,17,add 3 at position 16,flow_matching,0.3,2.0,49,189
82,add,15.0,3,,o61=O)NcCS-O6cr)3,o61=O)NcCS-O6cr3)3,18,add 3 at position 15,flow_matching,0.3,2.0,49,189
83,add,11.0,B,,o61=O)NcCS-O6cr3)3,o61=O)NcCS-BO6cr3)3,19,add B at position 11,flow_matching,0.3,2.0,49,189
84,replace,15.0,S,r,o61=O)NcCS-BO6cr3)3,o61=O)NcCS-BO6cS3)3,19,replace r at position 15 with S,flow_matching,0.3,2.0,49,189
85,add,19.0,[,,o61=O)NcCS-BO6cS3)3,o61=O)NcCS-BO6cS3)3[,20,add [ at position 19,flow_matching,0.3,2.0,49,189
86,replace,0.0,C,o,o61=O)NcCS-BO6cS3)3[,C61=O)NcCS-BO6cS3)3[,20,replace o at position 0 with C,flow_matching,0.3,2.0,49,189
87,remove,11.0,B,,C61=O)NcCS-BO6cS3)3[,C61=O)NcCS-O6cS3)3[,19,remove B from position 11,flow_matching,0.3,2.0,49,189
88,replace,1.0,C,6,C61=O)NcCS-O6cS3)3[,CC1=O)NcCS-O6cS3)3[,19,replace 6 at position 1 with C,flow_matching,0.3,2.0,49,189
89,add,8.0,n,,CC1=O)NcCS-O6cS3)3[,CC1=O)NcnCS-O6cS3)3[,20,add n at position 8,flow_matching,0.3,2.0,49,189
90,replace,2.0,(,1,CC1=O)NcnCS-O6cS3)3[,CC(=O)NcnCS-O6cS3)3[,20,replace 1 at position 2 with (,flow_matching,0.3,2.0,49,189
91,replace,19.0,+,[,CC(=O)NcnCS-O6cS3)3[,CC(=O)NcnCS-O6cS3)3+,20,replace [ at position 19 with +,flow_matching,0.3,2.0,49,189
92,replace,8.0,1,n,CC(=O)NcnCS-O6cS3)3+,CC(=O)Nc1CS-O6cS3)3+,20,replace n at position 8 with 1,flow_matching,0.3,2.0,49,189
93,replace,16.0,I,3,CC(=O)Nc1CS-O6cS3)3+,CC(=O)Nc1CS-O6cSI)3+,20,replace 3 at position 16 with I,flow_matching,0.3,2.0,49,189
94,replace,9.0,c,C,CC(=O)Nc1CS-O6cSI)3+,CC(=O)Nc1cS-O6cSI)3+,20,replace C at position 9 with c,flow_matching,0.3,2.0,49,189
95,add,7.0,3,,CC(=O)Nc1cS-O6cSI)3+,CC(=O)N3c1cS-O6cSI)3+,21,add 3 at position 7,flow_matching,0.3,2.0,49,189
96,replace,7.0,c,3,CC(=O)N3c1cS-O6cSI)3+,CC(=O)Ncc1cS-O6cSI)3+,21,replace 3 at position 7 with c,flow_matching,0.3,2.0,49,189
97,remove,14.0,6,,CC(=O)Ncc1cS-O6cSI)3+,CC(=O)Ncc1cS-OcSI)3+,20,remove 6 from position 14,flow_matching,0.3,2.0,49,189
98,add,7.0,c,,CC(=O)Ncc1cS-OcSI)3+,CC(=O)Nccc1cS-OcSI)3+,21,add c at position 7,flow_matching,0.3,2.0,49,189
99,replace,8.0,1,c,CC(=O)Nccc1cS-OcSI)3+,CC(=O)Nc1c1cS-OcSI)3+,21,replace c at position 8 with 1,flow_matching,0.3,2.0,49,189
100,replace,7.0,S,c,CC(=O)Nc1c1cS-OcSI)3+,CC(=O)NS1c1cS-OcSI)3+,21,replace c at position 7 with S,flow_matching,0.3,2.0,49,189
101,remove,7.0,S,,CC(=O)NS1c1cS-OcSI)3+,CC(=O)N1c1cS-OcSI)3+,20,remove S from position 7,flow_matching,0.3,2.0,49,189
102,add,1.0,O,,CC(=O)N1c1cS-OcSI)3+,COC(=O)N1c1cS-OcSI)3+,21,add O at position 1,flow_matching,0.3,2.0,49,189
103,remove,2.0,C,,COC(=O)N1c1cS-OcSI)3+,CO(=O)N1c1cS-OcSI)3+,20,remove C from position 2,flow_matching,0.3,2.0,49,189
104,replace,1.0,C,O,CO(=O)N1c1cS-OcSI)3+,CC(=O)N1c1cS-OcSI)3+,20,replace O at position 1 with C,flow_matching,0.3,2.0,49,189
105,replace,7.0,c,1,CC(=O)N1c1cS-OcSI)3+,CC(=O)Ncc1cS-OcSI)3+,20,replace 1 at position 7 with c,flow_matching,0.3,2.0,49,189
106,remove,10.0,c,,CC(=O)Ncc1cS-OcSI)3+,CC(=O)Ncc1S-OcSI)3+,19,remove c from position 10,flow_matching,0.3,2.0,49,189
107,replace,15.0,O,I,CC(=O)Ncc1S-OcSI)3+,CC(=O)Ncc1S-OcSO)3+,19,replace I at position 15 with O,flow_matching,0.3,2.0,49,189
108,replace,11.0,=,-,CC(=O)Ncc1S-OcSO)3+,CC(=O)Ncc1S=OcSO)3+,19,replace - at position 11 with =,flow_matching,0.3,2.0,49,189
109,add,19.0,1,,CC(=O)Ncc1S=OcSO)3+,CC(=O)Ncc1S=OcSO)3+1,20,add 1 at position 19,flow_matching,0.3,2.0,49,189
110,remove,0.0,C,,CC(=O)Ncc1S=OcSO)3+1,C(=O)Ncc1S=OcSO)3+1,19,remove C from position 0,flow_matching,0.3,2.0,49,189
111,add,4.0,[,,C(=O)Ncc1S=OcSO)3+1,C(=O[)Ncc1S=OcSO)3+1,20,add [ at position 4,flow_matching,0.3,2.0,49,189
112,replace,1.0,C,(,C(=O[)Ncc1S=OcSO)3+1,CC=O[)Ncc1S=OcSO)3+1,20,replace ( at position 1 with C,flow_matching,0.3,2.0,49,189
113,remove,9.0,1,,CC=O[)Ncc1S=OcSO)3+1,CC=O[)NccS=OcSO)3+1,19,remove 1 from position 9,flow_matching,0.3,2.0,49,189
114,replace,2.0,#,=,CC=O[)NccS=OcSO)3+1,CC#O[)NccS=OcSO)3+1,19,replace = at position 2 with #,flow_matching,0.3,2.0,49,189
115,add,3.0,#,,CC#O[)NccS=OcSO)3+1,CC##O[)NccS=OcSO)3+1,20,add # at position 3,flow_matching,0.3,2.0,49,189
116,add,14.0,(,,CC##O[)NccS=OcSO)3+1,CC##O[)NccS=Oc(SO)3+1,21,add ( at position 14,flow_matching,0.3,2.0,49,189
117,replace,2.0,(,#,CC##O[)NccS=Oc(SO)3+1,CC(#O[)NccS=Oc(SO)3+1,21,replace # at position 2 with (,flow_matching,0.3,2.0,49,189
118,replace,3.0,=,#,CC(#O[)NccS=Oc(SO)3+1,CC(=O[)NccS=Oc(SO)3+1,21,replace # at position 3 with =,flow_matching,0.3,2.0,49,189
119,replace,5.0,),[,CC(=O[)NccS=Oc(SO)3+1,CC(=O))NccS=Oc(SO)3+1,21,replace [ at position 5 with ),flow_matching,0.3,2.0,49,189
120,add,3.0,5,,CC(=O))NccS=Oc(SO)3+1,CC(5=O))NccS=Oc(SO)3+1,22,add 5 at position 3,flow_matching,0.3,2.0,49,189
121,add,14.0,+,,CC(5=O))NccS=Oc(SO)3+1,CC(5=O))NccS=O+c(SO)3+1,23,add + at position 14,flow_matching,0.3,2.0,49,189
122,add,1.0,3,,CC(5=O))NccS=O+c(SO)3+1,C3C(5=O))NccS=O+c(SO)3+1,24,add 3 at position 1,flow_matching,0.3,2.0,49,189
123,replace,22.0,),+,C3C(5=O))NccS=O+c(SO)3+1,C3C(5=O))NccS=O+c(SO)3)1,24,replace + at position 22 with ),flow_matching,0.3,2.0,49,189
124,replace,1.0,C,3,C3C(5=O))NccS=O+c(SO)3)1,CCC(5=O))NccS=O+c(SO)3)1,24,replace 3 at position 1 with C,flow_matching,0.3,2.0,49,189
125,remove,23.0,1,,CCC(5=O))NccS=O+c(SO)3)1,CCC(5=O))NccS=O+c(SO)3),23,remove 1 from position 23,flow_matching,0.3,2.0,49,189
126,add,23.0,S,,CCC(5=O))NccS=O+c(SO)3),CCC(5=O))NccS=O+c(SO)3)S,24,add S at position 23,flow_matching,0.3,2.0,49,189
127,replace,20.0,],),CCC(5=O))NccS=O+c(SO)3)S,CCC(5=O))NccS=O+c(SO]3)S,24,replace ) at position 20 with ],flow_matching,0.3,2.0,49,189
128,add,16.0,H,,CCC(5=O))NccS=O+c(SO]3)S,CCC(5=O))NccS=O+Hc(SO]3)S,25,add H at position 16,flow_matching,0.3,2.0,49,189
129,replace,7.0,],),CCC(5=O))NccS=O+Hc(SO]3)S,CCC(5=O])NccS=O+Hc(SO]3)S,25,replace ) at position 7 with ],flow_matching,0.3,2.0,49,189
130,replace,2.0,(,C,CCC(5=O])NccS=O+Hc(SO]3)S,CC((5=O])NccS=O+Hc(SO]3)S,25,replace C at position 2 with (,flow_matching,0.3,2.0,49,189
131,replace,3.0,=,(,CC((5=O])NccS=O+Hc(SO]3)S,CC(=5=O])NccS=O+Hc(SO]3)S,25,replace ( at position 3 with =,flow_matching,0.3,2.0,49,189
132,replace,21.0,C,],CC(=5=O])NccS=O+Hc(SO]3)S,CC(=5=O])NccS=O+Hc(SOC3)S,25,replace ] at position 21 with C,flow_matching,0.3,2.0,49,189
133,remove,8.0,),,CC(=5=O])NccS=O+Hc(SOC3)S,CC(=5=O]NccS=O+Hc(SOC3)S,24,remove ) from position 8,flow_matching,0.3,2.0,49,189
134,replace,4.0,O,5,CC(=5=O]NccS=O+Hc(SOC3)S,CC(=O=O]NccS=O+Hc(SOC3)S,24,replace 5 at position 4 with O,flow_matching,0.3,2.0,49,189
135,add,24.0,N,,CC(=O=O]NccS=O+Hc(SOC3)S,CC(=O=O]NccS=O+Hc(SOC3)SN,25,add N at position 24,flow_matching,0.3,2.0,49,189
136,remove,19.0,O,,CC(=O=O]NccS=O+Hc(SOC3)SN,CC(=O=O]NccS=O+Hc(SC3)SN,24,remove O from position 19,flow_matching,0.3,2.0,49,189
137,replace,5.0,),=,CC(=O=O]NccS=O+Hc(SC3)SN,CC(=O)O]NccS=O+Hc(SC3)SN,24,replace = at position 5 with ),flow_matching,0.3,2.0,49,189
138,add,9.0,l,,CC(=O)O]NccS=O+Hc(SC3)SN,CC(=O)O]NlccS=O+Hc(SC3)SN,25,add l at position 9,flow_matching,0.3,2.0,49,189
139,replace,6.0,N,O,CC(=O)O]NlccS=O+Hc(SC3)SN,CC(=O)N]NlccS=O+Hc(SC3)SN,25,replace O at position 6 with N,flow_matching,0.3,2.0,49,189
140,replace,7.0,c,],CC(=O)N]NlccS=O+Hc(SC3)SN,CC(=O)NcNlccS=O+Hc(SC3)SN,25,replace ] at position 7 with c,flow_matching,0.3,2.0,49,189
141,replace,12.0,n,S,CC(=O)NcNlccS=O+Hc(SC3)SN,CC(=O)NcNlccn=O+Hc(SC3)SN,25,replace S at position 12 with n,flow_matching,0.3,2.0,49,189
142,replace,12.0,\,n,CC(=O)NcNlccn=O+Hc(SC3)SN,CC(=O)NcNlcc\=O+Hc(SC3)SN,25,replace n at position 12 with \,flow_matching,0.3,2.0,49,189
143,replace,8.0,1,N,CC(=O)NcNlcc\=O+Hc(SC3)SN,CC(=O)Nc1lcc\=O+Hc(SC3)SN,25,replace N at position 8 with 1,flow_matching,0.3,2.0,49,189
144,add,3.0,o,,CC(=O)Nc1lcc\=O+Hc(SC3)SN,CC(o=O)Nc1lcc\=O+Hc(SC3)SN,26,add o at position 3,flow_matching,0.3,2.0,49,189
145,replace,3.0,=,o,CC(o=O)Nc1lcc\=O+Hc(SC3)SN,CC(==O)Nc1lcc\=O+Hc(SC3)SN,26,replace o at position 3 with =,flow_matching,0.3,2.0,49,189
146,replace,4.0,O,=,CC(==O)Nc1lcc\=O+Hc(SC3)SN,CC(=OO)Nc1lcc\=O+Hc(SC3)SN,26,replace = at position 4 with O,flow_matching,0.3,2.0,49,189
147,replace,5.0,),O,CC(=OO)Nc1lcc\=O+Hc(SC3)SN,CC(=O))Nc1lcc\=O+Hc(SC3)SN,26,replace O at position 5 with ),flow_matching,0.3,2.0,49,189
148,replace,6.0,N,),CC(=O))Nc1lcc\=O+Hc(SC3)SN,CC(=O)NNc1lcc\=O+Hc(SC3)SN,26,replace ) at position 6 with N,flow_matching,0.3,2.0,49,189
149,replace,7.0,c,N,CC(=O)NNc1lcc\=O+Hc(SC3)SN,CC(=O)Ncc1lcc\=O+Hc(SC3)SN,26,replace N at position 7 with c,flow_matching,0.3,2.0,49,189
150,replace,8.0,1,c,CC(=O)Ncc1lcc\=O+Hc(SC3)SN,CC(=O)Nc11lcc\=O+Hc(SC3)SN,26,replace c at position 8 with 1,flow_matching,0.3,2.0,49,189
151,replace,9.0,c,1,CC(=O)Nc11lcc\=O+Hc(SC3)SN,CC(=O)Nc1clcc\=O+Hc(SC3)SN,26,replace 1 at position 9 with c,flow_matching,0.3,2.0,49,189
152,replace,10.0,c,l,CC(=O)Nc1clcc\=O+Hc(SC3)SN,CC(=O)Nc1cccc\=O+Hc(SC3)SN,26,replace l at position 10 with c,flow_matching,0.3,2.0,49,189
153,replace,12.0,(,c,CC(=O)Nc1cccc\=O+Hc(SC3)SN,CC(=O)Nc1ccc(\=O+Hc(SC3)SN,26,replace c at position 12 with (,flow_matching,0.3,2.0,49,189
154,replace,13.0,N,\,CC(=O)Nc1ccc(\=O+Hc(SC3)SN,CC(=O)Nc1ccc(N=O+Hc(SC3)SN,26,replace \ at position 13 with N,flow_matching,0.3,2.0,49,189
155,replace,14.0,C,=,CC(=O)Nc1ccc(N=O+Hc(SC3)SN,CC(=O)Nc1ccc(NCO+Hc(SC3)SN,26,replace = at position 14 with C,flow_matching,0.3,2.0,49,189
156,replace,15.0,(,O,CC(=O)Nc1ccc(NCO+Hc(SC3)SN,CC(=O)Nc1ccc(NC(+Hc(SC3)SN,26,replace O at position 15 with (,flow_matching,0.3,2.0,49,189
157,replace,16.0,=,+,CC(=O)Nc1ccc(NC(+Hc(SC3)SN,CC(=O)Nc1ccc(NC(=Hc(SC3)SN,26,replace + at position 16 with =,flow_matching,0.3,2.0,49,189
158,replace,17.0,O,H,CC(=O)Nc1ccc(NC(=Hc(SC3)SN,CC(=O)Nc1ccc(NC(=Oc(SC3)SN,26,replace H at position 17 with O,flow_matching,0.3,2.0,49,189
159,replace,18.0,),c,CC(=O)Nc1ccc(NC(=Oc(SC3)SN,CC(=O)Nc1ccc(NC(=O)(SC3)SN,26,replace c at position 18 with ),flow_matching,0.3,2.0,49,189
160,replace,19.0,c,(,CC(=O)Nc1ccc(NC(=O)(SC3)SN,CC(=O)Nc1ccc(NC(=O)cSC3)SN,26,replace ( at position 19 with c,flow_matching,0.3,2.0,49,189
161,replace,20.0,2,S,CC(=O)Nc1ccc(NC(=O)cSC3)SN,CC(=O)Nc1ccc(NC(=O)c2C3)SN,26,replace S at position 20 with 2,flow_matching,0.3,2.0,49,189
162,replace,21.0,n,C,CC(=O)Nc1ccc(NC(=O)c2C3)SN,CC(=O)Nc1ccc(NC(=O)c2n3)SN,26,replace C at position 21 with n,flow_matching,0.3,2.0,49,189
163,replace,22.0,n,3,CC(=O)Nc1ccc(NC(=O)c2n3)SN,CC(=O)Nc1ccc(NC(=O)c2nn)SN,26,replace 3 at position 22 with n,flow_matching,0.3,2.0,49,189
164,replace,23.0,n,),CC(=O)Nc1ccc(NC(=O)c2nn)SN,CC(=O)Nc1ccc(NC(=O)c2nnnSN,26,replace ) at position 23 with n,flow_matching,0.3,2.0,49,189
165,replace,24.0,(,S,CC(=O)Nc1ccc(NC(=O)c2nnnSN,CC(=O)Nc1ccc(NC(=O)c2nnn(N,26,replace S at position 24 with (,flow_matching,0.3,2.0,49,189
166,replace,25.0,-,N,CC(=O)Nc1ccc(NC(=O)c2nnn(N,CC(=O)Nc1ccc(NC(=O)c2nnn(-,26,replace N at position 25 with -,flow_matching,0.3,2.0,49,189
167,add,26.0,c,,CC(=O)Nc1ccc(NC(=O)c2nnn(-,CC(=O)Nc1ccc(NC(=O)c2nnn(-c,27,add c at position 26,flow_matching,0.3,2.0,49,189
168,add,27.0,3,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3,28,add 3 at position 27,flow_matching,0.3,2.0,49,189
169,add,28.0,c,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3c,29,add c at position 28,flow_matching,0.3,2.0,49,189
170,add,29.0,c,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3c,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3cc,30,add c at position 29,flow_matching,0.3,2.0,49,189
171,add,30.0,c,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3cc,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc,31,add c at position 30,flow_matching,0.3,2.0,49,189
172,add,31.0,(,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(,32,add ( at position 31,flow_matching,0.3,2.0,49,189
173,add,32.0,C,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C,33,add C at position 32,flow_matching,0.3,2.0,49,189
174,add,33.0,),,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C),34,add ) at position 33,flow_matching,0.3,2.0,49,189
175,add,34.0,c,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C),CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c,35,add c at position 34,flow_matching,0.3,2.0,49,189
176,add,35.0,(,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(,36,add ( at position 35,flow_matching,0.3,2.0,49,189
177,add,36.0,C,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C,37,add C at position 36,flow_matching,0.3,2.0,49,189
178,add,37.0,),,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C),38,add ) at position 37,flow_matching,0.3,2.0,49,189
179,add,38.0,c,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C),CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c,39,add c at position 38,flow_matching,0.3,2.0,49,189
180,add,39.0,3,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3,40,add 3 at position 39,flow_matching,0.3,2.0,49,189
181,add,40.0,),,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3),41,add ) at position 40,flow_matching,0.3,2.0,49,189
182,add,41.0,c,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3),CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c,42,add c at position 41,flow_matching,0.3,2.0,49,189
183,add,42.0,2,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2,43,add 2 at position 42,flow_matching,0.3,2.0,49,189
184,add,43.0,C,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C,44,add C at position 43,flow_matching,0.3,2.0,49,189
185,add,44.0,),,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C),45,add ) at position 44,flow_matching,0.3,2.0,49,189
186,add,45.0,c,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C),CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C)c,46,add c at position 45,flow_matching,0.3,2.0,49,189
187,add,46.0,c,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C)c,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C)cc,47,add c at position 46,flow_matching,0.3,2.0,49,189
188,add,47.0,1,,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C)cc,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C)cc1,48,add 1 at position 47,flow_matching,0.3,2.0,49,189
189,add,48.0,"
",,CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C)cc1,"CC(=O)Nc1ccc(NC(=O)c2nnn(-c3ccc(C)c(C)c3)c2C)cc1
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,189
