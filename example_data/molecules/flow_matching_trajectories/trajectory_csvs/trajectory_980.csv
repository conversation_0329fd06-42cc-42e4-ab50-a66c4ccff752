step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,103
1,add,0.0,n,,,n,1,add n at position 0,flow_matching,0.3,2.0,43,103
2,replace,0.0,C,n,n,C,1,replace n at position 0 with C,flow_matching,0.3,2.0,43,103
3,replace,0.0,+,C,C,+,1,replace <PERSON> at position 0 with +,flow_matching,0.3,2.0,43,103
4,add,0.0,+,,+,++,2,add + at position 0,flow_matching,0.3,2.0,43,103
5,add,0.0,3,,++,3++,3,add 3 at position 0,flow_matching,0.3,2.0,43,103
6,replace,0.0,C,3,3++,C++,3,replace 3 at position 0 with <PERSON>,flow_matching,0.3,2.0,43,103
7,replace,1.0,C,+,C++,CC+,3,replace + at position 1 with C,flow_matching,0.3,2.0,43,103
8,remove,0.0,C,,<PERSON>+,C+,2,remove C from position 0,flow_matching,0.3,2.0,43,103
9,add,0.0,3,,C+,3C+,3,add 3 at position 0,flow_matching,0.3,2.0,43,103
10,replace,1.0,n,C,3C+,3n+,3,replace C at position 1 with n,flow_matching,0.3,2.0,43,103
11,remove,1.0,n,,3n+,3+,2,remove n from position 1,flow_matching,0.3,2.0,43,103
12,remove,1.0,+,,3+,3,1,remove + from position 1,flow_matching,0.3,2.0,43,103
13,replace,0.0,C,3,3,C,1,replace 3 at position 0 with C,flow_matching,0.3,2.0,43,103
14,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,43,103
15,add,1.0,@,,CC,C@C,3,add @ at position 1,flow_matching,0.3,2.0,43,103
16,add,1.0,2,,C@C,C2@C,4,add 2 at position 1,flow_matching,0.3,2.0,43,103
17,replace,0.0,#,C,C2@C,#2@C,4,replace C at position 0 with #,flow_matching,0.3,2.0,43,103
18,replace,0.0,C,#,#2@C,C2@C,4,replace # at position 0 with C,flow_matching,0.3,2.0,43,103
19,replace,1.0,C,2,C2@C,CC@C,4,replace 2 at position 1 with C,flow_matching,0.3,2.0,43,103
20,add,0.0,1,,CC@C,1CC@C,5,add 1 at position 0,flow_matching,0.3,2.0,43,103
21,remove,3.0,@,,1CC@C,1CCC,4,remove @ from position 3,flow_matching,0.3,2.0,43,103
22,add,0.0,+,,1CCC,+1CCC,5,add + at position 0,flow_matching,0.3,2.0,43,103
23,replace,3.0,c,C,+1CCC,+1CcC,5,replace C at position 3 with c,flow_matching,0.3,2.0,43,103
24,replace,0.0,C,+,+1CcC,C1CcC,5,replace + at position 0 with C,flow_matching,0.3,2.0,43,103
25,replace,4.0,/,C,C1CcC,C1Cc/,5,replace C at position 4 with /,flow_matching,0.3,2.0,43,103
26,replace,1.0,C,1,C1Cc/,CCCc/,5,replace 1 at position 1 with C,flow_matching,0.3,2.0,43,103
27,replace,3.0,N,c,CCCc/,CCCN/,5,replace c at position 3 with N,flow_matching,0.3,2.0,43,103
28,replace,4.0,1,/,CCCN/,CCCN1,5,replace / at position 4 with 1,flow_matching,0.3,2.0,43,103
29,add,5.0,C,,CCCN1,CCCN1C,6,add C at position 5,flow_matching,0.3,2.0,43,103
30,replace,0.0,S,C,CCCN1C,SCCN1C,6,replace C at position 0 with S,flow_matching,0.3,2.0,43,103
31,replace,0.0,C,S,SCCN1C,CCCN1C,6,replace S at position 0 with C,flow_matching,0.3,2.0,43,103
32,remove,5.0,C,,CCCN1C,CCCN1,5,remove C from position 5,flow_matching,0.3,2.0,43,103
33,add,5.0,B,,CCCN1,CCCN1B,6,add B at position 5,flow_matching,0.3,2.0,43,103
34,replace,5.0,C,B,CCCN1B,CCCN1C,6,replace B at position 5 with C,flow_matching,0.3,2.0,43,103
35,add,4.0,+,,CCCN1C,CCCN+1C,7,add + at position 4,flow_matching,0.3,2.0,43,103
36,add,4.0,S,,CCCN+1C,CCCNS+1C,8,add S at position 4,flow_matching,0.3,2.0,43,103
37,replace,7.0,=,C,CCCNS+1C,CCCNS+1=,8,replace C at position 7 with =,flow_matching,0.3,2.0,43,103
38,replace,3.0,#,N,CCCNS+1=,CCC#S+1=,8,replace N at position 3 with #,flow_matching,0.3,2.0,43,103
39,replace,3.0,N,#,CCC#S+1=,CCCNS+1=,8,replace # at position 3 with N,flow_matching,0.3,2.0,43,103
40,add,3.0,),,CCCNS+1=,CCC)NS+1=,9,add ) at position 3,flow_matching,0.3,2.0,43,103
41,replace,1.0,-,C,CCC)NS+1=,C-C)NS+1=,9,replace C at position 1 with -,flow_matching,0.3,2.0,43,103
42,remove,3.0,),,C-C)NS+1=,C-CNS+1=,8,remove ) from position 3,flow_matching,0.3,2.0,43,103
43,remove,1.0,-,,C-CNS+1=,CCNS+1=,7,remove - from position 1,flow_matching,0.3,2.0,43,103
44,add,7.0,I,,CCNS+1=,CCNS+1=I,8,add I at position 7,flow_matching,0.3,2.0,43,103
45,remove,3.0,S,,CCNS+1=I,CCN+1=I,7,remove S from position 3,flow_matching,0.3,2.0,43,103
46,add,4.0,6,,CCN+1=I,CCN+61=I,8,add 6 at position 4,flow_matching,0.3,2.0,43,103
47,add,8.0,O,,CCN+61=I,CCN+61=IO,9,add O at position 8,flow_matching,0.3,2.0,43,103
48,replace,6.0,6,=,CCN+61=IO,CCN+616IO,9,replace = at position 6 with 6,flow_matching,0.3,2.0,43,103
49,replace,2.0,C,N,CCN+616IO,CCC+616IO,9,replace N at position 2 with C,flow_matching,0.3,2.0,43,103
50,add,3.0,o,,CCC+616IO,CCCo+616IO,10,add o at position 3,flow_matching,0.3,2.0,43,103
51,replace,3.0,N,o,CCCo+616IO,CCCN+616IO,10,replace o at position 3 with N,flow_matching,0.3,2.0,43,103
52,replace,4.0,1,+,CCCN+616IO,CCCN1616IO,10,replace + at position 4 with 1,flow_matching,0.3,2.0,43,103
53,replace,5.0,C,6,CCCN1616IO,CCCN1C16IO,10,replace 6 at position 5 with C,flow_matching,0.3,2.0,43,103
54,add,9.0,S,,CCCN1C16IO,CCCN1C16ISO,11,add S at position 9,flow_matching,0.3,2.0,43,103
55,remove,4.0,1,,CCCN1C16ISO,CCCNC16ISO,10,remove 1 from position 4,flow_matching,0.3,2.0,43,103
56,replace,4.0,1,C,CCCNC16ISO,CCCN116ISO,10,replace C at position 4 with 1,flow_matching,0.3,2.0,43,103
57,add,5.0,n,,CCCN116ISO,CCCN1n16ISO,11,add n at position 5,flow_matching,0.3,2.0,43,103
58,replace,5.0,C,n,CCCN1n16ISO,CCCN1C16ISO,11,replace n at position 5 with C,flow_matching,0.3,2.0,43,103
59,replace,6.0,(,1,CCCN1C16ISO,CCCN1C(6ISO,11,replace 1 at position 6 with (,flow_matching,0.3,2.0,43,103
60,replace,7.0,N,6,CCCN1C(6ISO,CCCN1C(NISO,11,replace 6 at position 7 with N,flow_matching,0.3,2.0,43,103
61,remove,0.0,C,,CCCN1C(NISO,CCN1C(NISO,10,remove C from position 0,flow_matching,0.3,2.0,43,103
62,replace,8.0,+,S,CCN1C(NISO,CCN1C(NI+O,10,replace S at position 8 with +,flow_matching,0.3,2.0,43,103
63,replace,2.0,C,N,CCN1C(NI+O,CCC1C(NI+O,10,replace N at position 2 with C,flow_matching,0.3,2.0,43,103
64,replace,3.0,N,1,CCC1C(NI+O,CCCNC(NI+O,10,replace 1 at position 3 with N,flow_matching,0.3,2.0,43,103
65,replace,4.0,1,C,CCCNC(NI+O,CCCN1(NI+O,10,replace C at position 4 with 1,flow_matching,0.3,2.0,43,103
66,replace,5.0,C,(,CCCN1(NI+O,CCCN1CNI+O,10,replace ( at position 5 with C,flow_matching,0.3,2.0,43,103
67,replace,6.0,(,N,CCCN1CNI+O,CCCN1C(I+O,10,replace N at position 6 with (,flow_matching,0.3,2.0,43,103
68,replace,7.0,N,I,CCCN1C(I+O,CCCN1C(N+O,10,replace I at position 7 with N,flow_matching,0.3,2.0,43,103
69,replace,8.0,),+,CCCN1C(N+O,CCCN1C(N)O,10,replace + at position 8 with ),flow_matching,0.3,2.0,43,103
70,replace,9.0,=,O,CCCN1C(N)O,CCCN1C(N)=,10,replace O at position 9 with =,flow_matching,0.3,2.0,43,103
71,add,10.0,[,,CCCN1C(N)=,CCCN1C(N)=[,11,add [ at position 10,flow_matching,0.3,2.0,43,103
72,add,11.0,N,,CCCN1C(N)=[,CCCN1C(N)=[N,12,add N at position 11,flow_matching,0.3,2.0,43,103
73,add,12.0,H,,CCCN1C(N)=[N,CCCN1C(N)=[NH,13,add H at position 12,flow_matching,0.3,2.0,43,103
74,add,13.0,+,,CCCN1C(N)=[NH,CCCN1C(N)=[NH+,14,add + at position 13,flow_matching,0.3,2.0,43,103
75,add,14.0,],,CCCN1C(N)=[NH+,CCCN1C(N)=[NH+],15,add ] at position 14,flow_matching,0.3,2.0,43,103
76,add,15.0,C,,CCCN1C(N)=[NH+],CCCN1C(N)=[NH+]C,16,add C at position 15,flow_matching,0.3,2.0,43,103
77,add,16.0,[,,CCCN1C(N)=[NH+]C,CCCN1C(N)=[NH+]C[,17,add [ at position 16,flow_matching,0.3,2.0,43,103
78,add,17.0,C,,CCCN1C(N)=[NH+]C[,CCCN1C(N)=[NH+]C[C,18,add C at position 17,flow_matching,0.3,2.0,43,103
79,add,18.0,@,,CCCN1C(N)=[NH+]C[C,CCCN1C(N)=[NH+]C[C@,19,add @ at position 18,flow_matching,0.3,2.0,43,103
80,add,19.0,@,,CCCN1C(N)=[NH+]C[C@,CCCN1C(N)=[NH+]C[C@@,20,add @ at position 19,flow_matching,0.3,2.0,43,103
81,add,20.0,H,,CCCN1C(N)=[NH+]C[C@@,CCCN1C(N)=[NH+]C[C@@H,21,add H at position 20,flow_matching,0.3,2.0,43,103
82,add,21.0,],,CCCN1C(N)=[NH+]C[C@@H,CCCN1C(N)=[NH+]C[C@@H],22,add ] at position 21,flow_matching,0.3,2.0,43,103
83,add,22.0,1,,CCCN1C(N)=[NH+]C[C@@H],CCCN1C(N)=[NH+]C[C@@H]1,23,add 1 at position 22,flow_matching,0.3,2.0,43,103
84,add,23.0,c,,CCCN1C(N)=[NH+]C[C@@H]1,CCCN1C(N)=[NH+]C[C@@H]1c,24,add c at position 23,flow_matching,0.3,2.0,43,103
85,add,24.0,1,,CCCN1C(N)=[NH+]C[C@@H]1c,CCCN1C(N)=[NH+]C[C@@H]1c1,25,add 1 at position 24,flow_matching,0.3,2.0,43,103
86,add,25.0,c,,CCCN1C(N)=[NH+]C[C@@H]1c1,CCCN1C(N)=[NH+]C[C@@H]1c1c,26,add c at position 25,flow_matching,0.3,2.0,43,103
87,add,26.0,c,,CCCN1C(N)=[NH+]C[C@@H]1c1c,CCCN1C(N)=[NH+]C[C@@H]1c1cc,27,add c at position 26,flow_matching,0.3,2.0,43,103
88,add,27.0,(,,CCCN1C(N)=[NH+]C[C@@H]1c1cc,CCCN1C(N)=[NH+]C[C@@H]1c1cc(,28,add ( at position 27,flow_matching,0.3,2.0,43,103
89,add,28.0,C,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(,CCCN1C(N)=[NH+]C[C@@H]1c1cc(C,29,add C at position 28,flow_matching,0.3,2.0,43,103
90,add,29.0,l,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(C,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl,30,add l at position 29,flow_matching,0.3,2.0,43,103
91,add,30.0,),,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl),31,add ) at position 30,flow_matching,0.3,2.0,43,103
92,add,31.0,c,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl),CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c,32,add c at position 31,flow_matching,0.3,2.0,43,103
93,add,32.0,2,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2,33,add 2 at position 32,flow_matching,0.3,2.0,43,103
94,add,33.0,c,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c,34,add c at position 33,flow_matching,0.3,2.0,43,103
95,add,34.0,(,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(,35,add ( at position 34,flow_matching,0.3,2.0,43,103
96,add,35.0,c,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c,36,add c at position 35,flow_matching,0.3,2.0,43,103
97,add,36.0,1,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1,37,add 1 at position 36,flow_matching,0.3,2.0,43,103
98,add,37.0,),,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1),38,add ) at position 37,flow_matching,0.3,2.0,43,103
99,add,38.0,O,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1),CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1)O,39,add O at position 38,flow_matching,0.3,2.0,43,103
100,add,39.0,C,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1)O,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1)OC,40,add C at position 39,flow_matching,0.3,2.0,43,103
101,add,40.0,O,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1)OC,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1)OCO,41,add O at position 40,flow_matching,0.3,2.0,43,103
102,add,41.0,2,,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1)OCO,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1)OCO2,42,add 2 at position 41,flow_matching,0.3,2.0,43,103
103,add,42.0,"
",,CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1)OCO2,"CCCN1C(N)=[NH+]C[C@@H]1c1cc(Cl)c2c(c1)OCO2
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,103
