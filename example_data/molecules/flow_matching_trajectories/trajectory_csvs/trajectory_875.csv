step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,184
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,49,184
2,replace,0.0,/,C,C,/,1,replace <PERSON> at position 0 with /,flow_matching,0.3,2.0,49,184
3,remove,0.0,/,,/,,0,remove / from position 0,flow_matching,0.3,2.0,49,184
4,add,0.0,5,,,5,1,add 5 at position 0,flow_matching,0.3,2.0,49,184
5,replace,0.0,C,5,5,C,1,replace 5 at position 0 with C,flow_matching,0.3,2.0,49,184
6,replace,0.0,c,C,C,c,1,replace <PERSON> at position 0 with c,flow_matching,0.3,2.0,49,184
7,replace,0.0,+,c,c,+,1,replace c at position 0 with +,flow_matching,0.3,2.0,49,184
8,replace,0.0,C,+,+,C,1,replace + at position 0 with C,flow_matching,0.3,2.0,49,184
9,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,49,184
10,add,0.0,4,,CC,4CC,3,add 4 at position 0,flow_matching,0.3,2.0,49,184
11,replace,0.0,C,4,4CC,CCC,3,replace 4 at position 0 with C,flow_matching,0.3,2.0,49,184
12,replace,1.0,H,C,CCC,CHC,3,replace C at position 1 with H,flow_matching,0.3,2.0,49,184
13,remove,1.0,H,,CHC,CC,2,remove H from position 1,flow_matching,0.3,2.0,49,184
14,remove,1.0,C,,CC,C,1,remove C from position 1,flow_matching,0.3,2.0,49,184
15,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,49,184
16,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,49,184
17,add,0.0,5,,C,5C,2,add 5 at position 0,flow_matching,0.3,2.0,49,184
18,remove,1.0,C,,5C,5,1,remove C from position 1,flow_matching,0.3,2.0,49,184
19,remove,0.0,5,,5,,0,remove 5 from position 0,flow_matching,0.3,2.0,49,184
20,add,0.0,=,,,=,1,add = at position 0,flow_matching,0.3,2.0,49,184
21,replace,0.0,C,=,=,C,1,replace = at position 0 with C,flow_matching,0.3,2.0,49,184
22,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,49,184
23,add,0.0,1,,,1,1,add 1 at position 0,flow_matching,0.3,2.0,49,184
24,remove,0.0,1,,1,,0,remove 1 from position 0,flow_matching,0.3,2.0,49,184
25,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,49,184
26,remove,0.0,3,,3,,0,remove 3 from position 0,flow_matching,0.3,2.0,49,184
27,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,49,184
28,add,1.0,1,,C,C1,2,add 1 at position 1,flow_matching,0.3,2.0,49,184
29,replace,1.0,C,1,C1,CC,2,replace 1 at position 1 with C,flow_matching,0.3,2.0,49,184
30,add,2.0,[,,CC,CC[,3,add [ at position 2,flow_matching,0.3,2.0,49,184
31,add,3.0,C,,CC[,CC[C,4,add C at position 3,flow_matching,0.3,2.0,49,184
32,add,4.0,@,,CC[C,CC[C@,5,add @ at position 4,flow_matching,0.3,2.0,49,184
33,remove,0.0,C,,CC[C@,C[C@,4,remove C from position 0,flow_matching,0.3,2.0,49,184
34,remove,2.0,C,,C[C@,C[@,3,remove C from position 2,flow_matching,0.3,2.0,49,184
35,replace,2.0,F,@,C[@,C[F,3,replace @ at position 2 with F,flow_matching,0.3,2.0,49,184
36,replace,1.0,C,[,C[F,CCF,3,replace [ at position 1 with C,flow_matching,0.3,2.0,49,184
37,replace,2.0,[,F,CCF,CC[,3,replace F at position 2 with [,flow_matching,0.3,2.0,49,184
38,add,2.0,I,,CC[,CCI[,4,add I at position 2,flow_matching,0.3,2.0,49,184
39,replace,3.0,B,[,CCI[,CCIB,4,replace [ at position 3 with B,flow_matching,0.3,2.0,49,184
40,replace,0.0,\,C,CCIB,\CIB,4,replace C at position 0 with \,flow_matching,0.3,2.0,49,184
41,replace,0.0,7,\,\CIB,7CIB,4,replace \ at position 0 with 7,flow_matching,0.3,2.0,49,184
42,remove,3.0,B,,7CIB,7CI,3,remove B from position 3,flow_matching,0.3,2.0,49,184
43,add,2.0,F,,7CI,7CFI,4,add F at position 2,flow_matching,0.3,2.0,49,184
44,add,1.0,s,,7CFI,7sCFI,5,add s at position 1,flow_matching,0.3,2.0,49,184
45,add,5.0,],,7sCFI,7sCFI],6,add ] at position 5,flow_matching,0.3,2.0,49,184
46,replace,5.0,/,],7sCFI],7sCFI/,6,replace ] at position 5 with /,flow_matching,0.3,2.0,49,184
47,replace,2.0,2,C,7sCFI/,7s2FI/,6,replace C at position 2 with 2,flow_matching,0.3,2.0,49,184
48,add,5.0,N,,7s2FI/,7s2FIN/,7,add N at position 5,flow_matching,0.3,2.0,49,184
49,add,1.0,\,,7s2FIN/,7\s2FIN/,8,add \ at position 1,flow_matching,0.3,2.0,49,184
50,add,3.0,=,,7\s2FIN/,7\s=2FIN/,9,add = at position 3,flow_matching,0.3,2.0,49,184
51,replace,0.0,C,7,7\s=2FIN/,C\s=2FIN/,9,replace 7 at position 0 with C,flow_matching,0.3,2.0,49,184
52,add,4.0,/,,C\s=2FIN/,C\s=/2FIN/,10,add / at position 4,flow_matching,0.3,2.0,49,184
53,remove,9.0,/,,C\s=/2FIN/,C\s=/2FIN,9,remove / from position 9,flow_matching,0.3,2.0,49,184
54,add,5.0,),,C\s=/2FIN,C\s=/)2FIN,10,add ) at position 5,flow_matching,0.3,2.0,49,184
55,replace,1.0,C,\,C\s=/)2FIN,CCs=/)2FIN,10,replace \ at position 1 with C,flow_matching,0.3,2.0,49,184
56,replace,7.0,O,F,CCs=/)2FIN,CCs=/)2OIN,10,replace F at position 7 with O,flow_matching,0.3,2.0,49,184
57,remove,2.0,s,,CCs=/)2OIN,CC=/)2OIN,9,remove s from position 2,flow_matching,0.3,2.0,49,184
58,add,1.0,r,,CC=/)2OIN,CrC=/)2OIN,10,add r at position 1,flow_matching,0.3,2.0,49,184
59,replace,8.0,[,I,CrC=/)2OIN,CrC=/)2O[N,10,replace I at position 8 with [,flow_matching,0.3,2.0,49,184
60,replace,1.0,C,r,CrC=/)2O[N,CCC=/)2O[N,10,replace r at position 1 with C,flow_matching,0.3,2.0,49,184
61,remove,7.0,O,,CCC=/)2O[N,CCC=/)2[N,9,remove O from position 7,flow_matching,0.3,2.0,49,184
62,replace,1.0,O,C,CCC=/)2[N,COC=/)2[N,9,replace C at position 1 with O,flow_matching,0.3,2.0,49,184
63,add,1.0,S,,COC=/)2[N,CSOC=/)2[N,10,add S at position 1,flow_matching,0.3,2.0,49,184
64,add,8.0,H,,CSOC=/)2[N,CSOC=/)2H[N,11,add H at position 8,flow_matching,0.3,2.0,49,184
65,add,8.0,=,,CSOC=/)2H[N,CSOC=/)2=H[N,12,add = at position 8,flow_matching,0.3,2.0,49,184
66,add,10.0,F,,CSOC=/)2=H[N,CSOC=/)2=HF[N,13,add F at position 10,flow_matching,0.3,2.0,49,184
67,replace,7.0,=,2,CSOC=/)2=HF[N,CSOC=/)==HF[N,13,replace 2 at position 7 with =,flow_matching,0.3,2.0,49,184
68,add,13.0,-,,CSOC=/)==HF[N,CSOC=/)==HF[N-,14,add - at position 13,flow_matching,0.3,2.0,49,184
69,add,8.0,H,,CSOC=/)==HF[N-,CSOC=/)=H=HF[N-,15,add H at position 8,flow_matching,0.3,2.0,49,184
70,replace,9.0,N,=,CSOC=/)=H=HF[N-,CSOC=/)=HNHF[N-,15,replace = at position 9 with N,flow_matching,0.3,2.0,49,184
71,remove,11.0,F,,CSOC=/)=HNHF[N-,CSOC=/)=HNH[N-,14,remove F from position 11,flow_matching,0.3,2.0,49,184
72,replace,11.0,],[,CSOC=/)=HNH[N-,CSOC=/)=HNH]N-,14,replace [ at position 11 with ],flow_matching,0.3,2.0,49,184
73,remove,5.0,/,,CSOC=/)=HNH]N-,CSOC=)=HNH]N-,13,remove / from position 5,flow_matching,0.3,2.0,49,184
74,remove,7.0,H,,CSOC=)=HNH]N-,CSOC=)=NH]N-,12,remove H from position 7,flow_matching,0.3,2.0,49,184
75,replace,1.0,C,S,CSOC=)=NH]N-,CCOC=)=NH]N-,12,replace S at position 1 with C,flow_matching,0.3,2.0,49,184
76,add,11.0,C,,CCOC=)=NH]N-,CCOC=)=NH]NC-,13,add C at position 11,flow_matching,0.3,2.0,49,184
77,replace,2.0,[,O,CCOC=)=NH]NC-,CC[C=)=NH]NC-,13,replace O at position 2 with [,flow_matching,0.3,2.0,49,184
78,add,1.0,/,,CC[C=)=NH]NC-,C/C[C=)=NH]NC-,14,add / at position 1,flow_matching,0.3,2.0,49,184
79,replace,1.0,C,/,C/C[C=)=NH]NC-,CCC[C=)=NH]NC-,14,replace / at position 1 with C,flow_matching,0.3,2.0,49,184
80,replace,8.0,),N,CCC[C=)=NH]NC-,CCC[C=)=)H]NC-,14,replace N at position 8 with ),flow_matching,0.3,2.0,49,184
81,replace,2.0,[,C,CCC[C=)=)H]NC-,CC[[C=)=)H]NC-,14,replace C at position 2 with [,flow_matching,0.3,2.0,49,184
82,replace,12.0,H,C,CC[[C=)=)H]NC-,CC[[C=)=)H]NH-,14,replace C at position 12 with H,flow_matching,0.3,2.0,49,184
83,remove,1.0,C,,CC[[C=)=)H]NH-,C[[C=)=)H]NH-,13,remove C from position 1,flow_matching,0.3,2.0,49,184
84,replace,1.0,C,[,C[[C=)=)H]NH-,CC[C=)=)H]NH-,13,replace [ at position 1 with C,flow_matching,0.3,2.0,49,184
85,replace,4.0,@,=,CC[C=)=)H]NH-,CC[C@)=)H]NH-,13,replace = at position 4 with @,flow_matching,0.3,2.0,49,184
86,add,3.0,3,,CC[C@)=)H]NH-,CC[3C@)=)H]NH-,14,add 3 at position 3,flow_matching,0.3,2.0,49,184
87,replace,3.0,C,3,CC[3C@)=)H]NH-,CC[CC@)=)H]NH-,14,replace 3 at position 3 with C,flow_matching,0.3,2.0,49,184
88,remove,4.0,C,,CC[CC@)=)H]NH-,CC[C@)=)H]NH-,13,remove C from position 4,flow_matching,0.3,2.0,49,184
89,replace,5.0,@,),CC[C@)=)H]NH-,CC[C@@=)H]NH-,13,replace ) at position 5 with @,flow_matching,0.3,2.0,49,184
90,add,8.0,2,,CC[C@@=)H]NH-,CC[C@@=)2H]NH-,14,add 2 at position 8,flow_matching,0.3,2.0,49,184
91,remove,10.0,],,CC[C@@=)2H]NH-,CC[C@@=)2HNH-,13,remove ] from position 10,flow_matching,0.3,2.0,49,184
92,replace,6.0,H,=,CC[C@@=)2HNH-,CC[C@@H)2HNH-,13,replace = at position 6 with H,flow_matching,0.3,2.0,49,184
93,add,10.0,I,,CC[C@@H)2HNH-,CC[C@@H)2HINH-,14,add I at position 10,flow_matching,0.3,2.0,49,184
94,replace,7.0,],),CC[C@@H)2HINH-,CC[C@@H]2HINH-,14,replace ) at position 7 with ],flow_matching,0.3,2.0,49,184
95,replace,8.0,(,2,CC[C@@H]2HINH-,CC[C@@H](HINH-,14,replace 2 at position 8 with (,flow_matching,0.3,2.0,49,184
96,remove,12.0,H,,CC[C@@H](HINH-,CC[C@@H](HIN-,13,remove H from position 12,flow_matching,0.3,2.0,49,184
97,replace,2.0,S,[,CC[C@@H](HIN-,CCSC@@H](HIN-,13,replace [ at position 2 with S,flow_matching,0.3,2.0,49,184
98,add,8.0,c,,CCSC@@H](HIN-,CCSC@@H]c(HIN-,14,add c at position 8,flow_matching,0.3,2.0,49,184
99,add,9.0,I,,CCSC@@H]c(HIN-,CCSC@@H]cI(HIN-,15,add I at position 9,flow_matching,0.3,2.0,49,184
100,replace,14.0,=,-,CCSC@@H]cI(HIN-,CCSC@@H]cI(HIN=,15,replace - at position 14 with =,flow_matching,0.3,2.0,49,184
101,remove,2.0,S,,CCSC@@H]cI(HIN=,CCC@@H]cI(HIN=,14,remove S from position 2,flow_matching,0.3,2.0,49,184
102,add,3.0,\,,CCC@@H]cI(HIN=,CCC\@@H]cI(HIN=,15,add \ at position 3,flow_matching,0.3,2.0,49,184
103,remove,6.0,H,,CCC\@@H]cI(HIN=,CCC\@@]cI(HIN=,14,remove H from position 6,flow_matching,0.3,2.0,49,184
104,replace,0.0,3,C,CCC\@@]cI(HIN=,3CC\@@]cI(HIN=,14,replace C at position 0 with 3,flow_matching,0.3,2.0,49,184
105,add,11.0,(,,3CC\@@]cI(HIN=,3CC\@@]cI(H(IN=,15,add ( at position 11,flow_matching,0.3,2.0,49,184
106,replace,0.0,C,3,3CC\@@]cI(H(IN=,CCC\@@]cI(H(IN=,15,replace 3 at position 0 with C,flow_matching,0.3,2.0,49,184
107,replace,1.0,O,C,CCC\@@]cI(H(IN=,COC\@@]cI(H(IN=,15,replace C at position 1 with O,flow_matching,0.3,2.0,49,184
108,replace,2.0,B,C,COC\@@]cI(H(IN=,COB\@@]cI(H(IN=,15,replace C at position 2 with B,flow_matching,0.3,2.0,49,184
109,replace,9.0,6,(,COB\@@]cI(H(IN=,COB\@@]cI6H(IN=,15,replace ( at position 9 with 6,flow_matching,0.3,2.0,49,184
110,add,4.0,+,,COB\@@]cI6H(IN=,COB\+@@]cI6H(IN=,16,add + at position 4,flow_matching,0.3,2.0,49,184
111,remove,14.0,N,,COB\+@@]cI6H(IN=,COB\+@@]cI6H(I=,15,remove N from position 14,flow_matching,0.3,2.0,49,184
112,replace,1.0,C,O,COB\+@@]cI6H(I=,CCB\+@@]cI6H(I=,15,replace O at position 1 with C,flow_matching,0.3,2.0,49,184
113,replace,5.0,4,@,CCB\+@@]cI6H(I=,CCB\+4@]cI6H(I=,15,replace @ at position 5 with 4,flow_matching,0.3,2.0,49,184
114,add,10.0,H,,CCB\+4@]cI6H(I=,CCB\+4@]cIH6H(I=,16,add H at position 10,flow_matching,0.3,2.0,49,184
115,add,7.0,=,,CCB\+4@]cIH6H(I=,CCB\+4@=]cIH6H(I=,17,add = at position 7,flow_matching,0.3,2.0,49,184
116,replace,2.0,[,B,CCB\+4@=]cIH6H(I=,CC[\+4@=]cIH6H(I=,17,replace B at position 2 with [,flow_matching,0.3,2.0,49,184
117,add,3.0,@,,CC[\+4@=]cIH6H(I=,CC[@\+4@=]cIH6H(I=,18,add @ at position 3,flow_matching,0.3,2.0,49,184
118,replace,3.0,C,@,CC[@\+4@=]cIH6H(I=,CC[C\+4@=]cIH6H(I=,18,replace @ at position 3 with C,flow_matching,0.3,2.0,49,184
119,add,15.0,],,CC[C\+4@=]cIH6H(I=,CC[C\+4@=]cIH6H](I=,19,add ] at position 15,flow_matching,0.3,2.0,49,184
120,replace,4.0,@,\,CC[C\+4@=]cIH6H](I=,CC[C@+4@=]cIH6H](I=,19,replace \ at position 4 with @,flow_matching,0.3,2.0,49,184
121,remove,18.0,=,,CC[C@+4@=]cIH6H](I=,CC[C@+4@=]cIH6H](I,18,remove = from position 18,flow_matching,0.3,2.0,49,184
122,replace,8.0,3,=,CC[C@+4@=]cIH6H](I,CC[C@+4@3]cIH6H](I,18,replace = at position 8 with 3,flow_matching,0.3,2.0,49,184
123,remove,4.0,@,,CC[C@+4@3]cIH6H](I,CC[C+4@3]cIH6H](I,17,remove @ from position 4,flow_matching,0.3,2.0,49,184
124,remove,3.0,C,,CC[C+4@3]cIH6H](I,CC[+4@3]cIH6H](I,16,remove C from position 3,flow_matching,0.3,2.0,49,184
125,replace,11.0,),6,CC[+4@3]cIH6H](I,CC[+4@3]cIH)H](I,16,replace 6 at position 11 with ),flow_matching,0.3,2.0,49,184
126,add,2.0,c,,CC[+4@3]cIH)H](I,CCc[+4@3]cIH)H](I,17,add c at position 2,flow_matching,0.3,2.0,49,184
127,remove,13.0,H,,CCc[+4@3]cIH)H](I,CCc[+4@3]cIH)](I,16,remove H from position 13,flow_matching,0.3,2.0,49,184
128,add,6.0,2,,CCc[+4@3]cIH)](I,CCc[+42@3]cIH)](I,17,add 2 at position 6,flow_matching,0.3,2.0,49,184
129,replace,6.0,S,2,CCc[+42@3]cIH)](I,CCc[+4S@3]cIH)](I,17,replace 2 at position 6 with S,flow_matching,0.3,2.0,49,184
130,remove,1.0,C,,CCc[+4S@3]cIH)](I,Cc[+4S@3]cIH)](I,16,remove C from position 1,flow_matching,0.3,2.0,49,184
131,replace,15.0,H,I,Cc[+4S@3]cIH)](I,Cc[+4S@3]cIH)](H,16,replace I at position 15 with H,flow_matching,0.3,2.0,49,184
132,add,11.0,+,,Cc[+4S@3]cIH)](H,Cc[+4S@3]cI+H)](H,17,add + at position 11,flow_matching,0.3,2.0,49,184
133,replace,1.0,C,c,Cc[+4S@3]cI+H)](H,CC[+4S@3]cI+H)](H,17,replace c at position 1 with C,flow_matching,0.3,2.0,49,184
134,remove,4.0,4,,CC[+4S@3]cI+H)](H,CC[+S@3]cI+H)](H,16,remove 4 from position 4,flow_matching,0.3,2.0,49,184
135,add,16.0,2,,CC[+S@3]cI+H)](H,CC[+S@3]cI+H)](H2,17,add 2 at position 16,flow_matching,0.3,2.0,49,184
136,remove,16.0,2,,CC[+S@3]cI+H)](H2,CC[+S@3]cI+H)](H,16,remove 2 from position 16,flow_matching,0.3,2.0,49,184
137,remove,6.0,3,,CC[+S@3]cI+H)](H,CC[+S@]cI+H)](H,15,remove 3 from position 6,flow_matching,0.3,2.0,49,184
138,add,6.0,(,,CC[+S@]cI+H)](H,CC[+S@(]cI+H)](H,16,add ( at position 6,flow_matching,0.3,2.0,49,184
139,replace,12.0,n,),CC[+S@(]cI+H)](H,CC[+S@(]cI+Hn](H,16,replace ) at position 12 with n,flow_matching,0.3,2.0,49,184
140,replace,12.0,4,n,CC[+S@(]cI+Hn](H,CC[+S@(]cI+H4](H,16,replace n at position 12 with 4,flow_matching,0.3,2.0,49,184
141,replace,3.0,C,+,CC[+S@(]cI+H4](H,CC[CS@(]cI+H4](H,16,replace + at position 3 with C,flow_matching,0.3,2.0,49,184
142,replace,4.0,@,S,CC[CS@(]cI+H4](H,CC[C@@(]cI+H4](H,16,replace S at position 4 with @,flow_matching,0.3,2.0,49,184
143,replace,6.0,H,(,CC[C@@(]cI+H4](H,CC[C@@H]cI+H4](H,16,replace ( at position 6 with H,flow_matching,0.3,2.0,49,184
144,replace,8.0,(,c,CC[C@@H]cI+H4](H,CC[C@@H](I+H4](H,16,replace c at position 8 with (,flow_matching,0.3,2.0,49,184
145,replace,9.0,C,I,CC[C@@H](I+H4](H,CC[C@@H](C+H4](H,16,replace I at position 9 with C,flow_matching,0.3,2.0,49,184
146,replace,10.0,),+,CC[C@@H](C+H4](H,CC[C@@H](C)H4](H,16,replace + at position 10 with ),flow_matching,0.3,2.0,49,184
147,replace,11.0,C,H,CC[C@@H](C)H4](H,CC[C@@H](C)C4](H,16,replace H at position 11 with C,flow_matching,0.3,2.0,49,184
148,replace,12.0,(,4,CC[C@@H](C)C4](H,CC[C@@H](C)C(](H,16,replace 4 at position 12 with (,flow_matching,0.3,2.0,49,184
149,replace,13.0,=,],CC[C@@H](C)C(](H,CC[C@@H](C)C(=(H,16,replace ] at position 13 with =,flow_matching,0.3,2.0,49,184
150,replace,14.0,O,(,CC[C@@H](C)C(=(H,CC[C@@H](C)C(=OH,16,replace ( at position 14 with O,flow_matching,0.3,2.0,49,184
151,replace,15.0,),H,CC[C@@H](C)C(=OH,CC[C@@H](C)C(=O),16,replace H at position 15 with ),flow_matching,0.3,2.0,49,184
152,add,16.0,N,,CC[C@@H](C)C(=O),CC[C@@H](C)C(=O)N,17,add N at position 16,flow_matching,0.3,2.0,49,184
153,add,17.0,C,,CC[C@@H](C)C(=O)N,CC[C@@H](C)C(=O)NC,18,add C at position 17,flow_matching,0.3,2.0,49,184
154,add,18.0,C,,CC[C@@H](C)C(=O)NC,CC[C@@H](C)C(=O)NCC,19,add C at position 18,flow_matching,0.3,2.0,49,184
155,add,19.0,(,,CC[C@@H](C)C(=O)NCC,CC[C@@H](C)C(=O)NCC(,20,add ( at position 19,flow_matching,0.3,2.0,49,184
156,add,20.0,=,,CC[C@@H](C)C(=O)NCC(,CC[C@@H](C)C(=O)NCC(=,21,add = at position 20,flow_matching,0.3,2.0,49,184
157,add,21.0,O,,CC[C@@H](C)C(=O)NCC(=,CC[C@@H](C)C(=O)NCC(=O,22,add O at position 21,flow_matching,0.3,2.0,49,184
158,add,22.0,),,CC[C@@H](C)C(=O)NCC(=O,CC[C@@H](C)C(=O)NCC(=O),23,add ) at position 22,flow_matching,0.3,2.0,49,184
159,add,23.0,N,,CC[C@@H](C)C(=O)NCC(=O),CC[C@@H](C)C(=O)NCC(=O)N,24,add N at position 23,flow_matching,0.3,2.0,49,184
160,add,24.0,(,,CC[C@@H](C)C(=O)NCC(=O)N,CC[C@@H](C)C(=O)NCC(=O)N(,25,add ( at position 24,flow_matching,0.3,2.0,49,184
161,add,25.0,C,,CC[C@@H](C)C(=O)NCC(=O)N(,CC[C@@H](C)C(=O)NCC(=O)N(C,26,add C at position 25,flow_matching,0.3,2.0,49,184
162,add,26.0,),,CC[C@@H](C)C(=O)NCC(=O)N(C,CC[C@@H](C)C(=O)NCC(=O)N(C),27,add ) at position 26,flow_matching,0.3,2.0,49,184
163,add,27.0,[,,CC[C@@H](C)C(=O)NCC(=O)N(C),CC[C@@H](C)C(=O)NCC(=O)N(C)[,28,add [ at position 27,flow_matching,0.3,2.0,49,184
164,add,28.0,C,,CC[C@@H](C)C(=O)NCC(=O)N(C)[,CC[C@@H](C)C(=O)NCC(=O)N(C)[C,29,add C at position 28,flow_matching,0.3,2.0,49,184
165,add,29.0,@,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@,30,add @ at position 29,flow_matching,0.3,2.0,49,184
166,add,30.0,@,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@,31,add @ at position 30,flow_matching,0.3,2.0,49,184
167,add,31.0,H,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H,32,add H at position 31,flow_matching,0.3,2.0,49,184
168,add,32.0,],,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H],33,add ] at position 32,flow_matching,0.3,2.0,49,184
169,add,33.0,(,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H],CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](,34,add ( at position 33,flow_matching,0.3,2.0,49,184
170,add,34.0,C,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C,35,add C at position 34,flow_matching,0.3,2.0,49,184
171,add,35.0,),,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C),36,add ) at position 35,flow_matching,0.3,2.0,49,184
172,add,36.0,c,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C),CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c,37,add c at position 36,flow_matching,0.3,2.0,49,184
173,add,37.0,1,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1,38,add 1 at position 37,flow_matching,0.3,2.0,49,184
174,add,38.0,c,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1c,39,add c at position 38,flow_matching,0.3,2.0,49,184
175,add,39.0,c,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1c,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc,40,add c at position 39,flow_matching,0.3,2.0,49,184
176,add,40.0,(,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(,41,add ( at position 40,flow_matching,0.3,2.0,49,184
177,add,41.0,F,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F,42,add F at position 41,flow_matching,0.3,2.0,49,184
178,add,42.0,),,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F),43,add ) at position 42,flow_matching,0.3,2.0,49,184
179,add,43.0,c,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F),CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)c,44,add c at position 43,flow_matching,0.3,2.0,49,184
180,add,44.0,c,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)c,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)cc,45,add c at position 44,flow_matching,0.3,2.0,49,184
181,add,45.0,c,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)cc,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc,46,add c at position 45,flow_matching,0.3,2.0,49,184
182,add,46.0,1,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc1,47,add 1 at position 46,flow_matching,0.3,2.0,49,184
183,add,47.0,F,,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc1,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc1F,48,add F at position 47,flow_matching,0.3,2.0,49,184
184,add,48.0,"
",,CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc1F,"CC[C@@H](C)C(=O)NCC(=O)N(C)[C@@H](C)c1cc(F)ccc1F
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,184
