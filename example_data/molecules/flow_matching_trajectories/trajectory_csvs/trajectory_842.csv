step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,171
1,add,0.0,C,,,C,1,add <PERSON> at position 0,flow_matching,0.3,2.0,43,171
2,add,1.0,-,,C,C-,2,add - at position 1,flow_matching,0.3,2.0,43,171
3,add,0.0,s,,C-,sC-,3,add s at position 0,flow_matching,0.3,2.0,43,171
4,remove,1.0,C,,sC-,s-,2,remove C from position 1,flow_matching,0.3,2.0,43,171
5,replace,0.0,C,s,s-,C-,2,replace s at position 0 with C,flow_matching,0.3,2.0,43,171
6,add,1.0,B,,C-,CB-,3,add <PERSON> at position 1,flow_matching,0.3,2.0,43,171
7,remove,1.0,B,,CB-,C-,2,remove B from position 1,flow_matching,0.3,2.0,43,171
8,replace,0.0,#,C,C-,#-,2,replace C at position 0 with #,flow_matching,0.3,2.0,43,171
9,replace,0.0,C,#,#-,C-,2,replace # at position 0 with C,flow_matching,0.3,2.0,43,171
10,remove,1.0,-,,C-,C,1,remove - from position 1,flow_matching,0.3,2.0,43,171
11,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,43,171
12,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,43,171
13,add,1.0,n,,-,-n,2,add n at position 1,flow_matching,0.3,2.0,43,171
14,replace,0.0,C,-,-n,Cn,2,replace - at position 0 with C,flow_matching,0.3,2.0,43,171
15,replace,1.0,C,n,Cn,CC,2,replace n at position 1 with C,flow_matching,0.3,2.0,43,171
16,add,2.0,2,,CC,CC2,3,add 2 at position 2,flow_matching,0.3,2.0,43,171
17,replace,2.0,3,2,CC2,CC3,3,replace 2 at position 2 with 3,flow_matching,0.3,2.0,43,171
18,replace,2.0,F,3,CC3,CCF,3,replace 3 at position 2 with F,flow_matching,0.3,2.0,43,171
19,remove,0.0,C,,CCF,CF,2,remove C from position 0,flow_matching,0.3,2.0,43,171
20,add,1.0,+,,CF,C+F,3,add + at position 1,flow_matching,0.3,2.0,43,171
21,replace,1.0,C,+,C+F,CCF,3,replace + at position 1 with C,flow_matching,0.3,2.0,43,171
22,remove,1.0,C,,CCF,CF,2,remove C from position 1,flow_matching,0.3,2.0,43,171
23,remove,0.0,C,,CF,F,1,remove C from position 0,flow_matching,0.3,2.0,43,171
24,add,1.0,S,,F,FS,2,add S at position 1,flow_matching,0.3,2.0,43,171
25,add,0.0,/,,FS,/FS,3,add / at position 0,flow_matching,0.3,2.0,43,171
26,remove,0.0,/,,/FS,FS,2,remove / from position 0,flow_matching,0.3,2.0,43,171
27,replace,0.0,C,F,FS,CS,2,replace F at position 0 with C,flow_matching,0.3,2.0,43,171
28,remove,1.0,S,,CS,C,1,remove S from position 1,flow_matching,0.3,2.0,43,171
29,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,43,171
30,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,43,171
31,add,1.0,5,,C,C5,2,add 5 at position 1,flow_matching,0.3,2.0,43,171
32,remove,0.0,C,,C5,5,1,remove C from position 0,flow_matching,0.3,2.0,43,171
33,add,0.0,@,,5,@5,2,add @ at position 0,flow_matching,0.3,2.0,43,171
34,add,2.0,N,,@5,@5N,3,add N at position 2,flow_matching,0.3,2.0,43,171
35,replace,0.0,C,@,@5N,C5N,3,replace @ at position 0 with C,flow_matching,0.3,2.0,43,171
36,replace,0.0,l,C,C5N,l5N,3,replace C at position 0 with l,flow_matching,0.3,2.0,43,171
37,replace,1.0,2,5,l5N,l2N,3,replace 5 at position 1 with 2,flow_matching,0.3,2.0,43,171
38,remove,2.0,N,,l2N,l2,2,remove N from position 2,flow_matching,0.3,2.0,43,171
39,add,1.0,6,,l2,l62,3,add 6 at position 1,flow_matching,0.3,2.0,43,171
40,replace,0.0,C,l,l62,C62,3,replace l at position 0 with C,flow_matching,0.3,2.0,43,171
41,replace,1.0,C,6,C62,CC2,3,replace 6 at position 1 with C,flow_matching,0.3,2.0,43,171
42,replace,2.0,(,2,CC2,CC(,3,replace 2 at position 2 with (,flow_matching,0.3,2.0,43,171
43,add,2.0,1,,CC(,CC1(,4,add 1 at position 2,flow_matching,0.3,2.0,43,171
44,replace,2.0,(,1,CC1(,CC((,4,replace 1 at position 2 with (,flow_matching,0.3,2.0,43,171
45,add,4.0,s,,CC((,CC((s,5,add s at position 4,flow_matching,0.3,2.0,43,171
46,add,4.0,r,,CC((s,CC((rs,6,add r at position 4,flow_matching,0.3,2.0,43,171
47,replace,3.0,C,(,CC((rs,CC(Crs,6,replace ( at position 3 with C,flow_matching,0.3,2.0,43,171
48,add,5.0,+,,CC(Crs,CC(Cr+s,7,add + at position 5,flow_matching,0.3,2.0,43,171
49,replace,4.0,),r,CC(Cr+s,CC(C)+s,7,replace r at position 4 with ),flow_matching,0.3,2.0,43,171
50,remove,5.0,+,,CC(C)+s,CC(C)s,6,remove + from position 5,flow_matching,0.3,2.0,43,171
51,remove,4.0,),,CC(C)s,CC(Cs,5,remove ) from position 4,flow_matching,0.3,2.0,43,171
52,remove,2.0,(,,CC(Cs,CCCs,4,remove ( from position 2,flow_matching,0.3,2.0,43,171
53,remove,0.0,C,,CCCs,CCs,3,remove C from position 0,flow_matching,0.3,2.0,43,171
54,replace,2.0,(,s,CCs,CC(,3,replace s at position 2 with (,flow_matching,0.3,2.0,43,171
55,remove,2.0,(,,CC(,CC,2,remove ( from position 2,flow_matching,0.3,2.0,43,171
56,add,0.0,l,,CC,lCC,3,add l at position 0,flow_matching,0.3,2.0,43,171
57,replace,0.0,(,l,lCC,(CC,3,replace l at position 0 with (,flow_matching,0.3,2.0,43,171
58,replace,0.0,[,(,(CC,[CC,3,replace ( at position 0 with [,flow_matching,0.3,2.0,43,171
59,add,0.0,=,,[CC,=[CC,4,add = at position 0,flow_matching,0.3,2.0,43,171
60,add,2.0,s,,=[CC,=[sCC,5,add s at position 2,flow_matching,0.3,2.0,43,171
61,replace,2.0,S,s,=[sCC,=[SCC,5,replace s at position 2 with S,flow_matching,0.3,2.0,43,171
62,replace,4.0,@,C,=[SCC,=[SC@,5,replace C at position 4 with @,flow_matching,0.3,2.0,43,171
63,replace,3.0,],C,=[SC@,=[S]@,5,replace C at position 3 with ],flow_matching,0.3,2.0,43,171
64,remove,3.0,],,=[S]@,=[S@,4,remove ] from position 3,flow_matching,0.3,2.0,43,171
65,replace,1.0,F,[,=[S@,=FS@,4,replace [ at position 1 with F,flow_matching,0.3,2.0,43,171
66,add,3.0,(,,=FS@,=FS(@,5,add ( at position 3,flow_matching,0.3,2.0,43,171
67,replace,0.0,C,=,=FS(@,CFS(@,5,replace = at position 0 with C,flow_matching,0.3,2.0,43,171
68,replace,1.0,C,F,CFS(@,CCS(@,5,replace F at position 1 with C,flow_matching,0.3,2.0,43,171
69,add,5.0,(,,CCS(@,CCS(@(,6,add ( at position 5,flow_matching,0.3,2.0,43,171
70,replace,0.0,4,C,CCS(@(,4CS(@(,6,replace C at position 0 with 4,flow_matching,0.3,2.0,43,171
71,replace,0.0,C,4,4CS(@(,CCS(@(,6,replace 4 at position 0 with C,flow_matching,0.3,2.0,43,171
72,replace,1.0,r,C,CCS(@(,CrS(@(,6,replace C at position 1 with r,flow_matching,0.3,2.0,43,171
73,replace,1.0,C,r,CrS(@(,CCS(@(,6,replace r at position 1 with C,flow_matching,0.3,2.0,43,171
74,replace,2.0,(,S,CCS(@(,CC((@(,6,replace S at position 2 with (,flow_matching,0.3,2.0,43,171
75,replace,3.0,3,(,CC((@(,CC(3@(,6,replace ( at position 3 with 3,flow_matching,0.3,2.0,43,171
76,replace,4.0,O,@,CC(3@(,CC(3O(,6,replace @ at position 4 with O,flow_matching,0.3,2.0,43,171
77,remove,3.0,3,,CC(3O(,CC(O(,5,remove 3 from position 3,flow_matching,0.3,2.0,43,171
78,remove,1.0,C,,CC(O(,C(O(,4,remove C from position 1,flow_matching,0.3,2.0,43,171
79,remove,0.0,C,,C(O(,(O(,3,remove C from position 0,flow_matching,0.3,2.0,43,171
80,add,2.0,/,,(O(,(O/(,4,add / at position 2,flow_matching,0.3,2.0,43,171
81,replace,0.0,C,(,(O/(,CO/(,4,replace ( at position 0 with C,flow_matching,0.3,2.0,43,171
82,replace,1.0,C,O,CO/(,CC/(,4,replace O at position 1 with C,flow_matching,0.3,2.0,43,171
83,replace,2.0,(,/,CC/(,CC((,4,replace / at position 2 with (,flow_matching,0.3,2.0,43,171
84,replace,3.0,C,(,CC((,CC(C,4,replace ( at position 3 with C,flow_matching,0.3,2.0,43,171
85,add,4.0,),,CC(C,CC(C),5,add ) at position 4,flow_matching,0.3,2.0,43,171
86,add,3.0,l,,CC(C),CC(lC),6,add l at position 3,flow_matching,0.3,2.0,43,171
87,remove,3.0,l,,CC(lC),CC(C),5,remove l from position 3,flow_matching,0.3,2.0,43,171
88,add,5.0,[,,CC(C),CC(C)[,6,add [ at position 5,flow_matching,0.3,2.0,43,171
89,replace,2.0,o,(,CC(C)[,CCoC)[,6,replace ( at position 2 with o,flow_matching,0.3,2.0,43,171
90,replace,2.0,(,o,CCoC)[,CC(C)[,6,replace o at position 2 with (,flow_matching,0.3,2.0,43,171
91,add,6.0,C,,CC(C)[,CC(C)[C,7,add C at position 6,flow_matching,0.3,2.0,43,171
92,add,1.0,F,,CC(C)[C,CFC(C)[C,8,add F at position 1,flow_matching,0.3,2.0,43,171
93,replace,1.0,C,F,CFC(C)[C,CCC(C)[C,8,replace F at position 1 with C,flow_matching,0.3,2.0,43,171
94,add,2.0,1,,CCC(C)[C,CC1C(C)[C,9,add 1 at position 2,flow_matching,0.3,2.0,43,171
95,replace,5.0,l,C,CC1C(C)[C,CC1C(l)[C,9,replace C at position 5 with l,flow_matching,0.3,2.0,43,171
96,replace,2.0,(,1,CC1C(l)[C,CC(C(l)[C,9,replace 1 at position 2 with (,flow_matching,0.3,2.0,43,171
97,add,7.0,H,,CC(C(l)[C,CC(C(l)H[C,10,add H at position 7,flow_matching,0.3,2.0,43,171
98,replace,4.0,),(,CC(C(l)H[C,CC(C)l)H[C,10,replace ( at position 4 with ),flow_matching,0.3,2.0,43,171
99,add,5.0,6,,CC(C)l)H[C,CC(C)6l)H[C,11,add 6 at position 5,flow_matching,0.3,2.0,43,171
100,replace,5.0,[,6,CC(C)6l)H[C,CC(C)[l)H[C,11,replace 6 at position 5 with [,flow_matching,0.3,2.0,43,171
101,add,8.0,1,,CC(C)[l)H[C,CC(C)[l)1H[C,12,add 1 at position 8,flow_matching,0.3,2.0,43,171
102,replace,6.0,C,l,CC(C)[l)1H[C,CC(C)[C)1H[C,12,replace l at position 6 with C,flow_matching,0.3,2.0,43,171
103,remove,0.0,C,,CC(C)[C)1H[C,C(C)[C)1H[C,11,remove C from position 0,flow_matching,0.3,2.0,43,171
104,add,0.0,#,,C(C)[C)1H[C,#C(C)[C)1H[C,12,add # at position 0,flow_matching,0.3,2.0,43,171
105,replace,4.0,-,),#C(C)[C)1H[C,#C(C-[C)1H[C,12,replace ) at position 4 with -,flow_matching,0.3,2.0,43,171
106,remove,3.0,C,,#C(C-[C)1H[C,#C(-[C)1H[C,11,remove C from position 3,flow_matching,0.3,2.0,43,171
107,remove,9.0,[,,#C(-[C)1H[C,#C(-[C)1HC,10,remove [ from position 9,flow_matching,0.3,2.0,43,171
108,remove,4.0,[,,#C(-[C)1HC,#C(-C)1HC,9,remove [ from position 4,flow_matching,0.3,2.0,43,171
109,remove,7.0,H,,#C(-C)1HC,#C(-C)1C,8,remove H from position 7,flow_matching,0.3,2.0,43,171
110,replace,1.0,7,C,#C(-C)1C,#7(-C)1C,8,replace C at position 1 with 7,flow_matching,0.3,2.0,43,171
111,replace,0.0,6,#,#7(-C)1C,67(-C)1C,8,replace # at position 0 with 6,flow_matching,0.3,2.0,43,171
112,remove,7.0,C,,67(-C)1C,67(-C)1,7,remove C from position 7,flow_matching,0.3,2.0,43,171
113,add,4.0,[,,67(-C)1,67(-[C)1,8,add [ at position 4,flow_matching,0.3,2.0,43,171
114,replace,0.0,C,6,67(-[C)1,C7(-[C)1,8,replace 6 at position 0 with C,flow_matching,0.3,2.0,43,171
115,replace,1.0,C,7,C7(-[C)1,CC(-[C)1,8,replace 7 at position 1 with C,flow_matching,0.3,2.0,43,171
116,replace,5.0,(,C,CC(-[C)1,CC(-[()1,8,replace C at position 5 with (,flow_matching,0.3,2.0,43,171
117,replace,0.0,+,C,CC(-[()1,+C(-[()1,8,replace C at position 0 with +,flow_matching,0.3,2.0,43,171
118,replace,0.0,C,+,+C(-[()1,CC(-[()1,8,replace + at position 0 with C,flow_matching,0.3,2.0,43,171
119,add,0.0,-,,CC(-[()1,-CC(-[()1,9,add - at position 0,flow_matching,0.3,2.0,43,171
120,replace,0.0,C,-,-CC(-[()1,CCC(-[()1,9,replace - at position 0 with C,flow_matching,0.3,2.0,43,171
121,replace,2.0,(,C,CCC(-[()1,CC((-[()1,9,replace C at position 2 with (,flow_matching,0.3,2.0,43,171
122,replace,7.0,(,),CC((-[()1,CC((-[((1,9,replace ) at position 7 with (,flow_matching,0.3,2.0,43,171
123,replace,6.0,n,(,CC((-[((1,CC((-[n(1,9,replace ( at position 6 with n,flow_matching,0.3,2.0,43,171
124,replace,3.0,C,(,CC((-[n(1,CC(C-[n(1,9,replace ( at position 3 with C,flow_matching,0.3,2.0,43,171
125,remove,6.0,n,,CC(C-[n(1,CC(C-[(1,8,remove n from position 6,flow_matching,0.3,2.0,43,171
126,remove,0.0,C,,CC(C-[(1,C(C-[(1,7,remove C from position 0,flow_matching,0.3,2.0,43,171
127,add,2.0,@,,C(C-[(1,C(@C-[(1,8,add @ at position 2,flow_matching,0.3,2.0,43,171
128,remove,5.0,[,,C(@C-[(1,C(@C-(1,7,remove [ from position 5,flow_matching,0.3,2.0,43,171
129,replace,1.0,I,(,C(@C-(1,CI@C-(1,7,replace ( at position 1 with I,flow_matching,0.3,2.0,43,171
130,remove,4.0,-,,CI@C-(1,CI@C(1,6,remove - from position 4,flow_matching,0.3,2.0,43,171
131,replace,1.0,C,I,CI@C(1,CC@C(1,6,replace I at position 1 with C,flow_matching,0.3,2.0,43,171
132,replace,2.0,(,@,CC@C(1,CC(C(1,6,replace @ at position 2 with (,flow_matching,0.3,2.0,43,171
133,replace,4.0,),(,CC(C(1,CC(C)1,6,replace ( at position 4 with ),flow_matching,0.3,2.0,43,171
134,replace,5.0,[,1,CC(C)1,CC(C)[,6,replace 1 at position 5 with [,flow_matching,0.3,2.0,43,171
135,add,6.0,C,,CC(C)[,CC(C)[C,7,add C at position 6,flow_matching,0.3,2.0,43,171
136,add,7.0,@,,CC(C)[C,CC(C)[C@,8,add @ at position 7,flow_matching,0.3,2.0,43,171
137,add,8.0,@,,CC(C)[C@,CC(C)[C@@,9,add @ at position 8,flow_matching,0.3,2.0,43,171
138,add,9.0,H,,CC(C)[C@@,CC(C)[C@@H,10,add H at position 9,flow_matching,0.3,2.0,43,171
139,add,10.0,],,CC(C)[C@@H,CC(C)[C@@H],11,add ] at position 10,flow_matching,0.3,2.0,43,171
140,add,11.0,(,,CC(C)[C@@H],CC(C)[C@@H](,12,add ( at position 11,flow_matching,0.3,2.0,43,171
141,add,12.0,O,,CC(C)[C@@H](,CC(C)[C@@H](O,13,add O at position 12,flow_matching,0.3,2.0,43,171
142,add,13.0,N,,CC(C)[C@@H](O,CC(C)[C@@H](ON,14,add N at position 13,flow_matching,0.3,2.0,43,171
143,add,14.0,1,,CC(C)[C@@H](ON,CC(C)[C@@H](ON1,15,add 1 at position 14,flow_matching,0.3,2.0,43,171
144,add,15.0,C,,CC(C)[C@@H](ON1,CC(C)[C@@H](ON1C,16,add C at position 15,flow_matching,0.3,2.0,43,171
145,add,16.0,(,,CC(C)[C@@H](ON1C,CC(C)[C@@H](ON1C(,17,add ( at position 16,flow_matching,0.3,2.0,43,171
146,add,17.0,=,,CC(C)[C@@H](ON1C(,CC(C)[C@@H](ON1C(=,18,add = at position 17,flow_matching,0.3,2.0,43,171
147,add,18.0,O,,CC(C)[C@@H](ON1C(=,CC(C)[C@@H](ON1C(=O,19,add O at position 18,flow_matching,0.3,2.0,43,171
148,add,19.0,),,CC(C)[C@@H](ON1C(=O,CC(C)[C@@H](ON1C(=O),20,add ) at position 19,flow_matching,0.3,2.0,43,171
149,add,20.0,c,,CC(C)[C@@H](ON1C(=O),CC(C)[C@@H](ON1C(=O)c,21,add c at position 20,flow_matching,0.3,2.0,43,171
150,add,21.0,2,,CC(C)[C@@H](ON1C(=O)c,CC(C)[C@@H](ON1C(=O)c2,22,add 2 at position 21,flow_matching,0.3,2.0,43,171
151,add,22.0,c,,CC(C)[C@@H](ON1C(=O)c2,CC(C)[C@@H](ON1C(=O)c2c,23,add c at position 22,flow_matching,0.3,2.0,43,171
152,add,23.0,c,,CC(C)[C@@H](ON1C(=O)c2c,CC(C)[C@@H](ON1C(=O)c2cc,24,add c at position 23,flow_matching,0.3,2.0,43,171
153,add,24.0,c,,CC(C)[C@@H](ON1C(=O)c2cc,CC(C)[C@@H](ON1C(=O)c2ccc,25,add c at position 24,flow_matching,0.3,2.0,43,171
154,add,25.0,c,,CC(C)[C@@H](ON1C(=O)c2ccc,CC(C)[C@@H](ON1C(=O)c2cccc,26,add c at position 25,flow_matching,0.3,2.0,43,171
155,add,26.0,c,,CC(C)[C@@H](ON1C(=O)c2cccc,CC(C)[C@@H](ON1C(=O)c2ccccc,27,add c at position 26,flow_matching,0.3,2.0,43,171
156,add,27.0,2,,CC(C)[C@@H](ON1C(=O)c2ccccc,CC(C)[C@@H](ON1C(=O)c2ccccc2,28,add 2 at position 27,flow_matching,0.3,2.0,43,171
157,add,28.0,C,,CC(C)[C@@H](ON1C(=O)c2ccccc2,CC(C)[C@@H](ON1C(=O)c2ccccc2C,29,add C at position 28,flow_matching,0.3,2.0,43,171
158,add,29.0,1,,CC(C)[C@@H](ON1C(=O)c2ccccc2C,CC(C)[C@@H](ON1C(=O)c2ccccc2C1,30,add 1 at position 29,flow_matching,0.3,2.0,43,171
159,add,30.0,=,,CC(C)[C@@H](ON1C(=O)c2ccccc2C1,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=,31,add = at position 30,flow_matching,0.3,2.0,43,171
160,add,31.0,O,,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O,32,add O at position 31,flow_matching,0.3,2.0,43,171
161,add,32.0,),,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O),33,add ) at position 32,flow_matching,0.3,2.0,43,171
162,add,33.0,C,,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O),CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C,34,add C at position 33,flow_matching,0.3,2.0,43,171
163,add,34.0,(,,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(,35,add ( at position 34,flow_matching,0.3,2.0,43,171
164,add,35.0,=,,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=,36,add = at position 35,flow_matching,0.3,2.0,43,171
165,add,36.0,O,,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O,37,add O at position 36,flow_matching,0.3,2.0,43,171
166,add,37.0,),,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O),38,add ) at position 37,flow_matching,0.3,2.0,43,171
167,add,38.0,[,,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O),CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O)[,39,add [ at position 38,flow_matching,0.3,2.0,43,171
168,add,39.0,O,,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O)[,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O)[O,40,add O at position 39,flow_matching,0.3,2.0,43,171
169,add,40.0,-,,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O)[O,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O)[O-,41,add - at position 40,flow_matching,0.3,2.0,43,171
170,add,41.0,],,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O)[O-,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O)[O-],42,add ] at position 41,flow_matching,0.3,2.0,43,171
171,add,42.0,"
",,CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O)[O-],"CC(C)[C@@H](ON1C(=O)c2ccccc2C1=O)C(=O)[O-]
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,171
