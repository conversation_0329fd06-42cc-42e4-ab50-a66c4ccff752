step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,46,198
1,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,46,198
2,remove,0.0,6,,6,,0,remove 6 from position 0,flow_matching,0.3,2.0,46,198
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,46,198
4,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,46,198
5,add,0.0,S,,,S,1,add S at position 0,flow_matching,0.3,2.0,46,198
6,remove,0.0,S,,S,,0,remove S from position 0,flow_matching,0.3,2.0,46,198
7,add,0.0,C,,,C,1,add <PERSON> at position 0,flow_matching,0.3,2.0,46,198
8,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,46,198
9,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,46,198
10,add,1.0,o,,C,Co,2,add o at position 1,flow_matching,0.3,2.0,46,198
11,remove,0.0,C,,Co,o,1,remove C from position 0,flow_matching,0.3,2.0,46,198
12,remove,0.0,o,,o,,0,remove o from position 0,flow_matching,0.3,2.0,46,198
13,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,46,198
14,add,1.0,B,,C,CB,2,add B at position 1,flow_matching,0.3,2.0,46,198
15,remove,0.0,C,,CB,B,1,remove C from position 0,flow_matching,0.3,2.0,46,198
16,replace,0.0,C,B,B,C,1,replace B at position 0 with C,flow_matching,0.3,2.0,46,198
17,replace,0.0,c,C,C,c,1,replace C at position 0 with c,flow_matching,0.3,2.0,46,198
18,replace,0.0,B,c,c,B,1,replace c at position 0 with B,flow_matching,0.3,2.0,46,198
19,replace,0.0,C,B,B,C,1,replace B at position 0 with C,flow_matching,0.3,2.0,46,198
20,replace,0.0,F,C,C,F,1,replace C at position 0 with F,flow_matching,0.3,2.0,46,198
21,add,1.0,#,,F,F#,2,add # at position 1,flow_matching,0.3,2.0,46,198
22,replace,0.0,=,F,F#,=#,2,replace F at position 0 with =,flow_matching,0.3,2.0,46,198
23,replace,0.0,n,=,=#,n#,2,replace = at position 0 with n,flow_matching,0.3,2.0,46,198
24,add,0.0,7,,n#,7n#,3,add 7 at position 0,flow_matching,0.3,2.0,46,198
25,replace,0.0,C,7,7n#,Cn#,3,replace 7 at position 0 with C,flow_matching,0.3,2.0,46,198
26,add,1.0,2,,Cn#,C2n#,4,add 2 at position 1,flow_matching,0.3,2.0,46,198
27,replace,1.0,C,2,C2n#,CCn#,4,replace 2 at position 1 with C,flow_matching,0.3,2.0,46,198
28,remove,0.0,C,,CCn#,Cn#,3,remove C from position 0,flow_matching,0.3,2.0,46,198
29,add,3.0,),,Cn#,Cn#),4,add ) at position 3,flow_matching,0.3,2.0,46,198
30,remove,1.0,n,,Cn#),C#),3,remove n from position 1,flow_matching,0.3,2.0,46,198
31,replace,1.0,C,#,C#),CC),3,replace # at position 1 with C,flow_matching,0.3,2.0,46,198
32,remove,0.0,C,,CC),C),2,remove C from position 0,flow_matching,0.3,2.0,46,198
33,replace,1.0,C,),C),CC,2,replace ) at position 1 with C,flow_matching,0.3,2.0,46,198
34,add,0.0,[,,CC,[CC,3,add [ at position 0,flow_matching,0.3,2.0,46,198
35,replace,0.0,C,[,[CC,CCC,3,replace [ at position 0 with C,flow_matching,0.3,2.0,46,198
36,remove,0.0,C,,CCC,CC,2,remove C from position 0,flow_matching,0.3,2.0,46,198
37,add,1.0,),,CC,C)C,3,add ) at position 1,flow_matching,0.3,2.0,46,198
38,add,2.0,H,,C)C,C)HC,4,add H at position 2,flow_matching,0.3,2.0,46,198
39,add,0.0,F,,C)HC,FC)HC,5,add F at position 0,flow_matching,0.3,2.0,46,198
40,add,5.0,+,,FC)HC,FC)HC+,6,add + at position 5,flow_matching,0.3,2.0,46,198
41,replace,0.0,C,F,FC)HC+,CC)HC+,6,replace F at position 0 with C,flow_matching,0.3,2.0,46,198
42,remove,3.0,H,,CC)HC+,CC)C+,5,remove H from position 3,flow_matching,0.3,2.0,46,198
43,add,3.0,6,,CC)C+,CC)6C+,6,add 6 at position 3,flow_matching,0.3,2.0,46,198
44,replace,2.0,C,),CC)6C+,CCC6C+,6,replace ) at position 2 with C,flow_matching,0.3,2.0,46,198
45,add,3.0,),,CCC6C+,CCC)6C+,7,add ) at position 3,flow_matching,0.3,2.0,46,198
46,replace,6.0,O,+,CCC)6C+,CCC)6CO,7,replace + at position 6 with O,flow_matching,0.3,2.0,46,198
47,add,5.0,1,,CCC)6CO,CCC)61CO,8,add 1 at position 5,flow_matching,0.3,2.0,46,198
48,replace,3.0,[,),CCC)61CO,CCC[61CO,8,replace ) at position 3 with [,flow_matching,0.3,2.0,46,198
49,remove,3.0,[,,CCC[61CO,CCC61CO,7,remove [ from position 3,flow_matching,0.3,2.0,46,198
50,add,7.0,O,,CCC61CO,CCC61COO,8,add O at position 7,flow_matching,0.3,2.0,46,198
51,replace,3.0,[,6,CCC61COO,CCC[1COO,8,replace 6 at position 3 with [,flow_matching,0.3,2.0,46,198
52,add,2.0,+,,CCC[1COO,CC+C[1COO,9,add + at position 2,flow_matching,0.3,2.0,46,198
53,remove,0.0,C,,CC+C[1COO,C+C[1COO,8,remove C from position 0,flow_matching,0.3,2.0,46,198
54,replace,1.0,C,+,C+C[1COO,CCC[1COO,8,replace + at position 1 with C,flow_matching,0.3,2.0,46,198
55,replace,2.0,1,C,CCC[1COO,CC1[1COO,8,replace C at position 2 with 1,flow_matching,0.3,2.0,46,198
56,replace,1.0,5,C,CC1[1COO,C51[1COO,8,replace C at position 1 with 5,flow_matching,0.3,2.0,46,198
57,replace,1.0,C,5,C51[1COO,CC1[1COO,8,replace 5 at position 1 with C,flow_matching,0.3,2.0,46,198
58,add,4.0,/,,CC1[1COO,CC1[/1COO,9,add / at position 4,flow_matching,0.3,2.0,46,198
59,replace,2.0,C,1,CC1[/1COO,CCC[/1COO,9,replace 1 at position 2 with C,flow_matching,0.3,2.0,46,198
60,replace,4.0,C,/,CCC[/1COO,CCC[C1COO,9,replace / at position 4 with C,flow_matching,0.3,2.0,46,198
61,add,7.0,@,,CCC[C1COO,CCC[C1C@OO,10,add @ at position 7,flow_matching,0.3,2.0,46,198
62,replace,5.0,@,1,CCC[C1C@OO,CCC[C@C@OO,10,replace 1 at position 5 with @,flow_matching,0.3,2.0,46,198
63,replace,6.0,@,C,CCC[C@C@OO,CCC[C@@@OO,10,replace C at position 6 with @,flow_matching,0.3,2.0,46,198
64,add,10.0,5,,CCC[C@@@OO,CCC[C@@@OO5,11,add 5 at position 10,flow_matching,0.3,2.0,46,198
65,add,8.0,o,,CCC[C@@@OO5,CCC[C@@@oOO5,12,add o at position 8,flow_matching,0.3,2.0,46,198
66,replace,7.0,H,@,CCC[C@@@oOO5,CCC[C@@HoOO5,12,replace @ at position 7 with H,flow_matching,0.3,2.0,46,198
67,add,5.0,H,,CCC[C@@HoOO5,CCC[CH@@HoOO5,13,add H at position 5,flow_matching,0.3,2.0,46,198
68,replace,5.0,@,H,CCC[CH@@HoOO5,CCC[C@@@HoOO5,13,replace H at position 5 with @,flow_matching,0.3,2.0,46,198
69,remove,0.0,C,,CCC[C@@@HoOO5,CC[C@@@HoOO5,12,remove C from position 0,flow_matching,0.3,2.0,46,198
70,replace,9.0,5,O,CC[C@@@HoOO5,CC[C@@@Ho5O5,12,replace O at position 9 with 5,flow_matching,0.3,2.0,46,198
71,add,0.0,4,,CC[C@@@Ho5O5,4CC[C@@@Ho5O5,13,add 4 at position 0,flow_matching,0.3,2.0,46,198
72,add,7.0,(,,4CC[C@@@Ho5O5,4CC[C@@(@Ho5O5,14,add ( at position 7,flow_matching,0.3,2.0,46,198
73,add,0.0,),,4CC[C@@(@Ho5O5,)4CC[C@@(@Ho5O5,15,add ) at position 0,flow_matching,0.3,2.0,46,198
74,replace,0.0,C,),)4CC[C@@(@Ho5O5,C4CC[C@@(@Ho5O5,15,replace ) at position 0 with C,flow_matching,0.3,2.0,46,198
75,replace,1.0,C,4,C4CC[C@@(@Ho5O5,CCCC[C@@(@Ho5O5,15,replace 4 at position 1 with C,flow_matching,0.3,2.0,46,198
76,replace,4.0,H,[,CCCC[C@@(@Ho5O5,CCCCHC@@(@Ho5O5,15,replace [ at position 4 with H,flow_matching,0.3,2.0,46,198
77,add,0.0,l,,CCCCHC@@(@Ho5O5,lCCCCHC@@(@Ho5O5,16,add l at position 0,flow_matching,0.3,2.0,46,198
78,replace,0.0,C,l,lCCCCHC@@(@Ho5O5,CCCCCHC@@(@Ho5O5,16,replace l at position 0 with C,flow_matching,0.3,2.0,46,198
79,add,6.0,/,,CCCCCHC@@(@Ho5O5,CCCCCH/C@@(@Ho5O5,17,add / at position 6,flow_matching,0.3,2.0,46,198
80,add,0.0,],,CCCCCH/C@@(@Ho5O5,]CCCCCH/C@@(@Ho5O5,18,add ] at position 0,flow_matching,0.3,2.0,46,198
81,add,2.0,l,,]CCCCCH/C@@(@Ho5O5,]ClCCCCH/C@@(@Ho5O5,19,add l at position 2,flow_matching,0.3,2.0,46,198
82,add,18.0,2,,]ClCCCCH/C@@(@Ho5O5,]ClCCCCH/C@@(@Ho5O25,20,add 2 at position 18,flow_matching,0.3,2.0,46,198
83,remove,6.0,C,,]ClCCCCH/C@@(@Ho5O25,]ClCCCH/C@@(@Ho5O25,19,remove C from position 6,flow_matching,0.3,2.0,46,198
84,add,5.0,c,,]ClCCCH/C@@(@Ho5O25,]ClCCcCH/C@@(@Ho5O25,20,add c at position 5,flow_matching,0.3,2.0,46,198
85,add,20.0,H,,]ClCCcCH/C@@(@Ho5O25,]ClCCcCH/C@@(@Ho5O25H,21,add H at position 20,flow_matching,0.3,2.0,46,198
86,remove,5.0,c,,]ClCCcCH/C@@(@Ho5O25H,]ClCCCH/C@@(@Ho5O25H,20,remove c from position 5,flow_matching,0.3,2.0,46,198
87,add,2.0,3,,]ClCCCH/C@@(@Ho5O25H,]C3lCCCH/C@@(@Ho5O25H,21,add 3 at position 2,flow_matching,0.3,2.0,46,198
88,replace,3.0,@,l,]C3lCCCH/C@@(@Ho5O25H,]C3@CCCH/C@@(@Ho5O25H,21,replace l at position 3 with @,flow_matching,0.3,2.0,46,198
89,replace,4.0,+,C,]C3@CCCH/C@@(@Ho5O25H,]C3@+CCH/C@@(@Ho5O25H,21,replace C at position 4 with +,flow_matching,0.3,2.0,46,198
90,add,16.0,I,,]C3@+CCH/C@@(@Ho5O25H,]C3@+CCH/C@@(@HoI5O25H,22,add I at position 16,flow_matching,0.3,2.0,46,198
91,add,3.0,+,,]C3@+CCH/C@@(@HoI5O25H,]C3+@+CCH/C@@(@HoI5O25H,23,add + at position 3,flow_matching,0.3,2.0,46,198
92,add,13.0,3,,]C3+@+CCH/C@@(@HoI5O25H,]C3+@+CCH/C@@3(@HoI5O25H,24,add 3 at position 13,flow_matching,0.3,2.0,46,198
93,remove,10.0,C,,]C3+@+CCH/C@@3(@HoI5O25H,]C3+@+CCH/@@3(@HoI5O25H,23,remove C from position 10,flow_matching,0.3,2.0,46,198
94,add,3.0,5,,]C3+@+CCH/@@3(@HoI5O25H,]C35+@+CCH/@@3(@HoI5O25H,24,add 5 at position 3,flow_matching,0.3,2.0,46,198
95,remove,23.0,H,,]C35+@+CCH/@@3(@HoI5O25H,]C35+@+CCH/@@3(@HoI5O25,23,remove H from position 23,flow_matching,0.3,2.0,46,198
96,remove,22.0,5,,]C35+@+CCH/@@3(@HoI5O25,]C35+@+CCH/@@3(@HoI5O2,22,remove 5 from position 22,flow_matching,0.3,2.0,46,198
97,replace,0.0,C,],]C35+@+CCH/@@3(@HoI5O2,CC35+@+CCH/@@3(@HoI5O2,22,replace ] at position 0 with C,flow_matching,0.3,2.0,46,198
98,remove,11.0,@,,CC35+@+CCH/@@3(@HoI5O2,CC35+@+CCH/@3(@HoI5O2,21,remove @ from position 11,flow_matching,0.3,2.0,46,198
99,replace,2.0,C,3,CC35+@+CCH/@3(@HoI5O2,CCC5+@+CCH/@3(@HoI5O2,21,replace 3 at position 2 with C,flow_matching,0.3,2.0,46,198
100,replace,3.0,o,5,CCC5+@+CCH/@3(@HoI5O2,CCCo+@+CCH/@3(@HoI5O2,21,replace 5 at position 3 with o,flow_matching,0.3,2.0,46,198
101,replace,3.0,[,o,CCCo+@+CCH/@3(@HoI5O2,CCC[+@+CCH/@3(@HoI5O2,21,replace o at position 3 with [,flow_matching,0.3,2.0,46,198
102,add,7.0,n,,CCC[+@+CCH/@3(@HoI5O2,CCC[+@+nCCH/@3(@HoI5O2,22,add n at position 7,flow_matching,0.3,2.0,46,198
103,replace,2.0,s,C,CCC[+@+nCCH/@3(@HoI5O2,CCs[+@+nCCH/@3(@HoI5O2,22,replace C at position 2 with s,flow_matching,0.3,2.0,46,198
104,remove,5.0,@,,CCs[+@+nCCH/@3(@HoI5O2,CCs[++nCCH/@3(@HoI5O2,21,remove @ from position 5,flow_matching,0.3,2.0,46,198
105,replace,2.0,C,s,CCs[++nCCH/@3(@HoI5O2,CCC[++nCCH/@3(@HoI5O2,21,replace s at position 2 with C,flow_matching,0.3,2.0,46,198
106,add,6.0,F,,CCC[++nCCH/@3(@HoI5O2,CCC[++FnCCH/@3(@HoI5O2,22,add F at position 6,flow_matching,0.3,2.0,46,198
107,add,4.0,O,,CCC[++FnCCH/@3(@HoI5O2,CCC[O++FnCCH/@3(@HoI5O2,23,add O at position 4,flow_matching,0.3,2.0,46,198
108,replace,4.0,C,O,CCC[O++FnCCH/@3(@HoI5O2,CCC[C++FnCCH/@3(@HoI5O2,23,replace O at position 4 with C,flow_matching,0.3,2.0,46,198
109,replace,14.0,(,3,CCC[C++FnCCH/@3(@HoI5O2,CCC[C++FnCCH/@((@HoI5O2,23,replace 3 at position 14 with (,flow_matching,0.3,2.0,46,198
110,replace,5.0,@,+,CCC[C++FnCCH/@((@HoI5O2,CCC[C@+FnCCH/@((@HoI5O2,23,replace + at position 5 with @,flow_matching,0.3,2.0,46,198
111,replace,17.0,-,H,CCC[C@+FnCCH/@((@HoI5O2,CCC[C@+FnCCH/@((@-oI5O2,23,replace H at position 17 with -,flow_matching,0.3,2.0,46,198
112,add,0.0,s,,CCC[C@+FnCCH/@((@-oI5O2,sCCC[C@+FnCCH/@((@-oI5O2,24,add s at position 0,flow_matching,0.3,2.0,46,198
113,remove,21.0,5,,sCCC[C@+FnCCH/@((@-oI5O2,sCCC[C@+FnCCH/@((@-oIO2,23,remove 5 from position 21,flow_matching,0.3,2.0,46,198
114,add,7.0,l,,sCCC[C@+FnCCH/@((@-oIO2,sCCC[C@l+FnCCH/@((@-oIO2,24,add l at position 7,flow_matching,0.3,2.0,46,198
115,replace,0.0,C,s,sCCC[C@l+FnCCH/@((@-oIO2,CCCC[C@l+FnCCH/@((@-oIO2,24,replace s at position 0 with C,flow_matching,0.3,2.0,46,198
116,add,23.0,o,,CCCC[C@l+FnCCH/@((@-oIO2,CCCC[C@l+FnCCH/@((@-oIOo2,25,add o at position 23,flow_matching,0.3,2.0,46,198
117,add,0.0,O,,CCCC[C@l+FnCCH/@((@-oIOo2,OCCCC[C@l+FnCCH/@((@-oIOo2,26,add O at position 0,flow_matching,0.3,2.0,46,198
118,replace,0.0,C,O,OCCCC[C@l+FnCCH/@((@-oIOo2,CCCCC[C@l+FnCCH/@((@-oIOo2,26,replace O at position 0 with C,flow_matching,0.3,2.0,46,198
119,add,5.0,=,,CCCCC[C@l+FnCCH/@((@-oIOo2,CCCCC=[C@l+FnCCH/@((@-oIOo2,27,add = at position 5,flow_matching,0.3,2.0,46,198
120,remove,4.0,C,,CCCCC=[C@l+FnCCH/@((@-oIOo2,CCCC=[C@l+FnCCH/@((@-oIOo2,26,remove C from position 4,flow_matching,0.3,2.0,46,198
121,add,2.0,1,,CCCC=[C@l+FnCCH/@((@-oIOo2,CC1CC=[C@l+FnCCH/@((@-oIOo2,27,add 1 at position 2,flow_matching,0.3,2.0,46,198
122,replace,2.0,C,1,CC1CC=[C@l+FnCCH/@((@-oIOo2,CCCCC=[C@l+FnCCH/@((@-oIOo2,27,replace 1 at position 2 with C,flow_matching,0.3,2.0,46,198
123,replace,3.0,[,C,CCCCC=[C@l+FnCCH/@((@-oIOo2,CCC[C=[C@l+FnCCH/@((@-oIOo2,27,replace C at position 3 with [,flow_matching,0.3,2.0,46,198
124,replace,24.0,),O,CCC[C=[C@l+FnCCH/@((@-oIOo2,CCC[C=[C@l+FnCCH/@((@-oI)o2,27,replace O at position 24 with ),flow_matching,0.3,2.0,46,198
125,add,16.0,+,,CCC[C=[C@l+FnCCH/@((@-oI)o2,CCC[C=[C@l+FnCCH+/@((@-oI)o2,28,add + at position 16,flow_matching,0.3,2.0,46,198
126,add,1.0,I,,CCC[C=[C@l+FnCCH+/@((@-oI)o2,CICC[C=[C@l+FnCCH+/@((@-oI)o2,29,add I at position 1,flow_matching,0.3,2.0,46,198
127,replace,1.0,C,I,CICC[C=[C@l+FnCCH+/@((@-oI)o2,CCCC[C=[C@l+FnCCH+/@((@-oI)o2,29,replace I at position 1 with C,flow_matching,0.3,2.0,46,198
128,replace,3.0,[,C,CCCC[C=[C@l+FnCCH+/@((@-oI)o2,CCC[[C=[C@l+FnCCH+/@((@-oI)o2,29,replace C at position 3 with [,flow_matching,0.3,2.0,46,198
129,replace,4.0,C,[,CCC[[C=[C@l+FnCCH+/@((@-oI)o2,CCC[CC=[C@l+FnCCH+/@((@-oI)o2,29,replace [ at position 4 with C,flow_matching,0.3,2.0,46,198
130,add,0.0,+,,CCC[CC=[C@l+FnCCH+/@((@-oI)o2,+CCC[CC=[C@l+FnCCH+/@((@-oI)o2,30,add + at position 0,flow_matching,0.3,2.0,46,198
131,replace,29.0,o,2,+CCC[CC=[C@l+FnCCH+/@((@-oI)o2,+CCC[CC=[C@l+FnCCH+/@((@-oI)oo,30,replace 2 at position 29 with o,flow_matching,0.3,2.0,46,198
132,replace,0.0,C,+,+CCC[CC=[C@l+FnCCH+/@((@-oI)oo,CCCC[CC=[C@l+FnCCH+/@((@-oI)oo,30,replace + at position 0 with C,flow_matching,0.3,2.0,46,198
133,remove,11.0,l,,CCCC[CC=[C@l+FnCCH+/@((@-oI)oo,CCCC[CC=[C@+FnCCH+/@((@-oI)oo,29,remove l from position 11,flow_matching,0.3,2.0,46,198
134,replace,3.0,[,C,CCCC[CC=[C@+FnCCH+/@((@-oI)oo,CCC[[CC=[C@+FnCCH+/@((@-oI)oo,29,replace C at position 3 with [,flow_matching,0.3,2.0,46,198
135,replace,4.0,C,[,CCC[[CC=[C@+FnCCH+/@((@-oI)oo,CCC[CCC=[C@+FnCCH+/@((@-oI)oo,29,replace [ at position 4 with C,flow_matching,0.3,2.0,46,198
136,add,8.0,S,,CCC[CCC=[C@+FnCCH+/@((@-oI)oo,CCC[CCC=S[C@+FnCCH+/@((@-oI)oo,30,add S at position 8,flow_matching,0.3,2.0,46,198
137,replace,5.0,@,C,CCC[CCC=S[C@+FnCCH+/@((@-oI)oo,CCC[C@C=S[C@+FnCCH+/@((@-oI)oo,30,replace C at position 5 with @,flow_matching,0.3,2.0,46,198
138,add,13.0,S,,CCC[C@C=S[C@+FnCCH+/@((@-oI)oo,CCC[C@C=S[C@+SFnCCH+/@((@-oI)oo,31,add S at position 13,flow_matching,0.3,2.0,46,198
139,remove,28.0,),,CCC[C@C=S[C@+SFnCCH+/@((@-oI)oo,CCC[C@C=S[C@+SFnCCH+/@((@-oIoo,30,remove ) from position 28,flow_matching,0.3,2.0,46,198
140,add,27.0,5,,CCC[C@C=S[C@+SFnCCH+/@((@-oIoo,CCC[C@C=S[C@+SFnCCH+/@((@-o5Ioo,31,add 5 at position 27,flow_matching,0.3,2.0,46,198
141,add,7.0,n,,CCC[C@C=S[C@+SFnCCH+/@((@-o5Ioo,CCC[C@Cn=S[C@+SFnCCH+/@((@-o5Ioo,32,add n at position 7,flow_matching,0.3,2.0,46,198
142,add,4.0,(,,CCC[C@Cn=S[C@+SFnCCH+/@((@-o5Ioo,CCC[(C@Cn=S[C@+SFnCCH+/@((@-o5Ioo,33,add ( at position 4,flow_matching,0.3,2.0,46,198
143,add,4.0,l,,CCC[(C@Cn=S[C@+SFnCCH+/@((@-o5Ioo,CCC[l(C@Cn=S[C@+SFnCCH+/@((@-o5Ioo,34,add l at position 4,flow_matching,0.3,2.0,46,198
144,replace,20.0,H,C,CCC[l(C@Cn=S[C@+SFnCCH+/@((@-o5Ioo,CCC[l(C@Cn=S[C@+SFnCHH+/@((@-o5Ioo,34,replace C at position 20 with H,flow_matching,0.3,2.0,46,198
145,add,20.0,s,,CCC[l(C@Cn=S[C@+SFnCHH+/@((@-o5Ioo,CCC[l(C@Cn=S[C@+SFnCsHH+/@((@-o5Ioo,35,add s at position 20,flow_matching,0.3,2.0,46,198
146,replace,4.0,C,l,CCC[l(C@Cn=S[C@+SFnCsHH+/@((@-o5Ioo,CCC[C(C@Cn=S[C@+SFnCsHH+/@((@-o5Ioo,35,replace l at position 4 with C,flow_matching,0.3,2.0,46,198
147,replace,5.0,@,(,CCC[C(C@Cn=S[C@+SFnCsHH+/@((@-o5Ioo,CCC[C@C@Cn=S[C@+SFnCsHH+/@((@-o5Ioo,35,replace ( at position 5 with @,flow_matching,0.3,2.0,46,198
148,remove,34.0,o,,CCC[C@C@Cn=S[C@+SFnCsHH+/@((@-o5Ioo,CCC[C@C@Cn=S[C@+SFnCsHH+/@((@-o5Io,34,remove o from position 34,flow_matching,0.3,2.0,46,198
149,replace,6.0,@,C,CCC[C@C@Cn=S[C@+SFnCsHH+/@((@-o5Io,CCC[C@@@Cn=S[C@+SFnCsHH+/@((@-o5Io,34,replace C at position 6 with @,flow_matching,0.3,2.0,46,198
150,add,27.0,3,,CCC[C@@@Cn=S[C@+SFnCsHH+/@((@-o5Io,CCC[C@@@Cn=S[C@+SFnCsHH+/@(3(@-o5Io,35,add 3 at position 27,flow_matching,0.3,2.0,46,198
151,add,8.0,),,CCC[C@@@Cn=S[C@+SFnCsHH+/@(3(@-o5Io,CCC[C@@@)Cn=S[C@+SFnCsHH+/@(3(@-o5Io,36,add ) at position 8,flow_matching,0.3,2.0,46,198
152,add,22.0,-,,CCC[C@@@)Cn=S[C@+SFnCsHH+/@(3(@-o5Io,CCC[C@@@)Cn=S[C@+SFnCs-HH+/@(3(@-o5Io,37,add - at position 22,flow_matching,0.3,2.0,46,198
153,replace,7.0,H,@,CCC[C@@@)Cn=S[C@+SFnCs-HH+/@(3(@-o5Io,CCC[C@@H)Cn=S[C@+SFnCs-HH+/@(3(@-o5Io,37,replace @ at position 7 with H,flow_matching,0.3,2.0,46,198
154,replace,8.0,],),CCC[C@@H)Cn=S[C@+SFnCs-HH+/@(3(@-o5Io,CCC[C@@H]Cn=S[C@+SFnCs-HH+/@(3(@-o5Io,37,replace ) at position 8 with ],flow_matching,0.3,2.0,46,198
155,add,17.0,l,,CCC[C@@H]Cn=S[C@+SFnCs-HH+/@(3(@-o5Io,CCC[C@@H]Cn=S[C@+lSFnCs-HH+/@(3(@-o5Io,38,add l at position 17,flow_matching,0.3,2.0,46,198
156,remove,31.0,(,,CCC[C@@H]Cn=S[C@+lSFnCs-HH+/@(3(@-o5Io,CCC[C@@H]Cn=S[C@+lSFnCs-HH+/@(3@-o5Io,37,remove ( from position 31,flow_matching,0.3,2.0,46,198
157,add,26.0,[,,CCC[C@@H]Cn=S[C@+lSFnCs-HH+/@(3@-o5Io,CCC[C@@H]Cn=S[C@+lSFnCs-HH[+/@(3@-o5Io,38,add [ at position 26,flow_matching,0.3,2.0,46,198
158,add,21.0,c,,CCC[C@@H]Cn=S[C@+lSFnCs-HH[+/@(3@-o5Io,CCC[C@@H]Cn=S[C@+lSFncCs-HH[+/@(3@-o5Io,39,add c at position 21,flow_matching,0.3,2.0,46,198
159,replace,30.0,l,@,CCC[C@@H]Cn=S[C@+lSFncCs-HH[+/@(3@-o5Io,CCC[C@@H]Cn=S[C@+lSFncCs-HH[+/l(3@-o5Io,39,replace @ at position 30 with l,flow_matching,0.3,2.0,46,198
160,add,8.0,/,,CCC[C@@H]Cn=S[C@+lSFncCs-HH[+/l(3@-o5Io,CCC[C@@H/]Cn=S[C@+lSFncCs-HH[+/l(3@-o5Io,40,add / at position 8,flow_matching,0.3,2.0,46,198
161,remove,24.0,s,,CCC[C@@H/]Cn=S[C@+lSFncCs-HH[+/l(3@-o5Io,CCC[C@@H/]Cn=S[C@+lSFncC-HH[+/l(3@-o5Io,39,remove s from position 24,flow_matching,0.3,2.0,46,198
162,replace,7.0,N,H,CCC[C@@H/]Cn=S[C@+lSFncC-HH[+/l(3@-o5Io,CCC[C@@N/]Cn=S[C@+lSFncC-HH[+/l(3@-o5Io,39,replace H at position 7 with N,flow_matching,0.3,2.0,46,198
163,replace,7.0,H,N,CCC[C@@N/]Cn=S[C@+lSFncC-HH[+/l(3@-o5Io,CCC[C@@H/]Cn=S[C@+lSFncC-HH[+/l(3@-o5Io,39,replace N at position 7 with H,flow_matching,0.3,2.0,46,198
164,replace,8.0,],/,CCC[C@@H/]Cn=S[C@+lSFncC-HH[+/l(3@-o5Io,CCC[C@@H]]Cn=S[C@+lSFncC-HH[+/l(3@-o5Io,39,replace / at position 8 with ],flow_matching,0.3,2.0,46,198
165,replace,9.0,1,],CCC[C@@H]]Cn=S[C@+lSFncC-HH[+/l(3@-o5Io,CCC[C@@H]1Cn=S[C@+lSFncC-HH[+/l(3@-o5Io,39,replace ] at position 9 with 1,flow_matching,0.3,2.0,46,198
166,replace,11.0,[,n,CCC[C@@H]1Cn=S[C@+lSFncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[=S[C@+lSFncC-HH[+/l(3@-o5Io,39,replace n at position 11 with [,flow_matching,0.3,2.0,46,198
167,replace,12.0,C,=,CCC[C@@H]1C[=S[C@+lSFncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[CS[C@+lSFncC-HH[+/l(3@-o5Io,39,replace = at position 12 with C,flow_matching,0.3,2.0,46,198
168,replace,13.0,@,S,CCC[C@@H]1C[CS[C@+lSFncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@[C@+lSFncC-HH[+/l(3@-o5Io,39,replace S at position 13 with @,flow_matching,0.3,2.0,46,198
169,replace,14.0,H,[,CCC[C@@H]1C[C@[C@+lSFncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@HC@+lSFncC-HH[+/l(3@-o5Io,39,replace [ at position 14 with H,flow_matching,0.3,2.0,46,198
170,replace,15.0,],C,CCC[C@@H]1C[C@HC@+lSFncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@H]@+lSFncC-HH[+/l(3@-o5Io,39,replace C at position 15 with ],flow_matching,0.3,2.0,46,198
171,replace,16.0,1,@,CCC[C@@H]1C[C@H]@+lSFncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1+lSFncC-HH[+/l(3@-o5Io,39,replace @ at position 16 with 1,flow_matching,0.3,2.0,46,198
172,replace,17.0,N,+,CCC[C@@H]1C[C@H]1+lSFncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NlSFncC-HH[+/l(3@-o5Io,39,replace + at position 17 with N,flow_matching,0.3,2.0,46,198
173,replace,18.0,C,l,CCC[C@@H]1C[C@H]1NlSFncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NCSFncC-HH[+/l(3@-o5Io,39,replace l at position 18 with C,flow_matching,0.3,2.0,46,198
174,replace,19.0,(,S,CCC[C@@H]1C[C@H]1NCSFncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NC(FncC-HH[+/l(3@-o5Io,39,replace S at position 19 with (,flow_matching,0.3,2.0,46,198
175,replace,20.0,=,F,CCC[C@@H]1C[C@H]1NC(FncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=ncC-HH[+/l(3@-o5Io,39,replace F at position 20 with =,flow_matching,0.3,2.0,46,198
176,replace,21.0,O,n,CCC[C@@H]1C[C@H]1NC(=ncC-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=OcC-HH[+/l(3@-o5Io,39,replace n at position 21 with O,flow_matching,0.3,2.0,46,198
177,replace,22.0,),c,CCC[C@@H]1C[C@H]1NC(=OcC-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C-HH[+/l(3@-o5Io,39,replace c at position 22 with ),flow_matching,0.3,2.0,46,198
178,replace,24.0,1,-,CCC[C@@H]1C[C@H]1NC(=O)C-HH[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C1HH[+/l(3@-o5Io,39,replace - at position 24 with 1,flow_matching,0.3,2.0,46,198
179,replace,25.0,(,H,CCC[C@@H]1C[C@H]1NC(=O)C1HH[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(H[+/l(3@-o5Io,39,replace H at position 25 with (,flow_matching,0.3,2.0,46,198
180,replace,26.0,c,H,CCC[C@@H]1C[C@H]1NC(=O)C1(H[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c[+/l(3@-o5Io,39,replace H at position 26 with c,flow_matching,0.3,2.0,46,198
181,replace,27.0,2,[,CCC[C@@H]1C[C@H]1NC(=O)C1(c[+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c2+/l(3@-o5Io,39,replace [ at position 27 with 2,flow_matching,0.3,2.0,46,198
182,replace,28.0,c,+,CCC[C@@H]1C[C@H]1NC(=O)C1(c2+/l(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c2c/l(3@-o5Io,39,replace + at position 28 with c,flow_matching,0.3,2.0,46,198
183,replace,29.0,c,/,CCC[C@@H]1C[C@H]1NC(=O)C1(c2c/l(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccl(3@-o5Io,39,replace / at position 29 with c,flow_matching,0.3,2.0,46,198
184,replace,30.0,c,l,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccl(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(3@-o5Io,39,replace l at position 30 with c,flow_matching,0.3,2.0,46,198
185,replace,32.0,F,3,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(3@-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F@-o5Io,39,replace 3 at position 32 with F,flow_matching,0.3,2.0,46,198
186,replace,33.0,),@,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F@-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)-o5Io,39,replace @ at position 33 with ),flow_matching,0.3,2.0,46,198
187,replace,34.0,c,-,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)-o5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)co5Io,39,replace - at position 34 with c,flow_matching,0.3,2.0,46,198
188,replace,35.0,c,o,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)co5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc5Io,39,replace o at position 35 with c,flow_matching,0.3,2.0,46,198
189,replace,36.0,2,5,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc5Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2Io,39,replace 5 at position 36 with 2,flow_matching,0.3,2.0,46,198
190,replace,37.0,F,I,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2Io,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2Fo,39,replace I at position 37 with F,flow_matching,0.3,2.0,46,198
191,replace,38.0,),o,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2Fo,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F),39,replace o at position 38 with ),flow_matching,0.3,2.0,46,198
192,add,39.0,C,,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F),CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)C,40,add C at position 39,flow_matching,0.3,2.0,46,198
193,add,40.0,C,,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)C,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CC,41,add C at position 40,flow_matching,0.3,2.0,46,198
194,add,41.0,O,,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CC,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CCO,42,add O at position 41,flow_matching,0.3,2.0,46,198
195,add,42.0,C,,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CCO,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CCOC,43,add C at position 42,flow_matching,0.3,2.0,46,198
196,add,43.0,C,,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CCOC,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CCOCC,44,add C at position 43,flow_matching,0.3,2.0,46,198
197,add,44.0,1,,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CCOCC,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CCOCC1,45,add 1 at position 44,flow_matching,0.3,2.0,46,198
198,add,45.0,"
",,CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CCOCC1,"CCC[C@@H]1C[C@H]1NC(=O)C1(c2ccc(F)cc2F)CCOCC1
",46,"add 
 at position 45",flow_matching,0.3,2.0,46,198
