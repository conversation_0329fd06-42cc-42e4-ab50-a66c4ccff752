step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,42,196
1,add,0.0,=,,,=,1,add = at position 0,flow_matching,0.3,2.0,42,196
2,replace,0.0,C,=,=,C,1,replace = at position 0 with C,flow_matching,0.3,2.0,42,196
3,replace,0.0,3,C,C,3,1,replace <PERSON> at position 0 with 3,flow_matching,0.3,2.0,42,196
4,add,1.0,o,,3,3o,2,add o at position 1,flow_matching,0.3,2.0,42,196
5,add,2.0,1,,3o,3o1,3,add 1 at position 2,flow_matching,0.3,2.0,42,196
6,replace,0.0,#,3,3o1,#o1,3,replace 3 at position 0 with #,flow_matching,0.3,2.0,42,196
7,add,2.0,l,,#o1,#ol1,4,add l at position 2,flow_matching,0.3,2.0,42,196
8,add,1.0,H,,#ol1,#Hol1,5,add H at position 1,flow_matching,0.3,2.0,42,196
9,remove,3.0,l,,#Hol1,#Ho1,4,remove l from position 3,flow_matching,0.3,2.0,42,196
10,replace,0.0,C,#,#Ho1,CHo1,4,replace # at position 0 with C,flow_matching,0.3,2.0,42,196
11,replace,1.0,2,H,CHo1,C2o1,4,replace H at position 1 with 2,flow_matching,0.3,2.0,42,196
12,add,0.0,3,,C2o1,3C2o1,5,add 3 at position 0,flow_matching,0.3,2.0,42,196
13,remove,4.0,1,,3C2o1,3C2o,4,remove 1 from position 4,flow_matching,0.3,2.0,42,196
14,add,3.0,\,,3C2o,3C2\o,5,add \ at position 3,flow_matching,0.3,2.0,42,196
15,replace,3.0,(,\,3C2\o,3C2(o,5,replace \ at position 3 with (,flow_matching,0.3,2.0,42,196
16,replace,0.0,C,3,3C2(o,CC2(o,5,replace 3 at position 0 with C,flow_matching,0.3,2.0,42,196
17,replace,4.0,n,o,CC2(o,CC2(n,5,replace o at position 4 with n,flow_matching,0.3,2.0,42,196
18,replace,1.0,O,C,CC2(n,CO2(n,5,replace C at position 1 with O,flow_matching,0.3,2.0,42,196
19,replace,2.0,c,2,CO2(n,COc(n,5,replace 2 at position 2 with c,flow_matching,0.3,2.0,42,196
20,replace,3.0,1,(,COc(n,COc1n,5,replace ( at position 3 with 1,flow_matching,0.3,2.0,42,196
21,remove,0.0,C,,COc1n,Oc1n,4,remove C from position 0,flow_matching,0.3,2.0,42,196
22,replace,1.0,7,c,Oc1n,O71n,4,replace c at position 1 with 7,flow_matching,0.3,2.0,42,196
23,remove,2.0,1,,O71n,O7n,3,remove 1 from position 2,flow_matching,0.3,2.0,42,196
24,replace,0.0,C,O,O7n,C7n,3,replace O at position 0 with C,flow_matching,0.3,2.0,42,196
25,replace,1.0,O,7,C7n,COn,3,replace 7 at position 1 with O,flow_matching,0.3,2.0,42,196
26,replace,2.0,c,n,COn,COc,3,replace n at position 2 with c,flow_matching,0.3,2.0,42,196
27,replace,2.0,4,c,COc,CO4,3,replace c at position 2 with 4,flow_matching,0.3,2.0,42,196
28,replace,2.0,c,4,CO4,COc,3,replace 4 at position 2 with c,flow_matching,0.3,2.0,42,196
29,remove,0.0,C,,COc,Oc,2,remove C from position 0,flow_matching,0.3,2.0,42,196
30,replace,0.0,C,O,Oc,Cc,2,replace O at position 0 with C,flow_matching,0.3,2.0,42,196
31,remove,1.0,c,,Cc,C,1,remove c from position 1,flow_matching,0.3,2.0,42,196
32,add,0.0,F,,C,FC,2,add F at position 0,flow_matching,0.3,2.0,42,196
33,add,2.0,7,,FC,FC7,3,add 7 at position 2,flow_matching,0.3,2.0,42,196
34,replace,2.0,F,7,FC7,FCF,3,replace 7 at position 2 with F,flow_matching,0.3,2.0,42,196
35,replace,0.0,C,F,FCF,CCF,3,replace F at position 0 with C,flow_matching,0.3,2.0,42,196
36,replace,1.0,O,C,CCF,COF,3,replace C at position 1 with O,flow_matching,0.3,2.0,42,196
37,replace,2.0,c,F,COF,COc,3,replace F at position 2 with c,flow_matching,0.3,2.0,42,196
38,add,3.0,1,,COc,COc1,4,add 1 at position 3,flow_matching,0.3,2.0,42,196
39,add,2.0,@,,COc1,CO@c1,5,add @ at position 2,flow_matching,0.3,2.0,42,196
40,replace,2.0,c,@,CO@c1,COcc1,5,replace @ at position 2 with c,flow_matching,0.3,2.0,42,196
41,remove,3.0,c,,COcc1,COc1,4,remove c from position 3,flow_matching,0.3,2.0,42,196
42,add,4.0,c,,COc1,COc1c,5,add c at position 4,flow_matching,0.3,2.0,42,196
43,add,5.0,),,COc1c,COc1c),6,add ) at position 5,flow_matching,0.3,2.0,42,196
44,replace,5.0,c,),COc1c),COc1cc,6,replace ) at position 5 with c,flow_matching,0.3,2.0,42,196
45,add,6.0,=,,COc1cc,COc1cc=,7,add = at position 6,flow_matching,0.3,2.0,42,196
46,replace,6.0,c,=,COc1cc=,COc1ccc,7,replace = at position 6 with c,flow_matching,0.3,2.0,42,196
47,add,7.0,(,,COc1ccc,COc1ccc(,8,add ( at position 7,flow_matching,0.3,2.0,42,196
48,replace,5.0,(,c,COc1ccc(,COc1c(c(,8,replace c at position 5 with (,flow_matching,0.3,2.0,42,196
49,replace,5.0,6,(,COc1c(c(,COc1c6c(,8,replace ( at position 5 with 6,flow_matching,0.3,2.0,42,196
50,add,1.0,-,,COc1c6c(,C-Oc1c6c(,9,add - at position 1,flow_matching,0.3,2.0,42,196
51,add,8.0,-,,C-Oc1c6c(,C-Oc1c6c-(,10,add - at position 8,flow_matching,0.3,2.0,42,196
52,replace,1.0,O,-,C-Oc1c6c-(,COOc1c6c-(,10,replace - at position 1 with O,flow_matching,0.3,2.0,42,196
53,replace,9.0,r,(,COOc1c6c-(,COOc1c6c-r,10,replace ( at position 9 with r,flow_matching,0.3,2.0,42,196
54,add,4.0,+,,COOc1c6c-r,COOc+1c6c-r,11,add + at position 4,flow_matching,0.3,2.0,42,196
55,replace,10.0,N,r,COOc+1c6c-r,COOc+1c6c-N,11,replace r at position 10 with N,flow_matching,0.3,2.0,42,196
56,replace,2.0,c,O,COOc+1c6c-N,COcc+1c6c-N,11,replace O at position 2 with c,flow_matching,0.3,2.0,42,196
57,replace,3.0,1,c,COcc+1c6c-N,COc1+1c6c-N,11,replace c at position 3 with 1,flow_matching,0.3,2.0,42,196
58,add,10.0,I,,COc1+1c6c-N,COc1+1c6c-IN,12,add I at position 10,flow_matching,0.3,2.0,42,196
59,remove,10.0,I,,COc1+1c6c-IN,COc1+1c6c-N,11,remove I from position 10,flow_matching,0.3,2.0,42,196
60,add,2.0,N,,COc1+1c6c-N,CONc1+1c6c-N,12,add N at position 2,flow_matching,0.3,2.0,42,196
61,replace,2.0,c,N,CONc1+1c6c-N,COcc1+1c6c-N,12,replace N at position 2 with c,flow_matching,0.3,2.0,42,196
62,replace,3.0,1,c,COcc1+1c6c-N,COc11+1c6c-N,12,replace c at position 3 with 1,flow_matching,0.3,2.0,42,196
63,remove,9.0,c,,COc11+1c6c-N,COc11+1c6-N,11,remove c from position 9,flow_matching,0.3,2.0,42,196
64,add,6.0,=,,COc11+1c6-N,COc11+=1c6-N,12,add = at position 6,flow_matching,0.3,2.0,42,196
65,replace,11.0,\,N,COc11+=1c6-N,COc11+=1c6-\,12,replace N at position 11 with \,flow_matching,0.3,2.0,42,196
66,add,3.0,H,,COc11+=1c6-\,COcH11+=1c6-\,13,add H at position 3,flow_matching,0.3,2.0,42,196
67,add,4.0,),,COcH11+=1c6-\,COcH)11+=1c6-\,14,add ) at position 4,flow_matching,0.3,2.0,42,196
68,remove,2.0,c,,COcH)11+=1c6-\,COH)11+=1c6-\,13,remove c from position 2,flow_matching,0.3,2.0,42,196
69,add,6.0,S,,COH)11+=1c6-\,COH)11S+=1c6-\,14,add S at position 6,flow_matching,0.3,2.0,42,196
70,remove,8.0,=,,COH)11S+=1c6-\,COH)11S+1c6-\,13,remove = from position 8,flow_matching,0.3,2.0,42,196
71,remove,12.0,\,,COH)11S+1c6-\,COH)11S+1c6-,12,remove \ from position 12,flow_matching,0.3,2.0,42,196
72,remove,4.0,1,,COH)11S+1c6-,COH)1S+1c6-,11,remove 1 from position 4,flow_matching,0.3,2.0,42,196
73,remove,9.0,6,,COH)1S+1c6-,COH)1S+1c-,10,remove 6 from position 9,flow_matching,0.3,2.0,42,196
74,replace,2.0,c,H,COH)1S+1c-,COc)1S+1c-,10,replace H at position 2 with c,flow_matching,0.3,2.0,42,196
75,add,10.0,C,,COc)1S+1c-,COc)1S+1c-C,11,add C at position 10,flow_matching,0.3,2.0,42,196
76,replace,3.0,1,),COc)1S+1c-C,COc11S+1c-C,11,replace ) at position 3 with 1,flow_matching,0.3,2.0,42,196
77,replace,10.0,4,C,COc11S+1c-C,COc11S+1c-4,11,replace C at position 10 with 4,flow_matching,0.3,2.0,42,196
78,replace,4.0,c,1,COc11S+1c-4,COc1cS+1c-4,11,replace 1 at position 4 with c,flow_matching,0.3,2.0,42,196
79,add,1.0,#,,COc1cS+1c-4,C#Oc1cS+1c-4,12,add # at position 1,flow_matching,0.3,2.0,42,196
80,replace,5.0,F,c,C#Oc1cS+1c-4,C#Oc1FS+1c-4,12,replace c at position 5 with F,flow_matching,0.3,2.0,42,196
81,replace,1.0,O,#,C#Oc1FS+1c-4,COOc1FS+1c-4,12,replace # at position 1 with O,flow_matching,0.3,2.0,42,196
82,add,11.0,6,,COOc1FS+1c-4,COOc1FS+1c-64,13,add 6 at position 11,flow_matching,0.3,2.0,42,196
83,replace,9.0,F,c,COOc1FS+1c-64,COOc1FS+1F-64,13,replace c at position 9 with F,flow_matching,0.3,2.0,42,196
84,remove,10.0,-,,COOc1FS+1F-64,COOc1FS+1F64,12,remove - from position 10,flow_matching,0.3,2.0,42,196
85,replace,2.0,c,O,COOc1FS+1F64,COcc1FS+1F64,12,replace O at position 2 with c,flow_matching,0.3,2.0,42,196
86,replace,3.0,1,c,COcc1FS+1F64,COc11FS+1F64,12,replace c at position 3 with 1,flow_matching,0.3,2.0,42,196
87,add,6.0,3,,COc11FS+1F64,COc11F3S+1F64,13,add 3 at position 6,flow_matching,0.3,2.0,42,196
88,replace,7.0,o,S,COc11F3S+1F64,COc11F3o+1F64,13,replace S at position 7 with o,flow_matching,0.3,2.0,42,196
89,replace,11.0,@,6,COc11F3o+1F64,COc11F3o+1F@4,13,replace 6 at position 11 with @,flow_matching,0.3,2.0,42,196
90,add,13.0,B,,COc11F3o+1F@4,COc11F3o+1F@4B,14,add B at position 13,flow_matching,0.3,2.0,42,196
91,add,13.0,7,,COc11F3o+1F@4B,COc11F3o+1F@47B,15,add 7 at position 13,flow_matching,0.3,2.0,42,196
92,replace,1.0,S,O,COc11F3o+1F@47B,CSc11F3o+1F@47B,15,replace O at position 1 with S,flow_matching,0.3,2.0,42,196
93,replace,1.0,O,S,CSc11F3o+1F@47B,COc11F3o+1F@47B,15,replace S at position 1 with O,flow_matching,0.3,2.0,42,196
94,add,5.0,H,,COc11F3o+1F@47B,COc11HF3o+1F@47B,16,add H at position 5,flow_matching,0.3,2.0,42,196
95,replace,8.0,S,o,COc11HF3o+1F@47B,COc11HF3S+1F@47B,16,replace o at position 8 with S,flow_matching,0.3,2.0,42,196
96,replace,10.0,3,1,COc11HF3S+1F@47B,COc11HF3S+3F@47B,16,replace 1 at position 10 with 3,flow_matching,0.3,2.0,42,196
97,replace,4.0,c,1,COc11HF3S+3F@47B,COc1cHF3S+3F@47B,16,replace 1 at position 4 with c,flow_matching,0.3,2.0,42,196
98,add,12.0,3,,COc1cHF3S+3F@47B,COc1cHF3S+3F3@47B,17,add 3 at position 12,flow_matching,0.3,2.0,42,196
99,replace,5.0,c,H,COc1cHF3S+3F3@47B,COc1ccF3S+3F3@47B,17,replace H at position 5 with c,flow_matching,0.3,2.0,42,196
100,remove,15.0,7,,COc1ccF3S+3F3@47B,COc1ccF3S+3F3@4B,16,remove 7 from position 15,flow_matching,0.3,2.0,42,196
101,replace,6.0,c,F,COc1ccF3S+3F3@4B,COc1ccc3S+3F3@4B,16,replace F at position 6 with c,flow_matching,0.3,2.0,42,196
102,add,13.0,6,,COc1ccc3S+3F3@4B,COc1ccc3S+3F36@4B,17,add 6 at position 13,flow_matching,0.3,2.0,42,196
103,replace,7.0,(,3,COc1ccc3S+3F36@4B,COc1ccc(S+3F36@4B,17,replace 3 at position 7 with (,flow_matching,0.3,2.0,42,196
104,remove,10.0,3,,COc1ccc(S+3F36@4B,COc1ccc(S+F36@4B,16,remove 3 from position 10,flow_matching,0.3,2.0,42,196
105,remove,9.0,+,,COc1ccc(S+F36@4B,COc1ccc(SF36@4B,15,remove + from position 9,flow_matching,0.3,2.0,42,196
106,replace,8.0,N,S,COc1ccc(SF36@4B,COc1ccc(NF36@4B,15,replace S at position 8 with N,flow_matching,0.3,2.0,42,196
107,replace,8.0,[,N,COc1ccc(NF36@4B,COc1ccc([F36@4B,15,replace N at position 8 with [,flow_matching,0.3,2.0,42,196
108,remove,12.0,@,,COc1ccc([F36@4B,COc1ccc([F364B,14,remove @ from position 12,flow_matching,0.3,2.0,42,196
109,remove,5.0,c,,COc1ccc([F364B,COc1cc([F364B,13,remove c from position 5,flow_matching,0.3,2.0,42,196
110,replace,6.0,c,(,COc1cc([F364B,COc1ccc[F364B,13,replace ( at position 6 with c,flow_matching,0.3,2.0,42,196
111,add,3.0,F,,COc1ccc[F364B,COcF1ccc[F364B,14,add F at position 3,flow_matching,0.3,2.0,42,196
112,add,13.0,I,,COcF1ccc[F364B,COcF1ccc[F364IB,15,add I at position 13,flow_matching,0.3,2.0,42,196
113,remove,2.0,c,,COcF1ccc[F364IB,COF1ccc[F364IB,14,remove c from position 2,flow_matching,0.3,2.0,42,196
114,remove,7.0,[,,COF1ccc[F364IB,COF1cccF364IB,13,remove [ from position 7,flow_matching,0.3,2.0,42,196
115,add,6.0,3,,COF1cccF364IB,COF1cc3cF364IB,14,add 3 at position 6,flow_matching,0.3,2.0,42,196
116,add,0.0,+,,COF1cc3cF364IB,+COF1cc3cF364IB,15,add + at position 0,flow_matching,0.3,2.0,42,196
117,add,10.0,6,,+COF1cc3cF364IB,+COF1cc3cF6364IB,16,add 6 at position 10,flow_matching,0.3,2.0,42,196
118,replace,0.0,C,+,+COF1cc3cF6364IB,CCOF1cc3cF6364IB,16,replace + at position 0 with C,flow_matching,0.3,2.0,42,196
119,replace,9.0,7,F,CCOF1cc3cF6364IB,CCOF1cc3c76364IB,16,replace F at position 9 with 7,flow_matching,0.3,2.0,42,196
120,add,14.0,7,,CCOF1cc3c76364IB,CCOF1cc3c763647IB,17,add 7 at position 14,flow_matching,0.3,2.0,42,196
121,replace,1.0,O,C,CCOF1cc3c763647IB,COOF1cc3c763647IB,17,replace C at position 1 with O,flow_matching,0.3,2.0,42,196
122,replace,2.0,c,O,COOF1cc3c763647IB,COcF1cc3c763647IB,17,replace O at position 2 with c,flow_matching,0.3,2.0,42,196
123,remove,2.0,c,,COcF1cc3c763647IB,COF1cc3c763647IB,16,remove c from position 2,flow_matching,0.3,2.0,42,196
124,add,11.0,],,COF1cc3c763647IB,COF1cc3c763]647IB,17,add ] at position 11,flow_matching,0.3,2.0,42,196
125,remove,15.0,I,,COF1cc3c763]647IB,COF1cc3c763]647B,16,remove I from position 15,flow_matching,0.3,2.0,42,196
126,replace,14.0,l,7,COF1cc3c763]647B,COF1cc3c763]64lB,16,replace 7 at position 14 with l,flow_matching,0.3,2.0,42,196
127,replace,3.0,5,1,COF1cc3c763]64lB,COF5cc3c763]64lB,16,replace 1 at position 3 with 5,flow_matching,0.3,2.0,42,196
128,add,1.0,/,,COF5cc3c763]64lB,C/OF5cc3c763]64lB,17,add / at position 1,flow_matching,0.3,2.0,42,196
129,replace,15.0,-,l,C/OF5cc3c763]64lB,C/OF5cc3c763]64-B,17,replace l at position 15 with -,flow_matching,0.3,2.0,42,196
130,add,10.0,O,,C/OF5cc3c763]64-B,C/OF5cc3c7O63]64-B,18,add O at position 10,flow_matching,0.3,2.0,42,196
131,remove,3.0,F,,C/OF5cc3c7O63]64-B,C/O5cc3c7O63]64-B,17,remove F from position 3,flow_matching,0.3,2.0,42,196
132,replace,1.0,O,/,C/O5cc3c7O63]64-B,COO5cc3c7O63]64-B,17,replace / at position 1 with O,flow_matching,0.3,2.0,42,196
133,replace,13.0,(,6,COO5cc3c7O63]64-B,COO5cc3c7O63](4-B,17,replace 6 at position 13 with (,flow_matching,0.3,2.0,42,196
134,add,3.0,[,,COO5cc3c7O63](4-B,COO[5cc3c7O63](4-B,18,add [ at position 3,flow_matching,0.3,2.0,42,196
135,remove,4.0,5,,COO[5cc3c7O63](4-B,COO[cc3c7O63](4-B,17,remove 5 from position 4,flow_matching,0.3,2.0,42,196
136,replace,0.0,[,C,COO[cc3c7O63](4-B,[OO[cc3c7O63](4-B,17,replace C at position 0 with [,flow_matching,0.3,2.0,42,196
137,remove,0.0,[,,[OO[cc3c7O63](4-B,OO[cc3c7O63](4-B,16,remove [ from position 0,flow_matching,0.3,2.0,42,196
138,add,9.0,@,,OO[cc3c7O63](4-B,OO[cc3c7O@63](4-B,17,add @ at position 9,flow_matching,0.3,2.0,42,196
139,remove,8.0,O,,OO[cc3c7O@63](4-B,OO[cc3c7@63](4-B,16,remove O from position 8,flow_matching,0.3,2.0,42,196
140,replace,0.0,C,O,OO[cc3c7@63](4-B,CO[cc3c7@63](4-B,16,replace O at position 0 with C,flow_matching,0.3,2.0,42,196
141,add,15.0,=,,CO[cc3c7@63](4-B,CO[cc3c7@63](4-=B,17,add = at position 15,flow_matching,0.3,2.0,42,196
142,add,6.0,O,,CO[cc3c7@63](4-=B,CO[cc3Oc7@63](4-=B,18,add O at position 6,flow_matching,0.3,2.0,42,196
143,add,5.0,l,,CO[cc3Oc7@63](4-=B,CO[ccl3Oc7@63](4-=B,19,add l at position 5,flow_matching,0.3,2.0,42,196
144,remove,18.0,B,,CO[ccl3Oc7@63](4-=B,CO[ccl3Oc7@63](4-=,18,remove B from position 18,flow_matching,0.3,2.0,42,196
145,replace,10.0,N,@,CO[ccl3Oc7@63](4-=,CO[ccl3Oc7N63](4-=,18,replace @ at position 10 with N,flow_matching,0.3,2.0,42,196
146,add,5.0,r,,CO[ccl3Oc7N63](4-=,CO[ccrl3Oc7N63](4-=,19,add r at position 5,flow_matching,0.3,2.0,42,196
147,remove,12.0,6,,CO[ccrl3Oc7N63](4-=,CO[ccrl3Oc7N3](4-=,18,remove 6 from position 12,flow_matching,0.3,2.0,42,196
148,replace,10.0,c,7,CO[ccrl3Oc7N3](4-=,CO[ccrl3OccN3](4-=,18,replace 7 at position 10 with c,flow_matching,0.3,2.0,42,196
149,remove,10.0,c,,CO[ccrl3OccN3](4-=,CO[ccrl3OcN3](4-=,17,remove c from position 10,flow_matching,0.3,2.0,42,196
150,add,0.0,6,,CO[ccrl3OcN3](4-=,6CO[ccrl3OcN3](4-=,18,add 6 at position 0,flow_matching,0.3,2.0,42,196
151,remove,13.0,],,6CO[ccrl3OcN3](4-=,6CO[ccrl3OcN3(4-=,17,remove ] from position 13,flow_matching,0.3,2.0,42,196
152,remove,8.0,3,,6CO[ccrl3OcN3(4-=,6CO[ccrlOcN3(4-=,16,remove 3 from position 8,flow_matching,0.3,2.0,42,196
153,add,6.0,s,,6CO[ccrlOcN3(4-=,6CO[ccsrlOcN3(4-=,17,add s at position 6,flow_matching,0.3,2.0,42,196
154,replace,0.0,C,6,6CO[ccsrlOcN3(4-=,CCO[ccsrlOcN3(4-=,17,replace 6 at position 0 with C,flow_matching,0.3,2.0,42,196
155,replace,3.0,),[,CCO[ccsrlOcN3(4-=,CCO)ccsrlOcN3(4-=,17,replace [ at position 3 with ),flow_matching,0.3,2.0,42,196
156,add,4.0,I,,CCO)ccsrlOcN3(4-=,CCO)IccsrlOcN3(4-=,18,add I at position 4,flow_matching,0.3,2.0,42,196
157,remove,4.0,I,,CCO)IccsrlOcN3(4-=,CCO)ccsrlOcN3(4-=,17,remove I from position 4,flow_matching,0.3,2.0,42,196
158,replace,1.0,O,C,CCO)ccsrlOcN3(4-=,COO)ccsrlOcN3(4-=,17,replace C at position 1 with O,flow_matching,0.3,2.0,42,196
159,replace,2.0,c,O,COO)ccsrlOcN3(4-=,COc)ccsrlOcN3(4-=,17,replace O at position 2 with c,flow_matching,0.3,2.0,42,196
160,remove,14.0,4,,COc)ccsrlOcN3(4-=,COc)ccsrlOcN3(-=,16,remove 4 from position 14,flow_matching,0.3,2.0,42,196
161,replace,3.0,1,),COc)ccsrlOcN3(-=,COc1ccsrlOcN3(-=,16,replace ) at position 3 with 1,flow_matching,0.3,2.0,42,196
162,replace,6.0,c,s,COc1ccsrlOcN3(-=,COc1cccrlOcN3(-=,16,replace s at position 6 with c,flow_matching,0.3,2.0,42,196
163,replace,7.0,(,r,COc1cccrlOcN3(-=,COc1ccc(lOcN3(-=,16,replace r at position 7 with (,flow_matching,0.3,2.0,42,196
164,replace,8.0,[,l,COc1ccc(lOcN3(-=,COc1ccc([OcN3(-=,16,replace l at position 8 with [,flow_matching,0.3,2.0,42,196
165,replace,9.0,C,O,COc1ccc([OcN3(-=,COc1ccc([CcN3(-=,16,replace O at position 9 with C,flow_matching,0.3,2.0,42,196
166,replace,10.0,@,c,COc1ccc([CcN3(-=,COc1ccc([C@N3(-=,16,replace c at position 10 with @,flow_matching,0.3,2.0,42,196
167,replace,11.0,@,N,COc1ccc([C@N3(-=,COc1ccc([C@@3(-=,16,replace N at position 11 with @,flow_matching,0.3,2.0,42,196
168,replace,12.0,],3,COc1ccc([C@@3(-=,COc1ccc([C@@](-=,16,replace 3 at position 12 with ],flow_matching,0.3,2.0,42,196
169,replace,14.0,C,-,COc1ccc([C@@](-=,COc1ccc([C@@](C=,16,replace - at position 14 with C,flow_matching,0.3,2.0,42,196
170,replace,15.0,),=,COc1ccc([C@@](C=,COc1ccc([C@@](C),16,replace = at position 15 with ),flow_matching,0.3,2.0,42,196
171,add,16.0,(,,COc1ccc([C@@](C),COc1ccc([C@@](C)(,17,add ( at position 16,flow_matching,0.3,2.0,42,196
172,add,17.0,[,,COc1ccc([C@@](C)(,COc1ccc([C@@](C)([,18,add [ at position 17,flow_matching,0.3,2.0,42,196
173,add,18.0,N,,COc1ccc([C@@](C)([,COc1ccc([C@@](C)([N,19,add N at position 18,flow_matching,0.3,2.0,42,196
174,add,19.0,H,,COc1ccc([C@@](C)([N,COc1ccc([C@@](C)([NH,20,add H at position 19,flow_matching,0.3,2.0,42,196
175,add,20.0,3,,COc1ccc([C@@](C)([NH,COc1ccc([C@@](C)([NH3,21,add 3 at position 20,flow_matching,0.3,2.0,42,196
176,add,21.0,+,,COc1ccc([C@@](C)([NH3,COc1ccc([C@@](C)([NH3+,22,add + at position 21,flow_matching,0.3,2.0,42,196
177,add,22.0,],,COc1ccc([C@@](C)([NH3+,COc1ccc([C@@](C)([NH3+],23,add ] at position 22,flow_matching,0.3,2.0,42,196
178,add,23.0,),,COc1ccc([C@@](C)([NH3+],COc1ccc([C@@](C)([NH3+]),24,add ) at position 23,flow_matching,0.3,2.0,42,196
179,add,24.0,C,,COc1ccc([C@@](C)([NH3+]),COc1ccc([C@@](C)([NH3+])C,25,add C at position 24,flow_matching,0.3,2.0,42,196
180,add,25.0,c,,COc1ccc([C@@](C)([NH3+])C,COc1ccc([C@@](C)([NH3+])Cc,26,add c at position 25,flow_matching,0.3,2.0,42,196
181,add,26.0,2,,COc1ccc([C@@](C)([NH3+])Cc,COc1ccc([C@@](C)([NH3+])Cc2,27,add 2 at position 26,flow_matching,0.3,2.0,42,196
182,add,27.0,[,,COc1ccc([C@@](C)([NH3+])Cc2,COc1ccc([C@@](C)([NH3+])Cc2[,28,add [ at position 27,flow_matching,0.3,2.0,42,196
183,add,28.0,n,,COc1ccc([C@@](C)([NH3+])Cc2[,COc1ccc([C@@](C)([NH3+])Cc2[n,29,add n at position 28,flow_matching,0.3,2.0,42,196
184,add,29.0,H,,COc1ccc([C@@](C)([NH3+])Cc2[n,COc1ccc([C@@](C)([NH3+])Cc2[nH,30,add H at position 29,flow_matching,0.3,2.0,42,196
185,add,30.0,+,,COc1ccc([C@@](C)([NH3+])Cc2[nH,COc1ccc([C@@](C)([NH3+])Cc2[nH+,31,add + at position 30,flow_matching,0.3,2.0,42,196
186,add,31.0,],,COc1ccc([C@@](C)([NH3+])Cc2[nH+,COc1ccc([C@@](C)([NH3+])Cc2[nH+],32,add ] at position 31,flow_matching,0.3,2.0,42,196
187,add,32.0,c,,COc1ccc([C@@](C)([NH3+])Cc2[nH+],COc1ccc([C@@](C)([NH3+])Cc2[nH+]c,33,add c at position 32,flow_matching,0.3,2.0,42,196
188,add,33.0,c,,COc1ccc([C@@](C)([NH3+])Cc2[nH+]c,COc1ccc([C@@](C)([NH3+])Cc2[nH+]cc,34,add c at position 33,flow_matching,0.3,2.0,42,196
189,add,34.0,n,,COc1ccc([C@@](C)([NH3+])Cc2[nH+]cc,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn,35,add n at position 34,flow_matching,0.3,2.0,42,196
190,add,35.0,2,,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2,36,add 2 at position 35,flow_matching,0.3,2.0,42,196
191,add,36.0,C,,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C,37,add C at position 36,flow_matching,0.3,2.0,42,196
192,add,37.0,),,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C),38,add ) at position 37,flow_matching,0.3,2.0,42,196
193,add,38.0,c,,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C),COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C)c,39,add c at position 38,flow_matching,0.3,2.0,42,196
194,add,39.0,c,,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C)c,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C)cc,40,add c at position 39,flow_matching,0.3,2.0,42,196
195,add,40.0,1,,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C)cc,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C)cc1,41,add 1 at position 40,flow_matching,0.3,2.0,42,196
196,add,41.0,"
",,COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C)cc1,"COc1ccc([C@@](C)([NH3+])Cc2[nH+]ccn2C)cc1
",42,"add 
 at position 41",flow_matching,0.3,2.0,42,196
