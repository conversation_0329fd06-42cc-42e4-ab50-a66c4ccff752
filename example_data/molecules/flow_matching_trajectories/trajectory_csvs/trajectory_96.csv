step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,35,126
1,add,0.0,l,,,l,1,add l at position 0,flow_matching,0.3,2.0,35,126
2,replace,0.0,C,l,l,C,1,replace l at position 0 with C,flow_matching,0.3,2.0,35,126
3,add,0.0,-,,C,-C,2,add - at position 0,flow_matching,0.3,2.0,35,126
4,add,1.0,5,,-C,-5C,3,add 5 at position 1,flow_matching,0.3,2.0,35,126
5,add,2.0,),,-5C,-5)C,4,add ) at position 2,flow_matching,0.3,2.0,35,126
6,replace,1.0,C,5,-5)C,-C)C,4,replace 5 at position 1 with C,flow_matching,0.3,2.0,35,126
7,add,3.0,s,,-C)C,-C)sC,5,add s at position 3,flow_matching,0.3,2.0,35,126
8,remove,3.0,s,,-C)sC,-C)C,4,remove s from position 3,flow_matching,0.3,2.0,35,126
9,replace,0.0,C,-,-C)C,CC)C,4,replace - at position 0 with C,flow_matching,0.3,2.0,35,126
10,remove,3.0,C,,CC)C,CC),3,remove C from position 3,flow_matching,0.3,2.0,35,126
11,add,1.0,c,,CC),CcC),4,add c at position 1,flow_matching,0.3,2.0,35,126
12,replace,0.0,O,C,CcC),OcC),4,replace C at position 0 with O,flow_matching,0.3,2.0,35,126
13,replace,0.0,C,O,OcC),CcC),4,replace O at position 0 with C,flow_matching,0.3,2.0,35,126
14,replace,2.0,1,C,CcC),Cc1),4,replace C at position 2 with 1,flow_matching,0.3,2.0,35,126
15,replace,2.0,s,1,Cc1),Ccs),4,replace 1 at position 2 with s,flow_matching,0.3,2.0,35,126
16,replace,2.0,1,s,Ccs),Cc1),4,replace s at position 2 with 1,flow_matching,0.3,2.0,35,126
17,remove,1.0,c,,Cc1),C1),3,remove c from position 1,flow_matching,0.3,2.0,35,126
18,replace,1.0,c,1,C1),Cc),3,replace 1 at position 1 with c,flow_matching,0.3,2.0,35,126
19,add,0.0,/,,Cc),/Cc),4,add / at position 0,flow_matching,0.3,2.0,35,126
20,remove,1.0,C,,/Cc),/c),3,remove C from position 1,flow_matching,0.3,2.0,35,126
21,replace,0.0,n,/,/c),nc),3,replace / at position 0 with n,flow_matching,0.3,2.0,35,126
22,replace,0.0,1,n,nc),1c),3,replace n at position 0 with 1,flow_matching,0.3,2.0,35,126
23,replace,0.0,C,1,1c),Cc),3,replace 1 at position 0 with C,flow_matching,0.3,2.0,35,126
24,remove,2.0,),,Cc),Cc,2,remove ) from position 2,flow_matching,0.3,2.0,35,126
25,remove,0.0,C,,Cc,c,1,remove C from position 0,flow_matching,0.3,2.0,35,126
26,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,35,126
27,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,35,126
28,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,35,126
29,replace,0.0,N,O,O,N,1,replace O at position 0 with N,flow_matching,0.3,2.0,35,126
30,replace,0.0,C,N,N,C,1,replace N at position 0 with C,flow_matching,0.3,2.0,35,126
31,replace,0.0,s,C,C,s,1,replace C at position 0 with s,flow_matching,0.3,2.0,35,126
32,replace,0.0,C,s,s,C,1,replace s at position 0 with C,flow_matching,0.3,2.0,35,126
33,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,35,126
34,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,35,126
35,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,35,126
36,replace,3.0,#,c,Cc1c,Cc1#,4,replace c at position 3 with #,flow_matching,0.3,2.0,35,126
37,remove,2.0,1,,Cc1#,Cc#,3,remove 1 from position 2,flow_matching,0.3,2.0,35,126
38,replace,2.0,],#,Cc#,Cc],3,replace # at position 2 with ],flow_matching,0.3,2.0,35,126
39,replace,2.0,1,],Cc],Cc1,3,replace ] at position 2 with 1,flow_matching,0.3,2.0,35,126
40,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,35,126
41,remove,0.0,C,,Cc1c,c1c,3,remove C from position 0,flow_matching,0.3,2.0,35,126
42,replace,2.0,I,c,c1c,c1I,3,replace c at position 2 with I,flow_matching,0.3,2.0,35,126
43,add,0.0,B,,c1I,Bc1I,4,add B at position 0,flow_matching,0.3,2.0,35,126
44,replace,0.0,C,B,Bc1I,Cc1I,4,replace B at position 0 with C,flow_matching,0.3,2.0,35,126
45,add,4.0,S,,Cc1I,Cc1IS,5,add S at position 4,flow_matching,0.3,2.0,35,126
46,replace,3.0,c,I,Cc1IS,Cc1cS,5,replace I at position 3 with c,flow_matching,0.3,2.0,35,126
47,remove,2.0,1,,Cc1cS,CccS,4,remove 1 from position 2,flow_matching,0.3,2.0,35,126
48,replace,0.0,=,C,CccS,=ccS,4,replace C at position 0 with =,flow_matching,0.3,2.0,35,126
49,remove,3.0,S,,=ccS,=cc,3,remove S from position 3,flow_matching,0.3,2.0,35,126
50,replace,0.0,C,=,=cc,Ccc,3,replace = at position 0 with C,flow_matching,0.3,2.0,35,126
51,remove,0.0,C,,Ccc,cc,2,remove C from position 0,flow_matching,0.3,2.0,35,126
52,remove,1.0,c,,cc,c,1,remove c from position 1,flow_matching,0.3,2.0,35,126
53,add,1.0,4,,c,c4,2,add 4 at position 1,flow_matching,0.3,2.0,35,126
54,add,1.0,4,,c4,c44,3,add 4 at position 1,flow_matching,0.3,2.0,35,126
55,remove,1.0,4,,c44,c4,2,remove 4 from position 1,flow_matching,0.3,2.0,35,126
56,remove,1.0,4,,c4,c,1,remove 4 from position 1,flow_matching,0.3,2.0,35,126
57,add,0.0,H,,c,Hc,2,add H at position 0,flow_matching,0.3,2.0,35,126
58,replace,0.0,o,H,Hc,oc,2,replace H at position 0 with o,flow_matching,0.3,2.0,35,126
59,add,0.0,1,,oc,1oc,3,add 1 at position 0,flow_matching,0.3,2.0,35,126
60,replace,1.0,B,o,1oc,1Bc,3,replace o at position 1 with B,flow_matching,0.3,2.0,35,126
61,replace,0.0,C,1,1Bc,CBc,3,replace 1 at position 0 with C,flow_matching,0.3,2.0,35,126
62,remove,1.0,B,,CBc,Cc,2,remove B from position 1,flow_matching,0.3,2.0,35,126
63,remove,1.0,c,,Cc,C,1,remove c from position 1,flow_matching,0.3,2.0,35,126
64,add,0.0,3,,C,3C,2,add 3 at position 0,flow_matching,0.3,2.0,35,126
65,replace,1.0,-,C,3C,3-,2,replace C at position 1 with -,flow_matching,0.3,2.0,35,126
66,remove,1.0,-,,3-,3,1,remove - from position 1,flow_matching,0.3,2.0,35,126
67,replace,0.0,C,3,3,C,1,replace 3 at position 0 with C,flow_matching,0.3,2.0,35,126
68,add,1.0,l,,C,Cl,2,add l at position 1,flow_matching,0.3,2.0,35,126
69,replace,1.0,c,l,Cl,Cc,2,replace l at position 1 with c,flow_matching,0.3,2.0,35,126
70,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,35,126
71,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,35,126
72,add,3.0,[,,Cc1c,Cc1[c,5,add [ at position 3,flow_matching,0.3,2.0,35,126
73,replace,3.0,l,[,Cc1[c,Cc1lc,5,replace [ at position 3 with l,flow_matching,0.3,2.0,35,126
74,add,0.0,[,,Cc1lc,[Cc1lc,6,add [ at position 0,flow_matching,0.3,2.0,35,126
75,remove,1.0,C,,[Cc1lc,[c1lc,5,remove C from position 1,flow_matching,0.3,2.0,35,126
76,replace,0.0,C,[,[c1lc,Cc1lc,5,replace [ at position 0 with C,flow_matching,0.3,2.0,35,126
77,add,1.0,F,,Cc1lc,CFc1lc,6,add F at position 1,flow_matching,0.3,2.0,35,126
78,replace,1.0,c,F,CFc1lc,Ccc1lc,6,replace F at position 1 with c,flow_matching,0.3,2.0,35,126
79,replace,2.0,1,c,Ccc1lc,Cc11lc,6,replace c at position 2 with 1,flow_matching,0.3,2.0,35,126
80,replace,3.0,c,1,Cc11lc,Cc1clc,6,replace 1 at position 3 with c,flow_matching,0.3,2.0,35,126
81,add,0.0,#,,Cc1clc,#Cc1clc,7,add # at position 0,flow_matching,0.3,2.0,35,126
82,replace,4.0,l,c,#Cc1clc,#Cc1llc,7,replace c at position 4 with l,flow_matching,0.3,2.0,35,126
83,remove,0.0,#,,#Cc1llc,Cc1llc,6,remove # from position 0,flow_matching,0.3,2.0,35,126
84,add,6.0,+,,Cc1llc,Cc1llc+,7,add + at position 6,flow_matching,0.3,2.0,35,126
85,add,5.0,@,,Cc1llc+,Cc1ll@c+,8,add @ at position 5,flow_matching,0.3,2.0,35,126
86,replace,3.0,[,l,Cc1ll@c+,Cc1[l@c+,8,replace l at position 3 with [,flow_matching,0.3,2.0,35,126
87,replace,3.0,c,[,Cc1[l@c+,Cc1cl@c+,8,replace [ at position 3 with c,flow_matching,0.3,2.0,35,126
88,replace,4.0,c,l,Cc1cl@c+,Cc1cc@c+,8,replace l at position 4 with c,flow_matching,0.3,2.0,35,126
89,add,3.0,H,,Cc1cc@c+,Cc1Hcc@c+,9,add H at position 3,flow_matching,0.3,2.0,35,126
90,add,2.0,2,,Cc1Hcc@c+,Cc21Hcc@c+,10,add 2 at position 2,flow_matching,0.3,2.0,35,126
91,replace,2.0,1,2,Cc21Hcc@c+,Cc11Hcc@c+,10,replace 2 at position 2 with 1,flow_matching,0.3,2.0,35,126
92,replace,3.0,c,1,Cc11Hcc@c+,Cc1cHcc@c+,10,replace 1 at position 3 with c,flow_matching,0.3,2.0,35,126
93,replace,4.0,c,H,Cc1cHcc@c+,Cc1cccc@c+,10,replace H at position 4 with c,flow_matching,0.3,2.0,35,126
94,add,4.0,@,,Cc1cccc@c+,Cc1c@ccc@c+,11,add @ at position 4,flow_matching,0.3,2.0,35,126
95,replace,4.0,r,@,Cc1c@ccc@c+,Cc1crccc@c+,11,replace @ at position 4 with r,flow_matching,0.3,2.0,35,126
96,replace,4.0,l,r,Cc1crccc@c+,Cc1clccc@c+,11,replace r at position 4 with l,flow_matching,0.3,2.0,35,126
97,replace,4.0,c,l,Cc1clccc@c+,Cc1ccccc@c+,11,replace l at position 4 with c,flow_matching,0.3,2.0,35,126
98,replace,5.0,(,c,Cc1ccccc@c+,Cc1cc(cc@c+,11,replace c at position 5 with (,flow_matching,0.3,2.0,35,126
99,replace,6.0,C,c,Cc1cc(cc@c+,Cc1cc(Cc@c+,11,replace c at position 6 with C,flow_matching,0.3,2.0,35,126
100,replace,7.0,l,c,Cc1cc(Cc@c+,Cc1cc(Cl@c+,11,replace c at position 7 with l,flow_matching,0.3,2.0,35,126
101,replace,8.0,),@,Cc1cc(Cl@c+,Cc1cc(Cl)c+,11,replace @ at position 8 with ),flow_matching,0.3,2.0,35,126
102,replace,10.0,c,+,Cc1cc(Cl)c+,Cc1cc(Cl)cc,11,replace + at position 10 with c,flow_matching,0.3,2.0,35,126
103,add,11.0,c,,Cc1cc(Cl)cc,Cc1cc(Cl)ccc,12,add c at position 11,flow_matching,0.3,2.0,35,126
104,add,12.0,1,,Cc1cc(Cl)ccc,Cc1cc(Cl)ccc1,13,add 1 at position 12,flow_matching,0.3,2.0,35,126
105,add,13.0,O,,Cc1cc(Cl)ccc1,Cc1cc(Cl)ccc1O,14,add O at position 13,flow_matching,0.3,2.0,35,126
106,add,14.0,C,,Cc1cc(Cl)ccc1O,Cc1cc(Cl)ccc1OC,15,add C at position 14,flow_matching,0.3,2.0,35,126
107,add,15.0,C,,Cc1cc(Cl)ccc1OC,Cc1cc(Cl)ccc1OCC,16,add C at position 15,flow_matching,0.3,2.0,35,126
108,add,16.0,(,,Cc1cc(Cl)ccc1OCC,Cc1cc(Cl)ccc1OCC(,17,add ( at position 16,flow_matching,0.3,2.0,35,126
109,add,17.0,=,,Cc1cc(Cl)ccc1OCC(,Cc1cc(Cl)ccc1OCC(=,18,add = at position 17,flow_matching,0.3,2.0,35,126
110,add,18.0,O,,Cc1cc(Cl)ccc1OCC(=,Cc1cc(Cl)ccc1OCC(=O,19,add O at position 18,flow_matching,0.3,2.0,35,126
111,add,19.0,),,Cc1cc(Cl)ccc1OCC(=O,Cc1cc(Cl)ccc1OCC(=O),20,add ) at position 19,flow_matching,0.3,2.0,35,126
112,add,20.0,N,,Cc1cc(Cl)ccc1OCC(=O),Cc1cc(Cl)ccc1OCC(=O)N,21,add N at position 20,flow_matching,0.3,2.0,35,126
113,add,21.0,/,,Cc1cc(Cl)ccc1OCC(=O)N,Cc1cc(Cl)ccc1OCC(=O)N/,22,add / at position 21,flow_matching,0.3,2.0,35,126
114,add,22.0,N,,Cc1cc(Cl)ccc1OCC(=O)N/,Cc1cc(Cl)ccc1OCC(=O)N/N,23,add N at position 22,flow_matching,0.3,2.0,35,126
115,add,23.0,=,,Cc1cc(Cl)ccc1OCC(=O)N/N,Cc1cc(Cl)ccc1OCC(=O)N/N=,24,add = at position 23,flow_matching,0.3,2.0,35,126
116,add,24.0,C,,Cc1cc(Cl)ccc1OCC(=O)N/N=,Cc1cc(Cl)ccc1OCC(=O)N/N=C,25,add C at position 24,flow_matching,0.3,2.0,35,126
117,add,25.0,/,,Cc1cc(Cl)ccc1OCC(=O)N/N=C,Cc1cc(Cl)ccc1OCC(=O)N/N=C/,26,add / at position 25,flow_matching,0.3,2.0,35,126
118,add,26.0,c,,Cc1cc(Cl)ccc1OCC(=O)N/N=C/,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c,27,add c at position 26,flow_matching,0.3,2.0,35,126
119,add,27.0,1,,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1,28,add 1 at position 27,flow_matching,0.3,2.0,35,126
120,add,28.0,c,,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1c,29,add c at position 28,flow_matching,0.3,2.0,35,126
121,add,29.0,c,,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1c,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1cc,30,add c at position 29,flow_matching,0.3,2.0,35,126
122,add,30.0,c,,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1cc,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1ccc,31,add c at position 30,flow_matching,0.3,2.0,35,126
123,add,31.0,c,,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1ccc,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1cccc,32,add c at position 31,flow_matching,0.3,2.0,35,126
124,add,32.0,n,,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1cccc,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1ccccn,33,add n at position 32,flow_matching,0.3,2.0,35,126
125,add,33.0,1,,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1ccccn,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1ccccn1,34,add 1 at position 33,flow_matching,0.3,2.0,35,126
126,add,34.0,"
",,Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1ccccn1,"Cc1cc(Cl)ccc1OCC(=O)N/N=C/c1ccccn1
",35,"add 
 at position 34",flow_matching,0.3,2.0,35,126
