step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,42,142
1,add,0.0,s,,,s,1,add s at position 0,flow_matching,0.3,2.0,42,142
2,replace,0.0,n,s,s,n,1,replace s at position 0 with n,flow_matching,0.3,2.0,42,142
3,add,1.0,N,,n,nN,2,add N at position 1,flow_matching,0.3,2.0,42,142
4,replace,0.0,O,n,nN,ON,2,replace n at position 0 with O,flow_matching,0.3,2.0,42,142
5,add,0.0,[,,ON,[ON,3,add [ at position 0,flow_matching,0.3,2.0,42,142
6,add,3.0,S,,[ON,[ONS,4,add S at position 3,flow_matching,0.3,2.0,42,142
7,add,0.0,2,,[ONS,2[ONS,5,add 2 at position 0,flow_matching,0.3,2.0,42,142
8,replace,1.0,),[,2[ONS,2)ONS,5,replace [ at position 1 with ),flow_matching,0.3,2.0,42,142
9,replace,0.0,O,2,2)ONS,O)ONS,5,replace 2 at position 0 with O,flow_matching,0.3,2.0,42,142
10,replace,0.0,H,O,O)ONS,H)ONS,5,replace O at position 0 with H,flow_matching,0.3,2.0,42,142
11,replace,0.0,O,H,H)ONS,O)ONS,5,replace H at position 0 with O,flow_matching,0.3,2.0,42,142
12,add,2.0,H,,O)ONS,O)HONS,6,add H at position 2,flow_matching,0.3,2.0,42,142
13,replace,3.0,6,O,O)HONS,O)H6NS,6,replace O at position 3 with 6,flow_matching,0.3,2.0,42,142
14,add,5.0,/,,O)H6NS,O)H6N/S,7,add / at position 5,flow_matching,0.3,2.0,42,142
15,add,1.0,@,,O)H6N/S,O@)H6N/S,8,add @ at position 1,flow_matching,0.3,2.0,42,142
16,remove,4.0,6,,O@)H6N/S,O@)HN/S,7,remove 6 from position 4,flow_matching,0.3,2.0,42,142
17,remove,0.0,O,,O@)HN/S,@)HN/S,6,remove O from position 0,flow_matching,0.3,2.0,42,142
18,replace,0.0,O,@,@)HN/S,O)HN/S,6,replace @ at position 0 with O,flow_matching,0.3,2.0,42,142
19,add,0.0,(,,O)HN/S,(O)HN/S,7,add ( at position 0,flow_matching,0.3,2.0,42,142
20,replace,0.0,O,(,(O)HN/S,OO)HN/S,7,replace ( at position 0 with O,flow_matching,0.3,2.0,42,142
21,add,0.0,6,,OO)HN/S,6OO)HN/S,8,add 6 at position 0,flow_matching,0.3,2.0,42,142
22,add,0.0,/,,6OO)HN/S,/6OO)HN/S,9,add / at position 0,flow_matching,0.3,2.0,42,142
23,remove,6.0,N,,/6OO)HN/S,/6OO)H/S,8,remove N from position 6,flow_matching,0.3,2.0,42,142
24,add,6.0,=,,/6OO)H/S,/6OO)H=/S,9,add = at position 6,flow_matching,0.3,2.0,42,142
25,remove,2.0,O,,/6OO)H=/S,/6O)H=/S,8,remove O from position 2,flow_matching,0.3,2.0,42,142
26,add,4.0,c,,/6O)H=/S,/6O)cH=/S,9,add c at position 4,flow_matching,0.3,2.0,42,142
27,replace,0.0,O,/,/6O)cH=/S,O6O)cH=/S,9,replace / at position 0 with O,flow_matching,0.3,2.0,42,142
28,remove,7.0,/,,O6O)cH=/S,O6O)cH=S,8,remove / from position 7,flow_matching,0.3,2.0,42,142
29,replace,5.0,),H,O6O)cH=S,O6O)c)=S,8,replace H at position 5 with ),flow_matching,0.3,2.0,42,142
30,replace,1.0,=,6,O6O)c)=S,O=O)c)=S,8,replace 6 at position 1 with =,flow_matching,0.3,2.0,42,142
31,add,3.0,7,,O=O)c)=S,O=O7)c)=S,9,add 7 at position 3,flow_matching,0.3,2.0,42,142
32,remove,4.0,),,O=O7)c)=S,O=O7c)=S,8,remove ) from position 4,flow_matching,0.3,2.0,42,142
33,add,1.0,1,,O=O7c)=S,O1=O7c)=S,9,add 1 at position 1,flow_matching,0.3,2.0,42,142
34,remove,1.0,1,,O1=O7c)=S,O=O7c)=S,8,remove 1 from position 1,flow_matching,0.3,2.0,42,142
35,replace,2.0,s,O,O=O7c)=S,O=s7c)=S,8,replace O at position 2 with s,flow_matching,0.3,2.0,42,142
36,remove,5.0,),,O=s7c)=S,O=s7c=S,7,remove ) from position 5,flow_matching,0.3,2.0,42,142
37,replace,0.0,6,O,O=s7c=S,6=s7c=S,7,replace O at position 0 with 6,flow_matching,0.3,2.0,42,142
38,add,5.0,),,6=s7c=S,6=s7c)=S,8,add ) at position 5,flow_matching,0.3,2.0,42,142
39,add,6.0,O,,6=s7c)=S,6=s7c)O=S,9,add O at position 6,flow_matching,0.3,2.0,42,142
40,replace,0.0,O,6,6=s7c)O=S,O=s7c)O=S,9,replace 6 at position 0 with O,flow_matching,0.3,2.0,42,142
41,replace,2.0,C,s,O=s7c)O=S,O=C7c)O=S,9,replace s at position 2 with C,flow_matching,0.3,2.0,42,142
42,add,3.0,/,,O=C7c)O=S,O=C/7c)O=S,10,add / at position 3,flow_matching,0.3,2.0,42,142
43,replace,3.0,1,/,O=C/7c)O=S,O=C17c)O=S,10,replace / at position 3 with 1,flow_matching,0.3,2.0,42,142
44,add,4.0,5,,O=C17c)O=S,O=C157c)O=S,11,add 5 at position 4,flow_matching,0.3,2.0,42,142
45,replace,4.0,C,5,O=C157c)O=S,O=C1C7c)O=S,11,replace 5 at position 4 with C,flow_matching,0.3,2.0,42,142
46,replace,5.0,C,7,O=C1C7c)O=S,O=C1CCc)O=S,11,replace 7 at position 5 with C,flow_matching,0.3,2.0,42,142
47,replace,4.0,7,C,O=C1CCc)O=S,O=C17Cc)O=S,11,replace C at position 4 with 7,flow_matching,0.3,2.0,42,142
48,remove,3.0,1,,O=C17Cc)O=S,O=C7Cc)O=S,10,remove 1 from position 3,flow_matching,0.3,2.0,42,142
49,replace,2.0,),C,O=C7Cc)O=S,O=)7Cc)O=S,10,replace C at position 2 with ),flow_matching,0.3,2.0,42,142
50,replace,4.0,-,C,O=)7Cc)O=S,O=)7-c)O=S,10,replace C at position 4 with -,flow_matching,0.3,2.0,42,142
51,replace,7.0,S,O,O=)7-c)O=S,O=)7-c)S=S,10,replace O at position 7 with S,flow_matching,0.3,2.0,42,142
52,replace,2.0,C,),O=)7-c)S=S,O=C7-c)S=S,10,replace ) at position 2 with C,flow_matching,0.3,2.0,42,142
53,add,0.0,I,,O=C7-c)S=S,IO=C7-c)S=S,11,add I at position 0,flow_matching,0.3,2.0,42,142
54,replace,7.0,1,),IO=C7-c)S=S,IO=C7-c1S=S,11,replace ) at position 7 with 1,flow_matching,0.3,2.0,42,142
55,replace,7.0,O,1,IO=C7-c1S=S,IO=C7-cOS=S,11,replace 1 at position 7 with O,flow_matching,0.3,2.0,42,142
56,add,4.0,F,,IO=C7-cOS=S,IO=CF7-cOS=S,12,add F at position 4,flow_matching,0.3,2.0,42,142
57,remove,7.0,c,,IO=CF7-cOS=S,IO=CF7-OS=S,11,remove c from position 7,flow_matching,0.3,2.0,42,142
58,remove,6.0,-,,IO=CF7-OS=S,IO=CF7OS=S,10,remove - from position 6,flow_matching,0.3,2.0,42,142
59,replace,0.0,c,I,IO=CF7OS=S,cO=CF7OS=S,10,replace I at position 0 with c,flow_matching,0.3,2.0,42,142
60,add,0.0,5,,cO=CF7OS=S,5cO=CF7OS=S,11,add 5 at position 0,flow_matching,0.3,2.0,42,142
61,replace,1.0,@,c,5cO=CF7OS=S,5@O=CF7OS=S,11,replace c at position 1 with @,flow_matching,0.3,2.0,42,142
62,add,5.0,s,,5@O=CF7OS=S,5@O=CsF7OS=S,12,add s at position 5,flow_matching,0.3,2.0,42,142
63,replace,0.0,O,5,5@O=CsF7OS=S,O@O=CsF7OS=S,12,replace 5 at position 0 with O,flow_matching,0.3,2.0,42,142
64,replace,6.0,3,F,O@O=CsF7OS=S,O@O=Cs37OS=S,12,replace F at position 6 with 3,flow_matching,0.3,2.0,42,142
65,add,6.0,1,,O@O=Cs37OS=S,O@O=Cs137OS=S,13,add 1 at position 6,flow_matching,0.3,2.0,42,142
66,replace,7.0,+,3,O@O=Cs137OS=S,O@O=Cs1+7OS=S,13,replace 3 at position 7 with +,flow_matching,0.3,2.0,42,142
67,remove,7.0,+,,O@O=Cs1+7OS=S,O@O=Cs17OS=S,12,remove + from position 7,flow_matching,0.3,2.0,42,142
68,remove,11.0,S,,O@O=Cs17OS=S,O@O=Cs17OS=,11,remove S from position 11,flow_matching,0.3,2.0,42,142
69,replace,1.0,=,@,O@O=Cs17OS=,O=O=Cs17OS=,11,replace @ at position 1 with =,flow_matching,0.3,2.0,42,142
70,replace,2.0,C,O,O=O=Cs17OS=,O=C=Cs17OS=,11,replace O at position 2 with C,flow_matching,0.3,2.0,42,142
71,replace,3.0,1,=,O=C=Cs17OS=,O=C1Cs17OS=,11,replace = at position 3 with 1,flow_matching,0.3,2.0,42,142
72,replace,5.0,C,s,O=C1Cs17OS=,O=C1CC17OS=,11,replace s at position 5 with C,flow_matching,0.3,2.0,42,142
73,replace,5.0,1,C,O=C1CC17OS=,O=C1C117OS=,11,replace C at position 5 with 1,flow_matching,0.3,2.0,42,142
74,replace,4.0,6,C,O=C1C117OS=,O=C16117OS=,11,replace C at position 4 with 6,flow_matching,0.3,2.0,42,142
75,remove,5.0,1,,O=C16117OS=,O=C1617OS=,10,remove 1 from position 5,flow_matching,0.3,2.0,42,142
76,replace,4.0,C,6,O=C1617OS=,O=C1C17OS=,10,replace 6 at position 4 with C,flow_matching,0.3,2.0,42,142
77,remove,8.0,S,,O=C1C17OS=,O=C1C17O=,9,remove S from position 8,flow_matching,0.3,2.0,42,142
78,replace,1.0,-,=,O=C1C17O=,O-C1C17O=,9,replace = at position 1 with -,flow_matching,0.3,2.0,42,142
79,remove,1.0,-,,O-C1C17O=,OC1C17O=,8,remove - from position 1,flow_matching,0.3,2.0,42,142
80,replace,1.0,H,C,OC1C17O=,OH1C17O=,8,replace C at position 1 with H,flow_matching,0.3,2.0,42,142
81,replace,1.0,=,H,OH1C17O=,O=1C17O=,8,replace H at position 1 with =,flow_matching,0.3,2.0,42,142
82,replace,2.0,C,1,O=1C17O=,O=CC17O=,8,replace 1 at position 2 with C,flow_matching,0.3,2.0,42,142
83,remove,5.0,7,,O=CC17O=,O=CC1O=,7,remove 7 from position 5,flow_matching,0.3,2.0,42,142
84,replace,3.0,1,C,O=CC1O=,O=C11O=,7,replace C at position 3 with 1,flow_matching,0.3,2.0,42,142
85,replace,4.0,C,1,O=C11O=,O=C1CO=,7,replace 1 at position 4 with C,flow_matching,0.3,2.0,42,142
86,replace,2.0,],C,O=C1CO=,O=]1CO=,7,replace C at position 2 with ],flow_matching,0.3,2.0,42,142
87,replace,1.0,H,=,O=]1CO=,OH]1CO=,7,replace = at position 1 with H,flow_matching,0.3,2.0,42,142
88,remove,3.0,1,,OH]1CO=,OH]CO=,6,remove 1 from position 3,flow_matching,0.3,2.0,42,142
89,replace,1.0,=,H,OH]CO=,O=]CO=,6,replace H at position 1 with =,flow_matching,0.3,2.0,42,142
90,replace,2.0,C,],O=]CO=,O=CCO=,6,replace ] at position 2 with C,flow_matching,0.3,2.0,42,142
91,add,0.0,o,,O=CCO=,oO=CCO=,7,add o at position 0,flow_matching,0.3,2.0,42,142
92,add,3.0,r,,oO=CCO=,oO=rCCO=,8,add r at position 3,flow_matching,0.3,2.0,42,142
93,replace,0.0,O,o,oO=rCCO=,OO=rCCO=,8,replace o at position 0 with O,flow_matching,0.3,2.0,42,142
94,remove,0.0,O,,OO=rCCO=,O=rCCO=,7,remove O from position 0,flow_matching,0.3,2.0,42,142
95,replace,6.0,#,=,O=rCCO=,O=rCCO#,7,replace = at position 6 with #,flow_matching,0.3,2.0,42,142
96,replace,2.0,C,r,O=rCCO#,O=CCCO#,7,replace r at position 2 with C,flow_matching,0.3,2.0,42,142
97,replace,3.0,1,C,O=CCCO#,O=C1CO#,7,replace C at position 3 with 1,flow_matching,0.3,2.0,42,142
98,add,6.0,#,,O=C1CO#,O=C1CO##,8,add # at position 6,flow_matching,0.3,2.0,42,142
99,remove,2.0,C,,O=C1CO##,O=1CO##,7,remove C from position 2,flow_matching,0.3,2.0,42,142
100,add,0.0,O,,O=1CO##,OO=1CO##,8,add O at position 0,flow_matching,0.3,2.0,42,142
101,add,4.0,F,,OO=1CO##,OO=1FCO##,9,add F at position 4,flow_matching,0.3,2.0,42,142
102,add,8.0,O,,OO=1FCO##,OO=1FCO#O#,10,add O at position 8,flow_matching,0.3,2.0,42,142
103,replace,4.0,B,F,OO=1FCO#O#,OO=1BCO#O#,10,replace F at position 4 with B,flow_matching,0.3,2.0,42,142
104,replace,1.0,=,O,OO=1BCO#O#,O==1BCO#O#,10,replace O at position 1 with =,flow_matching,0.3,2.0,42,142
105,replace,2.0,C,=,O==1BCO#O#,O=C1BCO#O#,10,replace = at position 2 with C,flow_matching,0.3,2.0,42,142
106,replace,4.0,C,B,O=C1BCO#O#,O=C1CCO#O#,10,replace B at position 4 with C,flow_matching,0.3,2.0,42,142
107,replace,6.0,[,O,O=C1CCO#O#,O=C1CC[#O#,10,replace O at position 6 with [,flow_matching,0.3,2.0,42,142
108,replace,7.0,C,#,O=C1CC[#O#,O=C1CC[CO#,10,replace # at position 7 with C,flow_matching,0.3,2.0,42,142
109,replace,8.0,@,O,O=C1CC[CO#,O=C1CC[C@#,10,replace O at position 8 with @,flow_matching,0.3,2.0,42,142
110,replace,9.0,@,#,O=C1CC[C@#,O=C1CC[C@@,10,replace # at position 9 with @,flow_matching,0.3,2.0,42,142
111,add,10.0,H,,O=C1CC[C@@,O=C1CC[C@@H,11,add H at position 10,flow_matching,0.3,2.0,42,142
112,add,11.0,],,O=C1CC[C@@H,O=C1CC[C@@H],12,add ] at position 11,flow_matching,0.3,2.0,42,142
113,add,12.0,(,,O=C1CC[C@@H],O=C1CC[C@@H](,13,add ( at position 12,flow_matching,0.3,2.0,42,142
114,add,13.0,N,,O=C1CC[C@@H](,O=C1CC[C@@H](N,14,add N at position 13,flow_matching,0.3,2.0,42,142
115,add,14.0,C,,O=C1CC[C@@H](N,O=C1CC[C@@H](NC,15,add C at position 14,flow_matching,0.3,2.0,42,142
116,add,15.0,(,,O=C1CC[C@@H](NC,O=C1CC[C@@H](NC(,16,add ( at position 15,flow_matching,0.3,2.0,42,142
117,add,16.0,=,,O=C1CC[C@@H](NC(,O=C1CC[C@@H](NC(=,17,add = at position 16,flow_matching,0.3,2.0,42,142
118,add,17.0,O,,O=C1CC[C@@H](NC(=,O=C1CC[C@@H](NC(=O,18,add O at position 17,flow_matching,0.3,2.0,42,142
119,add,18.0,),,O=C1CC[C@@H](NC(=O,O=C1CC[C@@H](NC(=O),19,add ) at position 18,flow_matching,0.3,2.0,42,142
120,add,19.0,C,,O=C1CC[C@@H](NC(=O),O=C1CC[C@@H](NC(=O)C,20,add C at position 19,flow_matching,0.3,2.0,42,142
121,add,20.0,O,,O=C1CC[C@@H](NC(=O)C,O=C1CC[C@@H](NC(=O)CO,21,add O at position 20,flow_matching,0.3,2.0,42,142
122,add,21.0,c,,O=C1CC[C@@H](NC(=O)CO,O=C1CC[C@@H](NC(=O)COc,22,add c at position 21,flow_matching,0.3,2.0,42,142
123,add,22.0,2,,O=C1CC[C@@H](NC(=O)COc,O=C1CC[C@@H](NC(=O)COc2,23,add 2 at position 22,flow_matching,0.3,2.0,42,142
124,add,23.0,c,,O=C1CC[C@@H](NC(=O)COc2,O=C1CC[C@@H](NC(=O)COc2c,24,add c at position 23,flow_matching,0.3,2.0,42,142
125,add,24.0,c,,O=C1CC[C@@H](NC(=O)COc2c,O=C1CC[C@@H](NC(=O)COc2cc,25,add c at position 24,flow_matching,0.3,2.0,42,142
126,add,25.0,c,,O=C1CC[C@@H](NC(=O)COc2cc,O=C1CC[C@@H](NC(=O)COc2ccc,26,add c at position 25,flow_matching,0.3,2.0,42,142
127,add,26.0,(,,O=C1CC[C@@H](NC(=O)COc2ccc,O=C1CC[C@@H](NC(=O)COc2ccc(,27,add ( at position 26,flow_matching,0.3,2.0,42,142
128,add,27.0,C,,O=C1CC[C@@H](NC(=O)COc2ccc(,O=C1CC[C@@H](NC(=O)COc2ccc(C,28,add C at position 27,flow_matching,0.3,2.0,42,142
129,add,28.0,l,,O=C1CC[C@@H](NC(=O)COc2ccc(C,O=C1CC[C@@H](NC(=O)COc2ccc(Cl,29,add l at position 28,flow_matching,0.3,2.0,42,142
130,add,29.0,),,O=C1CC[C@@H](NC(=O)COc2ccc(Cl,O=C1CC[C@@H](NC(=O)COc2ccc(Cl),30,add ) at position 29,flow_matching,0.3,2.0,42,142
131,add,30.0,c,,O=C1CC[C@@H](NC(=O)COc2ccc(Cl),O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c,31,add c at position 30,flow_matching,0.3,2.0,42,142
132,add,31.0,(,,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(,32,add ( at position 31,flow_matching,0.3,2.0,42,142
133,add,32.0,C,,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(C,33,add C at position 32,flow_matching,0.3,2.0,42,142
134,add,33.0,l,,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(C,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl,34,add l at position 33,flow_matching,0.3,2.0,42,142
135,add,34.0,),,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl),35,add ) at position 34,flow_matching,0.3,2.0,42,142
136,add,35.0,c,,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl),O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c,36,add c at position 35,flow_matching,0.3,2.0,42,142
137,add,36.0,2,,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2,37,add 2 at position 36,flow_matching,0.3,2.0,42,142
138,add,37.0,),,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2),38,add ) at position 37,flow_matching,0.3,2.0,42,142
139,add,38.0,C,,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2),O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2)C,39,add C at position 38,flow_matching,0.3,2.0,42,142
140,add,39.0,N,,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2)C,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2)CN,40,add N at position 39,flow_matching,0.3,2.0,42,142
141,add,40.0,1,,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2)CN,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2)CN1,41,add 1 at position 40,flow_matching,0.3,2.0,42,142
142,add,41.0,"
",,O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2)CN1,"O=C1CC[C@@H](NC(=O)COc2ccc(Cl)c(Cl)c2)CN1
",42,"add 
 at position 41",flow_matching,0.3,2.0,42,142
