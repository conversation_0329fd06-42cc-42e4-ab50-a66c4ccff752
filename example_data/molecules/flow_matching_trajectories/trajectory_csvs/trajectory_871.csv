step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,41,191
1,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,41,191
2,remove,0.0,I,,I,,0,remove I from position 0,flow_matching,0.3,2.0,41,191
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,41,191
4,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,41,191
5,add,2.0,S,,C[,C[S,3,add S at position 2,flow_matching,0.3,2.0,41,191
6,remove,0.0,C,,C[S,[S,2,remove C from position 0,flow_matching,0.3,2.0,41,191
7,replace,1.0,n,S,[S,[n,2,replace S at position 1 with n,flow_matching,0.3,2.0,41,191
8,replace,0.0,1,[,[n,1n,2,replace [ at position 0 with 1,flow_matching,0.3,2.0,41,191
9,remove,0.0,1,,1n,n,1,remove 1 from position 0,flow_matching,0.3,2.0,41,191
10,replace,0.0,C,n,n,C,1,replace n at position 0 with C,flow_matching,0.3,2.0,41,191
11,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,41,191
12,add,1.0,O,,C[,CO[,3,add O at position 1,flow_matching,0.3,2.0,41,191
13,replace,1.0,[,O,CO[,C[[,3,replace O at position 1 with [,flow_matching,0.3,2.0,41,191
14,remove,2.0,[,,C[[,C[,2,remove [ from position 2,flow_matching,0.3,2.0,41,191
15,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,41,191
16,remove,0.0,C,,C[C,[C,2,remove C from position 0,flow_matching,0.3,2.0,41,191
17,replace,0.0,n,[,[C,nC,2,replace [ at position 0 with n,flow_matching,0.3,2.0,41,191
18,add,1.0,7,,nC,n7C,3,add 7 at position 1,flow_matching,0.3,2.0,41,191
19,replace,0.0,6,n,n7C,67C,3,replace n at position 0 with 6,flow_matching,0.3,2.0,41,191
20,replace,0.0,C,6,67C,C7C,3,replace 6 at position 0 with C,flow_matching,0.3,2.0,41,191
21,replace,1.0,[,7,C7C,C[C,3,replace 7 at position 1 with [,flow_matching,0.3,2.0,41,191
22,add,2.0,(,,C[C,C[(C,4,add ( at position 2,flow_matching,0.3,2.0,41,191
23,replace,2.0,C,(,C[(C,C[CC,4,replace ( at position 2 with C,flow_matching,0.3,2.0,41,191
24,replace,3.0,@,C,C[CC,C[C@,4,replace C at position 3 with @,flow_matching,0.3,2.0,41,191
25,add,4.0,@,,C[C@,C[C@@,5,add @ at position 4,flow_matching,0.3,2.0,41,191
26,remove,2.0,C,,C[C@@,C[@@,4,remove C from position 2,flow_matching,0.3,2.0,41,191
27,add,2.0,N,,C[@@,C[N@@,5,add N at position 2,flow_matching,0.3,2.0,41,191
28,add,5.0,6,,C[N@@,C[N@@6,6,add 6 at position 5,flow_matching,0.3,2.0,41,191
29,remove,4.0,@,,C[N@@6,C[N@6,5,remove @ from position 4,flow_matching,0.3,2.0,41,191
30,remove,2.0,N,,C[N@6,C[@6,4,remove N from position 2,flow_matching,0.3,2.0,41,191
31,replace,1.0,2,[,C[@6,C2@6,4,replace [ at position 1 with 2,flow_matching,0.3,2.0,41,191
32,add,3.0,N,,C2@6,C2@N6,5,add N at position 3,flow_matching,0.3,2.0,41,191
33,replace,1.0,[,2,C2@N6,C[@N6,5,replace 2 at position 1 with [,flow_matching,0.3,2.0,41,191
34,remove,4.0,6,,C[@N6,C[@N,4,remove 6 from position 4,flow_matching,0.3,2.0,41,191
35,add,4.0,H,,C[@N,C[@NH,5,add H at position 4,flow_matching,0.3,2.0,41,191
36,add,3.0,r,,C[@NH,C[@rNH,6,add r at position 3,flow_matching,0.3,2.0,41,191
37,add,3.0,@,,C[@rNH,C[@@rNH,7,add @ at position 3,flow_matching,0.3,2.0,41,191
38,add,2.0,2,,C[@@rNH,C[2@@rNH,8,add 2 at position 2,flow_matching,0.3,2.0,41,191
39,replace,2.0,(,2,C[2@@rNH,C[(@@rNH,8,replace 2 at position 2 with (,flow_matching,0.3,2.0,41,191
40,add,6.0,N,,C[(@@rNH,C[(@@rNNH,9,add N at position 6,flow_matching,0.3,2.0,41,191
41,remove,7.0,N,,C[(@@rNNH,C[(@@rNH,8,remove N from position 7,flow_matching,0.3,2.0,41,191
42,add,0.0,I,,C[(@@rNH,IC[(@@rNH,9,add I at position 0,flow_matching,0.3,2.0,41,191
43,add,9.0,s,,IC[(@@rNH,IC[(@@rNHs,10,add s at position 9,flow_matching,0.3,2.0,41,191
44,replace,9.0,-,s,IC[(@@rNHs,IC[(@@rNH-,10,replace s at position 9 with -,flow_matching,0.3,2.0,41,191
45,replace,5.0,4,@,IC[(@@rNH-,IC[(@4rNH-,10,replace @ at position 5 with 4,flow_matching,0.3,2.0,41,191
46,remove,5.0,4,,IC[(@4rNH-,IC[(@rNH-,9,remove 4 from position 5,flow_matching,0.3,2.0,41,191
47,replace,3.0,1,(,IC[(@rNH-,IC[1@rNH-,9,replace ( at position 3 with 1,flow_matching,0.3,2.0,41,191
48,replace,0.0,C,I,IC[1@rNH-,CC[1@rNH-,9,replace I at position 0 with C,flow_matching,0.3,2.0,41,191
49,replace,1.0,4,C,CC[1@rNH-,C4[1@rNH-,9,replace C at position 1 with 4,flow_matching,0.3,2.0,41,191
50,add,2.0,n,,C4[1@rNH-,C4n[1@rNH-,10,add n at position 2,flow_matching,0.3,2.0,41,191
51,remove,5.0,@,,C4n[1@rNH-,C4n[1rNH-,9,remove @ from position 5,flow_matching,0.3,2.0,41,191
52,replace,1.0,[,4,C4n[1rNH-,C[n[1rNH-,9,replace 4 at position 1 with [,flow_matching,0.3,2.0,41,191
53,add,4.0,o,,C[n[1rNH-,C[n[o1rNH-,10,add o at position 4,flow_matching,0.3,2.0,41,191
54,add,7.0,/,,C[n[o1rNH-,C[n[o1r/NH-,11,add / at position 7,flow_matching,0.3,2.0,41,191
55,replace,2.0,C,n,C[n[o1r/NH-,C[C[o1r/NH-,11,replace n at position 2 with C,flow_matching,0.3,2.0,41,191
56,replace,5.0,+,1,C[C[o1r/NH-,C[C[o+r/NH-,11,replace 1 at position 5 with +,flow_matching,0.3,2.0,41,191
57,replace,3.0,@,[,C[C[o+r/NH-,C[C@o+r/NH-,11,replace [ at position 3 with @,flow_matching,0.3,2.0,41,191
58,replace,5.0,],+,C[C@o+r/NH-,C[C@o]r/NH-,11,replace + at position 5 with ],flow_matching,0.3,2.0,41,191
59,replace,4.0,@,o,C[C@o]r/NH-,C[C@@]r/NH-,11,replace o at position 4 with @,flow_matching,0.3,2.0,41,191
60,replace,5.0,H,],C[C@@]r/NH-,C[C@@Hr/NH-,11,replace ] at position 5 with H,flow_matching,0.3,2.0,41,191
61,remove,3.0,@,,C[C@@Hr/NH-,C[C@Hr/NH-,10,remove @ from position 3,flow_matching,0.3,2.0,41,191
62,replace,2.0,2,C,C[C@Hr/NH-,C[2@Hr/NH-,10,replace C at position 2 with 2,flow_matching,0.3,2.0,41,191
63,replace,2.0,C,2,C[2@Hr/NH-,C[C@Hr/NH-,10,replace 2 at position 2 with C,flow_matching,0.3,2.0,41,191
64,replace,4.0,@,H,C[C@Hr/NH-,C[C@@r/NH-,10,replace H at position 4 with @,flow_matching,0.3,2.0,41,191
65,remove,2.0,C,,C[C@@r/NH-,C[@@r/NH-,9,remove C from position 2,flow_matching,0.3,2.0,41,191
66,replace,7.0,s,H,C[@@r/NH-,C[@@r/Ns-,9,replace H at position 7 with s,flow_matching,0.3,2.0,41,191
67,remove,3.0,@,,C[@@r/Ns-,C[@r/Ns-,8,remove @ from position 3,flow_matching,0.3,2.0,41,191
68,remove,6.0,s,,C[@r/Ns-,C[@r/N-,7,remove s from position 6,flow_matching,0.3,2.0,41,191
69,replace,2.0,C,@,C[@r/N-,C[Cr/N-,7,replace @ at position 2 with C,flow_matching,0.3,2.0,41,191
70,remove,3.0,r,,C[Cr/N-,C[C/N-,6,remove r from position 3,flow_matching,0.3,2.0,41,191
71,add,0.0,/,,C[C/N-,/C[C/N-,7,add / at position 0,flow_matching,0.3,2.0,41,191
72,add,6.0,),,/C[C/N-,/C[C/N)-,8,add ) at position 6,flow_matching,0.3,2.0,41,191
73,replace,6.0,[,),/C[C/N)-,/C[C/N[-,8,replace ) at position 6 with [,flow_matching,0.3,2.0,41,191
74,replace,0.0,C,/,/C[C/N[-,CC[C/N[-,8,replace / at position 0 with C,flow_matching,0.3,2.0,41,191
75,replace,2.0,1,[,CC[C/N[-,CC1C/N[-,8,replace [ at position 2 with 1,flow_matching,0.3,2.0,41,191
76,add,2.0,C,,CC1C/N[-,CCC1C/N[-,9,add C at position 2,flow_matching,0.3,2.0,41,191
77,replace,1.0,[,C,CCC1C/N[-,C[C1C/N[-,9,replace C at position 1 with [,flow_matching,0.3,2.0,41,191
78,replace,3.0,@,1,C[C1C/N[-,C[C@C/N[-,9,replace 1 at position 3 with @,flow_matching,0.3,2.0,41,191
79,add,1.0,1,,C[C@C/N[-,C1[C@C/N[-,10,add 1 at position 1,flow_matching,0.3,2.0,41,191
80,add,5.0,C,,C1[C@C/N[-,C1[C@CC/N[-,11,add C at position 5,flow_matching,0.3,2.0,41,191
81,add,9.0,),,C1[C@CC/N[-,C1[C@CC/N)[-,12,add ) at position 9,flow_matching,0.3,2.0,41,191
82,remove,8.0,N,,C1[C@CC/N)[-,C1[C@CC/)[-,11,remove N from position 8,flow_matching,0.3,2.0,41,191
83,replace,1.0,[,1,C1[C@CC/)[-,C[[C@CC/)[-,11,replace 1 at position 1 with [,flow_matching,0.3,2.0,41,191
84,remove,6.0,C,,C[[C@CC/)[-,C[[C@C/)[-,10,remove C from position 6,flow_matching,0.3,2.0,41,191
85,add,10.0,N,,C[[C@C/)[-,C[[C@C/)[-N,11,add N at position 10,flow_matching,0.3,2.0,41,191
86,replace,2.0,C,[,C[[C@C/)[-N,C[CC@C/)[-N,11,replace [ at position 2 with C,flow_matching,0.3,2.0,41,191
87,replace,3.0,@,C,C[CC@C/)[-N,C[C@@C/)[-N,11,replace C at position 3 with @,flow_matching,0.3,2.0,41,191
88,replace,5.0,H,C,C[C@@C/)[-N,C[C@@H/)[-N,11,replace C at position 5 with H,flow_matching,0.3,2.0,41,191
89,replace,6.0,],/,C[C@@H/)[-N,C[C@@H])[-N,11,replace / at position 6 with ],flow_matching,0.3,2.0,41,191
90,replace,4.0,=,@,C[C@@H])[-N,C[C@=H])[-N,11,replace @ at position 4 with =,flow_matching,0.3,2.0,41,191
91,replace,4.0,@,=,C[C@=H])[-N,C[C@@H])[-N,11,replace = at position 4 with @,flow_matching,0.3,2.0,41,191
92,replace,7.0,(,),C[C@@H])[-N,C[C@@H]([-N,11,replace ) at position 7 with (,flow_matching,0.3,2.0,41,191
93,add,1.0,l,,C[C@@H]([-N,Cl[C@@H]([-N,12,add l at position 1,flow_matching,0.3,2.0,41,191
94,replace,11.0,6,N,Cl[C@@H]([-N,Cl[C@@H]([-6,12,replace N at position 11 with 6,flow_matching,0.3,2.0,41,191
95,replace,1.0,[,l,Cl[C@@H]([-6,C[[C@@H]([-6,12,replace l at position 1 with [,flow_matching,0.3,2.0,41,191
96,add,8.0,n,,C[[C@@H]([-6,C[[C@@H]n([-6,13,add n at position 8,flow_matching,0.3,2.0,41,191
97,remove,5.0,@,,C[[C@@H]n([-6,C[[C@H]n([-6,12,remove @ from position 5,flow_matching,0.3,2.0,41,191
98,replace,2.0,C,[,C[[C@H]n([-6,C[CC@H]n([-6,12,replace [ at position 2 with C,flow_matching,0.3,2.0,41,191
99,replace,7.0,o,n,C[CC@H]n([-6,C[CC@H]o([-6,12,replace n at position 7 with o,flow_matching,0.3,2.0,41,191
100,replace,3.0,],C,C[CC@H]o([-6,C[C]@H]o([-6,12,replace C at position 3 with ],flow_matching,0.3,2.0,41,191
101,replace,11.0,c,6,C[C]@H]o([-6,C[C]@H]o([-c,12,replace 6 at position 11 with c,flow_matching,0.3,2.0,41,191
102,replace,3.0,@,],C[C]@H]o([-c,C[C@@H]o([-c,12,replace ] at position 3 with @,flow_matching,0.3,2.0,41,191
103,remove,4.0,@,,C[C@@H]o([-c,C[C@H]o([-c,11,remove @ from position 4,flow_matching,0.3,2.0,41,191
104,add,2.0,l,,C[C@H]o([-c,C[lC@H]o([-c,12,add l at position 2,flow_matching,0.3,2.0,41,191
105,add,7.0,s,,C[lC@H]o([-c,C[lC@H]so([-c,13,add s at position 7,flow_matching,0.3,2.0,41,191
106,replace,2.0,C,l,C[lC@H]so([-c,C[CC@H]so([-c,13,replace l at position 2 with C,flow_matching,0.3,2.0,41,191
107,add,1.0,r,,C[CC@H]so([-c,Cr[CC@H]so([-c,14,add r at position 1,flow_matching,0.3,2.0,41,191
108,replace,0.0,c,C,Cr[CC@H]so([-c,cr[CC@H]so([-c,14,replace C at position 0 with c,flow_matching,0.3,2.0,41,191
109,replace,0.0,C,c,cr[CC@H]so([-c,Cr[CC@H]so([-c,14,replace c at position 0 with C,flow_matching,0.3,2.0,41,191
110,replace,2.0,l,[,Cr[CC@H]so([-c,CrlCC@H]so([-c,14,replace [ at position 2 with l,flow_matching,0.3,2.0,41,191
111,replace,1.0,[,r,CrlCC@H]so([-c,C[lCC@H]so([-c,14,replace r at position 1 with [,flow_matching,0.3,2.0,41,191
112,replace,13.0,o,c,C[lCC@H]so([-c,C[lCC@H]so([-o,14,replace c at position 13 with o,flow_matching,0.3,2.0,41,191
113,remove,6.0,H,,C[lCC@H]so([-o,C[lCC@]so([-o,13,remove H from position 6,flow_matching,0.3,2.0,41,191
114,add,8.0,3,,C[lCC@]so([-o,C[lCC@]s3o([-o,14,add 3 at position 8,flow_matching,0.3,2.0,41,191
115,replace,2.0,C,l,C[lCC@]s3o([-o,C[CCC@]s3o([-o,14,replace l at position 2 with C,flow_matching,0.3,2.0,41,191
116,replace,8.0,5,3,C[CCC@]s3o([-o,C[CCC@]s5o([-o,14,replace 3 at position 8 with 5,flow_matching,0.3,2.0,41,191
117,remove,4.0,C,,C[CCC@]s5o([-o,C[CC@]s5o([-o,13,remove C from position 4,flow_matching,0.3,2.0,41,191
118,replace,7.0,4,5,C[CC@]s5o([-o,C[CC@]s4o([-o,13,replace 5 at position 7 with 4,flow_matching,0.3,2.0,41,191
119,replace,4.0,],@,C[CC@]s4o([-o,C[CC]]s4o([-o,13,replace @ at position 4 with ],flow_matching,0.3,2.0,41,191
120,replace,3.0,@,C,C[CC]]s4o([-o,C[C@]]s4o([-o,13,replace C at position 3 with @,flow_matching,0.3,2.0,41,191
121,add,2.0,s,,C[C@]]s4o([-o,C[sC@]]s4o([-o,14,add s at position 2,flow_matching,0.3,2.0,41,191
122,add,14.0,(,,C[sC@]]s4o([-o,C[sC@]]s4o([-o(,15,add ( at position 14,flow_matching,0.3,2.0,41,191
123,add,0.0,6,,C[sC@]]s4o([-o(,6C[sC@]]s4o([-o(,16,add 6 at position 0,flow_matching,0.3,2.0,41,191
124,replace,11.0,N,(,6C[sC@]]s4o([-o(,6C[sC@]]s4oN[-o(,16,replace ( at position 11 with N,flow_matching,0.3,2.0,41,191
125,replace,0.0,C,6,6C[sC@]]s4oN[-o(,CC[sC@]]s4oN[-o(,16,replace 6 at position 0 with C,flow_matching,0.3,2.0,41,191
126,replace,1.0,[,C,CC[sC@]]s4oN[-o(,C[[sC@]]s4oN[-o(,16,replace C at position 1 with [,flow_matching,0.3,2.0,41,191
127,add,13.0,(,,C[[sC@]]s4oN[-o(,C[[sC@]]s4oN[(-o(,17,add ( at position 13,flow_matching,0.3,2.0,41,191
128,remove,12.0,[,,C[[sC@]]s4oN[(-o(,C[[sC@]]s4oN(-o(,16,remove [ from position 12,flow_matching,0.3,2.0,41,191
129,replace,2.0,C,[,C[[sC@]]s4oN(-o(,C[CsC@]]s4oN(-o(,16,replace [ at position 2 with C,flow_matching,0.3,2.0,41,191
130,replace,3.0,@,s,C[CsC@]]s4oN(-o(,C[C@C@]]s4oN(-o(,16,replace s at position 3 with @,flow_matching,0.3,2.0,41,191
131,add,13.0,/,,C[C@C@]]s4oN(-o(,C[C@C@]]s4oN(/-o(,17,add / at position 13,flow_matching,0.3,2.0,41,191
132,replace,4.0,@,C,C[C@C@]]s4oN(/-o(,C[C@@@]]s4oN(/-o(,17,replace C at position 4 with @,flow_matching,0.3,2.0,41,191
133,remove,1.0,[,,C[C@@@]]s4oN(/-o(,CC@@@]]s4oN(/-o(,16,remove [ from position 1,flow_matching,0.3,2.0,41,191
134,add,10.0,S,,CC@@@]]s4oN(/-o(,CC@@@]]s4oSN(/-o(,17,add S at position 10,flow_matching,0.3,2.0,41,191
135,add,15.0,S,,CC@@@]]s4oSN(/-o(,CC@@@]]s4oSN(/-So(,18,add S at position 15,flow_matching,0.3,2.0,41,191
136,replace,5.0,@,],CC@@@]]s4oSN(/-So(,CC@@@@]s4oSN(/-So(,18,replace ] at position 5 with @,flow_matching,0.3,2.0,41,191
137,replace,1.0,[,C,CC@@@@]s4oSN(/-So(,C[@@@@]s4oSN(/-So(,18,replace C at position 1 with [,flow_matching,0.3,2.0,41,191
138,replace,3.0,H,@,C[@@@@]s4oSN(/-So(,C[@H@@]s4oSN(/-So(,18,replace @ at position 3 with H,flow_matching,0.3,2.0,41,191
139,replace,2.0,C,@,C[@H@@]s4oSN(/-So(,C[CH@@]s4oSN(/-So(,18,replace @ at position 2 with C,flow_matching,0.3,2.0,41,191
140,replace,3.0,@,H,C[CH@@]s4oSN(/-So(,C[C@@@]s4oSN(/-So(,18,replace H at position 3 with @,flow_matching,0.3,2.0,41,191
141,replace,15.0,o,S,C[C@@@]s4oSN(/-So(,C[C@@@]s4oSN(/-oo(,18,replace S at position 15 with o,flow_matching,0.3,2.0,41,191
142,replace,5.0,H,@,C[C@@@]s4oSN(/-oo(,C[C@@H]s4oSN(/-oo(,18,replace @ at position 5 with H,flow_matching,0.3,2.0,41,191
143,replace,11.0,r,N,C[C@@H]s4oSN(/-oo(,C[C@@H]s4oSr(/-oo(,18,replace N at position 11 with r,flow_matching,0.3,2.0,41,191
144,replace,7.0,(,s,C[C@@H]s4oSr(/-oo(,C[C@@H](4oSr(/-oo(,18,replace s at position 7 with (,flow_matching,0.3,2.0,41,191
145,replace,8.0,O,4,C[C@@H](4oSr(/-oo(,C[C@@H](OoSr(/-oo(,18,replace 4 at position 8 with O,flow_matching,0.3,2.0,41,191
146,add,18.0,o,,C[C@@H](OoSr(/-oo(,C[C@@H](OoSr(/-oo(o,19,add o at position 18,flow_matching,0.3,2.0,41,191
147,add,2.0,-,,C[C@@H](OoSr(/-oo(o,C[-C@@H](OoSr(/-oo(o,20,add - at position 2,flow_matching,0.3,2.0,41,191
148,add,9.0,s,,C[-C@@H](OoSr(/-oo(o,C[-C@@H](sOoSr(/-oo(o,21,add s at position 9,flow_matching,0.3,2.0,41,191
149,add,19.0,[,,C[-C@@H](sOoSr(/-oo(o,C[-C@@H](sOoSr(/-oo[(o,22,add [ at position 19,flow_matching,0.3,2.0,41,191
150,replace,2.0,C,-,C[-C@@H](sOoSr(/-oo[(o,C[CC@@H](sOoSr(/-oo[(o,22,replace - at position 2 with C,flow_matching,0.3,2.0,41,191
151,add,7.0,c,,C[CC@@H](sOoSr(/-oo[(o,C[CC@@Hc](sOoSr(/-oo[(o,23,add c at position 7,flow_matching,0.3,2.0,41,191
152,add,14.0,6,,C[CC@@Hc](sOoSr(/-oo[(o,C[CC@@Hc](sOoS6r(/-oo[(o,24,add 6 at position 14,flow_matching,0.3,2.0,41,191
153,replace,1.0,),[,C[CC@@Hc](sOoS6r(/-oo[(o,C)CC@@Hc](sOoS6r(/-oo[(o,24,replace [ at position 1 with ),flow_matching,0.3,2.0,41,191
154,replace,1.0,[,),C)CC@@Hc](sOoS6r(/-oo[(o,C[CC@@Hc](sOoS6r(/-oo[(o,24,replace ) at position 1 with [,flow_matching,0.3,2.0,41,191
155,replace,3.0,@,C,C[CC@@Hc](sOoS6r(/-oo[(o,C[C@@@Hc](sOoS6r(/-oo[(o,24,replace C at position 3 with @,flow_matching,0.3,2.0,41,191
156,replace,5.0,H,@,C[C@@@Hc](sOoS6r(/-oo[(o,C[C@@HHc](sOoS6r(/-oo[(o,24,replace @ at position 5 with H,flow_matching,0.3,2.0,41,191
157,replace,6.0,],H,C[C@@HHc](sOoS6r(/-oo[(o,C[C@@H]c](sOoS6r(/-oo[(o,24,replace H at position 6 with ],flow_matching,0.3,2.0,41,191
158,replace,7.0,(,c,C[C@@H]c](sOoS6r(/-oo[(o,C[C@@H](](sOoS6r(/-oo[(o,24,replace c at position 7 with (,flow_matching,0.3,2.0,41,191
159,replace,8.0,O,],C[C@@H](](sOoS6r(/-oo[(o,C[C@@H](O(sOoS6r(/-oo[(o,24,replace ] at position 8 with O,flow_matching,0.3,2.0,41,191
160,replace,9.0,),(,C[C@@H](O(sOoS6r(/-oo[(o,C[C@@H](O)sOoS6r(/-oo[(o,24,replace ( at position 9 with ),flow_matching,0.3,2.0,41,191
161,replace,10.0,c,s,C[C@@H](O)sOoS6r(/-oo[(o,C[C@@H](O)cOoS6r(/-oo[(o,24,replace s at position 10 with c,flow_matching,0.3,2.0,41,191
162,replace,11.0,1,O,C[C@@H](O)cOoS6r(/-oo[(o,C[C@@H](O)c1oS6r(/-oo[(o,24,replace O at position 11 with 1,flow_matching,0.3,2.0,41,191
163,replace,12.0,c,o,C[C@@H](O)c1oS6r(/-oo[(o,C[C@@H](O)c1cS6r(/-oo[(o,24,replace o at position 12 with c,flow_matching,0.3,2.0,41,191
164,replace,13.0,c,S,C[C@@H](O)c1cS6r(/-oo[(o,C[C@@H](O)c1cc6r(/-oo[(o,24,replace S at position 13 with c,flow_matching,0.3,2.0,41,191
165,replace,14.0,c,6,C[C@@H](O)c1cc6r(/-oo[(o,C[C@@H](O)c1cccr(/-oo[(o,24,replace 6 at position 14 with c,flow_matching,0.3,2.0,41,191
166,replace,15.0,(,r,C[C@@H](O)c1cccr(/-oo[(o,C[C@@H](O)c1ccc((/-oo[(o,24,replace r at position 15 with (,flow_matching,0.3,2.0,41,191
167,replace,16.0,F,(,C[C@@H](O)c1ccc((/-oo[(o,C[C@@H](O)c1ccc(F/-oo[(o,24,replace ( at position 16 with F,flow_matching,0.3,2.0,41,191
168,replace,17.0,),/,C[C@@H](O)c1ccc(F/-oo[(o,C[C@@H](O)c1ccc(F)-oo[(o,24,replace / at position 17 with ),flow_matching,0.3,2.0,41,191
169,replace,18.0,c,-,C[C@@H](O)c1ccc(F)-oo[(o,C[C@@H](O)c1ccc(F)coo[(o,24,replace - at position 18 with c,flow_matching,0.3,2.0,41,191
170,replace,19.0,c,o,C[C@@H](O)c1ccc(F)coo[(o,C[C@@H](O)c1ccc(F)cco[(o,24,replace o at position 19 with c,flow_matching,0.3,2.0,41,191
171,replace,20.0,1,o,C[C@@H](O)c1ccc(F)cco[(o,C[C@@H](O)c1ccc(F)cc1[(o,24,replace o at position 20 with 1,flow_matching,0.3,2.0,41,191
172,replace,21.0,O,[,C[C@@H](O)c1ccc(F)cc1[(o,C[C@@H](O)c1ccc(F)cc1O(o,24,replace [ at position 21 with O,flow_matching,0.3,2.0,41,191
173,replace,22.0,C,(,C[C@@H](O)c1ccc(F)cc1O(o,C[C@@H](O)c1ccc(F)cc1OCo,24,replace ( at position 22 with C,flow_matching,0.3,2.0,41,191
174,replace,23.0,c,o,C[C@@H](O)c1ccc(F)cc1OCo,C[C@@H](O)c1ccc(F)cc1OCc,24,replace o at position 23 with c,flow_matching,0.3,2.0,41,191
175,add,24.0,1,,C[C@@H](O)c1ccc(F)cc1OCc,C[C@@H](O)c1ccc(F)cc1OCc1,25,add 1 at position 24,flow_matching,0.3,2.0,41,191
176,add,25.0,n,,C[C@@H](O)c1ccc(F)cc1OCc1,C[C@@H](O)c1ccc(F)cc1OCc1n,26,add n at position 25,flow_matching,0.3,2.0,41,191
177,add,26.0,c,,C[C@@H](O)c1ccc(F)cc1OCc1n,C[C@@H](O)c1ccc(F)cc1OCc1nc,27,add c at position 26,flow_matching,0.3,2.0,41,191
178,add,27.0,(,,C[C@@H](O)c1ccc(F)cc1OCc1nc,C[C@@H](O)c1ccc(F)cc1OCc1nc(,28,add ( at position 27,flow_matching,0.3,2.0,41,191
179,add,28.0,C,,C[C@@H](O)c1ccc(F)cc1OCc1nc(,C[C@@H](O)c1ccc(F)cc1OCc1nc(C,29,add C at position 28,flow_matching,0.3,2.0,41,191
180,add,29.0,(,,C[C@@H](O)c1ccc(F)cc1OCc1nc(C,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(,30,add ( at position 29,flow_matching,0.3,2.0,41,191
181,add,30.0,C,,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C,31,add C at position 30,flow_matching,0.3,2.0,41,191
182,add,31.0,),,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C),32,add ) at position 31,flow_matching,0.3,2.0,41,191
183,add,32.0,(,,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C),C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(,33,add ( at position 32,flow_matching,0.3,2.0,41,191
184,add,33.0,C,,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C,34,add C at position 33,flow_matching,0.3,2.0,41,191
185,add,34.0,),,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C),35,add ) at position 34,flow_matching,0.3,2.0,41,191
186,add,35.0,C,,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C),C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C,36,add C at position 35,flow_matching,0.3,2.0,41,191
187,add,36.0,),,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C),37,add ) at position 36,flow_matching,0.3,2.0,41,191
188,add,37.0,c,,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C),C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C)c,38,add c at position 37,flow_matching,0.3,2.0,41,191
189,add,38.0,s,,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C)c,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C)cs,39,add s at position 38,flow_matching,0.3,2.0,41,191
190,add,39.0,1,,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C)cs,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C)cs1,40,add 1 at position 39,flow_matching,0.3,2.0,41,191
191,add,40.0,"
",,C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C)cs1,"C[C@@H](O)c1ccc(F)cc1OCc1nc(C(C)(C)C)cs1
",41,"add 
 at position 40",flow_matching,0.3,2.0,41,191
