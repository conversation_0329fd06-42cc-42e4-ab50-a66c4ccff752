step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,48,145
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,48,145
2,add,1.0,B,,C,CB,2,add B at position 1,flow_matching,0.3,2.0,48,145
3,add,1.0,=,,CB,C=B,3,add = at position 1,flow_matching,0.3,2.0,48,145
4,replace,1.0,7,=,C=B,C7B,3,replace = at position 1 with 7,flow_matching,0.3,2.0,48,145
5,replace,2.0,I,B,C7B,C7I,3,replace B at position 2 with I,flow_matching,0.3,2.0,48,145
6,replace,2.0,r,<PERSON>,<PERSON>7<PERSON>,C7r,3,replace I at position 2 with r,flow_matching,0.3,2.0,48,145
7,remove,2.0,r,,C7r,C7,2,remove r from position 2,flow_matching,0.3,2.0,48,145
8,replace,1.0,<PERSON>,7,C7,<PERSON>,2,replace 7 at position 1 with C,flow_matching,0.3,2.0,48,145
9,add,2.0,[,,CC,CC[,3,add [ at position 2,flow_matching,0.3,2.0,48,145
10,remove,2.0,[,,CC[,CC,2,remove [ from position 2,flow_matching,0.3,2.0,48,145
11,add,2.0,C,,CC,CCC,3,add C at position 2,flow_matching,0.3,2.0,48,145
12,replace,2.0,@,C,CCC,CC@,3,replace C at position 2 with @,flow_matching,0.3,2.0,48,145
13,remove,0.0,C,,CC@,C@,2,remove C from position 0,flow_matching,0.3,2.0,48,145
14,replace,0.0,=,C,C@,=@,2,replace C at position 0 with =,flow_matching,0.3,2.0,48,145
15,replace,0.0,C,=,=@,C@,2,replace = at position 0 with C,flow_matching,0.3,2.0,48,145
16,add,0.0,2,,C@,2C@,3,add 2 at position 0,flow_matching,0.3,2.0,48,145
17,replace,0.0,5,2,2C@,5C@,3,replace 2 at position 0 with 5,flow_matching,0.3,2.0,48,145
18,replace,1.0,1,C,5C@,51@,3,replace C at position 1 with 1,flow_matching,0.3,2.0,48,145
19,replace,0.0,=,5,51@,=1@,3,replace 5 at position 0 with =,flow_matching,0.3,2.0,48,145
20,replace,0.0,C,=,=1@,C1@,3,replace = at position 0 with C,flow_matching,0.3,2.0,48,145
21,add,2.0,H,,C1@,C1H@,4,add H at position 2,flow_matching,0.3,2.0,48,145
22,replace,1.0,C,1,C1H@,CCH@,4,replace 1 at position 1 with C,flow_matching,0.3,2.0,48,145
23,remove,2.0,H,,CCH@,CC@,3,remove H from position 2,flow_matching,0.3,2.0,48,145
24,replace,2.0,[,@,CC@,CC[,3,replace @ at position 2 with [,flow_matching,0.3,2.0,48,145
25,add,0.0,N,,CC[,NCC[,4,add N at position 0,flow_matching,0.3,2.0,48,145
26,replace,0.0,=,N,NCC[,=CC[,4,replace N at position 0 with =,flow_matching,0.3,2.0,48,145
27,replace,1.0,c,C,=CC[,=cC[,4,replace C at position 1 with c,flow_matching,0.3,2.0,48,145
28,add,3.0,7,,=cC[,=cC7[,5,add 7 at position 3,flow_matching,0.3,2.0,48,145
29,add,3.0,],,=cC7[,=cC]7[,6,add ] at position 3,flow_matching,0.3,2.0,48,145
30,replace,3.0,#,],=cC]7[,=cC#7[,6,replace ] at position 3 with #,flow_matching,0.3,2.0,48,145
31,add,6.0,+,,=cC#7[,=cC#7[+,7,add + at position 6,flow_matching,0.3,2.0,48,145
32,replace,0.0,C,=,=cC#7[+,CcC#7[+,7,replace = at position 0 with C,flow_matching,0.3,2.0,48,145
33,add,6.0,o,,CcC#7[+,CcC#7[o+,8,add o at position 6,flow_matching,0.3,2.0,48,145
34,replace,1.0,C,c,CcC#7[o+,CCC#7[o+,8,replace c at position 1 with C,flow_matching,0.3,2.0,48,145
35,remove,0.0,C,,CCC#7[o+,CC#7[o+,7,remove C from position 0,flow_matching,0.3,2.0,48,145
36,remove,2.0,#,,CC#7[o+,CC7[o+,6,remove # from position 2,flow_matching,0.3,2.0,48,145
37,replace,2.0,F,7,CC7[o+,CCF[o+,6,replace 7 at position 2 with F,flow_matching,0.3,2.0,48,145
38,remove,4.0,o,,CCF[o+,CCF[+,5,remove o from position 4,flow_matching,0.3,2.0,48,145
39,replace,2.0,[,F,CCF[+,CC[[+,5,replace F at position 2 with [,flow_matching,0.3,2.0,48,145
40,add,4.0,s,,CC[[+,CC[[s+,6,add s at position 4,flow_matching,0.3,2.0,48,145
41,replace,3.0,C,[,CC[[s+,CC[Cs+,6,replace [ at position 3 with C,flow_matching,0.3,2.0,48,145
42,remove,5.0,+,,CC[Cs+,CC[Cs,5,remove + from position 5,flow_matching,0.3,2.0,48,145
43,replace,0.0,N,C,CC[Cs,NC[Cs,5,replace C at position 0 with N,flow_matching,0.3,2.0,48,145
44,replace,0.0,C,N,NC[Cs,CC[Cs,5,replace N at position 0 with C,flow_matching,0.3,2.0,48,145
45,remove,3.0,C,,CC[Cs,CC[s,4,remove C from position 3,flow_matching,0.3,2.0,48,145
46,add,3.0,1,,CC[s,CC[1s,5,add 1 at position 3,flow_matching,0.3,2.0,48,145
47,add,5.0,],,CC[1s,CC[1s],6,add ] at position 5,flow_matching,0.3,2.0,48,145
48,remove,4.0,s,,CC[1s],CC[1],5,remove s from position 4,flow_matching,0.3,2.0,48,145
49,replace,3.0,C,1,CC[1],CC[C],5,replace 1 at position 3 with C,flow_matching,0.3,2.0,48,145
50,replace,3.0,),C,CC[C],CC[)],5,replace C at position 3 with ),flow_matching,0.3,2.0,48,145
51,remove,1.0,C,,CC[)],C[)],4,remove C from position 1,flow_matching,0.3,2.0,48,145
52,add,2.0,n,,C[)],C[n)],5,add n at position 2,flow_matching,0.3,2.0,48,145
53,replace,1.0,C,[,C[n)],CCn)],5,replace [ at position 1 with C,flow_matching,0.3,2.0,48,145
54,remove,3.0,),,CCn)],CCn],4,remove ) from position 3,flow_matching,0.3,2.0,48,145
55,add,4.0,r,,CCn],CCn]r,5,add r at position 4,flow_matching,0.3,2.0,48,145
56,replace,1.0,3,C,CCn]r,C3n]r,5,replace C at position 1 with 3,flow_matching,0.3,2.0,48,145
57,replace,1.0,C,3,C3n]r,CCn]r,5,replace 3 at position 1 with C,flow_matching,0.3,2.0,48,145
58,replace,2.0,[,n,CCn]r,CC[]r,5,replace n at position 2 with [,flow_matching,0.3,2.0,48,145
59,remove,1.0,C,,CC[]r,C[]r,4,remove C from position 1,flow_matching,0.3,2.0,48,145
60,replace,1.0,C,[,C[]r,CC]r,4,replace [ at position 1 with C,flow_matching,0.3,2.0,48,145
61,replace,3.0,],r,CC]r,CC]],4,replace r at position 3 with ],flow_matching,0.3,2.0,48,145
62,replace,2.0,I,],CC]],CCI],4,replace ] at position 2 with I,flow_matching,0.3,2.0,48,145
63,replace,3.0,r,],CCI],CCIr,4,replace ] at position 3 with r,flow_matching,0.3,2.0,48,145
64,add,3.0,C,,CCIr,CCICr,5,add C at position 3,flow_matching,0.3,2.0,48,145
65,replace,2.0,[,I,CCICr,CC[Cr,5,replace I at position 2 with [,flow_matching,0.3,2.0,48,145
66,remove,4.0,r,,CC[Cr,CC[C,4,remove r from position 4,flow_matching,0.3,2.0,48,145
67,add,0.0,F,,CC[C,FCC[C,5,add F at position 0,flow_matching,0.3,2.0,48,145
68,add,4.0,l,,FCC[C,FCC[lC,6,add l at position 4,flow_matching,0.3,2.0,48,145
69,remove,0.0,F,,FCC[lC,CC[lC,5,remove F from position 0,flow_matching,0.3,2.0,48,145
70,replace,3.0,C,l,CC[lC,CC[CC,5,replace l at position 3 with C,flow_matching,0.3,2.0,48,145
71,replace,2.0,F,[,CC[CC,CCFCC,5,replace [ at position 2 with F,flow_matching,0.3,2.0,48,145
72,replace,1.0,s,C,CCFCC,CsFCC,5,replace C at position 1 with s,flow_matching,0.3,2.0,48,145
73,remove,0.0,C,,CsFCC,sFCC,4,remove C from position 0,flow_matching,0.3,2.0,48,145
74,replace,0.0,=,s,sFCC,=FCC,4,replace s at position 0 with =,flow_matching,0.3,2.0,48,145
75,remove,0.0,=,,=FCC,FCC,3,remove = from position 0,flow_matching,0.3,2.0,48,145
76,add,2.0,c,,FCC,FCcC,4,add c at position 2,flow_matching,0.3,2.0,48,145
77,replace,0.0,C,F,FCcC,CCcC,4,replace F at position 0 with C,flow_matching,0.3,2.0,48,145
78,replace,2.0,[,c,CCcC,CC[C,4,replace c at position 2 with [,flow_matching,0.3,2.0,48,145
79,add,4.0,#,,CC[C,CC[C#,5,add # at position 4,flow_matching,0.3,2.0,48,145
80,replace,4.0,@,#,CC[C#,CC[C@,5,replace # at position 4 with @,flow_matching,0.3,2.0,48,145
81,replace,1.0,+,C,CC[C@,C+[C@,5,replace C at position 1 with +,flow_matching,0.3,2.0,48,145
82,add,4.0,),,C+[C@,C+[C)@,6,add ) at position 4,flow_matching,0.3,2.0,48,145
83,replace,1.0,C,+,C+[C)@,CC[C)@,6,replace + at position 1 with C,flow_matching,0.3,2.0,48,145
84,replace,4.0,@,),CC[C)@,CC[C@@,6,replace ) at position 4 with @,flow_matching,0.3,2.0,48,145
85,add,1.0,o,,CC[C@@,CoC[C@@,7,add o at position 1,flow_matching,0.3,2.0,48,145
86,add,3.0,-,,CoC[C@@,CoC-[C@@,8,add - at position 3,flow_matching,0.3,2.0,48,145
87,replace,2.0,l,C,CoC-[C@@,Col-[C@@,8,replace C at position 2 with l,flow_matching,0.3,2.0,48,145
88,replace,1.0,C,o,Col-[C@@,CCl-[C@@,8,replace o at position 1 with C,flow_matching,0.3,2.0,48,145
89,replace,2.0,[,l,CCl-[C@@,CC[-[C@@,8,replace l at position 2 with [,flow_matching,0.3,2.0,48,145
90,replace,3.0,C,-,CC[-[C@@,CC[C[C@@,8,replace - at position 3 with C,flow_matching,0.3,2.0,48,145
91,remove,0.0,C,,CC[C[C@@,C[C[C@@,7,remove C from position 0,flow_matching,0.3,2.0,48,145
92,add,4.0,+,,C[C[C@@,C[C[+C@@,8,add + at position 4,flow_matching,0.3,2.0,48,145
93,replace,1.0,),[,C[C[+C@@,C)C[+C@@,8,replace [ at position 1 with ),flow_matching,0.3,2.0,48,145
94,replace,1.0,C,),C)C[+C@@,CCC[+C@@,8,replace ) at position 1 with C,flow_matching,0.3,2.0,48,145
95,add,1.0,+,,CCC[+C@@,C+CC[+C@@,9,add + at position 1,flow_matching,0.3,2.0,48,145
96,add,1.0,s,,C+CC[+C@@,Cs+CC[+C@@,10,add s at position 1,flow_matching,0.3,2.0,48,145
97,add,9.0,+,,Cs+CC[+C@@,Cs+CC[+C@+@,11,add + at position 9,flow_matching,0.3,2.0,48,145
98,remove,6.0,+,,Cs+CC[+C@+@,Cs+CC[C@+@,10,remove + from position 6,flow_matching,0.3,2.0,48,145
99,replace,1.0,C,s,Cs+CC[C@+@,CC+CC[C@+@,10,replace s at position 1 with C,flow_matching,0.3,2.0,48,145
100,remove,8.0,+,,CC+CC[C@+@,CC+CC[C@@,9,remove + from position 8,flow_matching,0.3,2.0,48,145
101,replace,2.0,[,+,CC+CC[C@@,CC[CC[C@@,9,replace + at position 2 with [,flow_matching,0.3,2.0,48,145
102,replace,4.0,@,C,CC[CC[C@@,CC[C@[C@@,9,replace C at position 4 with @,flow_matching,0.3,2.0,48,145
103,replace,5.0,@,[,CC[C@[C@@,CC[C@@C@@,9,replace [ at position 5 with @,flow_matching,0.3,2.0,48,145
104,replace,6.0,],C,CC[C@@C@@,CC[C@@]@@,9,replace C at position 6 with ],flow_matching,0.3,2.0,48,145
105,replace,7.0,(,@,CC[C@@]@@,CC[C@@](@,9,replace @ at position 7 with (,flow_matching,0.3,2.0,48,145
106,replace,8.0,C,@,CC[C@@](@,CC[C@@](C,9,replace @ at position 8 with C,flow_matching,0.3,2.0,48,145
107,add,9.0,),,CC[C@@](C,CC[C@@](C),10,add ) at position 9,flow_matching,0.3,2.0,48,145
108,add,10.0,(,,CC[C@@](C),CC[C@@](C)(,11,add ( at position 10,flow_matching,0.3,2.0,48,145
109,add,11.0,[,,CC[C@@](C)(,CC[C@@](C)([,12,add [ at position 11,flow_matching,0.3,2.0,48,145
110,add,12.0,C,,CC[C@@](C)([,CC[C@@](C)([C,13,add C at position 12,flow_matching,0.3,2.0,48,145
111,add,13.0,@,,CC[C@@](C)([C,CC[C@@](C)([C@,14,add @ at position 13,flow_matching,0.3,2.0,48,145
112,add,14.0,@,,CC[C@@](C)([C@,CC[C@@](C)([C@@,15,add @ at position 14,flow_matching,0.3,2.0,48,145
113,add,15.0,H,,CC[C@@](C)([C@@,CC[C@@](C)([C@@H,16,add H at position 15,flow_matching,0.3,2.0,48,145
114,add,16.0,],,CC[C@@](C)([C@@H,CC[C@@](C)([C@@H],17,add ] at position 16,flow_matching,0.3,2.0,48,145
115,add,17.0,(,,CC[C@@](C)([C@@H],CC[C@@](C)([C@@H](,18,add ( at position 17,flow_matching,0.3,2.0,48,145
116,add,18.0,[,,CC[C@@](C)([C@@H](,CC[C@@](C)([C@@H]([,19,add [ at position 18,flow_matching,0.3,2.0,48,145
117,add,19.0,N,,CC[C@@](C)([C@@H]([,CC[C@@](C)([C@@H]([N,20,add N at position 19,flow_matching,0.3,2.0,48,145
118,add,20.0,H,,CC[C@@](C)([C@@H]([N,CC[C@@](C)([C@@H]([NH,21,add H at position 20,flow_matching,0.3,2.0,48,145
119,add,21.0,3,,CC[C@@](C)([C@@H]([NH,CC[C@@](C)([C@@H]([NH3,22,add 3 at position 21,flow_matching,0.3,2.0,48,145
120,add,22.0,+,,CC[C@@](C)([C@@H]([NH3,CC[C@@](C)([C@@H]([NH3+,23,add + at position 22,flow_matching,0.3,2.0,48,145
121,add,23.0,],,CC[C@@](C)([C@@H]([NH3+,CC[C@@](C)([C@@H]([NH3+],24,add ] at position 23,flow_matching,0.3,2.0,48,145
122,add,24.0,),,CC[C@@](C)([C@@H]([NH3+],CC[C@@](C)([C@@H]([NH3+]),25,add ) at position 24,flow_matching,0.3,2.0,48,145
123,add,25.0,c,,CC[C@@](C)([C@@H]([NH3+]),CC[C@@](C)([C@@H]([NH3+])c,26,add c at position 25,flow_matching,0.3,2.0,48,145
124,add,26.0,1,,CC[C@@](C)([C@@H]([NH3+])c,CC[C@@](C)([C@@H]([NH3+])c1,27,add 1 at position 26,flow_matching,0.3,2.0,48,145
125,add,27.0,c,,CC[C@@](C)([C@@H]([NH3+])c1,CC[C@@](C)([C@@H]([NH3+])c1c,28,add c at position 27,flow_matching,0.3,2.0,48,145
126,add,28.0,c,,CC[C@@](C)([C@@H]([NH3+])c1c,CC[C@@](C)([C@@H]([NH3+])c1cc,29,add c at position 28,flow_matching,0.3,2.0,48,145
127,add,29.0,(,,CC[C@@](C)([C@@H]([NH3+])c1cc,CC[C@@](C)([C@@H]([NH3+])c1cc(,30,add ( at position 29,flow_matching,0.3,2.0,48,145
128,add,30.0,B,,CC[C@@](C)([C@@H]([NH3+])c1cc(,CC[C@@](C)([C@@H]([NH3+])c1cc(B,31,add B at position 30,flow_matching,0.3,2.0,48,145
129,add,31.0,r,,CC[C@@](C)([C@@H]([NH3+])c1cc(B,CC[C@@](C)([C@@H]([NH3+])c1cc(Br,32,add r at position 31,flow_matching,0.3,2.0,48,145
130,add,32.0,),,CC[C@@](C)([C@@H]([NH3+])c1cc(Br,CC[C@@](C)([C@@H]([NH3+])c1cc(Br),33,add ) at position 32,flow_matching,0.3,2.0,48,145
131,add,33.0,c,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br),CC[C@@](C)([C@@H]([NH3+])c1cc(Br)c,34,add c at position 33,flow_matching,0.3,2.0,48,145
132,add,34.0,c,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)c,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)cc,35,add c at position 34,flow_matching,0.3,2.0,48,145
133,add,35.0,c,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)cc,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc,36,add c at position 35,flow_matching,0.3,2.0,48,145
134,add,36.0,1,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1,37,add 1 at position 36,flow_matching,0.3,2.0,48,145
135,add,37.0,F,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F,38,add F at position 37,flow_matching,0.3,2.0,48,145
136,add,38.0,),,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F),39,add ) at position 38,flow_matching,0.3,2.0,48,145
137,add,39.0,N,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F),CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N,40,add N at position 39,flow_matching,0.3,2.0,48,145
138,add,40.0,1,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1,41,add 1 at position 40,flow_matching,0.3,2.0,48,145
139,add,41.0,C,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1C,42,add C at position 41,flow_matching,0.3,2.0,48,145
140,add,42.0,C,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1C,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CC,43,add C at position 42,flow_matching,0.3,2.0,48,145
141,add,43.0,O,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CC,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CCO,44,add O at position 43,flow_matching,0.3,2.0,48,145
142,add,44.0,C,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CCO,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CCOC,45,add C at position 44,flow_matching,0.3,2.0,48,145
143,add,45.0,C,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CCOC,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CCOCC,46,add C at position 45,flow_matching,0.3,2.0,48,145
144,add,46.0,1,,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CCOCC,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CCOCC1,47,add 1 at position 46,flow_matching,0.3,2.0,48,145
145,add,47.0,"
",,CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CCOCC1,"CC[C@@](C)([C@@H]([NH3+])c1cc(Br)ccc1F)N1CCOCC1
",48,"add 
 at position 47",flow_matching,0.3,2.0,48,145
