step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,25,68
1,add,0.0,n,,,n,1,add n at position 0,flow_matching,0.3,2.0,25,68
2,replace,0.0,C,n,n,C,1,replace n at position 0 with C,flow_matching,0.3,2.0,25,68
3,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,25,68
4,add,0.0,1,,,1,1,add 1 at position 0,flow_matching,0.3,2.0,25,68
5,replace,0.0,I,1,1,I,1,replace 1 at position 0 with I,flow_matching,0.3,2.0,25,68
6,replace,0.0,<PERSON>,<PERSON>,<PERSON>,<PERSON>,1,replace I at position 0 with C,flow_matching,0.3,2.0,25,68
7,replace,0.0,N,C,<PERSON>,N,1,replace <PERSON> at position 0 with N,flow_matching,0.3,2.0,25,68
8,replace,0.0,<PERSON>,N,N,C,1,replace N at position 0 with C,flow_matching,0.3,2.0,25,68
9,add,0.0,c,,C,cC,2,add c at position 0,flow_matching,0.3,2.0,25,68
10,replace,0.0,C,c,cC,CC,2,replace c at position 0 with C,flow_matching,0.3,2.0,25,68
11,remove,0.0,C,,CC,C,1,remove C from position 0,flow_matching,0.3,2.0,25,68
12,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,25,68
13,remove,1.0,C,,CC,C,1,remove C from position 1,flow_matching,0.3,2.0,25,68
14,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,25,68
15,add,2.0,[,,CC,CC[,3,add [ at position 2,flow_matching,0.3,2.0,25,68
16,remove,1.0,C,,CC[,C[,2,remove C from position 1,flow_matching,0.3,2.0,25,68
17,add,1.0,(,,C[,C([,3,add ( at position 1,flow_matching,0.3,2.0,25,68
18,replace,1.0,C,(,C([,CC[,3,replace ( at position 1 with C,flow_matching,0.3,2.0,25,68
19,replace,2.0,2,[,CC[,CC2,3,replace [ at position 2 with 2,flow_matching,0.3,2.0,25,68
20,remove,0.0,C,,CC2,C2,2,remove C from position 0,flow_matching,0.3,2.0,25,68
21,add,2.0,1,,C2,C21,3,add 1 at position 2,flow_matching,0.3,2.0,25,68
22,remove,1.0,2,,C21,C1,2,remove 2 from position 1,flow_matching,0.3,2.0,25,68
23,remove,1.0,1,,C1,C,1,remove 1 from position 1,flow_matching,0.3,2.0,25,68
24,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,25,68
25,add,2.0,[,,CC,CC[,3,add [ at position 2,flow_matching,0.3,2.0,25,68
26,replace,0.0,7,C,CC[,7C[,3,replace C at position 0 with 7,flow_matching,0.3,2.0,25,68
27,remove,1.0,C,,7C[,7[,2,remove C from position 1,flow_matching,0.3,2.0,25,68
28,add,1.0,\,,7[,7\[,3,add \ at position 1,flow_matching,0.3,2.0,25,68
29,add,3.0,I,,7\[,7\[I,4,add I at position 3,flow_matching,0.3,2.0,25,68
30,remove,1.0,\,,7\[I,7[I,3,remove \ from position 1,flow_matching,0.3,2.0,25,68
31,replace,2.0,#,I,7[I,7[#,3,replace I at position 2 with #,flow_matching,0.3,2.0,25,68
32,add,2.0,=,,7[#,7[=#,4,add = at position 2,flow_matching,0.3,2.0,25,68
33,remove,3.0,#,,7[=#,7[=,3,remove # from position 3,flow_matching,0.3,2.0,25,68
34,replace,0.0,C,7,7[=,C[=,3,replace 7 at position 0 with C,flow_matching,0.3,2.0,25,68
35,replace,0.0,4,C,C[=,4[=,3,replace C at position 0 with 4,flow_matching,0.3,2.0,25,68
36,add,1.0,F,,4[=,4F[=,4,add F at position 1,flow_matching,0.3,2.0,25,68
37,add,2.0,/,,4F[=,4F/[=,5,add / at position 2,flow_matching,0.3,2.0,25,68
38,remove,1.0,F,,4F/[=,4/[=,4,remove F from position 1,flow_matching,0.3,2.0,25,68
39,add,0.0,\,,4/[=,\4/[=,5,add \ at position 0,flow_matching,0.3,2.0,25,68
40,remove,0.0,\,,\4/[=,4/[=,4,remove \ from position 0,flow_matching,0.3,2.0,25,68
41,add,2.0,H,,4/[=,4/H[=,5,add H at position 2,flow_matching,0.3,2.0,25,68
42,replace,0.0,C,4,4/H[=,C/H[=,5,replace 4 at position 0 with C,flow_matching,0.3,2.0,25,68
43,remove,1.0,/,,C/H[=,CH[=,4,remove / from position 1,flow_matching,0.3,2.0,25,68
44,add,0.0,1,,CH[=,1CH[=,5,add 1 at position 0,flow_matching,0.3,2.0,25,68
45,replace,0.0,C,1,1CH[=,CCH[=,5,replace 1 at position 0 with C,flow_matching,0.3,2.0,25,68
46,replace,2.0,[,H,CCH[=,CC[[=,5,replace H at position 2 with [,flow_matching,0.3,2.0,25,68
47,replace,3.0,C,[,CC[[=,CC[C=,5,replace [ at position 3 with C,flow_matching,0.3,2.0,25,68
48,replace,4.0,@,=,CC[C=,CC[C@,5,replace = at position 4 with @,flow_matching,0.3,2.0,25,68
49,add,5.0,H,,CC[C@,CC[C@H,6,add H at position 5,flow_matching,0.3,2.0,25,68
50,add,6.0,],,CC[C@H,CC[C@H],7,add ] at position 6,flow_matching,0.3,2.0,25,68
51,add,7.0,(,,CC[C@H],CC[C@H](,8,add ( at position 7,flow_matching,0.3,2.0,25,68
52,add,8.0,C,,CC[C@H](,CC[C@H](C,9,add C at position 8,flow_matching,0.3,2.0,25,68
53,add,9.0,),,CC[C@H](C,CC[C@H](C),10,add ) at position 9,flow_matching,0.3,2.0,25,68
54,add,10.0,C,,CC[C@H](C),CC[C@H](C)C,11,add C at position 10,flow_matching,0.3,2.0,25,68
55,add,11.0,(,,CC[C@H](C)C,CC[C@H](C)C(,12,add ( at position 11,flow_matching,0.3,2.0,25,68
56,add,12.0,=,,CC[C@H](C)C(,CC[C@H](C)C(=,13,add = at position 12,flow_matching,0.3,2.0,25,68
57,add,13.0,O,,CC[C@H](C)C(=,CC[C@H](C)C(=O,14,add O at position 13,flow_matching,0.3,2.0,25,68
58,add,14.0,),,CC[C@H](C)C(=O,CC[C@H](C)C(=O),15,add ) at position 14,flow_matching,0.3,2.0,25,68
59,add,15.0,N,,CC[C@H](C)C(=O),CC[C@H](C)C(=O)N,16,add N at position 15,flow_matching,0.3,2.0,25,68
60,add,16.0,c,,CC[C@H](C)C(=O)N,CC[C@H](C)C(=O)Nc,17,add c at position 16,flow_matching,0.3,2.0,25,68
61,add,17.0,1,,CC[C@H](C)C(=O)Nc,CC[C@H](C)C(=O)Nc1,18,add 1 at position 17,flow_matching,0.3,2.0,25,68
62,add,18.0,c,,CC[C@H](C)C(=O)Nc1,CC[C@H](C)C(=O)Nc1c,19,add c at position 18,flow_matching,0.3,2.0,25,68
63,add,19.0,c,,CC[C@H](C)C(=O)Nc1c,CC[C@H](C)C(=O)Nc1cc,20,add c at position 19,flow_matching,0.3,2.0,25,68
64,add,20.0,c,,CC[C@H](C)C(=O)Nc1cc,CC[C@H](C)C(=O)Nc1ccc,21,add c at position 20,flow_matching,0.3,2.0,25,68
65,add,21.0,c,,CC[C@H](C)C(=O)Nc1ccc,CC[C@H](C)C(=O)Nc1cccc,22,add c at position 21,flow_matching,0.3,2.0,25,68
66,add,22.0,n,,CC[C@H](C)C(=O)Nc1cccc,CC[C@H](C)C(=O)Nc1ccccn,23,add n at position 22,flow_matching,0.3,2.0,25,68
67,add,23.0,1,,CC[C@H](C)C(=O)Nc1ccccn,CC[C@H](C)C(=O)Nc1ccccn1,24,add 1 at position 23,flow_matching,0.3,2.0,25,68
68,add,24.0,"
",,CC[C@H](C)C(=O)Nc1ccccn1,"CC[C@H](C)C(=O)Nc1ccccn1
",25,"add 
 at position 24",flow_matching,0.3,2.0,25,68
