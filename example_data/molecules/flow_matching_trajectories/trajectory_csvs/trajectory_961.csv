step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,34,137
1,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,34,137
2,remove,0.0,],,],,0,remove ] from position 0,flow_matching,0.3,2.0,34,137
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,34,137
4,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,34,137
5,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,34,137
6,replace,0.0,C,B,B,C,1,replace <PERSON> at position 0 with C,flow_matching,0.3,2.0,34,137
7,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,34,137
8,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,34,137
9,replace,0.0,F,+,+,F,1,replace + at position 0 with F,flow_matching,0.3,2.0,34,137
10,replace,0.0,O,F,F,O,1,replace F at position 0 with O,flow_matching,0.3,2.0,34,137
11,add,0.0,],,O,]O,2,add ] at position 0,flow_matching,0.3,2.0,34,137
12,replace,0.0,C,],]O,CO,2,replace ] at position 0 with C,flow_matching,0.3,2.0,34,137
13,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,34,137
14,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,34,137
15,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,34,137
16,replace,0.0,C,I,I,C,1,replace I at position 0 with C,flow_matching,0.3,2.0,34,137
17,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,34,137
18,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,34,137
19,add,0.0,2,,6,26,2,add 2 at position 0,flow_matching,0.3,2.0,34,137
20,remove,0.0,2,,26,6,1,remove 2 from position 0,flow_matching,0.3,2.0,34,137
21,add,1.0,4,,6,64,2,add 4 at position 1,flow_matching,0.3,2.0,34,137
22,add,0.0,S,,64,S64,3,add S at position 0,flow_matching,0.3,2.0,34,137
23,replace,0.0,C,S,S64,C64,3,replace S at position 0 with C,flow_matching,0.3,2.0,34,137
24,add,1.0,(,,C64,C(64,4,add ( at position 1,flow_matching,0.3,2.0,34,137
25,replace,1.0,O,(,C(64,CO64,4,replace ( at position 1 with O,flow_matching,0.3,2.0,34,137
26,remove,0.0,C,,CO64,O64,3,remove C from position 0,flow_matching,0.3,2.0,34,137
27,add,1.0,H,,O64,OH64,4,add H at position 1,flow_matching,0.3,2.0,34,137
28,add,4.0,),,OH64,OH64),5,add ) at position 4,flow_matching,0.3,2.0,34,137
29,remove,4.0,),,OH64),OH64,4,remove ) from position 4,flow_matching,0.3,2.0,34,137
30,add,1.0,),,OH64,O)H64,5,add ) at position 1,flow_matching,0.3,2.0,34,137
31,remove,3.0,6,,O)H64,O)H4,4,remove 6 from position 3,flow_matching,0.3,2.0,34,137
32,replace,0.0,6,O,O)H4,6)H4,4,replace O at position 0 with 6,flow_matching,0.3,2.0,34,137
33,replace,0.0,C,6,6)H4,C)H4,4,replace 6 at position 0 with C,flow_matching,0.3,2.0,34,137
34,add,0.0,3,,C)H4,3C)H4,5,add 3 at position 0,flow_matching,0.3,2.0,34,137
35,add,4.0,7,,3C)H4,3C)H74,6,add 7 at position 4,flow_matching,0.3,2.0,34,137
36,replace,0.0,C,3,3C)H74,CC)H74,6,replace 3 at position 0 with C,flow_matching,0.3,2.0,34,137
37,remove,1.0,C,,CC)H74,C)H74,5,remove C from position 1,flow_matching,0.3,2.0,34,137
38,remove,3.0,7,,C)H74,C)H4,4,remove 7 from position 3,flow_matching,0.3,2.0,34,137
39,remove,0.0,C,,C)H4,)H4,3,remove C from position 0,flow_matching,0.3,2.0,34,137
40,remove,0.0,),,)H4,H4,2,remove ) from position 0,flow_matching,0.3,2.0,34,137
41,remove,0.0,H,,H4,4,1,remove H from position 0,flow_matching,0.3,2.0,34,137
42,add,0.0,C,,4,C4,2,add C at position 0,flow_matching,0.3,2.0,34,137
43,add,2.0,2,,C4,C42,3,add 2 at position 2,flow_matching,0.3,2.0,34,137
44,replace,1.0,c,4,C42,Cc2,3,replace 4 at position 1 with c,flow_matching,0.3,2.0,34,137
45,replace,1.0,O,c,Cc2,CO2,3,replace c at position 1 with O,flow_matching,0.3,2.0,34,137
46,remove,0.0,C,,CO2,O2,2,remove C from position 0,flow_matching,0.3,2.0,34,137
47,replace,0.0,C,O,O2,C2,2,replace O at position 0 with C,flow_matching,0.3,2.0,34,137
48,replace,1.0,O,2,C2,CO,2,replace 2 at position 1 with O,flow_matching,0.3,2.0,34,137
49,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,34,137
50,add,3.0,1,,COc,COc1,4,add 1 at position 3,flow_matching,0.3,2.0,34,137
51,replace,2.0,=,c,COc1,CO=1,4,replace c at position 2 with =,flow_matching,0.3,2.0,34,137
52,replace,3.0,6,1,CO=1,CO=6,4,replace 1 at position 3 with 6,flow_matching,0.3,2.0,34,137
53,add,2.0,S,,CO=6,COS=6,5,add S at position 2,flow_matching,0.3,2.0,34,137
54,replace,2.0,c,S,COS=6,COc=6,5,replace S at position 2 with c,flow_matching,0.3,2.0,34,137
55,replace,4.0,=,6,COc=6,COc==,5,replace 6 at position 4 with =,flow_matching,0.3,2.0,34,137
56,add,3.0,I,,COc==,COcI==,6,add I at position 3,flow_matching,0.3,2.0,34,137
57,add,5.0,-,,COcI==,COcI=-=,7,add - at position 5,flow_matching,0.3,2.0,34,137
58,replace,3.0,1,I,COcI=-=,COc1=-=,7,replace I at position 3 with 1,flow_matching,0.3,2.0,34,137
59,replace,4.0,c,=,COc1=-=,COc1c-=,7,replace = at position 4 with c,flow_matching,0.3,2.0,34,137
60,replace,5.0,c,-,COc1c-=,COc1cc=,7,replace - at position 5 with c,flow_matching,0.3,2.0,34,137
61,replace,6.0,c,=,COc1cc=,COc1ccc,7,replace = at position 6 with c,flow_matching,0.3,2.0,34,137
62,replace,1.0,N,O,COc1ccc,CNc1ccc,7,replace O at position 1 with N,flow_matching,0.3,2.0,34,137
63,replace,1.0,O,N,CNc1ccc,COc1ccc,7,replace N at position 1 with O,flow_matching,0.3,2.0,34,137
64,add,7.0,(,,COc1ccc,COc1ccc(,8,add ( at position 7,flow_matching,0.3,2.0,34,137
65,add,8.0,O,,COc1ccc(,COc1ccc(O,9,add O at position 8,flow_matching,0.3,2.0,34,137
66,add,9.0,C,,COc1ccc(O,COc1ccc(OC,10,add C at position 9,flow_matching,0.3,2.0,34,137
67,remove,0.0,C,,COc1ccc(OC,Oc1ccc(OC,9,remove C from position 0,flow_matching,0.3,2.0,34,137
68,remove,4.0,c,,Oc1ccc(OC,Oc1cc(OC,8,remove c from position 4,flow_matching,0.3,2.0,34,137
69,add,1.0,[,,Oc1cc(OC,O[c1cc(OC,9,add [ at position 1,flow_matching,0.3,2.0,34,137
70,add,2.0,=,,O[c1cc(OC,O[=c1cc(OC,10,add = at position 2,flow_matching,0.3,2.0,34,137
71,replace,0.0,C,O,O[=c1cc(OC,C[=c1cc(OC,10,replace O at position 0 with C,flow_matching,0.3,2.0,34,137
72,add,4.0,c,,C[=c1cc(OC,C[=cc1cc(OC,11,add c at position 4,flow_matching,0.3,2.0,34,137
73,replace,1.0,O,[,C[=cc1cc(OC,CO=cc1cc(OC,11,replace [ at position 1 with O,flow_matching,0.3,2.0,34,137
74,add,10.0,3,,CO=cc1cc(OC,CO=cc1cc(O3C,12,add 3 at position 10,flow_matching,0.3,2.0,34,137
75,replace,11.0,1,C,CO=cc1cc(O3C,CO=cc1cc(O31,12,replace C at position 11 with 1,flow_matching,0.3,2.0,34,137
76,replace,10.0,6,3,CO=cc1cc(O31,CO=cc1cc(O61,12,replace 3 at position 10 with 6,flow_matching,0.3,2.0,34,137
77,replace,2.0,c,=,CO=cc1cc(O61,COccc1cc(O61,12,replace = at position 2 with c,flow_matching,0.3,2.0,34,137
78,add,12.0,n,,COccc1cc(O61,COccc1cc(O61n,13,add n at position 12,flow_matching,0.3,2.0,34,137
79,add,13.0,l,,COccc1cc(O61n,COccc1cc(O61nl,14,add l at position 13,flow_matching,0.3,2.0,34,137
80,replace,3.0,1,c,COccc1cc(O61nl,COc1c1cc(O61nl,14,replace c at position 3 with 1,flow_matching,0.3,2.0,34,137
81,replace,8.0,1,(,COc1c1cc(O61nl,COc1c1cc1O61nl,14,replace ( at position 8 with 1,flow_matching,0.3,2.0,34,137
82,replace,5.0,c,1,COc1c1cc1O61nl,COc1cccc1O61nl,14,replace 1 at position 5 with c,flow_matching,0.3,2.0,34,137
83,add,11.0,1,,COc1cccc1O61nl,COc1cccc1O611nl,15,add 1 at position 11,flow_matching,0.3,2.0,34,137
84,remove,6.0,c,,COc1cccc1O611nl,COc1ccc1O611nl,14,remove c from position 6,flow_matching,0.3,2.0,34,137
85,remove,9.0,6,,COc1ccc1O611nl,COc1ccc1O11nl,13,remove 6 from position 9,flow_matching,0.3,2.0,34,137
86,add,9.0,],,COc1ccc1O11nl,COc1ccc1O]11nl,14,add ] at position 9,flow_matching,0.3,2.0,34,137
87,replace,7.0,(,1,COc1ccc1O]11nl,COc1ccc(O]11nl,14,replace 1 at position 7 with (,flow_matching,0.3,2.0,34,137
88,replace,9.0,C,],COc1ccc(O]11nl,COc1ccc(OC11nl,14,replace ] at position 9 with C,flow_matching,0.3,2.0,34,137
89,add,3.0,6,,COc1ccc(OC11nl,COc61ccc(OC11nl,15,add 6 at position 3,flow_matching,0.3,2.0,34,137
90,replace,3.0,1,6,COc61ccc(OC11nl,COc11ccc(OC11nl,15,replace 6 at position 3 with 1,flow_matching,0.3,2.0,34,137
91,replace,4.0,c,1,COc11ccc(OC11nl,COc1cccc(OC11nl,15,replace 1 at position 4 with c,flow_matching,0.3,2.0,34,137
92,replace,7.0,(,c,COc1cccc(OC11nl,COc1ccc((OC11nl,15,replace c at position 7 with (,flow_matching,0.3,2.0,34,137
93,replace,12.0,S,1,COc1ccc((OC11nl,COc1ccc((OC1Snl,15,replace 1 at position 12 with S,flow_matching,0.3,2.0,34,137
94,replace,8.0,O,(,COc1ccc((OC1Snl,COc1ccc(OOC1Snl,15,replace ( at position 8 with O,flow_matching,0.3,2.0,34,137
95,add,2.0,4,,COc1ccc(OOC1Snl,CO4c1ccc(OOC1Snl,16,add 4 at position 2,flow_matching,0.3,2.0,34,137
96,replace,2.0,c,4,CO4c1ccc(OOC1Snl,COcc1ccc(OOC1Snl,16,replace 4 at position 2 with c,flow_matching,0.3,2.0,34,137
97,add,2.0,+,,COcc1ccc(OOC1Snl,CO+cc1ccc(OOC1Snl,17,add + at position 2,flow_matching,0.3,2.0,34,137
98,replace,2.0,c,+,CO+cc1ccc(OOC1Snl,COccc1ccc(OOC1Snl,17,replace + at position 2 with c,flow_matching,0.3,2.0,34,137
99,replace,1.0,N,O,COccc1ccc(OOC1Snl,CNccc1ccc(OOC1Snl,17,replace O at position 1 with N,flow_matching,0.3,2.0,34,137
100,add,6.0,B,,CNccc1ccc(OOC1Snl,CNccc1Bccc(OOC1Snl,18,add B at position 6,flow_matching,0.3,2.0,34,137
101,replace,11.0,3,O,CNccc1Bccc(OOC1Snl,CNccc1Bccc(3OC1Snl,18,replace O at position 11 with 3,flow_matching,0.3,2.0,34,137
102,replace,0.0,#,C,CNccc1Bccc(3OC1Snl,#Nccc1Bccc(3OC1Snl,18,replace C at position 0 with #,flow_matching,0.3,2.0,34,137
103,replace,7.0,@,c,#Nccc1Bccc(3OC1Snl,#Nccc1B@cc(3OC1Snl,18,replace c at position 7 with @,flow_matching,0.3,2.0,34,137
104,replace,7.0,s,@,#Nccc1B@cc(3OC1Snl,#Nccc1Bscc(3OC1Snl,18,replace @ at position 7 with s,flow_matching,0.3,2.0,34,137
105,remove,17.0,l,,#Nccc1Bscc(3OC1Snl,#Nccc1Bscc(3OC1Sn,17,remove l from position 17,flow_matching,0.3,2.0,34,137
106,replace,0.0,C,#,#Nccc1Bscc(3OC1Sn,CNccc1Bscc(3OC1Sn,17,replace # at position 0 with C,flow_matching,0.3,2.0,34,137
107,replace,1.0,O,N,CNccc1Bscc(3OC1Sn,COccc1Bscc(3OC1Sn,17,replace N at position 1 with O,flow_matching,0.3,2.0,34,137
108,replace,3.0,1,c,COccc1Bscc(3OC1Sn,COc1c1Bscc(3OC1Sn,17,replace c at position 3 with 1,flow_matching,0.3,2.0,34,137
109,replace,5.0,c,1,COc1c1Bscc(3OC1Sn,COc1ccBscc(3OC1Sn,17,replace 1 at position 5 with c,flow_matching,0.3,2.0,34,137
110,replace,6.0,c,B,COc1ccBscc(3OC1Sn,COc1cccscc(3OC1Sn,17,replace B at position 6 with c,flow_matching,0.3,2.0,34,137
111,replace,7.0,(,s,COc1cccscc(3OC1Sn,COc1ccc(cc(3OC1Sn,17,replace s at position 7 with (,flow_matching,0.3,2.0,34,137
112,replace,8.0,O,c,COc1ccc(cc(3OC1Sn,COc1ccc(Oc(3OC1Sn,17,replace c at position 8 with O,flow_matching,0.3,2.0,34,137
113,replace,9.0,C,c,COc1ccc(Oc(3OC1Sn,COc1ccc(OC(3OC1Sn,17,replace c at position 9 with C,flow_matching,0.3,2.0,34,137
114,replace,10.0,),(,COc1ccc(OC(3OC1Sn,COc1ccc(OC)3OC1Sn,17,replace ( at position 10 with ),flow_matching,0.3,2.0,34,137
115,replace,11.0,c,3,COc1ccc(OC)3OC1Sn,COc1ccc(OC)cOC1Sn,17,replace 3 at position 11 with c,flow_matching,0.3,2.0,34,137
116,replace,12.0,(,O,COc1ccc(OC)cOC1Sn,COc1ccc(OC)c(C1Sn,17,replace O at position 12 with (,flow_matching,0.3,2.0,34,137
117,replace,13.0,/,C,COc1ccc(OC)c(C1Sn,COc1ccc(OC)c(/1Sn,17,replace C at position 13 with /,flow_matching,0.3,2.0,34,137
118,replace,14.0,C,1,COc1ccc(OC)c(/1Sn,COc1ccc(OC)c(/CSn,17,replace 1 at position 14 with C,flow_matching,0.3,2.0,34,137
119,replace,15.0,=,S,COc1ccc(OC)c(/CSn,COc1ccc(OC)c(/C=n,17,replace S at position 15 with =,flow_matching,0.3,2.0,34,137
120,replace,16.0,C,n,COc1ccc(OC)c(/C=n,COc1ccc(OC)c(/C=C,17,replace n at position 16 with C,flow_matching,0.3,2.0,34,137
121,add,17.0,/,,COc1ccc(OC)c(/C=C,COc1ccc(OC)c(/C=C/,18,add / at position 17,flow_matching,0.3,2.0,34,137
122,add,18.0,C,,COc1ccc(OC)c(/C=C/,COc1ccc(OC)c(/C=C/C,19,add C at position 18,flow_matching,0.3,2.0,34,137
123,add,19.0,(,,COc1ccc(OC)c(/C=C/C,COc1ccc(OC)c(/C=C/C(,20,add ( at position 19,flow_matching,0.3,2.0,34,137
124,add,20.0,=,,COc1ccc(OC)c(/C=C/C(,COc1ccc(OC)c(/C=C/C(=,21,add = at position 20,flow_matching,0.3,2.0,34,137
125,add,21.0,O,,COc1ccc(OC)c(/C=C/C(=,COc1ccc(OC)c(/C=C/C(=O,22,add O at position 21,flow_matching,0.3,2.0,34,137
126,add,22.0,),,COc1ccc(OC)c(/C=C/C(=O,COc1ccc(OC)c(/C=C/C(=O),23,add ) at position 22,flow_matching,0.3,2.0,34,137
127,add,23.0,O,,COc1ccc(OC)c(/C=C/C(=O),COc1ccc(OC)c(/C=C/C(=O)O,24,add O at position 23,flow_matching,0.3,2.0,34,137
128,add,24.0,C,,COc1ccc(OC)c(/C=C/C(=O)O,COc1ccc(OC)c(/C=C/C(=O)OC,25,add C at position 24,flow_matching,0.3,2.0,34,137
129,add,25.0,C,,COc1ccc(OC)c(/C=C/C(=O)OC,COc1ccc(OC)c(/C=C/C(=O)OCC,26,add C at position 25,flow_matching,0.3,2.0,34,137
130,add,26.0,(,,COc1ccc(OC)c(/C=C/C(=O)OCC,COc1ccc(OC)c(/C=C/C(=O)OCC(,27,add ( at position 26,flow_matching,0.3,2.0,34,137
131,add,27.0,C,,COc1ccc(OC)c(/C=C/C(=O)OCC(,COc1ccc(OC)c(/C=C/C(=O)OCC(C,28,add C at position 27,flow_matching,0.3,2.0,34,137
132,add,28.0,),,COc1ccc(OC)c(/C=C/C(=O)OCC(C,COc1ccc(OC)c(/C=C/C(=O)OCC(C),29,add ) at position 28,flow_matching,0.3,2.0,34,137
133,add,29.0,C,,COc1ccc(OC)c(/C=C/C(=O)OCC(C),COc1ccc(OC)c(/C=C/C(=O)OCC(C)C,30,add C at position 29,flow_matching,0.3,2.0,34,137
134,add,30.0,),,COc1ccc(OC)c(/C=C/C(=O)OCC(C)C,COc1ccc(OC)c(/C=C/C(=O)OCC(C)C),31,add ) at position 30,flow_matching,0.3,2.0,34,137
135,add,31.0,c,,COc1ccc(OC)c(/C=C/C(=O)OCC(C)C),COc1ccc(OC)c(/C=C/C(=O)OCC(C)C)c,32,add c at position 31,flow_matching,0.3,2.0,34,137
136,add,32.0,1,,COc1ccc(OC)c(/C=C/C(=O)OCC(C)C)c,COc1ccc(OC)c(/C=C/C(=O)OCC(C)C)c1,33,add 1 at position 32,flow_matching,0.3,2.0,34,137
137,add,33.0,"
",,COc1ccc(OC)c(/C=C/C(=O)OCC(C)C)c1,"COc1ccc(OC)c(/C=C/C(=O)OCC(C)C)c1
",34,"add 
 at position 33",flow_matching,0.3,2.0,34,137
