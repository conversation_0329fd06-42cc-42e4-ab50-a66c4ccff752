step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,181
1,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,43,181
2,remove,0.0,+,,+,,0,remove + from position 0,flow_matching,0.3,2.0,43,181
3,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,43,181
4,add,1.0,2,,F,F2,2,add 2 at position 1,flow_matching,0.3,2.0,43,181
5,add,1.0,r,,F2,Fr2,3,add r at position 1,flow_matching,0.3,2.0,43,181
6,add,1.0,-,,Fr2,F-r2,4,add - at position 1,flow_matching,0.3,2.0,43,181
7,replace,0.0,C,F,F-r2,C-r2,4,replace F at position 0 with C,flow_matching,0.3,2.0,43,181
8,add,0.0,H,,C-r2,HC-r2,5,add H at position 0,flow_matching,0.3,2.0,43,181
9,replace,0.0,C,H,HC-r2,CC-r2,5,replace H at position 0 with C,flow_matching,0.3,2.0,43,181
10,replace,4.0,3,2,CC-r2,CC-r3,5,replace 2 at position 4 with 3,flow_matching,0.3,2.0,43,181
11,replace,2.0,=,-,CC-r3,CC=r3,5,replace - at position 2 with =,flow_matching,0.3,2.0,43,181
12,remove,4.0,3,,CC=r3,CC=r,4,remove 3 from position 4,flow_matching,0.3,2.0,43,181
13,remove,0.0,C,,CC=r,C=r,3,remove C from position 0,flow_matching,0.3,2.0,43,181
14,replace,1.0,C,=,C=r,CCr,3,replace = at position 1 with C,flow_matching,0.3,2.0,43,181
15,remove,0.0,C,,CCr,Cr,2,remove C from position 0,flow_matching,0.3,2.0,43,181
16,add,1.0,@,,Cr,C@r,3,add @ at position 1,flow_matching,0.3,2.0,43,181
17,replace,1.0,C,@,C@r,CCr,3,replace @ at position 1 with C,flow_matching,0.3,2.0,43,181
18,replace,2.0,C,r,CCr,CCC,3,replace r at position 2 with C,flow_matching,0.3,2.0,43,181
19,add,3.0,(,,CCC,CCC(,4,add ( at position 3,flow_matching,0.3,2.0,43,181
20,add,4.0,=,,CCC(,CCC(=,5,add = at position 4,flow_matching,0.3,2.0,43,181
21,replace,0.0,O,C,CCC(=,OCC(=,5,replace C at position 0 with O,flow_matching,0.3,2.0,43,181
22,replace,4.0,I,=,OCC(=,OCC(I,5,replace = at position 4 with I,flow_matching,0.3,2.0,43,181
23,add,1.0,B,,OCC(I,OBCC(I,6,add B at position 1,flow_matching,0.3,2.0,43,181
24,replace,0.0,C,O,OBCC(I,CBCC(I,6,replace O at position 0 with C,flow_matching,0.3,2.0,43,181
25,remove,3.0,C,,CBCC(I,CBC(I,5,remove C from position 3,flow_matching,0.3,2.0,43,181
26,replace,1.0,C,B,CBC(I,CCC(I,5,replace B at position 1 with C,flow_matching,0.3,2.0,43,181
27,remove,0.0,C,,CCC(I,CC(I,4,remove C from position 0,flow_matching,0.3,2.0,43,181
28,replace,0.0,-,C,CC(I,-C(I,4,replace C at position 0 with -,flow_matching,0.3,2.0,43,181
29,replace,1.0,6,C,-C(I,-6(I,4,replace C at position 1 with 6,flow_matching,0.3,2.0,43,181
30,replace,0.0,C,-,-6(I,C6(I,4,replace - at position 0 with C,flow_matching,0.3,2.0,43,181
31,replace,3.0,F,I,C6(I,C6(F,4,replace I at position 3 with F,flow_matching,0.3,2.0,43,181
32,add,0.0,),,C6(F,)C6(F,5,add ) at position 0,flow_matching,0.3,2.0,43,181
33,replace,0.0,C,),)C6(F,CC6(F,5,replace ) at position 0 with C,flow_matching,0.3,2.0,43,181
34,replace,1.0,=,C,CC6(F,C=6(F,5,replace C at position 1 with =,flow_matching,0.3,2.0,43,181
35,replace,1.0,C,=,C=6(F,CC6(F,5,replace = at position 1 with C,flow_matching,0.3,2.0,43,181
36,replace,2.0,C,6,CC6(F,CCC(F,5,replace 6 at position 2 with C,flow_matching,0.3,2.0,43,181
37,remove,0.0,C,,CCC(F,CC(F,4,remove C from position 0,flow_matching,0.3,2.0,43,181
38,replace,2.0,C,(,CC(F,CCCF,4,replace ( at position 2 with C,flow_matching,0.3,2.0,43,181
39,replace,3.0,(,F,CCCF,CCC(,4,replace F at position 3 with (,flow_matching,0.3,2.0,43,181
40,add,4.0,=,,CCC(,CCC(=,5,add = at position 4,flow_matching,0.3,2.0,43,181
41,remove,4.0,=,,CCC(=,CCC(,4,remove = from position 4,flow_matching,0.3,2.0,43,181
42,remove,3.0,(,,CCC(,CCC,3,remove ( from position 3,flow_matching,0.3,2.0,43,181
43,replace,2.0,4,C,CCC,CC4,3,replace C at position 2 with 4,flow_matching,0.3,2.0,43,181
44,add,0.0,2,,CC4,2CC4,4,add 2 at position 0,flow_matching,0.3,2.0,43,181
45,remove,1.0,C,,2CC4,2C4,3,remove C from position 1,flow_matching,0.3,2.0,43,181
46,remove,2.0,4,,2C4,2C,2,remove 4 from position 2,flow_matching,0.3,2.0,43,181
47,replace,1.0,4,C,2C,24,2,replace C at position 1 with 4,flow_matching,0.3,2.0,43,181
48,replace,0.0,o,2,24,o4,2,replace 2 at position 0 with o,flow_matching,0.3,2.0,43,181
49,add,2.0,[,,o4,o4[,3,add [ at position 2,flow_matching,0.3,2.0,43,181
50,remove,2.0,[,,o4[,o4,2,remove [ from position 2,flow_matching,0.3,2.0,43,181
51,replace,0.0,C,o,o4,C4,2,replace o at position 0 with C,flow_matching,0.3,2.0,43,181
52,remove,1.0,4,,C4,C,1,remove 4 from position 1,flow_matching,0.3,2.0,43,181
53,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,43,181
54,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,43,181
55,replace,0.0,C,F,F,C,1,replace F at position 0 with C,flow_matching,0.3,2.0,43,181
56,add,1.0,7,,C,C7,2,add 7 at position 1,flow_matching,0.3,2.0,43,181
57,replace,1.0,C,7,C7,CC,2,replace 7 at position 1 with C,flow_matching,0.3,2.0,43,181
58,remove,1.0,C,,CC,C,1,remove C from position 1,flow_matching,0.3,2.0,43,181
59,replace,0.0,s,C,C,s,1,replace C at position 0 with s,flow_matching,0.3,2.0,43,181
60,add,1.0,n,,s,sn,2,add n at position 1,flow_matching,0.3,2.0,43,181
61,add,0.0,N,,sn,Nsn,3,add N at position 0,flow_matching,0.3,2.0,43,181
62,replace,0.0,5,N,Nsn,5sn,3,replace N at position 0 with 5,flow_matching,0.3,2.0,43,181
63,add,3.0,r,,5sn,5snr,4,add r at position 3,flow_matching,0.3,2.0,43,181
64,add,2.0,B,,5snr,5sBnr,5,add B at position 2,flow_matching,0.3,2.0,43,181
65,remove,0.0,5,,5sBnr,sBnr,4,remove 5 from position 0,flow_matching,0.3,2.0,43,181
66,replace,2.0,O,n,sBnr,sBOr,4,replace n at position 2 with O,flow_matching,0.3,2.0,43,181
67,add,4.0,N,,sBOr,sBOrN,5,add N at position 4,flow_matching,0.3,2.0,43,181
68,remove,1.0,B,,sBOrN,sOrN,4,remove B from position 1,flow_matching,0.3,2.0,43,181
69,remove,2.0,r,,sOrN,sON,3,remove r from position 2,flow_matching,0.3,2.0,43,181
70,replace,0.0,C,s,sON,CON,3,replace s at position 0 with C,flow_matching,0.3,2.0,43,181
71,replace,1.0,C,O,CON,CCN,3,replace O at position 1 with C,flow_matching,0.3,2.0,43,181
72,replace,2.0,C,N,CCN,CCC,3,replace N at position 2 with C,flow_matching,0.3,2.0,43,181
73,remove,2.0,C,,CCC,CC,2,remove C from position 2,flow_matching,0.3,2.0,43,181
74,add,0.0,O,,CC,OCC,3,add O at position 0,flow_matching,0.3,2.0,43,181
75,replace,0.0,C,O,OCC,CCC,3,replace O at position 0 with C,flow_matching,0.3,2.0,43,181
76,remove,1.0,C,,CCC,CC,2,remove C from position 1,flow_matching,0.3,2.0,43,181
77,add,1.0,-,,CC,C-C,3,add - at position 1,flow_matching,0.3,2.0,43,181
78,add,0.0,6,,C-C,6C-C,4,add 6 at position 0,flow_matching,0.3,2.0,43,181
79,replace,0.0,C,6,6C-C,CC-C,4,replace 6 at position 0 with C,flow_matching,0.3,2.0,43,181
80,add,3.0,5,,CC-C,CC-5C,5,add 5 at position 3,flow_matching,0.3,2.0,43,181
81,add,3.0,H,,CC-5C,CC-H5C,6,add H at position 3,flow_matching,0.3,2.0,43,181
82,add,3.0,H,,CC-H5C,CC-HH5C,7,add H at position 3,flow_matching,0.3,2.0,43,181
83,replace,2.0,C,-,CC-HH5C,CCCHH5C,7,replace - at position 2 with C,flow_matching,0.3,2.0,43,181
84,replace,1.0,r,C,CCCHH5C,CrCHH5C,7,replace C at position 1 with r,flow_matching,0.3,2.0,43,181
85,replace,1.0,C,r,CrCHH5C,CCCHH5C,7,replace r at position 1 with C,flow_matching,0.3,2.0,43,181
86,replace,3.0,(,H,CCCHH5C,CCC(H5C,7,replace H at position 3 with (,flow_matching,0.3,2.0,43,181
87,add,5.0,\,,CCC(H5C,CCC(H\5C,8,add \ at position 5,flow_matching,0.3,2.0,43,181
88,add,5.0,o,,CCC(H\5C,CCC(Ho\5C,9,add o at position 5,flow_matching,0.3,2.0,43,181
89,add,6.0,=,,CCC(Ho\5C,CCC(Ho=\5C,10,add = at position 6,flow_matching,0.3,2.0,43,181
90,replace,4.0,=,H,CCC(Ho=\5C,CCC(=o=\5C,10,replace H at position 4 with =,flow_matching,0.3,2.0,43,181
91,add,10.0,#,,CCC(=o=\5C,CCC(=o=\5C#,11,add # at position 10,flow_matching,0.3,2.0,43,181
92,replace,3.0,s,(,CCC(=o=\5C#,CCCs=o=\5C#,11,replace ( at position 3 with s,flow_matching,0.3,2.0,43,181
93,replace,2.0,[,C,CCCs=o=\5C#,CC[s=o=\5C#,11,replace C at position 2 with [,flow_matching,0.3,2.0,43,181
94,remove,1.0,C,,CC[s=o=\5C#,C[s=o=\5C#,10,remove C from position 1,flow_matching,0.3,2.0,43,181
95,add,5.0,[,,C[s=o=\5C#,C[s=o[=\5C#,11,add [ at position 5,flow_matching,0.3,2.0,43,181
96,replace,6.0,O,=,C[s=o[=\5C#,C[s=o[O\5C#,11,replace = at position 6 with O,flow_matching,0.3,2.0,43,181
97,add,1.0,o,,C[s=o[O\5C#,Co[s=o[O\5C#,12,add o at position 1,flow_matching,0.3,2.0,43,181
98,replace,1.0,C,o,Co[s=o[O\5C#,CC[s=o[O\5C#,12,replace o at position 1 with C,flow_matching,0.3,2.0,43,181
99,replace,11.0,n,#,CC[s=o[O\5C#,CC[s=o[O\5Cn,12,replace # at position 11 with n,flow_matching,0.3,2.0,43,181
100,replace,2.0,C,[,CC[s=o[O\5Cn,CCCs=o[O\5Cn,12,replace [ at position 2 with C,flow_matching,0.3,2.0,43,181
101,add,5.0,H,,CCCs=o[O\5Cn,CCCs=Ho[O\5Cn,13,add H at position 5,flow_matching,0.3,2.0,43,181
102,add,0.0,n,,CCCs=Ho[O\5Cn,nCCCs=Ho[O\5Cn,14,add n at position 0,flow_matching,0.3,2.0,43,181
103,replace,0.0,C,n,nCCCs=Ho[O\5Cn,CCCCs=Ho[O\5Cn,14,replace n at position 0 with C,flow_matching,0.3,2.0,43,181
104,replace,3.0,(,C,CCCCs=Ho[O\5Cn,CCC(s=Ho[O\5Cn,14,replace C at position 3 with (,flow_matching,0.3,2.0,43,181
105,add,2.0,-,,CCC(s=Ho[O\5Cn,CC-C(s=Ho[O\5Cn,15,add - at position 2,flow_matching,0.3,2.0,43,181
106,remove,11.0,\,,CC-C(s=Ho[O\5Cn,CC-C(s=Ho[O5Cn,14,remove \ from position 11,flow_matching,0.3,2.0,43,181
107,replace,4.0,7,(,CC-C(s=Ho[O5Cn,CC-C7s=Ho[O5Cn,14,replace ( at position 4 with 7,flow_matching,0.3,2.0,43,181
108,add,8.0,7,,CC-C7s=Ho[O5Cn,CC-C7s=H7o[O5Cn,15,add 7 at position 8,flow_matching,0.3,2.0,43,181
109,remove,9.0,o,,CC-C7s=H7o[O5Cn,CC-C7s=H7[O5Cn,14,remove o from position 9,flow_matching,0.3,2.0,43,181
110,replace,0.0,\,C,CC-C7s=H7[O5Cn,\C-C7s=H7[O5Cn,14,replace C at position 0 with \,flow_matching,0.3,2.0,43,181
111,replace,13.0,-,n,\C-C7s=H7[O5Cn,\C-C7s=H7[O5C-,14,replace n at position 13 with -,flow_matching,0.3,2.0,43,181
112,replace,5.0,#,s,\C-C7s=H7[O5C-,\C-C7#=H7[O5C-,14,replace s at position 5 with #,flow_matching,0.3,2.0,43,181
113,add,13.0,o,,\C-C7#=H7[O5C-,\C-C7#=H7[O5Co-,15,add o at position 13,flow_matching,0.3,2.0,43,181
114,add,12.0,S,,\C-C7#=H7[O5Co-,\C-C7#=H7[O5SCo-,16,add S at position 12,flow_matching,0.3,2.0,43,181
115,add,12.0,I,,\C-C7#=H7[O5SCo-,\C-C7#=H7[O5ISCo-,17,add I at position 12,flow_matching,0.3,2.0,43,181
116,replace,8.0,+,7,\C-C7#=H7[O5ISCo-,\C-C7#=H+[O5ISCo-,17,replace 7 at position 8 with +,flow_matching,0.3,2.0,43,181
117,replace,15.0,I,o,\C-C7#=H+[O5ISCo-,\C-C7#=H+[O5ISCI-,17,replace o at position 15 with I,flow_matching,0.3,2.0,43,181
118,replace,0.0,C,\,\C-C7#=H+[O5ISCI-,CC-C7#=H+[O5ISCI-,17,replace \ at position 0 with C,flow_matching,0.3,2.0,43,181
119,replace,2.0,C,-,CC-C7#=H+[O5ISCI-,CCCC7#=H+[O5ISCI-,17,replace - at position 2 with C,flow_matching,0.3,2.0,43,181
120,replace,3.0,(,C,CCCC7#=H+[O5ISCI-,CCC(7#=H+[O5ISCI-,17,replace C at position 3 with (,flow_matching,0.3,2.0,43,181
121,add,0.0,6,,CCC(7#=H+[O5ISCI-,6CCC(7#=H+[O5ISCI-,18,add 6 at position 0,flow_matching,0.3,2.0,43,181
122,replace,0.0,C,6,6CCC(7#=H+[O5ISCI-,CCCC(7#=H+[O5ISCI-,18,replace 6 at position 0 with C,flow_matching,0.3,2.0,43,181
123,replace,3.0,(,C,CCCC(7#=H+[O5ISCI-,CCC((7#=H+[O5ISCI-,18,replace C at position 3 with (,flow_matching,0.3,2.0,43,181
124,replace,16.0,c,I,CCC((7#=H+[O5ISCI-,CCC((7#=H+[O5ISCc-,18,replace I at position 16 with c,flow_matching,0.3,2.0,43,181
125,replace,4.0,=,(,CCC((7#=H+[O5ISCc-,CCC(=7#=H+[O5ISCc-,18,replace ( at position 4 with =,flow_matching,0.3,2.0,43,181
126,remove,5.0,7,,CCC(=7#=H+[O5ISCc-,CCC(=#=H+[O5ISCc-,17,remove 7 from position 5,flow_matching,0.3,2.0,43,181
127,replace,2.0,5,C,CCC(=#=H+[O5ISCc-,CC5(=#=H+[O5ISCc-,17,replace C at position 2 with 5,flow_matching,0.3,2.0,43,181
128,replace,2.0,C,5,CC5(=#=H+[O5ISCc-,CCC(=#=H+[O5ISCc-,17,replace 5 at position 2 with C,flow_matching,0.3,2.0,43,181
129,add,9.0,=,,CCC(=#=H+[O5ISCc-,CCC(=#=H+=[O5ISCc-,18,add = at position 9,flow_matching,0.3,2.0,43,181
130,remove,10.0,[,,CCC(=#=H+=[O5ISCc-,CCC(=#=H+=O5ISCc-,17,remove [ from position 10,flow_matching,0.3,2.0,43,181
131,remove,8.0,+,,CCC(=#=H+=O5ISCc-,CCC(=#=H=O5ISCc-,16,remove + from position 8,flow_matching,0.3,2.0,43,181
132,replace,9.0,B,O,CCC(=#=H=O5ISCc-,CCC(=#=H=B5ISCc-,16,replace O at position 9 with B,flow_matching,0.3,2.0,43,181
133,replace,5.0,O,#,CCC(=#=H=B5ISCc-,CCC(=O=H=B5ISCc-,16,replace # at position 5 with O,flow_matching,0.3,2.0,43,181
134,replace,7.0,O,H,CCC(=O=H=B5ISCc-,CCC(=O=O=B5ISCc-,16,replace H at position 7 with O,flow_matching,0.3,2.0,43,181
135,add,1.0,I,,CCC(=O=O=B5ISCc-,CICC(=O=O=B5ISCc-,17,add I at position 1,flow_matching,0.3,2.0,43,181
136,replace,11.0,r,5,CICC(=O=O=B5ISCc-,CICC(=O=O=BrISCc-,17,replace 5 at position 11 with r,flow_matching,0.3,2.0,43,181
137,replace,7.0,I,=,CICC(=O=O=BrISCc-,CICC(=OIO=BrISCc-,17,replace = at position 7 with I,flow_matching,0.3,2.0,43,181
138,remove,14.0,C,,CICC(=OIO=BrISCc-,CICC(=OIO=BrISc-,16,remove C from position 14,flow_matching,0.3,2.0,43,181
139,add,16.0,l,,CICC(=OIO=BrISc-,CICC(=OIO=BrISc-l,17,add l at position 16,flow_matching,0.3,2.0,43,181
140,remove,13.0,S,,CICC(=OIO=BrISc-l,CICC(=OIO=BrIc-l,16,remove S from position 13,flow_matching,0.3,2.0,43,181
141,replace,1.0,C,I,CICC(=OIO=BrIc-l,CCCC(=OIO=BrIc-l,16,replace I at position 1 with C,flow_matching,0.3,2.0,43,181
142,replace,3.0,(,C,CCCC(=OIO=BrIc-l,CCC((=OIO=BrIc-l,16,replace C at position 3 with (,flow_matching,0.3,2.0,43,181
143,replace,4.0,=,(,CCC((=OIO=BrIc-l,CCC(==OIO=BrIc-l,16,replace ( at position 4 with =,flow_matching,0.3,2.0,43,181
144,replace,5.0,O,=,CCC(==OIO=BrIc-l,CCC(=OOIO=BrIc-l,16,replace = at position 5 with O,flow_matching,0.3,2.0,43,181
145,replace,6.0,),O,CCC(=OOIO=BrIc-l,CCC(=O)IO=BrIc-l,16,replace O at position 6 with ),flow_matching,0.3,2.0,43,181
146,replace,7.0,N,I,CCC(=O)IO=BrIc-l,CCC(=O)NO=BrIc-l,16,replace I at position 7 with N,flow_matching,0.3,2.0,43,181
147,replace,8.0,1,O,CCC(=O)NO=BrIc-l,CCC(=O)N1=BrIc-l,16,replace O at position 8 with 1,flow_matching,0.3,2.0,43,181
148,replace,9.0,C,=,CCC(=O)N1=BrIc-l,CCC(=O)N1CBrIc-l,16,replace = at position 9 with C,flow_matching,0.3,2.0,43,181
149,replace,10.0,C,B,CCC(=O)N1CBrIc-l,CCC(=O)N1CCrIc-l,16,replace B at position 10 with C,flow_matching,0.3,2.0,43,181
150,replace,11.0,C,r,CCC(=O)N1CCrIc-l,CCC(=O)N1CCCIc-l,16,replace r at position 11 with C,flow_matching,0.3,2.0,43,181
151,replace,12.0,(,I,CCC(=O)N1CCCIc-l,CCC(=O)N1CCC(c-l,16,replace I at position 12 with (,flow_matching,0.3,2.0,43,181
152,replace,13.0,[,c,CCC(=O)N1CCC(c-l,CCC(=O)N1CCC([-l,16,replace c at position 13 with [,flow_matching,0.3,2.0,43,181
153,replace,14.0,N,-,CCC(=O)N1CCC([-l,CCC(=O)N1CCC([Nl,16,replace - at position 14 with N,flow_matching,0.3,2.0,43,181
154,replace,15.0,H,l,CCC(=O)N1CCC([Nl,CCC(=O)N1CCC([NH,16,replace l at position 15 with H,flow_matching,0.3,2.0,43,181
155,add,16.0,+,,CCC(=O)N1CCC([NH,CCC(=O)N1CCC([NH+,17,add + at position 16,flow_matching,0.3,2.0,43,181
156,add,17.0,],,CCC(=O)N1CCC([NH+,CCC(=O)N1CCC([NH+],18,add ] at position 17,flow_matching,0.3,2.0,43,181
157,add,18.0,(,,CCC(=O)N1CCC([NH+],CCC(=O)N1CCC([NH+](,19,add ( at position 18,flow_matching,0.3,2.0,43,181
158,add,19.0,C,,CCC(=O)N1CCC([NH+](,CCC(=O)N1CCC([NH+](C,20,add C at position 19,flow_matching,0.3,2.0,43,181
159,add,20.0,),,CCC(=O)N1CCC([NH+](C,CCC(=O)N1CCC([NH+](C),21,add ) at position 20,flow_matching,0.3,2.0,43,181
160,add,21.0,C,,CCC(=O)N1CCC([NH+](C),CCC(=O)N1CCC([NH+](C)C,22,add C at position 21,flow_matching,0.3,2.0,43,181
161,add,22.0,c,,CCC(=O)N1CCC([NH+](C)C,CCC(=O)N1CCC([NH+](C)Cc,23,add c at position 22,flow_matching,0.3,2.0,43,181
162,add,23.0,2,,CCC(=O)N1CCC([NH+](C)Cc,CCC(=O)N1CCC([NH+](C)Cc2,24,add 2 at position 23,flow_matching,0.3,2.0,43,181
163,add,24.0,c,,CCC(=O)N1CCC([NH+](C)Cc2,CCC(=O)N1CCC([NH+](C)Cc2c,25,add c at position 24,flow_matching,0.3,2.0,43,181
164,add,25.0,c,,CCC(=O)N1CCC([NH+](C)Cc2c,CCC(=O)N1CCC([NH+](C)Cc2cc,26,add c at position 25,flow_matching,0.3,2.0,43,181
165,add,26.0,c,,CCC(=O)N1CCC([NH+](C)Cc2cc,CCC(=O)N1CCC([NH+](C)Cc2ccc,27,add c at position 26,flow_matching,0.3,2.0,43,181
166,add,27.0,(,,CCC(=O)N1CCC([NH+](C)Cc2ccc,CCC(=O)N1CCC([NH+](C)Cc2ccc(,28,add ( at position 27,flow_matching,0.3,2.0,43,181
167,add,28.0,S,,CCC(=O)N1CCC([NH+](C)Cc2ccc(,CCC(=O)N1CCC([NH+](C)Cc2ccc(S,29,add S at position 28,flow_matching,0.3,2.0,43,181
168,add,29.0,C,,CCC(=O)N1CCC([NH+](C)Cc2ccc(S,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC,30,add C at position 29,flow_matching,0.3,2.0,43,181
169,add,30.0,),,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC),31,add ) at position 30,flow_matching,0.3,2.0,43,181
170,add,31.0,c,,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC),CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c,32,add c at position 31,flow_matching,0.3,2.0,43,181
171,add,32.0,(,,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(,33,add ( at position 32,flow_matching,0.3,2.0,43,181
172,add,33.0,O,,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(O,34,add O at position 33,flow_matching,0.3,2.0,43,181
173,add,34.0,C,,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(O,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC,35,add C at position 34,flow_matching,0.3,2.0,43,181
174,add,35.0,),,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC),36,add ) at position 35,flow_matching,0.3,2.0,43,181
175,add,36.0,c,,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC),CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c,37,add c at position 36,flow_matching,0.3,2.0,43,181
176,add,37.0,2,,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2,38,add 2 at position 37,flow_matching,0.3,2.0,43,181
177,add,38.0,),,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2),39,add ) at position 38,flow_matching,0.3,2.0,43,181
178,add,39.0,C,,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2),CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2)C,40,add C at position 39,flow_matching,0.3,2.0,43,181
179,add,40.0,C,,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2)C,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2)CC,41,add C at position 40,flow_matching,0.3,2.0,43,181
180,add,41.0,1,,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2)CC,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2)CC1,42,add 1 at position 41,flow_matching,0.3,2.0,43,181
181,add,42.0,"
",,CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2)CC1,"CCC(=O)N1CCC([NH+](C)Cc2ccc(SC)c(OC)c2)CC1
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,181
