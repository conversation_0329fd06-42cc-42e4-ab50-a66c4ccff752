step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,59,265
1,add,0.0,4,,,4,1,add 4 at position 0,flow_matching,0.3,2.0,59,265
2,replace,0.0,5,4,4,5,1,replace 4 at position 0 with 5,flow_matching,0.3,2.0,59,265
3,add,0.0,s,,5,s5,2,add s at position 0,flow_matching,0.3,2.0,59,265
4,remove,1.0,5,,s5,s,1,remove 5 from position 1,flow_matching,0.3,2.0,59,265
5,replace,0.0,C,s,s,C,1,replace s at position 0 with C,flow_matching,0.3,2.0,59,265
6,add,0.0,r,,<PERSON>,rC,2,add r at position 0,flow_matching,0.3,2.0,59,265
7,remove,0.0,r,,rC,C,1,remove r from position 0,flow_matching,0.3,2.0,59,265
8,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,59,265
9,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,59,265
10,add,1.0,+,,6,6+,2,add + at position 1,flow_matching,0.3,2.0,59,265
11,replace,0.0,),6,6+,)+,2,replace 6 at position 0 with ),flow_matching,0.3,2.0,59,265
12,replace,0.0,F,),)+,F+,2,replace ) at position 0 with F,flow_matching,0.3,2.0,59,265
13,add,1.0,),,F+,F)+,3,add ) at position 1,flow_matching,0.3,2.0,59,265
14,replace,0.0,),F,F)+,))+,3,replace F at position 0 with ),flow_matching,0.3,2.0,59,265
15,remove,1.0,),,))+,)+,2,remove ) from position 1,flow_matching,0.3,2.0,59,265
16,replace,0.0,S,),)+,S+,2,replace ) at position 0 with S,flow_matching,0.3,2.0,59,265
17,replace,0.0,C,S,S+,C+,2,replace S at position 0 with C,flow_matching,0.3,2.0,59,265
18,remove,1.0,+,,C+,C,1,remove + from position 1,flow_matching,0.3,2.0,59,265
19,replace,0.0,2,C,C,2,1,replace C at position 0 with 2,flow_matching,0.3,2.0,59,265
20,replace,0.0,C,2,2,C,1,replace 2 at position 0 with C,flow_matching,0.3,2.0,59,265
21,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,59,265
22,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,59,265
23,add,0.0,5,,COc,5COc,4,add 5 at position 0,flow_matching,0.3,2.0,59,265
24,replace,0.0,C,5,5COc,CCOc,4,replace 5 at position 0 with C,flow_matching,0.3,2.0,59,265
25,add,3.0,s,,CCOc,CCOsc,5,add s at position 3,flow_matching,0.3,2.0,59,265
26,add,5.0,],,CCOsc,CCOsc],6,add ] at position 5,flow_matching,0.3,2.0,59,265
27,replace,5.0,6,],CCOsc],CCOsc6,6,replace ] at position 5 with 6,flow_matching,0.3,2.0,59,265
28,add,1.0,c,,CCOsc6,CcCOsc6,7,add c at position 1,flow_matching,0.3,2.0,59,265
29,replace,1.0,s,c,CcCOsc6,CsCOsc6,7,replace c at position 1 with s,flow_matching,0.3,2.0,59,265
30,replace,1.0,+,s,CsCOsc6,C+COsc6,7,replace s at position 1 with +,flow_matching,0.3,2.0,59,265
31,add,1.0,H,,C+COsc6,CH+COsc6,8,add H at position 1,flow_matching,0.3,2.0,59,265
32,add,0.0,1,,CH+COsc6,1CH+COsc6,9,add 1 at position 0,flow_matching,0.3,2.0,59,265
33,replace,0.0,C,1,1CH+COsc6,CCH+COsc6,9,replace 1 at position 0 with C,flow_matching,0.3,2.0,59,265
34,add,2.0,-,,CCH+COsc6,CC-H+COsc6,10,add - at position 2,flow_matching,0.3,2.0,59,265
35,add,6.0,5,,CC-H+COsc6,CC-H+C5Osc6,11,add 5 at position 6,flow_matching,0.3,2.0,59,265
36,add,7.0,(,,CC-H+C5Osc6,CC-H+C5(Osc6,12,add ( at position 7,flow_matching,0.3,2.0,59,265
37,add,0.0,(,,CC-H+C5(Osc6,(CC-H+C5(Osc6,13,add ( at position 0,flow_matching,0.3,2.0,59,265
38,remove,9.0,O,,(CC-H+C5(Osc6,(CC-H+C5(sc6,12,remove O from position 9,flow_matching,0.3,2.0,59,265
39,add,5.0,2,,(CC-H+C5(sc6,(CC-H2+C5(sc6,13,add 2 at position 5,flow_matching,0.3,2.0,59,265
40,add,10.0,l,,(CC-H2+C5(sc6,(CC-H2+C5(lsc6,14,add l at position 10,flow_matching,0.3,2.0,59,265
41,add,8.0,+,,(CC-H2+C5(lsc6,(CC-H2+C+5(lsc6,15,add + at position 8,flow_matching,0.3,2.0,59,265
42,add,2.0,2,,(CC-H2+C+5(lsc6,(C2C-H2+C+5(lsc6,16,add 2 at position 2,flow_matching,0.3,2.0,59,265
43,remove,4.0,-,,(C2C-H2+C+5(lsc6,(C2CH2+C+5(lsc6,15,remove - from position 4,flow_matching,0.3,2.0,59,265
44,replace,2.0,+,2,(C2CH2+C+5(lsc6,(C+CH2+C+5(lsc6,15,replace 2 at position 2 with +,flow_matching,0.3,2.0,59,265
45,remove,5.0,2,,(C+CH2+C+5(lsc6,(C+CH+C+5(lsc6,14,remove 2 from position 5,flow_matching,0.3,2.0,59,265
46,replace,0.0,C,(,(C+CH+C+5(lsc6,CC+CH+C+5(lsc6,14,replace ( at position 0 with C,flow_matching,0.3,2.0,59,265
47,add,5.0,7,,CC+CH+C+5(lsc6,CC+CH7+C+5(lsc6,15,add 7 at position 5,flow_matching,0.3,2.0,59,265
48,replace,1.0,O,C,CC+CH7+C+5(lsc6,CO+CH7+C+5(lsc6,15,replace C at position 1 with O,flow_matching,0.3,2.0,59,265
49,replace,2.0,c,+,CO+CH7+C+5(lsc6,COcCH7+C+5(lsc6,15,replace + at position 2 with c,flow_matching,0.3,2.0,59,265
50,add,14.0,S,,COcCH7+C+5(lsc6,COcCH7+C+5(lscS6,16,add S at position 14,flow_matching,0.3,2.0,59,265
51,add,14.0,N,,COcCH7+C+5(lscS6,COcCH7+C+5(lscNS6,17,add N at position 14,flow_matching,0.3,2.0,59,265
52,replace,3.0,1,C,COcCH7+C+5(lscNS6,COc1H7+C+5(lscNS6,17,replace C at position 3 with 1,flow_matching,0.3,2.0,59,265
53,add,7.0,+,,COc1H7+C+5(lscNS6,COc1H7++C+5(lscNS6,18,add + at position 7,flow_matching,0.3,2.0,59,265
54,replace,4.0,F,H,COc1H7++C+5(lscNS6,COc1F7++C+5(lscNS6,18,replace H at position 4 with F,flow_matching,0.3,2.0,59,265
55,remove,13.0,s,,COc1F7++C+5(lscNS6,COc1F7++C+5(lcNS6,17,remove s from position 13,flow_matching,0.3,2.0,59,265
56,replace,4.0,c,F,COc1F7++C+5(lcNS6,COc1c7++C+5(lcNS6,17,replace F at position 4 with c,flow_matching,0.3,2.0,59,265
57,replace,5.0,c,7,COc1c7++C+5(lcNS6,COc1cc++C+5(lcNS6,17,replace 7 at position 5 with c,flow_matching,0.3,2.0,59,265
58,replace,6.0,F,+,COc1cc++C+5(lcNS6,COc1ccF+C+5(lcNS6,17,replace + at position 6 with F,flow_matching,0.3,2.0,59,265
59,add,10.0,O,,COc1ccF+C+5(lcNS6,COc1ccF+C+O5(lcNS6,18,add O at position 10,flow_matching,0.3,2.0,59,265
60,remove,13.0,l,,COc1ccF+C+O5(lcNS6,COc1ccF+C+O5(cNS6,17,remove l from position 13,flow_matching,0.3,2.0,59,265
61,add,17.0,l,,COc1ccF+C+O5(cNS6,COc1ccF+C+O5(cNS6l,18,add l at position 17,flow_matching,0.3,2.0,59,265
62,add,0.0,N,,COc1ccF+C+O5(cNS6l,NCOc1ccF+C+O5(cNS6l,19,add N at position 0,flow_matching,0.3,2.0,59,265
63,replace,4.0,B,1,NCOc1ccF+C+O5(cNS6l,NCOcBccF+C+O5(cNS6l,19,replace 1 at position 4 with B,flow_matching,0.3,2.0,59,265
64,add,6.0,],,NCOcBccF+C+O5(cNS6l,NCOcBc]cF+C+O5(cNS6l,20,add ] at position 6,flow_matching,0.3,2.0,59,265
65,remove,11.0,+,,NCOcBc]cF+C+O5(cNS6l,NCOcBc]cF+CO5(cNS6l,19,remove + from position 11,flow_matching,0.3,2.0,59,265
66,replace,0.0,C,N,NCOcBc]cF+CO5(cNS6l,CCOcBc]cF+CO5(cNS6l,19,replace N at position 0 with C,flow_matching,0.3,2.0,59,265
67,remove,15.0,N,,CCOcBc]cF+CO5(cNS6l,CCOcBc]cF+CO5(cS6l,18,remove N from position 15,flow_matching,0.3,2.0,59,265
68,remove,17.0,l,,CCOcBc]cF+CO5(cS6l,CCOcBc]cF+CO5(cS6,17,remove l from position 17,flow_matching,0.3,2.0,59,265
69,replace,1.0,O,C,CCOcBc]cF+CO5(cS6,COOcBc]cF+CO5(cS6,17,replace C at position 1 with O,flow_matching,0.3,2.0,59,265
70,remove,16.0,6,,COOcBc]cF+CO5(cS6,COOcBc]cF+CO5(cS,16,remove 6 from position 16,flow_matching,0.3,2.0,59,265
71,remove,11.0,O,,COOcBc]cF+CO5(cS,COOcBc]cF+C5(cS,15,remove O from position 11,flow_matching,0.3,2.0,59,265
72,add,4.0,S,,COOcBc]cF+C5(cS,COOcSBc]cF+C5(cS,16,add S at position 4,flow_matching,0.3,2.0,59,265
73,replace,8.0,r,c,COOcSBc]cF+C5(cS,COOcSBc]rF+C5(cS,16,replace c at position 8 with r,flow_matching,0.3,2.0,59,265
74,add,12.0,+,,COOcSBc]rF+C5(cS,COOcSBc]rF+C+5(cS,17,add + at position 12,flow_matching,0.3,2.0,59,265
75,replace,2.0,c,O,COOcSBc]rF+C+5(cS,COccSBc]rF+C+5(cS,17,replace O at position 2 with c,flow_matching,0.3,2.0,59,265
76,add,2.0,\,,COccSBc]rF+C+5(cS,CO\ccSBc]rF+C+5(cS,18,add \ at position 2,flow_matching,0.3,2.0,59,265
77,add,18.0,\,,CO\ccSBc]rF+C+5(cS,CO\ccSBc]rF+C+5(cS\,19,add \ at position 18,flow_matching,0.3,2.0,59,265
78,add,8.0,(,,CO\ccSBc]rF+C+5(cS\,CO\ccSBc(]rF+C+5(cS\,20,add ( at position 8,flow_matching,0.3,2.0,59,265
79,add,6.0,o,,CO\ccSBc(]rF+C+5(cS\,CO\ccSoBc(]rF+C+5(cS\,21,add o at position 6,flow_matching,0.3,2.0,59,265
80,add,7.0,1,,CO\ccSoBc(]rF+C+5(cS\,CO\ccSo1Bc(]rF+C+5(cS\,22,add 1 at position 7,flow_matching,0.3,2.0,59,265
81,replace,2.0,c,\,CO\ccSo1Bc(]rF+C+5(cS\,COcccSo1Bc(]rF+C+5(cS\,22,replace \ at position 2 with c,flow_matching,0.3,2.0,59,265
82,remove,5.0,S,,COcccSo1Bc(]rF+C+5(cS\,COccco1Bc(]rF+C+5(cS\,21,remove S from position 5,flow_matching,0.3,2.0,59,265
83,add,8.0,n,,COccco1Bc(]rF+C+5(cS\,COccco1Bnc(]rF+C+5(cS\,22,add n at position 8,flow_matching,0.3,2.0,59,265
84,add,2.0,c,,COccco1Bnc(]rF+C+5(cS\,COcccco1Bnc(]rF+C+5(cS\,23,add c at position 2,flow_matching,0.3,2.0,59,265
85,replace,3.0,1,c,COcccco1Bnc(]rF+C+5(cS\,COc1cco1Bnc(]rF+C+5(cS\,23,replace c at position 3 with 1,flow_matching,0.3,2.0,59,265
86,replace,6.0,/,o,COc1cco1Bnc(]rF+C+5(cS\,COc1cc/1Bnc(]rF+C+5(cS\,23,replace o at position 6 with /,flow_matching,0.3,2.0,59,265
87,replace,13.0,(,r,COc1cc/1Bnc(]rF+C+5(cS\,COc1cc/1Bnc(](F+C+5(cS\,23,replace r at position 13 with (,flow_matching,0.3,2.0,59,265
88,remove,8.0,B,,COc1cc/1Bnc(](F+C+5(cS\,COc1cc/1nc(](F+C+5(cS\,22,remove B from position 8,flow_matching,0.3,2.0,59,265
89,replace,6.0,2,/,COc1cc/1nc(](F+C+5(cS\,COc1cc21nc(](F+C+5(cS\,22,replace / at position 6 with 2,flow_matching,0.3,2.0,59,265
90,replace,4.0,O,c,COc1cc21nc(](F+C+5(cS\,COc1Oc21nc(](F+C+5(cS\,22,replace c at position 4 with O,flow_matching,0.3,2.0,59,265
91,replace,11.0,c,],COc1Oc21nc(](F+C+5(cS\,COc1Oc21nc(c(F+C+5(cS\,22,replace ] at position 11 with c,flow_matching,0.3,2.0,59,265
92,add,11.0,-,,COc1Oc21nc(c(F+C+5(cS\,COc1Oc21nc(-c(F+C+5(cS\,23,add - at position 11,flow_matching,0.3,2.0,59,265
93,add,16.0,6,,COc1Oc21nc(-c(F+C+5(cS\,COc1Oc21nc(-c(F+6C+5(cS\,24,add 6 at position 16,flow_matching,0.3,2.0,59,265
94,replace,1.0,7,O,COc1Oc21nc(-c(F+6C+5(cS\,C7c1Oc21nc(-c(F+6C+5(cS\,24,replace O at position 1 with 7,flow_matching,0.3,2.0,59,265
95,replace,1.0,O,7,C7c1Oc21nc(-c(F+6C+5(cS\,COc1Oc21nc(-c(F+6C+5(cS\,24,replace 7 at position 1 with O,flow_matching,0.3,2.0,59,265
96,replace,17.0,F,C,COc1Oc21nc(-c(F+6C+5(cS\,COc1Oc21nc(-c(F+6F+5(cS\,24,replace C at position 17 with F,flow_matching,0.3,2.0,59,265
97,remove,2.0,c,,COc1Oc21nc(-c(F+6F+5(cS\,CO1Oc21nc(-c(F+6F+5(cS\,23,remove c from position 2,flow_matching,0.3,2.0,59,265
98,add,6.0,4,,CO1Oc21nc(-c(F+6F+5(cS\,CO1Oc241nc(-c(F+6F+5(cS\,24,add 4 at position 6,flow_matching,0.3,2.0,59,265
99,replace,11.0,#,-,CO1Oc241nc(-c(F+6F+5(cS\,CO1Oc241nc(#c(F+6F+5(cS\,24,replace - at position 11 with #,flow_matching,0.3,2.0,59,265
100,remove,16.0,6,,CO1Oc241nc(#c(F+6F+5(cS\,CO1Oc241nc(#c(F+F+5(cS\,23,remove 6 from position 16,flow_matching,0.3,2.0,59,265
101,replace,9.0,),c,CO1Oc241nc(#c(F+F+5(cS\,CO1Oc241n)(#c(F+F+5(cS\,23,replace c at position 9 with ),flow_matching,0.3,2.0,59,265
102,replace,2.0,c,1,CO1Oc241n)(#c(F+F+5(cS\,COcOc241n)(#c(F+F+5(cS\,23,replace 1 at position 2 with c,flow_matching,0.3,2.0,59,265
103,add,14.0,n,,COcOc241n)(#c(F+F+5(cS\,COcOc241n)(#c(nF+F+5(cS\,24,add n at position 14,flow_matching,0.3,2.0,59,265
104,replace,5.0,C,2,COcOc241n)(#c(nF+F+5(cS\,COcOcC41n)(#c(nF+F+5(cS\,24,replace 2 at position 5 with C,flow_matching,0.3,2.0,59,265
105,add,21.0,C,,COcOcC41n)(#c(nF+F+5(cS\,COcOcC41n)(#c(nF+F+5(CcS\,25,add C at position 21,flow_matching,0.3,2.0,59,265
106,remove,20.0,(,,COcOcC41n)(#c(nF+F+5(CcS\,COcOcC41n)(#c(nF+F+5CcS\,24,remove ( from position 20,flow_matching,0.3,2.0,59,265
107,add,24.0,-,,COcOcC41n)(#c(nF+F+5CcS\,COcOcC41n)(#c(nF+F+5CcS\-,25,add - at position 24,flow_matching,0.3,2.0,59,265
108,replace,3.0,1,O,COcOcC41n)(#c(nF+F+5CcS\-,COc1cC41n)(#c(nF+F+5CcS\-,25,replace O at position 3 with 1,flow_matching,0.3,2.0,59,265
109,add,9.0,I,,COc1cC41n)(#c(nF+F+5CcS\-,COc1cC41nI)(#c(nF+F+5CcS\-,26,add I at position 9,flow_matching,0.3,2.0,59,265
110,remove,23.0,S,,COc1cC41nI)(#c(nF+F+5CcS\-,COc1cC41nI)(#c(nF+F+5Cc\-,25,remove S from position 23,flow_matching,0.3,2.0,59,265
111,add,15.0,S,,COc1cC41nI)(#c(nF+F+5Cc\-,COc1cC41nI)(#c(SnF+F+5Cc\-,26,add S at position 15,flow_matching,0.3,2.0,59,265
112,replace,19.0,B,F,COc1cC41nI)(#c(SnF+F+5Cc\-,COc1cC41nI)(#c(SnF+B+5Cc\-,26,replace F at position 19 with B,flow_matching,0.3,2.0,59,265
113,add,15.0,S,,COc1cC41nI)(#c(SnF+B+5Cc\-,COc1cC41nI)(#c(SSnF+B+5Cc\-,27,add S at position 15,flow_matching,0.3,2.0,59,265
114,replace,5.0,c,C,COc1cC41nI)(#c(SSnF+B+5Cc\-,COc1cc41nI)(#c(SSnF+B+5Cc\-,27,replace C at position 5 with c,flow_matching,0.3,2.0,59,265
115,replace,23.0,),C,COc1cc41nI)(#c(SSnF+B+5Cc\-,COc1cc41nI)(#c(SSnF+B+5)c\-,27,replace C at position 23 with ),flow_matching,0.3,2.0,59,265
116,add,4.0,O,,COc1cc41nI)(#c(SSnF+B+5)c\-,COc1Occ41nI)(#c(SSnF+B+5)c\-,28,add O at position 4,flow_matching,0.3,2.0,59,265
117,replace,4.0,c,O,COc1Occ41nI)(#c(SSnF+B+5)c\-,COc1ccc41nI)(#c(SSnF+B+5)c\-,28,replace O at position 4 with c,flow_matching,0.3,2.0,59,265
118,replace,6.0,2,c,COc1ccc41nI)(#c(SSnF+B+5)c\-,COc1cc241nI)(#c(SSnF+B+5)c\-,28,replace c at position 6 with 2,flow_matching,0.3,2.0,59,265
119,replace,7.0,c,4,COc1cc241nI)(#c(SSnF+B+5)c\-,COc1cc2c1nI)(#c(SSnF+B+5)c\-,28,replace 4 at position 7 with c,flow_matching,0.3,2.0,59,265
120,replace,24.0,7,),COc1cc2c1nI)(#c(SSnF+B+5)c\-,COc1cc2c1nI)(#c(SSnF+B+57c\-,28,replace ) at position 24 with 7,flow_matching,0.3,2.0,59,265
121,replace,19.0,3,F,COc1cc2c1nI)(#c(SSnF+B+57c\-,COc1cc2c1nI)(#c(SSn3+B+57c\-,28,replace F at position 19 with 3,flow_matching,0.3,2.0,59,265
122,add,18.0,(,,COc1cc2c1nI)(#c(SSn3+B+57c\-,COc1cc2c1nI)(#c(SS(n3+B+57c\-,29,add ( at position 18,flow_matching,0.3,2.0,59,265
123,add,29.0,n,,COc1cc2c1nI)(#c(SS(n3+B+57c\-,COc1cc2c1nI)(#c(SS(n3+B+57c\-n,30,add n at position 29,flow_matching,0.3,2.0,59,265
124,remove,12.0,(,,COc1cc2c1nI)(#c(SS(n3+B+57c\-n,COc1cc2c1nI)#c(SS(n3+B+57c\-n,29,remove ( from position 12,flow_matching,0.3,2.0,59,265
125,replace,3.0,6,1,COc1cc2c1nI)#c(SS(n3+B+57c\-n,COc6cc2c1nI)#c(SS(n3+B+57c\-n,29,replace 1 at position 3 with 6,flow_matching,0.3,2.0,59,265
126,replace,20.0,(,+,COc6cc2c1nI)#c(SS(n3+B+57c\-n,COc6cc2c1nI)#c(SS(n3(B+57c\-n,29,replace + at position 20 with (,flow_matching,0.3,2.0,59,265
127,remove,2.0,c,,COc6cc2c1nI)#c(SS(n3(B+57c\-n,CO6cc2c1nI)#c(SS(n3(B+57c\-n,28,remove c from position 2,flow_matching,0.3,2.0,59,265
128,add,3.0,6,,CO6cc2c1nI)#c(SS(n3(B+57c\-n,CO66cc2c1nI)#c(SS(n3(B+57c\-n,29,add 6 at position 3,flow_matching,0.3,2.0,59,265
129,add,23.0,@,,CO66cc2c1nI)#c(SS(n3(B+57c\-n,CO66cc2c1nI)#c(SS(n3(B+@57c\-n,30,add @ at position 23,flow_matching,0.3,2.0,59,265
130,replace,9.0,B,n,CO66cc2c1nI)#c(SS(n3(B+@57c\-n,CO66cc2c1BI)#c(SS(n3(B+@57c\-n,30,replace n at position 9 with B,flow_matching,0.3,2.0,59,265
131,remove,25.0,7,,CO66cc2c1BI)#c(SS(n3(B+@57c\-n,CO66cc2c1BI)#c(SS(n3(B+@5c\-n,29,remove 7 from position 25,flow_matching,0.3,2.0,59,265
132,replace,19.0,1,3,CO66cc2c1BI)#c(SS(n3(B+@5c\-n,CO66cc2c1BI)#c(SS(n1(B+@5c\-n,29,replace 3 at position 19 with 1,flow_matching,0.3,2.0,59,265
133,remove,6.0,2,,CO66cc2c1BI)#c(SS(n1(B+@5c\-n,CO66ccc1BI)#c(SS(n1(B+@5c\-n,28,remove 2 from position 6,flow_matching,0.3,2.0,59,265
134,remove,0.0,C,,CO66ccc1BI)#c(SS(n1(B+@5c\-n,O66ccc1BI)#c(SS(n1(B+@5c\-n,27,remove C from position 0,flow_matching,0.3,2.0,59,265
135,remove,9.0,),,O66ccc1BI)#c(SS(n1(B+@5c\-n,O66ccc1BI#c(SS(n1(B+@5c\-n,26,remove ) from position 9,flow_matching,0.3,2.0,59,265
136,replace,0.0,C,O,O66ccc1BI#c(SS(n1(B+@5c\-n,C66ccc1BI#c(SS(n1(B+@5c\-n,26,replace O at position 0 with C,flow_matching,0.3,2.0,59,265
137,add,6.0,s,,C66ccc1BI#c(SS(n1(B+@5c\-n,C66cccs1BI#c(SS(n1(B+@5c\-n,27,add s at position 6,flow_matching,0.3,2.0,59,265
138,replace,13.0,-,S,C66cccs1BI#c(SS(n1(B+@5c\-n,C66cccs1BI#c(-S(n1(B+@5c\-n,27,replace S at position 13 with -,flow_matching,0.3,2.0,59,265
139,add,18.0,/,,C66cccs1BI#c(-S(n1(B+@5c\-n,C66cccs1BI#c(-S(n1/(B+@5c\-n,28,add / at position 18,flow_matching,0.3,2.0,59,265
140,replace,1.0,O,6,C66cccs1BI#c(-S(n1/(B+@5c\-n,CO6cccs1BI#c(-S(n1/(B+@5c\-n,28,replace 6 at position 1 with O,flow_matching,0.3,2.0,59,265
141,replace,14.0,I,S,CO6cccs1BI#c(-S(n1/(B+@5c\-n,CO6cccs1BI#c(-I(n1/(B+@5c\-n,28,replace S at position 14 with I,flow_matching,0.3,2.0,59,265
142,remove,20.0,B,,CO6cccs1BI#c(-I(n1/(B+@5c\-n,CO6cccs1BI#c(-I(n1/(+@5c\-n,27,remove B from position 20,flow_matching,0.3,2.0,59,265
143,replace,2.0,c,6,CO6cccs1BI#c(-I(n1/(+@5c\-n,COccccs1BI#c(-I(n1/(+@5c\-n,27,replace 6 at position 2 with c,flow_matching,0.3,2.0,59,265
144,replace,3.0,1,c,COccccs1BI#c(-I(n1/(+@5c\-n,COc1ccs1BI#c(-I(n1/(+@5c\-n,27,replace c at position 3 with 1,flow_matching,0.3,2.0,59,265
145,replace,11.0,4,c,COc1ccs1BI#c(-I(n1/(+@5c\-n,COc1ccs1BI#4(-I(n1/(+@5c\-n,27,replace c at position 11 with 4,flow_matching,0.3,2.0,59,265
146,add,0.0,#,,COc1ccs1BI#4(-I(n1/(+@5c\-n,#COc1ccs1BI#4(-I(n1/(+@5c\-n,28,add # at position 0,flow_matching,0.3,2.0,59,265
147,replace,12.0,N,4,#COc1ccs1BI#4(-I(n1/(+@5c\-n,#COc1ccs1BI#N(-I(n1/(+@5c\-n,28,replace 4 at position 12 with N,flow_matching,0.3,2.0,59,265
148,add,25.0,/,,#COc1ccs1BI#N(-I(n1/(+@5c\-n,#COc1ccs1BI#N(-I(n1/(+@5c/\-n,29,add / at position 25,flow_matching,0.3,2.0,59,265
149,replace,4.0,o,1,#COc1ccs1BI#N(-I(n1/(+@5c/\-n,#COcoccs1BI#N(-I(n1/(+@5c/\-n,29,replace 1 at position 4 with o,flow_matching,0.3,2.0,59,265
150,replace,0.0,C,#,#COcoccs1BI#N(-I(n1/(+@5c/\-n,CCOcoccs1BI#N(-I(n1/(+@5c/\-n,29,replace # at position 0 with C,flow_matching,0.3,2.0,59,265
151,replace,1.0,O,C,CCOcoccs1BI#N(-I(n1/(+@5c/\-n,COOcoccs1BI#N(-I(n1/(+@5c/\-n,29,replace C at position 1 with O,flow_matching,0.3,2.0,59,265
152,replace,2.0,c,O,COOcoccs1BI#N(-I(n1/(+@5c/\-n,COccoccs1BI#N(-I(n1/(+@5c/\-n,29,replace O at position 2 with c,flow_matching,0.3,2.0,59,265
153,add,28.0,\,,COccoccs1BI#N(-I(n1/(+@5c/\-n,COccoccs1BI#N(-I(n1/(+@5c/\-\n,30,add \ at position 28,flow_matching,0.3,2.0,59,265
154,replace,26.0,-,\,COccoccs1BI#N(-I(n1/(+@5c/\-\n,COccoccs1BI#N(-I(n1/(+@5c/--\n,30,replace \ at position 26 with -,flow_matching,0.3,2.0,59,265
155,replace,3.0,1,c,COccoccs1BI#N(-I(n1/(+@5c/--\n,COc1occs1BI#N(-I(n1/(+@5c/--\n,30,replace c at position 3 with 1,flow_matching,0.3,2.0,59,265
156,add,21.0,5,,COc1occs1BI#N(-I(n1/(+@5c/--\n,COc1occs1BI#N(-I(n1/(5+@5c/--\n,31,add 5 at position 21,flow_matching,0.3,2.0,59,265
157,add,21.0,1,,COc1occs1BI#N(-I(n1/(5+@5c/--\n,COc1occs1BI#N(-I(n1/(15+@5c/--\n,32,add 1 at position 21,flow_matching,0.3,2.0,59,265
158,remove,10.0,I,,COc1occs1BI#N(-I(n1/(15+@5c/--\n,COc1occs1B#N(-I(n1/(15+@5c/--\n,31,remove I from position 10,flow_matching,0.3,2.0,59,265
159,replace,4.0,c,o,COc1occs1B#N(-I(n1/(15+@5c/--\n,COc1cccs1B#N(-I(n1/(15+@5c/--\n,31,replace o at position 4 with c,flow_matching,0.3,2.0,59,265
160,replace,14.0,c,I,COc1cccs1B#N(-I(n1/(15+@5c/--\n,COc1cccs1B#N(-c(n1/(15+@5c/--\n,31,replace I at position 14 with c,flow_matching,0.3,2.0,59,265
161,replace,6.0,2,c,COc1cccs1B#N(-c(n1/(15+@5c/--\n,COc1cc2s1B#N(-c(n1/(15+@5c/--\n,31,replace c at position 6 with 2,flow_matching,0.3,2.0,59,265
162,add,12.0,+,,COc1cc2s1B#N(-c(n1/(15+@5c/--\n,COc1cc2s1B#N+(-c(n1/(15+@5c/--\n,32,add + at position 12,flow_matching,0.3,2.0,59,265
163,remove,30.0,\,,COc1cc2s1B#N+(-c(n1/(15+@5c/--\n,COc1cc2s1B#N+(-c(n1/(15+@5c/--n,31,remove \ from position 30,flow_matching,0.3,2.0,59,265
164,replace,7.0,c,s,COc1cc2s1B#N+(-c(n1/(15+@5c/--n,COc1cc2c1B#N+(-c(n1/(15+@5c/--n,31,replace s at position 7 with c,flow_matching,0.3,2.0,59,265
165,replace,8.0,(,1,COc1cc2c1B#N+(-c(n1/(15+@5c/--n,COc1cc2c(B#N+(-c(n1/(15+@5c/--n,31,replace 1 at position 8 with (,flow_matching,0.3,2.0,59,265
166,add,6.0,[,,COc1cc2c(B#N+(-c(n1/(15+@5c/--n,COc1cc[2c(B#N+(-c(n1/(15+@5c/--n,32,add [ at position 6,flow_matching,0.3,2.0,59,265
167,replace,16.0,B,c,COc1cc[2c(B#N+(-c(n1/(15+@5c/--n,COc1cc[2c(B#N+(-B(n1/(15+@5c/--n,32,replace c at position 16 with B,flow_matching,0.3,2.0,59,265
168,add,18.0,S,,COc1cc[2c(B#N+(-B(n1/(15+@5c/--n,COc1cc[2c(B#N+(-B(Sn1/(15+@5c/--n,33,add S at position 18,flow_matching,0.3,2.0,59,265
169,replace,6.0,2,[,COc1cc[2c(B#N+(-B(Sn1/(15+@5c/--n,COc1cc22c(B#N+(-B(Sn1/(15+@5c/--n,33,replace [ at position 6 with 2,flow_matching,0.3,2.0,59,265
170,add,1.0,r,,COc1cc22c(B#N+(-B(Sn1/(15+@5c/--n,CrOc1cc22c(B#N+(-B(Sn1/(15+@5c/--n,34,add r at position 1,flow_matching,0.3,2.0,59,265
171,add,25.0,+,,CrOc1cc22c(B#N+(-B(Sn1/(15+@5c/--n,CrOc1cc22c(B#N+(-B(Sn1/(1+5+@5c/--n,35,add + at position 25,flow_matching,0.3,2.0,59,265
172,add,30.0,5,,CrOc1cc22c(B#N+(-B(Sn1/(1+5+@5c/--n,CrOc1cc22c(B#N+(-B(Sn1/(1+5+@55c/--n,36,add 5 at position 30,flow_matching,0.3,2.0,59,265
173,add,10.0,/,,CrOc1cc22c(B#N+(-B(Sn1/(1+5+@55c/--n,CrOc1cc22c/(B#N+(-B(Sn1/(1+5+@55c/--n,37,add / at position 10,flow_matching,0.3,2.0,59,265
174,replace,1.0,O,r,CrOc1cc22c/(B#N+(-B(Sn1/(1+5+@55c/--n,COOc1cc22c/(B#N+(-B(Sn1/(1+5+@55c/--n,37,replace r at position 1 with O,flow_matching,0.3,2.0,59,265
175,add,17.0,-,,COOc1cc22c/(B#N+(-B(Sn1/(1+5+@55c/--n,COOc1cc22c/(B#N+(--B(Sn1/(1+5+@55c/--n,38,add - at position 17,flow_matching,0.3,2.0,59,265
176,add,6.0,+,,COOc1cc22c/(B#N+(--B(Sn1/(1+5+@55c/--n,COOc1c+c22c/(B#N+(--B(Sn1/(1+5+@55c/--n,39,add + at position 6,flow_matching,0.3,2.0,59,265
177,replace,2.0,c,O,COOc1c+c22c/(B#N+(--B(Sn1/(1+5+@55c/--n,COcc1c+c22c/(B#N+(--B(Sn1/(1+5+@55c/--n,39,replace O at position 2 with c,flow_matching,0.3,2.0,59,265
178,remove,37.0,-,,COcc1c+c22c/(B#N+(--B(Sn1/(1+5+@55c/--n,COcc1c+c22c/(B#N+(--B(Sn1/(1+5+@55c/-n,38,remove - from position 37,flow_matching,0.3,2.0,59,265
179,remove,28.0,+,,COcc1c+c22c/(B#N+(--B(Sn1/(1+5+@55c/-n,COcc1c+c22c/(B#N+(--B(Sn1/(15+@55c/-n,37,remove + from position 28,flow_matching,0.3,2.0,59,265
180,replace,4.0,=,1,COcc1c+c22c/(B#N+(--B(Sn1/(15+@55c/-n,COcc=c+c22c/(B#N+(--B(Sn1/(15+@55c/-n,37,replace 1 at position 4 with =,flow_matching,0.3,2.0,59,265
181,remove,14.0,#,,COcc=c+c22c/(B#N+(--B(Sn1/(15+@55c/-n,COcc=c+c22c/(BN+(--B(Sn1/(15+@55c/-n,36,remove # from position 14,flow_matching,0.3,2.0,59,265
182,add,33.0,O,,COcc=c+c22c/(BN+(--B(Sn1/(15+@55c/-n,COcc=c+c22c/(BN+(--B(Sn1/(15+@55cO/-n,37,add O at position 33,flow_matching,0.3,2.0,59,265
183,remove,31.0,5,,COcc=c+c22c/(BN+(--B(Sn1/(15+@55cO/-n,COcc=c+c22c/(BN+(--B(Sn1/(15+@5cO/-n,36,remove 5 from position 31,flow_matching,0.3,2.0,59,265
184,replace,3.0,B,c,COcc=c+c22c/(BN+(--B(Sn1/(15+@5cO/-n,COcB=c+c22c/(BN+(--B(Sn1/(15+@5cO/-n,36,replace c at position 3 with B,flow_matching,0.3,2.0,59,265
185,add,35.0,O,,COcB=c+c22c/(BN+(--B(Sn1/(15+@5cO/-n,COcB=c+c22c/(BN+(--B(Sn1/(15+@5cO/-On,37,add O at position 35,flow_matching,0.3,2.0,59,265
186,add,0.0,],,COcB=c+c22c/(BN+(--B(Sn1/(15+@5cO/-On,]COcB=c+c22c/(BN+(--B(Sn1/(15+@5cO/-On,38,add ] at position 0,flow_matching,0.3,2.0,59,265
187,add,15.0,7,,]COcB=c+c22c/(BN+(--B(Sn1/(15+@5cO/-On,]COcB=c+c22c/(B7N+(--B(Sn1/(15+@5cO/-On,39,add 7 at position 15,flow_matching,0.3,2.0,59,265
188,replace,0.0,C,],]COcB=c+c22c/(B7N+(--B(Sn1/(15+@5cO/-On,CCOcB=c+c22c/(B7N+(--B(Sn1/(15+@5cO/-On,39,replace ] at position 0 with C,flow_matching,0.3,2.0,59,265
189,remove,30.0,+,,CCOcB=c+c22c/(B7N+(--B(Sn1/(15+@5cO/-On,CCOcB=c+c22c/(B7N+(--B(Sn1/(15@5cO/-On,38,remove + from position 30,flow_matching,0.3,2.0,59,265
190,replace,1.0,O,C,CCOcB=c+c22c/(B7N+(--B(Sn1/(15@5cO/-On,COOcB=c+c22c/(B7N+(--B(Sn1/(15@5cO/-On,38,replace C at position 1 with O,flow_matching,0.3,2.0,59,265
191,add,13.0,B,,COOcB=c+c22c/(B7N+(--B(Sn1/(15@5cO/-On,COOcB=c+c22c/B(B7N+(--B(Sn1/(15@5cO/-On,39,add B at position 13,flow_matching,0.3,2.0,59,265
192,replace,35.0,(,/,COOcB=c+c22c/B(B7N+(--B(Sn1/(15@5cO/-On,COOcB=c+c22c/B(B7N+(--B(Sn1/(15@5cO(-On,39,replace / at position 35 with (,flow_matching,0.3,2.0,59,265
193,replace,32.0,B,5,COOcB=c+c22c/B(B7N+(--B(Sn1/(15@5cO(-On,COOcB=c+c22c/B(B7N+(--B(Sn1/(15@BcO(-On,39,replace 5 at position 32 with B,flow_matching,0.3,2.0,59,265
194,replace,2.0,c,O,COOcB=c+c22c/B(B7N+(--B(Sn1/(15@BcO(-On,COccB=c+c22c/B(B7N+(--B(Sn1/(15@BcO(-On,39,replace O at position 2 with c,flow_matching,0.3,2.0,59,265
195,replace,22.0,-,B,COccB=c+c22c/B(B7N+(--B(Sn1/(15@BcO(-On,COccB=c+c22c/B(B7N+(---(Sn1/(15@BcO(-On,39,replace B at position 22 with -,flow_matching,0.3,2.0,59,265
196,remove,31.0,@,,COccB=c+c22c/B(B7N+(---(Sn1/(15@BcO(-On,COccB=c+c22c/B(B7N+(---(Sn1/(15BcO(-On,38,remove @ from position 31,flow_matching,0.3,2.0,59,265
197,add,10.0,(,,COccB=c+c22c/B(B7N+(---(Sn1/(15BcO(-On,COccB=c+c2(2c/B(B7N+(---(Sn1/(15BcO(-On,39,add ( at position 10,flow_matching,0.3,2.0,59,265
198,remove,16.0,B,,COccB=c+c2(2c/B(B7N+(---(Sn1/(15BcO(-On,COccB=c+c2(2c/B(7N+(---(Sn1/(15BcO(-On,38,remove B from position 16,flow_matching,0.3,2.0,59,265
199,replace,3.0,1,c,COccB=c+c2(2c/B(7N+(---(Sn1/(15BcO(-On,COc1B=c+c2(2c/B(7N+(---(Sn1/(15BcO(-On,38,replace c at position 3 with 1,flow_matching,0.3,2.0,59,265
200,add,33.0,#,,COc1B=c+c2(2c/B(7N+(---(Sn1/(15BcO(-On,COc1B=c+c2(2c/B(7N+(---(Sn1/(15Bc#O(-On,39,add # at position 33,flow_matching,0.3,2.0,59,265
201,remove,26.0,1,,COc1B=c+c2(2c/B(7N+(---(Sn1/(15Bc#O(-On,COc1B=c+c2(2c/B(7N+(---(Sn/(15Bc#O(-On,38,remove 1 from position 26,flow_matching,0.3,2.0,59,265
202,replace,4.0,c,B,COc1B=c+c2(2c/B(7N+(---(Sn/(15Bc#O(-On,COc1c=c+c2(2c/B(7N+(---(Sn/(15Bc#O(-On,38,replace B at position 4 with c,flow_matching,0.3,2.0,59,265
203,remove,3.0,1,,COc1c=c+c2(2c/B(7N+(---(Sn/(15Bc#O(-On,COcc=c+c2(2c/B(7N+(---(Sn/(15Bc#O(-On,37,remove 1 from position 3,flow_matching,0.3,2.0,59,265
204,remove,20.0,-,,COcc=c+c2(2c/B(7N+(---(Sn/(15Bc#O(-On,COcc=c+c2(2c/B(7N+(--(Sn/(15Bc#O(-On,36,remove - from position 20,flow_matching,0.3,2.0,59,265
205,remove,20.0,-,,COcc=c+c2(2c/B(7N+(--(Sn/(15Bc#O(-On,COcc=c+c2(2c/B(7N+(-(Sn/(15Bc#O(-On,35,remove - from position 20,flow_matching,0.3,2.0,59,265
206,replace,3.0,1,c,COcc=c+c2(2c/B(7N+(-(Sn/(15Bc#O(-On,COc1=c+c2(2c/B(7N+(-(Sn/(15Bc#O(-On,35,replace c at position 3 with 1,flow_matching,0.3,2.0,59,265
207,replace,20.0,3,(,COc1=c+c2(2c/B(7N+(-(Sn/(15Bc#O(-On,COc1=c+c2(2c/B(7N+(-3Sn/(15Bc#O(-On,35,replace ( at position 20 with 3,flow_matching,0.3,2.0,59,265
208,remove,4.0,=,,COc1=c+c2(2c/B(7N+(-3Sn/(15Bc#O(-On,COc1c+c2(2c/B(7N+(-3Sn/(15Bc#O(-On,34,remove = from position 4,flow_matching,0.3,2.0,59,265
209,replace,18.0,/,-,COc1c+c2(2c/B(7N+(-3Sn/(15Bc#O(-On,COc1c+c2(2c/B(7N+(/3Sn/(15Bc#O(-On,34,replace - at position 18 with /,flow_matching,0.3,2.0,59,265
210,replace,5.0,c,+,COc1c+c2(2c/B(7N+(/3Sn/(15Bc#O(-On,COc1ccc2(2c/B(7N+(/3Sn/(15Bc#O(-On,34,replace + at position 5 with c,flow_matching,0.3,2.0,59,265
211,replace,6.0,2,c,COc1ccc2(2c/B(7N+(/3Sn/(15Bc#O(-On,COc1cc22(2c/B(7N+(/3Sn/(15Bc#O(-On,34,replace c at position 6 with 2,flow_matching,0.3,2.0,59,265
212,replace,7.0,c,2,COc1cc22(2c/B(7N+(/3Sn/(15Bc#O(-On,COc1cc2c(2c/B(7N+(/3Sn/(15Bc#O(-On,34,replace 2 at position 7 with c,flow_matching,0.3,2.0,59,265
213,replace,12.0,l,B,COc1cc2c(2c/B(7N+(/3Sn/(15Bc#O(-On,COc1cc2c(2c/l(7N+(/3Sn/(15Bc#O(-On,34,replace B at position 12 with l,flow_matching,0.3,2.0,59,265
214,replace,9.0,c,2,COc1cc2c(2c/l(7N+(/3Sn/(15Bc#O(-On,COc1cc2c(cc/l(7N+(/3Sn/(15Bc#O(-On,34,replace 2 at position 9 with c,flow_matching,0.3,2.0,59,265
215,add,24.0,O,,COc1cc2c(cc/l(7N+(/3Sn/(15Bc#O(-On,COc1cc2c(cc/l(7N+(/3Sn/(O15Bc#O(-On,35,add O at position 24,flow_matching,0.3,2.0,59,265
216,remove,19.0,3,,COc1cc2c(cc/l(7N+(/3Sn/(O15Bc#O(-On,COc1cc2c(cc/l(7N+(/Sn/(O15Bc#O(-On,34,remove 3 from position 19,flow_matching,0.3,2.0,59,265
217,replace,11.0,1,/,COc1cc2c(cc/l(7N+(/Sn/(O15Bc#O(-On,COc1cc2c(cc1l(7N+(/Sn/(O15Bc#O(-On,34,replace / at position 11 with 1,flow_matching,0.3,2.0,59,265
218,remove,23.0,O,,COc1cc2c(cc1l(7N+(/Sn/(O15Bc#O(-On,COc1cc2c(cc1l(7N+(/Sn/(15Bc#O(-On,33,remove O from position 23,flow_matching,0.3,2.0,59,265
219,add,32.0,B,,COc1cc2c(cc1l(7N+(/Sn/(15Bc#O(-On,COc1cc2c(cc1l(7N+(/Sn/(15Bc#O(-OBn,34,add B at position 32,flow_matching,0.3,2.0,59,265
220,replace,12.0,O,l,COc1cc2c(cc1l(7N+(/Sn/(15Bc#O(-OBn,COc1cc2c(cc1O(7N+(/Sn/(15Bc#O(-OBn,34,replace l at position 12 with O,flow_matching,0.3,2.0,59,265
221,replace,13.0,C,(,COc1cc2c(cc1O(7N+(/Sn/(15Bc#O(-OBn,COc1cc2c(cc1OC7N+(/Sn/(15Bc#O(-OBn,34,replace ( at position 13 with C,flow_matching,0.3,2.0,59,265
222,replace,14.0,),7,COc1cc2c(cc1OC7N+(/Sn/(15Bc#O(-OBn,COc1cc2c(cc1OC)N+(/Sn/(15Bc#O(-OBn,34,replace 7 at position 14 with ),flow_matching,0.3,2.0,59,265
223,replace,15.0,[,N,COc1cc2c(cc1OC)N+(/Sn/(15Bc#O(-OBn,COc1cc2c(cc1OC)[+(/Sn/(15Bc#O(-OBn,34,replace N at position 15 with [,flow_matching,0.3,2.0,59,265
224,replace,16.0,C,+,COc1cc2c(cc1OC)[+(/Sn/(15Bc#O(-OBn,COc1cc2c(cc1OC)[C(/Sn/(15Bc#O(-OBn,34,replace + at position 16 with C,flow_matching,0.3,2.0,59,265
225,replace,17.0,@,(,COc1cc2c(cc1OC)[C(/Sn/(15Bc#O(-OBn,COc1cc2c(cc1OC)[C@/Sn/(15Bc#O(-OBn,34,replace ( at position 17 with @,flow_matching,0.3,2.0,59,265
226,replace,18.0,H,/,COc1cc2c(cc1OC)[C@/Sn/(15Bc#O(-OBn,COc1cc2c(cc1OC)[C@HSn/(15Bc#O(-OBn,34,replace / at position 18 with H,flow_matching,0.3,2.0,59,265
227,replace,19.0,],S,COc1cc2c(cc1OC)[C@HSn/(15Bc#O(-OBn,COc1cc2c(cc1OC)[C@H]n/(15Bc#O(-OBn,34,replace S at position 19 with ],flow_matching,0.3,2.0,59,265
228,replace,20.0,(,n,COc1cc2c(cc1OC)[C@H]n/(15Bc#O(-OBn,COc1cc2c(cc1OC)[C@H](/(15Bc#O(-OBn,34,replace n at position 20 with (,flow_matching,0.3,2.0,59,265
229,replace,21.0,C,/,COc1cc2c(cc1OC)[C@H](/(15Bc#O(-OBn,COc1cc2c(cc1OC)[C@H](C(15Bc#O(-OBn,34,replace / at position 21 with C,flow_matching,0.3,2.0,59,265
230,replace,23.0,=,1,COc1cc2c(cc1OC)[C@H](C(15Bc#O(-OBn,COc1cc2c(cc1OC)[C@H](C(=5Bc#O(-OBn,34,replace 1 at position 23 with =,flow_matching,0.3,2.0,59,265
231,replace,24.0,O,5,COc1cc2c(cc1OC)[C@H](C(=5Bc#O(-OBn,COc1cc2c(cc1OC)[C@H](C(=OBc#O(-OBn,34,replace 5 at position 24 with O,flow_matching,0.3,2.0,59,265
232,replace,25.0,),B,COc1cc2c(cc1OC)[C@H](C(=OBc#O(-OBn,COc1cc2c(cc1OC)[C@H](C(=O)c#O(-OBn,34,replace B at position 25 with ),flow_matching,0.3,2.0,59,265
233,replace,26.0,[,c,COc1cc2c(cc1OC)[C@H](C(=O)c#O(-OBn,COc1cc2c(cc1OC)[C@H](C(=O)[#O(-OBn,34,replace c at position 26 with [,flow_matching,0.3,2.0,59,265
234,replace,27.0,O,#,COc1cc2c(cc1OC)[C@H](C(=O)[#O(-OBn,COc1cc2c(cc1OC)[C@H](C(=O)[OO(-OBn,34,replace # at position 27 with O,flow_matching,0.3,2.0,59,265
235,replace,28.0,-,O,COc1cc2c(cc1OC)[C@H](C(=O)[OO(-OBn,COc1cc2c(cc1OC)[C@H](C(=O)[O-(-OBn,34,replace O at position 28 with -,flow_matching,0.3,2.0,59,265
236,replace,29.0,],(,COc1cc2c(cc1OC)[C@H](C(=O)[O-(-OBn,COc1cc2c(cc1OC)[C@H](C(=O)[O-]-OBn,34,replace ( at position 29 with ],flow_matching,0.3,2.0,59,265
237,replace,30.0,),-,COc1cc2c(cc1OC)[C@H](C(=O)[O-]-OBn,COc1cc2c(cc1OC)[C@H](C(=O)[O-])OBn,34,replace - at position 30 with ),flow_matching,0.3,2.0,59,265
238,replace,31.0,[,O,COc1cc2c(cc1OC)[C@H](C(=O)[O-])OBn,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[Bn,34,replace O at position 31 with [,flow_matching,0.3,2.0,59,265
239,replace,32.0,C,B,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[Bn,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[Cn,34,replace B at position 32 with C,flow_matching,0.3,2.0,59,265
240,replace,33.0,@,n,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[Cn,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@,34,replace n at position 33 with @,flow_matching,0.3,2.0,59,265
241,add,34.0,H,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H,35,add H at position 34,flow_matching,0.3,2.0,59,265
242,add,35.0,],,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H],36,add ] at position 35,flow_matching,0.3,2.0,59,265
243,add,36.0,(,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H],COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](,37,add ( at position 36,flow_matching,0.3,2.0,59,265
244,add,37.0,c,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c,38,add c at position 37,flow_matching,0.3,2.0,59,265
245,add,38.0,1,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1,39,add 1 at position 38,flow_matching,0.3,2.0,59,265
246,add,39.0,c,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1c,40,add c at position 39,flow_matching,0.3,2.0,59,265
247,add,40.0,c,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1c,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cc,41,add c at position 40,flow_matching,0.3,2.0,59,265
248,add,41.0,c,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cc,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1ccc,42,add c at position 41,flow_matching,0.3,2.0,59,265
249,add,42.0,c,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1ccc,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc,43,add c at position 42,flow_matching,0.3,2.0,59,265
250,add,43.0,(,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(,44,add ( at position 43,flow_matching,0.3,2.0,59,265
251,add,44.0,C,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(C,45,add C at position 44,flow_matching,0.3,2.0,59,265
252,add,45.0,l,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(C,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl,46,add l at position 45,flow_matching,0.3,2.0,59,265
253,add,46.0,),,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl),47,add ) at position 46,flow_matching,0.3,2.0,59,265
254,add,47.0,c,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl),COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c,48,add c at position 47,flow_matching,0.3,2.0,59,265
255,add,48.0,1,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1,49,add 1 at position 48,flow_matching,0.3,2.0,59,265
256,add,49.0,),,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1),50,add ) at position 49,flow_matching,0.3,2.0,59,265
257,add,50.0,N,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1),COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N,51,add N at position 50,flow_matching,0.3,2.0,59,265
258,add,51.0,(,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(,52,add ( at position 51,flow_matching,0.3,2.0,59,265
259,add,52.0,C,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C,53,add C at position 52,flow_matching,0.3,2.0,59,265
260,add,53.0,),,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C),54,add ) at position 53,flow_matching,0.3,2.0,59,265
261,add,54.0,C,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C),COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C,55,add C at position 54,flow_matching,0.3,2.0,59,265
262,add,55.0,2,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2,56,add 2 at position 55,flow_matching,0.3,2.0,59,265
263,add,56.0,=,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2=,57,add = at position 56,flow_matching,0.3,2.0,59,265
264,add,57.0,O,,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2=,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2=O,58,add O at position 57,flow_matching,0.3,2.0,59,265
265,add,58.0,"
",,COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2=O,"COc1cc2c(cc1OC)[C@H](C(=O)[O-])[C@H](c1cccc(Cl)c1)N(C)C2=O
",59,"add 
 at position 58",flow_matching,0.3,2.0,59,265
