step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,50,187
1,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,50,187
2,add,0.0,=,,N,=N,2,add = at position 0,flow_matching,0.3,2.0,50,187
3,replace,0.0,C,=,=N,CN,2,replace = at position 0 with C,flow_matching,0.3,2.0,50,187
4,add,2.0,=,,CN,CN=,3,add = at position 2,flow_matching,0.3,2.0,50,187
5,replace,1.0,[,N,CN=,C[=,3,replace N at position 1 with [,flow_matching,0.3,2.0,50,187
6,replace,2.0,C,=,C[=,C[C,3,replace = at position 2 with C,flow_matching,0.3,2.0,50,187
7,add,3.0,3,,C[C,C[C3,4,add 3 at position 3,flow_matching,0.3,2.0,50,187
8,replace,3.0,@,3,C[C3,C[C@,4,replace 3 at position 3 with @,flow_matching,0.3,2.0,50,187
9,add,4.0,@,,C[C@,C[C@@,5,add @ at position 4,flow_matching,0.3,2.0,50,187
10,add,4.0,O,,C[C@@,C[C@O@,6,add O at position 4,flow_matching,0.3,2.0,50,187
11,remove,2.0,C,,C[C@O@,C[@O@,5,remove C from position 2,flow_matching,0.3,2.0,50,187
12,replace,2.0,C,@,C[@O@,C[CO@,5,replace @ at position 2 with C,flow_matching,0.3,2.0,50,187
13,add,5.0,\,,C[CO@,C[CO@\,6,add \ at position 5,flow_matching,0.3,2.0,50,187
14,add,4.0,1,,C[CO@\,C[CO1@\,7,add 1 at position 4,flow_matching,0.3,2.0,50,187
15,add,4.0,5,,C[CO1@\,C[CO51@\,8,add 5 at position 4,flow_matching,0.3,2.0,50,187
16,remove,6.0,@,,C[CO51@\,C[CO51\,7,remove @ from position 6,flow_matching,0.3,2.0,50,187
17,replace,3.0,7,O,C[CO51\,C[C751\,7,replace O at position 3 with 7,flow_matching,0.3,2.0,50,187
18,remove,1.0,[,,C[C751\,CC751\,6,remove [ from position 1,flow_matching,0.3,2.0,50,187
19,add,4.0,C,,CC751\,CC75C1\,7,add C at position 4,flow_matching,0.3,2.0,50,187
20,replace,1.0,[,C,CC75C1\,C[75C1\,7,replace C at position 1 with [,flow_matching,0.3,2.0,50,187
21,replace,2.0,C,7,C[75C1\,C[C5C1\,7,replace 7 at position 2 with C,flow_matching,0.3,2.0,50,187
22,replace,3.0,@,5,C[C5C1\,C[C@C1\,7,replace 5 at position 3 with @,flow_matching,0.3,2.0,50,187
23,replace,4.0,@,C,C[C@C1\,C[C@@1\,7,replace C at position 4 with @,flow_matching,0.3,2.0,50,187
24,replace,5.0,H,1,C[C@@1\,C[C@@H\,7,replace 1 at position 5 with H,flow_matching,0.3,2.0,50,187
25,add,3.0,4,,C[C@@H\,C[C4@@H\,8,add 4 at position 3,flow_matching,0.3,2.0,50,187
26,remove,3.0,4,,C[C4@@H\,C[C@@H\,7,remove 4 from position 3,flow_matching,0.3,2.0,50,187
27,replace,6.0,],\,C[C@@H\,C[C@@H],7,replace \ at position 6 with ],flow_matching,0.3,2.0,50,187
28,replace,1.0,s,[,C[C@@H],CsC@@H],7,replace [ at position 1 with s,flow_matching,0.3,2.0,50,187
29,add,6.0,N,,CsC@@H],CsC@@HN],8,add N at position 6,flow_matching,0.3,2.0,50,187
30,add,7.0,6,,CsC@@HN],CsC@@HN6],9,add 6 at position 7,flow_matching,0.3,2.0,50,187
31,replace,1.0,[,s,CsC@@HN6],C[C@@HN6],9,replace s at position 1 with [,flow_matching,0.3,2.0,50,187
32,replace,6.0,],N,C[C@@HN6],C[C@@H]6],9,replace N at position 6 with ],flow_matching,0.3,2.0,50,187
33,replace,8.0,6,],C[C@@H]6],C[C@@H]66,9,replace ] at position 8 with 6,flow_matching,0.3,2.0,50,187
34,replace,0.0,c,C,C[C@@H]66,c[C@@H]66,9,replace C at position 0 with c,flow_matching,0.3,2.0,50,187
35,replace,5.0,@,H,c[C@@H]66,c[C@@@]66,9,replace H at position 5 with @,flow_matching,0.3,2.0,50,187
36,add,8.0,o,,c[C@@@]66,c[C@@@]6o6,10,add o at position 8,flow_matching,0.3,2.0,50,187
37,replace,6.0,3,],c[C@@@]6o6,c[C@@@36o6,10,replace ] at position 6 with 3,flow_matching,0.3,2.0,50,187
38,add,6.0,-,,c[C@@@36o6,c[C@@@-36o6,11,add - at position 6,flow_matching,0.3,2.0,50,187
39,replace,10.0,r,6,c[C@@@-36o6,c[C@@@-36or,11,replace 6 at position 10 with r,flow_matching,0.3,2.0,50,187
40,replace,0.0,C,c,c[C@@@-36or,C[C@@@-36or,11,replace c at position 0 with C,flow_matching,0.3,2.0,50,187
41,replace,2.0,o,C,C[C@@@-36or,C[o@@@-36or,11,replace C at position 2 with o,flow_matching,0.3,2.0,50,187
42,replace,2.0,C,o,C[o@@@-36or,C[C@@@-36or,11,replace o at position 2 with C,flow_matching,0.3,2.0,50,187
43,replace,5.0,H,@,C[C@@@-36or,C[C@@H-36or,11,replace @ at position 5 with H,flow_matching,0.3,2.0,50,187
44,replace,7.0,r,3,C[C@@H-36or,C[C@@H-r6or,11,replace 3 at position 7 with r,flow_matching,0.3,2.0,50,187
45,remove,5.0,H,,C[C@@H-r6or,C[C@@-r6or,10,remove H from position 5,flow_matching,0.3,2.0,50,187
46,replace,6.0,\,r,C[C@@-r6or,C[C@@-\6or,10,replace r at position 6 with \,flow_matching,0.3,2.0,50,187
47,remove,0.0,C,,C[C@@-\6or,[C@@-\6or,9,remove C from position 0,flow_matching,0.3,2.0,50,187
48,replace,0.0,C,[,[C@@-\6or,CC@@-\6or,9,replace [ at position 0 with C,flow_matching,0.3,2.0,50,187
49,replace,1.0,[,C,CC@@-\6or,C[@@-\6or,9,replace C at position 1 with [,flow_matching,0.3,2.0,50,187
50,replace,7.0,r,o,C[@@-\6or,C[@@-\6rr,9,replace o at position 7 with r,flow_matching,0.3,2.0,50,187
51,remove,2.0,@,,C[@@-\6rr,C[@-\6rr,8,remove @ from position 2,flow_matching,0.3,2.0,50,187
52,remove,6.0,r,,C[@-\6rr,C[@-\6r,7,remove r from position 6,flow_matching,0.3,2.0,50,187
53,add,0.0,6,,C[@-\6r,6C[@-\6r,8,add 6 at position 0,flow_matching,0.3,2.0,50,187
54,add,7.0,#,,6C[@-\6r,6C[@-\6#r,9,add # at position 7,flow_matching,0.3,2.0,50,187
55,replace,0.0,S,6,6C[@-\6#r,SC[@-\6#r,9,replace 6 at position 0 with S,flow_matching,0.3,2.0,50,187
56,replace,0.0,C,S,SC[@-\6#r,CC[@-\6#r,9,replace S at position 0 with C,flow_matching,0.3,2.0,50,187
57,add,8.0,),,CC[@-\6#r,CC[@-\6#)r,10,add ) at position 8,flow_matching,0.3,2.0,50,187
58,replace,1.0,[,C,CC[@-\6#)r,C[[@-\6#)r,10,replace C at position 1 with [,flow_matching,0.3,2.0,50,187
59,replace,2.0,C,[,C[[@-\6#)r,C[C@-\6#)r,10,replace [ at position 2 with C,flow_matching,0.3,2.0,50,187
60,replace,6.0,5,6,C[C@-\6#)r,C[C@-\5#)r,10,replace 6 at position 6 with 5,flow_matching,0.3,2.0,50,187
61,replace,9.0,n,r,C[C@-\5#)r,C[C@-\5#)n,10,replace r at position 9 with n,flow_matching,0.3,2.0,50,187
62,replace,4.0,/,-,C[C@-\5#)n,C[C@/\5#)n,10,replace - at position 4 with /,flow_matching,0.3,2.0,50,187
63,remove,9.0,n,,C[C@/\5#)n,C[C@/\5#),9,remove n from position 9,flow_matching,0.3,2.0,50,187
64,remove,6.0,5,,C[C@/\5#),C[C@/\#),8,remove 5 from position 6,flow_matching,0.3,2.0,50,187
65,remove,1.0,[,,C[C@/\#),CC@/\#),7,remove [ from position 1,flow_matching,0.3,2.0,50,187
66,replace,1.0,[,C,CC@/\#),C[@/\#),7,replace C at position 1 with [,flow_matching,0.3,2.0,50,187
67,remove,5.0,#,,C[@/\#),C[@/\),6,remove # from position 5,flow_matching,0.3,2.0,50,187
68,replace,2.0,C,@,C[@/\),C[C/\),6,replace @ at position 2 with C,flow_matching,0.3,2.0,50,187
69,replace,3.0,@,/,C[C/\),C[C@\),6,replace / at position 3 with @,flow_matching,0.3,2.0,50,187
70,add,3.0,3,,C[C@\),C[C3@\),7,add 3 at position 3,flow_matching,0.3,2.0,50,187
71,add,7.0,=,,C[C3@\),C[C3@\)=,8,add = at position 7,flow_matching,0.3,2.0,50,187
72,replace,3.0,@,3,C[C3@\)=,C[C@@\)=,8,replace 3 at position 3 with @,flow_matching,0.3,2.0,50,187
73,remove,3.0,@,,C[C@@\)=,C[C@\)=,7,remove @ from position 3,flow_matching,0.3,2.0,50,187
74,replace,4.0,@,\,C[C@\)=,C[C@@)=,7,replace \ at position 4 with @,flow_matching,0.3,2.0,50,187
75,remove,5.0,),,C[C@@)=,C[C@@=,6,remove ) from position 5,flow_matching,0.3,2.0,50,187
76,remove,2.0,C,,C[C@@=,C[@@=,5,remove C from position 2,flow_matching,0.3,2.0,50,187
77,remove,3.0,@,,C[@@=,C[@=,4,remove @ from position 3,flow_matching,0.3,2.0,50,187
78,remove,2.0,@,,C[@=,C[=,3,remove @ from position 2,flow_matching,0.3,2.0,50,187
79,add,1.0,l,,C[=,Cl[=,4,add l at position 1,flow_matching,0.3,2.0,50,187
80,replace,1.0,[,l,Cl[=,C[[=,4,replace l at position 1 with [,flow_matching,0.3,2.0,50,187
81,add,0.0,[,,C[[=,[C[[=,5,add [ at position 0,flow_matching,0.3,2.0,50,187
82,add,0.0,4,,[C[[=,4[C[[=,6,add 4 at position 0,flow_matching,0.3,2.0,50,187
83,add,2.0,/,,4[C[[=,4[/C[[=,7,add / at position 2,flow_matching,0.3,2.0,50,187
84,remove,1.0,[,,4[/C[[=,4/C[[=,6,remove [ from position 1,flow_matching,0.3,2.0,50,187
85,remove,1.0,/,,4/C[[=,4C[[=,5,remove / from position 1,flow_matching,0.3,2.0,50,187
86,add,5.0,\,,4C[[=,4C[[=\,6,add \ at position 5,flow_matching,0.3,2.0,50,187
87,replace,0.0,C,4,4C[[=\,CC[[=\,6,replace 4 at position 0 with C,flow_matching,0.3,2.0,50,187
88,replace,1.0,[,C,CC[[=\,C[[[=\,6,replace C at position 1 with [,flow_matching,0.3,2.0,50,187
89,add,5.0,[,,C[[[=\,C[[[=[\,7,add [ at position 5,flow_matching,0.3,2.0,50,187
90,add,5.0,(,,C[[[=[\,C[[[=([\,8,add ( at position 5,flow_matching,0.3,2.0,50,187
91,replace,3.0,O,[,C[[[=([\,C[[O=([\,8,replace [ at position 3 with O,flow_matching,0.3,2.0,50,187
92,replace,2.0,C,[,C[[O=([\,C[CO=([\,8,replace [ at position 2 with C,flow_matching,0.3,2.0,50,187
93,replace,1.0,\,[,C[CO=([\,C\CO=([\,8,replace [ at position 1 with \,flow_matching,0.3,2.0,50,187
94,replace,7.0,#,\,C\CO=([\,C\CO=([#,8,replace \ at position 7 with #,flow_matching,0.3,2.0,50,187
95,add,3.0,(,,C\CO=([#,C\C(O=([#,9,add ( at position 3,flow_matching,0.3,2.0,50,187
96,replace,5.0,2,=,C\C(O=([#,C\C(O2([#,9,replace = at position 5 with 2,flow_matching,0.3,2.0,50,187
97,replace,4.0,6,O,C\C(O2([#,C\C(62([#,9,replace O at position 4 with 6,flow_matching,0.3,2.0,50,187
98,replace,1.0,[,\,C\C(62([#,C[C(62([#,9,replace \ at position 1 with [,flow_matching,0.3,2.0,50,187
99,replace,4.0,\,6,C[C(62([#,C[C(\2([#,9,replace 6 at position 4 with \,flow_matching,0.3,2.0,50,187
100,remove,7.0,[,,C[C(\2([#,C[C(\2(#,8,remove [ from position 7,flow_matching,0.3,2.0,50,187
101,add,6.0,],,C[C(\2(#,C[C(\2](#,9,add ] at position 6,flow_matching,0.3,2.0,50,187
102,add,1.0,s,,C[C(\2](#,Cs[C(\2](#,10,add s at position 1,flow_matching,0.3,2.0,50,187
103,add,10.0,1,,Cs[C(\2](#,Cs[C(\2](#1,11,add 1 at position 10,flow_matching,0.3,2.0,50,187
104,add,9.0,=,,Cs[C(\2](#1,Cs[C(\2](=#1,12,add = at position 9,flow_matching,0.3,2.0,50,187
105,replace,2.0,(,[,Cs[C(\2](=#1,Cs(C(\2](=#1,12,replace [ at position 2 with (,flow_matching,0.3,2.0,50,187
106,replace,1.0,[,s,Cs(C(\2](=#1,C[(C(\2](=#1,12,replace s at position 1 with [,flow_matching,0.3,2.0,50,187
107,add,0.0,C,,C[(C(\2](=#1,CC[(C(\2](=#1,13,add C at position 0,flow_matching,0.3,2.0,50,187
108,add,9.0,),,CC[(C(\2](=#1,CC[(C(\2])(=#1,14,add ) at position 9,flow_matching,0.3,2.0,50,187
109,add,3.0,4,,CC[(C(\2])(=#1,CC[4(C(\2])(=#1,15,add 4 at position 3,flow_matching,0.3,2.0,50,187
110,replace,1.0,[,C,CC[4(C(\2])(=#1,C[[4(C(\2])(=#1,15,replace C at position 1 with [,flow_matching,0.3,2.0,50,187
111,remove,1.0,[,,C[[4(C(\2])(=#1,C[4(C(\2])(=#1,14,remove [ from position 1,flow_matching,0.3,2.0,50,187
112,replace,12.0,B,#,C[4(C(\2])(=#1,C[4(C(\2])(=B1,14,replace # at position 12 with B,flow_matching,0.3,2.0,50,187
113,add,1.0,s,,C[4(C(\2])(=B1,Cs[4(C(\2])(=B1,15,add s at position 1,flow_matching,0.3,2.0,50,187
114,replace,14.0,B,1,Cs[4(C(\2])(=B1,Cs[4(C(\2])(=BB,15,replace 1 at position 14 with B,flow_matching,0.3,2.0,50,187
115,replace,2.0,C,[,Cs[4(C(\2])(=BB,CsC4(C(\2])(=BB,15,replace [ at position 2 with C,flow_matching,0.3,2.0,50,187
116,replace,1.0,[,s,CsC4(C(\2])(=BB,C[C4(C(\2])(=BB,15,replace s at position 1 with [,flow_matching,0.3,2.0,50,187
117,replace,3.0,@,4,C[C4(C(\2])(=BB,C[C@(C(\2])(=BB,15,replace 4 at position 3 with @,flow_matching,0.3,2.0,50,187
118,replace,4.0,@,(,C[C@(C(\2])(=BB,C[C@@C(\2])(=BB,15,replace ( at position 4 with @,flow_matching,0.3,2.0,50,187
119,replace,5.0,H,C,C[C@@C(\2])(=BB,C[C@@H(\2])(=BB,15,replace C at position 5 with H,flow_matching,0.3,2.0,50,187
120,replace,6.0,],(,C[C@@H(\2])(=BB,C[C@@H]\2])(=BB,15,replace ( at position 6 with ],flow_matching,0.3,2.0,50,187
121,replace,2.0,),C,C[C@@H]\2])(=BB,C[)@@H]\2])(=BB,15,replace C at position 2 with ),flow_matching,0.3,2.0,50,187
122,replace,2.0,C,),C[)@@H]\2])(=BB,C[C@@H]\2])(=BB,15,replace ) at position 2 with C,flow_matching,0.3,2.0,50,187
123,add,1.0,=,,C[C@@H]\2])(=BB,C=[C@@H]\2])(=BB,16,add = at position 1,flow_matching,0.3,2.0,50,187
124,replace,1.0,[,=,C=[C@@H]\2])(=BB,C[[C@@H]\2])(=BB,16,replace = at position 1 with [,flow_matching,0.3,2.0,50,187
125,replace,2.0,C,[,C[[C@@H]\2])(=BB,C[CC@@H]\2])(=BB,16,replace [ at position 2 with C,flow_matching,0.3,2.0,50,187
126,add,5.0,-,,C[CC@@H]\2])(=BB,C[CC@-@H]\2])(=BB,17,add - at position 5,flow_matching,0.3,2.0,50,187
127,replace,3.0,@,C,C[CC@-@H]\2])(=BB,C[C@@-@H]\2])(=BB,17,replace C at position 3 with @,flow_matching,0.3,2.0,50,187
128,remove,7.0,H,,C[C@@-@H]\2])(=BB,C[C@@-@]\2])(=BB,16,remove H from position 7,flow_matching,0.3,2.0,50,187
129,remove,1.0,[,,C[C@@-@]\2])(=BB,CC@@-@]\2])(=BB,15,remove [ from position 1,flow_matching,0.3,2.0,50,187
130,add,0.0,@,,CC@@-@]\2])(=BB,@CC@@-@]\2])(=BB,16,add @ at position 0,flow_matching,0.3,2.0,50,187
131,replace,0.0,C,@,@CC@@-@]\2])(=BB,CCC@@-@]\2])(=BB,16,replace @ at position 0 with C,flow_matching,0.3,2.0,50,187
132,replace,1.0,[,C,CCC@@-@]\2])(=BB,C[C@@-@]\2])(=BB,16,replace C at position 1 with [,flow_matching,0.3,2.0,50,187
133,replace,10.0,O,],C[C@@-@]\2])(=BB,C[C@@-@]\2O)(=BB,16,replace ] at position 10 with O,flow_matching,0.3,2.0,50,187
134,add,0.0,[,,C[C@@-@]\2O)(=BB,[C[C@@-@]\2O)(=BB,17,add [ at position 0,flow_matching,0.3,2.0,50,187
135,remove,8.0,],,[C[C@@-@]\2O)(=BB,[C[C@@-@\2O)(=BB,16,remove ] from position 8,flow_matching,0.3,2.0,50,187
136,add,13.0,[,,[C[C@@-@\2O)(=BB,[C[C@@-@\2O)([=BB,17,add [ at position 13,flow_matching,0.3,2.0,50,187
137,replace,0.0,C,[,[C[C@@-@\2O)([=BB,CC[C@@-@\2O)([=BB,17,replace [ at position 0 with C,flow_matching,0.3,2.0,50,187
138,remove,15.0,B,,CC[C@@-@\2O)([=BB,CC[C@@-@\2O)([=B,16,remove B from position 15,flow_matching,0.3,2.0,50,187
139,add,10.0,[,,CC[C@@-@\2O)([=B,CC[C@@-@\2[O)([=B,17,add [ at position 10,flow_matching,0.3,2.0,50,187
140,remove,14.0,[,,CC[C@@-@\2[O)([=B,CC[C@@-@\2[O)(=B,16,remove [ from position 14,flow_matching,0.3,2.0,50,187
141,remove,2.0,[,,CC[C@@-@\2[O)(=B,CCC@@-@\2[O)(=B,15,remove [ from position 2,flow_matching,0.3,2.0,50,187
142,replace,1.0,[,C,CCC@@-@\2[O)(=B,C[C@@-@\2[O)(=B,15,replace C at position 1 with [,flow_matching,0.3,2.0,50,187
143,replace,5.0,H,-,C[C@@-@\2[O)(=B,C[C@@H@\2[O)(=B,15,replace - at position 5 with H,flow_matching,0.3,2.0,50,187
144,replace,6.0,],@,C[C@@H@\2[O)(=B,C[C@@H]\2[O)(=B,15,replace @ at position 6 with ],flow_matching,0.3,2.0,50,187
145,replace,7.0,1,\,C[C@@H]\2[O)(=B,C[C@@H]12[O)(=B,15,replace \ at position 7 with 1,flow_matching,0.3,2.0,50,187
146,replace,8.0,C,2,C[C@@H]12[O)(=B,C[C@@H]1C[O)(=B,15,replace 2 at position 8 with C,flow_matching,0.3,2.0,50,187
147,replace,9.0,c,[,C[C@@H]1C[O)(=B,C[C@@H]1CcO)(=B,15,replace [ at position 9 with c,flow_matching,0.3,2.0,50,187
148,replace,10.0,2,O,C[C@@H]1CcO)(=B,C[C@@H]1Cc2)(=B,15,replace O at position 10 with 2,flow_matching,0.3,2.0,50,187
149,replace,11.0,c,),C[C@@H]1Cc2)(=B,C[C@@H]1Cc2c(=B,15,replace ) at position 11 with c,flow_matching,0.3,2.0,50,187
150,replace,12.0,c,(,C[C@@H]1Cc2c(=B,C[C@@H]1Cc2cc=B,15,replace ( at position 12 with c,flow_matching,0.3,2.0,50,187
151,replace,13.0,c,=,C[C@@H]1Cc2cc=B,C[C@@H]1Cc2cccB,15,replace = at position 13 with c,flow_matching,0.3,2.0,50,187
152,replace,14.0,c,B,C[C@@H]1Cc2cccB,C[C@@H]1Cc2cccc,15,replace B at position 14 with c,flow_matching,0.3,2.0,50,187
153,add,15.0,c,,C[C@@H]1Cc2cccc,C[C@@H]1Cc2ccccc,16,add c at position 15,flow_matching,0.3,2.0,50,187
154,add,16.0,2,,C[C@@H]1Cc2ccccc,C[C@@H]1Cc2ccccc2,17,add 2 at position 16,flow_matching,0.3,2.0,50,187
155,add,17.0,N,,C[C@@H]1Cc2ccccc2,C[C@@H]1Cc2ccccc2N,18,add N at position 17,flow_matching,0.3,2.0,50,187
156,add,18.0,1,,C[C@@H]1Cc2ccccc2N,C[C@@H]1Cc2ccccc2N1,19,add 1 at position 18,flow_matching,0.3,2.0,50,187
157,add,19.0,C,,C[C@@H]1Cc2ccccc2N1,C[C@@H]1Cc2ccccc2N1C,20,add C at position 19,flow_matching,0.3,2.0,50,187
158,add,20.0,(,,C[C@@H]1Cc2ccccc2N1C,C[C@@H]1Cc2ccccc2N1C(,21,add ( at position 20,flow_matching,0.3,2.0,50,187
159,add,21.0,=,,C[C@@H]1Cc2ccccc2N1C(,C[C@@H]1Cc2ccccc2N1C(=,22,add = at position 21,flow_matching,0.3,2.0,50,187
160,add,22.0,O,,C[C@@H]1Cc2ccccc2N1C(=,C[C@@H]1Cc2ccccc2N1C(=O,23,add O at position 22,flow_matching,0.3,2.0,50,187
161,add,23.0,),,C[C@@H]1Cc2ccccc2N1C(=O,C[C@@H]1Cc2ccccc2N1C(=O),24,add ) at position 23,flow_matching,0.3,2.0,50,187
162,add,24.0,[,,C[C@@H]1Cc2ccccc2N1C(=O),C[C@@H]1Cc2ccccc2N1C(=O)[,25,add [ at position 24,flow_matching,0.3,2.0,50,187
163,add,25.0,C,,C[C@@H]1Cc2ccccc2N1C(=O)[,C[C@@H]1Cc2ccccc2N1C(=O)[C,26,add C at position 25,flow_matching,0.3,2.0,50,187
164,add,26.0,@,,C[C@@H]1Cc2ccccc2N1C(=O)[C,C[C@@H]1Cc2ccccc2N1C(=O)[C@,27,add @ at position 26,flow_matching,0.3,2.0,50,187
165,add,27.0,H,,C[C@@H]1Cc2ccccc2N1C(=O)[C@,C[C@@H]1Cc2ccccc2N1C(=O)[C@H,28,add H at position 27,flow_matching,0.3,2.0,50,187
166,add,28.0,],,C[C@@H]1Cc2ccccc2N1C(=O)[C@H,C[C@@H]1Cc2ccccc2N1C(=O)[C@H],29,add ] at position 28,flow_matching,0.3,2.0,50,187
167,add,29.0,1,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H],C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1,30,add 1 at position 29,flow_matching,0.3,2.0,50,187
168,add,30.0,C,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1C,31,add C at position 30,flow_matching,0.3,2.0,50,187
169,add,31.0,C,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1C,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CC,32,add C at position 31,flow_matching,0.3,2.0,50,187
170,add,32.0,C,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CC,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCC,33,add C at position 32,flow_matching,0.3,2.0,50,187
171,add,33.0,N,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCC,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN,34,add N at position 33,flow_matching,0.3,2.0,50,187
172,add,34.0,(,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(,35,add ( at position 34,flow_matching,0.3,2.0,50,187
173,add,35.0,C,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C,36,add C at position 35,flow_matching,0.3,2.0,50,187
174,add,36.0,(,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(,37,add ( at position 36,flow_matching,0.3,2.0,50,187
175,add,37.0,=,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=,38,add = at position 37,flow_matching,0.3,2.0,50,187
176,add,38.0,O,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O,39,add O at position 38,flow_matching,0.3,2.0,50,187
177,add,39.0,),,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O),40,add ) at position 39,flow_matching,0.3,2.0,50,187
178,add,40.0,N,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O),C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)N,41,add N at position 40,flow_matching,0.3,2.0,50,187
179,add,41.0,C,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)N,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC,42,add C at position 41,flow_matching,0.3,2.0,50,187
180,add,42.0,2,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2,43,add 2 at position 42,flow_matching,0.3,2.0,50,187
181,add,43.0,C,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2C,44,add C at position 43,flow_matching,0.3,2.0,50,187
182,add,44.0,C,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2C,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC,45,add C at position 44,flow_matching,0.3,2.0,50,187
183,add,45.0,2,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC2,46,add 2 at position 45,flow_matching,0.3,2.0,50,187
184,add,46.0,),,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC2,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC2),47,add ) at position 46,flow_matching,0.3,2.0,50,187
185,add,47.0,C,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC2),C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC2)C,48,add C at position 47,flow_matching,0.3,2.0,50,187
186,add,48.0,1,,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC2)C,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC2)C1,49,add 1 at position 48,flow_matching,0.3,2.0,50,187
187,add,49.0,"
",,C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC2)C1,"C[C@@H]1Cc2ccccc2N1C(=O)[C@H]1CCCN(C(=O)NC2CC2)C1
",50,"add 
 at position 49",flow_matching,0.3,2.0,50,187
