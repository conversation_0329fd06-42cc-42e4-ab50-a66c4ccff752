step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,45,156
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,45,156
2,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,45,156
3,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,45,156
4,add,1.0,[,,+,+[,2,add [ at position 1,flow_matching,0.3,2.0,45,156
5,replace,0.0,C,+,+[,C[,2,replace + at position 0 with C,flow_matching,0.3,2.0,45,156
6,remove,0.0,C,,C[,[,1,remove C from position 0,flow_matching,0.3,2.0,45,156
7,add,1.0,/,,[,[/,2,add / at position 1,flow_matching,0.3,2.0,45,156
8,replace,0.0,C,[,[/,C/,2,replace [ at position 0 with C,flow_matching,0.3,2.0,45,156
9,remove,0.0,C,,C/,/,1,remove C from position 0,flow_matching,0.3,2.0,45,156
10,replace,0.0,C,/,/,C,1,replace / at position 0 with C,flow_matching,0.3,2.0,45,156
11,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,45,156
12,add,0.0,H,,,H,1,add H at position 0,flow_matching,0.3,2.0,45,156
13,remove,0.0,H,,H,,0,remove H from position 0,flow_matching,0.3,2.0,45,156
14,add,0.0,2,,,2,1,add 2 at position 0,flow_matching,0.3,2.0,45,156
15,add,0.0,[,,2,[2,2,add [ at position 0,flow_matching,0.3,2.0,45,156
16,add,1.0,C,,[2,[C2,3,add C at position 1,flow_matching,0.3,2.0,45,156
17,replace,0.0,F,[,[C2,FC2,3,replace [ at position 0 with F,flow_matching,0.3,2.0,45,156
18,replace,2.0,@,2,FC2,FC@,3,replace 2 at position 2 with @,flow_matching,0.3,2.0,45,156
19,remove,2.0,@,,FC@,FC,2,remove @ from position 2,flow_matching,0.3,2.0,45,156
20,remove,0.0,F,,FC,C,1,remove F from position 0,flow_matching,0.3,2.0,45,156
21,add,1.0,/,,C,C/,2,add / at position 1,flow_matching,0.3,2.0,45,156
22,remove,1.0,/,,C/,C,1,remove / from position 1,flow_matching,0.3,2.0,45,156
23,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,45,156
24,add,0.0,2,,,2,1,add 2 at position 0,flow_matching,0.3,2.0,45,156
25,replace,0.0,6,2,2,6,1,replace 2 at position 0 with 6,flow_matching,0.3,2.0,45,156
26,replace,0.0,C,6,6,C,1,replace 6 at position 0 with C,flow_matching,0.3,2.0,45,156
27,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,45,156
28,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,45,156
29,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,45,156
30,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,45,156
31,add,1.0,F,,3,3F,2,add F at position 1,flow_matching,0.3,2.0,45,156
32,remove,1.0,F,,3F,3,1,remove F from position 1,flow_matching,0.3,2.0,45,156
33,add,0.0,O,,3,O3,2,add O at position 0,flow_matching,0.3,2.0,45,156
34,add,2.0,I,,O3,O3I,3,add I at position 2,flow_matching,0.3,2.0,45,156
35,replace,1.0,#,3,O3I,O#I,3,replace 3 at position 1 with #,flow_matching,0.3,2.0,45,156
36,replace,0.0,C,O,O#I,C#I,3,replace O at position 0 with C,flow_matching,0.3,2.0,45,156
37,remove,1.0,#,,C#I,CI,2,remove # from position 1,flow_matching,0.3,2.0,45,156
38,add,1.0,H,,CI,CHI,3,add H at position 1,flow_matching,0.3,2.0,45,156
39,replace,1.0,/,H,CHI,C/I,3,replace H at position 1 with /,flow_matching,0.3,2.0,45,156
40,replace,0.0,\,C,C/I,\/I,3,replace C at position 0 with \,flow_matching,0.3,2.0,45,156
41,replace,0.0,C,\,\/I,C/I,3,replace \ at position 0 with C,flow_matching,0.3,2.0,45,156
42,add,2.0,7,,C/I,C/7I,4,add 7 at position 2,flow_matching,0.3,2.0,45,156
43,remove,1.0,/,,C/7I,C7I,3,remove / from position 1,flow_matching,0.3,2.0,45,156
44,replace,0.0,/,C,C7I,/7I,3,replace C at position 0 with /,flow_matching,0.3,2.0,45,156
45,replace,1.0,n,7,/7I,/nI,3,replace 7 at position 1 with n,flow_matching,0.3,2.0,45,156
46,replace,0.0,C,/,/nI,CnI,3,replace / at position 0 with C,flow_matching,0.3,2.0,45,156
47,remove,0.0,C,,CnI,nI,2,remove C from position 0,flow_matching,0.3,2.0,45,156
48,replace,0.0,C,n,nI,CI,2,replace n at position 0 with C,flow_matching,0.3,2.0,45,156
49,add,1.0,-,,CI,C-I,3,add - at position 1,flow_matching,0.3,2.0,45,156
50,add,2.0,+,,C-I,C-+I,4,add + at position 2,flow_matching,0.3,2.0,45,156
51,replace,1.0,/,-,C-+I,C/+I,4,replace - at position 1 with /,flow_matching,0.3,2.0,45,156
52,replace,0.0,(,C,C/+I,(/+I,4,replace C at position 0 with (,flow_matching,0.3,2.0,45,156
53,remove,0.0,(,,(/+I,/+I,3,remove ( from position 0,flow_matching,0.3,2.0,45,156
54,add,3.0,n,,/+I,/+In,4,add n at position 3,flow_matching,0.3,2.0,45,156
55,replace,0.0,C,/,/+In,C+In,4,replace / at position 0 with C,flow_matching,0.3,2.0,45,156
56,replace,1.0,F,+,C+In,CFIn,4,replace + at position 1 with F,flow_matching,0.3,2.0,45,156
57,add,0.0,I,,CFIn,ICFIn,5,add I at position 0,flow_matching,0.3,2.0,45,156
58,add,2.0,5,,ICFIn,IC5FIn,6,add 5 at position 2,flow_matching,0.3,2.0,45,156
59,replace,2.0,+,5,IC5FIn,IC+FIn,6,replace 5 at position 2 with +,flow_matching,0.3,2.0,45,156
60,replace,2.0,N,+,IC+FIn,ICNFIn,6,replace + at position 2 with N,flow_matching,0.3,2.0,45,156
61,remove,4.0,I,,ICNFIn,ICNFn,5,remove I from position 4,flow_matching,0.3,2.0,45,156
62,replace,4.0,3,n,ICNFn,ICNF3,5,replace n at position 4 with 3,flow_matching,0.3,2.0,45,156
63,replace,0.0,C,I,ICNF3,CCNF3,5,replace I at position 0 with C,flow_matching,0.3,2.0,45,156
64,add,1.0,\,,CCNF3,C\CNF3,6,add \ at position 1,flow_matching,0.3,2.0,45,156
65,remove,0.0,C,,C\CNF3,\CNF3,5,remove C from position 0,flow_matching,0.3,2.0,45,156
66,remove,2.0,N,,\CNF3,\CF3,4,remove N from position 2,flow_matching,0.3,2.0,45,156
67,replace,0.0,C,\,\CF3,CCF3,4,replace \ at position 0 with C,flow_matching,0.3,2.0,45,156
68,add,3.0,6,,CCF3,CCF63,5,add 6 at position 3,flow_matching,0.3,2.0,45,156
69,replace,4.0,-,3,CCF63,CCF6-,5,replace 3 at position 4 with -,flow_matching,0.3,2.0,45,156
70,replace,4.0,+,-,CCF6-,CCF6+,5,replace - at position 4 with +,flow_matching,0.3,2.0,45,156
71,add,1.0,O,,CCF6+,COCF6+,6,add O at position 1,flow_matching,0.3,2.0,45,156
72,replace,2.0,s,C,COCF6+,COsF6+,6,replace C at position 2 with s,flow_matching,0.3,2.0,45,156
73,add,6.0,3,,COsF6+,COsF6+3,7,add 3 at position 6,flow_matching,0.3,2.0,45,156
74,replace,0.0,4,C,COsF6+3,4OsF6+3,7,replace C at position 0 with 4,flow_matching,0.3,2.0,45,156
75,add,6.0,-,,4OsF6+3,4OsF6+-3,8,add - at position 6,flow_matching,0.3,2.0,45,156
76,replace,0.0,C,4,4OsF6+-3,COsF6+-3,8,replace 4 at position 0 with C,flow_matching,0.3,2.0,45,156
77,replace,2.0,c,s,COsF6+-3,COcF6+-3,8,replace s at position 2 with c,flow_matching,0.3,2.0,45,156
78,remove,6.0,-,,COcF6+-3,COcF6+3,7,remove - from position 6,flow_matching,0.3,2.0,45,156
79,replace,1.0,/,O,COcF6+3,C/cF6+3,7,replace O at position 1 with /,flow_matching,0.3,2.0,45,156
80,add,4.0,l,,C/cF6+3,C/cFl6+3,8,add l at position 4,flow_matching,0.3,2.0,45,156
81,add,8.0,o,,C/cFl6+3,C/cFl6+3o,9,add o at position 8,flow_matching,0.3,2.0,45,156
82,replace,2.0,C,c,C/cFl6+3o,C/CFl6+3o,9,replace c at position 2 with C,flow_matching,0.3,2.0,45,156
83,replace,3.0,(,F,C/CFl6+3o,C/C(l6+3o,9,replace F at position 3 with (,flow_matching,0.3,2.0,45,156
84,replace,4.0,=,l,C/C(l6+3o,C/C(=6+3o,9,replace l at position 4 with =,flow_matching,0.3,2.0,45,156
85,add,7.0,n,,C/C(=6+3o,C/C(=6+n3o,10,add n at position 7,flow_matching,0.3,2.0,45,156
86,add,5.0,C,,C/C(=6+n3o,C/C(=C6+n3o,11,add C at position 5,flow_matching,0.3,2.0,45,156
87,replace,5.0,N,C,C/C(=C6+n3o,C/C(=N6+n3o,11,replace C at position 5 with N,flow_matching,0.3,2.0,45,156
88,replace,6.0,\,6,C/C(=N6+n3o,C/C(=N\+n3o,11,replace 6 at position 6 with \,flow_matching,0.3,2.0,45,156
89,add,0.0,\,,C/C(=N\+n3o,\C/C(=N\+n3o,12,add \ at position 0,flow_matching,0.3,2.0,45,156
90,remove,11.0,o,,\C/C(=N\+n3o,\C/C(=N\+n3,11,remove o from position 11,flow_matching,0.3,2.0,45,156
91,add,11.0,1,,\C/C(=N\+n3,\C/C(=N\+n31,12,add 1 at position 11,flow_matching,0.3,2.0,45,156
92,replace,0.0,C,\,\C/C(=N\+n31,CC/C(=N\+n31,12,replace \ at position 0 with C,flow_matching,0.3,2.0,45,156
93,replace,1.0,/,C,CC/C(=N\+n31,C//C(=N\+n31,12,replace C at position 1 with /,flow_matching,0.3,2.0,45,156
94,remove,10.0,3,,C//C(=N\+n31,C//C(=N\+n1,11,remove 3 from position 10,flow_matching,0.3,2.0,45,156
95,remove,1.0,/,,C//C(=N\+n1,C/C(=N\+n1,10,remove / from position 1,flow_matching,0.3,2.0,45,156
96,replace,7.0,N,+,C/C(=N\+n1,C/C(=N\Nn1,10,replace + at position 7 with N,flow_matching,0.3,2.0,45,156
97,add,0.0,6,,C/C(=N\Nn1,6C/C(=N\Nn1,11,add 6 at position 0,flow_matching,0.3,2.0,45,156
98,replace,2.0,\,/,6C/C(=N\Nn1,6C\C(=N\Nn1,11,replace / at position 2 with \,flow_matching,0.3,2.0,45,156
99,replace,0.0,C,6,6C\C(=N\Nn1,CC\C(=N\Nn1,11,replace 6 at position 0 with C,flow_matching,0.3,2.0,45,156
100,remove,10.0,1,,CC\C(=N\Nn1,CC\C(=N\Nn,10,remove 1 from position 10,flow_matching,0.3,2.0,45,156
101,replace,4.0,1,(,CC\C(=N\Nn,CC\C1=N\Nn,10,replace ( at position 4 with 1,flow_matching,0.3,2.0,45,156
102,add,1.0,6,,CC\C1=N\Nn,C6C\C1=N\Nn,11,add 6 at position 1,flow_matching,0.3,2.0,45,156
103,remove,2.0,C,,C6C\C1=N\Nn,C6\C1=N\Nn,10,remove C from position 2,flow_matching,0.3,2.0,45,156
104,replace,4.0,\,1,C6\C1=N\Nn,C6\C\=N\Nn,10,replace 1 at position 4 with \,flow_matching,0.3,2.0,45,156
105,remove,2.0,\,,C6\C\=N\Nn,C6C\=N\Nn,9,remove \ from position 2,flow_matching,0.3,2.0,45,156
106,replace,1.0,/,6,C6C\=N\Nn,C/C\=N\Nn,9,replace 6 at position 1 with /,flow_matching,0.3,2.0,45,156
107,remove,5.0,N,,C/C\=N\Nn,C/C\=\Nn,8,remove N from position 5,flow_matching,0.3,2.0,45,156
108,replace,3.0,(,\,C/C\=\Nn,C/C(=\Nn,8,replace \ at position 3 with (,flow_matching,0.3,2.0,45,156
109,replace,5.0,N,\,C/C(=\Nn,C/C(=NNn,8,replace \ at position 5 with N,flow_matching,0.3,2.0,45,156
110,remove,4.0,=,,C/C(=NNn,C/C(NNn,7,remove = from position 4,flow_matching,0.3,2.0,45,156
111,add,2.0,+,,C/C(NNn,C/+C(NNn,8,add + at position 2,flow_matching,0.3,2.0,45,156
112,add,0.0,C,,C/+C(NNn,CC/+C(NNn,9,add C at position 0,flow_matching,0.3,2.0,45,156
113,replace,1.0,l,C,CC/+C(NNn,Cl/+C(NNn,9,replace C at position 1 with l,flow_matching,0.3,2.0,45,156
114,replace,1.0,/,l,Cl/+C(NNn,C//+C(NNn,9,replace l at position 1 with /,flow_matching,0.3,2.0,45,156
115,replace,2.0,C,/,C//+C(NNn,C/C+C(NNn,9,replace / at position 2 with C,flow_matching,0.3,2.0,45,156
116,replace,3.0,(,+,C/C+C(NNn,C/C(C(NNn,9,replace + at position 3 with (,flow_matching,0.3,2.0,45,156
117,replace,4.0,=,C,C/C(C(NNn,C/C(=(NNn,9,replace C at position 4 with =,flow_matching,0.3,2.0,45,156
118,replace,5.0,N,(,C/C(=(NNn,C/C(=NNNn,9,replace ( at position 5 with N,flow_matching,0.3,2.0,45,156
119,replace,6.0,\,N,C/C(=NNNn,C/C(=N\Nn,9,replace N at position 6 with \,flow_matching,0.3,2.0,45,156
120,replace,8.0,c,n,C/C(=N\Nn,C/C(=N\Nc,9,replace n at position 8 with c,flow_matching,0.3,2.0,45,156
121,add,9.0,1,,C/C(=N\Nc,C/C(=N\Nc1,10,add 1 at position 9,flow_matching,0.3,2.0,45,156
122,add,10.0,n,,C/C(=N\Nc1,C/C(=N\Nc1n,11,add n at position 10,flow_matching,0.3,2.0,45,156
123,add,11.0,c,,C/C(=N\Nc1n,C/C(=N\Nc1nc,12,add c at position 11,flow_matching,0.3,2.0,45,156
124,add,12.0,n,,C/C(=N\Nc1nc,C/C(=N\Nc1ncn,13,add n at position 12,flow_matching,0.3,2.0,45,156
125,add,13.0,c,,C/C(=N\Nc1ncn,C/C(=N\Nc1ncnc,14,add c at position 13,flow_matching,0.3,2.0,45,156
126,add,14.0,2,,C/C(=N\Nc1ncnc,C/C(=N\Nc1ncnc2,15,add 2 at position 14,flow_matching,0.3,2.0,45,156
127,add,15.0,s,,C/C(=N\Nc1ncnc2,C/C(=N\Nc1ncnc2s,16,add s at position 15,flow_matching,0.3,2.0,45,156
128,add,16.0,c,,C/C(=N\Nc1ncnc2s,C/C(=N\Nc1ncnc2sc,17,add c at position 16,flow_matching,0.3,2.0,45,156
129,add,17.0,(,,C/C(=N\Nc1ncnc2sc,C/C(=N\Nc1ncnc2sc(,18,add ( at position 17,flow_matching,0.3,2.0,45,156
130,add,18.0,C,,C/C(=N\Nc1ncnc2sc(,C/C(=N\Nc1ncnc2sc(C,19,add C at position 18,flow_matching,0.3,2.0,45,156
131,add,19.0,),,C/C(=N\Nc1ncnc2sc(C,C/C(=N\Nc1ncnc2sc(C),20,add ) at position 19,flow_matching,0.3,2.0,45,156
132,add,20.0,c,,C/C(=N\Nc1ncnc2sc(C),C/C(=N\Nc1ncnc2sc(C)c,21,add c at position 20,flow_matching,0.3,2.0,45,156
133,add,21.0,(,,C/C(=N\Nc1ncnc2sc(C)c,C/C(=N\Nc1ncnc2sc(C)c(,22,add ( at position 21,flow_matching,0.3,2.0,45,156
134,add,22.0,C,,C/C(=N\Nc1ncnc2sc(C)c(,C/C(=N\Nc1ncnc2sc(C)c(C,23,add C at position 22,flow_matching,0.3,2.0,45,156
135,add,23.0,),,C/C(=N\Nc1ncnc2sc(C)c(C,C/C(=N\Nc1ncnc2sc(C)c(C),24,add ) at position 23,flow_matching,0.3,2.0,45,156
136,add,24.0,c,,C/C(=N\Nc1ncnc2sc(C)c(C),C/C(=N\Nc1ncnc2sc(C)c(C)c,25,add c at position 24,flow_matching,0.3,2.0,45,156
137,add,25.0,1,,C/C(=N\Nc1ncnc2sc(C)c(C)c,C/C(=N\Nc1ncnc2sc(C)c(C)c1,26,add 1 at position 25,flow_matching,0.3,2.0,45,156
138,add,26.0,2,,C/C(=N\Nc1ncnc2sc(C)c(C)c1,C/C(=N\Nc1ncnc2sc(C)c(C)c12,27,add 2 at position 26,flow_matching,0.3,2.0,45,156
139,add,27.0,),,C/C(=N\Nc1ncnc2sc(C)c(C)c12,C/C(=N\Nc1ncnc2sc(C)c(C)c12),28,add ) at position 27,flow_matching,0.3,2.0,45,156
140,add,28.0,c,,C/C(=N\Nc1ncnc2sc(C)c(C)c12),C/C(=N\Nc1ncnc2sc(C)c(C)c12)c,29,add c at position 28,flow_matching,0.3,2.0,45,156
141,add,29.0,1,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1,30,add 1 at position 29,flow_matching,0.3,2.0,45,156
142,add,30.0,c,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1c,31,add c at position 30,flow_matching,0.3,2.0,45,156
143,add,31.0,c,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1c,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cc,32,add c at position 31,flow_matching,0.3,2.0,45,156
144,add,32.0,c,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cc,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1ccc,33,add c at position 32,flow_matching,0.3,2.0,45,156
145,add,33.0,c,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1ccc,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc,34,add c at position 33,flow_matching,0.3,2.0,45,156
146,add,34.0,(,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(,35,add ( at position 34,flow_matching,0.3,2.0,45,156
147,add,35.0,O,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(O,36,add O at position 35,flow_matching,0.3,2.0,45,156
148,add,36.0,C,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(O,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC,37,add C at position 36,flow_matching,0.3,2.0,45,156
149,add,37.0,(,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(,38,add ( at position 37,flow_matching,0.3,2.0,45,156
150,add,38.0,F,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F,39,add F at position 38,flow_matching,0.3,2.0,45,156
151,add,39.0,),,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F),40,add ) at position 39,flow_matching,0.3,2.0,45,156
152,add,40.0,F,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F),C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F)F,41,add F at position 40,flow_matching,0.3,2.0,45,156
153,add,41.0,),,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F)F,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F)F),42,add ) at position 41,flow_matching,0.3,2.0,45,156
154,add,42.0,c,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F)F),C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F)F)c,43,add c at position 42,flow_matching,0.3,2.0,45,156
155,add,43.0,1,,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F)F)c,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F)F)c1,44,add 1 at position 43,flow_matching,0.3,2.0,45,156
156,add,44.0,"
",,C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F)F)c1,"C/C(=N\Nc1ncnc2sc(C)c(C)c12)c1cccc(OC(F)F)c1
",45,"add 
 at position 44",flow_matching,0.3,2.0,45,156
