step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,52,140
1,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,52,140
2,replace,0.0,C,6,6,C,1,replace 6 at position 0 with C,flow_matching,0.3,2.0,52,140
3,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,52,140
4,add,0.0,r,,,r,1,add r at position 0,flow_matching,0.3,2.0,52,140
5,remove,0.0,r,,r,,0,remove r from position 0,flow_matching,0.3,2.0,52,140
6,add,0.0,C,,,C,1,add <PERSON> at position 0,flow_matching,0.3,2.0,52,140
7,add,0.0,l,,C,lC,2,add l at position 0,flow_matching,0.3,2.0,52,140
8,remove,0.0,l,,lC,C,1,remove l from position 0,flow_matching,0.3,2.0,52,140
9,add,1.0,),,C,C),2,add ) at position 1,flow_matching,0.3,2.0,52,140
10,remove,1.0,),,C),C,1,remove ) from position 1,flow_matching,0.3,2.0,52,140
11,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,52,140
12,remove,1.0,O,,CO,C,1,remove O from position 1,flow_matching,0.3,2.0,52,140
13,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,52,140
14,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,52,140
15,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,52,140
16,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,52,140
17,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,52,140
18,add,0.0,@,,CO,@CO,3,add @ at position 0,flow_matching,0.3,2.0,52,140
19,add,1.0,-,,@CO,@-CO,4,add - at position 1,flow_matching,0.3,2.0,52,140
20,remove,2.0,C,,@-CO,@-O,3,remove C from position 2,flow_matching,0.3,2.0,52,140
21,replace,0.0,],@,@-O,]-O,3,replace @ at position 0 with ],flow_matching,0.3,2.0,52,140
22,add,1.0,S,,]-O,]S-O,4,add S at position 1,flow_matching,0.3,2.0,52,140
23,replace,0.0,C,],]S-O,CS-O,4,replace ] at position 0 with C,flow_matching,0.3,2.0,52,140
24,replace,3.0,r,O,CS-O,CS-r,4,replace O at position 3 with r,flow_matching,0.3,2.0,52,140
25,add,2.0,o,,CS-r,CSo-r,5,add o at position 2,flow_matching,0.3,2.0,52,140
26,replace,1.0,O,S,CSo-r,COo-r,5,replace S at position 1 with O,flow_matching,0.3,2.0,52,140
27,add,2.0,\,,COo-r,CO\o-r,6,add \ at position 2,flow_matching,0.3,2.0,52,140
28,add,6.0,H,,CO\o-r,CO\o-rH,7,add H at position 6,flow_matching,0.3,2.0,52,140
29,remove,5.0,r,,CO\o-rH,CO\o-H,6,remove r from position 5,flow_matching,0.3,2.0,52,140
30,replace,2.0,C,\,CO\o-H,COCo-H,6,replace \ at position 2 with C,flow_matching,0.3,2.0,52,140
31,replace,4.0,s,-,COCo-H,COCosH,6,replace - at position 4 with s,flow_matching,0.3,2.0,52,140
32,add,3.0,5,,COCosH,COC5osH,7,add 5 at position 3,flow_matching,0.3,2.0,52,140
33,remove,5.0,s,,COC5osH,COC5oH,6,remove s from position 5,flow_matching,0.3,2.0,52,140
34,add,0.0,[,,COC5oH,[COC5oH,7,add [ at position 0,flow_matching,0.3,2.0,52,140
35,add,1.0,5,,[COC5oH,[5COC5oH,8,add 5 at position 1,flow_matching,0.3,2.0,52,140
36,add,6.0,#,,[5COC5oH,[5COC5#oH,9,add # at position 6,flow_matching,0.3,2.0,52,140
37,replace,0.0,N,[,[5COC5#oH,N5COC5#oH,9,replace [ at position 0 with N,flow_matching,0.3,2.0,52,140
38,add,2.0,[,,N5COC5#oH,N5[COC5#oH,10,add [ at position 2,flow_matching,0.3,2.0,52,140
39,add,6.0,I,,N5[COC5#oH,N5[COCI5#oH,11,add I at position 6,flow_matching,0.3,2.0,52,140
40,replace,8.0,n,#,N5[COCI5#oH,N5[COCI5noH,11,replace # at position 8 with n,flow_matching,0.3,2.0,52,140
41,remove,9.0,o,,N5[COCI5noH,N5[COCI5nH,10,remove o from position 9,flow_matching,0.3,2.0,52,140
42,add,0.0,O,,N5[COCI5nH,ON5[COCI5nH,11,add O at position 0,flow_matching,0.3,2.0,52,140
43,replace,0.0,C,O,ON5[COCI5nH,CN5[COCI5nH,11,replace O at position 0 with C,flow_matching,0.3,2.0,52,140
44,add,4.0,7,,CN5[COCI5nH,CN5[7COCI5nH,12,add 7 at position 4,flow_matching,0.3,2.0,52,140
45,replace,1.0,O,N,CN5[7COCI5nH,CO5[7COCI5nH,12,replace N at position 1 with O,flow_matching,0.3,2.0,52,140
46,add,7.0,1,,CO5[7COCI5nH,CO5[7CO1CI5nH,13,add 1 at position 7,flow_matching,0.3,2.0,52,140
47,add,9.0,F,,CO5[7CO1CI5nH,CO5[7CO1CFI5nH,14,add F at position 9,flow_matching,0.3,2.0,52,140
48,replace,5.0,),C,CO5[7CO1CFI5nH,CO5[7)O1CFI5nH,14,replace C at position 5 with ),flow_matching,0.3,2.0,52,140
49,replace,3.0,7,[,CO5[7)O1CFI5nH,CO577)O1CFI5nH,14,replace [ at position 3 with 7,flow_matching,0.3,2.0,52,140
50,remove,8.0,C,,CO577)O1CFI5nH,CO577)O1FI5nH,13,remove C from position 8,flow_matching,0.3,2.0,52,140
51,replace,2.0,C,5,CO577)O1FI5nH,COC77)O1FI5nH,13,replace 5 at position 2 with C,flow_matching,0.3,2.0,52,140
52,replace,3.0,C,7,COC77)O1FI5nH,COCC7)O1FI5nH,13,replace 7 at position 3 with C,flow_matching,0.3,2.0,52,140
53,replace,3.0,\,C,COCC7)O1FI5nH,COC\7)O1FI5nH,13,replace C at position 3 with \,flow_matching,0.3,2.0,52,140
54,replace,7.0,6,1,COC\7)O1FI5nH,COC\7)O6FI5nH,13,replace 1 at position 7 with 6,flow_matching,0.3,2.0,52,140
55,add,10.0,F,,COC\7)O6FI5nH,COC\7)O6FIF5nH,14,add F at position 10,flow_matching,0.3,2.0,52,140
56,remove,7.0,6,,COC\7)O6FIF5nH,COC\7)OFIF5nH,13,remove 6 from position 7,flow_matching,0.3,2.0,52,140
57,replace,3.0,C,\,COC\7)OFIF5nH,COCC7)OFIF5nH,13,replace \ at position 3 with C,flow_matching,0.3,2.0,52,140
58,replace,4.0,N,7,COCC7)OFIF5nH,COCCN)OFIF5nH,13,replace 7 at position 4 with N,flow_matching,0.3,2.0,52,140
59,remove,0.0,C,,COCCN)OFIF5nH,OCCN)OFIF5nH,12,remove C from position 0,flow_matching,0.3,2.0,52,140
60,replace,0.0,C,O,OCCN)OFIF5nH,CCCN)OFIF5nH,12,replace O at position 0 with C,flow_matching,0.3,2.0,52,140
61,add,0.0,\,,CCCN)OFIF5nH,\CCCN)OFIF5nH,13,add \ at position 0,flow_matching,0.3,2.0,52,140
62,remove,8.0,I,,\CCCN)OFIF5nH,\CCCN)OFF5nH,12,remove I from position 8,flow_matching,0.3,2.0,52,140
63,add,4.0,7,,\CCCN)OFF5nH,\CCC7N)OFF5nH,13,add 7 at position 4,flow_matching,0.3,2.0,52,140
64,remove,3.0,C,,\CCC7N)OFF5nH,\CC7N)OFF5nH,12,remove C from position 3,flow_matching,0.3,2.0,52,140
65,add,9.0,5,,\CC7N)OFF5nH,\CC7N)OFF55nH,13,add 5 at position 9,flow_matching,0.3,2.0,52,140
66,replace,0.0,6,\,\CC7N)OFF55nH,6CC7N)OFF55nH,13,replace \ at position 0 with 6,flow_matching,0.3,2.0,52,140
67,add,6.0,[,,6CC7N)OFF55nH,6CC7N)[OFF55nH,14,add [ at position 6,flow_matching,0.3,2.0,52,140
68,replace,0.0,C,6,6CC7N)[OFF55nH,CCC7N)[OFF55nH,14,replace 6 at position 0 with C,flow_matching,0.3,2.0,52,140
69,replace,2.0,\,C,CCC7N)[OFF55nH,CC\7N)[OFF55nH,14,replace C at position 2 with \,flow_matching,0.3,2.0,52,140
70,replace,2.0,O,\,CC\7N)[OFF55nH,CCO7N)[OFF55nH,14,replace \ at position 2 with O,flow_matching,0.3,2.0,52,140
71,replace,2.0,-,O,CCO7N)[OFF55nH,CC-7N)[OFF55nH,14,replace O at position 2 with -,flow_matching,0.3,2.0,52,140
72,replace,8.0,o,F,CC-7N)[OFF55nH,CC-7N)[OoF55nH,14,replace F at position 8 with o,flow_matching,0.3,2.0,52,140
73,remove,11.0,5,,CC-7N)[OoF55nH,CC-7N)[OoF5nH,13,remove 5 from position 11,flow_matching,0.3,2.0,52,140
74,replace,6.0,I,[,CC-7N)[OoF5nH,CC-7N)IOoF5nH,13,replace [ at position 6 with I,flow_matching,0.3,2.0,52,140
75,replace,1.0,O,C,CC-7N)IOoF5nH,CO-7N)IOoF5nH,13,replace C at position 1 with O,flow_matching,0.3,2.0,52,140
76,replace,7.0,N,O,CO-7N)IOoF5nH,CO-7N)INoF5nH,13,replace O at position 7 with N,flow_matching,0.3,2.0,52,140
77,add,4.0,N,,CO-7N)INoF5nH,CO-7NN)INoF5nH,14,add N at position 4,flow_matching,0.3,2.0,52,140
78,replace,2.0,C,-,CO-7NN)INoF5nH,COC7NN)INoF5nH,14,replace - at position 2 with C,flow_matching,0.3,2.0,52,140
79,add,3.0,7,,COC7NN)INoF5nH,COC77NN)INoF5nH,15,add 7 at position 3,flow_matching,0.3,2.0,52,140
80,add,10.0,I,,COC77NN)INoF5nH,COC77NN)INIoF5nH,16,add I at position 10,flow_matching,0.3,2.0,52,140
81,add,2.0,B,,COC77NN)INIoF5nH,COBC77NN)INIoF5nH,17,add B at position 2,flow_matching,0.3,2.0,52,140
82,remove,13.0,F,,COBC77NN)INIoF5nH,COBC77NN)INIo5nH,16,remove F from position 13,flow_matching,0.3,2.0,52,140
83,replace,4.0,2,7,COBC77NN)INIo5nH,COBC27NN)INIo5nH,16,replace 7 at position 4 with 2,flow_matching,0.3,2.0,52,140
84,add,1.0,3,,COBC27NN)INIo5nH,C3OBC27NN)INIo5nH,17,add 3 at position 1,flow_matching,0.3,2.0,52,140
85,add,3.0,+,,C3OBC27NN)INIo5nH,C3O+BC27NN)INIo5nH,18,add + at position 3,flow_matching,0.3,2.0,52,140
86,replace,5.0,],C,C3O+BC27NN)INIo5nH,C3O+B]27NN)INIo5nH,18,replace C at position 5 with ],flow_matching,0.3,2.0,52,140
87,add,3.0,/,,C3O+B]27NN)INIo5nH,C3O/+B]27NN)INIo5nH,19,add / at position 3,flow_matching,0.3,2.0,52,140
88,replace,1.0,O,3,C3O/+B]27NN)INIo5nH,COO/+B]27NN)INIo5nH,19,replace 3 at position 1 with O,flow_matching,0.3,2.0,52,140
89,replace,4.0,#,+,COO/+B]27NN)INIo5nH,COO/#B]27NN)INIo5nH,19,replace + at position 4 with #,flow_matching,0.3,2.0,52,140
90,replace,2.0,C,O,COO/#B]27NN)INIo5nH,COC/#B]27NN)INIo5nH,19,replace O at position 2 with C,flow_matching,0.3,2.0,52,140
91,add,10.0,S,,COC/#B]27NN)INIo5nH,COC/#B]27NSN)INIo5nH,20,add S at position 10,flow_matching,0.3,2.0,52,140
92,replace,3.0,C,/,COC/#B]27NSN)INIo5nH,COCC#B]27NSN)INIo5nH,20,replace / at position 3 with C,flow_matching,0.3,2.0,52,140
93,replace,4.0,N,#,COCC#B]27NSN)INIo5nH,COCCNB]27NSN)INIo5nH,20,replace # at position 4 with N,flow_matching,0.3,2.0,52,140
94,replace,5.0,1,B,COCCNB]27NSN)INIo5nH,COCCN1]27NSN)INIo5nH,20,replace B at position 5 with 1,flow_matching,0.3,2.0,52,140
95,replace,6.0,C,],COCCN1]27NSN)INIo5nH,COCCN1C27NSN)INIo5nH,20,replace ] at position 6 with C,flow_matching,0.3,2.0,52,140
96,replace,7.0,(,2,COCCN1C27NSN)INIo5nH,COCCN1C(7NSN)INIo5nH,20,replace 2 at position 7 with (,flow_matching,0.3,2.0,52,140
97,replace,8.0,=,7,COCCN1C(7NSN)INIo5nH,COCCN1C(=NSN)INIo5nH,20,replace 7 at position 8 with =,flow_matching,0.3,2.0,52,140
98,replace,9.0,O,N,COCCN1C(=NSN)INIo5nH,COCCN1C(=OSN)INIo5nH,20,replace N at position 9 with O,flow_matching,0.3,2.0,52,140
99,replace,10.0,),S,COCCN1C(=OSN)INIo5nH,COCCN1C(=O)N)INIo5nH,20,replace S at position 10 with ),flow_matching,0.3,2.0,52,140
100,replace,11.0,C,N,COCCN1C(=O)N)INIo5nH,COCCN1C(=O)C)INIo5nH,20,replace N at position 11 with C,flow_matching,0.3,2.0,52,140
101,replace,12.0,C,),COCCN1C(=O)C)INIo5nH,COCCN1C(=O)CCINIo5nH,20,replace ) at position 12 with C,flow_matching,0.3,2.0,52,140
102,replace,13.0,[,I,COCCN1C(=O)CCINIo5nH,COCCN1C(=O)CC[NIo5nH,20,replace I at position 13 with [,flow_matching,0.3,2.0,52,140
103,replace,14.0,C,N,COCCN1C(=O)CC[NIo5nH,COCCN1C(=O)CC[CIo5nH,20,replace N at position 14 with C,flow_matching,0.3,2.0,52,140
104,replace,15.0,@,I,COCCN1C(=O)CC[CIo5nH,COCCN1C(=O)CC[C@o5nH,20,replace I at position 15 with @,flow_matching,0.3,2.0,52,140
105,replace,16.0,@,o,COCCN1C(=O)CC[C@o5nH,COCCN1C(=O)CC[C@@5nH,20,replace o at position 16 with @,flow_matching,0.3,2.0,52,140
106,replace,17.0,H,5,COCCN1C(=O)CC[C@@5nH,COCCN1C(=O)CC[C@@HnH,20,replace 5 at position 17 with H,flow_matching,0.3,2.0,52,140
107,replace,18.0,],n,COCCN1C(=O)CC[C@@HnH,COCCN1C(=O)CC[C@@H]H,20,replace n at position 18 with ],flow_matching,0.3,2.0,52,140
108,replace,19.0,2,H,COCCN1C(=O)CC[C@@H]H,COCCN1C(=O)CC[C@@H]2,20,replace H at position 19 with 2,flow_matching,0.3,2.0,52,140
109,add,20.0,C,,COCCN1C(=O)CC[C@@H]2,COCCN1C(=O)CC[C@@H]2C,21,add C at position 20,flow_matching,0.3,2.0,52,140
110,add,21.0,[,,COCCN1C(=O)CC[C@@H]2C,COCCN1C(=O)CC[C@@H]2C[,22,add [ at position 21,flow_matching,0.3,2.0,52,140
111,add,22.0,N,,COCCN1C(=O)CC[C@@H]2C[,COCCN1C(=O)CC[C@@H]2C[N,23,add N at position 22,flow_matching,0.3,2.0,52,140
112,add,23.0,H,,COCCN1C(=O)CC[C@@H]2C[N,COCCN1C(=O)CC[C@@H]2C[NH,24,add H at position 23,flow_matching,0.3,2.0,52,140
113,add,24.0,+,,COCCN1C(=O)CC[C@@H]2C[NH,COCCN1C(=O)CC[C@@H]2C[NH+,25,add + at position 24,flow_matching,0.3,2.0,52,140
114,add,25.0,],,COCCN1C(=O)CC[C@@H]2C[NH+,COCCN1C(=O)CC[C@@H]2C[NH+],26,add ] at position 25,flow_matching,0.3,2.0,52,140
115,add,26.0,(,,COCCN1C(=O)CC[C@@H]2C[NH+],COCCN1C(=O)CC[C@@H]2C[NH+](,27,add ( at position 26,flow_matching,0.3,2.0,52,140
116,add,27.0,C,,COCCN1C(=O)CC[C@@H]2C[NH+](,COCCN1C(=O)CC[C@@H]2C[NH+](C,28,add C at position 27,flow_matching,0.3,2.0,52,140
117,add,28.0,c,,COCCN1C(=O)CC[C@@H]2C[NH+](C,COCCN1C(=O)CC[C@@H]2C[NH+](Cc,29,add c at position 28,flow_matching,0.3,2.0,52,140
118,add,29.0,3,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3,30,add 3 at position 29,flow_matching,0.3,2.0,52,140
119,add,30.0,c,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3c,31,add c at position 30,flow_matching,0.3,2.0,52,140
120,add,31.0,c,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3c,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc,32,add c at position 31,flow_matching,0.3,2.0,52,140
121,add,32.0,(,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(,33,add ( at position 32,flow_matching,0.3,2.0,52,140
122,add,33.0,C,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C,34,add C at position 33,flow_matching,0.3,2.0,52,140
123,add,34.0,),,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C),35,add ) at position 34,flow_matching,0.3,2.0,52,140
124,add,35.0,c,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C),COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)c,36,add c at position 35,flow_matching,0.3,2.0,52,140
125,add,36.0,c,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)c,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)cc,37,add c at position 36,flow_matching,0.3,2.0,52,140
126,add,37.0,c,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)cc,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc,38,add c at position 37,flow_matching,0.3,2.0,52,140
127,add,38.0,3,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3,39,add 3 at position 38,flow_matching,0.3,2.0,52,140
128,add,39.0,C,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C,40,add C at position 39,flow_matching,0.3,2.0,52,140
129,add,40.0,),,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C),41,add ) at position 40,flow_matching,0.3,2.0,52,140
130,add,41.0,C,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C),COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)C,42,add C at position 41,flow_matching,0.3,2.0,52,140
131,add,42.0,C,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)C,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC,43,add C at position 42,flow_matching,0.3,2.0,52,140
132,add,43.0,[,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[,44,add [ at position 43,flow_matching,0.3,2.0,52,140
133,add,44.0,C,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C,45,add C at position 44,flow_matching,0.3,2.0,52,140
134,add,45.0,@,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@,46,add @ at position 45,flow_matching,0.3,2.0,52,140
135,add,46.0,@,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@,47,add @ at position 46,flow_matching,0.3,2.0,52,140
136,add,47.0,H,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@H,48,add H at position 47,flow_matching,0.3,2.0,52,140
137,add,48.0,],,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@H,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@H],49,add ] at position 48,flow_matching,0.3,2.0,52,140
138,add,49.0,2,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@H],COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@H]2,50,add 2 at position 49,flow_matching,0.3,2.0,52,140
139,add,50.0,1,,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@H]2,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@H]21,51,add 1 at position 50,flow_matching,0.3,2.0,52,140
140,add,51.0,"
",,COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@H]21,"COCCN1C(=O)CC[C@@H]2C[NH+](Cc3cc(C)ccc3C)CC[C@@H]21
",52,"add 
 at position 51",flow_matching,0.3,2.0,52,140
