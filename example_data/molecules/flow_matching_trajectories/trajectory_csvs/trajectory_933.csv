step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,58,185
1,add,0.0,2,,,2,1,add 2 at position 0,flow_matching,0.3,2.0,58,185
2,add,1.0,7,,2,27,2,add 7 at position 1,flow_matching,0.3,2.0,58,185
3,remove,1.0,7,,27,2,1,remove 7 from position 1,flow_matching,0.3,2.0,58,185
4,replace,0.0,3,2,2,3,1,replace 2 at position 0 with 3,flow_matching,0.3,2.0,58,185
5,add,1.0,H,,3,3H,2,add H at position 1,flow_matching,0.3,2.0,58,185
6,replace,1.0,I,H,3H,3I,2,replace H at position 1 with I,flow_matching,0.3,2.0,58,185
7,add,1.0,I,,3I,3II,3,add I at position 1,flow_matching,0.3,2.0,58,185
8,add,1.0,6,,3II,36II,4,add 6 at position 1,flow_matching,0.3,2.0,58,185
9,remove,2.0,I,,36II,36I,3,remove I from position 2,flow_matching,0.3,2.0,58,185
10,add,1.0,1,,36I,316I,4,add 1 at position 1,flow_matching,0.3,2.0,58,185
11,remove,1.0,1,,316I,36I,3,remove 1 from position 1,flow_matching,0.3,2.0,58,185
12,add,3.0,l,,36I,36Il,4,add l at position 3,flow_matching,0.3,2.0,58,185
13,remove,2.0,I,,36Il,36l,3,remove I from position 2,flow_matching,0.3,2.0,58,185
14,add,0.0,[,,36l,[36l,4,add [ at position 0,flow_matching,0.3,2.0,58,185
15,add,3.0,c,,[36l,[36cl,5,add c at position 3,flow_matching,0.3,2.0,58,185
16,replace,0.0,C,[,[36cl,C36cl,5,replace [ at position 0 with C,flow_matching,0.3,2.0,58,185
17,remove,3.0,c,,C36cl,C36l,4,remove c from position 3,flow_matching,0.3,2.0,58,185
18,add,4.0,4,,C36l,C36l4,5,add 4 at position 4,flow_matching,0.3,2.0,58,185
19,replace,1.0,l,3,C36l4,Cl6l4,5,replace 3 at position 1 with l,flow_matching,0.3,2.0,58,185
20,add,3.0,n,,Cl6l4,Cl6nl4,6,add n at position 3,flow_matching,0.3,2.0,58,185
21,replace,1.0,c,l,Cl6nl4,Cc6nl4,6,replace l at position 1 with c,flow_matching,0.3,2.0,58,185
22,add,6.0,B,,Cc6nl4,Cc6nl4B,7,add B at position 6,flow_matching,0.3,2.0,58,185
23,add,0.0,N,,Cc6nl4B,NCc6nl4B,8,add N at position 0,flow_matching,0.3,2.0,58,185
24,remove,4.0,n,,NCc6nl4B,NCc6l4B,7,remove n from position 4,flow_matching,0.3,2.0,58,185
25,replace,0.0,C,N,NCc6l4B,CCc6l4B,7,replace N at position 0 with C,flow_matching,0.3,2.0,58,185
26,replace,1.0,c,C,CCc6l4B,Ccc6l4B,7,replace C at position 1 with c,flow_matching,0.3,2.0,58,185
27,replace,2.0,5,c,Ccc6l4B,Cc56l4B,7,replace c at position 2 with 5,flow_matching,0.3,2.0,58,185
28,replace,2.0,1,5,Cc56l4B,Cc16l4B,7,replace 5 at position 2 with 1,flow_matching,0.3,2.0,58,185
29,replace,3.0,c,6,Cc16l4B,Cc1cl4B,7,replace 6 at position 3 with c,flow_matching,0.3,2.0,58,185
30,replace,4.0,(,l,Cc1cl4B,Cc1c(4B,7,replace l at position 4 with (,flow_matching,0.3,2.0,58,185
31,replace,1.0,/,c,Cc1c(4B,C/1c(4B,7,replace c at position 1 with /,flow_matching,0.3,2.0,58,185
32,remove,3.0,c,,C/1c(4B,C/1(4B,6,remove c from position 3,flow_matching,0.3,2.0,58,185
33,replace,0.0,=,C,C/1(4B,=/1(4B,6,replace C at position 0 with =,flow_matching,0.3,2.0,58,185
34,add,4.0,+,,=/1(4B,=/1(+4B,7,add + at position 4,flow_matching,0.3,2.0,58,185
35,remove,4.0,+,,=/1(+4B,=/1(4B,6,remove + from position 4,flow_matching,0.3,2.0,58,185
36,add,6.0,\,,=/1(4B,=/1(4B\,7,add \ at position 6,flow_matching,0.3,2.0,58,185
37,add,2.0,2,,=/1(4B\,=/21(4B\,8,add 2 at position 2,flow_matching,0.3,2.0,58,185
38,remove,5.0,4,,=/21(4B\,=/21(B\,7,remove 4 from position 5,flow_matching,0.3,2.0,58,185
39,replace,0.0,C,=,=/21(B\,C/21(B\,7,replace = at position 0 with C,flow_matching,0.3,2.0,58,185
40,replace,5.0,H,B,C/21(B\,C/21(H\,7,replace B at position 5 with H,flow_matching,0.3,2.0,58,185
41,replace,1.0,c,/,C/21(H\,Cc21(H\,7,replace / at position 1 with c,flow_matching,0.3,2.0,58,185
42,replace,2.0,1,2,Cc21(H\,Cc11(H\,7,replace 2 at position 2 with 1,flow_matching,0.3,2.0,58,185
43,replace,3.0,c,1,Cc11(H\,Cc1c(H\,7,replace 1 at position 3 with c,flow_matching,0.3,2.0,58,185
44,add,3.0,(,,Cc1c(H\,Cc1(c(H\,8,add ( at position 3,flow_matching,0.3,2.0,58,185
45,replace,3.0,c,(,Cc1(c(H\,Cc1cc(H\,8,replace ( at position 3 with c,flow_matching,0.3,2.0,58,185
46,add,6.0,-,,Cc1cc(H\,Cc1cc(-H\,9,add - at position 6,flow_matching,0.3,2.0,58,185
47,replace,4.0,(,c,Cc1cc(-H\,Cc1c((-H\,9,replace c at position 4 with (,flow_matching,0.3,2.0,58,185
48,replace,5.0,C,(,Cc1c((-H\,Cc1c(C-H\,9,replace ( at position 5 with C,flow_matching,0.3,2.0,58,185
49,add,3.0,/,,Cc1c(C-H\,Cc1/c(C-H\,10,add / at position 3,flow_matching,0.3,2.0,58,185
50,replace,3.0,c,/,Cc1/c(C-H\,Cc1cc(C-H\,10,replace / at position 3 with c,flow_matching,0.3,2.0,58,185
51,replace,3.0,H,c,Cc1cc(C-H\,Cc1Hc(C-H\,10,replace c at position 3 with H,flow_matching,0.3,2.0,58,185
52,remove,8.0,H,,Cc1Hc(C-H\,Cc1Hc(C-\,9,remove H from position 8,flow_matching,0.3,2.0,58,185
53,add,0.0,1,,Cc1Hc(C-\,1Cc1Hc(C-\,10,add 1 at position 0,flow_matching,0.3,2.0,58,185
54,replace,4.0,@,H,1Cc1Hc(C-\,1Cc1@c(C-\,10,replace H at position 4 with @,flow_matching,0.3,2.0,58,185
55,replace,0.0,C,1,1Cc1@c(C-\,CCc1@c(C-\,10,replace 1 at position 0 with C,flow_matching,0.3,2.0,58,185
56,replace,6.0,N,(,CCc1@c(C-\,CCc1@cNC-\,10,replace ( at position 6 with N,flow_matching,0.3,2.0,58,185
57,add,3.0,2,,CCc1@cNC-\,CCc21@cNC-\,11,add 2 at position 3,flow_matching,0.3,2.0,58,185
58,replace,1.0,c,C,CCc21@cNC-\,Ccc21@cNC-\,11,replace C at position 1 with c,flow_matching,0.3,2.0,58,185
59,remove,7.0,N,,Ccc21@cNC-\,Ccc21@cC-\,10,remove N from position 7,flow_matching,0.3,2.0,58,185
60,remove,8.0,-,,Ccc21@cC-\,Ccc21@cC\,9,remove - from position 8,flow_matching,0.3,2.0,58,185
61,add,6.0,3,,Ccc21@cC\,Ccc21@3cC\,10,add 3 at position 6,flow_matching,0.3,2.0,58,185
62,remove,8.0,C,,Ccc21@3cC\,Ccc21@3c\,9,remove C from position 8,flow_matching,0.3,2.0,58,185
63,replace,4.0,r,1,Ccc21@3c\,Ccc2r@3c\,9,replace 1 at position 4 with r,flow_matching,0.3,2.0,58,185
64,remove,7.0,c,,Ccc2r@3c\,Ccc2r@3\,8,remove c from position 7,flow_matching,0.3,2.0,58,185
65,add,4.0,6,,Ccc2r@3\,Ccc26r@3\,9,add 6 at position 4,flow_matching,0.3,2.0,58,185
66,remove,3.0,2,,Ccc26r@3\,Ccc6r@3\,8,remove 2 from position 3,flow_matching,0.3,2.0,58,185
67,replace,0.0,#,C,Ccc6r@3\,#cc6r@3\,8,replace C at position 0 with #,flow_matching,0.3,2.0,58,185
68,add,5.0,7,,#cc6r@3\,#cc6r7@3\,9,add 7 at position 5,flow_matching,0.3,2.0,58,185
69,replace,3.0,(,6,#cc6r7@3\,#cc(r7@3\,9,replace 6 at position 3 with (,flow_matching,0.3,2.0,58,185
70,add,5.0,S,,#cc(r7@3\,#cc(rS7@3\,10,add S at position 5,flow_matching,0.3,2.0,58,185
71,replace,7.0,4,@,#cc(rS7@3\,#cc(rS743\,10,replace @ at position 7 with 4,flow_matching,0.3,2.0,58,185
72,add,10.0,@,,#cc(rS743\,#cc(rS743\@,11,add @ at position 10,flow_matching,0.3,2.0,58,185
73,remove,5.0,S,,#cc(rS743\@,#cc(r743\@,10,remove S from position 5,flow_matching,0.3,2.0,58,185
74,replace,3.0,-,(,#cc(r743\@,#cc-r743\@,10,replace ( at position 3 with -,flow_matching,0.3,2.0,58,185
75,replace,9.0,+,@,#cc-r743\@,#cc-r743\+,10,replace @ at position 9 with +,flow_matching,0.3,2.0,58,185
76,add,1.0,],,#cc-r743\+,#]cc-r743\+,11,add ] at position 1,flow_matching,0.3,2.0,58,185
77,replace,2.0,n,c,#]cc-r743\+,#]nc-r743\+,11,replace c at position 2 with n,flow_matching,0.3,2.0,58,185
78,replace,10.0,5,+,#]nc-r743\+,#]nc-r743\5,11,replace + at position 10 with 5,flow_matching,0.3,2.0,58,185
79,remove,10.0,5,,#]nc-r743\5,#]nc-r743\,10,remove 5 from position 10,flow_matching,0.3,2.0,58,185
80,replace,0.0,C,#,#]nc-r743\,C]nc-r743\,10,replace # at position 0 with C,flow_matching,0.3,2.0,58,185
81,add,6.0,\,,C]nc-r743\,C]nc-r\743\,11,add \ at position 6,flow_matching,0.3,2.0,58,185
82,remove,7.0,7,,C]nc-r\743\,C]nc-r\43\,10,remove 7 from position 7,flow_matching,0.3,2.0,58,185
83,add,0.0,n,,C]nc-r\43\,nC]nc-r\43\,11,add n at position 0,flow_matching,0.3,2.0,58,185
84,replace,0.0,C,n,nC]nc-r\43\,CC]nc-r\43\,11,replace n at position 0 with C,flow_matching,0.3,2.0,58,185
85,remove,5.0,-,,CC]nc-r\43\,CC]ncr\43\,10,remove - from position 5,flow_matching,0.3,2.0,58,185
86,add,2.0,2,,CC]ncr\43\,CC2]ncr\43\,11,add 2 at position 2,flow_matching,0.3,2.0,58,185
87,replace,1.0,c,C,CC2]ncr\43\,Cc2]ncr\43\,11,replace C at position 1 with c,flow_matching,0.3,2.0,58,185
88,replace,2.0,1,2,Cc2]ncr\43\,Cc1]ncr\43\,11,replace 2 at position 2 with 1,flow_matching,0.3,2.0,58,185
89,replace,3.0,c,],Cc1]ncr\43\,Cc1cncr\43\,11,replace ] at position 3 with c,flow_matching,0.3,2.0,58,185
90,replace,3.0,S,c,Cc1cncr\43\,Cc1Sncr\43\,11,replace c at position 3 with S,flow_matching,0.3,2.0,58,185
91,add,8.0,r,,Cc1Sncr\43\,Cc1Sncr\r43\,12,add r at position 8,flow_matching,0.3,2.0,58,185
92,remove,2.0,1,,Cc1Sncr\r43\,CcSncr\r43\,11,remove 1 from position 2,flow_matching,0.3,2.0,58,185
93,replace,3.0,[,n,CcSncr\r43\,CcS[cr\r43\,11,replace n at position 3 with [,flow_matching,0.3,2.0,58,185
94,replace,2.0,1,S,CcS[cr\r43\,Cc1[cr\r43\,11,replace S at position 2 with 1,flow_matching,0.3,2.0,58,185
95,replace,3.0,c,[,Cc1[cr\r43\,Cc1ccr\r43\,11,replace [ at position 3 with c,flow_matching,0.3,2.0,58,185
96,remove,6.0,\,,Cc1ccr\r43\,Cc1ccrr43\,10,remove \ from position 6,flow_matching,0.3,2.0,58,185
97,replace,4.0,(,c,Cc1ccrr43\,Cc1c(rr43\,10,replace c at position 4 with (,flow_matching,0.3,2.0,58,185
98,add,9.0,1,,Cc1c(rr43\,Cc1c(rr431\,11,add 1 at position 9,flow_matching,0.3,2.0,58,185
99,add,8.0,-,,Cc1c(rr431\,Cc1c(rr4-31\,12,add - at position 8,flow_matching,0.3,2.0,58,185
100,replace,11.0,],\,Cc1c(rr4-31\,Cc1c(rr4-31],12,replace \ at position 11 with ],flow_matching,0.3,2.0,58,185
101,add,9.0,r,,Cc1c(rr4-31],Cc1c(rr4-r31],13,add r at position 9,flow_matching,0.3,2.0,58,185
102,replace,5.0,C,r,Cc1c(rr4-r31],Cc1c(Cr4-r31],13,replace r at position 5 with C,flow_matching,0.3,2.0,58,185
103,add,2.0,r,,Cc1c(Cr4-r31],Ccr1c(Cr4-r31],14,add r at position 2,flow_matching,0.3,2.0,58,185
104,remove,4.0,c,,Ccr1c(Cr4-r31],Ccr1(Cr4-r31],13,remove c from position 4,flow_matching,0.3,2.0,58,185
105,replace,2.0,1,r,Ccr1(Cr4-r31],Cc11(Cr4-r31],13,replace r at position 2 with 1,flow_matching,0.3,2.0,58,185
106,remove,4.0,(,,Cc11(Cr4-r31],Cc11Cr4-r31],12,remove ( from position 4,flow_matching,0.3,2.0,58,185
107,replace,3.0,c,1,Cc11Cr4-r31],Cc1cCr4-r31],12,replace 1 at position 3 with c,flow_matching,0.3,2.0,58,185
108,remove,1.0,c,,Cc1cCr4-r31],C1cCr4-r31],11,remove c from position 1,flow_matching,0.3,2.0,58,185
109,replace,1.0,c,1,C1cCr4-r31],CccCr4-r31],11,replace 1 at position 1 with c,flow_matching,0.3,2.0,58,185
110,add,5.0,S,,CccCr4-r31],CccCrS4-r31],12,add S at position 5,flow_matching,0.3,2.0,58,185
111,replace,6.0,r,4,CccCrS4-r31],CccCrSr-r31],12,replace 4 at position 6 with r,flow_matching,0.3,2.0,58,185
112,replace,4.0,N,r,CccCrSr-r31],CccCNSr-r31],12,replace r at position 4 with N,flow_matching,0.3,2.0,58,185
113,remove,2.0,c,,CccCNSr-r31],CcCNSr-r31],11,remove c from position 2,flow_matching,0.3,2.0,58,185
114,remove,7.0,r,,CcCNSr-r31],CcCNSr-31],10,remove r from position 7,flow_matching,0.3,2.0,58,185
115,remove,8.0,1,,CcCNSr-31],CcCNSr-3],9,remove 1 from position 8,flow_matching,0.3,2.0,58,185
116,remove,6.0,-,,CcCNSr-3],CcCNSr3],8,remove - from position 6,flow_matching,0.3,2.0,58,185
117,replace,5.0,6,r,CcCNSr3],CcCNS63],8,replace r at position 5 with 6,flow_matching,0.3,2.0,58,185
118,replace,0.0,B,C,CcCNS63],BcCNS63],8,replace C at position 0 with B,flow_matching,0.3,2.0,58,185
119,replace,0.0,C,B,BcCNS63],CcCNS63],8,replace B at position 0 with C,flow_matching,0.3,2.0,58,185
120,remove,4.0,S,,CcCNS63],CcCN63],7,remove S from position 4,flow_matching,0.3,2.0,58,185
121,remove,0.0,C,,CcCN63],cCN63],6,remove C from position 0,flow_matching,0.3,2.0,58,185
122,add,1.0,\,,cCN63],c\CN63],7,add \ at position 1,flow_matching,0.3,2.0,58,185
123,add,6.0,N,,c\CN63],c\CN63N],8,add N at position 6,flow_matching,0.3,2.0,58,185
124,remove,5.0,3,,c\CN63N],c\CN6N],7,remove 3 from position 5,flow_matching,0.3,2.0,58,185
125,add,5.0,/,,c\CN6N],c\CN6/N],8,add / at position 5,flow_matching,0.3,2.0,58,185
126,remove,0.0,c,,c\CN6/N],\CN6/N],7,remove c from position 0,flow_matching,0.3,2.0,58,185
127,replace,5.0,5,N,\CN6/N],\CN6/5],7,replace N at position 5 with 5,flow_matching,0.3,2.0,58,185
128,replace,0.0,C,\,\CN6/5],CCN6/5],7,replace \ at position 0 with C,flow_matching,0.3,2.0,58,185
129,replace,1.0,c,C,CCN6/5],CcN6/5],7,replace C at position 1 with c,flow_matching,0.3,2.0,58,185
130,replace,2.0,1,N,CcN6/5],Cc16/5],7,replace N at position 2 with 1,flow_matching,0.3,2.0,58,185
131,replace,3.0,c,6,Cc16/5],Cc1c/5],7,replace 6 at position 3 with c,flow_matching,0.3,2.0,58,185
132,replace,4.0,(,/,Cc1c/5],Cc1c(5],7,replace / at position 4 with (,flow_matching,0.3,2.0,58,185
133,replace,5.0,C,5,Cc1c(5],Cc1c(C],7,replace 5 at position 5 with C,flow_matching,0.3,2.0,58,185
134,replace,6.0,),],Cc1c(C],Cc1c(C),7,replace ] at position 6 with ),flow_matching,0.3,2.0,58,185
135,add,7.0,n,,Cc1c(C),Cc1c(C)n,8,add n at position 7,flow_matching,0.3,2.0,58,185
136,add,8.0,(,,Cc1c(C)n,Cc1c(C)n(,9,add ( at position 8,flow_matching,0.3,2.0,58,185
137,add,9.0,-,,Cc1c(C)n(,Cc1c(C)n(-,10,add - at position 9,flow_matching,0.3,2.0,58,185
138,add,10.0,c,,Cc1c(C)n(-,Cc1c(C)n(-c,11,add c at position 10,flow_matching,0.3,2.0,58,185
139,add,11.0,2,,Cc1c(C)n(-c,Cc1c(C)n(-c2,12,add 2 at position 11,flow_matching,0.3,2.0,58,185
140,add,12.0,c,,Cc1c(C)n(-c2,Cc1c(C)n(-c2c,13,add c at position 12,flow_matching,0.3,2.0,58,185
141,add,13.0,c,,Cc1c(C)n(-c2c,Cc1c(C)n(-c2cc,14,add c at position 13,flow_matching,0.3,2.0,58,185
142,add,14.0,c,,Cc1c(C)n(-c2cc,Cc1c(C)n(-c2ccc,15,add c at position 14,flow_matching,0.3,2.0,58,185
143,add,15.0,c,,Cc1c(C)n(-c2ccc,Cc1c(C)n(-c2cccc,16,add c at position 15,flow_matching,0.3,2.0,58,185
144,add,16.0,c,,Cc1c(C)n(-c2cccc,Cc1c(C)n(-c2ccccc,17,add c at position 16,flow_matching,0.3,2.0,58,185
145,add,17.0,2,,Cc1c(C)n(-c2ccccc,Cc1c(C)n(-c2ccccc2,18,add 2 at position 17,flow_matching,0.3,2.0,58,185
146,add,18.0,),,Cc1c(C)n(-c2ccccc2,Cc1c(C)n(-c2ccccc2),19,add ) at position 18,flow_matching,0.3,2.0,58,185
147,add,19.0,c,,Cc1c(C)n(-c2ccccc2),Cc1c(C)n(-c2ccccc2)c,20,add c at position 19,flow_matching,0.3,2.0,58,185
148,add,20.0,2,,Cc1c(C)n(-c2ccccc2)c,Cc1c(C)n(-c2ccccc2)c2,21,add 2 at position 20,flow_matching,0.3,2.0,58,185
149,add,21.0,n,,Cc1c(C)n(-c2ccccc2)c2,Cc1c(C)n(-c2ccccc2)c2n,22,add n at position 21,flow_matching,0.3,2.0,58,185
150,add,22.0,c,,Cc1c(C)n(-c2ccccc2)c2n,Cc1c(C)n(-c2ccccc2)c2nc,23,add c at position 22,flow_matching,0.3,2.0,58,185
151,add,23.0,(,,Cc1c(C)n(-c2ccccc2)c2nc,Cc1c(C)n(-c2ccccc2)c2nc(,24,add ( at position 23,flow_matching,0.3,2.0,58,185
152,add,24.0,C,,Cc1c(C)n(-c2ccccc2)c2nc(,Cc1c(C)n(-c2ccccc2)c2nc(C,25,add C at position 24,flow_matching,0.3,2.0,58,185
153,add,25.0,(,,Cc1c(C)n(-c2ccccc2)c2nc(C,Cc1c(C)n(-c2ccccc2)c2nc(C(,26,add ( at position 25,flow_matching,0.3,2.0,58,185
154,add,26.0,=,,Cc1c(C)n(-c2ccccc2)c2nc(C(,Cc1c(C)n(-c2ccccc2)c2nc(C(=,27,add = at position 26,flow_matching,0.3,2.0,58,185
155,add,27.0,O,,Cc1c(C)n(-c2ccccc2)c2nc(C(=,Cc1c(C)n(-c2ccccc2)c2nc(C(=O,28,add O at position 27,flow_matching,0.3,2.0,58,185
156,add,28.0,),,Cc1c(C)n(-c2ccccc2)c2nc(C(=O,Cc1c(C)n(-c2ccccc2)c2nc(C(=O),29,add ) at position 28,flow_matching,0.3,2.0,58,185
157,add,29.0,N,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O),Cc1c(C)n(-c2ccccc2)c2nc(C(=O)N,30,add N at position 29,flow_matching,0.3,2.0,58,185
158,add,30.0,c,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)N,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc,31,add c at position 30,flow_matching,0.3,2.0,58,185
159,add,31.0,3,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3,32,add 3 at position 31,flow_matching,0.3,2.0,58,185
160,add,32.0,c,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3c,33,add c at position 32,flow_matching,0.3,2.0,58,185
161,add,33.0,c,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3c,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3cc,34,add c at position 33,flow_matching,0.3,2.0,58,185
162,add,34.0,c,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3cc,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc,35,add c at position 34,flow_matching,0.3,2.0,58,185
163,add,35.0,(,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(,36,add ( at position 35,flow_matching,0.3,2.0,58,185
164,add,36.0,F,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F,37,add F at position 36,flow_matching,0.3,2.0,58,185
165,add,37.0,),,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F),38,add ) at position 37,flow_matching,0.3,2.0,58,185
166,add,38.0,c,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F),Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)c,39,add c at position 38,flow_matching,0.3,2.0,58,185
167,add,39.0,c,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)c,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc,40,add c at position 39,flow_matching,0.3,2.0,58,185
168,add,40.0,3,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3,41,add 3 at position 40,flow_matching,0.3,2.0,58,185
169,add,41.0,),,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3),42,add ) at position 41,flow_matching,0.3,2.0,58,185
170,add,42.0,n,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3),Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)n,43,add n at position 42,flow_matching,0.3,2.0,58,185
171,add,43.0,c,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)n,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc,44,add c at position 43,flow_matching,0.3,2.0,58,185
172,add,44.0,(,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(,45,add ( at position 44,flow_matching,0.3,2.0,58,185
173,add,45.0,N,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N,46,add N at position 45,flow_matching,0.3,2.0,58,185
174,add,46.0,3,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3,47,add 3 at position 46,flow_matching,0.3,2.0,58,185
175,add,47.0,C,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3C,48,add C at position 47,flow_matching,0.3,2.0,58,185
176,add,48.0,C,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3C,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CC,49,add C at position 48,flow_matching,0.3,2.0,58,185
177,add,49.0,C,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CC,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCC,50,add C at position 49,flow_matching,0.3,2.0,58,185
178,add,50.0,C,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCC,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCC,51,add C at position 50,flow_matching,0.3,2.0,58,185
179,add,51.0,C,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCC,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC,52,add C at position 51,flow_matching,0.3,2.0,58,185
180,add,52.0,3,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3,53,add 3 at position 52,flow_matching,0.3,2.0,58,185
181,add,53.0,),,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3),54,add ) at position 53,flow_matching,0.3,2.0,58,185
182,add,54.0,c,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3),Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3)c,55,add c at position 54,flow_matching,0.3,2.0,58,185
183,add,55.0,1,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3)c,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3)c1,56,add 1 at position 55,flow_matching,0.3,2.0,58,185
184,add,56.0,2,,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3)c1,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3)c12,57,add 2 at position 56,flow_matching,0.3,2.0,58,185
185,add,57.0,"
",,Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3)c12,"Cc1c(C)n(-c2ccccc2)c2nc(C(=O)Nc3ccc(F)cc3)nc(N3CCCCC3)c12
",58,"add 
 at position 57",flow_matching,0.3,2.0,58,185
