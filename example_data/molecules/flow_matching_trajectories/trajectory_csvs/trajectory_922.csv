step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,140
1,add,0.0,=,,,=,1,add = at position 0,flow_matching,0.3,2.0,49,140
2,add,0.0,[,,=,[=,2,add [ at position 0,flow_matching,0.3,2.0,49,140
3,replace,0.0,O,[,[=,O=,2,replace [ at position 0 with O,flow_matching,0.3,2.0,49,140
4,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,49,140
5,remove,0.0,=,,=,,0,remove = from position 0,flow_matching,0.3,2.0,49,140
6,add,0.0,[,,,[,1,add [ at position 0,flow_matching,0.3,2.0,49,140
7,add,1.0,),,[,[),2,add ) at position 1,flow_matching,0.3,2.0,49,140
8,replace,1.0,o,),[),[o,2,replace ) at position 1 with o,flow_matching,0.3,2.0,49,140
9,add,2.0,),,[o,[o),3,add ) at position 2,flow_matching,0.3,2.0,49,140
10,replace,0.0,O,[,[o),Oo),3,replace [ at position 0 with O,flow_matching,0.3,2.0,49,140
11,add,2.0,7,,Oo),Oo7),4,add 7 at position 2,flow_matching,0.3,2.0,49,140
12,replace,0.0,[,O,Oo7),[o7),4,replace O at position 0 with [,flow_matching,0.3,2.0,49,140
13,add,4.0,2,,[o7),[o7)2,5,add 2 at position 4,flow_matching,0.3,2.0,49,140
14,add,2.0,l,,[o7)2,[ol7)2,6,add l at position 2,flow_matching,0.3,2.0,49,140
15,replace,0.0,O,[,[ol7)2,Ool7)2,6,replace [ at position 0 with O,flow_matching,0.3,2.0,49,140
16,add,0.0,r,,Ool7)2,rOol7)2,7,add r at position 0,flow_matching,0.3,2.0,49,140
17,remove,4.0,7,,rOol7)2,rOol)2,6,remove 7 from position 4,flow_matching,0.3,2.0,49,140
18,replace,0.0,O,r,rOol)2,OOol)2,6,replace r at position 0 with O,flow_matching,0.3,2.0,49,140
19,replace,1.0,r,O,OOol)2,Orol)2,6,replace O at position 1 with r,flow_matching,0.3,2.0,49,140
20,add,4.0,\,,Orol)2,Orol\)2,7,add \ at position 4,flow_matching,0.3,2.0,49,140
21,replace,1.0,=,r,Orol\)2,O=ol\)2,7,replace r at position 1 with =,flow_matching,0.3,2.0,49,140
22,replace,2.0,C,o,O=ol\)2,O=Cl\)2,7,replace o at position 2 with C,flow_matching,0.3,2.0,49,140
23,replace,3.0,(,l,O=Cl\)2,O=C(\)2,7,replace l at position 3 with (,flow_matching,0.3,2.0,49,140
24,replace,4.0,N,\,O=C(\)2,O=C(N)2,7,replace \ at position 4 with N,flow_matching,0.3,2.0,49,140
25,remove,6.0,2,,O=C(N)2,O=C(N),6,remove 2 from position 6,flow_matching,0.3,2.0,49,140
26,replace,4.0,/,N,O=C(N),O=C(/),6,replace N at position 4 with /,flow_matching,0.3,2.0,49,140
27,add,6.0,r,,O=C(/),O=C(/)r,7,add r at position 6,flow_matching,0.3,2.0,49,140
28,remove,1.0,=,,O=C(/)r,OC(/)r,6,remove = from position 1,flow_matching,0.3,2.0,49,140
29,replace,1.0,=,C,OC(/)r,O=(/)r,6,replace C at position 1 with =,flow_matching,0.3,2.0,49,140
30,replace,2.0,C,(,O=(/)r,O=C/)r,6,replace ( at position 2 with C,flow_matching,0.3,2.0,49,140
31,add,6.0,r,,O=C/)r,O=C/)rr,7,add r at position 6,flow_matching,0.3,2.0,49,140
32,add,5.0,-,,O=C/)rr,O=C/)-rr,8,add - at position 5,flow_matching,0.3,2.0,49,140
33,remove,7.0,r,,O=C/)-rr,O=C/)-r,7,remove r from position 7,flow_matching,0.3,2.0,49,140
34,remove,5.0,-,,O=C/)-r,O=C/)r,6,remove - from position 5,flow_matching,0.3,2.0,49,140
35,replace,1.0,n,=,O=C/)r,OnC/)r,6,replace = at position 1 with n,flow_matching,0.3,2.0,49,140
36,replace,1.0,=,n,OnC/)r,O=C/)r,6,replace n at position 1 with =,flow_matching,0.3,2.0,49,140
37,remove,1.0,=,,O=C/)r,OC/)r,5,remove = from position 1,flow_matching,0.3,2.0,49,140
38,replace,1.0,=,C,OC/)r,O=/)r,5,replace C at position 1 with =,flow_matching,0.3,2.0,49,140
39,remove,3.0,),,O=/)r,O=/r,4,remove ) from position 3,flow_matching,0.3,2.0,49,140
40,replace,2.0,C,/,O=/r,O=Cr,4,replace / at position 2 with C,flow_matching,0.3,2.0,49,140
41,replace,2.0,H,C,O=Cr,O=Hr,4,replace C at position 2 with H,flow_matching,0.3,2.0,49,140
42,remove,2.0,H,,O=Hr,O=r,3,remove H from position 2,flow_matching,0.3,2.0,49,140
43,remove,0.0,O,,O=r,=r,2,remove O from position 0,flow_matching,0.3,2.0,49,140
44,replace,0.0,@,=,=r,@r,2,replace = at position 0 with @,flow_matching,0.3,2.0,49,140
45,add,1.0,),,@r,@)r,3,add ) at position 1,flow_matching,0.3,2.0,49,140
46,remove,2.0,r,,@)r,@),2,remove r from position 2,flow_matching,0.3,2.0,49,140
47,add,1.0,F,,@),@F),3,add F at position 1,flow_matching,0.3,2.0,49,140
48,add,2.0,+,,@F),@F+),4,add + at position 2,flow_matching,0.3,2.0,49,140
49,replace,2.0,n,+,@F+),@Fn),4,replace + at position 2 with n,flow_matching,0.3,2.0,49,140
50,replace,0.0,O,@,@Fn),OFn),4,replace @ at position 0 with O,flow_matching,0.3,2.0,49,140
51,replace,1.0,=,F,OFn),O=n),4,replace F at position 1 with =,flow_matching,0.3,2.0,49,140
52,replace,2.0,c,n,O=n),O=c),4,replace n at position 2 with c,flow_matching,0.3,2.0,49,140
53,remove,0.0,O,,O=c),=c),3,remove O from position 0,flow_matching,0.3,2.0,49,140
54,remove,2.0,),,=c),=c,2,remove ) from position 2,flow_matching,0.3,2.0,49,140
55,remove,0.0,=,,=c,c,1,remove = from position 0,flow_matching,0.3,2.0,49,140
56,remove,0.0,c,,c,,0,remove c from position 0,flow_matching,0.3,2.0,49,140
57,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,49,140
58,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,49,140
59,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,49,140
60,remove,0.0,c,,c,,0,remove c from position 0,flow_matching,0.3,2.0,49,140
61,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,49,140
62,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,49,140
63,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,49,140
64,add,1.0,@,,/,/@,2,add @ at position 1,flow_matching,0.3,2.0,49,140
65,replace,0.0,O,/,/@,O@,2,replace / at position 0 with O,flow_matching,0.3,2.0,49,140
66,replace,1.0,3,@,O@,O3,2,replace @ at position 1 with 3,flow_matching,0.3,2.0,49,140
67,replace,1.0,B,3,O3,OB,2,replace 3 at position 1 with B,flow_matching,0.3,2.0,49,140
68,add,1.0,n,,OB,OnB,3,add n at position 1,flow_matching,0.3,2.0,49,140
69,replace,1.0,r,n,OnB,OrB,3,replace n at position 1 with r,flow_matching,0.3,2.0,49,140
70,remove,0.0,O,,OrB,rB,2,remove O from position 0,flow_matching,0.3,2.0,49,140
71,remove,1.0,B,,rB,r,1,remove B from position 1,flow_matching,0.3,2.0,49,140
72,remove,0.0,r,,r,,0,remove r from position 0,flow_matching,0.3,2.0,49,140
73,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,49,140
74,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,49,140
75,add,2.0,C,,O=,O=C,3,add C at position 2,flow_matching,0.3,2.0,49,140
76,add,3.0,(,,O=C,O=C(,4,add ( at position 3,flow_matching,0.3,2.0,49,140
77,add,4.0,N,,O=C(,O=C(N,5,add N at position 4,flow_matching,0.3,2.0,49,140
78,add,3.0,@,,O=C(N,O=C@(N,6,add @ at position 3,flow_matching,0.3,2.0,49,140
79,replace,2.0,=,C,O=C@(N,O==@(N,6,replace C at position 2 with =,flow_matching,0.3,2.0,49,140
80,remove,4.0,(,,O==@(N,O==@N,5,remove ( from position 4,flow_matching,0.3,2.0,49,140
81,replace,2.0,C,=,O==@N,O=C@N,5,replace = at position 2 with C,flow_matching,0.3,2.0,49,140
82,replace,0.0,[,O,O=C@N,[=C@N,5,replace O at position 0 with [,flow_matching,0.3,2.0,49,140
83,replace,0.0,O,[,[=C@N,O=C@N,5,replace [ at position 0 with O,flow_matching,0.3,2.0,49,140
84,add,0.0,n,,O=C@N,nO=C@N,6,add n at position 0,flow_matching,0.3,2.0,49,140
85,remove,1.0,O,,nO=C@N,n=C@N,5,remove O from position 1,flow_matching,0.3,2.0,49,140
86,replace,0.0,O,n,n=C@N,O=C@N,5,replace n at position 0 with O,flow_matching,0.3,2.0,49,140
87,add,4.0,s,,O=C@N,O=C@sN,6,add s at position 4,flow_matching,0.3,2.0,49,140
88,add,2.0,-,,O=C@sN,O=-C@sN,7,add - at position 2,flow_matching,0.3,2.0,49,140
89,add,5.0,n,,O=-C@sN,O=-C@nsN,8,add n at position 5,flow_matching,0.3,2.0,49,140
90,replace,2.0,C,-,O=-C@nsN,O=CC@nsN,8,replace - at position 2 with C,flow_matching,0.3,2.0,49,140
91,replace,3.0,(,C,O=CC@nsN,O=C(@nsN,8,replace C at position 3 with (,flow_matching,0.3,2.0,49,140
92,replace,4.0,N,@,O=C(@nsN,O=C(NnsN,8,replace @ at position 4 with N,flow_matching,0.3,2.0,49,140
93,replace,5.0,c,n,O=C(NnsN,O=C(NcsN,8,replace n at position 5 with c,flow_matching,0.3,2.0,49,140
94,replace,6.0,1,s,O=C(NcsN,O=C(Nc1N,8,replace s at position 6 with 1,flow_matching,0.3,2.0,49,140
95,add,8.0,O,,O=C(Nc1N,O=C(Nc1NO,9,add O at position 8,flow_matching,0.3,2.0,49,140
96,add,8.0,H,,O=C(Nc1NO,O=C(Nc1NHO,10,add H at position 8,flow_matching,0.3,2.0,49,140
97,add,6.0,r,,O=C(Nc1NHO,O=C(Ncr1NHO,11,add r at position 6,flow_matching,0.3,2.0,49,140
98,replace,6.0,1,r,O=C(Ncr1NHO,O=C(Nc11NHO,11,replace r at position 6 with 1,flow_matching,0.3,2.0,49,140
99,replace,7.0,c,1,O=C(Nc11NHO,O=C(Nc1cNHO,11,replace 1 at position 7 with c,flow_matching,0.3,2.0,49,140
100,replace,8.0,c,N,O=C(Nc1cNHO,O=C(Nc1ccHO,11,replace N at position 8 with c,flow_matching,0.3,2.0,49,140
101,replace,9.0,c,H,O=C(Nc1ccHO,O=C(Nc1cccO,11,replace H at position 9 with c,flow_matching,0.3,2.0,49,140
102,replace,10.0,c,O,O=C(Nc1cccO,O=C(Nc1cccc,11,replace O at position 10 with c,flow_matching,0.3,2.0,49,140
103,add,11.0,(,,O=C(Nc1cccc,O=C(Nc1cccc(,12,add ( at position 11,flow_matching,0.3,2.0,49,140
104,add,12.0,N,,O=C(Nc1cccc(,O=C(Nc1cccc(N,13,add N at position 12,flow_matching,0.3,2.0,49,140
105,add,13.0,2,,O=C(Nc1cccc(N,O=C(Nc1cccc(N2,14,add 2 at position 13,flow_matching,0.3,2.0,49,140
106,add,14.0,C,,O=C(Nc1cccc(N2,O=C(Nc1cccc(N2C,15,add C at position 14,flow_matching,0.3,2.0,49,140
107,add,15.0,C,,O=C(Nc1cccc(N2C,O=C(Nc1cccc(N2CC,16,add C at position 15,flow_matching,0.3,2.0,49,140
108,add,16.0,C,,O=C(Nc1cccc(N2CC,O=C(Nc1cccc(N2CCC,17,add C at position 16,flow_matching,0.3,2.0,49,140
109,add,17.0,N,,O=C(Nc1cccc(N2CCC,O=C(Nc1cccc(N2CCCN,18,add N at position 17,flow_matching,0.3,2.0,49,140
110,add,18.0,C,,O=C(Nc1cccc(N2CCCN,O=C(Nc1cccc(N2CCCNC,19,add C at position 18,flow_matching,0.3,2.0,49,140
111,add,19.0,2,,O=C(Nc1cccc(N2CCCNC,O=C(Nc1cccc(N2CCCNC2,20,add 2 at position 19,flow_matching,0.3,2.0,49,140
112,add,20.0,=,,O=C(Nc1cccc(N2CCCNC2,O=C(Nc1cccc(N2CCCNC2=,21,add = at position 20,flow_matching,0.3,2.0,49,140
113,add,21.0,O,,O=C(Nc1cccc(N2CCCNC2=,O=C(Nc1cccc(N2CCCNC2=O,22,add O at position 21,flow_matching,0.3,2.0,49,140
114,add,22.0,),,O=C(Nc1cccc(N2CCCNC2=O,O=C(Nc1cccc(N2CCCNC2=O),23,add ) at position 22,flow_matching,0.3,2.0,49,140
115,add,23.0,c,,O=C(Nc1cccc(N2CCCNC2=O),O=C(Nc1cccc(N2CCCNC2=O)c,24,add c at position 23,flow_matching,0.3,2.0,49,140
116,add,24.0,1,,O=C(Nc1cccc(N2CCCNC2=O)c,O=C(Nc1cccc(N2CCCNC2=O)c1,25,add 1 at position 24,flow_matching,0.3,2.0,49,140
117,add,25.0,),,O=C(Nc1cccc(N2CCCNC2=O)c1,O=C(Nc1cccc(N2CCCNC2=O)c1),26,add ) at position 25,flow_matching,0.3,2.0,49,140
118,add,26.0,C,,O=C(Nc1cccc(N2CCCNC2=O)c1),O=C(Nc1cccc(N2CCCNC2=O)c1)C,27,add C at position 26,flow_matching,0.3,2.0,49,140
119,add,27.0,(,,O=C(Nc1cccc(N2CCCNC2=O)c1)C,O=C(Nc1cccc(N2CCCNC2=O)c1)C(,28,add ( at position 27,flow_matching,0.3,2.0,49,140
120,add,28.0,=,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=,29,add = at position 28,flow_matching,0.3,2.0,49,140
121,add,29.0,O,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O,30,add O at position 29,flow_matching,0.3,2.0,49,140
122,add,30.0,),,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O),31,add ) at position 30,flow_matching,0.3,2.0,49,140
123,add,31.0,N,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O),O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N,32,add N at position 31,flow_matching,0.3,2.0,49,140
124,add,32.0,1,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1,33,add 1 at position 32,flow_matching,0.3,2.0,49,140
125,add,33.0,C,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1C,34,add C at position 33,flow_matching,0.3,2.0,49,140
126,add,34.0,C,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1C,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CC,35,add C at position 34,flow_matching,0.3,2.0,49,140
127,add,35.0,c,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CC,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc,36,add c at position 35,flow_matching,0.3,2.0,49,140
128,add,36.0,2,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2,37,add 2 at position 36,flow_matching,0.3,2.0,49,140
129,add,37.0,c,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2c,38,add c at position 37,flow_matching,0.3,2.0,49,140
130,add,38.0,c,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2c,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc,39,add c at position 38,flow_matching,0.3,2.0,49,140
131,add,39.0,(,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(,40,add ( at position 39,flow_matching,0.3,2.0,49,140
132,add,40.0,F,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F,41,add F at position 40,flow_matching,0.3,2.0,49,140
133,add,41.0,),,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F),42,add ) at position 41,flow_matching,0.3,2.0,49,140
134,add,42.0,c,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F),O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)c,43,add c at position 42,flow_matching,0.3,2.0,49,140
135,add,43.0,c,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)c,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)cc,44,add c at position 43,flow_matching,0.3,2.0,49,140
136,add,44.0,c,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)cc,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)ccc,45,add c at position 44,flow_matching,0.3,2.0,49,140
137,add,45.0,2,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)ccc,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)ccc2,46,add 2 at position 45,flow_matching,0.3,2.0,49,140
138,add,46.0,C,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)ccc2,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)ccc2C,47,add C at position 46,flow_matching,0.3,2.0,49,140
139,add,47.0,1,,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)ccc2C,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)ccc2C1,48,add 1 at position 47,flow_matching,0.3,2.0,49,140
140,add,48.0,"
",,O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)ccc2C1,"O=C(Nc1cccc(N2CCCNC2=O)c1)C(=O)N1CCc2cc(F)ccc2C1
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,140
