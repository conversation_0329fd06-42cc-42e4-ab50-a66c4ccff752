step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,27,127
1,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,27,127
2,replace,0.0,C,/,/,C,1,replace / at position 0 with C,flow_matching,0.3,2.0,27,127
3,add,1.0,l,,C,Cl,2,add l at position 1,flow_matching,0.3,2.0,27,127
4,remove,0.0,C,,Cl,l,1,remove C from position 0,flow_matching,0.3,2.0,27,127
5,add,0.0,2,,l,2l,2,add 2 at position 0,flow_matching,0.3,2.0,27,127
6,add,0.0,l,,2l,l2l,3,add l at position 0,flow_matching,0.3,2.0,27,127
7,replace,0.0,C,l,l2l,C2l,3,replace l at position 0 with C,flow_matching,0.3,2.0,27,127
8,replace,2.0,[,l,C2l,C2[,3,replace l at position 2 with [,flow_matching,0.3,2.0,27,127
9,replace,1.0,l,2,C2[,Cl[,3,replace 2 at position 1 with l,flow_matching,0.3,2.0,27,127
10,replace,2.0,C,[,Cl[,ClC,3,replace [ at position 2 with C,flow_matching,0.3,2.0,27,127
11,add,3.0,C,,ClC,ClCC,4,add C at position 3,flow_matching,0.3,2.0,27,127
12,add,4.0,c,,ClCC,ClCCc,5,add c at position 4,flow_matching,0.3,2.0,27,127
13,replace,1.0,4,l,ClCCc,C4CCc,5,replace l at position 1 with 4,flow_matching,0.3,2.0,27,127
14,replace,4.0,s,c,C4CCc,C4CCs,5,replace c at position 4 with s,flow_matching,0.3,2.0,27,127
15,replace,1.0,l,4,C4CCs,ClCCs,5,replace 4 at position 1 with l,flow_matching,0.3,2.0,27,127
16,remove,2.0,C,,ClCCs,ClCs,4,remove C from position 2,flow_matching,0.3,2.0,27,127
17,replace,3.0,C,s,ClCs,ClCC,4,replace s at position 3 with C,flow_matching,0.3,2.0,27,127
18,remove,2.0,C,,ClCC,ClC,3,remove C from position 2,flow_matching,0.3,2.0,27,127
19,replace,1.0,#,l,ClC,C#C,3,replace l at position 1 with #,flow_matching,0.3,2.0,27,127
20,remove,0.0,C,,C#C,#C,2,remove C from position 0,flow_matching,0.3,2.0,27,127
21,add,2.0,C,,#C,#CC,3,add C at position 2,flow_matching,0.3,2.0,27,127
22,add,2.0,s,,#CC,#CsC,4,add s at position 2,flow_matching,0.3,2.0,27,127
23,replace,0.0,C,#,#CsC,CCsC,4,replace # at position 0 with C,flow_matching,0.3,2.0,27,127
24,add,1.0,6,,CCsC,C6CsC,5,add 6 at position 1,flow_matching,0.3,2.0,27,127
25,replace,0.0,H,C,C6CsC,H6CsC,5,replace C at position 0 with H,flow_matching,0.3,2.0,27,127
26,replace,3.0,],s,H6CsC,H6C]C,5,replace s at position 3 with ],flow_matching,0.3,2.0,27,127
27,remove,4.0,C,,H6C]C,H6C],4,remove C from position 4,flow_matching,0.3,2.0,27,127
28,replace,1.0,O,6,H6C],HOC],4,replace 6 at position 1 with O,flow_matching,0.3,2.0,27,127
29,add,4.0,4,,HOC],HOC]4,5,add 4 at position 4,flow_matching,0.3,2.0,27,127
30,replace,0.0,C,H,HOC]4,COC]4,5,replace H at position 0 with C,flow_matching,0.3,2.0,27,127
31,add,1.0,H,,COC]4,CHOC]4,6,add H at position 1,flow_matching,0.3,2.0,27,127
32,add,2.0,@,,CHOC]4,CH@OC]4,7,add @ at position 2,flow_matching,0.3,2.0,27,127
33,replace,1.0,l,H,CH@OC]4,Cl@OC]4,7,replace H at position 1 with l,flow_matching,0.3,2.0,27,127
34,remove,1.0,l,,Cl@OC]4,C@OC]4,6,remove l from position 1,flow_matching,0.3,2.0,27,127
35,add,0.0,S,,C@OC]4,SC@OC]4,7,add S at position 0,flow_matching,0.3,2.0,27,127
36,replace,0.0,C,S,SC@OC]4,CC@OC]4,7,replace S at position 0 with C,flow_matching,0.3,2.0,27,127
37,add,2.0,n,,CC@OC]4,CCn@OC]4,8,add n at position 2,flow_matching,0.3,2.0,27,127
38,add,4.0,#,,CCn@OC]4,CCn@#OC]4,9,add # at position 4,flow_matching,0.3,2.0,27,127
39,add,7.0,C,,CCn@#OC]4,CCn@#OCC]4,10,add C at position 7,flow_matching,0.3,2.0,27,127
40,remove,7.0,C,,CCn@#OCC]4,CCn@#OC]4,9,remove C from position 7,flow_matching,0.3,2.0,27,127
41,replace,1.0,l,C,CCn@#OC]4,Cln@#OC]4,9,replace C at position 1 with l,flow_matching,0.3,2.0,27,127
42,replace,2.0,=,n,Cln@#OC]4,Cl=@#OC]4,9,replace n at position 2 with =,flow_matching,0.3,2.0,27,127
43,replace,6.0,2,C,Cl=@#OC]4,Cl=@#O2]4,9,replace C at position 6 with 2,flow_matching,0.3,2.0,27,127
44,add,3.0,#,,Cl=@#O2]4,Cl=#@#O2]4,10,add # at position 3,flow_matching,0.3,2.0,27,127
45,remove,6.0,O,,Cl=#@#O2]4,Cl=#@#2]4,9,remove O from position 6,flow_matching,0.3,2.0,27,127
46,add,0.0,3,,Cl=#@#2]4,3Cl=#@#2]4,10,add 3 at position 0,flow_matching,0.3,2.0,27,127
47,replace,5.0,5,@,3Cl=#@#2]4,3Cl=#5#2]4,10,replace @ at position 5 with 5,flow_matching,0.3,2.0,27,127
48,replace,0.0,6,3,3Cl=#5#2]4,6Cl=#5#2]4,10,replace 3 at position 0 with 6,flow_matching,0.3,2.0,27,127
49,remove,2.0,l,,6Cl=#5#2]4,6C=#5#2]4,9,remove l from position 2,flow_matching,0.3,2.0,27,127
50,replace,0.0,C,6,6C=#5#2]4,CC=#5#2]4,9,replace 6 at position 0 with C,flow_matching,0.3,2.0,27,127
51,replace,3.0,-,#,CC=#5#2]4,CC=-5#2]4,9,replace # at position 3 with -,flow_matching,0.3,2.0,27,127
52,replace,1.0,l,C,CC=-5#2]4,Cl=-5#2]4,9,replace C at position 1 with l,flow_matching,0.3,2.0,27,127
53,add,0.0,c,,Cl=-5#2]4,cCl=-5#2]4,10,add c at position 0,flow_matching,0.3,2.0,27,127
54,add,5.0,[,,cCl=-5#2]4,cCl=-[5#2]4,11,add [ at position 5,flow_matching,0.3,2.0,27,127
55,replace,0.0,C,c,cCl=-[5#2]4,CCl=-[5#2]4,11,replace c at position 0 with C,flow_matching,0.3,2.0,27,127
56,add,9.0,-,,CCl=-[5#2]4,CCl=-[5#2-]4,12,add - at position 9,flow_matching,0.3,2.0,27,127
57,add,12.0,-,,CCl=-[5#2-]4,CCl=-[5#2-]4-,13,add - at position 12,flow_matching,0.3,2.0,27,127
58,remove,1.0,C,,CCl=-[5#2-]4-,Cl=-[5#2-]4-,12,remove C from position 1,flow_matching,0.3,2.0,27,127
59,replace,11.0,S,-,Cl=-[5#2-]4-,Cl=-[5#2-]4S,12,replace - at position 11 with S,flow_matching,0.3,2.0,27,127
60,replace,6.0,I,#,Cl=-[5#2-]4S,Cl=-[5I2-]4S,12,replace # at position 6 with I,flow_matching,0.3,2.0,27,127
61,replace,2.0,C,=,Cl=-[5I2-]4S,ClC-[5I2-]4S,12,replace = at position 2 with C,flow_matching,0.3,2.0,27,127
62,replace,3.0,C,-,ClC-[5I2-]4S,ClCC[5I2-]4S,12,replace - at position 3 with C,flow_matching,0.3,2.0,27,127
63,remove,3.0,C,,ClCC[5I2-]4S,ClC[5I2-]4S,11,remove C from position 3,flow_matching,0.3,2.0,27,127
64,replace,2.0,(,C,ClC[5I2-]4S,Cl([5I2-]4S,11,replace C at position 2 with (,flow_matching,0.3,2.0,27,127
65,remove,9.0,4,,Cl([5I2-]4S,Cl([5I2-]S,10,remove 4 from position 9,flow_matching,0.3,2.0,27,127
66,replace,2.0,C,(,Cl([5I2-]S,ClC[5I2-]S,10,replace ( at position 2 with C,flow_matching,0.3,2.0,27,127
67,replace,6.0,\,2,ClC[5I2-]S,ClC[5I\-]S,10,replace 2 at position 6 with \,flow_matching,0.3,2.0,27,127
68,replace,3.0,C,[,ClC[5I\-]S,ClCC5I\-]S,10,replace [ at position 3 with C,flow_matching,0.3,2.0,27,127
69,replace,4.0,c,5,ClCC5I\-]S,ClCCcI\-]S,10,replace 5 at position 4 with c,flow_matching,0.3,2.0,27,127
70,add,1.0,3,,ClCCcI\-]S,C3lCCcI\-]S,11,add 3 at position 1,flow_matching,0.3,2.0,27,127
71,remove,7.0,\,,C3lCCcI\-]S,C3lCCcI-]S,10,remove \ from position 7,flow_matching,0.3,2.0,27,127
72,add,6.0,5,,C3lCCcI-]S,C3lCCc5I-]S,11,add 5 at position 6,flow_matching,0.3,2.0,27,127
73,replace,1.0,l,3,C3lCCc5I-]S,CllCCc5I-]S,11,replace 3 at position 1 with l,flow_matching,0.3,2.0,27,127
74,replace,9.0,2,],CllCCc5I-]S,CllCCc5I-2S,11,replace ] at position 9 with 2,flow_matching,0.3,2.0,27,127
75,remove,9.0,2,,CllCCc5I-2S,CllCCc5I-S,10,remove 2 from position 9,flow_matching,0.3,2.0,27,127
76,add,10.0,3,,CllCCc5I-S,CllCCc5I-S3,11,add 3 at position 10,flow_matching,0.3,2.0,27,127
77,remove,3.0,C,,CllCCc5I-S3,CllCc5I-S3,10,remove C from position 3,flow_matching,0.3,2.0,27,127
78,remove,9.0,3,,CllCc5I-S3,CllCc5I-S,9,remove 3 from position 9,flow_matching,0.3,2.0,27,127
79,replace,2.0,C,l,CllCc5I-S,ClCCc5I-S,9,replace l at position 2 with C,flow_matching,0.3,2.0,27,127
80,add,5.0,H,,ClCCc5I-S,ClCCcH5I-S,10,add H at position 5,flow_matching,0.3,2.0,27,127
81,replace,4.0,n,c,ClCCcH5I-S,ClCCnH5I-S,10,replace c at position 4 with n,flow_matching,0.3,2.0,27,127
82,replace,4.0,c,n,ClCCnH5I-S,ClCCcH5I-S,10,replace n at position 4 with c,flow_matching,0.3,2.0,27,127
83,remove,6.0,5,,ClCCcH5I-S,ClCCcHI-S,9,remove 5 from position 6,flow_matching,0.3,2.0,27,127
84,add,4.0,5,,ClCCcHI-S,ClCC5cHI-S,10,add 5 at position 4,flow_matching,0.3,2.0,27,127
85,replace,4.0,c,5,ClCC5cHI-S,ClCCccHI-S,10,replace 5 at position 4 with c,flow_matching,0.3,2.0,27,127
86,replace,0.0,@,C,ClCCccHI-S,@lCCccHI-S,10,replace C at position 0 with @,flow_matching,0.3,2.0,27,127
87,replace,0.0,C,@,@lCCccHI-S,ClCCccHI-S,10,replace @ at position 0 with C,flow_matching,0.3,2.0,27,127
88,replace,5.0,1,c,ClCCccHI-S,ClCCc1HI-S,10,replace c at position 5 with 1,flow_matching,0.3,2.0,27,127
89,add,2.0,C,,ClCCc1HI-S,ClCCCc1HI-S,11,add C at position 2,flow_matching,0.3,2.0,27,127
90,replace,5.0,6,c,ClCCCc1HI-S,ClCCC61HI-S,11,replace c at position 5 with 6,flow_matching,0.3,2.0,27,127
91,add,3.0,B,,ClCCC61HI-S,ClCBCC61HI-S,12,add B at position 3,flow_matching,0.3,2.0,27,127
92,add,8.0,(,,ClCBCC61HI-S,ClCBCC61(HI-S,13,add ( at position 8,flow_matching,0.3,2.0,27,127
93,add,3.0,],,ClCBCC61(HI-S,ClC]BCC61(HI-S,14,add ] at position 3,flow_matching,0.3,2.0,27,127
94,remove,8.0,1,,ClC]BCC61(HI-S,ClC]BCC6(HI-S,13,remove 1 from position 8,flow_matching,0.3,2.0,27,127
95,remove,6.0,C,,ClC]BCC6(HI-S,ClC]BC6(HI-S,12,remove C from position 6,flow_matching,0.3,2.0,27,127
96,add,1.0,-,,ClC]BC6(HI-S,C-lC]BC6(HI-S,13,add - at position 1,flow_matching,0.3,2.0,27,127
97,remove,5.0,B,,C-lC]BC6(HI-S,C-lC]C6(HI-S,12,remove B from position 5,flow_matching,0.3,2.0,27,127
98,remove,3.0,C,,C-lC]C6(HI-S,C-l]C6(HI-S,11,remove C from position 3,flow_matching,0.3,2.0,27,127
99,replace,0.0,l,C,C-l]C6(HI-S,l-l]C6(HI-S,11,replace C at position 0 with l,flow_matching,0.3,2.0,27,127
100,replace,0.0,C,l,l-l]C6(HI-S,C-l]C6(HI-S,11,replace l at position 0 with C,flow_matching,0.3,2.0,27,127
101,replace,1.0,l,-,C-l]C6(HI-S,Cll]C6(HI-S,11,replace - at position 1 with l,flow_matching,0.3,2.0,27,127
102,add,10.0,B,,Cll]C6(HI-S,Cll]C6(HI-BS,12,add B at position 10,flow_matching,0.3,2.0,27,127
103,remove,3.0,],,Cll]C6(HI-BS,CllC6(HI-BS,11,remove ] from position 3,flow_matching,0.3,2.0,27,127
104,replace,2.0,C,l,CllC6(HI-BS,ClCC6(HI-BS,11,replace l at position 2 with C,flow_matching,0.3,2.0,27,127
105,replace,4.0,c,6,ClCC6(HI-BS,ClCCc(HI-BS,11,replace 6 at position 4 with c,flow_matching,0.3,2.0,27,127
106,replace,5.0,1,(,ClCCc(HI-BS,ClCCc1HI-BS,11,replace ( at position 5 with 1,flow_matching,0.3,2.0,27,127
107,replace,6.0,n,H,ClCCc1HI-BS,ClCCc1nI-BS,11,replace H at position 6 with n,flow_matching,0.3,2.0,27,127
108,replace,7.0,c,I,ClCCc1nI-BS,ClCCc1nc-BS,11,replace I at position 7 with c,flow_matching,0.3,2.0,27,127
109,replace,8.0,2,-,ClCCc1nc-BS,ClCCc1nc2BS,11,replace - at position 8 with 2,flow_matching,0.3,2.0,27,127
110,replace,9.0,c,B,ClCCc1nc2BS,ClCCc1nc2cS,11,replace B at position 9 with c,flow_matching,0.3,2.0,27,127
111,replace,10.0,c,S,ClCCc1nc2cS,ClCCc1nc2cc,11,replace S at position 10 with c,flow_matching,0.3,2.0,27,127
112,add,11.0,c,,ClCCc1nc2cc,ClCCc1nc2ccc,12,add c at position 11,flow_matching,0.3,2.0,27,127
113,add,12.0,n,,ClCCc1nc2ccc,ClCCc1nc2cccn,13,add n at position 12,flow_matching,0.3,2.0,27,127
114,add,13.0,c,,ClCCc1nc2cccn,ClCCc1nc2cccnc,14,add c at position 13,flow_matching,0.3,2.0,27,127
115,add,14.0,2,,ClCCc1nc2cccnc,ClCCc1nc2cccnc2,15,add 2 at position 14,flow_matching,0.3,2.0,27,127
116,add,15.0,n,,ClCCc1nc2cccnc2,ClCCc1nc2cccnc2n,16,add n at position 15,flow_matching,0.3,2.0,27,127
117,add,16.0,1,,ClCCc1nc2cccnc2n,ClCCc1nc2cccnc2n1,17,add 1 at position 16,flow_matching,0.3,2.0,27,127
118,add,17.0,C,,ClCCc1nc2cccnc2n1,ClCCc1nc2cccnc2n1C,18,add C at position 17,flow_matching,0.3,2.0,27,127
119,add,18.0,C,,ClCCc1nc2cccnc2n1C,ClCCc1nc2cccnc2n1CC,19,add C at position 18,flow_matching,0.3,2.0,27,127
120,add,19.0,n,,ClCCc1nc2cccnc2n1CC,ClCCc1nc2cccnc2n1CCn,20,add n at position 19,flow_matching,0.3,2.0,27,127
121,add,20.0,1,,ClCCc1nc2cccnc2n1CCn,ClCCc1nc2cccnc2n1CCn1,21,add 1 at position 20,flow_matching,0.3,2.0,27,127
122,add,21.0,c,,ClCCc1nc2cccnc2n1CCn1,ClCCc1nc2cccnc2n1CCn1c,22,add c at position 21,flow_matching,0.3,2.0,27,127
123,add,22.0,c,,ClCCc1nc2cccnc2n1CCn1c,ClCCc1nc2cccnc2n1CCn1cc,23,add c at position 22,flow_matching,0.3,2.0,27,127
124,add,23.0,c,,ClCCc1nc2cccnc2n1CCn1cc,ClCCc1nc2cccnc2n1CCn1ccc,24,add c at position 23,flow_matching,0.3,2.0,27,127
125,add,24.0,n,,ClCCc1nc2cccnc2n1CCn1ccc,ClCCc1nc2cccnc2n1CCn1cccn,25,add n at position 24,flow_matching,0.3,2.0,27,127
126,add,25.0,1,,ClCCc1nc2cccnc2n1CCn1cccn,ClCCc1nc2cccnc2n1CCn1cccn1,26,add 1 at position 25,flow_matching,0.3,2.0,27,127
127,add,26.0,"
",,ClCCc1nc2cccnc2n1CCn1cccn1,"ClCCc1nc2cccnc2n1CCn1cccn1
",27,"add 
 at position 26",flow_matching,0.3,2.0,27,127
