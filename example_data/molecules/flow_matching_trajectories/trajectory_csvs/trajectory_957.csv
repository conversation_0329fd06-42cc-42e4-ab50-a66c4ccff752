step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,46,135
1,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,46,135
2,replace,0.0,C,@,@,C,1,replace @ at position 0 with C,flow_matching,0.3,2.0,46,135
3,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,46,135
4,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,46,135
5,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,46,135
6,add,3.0,6,,<PERSON>c1<PERSON>,<PERSON>c16<PERSON>,5,add 6 at position 3,flow_matching,0.3,2.0,46,135
7,remove,0.0,C,,Cc16c,c16c,4,remove C from position 0,flow_matching,0.3,2.0,46,135
8,replace,0.0,C,c,c16c,C16c,4,replace c at position 0 with C,flow_matching,0.3,2.0,46,135
9,add,2.0,-,,C16c,C1-6c,5,add - at position 2,flow_matching,0.3,2.0,46,135
10,add,4.0,/,,C1-6c,C1-6/c,6,add / at position 4,flow_matching,0.3,2.0,46,135
11,add,3.0,I,,C1-6/c,C1-I6/c,7,add I at position 3,flow_matching,0.3,2.0,46,135
12,replace,1.0,c,1,C1-I6/c,Cc-I6/c,7,replace 1 at position 1 with c,flow_matching,0.3,2.0,46,135
13,replace,2.0,1,-,Cc-I6/c,Cc1I6/c,7,replace - at position 2 with 1,flow_matching,0.3,2.0,46,135
14,add,1.0,/,,Cc1I6/c,C/c1I6/c,8,add / at position 1,flow_matching,0.3,2.0,46,135
15,replace,6.0,4,/,C/c1I6/c,C/c1I64c,8,replace / at position 6 with 4,flow_matching,0.3,2.0,46,135
16,replace,1.0,c,/,C/c1I64c,Ccc1I64c,8,replace / at position 1 with c,flow_matching,0.3,2.0,46,135
17,remove,0.0,C,,Ccc1I64c,cc1I64c,7,remove C from position 0,flow_matching,0.3,2.0,46,135
18,replace,4.0,1,6,cc1I64c,cc1I14c,7,replace 6 at position 4 with 1,flow_matching,0.3,2.0,46,135
19,replace,0.0,l,c,cc1I14c,lc1I14c,7,replace c at position 0 with l,flow_matching,0.3,2.0,46,135
20,remove,4.0,1,,lc1I14c,lc1I4c,6,remove 1 from position 4,flow_matching,0.3,2.0,46,135
21,replace,0.0,C,l,lc1I4c,Cc1I4c,6,replace l at position 0 with C,flow_matching,0.3,2.0,46,135
22,add,4.0,C,,Cc1I4c,Cc1IC4c,7,add C at position 4,flow_matching,0.3,2.0,46,135
23,replace,2.0,#,1,Cc1IC4c,Cc#IC4c,7,replace 1 at position 2 with #,flow_matching,0.3,2.0,46,135
24,replace,5.0,[,4,Cc#IC4c,Cc#IC[c,7,replace 4 at position 5 with [,flow_matching,0.3,2.0,46,135
25,replace,5.0,4,[,Cc#IC[c,Cc#IC4c,7,replace [ at position 5 with 4,flow_matching,0.3,2.0,46,135
26,add,3.0,[,,Cc#IC4c,Cc#[IC4c,8,add [ at position 3,flow_matching,0.3,2.0,46,135
27,replace,2.0,1,#,Cc#[IC4c,Cc1[IC4c,8,replace # at position 2 with 1,flow_matching,0.3,2.0,46,135
28,replace,3.0,c,[,Cc1[IC4c,Cc1cIC4c,8,replace [ at position 3 with c,flow_matching,0.3,2.0,46,135
29,add,1.0,#,,Cc1cIC4c,C#c1cIC4c,9,add # at position 1,flow_matching,0.3,2.0,46,135
30,add,8.0,/,,C#c1cIC4c,C#c1cIC4/c,10,add / at position 8,flow_matching,0.3,2.0,46,135
31,add,8.0,#,,C#c1cIC4/c,C#c1cIC4#/c,11,add # at position 8,flow_matching,0.3,2.0,46,135
32,add,1.0,2,,C#c1cIC4#/c,C2#c1cIC4#/c,12,add 2 at position 1,flow_matching,0.3,2.0,46,135
33,remove,2.0,#,,C2#c1cIC4#/c,C2c1cIC4#/c,11,remove # from position 2,flow_matching,0.3,2.0,46,135
34,add,2.0,3,,C2c1cIC4#/c,C23c1cIC4#/c,12,add 3 at position 2,flow_matching,0.3,2.0,46,135
35,add,4.0,C,,C23c1cIC4#/c,C23cC1cIC4#/c,13,add C at position 4,flow_matching,0.3,2.0,46,135
36,replace,1.0,c,2,C23cC1cIC4#/c,Cc3cC1cIC4#/c,13,replace 2 at position 1 with c,flow_matching,0.3,2.0,46,135
37,remove,5.0,1,,Cc3cC1cIC4#/c,Cc3cCcIC4#/c,12,remove 1 from position 5,flow_matching,0.3,2.0,46,135
38,add,1.0,o,,Cc3cCcIC4#/c,Coc3cCcIC4#/c,13,add o at position 1,flow_matching,0.3,2.0,46,135
39,replace,6.0,r,c,Coc3cCcIC4#/c,Coc3cCrIC4#/c,13,replace c at position 6 with r,flow_matching,0.3,2.0,46,135
40,remove,3.0,3,,Coc3cCrIC4#/c,CoccCrIC4#/c,12,remove 3 from position 3,flow_matching,0.3,2.0,46,135
41,replace,8.0,=,4,CoccCrIC4#/c,CoccCrIC=#/c,12,replace 4 at position 8 with =,flow_matching,0.3,2.0,46,135
42,add,4.0,N,,CoccCrIC=#/c,CoccNCrIC=#/c,13,add N at position 4,flow_matching,0.3,2.0,46,135
43,remove,11.0,/,,CoccNCrIC=#/c,CoccNCrIC=#c,12,remove / from position 11,flow_matching,0.3,2.0,46,135
44,add,7.0,c,,CoccNCrIC=#c,CoccNCrcIC=#c,13,add c at position 7,flow_matching,0.3,2.0,46,135
45,replace,8.0,1,I,CoccNCrcIC=#c,CoccNCrc1C=#c,13,replace I at position 8 with 1,flow_matching,0.3,2.0,46,135
46,remove,12.0,c,,CoccNCrc1C=#c,CoccNCrc1C=#,12,remove c from position 12,flow_matching,0.3,2.0,46,135
47,add,8.0,/,,CoccNCrc1C=#,CoccNCrc/1C=#,13,add / at position 8,flow_matching,0.3,2.0,46,135
48,add,6.0,\,,CoccNCrc/1C=#,CoccNC\rc/1C=#,14,add \ at position 6,flow_matching,0.3,2.0,46,135
49,replace,1.0,c,o,CoccNC\rc/1C=#,CcccNC\rc/1C=#,14,replace o at position 1 with c,flow_matching,0.3,2.0,46,135
50,remove,10.0,1,,CcccNC\rc/1C=#,CcccNC\rc/C=#,13,remove 1 from position 10,flow_matching,0.3,2.0,46,135
51,replace,2.0,1,c,CcccNC\rc/C=#,Cc1cNC\rc/C=#,13,replace c at position 2 with 1,flow_matching,0.3,2.0,46,135
52,add,11.0,-,,Cc1cNC\rc/C=#,Cc1cNC\rc/C-=#,14,add - at position 11,flow_matching,0.3,2.0,46,135
53,replace,4.0,c,N,Cc1cNC\rc/C-=#,Cc1ccC\rc/C-=#,14,replace N at position 4 with c,flow_matching,0.3,2.0,46,135
54,add,6.0,o,,Cc1ccC\rc/C-=#,Cc1ccCo\rc/C-=#,15,add o at position 6,flow_matching,0.3,2.0,46,135
55,replace,3.0,),c,Cc1ccCo\rc/C-=#,Cc1)cCo\rc/C-=#,15,replace c at position 3 with ),flow_matching,0.3,2.0,46,135
56,remove,3.0,),,Cc1)cCo\rc/C-=#,Cc1cCo\rc/C-=#,14,remove ) from position 3,flow_matching,0.3,2.0,46,135
57,replace,4.0,c,C,Cc1cCo\rc/C-=#,Cc1cco\rc/C-=#,14,replace C at position 4 with c,flow_matching,0.3,2.0,46,135
58,add,12.0,7,,Cc1cco\rc/C-=#,Cc1cco\rc/C-7=#,15,add 7 at position 12,flow_matching,0.3,2.0,46,135
59,remove,2.0,1,,Cc1cco\rc/C-7=#,Cccco\rc/C-7=#,14,remove 1 from position 2,flow_matching,0.3,2.0,46,135
60,replace,2.0,1,c,Cccco\rc/C-7=#,Cc1co\rc/C-7=#,14,replace c at position 2 with 1,flow_matching,0.3,2.0,46,135
61,replace,3.0,6,c,Cc1co\rc/C-7=#,Cc16o\rc/C-7=#,14,replace c at position 3 with 6,flow_matching,0.3,2.0,46,135
62,replace,1.0,+,c,Cc16o\rc/C-7=#,C+16o\rc/C-7=#,14,replace c at position 1 with +,flow_matching,0.3,2.0,46,135
63,replace,1.0,c,+,C+16o\rc/C-7=#,Cc16o\rc/C-7=#,14,replace + at position 1 with c,flow_matching,0.3,2.0,46,135
64,add,13.0,],,Cc16o\rc/C-7=#,Cc16o\rc/C-7=]#,15,add ] at position 13,flow_matching,0.3,2.0,46,135
65,replace,3.0,c,6,Cc16o\rc/C-7=]#,Cc1co\rc/C-7=]#,15,replace 6 at position 3 with c,flow_matching,0.3,2.0,46,135
66,add,1.0,4,,Cc1co\rc/C-7=]#,C4c1co\rc/C-7=]#,16,add 4 at position 1,flow_matching,0.3,2.0,46,135
67,replace,1.0,c,4,C4c1co\rc/C-7=]#,Ccc1co\rc/C-7=]#,16,replace 4 at position 1 with c,flow_matching,0.3,2.0,46,135
68,remove,9.0,/,,Ccc1co\rc/C-7=]#,Ccc1co\rcC-7=]#,15,remove / from position 9,flow_matching,0.3,2.0,46,135
69,add,12.0,-,,Ccc1co\rcC-7=]#,Ccc1co\rcC-7-=]#,16,add - at position 12,flow_matching,0.3,2.0,46,135
70,replace,2.0,1,c,Ccc1co\rcC-7-=]#,Cc11co\rcC-7-=]#,16,replace c at position 2 with 1,flow_matching,0.3,2.0,46,135
71,replace,3.0,c,1,Cc11co\rcC-7-=]#,Cc1cco\rcC-7-=]#,16,replace 1 at position 3 with c,flow_matching,0.3,2.0,46,135
72,add,4.0,c,,Cc1cco\rcC-7-=]#,Cc1ccco\rcC-7-=]#,17,add c at position 4,flow_matching,0.3,2.0,46,135
73,replace,1.0,@,c,Cc1ccco\rcC-7-=]#,C@1ccco\rcC-7-=]#,17,replace c at position 1 with @,flow_matching,0.3,2.0,46,135
74,remove,0.0,C,,C@1ccco\rcC-7-=]#,@1ccco\rcC-7-=]#,16,remove C from position 0,flow_matching,0.3,2.0,46,135
75,add,5.0,6,,@1ccco\rcC-7-=]#,@1ccc6o\rcC-7-=]#,17,add 6 at position 5,flow_matching,0.3,2.0,46,135
76,remove,7.0,\,,@1ccc6o\rcC-7-=]#,@1ccc6orcC-7-=]#,16,remove \ from position 7,flow_matching,0.3,2.0,46,135
77,add,3.0,c,,@1ccc6orcC-7-=]#,@1cccc6orcC-7-=]#,17,add c at position 3,flow_matching,0.3,2.0,46,135
78,replace,11.0,N,-,@1cccc6orcC-7-=]#,@1cccc6orcCN7-=]#,17,replace - at position 11 with N,flow_matching,0.3,2.0,46,135
79,remove,14.0,=,,@1cccc6orcCN7-=]#,@1cccc6orcCN7-]#,16,remove = from position 14,flow_matching,0.3,2.0,46,135
80,add,14.0,6,,@1cccc6orcCN7-]#,@1cccc6orcCN7-6]#,17,add 6 at position 14,flow_matching,0.3,2.0,46,135
81,add,14.0,N,,@1cccc6orcCN7-6]#,@1cccc6orcCN7-N6]#,18,add N at position 14,flow_matching,0.3,2.0,46,135
82,remove,10.0,C,,@1cccc6orcCN7-N6]#,@1cccc6orcN7-N6]#,17,remove C from position 10,flow_matching,0.3,2.0,46,135
83,add,0.0,2,,@1cccc6orcN7-N6]#,2@1cccc6orcN7-N6]#,18,add 2 at position 0,flow_matching,0.3,2.0,46,135
84,replace,0.0,C,2,2@1cccc6orcN7-N6]#,C@1cccc6orcN7-N6]#,18,replace 2 at position 0 with C,flow_matching,0.3,2.0,46,135
85,add,6.0,@,,C@1cccc6orcN7-N6]#,C@1ccc@c6orcN7-N6]#,19,add @ at position 6,flow_matching,0.3,2.0,46,135
86,remove,10.0,r,,C@1ccc@c6orcN7-N6]#,C@1ccc@c6ocN7-N6]#,18,remove r from position 10,flow_matching,0.3,2.0,46,135
87,add,0.0,#,,C@1ccc@c6ocN7-N6]#,#C@1ccc@c6ocN7-N6]#,19,add # at position 0,flow_matching,0.3,2.0,46,135
88,replace,10.0,6,o,#C@1ccc@c6ocN7-N6]#,#C@1ccc@c66cN7-N6]#,19,replace o at position 10 with 6,flow_matching,0.3,2.0,46,135
89,add,15.0,),,#C@1ccc@c66cN7-N6]#,#C@1ccc@c66cN7-)N6]#,20,add ) at position 15,flow_matching,0.3,2.0,46,135
90,replace,1.0,4,C,#C@1ccc@c66cN7-)N6]#,#4@1ccc@c66cN7-)N6]#,20,replace C at position 1 with 4,flow_matching,0.3,2.0,46,135
91,replace,0.0,C,#,#4@1ccc@c66cN7-)N6]#,C4@1ccc@c66cN7-)N6]#,20,replace # at position 0 with C,flow_matching,0.3,2.0,46,135
92,replace,1.0,c,4,C4@1ccc@c66cN7-)N6]#,Cc@1ccc@c66cN7-)N6]#,20,replace 4 at position 1 with c,flow_matching,0.3,2.0,46,135
93,replace,2.0,1,@,Cc@1ccc@c66cN7-)N6]#,Cc11ccc@c66cN7-)N6]#,20,replace @ at position 2 with 1,flow_matching,0.3,2.0,46,135
94,replace,3.0,c,1,Cc11ccc@c66cN7-)N6]#,Cc1cccc@c66cN7-)N6]#,20,replace 1 at position 3 with c,flow_matching,0.3,2.0,46,135
95,replace,5.0,(,c,Cc1cccc@c66cN7-)N6]#,Cc1cc(c@c66cN7-)N6]#,20,replace c at position 5 with (,flow_matching,0.3,2.0,46,135
96,replace,6.0,C,c,Cc1cc(c@c66cN7-)N6]#,Cc1cc(C@c66cN7-)N6]#,20,replace c at position 6 with C,flow_matching,0.3,2.0,46,135
97,replace,7.0,),@,Cc1cc(C@c66cN7-)N6]#,Cc1cc(C)c66cN7-)N6]#,20,replace @ at position 7 with ),flow_matching,0.3,2.0,46,135
98,replace,8.0,n,c,Cc1cc(C)c66cN7-)N6]#,Cc1cc(C)n66cN7-)N6]#,20,replace c at position 8 with n,flow_matching,0.3,2.0,46,135
99,replace,9.0,(,6,Cc1cc(C)n66cN7-)N6]#,Cc1cc(C)n(6cN7-)N6]#,20,replace 6 at position 9 with (,flow_matching,0.3,2.0,46,135
100,replace,10.0,C,6,Cc1cc(C)n(6cN7-)N6]#,Cc1cc(C)n(CcN7-)N6]#,20,replace 6 at position 10 with C,flow_matching,0.3,2.0,46,135
101,replace,11.0,[,c,Cc1cc(C)n(CcN7-)N6]#,Cc1cc(C)n(C[N7-)N6]#,20,replace c at position 11 with [,flow_matching,0.3,2.0,46,135
102,replace,12.0,C,N,Cc1cc(C)n(C[N7-)N6]#,Cc1cc(C)n(C[C7-)N6]#,20,replace N at position 12 with C,flow_matching,0.3,2.0,46,135
103,replace,13.0,@,7,Cc1cc(C)n(C[C7-)N6]#,Cc1cc(C)n(C[C@-)N6]#,20,replace 7 at position 13 with @,flow_matching,0.3,2.0,46,135
104,replace,14.0,@,-,Cc1cc(C)n(C[C@-)N6]#,Cc1cc(C)n(C[C@@)N6]#,20,replace - at position 14 with @,flow_matching,0.3,2.0,46,135
105,replace,15.0,H,),Cc1cc(C)n(C[C@@)N6]#,Cc1cc(C)n(C[C@@HN6]#,20,replace ) at position 15 with H,flow_matching,0.3,2.0,46,135
106,replace,16.0,],N,Cc1cc(C)n(C[C@@HN6]#,Cc1cc(C)n(C[C@@H]6]#,20,replace N at position 16 with ],flow_matching,0.3,2.0,46,135
107,replace,17.0,(,6,Cc1cc(C)n(C[C@@H]6]#,Cc1cc(C)n(C[C@@H](]#,20,replace 6 at position 17 with (,flow_matching,0.3,2.0,46,135
108,replace,18.0,C,],Cc1cc(C)n(C[C@@H](]#,Cc1cc(C)n(C[C@@H](C#,20,replace ] at position 18 with C,flow_matching,0.3,2.0,46,135
109,replace,19.0,),#,Cc1cc(C)n(C[C@@H](C#,Cc1cc(C)n(C[C@@H](C),20,replace # at position 19 with ),flow_matching,0.3,2.0,46,135
110,add,20.0,C,,Cc1cc(C)n(C[C@@H](C),Cc1cc(C)n(C[C@@H](C)C,21,add C at position 20,flow_matching,0.3,2.0,46,135
111,add,21.0,N,,Cc1cc(C)n(C[C@@H](C)C,Cc1cc(C)n(C[C@@H](C)CN,22,add N at position 21,flow_matching,0.3,2.0,46,135
112,add,22.0,C,,Cc1cc(C)n(C[C@@H](C)CN,Cc1cc(C)n(C[C@@H](C)CNC,23,add C at position 22,flow_matching,0.3,2.0,46,135
113,add,23.0,(,,Cc1cc(C)n(C[C@@H](C)CNC,Cc1cc(C)n(C[C@@H](C)CNC(,24,add ( at position 23,flow_matching,0.3,2.0,46,135
114,add,24.0,=,,Cc1cc(C)n(C[C@@H](C)CNC(,Cc1cc(C)n(C[C@@H](C)CNC(=,25,add = at position 24,flow_matching,0.3,2.0,46,135
115,add,25.0,O,,Cc1cc(C)n(C[C@@H](C)CNC(=,Cc1cc(C)n(C[C@@H](C)CNC(=O,26,add O at position 25,flow_matching,0.3,2.0,46,135
116,add,26.0,),,Cc1cc(C)n(C[C@@H](C)CNC(=O,Cc1cc(C)n(C[C@@H](C)CNC(=O),27,add ) at position 26,flow_matching,0.3,2.0,46,135
117,add,27.0,N,,Cc1cc(C)n(C[C@@H](C)CNC(=O),Cc1cc(C)n(C[C@@H](C)CNC(=O)N,28,add N at position 27,flow_matching,0.3,2.0,46,135
118,add,28.0,C,,Cc1cc(C)n(C[C@@H](C)CNC(=O)N,Cc1cc(C)n(C[C@@H](C)CNC(=O)NC,29,add C at position 28,flow_matching,0.3,2.0,46,135
119,add,29.0,c,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NC,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc,30,add c at position 29,flow_matching,0.3,2.0,46,135
120,add,30.0,2,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2,31,add 2 at position 30,flow_matching,0.3,2.0,46,135
121,add,31.0,c,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2c,32,add c at position 31,flow_matching,0.3,2.0,46,135
122,add,32.0,c,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2c,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc,33,add c at position 32,flow_matching,0.3,2.0,46,135
123,add,33.0,3,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3,34,add 3 at position 33,flow_matching,0.3,2.0,46,135
124,add,34.0,c,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3c,35,add c at position 34,flow_matching,0.3,2.0,46,135
125,add,35.0,c,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3c,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3cc,36,add c at position 35,flow_matching,0.3,2.0,46,135
126,add,36.0,c,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3cc,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccc,37,add c at position 36,flow_matching,0.3,2.0,46,135
127,add,37.0,c,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccc,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3cccc,38,add c at position 37,flow_matching,0.3,2.0,46,135
128,add,38.0,c,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3cccc,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc,39,add c at position 38,flow_matching,0.3,2.0,46,135
129,add,39.0,3,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3,40,add 3 at position 39,flow_matching,0.3,2.0,46,135
130,add,40.0,o,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o,41,add o at position 40,flow_matching,0.3,2.0,46,135
131,add,41.0,2,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o2,42,add 2 at position 41,flow_matching,0.3,2.0,46,135
132,add,42.0,),,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o2,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o2),43,add ) at position 42,flow_matching,0.3,2.0,46,135
133,add,43.0,n,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o2),Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o2)n,44,add n at position 43,flow_matching,0.3,2.0,46,135
134,add,44.0,1,,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o2)n,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o2)n1,45,add 1 at position 44,flow_matching,0.3,2.0,46,135
135,add,45.0,"
",,Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o2)n1,"Cc1cc(C)n(C[C@@H](C)CNC(=O)NCc2cc3ccccc3o2)n1
",46,"add 
 at position 45",flow_matching,0.3,2.0,46,135
