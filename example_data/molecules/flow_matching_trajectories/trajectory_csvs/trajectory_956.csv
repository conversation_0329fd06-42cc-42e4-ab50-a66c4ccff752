step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,55,126
1,add,0.0,2,,,2,1,add 2 at position 0,flow_matching,0.3,2.0,55,126
2,replace,0.0,[,2,2,[,1,replace 2 at position 0 with [,flow_matching,0.3,2.0,55,126
3,replace,0.0,C,[,[,C,1,replace [ at position 0 with C,flow_matching,0.3,2.0,55,126
4,add,1.0,N,,C,CN,2,add N at position 1,flow_matching,0.3,2.0,55,126
5,replace,1.0,5,N,CN,C5,2,replace N at position 1 with 5,flow_matching,0.3,2.0,55,126
6,remove,1.0,5,,C5,C,1,remove 5 from position 1,flow_matching,0.3,2.0,55,126
7,add,1.0,N,,<PERSON>,CN,2,add N at position 1,flow_matching,0.3,2.0,55,126
8,replace,0.0,F,C,CN,FN,2,replace C at position 0 with F,flow_matching,0.3,2.0,55,126
9,add,1.0,H,,FN,FHN,3,add H at position 1,flow_matching,0.3,2.0,55,126
10,replace,0.0,C,F,FHN,CHN,3,replace F at position 0 with C,flow_matching,0.3,2.0,55,126
11,remove,0.0,C,,CHN,HN,2,remove C from position 0,flow_matching,0.3,2.0,55,126
12,add,0.0,#,,HN,#HN,3,add # at position 0,flow_matching,0.3,2.0,55,126
13,replace,0.0,C,#,#HN,CHN,3,replace # at position 0 with C,flow_matching,0.3,2.0,55,126
14,replace,1.0,N,H,CHN,CNN,3,replace H at position 1 with N,flow_matching,0.3,2.0,55,126
15,replace,2.0,c,N,CNN,CNc,3,replace N at position 2 with c,flow_matching,0.3,2.0,55,126
16,add,3.0,1,,CNc,CNc1,4,add 1 at position 3,flow_matching,0.3,2.0,55,126
17,replace,3.0,F,1,CNc1,CNcF,4,replace 1 at position 3 with F,flow_matching,0.3,2.0,55,126
18,replace,3.0,1,F,CNcF,CNc1,4,replace F at position 3 with 1,flow_matching,0.3,2.0,55,126
19,replace,1.0,F,N,CNc1,CFc1,4,replace N at position 1 with F,flow_matching,0.3,2.0,55,126
20,remove,0.0,C,,CFc1,Fc1,3,remove C from position 0,flow_matching,0.3,2.0,55,126
21,add,1.0,F,,Fc1,FFc1,4,add F at position 1,flow_matching,0.3,2.0,55,126
22,remove,1.0,F,,FFc1,Fc1,3,remove F from position 1,flow_matching,0.3,2.0,55,126
23,replace,0.0,C,F,Fc1,Cc1,3,replace F at position 0 with C,flow_matching,0.3,2.0,55,126
24,replace,1.0,N,c,Cc1,CN1,3,replace c at position 1 with N,flow_matching,0.3,2.0,55,126
25,replace,2.0,3,1,CN1,CN3,3,replace 1 at position 2 with 3,flow_matching,0.3,2.0,55,126
26,replace,2.0,c,3,CN3,CNc,3,replace 3 at position 2 with c,flow_matching,0.3,2.0,55,126
27,remove,2.0,c,,CNc,CN,2,remove c from position 2,flow_matching,0.3,2.0,55,126
28,add,2.0,c,,CN,CNc,3,add c at position 2,flow_matching,0.3,2.0,55,126
29,add,3.0,1,,CNc,CNc1,4,add 1 at position 3,flow_matching,0.3,2.0,55,126
30,remove,1.0,N,,CNc1,Cc1,3,remove N from position 1,flow_matching,0.3,2.0,55,126
31,replace,1.0,N,c,Cc1,CN1,3,replace c at position 1 with N,flow_matching,0.3,2.0,55,126
32,remove,2.0,1,,CN1,CN,2,remove 1 from position 2,flow_matching,0.3,2.0,55,126
33,add,2.0,c,,CN,CNc,3,add c at position 2,flow_matching,0.3,2.0,55,126
34,replace,1.0,H,N,CNc,CHc,3,replace N at position 1 with H,flow_matching,0.3,2.0,55,126
35,remove,0.0,C,,CHc,Hc,2,remove C from position 0,flow_matching,0.3,2.0,55,126
36,remove,1.0,c,,Hc,H,1,remove c from position 1,flow_matching,0.3,2.0,55,126
37,replace,0.0,=,H,H,=,1,replace H at position 0 with =,flow_matching,0.3,2.0,55,126
38,add,1.0,[,,=,=[,2,add [ at position 1,flow_matching,0.3,2.0,55,126
39,replace,0.0,3,=,=[,3[,2,replace = at position 0 with 3,flow_matching,0.3,2.0,55,126
40,remove,1.0,[,,3[,3,1,remove [ from position 1,flow_matching,0.3,2.0,55,126
41,replace,0.0,r,3,3,r,1,replace 3 at position 0 with r,flow_matching,0.3,2.0,55,126
42,replace,0.0,C,r,r,C,1,replace r at position 0 with C,flow_matching,0.3,2.0,55,126
43,add,1.0,N,,C,CN,2,add N at position 1,flow_matching,0.3,2.0,55,126
44,add,2.0,C,,CN,CNC,3,add C at position 2,flow_matching,0.3,2.0,55,126
45,replace,0.0,+,C,CNC,+NC,3,replace C at position 0 with +,flow_matching,0.3,2.0,55,126
46,replace,1.0,l,N,+NC,+lC,3,replace N at position 1 with l,flow_matching,0.3,2.0,55,126
47,add,2.0,N,,+lC,+lNC,4,add N at position 2,flow_matching,0.3,2.0,55,126
48,remove,2.0,N,,+lNC,+lC,3,remove N from position 2,flow_matching,0.3,2.0,55,126
49,add,2.0,4,,+lC,+l4C,4,add 4 at position 2,flow_matching,0.3,2.0,55,126
50,replace,2.0,(,4,+l4C,+l(C,4,replace 4 at position 2 with (,flow_matching,0.3,2.0,55,126
51,remove,3.0,C,,+l(C,+l(,3,remove C from position 3,flow_matching,0.3,2.0,55,126
52,add,3.0,(,,+l(,+l((,4,add ( at position 3,flow_matching,0.3,2.0,55,126
53,replace,3.0,s,(,+l((,+l(s,4,replace ( at position 3 with s,flow_matching,0.3,2.0,55,126
54,remove,0.0,+,,+l(s,l(s,3,remove + from position 0,flow_matching,0.3,2.0,55,126
55,add,0.0,s,,l(s,sl(s,4,add s at position 0,flow_matching,0.3,2.0,55,126
56,remove,2.0,(,,sl(s,sls,3,remove ( from position 2,flow_matching,0.3,2.0,55,126
57,add,0.0,],,sls,]sls,4,add ] at position 0,flow_matching,0.3,2.0,55,126
58,add,0.0,(,,]sls,(]sls,5,add ( at position 0,flow_matching,0.3,2.0,55,126
59,add,2.0,F,,(]sls,(]Fsls,6,add F at position 2,flow_matching,0.3,2.0,55,126
60,add,4.0,4,,(]Fsls,(]Fs4ls,7,add 4 at position 4,flow_matching,0.3,2.0,55,126
61,replace,0.0,C,(,(]Fs4ls,C]Fs4ls,7,replace ( at position 0 with C,flow_matching,0.3,2.0,55,126
62,add,2.0,O,,C]Fs4ls,C]OFs4ls,8,add O at position 2,flow_matching,0.3,2.0,55,126
63,replace,1.0,N,],C]OFs4ls,CNOFs4ls,8,replace ] at position 1 with N,flow_matching,0.3,2.0,55,126
64,replace,7.0,/,s,CNOFs4ls,CNOFs4l/,8,replace s at position 7 with /,flow_matching,0.3,2.0,55,126
65,replace,2.0,c,O,CNOFs4l/,CNcFs4l/,8,replace O at position 2 with c,flow_matching,0.3,2.0,55,126
66,replace,3.0,1,F,CNcFs4l/,CNc1s4l/,8,replace F at position 3 with 1,flow_matching,0.3,2.0,55,126
67,replace,0.0,B,C,CNc1s4l/,BNc1s4l/,8,replace C at position 0 with B,flow_matching,0.3,2.0,55,126
68,remove,4.0,s,,BNc1s4l/,BNc14l/,7,remove s from position 4,flow_matching,0.3,2.0,55,126
69,remove,5.0,l,,BNc14l/,BNc14/,6,remove l from position 5,flow_matching,0.3,2.0,55,126
70,replace,1.0,2,N,BNc14/,B2c14/,6,replace N at position 1 with 2,flow_matching,0.3,2.0,55,126
71,replace,3.0,I,1,B2c14/,B2cI4/,6,replace 1 at position 3 with I,flow_matching,0.3,2.0,55,126
72,remove,5.0,/,,B2cI4/,B2cI4,5,remove / from position 5,flow_matching,0.3,2.0,55,126
73,replace,0.0,C,B,B2cI4,C2cI4,5,replace B at position 0 with C,flow_matching,0.3,2.0,55,126
74,replace,1.0,N,2,C2cI4,CNcI4,5,replace 2 at position 1 with N,flow_matching,0.3,2.0,55,126
75,replace,3.0,1,I,CNcI4,CNc14,5,replace I at position 3 with 1,flow_matching,0.3,2.0,55,126
76,replace,4.0,n,4,CNc14,CNc1n,5,replace 4 at position 4 with n,flow_matching,0.3,2.0,55,126
77,add,5.0,c,,CNc1n,CNc1nc,6,add c at position 5,flow_matching,0.3,2.0,55,126
78,add,6.0,(,,CNc1nc,CNc1nc(,7,add ( at position 6,flow_matching,0.3,2.0,55,126
79,add,7.0,C,,CNc1nc(,CNc1nc(C,8,add C at position 7,flow_matching,0.3,2.0,55,126
80,add,8.0,2,,CNc1nc(C,CNc1nc(C2,9,add 2 at position 8,flow_matching,0.3,2.0,55,126
81,add,9.0,C,,CNc1nc(C2,CNc1nc(C2C,10,add C at position 9,flow_matching,0.3,2.0,55,126
82,add,10.0,C,,CNc1nc(C2C,CNc1nc(C2CC,11,add C at position 10,flow_matching,0.3,2.0,55,126
83,add,11.0,N,,CNc1nc(C2CC,CNc1nc(C2CCN,12,add N at position 11,flow_matching,0.3,2.0,55,126
84,add,12.0,(,,CNc1nc(C2CCN,CNc1nc(C2CCN(,13,add ( at position 12,flow_matching,0.3,2.0,55,126
85,add,13.0,C,,CNc1nc(C2CCN(,CNc1nc(C2CCN(C,14,add C at position 13,flow_matching,0.3,2.0,55,126
86,add,14.0,(,,CNc1nc(C2CCN(C,CNc1nc(C2CCN(C(,15,add ( at position 14,flow_matching,0.3,2.0,55,126
87,add,15.0,=,,CNc1nc(C2CCN(C(,CNc1nc(C2CCN(C(=,16,add = at position 15,flow_matching,0.3,2.0,55,126
88,add,16.0,O,,CNc1nc(C2CCN(C(=,CNc1nc(C2CCN(C(=O,17,add O at position 16,flow_matching,0.3,2.0,55,126
89,add,17.0,),,CNc1nc(C2CCN(C(=O,CNc1nc(C2CCN(C(=O),18,add ) at position 17,flow_matching,0.3,2.0,55,126
90,add,18.0,C,,CNc1nc(C2CCN(C(=O),CNc1nc(C2CCN(C(=O)C,19,add C at position 18,flow_matching,0.3,2.0,55,126
91,add,19.0,c,,CNc1nc(C2CCN(C(=O)C,CNc1nc(C2CCN(C(=O)Cc,20,add c at position 19,flow_matching,0.3,2.0,55,126
92,add,20.0,3,,CNc1nc(C2CCN(C(=O)Cc,CNc1nc(C2CCN(C(=O)Cc3,21,add 3 at position 20,flow_matching,0.3,2.0,55,126
93,add,21.0,c,,CNc1nc(C2CCN(C(=O)Cc3,CNc1nc(C2CCN(C(=O)Cc3c,22,add c at position 21,flow_matching,0.3,2.0,55,126
94,add,22.0,c,,CNc1nc(C2CCN(C(=O)Cc3c,CNc1nc(C2CCN(C(=O)Cc3cc,23,add c at position 22,flow_matching,0.3,2.0,55,126
95,add,23.0,c,,CNc1nc(C2CCN(C(=O)Cc3cc,CNc1nc(C2CCN(C(=O)Cc3ccc,24,add c at position 23,flow_matching,0.3,2.0,55,126
96,add,24.0,c,,CNc1nc(C2CCN(C(=O)Cc3ccc,CNc1nc(C2CCN(C(=O)Cc3cccc,25,add c at position 24,flow_matching,0.3,2.0,55,126
97,add,25.0,n,,CNc1nc(C2CCN(C(=O)Cc3cccc,CNc1nc(C2CCN(C(=O)Cc3ccccn,26,add n at position 25,flow_matching,0.3,2.0,55,126
98,add,26.0,3,,CNc1nc(C2CCN(C(=O)Cc3ccccn,CNc1nc(C2CCN(C(=O)Cc3ccccn3,27,add 3 at position 26,flow_matching,0.3,2.0,55,126
99,add,27.0,),,CNc1nc(C2CCN(C(=O)Cc3ccccn3,CNc1nc(C2CCN(C(=O)Cc3ccccn3),28,add ) at position 27,flow_matching,0.3,2.0,55,126
100,add,28.0,C,,CNc1nc(C2CCN(C(=O)Cc3ccccn3),CNc1nc(C2CCN(C(=O)Cc3ccccn3)C,29,add C at position 28,flow_matching,0.3,2.0,55,126
101,add,29.0,C,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)C,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC,30,add C at position 29,flow_matching,0.3,2.0,55,126
102,add,30.0,2,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2,31,add 2 at position 30,flow_matching,0.3,2.0,55,126
103,add,31.0,),,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2),32,add ) at position 31,flow_matching,0.3,2.0,55,126
104,add,32.0,[,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2),CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[,33,add [ at position 32,flow_matching,0.3,2.0,55,126
105,add,33.0,n,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[n,34,add n at position 33,flow_matching,0.3,2.0,55,126
106,add,34.0,H,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[n,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH,35,add H at position 34,flow_matching,0.3,2.0,55,126
107,add,35.0,+,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+,36,add + at position 35,flow_matching,0.3,2.0,55,126
108,add,36.0,],,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+],37,add ] at position 36,flow_matching,0.3,2.0,55,126
109,add,37.0,c,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+],CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c,38,add c at position 37,flow_matching,0.3,2.0,55,126
110,add,38.0,2,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2,39,add 2 at position 38,flow_matching,0.3,2.0,55,126
111,add,39.0,c,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c,40,add c at position 39,flow_matching,0.3,2.0,55,126
112,add,40.0,1,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1,41,add 1 at position 40,flow_matching,0.3,2.0,55,126
113,add,41.0,C,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1C,42,add C at position 41,flow_matching,0.3,2.0,55,126
114,add,42.0,N,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1C,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN,43,add N at position 42,flow_matching,0.3,2.0,55,126
115,add,43.0,(,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(,44,add ( at position 43,flow_matching,0.3,2.0,55,126
116,add,44.0,C,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C,45,add C at position 44,flow_matching,0.3,2.0,55,126
117,add,45.0,(,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(,46,add ( at position 45,flow_matching,0.3,2.0,55,126
118,add,46.0,C,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C,47,add C at position 46,flow_matching,0.3,2.0,55,126
119,add,47.0,),,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C),48,add ) at position 47,flow_matching,0.3,2.0,55,126
120,add,48.0,=,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C),CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=,49,add = at position 48,flow_matching,0.3,2.0,55,126
121,add,49.0,O,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O,50,add O at position 49,flow_matching,0.3,2.0,55,126
122,add,50.0,),,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O),51,add ) at position 50,flow_matching,0.3,2.0,55,126
123,add,51.0,C,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O),CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O)C,52,add C at position 51,flow_matching,0.3,2.0,55,126
124,add,52.0,C,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O)C,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O)CC,53,add C at position 52,flow_matching,0.3,2.0,55,126
125,add,53.0,2,,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O)CC,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O)CC2,54,add 2 at position 53,flow_matching,0.3,2.0,55,126
126,add,54.0,"
",,CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O)CC2,"CNc1nc(C2CCN(C(=O)Cc3ccccn3)CC2)[nH+]c2c1CN(C(C)=O)CC2
",55,"add 
 at position 54",flow_matching,0.3,2.0,55,126
