step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,41,165
1,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,41,165
2,add,1.0,S,,F,FS,2,add S at position 1,flow_matching,0.3,2.0,41,165
3,replace,0.0,C,F,FS,CS,2,replace F at position 0 with C,flow_matching,0.3,2.0,41,165
4,replace,1.0,O,S,CS,CO,2,replace S at position 1 with O,flow_matching,0.3,2.0,41,165
5,replace,0.0,l,C,CO,lO,2,replace <PERSON> at position 0 with l,flow_matching,0.3,2.0,41,165
6,add,1.0,[,,lO,l[O,3,add [ at position 1,flow_matching,0.3,2.0,41,165
7,replace,1.0,-,[,l[O,l-O,3,replace [ at position 1 with -,flow_matching,0.3,2.0,41,165
8,add,3.0,c,,l-O,l-Oc,4,add c at position 3,flow_matching,0.3,2.0,41,165
9,add,4.0,[,,l-Oc,l-Oc[,5,add [ at position 4,flow_matching,0.3,2.0,41,165
10,remove,1.0,-,,l-Oc[,lOc[,4,remove - from position 1,flow_matching,0.3,2.0,41,165
11,add,2.0,2,,lOc[,lO2c[,5,add 2 at position 2,flow_matching,0.3,2.0,41,165
12,add,2.0,5,,lO2c[,lO52c[,6,add 5 at position 2,flow_matching,0.3,2.0,41,165
13,add,1.0,s,,lO52c[,lsO52c[,7,add s at position 1,flow_matching,0.3,2.0,41,165
14,add,1.0,6,,lsO52c[,l6sO52c[,8,add 6 at position 1,flow_matching,0.3,2.0,41,165
15,replace,0.0,C,l,l6sO52c[,C6sO52c[,8,replace l at position 0 with C,flow_matching,0.3,2.0,41,165
16,add,6.0,@,,C6sO52c[,C6sO52@c[,9,add @ at position 6,flow_matching,0.3,2.0,41,165
17,replace,3.0,\,O,C6sO52@c[,C6s\52@c[,9,replace O at position 3 with \,flow_matching,0.3,2.0,41,165
18,add,9.0,B,,C6s\52@c[,C6s\52@c[B,10,add B at position 9,flow_matching,0.3,2.0,41,165
19,replace,1.0,O,6,C6s\52@c[B,COs\52@c[B,10,replace 6 at position 1 with O,flow_matching,0.3,2.0,41,165
20,add,5.0,],,COs\52@c[B,COs\5]2@c[B,11,add ] at position 5,flow_matching,0.3,2.0,41,165
21,replace,5.0,s,],COs\5]2@c[B,COs\5s2@c[B,11,replace ] at position 5 with s,flow_matching,0.3,2.0,41,165
22,add,7.0,6,,COs\5s2@c[B,COs\5s26@c[B,12,add 6 at position 7,flow_matching,0.3,2.0,41,165
23,replace,2.0,c,s,COs\5s26@c[B,COc\5s26@c[B,12,replace s at position 2 with c,flow_matching,0.3,2.0,41,165
24,replace,3.0,1,\,COc\5s26@c[B,COc15s26@c[B,12,replace \ at position 3 with 1,flow_matching,0.3,2.0,41,165
25,remove,10.0,[,,COc15s26@c[B,COc15s26@cB,11,remove [ from position 10,flow_matching,0.3,2.0,41,165
26,replace,5.0,+,s,COc15s26@cB,COc15+26@cB,11,replace s at position 5 with +,flow_matching,0.3,2.0,41,165
27,remove,6.0,2,,COc15+26@cB,COc15+6@cB,10,remove 2 from position 6,flow_matching,0.3,2.0,41,165
28,remove,5.0,+,,COc15+6@cB,COc156@cB,9,remove + from position 5,flow_matching,0.3,2.0,41,165
29,remove,3.0,1,,COc156@cB,COc56@cB,8,remove 1 from position 3,flow_matching,0.3,2.0,41,165
30,remove,6.0,c,,COc56@cB,COc56@B,7,remove c from position 6,flow_matching,0.3,2.0,41,165
31,add,6.0,O,,COc56@B,COc56@OB,8,add O at position 6,flow_matching,0.3,2.0,41,165
32,replace,3.0,1,5,COc56@OB,COc16@OB,8,replace 5 at position 3 with 1,flow_matching,0.3,2.0,41,165
33,replace,4.0,c,6,COc16@OB,COc1c@OB,8,replace 6 at position 4 with c,flow_matching,0.3,2.0,41,165
34,replace,3.0,],1,COc1c@OB,COc]c@OB,8,replace 1 at position 3 with ],flow_matching,0.3,2.0,41,165
35,add,8.0,/,,COc]c@OB,COc]c@OB/,9,add / at position 8,flow_matching,0.3,2.0,41,165
36,remove,4.0,c,,COc]c@OB/,COc]@OB/,8,remove c from position 4,flow_matching,0.3,2.0,41,165
37,add,7.0,),,COc]@OB/,COc]@OB)/,9,add ) at position 7,flow_matching,0.3,2.0,41,165
38,remove,8.0,/,,COc]@OB)/,COc]@OB),8,remove / from position 8,flow_matching,0.3,2.0,41,165
39,remove,3.0,],,COc]@OB),COc@OB),7,remove ] from position 3,flow_matching,0.3,2.0,41,165
40,add,3.0,[,,COc@OB),COc[@OB),8,add [ at position 3,flow_matching,0.3,2.0,41,165
41,add,8.0,o,,COc[@OB),COc[@OB)o,9,add o at position 8,flow_matching,0.3,2.0,41,165
42,replace,1.0,+,O,COc[@OB)o,C+c[@OB)o,9,replace O at position 1 with +,flow_matching,0.3,2.0,41,165
43,remove,3.0,[,,C+c[@OB)o,C+c@OB)o,8,remove [ from position 3,flow_matching,0.3,2.0,41,165
44,replace,1.0,O,+,C+c@OB)o,COc@OB)o,8,replace + at position 1 with O,flow_matching,0.3,2.0,41,165
45,remove,4.0,O,,COc@OB)o,COc@B)o,7,remove O from position 4,flow_matching,0.3,2.0,41,165
46,add,2.0,5,,COc@B)o,CO5c@B)o,8,add 5 at position 2,flow_matching,0.3,2.0,41,165
47,replace,2.0,],5,CO5c@B)o,CO]c@B)o,8,replace 5 at position 2 with ],flow_matching,0.3,2.0,41,165
48,remove,5.0,B,,CO]c@B)o,CO]c@)o,7,remove B from position 5,flow_matching,0.3,2.0,41,165
49,replace,2.0,c,],CO]c@)o,COcc@)o,7,replace ] at position 2 with c,flow_matching,0.3,2.0,41,165
50,add,7.0,4,,COcc@)o,COcc@)o4,8,add 4 at position 7,flow_matching,0.3,2.0,41,165
51,add,4.0,+,,COcc@)o4,COcc+@)o4,9,add + at position 4,flow_matching,0.3,2.0,41,165
52,replace,3.0,1,c,COcc+@)o4,COc1+@)o4,9,replace c at position 3 with 1,flow_matching,0.3,2.0,41,165
53,replace,4.0,c,+,COc1+@)o4,COc1c@)o4,9,replace + at position 4 with c,flow_matching,0.3,2.0,41,165
54,add,1.0,H,,COc1c@)o4,CHOc1c@)o4,10,add H at position 1,flow_matching,0.3,2.0,41,165
55,add,2.0,1,,CHOc1c@)o4,CH1Oc1c@)o4,11,add 1 at position 2,flow_matching,0.3,2.0,41,165
56,remove,6.0,c,,CH1Oc1c@)o4,CH1Oc1@)o4,10,remove c from position 6,flow_matching,0.3,2.0,41,165
57,replace,7.0,r,),CH1Oc1@)o4,CH1Oc1@ro4,10,replace ) at position 7 with r,flow_matching,0.3,2.0,41,165
58,replace,5.0,S,1,CH1Oc1@ro4,CH1OcS@ro4,10,replace 1 at position 5 with S,flow_matching,0.3,2.0,41,165
59,replace,2.0,5,1,CH1OcS@ro4,CH5OcS@ro4,10,replace 1 at position 2 with 5,flow_matching,0.3,2.0,41,165
60,replace,1.0,O,H,CH5OcS@ro4,CO5OcS@ro4,10,replace H at position 1 with O,flow_matching,0.3,2.0,41,165
61,replace,0.0,-,C,CO5OcS@ro4,-O5OcS@ro4,10,replace C at position 0 with -,flow_matching,0.3,2.0,41,165
62,add,4.0,n,,-O5OcS@ro4,-O5OncS@ro4,11,add n at position 4,flow_matching,0.3,2.0,41,165
63,add,10.0,S,,-O5OncS@ro4,-O5OncS@roS4,12,add S at position 10,flow_matching,0.3,2.0,41,165
64,add,8.0,5,,-O5OncS@roS4,-O5OncS@5roS4,13,add 5 at position 8,flow_matching,0.3,2.0,41,165
65,replace,9.0,l,r,-O5OncS@5roS4,-O5OncS@5loS4,13,replace r at position 9 with l,flow_matching,0.3,2.0,41,165
66,replace,8.0,#,5,-O5OncS@5loS4,-O5OncS@#loS4,13,replace 5 at position 8 with #,flow_matching,0.3,2.0,41,165
67,replace,3.0,N,O,-O5OncS@#loS4,-O5NncS@#loS4,13,replace O at position 3 with N,flow_matching,0.3,2.0,41,165
68,add,0.0,n,,-O5NncS@#loS4,n-O5NncS@#loS4,14,add n at position 0,flow_matching,0.3,2.0,41,165
69,replace,7.0,2,S,n-O5NncS@#loS4,n-O5Nnc2@#loS4,14,replace S at position 7 with 2,flow_matching,0.3,2.0,41,165
70,remove,10.0,l,,n-O5Nnc2@#loS4,n-O5Nnc2@#oS4,13,remove l from position 10,flow_matching,0.3,2.0,41,165
71,replace,0.0,C,n,n-O5Nnc2@#oS4,C-O5Nnc2@#oS4,13,replace n at position 0 with C,flow_matching,0.3,2.0,41,165
72,remove,9.0,#,,C-O5Nnc2@#oS4,C-O5Nnc2@oS4,12,remove # from position 9,flow_matching,0.3,2.0,41,165
73,add,2.0,4,,C-O5Nnc2@oS4,C-4O5Nnc2@oS4,13,add 4 at position 2,flow_matching,0.3,2.0,41,165
74,remove,10.0,o,,C-4O5Nnc2@oS4,C-4O5Nnc2@S4,12,remove o from position 10,flow_matching,0.3,2.0,41,165
75,remove,10.0,S,,C-4O5Nnc2@S4,C-4O5Nnc2@4,11,remove S from position 10,flow_matching,0.3,2.0,41,165
76,replace,10.0,B,4,C-4O5Nnc2@4,C-4O5Nnc2@B,11,replace 4 at position 10 with B,flow_matching,0.3,2.0,41,165
77,add,2.0,S,,C-4O5Nnc2@B,C-S4O5Nnc2@B,12,add S at position 2,flow_matching,0.3,2.0,41,165
78,replace,8.0,o,c,C-S4O5Nnc2@B,C-S4O5Nno2@B,12,replace c at position 8 with o,flow_matching,0.3,2.0,41,165
79,replace,1.0,O,-,C-S4O5Nno2@B,COS4O5Nno2@B,12,replace - at position 1 with O,flow_matching,0.3,2.0,41,165
80,add,9.0,),,COS4O5Nno2@B,COS4O5Nno)2@B,13,add ) at position 9,flow_matching,0.3,2.0,41,165
81,replace,2.0,c,S,COS4O5Nno)2@B,COc4O5Nno)2@B,13,replace S at position 2 with c,flow_matching,0.3,2.0,41,165
82,add,9.0,6,,COc4O5Nno)2@B,COc4O5Nno6)2@B,14,add 6 at position 9,flow_matching,0.3,2.0,41,165
83,replace,10.0,I,),COc4O5Nno6)2@B,COc4O5Nno6I2@B,14,replace ) at position 10 with I,flow_matching,0.3,2.0,41,165
84,add,14.0,n,,COc4O5Nno6I2@B,COc4O5Nno6I2@Bn,15,add n at position 14,flow_matching,0.3,2.0,41,165
85,replace,9.0,3,6,COc4O5Nno6I2@Bn,COc4O5Nno3I2@Bn,15,replace 6 at position 9 with 3,flow_matching,0.3,2.0,41,165
86,replace,3.0,1,4,COc4O5Nno3I2@Bn,COc1O5Nno3I2@Bn,15,replace 4 at position 3 with 1,flow_matching,0.3,2.0,41,165
87,replace,4.0,c,O,COc1O5Nno3I2@Bn,COc1c5Nno3I2@Bn,15,replace O at position 4 with c,flow_matching,0.3,2.0,41,165
88,replace,5.0,),5,COc1c5Nno3I2@Bn,COc1c)Nno3I2@Bn,15,replace 5 at position 5 with ),flow_matching,0.3,2.0,41,165
89,replace,5.0,c,),COc1c)Nno3I2@Bn,COc1ccNno3I2@Bn,15,replace ) at position 5 with c,flow_matching,0.3,2.0,41,165
90,add,3.0,n,,COc1ccNno3I2@Bn,COcn1ccNno3I2@Bn,16,add n at position 3,flow_matching,0.3,2.0,41,165
91,add,2.0,r,,COcn1ccNno3I2@Bn,COrcn1ccNno3I2@Bn,17,add r at position 2,flow_matching,0.3,2.0,41,165
92,add,0.0,4,,COrcn1ccNno3I2@Bn,4COrcn1ccNno3I2@Bn,18,add 4 at position 0,flow_matching,0.3,2.0,41,165
93,remove,12.0,3,,4COrcn1ccNno3I2@Bn,4COrcn1ccNnoI2@Bn,17,remove 3 from position 12,flow_matching,0.3,2.0,41,165
94,remove,16.0,n,,4COrcn1ccNnoI2@Bn,4COrcn1ccNnoI2@B,16,remove n from position 16,flow_matching,0.3,2.0,41,165
95,add,15.0,S,,4COrcn1ccNnoI2@B,4COrcn1ccNnoI2@SB,17,add S at position 15,flow_matching,0.3,2.0,41,165
96,add,4.0,=,,4COrcn1ccNnoI2@SB,4COr=cn1ccNnoI2@SB,18,add = at position 4,flow_matching,0.3,2.0,41,165
97,add,1.0,l,,4COr=cn1ccNnoI2@SB,4lCOr=cn1ccNnoI2@SB,19,add l at position 1,flow_matching,0.3,2.0,41,165
98,remove,2.0,C,,4lCOr=cn1ccNnoI2@SB,4lOr=cn1ccNnoI2@SB,18,remove C from position 2,flow_matching,0.3,2.0,41,165
99,add,11.0,),,4lOr=cn1ccNnoI2@SB,4lOr=cn1ccN)noI2@SB,19,add ) at position 11,flow_matching,0.3,2.0,41,165
100,replace,14.0,H,I,4lOr=cn1ccN)noI2@SB,4lOr=cn1ccN)noH2@SB,19,replace I at position 14 with H,flow_matching,0.3,2.0,41,165
101,replace,1.0,S,l,4lOr=cn1ccN)noH2@SB,4SOr=cn1ccN)noH2@SB,19,replace l at position 1 with S,flow_matching,0.3,2.0,41,165
102,replace,0.0,C,4,4SOr=cn1ccN)noH2@SB,CSOr=cn1ccN)noH2@SB,19,replace 4 at position 0 with C,flow_matching,0.3,2.0,41,165
103,replace,1.0,O,S,CSOr=cn1ccN)noH2@SB,COOr=cn1ccN)noH2@SB,19,replace S at position 1 with O,flow_matching,0.3,2.0,41,165
104,replace,2.0,c,O,COOr=cn1ccN)noH2@SB,COcr=cn1ccN)noH2@SB,19,replace O at position 2 with c,flow_matching,0.3,2.0,41,165
105,replace,3.0,1,r,COcr=cn1ccN)noH2@SB,COc1=cn1ccN)noH2@SB,19,replace r at position 3 with 1,flow_matching,0.3,2.0,41,165
106,replace,4.0,c,=,COc1=cn1ccN)noH2@SB,COc1ccn1ccN)noH2@SB,19,replace = at position 4 with c,flow_matching,0.3,2.0,41,165
107,remove,12.0,n,,COc1ccn1ccN)noH2@SB,COc1ccn1ccN)oH2@SB,18,remove n from position 12,flow_matching,0.3,2.0,41,165
108,replace,6.0,(,n,COc1ccn1ccN)oH2@SB,COc1cc(1ccN)oH2@SB,18,replace n at position 6 with (,flow_matching,0.3,2.0,41,165
109,remove,7.0,1,,COc1cc(1ccN)oH2@SB,COc1cc(ccN)oH2@SB,17,remove 1 from position 7,flow_matching,0.3,2.0,41,165
110,replace,8.0,7,c,COc1cc(ccN)oH2@SB,COc1cc(c7N)oH2@SB,17,replace c at position 8 with 7,flow_matching,0.3,2.0,41,165
111,replace,3.0,\,1,COc1cc(c7N)oH2@SB,COc\cc(c7N)oH2@SB,17,replace 1 at position 3 with \,flow_matching,0.3,2.0,41,165
112,add,17.0,),,COc\cc(c7N)oH2@SB,COc\cc(c7N)oH2@SB),18,add ) at position 17,flow_matching,0.3,2.0,41,165
113,replace,3.0,1,\,COc\cc(c7N)oH2@SB),COc1cc(c7N)oH2@SB),18,replace \ at position 3 with 1,flow_matching,0.3,2.0,41,165
114,add,8.0,#,,COc1cc(c7N)oH2@SB),COc1cc(c#7N)oH2@SB),19,add # at position 8,flow_matching,0.3,2.0,41,165
115,remove,9.0,7,,COc1cc(c#7N)oH2@SB),COc1cc(c#N)oH2@SB),18,remove 7 from position 9,flow_matching,0.3,2.0,41,165
116,replace,16.0,),B,COc1cc(c#N)oH2@SB),COc1cc(c#N)oH2@S)),18,replace B at position 16 with ),flow_matching,0.3,2.0,41,165
117,replace,7.0,O,c,COc1cc(c#N)oH2@S)),COc1cc(O#N)oH2@S)),18,replace c at position 7 with O,flow_matching,0.3,2.0,41,165
118,remove,2.0,c,,COc1cc(O#N)oH2@S)),CO1cc(O#N)oH2@S)),17,remove c from position 2,flow_matching,0.3,2.0,41,165
119,add,6.0,[,,CO1cc(O#N)oH2@S)),CO1cc([O#N)oH2@S)),18,add [ at position 6,flow_matching,0.3,2.0,41,165
120,add,13.0,l,,CO1cc([O#N)oH2@S)),CO1cc([O#N)oHl2@S)),19,add l at position 13,flow_matching,0.3,2.0,41,165
121,replace,2.0,c,1,CO1cc([O#N)oHl2@S)),COccc([O#N)oHl2@S)),19,replace 1 at position 2 with c,flow_matching,0.3,2.0,41,165
122,remove,8.0,#,,COccc([O#N)oHl2@S)),COccc([ON)oHl2@S)),18,remove # from position 8,flow_matching,0.3,2.0,41,165
123,replace,7.0,c,O,COccc([ON)oHl2@S)),COccc([cN)oHl2@S)),18,replace O at position 7 with c,flow_matching,0.3,2.0,41,165
124,replace,7.0,o,c,COccc([cN)oHl2@S)),COccc([oN)oHl2@S)),18,replace c at position 7 with o,flow_matching,0.3,2.0,41,165
125,add,13.0,],,COccc([oN)oHl2@S)),COccc([oN)oHl]2@S)),19,add ] at position 13,flow_matching,0.3,2.0,41,165
126,replace,5.0,),(,COccc([oN)oHl]2@S)),COccc)[oN)oHl]2@S)),19,replace ( at position 5 with ),flow_matching,0.3,2.0,41,165
127,replace,3.0,1,c,COccc)[oN)oHl]2@S)),COc1c)[oN)oHl]2@S)),19,replace c at position 3 with 1,flow_matching,0.3,2.0,41,165
128,replace,15.0,],@,COc1c)[oN)oHl]2@S)),COc1c)[oN)oHl]2]S)),19,replace @ at position 15 with ],flow_matching,0.3,2.0,41,165
129,replace,0.0,+,C,COc1c)[oN)oHl]2]S)),+Oc1c)[oN)oHl]2]S)),19,replace C at position 0 with +,flow_matching,0.3,2.0,41,165
130,replace,6.0,2,[,+Oc1c)[oN)oHl]2]S)),+Oc1c)2oN)oHl]2]S)),19,replace [ at position 6 with 2,flow_matching,0.3,2.0,41,165
131,replace,0.0,C,+,+Oc1c)2oN)oHl]2]S)),COc1c)2oN)oHl]2]S)),19,replace + at position 0 with C,flow_matching,0.3,2.0,41,165
132,replace,5.0,c,),COc1c)2oN)oHl]2]S)),COc1cc2oN)oHl]2]S)),19,replace ) at position 5 with c,flow_matching,0.3,2.0,41,165
133,replace,6.0,(,2,COc1cc2oN)oHl]2]S)),COc1cc(oN)oHl]2]S)),19,replace 2 at position 6 with (,flow_matching,0.3,2.0,41,165
134,replace,7.0,O,o,COc1cc(oN)oHl]2]S)),COc1cc(ON)oHl]2]S)),19,replace o at position 7 with O,flow_matching,0.3,2.0,41,165
135,replace,8.0,C,N,COc1cc(ON)oHl]2]S)),COc1cc(OC)oHl]2]S)),19,replace N at position 8 with C,flow_matching,0.3,2.0,41,165
136,replace,10.0,c,o,COc1cc(OC)oHl]2]S)),COc1cc(OC)cHl]2]S)),19,replace o at position 10 with c,flow_matching,0.3,2.0,41,165
137,replace,11.0,(,H,COc1cc(OC)cHl]2]S)),COc1cc(OC)c(l]2]S)),19,replace H at position 11 with (,flow_matching,0.3,2.0,41,165
138,replace,12.0,C,l,COc1cc(OC)c(l]2]S)),COc1cc(OC)c(C]2]S)),19,replace l at position 12 with C,flow_matching,0.3,2.0,41,165
139,replace,13.0,(,],COc1cc(OC)c(C]2]S)),COc1cc(OC)c(C(2]S)),19,replace ] at position 13 with (,flow_matching,0.3,2.0,41,165
140,replace,14.0,C,2,COc1cc(OC)c(C(2]S)),COc1cc(OC)c(C(C]S)),19,replace 2 at position 14 with C,flow_matching,0.3,2.0,41,165
141,replace,15.0,),],COc1cc(OC)c(C(C]S)),COc1cc(OC)c(C(C)S)),19,replace ] at position 15 with ),flow_matching,0.3,2.0,41,165
142,replace,16.0,=,S,COc1cc(OC)c(C(C)S)),COc1cc(OC)c(C(C)=)),19,replace S at position 16 with =,flow_matching,0.3,2.0,41,165
143,replace,17.0,O,),COc1cc(OC)c(C(C)=)),COc1cc(OC)c(C(C)=O),19,replace ) at position 17 with O,flow_matching,0.3,2.0,41,165
144,add,19.0,c,,COc1cc(OC)c(C(C)=O),COc1cc(OC)c(C(C)=O)c,20,add c at position 19,flow_matching,0.3,2.0,41,165
145,add,20.0,c,,COc1cc(OC)c(C(C)=O)c,COc1cc(OC)c(C(C)=O)cc,21,add c at position 20,flow_matching,0.3,2.0,41,165
146,add,21.0,1,,COc1cc(OC)c(C(C)=O)cc,COc1cc(OC)c(C(C)=O)cc1,22,add 1 at position 21,flow_matching,0.3,2.0,41,165
147,add,22.0,C,,COc1cc(OC)c(C(C)=O)cc1,COc1cc(OC)c(C(C)=O)cc1C,23,add C at position 22,flow_matching,0.3,2.0,41,165
148,add,23.0,S,,COc1cc(OC)c(C(C)=O)cc1C,COc1cc(OC)c(C(C)=O)cc1CS,24,add S at position 23,flow_matching,0.3,2.0,41,165
149,add,24.0,c,,COc1cc(OC)c(C(C)=O)cc1CS,COc1cc(OC)c(C(C)=O)cc1CSc,25,add c at position 24,flow_matching,0.3,2.0,41,165
150,add,25.0,1,,COc1cc(OC)c(C(C)=O)cc1CSc,COc1cc(OC)c(C(C)=O)cc1CSc1,26,add 1 at position 25,flow_matching,0.3,2.0,41,165
151,add,26.0,n,,COc1cc(OC)c(C(C)=O)cc1CSc1,COc1cc(OC)c(C(C)=O)cc1CSc1n,27,add n at position 26,flow_matching,0.3,2.0,41,165
152,add,27.0,n,,COc1cc(OC)c(C(C)=O)cc1CSc1n,COc1cc(OC)c(C(C)=O)cc1CSc1nn,28,add n at position 27,flow_matching,0.3,2.0,41,165
153,add,28.0,n,,COc1cc(OC)c(C(C)=O)cc1CSc1nn,COc1cc(OC)c(C(C)=O)cc1CSc1nnn,29,add n at position 28,flow_matching,0.3,2.0,41,165
154,add,29.0,n,,COc1cc(OC)c(C(C)=O)cc1CSc1nnn,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn,30,add n at position 29,flow_matching,0.3,2.0,41,165
155,add,30.0,1,,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1,31,add 1 at position 30,flow_matching,0.3,2.0,41,165
156,add,31.0,-,,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-,32,add - at position 31,flow_matching,0.3,2.0,41,165
157,add,32.0,c,,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c,33,add c at position 32,flow_matching,0.3,2.0,41,165
158,add,33.0,1,,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1,34,add 1 at position 33,flow_matching,0.3,2.0,41,165
159,add,34.0,c,,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1c,35,add c at position 34,flow_matching,0.3,2.0,41,165
160,add,35.0,c,,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1c,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1cc,36,add c at position 35,flow_matching,0.3,2.0,41,165
161,add,36.0,c,,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1cc,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1ccc,37,add c at position 36,flow_matching,0.3,2.0,41,165
162,add,37.0,c,,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1ccc,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1cccc,38,add c at position 37,flow_matching,0.3,2.0,41,165
163,add,38.0,c,,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1cccc,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1ccccc,39,add c at position 38,flow_matching,0.3,2.0,41,165
164,add,39.0,1,,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1ccccc,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1ccccc1,40,add 1 at position 39,flow_matching,0.3,2.0,41,165
165,add,40.0,"
",,COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1ccccc1,"COc1cc(OC)c(C(C)=O)cc1CSc1nnnn1-c1ccccc1
",41,"add 
 at position 40",flow_matching,0.3,2.0,41,165
