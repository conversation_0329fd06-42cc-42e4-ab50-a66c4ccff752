step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,30,97
1,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,30,97
2,replace,0.0,C,B,B,C,1,replace <PERSON> at position 0 with C,flow_matching,0.3,2.0,30,97
3,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,30,97
4,replace,1.0,B,O,CO,CB,2,replace O at position 1 with B,flow_matching,0.3,2.0,30,97
5,replace,1.0,7,B,CB,C7,2,replace B at position 1 with 7,flow_matching,0.3,2.0,30,97
6,replace,1.0,O,7,C7,<PERSON>,2,replace 7 at position 1 with O,flow_matching,0.3,2.0,30,97
7,add,2.0,O,,<PERSON>,COO,3,add O at position 2,flow_matching,0.3,2.0,30,97
8,replace,0.0,2,C,COO,2OO,3,replace C at position 0 with 2,flow_matching,0.3,2.0,30,97
9,add,2.0,),,2OO,2O)O,4,add ) at position 2,flow_matching,0.3,2.0,30,97
10,add,3.0,l,,2O)O,2O)lO,5,add l at position 3,flow_matching,0.3,2.0,30,97
11,replace,0.0,C,2,2O)lO,CO)lO,5,replace 2 at position 0 with C,flow_matching,0.3,2.0,30,97
12,replace,2.0,C,),CO)lO,COClO,5,replace ) at position 2 with C,flow_matching,0.3,2.0,30,97
13,add,0.0,4,,COClO,4COClO,6,add 4 at position 0,flow_matching,0.3,2.0,30,97
14,replace,0.0,C,4,4COClO,CCOClO,6,replace 4 at position 0 with C,flow_matching,0.3,2.0,30,97
15,remove,3.0,C,,CCOClO,CCOlO,5,remove C from position 3,flow_matching,0.3,2.0,30,97
16,remove,2.0,O,,CCOlO,CClO,4,remove O from position 2,flow_matching,0.3,2.0,30,97
17,replace,0.0,O,C,CClO,OClO,4,replace C at position 0 with O,flow_matching,0.3,2.0,30,97
18,replace,0.0,C,O,OClO,CClO,4,replace O at position 0 with C,flow_matching,0.3,2.0,30,97
19,remove,1.0,C,,CClO,ClO,3,remove C from position 1,flow_matching,0.3,2.0,30,97
20,replace,0.0,I,C,ClO,IlO,3,replace C at position 0 with I,flow_matching,0.3,2.0,30,97
21,replace,2.0,/,O,IlO,Il/,3,replace O at position 2 with /,flow_matching,0.3,2.0,30,97
22,add,0.0,C,,Il/,CIl/,4,add C at position 0,flow_matching,0.3,2.0,30,97
23,replace,1.0,O,I,CIl/,COl/,4,replace I at position 1 with O,flow_matching,0.3,2.0,30,97
24,replace,2.0,[,l,COl/,CO[/,4,replace l at position 2 with [,flow_matching,0.3,2.0,30,97
25,add,3.0,F,,CO[/,CO[F/,5,add F at position 3,flow_matching,0.3,2.0,30,97
26,replace,3.0,1,F,CO[F/,CO[1/,5,replace F at position 3 with 1,flow_matching,0.3,2.0,30,97
27,replace,2.0,C,[,CO[1/,COC1/,5,replace [ at position 2 with C,flow_matching,0.3,2.0,30,97
28,replace,0.0,l,C,COC1/,lOC1/,5,replace C at position 0 with l,flow_matching,0.3,2.0,30,97
29,replace,0.0,C,l,lOC1/,COC1/,5,replace l at position 0 with C,flow_matching,0.3,2.0,30,97
30,add,5.0,F,,COC1/,COC1/F,6,add F at position 5,flow_matching,0.3,2.0,30,97
31,add,4.0,B,,COC1/F,COC1B/F,7,add B at position 4,flow_matching,0.3,2.0,30,97
32,remove,1.0,O,,COC1B/F,CC1B/F,6,remove O from position 1,flow_matching,0.3,2.0,30,97
33,add,3.0,l,,CC1B/F,CC1lB/F,7,add l at position 3,flow_matching,0.3,2.0,30,97
34,replace,1.0,O,C,CC1lB/F,CO1lB/F,7,replace C at position 1 with O,flow_matching,0.3,2.0,30,97
35,remove,5.0,/,,CO1lB/F,CO1lBF,6,remove / from position 5,flow_matching,0.3,2.0,30,97
36,remove,1.0,O,,CO1lBF,C1lBF,5,remove O from position 1,flow_matching,0.3,2.0,30,97
37,add,2.0,I,,C1lBF,C1IlBF,6,add I at position 2,flow_matching,0.3,2.0,30,97
38,replace,1.0,O,1,C1IlBF,COIlBF,6,replace 1 at position 1 with O,flow_matching,0.3,2.0,30,97
39,add,1.0,\,,COIlBF,C\OIlBF,7,add \ at position 1,flow_matching,0.3,2.0,30,97
40,replace,1.0,O,\,C\OIlBF,COOIlBF,7,replace \ at position 1 with O,flow_matching,0.3,2.0,30,97
41,remove,6.0,F,,COOIlBF,COOIlB,6,remove F from position 6,flow_matching,0.3,2.0,30,97
42,replace,2.0,C,O,COOIlB,COCIlB,6,replace O at position 2 with C,flow_matching,0.3,2.0,30,97
43,replace,3.0,[,I,COCIlB,COC[lB,6,replace I at position 3 with [,flow_matching,0.3,2.0,30,97
44,replace,4.0,],l,COC[lB,COC[]B,6,replace l at position 4 with ],flow_matching,0.3,2.0,30,97
45,replace,4.0,C,],COC[]B,COC[CB,6,replace ] at position 4 with C,flow_matching,0.3,2.0,30,97
46,add,4.0,-,,COC[CB,COC[-CB,7,add - at position 4,flow_matching,0.3,2.0,30,97
47,remove,0.0,C,,COC[-CB,OC[-CB,6,remove C from position 0,flow_matching,0.3,2.0,30,97
48,replace,0.0,C,O,OC[-CB,CC[-CB,6,replace O at position 0 with C,flow_matching,0.3,2.0,30,97
49,replace,1.0,O,C,CC[-CB,CO[-CB,6,replace C at position 1 with O,flow_matching,0.3,2.0,30,97
50,replace,2.0,C,[,CO[-CB,COC-CB,6,replace [ at position 2 with C,flow_matching,0.3,2.0,30,97
51,replace,3.0,[,-,COC-CB,COC[CB,6,replace - at position 3 with [,flow_matching,0.3,2.0,30,97
52,replace,0.0,@,C,COC[CB,@OC[CB,6,replace C at position 0 with @,flow_matching,0.3,2.0,30,97
53,add,3.0,3,,@OC[CB,@OC3[CB,7,add 3 at position 3,flow_matching,0.3,2.0,30,97
54,remove,1.0,O,,@OC3[CB,@C3[CB,6,remove O from position 1,flow_matching,0.3,2.0,30,97
55,add,3.0,/,,@C3[CB,@C3/[CB,7,add / at position 3,flow_matching,0.3,2.0,30,97
56,replace,0.0,C,@,@C3/[CB,CC3/[CB,7,replace @ at position 0 with C,flow_matching,0.3,2.0,30,97
57,add,3.0,(,,CC3/[CB,CC3(/[CB,8,add ( at position 3,flow_matching,0.3,2.0,30,97
58,remove,0.0,C,,CC3(/[CB,C3(/[CB,7,remove C from position 0,flow_matching,0.3,2.0,30,97
59,remove,5.0,C,,C3(/[CB,C3(/[B,6,remove C from position 5,flow_matching,0.3,2.0,30,97
60,replace,2.0,1,(,C3(/[B,C31/[B,6,replace ( at position 2 with 1,flow_matching,0.3,2.0,30,97
61,replace,0.0,=,C,C31/[B,=31/[B,6,replace C at position 0 with =,flow_matching,0.3,2.0,30,97
62,replace,0.0,C,=,=31/[B,C31/[B,6,replace = at position 0 with C,flow_matching,0.3,2.0,30,97
63,remove,4.0,[,,C31/[B,C31/B,5,remove [ from position 4,flow_matching,0.3,2.0,30,97
64,remove,4.0,B,,C31/B,C31/,4,remove B from position 4,flow_matching,0.3,2.0,30,97
65,replace,3.0,(,/,C31/,C31(,4,replace / at position 3 with (,flow_matching,0.3,2.0,30,97
66,add,0.0,4,,C31(,4C31(,5,add 4 at position 0,flow_matching,0.3,2.0,30,97
67,add,5.0,F,,4C31(,4C31(F,6,add F at position 5,flow_matching,0.3,2.0,30,97
68,replace,0.0,C,4,4C31(F,CC31(F,6,replace 4 at position 0 with C,flow_matching,0.3,2.0,30,97
69,replace,1.0,O,C,CC31(F,CO31(F,6,replace C at position 1 with O,flow_matching,0.3,2.0,30,97
70,replace,2.0,C,3,CO31(F,COC1(F,6,replace 3 at position 2 with C,flow_matching,0.3,2.0,30,97
71,replace,3.0,[,1,COC1(F,COC[(F,6,replace 1 at position 3 with [,flow_matching,0.3,2.0,30,97
72,replace,4.0,C,(,COC[(F,COC[CF,6,replace ( at position 4 with C,flow_matching,0.3,2.0,30,97
73,replace,5.0,@,F,COC[CF,COC[C@,6,replace F at position 5 with @,flow_matching,0.3,2.0,30,97
74,add,6.0,H,,COC[C@,COC[C@H,7,add H at position 6,flow_matching,0.3,2.0,30,97
75,add,7.0,],,COC[C@H,COC[C@H],8,add ] at position 7,flow_matching,0.3,2.0,30,97
76,add,8.0,(,,COC[C@H],COC[C@H](,9,add ( at position 8,flow_matching,0.3,2.0,30,97
77,add,9.0,O,,COC[C@H](,COC[C@H](O,10,add O at position 9,flow_matching,0.3,2.0,30,97
78,add,10.0,),,COC[C@H](O,COC[C@H](O),11,add ) at position 10,flow_matching,0.3,2.0,30,97
79,add,11.0,C,,COC[C@H](O),COC[C@H](O)C,12,add C at position 11,flow_matching,0.3,2.0,30,97
80,add,12.0,[,,COC[C@H](O)C,COC[C@H](O)C[,13,add [ at position 12,flow_matching,0.3,2.0,30,97
81,add,13.0,N,,COC[C@H](O)C[,COC[C@H](O)C[N,14,add N at position 13,flow_matching,0.3,2.0,30,97
82,add,14.0,H,,COC[C@H](O)C[N,COC[C@H](O)C[NH,15,add H at position 14,flow_matching,0.3,2.0,30,97
83,add,15.0,+,,COC[C@H](O)C[NH,COC[C@H](O)C[NH+,16,add + at position 15,flow_matching,0.3,2.0,30,97
84,add,16.0,],,COC[C@H](O)C[NH+,COC[C@H](O)C[NH+],17,add ] at position 16,flow_matching,0.3,2.0,30,97
85,add,17.0,1,,COC[C@H](O)C[NH+],COC[C@H](O)C[NH+]1,18,add 1 at position 17,flow_matching,0.3,2.0,30,97
86,add,18.0,C,,COC[C@H](O)C[NH+]1,COC[C@H](O)C[NH+]1C,19,add C at position 18,flow_matching,0.3,2.0,30,97
87,add,19.0,C,,COC[C@H](O)C[NH+]1C,COC[C@H](O)C[NH+]1CC,20,add C at position 19,flow_matching,0.3,2.0,30,97
88,add,20.0,C,,COC[C@H](O)C[NH+]1CC,COC[C@H](O)C[NH+]1CCC,21,add C at position 20,flow_matching,0.3,2.0,30,97
89,add,21.0,(,,COC[C@H](O)C[NH+]1CCC,COC[C@H](O)C[NH+]1CCC(,22,add ( at position 21,flow_matching,0.3,2.0,30,97
90,add,22.0,C,,COC[C@H](O)C[NH+]1CCC(,COC[C@H](O)C[NH+]1CCC(C,23,add C at position 22,flow_matching,0.3,2.0,30,97
91,add,23.0,),,COC[C@H](O)C[NH+]1CCC(C,COC[C@H](O)C[NH+]1CCC(C),24,add ) at position 23,flow_matching,0.3,2.0,30,97
92,add,24.0,(,,COC[C@H](O)C[NH+]1CCC(C),COC[C@H](O)C[NH+]1CCC(C)(,25,add ( at position 24,flow_matching,0.3,2.0,30,97
93,add,25.0,C,,COC[C@H](O)C[NH+]1CCC(C)(,COC[C@H](O)C[NH+]1CCC(C)(C,26,add C at position 25,flow_matching,0.3,2.0,30,97
94,add,26.0,),,COC[C@H](O)C[NH+]1CCC(C)(C,COC[C@H](O)C[NH+]1CCC(C)(C),27,add ) at position 26,flow_matching,0.3,2.0,30,97
95,add,27.0,C,,COC[C@H](O)C[NH+]1CCC(C)(C),COC[C@H](O)C[NH+]1CCC(C)(C)C,28,add C at position 27,flow_matching,0.3,2.0,30,97
96,add,28.0,1,,COC[C@H](O)C[NH+]1CCC(C)(C)C,COC[C@H](O)C[NH+]1CCC(C)(C)C1,29,add 1 at position 28,flow_matching,0.3,2.0,30,97
97,add,29.0,"
",,COC[C@H](O)C[NH+]1CCC(C)(C)C1,"COC[C@H](O)C[NH+]1CCC(C)(C)C1
",30,"add 
 at position 29",flow_matching,0.3,2.0,30,97
