step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,38,114
1,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,38,114
2,add,1.0,C,,F,FC,2,add <PERSON> at position 1,flow_matching,0.3,2.0,38,114
3,replace,1.0,n,C,FC,Fn,2,replace <PERSON> at position 1 with n,flow_matching,0.3,2.0,38,114
4,add,1.0,/,,Fn,F/n,3,add / at position 1,flow_matching,0.3,2.0,38,114
5,replace,0.0,C,F,F/n,C/n,3,replace F at position 0 with C,flow_matching,0.3,2.0,38,114
6,replace,1.0,C,/,C/n,CCn,3,replace / at position 1 with C,flow_matching,0.3,2.0,38,114
7,replace,2.0,[,n,CCn,CC[,3,replace n at position 2 with [,flow_matching,0.3,2.0,38,114
8,add,0.0,5,,CC[,5CC[,4,add 5 at position 0,flow_matching,0.3,2.0,38,114
9,remove,3.0,[,,5CC[,5CC,3,remove [ from position 3,flow_matching,0.3,2.0,38,114
10,remove,2.0,C,,5CC,5C,2,remove C from position 2,flow_matching,0.3,2.0,38,114
11,remove,0.0,5,,5C,C,1,remove 5 from position 0,flow_matching,0.3,2.0,38,114
12,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,38,114
13,add,0.0,S,,CC,SCC,3,add S at position 0,flow_matching,0.3,2.0,38,114
14,replace,0.0,C,S,SCC,CCC,3,replace S at position 0 with C,flow_matching,0.3,2.0,38,114
15,replace,2.0,[,C,CCC,CC[,3,replace C at position 2 with [,flow_matching,0.3,2.0,38,114
16,remove,0.0,C,,CC[,C[,2,remove C from position 0,flow_matching,0.3,2.0,38,114
17,remove,1.0,[,,C[,C,1,remove [ from position 1,flow_matching,0.3,2.0,38,114
18,add,0.0,B,,C,BC,2,add B at position 0,flow_matching,0.3,2.0,38,114
19,remove,0.0,B,,BC,C,1,remove B from position 0,flow_matching,0.3,2.0,38,114
20,replace,0.0,6,C,C,6,1,replace C at position 0 with 6,flow_matching,0.3,2.0,38,114
21,add,1.0,O,,6,6O,2,add O at position 1,flow_matching,0.3,2.0,38,114
22,replace,0.0,C,6,6O,CO,2,replace 6 at position 0 with C,flow_matching,0.3,2.0,38,114
23,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,38,114
24,add,0.0,[,,O,[O,2,add [ at position 0,flow_matching,0.3,2.0,38,114
25,add,0.0,H,,[O,H[O,3,add H at position 0,flow_matching,0.3,2.0,38,114
26,add,1.0,H,,H[O,HH[O,4,add H at position 1,flow_matching,0.3,2.0,38,114
27,replace,0.0,C,H,HH[O,CH[O,4,replace H at position 0 with C,flow_matching,0.3,2.0,38,114
28,add,3.0,r,,CH[O,CH[rO,5,add r at position 3,flow_matching,0.3,2.0,38,114
29,replace,1.0,C,H,CH[rO,CC[rO,5,replace H at position 1 with C,flow_matching,0.3,2.0,38,114
30,replace,3.0,C,r,CC[rO,CC[CO,5,replace r at position 3 with C,flow_matching,0.3,2.0,38,114
31,remove,4.0,O,,CC[CO,CC[C,4,remove O from position 4,flow_matching,0.3,2.0,38,114
32,remove,0.0,C,,CC[C,C[C,3,remove C from position 0,flow_matching,0.3,2.0,38,114
33,remove,0.0,C,,C[C,[C,2,remove C from position 0,flow_matching,0.3,2.0,38,114
34,replace,0.0,C,[,[C,CC,2,replace [ at position 0 with C,flow_matching,0.3,2.0,38,114
35,replace,0.0,r,C,CC,rC,2,replace C at position 0 with r,flow_matching,0.3,2.0,38,114
36,remove,1.0,C,,rC,r,1,remove C from position 1,flow_matching,0.3,2.0,38,114
37,replace,0.0,],r,r,],1,replace r at position 0 with ],flow_matching,0.3,2.0,38,114
38,replace,0.0,7,],],7,1,replace ] at position 0 with 7,flow_matching,0.3,2.0,38,114
39,remove,0.0,7,,7,,0,remove 7 from position 0,flow_matching,0.3,2.0,38,114
40,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,38,114
41,add,1.0,B,,B,BB,2,add B at position 1,flow_matching,0.3,2.0,38,114
42,replace,0.0,I,B,BB,IB,2,replace B at position 0 with I,flow_matching,0.3,2.0,38,114
43,remove,0.0,I,,IB,B,1,remove I from position 0,flow_matching,0.3,2.0,38,114
44,replace,0.0,n,B,B,n,1,replace B at position 0 with n,flow_matching,0.3,2.0,38,114
45,replace,0.0,C,n,n,C,1,replace n at position 0 with C,flow_matching,0.3,2.0,38,114
46,add,1.0,F,,C,CF,2,add F at position 1,flow_matching,0.3,2.0,38,114
47,remove,1.0,F,,CF,C,1,remove F from position 1,flow_matching,0.3,2.0,38,114
48,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,38,114
49,add,2.0,I,,CC,CCI,3,add I at position 2,flow_matching,0.3,2.0,38,114
50,replace,2.0,[,I,CCI,CC[,3,replace I at position 2 with [,flow_matching,0.3,2.0,38,114
51,add,3.0,F,,CC[,CC[F,4,add F at position 3,flow_matching,0.3,2.0,38,114
52,replace,0.0,I,C,CC[F,IC[F,4,replace C at position 0 with I,flow_matching,0.3,2.0,38,114
53,remove,3.0,F,,IC[F,IC[,3,remove F from position 3,flow_matching,0.3,2.0,38,114
54,replace,0.0,C,I,IC[,CC[,3,replace I at position 0 with C,flow_matching,0.3,2.0,38,114
55,replace,2.0,#,[,CC[,CC#,3,replace [ at position 2 with #,flow_matching,0.3,2.0,38,114
56,replace,2.0,[,#,CC#,CC[,3,replace # at position 2 with [,flow_matching,0.3,2.0,38,114
57,replace,1.0,3,C,CC[,C3[,3,replace C at position 1 with 3,flow_matching,0.3,2.0,38,114
58,replace,1.0,C,3,C3[,CC[,3,replace 3 at position 1 with C,flow_matching,0.3,2.0,38,114
59,remove,0.0,C,,CC[,C[,2,remove C from position 0,flow_matching,0.3,2.0,38,114
60,add,1.0,N,,C[,CN[,3,add N at position 1,flow_matching,0.3,2.0,38,114
61,add,1.0,o,,CN[,CoN[,4,add o at position 1,flow_matching,0.3,2.0,38,114
62,replace,1.0,C,o,CoN[,CCN[,4,replace o at position 1 with C,flow_matching,0.3,2.0,38,114
63,add,3.0,-,,CCN[,CCN-[,5,add - at position 3,flow_matching,0.3,2.0,38,114
64,replace,2.0,[,N,CCN-[,CC[-[,5,replace N at position 2 with [,flow_matching,0.3,2.0,38,114
65,replace,3.0,C,-,CC[-[,CC[C[,5,replace - at position 3 with C,flow_matching,0.3,2.0,38,114
66,add,1.0,O,,CC[C[,COC[C[,6,add O at position 1,flow_matching,0.3,2.0,38,114
67,replace,1.0,C,O,COC[C[,CCC[C[,6,replace O at position 1 with C,flow_matching,0.3,2.0,38,114
68,add,0.0,@,,CCC[C[,@CCC[C[,7,add @ at position 0,flow_matching,0.3,2.0,38,114
69,remove,2.0,C,,@CCC[C[,@CC[C[,6,remove C from position 2,flow_matching,0.3,2.0,38,114
70,remove,2.0,C,,@CC[C[,@C[C[,5,remove C from position 2,flow_matching,0.3,2.0,38,114
71,remove,3.0,C,,@C[C[,@C[[,4,remove C from position 3,flow_matching,0.3,2.0,38,114
72,add,2.0,r,,@C[[,@Cr[[,5,add r at position 2,flow_matching,0.3,2.0,38,114
73,remove,1.0,C,,@Cr[[,@r[[,4,remove C from position 1,flow_matching,0.3,2.0,38,114
74,replace,2.0,4,[,@r[[,@r4[,4,replace [ at position 2 with 4,flow_matching,0.3,2.0,38,114
75,add,1.0,H,,@r4[,@Hr4[,5,add H at position 1,flow_matching,0.3,2.0,38,114
76,remove,2.0,r,,@Hr4[,@H4[,4,remove r from position 2,flow_matching,0.3,2.0,38,114
77,replace,0.0,C,@,@H4[,CH4[,4,replace @ at position 0 with C,flow_matching,0.3,2.0,38,114
78,replace,1.0,C,H,CH4[,CC4[,4,replace H at position 1 with C,flow_matching,0.3,2.0,38,114
79,replace,2.0,[,4,CC4[,CC[[,4,replace 4 at position 2 with [,flow_matching,0.3,2.0,38,114
80,replace,3.0,C,[,CC[[,CC[C,4,replace [ at position 3 with C,flow_matching,0.3,2.0,38,114
81,add,4.0,@,,CC[C,CC[C@,5,add @ at position 4,flow_matching,0.3,2.0,38,114
82,add,5.0,@,,CC[C@,CC[C@@,6,add @ at position 5,flow_matching,0.3,2.0,38,114
83,add,6.0,H,,CC[C@@,CC[C@@H,7,add H at position 6,flow_matching,0.3,2.0,38,114
84,add,7.0,],,CC[C@@H,CC[C@@H],8,add ] at position 7,flow_matching,0.3,2.0,38,114
85,add,8.0,1,,CC[C@@H],CC[C@@H]1,9,add 1 at position 8,flow_matching,0.3,2.0,38,114
86,add,9.0,C,,CC[C@@H]1,CC[C@@H]1C,10,add C at position 9,flow_matching,0.3,2.0,38,114
87,add,10.0,C,,CC[C@@H]1C,CC[C@@H]1CC,11,add C at position 10,flow_matching,0.3,2.0,38,114
88,add,11.0,C,,CC[C@@H]1CC,CC[C@@H]1CCC,12,add C at position 11,flow_matching,0.3,2.0,38,114
89,add,12.0,C,,CC[C@@H]1CCC,CC[C@@H]1CCCC,13,add C at position 12,flow_matching,0.3,2.0,38,114
90,add,13.0,N,,CC[C@@H]1CCCC,CC[C@@H]1CCCCN,14,add N at position 13,flow_matching,0.3,2.0,38,114
91,add,14.0,1,,CC[C@@H]1CCCCN,CC[C@@H]1CCCCN1,15,add 1 at position 14,flow_matching,0.3,2.0,38,114
92,add,15.0,C,,CC[C@@H]1CCCCN1,CC[C@@H]1CCCCN1C,16,add C at position 15,flow_matching,0.3,2.0,38,114
93,add,16.0,(,,CC[C@@H]1CCCCN1C,CC[C@@H]1CCCCN1C(,17,add ( at position 16,flow_matching,0.3,2.0,38,114
94,add,17.0,=,,CC[C@@H]1CCCCN1C(,CC[C@@H]1CCCCN1C(=,18,add = at position 17,flow_matching,0.3,2.0,38,114
95,add,18.0,S,,CC[C@@H]1CCCCN1C(=,CC[C@@H]1CCCCN1C(=S,19,add S at position 18,flow_matching,0.3,2.0,38,114
96,add,19.0,),,CC[C@@H]1CCCCN1C(=S,CC[C@@H]1CCCCN1C(=S),20,add ) at position 19,flow_matching,0.3,2.0,38,114
97,add,20.0,N,,CC[C@@H]1CCCCN1C(=S),CC[C@@H]1CCCCN1C(=S)N,21,add N at position 20,flow_matching,0.3,2.0,38,114
98,add,21.0,C,,CC[C@@H]1CCCCN1C(=S)N,CC[C@@H]1CCCCN1C(=S)NC,22,add C at position 21,flow_matching,0.3,2.0,38,114
99,add,22.0,(,,CC[C@@H]1CCCCN1C(=S)NC,CC[C@@H]1CCCCN1C(=S)NC(,23,add ( at position 22,flow_matching,0.3,2.0,38,114
100,add,23.0,=,,CC[C@@H]1CCCCN1C(=S)NC(,CC[C@@H]1CCCCN1C(=S)NC(=,24,add = at position 23,flow_matching,0.3,2.0,38,114
101,add,24.0,O,,CC[C@@H]1CCCCN1C(=S)NC(=,CC[C@@H]1CCCCN1C(=S)NC(=O,25,add O at position 24,flow_matching,0.3,2.0,38,114
102,add,25.0,),,CC[C@@H]1CCCCN1C(=S)NC(=O,CC[C@@H]1CCCCN1C(=S)NC(=O),26,add ) at position 25,flow_matching,0.3,2.0,38,114
103,add,26.0,c,,CC[C@@H]1CCCCN1C(=S)NC(=O),CC[C@@H]1CCCCN1C(=S)NC(=O)c,27,add c at position 26,flow_matching,0.3,2.0,38,114
104,add,27.0,1,,CC[C@@H]1CCCCN1C(=S)NC(=O)c,CC[C@@H]1CCCCN1C(=S)NC(=O)c1,28,add 1 at position 27,flow_matching,0.3,2.0,38,114
105,add,28.0,c,,CC[C@@H]1CCCCN1C(=S)NC(=O)c1,CC[C@@H]1CCCCN1C(=S)NC(=O)c1c,29,add c at position 28,flow_matching,0.3,2.0,38,114
106,add,29.0,c,,CC[C@@H]1CCCCN1C(=S)NC(=O)c1c,CC[C@@H]1CCCCN1C(=S)NC(=O)c1cc,30,add c at position 29,flow_matching,0.3,2.0,38,114
107,add,30.0,c,,CC[C@@H]1CCCCN1C(=S)NC(=O)c1cc,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc,31,add c at position 30,flow_matching,0.3,2.0,38,114
108,add,31.0,(,,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(,32,add ( at position 31,flow_matching,0.3,2.0,38,114
109,add,32.0,C,,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C,33,add C at position 32,flow_matching,0.3,2.0,38,114
110,add,33.0,),,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C),34,add ) at position 33,flow_matching,0.3,2.0,38,114
111,add,34.0,c,,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C),CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C)c,35,add c at position 34,flow_matching,0.3,2.0,38,114
112,add,35.0,c,,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C)c,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C)cc,36,add c at position 35,flow_matching,0.3,2.0,38,114
113,add,36.0,1,,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C)cc,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C)cc1,37,add 1 at position 36,flow_matching,0.3,2.0,38,114
114,add,37.0,"
",,CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C)cc1,"CC[C@@H]1CCCCN1C(=S)NC(=O)c1ccc(C)cc1
",38,"add 
 at position 37",flow_matching,0.3,2.0,38,114
