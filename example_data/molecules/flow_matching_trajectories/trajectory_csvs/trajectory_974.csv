step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,51,251
1,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,51,251
2,add,0.0,s,,6,s6,2,add s at position 0,flow_matching,0.3,2.0,51,251
3,replace,0.0,+,s,s6,+6,2,replace s at position 0 with +,flow_matching,0.3,2.0,51,251
4,replace,0.0,C,+,+6,C6,2,replace + at position 0 with C,flow_matching,0.3,2.0,51,251
5,remove,0.0,C,,C6,6,1,remove C from position 0,flow_matching,0.3,2.0,51,251
6,add,0.0,o,,6,o6,2,add o at position 0,flow_matching,0.3,2.0,51,251
7,remove,0.0,o,,o6,6,1,remove o from position 0,flow_matching,0.3,2.0,51,251
8,add,0.0,C,,6,C6,2,add C at position 0,flow_matching,0.3,2.0,51,251
9,replace,1.0,[,6,C6,C[,2,replace 6 at position 1 with [,flow_matching,0.3,2.0,51,251
10,add,0.0,[,,C[,[C[,3,add [ at position 0,flow_matching,0.3,2.0,51,251
11,remove,1.0,C,,[C[,[[,2,remove C from position 1,flow_matching,0.3,2.0,51,251
12,replace,0.0,C,[,[[,C[,2,replace [ at position 0 with C,flow_matching,0.3,2.0,51,251
13,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,51,251
14,remove,2.0,C,,C[C,C[,2,remove C from position 2,flow_matching,0.3,2.0,51,251
15,add,1.0,=,,C[,C=[,3,add = at position 1,flow_matching,0.3,2.0,51,251
16,add,1.0,7,,C=[,C7=[,4,add 7 at position 1,flow_matching,0.3,2.0,51,251
17,replace,1.0,[,7,C7=[,C[=[,4,replace 7 at position 1 with [,flow_matching,0.3,2.0,51,251
18,replace,0.0,+,C,C[=[,+[=[,4,replace C at position 0 with +,flow_matching,0.3,2.0,51,251
19,replace,0.0,C,+,+[=[,C[=[,4,replace + at position 0 with C,flow_matching,0.3,2.0,51,251
20,replace,2.0,C,=,C[=[,C[C[,4,replace = at position 2 with C,flow_matching,0.3,2.0,51,251
21,replace,3.0,@,[,C[C[,C[C@,4,replace [ at position 3 with @,flow_matching,0.3,2.0,51,251
22,replace,0.0,4,C,C[C@,4[C@,4,replace C at position 0 with 4,flow_matching,0.3,2.0,51,251
23,remove,2.0,C,,4[C@,4[@,3,remove C from position 2,flow_matching,0.3,2.0,51,251
24,remove,1.0,[,,4[@,4@,2,remove [ from position 1,flow_matching,0.3,2.0,51,251
25,remove,0.0,4,,4@,@,1,remove 4 from position 0,flow_matching,0.3,2.0,51,251
26,add,0.0,N,,@,N@,2,add N at position 0,flow_matching,0.3,2.0,51,251
27,remove,1.0,@,,N@,N,1,remove @ from position 1,flow_matching,0.3,2.0,51,251
28,replace,0.0,C,N,N,C,1,replace N at position 0 with C,flow_matching,0.3,2.0,51,251
29,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,51,251
30,remove,1.0,[,,C[,C,1,remove [ from position 1,flow_matching,0.3,2.0,51,251
31,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,51,251
32,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,51,251
33,remove,0.0,N,,N,,0,remove N from position 0,flow_matching,0.3,2.0,51,251
34,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,51,251
35,replace,0.0,),3,3,),1,replace 3 at position 0 with ),flow_matching,0.3,2.0,51,251
36,replace,0.0,C,),),C,1,replace ) at position 0 with C,flow_matching,0.3,2.0,51,251
37,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,51,251
38,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,51,251
39,replace,0.0,C,N,N,C,1,replace N at position 0 with C,flow_matching,0.3,2.0,51,251
40,add,1.0,/,,C,C/,2,add / at position 1,flow_matching,0.3,2.0,51,251
41,remove,0.0,C,,C/,/,1,remove C from position 0,flow_matching,0.3,2.0,51,251
42,remove,0.0,/,,/,,0,remove / from position 0,flow_matching,0.3,2.0,51,251
43,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,51,251
44,replace,0.0,C,3,3,C,1,replace 3 at position 0 with C,flow_matching,0.3,2.0,51,251
45,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,51,251
46,replace,0.0,2,C,C[,2[,2,replace C at position 0 with 2,flow_matching,0.3,2.0,51,251
47,replace,0.0,C,2,2[,C[,2,replace 2 at position 0 with C,flow_matching,0.3,2.0,51,251
48,add,1.0,\,,C[,C\[,3,add \ at position 1,flow_matching,0.3,2.0,51,251
49,replace,0.0,B,C,C\[,B\[,3,replace C at position 0 with B,flow_matching,0.3,2.0,51,251
50,replace,0.0,n,B,B\[,n\[,3,replace B at position 0 with n,flow_matching,0.3,2.0,51,251
51,add,1.0,2,,n\[,n2\[,4,add 2 at position 1,flow_matching,0.3,2.0,51,251
52,remove,0.0,n,,n2\[,2\[,3,remove n from position 0,flow_matching,0.3,2.0,51,251
53,add,0.0,3,,2\[,32\[,4,add 3 at position 0,flow_matching,0.3,2.0,51,251
54,replace,3.0,3,[,32\[,32\3,4,replace [ at position 3 with 3,flow_matching,0.3,2.0,51,251
55,replace,0.0,C,3,32\3,C2\3,4,replace 3 at position 0 with C,flow_matching,0.3,2.0,51,251
56,add,3.0,6,,C2\3,C2\63,5,add 6 at position 3,flow_matching,0.3,2.0,51,251
57,replace,1.0,[,2,C2\63,C[\63,5,replace 2 at position 1 with [,flow_matching,0.3,2.0,51,251
58,remove,2.0,\,,C[\63,C[63,4,remove \ from position 2,flow_matching,0.3,2.0,51,251
59,remove,1.0,[,,C[63,C63,3,remove [ from position 1,flow_matching,0.3,2.0,51,251
60,replace,1.0,[,6,C63,C[3,3,replace 6 at position 1 with [,flow_matching,0.3,2.0,51,251
61,remove,2.0,3,,C[3,C[,2,remove 3 from position 2,flow_matching,0.3,2.0,51,251
62,remove,0.0,C,,C[,[,1,remove C from position 0,flow_matching,0.3,2.0,51,251
63,add,1.0,6,,[,[6,2,add 6 at position 1,flow_matching,0.3,2.0,51,251
64,replace,0.0,C,[,[6,C6,2,replace [ at position 0 with C,flow_matching,0.3,2.0,51,251
65,add,0.0,2,,C6,2C6,3,add 2 at position 0,flow_matching,0.3,2.0,51,251
66,add,2.0,@,,2C6,2C@6,4,add @ at position 2,flow_matching,0.3,2.0,51,251
67,replace,0.0,C,2,2C@6,CC@6,4,replace 2 at position 0 with C,flow_matching,0.3,2.0,51,251
68,replace,1.0,[,C,CC@6,C[@6,4,replace C at position 1 with [,flow_matching,0.3,2.0,51,251
69,add,4.0,B,,C[@6,C[@6B,5,add B at position 4,flow_matching,0.3,2.0,51,251
70,replace,4.0,H,B,C[@6B,C[@6H,5,replace B at position 4 with H,flow_matching,0.3,2.0,51,251
71,remove,4.0,H,,C[@6H,C[@6,4,remove H from position 4,flow_matching,0.3,2.0,51,251
72,add,3.0,@,,C[@6,C[@@6,5,add @ at position 3,flow_matching,0.3,2.0,51,251
73,replace,2.0,+,@,C[@@6,C[+@6,5,replace @ at position 2 with +,flow_matching,0.3,2.0,51,251
74,replace,2.0,C,+,C[+@6,C[C@6,5,replace + at position 2 with C,flow_matching,0.3,2.0,51,251
75,replace,4.0,@,6,C[C@6,C[C@@,5,replace 6 at position 4 with @,flow_matching,0.3,2.0,51,251
76,remove,0.0,C,,C[C@@,[C@@,4,remove C from position 0,flow_matching,0.3,2.0,51,251
77,remove,1.0,C,,[C@@,[@@,3,remove C from position 1,flow_matching,0.3,2.0,51,251
78,replace,0.0,C,[,[@@,C@@,3,replace [ at position 0 with C,flow_matching,0.3,2.0,51,251
79,remove,0.0,C,,C@@,@@,2,remove C from position 0,flow_matching,0.3,2.0,51,251
80,replace,0.0,C,@,@@,C@,2,replace @ at position 0 with C,flow_matching,0.3,2.0,51,251
81,remove,0.0,C,,C@,@,1,remove C from position 0,flow_matching,0.3,2.0,51,251
82,remove,0.0,@,,@,,0,remove @ from position 0,flow_matching,0.3,2.0,51,251
83,add,0.0,H,,,H,1,add H at position 0,flow_matching,0.3,2.0,51,251
84,replace,0.0,C,H,H,C,1,replace H at position 0 with C,flow_matching,0.3,2.0,51,251
85,add,0.0,(,,C,(C,2,add ( at position 0,flow_matching,0.3,2.0,51,251
86,add,2.0,4,,(C,(C4,3,add 4 at position 2,flow_matching,0.3,2.0,51,251
87,remove,1.0,C,,(C4,(4,2,remove C from position 1,flow_matching,0.3,2.0,51,251
88,replace,0.0,C,(,(4,C4,2,replace ( at position 0 with C,flow_matching,0.3,2.0,51,251
89,add,0.0,),,C4,)C4,3,add ) at position 0,flow_matching,0.3,2.0,51,251
90,replace,0.0,C,),)C4,CC4,3,replace ) at position 0 with C,flow_matching,0.3,2.0,51,251
91,add,3.0,I,,CC4,CC4I,4,add I at position 3,flow_matching,0.3,2.0,51,251
92,replace,1.0,[,C,CC4I,C[4I,4,replace C at position 1 with [,flow_matching,0.3,2.0,51,251
93,replace,2.0,C,4,C[4I,C[CI,4,replace 4 at position 2 with C,flow_matching,0.3,2.0,51,251
94,add,4.0,N,,C[CI,C[CIN,5,add N at position 4,flow_matching,0.3,2.0,51,251
95,replace,2.0,5,C,C[CIN,C[5IN,5,replace C at position 2 with 5,flow_matching,0.3,2.0,51,251
96,replace,2.0,C,5,C[5IN,C[CIN,5,replace 5 at position 2 with C,flow_matching,0.3,2.0,51,251
97,replace,3.0,s,I,C[CIN,C[CsN,5,replace I at position 3 with s,flow_matching,0.3,2.0,51,251
98,replace,3.0,@,s,C[CsN,C[C@N,5,replace s at position 3 with @,flow_matching,0.3,2.0,51,251
99,add,5.0,N,,C[C@N,C[C@NN,6,add N at position 5,flow_matching,0.3,2.0,51,251
100,replace,4.0,@,N,C[C@NN,C[C@@N,6,replace N at position 4 with @,flow_matching,0.3,2.0,51,251
101,replace,5.0,],N,C[C@@N,C[C@@],6,replace N at position 5 with ],flow_matching,0.3,2.0,51,251
102,add,6.0,1,,C[C@@],C[C@@]1,7,add 1 at position 6,flow_matching,0.3,2.0,51,251
103,add,7.0,(,,C[C@@]1,C[C@@]1(,8,add ( at position 7,flow_matching,0.3,2.0,51,251
104,add,8.0,3,,C[C@@]1(,C[C@@]1(3,9,add 3 at position 8,flow_matching,0.3,2.0,51,251
105,add,9.0,2,,C[C@@]1(3,C[C@@]1(32,10,add 2 at position 9,flow_matching,0.3,2.0,51,251
106,remove,3.0,@,,C[C@@]1(32,C[C@]1(32,9,remove @ from position 3,flow_matching,0.3,2.0,51,251
107,add,7.0,@,,C[C@]1(32,C[C@]1(@32,10,add @ at position 7,flow_matching,0.3,2.0,51,251
108,replace,4.0,@,],C[C@]1(@32,C[C@@1(@32,10,replace ] at position 4 with @,flow_matching,0.3,2.0,51,251
109,replace,5.0,],1,C[C@@1(@32,C[C@@](@32,10,replace 1 at position 5 with ],flow_matching,0.3,2.0,51,251
110,replace,8.0,7,3,C[C@@](@32,C[C@@](@72,10,replace 3 at position 8 with 7,flow_matching,0.3,2.0,51,251
111,add,2.0,(,,C[C@@](@72,C[(C@@](@72,11,add ( at position 2,flow_matching,0.3,2.0,51,251
112,add,1.0,),,C[(C@@](@72,C)[(C@@](@72,12,add ) at position 1,flow_matching,0.3,2.0,51,251
113,replace,1.0,[,),C)[(C@@](@72,C[[(C@@](@72,12,replace ) at position 1 with [,flow_matching,0.3,2.0,51,251
114,replace,2.0,C,[,C[[(C@@](@72,C[C(C@@](@72,12,replace [ at position 2 with C,flow_matching,0.3,2.0,51,251
115,replace,3.0,@,(,C[C(C@@](@72,C[C@C@@](@72,12,replace ( at position 3 with @,flow_matching,0.3,2.0,51,251
116,replace,2.0,H,C,C[C@C@@](@72,C[H@C@@](@72,12,replace C at position 2 with H,flow_matching,0.3,2.0,51,251
117,replace,2.0,C,H,C[H@C@@](@72,C[C@C@@](@72,12,replace H at position 2 with C,flow_matching,0.3,2.0,51,251
118,replace,4.0,@,C,C[C@C@@](@72,C[C@@@@](@72,12,replace C at position 4 with @,flow_matching,0.3,2.0,51,251
119,add,0.0,2,,C[C@@@@](@72,2C[C@@@@](@72,13,add 2 at position 0,flow_matching,0.3,2.0,51,251
120,remove,5.0,@,,2C[C@@@@](@72,2C[C@@@](@72,12,remove @ from position 5,flow_matching,0.3,2.0,51,251
121,remove,1.0,C,,2C[C@@@](@72,2[C@@@](@72,11,remove C from position 1,flow_matching,0.3,2.0,51,251
122,replace,6.0,@,],2[C@@@](@72,2[C@@@@(@72,11,replace ] at position 6 with @,flow_matching,0.3,2.0,51,251
123,replace,0.0,C,2,2[C@@@@(@72,C[C@@@@(@72,11,replace 2 at position 0 with C,flow_matching,0.3,2.0,51,251
124,remove,3.0,@,,C[C@@@@(@72,C[C@@@(@72,10,remove @ from position 3,flow_matching,0.3,2.0,51,251
125,replace,5.0,],@,C[C@@@(@72,C[C@@](@72,10,replace @ at position 5 with ],flow_matching,0.3,2.0,51,251
126,replace,5.0,c,],C[C@@](@72,C[C@@c(@72,10,replace ] at position 5 with c,flow_matching,0.3,2.0,51,251
127,add,0.0,=,,C[C@@c(@72,=C[C@@c(@72,11,add = at position 0,flow_matching,0.3,2.0,51,251
128,replace,4.0,2,@,=C[C@@c(@72,=C[C2@c(@72,11,replace @ at position 4 with 2,flow_matching,0.3,2.0,51,251
129,replace,0.0,C,=,=C[C2@c(@72,CC[C2@c(@72,11,replace = at position 0 with C,flow_matching,0.3,2.0,51,251
130,remove,5.0,@,,CC[C2@c(@72,CC[C2c(@72,10,remove @ from position 5,flow_matching,0.3,2.0,51,251
131,add,0.0,B,,CC[C2c(@72,BCC[C2c(@72,11,add B at position 0,flow_matching,0.3,2.0,51,251
132,add,1.0,[,,BCC[C2c(@72,B[CC[C2c(@72,12,add [ at position 1,flow_matching,0.3,2.0,51,251
133,remove,1.0,[,,B[CC[C2c(@72,BCC[C2c(@72,11,remove [ from position 1,flow_matching,0.3,2.0,51,251
134,replace,0.0,C,B,BCC[C2c(@72,CCC[C2c(@72,11,replace B at position 0 with C,flow_matching,0.3,2.0,51,251
135,add,0.0,3,,CCC[C2c(@72,3CCC[C2c(@72,12,add 3 at position 0,flow_matching,0.3,2.0,51,251
136,replace,10.0,-,7,3CCC[C2c(@72,3CCC[C2c(@-2,12,replace 7 at position 10 with -,flow_matching,0.3,2.0,51,251
137,add,4.0,H,,3CCC[C2c(@-2,3CCCH[C2c(@-2,13,add H at position 4,flow_matching,0.3,2.0,51,251
138,add,10.0,-,,3CCCH[C2c(@-2,3CCCH[C2c(-@-2,14,add - at position 10,flow_matching,0.3,2.0,51,251
139,remove,0.0,3,,3CCCH[C2c(-@-2,CCCH[C2c(-@-2,13,remove 3 from position 0,flow_matching,0.3,2.0,51,251
140,replace,1.0,[,C,CCCH[C2c(-@-2,C[CH[C2c(-@-2,13,replace C at position 1 with [,flow_matching,0.3,2.0,51,251
141,replace,1.0,n,[,C[CH[C2c(-@-2,CnCH[C2c(-@-2,13,replace [ at position 1 with n,flow_matching,0.3,2.0,51,251
142,replace,8.0,@,(,CnCH[C2c(-@-2,CnCH[C2c@-@-2,13,replace ( at position 8 with @,flow_matching,0.3,2.0,51,251
143,replace,1.0,[,n,CnCH[C2c@-@-2,C[CH[C2c@-@-2,13,replace n at position 1 with [,flow_matching,0.3,2.0,51,251
144,replace,2.0,n,C,C[CH[C2c@-@-2,C[nH[C2c@-@-2,13,replace C at position 2 with n,flow_matching,0.3,2.0,51,251
145,replace,2.0,C,n,C[nH[C2c@-@-2,C[CH[C2c@-@-2,13,replace n at position 2 with C,flow_matching,0.3,2.0,51,251
146,replace,3.0,@,H,C[CH[C2c@-@-2,C[C@[C2c@-@-2,13,replace H at position 3 with @,flow_matching,0.3,2.0,51,251
147,add,13.0,-,,C[C@[C2c@-@-2,C[C@[C2c@-@-2-,14,add - at position 13,flow_matching,0.3,2.0,51,251
148,remove,4.0,[,,C[C@[C2c@-@-2-,C[C@C2c@-@-2-,13,remove [ from position 4,flow_matching,0.3,2.0,51,251
149,remove,9.0,@,,C[C@C2c@-@-2-,C[C@C2c@--2-,12,remove @ from position 9,flow_matching,0.3,2.0,51,251
150,replace,7.0,(,@,C[C@C2c@--2-,C[C@C2c(--2-,12,replace @ at position 7 with (,flow_matching,0.3,2.0,51,251
151,replace,8.0,o,-,C[C@C2c(--2-,C[C@C2c(o-2-,12,replace - at position 8 with o,flow_matching,0.3,2.0,51,251
152,replace,4.0,@,C,C[C@C2c(o-2-,C[C@@2c(o-2-,12,replace C at position 4 with @,flow_matching,0.3,2.0,51,251
153,replace,5.0,],2,C[C@@2c(o-2-,C[C@@]c(o-2-,12,replace 2 at position 5 with ],flow_matching,0.3,2.0,51,251
154,remove,2.0,C,,C[C@@]c(o-2-,C[@@]c(o-2-,11,remove C from position 2,flow_matching,0.3,2.0,51,251
155,replace,2.0,C,@,C[@@]c(o-2-,C[C@]c(o-2-,11,replace @ at position 2 with C,flow_matching,0.3,2.0,51,251
156,replace,4.0,@,],C[C@]c(o-2-,C[C@@c(o-2-,11,replace ] at position 4 with @,flow_matching,0.3,2.0,51,251
157,replace,5.0,],c,C[C@@c(o-2-,C[C@@](o-2-,11,replace c at position 5 with ],flow_matching,0.3,2.0,51,251
158,add,7.0,-,,C[C@@](o-2-,C[C@@](-o-2-,12,add - at position 7,flow_matching,0.3,2.0,51,251
159,replace,3.0,4,@,C[C@@](-o-2-,C[C4@](-o-2-,12,replace @ at position 3 with 4,flow_matching,0.3,2.0,51,251
160,remove,5.0,],,C[C4@](-o-2-,C[C4@(-o-2-,11,remove ] from position 5,flow_matching,0.3,2.0,51,251
161,replace,3.0,@,4,C[C4@(-o-2-,C[C@@(-o-2-,11,replace 4 at position 3 with @,flow_matching,0.3,2.0,51,251
162,add,3.0,),,C[C@@(-o-2-,C[C)@@(-o-2-,12,add ) at position 3,flow_matching,0.3,2.0,51,251
163,replace,3.0,@,),C[C)@@(-o-2-,C[C@@@(-o-2-,12,replace ) at position 3 with @,flow_matching,0.3,2.0,51,251
164,replace,6.0,=,(,C[C@@@(-o-2-,C[C@@@=-o-2-,12,replace ( at position 6 with =,flow_matching,0.3,2.0,51,251
165,add,6.0,@,,C[C@@@=-o-2-,C[C@@@@=-o-2-,13,add @ at position 6,flow_matching,0.3,2.0,51,251
166,replace,5.0,],@,C[C@@@@=-o-2-,C[C@@]@=-o-2-,13,replace @ at position 5 with ],flow_matching,0.3,2.0,51,251
167,replace,6.0,1,@,C[C@@]@=-o-2-,C[C@@]1=-o-2-,13,replace @ at position 6 with 1,flow_matching,0.3,2.0,51,251
168,add,9.0,B,,C[C@@]1=-o-2-,C[C@@]1=-Bo-2-,14,add B at position 9,flow_matching,0.3,2.0,51,251
169,add,13.0,I,,C[C@@]1=-Bo-2-,C[C@@]1=-Bo-2I-,15,add I at position 13,flow_matching,0.3,2.0,51,251
170,remove,6.0,1,,C[C@@]1=-Bo-2I-,C[C@@]=-Bo-2I-,14,remove 1 from position 6,flow_matching,0.3,2.0,51,251
171,remove,3.0,@,,C[C@@]=-Bo-2I-,C[C@]=-Bo-2I-,13,remove @ from position 3,flow_matching,0.3,2.0,51,251
172,replace,10.0,),2,C[C@]=-Bo-2I-,C[C@]=-Bo-)I-,13,replace 2 at position 10 with ),flow_matching,0.3,2.0,51,251
173,replace,3.0,o,@,C[C@]=-Bo-)I-,C[Co]=-Bo-)I-,13,replace @ at position 3 with o,flow_matching,0.3,2.0,51,251
174,replace,6.0,5,-,C[Co]=-Bo-)I-,C[Co]=5Bo-)I-,13,replace - at position 6 with 5,flow_matching,0.3,2.0,51,251
175,add,9.0,r,,C[Co]=5Bo-)I-,C[Co]=5Bor-)I-,14,add r at position 9,flow_matching,0.3,2.0,51,251
176,replace,3.0,@,o,C[Co]=5Bor-)I-,C[C@]=5Bor-)I-,14,replace o at position 3 with @,flow_matching,0.3,2.0,51,251
177,replace,4.0,@,],C[C@]=5Bor-)I-,C[C@@=5Bor-)I-,14,replace ] at position 4 with @,flow_matching,0.3,2.0,51,251
178,replace,4.0,2,@,C[C@@=5Bor-)I-,C[C@2=5Bor-)I-,14,replace @ at position 4 with 2,flow_matching,0.3,2.0,51,251
179,replace,4.0,@,2,C[C@2=5Bor-)I-,C[C@@=5Bor-)I-,14,replace 2 at position 4 with @,flow_matching,0.3,2.0,51,251
180,add,3.0,4,,C[C@@=5Bor-)I-,C[C4@@=5Bor-)I-,15,add 4 at position 3,flow_matching,0.3,2.0,51,251
181,add,12.0,-,,C[C4@@=5Bor-)I-,C[C4@@=5Bor--)I-,16,add - at position 12,flow_matching,0.3,2.0,51,251
182,replace,3.0,@,4,C[C4@@=5Bor--)I-,C[C@@@=5Bor--)I-,16,replace 4 at position 3 with @,flow_matching,0.3,2.0,51,251
183,replace,5.0,],@,C[C@@@=5Bor--)I-,C[C@@]=5Bor--)I-,16,replace @ at position 5 with ],flow_matching,0.3,2.0,51,251
184,add,1.0,l,,C[C@@]=5Bor--)I-,Cl[C@@]=5Bor--)I-,17,add l at position 1,flow_matching,0.3,2.0,51,251
185,add,17.0,1,,Cl[C@@]=5Bor--)I-,Cl[C@@]=5Bor--)I-1,18,add 1 at position 17,flow_matching,0.3,2.0,51,251
186,add,17.0,o,,Cl[C@@]=5Bor--)I-1,Cl[C@@]=5Bor--)I-o1,19,add o at position 17,flow_matching,0.3,2.0,51,251
187,add,6.0,[,,Cl[C@@]=5Bor--)I-o1,Cl[C@@[]=5Bor--)I-o1,20,add [ at position 6,flow_matching,0.3,2.0,51,251
188,add,4.0,S,,Cl[C@@[]=5Bor--)I-o1,Cl[CS@@[]=5Bor--)I-o1,21,add S at position 4,flow_matching,0.3,2.0,51,251
189,replace,1.0,[,l,Cl[CS@@[]=5Bor--)I-o1,C[[CS@@[]=5Bor--)I-o1,21,replace l at position 1 with [,flow_matching,0.3,2.0,51,251
190,add,20.0,7,,C[[CS@@[]=5Bor--)I-o1,C[[CS@@[]=5Bor--)I-o71,22,add 7 at position 20,flow_matching,0.3,2.0,51,251
191,replace,2.0,C,[,C[[CS@@[]=5Bor--)I-o71,C[CCS@@[]=5Bor--)I-o71,22,replace [ at position 2 with C,flow_matching,0.3,2.0,51,251
192,add,19.0,s,,C[CCS@@[]=5Bor--)I-o71,C[CCS@@[]=5Bor--)I-so71,23,add s at position 19,flow_matching,0.3,2.0,51,251
193,add,2.0,O,,C[CCS@@[]=5Bor--)I-so71,C[OCCS@@[]=5Bor--)I-so71,24,add O at position 2,flow_matching,0.3,2.0,51,251
194,replace,10.0,B,=,C[OCCS@@[]=5Bor--)I-so71,C[OCCS@@[]B5Bor--)I-so71,24,replace = at position 10 with B,flow_matching,0.3,2.0,51,251
195,replace,8.0,o,[,C[OCCS@@[]B5Bor--)I-so71,C[OCCS@@o]B5Bor--)I-so71,24,replace [ at position 8 with o,flow_matching,0.3,2.0,51,251
196,remove,14.0,r,,C[OCCS@@o]B5Bor--)I-so71,C[OCCS@@o]B5Bo--)I-so71,23,remove r from position 14,flow_matching,0.3,2.0,51,251
197,replace,2.0,C,O,C[OCCS@@o]B5Bo--)I-so71,C[CCCS@@o]B5Bo--)I-so71,23,replace O at position 2 with C,flow_matching,0.3,2.0,51,251
198,replace,11.0,O,5,C[CCCS@@o]B5Bo--)I-so71,C[CCCS@@o]BOBo--)I-so71,23,replace 5 at position 11 with O,flow_matching,0.3,2.0,51,251
199,replace,3.0,@,C,C[CCCS@@o]BOBo--)I-so71,C[C@CS@@o]BOBo--)I-so71,23,replace C at position 3 with @,flow_matching,0.3,2.0,51,251
200,remove,17.0,I,,C[C@CS@@o]BOBo--)I-so71,C[C@CS@@o]BOBo--)-so71,22,remove I from position 17,flow_matching,0.3,2.0,51,251
201,remove,9.0,],,C[C@CS@@o]BOBo--)-so71,C[C@CS@@oBOBo--)-so71,21,remove ] from position 9,flow_matching,0.3,2.0,51,251
202,replace,4.0,@,C,C[C@CS@@oBOBo--)-so71,C[C@@S@@oBOBo--)-so71,21,replace C at position 4 with @,flow_matching,0.3,2.0,51,251
203,add,2.0,@,,C[C@@S@@oBOBo--)-so71,C[@C@@S@@oBOBo--)-so71,22,add @ at position 2,flow_matching,0.3,2.0,51,251
204,replace,2.0,C,@,C[@C@@S@@oBOBo--)-so71,C[CC@@S@@oBOBo--)-so71,22,replace @ at position 2 with C,flow_matching,0.3,2.0,51,251
205,replace,3.0,@,C,C[CC@@S@@oBOBo--)-so71,C[C@@@S@@oBOBo--)-so71,22,replace C at position 3 with @,flow_matching,0.3,2.0,51,251
206,replace,5.0,],@,C[C@@@S@@oBOBo--)-so71,C[C@@]S@@oBOBo--)-so71,22,replace @ at position 5 with ],flow_matching,0.3,2.0,51,251
207,replace,6.0,1,S,C[C@@]S@@oBOBo--)-so71,C[C@@]1@@oBOBo--)-so71,22,replace S at position 6 with 1,flow_matching,0.3,2.0,51,251
208,replace,7.0,(,@,C[C@@]1@@oBOBo--)-so71,C[C@@]1(@oBOBo--)-so71,22,replace @ at position 7 with (,flow_matching,0.3,2.0,51,251
209,replace,8.0,C,@,C[C@@]1(@oBOBo--)-so71,C[C@@]1(CoBOBo--)-so71,22,replace @ at position 8 with C,flow_matching,0.3,2.0,51,251
210,replace,9.0,c,o,C[C@@]1(CoBOBo--)-so71,C[C@@]1(CcBOBo--)-so71,22,replace o at position 9 with c,flow_matching,0.3,2.0,51,251
211,replace,10.0,2,B,C[C@@]1(CcBOBo--)-so71,C[C@@]1(Cc2OBo--)-so71,22,replace B at position 10 with 2,flow_matching,0.3,2.0,51,251
212,replace,11.0,c,O,C[C@@]1(Cc2OBo--)-so71,C[C@@]1(Cc2cBo--)-so71,22,replace O at position 11 with c,flow_matching,0.3,2.0,51,251
213,replace,12.0,c,B,C[C@@]1(Cc2cBo--)-so71,C[C@@]1(Cc2cco--)-so71,22,replace B at position 12 with c,flow_matching,0.3,2.0,51,251
214,replace,13.0,c,o,C[C@@]1(Cc2cco--)-so71,C[C@@]1(Cc2ccc--)-so71,22,replace o at position 13 with c,flow_matching,0.3,2.0,51,251
215,replace,14.0,3,-,C[C@@]1(Cc2ccc--)-so71,C[C@@]1(Cc2ccc3-)-so71,22,replace - at position 14 with 3,flow_matching,0.3,2.0,51,251
216,replace,15.0,c,-,C[C@@]1(Cc2ccc3-)-so71,C[C@@]1(Cc2ccc3c)-so71,22,replace - at position 15 with c,flow_matching,0.3,2.0,51,251
217,replace,16.0,(,),C[C@@]1(Cc2ccc3c)-so71,C[C@@]1(Cc2ccc3c(-so71,22,replace ) at position 16 with (,flow_matching,0.3,2.0,51,251
218,replace,17.0,c,-,C[C@@]1(Cc2ccc3c(-so71,C[C@@]1(Cc2ccc3c(cso71,22,replace - at position 17 with c,flow_matching,0.3,2.0,51,251
219,replace,18.0,2,s,C[C@@]1(Cc2ccc3c(cso71,C[C@@]1(Cc2ccc3c(c2o71,22,replace s at position 18 with 2,flow_matching,0.3,2.0,51,251
220,replace,19.0,),o,C[C@@]1(Cc2ccc3c(c2o71,C[C@@]1(Cc2ccc3c(c2)71,22,replace o at position 19 with ),flow_matching,0.3,2.0,51,251
221,replace,20.0,O,7,C[C@@]1(Cc2ccc3c(c2)71,C[C@@]1(Cc2ccc3c(c2)O1,22,replace 7 at position 20 with O,flow_matching,0.3,2.0,51,251
222,replace,21.0,C,1,C[C@@]1(Cc2ccc3c(c2)O1,C[C@@]1(Cc2ccc3c(c2)OC,22,replace 1 at position 21 with C,flow_matching,0.3,2.0,51,251
223,add,22.0,O,,C[C@@]1(Cc2ccc3c(c2)OC,C[C@@]1(Cc2ccc3c(c2)OCO,23,add O at position 22,flow_matching,0.3,2.0,51,251
224,add,23.0,3,,C[C@@]1(Cc2ccc3c(c2)OCO,C[C@@]1(Cc2ccc3c(c2)OCO3,24,add 3 at position 23,flow_matching,0.3,2.0,51,251
225,add,24.0,),,C[C@@]1(Cc2ccc3c(c2)OCO3,C[C@@]1(Cc2ccc3c(c2)OCO3),25,add ) at position 24,flow_matching,0.3,2.0,51,251
226,add,25.0,C,,C[C@@]1(Cc2ccc3c(c2)OCO3),C[C@@]1(Cc2ccc3c(c2)OCO3)C,26,add C at position 25,flow_matching,0.3,2.0,51,251
227,add,26.0,C,,C[C@@]1(Cc2ccc3c(c2)OCO3)C,C[C@@]1(Cc2ccc3c(c2)OCO3)CC,27,add C at position 26,flow_matching,0.3,2.0,51,251
228,add,27.0,C,,C[C@@]1(Cc2ccc3c(c2)OCO3)CC,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC,28,add C at position 27,flow_matching,0.3,2.0,51,251
229,add,28.0,(,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(,29,add ( at position 28,flow_matching,0.3,2.0,51,251
230,add,29.0,=,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=,30,add = at position 29,flow_matching,0.3,2.0,51,251
231,add,30.0,O,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O,31,add O at position 30,flow_matching,0.3,2.0,51,251
232,add,31.0,),,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O),32,add ) at position 31,flow_matching,0.3,2.0,51,251
233,add,32.0,N,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O),C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N,33,add N at position 32,flow_matching,0.3,2.0,51,251
234,add,33.0,(,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(,34,add ( at position 33,flow_matching,0.3,2.0,51,251
235,add,34.0,C,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(C,35,add C at position 34,flow_matching,0.3,2.0,51,251
236,add,35.0,C,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(C,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CC,36,add C at position 35,flow_matching,0.3,2.0,51,251
237,add,36.0,c,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CC,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc,37,add c at position 36,flow_matching,0.3,2.0,51,251
238,add,37.0,2,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2,38,add 2 at position 37,flow_matching,0.3,2.0,51,251
239,add,38.0,c,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2c,39,add c at position 38,flow_matching,0.3,2.0,51,251
240,add,39.0,c,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2c,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2cc,40,add c at position 39,flow_matching,0.3,2.0,51,251
241,add,40.0,c,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2cc,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc,41,add c at position 40,flow_matching,0.3,2.0,51,251
242,add,41.0,(,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(,42,add ( at position 41,flow_matching,0.3,2.0,51,251
243,add,42.0,O,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O,43,add O at position 42,flow_matching,0.3,2.0,51,251
244,add,43.0,),,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O),44,add ) at position 43,flow_matching,0.3,2.0,51,251
245,add,44.0,c,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O),C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)c,45,add c at position 44,flow_matching,0.3,2.0,51,251
246,add,45.0,c,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)c,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc,46,add c at position 45,flow_matching,0.3,2.0,51,251
247,add,46.0,2,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc2,47,add 2 at position 46,flow_matching,0.3,2.0,51,251
248,add,47.0,),,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc2,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc2),48,add ) at position 47,flow_matching,0.3,2.0,51,251
249,add,48.0,C,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc2),C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc2)C,49,add C at position 48,flow_matching,0.3,2.0,51,251
250,add,49.0,1,,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc2)C,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc2)C1,50,add 1 at position 49,flow_matching,0.3,2.0,51,251
251,add,50.0,"
",,C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc2)C1,"C[C@@]1(Cc2ccc3c(c2)OCO3)CCC(=O)N(CCc2ccc(O)cc2)C1
",51,"add 
 at position 50",flow_matching,0.3,2.0,51,251
