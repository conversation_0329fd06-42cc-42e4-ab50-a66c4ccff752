step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,44,131
1,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,44,131
2,remove,0.0,N,,N,,0,remove N from position 0,flow_matching,0.3,2.0,44,131
3,add,0.0,1,,,1,1,add 1 at position 0,flow_matching,0.3,2.0,44,131
4,replace,0.0,C,1,1,C,1,replace 1 at position 0 with C,flow_matching,0.3,2.0,44,131
5,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,44,131
6,remove,1.0,C,,CC,C,1,remove <PERSON> from position 1,flow_matching,0.3,2.0,44,131
7,add,0.0,@,,C,@C,2,add @ at position 0,flow_matching,0.3,2.0,44,131
8,add,1.0,5,,@C,@5C,3,add 5 at position 1,flow_matching,0.3,2.0,44,131
9,replace,0.0,C,@,@5C,C5C,3,replace @ at position 0 with C,flow_matching,0.3,2.0,44,131
10,remove,1.0,5,,C5C,CC,2,remove 5 from position 1,flow_matching,0.3,2.0,44,131
11,add,1.0,(,,CC,C(C,3,add ( at position 1,flow_matching,0.3,2.0,44,131
12,replace,0.0,3,C,C(C,3(C,3,replace C at position 0 with 3,flow_matching,0.3,2.0,44,131
13,replace,0.0,C,3,3(C,C(C,3,replace 3 at position 0 with C,flow_matching,0.3,2.0,44,131
14,add,3.0,],,C(C,C(C],4,add ] at position 3,flow_matching,0.3,2.0,44,131
15,replace,0.0,3,C,C(C],3(C],4,replace C at position 0 with 3,flow_matching,0.3,2.0,44,131
16,replace,0.0,C,3,3(C],C(C],4,replace 3 at position 0 with C,flow_matching,0.3,2.0,44,131
17,replace,1.0,C,(,C(C],CCC],4,replace ( at position 1 with C,flow_matching,0.3,2.0,44,131
18,add,2.0,5,,CCC],CC5C],5,add 5 at position 2,flow_matching,0.3,2.0,44,131
19,replace,2.0,(,5,CC5C],CC(C],5,replace 5 at position 2 with (,flow_matching,0.3,2.0,44,131
20,replace,0.0,],C,CC(C],]C(C],5,replace C at position 0 with ],flow_matching,0.3,2.0,44,131
21,remove,3.0,C,,]C(C],]C(],4,remove C from position 3,flow_matching,0.3,2.0,44,131
22,add,3.0,-,,]C(],]C(-],5,add - at position 3,flow_matching,0.3,2.0,44,131
23,remove,4.0,],,]C(-],]C(-,4,remove ] from position 4,flow_matching,0.3,2.0,44,131
24,replace,0.0,C,],]C(-,CC(-,4,replace ] at position 0 with C,flow_matching,0.3,2.0,44,131
25,remove,3.0,-,,CC(-,CC(,3,remove - from position 3,flow_matching,0.3,2.0,44,131
26,add,1.0,(,,CC(,C(C(,4,add ( at position 1,flow_matching,0.3,2.0,44,131
27,replace,3.0,2,(,C(C(,C(C2,4,replace ( at position 3 with 2,flow_matching,0.3,2.0,44,131
28,remove,2.0,C,,C(C2,C(2,3,remove C from position 2,flow_matching,0.3,2.0,44,131
29,replace,1.0,-,(,C(2,C-2,3,replace ( at position 1 with -,flow_matching,0.3,2.0,44,131
30,add,1.0,[,,C-2,C[-2,4,add [ at position 1,flow_matching,0.3,2.0,44,131
31,replace,1.0,C,[,C[-2,CC-2,4,replace [ at position 1 with C,flow_matching,0.3,2.0,44,131
32,remove,2.0,-,,CC-2,CC2,3,remove - from position 2,flow_matching,0.3,2.0,44,131
33,replace,2.0,(,2,CC2,CC(,3,replace 2 at position 2 with (,flow_matching,0.3,2.0,44,131
34,add,3.0,C,,CC(,CC(C,4,add C at position 3,flow_matching,0.3,2.0,44,131
35,remove,0.0,C,,CC(C,C(C,3,remove C from position 0,flow_matching,0.3,2.0,44,131
36,remove,2.0,C,,C(C,C(,2,remove C from position 2,flow_matching,0.3,2.0,44,131
37,replace,1.0,C,(,C(,CC,2,replace ( at position 1 with C,flow_matching,0.3,2.0,44,131
38,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,44,131
39,replace,1.0,),C,CC(,C)(,3,replace C at position 1 with ),flow_matching,0.3,2.0,44,131
40,replace,1.0,C,),C)(,CC(,3,replace ) at position 1 with C,flow_matching,0.3,2.0,44,131
41,replace,1.0,c,C,CC(,Cc(,3,replace C at position 1 with c,flow_matching,0.3,2.0,44,131
42,remove,0.0,C,,Cc(,c(,2,remove C from position 0,flow_matching,0.3,2.0,44,131
43,replace,0.0,C,c,c(,C(,2,replace c at position 0 with C,flow_matching,0.3,2.0,44,131
44,add,1.0,/,,C(,C/(,3,add / at position 1,flow_matching,0.3,2.0,44,131
45,replace,1.0,C,/,C/(,CC(,3,replace / at position 1 with C,flow_matching,0.3,2.0,44,131
46,add,2.0,+,,CC(,CC+(,4,add + at position 2,flow_matching,0.3,2.0,44,131
47,replace,3.0,#,(,CC+(,CC+#,4,replace ( at position 3 with #,flow_matching,0.3,2.0,44,131
48,replace,2.0,(,+,CC+#,CC(#,4,replace + at position 2 with (,flow_matching,0.3,2.0,44,131
49,remove,1.0,C,,CC(#,C(#,3,remove C from position 1,flow_matching,0.3,2.0,44,131
50,add,3.0,-,,C(#,C(#-,4,add - at position 3,flow_matching,0.3,2.0,44,131
51,add,4.0,O,,C(#-,C(#-O,5,add O at position 4,flow_matching,0.3,2.0,44,131
52,replace,3.0,l,-,C(#-O,C(#lO,5,replace - at position 3 with l,flow_matching,0.3,2.0,44,131
53,replace,1.0,C,(,C(#lO,CC#lO,5,replace ( at position 1 with C,flow_matching,0.3,2.0,44,131
54,replace,2.0,(,#,CC#lO,CC(lO,5,replace # at position 2 with (,flow_matching,0.3,2.0,44,131
55,replace,3.0,C,l,CC(lO,CC(CO,5,replace l at position 3 with C,flow_matching,0.3,2.0,44,131
56,replace,1.0,\,C,CC(CO,C\(CO,5,replace C at position 1 with \,flow_matching,0.3,2.0,44,131
57,replace,4.0,o,O,C\(CO,C\(Co,5,replace O at position 4 with o,flow_matching,0.3,2.0,44,131
58,replace,2.0,C,(,C\(Co,C\CCo,5,replace ( at position 2 with C,flow_matching,0.3,2.0,44,131
59,replace,4.0,+,o,C\CCo,C\CC+,5,replace o at position 4 with +,flow_matching,0.3,2.0,44,131
60,replace,4.0,1,+,C\CC+,C\CC1,5,replace + at position 4 with 1,flow_matching,0.3,2.0,44,131
61,replace,2.0,N,C,C\CC1,C\NC1,5,replace C at position 2 with N,flow_matching,0.3,2.0,44,131
62,remove,1.0,\,,C\NC1,CNC1,4,remove \ from position 1,flow_matching,0.3,2.0,44,131
63,replace,1.0,B,N,CNC1,CBC1,4,replace N at position 1 with B,flow_matching,0.3,2.0,44,131
64,remove,3.0,1,,CBC1,CBC,3,remove 1 from position 3,flow_matching,0.3,2.0,44,131
65,replace,2.0,r,C,CBC,CBr,3,replace C at position 2 with r,flow_matching,0.3,2.0,44,131
66,replace,1.0,C,B,CBr,CCr,3,replace B at position 1 with C,flow_matching,0.3,2.0,44,131
67,replace,2.0,(,r,CCr,CC(,3,replace r at position 2 with (,flow_matching,0.3,2.0,44,131
68,add,1.0,n,,CC(,CnC(,4,add n at position 1,flow_matching,0.3,2.0,44,131
69,add,0.0,1,,CnC(,1CnC(,5,add 1 at position 0,flow_matching,0.3,2.0,44,131
70,remove,1.0,C,,1CnC(,1nC(,4,remove C from position 1,flow_matching,0.3,2.0,44,131
71,replace,0.0,I,1,1nC(,InC(,4,replace 1 at position 0 with I,flow_matching,0.3,2.0,44,131
72,add,2.0,N,,InC(,InNC(,5,add N at position 2,flow_matching,0.3,2.0,44,131
73,remove,1.0,n,,InNC(,INC(,4,remove n from position 1,flow_matching,0.3,2.0,44,131
74,replace,0.0,C,I,INC(,CNC(,4,replace I at position 0 with C,flow_matching,0.3,2.0,44,131
75,replace,1.0,C,N,CNC(,CCC(,4,replace N at position 1 with C,flow_matching,0.3,2.0,44,131
76,add,0.0,5,,CCC(,5CCC(,5,add 5 at position 0,flow_matching,0.3,2.0,44,131
77,remove,2.0,C,,5CCC(,5CC(,4,remove C from position 2,flow_matching,0.3,2.0,44,131
78,replace,0.0,C,5,5CC(,CCC(,4,replace 5 at position 0 with C,flow_matching,0.3,2.0,44,131
79,add,4.0,2,,CCC(,CCC(2,5,add 2 at position 4,flow_matching,0.3,2.0,44,131
80,replace,2.0,(,C,CCC(2,CC((2,5,replace C at position 2 with (,flow_matching,0.3,2.0,44,131
81,add,3.0,c,,CC((2,CC(c(2,6,add c at position 3,flow_matching,0.3,2.0,44,131
82,replace,0.0,O,C,CC(c(2,OC(c(2,6,replace C at position 0 with O,flow_matching,0.3,2.0,44,131
83,remove,4.0,(,,OC(c(2,OC(c2,5,remove ( from position 4,flow_matching,0.3,2.0,44,131
84,replace,0.0,C,O,OC(c2,CC(c2,5,replace O at position 0 with C,flow_matching,0.3,2.0,44,131
85,add,5.0,[,,CC(c2,CC(c2[,6,add [ at position 5,flow_matching,0.3,2.0,44,131
86,add,5.0,n,,CC(c2[,CC(c2n[,7,add n at position 5,flow_matching,0.3,2.0,44,131
87,replace,1.0,4,C,CC(c2n[,C4(c2n[,7,replace C at position 1 with 4,flow_matching,0.3,2.0,44,131
88,replace,5.0,6,n,C4(c2n[,C4(c26[,7,replace n at position 5 with 6,flow_matching,0.3,2.0,44,131
89,replace,1.0,C,4,C4(c26[,CC(c26[,7,replace 4 at position 1 with C,flow_matching,0.3,2.0,44,131
90,replace,3.0,C,c,CC(c26[,CC(C26[,7,replace c at position 3 with C,flow_matching,0.3,2.0,44,131
91,replace,4.0,[,2,CC(C26[,CC(C[6[,7,replace 2 at position 4 with [,flow_matching,0.3,2.0,44,131
92,replace,4.0,),[,CC(C[6[,CC(C)6[,7,replace [ at position 4 with ),flow_matching,0.3,2.0,44,131
93,replace,5.0,O,6,CC(C)6[,CC(C)O[,7,replace 6 at position 5 with O,flow_matching,0.3,2.0,44,131
94,replace,6.0,c,[,CC(C)O[,CC(C)Oc,7,replace [ at position 6 with c,flow_matching,0.3,2.0,44,131
95,add,7.0,1,,CC(C)Oc,CC(C)Oc1,8,add 1 at position 7,flow_matching,0.3,2.0,44,131
96,add,8.0,c,,CC(C)Oc1,CC(C)Oc1c,9,add c at position 8,flow_matching,0.3,2.0,44,131
97,add,9.0,c,,CC(C)Oc1c,CC(C)Oc1cc,10,add c at position 9,flow_matching,0.3,2.0,44,131
98,add,10.0,c,,CC(C)Oc1cc,CC(C)Oc1ccc,11,add c at position 10,flow_matching,0.3,2.0,44,131
99,add,11.0,(,,CC(C)Oc1ccc,CC(C)Oc1ccc(,12,add ( at position 11,flow_matching,0.3,2.0,44,131
100,add,12.0,N,,CC(C)Oc1ccc(,CC(C)Oc1ccc(N,13,add N at position 12,flow_matching,0.3,2.0,44,131
101,add,13.0,C,,CC(C)Oc1ccc(N,CC(C)Oc1ccc(NC,14,add C at position 13,flow_matching,0.3,2.0,44,131
102,add,14.0,(,,CC(C)Oc1ccc(NC,CC(C)Oc1ccc(NC(,15,add ( at position 14,flow_matching,0.3,2.0,44,131
103,add,15.0,=,,CC(C)Oc1ccc(NC(,CC(C)Oc1ccc(NC(=,16,add = at position 15,flow_matching,0.3,2.0,44,131
104,add,16.0,O,,CC(C)Oc1ccc(NC(=,CC(C)Oc1ccc(NC(=O,17,add O at position 16,flow_matching,0.3,2.0,44,131
105,add,17.0,),,CC(C)Oc1ccc(NC(=O,CC(C)Oc1ccc(NC(=O),18,add ) at position 17,flow_matching,0.3,2.0,44,131
106,add,18.0,N,,CC(C)Oc1ccc(NC(=O),CC(C)Oc1ccc(NC(=O)N,19,add N at position 18,flow_matching,0.3,2.0,44,131
107,add,19.0,C,,CC(C)Oc1ccc(NC(=O)N,CC(C)Oc1ccc(NC(=O)NC,20,add C at position 19,flow_matching,0.3,2.0,44,131
108,add,20.0,[,,CC(C)Oc1ccc(NC(=O)NC,CC(C)Oc1ccc(NC(=O)NC[,21,add [ at position 20,flow_matching,0.3,2.0,44,131
109,add,21.0,C,,CC(C)Oc1ccc(NC(=O)NC[,CC(C)Oc1ccc(NC(=O)NC[C,22,add C at position 21,flow_matching,0.3,2.0,44,131
110,add,22.0,@,,CC(C)Oc1ccc(NC(=O)NC[C,CC(C)Oc1ccc(NC(=O)NC[C@,23,add @ at position 22,flow_matching,0.3,2.0,44,131
111,add,23.0,H,,CC(C)Oc1ccc(NC(=O)NC[C@,CC(C)Oc1ccc(NC(=O)NC[C@H,24,add H at position 23,flow_matching,0.3,2.0,44,131
112,add,24.0,],,CC(C)Oc1ccc(NC(=O)NC[C@H,CC(C)Oc1ccc(NC(=O)NC[C@H],25,add ] at position 24,flow_matching,0.3,2.0,44,131
113,add,25.0,(,,CC(C)Oc1ccc(NC(=O)NC[C@H],CC(C)Oc1ccc(NC(=O)NC[C@H](,26,add ( at position 25,flow_matching,0.3,2.0,44,131
114,add,26.0,C,,CC(C)Oc1ccc(NC(=O)NC[C@H](,CC(C)Oc1ccc(NC(=O)NC[C@H](C,27,add C at position 26,flow_matching,0.3,2.0,44,131
115,add,27.0,),,CC(C)Oc1ccc(NC(=O)NC[C@H](C,CC(C)Oc1ccc(NC(=O)NC[C@H](C),28,add ) at position 27,flow_matching,0.3,2.0,44,131
116,add,28.0,N,,CC(C)Oc1ccc(NC(=O)NC[C@H](C),CC(C)Oc1ccc(NC(=O)NC[C@H](C)N,29,add N at position 28,flow_matching,0.3,2.0,44,131
117,add,29.0,2,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2,30,add 2 at position 29,flow_matching,0.3,2.0,44,131
118,add,30.0,C,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2C,31,add C at position 30,flow_matching,0.3,2.0,44,131
119,add,31.0,C,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2C,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CC,32,add C at position 31,flow_matching,0.3,2.0,44,131
120,add,32.0,O,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CC,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCO,33,add O at position 32,flow_matching,0.3,2.0,44,131
121,add,33.0,C,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCO,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOC,34,add C at position 33,flow_matching,0.3,2.0,44,131
122,add,34.0,C,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOC,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC,35,add C at position 34,flow_matching,0.3,2.0,44,131
123,add,35.0,2,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2,36,add 2 at position 35,flow_matching,0.3,2.0,44,131
124,add,36.0,),,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2),37,add ) at position 36,flow_matching,0.3,2.0,44,131
125,add,37.0,c,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2),CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c,38,add c at position 37,flow_matching,0.3,2.0,44,131
126,add,38.0,(,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(,39,add ( at position 38,flow_matching,0.3,2.0,44,131
127,add,39.0,F,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(F,40,add F at position 39,flow_matching,0.3,2.0,44,131
128,add,40.0,),,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(F,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(F),41,add ) at position 40,flow_matching,0.3,2.0,44,131
129,add,41.0,c,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(F),CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(F)c,42,add c at position 41,flow_matching,0.3,2.0,44,131
130,add,42.0,1,,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(F)c,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(F)c1,43,add 1 at position 42,flow_matching,0.3,2.0,44,131
131,add,43.0,"
",,CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(F)c1,"CC(C)Oc1ccc(NC(=O)NC[C@H](C)N2CCOCC2)c(F)c1
",44,"add 
 at position 43",flow_matching,0.3,2.0,44,131
