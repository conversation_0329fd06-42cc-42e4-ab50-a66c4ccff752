step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,60,282
1,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,60,282
2,replace,0.0,C,B,B,C,1,replace <PERSON> at position 0 with C,flow_matching,0.3,2.0,60,282
3,replace,0.0,#,C,C,#,1,replace <PERSON> at position 0 with #,flow_matching,0.3,2.0,60,282
4,replace,0.0,],#,#,],1,replace # at position 0 with ],flow_matching,0.3,2.0,60,282
5,replace,0.0,[,],],[,1,replace ] at position 0 with [,flow_matching,0.3,2.0,60,282
6,replace,0.0,C,[,[,C,1,replace [ at position 0 with C,flow_matching,0.3,2.0,60,282
7,add,0.0,[,,C,[C,2,add [ at position 0,flow_matching,0.3,2.0,60,282
8,add,2.0,(,,[C,[C(,3,add ( at position 2,flow_matching,0.3,2.0,60,282
9,replace,2.0,I,(,[C(,[CI,3,replace ( at position 2 with I,flow_matching,0.3,2.0,60,282
10,replace,0.0,C,[,[CI,CCI,3,replace [ at position 0 with C,flow_matching,0.3,2.0,60,282
11,add,3.0,F,,CCI,CCIF,4,add F at position 3,flow_matching,0.3,2.0,60,282
12,remove,1.0,C,,CCIF,CIF,3,remove C from position 1,flow_matching,0.3,2.0,60,282
13,replace,1.0,C,I,CIF,CCF,3,replace I at position 1 with C,flow_matching,0.3,2.0,60,282
14,replace,2.0,#,F,CCF,CC#,3,replace F at position 2 with #,flow_matching,0.3,2.0,60,282
15,replace,2.0,B,#,CC#,CCB,3,replace # at position 2 with B,flow_matching,0.3,2.0,60,282
16,add,0.0,s,,CCB,sCCB,4,add s at position 0,flow_matching,0.3,2.0,60,282
17,replace,0.0,C,s,sCCB,CCCB,4,replace s at position 0 with C,flow_matching,0.3,2.0,60,282
18,replace,2.0,(,C,CCCB,CC(B,4,replace C at position 2 with (,flow_matching,0.3,2.0,60,282
19,add,2.0,@,,CC(B,CC@(B,5,add @ at position 2,flow_matching,0.3,2.0,60,282
20,replace,0.0,+,C,CC@(B,+C@(B,5,replace C at position 0 with +,flow_matching,0.3,2.0,60,282
21,replace,0.0,C,+,+C@(B,CC@(B,5,replace + at position 0 with C,flow_matching,0.3,2.0,60,282
22,add,3.0,6,,CC@(B,CC@6(B,6,add 6 at position 3,flow_matching,0.3,2.0,60,282
23,remove,1.0,C,,CC@6(B,C@6(B,5,remove C from position 1,flow_matching,0.3,2.0,60,282
24,remove,0.0,C,,C@6(B,@6(B,4,remove C from position 0,flow_matching,0.3,2.0,60,282
25,replace,0.0,C,@,@6(B,C6(B,4,replace @ at position 0 with C,flow_matching,0.3,2.0,60,282
26,add,1.0,C,,C6(B,CC6(B,5,add C at position 1,flow_matching,0.3,2.0,60,282
27,replace,2.0,(,6,CC6(B,CC((B,5,replace 6 at position 2 with (,flow_matching,0.3,2.0,60,282
28,remove,3.0,(,,CC((B,CC(B,4,remove ( from position 3,flow_matching,0.3,2.0,60,282
29,add,4.0,F,,CC(B,CC(BF,5,add F at position 4,flow_matching,0.3,2.0,60,282
30,replace,4.0,7,F,CC(BF,CC(B7,5,replace F at position 4 with 7,flow_matching,0.3,2.0,60,282
31,replace,3.0,C,B,CC(B7,CC(C7,5,replace B at position 3 with C,flow_matching,0.3,2.0,60,282
32,remove,3.0,C,,CC(C7,CC(7,4,remove C from position 3,flow_matching,0.3,2.0,60,282
33,remove,3.0,7,,CC(7,CC(,3,remove 7 from position 3,flow_matching,0.3,2.0,60,282
34,add,1.0,l,,CC(,ClC(,4,add l at position 1,flow_matching,0.3,2.0,60,282
35,replace,2.0,-,C,ClC(,Cl-(,4,replace C at position 2 with -,flow_matching,0.3,2.0,60,282
36,remove,2.0,-,,Cl-(,Cl(,3,remove - from position 2,flow_matching,0.3,2.0,60,282
37,add,2.0,-,,Cl(,Cl-(,4,add - at position 2,flow_matching,0.3,2.0,60,282
38,replace,1.0,C,l,Cl-(,CC-(,4,replace l at position 1 with C,flow_matching,0.3,2.0,60,282
39,replace,2.0,(,-,CC-(,CC((,4,replace - at position 2 with (,flow_matching,0.3,2.0,60,282
40,add,4.0,S,,CC((,CC((S,5,add S at position 4,flow_matching,0.3,2.0,60,282
41,remove,3.0,(,,CC((S,CC(S,4,remove ( from position 3,flow_matching,0.3,2.0,60,282
42,remove,1.0,C,,CC(S,C(S,3,remove C from position 1,flow_matching,0.3,2.0,60,282
43,replace,2.0,],S,C(S,C(],3,replace S at position 2 with ],flow_matching,0.3,2.0,60,282
44,replace,1.0,C,(,C(],CC],3,replace ( at position 1 with C,flow_matching,0.3,2.0,60,282
45,remove,2.0,],,CC],CC,2,remove ] from position 2,flow_matching,0.3,2.0,60,282
46,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,60,282
47,add,3.0,C,,CC(,CC(C,4,add C at position 3,flow_matching,0.3,2.0,60,282
48,add,4.0,),,CC(C,CC(C),5,add ) at position 4,flow_matching,0.3,2.0,60,282
49,add,5.0,C,,CC(C),CC(C)C,6,add C at position 5,flow_matching,0.3,2.0,60,282
50,remove,3.0,C,,CC(C)C,CC()C,5,remove C from position 3,flow_matching,0.3,2.0,60,282
51,add,4.0,o,,CC()C,CC()oC,6,add o at position 4,flow_matching,0.3,2.0,60,282
52,replace,3.0,C,),CC()oC,CC(CoC,6,replace ) at position 3 with C,flow_matching,0.3,2.0,60,282
53,add,5.0,[,,CC(CoC,CC(Co[C,7,add [ at position 5,flow_matching,0.3,2.0,60,282
54,add,0.0,I,,CC(Co[C,ICC(Co[C,8,add I at position 0,flow_matching,0.3,2.0,60,282
55,remove,1.0,C,,ICC(Co[C,IC(Co[C,7,remove C from position 1,flow_matching,0.3,2.0,60,282
56,remove,4.0,o,,IC(Co[C,IC(C[C,6,remove o from position 4,flow_matching,0.3,2.0,60,282
57,remove,0.0,I,,IC(C[C,C(C[C,5,remove I from position 0,flow_matching,0.3,2.0,60,282
58,replace,1.0,C,(,C(C[C,CCC[C,5,replace ( at position 1 with C,flow_matching,0.3,2.0,60,282
59,replace,0.0,+,C,CCC[C,+CC[C,5,replace C at position 0 with +,flow_matching,0.3,2.0,60,282
60,replace,0.0,C,+,+CC[C,CCC[C,5,replace + at position 0 with C,flow_matching,0.3,2.0,60,282
61,remove,2.0,C,,CCC[C,CC[C,4,remove C from position 2,flow_matching,0.3,2.0,60,282
62,replace,3.0,s,C,CC[C,CC[s,4,replace C at position 3 with s,flow_matching,0.3,2.0,60,282
63,add,1.0,B,,CC[s,CBC[s,5,add B at position 1,flow_matching,0.3,2.0,60,282
64,replace,1.0,(,B,CBC[s,C(C[s,5,replace B at position 1 with (,flow_matching,0.3,2.0,60,282
65,replace,1.0,C,(,C(C[s,CCC[s,5,replace ( at position 1 with C,flow_matching,0.3,2.0,60,282
66,add,1.0,l,,CCC[s,ClCC[s,6,add l at position 1,flow_matching,0.3,2.0,60,282
67,add,5.0,1,,ClCC[s,ClCC[1s,7,add 1 at position 5,flow_matching,0.3,2.0,60,282
68,add,2.0,2,,ClCC[1s,Cl2CC[1s,8,add 2 at position 2,flow_matching,0.3,2.0,60,282
69,replace,1.0,C,l,Cl2CC[1s,CC2CC[1s,8,replace l at position 1 with C,flow_matching,0.3,2.0,60,282
70,add,0.0,7,,CC2CC[1s,7CC2CC[1s,9,add 7 at position 0,flow_matching,0.3,2.0,60,282
71,add,7.0,7,,7CC2CC[1s,7CC2CC[71s,10,add 7 at position 7,flow_matching,0.3,2.0,60,282
72,add,0.0,o,,7CC2CC[71s,o7CC2CC[71s,11,add o at position 0,flow_matching,0.3,2.0,60,282
73,replace,0.0,C,o,o7CC2CC[71s,C7CC2CC[71s,11,replace o at position 0 with C,flow_matching,0.3,2.0,60,282
74,replace,4.0,l,2,C7CC2CC[71s,C7CClCC[71s,11,replace 2 at position 4 with l,flow_matching,0.3,2.0,60,282
75,add,2.0,l,,C7CClCC[71s,C7lCClCC[71s,12,add l at position 2,flow_matching,0.3,2.0,60,282
76,remove,3.0,C,,C7lCClCC[71s,C7lClCC[71s,11,remove C from position 3,flow_matching,0.3,2.0,60,282
77,remove,5.0,C,,C7lClCC[71s,C7lClC[71s,10,remove C from position 5,flow_matching,0.3,2.0,60,282
78,add,4.0,B,,C7lClC[71s,C7lCBlC[71s,11,add B at position 4,flow_matching,0.3,2.0,60,282
79,add,0.0,I,,C7lCBlC[71s,IC7lCBlC[71s,12,add I at position 0,flow_matching,0.3,2.0,60,282
80,remove,4.0,C,,IC7lCBlC[71s,IC7lBlC[71s,11,remove C from position 4,flow_matching,0.3,2.0,60,282
81,remove,1.0,C,,IC7lBlC[71s,I7lBlC[71s,10,remove C from position 1,flow_matching,0.3,2.0,60,282
82,replace,0.0,C,I,I7lBlC[71s,C7lBlC[71s,10,replace I at position 0 with C,flow_matching,0.3,2.0,60,282
83,replace,1.0,C,7,C7lBlC[71s,CClBlC[71s,10,replace 7 at position 1 with C,flow_matching,0.3,2.0,60,282
84,add,8.0,l,,CClBlC[71s,CClBlC[7l1s,11,add l at position 8,flow_matching,0.3,2.0,60,282
85,add,9.0,+,,CClBlC[7l1s,CClBlC[7l+1s,12,add + at position 9,flow_matching,0.3,2.0,60,282
86,remove,9.0,+,,CClBlC[7l+1s,CClBlC[7l1s,11,remove + from position 9,flow_matching,0.3,2.0,60,282
87,remove,7.0,7,,CClBlC[7l1s,CClBlC[l1s,10,remove 7 from position 7,flow_matching,0.3,2.0,60,282
88,remove,9.0,s,,CClBlC[l1s,CClBlC[l1,9,remove s from position 9,flow_matching,0.3,2.0,60,282
89,add,9.0,I,,CClBlC[l1,CClBlC[l1I,10,add I at position 9,flow_matching,0.3,2.0,60,282
90,remove,4.0,l,,CClBlC[l1I,CClBC[l1I,9,remove l from position 4,flow_matching,0.3,2.0,60,282
91,add,9.0,=,,CClBC[l1I,CClBC[l1I=,10,add = at position 9,flow_matching,0.3,2.0,60,282
92,remove,3.0,B,,CClBC[l1I=,CClC[l1I=,9,remove B from position 3,flow_matching,0.3,2.0,60,282
93,replace,2.0,(,l,CClC[l1I=,CC(C[l1I=,9,replace l at position 2 with (,flow_matching,0.3,2.0,60,282
94,remove,2.0,(,,CC(C[l1I=,CCC[l1I=,8,remove ( from position 2,flow_matching,0.3,2.0,60,282
95,replace,2.0,(,C,CCC[l1I=,CC([l1I=,8,replace C at position 2 with (,flow_matching,0.3,2.0,60,282
96,replace,3.0,C,[,CC([l1I=,CC(Cl1I=,8,replace [ at position 3 with C,flow_matching,0.3,2.0,60,282
97,add,3.0,/,,CC(Cl1I=,CC(/Cl1I=,9,add / at position 3,flow_matching,0.3,2.0,60,282
98,replace,4.0,@,C,CC(/Cl1I=,CC(/@l1I=,9,replace C at position 4 with @,flow_matching,0.3,2.0,60,282
99,replace,3.0,C,/,CC(/@l1I=,CC(C@l1I=,9,replace / at position 3 with C,flow_matching,0.3,2.0,60,282
100,replace,0.0,6,C,CC(C@l1I=,6C(C@l1I=,9,replace C at position 0 with 6,flow_matching,0.3,2.0,60,282
101,replace,0.0,C,6,6C(C@l1I=,CC(C@l1I=,9,replace 6 at position 0 with C,flow_matching,0.3,2.0,60,282
102,replace,4.0,),@,CC(C@l1I=,CC(C)l1I=,9,replace @ at position 4 with ),flow_matching,0.3,2.0,60,282
103,add,7.0,2,,CC(C)l1I=,CC(C)l12I=,10,add 2 at position 7,flow_matching,0.3,2.0,60,282
104,replace,5.0,C,l,CC(C)l12I=,CC(C)C12I=,10,replace l at position 5 with C,flow_matching,0.3,2.0,60,282
105,add,5.0,1,,CC(C)C12I=,CC(C)1C12I=,11,add 1 at position 5,flow_matching,0.3,2.0,60,282
106,add,1.0,],,CC(C)1C12I=,C]C(C)1C12I=,12,add ] at position 1,flow_matching,0.3,2.0,60,282
107,add,3.0,+,,C]C(C)1C12I=,C]C+(C)1C12I=,13,add + at position 3,flow_matching,0.3,2.0,60,282
108,add,0.0,B,,C]C+(C)1C12I=,BC]C+(C)1C12I=,14,add B at position 0,flow_matching,0.3,2.0,60,282
109,replace,0.0,C,B,BC]C+(C)1C12I=,CC]C+(C)1C12I=,14,replace B at position 0 with C,flow_matching,0.3,2.0,60,282
110,replace,1.0,2,C,CC]C+(C)1C12I=,C2]C+(C)1C12I=,14,replace C at position 1 with 2,flow_matching,0.3,2.0,60,282
111,replace,1.0,C,2,C2]C+(C)1C12I=,CC]C+(C)1C12I=,14,replace 2 at position 1 with C,flow_matching,0.3,2.0,60,282
112,add,3.0,],,CC]C+(C)1C12I=,CC]]C+(C)1C12I=,15,add ] at position 3,flow_matching,0.3,2.0,60,282
113,remove,9.0,1,,CC]]C+(C)1C12I=,CC]]C+(C)C12I=,14,remove 1 from position 9,flow_matching,0.3,2.0,60,282
114,add,12.0,3,,CC]]C+(C)C12I=,CC]]C+(C)C123I=,15,add 3 at position 12,flow_matching,0.3,2.0,60,282
115,replace,2.0,(,],CC]]C+(C)C123I=,CC(]C+(C)C123I=,15,replace ] at position 2 with (,flow_matching,0.3,2.0,60,282
116,add,8.0,n,,CC(]C+(C)C123I=,CC(]C+(Cn)C123I=,16,add n at position 8,flow_matching,0.3,2.0,60,282
117,remove,11.0,1,,CC(]C+(Cn)C123I=,CC(]C+(Cn)C23I=,15,remove 1 from position 11,flow_matching,0.3,2.0,60,282
118,replace,7.0,],C,CC(]C+(Cn)C23I=,CC(]C+(]n)C23I=,15,replace C at position 7 with ],flow_matching,0.3,2.0,60,282
119,remove,3.0,],,CC(]C+(]n)C23I=,CC(C+(]n)C23I=,14,remove ] from position 3,flow_matching,0.3,2.0,60,282
120,replace,4.0,),+,CC(C+(]n)C23I=,CC(C)(]n)C23I=,14,replace + at position 4 with ),flow_matching,0.3,2.0,60,282
121,replace,10.0,H,2,CC(C)(]n)C23I=,CC(C)(]n)CH3I=,14,replace 2 at position 10 with H,flow_matching,0.3,2.0,60,282
122,remove,4.0,),,CC(C)(]n)CH3I=,CC(C(]n)CH3I=,13,remove ) from position 4,flow_matching,0.3,2.0,60,282
123,add,4.0,6,,CC(C(]n)CH3I=,CC(C6(]n)CH3I=,14,add 6 at position 4,flow_matching,0.3,2.0,60,282
124,replace,4.0,),6,CC(C6(]n)CH3I=,CC(C)(]n)CH3I=,14,replace 6 at position 4 with ),flow_matching,0.3,2.0,60,282
125,add,0.0,B,,CC(C)(]n)CH3I=,BCC(C)(]n)CH3I=,15,add B at position 0,flow_matching,0.3,2.0,60,282
126,replace,0.0,C,B,BCC(C)(]n)CH3I=,CCC(C)(]n)CH3I=,15,replace B at position 0 with C,flow_matching,0.3,2.0,60,282
127,replace,12.0,B,3,CCC(C)(]n)CH3I=,CCC(C)(]n)CHBI=,15,replace 3 at position 12 with B,flow_matching,0.3,2.0,60,282
128,replace,2.0,(,C,CCC(C)(]n)CHBI=,CC((C)(]n)CHBI=,15,replace C at position 2 with (,flow_matching,0.3,2.0,60,282
129,add,5.0,+,,CC((C)(]n)CHBI=,CC((C+)(]n)CHBI=,16,add + at position 5,flow_matching,0.3,2.0,60,282
130,add,8.0,I,,CC((C+)(]n)CHBI=,CC((C+)(I]n)CHBI=,17,add I at position 8,flow_matching,0.3,2.0,60,282
131,replace,3.0,7,(,CC((C+)(I]n)CHBI=,CC(7C+)(I]n)CHBI=,17,replace ( at position 3 with 7,flow_matching,0.3,2.0,60,282
132,replace,0.0,2,C,CC(7C+)(I]n)CHBI=,2C(7C+)(I]n)CHBI=,17,replace C at position 0 with 2,flow_matching,0.3,2.0,60,282
133,remove,15.0,I,,2C(7C+)(I]n)CHBI=,2C(7C+)(I]n)CHB=,16,remove I from position 15,flow_matching,0.3,2.0,60,282
134,replace,0.0,C,2,2C(7C+)(I]n)CHB=,CC(7C+)(I]n)CHB=,16,replace 2 at position 0 with C,flow_matching,0.3,2.0,60,282
135,remove,10.0,n,,CC(7C+)(I]n)CHB=,CC(7C+)(I])CHB=,15,remove n from position 10,flow_matching,0.3,2.0,60,282
136,remove,1.0,C,,CC(7C+)(I])CHB=,C(7C+)(I])CHB=,14,remove C from position 1,flow_matching,0.3,2.0,60,282
137,add,3.0,n,,C(7C+)(I])CHB=,C(7nC+)(I])CHB=,15,add n at position 3,flow_matching,0.3,2.0,60,282
138,add,15.0,B,,C(7nC+)(I])CHB=,C(7nC+)(I])CHB=B,16,add B at position 15,flow_matching,0.3,2.0,60,282
139,add,2.0,s,,C(7nC+)(I])CHB=B,C(s7nC+)(I])CHB=B,17,add s at position 2,flow_matching,0.3,2.0,60,282
140,add,1.0,-,,C(s7nC+)(I])CHB=B,C-(s7nC+)(I])CHB=B,18,add - at position 1,flow_matching,0.3,2.0,60,282
141,replace,6.0,[,C,C-(s7nC+)(I])CHB=B,C-(s7n[+)(I])CHB=B,18,replace C at position 6 with [,flow_matching,0.3,2.0,60,282
142,replace,1.0,C,-,C-(s7n[+)(I])CHB=B,CC(s7n[+)(I])CHB=B,18,replace - at position 1 with C,flow_matching,0.3,2.0,60,282
143,replace,3.0,C,s,CC(s7n[+)(I])CHB=B,CC(C7n[+)(I])CHB=B,18,replace s at position 3 with C,flow_matching,0.3,2.0,60,282
144,replace,4.0,),7,CC(C7n[+)(I])CHB=B,CC(C)n[+)(I])CHB=B,18,replace 7 at position 4 with ),flow_matching,0.3,2.0,60,282
145,replace,9.0,1,(,CC(C)n[+)(I])CHB=B,CC(C)n[+)1I])CHB=B,18,replace ( at position 9 with 1,flow_matching,0.3,2.0,60,282
146,remove,15.0,B,,CC(C)n[+)1I])CHB=B,CC(C)n[+)1I])CH=B,17,remove B from position 15,flow_matching,0.3,2.0,60,282
147,add,10.0,@,,CC(C)n[+)1I])CH=B,CC(C)n[+)1@I])CH=B,18,add @ at position 10,flow_matching,0.3,2.0,60,282
148,replace,5.0,C,n,CC(C)n[+)1@I])CH=B,CC(C)C[+)1@I])CH=B,18,replace n at position 5 with C,flow_matching,0.3,2.0,60,282
149,replace,13.0,C,),CC(C)C[+)1@I])CH=B,CC(C)C[+)1@I]CCH=B,18,replace ) at position 13 with C,flow_matching,0.3,2.0,60,282
150,replace,6.0,H,[,CC(C)C[+)1@I]CCH=B,CC(C)CH+)1@I]CCH=B,18,replace [ at position 6 with H,flow_matching,0.3,2.0,60,282
151,replace,10.0,n,@,CC(C)CH+)1@I]CCH=B,CC(C)CH+)1nI]CCH=B,18,replace @ at position 10 with n,flow_matching,0.3,2.0,60,282
152,add,5.0,4,,CC(C)CH+)1nI]CCH=B,CC(C)4CH+)1nI]CCH=B,19,add 4 at position 5,flow_matching,0.3,2.0,60,282
153,remove,6.0,C,,CC(C)4CH+)1nI]CCH=B,CC(C)4H+)1nI]CCH=B,18,remove C from position 6,flow_matching,0.3,2.0,60,282
154,add,5.0,r,,CC(C)4H+)1nI]CCH=B,CC(C)r4H+)1nI]CCH=B,19,add r at position 5,flow_matching,0.3,2.0,60,282
155,add,9.0,\,,CC(C)r4H+)1nI]CCH=B,CC(C)r4H+\)1nI]CCH=B,20,add \ at position 9,flow_matching,0.3,2.0,60,282
156,remove,17.0,H,,CC(C)r4H+\)1nI]CCH=B,CC(C)r4H+\)1nI]CC=B,19,remove H from position 17,flow_matching,0.3,2.0,60,282
157,remove,18.0,B,,CC(C)r4H+\)1nI]CC=B,CC(C)r4H+\)1nI]CC=,18,remove B from position 18,flow_matching,0.3,2.0,60,282
158,remove,9.0,\,,CC(C)r4H+\)1nI]CC=,CC(C)r4H+)1nI]CC=,17,remove \ from position 9,flow_matching,0.3,2.0,60,282
159,replace,5.0,C,r,CC(C)r4H+)1nI]CC=,CC(C)C4H+)1nI]CC=,17,replace r at position 5 with C,flow_matching,0.3,2.0,60,282
160,replace,6.0,(,4,CC(C)C4H+)1nI]CC=,CC(C)C(H+)1nI]CC=,17,replace 4 at position 6 with (,flow_matching,0.3,2.0,60,282
161,add,0.0,l,,CC(C)C(H+)1nI]CC=,lCC(C)C(H+)1nI]CC=,18,add l at position 0,flow_matching,0.3,2.0,60,282
162,replace,17.0,o,=,lCC(C)C(H+)1nI]CC=,lCC(C)C(H+)1nI]CCo,18,replace = at position 17 with o,flow_matching,0.3,2.0,60,282
163,replace,0.0,C,l,lCC(C)C(H+)1nI]CCo,CCC(C)C(H+)1nI]CCo,18,replace l at position 0 with C,flow_matching,0.3,2.0,60,282
164,add,3.0,n,,CCC(C)C(H+)1nI]CCo,CCCn(C)C(H+)1nI]CCo,19,add n at position 3,flow_matching,0.3,2.0,60,282
165,replace,12.0,],1,CCCn(C)C(H+)1nI]CCo,CCCn(C)C(H+)]nI]CCo,19,replace 1 at position 12 with ],flow_matching,0.3,2.0,60,282
166,add,13.0,c,,CCCn(C)C(H+)]nI]CCo,CCCn(C)C(H+)]cnI]CCo,20,add c at position 13,flow_matching,0.3,2.0,60,282
167,remove,4.0,(,,CCCn(C)C(H+)]cnI]CCo,CCCnC)C(H+)]cnI]CCo,19,remove ( from position 4,flow_matching,0.3,2.0,60,282
168,remove,17.0,C,,CCCnC)C(H+)]cnI]CCo,CCCnC)C(H+)]cnI]Co,18,remove C from position 17,flow_matching,0.3,2.0,60,282
169,replace,2.0,(,C,CCCnC)C(H+)]cnI]Co,CC(nC)C(H+)]cnI]Co,18,replace C at position 2 with (,flow_matching,0.3,2.0,60,282
170,replace,3.0,C,n,CC(nC)C(H+)]cnI]Co,CC(CC)C(H+)]cnI]Co,18,replace n at position 3 with C,flow_matching,0.3,2.0,60,282
171,replace,4.0,),C,CC(CC)C(H+)]cnI]Co,CC(C))C(H+)]cnI]Co,18,replace C at position 4 with ),flow_matching,0.3,2.0,60,282
172,add,13.0,),,CC(C))C(H+)]cnI]Co,CC(C))C(H+)]c)nI]Co,19,add ) at position 13,flow_matching,0.3,2.0,60,282
173,add,13.0,H,,CC(C))C(H+)]c)nI]Co,CC(C))C(H+)]cH)nI]Co,20,add H at position 13,flow_matching,0.3,2.0,60,282
174,remove,18.0,C,,CC(C))C(H+)]cH)nI]Co,CC(C))C(H+)]cH)nI]o,19,remove C from position 18,flow_matching,0.3,2.0,60,282
175,add,8.0,-,,CC(C))C(H+)]cH)nI]o,CC(C))C(-H+)]cH)nI]o,20,add - at position 8,flow_matching,0.3,2.0,60,282
176,remove,17.0,I,,CC(C))C(-H+)]cH)nI]o,CC(C))C(-H+)]cH)n]o,19,remove I from position 17,flow_matching,0.3,2.0,60,282
177,replace,12.0,2,],CC(C))C(-H+)]cH)n]o,CC(C))C(-H+)2cH)n]o,19,replace ] at position 12 with 2,flow_matching,0.3,2.0,60,282
178,add,2.0,3,,CC(C))C(-H+)2cH)n]o,CC3(C))C(-H+)2cH)n]o,20,add 3 at position 2,flow_matching,0.3,2.0,60,282
179,replace,17.0,r,n,CC3(C))C(-H+)2cH)n]o,CC3(C))C(-H+)2cH)r]o,20,replace n at position 17 with r,flow_matching,0.3,2.0,60,282
180,remove,7.0,C,,CC3(C))C(-H+)2cH)r]o,CC3(C))(-H+)2cH)r]o,19,remove C from position 7,flow_matching,0.3,2.0,60,282
181,replace,17.0,\,],CC3(C))(-H+)2cH)r]o,CC3(C))(-H+)2cH)r\o,19,replace ] at position 17 with \,flow_matching,0.3,2.0,60,282
182,replace,2.0,(,3,CC3(C))(-H+)2cH)r\o,CC((C))(-H+)2cH)r\o,19,replace 3 at position 2 with (,flow_matching,0.3,2.0,60,282
183,add,12.0,7,,CC((C))(-H+)2cH)r\o,CC((C))(-H+)72cH)r\o,20,add 7 at position 12,flow_matching,0.3,2.0,60,282
184,replace,3.0,C,(,CC((C))(-H+)72cH)r\o,CC(CC))(-H+)72cH)r\o,20,replace ( at position 3 with C,flow_matching,0.3,2.0,60,282
185,add,2.0,1,,CC(CC))(-H+)72cH)r\o,CC1(CC))(-H+)72cH)r\o,21,add 1 at position 2,flow_matching,0.3,2.0,60,282
186,add,16.0,c,,CC1(CC))(-H+)72cH)r\o,CC1(CC))(-H+)72ccH)r\o,22,add c at position 16,flow_matching,0.3,2.0,60,282
187,remove,15.0,c,,CC1(CC))(-H+)72ccH)r\o,CC1(CC))(-H+)72cH)r\o,21,remove c from position 15,flow_matching,0.3,2.0,60,282
188,add,12.0,6,,CC1(CC))(-H+)72cH)r\o,CC1(CC))(-H+6)72cH)r\o,22,add 6 at position 12,flow_matching,0.3,2.0,60,282
189,replace,12.0,o,6,CC1(CC))(-H+6)72cH)r\o,CC1(CC))(-H+o)72cH)r\o,22,replace 6 at position 12 with o,flow_matching,0.3,2.0,60,282
190,replace,2.0,(,1,CC1(CC))(-H+o)72cH)r\o,CC((CC))(-H+o)72cH)r\o,22,replace 1 at position 2 with (,flow_matching,0.3,2.0,60,282
191,replace,3.0,[,(,CC((CC))(-H+o)72cH)r\o,CC([CC))(-H+o)72cH)r\o,22,replace ( at position 3 with [,flow_matching,0.3,2.0,60,282
192,replace,2.0,B,(,CC([CC))(-H+o)72cH)r\o,CCB[CC))(-H+o)72cH)r\o,22,replace ( at position 2 with B,flow_matching,0.3,2.0,60,282
193,remove,19.0,r,,CCB[CC))(-H+o)72cH)r\o,CCB[CC))(-H+o)72cH)\o,21,remove r from position 19,flow_matching,0.3,2.0,60,282
194,replace,2.0,(,B,CCB[CC))(-H+o)72cH)\o,CC([CC))(-H+o)72cH)\o,21,replace B at position 2 with (,flow_matching,0.3,2.0,60,282
195,replace,0.0,),C,CC([CC))(-H+o)72cH)\o,)C([CC))(-H+o)72cH)\o,21,replace C at position 0 with ),flow_matching,0.3,2.0,60,282
196,add,19.0,s,,)C([CC))(-H+o)72cH)\o,)C([CC))(-H+o)72cH)s\o,22,add s at position 19,flow_matching,0.3,2.0,60,282
197,replace,20.0,r,\,)C([CC))(-H+o)72cH)s\o,)C([CC))(-H+o)72cH)sro,22,replace \ at position 20 with r,flow_matching,0.3,2.0,60,282
198,add,14.0,c,,)C([CC))(-H+o)72cH)sro,)C([CC))(-H+o)c72cH)sro,23,add c at position 14,flow_matching,0.3,2.0,60,282
199,replace,12.0,n,o,)C([CC))(-H+o)c72cH)sro,)C([CC))(-H+n)c72cH)sro,23,replace o at position 12 with n,flow_matching,0.3,2.0,60,282
200,replace,0.0,C,),)C([CC))(-H+n)c72cH)sro,CC([CC))(-H+n)c72cH)sro,23,replace ) at position 0 with C,flow_matching,0.3,2.0,60,282
201,add,9.0,[,,CC([CC))(-H+n)c72cH)sro,CC([CC))([-H+n)c72cH)sro,24,add [ at position 9,flow_matching,0.3,2.0,60,282
202,add,19.0,7,,CC([CC))([-H+n)c72cH)sro,CC([CC))([-H+n)c72c7H)sro,25,add 7 at position 19,flow_matching,0.3,2.0,60,282
203,replace,3.0,C,[,CC([CC))([-H+n)c72c7H)sro,CC(CCC))([-H+n)c72c7H)sro,25,replace [ at position 3 with C,flow_matching,0.3,2.0,60,282
204,replace,4.0,),C,CC(CCC))([-H+n)c72c7H)sro,CC(C)C))([-H+n)c72c7H)sro,25,replace C at position 4 with ),flow_matching,0.3,2.0,60,282
205,remove,5.0,C,,CC(C)C))([-H+n)c72c7H)sro,CC(C)))([-H+n)c72c7H)sro,24,remove C from position 5,flow_matching,0.3,2.0,60,282
206,replace,18.0,@,7,CC(C)))([-H+n)c72c7H)sro,CC(C)))([-H+n)c72c@H)sro,24,replace 7 at position 18 with @,flow_matching,0.3,2.0,60,282
207,replace,0.0,3,C,CC(C)))([-H+n)c72c@H)sro,3C(C)))([-H+n)c72c@H)sro,24,replace C at position 0 with 3,flow_matching,0.3,2.0,60,282
208,remove,5.0,),,3C(C)))([-H+n)c72c@H)sro,3C(C))([-H+n)c72c@H)sro,23,remove ) from position 5,flow_matching,0.3,2.0,60,282
209,add,2.0,7,,3C(C))([-H+n)c72c@H)sro,3C7(C))([-H+n)c72c@H)sro,24,add 7 at position 2,flow_matching,0.3,2.0,60,282
210,add,15.0,B,,3C7(C))([-H+n)c72c@H)sro,3C7(C))([-H+n)cB72c@H)sro,25,add B at position 15,flow_matching,0.3,2.0,60,282
211,add,12.0,3,,3C7(C))([-H+n)cB72c@H)sro,3C7(C))([-H+3n)cB72c@H)sro,26,add 3 at position 12,flow_matching,0.3,2.0,60,282
212,remove,13.0,n,,3C7(C))([-H+3n)cB72c@H)sro,3C7(C))([-H+3)cB72c@H)sro,25,remove n from position 13,flow_matching,0.3,2.0,60,282
213,add,11.0,=,,3C7(C))([-H+3)cB72c@H)sro,3C7(C))([-H=+3)cB72c@H)sro,26,add = at position 11,flow_matching,0.3,2.0,60,282
214,remove,2.0,7,,3C7(C))([-H=+3)cB72c@H)sro,3C(C))([-H=+3)cB72c@H)sro,25,remove 7 from position 2,flow_matching,0.3,2.0,60,282
215,replace,18.0,=,c,3C(C))([-H=+3)cB72c@H)sro,3C(C))([-H=+3)cB72=@H)sro,25,replace c at position 18 with =,flow_matching,0.3,2.0,60,282
216,replace,14.0,\,c,3C(C))([-H=+3)cB72=@H)sro,3C(C))([-H=+3)\B72=@H)sro,25,replace c at position 14 with \,flow_matching,0.3,2.0,60,282
217,replace,0.0,C,3,3C(C))([-H=+3)\B72=@H)sro,CC(C))([-H=+3)\B72=@H)sro,25,replace 3 at position 0 with C,flow_matching,0.3,2.0,60,282
218,replace,1.0,\,C,CC(C))([-H=+3)\B72=@H)sro,C\(C))([-H=+3)\B72=@H)sro,25,replace C at position 1 with \,flow_matching,0.3,2.0,60,282
219,remove,7.0,[,,C\(C))([-H=+3)\B72=@H)sro,C\(C))(-H=+3)\B72=@H)sro,24,remove [ from position 7,flow_matching,0.3,2.0,60,282
220,replace,1.0,C,\,C\(C))(-H=+3)\B72=@H)sro,CC(C))(-H=+3)\B72=@H)sro,24,replace \ at position 1 with C,flow_matching,0.3,2.0,60,282
221,add,3.0,@,,CC(C))(-H=+3)\B72=@H)sro,CC(@C))(-H=+3)\B72=@H)sro,25,add @ at position 3,flow_matching,0.3,2.0,60,282
222,replace,3.0,C,@,CC(@C))(-H=+3)\B72=@H)sro,CC(CC))(-H=+3)\B72=@H)sro,25,replace @ at position 3 with C,flow_matching,0.3,2.0,60,282
223,replace,0.0,O,C,CC(CC))(-H=+3)\B72=@H)sro,OC(CC))(-H=+3)\B72=@H)sro,25,replace C at position 0 with O,flow_matching,0.3,2.0,60,282
224,replace,0.0,C,O,OC(CC))(-H=+3)\B72=@H)sro,CC(CC))(-H=+3)\B72=@H)sro,25,replace O at position 0 with C,flow_matching,0.3,2.0,60,282
225,replace,21.0,2,),CC(CC))(-H=+3)\B72=@H)sro,CC(CC))(-H=+3)\B72=@H2sro,25,replace ) at position 21 with 2,flow_matching,0.3,2.0,60,282
226,remove,2.0,(,,CC(CC))(-H=+3)\B72=@H2sro,CCCC))(-H=+3)\B72=@H2sro,24,remove ( from position 2,flow_matching,0.3,2.0,60,282
227,replace,2.0,(,C,CCCC))(-H=+3)\B72=@H2sro,CC(C))(-H=+3)\B72=@H2sro,24,replace C at position 2 with (,flow_matching,0.3,2.0,60,282
228,replace,8.0,l,H,CC(C))(-H=+3)\B72=@H2sro,CC(C))(-l=+3)\B72=@H2sro,24,replace H at position 8 with l,flow_matching,0.3,2.0,60,282
229,replace,5.0,C,),CC(C))(-l=+3)\B72=@H2sro,CC(C)C(-l=+3)\B72=@H2sro,24,replace ) at position 5 with C,flow_matching,0.3,2.0,60,282
230,replace,7.0,=,-,CC(C)C(-l=+3)\B72=@H2sro,CC(C)C(=l=+3)\B72=@H2sro,24,replace - at position 7 with =,flow_matching,0.3,2.0,60,282
231,replace,8.0,O,l,CC(C)C(=l=+3)\B72=@H2sro,CC(C)C(=O=+3)\B72=@H2sro,24,replace l at position 8 with O,flow_matching,0.3,2.0,60,282
232,replace,9.0,),=,CC(C)C(=O=+3)\B72=@H2sro,CC(C)C(=O)+3)\B72=@H2sro,24,replace = at position 9 with ),flow_matching,0.3,2.0,60,282
233,replace,10.0,N,+,CC(C)C(=O)+3)\B72=@H2sro,CC(C)C(=O)N3)\B72=@H2sro,24,replace + at position 10 with N,flow_matching,0.3,2.0,60,282
234,replace,11.0,c,3,CC(C)C(=O)N3)\B72=@H2sro,CC(C)C(=O)Nc)\B72=@H2sro,24,replace 3 at position 11 with c,flow_matching,0.3,2.0,60,282
235,replace,12.0,1,),CC(C)C(=O)Nc)\B72=@H2sro,CC(C)C(=O)Nc1\B72=@H2sro,24,replace ) at position 12 with 1,flow_matching,0.3,2.0,60,282
236,replace,13.0,c,\,CC(C)C(=O)Nc1\B72=@H2sro,CC(C)C(=O)Nc1cB72=@H2sro,24,replace \ at position 13 with c,flow_matching,0.3,2.0,60,282
237,replace,14.0,c,B,CC(C)C(=O)Nc1cB72=@H2sro,CC(C)C(=O)Nc1cc72=@H2sro,24,replace B at position 14 with c,flow_matching,0.3,2.0,60,282
238,replace,15.0,c,7,CC(C)C(=O)Nc1cc72=@H2sro,CC(C)C(=O)Nc1ccc2=@H2sro,24,replace 7 at position 15 with c,flow_matching,0.3,2.0,60,282
239,replace,16.0,c,2,CC(C)C(=O)Nc1ccc2=@H2sro,CC(C)C(=O)Nc1cccc=@H2sro,24,replace 2 at position 16 with c,flow_matching,0.3,2.0,60,282
240,replace,17.0,(,=,CC(C)C(=O)Nc1cccc=@H2sro,CC(C)C(=O)Nc1cccc(@H2sro,24,replace = at position 17 with (,flow_matching,0.3,2.0,60,282
241,replace,18.0,N,@,CC(C)C(=O)Nc1cccc(@H2sro,CC(C)C(=O)Nc1cccc(NH2sro,24,replace @ at position 18 with N,flow_matching,0.3,2.0,60,282
242,replace,19.0,C,H,CC(C)C(=O)Nc1cccc(NH2sro,CC(C)C(=O)Nc1cccc(NC2sro,24,replace H at position 19 with C,flow_matching,0.3,2.0,60,282
243,replace,20.0,(,2,CC(C)C(=O)Nc1cccc(NC2sro,CC(C)C(=O)Nc1cccc(NC(sro,24,replace 2 at position 20 with (,flow_matching,0.3,2.0,60,282
244,replace,21.0,=,s,CC(C)C(=O)Nc1cccc(NC(sro,CC(C)C(=O)Nc1cccc(NC(=ro,24,replace s at position 21 with =,flow_matching,0.3,2.0,60,282
245,replace,22.0,O,r,CC(C)C(=O)Nc1cccc(NC(=ro,CC(C)C(=O)Nc1cccc(NC(=Oo,24,replace r at position 22 with O,flow_matching,0.3,2.0,60,282
246,replace,23.0,),o,CC(C)C(=O)Nc1cccc(NC(=Oo,CC(C)C(=O)Nc1cccc(NC(=O),24,replace o at position 23 with ),flow_matching,0.3,2.0,60,282
247,add,24.0,C,,CC(C)C(=O)Nc1cccc(NC(=O),CC(C)C(=O)Nc1cccc(NC(=O)C,25,add C at position 24,flow_matching,0.3,2.0,60,282
248,add,25.0,(,,CC(C)C(=O)Nc1cccc(NC(=O)C,CC(C)C(=O)Nc1cccc(NC(=O)C(,26,add ( at position 25,flow_matching,0.3,2.0,60,282
249,add,26.0,=,,CC(C)C(=O)Nc1cccc(NC(=O)C(,CC(C)C(=O)Nc1cccc(NC(=O)C(=,27,add = at position 26,flow_matching,0.3,2.0,60,282
250,add,27.0,O,,CC(C)C(=O)Nc1cccc(NC(=O)C(=,CC(C)C(=O)Nc1cccc(NC(=O)C(=O,28,add O at position 27,flow_matching,0.3,2.0,60,282
251,add,28.0,),,CC(C)C(=O)Nc1cccc(NC(=O)C(=O,CC(C)C(=O)Nc1cccc(NC(=O)C(=O),29,add ) at position 28,flow_matching,0.3,2.0,60,282
252,add,29.0,N,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O),CC(C)C(=O)Nc1cccc(NC(=O)C(=O)N,30,add N at position 29,flow_matching,0.3,2.0,60,282
253,add,30.0,C,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)N,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NC,31,add C at position 30,flow_matching,0.3,2.0,60,282
254,add,31.0,C,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NC,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC,32,add C at position 31,flow_matching,0.3,2.0,60,282
255,add,32.0,[,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[,33,add [ at position 32,flow_matching,0.3,2.0,60,282
256,add,33.0,C,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C,34,add C at position 33,flow_matching,0.3,2.0,60,282
257,add,34.0,@,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@,35,add @ at position 34,flow_matching,0.3,2.0,60,282
258,add,35.0,H,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H,36,add H at position 35,flow_matching,0.3,2.0,60,282
259,add,36.0,],,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H],37,add ] at position 36,flow_matching,0.3,2.0,60,282
260,add,37.0,2,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H],CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2,38,add 2 at position 37,flow_matching,0.3,2.0,60,282
261,add,38.0,C,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C,39,add C at position 38,flow_matching,0.3,2.0,60,282
262,add,39.0,[,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[,40,add [ at position 39,flow_matching,0.3,2.0,60,282
263,add,40.0,C,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C,41,add C at position 40,flow_matching,0.3,2.0,60,282
264,add,41.0,@,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@,42,add @ at position 41,flow_matching,0.3,2.0,60,282
265,add,42.0,H,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H,43,add H at position 42,flow_matching,0.3,2.0,60,282
266,add,43.0,],,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H],44,add ] at position 43,flow_matching,0.3,2.0,60,282
267,add,44.0,3,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H],CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3,45,add 3 at position 44,flow_matching,0.3,2.0,60,282
268,add,45.0,C,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3C,46,add C at position 45,flow_matching,0.3,2.0,60,282
269,add,46.0,C,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3C,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC,47,add C at position 46,flow_matching,0.3,2.0,60,282
270,add,47.0,[,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[,48,add [ at position 47,flow_matching,0.3,2.0,60,282
271,add,48.0,C,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C,49,add C at position 48,flow_matching,0.3,2.0,60,282
272,add,49.0,@,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@,50,add @ at position 49,flow_matching,0.3,2.0,60,282
273,add,50.0,@,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@,51,add @ at position 50,flow_matching,0.3,2.0,60,282
274,add,51.0,H,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H,52,add H at position 51,flow_matching,0.3,2.0,60,282
275,add,52.0,],,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H],53,add ] at position 52,flow_matching,0.3,2.0,60,282
276,add,53.0,2,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H],CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2,54,add 2 at position 53,flow_matching,0.3,2.0,60,282
277,add,54.0,C,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C,55,add C at position 54,flow_matching,0.3,2.0,60,282
278,add,55.0,3,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C3,56,add 3 at position 55,flow_matching,0.3,2.0,60,282
279,add,56.0,),,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C3,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C3),57,add ) at position 56,flow_matching,0.3,2.0,60,282
280,add,57.0,c,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C3),CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C3)c,58,add c at position 57,flow_matching,0.3,2.0,60,282
281,add,58.0,1,,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C3)c,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C3)c1,59,add 1 at position 58,flow_matching,0.3,2.0,60,282
282,add,59.0,"
",,CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C3)c1,"CC(C)C(=O)Nc1cccc(NC(=O)C(=O)NCC[C@H]2C[C@H]3CC[C@@H]2C3)c1
",60,"add 
 at position 59",flow_matching,0.3,2.0,60,282
