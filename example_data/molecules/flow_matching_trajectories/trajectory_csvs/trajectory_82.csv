step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,62,146
1,add,0.0,[,,,[,1,add [ at position 0,flow_matching,0.3,2.0,62,146
2,remove,0.0,[,,[,,0,remove [ from position 0,flow_matching,0.3,2.0,62,146
3,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,62,146
4,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,62,146
5,add,0.0,S,,,S,1,add S at position 0,flow_matching,0.3,2.0,62,146
6,remove,0.0,S,,S,,0,remove S from position 0,flow_matching,0.3,2.0,62,146
7,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,62,146
8,add,1.0,2,,c,c2,2,add 2 at position 1,flow_matching,0.3,2.0,62,146
9,remove,0.0,c,,c2,2,1,remove c from position 0,flow_matching,0.3,2.0,62,146
10,replace,0.0,O,2,2,O,1,replace 2 at position 0 with O,flow_matching,0.3,2.0,62,146
11,replace,0.0,@,O,O,@,1,replace O at position 0 with @,flow_matching,0.3,2.0,62,146
12,replace,0.0,O,@,@,O,1,replace @ at position 0 with O,flow_matching,0.3,2.0,62,146
13,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,62,146
14,add,2.0,C,,O=,O=C,3,add C at position 2,flow_matching,0.3,2.0,62,146
15,remove,1.0,=,,O=C,OC,2,remove = from position 1,flow_matching,0.3,2.0,62,146
16,add,2.0,s,,OC,OCs,3,add s at position 2,flow_matching,0.3,2.0,62,146
17,remove,2.0,s,,OCs,OC,2,remove s from position 2,flow_matching,0.3,2.0,62,146
18,replace,1.0,=,C,OC,O=,2,replace C at position 1 with =,flow_matching,0.3,2.0,62,146
19,add,2.0,C,,O=,O=C,3,add C at position 2,flow_matching,0.3,2.0,62,146
20,replace,2.0,5,C,O=C,O=5,3,replace C at position 2 with 5,flow_matching,0.3,2.0,62,146
21,remove,0.0,O,,O=5,=5,2,remove O from position 0,flow_matching,0.3,2.0,62,146
22,add,2.0,],,=5,=5],3,add ] at position 2,flow_matching,0.3,2.0,62,146
23,replace,0.0,O,=,=5],O5],3,replace = at position 0 with O,flow_matching,0.3,2.0,62,146
24,add,3.0,1,,O5],O5]1,4,add 1 at position 3,flow_matching,0.3,2.0,62,146
25,replace,1.0,=,5,O5]1,O=]1,4,replace 5 at position 1 with =,flow_matching,0.3,2.0,62,146
26,remove,0.0,O,,O=]1,=]1,3,remove O from position 0,flow_matching,0.3,2.0,62,146
27,add,0.0,n,,=]1,n=]1,4,add n at position 0,flow_matching,0.3,2.0,62,146
28,replace,3.0,I,1,n=]1,n=]I,4,replace 1 at position 3 with I,flow_matching,0.3,2.0,62,146
29,add,0.0,s,,n=]I,sn=]I,5,add s at position 0,flow_matching,0.3,2.0,62,146
30,replace,1.0,s,n,sn=]I,ss=]I,5,replace n at position 1 with s,flow_matching,0.3,2.0,62,146
31,add,0.0,],,ss=]I,]ss=]I,6,add ] at position 0,flow_matching,0.3,2.0,62,146
32,add,5.0,[,,]ss=]I,]ss=][I,7,add [ at position 5,flow_matching,0.3,2.0,62,146
33,replace,5.0,F,[,]ss=][I,]ss=]FI,7,replace [ at position 5 with F,flow_matching,0.3,2.0,62,146
34,replace,3.0,B,=,]ss=]FI,]ssB]FI,7,replace = at position 3 with B,flow_matching,0.3,2.0,62,146
35,add,3.0,l,,]ssB]FI,]sslB]FI,8,add l at position 3,flow_matching,0.3,2.0,62,146
36,add,5.0,c,,]sslB]FI,]sslBc]FI,9,add c at position 5,flow_matching,0.3,2.0,62,146
37,replace,0.0,O,],]sslBc]FI,OsslBc]FI,9,replace ] at position 0 with O,flow_matching,0.3,2.0,62,146
38,replace,1.0,=,s,OsslBc]FI,O=slBc]FI,9,replace s at position 1 with =,flow_matching,0.3,2.0,62,146
39,replace,2.0,C,s,O=slBc]FI,O=ClBc]FI,9,replace s at position 2 with C,flow_matching,0.3,2.0,62,146
40,add,1.0,=,,O=ClBc]FI,O==ClBc]FI,10,add = at position 1,flow_matching,0.3,2.0,62,146
41,replace,2.0,C,=,O==ClBc]FI,O=CClBc]FI,10,replace = at position 2 with C,flow_matching,0.3,2.0,62,146
42,add,3.0,#,,O=CClBc]FI,O=C#ClBc]FI,11,add # at position 3,flow_matching,0.3,2.0,62,146
43,replace,3.0,(,#,O=C#ClBc]FI,O=C(ClBc]FI,11,replace # at position 3 with (,flow_matching,0.3,2.0,62,146
44,replace,4.0,N,C,O=C(ClBc]FI,O=C(NlBc]FI,11,replace C at position 4 with N,flow_matching,0.3,2.0,62,146
45,replace,4.0,/,N,O=C(NlBc]FI,O=C(/lBc]FI,11,replace N at position 4 with /,flow_matching,0.3,2.0,62,146
46,add,9.0,@,,O=C(/lBc]FI,O=C(/lBc]@FI,12,add @ at position 9,flow_matching,0.3,2.0,62,146
47,replace,3.0,6,(,O=C(/lBc]@FI,O=C6/lBc]@FI,12,replace ( at position 3 with 6,flow_matching,0.3,2.0,62,146
48,replace,9.0,=,@,O=C6/lBc]@FI,O=C6/lBc]=FI,12,replace @ at position 9 with =,flow_matching,0.3,2.0,62,146
49,replace,1.0,s,=,O=C6/lBc]=FI,OsC6/lBc]=FI,12,replace = at position 1 with s,flow_matching,0.3,2.0,62,146
50,remove,0.0,O,,OsC6/lBc]=FI,sC6/lBc]=FI,11,remove O from position 0,flow_matching,0.3,2.0,62,146
51,replace,0.0,O,s,sC6/lBc]=FI,OC6/lBc]=FI,11,replace s at position 0 with O,flow_matching,0.3,2.0,62,146
52,replace,10.0,#,I,OC6/lBc]=FI,OC6/lBc]=F#,11,replace I at position 10 with #,flow_matching,0.3,2.0,62,146
53,replace,10.0,\,#,OC6/lBc]=F#,OC6/lBc]=F\,11,replace # at position 10 with \,flow_matching,0.3,2.0,62,146
54,replace,1.0,=,C,OC6/lBc]=F\,O=6/lBc]=F\,11,replace C at position 1 with =,flow_matching,0.3,2.0,62,146
55,remove,5.0,B,,O=6/lBc]=F\,O=6/lc]=F\,10,remove B from position 5,flow_matching,0.3,2.0,62,146
56,add,0.0,6,,O=6/lc]=F\,6O=6/lc]=F\,11,add 6 at position 0,flow_matching,0.3,2.0,62,146
57,add,10.0,c,,6O=6/lc]=F\,6O=6/lc]=Fc\,12,add c at position 10,flow_matching,0.3,2.0,62,146
58,replace,0.0,O,6,6O=6/lc]=Fc\,OO=6/lc]=Fc\,12,replace 6 at position 0 with O,flow_matching,0.3,2.0,62,146
59,replace,4.0,[,/,OO=6/lc]=Fc\,OO=6[lc]=Fc\,12,replace / at position 4 with [,flow_matching,0.3,2.0,62,146
60,add,8.0,\,,OO=6[lc]=Fc\,OO=6[lc]\=Fc\,13,add \ at position 8,flow_matching,0.3,2.0,62,146
61,remove,1.0,O,,OO=6[lc]\=Fc\,O=6[lc]\=Fc\,12,remove O from position 1,flow_matching,0.3,2.0,62,146
62,remove,3.0,[,,O=6[lc]\=Fc\,O=6lc]\=Fc\,11,remove [ from position 3,flow_matching,0.3,2.0,62,146
63,add,4.0,5,,O=6lc]\=Fc\,O=6l5c]\=Fc\,12,add 5 at position 4,flow_matching,0.3,2.0,62,146
64,add,6.0,/,,O=6l5c]\=Fc\,O=6l5c/]\=Fc\,13,add / at position 6,flow_matching,0.3,2.0,62,146
65,remove,12.0,\,,O=6l5c/]\=Fc\,O=6l5c/]\=Fc,12,remove \ from position 12,flow_matching,0.3,2.0,62,146
66,remove,2.0,6,,O=6l5c/]\=Fc,O=l5c/]\=Fc,11,remove 6 from position 2,flow_matching,0.3,2.0,62,146
67,replace,4.0,7,c,O=l5c/]\=Fc,O=l57/]\=Fc,11,replace c at position 4 with 7,flow_matching,0.3,2.0,62,146
68,replace,7.0,s,\,O=l57/]\=Fc,O=l57/]s=Fc,11,replace \ at position 7 with s,flow_matching,0.3,2.0,62,146
69,replace,2.0,C,l,O=l57/]s=Fc,O=C57/]s=Fc,11,replace l at position 2 with C,flow_matching,0.3,2.0,62,146
70,add,5.0,=,,O=C57/]s=Fc,O=C57=/]s=Fc,12,add = at position 5,flow_matching,0.3,2.0,62,146
71,replace,3.0,(,5,O=C57=/]s=Fc,O=C(7=/]s=Fc,12,replace 5 at position 3 with (,flow_matching,0.3,2.0,62,146
72,add,4.0,-,,O=C(7=/]s=Fc,O=C(-7=/]s=Fc,13,add - at position 4,flow_matching,0.3,2.0,62,146
73,replace,4.0,N,-,O=C(-7=/]s=Fc,O=C(N7=/]s=Fc,13,replace - at position 4 with N,flow_matching,0.3,2.0,62,146
74,replace,5.0,c,7,O=C(N7=/]s=Fc,O=C(Nc=/]s=Fc,13,replace 7 at position 5 with c,flow_matching,0.3,2.0,62,146
75,remove,12.0,c,,O=C(Nc=/]s=Fc,O=C(Nc=/]s=F,12,remove c from position 12,flow_matching,0.3,2.0,62,146
76,add,3.0,\,,O=C(Nc=/]s=F,O=C\(Nc=/]s=F,13,add \ at position 3,flow_matching,0.3,2.0,62,146
77,add,0.0,O,,O=C\(Nc=/]s=F,OO=C\(Nc=/]s=F,14,add O at position 0,flow_matching,0.3,2.0,62,146
78,replace,1.0,=,O,OO=C\(Nc=/]s=F,O==C\(Nc=/]s=F,14,replace O at position 1 with =,flow_matching,0.3,2.0,62,146
79,replace,2.0,C,=,O==C\(Nc=/]s=F,O=CC\(Nc=/]s=F,14,replace = at position 2 with C,flow_matching,0.3,2.0,62,146
80,remove,1.0,=,,O=CC\(Nc=/]s=F,OCC\(Nc=/]s=F,13,remove = from position 1,flow_matching,0.3,2.0,62,146
81,add,13.0,l,,OCC\(Nc=/]s=F,OCC\(Nc=/]s=Fl,14,add l at position 13,flow_matching,0.3,2.0,62,146
82,replace,8.0,],/,OCC\(Nc=/]s=Fl,OCC\(Nc=]]s=Fl,14,replace / at position 8 with ],flow_matching,0.3,2.0,62,146
83,replace,1.0,3,C,OCC\(Nc=]]s=Fl,O3C\(Nc=]]s=Fl,14,replace C at position 1 with 3,flow_matching,0.3,2.0,62,146
84,add,0.0,-,,O3C\(Nc=]]s=Fl,-O3C\(Nc=]]s=Fl,15,add - at position 0,flow_matching,0.3,2.0,62,146
85,replace,0.0,O,-,-O3C\(Nc=]]s=Fl,OO3C\(Nc=]]s=Fl,15,replace - at position 0 with O,flow_matching,0.3,2.0,62,146
86,add,10.0,F,,OO3C\(Nc=]]s=Fl,OO3C\(Nc=]F]s=Fl,16,add F at position 10,flow_matching,0.3,2.0,62,146
87,replace,1.0,=,O,OO3C\(Nc=]F]s=Fl,O=3C\(Nc=]F]s=Fl,16,replace O at position 1 with =,flow_matching,0.3,2.0,62,146
88,replace,2.0,C,3,O=3C\(Nc=]F]s=Fl,O=CC\(Nc=]F]s=Fl,16,replace 3 at position 2 with C,flow_matching,0.3,2.0,62,146
89,replace,3.0,(,C,O=CC\(Nc=]F]s=Fl,O=C(\(Nc=]F]s=Fl,16,replace C at position 3 with (,flow_matching,0.3,2.0,62,146
90,replace,4.0,N,\,O=C(\(Nc=]F]s=Fl,O=C(N(Nc=]F]s=Fl,16,replace \ at position 4 with N,flow_matching,0.3,2.0,62,146
91,replace,5.0,c,(,O=C(N(Nc=]F]s=Fl,O=C(NcNc=]F]s=Fl,16,replace ( at position 5 with c,flow_matching,0.3,2.0,62,146
92,replace,6.0,1,N,O=C(NcNc=]F]s=Fl,O=C(Nc1c=]F]s=Fl,16,replace N at position 6 with 1,flow_matching,0.3,2.0,62,146
93,replace,8.0,c,=,O=C(Nc1c=]F]s=Fl,O=C(Nc1cc]F]s=Fl,16,replace = at position 8 with c,flow_matching,0.3,2.0,62,146
94,replace,9.0,c,],O=C(Nc1cc]F]s=Fl,O=C(Nc1cccF]s=Fl,16,replace ] at position 9 with c,flow_matching,0.3,2.0,62,146
95,replace,10.0,(,F,O=C(Nc1cccF]s=Fl,O=C(Nc1ccc(]s=Fl,16,replace F at position 10 with (,flow_matching,0.3,2.0,62,146
96,replace,11.0,-,],O=C(Nc1ccc(]s=Fl,O=C(Nc1ccc(-s=Fl,16,replace ] at position 11 with -,flow_matching,0.3,2.0,62,146
97,replace,12.0,c,s,O=C(Nc1ccc(-s=Fl,O=C(Nc1ccc(-c=Fl,16,replace s at position 12 with c,flow_matching,0.3,2.0,62,146
98,replace,13.0,2,=,O=C(Nc1ccc(-c=Fl,O=C(Nc1ccc(-c2Fl,16,replace = at position 13 with 2,flow_matching,0.3,2.0,62,146
99,replace,14.0,n,F,O=C(Nc1ccc(-c2Fl,O=C(Nc1ccc(-c2nl,16,replace F at position 14 with n,flow_matching,0.3,2.0,62,146
100,replace,15.0,c,l,O=C(Nc1ccc(-c2nl,O=C(Nc1ccc(-c2nc,16,replace l at position 15 with c,flow_matching,0.3,2.0,62,146
101,add,16.0,3,,O=C(Nc1ccc(-c2nc,O=C(Nc1ccc(-c2nc3,17,add 3 at position 16,flow_matching,0.3,2.0,62,146
102,add,17.0,c,,O=C(Nc1ccc(-c2nc3,O=C(Nc1ccc(-c2nc3c,18,add c at position 17,flow_matching,0.3,2.0,62,146
103,add,18.0,c,,O=C(Nc1ccc(-c2nc3c,O=C(Nc1ccc(-c2nc3cc,19,add c at position 18,flow_matching,0.3,2.0,62,146
104,add,19.0,c,,O=C(Nc1ccc(-c2nc3cc,O=C(Nc1ccc(-c2nc3ccc,20,add c at position 19,flow_matching,0.3,2.0,62,146
105,add,20.0,c,,O=C(Nc1ccc(-c2nc3ccc,O=C(Nc1ccc(-c2nc3cccc,21,add c at position 20,flow_matching,0.3,2.0,62,146
106,add,21.0,c,,O=C(Nc1ccc(-c2nc3cccc,O=C(Nc1ccc(-c2nc3ccccc,22,add c at position 21,flow_matching,0.3,2.0,62,146
107,add,22.0,3,,O=C(Nc1ccc(-c2nc3ccccc,O=C(Nc1ccc(-c2nc3ccccc3,23,add 3 at position 22,flow_matching,0.3,2.0,62,146
108,add,23.0,o,,O=C(Nc1ccc(-c2nc3ccccc3,O=C(Nc1ccc(-c2nc3ccccc3o,24,add o at position 23,flow_matching,0.3,2.0,62,146
109,add,24.0,2,,O=C(Nc1ccc(-c2nc3ccccc3o,O=C(Nc1ccc(-c2nc3ccccc3o2,25,add 2 at position 24,flow_matching,0.3,2.0,62,146
110,add,25.0,),,O=C(Nc1ccc(-c2nc3ccccc3o2,O=C(Nc1ccc(-c2nc3ccccc3o2),26,add ) at position 25,flow_matching,0.3,2.0,62,146
111,add,26.0,c,,O=C(Nc1ccc(-c2nc3ccccc3o2),O=C(Nc1ccc(-c2nc3ccccc3o2)c,27,add c at position 26,flow_matching,0.3,2.0,62,146
112,add,27.0,c,,O=C(Nc1ccc(-c2nc3ccccc3o2)c,O=C(Nc1ccc(-c2nc3ccccc3o2)cc,28,add c at position 27,flow_matching,0.3,2.0,62,146
113,add,28.0,1,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1,29,add 1 at position 28,flow_matching,0.3,2.0,62,146
114,add,29.0,),,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1),30,add ) at position 29,flow_matching,0.3,2.0,62,146
115,add,30.0,[,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1),O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[,31,add [ at position 30,flow_matching,0.3,2.0,62,146
116,add,31.0,C,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C,32,add C at position 31,flow_matching,0.3,2.0,62,146
117,add,32.0,@,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@,33,add @ at position 32,flow_matching,0.3,2.0,62,146
118,add,33.0,H,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H,34,add H at position 33,flow_matching,0.3,2.0,62,146
119,add,34.0,],,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H],35,add ] at position 34,flow_matching,0.3,2.0,62,146
120,add,35.0,1,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H],O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1,36,add 1 at position 35,flow_matching,0.3,2.0,62,146
121,add,36.0,C,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1C,37,add C at position 36,flow_matching,0.3,2.0,62,146
122,add,37.0,C,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1C,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CC,38,add C at position 37,flow_matching,0.3,2.0,62,146
123,add,38.0,C,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CC,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCC,39,add C at position 38,flow_matching,0.3,2.0,62,146
124,add,39.0,N,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCC,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN,40,add N at position 39,flow_matching,0.3,2.0,62,146
125,add,40.0,1,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1,41,add 1 at position 40,flow_matching,0.3,2.0,62,146
126,add,41.0,S,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S,42,add S at position 41,flow_matching,0.3,2.0,62,146
127,add,42.0,(,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(,43,add ( at position 42,flow_matching,0.3,2.0,62,146
128,add,43.0,=,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=,44,add = at position 43,flow_matching,0.3,2.0,62,146
129,add,44.0,O,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O,45,add O at position 44,flow_matching,0.3,2.0,62,146
130,add,45.0,),,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O),46,add ) at position 45,flow_matching,0.3,2.0,62,146
131,add,46.0,(,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O),O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(,47,add ( at position 46,flow_matching,0.3,2.0,62,146
132,add,47.0,=,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=,48,add = at position 47,flow_matching,0.3,2.0,62,146
133,add,48.0,O,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O,49,add O at position 48,flow_matching,0.3,2.0,62,146
134,add,49.0,),,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O),50,add ) at position 49,flow_matching,0.3,2.0,62,146
135,add,50.0,c,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O),O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c,51,add c at position 50,flow_matching,0.3,2.0,62,146
136,add,51.0,1,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1,52,add 1 at position 51,flow_matching,0.3,2.0,62,146
137,add,52.0,c,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1c,53,add c at position 52,flow_matching,0.3,2.0,62,146
138,add,53.0,c,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1c,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1cc,54,add c at position 53,flow_matching,0.3,2.0,62,146
139,add,54.0,c,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1cc,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc,55,add c at position 54,flow_matching,0.3,2.0,62,146
140,add,55.0,(,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(,56,add ( at position 55,flow_matching,0.3,2.0,62,146
141,add,56.0,F,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F,57,add F at position 56,flow_matching,0.3,2.0,62,146
142,add,57.0,),,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F),58,add ) at position 57,flow_matching,0.3,2.0,62,146
143,add,58.0,c,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F),O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F)c,59,add c at position 58,flow_matching,0.3,2.0,62,146
144,add,59.0,c,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F)c,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F)cc,60,add c at position 59,flow_matching,0.3,2.0,62,146
145,add,60.0,1,,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F)cc,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F)cc1,61,add 1 at position 60,flow_matching,0.3,2.0,62,146
146,add,61.0,"
",,O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F)cc1,"O=C(Nc1ccc(-c2nc3ccccc3o2)cc1)[C@H]1CCCN1S(=O)(=O)c1ccc(F)cc1
",62,"add 
 at position 61",flow_matching,0.3,2.0,62,146
