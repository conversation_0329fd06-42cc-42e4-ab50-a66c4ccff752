step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,24,75
1,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,24,75
2,replace,0.0,C,6,6,C,1,replace 6 at position 0 with C,flow_matching,0.3,2.0,24,75
3,add,1.0,@,,C,C@,2,add @ at position 1,flow_matching,0.3,2.0,24,75
4,add,2.0,5,,C@,C@5,3,add 5 at position 2,flow_matching,0.3,2.0,24,75
5,remove,1.0,@,,C@5,C5,2,remove @ from position 1,flow_matching,0.3,2.0,24,75
6,add,0.0,6,,C5,6C5,3,add 6 at position 0,flow_matching,0.3,2.0,24,75
7,add,0.0,I,,6C5,I6C5,4,add I at position 0,flow_matching,0.3,2.0,24,75
8,add,0.0,o,,I6C5,oI6C5,5,add o at position 0,flow_matching,0.3,2.0,24,75
9,replace,0.0,C,o,oI6C5,CI6C5,5,replace o at position 0 with C,flow_matching,0.3,2.0,24,75
10,add,1.0,S,,CI6C5,CSI6C5,6,add S at position 1,flow_matching,0.3,2.0,24,75
11,replace,1.0,@,S,CSI6C5,C@I6C5,6,replace S at position 1 with @,flow_matching,0.3,2.0,24,75
12,add,3.0,=,,C@I6C5,C@I=6C5,7,add = at position 3,flow_matching,0.3,2.0,24,75
13,add,6.0,4,,C@I=6C5,C@I=6C45,8,add 4 at position 6,flow_matching,0.3,2.0,24,75
14,replace,1.0,C,@,C@I=6C45,CCI=6C45,8,replace @ at position 1 with C,flow_matching,0.3,2.0,24,75
15,remove,2.0,I,,CCI=6C45,CC=6C45,7,remove I from position 2,flow_matching,0.3,2.0,24,75
16,replace,2.0,N,=,CC=6C45,CCN6C45,7,replace = at position 2 with N,flow_matching,0.3,2.0,24,75
17,add,6.0,I,,CCN6C45,CCN6C4I5,8,add I at position 6,flow_matching,0.3,2.0,24,75
18,add,6.0,B,,CCN6C4I5,CCN6C4BI5,9,add B at position 6,flow_matching,0.3,2.0,24,75
19,replace,3.0,c,6,CCN6C4BI5,CCNcC4BI5,9,replace 6 at position 3 with c,flow_matching,0.3,2.0,24,75
20,add,1.0,[,,CCNcC4BI5,C[CNcC4BI5,10,add [ at position 1,flow_matching,0.3,2.0,24,75
21,add,6.0,o,,C[CNcC4BI5,C[CNcCo4BI5,11,add o at position 6,flow_matching,0.3,2.0,24,75
22,replace,6.0,O,o,C[CNcCo4BI5,C[CNcCO4BI5,11,replace o at position 6 with O,flow_matching,0.3,2.0,24,75
23,add,1.0,O,,C[CNcCO4BI5,CO[CNcCO4BI5,12,add O at position 1,flow_matching,0.3,2.0,24,75
24,remove,0.0,C,,CO[CNcCO4BI5,O[CNcCO4BI5,11,remove C from position 0,flow_matching,0.3,2.0,24,75
25,replace,2.0,1,C,O[CNcCO4BI5,O[1NcCO4BI5,11,replace C at position 2 with 1,flow_matching,0.3,2.0,24,75
26,replace,0.0,C,O,O[1NcCO4BI5,C[1NcCO4BI5,11,replace O at position 0 with C,flow_matching,0.3,2.0,24,75
27,replace,1.0,C,[,C[1NcCO4BI5,CC1NcCO4BI5,11,replace [ at position 1 with C,flow_matching,0.3,2.0,24,75
28,remove,3.0,N,,CC1NcCO4BI5,CC1cCO4BI5,10,remove N from position 3,flow_matching,0.3,2.0,24,75
29,replace,3.0,S,c,CC1cCO4BI5,CC1SCO4BI5,10,replace c at position 3 with S,flow_matching,0.3,2.0,24,75
30,replace,2.0,N,1,CC1SCO4BI5,CCNSCO4BI5,10,replace 1 at position 2 with N,flow_matching,0.3,2.0,24,75
31,replace,1.0,I,C,CCNSCO4BI5,CINSCO4BI5,10,replace C at position 1 with I,flow_matching,0.3,2.0,24,75
32,add,8.0,),,CINSCO4BI5,CINSCO4B)I5,11,add ) at position 8,flow_matching,0.3,2.0,24,75
33,replace,7.0,s,B,CINSCO4B)I5,CINSCO4s)I5,11,replace B at position 7 with s,flow_matching,0.3,2.0,24,75
34,add,6.0,(,,CINSCO4s)I5,CINSCO(4s)I5,12,add ( at position 6,flow_matching,0.3,2.0,24,75
35,add,0.0,(,,CINSCO(4s)I5,(CINSCO(4s)I5,13,add ( at position 0,flow_matching,0.3,2.0,24,75
36,remove,3.0,N,,(CINSCO(4s)I5,(CISCO(4s)I5,12,remove N from position 3,flow_matching,0.3,2.0,24,75
37,add,6.0,(,,(CISCO(4s)I5,(CISCO((4s)I5,13,add ( at position 6,flow_matching,0.3,2.0,24,75
38,replace,9.0,2,s,(CISCO((4s)I5,(CISCO((42)I5,13,replace s at position 9 with 2,flow_matching,0.3,2.0,24,75
39,add,10.0,r,,(CISCO((42)I5,(CISCO((42r)I5,14,add r at position 10,flow_matching,0.3,2.0,24,75
40,add,11.0,I,,(CISCO((42r)I5,(CISCO((42rI)I5,15,add I at position 11,flow_matching,0.3,2.0,24,75
41,remove,6.0,(,,(CISCO((42rI)I5,(CISCO(42rI)I5,14,remove ( from position 6,flow_matching,0.3,2.0,24,75
42,replace,0.0,C,(,(CISCO(42rI)I5,CCISCO(42rI)I5,14,replace ( at position 0 with C,flow_matching,0.3,2.0,24,75
43,replace,7.0,H,4,CCISCO(42rI)I5,CCISCO(H2rI)I5,14,replace 4 at position 7 with H,flow_matching,0.3,2.0,24,75
44,add,3.0,n,,CCISCO(H2rI)I5,CCInSCO(H2rI)I5,15,add n at position 3,flow_matching,0.3,2.0,24,75
45,replace,2.0,N,I,CCInSCO(H2rI)I5,CCNnSCO(H2rI)I5,15,replace I at position 2 with N,flow_matching,0.3,2.0,24,75
46,replace,2.0,o,N,CCNnSCO(H2rI)I5,CConSCO(H2rI)I5,15,replace N at position 2 with o,flow_matching,0.3,2.0,24,75
47,add,12.0,r,,CConSCO(H2rI)I5,CConSCO(H2rIr)I5,16,add r at position 12,flow_matching,0.3,2.0,24,75
48,add,7.0,C,,CConSCO(H2rIr)I5,CConSCOC(H2rIr)I5,17,add C at position 7,flow_matching,0.3,2.0,24,75
49,replace,2.0,N,o,CConSCOC(H2rIr)I5,CCNnSCOC(H2rIr)I5,17,replace o at position 2 with N,flow_matching,0.3,2.0,24,75
50,replace,3.0,c,n,CCNnSCOC(H2rIr)I5,CCNcSCOC(H2rIr)I5,17,replace n at position 3 with c,flow_matching,0.3,2.0,24,75
51,add,9.0,O,,CCNcSCOC(H2rIr)I5,CCNcSCOC(OH2rIr)I5,18,add O at position 9,flow_matching,0.3,2.0,24,75
52,add,9.0,#,,CCNcSCOC(OH2rIr)I5,CCNcSCOC(#OH2rIr)I5,19,add # at position 9,flow_matching,0.3,2.0,24,75
53,remove,18.0,5,,CCNcSCOC(#OH2rIr)I5,CCNcSCOC(#OH2rIr)I,18,remove 5 from position 18,flow_matching,0.3,2.0,24,75
54,replace,5.0,-,C,CCNcSCOC(#OH2rIr)I,CCNcS-OC(#OH2rIr)I,18,replace C at position 5 with -,flow_matching,0.3,2.0,24,75
55,replace,4.0,1,S,CCNcS-OC(#OH2rIr)I,CCNc1-OC(#OH2rIr)I,18,replace S at position 4 with 1,flow_matching,0.3,2.0,24,75
56,replace,1.0,l,C,CCNc1-OC(#OH2rIr)I,ClNc1-OC(#OH2rIr)I,18,replace C at position 1 with l,flow_matching,0.3,2.0,24,75
57,add,9.0,6,,ClNc1-OC(#OH2rIr)I,ClNc1-OC(6#OH2rIr)I,19,add 6 at position 9,flow_matching,0.3,2.0,24,75
58,replace,1.0,C,l,ClNc1-OC(6#OH2rIr)I,CCNc1-OC(6#OH2rIr)I,19,replace l at position 1 with C,flow_matching,0.3,2.0,24,75
59,replace,5.0,n,-,CCNc1-OC(6#OH2rIr)I,CCNc1nOC(6#OH2rIr)I,19,replace - at position 5 with n,flow_matching,0.3,2.0,24,75
60,replace,6.0,c,O,CCNc1nOC(6#OH2rIr)I,CCNc1ncC(6#OH2rIr)I,19,replace O at position 6 with c,flow_matching,0.3,2.0,24,75
61,replace,7.0,c,C,CCNc1ncC(6#OH2rIr)I,CCNc1ncc(6#OH2rIr)I,19,replace C at position 7 with c,flow_matching,0.3,2.0,24,75
62,replace,9.0,C,6,CCNc1ncc(6#OH2rIr)I,CCNc1ncc(C#OH2rIr)I,19,replace 6 at position 9 with C,flow_matching,0.3,2.0,24,75
63,replace,10.0,O,#,CCNc1ncc(C#OH2rIr)I,CCNc1ncc(COOH2rIr)I,19,replace # at position 10 with O,flow_matching,0.3,2.0,24,75
64,replace,11.0,C,O,CCNc1ncc(COOH2rIr)I,CCNc1ncc(COCH2rIr)I,19,replace O at position 11 with C,flow_matching,0.3,2.0,24,75
65,replace,12.0,C,H,CCNc1ncc(COCH2rIr)I,CCNc1ncc(COCC2rIr)I,19,replace H at position 12 with C,flow_matching,0.3,2.0,24,75
66,replace,14.0,C,r,CCNc1ncc(COCC2rIr)I,CCNc1ncc(COCC2CIr)I,19,replace r at position 14 with C,flow_matching,0.3,2.0,24,75
67,replace,15.0,C,I,CCNc1ncc(COCC2CIr)I,CCNc1ncc(COCC2CCr)I,19,replace I at position 15 with C,flow_matching,0.3,2.0,24,75
68,replace,16.0,C,r,CCNc1ncc(COCC2CCr)I,CCNc1ncc(COCC2CCC)I,19,replace r at position 16 with C,flow_matching,0.3,2.0,24,75
69,replace,17.0,C,),CCNc1ncc(COCC2CCC)I,CCNc1ncc(COCC2CCCCI,19,replace ) at position 17 with C,flow_matching,0.3,2.0,24,75
70,replace,18.0,C,I,CCNc1ncc(COCC2CCCCI,CCNc1ncc(COCC2CCCCC,19,replace I at position 18 with C,flow_matching,0.3,2.0,24,75
71,add,19.0,2,,CCNc1ncc(COCC2CCCCC,CCNc1ncc(COCC2CCCCC2,20,add 2 at position 19,flow_matching,0.3,2.0,24,75
72,add,20.0,),,CCNc1ncc(COCC2CCCCC2,CCNc1ncc(COCC2CCCCC2),21,add ) at position 20,flow_matching,0.3,2.0,24,75
73,add,21.0,s,,CCNc1ncc(COCC2CCCCC2),CCNc1ncc(COCC2CCCCC2)s,22,add s at position 21,flow_matching,0.3,2.0,24,75
74,add,22.0,1,,CCNc1ncc(COCC2CCCCC2)s,CCNc1ncc(COCC2CCCCC2)s1,23,add 1 at position 22,flow_matching,0.3,2.0,24,75
75,add,23.0,"
",,CCNc1ncc(COCC2CCCCC2)s1,"CCNc1ncc(COCC2CCCCC2)s1
",24,"add 
 at position 23",flow_matching,0.3,2.0,24,75
