step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,29,81
1,add,0.0,#,,,#,1,add # at position 0,flow_matching,0.3,2.0,29,81
2,replace,0.0,O,#,#,O,1,replace # at position 0 with O,flow_matching,0.3,2.0,29,81
3,replace,0.0,N,O,O,N,1,replace O at position 0 with N,flow_matching,0.3,2.0,29,81
4,add,1.0,C,,N,NC,2,add C at position 1,flow_matching,0.3,2.0,29,81
5,replace,1.0,),C,NC,N),2,replace C at position 1 with ),flow_matching,0.3,2.0,29,81
6,replace,0.0,c,N,N),c),2,replace N at position 0 with c,flow_matching,0.3,2.0,29,81
7,add,2.0,7,,c),c)7,3,add 7 at position 2,flow_matching,0.3,2.0,29,81
8,remove,1.0,),,c)7,c7,2,remove ) from position 1,flow_matching,0.3,2.0,29,81
9,replace,0.0,N,c,c7,N7,2,replace c at position 0 with N,flow_matching,0.3,2.0,29,81
10,remove,0.0,N,,N7,7,1,remove N from position 0,flow_matching,0.3,2.0,29,81
11,remove,0.0,7,,7,,0,remove 7 from position 0,flow_matching,0.3,2.0,29,81
12,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,29,81
13,add,0.0,1,,-,1-,2,add 1 at position 0,flow_matching,0.3,2.0,29,81
14,replace,0.0,N,1,1-,N-,2,replace 1 at position 0 with N,flow_matching,0.3,2.0,29,81
15,replace,0.0,H,N,N-,H-,2,replace N at position 0 with H,flow_matching,0.3,2.0,29,81
16,replace,0.0,N,H,H-,N-,2,replace H at position 0 with N,flow_matching,0.3,2.0,29,81
17,remove,1.0,-,,N-,N,1,remove - from position 1,flow_matching,0.3,2.0,29,81
18,add,1.0,C,,N,NC,2,add C at position 1,flow_matching,0.3,2.0,29,81
19,add,2.0,(,,NC,NC(,3,add ( at position 2,flow_matching,0.3,2.0,29,81
20,add,2.0,s,,NC(,NCs(,4,add s at position 2,flow_matching,0.3,2.0,29,81
21,remove,2.0,s,,NCs(,NC(,3,remove s from position 2,flow_matching,0.3,2.0,29,81
22,add,3.0,=,,NC(,NC(=,4,add = at position 3,flow_matching,0.3,2.0,29,81
23,replace,0.0,3,N,NC(=,3C(=,4,replace N at position 0 with 3,flow_matching,0.3,2.0,29,81
24,replace,0.0,N,3,3C(=,NC(=,4,replace 3 at position 0 with N,flow_matching,0.3,2.0,29,81
25,remove,0.0,N,,NC(=,C(=,3,remove N from position 0,flow_matching,0.3,2.0,29,81
26,add,3.0,N,,C(=,C(=N,4,add N at position 3,flow_matching,0.3,2.0,29,81
27,replace,1.0,S,(,C(=N,CS=N,4,replace ( at position 1 with S,flow_matching,0.3,2.0,29,81
28,replace,0.0,N,C,CS=N,NS=N,4,replace C at position 0 with N,flow_matching,0.3,2.0,29,81
29,replace,1.0,C,S,NS=N,NC=N,4,replace S at position 1 with C,flow_matching,0.3,2.0,29,81
30,remove,2.0,=,,NC=N,NCN,3,remove = from position 2,flow_matching,0.3,2.0,29,81
31,add,3.0,\,,NCN,NCN\,4,add \ at position 3,flow_matching,0.3,2.0,29,81
32,replace,2.0,(,N,NCN\,NC(\,4,replace N at position 2 with (,flow_matching,0.3,2.0,29,81
33,add,4.0,2,,NC(\,NC(\2,5,add 2 at position 4,flow_matching,0.3,2.0,29,81
34,replace,4.0,F,2,NC(\2,NC(\F,5,replace 2 at position 4 with F,flow_matching,0.3,2.0,29,81
35,add,4.0,I,,NC(\F,NC(\IF,6,add I at position 4,flow_matching,0.3,2.0,29,81
36,add,5.0,(,,NC(\IF,NC(\I(F,7,add ( at position 5,flow_matching,0.3,2.0,29,81
37,replace,3.0,=,\,NC(\I(F,NC(=I(F,7,replace \ at position 3 with =,flow_matching,0.3,2.0,29,81
38,replace,4.0,O,I,NC(=I(F,NC(=O(F,7,replace I at position 4 with O,flow_matching,0.3,2.0,29,81
39,add,2.0,[,,NC(=O(F,NC[(=O(F,8,add [ at position 2,flow_matching,0.3,2.0,29,81
40,replace,0.0,C,N,NC[(=O(F,CC[(=O(F,8,replace N at position 0 with C,flow_matching,0.3,2.0,29,81
41,replace,6.0,c,(,CC[(=O(F,CC[(=OcF,8,replace ( at position 6 with c,flow_matching,0.3,2.0,29,81
42,replace,0.0,N,C,CC[(=OcF,NC[(=OcF,8,replace C at position 0 with N,flow_matching,0.3,2.0,29,81
43,add,0.0,),,NC[(=OcF,)NC[(=OcF,9,add ) at position 0,flow_matching,0.3,2.0,29,81
44,replace,0.0,N,),)NC[(=OcF,NNC[(=OcF,9,replace ) at position 0 with N,flow_matching,0.3,2.0,29,81
45,replace,1.0,C,N,NNC[(=OcF,NCC[(=OcF,9,replace N at position 1 with C,flow_matching,0.3,2.0,29,81
46,remove,0.0,N,,NCC[(=OcF,CC[(=OcF,8,remove N from position 0,flow_matching,0.3,2.0,29,81
47,replace,0.0,N,C,CC[(=OcF,NC[(=OcF,8,replace C at position 0 with N,flow_matching,0.3,2.0,29,81
48,replace,2.0,(,[,NC[(=OcF,NC((=OcF,8,replace [ at position 2 with (,flow_matching,0.3,2.0,29,81
49,replace,3.0,=,(,NC((=OcF,NC(==OcF,8,replace ( at position 3 with =,flow_matching,0.3,2.0,29,81
50,add,5.0,@,,NC(==OcF,NC(==@OcF,9,add @ at position 5,flow_matching,0.3,2.0,29,81
51,replace,4.0,O,=,NC(==@OcF,NC(=O@OcF,9,replace = at position 4 with O,flow_matching,0.3,2.0,29,81
52,remove,4.0,O,,NC(=O@OcF,NC(=@OcF,8,remove O from position 4,flow_matching,0.3,2.0,29,81
53,replace,2.0,-,(,NC(=@OcF,NC-=@OcF,8,replace ( at position 2 with -,flow_matching,0.3,2.0,29,81
54,replace,2.0,(,-,NC-=@OcF,NC(=@OcF,8,replace - at position 2 with (,flow_matching,0.3,2.0,29,81
55,replace,4.0,O,@,NC(=@OcF,NC(=OOcF,8,replace @ at position 4 with O,flow_matching,0.3,2.0,29,81
56,add,7.0,r,,NC(=OOcF,NC(=OOcrF,9,add r at position 7,flow_matching,0.3,2.0,29,81
57,remove,4.0,O,,NC(=OOcrF,NC(=OcrF,8,remove O from position 4,flow_matching,0.3,2.0,29,81
58,replace,5.0,),c,NC(=OcrF,NC(=O)rF,8,replace c at position 5 with ),flow_matching,0.3,2.0,29,81
59,replace,6.0,C,r,NC(=O)rF,NC(=O)CF,8,replace r at position 6 with C,flow_matching,0.3,2.0,29,81
60,replace,7.0,1,F,NC(=O)CF,NC(=O)C1,8,replace F at position 7 with 1,flow_matching,0.3,2.0,29,81
61,add,8.0,(,,NC(=O)C1,NC(=O)C1(,9,add ( at position 8,flow_matching,0.3,2.0,29,81
62,add,9.0,N,,NC(=O)C1(,NC(=O)C1(N,10,add N at position 9,flow_matching,0.3,2.0,29,81
63,add,10.0,2,,NC(=O)C1(N,NC(=O)C1(N2,11,add 2 at position 10,flow_matching,0.3,2.0,29,81
64,add,11.0,C,,NC(=O)C1(N2,NC(=O)C1(N2C,12,add C at position 11,flow_matching,0.3,2.0,29,81
65,add,12.0,C,,NC(=O)C1(N2C,NC(=O)C1(N2CC,13,add C at position 12,flow_matching,0.3,2.0,29,81
66,add,13.0,C,,NC(=O)C1(N2CC,NC(=O)C1(N2CCC,14,add C at position 13,flow_matching,0.3,2.0,29,81
67,add,14.0,C,,NC(=O)C1(N2CCC,NC(=O)C1(N2CCCC,15,add C at position 14,flow_matching,0.3,2.0,29,81
68,add,15.0,2,,NC(=O)C1(N2CCCC,NC(=O)C1(N2CCCC2,16,add 2 at position 15,flow_matching,0.3,2.0,29,81
69,add,16.0,),,NC(=O)C1(N2CCCC2,NC(=O)C1(N2CCCC2),17,add ) at position 16,flow_matching,0.3,2.0,29,81
70,add,17.0,C,,NC(=O)C1(N2CCCC2),NC(=O)C1(N2CCCC2)C,18,add C at position 17,flow_matching,0.3,2.0,29,81
71,add,18.0,C,,NC(=O)C1(N2CCCC2)C,NC(=O)C1(N2CCCC2)CC,19,add C at position 18,flow_matching,0.3,2.0,29,81
72,add,19.0,[,,NC(=O)C1(N2CCCC2)CC,NC(=O)C1(N2CCCC2)CC[,20,add [ at position 19,flow_matching,0.3,2.0,29,81
73,add,20.0,N,,NC(=O)C1(N2CCCC2)CC[,NC(=O)C1(N2CCCC2)CC[N,21,add N at position 20,flow_matching,0.3,2.0,29,81
74,add,21.0,H,,NC(=O)C1(N2CCCC2)CC[N,NC(=O)C1(N2CCCC2)CC[NH,22,add H at position 21,flow_matching,0.3,2.0,29,81
75,add,22.0,2,,NC(=O)C1(N2CCCC2)CC[NH,NC(=O)C1(N2CCCC2)CC[NH2,23,add 2 at position 22,flow_matching,0.3,2.0,29,81
76,add,23.0,+,,NC(=O)C1(N2CCCC2)CC[NH2,NC(=O)C1(N2CCCC2)CC[NH2+,24,add + at position 23,flow_matching,0.3,2.0,29,81
77,add,24.0,],,NC(=O)C1(N2CCCC2)CC[NH2+,NC(=O)C1(N2CCCC2)CC[NH2+],25,add ] at position 24,flow_matching,0.3,2.0,29,81
78,add,25.0,C,,NC(=O)C1(N2CCCC2)CC[NH2+],NC(=O)C1(N2CCCC2)CC[NH2+]C,26,add C at position 25,flow_matching,0.3,2.0,29,81
79,add,26.0,C,,NC(=O)C1(N2CCCC2)CC[NH2+]C,NC(=O)C1(N2CCCC2)CC[NH2+]CC,27,add C at position 26,flow_matching,0.3,2.0,29,81
80,add,27.0,1,,NC(=O)C1(N2CCCC2)CC[NH2+]CC,NC(=O)C1(N2CCCC2)CC[NH2+]CC1,28,add 1 at position 27,flow_matching,0.3,2.0,29,81
81,add,28.0,"
",,NC(=O)C1(N2CCCC2)CC[NH2+]CC1,"NC(=O)C1(N2CCCC2)CC[NH2+]CC1
",29,"add 
 at position 28",flow_matching,0.3,2.0,29,81
