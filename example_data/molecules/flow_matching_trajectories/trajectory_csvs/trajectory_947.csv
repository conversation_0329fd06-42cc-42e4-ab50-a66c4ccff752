step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,48,107
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,48,107
2,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,48,107
3,replace,0.0,O,C,CO,OO,2,replace <PERSON> at position 0 with O,flow_matching,0.3,2.0,48,107
4,replace,1.0,@,O,OO,O@,2,replace O at position 1 with @,flow_matching,0.3,2.0,48,107
5,replace,1.0,l,@,O@,Ol,2,replace @ at position 1 with l,flow_matching,0.3,2.0,48,107
6,replace,0.0,<PERSON>,<PERSON>,<PERSON><PERSON>,Cl,2,replace <PERSON> at position 0 with <PERSON>,flow_matching,0.3,2.0,48,107
7,add,2.0,6,,Cl,Cl6,3,add 6 at position 2,flow_matching,0.3,2.0,48,107
8,replace,1.0,O,l,Cl6,CO6,3,replace l at position 1 with O,flow_matching,0.3,2.0,48,107
9,replace,2.0,c,6,CO6,COc,3,replace 6 at position 2 with c,flow_matching,0.3,2.0,48,107
10,add,3.0,1,,COc,COc1,4,add 1 at position 3,flow_matching,0.3,2.0,48,107
11,replace,2.0,N,c,COc1,CON1,4,replace c at position 2 with N,flow_matching,0.3,2.0,48,107
12,replace,2.0,-,N,CON1,CO-1,4,replace N at position 2 with -,flow_matching,0.3,2.0,48,107
13,replace,2.0,c,-,CO-1,COc1,4,replace - at position 2 with c,flow_matching,0.3,2.0,48,107
14,replace,3.0,5,1,COc1,COc5,4,replace 1 at position 3 with 5,flow_matching,0.3,2.0,48,107
15,remove,1.0,O,,COc5,Cc5,3,remove O from position 1,flow_matching,0.3,2.0,48,107
16,replace,1.0,O,c,Cc5,CO5,3,replace c at position 1 with O,flow_matching,0.3,2.0,48,107
17,remove,0.0,C,,CO5,O5,2,remove C from position 0,flow_matching,0.3,2.0,48,107
18,add,1.0,[,,O5,O[5,3,add [ at position 1,flow_matching,0.3,2.0,48,107
19,remove,1.0,[,,O[5,O5,2,remove [ from position 1,flow_matching,0.3,2.0,48,107
20,replace,0.0,C,O,O5,C5,2,replace O at position 0 with C,flow_matching,0.3,2.0,48,107
21,replace,1.0,O,5,C5,CO,2,replace 5 at position 1 with O,flow_matching,0.3,2.0,48,107
22,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,48,107
23,add,3.0,1,,COc,COc1,4,add 1 at position 3,flow_matching,0.3,2.0,48,107
24,replace,0.0,5,C,COc1,5Oc1,4,replace C at position 0 with 5,flow_matching,0.3,2.0,48,107
25,remove,2.0,c,,5Oc1,5O1,3,remove c from position 2,flow_matching,0.3,2.0,48,107
26,add,0.0,+,,5O1,+5O1,4,add + at position 0,flow_matching,0.3,2.0,48,107
27,replace,0.0,C,+,+5O1,C5O1,4,replace + at position 0 with C,flow_matching,0.3,2.0,48,107
28,add,2.0,#,,C5O1,C5#O1,5,add # at position 2,flow_matching,0.3,2.0,48,107
29,replace,1.0,3,5,C5#O1,C3#O1,5,replace 5 at position 1 with 3,flow_matching,0.3,2.0,48,107
30,replace,1.0,@,3,C3#O1,C@#O1,5,replace 3 at position 1 with @,flow_matching,0.3,2.0,48,107
31,remove,1.0,@,,C@#O1,C#O1,4,remove @ from position 1,flow_matching,0.3,2.0,48,107
32,replace,2.0,[,O,C#O1,C#[1,4,replace O at position 2 with [,flow_matching,0.3,2.0,48,107
33,remove,2.0,[,,C#[1,C#1,3,remove [ from position 2,flow_matching,0.3,2.0,48,107
34,add,2.0,5,,C#1,C#51,4,add 5 at position 2,flow_matching,0.3,2.0,48,107
35,remove,2.0,5,,C#51,C#1,3,remove 5 from position 2,flow_matching,0.3,2.0,48,107
36,remove,2.0,1,,C#1,C#,2,remove 1 from position 2,flow_matching,0.3,2.0,48,107
37,replace,1.0,O,#,C#,CO,2,replace # at position 1 with O,flow_matching,0.3,2.0,48,107
38,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,48,107
39,remove,0.0,C,,COc,Oc,2,remove C from position 0,flow_matching,0.3,2.0,48,107
40,replace,0.0,C,O,Oc,Cc,2,replace O at position 0 with C,flow_matching,0.3,2.0,48,107
41,replace,0.0,s,C,Cc,sc,2,replace C at position 0 with s,flow_matching,0.3,2.0,48,107
42,replace,0.0,C,s,sc,Cc,2,replace s at position 0 with C,flow_matching,0.3,2.0,48,107
43,add,0.0,N,,Cc,NCc,3,add N at position 0,flow_matching,0.3,2.0,48,107
44,replace,2.0,2,c,NCc,NC2,3,replace c at position 2 with 2,flow_matching,0.3,2.0,48,107
45,add,3.0,H,,NC2,NC2H,4,add H at position 3,flow_matching,0.3,2.0,48,107
46,replace,2.0,l,2,NC2H,NClH,4,replace 2 at position 2 with l,flow_matching,0.3,2.0,48,107
47,replace,0.0,C,N,NClH,CClH,4,replace N at position 0 with C,flow_matching,0.3,2.0,48,107
48,replace,1.0,O,C,CClH,COlH,4,replace C at position 1 with O,flow_matching,0.3,2.0,48,107
49,add,0.0,-,,COlH,-COlH,5,add - at position 0,flow_matching,0.3,2.0,48,107
50,replace,0.0,C,-,-COlH,CCOlH,5,replace - at position 0 with C,flow_matching,0.3,2.0,48,107
51,remove,0.0,C,,CCOlH,COlH,4,remove C from position 0,flow_matching,0.3,2.0,48,107
52,replace,2.0,c,l,COlH,COcH,4,replace l at position 2 with c,flow_matching,0.3,2.0,48,107
53,replace,3.0,1,H,COcH,COc1,4,replace H at position 3 with 1,flow_matching,0.3,2.0,48,107
54,add,4.0,c,,COc1,COc1c,5,add c at position 4,flow_matching,0.3,2.0,48,107
55,add,5.0,c,,COc1c,COc1cc,6,add c at position 5,flow_matching,0.3,2.0,48,107
56,add,1.0,/,,COc1cc,C/Oc1cc,7,add / at position 1,flow_matching,0.3,2.0,48,107
57,replace,1.0,O,/,C/Oc1cc,COOc1cc,7,replace / at position 1 with O,flow_matching,0.3,2.0,48,107
58,replace,6.0,),c,COOc1cc,COOc1c),7,replace c at position 6 with ),flow_matching,0.3,2.0,48,107
59,replace,6.0,o,),COOc1c),COOc1co,7,replace ) at position 6 with o,flow_matching,0.3,2.0,48,107
60,remove,1.0,O,,COOc1co,COc1co,6,remove O from position 1,flow_matching,0.3,2.0,48,107
61,add,1.0,1,,COc1co,C1Oc1co,7,add 1 at position 1,flow_matching,0.3,2.0,48,107
62,replace,1.0,O,1,C1Oc1co,COOc1co,7,replace 1 at position 1 with O,flow_matching,0.3,2.0,48,107
63,replace,2.0,c,O,COOc1co,COcc1co,7,replace O at position 2 with c,flow_matching,0.3,2.0,48,107
64,replace,3.0,1,c,COcc1co,COc11co,7,replace c at position 3 with 1,flow_matching,0.3,2.0,48,107
65,replace,4.0,c,1,COc11co,COc1cco,7,replace 1 at position 4 with c,flow_matching,0.3,2.0,48,107
66,replace,6.0,c,o,COc1cco,COc1ccc,7,replace o at position 6 with c,flow_matching,0.3,2.0,48,107
67,add,7.0,c,,COc1ccc,COc1cccc,8,add c at position 7,flow_matching,0.3,2.0,48,107
68,add,8.0,c,,COc1cccc,COc1ccccc,9,add c at position 8,flow_matching,0.3,2.0,48,107
69,add,9.0,1,,COc1ccccc,COc1ccccc1,10,add 1 at position 9,flow_matching,0.3,2.0,48,107
70,add,10.0,C,,COc1ccccc1,COc1ccccc1C,11,add C at position 10,flow_matching,0.3,2.0,48,107
71,add,11.0,O,,COc1ccccc1C,COc1ccccc1CO,12,add O at position 11,flow_matching,0.3,2.0,48,107
72,add,12.0,C,,COc1ccccc1CO,COc1ccccc1COC,13,add C at position 12,flow_matching,0.3,2.0,48,107
73,add,13.0,1,,COc1ccccc1COC,COc1ccccc1COC1,14,add 1 at position 13,flow_matching,0.3,2.0,48,107
74,add,14.0,C,,COc1ccccc1COC1,COc1ccccc1COC1C,15,add C at position 14,flow_matching,0.3,2.0,48,107
75,add,15.0,C,,COc1ccccc1COC1C,COc1ccccc1COC1CC,16,add C at position 15,flow_matching,0.3,2.0,48,107
76,add,16.0,N,,COc1ccccc1COC1CC,COc1ccccc1COC1CCN,17,add N at position 16,flow_matching,0.3,2.0,48,107
77,add,17.0,(,,COc1ccccc1COC1CCN,COc1ccccc1COC1CCN(,18,add ( at position 17,flow_matching,0.3,2.0,48,107
78,add,18.0,C,,COc1ccccc1COC1CCN(,COc1ccccc1COC1CCN(C,19,add C at position 18,flow_matching,0.3,2.0,48,107
79,add,19.0,(,,COc1ccccc1COC1CCN(C,COc1ccccc1COC1CCN(C(,20,add ( at position 19,flow_matching,0.3,2.0,48,107
80,add,20.0,=,,COc1ccccc1COC1CCN(C(,COc1ccccc1COC1CCN(C(=,21,add = at position 20,flow_matching,0.3,2.0,48,107
81,add,21.0,O,,COc1ccccc1COC1CCN(C(=,COc1ccccc1COC1CCN(C(=O,22,add O at position 21,flow_matching,0.3,2.0,48,107
82,add,22.0,),,COc1ccccc1COC1CCN(C(=O,COc1ccccc1COC1CCN(C(=O),23,add ) at position 22,flow_matching,0.3,2.0,48,107
83,add,23.0,[,,COc1ccccc1COC1CCN(C(=O),COc1ccccc1COC1CCN(C(=O)[,24,add [ at position 23,flow_matching,0.3,2.0,48,107
84,add,24.0,C,,COc1ccccc1COC1CCN(C(=O)[,COc1ccccc1COC1CCN(C(=O)[C,25,add C at position 24,flow_matching,0.3,2.0,48,107
85,add,25.0,@,,COc1ccccc1COC1CCN(C(=O)[C,COc1ccccc1COC1CCN(C(=O)[C@,26,add @ at position 25,flow_matching,0.3,2.0,48,107
86,add,26.0,H,,COc1ccccc1COC1CCN(C(=O)[C@,COc1ccccc1COC1CCN(C(=O)[C@H,27,add H at position 26,flow_matching,0.3,2.0,48,107
87,add,27.0,],,COc1ccccc1COC1CCN(C(=O)[C@H,COc1ccccc1COC1CCN(C(=O)[C@H],28,add ] at position 27,flow_matching,0.3,2.0,48,107
88,add,28.0,2,,COc1ccccc1COC1CCN(C(=O)[C@H],COc1ccccc1COC1CCN(C(=O)[C@H]2,29,add 2 at position 28,flow_matching,0.3,2.0,48,107
89,add,29.0,C,,COc1ccccc1COC1CCN(C(=O)[C@H]2,COc1ccccc1COC1CCN(C(=O)[C@H]2C,30,add C at position 29,flow_matching,0.3,2.0,48,107
90,add,30.0,C,,COc1ccccc1COC1CCN(C(=O)[C@H]2C,COc1ccccc1COC1CCN(C(=O)[C@H]2CC,31,add C at position 30,flow_matching,0.3,2.0,48,107
91,add,31.0,C,,COc1ccccc1COC1CCN(C(=O)[C@H]2CC,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC,32,add C at position 31,flow_matching,0.3,2.0,48,107
92,add,32.0,[,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[,33,add [ at position 32,flow_matching,0.3,2.0,48,107
93,add,33.0,C,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C,34,add C at position 33,flow_matching,0.3,2.0,48,107
94,add,34.0,@,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@,35,add @ at position 34,flow_matching,0.3,2.0,48,107
95,add,35.0,@,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@,36,add @ at position 35,flow_matching,0.3,2.0,48,107
96,add,36.0,H,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H,37,add H at position 36,flow_matching,0.3,2.0,48,107
97,add,37.0,],,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H],38,add ] at position 37,flow_matching,0.3,2.0,48,107
98,add,38.0,(,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H],COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](,39,add ( at position 38,flow_matching,0.3,2.0,48,107
99,add,39.0,C,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C,40,add C at position 39,flow_matching,0.3,2.0,48,107
100,add,40.0,),,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C),41,add ) at position 40,flow_matching,0.3,2.0,48,107
101,add,41.0,C,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C),COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C,42,add C at position 41,flow_matching,0.3,2.0,48,107
102,add,42.0,2,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2,43,add 2 at position 42,flow_matching,0.3,2.0,48,107
103,add,43.0,),,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2),44,add ) at position 43,flow_matching,0.3,2.0,48,107
104,add,44.0,C,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2),COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2)C,45,add C at position 44,flow_matching,0.3,2.0,48,107
105,add,45.0,C,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2)C,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2)CC,46,add C at position 45,flow_matching,0.3,2.0,48,107
106,add,46.0,1,,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2)CC,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2)CC1,47,add 1 at position 46,flow_matching,0.3,2.0,48,107
107,add,47.0,"
",,COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2)CC1,"COc1ccccc1COC1CCN(C(=O)[C@H]2CCC[C@@H](C)C2)CC1
",48,"add 
 at position 47",flow_matching,0.3,2.0,48,107
