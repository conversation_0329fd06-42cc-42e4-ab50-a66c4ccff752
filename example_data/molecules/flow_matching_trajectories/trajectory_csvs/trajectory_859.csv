step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,211
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,49,211
2,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,49,211
3,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,49,211
4,add,3.0,s,,Cc1,Cc1s,4,add s at position 3,flow_matching,0.3,2.0,49,211
5,replace,0.0,-,C,Cc1s,-c1s,4,replace <PERSON> at position 0 with -,flow_matching,0.3,2.0,49,211
6,replace,0.0,3,-,-c1s,3c1s,4,replace - at position 0 with 3,flow_matching,0.3,2.0,49,211
7,add,0.0,],,3c1s,]3c1s,5,add ] at position 0,flow_matching,0.3,2.0,49,211
8,add,0.0,],,]3c1s,]]3c1s,6,add ] at position 0,flow_matching,0.3,2.0,49,211
9,replace,2.0,(,3,]]3c1s,]](c1s,6,replace 3 at position 2 with (,flow_matching,0.3,2.0,49,211
10,add,3.0,B,,]](c1s,]](Bc1s,7,add B at position 3,flow_matching,0.3,2.0,49,211
11,replace,6.0,2,s,]](Bc1s,]](Bc12,7,replace s at position 6 with 2,flow_matching,0.3,2.0,49,211
12,replace,3.0,4,B,]](Bc12,]](4c12,7,replace B at position 3 with 4,flow_matching,0.3,2.0,49,211
13,replace,2.0,#,(,]](4c12,]]#4c12,7,replace ( at position 2 with #,flow_matching,0.3,2.0,49,211
14,remove,1.0,],,]]#4c12,]#4c12,6,remove ] from position 1,flow_matching,0.3,2.0,49,211
15,replace,4.0,4,1,]#4c12,]#4c42,6,replace 1 at position 4 with 4,flow_matching,0.3,2.0,49,211
16,replace,0.0,C,],]#4c42,C#4c42,6,replace ] at position 0 with C,flow_matching,0.3,2.0,49,211
17,replace,2.0,6,4,C#4c42,C#6c42,6,replace 4 at position 2 with 6,flow_matching,0.3,2.0,49,211
18,add,3.0,o,,C#6c42,C#6oc42,7,add o at position 3,flow_matching,0.3,2.0,49,211
19,replace,1.0,c,#,C#6oc42,Cc6oc42,7,replace # at position 1 with c,flow_matching,0.3,2.0,49,211
20,replace,2.0,1,6,Cc6oc42,Cc1oc42,7,replace 6 at position 2 with 1,flow_matching,0.3,2.0,49,211
21,remove,2.0,1,,Cc1oc42,Ccoc42,6,remove 1 from position 2,flow_matching,0.3,2.0,49,211
22,add,1.0,@,,Ccoc42,C@coc42,7,add @ at position 1,flow_matching,0.3,2.0,49,211
23,replace,1.0,c,@,C@coc42,Cccoc42,7,replace @ at position 1 with c,flow_matching,0.3,2.0,49,211
24,add,7.0,3,,Cccoc42,Cccoc423,8,add 3 at position 7,flow_matching,0.3,2.0,49,211
25,add,1.0,/,,Cccoc423,C/ccoc423,9,add / at position 1,flow_matching,0.3,2.0,49,211
26,add,0.0,(,,C/ccoc423,(C/ccoc423,10,add ( at position 0,flow_matching,0.3,2.0,49,211
27,replace,0.0,C,(,(C/ccoc423,CC/ccoc423,10,replace ( at position 0 with C,flow_matching,0.3,2.0,49,211
28,replace,1.0,c,C,CC/ccoc423,Cc/ccoc423,10,replace C at position 1 with c,flow_matching,0.3,2.0,49,211
29,replace,2.0,N,/,Cc/ccoc423,CcNccoc423,10,replace / at position 2 with N,flow_matching,0.3,2.0,49,211
30,replace,4.0,#,c,CcNccoc423,CcNc#oc423,10,replace c at position 4 with #,flow_matching,0.3,2.0,49,211
31,replace,2.0,1,N,CcNc#oc423,Cc1c#oc423,10,replace N at position 2 with 1,flow_matching,0.3,2.0,49,211
32,add,5.0,-,,Cc1c#oc423,Cc1c#-oc423,11,add - at position 5,flow_matching,0.3,2.0,49,211
33,add,9.0,o,,Cc1c#-oc423,Cc1c#-oc4o23,12,add o at position 9,flow_matching,0.3,2.0,49,211
34,remove,6.0,o,,Cc1c#-oc4o23,Cc1c#-c4o23,11,remove o from position 6,flow_matching,0.3,2.0,49,211
35,add,0.0,o,,Cc1c#-c4o23,oCc1c#-c4o23,12,add o at position 0,flow_matching,0.3,2.0,49,211
36,replace,1.0,[,C,oCc1c#-c4o23,o[c1c#-c4o23,12,replace C at position 1 with [,flow_matching,0.3,2.0,49,211
37,remove,0.0,o,,o[c1c#-c4o23,[c1c#-c4o23,11,remove o from position 0,flow_matching,0.3,2.0,49,211
38,replace,0.0,C,[,[c1c#-c4o23,Cc1c#-c4o23,11,replace [ at position 0 with C,flow_matching,0.3,2.0,49,211
39,add,7.0,#,,Cc1c#-c4o23,Cc1c#-c#4o23,12,add # at position 7,flow_matching,0.3,2.0,49,211
40,replace,3.0,s,c,Cc1c#-c#4o23,Cc1s#-c#4o23,12,replace c at position 3 with s,flow_matching,0.3,2.0,49,211
41,add,3.0,F,,Cc1s#-c#4o23,Cc1Fs#-c#4o23,13,add F at position 3,flow_matching,0.3,2.0,49,211
42,replace,3.0,s,F,Cc1Fs#-c#4o23,Cc1ss#-c#4o23,13,replace F at position 3 with s,flow_matching,0.3,2.0,49,211
43,replace,8.0,[,#,Cc1ss#-c#4o23,Cc1ss#-c[4o23,13,replace # at position 8 with [,flow_matching,0.3,2.0,49,211
44,replace,0.0,l,C,Cc1ss#-c[4o23,lc1ss#-c[4o23,13,replace C at position 0 with l,flow_matching,0.3,2.0,49,211
45,add,0.0,@,,lc1ss#-c[4o23,@lc1ss#-c[4o23,14,add @ at position 0,flow_matching,0.3,2.0,49,211
46,add,1.0,/,,@lc1ss#-c[4o23,@/lc1ss#-c[4o23,15,add / at position 1,flow_matching,0.3,2.0,49,211
47,replace,5.0,N,s,@/lc1ss#-c[4o23,@/lc1Ns#-c[4o23,15,replace s at position 5 with N,flow_matching,0.3,2.0,49,211
48,add,2.0,@,,@/lc1Ns#-c[4o23,@/@lc1Ns#-c[4o23,16,add @ at position 2,flow_matching,0.3,2.0,49,211
49,remove,1.0,/,,@/@lc1Ns#-c[4o23,@@lc1Ns#-c[4o23,15,remove / from position 1,flow_matching,0.3,2.0,49,211
50,remove,1.0,@,,@@lc1Ns#-c[4o23,@lc1Ns#-c[4o23,14,remove @ from position 1,flow_matching,0.3,2.0,49,211
51,replace,1.0,(,l,@lc1Ns#-c[4o23,@(c1Ns#-c[4o23,14,replace l at position 1 with (,flow_matching,0.3,2.0,49,211
52,remove,13.0,3,,@(c1Ns#-c[4o23,@(c1Ns#-c[4o2,13,remove 3 from position 13,flow_matching,0.3,2.0,49,211
53,replace,0.0,C,@,@(c1Ns#-c[4o2,C(c1Ns#-c[4o2,13,replace @ at position 0 with C,flow_matching,0.3,2.0,49,211
54,replace,1.0,c,(,C(c1Ns#-c[4o2,Ccc1Ns#-c[4o2,13,replace ( at position 1 with c,flow_matching,0.3,2.0,49,211
55,remove,5.0,s,,Ccc1Ns#-c[4o2,Ccc1N#-c[4o2,12,remove s from position 5,flow_matching,0.3,2.0,49,211
56,replace,9.0,-,4,Ccc1N#-c[4o2,Ccc1N#-c[-o2,12,replace 4 at position 9 with -,flow_matching,0.3,2.0,49,211
57,add,4.0,4,,Ccc1N#-c[-o2,Ccc14N#-c[-o2,13,add 4 at position 4,flow_matching,0.3,2.0,49,211
58,replace,2.0,1,c,Ccc14N#-c[-o2,Cc114N#-c[-o2,13,replace c at position 2 with 1,flow_matching,0.3,2.0,49,211
59,replace,3.0,s,1,Cc114N#-c[-o2,Cc1s4N#-c[-o2,13,replace 1 at position 3 with s,flow_matching,0.3,2.0,49,211
60,remove,1.0,c,,Cc1s4N#-c[-o2,C1s4N#-c[-o2,12,remove c from position 1,flow_matching,0.3,2.0,49,211
61,replace,1.0,c,1,C1s4N#-c[-o2,Ccs4N#-c[-o2,12,replace 1 at position 1 with c,flow_matching,0.3,2.0,49,211
62,replace,11.0,H,2,Ccs4N#-c[-o2,Ccs4N#-c[-oH,12,replace 2 at position 11 with H,flow_matching,0.3,2.0,49,211
63,replace,7.0,r,c,Ccs4N#-c[-oH,Ccs4N#-r[-oH,12,replace c at position 7 with r,flow_matching,0.3,2.0,49,211
64,replace,2.0,1,s,Ccs4N#-r[-oH,Cc14N#-r[-oH,12,replace s at position 2 with 1,flow_matching,0.3,2.0,49,211
65,remove,4.0,N,,Cc14N#-r[-oH,Cc14#-r[-oH,11,remove N from position 4,flow_matching,0.3,2.0,49,211
66,replace,8.0,o,-,Cc14#-r[-oH,Cc14#-r[ooH,11,replace - at position 8 with o,flow_matching,0.3,2.0,49,211
67,remove,1.0,c,,Cc14#-r[ooH,C14#-r[ooH,10,remove c from position 1,flow_matching,0.3,2.0,49,211
68,replace,6.0,s,[,C14#-r[ooH,C14#-rsooH,10,replace [ at position 6 with s,flow_matching,0.3,2.0,49,211
69,remove,0.0,C,,C14#-rsooH,14#-rsooH,9,remove C from position 0,flow_matching,0.3,2.0,49,211
70,replace,2.0,3,#,14#-rsooH,143-rsooH,9,replace # at position 2 with 3,flow_matching,0.3,2.0,49,211
71,replace,0.0,C,1,143-rsooH,C43-rsooH,9,replace 1 at position 0 with C,flow_matching,0.3,2.0,49,211
72,replace,6.0,H,o,C43-rsooH,C43-rsHoH,9,replace o at position 6 with H,flow_matching,0.3,2.0,49,211
73,remove,5.0,s,,C43-rsHoH,C43-rHoH,8,remove s from position 5,flow_matching,0.3,2.0,49,211
74,replace,1.0,c,4,C43-rHoH,Cc3-rHoH,8,replace 4 at position 1 with c,flow_matching,0.3,2.0,49,211
75,add,3.0,F,,Cc3-rHoH,Cc3F-rHoH,9,add F at position 3,flow_matching,0.3,2.0,49,211
76,replace,2.0,1,3,Cc3F-rHoH,Cc1F-rHoH,9,replace 3 at position 2 with 1,flow_matching,0.3,2.0,49,211
77,replace,7.0,/,o,Cc1F-rHoH,Cc1F-rH/H,9,replace o at position 7 with /,flow_matching,0.3,2.0,49,211
78,replace,3.0,s,F,Cc1F-rH/H,Cc1s-rH/H,9,replace F at position 3 with s,flow_matching,0.3,2.0,49,211
79,add,3.0,-,,Cc1s-rH/H,Cc1-s-rH/H,10,add - at position 3,flow_matching,0.3,2.0,49,211
80,add,5.0,l,,Cc1-s-rH/H,Cc1-sl-rH/H,11,add l at position 5,flow_matching,0.3,2.0,49,211
81,add,2.0,),,Cc1-sl-rH/H,Cc)1-sl-rH/H,12,add ) at position 2,flow_matching,0.3,2.0,49,211
82,add,0.0,),,Cc)1-sl-rH/H,)Cc)1-sl-rH/H,13,add ) at position 0,flow_matching,0.3,2.0,49,211
83,replace,10.0,/,H,)Cc)1-sl-rH/H,)Cc)1-sl-r//H,13,replace H at position 10 with /,flow_matching,0.3,2.0,49,211
84,add,9.0,o,,)Cc)1-sl-r//H,)Cc)1-sl-or//H,14,add o at position 9,flow_matching,0.3,2.0,49,211
85,remove,0.0,),,)Cc)1-sl-or//H,Cc)1-sl-or//H,13,remove ) from position 0,flow_matching,0.3,2.0,49,211
86,replace,2.0,1,),Cc)1-sl-or//H,Cc11-sl-or//H,13,replace ) at position 2 with 1,flow_matching,0.3,2.0,49,211
87,replace,3.0,s,1,Cc11-sl-or//H,Cc1s-sl-or//H,13,replace 1 at position 3 with s,flow_matching,0.3,2.0,49,211
88,remove,2.0,1,,Cc1s-sl-or//H,Ccs-sl-or//H,12,remove 1 from position 2,flow_matching,0.3,2.0,49,211
89,replace,1.0,N,c,Ccs-sl-or//H,CNs-sl-or//H,12,replace c at position 1 with N,flow_matching,0.3,2.0,49,211
90,replace,11.0,l,H,CNs-sl-or//H,CNs-sl-or//l,12,replace H at position 11 with l,flow_matching,0.3,2.0,49,211
91,replace,1.0,+,N,CNs-sl-or//l,C+s-sl-or//l,12,replace N at position 1 with +,flow_matching,0.3,2.0,49,211
92,replace,1.0,c,+,C+s-sl-or//l,Ccs-sl-or//l,12,replace + at position 1 with c,flow_matching,0.3,2.0,49,211
93,add,3.0,[,,Ccs-sl-or//l,Ccs[-sl-or//l,13,add [ at position 3,flow_matching,0.3,2.0,49,211
94,replace,2.0,1,s,Ccs[-sl-or//l,Cc1[-sl-or//l,13,replace s at position 2 with 1,flow_matching,0.3,2.0,49,211
95,replace,1.0,l,c,Cc1[-sl-or//l,Cl1[-sl-or//l,13,replace c at position 1 with l,flow_matching,0.3,2.0,49,211
96,replace,1.0,c,l,Cl1[-sl-or//l,Cc1[-sl-or//l,13,replace l at position 1 with c,flow_matching,0.3,2.0,49,211
97,replace,3.0,s,[,Cc1[-sl-or//l,Cc1s-sl-or//l,13,replace [ at position 3 with s,flow_matching,0.3,2.0,49,211
98,replace,10.0,s,/,Cc1s-sl-or//l,Cc1s-sl-ors/l,13,replace / at position 10 with s,flow_matching,0.3,2.0,49,211
99,add,6.0,),,Cc1s-sl-ors/l,Cc1s-s)l-ors/l,14,add ) at position 6,flow_matching,0.3,2.0,49,211
100,replace,4.0,c,-,Cc1s-s)l-ors/l,Cc1scs)l-ors/l,14,replace - at position 4 with c,flow_matching,0.3,2.0,49,211
101,remove,3.0,s,,Cc1scs)l-ors/l,Cc1cs)l-ors/l,13,remove s from position 3,flow_matching,0.3,2.0,49,211
102,replace,8.0,O,o,Cc1cs)l-ors/l,Cc1cs)l-Ors/l,13,replace o at position 8 with O,flow_matching,0.3,2.0,49,211
103,replace,6.0,5,l,Cc1cs)l-Ors/l,Cc1cs)5-Ors/l,13,replace l at position 6 with 5,flow_matching,0.3,2.0,49,211
104,replace,3.0,s,c,Cc1cs)5-Ors/l,Cc1ss)5-Ors/l,13,replace c at position 3 with s,flow_matching,0.3,2.0,49,211
105,remove,9.0,r,,Cc1ss)5-Ors/l,Cc1ss)5-Os/l,12,remove r from position 9,flow_matching,0.3,2.0,49,211
106,replace,4.0,c,s,Cc1ss)5-Os/l,Cc1sc)5-Os/l,12,replace s at position 4 with c,flow_matching,0.3,2.0,49,211
107,add,3.0,l,,Cc1sc)5-Os/l,Cc1lsc)5-Os/l,13,add l at position 3,flow_matching,0.3,2.0,49,211
108,replace,3.0,s,l,Cc1lsc)5-Os/l,Cc1ssc)5-Os/l,13,replace l at position 3 with s,flow_matching,0.3,2.0,49,211
109,remove,12.0,l,,Cc1ssc)5-Os/l,Cc1ssc)5-Os/,12,remove l from position 12,flow_matching,0.3,2.0,49,211
110,replace,10.0,n,s,Cc1ssc)5-Os/,Cc1ssc)5-On/,12,replace s at position 10 with n,flow_matching,0.3,2.0,49,211
111,replace,4.0,+,s,Cc1ssc)5-On/,Cc1s+c)5-On/,12,replace s at position 4 with +,flow_matching,0.3,2.0,49,211
112,replace,11.0,4,/,Cc1s+c)5-On/,Cc1s+c)5-On4,12,replace / at position 11 with 4,flow_matching,0.3,2.0,49,211
113,remove,7.0,5,,Cc1s+c)5-On4,Cc1s+c)-On4,11,remove 5 from position 7,flow_matching,0.3,2.0,49,211
114,replace,4.0,(,+,Cc1s+c)-On4,Cc1s(c)-On4,11,replace + at position 4 with (,flow_matching,0.3,2.0,49,211
115,replace,4.0,c,(,Cc1s(c)-On4,Cc1scc)-On4,11,replace ( at position 4 with c,flow_matching,0.3,2.0,49,211
116,replace,5.0,(,c,Cc1scc)-On4,Cc1sc()-On4,11,replace c at position 5 with (,flow_matching,0.3,2.0,49,211
117,add,1.0,B,,Cc1sc()-On4,CBc1sc()-On4,12,add B at position 1,flow_matching,0.3,2.0,49,211
118,remove,10.0,n,,CBc1sc()-On4,CBc1sc()-O4,11,remove n from position 10,flow_matching,0.3,2.0,49,211
119,replace,1.0,c,B,CBc1sc()-O4,Ccc1sc()-O4,11,replace B at position 1 with c,flow_matching,0.3,2.0,49,211
120,add,7.0,[,,Ccc1sc()-O4,Ccc1sc([)-O4,12,add [ at position 7,flow_matching,0.3,2.0,49,211
121,remove,1.0,c,,Ccc1sc([)-O4,Cc1sc([)-O4,11,remove c from position 1,flow_matching,0.3,2.0,49,211
122,replace,6.0,=,[,Cc1sc([)-O4,Cc1sc(=)-O4,11,replace [ at position 6 with =,flow_matching,0.3,2.0,49,211
123,add,8.0,-,,Cc1sc(=)-O4,Cc1sc(=)--O4,12,add - at position 8,flow_matching,0.3,2.0,49,211
124,replace,2.0,F,1,Cc1sc(=)--O4,CcFsc(=)--O4,12,replace 1 at position 2 with F,flow_matching,0.3,2.0,49,211
125,remove,0.0,C,,CcFsc(=)--O4,cFsc(=)--O4,11,remove C from position 0,flow_matching,0.3,2.0,49,211
126,replace,10.0,),4,cFsc(=)--O4,cFsc(=)--O),11,replace 4 at position 10 with ),flow_matching,0.3,2.0,49,211
127,replace,9.0,3,O,cFsc(=)--O),cFsc(=)--3),11,replace O at position 9 with 3,flow_matching,0.3,2.0,49,211
128,replace,0.0,C,c,cFsc(=)--3),CFsc(=)--3),11,replace c at position 0 with C,flow_matching,0.3,2.0,49,211
129,replace,1.0,c,F,CFsc(=)--3),Ccsc(=)--3),11,replace F at position 1 with c,flow_matching,0.3,2.0,49,211
130,add,3.0,O,,Ccsc(=)--3),CcsOc(=)--3),12,add O at position 3,flow_matching,0.3,2.0,49,211
131,replace,2.0,1,s,CcsOc(=)--3),Cc1Oc(=)--3),12,replace s at position 2 with 1,flow_matching,0.3,2.0,49,211
132,remove,0.0,C,,Cc1Oc(=)--3),c1Oc(=)--3),11,remove C from position 0,flow_matching,0.3,2.0,49,211
133,add,4.0,s,,c1Oc(=)--3),c1Ocs(=)--3),12,add s at position 4,flow_matching,0.3,2.0,49,211
134,remove,0.0,c,,c1Ocs(=)--3),1Ocs(=)--3),11,remove c from position 0,flow_matching,0.3,2.0,49,211
135,add,8.0,=,,1Ocs(=)--3),1Ocs(=)-=-3),12,add = at position 8,flow_matching,0.3,2.0,49,211
136,remove,8.0,=,,1Ocs(=)-=-3),1Ocs(=)--3),11,remove = from position 8,flow_matching,0.3,2.0,49,211
137,replace,4.0,3,(,1Ocs(=)--3),1Ocs3=)--3),11,replace ( at position 4 with 3,flow_matching,0.3,2.0,49,211
138,remove,9.0,3,,1Ocs3=)--3),1Ocs3=)--),10,remove 3 from position 9,flow_matching,0.3,2.0,49,211
139,add,8.0,l,,1Ocs3=)--),1Ocs3=)-l-),11,add l at position 8,flow_matching,0.3,2.0,49,211
140,replace,0.0,C,1,1Ocs3=)-l-),COcs3=)-l-),11,replace 1 at position 0 with C,flow_matching,0.3,2.0,49,211
141,remove,8.0,l,,COcs3=)-l-),COcs3=)--),10,remove l from position 8,flow_matching,0.3,2.0,49,211
142,replace,1.0,c,O,COcs3=)--),Cccs3=)--),10,replace O at position 1 with c,flow_matching,0.3,2.0,49,211
143,replace,2.0,1,c,Cccs3=)--),Cc1s3=)--),10,replace c at position 2 with 1,flow_matching,0.3,2.0,49,211
144,add,7.0,),,Cc1s3=)--),Cc1s3=))--),11,add ) at position 7,flow_matching,0.3,2.0,49,211
145,add,3.0,s,,Cc1s3=))--),Cc1ss3=))--),12,add s at position 3,flow_matching,0.3,2.0,49,211
146,replace,5.0,I,3,Cc1ss3=))--),Cc1ssI=))--),12,replace 3 at position 5 with I,flow_matching,0.3,2.0,49,211
147,replace,4.0,c,s,Cc1ssI=))--),Cc1scI=))--),12,replace s at position 4 with c,flow_matching,0.3,2.0,49,211
148,add,10.0,1,,Cc1scI=))--),Cc1scI=))-1-),13,add 1 at position 10,flow_matching,0.3,2.0,49,211
149,replace,5.0,(,I,Cc1scI=))-1-),Cc1sc(=))-1-),13,replace I at position 5 with (,flow_matching,0.3,2.0,49,211
150,replace,8.0,r,),Cc1sc(=))-1-),Cc1sc(=)r-1-),13,replace ) at position 8 with r,flow_matching,0.3,2.0,49,211
151,replace,7.0,O,),Cc1sc(=)r-1-),Cc1sc(=Or-1-),13,replace ) at position 7 with O,flow_matching,0.3,2.0,49,211
152,replace,8.0,),r,Cc1sc(=Or-1-),Cc1sc(=O)-1-),13,replace r at position 8 with ),flow_matching,0.3,2.0,49,211
153,replace,9.0,n,-,Cc1sc(=O)-1-),Cc1sc(=O)n1-),13,replace - at position 9 with n,flow_matching,0.3,2.0,49,211
154,add,10.0,=,,Cc1sc(=O)n1-),Cc1sc(=O)n=1-),14,add = at position 10,flow_matching,0.3,2.0,49,211
155,add,1.0,\,,Cc1sc(=O)n=1-),C\c1sc(=O)n=1-),15,add \ at position 1,flow_matching,0.3,2.0,49,211
156,replace,1.0,c,\,C\c1sc(=O)n=1-),Ccc1sc(=O)n=1-),15,replace \ at position 1 with c,flow_matching,0.3,2.0,49,211
157,replace,0.0,s,C,Ccc1sc(=O)n=1-),scc1sc(=O)n=1-),15,replace C at position 0 with s,flow_matching,0.3,2.0,49,211
158,remove,10.0,n,,scc1sc(=O)n=1-),scc1sc(=O)=1-),14,remove n from position 10,flow_matching,0.3,2.0,49,211
159,remove,12.0,-,,scc1sc(=O)=1-),scc1sc(=O)=1),13,remove - from position 12,flow_matching,0.3,2.0,49,211
160,add,13.0,),,scc1sc(=O)=1),scc1sc(=O)=1)),14,add ) at position 13,flow_matching,0.3,2.0,49,211
161,replace,0.0,C,s,scc1sc(=O)=1)),Ccc1sc(=O)=1)),14,replace s at position 0 with C,flow_matching,0.3,2.0,49,211
162,remove,0.0,C,,Ccc1sc(=O)=1)),cc1sc(=O)=1)),13,remove C from position 0,flow_matching,0.3,2.0,49,211
163,replace,1.0,\,c,cc1sc(=O)=1)),c\1sc(=O)=1)),13,replace c at position 1 with \,flow_matching,0.3,2.0,49,211
164,replace,0.0,C,c,c\1sc(=O)=1)),C\1sc(=O)=1)),13,replace c at position 0 with C,flow_matching,0.3,2.0,49,211
165,replace,1.0,B,\,C\1sc(=O)=1)),CB1sc(=O)=1)),13,replace \ at position 1 with B,flow_matching,0.3,2.0,49,211
166,replace,1.0,c,B,CB1sc(=O)=1)),Cc1sc(=O)=1)),13,replace B at position 1 with c,flow_matching,0.3,2.0,49,211
167,replace,9.0,n,=,Cc1sc(=O)=1)),Cc1sc(=O)n1)),13,replace = at position 9 with n,flow_matching,0.3,2.0,49,211
168,add,13.0,/,,Cc1sc(=O)n1)),Cc1sc(=O)n1))/,14,add / at position 13,flow_matching,0.3,2.0,49,211
169,add,7.0,c,,Cc1sc(=O)n1))/,Cc1sc(=cO)n1))/,15,add c at position 7,flow_matching,0.3,2.0,49,211
170,replace,7.0,O,c,Cc1sc(=cO)n1))/,Cc1sc(=OO)n1))/,15,replace c at position 7 with O,flow_matching,0.3,2.0,49,211
171,replace,8.0,),O,Cc1sc(=OO)n1))/,Cc1sc(=O))n1))/,15,replace O at position 8 with ),flow_matching,0.3,2.0,49,211
172,replace,9.0,n,),Cc1sc(=O))n1))/,Cc1sc(=O)nn1))/,15,replace ) at position 9 with n,flow_matching,0.3,2.0,49,211
173,replace,10.0,(,n,Cc1sc(=O)nn1))/,Cc1sc(=O)n(1))/,15,replace n at position 10 with (,flow_matching,0.3,2.0,49,211
174,replace,11.0,C,1,Cc1sc(=O)n(1))/,Cc1sc(=O)n(C))/,15,replace 1 at position 11 with C,flow_matching,0.3,2.0,49,211
175,replace,12.0,C,),Cc1sc(=O)n(C))/,Cc1sc(=O)n(CC)/,15,replace ) at position 12 with C,flow_matching,0.3,2.0,49,211
176,replace,13.0,C,),Cc1sc(=O)n(CC)/,Cc1sc(=O)n(CCC/,15,replace ) at position 13 with C,flow_matching,0.3,2.0,49,211
177,replace,14.0,(,/,Cc1sc(=O)n(CCC/,Cc1sc(=O)n(CCC(,15,replace / at position 14 with (,flow_matching,0.3,2.0,49,211
178,add,15.0,=,,Cc1sc(=O)n(CCC(,Cc1sc(=O)n(CCC(=,16,add = at position 15,flow_matching,0.3,2.0,49,211
179,add,16.0,O,,Cc1sc(=O)n(CCC(=,Cc1sc(=O)n(CCC(=O,17,add O at position 16,flow_matching,0.3,2.0,49,211
180,add,17.0,),,Cc1sc(=O)n(CCC(=O,Cc1sc(=O)n(CCC(=O),18,add ) at position 17,flow_matching,0.3,2.0,49,211
181,add,18.0,N,,Cc1sc(=O)n(CCC(=O),Cc1sc(=O)n(CCC(=O)N,19,add N at position 18,flow_matching,0.3,2.0,49,211
182,add,19.0,C,,Cc1sc(=O)n(CCC(=O)N,Cc1sc(=O)n(CCC(=O)NC,20,add C at position 19,flow_matching,0.3,2.0,49,211
183,add,20.0,2,,Cc1sc(=O)n(CCC(=O)NC,Cc1sc(=O)n(CCC(=O)NC2,21,add 2 at position 20,flow_matching,0.3,2.0,49,211
184,add,21.0,C,,Cc1sc(=O)n(CCC(=O)NC2,Cc1sc(=O)n(CCC(=O)NC2C,22,add C at position 21,flow_matching,0.3,2.0,49,211
185,add,22.0,C,,Cc1sc(=O)n(CCC(=O)NC2C,Cc1sc(=O)n(CCC(=O)NC2CC,23,add C at position 22,flow_matching,0.3,2.0,49,211
186,add,23.0,(,,Cc1sc(=O)n(CCC(=O)NC2CC,Cc1sc(=O)n(CCC(=O)NC2CC(,24,add ( at position 23,flow_matching,0.3,2.0,49,211
187,add,24.0,C,,Cc1sc(=O)n(CCC(=O)NC2CC(,Cc1sc(=O)n(CCC(=O)NC2CC(C,25,add C at position 24,flow_matching,0.3,2.0,49,211
188,add,25.0,),,Cc1sc(=O)n(CCC(=O)NC2CC(C,Cc1sc(=O)n(CCC(=O)NC2CC(C),26,add ) at position 25,flow_matching,0.3,2.0,49,211
189,add,26.0,(,,Cc1sc(=O)n(CCC(=O)NC2CC(C),Cc1sc(=O)n(CCC(=O)NC2CC(C)(,27,add ( at position 26,flow_matching,0.3,2.0,49,211
190,add,27.0,C,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C,28,add C at position 27,flow_matching,0.3,2.0,49,211
191,add,28.0,),,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C),29,add ) at position 28,flow_matching,0.3,2.0,49,211
192,add,29.0,[,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C),Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[,30,add [ at position 29,flow_matching,0.3,2.0,49,211
193,add,30.0,N,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[N,31,add N at position 30,flow_matching,0.3,2.0,49,211
194,add,31.0,H,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[N,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH,32,add H at position 31,flow_matching,0.3,2.0,49,211
195,add,32.0,2,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2,33,add 2 at position 32,flow_matching,0.3,2.0,49,211
196,add,33.0,+,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+,34,add + at position 33,flow_matching,0.3,2.0,49,211
197,add,34.0,],,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+],35,add ] at position 34,flow_matching,0.3,2.0,49,211
198,add,35.0,C,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+],Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C,36,add C at position 35,flow_matching,0.3,2.0,49,211
199,add,36.0,(,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(,37,add ( at position 36,flow_matching,0.3,2.0,49,211
200,add,37.0,C,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C,38,add C at position 37,flow_matching,0.3,2.0,49,211
201,add,38.0,),,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C),39,add ) at position 38,flow_matching,0.3,2.0,49,211
202,add,39.0,(,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C),Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(,40,add ( at position 39,flow_matching,0.3,2.0,49,211
203,add,40.0,C,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C,41,add C at position 40,flow_matching,0.3,2.0,49,211
204,add,41.0,),,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C),42,add ) at position 41,flow_matching,0.3,2.0,49,211
205,add,42.0,C,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C),Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C,43,add C at position 42,flow_matching,0.3,2.0,49,211
206,add,43.0,2,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2,44,add 2 at position 43,flow_matching,0.3,2.0,49,211
207,add,44.0,),,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2),45,add ) at position 44,flow_matching,0.3,2.0,49,211
208,add,45.0,c,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2),Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2)c,46,add c at position 45,flow_matching,0.3,2.0,49,211
209,add,46.0,1,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2)c,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2)c1,47,add 1 at position 46,flow_matching,0.3,2.0,49,211
210,add,47.0,C,,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2)c1,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2)c1C,48,add C at position 47,flow_matching,0.3,2.0,49,211
211,add,48.0,"
",,Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2)c1C,"Cc1sc(=O)n(CCC(=O)NC2CC(C)(C)[NH2+]C(C)(C)C2)c1C
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,211
