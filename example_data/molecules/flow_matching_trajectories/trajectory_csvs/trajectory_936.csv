step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,127
1,add,0.0,(,,,(,1,add ( at position 0,flow_matching,0.3,2.0,49,127
2,replace,0.0,C,(,(,C,1,replace ( at position 0 with C,flow_matching,0.3,2.0,49,127
3,replace,0.0,#,C,C,#,1,replace <PERSON> at position 0 with #,flow_matching,0.3,2.0,49,127
4,remove,0.0,#,,#,,0,remove # from position 0,flow_matching,0.3,2.0,49,127
5,add,0.0,(,,,(,1,add ( at position 0,flow_matching,0.3,2.0,49,127
6,add,1.0,@,,(,(@,2,add @ at position 1,flow_matching,0.3,2.0,49,127
7,add,1.0,I,,(@,(I@,3,add I at position 1,flow_matching,0.3,2.0,49,127
8,add,1.0,N,,(I@,(NI@,4,add N at position 1,flow_matching,0.3,2.0,49,127
9,replace,0.0,C,(,(NI@,CNI@,4,replace ( at position 0 with C,flow_matching,0.3,2.0,49,127
10,replace,1.0,C,N,CNI@,CCI@,4,replace N at position 1 with C,flow_matching,0.3,2.0,49,127
11,replace,2.0,(,I,CCI@,CC(@,4,replace I at position 2 with (,flow_matching,0.3,2.0,49,127
12,replace,0.0,N,C,CC(@,NC(@,4,replace C at position 0 with N,flow_matching,0.3,2.0,49,127
13,replace,0.0,C,N,NC(@,CC(@,4,replace N at position 0 with C,flow_matching,0.3,2.0,49,127
14,replace,3.0,N,@,CC(@,CC(N,4,replace @ at position 3 with N,flow_matching,0.3,2.0,49,127
15,remove,0.0,C,,CC(N,C(N,3,remove C from position 0,flow_matching,0.3,2.0,49,127
16,replace,0.0,H,C,C(N,H(N,3,replace C at position 0 with H,flow_matching,0.3,2.0,49,127
17,replace,0.0,C,H,H(N,C(N,3,replace H at position 0 with C,flow_matching,0.3,2.0,49,127
18,add,2.0,4,,C(N,C(4N,4,add 4 at position 2,flow_matching,0.3,2.0,49,127
19,remove,0.0,C,,C(4N,(4N,3,remove C from position 0,flow_matching,0.3,2.0,49,127
20,replace,2.0,H,N,(4N,(4H,3,replace N at position 2 with H,flow_matching,0.3,2.0,49,127
21,replace,0.0,C,(,(4H,C4H,3,replace ( at position 0 with C,flow_matching,0.3,2.0,49,127
22,remove,2.0,H,,C4H,C4,2,remove H from position 2,flow_matching,0.3,2.0,49,127
23,remove,1.0,4,,C4,C,1,remove 4 from position 1,flow_matching,0.3,2.0,49,127
24,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,49,127
25,add,0.0,l,,,l,1,add l at position 0,flow_matching,0.3,2.0,49,127
26,replace,0.0,6,l,l,6,1,replace l at position 0 with 6,flow_matching,0.3,2.0,49,127
27,remove,0.0,6,,6,,0,remove 6 from position 0,flow_matching,0.3,2.0,49,127
28,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,49,127
29,replace,0.0,F,@,@,F,1,replace @ at position 0 with F,flow_matching,0.3,2.0,49,127
30,add,0.0,3,,F,3F,2,add 3 at position 0,flow_matching,0.3,2.0,49,127
31,add,0.0,@,,3F,@3F,3,add @ at position 0,flow_matching,0.3,2.0,49,127
32,remove,0.0,@,,@3F,3F,2,remove @ from position 0,flow_matching,0.3,2.0,49,127
33,remove,1.0,F,,3F,3,1,remove F from position 1,flow_matching,0.3,2.0,49,127
34,replace,0.0,/,3,3,/,1,replace 3 at position 0 with /,flow_matching,0.3,2.0,49,127
35,replace,0.0,C,/,/,C,1,replace / at position 0 with C,flow_matching,0.3,2.0,49,127
36,add,0.0,r,,C,rC,2,add r at position 0,flow_matching,0.3,2.0,49,127
37,remove,1.0,C,,rC,r,1,remove C from position 1,flow_matching,0.3,2.0,49,127
38,add,1.0,H,,r,rH,2,add H at position 1,flow_matching,0.3,2.0,49,127
39,replace,1.0,),H,rH,r),2,replace H at position 1 with ),flow_matching,0.3,2.0,49,127
40,replace,0.0,C,r,r),C),2,replace r at position 0 with C,flow_matching,0.3,2.0,49,127
41,add,1.0,4,,C),C4),3,add 4 at position 1,flow_matching,0.3,2.0,49,127
42,add,1.0,O,,C4),CO4),4,add O at position 1,flow_matching,0.3,2.0,49,127
43,replace,1.0,C,O,CO4),CC4),4,replace O at position 1 with C,flow_matching,0.3,2.0,49,127
44,add,3.0,r,,CC4),CC4r),5,add r at position 3,flow_matching,0.3,2.0,49,127
45,replace,3.0,#,r,CC4r),CC4#),5,replace r at position 3 with #,flow_matching,0.3,2.0,49,127
46,add,5.0,4,,CC4#),CC4#)4,6,add 4 at position 5,flow_matching,0.3,2.0,49,127
47,replace,4.0,6,),CC4#)4,CC4#64,6,replace ) at position 4 with 6,flow_matching,0.3,2.0,49,127
48,replace,0.0,N,C,CC4#64,NC4#64,6,replace C at position 0 with N,flow_matching,0.3,2.0,49,127
49,add,5.0,#,,NC4#64,NC4#6#4,7,add # at position 5,flow_matching,0.3,2.0,49,127
50,replace,4.0,5,6,NC4#6#4,NC4#5#4,7,replace 6 at position 4 with 5,flow_matching,0.3,2.0,49,127
51,remove,6.0,4,,NC4#5#4,NC4#5#,6,remove 4 from position 6,flow_matching,0.3,2.0,49,127
52,replace,0.0,C,N,NC4#5#,CC4#5#,6,replace N at position 0 with C,flow_matching,0.3,2.0,49,127
53,add,6.0,+,,CC4#5#,CC4#5#+,7,add + at position 6,flow_matching,0.3,2.0,49,127
54,replace,0.0,B,C,CC4#5#+,BC4#5#+,7,replace C at position 0 with B,flow_matching,0.3,2.0,49,127
55,remove,4.0,5,,BC4#5#+,BC4##+,6,remove 5 from position 4,flow_matching,0.3,2.0,49,127
56,replace,0.0,C,B,BC4##+,CC4##+,6,replace B at position 0 with C,flow_matching,0.3,2.0,49,127
57,replace,1.0,l,C,CC4##+,Cl4##+,6,replace C at position 1 with l,flow_matching,0.3,2.0,49,127
58,replace,3.0,7,#,Cl4##+,Cl47#+,6,replace # at position 3 with 7,flow_matching,0.3,2.0,49,127
59,replace,1.0,C,l,Cl47#+,CC47#+,6,replace l at position 1 with C,flow_matching,0.3,2.0,49,127
60,add,5.0,[,,CC47#+,CC47#[+,7,add [ at position 5,flow_matching,0.3,2.0,49,127
61,replace,2.0,(,4,CC47#[+,CC(7#[+,7,replace 4 at position 2 with (,flow_matching,0.3,2.0,49,127
62,add,2.0,N,,CC(7#[+,CCN(7#[+,8,add N at position 2,flow_matching,0.3,2.0,49,127
63,replace,7.0,7,+,CCN(7#[+,CCN(7#[7,8,replace + at position 7 with 7,flow_matching,0.3,2.0,49,127
64,add,7.0,n,,CCN(7#[7,CCN(7#[n7,9,add n at position 7,flow_matching,0.3,2.0,49,127
65,add,8.0,],,CCN(7#[n7,CCN(7#[n]7,10,add ] at position 8,flow_matching,0.3,2.0,49,127
66,add,6.0,(,,CCN(7#[n]7,CCN(7#([n]7,11,add ( at position 6,flow_matching,0.3,2.0,49,127
67,add,8.0,I,,CCN(7#([n]7,CCN(7#([In]7,12,add I at position 8,flow_matching,0.3,2.0,49,127
68,replace,2.0,(,N,CCN(7#([In]7,CC((7#([In]7,12,replace N at position 2 with (,flow_matching,0.3,2.0,49,127
69,add,4.0,5,,CC((7#([In]7,CC((57#([In]7,13,add 5 at position 4,flow_matching,0.3,2.0,49,127
70,replace,5.0,s,7,CC((57#([In]7,CC((5s#([In]7,13,replace 7 at position 5 with s,flow_matching,0.3,2.0,49,127
71,add,13.0,[,,CC((5s#([In]7,CC((5s#([In]7[,14,add [ at position 13,flow_matching,0.3,2.0,49,127
72,replace,0.0,O,C,CC((5s#([In]7[,OC((5s#([In]7[,14,replace C at position 0 with O,flow_matching,0.3,2.0,49,127
73,add,2.0,7,,OC((5s#([In]7[,OC7((5s#([In]7[,15,add 7 at position 2,flow_matching,0.3,2.0,49,127
74,add,14.0,N,,OC7((5s#([In]7[,OC7((5s#([In]7N[,16,add N at position 14,flow_matching,0.3,2.0,49,127
75,remove,7.0,#,,OC7((5s#([In]7N[,OC7((5s([In]7N[,15,remove # from position 7,flow_matching,0.3,2.0,49,127
76,add,9.0,4,,OC7((5s([In]7N[,OC7((5s([4In]7N[,16,add 4 at position 9,flow_matching,0.3,2.0,49,127
77,add,15.0,5,,OC7((5s([4In]7N[,OC7((5s([4In]7N5[,17,add 5 at position 15,flow_matching,0.3,2.0,49,127
78,replace,0.0,C,O,OC7((5s([4In]7N5[,CC7((5s([4In]7N5[,17,replace O at position 0 with C,flow_matching,0.3,2.0,49,127
79,remove,9.0,4,,CC7((5s([4In]7N5[,CC7((5s([In]7N5[,16,remove 4 from position 9,flow_matching,0.3,2.0,49,127
80,remove,1.0,C,,CC7((5s([In]7N5[,C7((5s([In]7N5[,15,remove C from position 1,flow_matching,0.3,2.0,49,127
81,replace,1.0,C,7,C7((5s([In]7N5[,CC((5s([In]7N5[,15,replace 7 at position 1 with C,flow_matching,0.3,2.0,49,127
82,replace,3.0,=,(,CC((5s([In]7N5[,CC(=5s([In]7N5[,15,replace ( at position 3 with =,flow_matching,0.3,2.0,49,127
83,replace,4.0,O,5,CC(=5s([In]7N5[,CC(=Os([In]7N5[,15,replace 5 at position 4 with O,flow_matching,0.3,2.0,49,127
84,replace,5.0,),s,CC(=Os([In]7N5[,CC(=O)([In]7N5[,15,replace s at position 5 with ),flow_matching,0.3,2.0,49,127
85,replace,6.0,N,(,CC(=O)([In]7N5[,CC(=O)N[In]7N5[,15,replace ( at position 6 with N,flow_matching,0.3,2.0,49,127
86,replace,7.0,c,[,CC(=O)N[In]7N5[,CC(=O)NcIn]7N5[,15,replace [ at position 7 with c,flow_matching,0.3,2.0,49,127
87,replace,8.0,1,I,CC(=O)NcIn]7N5[,CC(=O)Nc1n]7N5[,15,replace I at position 8 with 1,flow_matching,0.3,2.0,49,127
88,replace,9.0,c,n,CC(=O)Nc1n]7N5[,CC(=O)Nc1c]7N5[,15,replace n at position 9 with c,flow_matching,0.3,2.0,49,127
89,replace,10.0,c,],CC(=O)Nc1c]7N5[,CC(=O)Nc1cc7N5[,15,replace ] at position 10 with c,flow_matching,0.3,2.0,49,127
90,replace,11.0,c,7,CC(=O)Nc1cc7N5[,CC(=O)Nc1cccN5[,15,replace 7 at position 11 with c,flow_matching,0.3,2.0,49,127
91,replace,12.0,c,N,CC(=O)Nc1cccN5[,CC(=O)Nc1cccc5[,15,replace N at position 12 with c,flow_matching,0.3,2.0,49,127
92,replace,13.0,(,5,CC(=O)Nc1cccc5[,CC(=O)Nc1cccc([,15,replace 5 at position 13 with (,flow_matching,0.3,2.0,49,127
93,replace,14.0,N,[,CC(=O)Nc1cccc([,CC(=O)Nc1cccc(N,15,replace [ at position 14 with N,flow_matching,0.3,2.0,49,127
94,add,15.0,C,,CC(=O)Nc1cccc(N,CC(=O)Nc1cccc(NC,16,add C at position 15,flow_matching,0.3,2.0,49,127
95,add,16.0,(,,CC(=O)Nc1cccc(NC,CC(=O)Nc1cccc(NC(,17,add ( at position 16,flow_matching,0.3,2.0,49,127
96,add,17.0,=,,CC(=O)Nc1cccc(NC(,CC(=O)Nc1cccc(NC(=,18,add = at position 17,flow_matching,0.3,2.0,49,127
97,add,18.0,O,,CC(=O)Nc1cccc(NC(=,CC(=O)Nc1cccc(NC(=O,19,add O at position 18,flow_matching,0.3,2.0,49,127
98,add,19.0,),,CC(=O)Nc1cccc(NC(=O,CC(=O)Nc1cccc(NC(=O),20,add ) at position 19,flow_matching,0.3,2.0,49,127
99,add,20.0,C,,CC(=O)Nc1cccc(NC(=O),CC(=O)Nc1cccc(NC(=O)C,21,add C at position 20,flow_matching,0.3,2.0,49,127
100,add,21.0,C,,CC(=O)Nc1cccc(NC(=O)C,CC(=O)Nc1cccc(NC(=O)CC,22,add C at position 21,flow_matching,0.3,2.0,49,127
101,add,22.0,c,,CC(=O)Nc1cccc(NC(=O)CC,CC(=O)Nc1cccc(NC(=O)CCc,23,add c at position 22,flow_matching,0.3,2.0,49,127
102,add,23.0,2,,CC(=O)Nc1cccc(NC(=O)CCc,CC(=O)Nc1cccc(NC(=O)CCc2,24,add 2 at position 23,flow_matching,0.3,2.0,49,127
103,add,24.0,c,,CC(=O)Nc1cccc(NC(=O)CCc2,CC(=O)Nc1cccc(NC(=O)CCc2c,25,add c at position 24,flow_matching,0.3,2.0,49,127
104,add,25.0,(,,CC(=O)Nc1cccc(NC(=O)CCc2c,CC(=O)Nc1cccc(NC(=O)CCc2c(,26,add ( at position 25,flow_matching,0.3,2.0,49,127
105,add,26.0,C,,CC(=O)Nc1cccc(NC(=O)CCc2c(,CC(=O)Nc1cccc(NC(=O)CCc2c(C,27,add C at position 26,flow_matching,0.3,2.0,49,127
106,add,27.0,),,CC(=O)Nc1cccc(NC(=O)CCc2c(C,CC(=O)Nc1cccc(NC(=O)CCc2c(C),28,add ) at position 27,flow_matching,0.3,2.0,49,127
107,add,28.0,[,,CC(=O)Nc1cccc(NC(=O)CCc2c(C),CC(=O)Nc1cccc(NC(=O)CCc2c(C)[,29,add [ at position 28,flow_matching,0.3,2.0,49,127
108,add,29.0,n,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[n,30,add n at position 29,flow_matching,0.3,2.0,49,127
109,add,30.0,H,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[n,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH,31,add H at position 30,flow_matching,0.3,2.0,49,127
110,add,31.0,],,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH],32,add ] at position 31,flow_matching,0.3,2.0,49,127
111,add,32.0,c,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH],CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c,33,add c at position 32,flow_matching,0.3,2.0,49,127
112,add,33.0,(,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(,34,add ( at position 33,flow_matching,0.3,2.0,49,127
113,add,34.0,=,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=,35,add = at position 34,flow_matching,0.3,2.0,49,127
114,add,35.0,S,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S,36,add S at position 35,flow_matching,0.3,2.0,49,127
115,add,36.0,),,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S),37,add ) at position 36,flow_matching,0.3,2.0,49,127
116,add,37.0,[,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S),CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[,38,add [ at position 37,flow_matching,0.3,2.0,49,127
117,add,38.0,n,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[n,39,add n at position 38,flow_matching,0.3,2.0,49,127
118,add,39.0,H,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[n,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH,40,add H at position 39,flow_matching,0.3,2.0,49,127
119,add,40.0,],,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH],41,add ] at position 40,flow_matching,0.3,2.0,49,127
120,add,41.0,c,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH],CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c,42,add c at position 41,flow_matching,0.3,2.0,49,127
121,add,42.0,2,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2,43,add 2 at position 42,flow_matching,0.3,2.0,49,127
122,add,43.0,=,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=,44,add = at position 43,flow_matching,0.3,2.0,49,127
123,add,44.0,O,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=O,45,add O at position 44,flow_matching,0.3,2.0,49,127
124,add,45.0,),,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=O,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=O),46,add ) at position 45,flow_matching,0.3,2.0,49,127
125,add,46.0,c,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=O),CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=O)c,47,add c at position 46,flow_matching,0.3,2.0,49,127
126,add,47.0,1,,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=O)c,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=O)c1,48,add 1 at position 47,flow_matching,0.3,2.0,49,127
127,add,48.0,"
",,CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=O)c1,"CC(=O)Nc1cccc(NC(=O)CCc2c(C)[nH]c(=S)[nH]c2=O)c1
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,127
