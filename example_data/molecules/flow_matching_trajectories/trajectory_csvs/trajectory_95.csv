step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,51,244
1,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,51,244
2,remove,0.0,c,,c,,0,remove c from position 0,flow_matching,0.3,2.0,51,244
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,51,244
4,replace,0.0,H,C,C,H,1,replace <PERSON> at position 0 with H,flow_matching,0.3,2.0,51,244
5,replace,0.0,@,H,H,@,1,replace H at position 0 with @,flow_matching,0.3,2.0,51,244
6,replace,0.0,C,@,@,C,1,replace @ at position 0 with C,flow_matching,0.3,2.0,51,244
7,replace,0.0,+,C,C,+,1,replace <PERSON> at position 0 with +,flow_matching,0.3,2.0,51,244
8,add,0.0,7,,+,7+,2,add 7 at position 0,flow_matching,0.3,2.0,51,244
9,replace,0.0,l,7,7+,l+,2,replace 7 at position 0 with l,flow_matching,0.3,2.0,51,244
10,remove,0.0,l,,l+,+,1,remove l from position 0,flow_matching,0.3,2.0,51,244
11,replace,0.0,F,+,+,F,1,replace + at position 0 with F,flow_matching,0.3,2.0,51,244
12,remove,0.0,F,,F,,0,remove F from position 0,flow_matching,0.3,2.0,51,244
13,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,51,244
14,replace,0.0,C,@,@,C,1,replace @ at position 0 with C,flow_matching,0.3,2.0,51,244
15,replace,0.0,I,C,C,I,1,replace C at position 0 with I,flow_matching,0.3,2.0,51,244
16,replace,0.0,3,I,I,3,1,replace I at position 0 with 3,flow_matching,0.3,2.0,51,244
17,remove,0.0,3,,3,,0,remove 3 from position 0,flow_matching,0.3,2.0,51,244
18,add,0.0,n,,,n,1,add n at position 0,flow_matching,0.3,2.0,51,244
19,replace,0.0,I,n,n,I,1,replace n at position 0 with I,flow_matching,0.3,2.0,51,244
20,add,1.0,H,,I,IH,2,add H at position 1,flow_matching,0.3,2.0,51,244
21,add,1.0,],,IH,I]H,3,add ] at position 1,flow_matching,0.3,2.0,51,244
22,add,3.0,],,I]H,I]H],4,add ] at position 3,flow_matching,0.3,2.0,51,244
23,add,4.0,5,,I]H],I]H]5,5,add 5 at position 4,flow_matching,0.3,2.0,51,244
24,remove,4.0,5,,I]H]5,I]H],4,remove 5 from position 4,flow_matching,0.3,2.0,51,244
25,replace,0.0,C,I,I]H],C]H],4,replace I at position 0 with C,flow_matching,0.3,2.0,51,244
26,add,0.0,7,,C]H],7C]H],5,add 7 at position 0,flow_matching,0.3,2.0,51,244
27,replace,1.0,(,C,7C]H],7(]H],5,replace C at position 1 with (,flow_matching,0.3,2.0,51,244
28,replace,0.0,C,7,7(]H],C(]H],5,replace 7 at position 0 with C,flow_matching,0.3,2.0,51,244
29,add,1.0,+,,C(]H],C+(]H],6,add + at position 1,flow_matching,0.3,2.0,51,244
30,replace,1.0,=,+,C+(]H],C=(]H],6,replace + at position 1 with =,flow_matching,0.3,2.0,51,244
31,add,2.0,S,,C=(]H],C=S(]H],7,add S at position 2,flow_matching,0.3,2.0,51,244
32,replace,2.0,C,S,C=S(]H],C=C(]H],7,replace S at position 2 with C,flow_matching,0.3,2.0,51,244
33,replace,1.0,2,=,C=C(]H],C2C(]H],7,replace = at position 1 with 2,flow_matching,0.3,2.0,51,244
34,replace,1.0,=,2,C2C(]H],C=C(]H],7,replace 2 at position 1 with =,flow_matching,0.3,2.0,51,244
35,replace,3.0,],(,C=C(]H],C=C]]H],7,replace ( at position 3 with ],flow_matching,0.3,2.0,51,244
36,add,0.0,\,,C=C]]H],\C=C]]H],8,add \ at position 0,flow_matching,0.3,2.0,51,244
37,replace,0.0,C,\,\C=C]]H],CC=C]]H],8,replace \ at position 0 with C,flow_matching,0.3,2.0,51,244
38,replace,1.0,=,C,CC=C]]H],C==C]]H],8,replace C at position 1 with =,flow_matching,0.3,2.0,51,244
39,add,2.0,F,,C==C]]H],C=F=C]]H],9,add F at position 2,flow_matching,0.3,2.0,51,244
40,replace,5.0,+,],C=F=C]]H],C=F=C+]H],9,replace ] at position 5 with +,flow_matching,0.3,2.0,51,244
41,replace,2.0,C,F,C=F=C+]H],C=C=C+]H],9,replace F at position 2 with C,flow_matching,0.3,2.0,51,244
42,add,9.0,(,,C=C=C+]H],C=C=C+]H](,10,add ( at position 9,flow_matching,0.3,2.0,51,244
43,replace,3.0,C,=,C=C=C+]H](,C=CCC+]H](,10,replace = at position 3 with C,flow_matching,0.3,2.0,51,244
44,replace,3.0,7,C,C=CCC+]H](,C=C7C+]H](,10,replace C at position 3 with 7,flow_matching,0.3,2.0,51,244
45,replace,9.0,r,(,C=C7C+]H](,C=C7C+]H]r,10,replace ( at position 9 with r,flow_matching,0.3,2.0,51,244
46,replace,3.0,C,7,C=C7C+]H]r,C=CCC+]H]r,10,replace 7 at position 3 with C,flow_matching,0.3,2.0,51,244
47,replace,4.0,N,C,C=CCC+]H]r,C=CCN+]H]r,10,replace C at position 4 with N,flow_matching,0.3,2.0,51,244
48,add,4.0,3,,C=CCN+]H]r,C=CC3N+]H]r,11,add 3 at position 4,flow_matching,0.3,2.0,51,244
49,replace,4.0,N,3,C=CC3N+]H]r,C=CCNN+]H]r,11,replace 3 at position 4 with N,flow_matching,0.3,2.0,51,244
50,remove,2.0,C,,C=CCNN+]H]r,C=CNN+]H]r,10,remove C from position 2,flow_matching,0.3,2.0,51,244
51,replace,3.0,@,N,C=CNN+]H]r,C=C@N+]H]r,10,replace N at position 3 with @,flow_matching,0.3,2.0,51,244
52,replace,3.0,C,@,C=C@N+]H]r,C=CCN+]H]r,10,replace @ at position 3 with C,flow_matching,0.3,2.0,51,244
53,replace,3.0,B,C,C=CCN+]H]r,C=CBN+]H]r,10,replace C at position 3 with B,flow_matching,0.3,2.0,51,244
54,add,9.0,=,,C=CBN+]H]r,C=CBN+]H]=r,11,add = at position 9,flow_matching,0.3,2.0,51,244
55,replace,3.0,C,B,C=CBN+]H]=r,C=CCN+]H]=r,11,replace B at position 3 with C,flow_matching,0.3,2.0,51,244
56,replace,5.0,(,+,C=CCN+]H]=r,C=CCN(]H]=r,11,replace + at position 5 with (,flow_matching,0.3,2.0,51,244
57,replace,6.0,C,],C=CCN(]H]=r,C=CCN(CH]=r,11,replace ] at position 6 with C,flow_matching,0.3,2.0,51,244
58,replace,0.0,#,C,C=CCN(CH]=r,#=CCN(CH]=r,11,replace C at position 0 with #,flow_matching,0.3,2.0,51,244
59,remove,2.0,C,,#=CCN(CH]=r,#=CN(CH]=r,10,remove C from position 2,flow_matching,0.3,2.0,51,244
60,add,0.0,=,,#=CN(CH]=r,=#=CN(CH]=r,11,add = at position 0,flow_matching,0.3,2.0,51,244
61,add,3.0,7,,=#=CN(CH]=r,=#=7CN(CH]=r,12,add 7 at position 3,flow_matching,0.3,2.0,51,244
62,replace,0.0,C,=,=#=7CN(CH]=r,C#=7CN(CH]=r,12,replace = at position 0 with C,flow_matching,0.3,2.0,51,244
63,remove,10.0,=,,C#=7CN(CH]=r,C#=7CN(CH]r,11,remove = from position 10,flow_matching,0.3,2.0,51,244
64,replace,8.0,1,H,C#=7CN(CH]r,C#=7CN(C1]r,11,replace H at position 8 with 1,flow_matching,0.3,2.0,51,244
65,add,10.0,/,,C#=7CN(C1]r,C#=7CN(C1]/r,12,add / at position 10,flow_matching,0.3,2.0,51,244
66,replace,1.0,=,#,C#=7CN(C1]/r,C==7CN(C1]/r,12,replace # at position 1 with =,flow_matching,0.3,2.0,51,244
67,replace,10.0,r,/,C==7CN(C1]/r,C==7CN(C1]rr,12,replace / at position 10 with r,flow_matching,0.3,2.0,51,244
68,replace,2.0,C,=,C==7CN(C1]rr,C=C7CN(C1]rr,12,replace = at position 2 with C,flow_matching,0.3,2.0,51,244
69,remove,9.0,],,C=C7CN(C1]rr,C=C7CN(C1rr,11,remove ] from position 9,flow_matching,0.3,2.0,51,244
70,replace,10.0,6,r,C=C7CN(C1rr,C=C7CN(C1r6,11,replace r at position 10 with 6,flow_matching,0.3,2.0,51,244
71,remove,1.0,=,,C=C7CN(C1r6,CC7CN(C1r6,10,remove = from position 1,flow_matching,0.3,2.0,51,244
72,remove,6.0,C,,CC7CN(C1r6,CC7CN(1r6,9,remove C from position 6,flow_matching,0.3,2.0,51,244
73,replace,6.0,5,1,CC7CN(1r6,CC7CN(5r6,9,replace 1 at position 6 with 5,flow_matching,0.3,2.0,51,244
74,replace,4.0,4,N,CC7CN(5r6,CC7C4(5r6,9,replace N at position 4 with 4,flow_matching,0.3,2.0,51,244
75,remove,1.0,C,,CC7C4(5r6,C7C4(5r6,8,remove C from position 1,flow_matching,0.3,2.0,51,244
76,remove,3.0,4,,C7C4(5r6,C7C(5r6,7,remove 4 from position 3,flow_matching,0.3,2.0,51,244
77,remove,3.0,(,,C7C(5r6,C7C5r6,6,remove ( from position 3,flow_matching,0.3,2.0,51,244
78,replace,1.0,=,7,C7C5r6,C=C5r6,6,replace 7 at position 1 with =,flow_matching,0.3,2.0,51,244
79,replace,5.0,l,6,C=C5r6,C=C5rl,6,replace 6 at position 5 with l,flow_matching,0.3,2.0,51,244
80,remove,4.0,r,,C=C5rl,C=C5l,5,remove r from position 4,flow_matching,0.3,2.0,51,244
81,remove,4.0,l,,C=C5l,C=C5,4,remove l from position 4,flow_matching,0.3,2.0,51,244
82,replace,3.0,C,5,C=C5,C=CC,4,replace 5 at position 3 with C,flow_matching,0.3,2.0,51,244
83,add,4.0,=,,C=CC,C=CC=,5,add = at position 4,flow_matching,0.3,2.0,51,244
84,remove,1.0,=,,C=CC=,CCC=,4,remove = from position 1,flow_matching,0.3,2.0,51,244
85,remove,3.0,=,,CCC=,CCC,3,remove = from position 3,flow_matching,0.3,2.0,51,244
86,remove,2.0,C,,CCC,CC,2,remove C from position 2,flow_matching,0.3,2.0,51,244
87,remove,0.0,C,,CC,C,1,remove C from position 0,flow_matching,0.3,2.0,51,244
88,add,0.0,1,,C,1C,2,add 1 at position 0,flow_matching,0.3,2.0,51,244
89,add,2.0,c,,1C,1Cc,3,add c at position 2,flow_matching,0.3,2.0,51,244
90,replace,0.0,S,1,1Cc,SCc,3,replace 1 at position 0 with S,flow_matching,0.3,2.0,51,244
91,add,2.0,#,,SCc,SC#c,4,add # at position 2,flow_matching,0.3,2.0,51,244
92,remove,3.0,c,,SC#c,SC#,3,remove c from position 3,flow_matching,0.3,2.0,51,244
93,replace,1.0,O,C,SC#,SO#,3,replace C at position 1 with O,flow_matching,0.3,2.0,51,244
94,replace,1.0,=,O,SO#,S=#,3,replace O at position 1 with =,flow_matching,0.3,2.0,51,244
95,replace,2.0,I,#,S=#,S=I,3,replace # at position 2 with I,flow_matching,0.3,2.0,51,244
96,remove,0.0,S,,S=I,=I,2,remove S from position 0,flow_matching,0.3,2.0,51,244
97,add,2.0,/,,=I,=I/,3,add / at position 2,flow_matching,0.3,2.0,51,244
98,add,2.0,6,,=I/,=I6/,4,add 6 at position 2,flow_matching,0.3,2.0,51,244
99,replace,3.0,1,/,=I6/,=I61,4,replace / at position 3 with 1,flow_matching,0.3,2.0,51,244
100,replace,0.0,C,=,=I61,CI61,4,replace = at position 0 with C,flow_matching,0.3,2.0,51,244
101,remove,3.0,1,,CI61,CI6,3,remove 1 from position 3,flow_matching,0.3,2.0,51,244
102,replace,1.0,=,I,CI6,C=6,3,replace I at position 1 with =,flow_matching,0.3,2.0,51,244
103,add,3.0,@,,C=6,C=6@,4,add @ at position 3,flow_matching,0.3,2.0,51,244
104,add,2.0,H,,C=6@,C=H6@,5,add H at position 2,flow_matching,0.3,2.0,51,244
105,replace,2.0,C,H,C=H6@,C=C6@,5,replace H at position 2 with C,flow_matching,0.3,2.0,51,244
106,add,5.0,H,,C=C6@,C=C6@H,6,add H at position 5,flow_matching,0.3,2.0,51,244
107,remove,5.0,H,,C=C6@H,C=C6@,5,remove H from position 5,flow_matching,0.3,2.0,51,244
108,add,3.0,+,,C=C6@,C=C+6@,6,add + at position 3,flow_matching,0.3,2.0,51,244
109,add,0.0,4,,C=C+6@,4C=C+6@,7,add 4 at position 0,flow_matching,0.3,2.0,51,244
110,add,2.0,o,,4C=C+6@,4Co=C+6@,8,add o at position 2,flow_matching,0.3,2.0,51,244
111,add,6.0,@,,4Co=C+6@,4Co=C+@6@,9,add @ at position 6,flow_matching,0.3,2.0,51,244
112,replace,5.0,\,+,4Co=C+@6@,4Co=C\@6@,9,replace + at position 5 with \,flow_matching,0.3,2.0,51,244
113,replace,3.0,\,=,4Co=C\@6@,4Co\C\@6@,9,replace = at position 3 with \,flow_matching,0.3,2.0,51,244
114,add,3.0,H,,4Co\C\@6@,4CoH\C\@6@,10,add H at position 3,flow_matching,0.3,2.0,51,244
115,remove,9.0,@,,4CoH\C\@6@,4CoH\C\@6,9,remove @ from position 9,flow_matching,0.3,2.0,51,244
116,replace,0.0,C,4,4CoH\C\@6,CCoH\C\@6,9,replace 4 at position 0 with C,flow_matching,0.3,2.0,51,244
117,remove,6.0,\,,CCoH\C\@6,CCoH\C@6,8,remove \ from position 6,flow_matching,0.3,2.0,51,244
118,replace,1.0,=,C,CCoH\C@6,C=oH\C@6,8,replace C at position 1 with =,flow_matching,0.3,2.0,51,244
119,replace,2.0,C,o,C=oH\C@6,C=CH\C@6,8,replace o at position 2 with C,flow_matching,0.3,2.0,51,244
120,replace,3.0,C,H,C=CH\C@6,C=CC\C@6,8,replace H at position 3 with C,flow_matching,0.3,2.0,51,244
121,add,8.0,l,,C=CC\C@6,C=CC\C@6l,9,add l at position 8,flow_matching,0.3,2.0,51,244
122,replace,0.0,\,C,C=CC\C@6l,\=CC\C@6l,9,replace C at position 0 with \,flow_matching,0.3,2.0,51,244
123,replace,2.0,=,C,\=CC\C@6l,\==C\C@6l,9,replace C at position 2 with =,flow_matching,0.3,2.0,51,244
124,replace,7.0,],6,\==C\C@6l,\==C\C@]l,9,replace 6 at position 7 with ],flow_matching,0.3,2.0,51,244
125,replace,3.0,S,C,\==C\C@]l,\==S\C@]l,9,replace C at position 3 with S,flow_matching,0.3,2.0,51,244
126,add,5.0,F,,\==S\C@]l,\==S\FC@]l,10,add F at position 5,flow_matching,0.3,2.0,51,244
127,replace,0.0,C,\,\==S\FC@]l,C==S\FC@]l,10,replace \ at position 0 with C,flow_matching,0.3,2.0,51,244
128,replace,9.0,O,l,C==S\FC@]l,C==S\FC@]O,10,replace l at position 9 with O,flow_matching,0.3,2.0,51,244
129,replace,2.0,C,=,C==S\FC@]O,C=CS\FC@]O,10,replace = at position 2 with C,flow_matching,0.3,2.0,51,244
130,replace,3.0,C,S,C=CS\FC@]O,C=CC\FC@]O,10,replace S at position 3 with C,flow_matching,0.3,2.0,51,244
131,replace,4.0,N,\,C=CC\FC@]O,C=CCNFC@]O,10,replace \ at position 4 with N,flow_matching,0.3,2.0,51,244
132,replace,5.0,O,F,C=CCNFC@]O,C=CCNOC@]O,10,replace F at position 5 with O,flow_matching,0.3,2.0,51,244
133,add,4.0,S,,C=CCNOC@]O,C=CCSNOC@]O,11,add S at position 4,flow_matching,0.3,2.0,51,244
134,replace,4.0,=,S,C=CCSNOC@]O,C=CC=NOC@]O,11,replace S at position 4 with =,flow_matching,0.3,2.0,51,244
135,remove,4.0,=,,C=CC=NOC@]O,C=CCNOC@]O,10,remove = from position 4,flow_matching,0.3,2.0,51,244
136,replace,5.0,(,O,C=CCNOC@]O,C=CCN(C@]O,10,replace O at position 5 with (,flow_matching,0.3,2.0,51,244
137,add,3.0,n,,C=CCN(C@]O,C=CnCN(C@]O,11,add n at position 3,flow_matching,0.3,2.0,51,244
138,replace,5.0,-,N,C=CnCN(C@]O,C=CnC-(C@]O,11,replace N at position 5 with -,flow_matching,0.3,2.0,51,244
139,remove,10.0,O,,C=CnC-(C@]O,C=CnC-(C@],10,remove O from position 10,flow_matching,0.3,2.0,51,244
140,remove,1.0,=,,C=CnC-(C@],CCnC-(C@],9,remove = from position 1,flow_matching,0.3,2.0,51,244
141,remove,4.0,-,,CCnC-(C@],CCnC(C@],8,remove - from position 4,flow_matching,0.3,2.0,51,244
142,remove,3.0,C,,CCnC(C@],CCn(C@],7,remove C from position 3,flow_matching,0.3,2.0,51,244
143,add,4.0,S,,CCn(C@],CCn(SC@],8,add S at position 4,flow_matching,0.3,2.0,51,244
144,replace,1.0,=,C,CCn(SC@],C=n(SC@],8,replace C at position 1 with =,flow_matching,0.3,2.0,51,244
145,remove,3.0,(,,C=n(SC@],C=nSC@],7,remove ( from position 3,flow_matching,0.3,2.0,51,244
146,add,5.0,+,,C=nSC@],C=nSC+@],8,add + at position 5,flow_matching,0.3,2.0,51,244
147,replace,2.0,C,n,C=nSC+@],C=CSC+@],8,replace n at position 2 with C,flow_matching,0.3,2.0,51,244
148,replace,3.0,C,S,C=CSC+@],C=CCC+@],8,replace S at position 3 with C,flow_matching,0.3,2.0,51,244
149,remove,6.0,@,,C=CCC+@],C=CCC+],7,remove @ from position 6,flow_matching,0.3,2.0,51,244
150,replace,4.0,N,C,C=CCC+],C=CCN+],7,replace C at position 4 with N,flow_matching,0.3,2.0,51,244
151,replace,1.0,3,=,C=CCN+],C3CCN+],7,replace = at position 1 with 3,flow_matching,0.3,2.0,51,244
152,add,3.0,S,,C3CCN+],C3CSCN+],8,add S at position 3,flow_matching,0.3,2.0,51,244
153,replace,1.0,=,3,C3CSCN+],C=CSCN+],8,replace 3 at position 1 with =,flow_matching,0.3,2.0,51,244
154,add,1.0,6,,C=CSCN+],C6=CSCN+],9,add 6 at position 1,flow_matching,0.3,2.0,51,244
155,remove,6.0,N,,C6=CSCN+],C6=CSC+],8,remove N from position 6,flow_matching,0.3,2.0,51,244
156,replace,1.0,=,6,C6=CSC+],C==CSC+],8,replace 6 at position 1 with =,flow_matching,0.3,2.0,51,244
157,replace,2.0,C,=,C==CSC+],C=CCSC+],8,replace = at position 2 with C,flow_matching,0.3,2.0,51,244
158,add,8.0,),,C=CCSC+],C=CCSC+]),9,add ) at position 8,flow_matching,0.3,2.0,51,244
159,replace,4.0,N,S,C=CCSC+]),C=CCNC+]),9,replace S at position 4 with N,flow_matching,0.3,2.0,51,244
160,remove,4.0,N,,C=CCNC+]),C=CCC+]),8,remove N from position 4,flow_matching,0.3,2.0,51,244
161,add,4.0,+,,C=CCC+]),C=CC+C+]),9,add + at position 4,flow_matching,0.3,2.0,51,244
162,replace,4.0,N,+,C=CC+C+]),C=CCNC+]),9,replace + at position 4 with N,flow_matching,0.3,2.0,51,244
163,remove,7.0,],,C=CCNC+]),C=CCNC+),8,remove ] from position 7,flow_matching,0.3,2.0,51,244
164,replace,1.0,S,=,C=CCNC+),CSCCNC+),8,replace = at position 1 with S,flow_matching,0.3,2.0,51,244
165,add,1.0,6,,CSCCNC+),C6SCCNC+),9,add 6 at position 1,flow_matching,0.3,2.0,51,244
166,replace,1.0,=,6,C6SCCNC+),C=SCCNC+),9,replace 6 at position 1 with =,flow_matching,0.3,2.0,51,244
167,add,3.0,],,C=SCCNC+),C=S]CCNC+),10,add ] at position 3,flow_matching,0.3,2.0,51,244
168,replace,8.0,c,+,C=S]CCNC+),C=S]CCNCc),10,replace + at position 8 with c,flow_matching,0.3,2.0,51,244
169,remove,1.0,=,,C=S]CCNCc),CS]CCNCc),9,remove = from position 1,flow_matching,0.3,2.0,51,244
170,replace,0.0,(,C,CS]CCNCc),(S]CCNCc),9,replace C at position 0 with (,flow_matching,0.3,2.0,51,244
171,add,5.0,@,,(S]CCNCc),(S]CC@NCc),10,add @ at position 5,flow_matching,0.3,2.0,51,244
172,replace,0.0,C,(,(S]CC@NCc),CS]CC@NCc),10,replace ( at position 0 with C,flow_matching,0.3,2.0,51,244
173,replace,6.0,[,N,CS]CC@NCc),CS]CC@[Cc),10,replace N at position 6 with [,flow_matching,0.3,2.0,51,244
174,remove,1.0,S,,CS]CC@[Cc),C]CC@[Cc),9,remove S from position 1,flow_matching,0.3,2.0,51,244
175,remove,5.0,[,,C]CC@[Cc),C]CC@Cc),8,remove [ from position 5,flow_matching,0.3,2.0,51,244
176,replace,0.0,/,C,C]CC@Cc),/]CC@Cc),8,replace C at position 0 with /,flow_matching,0.3,2.0,51,244
177,replace,7.0,2,),/]CC@Cc),/]CC@Cc2,8,replace ) at position 7 with 2,flow_matching,0.3,2.0,51,244
178,add,0.0,-,,/]CC@Cc2,-/]CC@Cc2,9,add - at position 0,flow_matching,0.3,2.0,51,244
179,replace,0.0,C,-,-/]CC@Cc2,C/]CC@Cc2,9,replace - at position 0 with C,flow_matching,0.3,2.0,51,244
180,replace,1.0,=,/,C/]CC@Cc2,C=]CC@Cc2,9,replace / at position 1 with =,flow_matching,0.3,2.0,51,244
181,remove,8.0,2,,C=]CC@Cc2,C=]CC@Cc,8,remove 2 from position 8,flow_matching,0.3,2.0,51,244
182,replace,2.0,C,],C=]CC@Cc,C=CCC@Cc,8,replace ] at position 2 with C,flow_matching,0.3,2.0,51,244
183,add,1.0,H,,C=CCC@Cc,CH=CCC@Cc,9,add H at position 1,flow_matching,0.3,2.0,51,244
184,replace,1.0,1,H,CH=CCC@Cc,C1=CCC@Cc,9,replace H at position 1 with 1,flow_matching,0.3,2.0,51,244
185,remove,5.0,C,,C1=CCC@Cc,C1=CC@Cc,8,remove C from position 5,flow_matching,0.3,2.0,51,244
186,replace,1.0,=,1,C1=CC@Cc,C==CC@Cc,8,replace 1 at position 1 with =,flow_matching,0.3,2.0,51,244
187,add,1.0,@,,C==CC@Cc,C@==CC@Cc,9,add @ at position 1,flow_matching,0.3,2.0,51,244
188,remove,8.0,c,,C@==CC@Cc,C@==CC@C,8,remove c from position 8,flow_matching,0.3,2.0,51,244
189,remove,7.0,C,,C@==CC@C,C@==CC@,7,remove C from position 7,flow_matching,0.3,2.0,51,244
190,replace,1.0,=,@,C@==CC@,C===CC@,7,replace @ at position 1 with =,flow_matching,0.3,2.0,51,244
191,replace,2.0,C,=,C===CC@,C=C=CC@,7,replace = at position 2 with C,flow_matching,0.3,2.0,51,244
192,replace,3.0,C,=,C=C=CC@,C=CCCC@,7,replace = at position 3 with C,flow_matching,0.3,2.0,51,244
193,replace,4.0,n,C,C=CCCC@,C=CCnC@,7,replace C at position 4 with n,flow_matching,0.3,2.0,51,244
194,replace,4.0,N,n,C=CCnC@,C=CCNC@,7,replace n at position 4 with N,flow_matching,0.3,2.0,51,244
195,replace,5.0,(,C,C=CCNC@,C=CCN(@,7,replace C at position 5 with (,flow_matching,0.3,2.0,51,244
196,replace,4.0,r,N,C=CCN(@,C=CCr(@,7,replace N at position 4 with r,flow_matching,0.3,2.0,51,244
197,replace,2.0,#,C,C=CCr(@,C=#Cr(@,7,replace C at position 2 with #,flow_matching,0.3,2.0,51,244
198,replace,2.0,C,#,C=#Cr(@,C=CCr(@,7,replace # at position 2 with C,flow_matching,0.3,2.0,51,244
199,replace,4.0,N,r,C=CCr(@,C=CCN(@,7,replace r at position 4 with N,flow_matching,0.3,2.0,51,244
200,replace,6.0,C,@,C=CCN(@,C=CCN(C,7,replace @ at position 6 with C,flow_matching,0.3,2.0,51,244
201,add,7.0,c,,C=CCN(C,C=CCN(Cc,8,add c at position 7,flow_matching,0.3,2.0,51,244
202,add,8.0,1,,C=CCN(Cc,C=CCN(Cc1,9,add 1 at position 8,flow_matching,0.3,2.0,51,244
203,add,9.0,c,,C=CCN(Cc1,C=CCN(Cc1c,10,add c at position 9,flow_matching,0.3,2.0,51,244
204,add,10.0,c,,C=CCN(Cc1c,C=CCN(Cc1cc,11,add c at position 10,flow_matching,0.3,2.0,51,244
205,add,11.0,c,,C=CCN(Cc1cc,C=CCN(Cc1ccc,12,add c at position 11,flow_matching,0.3,2.0,51,244
206,add,12.0,c,,C=CCN(Cc1ccc,C=CCN(Cc1cccc,13,add c at position 12,flow_matching,0.3,2.0,51,244
207,add,13.0,(,,C=CCN(Cc1cccc,C=CCN(Cc1cccc(,14,add ( at position 13,flow_matching,0.3,2.0,51,244
208,add,14.0,[,,C=CCN(Cc1cccc(,C=CCN(Cc1cccc([,15,add [ at position 14,flow_matching,0.3,2.0,51,244
209,add,15.0,N,,C=CCN(Cc1cccc([,C=CCN(Cc1cccc([N,16,add N at position 15,flow_matching,0.3,2.0,51,244
210,add,16.0,+,,C=CCN(Cc1cccc([N,C=CCN(Cc1cccc([N+,17,add + at position 16,flow_matching,0.3,2.0,51,244
211,add,17.0,],,C=CCN(Cc1cccc([N+,C=CCN(Cc1cccc([N+],18,add ] at position 17,flow_matching,0.3,2.0,51,244
212,add,18.0,(,,C=CCN(Cc1cccc([N+],C=CCN(Cc1cccc([N+](,19,add ( at position 18,flow_matching,0.3,2.0,51,244
213,add,19.0,=,,C=CCN(Cc1cccc([N+](,C=CCN(Cc1cccc([N+](=,20,add = at position 19,flow_matching,0.3,2.0,51,244
214,add,20.0,O,,C=CCN(Cc1cccc([N+](=,C=CCN(Cc1cccc([N+](=O,21,add O at position 20,flow_matching,0.3,2.0,51,244
215,add,21.0,),,C=CCN(Cc1cccc([N+](=O,C=CCN(Cc1cccc([N+](=O),22,add ) at position 21,flow_matching,0.3,2.0,51,244
216,add,22.0,[,,C=CCN(Cc1cccc([N+](=O),C=CCN(Cc1cccc([N+](=O)[,23,add [ at position 22,flow_matching,0.3,2.0,51,244
217,add,23.0,O,,C=CCN(Cc1cccc([N+](=O)[,C=CCN(Cc1cccc([N+](=O)[O,24,add O at position 23,flow_matching,0.3,2.0,51,244
218,add,24.0,-,,C=CCN(Cc1cccc([N+](=O)[O,C=CCN(Cc1cccc([N+](=O)[O-,25,add - at position 24,flow_matching,0.3,2.0,51,244
219,add,25.0,],,C=CCN(Cc1cccc([N+](=O)[O-,C=CCN(Cc1cccc([N+](=O)[O-],26,add ] at position 25,flow_matching,0.3,2.0,51,244
220,add,26.0,),,C=CCN(Cc1cccc([N+](=O)[O-],C=CCN(Cc1cccc([N+](=O)[O-]),27,add ) at position 26,flow_matching,0.3,2.0,51,244
221,add,27.0,c,,C=CCN(Cc1cccc([N+](=O)[O-]),C=CCN(Cc1cccc([N+](=O)[O-])c,28,add c at position 27,flow_matching,0.3,2.0,51,244
222,add,28.0,1,,C=CCN(Cc1cccc([N+](=O)[O-])c,C=CCN(Cc1cccc([N+](=O)[O-])c1,29,add 1 at position 28,flow_matching,0.3,2.0,51,244
223,add,29.0,),,C=CCN(Cc1cccc([N+](=O)[O-])c1,C=CCN(Cc1cccc([N+](=O)[O-])c1),30,add ) at position 29,flow_matching,0.3,2.0,51,244
224,add,30.0,C,,C=CCN(Cc1cccc([N+](=O)[O-])c1),C=CCN(Cc1cccc([N+](=O)[O-])c1)C,31,add C at position 30,flow_matching,0.3,2.0,51,244
225,add,31.0,(,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(,32,add ( at position 31,flow_matching,0.3,2.0,51,244
226,add,32.0,=,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=,33,add = at position 32,flow_matching,0.3,2.0,51,244
227,add,33.0,O,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O,34,add O at position 33,flow_matching,0.3,2.0,51,244
228,add,34.0,),,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O),35,add ) at position 34,flow_matching,0.3,2.0,51,244
229,add,35.0,N,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O),C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)N,36,add N at position 35,flow_matching,0.3,2.0,51,244
230,add,36.0,c,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)N,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc,37,add c at position 36,flow_matching,0.3,2.0,51,244
231,add,37.0,1,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1,38,add 1 at position 37,flow_matching,0.3,2.0,51,244
232,add,38.0,c,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1c,39,add c at position 38,flow_matching,0.3,2.0,51,244
233,add,39.0,c,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1c,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc,40,add c at position 39,flow_matching,0.3,2.0,51,244
234,add,40.0,(,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(,41,add ( at position 40,flow_matching,0.3,2.0,51,244
235,add,41.0,O,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(O,42,add O at position 41,flow_matching,0.3,2.0,51,244
236,add,42.0,C,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(O,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC,43,add C at position 42,flow_matching,0.3,2.0,51,244
237,add,43.0,),,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC),44,add ) at position 43,flow_matching,0.3,2.0,51,244
238,add,44.0,c,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC),C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)c,45,add c at position 44,flow_matching,0.3,2.0,51,244
239,add,45.0,c,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)c,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)cc,46,add c at position 45,flow_matching,0.3,2.0,51,244
240,add,46.0,c,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)cc,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)ccc,47,add c at position 46,flow_matching,0.3,2.0,51,244
241,add,47.0,1,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)ccc,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)ccc1,48,add 1 at position 47,flow_matching,0.3,2.0,51,244
242,add,48.0,C,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)ccc1,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)ccc1C,49,add C at position 48,flow_matching,0.3,2.0,51,244
243,add,49.0,l,,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)ccc1C,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)ccc1Cl,50,add l at position 49,flow_matching,0.3,2.0,51,244
244,add,50.0,"
",,C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)ccc1Cl,"C=CCN(Cc1cccc([N+](=O)[O-])c1)C(=O)Nc1cc(OC)ccc1Cl
",51,"add 
 at position 50",flow_matching,0.3,2.0,51,244
