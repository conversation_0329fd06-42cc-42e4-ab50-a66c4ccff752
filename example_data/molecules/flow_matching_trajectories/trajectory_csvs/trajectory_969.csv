step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,158
1,add,0.0,o,,,o,1,add o at position 0,flow_matching,0.3,2.0,49,158
2,remove,0.0,o,,o,,0,remove o from position 0,flow_matching,0.3,2.0,49,158
3,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,49,158
4,replace,0.0,o,I,I,o,1,replace I at position 0 with o,flow_matching,0.3,2.0,49,158
5,replace,0.0,C,o,o,C,1,replace o at position 0 with C,flow_matching,0.3,2.0,49,158
6,replace,0.0,],C,C,],1,replace <PERSON> at position 0 with ],flow_matching,0.3,2.0,49,158
7,replace,0.0,(,],],(,1,replace ] at position 0 with (,flow_matching,0.3,2.0,49,158
8,remove,0.0,(,,(,,0,remove ( from position 0,flow_matching,0.3,2.0,49,158
9,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,49,158
10,replace,0.0,C,I,I,C,1,replace I at position 0 with C,flow_matching,0.3,2.0,49,158
11,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,49,158
12,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,49,158
13,replace,2.0,1,(,CC(,CC1,3,replace ( at position 2 with 1,flow_matching,0.3,2.0,49,158
14,add,1.0,C,,CC1,CCC1,4,add C at position 1,flow_matching,0.3,2.0,49,158
15,replace,2.0,(,C,CCC1,CC(1,4,replace C at position 2 with (,flow_matching,0.3,2.0,49,158
16,replace,3.0,=,1,CC(1,CC(=,4,replace 1 at position 3 with =,flow_matching,0.3,2.0,49,158
17,replace,2.0,-,(,CC(=,CC-=,4,replace ( at position 2 with -,flow_matching,0.3,2.0,49,158
18,remove,3.0,=,,CC-=,CC-,3,remove = from position 3,flow_matching,0.3,2.0,49,158
19,replace,2.0,(,-,CC-,CC(,3,replace - at position 2 with (,flow_matching,0.3,2.0,49,158
20,add,3.0,=,,CC(,CC(=,4,add = at position 3,flow_matching,0.3,2.0,49,158
21,add,4.0,n,,CC(=,CC(=n,5,add n at position 4,flow_matching,0.3,2.0,49,158
22,replace,3.0,7,=,CC(=n,CC(7n,5,replace = at position 3 with 7,flow_matching,0.3,2.0,49,158
23,remove,2.0,(,,CC(7n,CC7n,4,remove ( from position 2,flow_matching,0.3,2.0,49,158
24,replace,0.0,l,C,CC7n,lC7n,4,replace C at position 0 with l,flow_matching,0.3,2.0,49,158
25,replace,0.0,C,l,lC7n,CC7n,4,replace l at position 0 with C,flow_matching,0.3,2.0,49,158
26,add,4.0,c,,CC7n,CC7nc,5,add c at position 4,flow_matching,0.3,2.0,49,158
27,replace,2.0,3,7,CC7nc,CC3nc,5,replace 7 at position 2 with 3,flow_matching,0.3,2.0,49,158
28,replace,2.0,S,3,CC3nc,CCSnc,5,replace 3 at position 2 with S,flow_matching,0.3,2.0,49,158
29,add,2.0,5,,CCSnc,CC5Snc,6,add 5 at position 2,flow_matching,0.3,2.0,49,158
30,replace,0.0,\,C,CC5Snc,\C5Snc,6,replace C at position 0 with \,flow_matching,0.3,2.0,49,158
31,add,5.0,@,,\C5Snc,\C5Sn@c,7,add @ at position 5,flow_matching,0.3,2.0,49,158
32,replace,4.0,@,n,\C5Sn@c,\C5S@@c,7,replace n at position 4 with @,flow_matching,0.3,2.0,49,158
33,add,0.0,5,,\C5S@@c,5\C5S@@c,8,add 5 at position 0,flow_matching,0.3,2.0,49,158
34,remove,6.0,@,,5\C5S@@c,5\C5S@c,7,remove @ from position 6,flow_matching,0.3,2.0,49,158
35,remove,1.0,\,,5\C5S@c,5C5S@c,6,remove \ from position 1,flow_matching,0.3,2.0,49,158
36,replace,2.0,s,5,5C5S@c,5CsS@c,6,replace 5 at position 2 with s,flow_matching,0.3,2.0,49,158
37,add,5.0,3,,5CsS@c,5CsS@3c,7,add 3 at position 5,flow_matching,0.3,2.0,49,158
38,add,6.0,4,,5CsS@3c,5CsS@34c,8,add 4 at position 6,flow_matching,0.3,2.0,49,158
39,replace,2.0,o,s,5CsS@34c,5CoS@34c,8,replace s at position 2 with o,flow_matching,0.3,2.0,49,158
40,replace,0.0,C,5,5CoS@34c,CCoS@34c,8,replace 5 at position 0 with C,flow_matching,0.3,2.0,49,158
41,add,0.0,7,,CCoS@34c,7CCoS@34c,9,add 7 at position 0,flow_matching,0.3,2.0,49,158
42,replace,0.0,C,7,7CCoS@34c,CCCoS@34c,9,replace 7 at position 0 with C,flow_matching,0.3,2.0,49,158
43,replace,7.0,],4,CCCoS@34c,CCCoS@3]c,9,replace 4 at position 7 with ],flow_matching,0.3,2.0,49,158
44,replace,7.0,[,],CCCoS@3]c,CCCoS@3[c,9,replace ] at position 7 with [,flow_matching,0.3,2.0,49,158
45,replace,2.0,),C,CCCoS@3[c,CC)oS@3[c,9,replace C at position 2 with ),flow_matching,0.3,2.0,49,158
46,replace,1.0,s,C,CC)oS@3[c,Cs)oS@3[c,9,replace C at position 1 with s,flow_matching,0.3,2.0,49,158
47,remove,6.0,3,,Cs)oS@3[c,Cs)oS@[c,8,remove 3 from position 6,flow_matching,0.3,2.0,49,158
48,replace,1.0,3,s,Cs)oS@[c,C3)oS@[c,8,replace s at position 1 with 3,flow_matching,0.3,2.0,49,158
49,add,7.0,o,,C3)oS@[c,C3)oS@[oc,9,add o at position 7,flow_matching,0.3,2.0,49,158
50,replace,1.0,C,3,C3)oS@[oc,CC)oS@[oc,9,replace 3 at position 1 with C,flow_matching,0.3,2.0,49,158
51,replace,2.0,(,),CC)oS@[oc,CC(oS@[oc,9,replace ) at position 2 with (,flow_matching,0.3,2.0,49,158
52,replace,1.0,+,C,CC(oS@[oc,C+(oS@[oc,9,replace C at position 1 with +,flow_matching,0.3,2.0,49,158
53,replace,1.0,C,+,C+(oS@[oc,CC(oS@[oc,9,replace + at position 1 with C,flow_matching,0.3,2.0,49,158
54,replace,7.0,#,o,CC(oS@[oc,CC(oS@[#c,9,replace o at position 7 with #,flow_matching,0.3,2.0,49,158
55,add,9.0,4,,CC(oS@[#c,CC(oS@[#c4,10,add 4 at position 9,flow_matching,0.3,2.0,49,158
56,replace,4.0,2,S,CC(oS@[#c4,CC(o2@[#c4,10,replace S at position 4 with 2,flow_matching,0.3,2.0,49,158
57,replace,3.0,=,o,CC(o2@[#c4,CC(=2@[#c4,10,replace o at position 3 with =,flow_matching,0.3,2.0,49,158
58,remove,4.0,2,,CC(=2@[#c4,CC(=@[#c4,9,remove 2 from position 4,flow_matching,0.3,2.0,49,158
59,replace,0.0,+,C,CC(=@[#c4,+C(=@[#c4,9,replace C at position 0 with +,flow_matching,0.3,2.0,49,158
60,remove,8.0,4,,+C(=@[#c4,+C(=@[#c,8,remove 4 from position 8,flow_matching,0.3,2.0,49,158
61,add,7.0,+,,+C(=@[#c,+C(=@[#+c,9,add + at position 7,flow_matching,0.3,2.0,49,158
62,add,2.0,N,,+C(=@[#+c,+CN(=@[#+c,10,add N at position 2,flow_matching,0.3,2.0,49,158
63,replace,8.0,#,+,+CN(=@[#+c,+CN(=@[##c,10,replace + at position 8 with #,flow_matching,0.3,2.0,49,158
64,add,4.0,l,,+CN(=@[##c,+CN(l=@[##c,11,add l at position 4,flow_matching,0.3,2.0,49,158
65,add,8.0,2,,+CN(l=@[##c,+CN(l=@[2##c,12,add 2 at position 8,flow_matching,0.3,2.0,49,158
66,remove,3.0,(,,+CN(l=@[2##c,+CNl=@[2##c,11,remove ( from position 3,flow_matching,0.3,2.0,49,158
67,replace,0.0,C,+,+CNl=@[2##c,CCNl=@[2##c,11,replace + at position 0 with C,flow_matching,0.3,2.0,49,158
68,replace,2.0,(,N,CCNl=@[2##c,CC(l=@[2##c,11,replace N at position 2 with (,flow_matching,0.3,2.0,49,158
69,add,1.0,o,,CC(l=@[2##c,CoC(l=@[2##c,12,add o at position 1,flow_matching,0.3,2.0,49,158
70,replace,1.0,C,o,CoC(l=@[2##c,CCC(l=@[2##c,12,replace o at position 1 with C,flow_matching,0.3,2.0,49,158
71,add,11.0,-,,CCC(l=@[2##c,CCC(l=@[2##-c,13,add - at position 11,flow_matching,0.3,2.0,49,158
72,remove,12.0,c,,CCC(l=@[2##-c,CCC(l=@[2##-,12,remove c from position 12,flow_matching,0.3,2.0,49,158
73,remove,5.0,=,,CCC(l=@[2##-,CCC(l@[2##-,11,remove = from position 5,flow_matching,0.3,2.0,49,158
74,replace,2.0,(,C,CCC(l@[2##-,CC((l@[2##-,11,replace C at position 2 with (,flow_matching,0.3,2.0,49,158
75,replace,3.0,B,(,CC((l@[2##-,CC(Bl@[2##-,11,replace ( at position 3 with B,flow_matching,0.3,2.0,49,158
76,replace,3.0,=,B,CC(Bl@[2##-,CC(=l@[2##-,11,replace B at position 3 with =,flow_matching,0.3,2.0,49,158
77,remove,10.0,-,,CC(=l@[2##-,CC(=l@[2##,10,remove - from position 10,flow_matching,0.3,2.0,49,158
78,remove,1.0,C,,CC(=l@[2##,C(=l@[2##,9,remove C from position 1,flow_matching,0.3,2.0,49,158
79,add,8.0,2,,C(=l@[2##,C(=l@[2#2#,10,add 2 at position 8,flow_matching,0.3,2.0,49,158
80,replace,1.0,C,(,C(=l@[2#2#,CC=l@[2#2#,10,replace ( at position 1 with C,flow_matching,0.3,2.0,49,158
81,remove,9.0,#,,CC=l@[2#2#,CC=l@[2#2,9,remove # from position 9,flow_matching,0.3,2.0,49,158
82,remove,6.0,2,,CC=l@[2#2,CC=l@[#2,8,remove 2 from position 6,flow_matching,0.3,2.0,49,158
83,add,0.0,=,,CC=l@[#2,=CC=l@[#2,9,add = at position 0,flow_matching,0.3,2.0,49,158
84,replace,0.0,C,=,=CC=l@[#2,CCC=l@[#2,9,replace = at position 0 with C,flow_matching,0.3,2.0,49,158
85,remove,3.0,=,,CCC=l@[#2,CCCl@[#2,8,remove = from position 3,flow_matching,0.3,2.0,49,158
86,replace,2.0,(,C,CCCl@[#2,CC(l@[#2,8,replace C at position 2 with (,flow_matching,0.3,2.0,49,158
87,add,7.0,5,,CC(l@[#2,CC(l@[#52,9,add 5 at position 7,flow_matching,0.3,2.0,49,158
88,remove,8.0,2,,CC(l@[#52,CC(l@[#5,8,remove 2 from position 8,flow_matching,0.3,2.0,49,158
89,replace,3.0,=,l,CC(l@[#5,CC(=@[#5,8,replace l at position 3 with =,flow_matching,0.3,2.0,49,158
90,replace,4.0,O,@,CC(=@[#5,CC(=O[#5,8,replace @ at position 4 with O,flow_matching,0.3,2.0,49,158
91,replace,5.0,),[,CC(=O[#5,CC(=O)#5,8,replace [ at position 5 with ),flow_matching,0.3,2.0,49,158
92,add,7.0,3,,CC(=O)#5,CC(=O)#35,9,add 3 at position 7,flow_matching,0.3,2.0,49,158
93,remove,2.0,(,,CC(=O)#35,CC=O)#35,8,remove ( from position 2,flow_matching,0.3,2.0,49,158
94,replace,5.0,/,#,CC=O)#35,CC=O)/35,8,replace # at position 5 with /,flow_matching,0.3,2.0,49,158
95,add,0.0,I,,CC=O)/35,ICC=O)/35,9,add I at position 0,flow_matching,0.3,2.0,49,158
96,remove,3.0,=,,ICC=O)/35,ICCO)/35,8,remove = from position 3,flow_matching,0.3,2.0,49,158
97,replace,2.0,=,C,ICCO)/35,IC=O)/35,8,replace C at position 2 with =,flow_matching,0.3,2.0,49,158
98,add,3.0,[,,IC=O)/35,IC=[O)/35,9,add [ at position 3,flow_matching,0.3,2.0,49,158
99,remove,2.0,=,,IC=[O)/35,IC[O)/35,8,remove = from position 2,flow_matching,0.3,2.0,49,158
100,add,7.0,I,,IC[O)/35,IC[O)/3I5,9,add I at position 7,flow_matching,0.3,2.0,49,158
101,replace,0.0,C,I,IC[O)/3I5,CC[O)/3I5,9,replace I at position 0 with C,flow_matching,0.3,2.0,49,158
102,remove,3.0,O,,CC[O)/3I5,CC[)/3I5,8,remove O from position 3,flow_matching,0.3,2.0,49,158
103,add,0.0,-,,CC[)/3I5,-CC[)/3I5,9,add - at position 0,flow_matching,0.3,2.0,49,158
104,add,3.0,2,,-CC[)/3I5,-CC2[)/3I5,10,add 2 at position 3,flow_matching,0.3,2.0,49,158
105,add,6.0,/,,-CC2[)/3I5,-CC2[)//3I5,11,add / at position 6,flow_matching,0.3,2.0,49,158
106,add,2.0,-,,-CC2[)//3I5,-C-C2[)//3I5,12,add - at position 2,flow_matching,0.3,2.0,49,158
107,replace,0.0,C,-,-C-C2[)//3I5,CC-C2[)//3I5,12,replace - at position 0 with C,flow_matching,0.3,2.0,49,158
108,replace,0.0,-,C,CC-C2[)//3I5,-C-C2[)//3I5,12,replace C at position 0 with -,flow_matching,0.3,2.0,49,158
109,remove,1.0,C,,-C-C2[)//3I5,--C2[)//3I5,11,remove C from position 1,flow_matching,0.3,2.0,49,158
110,add,6.0,l,,--C2[)//3I5,--C2[)l//3I5,12,add l at position 6,flow_matching,0.3,2.0,49,158
111,replace,0.0,C,-,--C2[)l//3I5,C-C2[)l//3I5,12,replace - at position 0 with C,flow_matching,0.3,2.0,49,158
112,replace,1.0,C,-,C-C2[)l//3I5,CCC2[)l//3I5,12,replace - at position 1 with C,flow_matching,0.3,2.0,49,158
113,replace,2.0,(,C,CCC2[)l//3I5,CC(2[)l//3I5,12,replace C at position 2 with (,flow_matching,0.3,2.0,49,158
114,replace,3.0,=,2,CC(2[)l//3I5,CC(=[)l//3I5,12,replace 2 at position 3 with =,flow_matching,0.3,2.0,49,158
115,replace,4.0,O,[,CC(=[)l//3I5,CC(=O)l//3I5,12,replace [ at position 4 with O,flow_matching,0.3,2.0,49,158
116,replace,6.0,N,l,CC(=O)l//3I5,CC(=O)N//3I5,12,replace l at position 6 with N,flow_matching,0.3,2.0,49,158
117,replace,7.0,c,/,CC(=O)N//3I5,CC(=O)Nc/3I5,12,replace / at position 7 with c,flow_matching,0.3,2.0,49,158
118,replace,8.0,1,/,CC(=O)Nc/3I5,CC(=O)Nc13I5,12,replace / at position 8 with 1,flow_matching,0.3,2.0,49,158
119,replace,9.0,c,3,CC(=O)Nc13I5,CC(=O)Nc1cI5,12,replace 3 at position 9 with c,flow_matching,0.3,2.0,49,158
120,replace,10.0,c,I,CC(=O)Nc1cI5,CC(=O)Nc1cc5,12,replace I at position 10 with c,flow_matching,0.3,2.0,49,158
121,replace,11.0,c,5,CC(=O)Nc1cc5,CC(=O)Nc1ccc,12,replace 5 at position 11 with c,flow_matching,0.3,2.0,49,158
122,add,12.0,(,,CC(=O)Nc1ccc,CC(=O)Nc1ccc(,13,add ( at position 12,flow_matching,0.3,2.0,49,158
123,add,13.0,C,,CC(=O)Nc1ccc(,CC(=O)Nc1ccc(C,14,add C at position 13,flow_matching,0.3,2.0,49,158
124,add,14.0,N,,CC(=O)Nc1ccc(C,CC(=O)Nc1ccc(CN,15,add N at position 14,flow_matching,0.3,2.0,49,158
125,add,15.0,2,,CC(=O)Nc1ccc(CN,CC(=O)Nc1ccc(CN2,16,add 2 at position 15,flow_matching,0.3,2.0,49,158
126,add,16.0,C,,CC(=O)Nc1ccc(CN2,CC(=O)Nc1ccc(CN2C,17,add C at position 16,flow_matching,0.3,2.0,49,158
127,add,17.0,C,,CC(=O)Nc1ccc(CN2C,CC(=O)Nc1ccc(CN2CC,18,add C at position 17,flow_matching,0.3,2.0,49,158
128,add,18.0,[,,CC(=O)Nc1ccc(CN2CC,CC(=O)Nc1ccc(CN2CC[,19,add [ at position 18,flow_matching,0.3,2.0,49,158
129,add,19.0,N,,CC(=O)Nc1ccc(CN2CC[,CC(=O)Nc1ccc(CN2CC[N,20,add N at position 19,flow_matching,0.3,2.0,49,158
130,add,20.0,H,,CC(=O)Nc1ccc(CN2CC[N,CC(=O)Nc1ccc(CN2CC[NH,21,add H at position 20,flow_matching,0.3,2.0,49,158
131,add,21.0,+,,CC(=O)Nc1ccc(CN2CC[NH,CC(=O)Nc1ccc(CN2CC[NH+,22,add + at position 21,flow_matching,0.3,2.0,49,158
132,add,22.0,],,CC(=O)Nc1ccc(CN2CC[NH+,CC(=O)Nc1ccc(CN2CC[NH+],23,add ] at position 22,flow_matching,0.3,2.0,49,158
133,add,23.0,(,,CC(=O)Nc1ccc(CN2CC[NH+],CC(=O)Nc1ccc(CN2CC[NH+](,24,add ( at position 23,flow_matching,0.3,2.0,49,158
134,add,24.0,C,,CC(=O)Nc1ccc(CN2CC[NH+](,CC(=O)Nc1ccc(CN2CC[NH+](C,25,add C at position 24,flow_matching,0.3,2.0,49,158
135,add,25.0,3,,CC(=O)Nc1ccc(CN2CC[NH+](C,CC(=O)Nc1ccc(CN2CC[NH+](C3,26,add 3 at position 25,flow_matching,0.3,2.0,49,158
136,add,26.0,C,,CC(=O)Nc1ccc(CN2CC[NH+](C3,CC(=O)Nc1ccc(CN2CC[NH+](C3C,27,add C at position 26,flow_matching,0.3,2.0,49,158
137,add,27.0,C,,CC(=O)Nc1ccc(CN2CC[NH+](C3C,CC(=O)Nc1ccc(CN2CC[NH+](C3CC,28,add C at position 27,flow_matching,0.3,2.0,49,158
138,add,28.0,C,,CC(=O)Nc1ccc(CN2CC[NH+](C3CC,CC(=O)Nc1ccc(CN2CC[NH+](C3CCC,29,add C at position 28,flow_matching,0.3,2.0,49,158
139,add,29.0,C,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCC,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC,30,add C at position 29,flow_matching,0.3,2.0,49,158
140,add,30.0,3,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3,31,add 3 at position 30,flow_matching,0.3,2.0,49,158
141,add,31.0,),,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3),32,add ) at position 31,flow_matching,0.3,2.0,49,158
142,add,32.0,[,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3),CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[,33,add [ at position 32,flow_matching,0.3,2.0,49,158
143,add,33.0,C,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C,34,add C at position 33,flow_matching,0.3,2.0,49,158
144,add,34.0,@,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@,35,add @ at position 34,flow_matching,0.3,2.0,49,158
145,add,35.0,H,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H,36,add H at position 35,flow_matching,0.3,2.0,49,158
146,add,36.0,],,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H],37,add ] at position 36,flow_matching,0.3,2.0,49,158
147,add,37.0,(,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H],CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](,38,add ( at position 37,flow_matching,0.3,2.0,49,158
148,add,38.0,C,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](C,39,add C at position 38,flow_matching,0.3,2.0,49,158
149,add,39.0,C,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](C,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CC,40,add C at position 39,flow_matching,0.3,2.0,49,158
150,add,40.0,O,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CC,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO,41,add O at position 40,flow_matching,0.3,2.0,49,158
151,add,41.0,),,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO),42,add ) at position 41,flow_matching,0.3,2.0,49,158
152,add,42.0,C,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO),CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C,43,add C at position 42,flow_matching,0.3,2.0,49,158
153,add,43.0,2,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2,44,add 2 at position 43,flow_matching,0.3,2.0,49,158
154,add,44.0,),,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2),45,add ) at position 44,flow_matching,0.3,2.0,49,158
155,add,45.0,c,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2),CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2)c,46,add c at position 45,flow_matching,0.3,2.0,49,158
156,add,46.0,c,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2)c,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2)cc,47,add c at position 46,flow_matching,0.3,2.0,49,158
157,add,47.0,1,,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2)cc,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2)cc1,48,add 1 at position 47,flow_matching,0.3,2.0,49,158
158,add,48.0,"
",,CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2)cc1,"CC(=O)Nc1ccc(CN2CC[NH+](C3CCCC3)[C@H](CCO)C2)cc1
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,158
