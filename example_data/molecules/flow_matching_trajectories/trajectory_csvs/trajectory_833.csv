step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,42,134
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,42,134
2,add,1.0,S,,C,CS,2,add S at position 1,flow_matching,0.3,2.0,42,134
3,add,2.0,(,,CS,CS(,3,add ( at position 2,flow_matching,0.3,2.0,42,134
4,add,1.0,#,,CS(,C#S(,4,add # at position 1,flow_matching,0.3,2.0,42,134
5,add,0.0,s,,C#S(,sC#S(,5,add s at position 0,flow_matching,0.3,2.0,42,134
6,add,5.0,2,,sC#S(,sC#S(2,6,add 2 at position 5,flow_matching,0.3,2.0,42,134
7,add,3.0,F,,sC#S(2,sC#FS(2,7,add F at position 3,flow_matching,0.3,2.0,42,134
8,replace,0.0,C,s,sC#FS(2,CC#FS(2,7,replace s at position 0 with C,flow_matching,0.3,2.0,42,134
9,replace,3.0,\,F,CC#FS(2,CC#\S(2,7,replace F at position 3 with \,flow_matching,0.3,2.0,42,134
10,remove,0.0,C,,CC#\S(2,C#\S(2,6,remove C from position 0,flow_matching,0.3,2.0,42,134
11,replace,1.0,S,#,C#\S(2,CS\S(2,6,replace # at position 1 with S,flow_matching,0.3,2.0,42,134
12,replace,1.0,6,S,CS\S(2,C6\S(2,6,replace S at position 1 with 6,flow_matching,0.3,2.0,42,134
13,remove,1.0,6,,C6\S(2,C\S(2,5,remove 6 from position 1,flow_matching,0.3,2.0,42,134
14,replace,1.0,S,\,C\S(2,CSS(2,5,replace \ at position 1 with S,flow_matching,0.3,2.0,42,134
15,remove,1.0,S,,CSS(2,CS(2,4,remove S from position 1,flow_matching,0.3,2.0,42,134
16,replace,0.0,N,C,CS(2,NS(2,4,replace C at position 0 with N,flow_matching,0.3,2.0,42,134
17,replace,0.0,(,N,NS(2,(S(2,4,replace N at position 0 with (,flow_matching,0.3,2.0,42,134
18,replace,0.0,C,(,(S(2,CS(2,4,replace ( at position 0 with C,flow_matching,0.3,2.0,42,134
19,replace,3.0,=,2,CS(2,CS(=,4,replace 2 at position 3 with =,flow_matching,0.3,2.0,42,134
20,replace,1.0,I,S,CS(=,CI(=,4,replace S at position 1 with I,flow_matching,0.3,2.0,42,134
21,remove,0.0,C,,CI(=,I(=,3,remove C from position 0,flow_matching,0.3,2.0,42,134
22,add,0.0,],,I(=,]I(=,4,add ] at position 0,flow_matching,0.3,2.0,42,134
23,remove,1.0,I,,]I(=,](=,3,remove I from position 1,flow_matching,0.3,2.0,42,134
24,replace,1.0,C,(,](=,]C=,3,replace ( at position 1 with C,flow_matching,0.3,2.0,42,134
25,add,3.0,@,,]C=,]C=@,4,add @ at position 3,flow_matching,0.3,2.0,42,134
26,replace,0.0,3,],]C=@,3C=@,4,replace ] at position 0 with 3,flow_matching,0.3,2.0,42,134
27,replace,0.0,C,3,3C=@,CC=@,4,replace 3 at position 0 with C,flow_matching,0.3,2.0,42,134
28,replace,0.0,l,C,CC=@,lC=@,4,replace C at position 0 with l,flow_matching,0.3,2.0,42,134
29,replace,0.0,C,l,lC=@,CC=@,4,replace l at position 0 with C,flow_matching,0.3,2.0,42,134
30,add,4.0,@,,CC=@,CC=@@,5,add @ at position 4,flow_matching,0.3,2.0,42,134
31,replace,1.0,S,C,CC=@@,CS=@@,5,replace C at position 1 with S,flow_matching,0.3,2.0,42,134
32,replace,1.0,o,S,CS=@@,Co=@@,5,replace S at position 1 with o,flow_matching,0.3,2.0,42,134
33,replace,1.0,=,o,Co=@@,C==@@,5,replace o at position 1 with =,flow_matching,0.3,2.0,42,134
34,remove,0.0,C,,C==@@,==@@,4,remove C from position 0,flow_matching,0.3,2.0,42,134
35,replace,2.0,),@,==@@,==)@,4,replace @ at position 2 with ),flow_matching,0.3,2.0,42,134
36,add,3.0,(,,==)@,==)(@,5,add ( at position 3,flow_matching,0.3,2.0,42,134
37,add,2.0,O,,==)(@,==O)(@,6,add O at position 2,flow_matching,0.3,2.0,42,134
38,add,5.0,),,==O)(@,==O)()@,7,add ) at position 5,flow_matching,0.3,2.0,42,134
39,replace,0.0,C,=,==O)()@,C=O)()@,7,replace = at position 0 with C,flow_matching,0.3,2.0,42,134
40,remove,2.0,O,,C=O)()@,C=)()@,6,remove O from position 2,flow_matching,0.3,2.0,42,134
41,add,4.0,H,,C=)()@,C=)(H)@,7,add H at position 4,flow_matching,0.3,2.0,42,134
42,replace,1.0,S,=,C=)(H)@,CS)(H)@,7,replace = at position 1 with S,flow_matching,0.3,2.0,42,134
43,replace,2.0,s,),CS)(H)@,CSs(H)@,7,replace ) at position 2 with s,flow_matching,0.3,2.0,42,134
44,replace,2.0,(,s,CSs(H)@,CS((H)@,7,replace s at position 2 with (,flow_matching,0.3,2.0,42,134
45,replace,3.0,=,(,CS((H)@,CS(=H)@,7,replace ( at position 3 with =,flow_matching,0.3,2.0,42,134
46,add,5.0,r,,CS(=H)@,CS(=Hr)@,8,add r at position 5,flow_matching,0.3,2.0,42,134
47,add,8.0,N,,CS(=Hr)@,CS(=Hr)@N,9,add N at position 8,flow_matching,0.3,2.0,42,134
48,add,5.0,[,,CS(=Hr)@N,CS(=H[r)@N,10,add [ at position 5,flow_matching,0.3,2.0,42,134
49,add,6.0,+,,CS(=H[r)@N,CS(=H[+r)@N,11,add + at position 6,flow_matching,0.3,2.0,42,134
50,add,11.0,7,,CS(=H[+r)@N,CS(=H[+r)@N7,12,add 7 at position 11,flow_matching,0.3,2.0,42,134
51,add,12.0,2,,CS(=H[+r)@N7,CS(=H[+r)@N72,13,add 2 at position 12,flow_matching,0.3,2.0,42,134
52,replace,4.0,O,H,CS(=H[+r)@N72,CS(=O[+r)@N72,13,replace H at position 4 with O,flow_matching,0.3,2.0,42,134
53,replace,6.0,-,+,CS(=O[+r)@N72,CS(=O[-r)@N72,13,replace + at position 6 with -,flow_matching,0.3,2.0,42,134
54,add,11.0,F,,CS(=O[-r)@N72,CS(=O[-r)@NF72,14,add F at position 11,flow_matching,0.3,2.0,42,134
55,add,0.0,=,,CS(=O[-r)@NF72,=CS(=O[-r)@NF72,15,add = at position 0,flow_matching,0.3,2.0,42,134
56,replace,0.0,C,=,=CS(=O[-r)@NF72,CCS(=O[-r)@NF72,15,replace = at position 0 with C,flow_matching,0.3,2.0,42,134
57,add,13.0,1,,CCS(=O[-r)@NF72,CCS(=O[-r)@NF172,16,add 1 at position 13,flow_matching,0.3,2.0,42,134
58,add,0.0,#,,CCS(=O[-r)@NF172,#CCS(=O[-r)@NF172,17,add # at position 0,flow_matching,0.3,2.0,42,134
59,add,6.0,I,,#CCS(=O[-r)@NF172,#CCS(=IO[-r)@NF172,18,add I at position 6,flow_matching,0.3,2.0,42,134
60,replace,0.0,C,#,#CCS(=IO[-r)@NF172,CCCS(=IO[-r)@NF172,18,replace # at position 0 with C,flow_matching,0.3,2.0,42,134
61,replace,17.0,S,2,CCCS(=IO[-r)@NF172,CCCS(=IO[-r)@NF17S,18,replace 2 at position 17 with S,flow_matching,0.3,2.0,42,134
62,replace,1.0,S,C,CCCS(=IO[-r)@NF17S,CSCS(=IO[-r)@NF17S,18,replace C at position 1 with S,flow_matching,0.3,2.0,42,134
63,remove,2.0,C,,CSCS(=IO[-r)@NF17S,CSS(=IO[-r)@NF17S,17,remove C from position 2,flow_matching,0.3,2.0,42,134
64,replace,2.0,(,S,CSS(=IO[-r)@NF17S,CS((=IO[-r)@NF17S,17,replace S at position 2 with (,flow_matching,0.3,2.0,42,134
65,add,10.0,@,,CS((=IO[-r)@NF17S,CS((=IO[-r@)@NF17S,18,add @ at position 10,flow_matching,0.3,2.0,42,134
66,replace,3.0,=,(,CS((=IO[-r@)@NF17S,CS(==IO[-r@)@NF17S,18,replace ( at position 3 with =,flow_matching,0.3,2.0,42,134
67,add,1.0,B,,CS(==IO[-r@)@NF17S,CBS(==IO[-r@)@NF17S,19,add B at position 1,flow_matching,0.3,2.0,42,134
68,replace,9.0,B,-,CBS(==IO[-r@)@NF17S,CBS(==IO[Br@)@NF17S,19,replace - at position 9 with B,flow_matching,0.3,2.0,42,134
69,replace,8.0,I,[,CBS(==IO[Br@)@NF17S,CBS(==IOIBr@)@NF17S,19,replace [ at position 8 with I,flow_matching,0.3,2.0,42,134
70,add,13.0,7,,CBS(==IOIBr@)@NF17S,CBS(==IOIBr@)7@NF17S,20,add 7 at position 13,flow_matching,0.3,2.0,42,134
71,replace,7.0,o,O,CBS(==IOIBr@)7@NF17S,CBS(==IoIBr@)7@NF17S,20,replace O at position 7 with o,flow_matching,0.3,2.0,42,134
72,replace,1.0,S,B,CBS(==IoIBr@)7@NF17S,CSS(==IoIBr@)7@NF17S,20,replace B at position 1 with S,flow_matching,0.3,2.0,42,134
73,replace,2.0,(,S,CSS(==IoIBr@)7@NF17S,CS((==IoIBr@)7@NF17S,20,replace S at position 2 with (,flow_matching,0.3,2.0,42,134
74,remove,8.0,I,,CS((==IoIBr@)7@NF17S,CS((==IoBr@)7@NF17S,19,remove I from position 8,flow_matching,0.3,2.0,42,134
75,replace,15.0,7,F,CS((==IoBr@)7@NF17S,CS((==IoBr@)7@N717S,19,replace F at position 15 with 7,flow_matching,0.3,2.0,42,134
76,replace,3.0,=,(,CS((==IoBr@)7@N717S,CS(===IoBr@)7@N717S,19,replace ( at position 3 with =,flow_matching,0.3,2.0,42,134
77,replace,4.0,O,=,CS(===IoBr@)7@N717S,CS(=O=IoBr@)7@N717S,19,replace = at position 4 with O,flow_matching,0.3,2.0,42,134
78,replace,3.0,B,=,CS(=O=IoBr@)7@N717S,CS(BO=IoBr@)7@N717S,19,replace = at position 3 with B,flow_matching,0.3,2.0,42,134
79,add,14.0,o,,CS(BO=IoBr@)7@N717S,CS(BO=IoBr@)7@oN717S,20,add o at position 14,flow_matching,0.3,2.0,42,134
80,add,15.0,2,,CS(BO=IoBr@)7@oN717S,CS(BO=IoBr@)7@o2N717S,21,add 2 at position 15,flow_matching,0.3,2.0,42,134
81,remove,20.0,S,,CS(BO=IoBr@)7@o2N717S,CS(BO=IoBr@)7@o2N717,20,remove S from position 20,flow_matching,0.3,2.0,42,134
82,replace,3.0,=,B,CS(BO=IoBr@)7@o2N717,CS(=O=IoBr@)7@o2N717,20,replace B at position 3 with =,flow_matching,0.3,2.0,42,134
83,remove,4.0,O,,CS(=O=IoBr@)7@o2N717,CS(==IoBr@)7@o2N717,19,remove O from position 4,flow_matching,0.3,2.0,42,134
84,remove,3.0,=,,CS(==IoBr@)7@o2N717,CS(=IoBr@)7@o2N717,18,remove = from position 3,flow_matching,0.3,2.0,42,134
85,add,15.0,5,,CS(=IoBr@)7@o2N717,CS(=IoBr@)7@o2N5717,19,add 5 at position 15,flow_matching,0.3,2.0,42,134
86,replace,4.0,O,I,CS(=IoBr@)7@o2N5717,CS(=OoBr@)7@o2N5717,19,replace I at position 4 with O,flow_matching,0.3,2.0,42,134
87,replace,5.0,),o,CS(=OoBr@)7@o2N5717,CS(=O)Br@)7@o2N5717,19,replace o at position 5 with ),flow_matching,0.3,2.0,42,134
88,replace,6.0,(,B,CS(=O)Br@)7@o2N5717,CS(=O)(r@)7@o2N5717,19,replace B at position 6 with (,flow_matching,0.3,2.0,42,134
89,remove,4.0,O,,CS(=O)(r@)7@o2N5717,CS(=)(r@)7@o2N5717,18,remove O from position 4,flow_matching,0.3,2.0,42,134
90,remove,1.0,S,,CS(=)(r@)7@o2N5717,C(=)(r@)7@o2N5717,17,remove S from position 1,flow_matching,0.3,2.0,42,134
91,replace,1.0,S,(,C(=)(r@)7@o2N5717,CS=)(r@)7@o2N5717,17,replace ( at position 1 with S,flow_matching,0.3,2.0,42,134
92,add,3.0,],,CS=)(r@)7@o2N5717,CS=])(r@)7@o2N5717,18,add ] at position 3,flow_matching,0.3,2.0,42,134
93,add,16.0,#,,CS=])(r@)7@o2N5717,CS=])(r@)7@o2N57#17,19,add # at position 16,flow_matching,0.3,2.0,42,134
94,add,12.0,@,,CS=])(r@)7@o2N57#17,CS=])(r@)7@o@2N57#17,20,add @ at position 12,flow_matching,0.3,2.0,42,134
95,replace,2.0,(,=,CS=])(r@)7@o@2N57#17,CS(])(r@)7@o@2N57#17,20,replace = at position 2 with (,flow_matching,0.3,2.0,42,134
96,replace,3.0,=,],CS(])(r@)7@o@2N57#17,CS(=)(r@)7@o@2N57#17,20,replace ] at position 3 with =,flow_matching,0.3,2.0,42,134
97,replace,4.0,O,),CS(=)(r@)7@o@2N57#17,CS(=O(r@)7@o@2N57#17,20,replace ) at position 4 with O,flow_matching,0.3,2.0,42,134
98,replace,5.0,),(,CS(=O(r@)7@o@2N57#17,CS(=O)r@)7@o@2N57#17,20,replace ( at position 5 with ),flow_matching,0.3,2.0,42,134
99,replace,6.0,(,r,CS(=O)r@)7@o@2N57#17,CS(=O)(@)7@o@2N57#17,20,replace r at position 6 with (,flow_matching,0.3,2.0,42,134
100,replace,7.0,=,@,CS(=O)(@)7@o@2N57#17,CS(=O)(=)7@o@2N57#17,20,replace @ at position 7 with =,flow_matching,0.3,2.0,42,134
101,replace,8.0,O,),CS(=O)(=)7@o@2N57#17,CS(=O)(=O7@o@2N57#17,20,replace ) at position 8 with O,flow_matching,0.3,2.0,42,134
102,replace,9.0,),7,CS(=O)(=O7@o@2N57#17,CS(=O)(=O)@o@2N57#17,20,replace 7 at position 9 with ),flow_matching,0.3,2.0,42,134
103,replace,10.0,c,@,CS(=O)(=O)@o@2N57#17,CS(=O)(=O)co@2N57#17,20,replace @ at position 10 with c,flow_matching,0.3,2.0,42,134
104,replace,11.0,1,o,CS(=O)(=O)co@2N57#17,CS(=O)(=O)c1@2N57#17,20,replace o at position 11 with 1,flow_matching,0.3,2.0,42,134
105,replace,12.0,c,@,CS(=O)(=O)c1@2N57#17,CS(=O)(=O)c1c2N57#17,20,replace @ at position 12 with c,flow_matching,0.3,2.0,42,134
106,replace,13.0,c,2,CS(=O)(=O)c1c2N57#17,CS(=O)(=O)c1ccN57#17,20,replace 2 at position 13 with c,flow_matching,0.3,2.0,42,134
107,replace,14.0,c,N,CS(=O)(=O)c1ccN57#17,CS(=O)(=O)c1ccc57#17,20,replace N at position 14 with c,flow_matching,0.3,2.0,42,134
108,replace,15.0,(,5,CS(=O)(=O)c1ccc57#17,CS(=O)(=O)c1ccc(7#17,20,replace 5 at position 15 with (,flow_matching,0.3,2.0,42,134
109,replace,16.0,C,7,CS(=O)(=O)c1ccc(7#17,CS(=O)(=O)c1ccc(C#17,20,replace 7 at position 16 with C,flow_matching,0.3,2.0,42,134
110,replace,17.0,(,#,CS(=O)(=O)c1ccc(C#17,CS(=O)(=O)c1ccc(C(17,20,replace # at position 17 with (,flow_matching,0.3,2.0,42,134
111,replace,18.0,=,1,CS(=O)(=O)c1ccc(C(17,CS(=O)(=O)c1ccc(C(=7,20,replace 1 at position 18 with =,flow_matching,0.3,2.0,42,134
112,replace,19.0,O,7,CS(=O)(=O)c1ccc(C(=7,CS(=O)(=O)c1ccc(C(=O,20,replace 7 at position 19 with O,flow_matching,0.3,2.0,42,134
113,add,20.0,),,CS(=O)(=O)c1ccc(C(=O,CS(=O)(=O)c1ccc(C(=O),21,add ) at position 20,flow_matching,0.3,2.0,42,134
114,add,21.0,N,,CS(=O)(=O)c1ccc(C(=O),CS(=O)(=O)c1ccc(C(=O)N,22,add N at position 21,flow_matching,0.3,2.0,42,134
115,add,22.0,c,,CS(=O)(=O)c1ccc(C(=O)N,CS(=O)(=O)c1ccc(C(=O)Nc,23,add c at position 22,flow_matching,0.3,2.0,42,134
116,add,23.0,2,,CS(=O)(=O)c1ccc(C(=O)Nc,CS(=O)(=O)c1ccc(C(=O)Nc2,24,add 2 at position 23,flow_matching,0.3,2.0,42,134
117,add,24.0,c,,CS(=O)(=O)c1ccc(C(=O)Nc2,CS(=O)(=O)c1ccc(C(=O)Nc2c,25,add c at position 24,flow_matching,0.3,2.0,42,134
118,add,25.0,c,,CS(=O)(=O)c1ccc(C(=O)Nc2c,CS(=O)(=O)c1ccc(C(=O)Nc2cc,26,add c at position 25,flow_matching,0.3,2.0,42,134
119,add,26.0,c,,CS(=O)(=O)c1ccc(C(=O)Nc2cc,CS(=O)(=O)c1ccc(C(=O)Nc2ccc,27,add c at position 26,flow_matching,0.3,2.0,42,134
120,add,27.0,(,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(,28,add ( at position 27,flow_matching,0.3,2.0,42,134
121,add,28.0,F,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F,29,add F at position 28,flow_matching,0.3,2.0,42,134
122,add,29.0,),,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F),30,add ) at position 29,flow_matching,0.3,2.0,42,134
123,add,30.0,c,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F),CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c,31,add c at position 30,flow_matching,0.3,2.0,42,134
124,add,31.0,(,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(,32,add ( at position 31,flow_matching,0.3,2.0,42,134
125,add,32.0,F,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F,33,add F at position 32,flow_matching,0.3,2.0,42,134
126,add,33.0,),,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F),34,add ) at position 33,flow_matching,0.3,2.0,42,134
127,add,34.0,c,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F),CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c,35,add c at position 34,flow_matching,0.3,2.0,42,134
128,add,35.0,2,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2,36,add 2 at position 35,flow_matching,0.3,2.0,42,134
129,add,36.0,F,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F,37,add F at position 36,flow_matching,0.3,2.0,42,134
130,add,37.0,),,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F),38,add ) at position 37,flow_matching,0.3,2.0,42,134
131,add,38.0,c,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F),CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F)c,39,add c at position 38,flow_matching,0.3,2.0,42,134
132,add,39.0,c,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F)c,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F)cc,40,add c at position 39,flow_matching,0.3,2.0,42,134
133,add,40.0,1,,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F)cc,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F)cc1,41,add 1 at position 40,flow_matching,0.3,2.0,42,134
134,add,41.0,"
",,CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F)cc1,"CS(=O)(=O)c1ccc(C(=O)Nc2ccc(F)c(F)c2F)cc1
",42,"add 
 at position 41",flow_matching,0.3,2.0,42,134
