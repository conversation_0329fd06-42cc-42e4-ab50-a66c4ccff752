step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,90
1,add,0.0,),,,),1,add ) at position 0,flow_matching,0.3,2.0,43,90
2,replace,0.0,1,),),1,1,replace ) at position 0 with 1,flow_matching,0.3,2.0,43,90
3,replace,0.0,N,1,1,N,1,replace 1 at position 0 with N,flow_matching,0.3,2.0,43,90
4,add,0.0,[,,N,[N,2,add [ at position 0,flow_matching,0.3,2.0,43,90
5,remove,1.0,N,,[N,[,1,remove N from position 1,flow_matching,0.3,2.0,43,90
6,replace,0.0,@,[,[,@,1,replace [ at position 0 with @,flow_matching,0.3,2.0,43,90
7,replace,0.0,N,@,@,N,1,replace @ at position 0 with N,flow_matching,0.3,2.0,43,90
8,remove,0.0,N,,N,,0,remove N from position 0,flow_matching,0.3,2.0,43,90
9,add,0.0,=,,,=,1,add = at position 0,flow_matching,0.3,2.0,43,90
10,replace,0.0,I,=,=,I,1,replace = at position 0 with I,flow_matching,0.3,2.0,43,90
11,replace,0.0,N,I,I,N,1,replace I at position 0 with N,flow_matching,0.3,2.0,43,90
12,add,1.0,C,,N,NC,2,add C at position 1,flow_matching,0.3,2.0,43,90
13,add,2.0,(,,NC,NC(,3,add ( at position 2,flow_matching,0.3,2.0,43,90
14,replace,2.0,4,(,NC(,NC4,3,replace ( at position 2 with 4,flow_matching,0.3,2.0,43,90
15,add,0.0,o,,NC4,oNC4,4,add o at position 0,flow_matching,0.3,2.0,43,90
16,remove,0.0,o,,oNC4,NC4,3,remove o from position 0,flow_matching,0.3,2.0,43,90
17,replace,2.0,(,4,NC4,NC(,3,replace 4 at position 2 with (,flow_matching,0.3,2.0,43,90
18,remove,1.0,C,,NC(,N(,2,remove C from position 1,flow_matching,0.3,2.0,43,90
19,add,0.0,-,,N(,-N(,3,add - at position 0,flow_matching,0.3,2.0,43,90
20,add,1.0,(,,-N(,-(N(,4,add ( at position 1,flow_matching,0.3,2.0,43,90
21,replace,0.0,N,-,-(N(,N(N(,4,replace - at position 0 with N,flow_matching,0.3,2.0,43,90
22,add,4.0,s,,N(N(,N(N(s,5,add s at position 4,flow_matching,0.3,2.0,43,90
23,add,3.0,s,,N(N(s,N(Ns(s,6,add s at position 3,flow_matching,0.3,2.0,43,90
24,replace,1.0,C,(,N(Ns(s,NCNs(s,6,replace ( at position 1 with C,flow_matching,0.3,2.0,43,90
25,replace,2.0,(,N,NCNs(s,NC(s(s,6,replace N at position 2 with (,flow_matching,0.3,2.0,43,90
26,replace,3.0,#,s,NC(s(s,NC(#(s,6,replace s at position 3 with #,flow_matching,0.3,2.0,43,90
27,add,4.0,1,,NC(#(s,NC(#1(s,7,add 1 at position 4,flow_matching,0.3,2.0,43,90
28,replace,2.0,\,(,NC(#1(s,NC\#1(s,7,replace ( at position 2 with \,flow_matching,0.3,2.0,43,90
29,remove,2.0,\,,NC\#1(s,NC#1(s,6,remove \ from position 2,flow_matching,0.3,2.0,43,90
30,add,5.0,C,,NC#1(s,NC#1(Cs,7,add C at position 5,flow_matching,0.3,2.0,43,90
31,remove,1.0,C,,NC#1(Cs,N#1(Cs,6,remove C from position 1,flow_matching,0.3,2.0,43,90
32,remove,1.0,#,,N#1(Cs,N1(Cs,5,remove # from position 1,flow_matching,0.3,2.0,43,90
33,replace,2.0,c,(,N1(Cs,N1cCs,5,replace ( at position 2 with c,flow_matching,0.3,2.0,43,90
34,add,1.0,(,,N1cCs,N(1cCs,6,add ( at position 1,flow_matching,0.3,2.0,43,90
35,replace,1.0,7,(,N(1cCs,N71cCs,6,replace ( at position 1 with 7,flow_matching,0.3,2.0,43,90
36,replace,1.0,C,7,N71cCs,NC1cCs,6,replace 7 at position 1 with C,flow_matching,0.3,2.0,43,90
37,replace,5.0,2,s,NC1cCs,NC1cC2,6,replace s at position 5 with 2,flow_matching,0.3,2.0,43,90
38,add,6.0,],,NC1cC2,NC1cC2],7,add ] at position 6,flow_matching,0.3,2.0,43,90
39,remove,2.0,1,,NC1cC2],NCcC2],6,remove 1 from position 2,flow_matching,0.3,2.0,43,90
40,replace,2.0,(,c,NCcC2],NC(C2],6,replace c at position 2 with (,flow_matching,0.3,2.0,43,90
41,replace,0.0,S,N,NC(C2],SC(C2],6,replace N at position 0 with S,flow_matching,0.3,2.0,43,90
42,remove,0.0,S,,SC(C2],C(C2],5,remove S from position 0,flow_matching,0.3,2.0,43,90
43,replace,0.0,N,C,C(C2],N(C2],5,replace C at position 0 with N,flow_matching,0.3,2.0,43,90
44,replace,4.0,=,],N(C2],N(C2=,5,replace ] at position 4 with =,flow_matching,0.3,2.0,43,90
45,add,0.0,C,,N(C2=,CN(C2=,6,add C at position 0,flow_matching,0.3,2.0,43,90
46,add,4.0,B,,CN(C2=,CN(CB2=,7,add B at position 4,flow_matching,0.3,2.0,43,90
47,remove,0.0,C,,CN(CB2=,N(CB2=,6,remove C from position 0,flow_matching,0.3,2.0,43,90
48,replace,4.0,#,2,N(CB2=,N(CB#=,6,replace 2 at position 4 with #,flow_matching,0.3,2.0,43,90
49,replace,1.0,C,(,N(CB#=,NCCB#=,6,replace ( at position 1 with C,flow_matching,0.3,2.0,43,90
50,replace,2.0,(,C,NCCB#=,NC(B#=,6,replace C at position 2 with (,flow_matching,0.3,2.0,43,90
51,replace,3.0,=,B,NC(B#=,NC(=#=,6,replace B at position 3 with =,flow_matching,0.3,2.0,43,90
52,replace,4.0,O,#,NC(=#=,NC(=O=,6,replace # at position 4 with O,flow_matching,0.3,2.0,43,90
53,replace,5.0,),=,NC(=O=,NC(=O),6,replace = at position 5 with ),flow_matching,0.3,2.0,43,90
54,add,6.0,C,,NC(=O),NC(=O)C,7,add C at position 6,flow_matching,0.3,2.0,43,90
55,add,7.0,O,,NC(=O)C,NC(=O)CO,8,add O at position 7,flow_matching,0.3,2.0,43,90
56,add,8.0,c,,NC(=O)CO,NC(=O)COc,9,add c at position 8,flow_matching,0.3,2.0,43,90
57,add,9.0,1,,NC(=O)COc,NC(=O)COc1,10,add 1 at position 9,flow_matching,0.3,2.0,43,90
58,add,10.0,c,,NC(=O)COc1,NC(=O)COc1c,11,add c at position 10,flow_matching,0.3,2.0,43,90
59,add,11.0,c,,NC(=O)COc1c,NC(=O)COc1cc,12,add c at position 11,flow_matching,0.3,2.0,43,90
60,add,12.0,c,,NC(=O)COc1cc,NC(=O)COc1ccc,13,add c at position 12,flow_matching,0.3,2.0,43,90
61,add,13.0,(,,NC(=O)COc1ccc,NC(=O)COc1ccc(,14,add ( at position 13,flow_matching,0.3,2.0,43,90
62,add,14.0,C,,NC(=O)COc1ccc(,NC(=O)COc1ccc(C,15,add C at position 14,flow_matching,0.3,2.0,43,90
63,add,15.0,(,,NC(=O)COc1ccc(C,NC(=O)COc1ccc(C(,16,add ( at position 15,flow_matching,0.3,2.0,43,90
64,add,16.0,=,,NC(=O)COc1ccc(C(,NC(=O)COc1ccc(C(=,17,add = at position 16,flow_matching,0.3,2.0,43,90
65,add,17.0,O,,NC(=O)COc1ccc(C(=,NC(=O)COc1ccc(C(=O,18,add O at position 17,flow_matching,0.3,2.0,43,90
66,add,18.0,),,NC(=O)COc1ccc(C(=O,NC(=O)COc1ccc(C(=O),19,add ) at position 18,flow_matching,0.3,2.0,43,90
67,add,19.0,N,,NC(=O)COc1ccc(C(=O),NC(=O)COc1ccc(C(=O)N,20,add N at position 19,flow_matching,0.3,2.0,43,90
68,add,20.0,[,,NC(=O)COc1ccc(C(=O)N,NC(=O)COc1ccc(C(=O)N[,21,add [ at position 20,flow_matching,0.3,2.0,43,90
69,add,21.0,C,,NC(=O)COc1ccc(C(=O)N[,NC(=O)COc1ccc(C(=O)N[C,22,add C at position 21,flow_matching,0.3,2.0,43,90
70,add,22.0,@,,NC(=O)COc1ccc(C(=O)N[C,NC(=O)COc1ccc(C(=O)N[C@,23,add @ at position 22,flow_matching,0.3,2.0,43,90
71,add,23.0,H,,NC(=O)COc1ccc(C(=O)N[C@,NC(=O)COc1ccc(C(=O)N[C@H,24,add H at position 23,flow_matching,0.3,2.0,43,90
72,add,24.0,],,NC(=O)COc1ccc(C(=O)N[C@H,NC(=O)COc1ccc(C(=O)N[C@H],25,add ] at position 24,flow_matching,0.3,2.0,43,90
73,add,25.0,2,,NC(=O)COc1ccc(C(=O)N[C@H],NC(=O)COc1ccc(C(=O)N[C@H]2,26,add 2 at position 25,flow_matching,0.3,2.0,43,90
74,add,26.0,C,,NC(=O)COc1ccc(C(=O)N[C@H]2,NC(=O)COc1ccc(C(=O)N[C@H]2C,27,add C at position 26,flow_matching,0.3,2.0,43,90
75,add,27.0,C,,NC(=O)COc1ccc(C(=O)N[C@H]2C,NC(=O)COc1ccc(C(=O)N[C@H]2CC,28,add C at position 27,flow_matching,0.3,2.0,43,90
76,add,28.0,C,,NC(=O)COc1ccc(C(=O)N[C@H]2CC,NC(=O)COc1ccc(C(=O)N[C@H]2CCC,29,add C at position 28,flow_matching,0.3,2.0,43,90
77,add,29.0,c,,NC(=O)COc1ccc(C(=O)N[C@H]2CCC,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc,30,add c at position 29,flow_matching,0.3,2.0,43,90
78,add,30.0,3,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3,31,add 3 at position 30,flow_matching,0.3,2.0,43,90
79,add,31.0,c,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3c,32,add c at position 31,flow_matching,0.3,2.0,43,90
80,add,32.0,c,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3c,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3cc,33,add c at position 32,flow_matching,0.3,2.0,43,90
81,add,33.0,c,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3cc,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccc,34,add c at position 33,flow_matching,0.3,2.0,43,90
82,add,34.0,c,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccc,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3cccc,35,add c at position 34,flow_matching,0.3,2.0,43,90
83,add,35.0,c,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3cccc,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc,36,add c at position 35,flow_matching,0.3,2.0,43,90
84,add,36.0,3,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc3,37,add 3 at position 36,flow_matching,0.3,2.0,43,90
85,add,37.0,2,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc3,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32,38,add 2 at position 37,flow_matching,0.3,2.0,43,90
86,add,38.0,),,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32),39,add ) at position 38,flow_matching,0.3,2.0,43,90
87,add,39.0,c,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32),NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32)c,40,add c at position 39,flow_matching,0.3,2.0,43,90
88,add,40.0,c,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32)c,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32)cc,41,add c at position 40,flow_matching,0.3,2.0,43,90
89,add,41.0,1,,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32)cc,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32)cc1,42,add 1 at position 41,flow_matching,0.3,2.0,43,90
90,add,42.0,"
",,NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32)cc1,"NC(=O)COc1ccc(C(=O)N[C@H]2CCCc3ccccc32)cc1
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,90
