step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,58,166
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,58,166
2,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,58,166
3,remove,0.0,C,,Cc,c,1,remove C from position 0,flow_matching,0.3,2.0,58,166
4,add,1.0,6,,c,c6,2,add 6 at position 1,flow_matching,0.3,2.0,58,166
5,remove,1.0,6,,c6,c,1,remove 6 from position 1,flow_matching,0.3,2.0,58,166
6,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,58,166
7,add,1.0,<PERSON>,,<PERSON>,<PERSON>,2,add <PERSON> at position 1,flow_matching,0.3,2.0,58,166
8,replace,0.0,N,<PERSON>,<PERSON>,<PERSON>,2,replace C at position 0 with N,flow_matching,0.3,2.0,58,166
9,replace,1.0,1,C,NC,N1,2,replace C at position 1 with 1,flow_matching,0.3,2.0,58,166
10,add,0.0,/,,N1,/N1,3,add / at position 0,flow_matching,0.3,2.0,58,166
11,remove,0.0,/,,/N1,N1,2,remove / from position 0,flow_matching,0.3,2.0,58,166
12,replace,0.0,C,N,N1,C1,2,replace N at position 0 with C,flow_matching,0.3,2.0,58,166
13,replace,1.0,C,1,C1,CC,2,replace 1 at position 1 with C,flow_matching,0.3,2.0,58,166
14,add,2.0,O,,CC,CCO,3,add O at position 2,flow_matching,0.3,2.0,58,166
15,add,2.0,\,,CCO,CC\O,4,add \ at position 2,flow_matching,0.3,2.0,58,166
16,replace,2.0,O,\,CC\O,CCOO,4,replace \ at position 2 with O,flow_matching,0.3,2.0,58,166
17,add,1.0,c,,CCOO,CcCOO,5,add c at position 1,flow_matching,0.3,2.0,58,166
18,add,5.0,@,,CcCOO,CcCOO@,6,add @ at position 5,flow_matching,0.3,2.0,58,166
19,remove,4.0,O,,CcCOO@,CcCO@,5,remove O from position 4,flow_matching,0.3,2.0,58,166
20,remove,2.0,C,,CcCO@,CcO@,4,remove C from position 2,flow_matching,0.3,2.0,58,166
21,replace,1.0,C,c,CcO@,CCO@,4,replace c at position 1 with C,flow_matching,0.3,2.0,58,166
22,replace,3.0,C,@,CCO@,CCOC,4,replace @ at position 3 with C,flow_matching,0.3,2.0,58,166
23,replace,0.0,r,C,CCOC,rCOC,4,replace C at position 0 with r,flow_matching,0.3,2.0,58,166
24,replace,0.0,C,r,rCOC,CCOC,4,replace r at position 0 with C,flow_matching,0.3,2.0,58,166
25,remove,0.0,C,,CCOC,COC,3,remove C from position 0,flow_matching,0.3,2.0,58,166
26,add,3.0,c,,COC,COCc,4,add c at position 3,flow_matching,0.3,2.0,58,166
27,remove,0.0,C,,COCc,OCc,3,remove C from position 0,flow_matching,0.3,2.0,58,166
28,remove,2.0,c,,OCc,OC,2,remove c from position 2,flow_matching,0.3,2.0,58,166
29,replace,0.0,C,O,OC,CC,2,replace O at position 0 with C,flow_matching,0.3,2.0,58,166
30,remove,0.0,C,,CC,C,1,remove C from position 0,flow_matching,0.3,2.0,58,166
31,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,58,166
32,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,58,166
33,add,2.0,I,,CC(,CCI(,4,add I at position 2,flow_matching,0.3,2.0,58,166
34,replace,2.0,O,I,CCI(,CCO(,4,replace I at position 2 with O,flow_matching,0.3,2.0,58,166
35,add,4.0,[,,CCO(,CCO([,5,add [ at position 4,flow_matching,0.3,2.0,58,166
36,add,5.0,r,,CCO([,CCO([r,6,add r at position 5,flow_matching,0.3,2.0,58,166
37,replace,3.0,C,(,CCO([r,CCOC[r,6,replace ( at position 3 with C,flow_matching,0.3,2.0,58,166
38,add,6.0,n,,CCOC[r,CCOC[rn,7,add n at position 6,flow_matching,0.3,2.0,58,166
39,add,6.0,I,,CCOC[rn,CCOC[rIn,8,add I at position 6,flow_matching,0.3,2.0,58,166
40,replace,4.0,(,[,CCOC[rIn,CCOC(rIn,8,replace [ at position 4 with (,flow_matching,0.3,2.0,58,166
41,replace,7.0,I,n,CCOC(rIn,CCOC(rII,8,replace n at position 7 with I,flow_matching,0.3,2.0,58,166
42,replace,5.0,=,r,CCOC(rII,CCOC(=II,8,replace r at position 5 with =,flow_matching,0.3,2.0,58,166
43,replace,4.0,5,(,CCOC(=II,CCOC5=II,8,replace ( at position 4 with 5,flow_matching,0.3,2.0,58,166
44,replace,4.0,(,5,CCOC5=II,CCOC(=II,8,replace 5 at position 4 with (,flow_matching,0.3,2.0,58,166
45,replace,5.0,1,=,CCOC(=II,CCOC(1II,8,replace = at position 5 with 1,flow_matching,0.3,2.0,58,166
46,replace,5.0,=,1,CCOC(1II,CCOC(=II,8,replace 1 at position 5 with =,flow_matching,0.3,2.0,58,166
47,remove,1.0,C,,CCOC(=II,COC(=II,7,remove C from position 1,flow_matching,0.3,2.0,58,166
48,replace,1.0,C,O,COC(=II,CCC(=II,7,replace O at position 1 with C,flow_matching,0.3,2.0,58,166
49,replace,3.0,F,(,CCC(=II,CCCF=II,7,replace ( at position 3 with F,flow_matching,0.3,2.0,58,166
50,replace,3.0,H,F,CCCF=II,CCCH=II,7,replace F at position 3 with H,flow_matching,0.3,2.0,58,166
51,replace,4.0,+,=,CCCH=II,CCCH+II,7,replace = at position 4 with +,flow_matching,0.3,2.0,58,166
52,add,7.0,@,,CCCH+II,CCCH+II@,8,add @ at position 7,flow_matching,0.3,2.0,58,166
53,replace,7.0,B,@,CCCH+II@,CCCH+IIB,8,replace @ at position 7 with B,flow_matching,0.3,2.0,58,166
54,add,6.0,\,,CCCH+IIB,CCCH+I\IB,9,add \ at position 6,flow_matching,0.3,2.0,58,166
55,replace,0.0,+,C,CCCH+I\IB,+CCH+I\IB,9,replace C at position 0 with +,flow_matching,0.3,2.0,58,166
56,replace,4.0,C,+,+CCH+I\IB,+CCHCI\IB,9,replace + at position 4 with C,flow_matching,0.3,2.0,58,166
57,replace,2.0,+,C,+CCHCI\IB,+C+HCI\IB,9,replace C at position 2 with +,flow_matching,0.3,2.0,58,166
58,replace,1.0,1,C,+C+HCI\IB,+1+HCI\IB,9,replace C at position 1 with 1,flow_matching,0.3,2.0,58,166
59,replace,0.0,C,+,+1+HCI\IB,C1+HCI\IB,9,replace + at position 0 with C,flow_matching,0.3,2.0,58,166
60,add,8.0,I,,C1+HCI\IB,C1+HCI\IIB,10,add I at position 8,flow_matching,0.3,2.0,58,166
61,replace,1.0,C,1,C1+HCI\IIB,CC+HCI\IIB,10,replace 1 at position 1 with C,flow_matching,0.3,2.0,58,166
62,add,6.0,4,,CC+HCI\IIB,CC+HCI4\IIB,11,add 4 at position 6,flow_matching,0.3,2.0,58,166
63,remove,2.0,+,,CC+HCI4\IIB,CCHCI4\IIB,10,remove + from position 2,flow_matching,0.3,2.0,58,166
64,remove,8.0,I,,CCHCI4\IIB,CCHCI4\IB,9,remove I from position 8,flow_matching,0.3,2.0,58,166
65,add,4.0,5,,CCHCI4\IB,CCHC5I4\IB,10,add 5 at position 4,flow_matching,0.3,2.0,58,166
66,replace,2.0,O,H,CCHC5I4\IB,CCOC5I4\IB,10,replace H at position 2 with O,flow_matching,0.3,2.0,58,166
67,add,5.0,3,,CCOC5I4\IB,CCOC53I4\IB,11,add 3 at position 5,flow_matching,0.3,2.0,58,166
68,replace,4.0,(,5,CCOC53I4\IB,CCOC(3I4\IB,11,replace 5 at position 4 with (,flow_matching,0.3,2.0,58,166
69,remove,0.0,C,,CCOC(3I4\IB,COC(3I4\IB,10,remove C from position 0,flow_matching,0.3,2.0,58,166
70,remove,3.0,(,,COC(3I4\IB,COC3I4\IB,9,remove ( from position 3,flow_matching,0.3,2.0,58,166
71,add,6.0,B,,COC3I4\IB,COC3I4B\IB,10,add B at position 6,flow_matching,0.3,2.0,58,166
72,add,5.0,+,,COC3I4B\IB,COC3I+4B\IB,11,add + at position 5,flow_matching,0.3,2.0,58,166
73,replace,1.0,C,O,COC3I+4B\IB,CCC3I+4B\IB,11,replace O at position 1 with C,flow_matching,0.3,2.0,58,166
74,remove,3.0,3,,CCC3I+4B\IB,CCCI+4B\IB,10,remove 3 from position 3,flow_matching,0.3,2.0,58,166
75,remove,3.0,I,,CCCI+4B\IB,CCC+4B\IB,9,remove I from position 3,flow_matching,0.3,2.0,58,166
76,add,5.0,@,,CCC+4B\IB,CCC+4@B\IB,10,add @ at position 5,flow_matching,0.3,2.0,58,166
77,add,10.0,),,CCC+4@B\IB,CCC+4@B\IB),11,add ) at position 10,flow_matching,0.3,2.0,58,166
78,replace,2.0,O,C,CCC+4@B\IB),CCO+4@B\IB),11,replace C at position 2 with O,flow_matching,0.3,2.0,58,166
79,add,2.0,(,,CCO+4@B\IB),CC(O+4@B\IB),12,add ( at position 2,flow_matching,0.3,2.0,58,166
80,add,2.0,S,,CC(O+4@B\IB),CCS(O+4@B\IB),13,add S at position 2,flow_matching,0.3,2.0,58,166
81,add,6.0,/,,CCS(O+4@B\IB),CCS(O+/4@B\IB),14,add / at position 6,flow_matching,0.3,2.0,58,166
82,remove,9.0,B,,CCS(O+/4@B\IB),CCS(O+/4@\IB),13,remove B from position 9,flow_matching,0.3,2.0,58,166
83,replace,7.0,F,4,CCS(O+/4@\IB),CCS(O+/F@\IB),13,replace 4 at position 7 with F,flow_matching,0.3,2.0,58,166
84,replace,10.0,N,I,CCS(O+/F@\IB),CCS(O+/F@\NB),13,replace I at position 10 with N,flow_matching,0.3,2.0,58,166
85,remove,6.0,/,,CCS(O+/F@\NB),CCS(O+F@\NB),12,remove / from position 6,flow_matching,0.3,2.0,58,166
86,replace,7.0,n,@,CCS(O+F@\NB),CCS(O+Fn\NB),12,replace @ at position 7 with n,flow_matching,0.3,2.0,58,166
87,replace,10.0,=,B,CCS(O+Fn\NB),CCS(O+Fn\N=),12,replace B at position 10 with =,flow_matching,0.3,2.0,58,166
88,replace,2.0,O,S,CCS(O+Fn\N=),CCO(O+Fn\N=),12,replace S at position 2 with O,flow_matching,0.3,2.0,58,166
89,replace,0.0,=,C,CCO(O+Fn\N=),=CO(O+Fn\N=),12,replace C at position 0 with =,flow_matching,0.3,2.0,58,166
90,replace,7.0,o,n,=CO(O+Fn\N=),=CO(O+Fo\N=),12,replace n at position 7 with o,flow_matching,0.3,2.0,58,166
91,remove,11.0,),,=CO(O+Fo\N=),=CO(O+Fo\N=,11,remove ) from position 11,flow_matching,0.3,2.0,58,166
92,add,9.0,O,,=CO(O+Fo\N=,=CO(O+Fo\ON=,12,add O at position 9,flow_matching,0.3,2.0,58,166
93,replace,0.0,C,=,=CO(O+Fo\ON=,CCO(O+Fo\ON=,12,replace = at position 0 with C,flow_matching,0.3,2.0,58,166
94,replace,8.0,r,\,CCO(O+Fo\ON=,CCO(O+ForON=,12,replace \ at position 8 with r,flow_matching,0.3,2.0,58,166
95,replace,3.0,C,(,CCO(O+ForON=,CCOCO+ForON=,12,replace ( at position 3 with C,flow_matching,0.3,2.0,58,166
96,replace,6.0,l,F,CCOCO+ForON=,CCOCO+lorON=,12,replace F at position 6 with l,flow_matching,0.3,2.0,58,166
97,add,0.0,-,,CCOCO+lorON=,-CCOCO+lorON=,13,add - at position 0,flow_matching,0.3,2.0,58,166
98,add,11.0,N,,-CCOCO+lorON=,-CCOCO+lorONN=,14,add N at position 11,flow_matching,0.3,2.0,58,166
99,remove,1.0,C,,-CCOCO+lorONN=,-COCO+lorONN=,13,remove C from position 1,flow_matching,0.3,2.0,58,166
100,replace,0.0,C,-,-COCO+lorONN=,CCOCO+lorONN=,13,replace - at position 0 with C,flow_matching,0.3,2.0,58,166
101,remove,6.0,l,,CCOCO+lorONN=,CCOCO+orONN=,12,remove l from position 6,flow_matching,0.3,2.0,58,166
102,replace,4.0,(,O,CCOCO+orONN=,CCOC(+orONN=,12,replace O at position 4 with (,flow_matching,0.3,2.0,58,166
103,replace,5.0,=,+,CCOC(+orONN=,CCOC(=orONN=,12,replace + at position 5 with =,flow_matching,0.3,2.0,58,166
104,replace,6.0,n,o,CCOC(=orONN=,CCOC(=nrONN=,12,replace o at position 6 with n,flow_matching,0.3,2.0,58,166
105,add,9.0,7,,CCOC(=nrONN=,CCOC(=nrO7NN=,13,add 7 at position 9,flow_matching,0.3,2.0,58,166
106,replace,1.0,4,C,CCOC(=nrO7NN=,C4OC(=nrO7NN=,13,replace C at position 1 with 4,flow_matching,0.3,2.0,58,166
107,replace,1.0,C,4,C4OC(=nrO7NN=,CCOC(=nrO7NN=,13,replace 4 at position 1 with C,flow_matching,0.3,2.0,58,166
108,add,1.0,O,,CCOC(=nrO7NN=,COCOC(=nrO7NN=,14,add O at position 1,flow_matching,0.3,2.0,58,166
109,add,10.0,7,,COCOC(=nrO7NN=,COCOC(=nrO77NN=,15,add 7 at position 10,flow_matching,0.3,2.0,58,166
110,remove,1.0,O,,COCOC(=nrO77NN=,CCOC(=nrO77NN=,14,remove O from position 1,flow_matching,0.3,2.0,58,166
111,remove,1.0,C,,CCOC(=nrO77NN=,COC(=nrO77NN=,13,remove C from position 1,flow_matching,0.3,2.0,58,166
112,replace,1.0,C,O,COC(=nrO77NN=,CCC(=nrO77NN=,13,replace O at position 1 with C,flow_matching,0.3,2.0,58,166
113,add,2.0,H,,CCC(=nrO77NN=,CCHC(=nrO77NN=,14,add H at position 2,flow_matching,0.3,2.0,58,166
114,replace,2.0,O,H,CCHC(=nrO77NN=,CCOC(=nrO77NN=,14,replace H at position 2 with O,flow_matching,0.3,2.0,58,166
115,replace,6.0,O,n,CCOC(=nrO77NN=,CCOC(=OrO77NN=,14,replace n at position 6 with O,flow_matching,0.3,2.0,58,166
116,replace,7.0,),r,CCOC(=OrO77NN=,CCOC(=O)O77NN=,14,replace r at position 7 with ),flow_matching,0.3,2.0,58,166
117,replace,8.0,C,O,CCOC(=O)O77NN=,CCOC(=O)C77NN=,14,replace O at position 8 with C,flow_matching,0.3,2.0,58,166
118,replace,9.0,O,7,CCOC(=O)C77NN=,CCOC(=O)CO7NN=,14,replace 7 at position 9 with O,flow_matching,0.3,2.0,58,166
119,replace,10.0,c,7,CCOC(=O)CO7NN=,CCOC(=O)COcNN=,14,replace 7 at position 10 with c,flow_matching,0.3,2.0,58,166
120,replace,11.0,1,N,CCOC(=O)COcNN=,CCOC(=O)COc1N=,14,replace N at position 11 with 1,flow_matching,0.3,2.0,58,166
121,replace,12.0,c,N,CCOC(=O)COc1N=,CCOC(=O)COc1c=,14,replace N at position 12 with c,flow_matching,0.3,2.0,58,166
122,replace,13.0,c,=,CCOC(=O)COc1c=,CCOC(=O)COc1cc,14,replace = at position 13 with c,flow_matching,0.3,2.0,58,166
123,add,14.0,c,,CCOC(=O)COc1cc,CCOC(=O)COc1ccc,15,add c at position 14,flow_matching,0.3,2.0,58,166
124,add,15.0,c,,CCOC(=O)COc1ccc,CCOC(=O)COc1cccc,16,add c at position 15,flow_matching,0.3,2.0,58,166
125,add,16.0,c,,CCOC(=O)COc1cccc,CCOC(=O)COc1ccccc,17,add c at position 16,flow_matching,0.3,2.0,58,166
126,add,17.0,1,,CCOC(=O)COc1ccccc,CCOC(=O)COc1ccccc1,18,add 1 at position 17,flow_matching,0.3,2.0,58,166
127,add,18.0,/,,CCOC(=O)COc1ccccc1,CCOC(=O)COc1ccccc1/,19,add / at position 18,flow_matching,0.3,2.0,58,166
128,add,19.0,C,,CCOC(=O)COc1ccccc1/,CCOC(=O)COc1ccccc1/C,20,add C at position 19,flow_matching,0.3,2.0,58,166
129,add,20.0,=,,CCOC(=O)COc1ccccc1/C,CCOC(=O)COc1ccccc1/C=,21,add = at position 20,flow_matching,0.3,2.0,58,166
130,add,21.0,C,,CCOC(=O)COc1ccccc1/C=,CCOC(=O)COc1ccccc1/C=C,22,add C at position 21,flow_matching,0.3,2.0,58,166
131,add,22.0,1,,CCOC(=O)COc1ccccc1/C=C,CCOC(=O)COc1ccccc1/C=C1,23,add 1 at position 22,flow_matching,0.3,2.0,58,166
132,add,23.0,/,,CCOC(=O)COc1ccccc1/C=C1,CCOC(=O)COc1ccccc1/C=C1/,24,add / at position 23,flow_matching,0.3,2.0,58,166
133,add,24.0,C,,CCOC(=O)COc1ccccc1/C=C1/,CCOC(=O)COc1ccccc1/C=C1/C,25,add C at position 24,flow_matching,0.3,2.0,58,166
134,add,25.0,(,,CCOC(=O)COc1ccccc1/C=C1/C,CCOC(=O)COc1ccccc1/C=C1/C(,26,add ( at position 25,flow_matching,0.3,2.0,58,166
135,add,26.0,=,,CCOC(=O)COc1ccccc1/C=C1/C(,CCOC(=O)COc1ccccc1/C=C1/C(=,27,add = at position 26,flow_matching,0.3,2.0,58,166
136,add,27.0,O,,CCOC(=O)COc1ccccc1/C=C1/C(=,CCOC(=O)COc1ccccc1/C=C1/C(=O,28,add O at position 27,flow_matching,0.3,2.0,58,166
137,add,28.0,),,CCOC(=O)COc1ccccc1/C=C1/C(=O,CCOC(=O)COc1ccccc1/C=C1/C(=O),29,add ) at position 28,flow_matching,0.3,2.0,58,166
138,add,29.0,N,,CCOC(=O)COc1ccccc1/C=C1/C(=O),CCOC(=O)COc1ccccc1/C=C1/C(=O)N,30,add N at position 29,flow_matching,0.3,2.0,58,166
139,add,30.0,C,,CCOC(=O)COc1ccccc1/C=C1/C(=O)N,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC,31,add C at position 30,flow_matching,0.3,2.0,58,166
140,add,31.0,(,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(,32,add ( at position 31,flow_matching,0.3,2.0,58,166
141,add,32.0,=,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=,33,add = at position 32,flow_matching,0.3,2.0,58,166
142,add,33.0,O,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O,34,add O at position 33,flow_matching,0.3,2.0,58,166
143,add,34.0,),,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O),35,add ) at position 34,flow_matching,0.3,2.0,58,166
144,add,35.0,N,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O),CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N,36,add N at position 35,flow_matching,0.3,2.0,58,166
145,add,36.0,(,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(,37,add ( at position 36,flow_matching,0.3,2.0,58,166
146,add,37.0,c,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c,38,add c at position 37,flow_matching,0.3,2.0,58,166
147,add,38.0,2,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2,39,add 2 at position 38,flow_matching,0.3,2.0,58,166
148,add,39.0,c,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2c,40,add c at position 39,flow_matching,0.3,2.0,58,166
149,add,40.0,c,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2c,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2cc,41,add c at position 40,flow_matching,0.3,2.0,58,166
150,add,41.0,c,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2cc,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc,42,add c at position 41,flow_matching,0.3,2.0,58,166
151,add,42.0,3,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3,43,add 3 at position 42,flow_matching,0.3,2.0,58,166
152,add,43.0,c,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c,44,add c at position 43,flow_matching,0.3,2.0,58,166
153,add,44.0,(,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(,45,add ( at position 44,flow_matching,0.3,2.0,58,166
154,add,45.0,c,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c,46,add c at position 45,flow_matching,0.3,2.0,58,166
155,add,46.0,2,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2,47,add 2 at position 46,flow_matching,0.3,2.0,58,166
156,add,47.0,),,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2),48,add ) at position 47,flow_matching,0.3,2.0,58,166
157,add,48.0,O,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2),CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)O,49,add O at position 48,flow_matching,0.3,2.0,58,166
158,add,49.0,C,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)O,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OC,50,add C at position 49,flow_matching,0.3,2.0,58,166
159,add,50.0,O,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OC,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO,51,add O at position 50,flow_matching,0.3,2.0,58,166
160,add,51.0,3,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3,52,add 3 at position 51,flow_matching,0.3,2.0,58,166
161,add,52.0,),,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3),53,add ) at position 52,flow_matching,0.3,2.0,58,166
162,add,53.0,C,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3),CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3)C,54,add C at position 53,flow_matching,0.3,2.0,58,166
163,add,54.0,1,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3)C,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3)C1,55,add 1 at position 54,flow_matching,0.3,2.0,58,166
164,add,55.0,=,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3)C1,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3)C1=,56,add = at position 55,flow_matching,0.3,2.0,58,166
165,add,56.0,O,,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3)C1=,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3)C1=O,57,add O at position 56,flow_matching,0.3,2.0,58,166
166,add,57.0,"
",,CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3)C1=O,"CCOC(=O)COc1ccccc1/C=C1/C(=O)NC(=O)N(c2ccc3c(c2)OCO3)C1=O
",58,"add 
 at position 57",flow_matching,0.3,2.0,58,166
