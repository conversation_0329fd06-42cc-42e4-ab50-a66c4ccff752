step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,31,76
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,31,76
2,replace,0.0,+,O,O,+,1,replace <PERSON> at position 0 with +,flow_matching,0.3,2.0,31,76
3,add,0.0,B,,+,B+,2,add B at position 0,flow_matching,0.3,2.0,31,76
4,replace,0.0,n,B,B+,n+,2,replace B at position 0 with n,flow_matching,0.3,2.0,31,76
5,replace,0.0,O,n,n+,O+,2,replace n at position 0 with O,flow_matching,0.3,2.0,31,76
6,remove,1.0,+,,O+,O,1,remove + from position 1,flow_matching,0.3,2.0,31,76
7,replace,0.0,3,O,O,3,1,replace <PERSON> at position 0 with 3,flow_matching,0.3,2.0,31,76
8,replace,0.0,I,3,3,I,1,replace 3 at position 0 with I,flow_matching,0.3,2.0,31,76
9,add,1.0,3,,I,I3,2,add 3 at position 1,flow_matching,0.3,2.0,31,76
10,replace,0.0,O,I,I3,O3,2,replace I at position 0 with O,flow_matching,0.3,2.0,31,76
11,add,2.0,o,,O3,O3o,3,add o at position 2,flow_matching,0.3,2.0,31,76
12,add,3.0,I,,O3o,O3oI,4,add I at position 3,flow_matching,0.3,2.0,31,76
13,replace,1.0,=,3,O3oI,O=oI,4,replace 3 at position 1 with =,flow_matching,0.3,2.0,31,76
14,replace,2.0,C,o,O=oI,O=CI,4,replace o at position 2 with C,flow_matching,0.3,2.0,31,76
15,replace,3.0,(,I,O=CI,O=C(,4,replace I at position 3 with (,flow_matching,0.3,2.0,31,76
16,add,4.0,[,,O=C(,O=C([,5,add [ at position 4,flow_matching,0.3,2.0,31,76
17,replace,4.0,c,[,O=C([,O=C(c,5,replace [ at position 4 with c,flow_matching,0.3,2.0,31,76
18,add,4.0,-,,O=C(c,O=C(-c,6,add - at position 4,flow_matching,0.3,2.0,31,76
19,remove,1.0,=,,O=C(-c,OC(-c,5,remove = from position 1,flow_matching,0.3,2.0,31,76
20,replace,1.0,=,C,OC(-c,O=(-c,5,replace C at position 1 with =,flow_matching,0.3,2.0,31,76
21,replace,3.0,@,-,O=(-c,O=(@c,5,replace - at position 3 with @,flow_matching,0.3,2.0,31,76
22,remove,3.0,@,,O=(@c,O=(c,4,remove @ from position 3,flow_matching,0.3,2.0,31,76
23,replace,3.0,O,c,O=(c,O=(O,4,replace c at position 3 with O,flow_matching,0.3,2.0,31,76
24,replace,2.0,],(,O=(O,O=]O,4,replace ( at position 2 with ],flow_matching,0.3,2.0,31,76
25,add,1.0,2,,O=]O,O2=]O,5,add 2 at position 1,flow_matching,0.3,2.0,31,76
26,replace,1.0,=,2,O2=]O,O==]O,5,replace 2 at position 1 with =,flow_matching,0.3,2.0,31,76
27,replace,2.0,C,=,O==]O,O=C]O,5,replace = at position 2 with C,flow_matching,0.3,2.0,31,76
28,add,3.0,n,,O=C]O,O=Cn]O,6,add n at position 3,flow_matching,0.3,2.0,31,76
29,replace,0.0,5,O,O=Cn]O,5=Cn]O,6,replace O at position 0 with 5,flow_matching,0.3,2.0,31,76
30,remove,3.0,n,,5=Cn]O,5=C]O,5,remove n from position 3,flow_matching,0.3,2.0,31,76
31,remove,2.0,C,,5=C]O,5=]O,4,remove C from position 2,flow_matching,0.3,2.0,31,76
32,remove,3.0,O,,5=]O,5=],3,remove O from position 3,flow_matching,0.3,2.0,31,76
33,add,0.0,c,,5=],c5=],4,add c at position 0,flow_matching,0.3,2.0,31,76
34,add,2.0,(,,c5=],c5(=],5,add ( at position 2,flow_matching,0.3,2.0,31,76
35,remove,0.0,c,,c5(=],5(=],4,remove c from position 0,flow_matching,0.3,2.0,31,76
36,remove,1.0,(,,5(=],5=],3,remove ( from position 1,flow_matching,0.3,2.0,31,76
37,replace,0.0,O,5,5=],O=],3,replace 5 at position 0 with O,flow_matching,0.3,2.0,31,76
38,replace,2.0,C,],O=],O=C,3,replace ] at position 2 with C,flow_matching,0.3,2.0,31,76
39,add,3.0,(,,O=C,O=C(,4,add ( at position 3,flow_matching,0.3,2.0,31,76
40,add,2.0,7,,O=C(,O=7C(,5,add 7 at position 2,flow_matching,0.3,2.0,31,76
41,replace,2.0,C,7,O=7C(,O=CC(,5,replace 7 at position 2 with C,flow_matching,0.3,2.0,31,76
42,replace,1.0,s,=,O=CC(,OsCC(,5,replace = at position 1 with s,flow_matching,0.3,2.0,31,76
43,replace,1.0,=,s,OsCC(,O=CC(,5,replace s at position 1 with =,flow_matching,0.3,2.0,31,76
44,replace,3.0,(,C,O=CC(,O=C((,5,replace C at position 3 with (,flow_matching,0.3,2.0,31,76
45,replace,2.0,H,C,O=C((,O=H((,5,replace C at position 2 with H,flow_matching,0.3,2.0,31,76
46,replace,2.0,C,H,O=H((,O=C((,5,replace H at position 2 with C,flow_matching,0.3,2.0,31,76
47,replace,4.0,[,(,O=C((,O=C([,5,replace ( at position 4 with [,flow_matching,0.3,2.0,31,76
48,add,2.0,F,,O=C([,O=FC([,6,add F at position 2,flow_matching,0.3,2.0,31,76
49,replace,2.0,C,F,O=FC([,O=CC([,6,replace F at position 2 with C,flow_matching,0.3,2.0,31,76
50,remove,3.0,C,,O=CC([,O=C([,5,remove C from position 3,flow_matching,0.3,2.0,31,76
51,add,5.0,O,,O=C([,O=C([O,6,add O at position 5,flow_matching,0.3,2.0,31,76
52,add,6.0,-,,O=C([O,O=C([O-,7,add - at position 6,flow_matching,0.3,2.0,31,76
53,add,7.0,],,O=C([O-,O=C([O-],8,add ] at position 7,flow_matching,0.3,2.0,31,76
54,add,8.0,),,O=C([O-],O=C([O-]),9,add ) at position 8,flow_matching,0.3,2.0,31,76
55,add,9.0,C,,O=C([O-]),O=C([O-])C,10,add C at position 9,flow_matching,0.3,2.0,31,76
56,add,10.0,C,,O=C([O-])C,O=C([O-])CC,11,add C at position 10,flow_matching,0.3,2.0,31,76
57,add,11.0,1,,O=C([O-])CC,O=C([O-])CC1,12,add 1 at position 11,flow_matching,0.3,2.0,31,76
58,add,12.0,=,,O=C([O-])CC1,O=C([O-])CC1=,13,add = at position 12,flow_matching,0.3,2.0,31,76
59,add,13.0,C,,O=C([O-])CC1=,O=C([O-])CC1=C,14,add C at position 13,flow_matching,0.3,2.0,31,76
60,add,14.0,(,,O=C([O-])CC1=C,O=C([O-])CC1=C(,15,add ( at position 14,flow_matching,0.3,2.0,31,76
61,add,15.0,C,,O=C([O-])CC1=C(,O=C([O-])CC1=C(C,16,add C at position 15,flow_matching,0.3,2.0,31,76
62,add,16.0,(,,O=C([O-])CC1=C(C,O=C([O-])CC1=C(C(,17,add ( at position 16,flow_matching,0.3,2.0,31,76
63,add,17.0,=,,O=C([O-])CC1=C(C(,O=C([O-])CC1=C(C(=,18,add = at position 17,flow_matching,0.3,2.0,31,76
64,add,18.0,O,,O=C([O-])CC1=C(C(=,O=C([O-])CC1=C(C(=O,19,add O at position 18,flow_matching,0.3,2.0,31,76
65,add,19.0,),,O=C([O-])CC1=C(C(=O,O=C([O-])CC1=C(C(=O),20,add ) at position 19,flow_matching,0.3,2.0,31,76
66,add,20.0,[,,O=C([O-])CC1=C(C(=O),O=C([O-])CC1=C(C(=O)[,21,add [ at position 20,flow_matching,0.3,2.0,31,76
67,add,21.0,O,,O=C([O-])CC1=C(C(=O)[,O=C([O-])CC1=C(C(=O)[O,22,add O at position 21,flow_matching,0.3,2.0,31,76
68,add,22.0,-,,O=C([O-])CC1=C(C(=O)[O,O=C([O-])CC1=C(C(=O)[O-,23,add - at position 22,flow_matching,0.3,2.0,31,76
69,add,23.0,],,O=C([O-])CC1=C(C(=O)[O-,O=C([O-])CC1=C(C(=O)[O-],24,add ] at position 23,flow_matching,0.3,2.0,31,76
70,add,24.0,),,O=C([O-])CC1=C(C(=O)[O-],O=C([O-])CC1=C(C(=O)[O-]),25,add ) at position 24,flow_matching,0.3,2.0,31,76
71,add,25.0,C,,O=C([O-])CC1=C(C(=O)[O-]),O=C([O-])CC1=C(C(=O)[O-])C,26,add C at position 25,flow_matching,0.3,2.0,31,76
72,add,26.0,C,,O=C([O-])CC1=C(C(=O)[O-])C,O=C([O-])CC1=C(C(=O)[O-])CC,27,add C at position 26,flow_matching,0.3,2.0,31,76
73,add,27.0,C,,O=C([O-])CC1=C(C(=O)[O-])CC,O=C([O-])CC1=C(C(=O)[O-])CCC,28,add C at position 27,flow_matching,0.3,2.0,31,76
74,add,28.0,C,,O=C([O-])CC1=C(C(=O)[O-])CCC,O=C([O-])CC1=C(C(=O)[O-])CCCC,29,add C at position 28,flow_matching,0.3,2.0,31,76
75,add,29.0,1,,O=C([O-])CC1=C(C(=O)[O-])CCCC,O=C([O-])CC1=C(C(=O)[O-])CCCC1,30,add 1 at position 29,flow_matching,0.3,2.0,31,76
76,add,30.0,"
",,O=C([O-])CC1=C(C(=O)[O-])CCCC1,"O=C([O-])CC1=C(C(=O)[O-])CCCC1
",31,"add 
 at position 30",flow_matching,0.3,2.0,31,76
