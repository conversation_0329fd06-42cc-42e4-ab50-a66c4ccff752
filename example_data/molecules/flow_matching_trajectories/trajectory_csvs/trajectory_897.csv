step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,37,81
1,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,37,81
2,add,1.0,3,,/,/3,2,add 3 at position 1,flow_matching,0.3,2.0,37,81
3,replace,0.0,C,/,/3,C3,2,replace / at position 0 with C,flow_matching,0.3,2.0,37,81
4,add,1.0,#,,C3,C#3,3,add # at position 1,flow_matching,0.3,2.0,37,81
5,add,3.0,(,,C#3,C#3(,4,add ( at position 3,flow_matching,0.3,2.0,37,81
6,replace,1.0,[,#,C#3(,C[3(,4,replace # at position 1 with [,flow_matching,0.3,2.0,37,81
7,replace,1.0,r,[,C[3(,Cr3(,4,replace [ at position 1 with r,flow_matching,0.3,2.0,37,81
8,replace,3.0,r,(,Cr3(,Cr3r,4,replace ( at position 3 with r,flow_matching,0.3,2.0,37,81
9,replace,1.0,@,r,Cr3r,C@3r,4,replace r at position 1 with @,flow_matching,0.3,2.0,37,81
10,add,2.0,B,,C@3r,C@B3r,5,add B at position 2,flow_matching,0.3,2.0,37,81
11,replace,2.0,O,B,C@B3r,C@O3r,5,replace B at position 2 with O,flow_matching,0.3,2.0,37,81
12,remove,0.0,C,,C@O3r,@O3r,4,remove C from position 0,flow_matching,0.3,2.0,37,81
13,replace,0.0,C,@,@O3r,CO3r,4,replace @ at position 0 with C,flow_matching,0.3,2.0,37,81
14,replace,1.0,S,O,CO3r,CS3r,4,replace O at position 1 with S,flow_matching,0.3,2.0,37,81
15,replace,2.0,n,3,CS3r,CSnr,4,replace 3 at position 2 with n,flow_matching,0.3,2.0,37,81
16,remove,2.0,n,,CSnr,CSr,3,remove n from position 2,flow_matching,0.3,2.0,37,81
17,replace,1.0,[,S,CSr,C[r,3,replace S at position 1 with [,flow_matching,0.3,2.0,37,81
18,replace,2.0,C,r,C[r,C[C,3,replace r at position 2 with C,flow_matching,0.3,2.0,37,81
19,replace,2.0,O,C,C[C,C[O,3,replace C at position 2 with O,flow_matching,0.3,2.0,37,81
20,add,3.0,B,,C[O,C[OB,4,add B at position 3,flow_matching,0.3,2.0,37,81
21,remove,0.0,C,,C[OB,[OB,3,remove C from position 0,flow_matching,0.3,2.0,37,81
22,replace,0.0,#,[,[OB,#OB,3,replace [ at position 0 with #,flow_matching,0.3,2.0,37,81
23,add,1.0,@,,#OB,#@OB,4,add @ at position 1,flow_matching,0.3,2.0,37,81
24,replace,0.0,C,#,#@OB,C@OB,4,replace # at position 0 with C,flow_matching,0.3,2.0,37,81
25,replace,1.0,r,@,C@OB,CrOB,4,replace @ at position 1 with r,flow_matching,0.3,2.0,37,81
26,replace,1.0,[,r,CrOB,C[OB,4,replace r at position 1 with [,flow_matching,0.3,2.0,37,81
27,remove,1.0,[,,C[OB,COB,3,remove [ from position 1,flow_matching,0.3,2.0,37,81
28,add,0.0,H,,COB,HCOB,4,add H at position 0,flow_matching,0.3,2.0,37,81
29,remove,0.0,H,,HCOB,COB,3,remove H from position 0,flow_matching,0.3,2.0,37,81
30,remove,0.0,C,,COB,OB,2,remove C from position 0,flow_matching,0.3,2.0,37,81
31,replace,0.0,C,O,OB,CB,2,replace O at position 0 with C,flow_matching,0.3,2.0,37,81
32,replace,1.0,[,B,CB,C[,2,replace B at position 1 with [,flow_matching,0.3,2.0,37,81
33,remove,1.0,[,,C[,C,1,remove [ from position 1,flow_matching,0.3,2.0,37,81
34,add,0.0,1,,C,1C,2,add 1 at position 0,flow_matching,0.3,2.0,37,81
35,remove,1.0,C,,1C,1,1,remove C from position 1,flow_matching,0.3,2.0,37,81
36,add,1.0,4,,1,14,2,add 4 at position 1,flow_matching,0.3,2.0,37,81
37,add,1.0,B,,14,1B4,3,add B at position 1,flow_matching,0.3,2.0,37,81
38,replace,0.0,C,1,1B4,CB4,3,replace 1 at position 0 with C,flow_matching,0.3,2.0,37,81
39,replace,1.0,[,B,CB4,C[4,3,replace B at position 1 with [,flow_matching,0.3,2.0,37,81
40,add,3.0,\,,C[4,C[4\,4,add \ at position 3,flow_matching,0.3,2.0,37,81
41,replace,2.0,C,4,C[4\,C[C\,4,replace 4 at position 2 with C,flow_matching,0.3,2.0,37,81
42,replace,3.0,@,\,C[C\,C[C@,4,replace \ at position 3 with @,flow_matching,0.3,2.0,37,81
43,replace,3.0,B,@,C[C@,C[CB,4,replace @ at position 3 with B,flow_matching,0.3,2.0,37,81
44,replace,3.0,@,B,C[CB,C[C@,4,replace B at position 3 with @,flow_matching,0.3,2.0,37,81
45,remove,1.0,[,,C[C@,CC@,3,remove [ from position 1,flow_matching,0.3,2.0,37,81
46,replace,1.0,[,C,CC@,C[@,3,replace C at position 1 with [,flow_matching,0.3,2.0,37,81
47,replace,2.0,C,@,C[@,C[C,3,replace @ at position 2 with C,flow_matching,0.3,2.0,37,81
48,add,3.0,@,,C[C,C[C@,4,add @ at position 3,flow_matching,0.3,2.0,37,81
49,add,4.0,H,,C[C@,C[C@H,5,add H at position 4,flow_matching,0.3,2.0,37,81
50,add,5.0,],,C[C@H,C[C@H],6,add ] at position 5,flow_matching,0.3,2.0,37,81
51,add,6.0,(,,C[C@H],C[C@H](,7,add ( at position 6,flow_matching,0.3,2.0,37,81
52,add,7.0,[,,C[C@H](,C[C@H]([,8,add [ at position 7,flow_matching,0.3,2.0,37,81
53,add,8.0,N,,C[C@H]([,C[C@H]([N,9,add N at position 8,flow_matching,0.3,2.0,37,81
54,add,9.0,H,,C[C@H]([N,C[C@H]([NH,10,add H at position 9,flow_matching,0.3,2.0,37,81
55,add,10.0,2,,C[C@H]([NH,C[C@H]([NH2,11,add 2 at position 10,flow_matching,0.3,2.0,37,81
56,add,11.0,+,,C[C@H]([NH2,C[C@H]([NH2+,12,add + at position 11,flow_matching,0.3,2.0,37,81
57,add,12.0,],,C[C@H]([NH2+,C[C@H]([NH2+],13,add ] at position 12,flow_matching,0.3,2.0,37,81
58,add,13.0,C,,C[C@H]([NH2+],C[C@H]([NH2+]C,14,add C at position 13,flow_matching,0.3,2.0,37,81
59,add,14.0,C,,C[C@H]([NH2+]C,C[C@H]([NH2+]CC,15,add C at position 14,flow_matching,0.3,2.0,37,81
60,add,15.0,(,,C[C@H]([NH2+]CC,C[C@H]([NH2+]CC(,16,add ( at position 15,flow_matching,0.3,2.0,37,81
61,add,16.0,=,,C[C@H]([NH2+]CC(,C[C@H]([NH2+]CC(=,17,add = at position 16,flow_matching,0.3,2.0,37,81
62,add,17.0,O,,C[C@H]([NH2+]CC(=,C[C@H]([NH2+]CC(=O,18,add O at position 17,flow_matching,0.3,2.0,37,81
63,add,18.0,),,C[C@H]([NH2+]CC(=O,C[C@H]([NH2+]CC(=O),19,add ) at position 18,flow_matching,0.3,2.0,37,81
64,add,19.0,N,,C[C@H]([NH2+]CC(=O),C[C@H]([NH2+]CC(=O)N,20,add N at position 19,flow_matching,0.3,2.0,37,81
65,add,20.0,(,,C[C@H]([NH2+]CC(=O)N,C[C@H]([NH2+]CC(=O)N(,21,add ( at position 20,flow_matching,0.3,2.0,37,81
66,add,21.0,C,,C[C@H]([NH2+]CC(=O)N(,C[C@H]([NH2+]CC(=O)N(C,22,add C at position 21,flow_matching,0.3,2.0,37,81
67,add,22.0,),,C[C@H]([NH2+]CC(=O)N(C,C[C@H]([NH2+]CC(=O)N(C),23,add ) at position 22,flow_matching,0.3,2.0,37,81
68,add,23.0,C,,C[C@H]([NH2+]CC(=O)N(C),C[C@H]([NH2+]CC(=O)N(C)C,24,add C at position 23,flow_matching,0.3,2.0,37,81
69,add,24.0,),,C[C@H]([NH2+]CC(=O)N(C)C,C[C@H]([NH2+]CC(=O)N(C)C),25,add ) at position 24,flow_matching,0.3,2.0,37,81
70,add,25.0,c,,C[C@H]([NH2+]CC(=O)N(C)C),C[C@H]([NH2+]CC(=O)N(C)C)c,26,add c at position 25,flow_matching,0.3,2.0,37,81
71,add,26.0,1,,C[C@H]([NH2+]CC(=O)N(C)C)c,C[C@H]([NH2+]CC(=O)N(C)C)c1,27,add 1 at position 26,flow_matching,0.3,2.0,37,81
72,add,27.0,c,,C[C@H]([NH2+]CC(=O)N(C)C)c1,C[C@H]([NH2+]CC(=O)N(C)C)c1c,28,add c at position 27,flow_matching,0.3,2.0,37,81
73,add,28.0,c,,C[C@H]([NH2+]CC(=O)N(C)C)c1c,C[C@H]([NH2+]CC(=O)N(C)C)c1cc,29,add c at position 28,flow_matching,0.3,2.0,37,81
74,add,29.0,c,,C[C@H]([NH2+]CC(=O)N(C)C)c1cc,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc,30,add c at position 29,flow_matching,0.3,2.0,37,81
75,add,30.0,(,,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(,31,add ( at position 30,flow_matching,0.3,2.0,37,81
76,add,31.0,C,,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(C,32,add C at position 31,flow_matching,0.3,2.0,37,81
77,add,32.0,l,,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(C,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(Cl,33,add l at position 32,flow_matching,0.3,2.0,37,81
78,add,33.0,),,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(Cl,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(Cl),34,add ) at position 33,flow_matching,0.3,2.0,37,81
79,add,34.0,s,,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(Cl),C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(Cl)s,35,add s at position 34,flow_matching,0.3,2.0,37,81
80,add,35.0,1,,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(Cl)s,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(Cl)s1,36,add 1 at position 35,flow_matching,0.3,2.0,37,81
81,add,36.0,"
",,C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(Cl)s1,"C[C@H]([NH2+]CC(=O)N(C)C)c1ccc(Cl)s1
",37,"add 
 at position 36",flow_matching,0.3,2.0,37,81
