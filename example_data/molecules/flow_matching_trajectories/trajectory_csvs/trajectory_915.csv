step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,28,133
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,28,133
2,add,0.0,B,,C,BC,2,add B at position 0,flow_matching,0.3,2.0,28,133
3,replace,1.0,n,C,BC,Bn,2,replace <PERSON> at position 1 with n,flow_matching,0.3,2.0,28,133
4,add,0.0,B,,Bn,BBn,3,add B at position 0,flow_matching,0.3,2.0,28,133
5,replace,0.0,o,B,BBn,oBn,3,replace B at position 0 with o,flow_matching,0.3,2.0,28,133
6,add,0.0,+,,oBn,+oBn,4,add + at position 0,flow_matching,0.3,2.0,28,133
7,replace,3.0,/,n,+oBn,+oB/,4,replace n at position 3 with /,flow_matching,0.3,2.0,28,133
8,add,1.0,1,,+oB/,+1oB/,5,add 1 at position 1,flow_matching,0.3,2.0,28,133
9,add,2.0,+,,+1oB/,+1+oB/,6,add + at position 2,flow_matching,0.3,2.0,28,133
10,remove,2.0,+,,+1+oB/,+1oB/,5,remove + from position 2,flow_matching,0.3,2.0,28,133
11,replace,0.0,C,+,+1oB/,C1oB/,5,replace + at position 0 with C,flow_matching,0.3,2.0,28,133
12,replace,1.0,=,1,C1oB/,C=oB/,5,replace 1 at position 1 with =,flow_matching,0.3,2.0,28,133
13,replace,1.0,#,=,C=oB/,C#oB/,5,replace = at position 1 with #,flow_matching,0.3,2.0,28,133
14,replace,2.0,C,o,C#oB/,C#CB/,5,replace o at position 2 with C,flow_matching,0.3,2.0,28,133
15,add,3.0,],,C#CB/,C#C]B/,6,add ] at position 3,flow_matching,0.3,2.0,28,133
16,add,6.0,l,,C#C]B/,C#C]B/l,7,add l at position 6,flow_matching,0.3,2.0,28,133
17,replace,3.0,C,],C#C]B/l,C#CCB/l,7,replace ] at position 3 with C,flow_matching,0.3,2.0,28,133
18,remove,4.0,B,,C#CCB/l,C#CC/l,6,remove B from position 4,flow_matching,0.3,2.0,28,133
19,remove,1.0,#,,C#CC/l,CCC/l,5,remove # from position 1,flow_matching,0.3,2.0,28,133
20,replace,1.0,#,C,CCC/l,C#C/l,5,replace C at position 1 with #,flow_matching,0.3,2.0,28,133
21,remove,0.0,C,,C#C/l,#C/l,4,remove C from position 0,flow_matching,0.3,2.0,28,133
22,replace,0.0,C,#,#C/l,CC/l,4,replace # at position 0 with C,flow_matching,0.3,2.0,28,133
23,add,4.0,N,,CC/l,CC/lN,5,add N at position 4,flow_matching,0.3,2.0,28,133
24,replace,1.0,#,C,CC/lN,C#/lN,5,replace C at position 1 with #,flow_matching,0.3,2.0,28,133
25,remove,4.0,N,,C#/lN,C#/l,4,remove N from position 4,flow_matching,0.3,2.0,28,133
26,add,0.0,B,,C#/l,BC#/l,5,add B at position 0,flow_matching,0.3,2.0,28,133
27,replace,0.0,C,B,BC#/l,CC#/l,5,replace B at position 0 with C,flow_matching,0.3,2.0,28,133
28,replace,1.0,r,C,CC#/l,Cr#/l,5,replace C at position 1 with r,flow_matching,0.3,2.0,28,133
29,replace,4.0,r,l,Cr#/l,Cr#/r,5,replace l at position 4 with r,flow_matching,0.3,2.0,28,133
30,add,0.0,4,,Cr#/r,4Cr#/r,6,add 4 at position 0,flow_matching,0.3,2.0,28,133
31,replace,0.0,C,4,4Cr#/r,CCr#/r,6,replace 4 at position 0 with C,flow_matching,0.3,2.0,28,133
32,add,0.0,\,,CCr#/r,\CCr#/r,7,add \ at position 0,flow_matching,0.3,2.0,28,133
33,replace,0.0,H,\,\CCr#/r,HCCr#/r,7,replace \ at position 0 with H,flow_matching,0.3,2.0,28,133
34,add,0.0,4,,HCCr#/r,4HCCr#/r,8,add 4 at position 0,flow_matching,0.3,2.0,28,133
35,add,7.0,/,,4HCCr#/r,4HCCr#//r,9,add / at position 7,flow_matching,0.3,2.0,28,133
36,replace,0.0,C,4,4HCCr#//r,CHCCr#//r,9,replace 4 at position 0 with C,flow_matching,0.3,2.0,28,133
37,add,0.0,3,,CHCCr#//r,3CHCCr#//r,10,add 3 at position 0,flow_matching,0.3,2.0,28,133
38,remove,4.0,C,,3CHCCr#//r,3CHCr#//r,9,remove C from position 4,flow_matching,0.3,2.0,28,133
39,replace,0.0,C,3,3CHCr#//r,CCHCr#//r,9,replace 3 at position 0 with C,flow_matching,0.3,2.0,28,133
40,replace,1.0,#,C,CCHCr#//r,C#HCr#//r,9,replace C at position 1 with #,flow_matching,0.3,2.0,28,133
41,replace,2.0,C,H,C#HCr#//r,C#CCr#//r,9,replace H at position 2 with C,flow_matching,0.3,2.0,28,133
42,replace,1.0,s,#,C#CCr#//r,CsCCr#//r,9,replace # at position 1 with s,flow_matching,0.3,2.0,28,133
43,replace,5.0,3,#,CsCCr#//r,CsCCr3//r,9,replace # at position 5 with 3,flow_matching,0.3,2.0,28,133
44,remove,4.0,r,,CsCCr3//r,CsCC3//r,8,remove r from position 4,flow_matching,0.3,2.0,28,133
45,remove,4.0,3,,CsCC3//r,CsCC//r,7,remove 3 from position 4,flow_matching,0.3,2.0,28,133
46,replace,1.0,#,s,CsCC//r,C#CC//r,7,replace s at position 1 with #,flow_matching,0.3,2.0,28,133
47,remove,6.0,r,,C#CC//r,C#CC//,6,remove r from position 6,flow_matching,0.3,2.0,28,133
48,remove,0.0,C,,C#CC//,#CC//,5,remove C from position 0,flow_matching,0.3,2.0,28,133
49,remove,2.0,C,,#CC//,#C//,4,remove C from position 2,flow_matching,0.3,2.0,28,133
50,replace,0.0,C,#,#C//,CC//,4,replace # at position 0 with C,flow_matching,0.3,2.0,28,133
51,replace,0.0,l,C,CC//,lC//,4,replace C at position 0 with l,flow_matching,0.3,2.0,28,133
52,remove,1.0,C,,lC//,l//,3,remove C from position 1,flow_matching,0.3,2.0,28,133
53,replace,0.0,C,l,l//,C//,3,replace l at position 0 with C,flow_matching,0.3,2.0,28,133
54,remove,2.0,/,,C//,C/,2,remove / from position 2,flow_matching,0.3,2.0,28,133
55,replace,1.0,],/,C/,C],2,replace / at position 1 with ],flow_matching,0.3,2.0,28,133
56,replace,1.0,#,],C],C#,2,replace ] at position 1 with #,flow_matching,0.3,2.0,28,133
57,add,2.0,C,,C#,C#C,3,add C at position 2,flow_matching,0.3,2.0,28,133
58,remove,1.0,#,,C#C,CC,2,remove # from position 1,flow_matching,0.3,2.0,28,133
59,replace,1.0,#,C,CC,C#,2,replace C at position 1 with #,flow_matching,0.3,2.0,28,133
60,add,2.0,[,,C#,C#[,3,add [ at position 2,flow_matching,0.3,2.0,28,133
61,add,0.0,\,,C#[,\C#[,4,add \ at position 0,flow_matching,0.3,2.0,28,133
62,remove,2.0,#,,\C#[,\C[,3,remove # from position 2,flow_matching,0.3,2.0,28,133
63,remove,1.0,C,,\C[,\[,2,remove C from position 1,flow_matching,0.3,2.0,28,133
64,replace,0.0,C,\,\[,C[,2,replace \ at position 0 with C,flow_matching,0.3,2.0,28,133
65,remove,1.0,[,,C[,C,1,remove [ from position 1,flow_matching,0.3,2.0,28,133
66,add,1.0,#,,C,C#,2,add # at position 1,flow_matching,0.3,2.0,28,133
67,remove,1.0,#,,C#,C,1,remove # from position 1,flow_matching,0.3,2.0,28,133
68,add,1.0,#,,C,C#,2,add # at position 1,flow_matching,0.3,2.0,28,133
69,add,2.0,C,,C#,C#C,3,add C at position 2,flow_matching,0.3,2.0,28,133
70,remove,0.0,C,,C#C,#C,2,remove C from position 0,flow_matching,0.3,2.0,28,133
71,add,0.0,],,#C,]#C,3,add ] at position 0,flow_matching,0.3,2.0,28,133
72,replace,0.0,C,],]#C,C#C,3,replace ] at position 0 with C,flow_matching,0.3,2.0,28,133
73,remove,1.0,#,,C#C,CC,2,remove # from position 1,flow_matching,0.3,2.0,28,133
74,add,1.0,H,,CC,CHC,3,add H at position 1,flow_matching,0.3,2.0,28,133
75,replace,1.0,#,H,CHC,C#C,3,replace H at position 1 with #,flow_matching,0.3,2.0,28,133
76,remove,0.0,C,,C#C,#C,2,remove C from position 0,flow_matching,0.3,2.0,28,133
77,replace,0.0,C,#,#C,CC,2,replace # at position 0 with C,flow_matching,0.3,2.0,28,133
78,add,2.0,o,,CC,CCo,3,add o at position 2,flow_matching,0.3,2.0,28,133
79,add,3.0,5,,CCo,CCo5,4,add 5 at position 3,flow_matching,0.3,2.0,28,133
80,replace,1.0,#,C,CCo5,C#o5,4,replace C at position 1 with #,flow_matching,0.3,2.0,28,133
81,replace,0.0,F,C,C#o5,F#o5,4,replace C at position 0 with F,flow_matching,0.3,2.0,28,133
82,replace,2.0,O,o,F#o5,F#O5,4,replace o at position 2 with O,flow_matching,0.3,2.0,28,133
83,add,4.0,],,F#O5,F#O5],5,add ] at position 4,flow_matching,0.3,2.0,28,133
84,add,0.0,2,,F#O5],2F#O5],6,add 2 at position 0,flow_matching,0.3,2.0,28,133
85,replace,0.0,C,2,2F#O5],CF#O5],6,replace 2 at position 0 with C,flow_matching,0.3,2.0,28,133
86,replace,4.0,r,5,CF#O5],CF#Or],6,replace 5 at position 4 with r,flow_matching,0.3,2.0,28,133
87,replace,1.0,N,F,CF#Or],CN#Or],6,replace F at position 1 with N,flow_matching,0.3,2.0,28,133
88,replace,1.0,#,N,CN#Or],C##Or],6,replace N at position 1 with #,flow_matching,0.3,2.0,28,133
89,replace,3.0,C,O,C##Or],C##Cr],6,replace O at position 3 with C,flow_matching,0.3,2.0,28,133
90,remove,5.0,],,C##Cr],C##Cr,5,remove ] from position 5,flow_matching,0.3,2.0,28,133
91,replace,3.0,],C,C##Cr,C##]r,5,replace C at position 3 with ],flow_matching,0.3,2.0,28,133
92,remove,2.0,#,,C##]r,C#]r,4,remove # from position 2,flow_matching,0.3,2.0,28,133
93,remove,0.0,C,,C#]r,#]r,3,remove C from position 0,flow_matching,0.3,2.0,28,133
94,remove,2.0,r,,#]r,#],2,remove r from position 2,flow_matching,0.3,2.0,28,133
95,replace,0.0,C,#,#],C],2,replace # at position 0 with C,flow_matching,0.3,2.0,28,133
96,add,1.0,r,,C],Cr],3,add r at position 1,flow_matching,0.3,2.0,28,133
97,add,2.0,/,,Cr],Cr/],4,add / at position 2,flow_matching,0.3,2.0,28,133
98,replace,1.0,#,r,Cr/],C#/],4,replace r at position 1 with #,flow_matching,0.3,2.0,28,133
99,add,0.0,),,C#/],)C#/],5,add ) at position 0,flow_matching,0.3,2.0,28,133
100,add,3.0,I,,)C#/],)C#I/],6,add I at position 3,flow_matching,0.3,2.0,28,133
101,add,5.0,#,,)C#I/],)C#I/#],7,add # at position 5,flow_matching,0.3,2.0,28,133
102,remove,2.0,#,,)C#I/#],)CI/#],6,remove # from position 2,flow_matching,0.3,2.0,28,133
103,replace,0.0,C,),)CI/#],CCI/#],6,replace ) at position 0 with C,flow_matching,0.3,2.0,28,133
104,remove,3.0,/,,CCI/#],CCI#],5,remove / from position 3,flow_matching,0.3,2.0,28,133
105,add,2.0,\,,CCI#],CC\I#],6,add \ at position 2,flow_matching,0.3,2.0,28,133
106,remove,3.0,I,,CC\I#],CC\#],5,remove I from position 3,flow_matching,0.3,2.0,28,133
107,replace,1.0,#,C,CC\#],C#\#],5,replace C at position 1 with #,flow_matching,0.3,2.0,28,133
108,replace,2.0,C,\,C#\#],C#C#],5,replace \ at position 2 with C,flow_matching,0.3,2.0,28,133
109,replace,3.0,C,#,C#C#],C#CC],5,replace # at position 3 with C,flow_matching,0.3,2.0,28,133
110,replace,4.0,(,],C#CC],C#CC(,5,replace ] at position 4 with (,flow_matching,0.3,2.0,28,133
111,add,5.0,C,,C#CC(,C#CC(C,6,add C at position 5,flow_matching,0.3,2.0,28,133
112,add,6.0,),,C#CC(C,C#CC(C),7,add ) at position 6,flow_matching,0.3,2.0,28,133
113,add,7.0,(,,C#CC(C),C#CC(C)(,8,add ( at position 7,flow_matching,0.3,2.0,28,133
114,add,8.0,C,,C#CC(C)(,C#CC(C)(C,9,add C at position 8,flow_matching,0.3,2.0,28,133
115,add,9.0,),,C#CC(C)(C,C#CC(C)(C),10,add ) at position 9,flow_matching,0.3,2.0,28,133
116,add,10.0,N,,C#CC(C)(C),C#CC(C)(C)N,11,add N at position 10,flow_matching,0.3,2.0,28,133
117,add,11.0,C,,C#CC(C)(C)N,C#CC(C)(C)NC,12,add C at position 11,flow_matching,0.3,2.0,28,133
118,add,12.0,[,,C#CC(C)(C)NC,C#CC(C)(C)NC[,13,add [ at position 12,flow_matching,0.3,2.0,28,133
119,add,13.0,C,,C#CC(C)(C)NC[,C#CC(C)(C)NC[C,14,add C at position 13,flow_matching,0.3,2.0,28,133
120,add,14.0,@,,C#CC(C)(C)NC[C,C#CC(C)(C)NC[C@,15,add @ at position 14,flow_matching,0.3,2.0,28,133
121,add,15.0,H,,C#CC(C)(C)NC[C@,C#CC(C)(C)NC[C@H,16,add H at position 15,flow_matching,0.3,2.0,28,133
122,add,16.0,],,C#CC(C)(C)NC[C@H,C#CC(C)(C)NC[C@H],17,add ] at position 16,flow_matching,0.3,2.0,28,133
123,add,17.0,1,,C#CC(C)(C)NC[C@H],C#CC(C)(C)NC[C@H]1,18,add 1 at position 17,flow_matching,0.3,2.0,28,133
124,add,18.0,C,,C#CC(C)(C)NC[C@H]1,C#CC(C)(C)NC[C@H]1C,19,add C at position 18,flow_matching,0.3,2.0,28,133
125,add,19.0,N,,C#CC(C)(C)NC[C@H]1C,C#CC(C)(C)NC[C@H]1CN,20,add N at position 19,flow_matching,0.3,2.0,28,133
126,add,20.0,(,,C#CC(C)(C)NC[C@H]1CN,C#CC(C)(C)NC[C@H]1CN(,21,add ( at position 20,flow_matching,0.3,2.0,28,133
127,add,21.0,C,,C#CC(C)(C)NC[C@H]1CN(,C#CC(C)(C)NC[C@H]1CN(C,22,add C at position 21,flow_matching,0.3,2.0,28,133
128,add,22.0,),,C#CC(C)(C)NC[C@H]1CN(C,C#CC(C)(C)NC[C@H]1CN(C),23,add ) at position 22,flow_matching,0.3,2.0,28,133
129,add,23.0,C,,C#CC(C)(C)NC[C@H]1CN(C),C#CC(C)(C)NC[C@H]1CN(C)C,24,add C at position 23,flow_matching,0.3,2.0,28,133
130,add,24.0,C,,C#CC(C)(C)NC[C@H]1CN(C)C,C#CC(C)(C)NC[C@H]1CN(C)CC,25,add C at position 24,flow_matching,0.3,2.0,28,133
131,add,25.0,O,,C#CC(C)(C)NC[C@H]1CN(C)CC,C#CC(C)(C)NC[C@H]1CN(C)CCO,26,add O at position 25,flow_matching,0.3,2.0,28,133
132,add,26.0,1,,C#CC(C)(C)NC[C@H]1CN(C)CCO,C#CC(C)(C)NC[C@H]1CN(C)CCO1,27,add 1 at position 26,flow_matching,0.3,2.0,28,133
133,add,27.0,"
",,C#CC(C)(C)NC[C@H]1CN(C)CCO1,"C#CC(C)(C)NC[C@H]1CN(C)CCO1
",28,"add 
 at position 27",flow_matching,0.3,2.0,28,133
