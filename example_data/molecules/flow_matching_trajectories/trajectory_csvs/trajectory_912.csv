step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,25,110
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,25,110
2,replace,0.0,+,C,C,+,1,replace <PERSON> at position 0 with +,flow_matching,0.3,2.0,25,110
3,replace,0.0,C,+,+,C,1,replace + at position 0 with C,flow_matching,0.3,2.0,25,110
4,replace,0.0,#,C,C,#,1,replace <PERSON> at position 0 with #,flow_matching,0.3,2.0,25,110
5,remove,0.0,#,,#,,0,remove # from position 0,flow_matching,0.3,2.0,25,110
6,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,25,110
7,add,0.0,I,,O,IO,2,add I at position 0,flow_matching,0.3,2.0,25,110
8,add,0.0,s,,IO,sIO,3,add s at position 0,flow_matching,0.3,2.0,25,110
9,remove,0.0,s,,sIO,IO,2,remove s from position 0,flow_matching,0.3,2.0,25,110
10,replace,0.0,],I,IO,]O,2,replace I at position 0 with ],flow_matching,0.3,2.0,25,110
11,replace,0.0,C,],]O,CO,2,replace ] at position 0 with C,flow_matching,0.3,2.0,25,110
12,replace,1.0,l,O,CO,Cl,2,replace O at position 1 with l,flow_matching,0.3,2.0,25,110
13,add,0.0,F,,Cl,FCl,3,add F at position 0,flow_matching,0.3,2.0,25,110
14,replace,0.0,C,F,FCl,CCl,3,replace F at position 0 with C,flow_matching,0.3,2.0,25,110
15,remove,2.0,l,,CCl,CC,2,remove l from position 2,flow_matching,0.3,2.0,25,110
16,remove,0.0,C,,CC,C,1,remove C from position 0,flow_matching,0.3,2.0,25,110
17,replace,0.0,=,C,C,=,1,replace C at position 0 with =,flow_matching,0.3,2.0,25,110
18,replace,0.0,(,=,=,(,1,replace = at position 0 with (,flow_matching,0.3,2.0,25,110
19,remove,0.0,(,,(,,0,remove ( from position 0,flow_matching,0.3,2.0,25,110
20,add,0.0,=,,,=,1,add = at position 0,flow_matching,0.3,2.0,25,110
21,replace,0.0,[,=,=,[,1,replace = at position 0 with [,flow_matching,0.3,2.0,25,110
22,remove,0.0,[,,[,,0,remove [ from position 0,flow_matching,0.3,2.0,25,110
23,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,25,110
24,add,1.0,l,,C,Cl,2,add l at position 1,flow_matching,0.3,2.0,25,110
25,add,1.0,B,,Cl,CBl,3,add B at position 1,flow_matching,0.3,2.0,25,110
26,add,0.0,2,,CBl,2CBl,4,add 2 at position 0,flow_matching,0.3,2.0,25,110
27,remove,3.0,l,,2CBl,2CB,3,remove l from position 3,flow_matching,0.3,2.0,25,110
28,replace,0.0,C,2,2CB,CCB,3,replace 2 at position 0 with C,flow_matching,0.3,2.0,25,110
29,add,1.0,-,,CCB,C-CB,4,add - at position 1,flow_matching,0.3,2.0,25,110
30,remove,2.0,C,,C-CB,C-B,3,remove C from position 2,flow_matching,0.3,2.0,25,110
31,replace,1.0,l,-,C-B,ClB,3,replace - at position 1 with l,flow_matching,0.3,2.0,25,110
32,add,2.0,l,,ClB,CllB,4,add l at position 2,flow_matching,0.3,2.0,25,110
33,add,3.0,H,,CllB,CllHB,5,add H at position 3,flow_matching,0.3,2.0,25,110
34,replace,0.0,l,C,CllHB,lllHB,5,replace C at position 0 with l,flow_matching,0.3,2.0,25,110
35,remove,3.0,H,,lllHB,lllB,4,remove H from position 3,flow_matching,0.3,2.0,25,110
36,add,2.0,s,,lllB,llslB,5,add s at position 2,flow_matching,0.3,2.0,25,110
37,replace,0.0,C,l,llslB,ClslB,5,replace l at position 0 with C,flow_matching,0.3,2.0,25,110
38,add,4.0,=,,ClslB,Clsl=B,6,add = at position 4,flow_matching,0.3,2.0,25,110
39,replace,5.0,/,B,Clsl=B,Clsl=/,6,replace B at position 5 with /,flow_matching,0.3,2.0,25,110
40,remove,1.0,l,,Clsl=/,Csl=/,5,remove l from position 1,flow_matching,0.3,2.0,25,110
41,replace,2.0,+,l,Csl=/,Cs+=/,5,replace l at position 2 with +,flow_matching,0.3,2.0,25,110
42,remove,0.0,C,,Cs+=/,s+=/,4,remove C from position 0,flow_matching,0.3,2.0,25,110
43,replace,0.0,C,s,s+=/,C+=/,4,replace s at position 0 with C,flow_matching,0.3,2.0,25,110
44,remove,0.0,C,,C+=/,+=/,3,remove C from position 0,flow_matching,0.3,2.0,25,110
45,replace,0.0,C,+,+=/,C=/,3,replace + at position 0 with C,flow_matching,0.3,2.0,25,110
46,replace,1.0,l,=,C=/,Cl/,3,replace = at position 1 with l,flow_matching,0.3,2.0,25,110
47,add,0.0,-,,Cl/,-Cl/,4,add - at position 0,flow_matching,0.3,2.0,25,110
48,replace,0.0,C,-,-Cl/,CCl/,4,replace - at position 0 with C,flow_matching,0.3,2.0,25,110
49,replace,1.0,l,C,CCl/,Cll/,4,replace C at position 1 with l,flow_matching,0.3,2.0,25,110
50,replace,2.0,c,l,Cll/,Clc/,4,replace l at position 2 with c,flow_matching,0.3,2.0,25,110
51,remove,0.0,C,,Clc/,lc/,3,remove C from position 0,flow_matching,0.3,2.0,25,110
52,add,2.0,r,,lc/,lcr/,4,add r at position 2,flow_matching,0.3,2.0,25,110
53,remove,0.0,l,,lcr/,cr/,3,remove l from position 0,flow_matching,0.3,2.0,25,110
54,replace,2.0,s,/,cr/,crs,3,replace / at position 2 with s,flow_matching,0.3,2.0,25,110
55,replace,2.0,(,s,crs,cr(,3,replace s at position 2 with (,flow_matching,0.3,2.0,25,110
56,remove,0.0,c,,cr(,r(,2,remove c from position 0,flow_matching,0.3,2.0,25,110
57,add,0.0,2,,r(,2r(,3,add 2 at position 0,flow_matching,0.3,2.0,25,110
58,replace,0.0,C,2,2r(,Cr(,3,replace 2 at position 0 with C,flow_matching,0.3,2.0,25,110
59,add,1.0,3,,Cr(,C3r(,4,add 3 at position 1,flow_matching,0.3,2.0,25,110
60,replace,1.0,l,3,C3r(,Clr(,4,replace 3 at position 1 with l,flow_matching,0.3,2.0,25,110
61,replace,2.0,c,r,Clr(,Clc(,4,replace r at position 2 with c,flow_matching,0.3,2.0,25,110
62,add,0.0,o,,Clc(,oClc(,5,add o at position 0,flow_matching,0.3,2.0,25,110
63,add,3.0,=,,oClc(,oCl=c(,6,add = at position 3,flow_matching,0.3,2.0,25,110
64,remove,3.0,=,,oCl=c(,oClc(,5,remove = from position 3,flow_matching,0.3,2.0,25,110
65,replace,4.0,1,(,oClc(,oClc1,5,replace ( at position 4 with 1,flow_matching,0.3,2.0,25,110
66,remove,4.0,1,,oClc1,oClc,4,remove 1 from position 4,flow_matching,0.3,2.0,25,110
67,add,3.0,@,,oClc,oCl@c,5,add @ at position 3,flow_matching,0.3,2.0,25,110
68,replace,3.0,4,@,oCl@c,oCl4c,5,replace @ at position 3 with 4,flow_matching,0.3,2.0,25,110
69,replace,0.0,C,o,oCl4c,CCl4c,5,replace o at position 0 with C,flow_matching,0.3,2.0,25,110
70,remove,2.0,l,,CCl4c,CC4c,4,remove l from position 2,flow_matching,0.3,2.0,25,110
71,replace,1.0,l,C,CC4c,Cl4c,4,replace C at position 1 with l,flow_matching,0.3,2.0,25,110
72,replace,2.0,c,4,Cl4c,Clcc,4,replace 4 at position 2 with c,flow_matching,0.3,2.0,25,110
73,replace,0.0,2,C,Clcc,2lcc,4,replace C at position 0 with 2,flow_matching,0.3,2.0,25,110
74,replace,0.0,C,2,2lcc,Clcc,4,replace 2 at position 0 with C,flow_matching,0.3,2.0,25,110
75,remove,1.0,l,,Clcc,Ccc,3,remove l from position 1,flow_matching,0.3,2.0,25,110
76,replace,2.0,O,c,Ccc,CcO,3,replace c at position 2 with O,flow_matching,0.3,2.0,25,110
77,remove,1.0,c,,CcO,CO,2,remove c from position 1,flow_matching,0.3,2.0,25,110
78,remove,1.0,O,,CO,C,1,remove O from position 1,flow_matching,0.3,2.0,25,110
79,add,1.0,l,,C,Cl,2,add l at position 1,flow_matching,0.3,2.0,25,110
80,replace,1.0,C,l,Cl,CC,2,replace l at position 1 with C,flow_matching,0.3,2.0,25,110
81,replace,1.0,l,C,CC,Cl,2,replace C at position 1 with l,flow_matching,0.3,2.0,25,110
82,add,1.0,I,,Cl,CIl,3,add I at position 1,flow_matching,0.3,2.0,25,110
83,remove,2.0,l,,CIl,CI,2,remove l from position 2,flow_matching,0.3,2.0,25,110
84,replace,1.0,l,I,CI,Cl,2,replace I at position 1 with l,flow_matching,0.3,2.0,25,110
85,add,2.0,(,,Cl,Cl(,3,add ( at position 2,flow_matching,0.3,2.0,25,110
86,add,3.0,@,,Cl(,Cl(@,4,add @ at position 3,flow_matching,0.3,2.0,25,110
87,remove,2.0,(,,Cl(@,Cl@,3,remove ( from position 2,flow_matching,0.3,2.0,25,110
88,replace,2.0,c,@,Cl@,Clc,3,replace @ at position 2 with c,flow_matching,0.3,2.0,25,110
89,add,3.0,1,,Clc,Clc1,4,add 1 at position 3,flow_matching,0.3,2.0,25,110
90,add,4.0,c,,Clc1,Clc1c,5,add c at position 4,flow_matching,0.3,2.0,25,110
91,add,5.0,c,,Clc1c,Clc1cc,6,add c at position 5,flow_matching,0.3,2.0,25,110
92,add,6.0,c,,Clc1cc,Clc1ccc,7,add c at position 6,flow_matching,0.3,2.0,25,110
93,add,7.0,(,,Clc1ccc,Clc1ccc(,8,add ( at position 7,flow_matching,0.3,2.0,25,110
94,add,8.0,C,,Clc1ccc(,Clc1ccc(C,9,add C at position 8,flow_matching,0.3,2.0,25,110
95,add,9.0,N,,Clc1ccc(C,Clc1ccc(CN,10,add N at position 9,flow_matching,0.3,2.0,25,110
96,add,10.0,c,,Clc1ccc(CN,Clc1ccc(CNc,11,add c at position 10,flow_matching,0.3,2.0,25,110
97,add,11.0,2,,Clc1ccc(CNc,Clc1ccc(CNc2,12,add 2 at position 11,flow_matching,0.3,2.0,25,110
98,add,12.0,n,,Clc1ccc(CNc2,Clc1ccc(CNc2n,13,add n at position 12,flow_matching,0.3,2.0,25,110
99,add,13.0,c,,Clc1ccc(CNc2n,Clc1ccc(CNc2nc,14,add c at position 13,flow_matching,0.3,2.0,25,110
100,add,14.0,c,,Clc1ccc(CNc2nc,Clc1ccc(CNc2ncc,15,add c at position 14,flow_matching,0.3,2.0,25,110
101,add,15.0,c,,Clc1ccc(CNc2ncc,Clc1ccc(CNc2nccc,16,add c at position 15,flow_matching,0.3,2.0,25,110
102,add,16.0,c,,Clc1ccc(CNc2nccc,Clc1ccc(CNc2ncccc,17,add c at position 16,flow_matching,0.3,2.0,25,110
103,add,17.0,2,,Clc1ccc(CNc2ncccc,Clc1ccc(CNc2ncccc2,18,add 2 at position 17,flow_matching,0.3,2.0,25,110
104,add,18.0,C,,Clc1ccc(CNc2ncccc2,Clc1ccc(CNc2ncccc2C,19,add C at position 18,flow_matching,0.3,2.0,25,110
105,add,19.0,l,,Clc1ccc(CNc2ncccc2C,Clc1ccc(CNc2ncccc2Cl,20,add l at position 19,flow_matching,0.3,2.0,25,110
106,add,20.0,),,Clc1ccc(CNc2ncccc2Cl,Clc1ccc(CNc2ncccc2Cl),21,add ) at position 20,flow_matching,0.3,2.0,25,110
107,add,21.0,c,,Clc1ccc(CNc2ncccc2Cl),Clc1ccc(CNc2ncccc2Cl)c,22,add c at position 21,flow_matching,0.3,2.0,25,110
108,add,22.0,n,,Clc1ccc(CNc2ncccc2Cl)c,Clc1ccc(CNc2ncccc2Cl)cn,23,add n at position 22,flow_matching,0.3,2.0,25,110
109,add,23.0,1,,Clc1ccc(CNc2ncccc2Cl)cn,Clc1ccc(CNc2ncccc2Cl)cn1,24,add 1 at position 23,flow_matching,0.3,2.0,25,110
110,add,24.0,"
",,Clc1ccc(CNc2ncccc2Cl)cn1,"Clc1ccc(CNc2ncccc2Cl)cn1
",25,"add 
 at position 24",flow_matching,0.3,2.0,25,110
