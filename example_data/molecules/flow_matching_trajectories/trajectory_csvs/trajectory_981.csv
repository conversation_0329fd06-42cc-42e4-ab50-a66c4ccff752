step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,66,217
1,add,0.0,=,,,=,1,add = at position 0,flow_matching,0.3,2.0,66,217
2,replace,0.0,S,=,=,S,1,replace = at position 0 with S,flow_matching,0.3,2.0,66,217
3,remove,0.0,S,,S,,0,remove S from position 0,flow_matching,0.3,2.0,66,217
4,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,66,217
5,add,0.0,1,,C,1C,2,add 1 at position 0,flow_matching,0.3,2.0,66,217
6,replace,0.0,C,1,1C,CC,2,replace 1 at position 0 with <PERSON>,flow_matching,0.3,2.0,66,217
7,remove,1.0,C,,CC,C,1,remove <PERSON> from position 1,flow_matching,0.3,2.0,66,217
8,add,1.0,C,,<PERSON>,<PERSON>,2,add C at position 1,flow_matching,0.3,2.0,66,217
9,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,66,217
10,replace,2.0,5,(,CC(,CC5,3,replace ( at position 2 with 5,flow_matching,0.3,2.0,66,217
11,replace,2.0,(,5,CC5,CC(,3,replace 5 at position 2 with (,flow_matching,0.3,2.0,66,217
12,add,3.0,C,,CC(,CC(C,4,add C at position 3,flow_matching,0.3,2.0,66,217
13,replace,2.0,3,(,CC(C,CC3C,4,replace ( at position 2 with 3,flow_matching,0.3,2.0,66,217
14,replace,2.0,(,3,CC3C,CC(C,4,replace 3 at position 2 with (,flow_matching,0.3,2.0,66,217
15,replace,0.0,@,C,CC(C,@C(C,4,replace C at position 0 with @,flow_matching,0.3,2.0,66,217
16,replace,0.0,C,@,@C(C,CC(C,4,replace @ at position 0 with C,flow_matching,0.3,2.0,66,217
17,replace,1.0,4,C,CC(C,C4(C,4,replace C at position 1 with 4,flow_matching,0.3,2.0,66,217
18,remove,0.0,C,,C4(C,4(C,3,remove C from position 0,flow_matching,0.3,2.0,66,217
19,replace,0.0,C,4,4(C,C(C,3,replace 4 at position 0 with C,flow_matching,0.3,2.0,66,217
20,add,2.0,n,,C(C,C(nC,4,add n at position 2,flow_matching,0.3,2.0,66,217
21,add,3.0,s,,C(nC,C(nsC,5,add s at position 3,flow_matching,0.3,2.0,66,217
22,add,4.0,c,,C(nsC,C(nscC,6,add c at position 4,flow_matching,0.3,2.0,66,217
23,remove,4.0,c,,C(nscC,C(nsC,5,remove c from position 4,flow_matching,0.3,2.0,66,217
24,remove,4.0,C,,C(nsC,C(ns,4,remove C from position 4,flow_matching,0.3,2.0,66,217
25,add,2.0,S,,C(ns,C(Sns,5,add S at position 2,flow_matching,0.3,2.0,66,217
26,replace,1.0,C,(,C(Sns,CCSns,5,replace ( at position 1 with C,flow_matching,0.3,2.0,66,217
27,replace,2.0,1,S,CCSns,CC1ns,5,replace S at position 2 with 1,flow_matching,0.3,2.0,66,217
28,add,3.0,\,,CC1ns,CC1\ns,6,add \ at position 3,flow_matching,0.3,2.0,66,217
29,replace,3.0,4,\,CC1\ns,CC14ns,6,replace \ at position 3 with 4,flow_matching,0.3,2.0,66,217
30,remove,2.0,1,,CC14ns,CC4ns,5,remove 1 from position 2,flow_matching,0.3,2.0,66,217
31,remove,4.0,s,,CC4ns,CC4n,4,remove s from position 4,flow_matching,0.3,2.0,66,217
32,remove,2.0,4,,CC4n,CCn,3,remove 4 from position 2,flow_matching,0.3,2.0,66,217
33,add,0.0,B,,CCn,BCCn,4,add B at position 0,flow_matching,0.3,2.0,66,217
34,replace,0.0,C,B,BCCn,CCCn,4,replace B at position 0 with C,flow_matching,0.3,2.0,66,217
35,replace,2.0,(,C,CCCn,CC(n,4,replace C at position 2 with (,flow_matching,0.3,2.0,66,217
36,replace,3.0,C,n,CC(n,CC(C,4,replace n at position 3 with C,flow_matching,0.3,2.0,66,217
37,add,4.0,),,CC(C,CC(C),5,add ) at position 4,flow_matching,0.3,2.0,66,217
38,add,5.0,C,,CC(C),CC(C)C,6,add C at position 5,flow_matching,0.3,2.0,66,217
39,add,6.0,[,,CC(C)C,CC(C)C[,7,add [ at position 6,flow_matching,0.3,2.0,66,217
40,add,7.0,C,,CC(C)C[,CC(C)C[C,8,add C at position 7,flow_matching,0.3,2.0,66,217
41,replace,2.0,C,(,CC(C)C[C,CCCC)C[C,8,replace ( at position 2 with C,flow_matching,0.3,2.0,66,217
42,add,3.0,=,,CCCC)C[C,CCC=C)C[C,9,add = at position 3,flow_matching,0.3,2.0,66,217
43,add,6.0,N,,CCC=C)C[C,CCC=C)NC[C,10,add N at position 6,flow_matching,0.3,2.0,66,217
44,add,10.0,4,,CCC=C)NC[C,CCC=C)NC[C4,11,add 4 at position 10,flow_matching,0.3,2.0,66,217
45,remove,2.0,C,,CCC=C)NC[C4,CC=C)NC[C4,10,remove C from position 2,flow_matching,0.3,2.0,66,217
46,remove,6.0,C,,CC=C)NC[C4,CC=C)N[C4,9,remove C from position 6,flow_matching,0.3,2.0,66,217
47,replace,2.0,(,=,CC=C)N[C4,CC(C)N[C4,9,replace = at position 2 with (,flow_matching,0.3,2.0,66,217
48,remove,6.0,[,,CC(C)N[C4,CC(C)NC4,8,remove [ from position 6,flow_matching,0.3,2.0,66,217
49,remove,7.0,4,,CC(C)NC4,CC(C)NC,7,remove 4 from position 7,flow_matching,0.3,2.0,66,217
50,replace,5.0,C,N,CC(C)NC,CC(C)CC,7,replace N at position 5 with C,flow_matching,0.3,2.0,66,217
51,replace,2.0,=,(,CC(C)CC,CC=C)CC,7,replace ( at position 2 with =,flow_matching,0.3,2.0,66,217
52,add,6.0,[,,CC=C)CC,CC=C)C[C,8,add [ at position 6,flow_matching,0.3,2.0,66,217
53,replace,2.0,(,=,CC=C)C[C,CC(C)C[C,8,replace = at position 2 with (,flow_matching,0.3,2.0,66,217
54,add,8.0,/,,CC(C)C[C,CC(C)C[C/,9,add / at position 8,flow_matching,0.3,2.0,66,217
55,add,8.0,=,,CC(C)C[C/,CC(C)C[C=/,10,add = at position 8,flow_matching,0.3,2.0,66,217
56,remove,1.0,C,,CC(C)C[C=/,C(C)C[C=/,9,remove C from position 1,flow_matching,0.3,2.0,66,217
57,remove,8.0,/,,C(C)C[C=/,C(C)C[C=,8,remove / from position 8,flow_matching,0.3,2.0,66,217
58,replace,1.0,C,(,C(C)C[C=,CCC)C[C=,8,replace ( at position 1 with C,flow_matching,0.3,2.0,66,217
59,replace,2.0,(,C,CCC)C[C=,CC()C[C=,8,replace C at position 2 with (,flow_matching,0.3,2.0,66,217
60,remove,7.0,=,,CC()C[C=,CC()C[C,7,remove = from position 7,flow_matching,0.3,2.0,66,217
61,remove,2.0,(,,CC()C[C,CC)C[C,6,remove ( from position 2,flow_matching,0.3,2.0,66,217
62,remove,4.0,[,,CC)C[C,CC)CC,5,remove [ from position 4,flow_matching,0.3,2.0,66,217
63,remove,3.0,C,,CC)CC,CC)C,4,remove C from position 3,flow_matching,0.3,2.0,66,217
64,add,3.0,),,CC)C,CC))C,5,add ) at position 3,flow_matching,0.3,2.0,66,217
65,replace,2.0,(,),CC))C,CC()C,5,replace ) at position 2 with (,flow_matching,0.3,2.0,66,217
66,replace,3.0,C,),CC()C,CC(CC,5,replace ) at position 3 with C,flow_matching,0.3,2.0,66,217
67,replace,4.0,),C,CC(CC,CC(C),5,replace C at position 4 with ),flow_matching,0.3,2.0,66,217
68,remove,1.0,C,,CC(C),C(C),4,remove C from position 1,flow_matching,0.3,2.0,66,217
69,remove,0.0,C,,C(C),(C),3,remove C from position 0,flow_matching,0.3,2.0,66,217
70,replace,0.0,C,(,(C),CC),3,replace ( at position 0 with C,flow_matching,0.3,2.0,66,217
71,replace,2.0,(,),CC),CC(,3,replace ) at position 2 with (,flow_matching,0.3,2.0,66,217
72,add,3.0,+,,CC(,CC(+,4,add + at position 3,flow_matching,0.3,2.0,66,217
73,remove,2.0,(,,CC(+,CC+,3,remove ( from position 2,flow_matching,0.3,2.0,66,217
74,replace,2.0,(,+,CC+,CC(,3,replace + at position 2 with (,flow_matching,0.3,2.0,66,217
75,add,3.0,C,,CC(,CC(C,4,add C at position 3,flow_matching,0.3,2.0,66,217
76,add,0.0,s,,CC(C,sCC(C,5,add s at position 0,flow_matching,0.3,2.0,66,217
77,add,0.0,s,,sCC(C,ssCC(C,6,add s at position 0,flow_matching,0.3,2.0,66,217
78,replace,0.0,6,s,ssCC(C,6sCC(C,6,replace s at position 0 with 6,flow_matching,0.3,2.0,66,217
79,replace,3.0,(,C,6sCC(C,6sC((C,6,replace C at position 3 with (,flow_matching,0.3,2.0,66,217
80,replace,5.0,(,C,6sC((C,6sC(((,6,replace C at position 5 with (,flow_matching,0.3,2.0,66,217
81,replace,4.0,1,(,6sC(((,6sC(1(,6,replace ( at position 4 with 1,flow_matching,0.3,2.0,66,217
82,add,6.0,l,,6sC(1(,6sC(1(l,7,add l at position 6,flow_matching,0.3,2.0,66,217
83,remove,6.0,l,,6sC(1(l,6sC(1(,6,remove l from position 6,flow_matching,0.3,2.0,66,217
84,add,0.0,S,,6sC(1(,S6sC(1(,7,add S at position 0,flow_matching,0.3,2.0,66,217
85,replace,1.0,[,6,S6sC(1(,S[sC(1(,7,replace 6 at position 1 with [,flow_matching,0.3,2.0,66,217
86,add,4.0,s,,S[sC(1(,S[sCs(1(,8,add s at position 4,flow_matching,0.3,2.0,66,217
87,remove,5.0,(,,S[sCs(1(,S[sCs1(,7,remove ( from position 5,flow_matching,0.3,2.0,66,217
88,replace,0.0,C,S,S[sCs1(,C[sCs1(,7,replace S at position 0 with C,flow_matching,0.3,2.0,66,217
89,replace,1.0,C,[,C[sCs1(,CCsCs1(,7,replace [ at position 1 with C,flow_matching,0.3,2.0,66,217
90,replace,0.0,F,C,CCsCs1(,FCsCs1(,7,replace C at position 0 with F,flow_matching,0.3,2.0,66,217
91,replace,0.0,C,F,FCsCs1(,CCsCs1(,7,replace F at position 0 with C,flow_matching,0.3,2.0,66,217
92,remove,4.0,s,,CCsCs1(,CCsC1(,6,remove s from position 4,flow_matching,0.3,2.0,66,217
93,replace,1.0,r,C,CCsC1(,CrsC1(,6,replace C at position 1 with r,flow_matching,0.3,2.0,66,217
94,replace,1.0,C,r,CrsC1(,CCsC1(,6,replace r at position 1 with C,flow_matching,0.3,2.0,66,217
95,replace,2.0,(,s,CCsC1(,CC(C1(,6,replace s at position 2 with (,flow_matching,0.3,2.0,66,217
96,replace,4.0,@,1,CC(C1(,CC(C@(,6,replace 1 at position 4 with @,flow_matching,0.3,2.0,66,217
97,add,2.0,(,,CC(C@(,CC((C@(,7,add ( at position 2,flow_matching,0.3,2.0,66,217
98,replace,5.0,=,@,CC((C@(,CC((C=(,7,replace @ at position 5 with =,flow_matching,0.3,2.0,66,217
99,replace,3.0,C,(,CC((C=(,CC(CC=(,7,replace ( at position 3 with C,flow_matching,0.3,2.0,66,217
100,replace,4.0,@,C,CC(CC=(,CC(C@=(,7,replace C at position 4 with @,flow_matching,0.3,2.0,66,217
101,add,0.0,(,,CC(C@=(,(CC(C@=(,8,add ( at position 0,flow_matching,0.3,2.0,66,217
102,add,7.0,=,,(CC(C@=(,(CC(C@==(,9,add = at position 7,flow_matching,0.3,2.0,66,217
103,remove,0.0,(,,(CC(C@==(,CC(C@==(,8,remove ( from position 0,flow_matching,0.3,2.0,66,217
104,replace,0.0,1,C,CC(C@==(,1C(C@==(,8,replace C at position 0 with 1,flow_matching,0.3,2.0,66,217
105,add,1.0,F,,1C(C@==(,1FC(C@==(,9,add F at position 1,flow_matching,0.3,2.0,66,217
106,replace,3.0,4,(,1FC(C@==(,1FC4C@==(,9,replace ( at position 3 with 4,flow_matching,0.3,2.0,66,217
107,remove,3.0,4,,1FC4C@==(,1FCC@==(,8,remove 4 from position 3,flow_matching,0.3,2.0,66,217
108,remove,6.0,=,,1FCC@==(,1FCC@=(,7,remove = from position 6,flow_matching,0.3,2.0,66,217
109,remove,1.0,F,,1FCC@=(,1CC@=(,6,remove F from position 1,flow_matching,0.3,2.0,66,217
110,replace,3.0,n,@,1CC@=(,1CCn=(,6,replace @ at position 3 with n,flow_matching,0.3,2.0,66,217
111,replace,0.0,C,1,1CCn=(,CCCn=(,6,replace 1 at position 0 with C,flow_matching,0.3,2.0,66,217
112,replace,2.0,(,C,CCCn=(,CC(n=(,6,replace C at position 2 with (,flow_matching,0.3,2.0,66,217
113,add,1.0,N,,CC(n=(,CNC(n=(,7,add N at position 1,flow_matching,0.3,2.0,66,217
114,replace,1.0,C,N,CNC(n=(,CCC(n=(,7,replace N at position 1 with C,flow_matching,0.3,2.0,66,217
115,add,0.0,),,CCC(n=(,)CCC(n=(,8,add ) at position 0,flow_matching,0.3,2.0,66,217
116,replace,0.0,C,),)CCC(n=(,CCCC(n=(,8,replace ) at position 0 with C,flow_matching,0.3,2.0,66,217
117,replace,1.0,(,C,CCCC(n=(,C(CC(n=(,8,replace C at position 1 with (,flow_matching,0.3,2.0,66,217
118,replace,1.0,C,(,C(CC(n=(,CCCC(n=(,8,replace ( at position 1 with C,flow_matching,0.3,2.0,66,217
119,replace,2.0,(,C,CCCC(n=(,CC(C(n=(,8,replace C at position 2 with (,flow_matching,0.3,2.0,66,217
120,replace,0.0,7,C,CC(C(n=(,7C(C(n=(,8,replace C at position 0 with 7,flow_matching,0.3,2.0,66,217
121,replace,2.0,4,(,7C(C(n=(,7C4C(n=(,8,replace ( at position 2 with 4,flow_matching,0.3,2.0,66,217
122,add,8.0,H,,7C4C(n=(,7C4C(n=(H,9,add H at position 8,flow_matching,0.3,2.0,66,217
123,add,2.0,7,,7C4C(n=(H,7C74C(n=(H,10,add 7 at position 2,flow_matching,0.3,2.0,66,217
124,remove,0.0,7,,7C74C(n=(H,C74C(n=(H,9,remove 7 from position 0,flow_matching,0.3,2.0,66,217
125,replace,8.0,#,H,C74C(n=(H,C74C(n=(#,9,replace H at position 8 with #,flow_matching,0.3,2.0,66,217
126,replace,4.0,[,(,C74C(n=(#,C74C[n=(#,9,replace ( at position 4 with [,flow_matching,0.3,2.0,66,217
127,replace,1.0,C,7,C74C[n=(#,CC4C[n=(#,9,replace 7 at position 1 with C,flow_matching,0.3,2.0,66,217
128,add,6.0,/,,CC4C[n=(#,CC4C[n/=(#,10,add / at position 6,flow_matching,0.3,2.0,66,217
129,replace,2.0,(,4,CC4C[n/=(#,CC(C[n/=(#,10,replace 4 at position 2 with (,flow_matching,0.3,2.0,66,217
130,replace,4.0,),[,CC(C[n/=(#,CC(C)n/=(#,10,replace [ at position 4 with ),flow_matching,0.3,2.0,66,217
131,add,2.0,n,,CC(C)n/=(#,CCn(C)n/=(#,11,add n at position 2,flow_matching,0.3,2.0,66,217
132,replace,2.0,(,n,CCn(C)n/=(#,CC((C)n/=(#,11,replace n at position 2 with (,flow_matching,0.3,2.0,66,217
133,remove,7.0,/,,CC((C)n/=(#,CC((C)n=(#,10,remove / from position 7,flow_matching,0.3,2.0,66,217
134,replace,3.0,C,(,CC((C)n=(#,CC(CC)n=(#,10,replace ( at position 3 with C,flow_matching,0.3,2.0,66,217
135,replace,6.0,s,n,CC(CC)n=(#,CC(CC)s=(#,10,replace n at position 6 with s,flow_matching,0.3,2.0,66,217
136,replace,4.0,1,C,CC(CC)s=(#,CC(C1)s=(#,10,replace C at position 4 with 1,flow_matching,0.3,2.0,66,217
137,replace,7.0,4,=,CC(C1)s=(#,CC(C1)s4(#,10,replace = at position 7 with 4,flow_matching,0.3,2.0,66,217
138,replace,4.0,),1,CC(C1)s4(#,CC(C))s4(#,10,replace 1 at position 4 with ),flow_matching,0.3,2.0,66,217
139,add,6.0,S,,CC(C))s4(#,CC(C))Ss4(#,11,add S at position 6,flow_matching,0.3,2.0,66,217
140,remove,5.0,),,CC(C))Ss4(#,CC(C)Ss4(#,10,remove ) from position 5,flow_matching,0.3,2.0,66,217
141,replace,3.0,F,C,CC(C)Ss4(#,CC(F)Ss4(#,10,replace C at position 3 with F,flow_matching,0.3,2.0,66,217
142,replace,3.0,C,F,CC(F)Ss4(#,CC(C)Ss4(#,10,replace F at position 3 with C,flow_matching,0.3,2.0,66,217
143,add,3.0,H,,CC(C)Ss4(#,CC(HC)Ss4(#,11,add H at position 3,flow_matching,0.3,2.0,66,217
144,add,11.0,s,,CC(HC)Ss4(#,CC(HC)Ss4(#s,12,add s at position 11,flow_matching,0.3,2.0,66,217
145,replace,3.0,C,H,CC(HC)Ss4(#s,CC(CC)Ss4(#s,12,replace H at position 3 with C,flow_matching,0.3,2.0,66,217
146,replace,4.0,),C,CC(CC)Ss4(#s,CC(C))Ss4(#s,12,replace C at position 4 with ),flow_matching,0.3,2.0,66,217
147,add,1.0,S,,CC(C))Ss4(#s,CSC(C))Ss4(#s,13,add S at position 1,flow_matching,0.3,2.0,66,217
148,remove,9.0,4,,CSC(C))Ss4(#s,CSC(C))Ss(#s,12,remove 4 from position 9,flow_matching,0.3,2.0,66,217
149,replace,0.0,),C,CSC(C))Ss(#s,)SC(C))Ss(#s,12,replace C at position 0 with ),flow_matching,0.3,2.0,66,217
150,remove,6.0,),,)SC(C))Ss(#s,)SC(C)Ss(#s,11,remove ) from position 6,flow_matching,0.3,2.0,66,217
151,replace,3.0,3,(,)SC(C)Ss(#s,)SC3C)Ss(#s,11,replace ( at position 3 with 3,flow_matching,0.3,2.0,66,217
152,add,8.0,F,,)SC3C)Ss(#s,)SC3C)SsF(#s,12,add F at position 8,flow_matching,0.3,2.0,66,217
153,remove,8.0,F,,)SC3C)SsF(#s,)SC3C)Ss(#s,11,remove F from position 8,flow_matching,0.3,2.0,66,217
154,remove,0.0,),,)SC3C)Ss(#s,SC3C)Ss(#s,10,remove ) from position 0,flow_matching,0.3,2.0,66,217
155,replace,0.0,C,S,SC3C)Ss(#s,CC3C)Ss(#s,10,replace S at position 0 with C,flow_matching,0.3,2.0,66,217
156,replace,2.0,(,3,CC3C)Ss(#s,CC(C)Ss(#s,10,replace 3 at position 2 with (,flow_matching,0.3,2.0,66,217
157,replace,5.0,C,S,CC(C)Ss(#s,CC(C)Cs(#s,10,replace S at position 5 with C,flow_matching,0.3,2.0,66,217
158,replace,6.0,[,s,CC(C)Cs(#s,CC(C)C[(#s,10,replace s at position 6 with [,flow_matching,0.3,2.0,66,217
159,replace,7.0,C,(,CC(C)C[(#s,CC(C)C[C#s,10,replace ( at position 7 with C,flow_matching,0.3,2.0,66,217
160,replace,8.0,@,#,CC(C)C[C#s,CC(C)C[C@s,10,replace # at position 8 with @,flow_matching,0.3,2.0,66,217
161,replace,9.0,@,s,CC(C)C[C@s,CC(C)C[C@@,10,replace s at position 9 with @,flow_matching,0.3,2.0,66,217
162,add,10.0,H,,CC(C)C[C@@,CC(C)C[C@@H,11,add H at position 10,flow_matching,0.3,2.0,66,217
163,add,11.0,],,CC(C)C[C@@H,CC(C)C[C@@H],12,add ] at position 11,flow_matching,0.3,2.0,66,217
164,add,12.0,(,,CC(C)C[C@@H],CC(C)C[C@@H](,13,add ( at position 12,flow_matching,0.3,2.0,66,217
165,add,13.0,N,,CC(C)C[C@@H](,CC(C)C[C@@H](N,14,add N at position 13,flow_matching,0.3,2.0,66,217
166,add,14.0,C,,CC(C)C[C@@H](N,CC(C)C[C@@H](NC,15,add C at position 14,flow_matching,0.3,2.0,66,217
167,add,15.0,(,,CC(C)C[C@@H](NC,CC(C)C[C@@H](NC(,16,add ( at position 15,flow_matching,0.3,2.0,66,217
168,add,16.0,=,,CC(C)C[C@@H](NC(,CC(C)C[C@@H](NC(=,17,add = at position 16,flow_matching,0.3,2.0,66,217
169,add,17.0,O,,CC(C)C[C@@H](NC(=,CC(C)C[C@@H](NC(=O,18,add O at position 17,flow_matching,0.3,2.0,66,217
170,add,18.0,),,CC(C)C[C@@H](NC(=O,CC(C)C[C@@H](NC(=O),19,add ) at position 18,flow_matching,0.3,2.0,66,217
171,add,19.0,[,,CC(C)C[C@@H](NC(=O),CC(C)C[C@@H](NC(=O)[,20,add [ at position 19,flow_matching,0.3,2.0,66,217
172,add,20.0,C,,CC(C)C[C@@H](NC(=O)[,CC(C)C[C@@H](NC(=O)[C,21,add C at position 20,flow_matching,0.3,2.0,66,217
173,add,21.0,@,,CC(C)C[C@@H](NC(=O)[C,CC(C)C[C@@H](NC(=O)[C@,22,add @ at position 21,flow_matching,0.3,2.0,66,217
174,add,22.0,@,,CC(C)C[C@@H](NC(=O)[C@,CC(C)C[C@@H](NC(=O)[C@@,23,add @ at position 22,flow_matching,0.3,2.0,66,217
175,add,23.0,H,,CC(C)C[C@@H](NC(=O)[C@@,CC(C)C[C@@H](NC(=O)[C@@H,24,add H at position 23,flow_matching,0.3,2.0,66,217
176,add,24.0,],,CC(C)C[C@@H](NC(=O)[C@@H,CC(C)C[C@@H](NC(=O)[C@@H],25,add ] at position 24,flow_matching,0.3,2.0,66,217
177,add,25.0,1,,CC(C)C[C@@H](NC(=O)[C@@H],CC(C)C[C@@H](NC(=O)[C@@H]1,26,add 1 at position 25,flow_matching,0.3,2.0,66,217
178,add,26.0,C,,CC(C)C[C@@H](NC(=O)[C@@H]1,CC(C)C[C@@H](NC(=O)[C@@H]1C,27,add C at position 26,flow_matching,0.3,2.0,66,217
179,add,27.0,[,,CC(C)C[C@@H](NC(=O)[C@@H]1C,CC(C)C[C@@H](NC(=O)[C@@H]1C[,28,add [ at position 27,flow_matching,0.3,2.0,66,217
180,add,28.0,C,,CC(C)C[C@@H](NC(=O)[C@@H]1C[,CC(C)C[C@@H](NC(=O)[C@@H]1C[C,29,add C at position 28,flow_matching,0.3,2.0,66,217
181,add,29.0,@,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@,30,add @ at position 29,flow_matching,0.3,2.0,66,217
182,add,30.0,@,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@,31,add @ at position 30,flow_matching,0.3,2.0,66,217
183,add,31.0,H,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H,32,add H at position 31,flow_matching,0.3,2.0,66,217
184,add,32.0,],,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H],33,add ] at position 32,flow_matching,0.3,2.0,66,217
185,add,33.0,1,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H],CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1,34,add 1 at position 33,flow_matching,0.3,2.0,66,217
186,add,34.0,c,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c,35,add c at position 34,flow_matching,0.3,2.0,66,217
187,add,35.0,1,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1,36,add 1 at position 35,flow_matching,0.3,2.0,66,217
188,add,36.0,c,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1c,37,add c at position 36,flow_matching,0.3,2.0,66,217
189,add,37.0,c,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1c,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cc,38,add c at position 37,flow_matching,0.3,2.0,66,217
190,add,38.0,c,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cc,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1ccc,39,add c at position 38,flow_matching,0.3,2.0,66,217
191,add,39.0,c,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1ccc,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc,40,add c at position 39,flow_matching,0.3,2.0,66,217
192,add,40.0,(,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(,41,add ( at position 40,flow_matching,0.3,2.0,66,217
193,add,41.0,C,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(C,42,add C at position 41,flow_matching,0.3,2.0,66,217
194,add,42.0,l,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(C,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl,43,add l at position 42,flow_matching,0.3,2.0,66,217
195,add,43.0,),,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl),44,add ) at position 43,flow_matching,0.3,2.0,66,217
196,add,44.0,c,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl),CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c,45,add c at position 44,flow_matching,0.3,2.0,66,217
197,add,45.0,1,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1,46,add 1 at position 45,flow_matching,0.3,2.0,66,217
198,add,46.0,C,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1C,47,add C at position 46,flow_matching,0.3,2.0,66,217
199,add,47.0,l,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1C,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl,48,add l at position 47,flow_matching,0.3,2.0,66,217
200,add,48.0,),,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl),49,add ) at position 48,flow_matching,0.3,2.0,66,217
201,add,49.0,C,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl),CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C,50,add C at position 49,flow_matching,0.3,2.0,66,217
202,add,50.0,(,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(,51,add ( at position 50,flow_matching,0.3,2.0,66,217
203,add,51.0,=,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=,52,add = at position 51,flow_matching,0.3,2.0,66,217
204,add,52.0,O,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O,53,add O at position 52,flow_matching,0.3,2.0,66,217
205,add,53.0,),,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O),54,add ) at position 53,flow_matching,0.3,2.0,66,217
206,add,54.0,N,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O),CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)N,55,add N at position 54,flow_matching,0.3,2.0,66,217
207,add,55.0,c,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)N,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc,56,add c at position 55,flow_matching,0.3,2.0,66,217
208,add,56.0,1,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1,57,add 1 at position 56,flow_matching,0.3,2.0,66,217
209,add,57.0,c,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1c,58,add c at position 57,flow_matching,0.3,2.0,66,217
210,add,58.0,c,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1c,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc,59,add c at position 58,flow_matching,0.3,2.0,66,217
211,add,59.0,[,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[,60,add [ at position 59,flow_matching,0.3,2.0,66,217
212,add,60.0,n,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[n,61,add n at position 60,flow_matching,0.3,2.0,66,217
213,add,61.0,H,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[n,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[nH,62,add H at position 61,flow_matching,0.3,2.0,66,217
214,add,62.0,],,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[nH,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[nH],63,add ] at position 62,flow_matching,0.3,2.0,66,217
215,add,63.0,n,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[nH],CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[nH]n,64,add n at position 63,flow_matching,0.3,2.0,66,217
216,add,64.0,1,,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[nH]n,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[nH]n1,65,add 1 at position 64,flow_matching,0.3,2.0,66,217
217,add,65.0,"
",,CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[nH]n1,"CC(C)C[C@@H](NC(=O)[C@@H]1C[C@@H]1c1cccc(Cl)c1Cl)C(=O)Nc1cc[nH]n1
",66,"add 
 at position 65",flow_matching,0.3,2.0,66,217
