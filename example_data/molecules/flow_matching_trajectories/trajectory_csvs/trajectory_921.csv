step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,50,166
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,50,166
2,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,50,166
3,add,2.0,C,,O=,O=C,3,add C at position 2,flow_matching,0.3,2.0,50,166
4,add,1.0,3,,O=C,O3=C,4,add 3 at position 1,flow_matching,0.3,2.0,50,166
5,remove,0.0,O,,O3=C,3=C,3,remove O from position 0,flow_matching,0.3,2.0,50,166
6,replace,0.0,O,3,3=C,O=C,3,replace 3 at position 0 with O,flow_matching,0.3,2.0,50,166
7,add,3.0,(,,O=C,O=C(,4,add ( at position 3,flow_matching,0.3,2.0,50,166
8,add,4.0,C,,O=C(,O=C(C,5,add C at position 4,flow_matching,0.3,2.0,50,166
9,add,5.0,N,,O=C(C,O=C(CN,6,add N at position 5,flow_matching,0.3,2.0,50,166
10,add,2.0,#,,O=C(CN,O=#C(CN,7,add # at position 2,flow_matching,0.3,2.0,50,166
11,remove,4.0,(,,O=#C(CN,O=#CCN,6,remove ( from position 4,flow_matching,0.3,2.0,50,166
12,replace,5.0,),N,O=#CCN,O=#CC),6,replace N at position 5 with ),flow_matching,0.3,2.0,50,166
13,remove,1.0,=,,O=#CC),O#CC),5,remove = from position 1,flow_matching,0.3,2.0,50,166
14,replace,0.0,5,O,O#CC),5#CC),5,replace O at position 0 with 5,flow_matching,0.3,2.0,50,166
15,replace,0.0,2,5,5#CC),2#CC),5,replace 5 at position 0 with 2,flow_matching,0.3,2.0,50,166
16,remove,4.0,),,2#CC),2#CC,4,remove ) from position 4,flow_matching,0.3,2.0,50,166
17,replace,0.0,O,2,2#CC,O#CC,4,replace 2 at position 0 with O,flow_matching,0.3,2.0,50,166
18,remove,2.0,C,,O#CC,O#C,3,remove C from position 2,flow_matching,0.3,2.0,50,166
19,add,0.0,H,,O#C,HO#C,4,add H at position 0,flow_matching,0.3,2.0,50,166
20,remove,2.0,#,,HO#C,HOC,3,remove # from position 2,flow_matching,0.3,2.0,50,166
21,add,2.0,+,,HOC,HO+C,4,add + at position 2,flow_matching,0.3,2.0,50,166
22,add,0.0,N,,HO+C,NHO+C,5,add N at position 0,flow_matching,0.3,2.0,50,166
23,add,2.0,#,,NHO+C,NH#O+C,6,add # at position 2,flow_matching,0.3,2.0,50,166
24,replace,5.0,@,C,NH#O+C,NH#O+@,6,replace C at position 5 with @,flow_matching,0.3,2.0,50,166
25,add,4.0,3,,NH#O+@,NH#O3+@,7,add 3 at position 4,flow_matching,0.3,2.0,50,166
26,add,2.0,1,,NH#O3+@,NH1#O3+@,8,add 1 at position 2,flow_matching,0.3,2.0,50,166
27,replace,0.0,O,N,NH1#O3+@,OH1#O3+@,8,replace N at position 0 with O,flow_matching,0.3,2.0,50,166
28,remove,4.0,O,,OH1#O3+@,OH1#3+@,7,remove O from position 4,flow_matching,0.3,2.0,50,166
29,replace,1.0,=,H,OH1#3+@,O=1#3+@,7,replace H at position 1 with =,flow_matching,0.3,2.0,50,166
30,remove,5.0,+,,O=1#3+@,O=1#3@,6,remove + from position 5,flow_matching,0.3,2.0,50,166
31,add,3.0,5,,O=1#3@,O=15#3@,7,add 5 at position 3,flow_matching,0.3,2.0,50,166
32,remove,2.0,1,,O=15#3@,O=5#3@,6,remove 1 from position 2,flow_matching,0.3,2.0,50,166
33,remove,4.0,3,,O=5#3@,O=5#@,5,remove 3 from position 4,flow_matching,0.3,2.0,50,166
34,add,4.0,\,,O=5#@,O=5#\@,6,add \ at position 4,flow_matching,0.3,2.0,50,166
35,remove,4.0,\,,O=5#\@,O=5#@,5,remove \ from position 4,flow_matching,0.3,2.0,50,166
36,replace,4.0,N,@,O=5#@,O=5#N,5,replace @ at position 4 with N,flow_matching,0.3,2.0,50,166
37,replace,1.0,n,=,O=5#N,On5#N,5,replace = at position 1 with n,flow_matching,0.3,2.0,50,166
38,remove,0.0,O,,On5#N,n5#N,4,remove O from position 0,flow_matching,0.3,2.0,50,166
39,remove,0.0,n,,n5#N,5#N,3,remove n from position 0,flow_matching,0.3,2.0,50,166
40,replace,2.0,6,N,5#N,5#6,3,replace N at position 2 with 6,flow_matching,0.3,2.0,50,166
41,add,3.0,c,,5#6,5#6c,4,add c at position 3,flow_matching,0.3,2.0,50,166
42,replace,2.0,l,6,5#6c,5#lc,4,replace 6 at position 2 with l,flow_matching,0.3,2.0,50,166
43,remove,0.0,5,,5#lc,#lc,3,remove 5 from position 0,flow_matching,0.3,2.0,50,166
44,add,1.0,H,,#lc,#Hlc,4,add H at position 1,flow_matching,0.3,2.0,50,166
45,replace,0.0,O,#,#Hlc,OHlc,4,replace # at position 0 with O,flow_matching,0.3,2.0,50,166
46,replace,1.0,=,H,OHlc,O=lc,4,replace H at position 1 with =,flow_matching,0.3,2.0,50,166
47,replace,1.0,3,=,O=lc,O3lc,4,replace = at position 1 with 3,flow_matching,0.3,2.0,50,166
48,replace,1.0,=,3,O3lc,O=lc,4,replace 3 at position 1 with =,flow_matching,0.3,2.0,50,166
49,add,3.0,s,,O=lc,O=lsc,5,add s at position 3,flow_matching,0.3,2.0,50,166
50,replace,2.0,C,l,O=lsc,O=Csc,5,replace l at position 2 with C,flow_matching,0.3,2.0,50,166
51,remove,3.0,s,,O=Csc,O=Cc,4,remove s from position 3,flow_matching,0.3,2.0,50,166
52,replace,0.0,(,O,O=Cc,(=Cc,4,replace O at position 0 with (,flow_matching,0.3,2.0,50,166
53,replace,1.0,6,=,(=Cc,(6Cc,4,replace = at position 1 with 6,flow_matching,0.3,2.0,50,166
54,replace,0.0,O,(,(6Cc,O6Cc,4,replace ( at position 0 with O,flow_matching,0.3,2.0,50,166
55,add,1.0,],,O6Cc,O]6Cc,5,add ] at position 1,flow_matching,0.3,2.0,50,166
56,replace,1.0,=,],O]6Cc,O=6Cc,5,replace ] at position 1 with =,flow_matching,0.3,2.0,50,166
57,replace,2.0,C,6,O=6Cc,O=CCc,5,replace 6 at position 2 with C,flow_matching,0.3,2.0,50,166
58,add,0.0,1,,O=CCc,1O=CCc,6,add 1 at position 0,flow_matching,0.3,2.0,50,166
59,add,4.0,7,,1O=CCc,1O=C7Cc,7,add 7 at position 4,flow_matching,0.3,2.0,50,166
60,replace,0.0,O,1,1O=C7Cc,OO=C7Cc,7,replace 1 at position 0 with O,flow_matching,0.3,2.0,50,166
61,add,6.0,4,,OO=C7Cc,OO=C7C4c,8,add 4 at position 6,flow_matching,0.3,2.0,50,166
62,replace,5.0,2,C,OO=C7C4c,OO=C724c,8,replace C at position 5 with 2,flow_matching,0.3,2.0,50,166
63,remove,5.0,2,,OO=C724c,OO=C74c,7,remove 2 from position 5,flow_matching,0.3,2.0,50,166
64,add,4.0,(,,OO=C74c,OO=C(74c,8,add ( at position 4,flow_matching,0.3,2.0,50,166
65,replace,1.0,=,O,OO=C(74c,O==C(74c,8,replace O at position 1 with =,flow_matching,0.3,2.0,50,166
66,replace,2.0,C,=,O==C(74c,O=CC(74c,8,replace = at position 2 with C,flow_matching,0.3,2.0,50,166
67,remove,2.0,C,,O=CC(74c,O=C(74c,7,remove C from position 2,flow_matching,0.3,2.0,50,166
68,add,2.0,[,,O=C(74c,O=[C(74c,8,add [ at position 2,flow_matching,0.3,2.0,50,166
69,replace,2.0,C,[,O=[C(74c,O=CC(74c,8,replace [ at position 2 with C,flow_matching,0.3,2.0,50,166
70,replace,3.0,(,C,O=CC(74c,O=C((74c,8,replace C at position 3 with (,flow_matching,0.3,2.0,50,166
71,replace,4.0,C,(,O=C((74c,O=C(C74c,8,replace ( at position 4 with C,flow_matching,0.3,2.0,50,166
72,replace,1.0,B,=,O=C(C74c,OBC(C74c,8,replace = at position 1 with B,flow_matching,0.3,2.0,50,166
73,add,7.0,6,,OBC(C74c,OBC(C746c,9,add 6 at position 7,flow_matching,0.3,2.0,50,166
74,remove,8.0,c,,OBC(C746c,OBC(C746,8,remove c from position 8,flow_matching,0.3,2.0,50,166
75,replace,1.0,=,B,OBC(C746,O=C(C746,8,replace B at position 1 with =,flow_matching,0.3,2.0,50,166
76,add,7.0,I,,O=C(C746,O=C(C74I6,9,add I at position 7,flow_matching,0.3,2.0,50,166
77,remove,1.0,=,,O=C(C74I6,OC(C74I6,8,remove = from position 1,flow_matching,0.3,2.0,50,166
78,replace,0.0,],O,OC(C74I6,]C(C74I6,8,replace O at position 0 with ],flow_matching,0.3,2.0,50,166
79,add,8.0,/,,]C(C74I6,]C(C74I6/,9,add / at position 8,flow_matching,0.3,2.0,50,166
80,replace,0.0,O,],]C(C74I6/,OC(C74I6/,9,replace ] at position 0 with O,flow_matching,0.3,2.0,50,166
81,replace,1.0,=,C,OC(C74I6/,O=(C74I6/,9,replace C at position 1 with =,flow_matching,0.3,2.0,50,166
82,remove,0.0,O,,O=(C74I6/,=(C74I6/,8,remove O from position 0,flow_matching,0.3,2.0,50,166
83,replace,2.0,I,C,=(C74I6/,=(I74I6/,8,replace C at position 2 with I,flow_matching,0.3,2.0,50,166
84,remove,5.0,I,,=(I74I6/,=(I746/,7,remove I from position 5,flow_matching,0.3,2.0,50,166
85,add,6.0,I,,=(I746/,=(I746I/,8,add I at position 6,flow_matching,0.3,2.0,50,166
86,add,0.0,@,,=(I746I/,@=(I746I/,9,add @ at position 0,flow_matching,0.3,2.0,50,166
87,remove,4.0,7,,@=(I746I/,@=(I46I/,8,remove 7 from position 4,flow_matching,0.3,2.0,50,166
88,add,6.0,r,,@=(I46I/,@=(I46rI/,9,add r at position 6,flow_matching,0.3,2.0,50,166
89,add,2.0,+,,@=(I46rI/,@=+(I46rI/,10,add + at position 2,flow_matching,0.3,2.0,50,166
90,replace,9.0,2,/,@=+(I46rI/,@=+(I46rI2,10,replace / at position 9 with 2,flow_matching,0.3,2.0,50,166
91,remove,4.0,I,,@=+(I46rI2,@=+(46rI2,9,remove I from position 4,flow_matching,0.3,2.0,50,166
92,replace,4.0,@,4,@=+(46rI2,@=+(@6rI2,9,replace 4 at position 4 with @,flow_matching,0.3,2.0,50,166
93,remove,3.0,(,,@=+(@6rI2,@=+@6rI2,8,remove ( from position 3,flow_matching,0.3,2.0,50,166
94,add,3.0,n,,@=+@6rI2,@=+n@6rI2,9,add n at position 3,flow_matching,0.3,2.0,50,166
95,remove,1.0,=,,@=+n@6rI2,@+n@6rI2,8,remove = from position 1,flow_matching,0.3,2.0,50,166
96,replace,0.0,O,@,@+n@6rI2,O+n@6rI2,8,replace @ at position 0 with O,flow_matching,0.3,2.0,50,166
97,replace,1.0,=,+,O+n@6rI2,O=n@6rI2,8,replace + at position 1 with =,flow_matching,0.3,2.0,50,166
98,replace,2.0,C,n,O=n@6rI2,O=C@6rI2,8,replace n at position 2 with C,flow_matching,0.3,2.0,50,166
99,remove,6.0,I,,O=C@6rI2,O=C@6r2,7,remove I from position 6,flow_matching,0.3,2.0,50,166
100,replace,1.0,4,=,O=C@6r2,O4C@6r2,7,replace = at position 1 with 4,flow_matching,0.3,2.0,50,166
101,replace,1.0,=,4,O4C@6r2,O=C@6r2,7,replace 4 at position 1 with =,flow_matching,0.3,2.0,50,166
102,replace,3.0,(,@,O=C@6r2,O=C(6r2,7,replace @ at position 3 with (,flow_matching,0.3,2.0,50,166
103,remove,2.0,C,,O=C(6r2,O=(6r2,6,remove C from position 2,flow_matching,0.3,2.0,50,166
104,add,4.0,[,,O=(6r2,O=(6[r2,7,add [ at position 4,flow_matching,0.3,2.0,50,166
105,replace,2.0,C,(,O=(6[r2,O=C6[r2,7,replace ( at position 2 with C,flow_matching,0.3,2.0,50,166
106,add,0.0,o,,O=C6[r2,oO=C6[r2,8,add o at position 0,flow_matching,0.3,2.0,50,166
107,add,0.0,/,,oO=C6[r2,/oO=C6[r2,9,add / at position 0,flow_matching,0.3,2.0,50,166
108,remove,4.0,C,,/oO=C6[r2,/oO=6[r2,8,remove C from position 4,flow_matching,0.3,2.0,50,166
109,remove,4.0,6,,/oO=6[r2,/oO=[r2,7,remove 6 from position 4,flow_matching,0.3,2.0,50,166
110,add,3.0,o,,/oO=[r2,/oOo=[r2,8,add o at position 3,flow_matching,0.3,2.0,50,166
111,add,6.0,],,/oOo=[r2,/oOo=[]r2,9,add ] at position 6,flow_matching,0.3,2.0,50,166
112,add,9.0,=,,/oOo=[]r2,/oOo=[]r2=,10,add = at position 9,flow_matching,0.3,2.0,50,166
113,replace,0.0,O,/,/oOo=[]r2=,OoOo=[]r2=,10,replace / at position 0 with O,flow_matching,0.3,2.0,50,166
114,add,1.0,I,,OoOo=[]r2=,OIoOo=[]r2=,11,add I at position 1,flow_matching,0.3,2.0,50,166
115,replace,1.0,=,I,OIoOo=[]r2=,O=oOo=[]r2=,11,replace I at position 1 with =,flow_matching,0.3,2.0,50,166
116,replace,4.0,F,o,O=oOo=[]r2=,O=oOF=[]r2=,11,replace o at position 4 with F,flow_matching,0.3,2.0,50,166
117,remove,10.0,=,,O=oOF=[]r2=,O=oOF=[]r2,10,remove = from position 10,flow_matching,0.3,2.0,50,166
118,add,9.0,B,,O=oOF=[]r2,O=oOF=[]rB2,11,add B at position 9,flow_matching,0.3,2.0,50,166
119,replace,2.0,C,o,O=oOF=[]rB2,O=COF=[]rB2,11,replace o at position 2 with C,flow_matching,0.3,2.0,50,166
120,replace,3.0,(,O,O=COF=[]rB2,O=C(F=[]rB2,11,replace O at position 3 with (,flow_matching,0.3,2.0,50,166
121,replace,4.0,C,F,O=C(F=[]rB2,O=C(C=[]rB2,11,replace F at position 4 with C,flow_matching,0.3,2.0,50,166
122,replace,5.0,N,=,O=C(C=[]rB2,O=C(CN[]rB2,11,replace = at position 5 with N,flow_matching,0.3,2.0,50,166
123,replace,6.0,(,[,O=C(CN[]rB2,O=C(CN(]rB2,11,replace [ at position 6 with (,flow_matching,0.3,2.0,50,166
124,replace,7.0,C,],O=C(CN(]rB2,O=C(CN(CrB2,11,replace ] at position 7 with C,flow_matching,0.3,2.0,50,166
125,replace,8.0,(,r,O=C(CN(CrB2,O=C(CN(C(B2,11,replace r at position 8 with (,flow_matching,0.3,2.0,50,166
126,replace,9.0,=,B,O=C(CN(C(B2,O=C(CN(C(=2,11,replace B at position 9 with =,flow_matching,0.3,2.0,50,166
127,replace,10.0,O,2,O=C(CN(C(=2,O=C(CN(C(=O,11,replace 2 at position 10 with O,flow_matching,0.3,2.0,50,166
128,add,11.0,),,O=C(CN(C(=O,O=C(CN(C(=O),12,add ) at position 11,flow_matching,0.3,2.0,50,166
129,add,12.0,C,,O=C(CN(C(=O),O=C(CN(C(=O)C,13,add C at position 12,flow_matching,0.3,2.0,50,166
130,add,13.0,n,,O=C(CN(C(=O)C,O=C(CN(C(=O)Cn,14,add n at position 13,flow_matching,0.3,2.0,50,166
131,add,14.0,1,,O=C(CN(C(=O)Cn,O=C(CN(C(=O)Cn1,15,add 1 at position 14,flow_matching,0.3,2.0,50,166
132,add,15.0,n,,O=C(CN(C(=O)Cn1,O=C(CN(C(=O)Cn1n,16,add n at position 15,flow_matching,0.3,2.0,50,166
133,add,16.0,n,,O=C(CN(C(=O)Cn1n,O=C(CN(C(=O)Cn1nn,17,add n at position 16,flow_matching,0.3,2.0,50,166
134,add,17.0,c,,O=C(CN(C(=O)Cn1nn,O=C(CN(C(=O)Cn1nnc,18,add c at position 17,flow_matching,0.3,2.0,50,166
135,add,18.0,2,,O=C(CN(C(=O)Cn1nnc,O=C(CN(C(=O)Cn1nnc2,19,add 2 at position 18,flow_matching,0.3,2.0,50,166
136,add,19.0,c,,O=C(CN(C(=O)Cn1nnc2,O=C(CN(C(=O)Cn1nnc2c,20,add c at position 19,flow_matching,0.3,2.0,50,166
137,add,20.0,c,,O=C(CN(C(=O)Cn1nnc2c,O=C(CN(C(=O)Cn1nnc2cc,21,add c at position 20,flow_matching,0.3,2.0,50,166
138,add,21.0,c,,O=C(CN(C(=O)Cn1nnc2cc,O=C(CN(C(=O)Cn1nnc2ccc,22,add c at position 21,flow_matching,0.3,2.0,50,166
139,add,22.0,c,,O=C(CN(C(=O)Cn1nnc2ccc,O=C(CN(C(=O)Cn1nnc2cccc,23,add c at position 22,flow_matching,0.3,2.0,50,166
140,add,23.0,c,,O=C(CN(C(=O)Cn1nnc2cccc,O=C(CN(C(=O)Cn1nnc2ccccc,24,add c at position 23,flow_matching,0.3,2.0,50,166
141,add,24.0,2,,O=C(CN(C(=O)Cn1nnc2ccccc,O=C(CN(C(=O)Cn1nnc2ccccc2,25,add 2 at position 24,flow_matching,0.3,2.0,50,166
142,add,25.0,1,,O=C(CN(C(=O)Cn1nnc2ccccc2,O=C(CN(C(=O)Cn1nnc2ccccc21,26,add 1 at position 25,flow_matching,0.3,2.0,50,166
143,add,26.0,),,O=C(CN(C(=O)Cn1nnc2ccccc21,O=C(CN(C(=O)Cn1nnc2ccccc21),27,add ) at position 26,flow_matching,0.3,2.0,50,166
144,add,27.0,c,,O=C(CN(C(=O)Cn1nnc2ccccc21),O=C(CN(C(=O)Cn1nnc2ccccc21)c,28,add c at position 27,flow_matching,0.3,2.0,50,166
145,add,28.0,1,,O=C(CN(C(=O)Cn1nnc2ccccc21)c,O=C(CN(C(=O)Cn1nnc2ccccc21)c1,29,add 1 at position 28,flow_matching,0.3,2.0,50,166
146,add,29.0,c,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1,O=C(CN(C(=O)Cn1nnc2ccccc21)c1c,30,add c at position 29,flow_matching,0.3,2.0,50,166
147,add,30.0,c,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1c,O=C(CN(C(=O)Cn1nnc2ccccc21)c1cc,31,add c at position 30,flow_matching,0.3,2.0,50,166
148,add,31.0,c,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1cc,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccc,32,add c at position 31,flow_matching,0.3,2.0,50,166
149,add,32.0,c,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccc,O=C(CN(C(=O)Cn1nnc2ccccc21)c1cccc,33,add c at position 32,flow_matching,0.3,2.0,50,166
150,add,33.0,c,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1cccc,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc,34,add c at position 33,flow_matching,0.3,2.0,50,166
151,add,34.0,1,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1,35,add 1 at position 34,flow_matching,0.3,2.0,50,166
152,add,35.0,),,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1),36,add ) at position 35,flow_matching,0.3,2.0,50,166
153,add,36.0,N,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1),O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)N,37,add N at position 36,flow_matching,0.3,2.0,50,166
154,add,37.0,C,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)N,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC,38,add C at position 37,flow_matching,0.3,2.0,50,166
155,add,38.0,[,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[,39,add [ at position 38,flow_matching,0.3,2.0,50,166
156,add,39.0,C,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C,40,add C at position 39,flow_matching,0.3,2.0,50,166
157,add,40.0,@,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@,41,add @ at position 40,flow_matching,0.3,2.0,50,166
158,add,41.0,H,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H,42,add H at position 41,flow_matching,0.3,2.0,50,166
159,add,42.0,],,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H],43,add ] at position 42,flow_matching,0.3,2.0,50,166
160,add,43.0,1,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H],O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1,44,add 1 at position 43,flow_matching,0.3,2.0,50,166
161,add,44.0,C,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1C,45,add C at position 44,flow_matching,0.3,2.0,50,166
162,add,45.0,C,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1C,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1CC,46,add C at position 45,flow_matching,0.3,2.0,50,166
163,add,46.0,C,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1CC,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1CCC,47,add C at position 46,flow_matching,0.3,2.0,50,166
164,add,47.0,O,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1CCC,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1CCCO,48,add O at position 47,flow_matching,0.3,2.0,50,166
165,add,48.0,1,,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1CCCO,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1CCCO1,49,add 1 at position 48,flow_matching,0.3,2.0,50,166
166,add,49.0,"
",,O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1CCCO1,"O=C(CN(C(=O)Cn1nnc2ccccc21)c1ccccc1)NC[C@H]1CCCO1
",50,"add 
 at position 49",flow_matching,0.3,2.0,50,166
