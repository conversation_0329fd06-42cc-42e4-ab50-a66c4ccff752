step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,42,204
1,add,0.0,\,,,\,1,add \ at position 0,flow_matching,0.3,2.0,42,204
2,replace,0.0,/,\,\,/,1,replace \ at position 0 with /,flow_matching,0.3,2.0,42,204
3,add,1.0,N,,/,/N,2,add N at position 1,flow_matching,0.3,2.0,42,204
4,replace,0.0,C,/,/N,CN,2,replace / at position 0 with C,flow_matching,0.3,2.0,42,204
5,replace,1.0,C,N,CN,CC,2,replace N at position 1 with C,flow_matching,0.3,2.0,42,204
6,add,2.0,<PERSON>,,<PERSON>,<PERSON><PERSON>,3,add N at position 2,flow_matching,0.3,2.0,42,204
7,add,3.0,(,,CCN,CCN(,4,add ( at position 3,flow_matching,0.3,2.0,42,204
8,add,4.0,c,,CCN(,CCN(c,5,add c at position 4,flow_matching,0.3,2.0,42,204
9,remove,4.0,c,,CCN(c,CCN(,4,remove c from position 4,flow_matching,0.3,2.0,42,204
10,remove,0.0,C,,CCN(,CN(,3,remove C from position 0,flow_matching,0.3,2.0,42,204
11,add,2.0,4,,CN(,CN4(,4,add 4 at position 2,flow_matching,0.3,2.0,42,204
12,remove,2.0,4,,CN4(,CN(,3,remove 4 from position 2,flow_matching,0.3,2.0,42,204
13,remove,0.0,C,,CN(,N(,2,remove C from position 0,flow_matching,0.3,2.0,42,204
14,replace,0.0,C,N,N(,C(,2,replace N at position 0 with C,flow_matching,0.3,2.0,42,204
15,replace,1.0,C,(,C(,CC,2,replace ( at position 1 with C,flow_matching,0.3,2.0,42,204
16,add,2.0,N,,CC,CCN,3,add N at position 2,flow_matching,0.3,2.0,42,204
17,remove,2.0,N,,CCN,CC,2,remove N from position 2,flow_matching,0.3,2.0,42,204
18,add,1.0,N,,CC,CNC,3,add N at position 1,flow_matching,0.3,2.0,42,204
19,replace,1.0,C,N,CNC,CCC,3,replace N at position 1 with C,flow_matching,0.3,2.0,42,204
20,remove,1.0,C,,CCC,CC,2,remove C from position 1,flow_matching,0.3,2.0,42,204
21,add,1.0,(,,CC,C(C,3,add ( at position 1,flow_matching,0.3,2.0,42,204
22,replace,1.0,C,(,C(C,CCC,3,replace ( at position 1 with C,flow_matching,0.3,2.0,42,204
23,replace,2.0,N,C,CCC,CCN,3,replace C at position 2 with N,flow_matching,0.3,2.0,42,204
24,remove,0.0,C,,CCN,CN,2,remove C from position 0,flow_matching,0.3,2.0,42,204
25,remove,1.0,N,,CN,C,1,remove N from position 1,flow_matching,0.3,2.0,42,204
26,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,42,204
27,add,2.0,N,,CC,CCN,3,add N at position 2,flow_matching,0.3,2.0,42,204
28,replace,1.0,4,C,CCN,C4N,3,replace C at position 1 with 4,flow_matching,0.3,2.0,42,204
29,replace,1.0,C,4,C4N,CCN,3,replace 4 at position 1 with C,flow_matching,0.3,2.0,42,204
30,replace,2.0,6,N,CCN,CC6,3,replace N at position 2 with 6,flow_matching,0.3,2.0,42,204
31,remove,0.0,C,,CC6,C6,2,remove C from position 0,flow_matching,0.3,2.0,42,204
32,add,2.0,6,,C6,C66,3,add 6 at position 2,flow_matching,0.3,2.0,42,204
33,add,3.0,+,,C66,C66+,4,add + at position 3,flow_matching,0.3,2.0,42,204
34,replace,1.0,C,6,C66+,CC6+,4,replace 6 at position 1 with C,flow_matching,0.3,2.0,42,204
35,add,3.0,c,,CC6+,CC6c+,5,add c at position 3,flow_matching,0.3,2.0,42,204
36,replace,2.0,N,6,CC6c+,CCNc+,5,replace 6 at position 2 with N,flow_matching,0.3,2.0,42,204
37,replace,3.0,(,c,CCNc+,CCN(+,5,replace c at position 3 with (,flow_matching,0.3,2.0,42,204
38,replace,4.0,C,+,CCN(+,CCN(C,5,replace + at position 4 with C,flow_matching,0.3,2.0,42,204
39,add,5.0,c,,CCN(C,CCN(Cc,6,add c at position 5,flow_matching,0.3,2.0,42,204
40,add,0.0,S,,CCN(Cc,SCCN(Cc,7,add S at position 0,flow_matching,0.3,2.0,42,204
41,replace,1.0,2,C,SCCN(Cc,S2CN(Cc,7,replace C at position 1 with 2,flow_matching,0.3,2.0,42,204
42,remove,1.0,2,,S2CN(Cc,SCN(Cc,6,remove 2 from position 1,flow_matching,0.3,2.0,42,204
43,add,0.0,/,,SCN(Cc,/SCN(Cc,7,add / at position 0,flow_matching,0.3,2.0,42,204
44,replace,4.0,N,(,/SCN(Cc,/SCNNCc,7,replace ( at position 4 with N,flow_matching,0.3,2.0,42,204
45,replace,0.0,C,/,/SCNNCc,CSCNNCc,7,replace / at position 0 with C,flow_matching,0.3,2.0,42,204
46,replace,1.0,C,S,CSCNNCc,CCCNNCc,7,replace S at position 1 with C,flow_matching,0.3,2.0,42,204
47,remove,3.0,N,,CCCNNCc,CCCNCc,6,remove N from position 3,flow_matching,0.3,2.0,42,204
48,replace,5.0,/,c,CCCNCc,CCCNC/,6,replace c at position 5 with /,flow_matching,0.3,2.0,42,204
49,replace,2.0,N,C,CCCNC/,CCNNC/,6,replace C at position 2 with N,flow_matching,0.3,2.0,42,204
50,remove,5.0,/,,CCNNC/,CCNNC,5,remove / from position 5,flow_matching,0.3,2.0,42,204
51,remove,2.0,N,,CCNNC,CCNC,4,remove N from position 2,flow_matching,0.3,2.0,42,204
52,add,1.0,[,,CCNC,C[CNC,5,add [ at position 1,flow_matching,0.3,2.0,42,204
53,replace,2.0,[,C,C[CNC,C[[NC,5,replace C at position 2 with [,flow_matching,0.3,2.0,42,204
54,replace,1.0,#,[,C[[NC,C#[NC,5,replace [ at position 1 with #,flow_matching,0.3,2.0,42,204
55,remove,1.0,#,,C#[NC,C[NC,4,remove # from position 1,flow_matching,0.3,2.0,42,204
56,remove,1.0,[,,C[NC,CNC,3,remove [ from position 1,flow_matching,0.3,2.0,42,204
57,add,0.0,O,,CNC,OCNC,4,add O at position 0,flow_matching,0.3,2.0,42,204
58,replace,3.0,r,C,OCNC,OCNr,4,replace C at position 3 with r,flow_matching,0.3,2.0,42,204
59,replace,0.0,C,O,OCNr,CCNr,4,replace O at position 0 with C,flow_matching,0.3,2.0,42,204
60,remove,3.0,r,,CCNr,CCN,3,remove r from position 3,flow_matching,0.3,2.0,42,204
61,add,2.0,7,,CCN,CC7N,4,add 7 at position 2,flow_matching,0.3,2.0,42,204
62,add,4.0,2,,CC7N,CC7N2,5,add 2 at position 4,flow_matching,0.3,2.0,42,204
63,add,2.0,4,,CC7N2,CC47N2,6,add 4 at position 2,flow_matching,0.3,2.0,42,204
64,add,0.0,+,,CC47N2,+CC47N2,7,add + at position 0,flow_matching,0.3,2.0,42,204
65,add,7.0,N,,+CC47N2,+CC47N2N,8,add N at position 7,flow_matching,0.3,2.0,42,204
66,add,0.0,/,,+CC47N2N,/+CC47N2N,9,add / at position 0,flow_matching,0.3,2.0,42,204
67,add,8.0,H,,/+CC47N2N,/+CC47N2HN,10,add H at position 8,flow_matching,0.3,2.0,42,204
68,replace,2.0,n,C,/+CC47N2HN,/+nC47N2HN,10,replace C at position 2 with n,flow_matching,0.3,2.0,42,204
69,replace,0.0,C,/,/+nC47N2HN,C+nC47N2HN,10,replace / at position 0 with C,flow_matching,0.3,2.0,42,204
70,replace,1.0,),+,C+nC47N2HN,C)nC47N2HN,10,replace + at position 1 with ),flow_matching,0.3,2.0,42,204
71,replace,4.0,s,4,C)nC47N2HN,C)nCs7N2HN,10,replace 4 at position 4 with s,flow_matching,0.3,2.0,42,204
72,add,5.0,l,,C)nCs7N2HN,C)nCsl7N2HN,11,add l at position 5,flow_matching,0.3,2.0,42,204
73,add,2.0,2,,C)nCsl7N2HN,C)2nCsl7N2HN,12,add 2 at position 2,flow_matching,0.3,2.0,42,204
74,replace,7.0,s,7,C)2nCsl7N2HN,C)2nCslsN2HN,12,replace 7 at position 7 with s,flow_matching,0.3,2.0,42,204
75,replace,1.0,C,),C)2nCslsN2HN,CC2nCslsN2HN,12,replace ) at position 1 with C,flow_matching,0.3,2.0,42,204
76,replace,2.0,N,2,CC2nCslsN2HN,CCNnCslsN2HN,12,replace 2 at position 2 with N,flow_matching,0.3,2.0,42,204
77,remove,1.0,C,,CCNnCslsN2HN,CNnCslsN2HN,11,remove C from position 1,flow_matching,0.3,2.0,42,204
78,replace,1.0,C,N,CNnCslsN2HN,CCnCslsN2HN,11,replace N at position 1 with C,flow_matching,0.3,2.0,42,204
79,replace,2.0,N,n,CCnCslsN2HN,CCNCslsN2HN,11,replace n at position 2 with N,flow_matching,0.3,2.0,42,204
80,replace,9.0,2,H,CCNCslsN2HN,CCNCslsN22N,11,replace H at position 9 with 2,flow_matching,0.3,2.0,42,204
81,replace,3.0,(,C,CCNCslsN22N,CCN(slsN22N,11,replace C at position 3 with (,flow_matching,0.3,2.0,42,204
82,remove,9.0,2,,CCN(slsN22N,CCN(slsN2N,10,remove 2 from position 9,flow_matching,0.3,2.0,42,204
83,add,0.0,),,CCN(slsN2N,)CCN(slsN2N,11,add ) at position 0,flow_matching,0.3,2.0,42,204
84,replace,0.0,C,),)CCN(slsN2N,CCCN(slsN2N,11,replace ) at position 0 with C,flow_matching,0.3,2.0,42,204
85,replace,4.0,l,(,CCCN(slsN2N,CCCNlslsN2N,11,replace ( at position 4 with l,flow_matching,0.3,2.0,42,204
86,remove,10.0,N,,CCCNlslsN2N,CCCNlslsN2,10,remove N from position 10,flow_matching,0.3,2.0,42,204
87,remove,5.0,s,,CCCNlslsN2,CCCNllsN2,9,remove s from position 5,flow_matching,0.3,2.0,42,204
88,add,4.0,c,,CCCNllsN2,CCCNcllsN2,10,add c at position 4,flow_matching,0.3,2.0,42,204
89,replace,2.0,N,C,CCCNcllsN2,CCNNcllsN2,10,replace C at position 2 with N,flow_matching,0.3,2.0,42,204
90,replace,3.0,(,N,CCNNcllsN2,CCN(cllsN2,10,replace N at position 3 with (,flow_matching,0.3,2.0,42,204
91,remove,5.0,l,,CCN(cllsN2,CCN(clsN2,9,remove l from position 5,flow_matching,0.3,2.0,42,204
92,replace,4.0,C,c,CCN(clsN2,CCN(ClsN2,9,replace c at position 4 with C,flow_matching,0.3,2.0,42,204
93,replace,0.0,H,C,CCN(ClsN2,HCN(ClsN2,9,replace C at position 0 with H,flow_matching,0.3,2.0,42,204
94,replace,0.0,C,H,HCN(ClsN2,CCN(ClsN2,9,replace H at position 0 with C,flow_matching,0.3,2.0,42,204
95,add,5.0,l,,CCN(ClsN2,CCN(CllsN2,10,add l at position 5,flow_matching,0.3,2.0,42,204
96,replace,9.0,-,2,CCN(CllsN2,CCN(CllsN-,10,replace 2 at position 9 with -,flow_matching,0.3,2.0,42,204
97,remove,8.0,N,,CCN(CllsN-,CCN(Clls-,9,remove N from position 8,flow_matching,0.3,2.0,42,204
98,replace,5.0,c,l,CCN(Clls-,CCN(Ccls-,9,replace l at position 5 with c,flow_matching,0.3,2.0,42,204
99,replace,1.0,F,C,CCN(Ccls-,CFN(Ccls-,9,replace C at position 1 with F,flow_matching,0.3,2.0,42,204
100,replace,1.0,C,F,CFN(Ccls-,CCN(Ccls-,9,replace F at position 1 with C,flow_matching,0.3,2.0,42,204
101,add,8.0,F,,CCN(Ccls-,CCN(CclsF-,10,add F at position 8,flow_matching,0.3,2.0,42,204
102,replace,6.0,1,l,CCN(CclsF-,CCN(Cc1sF-,10,replace l at position 6 with 1,flow_matching,0.3,2.0,42,204
103,remove,4.0,C,,CCN(Cc1sF-,CCN(c1sF-,9,remove C from position 4,flow_matching,0.3,2.0,42,204
104,replace,4.0,C,c,CCN(c1sF-,CCN(C1sF-,9,replace c at position 4 with C,flow_matching,0.3,2.0,42,204
105,replace,5.0,c,1,CCN(C1sF-,CCN(CcsF-,9,replace 1 at position 5 with c,flow_matching,0.3,2.0,42,204
106,replace,6.0,1,s,CCN(CcsF-,CCN(Cc1F-,9,replace s at position 6 with 1,flow_matching,0.3,2.0,42,204
107,remove,5.0,c,,CCN(Cc1F-,CCN(C1F-,8,remove c from position 5,flow_matching,0.3,2.0,42,204
108,add,7.0,s,,CCN(C1F-,CCN(C1Fs-,9,add s at position 7,flow_matching,0.3,2.0,42,204
109,add,0.0,\,,CCN(C1Fs-,\CCN(C1Fs-,10,add \ at position 0,flow_matching,0.3,2.0,42,204
110,replace,0.0,C,\,\CCN(C1Fs-,CCCN(C1Fs-,10,replace \ at position 0 with C,flow_matching,0.3,2.0,42,204
111,remove,4.0,(,,CCCN(C1Fs-,CCCNC1Fs-,9,remove ( from position 4,flow_matching,0.3,2.0,42,204
112,replace,2.0,N,C,CCCNC1Fs-,CCNNC1Fs-,9,replace C at position 2 with N,flow_matching,0.3,2.0,42,204
113,add,6.0,(,,CCNNC1Fs-,CCNNC1(Fs-,10,add ( at position 6,flow_matching,0.3,2.0,42,204
114,remove,9.0,-,,CCNNC1(Fs-,CCNNC1(Fs,9,remove - from position 9,flow_matching,0.3,2.0,42,204
115,add,9.0,(,,CCNNC1(Fs,CCNNC1(Fs(,10,add ( at position 9,flow_matching,0.3,2.0,42,204
116,replace,3.0,(,N,CCNNC1(Fs(,CCN(C1(Fs(,10,replace N at position 3 with (,flow_matching,0.3,2.0,42,204
117,remove,0.0,C,,CCN(C1(Fs(,CN(C1(Fs(,9,remove C from position 0,flow_matching,0.3,2.0,42,204
118,add,1.0,),,CN(C1(Fs(,C)N(C1(Fs(,10,add ) at position 1,flow_matching,0.3,2.0,42,204
119,replace,1.0,C,),C)N(C1(Fs(,CCN(C1(Fs(,10,replace ) at position 1 with C,flow_matching,0.3,2.0,42,204
120,replace,5.0,c,1,CCN(C1(Fs(,CCN(Cc(Fs(,10,replace 1 at position 5 with c,flow_matching,0.3,2.0,42,204
121,replace,1.0,N,C,CCN(Cc(Fs(,CNN(Cc(Fs(,10,replace C at position 1 with N,flow_matching,0.3,2.0,42,204
122,replace,1.0,C,N,CNN(Cc(Fs(,CCN(Cc(Fs(,10,replace N at position 1 with C,flow_matching,0.3,2.0,42,204
123,add,5.0,1,,CCN(Cc(Fs(,CCN(C1c(Fs(,11,add 1 at position 5,flow_matching,0.3,2.0,42,204
124,replace,6.0,(,c,CCN(C1c(Fs(,CCN(C1((Fs(,11,replace c at position 6 with (,flow_matching,0.3,2.0,42,204
125,replace,5.0,c,1,CCN(C1((Fs(,CCN(Cc((Fs(,11,replace 1 at position 5 with c,flow_matching,0.3,2.0,42,204
126,replace,6.0,1,(,CCN(Cc((Fs(,CCN(Cc1(Fs(,11,replace ( at position 6 with 1,flow_matching,0.3,2.0,42,204
127,replace,7.0,c,(,CCN(Cc1(Fs(,CCN(Cc1cFs(,11,replace ( at position 7 with c,flow_matching,0.3,2.0,42,204
128,replace,8.0,c,F,CCN(Cc1cFs(,CCN(Cc1ccs(,11,replace F at position 8 with c,flow_matching,0.3,2.0,42,204
129,replace,9.0,c,s,CCN(Cc1ccs(,CCN(Cc1ccc(,11,replace s at position 9 with c,flow_matching,0.3,2.0,42,204
130,add,2.0,s,,CCN(Cc1ccc(,CCsN(Cc1ccc(,12,add s at position 2,flow_matching,0.3,2.0,42,204
131,replace,2.0,N,s,CCsN(Cc1ccc(,CCNN(Cc1ccc(,12,replace s at position 2 with N,flow_matching,0.3,2.0,42,204
132,replace,3.0,(,N,CCNN(Cc1ccc(,CCN((Cc1ccc(,12,replace N at position 3 with (,flow_matching,0.3,2.0,42,204
133,replace,4.0,C,(,CCN((Cc1ccc(,CCN(CCc1ccc(,12,replace ( at position 4 with C,flow_matching,0.3,2.0,42,204
134,replace,5.0,c,C,CCN(CCc1ccc(,CCN(Ccc1ccc(,12,replace C at position 5 with c,flow_matching,0.3,2.0,42,204
135,remove,1.0,C,,CCN(Ccc1ccc(,CN(Ccc1ccc(,11,remove C from position 1,flow_matching,0.3,2.0,42,204
136,add,7.0,/,,CN(Ccc1ccc(,CN(Ccc1/ccc(,12,add / at position 7,flow_matching,0.3,2.0,42,204
137,add,4.0,n,,CN(Ccc1/ccc(,CN(Cncc1/ccc(,13,add n at position 4,flow_matching,0.3,2.0,42,204
138,replace,1.0,C,N,CN(Cncc1/ccc(,CC(Cncc1/ccc(,13,replace N at position 1 with C,flow_matching,0.3,2.0,42,204
139,replace,11.0,2,c,CC(Cncc1/ccc(,CC(Cncc1/cc2(,13,replace c at position 11 with 2,flow_matching,0.3,2.0,42,204
140,replace,2.0,N,(,CC(Cncc1/cc2(,CCNCncc1/cc2(,13,replace ( at position 2 with N,flow_matching,0.3,2.0,42,204
141,remove,6.0,c,,CCNCncc1/cc2(,CCNCnc1/cc2(,12,remove c from position 6,flow_matching,0.3,2.0,42,204
142,remove,9.0,c,,CCNCnc1/cc2(,CCNCnc1/c2(,11,remove c from position 9,flow_matching,0.3,2.0,42,204
143,replace,3.0,(,C,CCNCnc1/c2(,CCN(nc1/c2(,11,replace C at position 3 with (,flow_matching,0.3,2.0,42,204
144,replace,4.0,C,n,CCN(nc1/c2(,CCN(Cc1/c2(,11,replace n at position 4 with C,flow_matching,0.3,2.0,42,204
145,replace,2.0,n,N,CCN(Cc1/c2(,CCn(Cc1/c2(,11,replace N at position 2 with n,flow_matching,0.3,2.0,42,204
146,replace,2.0,N,n,CCn(Cc1/c2(,CCN(Cc1/c2(,11,replace n at position 2 with N,flow_matching,0.3,2.0,42,204
147,replace,1.0,r,C,CCN(Cc1/c2(,CrN(Cc1/c2(,11,replace C at position 1 with r,flow_matching,0.3,2.0,42,204
148,replace,1.0,C,r,CrN(Cc1/c2(,CCN(Cc1/c2(,11,replace r at position 1 with C,flow_matching,0.3,2.0,42,204
149,remove,6.0,1,,CCN(Cc1/c2(,CCN(Cc/c2(,10,remove 1 from position 6,flow_matching,0.3,2.0,42,204
150,replace,6.0,1,/,CCN(Cc/c2(,CCN(Cc1c2(,10,replace / at position 6 with 1,flow_matching,0.3,2.0,42,204
151,replace,7.0,5,c,CCN(Cc1c2(,CCN(Cc152(,10,replace c at position 7 with 5,flow_matching,0.3,2.0,42,204
152,replace,7.0,c,5,CCN(Cc152(,CCN(Cc1c2(,10,replace 5 at position 7 with c,flow_matching,0.3,2.0,42,204
153,replace,8.0,c,2,CCN(Cc1c2(,CCN(Cc1cc(,10,replace 2 at position 8 with c,flow_matching,0.3,2.0,42,204
154,add,10.0,+,,CCN(Cc1cc(,CCN(Cc1cc(+,11,add + at position 10,flow_matching,0.3,2.0,42,204
155,add,9.0,c,,CCN(Cc1cc(+,CCN(Cc1ccc(+,12,add c at position 9,flow_matching,0.3,2.0,42,204
156,replace,11.0,B,+,CCN(Cc1ccc(+,CCN(Cc1ccc(B,12,replace + at position 11 with B,flow_matching,0.3,2.0,42,204
157,replace,1.0,=,C,CCN(Cc1ccc(B,C=N(Cc1ccc(B,12,replace C at position 1 with =,flow_matching,0.3,2.0,42,204
158,remove,6.0,1,,C=N(Cc1ccc(B,C=N(Ccccc(B,11,remove 1 from position 6,flow_matching,0.3,2.0,42,204
159,replace,1.0,C,=,C=N(Ccccc(B,CCN(Ccccc(B,11,replace = at position 1 with C,flow_matching,0.3,2.0,42,204
160,replace,6.0,1,c,CCN(Ccccc(B,CCN(Cc1cc(B,11,replace c at position 6 with 1,flow_matching,0.3,2.0,42,204
161,remove,1.0,C,,CCN(Cc1cc(B,CN(Cc1cc(B,10,remove C from position 1,flow_matching,0.3,2.0,42,204
162,add,6.0,O,,CN(Cc1cc(B,CN(Cc1Occ(B,11,add O at position 6,flow_matching,0.3,2.0,42,204
163,replace,1.0,C,N,CN(Cc1Occ(B,CC(Cc1Occ(B,11,replace N at position 1 with C,flow_matching,0.3,2.0,42,204
164,remove,9.0,(,,CC(Cc1Occ(B,CC(Cc1OccB,10,remove ( from position 9,flow_matching,0.3,2.0,42,204
165,replace,7.0,l,c,CC(Cc1OccB,CC(Cc1OlcB,10,replace c at position 7 with l,flow_matching,0.3,2.0,42,204
166,add,9.0,[,,CC(Cc1OlcB,CC(Cc1Olc[B,11,add [ at position 9,flow_matching,0.3,2.0,42,204
167,add,0.0,H,,CC(Cc1Olc[B,HCC(Cc1Olc[B,12,add H at position 0,flow_matching,0.3,2.0,42,204
168,add,11.0,/,,HCC(Cc1Olc[B,HCC(Cc1Olc[/B,13,add / at position 11,flow_matching,0.3,2.0,42,204
169,replace,0.0,C,H,HCC(Cc1Olc[/B,CCC(Cc1Olc[/B,13,replace H at position 0 with C,flow_matching,0.3,2.0,42,204
170,replace,2.0,N,C,CCC(Cc1Olc[/B,CCN(Cc1Olc[/B,13,replace C at position 2 with N,flow_matching,0.3,2.0,42,204
171,replace,7.0,c,O,CCN(Cc1Olc[/B,CCN(Cc1clc[/B,13,replace O at position 7 with c,flow_matching,0.3,2.0,42,204
172,replace,8.0,c,l,CCN(Cc1clc[/B,CCN(Cc1ccc[/B,13,replace l at position 8 with c,flow_matching,0.3,2.0,42,204
173,replace,10.0,(,[,CCN(Cc1ccc[/B,CCN(Cc1ccc(/B,13,replace [ at position 10 with (,flow_matching,0.3,2.0,42,204
174,replace,11.0,B,/,CCN(Cc1ccc(/B,CCN(Cc1ccc(BB,13,replace / at position 11 with B,flow_matching,0.3,2.0,42,204
175,replace,12.0,r,B,CCN(Cc1ccc(BB,CCN(Cc1ccc(Br,13,replace B at position 12 with r,flow_matching,0.3,2.0,42,204
176,add,13.0,),,CCN(Cc1ccc(Br,CCN(Cc1ccc(Br),14,add ) at position 13,flow_matching,0.3,2.0,42,204
177,add,14.0,s,,CCN(Cc1ccc(Br),CCN(Cc1ccc(Br)s,15,add s at position 14,flow_matching,0.3,2.0,42,204
178,add,15.0,1,,CCN(Cc1ccc(Br)s,CCN(Cc1ccc(Br)s1,16,add 1 at position 15,flow_matching,0.3,2.0,42,204
179,add,16.0,),,CCN(Cc1ccc(Br)s1,CCN(Cc1ccc(Br)s1),17,add ) at position 16,flow_matching,0.3,2.0,42,204
180,add,17.0,C,,CCN(Cc1ccc(Br)s1),CCN(Cc1ccc(Br)s1)C,18,add C at position 17,flow_matching,0.3,2.0,42,204
181,add,18.0,(,,CCN(Cc1ccc(Br)s1)C,CCN(Cc1ccc(Br)s1)C(,19,add ( at position 18,flow_matching,0.3,2.0,42,204
182,add,19.0,=,,CCN(Cc1ccc(Br)s1)C(,CCN(Cc1ccc(Br)s1)C(=,20,add = at position 19,flow_matching,0.3,2.0,42,204
183,add,20.0,O,,CCN(Cc1ccc(Br)s1)C(=,CCN(Cc1ccc(Br)s1)C(=O,21,add O at position 20,flow_matching,0.3,2.0,42,204
184,add,21.0,),,CCN(Cc1ccc(Br)s1)C(=O,CCN(Cc1ccc(Br)s1)C(=O),22,add ) at position 21,flow_matching,0.3,2.0,42,204
185,add,22.0,C,,CCN(Cc1ccc(Br)s1)C(=O),CCN(Cc1ccc(Br)s1)C(=O)C,23,add C at position 22,flow_matching,0.3,2.0,42,204
186,add,23.0,[,,CCN(Cc1ccc(Br)s1)C(=O)C,CCN(Cc1ccc(Br)s1)C(=O)C[,24,add [ at position 23,flow_matching,0.3,2.0,42,204
187,add,24.0,N,,CCN(Cc1ccc(Br)s1)C(=O)C[,CCN(Cc1ccc(Br)s1)C(=O)C[N,25,add N at position 24,flow_matching,0.3,2.0,42,204
188,add,25.0,H,,CCN(Cc1ccc(Br)s1)C(=O)C[N,CCN(Cc1ccc(Br)s1)C(=O)C[NH,26,add H at position 25,flow_matching,0.3,2.0,42,204
189,add,26.0,+,,CCN(Cc1ccc(Br)s1)C(=O)C[NH,CCN(Cc1ccc(Br)s1)C(=O)C[NH+,27,add + at position 26,flow_matching,0.3,2.0,42,204
190,add,27.0,],,CCN(Cc1ccc(Br)s1)C(=O)C[NH+,CCN(Cc1ccc(Br)s1)C(=O)C[NH+],28,add ] at position 27,flow_matching,0.3,2.0,42,204
191,add,28.0,(,,CCN(Cc1ccc(Br)s1)C(=O)C[NH+],CCN(Cc1ccc(Br)s1)C(=O)C[NH+](,29,add ( at position 28,flow_matching,0.3,2.0,42,204
192,add,29.0,C,,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C,30,add C at position 29,flow_matching,0.3,2.0,42,204
193,add,30.0,),,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C),31,add ) at position 30,flow_matching,0.3,2.0,42,204
194,add,31.0,C,,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C),CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)C,32,add C at position 31,flow_matching,0.3,2.0,42,204
195,add,32.0,C,,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)C,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC,33,add C at position 32,flow_matching,0.3,2.0,42,204
196,add,33.0,(,,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(,34,add ( at position 33,flow_matching,0.3,2.0,42,204
197,add,34.0,=,,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=,35,add = at position 34,flow_matching,0.3,2.0,42,204
198,add,35.0,O,,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O,36,add O at position 35,flow_matching,0.3,2.0,42,204
199,add,36.0,),,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O),37,add ) at position 36,flow_matching,0.3,2.0,42,204
200,add,37.0,[,,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O),CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O)[,38,add [ at position 37,flow_matching,0.3,2.0,42,204
201,add,38.0,O,,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O)[,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O)[O,39,add O at position 38,flow_matching,0.3,2.0,42,204
202,add,39.0,-,,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O)[O,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O)[O-,40,add - at position 39,flow_matching,0.3,2.0,42,204
203,add,40.0,],,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O)[O-,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O)[O-],41,add ] at position 40,flow_matching,0.3,2.0,42,204
204,add,41.0,"
",,CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O)[O-],"CCN(Cc1ccc(Br)s1)C(=O)C[NH+](C)CC(=O)[O-]
",42,"add 
 at position 41",flow_matching,0.3,2.0,42,204
