step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,52,171
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,52,171
2,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,52,171
3,remove,0.0,C,,C[,[,1,remove C from position 0,flow_matching,0.3,2.0,52,171
4,add,1.0,n,,[,[n,2,add n at position 1,flow_matching,0.3,2.0,52,171
5,replace,0.0,C,[,[n,Cn,2,replace [ at position 0 with C,flow_matching,0.3,2.0,52,171
6,replace,1.0,[,n,Cn,C[,2,replace n at position 1 with [,flow_matching,0.3,2.0,52,171
7,replace,0.0,-,C,C[,-[,2,replace C at position 0 with -,flow_matching,0.3,2.0,52,171
8,replace,0.0,C,-,-[,C[,2,replace - at position 0 with C,flow_matching,0.3,2.0,52,171
9,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,52,171
10,remove,0.0,C,,C[C,[C,2,remove C from position 0,flow_matching,0.3,2.0,52,171
11,remove,1.0,C,,[C,[,1,remove C from position 1,flow_matching,0.3,2.0,52,171
12,replace,0.0,=,[,[,=,1,replace [ at position 0 with =,flow_matching,0.3,2.0,52,171
13,replace,0.0,C,=,=,C,1,replace = at position 0 with C,flow_matching,0.3,2.0,52,171
14,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,52,171
15,add,0.0,H,,,H,1,add H at position 0,flow_matching,0.3,2.0,52,171
16,replace,0.0,C,H,H,C,1,replace H at position 0 with C,flow_matching,0.3,2.0,52,171
17,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,52,171
18,replace,0.0,4,C,C[,4[,2,replace C at position 0 with 4,flow_matching,0.3,2.0,52,171
19,replace,0.0,C,4,4[,C[,2,replace 4 at position 0 with C,flow_matching,0.3,2.0,52,171
20,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,52,171
21,add,2.0,o,,C[C,C[oC,4,add o at position 2,flow_matching,0.3,2.0,52,171
22,add,0.0,s,,C[oC,sC[oC,5,add s at position 0,flow_matching,0.3,2.0,52,171
23,replace,2.0,6,[,sC[oC,sC6oC,5,replace [ at position 2 with 6,flow_matching,0.3,2.0,52,171
24,remove,3.0,o,,sC6oC,sC6C,4,remove o from position 3,flow_matching,0.3,2.0,52,171
25,replace,0.0,C,s,sC6C,CC6C,4,replace s at position 0 with C,flow_matching,0.3,2.0,52,171
26,remove,2.0,6,,CC6C,CCC,3,remove 6 from position 2,flow_matching,0.3,2.0,52,171
27,add,1.0,#,,CCC,C#CC,4,add # at position 1,flow_matching,0.3,2.0,52,171
28,replace,1.0,[,#,C#CC,C[CC,4,replace # at position 1 with [,flow_matching,0.3,2.0,52,171
29,remove,3.0,C,,C[CC,C[C,3,remove C from position 3,flow_matching,0.3,2.0,52,171
30,add,2.0,o,,C[C,C[oC,4,add o at position 2,flow_matching,0.3,2.0,52,171
31,replace,2.0,C,o,C[oC,C[CC,4,replace o at position 2 with C,flow_matching,0.3,2.0,52,171
32,add,2.0,5,,C[CC,C[5CC,5,add 5 at position 2,flow_matching,0.3,2.0,52,171
33,add,5.0,6,,C[5CC,C[5CC6,6,add 6 at position 5,flow_matching,0.3,2.0,52,171
34,add,5.0,+,,C[5CC6,C[5CC+6,7,add + at position 5,flow_matching,0.3,2.0,52,171
35,remove,1.0,[,,C[5CC+6,C5CC+6,6,remove [ from position 1,flow_matching,0.3,2.0,52,171
36,add,2.0,O,,C5CC+6,C5OCC+6,7,add O at position 2,flow_matching,0.3,2.0,52,171
37,replace,2.0,),O,C5OCC+6,C5)CC+6,7,replace O at position 2 with ),flow_matching,0.3,2.0,52,171
38,add,7.0,),,C5)CC+6,C5)CC+6),8,add ) at position 7,flow_matching,0.3,2.0,52,171
39,add,8.0,4,,C5)CC+6),C5)CC+6)4,9,add 4 at position 8,flow_matching,0.3,2.0,52,171
40,add,9.0,S,,C5)CC+6)4,C5)CC+6)4S,10,add S at position 9,flow_matching,0.3,2.0,52,171
41,replace,4.0,\,C,C5)CC+6)4S,C5)C\+6)4S,10,replace C at position 4 with \,flow_matching,0.3,2.0,52,171
42,replace,1.0,[,5,C5)C\+6)4S,C[)C\+6)4S,10,replace 5 at position 1 with [,flow_matching,0.3,2.0,52,171
43,remove,0.0,C,,C[)C\+6)4S,[)C\+6)4S,9,remove C from position 0,flow_matching,0.3,2.0,52,171
44,remove,2.0,C,,[)C\+6)4S,[)\+6)4S,8,remove C from position 2,flow_matching,0.3,2.0,52,171
45,replace,4.0,n,6,[)\+6)4S,[)\+n)4S,8,replace 6 at position 4 with n,flow_matching,0.3,2.0,52,171
46,replace,0.0,C,[,[)\+n)4S,C)\+n)4S,8,replace [ at position 0 with C,flow_matching,0.3,2.0,52,171
47,replace,1.0,[,),C)\+n)4S,C[\+n)4S,8,replace ) at position 1 with [,flow_matching,0.3,2.0,52,171
48,replace,7.0,=,S,C[\+n)4S,C[\+n)4=,8,replace S at position 7 with =,flow_matching,0.3,2.0,52,171
49,add,2.0,3,,C[\+n)4=,C[3\+n)4=,9,add 3 at position 2,flow_matching,0.3,2.0,52,171
50,remove,6.0,),,C[3\+n)4=,C[3\+n4=,8,remove ) from position 6,flow_matching,0.3,2.0,52,171
51,add,5.0,O,,C[3\+n4=,C[3\+On4=,9,add O at position 5,flow_matching,0.3,2.0,52,171
52,add,9.0,/,,C[3\+On4=,C[3\+On4=/,10,add / at position 9,flow_matching,0.3,2.0,52,171
53,replace,4.0,\,+,C[3\+On4=/,C[3\\On4=/,10,replace + at position 4 with \,flow_matching,0.3,2.0,52,171
54,replace,2.0,C,3,C[3\\On4=/,C[C\\On4=/,10,replace 3 at position 2 with C,flow_matching,0.3,2.0,52,171
55,replace,5.0,6,O,C[C\\On4=/,C[C\\6n4=/,10,replace O at position 5 with 6,flow_matching,0.3,2.0,52,171
56,add,4.0,4,,C[C\\6n4=/,C[C\4\6n4=/,11,add 4 at position 4,flow_matching,0.3,2.0,52,171
57,replace,3.0,@,\,C[C\4\6n4=/,C[C@4\6n4=/,11,replace \ at position 3 with @,flow_matching,0.3,2.0,52,171
58,replace,4.0,@,4,C[C@4\6n4=/,C[C@@\6n4=/,11,replace 4 at position 4 with @,flow_matching,0.3,2.0,52,171
59,replace,5.0,H,\,C[C@@\6n4=/,C[C@@H6n4=/,11,replace \ at position 5 with H,flow_matching,0.3,2.0,52,171
60,replace,10.0,6,/,C[C@@H6n4=/,C[C@@H6n4=6,11,replace / at position 10 with 6,flow_matching,0.3,2.0,52,171
61,replace,5.0,-,H,C[C@@H6n4=6,C[C@@-6n4=6,11,replace H at position 5 with -,flow_matching,0.3,2.0,52,171
62,add,7.0,3,,C[C@@-6n4=6,C[C@@-63n4=6,12,add 3 at position 7,flow_matching,0.3,2.0,52,171
63,add,10.0,r,,C[C@@-63n4=6,C[C@@-63n4r=6,13,add r at position 10,flow_matching,0.3,2.0,52,171
64,replace,7.0,4,3,C[C@@-63n4r=6,C[C@@-64n4r=6,13,replace 3 at position 7 with 4,flow_matching,0.3,2.0,52,171
65,remove,0.0,C,,C[C@@-64n4r=6,[C@@-64n4r=6,12,remove C from position 0,flow_matching,0.3,2.0,52,171
66,replace,0.0,C,[,[C@@-64n4r=6,CC@@-64n4r=6,12,replace [ at position 0 with C,flow_matching,0.3,2.0,52,171
67,replace,11.0,],6,CC@@-64n4r=6,CC@@-64n4r=],12,replace 6 at position 11 with ],flow_matching,0.3,2.0,52,171
68,add,5.0,-,,CC@@-64n4r=],CC@@--64n4r=],13,add - at position 5,flow_matching,0.3,2.0,52,171
69,replace,1.0,[,C,CC@@--64n4r=],C[@@--64n4r=],13,replace C at position 1 with [,flow_matching,0.3,2.0,52,171
70,replace,2.0,C,@,C[@@--64n4r=],C[C@--64n4r=],13,replace @ at position 2 with C,flow_matching,0.3,2.0,52,171
71,replace,7.0,C,4,C[C@--64n4r=],C[C@--6Cn4r=],13,replace 4 at position 7 with C,flow_matching,0.3,2.0,52,171
72,replace,10.0,4,r,C[C@--6Cn4r=],C[C@--6Cn44=],13,replace r at position 10 with 4,flow_matching,0.3,2.0,52,171
73,replace,11.0,4,=,C[C@--6Cn44=],C[C@--6Cn444],13,replace = at position 11 with 4,flow_matching,0.3,2.0,52,171
74,remove,6.0,6,,C[C@--6Cn444],C[C@--Cn444],12,remove 6 from position 6,flow_matching,0.3,2.0,52,171
75,replace,4.0,@,-,C[C@--Cn444],C[C@@-Cn444],12,replace - at position 4 with @,flow_matching,0.3,2.0,52,171
76,replace,5.0,H,-,C[C@@-Cn444],C[C@@HCn444],12,replace - at position 5 with H,flow_matching,0.3,2.0,52,171
77,remove,1.0,[,,C[C@@HCn444],CC@@HCn444],11,remove [ from position 1,flow_matching,0.3,2.0,52,171
78,replace,3.0,-,@,CC@@HCn444],CC@-HCn444],11,replace @ at position 3 with -,flow_matching,0.3,2.0,52,171
79,remove,1.0,C,,CC@-HCn444],C@-HCn444],10,remove C from position 1,flow_matching,0.3,2.0,52,171
80,replace,2.0,F,-,C@-HCn444],C@FHCn444],10,replace - at position 2 with F,flow_matching,0.3,2.0,52,171
81,remove,7.0,4,,C@FHCn444],C@FHCn44],9,remove 4 from position 7,flow_matching,0.3,2.0,52,171
82,replace,3.0,4,H,C@FHCn44],C@F4Cn44],9,replace H at position 3 with 4,flow_matching,0.3,2.0,52,171
83,replace,1.0,[,@,C@F4Cn44],C[F4Cn44],9,replace @ at position 1 with [,flow_matching,0.3,2.0,52,171
84,add,7.0,5,,C[F4Cn44],C[F4Cn454],10,add 5 at position 7,flow_matching,0.3,2.0,52,171
85,remove,0.0,C,,C[F4Cn454],[F4Cn454],9,remove C from position 0,flow_matching,0.3,2.0,52,171
86,replace,1.0,-,F,[F4Cn454],[-4Cn454],9,replace F at position 1 with -,flow_matching,0.3,2.0,52,171
87,remove,7.0,4,,[-4Cn454],[-4Cn45],8,remove 4 from position 7,flow_matching,0.3,2.0,52,171
88,remove,7.0,],,[-4Cn45],[-4Cn45,7,remove ] from position 7,flow_matching,0.3,2.0,52,171
89,remove,2.0,4,,[-4Cn45,[-Cn45,6,remove 4 from position 2,flow_matching,0.3,2.0,52,171
90,replace,2.0,2,C,[-Cn45,[-2n45,6,replace C at position 2 with 2,flow_matching,0.3,2.0,52,171
91,replace,3.0,\,n,[-2n45,[-2\45,6,replace n at position 3 with \,flow_matching,0.3,2.0,52,171
92,replace,0.0,C,[,[-2\45,C-2\45,6,replace [ at position 0 with C,flow_matching,0.3,2.0,52,171
93,remove,0.0,C,,C-2\45,-2\45,5,remove C from position 0,flow_matching,0.3,2.0,52,171
94,replace,1.0,5,2,-2\45,-5\45,5,replace 2 at position 1 with 5,flow_matching,0.3,2.0,52,171
95,replace,2.0,+,\,-5\45,-5+45,5,replace \ at position 2 with +,flow_matching,0.3,2.0,52,171
96,replace,0.0,C,-,-5+45,C5+45,5,replace - at position 0 with C,flow_matching,0.3,2.0,52,171
97,add,4.0,3,,C5+45,C5+435,6,add 3 at position 4,flow_matching,0.3,2.0,52,171
98,add,3.0,o,,C5+435,C5+o435,7,add o at position 3,flow_matching,0.3,2.0,52,171
99,add,3.0,(,,C5+o435,C5+(o435,8,add ( at position 3,flow_matching,0.3,2.0,52,171
100,add,0.0,S,,C5+(o435,SC5+(o435,9,add S at position 0,flow_matching,0.3,2.0,52,171
101,replace,0.0,C,S,SC5+(o435,CC5+(o435,9,replace S at position 0 with C,flow_matching,0.3,2.0,52,171
102,replace,1.0,[,C,CC5+(o435,C[5+(o435,9,replace C at position 1 with [,flow_matching,0.3,2.0,52,171
103,replace,2.0,),5,C[5+(o435,C[)+(o435,9,replace 5 at position 2 with ),flow_matching,0.3,2.0,52,171
104,add,8.0,5,,C[)+(o435,C[)+(o4355,10,add 5 at position 8,flow_matching,0.3,2.0,52,171
105,replace,2.0,C,),C[)+(o4355,C[C+(o4355,10,replace ) at position 2 with C,flow_matching,0.3,2.0,52,171
106,replace,3.0,@,+,C[C+(o4355,C[C@(o4355,10,replace + at position 3 with @,flow_matching,0.3,2.0,52,171
107,replace,4.0,@,(,C[C@(o4355,C[C@@o4355,10,replace ( at position 4 with @,flow_matching,0.3,2.0,52,171
108,replace,5.0,H,o,C[C@@o4355,C[C@@H4355,10,replace o at position 5 with H,flow_matching,0.3,2.0,52,171
109,replace,1.0,+,[,C[C@@H4355,C+C@@H4355,10,replace [ at position 1 with +,flow_matching,0.3,2.0,52,171
110,replace,1.0,[,+,C+C@@H4355,C[C@@H4355,10,replace + at position 1 with [,flow_matching,0.3,2.0,52,171
111,replace,3.0,=,@,C[C@@H4355,C[C=@H4355,10,replace @ at position 3 with =,flow_matching,0.3,2.0,52,171
112,replace,3.0,@,=,C[C=@H4355,C[C@@H4355,10,replace = at position 3 with @,flow_matching,0.3,2.0,52,171
113,add,0.0,H,,C[C@@H4355,HC[C@@H4355,11,add H at position 0,flow_matching,0.3,2.0,52,171
114,remove,6.0,H,,HC[C@@H4355,HC[C@@4355,10,remove H from position 6,flow_matching,0.3,2.0,52,171
115,add,7.0,[,,HC[C@@4355,HC[C@@4[355,11,add [ at position 7,flow_matching,0.3,2.0,52,171
116,remove,6.0,4,,HC[C@@4[355,HC[C@@[355,10,remove 4 from position 6,flow_matching,0.3,2.0,52,171
117,replace,0.0,C,H,HC[C@@[355,CC[C@@[355,10,replace H at position 0 with C,flow_matching,0.3,2.0,52,171
118,remove,6.0,[,,CC[C@@[355,CC[C@@355,9,remove [ from position 6,flow_matching,0.3,2.0,52,171
119,add,0.0,3,,CC[C@@355,3CC[C@@355,10,add 3 at position 0,flow_matching,0.3,2.0,52,171
120,add,6.0,6,,3CC[C@@355,3CC[C@6@355,11,add 6 at position 6,flow_matching,0.3,2.0,52,171
121,replace,0.0,C,3,3CC[C@6@355,CCC[C@6@355,11,replace 3 at position 0 with C,flow_matching,0.3,2.0,52,171
122,replace,1.0,[,C,CCC[C@6@355,C[C[C@6@355,11,replace C at position 1 with [,flow_matching,0.3,2.0,52,171
123,replace,3.0,@,[,C[C[C@6@355,C[C@C@6@355,11,replace [ at position 3 with @,flow_matching,0.3,2.0,52,171
124,replace,4.0,@,C,C[C@C@6@355,C[C@@@6@355,11,replace C at position 4 with @,flow_matching,0.3,2.0,52,171
125,replace,5.0,H,@,C[C@@@6@355,C[C@@H6@355,11,replace @ at position 5 with H,flow_matching,0.3,2.0,52,171
126,replace,6.0,],6,C[C@@H6@355,C[C@@H]@355,11,replace 6 at position 6 with ],flow_matching,0.3,2.0,52,171
127,replace,7.0,(,@,C[C@@H]@355,C[C@@H](355,11,replace @ at position 7 with (,flow_matching,0.3,2.0,52,171
128,replace,8.0,S,3,C[C@@H](355,C[C@@H](S55,11,replace 3 at position 8 with S,flow_matching,0.3,2.0,52,171
129,replace,9.0,c,5,C[C@@H](S55,C[C@@H](Sc5,11,replace 5 at position 9 with c,flow_matching,0.3,2.0,52,171
130,replace,10.0,1,5,C[C@@H](Sc5,C[C@@H](Sc1,11,replace 5 at position 10 with 1,flow_matching,0.3,2.0,52,171
131,add,11.0,c,,C[C@@H](Sc1,C[C@@H](Sc1c,12,add c at position 11,flow_matching,0.3,2.0,52,171
132,add,12.0,c,,C[C@@H](Sc1c,C[C@@H](Sc1cc,13,add c at position 12,flow_matching,0.3,2.0,52,171
133,add,13.0,(,,C[C@@H](Sc1cc,C[C@@H](Sc1cc(,14,add ( at position 13,flow_matching,0.3,2.0,52,171
134,add,14.0,C,,C[C@@H](Sc1cc(,C[C@@H](Sc1cc(C,15,add C at position 14,flow_matching,0.3,2.0,52,171
135,add,15.0,l,,C[C@@H](Sc1cc(C,C[C@@H](Sc1cc(Cl,16,add l at position 15,flow_matching,0.3,2.0,52,171
136,add,16.0,),,C[C@@H](Sc1cc(Cl,C[C@@H](Sc1cc(Cl),17,add ) at position 16,flow_matching,0.3,2.0,52,171
137,add,17.0,c,,C[C@@H](Sc1cc(Cl),C[C@@H](Sc1cc(Cl)c,18,add c at position 17,flow_matching,0.3,2.0,52,171
138,add,18.0,c,,C[C@@H](Sc1cc(Cl)c,C[C@@H](Sc1cc(Cl)cc,19,add c at position 18,flow_matching,0.3,2.0,52,171
139,add,19.0,c,,C[C@@H](Sc1cc(Cl)cc,C[C@@H](Sc1cc(Cl)ccc,20,add c at position 19,flow_matching,0.3,2.0,52,171
140,add,20.0,1,,C[C@@H](Sc1cc(Cl)ccc,C[C@@H](Sc1cc(Cl)ccc1,21,add 1 at position 20,flow_matching,0.3,2.0,52,171
141,add,21.0,C,,C[C@@H](Sc1cc(Cl)ccc1,C[C@@H](Sc1cc(Cl)ccc1C,22,add C at position 21,flow_matching,0.3,2.0,52,171
142,add,22.0,l,,C[C@@H](Sc1cc(Cl)ccc1C,C[C@@H](Sc1cc(Cl)ccc1Cl,23,add l at position 22,flow_matching,0.3,2.0,52,171
143,add,23.0,),,C[C@@H](Sc1cc(Cl)ccc1Cl,C[C@@H](Sc1cc(Cl)ccc1Cl),24,add ) at position 23,flow_matching,0.3,2.0,52,171
144,add,24.0,C,,C[C@@H](Sc1cc(Cl)ccc1Cl),C[C@@H](Sc1cc(Cl)ccc1Cl)C,25,add C at position 24,flow_matching,0.3,2.0,52,171
145,add,25.0,(,,C[C@@H](Sc1cc(Cl)ccc1Cl)C,C[C@@H](Sc1cc(Cl)ccc1Cl)C(,26,add ( at position 25,flow_matching,0.3,2.0,52,171
146,add,26.0,=,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=,27,add = at position 26,flow_matching,0.3,2.0,52,171
147,add,27.0,O,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O,28,add O at position 27,flow_matching,0.3,2.0,52,171
148,add,28.0,),,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O),29,add ) at position 28,flow_matching,0.3,2.0,52,171
149,add,29.0,N,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O),C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N,30,add N at position 29,flow_matching,0.3,2.0,52,171
150,add,30.0,1,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1,31,add 1 at position 30,flow_matching,0.3,2.0,52,171
151,add,31.0,C,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1C,32,add C at position 31,flow_matching,0.3,2.0,52,171
152,add,32.0,C,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1C,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CC,33,add C at position 32,flow_matching,0.3,2.0,52,171
153,add,33.0,C,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CC,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC,34,add C at position 33,flow_matching,0.3,2.0,52,171
154,add,34.0,[,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[,35,add [ at position 34,flow_matching,0.3,2.0,52,171
155,add,35.0,C,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C,36,add C at position 35,flow_matching,0.3,2.0,52,171
156,add,36.0,@,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@,37,add @ at position 36,flow_matching,0.3,2.0,52,171
157,add,37.0,H,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H,38,add H at position 37,flow_matching,0.3,2.0,52,171
158,add,38.0,],,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H],39,add ] at position 38,flow_matching,0.3,2.0,52,171
159,add,39.0,(,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H],C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](,40,add ( at position 39,flow_matching,0.3,2.0,52,171
160,add,40.0,C,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](C,41,add C at position 40,flow_matching,0.3,2.0,52,171
161,add,41.0,C,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](C,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CC,42,add C at position 41,flow_matching,0.3,2.0,52,171
162,add,42.0,C,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CC,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC,43,add C at position 42,flow_matching,0.3,2.0,52,171
163,add,43.0,(,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(,44,add ( at position 43,flow_matching,0.3,2.0,52,171
164,add,44.0,N,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N,45,add N at position 44,flow_matching,0.3,2.0,52,171
165,add,45.0,),,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N),46,add ) at position 45,flow_matching,0.3,2.0,52,171
166,add,46.0,=,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N),C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=,47,add = at position 46,flow_matching,0.3,2.0,52,171
167,add,47.0,O,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=O,48,add O at position 47,flow_matching,0.3,2.0,52,171
168,add,48.0,),,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=O,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=O),49,add ) at position 48,flow_matching,0.3,2.0,52,171
169,add,49.0,C,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=O),C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=O)C,50,add C at position 49,flow_matching,0.3,2.0,52,171
170,add,50.0,1,,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=O)C,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=O)C1,51,add 1 at position 50,flow_matching,0.3,2.0,52,171
171,add,51.0,"
",,C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=O)C1,"C[C@@H](Sc1cc(Cl)ccc1Cl)C(=O)N1CCC[C@H](CCC(N)=O)C1
",52,"add 
 at position 51",flow_matching,0.3,2.0,52,171
