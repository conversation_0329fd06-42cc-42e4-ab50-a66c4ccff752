step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,50,244
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,50,244
2,add,0.0,l,,O,lO,2,add l at position 0,flow_matching,0.3,2.0,50,244
3,replace,0.0,O,l,lO,OO,2,replace l at position 0 with O,flow_matching,0.3,2.0,50,244
4,replace,1.0,=,O,OO,O=,2,replace O at position 1 with =,flow_matching,0.3,2.0,50,244
5,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,50,244
6,replace,0.0,O,=,=,O,1,replace = at position 0 with O,flow_matching,0.3,2.0,50,244
7,add,0.0,S,,O,SO,2,add S at position 0,flow_matching,0.3,2.0,50,244
8,add,2.0,3,,SO,SO3,3,add 3 at position 2,flow_matching,0.3,2.0,50,244
9,remove,2.0,3,,SO3,SO,2,remove 3 from position 2,flow_matching,0.3,2.0,50,244
10,add,0.0,5,,SO,5SO,3,add 5 at position 0,flow_matching,0.3,2.0,50,244
11,remove,0.0,5,,5SO,SO,2,remove 5 from position 0,flow_matching,0.3,2.0,50,244
12,remove,0.0,S,,SO,O,1,remove S from position 0,flow_matching,0.3,2.0,50,244
13,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,50,244
14,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,50,244
15,replace,0.0,=,F,F,=,1,replace F at position 0 with =,flow_matching,0.3,2.0,50,244
16,replace,0.0,s,=,=,s,1,replace = at position 0 with s,flow_matching,0.3,2.0,50,244
17,replace,0.0,O,s,s,O,1,replace s at position 0 with O,flow_matching,0.3,2.0,50,244
18,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,50,244
19,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,50,244
20,remove,0.0,=,,=,,0,remove = from position 0,flow_matching,0.3,2.0,50,244
21,add,0.0,l,,,l,1,add l at position 0,flow_matching,0.3,2.0,50,244
22,add,1.0,o,,l,lo,2,add o at position 1,flow_matching,0.3,2.0,50,244
23,add,2.0,7,,lo,lo7,3,add 7 at position 2,flow_matching,0.3,2.0,50,244
24,remove,1.0,o,,lo7,l7,2,remove o from position 1,flow_matching,0.3,2.0,50,244
25,add,2.0,(,,l7,l7(,3,add ( at position 2,flow_matching,0.3,2.0,50,244
26,remove,0.0,l,,l7(,7(,2,remove l from position 0,flow_matching,0.3,2.0,50,244
27,replace,0.0,l,7,7(,l(,2,replace 7 at position 0 with l,flow_matching,0.3,2.0,50,244
28,add,2.0,r,,l(,l(r,3,add r at position 2,flow_matching,0.3,2.0,50,244
29,replace,2.0,O,r,l(r,l(O,3,replace r at position 2 with O,flow_matching,0.3,2.0,50,244
30,add,0.0,@,,l(O,@l(O,4,add @ at position 0,flow_matching,0.3,2.0,50,244
31,replace,3.0,3,O,@l(O,@l(3,4,replace O at position 3 with 3,flow_matching,0.3,2.0,50,244
32,replace,0.0,O,@,@l(3,Ol(3,4,replace @ at position 0 with O,flow_matching,0.3,2.0,50,244
33,add,1.0,6,,Ol(3,O6l(3,5,add 6 at position 1,flow_matching,0.3,2.0,50,244
34,add,3.0,=,,O6l(3,O6l=(3,6,add = at position 3,flow_matching,0.3,2.0,50,244
35,replace,2.0,),l,O6l=(3,O6)=(3,6,replace l at position 2 with ),flow_matching,0.3,2.0,50,244
36,add,1.0,),,O6)=(3,O)6)=(3,7,add ) at position 1,flow_matching,0.3,2.0,50,244
37,replace,1.0,=,),O)6)=(3,O=6)=(3,7,replace ) at position 1 with =,flow_matching,0.3,2.0,50,244
38,add,5.0,B,,O=6)=(3,O=6)=B(3,8,add B at position 5,flow_matching,0.3,2.0,50,244
39,remove,5.0,B,,O=6)=B(3,O=6)=(3,7,remove B from position 5,flow_matching,0.3,2.0,50,244
40,add,6.0,F,,O=6)=(3,O=6)=(F3,8,add F at position 6,flow_matching,0.3,2.0,50,244
41,add,2.0,/,,O=6)=(F3,O=/6)=(F3,9,add / at position 2,flow_matching,0.3,2.0,50,244
42,replace,7.0,c,F,O=/6)=(F3,O=/6)=(c3,9,replace F at position 7 with c,flow_matching,0.3,2.0,50,244
43,add,2.0,I,,O=/6)=(c3,O=I/6)=(c3,10,add I at position 2,flow_matching,0.3,2.0,50,244
44,replace,9.0,s,3,O=I/6)=(c3,O=I/6)=(cs,10,replace 3 at position 9 with s,flow_matching,0.3,2.0,50,244
45,add,7.0,S,,O=I/6)=(cs,O=I/6)=S(cs,11,add S at position 7,flow_matching,0.3,2.0,50,244
46,add,1.0,6,,O=I/6)=S(cs,O6=I/6)=S(cs,12,add 6 at position 1,flow_matching,0.3,2.0,50,244
47,remove,5.0,6,,O6=I/6)=S(cs,O6=I/)=S(cs,11,remove 6 from position 5,flow_matching,0.3,2.0,50,244
48,replace,1.0,=,6,O6=I/)=S(cs,O==I/)=S(cs,11,replace 6 at position 1 with =,flow_matching,0.3,2.0,50,244
49,replace,2.0,C,=,O==I/)=S(cs,O=CI/)=S(cs,11,replace = at position 2 with C,flow_matching,0.3,2.0,50,244
50,replace,5.0,6,),O=CI/)=S(cs,O=CI/6=S(cs,11,replace ) at position 5 with 6,flow_matching,0.3,2.0,50,244
51,add,5.0,/,,O=CI/6=S(cs,O=CI//6=S(cs,12,add / at position 5,flow_matching,0.3,2.0,50,244
52,add,0.0,r,,O=CI//6=S(cs,rO=CI//6=S(cs,13,add r at position 0,flow_matching,0.3,2.0,50,244
53,add,13.0,o,,rO=CI//6=S(cs,rO=CI//6=S(cso,14,add o at position 13,flow_matching,0.3,2.0,50,244
54,replace,0.0,O,r,rO=CI//6=S(cso,OO=CI//6=S(cso,14,replace r at position 0 with O,flow_matching,0.3,2.0,50,244
55,replace,3.0,@,C,OO=CI//6=S(cso,OO=@I//6=S(cso,14,replace C at position 3 with @,flow_matching,0.3,2.0,50,244
56,add,10.0,],,OO=@I//6=S(cso,OO=@I//6=S](cso,15,add ] at position 10,flow_matching,0.3,2.0,50,244
57,replace,1.0,),O,OO=@I//6=S](cso,O)=@I//6=S](cso,15,replace O at position 1 with ),flow_matching,0.3,2.0,50,244
58,replace,3.0,-,@,O)=@I//6=S](cso,O)=-I//6=S](cso,15,replace @ at position 3 with -,flow_matching,0.3,2.0,50,244
59,replace,2.0,5,=,O)=-I//6=S](cso,O)5-I//6=S](cso,15,replace = at position 2 with 5,flow_matching,0.3,2.0,50,244
60,replace,13.0,3,s,O)5-I//6=S](cso,O)5-I//6=S](c3o,15,replace s at position 13 with 3,flow_matching,0.3,2.0,50,244
61,replace,2.0,B,5,O)5-I//6=S](c3o,O)B-I//6=S](c3o,15,replace 5 at position 2 with B,flow_matching,0.3,2.0,50,244
62,replace,1.0,=,),O)B-I//6=S](c3o,O=B-I//6=S](c3o,15,replace ) at position 1 with =,flow_matching,0.3,2.0,50,244
63,replace,2.0,C,B,O=B-I//6=S](c3o,O=C-I//6=S](c3o,15,replace B at position 2 with C,flow_matching,0.3,2.0,50,244
64,remove,14.0,o,,O=C-I//6=S](c3o,O=C-I//6=S](c3,14,remove o from position 14,flow_matching,0.3,2.0,50,244
65,remove,12.0,c,,O=C-I//6=S](c3,O=C-I//6=S](3,13,remove c from position 12,flow_matching,0.3,2.0,50,244
66,replace,3.0,(,-,O=C-I//6=S](3,O=C(I//6=S](3,13,replace - at position 3 with (,flow_matching,0.3,2.0,50,244
67,replace,4.0,N,I,O=C(I//6=S](3,O=C(N//6=S](3,13,replace I at position 4 with N,flow_matching,0.3,2.0,50,244
68,replace,5.0,C,/,O=C(N//6=S](3,O=C(NC/6=S](3,13,replace / at position 5 with C,flow_matching,0.3,2.0,50,244
69,replace,6.0,c,/,O=C(NC/6=S](3,O=C(NCc6=S](3,13,replace / at position 6 with c,flow_matching,0.3,2.0,50,244
70,add,4.0,I,,O=C(NCc6=S](3,O=C(INCc6=S](3,14,add I at position 4,flow_matching,0.3,2.0,50,244
71,remove,0.0,O,,O=C(INCc6=S](3,=C(INCc6=S](3,13,remove O from position 0,flow_matching,0.3,2.0,50,244
72,add,5.0,F,,=C(INCc6=S](3,=C(INFCc6=S](3,14,add F at position 5,flow_matching,0.3,2.0,50,244
73,replace,0.0,O,=,=C(INFCc6=S](3,OC(INFCc6=S](3,14,replace = at position 0 with O,flow_matching,0.3,2.0,50,244
74,add,6.0,2,,OC(INFCc6=S](3,OC(INF2Cc6=S](3,15,add 2 at position 6,flow_matching,0.3,2.0,50,244
75,remove,5.0,F,,OC(INF2Cc6=S](3,OC(IN2Cc6=S](3,14,remove F from position 5,flow_matching,0.3,2.0,50,244
76,add,1.0,l,,OC(IN2Cc6=S](3,OlC(IN2Cc6=S](3,15,add l at position 1,flow_matching,0.3,2.0,50,244
77,replace,1.0,=,l,OlC(IN2Cc6=S](3,O=C(IN2Cc6=S](3,15,replace l at position 1 with =,flow_matching,0.3,2.0,50,244
78,replace,2.0,O,C,O=C(IN2Cc6=S](3,O=O(IN2Cc6=S](3,15,replace C at position 2 with O,flow_matching,0.3,2.0,50,244
79,add,14.0,F,,O=O(IN2Cc6=S](3,O=O(IN2Cc6=S](F3,16,add F at position 14,flow_matching,0.3,2.0,50,244
80,add,0.0,),,O=O(IN2Cc6=S](F3,)O=O(IN2Cc6=S](F3,17,add ) at position 0,flow_matching,0.3,2.0,50,244
81,replace,0.0,O,),)O=O(IN2Cc6=S](F3,OO=O(IN2Cc6=S](F3,17,replace ) at position 0 with O,flow_matching,0.3,2.0,50,244
82,replace,2.0,5,=,OO=O(IN2Cc6=S](F3,OO5O(IN2Cc6=S](F3,17,replace = at position 2 with 5,flow_matching,0.3,2.0,50,244
83,add,3.0,+,,OO5O(IN2Cc6=S](F3,OO5+O(IN2Cc6=S](F3,18,add + at position 3,flow_matching,0.3,2.0,50,244
84,replace,9.0,+,C,OO5+O(IN2Cc6=S](F3,OO5+O(IN2+c6=S](F3,18,replace C at position 9 with +,flow_matching,0.3,2.0,50,244
85,replace,2.0,l,5,OO5+O(IN2+c6=S](F3,OOl+O(IN2+c6=S](F3,18,replace 5 at position 2 with l,flow_matching,0.3,2.0,50,244
86,replace,17.0,o,3,OOl+O(IN2+c6=S](F3,OOl+O(IN2+c6=S](Fo,18,replace 3 at position 17 with o,flow_matching,0.3,2.0,50,244
87,replace,1.0,=,O,OOl+O(IN2+c6=S](Fo,O=l+O(IN2+c6=S](Fo,18,replace O at position 1 with =,flow_matching,0.3,2.0,50,244
88,add,2.0,1,,O=l+O(IN2+c6=S](Fo,O=1l+O(IN2+c6=S](Fo,19,add 1 at position 2,flow_matching,0.3,2.0,50,244
89,add,8.0,1,,O=1l+O(IN2+c6=S](Fo,O=1l+O(I1N2+c6=S](Fo,20,add 1 at position 8,flow_matching,0.3,2.0,50,244
90,replace,16.0,C,],O=1l+O(I1N2+c6=S](Fo,O=1l+O(I1N2+c6=SC(Fo,20,replace ] at position 16 with C,flow_matching,0.3,2.0,50,244
91,remove,1.0,=,,O=1l+O(I1N2+c6=SC(Fo,O1l+O(I1N2+c6=SC(Fo,19,remove = from position 1,flow_matching,0.3,2.0,50,244
92,replace,3.0,N,+,O1l+O(I1N2+c6=SC(Fo,O1lNO(I1N2+c6=SC(Fo,19,replace + at position 3 with N,flow_matching,0.3,2.0,50,244
93,replace,1.0,=,1,O1lNO(I1N2+c6=SC(Fo,O=lNO(I1N2+c6=SC(Fo,19,replace 1 at position 1 with =,flow_matching,0.3,2.0,50,244
94,add,15.0,@,,O=lNO(I1N2+c6=SC(Fo,O=lNO(I1N2+c6=S@C(Fo,20,add @ at position 15,flow_matching,0.3,2.0,50,244
95,add,12.0,2,,O=lNO(I1N2+c6=S@C(Fo,O=lNO(I1N2+c26=S@C(Fo,21,add 2 at position 12,flow_matching,0.3,2.0,50,244
96,add,17.0,/,,O=lNO(I1N2+c26=S@C(Fo,O=lNO(I1N2+c26=S@/C(Fo,22,add / at position 17,flow_matching,0.3,2.0,50,244
97,add,11.0,3,,O=lNO(I1N2+c26=S@/C(Fo,O=lNO(I1N2+3c26=S@/C(Fo,23,add 3 at position 11,flow_matching,0.3,2.0,50,244
98,add,17.0,S,,O=lNO(I1N2+3c26=S@/C(Fo,O=lNO(I1N2+3c26=SS@/C(Fo,24,add S at position 17,flow_matching,0.3,2.0,50,244
99,replace,2.0,C,l,O=lNO(I1N2+3c26=SS@/C(Fo,O=CNO(I1N2+3c26=SS@/C(Fo,24,replace l at position 2 with C,flow_matching,0.3,2.0,50,244
100,replace,19.0,=,/,O=CNO(I1N2+3c26=SS@/C(Fo,O=CNO(I1N2+3c26=SS@=C(Fo,24,replace / at position 19 with =,flow_matching,0.3,2.0,50,244
101,replace,3.0,(,N,O=CNO(I1N2+3c26=SS@=C(Fo,O=C(O(I1N2+3c26=SS@=C(Fo,24,replace N at position 3 with (,flow_matching,0.3,2.0,50,244
102,add,0.0,3,,O=C(O(I1N2+3c26=SS@=C(Fo,3O=C(O(I1N2+3c26=SS@=C(Fo,25,add 3 at position 0,flow_matching,0.3,2.0,50,244
103,add,11.0,4,,3O=C(O(I1N2+3c26=SS@=C(Fo,3O=C(O(I1N24+3c26=SS@=C(Fo,26,add 4 at position 11,flow_matching,0.3,2.0,50,244
104,replace,9.0,H,N,3O=C(O(I1N24+3c26=SS@=C(Fo,3O=C(O(I1H24+3c26=SS@=C(Fo,26,replace N at position 9 with H,flow_matching,0.3,2.0,50,244
105,replace,0.0,O,3,3O=C(O(I1H24+3c26=SS@=C(Fo,OO=C(O(I1H24+3c26=SS@=C(Fo,26,replace 3 at position 0 with O,flow_matching,0.3,2.0,50,244
106,replace,1.0,=,O,OO=C(O(I1H24+3c26=SS@=C(Fo,O==C(O(I1H24+3c26=SS@=C(Fo,26,replace O at position 1 with =,flow_matching,0.3,2.0,50,244
107,replace,2.0,C,=,O==C(O(I1H24+3c26=SS@=C(Fo,O=CC(O(I1H24+3c26=SS@=C(Fo,26,replace = at position 2 with C,flow_matching,0.3,2.0,50,244
108,add,6.0,l,,O=CC(O(I1H24+3c26=SS@=C(Fo,O=CC(Ol(I1H24+3c26=SS@=C(Fo,27,add l at position 6,flow_matching,0.3,2.0,50,244
109,replace,11.0,n,2,O=CC(Ol(I1H24+3c26=SS@=C(Fo,O=CC(Ol(I1Hn4+3c26=SS@=C(Fo,27,replace 2 at position 11 with n,flow_matching,0.3,2.0,50,244
110,remove,4.0,(,,O=CC(Ol(I1Hn4+3c26=SS@=C(Fo,O=CCOl(I1Hn4+3c26=SS@=C(Fo,26,remove ( from position 4,flow_matching,0.3,2.0,50,244
111,add,11.0,H,,O=CCOl(I1Hn4+3c26=SS@=C(Fo,O=CCOl(I1HnH4+3c26=SS@=C(Fo,27,add H at position 11,flow_matching,0.3,2.0,50,244
112,add,15.0,=,,O=CCOl(I1HnH4+3c26=SS@=C(Fo,O=CCOl(I1HnH4+3=c26=SS@=C(Fo,28,add = at position 15,flow_matching,0.3,2.0,50,244
113,replace,18.0,3,6,O=CCOl(I1HnH4+3=c26=SS@=C(Fo,O=CCOl(I1HnH4+3=c23=SS@=C(Fo,28,replace 6 at position 18 with 3,flow_matching,0.3,2.0,50,244
114,replace,3.0,(,C,O=CCOl(I1HnH4+3=c23=SS@=C(Fo,O=C(Ol(I1HnH4+3=c23=SS@=C(Fo,28,replace C at position 3 with (,flow_matching,0.3,2.0,50,244
115,replace,19.0,n,=,O=C(Ol(I1HnH4+3=c23=SS@=C(Fo,O=C(Ol(I1HnH4+3=c23nSS@=C(Fo,28,replace = at position 19 with n,flow_matching,0.3,2.0,50,244
116,replace,4.0,N,O,O=C(Ol(I1HnH4+3=c23nSS@=C(Fo,O=C(Nl(I1HnH4+3=c23nSS@=C(Fo,28,replace O at position 4 with N,flow_matching,0.3,2.0,50,244
117,add,4.0,o,,O=C(Nl(I1HnH4+3=c23nSS@=C(Fo,O=C(oNl(I1HnH4+3=c23nSS@=C(Fo,29,add o at position 4,flow_matching,0.3,2.0,50,244
118,add,6.0,B,,O=C(oNl(I1HnH4+3=c23nSS@=C(Fo,O=C(oNBl(I1HnH4+3=c23nSS@=C(Fo,30,add B at position 6,flow_matching,0.3,2.0,50,244
119,add,15.0,o,,O=C(oNBl(I1HnH4+3=c23nSS@=C(Fo,O=C(oNBl(I1HnH4o+3=c23nSS@=C(Fo,31,add o at position 15,flow_matching,0.3,2.0,50,244
120,replace,4.0,N,o,O=C(oNBl(I1HnH4o+3=c23nSS@=C(Fo,O=C(NNBl(I1HnH4o+3=c23nSS@=C(Fo,31,replace o at position 4 with N,flow_matching,0.3,2.0,50,244
121,replace,9.0,N,I,O=C(NNBl(I1HnH4o+3=c23nSS@=C(Fo,O=C(NNBl(N1HnH4o+3=c23nSS@=C(Fo,31,replace I at position 9 with N,flow_matching,0.3,2.0,50,244
122,add,17.0,n,,O=C(NNBl(N1HnH4o+3=c23nSS@=C(Fo,O=C(NNBl(N1HnH4o+n3=c23nSS@=C(Fo,32,add n at position 17,flow_matching,0.3,2.0,50,244
123,add,8.0,F,,O=C(NNBl(N1HnH4o+n3=c23nSS@=C(Fo,O=C(NNBlF(N1HnH4o+n3=c23nSS@=C(Fo,33,add F at position 8,flow_matching,0.3,2.0,50,244
124,replace,9.0,C,(,O=C(NNBlF(N1HnH4o+n3=c23nSS@=C(Fo,O=C(NNBlFCN1HnH4o+n3=c23nSS@=C(Fo,33,replace ( at position 9 with C,flow_matching,0.3,2.0,50,244
125,replace,19.0,F,3,O=C(NNBlFCN1HnH4o+n3=c23nSS@=C(Fo,O=C(NNBlFCN1HnH4o+nF=c23nSS@=C(Fo,33,replace 3 at position 19 with F,flow_matching,0.3,2.0,50,244
126,remove,2.0,C,,O=C(NNBlFCN1HnH4o+nF=c23nSS@=C(Fo,O=(NNBlFCN1HnH4o+nF=c23nSS@=C(Fo,32,remove C from position 2,flow_matching,0.3,2.0,50,244
127,replace,2.0,C,(,O=(NNBlFCN1HnH4o+nF=c23nSS@=C(Fo,O=CNNBlFCN1HnH4o+nF=c23nSS@=C(Fo,32,replace ( at position 2 with C,flow_matching,0.3,2.0,50,244
128,replace,28.0,-,C,O=CNNBlFCN1HnH4o+nF=c23nSS@=C(Fo,O=CNNBlFCN1HnH4o+nF=c23nSS@=-(Fo,32,replace C at position 28 with -,flow_matching,0.3,2.0,50,244
129,replace,3.0,(,N,O=CNNBlFCN1HnH4o+nF=c23nSS@=-(Fo,O=C(NBlFCN1HnH4o+nF=c23nSS@=-(Fo,32,replace N at position 3 with (,flow_matching,0.3,2.0,50,244
130,replace,5.0,C,B,O=C(NBlFCN1HnH4o+nF=c23nSS@=-(Fo,O=C(NClFCN1HnH4o+nF=c23nSS@=-(Fo,32,replace B at position 5 with C,flow_matching,0.3,2.0,50,244
131,replace,6.0,c,l,O=C(NClFCN1HnH4o+nF=c23nSS@=-(Fo,O=C(NCcFCN1HnH4o+nF=c23nSS@=-(Fo,32,replace l at position 6 with c,flow_matching,0.3,2.0,50,244
132,add,28.0,=,,O=C(NCcFCN1HnH4o+nF=c23nSS@=-(Fo,O=C(NCcFCN1HnH4o+nF=c23nSS@==-(Fo,33,add = at position 28,flow_matching,0.3,2.0,50,244
133,add,11.0,O,,O=C(NCcFCN1HnH4o+nF=c23nSS@==-(Fo,O=C(NCcFCN1OHnH4o+nF=c23nSS@==-(Fo,34,add O at position 11,flow_matching,0.3,2.0,50,244
134,replace,7.0,1,F,O=C(NCcFCN1OHnH4o+nF=c23nSS@==-(Fo,O=C(NCc1CN1OHnH4o+nF=c23nSS@==-(Fo,34,replace F at position 7 with 1,flow_matching,0.3,2.0,50,244
135,replace,8.0,c,C,O=C(NCc1CN1OHnH4o+nF=c23nSS@==-(Fo,O=C(NCc1cN1OHnH4o+nF=c23nSS@==-(Fo,34,replace C at position 8 with c,flow_matching,0.3,2.0,50,244
136,replace,9.0,c,N,O=C(NCc1cN1OHnH4o+nF=c23nSS@==-(Fo,O=C(NCc1cc1OHnH4o+nF=c23nSS@==-(Fo,34,replace N at position 9 with c,flow_matching,0.3,2.0,50,244
137,replace,10.0,c,1,O=C(NCc1cc1OHnH4o+nF=c23nSS@==-(Fo,O=C(NCc1cccOHnH4o+nF=c23nSS@==-(Fo,34,replace 1 at position 10 with c,flow_matching,0.3,2.0,50,244
138,replace,17.0,N,+,O=C(NCc1cccOHnH4o+nF=c23nSS@==-(Fo,O=C(NCc1cccOHnH4oNnF=c23nSS@==-(Fo,34,replace + at position 17 with N,flow_matching,0.3,2.0,50,244
139,add,22.0,r,,O=C(NCc1cccOHnH4oNnF=c23nSS@==-(Fo,O=C(NCc1cccOHnH4oNnF=cr23nSS@==-(Fo,35,add r at position 22,flow_matching,0.3,2.0,50,244
140,add,34.0,/,,O=C(NCc1cccOHnH4oNnF=cr23nSS@==-(Fo,O=C(NCc1cccOHnH4oNnF=cr23nSS@==-(F/o,36,add / at position 34,flow_matching,0.3,2.0,50,244
141,add,3.0,],,O=C(NCc1cccOHnH4oNnF=cr23nSS@==-(F/o,O=C](NCc1cccOHnH4oNnF=cr23nSS@==-(F/o,37,add ] at position 3,flow_matching,0.3,2.0,50,244
142,add,4.0,(,,O=C](NCc1cccOHnH4oNnF=cr23nSS@==-(F/o,O=C]((NCc1cccOHnH4oNnF=cr23nSS@==-(F/o,38,add ( at position 4,flow_matching,0.3,2.0,50,244
143,replace,13.0,N,O,O=C]((NCc1cccOHnH4oNnF=cr23nSS@==-(F/o,O=C]((NCc1cccNHnH4oNnF=cr23nSS@==-(F/o,38,replace O at position 13 with N,flow_matching,0.3,2.0,50,244
144,replace,3.0,(,],O=C]((NCc1cccNHnH4oNnF=cr23nSS@==-(F/o,O=C(((NCc1cccNHnH4oNnF=cr23nSS@==-(F/o,38,replace ] at position 3 with (,flow_matching,0.3,2.0,50,244
145,add,6.0,l,,O=C(((NCc1cccNHnH4oNnF=cr23nSS@==-(F/o,O=C(((lNCc1cccNHnH4oNnF=cr23nSS@==-(F/o,39,add l at position 6,flow_matching,0.3,2.0,50,244
146,replace,7.0,[,N,O=C(((lNCc1cccNHnH4oNnF=cr23nSS@==-(F/o,O=C(((l[Cc1cccNHnH4oNnF=cr23nSS@==-(F/o,39,replace N at position 7 with [,flow_matching,0.3,2.0,50,244
147,add,38.0,),,O=C(((l[Cc1cccNHnH4oNnF=cr23nSS@==-(F/o,O=C(((l[Cc1cccNHnH4oNnF=cr23nSS@==-(F/)o,40,add ) at position 38,flow_matching,0.3,2.0,50,244
148,add,12.0,H,,O=C(((l[Cc1cccNHnH4oNnF=cr23nSS@==-(F/)o,O=C(((l[Cc1cHccNHnH4oNnF=cr23nSS@==-(F/)o,41,add H at position 12,flow_matching,0.3,2.0,50,244
149,add,6.0,l,,O=C(((l[Cc1cHccNHnH4oNnF=cr23nSS@==-(F/)o,O=C(((ll[Cc1cHccNHnH4oNnF=cr23nSS@==-(F/)o,42,add l at position 6,flow_matching,0.3,2.0,50,244
150,remove,27.0,r,,O=C(((ll[Cc1cHccNHnH4oNnF=cr23nSS@==-(F/)o,O=C(((ll[Cc1cHccNHnH4oNnF=c23nSS@==-(F/)o,41,remove r from position 27,flow_matching,0.3,2.0,50,244
151,replace,36.0,+,(,O=C(((ll[Cc1cHccNHnH4oNnF=c23nSS@==-(F/)o,O=C(((ll[Cc1cHccNHnH4oNnF=c23nSS@==-+F/)o,41,replace ( at position 36 with +,flow_matching,0.3,2.0,50,244
152,replace,4.0,N,(,O=C(((ll[Cc1cHccNHnH4oNnF=c23nSS@==-+F/)o,O=C(N(ll[Cc1cHccNHnH4oNnF=c23nSS@==-+F/)o,41,replace ( at position 4 with N,flow_matching,0.3,2.0,50,244
153,remove,15.0,c,,O=C(N(ll[Cc1cHccNHnH4oNnF=c23nSS@==-+F/)o,O=C(N(ll[Cc1cHcNHnH4oNnF=c23nSS@==-+F/)o,40,remove c from position 15,flow_matching,0.3,2.0,50,244
154,replace,5.0,C,(,O=C(N(ll[Cc1cHcNHnH4oNnF=c23nSS@==-+F/)o,O=C(NCll[Cc1cHcNHnH4oNnF=c23nSS@==-+F/)o,40,replace ( at position 5 with C,flow_matching,0.3,2.0,50,244
155,add,16.0,@,,O=C(NCll[Cc1cHcNHnH4oNnF=c23nSS@==-+F/)o,O=C(NCll[Cc1cHcN@HnH4oNnF=c23nSS@==-+F/)o,41,add @ at position 16,flow_matching,0.3,2.0,50,244
156,add,3.0,C,,O=C(NCll[Cc1cHcN@HnH4oNnF=c23nSS@==-+F/)o,O=CC(NCll[Cc1cHcN@HnH4oNnF=c23nSS@==-+F/)o,42,add C at position 3,flow_matching,0.3,2.0,50,244
157,remove,30.0,n,,O=CC(NCll[Cc1cHcN@HnH4oNnF=c23nSS@==-+F/)o,O=CC(NCll[Cc1cHcN@HnH4oNnF=c23SS@==-+F/)o,41,remove n from position 30,flow_matching,0.3,2.0,50,244
158,replace,3.0,(,C,O=CC(NCll[Cc1cHcN@HnH4oNnF=c23SS@==-+F/)o,O=C((NCll[Cc1cHcN@HnH4oNnF=c23SS@==-+F/)o,41,replace C at position 3 with (,flow_matching,0.3,2.0,50,244
159,add,2.0,4,,O=C((NCll[Cc1cHcN@HnH4oNnF=c23SS@==-+F/)o,O=4C((NCll[Cc1cHcN@HnH4oNnF=c23SS@==-+F/)o,42,add 4 at position 2,flow_matching,0.3,2.0,50,244
160,add,17.0,+,,O=4C((NCll[Cc1cHcN@HnH4oNnF=c23SS@==-+F/)o,O=4C((NCll[Cc1cHc+N@HnH4oNnF=c23SS@==-+F/)o,43,add + at position 17,flow_matching,0.3,2.0,50,244
161,replace,14.0,n,c,O=4C((NCll[Cc1cHc+N@HnH4oNnF=c23SS@==-+F/)o,O=4C((NCll[Cc1nHc+N@HnH4oNnF=c23SS@==-+F/)o,43,replace c at position 14 with n,flow_matching,0.3,2.0,50,244
162,add,5.0,B,,O=4C((NCll[Cc1nHc+N@HnH4oNnF=c23SS@==-+F/)o,O=4C(B(NCll[Cc1nHc+N@HnH4oNnF=c23SS@==-+F/)o,44,add B at position 5,flow_matching,0.3,2.0,50,244
163,add,41.0,S,,O=4C(B(NCll[Cc1nHc+N@HnH4oNnF=c23SS@==-+F/)o,O=4C(B(NCll[Cc1nHc+N@HnH4oNnF=c23SS@==-+FS/)o,45,add S at position 41,flow_matching,0.3,2.0,50,244
164,replace,2.0,C,4,O=4C(B(NCll[Cc1nHc+N@HnH4oNnF=c23SS@==-+FS/)o,O=CC(B(NCll[Cc1nHc+N@HnH4oNnF=c23SS@==-+FS/)o,45,replace 4 at position 2 with C,flow_matching,0.3,2.0,50,244
165,replace,32.0,o,3,O=CC(B(NCll[Cc1nHc+N@HnH4oNnF=c23SS@==-+FS/)o,O=CC(B(NCll[Cc1nHc+N@HnH4oNnF=c2oSS@==-+FS/)o,45,replace 3 at position 32 with o,flow_matching,0.3,2.0,50,244
166,replace,0.0,-,O,O=CC(B(NCll[Cc1nHc+N@HnH4oNnF=c2oSS@==-+FS/)o,-=CC(B(NCll[Cc1nHc+N@HnH4oNnF=c2oSS@==-+FS/)o,45,replace O at position 0 with -,flow_matching,0.3,2.0,50,244
167,remove,18.0,+,,-=CC(B(NCll[Cc1nHc+N@HnH4oNnF=c2oSS@==-+FS/)o,-=CC(B(NCll[Cc1nHcN@HnH4oNnF=c2oSS@==-+FS/)o,44,remove + from position 18,flow_matching,0.3,2.0,50,244
168,add,12.0,],,-=CC(B(NCll[Cc1nHcN@HnH4oNnF=c2oSS@==-+FS/)o,-=CC(B(NCll[]Cc1nHcN@HnH4oNnF=c2oSS@==-+FS/)o,45,add ] at position 12,flow_matching,0.3,2.0,50,244
169,replace,0.0,O,-,-=CC(B(NCll[]Cc1nHcN@HnH4oNnF=c2oSS@==-+FS/)o,O=CC(B(NCll[]Cc1nHcN@HnH4oNnF=c2oSS@==-+FS/)o,45,replace - at position 0 with O,flow_matching,0.3,2.0,50,244
170,add,38.0,6,,O=CC(B(NCll[]Cc1nHcN@HnH4oNnF=c2oSS@==-+FS/)o,O=CC(B(NCll[]Cc1nHcN@HnH4oNnF=c2oSS@==6-+FS/)o,46,add 6 at position 38,flow_matching,0.3,2.0,50,244
171,replace,31.0,-,2,O=CC(B(NCll[]Cc1nHcN@HnH4oNnF=c2oSS@==6-+FS/)o,O=CC(B(NCll[]Cc1nHcN@HnH4oNnF=c-oSS@==6-+FS/)o,46,replace 2 at position 31 with -,flow_matching,0.3,2.0,50,244
172,add,11.0,\,,O=CC(B(NCll[]Cc1nHcN@HnH4oNnF=c-oSS@==6-+FS/)o,O=CC(B(NCll\[]Cc1nHcN@HnH4oNnF=c-oSS@==6-+FS/)o,47,add \ at position 11,flow_matching,0.3,2.0,50,244
173,replace,3.0,(,C,O=CC(B(NCll\[]Cc1nHcN@HnH4oNnF=c-oSS@==6-+FS/)o,O=C((B(NCll\[]Cc1nHcN@HnH4oNnF=c-oSS@==6-+FS/)o,47,replace C at position 3 with (,flow_matching,0.3,2.0,50,244
174,add,26.0,I,,O=C((B(NCll\[]Cc1nHcN@HnH4oNnF=c-oSS@==6-+FS/)o,O=C((B(NCll\[]Cc1nHcN@HnH4IoNnF=c-oSS@==6-+FS/)o,48,add I at position 26,flow_matching,0.3,2.0,50,244
175,replace,22.0,/,H,O=C((B(NCll\[]Cc1nHcN@HnH4IoNnF=c-oSS@==6-+FS/)o,O=C((B(NCll\[]Cc1nHcN@/nH4IoNnF=c-oSS@==6-+FS/)o,48,replace H at position 22 with /,flow_matching,0.3,2.0,50,244
176,add,32.0,B,,O=C((B(NCll\[]Cc1nHcN@/nH4IoNnF=c-oSS@==6-+FS/)o,O=C((B(NCll\[]Cc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,49,add B at position 32,flow_matching,0.3,2.0,50,244
177,add,13.0,B,,O=C((B(NCll\[]Cc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,O=C((B(NCll\[B]Cc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,50,add B at position 13,flow_matching,0.3,2.0,50,244
178,remove,12.0,[,,O=C((B(NCll\[B]Cc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,O=C((B(NCll\B]Cc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,49,remove [ from position 12,flow_matching,0.3,2.0,50,244
179,add,10.0,o,,O=C((B(NCll\B]Cc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,O=C((B(NClol\B]Cc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,50,add o at position 10,flow_matching,0.3,2.0,50,244
180,replace,4.0,N,(,O=C((B(NClol\B]Cc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,O=C(NB(NClol\B]Cc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,50,replace ( at position 4 with N,flow_matching,0.3,2.0,50,244
181,add,15.0,I,,O=C(NB(NClol\B]Cc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,O=C(NB(NClol\B]ICc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,51,add I at position 15,flow_matching,0.3,2.0,50,244
182,replace,5.0,C,B,O=C(NB(NClol\B]ICc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,O=C(NC(NClol\B]ICc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,51,replace B at position 5 with C,flow_matching,0.3,2.0,50,244
183,remove,44.0,-,,O=C(NC(NClol\B]ICc1nHcN@/nH4IoNnF=Bc-oSS@==6-+FS/)o,O=C(NC(NClol\B]ICc1nHcN@/nH4IoNnF=Bc-oSS@==6+FS/)o,50,remove - from position 44,flow_matching,0.3,2.0,50,244
184,remove,21.0,c,,O=C(NC(NClol\B]ICc1nHcN@/nH4IoNnF=Bc-oSS@==6+FS/)o,O=C(NC(NClol\B]ICc1nHN@/nH4IoNnF=Bc-oSS@==6+FS/)o,49,remove c from position 21,flow_matching,0.3,2.0,50,244
185,remove,21.0,N,,O=C(NC(NClol\B]ICc1nHN@/nH4IoNnF=Bc-oSS@==6+FS/)o,O=C(NC(NClol\B]ICc1nH@/nH4IoNnF=Bc-oSS@==6+FS/)o,48,remove N from position 21,flow_matching,0.3,2.0,50,244
186,add,26.0,6,,O=C(NC(NClol\B]ICc1nH@/nH4IoNnF=Bc-oSS@==6+FS/)o,O=C(NC(NClol\B]ICc1nH@/nH46IoNnF=Bc-oSS@==6+FS/)o,49,add 6 at position 26,flow_matching,0.3,2.0,50,244
187,remove,24.0,H,,O=C(NC(NClol\B]ICc1nH@/nH46IoNnF=Bc-oSS@==6+FS/)o,O=C(NC(NClol\B]ICc1nH@/n46IoNnF=Bc-oSS@==6+FS/)o,48,remove H from position 24,flow_matching,0.3,2.0,50,244
188,add,19.0,s,,O=C(NC(NClol\B]ICc1nH@/n46IoNnF=Bc-oSS@==6+FS/)o,O=C(NC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@==6+FS/)o,49,add s at position 19,flow_matching,0.3,2.0,50,244
189,add,3.0,O,,O=C(NC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@==6+FS/)o,O=CO(NC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@==6+FS/)o,50,add O at position 3,flow_matching,0.3,2.0,50,244
190,replace,3.0,(,O,O=CO(NC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@==6+FS/)o,O=C((NC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@==6+FS/)o,50,replace O at position 3 with (,flow_matching,0.3,2.0,50,244
191,replace,4.0,N,(,O=C((NC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@==6+FS/)o,O=C(NNC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@==6+FS/)o,50,replace ( at position 4 with N,flow_matching,0.3,2.0,50,244
192,remove,41.0,=,,O=C(NNC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@==6+FS/)o,O=C(NNC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@=6+FS/)o,49,remove = from position 41,flow_matching,0.3,2.0,50,244
193,replace,5.0,C,N,O=C(NNC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@=6+FS/)o,O=C(NCC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@=6+FS/)o,49,replace N at position 5 with C,flow_matching,0.3,2.0,50,244
194,replace,34.0,S,B,O=C(NCC(NClol\B]ICc1snH@/n46IoNnF=Bc-oSS@=6+FS/)o,O=C(NCC(NClol\B]ICc1snH@/n46IoNnF=Sc-oSS@=6+FS/)o,49,replace B at position 34 with S,flow_matching,0.3,2.0,50,244
195,add,10.0,S,,O=C(NCC(NClol\B]ICc1snH@/n46IoNnF=Sc-oSS@=6+FS/)o,O=C(NCC(NCSlol\B]ICc1snH@/n46IoNnF=Sc-oSS@=6+FS/)o,50,add S at position 10,flow_matching,0.3,2.0,50,244
196,add,31.0,5,,O=C(NCC(NCSlol\B]ICc1snH@/n46IoNnF=Sc-oSS@=6+FS/)o,O=C(NCC(NCSlol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,51,add 5 at position 31,flow_matching,0.3,2.0,50,244
197,add,12.0,/,,O=C(NCC(NCSlol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCC(NCSl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,add / at position 12,flow_matching,0.3,2.0,50,244
198,replace,6.0,c,C,O=C(NCC(NCSl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc(NCSl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace C at position 6 with c,flow_matching,0.3,2.0,50,244
199,replace,7.0,1,(,O=C(NCc(NCSl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1NCSl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace ( at position 7 with 1,flow_matching,0.3,2.0,50,244
200,replace,8.0,c,N,O=C(NCc1NCSl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1cCSl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace N at position 8 with c,flow_matching,0.3,2.0,50,244
201,replace,9.0,c,C,O=C(NCc1cCSl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccSl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace C at position 9 with c,flow_matching,0.3,2.0,50,244
202,replace,10.0,c,S,O=C(NCc1ccSl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1cccl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace S at position 10 with c,flow_matching,0.3,2.0,50,244
203,replace,11.0,(,l,O=C(NCc1cccl/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc(/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace l at position 11 with (,flow_matching,0.3,2.0,50,244
204,replace,12.0,[,/,O=C(NCc1ccc(/ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace / at position 12 with [,flow_matching,0.3,2.0,50,244
205,replace,13.0,N,o,O=C(NCc1ccc([ol\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([Nl\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace o at position 13 with N,flow_matching,0.3,2.0,50,244
206,replace,14.0,+,l,O=C(NCc1ccc([Nl\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace l at position 14 with +,flow_matching,0.3,2.0,50,244
207,replace,15.0,],\,O=C(NCc1ccc([N+\B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+]B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace \ at position 15 with ],flow_matching,0.3,2.0,50,244
208,replace,16.0,(,B,O=C(NCc1ccc([N+]B]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace B at position 16 with (,flow_matching,0.3,2.0,50,244
209,replace,17.0,=,],O=C(NCc1ccc([N+](]ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace ] at position 17 with =,flow_matching,0.3,2.0,50,244
210,replace,18.0,O,I,O=C(NCc1ccc([N+](=ICc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=OCc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace I at position 18 with O,flow_matching,0.3,2.0,50,244
211,replace,19.0,),C,O=C(NCc1ccc([N+](=OCc1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)c1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace C at position 19 with ),flow_matching,0.3,2.0,50,244
212,replace,20.0,[,c,O=C(NCc1ccc([N+](=O)c1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace c at position 20 with [,flow_matching,0.3,2.0,50,244
213,replace,21.0,O,1,O=C(NCc1ccc([N+](=O)[1snH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[OsnH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace 1 at position 21 with O,flow_matching,0.3,2.0,50,244
214,replace,22.0,-,s,O=C(NCc1ccc([N+](=O)[OsnH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-nH@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace s at position 22 with -,flow_matching,0.3,2.0,50,244
215,replace,23.0,],n,O=C(NCc1ccc([N+](=O)[O-nH@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-]H@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace n at position 23 with ],flow_matching,0.3,2.0,50,244
216,replace,24.0,),H,O=C(NCc1ccc([N+](=O)[O-]H@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])@/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace H at position 24 with ),flow_matching,0.3,2.0,50,244
217,replace,25.0,c,@,O=C(NCc1ccc([N+](=O)[O-])@/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])c/n46Io5NnF=Sc-oSS@=6+FS/)o,52,replace @ at position 25 with c,flow_matching,0.3,2.0,50,244
218,replace,26.0,c,/,O=C(NCc1ccc([N+](=O)[O-])c/n46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])ccn46Io5NnF=Sc-oSS@=6+FS/)o,52,replace / at position 26 with c,flow_matching,0.3,2.0,50,244
219,replace,27.0,1,n,O=C(NCc1ccc([N+](=O)[O-])ccn46Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc146Io5NnF=Sc-oSS@=6+FS/)o,52,replace n at position 27 with 1,flow_matching,0.3,2.0,50,244
220,replace,28.0,),4,O=C(NCc1ccc([N+](=O)[O-])cc146Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)6Io5NnF=Sc-oSS@=6+FS/)o,52,replace 4 at position 28 with ),flow_matching,0.3,2.0,50,244
221,replace,29.0,N,6,O=C(NCc1ccc([N+](=O)[O-])cc1)6Io5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)NIo5NnF=Sc-oSS@=6+FS/)o,52,replace 6 at position 29 with N,flow_matching,0.3,2.0,50,244
222,replace,30.0,[,I,O=C(NCc1ccc([N+](=O)[O-])cc1)NIo5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[o5NnF=Sc-oSS@=6+FS/)o,52,replace I at position 30 with [,flow_matching,0.3,2.0,50,244
223,replace,31.0,C,o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[o5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C5NnF=Sc-oSS@=6+FS/)o,52,replace o at position 31 with C,flow_matching,0.3,2.0,50,244
224,replace,32.0,@,5,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C5NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@NnF=Sc-oSS@=6+FS/)o,52,replace 5 at position 32 with @,flow_matching,0.3,2.0,50,244
225,replace,33.0,@,N,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@NnF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@nF=Sc-oSS@=6+FS/)o,52,replace N at position 33 with @,flow_matching,0.3,2.0,50,244
226,replace,34.0,H,n,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@nF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@HF=Sc-oSS@=6+FS/)o,52,replace n at position 34 with H,flow_matching,0.3,2.0,50,244
227,replace,35.0,],F,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@HF=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]=Sc-oSS@=6+FS/)o,52,replace F at position 35 with ],flow_matching,0.3,2.0,50,244
228,replace,36.0,1,=,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]=Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1Sc-oSS@=6+FS/)o,52,replace = at position 36 with 1,flow_matching,0.3,2.0,50,244
229,replace,37.0,C,S,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1Sc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1Cc-oSS@=6+FS/)o,52,replace S at position 37 with C,flow_matching,0.3,2.0,50,244
230,replace,38.0,C,c,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1Cc-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CC-oSS@=6+FS/)o,52,replace c at position 38 with C,flow_matching,0.3,2.0,50,244
231,replace,39.0,C,-,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CC-oSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCoSS@=6+FS/)o,52,replace - at position 39 with C,flow_matching,0.3,2.0,50,244
232,replace,40.0,C,o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCoSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCCSS@=6+FS/)o,52,replace o at position 40 with C,flow_matching,0.3,2.0,50,244
233,replace,41.0,[,S,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCCSS@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[S@=6+FS/)o,52,replace S at position 41 with [,flow_matching,0.3,2.0,50,244
234,replace,42.0,C,S,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[S@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@=6+FS/)o,52,replace S at position 42 with C,flow_matching,0.3,2.0,50,244
235,replace,44.0,H,=,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@=6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H6+FS/)o,52,replace = at position 44 with H,flow_matching,0.3,2.0,50,244
236,replace,45.0,],6,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H6+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]+FS/)o,52,replace 6 at position 45 with ],flow_matching,0.3,2.0,50,244
237,replace,46.0,1,+,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]+FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1FS/)o,52,replace + at position 46 with 1,flow_matching,0.3,2.0,50,244
238,replace,47.0,C,F,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1FS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CS/)o,52,replace F at position 47 with C,flow_matching,0.3,2.0,50,244
239,replace,48.0,O,S,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CS/)o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CO/)o,52,replace S at position 48 with O,flow_matching,0.3,2.0,50,244
240,replace,49.0,"
",/,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CO/)o,"O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CO
)o",52,"replace / at position 49 with 
",flow_matching,0.3,2.0,50,244
241,remove,49.0,"
",,"O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CO
)o",O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CO)o,51,"remove 
 from position 49",flow_matching,0.3,2.0,50,244
242,replace,49.0,"
",),O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CO)o,"O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CO
o",51,"replace ) at position 49 with 
",flow_matching,0.3,2.0,50,244
243,remove,49.0,"
",,"O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CO
o",O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1COo,50,"remove 
 from position 49",flow_matching,0.3,2.0,50,244
244,replace,49.0,"
",o,O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1COo,"O=C(NCc1ccc([N+](=O)[O-])cc1)N[C@@H]1CCCC[C@H]1CO
",50,"replace o at position 49 with 
",flow_matching,0.3,2.0,50,244
