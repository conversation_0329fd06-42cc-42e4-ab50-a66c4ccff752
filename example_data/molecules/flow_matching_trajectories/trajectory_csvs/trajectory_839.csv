step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,31,145
1,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,31,145
2,remove,0.0,F,,F,,0,remove F from position 0,flow_matching,0.3,2.0,31,145
3,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,31,145
4,replace,0.0,C,-,-,C,1,replace - at position 0 with C,flow_matching,0.3,2.0,31,145
5,replace,0.0,I,C,C,I,1,replace C at position 0 with I,flow_matching,0.3,2.0,31,145
6,add,1.0,C,,I,IC,2,add <PERSON> at position 1,flow_matching,0.3,2.0,31,145
7,replace,1.0,#,C,IC,I#,2,replace <PERSON> at position 1 with #,flow_matching,0.3,2.0,31,145
8,replace,1.0,C,#,I#,IC,2,replace # at position 1 with C,flow_matching,0.3,2.0,31,145
9,add,2.0,[,,IC,IC[,3,add [ at position 2,flow_matching,0.3,2.0,31,145
10,add,3.0,(,,IC[,IC[(,4,add ( at position 3,flow_matching,0.3,2.0,31,145
11,remove,1.0,C,,IC[(,I[(,3,remove C from position 1,flow_matching,0.3,2.0,31,145
12,replace,1.0,C,[,I[(,IC(,3,replace [ at position 1 with C,flow_matching,0.3,2.0,31,145
13,replace,0.0,=,I,IC(,=C(,3,replace I at position 0 with =,flow_matching,0.3,2.0,31,145
14,replace,0.0,I,=,=C(,IC(,3,replace = at position 0 with I,flow_matching,0.3,2.0,31,145
15,remove,1.0,C,,IC(,I(,2,remove C from position 1,flow_matching,0.3,2.0,31,145
16,add,1.0,6,,I(,I6(,3,add 6 at position 1,flow_matching,0.3,2.0,31,145
17,replace,2.0,=,(,I6(,I6=,3,replace ( at position 2 with =,flow_matching,0.3,2.0,31,145
18,add,3.0,c,,I6=,I6=c,4,add c at position 3,flow_matching,0.3,2.0,31,145
19,add,0.0,C,,I6=c,CI6=c,5,add C at position 0,flow_matching,0.3,2.0,31,145
20,replace,2.0,\,6,CI6=c,CI\=c,5,replace 6 at position 2 with \,flow_matching,0.3,2.0,31,145
21,remove,1.0,I,,CI\=c,C\=c,4,remove I from position 1,flow_matching,0.3,2.0,31,145
22,replace,2.0,r,=,C\=c,C\rc,4,replace = at position 2 with r,flow_matching,0.3,2.0,31,145
23,add,2.0,6,,C\rc,C\6rc,5,add 6 at position 2,flow_matching,0.3,2.0,31,145
24,remove,2.0,6,,C\6rc,C\rc,4,remove 6 from position 2,flow_matching,0.3,2.0,31,145
25,remove,0.0,C,,C\rc,\rc,3,remove C from position 0,flow_matching,0.3,2.0,31,145
26,replace,0.0,I,\,\rc,Irc,3,replace \ at position 0 with I,flow_matching,0.3,2.0,31,145
27,remove,2.0,c,,Irc,Ir,2,remove c from position 2,flow_matching,0.3,2.0,31,145
28,replace,1.0,C,r,Ir,IC,2,replace r at position 1 with C,flow_matching,0.3,2.0,31,145
29,add,0.0,(,,IC,(IC,3,add ( at position 0,flow_matching,0.3,2.0,31,145
30,remove,0.0,(,,(IC,IC,2,remove ( from position 0,flow_matching,0.3,2.0,31,145
31,replace,1.0,2,C,IC,I2,2,replace C at position 1 with 2,flow_matching,0.3,2.0,31,145
32,remove,0.0,I,,I2,2,1,remove I from position 0,flow_matching,0.3,2.0,31,145
33,replace,0.0,I,2,2,I,1,replace 2 at position 0 with I,flow_matching,0.3,2.0,31,145
34,add,1.0,C,,I,IC,2,add C at position 1,flow_matching,0.3,2.0,31,145
35,add,2.0,o,,IC,ICo,3,add o at position 2,flow_matching,0.3,2.0,31,145
36,remove,0.0,I,,ICo,Co,2,remove I from position 0,flow_matching,0.3,2.0,31,145
37,replace,1.0,c,o,Co,Cc,2,replace o at position 1 with c,flow_matching,0.3,2.0,31,145
38,add,2.0,F,,Cc,CcF,3,add F at position 2,flow_matching,0.3,2.0,31,145
39,replace,1.0,4,c,CcF,C4F,3,replace c at position 1 with 4,flow_matching,0.3,2.0,31,145
40,add,3.0,C,,C4F,C4FC,4,add C at position 3,flow_matching,0.3,2.0,31,145
41,remove,3.0,C,,C4FC,C4F,3,remove C from position 3,flow_matching,0.3,2.0,31,145
42,replace,1.0,),4,C4F,C)F,3,replace 4 at position 1 with ),flow_matching,0.3,2.0,31,145
43,remove,0.0,C,,C)F,)F,2,remove C from position 0,flow_matching,0.3,2.0,31,145
44,replace,0.0,I,),)F,IF,2,replace ) at position 0 with I,flow_matching,0.3,2.0,31,145
45,replace,0.0,O,I,IF,OF,2,replace I at position 0 with O,flow_matching,0.3,2.0,31,145
46,add,0.0,=,,OF,=OF,3,add = at position 0,flow_matching,0.3,2.0,31,145
47,replace,0.0,I,=,=OF,IOF,3,replace = at position 0 with I,flow_matching,0.3,2.0,31,145
48,replace,1.0,C,O,IOF,ICF,3,replace O at position 1 with C,flow_matching,0.3,2.0,31,145
49,replace,2.0,7,F,ICF,IC7,3,replace F at position 2 with 7,flow_matching,0.3,2.0,31,145
50,add,3.0,+,,IC7,IC7+,4,add + at position 3,flow_matching,0.3,2.0,31,145
51,replace,2.0,[,7,IC7+,IC[+,4,replace 7 at position 2 with [,flow_matching,0.3,2.0,31,145
52,replace,1.0,/,C,IC[+,I/[+,4,replace C at position 1 with /,flow_matching,0.3,2.0,31,145
53,add,1.0,=,,I/[+,I=/[+,5,add = at position 1,flow_matching,0.3,2.0,31,145
54,add,2.0,[,,I=/[+,I=[/[+,6,add [ at position 2,flow_matching,0.3,2.0,31,145
55,add,1.0,B,,I=[/[+,IB=[/[+,7,add B at position 1,flow_matching,0.3,2.0,31,145
56,add,6.0,/,,IB=[/[+,IB=[/[/+,8,add / at position 6,flow_matching,0.3,2.0,31,145
57,add,4.0,6,,IB=[/[/+,IB=[6/[/+,9,add 6 at position 4,flow_matching,0.3,2.0,31,145
58,replace,2.0,@,=,IB=[6/[/+,IB@[6/[/+,9,replace = at position 2 with @,flow_matching,0.3,2.0,31,145
59,add,4.0,\,,IB@[6/[/+,IB@[\6/[/+,10,add \ at position 4,flow_matching,0.3,2.0,31,145
60,replace,1.0,C,B,IB@[\6/[/+,IC@[\6/[/+,10,replace B at position 1 with C,flow_matching,0.3,2.0,31,145
61,add,0.0,C,,IC@[\6/[/+,CIC@[\6/[/+,11,add C at position 0,flow_matching,0.3,2.0,31,145
62,replace,0.0,I,C,CIC@[\6/[/+,IIC@[\6/[/+,11,replace C at position 0 with I,flow_matching,0.3,2.0,31,145
63,add,4.0,S,,IIC@[\6/[/+,IIC@S[\6/[/+,12,add S at position 4,flow_matching,0.3,2.0,31,145
64,add,11.0,@,,IIC@S[\6/[/+,IIC@S[\6/[/@+,13,add @ at position 11,flow_matching,0.3,2.0,31,145
65,add,6.0,(,,IIC@S[\6/[/@+,IIC@S[(\6/[/@+,14,add ( at position 6,flow_matching,0.3,2.0,31,145
66,replace,1.0,C,I,IIC@S[(\6/[/@+,ICC@S[(\6/[/@+,14,replace I at position 1 with C,flow_matching,0.3,2.0,31,145
67,add,13.0,I,,ICC@S[(\6/[/@+,ICC@S[(\6/[/@I+,15,add I at position 13,flow_matching,0.3,2.0,31,145
68,remove,11.0,/,,ICC@S[(\6/[/@I+,ICC@S[(\6/[@I+,14,remove / from position 11,flow_matching,0.3,2.0,31,145
69,replace,2.0,[,C,ICC@S[(\6/[@I+,IC[@S[(\6/[@I+,14,replace C at position 2 with [,flow_matching,0.3,2.0,31,145
70,replace,3.0,C,@,IC[@S[(\6/[@I+,IC[CS[(\6/[@I+,14,replace @ at position 3 with C,flow_matching,0.3,2.0,31,145
71,remove,2.0,[,,IC[CS[(\6/[@I+,ICCS[(\6/[@I+,13,remove [ from position 2,flow_matching,0.3,2.0,31,145
72,add,11.0,6,,ICCS[(\6/[@I+,ICCS[(\6/[@6I+,14,add 6 at position 11,flow_matching,0.3,2.0,31,145
73,remove,4.0,[,,ICCS[(\6/[@6I+,ICCS(\6/[@6I+,13,remove [ from position 4,flow_matching,0.3,2.0,31,145
74,replace,2.0,[,C,ICCS(\6/[@6I+,IC[S(\6/[@6I+,13,replace C at position 2 with [,flow_matching,0.3,2.0,31,145
75,add,12.0,l,,IC[S(\6/[@6I+,IC[S(\6/[@6Il+,14,add l at position 12,flow_matching,0.3,2.0,31,145
76,replace,3.0,C,S,IC[S(\6/[@6Il+,IC[C(\6/[@6Il+,14,replace S at position 3 with C,flow_matching,0.3,2.0,31,145
77,replace,4.0,@,(,IC[C(\6/[@6Il+,IC[C@\6/[@6Il+,14,replace ( at position 4 with @,flow_matching,0.3,2.0,31,145
78,replace,1.0,3,C,IC[C@\6/[@6Il+,I3[C@\6/[@6Il+,14,replace C at position 1 with 3,flow_matching,0.3,2.0,31,145
79,add,2.0,O,,I3[C@\6/[@6Il+,I3O[C@\6/[@6Il+,15,add O at position 2,flow_matching,0.3,2.0,31,145
80,replace,2.0,1,O,I3O[C@\6/[@6Il+,I31[C@\6/[@6Il+,15,replace O at position 2 with 1,flow_matching,0.3,2.0,31,145
81,add,15.0,N,,I31[C@\6/[@6Il+,I31[C@\6/[@6Il+N,16,add N at position 15,flow_matching,0.3,2.0,31,145
82,add,3.0,[,,I31[C@\6/[@6Il+N,I31[[C@\6/[@6Il+N,17,add [ at position 3,flow_matching,0.3,2.0,31,145
83,remove,1.0,3,,I31[[C@\6/[@6Il+N,I1[[C@\6/[@6Il+N,16,remove 3 from position 1,flow_matching,0.3,2.0,31,145
84,replace,1.0,C,1,I1[[C@\6/[@6Il+N,IC[[C@\6/[@6Il+N,16,replace 1 at position 1 with C,flow_matching,0.3,2.0,31,145
85,add,6.0,(,,IC[[C@\6/[@6Il+N,IC[[C@(\6/[@6Il+N,17,add ( at position 6,flow_matching,0.3,2.0,31,145
86,replace,3.0,C,[,IC[[C@(\6/[@6Il+N,IC[CC@(\6/[@6Il+N,17,replace [ at position 3 with C,flow_matching,0.3,2.0,31,145
87,remove,13.0,I,,IC[CC@(\6/[@6Il+N,IC[CC@(\6/[@6l+N,16,remove I from position 13,flow_matching,0.3,2.0,31,145
88,replace,4.0,@,C,IC[CC@(\6/[@6l+N,IC[C@@(\6/[@6l+N,16,replace C at position 4 with @,flow_matching,0.3,2.0,31,145
89,add,6.0,o,,IC[C@@(\6/[@6l+N,IC[C@@o(\6/[@6l+N,17,add o at position 6,flow_matching,0.3,2.0,31,145
90,remove,1.0,C,,IC[C@@o(\6/[@6l+N,I[C@@o(\6/[@6l+N,16,remove C from position 1,flow_matching,0.3,2.0,31,145
91,replace,1.0,C,[,I[C@@o(\6/[@6l+N,ICC@@o(\6/[@6l+N,16,replace [ at position 1 with C,flow_matching,0.3,2.0,31,145
92,replace,2.0,[,C,ICC@@o(\6/[@6l+N,IC[@@o(\6/[@6l+N,16,replace C at position 2 with [,flow_matching,0.3,2.0,31,145
93,replace,2.0,\,[,IC[@@o(\6/[@6l+N,IC\@@o(\6/[@6l+N,16,replace [ at position 2 with \,flow_matching,0.3,2.0,31,145
94,add,9.0,+,,IC\@@o(\6/[@6l+N,IC\@@o(\6+/[@6l+N,17,add + at position 9,flow_matching,0.3,2.0,31,145
95,add,1.0,\,,IC\@@o(\6+/[@6l+N,I\C\@@o(\6+/[@6l+N,18,add \ at position 1,flow_matching,0.3,2.0,31,145
96,add,1.0,),,I\C\@@o(\6+/[@6l+N,I)\C\@@o(\6+/[@6l+N,19,add ) at position 1,flow_matching,0.3,2.0,31,145
97,replace,1.0,C,),I)\C\@@o(\6+/[@6l+N,IC\C\@@o(\6+/[@6l+N,19,replace ) at position 1 with C,flow_matching,0.3,2.0,31,145
98,replace,2.0,[,\,IC\C\@@o(\6+/[@6l+N,IC[C\@@o(\6+/[@6l+N,19,replace \ at position 2 with [,flow_matching,0.3,2.0,31,145
99,replace,0.0,),I,IC[C\@@o(\6+/[@6l+N,)C[C\@@o(\6+/[@6l+N,19,replace I at position 0 with ),flow_matching,0.3,2.0,31,145
100,add,2.0,=,,)C[C\@@o(\6+/[@6l+N,)C=[C\@@o(\6+/[@6l+N,20,add = at position 2,flow_matching,0.3,2.0,31,145
101,add,20.0,),,)C=[C\@@o(\6+/[@6l+N,)C=[C\@@o(\6+/[@6l+N),21,add ) at position 20,flow_matching,0.3,2.0,31,145
102,replace,12.0,=,+,)C=[C\@@o(\6+/[@6l+N),)C=[C\@@o(\6=/[@6l+N),21,replace + at position 12 with =,flow_matching,0.3,2.0,31,145
103,add,2.0,+,,)C=[C\@@o(\6=/[@6l+N),)C+=[C\@@o(\6=/[@6l+N),22,add + at position 2,flow_matching,0.3,2.0,31,145
104,replace,0.0,I,),)C+=[C\@@o(\6=/[@6l+N),IC+=[C\@@o(\6=/[@6l+N),22,replace ) at position 0 with I,flow_matching,0.3,2.0,31,145
105,replace,3.0,[,=,IC+=[C\@@o(\6=/[@6l+N),IC+[[C\@@o(\6=/[@6l+N),22,replace = at position 3 with [,flow_matching,0.3,2.0,31,145
106,add,5.0,H,,IC+[[C\@@o(\6=/[@6l+N),IC+[[HC\@@o(\6=/[@6l+N),23,add H at position 5,flow_matching,0.3,2.0,31,145
107,replace,2.0,[,+,IC+[[HC\@@o(\6=/[@6l+N),IC[[[HC\@@o(\6=/[@6l+N),23,replace + at position 2 with [,flow_matching,0.3,2.0,31,145
108,replace,3.0,C,[,IC[[[HC\@@o(\6=/[@6l+N),IC[C[HC\@@o(\6=/[@6l+N),23,replace [ at position 3 with C,flow_matching,0.3,2.0,31,145
109,add,2.0,s,,IC[C[HC\@@o(\6=/[@6l+N),ICs[C[HC\@@o(\6=/[@6l+N),24,add s at position 2,flow_matching,0.3,2.0,31,145
110,replace,2.0,[,s,ICs[C[HC\@@o(\6=/[@6l+N),IC[[C[HC\@@o(\6=/[@6l+N),24,replace s at position 2 with [,flow_matching,0.3,2.0,31,145
111,replace,3.0,C,[,IC[[C[HC\@@o(\6=/[@6l+N),IC[CC[HC\@@o(\6=/[@6l+N),24,replace [ at position 3 with C,flow_matching,0.3,2.0,31,145
112,replace,11.0,I,o,IC[CC[HC\@@o(\6=/[@6l+N),IC[CC[HC\@@I(\6=/[@6l+N),24,replace o at position 11 with I,flow_matching,0.3,2.0,31,145
113,remove,11.0,I,,IC[CC[HC\@@I(\6=/[@6l+N),IC[CC[HC\@@(\6=/[@6l+N),23,remove I from position 11,flow_matching,0.3,2.0,31,145
114,replace,5.0,1,[,IC[CC[HC\@@(\6=/[@6l+N),IC[CC1HC\@@(\6=/[@6l+N),23,replace [ at position 5 with 1,flow_matching,0.3,2.0,31,145
115,remove,1.0,C,,IC[CC1HC\@@(\6=/[@6l+N),I[CC1HC\@@(\6=/[@6l+N),22,remove C from position 1,flow_matching,0.3,2.0,31,145
116,add,22.0,N,,I[CC1HC\@@(\6=/[@6l+N),I[CC1HC\@@(\6=/[@6l+N)N,23,add N at position 22,flow_matching,0.3,2.0,31,145
117,replace,1.0,C,[,I[CC1HC\@@(\6=/[@6l+N)N,ICCC1HC\@@(\6=/[@6l+N)N,23,replace [ at position 1 with C,flow_matching,0.3,2.0,31,145
118,replace,2.0,[,C,ICCC1HC\@@(\6=/[@6l+N)N,IC[C1HC\@@(\6=/[@6l+N)N,23,replace C at position 2 with [,flow_matching,0.3,2.0,31,145
119,replace,4.0,@,1,IC[C1HC\@@(\6=/[@6l+N)N,IC[C@HC\@@(\6=/[@6l+N)N,23,replace 1 at position 4 with @,flow_matching,0.3,2.0,31,145
120,replace,5.0,@,H,IC[C@HC\@@(\6=/[@6l+N)N,IC[C@@C\@@(\6=/[@6l+N)N,23,replace H at position 5 with @,flow_matching,0.3,2.0,31,145
121,replace,6.0,H,C,IC[C@@C\@@(\6=/[@6l+N)N,IC[C@@H\@@(\6=/[@6l+N)N,23,replace C at position 6 with H,flow_matching,0.3,2.0,31,145
122,replace,7.0,],\,IC[C@@H\@@(\6=/[@6l+N)N,IC[C@@H]@@(\6=/[@6l+N)N,23,replace \ at position 7 with ],flow_matching,0.3,2.0,31,145
123,replace,8.0,1,@,IC[C@@H]@@(\6=/[@6l+N)N,IC[C@@H]1@(\6=/[@6l+N)N,23,replace @ at position 8 with 1,flow_matching,0.3,2.0,31,145
124,replace,9.0,C,@,IC[C@@H]1@(\6=/[@6l+N)N,IC[C@@H]1C(\6=/[@6l+N)N,23,replace @ at position 9 with C,flow_matching,0.3,2.0,31,145
125,replace,10.0,n,(,IC[C@@H]1C(\6=/[@6l+N)N,IC[C@@H]1Cn\6=/[@6l+N)N,23,replace ( at position 10 with n,flow_matching,0.3,2.0,31,145
126,replace,11.0,2,\,IC[C@@H]1Cn\6=/[@6l+N)N,IC[C@@H]1Cn26=/[@6l+N)N,23,replace \ at position 11 with 2,flow_matching,0.3,2.0,31,145
127,replace,12.0,c,6,IC[C@@H]1Cn26=/[@6l+N)N,IC[C@@H]1Cn2c=/[@6l+N)N,23,replace 6 at position 12 with c,flow_matching,0.3,2.0,31,145
128,replace,13.0,(,=,IC[C@@H]1Cn2c=/[@6l+N)N,IC[C@@H]1Cn2c(/[@6l+N)N,23,replace = at position 13 with (,flow_matching,0.3,2.0,31,145
129,replace,14.0,n,/,IC[C@@H]1Cn2c(/[@6l+N)N,IC[C@@H]1Cn2c(n[@6l+N)N,23,replace / at position 14 with n,flow_matching,0.3,2.0,31,145
130,replace,15.0,n,[,IC[C@@H]1Cn2c(n[@6l+N)N,IC[C@@H]1Cn2c(nn@6l+N)N,23,replace [ at position 15 with n,flow_matching,0.3,2.0,31,145
131,replace,16.0,c,@,IC[C@@H]1Cn2c(nn@6l+N)N,IC[C@@H]1Cn2c(nnc6l+N)N,23,replace @ at position 16 with c,flow_matching,0.3,2.0,31,145
132,replace,17.0,2,6,IC[C@@H]1Cn2c(nnc6l+N)N,IC[C@@H]1Cn2c(nnc2l+N)N,23,replace 6 at position 17 with 2,flow_matching,0.3,2.0,31,145
133,replace,18.0,-,l,IC[C@@H]1Cn2c(nnc2l+N)N,IC[C@@H]1Cn2c(nnc2-+N)N,23,replace l at position 18 with -,flow_matching,0.3,2.0,31,145
134,replace,19.0,c,+,IC[C@@H]1Cn2c(nnc2-+N)N,IC[C@@H]1Cn2c(nnc2-cN)N,23,replace + at position 19 with c,flow_matching,0.3,2.0,31,145
135,replace,20.0,2,N,IC[C@@H]1Cn2c(nnc2-cN)N,IC[C@@H]1Cn2c(nnc2-c2)N,23,replace N at position 20 with 2,flow_matching,0.3,2.0,31,145
136,replace,21.0,c,),IC[C@@H]1Cn2c(nnc2-c2)N,IC[C@@H]1Cn2c(nnc2-c2cN,23,replace ) at position 21 with c,flow_matching,0.3,2.0,31,145
137,replace,22.0,c,N,IC[C@@H]1Cn2c(nnc2-c2cN,IC[C@@H]1Cn2c(nnc2-c2cc,23,replace N at position 22 with c,flow_matching,0.3,2.0,31,145
138,add,23.0,n,,IC[C@@H]1Cn2c(nnc2-c2cc,IC[C@@H]1Cn2c(nnc2-c2ccn,24,add n at position 23,flow_matching,0.3,2.0,31,145
139,add,24.0,c,,IC[C@@H]1Cn2c(nnc2-c2ccn,IC[C@@H]1Cn2c(nnc2-c2ccnc,25,add c at position 24,flow_matching,0.3,2.0,31,145
140,add,25.0,c,,IC[C@@H]1Cn2c(nnc2-c2ccnc,IC[C@@H]1Cn2c(nnc2-c2ccncc,26,add c at position 25,flow_matching,0.3,2.0,31,145
141,add,26.0,2,,IC[C@@H]1Cn2c(nnc2-c2ccncc,IC[C@@H]1Cn2c(nnc2-c2ccncc2,27,add 2 at position 26,flow_matching,0.3,2.0,31,145
142,add,27.0,),,IC[C@@H]1Cn2c(nnc2-c2ccncc2,IC[C@@H]1Cn2c(nnc2-c2ccncc2),28,add ) at position 27,flow_matching,0.3,2.0,31,145
143,add,28.0,S,,IC[C@@H]1Cn2c(nnc2-c2ccncc2),IC[C@@H]1Cn2c(nnc2-c2ccncc2)S,29,add S at position 28,flow_matching,0.3,2.0,31,145
144,add,29.0,1,,IC[C@@H]1Cn2c(nnc2-c2ccncc2)S,IC[C@@H]1Cn2c(nnc2-c2ccncc2)S1,30,add 1 at position 29,flow_matching,0.3,2.0,31,145
145,add,30.0,"
",,IC[C@@H]1Cn2c(nnc2-c2ccncc2)S1,"IC[C@@H]1Cn2c(nnc2-c2ccncc2)S1
",31,"add 
 at position 30",flow_matching,0.3,2.0,31,145
