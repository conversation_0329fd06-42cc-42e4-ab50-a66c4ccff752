step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,39,88
1,add,0.0,C,,,C,1,add <PERSON> at position 0,flow_matching,0.3,2.0,39,88
2,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,39,88
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,39,88
4,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,39,88
5,add,0.0,B,,CO,BCO,3,add B at position 0,flow_matching,0.3,2.0,39,88
6,add,0.0,],,BCO,]BCO,4,add ] at position 0,flow_matching,0.3,2.0,39,88
7,remove,0.0,],,]BCO,BCO,3,remove ] from position 0,flow_matching,0.3,2.0,39,88
8,add,1.0,5,,BCO,B5<PERSON>,4,add 5 at position 1,flow_matching,0.3,2.0,39,88
9,replace,0.0,1,B,B5CO,15CO,4,replace B at position 0 with 1,flow_matching,0.3,2.0,39,88
10,add,0.0,I,,15CO,I15CO,5,add I at position 0,flow_matching,0.3,2.0,39,88
11,replace,3.0,5,C,I15CO,I155O,5,replace C at position 3 with 5,flow_matching,0.3,2.0,39,88
12,add,0.0,o,,I155O,oI155O,6,add o at position 0,flow_matching,0.3,2.0,39,88
13,replace,0.0,C,o,oI155O,CI155O,6,replace o at position 0 with C,flow_matching,0.3,2.0,39,88
14,remove,0.0,C,,CI155O,I155O,5,remove C from position 0,flow_matching,0.3,2.0,39,88
15,remove,1.0,1,,I155O,I55O,4,remove 1 from position 1,flow_matching,0.3,2.0,39,88
16,replace,0.0,C,I,I55O,C55O,4,replace I at position 0 with C,flow_matching,0.3,2.0,39,88
17,remove,1.0,5,,C55O,C5O,3,remove 5 from position 1,flow_matching,0.3,2.0,39,88
18,replace,2.0,@,O,C5O,C5@,3,replace O at position 2 with @,flow_matching,0.3,2.0,39,88
19,replace,2.0,S,@,C5@,C5S,3,replace @ at position 2 with S,flow_matching,0.3,2.0,39,88
20,remove,1.0,5,,C5S,CS,2,remove 5 from position 1,flow_matching,0.3,2.0,39,88
21,add,0.0,1,,CS,1CS,3,add 1 at position 0,flow_matching,0.3,2.0,39,88
22,replace,0.0,C,1,1CS,CCS,3,replace 1 at position 0 with C,flow_matching,0.3,2.0,39,88
23,remove,2.0,S,,CCS,CC,2,remove S from position 2,flow_matching,0.3,2.0,39,88
24,replace,1.0,O,C,CC,CO,2,replace C at position 1 with O,flow_matching,0.3,2.0,39,88
25,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,39,88
26,add,1.0,I,,COc,CIOc,4,add I at position 1,flow_matching,0.3,2.0,39,88
27,replace,3.0,2,c,CIOc,CIO2,4,replace c at position 3 with 2,flow_matching,0.3,2.0,39,88
28,add,3.0,),,CIO2,CIO)2,5,add ) at position 3,flow_matching,0.3,2.0,39,88
29,add,1.0,O,,CIO)2,COIO)2,6,add O at position 1,flow_matching,0.3,2.0,39,88
30,replace,2.0,c,I,COIO)2,COcO)2,6,replace I at position 2 with c,flow_matching,0.3,2.0,39,88
31,replace,5.0,c,2,COcO)2,COcO)c,6,replace 2 at position 5 with c,flow_matching,0.3,2.0,39,88
32,add,2.0,],,COcO)c,CO]cO)c,7,add ] at position 2,flow_matching,0.3,2.0,39,88
33,remove,5.0,),,CO]cO)c,CO]cOc,6,remove ) from position 5,flow_matching,0.3,2.0,39,88
34,remove,5.0,c,,CO]cOc,CO]cO,5,remove c from position 5,flow_matching,0.3,2.0,39,88
35,add,1.0,H,,CO]cO,CHO]cO,6,add H at position 1,flow_matching,0.3,2.0,39,88
36,remove,1.0,H,,CHO]cO,CO]cO,5,remove H from position 1,flow_matching,0.3,2.0,39,88
37,replace,2.0,c,],CO]cO,COccO,5,replace ] at position 2 with c,flow_matching,0.3,2.0,39,88
38,replace,3.0,1,c,COccO,COc1O,5,replace c at position 3 with 1,flow_matching,0.3,2.0,39,88
39,replace,4.0,c,O,COc1O,COc1c,5,replace O at position 4 with c,flow_matching,0.3,2.0,39,88
40,add,5.0,c,,COc1c,COc1cc,6,add c at position 5,flow_matching,0.3,2.0,39,88
41,replace,2.0,],c,COc1cc,CO]1cc,6,replace c at position 2 with ],flow_matching,0.3,2.0,39,88
42,add,6.0,),,CO]1cc,CO]1cc),7,add ) at position 6,flow_matching,0.3,2.0,39,88
43,remove,6.0,),,CO]1cc),CO]1cc,6,remove ) from position 6,flow_matching,0.3,2.0,39,88
44,add,6.0,6,,CO]1cc,CO]1cc6,7,add 6 at position 6,flow_matching,0.3,2.0,39,88
45,replace,2.0,c,],CO]1cc6,COc1cc6,7,replace ] at position 2 with c,flow_matching,0.3,2.0,39,88
46,remove,2.0,c,,COc1cc6,CO1cc6,6,remove c from position 2,flow_matching,0.3,2.0,39,88
47,replace,4.0,6,c,CO1cc6,CO1c66,6,replace c at position 4 with 6,flow_matching,0.3,2.0,39,88
48,add,2.0,o,,CO1c66,COo1c66,7,add o at position 2,flow_matching,0.3,2.0,39,88
49,remove,2.0,o,,COo1c66,CO1c66,6,remove o from position 2,flow_matching,0.3,2.0,39,88
50,add,5.0,r,,CO1c66,CO1c6r6,7,add r at position 5,flow_matching,0.3,2.0,39,88
51,add,5.0,r,,CO1c6r6,CO1c6rr6,8,add r at position 5,flow_matching,0.3,2.0,39,88
52,replace,2.0,c,1,CO1c6rr6,COcc6rr6,8,replace 1 at position 2 with c,flow_matching,0.3,2.0,39,88
53,replace,3.0,1,c,COcc6rr6,COc16rr6,8,replace c at position 3 with 1,flow_matching,0.3,2.0,39,88
54,replace,4.0,c,6,COc16rr6,COc1crr6,8,replace 6 at position 4 with c,flow_matching,0.3,2.0,39,88
55,replace,5.0,c,r,COc1crr6,COc1ccr6,8,replace r at position 5 with c,flow_matching,0.3,2.0,39,88
56,replace,6.0,c,r,COc1ccr6,COc1ccc6,8,replace r at position 6 with c,flow_matching,0.3,2.0,39,88
57,replace,7.0,2,6,COc1ccc6,COc1ccc2,8,replace 6 at position 7 with 2,flow_matching,0.3,2.0,39,88
58,add,8.0,c,,COc1ccc2,COc1ccc2c,9,add c at position 8,flow_matching,0.3,2.0,39,88
59,add,9.0,c,,COc1ccc2c,COc1ccc2cc,10,add c at position 9,flow_matching,0.3,2.0,39,88
60,add,10.0,(,,COc1ccc2cc,COc1ccc2cc(,11,add ( at position 10,flow_matching,0.3,2.0,39,88
61,add,11.0,C,,COc1ccc2cc(,COc1ccc2cc(C,12,add C at position 11,flow_matching,0.3,2.0,39,88
62,add,12.0,O,,COc1ccc2cc(C,COc1ccc2cc(CO,13,add O at position 12,flow_matching,0.3,2.0,39,88
63,add,13.0,C,,COc1ccc2cc(CO,COc1ccc2cc(COC,14,add C at position 13,flow_matching,0.3,2.0,39,88
64,add,14.0,(,,COc1ccc2cc(COC,COc1ccc2cc(COC(,15,add ( at position 14,flow_matching,0.3,2.0,39,88
65,add,15.0,=,,COc1ccc2cc(COC(,COc1ccc2cc(COC(=,16,add = at position 15,flow_matching,0.3,2.0,39,88
66,add,16.0,O,,COc1ccc2cc(COC(=,COc1ccc2cc(COC(=O,17,add O at position 16,flow_matching,0.3,2.0,39,88
67,add,17.0,),,COc1ccc2cc(COC(=O,COc1ccc2cc(COC(=O),18,add ) at position 17,flow_matching,0.3,2.0,39,88
68,add,18.0,C,,COc1ccc2cc(COC(=O),COc1ccc2cc(COC(=O)C,19,add C at position 18,flow_matching,0.3,2.0,39,88
69,add,19.0,O,,COc1ccc2cc(COC(=O)C,COc1ccc2cc(COC(=O)CO,20,add O at position 19,flow_matching,0.3,2.0,39,88
70,add,20.0,c,,COc1ccc2cc(COC(=O)CO,COc1ccc2cc(COC(=O)COc,21,add c at position 20,flow_matching,0.3,2.0,39,88
71,add,21.0,3,,COc1ccc2cc(COC(=O)COc,COc1ccc2cc(COC(=O)COc3,22,add 3 at position 21,flow_matching,0.3,2.0,39,88
72,add,22.0,c,,COc1ccc2cc(COC(=O)COc3,COc1ccc2cc(COC(=O)COc3c,23,add c at position 22,flow_matching,0.3,2.0,39,88
73,add,23.0,c,,COc1ccc2cc(COC(=O)COc3c,COc1ccc2cc(COC(=O)COc3cc,24,add c at position 23,flow_matching,0.3,2.0,39,88
74,add,24.0,c,,COc1ccc2cc(COC(=O)COc3cc,COc1ccc2cc(COC(=O)COc3ccc,25,add c at position 24,flow_matching,0.3,2.0,39,88
75,add,25.0,c,,COc1ccc2cc(COC(=O)COc3ccc,COc1ccc2cc(COC(=O)COc3cccc,26,add c at position 25,flow_matching,0.3,2.0,39,88
76,add,26.0,c,,COc1ccc2cc(COC(=O)COc3cccc,COc1ccc2cc(COC(=O)COc3ccccc,27,add c at position 26,flow_matching,0.3,2.0,39,88
77,add,27.0,3,,COc1ccc2cc(COC(=O)COc3ccccc,COc1ccc2cc(COC(=O)COc3ccccc3,28,add 3 at position 27,flow_matching,0.3,2.0,39,88
78,add,28.0,C,,COc1ccc2cc(COC(=O)COc3ccccc3,COc1ccc2cc(COC(=O)COc3ccccc3C,29,add C at position 28,flow_matching,0.3,2.0,39,88
79,add,29.0,#,,COc1ccc2cc(COC(=O)COc3ccccc3C,COc1ccc2cc(COC(=O)COc3ccccc3C#,30,add # at position 29,flow_matching,0.3,2.0,39,88
80,add,30.0,N,,COc1ccc2cc(COC(=O)COc3ccccc3C#,COc1ccc2cc(COC(=O)COc3ccccc3C#N,31,add N at position 30,flow_matching,0.3,2.0,39,88
81,add,31.0,),,COc1ccc2cc(COC(=O)COc3ccccc3C#N,COc1ccc2cc(COC(=O)COc3ccccc3C#N),32,add ) at position 31,flow_matching,0.3,2.0,39,88
82,add,32.0,c,,COc1ccc2cc(COC(=O)COc3ccccc3C#N),COc1ccc2cc(COC(=O)COc3ccccc3C#N)c,33,add c at position 32,flow_matching,0.3,2.0,39,88
83,add,33.0,c,,COc1ccc2cc(COC(=O)COc3ccccc3C#N)c,COc1ccc2cc(COC(=O)COc3ccccc3C#N)cc,34,add c at position 33,flow_matching,0.3,2.0,39,88
84,add,34.0,c,,COc1ccc2cc(COC(=O)COc3ccccc3C#N)cc,COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc,35,add c at position 34,flow_matching,0.3,2.0,39,88
85,add,35.0,2,,COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc,COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc2,36,add 2 at position 35,flow_matching,0.3,2.0,39,88
86,add,36.0,c,,COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc2,COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc2c,37,add c at position 36,flow_matching,0.3,2.0,39,88
87,add,37.0,1,,COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc2c,COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc2c1,38,add 1 at position 37,flow_matching,0.3,2.0,39,88
88,add,38.0,"
",,COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc2c1,"COc1ccc2cc(COC(=O)COc3ccccc3C#N)ccc2c1
",39,"add 
 at position 38",flow_matching,0.3,2.0,39,88
