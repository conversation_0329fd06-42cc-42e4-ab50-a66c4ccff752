step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,44,163
1,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,44,163
2,replace,0.0,C,I,I,C,1,replace I at position 0 with C,flow_matching,0.3,2.0,44,163
3,add,1.0,/,,C,C/,2,add / at position 1,flow_matching,0.3,2.0,44,163
4,replace,1.0,C,/,C/,CC,2,replace / at position 1 with C,flow_matching,0.3,2.0,44,163
5,add,2.0,c,,CC,CCc,3,add c at position 2,flow_matching,0.3,2.0,44,163
6,add,3.0,-,,CCc,CCc-,4,add - at position 3,flow_matching,0.3,2.0,44,163
7,replace,2.0,o,c,CCc-,CCo-,4,replace c at position 2 with o,flow_matching,0.3,2.0,44,163
8,replace,2.0,c,o,CCo-,CCc-,4,replace o at position 2 with c,flow_matching,0.3,2.0,44,163
9,add,2.0,C,,CCc-,CCCc-,5,add C at position 2,flow_matching,0.3,2.0,44,163
10,replace,4.0,[,-,CCCc-,CCCc[,5,replace - at position 4 with [,flow_matching,0.3,2.0,44,163
11,add,5.0,s,,CCCc[,CCCc[s,6,add s at position 5,flow_matching,0.3,2.0,44,163
12,add,3.0,2,,CCCc[s,CCC2c[s,7,add 2 at position 3,flow_matching,0.3,2.0,44,163
13,add,3.0,6,,CCC2c[s,CCC62c[s,8,add 6 at position 3,flow_matching,0.3,2.0,44,163
14,remove,2.0,C,,CCC62c[s,CC62c[s,7,remove C from position 2,flow_matching,0.3,2.0,44,163
15,remove,0.0,C,,CC62c[s,C62c[s,6,remove C from position 0,flow_matching,0.3,2.0,44,163
16,remove,5.0,s,,C62c[s,C62c[,5,remove s from position 5,flow_matching,0.3,2.0,44,163
17,replace,0.0,S,C,C62c[,S62c[,5,replace C at position 0 with S,flow_matching,0.3,2.0,44,163
18,remove,2.0,2,,S62c[,S6c[,4,remove 2 from position 2,flow_matching,0.3,2.0,44,163
19,replace,1.0,5,6,S6c[,S5c[,4,replace 6 at position 1 with 5,flow_matching,0.3,2.0,44,163
20,replace,0.0,C,S,S5c[,C5c[,4,replace S at position 0 with C,flow_matching,0.3,2.0,44,163
21,add,3.0,-,,C5c[,C5c-[,5,add - at position 3,flow_matching,0.3,2.0,44,163
22,add,0.0,N,,C5c-[,NC5c-[,6,add N at position 0,flow_matching,0.3,2.0,44,163
23,remove,4.0,-,,NC5c-[,NC5c[,5,remove - from position 4,flow_matching,0.3,2.0,44,163
24,remove,3.0,c,,NC5c[,NC5[,4,remove c from position 3,flow_matching,0.3,2.0,44,163
25,remove,0.0,N,,NC5[,C5[,3,remove N from position 0,flow_matching,0.3,2.0,44,163
26,remove,2.0,[,,C5[,C5,2,remove [ from position 2,flow_matching,0.3,2.0,44,163
27,add,2.0,),,C5,C5),3,add ) at position 2,flow_matching,0.3,2.0,44,163
28,remove,0.0,C,,C5),5),2,remove C from position 0,flow_matching,0.3,2.0,44,163
29,replace,0.0,C,5,5),C),2,replace 5 at position 0 with C,flow_matching,0.3,2.0,44,163
30,replace,1.0,\,),C),C\,2,replace ) at position 1 with \,flow_matching,0.3,2.0,44,163
31,replace,1.0,C,\,C\,CC,2,replace \ at position 1 with C,flow_matching,0.3,2.0,44,163
32,add,2.0,c,,CC,CCc,3,add c at position 2,flow_matching,0.3,2.0,44,163
33,add,3.0,1,,CCc,CCc1,4,add 1 at position 3,flow_matching,0.3,2.0,44,163
34,add,4.0,c,,CCc1,CCc1c,5,add c at position 4,flow_matching,0.3,2.0,44,163
35,remove,4.0,c,,CCc1c,CCc1,4,remove c from position 4,flow_matching,0.3,2.0,44,163
36,add,4.0,c,,CCc1,CCc1c,5,add c at position 4,flow_matching,0.3,2.0,44,163
37,add,3.0,2,,CCc1c,CCc21c,6,add 2 at position 3,flow_matching,0.3,2.0,44,163
38,add,4.0,2,,CCc21c,CCc221c,7,add 2 at position 4,flow_matching,0.3,2.0,44,163
39,remove,5.0,1,,CCc221c,CCc22c,6,remove 1 from position 5,flow_matching,0.3,2.0,44,163
40,add,4.0,S,,CCc22c,CCc2S2c,7,add S at position 4,flow_matching,0.3,2.0,44,163
41,replace,0.0,6,C,CCc2S2c,6Cc2S2c,7,replace C at position 0 with 6,flow_matching,0.3,2.0,44,163
42,remove,3.0,2,,6Cc2S2c,6CcS2c,6,remove 2 from position 3,flow_matching,0.3,2.0,44,163
43,remove,4.0,2,,6CcS2c,6CcSc,5,remove 2 from position 4,flow_matching,0.3,2.0,44,163
44,add,3.0,6,,6CcSc,6Cc6Sc,6,add 6 at position 3,flow_matching,0.3,2.0,44,163
45,add,1.0,s,,6Cc6Sc,6sCc6Sc,7,add s at position 1,flow_matching,0.3,2.0,44,163
46,replace,0.0,C,6,6sCc6Sc,CsCc6Sc,7,replace 6 at position 0 with C,flow_matching,0.3,2.0,44,163
47,replace,1.0,C,s,CsCc6Sc,CCCc6Sc,7,replace s at position 1 with C,flow_matching,0.3,2.0,44,163
48,remove,5.0,S,,CCCc6Sc,CCCc6c,6,remove S from position 5,flow_matching,0.3,2.0,44,163
49,replace,3.0,-,c,CCCc6c,CCC-6c,6,replace c at position 3 with -,flow_matching,0.3,2.0,44,163
50,remove,2.0,C,,CCC-6c,CC-6c,5,remove C from position 2,flow_matching,0.3,2.0,44,163
51,add,4.0,@,,CC-6c,CC-6@c,6,add @ at position 4,flow_matching,0.3,2.0,44,163
52,replace,0.0,s,C,CC-6@c,sC-6@c,6,replace C at position 0 with s,flow_matching,0.3,2.0,44,163
53,add,3.0,n,,sC-6@c,sC-n6@c,7,add n at position 3,flow_matching,0.3,2.0,44,163
54,remove,4.0,6,,sC-n6@c,sC-n@c,6,remove 6 from position 4,flow_matching,0.3,2.0,44,163
55,replace,2.0,+,-,sC-n@c,sC+n@c,6,replace - at position 2 with +,flow_matching,0.3,2.0,44,163
56,add,2.0,=,,sC+n@c,sC=+n@c,7,add = at position 2,flow_matching,0.3,2.0,44,163
57,replace,2.0,7,=,sC=+n@c,sC7+n@c,7,replace = at position 2 with 7,flow_matching,0.3,2.0,44,163
58,remove,3.0,+,,sC7+n@c,sC7n@c,6,remove + from position 3,flow_matching,0.3,2.0,44,163
59,remove,3.0,n,,sC7n@c,sC7@c,5,remove n from position 3,flow_matching,0.3,2.0,44,163
60,add,4.0,O,,sC7@c,sC7@Oc,6,add O at position 4,flow_matching,0.3,2.0,44,163
61,replace,0.0,=,s,sC7@Oc,=C7@Oc,6,replace s at position 0 with =,flow_matching,0.3,2.0,44,163
62,remove,2.0,7,,=C7@Oc,=C@Oc,5,remove 7 from position 2,flow_matching,0.3,2.0,44,163
63,replace,3.0,1,O,=C@Oc,=C@1c,5,replace O at position 3 with 1,flow_matching,0.3,2.0,44,163
64,add,0.0,+,,=C@1c,+=C@1c,6,add + at position 0,flow_matching,0.3,2.0,44,163
65,remove,1.0,=,,+=C@1c,+C@1c,5,remove = from position 1,flow_matching,0.3,2.0,44,163
66,add,5.0,),,+C@1c,+C@1c),6,add ) at position 5,flow_matching,0.3,2.0,44,163
67,replace,0.0,C,+,+C@1c),CC@1c),6,replace + at position 0 with C,flow_matching,0.3,2.0,44,163
68,replace,3.0,6,1,CC@1c),CC@6c),6,replace 1 at position 3 with 6,flow_matching,0.3,2.0,44,163
69,add,6.0,r,,CC@6c),CC@6c)r,7,add r at position 6,flow_matching,0.3,2.0,44,163
70,remove,1.0,C,,CC@6c)r,C@6c)r,6,remove C from position 1,flow_matching,0.3,2.0,44,163
71,replace,1.0,C,@,C@6c)r,CC6c)r,6,replace @ at position 1 with C,flow_matching,0.3,2.0,44,163
72,replace,2.0,c,6,CC6c)r,CCcc)r,6,replace 6 at position 2 with c,flow_matching,0.3,2.0,44,163
73,remove,3.0,c,,CCcc)r,CCc)r,5,remove c from position 3,flow_matching,0.3,2.0,44,163
74,replace,3.0,1,),CCc)r,CCc1r,5,replace ) at position 3 with 1,flow_matching,0.3,2.0,44,163
75,replace,0.0,6,C,CCc1r,6Cc1r,5,replace C at position 0 with 6,flow_matching,0.3,2.0,44,163
76,replace,0.0,C,6,6Cc1r,CCc1r,5,replace 6 at position 0 with C,flow_matching,0.3,2.0,44,163
77,add,0.0,4,,CCc1r,4CCc1r,6,add 4 at position 0,flow_matching,0.3,2.0,44,163
78,replace,0.0,C,4,4CCc1r,CCCc1r,6,replace 4 at position 0 with C,flow_matching,0.3,2.0,44,163
79,replace,2.0,c,C,CCCc1r,CCcc1r,6,replace C at position 2 with c,flow_matching,0.3,2.0,44,163
80,replace,2.0,5,c,CCcc1r,CC5c1r,6,replace c at position 2 with 5,flow_matching,0.3,2.0,44,163
81,remove,2.0,5,,CC5c1r,CCc1r,5,remove 5 from position 2,flow_matching,0.3,2.0,44,163
82,replace,4.0,c,r,CCc1r,CCc1c,5,replace r at position 4 with c,flow_matching,0.3,2.0,44,163
83,remove,0.0,C,,CCc1c,Cc1c,4,remove C from position 0,flow_matching,0.3,2.0,44,163
84,remove,1.0,c,,Cc1c,C1c,3,remove c from position 1,flow_matching,0.3,2.0,44,163
85,remove,2.0,c,,C1c,C1,2,remove c from position 2,flow_matching,0.3,2.0,44,163
86,remove,1.0,1,,C1,C,1,remove 1 from position 1,flow_matching,0.3,2.0,44,163
87,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,44,163
88,add,0.0,7,,CC,7CC,3,add 7 at position 0,flow_matching,0.3,2.0,44,163
89,replace,0.0,F,7,7CC,FCC,3,replace 7 at position 0 with F,flow_matching,0.3,2.0,44,163
90,replace,0.0,C,F,FCC,CCC,3,replace F at position 0 with C,flow_matching,0.3,2.0,44,163
91,add,0.0,H,,CCC,HCCC,4,add H at position 0,flow_matching,0.3,2.0,44,163
92,replace,0.0,C,H,HCCC,CCCC,4,replace H at position 0 with C,flow_matching,0.3,2.0,44,163
93,replace,2.0,c,C,CCCC,CCcC,4,replace C at position 2 with c,flow_matching,0.3,2.0,44,163
94,replace,0.0,B,C,CCcC,BCcC,4,replace C at position 0 with B,flow_matching,0.3,2.0,44,163
95,replace,0.0,@,B,BCcC,@CcC,4,replace B at position 0 with @,flow_matching,0.3,2.0,44,163
96,remove,3.0,C,,@CcC,@Cc,3,remove C from position 3,flow_matching,0.3,2.0,44,163
97,add,3.0,4,,@Cc,@Cc4,4,add 4 at position 3,flow_matching,0.3,2.0,44,163
98,remove,1.0,C,,@Cc4,@c4,3,remove C from position 1,flow_matching,0.3,2.0,44,163
99,remove,1.0,c,,@c4,@4,2,remove c from position 1,flow_matching,0.3,2.0,44,163
100,replace,0.0,C,@,@4,C4,2,replace @ at position 0 with C,flow_matching,0.3,2.0,44,163
101,remove,1.0,4,,C4,C,1,remove 4 from position 1,flow_matching,0.3,2.0,44,163
102,add,0.0,3,,C,3C,2,add 3 at position 0,flow_matching,0.3,2.0,44,163
103,replace,0.0,C,3,3C,CC,2,replace 3 at position 0 with C,flow_matching,0.3,2.0,44,163
104,add,2.0,c,,CC,CCc,3,add c at position 2,flow_matching,0.3,2.0,44,163
105,add,2.0,\,,CCc,CC\c,4,add \ at position 2,flow_matching,0.3,2.0,44,163
106,remove,0.0,C,,CC\c,C\c,3,remove C from position 0,flow_matching,0.3,2.0,44,163
107,replace,1.0,C,\,C\c,CCc,3,replace \ at position 1 with C,flow_matching,0.3,2.0,44,163
108,add,3.0,1,,CCc,CCc1,4,add 1 at position 3,flow_matching,0.3,2.0,44,163
109,remove,0.0,C,,CCc1,Cc1,3,remove C from position 0,flow_matching,0.3,2.0,44,163
110,add,1.0,#,,Cc1,C#c1,4,add # at position 1,flow_matching,0.3,2.0,44,163
111,remove,3.0,1,,C#c1,C#c,3,remove 1 from position 3,flow_matching,0.3,2.0,44,163
112,replace,1.0,C,#,C#c,CCc,3,replace # at position 1 with C,flow_matching,0.3,2.0,44,163
113,add,1.0,=,,CCc,C=Cc,4,add = at position 1,flow_matching,0.3,2.0,44,163
114,remove,2.0,C,,C=Cc,C=c,3,remove C from position 2,flow_matching,0.3,2.0,44,163
115,remove,2.0,c,,C=c,C=,2,remove c from position 2,flow_matching,0.3,2.0,44,163
116,replace,1.0,C,=,C=,CC,2,replace = at position 1 with C,flow_matching,0.3,2.0,44,163
117,replace,1.0,),C,CC,C),2,replace C at position 1 with ),flow_matching,0.3,2.0,44,163
118,remove,1.0,),,C),C,1,remove ) from position 1,flow_matching,0.3,2.0,44,163
119,replace,0.0,4,C,C,4,1,replace C at position 0 with 4,flow_matching,0.3,2.0,44,163
120,add,0.0,C,,4,C4,2,add C at position 0,flow_matching,0.3,2.0,44,163
121,replace,1.0,C,4,C4,CC,2,replace 4 at position 1 with C,flow_matching,0.3,2.0,44,163
122,add,2.0,c,,CC,CCc,3,add c at position 2,flow_matching,0.3,2.0,44,163
123,add,3.0,1,,CCc,CCc1,4,add 1 at position 3,flow_matching,0.3,2.0,44,163
124,add,4.0,c,,CCc1,CCc1c,5,add c at position 4,flow_matching,0.3,2.0,44,163
125,add,5.0,c,,CCc1c,CCc1cc,6,add c at position 5,flow_matching,0.3,2.0,44,163
126,add,6.0,c,,CCc1cc,CCc1ccc,7,add c at position 6,flow_matching,0.3,2.0,44,163
127,add,7.0,(,,CCc1ccc,CCc1ccc(,8,add ( at position 7,flow_matching,0.3,2.0,44,163
128,add,8.0,N,,CCc1ccc(,CCc1ccc(N,9,add N at position 8,flow_matching,0.3,2.0,44,163
129,add,9.0,C,,CCc1ccc(N,CCc1ccc(NC,10,add C at position 9,flow_matching,0.3,2.0,44,163
130,add,10.0,(,,CCc1ccc(NC,CCc1ccc(NC(,11,add ( at position 10,flow_matching,0.3,2.0,44,163
131,add,11.0,=,,CCc1ccc(NC(,CCc1ccc(NC(=,12,add = at position 11,flow_matching,0.3,2.0,44,163
132,add,12.0,O,,CCc1ccc(NC(=,CCc1ccc(NC(=O,13,add O at position 12,flow_matching,0.3,2.0,44,163
133,add,13.0,),,CCc1ccc(NC(=O,CCc1ccc(NC(=O),14,add ) at position 13,flow_matching,0.3,2.0,44,163
134,add,14.0,c,,CCc1ccc(NC(=O),CCc1ccc(NC(=O)c,15,add c at position 14,flow_matching,0.3,2.0,44,163
135,add,15.0,2,,CCc1ccc(NC(=O)c,CCc1ccc(NC(=O)c2,16,add 2 at position 15,flow_matching,0.3,2.0,44,163
136,add,16.0,n,,CCc1ccc(NC(=O)c2,CCc1ccc(NC(=O)c2n,17,add n at position 16,flow_matching,0.3,2.0,44,163
137,add,17.0,n,,CCc1ccc(NC(=O)c2n,CCc1ccc(NC(=O)c2nn,18,add n at position 17,flow_matching,0.3,2.0,44,163
138,add,18.0,(,,CCc1ccc(NC(=O)c2nn,CCc1ccc(NC(=O)c2nn(,19,add ( at position 18,flow_matching,0.3,2.0,44,163
139,add,19.0,-,,CCc1ccc(NC(=O)c2nn(,CCc1ccc(NC(=O)c2nn(-,20,add - at position 19,flow_matching,0.3,2.0,44,163
140,add,20.0,c,,CCc1ccc(NC(=O)c2nn(-,CCc1ccc(NC(=O)c2nn(-c,21,add c at position 20,flow_matching,0.3,2.0,44,163
141,add,21.0,3,,CCc1ccc(NC(=O)c2nn(-c,CCc1ccc(NC(=O)c2nn(-c3,22,add 3 at position 21,flow_matching,0.3,2.0,44,163
142,add,22.0,c,,CCc1ccc(NC(=O)c2nn(-c3,CCc1ccc(NC(=O)c2nn(-c3c,23,add c at position 22,flow_matching,0.3,2.0,44,163
143,add,23.0,c,,CCc1ccc(NC(=O)c2nn(-c3c,CCc1ccc(NC(=O)c2nn(-c3cc,24,add c at position 23,flow_matching,0.3,2.0,44,163
144,add,24.0,c,,CCc1ccc(NC(=O)c2nn(-c3cc,CCc1ccc(NC(=O)c2nn(-c3ccc,25,add c at position 24,flow_matching,0.3,2.0,44,163
145,add,25.0,(,,CCc1ccc(NC(=O)c2nn(-c3ccc,CCc1ccc(NC(=O)c2nn(-c3ccc(,26,add ( at position 25,flow_matching,0.3,2.0,44,163
146,add,26.0,C,,CCc1ccc(NC(=O)c2nn(-c3ccc(,CCc1ccc(NC(=O)c2nn(-c3ccc(C,27,add C at position 26,flow_matching,0.3,2.0,44,163
147,add,27.0,C,,CCc1ccc(NC(=O)c2nn(-c3ccc(C,CCc1ccc(NC(=O)c2nn(-c3ccc(CC,28,add C at position 27,flow_matching,0.3,2.0,44,163
148,add,28.0,),,CCc1ccc(NC(=O)c2nn(-c3ccc(CC,CCc1ccc(NC(=O)c2nn(-c3ccc(CC),29,add ) at position 28,flow_matching,0.3,2.0,44,163
149,add,29.0,c,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC),CCc1ccc(NC(=O)c2nn(-c3ccc(CC)c,30,add c at position 29,flow_matching,0.3,2.0,44,163
150,add,30.0,c,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)c,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc,31,add c at position 30,flow_matching,0.3,2.0,44,163
151,add,31.0,3,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3,32,add 3 at position 31,flow_matching,0.3,2.0,44,163
152,add,32.0,),,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3),33,add ) at position 32,flow_matching,0.3,2.0,44,163
153,add,33.0,c,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3),CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)c,34,add c at position 33,flow_matching,0.3,2.0,44,163
154,add,34.0,c,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)c,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)cc,35,add c at position 34,flow_matching,0.3,2.0,44,163
155,add,35.0,c,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)cc,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc,36,add c at position 35,flow_matching,0.3,2.0,44,163
156,add,36.0,2,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2,37,add 2 at position 36,flow_matching,0.3,2.0,44,163
157,add,37.0,=,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=,38,add = at position 37,flow_matching,0.3,2.0,44,163
158,add,38.0,O,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O,39,add O at position 38,flow_matching,0.3,2.0,44,163
159,add,39.0,),,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O),40,add ) at position 39,flow_matching,0.3,2.0,44,163
160,add,40.0,c,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O),CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O)c,41,add c at position 40,flow_matching,0.3,2.0,44,163
161,add,41.0,c,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O)c,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O)cc,42,add c at position 41,flow_matching,0.3,2.0,44,163
162,add,42.0,1,,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O)cc,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O)cc1,43,add 1 at position 42,flow_matching,0.3,2.0,44,163
163,add,43.0,"
",,CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O)cc1,"CCc1ccc(NC(=O)c2nn(-c3ccc(CC)cc3)ccc2=O)cc1
",44,"add 
 at position 43",flow_matching,0.3,2.0,44,163
