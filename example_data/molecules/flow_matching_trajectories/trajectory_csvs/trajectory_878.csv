step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,31,80
1,add,0.0,#,,,#,1,add # at position 0,flow_matching,0.3,2.0,31,80
2,add,0.0,(,,#,(#,2,add ( at position 0,flow_matching,0.3,2.0,31,80
3,add,1.0,#,,(#,(##,3,add # at position 1,flow_matching,0.3,2.0,31,80
4,add,0.0,O,,(##,O(##,4,add O at position 0,flow_matching,0.3,2.0,31,80
5,add,3.0,(,,O(##,O(#(#,5,add ( at position 3,flow_matching,0.3,2.0,31,80
6,replace,0.0,C,O,O(#(#,C(#(#,5,replace O at position 0 with C,flow_matching,0.3,2.0,31,80
7,replace,1.0,C,(,C(#(#,CC#(#,5,replace ( at position 1 with C,flow_matching,0.3,2.0,31,80
8,replace,2.0,(,#,CC#(#,CC((#,5,replace # at position 2 with (,flow_matching,0.3,2.0,31,80
9,replace,3.0,6,(,CC((#,CC(6#,5,replace ( at position 3 with 6,flow_matching,0.3,2.0,31,80
10,add,3.0,@,,CC(6#,CC(@6#,6,add @ at position 3,flow_matching,0.3,2.0,31,80
11,add,3.0,n,,CC(@6#,CC(n@6#,7,add n at position 3,flow_matching,0.3,2.0,31,80
12,replace,3.0,C,n,CC(n@6#,CC(C@6#,7,replace n at position 3 with C,flow_matching,0.3,2.0,31,80
13,replace,4.0,),@,CC(C@6#,CC(C)6#,7,replace @ at position 4 with ),flow_matching,0.3,2.0,31,80
14,add,3.0,F,,CC(C)6#,CC(FC)6#,8,add F at position 3,flow_matching,0.3,2.0,31,80
15,add,0.0,O,,CC(FC)6#,OCC(FC)6#,9,add O at position 0,flow_matching,0.3,2.0,31,80
16,replace,0.0,C,O,OCC(FC)6#,CCC(FC)6#,9,replace O at position 0 with C,flow_matching,0.3,2.0,31,80
17,replace,2.0,(,C,CCC(FC)6#,CC((FC)6#,9,replace C at position 2 with (,flow_matching,0.3,2.0,31,80
18,replace,8.0,=,#,CC((FC)6#,CC((FC)6=,9,replace # at position 8 with =,flow_matching,0.3,2.0,31,80
19,add,1.0,5,,CC((FC)6=,C5C((FC)6=,10,add 5 at position 1,flow_matching,0.3,2.0,31,80
20,replace,1.0,C,5,C5C((FC)6=,CCC((FC)6=,10,replace 5 at position 1 with C,flow_matching,0.3,2.0,31,80
21,replace,4.0,H,(,CCC((FC)6=,CCC(HFC)6=,10,replace ( at position 4 with H,flow_matching,0.3,2.0,31,80
22,remove,2.0,C,,CCC(HFC)6=,CC(HFC)6=,9,remove C from position 2,flow_matching,0.3,2.0,31,80
23,replace,3.0,C,H,CC(HFC)6=,CC(CFC)6=,9,replace H at position 3 with C,flow_matching,0.3,2.0,31,80
24,add,8.0,r,,CC(CFC)6=,CC(CFC)6r=,10,add r at position 8,flow_matching,0.3,2.0,31,80
25,replace,4.0,),F,CC(CFC)6r=,CC(C)C)6r=,10,replace F at position 4 with ),flow_matching,0.3,2.0,31,80
26,replace,4.0,4,),CC(C)C)6r=,CC(C4C)6r=,10,replace ) at position 4 with 4,flow_matching,0.3,2.0,31,80
27,replace,4.0,),4,CC(C4C)6r=,CC(C)C)6r=,10,replace 4 at position 4 with ),flow_matching,0.3,2.0,31,80
28,replace,6.0,N,),CC(C)C)6r=,CC(C)CN6r=,10,replace ) at position 6 with N,flow_matching,0.3,2.0,31,80
29,replace,7.0,C,6,CC(C)CN6r=,CC(C)CNCr=,10,replace 6 at position 7 with C,flow_matching,0.3,2.0,31,80
30,replace,0.0,-,C,CC(C)CNCr=,-C(C)CNCr=,10,replace C at position 0 with -,flow_matching,0.3,2.0,31,80
31,replace,0.0,C,-,-C(C)CNCr=,CC(C)CNCr=,10,replace - at position 0 with C,flow_matching,0.3,2.0,31,80
32,remove,2.0,(,,CC(C)CNCr=,CCC)CNCr=,9,remove ( from position 2,flow_matching,0.3,2.0,31,80
33,add,0.0,H,,CCC)CNCr=,HCCC)CNCr=,10,add H at position 0,flow_matching,0.3,2.0,31,80
34,replace,0.0,C,H,HCCC)CNCr=,CCCC)CNCr=,10,replace H at position 0 with C,flow_matching,0.3,2.0,31,80
35,replace,2.0,(,C,CCCC)CNCr=,CC(C)CNCr=,10,replace C at position 2 with (,flow_matching,0.3,2.0,31,80
36,replace,5.0,-,C,CC(C)CNCr=,CC(C)-NCr=,10,replace C at position 5 with -,flow_matching,0.3,2.0,31,80
37,replace,5.0,C,-,CC(C)-NCr=,CC(C)CNCr=,10,replace - at position 5 with C,flow_matching,0.3,2.0,31,80
38,replace,4.0,C,),CC(C)CNCr=,CC(CCCNCr=,10,replace ) at position 4 with C,flow_matching,0.3,2.0,31,80
39,add,4.0,o,,CC(CCCNCr=,CC(CoCCNCr=,11,add o at position 4,flow_matching,0.3,2.0,31,80
40,replace,4.0,),o,CC(CoCCNCr=,CC(C)CCNCr=,11,replace o at position 4 with ),flow_matching,0.3,2.0,31,80
41,remove,10.0,=,,CC(C)CCNCr=,CC(C)CCNCr,10,remove = from position 10,flow_matching,0.3,2.0,31,80
42,add,9.0,I,,CC(C)CCNCr,CC(C)CCNCIr,11,add I at position 9,flow_matching,0.3,2.0,31,80
43,remove,6.0,C,,CC(C)CCNCIr,CC(C)CNCIr,10,remove C from position 6,flow_matching,0.3,2.0,31,80
44,remove,2.0,(,,CC(C)CNCIr,CCC)CNCIr,9,remove ( from position 2,flow_matching,0.3,2.0,31,80
45,replace,0.0,),C,CCC)CNCIr,)CC)CNCIr,9,replace C at position 0 with ),flow_matching,0.3,2.0,31,80
46,remove,5.0,N,,)CC)CNCIr,)CC)CCIr,8,remove N from position 5,flow_matching,0.3,2.0,31,80
47,add,2.0,@,,)CC)CCIr,)C@C)CCIr,9,add @ at position 2,flow_matching,0.3,2.0,31,80
48,remove,6.0,C,,)C@C)CCIr,)C@C)CIr,8,remove C from position 6,flow_matching,0.3,2.0,31,80
49,replace,3.0,@,C,)C@C)CIr,)C@@)CIr,8,replace C at position 3 with @,flow_matching,0.3,2.0,31,80
50,replace,0.0,C,),)C@@)CIr,CC@@)CIr,8,replace ) at position 0 with C,flow_matching,0.3,2.0,31,80
51,add,4.0,+,,CC@@)CIr,CC@@+)CIr,9,add + at position 4,flow_matching,0.3,2.0,31,80
52,replace,2.0,(,@,CC@@+)CIr,CC(@+)CIr,9,replace @ at position 2 with (,flow_matching,0.3,2.0,31,80
53,replace,3.0,C,@,CC(@+)CIr,CC(C+)CIr,9,replace @ at position 3 with C,flow_matching,0.3,2.0,31,80
54,replace,4.0,),+,CC(C+)CIr,CC(C))CIr,9,replace + at position 4 with ),flow_matching,0.3,2.0,31,80
55,replace,5.0,C,),CC(C))CIr,CC(C)CCIr,9,replace ) at position 5 with C,flow_matching,0.3,2.0,31,80
56,replace,6.0,N,C,CC(C)CCIr,CC(C)CNIr,9,replace C at position 6 with N,flow_matching,0.3,2.0,31,80
57,replace,7.0,C,I,CC(C)CNIr,CC(C)CNCr,9,replace I at position 7 with C,flow_matching,0.3,2.0,31,80
58,replace,8.0,(,r,CC(C)CNCr,CC(C)CNC(,9,replace r at position 8 with (,flow_matching,0.3,2.0,31,80
59,add,9.0,=,,CC(C)CNC(,CC(C)CNC(=,10,add = at position 9,flow_matching,0.3,2.0,31,80
60,add,10.0,O,,CC(C)CNC(=,CC(C)CNC(=O,11,add O at position 10,flow_matching,0.3,2.0,31,80
61,add,11.0,),,CC(C)CNC(=O,CC(C)CNC(=O),12,add ) at position 11,flow_matching,0.3,2.0,31,80
62,add,12.0,[,,CC(C)CNC(=O),CC(C)CNC(=O)[,13,add [ at position 12,flow_matching,0.3,2.0,31,80
63,add,13.0,C,,CC(C)CNC(=O)[,CC(C)CNC(=O)[C,14,add C at position 13,flow_matching,0.3,2.0,31,80
64,add,14.0,@,,CC(C)CNC(=O)[C,CC(C)CNC(=O)[C@,15,add @ at position 14,flow_matching,0.3,2.0,31,80
65,add,15.0,],,CC(C)CNC(=O)[C@,CC(C)CNC(=O)[C@],16,add ] at position 15,flow_matching,0.3,2.0,31,80
66,add,16.0,(,,CC(C)CNC(=O)[C@],CC(C)CNC(=O)[C@](,17,add ( at position 16,flow_matching,0.3,2.0,31,80
67,add,17.0,C,,CC(C)CNC(=O)[C@](,CC(C)CNC(=O)[C@](C,18,add C at position 17,flow_matching,0.3,2.0,31,80
68,add,18.0,),,CC(C)CNC(=O)[C@](C,CC(C)CNC(=O)[C@](C),19,add ) at position 18,flow_matching,0.3,2.0,31,80
69,add,19.0,(,,CC(C)CNC(=O)[C@](C),CC(C)CNC(=O)[C@](C)(,20,add ( at position 19,flow_matching,0.3,2.0,31,80
70,add,20.0,N,,CC(C)CNC(=O)[C@](C)(,CC(C)CNC(=O)[C@](C)(N,21,add N at position 20,flow_matching,0.3,2.0,31,80
71,add,21.0,),,CC(C)CNC(=O)[C@](C)(N,CC(C)CNC(=O)[C@](C)(N),22,add ) at position 21,flow_matching,0.3,2.0,31,80
72,add,22.0,C,,CC(C)CNC(=O)[C@](C)(N),CC(C)CNC(=O)[C@](C)(N)C,23,add C at position 22,flow_matching,0.3,2.0,31,80
73,add,23.0,(,,CC(C)CNC(=O)[C@](C)(N)C,CC(C)CNC(=O)[C@](C)(N)C(,24,add ( at position 23,flow_matching,0.3,2.0,31,80
74,add,24.0,F,,CC(C)CNC(=O)[C@](C)(N)C(,CC(C)CNC(=O)[C@](C)(N)C(F,25,add F at position 24,flow_matching,0.3,2.0,31,80
75,add,25.0,),,CC(C)CNC(=O)[C@](C)(N)C(F,CC(C)CNC(=O)[C@](C)(N)C(F),26,add ) at position 25,flow_matching,0.3,2.0,31,80
76,add,26.0,(,,CC(C)CNC(=O)[C@](C)(N)C(F),CC(C)CNC(=O)[C@](C)(N)C(F)(,27,add ( at position 26,flow_matching,0.3,2.0,31,80
77,add,27.0,F,,CC(C)CNC(=O)[C@](C)(N)C(F)(,CC(C)CNC(=O)[C@](C)(N)C(F)(F,28,add F at position 27,flow_matching,0.3,2.0,31,80
78,add,28.0,),,CC(C)CNC(=O)[C@](C)(N)C(F)(F,CC(C)CNC(=O)[C@](C)(N)C(F)(F),29,add ) at position 28,flow_matching,0.3,2.0,31,80
79,add,29.0,F,,CC(C)CNC(=O)[C@](C)(N)C(F)(F),CC(C)CNC(=O)[C@](C)(N)C(F)(F)F,30,add F at position 29,flow_matching,0.3,2.0,31,80
80,add,30.0,"
",,CC(C)CNC(=O)[C@](C)(N)C(F)(F)F,"CC(C)CNC(=O)[C@](C)(N)C(F)(F)F
",31,"add 
 at position 30",flow_matching,0.3,2.0,31,80
