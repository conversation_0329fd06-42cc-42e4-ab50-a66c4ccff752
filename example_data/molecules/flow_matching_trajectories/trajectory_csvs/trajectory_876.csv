step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,25,79
1,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,25,79
2,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,25,79
3,add,1.0,l,,C,Cl,2,add l at position 1,flow_matching,0.3,2.0,25,79
4,replace,1.0,O,l,Cl,CO,2,replace l at position 1 with O,flow_matching,0.3,2.0,25,79
5,replace,0.0,7,C,CO,7O,2,replace C at position 0 with 7,flow_matching,0.3,2.0,25,79
6,replace,0.0,-,7,7O,-O,2,replace 7 at position 0 with -,flow_matching,0.3,2.0,25,79
7,add,0.0,c,,-O,c-O,3,add c at position 0,flow_matching,0.3,2.0,25,79
8,add,2.0,#,,c-O,c-#O,4,add # at position 2,flow_matching,0.3,2.0,25,79
9,add,3.0,O,,c-#O,c-#OO,5,add O at position 3,flow_matching,0.3,2.0,25,79
10,replace,3.0,],O,c-#OO,c-#]O,5,replace O at position 3 with ],flow_matching,0.3,2.0,25,79
11,replace,0.0,C,c,c-#]O,C-#]O,5,replace c at position 0 with C,flow_matching,0.3,2.0,25,79
12,replace,1.0,O,-,C-#]O,CO#]O,5,replace - at position 1 with O,flow_matching,0.3,2.0,25,79
13,add,1.0,4,,CO#]O,C4O#]O,6,add 4 at position 1,flow_matching,0.3,2.0,25,79
14,replace,1.0,O,4,C4O#]O,COO#]O,6,replace 4 at position 1 with O,flow_matching,0.3,2.0,25,79
15,add,4.0,4,,COO#]O,COO#4]O,7,add 4 at position 4,flow_matching,0.3,2.0,25,79
16,add,1.0,\,,COO#4]O,C\OO#4]O,8,add \ at position 1,flow_matching,0.3,2.0,25,79
17,add,1.0,1,,C\OO#4]O,C1\OO#4]O,9,add 1 at position 1,flow_matching,0.3,2.0,25,79
18,add,3.0,[,,C1\OO#4]O,C1\[OO#4]O,10,add [ at position 3,flow_matching,0.3,2.0,25,79
19,add,8.0,F,,C1\[OO#4]O,C1\[OO#4F]O,11,add F at position 8,flow_matching,0.3,2.0,25,79
20,remove,1.0,1,,C1\[OO#4F]O,C\[OO#4F]O,10,remove 1 from position 1,flow_matching,0.3,2.0,25,79
21,replace,8.0,1,],C\[OO#4F]O,C\[OO#4F1O,10,replace ] at position 8 with 1,flow_matching,0.3,2.0,25,79
22,replace,1.0,O,\,C\[OO#4F1O,CO[OO#4F1O,10,replace \ at position 1 with O,flow_matching,0.3,2.0,25,79
23,replace,2.0,/,[,CO[OO#4F1O,CO/OO#4F1O,10,replace [ at position 2 with /,flow_matching,0.3,2.0,25,79
24,remove,2.0,/,,CO/OO#4F1O,COOO#4F1O,9,remove / from position 2,flow_matching,0.3,2.0,25,79
25,add,7.0,B,,COOO#4F1O,COOO#4FB1O,10,add B at position 7,flow_matching,0.3,2.0,25,79
26,remove,0.0,C,,COOO#4FB1O,OOO#4FB1O,9,remove C from position 0,flow_matching,0.3,2.0,25,79
27,add,8.0,/,,OOO#4FB1O,OOO#4FB1/O,10,add / at position 8,flow_matching,0.3,2.0,25,79
28,replace,0.0,C,O,OOO#4FB1/O,COO#4FB1/O,10,replace O at position 0 with C,flow_matching,0.3,2.0,25,79
29,replace,5.0,],F,COO#4FB1/O,COO#4]B1/O,10,replace F at position 5 with ],flow_matching,0.3,2.0,25,79
30,add,2.0,1,,COO#4]B1/O,CO1O#4]B1/O,11,add 1 at position 2,flow_matching,0.3,2.0,25,79
31,add,3.0,[,,CO1O#4]B1/O,CO1[O#4]B1/O,12,add [ at position 3,flow_matching,0.3,2.0,25,79
32,add,5.0,I,,CO1[O#4]B1/O,CO1[OI#4]B1/O,13,add I at position 5,flow_matching,0.3,2.0,25,79
33,add,11.0,F,,CO1[OI#4]B1/O,CO1[OI#4]B1F/O,14,add F at position 11,flow_matching,0.3,2.0,25,79
34,replace,2.0,/,1,CO1[OI#4]B1F/O,CO/[OI#4]B1F/O,14,replace 1 at position 2 with /,flow_matching,0.3,2.0,25,79
35,add,3.0,F,,CO/[OI#4]B1F/O,CO/F[OI#4]B1F/O,15,add F at position 3,flow_matching,0.3,2.0,25,79
36,replace,3.0,N,F,CO/F[OI#4]B1F/O,CO/N[OI#4]B1F/O,15,replace F at position 3 with N,flow_matching,0.3,2.0,25,79
37,remove,7.0,#,,CO/N[OI#4]B1F/O,CO/N[OI4]B1F/O,14,remove # from position 7,flow_matching,0.3,2.0,25,79
38,replace,4.0,=,[,CO/N[OI4]B1F/O,CO/N=OI4]B1F/O,14,replace [ at position 4 with =,flow_matching,0.3,2.0,25,79
39,remove,10.0,1,,CO/N=OI4]B1F/O,CO/N=OI4]BF/O,13,remove 1 from position 10,flow_matching,0.3,2.0,25,79
40,add,12.0,],,CO/N=OI4]BF/O,CO/N=OI4]BF/]O,14,add ] at position 12,flow_matching,0.3,2.0,25,79
41,add,13.0,#,,CO/N=OI4]BF/]O,CO/N=OI4]BF/]#O,15,add # at position 13,flow_matching,0.3,2.0,25,79
42,add,7.0,F,,CO/N=OI4]BF/]#O,CO/N=OIF4]BF/]#O,16,add F at position 7,flow_matching,0.3,2.0,25,79
43,replace,0.0,H,C,CO/N=OIF4]BF/]#O,HO/N=OIF4]BF/]#O,16,replace C at position 0 with H,flow_matching,0.3,2.0,25,79
44,replace,0.0,C,H,HO/N=OIF4]BF/]#O,CO/N=OIF4]BF/]#O,16,replace H at position 0 with C,flow_matching,0.3,2.0,25,79
45,add,9.0,\,,CO/N=OIF4]BF/]#O,CO/N=OIF4\]BF/]#O,17,add \ at position 9,flow_matching,0.3,2.0,25,79
46,remove,6.0,I,,CO/N=OIF4\]BF/]#O,CO/N=OF4\]BF/]#O,16,remove I from position 6,flow_matching,0.3,2.0,25,79
47,remove,0.0,C,,CO/N=OF4\]BF/]#O,O/N=OF4\]BF/]#O,15,remove C from position 0,flow_matching,0.3,2.0,25,79
48,add,5.0,I,,O/N=OF4\]BF/]#O,O/N=OIF4\]BF/]#O,16,add I at position 5,flow_matching,0.3,2.0,25,79
49,replace,6.0,4,F,O/N=OIF4\]BF/]#O,O/N=OI44\]BF/]#O,16,replace F at position 6 with 4,flow_matching,0.3,2.0,25,79
50,add,0.0,=,,O/N=OI44\]BF/]#O,=O/N=OI44\]BF/]#O,17,add = at position 0,flow_matching,0.3,2.0,25,79
51,replace,0.0,C,=,=O/N=OI44\]BF/]#O,CO/N=OI44\]BF/]#O,17,replace = at position 0 with C,flow_matching,0.3,2.0,25,79
52,replace,3.0,@,N,CO/N=OI44\]BF/]#O,CO/@=OI44\]BF/]#O,17,replace N at position 3 with @,flow_matching,0.3,2.0,25,79
53,remove,3.0,@,,CO/@=OI44\]BF/]#O,CO/=OI44\]BF/]#O,16,remove @ from position 3,flow_matching,0.3,2.0,25,79
54,replace,12.0,5,/,CO/=OI44\]BF/]#O,CO/=OI44\]BF5]#O,16,replace / at position 12 with 5,flow_matching,0.3,2.0,25,79
55,add,3.0,\,,CO/=OI44\]BF5]#O,CO/\=OI44\]BF5]#O,17,add \ at position 3,flow_matching,0.3,2.0,25,79
56,remove,12.0,F,,CO/\=OI44\]BF5]#O,CO/\=OI44\]B5]#O,16,remove F from position 12,flow_matching,0.3,2.0,25,79
57,replace,8.0,1,4,CO/\=OI44\]B5]#O,CO/\=OI41\]B5]#O,16,replace 4 at position 8 with 1,flow_matching,0.3,2.0,25,79
58,replace,15.0,-,O,CO/\=OI41\]B5]#O,CO/\=OI41\]B5]#-,16,replace O at position 15 with -,flow_matching,0.3,2.0,25,79
59,replace,3.0,N,\,CO/\=OI41\]B5]#-,CO/N=OI41\]B5]#-,16,replace \ at position 3 with N,flow_matching,0.3,2.0,25,79
60,replace,5.0,C,O,CO/N=OI41\]B5]#-,CO/N=CI41\]B5]#-,16,replace O at position 5 with C,flow_matching,0.3,2.0,25,79
61,replace,6.0,\,I,CO/N=CI41\]B5]#-,CO/N=C\41\]B5]#-,16,replace I at position 6 with \,flow_matching,0.3,2.0,25,79
62,replace,7.0,C,4,CO/N=C\41\]B5]#-,CO/N=C\C1\]B5]#-,16,replace 4 at position 7 with C,flow_matching,0.3,2.0,25,79
63,replace,8.0,(,1,CO/N=C\C1\]B5]#-,CO/N=C\C(\]B5]#-,16,replace 1 at position 8 with (,flow_matching,0.3,2.0,25,79
64,replace,9.0,C,\,CO/N=C\C(\]B5]#-,CO/N=C\C(C]B5]#-,16,replace \ at position 9 with C,flow_matching,0.3,2.0,25,79
65,replace,10.0,#,],CO/N=C\C(C]B5]#-,CO/N=C\C(C#B5]#-,16,replace ] at position 10 with #,flow_matching,0.3,2.0,25,79
66,replace,11.0,N,B,CO/N=C\C(C#B5]#-,CO/N=C\C(C#N5]#-,16,replace B at position 11 with N,flow_matching,0.3,2.0,25,79
67,replace,12.0,),5,CO/N=C\C(C#N5]#-,CO/N=C\C(C#N)]#-,16,replace 5 at position 12 with ),flow_matching,0.3,2.0,25,79
68,replace,13.0,=,],CO/N=C\C(C#N)]#-,CO/N=C\C(C#N)=#-,16,replace ] at position 13 with =,flow_matching,0.3,2.0,25,79
69,replace,14.0,C,#,CO/N=C\C(C#N)=#-,CO/N=C\C(C#N)=C-,16,replace # at position 14 with C,flow_matching,0.3,2.0,25,79
70,replace,15.0,/,-,CO/N=C\C(C#N)=C-,CO/N=C\C(C#N)=C/,16,replace - at position 15 with /,flow_matching,0.3,2.0,25,79
71,add,16.0,c,,CO/N=C\C(C#N)=C/,CO/N=C\C(C#N)=C/c,17,add c at position 16,flow_matching,0.3,2.0,25,79
72,add,17.0,1,,CO/N=C\C(C#N)=C/c,CO/N=C\C(C#N)=C/c1,18,add 1 at position 17,flow_matching,0.3,2.0,25,79
73,add,18.0,c,,CO/N=C\C(C#N)=C/c1,CO/N=C\C(C#N)=C/c1c,19,add c at position 18,flow_matching,0.3,2.0,25,79
74,add,19.0,c,,CO/N=C\C(C#N)=C/c1c,CO/N=C\C(C#N)=C/c1cc,20,add c at position 19,flow_matching,0.3,2.0,25,79
75,add,20.0,c,,CO/N=C\C(C#N)=C/c1cc,CO/N=C\C(C#N)=C/c1ccc,21,add c at position 20,flow_matching,0.3,2.0,25,79
76,add,21.0,n,,CO/N=C\C(C#N)=C/c1ccc,CO/N=C\C(C#N)=C/c1cccn,22,add n at position 21,flow_matching,0.3,2.0,25,79
77,add,22.0,c,,CO/N=C\C(C#N)=C/c1cccn,CO/N=C\C(C#N)=C/c1cccnc,23,add c at position 22,flow_matching,0.3,2.0,25,79
78,add,23.0,1,,CO/N=C\C(C#N)=C/c1cccnc,CO/N=C\C(C#N)=C/c1cccnc1,24,add 1 at position 23,flow_matching,0.3,2.0,25,79
79,add,24.0,"
",,CO/N=C\C(C#N)=C/c1cccnc1,"CO/N=C\C(C#N)=C/c1cccnc1
",25,"add 
 at position 24",flow_matching,0.3,2.0,25,79
