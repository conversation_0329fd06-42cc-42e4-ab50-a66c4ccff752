step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,46,199
1,add,0.0,\,,,\,1,add \ at position 0,flow_matching,0.3,2.0,46,199
2,add,1.0,C,,\,\C,2,add C at position 1,flow_matching,0.3,2.0,46,199
3,replace,0.0,C,\,\C,CC,2,replace \ at position 0 with C,flow_matching,0.3,2.0,46,199
4,remove,1.0,C,,CC,C,1,remove C from position 1,flow_matching,0.3,2.0,46,199
5,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,46,199
6,replace,1.0,4,<PERSON>,<PERSON>,C4,2,replace <PERSON> at position 1 with 4,flow_matching,0.3,2.0,46,199
7,replace,1.0,),4,C4,C),2,replace 4 at position 1 with ),flow_matching,0.3,2.0,46,199
8,add,1.0,+,,C),C+),3,add + at position 1,flow_matching,0.3,2.0,46,199
9,replace,1.0,C,+,C+),CC),3,replace + at position 1 with C,flow_matching,0.3,2.0,46,199
10,add,2.0,@,,CC),CC@),4,add @ at position 2,flow_matching,0.3,2.0,46,199
11,remove,0.0,C,,CC@),C@),3,remove C from position 0,flow_matching,0.3,2.0,46,199
12,replace,2.0,(,),C@),C@(,3,replace ) at position 2 with (,flow_matching,0.3,2.0,46,199
13,remove,2.0,(,,C@(,C@,2,remove ( from position 2,flow_matching,0.3,2.0,46,199
14,remove,0.0,C,,C@,@,1,remove C from position 0,flow_matching,0.3,2.0,46,199
15,remove,0.0,@,,@,,0,remove @ from position 0,flow_matching,0.3,2.0,46,199
16,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,46,199
17,replace,0.0,C,],],C,1,replace ] at position 0 with C,flow_matching,0.3,2.0,46,199
18,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,46,199
19,add,1.0,+,,CC,C+C,3,add + at position 1,flow_matching,0.3,2.0,46,199
20,add,2.0,s,,C+C,C+sC,4,add s at position 2,flow_matching,0.3,2.0,46,199
21,replace,1.0,C,+,C+sC,CCsC,4,replace + at position 1 with C,flow_matching,0.3,2.0,46,199
22,replace,2.0,(,s,CCsC,CC(C,4,replace s at position 2 with (,flow_matching,0.3,2.0,46,199
23,replace,2.0,+,(,CC(C,CC+C,4,replace ( at position 2 with +,flow_matching,0.3,2.0,46,199
24,add,4.0,[,,CC+C,CC+C[,5,add [ at position 4,flow_matching,0.3,2.0,46,199
25,replace,3.0,],C,CC+C[,CC+][,5,replace C at position 3 with ],flow_matching,0.3,2.0,46,199
26,replace,2.0,(,+,CC+][,CC(][,5,replace + at position 2 with (,flow_matching,0.3,2.0,46,199
27,replace,3.0,C,],CC(][,CC(C[,5,replace ] at position 3 with C,flow_matching,0.3,2.0,46,199
28,add,1.0,=,,CC(C[,C=C(C[,6,add = at position 1,flow_matching,0.3,2.0,46,199
29,replace,1.0,C,=,C=C(C[,CCC(C[,6,replace = at position 1 with C,flow_matching,0.3,2.0,46,199
30,remove,1.0,C,,CCC(C[,CC(C[,5,remove C from position 1,flow_matching,0.3,2.0,46,199
31,add,2.0,O,,CC(C[,CCO(C[,6,add O at position 2,flow_matching,0.3,2.0,46,199
32,remove,2.0,O,,CCO(C[,CC(C[,5,remove O from position 2,flow_matching,0.3,2.0,46,199
33,remove,3.0,C,,CC(C[,CC([,4,remove C from position 3,flow_matching,0.3,2.0,46,199
34,replace,0.0,3,C,CC([,3C([,4,replace C at position 0 with 3,flow_matching,0.3,2.0,46,199
35,replace,0.0,C,3,3C([,CC([,4,replace 3 at position 0 with C,flow_matching,0.3,2.0,46,199
36,remove,0.0,C,,CC([,C([,3,remove C from position 0,flow_matching,0.3,2.0,46,199
37,add,1.0,B,,C([,CB([,4,add B at position 1,flow_matching,0.3,2.0,46,199
38,add,0.0,N,,CB([,NCB([,5,add N at position 0,flow_matching,0.3,2.0,46,199
39,add,3.0,I,,NCB([,NCBI([,6,add I at position 3,flow_matching,0.3,2.0,46,199
40,replace,0.0,C,N,NCBI([,CCBI([,6,replace N at position 0 with C,flow_matching,0.3,2.0,46,199
41,remove,5.0,[,,CCBI([,CCBI(,5,remove [ from position 5,flow_matching,0.3,2.0,46,199
42,add,3.0,\,,CCBI(,CCB\I(,6,add \ at position 3,flow_matching,0.3,2.0,46,199
43,replace,2.0,(,B,CCB\I(,CC(\I(,6,replace B at position 2 with (,flow_matching,0.3,2.0,46,199
44,replace,1.0,2,C,CC(\I(,C2(\I(,6,replace C at position 1 with 2,flow_matching,0.3,2.0,46,199
45,remove,1.0,2,,C2(\I(,C(\I(,5,remove 2 from position 1,flow_matching,0.3,2.0,46,199
46,add,0.0,2,,C(\I(,2C(\I(,6,add 2 at position 0,flow_matching,0.3,2.0,46,199
47,remove,4.0,I,,2C(\I(,2C(\(,5,remove I from position 4,flow_matching,0.3,2.0,46,199
48,replace,0.0,C,2,2C(\(,CC(\(,5,replace 2 at position 0 with C,flow_matching,0.3,2.0,46,199
49,replace,3.0,C,\,CC(\(,CC(C(,5,replace \ at position 3 with C,flow_matching,0.3,2.0,46,199
50,add,5.0,/,,CC(C(,CC(C(/,6,add / at position 5,flow_matching,0.3,2.0,46,199
51,add,5.0,7,,CC(C(/,CC(C(7/,7,add 7 at position 5,flow_matching,0.3,2.0,46,199
52,add,5.0,6,,CC(C(7/,CC(C(67/,8,add 6 at position 5,flow_matching,0.3,2.0,46,199
53,replace,1.0,H,C,CC(C(67/,CH(C(67/,8,replace C at position 1 with H,flow_matching,0.3,2.0,46,199
54,remove,4.0,(,,CH(C(67/,CH(C67/,7,remove ( from position 4,flow_matching,0.3,2.0,46,199
55,replace,1.0,C,H,CH(C67/,CC(C67/,7,replace H at position 1 with C,flow_matching,0.3,2.0,46,199
56,replace,1.0,/,C,CC(C67/,C/(C67/,7,replace C at position 1 with /,flow_matching,0.3,2.0,46,199
57,replace,5.0,F,7,C/(C67/,C/(C6F/,7,replace 7 at position 5 with F,flow_matching,0.3,2.0,46,199
58,replace,0.0,=,C,C/(C6F/,=/(C6F/,7,replace C at position 0 with =,flow_matching,0.3,2.0,46,199
59,add,4.0,#,,=/(C6F/,=/(C#6F/,8,add # at position 4,flow_matching,0.3,2.0,46,199
60,add,1.0,c,,=/(C#6F/,=c/(C#6F/,9,add c at position 1,flow_matching,0.3,2.0,46,199
61,replace,0.0,C,=,=c/(C#6F/,Cc/(C#6F/,9,replace = at position 0 with C,flow_matching,0.3,2.0,46,199
62,replace,1.0,o,c,Cc/(C#6F/,Co/(C#6F/,9,replace c at position 1 with o,flow_matching,0.3,2.0,46,199
63,replace,1.0,C,o,Co/(C#6F/,CC/(C#6F/,9,replace o at position 1 with C,flow_matching,0.3,2.0,46,199
64,replace,2.0,(,/,CC/(C#6F/,CC((C#6F/,9,replace / at position 2 with (,flow_matching,0.3,2.0,46,199
65,replace,3.0,C,(,CC((C#6F/,CC(CC#6F/,9,replace ( at position 3 with C,flow_matching,0.3,2.0,46,199
66,replace,4.0,),C,CC(CC#6F/,CC(C)#6F/,9,replace C at position 4 with ),flow_matching,0.3,2.0,46,199
67,add,3.0,2,,CC(C)#6F/,CC(2C)#6F/,10,add 2 at position 3,flow_matching,0.3,2.0,46,199
68,replace,6.0,F,#,CC(2C)#6F/,CC(2C)F6F/,10,replace # at position 6 with F,flow_matching,0.3,2.0,46,199
69,remove,2.0,(,,CC(2C)F6F/,CC2C)F6F/,9,remove ( from position 2,flow_matching,0.3,2.0,46,199
70,replace,2.0,(,2,CC2C)F6F/,CC(C)F6F/,9,replace 2 at position 2 with (,flow_matching,0.3,2.0,46,199
71,remove,0.0,C,,CC(C)F6F/,C(C)F6F/,8,remove C from position 0,flow_matching,0.3,2.0,46,199
72,replace,1.0,C,(,C(C)F6F/,CCC)F6F/,8,replace ( at position 1 with C,flow_matching,0.3,2.0,46,199
73,replace,2.0,(,C,CCC)F6F/,CC()F6F/,8,replace C at position 2 with (,flow_matching,0.3,2.0,46,199
74,add,2.0,s,,CC()F6F/,CCs()F6F/,9,add s at position 2,flow_matching,0.3,2.0,46,199
75,add,4.0,5,,CCs()F6F/,CCs(5)F6F/,10,add 5 at position 4,flow_matching,0.3,2.0,46,199
76,replace,2.0,(,s,CCs(5)F6F/,CC((5)F6F/,10,replace s at position 2 with (,flow_matching,0.3,2.0,46,199
77,add,5.0,2,,CC((5)F6F/,CC((52)F6F/,11,add 2 at position 5,flow_matching,0.3,2.0,46,199
78,replace,4.0,I,5,CC((52)F6F/,CC((I2)F6F/,11,replace 5 at position 4 with I,flow_matching,0.3,2.0,46,199
79,replace,3.0,C,(,CC((I2)F6F/,CC(CI2)F6F/,11,replace ( at position 3 with C,flow_matching,0.3,2.0,46,199
80,remove,8.0,6,,CC(CI2)F6F/,CC(CI2)FF/,10,remove 6 from position 8,flow_matching,0.3,2.0,46,199
81,add,0.0,O,,CC(CI2)FF/,OCC(CI2)FF/,11,add O at position 0,flow_matching,0.3,2.0,46,199
82,add,1.0,s,,OCC(CI2)FF/,OsCC(CI2)FF/,12,add s at position 1,flow_matching,0.3,2.0,46,199
83,replace,3.0,+,C,OsCC(CI2)FF/,OsC+(CI2)FF/,12,replace C at position 3 with +,flow_matching,0.3,2.0,46,199
84,replace,4.0,l,(,OsC+(CI2)FF/,OsC+lCI2)FF/,12,replace ( at position 4 with l,flow_matching,0.3,2.0,46,199
85,remove,11.0,/,,OsC+lCI2)FF/,OsC+lCI2)FF,11,remove / from position 11,flow_matching,0.3,2.0,46,199
86,replace,0.0,C,O,OsC+lCI2)FF,CsC+lCI2)FF,11,replace O at position 0 with C,flow_matching,0.3,2.0,46,199
87,replace,2.0,2,C,CsC+lCI2)FF,Cs2+lCI2)FF,11,replace C at position 2 with 2,flow_matching,0.3,2.0,46,199
88,replace,4.0,6,l,Cs2+lCI2)FF,Cs2+6CI2)FF,11,replace l at position 4 with 6,flow_matching,0.3,2.0,46,199
89,add,2.0,N,,Cs2+6CI2)FF,CsN2+6CI2)FF,12,add N at position 2,flow_matching,0.3,2.0,46,199
90,replace,8.0,5,2,CsN2+6CI2)FF,CsN2+6CI5)FF,12,replace 2 at position 8 with 5,flow_matching,0.3,2.0,46,199
91,remove,6.0,C,,CsN2+6CI5)FF,CsN2+6I5)FF,11,remove C from position 6,flow_matching,0.3,2.0,46,199
92,add,11.0,6,,CsN2+6I5)FF,CsN2+6I5)FF6,12,add 6 at position 11,flow_matching,0.3,2.0,46,199
93,add,6.0,(,,CsN2+6I5)FF6,CsN2+6(I5)FF6,13,add ( at position 6,flow_matching,0.3,2.0,46,199
94,add,10.0,\,,CsN2+6(I5)FF6,CsN2+6(I5)\FF6,14,add \ at position 10,flow_matching,0.3,2.0,46,199
95,remove,3.0,2,,CsN2+6(I5)\FF6,CsN+6(I5)\FF6,13,remove 2 from position 3,flow_matching,0.3,2.0,46,199
96,replace,1.0,C,s,CsN+6(I5)\FF6,CCN+6(I5)\FF6,13,replace s at position 1 with C,flow_matching,0.3,2.0,46,199
97,remove,0.0,C,,CCN+6(I5)\FF6,CN+6(I5)\FF6,12,remove C from position 0,flow_matching,0.3,2.0,46,199
98,remove,8.0,\,,CN+6(I5)\FF6,CN+6(I5)FF6,11,remove \ from position 8,flow_matching,0.3,2.0,46,199
99,add,7.0,],,CN+6(I5)FF6,CN+6(I5])FF6,12,add ] at position 7,flow_matching,0.3,2.0,46,199
100,replace,1.0,C,N,CN+6(I5])FF6,CC+6(I5])FF6,12,replace N at position 1 with C,flow_matching,0.3,2.0,46,199
101,remove,11.0,6,,CC+6(I5])FF6,CC+6(I5])FF,11,remove 6 from position 11,flow_matching,0.3,2.0,46,199
102,add,10.0,o,,CC+6(I5])FF,CC+6(I5])FoF,12,add o at position 10,flow_matching,0.3,2.0,46,199
103,remove,3.0,6,,CC+6(I5])FoF,CC+(I5])FoF,11,remove 6 from position 3,flow_matching,0.3,2.0,46,199
104,add,0.0,7,,CC+(I5])FoF,7CC+(I5])FoF,12,add 7 at position 0,flow_matching,0.3,2.0,46,199
105,replace,0.0,C,7,7CC+(I5])FoF,CCC+(I5])FoF,12,replace 7 at position 0 with C,flow_matching,0.3,2.0,46,199
106,replace,2.0,(,C,CCC+(I5])FoF,CC(+(I5])FoF,12,replace C at position 2 with (,flow_matching,0.3,2.0,46,199
107,replace,9.0,/,F,CC(+(I5])FoF,CC(+(I5])/oF,12,replace F at position 9 with /,flow_matching,0.3,2.0,46,199
108,add,6.0,7,,CC(+(I5])/oF,CC(+(I75])/oF,13,add 7 at position 6,flow_matching,0.3,2.0,46,199
109,replace,5.0,(,I,CC(+(I75])/oF,CC(+((75])/oF,13,replace I at position 5 with (,flow_matching,0.3,2.0,46,199
110,replace,3.0,C,+,CC(+((75])/oF,CC(C((75])/oF,13,replace + at position 3 with C,flow_matching,0.3,2.0,46,199
111,add,8.0,C,,CC(C((75])/oF,CC(C((75C])/oF,14,add C at position 8,flow_matching,0.3,2.0,46,199
112,replace,4.0,),(,CC(C((75C])/oF,CC(C)(75C])/oF,14,replace ( at position 4 with ),flow_matching,0.3,2.0,46,199
113,add,10.0,n,,CC(C)(75C])/oF,CC(C)(75C]n)/oF,15,add n at position 10,flow_matching,0.3,2.0,46,199
114,remove,0.0,C,,CC(C)(75C]n)/oF,C(C)(75C]n)/oF,14,remove C from position 0,flow_matching,0.3,2.0,46,199
115,replace,1.0,C,(,C(C)(75C]n)/oF,CCC)(75C]n)/oF,14,replace ( at position 1 with C,flow_matching,0.3,2.0,46,199
116,replace,2.0,(,C,CCC)(75C]n)/oF,CC()(75C]n)/oF,14,replace C at position 2 with (,flow_matching,0.3,2.0,46,199
117,remove,2.0,(,,CC()(75C]n)/oF,CC)(75C]n)/oF,13,remove ( from position 2,flow_matching,0.3,2.0,46,199
118,add,11.0,-,,CC)(75C]n)/oF,CC)(75C]n)/-oF,14,add - at position 11,flow_matching,0.3,2.0,46,199
119,add,13.0,3,,CC)(75C]n)/-oF,CC)(75C]n)/-o3F,15,add 3 at position 13,flow_matching,0.3,2.0,46,199
120,add,3.0,c,,CC)(75C]n)/-o3F,CC)c(75C]n)/-o3F,16,add c at position 3,flow_matching,0.3,2.0,46,199
121,replace,2.0,(,),CC)c(75C]n)/-o3F,CC(c(75C]n)/-o3F,16,replace ) at position 2 with (,flow_matching,0.3,2.0,46,199
122,remove,2.0,(,,CC(c(75C]n)/-o3F,CCc(75C]n)/-o3F,15,remove ( from position 2,flow_matching,0.3,2.0,46,199
123,add,15.0,I,,CCc(75C]n)/-o3F,CCc(75C]n)/-o3FI,16,add I at position 15,flow_matching,0.3,2.0,46,199
124,replace,2.0,(,c,CCc(75C]n)/-o3FI,CC((75C]n)/-o3FI,16,replace c at position 2 with (,flow_matching,0.3,2.0,46,199
125,add,10.0,S,,CC((75C]n)/-o3FI,CC((75C]n)S/-o3FI,17,add S at position 10,flow_matching,0.3,2.0,46,199
126,add,5.0,7,,CC((75C]n)S/-o3FI,CC((775C]n)S/-o3FI,18,add 7 at position 5,flow_matching,0.3,2.0,46,199
127,replace,3.0,C,(,CC((775C]n)S/-o3FI,CC(C775C]n)S/-o3FI,18,replace ( at position 3 with C,flow_matching,0.3,2.0,46,199
128,replace,17.0,o,I,CC(C775C]n)S/-o3FI,CC(C775C]n)S/-o3Fo,18,replace I at position 17 with o,flow_matching,0.3,2.0,46,199
129,add,1.0,B,,CC(C775C]n)S/-o3Fo,CBC(C775C]n)S/-o3Fo,19,add B at position 1,flow_matching,0.3,2.0,46,199
130,add,3.0,5,,CBC(C775C]n)S/-o3Fo,CBC5(C775C]n)S/-o3Fo,20,add 5 at position 3,flow_matching,0.3,2.0,46,199
131,remove,14.0,/,,CBC5(C775C]n)S/-o3Fo,CBC5(C775C]n)S-o3Fo,19,remove / from position 14,flow_matching,0.3,2.0,46,199
132,remove,7.0,7,,CBC5(C775C]n)S-o3Fo,CBC5(C75C]n)S-o3Fo,18,remove 7 from position 7,flow_matching,0.3,2.0,46,199
133,replace,0.0,o,C,CBC5(C75C]n)S-o3Fo,oBC5(C75C]n)S-o3Fo,18,replace C at position 0 with o,flow_matching,0.3,2.0,46,199
134,add,11.0,F,,oBC5(C75C]n)S-o3Fo,oBC5(C75C]nF)S-o3Fo,19,add F at position 11,flow_matching,0.3,2.0,46,199
135,replace,0.0,C,o,oBC5(C75C]nF)S-o3Fo,CBC5(C75C]nF)S-o3Fo,19,replace o at position 0 with C,flow_matching,0.3,2.0,46,199
136,remove,17.0,F,,CBC5(C75C]nF)S-o3Fo,CBC5(C75C]nF)S-o3o,18,remove F from position 17,flow_matching,0.3,2.0,46,199
137,add,0.0,5,,CBC5(C75C]nF)S-o3o,5CBC5(C75C]nF)S-o3o,19,add 5 at position 0,flow_matching,0.3,2.0,46,199
138,add,2.0,=,,5CBC5(C75C]nF)S-o3o,5C=BC5(C75C]nF)S-o3o,20,add = at position 2,flow_matching,0.3,2.0,46,199
139,add,14.0,\,,5C=BC5(C75C]nF)S-o3o,5C=BC5(C75C]nF\)S-o3o,21,add \ at position 14,flow_matching,0.3,2.0,46,199
140,replace,0.0,C,5,5C=BC5(C75C]nF\)S-o3o,CC=BC5(C75C]nF\)S-o3o,21,replace 5 at position 0 with C,flow_matching,0.3,2.0,46,199
141,remove,19.0,3,,CC=BC5(C75C]nF\)S-o3o,CC=BC5(C75C]nF\)S-oo,20,remove 3 from position 19,flow_matching,0.3,2.0,46,199
142,replace,2.0,(,=,CC=BC5(C75C]nF\)S-oo,CC(BC5(C75C]nF\)S-oo,20,replace = at position 2 with (,flow_matching,0.3,2.0,46,199
143,remove,19.0,o,,CC(BC5(C75C]nF\)S-oo,CC(BC5(C75C]nF\)S-o,19,remove o from position 19,flow_matching,0.3,2.0,46,199
144,replace,13.0,=,F,CC(BC5(C75C]nF\)S-o,CC(BC5(C75C]n=\)S-o,19,replace F at position 13 with =,flow_matching,0.3,2.0,46,199
145,remove,7.0,C,,CC(BC5(C75C]n=\)S-o,CC(BC5(75C]n=\)S-o,18,remove C from position 7,flow_matching,0.3,2.0,46,199
146,add,13.0,1,,CC(BC5(75C]n=\)S-o,CC(BC5(75C]n=1\)S-o,19,add 1 at position 13,flow_matching,0.3,2.0,46,199
147,replace,3.0,C,B,CC(BC5(75C]n=1\)S-o,CC(CC5(75C]n=1\)S-o,19,replace B at position 3 with C,flow_matching,0.3,2.0,46,199
148,remove,15.0,),,CC(CC5(75C]n=1\)S-o,CC(CC5(75C]n=1\S-o,18,remove ) from position 15,flow_matching,0.3,2.0,46,199
149,replace,4.0,),C,CC(CC5(75C]n=1\S-o,CC(C)5(75C]n=1\S-o,18,replace C at position 4 with ),flow_matching,0.3,2.0,46,199
150,replace,15.0,N,S,CC(C)5(75C]n=1\S-o,CC(C)5(75C]n=1\N-o,18,replace S at position 15 with N,flow_matching,0.3,2.0,46,199
151,replace,16.0,6,-,CC(C)5(75C]n=1\N-o,CC(C)5(75C]n=1\N6o,18,replace - at position 16 with 6,flow_matching,0.3,2.0,46,199
152,remove,0.0,C,,CC(C)5(75C]n=1\N6o,C(C)5(75C]n=1\N6o,17,remove C from position 0,flow_matching,0.3,2.0,46,199
153,remove,4.0,5,,C(C)5(75C]n=1\N6o,C(C)(75C]n=1\N6o,16,remove 5 from position 4,flow_matching,0.3,2.0,46,199
154,add,15.0,2,,C(C)(75C]n=1\N6o,C(C)(75C]n=1\N62o,17,add 2 at position 15,flow_matching,0.3,2.0,46,199
155,replace,1.0,C,(,C(C)(75C]n=1\N62o,CCC)(75C]n=1\N62o,17,replace ( at position 1 with C,flow_matching,0.3,2.0,46,199
156,replace,2.0,(,C,CCC)(75C]n=1\N62o,CC()(75C]n=1\N62o,17,replace C at position 2 with (,flow_matching,0.3,2.0,46,199
157,replace,3.0,C,),CC()(75C]n=1\N62o,CC(C(75C]n=1\N62o,17,replace ) at position 3 with C,flow_matching,0.3,2.0,46,199
158,replace,4.0,),(,CC(C(75C]n=1\N62o,CC(C)75C]n=1\N62o,17,replace ( at position 4 with ),flow_matching,0.3,2.0,46,199
159,replace,5.0,C,7,CC(C)75C]n=1\N62o,CC(C)C5C]n=1\N62o,17,replace 7 at position 5 with C,flow_matching,0.3,2.0,46,199
160,replace,6.0,N,5,CC(C)C5C]n=1\N62o,CC(C)CNC]n=1\N62o,17,replace 5 at position 6 with N,flow_matching,0.3,2.0,46,199
161,replace,7.0,1,C,CC(C)CNC]n=1\N62o,CC(C)CN1]n=1\N62o,17,replace C at position 7 with 1,flow_matching,0.3,2.0,46,199
162,replace,8.0,C,],CC(C)CN1]n=1\N62o,CC(C)CN1Cn=1\N62o,17,replace ] at position 8 with C,flow_matching,0.3,2.0,46,199
163,replace,9.0,C,n,CC(C)CN1Cn=1\N62o,CC(C)CN1CC=1\N62o,17,replace n at position 9 with C,flow_matching,0.3,2.0,46,199
164,replace,10.0,O,=,CC(C)CN1CC=1\N62o,CC(C)CN1CCO1\N62o,17,replace = at position 10 with O,flow_matching,0.3,2.0,46,199
165,replace,11.0,[,1,CC(C)CN1CCO1\N62o,CC(C)CN1CCO[\N62o,17,replace 1 at position 11 with [,flow_matching,0.3,2.0,46,199
166,replace,12.0,C,\,CC(C)CN1CCO[\N62o,CC(C)CN1CCO[CN62o,17,replace \ at position 12 with C,flow_matching,0.3,2.0,46,199
167,replace,13.0,@,N,CC(C)CN1CCO[CN62o,CC(C)CN1CCO[C@62o,17,replace N at position 13 with @,flow_matching,0.3,2.0,46,199
168,replace,14.0,@,6,CC(C)CN1CCO[C@62o,CC(C)CN1CCO[C@@2o,17,replace 6 at position 14 with @,flow_matching,0.3,2.0,46,199
169,replace,15.0,H,2,CC(C)CN1CCO[C@@2o,CC(C)CN1CCO[C@@Ho,17,replace 2 at position 15 with H,flow_matching,0.3,2.0,46,199
170,replace,16.0,],o,CC(C)CN1CCO[C@@Ho,CC(C)CN1CCO[C@@H],17,replace o at position 16 with ],flow_matching,0.3,2.0,46,199
171,add,17.0,(,,CC(C)CN1CCO[C@@H],CC(C)CN1CCO[C@@H](,18,add ( at position 17,flow_matching,0.3,2.0,46,199
172,add,18.0,C,,CC(C)CN1CCO[C@@H](,CC(C)CN1CCO[C@@H](C,19,add C at position 18,flow_matching,0.3,2.0,46,199
173,add,19.0,N,,CC(C)CN1CCO[C@@H](C,CC(C)CN1CCO[C@@H](CN,20,add N at position 19,flow_matching,0.3,2.0,46,199
174,add,20.0,C,,CC(C)CN1CCO[C@@H](CN,CC(C)CN1CCO[C@@H](CNC,21,add C at position 20,flow_matching,0.3,2.0,46,199
175,add,21.0,(,,CC(C)CN1CCO[C@@H](CNC,CC(C)CN1CCO[C@@H](CNC(,22,add ( at position 21,flow_matching,0.3,2.0,46,199
176,add,22.0,=,,CC(C)CN1CCO[C@@H](CNC(,CC(C)CN1CCO[C@@H](CNC(=,23,add = at position 22,flow_matching,0.3,2.0,46,199
177,add,23.0,O,,CC(C)CN1CCO[C@@H](CNC(=,CC(C)CN1CCO[C@@H](CNC(=O,24,add O at position 23,flow_matching,0.3,2.0,46,199
178,add,24.0,),,CC(C)CN1CCO[C@@H](CNC(=O,CC(C)CN1CCO[C@@H](CNC(=O),25,add ) at position 24,flow_matching,0.3,2.0,46,199
179,add,25.0,/,,CC(C)CN1CCO[C@@H](CNC(=O),CC(C)CN1CCO[C@@H](CNC(=O)/,26,add / at position 25,flow_matching,0.3,2.0,46,199
180,add,26.0,C,,CC(C)CN1CCO[C@@H](CNC(=O)/,CC(C)CN1CCO[C@@H](CNC(=O)/C,27,add C at position 26,flow_matching,0.3,2.0,46,199
181,add,27.0,=,,CC(C)CN1CCO[C@@H](CNC(=O)/C,CC(C)CN1CCO[C@@H](CNC(=O)/C=,28,add = at position 27,flow_matching,0.3,2.0,46,199
182,add,28.0,C,,CC(C)CN1CCO[C@@H](CNC(=O)/C=,CC(C)CN1CCO[C@@H](CNC(=O)/C=C,29,add C at position 28,flow_matching,0.3,2.0,46,199
183,add,29.0,/,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/,30,add / at position 29,flow_matching,0.3,2.0,46,199
184,add,30.0,c,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c,31,add c at position 30,flow_matching,0.3,2.0,46,199
185,add,31.0,2,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2,32,add 2 at position 31,flow_matching,0.3,2.0,46,199
186,add,32.0,c,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2c,33,add c at position 32,flow_matching,0.3,2.0,46,199
187,add,33.0,c,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2c,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2cc,34,add c at position 33,flow_matching,0.3,2.0,46,199
188,add,34.0,n,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2cc,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccn,35,add n at position 34,flow_matching,0.3,2.0,46,199
189,add,35.0,c,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccn,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc,36,add c at position 35,flow_matching,0.3,2.0,46,199
190,add,36.0,(,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(,37,add ( at position 36,flow_matching,0.3,2.0,46,199
191,add,37.0,C,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(C,38,add C at position 37,flow_matching,0.3,2.0,46,199
192,add,38.0,l,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(C,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl,39,add l at position 38,flow_matching,0.3,2.0,46,199
193,add,39.0,),,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl),40,add ) at position 39,flow_matching,0.3,2.0,46,199
194,add,40.0,c,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl),CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c,41,add c at position 40,flow_matching,0.3,2.0,46,199
195,add,41.0,2,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c2,42,add 2 at position 41,flow_matching,0.3,2.0,46,199
196,add,42.0,),,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c2,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c2),43,add ) at position 42,flow_matching,0.3,2.0,46,199
197,add,43.0,C,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c2),CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c2)C,44,add C at position 43,flow_matching,0.3,2.0,46,199
198,add,44.0,1,,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c2)C,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c2)C1,45,add 1 at position 44,flow_matching,0.3,2.0,46,199
199,add,45.0,"
",,CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c2)C1,"CC(C)CN1CCO[C@@H](CNC(=O)/C=C/c2ccnc(Cl)c2)C1
",46,"add 
 at position 45",flow_matching,0.3,2.0,46,199
