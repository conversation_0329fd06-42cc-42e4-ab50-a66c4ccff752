step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,63,231
1,add,0.0,r,,,r,1,add r at position 0,flow_matching,0.3,2.0,63,231
2,replace,0.0,C,r,r,C,1,replace r at position 0 with C,flow_matching,0.3,2.0,63,231
3,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,63,231
4,add,0.0,N,,C[,NC[,3,add N at position 0,flow_matching,0.3,2.0,63,231
5,add,0.0,s,,NC[,sNC[,4,add s at position 0,flow_matching,0.3,2.0,63,231
6,add,1.0,\,,sNC[,s\NC[,5,add \ at position 1,flow_matching,0.3,2.0,63,231
7,remove,4.0,[,,s\NC[,s\NC,4,remove [ from position 4,flow_matching,0.3,2.0,63,231
8,replace,3.0,s,C,s\<PERSON>,s\Ns,4,replace C at position 3 with s,flow_matching,0.3,2.0,63,231
9,remove,1.0,\,,s\Ns,sNs,3,remove \ from position 1,flow_matching,0.3,2.0,63,231
10,add,3.0,S,,sNs,sNsS,4,add S at position 3,flow_matching,0.3,2.0,63,231
11,replace,0.0,5,s,sNsS,5NsS,4,replace s at position 0 with 5,flow_matching,0.3,2.0,63,231
12,add,3.0,#,,5NsS,5Ns#S,5,add # at position 3,flow_matching,0.3,2.0,63,231
13,replace,4.0,B,S,5Ns#S,5Ns#B,5,replace S at position 4 with B,flow_matching,0.3,2.0,63,231
14,replace,1.0,7,N,5Ns#B,57s#B,5,replace N at position 1 with 7,flow_matching,0.3,2.0,63,231
15,replace,0.0,C,5,57s#B,C7s#B,5,replace 5 at position 0 with C,flow_matching,0.3,2.0,63,231
16,add,1.0,\,,C7s#B,C\7s#B,6,add \ at position 1,flow_matching,0.3,2.0,63,231
17,add,4.0,S,,C\7s#B,C\7sS#B,7,add S at position 4,flow_matching,0.3,2.0,63,231
18,replace,0.0,=,C,C\7sS#B,=\7sS#B,7,replace C at position 0 with =,flow_matching,0.3,2.0,63,231
19,add,1.0,n,,=\7sS#B,=n\7sS#B,8,add n at position 1,flow_matching,0.3,2.0,63,231
20,replace,2.0,N,\,=n\7sS#B,=nN7sS#B,8,replace \ at position 2 with N,flow_matching,0.3,2.0,63,231
21,add,6.0,#,,=nN7sS#B,=nN7sS##B,9,add # at position 6,flow_matching,0.3,2.0,63,231
22,add,2.0,(,,=nN7sS##B,=n(N7sS##B,10,add ( at position 2,flow_matching,0.3,2.0,63,231
23,remove,7.0,#,,=n(N7sS##B,=n(N7sS#B,9,remove # from position 7,flow_matching,0.3,2.0,63,231
24,replace,2.0,N,(,=n(N7sS#B,=nNN7sS#B,9,replace ( at position 2 with N,flow_matching,0.3,2.0,63,231
25,remove,8.0,B,,=nNN7sS#B,=nNN7sS#,8,remove B from position 8,flow_matching,0.3,2.0,63,231
26,replace,0.0,C,=,=nNN7sS#,CnNN7sS#,8,replace = at position 0 with C,flow_matching,0.3,2.0,63,231
27,add,6.0,6,,CnNN7sS#,CnNN7s6S#,9,add 6 at position 6,flow_matching,0.3,2.0,63,231
28,replace,4.0,r,7,CnNN7s6S#,CnNNrs6S#,9,replace 7 at position 4 with r,flow_matching,0.3,2.0,63,231
29,add,9.0,1,,CnNNrs6S#,CnNNrs6S#1,10,add 1 at position 9,flow_matching,0.3,2.0,63,231
30,remove,8.0,#,,CnNNrs6S#1,CnNNrs6S1,9,remove # from position 8,flow_matching,0.3,2.0,63,231
31,remove,1.0,n,,CnNNrs6S1,CNNrs6S1,8,remove n from position 1,flow_matching,0.3,2.0,63,231
32,add,0.0,S,,CNNrs6S1,SCNNrs6S1,9,add S at position 0,flow_matching,0.3,2.0,63,231
33,add,3.0,B,,SCNNrs6S1,SCNBNrs6S1,10,add B at position 3,flow_matching,0.3,2.0,63,231
34,replace,0.0,C,S,SCNBNrs6S1,CCNBNrs6S1,10,replace S at position 0 with C,flow_matching,0.3,2.0,63,231
35,replace,1.0,[,C,CCNBNrs6S1,C[NBNrs6S1,10,replace C at position 1 with [,flow_matching,0.3,2.0,63,231
36,remove,0.0,C,,C[NBNrs6S1,[NBNrs6S1,9,remove C from position 0,flow_matching,0.3,2.0,63,231
37,replace,1.0,r,N,[NBNrs6S1,[rBNrs6S1,9,replace N at position 1 with r,flow_matching,0.3,2.0,63,231
38,remove,4.0,r,,[rBNrs6S1,[rBNs6S1,8,remove r from position 4,flow_matching,0.3,2.0,63,231
39,replace,5.0,@,6,[rBNs6S1,[rBNs@S1,8,replace 6 at position 5 with @,flow_matching,0.3,2.0,63,231
40,replace,0.0,C,[,[rBNs@S1,CrBNs@S1,8,replace [ at position 0 with C,flow_matching,0.3,2.0,63,231
41,replace,1.0,[,r,CrBNs@S1,C[BNs@S1,8,replace r at position 1 with [,flow_matching,0.3,2.0,63,231
42,remove,3.0,N,,C[BNs@S1,C[Bs@S1,7,remove N from position 3,flow_matching,0.3,2.0,63,231
43,remove,0.0,C,,C[Bs@S1,[Bs@S1,6,remove C from position 0,flow_matching,0.3,2.0,63,231
44,replace,5.0,2,1,[Bs@S1,[Bs@S2,6,replace 1 at position 5 with 2,flow_matching,0.3,2.0,63,231
45,replace,0.0,C,[,[Bs@S2,CBs@S2,6,replace [ at position 0 with C,flow_matching,0.3,2.0,63,231
46,remove,5.0,2,,CBs@S2,CBs@S,5,remove 2 from position 5,flow_matching,0.3,2.0,63,231
47,replace,1.0,[,B,CBs@S,C[s@S,5,replace B at position 1 with [,flow_matching,0.3,2.0,63,231
48,replace,2.0,1,s,C[s@S,C[1@S,5,replace s at position 2 with 1,flow_matching,0.3,2.0,63,231
49,replace,2.0,C,1,C[1@S,C[C@S,5,replace 1 at position 2 with C,flow_matching,0.3,2.0,63,231
50,replace,4.0,@,S,C[C@S,C[C@@,5,replace S at position 4 with @,flow_matching,0.3,2.0,63,231
51,add,5.0,H,,C[C@@,C[C@@H,6,add H at position 5,flow_matching,0.3,2.0,63,231
52,add,6.0,],,C[C@@H,C[C@@H],7,add ] at position 6,flow_matching,0.3,2.0,63,231
53,add,7.0,(,,C[C@@H],C[C@@H](,8,add ( at position 7,flow_matching,0.3,2.0,63,231
54,remove,4.0,@,,C[C@@H](,C[C@H](,7,remove @ from position 4,flow_matching,0.3,2.0,63,231
55,replace,4.0,@,H,C[C@H](,C[C@@](,7,replace H at position 4 with @,flow_matching,0.3,2.0,63,231
56,add,7.0,\,,C[C@@](,C[C@@](\,8,add \ at position 7,flow_matching,0.3,2.0,63,231
57,replace,5.0,H,],C[C@@](\,C[C@@H(\,8,replace ] at position 5 with H,flow_matching,0.3,2.0,63,231
58,replace,6.0,],(,C[C@@H(\,C[C@@H]\,8,replace ( at position 6 with ],flow_matching,0.3,2.0,63,231
59,replace,7.0,(,\,C[C@@H]\,C[C@@H](,8,replace \ at position 7 with (,flow_matching,0.3,2.0,63,231
60,add,6.0,N,,C[C@@H](,C[C@@HN](,9,add N at position 6,flow_matching,0.3,2.0,63,231
61,remove,1.0,[,,C[C@@HN](,CC@@HN](,8,remove [ from position 1,flow_matching,0.3,2.0,63,231
62,replace,1.0,[,C,CC@@HN](,C[@@HN](,8,replace C at position 1 with [,flow_matching,0.3,2.0,63,231
63,add,2.0,C,,C[@@HN](,C[C@@HN](,9,add C at position 2,flow_matching,0.3,2.0,63,231
64,replace,5.0,O,H,C[C@@HN](,C[C@@ON](,9,replace H at position 5 with O,flow_matching,0.3,2.0,63,231
65,remove,7.0,],,C[C@@ON](,C[C@@ON(,8,remove ] from position 7,flow_matching,0.3,2.0,63,231
66,replace,6.0,3,N,C[C@@ON(,C[C@@O3(,8,replace N at position 6 with 3,flow_matching,0.3,2.0,63,231
67,replace,7.0,S,(,C[C@@O3(,C[C@@O3S,8,replace ( at position 7 with S,flow_matching,0.3,2.0,63,231
68,replace,5.0,H,O,C[C@@O3S,C[C@@H3S,8,replace O at position 5 with H,flow_matching,0.3,2.0,63,231
69,replace,4.0,],@,C[C@@H3S,C[C@]H3S,8,replace @ at position 4 with ],flow_matching,0.3,2.0,63,231
70,replace,4.0,@,],C[C@]H3S,C[C@@H3S,8,replace ] at position 4 with @,flow_matching,0.3,2.0,63,231
71,replace,0.0,#,C,C[C@@H3S,#[C@@H3S,8,replace C at position 0 with #,flow_matching,0.3,2.0,63,231
72,add,1.0,-,,#[C@@H3S,#-[C@@H3S,9,add - at position 1,flow_matching,0.3,2.0,63,231
73,remove,0.0,#,,#-[C@@H3S,-[C@@H3S,8,remove # from position 0,flow_matching,0.3,2.0,63,231
74,add,1.0,#,,-[C@@H3S,-#[C@@H3S,9,add # at position 1,flow_matching,0.3,2.0,63,231
75,add,0.0,),,-#[C@@H3S,)-#[C@@H3S,10,add ) at position 0,flow_matching,0.3,2.0,63,231
76,replace,0.0,C,),)-#[C@@H3S,C-#[C@@H3S,10,replace ) at position 0 with C,flow_matching,0.3,2.0,63,231
77,replace,1.0,[,-,C-#[C@@H3S,C[#[C@@H3S,10,replace - at position 1 with [,flow_matching,0.3,2.0,63,231
78,replace,2.0,C,#,C[#[C@@H3S,C[C[C@@H3S,10,replace # at position 2 with C,flow_matching,0.3,2.0,63,231
79,replace,3.0,@,[,C[C[C@@H3S,C[C@C@@H3S,10,replace [ at position 3 with @,flow_matching,0.3,2.0,63,231
80,add,7.0,),,C[C@C@@H3S,C[C@C@@)H3S,11,add ) at position 7,flow_matching,0.3,2.0,63,231
81,remove,5.0,@,,C[C@C@@)H3S,C[C@C@)H3S,10,remove @ from position 5,flow_matching,0.3,2.0,63,231
82,replace,4.0,@,C,C[C@C@)H3S,C[C@@@)H3S,10,replace C at position 4 with @,flow_matching,0.3,2.0,63,231
83,remove,7.0,H,,C[C@@@)H3S,C[C@@@)3S,9,remove H from position 7,flow_matching,0.3,2.0,63,231
84,replace,4.0,\,@,C[C@@@)3S,C[C@\@)3S,9,replace @ at position 4 with \,flow_matching,0.3,2.0,63,231
85,replace,4.0,@,\,C[C@\@)3S,C[C@@@)3S,9,replace \ at position 4 with @,flow_matching,0.3,2.0,63,231
86,remove,3.0,@,,C[C@@@)3S,C[C@@)3S,8,remove @ from position 3,flow_matching,0.3,2.0,63,231
87,replace,7.0,#,S,C[C@@)3S,C[C@@)3#,8,replace S at position 7 with #,flow_matching,0.3,2.0,63,231
88,remove,5.0,),,C[C@@)3#,C[C@@3#,7,remove ) from position 5,flow_matching,0.3,2.0,63,231
89,replace,4.0,),@,C[C@@3#,C[C@)3#,7,replace @ at position 4 with ),flow_matching,0.3,2.0,63,231
90,remove,5.0,3,,C[C@)3#,C[C@)#,6,remove 3 from position 5,flow_matching,0.3,2.0,63,231
91,replace,4.0,@,),C[C@)#,C[C@@#,6,replace ) at position 4 with @,flow_matching,0.3,2.0,63,231
92,remove,1.0,[,,C[C@@#,CC@@#,5,remove [ from position 1,flow_matching,0.3,2.0,63,231
93,add,2.0,/,,CC@@#,CC/@@#,6,add / at position 2,flow_matching,0.3,2.0,63,231
94,remove,4.0,@,,CC/@@#,CC/@#,5,remove @ from position 4,flow_matching,0.3,2.0,63,231
95,replace,1.0,[,C,CC/@#,C[/@#,5,replace C at position 1 with [,flow_matching,0.3,2.0,63,231
96,replace,2.0,O,/,C[/@#,C[O@#,5,replace / at position 2 with O,flow_matching,0.3,2.0,63,231
97,add,4.0,H,,C[O@#,C[O@H#,6,add H at position 4,flow_matching,0.3,2.0,63,231
98,replace,2.0,C,O,C[O@H#,C[C@H#,6,replace O at position 2 with C,flow_matching,0.3,2.0,63,231
99,add,5.0,F,,C[C@H#,C[C@HF#,7,add F at position 5,flow_matching,0.3,2.0,63,231
100,add,0.0,+,,C[C@HF#,+C[C@HF#,8,add + at position 0,flow_matching,0.3,2.0,63,231
101,remove,6.0,F,,+C[C@HF#,+C[C@H#,7,remove F from position 6,flow_matching,0.3,2.0,63,231
102,add,0.0,(,,+C[C@H#,(+C[C@H#,8,add ( at position 0,flow_matching,0.3,2.0,63,231
103,remove,1.0,+,,(+C[C@H#,(C[C@H#,7,remove + from position 1,flow_matching,0.3,2.0,63,231
104,remove,1.0,C,,(C[C@H#,([C@H#,6,remove C from position 1,flow_matching,0.3,2.0,63,231
105,add,3.0,o,,([C@H#,([Co@H#,7,add o at position 3,flow_matching,0.3,2.0,63,231
106,replace,3.0,l,o,([Co@H#,([Cl@H#,7,replace o at position 3 with l,flow_matching,0.3,2.0,63,231
107,replace,0.0,C,(,([Cl@H#,C[Cl@H#,7,replace ( at position 0 with C,flow_matching,0.3,2.0,63,231
108,add,5.0,H,,C[Cl@H#,C[Cl@HH#,8,add H at position 5,flow_matching,0.3,2.0,63,231
109,replace,3.0,@,l,C[Cl@HH#,C[C@@HH#,8,replace l at position 3 with @,flow_matching,0.3,2.0,63,231
110,replace,6.0,],H,C[C@@HH#,C[C@@H]#,8,replace H at position 6 with ],flow_matching,0.3,2.0,63,231
111,add,8.0,7,,C[C@@H]#,C[C@@H]#7,9,add 7 at position 8,flow_matching,0.3,2.0,63,231
112,add,1.0,],,C[C@@H]#7,C][C@@H]#7,10,add ] at position 1,flow_matching,0.3,2.0,63,231
113,add,2.0,I,,C][C@@H]#7,C]I[C@@H]#7,11,add I at position 2,flow_matching,0.3,2.0,63,231
114,replace,1.0,[,],C]I[C@@H]#7,C[I[C@@H]#7,11,replace ] at position 1 with [,flow_matching,0.3,2.0,63,231
115,remove,1.0,[,,C[I[C@@H]#7,CI[C@@H]#7,10,remove [ from position 1,flow_matching,0.3,2.0,63,231
116,add,6.0,[,,CI[C@@H]#7,CI[C@@[H]#7,11,add [ at position 6,flow_matching,0.3,2.0,63,231
117,replace,4.0,(,@,CI[C@@[H]#7,CI[C(@[H]#7,11,replace @ at position 4 with (,flow_matching,0.3,2.0,63,231
118,add,1.0,n,,CI[C(@[H]#7,CnI[C(@[H]#7,12,add n at position 1,flow_matching,0.3,2.0,63,231
119,replace,1.0,[,n,CnI[C(@[H]#7,C[I[C(@[H]#7,12,replace n at position 1 with [,flow_matching,0.3,2.0,63,231
120,remove,4.0,C,,C[I[C(@[H]#7,C[I[(@[H]#7,11,remove C from position 4,flow_matching,0.3,2.0,63,231
121,replace,2.0,C,I,C[I[(@[H]#7,C[C[(@[H]#7,11,replace I at position 2 with C,flow_matching,0.3,2.0,63,231
122,replace,3.0,@,[,C[C[(@[H]#7,C[C@(@[H]#7,11,replace [ at position 3 with @,flow_matching,0.3,2.0,63,231
123,replace,4.0,7,(,C[C@(@[H]#7,C[C@7@[H]#7,11,replace ( at position 4 with 7,flow_matching,0.3,2.0,63,231
124,add,6.0,H,,C[C@7@[H]#7,C[C@7@H[H]#7,12,add H at position 6,flow_matching,0.3,2.0,63,231
125,remove,5.0,@,,C[C@7@H[H]#7,C[C@7H[H]#7,11,remove @ from position 5,flow_matching,0.3,2.0,63,231
126,replace,4.0,@,7,C[C@7H[H]#7,C[C@@H[H]#7,11,replace 7 at position 4 with @,flow_matching,0.3,2.0,63,231
127,replace,6.0,],[,C[C@@H[H]#7,C[C@@H]H]#7,11,replace [ at position 6 with ],flow_matching,0.3,2.0,63,231
128,replace,7.0,(,H,C[C@@H]H]#7,C[C@@H](]#7,11,replace H at position 7 with (,flow_matching,0.3,2.0,63,231
129,replace,8.0,C,],C[C@@H](]#7,C[C@@H](C#7,11,replace ] at position 8 with C,flow_matching,0.3,2.0,63,231
130,remove,1.0,[,,C[C@@H](C#7,CC@@H](C#7,10,remove [ from position 1,flow_matching,0.3,2.0,63,231
131,replace,2.0,/,@,CC@@H](C#7,CC/@H](C#7,10,replace @ at position 2 with /,flow_matching,0.3,2.0,63,231
132,add,9.0,4,,CC/@H](C#7,CC/@H](C#47,11,add 4 at position 9,flow_matching,0.3,2.0,63,231
133,replace,7.0,+,C,CC/@H](C#47,CC/@H](+#47,11,replace C at position 7 with +,flow_matching,0.3,2.0,63,231
134,replace,1.0,[,C,CC/@H](+#47,C[/@H](+#47,11,replace C at position 1 with [,flow_matching,0.3,2.0,63,231
135,replace,2.0,C,/,C[/@H](+#47,C[C@H](+#47,11,replace / at position 2 with C,flow_matching,0.3,2.0,63,231
136,add,9.0,I,,C[C@H](+#47,C[C@H](+#I47,12,add I at position 9,flow_matching,0.3,2.0,63,231
137,replace,1.0,=,[,C[C@H](+#I47,C=C@H](+#I47,12,replace [ at position 1 with =,flow_matching,0.3,2.0,63,231
138,replace,1.0,[,=,C=C@H](+#I47,C[C@H](+#I47,12,replace = at position 1 with [,flow_matching,0.3,2.0,63,231
139,replace,4.0,@,H,C[C@H](+#I47,C[C@@](+#I47,12,replace H at position 4 with @,flow_matching,0.3,2.0,63,231
140,add,6.0,6,,C[C@@](+#I47,C[C@@]6(+#I47,13,add 6 at position 6,flow_matching,0.3,2.0,63,231
141,replace,4.0,N,@,C[C@@]6(+#I47,C[C@N]6(+#I47,13,replace @ at position 4 with N,flow_matching,0.3,2.0,63,231
142,remove,5.0,],,C[C@N]6(+#I47,C[C@N6(+#I47,12,remove ] from position 5,flow_matching,0.3,2.0,63,231
143,remove,0.0,C,,C[C@N6(+#I47,[C@N6(+#I47,11,remove C from position 0,flow_matching,0.3,2.0,63,231
144,remove,10.0,7,,[C@N6(+#I47,[C@N6(+#I4,10,remove 7 from position 10,flow_matching,0.3,2.0,63,231
145,replace,0.0,C,[,[C@N6(+#I4,CC@N6(+#I4,10,replace [ at position 0 with C,flow_matching,0.3,2.0,63,231
146,replace,7.0,=,#,CC@N6(+#I4,CC@N6(+=I4,10,replace # at position 7 with =,flow_matching,0.3,2.0,63,231
147,remove,3.0,N,,CC@N6(+=I4,CC@6(+=I4,9,remove N from position 3,flow_matching,0.3,2.0,63,231
148,remove,4.0,(,,CC@6(+=I4,CC@6+=I4,8,remove ( from position 4,flow_matching,0.3,2.0,63,231
149,replace,1.0,[,C,CC@6+=I4,C[@6+=I4,8,replace C at position 1 with [,flow_matching,0.3,2.0,63,231
150,replace,2.0,C,@,C[@6+=I4,C[C6+=I4,8,replace @ at position 2 with C,flow_matching,0.3,2.0,63,231
151,replace,3.0,@,6,C[C6+=I4,C[C@+=I4,8,replace 6 at position 3 with @,flow_matching,0.3,2.0,63,231
152,replace,7.0,2,4,C[C@+=I4,C[C@+=I2,8,replace 4 at position 7 with 2,flow_matching,0.3,2.0,63,231
153,add,7.0,I,,C[C@+=I2,C[C@+=II2,9,add I at position 7,flow_matching,0.3,2.0,63,231
154,add,4.0,N,,C[C@+=II2,C[C@N+=II2,10,add N at position 4,flow_matching,0.3,2.0,63,231
155,add,7.0,#,,C[C@N+=II2,C[C@N+=#II2,11,add # at position 7,flow_matching,0.3,2.0,63,231
156,add,11.0,C,,C[C@N+=#II2,C[C@N+=#II2C,12,add C at position 11,flow_matching,0.3,2.0,63,231
157,replace,4.0,@,N,C[C@N+=#II2C,C[C@@+=#II2C,12,replace N at position 4 with @,flow_matching,0.3,2.0,63,231
158,replace,5.0,H,+,C[C@@+=#II2C,C[C@@H=#II2C,12,replace + at position 5 with H,flow_matching,0.3,2.0,63,231
159,replace,6.0,],=,C[C@@H=#II2C,C[C@@H]#II2C,12,replace = at position 6 with ],flow_matching,0.3,2.0,63,231
160,replace,7.0,(,#,C[C@@H]#II2C,C[C@@H](II2C,12,replace # at position 7 with (,flow_matching,0.3,2.0,63,231
161,replace,6.0,/,],C[C@@H](II2C,C[C@@H/(II2C,12,replace ] at position 6 with /,flow_matching,0.3,2.0,63,231
162,replace,4.0,o,@,C[C@@H/(II2C,C[C@oH/(II2C,12,replace @ at position 4 with o,flow_matching,0.3,2.0,63,231
163,remove,10.0,2,,C[C@oH/(II2C,C[C@oH/(IIC,11,remove 2 from position 10,flow_matching,0.3,2.0,63,231
164,remove,9.0,I,,C[C@oH/(IIC,C[C@oH/(IC,10,remove I from position 9,flow_matching,0.3,2.0,63,231
165,add,2.0,3,,C[C@oH/(IC,C[3C@oH/(IC,11,add 3 at position 2,flow_matching,0.3,2.0,63,231
166,replace,5.0,C,o,C[3C@oH/(IC,C[3C@CH/(IC,11,replace o at position 5 with C,flow_matching,0.3,2.0,63,231
167,replace,2.0,C,3,C[3C@CH/(IC,C[CC@CH/(IC,11,replace 3 at position 2 with C,flow_matching,0.3,2.0,63,231
168,replace,3.0,@,C,C[CC@CH/(IC,C[C@@CH/(IC,11,replace C at position 3 with @,flow_matching,0.3,2.0,63,231
169,replace,3.0,B,@,C[C@@CH/(IC,C[CB@CH/(IC,11,replace @ at position 3 with B,flow_matching,0.3,2.0,63,231
170,add,2.0,(,,C[CB@CH/(IC,C[(CB@CH/(IC,12,add ( at position 2,flow_matching,0.3,2.0,63,231
171,replace,2.0,C,(,C[(CB@CH/(IC,C[CCB@CH/(IC,12,replace ( at position 2 with C,flow_matching,0.3,2.0,63,231
172,replace,7.0,7,H,C[CCB@CH/(IC,C[CCB@C7/(IC,12,replace H at position 7 with 7,flow_matching,0.3,2.0,63,231
173,replace,3.0,@,C,C[CCB@C7/(IC,C[C@B@C7/(IC,12,replace C at position 3 with @,flow_matching,0.3,2.0,63,231
174,replace,4.0,@,B,C[C@B@C7/(IC,C[C@@@C7/(IC,12,replace B at position 4 with @,flow_matching,0.3,2.0,63,231
175,replace,5.0,H,@,C[C@@@C7/(IC,C[C@@HC7/(IC,12,replace @ at position 5 with H,flow_matching,0.3,2.0,63,231
176,replace,6.0,],C,C[C@@HC7/(IC,C[C@@H]7/(IC,12,replace C at position 6 with ],flow_matching,0.3,2.0,63,231
177,replace,7.0,(,7,C[C@@H]7/(IC,C[C@@H](/(IC,12,replace 7 at position 7 with (,flow_matching,0.3,2.0,63,231
178,replace,8.0,C,/,C[C@@H](/(IC,C[C@@H](C(IC,12,replace / at position 8 with C,flow_matching,0.3,2.0,63,231
179,replace,10.0,=,I,C[C@@H](C(IC,C[C@@H](C(=C,12,replace I at position 10 with =,flow_matching,0.3,2.0,63,231
180,replace,11.0,O,C,C[C@@H](C(=C,C[C@@H](C(=O,12,replace C at position 11 with O,flow_matching,0.3,2.0,63,231
181,add,12.0,),,C[C@@H](C(=O,C[C@@H](C(=O),13,add ) at position 12,flow_matching,0.3,2.0,63,231
182,add,13.0,C,,C[C@@H](C(=O),C[C@@H](C(=O)C,14,add C at position 13,flow_matching,0.3,2.0,63,231
183,add,14.0,1,,C[C@@H](C(=O)C,C[C@@H](C(=O)C1,15,add 1 at position 14,flow_matching,0.3,2.0,63,231
184,add,15.0,=,,C[C@@H](C(=O)C1,C[C@@H](C(=O)C1=,16,add = at position 15,flow_matching,0.3,2.0,63,231
185,add,16.0,c,,C[C@@H](C(=O)C1=,C[C@@H](C(=O)C1=c,17,add c at position 16,flow_matching,0.3,2.0,63,231
186,add,17.0,2,,C[C@@H](C(=O)C1=c,C[C@@H](C(=O)C1=c2,18,add 2 at position 17,flow_matching,0.3,2.0,63,231
187,add,18.0,c,,C[C@@H](C(=O)C1=c2,C[C@@H](C(=O)C1=c2c,19,add c at position 18,flow_matching,0.3,2.0,63,231
188,add,19.0,c,,C[C@@H](C(=O)C1=c2c,C[C@@H](C(=O)C1=c2cc,20,add c at position 19,flow_matching,0.3,2.0,63,231
189,add,20.0,c,,C[C@@H](C(=O)C1=c2cc,C[C@@H](C(=O)C1=c2ccc,21,add c at position 20,flow_matching,0.3,2.0,63,231
190,add,21.0,c,,C[C@@H](C(=O)C1=c2ccc,C[C@@H](C(=O)C1=c2cccc,22,add c at position 21,flow_matching,0.3,2.0,63,231
191,add,22.0,c,,C[C@@H](C(=O)C1=c2cccc,C[C@@H](C(=O)C1=c2ccccc,23,add c at position 22,flow_matching,0.3,2.0,63,231
192,add,23.0,2,,C[C@@H](C(=O)C1=c2ccccc,C[C@@H](C(=O)C1=c2ccccc2,24,add 2 at position 23,flow_matching,0.3,2.0,63,231
193,add,24.0,=,,C[C@@H](C(=O)C1=c2ccccc2,C[C@@H](C(=O)C1=c2ccccc2=,25,add = at position 24,flow_matching,0.3,2.0,63,231
194,add,25.0,[,,C[C@@H](C(=O)C1=c2ccccc2=,C[C@@H](C(=O)C1=c2ccccc2=[,26,add [ at position 25,flow_matching,0.3,2.0,63,231
195,add,26.0,N,,C[C@@H](C(=O)C1=c2ccccc2=[,C[C@@H](C(=O)C1=c2ccccc2=[N,27,add N at position 26,flow_matching,0.3,2.0,63,231
196,add,27.0,H,,C[C@@H](C(=O)C1=c2ccccc2=[N,C[C@@H](C(=O)C1=c2ccccc2=[NH,28,add H at position 27,flow_matching,0.3,2.0,63,231
197,add,28.0,+,,C[C@@H](C(=O)C1=c2ccccc2=[NH,C[C@@H](C(=O)C1=c2ccccc2=[NH+,29,add + at position 28,flow_matching,0.3,2.0,63,231
198,add,29.0,],,C[C@@H](C(=O)C1=c2ccccc2=[NH+,C[C@@H](C(=O)C1=c2ccccc2=[NH+],30,add ] at position 29,flow_matching,0.3,2.0,63,231
199,add,30.0,C,,C[C@@H](C(=O)C1=c2ccccc2=[NH+],C[C@@H](C(=O)C1=c2ccccc2=[NH+]C,31,add C at position 30,flow_matching,0.3,2.0,63,231
200,add,31.0,1,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1,32,add 1 at position 31,flow_matching,0.3,2.0,63,231
201,add,32.0,),,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1),33,add ) at position 32,flow_matching,0.3,2.0,63,231
202,add,33.0,[,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1),C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[,34,add [ at position 33,flow_matching,0.3,2.0,63,231
203,add,34.0,N,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[N,35,add N at position 34,flow_matching,0.3,2.0,63,231
204,add,35.0,H,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[N,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH,36,add H at position 35,flow_matching,0.3,2.0,63,231
205,add,36.0,+,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+,37,add + at position 36,flow_matching,0.3,2.0,63,231
206,add,37.0,],,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+],38,add ] at position 37,flow_matching,0.3,2.0,63,231
207,add,38.0,1,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+],C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1,39,add 1 at position 38,flow_matching,0.3,2.0,63,231
208,add,39.0,C,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1C,40,add C at position 39,flow_matching,0.3,2.0,63,231
209,add,40.0,C,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1C,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CC,41,add C at position 40,flow_matching,0.3,2.0,63,231
210,add,41.0,C,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CC,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC,42,add C at position 41,flow_matching,0.3,2.0,63,231
211,add,42.0,[,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[,43,add [ at position 42,flow_matching,0.3,2.0,63,231
212,add,43.0,C,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C,44,add C at position 43,flow_matching,0.3,2.0,63,231
213,add,44.0,@,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@,45,add @ at position 44,flow_matching,0.3,2.0,63,231
214,add,45.0,@,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@,46,add @ at position 45,flow_matching,0.3,2.0,63,231
215,add,46.0,H,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H,47,add H at position 46,flow_matching,0.3,2.0,63,231
216,add,47.0,],,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H],48,add ] at position 47,flow_matching,0.3,2.0,63,231
217,add,48.0,1,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H],C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1,49,add 1 at position 48,flow_matching,0.3,2.0,63,231
218,add,49.0,[,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[,50,add [ at position 49,flow_matching,0.3,2.0,63,231
219,add,50.0,C,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C,51,add C at position 50,flow_matching,0.3,2.0,63,231
220,add,51.0,@,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@,52,add @ at position 51,flow_matching,0.3,2.0,63,231
221,add,52.0,@,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@,53,add @ at position 52,flow_matching,0.3,2.0,63,231
222,add,53.0,H,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H,54,add H at position 53,flow_matching,0.3,2.0,63,231
223,add,54.0,],,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H],55,add ] at position 54,flow_matching,0.3,2.0,63,231
224,add,55.0,1,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H],C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1,56,add 1 at position 55,flow_matching,0.3,2.0,63,231
225,add,56.0,C,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1C,57,add C at position 56,flow_matching,0.3,2.0,63,231
226,add,57.0,C,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1C,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC,58,add C at position 57,flow_matching,0.3,2.0,63,231
227,add,58.0,=,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC=,59,add = at position 58,flow_matching,0.3,2.0,63,231
228,add,59.0,C,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC=,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC=C,60,add C at position 59,flow_matching,0.3,2.0,63,231
229,add,60.0,S,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC=C,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC=CS,61,add S at position 60,flow_matching,0.3,2.0,63,231
230,add,61.0,1,,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC=CS,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC=CS1,62,add 1 at position 61,flow_matching,0.3,2.0,63,231
231,add,62.0,"
",,C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC=CS1,"C[C@@H](C(=O)C1=c2ccccc2=[NH+]C1)[NH+]1CCC[C@@H]1[C@@H]1CC=CS1
",63,"add 
 at position 62",flow_matching,0.3,2.0,63,231
