step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,35,125
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,35,125
2,replace,0.0,s,C,C,s,1,replace <PERSON> at position 0 with s,flow_matching,0.3,2.0,35,125
3,replace,0.0,3,s,s,3,1,replace s at position 0 with 3,flow_matching,0.3,2.0,35,125
4,replace,0.0,C,3,3,C,1,replace 3 at position 0 with C,flow_matching,0.3,2.0,35,125
5,replace,0.0,/,C,C,/,1,replace C at position 0 with /,flow_matching,0.3,2.0,35,125
6,add,1.0,N,,/,/N,2,add N at position 1,flow_matching,0.3,2.0,35,125
7,remove,0.0,/,,/N,N,1,remove / from position 0,flow_matching,0.3,2.0,35,125
8,replace,0.0,C,N,N,C,1,replace N at position 0 with C,flow_matching,0.3,2.0,35,125
9,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,35,125
10,add,0.0,=,,CO,=CO,3,add = at position 0,flow_matching,0.3,2.0,35,125
11,replace,0.0,C,=,=CO,CCO,3,replace = at position 0 with C,flow_matching,0.3,2.0,35,125
12,remove,1.0,C,,CCO,CO,2,remove C from position 1,flow_matching,0.3,2.0,35,125
13,replace,0.0,B,C,CO,BO,2,replace C at position 0 with B,flow_matching,0.3,2.0,35,125
14,add,1.0,),,BO,B)O,3,add ) at position 1,flow_matching,0.3,2.0,35,125
15,add,0.0,@,,B)O,@B)O,4,add @ at position 0,flow_matching,0.3,2.0,35,125
16,replace,2.0,=,),@B)O,@B=O,4,replace ) at position 2 with =,flow_matching,0.3,2.0,35,125
17,replace,0.0,r,@,@B=O,rB=O,4,replace @ at position 0 with r,flow_matching,0.3,2.0,35,125
18,remove,2.0,=,,rB=O,rBO,3,remove = from position 2,flow_matching,0.3,2.0,35,125
19,remove,2.0,O,,rBO,rB,2,remove O from position 2,flow_matching,0.3,2.0,35,125
20,add,2.0,O,,rB,rBO,3,add O at position 2,flow_matching,0.3,2.0,35,125
21,replace,0.0,C,r,rBO,CBO,3,replace r at position 0 with C,flow_matching,0.3,2.0,35,125
22,replace,1.0,O,B,CBO,COO,3,replace B at position 1 with O,flow_matching,0.3,2.0,35,125
23,add,1.0,I,,COO,CIOO,4,add I at position 1,flow_matching,0.3,2.0,35,125
24,replace,3.0,+,O,CIOO,CIO+,4,replace O at position 3 with +,flow_matching,0.3,2.0,35,125
25,add,4.0,#,,CIO+,CIO+#,5,add # at position 4,flow_matching,0.3,2.0,35,125
26,replace,1.0,],I,CIO+#,C]O+#,5,replace I at position 1 with ],flow_matching,0.3,2.0,35,125
27,replace,1.0,\,],C]O+#,C\O+#,5,replace ] at position 1 with \,flow_matching,0.3,2.0,35,125
28,remove,0.0,C,,C\O+#,\O+#,4,remove C from position 0,flow_matching,0.3,2.0,35,125
29,remove,3.0,#,,\O+#,\O+,3,remove # from position 3,flow_matching,0.3,2.0,35,125
30,replace,0.0,C,\,\O+,CO+,3,replace \ at position 0 with C,flow_matching,0.3,2.0,35,125
31,add,3.0,F,,CO+,CO+F,4,add F at position 3,flow_matching,0.3,2.0,35,125
32,remove,1.0,O,,CO+F,C+F,3,remove O from position 1,flow_matching,0.3,2.0,35,125
33,add,0.0,s,,C+F,sC+F,4,add s at position 0,flow_matching,0.3,2.0,35,125
34,replace,0.0,C,s,sC+F,CC+F,4,replace s at position 0 with C,flow_matching,0.3,2.0,35,125
35,remove,0.0,C,,CC+F,C+F,3,remove C from position 0,flow_matching,0.3,2.0,35,125
36,replace,1.0,O,+,C+F,COF,3,replace + at position 1 with O,flow_matching,0.3,2.0,35,125
37,replace,1.0,5,O,COF,C5F,3,replace O at position 1 with 5,flow_matching,0.3,2.0,35,125
38,add,0.0,5,,C5F,5C5F,4,add 5 at position 0,flow_matching,0.3,2.0,35,125
39,remove,3.0,F,,5C5F,5C5,3,remove F from position 3,flow_matching,0.3,2.0,35,125
40,replace,0.0,C,5,5C5,CC5,3,replace 5 at position 0 with C,flow_matching,0.3,2.0,35,125
41,replace,0.0,=,C,CC5,=C5,3,replace C at position 0 with =,flow_matching,0.3,2.0,35,125
42,replace,0.0,C,=,=C5,CC5,3,replace = at position 0 with C,flow_matching,0.3,2.0,35,125
43,add,0.0,],,CC5,]CC5,4,add ] at position 0,flow_matching,0.3,2.0,35,125
44,replace,0.0,c,],]CC5,cCC5,4,replace ] at position 0 with c,flow_matching,0.3,2.0,35,125
45,replace,3.0,#,5,cCC5,cCC#,4,replace 5 at position 3 with #,flow_matching,0.3,2.0,35,125
46,replace,0.0,4,c,cCC#,4CC#,4,replace c at position 0 with 4,flow_matching,0.3,2.0,35,125
47,add,0.0,5,,4CC#,54CC#,5,add 5 at position 0,flow_matching,0.3,2.0,35,125
48,remove,1.0,4,,54CC#,5CC#,4,remove 4 from position 1,flow_matching,0.3,2.0,35,125
49,add,3.0,c,,5CC#,5CCc#,5,add c at position 3,flow_matching,0.3,2.0,35,125
50,remove,2.0,C,,5CCc#,5Cc#,4,remove C from position 2,flow_matching,0.3,2.0,35,125
51,remove,3.0,#,,5Cc#,5Cc,3,remove # from position 3,flow_matching,0.3,2.0,35,125
52,replace,0.0,C,5,5Cc,CCc,3,replace 5 at position 0 with C,flow_matching,0.3,2.0,35,125
53,replace,1.0,7,C,CCc,C7c,3,replace C at position 1 with 7,flow_matching,0.3,2.0,35,125
54,add,3.0,(,,C7c,C7c(,4,add ( at position 3,flow_matching,0.3,2.0,35,125
55,add,1.0,2,,C7c(,C27c(,5,add 2 at position 1,flow_matching,0.3,2.0,35,125
56,add,2.0,c,,C27c(,C2c7c(,6,add c at position 2,flow_matching,0.3,2.0,35,125
57,replace,3.0,F,7,C2c7c(,C2cFc(,6,replace 7 at position 3 with F,flow_matching,0.3,2.0,35,125
58,replace,5.0,],(,C2cFc(,C2cFc],6,replace ( at position 5 with ],flow_matching,0.3,2.0,35,125
59,add,1.0,s,,C2cFc],Cs2cFc],7,add s at position 1,flow_matching,0.3,2.0,35,125
60,remove,6.0,],,Cs2cFc],Cs2cFc,6,remove ] from position 6,flow_matching,0.3,2.0,35,125
61,replace,1.0,O,s,Cs2cFc,CO2cFc,6,replace s at position 1 with O,flow_matching,0.3,2.0,35,125
62,replace,4.0,O,F,CO2cFc,CO2cOc,6,replace F at position 4 with O,flow_matching,0.3,2.0,35,125
63,replace,2.0,+,2,CO2cOc,CO+cOc,6,replace 2 at position 2 with +,flow_matching,0.3,2.0,35,125
64,replace,2.0,C,+,CO+cOc,COCcOc,6,replace + at position 2 with C,flow_matching,0.3,2.0,35,125
65,replace,3.0,(,c,COCcOc,COC(Oc,6,replace c at position 3 with (,flow_matching,0.3,2.0,35,125
66,replace,4.0,=,O,COC(Oc,COC(=c,6,replace O at position 4 with =,flow_matching,0.3,2.0,35,125
67,replace,2.0,n,C,COC(=c,COn(=c,6,replace C at position 2 with n,flow_matching,0.3,2.0,35,125
68,remove,4.0,=,,COn(=c,COn(c,5,remove = from position 4,flow_matching,0.3,2.0,35,125
69,replace,2.0,C,n,COn(c,COC(c,5,replace n at position 2 with C,flow_matching,0.3,2.0,35,125
70,replace,4.0,=,c,COC(c,COC(=,5,replace c at position 4 with =,flow_matching,0.3,2.0,35,125
71,add,5.0,O,,COC(=,COC(=O,6,add O at position 5,flow_matching,0.3,2.0,35,125
72,add,6.0,n,,COC(=O,COC(=On,7,add n at position 6,flow_matching,0.3,2.0,35,125
73,replace,1.0,2,O,COC(=On,C2C(=On,7,replace O at position 1 with 2,flow_matching,0.3,2.0,35,125
74,replace,6.0,],n,C2C(=On,C2C(=O],7,replace n at position 6 with ],flow_matching,0.3,2.0,35,125
75,add,2.0,2,,C2C(=O],C22C(=O],8,add 2 at position 2,flow_matching,0.3,2.0,35,125
76,add,2.0,S,,C22C(=O],C2S2C(=O],9,add S at position 2,flow_matching,0.3,2.0,35,125
77,replace,1.0,O,2,C2S2C(=O],COS2C(=O],9,replace 2 at position 1 with O,flow_matching,0.3,2.0,35,125
78,add,0.0,),,COS2C(=O],)COS2C(=O],10,add ) at position 0,flow_matching,0.3,2.0,35,125
79,replace,0.0,C,),)COS2C(=O],CCOS2C(=O],10,replace ) at position 0 with C,flow_matching,0.3,2.0,35,125
80,remove,2.0,O,,CCOS2C(=O],CCS2C(=O],9,remove O from position 2,flow_matching,0.3,2.0,35,125
81,remove,7.0,O,,CCS2C(=O],CCS2C(=],8,remove O from position 7,flow_matching,0.3,2.0,35,125
82,replace,5.0,O,(,CCS2C(=],CCS2CO=],8,replace ( at position 5 with O,flow_matching,0.3,2.0,35,125
83,add,6.0,r,,CCS2CO=],CCS2COr=],9,add r at position 6,flow_matching,0.3,2.0,35,125
84,add,1.0,7,,CCS2COr=],C7CS2COr=],10,add 7 at position 1,flow_matching,0.3,2.0,35,125
85,replace,1.0,O,7,C7CS2COr=],COCS2COr=],10,replace 7 at position 1 with O,flow_matching,0.3,2.0,35,125
86,add,2.0,@,,COCS2COr=],CO@CS2COr=],11,add @ at position 2,flow_matching,0.3,2.0,35,125
87,replace,4.0,r,S,CO@CS2COr=],CO@Cr2COr=],11,replace S at position 4 with r,flow_matching,0.3,2.0,35,125
88,replace,2.0,C,@,CO@Cr2COr=],COCCr2COr=],11,replace @ at position 2 with C,flow_matching,0.3,2.0,35,125
89,replace,3.0,(,C,COCCr2COr=],COC(r2COr=],11,replace C at position 3 with (,flow_matching,0.3,2.0,35,125
90,replace,1.0,),O,COC(r2COr=],C)C(r2COr=],11,replace O at position 1 with ),flow_matching,0.3,2.0,35,125
91,remove,9.0,=,,C)C(r2COr=],C)C(r2COr],10,remove = from position 9,flow_matching,0.3,2.0,35,125
92,replace,1.0,O,),C)C(r2COr],COC(r2COr],10,replace ) at position 1 with O,flow_matching,0.3,2.0,35,125
93,add,5.0,3,,COC(r2COr],COC(r32COr],11,add 3 at position 5,flow_matching,0.3,2.0,35,125
94,add,3.0,n,,COC(r32COr],COCn(r32COr],12,add n at position 3,flow_matching,0.3,2.0,35,125
95,replace,3.0,(,n,COCn(r32COr],COC((r32COr],12,replace n at position 3 with (,flow_matching,0.3,2.0,35,125
96,replace,4.0,=,(,COC((r32COr],COC(=r32COr],12,replace ( at position 4 with =,flow_matching,0.3,2.0,35,125
97,replace,5.0,O,r,COC(=r32COr],COC(=O32COr],12,replace r at position 5 with O,flow_matching,0.3,2.0,35,125
98,replace,6.0,),3,COC(=O32COr],COC(=O)2COr],12,replace 3 at position 6 with ),flow_matching,0.3,2.0,35,125
99,replace,7.0,[,2,COC(=O)2COr],COC(=O)[COr],12,replace 2 at position 7 with [,flow_matching,0.3,2.0,35,125
100,replace,9.0,@,O,COC(=O)[COr],COC(=O)[C@r],12,replace O at position 9 with @,flow_matching,0.3,2.0,35,125
101,replace,10.0,@,r,COC(=O)[C@r],COC(=O)[C@@],12,replace r at position 10 with @,flow_matching,0.3,2.0,35,125
102,replace,11.0,H,],COC(=O)[C@@],COC(=O)[C@@H,12,replace ] at position 11 with H,flow_matching,0.3,2.0,35,125
103,add,12.0,],,COC(=O)[C@@H,COC(=O)[C@@H],13,add ] at position 12,flow_matching,0.3,2.0,35,125
104,add,13.0,(,,COC(=O)[C@@H],COC(=O)[C@@H](,14,add ( at position 13,flow_matching,0.3,2.0,35,125
105,add,14.0,c,,COC(=O)[C@@H](,COC(=O)[C@@H](c,15,add c at position 14,flow_matching,0.3,2.0,35,125
106,add,15.0,1,,COC(=O)[C@@H](c,COC(=O)[C@@H](c1,16,add 1 at position 15,flow_matching,0.3,2.0,35,125
107,add,16.0,c,,COC(=O)[C@@H](c1,COC(=O)[C@@H](c1c,17,add c at position 16,flow_matching,0.3,2.0,35,125
108,add,17.0,c,,COC(=O)[C@@H](c1c,COC(=O)[C@@H](c1cc,18,add c at position 17,flow_matching,0.3,2.0,35,125
109,add,18.0,c,,COC(=O)[C@@H](c1cc,COC(=O)[C@@H](c1ccc,19,add c at position 18,flow_matching,0.3,2.0,35,125
110,add,19.0,c,,COC(=O)[C@@H](c1ccc,COC(=O)[C@@H](c1cccc,20,add c at position 19,flow_matching,0.3,2.0,35,125
111,add,20.0,c,,COC(=O)[C@@H](c1cccc,COC(=O)[C@@H](c1ccccc,21,add c at position 20,flow_matching,0.3,2.0,35,125
112,add,21.0,1,,COC(=O)[C@@H](c1ccccc,COC(=O)[C@@H](c1ccccc1,22,add 1 at position 21,flow_matching,0.3,2.0,35,125
113,add,22.0,C,,COC(=O)[C@@H](c1ccccc1,COC(=O)[C@@H](c1ccccc1C,23,add C at position 22,flow_matching,0.3,2.0,35,125
114,add,23.0,l,,COC(=O)[C@@H](c1ccccc1C,COC(=O)[C@@H](c1ccccc1Cl,24,add l at position 23,flow_matching,0.3,2.0,35,125
115,add,24.0,),,COC(=O)[C@@H](c1ccccc1Cl,COC(=O)[C@@H](c1ccccc1Cl),25,add ) at position 24,flow_matching,0.3,2.0,35,125
116,add,25.0,N,,COC(=O)[C@@H](c1ccccc1Cl),COC(=O)[C@@H](c1ccccc1Cl)N,26,add N at position 25,flow_matching,0.3,2.0,35,125
117,add,26.0,1,,COC(=O)[C@@H](c1ccccc1Cl)N,COC(=O)[C@@H](c1ccccc1Cl)N1,27,add 1 at position 26,flow_matching,0.3,2.0,35,125
118,add,27.0,C,,COC(=O)[C@@H](c1ccccc1Cl)N1,COC(=O)[C@@H](c1ccccc1Cl)N1C,28,add C at position 27,flow_matching,0.3,2.0,35,125
119,add,28.0,C,,COC(=O)[C@@H](c1ccccc1Cl)N1C,COC(=O)[C@@H](c1ccccc1Cl)N1CC,29,add C at position 28,flow_matching,0.3,2.0,35,125
120,add,29.0,C,,COC(=O)[C@@H](c1ccccc1Cl)N1CC,COC(=O)[C@@H](c1ccccc1Cl)N1CCC,30,add C at position 29,flow_matching,0.3,2.0,35,125
121,add,30.0,S,,COC(=O)[C@@H](c1ccccc1Cl)N1CCC,COC(=O)[C@@H](c1ccccc1Cl)N1CCCS,31,add S at position 30,flow_matching,0.3,2.0,35,125
122,add,31.0,C,,COC(=O)[C@@H](c1ccccc1Cl)N1CCCS,COC(=O)[C@@H](c1ccccc1Cl)N1CCCSC,32,add C at position 31,flow_matching,0.3,2.0,35,125
123,add,32.0,C,,COC(=O)[C@@H](c1ccccc1Cl)N1CCCSC,COC(=O)[C@@H](c1ccccc1Cl)N1CCCSCC,33,add C at position 32,flow_matching,0.3,2.0,35,125
124,add,33.0,1,,COC(=O)[C@@H](c1ccccc1Cl)N1CCCSCC,COC(=O)[C@@H](c1ccccc1Cl)N1CCCSCC1,34,add 1 at position 33,flow_matching,0.3,2.0,35,125
125,add,34.0,"
",,COC(=O)[C@@H](c1ccccc1Cl)N1CCCSCC1,"COC(=O)[C@@H](c1ccccc1Cl)N1CCCSCC1
",35,"add 
 at position 34",flow_matching,0.3,2.0,35,125
