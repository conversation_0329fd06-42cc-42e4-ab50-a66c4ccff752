step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,58,133
1,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,58,133
2,add,1.0,C,,c,cC,2,add C at position 1,flow_matching,0.3,2.0,58,133
3,remove,1.0,C,,cC,c,1,remove C from position 1,flow_matching,0.3,2.0,58,133
4,replace,0.0,=,c,c,=,1,replace c at position 0 with =,flow_matching,0.3,2.0,58,133
5,remove,0.0,=,,=,,0,remove = from position 0,flow_matching,0.3,2.0,58,133
6,add,0.0,l,,,l,1,add l at position 0,flow_matching,0.3,2.0,58,133
7,replace,0.0,C,l,l,C,1,replace l at position 0 with C,flow_matching,0.3,2.0,58,133
8,add,1.0,4,,C,C4,2,add 4 at position 1,flow_matching,0.3,2.0,58,133
9,replace,1.0,O,4,C4,CO,2,replace 4 at position 1 with O,flow_matching,0.3,2.0,58,133
10,replace,1.0,n,O,CO,Cn,2,replace O at position 1 with n,flow_matching,0.3,2.0,58,133
11,replace,1.0,O,n,Cn,CO,2,replace n at position 1 with O,flow_matching,0.3,2.0,58,133
12,remove,1.0,O,,CO,C,1,remove O from position 1,flow_matching,0.3,2.0,58,133
13,add,1.0,2,,C,C2,2,add 2 at position 1,flow_matching,0.3,2.0,58,133
14,add,0.0,@,,C2,@C2,3,add @ at position 0,flow_matching,0.3,2.0,58,133
15,add,1.0,),,@C2,@)C2,4,add ) at position 1,flow_matching,0.3,2.0,58,133
16,replace,3.0,O,2,@)C2,@)CO,4,replace 2 at position 3 with O,flow_matching,0.3,2.0,58,133
17,replace,0.0,C,@,@)CO,C)CO,4,replace @ at position 0 with C,flow_matching,0.3,2.0,58,133
18,remove,3.0,O,,C)CO,C)C,3,remove O from position 3,flow_matching,0.3,2.0,58,133
19,replace,1.0,O,),C)C,COC,3,replace ) at position 1 with O,flow_matching,0.3,2.0,58,133
20,replace,2.0,c,C,COC,COc,3,replace C at position 2 with c,flow_matching,0.3,2.0,58,133
21,add,3.0,1,,COc,COc1,4,add 1 at position 3,flow_matching,0.3,2.0,58,133
22,remove,0.0,C,,COc1,Oc1,3,remove C from position 0,flow_matching,0.3,2.0,58,133
23,remove,2.0,1,,Oc1,Oc,2,remove 1 from position 2,flow_matching,0.3,2.0,58,133
24,remove,1.0,c,,Oc,O,1,remove c from position 1,flow_matching,0.3,2.0,58,133
25,replace,0.0,B,O,O,B,1,replace O at position 0 with B,flow_matching,0.3,2.0,58,133
26,replace,0.0,F,B,B,F,1,replace B at position 0 with F,flow_matching,0.3,2.0,58,133
27,add,1.0,5,,F,F5,2,add 5 at position 1,flow_matching,0.3,2.0,58,133
28,remove,1.0,5,,F5,F,1,remove 5 from position 1,flow_matching,0.3,2.0,58,133
29,replace,0.0,),F,F,),1,replace F at position 0 with ),flow_matching,0.3,2.0,58,133
30,replace,0.0,C,),),C,1,replace ) at position 0 with C,flow_matching,0.3,2.0,58,133
31,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,58,133
32,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,58,133
33,add,1.0,6,,O,O6,2,add 6 at position 1,flow_matching,0.3,2.0,58,133
34,remove,1.0,6,,O6,O,1,remove 6 from position 1,flow_matching,0.3,2.0,58,133
35,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,58,133
36,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,58,133
37,replace,0.0,\,+,+,\,1,replace + at position 0 with \,flow_matching,0.3,2.0,58,133
38,add,1.0,F,,\,\F,2,add F at position 1,flow_matching,0.3,2.0,58,133
39,replace,1.0,6,F,\F,\6,2,replace F at position 1 with 6,flow_matching,0.3,2.0,58,133
40,replace,0.0,C,\,\6,C6,2,replace \ at position 0 with C,flow_matching,0.3,2.0,58,133
41,remove,1.0,6,,C6,C,1,remove 6 from position 1,flow_matching,0.3,2.0,58,133
42,replace,0.0,6,C,C,6,1,replace C at position 0 with 6,flow_matching,0.3,2.0,58,133
43,replace,0.0,C,6,6,C,1,replace 6 at position 0 with C,flow_matching,0.3,2.0,58,133
44,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,58,133
45,add,2.0,N,,CO,CON,3,add N at position 2,flow_matching,0.3,2.0,58,133
46,add,1.0,@,,CON,C@ON,4,add @ at position 1,flow_matching,0.3,2.0,58,133
47,replace,1.0,O,@,C@ON,COON,4,replace @ at position 1 with O,flow_matching,0.3,2.0,58,133
48,replace,2.0,c,O,COON,COcN,4,replace O at position 2 with c,flow_matching,0.3,2.0,58,133
49,replace,3.0,1,N,COcN,COc1,4,replace N at position 3 with 1,flow_matching,0.3,2.0,58,133
50,add,4.0,#,,COc1,COc1#,5,add # at position 4,flow_matching,0.3,2.0,58,133
51,replace,4.0,c,#,COc1#,COc1c,5,replace # at position 4 with c,flow_matching,0.3,2.0,58,133
52,add,0.0,\,,COc1c,\COc1c,6,add \ at position 0,flow_matching,0.3,2.0,58,133
53,remove,4.0,1,,\COc1c,\COcc,5,remove 1 from position 4,flow_matching,0.3,2.0,58,133
54,replace,0.0,C,\,\COcc,CCOcc,5,replace \ at position 0 with C,flow_matching,0.3,2.0,58,133
55,replace,1.0,O,C,CCOcc,COOcc,5,replace C at position 1 with O,flow_matching,0.3,2.0,58,133
56,add,4.0,-,,COOcc,COOc-c,6,add - at position 4,flow_matching,0.3,2.0,58,133
57,replace,2.0,c,O,COOc-c,COcc-c,6,replace O at position 2 with c,flow_matching,0.3,2.0,58,133
58,replace,3.0,o,c,COcc-c,COco-c,6,replace c at position 3 with o,flow_matching,0.3,2.0,58,133
59,replace,4.0,),-,COco-c,COco)c,6,replace - at position 4 with ),flow_matching,0.3,2.0,58,133
60,add,4.0,C,,COco)c,COcoC)c,7,add C at position 4,flow_matching,0.3,2.0,58,133
61,replace,3.0,1,o,COcoC)c,COc1C)c,7,replace o at position 3 with 1,flow_matching,0.3,2.0,58,133
62,add,2.0,1,,COc1C)c,CO1c1C)c,8,add 1 at position 2,flow_matching,0.3,2.0,58,133
63,add,3.0,B,,CO1c1C)c,CO1Bc1C)c,9,add B at position 3,flow_matching,0.3,2.0,58,133
64,add,1.0,B,,CO1Bc1C)c,CBO1Bc1C)c,10,add B at position 1,flow_matching,0.3,2.0,58,133
65,add,3.0,[,,CBO1Bc1C)c,CBO[1Bc1C)c,11,add [ at position 3,flow_matching,0.3,2.0,58,133
66,add,0.0,N,,CBO[1Bc1C)c,NCBO[1Bc1C)c,12,add N at position 0,flow_matching,0.3,2.0,58,133
67,replace,0.0,C,N,NCBO[1Bc1C)c,CCBO[1Bc1C)c,12,replace N at position 0 with C,flow_matching,0.3,2.0,58,133
68,replace,1.0,O,C,CCBO[1Bc1C)c,COBO[1Bc1C)c,12,replace C at position 1 with O,flow_matching,0.3,2.0,58,133
69,replace,2.0,c,B,COBO[1Bc1C)c,COcO[1Bc1C)c,12,replace B at position 2 with c,flow_matching,0.3,2.0,58,133
70,replace,10.0,],),COcO[1Bc1C)c,COcO[1Bc1C]c,12,replace ) at position 10 with ],flow_matching,0.3,2.0,58,133
71,replace,3.0,1,O,COcO[1Bc1C]c,COc1[1Bc1C]c,12,replace O at position 3 with 1,flow_matching,0.3,2.0,58,133
72,add,12.0,+,,COc1[1Bc1C]c,COc1[1Bc1C]c+,13,add + at position 12,flow_matching,0.3,2.0,58,133
73,add,9.0,c,,COc1[1Bc1C]c+,COc1[1Bc1cC]c+,14,add c at position 9,flow_matching,0.3,2.0,58,133
74,replace,4.0,c,[,COc1[1Bc1cC]c+,COc1c1Bc1cC]c+,14,replace [ at position 4 with c,flow_matching,0.3,2.0,58,133
75,replace,0.0,c,C,COc1c1Bc1cC]c+,cOc1c1Bc1cC]c+,14,replace C at position 0 with c,flow_matching,0.3,2.0,58,133
76,replace,7.0,I,c,cOc1c1Bc1cC]c+,cOc1c1BI1cC]c+,14,replace c at position 7 with I,flow_matching,0.3,2.0,58,133
77,add,11.0,],,cOc1c1BI1cC]c+,cOc1c1BI1cC]]c+,15,add ] at position 11,flow_matching,0.3,2.0,58,133
78,add,3.0,3,,cOc1c1BI1cC]]c+,cOc31c1BI1cC]]c+,16,add 3 at position 3,flow_matching,0.3,2.0,58,133
79,add,3.0,1,,cOc31c1BI1cC]]c+,cOc131c1BI1cC]]c+,17,add 1 at position 3,flow_matching,0.3,2.0,58,133
80,replace,0.0,C,c,cOc131c1BI1cC]]c+,COc131c1BI1cC]]c+,17,replace c at position 0 with C,flow_matching,0.3,2.0,58,133
81,replace,4.0,c,3,COc131c1BI1cC]]c+,COc1c1c1BI1cC]]c+,17,replace 3 at position 4 with c,flow_matching,0.3,2.0,58,133
82,replace,5.0,c,1,COc1c1c1BI1cC]]c+,COc1ccc1BI1cC]]c+,17,replace 1 at position 5 with c,flow_matching,0.3,2.0,58,133
83,replace,7.0,(,1,COc1ccc1BI1cC]]c+,COc1ccc(BI1cC]]c+,17,replace 1 at position 7 with (,flow_matching,0.3,2.0,58,133
84,replace,8.0,-,B,COc1ccc(BI1cC]]c+,COc1ccc(-I1cC]]c+,17,replace B at position 8 with -,flow_matching,0.3,2.0,58,133
85,replace,9.0,n,I,COc1ccc(-I1cC]]c+,COc1ccc(-n1cC]]c+,17,replace I at position 9 with n,flow_matching,0.3,2.0,58,133
86,replace,10.0,2,1,COc1ccc(-n1cC]]c+,COc1ccc(-n2cC]]c+,17,replace 1 at position 10 with 2,flow_matching,0.3,2.0,58,133
87,replace,11.0,n,c,COc1ccc(-n2cC]]c+,COc1ccc(-n2nC]]c+,17,replace c at position 11 with n,flow_matching,0.3,2.0,58,133
88,replace,12.0,c,C,COc1ccc(-n2nC]]c+,COc1ccc(-n2nc]]c+,17,replace C at position 12 with c,flow_matching,0.3,2.0,58,133
89,replace,13.0,(,],COc1ccc(-n2nc]]c+,COc1ccc(-n2nc(]c+,17,replace ] at position 13 with (,flow_matching,0.3,2.0,58,133
90,replace,14.0,C,],COc1ccc(-n2nc(]c+,COc1ccc(-n2nc(Cc+,17,replace ] at position 14 with C,flow_matching,0.3,2.0,58,133
91,replace,15.0,),c,COc1ccc(-n2nc(Cc+,COc1ccc(-n2nc(C)+,17,replace c at position 15 with ),flow_matching,0.3,2.0,58,133
92,replace,16.0,c,+,COc1ccc(-n2nc(C)+,COc1ccc(-n2nc(C)c,17,replace + at position 16 with c,flow_matching,0.3,2.0,58,133
93,add,17.0,3,,COc1ccc(-n2nc(C)c,COc1ccc(-n2nc(C)c3,18,add 3 at position 17,flow_matching,0.3,2.0,58,133
94,add,18.0,c,,COc1ccc(-n2nc(C)c3,COc1ccc(-n2nc(C)c3c,19,add c at position 18,flow_matching,0.3,2.0,58,133
95,add,19.0,2,,COc1ccc(-n2nc(C)c3c,COc1ccc(-n2nc(C)c3c2,20,add 2 at position 19,flow_matching,0.3,2.0,58,133
96,add,20.0,C,,COc1ccc(-n2nc(C)c3c2,COc1ccc(-n2nc(C)c3c2C,21,add C at position 20,flow_matching,0.3,2.0,58,133
97,add,21.0,[,,COc1ccc(-n2nc(C)c3c2C,COc1ccc(-n2nc(C)c3c2C[,22,add [ at position 21,flow_matching,0.3,2.0,58,133
98,add,22.0,C,,COc1ccc(-n2nc(C)c3c2C[,COc1ccc(-n2nc(C)c3c2C[C,23,add C at position 22,flow_matching,0.3,2.0,58,133
99,add,23.0,@,,COc1ccc(-n2nc(C)c3c2C[C,COc1ccc(-n2nc(C)c3c2C[C@,24,add @ at position 23,flow_matching,0.3,2.0,58,133
100,add,24.0,H,,COc1ccc(-n2nc(C)c3c2C[C@,COc1ccc(-n2nc(C)c3c2C[C@H,25,add H at position 24,flow_matching,0.3,2.0,58,133
101,add,25.0,],,COc1ccc(-n2nc(C)c3c2C[C@H,COc1ccc(-n2nc(C)c3c2C[C@H],26,add ] at position 25,flow_matching,0.3,2.0,58,133
102,add,26.0,(,,COc1ccc(-n2nc(C)c3c2C[C@H],COc1ccc(-n2nc(C)c3c2C[C@H](,27,add ( at position 26,flow_matching,0.3,2.0,58,133
103,add,27.0,c,,COc1ccc(-n2nc(C)c3c2C[C@H](,COc1ccc(-n2nc(C)c3c2C[C@H](c,28,add c at position 27,flow_matching,0.3,2.0,58,133
104,add,28.0,2,,COc1ccc(-n2nc(C)c3c2C[C@H](c,COc1ccc(-n2nc(C)c3c2C[C@H](c2,29,add 2 at position 28,flow_matching,0.3,2.0,58,133
105,add,29.0,c,,COc1ccc(-n2nc(C)c3c2C[C@H](c2,COc1ccc(-n2nc(C)c3c2C[C@H](c2c,30,add c at position 29,flow_matching,0.3,2.0,58,133
106,add,30.0,c,,COc1ccc(-n2nc(C)c3c2C[C@H](c2c,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc,31,add c at position 30,flow_matching,0.3,2.0,58,133
107,add,31.0,(,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(,32,add ( at position 31,flow_matching,0.3,2.0,58,133
108,add,32.0,O,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(O,33,add O at position 32,flow_matching,0.3,2.0,58,133
109,add,33.0,C,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(O,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC,34,add C at position 33,flow_matching,0.3,2.0,58,133
110,add,34.0,),,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC),35,add ) at position 34,flow_matching,0.3,2.0,58,133
111,add,35.0,c,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC),COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c,36,add c at position 35,flow_matching,0.3,2.0,58,133
112,add,36.0,(,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(,37,add ( at position 36,flow_matching,0.3,2.0,58,133
113,add,37.0,O,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(O,38,add O at position 37,flow_matching,0.3,2.0,58,133
114,add,38.0,C,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(O,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC,39,add C at position 38,flow_matching,0.3,2.0,58,133
115,add,39.0,),,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC),40,add ) at position 39,flow_matching,0.3,2.0,58,133
116,add,40.0,c,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC),COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c,41,add c at position 40,flow_matching,0.3,2.0,58,133
117,add,41.0,(,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(,42,add ( at position 41,flow_matching,0.3,2.0,58,133
118,add,42.0,O,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(O,43,add O at position 42,flow_matching,0.3,2.0,58,133
119,add,43.0,C,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(O,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC,44,add C at position 43,flow_matching,0.3,2.0,58,133
120,add,44.0,),,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC),45,add ) at position 44,flow_matching,0.3,2.0,58,133
121,add,45.0,c,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC),COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c,46,add c at position 45,flow_matching,0.3,2.0,58,133
122,add,46.0,2,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2,47,add 2 at position 46,flow_matching,0.3,2.0,58,133
123,add,47.0,),,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2),48,add ) at position 47,flow_matching,0.3,2.0,58,133
124,add,48.0,C,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2),COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)C,49,add C at position 48,flow_matching,0.3,2.0,58,133
125,add,49.0,C,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)C,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC,50,add C at position 49,flow_matching,0.3,2.0,58,133
126,add,50.0,3,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3,51,add 3 at position 50,flow_matching,0.3,2.0,58,133
127,add,51.0,=,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=,52,add = at position 51,flow_matching,0.3,2.0,58,133
128,add,52.0,O,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O,53,add O at position 52,flow_matching,0.3,2.0,58,133
129,add,53.0,),,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O),54,add ) at position 53,flow_matching,0.3,2.0,58,133
130,add,54.0,c,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O),COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O)c,55,add c at position 54,flow_matching,0.3,2.0,58,133
131,add,55.0,c,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O)c,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O)cc,56,add c at position 55,flow_matching,0.3,2.0,58,133
132,add,56.0,1,,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O)cc,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O)cc1,57,add 1 at position 56,flow_matching,0.3,2.0,58,133
133,add,57.0,"
",,COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O)cc1,"COc1ccc(-n2nc(C)c3c2C[C@H](c2cc(OC)c(OC)c(OC)c2)CC3=O)cc1
",58,"add 
 at position 57",flow_matching,0.3,2.0,58,133
