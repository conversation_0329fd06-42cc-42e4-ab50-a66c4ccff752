step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,53,237
1,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,53,237
2,add,0.0,s,,B,sB,2,add s at position 0,flow_matching,0.3,2.0,53,237
3,remove,0.0,s,,sB,B,1,remove s from position 0,flow_matching,0.3,2.0,53,237
4,replace,0.0,3,B,B,3,1,replace <PERSON> at position 0 with 3,flow_matching,0.3,2.0,53,237
5,add,0.0,s,,3,s3,2,add s at position 0,flow_matching,0.3,2.0,53,237
6,replace,0.0,<PERSON>,s,s3,I3,2,replace s at position 0 with I,flow_matching,0.3,2.0,53,237
7,replace,0.0,C,<PERSON>,I3,C3,2,replace I at position 0 with C,flow_matching,0.3,2.0,53,237
8,replace,1.0,c,3,C3,Cc,2,replace 3 at position 1 with c,flow_matching,0.3,2.0,53,237
9,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,53,237
10,add,3.0,n,,Cc1,Cc1n,4,add n at position 3,flow_matching,0.3,2.0,53,237
11,replace,2.0,-,1,Cc1n,Cc-n,4,replace 1 at position 2 with -,flow_matching,0.3,2.0,53,237
12,add,2.0,N,,Cc-n,CcN-n,5,add N at position 2,flow_matching,0.3,2.0,53,237
13,replace,4.0,],n,CcN-n,CcN-],5,replace n at position 4 with ],flow_matching,0.3,2.0,53,237
14,replace,2.0,1,N,CcN-],Cc1-],5,replace N at position 2 with 1,flow_matching,0.3,2.0,53,237
15,remove,4.0,],,Cc1-],Cc1-,4,remove ] from position 4,flow_matching,0.3,2.0,53,237
16,replace,3.0,n,-,Cc1-,Cc1n,4,replace - at position 3 with n,flow_matching,0.3,2.0,53,237
17,remove,2.0,1,,Cc1n,Ccn,3,remove 1 from position 2,flow_matching,0.3,2.0,53,237
18,replace,2.0,1,n,Ccn,Cc1,3,replace n at position 2 with 1,flow_matching,0.3,2.0,53,237
19,add,3.0,n,,Cc1,Cc1n,4,add n at position 3,flow_matching,0.3,2.0,53,237
20,replace,2.0,[,1,Cc1n,Cc[n,4,replace 1 at position 2 with [,flow_matching,0.3,2.0,53,237
21,remove,3.0,n,,Cc[n,Cc[,3,remove n from position 3,flow_matching,0.3,2.0,53,237
22,add,2.0,s,,Cc[,Ccs[,4,add s at position 2,flow_matching,0.3,2.0,53,237
23,add,0.0,F,,Ccs[,FCcs[,5,add F at position 0,flow_matching,0.3,2.0,53,237
24,remove,3.0,s,,FCcs[,FCc[,4,remove s from position 3,flow_matching,0.3,2.0,53,237
25,replace,0.0,C,F,FCc[,CCc[,4,replace F at position 0 with C,flow_matching,0.3,2.0,53,237
26,add,2.0,#,,CCc[,CC#c[,5,add # at position 2,flow_matching,0.3,2.0,53,237
27,add,3.0,S,,CC#c[,CC#Sc[,6,add S at position 3,flow_matching,0.3,2.0,53,237
28,replace,1.0,c,C,CC#Sc[,Cc#Sc[,6,replace C at position 1 with c,flow_matching,0.3,2.0,53,237
29,remove,3.0,S,,Cc#Sc[,Cc#c[,5,remove S from position 3,flow_matching,0.3,2.0,53,237
30,replace,3.0,s,c,Cc#c[,Cc#s[,5,replace c at position 3 with s,flow_matching,0.3,2.0,53,237
31,add,4.0,o,,Cc#s[,Cc#so[,6,add o at position 4,flow_matching,0.3,2.0,53,237
32,replace,2.0,1,#,Cc#so[,Cc1so[,6,replace # at position 2 with 1,flow_matching,0.3,2.0,53,237
33,replace,5.0,r,[,Cc1so[,Cc1sor,6,replace [ at position 5 with r,flow_matching,0.3,2.0,53,237
34,remove,4.0,o,,Cc1sor,Cc1sr,5,remove o from position 4,flow_matching,0.3,2.0,53,237
35,replace,3.0,n,s,Cc1sr,Cc1nr,5,replace s at position 3 with n,flow_matching,0.3,2.0,53,237
36,add,5.0,6,,Cc1nr,Cc1nr6,6,add 6 at position 5,flow_matching,0.3,2.0,53,237
37,remove,5.0,6,,Cc1nr6,Cc1nr,5,remove 6 from position 5,flow_matching,0.3,2.0,53,237
38,replace,0.0,2,C,Cc1nr,2c1nr,5,replace C at position 0 with 2,flow_matching,0.3,2.0,53,237
39,add,5.0,5,,2c1nr,2c1nr5,6,add 5 at position 5,flow_matching,0.3,2.0,53,237
40,add,3.0,F,,2c1nr5,2c1Fnr5,7,add F at position 3,flow_matching,0.3,2.0,53,237
41,replace,3.0,c,F,2c1Fnr5,2c1cnr5,7,replace F at position 3 with c,flow_matching,0.3,2.0,53,237
42,replace,0.0,C,2,2c1cnr5,Cc1cnr5,7,replace 2 at position 0 with C,flow_matching,0.3,2.0,53,237
43,replace,3.0,n,c,Cc1cnr5,Cc1nnr5,7,replace c at position 3 with n,flow_matching,0.3,2.0,53,237
44,replace,5.0,(,r,Cc1nnr5,Cc1nn(5,7,replace r at position 5 with (,flow_matching,0.3,2.0,53,237
45,add,0.0,O,,Cc1nn(5,OCc1nn(5,8,add O at position 0,flow_matching,0.3,2.0,53,237
46,remove,5.0,n,,OCc1nn(5,OCc1n(5,7,remove n from position 5,flow_matching,0.3,2.0,53,237
47,replace,1.0,[,C,OCc1n(5,O[c1n(5,7,replace C at position 1 with [,flow_matching,0.3,2.0,53,237
48,add,5.0,S,,O[c1n(5,O[c1nS(5,8,add S at position 5,flow_matching,0.3,2.0,53,237
49,add,5.0,1,,O[c1nS(5,O[c1n1S(5,9,add 1 at position 5,flow_matching,0.3,2.0,53,237
50,replace,0.0,C,O,O[c1n1S(5,C[c1n1S(5,9,replace O at position 0 with C,flow_matching,0.3,2.0,53,237
51,replace,1.0,c,[,C[c1n1S(5,Ccc1n1S(5,9,replace [ at position 1 with c,flow_matching,0.3,2.0,53,237
52,replace,2.0,1,c,Ccc1n1S(5,Cc11n1S(5,9,replace c at position 2 with 1,flow_matching,0.3,2.0,53,237
53,remove,3.0,1,,Cc11n1S(5,Cc1n1S(5,8,remove 1 from position 3,flow_matching,0.3,2.0,53,237
54,add,1.0,/,,Cc1n1S(5,C/c1n1S(5,9,add / at position 1,flow_matching,0.3,2.0,53,237
55,add,4.0,s,,C/c1n1S(5,C/c1sn1S(5,10,add s at position 4,flow_matching,0.3,2.0,53,237
56,replace,9.0,r,5,C/c1sn1S(5,C/c1sn1S(r,10,replace 5 at position 9 with r,flow_matching,0.3,2.0,53,237
57,add,7.0,r,,C/c1sn1S(r,C/c1sn1rS(r,11,add r at position 7,flow_matching,0.3,2.0,53,237
58,add,7.0,2,,C/c1sn1rS(r,C/c1sn12rS(r,12,add 2 at position 7,flow_matching,0.3,2.0,53,237
59,remove,7.0,2,,C/c1sn12rS(r,C/c1sn1rS(r,11,remove 2 from position 7,flow_matching,0.3,2.0,53,237
60,replace,1.0,c,/,C/c1sn1rS(r,Ccc1sn1rS(r,11,replace / at position 1 with c,flow_matching,0.3,2.0,53,237
61,remove,2.0,c,,Ccc1sn1rS(r,Cc1sn1rS(r,10,remove c from position 2,flow_matching,0.3,2.0,53,237
62,replace,3.0,n,s,Cc1sn1rS(r,Cc1nn1rS(r,10,replace s at position 3 with n,flow_matching,0.3,2.0,53,237
63,remove,6.0,r,,Cc1nn1rS(r,Cc1nn1S(r,9,remove r from position 6,flow_matching,0.3,2.0,53,237
64,replace,7.0,6,(,Cc1nn1S(r,Cc1nn1S6r,9,replace ( at position 7 with 6,flow_matching,0.3,2.0,53,237
65,replace,3.0,l,n,Cc1nn1S6r,Cc1ln1S6r,9,replace n at position 3 with l,flow_matching,0.3,2.0,53,237
66,remove,5.0,1,,Cc1ln1S6r,Cc1lnS6r,8,remove 1 from position 5,flow_matching,0.3,2.0,53,237
67,replace,3.0,n,l,Cc1lnS6r,Cc1nnS6r,8,replace l at position 3 with n,flow_matching,0.3,2.0,53,237
68,replace,5.0,(,S,Cc1nnS6r,Cc1nn(6r,8,replace S at position 5 with (,flow_matching,0.3,2.0,53,237
69,replace,4.0,7,n,Cc1nn(6r,Cc1n7(6r,8,replace n at position 4 with 7,flow_matching,0.3,2.0,53,237
70,replace,5.0,7,(,Cc1n7(6r,Cc1n776r,8,replace ( at position 5 with 7,flow_matching,0.3,2.0,53,237
71,add,4.0,C,,Cc1n776r,Cc1nC776r,9,add C at position 4,flow_matching,0.3,2.0,53,237
72,add,2.0,[,,Cc1nC776r,Cc[1nC776r,10,add [ at position 2,flow_matching,0.3,2.0,53,237
73,add,2.0,[,,Cc[1nC776r,Cc[[1nC776r,11,add [ at position 2,flow_matching,0.3,2.0,53,237
74,replace,2.0,1,[,Cc[[1nC776r,Cc1[1nC776r,11,replace [ at position 2 with 1,flow_matching,0.3,2.0,53,237
75,replace,3.0,n,[,Cc1[1nC776r,Cc1n1nC776r,11,replace [ at position 3 with n,flow_matching,0.3,2.0,53,237
76,add,9.0,r,,Cc1n1nC776r,Cc1n1nC77r6r,12,add r at position 9,flow_matching,0.3,2.0,53,237
77,add,3.0,c,,Cc1n1nC77r6r,Cc1cn1nC77r6r,13,add c at position 3,flow_matching,0.3,2.0,53,237
78,remove,5.0,1,,Cc1cn1nC77r6r,Cc1cnnC77r6r,12,remove 1 from position 5,flow_matching,0.3,2.0,53,237
79,replace,0.0,s,C,Cc1cnnC77r6r,sc1cnnC77r6r,12,replace C at position 0 with s,flow_matching,0.3,2.0,53,237
80,add,5.0,o,,sc1cnnC77r6r,sc1cnonC77r6r,13,add o at position 5,flow_matching,0.3,2.0,53,237
81,remove,9.0,7,,sc1cnonC77r6r,sc1cnonC7r6r,12,remove 7 from position 9,flow_matching,0.3,2.0,53,237
82,remove,5.0,o,,sc1cnonC7r6r,sc1cnnC7r6r,11,remove o from position 5,flow_matching,0.3,2.0,53,237
83,add,3.0,N,,sc1cnnC7r6r,sc1NcnnC7r6r,12,add N at position 3,flow_matching,0.3,2.0,53,237
84,remove,3.0,N,,sc1NcnnC7r6r,sc1cnnC7r6r,11,remove N from position 3,flow_matching,0.3,2.0,53,237
85,replace,0.0,C,s,sc1cnnC7r6r,Cc1cnnC7r6r,11,replace s at position 0 with C,flow_matching,0.3,2.0,53,237
86,replace,3.0,n,c,Cc1cnnC7r6r,Cc1nnnC7r6r,11,replace c at position 3 with n,flow_matching,0.3,2.0,53,237
87,replace,5.0,(,n,Cc1nnnC7r6r,Cc1nn(C7r6r,11,replace n at position 5 with (,flow_matching,0.3,2.0,53,237
88,replace,4.0,=,n,Cc1nn(C7r6r,Cc1n=(C7r6r,11,replace n at position 4 with =,flow_matching,0.3,2.0,53,237
89,replace,6.0,4,C,Cc1n=(C7r6r,Cc1n=(47r6r,11,replace C at position 6 with 4,flow_matching,0.3,2.0,53,237
90,add,10.0,r,,Cc1n=(47r6r,Cc1n=(47r6rr,12,add r at position 10,flow_matching,0.3,2.0,53,237
91,add,2.0,/,,Cc1n=(47r6rr,Cc/1n=(47r6rr,13,add / at position 2,flow_matching,0.3,2.0,53,237
92,replace,2.0,I,/,Cc/1n=(47r6rr,CcI1n=(47r6rr,13,replace / at position 2 with I,flow_matching,0.3,2.0,53,237
93,add,5.0,7,,CcI1n=(47r6rr,CcI1n7=(47r6rr,14,add 7 at position 5,flow_matching,0.3,2.0,53,237
94,add,14.0,7,,CcI1n7=(47r6rr,CcI1n7=(47r6rr7,15,add 7 at position 14,flow_matching,0.3,2.0,53,237
95,remove,7.0,(,,CcI1n7=(47r6rr7,CcI1n7=47r6rr7,14,remove ( from position 7,flow_matching,0.3,2.0,53,237
96,replace,2.0,1,I,CcI1n7=47r6rr7,Cc11n7=47r6rr7,14,replace I at position 2 with 1,flow_matching,0.3,2.0,53,237
97,replace,3.0,n,1,Cc11n7=47r6rr7,Cc1nn7=47r6rr7,14,replace 1 at position 3 with n,flow_matching,0.3,2.0,53,237
98,remove,3.0,n,,Cc1nn7=47r6rr7,Cc1n7=47r6rr7,13,remove n from position 3,flow_matching,0.3,2.0,53,237
99,remove,12.0,7,,Cc1n7=47r6rr7,Cc1n7=47r6rr,12,remove 7 from position 12,flow_matching,0.3,2.0,53,237
100,add,10.0,5,,Cc1n7=47r6rr,Cc1n7=47r65rr,13,add 5 at position 10,flow_matching,0.3,2.0,53,237
101,replace,0.0,(,C,Cc1n7=47r65rr,(c1n7=47r65rr,13,replace C at position 0 with (,flow_matching,0.3,2.0,53,237
102,replace,0.0,C,(,(c1n7=47r65rr,Cc1n7=47r65rr,13,replace ( at position 0 with C,flow_matching,0.3,2.0,53,237
103,add,5.0,),,Cc1n7=47r65rr,Cc1n7)=47r65rr,14,add ) at position 5,flow_matching,0.3,2.0,53,237
104,remove,11.0,5,,Cc1n7)=47r65rr,Cc1n7)=47r6rr,13,remove 5 from position 11,flow_matching,0.3,2.0,53,237
105,replace,4.0,n,7,Cc1n7)=47r6rr,Cc1nn)=47r6rr,13,replace 7 at position 4 with n,flow_matching,0.3,2.0,53,237
106,replace,5.0,(,),Cc1nn)=47r6rr,Cc1nn(=47r6rr,13,replace ) at position 5 with (,flow_matching,0.3,2.0,53,237
107,replace,6.0,C,=,Cc1nn(=47r6rr,Cc1nn(C47r6rr,13,replace = at position 6 with C,flow_matching,0.3,2.0,53,237
108,replace,7.0,),4,Cc1nn(C47r6rr,Cc1nn(C)7r6rr,13,replace 4 at position 7 with ),flow_matching,0.3,2.0,53,237
109,replace,4.0,o,n,Cc1nn(C)7r6rr,Cc1no(C)7r6rr,13,replace n at position 4 with o,flow_matching,0.3,2.0,53,237
110,add,11.0,H,,Cc1no(C)7r6rr,Cc1no(C)7r6Hrr,14,add H at position 11,flow_matching,0.3,2.0,53,237
111,replace,2.0,/,1,Cc1no(C)7r6Hrr,Cc/no(C)7r6Hrr,14,replace 1 at position 2 with /,flow_matching,0.3,2.0,53,237
112,add,6.0,4,,Cc/no(C)7r6Hrr,Cc/no(4C)7r6Hrr,15,add 4 at position 6,flow_matching,0.3,2.0,53,237
113,add,11.0,o,,Cc/no(4C)7r6Hrr,Cc/no(4C)7ro6Hrr,16,add o at position 11,flow_matching,0.3,2.0,53,237
114,add,16.0,r,,Cc/no(4C)7ro6Hrr,Cc/no(4C)7ro6Hrrr,17,add r at position 16,flow_matching,0.3,2.0,53,237
115,replace,7.0,o,C,Cc/no(4C)7ro6Hrrr,Cc/no(4o)7ro6Hrrr,17,replace C at position 7 with o,flow_matching,0.3,2.0,53,237
116,add,3.0,I,,Cc/no(4o)7ro6Hrrr,Cc/Ino(4o)7ro6Hrrr,18,add I at position 3,flow_matching,0.3,2.0,53,237
117,remove,6.0,(,,Cc/Ino(4o)7ro6Hrrr,Cc/Ino4o)7ro6Hrrr,17,remove ( from position 6,flow_matching,0.3,2.0,53,237
118,replace,2.0,1,/,Cc/Ino4o)7ro6Hrrr,Cc1Ino4o)7ro6Hrrr,17,replace / at position 2 with 1,flow_matching,0.3,2.0,53,237
119,replace,3.0,n,I,Cc1Ino4o)7ro6Hrrr,Cc1nno4o)7ro6Hrrr,17,replace I at position 3 with n,flow_matching,0.3,2.0,53,237
120,replace,7.0,N,o,Cc1nno4o)7ro6Hrrr,Cc1nno4N)7ro6Hrrr,17,replace o at position 7 with N,flow_matching,0.3,2.0,53,237
121,replace,5.0,(,o,Cc1nno4N)7ro6Hrrr,Cc1nn(4N)7ro6Hrrr,17,replace o at position 5 with (,flow_matching,0.3,2.0,53,237
122,add,7.0,1,,Cc1nn(4N)7ro6Hrrr,Cc1nn(41N)7ro6Hrrr,18,add 1 at position 7,flow_matching,0.3,2.0,53,237
123,add,17.0,O,,Cc1nn(41N)7ro6Hrrr,Cc1nn(41N)7ro6HrrOr,19,add O at position 17,flow_matching,0.3,2.0,53,237
124,replace,5.0,H,(,Cc1nn(41N)7ro6HrrOr,Cc1nnH41N)7ro6HrrOr,19,replace ( at position 5 with H,flow_matching,0.3,2.0,53,237
125,replace,5.0,(,H,Cc1nnH41N)7ro6HrrOr,Cc1nn(41N)7ro6HrrOr,19,replace H at position 5 with (,flow_matching,0.3,2.0,53,237
126,replace,16.0,n,r,Cc1nn(41N)7ro6HrrOr,Cc1nn(41N)7ro6HrnOr,19,replace r at position 16 with n,flow_matching,0.3,2.0,53,237
127,add,12.0,=,,Cc1nn(41N)7ro6HrnOr,Cc1nn(41N)7r=o6HrnOr,20,add = at position 12,flow_matching,0.3,2.0,53,237
128,replace,6.0,C,4,Cc1nn(41N)7r=o6HrnOr,Cc1nn(C1N)7r=o6HrnOr,20,replace 4 at position 6 with C,flow_matching,0.3,2.0,53,237
129,replace,7.0,),1,Cc1nn(C1N)7r=o6HrnOr,Cc1nn(C)N)7r=o6HrnOr,20,replace 1 at position 7 with ),flow_matching,0.3,2.0,53,237
130,remove,6.0,C,,Cc1nn(C)N)7r=o6HrnOr,Cc1nn()N)7r=o6HrnOr,19,remove C from position 6,flow_matching,0.3,2.0,53,237
131,replace,6.0,C,),Cc1nn()N)7r=o6HrnOr,Cc1nn(CN)7r=o6HrnOr,19,replace ) at position 6 with C,flow_matching,0.3,2.0,53,237
132,replace,7.0,),N,Cc1nn(CN)7r=o6HrnOr,Cc1nn(C))7r=o6HrnOr,19,replace N at position 7 with ),flow_matching,0.3,2.0,53,237
133,add,14.0,-,,Cc1nn(C))7r=o6HrnOr,Cc1nn(C))7r=o6-HrnOr,20,add - at position 14,flow_matching,0.3,2.0,53,237
134,replace,8.0,c,),Cc1nn(C))7r=o6-HrnOr,Cc1nn(C)c7r=o6-HrnOr,20,replace ) at position 8 with c,flow_matching,0.3,2.0,53,237
135,add,5.0,l,,Cc1nn(C)c7r=o6-HrnOr,Cc1nnl(C)c7r=o6-HrnOr,21,add l at position 5,flow_matching,0.3,2.0,53,237
136,add,6.0,(,,Cc1nnl(C)c7r=o6-HrnOr,Cc1nnl((C)c7r=o6-HrnOr,22,add ( at position 6,flow_matching,0.3,2.0,53,237
137,replace,5.0,(,l,Cc1nnl((C)c7r=o6-HrnOr,Cc1nn(((C)c7r=o6-HrnOr,22,replace l at position 5 with (,flow_matching,0.3,2.0,53,237
138,replace,6.0,C,(,Cc1nn(((C)c7r=o6-HrnOr,Cc1nn(C(C)c7r=o6-HrnOr,22,replace ( at position 6 with C,flow_matching,0.3,2.0,53,237
139,add,7.0,-,,Cc1nn(C(C)c7r=o6-HrnOr,Cc1nn(C-(C)c7r=o6-HrnOr,23,add - at position 7,flow_matching,0.3,2.0,53,237
140,replace,7.0,),-,Cc1nn(C-(C)c7r=o6-HrnOr,Cc1nn(C)(C)c7r=o6-HrnOr,23,replace - at position 7 with ),flow_matching,0.3,2.0,53,237
141,remove,4.0,n,,Cc1nn(C)(C)c7r=o6-HrnOr,Cc1n(C)(C)c7r=o6-HrnOr,22,remove n from position 4,flow_matching,0.3,2.0,53,237
142,replace,4.0,n,(,Cc1n(C)(C)c7r=o6-HrnOr,Cc1nnC)(C)c7r=o6-HrnOr,22,replace ( at position 4 with n,flow_matching,0.3,2.0,53,237
143,replace,3.0,H,n,Cc1nnC)(C)c7r=o6-HrnOr,Cc1HnC)(C)c7r=o6-HrnOr,22,replace n at position 3 with H,flow_matching,0.3,2.0,53,237
144,remove,4.0,n,,Cc1HnC)(C)c7r=o6-HrnOr,Cc1HC)(C)c7r=o6-HrnOr,21,remove n from position 4,flow_matching,0.3,2.0,53,237
145,add,19.0,r,,Cc1HC)(C)c7r=o6-HrnOr,Cc1HC)(C)c7r=o6-HrnrOr,22,add r at position 19,flow_matching,0.3,2.0,53,237
146,replace,3.0,n,H,Cc1HC)(C)c7r=o6-HrnrOr,Cc1nC)(C)c7r=o6-HrnrOr,22,replace H at position 3 with n,flow_matching,0.3,2.0,53,237
147,remove,14.0,6,,Cc1nC)(C)c7r=o6-HrnrOr,Cc1nC)(C)c7r=o-HrnrOr,21,remove 6 from position 14,flow_matching,0.3,2.0,53,237
148,add,19.0,l,,Cc1nC)(C)c7r=o-HrnrOr,Cc1nC)(C)c7r=o-HrnrlOr,22,add l at position 19,flow_matching,0.3,2.0,53,237
149,replace,21.0,-,r,Cc1nC)(C)c7r=o-HrnrlOr,Cc1nC)(C)c7r=o-HrnrlO-,22,replace r at position 21 with -,flow_matching,0.3,2.0,53,237
150,replace,4.0,n,C,Cc1nC)(C)c7r=o-HrnrlO-,Cc1nn)(C)c7r=o-HrnrlO-,22,replace C at position 4 with n,flow_matching,0.3,2.0,53,237
151,add,21.0,\,,Cc1nn)(C)c7r=o-HrnrlO-,Cc1nn)(C)c7r=o-HrnrlO\-,23,add \ at position 21,flow_matching,0.3,2.0,53,237
152,replace,5.0,(,),Cc1nn)(C)c7r=o-HrnrlO\-,Cc1nn((C)c7r=o-HrnrlO\-,23,replace ) at position 5 with (,flow_matching,0.3,2.0,53,237
153,add,17.0,7,,Cc1nn((C)c7r=o-HrnrlO\-,Cc1nn((C)c7r=o-Hr7nrlO\-,24,add 7 at position 17,flow_matching,0.3,2.0,53,237
154,remove,5.0,(,,Cc1nn((C)c7r=o-Hr7nrlO\-,Cc1nn(C)c7r=o-Hr7nrlO\-,23,remove ( from position 5,flow_matching,0.3,2.0,53,237
155,remove,12.0,o,,Cc1nn(C)c7r=o-Hr7nrlO\-,Cc1nn(C)c7r=-Hr7nrlO\-,22,remove o from position 12,flow_matching,0.3,2.0,53,237
156,replace,9.0,c,7,Cc1nn(C)c7r=-Hr7nrlO\-,Cc1nn(C)ccr=-Hr7nrlO\-,22,replace 7 at position 9 with c,flow_matching,0.3,2.0,53,237
157,replace,10.0,1,r,Cc1nn(C)ccr=-Hr7nrlO\-,Cc1nn(C)cc1=-Hr7nrlO\-,22,replace r at position 10 with 1,flow_matching,0.3,2.0,53,237
158,remove,15.0,7,,Cc1nn(C)cc1=-Hr7nrlO\-,Cc1nn(C)cc1=-HrnrlO\-,21,remove 7 from position 15,flow_matching,0.3,2.0,53,237
159,replace,11.0,[,=,Cc1nn(C)cc1=-HrnrlO\-,Cc1nn(C)cc1[-HrnrlO\-,21,replace = at position 11 with [,flow_matching,0.3,2.0,53,237
160,replace,5.0,I,(,Cc1nn(C)cc1[-HrnrlO\-,Cc1nnIC)cc1[-HrnrlO\-,21,replace ( at position 5 with I,flow_matching,0.3,2.0,53,237
161,replace,5.0,(,I,Cc1nnIC)cc1[-HrnrlO\-,Cc1nn(C)cc1[-HrnrlO\-,21,replace I at position 5 with (,flow_matching,0.3,2.0,53,237
162,remove,17.0,l,,Cc1nn(C)cc1[-HrnrlO\-,Cc1nn(C)cc1[-HrnrO\-,20,remove l from position 17,flow_matching,0.3,2.0,53,237
163,replace,12.0,C,-,Cc1nn(C)cc1[-HrnrO\-,Cc1nn(C)cc1[CHrnrO\-,20,replace - at position 12 with C,flow_matching,0.3,2.0,53,237
164,add,19.0,c,,Cc1nn(C)cc1[CHrnrO\-,Cc1nn(C)cc1[CHrnrO\c-,21,add c at position 19,flow_matching,0.3,2.0,53,237
165,add,1.0,N,,Cc1nn(C)cc1[CHrnrO\c-,CNc1nn(C)cc1[CHrnrO\c-,22,add N at position 1,flow_matching,0.3,2.0,53,237
166,replace,1.0,c,N,CNc1nn(C)cc1[CHrnrO\c-,Ccc1nn(C)cc1[CHrnrO\c-,22,replace N at position 1 with c,flow_matching,0.3,2.0,53,237
167,remove,7.0,C,,Ccc1nn(C)cc1[CHrnrO\c-,Ccc1nn()cc1[CHrnrO\c-,21,remove C from position 7,flow_matching,0.3,2.0,53,237
168,remove,2.0,c,,Ccc1nn()cc1[CHrnrO\c-,Cc1nn()cc1[CHrnrO\c-,20,remove c from position 2,flow_matching,0.3,2.0,53,237
169,replace,6.0,C,),Cc1nn()cc1[CHrnrO\c-,Cc1nn(Ccc1[CHrnrO\c-,20,replace ) at position 6 with C,flow_matching,0.3,2.0,53,237
170,replace,7.0,),c,Cc1nn(Ccc1[CHrnrO\c-,Cc1nn(C)c1[CHrnrO\c-,20,replace c at position 7 with ),flow_matching,0.3,2.0,53,237
171,remove,4.0,n,,Cc1nn(C)c1[CHrnrO\c-,Cc1n(C)c1[CHrnrO\c-,19,remove n from position 4,flow_matching,0.3,2.0,53,237
172,remove,16.0,\,,Cc1n(C)c1[CHrnrO\c-,Cc1n(C)c1[CHrnrOc-,18,remove \ from position 16,flow_matching,0.3,2.0,53,237
173,add,13.0,4,,Cc1n(C)c1[CHrnrOc-,Cc1n(C)c1[CHr4nrOc-,19,add 4 at position 13,flow_matching,0.3,2.0,53,237
174,remove,11.0,H,,Cc1n(C)c1[CHr4nrOc-,Cc1n(C)c1[Cr4nrOc-,18,remove H from position 11,flow_matching,0.3,2.0,53,237
175,replace,4.0,n,(,Cc1n(C)c1[Cr4nrOc-,Cc1nnC)c1[Cr4nrOc-,18,replace ( at position 4 with n,flow_matching,0.3,2.0,53,237
176,add,15.0,r,,Cc1nnC)c1[Cr4nrOc-,Cc1nnC)c1[Cr4nrrOc-,19,add r at position 15,flow_matching,0.3,2.0,53,237
177,replace,5.0,(,C,Cc1nnC)c1[Cr4nrrOc-,Cc1nn()c1[Cr4nrrOc-,19,replace C at position 5 with (,flow_matching,0.3,2.0,53,237
178,remove,0.0,C,,Cc1nn()c1[Cr4nrrOc-,c1nn()c1[Cr4nrrOc-,18,remove C from position 0,flow_matching,0.3,2.0,53,237
179,add,15.0,),,c1nn()c1[Cr4nrrOc-,c1nn()c1[Cr4nrr)Oc-,19,add ) at position 15,flow_matching,0.3,2.0,53,237
180,replace,0.0,C,c,c1nn()c1[Cr4nrr)Oc-,C1nn()c1[Cr4nrr)Oc-,19,replace c at position 0 with C,flow_matching,0.3,2.0,53,237
181,add,16.0,),,C1nn()c1[Cr4nrr)Oc-,C1nn()c1[Cr4nrr))Oc-,20,add ) at position 16,flow_matching,0.3,2.0,53,237
182,replace,1.0,c,1,C1nn()c1[Cr4nrr))Oc-,Ccnn()c1[Cr4nrr))Oc-,20,replace 1 at position 1 with c,flow_matching,0.3,2.0,53,237
183,replace,16.0,2,),Ccnn()c1[Cr4nrr))Oc-,Ccnn()c1[Cr4nrr)2Oc-,20,replace ) at position 16 with 2,flow_matching,0.3,2.0,53,237
184,remove,8.0,[,,Ccnn()c1[Cr4nrr)2Oc-,Ccnn()c1Cr4nrr)2Oc-,19,remove [ from position 8,flow_matching,0.3,2.0,53,237
185,add,9.0,o,,Ccnn()c1Cr4nrr)2Oc-,Ccnn()c1Cor4nrr)2Oc-,20,add o at position 9,flow_matching,0.3,2.0,53,237
186,replace,17.0,l,O,Ccnn()c1Cor4nrr)2Oc-,Ccnn()c1Cor4nrr)2lc-,20,replace O at position 17 with l,flow_matching,0.3,2.0,53,237
187,add,11.0,=,,Ccnn()c1Cor4nrr)2lc-,Ccnn()c1Cor=4nrr)2lc-,21,add = at position 11,flow_matching,0.3,2.0,53,237
188,replace,2.0,1,n,Ccnn()c1Cor=4nrr)2lc-,Cc1n()c1Cor=4nrr)2lc-,21,replace n at position 2 with 1,flow_matching,0.3,2.0,53,237
189,replace,4.0,n,(,Cc1n()c1Cor=4nrr)2lc-,Cc1nn)c1Cor=4nrr)2lc-,21,replace ( at position 4 with n,flow_matching,0.3,2.0,53,237
190,replace,5.0,(,),Cc1nn)c1Cor=4nrr)2lc-,Cc1nn(c1Cor=4nrr)2lc-,21,replace ) at position 5 with (,flow_matching,0.3,2.0,53,237
191,replace,6.0,C,c,Cc1nn(c1Cor=4nrr)2lc-,Cc1nn(C1Cor=4nrr)2lc-,21,replace c at position 6 with C,flow_matching,0.3,2.0,53,237
192,replace,7.0,),1,Cc1nn(C1Cor=4nrr)2lc-,Cc1nn(C)Cor=4nrr)2lc-,21,replace 1 at position 7 with ),flow_matching,0.3,2.0,53,237
193,replace,8.0,c,C,Cc1nn(C)Cor=4nrr)2lc-,Cc1nn(C)cor=4nrr)2lc-,21,replace C at position 8 with c,flow_matching,0.3,2.0,53,237
194,replace,9.0,c,o,Cc1nn(C)cor=4nrr)2lc-,Cc1nn(C)ccr=4nrr)2lc-,21,replace o at position 9 with c,flow_matching,0.3,2.0,53,237
195,replace,10.0,1,r,Cc1nn(C)ccr=4nrr)2lc-,Cc1nn(C)cc1=4nrr)2lc-,21,replace r at position 10 with 1,flow_matching,0.3,2.0,53,237
196,replace,11.0,[,=,Cc1nn(C)cc1=4nrr)2lc-,Cc1nn(C)cc1[4nrr)2lc-,21,replace = at position 11 with [,flow_matching,0.3,2.0,53,237
197,replace,12.0,C,4,Cc1nn(C)cc1[4nrr)2lc-,Cc1nn(C)cc1[Cnrr)2lc-,21,replace 4 at position 12 with C,flow_matching,0.3,2.0,53,237
198,replace,13.0,@,n,Cc1nn(C)cc1[Cnrr)2lc-,Cc1nn(C)cc1[C@rr)2lc-,21,replace n at position 13 with @,flow_matching,0.3,2.0,53,237
199,replace,14.0,@,r,Cc1nn(C)cc1[C@rr)2lc-,Cc1nn(C)cc1[C@@r)2lc-,21,replace r at position 14 with @,flow_matching,0.3,2.0,53,237
200,replace,15.0,H,r,Cc1nn(C)cc1[C@@r)2lc-,Cc1nn(C)cc1[C@@H)2lc-,21,replace r at position 15 with H,flow_matching,0.3,2.0,53,237
201,replace,16.0,],),Cc1nn(C)cc1[C@@H)2lc-,Cc1nn(C)cc1[C@@H]2lc-,21,replace ) at position 16 with ],flow_matching,0.3,2.0,53,237
202,replace,17.0,(,2,Cc1nn(C)cc1[C@@H]2lc-,Cc1nn(C)cc1[C@@H](lc-,21,replace 2 at position 17 with (,flow_matching,0.3,2.0,53,237
203,replace,18.0,C,l,Cc1nn(C)cc1[C@@H](lc-,Cc1nn(C)cc1[C@@H](Cc-,21,replace l at position 18 with C,flow_matching,0.3,2.0,53,237
204,replace,19.0,),c,Cc1nn(C)cc1[C@@H](Cc-,Cc1nn(C)cc1[C@@H](C)-,21,replace c at position 19 with ),flow_matching,0.3,2.0,53,237
205,replace,20.0,N,-,Cc1nn(C)cc1[C@@H](C)-,Cc1nn(C)cc1[C@@H](C)N,21,replace - at position 20 with N,flow_matching,0.3,2.0,53,237
206,add,21.0,C,,Cc1nn(C)cc1[C@@H](C)N,Cc1nn(C)cc1[C@@H](C)NC,22,add C at position 21,flow_matching,0.3,2.0,53,237
207,add,22.0,(,,Cc1nn(C)cc1[C@@H](C)NC,Cc1nn(C)cc1[C@@H](C)NC(,23,add ( at position 22,flow_matching,0.3,2.0,53,237
208,add,23.0,=,,Cc1nn(C)cc1[C@@H](C)NC(,Cc1nn(C)cc1[C@@H](C)NC(=,24,add = at position 23,flow_matching,0.3,2.0,53,237
209,add,24.0,O,,Cc1nn(C)cc1[C@@H](C)NC(=,Cc1nn(C)cc1[C@@H](C)NC(=O,25,add O at position 24,flow_matching,0.3,2.0,53,237
210,add,25.0,),,Cc1nn(C)cc1[C@@H](C)NC(=O,Cc1nn(C)cc1[C@@H](C)NC(=O),26,add ) at position 25,flow_matching,0.3,2.0,53,237
211,add,26.0,C,,Cc1nn(C)cc1[C@@H](C)NC(=O),Cc1nn(C)cc1[C@@H](C)NC(=O)C,27,add C at position 26,flow_matching,0.3,2.0,53,237
212,add,27.0,(,,Cc1nn(C)cc1[C@@H](C)NC(=O)C,Cc1nn(C)cc1[C@@H](C)NC(=O)C(,28,add ( at position 27,flow_matching,0.3,2.0,53,237
213,add,28.0,=,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=,29,add = at position 28,flow_matching,0.3,2.0,53,237
214,add,29.0,O,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O,30,add O at position 29,flow_matching,0.3,2.0,53,237
215,add,30.0,),,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O),31,add ) at position 30,flow_matching,0.3,2.0,53,237
216,add,31.0,N,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O),Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)N,32,add N at position 31,flow_matching,0.3,2.0,53,237
217,add,32.0,c,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)N,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc,33,add c at position 32,flow_matching,0.3,2.0,53,237
218,add,33.0,1,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1,34,add 1 at position 33,flow_matching,0.3,2.0,53,237
219,add,34.0,c,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1c,35,add c at position 34,flow_matching,0.3,2.0,53,237
220,add,35.0,c,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1c,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1cc,36,add c at position 35,flow_matching,0.3,2.0,53,237
221,add,36.0,c,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1cc,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc,37,add c at position 36,flow_matching,0.3,2.0,53,237
222,add,37.0,(,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(,38,add ( at position 37,flow_matching,0.3,2.0,53,237
223,add,38.0,O,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(O,39,add O at position 38,flow_matching,0.3,2.0,53,237
224,add,39.0,C,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(O,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OC,40,add C at position 39,flow_matching,0.3,2.0,53,237
225,add,40.0,C,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OC,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC,41,add C at position 40,flow_matching,0.3,2.0,53,237
226,add,41.0,2,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2,42,add 2 at position 41,flow_matching,0.3,2.0,53,237
227,add,42.0,C,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2C,43,add C at position 42,flow_matching,0.3,2.0,53,237
228,add,43.0,C,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2C,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CC,44,add C at position 43,flow_matching,0.3,2.0,53,237
229,add,44.0,C,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CC,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCC,45,add C at position 44,flow_matching,0.3,2.0,53,237
230,add,45.0,C,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCC,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCC,46,add C at position 45,flow_matching,0.3,2.0,53,237
231,add,46.0,C,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCC,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC,47,add C at position 46,flow_matching,0.3,2.0,53,237
232,add,47.0,2,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2,48,add 2 at position 47,flow_matching,0.3,2.0,53,237
233,add,48.0,),,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2),49,add ) at position 48,flow_matching,0.3,2.0,53,237
234,add,49.0,c,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2),Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2)c,50,add c at position 49,flow_matching,0.3,2.0,53,237
235,add,50.0,c,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2)c,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2)cc,51,add c at position 50,flow_matching,0.3,2.0,53,237
236,add,51.0,1,,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2)cc,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2)cc1,52,add 1 at position 51,flow_matching,0.3,2.0,53,237
237,add,52.0,"
",,Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2)cc1,"Cc1nn(C)cc1[C@@H](C)NC(=O)C(=O)Nc1ccc(OCC2CCCCC2)cc1
",53,"add 
 at position 52",flow_matching,0.3,2.0,53,237
