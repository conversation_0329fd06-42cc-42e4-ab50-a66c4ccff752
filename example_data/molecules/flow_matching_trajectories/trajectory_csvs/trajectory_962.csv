step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,189
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,49,189
2,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,49,189
3,add,0.0,l,,,l,1,add l at position 0,flow_matching,0.3,2.0,49,189
4,add,0.0,F,,l,Fl,2,add F at position 0,flow_matching,0.3,2.0,49,189
5,remove,1.0,l,,Fl,F,1,remove l from position 1,flow_matching,0.3,2.0,49,189
6,replace,0.0,C,F,F,C,1,replace F at position 0 with C,flow_matching,0.3,2.0,49,189
7,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,49,189
8,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,49,189
9,add,0.0,3,,@,3@,2,add 3 at position 0,flow_matching,0.3,2.0,49,189
10,replace,0.0,C,3,3@,C@,2,replace 3 at position 0 with C,flow_matching,0.3,2.0,49,189
11,replace,1.0,6,@,C@,C6,2,replace @ at position 1 with 6,flow_matching,0.3,2.0,49,189
12,add,1.0,=,,C6,C=6,3,add = at position 1,flow_matching,0.3,2.0,49,189
13,add,0.0,),,C=6,)C=6,4,add ) at position 0,flow_matching,0.3,2.0,49,189
14,replace,0.0,C,),)C=6,CC=6,4,replace ) at position 0 with C,flow_matching,0.3,2.0,49,189
15,replace,1.0,[,C,CC=6,C[=6,4,replace C at position 1 with [,flow_matching,0.3,2.0,49,189
16,add,4.0,I,,C[=6,C[=6I,5,add I at position 4,flow_matching,0.3,2.0,49,189
17,add,2.0,F,,C[=6I,C[F=6I,6,add F at position 2,flow_matching,0.3,2.0,49,189
18,replace,2.0,C,F,C[F=6I,C[C=6I,6,replace F at position 2 with C,flow_matching,0.3,2.0,49,189
19,remove,0.0,C,,C[C=6I,[C=6I,5,remove C from position 0,flow_matching,0.3,2.0,49,189
20,add,5.0,6,,[C=6I,[C=6I6,6,add 6 at position 5,flow_matching,0.3,2.0,49,189
21,replace,0.0,C,[,[C=6I6,CC=6I6,6,replace [ at position 0 with C,flow_matching,0.3,2.0,49,189
22,replace,1.0,[,C,CC=6I6,C[=6I6,6,replace C at position 1 with [,flow_matching,0.3,2.0,49,189
23,remove,4.0,I,,C[=6I6,C[=66,5,remove I from position 4,flow_matching,0.3,2.0,49,189
24,add,0.0,[,,C[=66,[C[=66,6,add [ at position 0,flow_matching,0.3,2.0,49,189
25,remove,1.0,C,,[C[=66,[[=66,5,remove C from position 1,flow_matching,0.3,2.0,49,189
26,add,1.0,+,,[[=66,[+[=66,6,add + at position 1,flow_matching,0.3,2.0,49,189
27,remove,5.0,6,,[+[=66,[+[=6,5,remove 6 from position 5,flow_matching,0.3,2.0,49,189
28,replace,0.0,C,[,[+[=6,C+[=6,5,replace [ at position 0 with C,flow_matching,0.3,2.0,49,189
29,replace,1.0,[,+,C+[=6,C[[=6,5,replace + at position 1 with [,flow_matching,0.3,2.0,49,189
30,replace,2.0,C,[,C[[=6,C[C=6,5,replace [ at position 2 with C,flow_matching,0.3,2.0,49,189
31,remove,2.0,C,,C[C=6,C[=6,4,remove C from position 2,flow_matching,0.3,2.0,49,189
32,replace,2.0,C,=,C[=6,C[C6,4,replace = at position 2 with C,flow_matching,0.3,2.0,49,189
33,remove,3.0,6,,C[C6,C[C,3,remove 6 from position 3,flow_matching,0.3,2.0,49,189
34,replace,1.0,3,[,C[C,C3C,3,replace [ at position 1 with 3,flow_matching,0.3,2.0,49,189
35,remove,1.0,3,,C3C,CC,2,remove 3 from position 1,flow_matching,0.3,2.0,49,189
36,replace,1.0,[,C,CC,C[,2,replace C at position 1 with [,flow_matching,0.3,2.0,49,189
37,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,49,189
38,add,0.0,l,,C[C,lC[C,4,add l at position 0,flow_matching,0.3,2.0,49,189
39,replace,0.0,C,l,lC[C,CC[C,4,replace l at position 0 with C,flow_matching,0.3,2.0,49,189
40,replace,1.0,[,C,CC[C,C[[C,4,replace C at position 1 with [,flow_matching,0.3,2.0,49,189
41,replace,0.0,4,C,C[[C,4[[C,4,replace C at position 0 with 4,flow_matching,0.3,2.0,49,189
42,remove,3.0,C,,4[[C,4[[,3,remove C from position 3,flow_matching,0.3,2.0,49,189
43,add,2.0,=,,4[[,4[=[,4,add = at position 2,flow_matching,0.3,2.0,49,189
44,add,1.0,/,,4[=[,4/[=[,5,add / at position 1,flow_matching,0.3,2.0,49,189
45,replace,0.0,C,4,4/[=[,C/[=[,5,replace 4 at position 0 with C,flow_matching,0.3,2.0,49,189
46,replace,1.0,[,/,C/[=[,C[[=[,5,replace / at position 1 with [,flow_matching,0.3,2.0,49,189
47,replace,2.0,C,[,C[[=[,C[C=[,5,replace [ at position 2 with C,flow_matching,0.3,2.0,49,189
48,remove,3.0,=,,C[C=[,C[C[,4,remove = from position 3,flow_matching,0.3,2.0,49,189
49,add,3.0,@,,C[C[,C[C@[,5,add @ at position 3,flow_matching,0.3,2.0,49,189
50,add,0.0,N,,C[C@[,NC[C@[,6,add N at position 0,flow_matching,0.3,2.0,49,189
51,remove,2.0,[,,NC[C@[,NCC@[,5,remove [ from position 2,flow_matching,0.3,2.0,49,189
52,replace,0.0,C,N,NCC@[,CCC@[,5,replace N at position 0 with C,flow_matching,0.3,2.0,49,189
53,replace,1.0,[,C,CCC@[,C[C@[,5,replace C at position 1 with [,flow_matching,0.3,2.0,49,189
54,remove,1.0,[,,C[C@[,CC@[,4,remove [ from position 1,flow_matching,0.3,2.0,49,189
55,add,0.0,=,,CC@[,=CC@[,5,add = at position 0,flow_matching,0.3,2.0,49,189
56,remove,1.0,C,,=CC@[,=C@[,4,remove C from position 1,flow_matching,0.3,2.0,49,189
57,add,2.0,o,,=C@[,=Co@[,5,add o at position 2,flow_matching,0.3,2.0,49,189
58,replace,0.0,C,=,=Co@[,CCo@[,5,replace = at position 0 with C,flow_matching,0.3,2.0,49,189
59,replace,1.0,[,C,CCo@[,C[o@[,5,replace C at position 1 with [,flow_matching,0.3,2.0,49,189
60,add,5.0,o,,C[o@[,C[o@[o,6,add o at position 5,flow_matching,0.3,2.0,49,189
61,add,2.0,/,,C[o@[o,C[/o@[o,7,add / at position 2,flow_matching,0.3,2.0,49,189
62,add,2.0,(,,C[/o@[o,C[(/o@[o,8,add ( at position 2,flow_matching,0.3,2.0,49,189
63,remove,2.0,(,,C[(/o@[o,C[/o@[o,7,remove ( from position 2,flow_matching,0.3,2.0,49,189
64,remove,1.0,[,,C[/o@[o,C/o@[o,6,remove [ from position 1,flow_matching,0.3,2.0,49,189
65,replace,1.0,[,/,C/o@[o,C[o@[o,6,replace / at position 1 with [,flow_matching,0.3,2.0,49,189
66,replace,2.0,C,o,C[o@[o,C[C@[o,6,replace o at position 2 with C,flow_matching,0.3,2.0,49,189
67,add,6.0,4,,C[C@[o,C[C@[o4,7,add 4 at position 6,flow_matching,0.3,2.0,49,189
68,replace,4.0,H,[,C[C@[o4,C[C@Ho4,7,replace [ at position 4 with H,flow_matching,0.3,2.0,49,189
69,replace,5.0,],o,C[C@Ho4,C[C@H]4,7,replace o at position 5 with ],flow_matching,0.3,2.0,49,189
70,add,0.0,2,,C[C@H]4,2C[C@H]4,8,add 2 at position 0,flow_matching,0.3,2.0,49,189
71,replace,4.0,S,@,2C[C@H]4,2C[CSH]4,8,replace @ at position 4 with S,flow_matching,0.3,2.0,49,189
72,replace,2.0,=,[,2C[CSH]4,2C=CSH]4,8,replace [ at position 2 with =,flow_matching,0.3,2.0,49,189
73,replace,2.0,o,=,2C=CSH]4,2CoCSH]4,8,replace = at position 2 with o,flow_matching,0.3,2.0,49,189
74,add,0.0,1,,2CoCSH]4,12CoCSH]4,9,add 1 at position 0,flow_matching,0.3,2.0,49,189
75,add,5.0,#,,12CoCSH]4,12CoC#SH]4,10,add # at position 5,flow_matching,0.3,2.0,49,189
76,remove,1.0,2,,12CoC#SH]4,1CoC#SH]4,9,remove 2 from position 1,flow_matching,0.3,2.0,49,189
77,replace,0.0,C,1,1CoC#SH]4,CCoC#SH]4,9,replace 1 at position 0 with C,flow_matching,0.3,2.0,49,189
78,add,4.0,c,,CCoC#SH]4,CCoCc#SH]4,10,add c at position 4,flow_matching,0.3,2.0,49,189
79,remove,8.0,],,CCoCc#SH]4,CCoCc#SH4,9,remove ] from position 8,flow_matching,0.3,2.0,49,189
80,replace,8.0,3,4,CCoCc#SH4,CCoCc#SH3,9,replace 4 at position 8 with 3,flow_matching,0.3,2.0,49,189
81,remove,6.0,S,,CCoCc#SH3,CCoCc#H3,8,remove S from position 6,flow_matching,0.3,2.0,49,189
82,replace,0.0,c,C,CCoCc#H3,cCoCc#H3,8,replace C at position 0 with c,flow_matching,0.3,2.0,49,189
83,replace,4.0,1,c,cCoCc#H3,cCoC1#H3,8,replace c at position 4 with 1,flow_matching,0.3,2.0,49,189
84,add,8.0,2,,cCoC1#H3,cCoC1#H32,9,add 2 at position 8,flow_matching,0.3,2.0,49,189
85,add,9.0,#,,cCoC1#H32,cCoC1#H32#,10,add # at position 9,flow_matching,0.3,2.0,49,189
86,replace,0.0,C,c,cCoC1#H32#,CCoC1#H32#,10,replace c at position 0 with C,flow_matching,0.3,2.0,49,189
87,add,3.0,2,,CCoC1#H32#,CCo2C1#H32#,11,add 2 at position 3,flow_matching,0.3,2.0,49,189
88,replace,1.0,[,C,CCo2C1#H32#,C[o2C1#H32#,11,replace C at position 1 with [,flow_matching,0.3,2.0,49,189
89,replace,7.0,O,H,C[o2C1#H32#,C[o2C1#O32#,11,replace H at position 7 with O,flow_matching,0.3,2.0,49,189
90,replace,2.0,C,o,C[o2C1#O32#,C[C2C1#O32#,11,replace o at position 2 with C,flow_matching,0.3,2.0,49,189
91,add,1.0,C,,C[C2C1#O32#,CC[C2C1#O32#,12,add C at position 1,flow_matching,0.3,2.0,49,189
92,add,4.0,[,,CC[C2C1#O32#,CC[C[2C1#O32#,13,add [ at position 4,flow_matching,0.3,2.0,49,189
93,remove,1.0,C,,CC[C[2C1#O32#,C[C[2C1#O32#,12,remove C from position 1,flow_matching,0.3,2.0,49,189
94,replace,3.0,@,[,C[C[2C1#O32#,C[C@2C1#O32#,12,replace [ at position 3 with @,flow_matching,0.3,2.0,49,189
95,replace,1.0,1,[,C[C@2C1#O32#,C1C@2C1#O32#,12,replace [ at position 1 with 1,flow_matching,0.3,2.0,49,189
96,add,10.0,n,,C1C@2C1#O32#,C1C@2C1#O3n2#,13,add n at position 10,flow_matching,0.3,2.0,49,189
97,replace,12.0,=,#,C1C@2C1#O3n2#,C1C@2C1#O3n2=,13,replace # at position 12 with =,flow_matching,0.3,2.0,49,189
98,replace,1.0,[,1,C1C@2C1#O3n2=,C[C@2C1#O3n2=,13,replace 1 at position 1 with [,flow_matching,0.3,2.0,49,189
99,replace,4.0,H,2,C[C@2C1#O3n2=,C[C@HC1#O3n2=,13,replace 2 at position 4 with H,flow_matching,0.3,2.0,49,189
100,replace,5.0,],C,C[C@HC1#O3n2=,C[C@H]1#O3n2=,13,replace C at position 5 with ],flow_matching,0.3,2.0,49,189
101,add,4.0,1,,C[C@H]1#O3n2=,C[C@1H]1#O3n2=,14,add 1 at position 4,flow_matching,0.3,2.0,49,189
102,remove,7.0,1,,C[C@1H]1#O3n2=,C[C@1H]#O3n2=,13,remove 1 from position 7,flow_matching,0.3,2.0,49,189
103,remove,5.0,H,,C[C@1H]#O3n2=,C[C@1]#O3n2=,12,remove H from position 5,flow_matching,0.3,2.0,49,189
104,replace,7.0,-,O,C[C@1]#O3n2=,C[C@1]#-3n2=,12,replace O at position 7 with -,flow_matching,0.3,2.0,49,189
105,add,6.0,3,,C[C@1]#-3n2=,C[C@1]3#-3n2=,13,add 3 at position 6,flow_matching,0.3,2.0,49,189
106,remove,1.0,[,,C[C@1]3#-3n2=,CC@1]3#-3n2=,12,remove [ from position 1,flow_matching,0.3,2.0,49,189
107,add,5.0,B,,CC@1]3#-3n2=,CC@1]B3#-3n2=,13,add B at position 5,flow_matching,0.3,2.0,49,189
108,replace,1.0,[,C,CC@1]B3#-3n2=,C[@1]B3#-3n2=,13,replace C at position 1 with [,flow_matching,0.3,2.0,49,189
109,add,3.0,B,,C[@1]B3#-3n2=,C[@B1]B3#-3n2=,14,add B at position 3,flow_matching,0.3,2.0,49,189
110,replace,2.0,F,@,C[@B1]B3#-3n2=,C[FB1]B3#-3n2=,14,replace @ at position 2 with F,flow_matching,0.3,2.0,49,189
111,replace,2.0,C,F,C[FB1]B3#-3n2=,C[CB1]B3#-3n2=,14,replace F at position 2 with C,flow_matching,0.3,2.0,49,189
112,replace,12.0,\,2,C[CB1]B3#-3n2=,C[CB1]B3#-3n\=,14,replace 2 at position 12 with \,flow_matching,0.3,2.0,49,189
113,remove,7.0,3,,C[CB1]B3#-3n\=,C[CB1]B#-3n\=,13,remove 3 from position 7,flow_matching,0.3,2.0,49,189
114,replace,2.0,H,C,C[CB1]B#-3n\=,C[HB1]B#-3n\=,13,replace C at position 2 with H,flow_matching,0.3,2.0,49,189
115,remove,4.0,1,,C[HB1]B#-3n\=,C[HB]B#-3n\=,12,remove 1 from position 4,flow_matching,0.3,2.0,49,189
116,replace,2.0,C,H,C[HB]B#-3n\=,C[CB]B#-3n\=,12,replace H at position 2 with C,flow_matching,0.3,2.0,49,189
117,add,12.0,],,C[CB]B#-3n\=,C[CB]B#-3n\=],13,add ] at position 12,flow_matching,0.3,2.0,49,189
118,add,7.0,],,C[CB]B#-3n\=],C[CB]B#]-3n\=],14,add ] at position 7,flow_matching,0.3,2.0,49,189
119,replace,3.0,@,B,C[CB]B#]-3n\=],C[C@]B#]-3n\=],14,replace B at position 3 with @,flow_matching,0.3,2.0,49,189
120,replace,0.0,-,C,C[C@]B#]-3n\=],-[C@]B#]-3n\=],14,replace C at position 0 with -,flow_matching,0.3,2.0,49,189
121,remove,11.0,\,,-[C@]B#]-3n\=],-[C@]B#]-3n=],13,remove \ from position 11,flow_matching,0.3,2.0,49,189
122,add,0.0,l,,-[C@]B#]-3n=],l-[C@]B#]-3n=],14,add l at position 0,flow_matching,0.3,2.0,49,189
123,replace,0.0,C,l,l-[C@]B#]-3n=],C-[C@]B#]-3n=],14,replace l at position 0 with C,flow_matching,0.3,2.0,49,189
124,add,0.0,H,,C-[C@]B#]-3n=],HC-[C@]B#]-3n=],15,add H at position 0,flow_matching,0.3,2.0,49,189
125,replace,0.0,C,H,HC-[C@]B#]-3n=],CC-[C@]B#]-3n=],15,replace H at position 0 with C,flow_matching,0.3,2.0,49,189
126,replace,12.0,[,n,CC-[C@]B#]-3n=],CC-[C@]B#]-3[=],15,replace n at position 12 with [,flow_matching,0.3,2.0,49,189
127,remove,9.0,],,CC-[C@]B#]-3[=],CC-[C@]B#-3[=],14,remove ] from position 9,flow_matching,0.3,2.0,49,189
128,replace,1.0,[,C,CC-[C@]B#-3[=],C[-[C@]B#-3[=],14,replace C at position 1 with [,flow_matching,0.3,2.0,49,189
129,replace,9.0,4,-,C[-[C@]B#-3[=],C[-[C@]B#43[=],14,replace - at position 9 with 4,flow_matching,0.3,2.0,49,189
130,replace,5.0,1,@,C[-[C@]B#43[=],C[-[C1]B#43[=],14,replace @ at position 5 with 1,flow_matching,0.3,2.0,49,189
131,replace,4.0,c,C,C[-[C1]B#43[=],C[-[c1]B#43[=],14,replace C at position 4 with c,flow_matching,0.3,2.0,49,189
132,replace,0.0,=,C,C[-[c1]B#43[=],=[-[c1]B#43[=],14,replace C at position 0 with =,flow_matching,0.3,2.0,49,189
133,add,0.0,),,=[-[c1]B#43[=],)=[-[c1]B#43[=],15,add ) at position 0,flow_matching,0.3,2.0,49,189
134,add,9.0,#,,)=[-[c1]B#43[=],)=[-[c1]B##43[=],16,add # at position 9,flow_matching,0.3,2.0,49,189
135,replace,0.0,C,),)=[-[c1]B##43[=],C=[-[c1]B##43[=],16,replace ) at position 0 with C,flow_matching,0.3,2.0,49,189
136,replace,1.0,[,=,C=[-[c1]B##43[=],C[[-[c1]B##43[=],16,replace = at position 1 with [,flow_matching,0.3,2.0,49,189
137,remove,2.0,[,,C[[-[c1]B##43[=],C[-[c1]B##43[=],15,remove [ from position 2,flow_matching,0.3,2.0,49,189
138,replace,2.0,C,-,C[-[c1]B##43[=],C[C[c1]B##43[=],15,replace - at position 2 with C,flow_matching,0.3,2.0,49,189
139,replace,7.0,n,B,C[C[c1]B##43[=],C[C[c1]n##43[=],15,replace B at position 7 with n,flow_matching,0.3,2.0,49,189
140,remove,13.0,=,,C[C[c1]n##43[=],C[C[c1]n##43[],14,remove = from position 13,flow_matching,0.3,2.0,49,189
141,replace,6.0,3,],C[C[c1]n##43[],C[C[c13n##43[],14,replace ] at position 6 with 3,flow_matching,0.3,2.0,49,189
142,add,11.0,4,,C[C[c13n##43[],C[C[c13n##443[],15,add 4 at position 11,flow_matching,0.3,2.0,49,189
143,replace,7.0,@,n,C[C[c13n##443[],C[C[c13@##443[],15,replace n at position 7 with @,flow_matching,0.3,2.0,49,189
144,replace,8.0,-,#,C[C[c13@##443[],C[C[c13@-#443[],15,replace # at position 8 with -,flow_matching,0.3,2.0,49,189
145,replace,3.0,@,[,C[C[c13@-#443[],C[C@c13@-#443[],15,replace [ at position 3 with @,flow_matching,0.3,2.0,49,189
146,replace,4.0,H,c,C[C@c13@-#443[],C[C@H13@-#443[],15,replace c at position 4 with H,flow_matching,0.3,2.0,49,189
147,replace,5.0,],1,C[C@H13@-#443[],C[C@H]3@-#443[],15,replace 1 at position 5 with ],flow_matching,0.3,2.0,49,189
148,replace,6.0,(,3,C[C@H]3@-#443[],C[C@H](@-#443[],15,replace 3 at position 6 with (,flow_matching,0.3,2.0,49,189
149,replace,7.0,N,@,C[C@H](@-#443[],C[C@H](N-#443[],15,replace @ at position 7 with N,flow_matching,0.3,2.0,49,189
150,replace,8.0,C,-,C[C@H](N-#443[],C[C@H](NC#443[],15,replace - at position 8 with C,flow_matching,0.3,2.0,49,189
151,replace,9.0,(,#,C[C@H](NC#443[],C[C@H](NC(443[],15,replace # at position 9 with (,flow_matching,0.3,2.0,49,189
152,replace,10.0,=,4,C[C@H](NC(443[],C[C@H](NC(=43[],15,replace 4 at position 10 with =,flow_matching,0.3,2.0,49,189
153,replace,11.0,O,4,C[C@H](NC(=43[],C[C@H](NC(=O3[],15,replace 4 at position 11 with O,flow_matching,0.3,2.0,49,189
154,replace,12.0,),3,C[C@H](NC(=O3[],C[C@H](NC(=O)[],15,replace 3 at position 12 with ),flow_matching,0.3,2.0,49,189
155,replace,14.0,C,],C[C@H](NC(=O)[],C[C@H](NC(=O)[C,15,replace ] at position 14 with C,flow_matching,0.3,2.0,49,189
156,add,15.0,@,,C[C@H](NC(=O)[C,C[C@H](NC(=O)[C@,16,add @ at position 15,flow_matching,0.3,2.0,49,189
157,add,16.0,H,,C[C@H](NC(=O)[C@,C[C@H](NC(=O)[C@H,17,add H at position 16,flow_matching,0.3,2.0,49,189
158,add,17.0,],,C[C@H](NC(=O)[C@H,C[C@H](NC(=O)[C@H],18,add ] at position 17,flow_matching,0.3,2.0,49,189
159,add,18.0,1,,C[C@H](NC(=O)[C@H],C[C@H](NC(=O)[C@H]1,19,add 1 at position 18,flow_matching,0.3,2.0,49,189
160,add,19.0,C,,C[C@H](NC(=O)[C@H]1,C[C@H](NC(=O)[C@H]1C,20,add C at position 19,flow_matching,0.3,2.0,49,189
161,add,20.0,C,,C[C@H](NC(=O)[C@H]1C,C[C@H](NC(=O)[C@H]1CC,21,add C at position 20,flow_matching,0.3,2.0,49,189
162,add,21.0,[,,C[C@H](NC(=O)[C@H]1CC,C[C@H](NC(=O)[C@H]1CC[,22,add [ at position 21,flow_matching,0.3,2.0,49,189
163,add,22.0,C,,C[C@H](NC(=O)[C@H]1CC[,C[C@H](NC(=O)[C@H]1CC[C,23,add C at position 22,flow_matching,0.3,2.0,49,189
164,add,23.0,@,,C[C@H](NC(=O)[C@H]1CC[C,C[C@H](NC(=O)[C@H]1CC[C@,24,add @ at position 23,flow_matching,0.3,2.0,49,189
165,add,24.0,H,,C[C@H](NC(=O)[C@H]1CC[C@,C[C@H](NC(=O)[C@H]1CC[C@H,25,add H at position 24,flow_matching,0.3,2.0,49,189
166,add,25.0,],,C[C@H](NC(=O)[C@H]1CC[C@H,C[C@H](NC(=O)[C@H]1CC[C@H],26,add ] at position 25,flow_matching,0.3,2.0,49,189
167,add,26.0,(,,C[C@H](NC(=O)[C@H]1CC[C@H],C[C@H](NC(=O)[C@H]1CC[C@H](,27,add ( at position 26,flow_matching,0.3,2.0,49,189
168,add,27.0,C,,C[C@H](NC(=O)[C@H]1CC[C@H](,C[C@H](NC(=O)[C@H]1CC[C@H](C,28,add C at position 27,flow_matching,0.3,2.0,49,189
169,add,28.0,[,,C[C@H](NC(=O)[C@H]1CC[C@H](C,C[C@H](NC(=O)[C@H]1CC[C@H](C[,29,add [ at position 28,flow_matching,0.3,2.0,49,189
170,add,29.0,N,,C[C@H](NC(=O)[C@H]1CC[C@H](C[,C[C@H](NC(=O)[C@H]1CC[C@H](C[N,30,add N at position 29,flow_matching,0.3,2.0,49,189
171,add,30.0,H,,C[C@H](NC(=O)[C@H]1CC[C@H](C[N,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH,31,add H at position 30,flow_matching,0.3,2.0,49,189
172,add,31.0,3,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3,32,add 3 at position 31,flow_matching,0.3,2.0,49,189
173,add,32.0,+,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+,33,add + at position 32,flow_matching,0.3,2.0,49,189
174,add,33.0,],,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+],34,add ] at position 33,flow_matching,0.3,2.0,49,189
175,add,34.0,),,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+],C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+]),35,add ) at position 34,flow_matching,0.3,2.0,49,189
176,add,35.0,O,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+]),C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O,36,add O at position 35,flow_matching,0.3,2.0,49,189
177,add,36.0,1,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1,37,add 1 at position 36,flow_matching,0.3,2.0,49,189
178,add,37.0,),,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1),38,add ) at position 37,flow_matching,0.3,2.0,49,189
179,add,38.0,C,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1),C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C,39,add C at position 38,flow_matching,0.3,2.0,49,189
180,add,39.0,(,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(,40,add ( at position 39,flow_matching,0.3,2.0,49,189
181,add,40.0,=,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=,41,add = at position 40,flow_matching,0.3,2.0,49,189
182,add,41.0,O,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O,42,add O at position 41,flow_matching,0.3,2.0,49,189
183,add,42.0,),,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O),43,add ) at position 42,flow_matching,0.3,2.0,49,189
184,add,43.0,N,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O),C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N,44,add N at position 43,flow_matching,0.3,2.0,49,189
185,add,44.0,(,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N(,45,add ( at position 44,flow_matching,0.3,2.0,49,189
186,add,45.0,C,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N(,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N(C,46,add C at position 45,flow_matching,0.3,2.0,49,189
187,add,46.0,),,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N(C,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N(C),47,add ) at position 46,flow_matching,0.3,2.0,49,189
188,add,47.0,C,,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N(C),C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N(C)C,48,add C at position 47,flow_matching,0.3,2.0,49,189
189,add,48.0,"
",,C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N(C)C,"C[C@H](NC(=O)[C@H]1CC[C@H](C[NH3+])O1)C(=O)N(C)C
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,189
