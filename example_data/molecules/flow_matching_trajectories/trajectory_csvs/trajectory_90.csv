step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,151
1,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,43,151
2,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,43,151
3,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,43,151
4,add,2.0,C,,CC,CCC,3,add C at position 2,flow_matching,0.3,2.0,43,151
5,add,2.0,),,CCC,CC)C,4,add ) at position 2,flow_matching,0.3,2.0,43,151
6,replace,2.0,7,),CC)C,CC7C,4,replace ) at position 2 with 7,flow_matching,0.3,2.0,43,151
7,remove,0.0,C,,CC7C,C7C,3,remove C from position 0,flow_matching,0.3,2.0,43,151
8,add,0.0,c,,C7C,cC7C,4,add c at position 0,flow_matching,0.3,2.0,43,151
9,replace,0.0,C,c,cC7C,CC7C,4,replace c at position 0 with C,flow_matching,0.3,2.0,43,151
10,replace,2.0,C,7,CC7C,CCCC,4,replace 7 at position 2 with C,flow_matching,0.3,2.0,43,151
11,replace,3.0,[,C,CCCC,CCC[,4,replace C at position 3 with [,flow_matching,0.3,2.0,43,151
12,add,2.0,F,,CCC[,CCFC[,5,add F at position 2,flow_matching,0.3,2.0,43,151
13,replace,0.0,],C,CCFC[,]CFC[,5,replace C at position 0 with ],flow_matching,0.3,2.0,43,151
14,replace,0.0,C,],]CFC[,CCFC[,5,replace ] at position 0 with C,flow_matching,0.3,2.0,43,151
15,add,5.0,/,,CCFC[,CCFC[/,6,add / at position 5,flow_matching,0.3,2.0,43,151
16,add,5.0,#,,CCFC[/,CCFC[#/,7,add # at position 5,flow_matching,0.3,2.0,43,151
17,add,7.0,],,CCFC[#/,CCFC[#/],8,add ] at position 7,flow_matching,0.3,2.0,43,151
18,replace,2.0,C,F,CCFC[#/],CCCC[#/],8,replace F at position 2 with C,flow_matching,0.3,2.0,43,151
19,replace,3.0,[,C,CCCC[#/],CCC[[#/],8,replace C at position 3 with [,flow_matching,0.3,2.0,43,151
20,replace,3.0,N,[,CCC[[#/],CCCN[#/],8,replace [ at position 3 with N,flow_matching,0.3,2.0,43,151
21,add,6.0,H,,CCCN[#/],CCCN[#H/],9,add H at position 6,flow_matching,0.3,2.0,43,151
22,replace,3.0,[,N,CCCN[#H/],CCC[[#H/],9,replace N at position 3 with [,flow_matching,0.3,2.0,43,151
23,add,0.0,C,,CCC[[#H/],CCCC[[#H/],10,add C at position 0,flow_matching,0.3,2.0,43,151
24,add,5.0,O,,CCCC[[#H/],CCCC[O[#H/],11,add O at position 5,flow_matching,0.3,2.0,43,151
25,replace,3.0,[,C,CCCC[O[#H/],CCC[[O[#H/],11,replace C at position 3 with [,flow_matching,0.3,2.0,43,151
26,add,5.0,l,,CCC[[O[#H/],CCC[[lO[#H/],12,add l at position 5,flow_matching,0.3,2.0,43,151
27,replace,4.0,C,[,CCC[[lO[#H/],CCC[ClO[#H/],12,replace [ at position 4 with C,flow_matching,0.3,2.0,43,151
28,replace,9.0,c,H,CCC[ClO[#H/],CCC[ClO[#c/],12,replace H at position 9 with c,flow_matching,0.3,2.0,43,151
29,replace,3.0,2,[,CCC[ClO[#c/],CCC2ClO[#c/],12,replace [ at position 3 with 2,flow_matching,0.3,2.0,43,151
30,remove,3.0,2,,CCC2ClO[#c/],CCCClO[#c/],11,remove 2 from position 3,flow_matching,0.3,2.0,43,151
31,remove,4.0,l,,CCCClO[#c/],CCCCO[#c/],10,remove l from position 4,flow_matching,0.3,2.0,43,151
32,remove,0.0,C,,CCCCO[#c/],CCCO[#c/],9,remove C from position 0,flow_matching,0.3,2.0,43,151
33,remove,3.0,O,,CCCO[#c/],CCC[#c/],8,remove O from position 3,flow_matching,0.3,2.0,43,151
34,add,2.0,l,,CCC[#c/],CClC[#c/],9,add l at position 2,flow_matching,0.3,2.0,43,151
35,remove,2.0,l,,CClC[#c/],CCC[#c/],8,remove l from position 2,flow_matching,0.3,2.0,43,151
36,add,0.0,n,,CCC[#c/],nCCC[#c/],9,add n at position 0,flow_matching,0.3,2.0,43,151
37,add,0.0,],,nCCC[#c/],]nCCC[#c/],10,add ] at position 0,flow_matching,0.3,2.0,43,151
38,replace,0.0,C,],]nCCC[#c/],CnCCC[#c/],10,replace ] at position 0 with C,flow_matching,0.3,2.0,43,151
39,add,10.0,o,,CnCCC[#c/],CnCCC[#c/]o,11,add o at position 10,flow_matching,0.3,2.0,43,151
40,replace,1.0,C,n,CnCCC[#c/]o,CCCCC[#c/]o,11,replace n at position 1 with C,flow_matching,0.3,2.0,43,151
41,replace,10.0,+,o,CCCCC[#c/]o,CCCCC[#c/]+,11,replace o at position 10 with +,flow_matching,0.3,2.0,43,151
42,replace,3.0,[,C,CCCCC[#c/]+,CCC[C[#c/]+,11,replace C at position 3 with [,flow_matching,0.3,2.0,43,151
43,replace,5.0,@,[,CCC[C[#c/]+,CCC[C@#c/]+,11,replace [ at position 5 with @,flow_matching,0.3,2.0,43,151
44,add,4.0,),,CCC[C@#c/]+,CCC[)C@#c/]+,12,add ) at position 4,flow_matching,0.3,2.0,43,151
45,remove,2.0,C,,CCC[)C@#c/]+,CC[)C@#c/]+,11,remove C from position 2,flow_matching,0.3,2.0,43,151
46,add,11.0,-,,CC[)C@#c/]+,CC[)C@#c/]+-,12,add - at position 11,flow_matching,0.3,2.0,43,151
47,add,3.0,@,,CC[)C@#c/]+-,CC[@)C@#c/]+-,13,add @ at position 3,flow_matching,0.3,2.0,43,151
48,remove,1.0,C,,CC[@)C@#c/]+-,C[@)C@#c/]+-,12,remove C from position 1,flow_matching,0.3,2.0,43,151
49,add,6.0,4,,C[@)C@#c/]+-,C[@)C@4#c/]+-,13,add 4 at position 6,flow_matching,0.3,2.0,43,151
50,replace,1.0,C,[,C[@)C@4#c/]+-,CC@)C@4#c/]+-,13,replace [ at position 1 with C,flow_matching,0.3,2.0,43,151
51,add,7.0,/,,CC@)C@4#c/]+-,CC@)C@4/#c/]+-,14,add / at position 7,flow_matching,0.3,2.0,43,151
52,replace,2.0,C,@,CC@)C@4/#c/]+-,CCC)C@4/#c/]+-,14,replace @ at position 2 with C,flow_matching,0.3,2.0,43,151
53,add,0.0,3,,CCC)C@4/#c/]+-,3CCC)C@4/#c/]+-,15,add 3 at position 0,flow_matching,0.3,2.0,43,151
54,remove,10.0,c,,3CCC)C@4/#c/]+-,3CCC)C@4/#/]+-,14,remove c from position 10,flow_matching,0.3,2.0,43,151
55,replace,2.0,@,C,3CCC)C@4/#/]+-,3C@C)C@4/#/]+-,14,replace C at position 2 with @,flow_matching,0.3,2.0,43,151
56,add,9.0,N,,3C@C)C@4/#/]+-,3C@C)C@4/N#/]+-,15,add N at position 9,flow_matching,0.3,2.0,43,151
57,remove,14.0,-,,3C@C)C@4/N#/]+-,3C@C)C@4/N#/]+,14,remove - from position 14,flow_matching,0.3,2.0,43,151
58,add,8.0,4,,3C@C)C@4/N#/]+,3C@C)C@44/N#/]+,15,add 4 at position 8,flow_matching,0.3,2.0,43,151
59,replace,0.0,C,3,3C@C)C@44/N#/]+,CC@C)C@44/N#/]+,15,replace 3 at position 0 with C,flow_matching,0.3,2.0,43,151
60,replace,2.0,C,@,CC@C)C@44/N#/]+,CCCC)C@44/N#/]+,15,replace @ at position 2 with C,flow_matching,0.3,2.0,43,151
61,replace,3.0,[,C,CCCC)C@44/N#/]+,CCC[)C@44/N#/]+,15,replace C at position 3 with [,flow_matching,0.3,2.0,43,151
62,replace,11.0,\,#,CCC[)C@44/N#/]+,CCC[)C@44/N\/]+,15,replace # at position 11 with \,flow_matching,0.3,2.0,43,151
63,replace,4.0,C,),CCC[)C@44/N\/]+,CCC[CC@44/N\/]+,15,replace ) at position 4 with C,flow_matching,0.3,2.0,43,151
64,replace,5.0,@,C,CCC[CC@44/N\/]+,CCC[C@@44/N\/]+,15,replace C at position 5 with @,flow_matching,0.3,2.0,43,151
65,remove,9.0,/,,CCC[C@@44/N\/]+,CCC[C@@44N\/]+,14,remove / from position 9,flow_matching,0.3,2.0,43,151
66,replace,4.0,=,C,CCC[C@@44N\/]+,CCC[=@@44N\/]+,14,replace C at position 4 with =,flow_matching,0.3,2.0,43,151
67,add,9.0,N,,CCC[=@@44N\/]+,CCC[=@@44NN\/]+,15,add N at position 9,flow_matching,0.3,2.0,43,151
68,replace,4.0,C,=,CCC[=@@44NN\/]+,CCC[C@@44NN\/]+,15,replace = at position 4 with C,flow_matching,0.3,2.0,43,151
69,remove,0.0,C,,CCC[C@@44NN\/]+,CC[C@@44NN\/]+,14,remove C from position 0,flow_matching,0.3,2.0,43,151
70,remove,11.0,/,,CC[C@@44NN\/]+,CC[C@@44NN\]+,13,remove / from position 11,flow_matching,0.3,2.0,43,151
71,remove,4.0,@,,CC[C@@44NN\]+,CC[C@44NN\]+,12,remove @ from position 4,flow_matching,0.3,2.0,43,151
72,remove,4.0,@,,CC[C@44NN\]+,CC[C44NN\]+,11,remove @ from position 4,flow_matching,0.3,2.0,43,151
73,replace,2.0,6,[,CC[C44NN\]+,CC6C44NN\]+,11,replace [ at position 2 with 6,flow_matching,0.3,2.0,43,151
74,remove,7.0,N,,CC6C44NN\]+,CC6C44N\]+,10,remove N from position 7,flow_matching,0.3,2.0,43,151
75,remove,8.0,],,CC6C44N\]+,CC6C44N\+,9,remove ] from position 8,flow_matching,0.3,2.0,43,151
76,add,7.0,#,,CC6C44N\+,CC6C44N#\+,10,add # at position 7,flow_matching,0.3,2.0,43,151
77,remove,4.0,4,,CC6C44N#\+,CC6C4N#\+,9,remove 4 from position 4,flow_matching,0.3,2.0,43,151
78,remove,7.0,\,,CC6C4N#\+,CC6C4N#+,8,remove \ from position 7,flow_matching,0.3,2.0,43,151
79,add,6.0,/,,CC6C4N#+,CC6C4N/#+,9,add / at position 6,flow_matching,0.3,2.0,43,151
80,replace,2.0,C,6,CC6C4N/#+,CCCC4N/#+,9,replace 6 at position 2 with C,flow_matching,0.3,2.0,43,151
81,remove,7.0,#,,CCCC4N/#+,CCCC4N/+,8,remove # from position 7,flow_matching,0.3,2.0,43,151
82,add,4.0,4,,CCCC4N/+,CCCC44N/+,9,add 4 at position 4,flow_matching,0.3,2.0,43,151
83,add,9.0,3,,CCCC44N/+,CCCC44N/+3,10,add 3 at position 9,flow_matching,0.3,2.0,43,151
84,replace,6.0,S,N,CCCC44N/+3,CCCC44S/+3,10,replace N at position 6 with S,flow_matching,0.3,2.0,43,151
85,replace,6.0,3,S,CCCC44S/+3,CCCC443/+3,10,replace S at position 6 with 3,flow_matching,0.3,2.0,43,151
86,replace,3.0,[,C,CCCC443/+3,CCC[443/+3,10,replace C at position 3 with [,flow_matching,0.3,2.0,43,151
87,replace,4.0,C,4,CCC[443/+3,CCC[C43/+3,10,replace 4 at position 4 with C,flow_matching,0.3,2.0,43,151
88,replace,1.0,O,C,CCC[C43/+3,COC[C43/+3,10,replace C at position 1 with O,flow_matching,0.3,2.0,43,151
89,remove,5.0,4,,COC[C43/+3,COC[C3/+3,9,remove 4 from position 5,flow_matching,0.3,2.0,43,151
90,replace,8.0,F,3,COC[C3/+3,COC[C3/+F,9,replace 3 at position 8 with F,flow_matching,0.3,2.0,43,151
91,replace,1.0,C,O,COC[C3/+F,CCC[C3/+F,9,replace O at position 1 with C,flow_matching,0.3,2.0,43,151
92,replace,5.0,@,3,CCC[C3/+F,CCC[C@/+F,9,replace 3 at position 5 with @,flow_matching,0.3,2.0,43,151
93,replace,6.0,=,/,CCC[C@/+F,CCC[C@=+F,9,replace / at position 6 with =,flow_matching,0.3,2.0,43,151
94,remove,5.0,@,,CCC[C@=+F,CCC[C=+F,8,remove @ from position 5,flow_matching,0.3,2.0,43,151
95,add,8.0,7,,CCC[C=+F,CCC[C=+F7,9,add 7 at position 8,flow_matching,0.3,2.0,43,151
96,replace,4.0,-,C,CCC[C=+F7,CCC[-=+F7,9,replace C at position 4 with -,flow_matching,0.3,2.0,43,151
97,add,5.0,F,,CCC[-=+F7,CCC[-F=+F7,10,add F at position 5,flow_matching,0.3,2.0,43,151
98,replace,4.0,C,-,CCC[-F=+F7,CCC[CF=+F7,10,replace - at position 4 with C,flow_matching,0.3,2.0,43,151
99,add,9.0,/,,CCC[CF=+F7,CCC[CF=+F/7,11,add / at position 9,flow_matching,0.3,2.0,43,151
100,remove,4.0,C,,CCC[CF=+F/7,CCC[F=+F/7,10,remove C from position 4,flow_matching,0.3,2.0,43,151
101,remove,0.0,C,,CCC[F=+F/7,CC[F=+F/7,9,remove C from position 0,flow_matching,0.3,2.0,43,151
102,add,0.0,],,CC[F=+F/7,]CC[F=+F/7,10,add ] at position 0,flow_matching,0.3,2.0,43,151
103,replace,5.0,/,=,]CC[F=+F/7,]CC[F/+F/7,10,replace = at position 5 with /,flow_matching,0.3,2.0,43,151
104,replace,0.0,C,],]CC[F/+F/7,CCC[F/+F/7,10,replace ] at position 0 with C,flow_matching,0.3,2.0,43,151
105,add,0.0,r,,CCC[F/+F/7,rCCC[F/+F/7,11,add r at position 0,flow_matching,0.3,2.0,43,151
106,add,3.0,@,,rCCC[F/+F/7,rCC@C[F/+F/7,12,add @ at position 3,flow_matching,0.3,2.0,43,151
107,remove,5.0,[,,rCC@C[F/+F/7,rCC@CF/+F/7,11,remove [ from position 5,flow_matching,0.3,2.0,43,151
108,remove,7.0,+,,rCC@CF/+F/7,rCC@CF/F/7,10,remove + from position 7,flow_matching,0.3,2.0,43,151
109,remove,4.0,C,,rCC@CF/F/7,rCC@F/F/7,9,remove C from position 4,flow_matching,0.3,2.0,43,151
110,remove,5.0,/,,rCC@F/F/7,rCC@FF/7,8,remove / from position 5,flow_matching,0.3,2.0,43,151
111,replace,0.0,C,r,rCC@FF/7,CCC@FF/7,8,replace r at position 0 with C,flow_matching,0.3,2.0,43,151
112,replace,3.0,[,@,CCC@FF/7,CCC[FF/7,8,replace @ at position 3 with [,flow_matching,0.3,2.0,43,151
113,replace,4.0,C,F,CCC[FF/7,CCC[CF/7,8,replace F at position 4 with C,flow_matching,0.3,2.0,43,151
114,replace,5.0,@,F,CCC[CF/7,CCC[C@/7,8,replace F at position 5 with @,flow_matching,0.3,2.0,43,151
115,replace,6.0,@,/,CCC[C@/7,CCC[C@@7,8,replace / at position 6 with @,flow_matching,0.3,2.0,43,151
116,replace,7.0,H,7,CCC[C@@7,CCC[C@@H,8,replace 7 at position 7 with H,flow_matching,0.3,2.0,43,151
117,add,8.0,],,CCC[C@@H,CCC[C@@H],9,add ] at position 8,flow_matching,0.3,2.0,43,151
118,add,9.0,1,,CCC[C@@H],CCC[C@@H]1,10,add 1 at position 9,flow_matching,0.3,2.0,43,151
119,add,10.0,C,,CCC[C@@H]1,CCC[C@@H]1C,11,add C at position 10,flow_matching,0.3,2.0,43,151
120,add,11.0,N,,CCC[C@@H]1C,CCC[C@@H]1CN,12,add N at position 11,flow_matching,0.3,2.0,43,151
121,add,12.0,(,,CCC[C@@H]1CN,CCC[C@@H]1CN(,13,add ( at position 12,flow_matching,0.3,2.0,43,151
122,add,13.0,C,,CCC[C@@H]1CN(,CCC[C@@H]1CN(C,14,add C at position 13,flow_matching,0.3,2.0,43,151
123,add,14.0,(,,CCC[C@@H]1CN(C,CCC[C@@H]1CN(C(,15,add ( at position 14,flow_matching,0.3,2.0,43,151
124,add,15.0,=,,CCC[C@@H]1CN(C(,CCC[C@@H]1CN(C(=,16,add = at position 15,flow_matching,0.3,2.0,43,151
125,add,16.0,O,,CCC[C@@H]1CN(C(=,CCC[C@@H]1CN(C(=O,17,add O at position 16,flow_matching,0.3,2.0,43,151
126,add,17.0,),,CCC[C@@H]1CN(C(=O,CCC[C@@H]1CN(C(=O),18,add ) at position 17,flow_matching,0.3,2.0,43,151
127,add,18.0,C,,CCC[C@@H]1CN(C(=O),CCC[C@@H]1CN(C(=O)C,19,add C at position 18,flow_matching,0.3,2.0,43,151
128,add,19.0,(,,CCC[C@@H]1CN(C(=O)C,CCC[C@@H]1CN(C(=O)C(,20,add ( at position 19,flow_matching,0.3,2.0,43,151
129,add,20.0,=,,CCC[C@@H]1CN(C(=O)C(,CCC[C@@H]1CN(C(=O)C(=,21,add = at position 20,flow_matching,0.3,2.0,43,151
130,add,21.0,O,,CCC[C@@H]1CN(C(=O)C(=,CCC[C@@H]1CN(C(=O)C(=O,22,add O at position 21,flow_matching,0.3,2.0,43,151
131,add,22.0,),,CCC[C@@H]1CN(C(=O)C(=O,CCC[C@@H]1CN(C(=O)C(=O),23,add ) at position 22,flow_matching,0.3,2.0,43,151
132,add,23.0,N,,CCC[C@@H]1CN(C(=O)C(=O),CCC[C@@H]1CN(C(=O)C(=O)N,24,add N at position 23,flow_matching,0.3,2.0,43,151
133,add,24.0,c,,CCC[C@@H]1CN(C(=O)C(=O)N,CCC[C@@H]1CN(C(=O)C(=O)Nc,25,add c at position 24,flow_matching,0.3,2.0,43,151
134,add,25.0,2,,CCC[C@@H]1CN(C(=O)C(=O)Nc,CCC[C@@H]1CN(C(=O)C(=O)Nc2,26,add 2 at position 25,flow_matching,0.3,2.0,43,151
135,add,26.0,c,,CCC[C@@H]1CN(C(=O)C(=O)Nc2,CCC[C@@H]1CN(C(=O)C(=O)Nc2c,27,add c at position 26,flow_matching,0.3,2.0,43,151
136,add,27.0,c,,CCC[C@@H]1CN(C(=O)C(=O)Nc2c,CCC[C@@H]1CN(C(=O)C(=O)Nc2cc,28,add c at position 27,flow_matching,0.3,2.0,43,151
137,add,28.0,c,,CCC[C@@H]1CN(C(=O)C(=O)Nc2cc,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc,29,add c at position 28,flow_matching,0.3,2.0,43,151
138,add,29.0,(,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(,30,add ( at position 29,flow_matching,0.3,2.0,43,151
139,add,30.0,C,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C,31,add C at position 30,flow_matching,0.3,2.0,43,151
140,add,31.0,),,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C),32,add ) at position 31,flow_matching,0.3,2.0,43,151
141,add,32.0,n,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C),CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)n,33,add n at position 32,flow_matching,0.3,2.0,43,151
142,add,33.0,c,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)n,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc,34,add c at position 33,flow_matching,0.3,2.0,43,151
143,add,34.0,2,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2,35,add 2 at position 34,flow_matching,0.3,2.0,43,151
144,add,35.0,C,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2C,36,add C at position 35,flow_matching,0.3,2.0,43,151
145,add,36.0,l,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2C,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl,37,add l at position 36,flow_matching,0.3,2.0,43,151
146,add,37.0,),,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl),38,add ) at position 37,flow_matching,0.3,2.0,43,151
147,add,38.0,C,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl),CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl)C,39,add C at position 38,flow_matching,0.3,2.0,43,151
148,add,39.0,C,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl)C,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl)CC,40,add C at position 39,flow_matching,0.3,2.0,43,151
149,add,40.0,O,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl)CC,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl)CCO,41,add O at position 40,flow_matching,0.3,2.0,43,151
150,add,41.0,1,,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl)CCO,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl)CCO1,42,add 1 at position 41,flow_matching,0.3,2.0,43,151
151,add,42.0,"
",,CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl)CCO1,"CCC[C@@H]1CN(C(=O)C(=O)Nc2ccc(C)nc2Cl)CCO1
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,151
