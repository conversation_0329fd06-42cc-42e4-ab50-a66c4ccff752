step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,26,117
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,26,117
2,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,26,117
3,remove,1.0,=,,O=,O,1,remove = from position 1,flow_matching,0.3,2.0,26,117
4,add,0.0,3,,O,3O,2,add 3 at position 0,flow_matching,0.3,2.0,26,117
5,replace,0.0,O,3,3O,OO,2,replace 3 at position 0 with O,flow_matching,0.3,2.0,26,117
6,replace,1.0,=,O,OO,O=,2,replace <PERSON> at position 1 with =,flow_matching,0.3,2.0,26,117
7,remove,1.0,=,,O=,O,1,remove = from position 1,flow_matching,0.3,2.0,26,117
8,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,26,117
9,add,1.0,[,,O=,O[=,3,add [ at position 1,flow_matching,0.3,2.0,26,117
10,replace,1.0,/,[,O[=,O/=,3,replace [ at position 1 with /,flow_matching,0.3,2.0,26,117
11,remove,2.0,=,,O/=,O/,2,remove = from position 2,flow_matching,0.3,2.0,26,117
12,remove,0.0,O,,O/,/,1,remove O from position 0,flow_matching,0.3,2.0,26,117
13,replace,0.0,o,/,/,o,1,replace / at position 0 with o,flow_matching,0.3,2.0,26,117
14,add,1.0,H,,o,oH,2,add H at position 1,flow_matching,0.3,2.0,26,117
15,remove,0.0,o,,oH,H,1,remove o from position 0,flow_matching,0.3,2.0,26,117
16,replace,0.0,1,H,H,1,1,replace H at position 0 with 1,flow_matching,0.3,2.0,26,117
17,replace,0.0,O,1,1,O,1,replace 1 at position 0 with O,flow_matching,0.3,2.0,26,117
18,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,26,117
19,add,2.0,C,,O=,O=C,3,add C at position 2,flow_matching,0.3,2.0,26,117
20,remove,2.0,C,,O=C,O=,2,remove C from position 2,flow_matching,0.3,2.0,26,117
21,add,2.0,C,,O=,O=C,3,add C at position 2,flow_matching,0.3,2.0,26,117
22,add,3.0,4,,O=C,O=C4,4,add 4 at position 3,flow_matching,0.3,2.0,26,117
23,add,2.0,1,,O=C4,O=1C4,5,add 1 at position 2,flow_matching,0.3,2.0,26,117
24,replace,1.0,7,=,O=1C4,O71C4,5,replace = at position 1 with 7,flow_matching,0.3,2.0,26,117
25,remove,1.0,7,,O71C4,O1C4,4,remove 7 from position 1,flow_matching,0.3,2.0,26,117
26,replace,1.0,=,1,O1C4,O=C4,4,replace 1 at position 1 with =,flow_matching,0.3,2.0,26,117
27,remove,2.0,C,,O=C4,O=4,3,remove C from position 2,flow_matching,0.3,2.0,26,117
28,remove,2.0,4,,O=4,O=,2,remove 4 from position 2,flow_matching,0.3,2.0,26,117
29,add,0.0,-,,O=,-O=,3,add - at position 0,flow_matching,0.3,2.0,26,117
30,replace,0.0,O,-,-O=,OO=,3,replace - at position 0 with O,flow_matching,0.3,2.0,26,117
31,add,0.0,n,,OO=,nOO=,4,add n at position 0,flow_matching,0.3,2.0,26,117
32,replace,1.0,6,O,nOO=,n6O=,4,replace O at position 1 with 6,flow_matching,0.3,2.0,26,117
33,remove,3.0,=,,n6O=,n6O,3,remove = from position 3,flow_matching,0.3,2.0,26,117
34,replace,0.0,O,n,n6O,O6O,3,replace n at position 0 with O,flow_matching,0.3,2.0,26,117
35,add,2.0,o,,O6O,O6oO,4,add o at position 2,flow_matching,0.3,2.0,26,117
36,replace,1.0,=,6,O6oO,O=oO,4,replace 6 at position 1 with =,flow_matching,0.3,2.0,26,117
37,replace,2.0,C,o,O=oO,O=CO,4,replace o at position 2 with C,flow_matching,0.3,2.0,26,117
38,add,1.0,4,,O=CO,O4=CO,5,add 4 at position 1,flow_matching,0.3,2.0,26,117
39,replace,1.0,s,4,O4=CO,Os=CO,5,replace 4 at position 1 with s,flow_matching,0.3,2.0,26,117
40,replace,1.0,=,s,Os=CO,O==CO,5,replace s at position 1 with =,flow_matching,0.3,2.0,26,117
41,replace,2.0,[,=,O==CO,O=[CO,5,replace = at position 2 with [,flow_matching,0.3,2.0,26,117
42,add,1.0,I,,O=[CO,OI=[CO,6,add I at position 1,flow_matching,0.3,2.0,26,117
43,replace,1.0,=,I,OI=[CO,O==[CO,6,replace I at position 1 with =,flow_matching,0.3,2.0,26,117
44,add,3.0,C,,O==[CO,O==C[CO,7,add C at position 3,flow_matching,0.3,2.0,26,117
45,replace,6.0,[,O,O==C[CO,O==C[C[,7,replace O at position 6 with [,flow_matching,0.3,2.0,26,117
46,add,5.0,/,,O==C[C[,O==C[/C[,8,add / at position 5,flow_matching,0.3,2.0,26,117
47,add,0.0,3,,O==C[/C[,3O==C[/C[,9,add 3 at position 0,flow_matching,0.3,2.0,26,117
48,replace,0.0,O,3,3O==C[/C[,OO==C[/C[,9,replace 3 at position 0 with O,flow_matching,0.3,2.0,26,117
49,replace,1.0,4,O,OO==C[/C[,O4==C[/C[,9,replace O at position 1 with 4,flow_matching,0.3,2.0,26,117
50,remove,6.0,/,,O4==C[/C[,O4==C[C[,8,remove / from position 6,flow_matching,0.3,2.0,26,117
51,add,2.0,s,,O4==C[C[,O4s==C[C[,9,add s at position 2,flow_matching,0.3,2.0,26,117
52,add,3.0,B,,O4s==C[C[,O4sB==C[C[,10,add B at position 3,flow_matching,0.3,2.0,26,117
53,replace,5.0,r,=,O4sB==C[C[,O4sB=rC[C[,10,replace = at position 5 with r,flow_matching,0.3,2.0,26,117
54,replace,1.0,=,4,O4sB=rC[C[,O=sB=rC[C[,10,replace 4 at position 1 with =,flow_matching,0.3,2.0,26,117
55,replace,5.0,s,r,O=sB=rC[C[,O=sB=sC[C[,10,replace r at position 5 with s,flow_matching,0.3,2.0,26,117
56,replace,9.0,I,[,O=sB=sC[C[,O=sB=sC[CI,10,replace [ at position 9 with I,flow_matching,0.3,2.0,26,117
57,replace,3.0,l,B,O=sB=sC[CI,O=sl=sC[CI,10,replace B at position 3 with l,flow_matching,0.3,2.0,26,117
58,remove,0.0,O,,O=sl=sC[CI,=sl=sC[CI,9,remove O from position 0,flow_matching,0.3,2.0,26,117
59,add,0.0,l,,=sl=sC[CI,l=sl=sC[CI,10,add l at position 0,flow_matching,0.3,2.0,26,117
60,add,3.0,1,,l=sl=sC[CI,l=s1l=sC[CI,11,add 1 at position 3,flow_matching,0.3,2.0,26,117
61,replace,0.0,O,l,l=s1l=sC[CI,O=s1l=sC[CI,11,replace l at position 0 with O,flow_matching,0.3,2.0,26,117
62,add,4.0,l,,O=s1l=sC[CI,O=s1ll=sC[CI,12,add l at position 4,flow_matching,0.3,2.0,26,117
63,replace,2.0,C,s,O=s1ll=sC[CI,O=C1ll=sC[CI,12,replace s at position 2 with C,flow_matching,0.3,2.0,26,117
64,add,4.0,/,,O=C1ll=sC[CI,O=C1/ll=sC[CI,13,add / at position 4,flow_matching,0.3,2.0,26,117
65,remove,3.0,1,,O=C1/ll=sC[CI,O=C/ll=sC[CI,12,remove 1 from position 3,flow_matching,0.3,2.0,26,117
66,add,7.0,#,,O=C/ll=sC[CI,O=C/ll=#sC[CI,13,add # at position 7,flow_matching,0.3,2.0,26,117
67,remove,3.0,/,,O=C/ll=#sC[CI,O=Cll=#sC[CI,12,remove / from position 3,flow_matching,0.3,2.0,26,117
68,remove,3.0,l,,O=Cll=#sC[CI,O=Cl=#sC[CI,11,remove l from position 3,flow_matching,0.3,2.0,26,117
69,add,6.0,n,,O=Cl=#sC[CI,O=Cl=#nsC[CI,12,add n at position 6,flow_matching,0.3,2.0,26,117
70,add,3.0,\,,O=Cl=#nsC[CI,O=C\l=#nsC[CI,13,add \ at position 3,flow_matching,0.3,2.0,26,117
71,replace,3.0,c,\,O=C\l=#nsC[CI,O=Ccl=#nsC[CI,13,replace \ at position 3 with c,flow_matching,0.3,2.0,26,117
72,replace,4.0,1,l,O=Ccl=#nsC[CI,O=Cc1=#nsC[CI,13,replace l at position 4 with 1,flow_matching,0.3,2.0,26,117
73,replace,5.0,c,=,O=Cc1=#nsC[CI,O=Cc1c#nsC[CI,13,replace = at position 5 with c,flow_matching,0.3,2.0,26,117
74,replace,6.0,c,#,O=Cc1c#nsC[CI,O=Cc1ccnsC[CI,13,replace # at position 6 with c,flow_matching,0.3,2.0,26,117
75,replace,8.0,(,s,O=Cc1ccnsC[CI,O=Cc1ccn(C[CI,13,replace s at position 8 with (,flow_matching,0.3,2.0,26,117
76,remove,10.0,[,,O=Cc1ccn(C[CI,O=Cc1ccn(CCI,12,remove [ from position 10,flow_matching,0.3,2.0,26,117
77,add,12.0,4,,O=Cc1ccn(CCI,O=Cc1ccn(CCI4,13,add 4 at position 12,flow_matching,0.3,2.0,26,117
78,add,12.0,-,,O=Cc1ccn(CCI4,O=Cc1ccn(CCI-4,14,add - at position 12,flow_matching,0.3,2.0,26,117
79,add,0.0,o,,O=Cc1ccn(CCI-4,oO=Cc1ccn(CCI-4,15,add o at position 0,flow_matching,0.3,2.0,26,117
80,add,4.0,B,,oO=Cc1ccn(CCI-4,oO=CBc1ccn(CCI-4,16,add B at position 4,flow_matching,0.3,2.0,26,117
81,remove,7.0,c,,oO=CBc1ccn(CCI-4,oO=CBc1cn(CCI-4,15,remove c from position 7,flow_matching,0.3,2.0,26,117
82,replace,0.0,O,o,oO=CBc1cn(CCI-4,OO=CBc1cn(CCI-4,15,replace o at position 0 with O,flow_matching,0.3,2.0,26,117
83,replace,8.0,=,n,OO=CBc1cn(CCI-4,OO=CBc1c=(CCI-4,15,replace n at position 8 with =,flow_matching,0.3,2.0,26,117
84,remove,0.0,O,,OO=CBc1c=(CCI-4,O=CBc1c=(CCI-4,14,remove O from position 0,flow_matching,0.3,2.0,26,117
85,replace,3.0,c,B,O=CBc1c=(CCI-4,O=Ccc1c=(CCI-4,14,replace B at position 3 with c,flow_matching,0.3,2.0,26,117
86,replace,9.0,6,C,O=Ccc1c=(CCI-4,O=Ccc1c=(6CI-4,14,replace C at position 9 with 6,flow_matching,0.3,2.0,26,117
87,replace,3.0,[,c,O=Ccc1c=(6CI-4,O=C[c1c=(6CI-4,14,replace c at position 3 with [,flow_matching,0.3,2.0,26,117
88,add,9.0,=,,O=C[c1c=(6CI-4,O=C[c1c=(=6CI-4,15,add = at position 9,flow_matching,0.3,2.0,26,117
89,replace,12.0,2,I,O=C[c1c=(=6CI-4,O=C[c1c=(=6C2-4,15,replace I at position 12 with 2,flow_matching,0.3,2.0,26,117
90,replace,14.0,H,4,O=C[c1c=(=6C2-4,O=C[c1c=(=6C2-H,15,replace 4 at position 14 with H,flow_matching,0.3,2.0,26,117
91,remove,8.0,(,,O=C[c1c=(=6C2-H,O=C[c1c==6C2-H,14,remove ( from position 8,flow_matching,0.3,2.0,26,117
92,replace,5.0,N,1,O=C[c1c==6C2-H,O=C[cNc==6C2-H,14,replace 1 at position 5 with N,flow_matching,0.3,2.0,26,117
93,replace,3.0,c,[,O=C[cNc==6C2-H,O=CccNc==6C2-H,14,replace [ at position 3 with c,flow_matching,0.3,2.0,26,117
94,add,13.0,O,,O=CccNc==6C2-H,O=CccNc==6C2-OH,15,add O at position 13,flow_matching,0.3,2.0,26,117
95,replace,14.0,7,H,O=CccNc==6C2-OH,O=CccNc==6C2-O7,15,replace H at position 14 with 7,flow_matching,0.3,2.0,26,117
96,replace,4.0,1,c,O=CccNc==6C2-O7,O=Cc1Nc==6C2-O7,15,replace c at position 4 with 1,flow_matching,0.3,2.0,26,117
97,replace,4.0,/,1,O=Cc1Nc==6C2-O7,O=Cc/Nc==6C2-O7,15,replace 1 at position 4 with /,flow_matching,0.3,2.0,26,117
98,replace,4.0,1,/,O=Cc/Nc==6C2-O7,O=Cc1Nc==6C2-O7,15,replace / at position 4 with 1,flow_matching,0.3,2.0,26,117
99,replace,5.0,c,N,O=Cc1Nc==6C2-O7,O=Cc1cc==6C2-O7,15,replace N at position 5 with c,flow_matching,0.3,2.0,26,117
100,replace,7.0,n,=,O=Cc1cc==6C2-O7,O=Cc1ccn=6C2-O7,15,replace = at position 7 with n,flow_matching,0.3,2.0,26,117
101,replace,8.0,(,=,O=Cc1ccn=6C2-O7,O=Cc1ccn(6C2-O7,15,replace = at position 8 with (,flow_matching,0.3,2.0,26,117
102,replace,9.0,-,6,O=Cc1ccn(6C2-O7,O=Cc1ccn(-C2-O7,15,replace 6 at position 9 with -,flow_matching,0.3,2.0,26,117
103,replace,10.0,c,C,O=Cc1ccn(-C2-O7,O=Cc1ccn(-c2-O7,15,replace C at position 10 with c,flow_matching,0.3,2.0,26,117
104,replace,12.0,c,-,O=Cc1ccn(-c2-O7,O=Cc1ccn(-c2cO7,15,replace - at position 12 with c,flow_matching,0.3,2.0,26,117
105,replace,13.0,c,O,O=Cc1ccn(-c2cO7,O=Cc1ccn(-c2cc7,15,replace O at position 13 with c,flow_matching,0.3,2.0,26,117
106,replace,14.0,c,7,O=Cc1ccn(-c2cc7,O=Cc1ccn(-c2ccc,15,replace 7 at position 14 with c,flow_matching,0.3,2.0,26,117
107,add,15.0,(,,O=Cc1ccn(-c2ccc,O=Cc1ccn(-c2ccc(,16,add ( at position 15,flow_matching,0.3,2.0,26,117
108,add,16.0,B,,O=Cc1ccn(-c2ccc(,O=Cc1ccn(-c2ccc(B,17,add B at position 16,flow_matching,0.3,2.0,26,117
109,add,17.0,r,,O=Cc1ccn(-c2ccc(B,O=Cc1ccn(-c2ccc(Br,18,add r at position 17,flow_matching,0.3,2.0,26,117
110,add,18.0,),,O=Cc1ccn(-c2ccc(Br,O=Cc1ccn(-c2ccc(Br),19,add ) at position 18,flow_matching,0.3,2.0,26,117
111,add,19.0,c,,O=Cc1ccn(-c2ccc(Br),O=Cc1ccn(-c2ccc(Br)c,20,add c at position 19,flow_matching,0.3,2.0,26,117
112,add,20.0,c,,O=Cc1ccn(-c2ccc(Br)c,O=Cc1ccn(-c2ccc(Br)cc,21,add c at position 20,flow_matching,0.3,2.0,26,117
113,add,21.0,2,,O=Cc1ccn(-c2ccc(Br)cc,O=Cc1ccn(-c2ccc(Br)cc2,22,add 2 at position 21,flow_matching,0.3,2.0,26,117
114,add,22.0,),,O=Cc1ccn(-c2ccc(Br)cc2,O=Cc1ccn(-c2ccc(Br)cc2),23,add ) at position 22,flow_matching,0.3,2.0,26,117
115,add,23.0,c,,O=Cc1ccn(-c2ccc(Br)cc2),O=Cc1ccn(-c2ccc(Br)cc2)c,24,add c at position 23,flow_matching,0.3,2.0,26,117
116,add,24.0,1,,O=Cc1ccn(-c2ccc(Br)cc2)c,O=Cc1ccn(-c2ccc(Br)cc2)c1,25,add 1 at position 24,flow_matching,0.3,2.0,26,117
117,add,25.0,"
",,O=Cc1ccn(-c2ccc(Br)cc2)c1,"O=Cc1ccn(-c2ccc(Br)cc2)c1
",26,"add 
 at position 25",flow_matching,0.3,2.0,26,117
