step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,41,118
1,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,41,118
2,replace,0.0,n,+,+,n,1,replace + at position 0 with n,flow_matching,0.3,2.0,41,118
3,replace,0.0,C,n,n,C,1,replace n at position 0 with C,flow_matching,0.3,2.0,41,118
4,replace,0.0,3,C,C,3,1,replace <PERSON> at position 0 with 3,flow_matching,0.3,2.0,41,118
5,add,1.0,S,,3,3S,2,add S at position 1,flow_matching,0.3,2.0,41,118
6,add,1.0,4,,3S,34S,3,add 4 at position 1,flow_matching,0.3,2.0,41,118
7,replace,0.0,C,3,34S,C4S,3,replace 3 at position 0 with C,flow_matching,0.3,2.0,41,118
8,remove,1.0,4,,C4S,CS,2,remove 4 from position 1,flow_matching,0.3,2.0,41,118
9,replace,1.0,C,S,CS,CC,2,replace S at position 1 with C,flow_matching,0.3,2.0,41,118
10,add,2.0,r,,CC,CCr,3,add r at position 2,flow_matching,0.3,2.0,41,118
11,remove,1.0,C,,CCr,Cr,2,remove C from position 1,flow_matching,0.3,2.0,41,118
12,remove,1.0,r,,Cr,C,1,remove r from position 1,flow_matching,0.3,2.0,41,118
13,replace,0.0,5,C,C,5,1,replace C at position 0 with 5,flow_matching,0.3,2.0,41,118
14,replace,0.0,],5,5,],1,replace 5 at position 0 with ],flow_matching,0.3,2.0,41,118
15,remove,0.0,],,],,0,remove ] from position 0,flow_matching,0.3,2.0,41,118
16,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,41,118
17,replace,0.0,/,],],/,1,replace ] at position 0 with /,flow_matching,0.3,2.0,41,118
18,add,1.0,[,,/,/[,2,add [ at position 1,flow_matching,0.3,2.0,41,118
19,add,0.0,\,,/[,\/[,3,add \ at position 0,flow_matching,0.3,2.0,41,118
20,replace,0.0,C,\,\/[,C/[,3,replace \ at position 0 with C,flow_matching,0.3,2.0,41,118
21,add,3.0,r,,C/[,C/[r,4,add r at position 3,flow_matching,0.3,2.0,41,118
22,remove,0.0,C,,C/[r,/[r,3,remove C from position 0,flow_matching,0.3,2.0,41,118
23,replace,0.0,C,/,/[r,C[r,3,replace / at position 0 with C,flow_matching,0.3,2.0,41,118
24,replace,1.0,C,[,C[r,CCr,3,replace [ at position 1 with C,flow_matching,0.3,2.0,41,118
25,remove,1.0,C,,CCr,Cr,2,remove C from position 1,flow_matching,0.3,2.0,41,118
26,add,1.0,/,,Cr,C/r,3,add / at position 1,flow_matching,0.3,2.0,41,118
27,replace,1.0,H,/,C/r,CHr,3,replace / at position 1 with H,flow_matching,0.3,2.0,41,118
28,add,3.0,7,,CHr,CHr7,4,add 7 at position 3,flow_matching,0.3,2.0,41,118
29,replace,2.0,[,r,CHr7,CH[7,4,replace r at position 2 with [,flow_matching,0.3,2.0,41,118
30,add,3.0,C,,CH[7,CH[C7,5,add C at position 3,flow_matching,0.3,2.0,41,118
31,replace,1.0,C,H,CH[C7,CC[C7,5,replace H at position 1 with C,flow_matching,0.3,2.0,41,118
32,replace,3.0,#,C,CC[C7,CC[#7,5,replace C at position 3 with #,flow_matching,0.3,2.0,41,118
33,replace,2.0,C,[,CC[#7,CCC#7,5,replace [ at position 2 with C,flow_matching,0.3,2.0,41,118
34,remove,4.0,7,,CCC#7,CCC#,4,remove 7 from position 4,flow_matching,0.3,2.0,41,118
35,replace,3.0,C,#,CCC#,CCCC,4,replace # at position 3 with C,flow_matching,0.3,2.0,41,118
36,add,4.0,O,,CCCC,CCCCO,5,add O at position 4,flow_matching,0.3,2.0,41,118
37,add,5.0,c,,CCCCO,CCCCOc,6,add c at position 5,flow_matching,0.3,2.0,41,118
38,add,6.0,1,,CCCCOc,CCCCOc1,7,add 1 at position 6,flow_matching,0.3,2.0,41,118
39,add,6.0,(,,CCCCOc1,CCCCOc(1,8,add ( at position 6,flow_matching,0.3,2.0,41,118
40,replace,5.0,(,c,CCCCOc(1,CCCCO((1,8,replace c at position 5 with (,flow_matching,0.3,2.0,41,118
41,add,1.0,l,,CCCCO((1,ClCCCO((1,9,add l at position 1,flow_matching,0.3,2.0,41,118
42,remove,5.0,O,,ClCCCO((1,ClCCC((1,8,remove O from position 5,flow_matching,0.3,2.0,41,118
43,add,4.0,3,,ClCCC((1,ClCC3C((1,9,add 3 at position 4,flow_matching,0.3,2.0,41,118
44,replace,7.0,3,(,ClCC3C((1,ClCC3C(31,9,replace ( at position 7 with 3,flow_matching,0.3,2.0,41,118
45,replace,1.0,C,l,ClCC3C(31,CCCC3C(31,9,replace l at position 1 with C,flow_matching,0.3,2.0,41,118
46,remove,7.0,3,,CCCC3C(31,CCCC3C(1,8,remove 3 from position 7,flow_matching,0.3,2.0,41,118
47,replace,4.0,O,3,CCCC3C(1,CCCCOC(1,8,replace 3 at position 4 with O,flow_matching,0.3,2.0,41,118
48,replace,5.0,c,C,CCCCOC(1,CCCCOc(1,8,replace C at position 5 with c,flow_matching,0.3,2.0,41,118
49,remove,0.0,C,,CCCCOc(1,CCCOc(1,7,remove C from position 0,flow_matching,0.3,2.0,41,118
50,remove,1.0,C,,CCCOc(1,CCOc(1,6,remove C from position 1,flow_matching,0.3,2.0,41,118
51,remove,5.0,1,,CCOc(1,CCOc(,5,remove 1 from position 5,flow_matching,0.3,2.0,41,118
52,add,2.0,@,,CCOc(,CC@Oc(,6,add @ at position 2,flow_matching,0.3,2.0,41,118
53,add,4.0,O,,CC@Oc(,CC@OOc(,7,add O at position 4,flow_matching,0.3,2.0,41,118
54,remove,5.0,c,,CC@OOc(,CC@OO(,6,remove c from position 5,flow_matching,0.3,2.0,41,118
55,replace,2.0,C,@,CC@OO(,CCCOO(,6,replace @ at position 2 with C,flow_matching,0.3,2.0,41,118
56,replace,3.0,C,O,CCCOO(,CCCCO(,6,replace O at position 3 with C,flow_matching,0.3,2.0,41,118
57,remove,3.0,C,,CCCCO(,CCCO(,5,remove C from position 3,flow_matching,0.3,2.0,41,118
58,replace,4.0,4,(,CCCO(,CCCO4,5,replace ( at position 4 with 4,flow_matching,0.3,2.0,41,118
59,replace,3.0,C,O,CCCO4,CCCC4,5,replace O at position 3 with C,flow_matching,0.3,2.0,41,118
60,add,4.0,c,,CCCC4,CCCCc4,6,add c at position 4,flow_matching,0.3,2.0,41,118
61,replace,4.0,O,c,CCCCc4,CCCCO4,6,replace c at position 4 with O,flow_matching,0.3,2.0,41,118
62,replace,4.0,[,O,CCCCO4,CCCC[4,6,replace O at position 4 with [,flow_matching,0.3,2.0,41,118
63,replace,4.0,O,[,CCCC[4,CCCCO4,6,replace [ at position 4 with O,flow_matching,0.3,2.0,41,118
64,replace,0.0,1,C,CCCCO4,1CCCO4,6,replace C at position 0 with 1,flow_matching,0.3,2.0,41,118
65,replace,3.0,6,C,1CCCO4,1CC6O4,6,replace C at position 3 with 6,flow_matching,0.3,2.0,41,118
66,replace,0.0,@,1,1CC6O4,@CC6O4,6,replace 1 at position 0 with @,flow_matching,0.3,2.0,41,118
67,replace,0.0,C,@,@CC6O4,CCC6O4,6,replace @ at position 0 with C,flow_matching,0.3,2.0,41,118
68,replace,3.0,-,6,CCC6O4,CCC-O4,6,replace 6 at position 3 with -,flow_matching,0.3,2.0,41,118
69,replace,1.0,s,C,CCC-O4,CsC-O4,6,replace C at position 1 with s,flow_matching,0.3,2.0,41,118
70,replace,5.0,(,4,CsC-O4,CsC-O(,6,replace 4 at position 5 with (,flow_matching,0.3,2.0,41,118
71,add,2.0,n,,CsC-O(,CsnC-O(,7,add n at position 2,flow_matching,0.3,2.0,41,118
72,replace,1.0,C,s,CsnC-O(,CCnC-O(,7,replace s at position 1 with C,flow_matching,0.3,2.0,41,118
73,replace,2.0,C,n,CCnC-O(,CCCC-O(,7,replace n at position 2 with C,flow_matching,0.3,2.0,41,118
74,remove,3.0,C,,CCCC-O(,CCC-O(,6,remove C from position 3,flow_matching,0.3,2.0,41,118
75,replace,3.0,3,-,CCC-O(,CCC3O(,6,replace - at position 3 with 3,flow_matching,0.3,2.0,41,118
76,add,1.0,=,,CCC3O(,C=CC3O(,7,add = at position 1,flow_matching,0.3,2.0,41,118
77,add,4.0,/,,C=CC3O(,C=CC/3O(,8,add / at position 4,flow_matching,0.3,2.0,41,118
78,add,4.0,O,,C=CC/3O(,C=CCO/3O(,9,add O at position 4,flow_matching,0.3,2.0,41,118
79,add,8.0,=,,C=CCO/3O(,C=CCO/3O=(,10,add = at position 8,flow_matching,0.3,2.0,41,118
80,remove,7.0,O,,C=CCO/3O=(,C=CCO/3=(,9,remove O from position 7,flow_matching,0.3,2.0,41,118
81,remove,7.0,=,,C=CCO/3=(,C=CCO/3(,8,remove = from position 7,flow_matching,0.3,2.0,41,118
82,replace,1.0,C,=,C=CCO/3(,CCCCO/3(,8,replace = at position 1 with C,flow_matching,0.3,2.0,41,118
83,replace,5.0,c,/,CCCCO/3(,CCCCOc3(,8,replace / at position 5 with c,flow_matching,0.3,2.0,41,118
84,replace,6.0,1,3,CCCCOc3(,CCCCOc1(,8,replace 3 at position 6 with 1,flow_matching,0.3,2.0,41,118
85,replace,7.0,c,(,CCCCOc1(,CCCCOc1c,8,replace ( at position 7 with c,flow_matching,0.3,2.0,41,118
86,add,8.0,c,,CCCCOc1c,CCCCOc1cc,9,add c at position 8,flow_matching,0.3,2.0,41,118
87,add,9.0,c,,CCCCOc1cc,CCCCOc1ccc,10,add c at position 9,flow_matching,0.3,2.0,41,118
88,add,10.0,c,,CCCCOc1ccc,CCCCOc1cccc,11,add c at position 10,flow_matching,0.3,2.0,41,118
89,add,11.0,c,,CCCCOc1cccc,CCCCOc1ccccc,12,add c at position 11,flow_matching,0.3,2.0,41,118
90,add,12.0,1,,CCCCOc1ccccc,CCCCOc1ccccc1,13,add 1 at position 12,flow_matching,0.3,2.0,41,118
91,add,13.0,/,,CCCCOc1ccccc1,CCCCOc1ccccc1/,14,add / at position 13,flow_matching,0.3,2.0,41,118
92,add,14.0,C,,CCCCOc1ccccc1/,CCCCOc1ccccc1/C,15,add C at position 14,flow_matching,0.3,2.0,41,118
93,add,15.0,=,,CCCCOc1ccccc1/C,CCCCOc1ccccc1/C=,16,add = at position 15,flow_matching,0.3,2.0,41,118
94,add,16.0,C,,CCCCOc1ccccc1/C=,CCCCOc1ccccc1/C=C,17,add C at position 16,flow_matching,0.3,2.0,41,118
95,add,17.0,1,,CCCCOc1ccccc1/C=C,CCCCOc1ccccc1/C=C1,18,add 1 at position 17,flow_matching,0.3,2.0,41,118
96,add,18.0,\,,CCCCOc1ccccc1/C=C1,CCCCOc1ccccc1/C=C1\,19,add \ at position 18,flow_matching,0.3,2.0,41,118
97,add,19.0,S,,CCCCOc1ccccc1/C=C1\,CCCCOc1ccccc1/C=C1\S,20,add S at position 19,flow_matching,0.3,2.0,41,118
98,add,20.0,C,,CCCCOc1ccccc1/C=C1\S,CCCCOc1ccccc1/C=C1\SC,21,add C at position 20,flow_matching,0.3,2.0,41,118
99,add,21.0,(,,CCCCOc1ccccc1/C=C1\SC,CCCCOc1ccccc1/C=C1\SC(,22,add ( at position 21,flow_matching,0.3,2.0,41,118
100,add,22.0,N,,CCCCOc1ccccc1/C=C1\SC(,CCCCOc1ccccc1/C=C1\SC(N,23,add N at position 22,flow_matching,0.3,2.0,41,118
101,add,23.0,2,,CCCCOc1ccccc1/C=C1\SC(N,CCCCOc1ccccc1/C=C1\SC(N2,24,add 2 at position 23,flow_matching,0.3,2.0,41,118
102,add,24.0,C,,CCCCOc1ccccc1/C=C1\SC(N2,CCCCOc1ccccc1/C=C1\SC(N2C,25,add C at position 24,flow_matching,0.3,2.0,41,118
103,add,25.0,C,,CCCCOc1ccccc1/C=C1\SC(N2C,CCCCOc1ccccc1/C=C1\SC(N2CC,26,add C at position 25,flow_matching,0.3,2.0,41,118
104,add,26.0,C,,CCCCOc1ccccc1/C=C1\SC(N2CC,CCCCOc1ccccc1/C=C1\SC(N2CCC,27,add C at position 26,flow_matching,0.3,2.0,41,118
105,add,27.0,(,,CCCCOc1ccccc1/C=C1\SC(N2CCC,CCCCOc1ccccc1/C=C1\SC(N2CCC(,28,add ( at position 27,flow_matching,0.3,2.0,41,118
106,add,28.0,C,,CCCCOc1ccccc1/C=C1\SC(N2CCC(,CCCCOc1ccccc1/C=C1\SC(N2CCC(C,29,add C at position 28,flow_matching,0.3,2.0,41,118
107,add,29.0,),,CCCCOc1ccccc1/C=C1\SC(N2CCC(C,CCCCOc1ccccc1/C=C1\SC(N2CCC(C),30,add ) at position 29,flow_matching,0.3,2.0,41,118
108,add,30.0,C,,CCCCOc1ccccc1/C=C1\SC(N2CCC(C),CCCCOc1ccccc1/C=C1\SC(N2CCC(C)C,31,add C at position 30,flow_matching,0.3,2.0,41,118
109,add,31.0,C,,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)C,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC,32,add C at position 31,flow_matching,0.3,2.0,41,118
110,add,32.0,2,,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2,33,add 2 at position 32,flow_matching,0.3,2.0,41,118
111,add,33.0,),,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2),34,add ) at position 33,flow_matching,0.3,2.0,41,118
112,add,34.0,=,,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2),CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=,35,add = at position 34,flow_matching,0.3,2.0,41,118
113,add,35.0,N,,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=N,36,add N at position 35,flow_matching,0.3,2.0,41,118
114,add,36.0,C,,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=N,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=NC,37,add C at position 36,flow_matching,0.3,2.0,41,118
115,add,37.0,1,,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=NC,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=NC1,38,add 1 at position 37,flow_matching,0.3,2.0,41,118
116,add,38.0,=,,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=NC1,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=NC1=,39,add = at position 38,flow_matching,0.3,2.0,41,118
117,add,39.0,O,,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=NC1=,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=NC1=O,40,add O at position 39,flow_matching,0.3,2.0,41,118
118,add,40.0,"
",,CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=NC1=O,"CCCCOc1ccccc1/C=C1\SC(N2CCC(C)CC2)=NC1=O
",41,"add 
 at position 40",flow_matching,0.3,2.0,41,118
