step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,122
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,49,122
2,replace,0.0,7,C,C,7,1,replace <PERSON> at position 0 with 7,flow_matching,0.3,2.0,49,122
3,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,49,122
4,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,49,122
5,add,0.0,l,,CO,lCO,3,add l at position 0,flow_matching,0.3,2.0,49,122
6,replace,0.0,<PERSON>,l,lCO,CCO,3,replace l at position 0 with C,flow_matching,0.3,2.0,49,122
7,add,2.0,=,,CCO,CC=O,4,add = at position 2,flow_matching,0.3,2.0,49,122
8,replace,0.0,I,C,CC=O,IC=O,4,replace C at position 0 with I,flow_matching,0.3,2.0,49,122
9,replace,0.0,C,I,IC=O,CC=O,4,replace I at position 0 with C,flow_matching,0.3,2.0,49,122
10,replace,2.0,#,=,CC=O,CC#O,4,replace = at position 2 with #,flow_matching,0.3,2.0,49,122
11,add,4.0,7,,CC#O,CC#O7,5,add 7 at position 4,flow_matching,0.3,2.0,49,122
12,remove,0.0,C,,CC#O7,C#O7,4,remove C from position 0,flow_matching,0.3,2.0,49,122
13,replace,1.0,O,#,C#O7,COO7,4,replace # at position 1 with O,flow_matching,0.3,2.0,49,122
14,add,0.0,#,,COO7,#COO7,5,add # at position 0,flow_matching,0.3,2.0,49,122
15,add,2.0,C,,#COO7,#CCOO7,6,add C at position 2,flow_matching,0.3,2.0,49,122
16,replace,1.0,F,C,#CCOO7,#FCOO7,6,replace C at position 1 with F,flow_matching,0.3,2.0,49,122
17,remove,3.0,O,,#FCOO7,#FCO7,5,remove O from position 3,flow_matching,0.3,2.0,49,122
18,replace,4.0,N,7,#FCO7,#FCON,5,replace 7 at position 4 with N,flow_matching,0.3,2.0,49,122
19,remove,4.0,N,,#FCON,#FCO,4,remove N from position 4,flow_matching,0.3,2.0,49,122
20,replace,0.0,[,#,#FCO,[FCO,4,replace # at position 0 with [,flow_matching,0.3,2.0,49,122
21,replace,0.0,C,[,[FCO,CFCO,4,replace [ at position 0 with C,flow_matching,0.3,2.0,49,122
22,replace,3.0,@,O,CFCO,CFC@,4,replace O at position 3 with @,flow_matching,0.3,2.0,49,122
23,remove,3.0,@,,CFC@,CFC,3,remove @ from position 3,flow_matching,0.3,2.0,49,122
24,add,2.0,1,,CFC,CF1C,4,add 1 at position 2,flow_matching,0.3,2.0,49,122
25,replace,0.0,/,C,CF1C,/F1C,4,replace C at position 0 with /,flow_matching,0.3,2.0,49,122
26,replace,0.0,C,/,/F1C,CF1C,4,replace / at position 0 with C,flow_matching,0.3,2.0,49,122
27,add,4.0,N,,CF1C,CF1CN,5,add N at position 4,flow_matching,0.3,2.0,49,122
28,remove,4.0,N,,CF1CN,CF1C,4,remove N from position 4,flow_matching,0.3,2.0,49,122
29,replace,3.0,=,C,CF1C,CF1=,4,replace C at position 3 with =,flow_matching,0.3,2.0,49,122
30,remove,3.0,=,,CF1=,CF1,3,remove = from position 3,flow_matching,0.3,2.0,49,122
31,replace,0.0,r,C,CF1,rF1,3,replace C at position 0 with r,flow_matching,0.3,2.0,49,122
32,remove,0.0,r,,rF1,F1,2,remove r from position 0,flow_matching,0.3,2.0,49,122
33,replace,0.0,s,F,F1,s1,2,replace F at position 0 with s,flow_matching,0.3,2.0,49,122
34,remove,1.0,1,,s1,s,1,remove 1 from position 1,flow_matching,0.3,2.0,49,122
35,replace,0.0,C,s,s,C,1,replace s at position 0 with C,flow_matching,0.3,2.0,49,122
36,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,49,122
37,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,49,122
38,add,1.0,@,,O,O@,2,add @ at position 1,flow_matching,0.3,2.0,49,122
39,replace,0.0,C,O,O@,C@,2,replace O at position 0 with C,flow_matching,0.3,2.0,49,122
40,replace,1.0,O,@,C@,CO,2,replace @ at position 1 with O,flow_matching,0.3,2.0,49,122
41,add,0.0,3,,CO,3CO,3,add 3 at position 0,flow_matching,0.3,2.0,49,122
42,add,0.0,H,,3CO,H3CO,4,add H at position 0,flow_matching,0.3,2.0,49,122
43,add,3.0,@,,H3CO,H3C@O,5,add @ at position 3,flow_matching,0.3,2.0,49,122
44,add,1.0,H,,H3C@O,HH3C@O,6,add H at position 1,flow_matching,0.3,2.0,49,122
45,remove,0.0,H,,HH3C@O,H3C@O,5,remove H from position 0,flow_matching,0.3,2.0,49,122
46,replace,0.0,C,H,H3C@O,C3C@O,5,replace H at position 0 with C,flow_matching,0.3,2.0,49,122
47,replace,4.0,C,O,C3C@O,C3C@C,5,replace O at position 4 with C,flow_matching,0.3,2.0,49,122
48,replace,2.0,7,C,C3C@C,C37@C,5,replace C at position 2 with 7,flow_matching,0.3,2.0,49,122
49,replace,3.0,C,@,C37@C,C37CC,5,replace @ at position 3 with C,flow_matching,0.3,2.0,49,122
50,add,3.0,s,,C37CC,C37sCC,6,add s at position 3,flow_matching,0.3,2.0,49,122
51,remove,2.0,7,,C37sCC,C3sCC,5,remove 7 from position 2,flow_matching,0.3,2.0,49,122
52,add,1.0,(,,C3sCC,C(3sCC,6,add ( at position 1,flow_matching,0.3,2.0,49,122
53,replace,1.0,/,(,C(3sCC,C/3sCC,6,replace ( at position 1 with /,flow_matching,0.3,2.0,49,122
54,replace,1.0,S,/,C/3sCC,CS3sCC,6,replace / at position 1 with S,flow_matching,0.3,2.0,49,122
55,add,2.0,O,,CS3sCC,CSO3sCC,7,add O at position 2,flow_matching,0.3,2.0,49,122
56,replace,3.0,N,3,CSO3sCC,CSONsCC,7,replace 3 at position 3 with N,flow_matching,0.3,2.0,49,122
57,remove,3.0,N,,CSONsCC,CSOsCC,6,remove N from position 3,flow_matching,0.3,2.0,49,122
58,replace,1.0,O,S,CSOsCC,COOsCC,6,replace S at position 1 with O,flow_matching,0.3,2.0,49,122
59,add,0.0,3,,COOsCC,3COOsCC,7,add 3 at position 0,flow_matching,0.3,2.0,49,122
60,add,3.0,B,,3COOsCC,3COBOsCC,8,add B at position 3,flow_matching,0.3,2.0,49,122
61,remove,2.0,O,,3COBOsCC,3CBOsCC,7,remove O from position 2,flow_matching,0.3,2.0,49,122
62,add,2.0,@,,3CBOsCC,3C@BOsCC,8,add @ at position 2,flow_matching,0.3,2.0,49,122
63,remove,2.0,@,,3C@BOsCC,3CBOsCC,7,remove @ from position 2,flow_matching,0.3,2.0,49,122
64,replace,1.0,=,C,3CBOsCC,3=BOsCC,7,replace C at position 1 with =,flow_matching,0.3,2.0,49,122
65,remove,4.0,s,,3=BOsCC,3=BOCC,6,remove s from position 4,flow_matching,0.3,2.0,49,122
66,replace,0.0,C,3,3=BOCC,C=BOCC,6,replace 3 at position 0 with C,flow_matching,0.3,2.0,49,122
67,replace,1.0,O,=,C=BOCC,COBOCC,6,replace = at position 1 with O,flow_matching,0.3,2.0,49,122
68,remove,0.0,C,,COBOCC,OBOCC,5,remove C from position 0,flow_matching,0.3,2.0,49,122
69,add,0.0,=,,OBOCC,=OBOCC,6,add = at position 0,flow_matching,0.3,2.0,49,122
70,remove,4.0,C,,=OBOCC,=OBOC,5,remove C from position 4,flow_matching,0.3,2.0,49,122
71,replace,0.0,o,=,=OBOC,oOBOC,5,replace = at position 0 with o,flow_matching,0.3,2.0,49,122
72,add,2.0,3,,oOBOC,oO3BOC,6,add 3 at position 2,flow_matching,0.3,2.0,49,122
73,replace,0.0,C,o,oO3BOC,CO3BOC,6,replace o at position 0 with C,flow_matching,0.3,2.0,49,122
74,add,1.0,-,,CO3BOC,C-O3BOC,7,add - at position 1,flow_matching,0.3,2.0,49,122
75,replace,1.0,O,-,C-O3BOC,COO3BOC,7,replace - at position 1 with O,flow_matching,0.3,2.0,49,122
76,replace,2.0,c,O,COO3BOC,COc3BOC,7,replace O at position 2 with c,flow_matching,0.3,2.0,49,122
77,replace,3.0,1,3,COc3BOC,COc1BOC,7,replace 3 at position 3 with 1,flow_matching,0.3,2.0,49,122
78,replace,4.0,c,B,COc1BOC,COc1cOC,7,replace B at position 4 with c,flow_matching,0.3,2.0,49,122
79,replace,5.0,c,O,COc1cOC,COc1ccC,7,replace O at position 5 with c,flow_matching,0.3,2.0,49,122
80,replace,6.0,(,C,COc1ccC,COc1cc(,7,replace C at position 6 with (,flow_matching,0.3,2.0,49,122
81,add,7.0,[,,COc1cc(,COc1cc([,8,add [ at position 7,flow_matching,0.3,2.0,49,122
82,add,8.0,C,,COc1cc([,COc1cc([C,9,add C at position 8,flow_matching,0.3,2.0,49,122
83,add,9.0,@,,COc1cc([C,COc1cc([C@,10,add @ at position 9,flow_matching,0.3,2.0,49,122
84,add,10.0,@,,COc1cc([C@,COc1cc([C@@,11,add @ at position 10,flow_matching,0.3,2.0,49,122
85,add,11.0,H,,COc1cc([C@@,COc1cc([C@@H,12,add H at position 11,flow_matching,0.3,2.0,49,122
86,add,12.0,],,COc1cc([C@@H,COc1cc([C@@H],13,add ] at position 12,flow_matching,0.3,2.0,49,122
87,add,13.0,2,,COc1cc([C@@H],COc1cc([C@@H]2,14,add 2 at position 13,flow_matching,0.3,2.0,49,122
88,add,14.0,C,,COc1cc([C@@H]2,COc1cc([C@@H]2C,15,add C at position 14,flow_matching,0.3,2.0,49,122
89,add,15.0,C,,COc1cc([C@@H]2C,COc1cc([C@@H]2CC,16,add C at position 15,flow_matching,0.3,2.0,49,122
90,add,16.0,(,,COc1cc([C@@H]2CC,COc1cc([C@@H]2CC(,17,add ( at position 16,flow_matching,0.3,2.0,49,122
91,add,17.0,=,,COc1cc([C@@H]2CC(,COc1cc([C@@H]2CC(=,18,add = at position 17,flow_matching,0.3,2.0,49,122
92,add,18.0,O,,COc1cc([C@@H]2CC(=,COc1cc([C@@H]2CC(=O,19,add O at position 18,flow_matching,0.3,2.0,49,122
93,add,19.0,),,COc1cc([C@@H]2CC(=O,COc1cc([C@@H]2CC(=O),20,add ) at position 19,flow_matching,0.3,2.0,49,122
94,add,20.0,N,,COc1cc([C@@H]2CC(=O),COc1cc([C@@H]2CC(=O)N,21,add N at position 20,flow_matching,0.3,2.0,49,122
95,add,21.0,c,,COc1cc([C@@H]2CC(=O)N,COc1cc([C@@H]2CC(=O)Nc,22,add c at position 21,flow_matching,0.3,2.0,49,122
96,add,22.0,3,,COc1cc([C@@H]2CC(=O)Nc,COc1cc([C@@H]2CC(=O)Nc3,23,add 3 at position 22,flow_matching,0.3,2.0,49,122
97,add,23.0,c,,COc1cc([C@@H]2CC(=O)Nc3,COc1cc([C@@H]2CC(=O)Nc3c,24,add c at position 23,flow_matching,0.3,2.0,49,122
98,add,24.0,2,,COc1cc([C@@H]2CC(=O)Nc3c,COc1cc([C@@H]2CC(=O)Nc3c2,25,add 2 at position 24,flow_matching,0.3,2.0,49,122
99,add,25.0,c,,COc1cc([C@@H]2CC(=O)Nc3c2,COc1cc([C@@H]2CC(=O)Nc3c2c,26,add c at position 25,flow_matching,0.3,2.0,49,122
100,add,26.0,n,,COc1cc([C@@H]2CC(=O)Nc3c2c,COc1cc([C@@H]2CC(=O)Nc3c2cn,27,add n at position 26,flow_matching,0.3,2.0,49,122
101,add,27.0,n,,COc1cc([C@@H]2CC(=O)Nc3c2cn,COc1cc([C@@H]2CC(=O)Nc3c2cnn,28,add n at position 27,flow_matching,0.3,2.0,49,122
102,add,28.0,3,,COc1cc([C@@H]2CC(=O)Nc3c2cnn,COc1cc([C@@H]2CC(=O)Nc3c2cnn3,29,add 3 at position 28,flow_matching,0.3,2.0,49,122
103,add,29.0,C,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3,COc1cc([C@@H]2CC(=O)Nc3c2cnn3C,30,add C at position 29,flow_matching,0.3,2.0,49,122
104,add,30.0,c,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3C,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc,31,add c at position 30,flow_matching,0.3,2.0,49,122
105,add,31.0,2,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2,32,add 2 at position 31,flow_matching,0.3,2.0,49,122
106,add,32.0,c,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2c,33,add c at position 32,flow_matching,0.3,2.0,49,122
107,add,33.0,c,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2c,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cc,34,add c at position 33,flow_matching,0.3,2.0,49,122
108,add,34.0,c,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cc,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2ccc,35,add c at position 34,flow_matching,0.3,2.0,49,122
109,add,35.0,n,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2ccc,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccn,36,add n at position 35,flow_matching,0.3,2.0,49,122
110,add,36.0,c,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccn,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc,37,add c at position 36,flow_matching,0.3,2.0,49,122
111,add,37.0,2,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2,38,add 2 at position 37,flow_matching,0.3,2.0,49,122
112,add,38.0,),,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2),39,add ) at position 38,flow_matching,0.3,2.0,49,122
113,add,39.0,c,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2),COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)c,40,add c at position 39,flow_matching,0.3,2.0,49,122
114,add,40.0,c,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)c,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc,41,add c at position 40,flow_matching,0.3,2.0,49,122
115,add,41.0,2,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2,42,add 2 at position 41,flow_matching,0.3,2.0,49,122
116,add,42.0,c,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c,43,add c at position 42,flow_matching,0.3,2.0,49,122
117,add,43.0,1,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1,44,add 1 at position 43,flow_matching,0.3,2.0,49,122
118,add,44.0,O,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1O,45,add O at position 44,flow_matching,0.3,2.0,49,122
119,add,45.0,C,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1O,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1OC,46,add C at position 45,flow_matching,0.3,2.0,49,122
120,add,46.0,O,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1OC,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1OCO,47,add O at position 46,flow_matching,0.3,2.0,49,122
121,add,47.0,2,,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1OCO,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1OCO2,48,add 2 at position 47,flow_matching,0.3,2.0,49,122
122,add,48.0,"
",,COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1OCO2,"COc1cc([C@@H]2CC(=O)Nc3c2cnn3Cc2cccnc2)cc2c1OCO2
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,122
