step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,55,197
1,add,0.0,5,,,5,1,add 5 at position 0,flow_matching,0.3,2.0,55,197
2,add,1.0,1,,5,51,2,add 1 at position 1,flow_matching,0.3,2.0,55,197
3,replace,0.0,F,5,51,F1,2,replace 5 at position 0 with F,flow_matching,0.3,2.0,55,197
4,remove,0.0,F,,F1,1,1,remove F from position 0,flow_matching,0.3,2.0,55,197
5,remove,0.0,1,,1,,0,remove 1 from position 0,flow_matching,0.3,2.0,55,197
6,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,55,197
7,remove,0.0,F,,F,,0,remove F from position 0,flow_matching,0.3,2.0,55,197
8,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,55,197
9,add,1.0,c,,F,Fc,2,add c at position 1,flow_matching,0.3,2.0,55,197
10,add,0.0,F,,Fc,FFc,3,add F at position 0,flow_matching,0.3,2.0,55,197
11,add,1.0,o,,FFc,FoFc,4,add o at position 1,flow_matching,0.3,2.0,55,197
12,replace,0.0,1,F,FoFc,1oFc,4,replace F at position 0 with 1,flow_matching,0.3,2.0,55,197
13,add,2.0,H,,1oFc,1oHFc,5,add H at position 2,flow_matching,0.3,2.0,55,197
14,add,3.0,n,,1oHFc,1oHnFc,6,add n at position 3,flow_matching,0.3,2.0,55,197
15,replace,0.0,F,1,1oHnFc,FoHnFc,6,replace 1 at position 0 with F,flow_matching,0.3,2.0,55,197
16,add,0.0,-,,FoHnFc,-FoHnFc,7,add - at position 0,flow_matching,0.3,2.0,55,197
17,replace,0.0,F,-,-FoHnFc,FFoHnFc,7,replace - at position 0 with F,flow_matching,0.3,2.0,55,197
18,replace,2.0,l,o,FFoHnFc,FFlHnFc,7,replace o at position 2 with l,flow_matching,0.3,2.0,55,197
19,replace,2.0,H,l,FFlHnFc,FFHHnFc,7,replace l at position 2 with H,flow_matching,0.3,2.0,55,197
20,replace,1.0,c,F,FFHHnFc,FcHHnFc,7,replace F at position 1 with c,flow_matching,0.3,2.0,55,197
21,add,2.0,l,,FcHHnFc,FclHHnFc,8,add l at position 2,flow_matching,0.3,2.0,55,197
22,replace,2.0,1,l,FclHHnFc,Fc1HHnFc,8,replace l at position 2 with 1,flow_matching,0.3,2.0,55,197
23,add,1.0,-,,Fc1HHnFc,F-c1HHnFc,9,add - at position 1,flow_matching,0.3,2.0,55,197
24,replace,0.0,H,F,F-c1HHnFc,H-c1HHnFc,9,replace F at position 0 with H,flow_matching,0.3,2.0,55,197
25,replace,1.0,C,-,H-c1HHnFc,HCc1HHnFc,9,replace - at position 1 with C,flow_matching,0.3,2.0,55,197
26,replace,2.0,(,c,HCc1HHnFc,HC(1HHnFc,9,replace c at position 2 with (,flow_matching,0.3,2.0,55,197
27,replace,1.0,N,C,HC(1HHnFc,HN(1HHnFc,9,replace C at position 1 with N,flow_matching,0.3,2.0,55,197
28,remove,0.0,H,,HN(1HHnFc,N(1HHnFc,8,remove H from position 0,flow_matching,0.3,2.0,55,197
29,remove,2.0,1,,N(1HHnFc,N(HHnFc,7,remove 1 from position 2,flow_matching,0.3,2.0,55,197
30,remove,2.0,H,,N(HHnFc,N(HnFc,6,remove H from position 2,flow_matching,0.3,2.0,55,197
31,replace,0.0,F,N,N(HnFc,F(HnFc,6,replace N at position 0 with F,flow_matching,0.3,2.0,55,197
32,add,3.0,5,,F(HnFc,F(H5nFc,7,add 5 at position 3,flow_matching,0.3,2.0,55,197
33,replace,1.0,c,(,F(H5nFc,FcH5nFc,7,replace ( at position 1 with c,flow_matching,0.3,2.0,55,197
34,add,6.0,N,,FcH5nFc,FcH5nFNc,8,add N at position 6,flow_matching,0.3,2.0,55,197
35,replace,7.0,(,c,FcH5nFNc,FcH5nFN(,8,replace c at position 7 with (,flow_matching,0.3,2.0,55,197
36,add,4.0,o,,FcH5nFN(,FcH5onFN(,9,add o at position 4,flow_matching,0.3,2.0,55,197
37,remove,2.0,H,,FcH5onFN(,Fc5onFN(,8,remove H from position 2,flow_matching,0.3,2.0,55,197
38,add,8.0,=,,Fc5onFN(,Fc5onFN(=,9,add = at position 8,flow_matching,0.3,2.0,55,197
39,add,6.0,B,,Fc5onFN(=,Fc5onFBN(=,10,add B at position 6,flow_matching,0.3,2.0,55,197
40,remove,0.0,F,,Fc5onFBN(=,c5onFBN(=,9,remove F from position 0,flow_matching,0.3,2.0,55,197
41,add,0.0,S,,c5onFBN(=,Sc5onFBN(=,10,add S at position 0,flow_matching,0.3,2.0,55,197
42,replace,9.0,B,=,Sc5onFBN(=,Sc5onFBN(B,10,replace = at position 9 with B,flow_matching,0.3,2.0,55,197
43,remove,6.0,B,,Sc5onFBN(B,Sc5onFN(B,9,remove B from position 6,flow_matching,0.3,2.0,55,197
44,replace,0.0,F,S,Sc5onFN(B,Fc5onFN(B,9,replace S at position 0 with F,flow_matching,0.3,2.0,55,197
45,remove,2.0,5,,Fc5onFN(B,FconFN(B,8,remove 5 from position 2,flow_matching,0.3,2.0,55,197
46,replace,2.0,1,o,FconFN(B,Fc1nFN(B,8,replace o at position 2 with 1,flow_matching,0.3,2.0,55,197
47,add,7.0,6,,Fc1nFN(B,Fc1nFN(6B,9,add 6 at position 7,flow_matching,0.3,2.0,55,197
48,replace,0.0,[,F,Fc1nFN(6B,[c1nFN(6B,9,replace F at position 0 with [,flow_matching,0.3,2.0,55,197
49,add,5.0,),,[c1nFN(6B,[c1nF)N(6B,10,add ) at position 5,flow_matching,0.3,2.0,55,197
50,replace,6.0,=,N,[c1nF)N(6B,[c1nF)=(6B,10,replace N at position 6 with =,flow_matching,0.3,2.0,55,197
51,replace,8.0,1,6,[c1nF)=(6B,[c1nF)=(1B,10,replace 6 at position 8 with 1,flow_matching,0.3,2.0,55,197
52,remove,9.0,B,,[c1nF)=(1B,[c1nF)=(1,9,remove B from position 9,flow_matching,0.3,2.0,55,197
53,replace,2.0,\,1,[c1nF)=(1,[c\nF)=(1,9,replace 1 at position 2 with \,flow_matching,0.3,2.0,55,197
54,replace,0.0,F,[,[c\nF)=(1,Fc\nF)=(1,9,replace [ at position 0 with F,flow_matching,0.3,2.0,55,197
55,replace,2.0,1,\,Fc\nF)=(1,Fc1nF)=(1,9,replace \ at position 2 with 1,flow_matching,0.3,2.0,55,197
56,replace,7.0,S,(,Fc1nF)=(1,Fc1nF)=S1,9,replace ( at position 7 with S,flow_matching,0.3,2.0,55,197
57,replace,3.0,c,n,Fc1nF)=S1,Fc1cF)=S1,9,replace n at position 3 with c,flow_matching,0.3,2.0,55,197
58,add,4.0,(,,Fc1cF)=S1,Fc1c(F)=S1,10,add ( at position 4,flow_matching,0.3,2.0,55,197
59,replace,4.0,c,(,Fc1c(F)=S1,Fc1ccF)=S1,10,replace ( at position 4 with c,flow_matching,0.3,2.0,55,197
60,remove,7.0,=,,Fc1ccF)=S1,Fc1ccF)S1,9,remove = from position 7,flow_matching,0.3,2.0,55,197
61,add,2.0,4,,Fc1ccF)S1,Fc41ccF)S1,10,add 4 at position 2,flow_matching,0.3,2.0,55,197
62,replace,2.0,1,4,Fc41ccF)S1,Fc11ccF)S1,10,replace 4 at position 2 with 1,flow_matching,0.3,2.0,55,197
63,add,6.0,4,,Fc11ccF)S1,Fc11cc4F)S1,11,add 4 at position 6,flow_matching,0.3,2.0,55,197
64,replace,6.0,\,4,Fc11cc4F)S1,Fc11cc\F)S1,11,replace 4 at position 6 with \,flow_matching,0.3,2.0,55,197
65,replace,3.0,c,1,Fc11cc\F)S1,Fc1ccc\F)S1,11,replace 1 at position 3 with c,flow_matching,0.3,2.0,55,197
66,replace,4.0,[,c,Fc1ccc\F)S1,Fc1c[c\F)S1,11,replace c at position 4 with [,flow_matching,0.3,2.0,55,197
67,replace,4.0,c,[,Fc1c[c\F)S1,Fc1ccc\F)S1,11,replace [ at position 4 with c,flow_matching,0.3,2.0,55,197
68,replace,7.0,c,F,Fc1ccc\F)S1,Fc1ccc\c)S1,11,replace F at position 7 with c,flow_matching,0.3,2.0,55,197
69,replace,6.0,c,\,Fc1ccc\c)S1,Fc1ccccc)S1,11,replace \ at position 6 with c,flow_matching,0.3,2.0,55,197
70,replace,9.0,n,S,Fc1ccccc)S1,Fc1ccccc)n1,11,replace S at position 9 with n,flow_matching,0.3,2.0,55,197
71,add,8.0,o,,Fc1ccccc)n1,Fc1ccccco)n1,12,add o at position 8,flow_matching,0.3,2.0,55,197
72,remove,10.0,n,,Fc1ccccco)n1,Fc1ccccco)1,11,remove n from position 10,flow_matching,0.3,2.0,55,197
73,add,0.0,-,,Fc1ccccco)1,-Fc1ccccco)1,12,add - at position 0,flow_matching,0.3,2.0,55,197
74,replace,3.0,B,1,-Fc1ccccco)1,-FcBccccco)1,12,replace 1 at position 3 with B,flow_matching,0.3,2.0,55,197
75,remove,2.0,c,,-FcBccccco)1,-FBccccco)1,11,remove c from position 2,flow_matching,0.3,2.0,55,197
76,replace,2.0,@,B,-FBccccco)1,-F@ccccco)1,11,replace B at position 2 with @,flow_matching,0.3,2.0,55,197
77,replace,0.0,F,-,-F@ccccco)1,FF@ccccco)1,11,replace - at position 0 with F,flow_matching,0.3,2.0,55,197
78,add,8.0,\,,FF@ccccco)1,FF@ccccc\o)1,12,add \ at position 8,flow_matching,0.3,2.0,55,197
79,replace,1.0,c,F,FF@ccccc\o)1,Fc@ccccc\o)1,12,replace F at position 1 with c,flow_matching,0.3,2.0,55,197
80,replace,2.0,1,@,Fc@ccccc\o)1,Fc1ccccc\o)1,12,replace @ at position 2 with 1,flow_matching,0.3,2.0,55,197
81,replace,8.0,1,\,Fc1ccccc\o)1,Fc1ccccc1o)1,12,replace \ at position 8 with 1,flow_matching,0.3,2.0,55,197
82,replace,10.0,B,),Fc1ccccc1o)1,Fc1ccccc1oB1,12,replace ) at position 10 with B,flow_matching,0.3,2.0,55,197
83,add,11.0,(,,Fc1ccccc1oB1,Fc1ccccc1oB(1,13,add ( at position 11,flow_matching,0.3,2.0,55,197
84,replace,2.0,-,1,Fc1ccccc1oB(1,Fc-ccccc1oB(1,13,replace 1 at position 2 with -,flow_matching,0.3,2.0,55,197
85,replace,2.0,1,-,Fc-ccccc1oB(1,Fc1ccccc1oB(1,13,replace - at position 2 with 1,flow_matching,0.3,2.0,55,197
86,replace,9.0,[,o,Fc1ccccc1oB(1,Fc1ccccc1[B(1,13,replace o at position 9 with [,flow_matching,0.3,2.0,55,197
87,remove,10.0,B,,Fc1ccccc1[B(1,Fc1ccccc1[(1,12,remove B from position 10,flow_matching,0.3,2.0,55,197
88,add,0.0,@,,Fc1ccccc1[(1,@Fc1ccccc1[(1,13,add @ at position 0,flow_matching,0.3,2.0,55,197
89,replace,0.0,l,@,@Fc1ccccc1[(1,lFc1ccccc1[(1,13,replace @ at position 0 with l,flow_matching,0.3,2.0,55,197
90,add,1.0,#,,lFc1ccccc1[(1,l#Fc1ccccc1[(1,14,add # at position 1,flow_matching,0.3,2.0,55,197
91,replace,0.0,F,l,l#Fc1ccccc1[(1,F#Fc1ccccc1[(1,14,replace l at position 0 with F,flow_matching,0.3,2.0,55,197
92,replace,1.0,c,#,F#Fc1ccccc1[(1,FcFc1ccccc1[(1,14,replace # at position 1 with c,flow_matching,0.3,2.0,55,197
93,add,12.0,4,,FcFc1ccccc1[(1,FcFc1ccccc1[4(1,15,add 4 at position 12,flow_matching,0.3,2.0,55,197
94,remove,8.0,c,,FcFc1ccccc1[4(1,FcFc1cccc1[4(1,14,remove c from position 8,flow_matching,0.3,2.0,55,197
95,add,9.0,F,,FcFc1cccc1[4(1,FcFc1ccccF1[4(1,15,add F at position 9,flow_matching,0.3,2.0,55,197
96,replace,2.0,n,F,FcFc1ccccF1[4(1,Fcnc1ccccF1[4(1,15,replace F at position 2 with n,flow_matching,0.3,2.0,55,197
97,replace,2.0,F,n,Fcnc1ccccF1[4(1,FcFc1ccccF1[4(1,15,replace n at position 2 with F,flow_matching,0.3,2.0,55,197
98,replace,6.0,],c,FcFc1ccccF1[4(1,FcFc1c]ccF1[4(1,15,replace c at position 6 with ],flow_matching,0.3,2.0,55,197
99,remove,9.0,F,,FcFc1c]ccF1[4(1,FcFc1c]cc1[4(1,14,remove F from position 9,flow_matching,0.3,2.0,55,197
100,replace,2.0,1,F,FcFc1c]cc1[4(1,Fc1c1c]cc1[4(1,14,replace F at position 2 with 1,flow_matching,0.3,2.0,55,197
101,add,0.0,5,,Fc1c1c]cc1[4(1,5Fc1c1c]cc1[4(1,15,add 5 at position 0,flow_matching,0.3,2.0,55,197
102,add,3.0,#,,5Fc1c1c]cc1[4(1,5Fc#1c1c]cc1[4(1,16,add # at position 3,flow_matching,0.3,2.0,55,197
103,replace,1.0,4,F,5Fc#1c1c]cc1[4(1,54c#1c1c]cc1[4(1,16,replace F at position 1 with 4,flow_matching,0.3,2.0,55,197
104,add,1.0,7,,54c#1c1c]cc1[4(1,574c#1c1c]cc1[4(1,17,add 7 at position 1,flow_matching,0.3,2.0,55,197
105,replace,0.0,F,5,574c#1c1c]cc1[4(1,F74c#1c1c]cc1[4(1,17,replace 5 at position 0 with F,flow_matching,0.3,2.0,55,197
106,replace,1.0,+,7,F74c#1c1c]cc1[4(1,F+4c#1c1c]cc1[4(1,17,replace 7 at position 1 with +,flow_matching,0.3,2.0,55,197
107,remove,4.0,#,,F+4c#1c1c]cc1[4(1,F+4c1c1c]cc1[4(1,16,remove # from position 4,flow_matching,0.3,2.0,55,197
108,replace,1.0,c,+,F+4c1c1c]cc1[4(1,Fc4c1c1c]cc1[4(1,16,replace + at position 1 with c,flow_matching,0.3,2.0,55,197
109,replace,5.0,5,c,Fc4c1c1c]cc1[4(1,Fc4c151c]cc1[4(1,16,replace c at position 5 with 5,flow_matching,0.3,2.0,55,197
110,replace,13.0,S,4,Fc4c151c]cc1[4(1,Fc4c151c]cc1[S(1,16,replace 4 at position 13 with S,flow_matching,0.3,2.0,55,197
111,add,12.0,5,,Fc4c151c]cc1[S(1,Fc4c151c]cc15[S(1,17,add 5 at position 12,flow_matching,0.3,2.0,55,197
112,replace,2.0,1,4,Fc4c151c]cc15[S(1,Fc1c151c]cc15[S(1,17,replace 4 at position 2 with 1,flow_matching,0.3,2.0,55,197
113,replace,11.0,r,1,Fc1c151c]cc15[S(1,Fc1c151c]ccr5[S(1,17,replace 1 at position 11 with r,flow_matching,0.3,2.0,55,197
114,remove,6.0,1,,Fc1c151c]ccr5[S(1,Fc1c15c]ccr5[S(1,16,remove 1 from position 6,flow_matching,0.3,2.0,55,197
115,add,1.0,F,,Fc1c15c]ccr5[S(1,FFc1c15c]ccr5[S(1,17,add F at position 1,flow_matching,0.3,2.0,55,197
116,remove,5.0,1,,FFc1c15c]ccr5[S(1,FFc1c5c]ccr5[S(1,16,remove 1 from position 5,flow_matching,0.3,2.0,55,197
117,remove,7.0,],,FFc1c5c]ccr5[S(1,FFc1c5cccr5[S(1,15,remove ] from position 7,flow_matching,0.3,2.0,55,197
118,add,5.0,N,,FFc1c5cccr5[S(1,FFc1cN5cccr5[S(1,16,add N at position 5,flow_matching,0.3,2.0,55,197
119,remove,7.0,c,,FFc1cN5cccr5[S(1,FFc1cN5ccr5[S(1,15,remove c from position 7,flow_matching,0.3,2.0,55,197
120,add,0.0,n,,FFc1cN5ccr5[S(1,nFFc1cN5ccr5[S(1,16,add n at position 0,flow_matching,0.3,2.0,55,197
121,replace,0.0,F,n,nFFc1cN5ccr5[S(1,FFFc1cN5ccr5[S(1,16,replace n at position 0 with F,flow_matching,0.3,2.0,55,197
122,replace,1.0,c,F,FFFc1cN5ccr5[S(1,FcFc1cN5ccr5[S(1,16,replace F at position 1 with c,flow_matching,0.3,2.0,55,197
123,add,11.0,S,,FcFc1cN5ccr5[S(1,FcFc1cN5ccrS5[S(1,17,add S at position 11,flow_matching,0.3,2.0,55,197
124,replace,9.0,(,c,FcFc1cN5ccrS5[S(1,FcFc1cN5c(rS5[S(1,17,replace c at position 9 with (,flow_matching,0.3,2.0,55,197
125,replace,2.0,1,F,FcFc1cN5c(rS5[S(1,Fc1c1cN5c(rS5[S(1,17,replace F at position 2 with 1,flow_matching,0.3,2.0,55,197
126,remove,12.0,5,,Fc1c1cN5c(rS5[S(1,Fc1c1cN5c(rS[S(1,16,remove 5 from position 12,flow_matching,0.3,2.0,55,197
127,add,15.0,],,Fc1c1cN5c(rS[S(1,Fc1c1cN5c(rS[S(]1,17,add ] at position 15,flow_matching,0.3,2.0,55,197
128,replace,4.0,c,1,Fc1c1cN5c(rS[S(]1,Fc1cccN5c(rS[S(]1,17,replace 1 at position 4 with c,flow_matching,0.3,2.0,55,197
129,replace,6.0,c,N,Fc1cccN5c(rS[S(]1,Fc1cccc5c(rS[S(]1,17,replace N at position 6 with c,flow_matching,0.3,2.0,55,197
130,add,2.0,#,,Fc1cccc5c(rS[S(]1,Fc#1cccc5c(rS[S(]1,18,add # at position 2,flow_matching,0.3,2.0,55,197
131,add,5.0,s,,Fc#1cccc5c(rS[S(]1,Fc#1csccc5c(rS[S(]1,19,add s at position 5,flow_matching,0.3,2.0,55,197
132,replace,9.0,=,5,Fc#1csccc5c(rS[S(]1,Fc#1csccc=c(rS[S(]1,19,replace 5 at position 9 with =,flow_matching,0.3,2.0,55,197
133,remove,12.0,r,,Fc#1csccc=c(rS[S(]1,Fc#1csccc=c(S[S(]1,18,remove r from position 12,flow_matching,0.3,2.0,55,197
134,add,8.0,4,,Fc#1csccc=c(S[S(]1,Fc#1cscc4c=c(S[S(]1,19,add 4 at position 8,flow_matching,0.3,2.0,55,197
135,remove,11.0,c,,Fc#1cscc4c=c(S[S(]1,Fc#1cscc4c=(S[S(]1,18,remove c from position 11,flow_matching,0.3,2.0,55,197
136,remove,9.0,c,,Fc#1cscc4c=(S[S(]1,Fc#1cscc4=(S[S(]1,17,remove c from position 9,flow_matching,0.3,2.0,55,197
137,add,12.0,[,,Fc#1cscc4=(S[S(]1,Fc#1cscc4=(S[[S(]1,18,add [ at position 12,flow_matching,0.3,2.0,55,197
138,add,7.0,#,,Fc#1cscc4=(S[[S(]1,Fc#1csc#c4=(S[[S(]1,19,add # at position 7,flow_matching,0.3,2.0,55,197
139,replace,2.0,1,#,Fc#1csc#c4=(S[[S(]1,Fc11csc#c4=(S[[S(]1,19,replace # at position 2 with 1,flow_matching,0.3,2.0,55,197
140,remove,13.0,[,,Fc11csc#c4=(S[[S(]1,Fc11csc#c4=(S[S(]1,18,remove [ from position 13,flow_matching,0.3,2.0,55,197
141,add,14.0,r,,Fc11csc#c4=(S[S(]1,Fc11csc#c4=(S[rS(]1,19,add r at position 14,flow_matching,0.3,2.0,55,197
142,add,13.0,o,,Fc11csc#c4=(S[rS(]1,Fc11csc#c4=(So[rS(]1,20,add o at position 13,flow_matching,0.3,2.0,55,197
143,remove,8.0,c,,Fc11csc#c4=(So[rS(]1,Fc11csc#4=(So[rS(]1,19,remove c from position 8,flow_matching,0.3,2.0,55,197
144,add,1.0,N,,Fc11csc#4=(So[rS(]1,FNc11csc#4=(So[rS(]1,20,add N at position 1,flow_matching,0.3,2.0,55,197
145,replace,1.0,c,N,FNc11csc#4=(So[rS(]1,Fcc11csc#4=(So[rS(]1,20,replace N at position 1 with c,flow_matching,0.3,2.0,55,197
146,add,14.0,I,,Fcc11csc#4=(So[rS(]1,Fcc11csc#4=(SoI[rS(]1,21,add I at position 14,flow_matching,0.3,2.0,55,197
147,replace,2.0,1,c,Fcc11csc#4=(SoI[rS(]1,Fc111csc#4=(SoI[rS(]1,21,replace c at position 2 with 1,flow_matching,0.3,2.0,55,197
148,replace,3.0,c,1,Fc111csc#4=(SoI[rS(]1,Fc1c1csc#4=(SoI[rS(]1,21,replace 1 at position 3 with c,flow_matching,0.3,2.0,55,197
149,replace,4.0,c,1,Fc1c1csc#4=(SoI[rS(]1,Fc1cccsc#4=(SoI[rS(]1,21,replace 1 at position 4 with c,flow_matching,0.3,2.0,55,197
150,replace,6.0,c,s,Fc1cccsc#4=(SoI[rS(]1,Fc1ccccc#4=(SoI[rS(]1,21,replace s at position 6 with c,flow_matching,0.3,2.0,55,197
151,replace,8.0,1,#,Fc1ccccc#4=(SoI[rS(]1,Fc1ccccc14=(SoI[rS(]1,21,replace # at position 8 with 1,flow_matching,0.3,2.0,55,197
152,replace,9.0,[,4,Fc1ccccc14=(SoI[rS(]1,Fc1ccccc1[=(SoI[rS(]1,21,replace 4 at position 9 with [,flow_matching,0.3,2.0,55,197
153,replace,10.0,C,=,Fc1ccccc1[=(SoI[rS(]1,Fc1ccccc1[C(SoI[rS(]1,21,replace = at position 10 with C,flow_matching,0.3,2.0,55,197
154,replace,11.0,@,(,Fc1ccccc1[C(SoI[rS(]1,Fc1ccccc1[C@SoI[rS(]1,21,replace ( at position 11 with @,flow_matching,0.3,2.0,55,197
155,replace,12.0,@,S,Fc1ccccc1[C@SoI[rS(]1,Fc1ccccc1[C@@oI[rS(]1,21,replace S at position 12 with @,flow_matching,0.3,2.0,55,197
156,replace,13.0,H,o,Fc1ccccc1[C@@oI[rS(]1,Fc1ccccc1[C@@HI[rS(]1,21,replace o at position 13 with H,flow_matching,0.3,2.0,55,197
157,replace,14.0,],I,Fc1ccccc1[C@@HI[rS(]1,Fc1ccccc1[C@@H][rS(]1,21,replace I at position 14 with ],flow_matching,0.3,2.0,55,197
158,replace,15.0,(,[,Fc1ccccc1[C@@H][rS(]1,Fc1ccccc1[C@@H](rS(]1,21,replace [ at position 15 with (,flow_matching,0.3,2.0,55,197
159,replace,16.0,c,r,Fc1ccccc1[C@@H](rS(]1,Fc1ccccc1[C@@H](cS(]1,21,replace r at position 16 with c,flow_matching,0.3,2.0,55,197
160,replace,17.0,1,S,Fc1ccccc1[C@@H](cS(]1,Fc1ccccc1[C@@H](c1(]1,21,replace S at position 17 with 1,flow_matching,0.3,2.0,55,197
161,replace,18.0,n,(,Fc1ccccc1[C@@H](c1(]1,Fc1ccccc1[C@@H](c1n]1,21,replace ( at position 18 with n,flow_matching,0.3,2.0,55,197
162,replace,19.0,n,],Fc1ccccc1[C@@H](c1n]1,Fc1ccccc1[C@@H](c1nn1,21,replace ] at position 19 with n,flow_matching,0.3,2.0,55,197
163,replace,20.0,n,1,Fc1ccccc1[C@@H](c1nn1,Fc1ccccc1[C@@H](c1nnn,21,replace 1 at position 20 with n,flow_matching,0.3,2.0,55,197
164,add,21.0,n,,Fc1ccccc1[C@@H](c1nnn,Fc1ccccc1[C@@H](c1nnnn,22,add n at position 21,flow_matching,0.3,2.0,55,197
165,add,22.0,1,,Fc1ccccc1[C@@H](c1nnnn,Fc1ccccc1[C@@H](c1nnnn1,23,add 1 at position 22,flow_matching,0.3,2.0,55,197
166,add,23.0,C,,Fc1ccccc1[C@@H](c1nnnn1,Fc1ccccc1[C@@H](c1nnnn1C,24,add C at position 23,flow_matching,0.3,2.0,55,197
167,add,24.0,1,,Fc1ccccc1[C@@H](c1nnnn1C,Fc1ccccc1[C@@H](c1nnnn1C1,25,add 1 at position 24,flow_matching,0.3,2.0,55,197
168,add,25.0,C,,Fc1ccccc1[C@@H](c1nnnn1C1,Fc1ccccc1[C@@H](c1nnnn1C1C,26,add C at position 25,flow_matching,0.3,2.0,55,197
169,add,26.0,C,,Fc1ccccc1[C@@H](c1nnnn1C1C,Fc1ccccc1[C@@H](c1nnnn1C1CC,27,add C at position 26,flow_matching,0.3,2.0,55,197
170,add,27.0,C,,Fc1ccccc1[C@@H](c1nnnn1C1CC,Fc1ccccc1[C@@H](c1nnnn1C1CCC,28,add C at position 27,flow_matching,0.3,2.0,55,197
171,add,28.0,C,,Fc1ccccc1[C@@H](c1nnnn1C1CCC,Fc1ccccc1[C@@H](c1nnnn1C1CCCC,29,add C at position 28,flow_matching,0.3,2.0,55,197
172,add,29.0,C,,Fc1ccccc1[C@@H](c1nnnn1C1CCCC,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC,30,add C at position 29,flow_matching,0.3,2.0,55,197
173,add,30.0,1,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1,31,add 1 at position 30,flow_matching,0.3,2.0,55,197
174,add,31.0,),,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1),32,add ) at position 31,flow_matching,0.3,2.0,55,197
175,add,32.0,[,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1),Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[,33,add [ at position 32,flow_matching,0.3,2.0,55,197
176,add,33.0,N,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[N,34,add N at position 33,flow_matching,0.3,2.0,55,197
177,add,34.0,H,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[N,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH,35,add H at position 34,flow_matching,0.3,2.0,55,197
178,add,35.0,+,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+,36,add + at position 35,flow_matching,0.3,2.0,55,197
179,add,36.0,],,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+],37,add ] at position 36,flow_matching,0.3,2.0,55,197
180,add,37.0,1,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+],Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1,38,add 1 at position 37,flow_matching,0.3,2.0,55,197
181,add,38.0,C,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1C,39,add C at position 38,flow_matching,0.3,2.0,55,197
182,add,39.0,C,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1C,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CC,40,add C at position 39,flow_matching,0.3,2.0,55,197
183,add,40.0,N,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CC,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN,41,add N at position 40,flow_matching,0.3,2.0,55,197
184,add,41.0,(,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(,42,add ( at position 41,flow_matching,0.3,2.0,55,197
185,add,42.0,c,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c,43,add c at position 42,flow_matching,0.3,2.0,55,197
186,add,43.0,2,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2,44,add 2 at position 43,flow_matching,0.3,2.0,55,197
187,add,44.0,c,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2c,45,add c at position 44,flow_matching,0.3,2.0,55,197
188,add,45.0,c,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2c,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2cc,46,add c at position 45,flow_matching,0.3,2.0,55,197
189,add,46.0,c,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2cc,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccc,47,add c at position 46,flow_matching,0.3,2.0,55,197
190,add,47.0,c,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccc,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2cccc,48,add c at position 47,flow_matching,0.3,2.0,55,197
191,add,48.0,c,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2cccc,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc,49,add c at position 48,flow_matching,0.3,2.0,55,197
192,add,49.0,2,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2,50,add 2 at position 49,flow_matching,0.3,2.0,55,197
193,add,50.0,),,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2),51,add ) at position 50,flow_matching,0.3,2.0,55,197
194,add,51.0,C,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2),Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2)C,52,add C at position 51,flow_matching,0.3,2.0,55,197
195,add,52.0,C,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2)C,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2)CC,53,add C at position 52,flow_matching,0.3,2.0,55,197
196,add,53.0,1,,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2)CC,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2)CC1,54,add 1 at position 53,flow_matching,0.3,2.0,55,197
197,add,54.0,"
",,Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2)CC1,"Fc1ccccc1[C@@H](c1nnnn1C1CCCCC1)[NH+]1CCN(c2ccccc2)CC1
",55,"add 
 at position 54",flow_matching,0.3,2.0,55,197
