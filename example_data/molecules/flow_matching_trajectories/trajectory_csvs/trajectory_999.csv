step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,37,131
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,37,131
2,add,1.0,),,C,C),2,add ) at position 1,flow_matching,0.3,2.0,37,131
3,add,0.0,I,,C),IC),3,add I at position 0,flow_matching,0.3,2.0,37,131
4,replace,0.0,C,I,IC),CC),3,replace I at position 0 with C,flow_matching,0.3,2.0,37,131
5,replace,1.0,c,C,CC),Cc),3,replace <PERSON> at position 1 with c,flow_matching,0.3,2.0,37,131
6,replace,2.0,1,),Cc),Cc1,3,replace ) at position 2 with 1,flow_matching,0.3,2.0,37,131
7,replace,0.0,S,C,Cc1,Sc1,3,replace <PERSON> at position 0 with S,flow_matching,0.3,2.0,37,131
8,remove,1.0,c,,Sc1,S1,2,remove c from position 1,flow_matching,0.3,2.0,37,131
9,remove,0.0,S,,S1,1,1,remove S from position 0,flow_matching,0.3,2.0,37,131
10,add,0.0,\,,1,\1,2,add \ at position 0,flow_matching,0.3,2.0,37,131
11,add,2.0,O,,\1,\1O,3,add O at position 2,flow_matching,0.3,2.0,37,131
12,add,3.0,O,,\1O,\1OO,4,add O at position 3,flow_matching,0.3,2.0,37,131
13,remove,3.0,O,,\1OO,\1O,3,remove O from position 3,flow_matching,0.3,2.0,37,131
14,remove,2.0,O,,\1O,\1,2,remove O from position 2,flow_matching,0.3,2.0,37,131
15,replace,0.0,C,\,\1,C1,2,replace \ at position 0 with C,flow_matching,0.3,2.0,37,131
16,replace,1.0,c,1,C1,Cc,2,replace 1 at position 1 with c,flow_matching,0.3,2.0,37,131
17,remove,1.0,c,,Cc,C,1,remove c from position 1,flow_matching,0.3,2.0,37,131
18,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,37,131
19,add,0.0,#,,,#,1,add # at position 0,flow_matching,0.3,2.0,37,131
20,replace,0.0,-,#,#,-,1,replace # at position 0 with -,flow_matching,0.3,2.0,37,131
21,remove,0.0,-,,-,,0,remove - from position 0,flow_matching,0.3,2.0,37,131
22,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,37,131
23,add,0.0,],,3,]3,2,add ] at position 0,flow_matching,0.3,2.0,37,131
24,remove,1.0,3,,]3,],1,remove 3 from position 1,flow_matching,0.3,2.0,37,131
25,replace,0.0,C,],],C,1,replace ] at position 0 with C,flow_matching,0.3,2.0,37,131
26,replace,0.0,],C,C,],1,replace C at position 0 with ],flow_matching,0.3,2.0,37,131
27,replace,0.0,C,],],C,1,replace ] at position 0 with C,flow_matching,0.3,2.0,37,131
28,add,0.0,[,,C,[C,2,add [ at position 0,flow_matching,0.3,2.0,37,131
29,replace,0.0,N,[,[C,NC,2,replace [ at position 0 with N,flow_matching,0.3,2.0,37,131
30,replace,0.0,5,N,NC,5C,2,replace N at position 0 with 5,flow_matching,0.3,2.0,37,131
31,replace,0.0,C,5,5C,CC,2,replace 5 at position 0 with C,flow_matching,0.3,2.0,37,131
32,replace,1.0,5,C,CC,C5,2,replace C at position 1 with 5,flow_matching,0.3,2.0,37,131
33,replace,1.0,c,5,C5,Cc,2,replace 5 at position 1 with c,flow_matching,0.3,2.0,37,131
34,replace,1.0,#,c,Cc,C#,2,replace c at position 1 with #,flow_matching,0.3,2.0,37,131
35,replace,1.0,c,#,C#,Cc,2,replace # at position 1 with c,flow_matching,0.3,2.0,37,131
36,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,37,131
37,add,1.0,[,,Cc1,C[c1,4,add [ at position 1,flow_matching,0.3,2.0,37,131
38,replace,1.0,-,[,C[c1,C-c1,4,replace [ at position 1 with -,flow_matching,0.3,2.0,37,131
39,remove,0.0,C,,C-c1,-c1,3,remove C from position 0,flow_matching,0.3,2.0,37,131
40,remove,2.0,1,,-c1,-c,2,remove 1 from position 2,flow_matching,0.3,2.0,37,131
41,replace,0.0,C,-,-c,Cc,2,replace - at position 0 with C,flow_matching,0.3,2.0,37,131
42,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,37,131
43,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,37,131
44,replace,2.0,\,1,Cc1c,Cc\c,4,replace 1 at position 2 with \,flow_matching,0.3,2.0,37,131
45,add,3.0,O,,Cc\c,Cc\Oc,5,add O at position 3,flow_matching,0.3,2.0,37,131
46,add,0.0,+,,Cc\Oc,+Cc\Oc,6,add + at position 0,flow_matching,0.3,2.0,37,131
47,add,5.0,n,,+Cc\Oc,+Cc\Onc,7,add n at position 5,flow_matching,0.3,2.0,37,131
48,replace,6.0,O,c,+Cc\Onc,+Cc\OnO,7,replace c at position 6 with O,flow_matching,0.3,2.0,37,131
49,add,4.0,),,+Cc\OnO,+Cc\)OnO,8,add ) at position 4,flow_matching,0.3,2.0,37,131
50,add,5.0,[,,+Cc\)OnO,+Cc\)[OnO,9,add [ at position 5,flow_matching,0.3,2.0,37,131
51,replace,0.0,C,+,+Cc\)[OnO,CCc\)[OnO,9,replace + at position 0 with C,flow_matching,0.3,2.0,37,131
52,replace,7.0,l,n,CCc\)[OnO,CCc\)[OlO,9,replace n at position 7 with l,flow_matching,0.3,2.0,37,131
53,replace,1.0,c,C,CCc\)[OlO,Ccc\)[OlO,9,replace C at position 1 with c,flow_matching,0.3,2.0,37,131
54,replace,1.0,[,c,Ccc\)[OlO,C[c\)[OlO,9,replace c at position 1 with [,flow_matching,0.3,2.0,37,131
55,add,9.0,3,,C[c\)[OlO,C[c\)[OlO3,10,add 3 at position 9,flow_matching,0.3,2.0,37,131
56,remove,4.0,),,C[c\)[OlO3,C[c\[OlO3,9,remove ) from position 4,flow_matching,0.3,2.0,37,131
57,replace,1.0,c,[,C[c\[OlO3,Ccc\[OlO3,9,replace [ at position 1 with c,flow_matching,0.3,2.0,37,131
58,add,4.0,],,Ccc\[OlO3,Ccc\][OlO3,10,add ] at position 4,flow_matching,0.3,2.0,37,131
59,add,0.0,S,,Ccc\][OlO3,SCcc\][OlO3,11,add S at position 0,flow_matching,0.3,2.0,37,131
60,remove,9.0,O,,SCcc\][OlO3,SCcc\][Ol3,10,remove O from position 9,flow_matching,0.3,2.0,37,131
61,replace,0.0,C,S,SCcc\][Ol3,CCcc\][Ol3,10,replace S at position 0 with C,flow_matching,0.3,2.0,37,131
62,replace,1.0,c,C,CCcc\][Ol3,Cccc\][Ol3,10,replace C at position 1 with c,flow_matching,0.3,2.0,37,131
63,replace,2.0,1,c,Cccc\][Ol3,Cc1c\][Ol3,10,replace c at position 2 with 1,flow_matching,0.3,2.0,37,131
64,add,1.0,5,,Cc1c\][Ol3,C5c1c\][Ol3,11,add 5 at position 1,flow_matching,0.3,2.0,37,131
65,add,4.0,3,,C5c1c\][Ol3,C5c13c\][Ol3,12,add 3 at position 4,flow_matching,0.3,2.0,37,131
66,replace,6.0,7,\,C5c13c\][Ol3,C5c13c7][Ol3,12,replace \ at position 6 with 7,flow_matching,0.3,2.0,37,131
67,replace,10.0,[,l,C5c13c7][Ol3,C5c13c7][O[3,12,replace l at position 10 with [,flow_matching,0.3,2.0,37,131
68,replace,2.0,\,c,C5c13c7][O[3,C5\13c7][O[3,12,replace c at position 2 with \,flow_matching,0.3,2.0,37,131
69,remove,6.0,7,,C5\13c7][O[3,C5\13c][O[3,11,remove 7 from position 6,flow_matching,0.3,2.0,37,131
70,add,11.0,I,,C5\13c][O[3,C5\13c][O[3I,12,add I at position 11,flow_matching,0.3,2.0,37,131
71,replace,1.0,c,5,C5\13c][O[3I,Cc\13c][O[3I,12,replace 5 at position 1 with c,flow_matching,0.3,2.0,37,131
72,replace,2.0,1,\,Cc\13c][O[3I,Cc113c][O[3I,12,replace \ at position 2 with 1,flow_matching,0.3,2.0,37,131
73,replace,3.0,c,1,Cc113c][O[3I,Cc1c3c][O[3I,12,replace 1 at position 3 with c,flow_matching,0.3,2.0,37,131
74,replace,10.0,6,3,Cc1c3c][O[3I,Cc1c3c][O[6I,12,replace 3 at position 10 with 6,flow_matching,0.3,2.0,37,131
75,replace,4.0,c,3,Cc1c3c][O[6I,Cc1ccc][O[6I,12,replace 3 at position 4 with c,flow_matching,0.3,2.0,37,131
76,add,8.0,7,,Cc1ccc][O[6I,Cc1ccc][7O[6I,13,add 7 at position 8,flow_matching,0.3,2.0,37,131
77,replace,1.0,I,c,Cc1ccc][7O[6I,CI1ccc][7O[6I,13,replace c at position 1 with I,flow_matching,0.3,2.0,37,131
78,replace,1.0,c,I,CI1ccc][7O[6I,Cc1ccc][7O[6I,13,replace I at position 1 with c,flow_matching,0.3,2.0,37,131
79,remove,8.0,7,,Cc1ccc][7O[6I,Cc1ccc][O[6I,12,remove 7 from position 8,flow_matching,0.3,2.0,37,131
80,replace,6.0,c,],Cc1ccc][O[6I,Cc1cccc[O[6I,12,replace ] at position 6 with c,flow_matching,0.3,2.0,37,131
81,add,11.0,3,,Cc1cccc[O[6I,Cc1cccc[O[63I,13,add 3 at position 11,flow_matching,0.3,2.0,37,131
82,replace,7.0,(,[,Cc1cccc[O[63I,Cc1cccc(O[63I,13,replace [ at position 7 with (,flow_matching,0.3,2.0,37,131
83,replace,8.0,[,O,Cc1cccc(O[63I,Cc1cccc([[63I,13,replace O at position 8 with [,flow_matching,0.3,2.0,37,131
84,replace,9.0,C,[,Cc1cccc([[63I,Cc1cccc([C63I,13,replace [ at position 9 with C,flow_matching,0.3,2.0,37,131
85,add,4.0,4,,Cc1cccc([C63I,Cc1c4ccc([C63I,14,add 4 at position 4,flow_matching,0.3,2.0,37,131
86,replace,10.0,-,C,Cc1c4ccc([C63I,Cc1c4ccc([-63I,14,replace C at position 10 with -,flow_matching,0.3,2.0,37,131
87,remove,4.0,4,,Cc1c4ccc([-63I,Cc1cccc([-63I,13,remove 4 from position 4,flow_matching,0.3,2.0,37,131
88,add,5.0,(,,Cc1cccc([-63I,Cc1cc(cc([-63I,14,add ( at position 5,flow_matching,0.3,2.0,37,131
89,replace,10.0,7,-,Cc1cc(cc([-63I,Cc1cc(cc([763I,14,replace - at position 10 with 7,flow_matching,0.3,2.0,37,131
90,remove,3.0,c,,Cc1cc(cc([763I,Cc1c(cc([763I,13,remove c from position 3,flow_matching,0.3,2.0,37,131
91,add,13.0,[,,Cc1c(cc([763I,Cc1c(cc([763I[,14,add [ at position 13,flow_matching,0.3,2.0,37,131
92,replace,4.0,c,(,Cc1c(cc([763I[,Cc1cccc([763I[,14,replace ( at position 4 with c,flow_matching,0.3,2.0,37,131
93,remove,1.0,c,,Cc1cccc([763I[,C1cccc([763I[,13,remove c from position 1,flow_matching,0.3,2.0,37,131
94,add,5.0,r,,C1cccc([763I[,C1cccrc([763I[,14,add r at position 5,flow_matching,0.3,2.0,37,131
95,add,2.0,n,,C1cccrc([763I[,C1ncccrc([763I[,15,add n at position 2,flow_matching,0.3,2.0,37,131
96,remove,0.0,C,,C1ncccrc([763I[,1ncccrc([763I[,14,remove C from position 0,flow_matching,0.3,2.0,37,131
97,replace,0.0,C,1,1ncccrc([763I[,Cncccrc([763I[,14,replace 1 at position 0 with C,flow_matching,0.3,2.0,37,131
98,replace,10.0,#,6,Cncccrc([763I[,Cncccrc([7#3I[,14,replace 6 at position 10 with #,flow_matching,0.3,2.0,37,131
99,replace,1.0,c,n,Cncccrc([7#3I[,Cccccrc([7#3I[,14,replace n at position 1 with c,flow_matching,0.3,2.0,37,131
100,add,14.0,F,,Cccccrc([7#3I[,Cccccrc([7#3I[F,15,add F at position 14,flow_matching,0.3,2.0,37,131
101,replace,12.0,=,I,Cccccrc([7#3I[F,Cccccrc([7#3=[F,15,replace I at position 12 with =,flow_matching,0.3,2.0,37,131
102,replace,2.0,1,c,Cccccrc([7#3=[F,Cc1ccrc([7#3=[F,15,replace c at position 2 with 1,flow_matching,0.3,2.0,37,131
103,replace,5.0,c,r,Cc1ccrc([7#3=[F,Cc1cccc([7#3=[F,15,replace r at position 5 with c,flow_matching,0.3,2.0,37,131
104,replace,9.0,C,7,Cc1cccc([7#3=[F,Cc1cccc([C#3=[F,15,replace 7 at position 9 with C,flow_matching,0.3,2.0,37,131
105,replace,10.0,@,#,Cc1cccc([C#3=[F,Cc1cccc([C@3=[F,15,replace # at position 10 with @,flow_matching,0.3,2.0,37,131
106,replace,11.0,H,3,Cc1cccc([C@3=[F,Cc1cccc([C@H=[F,15,replace 3 at position 11 with H,flow_matching,0.3,2.0,37,131
107,replace,12.0,],=,Cc1cccc([C@H=[F,Cc1cccc([C@H][F,15,replace = at position 12 with ],flow_matching,0.3,2.0,37,131
108,replace,13.0,(,[,Cc1cccc([C@H][F,Cc1cccc([C@H](F,15,replace [ at position 13 with (,flow_matching,0.3,2.0,37,131
109,replace,14.0,C,F,Cc1cccc([C@H](F,Cc1cccc([C@H](C,15,replace F at position 14 with C,flow_matching,0.3,2.0,37,131
110,add,15.0,C,,Cc1cccc([C@H](C,Cc1cccc([C@H](CC,16,add C at position 15,flow_matching,0.3,2.0,37,131
111,add,16.0,l,,Cc1cccc([C@H](CC,Cc1cccc([C@H](CCl,17,add l at position 16,flow_matching,0.3,2.0,37,131
112,add,17.0,),,Cc1cccc([C@H](CCl,Cc1cccc([C@H](CCl),18,add ) at position 17,flow_matching,0.3,2.0,37,131
113,add,18.0,C,,Cc1cccc([C@H](CCl),Cc1cccc([C@H](CCl)C,19,add C at position 18,flow_matching,0.3,2.0,37,131
114,add,19.0,C,,Cc1cccc([C@H](CCl)C,Cc1cccc([C@H](CCl)CC,20,add C at position 19,flow_matching,0.3,2.0,37,131
115,add,20.0,C,,Cc1cccc([C@H](CCl)CC,Cc1cccc([C@H](CCl)CCC,21,add C at position 20,flow_matching,0.3,2.0,37,131
116,add,21.0,[,,Cc1cccc([C@H](CCl)CCC,Cc1cccc([C@H](CCl)CCC[,22,add [ at position 21,flow_matching,0.3,2.0,37,131
117,add,22.0,C,,Cc1cccc([C@H](CCl)CCC[,Cc1cccc([C@H](CCl)CCC[C,23,add C at position 22,flow_matching,0.3,2.0,37,131
118,add,23.0,@,,Cc1cccc([C@H](CCl)CCC[C,Cc1cccc([C@H](CCl)CCC[C@,24,add @ at position 23,flow_matching,0.3,2.0,37,131
119,add,24.0,@,,Cc1cccc([C@H](CCl)CCC[C@,Cc1cccc([C@H](CCl)CCC[C@@,25,add @ at position 24,flow_matching,0.3,2.0,37,131
120,add,25.0,H,,Cc1cccc([C@H](CCl)CCC[C@@,Cc1cccc([C@H](CCl)CCC[C@@H,26,add H at position 25,flow_matching,0.3,2.0,37,131
121,add,26.0,],,Cc1cccc([C@H](CCl)CCC[C@@H,Cc1cccc([C@H](CCl)CCC[C@@H],27,add ] at position 26,flow_matching,0.3,2.0,37,131
122,add,27.0,2,,Cc1cccc([C@H](CCl)CCC[C@@H],Cc1cccc([C@H](CCl)CCC[C@@H]2,28,add 2 at position 27,flow_matching,0.3,2.0,37,131
123,add,28.0,C,,Cc1cccc([C@H](CCl)CCC[C@@H]2,Cc1cccc([C@H](CCl)CCC[C@@H]2C,29,add C at position 28,flow_matching,0.3,2.0,37,131
124,add,29.0,C,,Cc1cccc([C@H](CCl)CCC[C@@H]2C,Cc1cccc([C@H](CCl)CCC[C@@H]2CC,30,add C at position 29,flow_matching,0.3,2.0,37,131
125,add,30.0,C,,Cc1cccc([C@H](CCl)CCC[C@@H]2CC,Cc1cccc([C@H](CCl)CCC[C@@H]2CCC,31,add C at position 30,flow_matching,0.3,2.0,37,131
126,add,31.0,O,,Cc1cccc([C@H](CCl)CCC[C@@H]2CCC,Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO,32,add O at position 31,flow_matching,0.3,2.0,37,131
127,add,32.0,2,,Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO,Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO2,33,add 2 at position 32,flow_matching,0.3,2.0,37,131
128,add,33.0,),,Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO2,Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO2),34,add ) at position 33,flow_matching,0.3,2.0,37,131
129,add,34.0,c,,Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO2),Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO2)c,35,add c at position 34,flow_matching,0.3,2.0,37,131
130,add,35.0,1,,Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO2)c,Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO2)c1,36,add 1 at position 35,flow_matching,0.3,2.0,37,131
131,add,36.0,"
",,Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO2)c1,"Cc1cccc([C@H](CCl)CCC[C@@H]2CCCO2)c1
",37,"add 
 at position 36",flow_matching,0.3,2.0,37,131
