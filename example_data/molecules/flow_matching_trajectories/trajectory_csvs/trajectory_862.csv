step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,38,170
1,add,0.0,[,,,[,1,add [ at position 0,flow_matching,0.3,2.0,38,170
2,add,0.0,5,,[,5[,2,add 5 at position 0,flow_matching,0.3,2.0,38,170
3,replace,0.0,C,5,5[,C[,2,replace 5 at position 0 with C,flow_matching,0.3,2.0,38,170
4,replace,1.0,F,[,C[,CF,2,replace [ at position 1 with F,flow_matching,0.3,2.0,38,170
5,replace,1.0,O,F,CF,CO,2,replace F at position 1 with O,flow_matching,0.3,2.0,38,170
6,replace,0.0,<PERSON>,<PERSON>,<PERSON>,BO,2,replace <PERSON> at position 0 with B,flow_matching,0.3,2.0,38,170
7,replace,0.0,+,B,BO,+O,2,replace <PERSON> at position 0 with +,flow_matching,0.3,2.0,38,170
8,remove,0.0,+,,+O,O,1,remove + from position 0,flow_matching,0.3,2.0,38,170
9,add,1.0,H,,O,OH,2,add H at position 1,flow_matching,0.3,2.0,38,170
10,replace,0.0,C,O,OH,CH,2,replace O at position 0 with C,flow_matching,0.3,2.0,38,170
11,replace,1.0,O,H,CH,CO,2,replace H at position 1 with O,flow_matching,0.3,2.0,38,170
12,add,0.0,I,,CO,ICO,3,add I at position 0,flow_matching,0.3,2.0,38,170
13,replace,0.0,C,I,ICO,CCO,3,replace I at position 0 with C,flow_matching,0.3,2.0,38,170
14,add,0.0,l,,CCO,lCCO,4,add l at position 0,flow_matching,0.3,2.0,38,170
15,replace,0.0,C,l,lCCO,CCCO,4,replace l at position 0 with C,flow_matching,0.3,2.0,38,170
16,add,2.0,6,,CCCO,CC6CO,5,add 6 at position 2,flow_matching,0.3,2.0,38,170
17,replace,0.0,o,C,CC6CO,oC6CO,5,replace C at position 0 with o,flow_matching,0.3,2.0,38,170
18,replace,0.0,C,o,oC6CO,CC6CO,5,replace o at position 0 with C,flow_matching,0.3,2.0,38,170
19,remove,0.0,C,,CC6CO,C6CO,4,remove C from position 0,flow_matching,0.3,2.0,38,170
20,add,3.0,r,,C6CO,C6CrO,5,add r at position 3,flow_matching,0.3,2.0,38,170
21,add,4.0,o,,C6CrO,C6CroO,6,add o at position 4,flow_matching,0.3,2.0,38,170
22,add,6.0,1,,C6CroO,C6CroO1,7,add 1 at position 6,flow_matching,0.3,2.0,38,170
23,add,7.0,o,,C6CroO1,C6CroO1o,8,add o at position 7,flow_matching,0.3,2.0,38,170
24,remove,1.0,6,,C6CroO1o,CCroO1o,7,remove 6 from position 1,flow_matching,0.3,2.0,38,170
25,remove,2.0,r,,CCroO1o,CCoO1o,6,remove r from position 2,flow_matching,0.3,2.0,38,170
26,add,2.0,B,,CCoO1o,CCBoO1o,7,add B at position 2,flow_matching,0.3,2.0,38,170
27,add,1.0,r,,CCBoO1o,CrCBoO1o,8,add r at position 1,flow_matching,0.3,2.0,38,170
28,add,6.0,4,,CrCBoO1o,CrCBoO41o,9,add 4 at position 6,flow_matching,0.3,2.0,38,170
29,add,8.0,-,,CrCBoO41o,CrCBoO41-o,10,add - at position 8,flow_matching,0.3,2.0,38,170
30,replace,1.0,O,r,CrCBoO41-o,COCBoO41-o,10,replace r at position 1 with O,flow_matching,0.3,2.0,38,170
31,add,3.0,4,,COCBoO41-o,COC4BoO41-o,11,add 4 at position 3,flow_matching,0.3,2.0,38,170
32,remove,8.0,1,,COC4BoO41-o,COC4BoO4-o,10,remove 1 from position 8,flow_matching,0.3,2.0,38,170
33,add,8.0,),,COC4BoO4-o,COC4BoO4)-o,11,add ) at position 8,flow_matching,0.3,2.0,38,170
34,replace,1.0,),O,COC4BoO4)-o,C)C4BoO4)-o,11,replace O at position 1 with ),flow_matching,0.3,2.0,38,170
35,replace,1.0,O,),C)C4BoO4)-o,COC4BoO4)-o,11,replace ) at position 1 with O,flow_matching,0.3,2.0,38,170
36,replace,2.0,c,C,COC4BoO4)-o,COc4BoO4)-o,11,replace C at position 2 with c,flow_matching,0.3,2.0,38,170
37,add,1.0,C,,COc4BoO4)-o,CCOc4BoO4)-o,12,add C at position 1,flow_matching,0.3,2.0,38,170
38,add,10.0,+,,CCOc4BoO4)-o,CCOc4BoO4)+-o,13,add + at position 10,flow_matching,0.3,2.0,38,170
39,remove,7.0,O,,CCOc4BoO4)+-o,CCOc4Bo4)+-o,12,remove O from position 7,flow_matching,0.3,2.0,38,170
40,replace,1.0,O,C,CCOc4Bo4)+-o,COOc4Bo4)+-o,12,replace C at position 1 with O,flow_matching,0.3,2.0,38,170
41,replace,2.0,c,O,COOc4Bo4)+-o,COcc4Bo4)+-o,12,replace O at position 2 with c,flow_matching,0.3,2.0,38,170
42,add,4.0,3,,COcc4Bo4)+-o,COcc34Bo4)+-o,13,add 3 at position 4,flow_matching,0.3,2.0,38,170
43,remove,2.0,c,,COcc34Bo4)+-o,COc34Bo4)+-o,12,remove c from position 2,flow_matching,0.3,2.0,38,170
44,replace,3.0,1,3,COc34Bo4)+-o,COc14Bo4)+-o,12,replace 3 at position 3 with 1,flow_matching,0.3,2.0,38,170
45,remove,11.0,o,,COc14Bo4)+-o,COc14Bo4)+-,11,remove o from position 11,flow_matching,0.3,2.0,38,170
46,add,9.0,5,,COc14Bo4)+-,COc14Bo4)5+-,12,add 5 at position 9,flow_matching,0.3,2.0,38,170
47,replace,6.0,4,o,COc14Bo4)5+-,COc14B44)5+-,12,replace o at position 6 with 4,flow_matching,0.3,2.0,38,170
48,replace,5.0,o,B,COc14B44)5+-,COc14o44)5+-,12,replace B at position 5 with o,flow_matching,0.3,2.0,38,170
49,remove,6.0,4,,COc14o44)5+-,COc14o4)5+-,11,remove 4 from position 6,flow_matching,0.3,2.0,38,170
50,add,3.0,1,,COc14o4)5+-,COc114o4)5+-,12,add 1 at position 3,flow_matching,0.3,2.0,38,170
51,replace,4.0,c,1,COc114o4)5+-,COc1c4o4)5+-,12,replace 1 at position 4 with c,flow_matching,0.3,2.0,38,170
52,remove,11.0,-,,COc1c4o4)5+-,COc1c4o4)5+,11,remove - from position 11,flow_matching,0.3,2.0,38,170
53,replace,5.0,c,4,COc1c4o4)5+,COc1cco4)5+,11,replace 4 at position 5 with c,flow_matching,0.3,2.0,38,170
54,add,0.0,r,,COc1cco4)5+,rCOc1cco4)5+,12,add r at position 0,flow_matching,0.3,2.0,38,170
55,replace,0.0,C,r,rCOc1cco4)5+,CCOc1cco4)5+,12,replace r at position 0 with C,flow_matching,0.3,2.0,38,170
56,replace,1.0,O,C,CCOc1cco4)5+,COOc1cco4)5+,12,replace C at position 1 with O,flow_matching,0.3,2.0,38,170
57,remove,5.0,c,,COOc1cco4)5+,COOc1co4)5+,11,remove c from position 5,flow_matching,0.3,2.0,38,170
58,replace,2.0,c,O,COOc1co4)5+,COcc1co4)5+,11,replace O at position 2 with c,flow_matching,0.3,2.0,38,170
59,add,1.0,),,COcc1co4)5+,C)Occ1co4)5+,12,add ) at position 1,flow_matching,0.3,2.0,38,170
60,add,6.0,2,,C)Occ1co4)5+,C)Occ12co4)5+,13,add 2 at position 6,flow_matching,0.3,2.0,38,170
61,replace,5.0,S,1,C)Occ12co4)5+,C)OccS2co4)5+,13,replace 1 at position 5 with S,flow_matching,0.3,2.0,38,170
62,remove,2.0,O,,C)OccS2co4)5+,C)ccS2co4)5+,12,remove O from position 2,flow_matching,0.3,2.0,38,170
63,add,8.0,F,,C)ccS2co4)5+,C)ccS2coF4)5+,13,add F at position 8,flow_matching,0.3,2.0,38,170
64,add,6.0,],,C)ccS2coF4)5+,C)ccS2]coF4)5+,14,add ] at position 6,flow_matching,0.3,2.0,38,170
65,replace,1.0,O,),C)ccS2]coF4)5+,COccS2]coF4)5+,14,replace ) at position 1 with O,flow_matching,0.3,2.0,38,170
66,add,9.0,5,,COccS2]coF4)5+,COccS2]co5F4)5+,15,add 5 at position 9,flow_matching,0.3,2.0,38,170
67,replace,3.0,1,c,COccS2]co5F4)5+,COc1S2]co5F4)5+,15,replace c at position 3 with 1,flow_matching,0.3,2.0,38,170
68,replace,4.0,c,S,COc1S2]co5F4)5+,COc1c2]co5F4)5+,15,replace S at position 4 with c,flow_matching,0.3,2.0,38,170
69,remove,6.0,],,COc1c2]co5F4)5+,COc1c2co5F4)5+,14,remove ] from position 6,flow_matching,0.3,2.0,38,170
70,replace,5.0,c,2,COc1c2co5F4)5+,COc1ccco5F4)5+,14,replace 2 at position 5 with c,flow_matching,0.3,2.0,38,170
71,add,2.0,n,,COc1ccco5F4)5+,COnc1ccco5F4)5+,15,add n at position 2,flow_matching,0.3,2.0,38,170
72,add,0.0,I,,COnc1ccco5F4)5+,ICOnc1ccco5F4)5+,16,add I at position 0,flow_matching,0.3,2.0,38,170
73,replace,6.0,6,c,ICOnc1ccco5F4)5+,ICOnc16cco5F4)5+,16,replace c at position 6 with 6,flow_matching,0.3,2.0,38,170
74,replace,0.0,C,I,ICOnc16cco5F4)5+,CCOnc16cco5F4)5+,16,replace I at position 0 with C,flow_matching,0.3,2.0,38,170
75,replace,15.0,#,+,CCOnc16cco5F4)5+,CCOnc16cco5F4)5#,16,replace + at position 15 with #,flow_matching,0.3,2.0,38,170
76,add,8.0,2,,CCOnc16cco5F4)5#,CCOnc16c2co5F4)5#,17,add 2 at position 8,flow_matching,0.3,2.0,38,170
77,replace,1.0,O,C,CCOnc16c2co5F4)5#,COOnc16c2co5F4)5#,17,replace C at position 1 with O,flow_matching,0.3,2.0,38,170
78,add,8.0,4,,COOnc16c2co5F4)5#,COOnc16c42co5F4)5#,18,add 4 at position 8,flow_matching,0.3,2.0,38,170
79,replace,2.0,c,O,COOnc16c42co5F4)5#,COcnc16c42co5F4)5#,18,replace O at position 2 with c,flow_matching,0.3,2.0,38,170
80,replace,3.0,1,n,COcnc16c42co5F4)5#,COc1c16c42co5F4)5#,18,replace n at position 3 with 1,flow_matching,0.3,2.0,38,170
81,remove,10.0,c,,COc1c16c42co5F4)5#,COc1c16c42o5F4)5#,17,remove c from position 10,flow_matching,0.3,2.0,38,170
82,remove,8.0,4,,COc1c16c42o5F4)5#,COc1c16c2o5F4)5#,16,remove 4 from position 8,flow_matching,0.3,2.0,38,170
83,replace,5.0,c,1,COc1c16c2o5F4)5#,COc1cc6c2o5F4)5#,16,replace 1 at position 5 with c,flow_matching,0.3,2.0,38,170
84,replace,4.0,5,c,COc1cc6c2o5F4)5#,COc15c6c2o5F4)5#,16,replace c at position 4 with 5,flow_matching,0.3,2.0,38,170
85,replace,4.0,c,5,COc15c6c2o5F4)5#,COc1cc6c2o5F4)5#,16,replace 5 at position 4 with c,flow_matching,0.3,2.0,38,170
86,remove,12.0,4,,COc1cc6c2o5F4)5#,COc1cc6c2o5F)5#,15,remove 4 from position 12,flow_matching,0.3,2.0,38,170
87,replace,3.0,=,1,COc1cc6c2o5F)5#,COc=cc6c2o5F)5#,15,replace 1 at position 3 with =,flow_matching,0.3,2.0,38,170
88,replace,0.0,\,C,COc=cc6c2o5F)5#,\Oc=cc6c2o5F)5#,15,replace C at position 0 with \,flow_matching,0.3,2.0,38,170
89,add,10.0,],,\Oc=cc6c2o5F)5#,\Oc=cc6c2o]5F)5#,16,add ] at position 10,flow_matching,0.3,2.0,38,170
90,replace,0.0,C,\,\Oc=cc6c2o]5F)5#,COc=cc6c2o]5F)5#,16,replace \ at position 0 with C,flow_matching,0.3,2.0,38,170
91,replace,4.0,1,c,COc=cc6c2o]5F)5#,COc=1c6c2o]5F)5#,16,replace c at position 4 with 1,flow_matching,0.3,2.0,38,170
92,replace,11.0,B,5,COc=1c6c2o]5F)5#,COc=1c6c2o]BF)5#,16,replace 5 at position 11 with B,flow_matching,0.3,2.0,38,170
93,replace,8.0,1,2,COc=1c6c2o]BF)5#,COc=1c6c1o]BF)5#,16,replace 2 at position 8 with 1,flow_matching,0.3,2.0,38,170
94,replace,3.0,1,=,COc=1c6c1o]BF)5#,COc11c6c1o]BF)5#,16,replace = at position 3 with 1,flow_matching,0.3,2.0,38,170
95,replace,9.0,I,o,COc11c6c1o]BF)5#,COc11c6c1I]BF)5#,16,replace o at position 9 with I,flow_matching,0.3,2.0,38,170
96,replace,1.0,),O,COc11c6c1I]BF)5#,C)c11c6c1I]BF)5#,16,replace O at position 1 with ),flow_matching,0.3,2.0,38,170
97,remove,7.0,c,,C)c11c6c1I]BF)5#,C)c11c61I]BF)5#,15,remove c from position 7,flow_matching,0.3,2.0,38,170
98,replace,1.0,O,),C)c11c61I]BF)5#,COc11c61I]BF)5#,15,replace ) at position 1 with O,flow_matching,0.3,2.0,38,170
99,replace,4.0,c,1,COc11c61I]BF)5#,COc1cc61I]BF)5#,15,replace 1 at position 4 with c,flow_matching,0.3,2.0,38,170
100,remove,9.0,],,COc1cc61I]BF)5#,COc1cc61IBF)5#,14,remove ] from position 9,flow_matching,0.3,2.0,38,170
101,replace,6.0,c,6,COc1cc61IBF)5#,COc1ccc1IBF)5#,14,replace 6 at position 6 with c,flow_matching,0.3,2.0,38,170
102,add,13.0,5,,COc1ccc1IBF)5#,COc1ccc1IBF)55#,15,add 5 at position 13,flow_matching,0.3,2.0,38,170
103,replace,7.0,c,1,COc1ccc1IBF)55#,COc1ccccIBF)55#,15,replace 1 at position 7 with c,flow_matching,0.3,2.0,38,170
104,replace,8.0,(,I,COc1ccccIBF)55#,COc1cccc(BF)55#,15,replace I at position 8 with (,flow_matching,0.3,2.0,38,170
105,replace,9.0,[,B,COc1cccc(BF)55#,COc1cccc([F)55#,15,replace B at position 9 with [,flow_matching,0.3,2.0,38,170
106,add,1.0,N,,COc1cccc([F)55#,CNOc1cccc([F)55#,16,add N at position 1,flow_matching,0.3,2.0,38,170
107,add,6.0,o,,CNOc1cccc([F)55#,CNOc1coccc([F)55#,17,add o at position 6,flow_matching,0.3,2.0,38,170
108,replace,1.0,O,N,CNOc1coccc([F)55#,COOc1coccc([F)55#,17,replace N at position 1 with O,flow_matching,0.3,2.0,38,170
109,replace,2.0,c,O,COOc1coccc([F)55#,COcc1coccc([F)55#,17,replace O at position 2 with c,flow_matching,0.3,2.0,38,170
110,add,12.0,@,,COcc1coccc([F)55#,COcc1coccc([@F)55#,18,add @ at position 12,flow_matching,0.3,2.0,38,170
111,remove,15.0,5,,COcc1coccc([@F)55#,COcc1coccc([@F)5#,17,remove 5 from position 15,flow_matching,0.3,2.0,38,170
112,remove,12.0,@,,COcc1coccc([@F)5#,COcc1coccc([F)5#,16,remove @ from position 12,flow_matching,0.3,2.0,38,170
113,add,6.0,-,,COcc1coccc([F)5#,COcc1c-occc([F)5#,17,add - at position 6,flow_matching,0.3,2.0,38,170
114,replace,9.0,C,c,COcc1c-occc([F)5#,COcc1c-ocCc([F)5#,17,replace c at position 9 with C,flow_matching,0.3,2.0,38,170
115,remove,6.0,-,,COcc1c-ocCc([F)5#,COcc1cocCc([F)5#,16,remove - from position 6,flow_matching,0.3,2.0,38,170
116,replace,3.0,1,c,COcc1cocCc([F)5#,COc11cocCc([F)5#,16,replace c at position 3 with 1,flow_matching,0.3,2.0,38,170
117,add,7.0,n,,COc11cocCc([F)5#,COc11concCc([F)5#,17,add n at position 7,flow_matching,0.3,2.0,38,170
118,remove,14.0,),,COc11concCc([F)5#,COc11concCc([F5#,16,remove ) from position 14,flow_matching,0.3,2.0,38,170
119,replace,2.0,5,c,COc11concCc([F5#,CO511concCc([F5#,16,replace c at position 2 with 5,flow_matching,0.3,2.0,38,170
120,replace,2.0,c,5,CO511concCc([F5#,COc11concCc([F5#,16,replace 5 at position 2 with c,flow_matching,0.3,2.0,38,170
121,replace,4.0,c,1,COc11concCc([F5#,COc1cconcCc([F5#,16,replace 1 at position 4 with c,flow_matching,0.3,2.0,38,170
122,replace,6.0,c,o,COc1cconcCc([F5#,COc1cccncCc([F5#,16,replace o at position 6 with c,flow_matching,0.3,2.0,38,170
123,replace,7.0,c,n,COc1cccncCc([F5#,COc1cccccCc([F5#,16,replace n at position 7 with c,flow_matching,0.3,2.0,38,170
124,replace,5.0,#,c,COc1cccccCc([F5#,COc1c#cccCc([F5#,16,replace c at position 5 with #,flow_matching,0.3,2.0,38,170
125,replace,5.0,c,#,COc1c#cccCc([F5#,COc1cccccCc([F5#,16,replace # at position 5 with c,flow_matching,0.3,2.0,38,170
126,replace,8.0,(,c,COc1cccccCc([F5#,COc1cccc(Cc([F5#,16,replace c at position 8 with (,flow_matching,0.3,2.0,38,170
127,replace,9.0,[,C,COc1cccc(Cc([F5#,COc1cccc([c([F5#,16,replace C at position 9 with [,flow_matching,0.3,2.0,38,170
128,replace,1.0,+,O,COc1cccc([c([F5#,C+c1cccc([c([F5#,16,replace O at position 1 with +,flow_matching,0.3,2.0,38,170
129,remove,2.0,c,,C+c1cccc([c([F5#,C+1cccc([c([F5#,15,remove c from position 2,flow_matching,0.3,2.0,38,170
130,remove,11.0,[,,C+1cccc([c([F5#,C+1cccc([c(F5#,14,remove [ from position 11,flow_matching,0.3,2.0,38,170
131,replace,0.0,s,C,C+1cccc([c(F5#,s+1cccc([c(F5#,14,replace C at position 0 with s,flow_matching,0.3,2.0,38,170
132,replace,5.0,B,c,s+1cccc([c(F5#,s+1ccBc([c(F5#,14,replace c at position 5 with B,flow_matching,0.3,2.0,38,170
133,replace,0.0,C,s,s+1ccBc([c(F5#,C+1ccBc([c(F5#,14,replace s at position 0 with C,flow_matching,0.3,2.0,38,170
134,replace,1.0,O,+,C+1ccBc([c(F5#,CO1ccBc([c(F5#,14,replace + at position 1 with O,flow_matching,0.3,2.0,38,170
135,replace,1.0,[,O,CO1ccBc([c(F5#,C[1ccBc([c(F5#,14,replace O at position 1 with [,flow_matching,0.3,2.0,38,170
136,replace,1.0,O,[,C[1ccBc([c(F5#,CO1ccBc([c(F5#,14,replace [ at position 1 with O,flow_matching,0.3,2.0,38,170
137,add,8.0,4,,CO1ccBc([c(F5#,CO1ccBc(4[c(F5#,15,add 4 at position 8,flow_matching,0.3,2.0,38,170
138,add,1.0,l,,CO1ccBc(4[c(F5#,ClO1ccBc(4[c(F5#,16,add l at position 1,flow_matching,0.3,2.0,38,170
139,replace,1.0,O,l,ClO1ccBc(4[c(F5#,COO1ccBc(4[c(F5#,16,replace l at position 1 with O,flow_matching,0.3,2.0,38,170
140,replace,2.0,c,O,COO1ccBc(4[c(F5#,COc1ccBc(4[c(F5#,16,replace O at position 2 with c,flow_matching,0.3,2.0,38,170
141,replace,6.0,c,B,COc1ccBc(4[c(F5#,COc1cccc(4[c(F5#,16,replace B at position 6 with c,flow_matching,0.3,2.0,38,170
142,replace,9.0,[,4,COc1cccc(4[c(F5#,COc1cccc([[c(F5#,16,replace 4 at position 9 with [,flow_matching,0.3,2.0,38,170
143,replace,10.0,C,[,COc1cccc([[c(F5#,COc1cccc([Cc(F5#,16,replace [ at position 10 with C,flow_matching,0.3,2.0,38,170
144,replace,11.0,@,c,COc1cccc([Cc(F5#,COc1cccc([C@(F5#,16,replace c at position 11 with @,flow_matching,0.3,2.0,38,170
145,replace,12.0,H,(,COc1cccc([C@(F5#,COc1cccc([C@HF5#,16,replace ( at position 12 with H,flow_matching,0.3,2.0,38,170
146,replace,13.0,],F,COc1cccc([C@HF5#,COc1cccc([C@H]5#,16,replace F at position 13 with ],flow_matching,0.3,2.0,38,170
147,replace,14.0,2,5,COc1cccc([C@H]5#,COc1cccc([C@H]2#,16,replace 5 at position 14 with 2,flow_matching,0.3,2.0,38,170
148,replace,15.0,C,#,COc1cccc([C@H]2#,COc1cccc([C@H]2C,16,replace # at position 15 with C,flow_matching,0.3,2.0,38,170
149,add,16.0,C,,COc1cccc([C@H]2C,COc1cccc([C@H]2CC,17,add C at position 16,flow_matching,0.3,2.0,38,170
150,add,17.0,C,,COc1cccc([C@H]2CC,COc1cccc([C@H]2CCC,18,add C at position 17,flow_matching,0.3,2.0,38,170
151,add,18.0,N,,COc1cccc([C@H]2CCC,COc1cccc([C@H]2CCCN,19,add N at position 18,flow_matching,0.3,2.0,38,170
152,add,19.0,2,,COc1cccc([C@H]2CCCN,COc1cccc([C@H]2CCCN2,20,add 2 at position 19,flow_matching,0.3,2.0,38,170
153,add,20.0,C,,COc1cccc([C@H]2CCCN2,COc1cccc([C@H]2CCCN2C,21,add C at position 20,flow_matching,0.3,2.0,38,170
154,add,21.0,(,,COc1cccc([C@H]2CCCN2C,COc1cccc([C@H]2CCCN2C(,22,add ( at position 21,flow_matching,0.3,2.0,38,170
155,add,22.0,=,,COc1cccc([C@H]2CCCN2C(,COc1cccc([C@H]2CCCN2C(=,23,add = at position 22,flow_matching,0.3,2.0,38,170
156,add,23.0,O,,COc1cccc([C@H]2CCCN2C(=,COc1cccc([C@H]2CCCN2C(=O,24,add O at position 23,flow_matching,0.3,2.0,38,170
157,add,24.0,),,COc1cccc([C@H]2CCCN2C(=O,COc1cccc([C@H]2CCCN2C(=O),25,add ) at position 24,flow_matching,0.3,2.0,38,170
158,add,25.0,c,,COc1cccc([C@H]2CCCN2C(=O),COc1cccc([C@H]2CCCN2C(=O)c,26,add c at position 25,flow_matching,0.3,2.0,38,170
159,add,26.0,2,,COc1cccc([C@H]2CCCN2C(=O)c,COc1cccc([C@H]2CCCN2C(=O)c2,27,add 2 at position 26,flow_matching,0.3,2.0,38,170
160,add,27.0,c,,COc1cccc([C@H]2CCCN2C(=O)c2,COc1cccc([C@H]2CCCN2C(=O)c2c,28,add c at position 27,flow_matching,0.3,2.0,38,170
161,add,28.0,c,,COc1cccc([C@H]2CCCN2C(=O)c2c,COc1cccc([C@H]2CCCN2C(=O)c2cc,29,add c at position 28,flow_matching,0.3,2.0,38,170
162,add,29.0,c,,COc1cccc([C@H]2CCCN2C(=O)c2cc,COc1cccc([C@H]2CCCN2C(=O)c2ccc,30,add c at position 29,flow_matching,0.3,2.0,38,170
163,add,30.0,c,,COc1cccc([C@H]2CCCN2C(=O)c2ccc,COc1cccc([C@H]2CCCN2C(=O)c2cccc,31,add c at position 30,flow_matching,0.3,2.0,38,170
164,add,31.0,c,,COc1cccc([C@H]2CCCN2C(=O)c2cccc,COc1cccc([C@H]2CCCN2C(=O)c2ccccc,32,add c at position 31,flow_matching,0.3,2.0,38,170
165,add,32.0,2,,COc1cccc([C@H]2CCCN2C(=O)c2ccccc,COc1cccc([C@H]2CCCN2C(=O)c2ccccc2,33,add 2 at position 32,flow_matching,0.3,2.0,38,170
166,add,33.0,I,,COc1cccc([C@H]2CCCN2C(=O)c2ccccc2,COc1cccc([C@H]2CCCN2C(=O)c2ccccc2I,34,add I at position 33,flow_matching,0.3,2.0,38,170
167,add,34.0,),,COc1cccc([C@H]2CCCN2C(=O)c2ccccc2I,COc1cccc([C@H]2CCCN2C(=O)c2ccccc2I),35,add ) at position 34,flow_matching,0.3,2.0,38,170
168,add,35.0,c,,COc1cccc([C@H]2CCCN2C(=O)c2ccccc2I),COc1cccc([C@H]2CCCN2C(=O)c2ccccc2I)c,36,add c at position 35,flow_matching,0.3,2.0,38,170
169,add,36.0,1,,COc1cccc([C@H]2CCCN2C(=O)c2ccccc2I)c,COc1cccc([C@H]2CCCN2C(=O)c2ccccc2I)c1,37,add 1 at position 36,flow_matching,0.3,2.0,38,170
170,add,37.0,"
",,COc1cccc([C@H]2CCCN2C(=O)c2ccccc2I)c1,"COc1cccc([C@H]2CCCN2C(=O)c2ccccc2I)c1
",38,"add 
 at position 37",flow_matching,0.3,2.0,38,170
