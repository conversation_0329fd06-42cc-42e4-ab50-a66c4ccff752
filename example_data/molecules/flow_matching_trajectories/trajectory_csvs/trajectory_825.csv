step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,48,200
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,48,200
2,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,48,200
3,add,0.0,4,,,4,1,add 4 at position 0,flow_matching,0.3,2.0,48,200
4,replace,0.0,O,4,4,O,1,replace 4 at position 0 with O,flow_matching,0.3,2.0,48,200
5,add,1.0,O,,O,OO,2,add O at position 1,flow_matching,0.3,2.0,48,200
6,replace,1.0,=,O,OO,O=,2,replace <PERSON> at position 1 with =,flow_matching,0.3,2.0,48,200
7,replace,1.0,@,=,O=,O@,2,replace = at position 1 with @,flow_matching,0.3,2.0,48,200
8,remove,0.0,O,,O@,@,1,remove O from position 0,flow_matching,0.3,2.0,48,200
9,add,0.0,c,,@,c@,2,add c at position 0,flow_matching,0.3,2.0,48,200
10,add,0.0,O,,c@,Oc@,3,add O at position 0,flow_matching,0.3,2.0,48,200
11,remove,2.0,@,,Oc@,Oc,2,remove @ from position 2,flow_matching,0.3,2.0,48,200
12,replace,1.0,=,c,Oc,O=,2,replace c at position 1 with =,flow_matching,0.3,2.0,48,200
13,add,0.0,n,,O=,nO=,3,add n at position 0,flow_matching,0.3,2.0,48,200
14,add,3.0,3,,nO=,nO=3,4,add 3 at position 3,flow_matching,0.3,2.0,48,200
15,replace,0.0,O,n,nO=3,OO=3,4,replace n at position 0 with O,flow_matching,0.3,2.0,48,200
16,replace,3.0,N,3,OO=3,OO=N,4,replace 3 at position 3 with N,flow_matching,0.3,2.0,48,200
17,remove,1.0,O,,OO=N,O=N,3,remove O from position 1,flow_matching,0.3,2.0,48,200
18,add,2.0,\,,O=N,O=\N,4,add \ at position 2,flow_matching,0.3,2.0,48,200
19,replace,0.0,l,O,O=\N,l=\N,4,replace O at position 0 with l,flow_matching,0.3,2.0,48,200
20,replace,0.0,#,l,l=\N,#=\N,4,replace l at position 0 with #,flow_matching,0.3,2.0,48,200
21,replace,3.0,o,N,#=\N,#=\o,4,replace N at position 3 with o,flow_matching,0.3,2.0,48,200
22,replace,0.0,O,#,#=\o,O=\o,4,replace # at position 0 with O,flow_matching,0.3,2.0,48,200
23,replace,0.0,l,O,O=\o,l=\o,4,replace O at position 0 with l,flow_matching,0.3,2.0,48,200
24,remove,3.0,o,,l=\o,l=\,3,remove o from position 3,flow_matching,0.3,2.0,48,200
25,replace,0.0,O,l,l=\,O=\,3,replace l at position 0 with O,flow_matching,0.3,2.0,48,200
26,replace,2.0,7,\,O=\,O=7,3,replace \ at position 2 with 7,flow_matching,0.3,2.0,48,200
27,replace,2.0,n,7,O=7,O=n,3,replace 7 at position 2 with n,flow_matching,0.3,2.0,48,200
28,add,1.0,C,,O=n,OC=n,4,add C at position 1,flow_matching,0.3,2.0,48,200
29,replace,1.0,=,C,OC=n,O==n,4,replace C at position 1 with =,flow_matching,0.3,2.0,48,200
30,remove,0.0,O,,O==n,==n,3,remove O from position 0,flow_matching,0.3,2.0,48,200
31,add,3.0,n,,==n,==nn,4,add n at position 3,flow_matching,0.3,2.0,48,200
32,replace,3.0,),n,==nn,==n),4,replace n at position 3 with ),flow_matching,0.3,2.0,48,200
33,replace,0.0,O,=,==n),O=n),4,replace = at position 0 with O,flow_matching,0.3,2.0,48,200
34,remove,1.0,=,,O=n),On),3,remove = from position 1,flow_matching,0.3,2.0,48,200
35,add,3.0,c,,On),On)c,4,add c at position 3,flow_matching,0.3,2.0,48,200
36,replace,3.0,@,c,On)c,On)@,4,replace c at position 3 with @,flow_matching,0.3,2.0,48,200
37,add,2.0,6,,On)@,On6)@,5,add 6 at position 2,flow_matching,0.3,2.0,48,200
38,remove,2.0,6,,On6)@,On)@,4,remove 6 from position 2,flow_matching,0.3,2.0,48,200
39,replace,3.0,F,@,On)@,On)F,4,replace @ at position 3 with F,flow_matching,0.3,2.0,48,200
40,replace,1.0,=,n,On)F,O=)F,4,replace n at position 1 with =,flow_matching,0.3,2.0,48,200
41,remove,1.0,=,,O=)F,O)F,3,remove = from position 1,flow_matching,0.3,2.0,48,200
42,add,2.0,+,,O)F,O)+F,4,add + at position 2,flow_matching,0.3,2.0,48,200
43,remove,1.0,),,O)+F,O+F,3,remove ) from position 1,flow_matching,0.3,2.0,48,200
44,replace,0.0,N,O,O+F,N+F,3,replace O at position 0 with N,flow_matching,0.3,2.0,48,200
45,add,2.0,4,,N+F,N+4F,4,add 4 at position 2,flow_matching,0.3,2.0,48,200
46,replace,1.0,@,+,N+4F,N@4F,4,replace + at position 1 with @,flow_matching,0.3,2.0,48,200
47,replace,0.0,O,N,N@4F,O@4F,4,replace N at position 0 with O,flow_matching,0.3,2.0,48,200
48,add,3.0,I,,O@4F,O@4IF,5,add I at position 3,flow_matching,0.3,2.0,48,200
49,add,5.0,2,,O@4IF,O@4IF2,6,add 2 at position 5,flow_matching,0.3,2.0,48,200
50,replace,1.0,2,@,O@4IF2,O24IF2,6,replace @ at position 1 with 2,flow_matching,0.3,2.0,48,200
51,replace,3.0,[,I,O24IF2,O24[F2,6,replace I at position 3 with [,flow_matching,0.3,2.0,48,200
52,replace,0.0,2,O,O24[F2,224[F2,6,replace O at position 0 with 2,flow_matching,0.3,2.0,48,200
53,remove,0.0,2,,224[F2,24[F2,5,remove 2 from position 0,flow_matching,0.3,2.0,48,200
54,remove,3.0,F,,24[F2,24[2,4,remove F from position 3,flow_matching,0.3,2.0,48,200
55,replace,0.0,O,2,24[2,O4[2,4,replace 2 at position 0 with O,flow_matching,0.3,2.0,48,200
56,add,0.0,B,,O4[2,BO4[2,5,add B at position 0,flow_matching,0.3,2.0,48,200
57,replace,4.0,o,2,BO4[2,BO4[o,5,replace 2 at position 4 with o,flow_matching,0.3,2.0,48,200
58,remove,1.0,O,,BO4[o,B4[o,4,remove O from position 1,flow_matching,0.3,2.0,48,200
59,add,4.0,],,B4[o,B4[o],5,add ] at position 4,flow_matching,0.3,2.0,48,200
60,replace,0.0,5,B,B4[o],54[o],5,replace B at position 0 with 5,flow_matching,0.3,2.0,48,200
61,replace,0.0,O,5,54[o],O4[o],5,replace 5 at position 0 with O,flow_matching,0.3,2.0,48,200
62,replace,1.0,=,4,O4[o],O=[o],5,replace 4 at position 1 with =,flow_matching,0.3,2.0,48,200
63,replace,2.0,],[,O=[o],O=]o],5,replace [ at position 2 with ],flow_matching,0.3,2.0,48,200
64,replace,2.0,),],O=]o],O=)o],5,replace ] at position 2 with ),flow_matching,0.3,2.0,48,200
65,replace,3.0,6,o,O=)o],O=)6],5,replace o at position 3 with 6,flow_matching,0.3,2.0,48,200
66,replace,2.0,C,),O=)6],O=C6],5,replace ) at position 2 with C,flow_matching,0.3,2.0,48,200
67,remove,2.0,C,,O=C6],O=6],4,remove C from position 2,flow_matching,0.3,2.0,48,200
68,add,2.0,(,,O=6],O=(6],5,add ( at position 2,flow_matching,0.3,2.0,48,200
69,replace,1.0,7,=,O=(6],O7(6],5,replace = at position 1 with 7,flow_matching,0.3,2.0,48,200
70,remove,4.0,],,O7(6],O7(6,4,remove ] from position 4,flow_matching,0.3,2.0,48,200
71,remove,0.0,O,,O7(6,7(6,3,remove O from position 0,flow_matching,0.3,2.0,48,200
72,remove,2.0,6,,7(6,7(,2,remove 6 from position 2,flow_matching,0.3,2.0,48,200
73,add,0.0,1,,7(,17(,3,add 1 at position 0,flow_matching,0.3,2.0,48,200
74,replace,2.0,F,(,17(,17F,3,replace ( at position 2 with F,flow_matching,0.3,2.0,48,200
75,replace,0.0,O,1,17F,O7F,3,replace 1 at position 0 with O,flow_matching,0.3,2.0,48,200
76,replace,1.0,=,7,O7F,O=F,3,replace 7 at position 1 with =,flow_matching,0.3,2.0,48,200
77,replace,1.0,6,=,O=F,O6F,3,replace = at position 1 with 6,flow_matching,0.3,2.0,48,200
78,replace,1.0,=,6,O6F,O=F,3,replace 6 at position 1 with =,flow_matching,0.3,2.0,48,200
79,add,1.0,H,,O=F,OH=F,4,add H at position 1,flow_matching,0.3,2.0,48,200
80,replace,3.0,3,F,OH=F,OH=3,4,replace F at position 3 with 3,flow_matching,0.3,2.0,48,200
81,remove,3.0,3,,OH=3,OH=,3,remove 3 from position 3,flow_matching,0.3,2.0,48,200
82,remove,0.0,O,,OH=,H=,2,remove O from position 0,flow_matching,0.3,2.0,48,200
83,add,0.0,O,,H=,OH=,3,add O at position 0,flow_matching,0.3,2.0,48,200
84,replace,1.0,=,H,OH=,O==,3,replace H at position 1 with =,flow_matching,0.3,2.0,48,200
85,replace,2.0,C,=,O==,O=C,3,replace = at position 2 with C,flow_matching,0.3,2.0,48,200
86,add,3.0,(,,O=C,O=C(,4,add ( at position 3,flow_matching,0.3,2.0,48,200
87,remove,0.0,O,,O=C(,=C(,3,remove O from position 0,flow_matching,0.3,2.0,48,200
88,replace,0.0,-,=,=C(,-C(,3,replace = at position 0 with -,flow_matching,0.3,2.0,48,200
89,replace,0.0,O,-,-C(,OC(,3,replace - at position 0 with O,flow_matching,0.3,2.0,48,200
90,replace,1.0,=,C,OC(,O=(,3,replace C at position 1 with =,flow_matching,0.3,2.0,48,200
91,remove,0.0,O,,O=(,=(,2,remove O from position 0,flow_matching,0.3,2.0,48,200
92,replace,0.0,C,=,=(,C(,2,replace = at position 0 with C,flow_matching,0.3,2.0,48,200
93,replace,0.0,O,C,C(,O(,2,replace C at position 0 with O,flow_matching,0.3,2.0,48,200
94,remove,1.0,(,,O(,O,1,remove ( from position 1,flow_matching,0.3,2.0,48,200
95,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,48,200
96,replace,0.0,l,O,O=,l=,2,replace O at position 0 with l,flow_matching,0.3,2.0,48,200
97,remove,1.0,=,,l=,l,1,remove = from position 1,flow_matching,0.3,2.0,48,200
98,replace,0.0,O,l,l,O,1,replace l at position 0 with O,flow_matching,0.3,2.0,48,200
99,add,0.0,[,,O,[O,2,add [ at position 0,flow_matching,0.3,2.0,48,200
100,replace,0.0,S,[,[O,SO,2,replace [ at position 0 with S,flow_matching,0.3,2.0,48,200
101,remove,0.0,S,,SO,O,1,remove S from position 0,flow_matching,0.3,2.0,48,200
102,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,48,200
103,remove,1.0,=,,O=,O,1,remove = from position 1,flow_matching,0.3,2.0,48,200
104,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,48,200
105,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,48,200
106,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,48,200
107,replace,0.0,O,C,CO,OO,2,replace C at position 0 with O,flow_matching,0.3,2.0,48,200
108,replace,1.0,=,O,OO,O=,2,replace O at position 1 with =,flow_matching,0.3,2.0,48,200
109,add,2.0,C,,O=,O=C,3,add C at position 2,flow_matching,0.3,2.0,48,200
110,remove,0.0,O,,O=C,=C,2,remove O from position 0,flow_matching,0.3,2.0,48,200
111,replace,0.0,4,=,=C,4C,2,replace = at position 0 with 4,flow_matching,0.3,2.0,48,200
112,add,2.0,=,,4C,4C=,3,add = at position 2,flow_matching,0.3,2.0,48,200
113,replace,2.0,H,=,4C=,4CH,3,replace = at position 2 with H,flow_matching,0.3,2.0,48,200
114,replace,0.0,\,4,4CH,\CH,3,replace 4 at position 0 with \,flow_matching,0.3,2.0,48,200
115,add,2.0,2,,\CH,\C2H,4,add 2 at position 2,flow_matching,0.3,2.0,48,200
116,remove,1.0,C,,\C2H,\2H,3,remove C from position 1,flow_matching,0.3,2.0,48,200
117,replace,2.0,#,H,\2H,\2#,3,replace H at position 2 with #,flow_matching,0.3,2.0,48,200
118,remove,0.0,\,,\2#,2#,2,remove \ from position 0,flow_matching,0.3,2.0,48,200
119,replace,0.0,O,2,2#,O#,2,replace 2 at position 0 with O,flow_matching,0.3,2.0,48,200
120,replace,0.0,H,O,O#,H#,2,replace O at position 0 with H,flow_matching,0.3,2.0,48,200
121,remove,1.0,#,,H#,H,1,remove # from position 1,flow_matching,0.3,2.0,48,200
122,remove,0.0,H,,H,,0,remove H from position 0,flow_matching,0.3,2.0,48,200
123,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,48,200
124,replace,0.0,O,],],O,1,replace ] at position 0 with O,flow_matching,0.3,2.0,48,200
125,add,0.0,\,,O,\O,2,add \ at position 0,flow_matching,0.3,2.0,48,200
126,replace,0.0,s,\,\O,sO,2,replace \ at position 0 with s,flow_matching,0.3,2.0,48,200
127,remove,1.0,O,,sO,s,1,remove O from position 1,flow_matching,0.3,2.0,48,200
128,replace,0.0,O,s,s,O,1,replace s at position 0 with O,flow_matching,0.3,2.0,48,200
129,add,1.0,O,,O,OO,2,add O at position 1,flow_matching,0.3,2.0,48,200
130,remove,0.0,O,,OO,O,1,remove O from position 0,flow_matching,0.3,2.0,48,200
131,add,0.0,],,O,]O,2,add ] at position 0,flow_matching,0.3,2.0,48,200
132,add,0.0,1,,]O,1]O,3,add 1 at position 0,flow_matching,0.3,2.0,48,200
133,replace,0.0,O,1,1]O,O]O,3,replace 1 at position 0 with O,flow_matching,0.3,2.0,48,200
134,add,2.0,B,,O]O,O]BO,4,add B at position 2,flow_matching,0.3,2.0,48,200
135,replace,0.0,5,O,O]BO,5]BO,4,replace O at position 0 with 5,flow_matching,0.3,2.0,48,200
136,add,2.0,H,,5]BO,5]HBO,5,add H at position 2,flow_matching,0.3,2.0,48,200
137,replace,0.0,O,5,5]HBO,O]HBO,5,replace 5 at position 0 with O,flow_matching,0.3,2.0,48,200
138,remove,0.0,O,,O]HBO,]HBO,4,remove O from position 0,flow_matching,0.3,2.0,48,200
139,replace,3.0,B,O,]HBO,]HBB,4,replace O at position 3 with B,flow_matching,0.3,2.0,48,200
140,add,4.0,(,,]HBB,]HBB(,5,add ( at position 4,flow_matching,0.3,2.0,48,200
141,replace,0.0,O,],]HBB(,OHBB(,5,replace ] at position 0 with O,flow_matching,0.3,2.0,48,200
142,add,3.0,#,,OHBB(,OHB#B(,6,add # at position 3,flow_matching,0.3,2.0,48,200
143,add,2.0,C,,OHB#B(,OHCB#B(,7,add C at position 2,flow_matching,0.3,2.0,48,200
144,remove,2.0,C,,OHCB#B(,OHB#B(,6,remove C from position 2,flow_matching,0.3,2.0,48,200
145,replace,0.0,@,O,OHB#B(,@HB#B(,6,replace O at position 0 with @,flow_matching,0.3,2.0,48,200
146,replace,1.0,\,H,@HB#B(,@\B#B(,6,replace H at position 1 with \,flow_matching,0.3,2.0,48,200
147,replace,0.0,C,@,@\B#B(,C\B#B(,6,replace @ at position 0 with C,flow_matching,0.3,2.0,48,200
148,replace,0.0,O,C,C\B#B(,O\B#B(,6,replace C at position 0 with O,flow_matching,0.3,2.0,48,200
149,remove,2.0,B,,O\B#B(,O\#B(,5,remove B from position 2,flow_matching,0.3,2.0,48,200
150,remove,0.0,O,,O\#B(,\#B(,4,remove O from position 0,flow_matching,0.3,2.0,48,200
151,remove,2.0,B,,\#B(,\#(,3,remove B from position 2,flow_matching,0.3,2.0,48,200
152,add,3.0,o,,\#(,\#(o,4,add o at position 3,flow_matching,0.3,2.0,48,200
153,replace,0.0,O,\,\#(o,O#(o,4,replace \ at position 0 with O,flow_matching,0.3,2.0,48,200
154,replace,1.0,=,#,O#(o,O=(o,4,replace # at position 1 with =,flow_matching,0.3,2.0,48,200
155,replace,2.0,C,(,O=(o,O=Co,4,replace ( at position 2 with C,flow_matching,0.3,2.0,48,200
156,replace,3.0,(,o,O=Co,O=C(,4,replace o at position 3 with (,flow_matching,0.3,2.0,48,200
157,add,4.0,C,,O=C(,O=C(C,5,add C at position 4,flow_matching,0.3,2.0,48,200
158,add,5.0,O,,O=C(C,O=C(CO,6,add O at position 5,flow_matching,0.3,2.0,48,200
159,add,6.0,C,,O=C(CO,O=C(COC,7,add C at position 6,flow_matching,0.3,2.0,48,200
160,add,7.0,(,,O=C(COC,O=C(COC(,8,add ( at position 7,flow_matching,0.3,2.0,48,200
161,add,8.0,=,,O=C(COC(,O=C(COC(=,9,add = at position 8,flow_matching,0.3,2.0,48,200
162,add,9.0,O,,O=C(COC(=,O=C(COC(=O,10,add O at position 9,flow_matching,0.3,2.0,48,200
163,add,10.0,),,O=C(COC(=O,O=C(COC(=O),11,add ) at position 10,flow_matching,0.3,2.0,48,200
164,add,11.0,c,,O=C(COC(=O),O=C(COC(=O)c,12,add c at position 11,flow_matching,0.3,2.0,48,200
165,add,12.0,1,,O=C(COC(=O)c,O=C(COC(=O)c1,13,add 1 at position 12,flow_matching,0.3,2.0,48,200
166,add,13.0,c,,O=C(COC(=O)c1,O=C(COC(=O)c1c,14,add c at position 13,flow_matching,0.3,2.0,48,200
167,add,14.0,c,,O=C(COC(=O)c1c,O=C(COC(=O)c1cc,15,add c at position 14,flow_matching,0.3,2.0,48,200
168,add,15.0,c,,O=C(COC(=O)c1cc,O=C(COC(=O)c1ccc,16,add c at position 15,flow_matching,0.3,2.0,48,200
169,add,16.0,(,,O=C(COC(=O)c1ccc,O=C(COC(=O)c1ccc(,17,add ( at position 16,flow_matching,0.3,2.0,48,200
170,add,17.0,C,,O=C(COC(=O)c1ccc(,O=C(COC(=O)c1ccc(C,18,add C at position 17,flow_matching,0.3,2.0,48,200
171,add,18.0,l,,O=C(COC(=O)c1ccc(C,O=C(COC(=O)c1ccc(Cl,19,add l at position 18,flow_matching,0.3,2.0,48,200
172,add,19.0,),,O=C(COC(=O)c1ccc(Cl,O=C(COC(=O)c1ccc(Cl),20,add ) at position 19,flow_matching,0.3,2.0,48,200
173,add,20.0,n,,O=C(COC(=O)c1ccc(Cl),O=C(COC(=O)c1ccc(Cl)n,21,add n at position 20,flow_matching,0.3,2.0,48,200
174,add,21.0,c,,O=C(COC(=O)c1ccc(Cl)n,O=C(COC(=O)c1ccc(Cl)nc,22,add c at position 21,flow_matching,0.3,2.0,48,200
175,add,22.0,1,,O=C(COC(=O)c1ccc(Cl)nc,O=C(COC(=O)c1ccc(Cl)nc1,23,add 1 at position 22,flow_matching,0.3,2.0,48,200
176,add,23.0,),,O=C(COC(=O)c1ccc(Cl)nc1,O=C(COC(=O)c1ccc(Cl)nc1),24,add ) at position 23,flow_matching,0.3,2.0,48,200
177,add,24.0,N,,O=C(COC(=O)c1ccc(Cl)nc1),O=C(COC(=O)c1ccc(Cl)nc1)N,25,add N at position 24,flow_matching,0.3,2.0,48,200
178,add,25.0,C,,O=C(COC(=O)c1ccc(Cl)nc1)N,O=C(COC(=O)c1ccc(Cl)nc1)NC,26,add C at position 25,flow_matching,0.3,2.0,48,200
179,add,26.0,(,,O=C(COC(=O)c1ccc(Cl)nc1)NC,O=C(COC(=O)c1ccc(Cl)nc1)NC(,27,add ( at position 26,flow_matching,0.3,2.0,48,200
180,add,27.0,=,,O=C(COC(=O)c1ccc(Cl)nc1)NC(,O=C(COC(=O)c1ccc(Cl)nc1)NC(=,28,add = at position 27,flow_matching,0.3,2.0,48,200
181,add,28.0,O,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O,29,add O at position 28,flow_matching,0.3,2.0,48,200
182,add,29.0,),,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O),30,add ) at position 29,flow_matching,0.3,2.0,48,200
183,add,30.0,N,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O),O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)N,31,add N at position 30,flow_matching,0.3,2.0,48,200
184,add,31.0,c,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)N,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc,32,add c at position 31,flow_matching,0.3,2.0,48,200
185,add,32.0,1,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1,33,add 1 at position 32,flow_matching,0.3,2.0,48,200
186,add,33.0,c,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1c,34,add c at position 33,flow_matching,0.3,2.0,48,200
187,add,34.0,c,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1c,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1cc,35,add c at position 34,flow_matching,0.3,2.0,48,200
188,add,35.0,c,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1cc,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc,36,add c at position 35,flow_matching,0.3,2.0,48,200
189,add,36.0,2,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2,37,add 2 at position 36,flow_matching,0.3,2.0,48,200
190,add,37.0,c,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c,38,add c at position 37,flow_matching,0.3,2.0,48,200
191,add,38.0,(,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(,39,add ( at position 38,flow_matching,0.3,2.0,48,200
192,add,39.0,c,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c,40,add c at position 39,flow_matching,0.3,2.0,48,200
193,add,40.0,1,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1,41,add 1 at position 40,flow_matching,0.3,2.0,48,200
194,add,41.0,),,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1),42,add ) at position 41,flow_matching,0.3,2.0,48,200
195,add,42.0,O,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1),O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)O,43,add O at position 42,flow_matching,0.3,2.0,48,200
196,add,43.0,C,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)O,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)OC,44,add C at position 43,flow_matching,0.3,2.0,48,200
197,add,44.0,C,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)OC,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)OCC,45,add C at position 44,flow_matching,0.3,2.0,48,200
198,add,45.0,O,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)OCC,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)OCCO,46,add O at position 45,flow_matching,0.3,2.0,48,200
199,add,46.0,2,,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)OCCO,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)OCCO2,47,add 2 at position 46,flow_matching,0.3,2.0,48,200
200,add,47.0,"
",,O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)OCCO2,"O=C(COC(=O)c1ccc(Cl)nc1)NC(=O)Nc1ccc2c(c1)OCCO2
",48,"add 
 at position 47",flow_matching,0.3,2.0,48,200
