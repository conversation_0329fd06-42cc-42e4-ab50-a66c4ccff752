step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,29,109
1,add,0.0,s,,,s,1,add s at position 0,flow_matching,0.3,2.0,29,109
2,add,0.0,\,,s,\s,2,add \ at position 0,flow_matching,0.3,2.0,29,109
3,remove,0.0,\,,\s,s,1,remove \ from position 0,flow_matching,0.3,2.0,29,109
4,replace,0.0,C,s,s,C,1,replace s at position 0 with C,flow_matching,0.3,2.0,29,109
5,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,29,109
6,add,0.0,C,,,C,1,add <PERSON> at position 0,flow_matching,0.3,2.0,29,109
7,replace,0.0,=,C,C,=,1,replace <PERSON> at position 0 with =,flow_matching,0.3,2.0,29,109
8,replace,0.0,C,=,=,C,1,replace = at position 0 with C,flow_matching,0.3,2.0,29,109
9,add,1.0,H,,C,CH,2,add H at position 1,flow_matching,0.3,2.0,29,109
10,replace,1.0,C,H,CH,CC,2,replace H at position 1 with C,flow_matching,0.3,2.0,29,109
11,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,29,109
12,replace,0.0,I,C,CC(,IC(,3,replace C at position 0 with I,flow_matching,0.3,2.0,29,109
13,replace,1.0,n,C,IC(,In(,3,replace C at position 1 with n,flow_matching,0.3,2.0,29,109
14,add,2.0,#,,In(,In#(,4,add # at position 2,flow_matching,0.3,2.0,29,109
15,replace,0.0,C,I,In#(,Cn#(,4,replace I at position 0 with C,flow_matching,0.3,2.0,29,109
16,replace,3.0,B,(,Cn#(,Cn#B,4,replace ( at position 3 with B,flow_matching,0.3,2.0,29,109
17,replace,0.0,#,C,Cn#B,#n#B,4,replace C at position 0 with #,flow_matching,0.3,2.0,29,109
18,replace,0.0,C,#,#n#B,Cn#B,4,replace # at position 0 with C,flow_matching,0.3,2.0,29,109
19,replace,1.0,=,n,Cn#B,C=#B,4,replace n at position 1 with =,flow_matching,0.3,2.0,29,109
20,replace,0.0,r,C,C=#B,r=#B,4,replace C at position 0 with r,flow_matching,0.3,2.0,29,109
21,replace,0.0,C,r,r=#B,C=#B,4,replace r at position 0 with C,flow_matching,0.3,2.0,29,109
22,replace,1.0,C,=,C=#B,CC#B,4,replace = at position 1 with C,flow_matching,0.3,2.0,29,109
23,remove,1.0,C,,CC#B,C#B,3,remove C from position 1,flow_matching,0.3,2.0,29,109
24,remove,2.0,B,,C#B,C#,2,remove B from position 2,flow_matching,0.3,2.0,29,109
25,replace,1.0,C,#,C#,CC,2,replace # at position 1 with C,flow_matching,0.3,2.0,29,109
26,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,29,109
27,add,1.0,O,,CC(,COC(,4,add O at position 1,flow_matching,0.3,2.0,29,109
28,replace,1.0,C,O,COC(,CCC(,4,replace O at position 1 with C,flow_matching,0.3,2.0,29,109
29,remove,1.0,C,,CCC(,CC(,3,remove C from position 1,flow_matching,0.3,2.0,29,109
30,remove,0.0,C,,CC(,C(,2,remove C from position 0,flow_matching,0.3,2.0,29,109
31,remove,1.0,(,,C(,C,1,remove ( from position 1,flow_matching,0.3,2.0,29,109
32,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,29,109
33,add,0.0,S,,CC,SCC,3,add S at position 0,flow_matching,0.3,2.0,29,109
34,replace,0.0,C,S,SCC,CCC,3,replace S at position 0 with C,flow_matching,0.3,2.0,29,109
35,remove,2.0,C,,CCC,CC,2,remove C from position 2,flow_matching,0.3,2.0,29,109
36,replace,0.0,c,C,CC,cC,2,replace C at position 0 with c,flow_matching,0.3,2.0,29,109
37,remove,0.0,c,,cC,C,1,remove c from position 0,flow_matching,0.3,2.0,29,109
38,replace,0.0,-,C,C,-,1,replace C at position 0 with -,flow_matching,0.3,2.0,29,109
39,remove,0.0,-,,-,,0,remove - from position 0,flow_matching,0.3,2.0,29,109
40,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,29,109
41,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,29,109
42,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,29,109
43,add,3.0,C,,CC(,CC(C,4,add C at position 3,flow_matching,0.3,2.0,29,109
44,add,4.0,),,CC(C,CC(C),5,add ) at position 4,flow_matching,0.3,2.0,29,109
45,add,3.0,c,,CC(C),CC(cC),6,add c at position 3,flow_matching,0.3,2.0,29,109
46,add,2.0,/,,CC(cC),CC/(cC),7,add / at position 2,flow_matching,0.3,2.0,29,109
47,replace,2.0,(,/,CC/(cC),CC((cC),7,replace / at position 2 with (,flow_matching,0.3,2.0,29,109
48,replace,3.0,C,(,CC((cC),CC(CcC),7,replace ( at position 3 with C,flow_matching,0.3,2.0,29,109
49,remove,1.0,C,,CC(CcC),C(CcC),6,remove C from position 1,flow_matching,0.3,2.0,29,109
50,add,4.0,r,,C(CcC),C(CcrC),7,add r at position 4,flow_matching,0.3,2.0,29,109
51,replace,1.0,C,(,C(CcrC),CCCcrC),7,replace ( at position 1 with C,flow_matching,0.3,2.0,29,109
52,add,6.0,/,,CCCcrC),CCCcrC/),8,add / at position 6,flow_matching,0.3,2.0,29,109
53,replace,2.0,(,C,CCCcrC/),CC(crC/),8,replace C at position 2 with (,flow_matching,0.3,2.0,29,109
54,replace,3.0,C,c,CC(crC/),CC(CrC/),8,replace c at position 3 with C,flow_matching,0.3,2.0,29,109
55,remove,3.0,C,,CC(CrC/),CC(rC/),7,remove C from position 3,flow_matching,0.3,2.0,29,109
56,add,3.0,2,,CC(rC/),CC(2rC/),8,add 2 at position 3,flow_matching,0.3,2.0,29,109
57,replace,3.0,C,2,CC(2rC/),CC(CrC/),8,replace 2 at position 3 with C,flow_matching,0.3,2.0,29,109
58,replace,7.0,o,),CC(CrC/),CC(CrC/o,8,replace ) at position 7 with o,flow_matching,0.3,2.0,29,109
59,remove,4.0,r,,CC(CrC/o,CC(CC/o,7,remove r from position 4,flow_matching,0.3,2.0,29,109
60,replace,5.0,l,/,CC(CC/o,CC(CClo,7,replace / at position 5 with l,flow_matching,0.3,2.0,29,109
61,replace,3.0,=,C,CC(CClo,CC(=Clo,7,replace C at position 3 with =,flow_matching,0.3,2.0,29,109
62,remove,5.0,l,,CC(=Clo,CC(=Co,6,remove l from position 5,flow_matching,0.3,2.0,29,109
63,replace,4.0,1,C,CC(=Co,CC(=1o,6,replace C at position 4 with 1,flow_matching,0.3,2.0,29,109
64,replace,3.0,C,=,CC(=1o,CC(C1o,6,replace = at position 3 with C,flow_matching,0.3,2.0,29,109
65,add,5.0,O,,CC(C1o,CC(C1Oo,7,add O at position 5,flow_matching,0.3,2.0,29,109
66,replace,4.0,7,1,CC(C1Oo,CC(C7Oo,7,replace 1 at position 4 with 7,flow_matching,0.3,2.0,29,109
67,replace,6.0,+,o,CC(C7Oo,CC(C7O+,7,replace o at position 6 with +,flow_matching,0.3,2.0,29,109
68,add,1.0,r,,CC(C7O+,CrC(C7O+,8,add r at position 1,flow_matching,0.3,2.0,29,109
69,remove,1.0,r,,CrC(C7O+,CC(C7O+,7,remove r from position 1,flow_matching,0.3,2.0,29,109
70,add,4.0,5,,CC(C7O+,CC(C57O+,8,add 5 at position 4,flow_matching,0.3,2.0,29,109
71,add,3.0,1,,CC(C57O+,CC(1C57O+,9,add 1 at position 3,flow_matching,0.3,2.0,29,109
72,replace,7.0,(,O,CC(1C57O+,CC(1C57(+,9,replace O at position 7 with (,flow_matching,0.3,2.0,29,109
73,replace,8.0,r,+,CC(1C57(+,CC(1C57(r,9,replace + at position 8 with r,flow_matching,0.3,2.0,29,109
74,replace,5.0,#,5,CC(1C57(r,CC(1C#7(r,9,replace 5 at position 5 with #,flow_matching,0.3,2.0,29,109
75,add,3.0,F,,CC(1C#7(r,CC(F1C#7(r,10,add F at position 3,flow_matching,0.3,2.0,29,109
76,replace,3.0,C,F,CC(F1C#7(r,CC(C1C#7(r,10,replace F at position 3 with C,flow_matching,0.3,2.0,29,109
77,add,2.0,F,,CC(C1C#7(r,CCF(C1C#7(r,11,add F at position 2,flow_matching,0.3,2.0,29,109
78,add,11.0,S,,CCF(C1C#7(r,CCF(C1C#7(rS,12,add S at position 11,flow_matching,0.3,2.0,29,109
79,replace,10.0,N,r,CCF(C1C#7(rS,CCF(C1C#7(NS,12,replace r at position 10 with N,flow_matching,0.3,2.0,29,109
80,remove,5.0,1,,CCF(C1C#7(NS,CCF(CC#7(NS,11,remove 1 from position 5,flow_matching,0.3,2.0,29,109
81,add,10.0,F,,CCF(CC#7(NS,CCF(CC#7(NFS,12,add F at position 10,flow_matching,0.3,2.0,29,109
82,add,8.0,S,,CCF(CC#7(NFS,CCF(CC#7S(NFS,13,add S at position 8,flow_matching,0.3,2.0,29,109
83,remove,8.0,S,,CCF(CC#7S(NFS,CCF(CC#7(NFS,12,remove S from position 8,flow_matching,0.3,2.0,29,109
84,replace,2.0,(,F,CCF(CC#7(NFS,CC((CC#7(NFS,12,replace F at position 2 with (,flow_matching,0.3,2.0,29,109
85,replace,3.0,C,(,CC((CC#7(NFS,CC(CCC#7(NFS,12,replace ( at position 3 with C,flow_matching,0.3,2.0,29,109
86,replace,4.0,),C,CC(CCC#7(NFS,CC(C)C#7(NFS,12,replace C at position 4 with ),flow_matching,0.3,2.0,29,109
87,replace,6.0,O,#,CC(C)C#7(NFS,CC(C)CO7(NFS,12,replace # at position 6 with O,flow_matching,0.3,2.0,29,109
88,replace,7.0,N,7,CC(C)CO7(NFS,CC(C)CON(NFS,12,replace 7 at position 7 with N,flow_matching,0.3,2.0,29,109
89,replace,8.0,c,(,CC(C)CON(NFS,CC(C)CONcNFS,12,replace ( at position 8 with c,flow_matching,0.3,2.0,29,109
90,replace,9.0,1,N,CC(C)CONcNFS,CC(C)CONc1FS,12,replace N at position 9 with 1,flow_matching,0.3,2.0,29,109
91,replace,10.0,n,F,CC(C)CONc1FS,CC(C)CONc1nS,12,replace F at position 10 with n,flow_matching,0.3,2.0,29,109
92,replace,11.0,c,S,CC(C)CONc1nS,CC(C)CONc1nc,12,replace S at position 11 with c,flow_matching,0.3,2.0,29,109
93,add,12.0,n,,CC(C)CONc1nc,CC(C)CONc1ncn,13,add n at position 12,flow_matching,0.3,2.0,29,109
94,add,13.0,c,,CC(C)CONc1ncn,CC(C)CONc1ncnc,14,add c at position 13,flow_matching,0.3,2.0,29,109
95,add,14.0,2,,CC(C)CONc1ncnc,CC(C)CONc1ncnc2,15,add 2 at position 14,flow_matching,0.3,2.0,29,109
96,add,15.0,s,,CC(C)CONc1ncnc2,CC(C)CONc1ncnc2s,16,add s at position 15,flow_matching,0.3,2.0,29,109
97,add,16.0,c,,CC(C)CONc1ncnc2s,CC(C)CONc1ncnc2sc,17,add c at position 16,flow_matching,0.3,2.0,29,109
98,add,17.0,3,,CC(C)CONc1ncnc2sc,CC(C)CONc1ncnc2sc3,18,add 3 at position 17,flow_matching,0.3,2.0,29,109
99,add,18.0,c,,CC(C)CONc1ncnc2sc3,CC(C)CONc1ncnc2sc3c,19,add c at position 18,flow_matching,0.3,2.0,29,109
100,add,19.0,(,,CC(C)CONc1ncnc2sc3c,CC(C)CONc1ncnc2sc3c(,20,add ( at position 19,flow_matching,0.3,2.0,29,109
101,add,20.0,c,,CC(C)CONc1ncnc2sc3c(,CC(C)CONc1ncnc2sc3c(c,21,add c at position 20,flow_matching,0.3,2.0,29,109
102,add,21.0,1,,CC(C)CONc1ncnc2sc3c(c,CC(C)CONc1ncnc2sc3c(c1,22,add 1 at position 21,flow_matching,0.3,2.0,29,109
103,add,22.0,2,,CC(C)CONc1ncnc2sc3c(c1,CC(C)CONc1ncnc2sc3c(c12,23,add 2 at position 22,flow_matching,0.3,2.0,29,109
104,add,23.0,),,CC(C)CONc1ncnc2sc3c(c12,CC(C)CONc1ncnc2sc3c(c12),24,add ) at position 23,flow_matching,0.3,2.0,29,109
105,add,24.0,C,,CC(C)CONc1ncnc2sc3c(c12),CC(C)CONc1ncnc2sc3c(c12)C,25,add C at position 24,flow_matching,0.3,2.0,29,109
106,add,25.0,C,,CC(C)CONc1ncnc2sc3c(c12)C,CC(C)CONc1ncnc2sc3c(c12)CC,26,add C at position 25,flow_matching,0.3,2.0,29,109
107,add,26.0,C,,CC(C)CONc1ncnc2sc3c(c12)CC,CC(C)CONc1ncnc2sc3c(c12)CCC,27,add C at position 26,flow_matching,0.3,2.0,29,109
108,add,27.0,3,,CC(C)CONc1ncnc2sc3c(c12)CCC,CC(C)CONc1ncnc2sc3c(c12)CCC3,28,add 3 at position 27,flow_matching,0.3,2.0,29,109
109,add,28.0,"
",,CC(C)CONc1ncnc2sc3c(c12)CCC3,"CC(C)CONc1ncnc2sc3c(c12)CCC3
",29,"add 
 at position 28",flow_matching,0.3,2.0,29,109
