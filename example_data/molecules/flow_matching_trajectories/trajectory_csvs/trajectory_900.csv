step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,147
1,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,43,147
2,add,1.0,o,,3,3o,2,add o at position 1,flow_matching,0.3,2.0,43,147
3,remove,1.0,o,,3o,3,1,remove o from position 1,flow_matching,0.3,2.0,43,147
4,replace,0.0,C,3,3,C,1,replace 3 at position 0 with <PERSON>,flow_matching,0.3,2.0,43,147
5,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,43,147
6,add,0.0,5,,CO,5CO,3,add 5 at position 0,flow_matching,0.3,2.0,43,147
7,replace,2.0,n,O,5CO,5Cn,3,replace <PERSON> at position 2 with n,flow_matching,0.3,2.0,43,147
8,add,0.0,C,,5Cn,C5Cn,4,add C at position 0,flow_matching,0.3,2.0,43,147
9,add,3.0,),,C5Cn,C5C)n,5,add ) at position 3,flow_matching,0.3,2.0,43,147
10,replace,1.0,O,5,C5C)n,COC)n,5,replace 5 at position 1 with O,flow_matching,0.3,2.0,43,147
11,replace,2.0,c,C,COC)n,COc)n,5,replace C at position 2 with c,flow_matching,0.3,2.0,43,147
12,replace,3.0,1,),COc)n,COc1n,5,replace ) at position 3 with 1,flow_matching,0.3,2.0,43,147
13,replace,4.0,c,n,COc1n,COc1c,5,replace n at position 4 with c,flow_matching,0.3,2.0,43,147
14,remove,1.0,O,,COc1c,Cc1c,4,remove O from position 1,flow_matching,0.3,2.0,43,147
15,remove,0.0,C,,Cc1c,c1c,3,remove C from position 0,flow_matching,0.3,2.0,43,147
16,replace,1.0,I,1,c1c,cIc,3,replace 1 at position 1 with I,flow_matching,0.3,2.0,43,147
17,replace,0.0,C,c,cIc,CIc,3,replace c at position 0 with C,flow_matching,0.3,2.0,43,147
18,replace,1.0,O,I,CIc,COc,3,replace I at position 1 with O,flow_matching,0.3,2.0,43,147
19,replace,1.0,H,O,COc,CHc,3,replace O at position 1 with H,flow_matching,0.3,2.0,43,147
20,add,2.0,o,,CHc,CHoc,4,add o at position 2,flow_matching,0.3,2.0,43,147
21,replace,1.0,+,H,CHoc,C+oc,4,replace H at position 1 with +,flow_matching,0.3,2.0,43,147
22,add,1.0,[,,C+oc,C[+oc,5,add [ at position 1,flow_matching,0.3,2.0,43,147
23,add,0.0,l,,C[+oc,lC[+oc,6,add l at position 0,flow_matching,0.3,2.0,43,147
24,remove,5.0,c,,lC[+oc,lC[+o,5,remove c from position 5,flow_matching,0.3,2.0,43,147
25,replace,3.0,5,+,lC[+o,lC[5o,5,replace + at position 3 with 5,flow_matching,0.3,2.0,43,147
26,replace,0.0,C,l,lC[5o,CC[5o,5,replace l at position 0 with C,flow_matching,0.3,2.0,43,147
27,add,0.0,7,,CC[5o,7CC[5o,6,add 7 at position 0,flow_matching,0.3,2.0,43,147
28,add,5.0,I,,7CC[5o,7CC[5Io,7,add I at position 5,flow_matching,0.3,2.0,43,147
29,add,7.0,N,,7CC[5Io,7CC[5IoN,8,add N at position 7,flow_matching,0.3,2.0,43,147
30,replace,0.0,@,7,7CC[5IoN,@CC[5IoN,8,replace 7 at position 0 with @,flow_matching,0.3,2.0,43,147
31,replace,0.0,C,@,@CC[5IoN,CCC[5IoN,8,replace @ at position 0 with C,flow_matching,0.3,2.0,43,147
32,replace,5.0,N,I,CCC[5IoN,CCC[5NoN,8,replace I at position 5 with N,flow_matching,0.3,2.0,43,147
33,remove,7.0,N,,CCC[5NoN,CCC[5No,7,remove N from position 7,flow_matching,0.3,2.0,43,147
34,add,3.0,2,,CCC[5No,CCC2[5No,8,add 2 at position 3,flow_matching,0.3,2.0,43,147
35,remove,3.0,2,,CCC2[5No,CCC[5No,7,remove 2 from position 3,flow_matching,0.3,2.0,43,147
36,remove,6.0,o,,CCC[5No,CCC[5N,6,remove o from position 6,flow_matching,0.3,2.0,43,147
37,replace,1.0,O,C,CCC[5N,COC[5N,6,replace C at position 1 with O,flow_matching,0.3,2.0,43,147
38,add,1.0,-,,COC[5N,C-OC[5N,7,add - at position 1,flow_matching,0.3,2.0,43,147
39,remove,2.0,O,,C-OC[5N,C-C[5N,6,remove O from position 2,flow_matching,0.3,2.0,43,147
40,replace,1.0,c,-,C-C[5N,CcC[5N,6,replace - at position 1 with c,flow_matching,0.3,2.0,43,147
41,add,1.0,),,CcC[5N,C)cC[5N,7,add ) at position 1,flow_matching,0.3,2.0,43,147
42,replace,1.0,O,),C)cC[5N,COcC[5N,7,replace ) at position 1 with O,flow_matching,0.3,2.0,43,147
43,add,4.0,/,,COcC[5N,COcC/[5N,8,add / at position 4,flow_matching,0.3,2.0,43,147
44,replace,3.0,-,C,COcC/[5N,COc-/[5N,8,replace C at position 3 with -,flow_matching,0.3,2.0,43,147
45,replace,3.0,1,-,COc-/[5N,COc1/[5N,8,replace - at position 3 with 1,flow_matching,0.3,2.0,43,147
46,remove,2.0,c,,COc1/[5N,CO1/[5N,7,remove c from position 2,flow_matching,0.3,2.0,43,147
47,remove,0.0,C,,CO1/[5N,O1/[5N,6,remove C from position 0,flow_matching,0.3,2.0,43,147
48,add,6.0,S,,O1/[5N,O1/[5NS,7,add S at position 6,flow_matching,0.3,2.0,43,147
49,add,7.0,I,,O1/[5NS,O1/[5NSI,8,add I at position 7,flow_matching,0.3,2.0,43,147
50,replace,0.0,C,O,O1/[5NSI,C1/[5NSI,8,replace O at position 0 with C,flow_matching,0.3,2.0,43,147
51,remove,2.0,/,,C1/[5NSI,C1[5NSI,7,remove / from position 2,flow_matching,0.3,2.0,43,147
52,replace,1.0,c,1,C1[5NSI,Cc[5NSI,7,replace 1 at position 1 with c,flow_matching,0.3,2.0,43,147
53,add,1.0,F,,Cc[5NSI,CFc[5NSI,8,add F at position 1,flow_matching,0.3,2.0,43,147
54,remove,6.0,S,,CFc[5NSI,CFc[5NI,7,remove S from position 6,flow_matching,0.3,2.0,43,147
55,replace,1.0,O,F,CFc[5NI,COc[5NI,7,replace F at position 1 with O,flow_matching,0.3,2.0,43,147
56,add,3.0,4,,COc[5NI,COc4[5NI,8,add 4 at position 3,flow_matching,0.3,2.0,43,147
57,replace,3.0,1,4,COc4[5NI,COc1[5NI,8,replace 4 at position 3 with 1,flow_matching,0.3,2.0,43,147
58,add,0.0,F,,COc1[5NI,FCOc1[5NI,9,add F at position 0,flow_matching,0.3,2.0,43,147
59,remove,8.0,I,,FCOc1[5NI,FCOc1[5N,8,remove I from position 8,flow_matching,0.3,2.0,43,147
60,add,1.0,2,,FCOc1[5N,F2COc1[5N,9,add 2 at position 1,flow_matching,0.3,2.0,43,147
61,add,9.0,1,,F2COc1[5N,F2COc1[5N1,10,add 1 at position 9,flow_matching,0.3,2.0,43,147
62,replace,2.0,l,C,F2COc1[5N1,F2lOc1[5N1,10,replace C at position 2 with l,flow_matching,0.3,2.0,43,147
63,replace,9.0,],1,F2lOc1[5N1,F2lOc1[5N],10,replace 1 at position 9 with ],flow_matching,0.3,2.0,43,147
64,replace,0.0,C,F,F2lOc1[5N],C2lOc1[5N],10,replace F at position 0 with C,flow_matching,0.3,2.0,43,147
65,replace,2.0,n,l,C2lOc1[5N],C2nOc1[5N],10,replace l at position 2 with n,flow_matching,0.3,2.0,43,147
66,add,7.0,5,,C2nOc1[5N],C2nOc1[55N],11,add 5 at position 7,flow_matching,0.3,2.0,43,147
67,replace,5.0,/,1,C2nOc1[55N],C2nOc/[55N],11,replace 1 at position 5 with /,flow_matching,0.3,2.0,43,147
68,add,3.0,[,,C2nOc/[55N],C2n[Oc/[55N],12,add [ at position 3,flow_matching,0.3,2.0,43,147
69,replace,1.0,4,2,C2n[Oc/[55N],C4n[Oc/[55N],12,replace 2 at position 1 with 4,flow_matching,0.3,2.0,43,147
70,replace,2.0,S,n,C4n[Oc/[55N],C4S[Oc/[55N],12,replace n at position 2 with S,flow_matching,0.3,2.0,43,147
71,add,11.0,#,,C4S[Oc/[55N],C4S[Oc/[55N#],13,add # at position 11,flow_matching,0.3,2.0,43,147
72,remove,10.0,N,,C4S[Oc/[55N#],C4S[Oc/[55#],12,remove N from position 10,flow_matching,0.3,2.0,43,147
73,replace,3.0,1,[,C4S[Oc/[55#],C4S1Oc/[55#],12,replace [ at position 3 with 1,flow_matching,0.3,2.0,43,147
74,replace,7.0,O,[,C4S1Oc/[55#],C4S1Oc/O55#],12,replace [ at position 7 with O,flow_matching,0.3,2.0,43,147
75,replace,6.0,),/,C4S1Oc/O55#],C4S1Oc)O55#],12,replace / at position 6 with ),flow_matching,0.3,2.0,43,147
76,replace,1.0,O,4,C4S1Oc)O55#],COS1Oc)O55#],12,replace 4 at position 1 with O,flow_matching,0.3,2.0,43,147
77,replace,2.0,c,S,COS1Oc)O55#],COc1Oc)O55#],12,replace S at position 2 with c,flow_matching,0.3,2.0,43,147
78,add,8.0,3,,COc1Oc)O55#],COc1Oc)O355#],13,add 3 at position 8,flow_matching,0.3,2.0,43,147
79,add,0.0,=,,COc1Oc)O355#],=COc1Oc)O355#],14,add = at position 0,flow_matching,0.3,2.0,43,147
80,add,6.0,c,,=COc1Oc)O355#],=COc1Occ)O355#],15,add c at position 6,flow_matching,0.3,2.0,43,147
81,replace,0.0,C,=,=COc1Occ)O355#],CCOc1Occ)O355#],15,replace = at position 0 with C,flow_matching,0.3,2.0,43,147
82,replace,8.0,@,),CCOc1Occ)O355#],CCOc1Occ@O355#],15,replace ) at position 8 with @,flow_matching,0.3,2.0,43,147
83,add,11.0,\,,CCOc1Occ@O355#],CCOc1Occ@O3\55#],16,add \ at position 11,flow_matching,0.3,2.0,43,147
84,add,13.0,F,,CCOc1Occ@O3\55#],CCOc1Occ@O3\5F5#],17,add F at position 13,flow_matching,0.3,2.0,43,147
85,add,17.0,I,,CCOc1Occ@O3\5F5#],CCOc1Occ@O3\5F5#]I,18,add I at position 17,flow_matching,0.3,2.0,43,147
86,remove,1.0,C,,CCOc1Occ@O3\5F5#]I,COc1Occ@O3\5F5#]I,17,remove C from position 1,flow_matching,0.3,2.0,43,147
87,replace,8.0,H,O,COc1Occ@O3\5F5#]I,COc1Occ@H3\5F5#]I,17,replace O at position 8 with H,flow_matching,0.3,2.0,43,147
88,replace,4.0,c,O,COc1Occ@H3\5F5#]I,COc1ccc@H3\5F5#]I,17,replace O at position 4 with c,flow_matching,0.3,2.0,43,147
89,replace,7.0,(,@,COc1ccc@H3\5F5#]I,COc1ccc(H3\5F5#]I,17,replace @ at position 7 with (,flow_matching,0.3,2.0,43,147
90,replace,12.0,S,F,COc1ccc(H3\5F5#]I,COc1ccc(H3\5S5#]I,17,replace F at position 12 with S,flow_matching,0.3,2.0,43,147
91,add,15.0,/,,COc1ccc(H3\5S5#]I,COc1ccc(H3\5S5#/]I,18,add / at position 15,flow_matching,0.3,2.0,43,147
92,replace,8.0,-,H,COc1ccc(H3\5S5#/]I,COc1ccc(-3\5S5#/]I,18,replace H at position 8 with -,flow_matching,0.3,2.0,43,147
93,remove,8.0,-,,COc1ccc(-3\5S5#/]I,COc1ccc(3\5S5#/]I,17,remove - from position 8,flow_matching,0.3,2.0,43,147
94,add,12.0,2,,COc1ccc(3\5S5#/]I,COc1ccc(3\5S25#/]I,18,add 2 at position 12,flow_matching,0.3,2.0,43,147
95,add,12.0,1,,COc1ccc(3\5S25#/]I,COc1ccc(3\5S125#/]I,19,add 1 at position 12,flow_matching,0.3,2.0,43,147
96,remove,9.0,\,,COc1ccc(3\5S125#/]I,COc1ccc(35S125#/]I,18,remove \ from position 9,flow_matching,0.3,2.0,43,147
97,add,4.0,3,,COc1ccc(35S125#/]I,COc13ccc(35S125#/]I,19,add 3 at position 4,flow_matching,0.3,2.0,43,147
98,add,10.0,=,,COc13ccc(35S125#/]I,COc13ccc(3=5S125#/]I,20,add = at position 10,flow_matching,0.3,2.0,43,147
99,replace,4.0,c,3,COc13ccc(3=5S125#/]I,COc1cccc(3=5S125#/]I,20,replace 3 at position 4 with c,flow_matching,0.3,2.0,43,147
100,add,3.0,#,,COc1cccc(3=5S125#/]I,COc#1cccc(3=5S125#/]I,21,add # at position 3,flow_matching,0.3,2.0,43,147
101,replace,3.0,1,#,COc#1cccc(3=5S125#/]I,COc11cccc(3=5S125#/]I,21,replace # at position 3 with 1,flow_matching,0.3,2.0,43,147
102,replace,4.0,c,1,COc11cccc(3=5S125#/]I,COc1ccccc(3=5S125#/]I,21,replace 1 at position 4 with c,flow_matching,0.3,2.0,43,147
103,add,2.0,F,,COc1ccccc(3=5S125#/]I,COFc1ccccc(3=5S125#/]I,22,add F at position 2,flow_matching,0.3,2.0,43,147
104,add,0.0,5,,COFc1ccccc(3=5S125#/]I,5COFc1ccccc(3=5S125#/]I,23,add 5 at position 0,flow_matching,0.3,2.0,43,147
105,replace,9.0,-,c,5COFc1ccccc(3=5S125#/]I,5COFc1ccc-c(3=5S125#/]I,23,replace c at position 9 with -,flow_matching,0.3,2.0,43,147
106,replace,8.0,(,c,5COFc1ccc-c(3=5S125#/]I,5COFc1cc(-c(3=5S125#/]I,23,replace c at position 8 with (,flow_matching,0.3,2.0,43,147
107,replace,0.0,C,5,5COFc1cc(-c(3=5S125#/]I,CCOFc1cc(-c(3=5S125#/]I,23,replace 5 at position 0 with C,flow_matching,0.3,2.0,43,147
108,replace,1.0,O,C,CCOFc1cc(-c(3=5S125#/]I,COOFc1cc(-c(3=5S125#/]I,23,replace C at position 1 with O,flow_matching,0.3,2.0,43,147
109,replace,2.0,c,O,COOFc1cc(-c(3=5S125#/]I,COcFc1cc(-c(3=5S125#/]I,23,replace O at position 2 with c,flow_matching,0.3,2.0,43,147
110,replace,3.0,1,F,COcFc1cc(-c(3=5S125#/]I,COc1c1cc(-c(3=5S125#/]I,23,replace F at position 3 with 1,flow_matching,0.3,2.0,43,147
111,replace,5.0,c,1,COc1c1cc(-c(3=5S125#/]I,COc1cccc(-c(3=5S125#/]I,23,replace 1 at position 5 with c,flow_matching,0.3,2.0,43,147
112,replace,7.0,(,c,COc1cccc(-c(3=5S125#/]I,COc1ccc((-c(3=5S125#/]I,23,replace c at position 7 with (,flow_matching,0.3,2.0,43,147
113,replace,8.0,-,(,COc1ccc((-c(3=5S125#/]I,COc1ccc(--c(3=5S125#/]I,23,replace ( at position 8 with -,flow_matching,0.3,2.0,43,147
114,replace,9.0,c,-,COc1ccc(--c(3=5S125#/]I,COc1ccc(-cc(3=5S125#/]I,23,replace - at position 9 with c,flow_matching,0.3,2.0,43,147
115,replace,10.0,2,c,COc1ccc(-cc(3=5S125#/]I,COc1ccc(-c2(3=5S125#/]I,23,replace c at position 10 with 2,flow_matching,0.3,2.0,43,147
116,replace,11.0,c,(,COc1ccc(-c2(3=5S125#/]I,COc1ccc(-c2c3=5S125#/]I,23,replace ( at position 11 with c,flow_matching,0.3,2.0,43,147
117,replace,12.0,s,3,COc1ccc(-c2c3=5S125#/]I,COc1ccc(-c2cs=5S125#/]I,23,replace 3 at position 12 with s,flow_matching,0.3,2.0,43,147
118,replace,13.0,c,=,COc1ccc(-c2cs=5S125#/]I,COc1ccc(-c2csc5S125#/]I,23,replace = at position 13 with c,flow_matching,0.3,2.0,43,147
119,replace,14.0,(,5,COc1ccc(-c2csc5S125#/]I,COc1ccc(-c2csc(S125#/]I,23,replace 5 at position 14 with (,flow_matching,0.3,2.0,43,147
120,replace,15.0,N,S,COc1ccc(-c2csc(S125#/]I,COc1ccc(-c2csc(N125#/]I,23,replace S at position 15 with N,flow_matching,0.3,2.0,43,147
121,replace,16.0,C,1,COc1ccc(-c2csc(N125#/]I,COc1ccc(-c2csc(NC25#/]I,23,replace 1 at position 16 with C,flow_matching,0.3,2.0,43,147
122,replace,17.0,(,2,COc1ccc(-c2csc(NC25#/]I,COc1ccc(-c2csc(NC(5#/]I,23,replace 2 at position 17 with (,flow_matching,0.3,2.0,43,147
123,replace,18.0,=,5,COc1ccc(-c2csc(NC(5#/]I,COc1ccc(-c2csc(NC(=#/]I,23,replace 5 at position 18 with =,flow_matching,0.3,2.0,43,147
124,replace,19.0,O,#,COc1ccc(-c2csc(NC(=#/]I,COc1ccc(-c2csc(NC(=O/]I,23,replace # at position 19 with O,flow_matching,0.3,2.0,43,147
125,replace,20.0,),/,COc1ccc(-c2csc(NC(=O/]I,COc1ccc(-c2csc(NC(=O)]I,23,replace / at position 20 with ),flow_matching,0.3,2.0,43,147
126,replace,21.0,N,],COc1ccc(-c2csc(NC(=O)]I,COc1ccc(-c2csc(NC(=O)NI,23,replace ] at position 21 with N,flow_matching,0.3,2.0,43,147
127,replace,22.0,c,I,COc1ccc(-c2csc(NC(=O)NI,COc1ccc(-c2csc(NC(=O)Nc,23,replace I at position 22 with c,flow_matching,0.3,2.0,43,147
128,add,23.0,3,,COc1ccc(-c2csc(NC(=O)Nc,COc1ccc(-c2csc(NC(=O)Nc3,24,add 3 at position 23,flow_matching,0.3,2.0,43,147
129,add,24.0,c,,COc1ccc(-c2csc(NC(=O)Nc3,COc1ccc(-c2csc(NC(=O)Nc3c,25,add c at position 24,flow_matching,0.3,2.0,43,147
130,add,25.0,c,,COc1ccc(-c2csc(NC(=O)Nc3c,COc1ccc(-c2csc(NC(=O)Nc3cc,26,add c at position 25,flow_matching,0.3,2.0,43,147
131,add,26.0,c,,COc1ccc(-c2csc(NC(=O)Nc3cc,COc1ccc(-c2csc(NC(=O)Nc3ccc,27,add c at position 26,flow_matching,0.3,2.0,43,147
132,add,27.0,(,,COc1ccc(-c2csc(NC(=O)Nc3ccc,COc1ccc(-c2csc(NC(=O)Nc3ccc(,28,add ( at position 27,flow_matching,0.3,2.0,43,147
133,add,28.0,F,,COc1ccc(-c2csc(NC(=O)Nc3ccc(,COc1ccc(-c2csc(NC(=O)Nc3ccc(F,29,add F at position 28,flow_matching,0.3,2.0,43,147
134,add,29.0,),,COc1ccc(-c2csc(NC(=O)Nc3ccc(F,COc1ccc(-c2csc(NC(=O)Nc3ccc(F),30,add ) at position 29,flow_matching,0.3,2.0,43,147
135,add,30.0,c,,COc1ccc(-c2csc(NC(=O)Nc3ccc(F),COc1ccc(-c2csc(NC(=O)Nc3ccc(F)c,31,add c at position 30,flow_matching,0.3,2.0,43,147
136,add,31.0,c,,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)c,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc,32,add c at position 31,flow_matching,0.3,2.0,43,147
137,add,32.0,3,,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3,33,add 3 at position 32,flow_matching,0.3,2.0,43,147
138,add,33.0,),,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3),34,add ) at position 33,flow_matching,0.3,2.0,43,147
139,add,34.0,n,,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3),COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n,35,add n at position 34,flow_matching,0.3,2.0,43,147
140,add,35.0,2,,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2,36,add 2 at position 35,flow_matching,0.3,2.0,43,147
141,add,36.0,),,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2),37,add ) at position 36,flow_matching,0.3,2.0,43,147
142,add,37.0,c,,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2),COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)c,38,add c at position 37,flow_matching,0.3,2.0,43,147
143,add,38.0,c,,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)c,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)cc,39,add c at position 38,flow_matching,0.3,2.0,43,147
144,add,39.0,1,,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)cc,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)cc1,40,add 1 at position 39,flow_matching,0.3,2.0,43,147
145,add,40.0,O,,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)cc1,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)cc1O,41,add O at position 40,flow_matching,0.3,2.0,43,147
146,add,41.0,C,,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)cc1O,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)cc1OC,42,add C at position 41,flow_matching,0.3,2.0,43,147
147,add,42.0,"
",,COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)cc1OC,"COc1ccc(-c2csc(NC(=O)Nc3ccc(F)cc3)n2)cc1OC
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,147
