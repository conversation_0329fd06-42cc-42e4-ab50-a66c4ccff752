step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,48,150
1,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,48,150
2,add,0.0,#,,-,#-,2,add # at position 0,flow_matching,0.3,2.0,48,150
3,replace,0.0,F,#,#-,F-,2,replace # at position 0 with F,flow_matching,0.3,2.0,48,150
4,remove,1.0,-,,F-,F,1,remove - from position 1,flow_matching,0.3,2.0,48,150
5,replace,0.0,2,F,F,2,1,replace F at position 0 with 2,flow_matching,0.3,2.0,48,150
6,add,0.0,=,,2,=2,2,add = at position 0,flow_matching,0.3,2.0,48,150
7,add,1.0,r,,=2,=r2,3,add r at position 1,flow_matching,0.3,2.0,48,150
8,replace,0.0,C,=,=r2,Cr2,3,replace = at position 0 with C,flow_matching,0.3,2.0,48,150
9,add,0.0,[,,Cr2,[Cr2,4,add [ at position 0,flow_matching,0.3,2.0,48,150
10,replace,0.0,C,[,[Cr2,CCr2,4,replace [ at position 0 with C,flow_matching,0.3,2.0,48,150
11,remove,3.0,2,,CCr2,CCr,3,remove 2 from position 3,flow_matching,0.3,2.0,48,150
12,add,0.0,4,,CCr,4CCr,4,add 4 at position 0,flow_matching,0.3,2.0,48,150
13,add,2.0,N,,4CCr,4CNCr,5,add N at position 2,flow_matching,0.3,2.0,48,150
14,add,1.0,(,,4CNCr,4(CNCr,6,add ( at position 1,flow_matching,0.3,2.0,48,150
15,replace,1.0,@,(,4(CNCr,4@CNCr,6,replace ( at position 1 with @,flow_matching,0.3,2.0,48,150
16,add,3.0,-,,4@CNCr,4@C-NCr,7,add - at position 3,flow_matching,0.3,2.0,48,150
17,add,0.0,/,,4@C-NCr,/4@C-NCr,8,add / at position 0,flow_matching,0.3,2.0,48,150
18,replace,0.0,C,/,/4@C-NCr,C4@C-NCr,8,replace / at position 0 with C,flow_matching,0.3,2.0,48,150
19,replace,6.0,/,C,C4@C-NCr,C4@C-N/r,8,replace C at position 6 with /,flow_matching,0.3,2.0,48,150
20,replace,1.0,O,4,C4@C-N/r,CO@C-N/r,8,replace 4 at position 1 with O,flow_matching,0.3,2.0,48,150
21,replace,2.0,c,@,CO@C-N/r,COcC-N/r,8,replace @ at position 2 with c,flow_matching,0.3,2.0,48,150
22,remove,4.0,-,,COcC-N/r,COcCN/r,7,remove - from position 4,flow_matching,0.3,2.0,48,150
23,add,5.0,2,,COcCN/r,COcCN2/r,8,add 2 at position 5,flow_matching,0.3,2.0,48,150
24,add,0.0,=,,COcCN2/r,=COcCN2/r,9,add = at position 0,flow_matching,0.3,2.0,48,150
25,remove,6.0,2,,=COcCN2/r,=COcCN/r,8,remove 2 from position 6,flow_matching,0.3,2.0,48,150
26,replace,0.0,C,=,=COcCN/r,CCOcCN/r,8,replace = at position 0 with C,flow_matching,0.3,2.0,48,150
27,add,0.0,1,,CCOcCN/r,1CCOcCN/r,9,add 1 at position 0,flow_matching,0.3,2.0,48,150
28,replace,7.0,@,/,1CCOcCN/r,1CCOcCN@r,9,replace / at position 7 with @,flow_matching,0.3,2.0,48,150
29,remove,6.0,N,,1CCOcCN@r,1CCOcC@r,8,remove N from position 6,flow_matching,0.3,2.0,48,150
30,replace,0.0,C,1,1CCOcC@r,CCCOcC@r,8,replace 1 at position 0 with C,flow_matching,0.3,2.0,48,150
31,replace,7.0,s,r,CCCOcC@r,CCCOcC@s,8,replace r at position 7 with s,flow_matching,0.3,2.0,48,150
32,replace,1.0,O,C,CCCOcC@s,COCOcC@s,8,replace C at position 1 with O,flow_matching,0.3,2.0,48,150
33,add,3.0,],,COCOcC@s,COC]OcC@s,9,add ] at position 3,flow_matching,0.3,2.0,48,150
34,add,5.0,4,,COC]OcC@s,COC]O4cC@s,10,add 4 at position 5,flow_matching,0.3,2.0,48,150
35,replace,2.0,c,C,COC]O4cC@s,COc]O4cC@s,10,replace C at position 2 with c,flow_matching,0.3,2.0,48,150
36,replace,3.0,1,],COc]O4cC@s,COc1O4cC@s,10,replace ] at position 3 with 1,flow_matching,0.3,2.0,48,150
37,replace,4.0,c,O,COc1O4cC@s,COc1c4cC@s,10,replace O at position 4 with c,flow_matching,0.3,2.0,48,150
38,add,7.0,/,,COc1c4cC@s,COc1c4c/C@s,11,add / at position 7,flow_matching,0.3,2.0,48,150
39,remove,1.0,O,,COc1c4c/C@s,Cc1c4c/C@s,10,remove O from position 1,flow_matching,0.3,2.0,48,150
40,remove,1.0,c,,Cc1c4c/C@s,C1c4c/C@s,9,remove c from position 1,flow_matching,0.3,2.0,48,150
41,add,2.0,O,,C1c4c/C@s,C1Oc4c/C@s,10,add O at position 2,flow_matching,0.3,2.0,48,150
42,add,10.0,3,,C1Oc4c/C@s,C1Oc4c/C@s3,11,add 3 at position 10,flow_matching,0.3,2.0,48,150
43,replace,5.0,I,c,C1Oc4c/C@s3,C1Oc4I/C@s3,11,replace c at position 5 with I,flow_matching,0.3,2.0,48,150
44,replace,1.0,O,1,C1Oc4I/C@s3,COOc4I/C@s3,11,replace 1 at position 1 with O,flow_matching,0.3,2.0,48,150
45,add,4.0,=,,COOc4I/C@s3,COOc=4I/C@s3,12,add = at position 4,flow_matching,0.3,2.0,48,150
46,add,8.0,5,,COOc=4I/C@s3,COOc=4I/5C@s3,13,add 5 at position 8,flow_matching,0.3,2.0,48,150
47,remove,2.0,O,,COOc=4I/5C@s3,COc=4I/5C@s3,12,remove O from position 2,flow_matching,0.3,2.0,48,150
48,add,10.0,/,,COc=4I/5C@s3,COc=4I/5C@/s3,13,add / at position 10,flow_matching,0.3,2.0,48,150
49,replace,3.0,1,=,COc=4I/5C@/s3,COc14I/5C@/s3,13,replace = at position 3 with 1,flow_matching,0.3,2.0,48,150
50,replace,4.0,c,4,COc14I/5C@/s3,COc1cI/5C@/s3,13,replace 4 at position 4 with c,flow_matching,0.3,2.0,48,150
51,remove,1.0,O,,COc1cI/5C@/s3,Cc1cI/5C@/s3,12,remove O from position 1,flow_matching,0.3,2.0,48,150
52,replace,1.0,O,c,Cc1cI/5C@/s3,CO1cI/5C@/s3,12,replace c at position 1 with O,flow_matching,0.3,2.0,48,150
53,replace,9.0,I,/,CO1cI/5C@/s3,CO1cI/5C@Is3,12,replace / at position 9 with I,flow_matching,0.3,2.0,48,150
54,replace,0.0,4,C,CO1cI/5C@Is3,4O1cI/5C@Is3,12,replace C at position 0 with 4,flow_matching,0.3,2.0,48,150
55,remove,11.0,3,,4O1cI/5C@Is3,4O1cI/5C@Is,11,remove 3 from position 11,flow_matching,0.3,2.0,48,150
56,add,10.0,#,,4O1cI/5C@Is,4O1cI/5C@I#s,12,add # at position 10,flow_matching,0.3,2.0,48,150
57,replace,5.0,n,/,4O1cI/5C@I#s,4O1cIn5C@I#s,12,replace / at position 5 with n,flow_matching,0.3,2.0,48,150
58,replace,8.0,-,@,4O1cIn5C@I#s,4O1cIn5C-I#s,12,replace @ at position 8 with -,flow_matching,0.3,2.0,48,150
59,replace,1.0,5,O,4O1cIn5C-I#s,451cIn5C-I#s,12,replace O at position 1 with 5,flow_matching,0.3,2.0,48,150
60,replace,0.0,C,4,451cIn5C-I#s,C51cIn5C-I#s,12,replace 4 at position 0 with C,flow_matching,0.3,2.0,48,150
61,replace,8.0,@,-,C51cIn5C-I#s,C51cIn5C@I#s,12,replace - at position 8 with @,flow_matching,0.3,2.0,48,150
62,add,9.0,\,,C51cIn5C@I#s,C51cIn5C@\I#s,13,add \ at position 9,flow_matching,0.3,2.0,48,150
63,remove,6.0,5,,C51cIn5C@\I#s,C51cInC@\I#s,12,remove 5 from position 6,flow_matching,0.3,2.0,48,150
64,remove,8.0,\,,C51cInC@\I#s,C51cInC@I#s,11,remove \ from position 8,flow_matching,0.3,2.0,48,150
65,replace,1.0,O,5,C51cInC@I#s,CO1cInC@I#s,11,replace 5 at position 1 with O,flow_matching,0.3,2.0,48,150
66,replace,9.0,],#,CO1cInC@I#s,CO1cInC@I]s,11,replace # at position 9 with ],flow_matching,0.3,2.0,48,150
67,remove,3.0,c,,CO1cInC@I]s,CO1InC@I]s,10,remove c from position 3,flow_matching,0.3,2.0,48,150
68,replace,2.0,c,1,CO1InC@I]s,COcInC@I]s,10,replace 1 at position 2 with c,flow_matching,0.3,2.0,48,150
69,remove,4.0,n,,COcInC@I]s,COcIC@I]s,9,remove n from position 4,flow_matching,0.3,2.0,48,150
70,remove,2.0,c,,COcIC@I]s,COIC@I]s,8,remove c from position 2,flow_matching,0.3,2.0,48,150
71,add,7.0,c,,COIC@I]s,COIC@I]cs,9,add c at position 7,flow_matching,0.3,2.0,48,150
72,replace,2.0,c,I,COIC@I]cs,COcC@I]cs,9,replace I at position 2 with c,flow_matching,0.3,2.0,48,150
73,replace,3.0,1,C,COcC@I]cs,COc1@I]cs,9,replace C at position 3 with 1,flow_matching,0.3,2.0,48,150
74,replace,6.0,l,],COc1@I]cs,COc1@Ilcs,9,replace ] at position 6 with l,flow_matching,0.3,2.0,48,150
75,replace,4.0,c,@,COc1@Ilcs,COc1cIlcs,9,replace @ at position 4 with c,flow_matching,0.3,2.0,48,150
76,replace,3.0,],1,COc1cIlcs,COc]cIlcs,9,replace 1 at position 3 with ],flow_matching,0.3,2.0,48,150
77,remove,3.0,],,COc]cIlcs,COccIlcs,8,remove ] from position 3,flow_matching,0.3,2.0,48,150
78,remove,1.0,O,,COccIlcs,CccIlcs,7,remove O from position 1,flow_matching,0.3,2.0,48,150
79,add,3.0,],,CccIlcs,Ccc]Ilcs,8,add ] at position 3,flow_matching,0.3,2.0,48,150
80,add,6.0,n,,Ccc]Ilcs,Ccc]Ilncs,9,add n at position 6,flow_matching,0.3,2.0,48,150
81,add,3.0,S,,Ccc]Ilncs,CccS]Ilncs,10,add S at position 3,flow_matching,0.3,2.0,48,150
82,replace,1.0,O,c,CccS]Ilncs,COcS]Ilncs,10,replace c at position 1 with O,flow_matching,0.3,2.0,48,150
83,remove,7.0,n,,COcS]Ilncs,COcS]Ilcs,9,remove n from position 7,flow_matching,0.3,2.0,48,150
84,remove,4.0,],,COcS]Ilcs,COcSIlcs,8,remove ] from position 4,flow_matching,0.3,2.0,48,150
85,add,8.0,=,,COcSIlcs,COcSIlcs=,9,add = at position 8,flow_matching,0.3,2.0,48,150
86,replace,3.0,1,S,COcSIlcs=,COc1Ilcs=,9,replace S at position 3 with 1,flow_matching,0.3,2.0,48,150
87,replace,4.0,c,I,COc1Ilcs=,COc1clcs=,9,replace I at position 4 with c,flow_matching,0.3,2.0,48,150
88,remove,1.0,O,,COc1clcs=,Cc1clcs=,8,remove O from position 1,flow_matching,0.3,2.0,48,150
89,remove,6.0,s,,Cc1clcs=,Cc1clc=,7,remove s from position 6,flow_matching,0.3,2.0,48,150
90,add,0.0,n,,Cc1clc=,nCc1clc=,8,add n at position 0,flow_matching,0.3,2.0,48,150
91,add,6.0,\,,nCc1clc=,nCc1cl\c=,9,add \ at position 6,flow_matching,0.3,2.0,48,150
92,remove,3.0,1,,nCc1cl\c=,nCccl\c=,8,remove 1 from position 3,flow_matching,0.3,2.0,48,150
93,add,6.0,o,,nCccl\c=,nCccl\oc=,9,add o at position 6,flow_matching,0.3,2.0,48,150
94,replace,0.0,C,n,nCccl\oc=,CCccl\oc=,9,replace n at position 0 with C,flow_matching,0.3,2.0,48,150
95,replace,1.0,O,C,CCccl\oc=,COccl\oc=,9,replace C at position 1 with O,flow_matching,0.3,2.0,48,150
96,add,1.0,S,,COccl\oc=,CSOccl\oc=,10,add S at position 1,flow_matching,0.3,2.0,48,150
97,add,8.0,1,,CSOccl\oc=,CSOccl\o1c=,11,add 1 at position 8,flow_matching,0.3,2.0,48,150
98,remove,9.0,c,,CSOccl\o1c=,CSOccl\o1=,10,remove c from position 9,flow_matching,0.3,2.0,48,150
99,replace,1.0,O,S,CSOccl\o1=,COOccl\o1=,10,replace S at position 1 with O,flow_matching,0.3,2.0,48,150
100,add,8.0,S,,COOccl\o1=,COOccl\oS1=,11,add S at position 8,flow_matching,0.3,2.0,48,150
101,replace,2.0,c,O,COOccl\oS1=,COcccl\oS1=,11,replace O at position 2 with c,flow_matching,0.3,2.0,48,150
102,remove,1.0,O,,COcccl\oS1=,Ccccl\oS1=,10,remove O from position 1,flow_matching,0.3,2.0,48,150
103,add,8.0,4,,Ccccl\oS1=,Ccccl\oS41=,11,add 4 at position 8,flow_matching,0.3,2.0,48,150
104,remove,8.0,4,,Ccccl\oS41=,Ccccl\oS1=,10,remove 4 from position 8,flow_matching,0.3,2.0,48,150
105,replace,1.0,O,c,Ccccl\oS1=,COccl\oS1=,10,replace c at position 1 with O,flow_matching,0.3,2.0,48,150
106,replace,3.0,1,c,COccl\oS1=,COc1l\oS1=,10,replace c at position 3 with 1,flow_matching,0.3,2.0,48,150
107,replace,4.0,c,l,COc1l\oS1=,COc1c\oS1=,10,replace l at position 4 with c,flow_matching,0.3,2.0,48,150
108,replace,5.0,c,\,COc1c\oS1=,COc1ccoS1=,10,replace \ at position 5 with c,flow_matching,0.3,2.0,48,150
109,replace,6.0,c,o,COc1ccoS1=,COc1cccS1=,10,replace o at position 6 with c,flow_matching,0.3,2.0,48,150
110,replace,7.0,(,S,COc1cccS1=,COc1ccc(1=,10,replace S at position 7 with (,flow_matching,0.3,2.0,48,150
111,replace,8.0,B,1,COc1ccc(1=,COc1ccc(B=,10,replace 1 at position 8 with B,flow_matching,0.3,2.0,48,150
112,replace,9.0,r,=,COc1ccc(B=,COc1ccc(Br,10,replace = at position 9 with r,flow_matching,0.3,2.0,48,150
113,add,10.0,),,COc1ccc(Br,COc1ccc(Br),11,add ) at position 10,flow_matching,0.3,2.0,48,150
114,add,11.0,c,,COc1ccc(Br),COc1ccc(Br)c,12,add c at position 11,flow_matching,0.3,2.0,48,150
115,add,12.0,c,,COc1ccc(Br)c,COc1ccc(Br)cc,13,add c at position 12,flow_matching,0.3,2.0,48,150
116,add,13.0,1,,COc1ccc(Br)cc,COc1ccc(Br)cc1,14,add 1 at position 13,flow_matching,0.3,2.0,48,150
117,add,14.0,/,,COc1ccc(Br)cc1,COc1ccc(Br)cc1/,15,add / at position 14,flow_matching,0.3,2.0,48,150
118,add,15.0,C,,COc1ccc(Br)cc1/,COc1ccc(Br)cc1/C,16,add C at position 15,flow_matching,0.3,2.0,48,150
119,add,16.0,=,,COc1ccc(Br)cc1/C,COc1ccc(Br)cc1/C=,17,add = at position 16,flow_matching,0.3,2.0,48,150
120,add,17.0,C,,COc1ccc(Br)cc1/C=,COc1ccc(Br)cc1/C=C,18,add C at position 17,flow_matching,0.3,2.0,48,150
121,add,18.0,/,,COc1ccc(Br)cc1/C=C,COc1ccc(Br)cc1/C=C/,19,add / at position 18,flow_matching,0.3,2.0,48,150
122,add,19.0,C,,COc1ccc(Br)cc1/C=C/,COc1ccc(Br)cc1/C=C/C,20,add C at position 19,flow_matching,0.3,2.0,48,150
123,add,20.0,(,,COc1ccc(Br)cc1/C=C/C,COc1ccc(Br)cc1/C=C/C(,21,add ( at position 20,flow_matching,0.3,2.0,48,150
124,add,21.0,=,,COc1ccc(Br)cc1/C=C/C(,COc1ccc(Br)cc1/C=C/C(=,22,add = at position 21,flow_matching,0.3,2.0,48,150
125,add,22.0,O,,COc1ccc(Br)cc1/C=C/C(=,COc1ccc(Br)cc1/C=C/C(=O,23,add O at position 22,flow_matching,0.3,2.0,48,150
126,add,23.0,),,COc1ccc(Br)cc1/C=C/C(=O,COc1ccc(Br)cc1/C=C/C(=O),24,add ) at position 23,flow_matching,0.3,2.0,48,150
127,add,24.0,N,,COc1ccc(Br)cc1/C=C/C(=O),COc1ccc(Br)cc1/C=C/C(=O)N,25,add N at position 24,flow_matching,0.3,2.0,48,150
128,add,25.0,1,,COc1ccc(Br)cc1/C=C/C(=O)N,COc1ccc(Br)cc1/C=C/C(=O)N1,26,add 1 at position 25,flow_matching,0.3,2.0,48,150
129,add,26.0,C,,COc1ccc(Br)cc1/C=C/C(=O)N1,COc1ccc(Br)cc1/C=C/C(=O)N1C,27,add C at position 26,flow_matching,0.3,2.0,48,150
130,add,27.0,C,,COc1ccc(Br)cc1/C=C/C(=O)N1C,COc1ccc(Br)cc1/C=C/C(=O)N1CC,28,add C at position 27,flow_matching,0.3,2.0,48,150
131,add,28.0,N,,COc1ccc(Br)cc1/C=C/C(=O)N1CC,COc1ccc(Br)cc1/C=C/C(=O)N1CCN,29,add N at position 28,flow_matching,0.3,2.0,48,150
132,add,29.0,(,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(,30,add ( at position 29,flow_matching,0.3,2.0,48,150
133,add,30.0,C,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C,31,add C at position 30,flow_matching,0.3,2.0,48,150
134,add,31.0,(,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(,32,add ( at position 31,flow_matching,0.3,2.0,48,150
135,add,32.0,=,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=,33,add = at position 32,flow_matching,0.3,2.0,48,150
136,add,33.0,O,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O,34,add O at position 33,flow_matching,0.3,2.0,48,150
137,add,34.0,),,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O),35,add ) at position 34,flow_matching,0.3,2.0,48,150
138,add,35.0,c,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O),COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c,36,add c at position 35,flow_matching,0.3,2.0,48,150
139,add,36.0,2,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2,37,add 2 at position 36,flow_matching,0.3,2.0,48,150
140,add,37.0,c,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2c,38,add c at position 37,flow_matching,0.3,2.0,48,150
141,add,38.0,c,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2c,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2cc,39,add c at position 38,flow_matching,0.3,2.0,48,150
142,add,39.0,c,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2cc,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccc,40,add c at position 39,flow_matching,0.3,2.0,48,150
143,add,40.0,c,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccc,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2cccc,41,add c at position 40,flow_matching,0.3,2.0,48,150
144,add,41.0,c,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2cccc,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc,42,add c at position 41,flow_matching,0.3,2.0,48,150
145,add,42.0,2,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2,43,add 2 at position 42,flow_matching,0.3,2.0,48,150
146,add,43.0,),,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2),44,add ) at position 43,flow_matching,0.3,2.0,48,150
147,add,44.0,C,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2),COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2)C,45,add C at position 44,flow_matching,0.3,2.0,48,150
148,add,45.0,C,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2)C,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2)CC,46,add C at position 45,flow_matching,0.3,2.0,48,150
149,add,46.0,1,,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2)CC,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2)CC1,47,add 1 at position 46,flow_matching,0.3,2.0,48,150
150,add,47.0,"
",,COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2)CC1,"COc1ccc(Br)cc1/C=C/C(=O)N1CCN(C(=O)c2ccccc2)CC1
",48,"add 
 at position 47",flow_matching,0.3,2.0,48,150
