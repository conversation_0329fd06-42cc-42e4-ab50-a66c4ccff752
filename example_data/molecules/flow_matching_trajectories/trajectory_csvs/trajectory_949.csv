step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,116
1,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,43,116
2,add,0.0,H,,@,H@,2,add H at position 0,flow_matching,0.3,2.0,43,116
3,add,0.0,\,,H@,\H@,3,add \ at position 0,flow_matching,0.3,2.0,43,116
4,replace,0.0,C,\,\H@,CH@,3,replace \ at position 0 with C,flow_matching,0.3,2.0,43,116
5,remove,0.0,C,,CH@,H@,2,remove C from position 0,flow_matching,0.3,2.0,43,116
6,replace,0.0,<PERSON>,<PERSON>,<PERSON>@,N@,2,replace <PERSON> at position 0 with N,flow_matching,0.3,2.0,43,116
7,add,2.0,3,,N@,N@3,3,add 3 at position 2,flow_matching,0.3,2.0,43,116
8,add,2.0,-,,N@3,N@-3,4,add - at position 2,flow_matching,0.3,2.0,43,116
9,add,4.0,1,,N@-3,N@-31,5,add 1 at position 4,flow_matching,0.3,2.0,43,116
10,remove,3.0,3,,N@-31,N@-1,4,remove 3 from position 3,flow_matching,0.3,2.0,43,116
11,add,4.0,=,,N@-1,N@-1=,5,add = at position 4,flow_matching,0.3,2.0,43,116
12,replace,0.0,C,N,N@-1=,C@-1=,5,replace N at position 0 with C,flow_matching,0.3,2.0,43,116
13,add,1.0,@,,C@-1=,C@@-1=,6,add @ at position 1,flow_matching,0.3,2.0,43,116
14,add,6.0,/,,C@@-1=,C@@-1=/,7,add / at position 6,flow_matching,0.3,2.0,43,116
15,replace,1.0,4,@,C@@-1=/,C4@-1=/,7,replace @ at position 1 with 4,flow_matching,0.3,2.0,43,116
16,replace,1.0,C,4,C4@-1=/,CC@-1=/,7,replace 4 at position 1 with C,flow_matching,0.3,2.0,43,116
17,add,4.0,(,,CC@-1=/,CC@-(1=/,8,add ( at position 4,flow_matching,0.3,2.0,43,116
18,remove,3.0,-,,CC@-(1=/,CC@(1=/,7,remove - from position 3,flow_matching,0.3,2.0,43,116
19,add,0.0,n,,CC@(1=/,nCC@(1=/,8,add n at position 0,flow_matching,0.3,2.0,43,116
20,remove,3.0,@,,nCC@(1=/,nCC(1=/,7,remove @ from position 3,flow_matching,0.3,2.0,43,116
21,remove,2.0,C,,nCC(1=/,nC(1=/,6,remove C from position 2,flow_matching,0.3,2.0,43,116
22,replace,0.0,C,n,nC(1=/,CC(1=/,6,replace n at position 0 with C,flow_matching,0.3,2.0,43,116
23,remove,0.0,C,,CC(1=/,C(1=/,5,remove C from position 0,flow_matching,0.3,2.0,43,116
24,remove,3.0,=,,C(1=/,C(1/,4,remove = from position 3,flow_matching,0.3,2.0,43,116
25,add,3.0,6,,C(1/,C(16/,5,add 6 at position 3,flow_matching,0.3,2.0,43,116
26,add,2.0,o,,C(16/,C(o16/,6,add o at position 2,flow_matching,0.3,2.0,43,116
27,remove,4.0,6,,C(o16/,C(o1/,5,remove 6 from position 4,flow_matching,0.3,2.0,43,116
28,replace,2.0,\,o,C(o1/,C(\1/,5,replace o at position 2 with \,flow_matching,0.3,2.0,43,116
29,remove,3.0,1,,C(\1/,C(\/,4,remove 1 from position 3,flow_matching,0.3,2.0,43,116
30,add,4.0,@,,C(\/,C(\/@,5,add @ at position 4,flow_matching,0.3,2.0,43,116
31,remove,4.0,@,,C(\/@,C(\/,4,remove @ from position 4,flow_matching,0.3,2.0,43,116
32,remove,2.0,\,,C(\/,C(/,3,remove \ from position 2,flow_matching,0.3,2.0,43,116
33,add,0.0,r,,C(/,rC(/,4,add r at position 0,flow_matching,0.3,2.0,43,116
34,replace,0.0,C,r,rC(/,CC(/,4,replace r at position 0 with C,flow_matching,0.3,2.0,43,116
35,replace,2.0,C,(,CC(/,CCC/,4,replace ( at position 2 with C,flow_matching,0.3,2.0,43,116
36,replace,0.0,I,C,CCC/,ICC/,4,replace C at position 0 with I,flow_matching,0.3,2.0,43,116
37,replace,0.0,C,I,ICC/,CCC/,4,replace I at position 0 with C,flow_matching,0.3,2.0,43,116
38,add,1.0,6,,CCC/,C6CC/,5,add 6 at position 1,flow_matching,0.3,2.0,43,116
39,remove,0.0,C,,C6CC/,6CC/,4,remove C from position 0,flow_matching,0.3,2.0,43,116
40,remove,3.0,/,,6CC/,6CC,3,remove / from position 3,flow_matching,0.3,2.0,43,116
41,add,3.0,o,,6CC,6CCo,4,add o at position 3,flow_matching,0.3,2.0,43,116
42,replace,0.0,C,6,6CCo,CCCo,4,replace 6 at position 0 with C,flow_matching,0.3,2.0,43,116
43,add,0.0,N,,CCCo,NCCCo,5,add N at position 0,flow_matching,0.3,2.0,43,116
44,remove,2.0,C,,NCCCo,NCCo,4,remove C from position 2,flow_matching,0.3,2.0,43,116
45,replace,2.0,F,C,NCCo,NCFo,4,replace C at position 2 with F,flow_matching,0.3,2.0,43,116
46,replace,0.0,C,N,NCFo,CCFo,4,replace N at position 0 with C,flow_matching,0.3,2.0,43,116
47,replace,0.0,-,C,CCFo,-CFo,4,replace C at position 0 with -,flow_matching,0.3,2.0,43,116
48,add,4.0,(,,-CFo,-CFo(,5,add ( at position 4,flow_matching,0.3,2.0,43,116
49,replace,3.0,#,o,-CFo(,-CF#(,5,replace o at position 3 with #,flow_matching,0.3,2.0,43,116
50,add,0.0,1,,-CF#(,1-CF#(,6,add 1 at position 0,flow_matching,0.3,2.0,43,116
51,add,2.0,r,,1-CF#(,1-rCF#(,7,add r at position 2,flow_matching,0.3,2.0,43,116
52,replace,0.0,C,1,1-rCF#(,C-rCF#(,7,replace 1 at position 0 with C,flow_matching,0.3,2.0,43,116
53,replace,0.0,r,C,C-rCF#(,r-rCF#(,7,replace C at position 0 with r,flow_matching,0.3,2.0,43,116
54,replace,0.0,C,r,r-rCF#(,C-rCF#(,7,replace r at position 0 with C,flow_matching,0.3,2.0,43,116
55,remove,2.0,r,,C-rCF#(,C-CF#(,6,remove r from position 2,flow_matching,0.3,2.0,43,116
56,replace,0.0,1,C,C-CF#(,1-CF#(,6,replace C at position 0 with 1,flow_matching,0.3,2.0,43,116
57,replace,0.0,C,1,1-CF#(,C-CF#(,6,replace 1 at position 0 with C,flow_matching,0.3,2.0,43,116
58,replace,0.0,r,C,C-CF#(,r-CF#(,6,replace C at position 0 with r,flow_matching,0.3,2.0,43,116
59,replace,2.0,(,C,r-CF#(,r-(F#(,6,replace C at position 2 with (,flow_matching,0.3,2.0,43,116
60,replace,0.0,C,r,r-(F#(,C-(F#(,6,replace r at position 0 with C,flow_matching,0.3,2.0,43,116
61,add,5.0,c,,C-(F#(,C-(F#c(,7,add c at position 5,flow_matching,0.3,2.0,43,116
62,replace,1.0,C,-,C-(F#c(,CC(F#c(,7,replace - at position 1 with C,flow_matching,0.3,2.0,43,116
63,add,6.0,I,,CC(F#c(,CC(F#cI(,8,add I at position 6,flow_matching,0.3,2.0,43,116
64,remove,4.0,#,,CC(F#cI(,CC(FcI(,7,remove # from position 4,flow_matching,0.3,2.0,43,116
65,add,2.0,1,,CC(FcI(,CC1(FcI(,8,add 1 at position 2,flow_matching,0.3,2.0,43,116
66,replace,0.0,r,C,CC1(FcI(,rC1(FcI(,8,replace C at position 0 with r,flow_matching,0.3,2.0,43,116
67,remove,7.0,(,,rC1(FcI(,rC1(FcI,7,remove ( from position 7,flow_matching,0.3,2.0,43,116
68,add,7.0,l,,rC1(FcI,rC1(FcIl,8,add l at position 7,flow_matching,0.3,2.0,43,116
69,replace,4.0,=,F,rC1(FcIl,rC1(=cIl,8,replace F at position 4 with =,flow_matching,0.3,2.0,43,116
70,replace,0.0,C,r,rC1(=cIl,CC1(=cIl,8,replace r at position 0 with C,flow_matching,0.3,2.0,43,116
71,replace,2.0,2,1,CC1(=cIl,CC2(=cIl,8,replace 1 at position 2 with 2,flow_matching,0.3,2.0,43,116
72,replace,2.0,C,2,CC2(=cIl,CCC(=cIl,8,replace 2 at position 2 with C,flow_matching,0.3,2.0,43,116
73,remove,6.0,I,,CCC(=cIl,CCC(=cl,7,remove I from position 6,flow_matching,0.3,2.0,43,116
74,add,1.0,2,,CCC(=cl,C2CC(=cl,8,add 2 at position 1,flow_matching,0.3,2.0,43,116
75,remove,4.0,(,,C2CC(=cl,C2CC=cl,7,remove ( from position 4,flow_matching,0.3,2.0,43,116
76,replace,1.0,C,2,C2CC=cl,CCCC=cl,7,replace 2 at position 1 with C,flow_matching,0.3,2.0,43,116
77,replace,3.0,n,C,CCCC=cl,CCCn=cl,7,replace C at position 3 with n,flow_matching,0.3,2.0,43,116
78,replace,4.0,1,=,CCCn=cl,CCCn1cl,7,replace = at position 4 with 1,flow_matching,0.3,2.0,43,116
79,replace,5.0,n,c,CCCn1cl,CCCn1nl,7,replace c at position 5 with n,flow_matching,0.3,2.0,43,116
80,replace,6.0,n,l,CCCn1nl,CCCn1nn,7,replace l at position 6 with n,flow_matching,0.3,2.0,43,116
81,add,7.0,n,,CCCn1nn,CCCn1nnn,8,add n at position 7,flow_matching,0.3,2.0,43,116
82,add,8.0,c,,CCCn1nnn,CCCn1nnnc,9,add c at position 8,flow_matching,0.3,2.0,43,116
83,add,9.0,1,,CCCn1nnnc,CCCn1nnnc1,10,add 1 at position 9,flow_matching,0.3,2.0,43,116
84,add,10.0,C,,CCCn1nnnc1,CCCn1nnnc1C,11,add C at position 10,flow_matching,0.3,2.0,43,116
85,add,11.0,N,,CCCn1nnnc1C,CCCn1nnnc1CN,12,add N at position 11,flow_matching,0.3,2.0,43,116
86,add,12.0,1,,CCCn1nnnc1CN,CCCn1nnnc1CN1,13,add 1 at position 12,flow_matching,0.3,2.0,43,116
87,add,13.0,C,,CCCn1nnnc1CN1,CCCn1nnnc1CN1C,14,add C at position 13,flow_matching,0.3,2.0,43,116
88,add,14.0,C,,CCCn1nnnc1CN1C,CCCn1nnnc1CN1CC,15,add C at position 14,flow_matching,0.3,2.0,43,116
89,add,15.0,[,,CCCn1nnnc1CN1CC,CCCn1nnnc1CN1CC[,16,add [ at position 15,flow_matching,0.3,2.0,43,116
90,add,16.0,C,,CCCn1nnnc1CN1CC[,CCCn1nnnc1CN1CC[C,17,add C at position 16,flow_matching,0.3,2.0,43,116
91,add,17.0,@,,CCCn1nnnc1CN1CC[C,CCCn1nnnc1CN1CC[C@,18,add @ at position 17,flow_matching,0.3,2.0,43,116
92,add,18.0,],,CCCn1nnnc1CN1CC[C@,CCCn1nnnc1CN1CC[C@],19,add ] at position 18,flow_matching,0.3,2.0,43,116
93,add,19.0,2,,CCCn1nnnc1CN1CC[C@],CCCn1nnnc1CN1CC[C@]2,20,add 2 at position 19,flow_matching,0.3,2.0,43,116
94,add,20.0,(,,CCCn1nnnc1CN1CC[C@]2,CCCn1nnnc1CN1CC[C@]2(,21,add ( at position 20,flow_matching,0.3,2.0,43,116
95,add,21.0,C,,CCCn1nnnc1CN1CC[C@]2(,CCCn1nnnc1CN1CC[C@]2(C,22,add C at position 21,flow_matching,0.3,2.0,43,116
96,add,22.0,1,,CCCn1nnnc1CN1CC[C@]2(C,CCCn1nnnc1CN1CC[C@]2(C1,23,add 1 at position 22,flow_matching,0.3,2.0,43,116
97,add,23.0,),,CCCn1nnnc1CN1CC[C@]2(C1,CCCn1nnnc1CN1CC[C@]2(C1),24,add ) at position 23,flow_matching,0.3,2.0,43,116
98,add,24.0,N,,CCCn1nnnc1CN1CC[C@]2(C1),CCCn1nnnc1CN1CC[C@]2(C1)N,25,add N at position 24,flow_matching,0.3,2.0,43,116
99,add,25.0,C,,CCCn1nnnc1CN1CC[C@]2(C1)N,CCCn1nnnc1CN1CC[C@]2(C1)NC,26,add C at position 25,flow_matching,0.3,2.0,43,116
100,add,26.0,(,,CCCn1nnnc1CN1CC[C@]2(C1)NC,CCCn1nnnc1CN1CC[C@]2(C1)NC(,27,add ( at position 26,flow_matching,0.3,2.0,43,116
101,add,27.0,=,,CCCn1nnnc1CN1CC[C@]2(C1)NC(,CCCn1nnnc1CN1CC[C@]2(C1)NC(=,28,add = at position 27,flow_matching,0.3,2.0,43,116
102,add,28.0,O,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O,29,add O at position 28,flow_matching,0.3,2.0,43,116
103,add,29.0,),,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O),30,add ) at position 29,flow_matching,0.3,2.0,43,116
104,add,30.0,N,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O),CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N,31,add N at position 30,flow_matching,0.3,2.0,43,116
105,add,31.0,(,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(,32,add ( at position 31,flow_matching,0.3,2.0,43,116
106,add,32.0,C,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C,33,add C at position 32,flow_matching,0.3,2.0,43,116
107,add,33.0,(,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(,34,add ( at position 33,flow_matching,0.3,2.0,43,116
108,add,34.0,C,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C,35,add C at position 34,flow_matching,0.3,2.0,43,116
109,add,35.0,),,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C),36,add ) at position 35,flow_matching,0.3,2.0,43,116
110,add,36.0,C,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C),CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C,37,add C at position 36,flow_matching,0.3,2.0,43,116
111,add,37.0,),,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C),38,add ) at position 37,flow_matching,0.3,2.0,43,116
112,add,38.0,C,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C),CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C)C,39,add C at position 38,flow_matching,0.3,2.0,43,116
113,add,39.0,2,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C)C,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C)C2,40,add 2 at position 39,flow_matching,0.3,2.0,43,116
114,add,40.0,=,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C)C2,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C)C2=,41,add = at position 40,flow_matching,0.3,2.0,43,116
115,add,41.0,O,,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C)C2=,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C)C2=O,42,add O at position 41,flow_matching,0.3,2.0,43,116
116,add,42.0,"
",,CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C)C2=O,"CCCn1nnnc1CN1CC[C@]2(C1)NC(=O)N(C(C)C)C2=O
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,116
