step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,47,196
1,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,47,196
2,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,47,196
3,add,0.0,2,,C,2C,2,add 2 at position 0,flow_matching,0.3,2.0,47,196
4,remove,0.0,2,,2C,C,1,remove 2 from position 0,flow_matching,0.3,2.0,47,196
5,add,1.0,2,,C,C2,2,add 2 at position 1,flow_matching,0.3,2.0,47,196
6,add,0.0,2,,C2,2C2,3,add 2 at position 0,flow_matching,0.3,2.0,47,196
7,replace,0.0,l,2,2C2,lC2,3,replace 2 at position 0 with l,flow_matching,0.3,2.0,47,196
8,replace,0.0,N,l,lC2,NC2,3,replace l at position 0 with N,flow_matching,0.3,2.0,47,196
9,replace,2.0,(,2,NC2,NC(,3,replace 2 at position 2 with (,flow_matching,0.3,2.0,47,196
10,remove,0.0,N,,NC(,C(,2,remove N from position 0,flow_matching,0.3,2.0,47,196
11,replace,0.0,N,C,C(,N(,2,replace C at position 0 with N,flow_matching,0.3,2.0,47,196
12,replace,0.0,c,N,N(,c(,2,replace N at position 0 with c,flow_matching,0.3,2.0,47,196
13,replace,0.0,N,c,c(,N(,2,replace c at position 0 with N,flow_matching,0.3,2.0,47,196
14,add,2.0,@,,N(,N(@,3,add @ at position 2,flow_matching,0.3,2.0,47,196
15,add,0.0,+,,N(@,+N(@,4,add + at position 0,flow_matching,0.3,2.0,47,196
16,replace,0.0,N,+,+N(@,NN(@,4,replace + at position 0 with N,flow_matching,0.3,2.0,47,196
17,add,2.0,#,,NN(@,NN#(@,5,add # at position 2,flow_matching,0.3,2.0,47,196
18,replace,1.0,C,N,NN#(@,NC#(@,5,replace N at position 1 with C,flow_matching,0.3,2.0,47,196
19,remove,2.0,#,,NC#(@,NC(@,4,remove # from position 2,flow_matching,0.3,2.0,47,196
20,replace,3.0,=,@,NC(@,NC(=,4,replace @ at position 3 with =,flow_matching,0.3,2.0,47,196
21,add,4.0,O,,NC(=,NC(=O,5,add O at position 4,flow_matching,0.3,2.0,47,196
22,remove,1.0,C,,NC(=O,N(=O,4,remove C from position 1,flow_matching,0.3,2.0,47,196
23,remove,0.0,N,,N(=O,(=O,3,remove N from position 0,flow_matching,0.3,2.0,47,196
24,replace,0.0,N,(,(=O,N=O,3,replace ( at position 0 with N,flow_matching,0.3,2.0,47,196
25,replace,1.0,C,=,N=O,NCO,3,replace = at position 1 with C,flow_matching,0.3,2.0,47,196
26,replace,1.0,r,C,NCO,NrO,3,replace C at position 1 with r,flow_matching,0.3,2.0,47,196
27,replace,1.0,C,r,NrO,NCO,3,replace r at position 1 with C,flow_matching,0.3,2.0,47,196
28,add,1.0,/,,NCO,N/CO,4,add / at position 1,flow_matching,0.3,2.0,47,196
29,replace,1.0,C,/,N/CO,NCCO,4,replace / at position 1 with C,flow_matching,0.3,2.0,47,196
30,replace,2.0,(,C,NCCO,NC(O,4,replace C at position 2 with (,flow_matching,0.3,2.0,47,196
31,add,3.0,=,,NC(O,NC(=O,5,add = at position 3,flow_matching,0.3,2.0,47,196
32,replace,4.0,l,O,NC(=O,NC(=l,5,replace O at position 4 with l,flow_matching,0.3,2.0,47,196
33,add,5.0,(,,NC(=l,NC(=l(,6,add ( at position 5,flow_matching,0.3,2.0,47,196
34,replace,2.0,B,(,NC(=l(,NCB=l(,6,replace ( at position 2 with B,flow_matching,0.3,2.0,47,196
35,remove,2.0,B,,NCB=l(,NC=l(,5,remove B from position 2,flow_matching,0.3,2.0,47,196
36,replace,2.0,(,=,NC=l(,NC(l(,5,replace = at position 2 with (,flow_matching,0.3,2.0,47,196
37,replace,4.0,#,(,NC(l(,NC(l#,5,replace ( at position 4 with #,flow_matching,0.3,2.0,47,196
38,remove,2.0,(,,NC(l#,NCl#,4,remove ( from position 2,flow_matching,0.3,2.0,47,196
39,add,3.0,B,,NCl#,NClB#,5,add B at position 3,flow_matching,0.3,2.0,47,196
40,replace,2.0,2,l,NClB#,NC2B#,5,replace l at position 2 with 2,flow_matching,0.3,2.0,47,196
41,remove,3.0,B,,NC2B#,NC2#,4,remove B from position 3,flow_matching,0.3,2.0,47,196
42,remove,1.0,C,,NC2#,N2#,3,remove C from position 1,flow_matching,0.3,2.0,47,196
43,replace,1.0,@,2,N2#,N@#,3,replace 2 at position 1 with @,flow_matching,0.3,2.0,47,196
44,replace,1.0,C,@,N@#,NC#,3,replace @ at position 1 with C,flow_matching,0.3,2.0,47,196
45,add,2.0,/,,NC#,NC/#,4,add / at position 2,flow_matching,0.3,2.0,47,196
46,replace,3.0,/,#,NC/#,NC//,4,replace # at position 3 with /,flow_matching,0.3,2.0,47,196
47,replace,2.0,(,/,NC//,NC(/,4,replace / at position 2 with (,flow_matching,0.3,2.0,47,196
48,replace,3.0,=,/,NC(/,NC(=,4,replace / at position 3 with =,flow_matching,0.3,2.0,47,196
49,add,4.0,O,,NC(=,NC(=O,5,add O at position 4,flow_matching,0.3,2.0,47,196
50,add,4.0,),,NC(=O,NC(=)O,6,add ) at position 4,flow_matching,0.3,2.0,47,196
51,remove,1.0,C,,NC(=)O,N(=)O,5,remove C from position 1,flow_matching,0.3,2.0,47,196
52,replace,1.0,5,(,N(=)O,N5=)O,5,replace ( at position 1 with 5,flow_matching,0.3,2.0,47,196
53,replace,1.0,C,5,N5=)O,NC=)O,5,replace 5 at position 1 with C,flow_matching,0.3,2.0,47,196
54,add,2.0,3,,NC=)O,NC3=)O,6,add 3 at position 2,flow_matching,0.3,2.0,47,196
55,replace,2.0,I,3,NC3=)O,NCI=)O,6,replace 3 at position 2 with I,flow_matching,0.3,2.0,47,196
56,remove,3.0,=,,NCI=)O,NCI)O,5,remove = from position 3,flow_matching,0.3,2.0,47,196
57,replace,2.0,(,I,NCI)O,NC()O,5,replace I at position 2 with (,flow_matching,0.3,2.0,47,196
58,remove,1.0,C,,NC()O,N()O,4,remove C from position 1,flow_matching,0.3,2.0,47,196
59,remove,0.0,N,,N()O,()O,3,remove N from position 0,flow_matching,0.3,2.0,47,196
60,remove,1.0,),,()O,(O,2,remove ) from position 1,flow_matching,0.3,2.0,47,196
61,replace,0.0,N,(,(O,NO,2,replace ( at position 0 with N,flow_matching,0.3,2.0,47,196
62,replace,1.0,C,O,NO,NC,2,replace O at position 1 with C,flow_matching,0.3,2.0,47,196
63,add,2.0,7,,NC,NC7,3,add 7 at position 2,flow_matching,0.3,2.0,47,196
64,replace,0.0,O,N,NC7,OC7,3,replace N at position 0 with O,flow_matching,0.3,2.0,47,196
65,remove,1.0,C,,OC7,O7,2,remove C from position 1,flow_matching,0.3,2.0,47,196
66,replace,0.0,N,O,O7,N7,2,replace O at position 0 with N,flow_matching,0.3,2.0,47,196
67,remove,1.0,7,,N7,N,1,remove 7 from position 1,flow_matching,0.3,2.0,47,196
68,replace,0.0,r,N,N,r,1,replace N at position 0 with r,flow_matching,0.3,2.0,47,196
69,replace,0.0,N,r,r,N,1,replace r at position 0 with N,flow_matching,0.3,2.0,47,196
70,remove,0.0,N,,N,,0,remove N from position 0,flow_matching,0.3,2.0,47,196
71,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,47,196
72,replace,0.0,N,+,+,N,1,replace + at position 0 with N,flow_matching,0.3,2.0,47,196
73,add,1.0,C,,N,NC,2,add C at position 1,flow_matching,0.3,2.0,47,196
74,add,2.0,o,,NC,NCo,3,add o at position 2,flow_matching,0.3,2.0,47,196
75,remove,1.0,C,,NCo,No,2,remove C from position 1,flow_matching,0.3,2.0,47,196
76,remove,1.0,o,,No,N,1,remove o from position 1,flow_matching,0.3,2.0,47,196
77,add,0.0,o,,N,oN,2,add o at position 0,flow_matching,0.3,2.0,47,196
78,remove,1.0,N,,oN,o,1,remove N from position 1,flow_matching,0.3,2.0,47,196
79,add,0.0,r,,o,ro,2,add r at position 0,flow_matching,0.3,2.0,47,196
80,replace,0.0,N,r,ro,No,2,replace r at position 0 with N,flow_matching,0.3,2.0,47,196
81,add,0.0,F,,No,FNo,3,add F at position 0,flow_matching,0.3,2.0,47,196
82,add,2.0,2,,FNo,FN2o,4,add 2 at position 2,flow_matching,0.3,2.0,47,196
83,remove,3.0,o,,FN2o,FN2,3,remove o from position 3,flow_matching,0.3,2.0,47,196
84,replace,0.0,N,F,FN2,NN2,3,replace F at position 0 with N,flow_matching,0.3,2.0,47,196
85,add,2.0,c,,NN2,NNc2,4,add c at position 2,flow_matching,0.3,2.0,47,196
86,add,3.0,H,,NNc2,NNcH2,5,add H at position 3,flow_matching,0.3,2.0,47,196
87,replace,1.0,C,N,NNcH2,NCcH2,5,replace N at position 1 with C,flow_matching,0.3,2.0,47,196
88,replace,3.0,o,H,NCcH2,NCco2,5,replace H at position 3 with o,flow_matching,0.3,2.0,47,196
89,remove,3.0,o,,NCco2,NCc2,4,remove o from position 3,flow_matching,0.3,2.0,47,196
90,add,2.0,],,NCc2,NC]c2,5,add ] at position 2,flow_matching,0.3,2.0,47,196
91,add,0.0,C,,NC]c2,CNC]c2,6,add C at position 0,flow_matching,0.3,2.0,47,196
92,add,3.0,s,,CNC]c2,CNCs]c2,7,add s at position 3,flow_matching,0.3,2.0,47,196
93,replace,0.0,N,C,CNCs]c2,NNCs]c2,7,replace C at position 0 with N,flow_matching,0.3,2.0,47,196
94,remove,6.0,2,,NNCs]c2,NNCs]c,6,remove 2 from position 6,flow_matching,0.3,2.0,47,196
95,replace,1.0,C,N,NNCs]c,NCCs]c,6,replace N at position 1 with C,flow_matching,0.3,2.0,47,196
96,remove,5.0,c,,NCCs]c,NCCs],5,remove c from position 5,flow_matching,0.3,2.0,47,196
97,remove,0.0,N,,NCCs],CCs],4,remove N from position 0,flow_matching,0.3,2.0,47,196
98,replace,0.0,N,C,CCs],NCs],4,replace C at position 0 with N,flow_matching,0.3,2.0,47,196
99,replace,0.0,7,N,NCs],7Cs],4,replace N at position 0 with 7,flow_matching,0.3,2.0,47,196
100,replace,0.0,N,7,7Cs],NCs],4,replace 7 at position 0 with N,flow_matching,0.3,2.0,47,196
101,replace,1.0,l,C,NCs],Nls],4,replace C at position 1 with l,flow_matching,0.3,2.0,47,196
102,remove,0.0,N,,Nls],ls],3,remove N from position 0,flow_matching,0.3,2.0,47,196
103,replace,0.0,N,l,ls],Ns],3,replace l at position 0 with N,flow_matching,0.3,2.0,47,196
104,add,1.0,l,,Ns],Nls],4,add l at position 1,flow_matching,0.3,2.0,47,196
105,remove,3.0,],,Nls],Nls,3,remove ] from position 3,flow_matching,0.3,2.0,47,196
106,add,1.0,C,,Nls,NCls,4,add C at position 1,flow_matching,0.3,2.0,47,196
107,replace,3.0,(,s,NCls,NCl(,4,replace s at position 3 with (,flow_matching,0.3,2.0,47,196
108,add,1.0,S,,NCl(,NSCl(,5,add S at position 1,flow_matching,0.3,2.0,47,196
109,replace,1.0,C,S,NSCl(,NCCl(,5,replace S at position 1 with C,flow_matching,0.3,2.0,47,196
110,replace,4.0,s,(,NCCl(,NCCls,5,replace ( at position 4 with s,flow_matching,0.3,2.0,47,196
111,replace,2.0,(,C,NCCls,NC(ls,5,replace C at position 2 with (,flow_matching,0.3,2.0,47,196
112,replace,2.0,],(,NC(ls,NC]ls,5,replace ( at position 2 with ],flow_matching,0.3,2.0,47,196
113,add,0.0,=,,NC]ls,=NC]ls,6,add = at position 0,flow_matching,0.3,2.0,47,196
114,replace,3.0,B,],=NC]ls,=NCBls,6,replace ] at position 3 with B,flow_matching,0.3,2.0,47,196
115,replace,0.0,N,=,=NCBls,NNCBls,6,replace = at position 0 with N,flow_matching,0.3,2.0,47,196
116,replace,1.0,C,N,NNCBls,NCCBls,6,replace N at position 1 with C,flow_matching,0.3,2.0,47,196
117,remove,5.0,s,,NCCBls,NCCBl,5,remove s from position 5,flow_matching,0.3,2.0,47,196
118,remove,0.0,N,,NCCBl,CCBl,4,remove N from position 0,flow_matching,0.3,2.0,47,196
119,remove,1.0,C,,CCBl,CBl,3,remove C from position 1,flow_matching,0.3,2.0,47,196
120,add,3.0,N,,CBl,CBlN,4,add N at position 3,flow_matching,0.3,2.0,47,196
121,remove,0.0,C,,CBlN,BlN,3,remove C from position 0,flow_matching,0.3,2.0,47,196
122,remove,0.0,B,,BlN,lN,2,remove B from position 0,flow_matching,0.3,2.0,47,196
123,replace,0.0,N,l,lN,NN,2,replace l at position 0 with N,flow_matching,0.3,2.0,47,196
124,add,0.0,O,,NN,ONN,3,add O at position 0,flow_matching,0.3,2.0,47,196
125,remove,1.0,N,,ONN,ON,2,remove N from position 1,flow_matching,0.3,2.0,47,196
126,remove,0.0,O,,ON,N,1,remove O from position 0,flow_matching,0.3,2.0,47,196
127,remove,0.0,N,,N,,0,remove N from position 0,flow_matching,0.3,2.0,47,196
128,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,47,196
129,replace,0.0,7,],],7,1,replace ] at position 0 with 7,flow_matching,0.3,2.0,47,196
130,replace,0.0,S,7,7,S,1,replace 7 at position 0 with S,flow_matching,0.3,2.0,47,196
131,remove,0.0,S,,S,,0,remove S from position 0,flow_matching,0.3,2.0,47,196
132,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,47,196
133,add,1.0,/,,-,-/,2,add / at position 1,flow_matching,0.3,2.0,47,196
134,replace,0.0,O,-,-/,O/,2,replace - at position 0 with O,flow_matching,0.3,2.0,47,196
135,add,0.0,I,,O/,IO/,3,add I at position 0,flow_matching,0.3,2.0,47,196
136,replace,0.0,N,I,IO/,NO/,3,replace I at position 0 with N,flow_matching,0.3,2.0,47,196
137,replace,1.0,C,O,NO/,NC/,3,replace O at position 1 with C,flow_matching,0.3,2.0,47,196
138,add,1.0,F,,NC/,NFC/,4,add F at position 1,flow_matching,0.3,2.0,47,196
139,replace,2.0,o,C,NFC/,NFo/,4,replace C at position 2 with o,flow_matching,0.3,2.0,47,196
140,add,3.0,],,NFo/,NFo]/,5,add ] at position 3,flow_matching,0.3,2.0,47,196
141,replace,1.0,C,F,NFo]/,NCo]/,5,replace F at position 1 with C,flow_matching,0.3,2.0,47,196
142,remove,0.0,N,,NCo]/,Co]/,4,remove N from position 0,flow_matching,0.3,2.0,47,196
143,replace,0.0,N,C,Co]/,No]/,4,replace C at position 0 with N,flow_matching,0.3,2.0,47,196
144,remove,0.0,N,,No]/,o]/,3,remove N from position 0,flow_matching,0.3,2.0,47,196
145,add,1.0,l,,o]/,ol]/,4,add l at position 1,flow_matching,0.3,2.0,47,196
146,add,1.0,(,,ol]/,o(l]/,5,add ( at position 1,flow_matching,0.3,2.0,47,196
147,replace,0.0,N,o,o(l]/,N(l]/,5,replace o at position 0 with N,flow_matching,0.3,2.0,47,196
148,replace,1.0,C,(,N(l]/,NCl]/,5,replace ( at position 1 with C,flow_matching,0.3,2.0,47,196
149,remove,4.0,/,,NCl]/,NCl],4,remove / from position 4,flow_matching,0.3,2.0,47,196
150,add,1.0,-,,NCl],N-Cl],5,add - at position 1,flow_matching,0.3,2.0,47,196
151,replace,1.0,C,-,N-Cl],NCCl],5,replace - at position 1 with C,flow_matching,0.3,2.0,47,196
152,replace,2.0,(,C,NCCl],NC(l],5,replace C at position 2 with (,flow_matching,0.3,2.0,47,196
153,replace,3.0,=,l,NC(l],NC(=],5,replace l at position 3 with =,flow_matching,0.3,2.0,47,196
154,replace,4.0,O,],NC(=],NC(=O,5,replace ] at position 4 with O,flow_matching,0.3,2.0,47,196
155,add,5.0,),,NC(=O,NC(=O),6,add ) at position 5,flow_matching,0.3,2.0,47,196
156,add,6.0,[,,NC(=O),NC(=O)[,7,add [ at position 6,flow_matching,0.3,2.0,47,196
157,add,7.0,C,,NC(=O)[,NC(=O)[C,8,add C at position 7,flow_matching,0.3,2.0,47,196
158,add,8.0,@,,NC(=O)[C,NC(=O)[C@,9,add @ at position 8,flow_matching,0.3,2.0,47,196
159,add,9.0,@,,NC(=O)[C@,NC(=O)[C@@,10,add @ at position 9,flow_matching,0.3,2.0,47,196
160,add,10.0,H,,NC(=O)[C@@,NC(=O)[C@@H,11,add H at position 10,flow_matching,0.3,2.0,47,196
161,add,11.0,],,NC(=O)[C@@H,NC(=O)[C@@H],12,add ] at position 11,flow_matching,0.3,2.0,47,196
162,add,12.0,1,,NC(=O)[C@@H],NC(=O)[C@@H]1,13,add 1 at position 12,flow_matching,0.3,2.0,47,196
163,add,13.0,C,,NC(=O)[C@@H]1,NC(=O)[C@@H]1C,14,add C at position 13,flow_matching,0.3,2.0,47,196
164,add,14.0,C,,NC(=O)[C@@H]1C,NC(=O)[C@@H]1CC,15,add C at position 14,flow_matching,0.3,2.0,47,196
165,add,15.0,C,,NC(=O)[C@@H]1CC,NC(=O)[C@@H]1CCC,16,add C at position 15,flow_matching,0.3,2.0,47,196
166,add,16.0,N,,NC(=O)[C@@H]1CCC,NC(=O)[C@@H]1CCCN,17,add N at position 16,flow_matching,0.3,2.0,47,196
167,add,17.0,(,,NC(=O)[C@@H]1CCCN,NC(=O)[C@@H]1CCCN(,18,add ( at position 17,flow_matching,0.3,2.0,47,196
168,add,18.0,C,,NC(=O)[C@@H]1CCCN(,NC(=O)[C@@H]1CCCN(C,19,add C at position 18,flow_matching,0.3,2.0,47,196
169,add,19.0,(,,NC(=O)[C@@H]1CCCN(C,NC(=O)[C@@H]1CCCN(C(,20,add ( at position 19,flow_matching,0.3,2.0,47,196
170,add,20.0,=,,NC(=O)[C@@H]1CCCN(C(,NC(=O)[C@@H]1CCCN(C(=,21,add = at position 20,flow_matching,0.3,2.0,47,196
171,add,21.0,O,,NC(=O)[C@@H]1CCCN(C(=,NC(=O)[C@@H]1CCCN(C(=O,22,add O at position 21,flow_matching,0.3,2.0,47,196
172,add,22.0,),,NC(=O)[C@@H]1CCCN(C(=O,NC(=O)[C@@H]1CCCN(C(=O),23,add ) at position 22,flow_matching,0.3,2.0,47,196
173,add,23.0,C,,NC(=O)[C@@H]1CCCN(C(=O),NC(=O)[C@@H]1CCCN(C(=O)C,24,add C at position 23,flow_matching,0.3,2.0,47,196
174,add,24.0,n,,NC(=O)[C@@H]1CCCN(C(=O)C,NC(=O)[C@@H]1CCCN(C(=O)Cn,25,add n at position 24,flow_matching,0.3,2.0,47,196
175,add,25.0,2,,NC(=O)[C@@H]1CCCN(C(=O)Cn,NC(=O)[C@@H]1CCCN(C(=O)Cn2,26,add 2 at position 25,flow_matching,0.3,2.0,47,196
176,add,26.0,n,,NC(=O)[C@@H]1CCCN(C(=O)Cn2,NC(=O)[C@@H]1CCCN(C(=O)Cn2n,27,add n at position 26,flow_matching,0.3,2.0,47,196
177,add,27.0,c,,NC(=O)[C@@H]1CCCN(C(=O)Cn2n,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc,28,add c at position 27,flow_matching,0.3,2.0,47,196
178,add,28.0,(,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(,29,add ( at position 28,flow_matching,0.3,2.0,47,196
179,add,29.0,-,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-,30,add - at position 29,flow_matching,0.3,2.0,47,196
180,add,30.0,c,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c,31,add c at position 30,flow_matching,0.3,2.0,47,196
181,add,31.0,3,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3,32,add 3 at position 31,flow_matching,0.3,2.0,47,196
182,add,32.0,c,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3c,33,add c at position 32,flow_matching,0.3,2.0,47,196
183,add,33.0,c,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3c,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cc,34,add c at position 33,flow_matching,0.3,2.0,47,196
184,add,34.0,c,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cc,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3ccc,35,add c at position 34,flow_matching,0.3,2.0,47,196
185,add,35.0,s,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3ccc,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs,36,add s at position 35,flow_matching,0.3,2.0,47,196
186,add,36.0,3,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3,37,add 3 at position 36,flow_matching,0.3,2.0,47,196
187,add,37.0,),,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3),38,add ) at position 37,flow_matching,0.3,2.0,47,196
188,add,38.0,o,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3),NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)o,39,add o at position 38,flow_matching,0.3,2.0,47,196
189,add,39.0,c,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)o,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc,40,add c at position 39,flow_matching,0.3,2.0,47,196
190,add,40.0,2,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2,41,add 2 at position 40,flow_matching,0.3,2.0,47,196
191,add,41.0,=,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=,42,add = at position 41,flow_matching,0.3,2.0,47,196
192,add,42.0,O,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=O,43,add O at position 42,flow_matching,0.3,2.0,47,196
193,add,43.0,),,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=O,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=O),44,add ) at position 43,flow_matching,0.3,2.0,47,196
194,add,44.0,C,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=O),NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=O)C,45,add C at position 44,flow_matching,0.3,2.0,47,196
195,add,45.0,1,,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=O)C,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=O)C1,46,add 1 at position 45,flow_matching,0.3,2.0,47,196
196,add,46.0,"
",,NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=O)C1,"NC(=O)[C@@H]1CCCN(C(=O)Cn2nc(-c3cccs3)oc2=O)C1
",47,"add 
 at position 46",flow_matching,0.3,2.0,47,196
