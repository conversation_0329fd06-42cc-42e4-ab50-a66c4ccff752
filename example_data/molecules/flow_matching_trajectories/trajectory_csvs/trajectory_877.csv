step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,52,173
1,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,52,173
2,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,52,173
3,replace,0.0,+,C,C,+,1,replace <PERSON> at position 0 with +,flow_matching,0.3,2.0,52,173
4,add,1.0,=,,+,+=,2,add = at position 1,flow_matching,0.3,2.0,52,173
5,replace,0.0,C,+,+=,C=,2,replace + at position 0 with C,flow_matching,0.3,2.0,52,173
6,replace,1.0,O,=,C=,CO,2,replace = at position 1 with <PERSON>,flow_matching,0.3,2.0,52,173
7,add,2.0,6,,CO,CO6,3,add 6 at position 2,flow_matching,0.3,2.0,52,173
8,replace,2.0,[,6,CO6,CO[,3,replace 6 at position 2 with [,flow_matching,0.3,2.0,52,173
9,add,2.0,H,,CO[,COH[,4,add H at position 2,flow_matching,0.3,2.0,52,173
10,replace,2.0,[,H,COH[,CO[[,4,replace H at position 2 with [,flow_matching,0.3,2.0,52,173
11,add,2.0,B,,CO[[,COB[[,5,add B at position 2,flow_matching,0.3,2.0,52,173
12,remove,0.0,C,,COB[[,OB[[,4,remove C from position 0,flow_matching,0.3,2.0,52,173
13,replace,0.0,C,O,OB[[,CB[[,4,replace O at position 0 with C,flow_matching,0.3,2.0,52,173
14,replace,1.0,o,B,CB[[,Co[[,4,replace B at position 1 with o,flow_matching,0.3,2.0,52,173
15,replace,1.0,O,o,Co[[,CO[[,4,replace o at position 1 with O,flow_matching,0.3,2.0,52,173
16,add,0.0,@,,CO[[,@CO[[,5,add @ at position 0,flow_matching,0.3,2.0,52,173
17,replace,0.0,C,@,@CO[[,CCO[[,5,replace @ at position 0 with C,flow_matching,0.3,2.0,52,173
18,replace,1.0,O,C,CCO[[,COO[[,5,replace C at position 1 with O,flow_matching,0.3,2.0,52,173
19,add,4.0,3,,COO[[,COO[3[,6,add 3 at position 4,flow_matching,0.3,2.0,52,173
20,add,2.0,=,,COO[3[,CO=O[3[,7,add = at position 2,flow_matching,0.3,2.0,52,173
21,replace,2.0,[,=,CO=O[3[,CO[O[3[,7,replace = at position 2 with [,flow_matching,0.3,2.0,52,173
22,replace,3.0,C,O,CO[O[3[,CO[C[3[,7,replace O at position 3 with C,flow_matching,0.3,2.0,52,173
23,add,2.0,6,,CO[C[3[,CO6[C[3[,8,add 6 at position 2,flow_matching,0.3,2.0,52,173
24,remove,2.0,6,,CO6[C[3[,CO[C[3[,7,remove 6 from position 2,flow_matching,0.3,2.0,52,173
25,add,7.0,N,,CO[C[3[,CO[C[3[N,8,add N at position 7,flow_matching,0.3,2.0,52,173
26,replace,3.0,#,C,CO[C[3[N,CO[#[3[N,8,replace C at position 3 with #,flow_matching,0.3,2.0,52,173
27,remove,1.0,O,,CO[#[3[N,C[#[3[N,7,remove O from position 1,flow_matching,0.3,2.0,52,173
28,remove,6.0,N,,C[#[3[N,C[#[3[,6,remove N from position 6,flow_matching,0.3,2.0,52,173
29,replace,1.0,O,[,C[#[3[,CO#[3[,6,replace [ at position 1 with O,flow_matching,0.3,2.0,52,173
30,add,6.0,[,,CO#[3[,CO#[3[[,7,add [ at position 6,flow_matching,0.3,2.0,52,173
31,replace,2.0,[,#,CO#[3[[,CO[[3[[,7,replace # at position 2 with [,flow_matching,0.3,2.0,52,173
32,replace,2.0,-,[,CO[[3[[,CO-[3[[,7,replace [ at position 2 with -,flow_matching,0.3,2.0,52,173
33,replace,2.0,[,-,CO-[3[[,CO[[3[[,7,replace - at position 2 with [,flow_matching,0.3,2.0,52,173
34,add,0.0,O,,CO[[3[[,OCO[[3[[,8,add O at position 0,flow_matching,0.3,2.0,52,173
35,remove,5.0,3,,OCO[[3[[,OCO[[[[,7,remove 3 from position 5,flow_matching,0.3,2.0,52,173
36,replace,0.0,@,O,OCO[[[[,@CO[[[[,7,replace O at position 0 with @,flow_matching,0.3,2.0,52,173
37,remove,0.0,@,,@CO[[[[,CO[[[[,6,remove @ from position 0,flow_matching,0.3,2.0,52,173
38,replace,3.0,C,[,CO[[[[,CO[C[[,6,replace [ at position 3 with C,flow_matching,0.3,2.0,52,173
39,add,6.0,n,,CO[C[[,CO[C[[n,7,add n at position 6,flow_matching,0.3,2.0,52,173
40,replace,4.0,@,[,CO[C[[n,CO[C@[n,7,replace [ at position 4 with @,flow_matching,0.3,2.0,52,173
41,replace,6.0,F,n,CO[C@[n,CO[C@[F,7,replace n at position 6 with F,flow_matching,0.3,2.0,52,173
42,replace,1.0,o,O,CO[C@[F,Co[C@[F,7,replace O at position 1 with o,flow_matching,0.3,2.0,52,173
43,replace,1.0,1,o,Co[C@[F,C1[C@[F,7,replace o at position 1 with 1,flow_matching,0.3,2.0,52,173
44,add,3.0,1,,C1[C@[F,C1[1C@[F,8,add 1 at position 3,flow_matching,0.3,2.0,52,173
45,replace,1.0,O,1,C1[1C@[F,CO[1C@[F,8,replace 1 at position 1 with O,flow_matching,0.3,2.0,52,173
46,replace,3.0,o,1,CO[1C@[F,CO[oC@[F,8,replace 1 at position 3 with o,flow_matching,0.3,2.0,52,173
47,remove,3.0,o,,CO[oC@[F,CO[C@[F,7,remove o from position 3,flow_matching,0.3,2.0,52,173
48,remove,0.0,C,,CO[C@[F,O[C@[F,6,remove C from position 0,flow_matching,0.3,2.0,52,173
49,add,0.0,/,,O[C@[F,/O[C@[F,7,add / at position 0,flow_matching,0.3,2.0,52,173
50,replace,0.0,C,/,/O[C@[F,CO[C@[F,7,replace / at position 0 with C,flow_matching,0.3,2.0,52,173
51,add,2.0,o,,CO[C@[F,COo[C@[F,8,add o at position 2,flow_matching,0.3,2.0,52,173
52,add,5.0,I,,COo[C@[F,COo[CI@[F,9,add I at position 5,flow_matching,0.3,2.0,52,173
53,replace,2.0,[,o,COo[CI@[F,CO[[CI@[F,9,replace o at position 2 with [,flow_matching,0.3,2.0,52,173
54,remove,6.0,@,,CO[[CI@[F,CO[[CI[F,8,remove @ from position 6,flow_matching,0.3,2.0,52,173
55,replace,6.0,H,[,CO[[CI[F,CO[[CIHF,8,replace [ at position 6 with H,flow_matching,0.3,2.0,52,173
56,add,4.0,3,,CO[[CIHF,CO[[3CIHF,9,add 3 at position 4,flow_matching,0.3,2.0,52,173
57,remove,8.0,F,,CO[[3CIHF,CO[[3CIH,8,remove F from position 8,flow_matching,0.3,2.0,52,173
58,replace,3.0,C,[,CO[[3CIH,CO[C3CIH,8,replace [ at position 3 with C,flow_matching,0.3,2.0,52,173
59,replace,4.0,@,3,CO[C3CIH,CO[C@CIH,8,replace 3 at position 4 with @,flow_matching,0.3,2.0,52,173
60,replace,3.0,7,C,CO[C@CIH,CO[7@CIH,8,replace C at position 3 with 7,flow_matching,0.3,2.0,52,173
61,remove,4.0,@,,CO[7@CIH,CO[7CIH,7,remove @ from position 4,flow_matching,0.3,2.0,52,173
62,remove,3.0,7,,CO[7CIH,CO[CIH,6,remove 7 from position 3,flow_matching,0.3,2.0,52,173
63,replace,4.0,@,I,CO[CIH,CO[C@H,6,replace I at position 4 with @,flow_matching,0.3,2.0,52,173
64,add,6.0,],,CO[C@H,CO[C@H],7,add ] at position 6,flow_matching,0.3,2.0,52,173
65,add,7.0,(,,CO[C@H],CO[C@H](,8,add ( at position 7,flow_matching,0.3,2.0,52,173
66,remove,7.0,(,,CO[C@H](,CO[C@H],7,remove ( from position 7,flow_matching,0.3,2.0,52,173
67,add,7.0,(,,CO[C@H],CO[C@H](,8,add ( at position 7,flow_matching,0.3,2.0,52,173
68,add,6.0,(,,CO[C@H](,CO[C@H(](,9,add ( at position 6,flow_matching,0.3,2.0,52,173
69,add,5.0,B,,CO[C@H(](,CO[C@BH(](,10,add B at position 5,flow_matching,0.3,2.0,52,173
70,add,1.0,c,,CO[C@BH(](,CcO[C@BH(](,11,add c at position 1,flow_matching,0.3,2.0,52,173
71,add,10.0,@,,CcO[C@BH(](,CcO[C@BH(]@(,12,add @ at position 10,flow_matching,0.3,2.0,52,173
72,remove,7.0,H,,CcO[C@BH(]@(,CcO[C@B(]@(,11,remove H from position 7,flow_matching,0.3,2.0,52,173
73,add,8.0,1,,CcO[C@B(]@(,CcO[C@B(1]@(,12,add 1 at position 8,flow_matching,0.3,2.0,52,173
74,replace,1.0,),c,CcO[C@B(1]@(,C)O[C@B(1]@(,12,replace c at position 1 with ),flow_matching,0.3,2.0,52,173
75,add,3.0,7,,C)O[C@B(1]@(,C)O7[C@B(1]@(,13,add 7 at position 3,flow_matching,0.3,2.0,52,173
76,replace,1.0,O,),C)O7[C@B(1]@(,COO7[C@B(1]@(,13,replace ) at position 1 with O,flow_matching,0.3,2.0,52,173
77,replace,2.0,[,O,COO7[C@B(1]@(,CO[7[C@B(1]@(,13,replace O at position 2 with [,flow_matching,0.3,2.0,52,173
78,replace,8.0,],(,CO[7[C@B(1]@(,CO[7[C@B]1]@(,13,replace ( at position 8 with ],flow_matching,0.3,2.0,52,173
79,replace,3.0,C,7,CO[7[C@B]1]@(,CO[C[C@B]1]@(,13,replace 7 at position 3 with C,flow_matching,0.3,2.0,52,173
80,replace,4.0,@,[,CO[C[C@B]1]@(,CO[C@C@B]1]@(,13,replace [ at position 4 with @,flow_matching,0.3,2.0,52,173
81,replace,5.0,H,C,CO[C@C@B]1]@(,CO[C@H@B]1]@(,13,replace C at position 5 with H,flow_matching,0.3,2.0,52,173
82,remove,5.0,H,,CO[C@H@B]1]@(,CO[C@@B]1]@(,12,remove H from position 5,flow_matching,0.3,2.0,52,173
83,replace,5.0,H,@,CO[C@@B]1]@(,CO[C@HB]1]@(,12,replace @ at position 5 with H,flow_matching,0.3,2.0,52,173
84,replace,6.0,],B,CO[C@HB]1]@(,CO[C@H]]1]@(,12,replace B at position 6 with ],flow_matching,0.3,2.0,52,173
85,remove,7.0,],,CO[C@H]]1]@(,CO[C@H]1]@(,11,remove ] from position 7,flow_matching,0.3,2.0,52,173
86,replace,8.0,B,],CO[C@H]1]@(,CO[C@H]1B@(,11,replace ] at position 8 with B,flow_matching,0.3,2.0,52,173
87,replace,7.0,(,1,CO[C@H]1B@(,CO[C@H](B@(,11,replace 1 at position 7 with (,flow_matching,0.3,2.0,52,173
88,remove,8.0,B,,CO[C@H](B@(,CO[C@H](@(,10,remove B from position 8,flow_matching,0.3,2.0,52,173
89,replace,8.0,c,@,CO[C@H](@(,CO[C@H](c(,10,replace @ at position 8 with c,flow_matching,0.3,2.0,52,173
90,replace,9.0,1,(,CO[C@H](c(,CO[C@H](c1,10,replace ( at position 9 with 1,flow_matching,0.3,2.0,52,173
91,add,6.0,O,,CO[C@H](c1,CO[C@HO](c1,11,add O at position 6,flow_matching,0.3,2.0,52,173
92,replace,10.0,o,1,CO[C@HO](c1,CO[C@HO](co,11,replace 1 at position 10 with o,flow_matching,0.3,2.0,52,173
93,replace,6.0,],O,CO[C@HO](co,CO[C@H]](co,11,replace O at position 6 with ],flow_matching,0.3,2.0,52,173
94,replace,7.0,(,],CO[C@H]](co,CO[C@H]((co,11,replace ] at position 7 with (,flow_matching,0.3,2.0,52,173
95,replace,8.0,c,(,CO[C@H]((co,CO[C@H](cco,11,replace ( at position 8 with c,flow_matching,0.3,2.0,52,173
96,replace,9.0,1,c,CO[C@H](cco,CO[C@H](c1o,11,replace c at position 9 with 1,flow_matching,0.3,2.0,52,173
97,add,3.0,),,CO[C@H](c1o,CO[)C@H](c1o,12,add ) at position 3,flow_matching,0.3,2.0,52,173
98,add,12.0,4,,CO[)C@H](c1o,CO[)C@H](c1o4,13,add 4 at position 12,flow_matching,0.3,2.0,52,173
99,remove,6.0,H,,CO[)C@H](c1o4,CO[)C@](c1o4,12,remove H from position 6,flow_matching,0.3,2.0,52,173
100,remove,4.0,C,,CO[)C@](c1o4,CO[)@](c1o4,11,remove C from position 4,flow_matching,0.3,2.0,52,173
101,replace,3.0,C,),CO[)@](c1o4,CO[C@](c1o4,11,replace ) at position 3 with C,flow_matching,0.3,2.0,52,173
102,replace,10.0,[,4,CO[C@](c1o4,CO[C@](c1o[,11,replace 4 at position 10 with [,flow_matching,0.3,2.0,52,173
103,add,10.0,5,,CO[C@](c1o[,CO[C@](c1o5[,12,add 5 at position 10,flow_matching,0.3,2.0,52,173
104,replace,5.0,H,],CO[C@](c1o5[,CO[C@H(c1o5[,12,replace ] at position 5 with H,flow_matching,0.3,2.0,52,173
105,replace,6.0,4,(,CO[C@H(c1o5[,CO[C@H4c1o5[,12,replace ( at position 6 with 4,flow_matching,0.3,2.0,52,173
106,remove,0.0,C,,CO[C@H4c1o5[,O[C@H4c1o5[,11,remove C from position 0,flow_matching,0.3,2.0,52,173
107,replace,0.0,C,O,O[C@H4c1o5[,C[C@H4c1o5[,11,replace O at position 0 with C,flow_matching,0.3,2.0,52,173
108,replace,1.0,O,[,C[C@H4c1o5[,COC@H4c1o5[,11,replace [ at position 1 with O,flow_matching,0.3,2.0,52,173
109,replace,6.0,O,c,COC@H4c1o5[,COC@H4O1o5[,11,replace c at position 6 with O,flow_matching,0.3,2.0,52,173
110,replace,4.0,#,H,COC@H4O1o5[,COC@#4O1o5[,11,replace H at position 4 with #,flow_matching,0.3,2.0,52,173
111,replace,6.0,n,O,COC@#4O1o5[,COC@#4n1o5[,11,replace O at position 6 with n,flow_matching,0.3,2.0,52,173
112,remove,8.0,o,,COC@#4n1o5[,COC@#4n15[,10,remove o from position 8,flow_matching,0.3,2.0,52,173
113,add,7.0,2,,COC@#4n15[,COC@#4n215[,11,add 2 at position 7,flow_matching,0.3,2.0,52,173
114,replace,2.0,[,C,COC@#4n215[,CO[@#4n215[,11,replace C at position 2 with [,flow_matching,0.3,2.0,52,173
115,replace,8.0,3,1,CO[@#4n215[,CO[@#4n235[,11,replace 1 at position 8 with 3,flow_matching,0.3,2.0,52,173
116,replace,3.0,C,@,CO[@#4n235[,CO[C#4n235[,11,replace @ at position 3 with C,flow_matching,0.3,2.0,52,173
117,add,2.0,/,,CO[C#4n235[,CO/[C#4n235[,12,add / at position 2,flow_matching,0.3,2.0,52,173
118,add,6.0,n,,CO/[C#4n235[,CO/[C#n4n235[,13,add n at position 6,flow_matching,0.3,2.0,52,173
119,add,7.0,N,,CO/[C#n4n235[,CO/[C#nN4n235[,14,add N at position 7,flow_matching,0.3,2.0,52,173
120,replace,0.0,3,C,CO/[C#nN4n235[,3O/[C#nN4n235[,14,replace C at position 0 with 3,flow_matching,0.3,2.0,52,173
121,replace,12.0,@,5,3O/[C#nN4n235[,3O/[C#nN4n23@[,14,replace 5 at position 12 with @,flow_matching,0.3,2.0,52,173
122,replace,0.0,C,3,3O/[C#nN4n23@[,CO/[C#nN4n23@[,14,replace 3 at position 0 with C,flow_matching,0.3,2.0,52,173
123,add,5.0,/,,CO/[C#nN4n23@[,CO/[C/#nN4n23@[,15,add / at position 5,flow_matching,0.3,2.0,52,173
124,replace,2.0,[,/,CO/[C/#nN4n23@[,CO[[C/#nN4n23@[,15,replace / at position 2 with [,flow_matching,0.3,2.0,52,173
125,replace,3.0,C,[,CO[[C/#nN4n23@[,CO[CC/#nN4n23@[,15,replace [ at position 3 with C,flow_matching,0.3,2.0,52,173
126,replace,4.0,@,C,CO[CC/#nN4n23@[,CO[C@/#nN4n23@[,15,replace C at position 4 with @,flow_matching,0.3,2.0,52,173
127,replace,5.0,H,/,CO[C@/#nN4n23@[,CO[C@H#nN4n23@[,15,replace / at position 5 with H,flow_matching,0.3,2.0,52,173
128,replace,6.0,],#,CO[C@H#nN4n23@[,CO[C@H]nN4n23@[,15,replace # at position 6 with ],flow_matching,0.3,2.0,52,173
129,replace,7.0,(,n,CO[C@H]nN4n23@[,CO[C@H](N4n23@[,15,replace n at position 7 with (,flow_matching,0.3,2.0,52,173
130,replace,8.0,c,N,CO[C@H](N4n23@[,CO[C@H](c4n23@[,15,replace N at position 8 with c,flow_matching,0.3,2.0,52,173
131,replace,9.0,1,4,CO[C@H](c4n23@[,CO[C@H](c1n23@[,15,replace 4 at position 9 with 1,flow_matching,0.3,2.0,52,173
132,replace,10.0,c,n,CO[C@H](c1n23@[,CO[C@H](c1c23@[,15,replace n at position 10 with c,flow_matching,0.3,2.0,52,173
133,replace,11.0,c,2,CO[C@H](c1c23@[,CO[C@H](c1cc3@[,15,replace 2 at position 11 with c,flow_matching,0.3,2.0,52,173
134,replace,12.0,c,3,CO[C@H](c1cc3@[,CO[C@H](c1ccc@[,15,replace 3 at position 12 with c,flow_matching,0.3,2.0,52,173
135,replace,13.0,(,@,CO[C@H](c1ccc@[,CO[C@H](c1ccc([,15,replace @ at position 13 with (,flow_matching,0.3,2.0,52,173
136,replace,14.0,C,[,CO[C@H](c1ccc([,CO[C@H](c1ccc(C,15,replace [ at position 14 with C,flow_matching,0.3,2.0,52,173
137,add,15.0,l,,CO[C@H](c1ccc(C,CO[C@H](c1ccc(Cl,16,add l at position 15,flow_matching,0.3,2.0,52,173
138,add,16.0,),,CO[C@H](c1ccc(Cl,CO[C@H](c1ccc(Cl),17,add ) at position 16,flow_matching,0.3,2.0,52,173
139,add,17.0,c,,CO[C@H](c1ccc(Cl),CO[C@H](c1ccc(Cl)c,18,add c at position 17,flow_matching,0.3,2.0,52,173
140,add,18.0,c,,CO[C@H](c1ccc(Cl)c,CO[C@H](c1ccc(Cl)cc,19,add c at position 18,flow_matching,0.3,2.0,52,173
141,add,19.0,1,,CO[C@H](c1ccc(Cl)cc,CO[C@H](c1ccc(Cl)cc1,20,add 1 at position 19,flow_matching,0.3,2.0,52,173
142,add,20.0,),,CO[C@H](c1ccc(Cl)cc1,CO[C@H](c1ccc(Cl)cc1),21,add ) at position 20,flow_matching,0.3,2.0,52,173
143,add,21.0,[,,CO[C@H](c1ccc(Cl)cc1),CO[C@H](c1ccc(Cl)cc1)[,22,add [ at position 21,flow_matching,0.3,2.0,52,173
144,add,22.0,C,,CO[C@H](c1ccc(Cl)cc1)[,CO[C@H](c1ccc(Cl)cc1)[C,23,add C at position 22,flow_matching,0.3,2.0,52,173
145,add,23.0,@,,CO[C@H](c1ccc(Cl)cc1)[C,CO[C@H](c1ccc(Cl)cc1)[C@,24,add @ at position 23,flow_matching,0.3,2.0,52,173
146,add,24.0,@,,CO[C@H](c1ccc(Cl)cc1)[C@,CO[C@H](c1ccc(Cl)cc1)[C@@,25,add @ at position 24,flow_matching,0.3,2.0,52,173
147,add,25.0,H,,CO[C@H](c1ccc(Cl)cc1)[C@@,CO[C@H](c1ccc(Cl)cc1)[C@@H,26,add H at position 25,flow_matching,0.3,2.0,52,173
148,add,26.0,],,CO[C@H](c1ccc(Cl)cc1)[C@@H,CO[C@H](c1ccc(Cl)cc1)[C@@H],27,add ] at position 26,flow_matching,0.3,2.0,52,173
149,add,27.0,(,,CO[C@H](c1ccc(Cl)cc1)[C@@H],CO[C@H](c1ccc(Cl)cc1)[C@@H](,28,add ( at position 27,flow_matching,0.3,2.0,52,173
150,add,28.0,C,,CO[C@H](c1ccc(Cl)cc1)[C@@H](,CO[C@H](c1ccc(Cl)cc1)[C@@H](C,29,add C at position 28,flow_matching,0.3,2.0,52,173
151,add,29.0,),,CO[C@H](c1ccc(Cl)cc1)[C@@H](C,CO[C@H](c1ccc(Cl)cc1)[C@@H](C),30,add ) at position 29,flow_matching,0.3,2.0,52,173
152,add,30.0,N,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C),CO[C@H](c1ccc(Cl)cc1)[C@@H](C)N,31,add N at position 30,flow_matching,0.3,2.0,52,173
153,add,31.0,C,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)N,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC,32,add C at position 31,flow_matching,0.3,2.0,52,173
154,add,32.0,(,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(,33,add ( at position 32,flow_matching,0.3,2.0,52,173
155,add,33.0,=,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=,34,add = at position 33,flow_matching,0.3,2.0,52,173
156,add,34.0,O,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O,35,add O at position 34,flow_matching,0.3,2.0,52,173
157,add,35.0,),,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O),36,add ) at position 35,flow_matching,0.3,2.0,52,173
158,add,36.0,C,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O),CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C,37,add C at position 36,flow_matching,0.3,2.0,52,173
159,add,37.0,(,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(,38,add ( at position 37,flow_matching,0.3,2.0,52,173
160,add,38.0,=,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=,39,add = at position 38,flow_matching,0.3,2.0,52,173
161,add,39.0,O,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O,40,add O at position 39,flow_matching,0.3,2.0,52,173
162,add,40.0,),,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O),41,add ) at position 40,flow_matching,0.3,2.0,52,173
163,add,41.0,N,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O),CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)N,42,add N at position 41,flow_matching,0.3,2.0,52,173
164,add,42.0,c,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)N,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc,43,add c at position 42,flow_matching,0.3,2.0,52,173
165,add,43.0,1,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1,44,add 1 at position 43,flow_matching,0.3,2.0,52,173
166,add,44.0,c,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1c,45,add c at position 44,flow_matching,0.3,2.0,52,173
167,add,45.0,c,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1c,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1cc,46,add c at position 45,flow_matching,0.3,2.0,52,173
168,add,46.0,c,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1cc,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1ccc,47,add c at position 46,flow_matching,0.3,2.0,52,173
169,add,47.0,c,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1ccc,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1cccc,48,add c at position 47,flow_matching,0.3,2.0,52,173
170,add,48.0,c,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1cccc,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1ccccc,49,add c at position 48,flow_matching,0.3,2.0,52,173
171,add,49.0,1,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1ccccc,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1ccccc1,50,add 1 at position 49,flow_matching,0.3,2.0,52,173
172,add,50.0,C,,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1ccccc1,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1ccccc1C,51,add C at position 50,flow_matching,0.3,2.0,52,173
173,add,51.0,"
",,CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1ccccc1C,"CO[C@H](c1ccc(Cl)cc1)[C@@H](C)NC(=O)C(=O)Nc1ccccc1C
",52,"add 
 at position 51",flow_matching,0.3,2.0,52,173
