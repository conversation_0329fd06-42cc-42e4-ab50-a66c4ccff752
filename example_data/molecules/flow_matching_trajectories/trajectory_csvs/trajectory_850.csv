step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,35,150
1,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,35,150
2,replace,0.0,C,6,6,C,1,replace 6 at position 0 with C,flow_matching,0.3,2.0,35,150
3,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,35,150
4,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,35,150
5,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,35,150
6,add,2.0,+,,CC,CC+,3,add + at position 2,flow_matching,0.3,2.0,35,150
7,replace,2.0,C,+,CC+,CCC,3,replace + at position 2 with <PERSON>,flow_matching,0.3,2.0,35,150
8,remove,0.0,C,,CCC,CC,2,remove C from position 0,flow_matching,0.3,2.0,35,150
9,add,2.0,l,,CC,CCl,3,add l at position 2,flow_matching,0.3,2.0,35,150
10,replace,2.0,C,l,CCl,CCC,3,replace l at position 2 with C,flow_matching,0.3,2.0,35,150
11,add,3.0,C,,CCC,CCCC,4,add C at position 3,flow_matching,0.3,2.0,35,150
12,replace,3.0,\,C,CCCC,CCC\,4,replace C at position 3 with \,flow_matching,0.3,2.0,35,150
13,add,3.0,4,,CCC\,CCC4\,5,add 4 at position 3,flow_matching,0.3,2.0,35,150
14,add,4.0,),,CCC4\,CCC4)\,6,add ) at position 4,flow_matching,0.3,2.0,35,150
15,add,5.0,O,,CCC4)\,CCC4)O\,7,add O at position 5,flow_matching,0.3,2.0,35,150
16,remove,2.0,C,,CCC4)O\,CC4)O\,6,remove C from position 2,flow_matching,0.3,2.0,35,150
17,add,3.0,N,,CC4)O\,CC4N)O\,7,add N at position 3,flow_matching,0.3,2.0,35,150
18,replace,5.0,S,O,CC4N)O\,CC4N)S\,7,replace O at position 5 with S,flow_matching,0.3,2.0,35,150
19,add,0.0,B,,CC4N)S\,BCC4N)S\,8,add B at position 0,flow_matching,0.3,2.0,35,150
20,remove,5.0,),,BCC4N)S\,BCC4NS\,7,remove ) from position 5,flow_matching,0.3,2.0,35,150
21,replace,0.0,C,B,BCC4NS\,CCC4NS\,7,replace B at position 0 with C,flow_matching,0.3,2.0,35,150
22,replace,3.0,C,4,CCC4NS\,CCCCNS\,7,replace 4 at position 3 with C,flow_matching,0.3,2.0,35,150
23,remove,3.0,C,,CCCCNS\,CCCNS\,6,remove C from position 3,flow_matching,0.3,2.0,35,150
24,replace,3.0,C,N,CCCNS\,CCCCS\,6,replace N at position 3 with C,flow_matching,0.3,2.0,35,150
25,add,2.0,r,,CCCCS\,CCrCCS\,7,add r at position 2,flow_matching,0.3,2.0,35,150
26,add,5.0,O,,CCrCCS\,CCrCCOS\,8,add O at position 5,flow_matching,0.3,2.0,35,150
27,add,4.0,[,,CCrCCOS\,CCrC[COS\,9,add [ at position 4,flow_matching,0.3,2.0,35,150
28,add,8.0,s,,CCrC[COS\,CCrC[COSs\,10,add s at position 8,flow_matching,0.3,2.0,35,150
29,add,7.0,],,CCrC[COSs\,CCrC[CO]Ss\,11,add ] at position 7,flow_matching,0.3,2.0,35,150
30,add,4.0,+,,CCrC[CO]Ss\,CCrC+[CO]Ss\,12,add + at position 4,flow_matching,0.3,2.0,35,150
31,add,6.0,l,,CCrC+[CO]Ss\,CCrC+[lCO]Ss\,13,add l at position 6,flow_matching,0.3,2.0,35,150
32,add,3.0,s,,CCrC+[lCO]Ss\,CCrsC+[lCO]Ss\,14,add s at position 3,flow_matching,0.3,2.0,35,150
33,replace,2.0,C,r,CCrsC+[lCO]Ss\,CCCsC+[lCO]Ss\,14,replace r at position 2 with C,flow_matching,0.3,2.0,35,150
34,remove,13.0,\,,CCCsC+[lCO]Ss\,CCCsC+[lCO]Ss,13,remove \ from position 13,flow_matching,0.3,2.0,35,150
35,add,2.0,),,CCCsC+[lCO]Ss,CC)CsC+[lCO]Ss,14,add ) at position 2,flow_matching,0.3,2.0,35,150
36,replace,2.0,C,),CC)CsC+[lCO]Ss,CCCCsC+[lCO]Ss,14,replace ) at position 2 with C,flow_matching,0.3,2.0,35,150
37,replace,10.0,N,O,CCCCsC+[lCO]Ss,CCCCsC+[lCN]Ss,14,replace O at position 10 with N,flow_matching,0.3,2.0,35,150
38,add,1.0,4,,CCCCsC+[lCN]Ss,C4CCCsC+[lCN]Ss,15,add 4 at position 1,flow_matching,0.3,2.0,35,150
39,remove,8.0,[,,C4CCCsC+[lCN]Ss,C4CCCsC+lCN]Ss,14,remove [ from position 8,flow_matching,0.3,2.0,35,150
40,replace,1.0,C,4,C4CCCsC+lCN]Ss,CCCCCsC+lCN]Ss,14,replace 4 at position 1 with C,flow_matching,0.3,2.0,35,150
41,replace,5.0,n,s,CCCCCsC+lCN]Ss,CCCCCnC+lCN]Ss,14,replace s at position 5 with n,flow_matching,0.3,2.0,35,150
42,remove,3.0,C,,CCCCCnC+lCN]Ss,CCCCnC+lCN]Ss,13,remove C from position 3,flow_matching,0.3,2.0,35,150
43,replace,2.0,/,C,CCCCnC+lCN]Ss,CC/CnC+lCN]Ss,13,replace C at position 2 with /,flow_matching,0.3,2.0,35,150
44,replace,2.0,C,/,CC/CnC+lCN]Ss,CCCCnC+lCN]Ss,13,replace / at position 2 with C,flow_matching,0.3,2.0,35,150
45,remove,8.0,C,,CCCCnC+lCN]Ss,CCCCnC+lN]Ss,12,remove C from position 8,flow_matching,0.3,2.0,35,150
46,replace,4.0,C,n,CCCCnC+lN]Ss,CCCCCC+lN]Ss,12,replace n at position 4 with C,flow_matching,0.3,2.0,35,150
47,replace,3.0,r,C,CCCCCC+lN]Ss,CCCrCC+lN]Ss,12,replace C at position 3 with r,flow_matching,0.3,2.0,35,150
48,replace,3.0,C,r,CCCrCC+lN]Ss,CCCCCC+lN]Ss,12,replace r at position 3 with C,flow_matching,0.3,2.0,35,150
49,replace,7.0,(,l,CCCCCC+lN]Ss,CCCCCC+(N]Ss,12,replace l at position 7 with (,flow_matching,0.3,2.0,35,150
50,replace,5.0,n,C,CCCCCC+(N]Ss,CCCCCn+(N]Ss,12,replace C at position 5 with n,flow_matching,0.3,2.0,35,150
51,replace,9.0,S,],CCCCCn+(N]Ss,CCCCCn+(NSSs,12,replace ] at position 9 with S,flow_matching,0.3,2.0,35,150
52,replace,6.0,1,+,CCCCCn+(NSSs,CCCCCn1(NSSs,12,replace + at position 6 with 1,flow_matching,0.3,2.0,35,150
53,add,7.0,@,,CCCCCn1(NSSs,CCCCCn1@(NSSs,13,add @ at position 7,flow_matching,0.3,2.0,35,150
54,replace,7.0,c,@,CCCCCn1@(NSSs,CCCCCn1c(NSSs,13,replace @ at position 7 with c,flow_matching,0.3,2.0,35,150
55,add,7.0,B,,CCCCCn1c(NSSs,CCCCCn1Bc(NSSs,14,add B at position 7,flow_matching,0.3,2.0,35,150
56,add,12.0,H,,CCCCCn1Bc(NSSs,CCCCCn1Bc(NSHSs,15,add H at position 12,flow_matching,0.3,2.0,35,150
57,add,11.0,[,,CCCCCn1Bc(NSHSs,CCCCCn1Bc(N[SHSs,16,add [ at position 11,flow_matching,0.3,2.0,35,150
58,remove,2.0,C,,CCCCCn1Bc(N[SHSs,CCCCn1Bc(N[SHSs,15,remove C from position 2,flow_matching,0.3,2.0,35,150
59,remove,1.0,C,,CCCCn1Bc(N[SHSs,CCCn1Bc(N[SHSs,14,remove C from position 1,flow_matching,0.3,2.0,35,150
60,replace,1.0,],C,CCCn1Bc(N[SHSs,C]Cn1Bc(N[SHSs,14,replace C at position 1 with ],flow_matching,0.3,2.0,35,150
61,add,6.0,N,,C]Cn1Bc(N[SHSs,C]Cn1BNc(N[SHSs,15,add N at position 6,flow_matching,0.3,2.0,35,150
62,replace,1.0,C,],C]Cn1BNc(N[SHSs,CCCn1BNc(N[SHSs,15,replace ] at position 1 with C,flow_matching,0.3,2.0,35,150
63,add,14.0,),,CCCn1BNc(N[SHSs,CCCn1BNc(N[SHS)s,16,add ) at position 14,flow_matching,0.3,2.0,35,150
64,replace,3.0,C,n,CCCn1BNc(N[SHS)s,CCCC1BNc(N[SHS)s,16,replace n at position 3 with C,flow_matching,0.3,2.0,35,150
65,add,10.0,-,,CCCC1BNc(N[SHS)s,CCCC1BNc(N-[SHS)s,17,add - at position 10,flow_matching,0.3,2.0,35,150
66,add,1.0,l,,CCCC1BNc(N-[SHS)s,ClCCC1BNc(N-[SHS)s,18,add l at position 1,flow_matching,0.3,2.0,35,150
67,remove,13.0,S,,ClCCC1BNc(N-[SHS)s,ClCCC1BNc(N-[HS)s,17,remove S from position 13,flow_matching,0.3,2.0,35,150
68,replace,1.0,C,l,ClCCC1BNc(N-[HS)s,CCCCC1BNc(N-[HS)s,17,replace l at position 1 with C,flow_matching,0.3,2.0,35,150
69,remove,4.0,C,,CCCCC1BNc(N-[HS)s,CCCC1BNc(N-[HS)s,16,remove C from position 4,flow_matching,0.3,2.0,35,150
70,remove,12.0,H,,CCCC1BNc(N-[HS)s,CCCC1BNc(N-[S)s,15,remove H from position 12,flow_matching,0.3,2.0,35,150
71,replace,4.0,C,1,CCCC1BNc(N-[S)s,CCCCCBNc(N-[S)s,15,replace 1 at position 4 with C,flow_matching,0.3,2.0,35,150
72,replace,5.0,/,B,CCCCCBNc(N-[S)s,CCCCC/Nc(N-[S)s,15,replace B at position 5 with /,flow_matching,0.3,2.0,35,150
73,replace,5.0,n,/,CCCCC/Nc(N-[S)s,CCCCCnNc(N-[S)s,15,replace / at position 5 with n,flow_matching,0.3,2.0,35,150
74,replace,6.0,1,N,CCCCCnNc(N-[S)s,CCCCCn1c(N-[S)s,15,replace N at position 6 with 1,flow_matching,0.3,2.0,35,150
75,replace,9.0,S,N,CCCCCn1c(N-[S)s,CCCCCn1c(S-[S)s,15,replace N at position 9 with S,flow_matching,0.3,2.0,35,150
76,replace,10.0,C,-,CCCCCn1c(S-[S)s,CCCCCn1c(SC[S)s,15,replace - at position 10 with C,flow_matching,0.3,2.0,35,150
77,add,15.0,s,,CCCCCn1c(SC[S)s,CCCCCn1c(SC[S)ss,16,add s at position 15,flow_matching,0.3,2.0,35,150
78,replace,11.0,C,[,CCCCCn1c(SC[S)ss,CCCCCn1c(SCCS)ss,16,replace [ at position 11 with C,flow_matching,0.3,2.0,35,150
79,add,4.0,3,,CCCCCn1c(SCCS)ss,CCCC3Cn1c(SCCS)ss,17,add 3 at position 4,flow_matching,0.3,2.0,35,150
80,remove,8.0,c,,CCCC3Cn1c(SCCS)ss,CCCC3Cn1(SCCS)ss,16,remove c from position 8,flow_matching,0.3,2.0,35,150
81,add,13.0,I,,CCCC3Cn1(SCCS)ss,CCCC3Cn1(SCCSI)ss,17,add I at position 13,flow_matching,0.3,2.0,35,150
82,add,6.0,(,,CCCC3Cn1(SCCSI)ss,CCCC3C(n1(SCCSI)ss,18,add ( at position 6,flow_matching,0.3,2.0,35,150
83,replace,5.0,7,C,CCCC3C(n1(SCCSI)ss,CCCC37(n1(SCCSI)ss,18,replace C at position 5 with 7,flow_matching,0.3,2.0,35,150
84,replace,15.0,3,),CCCC37(n1(SCCSI)ss,CCCC37(n1(SCCSI3ss,18,replace ) at position 15 with 3,flow_matching,0.3,2.0,35,150
85,replace,4.0,C,3,CCCC37(n1(SCCSI3ss,CCCCC7(n1(SCCSI3ss,18,replace 3 at position 4 with C,flow_matching,0.3,2.0,35,150
86,replace,9.0,s,(,CCCCC7(n1(SCCSI3ss,CCCCC7(n1sSCCSI3ss,18,replace ( at position 9 with s,flow_matching,0.3,2.0,35,150
87,replace,17.0,4,s,CCCCC7(n1sSCCSI3ss,CCCCC7(n1sSCCSI3s4,18,replace s at position 17 with 4,flow_matching,0.3,2.0,35,150
88,remove,14.0,I,,CCCCC7(n1sSCCSI3s4,CCCCC7(n1sSCCS3s4,17,remove I from position 14,flow_matching,0.3,2.0,35,150
89,add,6.0,2,,CCCCC7(n1sSCCS3s4,CCCCC72(n1sSCCS3s4,18,add 2 at position 6,flow_matching,0.3,2.0,35,150
90,remove,3.0,C,,CCCCC72(n1sSCCS3s4,CCCC72(n1sSCCS3s4,17,remove C from position 3,flow_matching,0.3,2.0,35,150
91,add,11.0,I,,CCCC72(n1sSCCS3s4,CCCC72(n1sSICCS3s4,18,add I at position 11,flow_matching,0.3,2.0,35,150
92,add,17.0,n,,CCCC72(n1sSICCS3s4,CCCC72(n1sSICCS3sn4,19,add n at position 17,flow_matching,0.3,2.0,35,150
93,replace,17.0,r,n,CCCC72(n1sSICCS3sn4,CCCC72(n1sSICCS3sr4,19,replace n at position 17 with r,flow_matching,0.3,2.0,35,150
94,remove,12.0,C,,CCCC72(n1sSICCS3sr4,CCCC72(n1sSICS3sr4,18,remove C from position 12,flow_matching,0.3,2.0,35,150
95,add,13.0,\,,CCCC72(n1sSICS3sr4,CCCC72(n1sSIC\S3sr4,19,add \ at position 13,flow_matching,0.3,2.0,35,150
96,replace,5.0,[,2,CCCC72(n1sSIC\S3sr4,CCCC7[(n1sSIC\S3sr4,19,replace 2 at position 5 with [,flow_matching,0.3,2.0,35,150
97,replace,4.0,C,7,CCCC7[(n1sSIC\S3sr4,CCCCC[(n1sSIC\S3sr4,19,replace 7 at position 4 with C,flow_matching,0.3,2.0,35,150
98,remove,13.0,\,,CCCCC[(n1sSIC\S3sr4,CCCCC[(n1sSICS3sr4,18,remove \ from position 13,flow_matching,0.3,2.0,35,150
99,remove,13.0,S,,CCCCC[(n1sSICS3sr4,CCCCC[(n1sSIC3sr4,17,remove S from position 13,flow_matching,0.3,2.0,35,150
100,replace,13.0,7,3,CCCCC[(n1sSIC3sr4,CCCCC[(n1sSIC7sr4,17,replace 3 at position 13 with 7,flow_matching,0.3,2.0,35,150
101,replace,5.0,n,[,CCCCC[(n1sSIC7sr4,CCCCCn(n1sSIC7sr4,17,replace [ at position 5 with n,flow_matching,0.3,2.0,35,150
102,remove,15.0,r,,CCCCCn(n1sSIC7sr4,CCCCCn(n1sSIC7s4,16,remove r from position 15,flow_matching,0.3,2.0,35,150
103,replace,10.0,@,S,CCCCCn(n1sSIC7s4,CCCCCn(n1s@IC7s4,16,replace S at position 10 with @,flow_matching,0.3,2.0,35,150
104,add,6.0,3,,CCCCCn(n1s@IC7s4,CCCCCn3(n1s@IC7s4,17,add 3 at position 6,flow_matching,0.3,2.0,35,150
105,add,13.0,],,CCCCCn3(n1s@IC7s4,CCCCCn3(n1s@I]C7s4,18,add ] at position 13,flow_matching,0.3,2.0,35,150
106,replace,2.0,),C,CCCCCn3(n1s@I]C7s4,CC)CCn3(n1s@I]C7s4,18,replace C at position 2 with ),flow_matching,0.3,2.0,35,150
107,add,18.0,-,,CC)CCn3(n1s@I]C7s4,CC)CCn3(n1s@I]C7s4-,19,add - at position 18,flow_matching,0.3,2.0,35,150
108,replace,2.0,C,),CC)CCn3(n1s@I]C7s4-,CCCCCn3(n1s@I]C7s4-,19,replace ) at position 2 with C,flow_matching,0.3,2.0,35,150
109,replace,6.0,1,3,CCCCCn3(n1s@I]C7s4-,CCCCCn1(n1s@I]C7s4-,19,replace 3 at position 6 with 1,flow_matching,0.3,2.0,35,150
110,replace,7.0,c,(,CCCCCn1(n1s@I]C7s4-,CCCCCn1cn1s@I]C7s4-,19,replace ( at position 7 with c,flow_matching,0.3,2.0,35,150
111,add,15.0,l,,CCCCCn1cn1s@I]C7s4-,CCCCCn1cn1s@I]Cl7s4-,20,add l at position 15,flow_matching,0.3,2.0,35,150
112,add,17.0,H,,CCCCCn1cn1s@I]Cl7s4-,CCCCCn1cn1s@I]Cl7Hs4-,21,add H at position 17,flow_matching,0.3,2.0,35,150
113,replace,18.0,l,s,CCCCCn1cn1s@I]Cl7Hs4-,CCCCCn1cn1s@I]Cl7Hl4-,21,replace s at position 18 with l,flow_matching,0.3,2.0,35,150
114,remove,20.0,-,,CCCCCn1cn1s@I]Cl7Hl4-,CCCCCn1cn1s@I]Cl7Hl4,20,remove - from position 20,flow_matching,0.3,2.0,35,150
115,remove,17.0,H,,CCCCCn1cn1s@I]Cl7Hl4,CCCCCn1cn1s@I]Cl7l4,19,remove H from position 17,flow_matching,0.3,2.0,35,150
116,replace,8.0,(,n,CCCCCn1cn1s@I]Cl7l4,CCCCCn1c(1s@I]Cl7l4,19,replace n at position 8 with (,flow_matching,0.3,2.0,35,150
117,add,1.0,(,,CCCCCn1c(1s@I]Cl7l4,C(CCCCn1c(1s@I]Cl7l4,20,add ( at position 1,flow_matching,0.3,2.0,35,150
118,replace,0.0,2,C,C(CCCCn1c(1s@I]Cl7l4,2(CCCCn1c(1s@I]Cl7l4,20,replace C at position 0 with 2,flow_matching,0.3,2.0,35,150
119,replace,0.0,C,2,2(CCCCn1c(1s@I]Cl7l4,C(CCCCn1c(1s@I]Cl7l4,20,replace 2 at position 0 with C,flow_matching,0.3,2.0,35,150
120,replace,1.0,C,(,C(CCCCn1c(1s@I]Cl7l4,CCCCCCn1c(1s@I]Cl7l4,20,replace ( at position 1 with C,flow_matching,0.3,2.0,35,150
121,replace,5.0,n,C,CCCCCCn1c(1s@I]Cl7l4,CCCCCnn1c(1s@I]Cl7l4,20,replace C at position 5 with n,flow_matching,0.3,2.0,35,150
122,replace,6.0,1,n,CCCCCnn1c(1s@I]Cl7l4,CCCCCn11c(1s@I]Cl7l4,20,replace n at position 6 with 1,flow_matching,0.3,2.0,35,150
123,replace,7.0,c,1,CCCCCn11c(1s@I]Cl7l4,CCCCCn1cc(1s@I]Cl7l4,20,replace 1 at position 7 with c,flow_matching,0.3,2.0,35,150
124,replace,8.0,(,c,CCCCCn1cc(1s@I]Cl7l4,CCCCCn1c((1s@I]Cl7l4,20,replace c at position 8 with (,flow_matching,0.3,2.0,35,150
125,replace,9.0,S,(,CCCCCn1c((1s@I]Cl7l4,CCCCCn1c(S1s@I]Cl7l4,20,replace ( at position 9 with S,flow_matching,0.3,2.0,35,150
126,replace,10.0,C,1,CCCCCn1c(S1s@I]Cl7l4,CCCCCn1c(SCs@I]Cl7l4,20,replace 1 at position 10 with C,flow_matching,0.3,2.0,35,150
127,replace,11.0,C,s,CCCCCn1c(SCs@I]Cl7l4,CCCCCn1c(SCC@I]Cl7l4,20,replace s at position 11 with C,flow_matching,0.3,2.0,35,150
128,replace,12.0,(,@,CCCCCn1c(SCC@I]Cl7l4,CCCCCn1c(SCC(I]Cl7l4,20,replace @ at position 12 with (,flow_matching,0.3,2.0,35,150
129,replace,13.0,=,I,CCCCCn1c(SCC(I]Cl7l4,CCCCCn1c(SCC(=]Cl7l4,20,replace I at position 13 with =,flow_matching,0.3,2.0,35,150
130,replace,14.0,O,],CCCCCn1c(SCC(=]Cl7l4,CCCCCn1c(SCC(=OCl7l4,20,replace ] at position 14 with O,flow_matching,0.3,2.0,35,150
131,replace,15.0,),C,CCCCCn1c(SCC(=OCl7l4,CCCCCn1c(SCC(=O)l7l4,20,replace C at position 15 with ),flow_matching,0.3,2.0,35,150
132,replace,16.0,[,l,CCCCCn1c(SCC(=O)l7l4,CCCCCn1c(SCC(=O)[7l4,20,replace l at position 16 with [,flow_matching,0.3,2.0,35,150
133,replace,17.0,O,7,CCCCCn1c(SCC(=O)[7l4,CCCCCn1c(SCC(=O)[Ol4,20,replace 7 at position 17 with O,flow_matching,0.3,2.0,35,150
134,replace,18.0,-,l,CCCCCn1c(SCC(=O)[Ol4,CCCCCn1c(SCC(=O)[O-4,20,replace l at position 18 with -,flow_matching,0.3,2.0,35,150
135,replace,19.0,],4,CCCCCn1c(SCC(=O)[O-4,CCCCCn1c(SCC(=O)[O-],20,replace 4 at position 19 with ],flow_matching,0.3,2.0,35,150
136,add,20.0,),,CCCCCn1c(SCC(=O)[O-],CCCCCn1c(SCC(=O)[O-]),21,add ) at position 20,flow_matching,0.3,2.0,35,150
137,add,21.0,n,,CCCCCn1c(SCC(=O)[O-]),CCCCCn1c(SCC(=O)[O-])n,22,add n at position 21,flow_matching,0.3,2.0,35,150
138,add,22.0,c,,CCCCCn1c(SCC(=O)[O-])n,CCCCCn1c(SCC(=O)[O-])nc,23,add c at position 22,flow_matching,0.3,2.0,35,150
139,add,23.0,2,,CCCCCn1c(SCC(=O)[O-])nc,CCCCCn1c(SCC(=O)[O-])nc2,24,add 2 at position 23,flow_matching,0.3,2.0,35,150
140,add,24.0,c,,CCCCCn1c(SCC(=O)[O-])nc2,CCCCCn1c(SCC(=O)[O-])nc2c,25,add c at position 24,flow_matching,0.3,2.0,35,150
141,add,25.0,c,,CCCCCn1c(SCC(=O)[O-])nc2c,CCCCCn1c(SCC(=O)[O-])nc2cc,26,add c at position 25,flow_matching,0.3,2.0,35,150
142,add,26.0,c,,CCCCCn1c(SCC(=O)[O-])nc2cc,CCCCCn1c(SCC(=O)[O-])nc2ccc,27,add c at position 26,flow_matching,0.3,2.0,35,150
143,add,27.0,c,,CCCCCn1c(SCC(=O)[O-])nc2ccc,CCCCCn1c(SCC(=O)[O-])nc2cccc,28,add c at position 27,flow_matching,0.3,2.0,35,150
144,add,28.0,c,,CCCCCn1c(SCC(=O)[O-])nc2cccc,CCCCCn1c(SCC(=O)[O-])nc2ccccc,29,add c at position 28,flow_matching,0.3,2.0,35,150
145,add,29.0,2,,CCCCCn1c(SCC(=O)[O-])nc2ccccc,CCCCCn1c(SCC(=O)[O-])nc2ccccc2,30,add 2 at position 29,flow_matching,0.3,2.0,35,150
146,add,30.0,c,,CCCCCn1c(SCC(=O)[O-])nc2ccccc2,CCCCCn1c(SCC(=O)[O-])nc2ccccc2c,31,add c at position 30,flow_matching,0.3,2.0,35,150
147,add,31.0,1,,CCCCCn1c(SCC(=O)[O-])nc2ccccc2c,CCCCCn1c(SCC(=O)[O-])nc2ccccc2c1,32,add 1 at position 31,flow_matching,0.3,2.0,35,150
148,add,32.0,=,,CCCCCn1c(SCC(=O)[O-])nc2ccccc2c1,CCCCCn1c(SCC(=O)[O-])nc2ccccc2c1=,33,add = at position 32,flow_matching,0.3,2.0,35,150
149,add,33.0,O,,CCCCCn1c(SCC(=O)[O-])nc2ccccc2c1=,CCCCCn1c(SCC(=O)[O-])nc2ccccc2c1=O,34,add O at position 33,flow_matching,0.3,2.0,35,150
150,add,34.0,"
",,CCCCCn1c(SCC(=O)[O-])nc2ccccc2c1=O,"CCCCCn1c(SCC(=O)[O-])nc2ccccc2c1=O
",35,"add 
 at position 34",flow_matching,0.3,2.0,35,150
