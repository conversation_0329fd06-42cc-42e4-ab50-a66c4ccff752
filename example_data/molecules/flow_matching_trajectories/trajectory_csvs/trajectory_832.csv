step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,51,230
1,add,0.0,-,,,-,1,add - at position 0,flow_matching,0.3,2.0,51,230
2,replace,0.0,O,-,-,O,1,replace - at position 0 with O,flow_matching,0.3,2.0,51,230
3,add,0.0,(,,O,(O,2,add ( at position 0,flow_matching,0.3,2.0,51,230
4,replace,0.0,4,(,(O,4O,2,replace ( at position 0 with 4,flow_matching,0.3,2.0,51,230
5,replace,0.0,O,4,4O,OO,2,replace 4 at position 0 with O,flow_matching,0.3,2.0,51,230
6,replace,1.0,/,O,OO,O/,2,replace O at position 1 with /,flow_matching,0.3,2.0,51,230
7,remove,1.0,/,,O/,O,1,remove / from position 1,flow_matching,0.3,2.0,51,230
8,add,0.0,#,,O,#O,2,add # at position 0,flow_matching,0.3,2.0,51,230
9,remove,0.0,#,,#O,O,1,remove # from position 0,flow_matching,0.3,2.0,51,230
10,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,51,230
11,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,51,230
12,remove,0.0,],,],,0,remove ] from position 0,flow_matching,0.3,2.0,51,230
13,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,51,230
14,add,1.0,o,,/,/o,2,add o at position 1,flow_matching,0.3,2.0,51,230
15,replace,0.0,5,/,/o,5o,2,replace / at position 0 with 5,flow_matching,0.3,2.0,51,230
16,remove,0.0,5,,5o,o,1,remove 5 from position 0,flow_matching,0.3,2.0,51,230
17,remove,0.0,o,,o,,0,remove o from position 0,flow_matching,0.3,2.0,51,230
18,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,51,230
19,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,51,230
20,add,2.0,C,,O=,O=C,3,add C at position 2,flow_matching,0.3,2.0,51,230
21,remove,1.0,=,,O=C,OC,2,remove = from position 1,flow_matching,0.3,2.0,51,230
22,add,2.0,/,,OC,OC/,3,add / at position 2,flow_matching,0.3,2.0,51,230
23,remove,0.0,O,,OC/,C/,2,remove O from position 0,flow_matching,0.3,2.0,51,230
24,remove,1.0,/,,C/,C,1,remove / from position 1,flow_matching,0.3,2.0,51,230
25,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,51,230
26,remove,0.0,C,,CC,C,1,remove C from position 0,flow_matching,0.3,2.0,51,230
27,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,51,230
28,add,0.0,),,,),1,add ) at position 0,flow_matching,0.3,2.0,51,230
29,add,0.0,),,),)),2,add ) at position 0,flow_matching,0.3,2.0,51,230
30,remove,0.0,),,)),),1,remove ) from position 0,flow_matching,0.3,2.0,51,230
31,replace,0.0,[,),),[,1,replace ) at position 0 with [,flow_matching,0.3,2.0,51,230
32,add,0.0,@,,[,@[,2,add @ at position 0,flow_matching,0.3,2.0,51,230
33,replace,0.0,O,@,@[,O[,2,replace @ at position 0 with O,flow_matching,0.3,2.0,51,230
34,replace,1.0,=,[,O[,O=,2,replace [ at position 1 with =,flow_matching,0.3,2.0,51,230
35,remove,1.0,=,,O=,O,1,remove = from position 1,flow_matching,0.3,2.0,51,230
36,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,51,230
37,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,51,230
38,remove,0.0,7,,7,,0,remove 7 from position 0,flow_matching,0.3,2.0,51,230
39,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,51,230
40,add,0.0,s,,O,sO,2,add s at position 0,flow_matching,0.3,2.0,51,230
41,add,1.0,@,,sO,s@O,3,add @ at position 1,flow_matching,0.3,2.0,51,230
42,add,2.0,H,,s@O,s@HO,4,add H at position 2,flow_matching,0.3,2.0,51,230
43,add,3.0,n,,s@HO,s@HnO,5,add n at position 3,flow_matching,0.3,2.0,51,230
44,replace,0.0,O,s,s@HnO,O@HnO,5,replace s at position 0 with O,flow_matching,0.3,2.0,51,230
45,replace,1.0,=,@,O@HnO,O=HnO,5,replace @ at position 1 with =,flow_matching,0.3,2.0,51,230
46,replace,2.0,-,H,O=HnO,O=-nO,5,replace H at position 2 with -,flow_matching,0.3,2.0,51,230
47,remove,3.0,n,,O=-nO,O=-O,4,remove n from position 3,flow_matching,0.3,2.0,51,230
48,replace,0.0,6,O,O=-O,6=-O,4,replace O at position 0 with 6,flow_matching,0.3,2.0,51,230
49,replace,0.0,4,6,6=-O,4=-O,4,replace 6 at position 0 with 4,flow_matching,0.3,2.0,51,230
50,replace,0.0,O,4,4=-O,O=-O,4,replace 4 at position 0 with O,flow_matching,0.3,2.0,51,230
51,replace,0.0,4,O,O=-O,4=-O,4,replace O at position 0 with 4,flow_matching,0.3,2.0,51,230
52,replace,1.0,2,=,4=-O,42-O,4,replace = at position 1 with 2,flow_matching,0.3,2.0,51,230
53,add,0.0,),,42-O,)42-O,5,add ) at position 0,flow_matching,0.3,2.0,51,230
54,remove,0.0,),,)42-O,42-O,4,remove ) from position 0,flow_matching,0.3,2.0,51,230
55,remove,2.0,-,,42-O,42O,3,remove - from position 2,flow_matching,0.3,2.0,51,230
56,remove,2.0,O,,42O,42,2,remove O from position 2,flow_matching,0.3,2.0,51,230
57,replace,0.0,[,4,42,[2,2,replace 4 at position 0 with [,flow_matching,0.3,2.0,51,230
58,remove,1.0,2,,[2,[,1,remove 2 from position 1,flow_matching,0.3,2.0,51,230
59,remove,0.0,[,,[,,0,remove [ from position 0,flow_matching,0.3,2.0,51,230
60,add,0.0,o,,,o,1,add o at position 0,flow_matching,0.3,2.0,51,230
61,add,0.0,],,o,]o,2,add ] at position 0,flow_matching,0.3,2.0,51,230
62,replace,0.0,O,],]o,Oo,2,replace ] at position 0 with O,flow_matching,0.3,2.0,51,230
63,add,1.0,o,,Oo,Ooo,3,add o at position 1,flow_matching,0.3,2.0,51,230
64,replace,1.0,=,o,Ooo,O=o,3,replace o at position 1 with =,flow_matching,0.3,2.0,51,230
65,remove,1.0,=,,O=o,Oo,2,remove = from position 1,flow_matching,0.3,2.0,51,230
66,remove,1.0,o,,Oo,O,1,remove o from position 1,flow_matching,0.3,2.0,51,230
67,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,51,230
68,add,0.0,1,,,1,1,add 1 at position 0,flow_matching,0.3,2.0,51,230
69,replace,0.0,s,1,1,s,1,replace 1 at position 0 with s,flow_matching,0.3,2.0,51,230
70,add,1.0,4,,s,s4,2,add 4 at position 1,flow_matching,0.3,2.0,51,230
71,add,1.0,F,,s4,sF4,3,add F at position 1,flow_matching,0.3,2.0,51,230
72,replace,0.0,O,s,sF4,OF4,3,replace s at position 0 with O,flow_matching,0.3,2.0,51,230
73,replace,2.0,N,4,OF4,OFN,3,replace 4 at position 2 with N,flow_matching,0.3,2.0,51,230
74,add,3.0,r,,OFN,OFNr,4,add r at position 3,flow_matching,0.3,2.0,51,230
75,replace,1.0,=,F,OFNr,O=Nr,4,replace F at position 1 with =,flow_matching,0.3,2.0,51,230
76,add,2.0,2,,O=Nr,O=2Nr,5,add 2 at position 2,flow_matching,0.3,2.0,51,230
77,add,1.0,+,,O=2Nr,O+=2Nr,6,add + at position 1,flow_matching,0.3,2.0,51,230
78,add,5.0,5,,O+=2Nr,O+=2N5r,7,add 5 at position 5,flow_matching,0.3,2.0,51,230
79,replace,1.0,=,+,O+=2N5r,O==2N5r,7,replace + at position 1 with =,flow_matching,0.3,2.0,51,230
80,remove,0.0,O,,O==2N5r,==2N5r,6,remove O from position 0,flow_matching,0.3,2.0,51,230
81,replace,0.0,O,=,==2N5r,O=2N5r,6,replace = at position 0 with O,flow_matching,0.3,2.0,51,230
82,replace,2.0,C,2,O=2N5r,O=CN5r,6,replace 2 at position 2 with C,flow_matching,0.3,2.0,51,230
83,remove,1.0,=,,O=CN5r,OCN5r,5,remove = from position 1,flow_matching,0.3,2.0,51,230
84,replace,1.0,=,C,OCN5r,O=N5r,5,replace C at position 1 with =,flow_matching,0.3,2.0,51,230
85,replace,2.0,C,N,O=N5r,O=C5r,5,replace N at position 2 with C,flow_matching,0.3,2.0,51,230
86,replace,3.0,(,5,O=C5r,O=C(r,5,replace 5 at position 3 with (,flow_matching,0.3,2.0,51,230
87,replace,4.0,s,r,O=C(r,O=C(s,5,replace r at position 4 with s,flow_matching,0.3,2.0,51,230
88,replace,2.0,r,C,O=C(s,O=r(s,5,replace C at position 2 with r,flow_matching,0.3,2.0,51,230
89,remove,3.0,(,,O=r(s,O=rs,4,remove ( from position 3,flow_matching,0.3,2.0,51,230
90,remove,1.0,=,,O=rs,Ors,3,remove = from position 1,flow_matching,0.3,2.0,51,230
91,replace,0.0,F,O,Ors,Frs,3,replace O at position 0 with F,flow_matching,0.3,2.0,51,230
92,replace,0.0,O,F,Frs,Ors,3,replace F at position 0 with O,flow_matching,0.3,2.0,51,230
93,remove,0.0,O,,Ors,rs,2,remove O from position 0,flow_matching,0.3,2.0,51,230
94,replace,0.0,O,r,rs,Os,2,replace r at position 0 with O,flow_matching,0.3,2.0,51,230
95,add,1.0,I,,Os,OIs,3,add I at position 1,flow_matching,0.3,2.0,51,230
96,replace,1.0,=,I,OIs,O=s,3,replace I at position 1 with =,flow_matching,0.3,2.0,51,230
97,remove,1.0,=,,O=s,Os,2,remove = from position 1,flow_matching,0.3,2.0,51,230
98,replace,1.0,=,s,Os,O=,2,replace s at position 1 with =,flow_matching,0.3,2.0,51,230
99,replace,0.0,C,O,O=,C=,2,replace O at position 0 with C,flow_matching,0.3,2.0,51,230
100,remove,1.0,=,,C=,C,1,remove = from position 1,flow_matching,0.3,2.0,51,230
101,add,1.0,@,,C,C@,2,add @ at position 1,flow_matching,0.3,2.0,51,230
102,add,2.0,3,,C@,C@3,3,add 3 at position 2,flow_matching,0.3,2.0,51,230
103,replace,2.0,6,3,C@3,C@6,3,replace 3 at position 2 with 6,flow_matching,0.3,2.0,51,230
104,replace,2.0,5,6,C@6,C@5,3,replace 6 at position 2 with 5,flow_matching,0.3,2.0,51,230
105,replace,0.0,@,C,C@5,@@5,3,replace C at position 0 with @,flow_matching,0.3,2.0,51,230
106,replace,0.0,O,@,@@5,O@5,3,replace @ at position 0 with O,flow_matching,0.3,2.0,51,230
107,remove,2.0,5,,O@5,O@,2,remove 5 from position 2,flow_matching,0.3,2.0,51,230
108,remove,1.0,@,,O@,O,1,remove @ from position 1,flow_matching,0.3,2.0,51,230
109,add,0.0,1,,O,1O,2,add 1 at position 0,flow_matching,0.3,2.0,51,230
110,replace,1.0,C,O,1O,1C,2,replace O at position 1 with C,flow_matching,0.3,2.0,51,230
111,replace,1.0,r,C,1C,1r,2,replace C at position 1 with r,flow_matching,0.3,2.0,51,230
112,replace,0.0,O,1,1r,Or,2,replace 1 at position 0 with O,flow_matching,0.3,2.0,51,230
113,remove,0.0,O,,Or,r,1,remove O from position 0,flow_matching,0.3,2.0,51,230
114,remove,0.0,r,,r,,0,remove r from position 0,flow_matching,0.3,2.0,51,230
115,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,51,230
116,replace,0.0,s,F,F,s,1,replace F at position 0 with s,flow_matching,0.3,2.0,51,230
117,replace,0.0,O,s,s,O,1,replace s at position 0 with O,flow_matching,0.3,2.0,51,230
118,replace,0.0,r,O,O,r,1,replace O at position 0 with r,flow_matching,0.3,2.0,51,230
119,replace,0.0,[,r,r,[,1,replace r at position 0 with [,flow_matching,0.3,2.0,51,230
120,replace,0.0,B,[,[,B,1,replace [ at position 0 with B,flow_matching,0.3,2.0,51,230
121,remove,0.0,B,,B,,0,remove B from position 0,flow_matching,0.3,2.0,51,230
122,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,51,230
123,add,0.0,l,,c,lc,2,add l at position 0,flow_matching,0.3,2.0,51,230
124,replace,0.0,O,l,lc,Oc,2,replace l at position 0 with O,flow_matching,0.3,2.0,51,230
125,add,0.0,H,,Oc,HOc,3,add H at position 0,flow_matching,0.3,2.0,51,230
126,replace,0.0,O,H,HOc,OOc,3,replace H at position 0 with O,flow_matching,0.3,2.0,51,230
127,add,0.0,c,,OOc,cOOc,4,add c at position 0,flow_matching,0.3,2.0,51,230
128,add,3.0,-,,cOOc,cOO-c,5,add - at position 3,flow_matching,0.3,2.0,51,230
129,replace,0.0,O,c,cOO-c,OOO-c,5,replace c at position 0 with O,flow_matching,0.3,2.0,51,230
130,replace,1.0,=,O,OOO-c,O=O-c,5,replace O at position 1 with =,flow_matching,0.3,2.0,51,230
131,replace,2.0,C,O,O=O-c,O=C-c,5,replace O at position 2 with C,flow_matching,0.3,2.0,51,230
132,add,2.0,B,,O=C-c,O=BC-c,6,add B at position 2,flow_matching,0.3,2.0,51,230
133,replace,2.0,n,B,O=BC-c,O=nC-c,6,replace B at position 2 with n,flow_matching,0.3,2.0,51,230
134,remove,1.0,=,,O=nC-c,OnC-c,5,remove = from position 1,flow_matching,0.3,2.0,51,230
135,replace,1.0,=,n,OnC-c,O=C-c,5,replace n at position 1 with =,flow_matching,0.3,2.0,51,230
136,add,4.0,c,,O=C-c,O=C-cc,6,add c at position 4,flow_matching,0.3,2.0,51,230
137,replace,5.0,+,c,O=C-cc,O=C-c+,6,replace c at position 5 with +,flow_matching,0.3,2.0,51,230
138,add,4.0,S,,O=C-c+,O=C-Sc+,7,add S at position 4,flow_matching,0.3,2.0,51,230
139,add,2.0,N,,O=C-Sc+,O=NC-Sc+,8,add N at position 2,flow_matching,0.3,2.0,51,230
140,add,8.0,l,,O=NC-Sc+,O=NC-Sc+l,9,add l at position 8,flow_matching,0.3,2.0,51,230
141,replace,1.0,4,=,O=NC-Sc+l,O4NC-Sc+l,9,replace = at position 1 with 4,flow_matching,0.3,2.0,51,230
142,remove,7.0,+,,O4NC-Sc+l,O4NC-Scl,8,remove + from position 7,flow_matching,0.3,2.0,51,230
143,add,1.0,o,,O4NC-Scl,Oo4NC-Scl,9,add o at position 1,flow_matching,0.3,2.0,51,230
144,replace,4.0,+,C,Oo4NC-Scl,Oo4N+-Scl,9,replace C at position 4 with +,flow_matching,0.3,2.0,51,230
145,replace,1.0,=,o,Oo4N+-Scl,O=4N+-Scl,9,replace o at position 1 with =,flow_matching,0.3,2.0,51,230
146,replace,2.0,C,4,O=4N+-Scl,O=CN+-Scl,9,replace 4 at position 2 with C,flow_matching,0.3,2.0,51,230
147,remove,7.0,c,,O=CN+-Scl,O=CN+-Sl,8,remove c from position 7,flow_matching,0.3,2.0,51,230
148,remove,4.0,+,,O=CN+-Sl,O=CN-Sl,7,remove + from position 4,flow_matching,0.3,2.0,51,230
149,remove,6.0,l,,O=CN-Sl,O=CN-S,6,remove l from position 6,flow_matching,0.3,2.0,51,230
150,remove,0.0,O,,O=CN-S,=CN-S,5,remove O from position 0,flow_matching,0.3,2.0,51,230
151,replace,3.0,B,-,=CN-S,=CNBS,5,replace - at position 3 with B,flow_matching,0.3,2.0,51,230
152,replace,0.0,O,=,=CNBS,OCNBS,5,replace = at position 0 with O,flow_matching,0.3,2.0,51,230
153,add,5.0,\,,OCNBS,OCNBS\,6,add \ at position 5,flow_matching,0.3,2.0,51,230
154,replace,5.0,(,\,OCNBS\,OCNBS(,6,replace \ at position 5 with (,flow_matching,0.3,2.0,51,230
155,add,0.0,o,,OCNBS(,oOCNBS(,7,add o at position 0,flow_matching,0.3,2.0,51,230
156,replace,4.0,r,B,oOCNBS(,oOCNrS(,7,replace B at position 4 with r,flow_matching,0.3,2.0,51,230
157,remove,4.0,r,,oOCNrS(,oOCNS(,6,remove r from position 4,flow_matching,0.3,2.0,51,230
158,replace,2.0,5,C,oOCNS(,oO5NS(,6,replace C at position 2 with 5,flow_matching,0.3,2.0,51,230
159,remove,3.0,N,,oO5NS(,oO5S(,5,remove N from position 3,flow_matching,0.3,2.0,51,230
160,add,2.0,=,,oO5S(,oO=5S(,6,add = at position 2,flow_matching,0.3,2.0,51,230
161,remove,5.0,(,,oO=5S(,oO=5S,5,remove ( from position 5,flow_matching,0.3,2.0,51,230
162,replace,0.0,O,o,oO=5S,OO=5S,5,replace o at position 0 with O,flow_matching,0.3,2.0,51,230
163,add,2.0,/,,OO=5S,OO/=5S,6,add / at position 2,flow_matching,0.3,2.0,51,230
164,remove,4.0,5,,OO/=5S,OO/=S,5,remove 5 from position 4,flow_matching,0.3,2.0,51,230
165,add,0.0,-,,OO/=S,-OO/=S,6,add - at position 0,flow_matching,0.3,2.0,51,230
166,remove,1.0,O,,-OO/=S,-O/=S,5,remove O from position 1,flow_matching,0.3,2.0,51,230
167,replace,3.0,\,=,-O/=S,-O/\S,5,replace = at position 3 with \,flow_matching,0.3,2.0,51,230
168,remove,0.0,-,,-O/\S,O/\S,4,remove - from position 0,flow_matching,0.3,2.0,51,230
169,replace,1.0,=,/,O/\S,O=\S,4,replace / at position 1 with =,flow_matching,0.3,2.0,51,230
170,remove,3.0,S,,O=\S,O=\,3,remove S from position 3,flow_matching,0.3,2.0,51,230
171,add,0.0,l,,O=\,lO=\,4,add l at position 0,flow_matching,0.3,2.0,51,230
172,replace,0.0,O,l,lO=\,OO=\,4,replace l at position 0 with O,flow_matching,0.3,2.0,51,230
173,replace,3.0,2,\,OO=\,OO=2,4,replace \ at position 3 with 2,flow_matching,0.3,2.0,51,230
174,add,1.0,I,,OO=2,OIO=2,5,add I at position 1,flow_matching,0.3,2.0,51,230
175,replace,1.0,O,I,OIO=2,OOO=2,5,replace I at position 1 with O,flow_matching,0.3,2.0,51,230
176,replace,0.0,S,O,OOO=2,SOO=2,5,replace O at position 0 with S,flow_matching,0.3,2.0,51,230
177,replace,0.0,O,S,SOO=2,OOO=2,5,replace S at position 0 with O,flow_matching,0.3,2.0,51,230
178,replace,1.0,=,O,OOO=2,O=O=2,5,replace O at position 1 with =,flow_matching,0.3,2.0,51,230
179,replace,1.0,6,=,O=O=2,O6O=2,5,replace = at position 1 with 6,flow_matching,0.3,2.0,51,230
180,add,1.0,/,,O6O=2,O/6O=2,6,add / at position 1,flow_matching,0.3,2.0,51,230
181,replace,1.0,=,/,O/6O=2,O=6O=2,6,replace / at position 1 with =,flow_matching,0.3,2.0,51,230
182,replace,2.0,C,6,O=6O=2,O=CO=2,6,replace 6 at position 2 with C,flow_matching,0.3,2.0,51,230
183,replace,3.0,(,O,O=CO=2,O=C(=2,6,replace O at position 3 with (,flow_matching,0.3,2.0,51,230
184,replace,4.0,C,=,O=C(=2,O=C(C2,6,replace = at position 4 with C,flow_matching,0.3,2.0,51,230
185,replace,5.0,n,2,O=C(C2,O=C(Cn,6,replace 2 at position 5 with n,flow_matching,0.3,2.0,51,230
186,add,6.0,1,,O=C(Cn,O=C(Cn1,7,add 1 at position 6,flow_matching,0.3,2.0,51,230
187,add,7.0,c,,O=C(Cn1,O=C(Cn1c,8,add c at position 7,flow_matching,0.3,2.0,51,230
188,add,8.0,(,,O=C(Cn1c,O=C(Cn1c(,9,add ( at position 8,flow_matching,0.3,2.0,51,230
189,add,9.0,=,,O=C(Cn1c(,O=C(Cn1c(=,10,add = at position 9,flow_matching,0.3,2.0,51,230
190,add,10.0,O,,O=C(Cn1c(=,O=C(Cn1c(=O,11,add O at position 10,flow_matching,0.3,2.0,51,230
191,add,11.0,),,O=C(Cn1c(=O,O=C(Cn1c(=O),12,add ) at position 11,flow_matching,0.3,2.0,51,230
192,add,12.0,c,,O=C(Cn1c(=O),O=C(Cn1c(=O)c,13,add c at position 12,flow_matching,0.3,2.0,51,230
193,add,13.0,(,,O=C(Cn1c(=O)c,O=C(Cn1c(=O)c(,14,add ( at position 13,flow_matching,0.3,2.0,51,230
194,add,14.0,=,,O=C(Cn1c(=O)c(,O=C(Cn1c(=O)c(=,15,add = at position 14,flow_matching,0.3,2.0,51,230
195,add,15.0,O,,O=C(Cn1c(=O)c(=,O=C(Cn1c(=O)c(=O,16,add O at position 15,flow_matching,0.3,2.0,51,230
196,add,16.0,),,O=C(Cn1c(=O)c(=O,O=C(Cn1c(=O)c(=O),17,add ) at position 16,flow_matching,0.3,2.0,51,230
197,add,17.0,n,,O=C(Cn1c(=O)c(=O),O=C(Cn1c(=O)c(=O)n,18,add n at position 17,flow_matching,0.3,2.0,51,230
198,add,18.0,(,,O=C(Cn1c(=O)c(=O)n,O=C(Cn1c(=O)c(=O)n(,19,add ( at position 18,flow_matching,0.3,2.0,51,230
199,add,19.0,C,,O=C(Cn1c(=O)c(=O)n(,O=C(Cn1c(=O)c(=O)n(C,20,add C at position 19,flow_matching,0.3,2.0,51,230
200,add,20.0,c,,O=C(Cn1c(=O)c(=O)n(C,O=C(Cn1c(=O)c(=O)n(Cc,21,add c at position 20,flow_matching,0.3,2.0,51,230
201,add,21.0,2,,O=C(Cn1c(=O)c(=O)n(Cc,O=C(Cn1c(=O)c(=O)n(Cc2,22,add 2 at position 21,flow_matching,0.3,2.0,51,230
202,add,22.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2,O=C(Cn1c(=O)c(=O)n(Cc2c,23,add c at position 22,flow_matching,0.3,2.0,51,230
203,add,23.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2c,O=C(Cn1c(=O)c(=O)n(Cc2cc,24,add c at position 23,flow_matching,0.3,2.0,51,230
204,add,24.0,n,,O=C(Cn1c(=O)c(=O)n(Cc2cc,O=C(Cn1c(=O)c(=O)n(Cc2ccn,25,add n at position 24,flow_matching,0.3,2.0,51,230
205,add,25.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccn,O=C(Cn1c(=O)c(=O)n(Cc2ccnc,26,add c at position 25,flow_matching,0.3,2.0,51,230
206,add,26.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccnc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc,27,add c at position 26,flow_matching,0.3,2.0,51,230
207,add,27.0,2,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2,28,add 2 at position 27,flow_matching,0.3,2.0,51,230
208,add,28.0,),,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2),29,add ) at position 28,flow_matching,0.3,2.0,51,230
209,add,29.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2),O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c,30,add c at position 29,flow_matching,0.3,2.0,51,230
210,add,30.0,2,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2,31,add 2 at position 30,flow_matching,0.3,2.0,51,230
211,add,31.0,n,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2n,32,add n at position 31,flow_matching,0.3,2.0,51,230
212,add,32.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2n,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2nc,33,add c at position 32,flow_matching,0.3,2.0,51,230
213,add,33.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2nc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncc,34,add c at position 33,flow_matching,0.3,2.0,51,230
214,add,34.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2nccc,35,add c at position 34,flow_matching,0.3,2.0,51,230
215,add,35.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2nccc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc,36,add c at position 35,flow_matching,0.3,2.0,51,230
216,add,36.0,2,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc2,37,add 2 at position 36,flow_matching,0.3,2.0,51,230
217,add,37.0,1,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc2,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21,38,add 1 at position 37,flow_matching,0.3,2.0,51,230
218,add,38.0,),,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21),39,add ) at position 38,flow_matching,0.3,2.0,51,230
219,add,39.0,N,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21),O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)N,40,add N at position 39,flow_matching,0.3,2.0,51,230
220,add,40.0,C,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)N,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NC,41,add C at position 40,flow_matching,0.3,2.0,51,230
221,add,41.0,C,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NC,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCC,42,add C at position 41,flow_matching,0.3,2.0,51,230
222,add,42.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCC,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc,43,add c at position 42,flow_matching,0.3,2.0,51,230
223,add,43.0,1,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1,44,add 1 at position 43,flow_matching,0.3,2.0,51,230
224,add,44.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1c,45,add c at position 44,flow_matching,0.3,2.0,51,230
225,add,45.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1c,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1cc,46,add c at position 45,flow_matching,0.3,2.0,51,230
226,add,46.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1cc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1ccc,47,add c at position 46,flow_matching,0.3,2.0,51,230
227,add,47.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1ccc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1cccc,48,add c at position 47,flow_matching,0.3,2.0,51,230
228,add,48.0,c,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1cccc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1ccccc,49,add c at position 48,flow_matching,0.3,2.0,51,230
229,add,49.0,1,,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1ccccc,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1ccccc1,50,add 1 at position 49,flow_matching,0.3,2.0,51,230
230,add,50.0,"
",,O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1ccccc1,"O=C(Cn1c(=O)c(=O)n(Cc2ccncc2)c2ncccc21)NCCc1ccccc1
",51,"add 
 at position 50",flow_matching,0.3,2.0,51,230
