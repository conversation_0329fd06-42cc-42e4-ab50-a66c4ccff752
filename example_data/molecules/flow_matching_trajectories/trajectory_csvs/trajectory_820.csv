step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,52,168
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,52,168
2,add,1.0,(,,C,C(,2,add ( at position 1,flow_matching,0.3,2.0,52,168
3,add,2.0,3,,C(,C(3,3,add 3 at position 2,flow_matching,0.3,2.0,52,168
4,replace,1.0,C,(,C(3,CC3,3,replace ( at position 1 with C,flow_matching,0.3,2.0,52,168
5,remove,0.0,C,,CC3,C3,2,remove <PERSON> from position 0,flow_matching,0.3,2.0,52,168
6,remove,0.0,<PERSON>,,C3,3,1,remove <PERSON> from position 0,flow_matching,0.3,2.0,52,168
7,add,1.0,7,,3,37,2,add 7 at position 1,flow_matching,0.3,2.0,52,168
8,remove,0.0,3,,37,7,1,remove 3 from position 0,flow_matching,0.3,2.0,52,168
9,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,52,168
10,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,52,168
11,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,52,168
12,replace,0.0,C,O,O,C,1,replace O at position 0 with C,flow_matching,0.3,2.0,52,168
13,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,52,168
14,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,52,168
15,replace,0.0,5,],],5,1,replace ] at position 0 with 5,flow_matching,0.3,2.0,52,168
16,remove,0.0,5,,5,,0,remove 5 from position 0,flow_matching,0.3,2.0,52,168
17,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,52,168
18,replace,0.0,C,N,N,C,1,replace N at position 0 with C,flow_matching,0.3,2.0,52,168
19,add,1.0,-,,C,C-,2,add - at position 1,flow_matching,0.3,2.0,52,168
20,remove,0.0,C,,C-,-,1,remove C from position 0,flow_matching,0.3,2.0,52,168
21,add,1.0,C,,-,-C,2,add C at position 1,flow_matching,0.3,2.0,52,168
22,replace,0.0,C,-,-C,CC,2,replace - at position 0 with C,flow_matching,0.3,2.0,52,168
23,add,1.0,\,,CC,C\C,3,add \ at position 1,flow_matching,0.3,2.0,52,168
24,add,3.0,2,,C\C,C\C2,4,add 2 at position 3,flow_matching,0.3,2.0,52,168
25,replace,3.0,3,2,C\C2,C\C3,4,replace 2 at position 3 with 3,flow_matching,0.3,2.0,52,168
26,replace,1.0,C,\,C\C3,CCC3,4,replace \ at position 1 with C,flow_matching,0.3,2.0,52,168
27,replace,2.0,(,C,CCC3,CC(3,4,replace C at position 2 with (,flow_matching,0.3,2.0,52,168
28,replace,3.0,C,3,CC(3,CC(C,4,replace 3 at position 3 with C,flow_matching,0.3,2.0,52,168
29,add,4.0,C,,CC(C,CC(CC,5,add C at position 4,flow_matching,0.3,2.0,52,168
30,replace,4.0,),C,CC(CC,CC(C),5,replace C at position 4 with ),flow_matching,0.3,2.0,52,168
31,add,5.0,c,,CC(C),CC(C)c,6,add c at position 5,flow_matching,0.3,2.0,52,168
32,remove,4.0,),,CC(C)c,CC(Cc,5,remove ) from position 4,flow_matching,0.3,2.0,52,168
33,replace,4.0,r,c,CC(Cc,CC(Cr,5,replace c at position 4 with r,flow_matching,0.3,2.0,52,168
34,add,3.0,B,,CC(Cr,CC(BCr,6,add B at position 3,flow_matching,0.3,2.0,52,168
35,remove,2.0,(,,CC(BCr,CCBCr,5,remove ( from position 2,flow_matching,0.3,2.0,52,168
36,add,3.0,[,,CCBCr,CCB[Cr,6,add [ at position 3,flow_matching,0.3,2.0,52,168
37,replace,2.0,(,B,CCB[Cr,CC([Cr,6,replace B at position 2 with (,flow_matching,0.3,2.0,52,168
38,replace,3.0,o,[,CC([Cr,CC(oCr,6,replace [ at position 3 with o,flow_matching,0.3,2.0,52,168
39,add,1.0,6,,CC(oCr,C6C(oCr,7,add 6 at position 1,flow_matching,0.3,2.0,52,168
40,replace,4.0,N,o,C6C(oCr,C6C(NCr,7,replace o at position 4 with N,flow_matching,0.3,2.0,52,168
41,remove,6.0,r,,C6C(NCr,C6C(NC,6,remove r from position 6,flow_matching,0.3,2.0,52,168
42,replace,1.0,C,6,C6C(NC,CCC(NC,6,replace 6 at position 1 with C,flow_matching,0.3,2.0,52,168
43,replace,4.0,[,N,CCC(NC,CCC([C,6,replace N at position 4 with [,flow_matching,0.3,2.0,52,168
44,replace,2.0,(,C,CCC([C,CC(([C,6,replace C at position 2 with (,flow_matching,0.3,2.0,52,168
45,replace,3.0,C,(,CC(([C,CC(C[C,6,replace ( at position 3 with C,flow_matching,0.3,2.0,52,168
46,remove,1.0,C,,CC(C[C,C(C[C,5,remove C from position 1,flow_matching,0.3,2.0,52,168
47,remove,3.0,[,,C(C[C,C(CC,4,remove [ from position 3,flow_matching,0.3,2.0,52,168
48,add,0.0,\,,C(CC,\C(CC,5,add \ at position 0,flow_matching,0.3,2.0,52,168
49,replace,0.0,C,\,\C(CC,CC(CC,5,replace \ at position 0 with C,flow_matching,0.3,2.0,52,168
50,remove,2.0,(,,CC(CC,CCCC,4,remove ( from position 2,flow_matching,0.3,2.0,52,168
51,replace,1.0,-,C,CCCC,C-CC,4,replace C at position 1 with -,flow_matching,0.3,2.0,52,168
52,remove,2.0,C,,C-CC,C-C,3,remove C from position 2,flow_matching,0.3,2.0,52,168
53,replace,1.0,C,-,C-C,CCC,3,replace - at position 1 with C,flow_matching,0.3,2.0,52,168
54,remove,1.0,C,,CCC,CC,2,remove C from position 1,flow_matching,0.3,2.0,52,168
55,add,0.0,H,,CC,HCC,3,add H at position 0,flow_matching,0.3,2.0,52,168
56,remove,1.0,C,,HCC,HC,2,remove C from position 1,flow_matching,0.3,2.0,52,168
57,replace,1.0,1,C,HC,H1,2,replace C at position 1 with 1,flow_matching,0.3,2.0,52,168
58,replace,1.0,@,1,H1,H@,2,replace 1 at position 1 with @,flow_matching,0.3,2.0,52,168
59,replace,0.0,C,H,H@,C@,2,replace H at position 0 with C,flow_matching,0.3,2.0,52,168
60,remove,1.0,@,,C@,C,1,remove @ from position 1,flow_matching,0.3,2.0,52,168
61,replace,0.0,[,C,C,[,1,replace C at position 0 with [,flow_matching,0.3,2.0,52,168
62,add,0.0,5,,[,5[,2,add 5 at position 0,flow_matching,0.3,2.0,52,168
63,remove,0.0,5,,5[,[,1,remove 5 from position 0,flow_matching,0.3,2.0,52,168
64,add,0.0,O,,[,O[,2,add O at position 0,flow_matching,0.3,2.0,52,168
65,add,1.0,\,,O[,O\[,3,add \ at position 1,flow_matching,0.3,2.0,52,168
66,replace,1.0,7,\,O\[,O7[,3,replace \ at position 1 with 7,flow_matching,0.3,2.0,52,168
67,remove,0.0,O,,O7[,7[,2,remove O from position 0,flow_matching,0.3,2.0,52,168
68,remove,1.0,[,,7[,7,1,remove [ from position 1,flow_matching,0.3,2.0,52,168
69,remove,0.0,7,,7,,0,remove 7 from position 0,flow_matching,0.3,2.0,52,168
70,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,52,168
71,replace,0.0,5,C,C,5,1,replace C at position 0 with 5,flow_matching,0.3,2.0,52,168
72,add,0.0,r,,5,r5,2,add r at position 0,flow_matching,0.3,2.0,52,168
73,replace,0.0,N,r,r5,N5,2,replace r at position 0 with N,flow_matching,0.3,2.0,52,168
74,remove,1.0,5,,N5,N,1,remove 5 from position 1,flow_matching,0.3,2.0,52,168
75,replace,0.0,1,N,N,1,1,replace N at position 0 with 1,flow_matching,0.3,2.0,52,168
76,replace,0.0,C,1,1,C,1,replace 1 at position 0 with C,flow_matching,0.3,2.0,52,168
77,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,52,168
78,add,2.0,/,,CC,CC/,3,add / at position 2,flow_matching,0.3,2.0,52,168
79,replace,2.0,C,/,CC/,CCC,3,replace / at position 2 with C,flow_matching,0.3,2.0,52,168
80,add,1.0,F,,CCC,CFCC,4,add F at position 1,flow_matching,0.3,2.0,52,168
81,remove,2.0,C,,CFCC,CFC,3,remove C from position 2,flow_matching,0.3,2.0,52,168
82,replace,1.0,3,F,CFC,C3C,3,replace F at position 1 with 3,flow_matching,0.3,2.0,52,168
83,replace,2.0,4,C,C3C,C34,3,replace C at position 2 with 4,flow_matching,0.3,2.0,52,168
84,add,3.0,3,,C34,C343,4,add 3 at position 3,flow_matching,0.3,2.0,52,168
85,replace,1.0,6,3,C343,C643,4,replace 3 at position 1 with 6,flow_matching,0.3,2.0,52,168
86,replace,1.0,N,6,C643,CN43,4,replace 6 at position 1 with N,flow_matching,0.3,2.0,52,168
87,replace,0.0,(,C,CN43,(N43,4,replace C at position 0 with (,flow_matching,0.3,2.0,52,168
88,add,1.0,c,,(N43,(cN43,5,add c at position 1,flow_matching,0.3,2.0,52,168
89,replace,3.0,-,4,(cN43,(cN-3,5,replace 4 at position 3 with -,flow_matching,0.3,2.0,52,168
90,remove,0.0,(,,(cN-3,cN-3,4,remove ( from position 0,flow_matching,0.3,2.0,52,168
91,add,4.0,@,,cN-3,cN-3@,5,add @ at position 4,flow_matching,0.3,2.0,52,168
92,replace,4.0,],@,cN-3@,cN-3],5,replace @ at position 4 with ],flow_matching,0.3,2.0,52,168
93,remove,1.0,N,,cN-3],c-3],4,remove N from position 1,flow_matching,0.3,2.0,52,168
94,remove,1.0,-,,c-3],c3],3,remove - from position 1,flow_matching,0.3,2.0,52,168
95,remove,0.0,c,,c3],3],2,remove c from position 0,flow_matching,0.3,2.0,52,168
96,remove,0.0,3,,3],],1,remove 3 from position 0,flow_matching,0.3,2.0,52,168
97,replace,0.0,C,],],C,1,replace ] at position 0 with C,flow_matching,0.3,2.0,52,168
98,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,52,168
99,add,0.0,o,,,o,1,add o at position 0,flow_matching,0.3,2.0,52,168
100,add,0.0,#,,o,#o,2,add # at position 0,flow_matching,0.3,2.0,52,168
101,remove,0.0,#,,#o,o,1,remove # from position 0,flow_matching,0.3,2.0,52,168
102,replace,0.0,6,o,o,6,1,replace o at position 0 with 6,flow_matching,0.3,2.0,52,168
103,add,0.0,#,,6,#6,2,add # at position 0,flow_matching,0.3,2.0,52,168
104,remove,1.0,6,,#6,#,1,remove 6 from position 1,flow_matching,0.3,2.0,52,168
105,replace,0.0,C,#,#,C,1,replace # at position 0 with C,flow_matching,0.3,2.0,52,168
106,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,52,168
107,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,52,168
108,add,3.0,C,,CC(,CC(C,4,add C at position 3,flow_matching,0.3,2.0,52,168
109,add,3.0,1,,CC(C,CC(1C,5,add 1 at position 3,flow_matching,0.3,2.0,52,168
110,replace,3.0,C,1,CC(1C,CC(CC,5,replace 1 at position 3 with C,flow_matching,0.3,2.0,52,168
111,replace,4.0,),C,CC(CC,CC(C),5,replace C at position 4 with ),flow_matching,0.3,2.0,52,168
112,add,5.0,c,,CC(C),CC(C)c,6,add c at position 5,flow_matching,0.3,2.0,52,168
113,remove,0.0,C,,CC(C)c,C(C)c,5,remove C from position 0,flow_matching,0.3,2.0,52,168
114,replace,2.0,-,C,C(C)c,C(-)c,5,replace C at position 2 with -,flow_matching,0.3,2.0,52,168
115,replace,2.0,(,-,C(-)c,C(()c,5,replace - at position 2 with (,flow_matching,0.3,2.0,52,168
116,add,2.0,[,,C(()c,C([()c,6,add [ at position 2,flow_matching,0.3,2.0,52,168
117,replace,1.0,C,(,C([()c,CC[()c,6,replace ( at position 1 with C,flow_matching,0.3,2.0,52,168
118,add,2.0,I,,CC[()c,CCI[()c,7,add I at position 2,flow_matching,0.3,2.0,52,168
119,replace,2.0,(,I,CCI[()c,CC([()c,7,replace I at position 2 with (,flow_matching,0.3,2.0,52,168
120,replace,3.0,C,[,CC([()c,CC(C()c,7,replace [ at position 3 with C,flow_matching,0.3,2.0,52,168
121,replace,4.0,),(,CC(C()c,CC(C))c,7,replace ( at position 4 with ),flow_matching,0.3,2.0,52,168
122,replace,5.0,c,),CC(C))c,CC(C)cc,7,replace ) at position 5 with c,flow_matching,0.3,2.0,52,168
123,replace,6.0,1,c,CC(C)cc,CC(C)c1,7,replace c at position 6 with 1,flow_matching,0.3,2.0,52,168
124,add,7.0,c,,CC(C)c1,CC(C)c1c,8,add c at position 7,flow_matching,0.3,2.0,52,168
125,add,8.0,c,,CC(C)c1c,CC(C)c1cc,9,add c at position 8,flow_matching,0.3,2.0,52,168
126,add,9.0,c,,CC(C)c1cc,CC(C)c1ccc,10,add c at position 9,flow_matching,0.3,2.0,52,168
127,add,10.0,c,,CC(C)c1ccc,CC(C)c1cccc,11,add c at position 10,flow_matching,0.3,2.0,52,168
128,add,11.0,c,,CC(C)c1cccc,CC(C)c1ccccc,12,add c at position 11,flow_matching,0.3,2.0,52,168
129,add,12.0,1,,CC(C)c1ccccc,CC(C)c1ccccc1,13,add 1 at position 12,flow_matching,0.3,2.0,52,168
130,add,13.0,N,,CC(C)c1ccccc1,CC(C)c1ccccc1N,14,add N at position 13,flow_matching,0.3,2.0,52,168
131,add,14.0,C,,CC(C)c1ccccc1N,CC(C)c1ccccc1NC,15,add C at position 14,flow_matching,0.3,2.0,52,168
132,add,15.0,(,,CC(C)c1ccccc1NC,CC(C)c1ccccc1NC(,16,add ( at position 15,flow_matching,0.3,2.0,52,168
133,add,16.0,=,,CC(C)c1ccccc1NC(,CC(C)c1ccccc1NC(=,17,add = at position 16,flow_matching,0.3,2.0,52,168
134,add,17.0,O,,CC(C)c1ccccc1NC(=,CC(C)c1ccccc1NC(=O,18,add O at position 17,flow_matching,0.3,2.0,52,168
135,add,18.0,),,CC(C)c1ccccc1NC(=O,CC(C)c1ccccc1NC(=O),19,add ) at position 18,flow_matching,0.3,2.0,52,168
136,add,19.0,C,,CC(C)c1ccccc1NC(=O),CC(C)c1ccccc1NC(=O)C,20,add C at position 19,flow_matching,0.3,2.0,52,168
137,add,20.0,[,,CC(C)c1ccccc1NC(=O)C,CC(C)c1ccccc1NC(=O)C[,21,add [ at position 20,flow_matching,0.3,2.0,52,168
138,add,21.0,N,,CC(C)c1ccccc1NC(=O)C[,CC(C)c1ccccc1NC(=O)C[N,22,add N at position 21,flow_matching,0.3,2.0,52,168
139,add,22.0,H,,CC(C)c1ccccc1NC(=O)C[N,CC(C)c1ccccc1NC(=O)C[NH,23,add H at position 22,flow_matching,0.3,2.0,52,168
140,add,23.0,+,,CC(C)c1ccccc1NC(=O)C[NH,CC(C)c1ccccc1NC(=O)C[NH+,24,add + at position 23,flow_matching,0.3,2.0,52,168
141,add,24.0,],,CC(C)c1ccccc1NC(=O)C[NH+,CC(C)c1ccccc1NC(=O)C[NH+],25,add ] at position 24,flow_matching,0.3,2.0,52,168
142,add,25.0,(,,CC(C)c1ccccc1NC(=O)C[NH+],CC(C)c1ccccc1NC(=O)C[NH+](,26,add ( at position 25,flow_matching,0.3,2.0,52,168
143,add,26.0,C,,CC(C)c1ccccc1NC(=O)C[NH+](,CC(C)c1ccccc1NC(=O)C[NH+](C,27,add C at position 26,flow_matching,0.3,2.0,52,168
144,add,27.0,(,,CC(C)c1ccccc1NC(=O)C[NH+](C,CC(C)c1ccccc1NC(=O)C[NH+](C(,28,add ( at position 27,flow_matching,0.3,2.0,52,168
145,add,28.0,C,,CC(C)c1ccccc1NC(=O)C[NH+](C(,CC(C)c1ccccc1NC(=O)C[NH+](C(C,29,add C at position 28,flow_matching,0.3,2.0,52,168
146,add,29.0,),,CC(C)c1ccccc1NC(=O)C[NH+](C(C,CC(C)c1ccccc1NC(=O)C[NH+](C(C),30,add ) at position 29,flow_matching,0.3,2.0,52,168
147,add,30.0,C,,CC(C)c1ccccc1NC(=O)C[NH+](C(C),CC(C)c1ccccc1NC(=O)C[NH+](C(C)C,31,add C at position 30,flow_matching,0.3,2.0,52,168
148,add,31.0,),,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C),32,add ) at position 31,flow_matching,0.3,2.0,52,168
149,add,32.0,[,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C),CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[,33,add [ at position 32,flow_matching,0.3,2.0,52,168
150,add,33.0,C,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C,34,add C at position 33,flow_matching,0.3,2.0,52,168
151,add,34.0,@,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@,35,add @ at position 34,flow_matching,0.3,2.0,52,168
152,add,35.0,@,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@,36,add @ at position 35,flow_matching,0.3,2.0,52,168
153,add,36.0,H,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H,37,add H at position 36,flow_matching,0.3,2.0,52,168
154,add,37.0,],,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H],38,add ] at position 37,flow_matching,0.3,2.0,52,168
155,add,38.0,1,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H],CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1,39,add 1 at position 38,flow_matching,0.3,2.0,52,168
156,add,39.0,C,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1C,40,add C at position 39,flow_matching,0.3,2.0,52,168
157,add,40.0,C,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1C,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CC,41,add C at position 40,flow_matching,0.3,2.0,52,168
158,add,41.0,C,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CC,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCC,42,add C at position 41,flow_matching,0.3,2.0,52,168
159,add,42.0,C,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCC,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC,43,add C at position 42,flow_matching,0.3,2.0,52,168
160,add,43.0,[,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[,44,add [ at position 43,flow_matching,0.3,2.0,52,168
161,add,44.0,C,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C,45,add C at position 44,flow_matching,0.3,2.0,52,168
162,add,45.0,@,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@,46,add @ at position 45,flow_matching,0.3,2.0,52,168
163,add,46.0,@,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@,47,add @ at position 46,flow_matching,0.3,2.0,52,168
164,add,47.0,H,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@H,48,add H at position 47,flow_matching,0.3,2.0,52,168
165,add,48.0,],,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@H,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@H],49,add ] at position 48,flow_matching,0.3,2.0,52,168
166,add,49.0,1,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@H],CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@H]1,50,add 1 at position 49,flow_matching,0.3,2.0,52,168
167,add,50.0,O,,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@H]1,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@H]1O,51,add O at position 50,flow_matching,0.3,2.0,52,168
168,add,51.0,"
",,CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@H]1O,"CC(C)c1ccccc1NC(=O)C[NH+](C(C)C)[C@@H]1CCCC[C@@H]1O
",52,"add 
 at position 51",flow_matching,0.3,2.0,52,168
