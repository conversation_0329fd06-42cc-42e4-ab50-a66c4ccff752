step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,46,211
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,46,211
2,add,0.0,I,,C,IC,2,add I at position 0,flow_matching,0.3,2.0,46,211
3,remove,1.0,C,,IC,I,1,remove C from position 1,flow_matching,0.3,2.0,46,211
4,replace,0.0,B,I,I,B,1,replace I at position 0 with B,flow_matching,0.3,2.0,46,211
5,remove,0.0,B,,B,,0,remove B from position 0,flow_matching,0.3,2.0,46,211
6,add,0.0,S,,,S,1,add S at position 0,flow_matching,0.3,2.0,46,211
7,add,0.0,(,,S,(S,2,add ( at position 0,flow_matching,0.3,2.0,46,211
8,replace,0.0,C,(,(S,CS,2,replace ( at position 0 with C,flow_matching,0.3,2.0,46,211
9,replace,1.0,c,S,CS,Cc,2,replace S at position 1 with c,flow_matching,0.3,2.0,46,211
10,add,1.0,s,,Cc,Csc,3,add s at position 1,flow_matching,0.3,2.0,46,211
11,remove,0.0,C,,Csc,sc,2,remove C from position 0,flow_matching,0.3,2.0,46,211
12,remove,1.0,c,,sc,s,1,remove c from position 1,flow_matching,0.3,2.0,46,211
13,replace,0.0,C,s,s,C,1,replace s at position 0 with C,flow_matching,0.3,2.0,46,211
14,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,46,211
15,add,0.0,l,,,l,1,add l at position 0,flow_matching,0.3,2.0,46,211
16,replace,0.0,C,l,l,C,1,replace l at position 0 with C,flow_matching,0.3,2.0,46,211
17,add,1.0,I,,C,CI,2,add I at position 1,flow_matching,0.3,2.0,46,211
18,add,2.0,O,,CI,CIO,3,add O at position 2,flow_matching,0.3,2.0,46,211
19,replace,0.0,F,C,CIO,FIO,3,replace C at position 0 with F,flow_matching,0.3,2.0,46,211
20,add,1.0,2,,FIO,F2IO,4,add 2 at position 1,flow_matching,0.3,2.0,46,211
21,replace,2.0,l,I,F2IO,F2lO,4,replace I at position 2 with l,flow_matching,0.3,2.0,46,211
22,remove,1.0,2,,F2lO,FlO,3,remove 2 from position 1,flow_matching,0.3,2.0,46,211
23,remove,1.0,l,,FlO,FO,2,remove l from position 1,flow_matching,0.3,2.0,46,211
24,replace,1.0,1,O,FO,F1,2,replace O at position 1 with 1,flow_matching,0.3,2.0,46,211
25,replace,0.0,C,F,F1,C1,2,replace F at position 0 with C,flow_matching,0.3,2.0,46,211
26,add,0.0,o,,C1,oC1,3,add o at position 0,flow_matching,0.3,2.0,46,211
27,replace,0.0,@,o,oC1,@C1,3,replace o at position 0 with @,flow_matching,0.3,2.0,46,211
28,replace,2.0,6,1,@C1,@C6,3,replace 1 at position 2 with 6,flow_matching,0.3,2.0,46,211
29,remove,0.0,@,,@C6,C6,2,remove @ from position 0,flow_matching,0.3,2.0,46,211
30,replace,1.0,r,6,C6,Cr,2,replace 6 at position 1 with r,flow_matching,0.3,2.0,46,211
31,add,0.0,O,,Cr,OCr,3,add O at position 0,flow_matching,0.3,2.0,46,211
32,replace,0.0,C,O,OCr,CCr,3,replace O at position 0 with C,flow_matching,0.3,2.0,46,211
33,replace,1.0,],C,CCr,C]r,3,replace C at position 1 with ],flow_matching,0.3,2.0,46,211
34,remove,2.0,r,,C]r,C],2,remove r from position 2,flow_matching,0.3,2.0,46,211
35,remove,1.0,],,C],C,1,remove ] from position 1,flow_matching,0.3,2.0,46,211
36,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,46,211
37,replace,1.0,O,c,Cc,CO,2,replace c at position 1 with O,flow_matching,0.3,2.0,46,211
38,add,0.0,2,,CO,2CO,3,add 2 at position 0,flow_matching,0.3,2.0,46,211
39,add,2.0,=,,2CO,2C=O,4,add = at position 2,flow_matching,0.3,2.0,46,211
40,replace,1.0,-,C,2C=O,2-=O,4,replace C at position 1 with -,flow_matching,0.3,2.0,46,211
41,add,0.0,c,,2-=O,c2-=O,5,add c at position 0,flow_matching,0.3,2.0,46,211
42,replace,0.0,C,c,c2-=O,C2-=O,5,replace c at position 0 with C,flow_matching,0.3,2.0,46,211
43,add,0.0,[,,C2-=O,[C2-=O,6,add [ at position 0,flow_matching,0.3,2.0,46,211
44,remove,4.0,=,,[C2-=O,[C2-O,5,remove = from position 4,flow_matching,0.3,2.0,46,211
45,replace,2.0,@,2,[C2-O,[C@-O,5,replace 2 at position 2 with @,flow_matching,0.3,2.0,46,211
46,replace,1.0,r,C,[C@-O,[r@-O,5,replace C at position 1 with r,flow_matching,0.3,2.0,46,211
47,remove,3.0,-,,[r@-O,[r@O,4,remove - from position 3,flow_matching,0.3,2.0,46,211
48,replace,0.0,C,[,[r@O,Cr@O,4,replace [ at position 0 with C,flow_matching,0.3,2.0,46,211
49,replace,1.0,/,r,Cr@O,C/@O,4,replace r at position 1 with /,flow_matching,0.3,2.0,46,211
50,replace,1.0,c,/,C/@O,Cc@O,4,replace / at position 1 with c,flow_matching,0.3,2.0,46,211
51,remove,1.0,c,,Cc@O,C@O,3,remove c from position 1,flow_matching,0.3,2.0,46,211
52,replace,1.0,c,@,C@O,CcO,3,replace @ at position 1 with c,flow_matching,0.3,2.0,46,211
53,remove,2.0,O,,CcO,Cc,2,remove O from position 2,flow_matching,0.3,2.0,46,211
54,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,46,211
55,add,1.0,[,,Cc1,C[c1,4,add [ at position 1,flow_matching,0.3,2.0,46,211
56,add,4.0,2,,C[c1,C[c12,5,add 2 at position 4,flow_matching,0.3,2.0,46,211
57,replace,3.0,I,1,C[c12,C[cI2,5,replace 1 at position 3 with I,flow_matching,0.3,2.0,46,211
58,replace,4.0,I,2,C[cI2,C[cII,5,replace 2 at position 4 with I,flow_matching,0.3,2.0,46,211
59,replace,1.0,c,[,C[cII,CccII,5,replace [ at position 1 with c,flow_matching,0.3,2.0,46,211
60,remove,2.0,c,,CccII,CcII,4,remove c from position 2,flow_matching,0.3,2.0,46,211
61,add,4.0,S,,CcII,CcIIS,5,add S at position 4,flow_matching,0.3,2.0,46,211
62,replace,2.0,1,I,CcIIS,Cc1IS,5,replace I at position 2 with 1,flow_matching,0.3,2.0,46,211
63,replace,3.0,c,I,Cc1IS,Cc1cS,5,replace I at position 3 with c,flow_matching,0.3,2.0,46,211
64,replace,3.0,+,c,Cc1cS,Cc1+S,5,replace c at position 3 with +,flow_matching,0.3,2.0,46,211
65,add,0.0,(,,Cc1+S,(Cc1+S,6,add ( at position 0,flow_matching,0.3,2.0,46,211
66,replace,0.0,C,(,(Cc1+S,CCc1+S,6,replace ( at position 0 with C,flow_matching,0.3,2.0,46,211
67,replace,1.0,c,C,CCc1+S,Ccc1+S,6,replace C at position 1 with c,flow_matching,0.3,2.0,46,211
68,add,2.0,N,,Ccc1+S,CcNc1+S,7,add N at position 2,flow_matching,0.3,2.0,46,211
69,add,3.0,7,,CcNc1+S,CcN7c1+S,8,add 7 at position 3,flow_matching,0.3,2.0,46,211
70,add,5.0,I,,CcN7c1+S,CcN7cI1+S,9,add I at position 5,flow_matching,0.3,2.0,46,211
71,remove,4.0,c,,CcN7cI1+S,CcN7I1+S,8,remove c from position 4,flow_matching,0.3,2.0,46,211
72,remove,6.0,+,,CcN7I1+S,CcN7I1S,7,remove + from position 6,flow_matching,0.3,2.0,46,211
73,replace,2.0,4,N,CcN7I1S,Cc47I1S,7,replace N at position 2 with 4,flow_matching,0.3,2.0,46,211
74,replace,2.0,1,4,Cc47I1S,Cc17I1S,7,replace 4 at position 2 with 1,flow_matching,0.3,2.0,46,211
75,replace,5.0,),1,Cc17I1S,Cc17I)S,7,replace 1 at position 5 with ),flow_matching,0.3,2.0,46,211
76,replace,3.0,c,7,Cc17I)S,Cc1cI)S,7,replace 7 at position 3 with c,flow_matching,0.3,2.0,46,211
77,add,0.0,O,,Cc1cI)S,OCc1cI)S,8,add O at position 0,flow_matching,0.3,2.0,46,211
78,remove,6.0,),,OCc1cI)S,OCc1cIS,7,remove ) from position 6,flow_matching,0.3,2.0,46,211
79,replace,5.0,4,I,OCc1cIS,OCc1c4S,7,replace I at position 5 with 4,flow_matching,0.3,2.0,46,211
80,add,6.0,l,,OCc1c4S,OCc1c4lS,8,add l at position 6,flow_matching,0.3,2.0,46,211
81,add,4.0,1,,OCc1c4lS,OCc11c4lS,9,add 1 at position 4,flow_matching,0.3,2.0,46,211
82,add,6.0,l,,OCc11c4lS,OCc11cl4lS,10,add l at position 6,flow_matching,0.3,2.0,46,211
83,add,8.0,C,,OCc11cl4lS,OCc11cl4ClS,11,add C at position 8,flow_matching,0.3,2.0,46,211
84,remove,7.0,4,,OCc11cl4ClS,OCc11clClS,10,remove 4 from position 7,flow_matching,0.3,2.0,46,211
85,replace,0.0,C,O,OCc11clClS,CCc11clClS,10,replace O at position 0 with C,flow_matching,0.3,2.0,46,211
86,remove,9.0,S,,CCc11clClS,CCc11clCl,9,remove S from position 9,flow_matching,0.3,2.0,46,211
87,remove,0.0,C,,CCc11clCl,Cc11clCl,8,remove C from position 0,flow_matching,0.3,2.0,46,211
88,add,7.0,I,,Cc11clCl,Cc11clCIl,9,add I at position 7,flow_matching,0.3,2.0,46,211
89,remove,7.0,I,,Cc11clCIl,Cc11clCl,8,remove I from position 7,flow_matching,0.3,2.0,46,211
90,remove,2.0,1,,Cc11clCl,Cc1clCl,7,remove 1 from position 2,flow_matching,0.3,2.0,46,211
91,add,4.0,s,,Cc1clCl,Cc1cslCl,8,add s at position 4,flow_matching,0.3,2.0,46,211
92,replace,1.0,+,c,Cc1cslCl,C+1cslCl,8,replace c at position 1 with +,flow_matching,0.3,2.0,46,211
93,replace,3.0,(,c,C+1cslCl,C+1(slCl,8,replace c at position 3 with (,flow_matching,0.3,2.0,46,211
94,add,5.0,n,,C+1(slCl,C+1(snlCl,9,add n at position 5,flow_matching,0.3,2.0,46,211
95,replace,1.0,c,+,C+1(snlCl,Cc1(snlCl,9,replace + at position 1 with c,flow_matching,0.3,2.0,46,211
96,add,9.0,],,Cc1(snlCl,Cc1(snlCl],10,add ] at position 9,flow_matching,0.3,2.0,46,211
97,add,8.0,#,,Cc1(snlCl],Cc1(snlC#l],11,add # at position 8,flow_matching,0.3,2.0,46,211
98,add,7.0,6,,Cc1(snlC#l],Cc1(snl6C#l],12,add 6 at position 7,flow_matching,0.3,2.0,46,211
99,add,6.0,B,,Cc1(snl6C#l],Cc1(snBl6C#l],13,add B at position 6,flow_matching,0.3,2.0,46,211
100,add,10.0,N,,Cc1(snBl6C#l],Cc1(snBl6CN#l],14,add N at position 10,flow_matching,0.3,2.0,46,211
101,replace,3.0,c,(,Cc1(snBl6CN#l],Cc1csnBl6CN#l],14,replace ( at position 3 with c,flow_matching,0.3,2.0,46,211
102,replace,3.0,5,c,Cc1csnBl6CN#l],Cc15snBl6CN#l],14,replace c at position 3 with 5,flow_matching,0.3,2.0,46,211
103,add,6.0,4,,Cc15snBl6CN#l],Cc15sn4Bl6CN#l],15,add 4 at position 6,flow_matching,0.3,2.0,46,211
104,replace,10.0,2,C,Cc15sn4Bl6CN#l],Cc15sn4Bl62N#l],15,replace C at position 10 with 2,flow_matching,0.3,2.0,46,211
105,replace,3.0,c,5,Cc15sn4Bl62N#l],Cc1csn4Bl62N#l],15,replace 5 at position 3 with c,flow_matching,0.3,2.0,46,211
106,replace,4.0,c,s,Cc1csn4Bl62N#l],Cc1ccn4Bl62N#l],15,replace s at position 4 with c,flow_matching,0.3,2.0,46,211
107,add,7.0,1,,Cc1ccn4Bl62N#l],Cc1ccn41Bl62N#l],16,add 1 at position 7,flow_matching,0.3,2.0,46,211
108,remove,9.0,l,,Cc1ccn41Bl62N#l],Cc1ccn41B62N#l],15,remove l from position 9,flow_matching,0.3,2.0,46,211
109,replace,0.0,N,C,Cc1ccn41B62N#l],Nc1ccn41B62N#l],15,replace C at position 0 with N,flow_matching,0.3,2.0,46,211
110,add,10.0,6,,Nc1ccn41B62N#l],Nc1ccn41B662N#l],16,add 6 at position 10,flow_matching,0.3,2.0,46,211
111,replace,0.0,C,N,Nc1ccn41B662N#l],Cc1ccn41B662N#l],16,replace N at position 0 with C,flow_matching,0.3,2.0,46,211
112,remove,1.0,c,,Cc1ccn41B662N#l],C1ccn41B662N#l],15,remove c from position 1,flow_matching,0.3,2.0,46,211
113,replace,5.0,),4,C1ccn41B662N#l],C1ccn)1B662N#l],15,replace 4 at position 5 with ),flow_matching,0.3,2.0,46,211
114,add,8.0,H,,C1ccn)1B662N#l],C1ccn)1BH662N#l],16,add H at position 8,flow_matching,0.3,2.0,46,211
115,add,12.0,r,,C1ccn)1BH662N#l],C1ccn)1BH662rN#l],17,add r at position 12,flow_matching,0.3,2.0,46,211
116,remove,0.0,C,,C1ccn)1BH662rN#l],1ccn)1BH662rN#l],16,remove C from position 0,flow_matching,0.3,2.0,46,211
117,add,1.0,I,,1ccn)1BH662rN#l],1Iccn)1BH662rN#l],17,add I at position 1,flow_matching,0.3,2.0,46,211
118,add,7.0,s,,1Iccn)1BH662rN#l],1Iccn)1sBH662rN#l],18,add s at position 7,flow_matching,0.3,2.0,46,211
119,replace,12.0,c,2,1Iccn)1sBH662rN#l],1Iccn)1sBH66crN#l],18,replace 2 at position 12 with c,flow_matching,0.3,2.0,46,211
120,replace,0.0,C,1,1Iccn)1sBH66crN#l],CIccn)1sBH66crN#l],18,replace 1 at position 0 with C,flow_matching,0.3,2.0,46,211
121,replace,1.0,c,I,CIccn)1sBH66crN#l],Ccccn)1sBH66crN#l],18,replace I at position 1 with c,flow_matching,0.3,2.0,46,211
122,remove,11.0,6,,Ccccn)1sBH66crN#l],Ccccn)1sBH6crN#l],17,remove 6 from position 11,flow_matching,0.3,2.0,46,211
123,replace,7.0,),s,Ccccn)1sBH6crN#l],Ccccn)1)BH6crN#l],17,replace s at position 7 with ),flow_matching,0.3,2.0,46,211
124,replace,12.0,/,r,Ccccn)1)BH6crN#l],Ccccn)1)BH6c/N#l],17,replace r at position 12 with /,flow_matching,0.3,2.0,46,211
125,add,7.0,C,,Ccccn)1)BH6c/N#l],Ccccn)1C)BH6c/N#l],18,add C at position 7,flow_matching,0.3,2.0,46,211
126,replace,7.0,I,C,Ccccn)1C)BH6c/N#l],Ccccn)1I)BH6c/N#l],18,replace C at position 7 with I,flow_matching,0.3,2.0,46,211
127,replace,2.0,1,c,Ccccn)1I)BH6c/N#l],Cc1cn)1I)BH6c/N#l],18,replace c at position 2 with 1,flow_matching,0.3,2.0,46,211
128,replace,4.0,c,n,Cc1cn)1I)BH6c/N#l],Cc1cc)1I)BH6c/N#l],18,replace n at position 4 with c,flow_matching,0.3,2.0,46,211
129,replace,5.0,s,),Cc1cc)1I)BH6c/N#l],Cc1ccs1I)BH6c/N#l],18,replace ) at position 5 with s,flow_matching,0.3,2.0,46,211
130,replace,6.0,c,1,Cc1ccs1I)BH6c/N#l],Cc1ccscI)BH6c/N#l],18,replace 1 at position 6 with c,flow_matching,0.3,2.0,46,211
131,add,0.0,-,,Cc1ccscI)BH6c/N#l],-Cc1ccscI)BH6c/N#l],19,add - at position 0,flow_matching,0.3,2.0,46,211
132,remove,16.0,#,,-Cc1ccscI)BH6c/N#l],-Cc1ccscI)BH6c/Nl],18,remove # from position 16,flow_matching,0.3,2.0,46,211
133,add,6.0,\,,-Cc1ccscI)BH6c/Nl],-Cc1cc\scI)BH6c/Nl],19,add \ at position 6,flow_matching,0.3,2.0,46,211
134,replace,17.0,S,l,-Cc1cc\scI)BH6c/Nl],-Cc1cc\scI)BH6c/NS],19,replace l at position 17 with S,flow_matching,0.3,2.0,46,211
135,replace,0.0,C,-,-Cc1cc\scI)BH6c/NS],CCc1cc\scI)BH6c/NS],19,replace - at position 0 with C,flow_matching,0.3,2.0,46,211
136,replace,15.0,],/,CCc1cc\scI)BH6c/NS],CCc1cc\scI)BH6c]NS],19,replace / at position 15 with ],flow_matching,0.3,2.0,46,211
137,remove,18.0,],,CCc1cc\scI)BH6c]NS],CCc1cc\scI)BH6c]NS,18,remove ] from position 18,flow_matching,0.3,2.0,46,211
138,replace,1.0,c,C,CCc1cc\scI)BH6c]NS,Ccc1cc\scI)BH6c]NS,18,replace C at position 1 with c,flow_matching,0.3,2.0,46,211
139,replace,2.0,1,c,Ccc1cc\scI)BH6c]NS,Cc11cc\scI)BH6c]NS,18,replace c at position 2 with 1,flow_matching,0.3,2.0,46,211
140,add,16.0,c,,Cc11cc\scI)BH6c]NS,Cc11cc\scI)BH6c]cNS,19,add c at position 16,flow_matching,0.3,2.0,46,211
141,replace,3.0,c,1,Cc11cc\scI)BH6c]cNS,Cc1ccc\scI)BH6c]cNS,19,replace 1 at position 3 with c,flow_matching,0.3,2.0,46,211
142,remove,18.0,S,,Cc1ccc\scI)BH6c]cNS,Cc1ccc\scI)BH6c]cN,18,remove S from position 18,flow_matching,0.3,2.0,46,211
143,remove,1.0,c,,Cc1ccc\scI)BH6c]cN,C1ccc\scI)BH6c]cN,17,remove c from position 1,flow_matching,0.3,2.0,46,211
144,replace,1.0,c,1,C1ccc\scI)BH6c]cN,Ccccc\scI)BH6c]cN,17,replace 1 at position 1 with c,flow_matching,0.3,2.0,46,211
145,add,9.0,@,,Ccccc\scI)BH6c]cN,Ccccc\scI@)BH6c]cN,18,add @ at position 9,flow_matching,0.3,2.0,46,211
146,remove,7.0,c,,Ccccc\scI@)BH6c]cN,Ccccc\sI@)BH6c]cN,17,remove c from position 7,flow_matching,0.3,2.0,46,211
147,remove,0.0,C,,Ccccc\sI@)BH6c]cN,cccc\sI@)BH6c]cN,16,remove C from position 0,flow_matching,0.3,2.0,46,211
148,remove,0.0,c,,cccc\sI@)BH6c]cN,ccc\sI@)BH6c]cN,15,remove c from position 0,flow_matching,0.3,2.0,46,211
149,replace,0.0,C,c,ccc\sI@)BH6c]cN,Ccc\sI@)BH6c]cN,15,replace c at position 0 with C,flow_matching,0.3,2.0,46,211
150,add,4.0,6,,Ccc\sI@)BH6c]cN,Ccc\6sI@)BH6c]cN,16,add 6 at position 4,flow_matching,0.3,2.0,46,211
151,replace,2.0,1,c,Ccc\6sI@)BH6c]cN,Cc1\6sI@)BH6c]cN,16,replace c at position 2 with 1,flow_matching,0.3,2.0,46,211
152,remove,4.0,6,,Cc1\6sI@)BH6c]cN,Cc1\sI@)BH6c]cN,15,remove 6 from position 4,flow_matching,0.3,2.0,46,211
153,replace,3.0,c,\,Cc1\sI@)BH6c]cN,Cc1csI@)BH6c]cN,15,replace \ at position 3 with c,flow_matching,0.3,2.0,46,211
154,remove,3.0,c,,Cc1csI@)BH6c]cN,Cc1sI@)BH6c]cN,14,remove c from position 3,flow_matching,0.3,2.0,46,211
155,replace,7.0,/,B,Cc1sI@)BH6c]cN,Cc1sI@)/H6c]cN,14,replace B at position 7 with /,flow_matching,0.3,2.0,46,211
156,replace,3.0,c,s,Cc1sI@)/H6c]cN,Cc1cI@)/H6c]cN,14,replace s at position 3 with c,flow_matching,0.3,2.0,46,211
157,replace,4.0,c,I,Cc1cI@)/H6c]cN,Cc1cc@)/H6c]cN,14,replace I at position 4 with c,flow_matching,0.3,2.0,46,211
158,remove,1.0,c,,Cc1cc@)/H6c]cN,C1cc@)/H6c]cN,13,remove c from position 1,flow_matching,0.3,2.0,46,211
159,remove,11.0,c,,C1cc@)/H6c]cN,C1cc@)/H6c]N,12,remove c from position 11,flow_matching,0.3,2.0,46,211
160,replace,2.0,-,c,C1cc@)/H6c]N,C1-c@)/H6c]N,12,replace c at position 2 with -,flow_matching,0.3,2.0,46,211
161,remove,8.0,6,,C1-c@)/H6c]N,C1-c@)/Hc]N,11,remove 6 from position 8,flow_matching,0.3,2.0,46,211
162,add,11.0,I,,C1-c@)/Hc]N,C1-c@)/Hc]NI,12,add I at position 11,flow_matching,0.3,2.0,46,211
163,replace,1.0,c,1,C1-c@)/Hc]NI,Cc-c@)/Hc]NI,12,replace 1 at position 1 with c,flow_matching,0.3,2.0,46,211
164,remove,4.0,@,,Cc-c@)/Hc]NI,Cc-c)/Hc]NI,11,remove @ from position 4,flow_matching,0.3,2.0,46,211
165,remove,1.0,c,,Cc-c)/Hc]NI,C-c)/Hc]NI,10,remove c from position 1,flow_matching,0.3,2.0,46,211
166,add,7.0,B,,C-c)/Hc]NI,C-c)/HcB]NI,11,add B at position 7,flow_matching,0.3,2.0,46,211
167,replace,1.0,B,-,C-c)/HcB]NI,CBc)/HcB]NI,11,replace - at position 1 with B,flow_matching,0.3,2.0,46,211
168,add,8.0,5,,CBc)/HcB]NI,CBc)/HcB5]NI,12,add 5 at position 8,flow_matching,0.3,2.0,46,211
169,replace,1.0,c,B,CBc)/HcB5]NI,Ccc)/HcB5]NI,12,replace B at position 1 with c,flow_matching,0.3,2.0,46,211
170,replace,2.0,1,c,Ccc)/HcB5]NI,Cc1)/HcB5]NI,12,replace c at position 2 with 1,flow_matching,0.3,2.0,46,211
171,replace,3.0,c,),Cc1)/HcB5]NI,Cc1c/HcB5]NI,12,replace ) at position 3 with c,flow_matching,0.3,2.0,46,211
172,replace,4.0,c,/,Cc1c/HcB5]NI,Cc1ccHcB5]NI,12,replace / at position 4 with c,flow_matching,0.3,2.0,46,211
173,replace,5.0,s,H,Cc1ccHcB5]NI,Cc1ccscB5]NI,12,replace H at position 5 with s,flow_matching,0.3,2.0,46,211
174,replace,7.0,1,B,Cc1ccscB5]NI,Cc1ccsc15]NI,12,replace B at position 7 with 1,flow_matching,0.3,2.0,46,211
175,replace,8.0,C,5,Cc1ccsc15]NI,Cc1ccsc1C]NI,12,replace 5 at position 8 with C,flow_matching,0.3,2.0,46,211
176,replace,9.0,[,],Cc1ccsc1C]NI,Cc1ccsc1C[NI,12,replace ] at position 9 with [,flow_matching,0.3,2.0,46,211
177,replace,11.0,H,I,Cc1ccsc1C[NI,Cc1ccsc1C[NH,12,replace I at position 11 with H,flow_matching,0.3,2.0,46,211
178,add,12.0,+,,Cc1ccsc1C[NH,Cc1ccsc1C[NH+,13,add + at position 12,flow_matching,0.3,2.0,46,211
179,add,13.0,],,Cc1ccsc1C[NH+,Cc1ccsc1C[NH+],14,add ] at position 13,flow_matching,0.3,2.0,46,211
180,add,14.0,(,,Cc1ccsc1C[NH+],Cc1ccsc1C[NH+](,15,add ( at position 14,flow_matching,0.3,2.0,46,211
181,add,15.0,C,,Cc1ccsc1C[NH+](,Cc1ccsc1C[NH+](C,16,add C at position 15,flow_matching,0.3,2.0,46,211
182,add,16.0,c,,Cc1ccsc1C[NH+](C,Cc1ccsc1C[NH+](Cc,17,add c at position 16,flow_matching,0.3,2.0,46,211
183,add,17.0,1,,Cc1ccsc1C[NH+](Cc,Cc1ccsc1C[NH+](Cc1,18,add 1 at position 17,flow_matching,0.3,2.0,46,211
184,add,18.0,n,,Cc1ccsc1C[NH+](Cc1,Cc1ccsc1C[NH+](Cc1n,19,add n at position 18,flow_matching,0.3,2.0,46,211
185,add,19.0,c,,Cc1ccsc1C[NH+](Cc1n,Cc1ccsc1C[NH+](Cc1nc,20,add c at position 19,flow_matching,0.3,2.0,46,211
186,add,20.0,2,,Cc1ccsc1C[NH+](Cc1nc,Cc1ccsc1C[NH+](Cc1nc2,21,add 2 at position 20,flow_matching,0.3,2.0,46,211
187,add,21.0,c,,Cc1ccsc1C[NH+](Cc1nc2,Cc1ccsc1C[NH+](Cc1nc2c,22,add c at position 21,flow_matching,0.3,2.0,46,211
188,add,22.0,c,,Cc1ccsc1C[NH+](Cc1nc2c,Cc1ccsc1C[NH+](Cc1nc2cc,23,add c at position 22,flow_matching,0.3,2.0,46,211
189,add,23.0,c,,Cc1ccsc1C[NH+](Cc1nc2cc,Cc1ccsc1C[NH+](Cc1nc2ccc,24,add c at position 23,flow_matching,0.3,2.0,46,211
190,add,24.0,c,,Cc1ccsc1C[NH+](Cc1nc2ccc,Cc1ccsc1C[NH+](Cc1nc2cccc,25,add c at position 24,flow_matching,0.3,2.0,46,211
191,add,25.0,c,,Cc1ccsc1C[NH+](Cc1nc2cccc,Cc1ccsc1C[NH+](Cc1nc2ccccc,26,add c at position 25,flow_matching,0.3,2.0,46,211
192,add,26.0,2,,Cc1ccsc1C[NH+](Cc1nc2ccccc,Cc1ccsc1C[NH+](Cc1nc2ccccc2,27,add 2 at position 26,flow_matching,0.3,2.0,46,211
193,add,27.0,n,,Cc1ccsc1C[NH+](Cc1nc2ccccc2,Cc1ccsc1C[NH+](Cc1nc2ccccc2n,28,add n at position 27,flow_matching,0.3,2.0,46,211
194,add,28.0,1,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1,29,add 1 at position 28,flow_matching,0.3,2.0,46,211
195,add,29.0,C,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C,30,add C at position 29,flow_matching,0.3,2.0,46,211
196,add,30.0,(,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(,31,add ( at position 30,flow_matching,0.3,2.0,46,211
197,add,31.0,C,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C,32,add C at position 31,flow_matching,0.3,2.0,46,211
198,add,32.0,),,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C),33,add ) at position 32,flow_matching,0.3,2.0,46,211
199,add,33.0,C,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C),Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C,34,add C at position 33,flow_matching,0.3,2.0,46,211
200,add,34.0,),,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C),35,add ) at position 34,flow_matching,0.3,2.0,46,211
201,add,35.0,C,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C),Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C,36,add C at position 35,flow_matching,0.3,2.0,46,211
202,add,36.0,[,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[,37,add [ at position 36,flow_matching,0.3,2.0,46,211
203,add,37.0,C,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C,38,add C at position 37,flow_matching,0.3,2.0,46,211
204,add,38.0,@,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@,39,add @ at position 38,flow_matching,0.3,2.0,46,211
205,add,39.0,H,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H,40,add H at position 39,flow_matching,0.3,2.0,46,211
206,add,40.0,],,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H],41,add ] at position 40,flow_matching,0.3,2.0,46,211
207,add,41.0,(,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H],Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H](,42,add ( at position 41,flow_matching,0.3,2.0,46,211
208,add,42.0,C,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H](,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H](C,43,add C at position 42,flow_matching,0.3,2.0,46,211
209,add,43.0,),,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H](C,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H](C),44,add ) at position 43,flow_matching,0.3,2.0,46,211
210,add,44.0,O,,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H](C),Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H](C)O,45,add O at position 44,flow_matching,0.3,2.0,46,211
211,add,45.0,"
",,Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H](C)O,"Cc1ccsc1C[NH+](Cc1nc2ccccc2n1C(C)C)C[C@H](C)O
",46,"add 
 at position 45",flow_matching,0.3,2.0,46,211
