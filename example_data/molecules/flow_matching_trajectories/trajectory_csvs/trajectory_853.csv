step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,228
1,add,0.0,\,,,\,1,add \ at position 0,flow_matching,0.3,2.0,49,228
2,remove,0.0,\,,\,,0,remove \ from position 0,flow_matching,0.3,2.0,49,228
3,add,0.0,H,,,H,1,add H at position 0,flow_matching,0.3,2.0,49,228
4,add,0.0,n,,H,nH,2,add n at position 0,flow_matching,0.3,2.0,49,228
5,replace,1.0,#,H,nH,n#,2,replace H at position 1 with #,flow_matching,0.3,2.0,49,228
6,remove,0.0,n,,n#,#,1,remove n from position 0,flow_matching,0.3,2.0,49,228
7,add,1.0,o,,#,#o,2,add o at position 1,flow_matching,0.3,2.0,49,228
8,add,2.0,N,,#o,#oN,3,add N at position 2,flow_matching,0.3,2.0,49,228
9,replace,0.0,C,#,#oN,CoN,3,replace # at position 0 with C,flow_matching,0.3,2.0,49,228
10,add,1.0,6,,CoN,C6oN,4,add 6 at position 1,flow_matching,0.3,2.0,49,228
11,replace,1.0,/,6,C6oN,C/oN,4,replace 6 at position 1 with /,flow_matching,0.3,2.0,49,228
12,replace,2.0,l,o,C/oN,C/lN,4,replace o at position 2 with l,flow_matching,0.3,2.0,49,228
13,replace,3.0,C,N,C/lN,C/lC,4,replace N at position 3 with C,flow_matching,0.3,2.0,49,228
14,add,4.0,],,C/lC,C/lC],5,add ] at position 4,flow_matching,0.3,2.0,49,228
15,replace,2.0,C,l,C/lC],C/CC],5,replace l at position 2 with C,flow_matching,0.3,2.0,49,228
16,remove,4.0,],,C/CC],C/CC,4,remove ] from position 4,flow_matching,0.3,2.0,49,228
17,add,2.0,o,,C/CC,C/oCC,5,add o at position 2,flow_matching,0.3,2.0,49,228
18,add,1.0,I,,C/oCC,CI/oCC,6,add I at position 1,flow_matching,0.3,2.0,49,228
19,replace,1.0,/,I,CI/oCC,C//oCC,6,replace I at position 1 with /,flow_matching,0.3,2.0,49,228
20,replace,2.0,C,/,C//oCC,C/CoCC,6,replace / at position 2 with C,flow_matching,0.3,2.0,49,228
21,remove,3.0,o,,C/CoCC,C/CCC,5,remove o from position 3,flow_matching,0.3,2.0,49,228
22,add,1.0,C,,C/CCC,CC/CCC,6,add C at position 1,flow_matching,0.3,2.0,49,228
23,replace,0.0,I,C,CC/CCC,IC/CCC,6,replace C at position 0 with I,flow_matching,0.3,2.0,49,228
24,remove,1.0,C,,IC/CCC,I/CCC,5,remove C from position 1,flow_matching,0.3,2.0,49,228
25,replace,4.0,3,C,I/CCC,I/CC3,5,replace C at position 4 with 3,flow_matching,0.3,2.0,49,228
26,replace,0.0,C,I,I/CC3,C/CC3,5,replace I at position 0 with C,flow_matching,0.3,2.0,49,228
27,remove,3.0,C,,C/CC3,C/C3,4,remove C from position 3,flow_matching,0.3,2.0,49,228
28,add,2.0,4,,C/C3,C/4C3,5,add 4 at position 2,flow_matching,0.3,2.0,49,228
29,remove,2.0,4,,C/4C3,C/C3,4,remove 4 from position 2,flow_matching,0.3,2.0,49,228
30,replace,2.0,],C,C/C3,C/]3,4,replace C at position 2 with ],flow_matching,0.3,2.0,49,228
31,replace,3.0,s,3,C/]3,C/]s,4,replace 3 at position 3 with s,flow_matching,0.3,2.0,49,228
32,remove,1.0,/,,C/]s,C]s,3,remove / from position 1,flow_matching,0.3,2.0,49,228
33,remove,2.0,s,,C]s,C],2,remove s from position 2,flow_matching,0.3,2.0,49,228
34,replace,1.0,/,],C],C/,2,replace ] at position 1 with /,flow_matching,0.3,2.0,49,228
35,remove,1.0,/,,C/,C,1,remove / from position 1,flow_matching,0.3,2.0,49,228
36,replace,0.0,n,C,C,n,1,replace C at position 0 with n,flow_matching,0.3,2.0,49,228
37,add,0.0,/,,n,/n,2,add / at position 0,flow_matching,0.3,2.0,49,228
38,add,1.0,@,,/n,/@n,3,add @ at position 1,flow_matching,0.3,2.0,49,228
39,add,1.0,@,,/@n,/@@n,4,add @ at position 1,flow_matching,0.3,2.0,49,228
40,replace,0.0,C,/,/@@n,C@@n,4,replace / at position 0 with C,flow_matching,0.3,2.0,49,228
41,remove,0.0,C,,C@@n,@@n,3,remove C from position 0,flow_matching,0.3,2.0,49,228
42,replace,0.0,C,@,@@n,C@n,3,replace @ at position 0 with C,flow_matching,0.3,2.0,49,228
43,add,2.0,6,,C@n,C@6n,4,add 6 at position 2,flow_matching,0.3,2.0,49,228
44,remove,3.0,n,,C@6n,C@6,3,remove n from position 3,flow_matching,0.3,2.0,49,228
45,add,0.0,(,,C@6,(C@6,4,add ( at position 0,flow_matching,0.3,2.0,49,228
46,remove,0.0,(,,(C@6,C@6,3,remove ( from position 0,flow_matching,0.3,2.0,49,228
47,remove,2.0,6,,C@6,C@,2,remove 6 from position 2,flow_matching,0.3,2.0,49,228
48,remove,0.0,C,,C@,@,1,remove C from position 0,flow_matching,0.3,2.0,49,228
49,add,1.0,S,,@,@S,2,add S at position 1,flow_matching,0.3,2.0,49,228
50,replace,0.0,C,@,@S,CS,2,replace @ at position 0 with C,flow_matching,0.3,2.0,49,228
51,add,1.0,7,,CS,C7S,3,add 7 at position 1,flow_matching,0.3,2.0,49,228
52,replace,1.0,/,7,C7S,C/S,3,replace 7 at position 1 with /,flow_matching,0.3,2.0,49,228
53,replace,2.0,C,S,C/S,C/C,3,replace S at position 2 with C,flow_matching,0.3,2.0,49,228
54,replace,1.0,@,/,C/C,C@C,3,replace / at position 1 with @,flow_matching,0.3,2.0,49,228
55,replace,1.0,7,@,C@C,C7C,3,replace @ at position 1 with 7,flow_matching,0.3,2.0,49,228
56,replace,0.0,),C,C7C,)7C,3,replace C at position 0 with ),flow_matching,0.3,2.0,49,228
57,replace,0.0,C,),)7C,C7C,3,replace ) at position 0 with C,flow_matching,0.3,2.0,49,228
58,replace,0.0,o,C,C7C,o7C,3,replace C at position 0 with o,flow_matching,0.3,2.0,49,228
59,add,0.0,/,,o7C,/o7C,4,add / at position 0,flow_matching,0.3,2.0,49,228
60,replace,1.0,+,o,/o7C,/+7C,4,replace o at position 1 with +,flow_matching,0.3,2.0,49,228
61,add,4.0,O,,/+7C,/+7CO,5,add O at position 4,flow_matching,0.3,2.0,49,228
62,add,5.0,\,,/+7CO,/+7CO\,6,add \ at position 5,flow_matching,0.3,2.0,49,228
63,replace,0.0,C,/,/+7CO\,C+7CO\,6,replace / at position 0 with C,flow_matching,0.3,2.0,49,228
64,replace,1.0,/,+,C+7CO\,C/7CO\,6,replace + at position 1 with /,flow_matching,0.3,2.0,49,228
65,remove,2.0,7,,C/7CO\,C/CO\,5,remove 7 from position 2,flow_matching,0.3,2.0,49,228
66,replace,0.0,5,C,C/CO\,5/CO\,5,replace C at position 0 with 5,flow_matching,0.3,2.0,49,228
67,remove,1.0,/,,5/CO\,5CO\,4,remove / from position 1,flow_matching,0.3,2.0,49,228
68,remove,1.0,C,,5CO\,5O\,3,remove C from position 1,flow_matching,0.3,2.0,49,228
69,add,2.0,s,,5O\,5Os\,4,add s at position 2,flow_matching,0.3,2.0,49,228
70,add,2.0,+,,5Os\,5O+s\,5,add + at position 2,flow_matching,0.3,2.0,49,228
71,add,1.0,S,,5O+s\,5SO+s\,6,add S at position 1,flow_matching,0.3,2.0,49,228
72,remove,4.0,s,,5SO+s\,5SO+\,5,remove s from position 4,flow_matching,0.3,2.0,49,228
73,replace,0.0,C,5,5SO+\,CSO+\,5,replace 5 at position 0 with C,flow_matching,0.3,2.0,49,228
74,replace,1.0,/,S,CSO+\,C/O+\,5,replace S at position 1 with /,flow_matching,0.3,2.0,49,228
75,remove,0.0,C,,C/O+\,/O+\,4,remove C from position 0,flow_matching,0.3,2.0,49,228
76,add,0.0,/,,/O+\,//O+\,5,add / at position 0,flow_matching,0.3,2.0,49,228
77,replace,2.0,H,O,//O+\,//H+\,5,replace O at position 2 with H,flow_matching,0.3,2.0,49,228
78,remove,1.0,/,,//H+\,/H+\,4,remove / from position 1,flow_matching,0.3,2.0,49,228
79,replace,0.0,C,/,/H+\,CH+\,4,replace / at position 0 with C,flow_matching,0.3,2.0,49,228
80,remove,2.0,+,,CH+\,CH\,3,remove + from position 2,flow_matching,0.3,2.0,49,228
81,replace,1.0,/,H,CH\,C/\,3,replace H at position 1 with /,flow_matching,0.3,2.0,49,228
82,replace,2.0,C,\,C/\,C/C,3,replace \ at position 2 with C,flow_matching,0.3,2.0,49,228
83,remove,1.0,/,,C/C,CC,2,remove / from position 1,flow_matching,0.3,2.0,49,228
84,replace,0.0,B,C,CC,BC,2,replace C at position 0 with B,flow_matching,0.3,2.0,49,228
85,replace,1.0,o,C,BC,Bo,2,replace C at position 1 with o,flow_matching,0.3,2.0,49,228
86,add,1.0,6,,Bo,B6o,3,add 6 at position 1,flow_matching,0.3,2.0,49,228
87,replace,0.0,C,B,B6o,C6o,3,replace B at position 0 with C,flow_matching,0.3,2.0,49,228
88,add,3.0,/,,C6o,C6o/,4,add / at position 3,flow_matching,0.3,2.0,49,228
89,replace,3.0,l,/,C6o/,C6ol,4,replace / at position 3 with l,flow_matching,0.3,2.0,49,228
90,replace,1.0,/,6,C6ol,C/ol,4,replace 6 at position 1 with /,flow_matching,0.3,2.0,49,228
91,replace,2.0,C,o,C/ol,C/Cl,4,replace o at position 2 with C,flow_matching,0.3,2.0,49,228
92,replace,3.0,(,l,C/Cl,C/C(,4,replace l at position 3 with (,flow_matching,0.3,2.0,49,228
93,add,4.0,=,,C/C(,C/C(=,5,add = at position 4,flow_matching,0.3,2.0,49,228
94,replace,1.0,1,/,C/C(=,C1C(=,5,replace / at position 1 with 1,flow_matching,0.3,2.0,49,228
95,add,2.0,C,,C1C(=,C1CC(=,6,add C at position 2,flow_matching,0.3,2.0,49,228
96,remove,2.0,C,,C1CC(=,C1C(=,5,remove C from position 2,flow_matching,0.3,2.0,49,228
97,replace,1.0,/,1,C1C(=,C/C(=,5,replace 1 at position 1 with /,flow_matching,0.3,2.0,49,228
98,replace,4.0,2,=,C/C(=,C/C(2,5,replace = at position 4 with 2,flow_matching,0.3,2.0,49,228
99,replace,0.0,-,C,C/C(2,-/C(2,5,replace C at position 0 with -,flow_matching,0.3,2.0,49,228
100,replace,0.0,C,-,-/C(2,C/C(2,5,replace - at position 0 with C,flow_matching,0.3,2.0,49,228
101,replace,4.0,/,2,C/C(2,C/C(/,5,replace 2 at position 4 with /,flow_matching,0.3,2.0,49,228
102,replace,4.0,=,/,C/C(/,C/C(=,5,replace / at position 4 with =,flow_matching,0.3,2.0,49,228
103,add,5.0,C,,C/C(=,C/C(=C,6,add C at position 5,flow_matching,0.3,2.0,49,228
104,add,6.0,/,,C/C(=C,C/C(=C/,7,add / at position 6,flow_matching,0.3,2.0,49,228
105,replace,5.0,#,C,C/C(=C/,C/C(=#/,7,replace C at position 5 with #,flow_matching,0.3,2.0,49,228
106,replace,0.0,+,C,C/C(=#/,+/C(=#/,7,replace C at position 0 with +,flow_matching,0.3,2.0,49,228
107,add,7.0,C,,+/C(=#/,+/C(=#/C,8,add C at position 7,flow_matching,0.3,2.0,49,228
108,replace,0.0,C,+,+/C(=#/C,C/C(=#/C,8,replace + at position 0 with C,flow_matching,0.3,2.0,49,228
109,remove,7.0,C,,C/C(=#/C,C/C(=#/,7,remove C from position 7,flow_matching,0.3,2.0,49,228
110,add,0.0,N,,C/C(=#/,NC/C(=#/,8,add N at position 0,flow_matching,0.3,2.0,49,228
111,replace,0.0,C,N,NC/C(=#/,CC/C(=#/,8,replace N at position 0 with C,flow_matching,0.3,2.0,49,228
112,remove,5.0,=,,CC/C(=#/,CC/C(#/,7,remove = from position 5,flow_matching,0.3,2.0,49,228
113,add,4.0,#,,CC/C(#/,CC/C#(#/,8,add # at position 4,flow_matching,0.3,2.0,49,228
114,add,7.0,-,,CC/C#(#/,CC/C#(#-/,9,add - at position 7,flow_matching,0.3,2.0,49,228
115,add,1.0,=,,CC/C#(#-/,C=C/C#(#-/,10,add = at position 1,flow_matching,0.3,2.0,49,228
116,replace,1.0,/,=,C=C/C#(#-/,C/C/C#(#-/,10,replace = at position 1 with /,flow_matching,0.3,2.0,49,228
117,replace,3.0,(,/,C/C/C#(#-/,C/C(C#(#-/,10,replace / at position 3 with (,flow_matching,0.3,2.0,49,228
118,remove,3.0,(,,C/C(C#(#-/,C/CC#(#-/,9,remove ( from position 3,flow_matching,0.3,2.0,49,228
119,replace,3.0,(,C,C/CC#(#-/,C/C(#(#-/,9,replace C at position 3 with (,flow_matching,0.3,2.0,49,228
120,replace,4.0,=,#,C/C(#(#-/,C/C(=(#-/,9,replace # at position 4 with =,flow_matching,0.3,2.0,49,228
121,replace,5.0,r,(,C/C(=(#-/,C/C(=r#-/,9,replace ( at position 5 with r,flow_matching,0.3,2.0,49,228
122,replace,4.0,6,=,C/C(=r#-/,C/C(6r#-/,9,replace = at position 4 with 6,flow_matching,0.3,2.0,49,228
123,replace,4.0,=,6,C/C(6r#-/,C/C(=r#-/,9,replace 6 at position 4 with =,flow_matching,0.3,2.0,49,228
124,replace,5.0,C,r,C/C(=r#-/,C/C(=C#-/,9,replace r at position 5 with C,flow_matching,0.3,2.0,49,228
125,add,7.0,2,,C/C(=C#-/,C/C(=C#2-/,10,add 2 at position 7,flow_matching,0.3,2.0,49,228
126,add,0.0,=,,C/C(=C#2-/,=C/C(=C#2-/,11,add = at position 0,flow_matching,0.3,2.0,49,228
127,replace,0.0,C,=,=C/C(=C#2-/,CC/C(=C#2-/,11,replace = at position 0 with C,flow_matching,0.3,2.0,49,228
128,replace,10.0,o,/,CC/C(=C#2-/,CC/C(=C#2-o,11,replace / at position 10 with o,flow_matching,0.3,2.0,49,228
129,replace,7.0,2,#,CC/C(=C#2-o,CC/C(=C22-o,11,replace # at position 7 with 2,flow_matching,0.3,2.0,49,228
130,replace,0.0,F,C,CC/C(=C22-o,FC/C(=C22-o,11,replace C at position 0 with F,flow_matching,0.3,2.0,49,228
131,replace,0.0,/,F,FC/C(=C22-o,/C/C(=C22-o,11,replace F at position 0 with /,flow_matching,0.3,2.0,49,228
132,replace,0.0,C,/,/C/C(=C22-o,CC/C(=C22-o,11,replace / at position 0 with C,flow_matching,0.3,2.0,49,228
133,replace,1.0,/,C,CC/C(=C22-o,C//C(=C22-o,11,replace C at position 1 with /,flow_matching,0.3,2.0,49,228
134,remove,4.0,(,,C//C(=C22-o,C//C=C22-o,10,remove ( from position 4,flow_matching,0.3,2.0,49,228
135,replace,3.0,5,C,C//C=C22-o,C//5=C22-o,10,replace C at position 3 with 5,flow_matching,0.3,2.0,49,228
136,remove,7.0,2,,C//5=C22-o,C//5=C2-o,9,remove 2 from position 7,flow_matching,0.3,2.0,49,228
137,replace,2.0,C,/,C//5=C2-o,C/C5=C2-o,9,replace / at position 2 with C,flow_matching,0.3,2.0,49,228
138,replace,3.0,(,5,C/C5=C2-o,C/C(=C2-o,9,replace 5 at position 3 with (,flow_matching,0.3,2.0,49,228
139,replace,6.0,/,2,C/C(=C2-o,C/C(=C/-o,9,replace 2 at position 6 with /,flow_matching,0.3,2.0,49,228
140,add,2.0,F,,C/C(=C/-o,C/FC(=C/-o,10,add F at position 2,flow_matching,0.3,2.0,49,228
141,replace,0.0,F,C,C/FC(=C/-o,F/FC(=C/-o,10,replace C at position 0 with F,flow_matching,0.3,2.0,49,228
142,replace,0.0,C,F,F/FC(=C/-o,C/FC(=C/-o,10,replace F at position 0 with C,flow_matching,0.3,2.0,49,228
143,add,0.0,S,,C/FC(=C/-o,SC/FC(=C/-o,11,add S at position 0,flow_matching,0.3,2.0,49,228
144,remove,5.0,(,,SC/FC(=C/-o,SC/FC=C/-o,10,remove ( from position 5,flow_matching,0.3,2.0,49,228
145,add,3.0,c,,SC/FC=C/-o,SC/cFC=C/-o,11,add c at position 3,flow_matching,0.3,2.0,49,228
146,remove,8.0,/,,SC/cFC=C/-o,SC/cFC=C-o,10,remove / from position 8,flow_matching,0.3,2.0,49,228
147,remove,4.0,F,,SC/cFC=C-o,SC/cC=C-o,9,remove F from position 4,flow_matching,0.3,2.0,49,228
148,replace,2.0,l,/,SC/cC=C-o,SClcC=C-o,9,replace / at position 2 with l,flow_matching,0.3,2.0,49,228
149,replace,3.0,4,c,SClcC=C-o,SCl4C=C-o,9,replace c at position 3 with 4,flow_matching,0.3,2.0,49,228
150,add,1.0,),,SCl4C=C-o,S)Cl4C=C-o,10,add ) at position 1,flow_matching,0.3,2.0,49,228
151,add,4.0,2,,S)Cl4C=C-o,S)Cl24C=C-o,11,add 2 at position 4,flow_matching,0.3,2.0,49,228
152,replace,0.0,C,S,S)Cl24C=C-o,C)Cl24C=C-o,11,replace S at position 0 with C,flow_matching,0.3,2.0,49,228
153,add,0.0,),,C)Cl24C=C-o,)C)Cl24C=C-o,12,add ) at position 0,flow_matching,0.3,2.0,49,228
154,replace,0.0,3,),)C)Cl24C=C-o,3C)Cl24C=C-o,12,replace ) at position 0 with 3,flow_matching,0.3,2.0,49,228
155,add,0.0,4,,3C)Cl24C=C-o,43C)Cl24C=C-o,13,add 4 at position 0,flow_matching,0.3,2.0,49,228
156,add,9.0,],,43C)Cl24C=C-o,43C)Cl24C]=C-o,14,add ] at position 9,flow_matching,0.3,2.0,49,228
157,replace,0.0,C,4,43C)Cl24C]=C-o,C3C)Cl24C]=C-o,14,replace 4 at position 0 with C,flow_matching,0.3,2.0,49,228
158,replace,0.0,N,C,C3C)Cl24C]=C-o,N3C)Cl24C]=C-o,14,replace C at position 0 with N,flow_matching,0.3,2.0,49,228
159,add,8.0,N,,N3C)Cl24C]=C-o,N3C)Cl24NC]=C-o,15,add N at position 8,flow_matching,0.3,2.0,49,228
160,add,3.0,O,,N3C)Cl24NC]=C-o,N3CO)Cl24NC]=C-o,16,add O at position 3,flow_matching,0.3,2.0,49,228
161,add,15.0,7,,N3CO)Cl24NC]=C-o,N3CO)Cl24NC]=C-7o,17,add 7 at position 15,flow_matching,0.3,2.0,49,228
162,replace,2.0,r,C,N3CO)Cl24NC]=C-7o,N3rO)Cl24NC]=C-7o,17,replace C at position 2 with r,flow_matching,0.3,2.0,49,228
163,remove,4.0,),,N3rO)Cl24NC]=C-7o,N3rOCl24NC]=C-7o,16,remove ) from position 4,flow_matching,0.3,2.0,49,228
164,replace,0.0,C,N,N3rOCl24NC]=C-7o,C3rOCl24NC]=C-7o,16,replace N at position 0 with C,flow_matching,0.3,2.0,49,228
165,remove,14.0,7,,C3rOCl24NC]=C-7o,C3rOCl24NC]=C-o,15,remove 7 from position 14,flow_matching,0.3,2.0,49,228
166,add,11.0,1,,C3rOCl24NC]=C-o,C3rOCl24NC]1=C-o,16,add 1 at position 11,flow_matching,0.3,2.0,49,228
167,replace,1.0,/,3,C3rOCl24NC]1=C-o,C/rOCl24NC]1=C-o,16,replace 3 at position 1 with /,flow_matching,0.3,2.0,49,228
168,replace,2.0,C,r,C/rOCl24NC]1=C-o,C/COCl24NC]1=C-o,16,replace r at position 2 with C,flow_matching,0.3,2.0,49,228
169,replace,3.0,(,O,C/COCl24NC]1=C-o,C/C(Cl24NC]1=C-o,16,replace O at position 3 with (,flow_matching,0.3,2.0,49,228
170,replace,13.0,7,C,C/C(Cl24NC]1=C-o,C/C(Cl24NC]1=7-o,16,replace C at position 13 with 7,flow_matching,0.3,2.0,49,228
171,remove,14.0,-,,C/C(Cl24NC]1=7-o,C/C(Cl24NC]1=7o,15,remove - from position 14,flow_matching,0.3,2.0,49,228
172,remove,10.0,],,C/C(Cl24NC]1=7o,C/C(Cl24NC1=7o,14,remove ] from position 10,flow_matching,0.3,2.0,49,228
173,replace,8.0,7,N,C/C(Cl24NC1=7o,C/C(Cl247C1=7o,14,replace N at position 8 with 7,flow_matching,0.3,2.0,49,228
174,replace,4.0,=,C,C/C(Cl247C1=7o,C/C(=l247C1=7o,14,replace C at position 4 with =,flow_matching,0.3,2.0,49,228
175,add,14.0,/,,C/C(=l247C1=7o,C/C(=l247C1=7o/,15,add / at position 14,flow_matching,0.3,2.0,49,228
176,add,11.0,H,,C/C(=l247C1=7o/,C/C(=l247C1H=7o/,16,add H at position 11,flow_matching,0.3,2.0,49,228
177,add,11.0,@,,C/C(=l247C1H=7o/,C/C(=l247C1@H=7o/,17,add @ at position 11,flow_matching,0.3,2.0,49,228
178,remove,1.0,/,,C/C(=l247C1@H=7o/,CC(=l247C1@H=7o/,16,remove / from position 1,flow_matching,0.3,2.0,49,228
179,remove,6.0,4,,CC(=l247C1@H=7o/,CC(=l27C1@H=7o/,15,remove 4 from position 6,flow_matching,0.3,2.0,49,228
180,remove,1.0,C,,CC(=l27C1@H=7o/,C(=l27C1@H=7o/,14,remove C from position 1,flow_matching,0.3,2.0,49,228
181,replace,1.0,/,(,C(=l27C1@H=7o/,C/=l27C1@H=7o/,14,replace ( at position 1 with /,flow_matching,0.3,2.0,49,228
182,replace,2.0,C,=,C/=l27C1@H=7o/,C/Cl27C1@H=7o/,14,replace = at position 2 with C,flow_matching,0.3,2.0,49,228
183,replace,3.0,(,l,C/Cl27C1@H=7o/,C/C(27C1@H=7o/,14,replace l at position 3 with (,flow_matching,0.3,2.0,49,228
184,replace,4.0,=,2,C/C(27C1@H=7o/,C/C(=7C1@H=7o/,14,replace 2 at position 4 with =,flow_matching,0.3,2.0,49,228
185,replace,5.0,C,7,C/C(=7C1@H=7o/,C/C(=CC1@H=7o/,14,replace 7 at position 5 with C,flow_matching,0.3,2.0,49,228
186,replace,6.0,/,C,C/C(=CC1@H=7o/,C/C(=C/1@H=7o/,14,replace C at position 6 with /,flow_matching,0.3,2.0,49,228
187,replace,7.0,C,1,C/C(=C/1@H=7o/,C/C(=C/C@H=7o/,14,replace 1 at position 7 with C,flow_matching,0.3,2.0,49,228
188,replace,8.0,(,@,C/C(=C/C@H=7o/,C/C(=C/C(H=7o/,14,replace @ at position 8 with (,flow_matching,0.3,2.0,49,228
189,replace,9.0,=,H,C/C(=C/C(H=7o/,C/C(=C/C(==7o/,14,replace H at position 9 with =,flow_matching,0.3,2.0,49,228
190,replace,10.0,O,=,C/C(=C/C(==7o/,C/C(=C/C(=O7o/,14,replace = at position 10 with O,flow_matching,0.3,2.0,49,228
191,replace,11.0,),7,C/C(=C/C(=O7o/,C/C(=C/C(=O)o/,14,replace 7 at position 11 with ),flow_matching,0.3,2.0,49,228
192,replace,12.0,N,o,C/C(=C/C(=O)o/,C/C(=C/C(=O)N/,14,replace o at position 12 with N,flow_matching,0.3,2.0,49,228
193,replace,13.0,[,/,C/C(=C/C(=O)N/,C/C(=C/C(=O)N[,14,replace / at position 13 with [,flow_matching,0.3,2.0,49,228
194,add,14.0,C,,C/C(=C/C(=O)N[,C/C(=C/C(=O)N[C,15,add C at position 14,flow_matching,0.3,2.0,49,228
195,add,15.0,@,,C/C(=C/C(=O)N[C,C/C(=C/C(=O)N[C@,16,add @ at position 15,flow_matching,0.3,2.0,49,228
196,add,16.0,@,,C/C(=C/C(=O)N[C@,C/C(=C/C(=O)N[C@@,17,add @ at position 16,flow_matching,0.3,2.0,49,228
197,add,17.0,H,,C/C(=C/C(=O)N[C@@,C/C(=C/C(=O)N[C@@H,18,add H at position 17,flow_matching,0.3,2.0,49,228
198,add,18.0,],,C/C(=C/C(=O)N[C@@H,C/C(=C/C(=O)N[C@@H],19,add ] at position 18,flow_matching,0.3,2.0,49,228
199,add,19.0,(,,C/C(=C/C(=O)N[C@@H],C/C(=C/C(=O)N[C@@H](,20,add ( at position 19,flow_matching,0.3,2.0,49,228
200,add,20.0,C,,C/C(=C/C(=O)N[C@@H](,C/C(=C/C(=O)N[C@@H](C,21,add C at position 20,flow_matching,0.3,2.0,49,228
201,add,21.0,),,C/C(=C/C(=O)N[C@@H](C,C/C(=C/C(=O)N[C@@H](C),22,add ) at position 21,flow_matching,0.3,2.0,49,228
202,add,22.0,c,,C/C(=C/C(=O)N[C@@H](C),C/C(=C/C(=O)N[C@@H](C)c,23,add c at position 22,flow_matching,0.3,2.0,49,228
203,add,23.0,1,,C/C(=C/C(=O)N[C@@H](C)c,C/C(=C/C(=O)N[C@@H](C)c1,24,add 1 at position 23,flow_matching,0.3,2.0,49,228
204,add,24.0,c,,C/C(=C/C(=O)N[C@@H](C)c1,C/C(=C/C(=O)N[C@@H](C)c1c,25,add c at position 24,flow_matching,0.3,2.0,49,228
205,add,25.0,(,,C/C(=C/C(=O)N[C@@H](C)c1c,C/C(=C/C(=O)N[C@@H](C)c1c(,26,add ( at position 25,flow_matching,0.3,2.0,49,228
206,add,26.0,C,,C/C(=C/C(=O)N[C@@H](C)c1c(,C/C(=C/C(=O)N[C@@H](C)c1c(C,27,add C at position 26,flow_matching,0.3,2.0,49,228
207,add,27.0,),,C/C(=C/C(=O)N[C@@H](C)c1c(C,C/C(=C/C(=O)N[C@@H](C)c1c(C),28,add ) at position 27,flow_matching,0.3,2.0,49,228
208,add,28.0,n,,C/C(=C/C(=O)N[C@@H](C)c1c(C),C/C(=C/C(=O)N[C@@H](C)c1c(C)n,29,add n at position 28,flow_matching,0.3,2.0,49,228
209,add,29.0,o,,C/C(=C/C(=O)N[C@@H](C)c1c(C)n,C/C(=C/C(=O)N[C@@H](C)c1c(C)no,30,add o at position 29,flow_matching,0.3,2.0,49,228
210,add,30.0,c,,C/C(=C/C(=O)N[C@@H](C)c1c(C)no,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc,31,add c at position 30,flow_matching,0.3,2.0,49,228
211,add,31.0,1,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1,32,add 1 at position 31,flow_matching,0.3,2.0,49,228
212,add,32.0,C,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C,33,add C at position 32,flow_matching,0.3,2.0,49,228
213,add,33.0,),,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C),34,add ) at position 33,flow_matching,0.3,2.0,49,228
214,add,34.0,c,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C),C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c,35,add c at position 34,flow_matching,0.3,2.0,49,228
215,add,35.0,1,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1,36,add 1 at position 35,flow_matching,0.3,2.0,49,228
216,add,36.0,c,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1c,37,add c at position 36,flow_matching,0.3,2.0,49,228
217,add,37.0,c,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1c,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1cc,38,add c at position 37,flow_matching,0.3,2.0,49,228
218,add,38.0,c,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1cc,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccc,39,add c at position 38,flow_matching,0.3,2.0,49,228
219,add,39.0,c,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccc,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1cccc,40,add c at position 39,flow_matching,0.3,2.0,49,228
220,add,40.0,c,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1cccc,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc,41,add c at position 40,flow_matching,0.3,2.0,49,228
221,add,41.0,1,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1,42,add 1 at position 41,flow_matching,0.3,2.0,49,228
222,add,42.0,O,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1O,43,add O at position 42,flow_matching,0.3,2.0,49,228
223,add,43.0,C,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1O,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC,44,add C at position 43,flow_matching,0.3,2.0,49,228
224,add,44.0,(,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC(,45,add ( at position 44,flow_matching,0.3,2.0,49,228
225,add,45.0,F,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC(,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC(F,46,add F at position 45,flow_matching,0.3,2.0,49,228
226,add,46.0,),,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC(F,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC(F),47,add ) at position 46,flow_matching,0.3,2.0,49,228
227,add,47.0,F,,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC(F),C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC(F)F,48,add F at position 47,flow_matching,0.3,2.0,49,228
228,add,48.0,"
",,C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC(F)F,"C/C(=C/C(=O)N[C@@H](C)c1c(C)noc1C)c1ccccc1OC(F)F
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,228
