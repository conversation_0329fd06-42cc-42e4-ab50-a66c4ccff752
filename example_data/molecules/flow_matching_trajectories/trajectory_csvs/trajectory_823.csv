step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,55,259
1,add,0.0,=,,,=,1,add = at position 0,flow_matching,0.3,2.0,55,259
2,replace,0.0,O,=,=,O,1,replace = at position 0 with O,flow_matching,0.3,2.0,55,259
3,add,0.0,=,,O,=O,2,add = at position 0,flow_matching,0.3,2.0,55,259
4,replace,0.0,O,=,=O,OO,2,replace = at position 0 with O,flow_matching,0.3,2.0,55,259
5,add,2.0,=,,OO,OO=,3,add = at position 2,flow_matching,0.3,2.0,55,259
6,replace,1.0,=,O,OO=,O==,3,replace O at position 1 with =,flow_matching,0.3,2.0,55,259
7,replace,0.0,\,O,O==,\==,3,replace O at position 0 with \,flow_matching,0.3,2.0,55,259
8,replace,0.0,O,\,\==,O==,3,replace \ at position 0 with O,flow_matching,0.3,2.0,55,259
9,replace,0.0,o,O,O==,o==,3,replace O at position 0 with o,flow_matching,0.3,2.0,55,259
10,remove,2.0,=,,o==,o=,2,remove = from position 2,flow_matching,0.3,2.0,55,259
11,remove,0.0,o,,o=,=,1,remove o from position 0,flow_matching,0.3,2.0,55,259
12,remove,0.0,=,,=,,0,remove = from position 0,flow_matching,0.3,2.0,55,259
13,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,55,259
14,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,55,259
15,remove,1.0,=,,O=,O,1,remove = from position 1,flow_matching,0.3,2.0,55,259
16,replace,0.0,B,O,O,B,1,replace O at position 0 with B,flow_matching,0.3,2.0,55,259
17,replace,0.0,O,B,B,O,1,replace B at position 0 with O,flow_matching,0.3,2.0,55,259
18,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,55,259
19,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,55,259
20,replace,0.0,O,F,F,O,1,replace F at position 0 with O,flow_matching,0.3,2.0,55,259
21,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,55,259
22,remove,1.0,=,,O=,O,1,remove = from position 1,flow_matching,0.3,2.0,55,259
23,add,1.0,#,,O,O#,2,add # at position 1,flow_matching,0.3,2.0,55,259
24,remove,1.0,#,,O#,O,1,remove # from position 1,flow_matching,0.3,2.0,55,259
25,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,55,259
26,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,55,259
27,add,1.0,1,,O,O1,2,add 1 at position 1,flow_matching,0.3,2.0,55,259
28,remove,0.0,O,,O1,1,1,remove O from position 0,flow_matching,0.3,2.0,55,259
29,replace,0.0,O,1,1,O,1,replace 1 at position 0 with O,flow_matching,0.3,2.0,55,259
30,replace,0.0,7,O,O,7,1,replace O at position 0 with 7,flow_matching,0.3,2.0,55,259
31,replace,0.0,O,7,7,O,1,replace 7 at position 0 with O,flow_matching,0.3,2.0,55,259
32,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,55,259
33,add,0.0,(,,O=,(O=,3,add ( at position 0,flow_matching,0.3,2.0,55,259
34,replace,0.0,O,(,(O=,OO=,3,replace ( at position 0 with O,flow_matching,0.3,2.0,55,259
35,remove,1.0,O,,OO=,O=,2,remove O from position 1,flow_matching,0.3,2.0,55,259
36,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,55,259
37,replace,0.0,O,=,=,O,1,replace = at position 0 with O,flow_matching,0.3,2.0,55,259
38,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,55,259
39,add,2.0,I,,O=,O=I,3,add I at position 2,flow_matching,0.3,2.0,55,259
40,replace,2.0,C,I,O=I,O=C,3,replace I at position 2 with C,flow_matching,0.3,2.0,55,259
41,remove,1.0,=,,O=C,OC,2,remove = from position 1,flow_matching,0.3,2.0,55,259
42,add,2.0,r,,OC,OCr,3,add r at position 2,flow_matching,0.3,2.0,55,259
43,add,1.0,],,OCr,O]Cr,4,add ] at position 1,flow_matching,0.3,2.0,55,259
44,replace,1.0,=,],O]Cr,O=Cr,4,replace ] at position 1 with =,flow_matching,0.3,2.0,55,259
45,add,1.0,r,,O=Cr,Or=Cr,5,add r at position 1,flow_matching,0.3,2.0,55,259
46,replace,1.0,=,r,Or=Cr,O==Cr,5,replace r at position 1 with =,flow_matching,0.3,2.0,55,259
47,add,3.0,@,,O==Cr,O==@Cr,6,add @ at position 3,flow_matching,0.3,2.0,55,259
48,add,3.0,4,,O==@Cr,O==4@Cr,7,add 4 at position 3,flow_matching,0.3,2.0,55,259
49,replace,2.0,C,=,O==4@Cr,O=C4@Cr,7,replace = at position 2 with C,flow_matching,0.3,2.0,55,259
50,remove,0.0,O,,O=C4@Cr,=C4@Cr,6,remove O from position 0,flow_matching,0.3,2.0,55,259
51,replace,0.0,O,=,=C4@Cr,OC4@Cr,6,replace = at position 0 with O,flow_matching,0.3,2.0,55,259
52,replace,1.0,4,C,OC4@Cr,O44@Cr,6,replace C at position 1 with 4,flow_matching,0.3,2.0,55,259
53,add,3.0,-,,O44@Cr,O44-@Cr,7,add - at position 3,flow_matching,0.3,2.0,55,259
54,replace,1.0,=,4,O44-@Cr,O=4-@Cr,7,replace 4 at position 1 with =,flow_matching,0.3,2.0,55,259
55,add,2.0,H,,O=4-@Cr,O=H4-@Cr,8,add H at position 2,flow_matching,0.3,2.0,55,259
56,replace,2.0,C,H,O=H4-@Cr,O=C4-@Cr,8,replace H at position 2 with C,flow_matching,0.3,2.0,55,259
57,remove,7.0,r,,O=C4-@Cr,O=C4-@C,7,remove r from position 7,flow_matching,0.3,2.0,55,259
58,add,4.0,+,,O=C4-@C,O=C4+-@C,8,add + at position 4,flow_matching,0.3,2.0,55,259
59,add,1.0,\,,O=C4+-@C,O\=C4+-@C,9,add \ at position 1,flow_matching,0.3,2.0,55,259
60,replace,1.0,=,\,O\=C4+-@C,O==C4+-@C,9,replace \ at position 1 with =,flow_matching,0.3,2.0,55,259
61,replace,7.0,H,@,O==C4+-@C,O==C4+-HC,9,replace @ at position 7 with H,flow_matching,0.3,2.0,55,259
62,add,2.0,[,,O==C4+-HC,O=[=C4+-HC,10,add [ at position 2,flow_matching,0.3,2.0,55,259
63,replace,2.0,C,[,O=[=C4+-HC,O=C=C4+-HC,10,replace [ at position 2 with C,flow_matching,0.3,2.0,55,259
64,remove,6.0,+,,O=C=C4+-HC,O=C=C4-HC,9,remove + from position 6,flow_matching,0.3,2.0,55,259
65,remove,4.0,C,,O=C=C4-HC,O=C=4-HC,8,remove C from position 4,flow_matching,0.3,2.0,55,259
66,replace,2.0,2,C,O=C=4-HC,O=2=4-HC,8,replace C at position 2 with 2,flow_matching,0.3,2.0,55,259
67,replace,2.0,C,2,O=2=4-HC,O=C=4-HC,8,replace 2 at position 2 with C,flow_matching,0.3,2.0,55,259
68,replace,3.0,(,=,O=C=4-HC,O=C(4-HC,8,replace = at position 3 with (,flow_matching,0.3,2.0,55,259
69,replace,6.0,o,H,O=C(4-HC,O=C(4-oC,8,replace H at position 6 with o,flow_matching,0.3,2.0,55,259
70,replace,4.0,C,4,O=C(4-oC,O=C(C-oC,8,replace 4 at position 4 with C,flow_matching,0.3,2.0,55,259
71,remove,0.0,O,,O=C(C-oC,=C(C-oC,7,remove O from position 0,flow_matching,0.3,2.0,55,259
72,remove,5.0,o,,=C(C-oC,=C(C-C,6,remove o from position 5,flow_matching,0.3,2.0,55,259
73,remove,5.0,C,,=C(C-C,=C(C-,5,remove C from position 5,flow_matching,0.3,2.0,55,259
74,replace,0.0,),=,=C(C-,)C(C-,5,replace = at position 0 with ),flow_matching,0.3,2.0,55,259
75,remove,3.0,C,,)C(C-,)C(-,4,remove C from position 3,flow_matching,0.3,2.0,55,259
76,add,4.0,+,,)C(-,)C(-+,5,add + at position 4,flow_matching,0.3,2.0,55,259
77,add,4.0,6,,)C(-+,)C(-6+,6,add 6 at position 4,flow_matching,0.3,2.0,55,259
78,remove,2.0,(,,)C(-6+,)C-6+,5,remove ( from position 2,flow_matching,0.3,2.0,55,259
79,replace,0.0,r,),)C-6+,rC-6+,5,replace ) at position 0 with r,flow_matching,0.3,2.0,55,259
80,remove,0.0,r,,rC-6+,C-6+,4,remove r from position 0,flow_matching,0.3,2.0,55,259
81,replace,0.0,O,C,C-6+,O-6+,4,replace C at position 0 with O,flow_matching,0.3,2.0,55,259
82,replace,1.0,=,-,O-6+,O=6+,4,replace - at position 1 with =,flow_matching,0.3,2.0,55,259
83,replace,2.0,C,6,O=6+,O=C+,4,replace 6 at position 2 with C,flow_matching,0.3,2.0,55,259
84,replace,3.0,N,+,O=C+,O=CN,4,replace + at position 3 with N,flow_matching,0.3,2.0,55,259
85,add,1.0,B,,O=CN,OB=CN,5,add B at position 1,flow_matching,0.3,2.0,55,259
86,remove,0.0,O,,OB=CN,B=CN,4,remove O from position 0,flow_matching,0.3,2.0,55,259
87,add,1.0,n,,B=CN,Bn=CN,5,add n at position 1,flow_matching,0.3,2.0,55,259
88,add,4.0,r,,Bn=CN,Bn=CrN,6,add r at position 4,flow_matching,0.3,2.0,55,259
89,replace,0.0,O,B,Bn=CrN,On=CrN,6,replace B at position 0 with O,flow_matching,0.3,2.0,55,259
90,replace,1.0,=,n,On=CrN,O==CrN,6,replace n at position 1 with =,flow_matching,0.3,2.0,55,259
91,replace,0.0,C,O,O==CrN,C==CrN,6,replace O at position 0 with C,flow_matching,0.3,2.0,55,259
92,replace,0.0,O,C,C==CrN,O==CrN,6,replace C at position 0 with O,flow_matching,0.3,2.0,55,259
93,add,3.0,[,,O==CrN,O==[CrN,7,add [ at position 3,flow_matching,0.3,2.0,55,259
94,add,7.0,o,,O==[CrN,O==[CrNo,8,add o at position 7,flow_matching,0.3,2.0,55,259
95,add,5.0,\,,O==[CrNo,O==[C\rNo,9,add \ at position 5,flow_matching,0.3,2.0,55,259
96,add,9.0,C,,O==[C\rNo,O==[C\rNoC,10,add C at position 9,flow_matching,0.3,2.0,55,259
97,replace,2.0,C,=,O==[C\rNoC,O=C[C\rNoC,10,replace = at position 2 with C,flow_matching,0.3,2.0,55,259
98,add,5.0,N,,O=C[C\rNoC,O=C[CN\rNoC,11,add N at position 5,flow_matching,0.3,2.0,55,259
99,replace,3.0,(,[,O=C[CN\rNoC,O=C(CN\rNoC,11,replace [ at position 3 with (,flow_matching,0.3,2.0,55,259
100,remove,2.0,C,,O=C(CN\rNoC,O=(CN\rNoC,10,remove C from position 2,flow_matching,0.3,2.0,55,259
101,replace,2.0,C,(,O=(CN\rNoC,O=CCN\rNoC,10,replace ( at position 2 with C,flow_matching,0.3,2.0,55,259
102,add,10.0,r,,O=CCN\rNoC,O=CCN\rNoCr,11,add r at position 10,flow_matching,0.3,2.0,55,259
103,add,6.0,S,,O=CCN\rNoCr,O=CCN\SrNoCr,12,add S at position 6,flow_matching,0.3,2.0,55,259
104,replace,3.0,(,C,O=CCN\SrNoCr,O=C(N\SrNoCr,12,replace C at position 3 with (,flow_matching,0.3,2.0,55,259
105,add,0.0,n,,O=C(N\SrNoCr,nO=C(N\SrNoCr,13,add n at position 0,flow_matching,0.3,2.0,55,259
106,replace,8.0,O,r,nO=C(N\SrNoCr,nO=C(N\SONoCr,13,replace r at position 8 with O,flow_matching,0.3,2.0,55,259
107,add,1.0,N,,nO=C(N\SONoCr,nNO=C(N\SONoCr,14,add N at position 1,flow_matching,0.3,2.0,55,259
108,replace,13.0,N,r,nNO=C(N\SONoCr,nNO=C(N\SONoCN,14,replace r at position 13 with N,flow_matching,0.3,2.0,55,259
109,replace,13.0,n,N,nNO=C(N\SONoCN,nNO=C(N\SONoCn,14,replace N at position 13 with n,flow_matching,0.3,2.0,55,259
110,replace,0.0,O,n,nNO=C(N\SONoCn,ONO=C(N\SONoCn,14,replace n at position 0 with O,flow_matching,0.3,2.0,55,259
111,remove,12.0,C,,ONO=C(N\SONoCn,ONO=C(N\SONon,13,remove C from position 12,flow_matching,0.3,2.0,55,259
112,replace,1.0,=,N,ONO=C(N\SONon,O=O=C(N\SONon,13,replace N at position 1 with =,flow_matching,0.3,2.0,55,259
113,replace,4.0,),C,O=O=C(N\SONon,O=O=)(N\SONon,13,replace C at position 4 with ),flow_matching,0.3,2.0,55,259
114,replace,2.0,C,O,O=O=)(N\SONon,O=C=)(N\SONon,13,replace O at position 2 with C,flow_matching,0.3,2.0,55,259
115,add,10.0,N,,O=C=)(N\SONon,O=C=)(N\SONNon,14,add N at position 10,flow_matching,0.3,2.0,55,259
116,remove,6.0,N,,O=C=)(N\SONNon,O=C=)(\SONNon,13,remove N from position 6,flow_matching,0.3,2.0,55,259
117,replace,3.0,(,=,O=C=)(\SONNon,O=C()(\SONNon,13,replace = at position 3 with (,flow_matching,0.3,2.0,55,259
118,remove,3.0,(,,O=C()(\SONNon,O=C)(\SONNon,12,remove ( from position 3,flow_matching,0.3,2.0,55,259
119,add,3.0,C,,O=C)(\SONNon,O=CC)(\SONNon,13,add C at position 3,flow_matching,0.3,2.0,55,259
120,remove,7.0,S,,O=CC)(\SONNon,O=CC)(\ONNon,12,remove S from position 7,flow_matching,0.3,2.0,55,259
121,replace,8.0,O,N,O=CC)(\ONNon,O=CC)(\OONon,12,replace N at position 8 with O,flow_matching,0.3,2.0,55,259
122,replace,3.0,(,C,O=CC)(\OONon,O=C()(\OONon,12,replace C at position 3 with (,flow_matching,0.3,2.0,55,259
123,remove,4.0,),,O=C()(\OONon,O=C((\OONon,11,remove ) from position 4,flow_matching,0.3,2.0,55,259
124,remove,7.0,O,,O=C((\OONon,O=C((\ONon,10,remove O from position 7,flow_matching,0.3,2.0,55,259
125,remove,5.0,\,,O=C((\ONon,O=C((ONon,9,remove \ from position 5,flow_matching,0.3,2.0,55,259
126,replace,4.0,C,(,O=C((ONon,O=C(CONon,9,replace ( at position 4 with C,flow_matching,0.3,2.0,55,259
127,add,6.0,1,,O=C(CONon,O=C(CO1Non,10,add 1 at position 6,flow_matching,0.3,2.0,55,259
128,replace,3.0,+,(,O=C(CO1Non,O=C+CO1Non,10,replace ( at position 3 with +,flow_matching,0.3,2.0,55,259
129,add,6.0,5,,O=C+CO1Non,O=C+CO51Non,11,add 5 at position 6,flow_matching,0.3,2.0,55,259
130,add,1.0,N,,O=C+CO51Non,ON=C+CO51Non,12,add N at position 1,flow_matching,0.3,2.0,55,259
131,replace,8.0,n,1,ON=C+CO51Non,ON=C+CO5nNon,12,replace 1 at position 8 with n,flow_matching,0.3,2.0,55,259
132,add,2.0,4,,ON=C+CO5nNon,ON4=C+CO5nNon,13,add 4 at position 2,flow_matching,0.3,2.0,55,259
133,replace,6.0,r,C,ON4=C+CO5nNon,ON4=C+rO5nNon,13,replace C at position 6 with r,flow_matching,0.3,2.0,55,259
134,add,7.0,C,,ON4=C+rO5nNon,ON4=C+rCO5nNon,14,add C at position 7,flow_matching,0.3,2.0,55,259
135,add,5.0,@,,ON4=C+rCO5nNon,ON4=C@+rCO5nNon,15,add @ at position 5,flow_matching,0.3,2.0,55,259
136,replace,0.0,+,O,ON4=C@+rCO5nNon,+N4=C@+rCO5nNon,15,replace O at position 0 with +,flow_matching,0.3,2.0,55,259
137,replace,3.0,c,=,+N4=C@+rCO5nNon,+N4cC@+rCO5nNon,15,replace = at position 3 with c,flow_matching,0.3,2.0,55,259
138,replace,10.0,F,5,+N4cC@+rCO5nNon,+N4cC@+rCOFnNon,15,replace 5 at position 10 with F,flow_matching,0.3,2.0,55,259
139,add,6.0,c,,+N4cC@+rCOFnNon,+N4cC@c+rCOFnNon,16,add c at position 6,flow_matching,0.3,2.0,55,259
140,replace,0.0,O,+,+N4cC@c+rCOFnNon,ON4cC@c+rCOFnNon,16,replace + at position 0 with O,flow_matching,0.3,2.0,55,259
141,add,15.0,3,,ON4cC@c+rCOFnNon,ON4cC@c+rCOFnNo3n,17,add 3 at position 15,flow_matching,0.3,2.0,55,259
142,remove,9.0,C,,ON4cC@c+rCOFnNo3n,ON4cC@c+rOFnNo3n,16,remove C from position 9,flow_matching,0.3,2.0,55,259
143,replace,1.0,=,N,ON4cC@c+rOFnNo3n,O=4cC@c+rOFnNo3n,16,replace N at position 1 with =,flow_matching,0.3,2.0,55,259
144,replace,2.0,C,4,O=4cC@c+rOFnNo3n,O=CcC@c+rOFnNo3n,16,replace 4 at position 2 with C,flow_matching,0.3,2.0,55,259
145,replace,3.0,(,c,O=CcC@c+rOFnNo3n,O=C(C@c+rOFnNo3n,16,replace c at position 3 with (,flow_matching,0.3,2.0,55,259
146,add,12.0,[,,O=C(C@c+rOFnNo3n,O=C(C@c+rOFn[No3n,17,add [ at position 12,flow_matching,0.3,2.0,55,259
147,add,6.0,7,,O=C(C@c+rOFn[No3n,O=C(C@7c+rOFn[No3n,18,add 7 at position 6,flow_matching,0.3,2.0,55,259
148,replace,5.0,C,@,O=C(C@7c+rOFn[No3n,O=C(CC7c+rOFn[No3n,18,replace @ at position 5 with C,flow_matching,0.3,2.0,55,259
149,remove,12.0,n,,O=C(CC7c+rOFn[No3n,O=C(CC7c+rOF[No3n,17,remove n from position 12,flow_matching,0.3,2.0,55,259
150,remove,6.0,7,,O=C(CC7c+rOF[No3n,O=C(CCc+rOF[No3n,16,remove 7 from position 6,flow_matching,0.3,2.0,55,259
151,replace,5.0,r,C,O=C(CCc+rOF[No3n,O=C(Crc+rOF[No3n,16,replace C at position 5 with r,flow_matching,0.3,2.0,55,259
152,add,6.0,2,,O=C(Crc+rOF[No3n,O=C(Cr2c+rOF[No3n,17,add 2 at position 6,flow_matching,0.3,2.0,55,259
153,add,8.0,7,,O=C(Cr2c+rOF[No3n,O=C(Cr2c7+rOF[No3n,18,add 7 at position 8,flow_matching,0.3,2.0,55,259
154,replace,5.0,C,r,O=C(Cr2c7+rOF[No3n,O=C(CC2c7+rOF[No3n,18,replace r at position 5 with C,flow_matching,0.3,2.0,55,259
155,remove,9.0,+,,O=C(CC2c7+rOF[No3n,O=C(CC2c7rOF[No3n,17,remove + from position 9,flow_matching,0.3,2.0,55,259
156,replace,15.0,#,3,O=C(CC2c7rOF[No3n,O=C(CC2c7rOF[No#n,17,replace 3 at position 15 with #,flow_matching,0.3,2.0,55,259
157,replace,6.0,N,2,O=C(CC2c7rOF[No#n,O=C(CCNc7rOF[No#n,17,replace 2 at position 6 with N,flow_matching,0.3,2.0,55,259
158,replace,12.0,(,[,O=C(CCNc7rOF[No#n,O=C(CCNc7rOF(No#n,17,replace [ at position 12 with (,flow_matching,0.3,2.0,55,259
159,add,13.0,F,,O=C(CCNc7rOF(No#n,O=C(CCNc7rOF(FNo#n,18,add F at position 13,flow_matching,0.3,2.0,55,259
160,replace,0.0,H,O,O=C(CCNc7rOF(FNo#n,H=C(CCNc7rOF(FNo#n,18,replace O at position 0 with H,flow_matching,0.3,2.0,55,259
161,add,7.0,s,,H=C(CCNc7rOF(FNo#n,H=C(CCNsc7rOF(FNo#n,19,add s at position 7,flow_matching,0.3,2.0,55,259
162,replace,0.0,O,H,H=C(CCNsc7rOF(FNo#n,O=C(CCNsc7rOF(FNo#n,19,replace H at position 0 with O,flow_matching,0.3,2.0,55,259
163,add,0.0,l,,O=C(CCNsc7rOF(FNo#n,lO=C(CCNsc7rOF(FNo#n,20,add l at position 0,flow_matching,0.3,2.0,55,259
164,remove,7.0,N,,lO=C(CCNsc7rOF(FNo#n,lO=C(CCsc7rOF(FNo#n,19,remove N from position 7,flow_matching,0.3,2.0,55,259
165,replace,11.0,B,O,lO=C(CCsc7rOF(FNo#n,lO=C(CCsc7rBF(FNo#n,19,replace O at position 11 with B,flow_matching,0.3,2.0,55,259
166,replace,14.0,#,F,lO=C(CCsc7rBF(FNo#n,lO=C(CCsc7rBF(#No#n,19,replace F at position 14 with #,flow_matching,0.3,2.0,55,259
167,add,1.0,4,,lO=C(CCsc7rBF(#No#n,l4O=C(CCsc7rBF(#No#n,20,add 4 at position 1,flow_matching,0.3,2.0,55,259
168,remove,1.0,4,,l4O=C(CCsc7rBF(#No#n,lO=C(CCsc7rBF(#No#n,19,remove 4 from position 1,flow_matching,0.3,2.0,55,259
169,replace,0.0,O,l,lO=C(CCsc7rBF(#No#n,OO=C(CCsc7rBF(#No#n,19,replace l at position 0 with O,flow_matching,0.3,2.0,55,259
170,replace,1.0,=,O,OO=C(CCsc7rBF(#No#n,O==C(CCsc7rBF(#No#n,19,replace O at position 1 with =,flow_matching,0.3,2.0,55,259
171,add,5.0,3,,O==C(CCsc7rBF(#No#n,O==C(3CCsc7rBF(#No#n,20,add 3 at position 5,flow_matching,0.3,2.0,55,259
172,remove,3.0,C,,O==C(3CCsc7rBF(#No#n,O==(3CCsc7rBF(#No#n,19,remove C from position 3,flow_matching,0.3,2.0,55,259
173,replace,2.0,C,=,O==(3CCsc7rBF(#No#n,O=C(3CCsc7rBF(#No#n,19,replace = at position 2 with C,flow_matching,0.3,2.0,55,259
174,replace,4.0,C,3,O=C(3CCsc7rBF(#No#n,O=C(CCCsc7rBF(#No#n,19,replace 3 at position 4 with C,flow_matching,0.3,2.0,55,259
175,replace,17.0,=,#,O=C(CCCsc7rBF(#No#n,O=C(CCCsc7rBF(#No=n,19,replace # at position 17 with =,flow_matching,0.3,2.0,55,259
176,replace,6.0,N,C,O=C(CCCsc7rBF(#No=n,O=C(CCNsc7rBF(#No=n,19,replace C at position 6 with N,flow_matching,0.3,2.0,55,259
177,remove,11.0,B,,O=C(CCNsc7rBF(#No=n,O=C(CCNsc7rF(#No=n,18,remove B from position 11,flow_matching,0.3,2.0,55,259
178,replace,7.0,c,s,O=C(CCNsc7rF(#No=n,O=C(CCNcc7rF(#No=n,18,replace s at position 7 with c,flow_matching,0.3,2.0,55,259
179,replace,8.0,1,c,O=C(CCNcc7rF(#No=n,O=C(CCNc17rF(#No=n,18,replace c at position 8 with 1,flow_matching,0.3,2.0,55,259
180,add,2.0,\,,O=C(CCNc17rF(#No=n,O=\C(CCNc17rF(#No=n,19,add \ at position 2,flow_matching,0.3,2.0,55,259
181,replace,17.0,H,=,O=\C(CCNc17rF(#No=n,O=\C(CCNc17rF(#NoHn,19,replace = at position 17 with H,flow_matching,0.3,2.0,55,259
182,add,6.0,(,,O=\C(CCNc17rF(#NoHn,O=\C(C(CNc17rF(#NoHn,20,add ( at position 6,flow_matching,0.3,2.0,55,259
183,replace,2.0,C,\,O=\C(C(CNc17rF(#NoHn,O=CC(C(CNc17rF(#NoHn,20,replace \ at position 2 with C,flow_matching,0.3,2.0,55,259
184,replace,3.0,(,C,O=CC(C(CNc17rF(#NoHn,O=C((C(CNc17rF(#NoHn,20,replace C at position 3 with (,flow_matching,0.3,2.0,55,259
185,replace,18.0,+,H,O=C((C(CNc17rF(#NoHn,O=C((C(CNc17rF(#No+n,20,replace H at position 18 with +,flow_matching,0.3,2.0,55,259
186,replace,4.0,C,(,O=C((C(CNc17rF(#No+n,O=C(CC(CNc17rF(#No+n,20,replace ( at position 4 with C,flow_matching,0.3,2.0,55,259
187,replace,7.0,-,C,O=C(CC(CNc17rF(#No+n,O=C(CC(-Nc17rF(#No+n,20,replace C at position 7 with -,flow_matching,0.3,2.0,55,259
188,replace,6.0,N,(,O=C(CC(-Nc17rF(#No+n,O=C(CCN-Nc17rF(#No+n,20,replace ( at position 6 with N,flow_matching,0.3,2.0,55,259
189,replace,7.0,c,-,O=C(CCN-Nc17rF(#No+n,O=C(CCNcNc17rF(#No+n,20,replace - at position 7 with c,flow_matching,0.3,2.0,55,259
190,add,13.0,],,O=C(CCNcNc17rF(#No+n,O=C(CCNcNc17r]F(#No+n,21,add ] at position 13,flow_matching,0.3,2.0,55,259
191,add,1.0,B,,O=C(CCNcNc17r]F(#No+n,OB=C(CCNcNc17r]F(#No+n,22,add B at position 1,flow_matching,0.3,2.0,55,259
192,add,10.0,/,,OB=C(CCNcNc17r]F(#No+n,OB=C(CCNcN/c17r]F(#No+n,23,add / at position 10,flow_matching,0.3,2.0,55,259
193,replace,4.0,l,(,OB=C(CCNcN/c17r]F(#No+n,OB=ClCCNcN/c17r]F(#No+n,23,replace ( at position 4 with l,flow_matching,0.3,2.0,55,259
194,remove,5.0,C,,OB=ClCCNcN/c17r]F(#No+n,OB=ClCNcN/c17r]F(#No+n,22,remove C from position 5,flow_matching,0.3,2.0,55,259
195,add,15.0,#,,OB=ClCNcN/c17r]F(#No+n,OB=ClCNcN/c17r]#F(#No+n,23,add # at position 15,flow_matching,0.3,2.0,55,259
196,replace,19.0,I,N,OB=ClCNcN/c17r]#F(#No+n,OB=ClCNcN/c17r]#F(#Io+n,23,replace N at position 19 with I,flow_matching,0.3,2.0,55,259
197,add,19.0,#,,OB=ClCNcN/c17r]#F(#Io+n,OB=ClCNcN/c17r]#F(##Io+n,24,add # at position 19,flow_matching,0.3,2.0,55,259
198,add,22.0,3,,OB=ClCNcN/c17r]#F(##Io+n,OB=ClCNcN/c17r]#F(##Io3+n,25,add 3 at position 22,flow_matching,0.3,2.0,55,259
199,remove,9.0,/,,OB=ClCNcN/c17r]#F(##Io3+n,OB=ClCNcNc17r]#F(##Io3+n,24,remove / from position 9,flow_matching,0.3,2.0,55,259
200,remove,6.0,N,,OB=ClCNcNc17r]#F(##Io3+n,OB=ClCcNc17r]#F(##Io3+n,23,remove N from position 6,flow_matching,0.3,2.0,55,259
201,replace,16.0,),#,OB=ClCcNc17r]#F(##Io3+n,OB=ClCcNc17r]#F()#Io3+n,23,replace # at position 16 with ),flow_matching,0.3,2.0,55,259
202,replace,3.0,(,C,OB=ClCcNc17r]#F()#Io3+n,OB=(lCcNc17r]#F()#Io3+n,23,replace C at position 3 with (,flow_matching,0.3,2.0,55,259
203,replace,1.0,=,B,OB=(lCcNc17r]#F()#Io3+n,O==(lCcNc17r]#F()#Io3+n,23,replace B at position 1 with =,flow_matching,0.3,2.0,55,259
204,remove,8.0,c,,O==(lCcNc17r]#F()#Io3+n,O==(lCcN17r]#F()#Io3+n,22,remove c from position 8,flow_matching,0.3,2.0,55,259
205,replace,8.0,l,1,O==(lCcN17r]#F()#Io3+n,O==(lCcNl7r]#F()#Io3+n,22,replace 1 at position 8 with l,flow_matching,0.3,2.0,55,259
206,replace,20.0,[,+,O==(lCcNl7r]#F()#Io3+n,O==(lCcNl7r]#F()#Io3[n,22,replace + at position 20 with [,flow_matching,0.3,2.0,55,259
207,add,19.0,),,O==(lCcNl7r]#F()#Io3[n,O==(lCcNl7r]#F()#Io)3[n,23,add ) at position 19,flow_matching,0.3,2.0,55,259
208,add,15.0,F,,O==(lCcNl7r]#F()#Io)3[n,O==(lCcNl7r]#F(F)#Io)3[n,24,add F at position 15,flow_matching,0.3,2.0,55,259
209,replace,2.0,C,=,O==(lCcNl7r]#F(F)#Io)3[n,O=C(lCcNl7r]#F(F)#Io)3[n,24,replace = at position 2 with C,flow_matching,0.3,2.0,55,259
210,replace,4.0,C,l,O=C(lCcNl7r]#F(F)#Io)3[n,O=C(CCcNl7r]#F(F)#Io)3[n,24,replace l at position 4 with C,flow_matching,0.3,2.0,55,259
211,replace,6.0,N,c,O=C(CCcNl7r]#F(F)#Io)3[n,O=C(CCNNl7r]#F(F)#Io)3[n,24,replace c at position 6 with N,flow_matching,0.3,2.0,55,259
212,replace,7.0,c,N,O=C(CCNNl7r]#F(F)#Io)3[n,O=C(CCNcl7r]#F(F)#Io)3[n,24,replace N at position 7 with c,flow_matching,0.3,2.0,55,259
213,replace,8.0,1,l,O=C(CCNcl7r]#F(F)#Io)3[n,O=C(CCNc17r]#F(F)#Io)3[n,24,replace l at position 8 with 1,flow_matching,0.3,2.0,55,259
214,replace,9.0,c,7,O=C(CCNc17r]#F(F)#Io)3[n,O=C(CCNc1cr]#F(F)#Io)3[n,24,replace 7 at position 9 with c,flow_matching,0.3,2.0,55,259
215,replace,10.0,c,r,O=C(CCNc1cr]#F(F)#Io)3[n,O=C(CCNc1cc]#F(F)#Io)3[n,24,replace r at position 10 with c,flow_matching,0.3,2.0,55,259
216,replace,11.0,c,],O=C(CCNc1cc]#F(F)#Io)3[n,O=C(CCNc1ccc#F(F)#Io)3[n,24,replace ] at position 11 with c,flow_matching,0.3,2.0,55,259
217,replace,12.0,c,#,O=C(CCNc1ccc#F(F)#Io)3[n,O=C(CCNc1ccccF(F)#Io)3[n,24,replace # at position 12 with c,flow_matching,0.3,2.0,55,259
218,replace,13.0,c,F,O=C(CCNc1ccccF(F)#Io)3[n,O=C(CCNc1ccccc(F)#Io)3[n,24,replace F at position 13 with c,flow_matching,0.3,2.0,55,259
219,replace,14.0,1,(,O=C(CCNc1ccccc(F)#Io)3[n,O=C(CCNc1ccccc1F)#Io)3[n,24,replace ( at position 14 with 1,flow_matching,0.3,2.0,55,259
220,replace,15.0,[,F,O=C(CCNc1ccccc1F)#Io)3[n,O=C(CCNc1ccccc1[)#Io)3[n,24,replace F at position 15 with [,flow_matching,0.3,2.0,55,259
221,replace,16.0,N,),O=C(CCNc1ccccc1[)#Io)3[n,O=C(CCNc1ccccc1[N#Io)3[n,24,replace ) at position 16 with N,flow_matching,0.3,2.0,55,259
222,replace,17.0,+,#,O=C(CCNc1ccccc1[N#Io)3[n,O=C(CCNc1ccccc1[N+Io)3[n,24,replace # at position 17 with +,flow_matching,0.3,2.0,55,259
223,replace,18.0,],I,O=C(CCNc1ccccc1[N+Io)3[n,O=C(CCNc1ccccc1[N+]o)3[n,24,replace I at position 18 with ],flow_matching,0.3,2.0,55,259
224,replace,19.0,(,o,O=C(CCNc1ccccc1[N+]o)3[n,O=C(CCNc1ccccc1[N+]()3[n,24,replace o at position 19 with (,flow_matching,0.3,2.0,55,259
225,replace,20.0,=,),O=C(CCNc1ccccc1[N+]()3[n,O=C(CCNc1ccccc1[N+](=3[n,24,replace ) at position 20 with =,flow_matching,0.3,2.0,55,259
226,replace,21.0,O,3,O=C(CCNc1ccccc1[N+](=3[n,O=C(CCNc1ccccc1[N+](=O[n,24,replace 3 at position 21 with O,flow_matching,0.3,2.0,55,259
227,replace,22.0,),[,O=C(CCNc1ccccc1[N+](=O[n,O=C(CCNc1ccccc1[N+](=O)n,24,replace [ at position 22 with ),flow_matching,0.3,2.0,55,259
228,replace,23.0,[,n,O=C(CCNc1ccccc1[N+](=O)n,O=C(CCNc1ccccc1[N+](=O)[,24,replace n at position 23 with [,flow_matching,0.3,2.0,55,259
229,add,24.0,O,,O=C(CCNc1ccccc1[N+](=O)[,O=C(CCNc1ccccc1[N+](=O)[O,25,add O at position 24,flow_matching,0.3,2.0,55,259
230,add,25.0,-,,O=C(CCNc1ccccc1[N+](=O)[O,O=C(CCNc1ccccc1[N+](=O)[O-,26,add - at position 25,flow_matching,0.3,2.0,55,259
231,add,26.0,],,O=C(CCNc1ccccc1[N+](=O)[O-,O=C(CCNc1ccccc1[N+](=O)[O-],27,add ] at position 26,flow_matching,0.3,2.0,55,259
232,add,27.0,),,O=C(CCNc1ccccc1[N+](=O)[O-],O=C(CCNc1ccccc1[N+](=O)[O-]),28,add ) at position 27,flow_matching,0.3,2.0,55,259
233,add,28.0,N,,O=C(CCNc1ccccc1[N+](=O)[O-]),O=C(CCNc1ccccc1[N+](=O)[O-])N,29,add N at position 28,flow_matching,0.3,2.0,55,259
234,add,29.0,1,,O=C(CCNc1ccccc1[N+](=O)[O-])N,O=C(CCNc1ccccc1[N+](=O)[O-])N1,30,add 1 at position 29,flow_matching,0.3,2.0,55,259
235,add,30.0,C,,O=C(CCNc1ccccc1[N+](=O)[O-])N1,O=C(CCNc1ccccc1[N+](=O)[O-])N1C,31,add C at position 30,flow_matching,0.3,2.0,55,259
236,add,31.0,C,,O=C(CCNc1ccccc1[N+](=O)[O-])N1C,O=C(CCNc1ccccc1[N+](=O)[O-])N1CC,32,add C at position 31,flow_matching,0.3,2.0,55,259
237,add,32.0,C,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CC,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC,33,add C at position 32,flow_matching,0.3,2.0,55,259
238,add,33.0,[,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[,34,add [ at position 33,flow_matching,0.3,2.0,55,259
239,add,34.0,C,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C,35,add C at position 34,flow_matching,0.3,2.0,55,259
240,add,35.0,@,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@,36,add @ at position 35,flow_matching,0.3,2.0,55,259
241,add,36.0,@,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@,37,add @ at position 36,flow_matching,0.3,2.0,55,259
242,add,37.0,H,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H,38,add H at position 37,flow_matching,0.3,2.0,55,259
243,add,38.0,],,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H],39,add ] at position 38,flow_matching,0.3,2.0,55,259
244,add,39.0,(,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H],O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H](,40,add ( at position 39,flow_matching,0.3,2.0,55,259
245,add,40.0,[,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H](,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([,41,add [ at position 40,flow_matching,0.3,2.0,55,259
246,add,41.0,N,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([N,42,add N at position 41,flow_matching,0.3,2.0,55,259
247,add,42.0,H,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([N,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH,43,add H at position 42,flow_matching,0.3,2.0,55,259
248,add,43.0,+,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+,44,add + at position 43,flow_matching,0.3,2.0,55,259
249,add,44.0,],,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+],45,add ] at position 44,flow_matching,0.3,2.0,55,259
250,add,45.0,2,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+],O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2,46,add 2 at position 45,flow_matching,0.3,2.0,55,259
251,add,46.0,C,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2C,47,add C at position 46,flow_matching,0.3,2.0,55,259
252,add,47.0,C,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2C,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CC,48,add C at position 47,flow_matching,0.3,2.0,55,259
253,add,48.0,C,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CC,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCC,49,add C at position 48,flow_matching,0.3,2.0,55,259
254,add,49.0,C,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCC,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC,50,add C at position 49,flow_matching,0.3,2.0,55,259
255,add,50.0,2,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC2,51,add 2 at position 50,flow_matching,0.3,2.0,55,259
256,add,51.0,),,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC2,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC2),52,add ) at position 51,flow_matching,0.3,2.0,55,259
257,add,52.0,C,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC2),O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC2)C,53,add C at position 52,flow_matching,0.3,2.0,55,259
258,add,53.0,1,,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC2)C,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC2)C1,54,add 1 at position 53,flow_matching,0.3,2.0,55,259
259,add,54.0,"
",,O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC2)C1,"O=C(CCNc1ccccc1[N+](=O)[O-])N1CCC[C@@H]([NH+]2CCCC2)C1
",55,"add 
 at position 54",flow_matching,0.3,2.0,55,259
