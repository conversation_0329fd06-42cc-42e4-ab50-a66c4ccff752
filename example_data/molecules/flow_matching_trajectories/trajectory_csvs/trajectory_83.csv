step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,31,66
1,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,31,66
2,remove,0.0,6,,6,,0,remove 6 from position 0,flow_matching,0.3,2.0,31,66
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,31,66
4,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,31,66
5,replace,1.0,@,C,CC,C@,2,replace C at position 1 with @,flow_matching,0.3,2.0,31,66
6,add,2.0,#,,C@,C@#,3,add # at position 2,flow_matching,0.3,2.0,31,66
7,remove,1.0,@,,C@#,C#,2,remove @ from position 1,flow_matching,0.3,2.0,31,66
8,replace,0.0,],<PERSON>,<PERSON>#,]#,2,replace C at position 0 with ],flow_matching,0.3,2.0,31,66
9,add,2.0,[,,]#,]#[,3,add [ at position 2,flow_matching,0.3,2.0,31,66
10,replace,0.0,C,],]#[,C#[,3,replace ] at position 0 with C,flow_matching,0.3,2.0,31,66
11,remove,0.0,C,,C#[,#[,2,remove C from position 0,flow_matching,0.3,2.0,31,66
12,add,0.0,s,,#[,s#[,3,add s at position 0,flow_matching,0.3,2.0,31,66
13,replace,0.0,C,s,s#[,C#[,3,replace s at position 0 with C,flow_matching,0.3,2.0,31,66
14,replace,1.0,C,#,C#[,CC[,3,replace # at position 1 with C,flow_matching,0.3,2.0,31,66
15,remove,2.0,[,,CC[,CC,2,remove [ from position 2,flow_matching,0.3,2.0,31,66
16,add,2.0,[,,CC,CC[,3,add [ at position 2,flow_matching,0.3,2.0,31,66
17,add,3.0,C,,CC[,CC[C,4,add C at position 3,flow_matching,0.3,2.0,31,66
18,replace,1.0,(,C,CC[C,C([C,4,replace C at position 1 with (,flow_matching,0.3,2.0,31,66
19,replace,1.0,C,(,C([C,CC[C,4,replace ( at position 1 with C,flow_matching,0.3,2.0,31,66
20,add,2.0,O,,CC[C,CCO[C,5,add O at position 2,flow_matching,0.3,2.0,31,66
21,add,3.0,o,,CCO[C,CCOo[C,6,add o at position 3,flow_matching,0.3,2.0,31,66
22,replace,2.0,[,O,CCOo[C,CC[o[C,6,replace O at position 2 with [,flow_matching,0.3,2.0,31,66
23,add,0.0,N,,CC[o[C,NCC[o[C,7,add N at position 0,flow_matching,0.3,2.0,31,66
24,remove,1.0,C,,NCC[o[C,NC[o[C,6,remove C from position 1,flow_matching,0.3,2.0,31,66
25,add,6.0,C,,NC[o[C,NC[o[CC,7,add C at position 6,flow_matching,0.3,2.0,31,66
26,remove,4.0,[,,NC[o[CC,NC[oCC,6,remove [ from position 4,flow_matching,0.3,2.0,31,66
27,replace,5.0,#,C,NC[oCC,NC[oC#,6,replace C at position 5 with #,flow_matching,0.3,2.0,31,66
28,replace,0.0,C,N,NC[oC#,CC[oC#,6,replace N at position 0 with C,flow_matching,0.3,2.0,31,66
29,replace,3.0,C,o,CC[oC#,CC[CC#,6,replace o at position 3 with C,flow_matching,0.3,2.0,31,66
30,replace,5.0,s,#,CC[CC#,CC[CCs,6,replace # at position 5 with s,flow_matching,0.3,2.0,31,66
31,remove,0.0,C,,CC[CCs,C[CCs,5,remove C from position 0,flow_matching,0.3,2.0,31,66
32,replace,2.0,H,C,C[CCs,C[HCs,5,replace C at position 2 with H,flow_matching,0.3,2.0,31,66
33,add,4.0,4,,C[HCs,C[HC4s,6,add 4 at position 4,flow_matching,0.3,2.0,31,66
34,remove,0.0,C,,C[HC4s,[HC4s,5,remove C from position 0,flow_matching,0.3,2.0,31,66
35,add,2.0,B,,[HC4s,[HBC4s,6,add B at position 2,flow_matching,0.3,2.0,31,66
36,replace,0.0,C,[,[HBC4s,CHBC4s,6,replace [ at position 0 with C,flow_matching,0.3,2.0,31,66
37,replace,1.0,C,H,CHBC4s,CCBC4s,6,replace H at position 1 with C,flow_matching,0.3,2.0,31,66
38,replace,2.0,s,B,CCBC4s,CCsC4s,6,replace B at position 2 with s,flow_matching,0.3,2.0,31,66
39,replace,2.0,[,s,CCsC4s,CC[C4s,6,replace s at position 2 with [,flow_matching,0.3,2.0,31,66
40,replace,4.0,@,4,CC[C4s,CC[C@s,6,replace 4 at position 4 with @,flow_matching,0.3,2.0,31,66
41,replace,5.0,@,s,CC[C@s,CC[C@@,6,replace s at position 5 with @,flow_matching,0.3,2.0,31,66
42,add,6.0,H,,CC[C@@,CC[C@@H,7,add H at position 6,flow_matching,0.3,2.0,31,66
43,add,7.0,],,CC[C@@H,CC[C@@H],8,add ] at position 7,flow_matching,0.3,2.0,31,66
44,add,8.0,(,,CC[C@@H],CC[C@@H](,9,add ( at position 8,flow_matching,0.3,2.0,31,66
45,add,9.0,C,,CC[C@@H](,CC[C@@H](C,10,add C at position 9,flow_matching,0.3,2.0,31,66
46,add,10.0,),,CC[C@@H](C,CC[C@@H](C),11,add ) at position 10,flow_matching,0.3,2.0,31,66
47,add,11.0,C,,CC[C@@H](C),CC[C@@H](C)C,12,add C at position 11,flow_matching,0.3,2.0,31,66
48,add,12.0,N,,CC[C@@H](C)C,CC[C@@H](C)CN,13,add N at position 12,flow_matching,0.3,2.0,31,66
49,add,13.0,c,,CC[C@@H](C)CN,CC[C@@H](C)CNc,14,add c at position 13,flow_matching,0.3,2.0,31,66
50,add,14.0,1,,CC[C@@H](C)CNc,CC[C@@H](C)CNc1,15,add 1 at position 14,flow_matching,0.3,2.0,31,66
51,add,15.0,n,,CC[C@@H](C)CNc1,CC[C@@H](C)CNc1n,16,add n at position 15,flow_matching,0.3,2.0,31,66
52,add,16.0,c,,CC[C@@H](C)CNc1n,CC[C@@H](C)CNc1nc,17,add c at position 16,flow_matching,0.3,2.0,31,66
53,add,17.0,2,,CC[C@@H](C)CNc1nc,CC[C@@H](C)CNc1nc2,18,add 2 at position 17,flow_matching,0.3,2.0,31,66
54,add,18.0,c,,CC[C@@H](C)CNc1nc2,CC[C@@H](C)CNc1nc2c,19,add c at position 18,flow_matching,0.3,2.0,31,66
55,add,19.0,c,,CC[C@@H](C)CNc1nc2c,CC[C@@H](C)CNc1nc2cc,20,add c at position 19,flow_matching,0.3,2.0,31,66
56,add,20.0,c,,CC[C@@H](C)CNc1nc2cc,CC[C@@H](C)CNc1nc2ccc,21,add c at position 20,flow_matching,0.3,2.0,31,66
57,add,21.0,(,,CC[C@@H](C)CNc1nc2ccc,CC[C@@H](C)CNc1nc2ccc(,22,add ( at position 21,flow_matching,0.3,2.0,31,66
58,add,22.0,C,,CC[C@@H](C)CNc1nc2ccc(,CC[C@@H](C)CNc1nc2ccc(C,23,add C at position 22,flow_matching,0.3,2.0,31,66
59,add,23.0,l,,CC[C@@H](C)CNc1nc2ccc(C,CC[C@@H](C)CNc1nc2ccc(Cl,24,add l at position 23,flow_matching,0.3,2.0,31,66
60,add,24.0,),,CC[C@@H](C)CNc1nc2ccc(Cl,CC[C@@H](C)CNc1nc2ccc(Cl),25,add ) at position 24,flow_matching,0.3,2.0,31,66
61,add,25.0,c,,CC[C@@H](C)CNc1nc2ccc(Cl),CC[C@@H](C)CNc1nc2ccc(Cl)c,26,add c at position 25,flow_matching,0.3,2.0,31,66
62,add,26.0,c,,CC[C@@H](C)CNc1nc2ccc(Cl)c,CC[C@@H](C)CNc1nc2ccc(Cl)cc,27,add c at position 26,flow_matching,0.3,2.0,31,66
63,add,27.0,2,,CC[C@@H](C)CNc1nc2ccc(Cl)cc,CC[C@@H](C)CNc1nc2ccc(Cl)cc2,28,add 2 at position 27,flow_matching,0.3,2.0,31,66
64,add,28.0,s,,CC[C@@H](C)CNc1nc2ccc(Cl)cc2,CC[C@@H](C)CNc1nc2ccc(Cl)cc2s,29,add s at position 28,flow_matching,0.3,2.0,31,66
65,add,29.0,1,,CC[C@@H](C)CNc1nc2ccc(Cl)cc2s,CC[C@@H](C)CNc1nc2ccc(Cl)cc2s1,30,add 1 at position 29,flow_matching,0.3,2.0,31,66
66,add,30.0,"
",,CC[C@@H](C)CNc1nc2ccc(Cl)cc2s1,"CC[C@@H](C)CNc1nc2ccc(Cl)cc2s1
",31,"add 
 at position 30",flow_matching,0.3,2.0,31,66
