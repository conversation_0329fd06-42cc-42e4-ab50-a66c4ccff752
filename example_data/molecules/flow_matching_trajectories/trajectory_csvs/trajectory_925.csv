step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,48,141
1,add,0.0,2,,,2,1,add 2 at position 0,flow_matching,0.3,2.0,48,141
2,add,1.0,),,2,2),2,add ) at position 1,flow_matching,0.3,2.0,48,141
3,remove,0.0,2,,2),),1,remove 2 from position 0,flow_matching,0.3,2.0,48,141
4,replace,0.0,c,),),c,1,replace ) at position 0 with c,flow_matching,0.3,2.0,48,141
5,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,48,141
6,add,1.0,<PERSON>,,<PERSON>,<PERSON>,2,add <PERSON> at position 1,flow_matching,0.3,2.0,48,141
7,remove,0.0,C,,CC,C,1,remove C from position 0,flow_matching,0.3,2.0,48,141
8,add,0.0,4,,C,4C,2,add 4 at position 0,flow_matching,0.3,2.0,48,141
9,replace,0.0,n,4,4C,nC,2,replace 4 at position 0 with n,flow_matching,0.3,2.0,48,141
10,remove,1.0,C,,nC,n,1,remove C from position 1,flow_matching,0.3,2.0,48,141
11,add,1.0,r,,n,nr,2,add r at position 1,flow_matching,0.3,2.0,48,141
12,add,2.0,4,,nr,nr4,3,add 4 at position 2,flow_matching,0.3,2.0,48,141
13,replace,1.0,/,r,nr4,n/4,3,replace r at position 1 with /,flow_matching,0.3,2.0,48,141
14,replace,0.0,C,n,n/4,C/4,3,replace n at position 0 with C,flow_matching,0.3,2.0,48,141
15,remove,2.0,4,,C/4,C/,2,remove 4 from position 2,flow_matching,0.3,2.0,48,141
16,replace,0.0,5,C,C/,5/,2,replace C at position 0 with 5,flow_matching,0.3,2.0,48,141
17,remove,1.0,/,,5/,5,1,remove / from position 1,flow_matching,0.3,2.0,48,141
18,add,0.0,+,,5,+5,2,add + at position 0,flow_matching,0.3,2.0,48,141
19,remove,1.0,5,,+5,+,1,remove 5 from position 1,flow_matching,0.3,2.0,48,141
20,replace,0.0,C,+,+,C,1,replace + at position 0 with C,flow_matching,0.3,2.0,48,141
21,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,48,141
22,replace,1.0,r,C,CC,Cr,2,replace C at position 1 with r,flow_matching,0.3,2.0,48,141
23,replace,1.0,C,r,Cr,CC,2,replace r at position 1 with C,flow_matching,0.3,2.0,48,141
24,add,0.0,o,,CC,oCC,3,add o at position 0,flow_matching,0.3,2.0,48,141
25,add,0.0,H,,oCC,HoCC,4,add H at position 0,flow_matching,0.3,2.0,48,141
26,remove,1.0,o,,HoCC,HCC,3,remove o from position 1,flow_matching,0.3,2.0,48,141
27,replace,0.0,C,H,HCC,CCC,3,replace H at position 0 with C,flow_matching,0.3,2.0,48,141
28,replace,0.0,\,C,CCC,\CC,3,replace C at position 0 with \,flow_matching,0.3,2.0,48,141
29,replace,0.0,C,\,\CC,CCC,3,replace \ at position 0 with C,flow_matching,0.3,2.0,48,141
30,remove,2.0,C,,CCC,CC,2,remove C from position 2,flow_matching,0.3,2.0,48,141
31,remove,0.0,C,,CC,C,1,remove C from position 0,flow_matching,0.3,2.0,48,141
32,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,48,141
33,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,48,141
34,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,48,141
35,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,48,141
36,replace,1.0,2,C,CC(,C2(,3,replace C at position 1 with 2,flow_matching,0.3,2.0,48,141
37,replace,1.0,C,2,C2(,CC(,3,replace 2 at position 1 with C,flow_matching,0.3,2.0,48,141
38,add,0.0,O,,CC(,OCC(,4,add O at position 0,flow_matching,0.3,2.0,48,141
39,remove,3.0,(,,OCC(,OCC,3,remove ( from position 3,flow_matching,0.3,2.0,48,141
40,replace,0.0,C,O,OCC,CCC,3,replace O at position 0 with C,flow_matching,0.3,2.0,48,141
41,add,2.0,2,,CCC,CC2C,4,add 2 at position 2,flow_matching,0.3,2.0,48,141
42,add,2.0,c,,CC2C,CCc2C,5,add c at position 2,flow_matching,0.3,2.0,48,141
43,remove,1.0,C,,CCc2C,Cc2C,4,remove C from position 1,flow_matching,0.3,2.0,48,141
44,replace,0.0,#,C,Cc2C,#c2C,4,replace C at position 0 with #,flow_matching,0.3,2.0,48,141
45,add,2.0,7,,#c2C,#c72C,5,add 7 at position 2,flow_matching,0.3,2.0,48,141
46,add,4.0,C,,#c72C,#c72CC,6,add C at position 4,flow_matching,0.3,2.0,48,141
47,replace,0.0,C,#,#c72CC,Cc72CC,6,replace # at position 0 with C,flow_matching,0.3,2.0,48,141
48,add,0.0,+,,Cc72CC,+Cc72CC,7,add + at position 0,flow_matching,0.3,2.0,48,141
49,replace,6.0,I,C,+Cc72CC,+Cc72CI,7,replace C at position 6 with I,flow_matching,0.3,2.0,48,141
50,add,2.0,=,,+Cc72CI,+C=c72CI,8,add = at position 2,flow_matching,0.3,2.0,48,141
51,replace,0.0,C,+,+C=c72CI,CC=c72CI,8,replace + at position 0 with C,flow_matching,0.3,2.0,48,141
52,replace,2.0,(,=,CC=c72CI,CC(c72CI,8,replace = at position 2 with (,flow_matching,0.3,2.0,48,141
53,replace,0.0,4,C,CC(c72CI,4C(c72CI,8,replace C at position 0 with 4,flow_matching,0.3,2.0,48,141
54,remove,1.0,C,,4C(c72CI,4(c72CI,7,remove C from position 1,flow_matching,0.3,2.0,48,141
55,add,2.0,(,,4(c72CI,4((c72CI,8,add ( at position 2,flow_matching,0.3,2.0,48,141
56,remove,3.0,c,,4((c72CI,4((72CI,7,remove c from position 3,flow_matching,0.3,2.0,48,141
57,add,4.0,6,,4((72CI,4((762CI,8,add 6 at position 4,flow_matching,0.3,2.0,48,141
58,add,3.0,@,,4((762CI,4((@762CI,9,add @ at position 3,flow_matching,0.3,2.0,48,141
59,remove,1.0,(,,4((@762CI,4(@762CI,8,remove ( from position 1,flow_matching,0.3,2.0,48,141
60,replace,5.0,S,2,4(@762CI,4(@76SCI,8,replace 2 at position 5 with S,flow_matching,0.3,2.0,48,141
61,add,3.0,-,,4(@76SCI,4(@-76SCI,9,add - at position 3,flow_matching,0.3,2.0,48,141
62,remove,3.0,-,,4(@-76SCI,4(@76SCI,8,remove - from position 3,flow_matching,0.3,2.0,48,141
63,replace,0.0,C,4,4(@76SCI,C(@76SCI,8,replace 4 at position 0 with C,flow_matching,0.3,2.0,48,141
64,add,5.0,(,,C(@76SCI,C(@76(SCI,9,add ( at position 5,flow_matching,0.3,2.0,48,141
65,remove,0.0,C,,C(@76(SCI,(@76(SCI,8,remove C from position 0,flow_matching,0.3,2.0,48,141
66,replace,0.0,C,(,(@76(SCI,C@76(SCI,8,replace ( at position 0 with C,flow_matching,0.3,2.0,48,141
67,remove,7.0,I,,C@76(SCI,C@76(SC,7,remove I from position 7,flow_matching,0.3,2.0,48,141
68,remove,5.0,S,,C@76(SC,C@76(C,6,remove S from position 5,flow_matching,0.3,2.0,48,141
69,replace,0.0,6,C,C@76(C,6@76(C,6,replace C at position 0 with 6,flow_matching,0.3,2.0,48,141
70,replace,2.0,=,7,6@76(C,6@=6(C,6,replace 7 at position 2 with =,flow_matching,0.3,2.0,48,141
71,remove,5.0,C,,6@=6(C,6@=6(,5,remove C from position 5,flow_matching,0.3,2.0,48,141
72,replace,0.0,C,6,6@=6(,C@=6(,5,replace 6 at position 0 with C,flow_matching,0.3,2.0,48,141
73,replace,1.0,C,@,C@=6(,CC=6(,5,replace @ at position 1 with C,flow_matching,0.3,2.0,48,141
74,add,2.0,7,,CC=6(,CC7=6(,6,add 7 at position 2,flow_matching,0.3,2.0,48,141
75,remove,2.0,7,,CC7=6(,CC=6(,5,remove 7 from position 2,flow_matching,0.3,2.0,48,141
76,replace,2.0,\,=,CC=6(,CC\6(,5,replace = at position 2 with \,flow_matching,0.3,2.0,48,141
77,remove,0.0,C,,CC\6(,C\6(,4,remove C from position 0,flow_matching,0.3,2.0,48,141
78,add,1.0,2,,C\6(,C2\6(,5,add 2 at position 1,flow_matching,0.3,2.0,48,141
79,replace,3.0,s,6,C2\6(,C2\s(,5,replace 6 at position 3 with s,flow_matching,0.3,2.0,48,141
80,replace,0.0,/,C,C2\s(,/2\s(,5,replace C at position 0 with /,flow_matching,0.3,2.0,48,141
81,remove,2.0,\,,/2\s(,/2s(,4,remove \ from position 2,flow_matching,0.3,2.0,48,141
82,add,3.0,1,,/2s(,/2s1(,5,add 1 at position 3,flow_matching,0.3,2.0,48,141
83,remove,1.0,2,,/2s1(,/s1(,4,remove 2 from position 1,flow_matching,0.3,2.0,48,141
84,replace,0.0,C,/,/s1(,Cs1(,4,replace / at position 0 with C,flow_matching,0.3,2.0,48,141
85,replace,1.0,S,s,Cs1(,CS1(,4,replace s at position 1 with S,flow_matching,0.3,2.0,48,141
86,add,2.0,r,,CS1(,CSr1(,5,add r at position 2,flow_matching,0.3,2.0,48,141
87,remove,0.0,C,,CSr1(,Sr1(,4,remove C from position 0,flow_matching,0.3,2.0,48,141
88,replace,0.0,C,S,Sr1(,Cr1(,4,replace S at position 0 with C,flow_matching,0.3,2.0,48,141
89,replace,1.0,C,r,Cr1(,CC1(,4,replace r at position 1 with C,flow_matching,0.3,2.0,48,141
90,replace,2.0,(,1,CC1(,CC((,4,replace 1 at position 2 with (,flow_matching,0.3,2.0,48,141
91,replace,3.0,=,(,CC((,CC(=,4,replace ( at position 3 with =,flow_matching,0.3,2.0,48,141
92,add,1.0,l,,CC(=,ClC(=,5,add l at position 1,flow_matching,0.3,2.0,48,141
93,replace,0.0,B,C,ClC(=,BlC(=,5,replace C at position 0 with B,flow_matching,0.3,2.0,48,141
94,replace,0.0,C,B,BlC(=,ClC(=,5,replace B at position 0 with C,flow_matching,0.3,2.0,48,141
95,replace,1.0,C,l,ClC(=,CCC(=,5,replace l at position 1 with C,flow_matching,0.3,2.0,48,141
96,replace,2.0,(,C,CCC(=,CC((=,5,replace C at position 2 with (,flow_matching,0.3,2.0,48,141
97,replace,3.0,=,(,CC((=,CC(==,5,replace ( at position 3 with =,flow_matching,0.3,2.0,48,141
98,replace,4.0,O,=,CC(==,CC(=O,5,replace = at position 4 with O,flow_matching,0.3,2.0,48,141
99,add,5.0,),,CC(=O,CC(=O),6,add ) at position 5,flow_matching,0.3,2.0,48,141
100,add,6.0,C,,CC(=O),CC(=O)C,7,add C at position 6,flow_matching,0.3,2.0,48,141
101,add,7.0,[,,CC(=O)C,CC(=O)C[,8,add [ at position 7,flow_matching,0.3,2.0,48,141
102,add,8.0,C,,CC(=O)C[,CC(=O)C[C,9,add C at position 8,flow_matching,0.3,2.0,48,141
103,add,9.0,@,,CC(=O)C[C,CC(=O)C[C@,10,add @ at position 9,flow_matching,0.3,2.0,48,141
104,add,10.0,],,CC(=O)C[C@,CC(=O)C[C@],11,add ] at position 10,flow_matching,0.3,2.0,48,141
105,add,11.0,1,,CC(=O)C[C@],CC(=O)C[C@]1,12,add 1 at position 11,flow_matching,0.3,2.0,48,141
106,add,12.0,(,,CC(=O)C[C@]1,CC(=O)C[C@]1(,13,add ( at position 12,flow_matching,0.3,2.0,48,141
107,add,13.0,O,,CC(=O)C[C@]1(,CC(=O)C[C@]1(O,14,add O at position 13,flow_matching,0.3,2.0,48,141
108,add,14.0,),,CC(=O)C[C@]1(O,CC(=O)C[C@]1(O),15,add ) at position 14,flow_matching,0.3,2.0,48,141
109,add,15.0,C,,CC(=O)C[C@]1(O),CC(=O)C[C@]1(O)C,16,add C at position 15,flow_matching,0.3,2.0,48,141
110,add,16.0,(,,CC(=O)C[C@]1(O)C,CC(=O)C[C@]1(O)C(,17,add ( at position 16,flow_matching,0.3,2.0,48,141
111,add,17.0,=,,CC(=O)C[C@]1(O)C(,CC(=O)C[C@]1(O)C(=,18,add = at position 17,flow_matching,0.3,2.0,48,141
112,add,18.0,O,,CC(=O)C[C@]1(O)C(=,CC(=O)C[C@]1(O)C(=O,19,add O at position 18,flow_matching,0.3,2.0,48,141
113,add,19.0,),,CC(=O)C[C@]1(O)C(=O,CC(=O)C[C@]1(O)C(=O),20,add ) at position 19,flow_matching,0.3,2.0,48,141
114,add,20.0,N,,CC(=O)C[C@]1(O)C(=O),CC(=O)C[C@]1(O)C(=O)N,21,add N at position 20,flow_matching,0.3,2.0,48,141
115,add,21.0,(,,CC(=O)C[C@]1(O)C(=O)N,CC(=O)C[C@]1(O)C(=O)N(,22,add ( at position 21,flow_matching,0.3,2.0,48,141
116,add,22.0,C,,CC(=O)C[C@]1(O)C(=O)N(,CC(=O)C[C@]1(O)C(=O)N(C,23,add C at position 22,flow_matching,0.3,2.0,48,141
117,add,23.0,c,,CC(=O)C[C@]1(O)C(=O)N(C,CC(=O)C[C@]1(O)C(=O)N(Cc,24,add c at position 23,flow_matching,0.3,2.0,48,141
118,add,24.0,2,,CC(=O)C[C@]1(O)C(=O)N(Cc,CC(=O)C[C@]1(O)C(=O)N(Cc2,25,add 2 at position 24,flow_matching,0.3,2.0,48,141
119,add,25.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2,CC(=O)C[C@]1(O)C(=O)N(Cc2c,26,add c at position 25,flow_matching,0.3,2.0,48,141
120,add,26.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2c,CC(=O)C[C@]1(O)C(=O)N(Cc2cc,27,add c at position 26,flow_matching,0.3,2.0,48,141
121,add,27.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2cc,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc,28,add c at position 27,flow_matching,0.3,2.0,48,141
122,add,28.0,(,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(,29,add ( at position 28,flow_matching,0.3,2.0,48,141
123,add,29.0,C,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C,30,add C at position 29,flow_matching,0.3,2.0,48,141
124,add,30.0,),,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C),31,add ) at position 30,flow_matching,0.3,2.0,48,141
125,add,31.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C),CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)c,32,add c at position 31,flow_matching,0.3,2.0,48,141
126,add,32.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)c,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc,33,add c at position 32,flow_matching,0.3,2.0,48,141
127,add,33.0,2,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2,34,add 2 at position 33,flow_matching,0.3,2.0,48,141
128,add,34.0,),,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2),35,add ) at position 34,flow_matching,0.3,2.0,48,141
129,add,35.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2),CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c,36,add c at position 35,flow_matching,0.3,2.0,48,141
130,add,36.0,2,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2,37,add 2 at position 36,flow_matching,0.3,2.0,48,141
131,add,37.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c,38,add c at position 37,flow_matching,0.3,2.0,48,141
132,add,38.0,(,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(,39,add ( at position 38,flow_matching,0.3,2.0,48,141
133,add,39.0,C,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C,40,add C at position 39,flow_matching,0.3,2.0,48,141
134,add,40.0,),,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C),41,add ) at position 40,flow_matching,0.3,2.0,48,141
135,add,41.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C),CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)c,42,add c at position 41,flow_matching,0.3,2.0,48,141
136,add,42.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)c,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)cc,43,add c at position 42,flow_matching,0.3,2.0,48,141
137,add,43.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)cc,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)ccc,44,add c at position 43,flow_matching,0.3,2.0,48,141
138,add,44.0,c,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)ccc,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)cccc,45,add c at position 44,flow_matching,0.3,2.0,48,141
139,add,45.0,2,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)cccc,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)cccc2,46,add 2 at position 45,flow_matching,0.3,2.0,48,141
140,add,46.0,1,,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)cccc2,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)cccc21,47,add 1 at position 46,flow_matching,0.3,2.0,48,141
141,add,47.0,"
",,CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)cccc21,"CC(=O)C[C@]1(O)C(=O)N(Cc2ccc(C)cc2)c2c(C)cccc21
",48,"add 
 at position 47",flow_matching,0.3,2.0,48,141
