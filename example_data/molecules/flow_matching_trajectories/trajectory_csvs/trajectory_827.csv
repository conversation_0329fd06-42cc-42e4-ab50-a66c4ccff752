step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,143
1,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,43,143
2,replace,0.0,C,B,B,C,1,replace <PERSON> at position 0 with C,flow_matching,0.3,2.0,43,143
3,replace,0.0,],C,C,],1,replace <PERSON> at position 0 with ],flow_matching,0.3,2.0,43,143
4,add,1.0,O,,],]O,2,add O at position 1,flow_matching,0.3,2.0,43,143
5,add,2.0,l,,]O,]Ol,3,add l at position 2,flow_matching,0.3,2.0,43,143
6,add,0.0,\,,]Ol,\]Ol,4,add \ at position 0,flow_matching,0.3,2.0,43,143
7,replace,0.0,C,\,\]Ol,C]Ol,4,replace \ at position 0 with C,flow_matching,0.3,2.0,43,143
8,replace,1.0,C,],C]Ol,CCOl,4,replace ] at position 1 with C,flow_matching,0.3,2.0,43,143
9,replace,3.0,C,l,CCOl,CCOC,4,replace l at position 3 with C,flow_matching,0.3,2.0,43,143
10,replace,1.0,),C,CCOC,C)OC,4,replace C at position 1 with ),flow_matching,0.3,2.0,43,143
11,replace,1.0,C,),C)OC,CCOC,4,replace ) at position 1 with C,flow_matching,0.3,2.0,43,143
12,add,4.0,(,,CCOC,CCOC(,5,add ( at position 4,flow_matching,0.3,2.0,43,143
13,add,5.0,=,,CCOC(,CCOC(=,6,add = at position 5,flow_matching,0.3,2.0,43,143
14,add,6.0,O,,CCOC(=,CCOC(=O,7,add O at position 6,flow_matching,0.3,2.0,43,143
15,remove,0.0,C,,CCOC(=O,COC(=O,6,remove C from position 0,flow_matching,0.3,2.0,43,143
16,replace,1.0,C,O,COC(=O,CCC(=O,6,replace O at position 1 with C,flow_matching,0.3,2.0,43,143
17,remove,0.0,C,,CCC(=O,CC(=O,5,remove C from position 0,flow_matching,0.3,2.0,43,143
18,remove,0.0,C,,CC(=O,C(=O,4,remove C from position 0,flow_matching,0.3,2.0,43,143
19,remove,3.0,O,,C(=O,C(=,3,remove O from position 3,flow_matching,0.3,2.0,43,143
20,replace,1.0,C,(,C(=,CC=,3,replace ( at position 1 with C,flow_matching,0.3,2.0,43,143
21,replace,0.0,/,C,CC=,/C=,3,replace C at position 0 with /,flow_matching,0.3,2.0,43,143
22,remove,2.0,=,,/C=,/C,2,remove = from position 2,flow_matching,0.3,2.0,43,143
23,remove,0.0,/,,/C,C,1,remove / from position 0,flow_matching,0.3,2.0,43,143
24,add,1.0,S,,C,CS,2,add S at position 1,flow_matching,0.3,2.0,43,143
25,remove,1.0,S,,CS,C,1,remove S from position 1,flow_matching,0.3,2.0,43,143
26,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,43,143
27,add,0.0,),,,),1,add ) at position 0,flow_matching,0.3,2.0,43,143
28,replace,0.0,C,),),C,1,replace ) at position 0 with C,flow_matching,0.3,2.0,43,143
29,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,43,143
30,add,2.0,@,,CC,CC@,3,add @ at position 2,flow_matching,0.3,2.0,43,143
31,add,2.0,],,CC@,CC]@,4,add ] at position 2,flow_matching,0.3,2.0,43,143
32,add,4.0,N,,CC]@,CC]@N,5,add N at position 4,flow_matching,0.3,2.0,43,143
33,add,1.0,c,,CC]@N,CcC]@N,6,add c at position 1,flow_matching,0.3,2.0,43,143
34,replace,1.0,C,c,CcC]@N,CCC]@N,6,replace c at position 1 with C,flow_matching,0.3,2.0,43,143
35,add,4.0,N,,CCC]@N,CCC]N@N,7,add N at position 4,flow_matching,0.3,2.0,43,143
36,replace,3.0,#,],CCC]N@N,CCC#N@N,7,replace ] at position 3 with #,flow_matching,0.3,2.0,43,143
37,add,1.0,O,,CCC#N@N,COCC#N@N,8,add O at position 1,flow_matching,0.3,2.0,43,143
38,replace,0.0,[,C,COCC#N@N,[OCC#N@N,8,replace C at position 0 with [,flow_matching,0.3,2.0,43,143
39,add,1.0,O,,[OCC#N@N,[OOCC#N@N,9,add O at position 1,flow_matching,0.3,2.0,43,143
40,remove,2.0,O,,[OOCC#N@N,[OCC#N@N,8,remove O from position 2,flow_matching,0.3,2.0,43,143
41,add,6.0,o,,[OCC#N@N,[OCC#No@N,9,add o at position 6,flow_matching,0.3,2.0,43,143
42,replace,1.0,c,O,[OCC#No@N,[cCC#No@N,9,replace O at position 1 with c,flow_matching,0.3,2.0,43,143
43,replace,0.0,C,[,[cCC#No@N,CcCC#No@N,9,replace [ at position 0 with C,flow_matching,0.3,2.0,43,143
44,remove,5.0,N,,CcCC#No@N,CcCC#o@N,8,remove N from position 5,flow_matching,0.3,2.0,43,143
45,replace,1.0,C,c,CcCC#o@N,CCCC#o@N,8,replace c at position 1 with C,flow_matching,0.3,2.0,43,143
46,remove,6.0,@,,CCCC#o@N,CCCC#oN,7,remove @ from position 6,flow_matching,0.3,2.0,43,143
47,replace,4.0,H,#,CCCC#oN,CCCCHoN,7,replace # at position 4 with H,flow_matching,0.3,2.0,43,143
48,replace,5.0,3,o,CCCCHoN,CCCCH3N,7,replace o at position 5 with 3,flow_matching,0.3,2.0,43,143
49,remove,0.0,C,,CCCCH3N,CCCH3N,6,remove C from position 0,flow_matching,0.3,2.0,43,143
50,remove,5.0,N,,CCCH3N,CCCH3,5,remove N from position 5,flow_matching,0.3,2.0,43,143
51,add,3.0,r,,CCCH3,CCCrH3,6,add r at position 3,flow_matching,0.3,2.0,43,143
52,add,0.0,#,,CCCrH3,#CCCrH3,7,add # at position 0,flow_matching,0.3,2.0,43,143
53,add,6.0,=,,#CCCrH3,#CCCrH=3,8,add = at position 6,flow_matching,0.3,2.0,43,143
54,remove,0.0,#,,#CCCrH=3,CCCrH=3,7,remove # from position 0,flow_matching,0.3,2.0,43,143
55,replace,2.0,O,C,CCCrH=3,CCOrH=3,7,replace C at position 2 with O,flow_matching,0.3,2.0,43,143
56,replace,2.0,I,O,CCOrH=3,CCIrH=3,7,replace O at position 2 with I,flow_matching,0.3,2.0,43,143
57,remove,0.0,C,,CCIrH=3,CIrH=3,6,remove C from position 0,flow_matching,0.3,2.0,43,143
58,remove,1.0,I,,CIrH=3,CrH=3,5,remove I from position 1,flow_matching,0.3,2.0,43,143
59,replace,1.0,C,r,CrH=3,CCH=3,5,replace r at position 1 with C,flow_matching,0.3,2.0,43,143
60,add,4.0,-,,CCH=3,CCH=-3,6,add - at position 4,flow_matching,0.3,2.0,43,143
61,add,1.0,[,,CCH=-3,C[CH=-3,7,add [ at position 1,flow_matching,0.3,2.0,43,143
62,replace,1.0,C,[,C[CH=-3,CCCH=-3,7,replace [ at position 1 with C,flow_matching,0.3,2.0,43,143
63,replace,2.0,\,C,CCCH=-3,CC\H=-3,7,replace C at position 2 with \,flow_matching,0.3,2.0,43,143
64,replace,2.0,F,\,CC\H=-3,CCFH=-3,7,replace \ at position 2 with F,flow_matching,0.3,2.0,43,143
65,replace,5.0,@,-,CCFH=-3,CCFH=@3,7,replace - at position 5 with @,flow_matching,0.3,2.0,43,143
66,replace,4.0,6,=,CCFH=@3,CCFH6@3,7,replace = at position 4 with 6,flow_matching,0.3,2.0,43,143
67,replace,2.0,O,F,CCFH6@3,CCOH6@3,7,replace F at position 2 with O,flow_matching,0.3,2.0,43,143
68,remove,6.0,3,,CCOH6@3,CCOH6@,6,remove 3 from position 6,flow_matching,0.3,2.0,43,143
69,replace,3.0,C,H,CCOH6@,CCOC6@,6,replace H at position 3 with C,flow_matching,0.3,2.0,43,143
70,add,5.0,#,,CCOC6@,CCOC6#@,7,add # at position 5,flow_matching,0.3,2.0,43,143
71,remove,6.0,@,,CCOC6#@,CCOC6#,6,remove @ from position 6,flow_matching,0.3,2.0,43,143
72,add,5.0,),,CCOC6#,CCOC6)#,7,add ) at position 5,flow_matching,0.3,2.0,43,143
73,replace,5.0,#,),CCOC6)#,CCOC6##,7,replace ) at position 5 with #,flow_matching,0.3,2.0,43,143
74,replace,4.0,(,6,CCOC6##,CCOC(##,7,replace 6 at position 4 with (,flow_matching,0.3,2.0,43,143
75,replace,5.0,=,#,CCOC(##,CCOC(=#,7,replace # at position 5 with =,flow_matching,0.3,2.0,43,143
76,remove,1.0,C,,CCOC(=#,COC(=#,6,remove C from position 1,flow_matching,0.3,2.0,43,143
77,add,0.0,l,,COC(=#,lCOC(=#,7,add l at position 0,flow_matching,0.3,2.0,43,143
78,remove,2.0,O,,lCOC(=#,lCC(=#,6,remove O from position 2,flow_matching,0.3,2.0,43,143
79,replace,0.0,C,l,lCC(=#,CCC(=#,6,replace l at position 0 with C,flow_matching,0.3,2.0,43,143
80,replace,2.0,O,C,CCC(=#,CCO(=#,6,replace C at position 2 with O,flow_matching,0.3,2.0,43,143
81,add,2.0,@,,CCO(=#,CC@O(=#,7,add @ at position 2,flow_matching,0.3,2.0,43,143
82,replace,2.0,O,@,CC@O(=#,CCOO(=#,7,replace @ at position 2 with O,flow_matching,0.3,2.0,43,143
83,remove,5.0,=,,CCOO(=#,CCOO(#,6,remove = from position 5,flow_matching,0.3,2.0,43,143
84,replace,3.0,F,O,CCOO(#,CCOF(#,6,replace O at position 3 with F,flow_matching,0.3,2.0,43,143
85,replace,3.0,r,F,CCOF(#,CCOr(#,6,replace F at position 3 with r,flow_matching,0.3,2.0,43,143
86,replace,3.0,C,r,CCOr(#,CCOC(#,6,replace r at position 3 with C,flow_matching,0.3,2.0,43,143
87,replace,5.0,=,#,CCOC(#,CCOC(=,6,replace # at position 5 with =,flow_matching,0.3,2.0,43,143
88,add,6.0,O,,CCOC(=,CCOC(=O,7,add O at position 6,flow_matching,0.3,2.0,43,143
89,remove,5.0,=,,CCOC(=O,CCOC(O,6,remove = from position 5,flow_matching,0.3,2.0,43,143
90,replace,0.0,H,C,CCOC(O,HCOC(O,6,replace C at position 0 with H,flow_matching,0.3,2.0,43,143
91,add,3.0,3,,HCOC(O,HCO3C(O,7,add 3 at position 3,flow_matching,0.3,2.0,43,143
92,add,7.0,2,,HCO3C(O,HCO3C(O2,8,add 2 at position 7,flow_matching,0.3,2.0,43,143
93,replace,0.0,C,H,HCO3C(O2,CCO3C(O2,8,replace H at position 0 with C,flow_matching,0.3,2.0,43,143
94,replace,5.0,3,(,CCO3C(O2,CCO3C3O2,8,replace ( at position 5 with 3,flow_matching,0.3,2.0,43,143
95,add,0.0,3,,CCO3C3O2,3CCO3C3O2,9,add 3 at position 0,flow_matching,0.3,2.0,43,143
96,replace,2.0,l,C,3CCO3C3O2,3ClO3C3O2,9,replace C at position 2 with l,flow_matching,0.3,2.0,43,143
97,add,8.0,F,,3ClO3C3O2,3ClO3C3OF2,10,add F at position 8,flow_matching,0.3,2.0,43,143
98,replace,0.0,C,3,3ClO3C3OF2,CClO3C3OF2,10,replace 3 at position 0 with C,flow_matching,0.3,2.0,43,143
99,add,1.0,[,,CClO3C3OF2,C[ClO3C3OF2,11,add [ at position 1,flow_matching,0.3,2.0,43,143
100,remove,10.0,2,,C[ClO3C3OF2,C[ClO3C3OF,10,remove 2 from position 10,flow_matching,0.3,2.0,43,143
101,add,1.0,n,,C[ClO3C3OF,Cn[ClO3C3OF,11,add n at position 1,flow_matching,0.3,2.0,43,143
102,add,3.0,/,,Cn[ClO3C3OF,Cn[/ClO3C3OF,12,add / at position 3,flow_matching,0.3,2.0,43,143
103,replace,1.0,H,n,Cn[/ClO3C3OF,CH[/ClO3C3OF,12,replace n at position 1 with H,flow_matching,0.3,2.0,43,143
104,replace,1.0,C,H,CH[/ClO3C3OF,CC[/ClO3C3OF,12,replace H at position 1 with C,flow_matching,0.3,2.0,43,143
105,replace,2.0,O,[,CC[/ClO3C3OF,CCO/ClO3C3OF,12,replace [ at position 2 with O,flow_matching,0.3,2.0,43,143
106,replace,3.0,C,/,CCO/ClO3C3OF,CCOCClO3C3OF,12,replace / at position 3 with C,flow_matching,0.3,2.0,43,143
107,replace,4.0,(,C,CCOCClO3C3OF,CCOC(lO3C3OF,12,replace C at position 4 with (,flow_matching,0.3,2.0,43,143
108,replace,5.0,=,l,CCOC(lO3C3OF,CCOC(=O3C3OF,12,replace l at position 5 with =,flow_matching,0.3,2.0,43,143
109,replace,7.0,),3,CCOC(=O3C3OF,CCOC(=O)C3OF,12,replace 3 at position 7 with ),flow_matching,0.3,2.0,43,143
110,replace,9.0,1,3,CCOC(=O)C3OF,CCOC(=O)C1OF,12,replace 3 at position 9 with 1,flow_matching,0.3,2.0,43,143
111,replace,10.0,C,O,CCOC(=O)C1OF,CCOC(=O)C1CF,12,replace O at position 10 with C,flow_matching,0.3,2.0,43,143
112,replace,11.0,C,F,CCOC(=O)C1CF,CCOC(=O)C1CC,12,replace F at position 11 with C,flow_matching,0.3,2.0,43,143
113,add,12.0,C,,CCOC(=O)C1CC,CCOC(=O)C1CCC,13,add C at position 12,flow_matching,0.3,2.0,43,143
114,add,13.0,(,,CCOC(=O)C1CCC,CCOC(=O)C1CCC(,14,add ( at position 13,flow_matching,0.3,2.0,43,143
115,add,14.0,N,,CCOC(=O)C1CCC(,CCOC(=O)C1CCC(N,15,add N at position 14,flow_matching,0.3,2.0,43,143
116,add,15.0,C,,CCOC(=O)C1CCC(N,CCOC(=O)C1CCC(NC,16,add C at position 15,flow_matching,0.3,2.0,43,143
117,add,16.0,(,,CCOC(=O)C1CCC(NC,CCOC(=O)C1CCC(NC(,17,add ( at position 16,flow_matching,0.3,2.0,43,143
118,add,17.0,=,,CCOC(=O)C1CCC(NC(,CCOC(=O)C1CCC(NC(=,18,add = at position 17,flow_matching,0.3,2.0,43,143
119,add,18.0,O,,CCOC(=O)C1CCC(NC(=,CCOC(=O)C1CCC(NC(=O,19,add O at position 18,flow_matching,0.3,2.0,43,143
120,add,19.0,),,CCOC(=O)C1CCC(NC(=O,CCOC(=O)C1CCC(NC(=O),20,add ) at position 19,flow_matching,0.3,2.0,43,143
121,add,20.0,[,,CCOC(=O)C1CCC(NC(=O),CCOC(=O)C1CCC(NC(=O)[,21,add [ at position 20,flow_matching,0.3,2.0,43,143
122,add,21.0,C,,CCOC(=O)C1CCC(NC(=O)[,CCOC(=O)C1CCC(NC(=O)[C,22,add C at position 21,flow_matching,0.3,2.0,43,143
123,add,22.0,@,,CCOC(=O)C1CCC(NC(=O)[C,CCOC(=O)C1CCC(NC(=O)[C@,23,add @ at position 22,flow_matching,0.3,2.0,43,143
124,add,23.0,@,,CCOC(=O)C1CCC(NC(=O)[C@,CCOC(=O)C1CCC(NC(=O)[C@@,24,add @ at position 23,flow_matching,0.3,2.0,43,143
125,add,24.0,],,CCOC(=O)C1CCC(NC(=O)[C@@,CCOC(=O)C1CCC(NC(=O)[C@@],25,add ] at position 24,flow_matching,0.3,2.0,43,143
126,add,25.0,(,,CCOC(=O)C1CCC(NC(=O)[C@@],CCOC(=O)C1CCC(NC(=O)[C@@](,26,add ( at position 25,flow_matching,0.3,2.0,43,143
127,add,26.0,C,,CCOC(=O)C1CCC(NC(=O)[C@@](,CCOC(=O)C1CCC(NC(=O)[C@@](C,27,add C at position 26,flow_matching,0.3,2.0,43,143
128,add,27.0,),,CCOC(=O)C1CCC(NC(=O)[C@@](C,CCOC(=O)C1CCC(NC(=O)[C@@](C),28,add ) at position 27,flow_matching,0.3,2.0,43,143
129,add,28.0,(,,CCOC(=O)C1CCC(NC(=O)[C@@](C),CCOC(=O)C1CCC(NC(=O)[C@@](C)(,29,add ( at position 28,flow_matching,0.3,2.0,43,143
130,add,29.0,[,,CCOC(=O)C1CCC(NC(=O)[C@@](C)(,CCOC(=O)C1CCC(NC(=O)[C@@](C)([,30,add [ at position 29,flow_matching,0.3,2.0,43,143
131,add,30.0,N,,CCOC(=O)C1CCC(NC(=O)[C@@](C)([,CCOC(=O)C1CCC(NC(=O)[C@@](C)([N,31,add N at position 30,flow_matching,0.3,2.0,43,143
132,add,31.0,H,,CCOC(=O)C1CCC(NC(=O)[C@@](C)([N,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH,32,add H at position 31,flow_matching,0.3,2.0,43,143
133,add,32.0,3,,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3,33,add 3 at position 32,flow_matching,0.3,2.0,43,143
134,add,33.0,+,,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+,34,add + at position 33,flow_matching,0.3,2.0,43,143
135,add,34.0,],,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+],35,add ] at position 34,flow_matching,0.3,2.0,43,143
136,add,35.0,),,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+],CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+]),36,add ) at position 35,flow_matching,0.3,2.0,43,143
137,add,36.0,C,,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+]),CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])C,37,add C at position 36,flow_matching,0.3,2.0,43,143
138,add,37.0,C,,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])C,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC,38,add C at position 37,flow_matching,0.3,2.0,43,143
139,add,38.0,),,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC),39,add ) at position 38,flow_matching,0.3,2.0,43,143
140,add,39.0,C,,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC),CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC)C,40,add C at position 39,flow_matching,0.3,2.0,43,143
141,add,40.0,C,,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC)C,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC)CC,41,add C at position 40,flow_matching,0.3,2.0,43,143
142,add,41.0,1,,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC)CC,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC)CC1,42,add 1 at position 41,flow_matching,0.3,2.0,43,143
143,add,42.0,"
",,CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC)CC1,"CCOC(=O)C1CCC(NC(=O)[C@@](C)([NH3+])CC)CC1
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,143
