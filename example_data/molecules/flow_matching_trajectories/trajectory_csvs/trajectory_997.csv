step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,48,124
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,48,124
2,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,48,124
3,remove,1.0,c,,Cc,C,1,remove c from position 1,flow_matching,0.3,2.0,48,124
4,add,1.0,@,,C,C@,2,add @ at position 1,flow_matching,0.3,2.0,48,124
5,replace,0.0,],C,C@,]@,2,replace C at position 0 with ],flow_matching,0.3,2.0,48,124
6,add,2.0,N,,]@,]@N,3,add N at position 2,flow_matching,0.3,2.0,48,124
7,replace,0.0,C,],]@N,C@N,3,replace ] at position 0 with C,flow_matching,0.3,2.0,48,124
8,replace,1.0,[,@,C@N,C[N,3,replace @ at position 1 with [,flow_matching,0.3,2.0,48,124
9,remove,2.0,N,,C[N,C[,2,remove N from position 2,flow_matching,0.3,2.0,48,124
10,add,2.0,4,,C[,C[4,3,add 4 at position 2,flow_matching,0.3,2.0,48,124
11,add,2.0,4,,C[4,C[44,4,add 4 at position 2,flow_matching,0.3,2.0,48,124
12,replace,1.0,c,[,C[44,Cc44,4,replace [ at position 1 with c,flow_matching,0.3,2.0,48,124
13,add,1.0,[,,Cc44,C[c44,5,add [ at position 1,flow_matching,0.3,2.0,48,124
14,add,5.0,N,,C[c44,C[c44N,6,add N at position 5,flow_matching,0.3,2.0,48,124
15,replace,1.0,c,[,C[c44N,Ccc44N,6,replace [ at position 1 with c,flow_matching,0.3,2.0,48,124
16,replace,4.0,r,4,Ccc44N,Ccc4rN,6,replace 4 at position 4 with r,flow_matching,0.3,2.0,48,124
17,replace,5.0,3,N,Ccc4rN,Ccc4r3,6,replace N at position 5 with 3,flow_matching,0.3,2.0,48,124
18,remove,4.0,r,,Ccc4r3,Ccc43,5,remove r from position 4,flow_matching,0.3,2.0,48,124
19,remove,4.0,3,,Ccc43,Ccc4,4,remove 3 from position 4,flow_matching,0.3,2.0,48,124
20,remove,3.0,4,,Ccc4,Ccc,3,remove 4 from position 3,flow_matching,0.3,2.0,48,124
21,replace,2.0,r,c,Ccc,Ccr,3,replace c at position 2 with r,flow_matching,0.3,2.0,48,124
22,remove,2.0,r,,Ccr,Cc,2,remove r from position 2,flow_matching,0.3,2.0,48,124
23,add,1.0,\,,Cc,C\c,3,add \ at position 1,flow_matching,0.3,2.0,48,124
24,remove,2.0,c,,C\c,C\,2,remove c from position 2,flow_matching,0.3,2.0,48,124
25,replace,1.0,c,\,C\,Cc,2,replace \ at position 1 with c,flow_matching,0.3,2.0,48,124
26,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,48,124
27,remove,1.0,c,,Cc1,C1,2,remove c from position 1,flow_matching,0.3,2.0,48,124
28,remove,0.0,C,,C1,1,1,remove C from position 0,flow_matching,0.3,2.0,48,124
29,add,0.0,=,,1,=1,2,add = at position 0,flow_matching,0.3,2.0,48,124
30,remove,1.0,1,,=1,=,1,remove 1 from position 1,flow_matching,0.3,2.0,48,124
31,replace,0.0,C,=,=,C,1,replace = at position 0 with C,flow_matching,0.3,2.0,48,124
32,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,48,124
33,add,0.0,o,,,o,1,add o at position 0,flow_matching,0.3,2.0,48,124
34,replace,0.0,C,o,o,C,1,replace o at position 0 with C,flow_matching,0.3,2.0,48,124
35,add,0.0,-,,C,-C,2,add - at position 0,flow_matching,0.3,2.0,48,124
36,remove,0.0,-,,-C,C,1,remove - from position 0,flow_matching,0.3,2.0,48,124
37,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,48,124
38,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,48,124
39,replace,0.0,/,@,@,/,1,replace @ at position 0 with /,flow_matching,0.3,2.0,48,124
40,replace,0.0,H,/,/,H,1,replace / at position 0 with H,flow_matching,0.3,2.0,48,124
41,replace,0.0,C,H,H,C,1,replace H at position 0 with C,flow_matching,0.3,2.0,48,124
42,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,48,124
43,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,48,124
44,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,48,124
45,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,48,124
46,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,48,124
47,replace,0.0,I,C,Cc1,Ic1,3,replace C at position 0 with I,flow_matching,0.3,2.0,48,124
48,replace,0.0,c,I,Ic1,cc1,3,replace I at position 0 with c,flow_matching,0.3,2.0,48,124
49,add,1.0,s,,cc1,csc1,4,add s at position 1,flow_matching,0.3,2.0,48,124
50,replace,0.0,C,c,csc1,Csc1,4,replace c at position 0 with C,flow_matching,0.3,2.0,48,124
51,replace,1.0,c,s,Csc1,Ccc1,4,replace s at position 1 with c,flow_matching,0.3,2.0,48,124
52,replace,0.0,2,C,Ccc1,2cc1,4,replace C at position 0 with 2,flow_matching,0.3,2.0,48,124
53,replace,3.0,],1,2cc1,2cc],4,replace 1 at position 3 with ],flow_matching,0.3,2.0,48,124
54,remove,0.0,2,,2cc],cc],3,remove 2 from position 0,flow_matching,0.3,2.0,48,124
55,add,1.0,+,,cc],c+c],4,add + at position 1,flow_matching,0.3,2.0,48,124
56,remove,3.0,],,c+c],c+c,3,remove ] from position 3,flow_matching,0.3,2.0,48,124
57,add,0.0,/,,c+c,/c+c,4,add / at position 0,flow_matching,0.3,2.0,48,124
58,replace,0.0,C,/,/c+c,Cc+c,4,replace / at position 0 with C,flow_matching,0.3,2.0,48,124
59,remove,3.0,c,,Cc+c,Cc+,3,remove c from position 3,flow_matching,0.3,2.0,48,124
60,replace,2.0,c,+,Cc+,Ccc,3,replace + at position 2 with c,flow_matching,0.3,2.0,48,124
61,remove,1.0,c,,Ccc,Cc,2,remove c from position 1,flow_matching,0.3,2.0,48,124
62,remove,1.0,c,,Cc,C,1,remove c from position 1,flow_matching,0.3,2.0,48,124
63,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,48,124
64,replace,1.0,I,c,Cc,CI,2,replace c at position 1 with I,flow_matching,0.3,2.0,48,124
65,remove,0.0,C,,CI,I,1,remove C from position 0,flow_matching,0.3,2.0,48,124
66,remove,0.0,I,,I,,0,remove I from position 0,flow_matching,0.3,2.0,48,124
67,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,48,124
68,add,1.0,H,,C,CH,2,add H at position 1,flow_matching,0.3,2.0,48,124
69,replace,1.0,[,H,CH,C[,2,replace H at position 1 with [,flow_matching,0.3,2.0,48,124
70,remove,0.0,C,,C[,[,1,remove C from position 0,flow_matching,0.3,2.0,48,124
71,add,0.0,C,,[,C[,2,add C at position 0,flow_matching,0.3,2.0,48,124
72,replace,0.0,l,C,C[,l[,2,replace C at position 0 with l,flow_matching,0.3,2.0,48,124
73,add,1.0,c,,l[,lc[,3,add c at position 1,flow_matching,0.3,2.0,48,124
74,replace,0.0,C,l,lc[,Cc[,3,replace l at position 0 with C,flow_matching,0.3,2.0,48,124
75,replace,2.0,S,[,Cc[,CcS,3,replace [ at position 2 with S,flow_matching,0.3,2.0,48,124
76,replace,1.0,[,c,CcS,C[S,3,replace c at position 1 with [,flow_matching,0.3,2.0,48,124
77,add,1.0,o,,C[S,Co[S,4,add o at position 1,flow_matching,0.3,2.0,48,124
78,replace,1.0,c,o,Co[S,Cc[S,4,replace o at position 1 with c,flow_matching,0.3,2.0,48,124
79,replace,2.0,1,[,Cc[S,Cc1S,4,replace [ at position 2 with 1,flow_matching,0.3,2.0,48,124
80,replace,3.0,c,S,Cc1S,Cc1c,4,replace S at position 3 with c,flow_matching,0.3,2.0,48,124
81,add,4.0,c,,Cc1c,Cc1cc,5,add c at position 4,flow_matching,0.3,2.0,48,124
82,add,5.0,c,,Cc1cc,Cc1ccc,6,add c at position 5,flow_matching,0.3,2.0,48,124
83,add,6.0,(,,Cc1ccc,Cc1ccc(,7,add ( at position 6,flow_matching,0.3,2.0,48,124
84,add,7.0,N,,Cc1ccc(,Cc1ccc(N,8,add N at position 7,flow_matching,0.3,2.0,48,124
85,add,8.0,C,,Cc1ccc(N,Cc1ccc(NC,9,add C at position 8,flow_matching,0.3,2.0,48,124
86,add,9.0,(,,Cc1ccc(NC,Cc1ccc(NC(,10,add ( at position 9,flow_matching,0.3,2.0,48,124
87,add,10.0,=,,Cc1ccc(NC(,Cc1ccc(NC(=,11,add = at position 10,flow_matching,0.3,2.0,48,124
88,add,11.0,O,,Cc1ccc(NC(=,Cc1ccc(NC(=O,12,add O at position 11,flow_matching,0.3,2.0,48,124
89,add,12.0,),,Cc1ccc(NC(=O,Cc1ccc(NC(=O),13,add ) at position 12,flow_matching,0.3,2.0,48,124
90,add,13.0,c,,Cc1ccc(NC(=O),Cc1ccc(NC(=O)c,14,add c at position 13,flow_matching,0.3,2.0,48,124
91,add,14.0,2,,Cc1ccc(NC(=O)c,Cc1ccc(NC(=O)c2,15,add 2 at position 14,flow_matching,0.3,2.0,48,124
92,add,15.0,c,,Cc1ccc(NC(=O)c2,Cc1ccc(NC(=O)c2c,16,add c at position 15,flow_matching,0.3,2.0,48,124
93,add,16.0,c,,Cc1ccc(NC(=O)c2c,Cc1ccc(NC(=O)c2cc,17,add c at position 16,flow_matching,0.3,2.0,48,124
94,add,17.0,3,,Cc1ccc(NC(=O)c2cc,Cc1ccc(NC(=O)c2cc3,18,add 3 at position 17,flow_matching,0.3,2.0,48,124
95,add,18.0,c,,Cc1ccc(NC(=O)c2cc3,Cc1ccc(NC(=O)c2cc3c,19,add c at position 18,flow_matching,0.3,2.0,48,124
96,add,19.0,c,,Cc1ccc(NC(=O)c2cc3c,Cc1ccc(NC(=O)c2cc3cc,20,add c at position 19,flow_matching,0.3,2.0,48,124
97,add,20.0,c,,Cc1ccc(NC(=O)c2cc3cc,Cc1ccc(NC(=O)c2cc3ccc,21,add c at position 20,flow_matching,0.3,2.0,48,124
98,add,21.0,c,,Cc1ccc(NC(=O)c2cc3ccc,Cc1ccc(NC(=O)c2cc3cccc,22,add c at position 21,flow_matching,0.3,2.0,48,124
99,add,22.0,c,,Cc1ccc(NC(=O)c2cc3cccc,Cc1ccc(NC(=O)c2cc3ccccc,23,add c at position 22,flow_matching,0.3,2.0,48,124
100,add,23.0,3,,Cc1ccc(NC(=O)c2cc3ccccc,Cc1ccc(NC(=O)c2cc3ccccc3,24,add 3 at position 23,flow_matching,0.3,2.0,48,124
101,add,24.0,o,,Cc1ccc(NC(=O)c2cc3ccccc3,Cc1ccc(NC(=O)c2cc3ccccc3o,25,add o at position 24,flow_matching,0.3,2.0,48,124
102,add,25.0,c,,Cc1ccc(NC(=O)c2cc3ccccc3o,Cc1ccc(NC(=O)c2cc3ccccc3oc,26,add c at position 25,flow_matching,0.3,2.0,48,124
103,add,26.0,2,,Cc1ccc(NC(=O)c2cc3ccccc3oc,Cc1ccc(NC(=O)c2cc3ccccc3oc2,27,add 2 at position 26,flow_matching,0.3,2.0,48,124
104,add,27.0,=,,Cc1ccc(NC(=O)c2cc3ccccc3oc2,Cc1ccc(NC(=O)c2cc3ccccc3oc2=,28,add = at position 27,flow_matching,0.3,2.0,48,124
105,add,28.0,O,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O,29,add O at position 28,flow_matching,0.3,2.0,48,124
106,add,29.0,),,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O),30,add ) at position 29,flow_matching,0.3,2.0,48,124
107,add,30.0,c,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O),Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c,31,add c at position 30,flow_matching,0.3,2.0,48,124
108,add,31.0,(,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c(,32,add ( at position 31,flow_matching,0.3,2.0,48,124
109,add,32.0,[,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c(,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([,33,add [ at position 32,flow_matching,0.3,2.0,48,124
110,add,33.0,N,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N,34,add N at position 33,flow_matching,0.3,2.0,48,124
111,add,34.0,+,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+,35,add + at position 34,flow_matching,0.3,2.0,48,124
112,add,35.0,],,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+],36,add ] at position 35,flow_matching,0.3,2.0,48,124
113,add,36.0,(,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+],Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](,37,add ( at position 36,flow_matching,0.3,2.0,48,124
114,add,37.0,=,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=,38,add = at position 37,flow_matching,0.3,2.0,48,124
115,add,38.0,O,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O,39,add O at position 38,flow_matching,0.3,2.0,48,124
116,add,39.0,),,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O),40,add ) at position 39,flow_matching,0.3,2.0,48,124
117,add,40.0,[,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O),Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[,41,add [ at position 40,flow_matching,0.3,2.0,48,124
118,add,41.0,O,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O,42,add O at position 41,flow_matching,0.3,2.0,48,124
119,add,42.0,-,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-,43,add - at position 42,flow_matching,0.3,2.0,48,124
120,add,43.0,],,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-],44,add ] at position 43,flow_matching,0.3,2.0,48,124
121,add,44.0,),,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-],Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-]),45,add ) at position 44,flow_matching,0.3,2.0,48,124
122,add,45.0,c,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-]),Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-])c,46,add c at position 45,flow_matching,0.3,2.0,48,124
123,add,46.0,1,,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-])c,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-])c1,47,add 1 at position 46,flow_matching,0.3,2.0,48,124
124,add,47.0,"
",,Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-])c1,"Cc1ccc(NC(=O)c2cc3ccccc3oc2=O)c([N+](=O)[O-])c1
",48,"add 
 at position 47",flow_matching,0.3,2.0,48,124
