step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,65,250
1,add,0.0,#,,,#,1,add # at position 0,flow_matching,0.3,2.0,65,250
2,replace,0.0,B,#,#,B,1,replace # at position 0 with B,flow_matching,0.3,2.0,65,250
3,remove,0.0,B,,B,,0,remove B from position 0,flow_matching,0.3,2.0,65,250
4,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,65,250
5,replace,0.0,C,@,@,C,1,replace @ at position 0 with C,flow_matching,0.3,2.0,65,250
6,add,1.0,n,,C,Cn,2,add n at position 1,flow_matching,0.3,2.0,65,250
7,add,0.0,],,Cn,]Cn,3,add ] at position 0,flow_matching,0.3,2.0,65,250
8,replace,0.0,C,],]Cn,CCn,3,replace ] at position 0 with C,flow_matching,0.3,2.0,65,250
9,remove,1.0,C,,CCn,Cn,2,remove C from position 1,flow_matching,0.3,2.0,65,250
10,remove,0.0,C,,Cn,n,1,remove C from position 0,flow_matching,0.3,2.0,65,250
11,replace,0.0,C,n,n,C,1,replace n at position 0 with C,flow_matching,0.3,2.0,65,250
12,replace,0.0,2,C,C,2,1,replace C at position 0 with 2,flow_matching,0.3,2.0,65,250
13,replace,0.0,c,2,2,c,1,replace 2 at position 0 with c,flow_matching,0.3,2.0,65,250
14,replace,0.0,#,c,c,#,1,replace c at position 0 with #,flow_matching,0.3,2.0,65,250
15,remove,0.0,#,,#,,0,remove # from position 0,flow_matching,0.3,2.0,65,250
16,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,65,250
17,replace,0.0,S,@,@,S,1,replace @ at position 0 with S,flow_matching,0.3,2.0,65,250
18,replace,0.0,+,S,S,+,1,replace S at position 0 with +,flow_matching,0.3,2.0,65,250
19,replace,0.0,[,+,+,[,1,replace + at position 0 with [,flow_matching,0.3,2.0,65,250
20,remove,0.0,[,,[,,0,remove [ from position 0,flow_matching,0.3,2.0,65,250
21,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,65,250
22,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,65,250
23,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,65,250
24,add,2.0,1,,CC,CC1,3,add 1 at position 2,flow_matching,0.3,2.0,65,250
25,add,3.0,(,,CC1,CC1(,4,add ( at position 3,flow_matching,0.3,2.0,65,250
26,add,2.0,#,,CC1(,CC#1(,5,add # at position 2,flow_matching,0.3,2.0,65,250
27,add,2.0,#,,CC#1(,CC##1(,6,add # at position 2,flow_matching,0.3,2.0,65,250
28,add,2.0,l,,CC##1(,CCl##1(,7,add l at position 2,flow_matching,0.3,2.0,65,250
29,add,3.0,2,,CCl##1(,CCl2##1(,8,add 2 at position 3,flow_matching,0.3,2.0,65,250
30,replace,2.0,1,l,CCl2##1(,CC12##1(,8,replace l at position 2 with 1,flow_matching,0.3,2.0,65,250
31,replace,4.0,5,#,CC12##1(,CC125#1(,8,replace # at position 4 with 5,flow_matching,0.3,2.0,65,250
32,add,5.0,\,,CC125#1(,CC125\#1(,9,add \ at position 5,flow_matching,0.3,2.0,65,250
33,replace,4.0,\,5,CC125\#1(,CC12\\#1(,9,replace 5 at position 4 with \,flow_matching,0.3,2.0,65,250
34,add,1.0,B,,CC12\\#1(,CBC12\\#1(,10,add B at position 1,flow_matching,0.3,2.0,65,250
35,remove,0.0,C,,CBC12\\#1(,BC12\\#1(,9,remove C from position 0,flow_matching,0.3,2.0,65,250
36,add,1.0,6,,BC12\\#1(,B6C12\\#1(,10,add 6 at position 1,flow_matching,0.3,2.0,65,250
37,add,10.0,1,,B6C12\\#1(,B6C12\\#1(1,11,add 1 at position 10,flow_matching,0.3,2.0,65,250
38,replace,6.0,/,\,B6C12\\#1(1,B6C12\/#1(1,11,replace \ at position 6 with /,flow_matching,0.3,2.0,65,250
39,replace,0.0,C,B,B6C12\/#1(1,C6C12\/#1(1,11,replace B at position 0 with C,flow_matching,0.3,2.0,65,250
40,replace,1.0,C,6,C6C12\/#1(1,CCC12\/#1(1,11,replace 6 at position 1 with C,flow_matching,0.3,2.0,65,250
41,remove,2.0,C,,CCC12\/#1(1,CC12\/#1(1,10,remove C from position 2,flow_matching,0.3,2.0,65,250
42,add,5.0,4,,CC12\/#1(1,CC12\4/#1(1,11,add 4 at position 5,flow_matching,0.3,2.0,65,250
43,remove,8.0,1,,CC12\4/#1(1,CC12\4/#(1,10,remove 1 from position 8,flow_matching,0.3,2.0,65,250
44,replace,3.0,(,2,CC12\4/#(1,CC1(\4/#(1,10,replace 2 at position 3 with (,flow_matching,0.3,2.0,65,250
45,remove,4.0,\,,CC1(\4/#(1,CC1(4/#(1,9,remove \ from position 4,flow_matching,0.3,2.0,65,250
46,add,6.0,#,,CC1(4/#(1,CC1(4/##(1,10,add # at position 6,flow_matching,0.3,2.0,65,250
47,replace,4.0,C,4,CC1(4/##(1,CC1(C/##(1,10,replace 4 at position 4 with C,flow_matching,0.3,2.0,65,250
48,remove,5.0,/,,CC1(C/##(1,CC1(C##(1,9,remove / from position 5,flow_matching,0.3,2.0,65,250
49,add,5.0,2,,CC1(C##(1,CC1(C2##(1,10,add 2 at position 5,flow_matching,0.3,2.0,65,250
50,add,10.0,7,,CC1(C2##(1,CC1(C2##(17,11,add 7 at position 10,flow_matching,0.3,2.0,65,250
51,replace,3.0,1,(,CC1(C2##(17,CC11C2##(17,11,replace ( at position 3 with 1,flow_matching,0.3,2.0,65,250
52,replace,3.0,(,1,CC11C2##(17,CC1(C2##(17,11,replace 1 at position 3 with (,flow_matching,0.3,2.0,65,250
53,remove,1.0,C,,CC1(C2##(17,C1(C2##(17,10,remove C from position 1,flow_matching,0.3,2.0,65,250
54,replace,1.0,C,1,C1(C2##(17,CC(C2##(17,10,replace 1 at position 1 with C,flow_matching,0.3,2.0,65,250
55,remove,5.0,#,,CC(C2##(17,CC(C2#(17,9,remove # from position 5,flow_matching,0.3,2.0,65,250
56,replace,2.0,1,(,CC(C2#(17,CC1C2#(17,9,replace ( at position 2 with 1,flow_matching,0.3,2.0,65,250
57,remove,4.0,2,,CC1C2#(17,CC1C#(17,8,remove 2 from position 4,flow_matching,0.3,2.0,65,250
58,add,6.0,=,,CC1C#(17,CC1C#(=17,9,add = at position 6,flow_matching,0.3,2.0,65,250
59,remove,7.0,1,,CC1C#(=17,CC1C#(=7,8,remove 1 from position 7,flow_matching,0.3,2.0,65,250
60,replace,1.0,],C,CC1C#(=7,C]1C#(=7,8,replace C at position 1 with ],flow_matching,0.3,2.0,65,250
61,replace,1.0,C,],C]1C#(=7,CC1C#(=7,8,replace ] at position 1 with C,flow_matching,0.3,2.0,65,250
62,replace,3.0,(,C,CC1C#(=7,CC1(#(=7,8,replace C at position 3 with (,flow_matching,0.3,2.0,65,250
63,add,8.0,I,,CC1(#(=7,CC1(#(=7I,9,add I at position 8,flow_matching,0.3,2.0,65,250
64,add,8.0,F,,CC1(#(=7I,CC1(#(=7FI,10,add F at position 8,flow_matching,0.3,2.0,65,250
65,add,0.0,@,,CC1(#(=7FI,@CC1(#(=7FI,11,add @ at position 0,flow_matching,0.3,2.0,65,250
66,add,6.0,(,,@CC1(#(=7FI,@CC1(#((=7FI,12,add ( at position 6,flow_matching,0.3,2.0,65,250
67,add,7.0,7,,@CC1(#((=7FI,@CC1(#(7(=7FI,13,add 7 at position 7,flow_matching,0.3,2.0,65,250
68,add,6.0,1,,@CC1(#(7(=7FI,@CC1(#1(7(=7FI,14,add 1 at position 6,flow_matching,0.3,2.0,65,250
69,remove,3.0,1,,@CC1(#1(7(=7FI,@CC(#1(7(=7FI,13,remove 1 from position 3,flow_matching,0.3,2.0,65,250
70,add,7.0,B,,@CC(#1(7(=7FI,@CC(#1(B7(=7FI,14,add B at position 7,flow_matching,0.3,2.0,65,250
71,add,11.0,],,@CC(#1(B7(=7FI,@CC(#1(B7(=]7FI,15,add ] at position 11,flow_matching,0.3,2.0,65,250
72,replace,0.0,C,@,@CC(#1(B7(=]7FI,CCC(#1(B7(=]7FI,15,replace @ at position 0 with C,flow_matching,0.3,2.0,65,250
73,replace,1.0,(,C,CCC(#1(B7(=]7FI,C(C(#1(B7(=]7FI,15,replace C at position 1 with (,flow_matching,0.3,2.0,65,250
74,add,3.0,l,,C(C(#1(B7(=]7FI,C(Cl(#1(B7(=]7FI,16,add l at position 3,flow_matching,0.3,2.0,65,250
75,replace,1.0,C,(,C(Cl(#1(B7(=]7FI,CCCl(#1(B7(=]7FI,16,replace ( at position 1 with C,flow_matching,0.3,2.0,65,250
76,add,13.0,],,CCCl(#1(B7(=]7FI,CCCl(#1(B7(=]]7FI,17,add ] at position 13,flow_matching,0.3,2.0,65,250
77,remove,1.0,C,,CCCl(#1(B7(=]]7FI,CCl(#1(B7(=]]7FI,16,remove C from position 1,flow_matching,0.3,2.0,65,250
78,replace,2.0,1,l,CCl(#1(B7(=]]7FI,CC1(#1(B7(=]]7FI,16,replace l at position 2 with 1,flow_matching,0.3,2.0,65,250
79,add,8.0,),,CC1(#1(B7(=]]7FI,CC1(#1(B)7(=]]7FI,17,add ) at position 8,flow_matching,0.3,2.0,65,250
80,replace,4.0,@,#,CC1(#1(B)7(=]]7FI,CC1(@1(B)7(=]]7FI,17,replace # at position 4 with @,flow_matching,0.3,2.0,65,250
81,remove,7.0,B,,CC1(@1(B)7(=]]7FI,CC1(@1()7(=]]7FI,16,remove B from position 7,flow_matching,0.3,2.0,65,250
82,replace,4.0,C,@,CC1(@1()7(=]]7FI,CC1(C1()7(=]]7FI,16,replace @ at position 4 with C,flow_matching,0.3,2.0,65,250
83,remove,8.0,7,,CC1(C1()7(=]]7FI,CC1(C1()(=]]7FI,15,remove 7 from position 8,flow_matching,0.3,2.0,65,250
84,remove,0.0,C,,CC1(C1()(=]]7FI,C1(C1()(=]]7FI,14,remove C from position 0,flow_matching,0.3,2.0,65,250
85,replace,7.0,#,(,C1(C1()(=]]7FI,C1(C1()#=]]7FI,14,replace ( at position 7 with #,flow_matching,0.3,2.0,65,250
86,replace,1.0,C,1,C1(C1()#=]]7FI,CC(C1()#=]]7FI,14,replace 1 at position 1 with C,flow_matching,0.3,2.0,65,250
87,replace,2.0,1,(,CC(C1()#=]]7FI,CC1C1()#=]]7FI,14,replace ( at position 2 with 1,flow_matching,0.3,2.0,65,250
88,add,8.0,O,,CC1C1()#=]]7FI,CC1C1()#O=]]7FI,15,add O at position 8,flow_matching,0.3,2.0,65,250
89,replace,3.0,(,C,CC1C1()#O=]]7FI,CC1(1()#O=]]7FI,15,replace C at position 3 with (,flow_matching,0.3,2.0,65,250
90,remove,1.0,C,,CC1(1()#O=]]7FI,C1(1()#O=]]7FI,14,remove C from position 1,flow_matching,0.3,2.0,65,250
91,replace,1.0,C,1,C1(1()#O=]]7FI,CC(1()#O=]]7FI,14,replace 1 at position 1 with C,flow_matching,0.3,2.0,65,250
92,add,12.0,C,,CC(1()#O=]]7FI,CC(1()#O=]]7CFI,15,add C at position 12,flow_matching,0.3,2.0,65,250
93,add,13.0,[,,CC(1()#O=]]7CFI,CC(1()#O=]]7C[FI,16,add [ at position 13,flow_matching,0.3,2.0,65,250
94,replace,2.0,1,(,CC(1()#O=]]7C[FI,CC11()#O=]]7C[FI,16,replace ( at position 2 with 1,flow_matching,0.3,2.0,65,250
95,remove,5.0,),,CC11()#O=]]7C[FI,CC11(#O=]]7C[FI,15,remove ) from position 5,flow_matching,0.3,2.0,65,250
96,add,0.0,=,,CC11(#O=]]7C[FI,=CC11(#O=]]7C[FI,16,add = at position 0,flow_matching,0.3,2.0,65,250
97,replace,0.0,C,=,=CC11(#O=]]7C[FI,CCC11(#O=]]7C[FI,16,replace = at position 0 with C,flow_matching,0.3,2.0,65,250
98,remove,12.0,C,,CCC11(#O=]]7C[FI,CCC11(#O=]]7[FI,15,remove C from position 12,flow_matching,0.3,2.0,65,250
99,add,13.0,=,,CCC11(#O=]]7[FI,CCC11(#O=]]7[=FI,16,add = at position 13,flow_matching,0.3,2.0,65,250
100,remove,10.0,],,CCC11(#O=]]7[=FI,CCC11(#O=]7[=FI,15,remove ] from position 10,flow_matching,0.3,2.0,65,250
101,replace,2.0,1,C,CCC11(#O=]7[=FI,CC111(#O=]7[=FI,15,replace C at position 2 with 1,flow_matching,0.3,2.0,65,250
102,add,1.0,+,,CC111(#O=]7[=FI,C+C111(#O=]7[=FI,16,add + at position 1,flow_matching,0.3,2.0,65,250
103,replace,4.0,(,1,C+C111(#O=]7[=FI,C+C1(1(#O=]7[=FI,16,replace 1 at position 4 with (,flow_matching,0.3,2.0,65,250
104,replace,1.0,C,+,C+C1(1(#O=]7[=FI,CCC1(1(#O=]7[=FI,16,replace + at position 1 with C,flow_matching,0.3,2.0,65,250
105,remove,10.0,],,CCC1(1(#O=]7[=FI,CCC1(1(#O=7[=FI,15,remove ] from position 10,flow_matching,0.3,2.0,65,250
106,add,15.0,3,,CCC1(1(#O=7[=FI,CCC1(1(#O=7[=FI3,16,add 3 at position 15,flow_matching,0.3,2.0,65,250
107,replace,2.0,1,C,CCC1(1(#O=7[=FI3,CC11(1(#O=7[=FI3,16,replace C at position 2 with 1,flow_matching,0.3,2.0,65,250
108,replace,7.0,7,#,CC11(1(#O=7[=FI3,CC11(1(7O=7[=FI3,16,replace # at position 7 with 7,flow_matching,0.3,2.0,65,250
109,add,13.0,(,,CC11(1(7O=7[=FI3,CC11(1(7O=7[=(FI3,17,add ( at position 13,flow_matching,0.3,2.0,65,250
110,replace,13.0,2,(,CC11(1(7O=7[=(FI3,CC11(1(7O=7[=2FI3,17,replace ( at position 13 with 2,flow_matching,0.3,2.0,65,250
111,replace,10.0,5,7,CC11(1(7O=7[=2FI3,CC11(1(7O=5[=2FI3,17,replace 7 at position 10 with 5,flow_matching,0.3,2.0,65,250
112,remove,4.0,(,,CC11(1(7O=5[=2FI3,CC111(7O=5[=2FI3,16,remove ( from position 4,flow_matching,0.3,2.0,65,250
113,remove,12.0,2,,CC111(7O=5[=2FI3,CC111(7O=5[=FI3,15,remove 2 from position 12,flow_matching,0.3,2.0,65,250
114,add,3.0,(,,CC111(7O=5[=FI3,CC1(11(7O=5[=FI3,16,add ( at position 3,flow_matching,0.3,2.0,65,250
115,add,1.0,/,,CC1(11(7O=5[=FI3,C/C1(11(7O=5[=FI3,17,add / at position 1,flow_matching,0.3,2.0,65,250
116,add,17.0,-,,C/C1(11(7O=5[=FI3,C/C1(11(7O=5[=FI3-,18,add - at position 17,flow_matching,0.3,2.0,65,250
117,replace,13.0,C,=,C/C1(11(7O=5[=FI3-,C/C1(11(7O=5[CFI3-,18,replace = at position 13 with C,flow_matching,0.3,2.0,65,250
118,replace,1.0,C,/,C/C1(11(7O=5[CFI3-,CCC1(11(7O=5[CFI3-,18,replace / at position 1 with C,flow_matching,0.3,2.0,65,250
119,replace,2.0,1,C,CCC1(11(7O=5[CFI3-,CC11(11(7O=5[CFI3-,18,replace C at position 2 with 1,flow_matching,0.3,2.0,65,250
120,add,9.0,n,,CC11(11(7O=5[CFI3-,CC11(11(7nO=5[CFI3-,19,add n at position 9,flow_matching,0.3,2.0,65,250
121,add,4.0,r,,CC11(11(7nO=5[CFI3-,CC11r(11(7nO=5[CFI3-,20,add r at position 4,flow_matching,0.3,2.0,65,250
122,replace,3.0,(,1,CC11r(11(7nO=5[CFI3-,CC1(r(11(7nO=5[CFI3-,20,replace 1 at position 3 with (,flow_matching,0.3,2.0,65,250
123,add,17.0,),,CC1(r(11(7nO=5[CFI3-,CC1(r(11(7nO=5[CF)I3-,21,add ) at position 17,flow_matching,0.3,2.0,65,250
124,add,2.0,H,,CC1(r(11(7nO=5[CF)I3-,CCH1(r(11(7nO=5[CF)I3-,22,add H at position 2,flow_matching,0.3,2.0,65,250
125,add,17.0,@,,CCH1(r(11(7nO=5[CF)I3-,CCH1(r(11(7nO=5[C@F)I3-,23,add @ at position 17,flow_matching,0.3,2.0,65,250
126,add,12.0,/,,CCH1(r(11(7nO=5[C@F)I3-,CCH1(r(11(7n/O=5[C@F)I3-,24,add / at position 12,flow_matching,0.3,2.0,65,250
127,replace,6.0,1,(,CCH1(r(11(7n/O=5[C@F)I3-,CCH1(r111(7n/O=5[C@F)I3-,24,replace ( at position 6 with 1,flow_matching,0.3,2.0,65,250
128,add,16.0,I,,CCH1(r111(7n/O=5[C@F)I3-,CCH1(r111(7n/O=5I[C@F)I3-,25,add I at position 16,flow_matching,0.3,2.0,65,250
129,replace,10.0,],7,CCH1(r111(7n/O=5I[C@F)I3-,CCH1(r111(]n/O=5I[C@F)I3-,25,replace 7 at position 10 with ],flow_matching,0.3,2.0,65,250
130,replace,17.0,7,[,CCH1(r111(]n/O=5I[C@F)I3-,CCH1(r111(]n/O=5I7C@F)I3-,25,replace [ at position 17 with 7,flow_matching,0.3,2.0,65,250
131,add,1.0,6,,CCH1(r111(]n/O=5I7C@F)I3-,C6CH1(r111(]n/O=5I7C@F)I3-,26,add 6 at position 1,flow_matching,0.3,2.0,65,250
132,add,6.0,F,,C6CH1(r111(]n/O=5I7C@F)I3-,C6CH1(Fr111(]n/O=5I7C@F)I3-,27,add F at position 6,flow_matching,0.3,2.0,65,250
133,add,17.0,l,,C6CH1(Fr111(]n/O=5I7C@F)I3-,C6CH1(Fr111(]n/O=l5I7C@F)I3-,28,add l at position 17,flow_matching,0.3,2.0,65,250
134,add,20.0,\,,C6CH1(Fr111(]n/O=l5I7C@F)I3-,C6CH1(Fr111(]n/O=l5I\7C@F)I3-,29,add \ at position 20,flow_matching,0.3,2.0,65,250
135,add,1.0,l,,C6CH1(Fr111(]n/O=l5I\7C@F)I3-,Cl6CH1(Fr111(]n/O=l5I\7C@F)I3-,30,add l at position 1,flow_matching,0.3,2.0,65,250
136,replace,17.0,7,=,Cl6CH1(Fr111(]n/O=l5I\7C@F)I3-,Cl6CH1(Fr111(]n/O7l5I\7C@F)I3-,30,replace = at position 17 with 7,flow_matching,0.3,2.0,65,250
137,remove,7.0,F,,Cl6CH1(Fr111(]n/O7l5I\7C@F)I3-,Cl6CH1(r111(]n/O7l5I\7C@F)I3-,29,remove F from position 7,flow_matching,0.3,2.0,65,250
138,replace,1.0,C,l,Cl6CH1(r111(]n/O7l5I\7C@F)I3-,CC6CH1(r111(]n/O7l5I\7C@F)I3-,29,replace l at position 1 with C,flow_matching,0.3,2.0,65,250
139,replace,14.0,C,/,CC6CH1(r111(]n/O7l5I\7C@F)I3-,CC6CH1(r111(]nCO7l5I\7C@F)I3-,29,replace / at position 14 with C,flow_matching,0.3,2.0,65,250
140,add,5.0,l,,CC6CH1(r111(]nCO7l5I\7C@F)I3-,CC6CHl1(r111(]nCO7l5I\7C@F)I3-,30,add l at position 5,flow_matching,0.3,2.0,65,250
141,add,16.0,(,,CC6CHl1(r111(]nCO7l5I\7C@F)I3-,CC6CHl1(r111(]nC(O7l5I\7C@F)I3-,31,add ( at position 16,flow_matching,0.3,2.0,65,250
142,replace,23.0,O,7,CC6CHl1(r111(]nC(O7l5I\7C@F)I3-,CC6CHl1(r111(]nC(O7l5I\OC@F)I3-,31,replace 7 at position 23 with O,flow_matching,0.3,2.0,65,250
143,remove,14.0,n,,CC6CHl1(r111(]nC(O7l5I\OC@F)I3-,CC6CHl1(r111(]C(O7l5I\OC@F)I3-,30,remove n from position 14,flow_matching,0.3,2.0,65,250
144,replace,14.0,r,C,CC6CHl1(r111(]C(O7l5I\OC@F)I3-,CC6CHl1(r111(]r(O7l5I\OC@F)I3-,30,replace C at position 14 with r,flow_matching,0.3,2.0,65,250
145,replace,18.0,o,l,CC6CHl1(r111(]r(O7l5I\OC@F)I3-,CC6CHl1(r111(]r(O7o5I\OC@F)I3-,30,replace l at position 18 with o,flow_matching,0.3,2.0,65,250
146,replace,2.0,1,6,CC6CHl1(r111(]r(O7o5I\OC@F)I3-,CC1CHl1(r111(]r(O7o5I\OC@F)I3-,30,replace 6 at position 2 with 1,flow_matching,0.3,2.0,65,250
147,add,7.0,F,,CC1CHl1(r111(]r(O7o5I\OC@F)I3-,CC1CHl1F(r111(]r(O7o5I\OC@F)I3-,31,add F at position 7,flow_matching,0.3,2.0,65,250
148,remove,8.0,(,,CC1CHl1F(r111(]r(O7o5I\OC@F)I3-,CC1CHl1Fr111(]r(O7o5I\OC@F)I3-,30,remove ( from position 8,flow_matching,0.3,2.0,65,250
149,add,4.0,+,,CC1CHl1Fr111(]r(O7o5I\OC@F)I3-,CC1C+Hl1Fr111(]r(O7o5I\OC@F)I3-,31,add + at position 4,flow_matching,0.3,2.0,65,250
150,replace,3.0,(,C,CC1C+Hl1Fr111(]r(O7o5I\OC@F)I3-,CC1(+Hl1Fr111(]r(O7o5I\OC@F)I3-,31,replace C at position 3 with (,flow_matching,0.3,2.0,65,250
151,replace,4.0,O,+,CC1(+Hl1Fr111(]r(O7o5I\OC@F)I3-,CC1(OHl1Fr111(]r(O7o5I\OC@F)I3-,31,replace + at position 4 with O,flow_matching,0.3,2.0,65,250
152,replace,4.0,C,O,CC1(OHl1Fr111(]r(O7o5I\OC@F)I3-,CC1(CHl1Fr111(]r(O7o5I\OC@F)I3-,31,replace O at position 4 with C,flow_matching,0.3,2.0,65,250
153,replace,5.0,),H,CC1(CHl1Fr111(]r(O7o5I\OC@F)I3-,CC1(C)l1Fr111(]r(O7o5I\OC@F)I3-,31,replace H at position 5 with ),flow_matching,0.3,2.0,65,250
154,replace,11.0,4,1,CC1(C)l1Fr111(]r(O7o5I\OC@F)I3-,CC1(C)l1Fr141(]r(O7o5I\OC@F)I3-,31,replace 1 at position 11 with 4,flow_matching,0.3,2.0,65,250
155,add,2.0,C,,CC1(C)l1Fr141(]r(O7o5I\OC@F)I3-,CCC1(C)l1Fr141(]r(O7o5I\OC@F)I3-,32,add C at position 2,flow_matching,0.3,2.0,65,250
156,replace,29.0,4,I,CCC1(C)l1Fr141(]r(O7o5I\OC@F)I3-,CCC1(C)l1Fr141(]r(O7o5I\OC@F)43-,32,replace I at position 29 with 4,flow_matching,0.3,2.0,65,250
157,add,32.0,+,,CCC1(C)l1Fr141(]r(O7o5I\OC@F)43-,CCC1(C)l1Fr141(]r(O7o5I\OC@F)43-+,33,add + at position 32,flow_matching,0.3,2.0,65,250
158,remove,18.0,O,,CCC1(C)l1Fr141(]r(O7o5I\OC@F)43-+,CCC1(C)l1Fr141(]r(7o5I\OC@F)43-+,32,remove O from position 18,flow_matching,0.3,2.0,65,250
159,replace,16.0,7,r,CCC1(C)l1Fr141(]r(7o5I\OC@F)43-+,CCC1(C)l1Fr141(]7(7o5I\OC@F)43-+,32,replace r at position 16 with 7,flow_matching,0.3,2.0,65,250
160,remove,10.0,r,,CCC1(C)l1Fr141(]7(7o5I\OC@F)43-+,CCC1(C)l1F141(]7(7o5I\OC@F)43-+,31,remove r from position 10,flow_matching,0.3,2.0,65,250
161,remove,18.0,o,,CCC1(C)l1F141(]7(7o5I\OC@F)43-+,CCC1(C)l1F141(]7(75I\OC@F)43-+,30,remove o from position 18,flow_matching,0.3,2.0,65,250
162,replace,2.0,1,C,CCC1(C)l1F141(]7(75I\OC@F)43-+,CC11(C)l1F141(]7(75I\OC@F)43-+,30,replace C at position 2 with 1,flow_matching,0.3,2.0,65,250
163,remove,8.0,1,,CC11(C)l1F141(]7(75I\OC@F)43-+,CC11(C)lF141(]7(75I\OC@F)43-+,29,remove 1 from position 8,flow_matching,0.3,2.0,65,250
164,add,1.0,C,,CC11(C)lF141(]7(75I\OC@F)43-+,CCC11(C)lF141(]7(75I\OC@F)43-+,30,add C at position 1,flow_matching,0.3,2.0,65,250
165,replace,2.0,1,C,CCC11(C)lF141(]7(75I\OC@F)43-+,CC111(C)lF141(]7(75I\OC@F)43-+,30,replace C at position 2 with 1,flow_matching,0.3,2.0,65,250
166,replace,1.0,2,C,CC111(C)lF141(]7(75I\OC@F)43-+,C2111(C)lF141(]7(75I\OC@F)43-+,30,replace C at position 1 with 2,flow_matching,0.3,2.0,65,250
167,remove,7.0,),,C2111(C)lF141(]7(75I\OC@F)43-+,C2111(ClF141(]7(75I\OC@F)43-+,29,remove ) from position 7,flow_matching,0.3,2.0,65,250
168,remove,6.0,C,,C2111(ClF141(]7(75I\OC@F)43-+,C2111(lF141(]7(75I\OC@F)43-+,28,remove C from position 6,flow_matching,0.3,2.0,65,250
169,remove,21.0,@,,C2111(lF141(]7(75I\OC@F)43-+,C2111(lF141(]7(75I\OCF)43-+,27,remove @ from position 21,flow_matching,0.3,2.0,65,250
170,replace,0.0,1,C,C2111(lF141(]7(75I\OCF)43-+,12111(lF141(]7(75I\OCF)43-+,27,replace C at position 0 with 1,flow_matching,0.3,2.0,65,250
171,add,19.0,c,,12111(lF141(]7(75I\OCF)43-+,12111(lF141(]7(75I\cOCF)43-+,28,add c at position 19,flow_matching,0.3,2.0,65,250
172,add,10.0,s,,12111(lF141(]7(75I\cOCF)43-+,12111(lF14s1(]7(75I\cOCF)43-+,29,add s at position 10,flow_matching,0.3,2.0,65,250
173,replace,0.0,C,1,12111(lF14s1(]7(75I\cOCF)43-+,C2111(lF14s1(]7(75I\cOCF)43-+,29,replace 1 at position 0 with C,flow_matching,0.3,2.0,65,250
174,replace,15.0,c,(,C2111(lF14s1(]7(75I\cOCF)43-+,C2111(lF14s1(]7c75I\cOCF)43-+,29,replace ( at position 15 with c,flow_matching,0.3,2.0,65,250
175,remove,22.0,C,,C2111(lF14s1(]7c75I\cOCF)43-+,C2111(lF14s1(]7c75I\cOF)43-+,28,remove C from position 22,flow_matching,0.3,2.0,65,250
176,replace,1.0,C,2,C2111(lF14s1(]7c75I\cOF)43-+,CC111(lF14s1(]7c75I\cOF)43-+,28,replace 2 at position 1 with C,flow_matching,0.3,2.0,65,250
177,add,27.0,O,,CC111(lF14s1(]7c75I\cOF)43-+,CC111(lF14s1(]7c75I\cOF)43-O+,29,add O at position 27,flow_matching,0.3,2.0,65,250
178,add,6.0,B,,CC111(lF14s1(]7c75I\cOF)43-O+,CC111(BlF14s1(]7c75I\cOF)43-O+,30,add B at position 6,flow_matching,0.3,2.0,65,250
179,add,24.0,C,,CC111(BlF14s1(]7c75I\cOF)43-O+,CC111(BlF14s1(]7c75I\cOFC)43-O+,31,add C at position 24,flow_matching,0.3,2.0,65,250
180,remove,12.0,1,,CC111(BlF14s1(]7c75I\cOFC)43-O+,CC111(BlF14s(]7c75I\cOFC)43-O+,30,remove 1 from position 12,flow_matching,0.3,2.0,65,250
181,add,8.0,/,,CC111(BlF14s(]7c75I\cOFC)43-O+,CC111(Bl/F14s(]7c75I\cOFC)43-O+,31,add / at position 8,flow_matching,0.3,2.0,65,250
182,add,2.0,o,,CC111(Bl/F14s(]7c75I\cOFC)43-O+,CCo111(Bl/F14s(]7c75I\cOFC)43-O+,32,add o at position 2,flow_matching,0.3,2.0,65,250
183,add,0.0,/,,CCo111(Bl/F14s(]7c75I\cOFC)43-O+,/CCo111(Bl/F14s(]7c75I\cOFC)43-O+,33,add / at position 0,flow_matching,0.3,2.0,65,250
184,replace,0.0,r,/,/CCo111(Bl/F14s(]7c75I\cOFC)43-O+,rCCo111(Bl/F14s(]7c75I\cOFC)43-O+,33,replace / at position 0 with r,flow_matching,0.3,2.0,65,250
185,replace,17.0,\,7,rCCo111(Bl/F14s(]7c75I\cOFC)43-O+,rCCo111(Bl/F14s(]\c75I\cOFC)43-O+,33,replace 7 at position 17 with \,flow_matching,0.3,2.0,65,250
186,replace,31.0,B,O,rCCo111(Bl/F14s(]\c75I\cOFC)43-O+,rCCo111(Bl/F14s(]\c75I\cOFC)43-B+,33,replace O at position 31 with B,flow_matching,0.3,2.0,65,250
187,replace,0.0,C,r,rCCo111(Bl/F14s(]\c75I\cOFC)43-B+,CCCo111(Bl/F14s(]\c75I\cOFC)43-B+,33,replace r at position 0 with C,flow_matching,0.3,2.0,65,250
188,replace,2.0,1,C,CCCo111(Bl/F14s(]\c75I\cOFC)43-B+,CC1o111(Bl/F14s(]\c75I\cOFC)43-B+,33,replace C at position 2 with 1,flow_matching,0.3,2.0,65,250
189,replace,3.0,(,o,CC1o111(Bl/F14s(]\c75I\cOFC)43-B+,CC1(111(Bl/F14s(]\c75I\cOFC)43-B+,33,replace o at position 3 with (,flow_matching,0.3,2.0,65,250
190,replace,4.0,C,1,CC1(111(Bl/F14s(]\c75I\cOFC)43-B+,CC1(C11(Bl/F14s(]\c75I\cOFC)43-B+,33,replace 1 at position 4 with C,flow_matching,0.3,2.0,65,250
191,replace,5.0,),1,CC1(C11(Bl/F14s(]\c75I\cOFC)43-B+,CC1(C)1(Bl/F14s(]\c75I\cOFC)43-B+,33,replace 1 at position 5 with ),flow_matching,0.3,2.0,65,250
192,replace,6.0,O,1,CC1(C)1(Bl/F14s(]\c75I\cOFC)43-B+,CC1(C)O(Bl/F14s(]\c75I\cOFC)43-B+,33,replace 1 at position 6 with O,flow_matching,0.3,2.0,65,250
193,replace,7.0,C,(,CC1(C)O(Bl/F14s(]\c75I\cOFC)43-B+,CC1(C)OCBl/F14s(]\c75I\cOFC)43-B+,33,replace ( at position 7 with C,flow_matching,0.3,2.0,65,250
194,replace,8.0,[,B,CC1(C)OCBl/F14s(]\c75I\cOFC)43-B+,CC1(C)OC[l/F14s(]\c75I\cOFC)43-B+,33,replace B at position 8 with [,flow_matching,0.3,2.0,65,250
195,replace,9.0,C,l,CC1(C)OC[l/F14s(]\c75I\cOFC)43-B+,CC1(C)OC[C/F14s(]\c75I\cOFC)43-B+,33,replace l at position 9 with C,flow_matching,0.3,2.0,65,250
196,replace,10.0,@,/,CC1(C)OC[C/F14s(]\c75I\cOFC)43-B+,CC1(C)OC[C@F14s(]\c75I\cOFC)43-B+,33,replace / at position 10 with @,flow_matching,0.3,2.0,65,250
197,replace,11.0,H,F,CC1(C)OC[C@F14s(]\c75I\cOFC)43-B+,CC1(C)OC[C@H14s(]\c75I\cOFC)43-B+,33,replace F at position 11 with H,flow_matching,0.3,2.0,65,250
198,replace,12.0,],1,CC1(C)OC[C@H14s(]\c75I\cOFC)43-B+,CC1(C)OC[C@H]4s(]\c75I\cOFC)43-B+,33,replace 1 at position 12 with ],flow_matching,0.3,2.0,65,250
199,replace,13.0,(,4,CC1(C)OC[C@H]4s(]\c75I\cOFC)43-B+,CC1(C)OC[C@H](s(]\c75I\cOFC)43-B+,33,replace 4 at position 13 with (,flow_matching,0.3,2.0,65,250
200,replace,14.0,[,s,CC1(C)OC[C@H](s(]\c75I\cOFC)43-B+,CC1(C)OC[C@H]([(]\c75I\cOFC)43-B+,33,replace s at position 14 with [,flow_matching,0.3,2.0,65,250
201,replace,15.0,C,(,CC1(C)OC[C@H]([(]\c75I\cOFC)43-B+,CC1(C)OC[C@H]([C]\c75I\cOFC)43-B+,33,replace ( at position 15 with C,flow_matching,0.3,2.0,65,250
202,replace,16.0,@,],CC1(C)OC[C@H]([C]\c75I\cOFC)43-B+,CC1(C)OC[C@H]([C@\c75I\cOFC)43-B+,33,replace ] at position 16 with @,flow_matching,0.3,2.0,65,250
203,replace,17.0,H,\,CC1(C)OC[C@H]([C@\c75I\cOFC)43-B+,CC1(C)OC[C@H]([C@Hc75I\cOFC)43-B+,33,replace \ at position 17 with H,flow_matching,0.3,2.0,65,250
204,replace,18.0,],c,CC1(C)OC[C@H]([C@Hc75I\cOFC)43-B+,CC1(C)OC[C@H]([C@H]75I\cOFC)43-B+,33,replace c at position 18 with ],flow_matching,0.3,2.0,65,250
205,replace,19.0,2,7,CC1(C)OC[C@H]([C@H]75I\cOFC)43-B+,CC1(C)OC[C@H]([C@H]25I\cOFC)43-B+,33,replace 7 at position 19 with 2,flow_matching,0.3,2.0,65,250
206,replace,20.0,O,5,CC1(C)OC[C@H]([C@H]25I\cOFC)43-B+,CC1(C)OC[C@H]([C@H]2OI\cOFC)43-B+,33,replace 5 at position 20 with O,flow_matching,0.3,2.0,65,250
207,replace,21.0,[,I,CC1(C)OC[C@H]([C@H]2OI\cOFC)43-B+,CC1(C)OC[C@H]([C@H]2O[\cOFC)43-B+,33,replace I at position 21 with [,flow_matching,0.3,2.0,65,250
208,replace,22.0,C,\,CC1(C)OC[C@H]([C@H]2O[\cOFC)43-B+,CC1(C)OC[C@H]([C@H]2O[CcOFC)43-B+,33,replace \ at position 22 with C,flow_matching,0.3,2.0,65,250
209,replace,23.0,@,c,CC1(C)OC[C@H]([C@H]2O[CcOFC)43-B+,CC1(C)OC[C@H]([C@H]2O[C@OFC)43-B+,33,replace c at position 23 with @,flow_matching,0.3,2.0,65,250
210,replace,24.0,@,O,CC1(C)OC[C@H]([C@H]2O[C@OFC)43-B+,CC1(C)OC[C@H]([C@H]2O[C@@FC)43-B+,33,replace O at position 24 with @,flow_matching,0.3,2.0,65,250
211,replace,25.0,H,F,CC1(C)OC[C@H]([C@H]2O[C@@FC)43-B+,CC1(C)OC[C@H]([C@H]2O[C@@HC)43-B+,33,replace F at position 25 with H,flow_matching,0.3,2.0,65,250
212,replace,26.0,],C,CC1(C)OC[C@H]([C@H]2O[C@@HC)43-B+,CC1(C)OC[C@H]([C@H]2O[C@@H])43-B+,33,replace C at position 26 with ],flow_matching,0.3,2.0,65,250
213,replace,27.0,3,),CC1(C)OC[C@H]([C@H]2O[C@@H])43-B+,CC1(C)OC[C@H]([C@H]2O[C@@H]343-B+,33,replace ) at position 27 with 3,flow_matching,0.3,2.0,65,250
214,replace,28.0,O,4,CC1(C)OC[C@H]([C@H]2O[C@@H]343-B+,CC1(C)OC[C@H]([C@H]2O[C@@H]3O3-B+,33,replace 4 at position 28 with O,flow_matching,0.3,2.0,65,250
215,replace,29.0,C,3,CC1(C)OC[C@H]([C@H]2O[C@@H]3O3-B+,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC-B+,33,replace 3 at position 29 with C,flow_matching,0.3,2.0,65,250
216,replace,30.0,(,-,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC-B+,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(B+,33,replace - at position 30 with (,flow_matching,0.3,2.0,65,250
217,replace,31.0,C,B,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(B+,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C+,33,replace B at position 31 with C,flow_matching,0.3,2.0,65,250
218,replace,32.0,),+,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C+,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C),33,replace + at position 32 with ),flow_matching,0.3,2.0,65,250
219,add,33.0,(,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C),CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(,34,add ( at position 33,flow_matching,0.3,2.0,65,250
220,add,34.0,C,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C,35,add C at position 34,flow_matching,0.3,2.0,65,250
221,add,35.0,),,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C),36,add ) at position 35,flow_matching,0.3,2.0,65,250
222,add,36.0,O,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C),CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O,37,add O at position 36,flow_matching,0.3,2.0,65,250
223,add,37.0,[,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[,38,add [ at position 37,flow_matching,0.3,2.0,65,250
224,add,38.0,C,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C,39,add C at position 38,flow_matching,0.3,2.0,65,250
225,add,39.0,@,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@,40,add @ at position 39,flow_matching,0.3,2.0,65,250
226,add,40.0,@,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@,41,add @ at position 40,flow_matching,0.3,2.0,65,250
227,add,41.0,H,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H,42,add H at position 41,flow_matching,0.3,2.0,65,250
228,add,42.0,],,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H],43,add ] at position 42,flow_matching,0.3,2.0,65,250
229,add,43.0,3,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H],CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3,44,add 3 at position 43,flow_matching,0.3,2.0,65,250
230,add,44.0,[,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[,45,add [ at position 44,flow_matching,0.3,2.0,65,250
231,add,45.0,C,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C,46,add C at position 45,flow_matching,0.3,2.0,65,250
232,add,46.0,@,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@,47,add @ at position 46,flow_matching,0.3,2.0,65,250
233,add,47.0,H,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H,48,add H at position 47,flow_matching,0.3,2.0,65,250
234,add,48.0,],,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H],49,add ] at position 48,flow_matching,0.3,2.0,65,250
235,add,49.0,2,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H],CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2,50,add 2 at position 49,flow_matching,0.3,2.0,65,250
236,add,50.0,O,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2O,51,add O at position 50,flow_matching,0.3,2.0,65,250
237,add,51.0,S,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2O,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS,52,add S at position 51,flow_matching,0.3,2.0,65,250
238,add,52.0,(,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(,53,add ( at position 52,flow_matching,0.3,2.0,65,250
239,add,53.0,C,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C,54,add C at position 53,flow_matching,0.3,2.0,65,250
240,add,54.0,),,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C),55,add ) at position 54,flow_matching,0.3,2.0,65,250
241,add,55.0,(,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C),CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(,56,add ( at position 55,flow_matching,0.3,2.0,65,250
242,add,56.0,=,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=,57,add = at position 56,flow_matching,0.3,2.0,65,250
243,add,57.0,O,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O,58,add O at position 57,flow_matching,0.3,2.0,65,250
244,add,58.0,),,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O),59,add ) at position 58,flow_matching,0.3,2.0,65,250
245,add,59.0,=,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O),CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=,60,add = at position 59,flow_matching,0.3,2.0,65,250
246,add,60.0,O,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=O,61,add O at position 60,flow_matching,0.3,2.0,65,250
247,add,61.0,),,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=O,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=O),62,add ) at position 61,flow_matching,0.3,2.0,65,250
248,add,62.0,O,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=O),CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=O)O,63,add O at position 62,flow_matching,0.3,2.0,65,250
249,add,63.0,1,,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=O)O,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=O)O1,64,add 1 at position 63,flow_matching,0.3,2.0,65,250
250,add,64.0,"
",,CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=O)O1,"CC1(C)OC[C@H]([C@H]2O[C@@H]3OC(C)(C)O[C@@H]3[C@H]2OS(C)(=O)=O)O1
",65,"add 
 at position 64",flow_matching,0.3,2.0,65,250
