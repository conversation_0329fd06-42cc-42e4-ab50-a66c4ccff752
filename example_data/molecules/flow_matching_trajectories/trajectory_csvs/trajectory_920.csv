step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,45,198
1,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,45,198
2,replace,0.0,r,c,c,r,1,replace c at position 0 with r,flow_matching,0.3,2.0,45,198
3,remove,0.0,r,,r,,0,remove r from position 0,flow_matching,0.3,2.0,45,198
4,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,45,198
5,add,1.0,S,,C,CS,2,add S at position 1,flow_matching,0.3,2.0,45,198
6,remove,0.0,C,,CS,S,1,remove C from position 0,flow_matching,0.3,2.0,45,198
7,add,1.0,\,,S,S\,2,add \ at position 1,flow_matching,0.3,2.0,45,198
8,replace,0.0,\,S,S\,\\,2,replace S at position 0 with \,flow_matching,0.3,2.0,45,198
9,replace,0.0,I,\,\\,I\,2,replace \ at position 0 with I,flow_matching,0.3,2.0,45,198
10,replace,0.0,C,I,I\,C\,2,replace I at position 0 with C,flow_matching,0.3,2.0,45,198
11,remove,1.0,\,,C\,C,1,remove \ from position 1,flow_matching,0.3,2.0,45,198
12,add,0.0,O,,C,OC,2,add O at position 0,flow_matching,0.3,2.0,45,198
13,remove,1.0,C,,OC,O,1,remove C from position 1,flow_matching,0.3,2.0,45,198
14,replace,0.0,C,O,O,C,1,replace O at position 0 with C,flow_matching,0.3,2.0,45,198
15,add,1.0,S,,C,CS,2,add S at position 1,flow_matching,0.3,2.0,45,198
16,add,0.0,/,,CS,/CS,3,add / at position 0,flow_matching,0.3,2.0,45,198
17,remove,1.0,C,,/CS,/S,2,remove C from position 1,flow_matching,0.3,2.0,45,198
18,remove,1.0,S,,/S,/,1,remove S from position 1,flow_matching,0.3,2.0,45,198
19,add,1.0,C,,/,/C,2,add C at position 1,flow_matching,0.3,2.0,45,198
20,add,2.0,(,,/C,/C(,3,add ( at position 2,flow_matching,0.3,2.0,45,198
21,replace,0.0,7,/,/C(,7C(,3,replace / at position 0 with 7,flow_matching,0.3,2.0,45,198
22,replace,2.0,B,(,7C(,7CB,3,replace ( at position 2 with B,flow_matching,0.3,2.0,45,198
23,replace,0.0,-,7,7CB,-CB,3,replace 7 at position 0 with -,flow_matching,0.3,2.0,45,198
24,replace,2.0,-,B,-CB,-C-,3,replace B at position 2 with -,flow_matching,0.3,2.0,45,198
25,replace,0.0,C,-,-C-,CC-,3,replace - at position 0 with C,flow_matching,0.3,2.0,45,198
26,replace,0.0,s,C,CC-,sC-,3,replace C at position 0 with s,flow_matching,0.3,2.0,45,198
27,add,0.0,@,,sC-,@sC-,4,add @ at position 0,flow_matching,0.3,2.0,45,198
28,add,4.0,7,,@sC-,@sC-7,5,add 7 at position 4,flow_matching,0.3,2.0,45,198
29,replace,0.0,C,@,@sC-7,CsC-7,5,replace @ at position 0 with C,flow_matching,0.3,2.0,45,198
30,replace,4.0,O,7,CsC-7,CsC-O,5,replace 7 at position 4 with O,flow_matching,0.3,2.0,45,198
31,replace,3.0,#,-,CsC-O,CsC#O,5,replace - at position 3 with #,flow_matching,0.3,2.0,45,198
32,add,0.0,7,,CsC#O,7CsC#O,6,add 7 at position 0,flow_matching,0.3,2.0,45,198
33,add,5.0,(,,7CsC#O,7CsC#(O,7,add ( at position 5,flow_matching,0.3,2.0,45,198
34,add,1.0,+,,7CsC#(O,7+CsC#(O,8,add + at position 1,flow_matching,0.3,2.0,45,198
35,replace,0.0,C,7,7+CsC#(O,C+CsC#(O,8,replace 7 at position 0 with C,flow_matching,0.3,2.0,45,198
36,replace,1.0,S,+,C+CsC#(O,CSCsC#(O,8,replace + at position 1 with S,flow_matching,0.3,2.0,45,198
37,replace,1.0,@,S,CSCsC#(O,C@CsC#(O,8,replace S at position 1 with @,flow_matching,0.3,2.0,45,198
38,add,1.0,3,,C@CsC#(O,C3@CsC#(O,9,add 3 at position 1,flow_matching,0.3,2.0,45,198
39,replace,0.0,6,C,C3@CsC#(O,63@CsC#(O,9,replace C at position 0 with 6,flow_matching,0.3,2.0,45,198
40,remove,7.0,(,,63@CsC#(O,63@CsC#O,8,remove ( from position 7,flow_matching,0.3,2.0,45,198
41,remove,2.0,@,,63@CsC#O,63CsC#O,7,remove @ from position 2,flow_matching,0.3,2.0,45,198
42,add,3.0,l,,63CsC#O,63ClsC#O,8,add l at position 3,flow_matching,0.3,2.0,45,198
43,replace,2.0,N,C,63ClsC#O,63NlsC#O,8,replace C at position 2 with N,flow_matching,0.3,2.0,45,198
44,replace,0.0,C,6,63NlsC#O,C3NlsC#O,8,replace 6 at position 0 with C,flow_matching,0.3,2.0,45,198
45,remove,3.0,l,,C3NlsC#O,C3NsC#O,7,remove l from position 3,flow_matching,0.3,2.0,45,198
46,add,3.0,(,,C3NsC#O,C3N(sC#O,8,add ( at position 3,flow_matching,0.3,2.0,45,198
47,replace,6.0,l,#,C3N(sC#O,C3N(sClO,8,replace # at position 6 with l,flow_matching,0.3,2.0,45,198
48,replace,1.0,H,3,C3N(sClO,CHN(sClO,8,replace 3 at position 1 with H,flow_matching,0.3,2.0,45,198
49,add,5.0,c,,CHN(sClO,CHN(scClO,9,add c at position 5,flow_matching,0.3,2.0,45,198
50,add,5.0,+,,CHN(scClO,CHN(s+cClO,10,add + at position 5,flow_matching,0.3,2.0,45,198
51,replace,1.0,(,H,CHN(s+cClO,C(N(s+cClO,10,replace H at position 1 with (,flow_matching,0.3,2.0,45,198
52,replace,1.0,S,(,C(N(s+cClO,CSN(s+cClO,10,replace ( at position 1 with S,flow_matching,0.3,2.0,45,198
53,add,8.0,#,,CSN(s+cClO,CSN(s+cC#lO,11,add # at position 8,flow_matching,0.3,2.0,45,198
54,replace,4.0,[,s,CSN(s+cC#lO,CSN([+cC#lO,11,replace s at position 4 with [,flow_matching,0.3,2.0,45,198
55,add,9.0,c,,CSN([+cC#lO,CSN([+cC#clO,12,add c at position 9,flow_matching,0.3,2.0,45,198
56,replace,2.0,(,N,CSN([+cC#clO,CS(([+cC#clO,12,replace N at position 2 with (,flow_matching,0.3,2.0,45,198
57,add,5.0,/,,CS(([+cC#clO,CS(([/+cC#clO,13,add / at position 5,flow_matching,0.3,2.0,45,198
58,replace,3.0,=,(,CS(([/+cC#clO,CS(=[/+cC#clO,13,replace ( at position 3 with =,flow_matching,0.3,2.0,45,198
59,replace,4.0,O,[,CS(=[/+cC#clO,CS(=O/+cC#clO,13,replace [ at position 4 with O,flow_matching,0.3,2.0,45,198
60,add,3.0,3,,CS(=O/+cC#clO,CS(3=O/+cC#clO,14,add 3 at position 3,flow_matching,0.3,2.0,45,198
61,replace,3.0,=,3,CS(3=O/+cC#clO,CS(==O/+cC#clO,14,replace 3 at position 3 with =,flow_matching,0.3,2.0,45,198
62,add,13.0,n,,CS(==O/+cC#clO,CS(==O/+cC#clnO,15,add n at position 13,flow_matching,0.3,2.0,45,198
63,replace,4.0,O,=,CS(==O/+cC#clnO,CS(=OO/+cC#clnO,15,replace = at position 4 with O,flow_matching,0.3,2.0,45,198
64,replace,5.0,),O,CS(=OO/+cC#clnO,CS(=O)/+cC#clnO,15,replace O at position 5 with ),flow_matching,0.3,2.0,45,198
65,remove,6.0,/,,CS(=O)/+cC#clnO,CS(=O)+cC#clnO,14,remove / from position 6,flow_matching,0.3,2.0,45,198
66,remove,11.0,l,,CS(=O)+cC#clnO,CS(=O)+cC#cnO,13,remove l from position 11,flow_matching,0.3,2.0,45,198
67,replace,6.0,(,+,CS(=O)+cC#cnO,CS(=O)(cC#cnO,13,replace + at position 6 with (,flow_matching,0.3,2.0,45,198
68,remove,9.0,#,,CS(=O)(cC#cnO,CS(=O)(cCcnO,12,remove # from position 9,flow_matching,0.3,2.0,45,198
69,replace,2.0,l,(,CS(=O)(cCcnO,CSl=O)(cCcnO,12,replace ( at position 2 with l,flow_matching,0.3,2.0,45,198
70,remove,5.0,),,CSl=O)(cCcnO,CSl=O(cCcnO,11,remove ) from position 5,flow_matching,0.3,2.0,45,198
71,replace,7.0,@,C,CSl=O(cCcnO,CSl=O(c@cnO,11,replace C at position 7 with @,flow_matching,0.3,2.0,45,198
72,replace,6.0,),c,CSl=O(c@cnO,CSl=O()@cnO,11,replace c at position 6 with ),flow_matching,0.3,2.0,45,198
73,replace,2.0,(,l,CSl=O()@cnO,CS(=O()@cnO,11,replace l at position 2 with (,flow_matching,0.3,2.0,45,198
74,replace,5.0,),(,CS(=O()@cnO,CS(=O))@cnO,11,replace ( at position 5 with ),flow_matching,0.3,2.0,45,198
75,replace,6.0,(,),CS(=O))@cnO,CS(=O)(@cnO,11,replace ) at position 6 with (,flow_matching,0.3,2.0,45,198
76,replace,7.0,=,@,CS(=O)(@cnO,CS(=O)(=cnO,11,replace @ at position 7 with =,flow_matching,0.3,2.0,45,198
77,replace,8.0,O,c,CS(=O)(=cnO,CS(=O)(=OnO,11,replace c at position 8 with O,flow_matching,0.3,2.0,45,198
78,add,4.0,(,,CS(=O)(=OnO,CS(=(O)(=OnO,12,add ( at position 4,flow_matching,0.3,2.0,45,198
79,add,10.0,I,,CS(=(O)(=OnO,CS(=(O)(=OInO,13,add I at position 10,flow_matching,0.3,2.0,45,198
80,add,7.0,@,,CS(=(O)(=OInO,CS(=(O)@(=OInO,14,add @ at position 7,flow_matching,0.3,2.0,45,198
81,add,13.0,B,,CS(=(O)@(=OInO,CS(=(O)@(=OInBO,15,add B at position 13,flow_matching,0.3,2.0,45,198
82,replace,4.0,O,(,CS(=(O)@(=OInBO,CS(=OO)@(=OInBO,15,replace ( at position 4 with O,flow_matching,0.3,2.0,45,198
83,replace,10.0,+,O,CS(=OO)@(=OInBO,CS(=OO)@(=+InBO,15,replace O at position 10 with +,flow_matching,0.3,2.0,45,198
84,replace,11.0,=,I,CS(=OO)@(=+InBO,CS(=OO)@(=+=nBO,15,replace I at position 11 with =,flow_matching,0.3,2.0,45,198
85,replace,5.0,),O,CS(=OO)@(=+=nBO,CS(=O))@(=+=nBO,15,replace O at position 5 with ),flow_matching,0.3,2.0,45,198
86,replace,6.0,(,),CS(=O))@(=+=nBO,CS(=O)(@(=+=nBO,15,replace ) at position 6 with (,flow_matching,0.3,2.0,45,198
87,replace,7.0,=,@,CS(=O)(@(=+=nBO,CS(=O)(=(=+=nBO,15,replace @ at position 7 with =,flow_matching,0.3,2.0,45,198
88,add,15.0,I,,CS(=O)(=(=+=nBO,CS(=O)(=(=+=nBOI,16,add I at position 15,flow_matching,0.3,2.0,45,198
89,replace,8.0,O,(,CS(=O)(=(=+=nBOI,CS(=O)(=O=+=nBOI,16,replace ( at position 8 with O,flow_matching,0.3,2.0,45,198
90,add,15.0,4,,CS(=O)(=O=+=nBOI,CS(=O)(=O=+=nBO4I,17,add 4 at position 15,flow_matching,0.3,2.0,45,198
91,replace,9.0,),=,CS(=O)(=O=+=nBO4I,CS(=O)(=O)+=nBO4I,17,replace = at position 9 with ),flow_matching,0.3,2.0,45,198
92,replace,5.0,n,),CS(=O)(=O)+=nBO4I,CS(=On(=O)+=nBO4I,17,replace ) at position 5 with n,flow_matching,0.3,2.0,45,198
93,replace,3.0,2,=,CS(=On(=O)+=nBO4I,CS(2On(=O)+=nBO4I,17,replace = at position 3 with 2,flow_matching,0.3,2.0,45,198
94,remove,8.0,O,,CS(2On(=O)+=nBO4I,CS(2On(=)+=nBO4I,16,remove O from position 8,flow_matching,0.3,2.0,45,198
95,replace,3.0,=,2,CS(2On(=)+=nBO4I,CS(=On(=)+=nBO4I,16,replace 2 at position 3 with =,flow_matching,0.3,2.0,45,198
96,add,16.0,c,,CS(=On(=)+=nBO4I,CS(=On(=)+=nBO4Ic,17,add c at position 16,flow_matching,0.3,2.0,45,198
97,add,14.0,O,,CS(=On(=)+=nBO4Ic,CS(=On(=)+=nBOO4Ic,18,add O at position 14,flow_matching,0.3,2.0,45,198
98,remove,9.0,+,,CS(=On(=)+=nBOO4Ic,CS(=On(=)=nBOO4Ic,17,remove + from position 9,flow_matching,0.3,2.0,45,198
99,replace,5.0,),n,CS(=On(=)=nBOO4Ic,CS(=O)(=)=nBOO4Ic,17,replace n at position 5 with ),flow_matching,0.3,2.0,45,198
100,add,5.0,-,,CS(=O)(=)=nBOO4Ic,CS(=O-)(=)=nBOO4Ic,18,add - at position 5,flow_matching,0.3,2.0,45,198
101,replace,5.0,),-,CS(=O-)(=)=nBOO4Ic,CS(=O))(=)=nBOO4Ic,18,replace - at position 5 with ),flow_matching,0.3,2.0,45,198
102,replace,6.0,(,),CS(=O))(=)=nBOO4Ic,CS(=O)((=)=nBOO4Ic,18,replace ) at position 6 with (,flow_matching,0.3,2.0,45,198
103,replace,7.0,=,(,CS(=O)((=)=nBOO4Ic,CS(=O)(==)=nBOO4Ic,18,replace ( at position 7 with =,flow_matching,0.3,2.0,45,198
104,replace,17.0,5,c,CS(=O)(==)=nBOO4Ic,CS(=O)(==)=nBOO4I5,18,replace c at position 17 with 5,flow_matching,0.3,2.0,45,198
105,replace,8.0,),=,CS(=O)(==)=nBOO4I5,CS(=O)(=))=nBOO4I5,18,replace = at position 8 with ),flow_matching,0.3,2.0,45,198
106,replace,8.0,O,),CS(=O)(=))=nBOO4I5,CS(=O)(=O)=nBOO4I5,18,replace ) at position 8 with O,flow_matching,0.3,2.0,45,198
107,add,15.0,H,,CS(=O)(=O)=nBOO4I5,CS(=O)(=O)=nBOOH4I5,19,add H at position 15,flow_matching,0.3,2.0,45,198
108,replace,10.0,N,=,CS(=O)(=O)=nBOOH4I5,CS(=O)(=O)NnBOOH4I5,19,replace = at position 10 with N,flow_matching,0.3,2.0,45,198
109,replace,11.0,1,n,CS(=O)(=O)NnBOOH4I5,CS(=O)(=O)N1BOOH4I5,19,replace n at position 11 with 1,flow_matching,0.3,2.0,45,198
110,add,15.0,3,,CS(=O)(=O)N1BOOH4I5,CS(=O)(=O)N1BOO3H4I5,20,add 3 at position 15,flow_matching,0.3,2.0,45,198
111,remove,17.0,4,,CS(=O)(=O)N1BOO3H4I5,CS(=O)(=O)N1BOO3HI5,19,remove 4 from position 17,flow_matching,0.3,2.0,45,198
112,remove,14.0,O,,CS(=O)(=O)N1BOO3HI5,CS(=O)(=O)N1BO3HI5,18,remove O from position 14,flow_matching,0.3,2.0,45,198
113,add,18.0,2,,CS(=O)(=O)N1BO3HI5,CS(=O)(=O)N1BO3HI52,19,add 2 at position 18,flow_matching,0.3,2.0,45,198
114,replace,8.0,(,O,CS(=O)(=O)N1BO3HI52,CS(=O)(=()N1BO3HI52,19,replace O at position 8 with (,flow_matching,0.3,2.0,45,198
115,remove,18.0,2,,CS(=O)(=()N1BO3HI52,CS(=O)(=()N1BO3HI5,18,remove 2 from position 18,flow_matching,0.3,2.0,45,198
116,replace,14.0,5,3,CS(=O)(=()N1BO3HI5,CS(=O)(=()N1BO5HI5,18,replace 3 at position 14 with 5,flow_matching,0.3,2.0,45,198
117,replace,3.0,+,=,CS(=O)(=()N1BO5HI5,CS(+O)(=()N1BO5HI5,18,replace = at position 3 with +,flow_matching,0.3,2.0,45,198
118,add,11.0,5,,CS(+O)(=()N1BO5HI5,CS(+O)(=()N51BO5HI5,19,add 5 at position 11,flow_matching,0.3,2.0,45,198
119,replace,13.0,/,B,CS(+O)(=()N51BO5HI5,CS(+O)(=()N51/O5HI5,19,replace B at position 13 with /,flow_matching,0.3,2.0,45,198
120,replace,3.0,=,+,CS(+O)(=()N51/O5HI5,CS(=O)(=()N51/O5HI5,19,replace + at position 3 with =,flow_matching,0.3,2.0,45,198
121,replace,8.0,O,(,CS(=O)(=()N51/O5HI5,CS(=O)(=O)N51/O5HI5,19,replace ( at position 8 with O,flow_matching,0.3,2.0,45,198
122,add,13.0,c,,CS(=O)(=O)N51/O5HI5,CS(=O)(=O)N51c/O5HI5,20,add c at position 13,flow_matching,0.3,2.0,45,198
123,remove,13.0,c,,CS(=O)(=O)N51c/O5HI5,CS(=O)(=O)N51/O5HI5,19,remove c from position 13,flow_matching,0.3,2.0,45,198
124,add,15.0,+,,CS(=O)(=O)N51/O5HI5,CS(=O)(=O)N51/O+5HI5,20,add + at position 15,flow_matching,0.3,2.0,45,198
125,remove,11.0,5,,CS(=O)(=O)N51/O+5HI5,CS(=O)(=O)N1/O+5HI5,19,remove 5 from position 11,flow_matching,0.3,2.0,45,198
126,replace,12.0,C,/,CS(=O)(=O)N1/O+5HI5,CS(=O)(=O)N1CO+5HI5,19,replace / at position 12 with C,flow_matching,0.3,2.0,45,198
127,add,3.0,C,,CS(=O)(=O)N1CO+5HI5,CS(C=O)(=O)N1CO+5HI5,20,add C at position 3,flow_matching,0.3,2.0,45,198
128,add,19.0,#,,CS(C=O)(=O)N1CO+5HI5,CS(C=O)(=O)N1CO+5HI#5,21,add # at position 19,flow_matching,0.3,2.0,45,198
129,replace,3.0,=,C,CS(C=O)(=O)N1CO+5HI#5,CS(==O)(=O)N1CO+5HI#5,21,replace C at position 3 with =,flow_matching,0.3,2.0,45,198
130,replace,8.0,#,=,CS(==O)(=O)N1CO+5HI#5,CS(==O)(#O)N1CO+5HI#5,21,replace = at position 8 with #,flow_matching,0.3,2.0,45,198
131,replace,0.0,o,C,CS(==O)(#O)N1CO+5HI#5,oS(==O)(#O)N1CO+5HI#5,21,replace C at position 0 with o,flow_matching,0.3,2.0,45,198
132,add,1.0,6,,oS(==O)(#O)N1CO+5HI#5,o6S(==O)(#O)N1CO+5HI#5,22,add 6 at position 1,flow_matching,0.3,2.0,45,198
133,remove,18.0,H,,o6S(==O)(#O)N1CO+5HI#5,o6S(==O)(#O)N1CO+5I#5,21,remove H from position 18,flow_matching,0.3,2.0,45,198
134,add,12.0,[,,o6S(==O)(#O)N1CO+5I#5,o6S(==O)(#O)[N1CO+5I#5,22,add [ at position 12,flow_matching,0.3,2.0,45,198
135,replace,0.0,C,o,o6S(==O)(#O)[N1CO+5I#5,C6S(==O)(#O)[N1CO+5I#5,22,replace o at position 0 with C,flow_matching,0.3,2.0,45,198
136,add,5.0,6,,C6S(==O)(#O)[N1CO+5I#5,C6S(=6=O)(#O)[N1CO+5I#5,23,add 6 at position 5,flow_matching,0.3,2.0,45,198
137,replace,17.0,l,O,C6S(=6=O)(#O)[N1CO+5I#5,C6S(=6=O)(#O)[N1Cl+5I#5,23,replace O at position 17 with l,flow_matching,0.3,2.0,45,198
138,add,4.0,N,,C6S(=6=O)(#O)[N1Cl+5I#5,C6S(N=6=O)(#O)[N1Cl+5I#5,24,add N at position 4,flow_matching,0.3,2.0,45,198
139,add,8.0,O,,C6S(N=6=O)(#O)[N1Cl+5I#5,C6S(N=6=OO)(#O)[N1Cl+5I#5,25,add O at position 8,flow_matching,0.3,2.0,45,198
140,replace,0.0,\,C,C6S(N=6=OO)(#O)[N1Cl+5I#5,\6S(N=6=OO)(#O)[N1Cl+5I#5,25,replace C at position 0 with \,flow_matching,0.3,2.0,45,198
141,add,25.0,5,,\6S(N=6=OO)(#O)[N1Cl+5I#5,\6S(N=6=OO)(#O)[N1Cl+5I#55,26,add 5 at position 25,flow_matching,0.3,2.0,45,198
142,remove,24.0,5,,\6S(N=6=OO)(#O)[N1Cl+5I#55,\6S(N=6=OO)(#O)[N1Cl+5I#5,25,remove 5 from position 24,flow_matching,0.3,2.0,45,198
143,replace,24.0,2,5,\6S(N=6=OO)(#O)[N1Cl+5I#5,\6S(N=6=OO)(#O)[N1Cl+5I#2,25,replace 5 at position 24 with 2,flow_matching,0.3,2.0,45,198
144,add,2.0,O,,\6S(N=6=OO)(#O)[N1Cl+5I#2,\6OS(N=6=OO)(#O)[N1Cl+5I#2,26,add O at position 2,flow_matching,0.3,2.0,45,198
145,replace,0.0,C,\,\6OS(N=6=OO)(#O)[N1Cl+5I#2,C6OS(N=6=OO)(#O)[N1Cl+5I#2,26,replace \ at position 0 with C,flow_matching,0.3,2.0,45,198
146,replace,14.0,5,O,C6OS(N=6=OO)(#O)[N1Cl+5I#2,C6OS(N=6=OO)(#5)[N1Cl+5I#2,26,replace O at position 14 with 5,flow_matching,0.3,2.0,45,198
147,replace,5.0,@,N,C6OS(N=6=OO)(#5)[N1Cl+5I#2,C6OS(@=6=OO)(#5)[N1Cl+5I#2,26,replace N at position 5 with @,flow_matching,0.3,2.0,45,198
148,replace,20.0,B,l,C6OS(@=6=OO)(#5)[N1Cl+5I#2,C6OS(@=6=OO)(#5)[N1CB+5I#2,26,replace l at position 20 with B,flow_matching,0.3,2.0,45,198
149,remove,22.0,5,,C6OS(@=6=OO)(#5)[N1CB+5I#2,C6OS(@=6=OO)(#5)[N1CB+I#2,25,remove 5 from position 22,flow_matching,0.3,2.0,45,198
150,replace,8.0,),=,C6OS(@=6=OO)(#5)[N1CB+I#2,C6OS(@=6)OO)(#5)[N1CB+I#2,25,replace = at position 8 with ),flow_matching,0.3,2.0,45,198
151,add,22.0,B,,C6OS(@=6)OO)(#5)[N1CB+I#2,C6OS(@=6)OO)(#5)[N1CB+BI#2,26,add B at position 22,flow_matching,0.3,2.0,45,198
152,replace,1.0,S,6,C6OS(@=6)OO)(#5)[N1CB+BI#2,CSOS(@=6)OO)(#5)[N1CB+BI#2,26,replace 6 at position 1 with S,flow_matching,0.3,2.0,45,198
153,replace,2.0,(,O,CSOS(@=6)OO)(#5)[N1CB+BI#2,CS(S(@=6)OO)(#5)[N1CB+BI#2,26,replace O at position 2 with (,flow_matching,0.3,2.0,45,198
154,remove,6.0,=,,CS(S(@=6)OO)(#5)[N1CB+BI#2,CS(S(@6)OO)(#5)[N1CB+BI#2,25,remove = from position 6,flow_matching,0.3,2.0,45,198
155,replace,3.0,=,S,CS(S(@6)OO)(#5)[N1CB+BI#2,CS(=(@6)OO)(#5)[N1CB+BI#2,25,replace S at position 3 with =,flow_matching,0.3,2.0,45,198
156,add,22.0,+,,CS(=(@6)OO)(#5)[N1CB+BI#2,CS(=(@6)OO)(#5)[N1CB+B+I#2,26,add + at position 22,flow_matching,0.3,2.0,45,198
157,replace,4.0,O,(,CS(=(@6)OO)(#5)[N1CB+B+I#2,CS(=O@6)OO)(#5)[N1CB+B+I#2,26,replace ( at position 4 with O,flow_matching,0.3,2.0,45,198
158,replace,14.0,=,),CS(=O@6)OO)(#5)[N1CB+B+I#2,CS(=O@6)OO)(#5=[N1CB+B+I#2,26,replace ) at position 14 with =,flow_matching,0.3,2.0,45,198
159,add,24.0,1,,CS(=O@6)OO)(#5=[N1CB+B+I#2,CS(=O@6)OO)(#5=[N1CB+B+I1#2,27,add 1 at position 24,flow_matching,0.3,2.0,45,198
160,replace,5.0,),@,CS(=O@6)OO)(#5=[N1CB+B+I1#2,CS(=O)6)OO)(#5=[N1CB+B+I1#2,27,replace @ at position 5 with ),flow_matching,0.3,2.0,45,198
161,replace,6.0,(,6,CS(=O)6)OO)(#5=[N1CB+B+I1#2,CS(=O)()OO)(#5=[N1CB+B+I1#2,27,replace 6 at position 6 with (,flow_matching,0.3,2.0,45,198
162,replace,7.0,=,),CS(=O)()OO)(#5=[N1CB+B+I1#2,CS(=O)(=OO)(#5=[N1CB+B+I1#2,27,replace ) at position 7 with =,flow_matching,0.3,2.0,45,198
163,replace,9.0,),O,CS(=O)(=OO)(#5=[N1CB+B+I1#2,CS(=O)(=O))(#5=[N1CB+B+I1#2,27,replace O at position 9 with ),flow_matching,0.3,2.0,45,198
164,replace,10.0,N,),CS(=O)(=O))(#5=[N1CB+B+I1#2,CS(=O)(=O)N(#5=[N1CB+B+I1#2,27,replace ) at position 10 with N,flow_matching,0.3,2.0,45,198
165,replace,11.0,1,(,CS(=O)(=O)N(#5=[N1CB+B+I1#2,CS(=O)(=O)N1#5=[N1CB+B+I1#2,27,replace ( at position 11 with 1,flow_matching,0.3,2.0,45,198
166,replace,12.0,C,#,CS(=O)(=O)N1#5=[N1CB+B+I1#2,CS(=O)(=O)N1C5=[N1CB+B+I1#2,27,replace # at position 12 with C,flow_matching,0.3,2.0,45,198
167,replace,13.0,C,5,CS(=O)(=O)N1C5=[N1CB+B+I1#2,CS(=O)(=O)N1CC=[N1CB+B+I1#2,27,replace 5 at position 13 with C,flow_matching,0.3,2.0,45,198
168,replace,14.0,C,=,CS(=O)(=O)N1CC=[N1CB+B+I1#2,CS(=O)(=O)N1CCC[N1CB+B+I1#2,27,replace = at position 14 with C,flow_matching,0.3,2.0,45,198
169,replace,15.0,(,[,CS(=O)(=O)N1CCC[N1CB+B+I1#2,CS(=O)(=O)N1CCC(N1CB+B+I1#2,27,replace [ at position 15 with (,flow_matching,0.3,2.0,45,198
170,replace,16.0,C,N,CS(=O)(=O)N1CCC(N1CB+B+I1#2,CS(=O)(=O)N1CCC(C1CB+B+I1#2,27,replace N at position 16 with C,flow_matching,0.3,2.0,45,198
171,replace,17.0,(,1,CS(=O)(=O)N1CCC(C1CB+B+I1#2,CS(=O)(=O)N1CCC(C(CB+B+I1#2,27,replace 1 at position 17 with (,flow_matching,0.3,2.0,45,198
172,replace,18.0,=,C,CS(=O)(=O)N1CCC(C(CB+B+I1#2,CS(=O)(=O)N1CCC(C(=B+B+I1#2,27,replace C at position 18 with =,flow_matching,0.3,2.0,45,198
173,replace,19.0,O,B,CS(=O)(=O)N1CCC(C(=B+B+I1#2,CS(=O)(=O)N1CCC(C(=O+B+I1#2,27,replace B at position 19 with O,flow_matching,0.3,2.0,45,198
174,replace,20.0,),+,CS(=O)(=O)N1CCC(C(=O+B+I1#2,CS(=O)(=O)N1CCC(C(=O)B+I1#2,27,replace + at position 20 with ),flow_matching,0.3,2.0,45,198
175,replace,21.0,N,B,CS(=O)(=O)N1CCC(C(=O)B+I1#2,CS(=O)(=O)N1CCC(C(=O)N+I1#2,27,replace B at position 21 with N,flow_matching,0.3,2.0,45,198
176,replace,22.0,c,+,CS(=O)(=O)N1CCC(C(=O)N+I1#2,CS(=O)(=O)N1CCC(C(=O)NcI1#2,27,replace + at position 22 with c,flow_matching,0.3,2.0,45,198
177,replace,23.0,2,I,CS(=O)(=O)N1CCC(C(=O)NcI1#2,CS(=O)(=O)N1CCC(C(=O)Nc21#2,27,replace I at position 23 with 2,flow_matching,0.3,2.0,45,198
178,replace,24.0,s,1,CS(=O)(=O)N1CCC(C(=O)Nc21#2,CS(=O)(=O)N1CCC(C(=O)Nc2s#2,27,replace 1 at position 24 with s,flow_matching,0.3,2.0,45,198
179,replace,25.0,c,#,CS(=O)(=O)N1CCC(C(=O)Nc2s#2,CS(=O)(=O)N1CCC(C(=O)Nc2sc2,27,replace # at position 25 with c,flow_matching,0.3,2.0,45,198
180,replace,26.0,3,2,CS(=O)(=O)N1CCC(C(=O)Nc2sc2,CS(=O)(=O)N1CCC(C(=O)Nc2sc3,27,replace 2 at position 26 with 3,flow_matching,0.3,2.0,45,198
181,add,27.0,c,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c,28,add c at position 27,flow_matching,0.3,2.0,45,198
182,add,28.0,(,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(,29,add ( at position 28,flow_matching,0.3,2.0,45,198
183,add,29.0,c,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c,30,add c at position 29,flow_matching,0.3,2.0,45,198
184,add,30.0,2,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2,31,add 2 at position 30,flow_matching,0.3,2.0,45,198
185,add,31.0,C,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C,32,add C at position 31,flow_matching,0.3,2.0,45,198
186,add,32.0,#,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#,33,add # at position 32,flow_matching,0.3,2.0,45,198
187,add,33.0,N,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N,34,add N at position 33,flow_matching,0.3,2.0,45,198
188,add,34.0,),,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N),35,add ) at position 34,flow_matching,0.3,2.0,45,198
189,add,35.0,C,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N),CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)C,36,add C at position 35,flow_matching,0.3,2.0,45,198
190,add,36.0,C,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)C,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CC,37,add C at position 36,flow_matching,0.3,2.0,45,198
191,add,37.0,C,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CC,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCC,38,add C at position 37,flow_matching,0.3,2.0,45,198
192,add,38.0,C,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCC,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC,39,add C at position 38,flow_matching,0.3,2.0,45,198
193,add,39.0,3,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3,40,add 3 at position 39,flow_matching,0.3,2.0,45,198
194,add,40.0,),,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3),41,add ) at position 40,flow_matching,0.3,2.0,45,198
195,add,41.0,C,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3),CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3)C,42,add C at position 41,flow_matching,0.3,2.0,45,198
196,add,42.0,C,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3)C,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3)CC,43,add C at position 42,flow_matching,0.3,2.0,45,198
197,add,43.0,1,,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3)CC,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3)CC1,44,add 1 at position 43,flow_matching,0.3,2.0,45,198
198,add,44.0,"
",,CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3)CC1,"CS(=O)(=O)N1CCC(C(=O)Nc2sc3c(c2C#N)CCCC3)CC1
",45,"add 
 at position 44",flow_matching,0.3,2.0,45,198
