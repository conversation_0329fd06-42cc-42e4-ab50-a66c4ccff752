step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,38,116
1,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,38,116
2,replace,0.0,O,],],O,1,replace ] at position 0 with O,flow_matching,0.3,2.0,38,116
3,add,0.0,(,,O,(O,2,add ( at position 0,flow_matching,0.3,2.0,38,116
4,replace,0.0,O,(,(O,OO,2,replace ( at position 0 with O,flow_matching,0.3,2.0,38,116
5,add,2.0,S,,OO,OOS,3,add S at position 2,flow_matching,0.3,2.0,38,116
6,replace,2.0,2,<PERSON>,<PERSON><PERSON>,OO2,3,replace <PERSON> at position 2 with 2,flow_matching,0.3,2.0,38,116
7,replace,2.0,],2,OO2,OO],3,replace 2 at position 2 with ],flow_matching,0.3,2.0,38,116
8,replace,2.0,l,],OO],OOl,3,replace ] at position 2 with l,flow_matching,0.3,2.0,38,116
9,replace,1.0,=,O,OOl,O=l,3,replace O at position 1 with =,flow_matching,0.3,2.0,38,116
10,replace,2.0,C,l,O=l,O=C,3,replace l at position 2 with C,flow_matching,0.3,2.0,38,116
11,replace,1.0,s,=,O=C,OsC,3,replace = at position 1 with s,flow_matching,0.3,2.0,38,116
12,add,1.0,C,,OsC,OCsC,4,add C at position 1,flow_matching,0.3,2.0,38,116
13,replace,1.0,=,C,OCsC,O=sC,4,replace C at position 1 with =,flow_matching,0.3,2.0,38,116
14,replace,2.0,C,s,O=sC,O=CC,4,replace s at position 2 with C,flow_matching,0.3,2.0,38,116
15,replace,3.0,(,C,O=CC,O=C(,4,replace C at position 3 with (,flow_matching,0.3,2.0,38,116
16,add,3.0,\,,O=C(,O=C\(,5,add \ at position 3,flow_matching,0.3,2.0,38,116
17,replace,4.0,r,(,O=C\(,O=C\r,5,replace ( at position 4 with r,flow_matching,0.3,2.0,38,116
18,remove,4.0,r,,O=C\r,O=C\,4,remove r from position 4,flow_matching,0.3,2.0,38,116
19,replace,0.0,-,O,O=C\,-=C\,4,replace O at position 0 with -,flow_matching,0.3,2.0,38,116
20,replace,2.0,F,C,-=C\,-=F\,4,replace C at position 2 with F,flow_matching,0.3,2.0,38,116
21,replace,0.0,O,-,-=F\,O=F\,4,replace - at position 0 with O,flow_matching,0.3,2.0,38,116
22,replace,2.0,C,F,O=F\,O=C\,4,replace F at position 2 with C,flow_matching,0.3,2.0,38,116
23,remove,0.0,O,,O=C\,=C\,3,remove O from position 0,flow_matching,0.3,2.0,38,116
24,replace,0.0,O,=,=C\,OC\,3,replace = at position 0 with O,flow_matching,0.3,2.0,38,116
25,add,2.0,6,,OC\,OC6\,4,add 6 at position 2,flow_matching,0.3,2.0,38,116
26,replace,1.0,=,C,OC6\,O=6\,4,replace C at position 1 with =,flow_matching,0.3,2.0,38,116
27,remove,0.0,O,,O=6\,=6\,3,remove O from position 0,flow_matching,0.3,2.0,38,116
28,add,0.0,r,,=6\,r=6\,4,add r at position 0,flow_matching,0.3,2.0,38,116
29,replace,3.0,-,\,r=6\,r=6-,4,replace \ at position 3 with -,flow_matching,0.3,2.0,38,116
30,replace,1.0,r,=,r=6-,rr6-,4,replace = at position 1 with r,flow_matching,0.3,2.0,38,116
31,replace,0.0,O,r,rr6-,Or6-,4,replace r at position 0 with O,flow_matching,0.3,2.0,38,116
32,replace,1.0,=,r,Or6-,O=6-,4,replace r at position 1 with =,flow_matching,0.3,2.0,38,116
33,replace,2.0,C,6,O=6-,O=C-,4,replace 6 at position 2 with C,flow_matching,0.3,2.0,38,116
34,replace,3.0,(,-,O=C-,O=C(,4,replace - at position 3 with (,flow_matching,0.3,2.0,38,116
35,add,4.0,N,,O=C(,O=C(N,5,add N at position 4,flow_matching,0.3,2.0,38,116
36,add,5.0,c,,O=C(N,O=C(Nc,6,add c at position 5,flow_matching,0.3,2.0,38,116
37,add,6.0,1,,O=C(Nc,O=C(Nc1,7,add 1 at position 6,flow_matching,0.3,2.0,38,116
38,remove,2.0,C,,O=C(Nc1,O=(Nc1,6,remove C from position 2,flow_matching,0.3,2.0,38,116
39,remove,5.0,1,,O=(Nc1,O=(Nc,5,remove 1 from position 5,flow_matching,0.3,2.0,38,116
40,remove,2.0,(,,O=(Nc,O=Nc,4,remove ( from position 2,flow_matching,0.3,2.0,38,116
41,replace,2.0,C,N,O=Nc,O=Cc,4,replace N at position 2 with C,flow_matching,0.3,2.0,38,116
42,add,0.0,7,,O=Cc,7O=Cc,5,add 7 at position 0,flow_matching,0.3,2.0,38,116
43,replace,0.0,O,7,7O=Cc,OO=Cc,5,replace 7 at position 0 with O,flow_matching,0.3,2.0,38,116
44,replace,1.0,=,O,OO=Cc,O==Cc,5,replace O at position 1 with =,flow_matching,0.3,2.0,38,116
45,replace,2.0,C,=,O==Cc,O=CCc,5,replace = at position 2 with C,flow_matching,0.3,2.0,38,116
46,add,1.0,-,,O=CCc,O-=CCc,6,add - at position 1,flow_matching,0.3,2.0,38,116
47,replace,4.0,/,C,O-=CCc,O-=C/c,6,replace C at position 4 with /,flow_matching,0.3,2.0,38,116
48,replace,1.0,=,-,O-=C/c,O==C/c,6,replace - at position 1 with =,flow_matching,0.3,2.0,38,116
49,add,6.0,F,,O==C/c,O==C/cF,7,add F at position 6,flow_matching,0.3,2.0,38,116
50,replace,4.0,#,/,O==C/cF,O==C#cF,7,replace / at position 4 with #,flow_matching,0.3,2.0,38,116
51,add,2.0,(,,O==C#cF,O=(=C#cF,8,add ( at position 2,flow_matching,0.3,2.0,38,116
52,replace,2.0,C,(,O=(=C#cF,O=C=C#cF,8,replace ( at position 2 with C,flow_matching,0.3,2.0,38,116
53,replace,3.0,(,=,O=C=C#cF,O=C(C#cF,8,replace = at position 3 with (,flow_matching,0.3,2.0,38,116
54,add,5.0,c,,O=C(C#cF,O=C(Cc#cF,9,add c at position 5,flow_matching,0.3,2.0,38,116
55,replace,8.0,),F,O=C(Cc#cF,O=C(Cc#c),9,replace F at position 8 with ),flow_matching,0.3,2.0,38,116
56,replace,1.0,o,=,O=C(Cc#c),OoC(Cc#c),9,replace = at position 1 with o,flow_matching,0.3,2.0,38,116
57,add,0.0,@,,OoC(Cc#c),@OoC(Cc#c),10,add @ at position 0,flow_matching,0.3,2.0,38,116
58,replace,0.0,O,@,@OoC(Cc#c),OOoC(Cc#c),10,replace @ at position 0 with O,flow_matching,0.3,2.0,38,116
59,replace,1.0,=,O,OOoC(Cc#c),O=oC(Cc#c),10,replace O at position 1 with =,flow_matching,0.3,2.0,38,116
60,add,10.0,O,,O=oC(Cc#c),O=oC(Cc#c)O,11,add O at position 10,flow_matching,0.3,2.0,38,116
61,replace,7.0,5,#,O=oC(Cc#c)O,O=oC(Cc5c)O,11,replace # at position 7 with 5,flow_matching,0.3,2.0,38,116
62,replace,2.0,C,o,O=oC(Cc5c)O,O=CC(Cc5c)O,11,replace o at position 2 with C,flow_matching,0.3,2.0,38,116
63,remove,0.0,O,,O=CC(Cc5c)O,=CC(Cc5c)O,10,remove O from position 0,flow_matching,0.3,2.0,38,116
64,replace,0.0,O,=,=CC(Cc5c)O,OCC(Cc5c)O,10,replace = at position 0 with O,flow_matching,0.3,2.0,38,116
65,add,6.0,6,,OCC(Cc5c)O,OCC(Cc65c)O,11,add 6 at position 6,flow_matching,0.3,2.0,38,116
66,add,10.0,\,,OCC(Cc65c)O,OCC(Cc65c)\O,12,add \ at position 10,flow_matching,0.3,2.0,38,116
67,remove,0.0,O,,OCC(Cc65c)\O,CC(Cc65c)\O,11,remove O from position 0,flow_matching,0.3,2.0,38,116
68,replace,9.0,C,\,CC(Cc65c)\O,CC(Cc65c)CO,11,replace \ at position 9 with C,flow_matching,0.3,2.0,38,116
69,add,7.0,6,,CC(Cc65c)CO,CC(Cc656c)CO,12,add 6 at position 7,flow_matching,0.3,2.0,38,116
70,replace,0.0,O,C,CC(Cc656c)CO,OC(Cc656c)CO,12,replace C at position 0 with O,flow_matching,0.3,2.0,38,116
71,add,11.0,@,,OC(Cc656c)CO,OC(Cc656c)C@O,13,add @ at position 11,flow_matching,0.3,2.0,38,116
72,add,13.0,),,OC(Cc656c)C@O,OC(Cc656c)C@O),14,add ) at position 13,flow_matching,0.3,2.0,38,116
73,add,6.0,l,,OC(Cc656c)C@O),OC(Cc6l56c)C@O),15,add l at position 6,flow_matching,0.3,2.0,38,116
74,remove,0.0,O,,OC(Cc6l56c)C@O),C(Cc6l56c)C@O),14,remove O from position 0,flow_matching,0.3,2.0,38,116
75,replace,0.0,F,C,C(Cc6l56c)C@O),F(Cc6l56c)C@O),14,replace C at position 0 with F,flow_matching,0.3,2.0,38,116
76,replace,6.0,I,5,F(Cc6l56c)C@O),F(Cc6lI6c)C@O),14,replace 5 at position 6 with I,flow_matching,0.3,2.0,38,116
77,add,11.0,-,,F(Cc6lI6c)C@O),F(Cc6lI6c)C-@O),15,add - at position 11,flow_matching,0.3,2.0,38,116
78,add,3.0,2,,F(Cc6lI6c)C-@O),F(C2c6lI6c)C-@O),16,add 2 at position 3,flow_matching,0.3,2.0,38,116
79,remove,6.0,l,,F(C2c6lI6c)C-@O),F(C2c6I6c)C-@O),15,remove l from position 6,flow_matching,0.3,2.0,38,116
80,add,13.0,I,,F(C2c6I6c)C-@O),F(C2c6I6c)C-@IO),16,add I at position 13,flow_matching,0.3,2.0,38,116
81,replace,0.0,O,F,F(C2c6I6c)C-@IO),O(C2c6I6c)C-@IO),16,replace F at position 0 with O,flow_matching,0.3,2.0,38,116
82,replace,1.0,=,(,O(C2c6I6c)C-@IO),O=C2c6I6c)C-@IO),16,replace ( at position 1 with =,flow_matching,0.3,2.0,38,116
83,replace,3.0,(,2,O=C2c6I6c)C-@IO),O=C(c6I6c)C-@IO),16,replace 2 at position 3 with (,flow_matching,0.3,2.0,38,116
84,replace,4.0,N,c,O=C(c6I6c)C-@IO),O=C(N6I6c)C-@IO),16,replace c at position 4 with N,flow_matching,0.3,2.0,38,116
85,replace,5.0,c,6,O=C(N6I6c)C-@IO),O=C(NcI6c)C-@IO),16,replace 6 at position 5 with c,flow_matching,0.3,2.0,38,116
86,replace,6.0,1,I,O=C(NcI6c)C-@IO),O=C(Nc16c)C-@IO),16,replace I at position 6 with 1,flow_matching,0.3,2.0,38,116
87,replace,7.0,c,6,O=C(Nc16c)C-@IO),O=C(Nc1cc)C-@IO),16,replace 6 at position 7 with c,flow_matching,0.3,2.0,38,116
88,replace,9.0,c,),O=C(Nc1cc)C-@IO),O=C(Nc1cccC-@IO),16,replace ) at position 9 with c,flow_matching,0.3,2.0,38,116
89,replace,10.0,2,C,O=C(Nc1cccC-@IO),O=C(Nc1ccc2-@IO),16,replace C at position 10 with 2,flow_matching,0.3,2.0,38,116
90,replace,11.0,n,-,O=C(Nc1ccc2-@IO),O=C(Nc1ccc2n@IO),16,replace - at position 11 with n,flow_matching,0.3,2.0,38,116
91,replace,12.0,c,@,O=C(Nc1ccc2n@IO),O=C(Nc1ccc2ncIO),16,replace @ at position 12 with c,flow_matching,0.3,2.0,38,116
92,replace,13.0,c,I,O=C(Nc1ccc2ncIO),O=C(Nc1ccc2nccO),16,replace I at position 13 with c,flow_matching,0.3,2.0,38,116
93,replace,14.0,c,O,O=C(Nc1ccc2nccO),O=C(Nc1ccc2nccc),16,replace O at position 14 with c,flow_matching,0.3,2.0,38,116
94,replace,15.0,c,),O=C(Nc1ccc2nccc),O=C(Nc1ccc2ncccc,16,replace ) at position 15 with c,flow_matching,0.3,2.0,38,116
95,add,16.0,2,,O=C(Nc1ccc2ncccc,O=C(Nc1ccc2ncccc2,17,add 2 at position 16,flow_matching,0.3,2.0,38,116
96,add,17.0,c,,O=C(Nc1ccc2ncccc2,O=C(Nc1ccc2ncccc2c,18,add c at position 17,flow_matching,0.3,2.0,38,116
97,add,18.0,1,,O=C(Nc1ccc2ncccc2c,O=C(Nc1ccc2ncccc2c1,19,add 1 at position 18,flow_matching,0.3,2.0,38,116
98,add,19.0,),,O=C(Nc1ccc2ncccc2c1,O=C(Nc1ccc2ncccc2c1),20,add ) at position 19,flow_matching,0.3,2.0,38,116
99,add,20.0,C,,O=C(Nc1ccc2ncccc2c1),O=C(Nc1ccc2ncccc2c1)C,21,add C at position 20,flow_matching,0.3,2.0,38,116
100,add,21.0,(,,O=C(Nc1ccc2ncccc2c1)C,O=C(Nc1ccc2ncccc2c1)C(,22,add ( at position 21,flow_matching,0.3,2.0,38,116
101,add,22.0,=,,O=C(Nc1ccc2ncccc2c1)C(,O=C(Nc1ccc2ncccc2c1)C(=,23,add = at position 22,flow_matching,0.3,2.0,38,116
102,add,23.0,O,,O=C(Nc1ccc2ncccc2c1)C(=,O=C(Nc1ccc2ncccc2c1)C(=O,24,add O at position 23,flow_matching,0.3,2.0,38,116
103,add,24.0,),,O=C(Nc1ccc2ncccc2c1)C(=O,O=C(Nc1ccc2ncccc2c1)C(=O),25,add ) at position 24,flow_matching,0.3,2.0,38,116
104,add,25.0,N,,O=C(Nc1ccc2ncccc2c1)C(=O),O=C(Nc1ccc2ncccc2c1)C(=O)N,26,add N at position 25,flow_matching,0.3,2.0,38,116
105,add,26.0,C,,O=C(Nc1ccc2ncccc2c1)C(=O)N,O=C(Nc1ccc2ncccc2c1)C(=O)NC,27,add C at position 26,flow_matching,0.3,2.0,38,116
106,add,27.0,1,,O=C(Nc1ccc2ncccc2c1)C(=O)NC,O=C(Nc1ccc2ncccc2c1)C(=O)NC1,28,add 1 at position 27,flow_matching,0.3,2.0,38,116
107,add,28.0,C,,O=C(Nc1ccc2ncccc2c1)C(=O)NC1,O=C(Nc1ccc2ncccc2c1)C(=O)NC1C,29,add C at position 28,flow_matching,0.3,2.0,38,116
108,add,29.0,C,,O=C(Nc1ccc2ncccc2c1)C(=O)NC1C,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CC,30,add C at position 29,flow_matching,0.3,2.0,38,116
109,add,30.0,C,,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CC,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC,31,add C at position 30,flow_matching,0.3,2.0,38,116
110,add,31.0,(,,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(,32,add ( at position 31,flow_matching,0.3,2.0,38,116
111,add,32.0,O,,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O,33,add O at position 32,flow_matching,0.3,2.0,38,116
112,add,33.0,),,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O),34,add ) at position 33,flow_matching,0.3,2.0,38,116
113,add,34.0,C,,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O),O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O)C,35,add C at position 34,flow_matching,0.3,2.0,38,116
114,add,35.0,C,,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O)C,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O)CC,36,add C at position 35,flow_matching,0.3,2.0,38,116
115,add,36.0,1,,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O)CC,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O)CC1,37,add 1 at position 36,flow_matching,0.3,2.0,38,116
116,add,37.0,"
",,O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O)CC1,"O=C(Nc1ccc2ncccc2c1)C(=O)NC1CCC(O)CC1
",38,"add 
 at position 37",flow_matching,0.3,2.0,38,116
