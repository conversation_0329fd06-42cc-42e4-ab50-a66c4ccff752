step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,66,312
1,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,66,312
2,remove,0.0,I,,I,,0,remove I from position 0,flow_matching,0.3,2.0,66,312
3,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,66,312
4,remove,0.0,+,,+,,0,remove + from position 0,flow_matching,0.3,2.0,66,312
5,add,0.0,2,,,2,1,add 2 at position 0,flow_matching,0.3,2.0,66,312
6,remove,0.0,2,,2,,0,remove 2 from position 0,flow_matching,0.3,2.0,66,312
7,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,66,312
8,add,0.0,l,,C,lC,2,add l at position 0,flow_matching,0.3,2.0,66,312
9,add,0.0,H,,lC,HlC,3,add H at position 0,flow_matching,0.3,2.0,66,312
10,replace,0.0,r,H,HlC,rlC,3,replace H at position 0 with r,flow_matching,0.3,2.0,66,312
11,replace,0.0,C,r,rlC,ClC,3,replace r at position 0 with C,flow_matching,0.3,2.0,66,312
12,replace,1.0,O,l,ClC,COC,3,replace l at position 1 with O,flow_matching,0.3,2.0,66,312
13,replace,1.0,H,O,COC,CHC,3,replace O at position 1 with H,flow_matching,0.3,2.0,66,312
14,replace,2.0,s,C,CHC,CHs,3,replace C at position 2 with s,flow_matching,0.3,2.0,66,312
15,add,3.0,@,,CHs,CHs@,4,add @ at position 3,flow_matching,0.3,2.0,66,312
16,replace,1.0,O,H,CHs@,COs@,4,replace H at position 1 with O,flow_matching,0.3,2.0,66,312
17,add,2.0,B,,COs@,COBs@,5,add B at position 2,flow_matching,0.3,2.0,66,312
18,replace,3.0,[,s,COBs@,COB[@,5,replace s at position 3 with [,flow_matching,0.3,2.0,66,312
19,replace,2.0,S,B,COB[@,COS[@,5,replace B at position 2 with S,flow_matching,0.3,2.0,66,312
20,replace,2.0,c,S,COS[@,COc[@,5,replace S at position 2 with c,flow_matching,0.3,2.0,66,312
21,replace,3.0,1,[,COc[@,COc1@,5,replace [ at position 3 with 1,flow_matching,0.3,2.0,66,312
22,add,1.0,@,,COc1@,C@Oc1@,6,add @ at position 1,flow_matching,0.3,2.0,66,312
23,remove,4.0,1,,C@Oc1@,C@Oc@,5,remove 1 from position 4,flow_matching,0.3,2.0,66,312
24,add,1.0,2,,C@Oc@,C2@Oc@,6,add 2 at position 1,flow_matching,0.3,2.0,66,312
25,replace,2.0,6,@,C2@Oc@,C26Oc@,6,replace @ at position 2 with 6,flow_matching,0.3,2.0,66,312
26,replace,4.0,-,c,C26Oc@,C26O-@,6,replace c at position 4 with -,flow_matching,0.3,2.0,66,312
27,replace,4.0,O,-,C26O-@,C26OO@,6,replace - at position 4 with O,flow_matching,0.3,2.0,66,312
28,remove,4.0,O,,C26OO@,C26O@,5,remove O from position 4,flow_matching,0.3,2.0,66,312
29,add,4.0,6,,C26O@,C26O6@,6,add 6 at position 4,flow_matching,0.3,2.0,66,312
30,replace,2.0,F,6,C26O6@,C2FO6@,6,replace 6 at position 2 with F,flow_matching,0.3,2.0,66,312
31,replace,1.0,O,2,C2FO6@,COFO6@,6,replace 2 at position 1 with O,flow_matching,0.3,2.0,66,312
32,remove,2.0,F,,COFO6@,COO6@,5,remove F from position 2,flow_matching,0.3,2.0,66,312
33,remove,0.0,C,,COO6@,OO6@,4,remove C from position 0,flow_matching,0.3,2.0,66,312
34,replace,0.0,C,O,OO6@,CO6@,4,replace O at position 0 with C,flow_matching,0.3,2.0,66,312
35,add,2.0,[,,CO6@,CO[6@,5,add [ at position 2,flow_matching,0.3,2.0,66,312
36,remove,3.0,6,,CO[6@,CO[@,4,remove 6 from position 3,flow_matching,0.3,2.0,66,312
37,remove,0.0,C,,CO[@,O[@,3,remove C from position 0,flow_matching,0.3,2.0,66,312
38,replace,0.0,C,O,O[@,C[@,3,replace O at position 0 with C,flow_matching,0.3,2.0,66,312
39,replace,1.0,O,[,C[@,CO@,3,replace [ at position 1 with O,flow_matching,0.3,2.0,66,312
40,remove,2.0,@,,CO@,CO,2,remove @ from position 2,flow_matching,0.3,2.0,66,312
41,add,0.0,/,,CO,/CO,3,add / at position 0,flow_matching,0.3,2.0,66,312
42,add,3.0,\,,/CO,/CO\,4,add \ at position 3,flow_matching,0.3,2.0,66,312
43,remove,0.0,/,,/CO\,CO\,3,remove / from position 0,flow_matching,0.3,2.0,66,312
44,replace,0.0,#,C,CO\,#O\,3,replace C at position 0 with #,flow_matching,0.3,2.0,66,312
45,replace,0.0,C,#,#O\,CO\,3,replace # at position 0 with C,flow_matching,0.3,2.0,66,312
46,replace,2.0,c,\,CO\,COc,3,replace \ at position 2 with c,flow_matching,0.3,2.0,66,312
47,remove,1.0,O,,COc,Cc,2,remove O from position 1,flow_matching,0.3,2.0,66,312
48,replace,1.0,C,c,Cc,CC,2,replace c at position 1 with C,flow_matching,0.3,2.0,66,312
49,add,2.0,s,,CC,CCs,3,add s at position 2,flow_matching,0.3,2.0,66,312
50,add,0.0,),,CCs,)CCs,4,add ) at position 0,flow_matching,0.3,2.0,66,312
51,add,4.0,S,,)CCs,)CCsS,5,add S at position 4,flow_matching,0.3,2.0,66,312
52,replace,0.0,C,),)CCsS,CCCsS,5,replace ) at position 0 with C,flow_matching,0.3,2.0,66,312
53,replace,1.0,#,C,CCCsS,C#CsS,5,replace C at position 1 with #,flow_matching,0.3,2.0,66,312
54,replace,1.0,O,#,C#CsS,COCsS,5,replace # at position 1 with O,flow_matching,0.3,2.0,66,312
55,remove,2.0,C,,COCsS,COsS,4,remove C from position 2,flow_matching,0.3,2.0,66,312
56,replace,3.0,F,S,COsS,COsF,4,replace S at position 3 with F,flow_matching,0.3,2.0,66,312
57,remove,3.0,F,,COsF,COs,3,remove F from position 3,flow_matching,0.3,2.0,66,312
58,replace,2.0,c,s,COs,COc,3,replace s at position 2 with c,flow_matching,0.3,2.0,66,312
59,add,0.0,S,,COc,SCOc,4,add S at position 0,flow_matching,0.3,2.0,66,312
60,add,3.0,O,,SCOc,SCOOc,5,add O at position 3,flow_matching,0.3,2.0,66,312
61,add,5.0,6,,SCOOc,SCOOc6,6,add 6 at position 5,flow_matching,0.3,2.0,66,312
62,add,6.0,o,,SCOOc6,SCOOc6o,7,add o at position 6,flow_matching,0.3,2.0,66,312
63,remove,0.0,S,,SCOOc6o,COOc6o,6,remove S from position 0,flow_matching,0.3,2.0,66,312
64,replace,3.0,5,c,COOc6o,COO56o,6,replace c at position 3 with 5,flow_matching,0.3,2.0,66,312
65,replace,1.0,c,O,COO56o,CcO56o,6,replace O at position 1 with c,flow_matching,0.3,2.0,66,312
66,replace,1.0,O,c,CcO56o,COO56o,6,replace c at position 1 with O,flow_matching,0.3,2.0,66,312
67,remove,5.0,o,,COO56o,COO56,5,remove o from position 5,flow_matching,0.3,2.0,66,312
68,replace,1.0,1,O,COO56,C1O56,5,replace O at position 1 with 1,flow_matching,0.3,2.0,66,312
69,add,1.0,3,,C1O56,C31O56,6,add 3 at position 1,flow_matching,0.3,2.0,66,312
70,replace,1.0,O,3,C31O56,CO1O56,6,replace 3 at position 1 with O,flow_matching,0.3,2.0,66,312
71,remove,4.0,5,,CO1O56,CO1O6,5,remove 5 from position 4,flow_matching,0.3,2.0,66,312
72,replace,2.0,c,1,CO1O6,COcO6,5,replace 1 at position 2 with c,flow_matching,0.3,2.0,66,312
73,add,5.0,B,,COcO6,COcO6B,6,add B at position 5,flow_matching,0.3,2.0,66,312
74,replace,3.0,1,O,COcO6B,COc16B,6,replace O at position 3 with 1,flow_matching,0.3,2.0,66,312
75,remove,2.0,c,,COc16B,CO16B,5,remove c from position 2,flow_matching,0.3,2.0,66,312
76,replace,2.0,c,1,CO16B,COc6B,5,replace 1 at position 2 with c,flow_matching,0.3,2.0,66,312
77,remove,3.0,6,,COc6B,COcB,4,remove 6 from position 3,flow_matching,0.3,2.0,66,312
78,replace,1.0,l,O,COcB,ClcB,4,replace O at position 1 with l,flow_matching,0.3,2.0,66,312
79,replace,1.0,O,l,ClcB,COcB,4,replace l at position 1 with O,flow_matching,0.3,2.0,66,312
80,replace,3.0,1,B,COcB,COc1,4,replace B at position 3 with 1,flow_matching,0.3,2.0,66,312
81,replace,0.0,O,C,COc1,OOc1,4,replace C at position 0 with O,flow_matching,0.3,2.0,66,312
82,replace,0.0,C,O,OOc1,COc1,4,replace O at position 0 with C,flow_matching,0.3,2.0,66,312
83,remove,0.0,C,,COc1,Oc1,3,remove C from position 0,flow_matching,0.3,2.0,66,312
84,add,3.0,o,,Oc1,Oc1o,4,add o at position 3,flow_matching,0.3,2.0,66,312
85,replace,0.0,C,O,Oc1o,Cc1o,4,replace O at position 0 with C,flow_matching,0.3,2.0,66,312
86,remove,0.0,C,,Cc1o,c1o,3,remove C from position 0,flow_matching,0.3,2.0,66,312
87,replace,1.0,c,1,c1o,cco,3,replace 1 at position 1 with c,flow_matching,0.3,2.0,66,312
88,remove,1.0,c,,cco,co,2,remove c from position 1,flow_matching,0.3,2.0,66,312
89,remove,0.0,c,,co,o,1,remove c from position 0,flow_matching,0.3,2.0,66,312
90,replace,0.0,C,o,o,C,1,replace o at position 0 with C,flow_matching,0.3,2.0,66,312
91,add,1.0,2,,C,C2,2,add 2 at position 1,flow_matching,0.3,2.0,66,312
92,add,0.0,6,,C2,6C2,3,add 6 at position 0,flow_matching,0.3,2.0,66,312
93,add,2.0,[,,6C2,6C[2,4,add [ at position 2,flow_matching,0.3,2.0,66,312
94,replace,0.0,C,6,6C[2,CC[2,4,replace 6 at position 0 with C,flow_matching,0.3,2.0,66,312
95,replace,1.0,O,C,CC[2,CO[2,4,replace C at position 1 with O,flow_matching,0.3,2.0,66,312
96,replace,1.0,-,O,CO[2,C-[2,4,replace O at position 1 with -,flow_matching,0.3,2.0,66,312
97,replace,0.0,I,C,C-[2,I-[2,4,replace C at position 0 with I,flow_matching,0.3,2.0,66,312
98,remove,0.0,I,,I-[2,-[2,3,remove I from position 0,flow_matching,0.3,2.0,66,312
99,remove,2.0,2,,-[2,-[,2,remove 2 from position 2,flow_matching,0.3,2.0,66,312
100,add,2.0,2,,-[,-[2,3,add 2 at position 2,flow_matching,0.3,2.0,66,312
101,add,1.0,(,,-[2,-([2,4,add ( at position 1,flow_matching,0.3,2.0,66,312
102,remove,2.0,[,,-([2,-(2,3,remove [ from position 2,flow_matching,0.3,2.0,66,312
103,replace,0.0,o,-,-(2,o(2,3,replace - at position 0 with o,flow_matching,0.3,2.0,66,312
104,replace,0.0,C,o,o(2,C(2,3,replace o at position 0 with C,flow_matching,0.3,2.0,66,312
105,replace,1.0,H,(,C(2,CH2,3,replace ( at position 1 with H,flow_matching,0.3,2.0,66,312
106,add,3.0,C,,CH2,CH2C,4,add C at position 3,flow_matching,0.3,2.0,66,312
107,remove,1.0,H,,CH2C,C2C,3,remove H from position 1,flow_matching,0.3,2.0,66,312
108,remove,1.0,2,,C2C,CC,2,remove 2 from position 1,flow_matching,0.3,2.0,66,312
109,add,2.0,-,,CC,CC-,3,add - at position 2,flow_matching,0.3,2.0,66,312
110,remove,2.0,-,,CC-,CC,2,remove - from position 2,flow_matching,0.3,2.0,66,312
111,add,2.0,5,,CC,CC5,3,add 5 at position 2,flow_matching,0.3,2.0,66,312
112,remove,2.0,5,,CC5,CC,2,remove 5 from position 2,flow_matching,0.3,2.0,66,312
113,add,0.0,=,,CC,=CC,3,add = at position 0,flow_matching,0.3,2.0,66,312
114,replace,2.0,],C,=CC,=C],3,replace C at position 2 with ],flow_matching,0.3,2.0,66,312
115,remove,2.0,],,=C],=C,2,remove ] from position 2,flow_matching,0.3,2.0,66,312
116,remove,0.0,=,,=C,C,1,remove = from position 0,flow_matching,0.3,2.0,66,312
117,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,66,312
118,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,66,312
119,add,0.0,],,COc,]COc,4,add ] at position 0,flow_matching,0.3,2.0,66,312
120,replace,1.0,S,C,]COc,]SOc,4,replace C at position 1 with S,flow_matching,0.3,2.0,66,312
121,add,0.0,s,,]SOc,s]SOc,5,add s at position 0,flow_matching,0.3,2.0,66,312
122,replace,0.0,C,s,s]SOc,C]SOc,5,replace s at position 0 with C,flow_matching,0.3,2.0,66,312
123,remove,0.0,C,,C]SOc,]SOc,4,remove C from position 0,flow_matching,0.3,2.0,66,312
124,remove,3.0,c,,]SOc,]SO,3,remove c from position 3,flow_matching,0.3,2.0,66,312
125,replace,0.0,C,],]SO,CSO,3,replace ] at position 0 with C,flow_matching,0.3,2.0,66,312
126,replace,1.0,(,S,CSO,C(O,3,replace S at position 1 with (,flow_matching,0.3,2.0,66,312
127,replace,1.0,O,(,C(O,COO,3,replace ( at position 1 with O,flow_matching,0.3,2.0,66,312
128,replace,2.0,c,O,COO,COc,3,replace O at position 2 with c,flow_matching,0.3,2.0,66,312
129,add,2.0,1,,COc,CO1c,4,add 1 at position 2,flow_matching,0.3,2.0,66,312
130,replace,2.0,c,1,CO1c,COcc,4,replace 1 at position 2 with c,flow_matching,0.3,2.0,66,312
131,replace,3.0,1,c,COcc,COc1,4,replace c at position 3 with 1,flow_matching,0.3,2.0,66,312
132,add,4.0,c,,COc1,COc1c,5,add c at position 4,flow_matching,0.3,2.0,66,312
133,add,5.0,c,,COc1c,COc1cc,6,add c at position 5,flow_matching,0.3,2.0,66,312
134,add,6.0,(,,COc1cc,COc1cc(,7,add ( at position 6,flow_matching,0.3,2.0,66,312
135,replace,1.0,/,O,COc1cc(,C/c1cc(,7,replace O at position 1 with /,flow_matching,0.3,2.0,66,312
136,add,0.0,(,,C/c1cc(,(C/c1cc(,8,add ( at position 0,flow_matching,0.3,2.0,66,312
137,replace,0.0,C,(,(C/c1cc(,CC/c1cc(,8,replace ( at position 0 with C,flow_matching,0.3,2.0,66,312
138,replace,1.0,O,C,CC/c1cc(,CO/c1cc(,8,replace C at position 1 with O,flow_matching,0.3,2.0,66,312
139,add,3.0,2,,CO/c1cc(,CO/2c1cc(,9,add 2 at position 3,flow_matching,0.3,2.0,66,312
140,add,4.0,H,,CO/2c1cc(,CO/2Hc1cc(,10,add H at position 4,flow_matching,0.3,2.0,66,312
141,replace,8.0,O,c,CO/2Hc1cc(,CO/2Hc1cO(,10,replace c at position 8 with O,flow_matching,0.3,2.0,66,312
142,replace,3.0,6,2,CO/2Hc1cO(,CO/6Hc1cO(,10,replace 2 at position 3 with 6,flow_matching,0.3,2.0,66,312
143,replace,2.0,c,/,CO/6Hc1cO(,COc6Hc1cO(,10,replace / at position 2 with c,flow_matching,0.3,2.0,66,312
144,add,3.0,r,,COc6Hc1cO(,COcr6Hc1cO(,11,add r at position 3,flow_matching,0.3,2.0,66,312
145,replace,3.0,1,r,COcr6Hc1cO(,COc16Hc1cO(,11,replace r at position 3 with 1,flow_matching,0.3,2.0,66,312
146,add,3.0,7,,COc16Hc1cO(,COc716Hc1cO(,12,add 7 at position 3,flow_matching,0.3,2.0,66,312
147,replace,3.0,1,7,COc716Hc1cO(,COc116Hc1cO(,12,replace 7 at position 3 with 1,flow_matching,0.3,2.0,66,312
148,replace,4.0,c,1,COc116Hc1cO(,COc1c6Hc1cO(,12,replace 1 at position 4 with c,flow_matching,0.3,2.0,66,312
149,add,7.0,s,,COc1c6Hc1cO(,COc1c6Hsc1cO(,13,add s at position 7,flow_matching,0.3,2.0,66,312
150,replace,5.0,c,6,COc1c6Hsc1cO(,COc1ccHsc1cO(,13,replace 6 at position 5 with c,flow_matching,0.3,2.0,66,312
151,replace,8.0,7,c,COc1ccHsc1cO(,COc1ccHs71cO(,13,replace c at position 8 with 7,flow_matching,0.3,2.0,66,312
152,replace,6.0,(,H,COc1ccHs71cO(,COc1cc(s71cO(,13,replace H at position 6 with (,flow_matching,0.3,2.0,66,312
153,add,8.0,3,,COc1cc(s71cO(,COc1cc(s371cO(,14,add 3 at position 8,flow_matching,0.3,2.0,66,312
154,add,0.0,@,,COc1cc(s371cO(,@COc1cc(s371cO(,15,add @ at position 0,flow_matching,0.3,2.0,66,312
155,replace,0.0,C,@,@COc1cc(s371cO(,CCOc1cc(s371cO(,15,replace @ at position 0 with C,flow_matching,0.3,2.0,66,312
156,remove,14.0,(,,CCOc1cc(s371cO(,CCOc1cc(s371cO,14,remove ( from position 14,flow_matching,0.3,2.0,66,312
157,add,3.0,n,,CCOc1cc(s371cO,CCOnc1cc(s371cO,15,add n at position 3,flow_matching,0.3,2.0,66,312
158,replace,1.0,O,C,CCOnc1cc(s371cO,COOnc1cc(s371cO,15,replace C at position 1 with O,flow_matching,0.3,2.0,66,312
159,add,12.0,#,,COOnc1cc(s371cO,COOnc1cc(s37#1cO,16,add # at position 12,flow_matching,0.3,2.0,66,312
160,add,5.0,n,,COOnc1cc(s37#1cO,COOncn1cc(s37#1cO,17,add n at position 5,flow_matching,0.3,2.0,66,312
161,remove,6.0,1,,COOncn1cc(s37#1cO,COOncncc(s37#1cO,16,remove 1 from position 6,flow_matching,0.3,2.0,66,312
162,add,15.0,6,,COOncncc(s37#1cO,COOncncc(s37#1c6O,17,add 6 at position 15,flow_matching,0.3,2.0,66,312
163,add,7.0,\,,COOncncc(s37#1c6O,COOncnc\c(s37#1c6O,18,add \ at position 7,flow_matching,0.3,2.0,66,312
164,replace,2.0,c,O,COOncnc\c(s37#1c6O,COcncnc\c(s37#1c6O,18,replace O at position 2 with c,flow_matching,0.3,2.0,66,312
165,remove,16.0,6,,COcncnc\c(s37#1c6O,COcncnc\c(s37#1cO,17,remove 6 from position 16,flow_matching,0.3,2.0,66,312
166,replace,3.0,1,n,COcncnc\c(s37#1cO,COc1cnc\c(s37#1cO,17,replace n at position 3 with 1,flow_matching,0.3,2.0,66,312
167,remove,0.0,C,,COc1cnc\c(s37#1cO,Oc1cnc\c(s37#1cO,16,remove C from position 0,flow_matching,0.3,2.0,66,312
168,replace,0.0,C,O,Oc1cnc\c(s37#1cO,Cc1cnc\c(s37#1cO,16,replace O at position 0 with C,flow_matching,0.3,2.0,66,312
169,add,2.0,C,,Cc1cnc\c(s37#1cO,CcC1cnc\c(s37#1cO,17,add C at position 2,flow_matching,0.3,2.0,66,312
170,replace,11.0,#,3,CcC1cnc\c(s37#1cO,CcC1cnc\c(s#7#1cO,17,replace 3 at position 11 with #,flow_matching,0.3,2.0,66,312
171,replace,1.0,O,c,CcC1cnc\c(s#7#1cO,COC1cnc\c(s#7#1cO,17,replace c at position 1 with O,flow_matching,0.3,2.0,66,312
172,add,8.0,B,,COC1cnc\c(s#7#1cO,COC1cnc\Bc(s#7#1cO,18,add B at position 8,flow_matching,0.3,2.0,66,312
173,replace,2.0,c,C,COC1cnc\Bc(s#7#1cO,COc1cnc\Bc(s#7#1cO,18,replace C at position 2 with c,flow_matching,0.3,2.0,66,312
174,add,9.0,=,,COc1cnc\Bc(s#7#1cO,COc1cnc\B=c(s#7#1cO,19,add = at position 9,flow_matching,0.3,2.0,66,312
175,add,8.0,I,,COc1cnc\B=c(s#7#1cO,COc1cnc\IB=c(s#7#1cO,20,add I at position 8,flow_matching,0.3,2.0,66,312
176,add,8.0,=,,COc1cnc\IB=c(s#7#1cO,COc1cnc\=IB=c(s#7#1cO,21,add = at position 8,flow_matching,0.3,2.0,66,312
177,replace,5.0,c,n,COc1cnc\=IB=c(s#7#1cO,COc1ccc\=IB=c(s#7#1cO,21,replace n at position 5 with c,flow_matching,0.3,2.0,66,312
178,remove,2.0,c,,COc1ccc\=IB=c(s#7#1cO,CO1ccc\=IB=c(s#7#1cO,20,remove c from position 2,flow_matching,0.3,2.0,66,312
179,remove,12.0,(,,CO1ccc\=IB=c(s#7#1cO,CO1ccc\=IB=cs#7#1cO,19,remove ( from position 12,flow_matching,0.3,2.0,66,312
180,add,0.0,),,CO1ccc\=IB=cs#7#1cO,)CO1ccc\=IB=cs#7#1cO,20,add ) at position 0,flow_matching,0.3,2.0,66,312
181,add,9.0,5,,)CO1ccc\=IB=cs#7#1cO,)CO1ccc\=5IB=cs#7#1cO,21,add 5 at position 9,flow_matching,0.3,2.0,66,312
182,replace,0.0,C,),)CO1ccc\=5IB=cs#7#1cO,CCO1ccc\=5IB=cs#7#1cO,21,replace ) at position 0 with C,flow_matching,0.3,2.0,66,312
183,add,16.0,],,CCO1ccc\=5IB=cs#7#1cO,CCO1ccc\=5IB=cs#]7#1cO,22,add ] at position 16,flow_matching,0.3,2.0,66,312
184,remove,7.0,\,,CCO1ccc\=5IB=cs#]7#1cO,CCO1ccc=5IB=cs#]7#1cO,21,remove \ from position 7,flow_matching,0.3,2.0,66,312
185,remove,11.0,=,,CCO1ccc=5IB=cs#]7#1cO,CCO1ccc=5IBcs#]7#1cO,20,remove = from position 11,flow_matching,0.3,2.0,66,312
186,add,7.0,B,,CCO1ccc=5IBcs#]7#1cO,CCO1cccB=5IBcs#]7#1cO,21,add B at position 7,flow_matching,0.3,2.0,66,312
187,replace,1.0,O,C,CCO1cccB=5IBcs#]7#1cO,COO1cccB=5IBcs#]7#1cO,21,replace C at position 1 with O,flow_matching,0.3,2.0,66,312
188,add,14.0,N,,COO1cccB=5IBcs#]7#1cO,COO1cccB=5IBcsN#]7#1cO,22,add N at position 14,flow_matching,0.3,2.0,66,312
189,replace,2.0,c,O,COO1cccB=5IBcsN#]7#1cO,COc1cccB=5IBcsN#]7#1cO,22,replace O at position 2 with c,flow_matching,0.3,2.0,66,312
190,replace,3.0,/,1,COc1cccB=5IBcsN#]7#1cO,COc/cccB=5IBcsN#]7#1cO,22,replace 1 at position 3 with /,flow_matching,0.3,2.0,66,312
191,remove,10.0,I,,COc/cccB=5IBcsN#]7#1cO,COc/cccB=5BcsN#]7#1cO,21,remove I from position 10,flow_matching,0.3,2.0,66,312
192,remove,10.0,B,,COc/cccB=5BcsN#]7#1cO,COc/cccB=5csN#]7#1cO,20,remove B from position 10,flow_matching,0.3,2.0,66,312
193,replace,1.0,n,O,COc/cccB=5csN#]7#1cO,Cnc/cccB=5csN#]7#1cO,20,replace O at position 1 with n,flow_matching,0.3,2.0,66,312
194,replace,1.0,O,n,Cnc/cccB=5csN#]7#1cO,COc/cccB=5csN#]7#1cO,20,replace n at position 1 with O,flow_matching,0.3,2.0,66,312
195,replace,14.0,/,],COc/cccB=5csN#]7#1cO,COc/cccB=5csN#/7#1cO,20,replace ] at position 14 with /,flow_matching,0.3,2.0,66,312
196,replace,3.0,1,/,COc/cccB=5csN#/7#1cO,COc1cccB=5csN#/7#1cO,20,replace / at position 3 with 1,flow_matching,0.3,2.0,66,312
197,replace,6.0,(,c,COc1cccB=5csN#/7#1cO,COc1cc(B=5csN#/7#1cO,20,replace c at position 6 with (,flow_matching,0.3,2.0,66,312
198,remove,10.0,c,,COc1cc(B=5csN#/7#1cO,COc1cc(B=5sN#/7#1cO,19,remove c from position 10,flow_matching,0.3,2.0,66,312
199,remove,15.0,#,,COc1cc(B=5sN#/7#1cO,COc1cc(B=5sN#/71cO,18,remove # from position 15,flow_matching,0.3,2.0,66,312
200,add,4.0,[,,COc1cc(B=5sN#/71cO,COc1[cc(B=5sN#/71cO,19,add [ at position 4,flow_matching,0.3,2.0,66,312
201,add,10.0,+,,COc1[cc(B=5sN#/71cO,COc1[cc(B=+5sN#/71cO,20,add + at position 10,flow_matching,0.3,2.0,66,312
202,replace,4.0,c,[,COc1[cc(B=+5sN#/71cO,COc1ccc(B=+5sN#/71cO,20,replace [ at position 4 with c,flow_matching,0.3,2.0,66,312
203,replace,17.0,N,1,COc1ccc(B=+5sN#/71cO,COc1ccc(B=+5sN#/7NcO,20,replace 1 at position 17 with N,flow_matching,0.3,2.0,66,312
204,add,4.0,(,,COc1ccc(B=+5sN#/7NcO,COc1(ccc(B=+5sN#/7NcO,21,add ( at position 4,flow_matching,0.3,2.0,66,312
205,add,5.0,+,,COc1(ccc(B=+5sN#/7NcO,COc1(+ccc(B=+5sN#/7NcO,22,add + at position 5,flow_matching,0.3,2.0,66,312
206,replace,4.0,c,(,COc1(+ccc(B=+5sN#/7NcO,COc1c+ccc(B=+5sN#/7NcO,22,replace ( at position 4 with c,flow_matching,0.3,2.0,66,312
207,add,7.0,7,,COc1c+ccc(B=+5sN#/7NcO,COc1c+c7cc(B=+5sN#/7NcO,23,add 7 at position 7,flow_matching,0.3,2.0,66,312
208,replace,5.0,B,+,COc1c+c7cc(B=+5sN#/7NcO,COc1cBc7cc(B=+5sN#/7NcO,23,replace + at position 5 with B,flow_matching,0.3,2.0,66,312
209,add,12.0,l,,COc1cBc7cc(B=+5sN#/7NcO,COc1cBc7cc(Bl=+5sN#/7NcO,24,add l at position 12,flow_matching,0.3,2.0,66,312
210,replace,9.0,3,c,COc1cBc7cc(Bl=+5sN#/7NcO,COc1cBc7c3(Bl=+5sN#/7NcO,24,replace c at position 9 with 3,flow_matching,0.3,2.0,66,312
211,replace,0.0,),C,COc1cBc7c3(Bl=+5sN#/7NcO,)Oc1cBc7c3(Bl=+5sN#/7NcO,24,replace C at position 0 with ),flow_matching,0.3,2.0,66,312
212,replace,19.0,),/,)Oc1cBc7c3(Bl=+5sN#/7NcO,)Oc1cBc7c3(Bl=+5sN#)7NcO,24,replace / at position 19 with ),flow_matching,0.3,2.0,66,312
213,replace,0.0,C,),)Oc1cBc7c3(Bl=+5sN#)7NcO,COc1cBc7c3(Bl=+5sN#)7NcO,24,replace ) at position 0 with C,flow_matching,0.3,2.0,66,312
214,replace,0.0,/,C,COc1cBc7c3(Bl=+5sN#)7NcO,/Oc1cBc7c3(Bl=+5sN#)7NcO,24,replace C at position 0 with /,flow_matching,0.3,2.0,66,312
215,replace,0.0,C,/,/Oc1cBc7c3(Bl=+5sN#)7NcO,COc1cBc7c3(Bl=+5sN#)7NcO,24,replace / at position 0 with C,flow_matching,0.3,2.0,66,312
216,replace,5.0,c,B,COc1cBc7c3(Bl=+5sN#)7NcO,COc1ccc7c3(Bl=+5sN#)7NcO,24,replace B at position 5 with c,flow_matching,0.3,2.0,66,312
217,replace,14.0,r,+,COc1ccc7c3(Bl=+5sN#)7NcO,COc1ccc7c3(Bl=r5sN#)7NcO,24,replace + at position 14 with r,flow_matching,0.3,2.0,66,312
218,remove,3.0,1,,COc1ccc7c3(Bl=r5sN#)7NcO,COcccc7c3(Bl=r5sN#)7NcO,23,remove 1 from position 3,flow_matching,0.3,2.0,66,312
219,remove,11.0,l,,COcccc7c3(Bl=r5sN#)7NcO,COcccc7c3(B=r5sN#)7NcO,22,remove l from position 11,flow_matching,0.3,2.0,66,312
220,replace,14.0,\,s,COcccc7c3(B=r5sN#)7NcO,COcccc7c3(B=r5\N#)7NcO,22,replace s at position 14 with \,flow_matching,0.3,2.0,66,312
221,add,15.0,7,,COcccc7c3(B=r5\N#)7NcO,COcccc7c3(B=r5\7N#)7NcO,23,add 7 at position 15,flow_matching,0.3,2.0,66,312
222,replace,6.0,),7,COcccc7c3(B=r5\7N#)7NcO,COcccc)c3(B=r5\7N#)7NcO,23,replace 7 at position 6 with ),flow_matching,0.3,2.0,66,312
223,add,0.0,c,,COcccc)c3(B=r5\7N#)7NcO,cCOcccc)c3(B=r5\7N#)7NcO,24,add c at position 0,flow_matching,0.3,2.0,66,312
224,add,2.0,-,,cCOcccc)c3(B=r5\7N#)7NcO,cC-Occcc)c3(B=r5\7N#)7NcO,25,add - at position 2,flow_matching,0.3,2.0,66,312
225,remove,21.0,7,,cC-Occcc)c3(B=r5\7N#)7NcO,cC-Occcc)c3(B=r5\7N#)NcO,24,remove 7 from position 21,flow_matching,0.3,2.0,66,312
226,remove,5.0,c,,cC-Occcc)c3(B=r5\7N#)NcO,cC-Occc)c3(B=r5\7N#)NcO,23,remove c from position 5,flow_matching,0.3,2.0,66,312
227,replace,0.0,C,c,cC-Occc)c3(B=r5\7N#)NcO,CC-Occc)c3(B=r5\7N#)NcO,23,replace c at position 0 with C,flow_matching,0.3,2.0,66,312
228,remove,21.0,c,,CC-Occc)c3(B=r5\7N#)NcO,CC-Occc)c3(B=r5\7N#)NO,22,remove c from position 21,flow_matching,0.3,2.0,66,312
229,add,17.0,+,,CC-Occc)c3(B=r5\7N#)NO,CC-Occc)c3(B=r5\7+N#)NO,23,add + at position 17,flow_matching,0.3,2.0,66,312
230,replace,1.0,O,C,CC-Occc)c3(B=r5\7+N#)NO,CO-Occc)c3(B=r5\7+N#)NO,23,replace C at position 1 with O,flow_matching,0.3,2.0,66,312
231,remove,8.0,c,,CO-Occc)c3(B=r5\7+N#)NO,CO-Occc)3(B=r5\7+N#)NO,22,remove c from position 8,flow_matching,0.3,2.0,66,312
232,add,1.0,[,,CO-Occc)3(B=r5\7+N#)NO,C[O-Occc)3(B=r5\7+N#)NO,23,add [ at position 1,flow_matching,0.3,2.0,66,312
233,replace,1.0,O,[,C[O-Occc)3(B=r5\7+N#)NO,COO-Occc)3(B=r5\7+N#)NO,23,replace [ at position 1 with O,flow_matching,0.3,2.0,66,312
234,add,11.0,C,,COO-Occc)3(B=r5\7+N#)NO,COO-Occc)3(CB=r5\7+N#)NO,24,add C at position 11,flow_matching,0.3,2.0,66,312
235,add,10.0,=,,COO-Occc)3(CB=r5\7+N#)NO,COO-Occc)3=(CB=r5\7+N#)NO,25,add = at position 10,flow_matching,0.3,2.0,66,312
236,remove,4.0,O,,COO-Occc)3=(CB=r5\7+N#)NO,COO-ccc)3=(CB=r5\7+N#)NO,24,remove O from position 4,flow_matching,0.3,2.0,66,312
237,replace,2.0,c,O,COO-ccc)3=(CB=r5\7+N#)NO,COc-ccc)3=(CB=r5\7+N#)NO,24,replace O at position 2 with c,flow_matching,0.3,2.0,66,312
238,replace,3.0,1,-,COc-ccc)3=(CB=r5\7+N#)NO,COc1ccc)3=(CB=r5\7+N#)NO,24,replace - at position 3 with 1,flow_matching,0.3,2.0,66,312
239,add,19.0,I,,COc1ccc)3=(CB=r5\7+N#)NO,COc1ccc)3=(CB=r5\7+IN#)NO,25,add I at position 19,flow_matching,0.3,2.0,66,312
240,remove,16.0,\,,COc1ccc)3=(CB=r5\7+IN#)NO,COc1ccc)3=(CB=r57+IN#)NO,24,remove \ from position 16,flow_matching,0.3,2.0,66,312
241,remove,13.0,=,,COc1ccc)3=(CB=r57+IN#)NO,COc1ccc)3=(CBr57+IN#)NO,23,remove = from position 13,flow_matching,0.3,2.0,66,312
242,replace,9.0,),=,COc1ccc)3=(CBr57+IN#)NO,COc1ccc)3)(CBr57+IN#)NO,23,replace = at position 9 with ),flow_matching,0.3,2.0,66,312
243,add,18.0,c,,COc1ccc)3)(CBr57+IN#)NO,COc1ccc)3)(CBr57+IcN#)NO,24,add c at position 18,flow_matching,0.3,2.0,66,312
244,add,24.0,4,,COc1ccc)3)(CBr57+IcN#)NO,COc1ccc)3)(CBr57+IcN#)NO4,25,add 4 at position 24,flow_matching,0.3,2.0,66,312
245,remove,20.0,#,,COc1ccc)3)(CBr57+IcN#)NO4,COc1ccc)3)(CBr57+IcN)NO4,24,remove # from position 20,flow_matching,0.3,2.0,66,312
246,replace,6.0,(,c,COc1ccc)3)(CBr57+IcN)NO4,COc1cc()3)(CBr57+IcN)NO4,24,replace c at position 6 with (,flow_matching,0.3,2.0,66,312
247,replace,8.0,r,3,COc1cc()3)(CBr57+IcN)NO4,COc1cc()r)(CBr57+IcN)NO4,24,replace 3 at position 8 with r,flow_matching,0.3,2.0,66,312
248,replace,14.0,4,5,COc1cc()r)(CBr57+IcN)NO4,COc1cc()r)(CBr47+IcN)NO4,24,replace 5 at position 14 with 4,flow_matching,0.3,2.0,66,312
249,remove,10.0,(,,COc1cc()r)(CBr47+IcN)NO4,COc1cc()r)CBr47+IcN)NO4,23,remove ( from position 10,flow_matching,0.3,2.0,66,312
250,replace,14.0,5,7,COc1cc()r)CBr47+IcN)NO4,COc1cc()r)CBr45+IcN)NO4,23,replace 7 at position 14 with 5,flow_matching,0.3,2.0,66,312
251,replace,7.0,[,),COc1cc()r)CBr45+IcN)NO4,COc1cc([r)CBr45+IcN)NO4,23,replace ) at position 7 with [,flow_matching,0.3,2.0,66,312
252,replace,8.0,C,r,COc1cc([r)CBr45+IcN)NO4,COc1cc([C)CBr45+IcN)NO4,23,replace r at position 8 with C,flow_matching,0.3,2.0,66,312
253,replace,5.0,6,c,COc1cc([C)CBr45+IcN)NO4,COc1c6([C)CBr45+IcN)NO4,23,replace c at position 5 with 6,flow_matching,0.3,2.0,66,312
254,replace,5.0,c,6,COc1c6([C)CBr45+IcN)NO4,COc1cc([C)CBr45+IcN)NO4,23,replace 6 at position 5 with c,flow_matching,0.3,2.0,66,312
255,remove,17.0,c,,COc1cc([C)CBr45+IcN)NO4,COc1cc([C)CBr45+IN)NO4,22,remove c from position 17,flow_matching,0.3,2.0,66,312
256,replace,9.0,@,),COc1cc([C)CBr45+IN)NO4,COc1cc([C@CBr45+IN)NO4,22,replace ) at position 9 with @,flow_matching,0.3,2.0,66,312
257,add,9.0,F,,COc1cc([C@CBr45+IN)NO4,COc1cc([CF@CBr45+IN)NO4,23,add F at position 9,flow_matching,0.3,2.0,66,312
258,replace,9.0,@,F,COc1cc([CF@CBr45+IN)NO4,COc1cc([C@@CBr45+IN)NO4,23,replace F at position 9 with @,flow_matching,0.3,2.0,66,312
259,replace,10.0,H,@,COc1cc([C@@CBr45+IN)NO4,COc1cc([C@HCBr45+IN)NO4,23,replace @ at position 10 with H,flow_matching,0.3,2.0,66,312
260,replace,11.0,],C,COc1cc([C@HCBr45+IN)NO4,COc1cc([C@H]Br45+IN)NO4,23,replace C at position 11 with ],flow_matching,0.3,2.0,66,312
261,replace,12.0,2,B,COc1cc([C@H]Br45+IN)NO4,COc1cc([C@H]2r45+IN)NO4,23,replace B at position 12 with 2,flow_matching,0.3,2.0,66,312
262,replace,13.0,C,r,COc1cc([C@H]2r45+IN)NO4,COc1cc([C@H]2C45+IN)NO4,23,replace r at position 13 with C,flow_matching,0.3,2.0,66,312
263,replace,14.0,(,4,COc1cc([C@H]2C45+IN)NO4,COc1cc([C@H]2C(5+IN)NO4,23,replace 4 at position 14 with (,flow_matching,0.3,2.0,66,312
264,replace,15.0,C,5,COc1cc([C@H]2C(5+IN)NO4,COc1cc([C@H]2C(C+IN)NO4,23,replace 5 at position 15 with C,flow_matching,0.3,2.0,66,312
265,replace,16.0,(,+,COc1cc([C@H]2C(C+IN)NO4,COc1cc([C@H]2C(C(IN)NO4,23,replace + at position 16 with (,flow_matching,0.3,2.0,66,312
266,replace,17.0,=,I,COc1cc([C@H]2C(C(IN)NO4,COc1cc([C@H]2C(C(=N)NO4,23,replace I at position 17 with =,flow_matching,0.3,2.0,66,312
267,replace,18.0,O,N,COc1cc([C@H]2C(C(=N)NO4,COc1cc([C@H]2C(C(=O)NO4,23,replace N at position 18 with O,flow_matching,0.3,2.0,66,312
268,replace,21.0,c,O,COc1cc([C@H]2C(C(=O)NO4,COc1cc([C@H]2C(C(=O)Nc4,23,replace O at position 21 with c,flow_matching,0.3,2.0,66,312
269,replace,22.0,3,4,COc1cc([C@H]2C(C(=O)Nc4,COc1cc([C@H]2C(C(=O)Nc3,23,replace 4 at position 22 with 3,flow_matching,0.3,2.0,66,312
270,add,23.0,c,,COc1cc([C@H]2C(C(=O)Nc3,COc1cc([C@H]2C(C(=O)Nc3c,24,add c at position 23,flow_matching,0.3,2.0,66,312
271,add,24.0,c,,COc1cc([C@H]2C(C(=O)Nc3c,COc1cc([C@H]2C(C(=O)Nc3cc,25,add c at position 24,flow_matching,0.3,2.0,66,312
272,add,25.0,c,,COc1cc([C@H]2C(C(=O)Nc3cc,COc1cc([C@H]2C(C(=O)Nc3ccc,26,add c at position 25,flow_matching,0.3,2.0,66,312
273,add,26.0,c,,COc1cc([C@H]2C(C(=O)Nc3ccc,COc1cc([C@H]2C(C(=O)Nc3cccc,27,add c at position 26,flow_matching,0.3,2.0,66,312
274,add,27.0,n,,COc1cc([C@H]2C(C(=O)Nc3cccc,COc1cc([C@H]2C(C(=O)Nc3ccccn,28,add n at position 27,flow_matching,0.3,2.0,66,312
275,add,28.0,3,,COc1cc([C@H]2C(C(=O)Nc3ccccn,COc1cc([C@H]2C(C(=O)Nc3ccccn3,29,add 3 at position 28,flow_matching,0.3,2.0,66,312
276,add,29.0,),,COc1cc([C@H]2C(C(=O)Nc3ccccn3,COc1cc([C@H]2C(C(=O)Nc3ccccn3),30,add ) at position 29,flow_matching,0.3,2.0,66,312
277,add,30.0,=,,COc1cc([C@H]2C(C(=O)Nc3ccccn3),COc1cc([C@H]2C(C(=O)Nc3ccccn3)=,31,add = at position 30,flow_matching,0.3,2.0,66,312
278,add,31.0,C,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C,32,add C at position 31,flow_matching,0.3,2.0,66,312
279,add,32.0,(,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(,33,add ( at position 32,flow_matching,0.3,2.0,66,312
280,add,33.0,C,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C,34,add C at position 33,flow_matching,0.3,2.0,66,312
281,add,34.0,),,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C),35,add ) at position 34,flow_matching,0.3,2.0,66,312
282,add,35.0,N,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C),COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)N,36,add N at position 35,flow_matching,0.3,2.0,66,312
283,add,36.0,C,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)N,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC,37,add C at position 36,flow_matching,0.3,2.0,66,312
284,add,37.0,3,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3,38,add 3 at position 37,flow_matching,0.3,2.0,66,312
285,add,38.0,=,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=,39,add = at position 38,flow_matching,0.3,2.0,66,312
286,add,39.0,C,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C,40,add C at position 39,flow_matching,0.3,2.0,66,312
287,add,40.0,2,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2,41,add 2 at position 40,flow_matching,0.3,2.0,66,312
288,add,41.0,C,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C,42,add C at position 41,flow_matching,0.3,2.0,66,312
289,add,42.0,(,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(,43,add ( at position 42,flow_matching,0.3,2.0,66,312
290,add,43.0,=,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=,44,add = at position 43,flow_matching,0.3,2.0,66,312
291,add,44.0,O,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O,45,add O at position 44,flow_matching,0.3,2.0,66,312
292,add,45.0,),,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O),46,add ) at position 45,flow_matching,0.3,2.0,66,312
293,add,46.0,C,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O),COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)C,47,add C at position 46,flow_matching,0.3,2.0,66,312
294,add,47.0,C,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)C,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CC,48,add C at position 47,flow_matching,0.3,2.0,66,312
295,add,48.0,C,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CC,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC,49,add C at position 48,flow_matching,0.3,2.0,66,312
296,add,49.0,3,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3,50,add 3 at position 49,flow_matching,0.3,2.0,66,312
297,add,50.0,),,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3),51,add ) at position 50,flow_matching,0.3,2.0,66,312
298,add,51.0,c,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3),COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)c,52,add c at position 51,flow_matching,0.3,2.0,66,312
299,add,52.0,c,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)c,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)cc,53,add c at position 52,flow_matching,0.3,2.0,66,312
300,add,53.0,c,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)cc,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc,54,add c at position 53,flow_matching,0.3,2.0,66,312
301,add,54.0,1,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1,55,add 1 at position 54,flow_matching,0.3,2.0,66,312
302,add,55.0,O,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1O,56,add O at position 55,flow_matching,0.3,2.0,66,312
303,add,56.0,C,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1O,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OC,57,add C at position 56,flow_matching,0.3,2.0,66,312
304,add,57.0,c,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OC,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc,58,add c at position 57,flow_matching,0.3,2.0,66,312
305,add,58.0,1,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1,59,add 1 at position 58,flow_matching,0.3,2.0,66,312
306,add,59.0,c,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1c,60,add c at position 59,flow_matching,0.3,2.0,66,312
307,add,60.0,c,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1c,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1cc,61,add c at position 60,flow_matching,0.3,2.0,66,312
308,add,61.0,c,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1cc,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1ccc,62,add c at position 61,flow_matching,0.3,2.0,66,312
309,add,62.0,c,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1ccc,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1cccc,63,add c at position 62,flow_matching,0.3,2.0,66,312
310,add,63.0,c,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1cccc,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1ccccc,64,add c at position 63,flow_matching,0.3,2.0,66,312
311,add,64.0,1,,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1ccccc,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1ccccc1,65,add 1 at position 64,flow_matching,0.3,2.0,66,312
312,add,65.0,"
",,COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1ccccc1,"COc1cc([C@H]2C(C(=O)Nc3ccccn3)=C(C)NC3=C2C(=O)CCC3)ccc1OCc1ccccc1
",66,"add 
 at position 65",flow_matching,0.3,2.0,66,312
