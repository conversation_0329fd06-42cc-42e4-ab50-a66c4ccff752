step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,34,116
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,34,116
2,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,34,116
3,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,34,116
4,add,1.0,s,,F,Fs,2,add s at position 1,flow_matching,0.3,2.0,34,116
5,remove,1.0,s,,Fs,F,1,remove s from position 1,flow_matching,0.3,2.0,34,116
6,add,0.0,\,,F,\F,2,add \ at position 0,flow_matching,0.3,2.0,34,116
7,replace,1.0,S,F,\F,\S,2,replace F at position 1 with S,flow_matching,0.3,2.0,34,116
8,add,2.0,(,,\S,\S(,3,add ( at position 2,flow_matching,0.3,2.0,34,116
9,remove,1.0,S,,\S(,\(,2,remove S from position 1,flow_matching,0.3,2.0,34,116
10,remove,1.0,(,,\(,\,1,remove ( from position 1,flow_matching,0.3,2.0,34,116
11,replace,0.0,C,\,\,C,1,replace \ at position 0 with C,flow_matching,0.3,2.0,34,116
12,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,34,116
13,add,2.0,C,,CC,CCC,3,add C at position 2,flow_matching,0.3,2.0,34,116
14,remove,1.0,C,,CCC,CC,2,remove C from position 1,flow_matching,0.3,2.0,34,116
15,replace,1.0,r,C,CC,Cr,2,replace C at position 1 with r,flow_matching,0.3,2.0,34,116
16,add,2.0,s,,Cr,Crs,3,add s at position 2,flow_matching,0.3,2.0,34,116
17,add,0.0,N,,Crs,NCrs,4,add N at position 0,flow_matching,0.3,2.0,34,116
18,remove,0.0,N,,NCrs,Crs,3,remove N from position 0,flow_matching,0.3,2.0,34,116
19,replace,1.0,C,r,Crs,CCs,3,replace r at position 1 with C,flow_matching,0.3,2.0,34,116
20,replace,2.0,C,s,CCs,CCC,3,replace s at position 2 with C,flow_matching,0.3,2.0,34,116
21,add,3.0,[,,CCC,CCC[,4,add [ at position 3,flow_matching,0.3,2.0,34,116
22,add,1.0,o,,CCC[,CoCC[,5,add o at position 1,flow_matching,0.3,2.0,34,116
23,remove,1.0,o,,CoCC[,CCC[,4,remove o from position 1,flow_matching,0.3,2.0,34,116
24,replace,3.0,c,[,CCC[,CCCc,4,replace [ at position 3 with c,flow_matching,0.3,2.0,34,116
25,add,4.0,l,,CCCc,CCCcl,5,add l at position 4,flow_matching,0.3,2.0,34,116
26,remove,1.0,C,,CCCcl,CCcl,4,remove C from position 1,flow_matching,0.3,2.0,34,116
27,add,2.0,c,,CCcl,CCccl,5,add c at position 2,flow_matching,0.3,2.0,34,116
28,add,4.0,\,,CCccl,CCcc\l,6,add \ at position 4,flow_matching,0.3,2.0,34,116
29,add,3.0,l,,CCcc\l,CCclc\l,7,add l at position 3,flow_matching,0.3,2.0,34,116
30,add,1.0,\,,CCclc\l,C\Cclc\l,8,add \ at position 1,flow_matching,0.3,2.0,34,116
31,remove,2.0,C,,C\Cclc\l,C\clc\l,7,remove C from position 2,flow_matching,0.3,2.0,34,116
32,add,7.0,1,,C\clc\l,C\clc\l1,8,add 1 at position 7,flow_matching,0.3,2.0,34,116
33,add,4.0,2,,C\clc\l1,C\cl2c\l1,9,add 2 at position 4,flow_matching,0.3,2.0,34,116
34,add,1.0,],,C\cl2c\l1,C]\cl2c\l1,10,add ] at position 1,flow_matching,0.3,2.0,34,116
35,add,7.0,s,,C]\cl2c\l1,C]\cl2cs\l1,11,add s at position 7,flow_matching,0.3,2.0,34,116
36,add,10.0,N,,C]\cl2cs\l1,C]\cl2cs\lN1,12,add N at position 10,flow_matching,0.3,2.0,34,116
37,add,7.0,N,,C]\cl2cs\lN1,C]\cl2cNs\lN1,13,add N at position 7,flow_matching,0.3,2.0,34,116
38,remove,8.0,s,,C]\cl2cNs\lN1,C]\cl2cN\lN1,12,remove s from position 8,flow_matching,0.3,2.0,34,116
39,add,11.0,2,,C]\cl2cN\lN1,C]\cl2cN\lN21,13,add 2 at position 11,flow_matching,0.3,2.0,34,116
40,add,2.0,#,,C]\cl2cN\lN21,C]#\cl2cN\lN21,14,add # at position 2,flow_matching,0.3,2.0,34,116
41,replace,1.0,C,],C]#\cl2cN\lN21,CC#\cl2cN\lN21,14,replace ] at position 1 with C,flow_matching,0.3,2.0,34,116
42,remove,3.0,\,,CC#\cl2cN\lN21,CC#cl2cN\lN21,13,remove \ from position 3,flow_matching,0.3,2.0,34,116
43,replace,2.0,C,#,CC#cl2cN\lN21,CCCcl2cN\lN21,13,replace # at position 2 with C,flow_matching,0.3,2.0,34,116
44,add,6.0,),,CCCcl2cN\lN21,CCCcl2)cN\lN21,14,add ) at position 6,flow_matching,0.3,2.0,34,116
45,remove,4.0,l,,CCCcl2)cN\lN21,CCCc2)cN\lN21,13,remove l from position 4,flow_matching,0.3,2.0,34,116
46,replace,3.0,[,c,CCCc2)cN\lN21,CCC[2)cN\lN21,13,replace c at position 3 with [,flow_matching,0.3,2.0,34,116
47,add,8.0,2,,CCC[2)cN\lN21,CCC[2)cN2\lN21,14,add 2 at position 8,flow_matching,0.3,2.0,34,116
48,replace,4.0,C,2,CCC[2)cN2\lN21,CCC[C)cN2\lN21,14,replace 2 at position 4 with C,flow_matching,0.3,2.0,34,116
49,replace,11.0,c,N,CCC[C)cN2\lN21,CCC[C)cN2\lc21,14,replace N at position 11 with c,flow_matching,0.3,2.0,34,116
50,replace,5.0,H,),CCC[C)cN2\lc21,CCC[CHcN2\lc21,14,replace ) at position 5 with H,flow_matching,0.3,2.0,34,116
51,replace,0.0,[,C,CCC[CHcN2\lc21,[CC[CHcN2\lc21,14,replace C at position 0 with [,flow_matching,0.3,2.0,34,116
52,add,2.0,s,,[CC[CHcN2\lc21,[CsC[CHcN2\lc21,15,add s at position 2,flow_matching,0.3,2.0,34,116
53,replace,0.0,C,[,[CsC[CHcN2\lc21,CCsC[CHcN2\lc21,15,replace [ at position 0 with C,flow_matching,0.3,2.0,34,116
54,remove,0.0,C,,CCsC[CHcN2\lc21,CsC[CHcN2\lc21,14,remove C from position 0,flow_matching,0.3,2.0,34,116
55,replace,1.0,C,s,CsC[CHcN2\lc21,CCC[CHcN2\lc21,14,replace s at position 1 with C,flow_matching,0.3,2.0,34,116
56,replace,5.0,@,H,CCC[CHcN2\lc21,CCC[C@cN2\lc21,14,replace H at position 5 with @,flow_matching,0.3,2.0,34,116
57,replace,13.0,(,1,CCC[C@cN2\lc21,CCC[C@cN2\lc2(,14,replace 1 at position 13 with (,flow_matching,0.3,2.0,34,116
58,add,1.0,5,,CCC[C@cN2\lc2(,C5CC[C@cN2\lc2(,15,add 5 at position 1,flow_matching,0.3,2.0,34,116
59,replace,9.0,(,2,C5CC[C@cN2\lc2(,C5CC[C@cN(\lc2(,15,replace 2 at position 9 with (,flow_matching,0.3,2.0,34,116
60,replace,1.0,C,5,C5CC[C@cN(\lc2(,CCCC[C@cN(\lc2(,15,replace 5 at position 1 with C,flow_matching,0.3,2.0,34,116
61,replace,3.0,[,C,CCCC[C@cN(\lc2(,CCC[[C@cN(\lc2(,15,replace C at position 3 with [,flow_matching,0.3,2.0,34,116
62,replace,4.0,C,[,CCC[[C@cN(\lc2(,CCC[CC@cN(\lc2(,15,replace [ at position 4 with C,flow_matching,0.3,2.0,34,116
63,remove,14.0,(,,CCC[CC@cN(\lc2(,CCC[CC@cN(\lc2,14,remove ( from position 14,flow_matching,0.3,2.0,34,116
64,remove,11.0,l,,CCC[CC@cN(\lc2,CCC[CC@cN(\c2,13,remove l from position 11,flow_matching,0.3,2.0,34,116
65,replace,5.0,@,C,CCC[CC@cN(\c2,CCC[C@@cN(\c2,13,replace C at position 5 with @,flow_matching,0.3,2.0,34,116
66,remove,12.0,2,,CCC[C@@cN(\c2,CCC[C@@cN(\c,12,remove 2 from position 12,flow_matching,0.3,2.0,34,116
67,replace,8.0,6,N,CCC[C@@cN(\c,CCC[C@@c6(\c,12,replace N at position 8 with 6,flow_matching,0.3,2.0,34,116
68,add,11.0,I,,CCC[C@@c6(\c,CCC[C@@c6(\Ic,13,add I at position 11,flow_matching,0.3,2.0,34,116
69,replace,1.0,+,C,CCC[C@@c6(\Ic,C+C[C@@c6(\Ic,13,replace C at position 1 with +,flow_matching,0.3,2.0,34,116
70,replace,1.0,C,+,C+C[C@@c6(\Ic,CCC[C@@c6(\Ic,13,replace + at position 1 with C,flow_matching,0.3,2.0,34,116
71,replace,11.0,2,I,CCC[C@@c6(\Ic,CCC[C@@c6(\2c,13,replace I at position 11 with 2,flow_matching,0.3,2.0,34,116
72,remove,2.0,C,,CCC[C@@c6(\2c,CC[C@@c6(\2c,12,remove C from position 2,flow_matching,0.3,2.0,34,116
73,remove,9.0,\,,CC[C@@c6(\2c,CC[C@@c6(2c,11,remove \ from position 9,flow_matching,0.3,2.0,34,116
74,replace,2.0,C,[,CC[C@@c6(2c,CCCC@@c6(2c,11,replace [ at position 2 with C,flow_matching,0.3,2.0,34,116
75,add,3.0,#,,CCCC@@c6(2c,CCC#C@@c6(2c,12,add # at position 3,flow_matching,0.3,2.0,34,116
76,add,10.0,2,,CCC#C@@c6(2c,CCC#C@@c6(22c,13,add 2 at position 10,flow_matching,0.3,2.0,34,116
77,replace,2.0,#,C,CCC#C@@c6(22c,CC##C@@c6(22c,13,replace C at position 2 with #,flow_matching,0.3,2.0,34,116
78,add,10.0,(,,CC##C@@c6(22c,CC##C@@c6((22c,14,add ( at position 10,flow_matching,0.3,2.0,34,116
79,replace,2.0,C,#,CC##C@@c6((22c,CCC#C@@c6((22c,14,replace # at position 2 with C,flow_matching,0.3,2.0,34,116
80,replace,3.0,[,#,CCC#C@@c6((22c,CCC[C@@c6((22c,14,replace # at position 3 with [,flow_matching,0.3,2.0,34,116
81,replace,6.0,H,@,CCC[C@@c6((22c,CCC[C@Hc6((22c,14,replace @ at position 6 with H,flow_matching,0.3,2.0,34,116
82,replace,7.0,],c,CCC[C@Hc6((22c,CCC[C@H]6((22c,14,replace c at position 7 with ],flow_matching,0.3,2.0,34,116
83,remove,6.0,H,,CCC[C@H]6((22c,CCC[C@]6((22c,13,remove H from position 6,flow_matching,0.3,2.0,34,116
84,add,9.0,H,,CCC[C@]6((22c,CCC[C@]6(H(22c,14,add H at position 9,flow_matching,0.3,2.0,34,116
85,replace,11.0,l,2,CCC[C@]6(H(22c,CCC[C@]6(H(l2c,14,replace 2 at position 11 with l,flow_matching,0.3,2.0,34,116
86,add,13.0,O,,CCC[C@]6(H(l2c,CCC[C@]6(H(l2Oc,15,add O at position 13,flow_matching,0.3,2.0,34,116
87,remove,6.0,],,CCC[C@]6(H(l2Oc,CCC[C@6(H(l2Oc,14,remove ] from position 6,flow_matching,0.3,2.0,34,116
88,remove,10.0,l,,CCC[C@6(H(l2Oc,CCC[C@6(H(2Oc,13,remove l from position 10,flow_matching,0.3,2.0,34,116
89,replace,6.0,H,6,CCC[C@6(H(2Oc,CCC[C@H(H(2Oc,13,replace 6 at position 6 with H,flow_matching,0.3,2.0,34,116
90,replace,7.0,],(,CCC[C@H(H(2Oc,CCC[C@H]H(2Oc,13,replace ( at position 7 with ],flow_matching,0.3,2.0,34,116
91,replace,8.0,(,H,CCC[C@H]H(2Oc,CCC[C@H]((2Oc,13,replace H at position 8 with (,flow_matching,0.3,2.0,34,116
92,replace,9.0,C,(,CCC[C@H]((2Oc,CCC[C@H](C2Oc,13,replace ( at position 9 with C,flow_matching,0.3,2.0,34,116
93,replace,10.0,),2,CCC[C@H](C2Oc,CCC[C@H](C)Oc,13,replace 2 at position 10 with ),flow_matching,0.3,2.0,34,116
94,replace,11.0,N,O,CCC[C@H](C)Oc,CCC[C@H](C)Nc,13,replace O at position 11 with N,flow_matching,0.3,2.0,34,116
95,replace,12.0,C,c,CCC[C@H](C)Nc,CCC[C@H](C)NC,13,replace c at position 12 with C,flow_matching,0.3,2.0,34,116
96,add,13.0,(,,CCC[C@H](C)NC,CCC[C@H](C)NC(,14,add ( at position 13,flow_matching,0.3,2.0,34,116
97,add,14.0,=,,CCC[C@H](C)NC(,CCC[C@H](C)NC(=,15,add = at position 14,flow_matching,0.3,2.0,34,116
98,add,15.0,O,,CCC[C@H](C)NC(=,CCC[C@H](C)NC(=O,16,add O at position 15,flow_matching,0.3,2.0,34,116
99,add,16.0,),,CCC[C@H](C)NC(=O,CCC[C@H](C)NC(=O),17,add ) at position 16,flow_matching,0.3,2.0,34,116
100,add,17.0,C,,CCC[C@H](C)NC(=O),CCC[C@H](C)NC(=O)C,18,add C at position 17,flow_matching,0.3,2.0,34,116
101,add,18.0,[,,CCC[C@H](C)NC(=O)C,CCC[C@H](C)NC(=O)C[,19,add [ at position 18,flow_matching,0.3,2.0,34,116
102,add,19.0,N,,CCC[C@H](C)NC(=O)C[,CCC[C@H](C)NC(=O)C[N,20,add N at position 19,flow_matching,0.3,2.0,34,116
103,add,20.0,H,,CCC[C@H](C)NC(=O)C[N,CCC[C@H](C)NC(=O)C[NH,21,add H at position 20,flow_matching,0.3,2.0,34,116
104,add,21.0,2,,CCC[C@H](C)NC(=O)C[NH,CCC[C@H](C)NC(=O)C[NH2,22,add 2 at position 21,flow_matching,0.3,2.0,34,116
105,add,22.0,+,,CCC[C@H](C)NC(=O)C[NH2,CCC[C@H](C)NC(=O)C[NH2+,23,add + at position 22,flow_matching,0.3,2.0,34,116
106,add,23.0,],,CCC[C@H](C)NC(=O)C[NH2+,CCC[C@H](C)NC(=O)C[NH2+],24,add ] at position 23,flow_matching,0.3,2.0,34,116
107,add,24.0,C,,CCC[C@H](C)NC(=O)C[NH2+],CCC[C@H](C)NC(=O)C[NH2+]C,25,add C at position 24,flow_matching,0.3,2.0,34,116
108,add,25.0,c,,CCC[C@H](C)NC(=O)C[NH2+]C,CCC[C@H](C)NC(=O)C[NH2+]Cc,26,add c at position 25,flow_matching,0.3,2.0,34,116
109,add,26.0,1,,CCC[C@H](C)NC(=O)C[NH2+]Cc,CCC[C@H](C)NC(=O)C[NH2+]Cc1,27,add 1 at position 26,flow_matching,0.3,2.0,34,116
110,add,27.0,c,,CCC[C@H](C)NC(=O)C[NH2+]Cc1,CCC[C@H](C)NC(=O)C[NH2+]Cc1c,28,add c at position 27,flow_matching,0.3,2.0,34,116
111,add,28.0,s,,CCC[C@H](C)NC(=O)C[NH2+]Cc1c,CCC[C@H](C)NC(=O)C[NH2+]Cc1cs,29,add s at position 28,flow_matching,0.3,2.0,34,116
112,add,29.0,c,,CCC[C@H](C)NC(=O)C[NH2+]Cc1cs,CCC[C@H](C)NC(=O)C[NH2+]Cc1csc,30,add c at position 29,flow_matching,0.3,2.0,34,116
113,add,30.0,c,,CCC[C@H](C)NC(=O)C[NH2+]Cc1csc,CCC[C@H](C)NC(=O)C[NH2+]Cc1cscc,31,add c at position 30,flow_matching,0.3,2.0,34,116
114,add,31.0,1,,CCC[C@H](C)NC(=O)C[NH2+]Cc1cscc,CCC[C@H](C)NC(=O)C[NH2+]Cc1cscc1,32,add 1 at position 31,flow_matching,0.3,2.0,34,116
115,add,32.0,C,,CCC[C@H](C)NC(=O)C[NH2+]Cc1cscc1,CCC[C@H](C)NC(=O)C[NH2+]Cc1cscc1C,33,add C at position 32,flow_matching,0.3,2.0,34,116
116,add,33.0,"
",,CCC[C@H](C)NC(=O)C[NH2+]Cc1cscc1C,"CCC[C@H](C)NC(=O)C[NH2+]Cc1cscc1C
",34,"add 
 at position 33",flow_matching,0.3,2.0,34,116
