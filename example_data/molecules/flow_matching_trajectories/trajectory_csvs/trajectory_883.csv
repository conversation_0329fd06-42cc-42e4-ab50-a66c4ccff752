step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,41,92
1,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,41,92
2,replace,0.0,],B,B,],1,replace <PERSON> at position 0 with ],flow_matching,0.3,2.0,41,92
3,replace,0.0,C,],],C,1,replace ] at position 0 with C,flow_matching,0.3,2.0,41,92
4,add,1.0,C,,C,CC,2,add <PERSON> at position 1,flow_matching,0.3,2.0,41,92
5,remove,1.0,C,,CC,C,1,remove C from position 1,flow_matching,0.3,2.0,41,92
6,add,1.0,<PERSON>,,<PERSON>,<PERSON>,2,add <PERSON> at position 1,flow_matching,0.3,2.0,41,92
7,remove,1.0,<PERSON>,,<PERSON>,<PERSON>,1,remove <PERSON> from position 1,flow_matching,0.3,2.0,41,92
8,add,0.0,<PERSON>,,<PERSON>,<PERSON><PERSON>,2,add I at position 0,flow_matching,0.3,2.0,41,92
9,replace,1.0,],C,IC,I],2,replace C at position 1 with ],flow_matching,0.3,2.0,41,92
10,remove,0.0,I,,I],],1,remove I from position 0,flow_matching,0.3,2.0,41,92
11,replace,0.0,C,],],C,1,replace ] at position 0 with C,flow_matching,0.3,2.0,41,92
12,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,41,92
13,add,0.0,7,,CC,7CC,3,add 7 at position 0,flow_matching,0.3,2.0,41,92
14,replace,0.0,C,7,7CC,CCC,3,replace 7 at position 0 with C,flow_matching,0.3,2.0,41,92
15,replace,2.0,S,C,CCC,CCS,3,replace C at position 2 with S,flow_matching,0.3,2.0,41,92
16,replace,2.0,+,S,CCS,CC+,3,replace S at position 2 with +,flow_matching,0.3,2.0,41,92
17,add,3.0,n,,CC+,CC+n,4,add n at position 3,flow_matching,0.3,2.0,41,92
18,remove,1.0,C,,CC+n,C+n,3,remove C from position 1,flow_matching,0.3,2.0,41,92
19,add,1.0,N,,C+n,CN+n,4,add N at position 1,flow_matching,0.3,2.0,41,92
20,replace,1.0,C,N,CN+n,CC+n,4,replace N at position 1 with C,flow_matching,0.3,2.0,41,92
21,replace,2.0,S,+,CC+n,CCSn,4,replace + at position 2 with S,flow_matching,0.3,2.0,41,92
22,replace,1.0,7,C,CCSn,C7Sn,4,replace C at position 1 with 7,flow_matching,0.3,2.0,41,92
23,replace,2.0,[,S,C7Sn,C7[n,4,replace S at position 2 with [,flow_matching,0.3,2.0,41,92
24,add,0.0,5,,C7[n,5C7[n,5,add 5 at position 0,flow_matching,0.3,2.0,41,92
25,remove,2.0,7,,5C7[n,5C[n,4,remove 7 from position 2,flow_matching,0.3,2.0,41,92
26,replace,1.0,1,C,5C[n,51[n,4,replace C at position 1 with 1,flow_matching,0.3,2.0,41,92
27,replace,0.0,C,5,51[n,C1[n,4,replace 5 at position 0 with C,flow_matching,0.3,2.0,41,92
28,replace,1.0,C,1,C1[n,CC[n,4,replace 1 at position 1 with C,flow_matching,0.3,2.0,41,92
29,add,4.0,[,,CC[n,CC[n[,5,add [ at position 4,flow_matching,0.3,2.0,41,92
30,remove,0.0,C,,CC[n[,C[n[,4,remove C from position 0,flow_matching,0.3,2.0,41,92
31,remove,2.0,n,,C[n[,C[[,3,remove n from position 2,flow_matching,0.3,2.0,41,92
32,add,1.0,7,,C[[,C7[[,4,add 7 at position 1,flow_matching,0.3,2.0,41,92
33,add,0.0,[,,C7[[,[C7[[,5,add [ at position 0,flow_matching,0.3,2.0,41,92
34,remove,2.0,7,,[C7[[,[C[[,4,remove 7 from position 2,flow_matching,0.3,2.0,41,92
35,replace,0.0,n,[,[C[[,nC[[,4,replace [ at position 0 with n,flow_matching,0.3,2.0,41,92
36,replace,0.0,C,n,nC[[,CC[[,4,replace n at position 0 with C,flow_matching,0.3,2.0,41,92
37,remove,0.0,C,,CC[[,C[[,3,remove C from position 0,flow_matching,0.3,2.0,41,92
38,add,3.0,[,,C[[,C[[[,4,add [ at position 3,flow_matching,0.3,2.0,41,92
39,replace,0.0,3,C,C[[[,3[[[,4,replace C at position 0 with 3,flow_matching,0.3,2.0,41,92
40,add,2.0,[,,3[[[,3[[[[,5,add [ at position 2,flow_matching,0.3,2.0,41,92
41,replace,0.0,C,3,3[[[[,C[[[[,5,replace 3 at position 0 with C,flow_matching,0.3,2.0,41,92
42,add,5.0,[,,C[[[[,C[[[[[,6,add [ at position 5,flow_matching,0.3,2.0,41,92
43,replace,1.0,H,[,C[[[[[,CH[[[[,6,replace [ at position 1 with H,flow_matching,0.3,2.0,41,92
44,replace,1.0,C,H,CH[[[[,CC[[[[,6,replace H at position 1 with C,flow_matching,0.3,2.0,41,92
45,add,4.0,S,,CC[[[[,CC[[S[[,7,add S at position 4,flow_matching,0.3,2.0,41,92
46,replace,2.0,S,[,CC[[S[[,CCS[S[[,7,replace [ at position 2 with S,flow_matching,0.3,2.0,41,92
47,add,0.0,C,,CCS[S[[,CCCS[S[[,8,add C at position 0,flow_matching,0.3,2.0,41,92
48,replace,2.0,S,C,CCCS[S[[,CCSS[S[[,8,replace C at position 2 with S,flow_matching,0.3,2.0,41,92
49,replace,3.0,(,S,CCSS[S[[,CCS([S[[,8,replace S at position 3 with (,flow_matching,0.3,2.0,41,92
50,replace,0.0,#,C,CCS([S[[,#CS([S[[,8,replace C at position 0 with #,flow_matching,0.3,2.0,41,92
51,replace,7.0,I,[,#CS([S[[,#CS([S[I,8,replace [ at position 7 with I,flow_matching,0.3,2.0,41,92
52,remove,6.0,[,,#CS([S[I,#CS([SI,7,remove [ from position 6,flow_matching,0.3,2.0,41,92
53,add,6.0,7,,#CS([SI,#CS([S7I,8,add 7 at position 6,flow_matching,0.3,2.0,41,92
54,add,4.0,l,,#CS([S7I,#CS(l[S7I,9,add l at position 4,flow_matching,0.3,2.0,41,92
55,replace,0.0,C,#,#CS(l[S7I,CCS(l[S7I,9,replace # at position 0 with C,flow_matching,0.3,2.0,41,92
56,replace,4.0,=,l,CCS(l[S7I,CCS(=[S7I,9,replace l at position 4 with =,flow_matching,0.3,2.0,41,92
57,replace,5.0,O,[,CCS(=[S7I,CCS(=OS7I,9,replace [ at position 5 with O,flow_matching,0.3,2.0,41,92
58,replace,6.0,),S,CCS(=OS7I,CCS(=O)7I,9,replace S at position 6 with ),flow_matching,0.3,2.0,41,92
59,replace,7.0,(,7,CCS(=O)7I,CCS(=O)(I,9,replace 7 at position 7 with (,flow_matching,0.3,2.0,41,92
60,replace,8.0,=,I,CCS(=O)(I,CCS(=O)(=,9,replace I at position 8 with =,flow_matching,0.3,2.0,41,92
61,add,9.0,O,,CCS(=O)(=,CCS(=O)(=O,10,add O at position 9,flow_matching,0.3,2.0,41,92
62,add,10.0,),,CCS(=O)(=O,CCS(=O)(=O),11,add ) at position 10,flow_matching,0.3,2.0,41,92
63,add,11.0,C,,CCS(=O)(=O),CCS(=O)(=O)C,12,add C at position 11,flow_matching,0.3,2.0,41,92
64,add,12.0,C,,CCS(=O)(=O)C,CCS(=O)(=O)CC,13,add C at position 12,flow_matching,0.3,2.0,41,92
65,add,13.0,N,,CCS(=O)(=O)CC,CCS(=O)(=O)CCN,14,add N at position 13,flow_matching,0.3,2.0,41,92
66,add,14.0,(,,CCS(=O)(=O)CCN,CCS(=O)(=O)CCN(,15,add ( at position 14,flow_matching,0.3,2.0,41,92
67,add,15.0,C,,CCS(=O)(=O)CCN(,CCS(=O)(=O)CCN(C,16,add C at position 15,flow_matching,0.3,2.0,41,92
68,add,16.0,),,CCS(=O)(=O)CCN(C,CCS(=O)(=O)CCN(C),17,add ) at position 16,flow_matching,0.3,2.0,41,92
69,add,17.0,C,,CCS(=O)(=O)CCN(C),CCS(=O)(=O)CCN(C)C,18,add C at position 17,flow_matching,0.3,2.0,41,92
70,add,18.0,c,,CCS(=O)(=O)CCN(C)C,CCS(=O)(=O)CCN(C)Cc,19,add c at position 18,flow_matching,0.3,2.0,41,92
71,add,19.0,1,,CCS(=O)(=O)CCN(C)Cc,CCS(=O)(=O)CCN(C)Cc1,20,add 1 at position 19,flow_matching,0.3,2.0,41,92
72,add,20.0,c,,CCS(=O)(=O)CCN(C)Cc1,CCS(=O)(=O)CCN(C)Cc1c,21,add c at position 20,flow_matching,0.3,2.0,41,92
73,add,21.0,[,,CCS(=O)(=O)CCN(C)Cc1c,CCS(=O)(=O)CCN(C)Cc1c[,22,add [ at position 21,flow_matching,0.3,2.0,41,92
74,add,22.0,n,,CCS(=O)(=O)CCN(C)Cc1c[,CCS(=O)(=O)CCN(C)Cc1c[n,23,add n at position 22,flow_matching,0.3,2.0,41,92
75,add,23.0,H,,CCS(=O)(=O)CCN(C)Cc1c[n,CCS(=O)(=O)CCN(C)Cc1c[nH,24,add H at position 23,flow_matching,0.3,2.0,41,92
76,add,24.0,],,CCS(=O)(=O)CCN(C)Cc1c[nH,CCS(=O)(=O)CCN(C)Cc1c[nH],25,add ] at position 24,flow_matching,0.3,2.0,41,92
77,add,25.0,n,,CCS(=O)(=O)CCN(C)Cc1c[nH],CCS(=O)(=O)CCN(C)Cc1c[nH]n,26,add n at position 25,flow_matching,0.3,2.0,41,92
78,add,26.0,c,,CCS(=O)(=O)CCN(C)Cc1c[nH]n,CCS(=O)(=O)CCN(C)Cc1c[nH]nc,27,add c at position 26,flow_matching,0.3,2.0,41,92
79,add,27.0,1,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1,28,add 1 at position 27,flow_matching,0.3,2.0,41,92
80,add,28.0,-,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-,29,add - at position 28,flow_matching,0.3,2.0,41,92
81,add,29.0,c,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c,30,add c at position 29,flow_matching,0.3,2.0,41,92
82,add,30.0,1,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1,31,add 1 at position 30,flow_matching,0.3,2.0,41,92
83,add,31.0,c,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1c,32,add c at position 31,flow_matching,0.3,2.0,41,92
84,add,32.0,c,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1c,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1cc,33,add c at position 32,flow_matching,0.3,2.0,41,92
85,add,33.0,c,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1cc,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc,34,add c at position 33,flow_matching,0.3,2.0,41,92
86,add,34.0,(,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(,35,add ( at position 34,flow_matching,0.3,2.0,41,92
87,add,35.0,C,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C,36,add C at position 35,flow_matching,0.3,2.0,41,92
88,add,36.0,),,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C),37,add ) at position 36,flow_matching,0.3,2.0,41,92
89,add,37.0,c,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C),CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C)c,38,add c at position 37,flow_matching,0.3,2.0,41,92
90,add,38.0,c,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C)c,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C)cc,39,add c at position 38,flow_matching,0.3,2.0,41,92
91,add,39.0,1,,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C)cc,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C)cc1,40,add 1 at position 39,flow_matching,0.3,2.0,41,92
92,add,40.0,"
",,CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C)cc1,"CCS(=O)(=O)CCN(C)Cc1c[nH]nc1-c1ccc(C)cc1
",41,"add 
 at position 40",flow_matching,0.3,2.0,41,92
