step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,41,167
1,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,41,167
2,replace,0.0,C,],],C,1,replace ] at position 0 with C,flow_matching,0.3,2.0,41,167
3,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,41,167
4,add,0.0,I,,Cc,ICc,3,add I at position 0,flow_matching,0.3,2.0,41,167
5,add,0.0,2,,ICc,2ICc,4,add 2 at position 0,flow_matching,0.3,2.0,41,167
6,add,1.0,\,,2ICc,2\ICc,5,add \ at position 1,flow_matching,0.3,2.0,41,167
7,remove,0.0,2,,2\ICc,\ICc,4,remove 2 from position 0,flow_matching,0.3,2.0,41,167
8,replace,1.0,S,I,\ICc,\SCc,4,replace I at position 1 with S,flow_matching,0.3,2.0,41,167
9,add,0.0,(,,\SCc,(\SCc,5,add ( at position 0,flow_matching,0.3,2.0,41,167
10,replace,0.0,C,(,(\SCc,C\SCc,5,replace ( at position 0 with C,flow_matching,0.3,2.0,41,167
11,replace,4.0,H,c,C\SCc,C\SCH,5,replace c at position 4 with H,flow_matching,0.3,2.0,41,167
12,add,0.0,3,,C\SCH,3C\SCH,6,add 3 at position 0,flow_matching,0.3,2.0,41,167
13,replace,3.0,s,S,3C\SCH,3C\sCH,6,replace S at position 3 with s,flow_matching,0.3,2.0,41,167
14,remove,2.0,\,,3C\sCH,3CsCH,5,remove \ from position 2,flow_matching,0.3,2.0,41,167
15,remove,4.0,H,,3CsCH,3CsC,4,remove H from position 4,flow_matching,0.3,2.0,41,167
16,add,0.0,),,3CsC,)3CsC,5,add ) at position 0,flow_matching,0.3,2.0,41,167
17,replace,0.0,C,),)3CsC,C3CsC,5,replace ) at position 0 with C,flow_matching,0.3,2.0,41,167
18,replace,1.0,c,3,C3CsC,CcCsC,5,replace 3 at position 1 with c,flow_matching,0.3,2.0,41,167
19,replace,2.0,1,C,CcCsC,Cc1sC,5,replace C at position 2 with 1,flow_matching,0.3,2.0,41,167
20,remove,0.0,C,,Cc1sC,c1sC,4,remove C from position 0,flow_matching,0.3,2.0,41,167
21,replace,0.0,4,c,c1sC,41sC,4,replace c at position 0 with 4,flow_matching,0.3,2.0,41,167
22,replace,0.0,C,4,41sC,C1sC,4,replace 4 at position 0 with C,flow_matching,0.3,2.0,41,167
23,replace,1.0,/,1,C1sC,C/sC,4,replace 1 at position 1 with /,flow_matching,0.3,2.0,41,167
24,add,4.0,#,,C/sC,C/sC#,5,add # at position 4,flow_matching,0.3,2.0,41,167
25,remove,4.0,#,,C/sC#,C/sC,4,remove # from position 4,flow_matching,0.3,2.0,41,167
26,add,2.0,+,,C/sC,C/+sC,5,add + at position 2,flow_matching,0.3,2.0,41,167
27,replace,3.0,+,s,C/+sC,C/++C,5,replace s at position 3 with +,flow_matching,0.3,2.0,41,167
28,replace,1.0,c,/,C/++C,Cc++C,5,replace / at position 1 with c,flow_matching,0.3,2.0,41,167
29,remove,1.0,c,,Cc++C,C++C,4,remove c from position 1,flow_matching,0.3,2.0,41,167
30,replace,1.0,c,+,C++C,Cc+C,4,replace + at position 1 with c,flow_matching,0.3,2.0,41,167
31,replace,2.0,1,+,Cc+C,Cc1C,4,replace + at position 2 with 1,flow_matching,0.3,2.0,41,167
32,replace,3.0,c,C,Cc1C,Cc1c,4,replace C at position 3 with c,flow_matching,0.3,2.0,41,167
33,replace,3.0,3,c,Cc1c,Cc13,4,replace c at position 3 with 3,flow_matching,0.3,2.0,41,167
34,add,1.0,o,,Cc13,Coc13,5,add o at position 1,flow_matching,0.3,2.0,41,167
35,replace,4.0,4,3,Coc13,Coc14,5,replace 3 at position 4 with 4,flow_matching,0.3,2.0,41,167
36,remove,2.0,c,,Coc14,Co14,4,remove c from position 2,flow_matching,0.3,2.0,41,167
37,replace,1.0,c,o,Co14,Cc14,4,replace o at position 1 with c,flow_matching,0.3,2.0,41,167
38,replace,3.0,c,4,Cc14,Cc1c,4,replace 4 at position 3 with c,flow_matching,0.3,2.0,41,167
39,remove,2.0,1,,Cc1c,Ccc,3,remove 1 from position 2,flow_matching,0.3,2.0,41,167
40,remove,2.0,c,,Ccc,Cc,2,remove c from position 2,flow_matching,0.3,2.0,41,167
41,remove,0.0,C,,Cc,c,1,remove C from position 0,flow_matching,0.3,2.0,41,167
42,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,41,167
43,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,41,167
44,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,41,167
45,replace,1.0,),c,Cc1,C)1,3,replace c at position 1 with ),flow_matching,0.3,2.0,41,167
46,replace,1.0,c,),C)1,Cc1,3,replace ) at position 1 with c,flow_matching,0.3,2.0,41,167
47,replace,0.0,#,C,Cc1,#c1,3,replace C at position 0 with #,flow_matching,0.3,2.0,41,167
48,add,1.0,l,,#c1,#lc1,4,add l at position 1,flow_matching,0.3,2.0,41,167
49,remove,1.0,l,,#lc1,#c1,3,remove l from position 1,flow_matching,0.3,2.0,41,167
50,replace,2.0,(,1,#c1,#c(,3,replace 1 at position 2 with (,flow_matching,0.3,2.0,41,167
51,replace,1.0,2,c,#c(,#2(,3,replace c at position 1 with 2,flow_matching,0.3,2.0,41,167
52,replace,0.0,C,#,#2(,C2(,3,replace # at position 0 with C,flow_matching,0.3,2.0,41,167
53,add,3.0,O,,C2(,C2(O,4,add O at position 3,flow_matching,0.3,2.0,41,167
54,replace,1.0,c,2,C2(O,Cc(O,4,replace 2 at position 1 with c,flow_matching,0.3,2.0,41,167
55,add,1.0,],,Cc(O,C]c(O,5,add ] at position 1,flow_matching,0.3,2.0,41,167
56,replace,1.0,c,],C]c(O,Ccc(O,5,replace ] at position 1 with c,flow_matching,0.3,2.0,41,167
57,add,2.0,n,,Ccc(O,Ccnc(O,6,add n at position 2,flow_matching,0.3,2.0,41,167
58,replace,2.0,1,n,Ccnc(O,Cc1c(O,6,replace n at position 2 with 1,flow_matching,0.3,2.0,41,167
59,remove,2.0,1,,Cc1c(O,Ccc(O,5,remove 1 from position 2,flow_matching,0.3,2.0,41,167
60,add,5.0,F,,Ccc(O,Ccc(OF,6,add F at position 5,flow_matching,0.3,2.0,41,167
61,remove,3.0,(,,Ccc(OF,CccOF,5,remove ( from position 3,flow_matching,0.3,2.0,41,167
62,replace,2.0,1,c,CccOF,Cc1OF,5,replace c at position 2 with 1,flow_matching,0.3,2.0,41,167
63,remove,0.0,C,,Cc1OF,c1OF,4,remove C from position 0,flow_matching,0.3,2.0,41,167
64,replace,0.0,C,c,c1OF,C1OF,4,replace c at position 0 with C,flow_matching,0.3,2.0,41,167
65,replace,1.0,c,1,C1OF,CcOF,4,replace 1 at position 1 with c,flow_matching,0.3,2.0,41,167
66,replace,0.0,],C,CcOF,]cOF,4,replace C at position 0 with ],flow_matching,0.3,2.0,41,167
67,replace,0.0,C,],]cOF,CcOF,4,replace ] at position 0 with C,flow_matching,0.3,2.0,41,167
68,replace,1.0,N,c,CcOF,CNOF,4,replace c at position 1 with N,flow_matching,0.3,2.0,41,167
69,remove,2.0,O,,CNOF,CNF,3,remove O from position 2,flow_matching,0.3,2.0,41,167
70,replace,0.0,@,C,CNF,@NF,3,replace C at position 0 with @,flow_matching,0.3,2.0,41,167
71,add,0.0,n,,@NF,n@NF,4,add n at position 0,flow_matching,0.3,2.0,41,167
72,replace,0.0,C,n,n@NF,C@NF,4,replace n at position 0 with C,flow_matching,0.3,2.0,41,167
73,remove,0.0,C,,C@NF,@NF,3,remove C from position 0,flow_matching,0.3,2.0,41,167
74,add,0.0,5,,@NF,5@NF,4,add 5 at position 0,flow_matching,0.3,2.0,41,167
75,add,2.0,/,,5@NF,5@/NF,5,add / at position 2,flow_matching,0.3,2.0,41,167
76,replace,2.0,n,/,5@/NF,5@nNF,5,replace / at position 2 with n,flow_matching,0.3,2.0,41,167
77,remove,1.0,@,,5@nNF,5nNF,4,remove @ from position 1,flow_matching,0.3,2.0,41,167
78,replace,0.0,C,5,5nNF,CnNF,4,replace 5 at position 0 with C,flow_matching,0.3,2.0,41,167
79,remove,3.0,F,,CnNF,CnN,3,remove F from position 3,flow_matching,0.3,2.0,41,167
80,replace,1.0,s,n,CnN,CsN,3,replace n at position 1 with s,flow_matching,0.3,2.0,41,167
81,replace,1.0,c,s,CsN,CcN,3,replace s at position 1 with c,flow_matching,0.3,2.0,41,167
82,add,0.0,=,,CcN,=CcN,4,add = at position 0,flow_matching,0.3,2.0,41,167
83,add,0.0,+,,=CcN,+=CcN,5,add + at position 0,flow_matching,0.3,2.0,41,167
84,remove,4.0,N,,+=CcN,+=Cc,4,remove N from position 4,flow_matching,0.3,2.0,41,167
85,add,3.0,S,,+=Cc,+=CSc,5,add S at position 3,flow_matching,0.3,2.0,41,167
86,replace,0.0,C,+,+=CSc,C=CSc,5,replace + at position 0 with C,flow_matching,0.3,2.0,41,167
87,add,5.0,r,,C=CSc,C=CScr,6,add r at position 5,flow_matching,0.3,2.0,41,167
88,remove,0.0,C,,C=CScr,=CScr,5,remove C from position 0,flow_matching,0.3,2.0,41,167
89,add,4.0,I,,=CScr,=CScIr,6,add I at position 4,flow_matching,0.3,2.0,41,167
90,remove,1.0,C,,=CScIr,=ScIr,5,remove C from position 1,flow_matching,0.3,2.0,41,167
91,replace,0.0,C,=,=ScIr,CScIr,5,replace = at position 0 with C,flow_matching,0.3,2.0,41,167
92,replace,1.0,c,S,CScIr,CccIr,5,replace S at position 1 with c,flow_matching,0.3,2.0,41,167
93,remove,2.0,c,,CccIr,CcIr,4,remove c from position 2,flow_matching,0.3,2.0,41,167
94,add,2.0,B,,CcIr,CcBIr,5,add B at position 2,flow_matching,0.3,2.0,41,167
95,add,3.0,\,,CcBIr,CcB\Ir,6,add \ at position 3,flow_matching,0.3,2.0,41,167
96,remove,2.0,B,,CcB\Ir,Cc\Ir,5,remove B from position 2,flow_matching,0.3,2.0,41,167
97,replace,1.0,3,c,Cc\Ir,C3\Ir,5,replace c at position 1 with 3,flow_matching,0.3,2.0,41,167
98,replace,3.0,r,I,C3\Ir,C3\rr,5,replace I at position 3 with r,flow_matching,0.3,2.0,41,167
99,remove,0.0,C,,C3\rr,3\rr,4,remove C from position 0,flow_matching,0.3,2.0,41,167
100,add,4.0,1,,3\rr,3\rr1,5,add 1 at position 4,flow_matching,0.3,2.0,41,167
101,add,2.0,o,,3\rr1,3\orr1,6,add o at position 2,flow_matching,0.3,2.0,41,167
102,add,5.0,2,,3\orr1,3\orr21,7,add 2 at position 5,flow_matching,0.3,2.0,41,167
103,add,6.0,7,,3\orr21,3\orr271,8,add 7 at position 6,flow_matching,0.3,2.0,41,167
104,add,4.0,],,3\orr271,3\or]r271,9,add ] at position 4,flow_matching,0.3,2.0,41,167
105,add,9.0,n,,3\or]r271,3\or]r271n,10,add n at position 9,flow_matching,0.3,2.0,41,167
106,replace,0.0,C,3,3\or]r271n,C\or]r271n,10,replace 3 at position 0 with C,flow_matching,0.3,2.0,41,167
107,remove,5.0,r,,C\or]r271n,C\or]271n,9,remove r from position 5,flow_matching,0.3,2.0,41,167
108,add,3.0,B,,C\or]271n,C\oBr]271n,10,add B at position 3,flow_matching,0.3,2.0,41,167
109,replace,1.0,c,\,C\oBr]271n,CcoBr]271n,10,replace \ at position 1 with c,flow_matching,0.3,2.0,41,167
110,replace,2.0,1,o,CcoBr]271n,Cc1Br]271n,10,replace o at position 2 with 1,flow_matching,0.3,2.0,41,167
111,replace,8.0,=,1,Cc1Br]271n,Cc1Br]27=n,10,replace 1 at position 8 with =,flow_matching,0.3,2.0,41,167
112,replace,2.0,7,1,Cc1Br]27=n,Cc7Br]27=n,10,replace 1 at position 2 with 7,flow_matching,0.3,2.0,41,167
113,remove,0.0,C,,Cc7Br]27=n,c7Br]27=n,9,remove C from position 0,flow_matching,0.3,2.0,41,167
114,remove,2.0,B,,c7Br]27=n,c7r]27=n,8,remove B from position 2,flow_matching,0.3,2.0,41,167
115,add,0.0,/,,c7r]27=n,/c7r]27=n,9,add / at position 0,flow_matching,0.3,2.0,41,167
116,replace,1.0,O,c,/c7r]27=n,/O7r]27=n,9,replace c at position 1 with O,flow_matching,0.3,2.0,41,167
117,add,5.0,l,,/O7r]27=n,/O7r]l27=n,10,add l at position 5,flow_matching,0.3,2.0,41,167
118,replace,0.0,C,/,/O7r]l27=n,CO7r]l27=n,10,replace / at position 0 with C,flow_matching,0.3,2.0,41,167
119,replace,1.0,c,O,CO7r]l27=n,Cc7r]l27=n,10,replace O at position 1 with c,flow_matching,0.3,2.0,41,167
120,add,4.0,C,,Cc7r]l27=n,Cc7rC]l27=n,11,add C at position 4,flow_matching,0.3,2.0,41,167
121,replace,10.0,C,n,Cc7rC]l27=n,Cc7rC]l27=C,11,replace n at position 10 with C,flow_matching,0.3,2.0,41,167
122,replace,2.0,1,7,Cc7rC]l27=C,Cc1rC]l27=C,11,replace 7 at position 2 with 1,flow_matching,0.3,2.0,41,167
123,replace,0.0,(,C,Cc1rC]l27=C,(c1rC]l27=C,11,replace C at position 0 with (,flow_matching,0.3,2.0,41,167
124,remove,2.0,1,,(c1rC]l27=C,(crC]l27=C,10,remove 1 from position 2,flow_matching,0.3,2.0,41,167
125,add,4.0,\,,(crC]l27=C,(crC\]l27=C,11,add \ at position 4,flow_matching,0.3,2.0,41,167
126,remove,10.0,C,,(crC\]l27=C,(crC\]l27=,10,remove C from position 10,flow_matching,0.3,2.0,41,167
127,replace,8.0,n,7,(crC\]l27=,(crC\]l2n=,10,replace 7 at position 8 with n,flow_matching,0.3,2.0,41,167
128,add,10.0,=,,(crC\]l2n=,(crC\]l2n==,11,add = at position 10,flow_matching,0.3,2.0,41,167
129,replace,0.0,C,(,(crC\]l2n==,CcrC\]l2n==,11,replace ( at position 0 with C,flow_matching,0.3,2.0,41,167
130,replace,2.0,1,r,CcrC\]l2n==,Cc1C\]l2n==,11,replace r at position 2 with 1,flow_matching,0.3,2.0,41,167
131,replace,3.0,c,C,Cc1C\]l2n==,Cc1c\]l2n==,11,replace C at position 3 with c,flow_matching,0.3,2.0,41,167
132,replace,4.0,c,\,Cc1c\]l2n==,Cc1cc]l2n==,11,replace \ at position 4 with c,flow_matching,0.3,2.0,41,167
133,replace,5.0,c,],Cc1cc]l2n==,Cc1cccl2n==,11,replace ] at position 5 with c,flow_matching,0.3,2.0,41,167
134,replace,6.0,(,l,Cc1cccl2n==,Cc1ccc(2n==,11,replace l at position 6 with (,flow_matching,0.3,2.0,41,167
135,replace,7.0,C,2,Cc1ccc(2n==,Cc1ccc(Cn==,11,replace 2 at position 7 with C,flow_matching,0.3,2.0,41,167
136,replace,8.0,(,n,Cc1ccc(Cn==,Cc1ccc(C(==,11,replace n at position 8 with (,flow_matching,0.3,2.0,41,167
137,replace,10.0,O,=,Cc1ccc(C(==,Cc1ccc(C(=O,11,replace = at position 10 with O,flow_matching,0.3,2.0,41,167
138,add,11.0,),,Cc1ccc(C(=O,Cc1ccc(C(=O),12,add ) at position 11,flow_matching,0.3,2.0,41,167
139,add,12.0,N,,Cc1ccc(C(=O),Cc1ccc(C(=O)N,13,add N at position 12,flow_matching,0.3,2.0,41,167
140,add,13.0,[,,Cc1ccc(C(=O)N,Cc1ccc(C(=O)N[,14,add [ at position 13,flow_matching,0.3,2.0,41,167
141,add,14.0,C,,Cc1ccc(C(=O)N[,Cc1ccc(C(=O)N[C,15,add C at position 14,flow_matching,0.3,2.0,41,167
142,add,15.0,@,,Cc1ccc(C(=O)N[C,Cc1ccc(C(=O)N[C@,16,add @ at position 15,flow_matching,0.3,2.0,41,167
143,add,16.0,H,,Cc1ccc(C(=O)N[C@,Cc1ccc(C(=O)N[C@H,17,add H at position 16,flow_matching,0.3,2.0,41,167
144,add,17.0,],,Cc1ccc(C(=O)N[C@H,Cc1ccc(C(=O)N[C@H],18,add ] at position 17,flow_matching,0.3,2.0,41,167
145,add,18.0,2,,Cc1ccc(C(=O)N[C@H],Cc1ccc(C(=O)N[C@H]2,19,add 2 at position 18,flow_matching,0.3,2.0,41,167
146,add,19.0,C,,Cc1ccc(C(=O)N[C@H]2,Cc1ccc(C(=O)N[C@H]2C,20,add C at position 19,flow_matching,0.3,2.0,41,167
147,add,20.0,C,,Cc1ccc(C(=O)N[C@H]2C,Cc1ccc(C(=O)N[C@H]2CC,21,add C at position 20,flow_matching,0.3,2.0,41,167
148,add,21.0,C,,Cc1ccc(C(=O)N[C@H]2CC,Cc1ccc(C(=O)N[C@H]2CCC,22,add C at position 21,flow_matching,0.3,2.0,41,167
149,add,22.0,[,,Cc1ccc(C(=O)N[C@H]2CCC,Cc1ccc(C(=O)N[C@H]2CCC[,23,add [ at position 22,flow_matching,0.3,2.0,41,167
150,add,23.0,N,,Cc1ccc(C(=O)N[C@H]2CCC[,Cc1ccc(C(=O)N[C@H]2CCC[N,24,add N at position 23,flow_matching,0.3,2.0,41,167
151,add,24.0,H,,Cc1ccc(C(=O)N[C@H]2CCC[N,Cc1ccc(C(=O)N[C@H]2CCC[NH,25,add H at position 24,flow_matching,0.3,2.0,41,167
152,add,25.0,2,,Cc1ccc(C(=O)N[C@H]2CCC[NH,Cc1ccc(C(=O)N[C@H]2CCC[NH2,26,add 2 at position 25,flow_matching,0.3,2.0,41,167
153,add,26.0,+,,Cc1ccc(C(=O)N[C@H]2CCC[NH2,Cc1ccc(C(=O)N[C@H]2CCC[NH2+,27,add + at position 26,flow_matching,0.3,2.0,41,167
154,add,27.0,],,Cc1ccc(C(=O)N[C@H]2CCC[NH2+,Cc1ccc(C(=O)N[C@H]2CCC[NH2+],28,add ] at position 27,flow_matching,0.3,2.0,41,167
155,add,28.0,[,,Cc1ccc(C(=O)N[C@H]2CCC[NH2+],Cc1ccc(C(=O)N[C@H]2CCC[NH2+][,29,add [ at position 28,flow_matching,0.3,2.0,41,167
156,add,29.0,C,,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C,30,add C at position 29,flow_matching,0.3,2.0,41,167
157,add,30.0,@,,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@,31,add @ at position 30,flow_matching,0.3,2.0,41,167
158,add,31.0,H,,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H,32,add H at position 31,flow_matching,0.3,2.0,41,167
159,add,32.0,],,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H],33,add ] at position 32,flow_matching,0.3,2.0,41,167
160,add,33.0,2,,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H],Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2,34,add 2 at position 33,flow_matching,0.3,2.0,41,167
161,add,34.0,C,,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C,35,add C at position 34,flow_matching,0.3,2.0,41,167
162,add,35.0,),,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C),36,add ) at position 35,flow_matching,0.3,2.0,41,167
163,add,36.0,c,,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C),Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C)c,37,add c at position 36,flow_matching,0.3,2.0,41,167
164,add,37.0,c,,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C)c,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C)cc,38,add c at position 37,flow_matching,0.3,2.0,41,167
165,add,38.0,1,,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C)cc,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C)cc1,39,add 1 at position 38,flow_matching,0.3,2.0,41,167
166,add,39.0,F,,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C)cc1,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C)cc1F,40,add F at position 39,flow_matching,0.3,2.0,41,167
167,add,40.0,"
",,Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C)cc1F,"Cc1ccc(C(=O)N[C@H]2CCC[NH2+][C@H]2C)cc1F
",41,"add 
 at position 40",flow_matching,0.3,2.0,41,167
