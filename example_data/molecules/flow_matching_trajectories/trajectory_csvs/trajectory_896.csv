step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,230
1,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,49,230
2,replace,0.0,O,6,6,O,1,replace 6 at position 0 with O,flow_matching,0.3,2.0,49,230
3,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,49,230
4,add,2.0,C,,O=,O=C,3,add C at position 2,flow_matching,0.3,2.0,49,230
5,remove,0.0,O,,O=C,=C,2,remove O from position 0,flow_matching,0.3,2.0,49,230
6,replace,0.0,(,=,=C,(C,2,replace = at position 0 with (,flow_matching,0.3,2.0,49,230
7,add,0.0,F,,(C,F(C,3,add F at position 0,flow_matching,0.3,2.0,49,230
8,replace,2.0,6,C,F(C,F(6,3,replace C at position 2 with 6,flow_matching,0.3,2.0,49,230
9,add,0.0,c,,F(6,cF(6,4,add c at position 0,flow_matching,0.3,2.0,49,230
10,replace,3.0,c,6,cF(6,cF(c,4,replace 6 at position 3 with c,flow_matching,0.3,2.0,49,230
11,add,2.0,[,,cF(c,cF[(c,5,add [ at position 2,flow_matching,0.3,2.0,49,230
12,add,0.0,),,cF[(c,)cF[(c,6,add ) at position 0,flow_matching,0.3,2.0,49,230
13,add,0.0,@,,)cF[(c,@)cF[(c,7,add @ at position 0,flow_matching,0.3,2.0,49,230
14,add,5.0,F,,@)cF[(c,@)cF[F(c,8,add F at position 5,flow_matching,0.3,2.0,49,230
15,add,1.0,I,,@)cF[F(c,@I)cF[F(c,9,add I at position 1,flow_matching,0.3,2.0,49,230
16,remove,1.0,I,,@I)cF[F(c,@)cF[F(c,8,remove I from position 1,flow_matching,0.3,2.0,49,230
17,add,1.0,2,,@)cF[F(c,@2)cF[F(c,9,add 2 at position 1,flow_matching,0.3,2.0,49,230
18,replace,3.0,(,c,@2)cF[F(c,@2)(F[F(c,9,replace c at position 3 with (,flow_matching,0.3,2.0,49,230
19,replace,1.0,S,2,@2)(F[F(c,@S)(F[F(c,9,replace 2 at position 1 with S,flow_matching,0.3,2.0,49,230
20,remove,3.0,(,,@S)(F[F(c,@S)F[F(c,8,remove ( from position 3,flow_matching,0.3,2.0,49,230
21,replace,0.0,O,@,@S)F[F(c,OS)F[F(c,8,replace @ at position 0 with O,flow_matching,0.3,2.0,49,230
22,replace,1.0,=,S,OS)F[F(c,O=)F[F(c,8,replace S at position 1 with =,flow_matching,0.3,2.0,49,230
23,add,4.0,s,,O=)F[F(c,O=)Fs[F(c,9,add s at position 4,flow_matching,0.3,2.0,49,230
24,remove,2.0,),,O=)Fs[F(c,O=Fs[F(c,8,remove ) from position 2,flow_matching,0.3,2.0,49,230
25,remove,6.0,(,,O=Fs[F(c,O=Fs[Fc,7,remove ( from position 6,flow_matching,0.3,2.0,49,230
26,replace,2.0,@,F,O=Fs[Fc,O=@s[Fc,7,replace F at position 2 with @,flow_matching,0.3,2.0,49,230
27,remove,0.0,O,,O=@s[Fc,=@s[Fc,6,remove O from position 0,flow_matching,0.3,2.0,49,230
28,add,6.0,4,,=@s[Fc,=@s[Fc4,7,add 4 at position 6,flow_matching,0.3,2.0,49,230
29,replace,0.0,O,=,=@s[Fc4,O@s[Fc4,7,replace = at position 0 with O,flow_matching,0.3,2.0,49,230
30,replace,1.0,=,@,O@s[Fc4,O=s[Fc4,7,replace @ at position 1 with =,flow_matching,0.3,2.0,49,230
31,replace,2.0,C,s,O=s[Fc4,O=C[Fc4,7,replace s at position 2 with C,flow_matching,0.3,2.0,49,230
32,replace,3.0,(,[,O=C[Fc4,O=C(Fc4,7,replace [ at position 3 with (,flow_matching,0.3,2.0,49,230
33,replace,4.0,C,F,O=C(Fc4,O=C(Cc4,7,replace F at position 4 with C,flow_matching,0.3,2.0,49,230
34,replace,5.0,/,c,O=C(Cc4,O=C(C/4,7,replace c at position 5 with /,flow_matching,0.3,2.0,49,230
35,replace,6.0,C,4,O=C(C/4,O=C(C/C,7,replace 4 at position 6 with C,flow_matching,0.3,2.0,49,230
36,replace,6.0,2,C,O=C(C/C,O=C(C/2,7,replace C at position 6 with 2,flow_matching,0.3,2.0,49,230
37,add,5.0,r,,O=C(C/2,O=C(Cr/2,8,add r at position 5,flow_matching,0.3,2.0,49,230
38,replace,2.0,B,C,O=C(Cr/2,O=B(Cr/2,8,replace C at position 2 with B,flow_matching,0.3,2.0,49,230
39,replace,2.0,r,B,O=B(Cr/2,O=r(Cr/2,8,replace B at position 2 with r,flow_matching,0.3,2.0,49,230
40,add,8.0,n,,O=r(Cr/2,O=r(Cr/2n,9,add n at position 8,flow_matching,0.3,2.0,49,230
41,replace,2.0,C,r,O=r(Cr/2n,O=C(Cr/2n,9,replace r at position 2 with C,flow_matching,0.3,2.0,49,230
42,replace,6.0,F,/,O=C(Cr/2n,O=C(CrF2n,9,replace / at position 6 with F,flow_matching,0.3,2.0,49,230
43,remove,4.0,C,,O=C(CrF2n,O=C(rF2n,8,remove C from position 4,flow_matching,0.3,2.0,49,230
44,remove,6.0,2,,O=C(rF2n,O=C(rFn,7,remove 2 from position 6,flow_matching,0.3,2.0,49,230
45,add,3.0,],,O=C(rFn,O=C](rFn,8,add ] at position 3,flow_matching,0.3,2.0,49,230
46,replace,5.0,l,r,O=C](rFn,O=C](lFn,8,replace r at position 5 with l,flow_matching,0.3,2.0,49,230
47,remove,5.0,l,,O=C](lFn,O=C](Fn,7,remove l from position 5,flow_matching,0.3,2.0,49,230
48,remove,0.0,O,,O=C](Fn,=C](Fn,6,remove O from position 0,flow_matching,0.3,2.0,49,230
49,remove,1.0,C,,=C](Fn,=](Fn,5,remove C from position 1,flow_matching,0.3,2.0,49,230
50,replace,0.0,O,=,=](Fn,O](Fn,5,replace = at position 0 with O,flow_matching,0.3,2.0,49,230
51,add,1.0,F,,O](Fn,OF](Fn,6,add F at position 1,flow_matching,0.3,2.0,49,230
52,replace,3.0,[,(,OF](Fn,OF][Fn,6,replace ( at position 3 with [,flow_matching,0.3,2.0,49,230
53,add,0.0,S,,OF][Fn,SOF][Fn,7,add S at position 0,flow_matching,0.3,2.0,49,230
54,replace,0.0,O,S,SOF][Fn,OOF][Fn,7,replace S at position 0 with O,flow_matching,0.3,2.0,49,230
55,replace,2.0,\,F,OOF][Fn,OO\][Fn,7,replace F at position 2 with \,flow_matching,0.3,2.0,49,230
56,replace,2.0,4,\,OO\][Fn,OO4][Fn,7,replace \ at position 2 with 4,flow_matching,0.3,2.0,49,230
57,replace,4.0,s,[,OO4][Fn,OO4]sFn,7,replace [ at position 4 with s,flow_matching,0.3,2.0,49,230
58,replace,5.0,I,F,OO4]sFn,OO4]sIn,7,replace F at position 5 with I,flow_matching,0.3,2.0,49,230
59,add,6.0,5,,OO4]sIn,OO4]sI5n,8,add 5 at position 6,flow_matching,0.3,2.0,49,230
60,remove,7.0,n,,OO4]sI5n,OO4]sI5,7,remove n from position 7,flow_matching,0.3,2.0,49,230
61,add,3.0,N,,OO4]sI5,OO4N]sI5,8,add N at position 3,flow_matching,0.3,2.0,49,230
62,replace,1.0,=,O,OO4N]sI5,O=4N]sI5,8,replace O at position 1 with =,flow_matching,0.3,2.0,49,230
63,replace,2.0,C,4,O=4N]sI5,O=CN]sI5,8,replace 4 at position 2 with C,flow_matching,0.3,2.0,49,230
64,replace,3.0,(,N,O=CN]sI5,O=C(]sI5,8,replace N at position 3 with (,flow_matching,0.3,2.0,49,230
65,add,1.0,r,,O=C(]sI5,Or=C(]sI5,9,add r at position 1,flow_matching,0.3,2.0,49,230
66,replace,0.0,),O,Or=C(]sI5,)r=C(]sI5,9,replace O at position 0 with ),flow_matching,0.3,2.0,49,230
67,add,2.0,#,,)r=C(]sI5,)r#=C(]sI5,10,add # at position 2,flow_matching,0.3,2.0,49,230
68,add,7.0,-,,)r#=C(]sI5,)r#=C(]-sI5,11,add - at position 7,flow_matching,0.3,2.0,49,230
69,replace,7.0,l,-,)r#=C(]-sI5,)r#=C(]lsI5,11,replace - at position 7 with l,flow_matching,0.3,2.0,49,230
70,add,5.0,B,,)r#=C(]lsI5,)r#=CB(]lsI5,12,add B at position 5,flow_matching,0.3,2.0,49,230
71,remove,5.0,B,,)r#=CB(]lsI5,)r#=C(]lsI5,11,remove B from position 5,flow_matching,0.3,2.0,49,230
72,remove,10.0,5,,)r#=C(]lsI5,)r#=C(]lsI,10,remove 5 from position 10,flow_matching,0.3,2.0,49,230
73,add,5.0,4,,)r#=C(]lsI,)r#=C4(]lsI,11,add 4 at position 5,flow_matching,0.3,2.0,49,230
74,replace,0.0,O,),)r#=C4(]lsI,Or#=C4(]lsI,11,replace ) at position 0 with O,flow_matching,0.3,2.0,49,230
75,add,2.0,s,,Or#=C4(]lsI,Ors#=C4(]lsI,12,add s at position 2,flow_matching,0.3,2.0,49,230
76,replace,1.0,=,r,Ors#=C4(]lsI,O=s#=C4(]lsI,12,replace r at position 1 with =,flow_matching,0.3,2.0,49,230
77,replace,2.0,C,s,O=s#=C4(]lsI,O=C#=C4(]lsI,12,replace s at position 2 with C,flow_matching,0.3,2.0,49,230
78,replace,3.0,(,#,O=C#=C4(]lsI,O=C(=C4(]lsI,12,replace # at position 3 with (,flow_matching,0.3,2.0,49,230
79,remove,1.0,=,,O=C(=C4(]lsI,OC(=C4(]lsI,11,remove = from position 1,flow_matching,0.3,2.0,49,230
80,replace,1.0,=,C,OC(=C4(]lsI,O=(=C4(]lsI,11,replace C at position 1 with =,flow_matching,0.3,2.0,49,230
81,remove,2.0,(,,O=(=C4(]lsI,O==C4(]lsI,10,remove ( from position 2,flow_matching,0.3,2.0,49,230
82,add,6.0,],,O==C4(]lsI,O==C4(]]lsI,11,add ] at position 6,flow_matching,0.3,2.0,49,230
83,replace,2.0,C,=,O==C4(]]lsI,O=CC4(]]lsI,11,replace = at position 2 with C,flow_matching,0.3,2.0,49,230
84,replace,10.0,5,I,O=CC4(]]lsI,O=CC4(]]ls5,11,replace I at position 10 with 5,flow_matching,0.3,2.0,49,230
85,replace,3.0,(,C,O=CC4(]]ls5,O=C(4(]]ls5,11,replace C at position 3 with (,flow_matching,0.3,2.0,49,230
86,replace,4.0,C,4,O=C(4(]]ls5,O=C(C(]]ls5,11,replace 4 at position 4 with C,flow_matching,0.3,2.0,49,230
87,add,1.0,S,,O=C(C(]]ls5,OS=C(C(]]ls5,12,add S at position 1,flow_matching,0.3,2.0,49,230
88,add,8.0,4,,OS=C(C(]]ls5,OS=C(C(]4]ls5,13,add 4 at position 8,flow_matching,0.3,2.0,49,230
89,replace,1.0,=,S,OS=C(C(]4]ls5,O==C(C(]4]ls5,13,replace S at position 1 with =,flow_matching,0.3,2.0,49,230
90,replace,2.0,C,=,O==C(C(]4]ls5,O=CC(C(]4]ls5,13,replace = at position 2 with C,flow_matching,0.3,2.0,49,230
91,replace,3.0,(,C,O=CC(C(]4]ls5,O=C((C(]4]ls5,13,replace C at position 3 with (,flow_matching,0.3,2.0,49,230
92,replace,4.0,C,(,O=C((C(]4]ls5,O=C(CC(]4]ls5,13,replace ( at position 4 with C,flow_matching,0.3,2.0,49,230
93,remove,7.0,],,O=C(CC(]4]ls5,O=C(CC(4]ls5,12,remove ] from position 7,flow_matching,0.3,2.0,49,230
94,replace,9.0,2,l,O=C(CC(4]ls5,O=C(CC(4]2s5,12,replace l at position 9 with 2,flow_matching,0.3,2.0,49,230
95,replace,8.0,O,],O=C(CC(4]2s5,O=C(CC(4O2s5,12,replace ] at position 8 with O,flow_matching,0.3,2.0,49,230
96,replace,5.0,/,C,O=C(CC(4O2s5,O=C(C/(4O2s5,12,replace C at position 5 with /,flow_matching,0.3,2.0,49,230
97,add,3.0,c,,O=C(C/(4O2s5,O=Cc(C/(4O2s5,13,add c at position 3,flow_matching,0.3,2.0,49,230
98,replace,3.0,n,c,O=Cc(C/(4O2s5,O=Cn(C/(4O2s5,13,replace c at position 3 with n,flow_matching,0.3,2.0,49,230
99,replace,3.0,(,n,O=Cn(C/(4O2s5,O=C((C/(4O2s5,13,replace n at position 3 with (,flow_matching,0.3,2.0,49,230
100,add,10.0,l,,O=C((C/(4O2s5,O=C((C/(4Ol2s5,14,add l at position 10,flow_matching,0.3,2.0,49,230
101,replace,4.0,C,(,O=C((C/(4Ol2s5,O=C(CC/(4Ol2s5,14,replace ( at position 4 with C,flow_matching,0.3,2.0,49,230
102,replace,5.0,/,C,O=C(CC/(4Ol2s5,O=C(C//(4Ol2s5,14,replace C at position 5 with /,flow_matching,0.3,2.0,49,230
103,add,13.0,s,,O=C(C//(4Ol2s5,O=C(C//(4Ol2ss5,15,add s at position 13,flow_matching,0.3,2.0,49,230
104,add,11.0,/,,O=C(C//(4Ol2ss5,O=C(C//(4Ol/2ss5,16,add / at position 11,flow_matching,0.3,2.0,49,230
105,add,3.0,1,,O=C(C//(4Ol/2ss5,O=C1(C//(4Ol/2ss5,17,add 1 at position 3,flow_matching,0.3,2.0,49,230
106,remove,15.0,s,,O=C1(C//(4Ol/2ss5,O=C1(C//(4Ol/2s5,16,remove s from position 15,flow_matching,0.3,2.0,49,230
107,replace,7.0,O,/,O=C1(C//(4Ol/2s5,O=C1(C/O(4Ol/2s5,16,replace / at position 7 with O,flow_matching,0.3,2.0,49,230
108,replace,3.0,(,1,O=C1(C/O(4Ol/2s5,O=C((C/O(4Ol/2s5,16,replace 1 at position 3 with (,flow_matching,0.3,2.0,49,230
109,replace,4.0,C,(,O=C((C/O(4Ol/2s5,O=C(CC/O(4Ol/2s5,16,replace ( at position 4 with C,flow_matching,0.3,2.0,49,230
110,remove,1.0,=,,O=C(CC/O(4Ol/2s5,OC(CC/O(4Ol/2s5,15,remove = from position 1,flow_matching,0.3,2.0,49,230
111,replace,1.0,=,C,OC(CC/O(4Ol/2s5,O=(CC/O(4Ol/2s5,15,replace C at position 1 with =,flow_matching,0.3,2.0,49,230
112,replace,2.0,C,(,O=(CC/O(4Ol/2s5,O=CCC/O(4Ol/2s5,15,replace ( at position 2 with C,flow_matching,0.3,2.0,49,230
113,replace,3.0,(,C,O=CCC/O(4Ol/2s5,O=C(C/O(4Ol/2s5,15,replace C at position 3 with (,flow_matching,0.3,2.0,49,230
114,remove,11.0,/,,O=C(C/O(4Ol/2s5,O=C(C/O(4Ol2s5,14,remove / from position 11,flow_matching,0.3,2.0,49,230
115,remove,11.0,2,,O=C(C/O(4Ol2s5,O=C(C/O(4Ols5,13,remove 2 from position 11,flow_matching,0.3,2.0,49,230
116,remove,7.0,(,,O=C(C/O(4Ols5,O=C(C/O4Ols5,12,remove ( from position 7,flow_matching,0.3,2.0,49,230
117,replace,6.0,C,O,O=C(C/O4Ols5,O=C(C/C4Ols5,12,replace O at position 6 with C,flow_matching,0.3,2.0,49,230
118,replace,7.0,(,4,O=C(C/C4Ols5,O=C(C/C(Ols5,12,replace 4 at position 7 with (,flow_matching,0.3,2.0,49,230
119,replace,9.0,4,l,O=C(C/C(Ols5,O=C(C/C(O4s5,12,replace l at position 9 with 4,flow_matching,0.3,2.0,49,230
120,remove,11.0,5,,O=C(C/C(O4s5,O=C(C/C(O4s,11,remove 5 from position 11,flow_matching,0.3,2.0,49,230
121,remove,9.0,4,,O=C(C/C(O4s,O=C(C/C(Os,10,remove 4 from position 9,flow_matching,0.3,2.0,49,230
122,remove,0.0,O,,O=C(C/C(Os,=C(C/C(Os,9,remove O from position 0,flow_matching,0.3,2.0,49,230
123,add,4.0,n,,=C(C/C(Os,=C(Cn/C(Os,10,add n at position 4,flow_matching,0.3,2.0,49,230
124,replace,0.0,O,=,=C(Cn/C(Os,OC(Cn/C(Os,10,replace = at position 0 with O,flow_matching,0.3,2.0,49,230
125,remove,8.0,O,,OC(Cn/C(Os,OC(Cn/C(s,9,remove O from position 8,flow_matching,0.3,2.0,49,230
126,replace,5.0,C,/,OC(Cn/C(s,OC(CnCC(s,9,replace / at position 5 with C,flow_matching,0.3,2.0,49,230
127,remove,3.0,C,,OC(CnCC(s,OC(nCC(s,8,remove C from position 3,flow_matching,0.3,2.0,49,230
128,remove,6.0,(,,OC(nCC(s,OC(nCCs,7,remove ( from position 6,flow_matching,0.3,2.0,49,230
129,replace,6.0,/,s,OC(nCCs,OC(nCC/,7,replace s at position 6 with /,flow_matching,0.3,2.0,49,230
130,replace,1.0,=,C,OC(nCC/,O=(nCC/,7,replace C at position 1 with =,flow_matching,0.3,2.0,49,230
131,add,7.0,3,,O=(nCC/,O=(nCC/3,8,add 3 at position 7,flow_matching,0.3,2.0,49,230
132,replace,2.0,C,(,O=(nCC/3,O=CnCC/3,8,replace ( at position 2 with C,flow_matching,0.3,2.0,49,230
133,replace,3.0,(,n,O=CnCC/3,O=C(CC/3,8,replace n at position 3 with (,flow_matching,0.3,2.0,49,230
134,replace,5.0,/,C,O=C(CC/3,O=C(C//3,8,replace C at position 5 with /,flow_matching,0.3,2.0,49,230
135,add,1.0,4,,O=C(C//3,O4=C(C//3,9,add 4 at position 1,flow_matching,0.3,2.0,49,230
136,remove,2.0,=,,O4=C(C//3,O4C(C//3,8,remove = from position 2,flow_matching,0.3,2.0,49,230
137,remove,5.0,/,,O4C(C//3,O4C(C/3,7,remove / from position 5,flow_matching,0.3,2.0,49,230
138,add,2.0,c,,O4C(C/3,O4cC(C/3,8,add c at position 2,flow_matching,0.3,2.0,49,230
139,add,3.0,s,,O4cC(C/3,O4csC(C/3,9,add s at position 3,flow_matching,0.3,2.0,49,230
140,replace,1.0,B,4,O4csC(C/3,OBcsC(C/3,9,replace 4 at position 1 with B,flow_matching,0.3,2.0,49,230
141,add,3.0,O,,OBcsC(C/3,OBcOsC(C/3,10,add O at position 3,flow_matching,0.3,2.0,49,230
142,add,5.0,1,,OBcOsC(C/3,OBcOs1C(C/3,11,add 1 at position 5,flow_matching,0.3,2.0,49,230
143,add,2.0,o,,OBcOs1C(C/3,OBocOs1C(C/3,12,add o at position 2,flow_matching,0.3,2.0,49,230
144,replace,1.0,=,B,OBocOs1C(C/3,O=ocOs1C(C/3,12,replace B at position 1 with =,flow_matching,0.3,2.0,49,230
145,add,1.0,s,,O=ocOs1C(C/3,Os=ocOs1C(C/3,13,add s at position 1,flow_matching,0.3,2.0,49,230
146,remove,5.0,O,,Os=ocOs1C(C/3,Os=ocs1C(C/3,12,remove O from position 5,flow_matching,0.3,2.0,49,230
147,replace,1.0,=,s,Os=ocs1C(C/3,O==ocs1C(C/3,12,replace s at position 1 with =,flow_matching,0.3,2.0,49,230
148,replace,2.0,C,=,O==ocs1C(C/3,O=Cocs1C(C/3,12,replace = at position 2 with C,flow_matching,0.3,2.0,49,230
149,replace,5.0,N,s,O=Cocs1C(C/3,O=CocN1C(C/3,12,replace s at position 5 with N,flow_matching,0.3,2.0,49,230
150,replace,5.0,l,N,O=CocN1C(C/3,O=Cocl1C(C/3,12,replace N at position 5 with l,flow_matching,0.3,2.0,49,230
151,replace,8.0,[,(,O=Cocl1C(C/3,O=Cocl1C[C/3,12,replace ( at position 8 with [,flow_matching,0.3,2.0,49,230
152,remove,8.0,[,,O=Cocl1C[C/3,O=Cocl1CC/3,11,remove [ from position 8,flow_matching,0.3,2.0,49,230
153,add,11.0,3,,O=Cocl1CC/3,O=Cocl1CC/33,12,add 3 at position 11,flow_matching,0.3,2.0,49,230
154,replace,7.0,6,C,O=Cocl1CC/33,O=Cocl16C/33,12,replace C at position 7 with 6,flow_matching,0.3,2.0,49,230
155,replace,3.0,(,o,O=Cocl16C/33,O=C(cl16C/33,12,replace o at position 3 with (,flow_matching,0.3,2.0,49,230
156,replace,4.0,C,c,O=C(cl16C/33,O=C(Cl16C/33,12,replace c at position 4 with C,flow_matching,0.3,2.0,49,230
157,replace,7.0,B,6,O=C(Cl16C/33,O=C(Cl1BC/33,12,replace 6 at position 7 with B,flow_matching,0.3,2.0,49,230
158,add,4.0,2,,O=C(Cl1BC/33,O=C(2Cl1BC/33,13,add 2 at position 4,flow_matching,0.3,2.0,49,230
159,replace,5.0,/,C,O=C(2Cl1BC/33,O=C(2/l1BC/33,13,replace C at position 5 with /,flow_matching,0.3,2.0,49,230
160,remove,11.0,3,,O=C(2/l1BC/33,O=C(2/l1BC/3,12,remove 3 from position 11,flow_matching,0.3,2.0,49,230
161,replace,4.0,C,2,O=C(2/l1BC/3,O=C(C/l1BC/3,12,replace 2 at position 4 with C,flow_matching,0.3,2.0,49,230
162,add,10.0,6,,O=C(C/l1BC/3,O=C(C/l1BC6/3,13,add 6 at position 10,flow_matching,0.3,2.0,49,230
163,add,2.0,1,,O=C(C/l1BC6/3,O=1C(C/l1BC6/3,14,add 1 at position 2,flow_matching,0.3,2.0,49,230
164,replace,3.0,/,C,O=1C(C/l1BC6/3,O=1/(C/l1BC6/3,14,replace C at position 3 with /,flow_matching,0.3,2.0,49,230
165,replace,6.0,o,/,O=1/(C/l1BC6/3,O=1/(Col1BC6/3,14,replace / at position 6 with o,flow_matching,0.3,2.0,49,230
166,replace,2.0,C,1,O=1/(Col1BC6/3,O=C/(Col1BC6/3,14,replace 1 at position 2 with C,flow_matching,0.3,2.0,49,230
167,remove,11.0,6,,O=C/(Col1BC6/3,O=C/(Col1BC/3,13,remove 6 from position 11,flow_matching,0.3,2.0,49,230
168,replace,9.0,n,B,O=C/(Col1BC/3,O=C/(Col1nC/3,13,replace B at position 9 with n,flow_matching,0.3,2.0,49,230
169,add,5.0,C,,O=C/(Col1nC/3,O=C/(CCol1nC/3,14,add C at position 5,flow_matching,0.3,2.0,49,230
170,replace,4.0,+,(,O=C/(CCol1nC/3,O=C/+CCol1nC/3,14,replace ( at position 4 with +,flow_matching,0.3,2.0,49,230
171,replace,3.0,(,/,O=C/+CCol1nC/3,O=C(+CCol1nC/3,14,replace / at position 3 with (,flow_matching,0.3,2.0,49,230
172,replace,2.0,5,C,O=C(+CCol1nC/3,O=5(+CCol1nC/3,14,replace C at position 2 with 5,flow_matching,0.3,2.0,49,230
173,replace,1.0,l,=,O=5(+CCol1nC/3,Ol5(+CCol1nC/3,14,replace = at position 1 with l,flow_matching,0.3,2.0,49,230
174,replace,4.0,],+,Ol5(+CCol1nC/3,Ol5(]CCol1nC/3,14,replace + at position 4 with ],flow_matching,0.3,2.0,49,230
175,replace,1.0,=,l,Ol5(]CCol1nC/3,O=5(]CCol1nC/3,14,replace l at position 1 with =,flow_matching,0.3,2.0,49,230
176,add,12.0,B,,O=5(]CCol1nC/3,O=5(]CCol1nCB/3,15,add B at position 12,flow_matching,0.3,2.0,49,230
177,replace,0.0,I,O,O=5(]CCol1nCB/3,I=5(]CCol1nCB/3,15,replace O at position 0 with I,flow_matching,0.3,2.0,49,230
178,replace,0.0,O,I,I=5(]CCol1nCB/3,O=5(]CCol1nCB/3,15,replace I at position 0 with O,flow_matching,0.3,2.0,49,230
179,replace,7.0,s,o,O=5(]CCol1nCB/3,O=5(]CCsl1nCB/3,15,replace o at position 7 with s,flow_matching,0.3,2.0,49,230
180,add,11.0,\,,O=5(]CCsl1nCB/3,O=5(]CCsl1n\CB/3,16,add \ at position 11,flow_matching,0.3,2.0,49,230
181,remove,4.0,],,O=5(]CCsl1n\CB/3,O=5(CCsl1n\CB/3,15,remove ] from position 4,flow_matching,0.3,2.0,49,230
182,replace,2.0,C,5,O=5(CCsl1n\CB/3,O=C(CCsl1n\CB/3,15,replace 5 at position 2 with C,flow_matching,0.3,2.0,49,230
183,replace,5.0,/,C,O=C(CCsl1n\CB/3,O=C(C/sl1n\CB/3,15,replace C at position 5 with /,flow_matching,0.3,2.0,49,230
184,remove,7.0,l,,O=C(C/sl1n\CB/3,O=C(C/s1n\CB/3,14,remove l from position 7,flow_matching,0.3,2.0,49,230
185,replace,13.0,F,3,O=C(C/s1n\CB/3,O=C(C/s1n\CB/F,14,replace 3 at position 13 with F,flow_matching,0.3,2.0,49,230
186,replace,6.0,C,s,O=C(C/s1n\CB/F,O=C(C/C1n\CB/F,14,replace s at position 6 with C,flow_matching,0.3,2.0,49,230
187,replace,6.0,s,C,O=C(C/C1n\CB/F,O=C(C/s1n\CB/F,14,replace C at position 6 with s,flow_matching,0.3,2.0,49,230
188,replace,6.0,C,s,O=C(C/s1n\CB/F,O=C(C/C1n\CB/F,14,replace s at position 6 with C,flow_matching,0.3,2.0,49,230
189,replace,7.0,(,1,O=C(C/C1n\CB/F,O=C(C/C(n\CB/F,14,replace 1 at position 7 with (,flow_matching,0.3,2.0,49,230
190,replace,8.0,=,n,O=C(C/C(n\CB/F,O=C(C/C(=\CB/F,14,replace n at position 8 with =,flow_matching,0.3,2.0,49,230
191,replace,9.0,N,\,O=C(C/C(=\CB/F,O=C(C/C(=NCB/F,14,replace \ at position 9 with N,flow_matching,0.3,2.0,49,230
192,replace,10.0,\,C,O=C(C/C(=NCB/F,O=C(C/C(=N\B/F,14,replace C at position 10 with \,flow_matching,0.3,2.0,49,230
193,replace,11.0,N,B,O=C(C/C(=N\B/F,O=C(C/C(=N\N/F,14,replace B at position 11 with N,flow_matching,0.3,2.0,49,230
194,replace,12.0,c,/,O=C(C/C(=N\N/F,O=C(C/C(=N\NcF,14,replace / at position 12 with c,flow_matching,0.3,2.0,49,230
195,replace,13.0,1,F,O=C(C/C(=N\NcF,O=C(C/C(=N\Nc1,14,replace F at position 13 with 1,flow_matching,0.3,2.0,49,230
196,add,14.0,n,,O=C(C/C(=N\Nc1,O=C(C/C(=N\Nc1n,15,add n at position 14,flow_matching,0.3,2.0,49,230
197,add,15.0,c,,O=C(C/C(=N\Nc1n,O=C(C/C(=N\Nc1nc,16,add c at position 15,flow_matching,0.3,2.0,49,230
198,add,16.0,(,,O=C(C/C(=N\Nc1nc,O=C(C/C(=N\Nc1nc(,17,add ( at position 16,flow_matching,0.3,2.0,49,230
199,add,17.0,-,,O=C(C/C(=N\Nc1nc(,O=C(C/C(=N\Nc1nc(-,18,add - at position 17,flow_matching,0.3,2.0,49,230
200,add,18.0,c,,O=C(C/C(=N\Nc1nc(-,O=C(C/C(=N\Nc1nc(-c,19,add c at position 18,flow_matching,0.3,2.0,49,230
201,add,19.0,2,,O=C(C/C(=N\Nc1nc(-c,O=C(C/C(=N\Nc1nc(-c2,20,add 2 at position 19,flow_matching,0.3,2.0,49,230
202,add,20.0,c,,O=C(C/C(=N\Nc1nc(-c2,O=C(C/C(=N\Nc1nc(-c2c,21,add c at position 20,flow_matching,0.3,2.0,49,230
203,add,21.0,c,,O=C(C/C(=N\Nc1nc(-c2c,O=C(C/C(=N\Nc1nc(-c2cc,22,add c at position 21,flow_matching,0.3,2.0,49,230
204,add,22.0,c,,O=C(C/C(=N\Nc1nc(-c2cc,O=C(C/C(=N\Nc1nc(-c2ccc,23,add c at position 22,flow_matching,0.3,2.0,49,230
205,add,23.0,c,,O=C(C/C(=N\Nc1nc(-c2ccc,O=C(C/C(=N\Nc1nc(-c2cccc,24,add c at position 23,flow_matching,0.3,2.0,49,230
206,add,24.0,c,,O=C(C/C(=N\Nc1nc(-c2cccc,O=C(C/C(=N\Nc1nc(-c2ccccc,25,add c at position 24,flow_matching,0.3,2.0,49,230
207,add,25.0,2,,O=C(C/C(=N\Nc1nc(-c2ccccc,O=C(C/C(=N\Nc1nc(-c2ccccc2,26,add 2 at position 25,flow_matching,0.3,2.0,49,230
208,add,26.0,),,O=C(C/C(=N\Nc1nc(-c2ccccc2,O=C(C/C(=N\Nc1nc(-c2ccccc2),27,add ) at position 26,flow_matching,0.3,2.0,49,230
209,add,27.0,c,,O=C(C/C(=N\Nc1nc(-c2ccccc2),O=C(C/C(=N\Nc1nc(-c2ccccc2)c,28,add c at position 27,flow_matching,0.3,2.0,49,230
210,add,28.0,s,,O=C(C/C(=N\Nc1nc(-c2ccccc2)c,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs,29,add s at position 28,flow_matching,0.3,2.0,49,230
211,add,29.0,1,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1,30,add 1 at position 29,flow_matching,0.3,2.0,49,230
212,add,30.0,),,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1),31,add ) at position 30,flow_matching,0.3,2.0,49,230
213,add,31.0,c,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1),O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c,32,add c at position 31,flow_matching,0.3,2.0,49,230
214,add,32.0,1,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1,33,add 1 at position 32,flow_matching,0.3,2.0,49,230
215,add,33.0,c,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1c,34,add c at position 33,flow_matching,0.3,2.0,49,230
216,add,34.0,c,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1c,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1cc,35,add c at position 34,flow_matching,0.3,2.0,49,230
217,add,35.0,c,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1cc,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccc,36,add c at position 35,flow_matching,0.3,2.0,49,230
218,add,36.0,c,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccc,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1cccc,37,add c at position 36,flow_matching,0.3,2.0,49,230
219,add,37.0,c,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1cccc,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc,38,add c at position 37,flow_matching,0.3,2.0,49,230
220,add,38.0,1,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1,39,add 1 at position 38,flow_matching,0.3,2.0,49,230
221,add,39.0,),,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1),40,add ) at position 39,flow_matching,0.3,2.0,49,230
222,add,40.0,C,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1),O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C,41,add C at position 40,flow_matching,0.3,2.0,49,230
223,add,41.0,(,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(,42,add ( at position 41,flow_matching,0.3,2.0,49,230
224,add,42.0,F,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F,43,add F at position 42,flow_matching,0.3,2.0,49,230
225,add,43.0,),,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F),44,add ) at position 43,flow_matching,0.3,2.0,49,230
226,add,44.0,(,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F),O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F)(,45,add ( at position 44,flow_matching,0.3,2.0,49,230
227,add,45.0,F,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F)(,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F)(F,46,add F at position 45,flow_matching,0.3,2.0,49,230
228,add,46.0,),,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F)(F,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F)(F),47,add ) at position 46,flow_matching,0.3,2.0,49,230
229,add,47.0,F,,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F)(F),O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F)(F)F,48,add F at position 47,flow_matching,0.3,2.0,49,230
230,add,48.0,"
",,O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F)(F)F,"O=C(C/C(=N\Nc1nc(-c2ccccc2)cs1)c1ccccc1)C(F)(F)F
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,230
