step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,171
1,add,0.0,r,,,r,1,add r at position 0,flow_matching,0.3,2.0,43,171
2,replace,0.0,5,r,r,5,1,replace r at position 0 with 5,flow_matching,0.3,2.0,43,171
3,replace,0.0,C,5,5,C,1,replace 5 at position 0 with C,flow_matching,0.3,2.0,43,171
4,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,43,171
5,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,43,171
6,replace,1.0,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON>c,3,replace <PERSON> at position 1 with B,flow_matching,0.3,2.0,43,171
7,add,0.0,c,,CBc,cCBc,4,add c at position 0,flow_matching,0.3,2.0,43,171
8,replace,0.0,C,c,cCBc,CCBc,4,replace c at position 0 with C,flow_matching,0.3,2.0,43,171
9,replace,1.0,O,C,CCBc,COBc,4,replace C at position 1 with O,flow_matching,0.3,2.0,43,171
10,replace,2.0,c,B,COBc,COcc,4,replace B at position 2 with c,flow_matching,0.3,2.0,43,171
11,replace,3.0,1,c,COcc,COc1,4,replace c at position 3 with 1,flow_matching,0.3,2.0,43,171
12,add,4.0,],,COc1,COc1],5,add ] at position 4,flow_matching,0.3,2.0,43,171
13,replace,4.0,c,],COc1],COc1c,5,replace ] at position 4 with c,flow_matching,0.3,2.0,43,171
14,remove,3.0,1,,COc1c,COcc,4,remove 1 from position 3,flow_matching,0.3,2.0,43,171
15,replace,0.0,#,C,COcc,#Occ,4,replace C at position 0 with #,flow_matching,0.3,2.0,43,171
16,add,2.0,[,,#Occ,#O[cc,5,add [ at position 2,flow_matching,0.3,2.0,43,171
17,replace,0.0,C,#,#O[cc,CO[cc,5,replace # at position 0 with C,flow_matching,0.3,2.0,43,171
18,add,2.0,B,,CO[cc,COB[cc,6,add B at position 2,flow_matching,0.3,2.0,43,171
19,replace,2.0,c,B,COB[cc,COc[cc,6,replace B at position 2 with c,flow_matching,0.3,2.0,43,171
20,remove,2.0,c,,COc[cc,CO[cc,5,remove c from position 2,flow_matching,0.3,2.0,43,171
21,replace,2.0,c,[,CO[cc,COccc,5,replace [ at position 2 with c,flow_matching,0.3,2.0,43,171
22,add,2.0,S,,COccc,COSccc,6,add S at position 2,flow_matching,0.3,2.0,43,171
23,replace,2.0,c,S,COSccc,COcccc,6,replace S at position 2 with c,flow_matching,0.3,2.0,43,171
24,replace,3.0,1,c,COcccc,COc1cc,6,replace c at position 3 with 1,flow_matching,0.3,2.0,43,171
25,add,6.0,c,,COc1cc,COc1ccc,7,add c at position 6,flow_matching,0.3,2.0,43,171
26,add,7.0,(,,COc1ccc,COc1ccc(,8,add ( at position 7,flow_matching,0.3,2.0,43,171
27,add,0.0,5,,COc1ccc(,5COc1ccc(,9,add 5 at position 0,flow_matching,0.3,2.0,43,171
28,replace,6.0,r,c,5COc1ccc(,5COc1crc(,9,replace c at position 6 with r,flow_matching,0.3,2.0,43,171
29,remove,3.0,c,,5COc1crc(,5CO1crc(,8,remove c from position 3,flow_matching,0.3,2.0,43,171
30,add,2.0,3,,5CO1crc(,5C3O1crc(,9,add 3 at position 2,flow_matching,0.3,2.0,43,171
31,replace,0.0,C,5,5C3O1crc(,CC3O1crc(,9,replace 5 at position 0 with C,flow_matching,0.3,2.0,43,171
32,remove,3.0,O,,CC3O1crc(,CC31crc(,8,remove O from position 3,flow_matching,0.3,2.0,43,171
33,remove,3.0,1,,CC31crc(,CC3crc(,7,remove 1 from position 3,flow_matching,0.3,2.0,43,171
34,replace,6.0,7,(,CC3crc(,CC3crc7,7,replace ( at position 6 with 7,flow_matching,0.3,2.0,43,171
35,replace,6.0,-,7,CC3crc7,CC3crc-,7,replace 7 at position 6 with -,flow_matching,0.3,2.0,43,171
36,add,4.0,\,,CC3crc-,CC3c\rc-,8,add \ at position 4,flow_matching,0.3,2.0,43,171
37,add,1.0,H,,CC3c\rc-,CHC3c\rc-,9,add H at position 1,flow_matching,0.3,2.0,43,171
38,remove,6.0,r,,CHC3c\rc-,CHC3c\c-,8,remove r from position 6,flow_matching,0.3,2.0,43,171
39,replace,4.0,H,c,CHC3c\c-,CHC3H\c-,8,replace c at position 4 with H,flow_matching,0.3,2.0,43,171
40,replace,1.0,O,H,CHC3H\c-,COC3H\c-,8,replace H at position 1 with O,flow_matching,0.3,2.0,43,171
41,remove,2.0,C,,COC3H\c-,CO3H\c-,7,remove C from position 2,flow_matching,0.3,2.0,43,171
42,remove,5.0,c,,CO3H\c-,CO3H\-,6,remove c from position 5,flow_matching,0.3,2.0,43,171
43,replace,1.0,B,O,CO3H\-,CB3H\-,6,replace O at position 1 with B,flow_matching,0.3,2.0,43,171
44,add,0.0,\,,CB3H\-,\CB3H\-,7,add \ at position 0,flow_matching,0.3,2.0,43,171
45,remove,3.0,3,,\CB3H\-,\CBH\-,6,remove 3 from position 3,flow_matching,0.3,2.0,43,171
46,replace,3.0,#,H,\CBH\-,\CB#\-,6,replace H at position 3 with #,flow_matching,0.3,2.0,43,171
47,add,3.0,(,,\CB#\-,\CB(#\-,7,add ( at position 3,flow_matching,0.3,2.0,43,171
48,replace,0.0,C,\,\CB(#\-,CCB(#\-,7,replace \ at position 0 with C,flow_matching,0.3,2.0,43,171
49,add,1.0,S,,CCB(#\-,CSCB(#\-,8,add S at position 1,flow_matching,0.3,2.0,43,171
50,replace,0.0,F,C,CSCB(#\-,FSCB(#\-,8,replace C at position 0 with F,flow_matching,0.3,2.0,43,171
51,add,6.0,3,,FSCB(#\-,FSCB(#3\-,9,add 3 at position 6,flow_matching,0.3,2.0,43,171
52,replace,0.0,C,F,FSCB(#3\-,CSCB(#3\-,9,replace F at position 0 with C,flow_matching,0.3,2.0,43,171
53,add,7.0,c,,CSCB(#3\-,CSCB(#3c\-,10,add c at position 7,flow_matching,0.3,2.0,43,171
54,replace,1.0,O,S,CSCB(#3c\-,COCB(#3c\-,10,replace S at position 1 with O,flow_matching,0.3,2.0,43,171
55,replace,2.0,c,C,COCB(#3c\-,COcB(#3c\-,10,replace C at position 2 with c,flow_matching,0.3,2.0,43,171
56,replace,3.0,1,B,COcB(#3c\-,COc1(#3c\-,10,replace B at position 3 with 1,flow_matching,0.3,2.0,43,171
57,replace,4.0,c,(,COc1(#3c\-,COc1c#3c\-,10,replace ( at position 4 with c,flow_matching,0.3,2.0,43,171
58,add,9.0,O,,COc1c#3c\-,COc1c#3c\O-,11,add O at position 9,flow_matching,0.3,2.0,43,171
59,add,11.0,@,,COc1c#3c\O-,COc1c#3c\O-@,12,add @ at position 11,flow_matching,0.3,2.0,43,171
60,replace,5.0,c,#,COc1c#3c\O-@,COc1cc3c\O-@,12,replace # at position 5 with c,flow_matching,0.3,2.0,43,171
61,remove,7.0,c,,COc1cc3c\O-@,COc1cc3\O-@,11,remove c from position 7,flow_matching,0.3,2.0,43,171
62,remove,7.0,\,,COc1cc3\O-@,COc1cc3O-@,10,remove \ from position 7,flow_matching,0.3,2.0,43,171
63,replace,6.0,+,3,COc1cc3O-@,COc1cc+O-@,10,replace 3 at position 6 with +,flow_matching,0.3,2.0,43,171
64,replace,7.0,r,O,COc1cc+O-@,COc1cc+r-@,10,replace O at position 7 with r,flow_matching,0.3,2.0,43,171
65,replace,6.0,c,+,COc1cc+r-@,COc1cccr-@,10,replace + at position 6 with c,flow_matching,0.3,2.0,43,171
66,replace,9.0,-,@,COc1cccr-@,COc1cccr--,10,replace @ at position 9 with -,flow_matching,0.3,2.0,43,171
67,replace,9.0,/,-,COc1cccr--,COc1cccr-/,10,replace - at position 9 with /,flow_matching,0.3,2.0,43,171
68,replace,7.0,(,r,COc1cccr-/,COc1ccc(-/,10,replace r at position 7 with (,flow_matching,0.3,2.0,43,171
69,replace,1.0,B,O,COc1ccc(-/,CBc1ccc(-/,10,replace O at position 1 with B,flow_matching,0.3,2.0,43,171
70,add,2.0,#,,CBc1ccc(-/,CB#c1ccc(-/,11,add # at position 2,flow_matching,0.3,2.0,43,171
71,replace,1.0,O,B,CB#c1ccc(-/,CO#c1ccc(-/,11,replace B at position 1 with O,flow_matching,0.3,2.0,43,171
72,add,10.0,c,,CO#c1ccc(-/,CO#c1ccc(-c/,12,add c at position 10,flow_matching,0.3,2.0,43,171
73,add,3.0,F,,CO#c1ccc(-c/,CO#Fc1ccc(-c/,13,add F at position 3,flow_matching,0.3,2.0,43,171
74,add,6.0,7,,CO#Fc1ccc(-c/,CO#Fc17ccc(-c/,14,add 7 at position 6,flow_matching,0.3,2.0,43,171
75,replace,2.0,c,#,CO#Fc17ccc(-c/,COcFc17ccc(-c/,14,replace # at position 2 with c,flow_matching,0.3,2.0,43,171
76,replace,3.0,1,F,COcFc17ccc(-c/,COc1c17ccc(-c/,14,replace F at position 3 with 1,flow_matching,0.3,2.0,43,171
77,replace,5.0,c,1,COc1c17ccc(-c/,COc1cc7ccc(-c/,14,replace 1 at position 5 with c,flow_matching,0.3,2.0,43,171
78,replace,6.0,c,7,COc1cc7ccc(-c/,COc1cccccc(-c/,14,replace 7 at position 6 with c,flow_matching,0.3,2.0,43,171
79,remove,9.0,c,,COc1cccccc(-c/,COc1ccccc(-c/,13,remove c from position 9,flow_matching,0.3,2.0,43,171
80,remove,3.0,1,,COc1ccccc(-c/,COcccccc(-c/,12,remove 1 from position 3,flow_matching,0.3,2.0,43,171
81,add,11.0,[,,COcccccc(-c/,COcccccc(-c[/,13,add [ at position 11,flow_matching,0.3,2.0,43,171
82,add,12.0,l,,COcccccc(-c[/,COcccccc(-c[l/,14,add l at position 12,flow_matching,0.3,2.0,43,171
83,add,3.0,F,,COcccccc(-c[l/,COcFccccc(-c[l/,15,add F at position 3,flow_matching,0.3,2.0,43,171
84,remove,0.0,C,,COcFccccc(-c[l/,OcFccccc(-c[l/,14,remove C from position 0,flow_matching,0.3,2.0,43,171
85,replace,12.0,o,l,OcFccccc(-c[l/,OcFccccc(-c[o/,14,replace l at position 12 with o,flow_matching,0.3,2.0,43,171
86,add,13.0,o,,OcFccccc(-c[o/,OcFccccc(-c[oo/,15,add o at position 13,flow_matching,0.3,2.0,43,171
87,add,9.0,r,,OcFccccc(-c[oo/,OcFccccc(r-c[oo/,16,add r at position 9,flow_matching,0.3,2.0,43,171
88,replace,0.0,C,O,OcFccccc(r-c[oo/,CcFccccc(r-c[oo/,16,replace O at position 0 with C,flow_matching,0.3,2.0,43,171
89,replace,1.0,O,c,CcFccccc(r-c[oo/,COFccccc(r-c[oo/,16,replace c at position 1 with O,flow_matching,0.3,2.0,43,171
90,add,8.0,6,,COFccccc(r-c[oo/,COFccccc6(r-c[oo/,17,add 6 at position 8,flow_matching,0.3,2.0,43,171
91,add,1.0,n,,COFccccc6(r-c[oo/,CnOFccccc6(r-c[oo/,18,add n at position 1,flow_matching,0.3,2.0,43,171
92,add,6.0,S,,CnOFccccc6(r-c[oo/,CnOFccSccc6(r-c[oo/,19,add S at position 6,flow_matching,0.3,2.0,43,171
93,remove,17.0,o,,CnOFccSccc6(r-c[oo/,CnOFccSccc6(r-c[o/,18,remove o from position 17,flow_matching,0.3,2.0,43,171
94,replace,1.0,O,n,CnOFccSccc6(r-c[o/,COOFccSccc6(r-c[o/,18,replace n at position 1 with O,flow_matching,0.3,2.0,43,171
95,remove,6.0,S,,COOFccSccc6(r-c[o/,COOFccccc6(r-c[o/,17,remove S from position 6,flow_matching,0.3,2.0,43,171
96,add,6.0,H,,COOFccccc6(r-c[o/,COOFccHccc6(r-c[o/,18,add H at position 6,flow_matching,0.3,2.0,43,171
97,add,8.0,[,,COOFccHccc6(r-c[o/,COOFccHc[cc6(r-c[o/,19,add [ at position 8,flow_matching,0.3,2.0,43,171
98,replace,2.0,c,O,COOFccHc[cc6(r-c[o/,COcFccHc[cc6(r-c[o/,19,replace O at position 2 with c,flow_matching,0.3,2.0,43,171
99,add,17.0,N,,COcFccHc[cc6(r-c[o/,COcFccHc[cc6(r-c[No/,20,add N at position 17,flow_matching,0.3,2.0,43,171
100,replace,3.0,1,F,COcFccHc[cc6(r-c[No/,COc1ccHc[cc6(r-c[No/,20,replace F at position 3 with 1,flow_matching,0.3,2.0,43,171
101,remove,14.0,-,,COc1ccHc[cc6(r-c[No/,COc1ccHc[cc6(rc[No/,19,remove - from position 14,flow_matching,0.3,2.0,43,171
102,replace,6.0,c,H,COc1ccHc[cc6(rc[No/,COc1cccc[cc6(rc[No/,19,replace H at position 6 with c,flow_matching,0.3,2.0,43,171
103,replace,13.0,5,r,COc1cccc[cc6(rc[No/,COc1cccc[cc6(5c[No/,19,replace r at position 13 with 5,flow_matching,0.3,2.0,43,171
104,replace,7.0,(,c,COc1cccc[cc6(5c[No/,COc1ccc([cc6(5c[No/,19,replace c at position 7 with (,flow_matching,0.3,2.0,43,171
105,replace,6.0,(,c,COc1ccc([cc6(5c[No/,COc1cc(([cc6(5c[No/,19,replace c at position 6 with (,flow_matching,0.3,2.0,43,171
106,remove,12.0,(,,COc1cc(([cc6(5c[No/,COc1cc(([cc65c[No/,18,remove ( from position 12,flow_matching,0.3,2.0,43,171
107,add,10.0,\,,COc1cc(([cc65c[No/,COc1cc(([c\c65c[No/,19,add \ at position 10,flow_matching,0.3,2.0,43,171
108,replace,6.0,c,(,COc1cc(([c\c65c[No/,COc1ccc([c\c65c[No/,19,replace ( at position 6 with c,flow_matching,0.3,2.0,43,171
109,add,1.0,),,COc1ccc([c\c65c[No/,C)Oc1ccc([c\c65c[No/,20,add ) at position 1,flow_matching,0.3,2.0,43,171
110,remove,7.0,c,,C)Oc1ccc([c\c65c[No/,C)Oc1cc([c\c65c[No/,19,remove c from position 7,flow_matching,0.3,2.0,43,171
111,replace,1.0,O,),C)Oc1cc([c\c65c[No/,COOc1cc([c\c65c[No/,19,replace ) at position 1 with O,flow_matching,0.3,2.0,43,171
112,add,1.0,O,,COOc1cc([c\c65c[No/,COOOc1cc([c\c65c[No/,20,add O at position 1,flow_matching,0.3,2.0,43,171
113,replace,2.0,c,O,COOOc1cc([c\c65c[No/,COcOc1cc([c\c65c[No/,20,replace O at position 2 with c,flow_matching,0.3,2.0,43,171
114,add,17.0,@,,COcOc1cc([c\c65c[No/,COcOc1cc([c\c65c[@No/,21,add @ at position 17,flow_matching,0.3,2.0,43,171
115,replace,3.0,1,O,COcOc1cc([c\c65c[@No/,COc1c1cc([c\c65c[@No/,21,replace O at position 3 with 1,flow_matching,0.3,2.0,43,171
116,add,8.0,1,,COc1c1cc([c\c65c[@No/,COc1c1cc1([c\c65c[@No/,22,add 1 at position 8,flow_matching,0.3,2.0,43,171
117,replace,12.0,+,\,COc1c1cc1([c\c65c[@No/,COc1c1cc1([c+c65c[@No/,22,replace \ at position 12 with +,flow_matching,0.3,2.0,43,171
118,add,3.0,4,,COc1c1cc1([c+c65c[@No/,COc41c1cc1([c+c65c[@No/,23,add 4 at position 3,flow_matching,0.3,2.0,43,171
119,replace,9.0,F,1,COc41c1cc1([c+c65c[@No/,COc41c1ccF([c+c65c[@No/,23,replace 1 at position 9 with F,flow_matching,0.3,2.0,43,171
120,replace,3.0,1,4,COc41c1ccF([c+c65c[@No/,COc11c1ccF([c+c65c[@No/,23,replace 4 at position 3 with 1,flow_matching,0.3,2.0,43,171
121,remove,19.0,@,,COc11c1ccF([c+c65c[@No/,COc11c1ccF([c+c65c[No/,22,remove @ from position 19,flow_matching,0.3,2.0,43,171
122,remove,5.0,c,,COc11c1ccF([c+c65c[No/,COc111ccF([c+c65c[No/,21,remove c from position 5,flow_matching,0.3,2.0,43,171
123,add,6.0,5,,COc111ccF([c+c65c[No/,COc1115ccF([c+c65c[No/,22,add 5 at position 6,flow_matching,0.3,2.0,43,171
124,replace,4.0,c,1,COc1115ccF([c+c65c[No/,COc1c15ccF([c+c65c[No/,22,replace 1 at position 4 with c,flow_matching,0.3,2.0,43,171
125,replace,2.0,H,c,COc1c15ccF([c+c65c[No/,COH1c15ccF([c+c65c[No/,22,replace c at position 2 with H,flow_matching,0.3,2.0,43,171
126,remove,12.0,c,,COH1c15ccF([c+c65c[No/,COH1c15ccF([+c65c[No/,21,remove c from position 12,flow_matching,0.3,2.0,43,171
127,replace,15.0,#,5,COH1c15ccF([+c65c[No/,COH1c15ccF([+c6#c[No/,21,replace 5 at position 15 with #,flow_matching,0.3,2.0,43,171
128,replace,2.0,[,H,COH1c15ccF([+c6#c[No/,CO[1c15ccF([+c6#c[No/,21,replace H at position 2 with [,flow_matching,0.3,2.0,43,171
129,add,8.0,O,,CO[1c15ccF([+c6#c[No/,CO[1c15cOcF([+c6#c[No/,22,add O at position 8,flow_matching,0.3,2.0,43,171
130,remove,20.0,o,,CO[1c15cOcF([+c6#c[No/,CO[1c15cOcF([+c6#c[N/,21,remove o from position 20,flow_matching,0.3,2.0,43,171
131,replace,2.0,c,[,CO[1c15cOcF([+c6#c[N/,COc1c15cOcF([+c6#c[N/,21,replace [ at position 2 with c,flow_matching,0.3,2.0,43,171
132,remove,15.0,6,,COc1c15cOcF([+c6#c[N/,COc1c15cOcF([+c#c[N/,20,remove 6 from position 15,flow_matching,0.3,2.0,43,171
133,replace,5.0,c,1,COc1c15cOcF([+c#c[N/,COc1cc5cOcF([+c#c[N/,20,replace 1 at position 5 with c,flow_matching,0.3,2.0,43,171
134,add,20.0,#,,COc1cc5cOcF([+c#c[N/,COc1cc5cOcF([+c#c[N/#,21,add # at position 20,flow_matching,0.3,2.0,43,171
135,replace,6.0,c,5,COc1cc5cOcF([+c#c[N/#,COc1ccccOcF([+c#c[N/#,21,replace 5 at position 6 with c,flow_matching,0.3,2.0,43,171
136,replace,7.0,(,c,COc1ccccOcF([+c#c[N/#,COc1ccc(OcF([+c#c[N/#,21,replace c at position 7 with (,flow_matching,0.3,2.0,43,171
137,replace,8.0,C,O,COc1ccc(OcF([+c#c[N/#,COc1ccc(CcF([+c#c[N/#,21,replace O at position 8 with C,flow_matching,0.3,2.0,43,171
138,replace,9.0,C,c,COc1ccc(CcF([+c#c[N/#,COc1ccc(CCF([+c#c[N/#,21,replace c at position 9 with C,flow_matching,0.3,2.0,43,171
139,replace,10.0,C,F,COc1ccc(CCF([+c#c[N/#,COc1ccc(CCC([+c#c[N/#,21,replace F at position 10 with C,flow_matching,0.3,2.0,43,171
140,replace,11.0,C,(,COc1ccc(CCC([+c#c[N/#,COc1ccc(CCCC[+c#c[N/#,21,replace ( at position 11 with C,flow_matching,0.3,2.0,43,171
141,replace,12.0,(,[,COc1ccc(CCCC[+c#c[N/#,COc1ccc(CCCC(+c#c[N/#,21,replace [ at position 12 with (,flow_matching,0.3,2.0,43,171
142,replace,13.0,=,+,COc1ccc(CCCC(+c#c[N/#,COc1ccc(CCCC(=c#c[N/#,21,replace + at position 13 with =,flow_matching,0.3,2.0,43,171
143,replace,14.0,O,c,COc1ccc(CCCC(=c#c[N/#,COc1ccc(CCCC(=O#c[N/#,21,replace c at position 14 with O,flow_matching,0.3,2.0,43,171
144,replace,15.0,),#,COc1ccc(CCCC(=O#c[N/#,COc1ccc(CCCC(=O)c[N/#,21,replace # at position 15 with ),flow_matching,0.3,2.0,43,171
145,replace,16.0,N,c,COc1ccc(CCCC(=O)c[N/#,COc1ccc(CCCC(=O)N[N/#,21,replace c at position 16 with N,flow_matching,0.3,2.0,43,171
146,replace,17.0,c,[,COc1ccc(CCCC(=O)N[N/#,COc1ccc(CCCC(=O)NcN/#,21,replace [ at position 17 with c,flow_matching,0.3,2.0,43,171
147,replace,18.0,2,N,COc1ccc(CCCC(=O)NcN/#,COc1ccc(CCCC(=O)Nc2/#,21,replace N at position 18 with 2,flow_matching,0.3,2.0,43,171
148,replace,19.0,c,/,COc1ccc(CCCC(=O)Nc2/#,COc1ccc(CCCC(=O)Nc2c#,21,replace / at position 19 with c,flow_matching,0.3,2.0,43,171
149,replace,20.0,c,#,COc1ccc(CCCC(=O)Nc2c#,COc1ccc(CCCC(=O)Nc2cc,21,replace # at position 20 with c,flow_matching,0.3,2.0,43,171
150,add,21.0,c,,COc1ccc(CCCC(=O)Nc2cc,COc1ccc(CCCC(=O)Nc2ccc,22,add c at position 21,flow_matching,0.3,2.0,43,171
151,add,22.0,c,,COc1ccc(CCCC(=O)Nc2ccc,COc1ccc(CCCC(=O)Nc2cccc,23,add c at position 22,flow_matching,0.3,2.0,43,171
152,add,23.0,(,,COc1ccc(CCCC(=O)Nc2cccc,COc1ccc(CCCC(=O)Nc2cccc(,24,add ( at position 23,flow_matching,0.3,2.0,43,171
153,add,24.0,S,,COc1ccc(CCCC(=O)Nc2cccc(,COc1ccc(CCCC(=O)Nc2cccc(S,25,add S at position 24,flow_matching,0.3,2.0,43,171
154,add,25.0,(,,COc1ccc(CCCC(=O)Nc2cccc(S,COc1ccc(CCCC(=O)Nc2cccc(S(,26,add ( at position 25,flow_matching,0.3,2.0,43,171
155,add,26.0,N,,COc1ccc(CCCC(=O)Nc2cccc(S(,COc1ccc(CCCC(=O)Nc2cccc(S(N,27,add N at position 26,flow_matching,0.3,2.0,43,171
156,add,27.0,),,COc1ccc(CCCC(=O)Nc2cccc(S(N,COc1ccc(CCCC(=O)Nc2cccc(S(N),28,add ) at position 27,flow_matching,0.3,2.0,43,171
157,add,28.0,(,,COc1ccc(CCCC(=O)Nc2cccc(S(N),COc1ccc(CCCC(=O)Nc2cccc(S(N)(,29,add ( at position 28,flow_matching,0.3,2.0,43,171
158,add,29.0,=,,COc1ccc(CCCC(=O)Nc2cccc(S(N)(,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=,30,add = at position 29,flow_matching,0.3,2.0,43,171
159,add,30.0,O,,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O,31,add O at position 30,flow_matching,0.3,2.0,43,171
160,add,31.0,),,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O),32,add ) at position 31,flow_matching,0.3,2.0,43,171
161,add,32.0,=,,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O),COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=,33,add = at position 32,flow_matching,0.3,2.0,43,171
162,add,33.0,O,,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O,34,add O at position 33,flow_matching,0.3,2.0,43,171
163,add,34.0,),,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O),35,add ) at position 34,flow_matching,0.3,2.0,43,171
164,add,35.0,c,,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O),COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c,36,add c at position 35,flow_matching,0.3,2.0,43,171
165,add,36.0,2,,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2,37,add 2 at position 36,flow_matching,0.3,2.0,43,171
166,add,37.0,),,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2),38,add ) at position 37,flow_matching,0.3,2.0,43,171
167,add,38.0,c,,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2),COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2)c,39,add c at position 38,flow_matching,0.3,2.0,43,171
168,add,39.0,c,,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2)c,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2)cc,40,add c at position 39,flow_matching,0.3,2.0,43,171
169,add,40.0,1,,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2)cc,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2)cc1,41,add 1 at position 40,flow_matching,0.3,2.0,43,171
170,add,41.0,F,,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2)cc1,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2)cc1F,42,add F at position 41,flow_matching,0.3,2.0,43,171
171,add,42.0,"
",,COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2)cc1F,"COc1ccc(CCCC(=O)Nc2cccc(S(N)(=O)=O)c2)cc1F
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,171
