step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,42,80
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,42,80
2,add,1.0,C,,C,CC,2,add <PERSON> at position 1,flow_matching,0.3,2.0,42,80
3,add,1.0,S,,CC,CSC,3,add S at position 1,flow_matching,0.3,2.0,42,80
4,remove,1.0,S,,CSC,CC,2,remove S from position 1,flow_matching,0.3,2.0,42,80
5,remove,0.0,C,,CC,C,1,remove C from position 0,flow_matching,0.3,2.0,42,80
6,replace,0.0,\,C,C,\,1,replace <PERSON> at position 0 with \,flow_matching,0.3,2.0,42,80
7,replace,0.0,C,\,\,C,1,replace \ at position 0 with C,flow_matching,0.3,2.0,42,80
8,add,1.0,C,,<PERSON>,CC,2,add C at position 1,flow_matching,0.3,2.0,42,80
9,remove,1.0,C,,CC,C,1,remove C from position 1,flow_matching,0.3,2.0,42,80
10,add,1.0,3,,C,C3,2,add 3 at position 1,flow_matching,0.3,2.0,42,80
11,replace,0.0,l,C,C3,l3,2,replace C at position 0 with l,flow_matching,0.3,2.0,42,80
12,replace,0.0,C,l,l3,C3,2,replace l at position 0 with C,flow_matching,0.3,2.0,42,80
13,replace,1.0,C,3,C3,CC,2,replace 3 at position 1 with C,flow_matching,0.3,2.0,42,80
14,add,2.0,],,CC,CC],3,add ] at position 2,flow_matching,0.3,2.0,42,80
15,replace,2.0,c,],CC],CCc,3,replace ] at position 2 with c,flow_matching,0.3,2.0,42,80
16,add,3.0,1,,CCc,CCc1,4,add 1 at position 3,flow_matching,0.3,2.0,42,80
17,add,4.0,n,,CCc1,CCc1n,5,add n at position 4,flow_matching,0.3,2.0,42,80
18,add,3.0,=,,CCc1n,CCc=1n,6,add = at position 3,flow_matching,0.3,2.0,42,80
19,replace,3.0,1,=,CCc=1n,CCc11n,6,replace = at position 3 with 1,flow_matching,0.3,2.0,42,80
20,replace,4.0,n,1,CCc11n,CCc1nn,6,replace 1 at position 4 with n,flow_matching,0.3,2.0,42,80
21,replace,5.0,4,n,CCc1nn,CCc1n4,6,replace n at position 5 with 4,flow_matching,0.3,2.0,42,80
22,replace,1.0,7,C,CCc1n4,C7c1n4,6,replace C at position 1 with 7,flow_matching,0.3,2.0,42,80
23,replace,1.0,r,7,C7c1n4,Crc1n4,6,replace 7 at position 1 with r,flow_matching,0.3,2.0,42,80
24,replace,1.0,C,r,Crc1n4,CCc1n4,6,replace r at position 1 with C,flow_matching,0.3,2.0,42,80
25,replace,5.0,o,4,CCc1n4,CCc1no,6,replace 4 at position 5 with o,flow_matching,0.3,2.0,42,80
26,replace,1.0,6,C,CCc1no,C6c1no,6,replace C at position 1 with 6,flow_matching,0.3,2.0,42,80
27,replace,1.0,C,6,C6c1no,CCc1no,6,replace 6 at position 1 with C,flow_matching,0.3,2.0,42,80
28,replace,1.0,@,C,CCc1no,C@c1no,6,replace C at position 1 with @,flow_matching,0.3,2.0,42,80
29,replace,1.0,C,@,C@c1no,CCc1no,6,replace @ at position 1 with C,flow_matching,0.3,2.0,42,80
30,add,3.0,r,,CCc1no,CCcr1no,7,add r at position 3,flow_matching,0.3,2.0,42,80
31,replace,3.0,1,r,CCcr1no,CCc11no,7,replace r at position 3 with 1,flow_matching,0.3,2.0,42,80
32,replace,4.0,n,1,CCc11no,CCc1nno,7,replace 1 at position 4 with n,flow_matching,0.3,2.0,42,80
33,replace,6.0,(,o,CCc1nno,CCc1nn(,7,replace o at position 6 with (,flow_matching,0.3,2.0,42,80
34,add,6.0,=,,CCc1nn(,CCc1nn=(,8,add = at position 6,flow_matching,0.3,2.0,42,80
35,add,4.0,r,,CCc1nn=(,CCc1rnn=(,9,add r at position 4,flow_matching,0.3,2.0,42,80
36,remove,1.0,C,,CCc1rnn=(,Cc1rnn=(,8,remove C from position 1,flow_matching,0.3,2.0,42,80
37,remove,0.0,C,,Cc1rnn=(,c1rnn=(,7,remove C from position 0,flow_matching,0.3,2.0,42,80
38,add,5.0,),,c1rnn=(,c1rnn)=(,8,add ) at position 5,flow_matching,0.3,2.0,42,80
39,remove,6.0,=,,c1rnn)=(,c1rnn)(,7,remove = from position 6,flow_matching,0.3,2.0,42,80
40,replace,0.0,C,c,c1rnn)(,C1rnn)(,7,replace c at position 0 with C,flow_matching,0.3,2.0,42,80
41,replace,1.0,C,1,C1rnn)(,CCrnn)(,7,replace 1 at position 1 with C,flow_matching,0.3,2.0,42,80
42,replace,2.0,c,r,CCrnn)(,CCcnn)(,7,replace r at position 2 with c,flow_matching,0.3,2.0,42,80
43,replace,3.0,1,n,CCcnn)(,CCc1n)(,7,replace n at position 3 with 1,flow_matching,0.3,2.0,42,80
44,replace,5.0,H,),CCc1n)(,CCc1nH(,7,replace ) at position 5 with H,flow_matching,0.3,2.0,42,80
45,replace,5.0,n,H,CCc1nH(,CCc1nn(,7,replace H at position 5 with n,flow_matching,0.3,2.0,42,80
46,add,7.0,C,,CCc1nn(,CCc1nn(C,8,add C at position 7,flow_matching,0.3,2.0,42,80
47,add,8.0,C,,CCc1nn(C,CCc1nn(CC,9,add C at position 8,flow_matching,0.3,2.0,42,80
48,add,9.0,),,CCc1nn(CC,CCc1nn(CC),10,add ) at position 9,flow_matching,0.3,2.0,42,80
49,add,10.0,c,,CCc1nn(CC),CCc1nn(CC)c,11,add c at position 10,flow_matching,0.3,2.0,42,80
50,add,11.0,(,,CCc1nn(CC)c,CCc1nn(CC)c(,12,add ( at position 11,flow_matching,0.3,2.0,42,80
51,add,12.0,C,,CCc1nn(CC)c(,CCc1nn(CC)c(C,13,add C at position 12,flow_matching,0.3,2.0,42,80
52,add,13.0,[,,CCc1nn(CC)c(C,CCc1nn(CC)c(C[,14,add [ at position 13,flow_matching,0.3,2.0,42,80
53,add,14.0,C,,CCc1nn(CC)c(C[,CCc1nn(CC)c(C[C,15,add C at position 14,flow_matching,0.3,2.0,42,80
54,add,15.0,@,,CCc1nn(CC)c(C[C,CCc1nn(CC)c(C[C@,16,add @ at position 15,flow_matching,0.3,2.0,42,80
55,add,16.0,@,,CCc1nn(CC)c(C[C@,CCc1nn(CC)c(C[C@@,17,add @ at position 16,flow_matching,0.3,2.0,42,80
56,add,17.0,],,CCc1nn(CC)c(C[C@@,CCc1nn(CC)c(C[C@@],18,add ] at position 17,flow_matching,0.3,2.0,42,80
57,add,18.0,2,,CCc1nn(CC)c(C[C@@],CCc1nn(CC)c(C[C@@]2,19,add 2 at position 18,flow_matching,0.3,2.0,42,80
58,add,19.0,(,,CCc1nn(CC)c(C[C@@]2,CCc1nn(CC)c(C[C@@]2(,20,add ( at position 19,flow_matching,0.3,2.0,42,80
59,add,20.0,C,,CCc1nn(CC)c(C[C@@]2(,CCc1nn(CC)c(C[C@@]2(C,21,add C at position 20,flow_matching,0.3,2.0,42,80
60,add,21.0,3,,CCc1nn(CC)c(C[C@@]2(C,CCc1nn(CC)c(C[C@@]2(C3,22,add 3 at position 21,flow_matching,0.3,2.0,42,80
61,add,22.0,C,,CCc1nn(CC)c(C[C@@]2(C3,CCc1nn(CC)c(C[C@@]2(C3C,23,add C at position 22,flow_matching,0.3,2.0,42,80
62,add,23.0,C,,CCc1nn(CC)c(C[C@@]2(C3C,CCc1nn(CC)c(C[C@@]2(C3CC,24,add C at position 23,flow_matching,0.3,2.0,42,80
63,add,24.0,3,,CCc1nn(CC)c(C[C@@]2(C3CC,CCc1nn(CC)c(C[C@@]2(C3CC3,25,add 3 at position 24,flow_matching,0.3,2.0,42,80
64,add,25.0,),,CCc1nn(CC)c(C[C@@]2(C3CC3,CCc1nn(CC)c(C[C@@]2(C3CC3),26,add ) at position 25,flow_matching,0.3,2.0,42,80
65,add,26.0,C,,CCc1nn(CC)c(C[C@@]2(C3CC3),CCc1nn(CC)c(C[C@@]2(C3CC3)C,27,add C at position 26,flow_matching,0.3,2.0,42,80
66,add,27.0,C,,CCc1nn(CC)c(C[C@@]2(C3CC3)C,CCc1nn(CC)c(C[C@@]2(C3CC3)CC,28,add C at position 27,flow_matching,0.3,2.0,42,80
67,add,28.0,C,,CCc1nn(CC)c(C[C@@]2(C3CC3)CC,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC,29,add C at position 28,flow_matching,0.3,2.0,42,80
68,add,29.0,[,,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[,30,add [ at position 29,flow_matching,0.3,2.0,42,80
69,add,30.0,N,,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[N,31,add N at position 30,flow_matching,0.3,2.0,42,80
70,add,31.0,H,,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[N,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH,32,add H at position 31,flow_matching,0.3,2.0,42,80
71,add,32.0,2,,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2,33,add 2 at position 32,flow_matching,0.3,2.0,42,80
72,add,33.0,+,,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+,34,add + at position 33,flow_matching,0.3,2.0,42,80
73,add,34.0,],,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+],35,add ] at position 34,flow_matching,0.3,2.0,42,80
74,add,35.0,2,,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+],CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2,36,add 2 at position 35,flow_matching,0.3,2.0,42,80
75,add,36.0,),,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2),37,add ) at position 36,flow_matching,0.3,2.0,42,80
76,add,37.0,c,,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2),CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2)c,38,add c at position 37,flow_matching,0.3,2.0,42,80
77,add,38.0,1,,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2)c,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2)c1,39,add 1 at position 38,flow_matching,0.3,2.0,42,80
78,add,39.0,B,,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2)c1,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2)c1B,40,add B at position 39,flow_matching,0.3,2.0,42,80
79,add,40.0,r,,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2)c1B,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2)c1Br,41,add r at position 40,flow_matching,0.3,2.0,42,80
80,add,41.0,"
",,CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2)c1Br,"CCc1nn(CC)c(C[C@@]2(C3CC3)CCC[NH2+]2)c1Br
",42,"add 
 at position 41",flow_matching,0.3,2.0,42,80
