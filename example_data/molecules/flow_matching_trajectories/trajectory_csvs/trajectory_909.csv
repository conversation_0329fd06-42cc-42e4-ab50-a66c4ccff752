step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,40,173
1,add,0.0,s,,,s,1,add s at position 0,flow_matching,0.3,2.0,40,173
2,remove,0.0,s,,s,,0,remove s from position 0,flow_matching,0.3,2.0,40,173
3,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,40,173
4,remove,0.0,F,,F,,0,remove F from position 0,flow_matching,0.3,2.0,40,173
5,add,0.0,5,,,5,1,add 5 at position 0,flow_matching,0.3,2.0,40,173
6,add,0.0,c,,5,c5,2,add c at position 0,flow_matching,0.3,2.0,40,173
7,replace,0.0,C,c,c5,C5,2,replace c at position 0 with C,flow_matching,0.3,2.0,40,173
8,add,2.0,/,,C5,C5/,3,add / at position 2,flow_matching,0.3,2.0,40,173
9,replace,1.0,c,5,C5/,Cc/,3,replace 5 at position 1 with c,flow_matching,0.3,2.0,40,173
10,replace,1.0,1,c,Cc/,C1/,3,replace c at position 1 with 1,flow_matching,0.3,2.0,40,173
11,replace,1.0,c,1,C1/,Cc/,3,replace 1 at position 1 with c,flow_matching,0.3,2.0,40,173
12,add,0.0,[,,Cc/,[Cc/,4,add [ at position 0,flow_matching,0.3,2.0,40,173
13,replace,3.0,B,/,[Cc/,[CcB,4,replace / at position 3 with B,flow_matching,0.3,2.0,40,173
14,replace,0.0,C,[,[CcB,CCcB,4,replace [ at position 0 with C,flow_matching,0.3,2.0,40,173
15,replace,1.0,c,C,CCcB,CccB,4,replace C at position 1 with c,flow_matching,0.3,2.0,40,173
16,replace,2.0,1,c,CccB,Cc1B,4,replace c at position 2 with 1,flow_matching,0.3,2.0,40,173
17,replace,3.0,c,B,Cc1B,Cc1c,4,replace B at position 3 with c,flow_matching,0.3,2.0,40,173
18,remove,1.0,c,,Cc1c,C1c,3,remove c from position 1,flow_matching,0.3,2.0,40,173
19,replace,1.0,c,1,C1c,Ccc,3,replace 1 at position 1 with c,flow_matching,0.3,2.0,40,173
20,add,2.0,+,,Ccc,Cc+c,4,add + at position 2,flow_matching,0.3,2.0,40,173
21,replace,2.0,1,+,Cc+c,Cc1c,4,replace + at position 2 with 1,flow_matching,0.3,2.0,40,173
22,add,4.0,\,,Cc1c,Cc1c\,5,add \ at position 4,flow_matching,0.3,2.0,40,173
23,replace,4.0,s,\,Cc1c\,Cc1cs,5,replace \ at position 4 with s,flow_matching,0.3,2.0,40,173
24,replace,2.0,\,1,Cc1cs,Cc\cs,5,replace 1 at position 2 with \,flow_matching,0.3,2.0,40,173
25,replace,2.0,1,\,Cc\cs,Cc1cs,5,replace \ at position 2 with 1,flow_matching,0.3,2.0,40,173
26,add,5.0,c,,Cc1cs,Cc1csc,6,add c at position 5,flow_matching,0.3,2.0,40,173
27,add,6.0,(,,Cc1csc,Cc1csc(,7,add ( at position 6,flow_matching,0.3,2.0,40,173
28,add,3.0,1,,Cc1csc(,Cc11csc(,8,add 1 at position 3,flow_matching,0.3,2.0,40,173
29,add,3.0,+,,Cc11csc(,Cc1+1csc(,9,add + at position 3,flow_matching,0.3,2.0,40,173
30,replace,6.0,),s,Cc1+1csc(,Cc1+1c)c(,9,replace s at position 6 with ),flow_matching,0.3,2.0,40,173
31,add,3.0,@,,Cc1+1c)c(,Cc1@+1c)c(,10,add @ at position 3,flow_matching,0.3,2.0,40,173
32,remove,4.0,+,,Cc1@+1c)c(,Cc1@1c)c(,9,remove + from position 4,flow_matching,0.3,2.0,40,173
33,replace,4.0,l,1,Cc1@1c)c(,Cc1@lc)c(,9,replace 1 at position 4 with l,flow_matching,0.3,2.0,40,173
34,replace,3.0,c,@,Cc1@lc)c(,Cc1clc)c(,9,replace @ at position 3 with c,flow_matching,0.3,2.0,40,173
35,remove,3.0,c,,Cc1clc)c(,Cc1lc)c(,8,remove c from position 3,flow_matching,0.3,2.0,40,173
36,remove,1.0,c,,Cc1lc)c(,C1lc)c(,7,remove c from position 1,flow_matching,0.3,2.0,40,173
37,replace,1.0,c,1,C1lc)c(,Cclc)c(,7,replace 1 at position 1 with c,flow_matching,0.3,2.0,40,173
38,replace,2.0,1,l,Cclc)c(,Cc1c)c(,7,replace l at position 2 with 1,flow_matching,0.3,2.0,40,173
39,add,0.0,1,,Cc1c)c(,1Cc1c)c(,8,add 1 at position 0,flow_matching,0.3,2.0,40,173
40,add,3.0,2,,1Cc1c)c(,1Cc21c)c(,9,add 2 at position 3,flow_matching,0.3,2.0,40,173
41,add,6.0,F,,1Cc21c)c(,1Cc21cF)c(,10,add F at position 6,flow_matching,0.3,2.0,40,173
42,replace,0.0,C,1,1Cc21cF)c(,CCc21cF)c(,10,replace 1 at position 0 with C,flow_matching,0.3,2.0,40,173
43,add,8.0,O,,CCc21cF)c(,CCc21cF)Oc(,11,add O at position 8,flow_matching,0.3,2.0,40,173
44,replace,6.0,B,F,CCc21cF)Oc(,CCc21cB)Oc(,11,replace F at position 6 with B,flow_matching,0.3,2.0,40,173
45,add,10.0,),,CCc21cB)Oc(,CCc21cB)Oc)(,12,add ) at position 10,flow_matching,0.3,2.0,40,173
46,replace,1.0,c,C,CCc21cB)Oc)(,Ccc21cB)Oc)(,12,replace C at position 1 with c,flow_matching,0.3,2.0,40,173
47,add,5.0,3,,Ccc21cB)Oc)(,Ccc213cB)Oc)(,13,add 3 at position 5,flow_matching,0.3,2.0,40,173
48,replace,2.0,1,c,Ccc213cB)Oc)(,Cc1213cB)Oc)(,13,replace c at position 2 with 1,flow_matching,0.3,2.0,40,173
49,replace,3.0,c,2,Cc1213cB)Oc)(,Cc1c13cB)Oc)(,13,replace 2 at position 3 with c,flow_matching,0.3,2.0,40,173
50,add,10.0,(,,Cc1c13cB)Oc)(,Cc1c13cB)O(c)(,14,add ( at position 10,flow_matching,0.3,2.0,40,173
51,replace,4.0,s,1,Cc1c13cB)O(c)(,Cc1cs3cB)O(c)(,14,replace 1 at position 4 with s,flow_matching,0.3,2.0,40,173
52,replace,5.0,c,3,Cc1cs3cB)O(c)(,Cc1csccB)O(c)(,14,replace 3 at position 5 with c,flow_matching,0.3,2.0,40,173
53,replace,12.0,[,),Cc1csccB)O(c)(,Cc1csccB)O(c[(,14,replace ) at position 12 with [,flow_matching,0.3,2.0,40,173
54,add,5.0,(,,Cc1csccB)O(c[(,Cc1cs(ccB)O(c[(,15,add ( at position 5,flow_matching,0.3,2.0,40,173
55,replace,5.0,c,(,Cc1cs(ccB)O(c[(,Cc1cscccB)O(c[(,15,replace ( at position 5 with c,flow_matching,0.3,2.0,40,173
56,add,7.0,+,,Cc1cscccB)O(c[(,Cc1cscc+cB)O(c[(,16,add + at position 7,flow_matching,0.3,2.0,40,173
57,replace,15.0,6,(,Cc1cscc+cB)O(c[(,Cc1cscc+cB)O(c[6,16,replace ( at position 15 with 6,flow_matching,0.3,2.0,40,173
58,replace,15.0,[,6,Cc1cscc+cB)O(c[6,Cc1cscc+cB)O(c[[,16,replace 6 at position 15 with [,flow_matching,0.3,2.0,40,173
59,replace,6.0,(,c,Cc1cscc+cB)O(c[[,Cc1csc(+cB)O(c[[,16,replace c at position 6 with (,flow_matching,0.3,2.0,40,173
60,remove,11.0,O,,Cc1csc(+cB)O(c[[,Cc1csc(+cB)(c[[,15,remove O from position 11,flow_matching,0.3,2.0,40,173
61,remove,6.0,(,,Cc1csc(+cB)(c[[,Cc1csc+cB)(c[[,14,remove ( from position 6,flow_matching,0.3,2.0,40,173
62,replace,6.0,(,+,Cc1csc+cB)(c[[,Cc1csc(cB)(c[[,14,replace + at position 6 with (,flow_matching,0.3,2.0,40,173
63,add,11.0,+,,Cc1csc(cB)(c[[,Cc1csc(cB)(+c[[,15,add + at position 11,flow_matching,0.3,2.0,40,173
64,add,4.0,),,Cc1csc(cB)(+c[[,Cc1c)sc(cB)(+c[[,16,add ) at position 4,flow_matching,0.3,2.0,40,173
65,replace,7.0,\,(,Cc1c)sc(cB)(+c[[,Cc1c)sc\cB)(+c[[,16,replace ( at position 7 with \,flow_matching,0.3,2.0,40,173
66,add,6.0,F,,Cc1c)sc\cB)(+c[[,Cc1c)sFc\cB)(+c[[,17,add F at position 6,flow_matching,0.3,2.0,40,173
67,replace,4.0,s,),Cc1c)sFc\cB)(+c[[,Cc1cssFc\cB)(+c[[,17,replace ) at position 4 with s,flow_matching,0.3,2.0,40,173
68,add,12.0,4,,Cc1cssFc\cB)(+c[[,Cc1cssFc\cB)4(+c[[,18,add 4 at position 12,flow_matching,0.3,2.0,40,173
69,replace,5.0,c,s,Cc1cssFc\cB)4(+c[[,Cc1cscFc\cB)4(+c[[,18,replace s at position 5 with c,flow_matching,0.3,2.0,40,173
70,replace,6.0,(,F,Cc1cscFc\cB)4(+c[[,Cc1csc(c\cB)4(+c[[,18,replace F at position 6 with (,flow_matching,0.3,2.0,40,173
71,replace,7.0,[,c,Cc1csc(c\cB)4(+c[[,Cc1csc([\cB)4(+c[[,18,replace c at position 7 with [,flow_matching,0.3,2.0,40,173
72,remove,8.0,\,,Cc1csc([\cB)4(+c[[,Cc1csc([cB)4(+c[[,17,remove \ from position 8,flow_matching,0.3,2.0,40,173
73,replace,12.0,s,(,Cc1csc([cB)4(+c[[,Cc1csc([cB)4s+c[[,17,replace ( at position 12 with s,flow_matching,0.3,2.0,40,173
74,add,2.0,+,,Cc1csc([cB)4s+c[[,Cc+1csc([cB)4s+c[[,18,add + at position 2,flow_matching,0.3,2.0,40,173
75,replace,2.0,1,+,Cc+1csc([cB)4s+c[[,Cc11csc([cB)4s+c[[,18,replace + at position 2 with 1,flow_matching,0.3,2.0,40,173
76,replace,2.0,-,1,Cc11csc([cB)4s+c[[,Cc-1csc([cB)4s+c[[,18,replace 1 at position 2 with -,flow_matching,0.3,2.0,40,173
77,replace,5.0,S,s,Cc-1csc([cB)4s+c[[,Cc-1cSc([cB)4s+c[[,18,replace s at position 5 with S,flow_matching,0.3,2.0,40,173
78,replace,14.0,S,+,Cc-1cSc([cB)4s+c[[,Cc-1cSc([cB)4sSc[[,18,replace + at position 14 with S,flow_matching,0.3,2.0,40,173
79,add,10.0,#,,Cc-1cSc([cB)4sSc[[,Cc-1cSc([c#B)4sSc[[,19,add # at position 10,flow_matching,0.3,2.0,40,173
80,add,12.0,o,,Cc-1cSc([c#B)4sSc[[,Cc-1cSc([c#Bo)4sSc[[,20,add o at position 12,flow_matching,0.3,2.0,40,173
81,add,2.0,7,,Cc-1cSc([c#Bo)4sSc[[,Cc7-1cSc([c#Bo)4sSc[[,21,add 7 at position 2,flow_matching,0.3,2.0,40,173
82,remove,1.0,c,,Cc7-1cSc([c#Bo)4sSc[[,C7-1cSc([c#Bo)4sSc[[,20,remove c from position 1,flow_matching,0.3,2.0,40,173
83,replace,15.0,\,s,C7-1cSc([c#Bo)4sSc[[,C7-1cSc([c#Bo)4\Sc[[,20,replace s at position 15 with \,flow_matching,0.3,2.0,40,173
84,replace,19.0,#,[,C7-1cSc([c#Bo)4\Sc[[,C7-1cSc([c#Bo)4\Sc[#,20,replace [ at position 19 with #,flow_matching,0.3,2.0,40,173
85,add,20.0,(,,C7-1cSc([c#Bo)4\Sc[#,C7-1cSc([c#Bo)4\Sc[#(,21,add ( at position 20,flow_matching,0.3,2.0,40,173
86,remove,17.0,c,,C7-1cSc([c#Bo)4\Sc[#(,C7-1cSc([c#Bo)4\S[#(,20,remove c from position 17,flow_matching,0.3,2.0,40,173
87,replace,1.0,c,7,C7-1cSc([c#Bo)4\S[#(,Cc-1cSc([c#Bo)4\S[#(,20,replace 7 at position 1 with c,flow_matching,0.3,2.0,40,173
88,replace,8.0,],[,Cc-1cSc([c#Bo)4\S[#(,Cc-1cSc(]c#Bo)4\S[#(,20,replace [ at position 8 with ],flow_matching,0.3,2.0,40,173
89,replace,2.0,1,-,Cc-1cSc(]c#Bo)4\S[#(,Cc11cSc(]c#Bo)4\S[#(,20,replace - at position 2 with 1,flow_matching,0.3,2.0,40,173
90,add,16.0,l,,Cc11cSc(]c#Bo)4\S[#(,Cc11cSc(]c#Bo)4\lS[#(,21,add l at position 16,flow_matching,0.3,2.0,40,173
91,add,7.0,/,,Cc11cSc(]c#Bo)4\lS[#(,Cc11cSc/(]c#Bo)4\lS[#(,22,add / at position 7,flow_matching,0.3,2.0,40,173
92,replace,3.0,c,1,Cc11cSc/(]c#Bo)4\lS[#(,Cc1ccSc/(]c#Bo)4\lS[#(,22,replace 1 at position 3 with c,flow_matching,0.3,2.0,40,173
93,replace,8.0,2,(,Cc1ccSc/(]c#Bo)4\lS[#(,Cc1ccSc/2]c#Bo)4\lS[#(,22,replace ( at position 8 with 2,flow_matching,0.3,2.0,40,173
94,replace,16.0,[,\,Cc1ccSc/2]c#Bo)4\lS[#(,Cc1ccSc/2]c#Bo)4[lS[#(,22,replace \ at position 16 with [,flow_matching,0.3,2.0,40,173
95,remove,12.0,B,,Cc1ccSc/2]c#Bo)4[lS[#(,Cc1ccSc/2]c#o)4[lS[#(,21,remove B from position 12,flow_matching,0.3,2.0,40,173
96,replace,4.0,s,c,Cc1ccSc/2]c#o)4[lS[#(,Cc1csSc/2]c#o)4[lS[#(,21,replace c at position 4 with s,flow_matching,0.3,2.0,40,173
97,replace,5.0,c,S,Cc1csSc/2]c#o)4[lS[#(,Cc1cscc/2]c#o)4[lS[#(,21,replace S at position 5 with c,flow_matching,0.3,2.0,40,173
98,add,15.0,2,,Cc1cscc/2]c#o)4[lS[#(,Cc1cscc/2]c#o)42[lS[#(,22,add 2 at position 15,flow_matching,0.3,2.0,40,173
99,replace,6.0,(,c,Cc1cscc/2]c#o)42[lS[#(,Cc1csc(/2]c#o)42[lS[#(,22,replace c at position 6 with (,flow_matching,0.3,2.0,40,173
100,add,9.0,I,,Cc1csc(/2]c#o)42[lS[#(,Cc1csc(/2I]c#o)42[lS[#(,23,add I at position 9,flow_matching,0.3,2.0,40,173
101,replace,7.0,[,/,Cc1csc(/2I]c#o)42[lS[#(,Cc1csc([2I]c#o)42[lS[#(,23,replace / at position 7 with [,flow_matching,0.3,2.0,40,173
102,add,0.0,B,,Cc1csc([2I]c#o)42[lS[#(,BCc1csc([2I]c#o)42[lS[#(,24,add B at position 0,flow_matching,0.3,2.0,40,173
103,replace,0.0,C,B,BCc1csc([2I]c#o)42[lS[#(,CCc1csc([2I]c#o)42[lS[#(,24,replace B at position 0 with C,flow_matching,0.3,2.0,40,173
104,remove,2.0,c,,CCc1csc([2I]c#o)42[lS[#(,CC1csc([2I]c#o)42[lS[#(,23,remove c from position 2,flow_matching,0.3,2.0,40,173
105,replace,1.0,c,C,CC1csc([2I]c#o)42[lS[#(,Cc1csc([2I]c#o)42[lS[#(,23,replace C at position 1 with c,flow_matching,0.3,2.0,40,173
106,replace,8.0,C,2,Cc1csc([2I]c#o)42[lS[#(,Cc1csc([CI]c#o)42[lS[#(,23,replace 2 at position 8 with C,flow_matching,0.3,2.0,40,173
107,replace,9.0,@,I,Cc1csc([CI]c#o)42[lS[#(,Cc1csc([C@]c#o)42[lS[#(,23,replace I at position 9 with @,flow_matching,0.3,2.0,40,173
108,add,18.0,c,,Cc1csc([C@]c#o)42[lS[#(,Cc1csc([C@]c#o)42[clS[#(,24,add c at position 18,flow_matching,0.3,2.0,40,173
109,remove,8.0,C,,Cc1csc([C@]c#o)42[clS[#(,Cc1csc([@]c#o)42[clS[#(,23,remove C from position 8,flow_matching,0.3,2.0,40,173
110,add,12.0,r,,Cc1csc([@]c#o)42[clS[#(,Cc1csc([@]c#ro)42[clS[#(,24,add r at position 12,flow_matching,0.3,2.0,40,173
111,replace,8.0,C,@,Cc1csc([@]c#ro)42[clS[#(,Cc1csc([C]c#ro)42[clS[#(,24,replace @ at position 8 with C,flow_matching,0.3,2.0,40,173
112,remove,21.0,[,,Cc1csc([C]c#ro)42[clS[#(,Cc1csc([C]c#ro)42[clS#(,23,remove [ from position 21,flow_matching,0.3,2.0,40,173
113,add,18.0,=,,Cc1csc([C]c#ro)42[clS#(,Cc1csc([C]c#ro)42[=clS#(,24,add = at position 18,flow_matching,0.3,2.0,40,173
114,replace,16.0,I,2,Cc1csc([C]c#ro)42[=clS#(,Cc1csc([C]c#ro)4I[=clS#(,24,replace 2 at position 16 with I,flow_matching,0.3,2.0,40,173
115,add,1.0,#,,Cc1csc([C]c#ro)4I[=clS#(,C#c1csc([C]c#ro)4I[=clS#(,25,add # at position 1,flow_matching,0.3,2.0,40,173
116,add,12.0,),,C#c1csc([C]c#ro)4I[=clS#(,C#c1csc([C]c)#ro)4I[=clS#(,26,add ) at position 12,flow_matching,0.3,2.0,40,173
117,remove,5.0,s,,C#c1csc([C]c)#ro)4I[=clS#(,C#c1cc([C]c)#ro)4I[=clS#(,25,remove s from position 5,flow_matching,0.3,2.0,40,173
118,replace,21.0,4,l,C#c1cc([C]c)#ro)4I[=clS#(,C#c1cc([C]c)#ro)4I[=c4S#(,25,replace l at position 21 with 4,flow_matching,0.3,2.0,40,173
119,replace,1.0,c,#,C#c1cc([C]c)#ro)4I[=c4S#(,Ccc1cc([C]c)#ro)4I[=c4S#(,25,replace # at position 1 with c,flow_matching,0.3,2.0,40,173
120,remove,23.0,#,,Ccc1cc([C]c)#ro)4I[=c4S#(,Ccc1cc([C]c)#ro)4I[=c4S(,24,remove # from position 23,flow_matching,0.3,2.0,40,173
121,add,18.0,(,,Ccc1cc([C]c)#ro)4I[=c4S(,Ccc1cc([C]c)#ro)4I([=c4S(,25,add ( at position 18,flow_matching,0.3,2.0,40,173
122,add,8.0,\,,Ccc1cc([C]c)#ro)4I([=c4S(,Ccc1cc([\C]c)#ro)4I([=c4S(,26,add \ at position 8,flow_matching,0.3,2.0,40,173
123,replace,2.0,1,c,Ccc1cc([\C]c)#ro)4I([=c4S(,Cc11cc([\C]c)#ro)4I([=c4S(,26,replace c at position 2 with 1,flow_matching,0.3,2.0,40,173
124,replace,3.0,c,1,Cc11cc([\C]c)#ro)4I([=c4S(,Cc1ccc([\C]c)#ro)4I([=c4S(,26,replace 1 at position 3 with c,flow_matching,0.3,2.0,40,173
125,add,9.0,/,,Cc1ccc([\C]c)#ro)4I([=c4S(,Cc1ccc([\/C]c)#ro)4I([=c4S(,27,add / at position 9,flow_matching,0.3,2.0,40,173
126,add,6.0,\,,Cc1ccc([\/C]c)#ro)4I([=c4S(,Cc1ccc\([\/C]c)#ro)4I([=c4S(,28,add \ at position 6,flow_matching,0.3,2.0,40,173
127,replace,21.0,H,(,Cc1ccc\([\/C]c)#ro)4I([=c4S(,Cc1ccc\([\/C]c)#ro)4IH[=c4S(,28,replace ( at position 21 with H,flow_matching,0.3,2.0,40,173
128,add,25.0,C,,Cc1ccc\([\/C]c)#ro)4IH[=c4S(,Cc1ccc\([\/C]c)#ro)4IH[=cC4S(,29,add C at position 25,flow_matching,0.3,2.0,40,173
129,remove,25.0,C,,Cc1ccc\([\/C]c)#ro)4IH[=cC4S(,Cc1ccc\([\/C]c)#ro)4IH[=c4S(,28,remove C from position 25,flow_matching,0.3,2.0,40,173
130,remove,2.0,1,,Cc1ccc\([\/C]c)#ro)4IH[=c4S(,Ccccc\([\/C]c)#ro)4IH[=c4S(,27,remove 1 from position 2,flow_matching,0.3,2.0,40,173
131,replace,2.0,1,c,Ccccc\([\/C]c)#ro)4IH[=c4S(,Cc1cc\([\/C]c)#ro)4IH[=c4S(,27,replace c at position 2 with 1,flow_matching,0.3,2.0,40,173
132,add,5.0,#,,Cc1cc\([\/C]c)#ro)4IH[=c4S(,Cc1cc#\([\/C]c)#ro)4IH[=c4S(,28,add # at position 5,flow_matching,0.3,2.0,40,173
133,add,20.0,#,,Cc1cc#\([\/C]c)#ro)4IH[=c4S(,Cc1cc#\([\/C]c)#ro)4#IH[=c4S(,29,add # at position 20,flow_matching,0.3,2.0,40,173
134,remove,18.0,),,Cc1cc#\([\/C]c)#ro)4#IH[=c4S(,Cc1cc#\([\/C]c)#ro4#IH[=c4S(,28,remove ) from position 18,flow_matching,0.3,2.0,40,173
135,remove,14.0,),,Cc1cc#\([\/C]c)#ro4#IH[=c4S(,Cc1cc#\([\/C]c#ro4#IH[=c4S(,27,remove ) from position 14,flow_matching,0.3,2.0,40,173
136,remove,11.0,C,,Cc1cc#\([\/C]c#ro4#IH[=c4S(,Cc1cc#\([\/]c#ro4#IH[=c4S(,26,remove C from position 11,flow_matching,0.3,2.0,40,173
137,replace,11.0,l,],Cc1cc#\([\/]c#ro4#IH[=c4S(,Cc1cc#\([\/lc#ro4#IH[=c4S(,26,replace ] at position 11 with l,flow_matching,0.3,2.0,40,173
138,replace,4.0,s,c,Cc1cc#\([\/lc#ro4#IH[=c4S(,Cc1cs#\([\/lc#ro4#IH[=c4S(,26,replace c at position 4 with s,flow_matching,0.3,2.0,40,173
139,replace,5.0,c,#,Cc1cs#\([\/lc#ro4#IH[=c4S(,Cc1csc\([\/lc#ro4#IH[=c4S(,26,replace # at position 5 with c,flow_matching,0.3,2.0,40,173
140,replace,6.0,(,\,Cc1csc\([\/lc#ro4#IH[=c4S(,Cc1csc(([\/lc#ro4#IH[=c4S(,26,replace \ at position 6 with (,flow_matching,0.3,2.0,40,173
141,replace,7.0,[,(,Cc1csc(([\/lc#ro4#IH[=c4S(,Cc1csc([[\/lc#ro4#IH[=c4S(,26,replace ( at position 7 with [,flow_matching,0.3,2.0,40,173
142,replace,8.0,C,[,Cc1csc([[\/lc#ro4#IH[=c4S(,Cc1csc([C\/lc#ro4#IH[=c4S(,26,replace [ at position 8 with C,flow_matching,0.3,2.0,40,173
143,replace,9.0,@,\,Cc1csc([C\/lc#ro4#IH[=c4S(,Cc1csc([C@/lc#ro4#IH[=c4S(,26,replace \ at position 9 with @,flow_matching,0.3,2.0,40,173
144,replace,10.0,H,/,Cc1csc([C@/lc#ro4#IH[=c4S(,Cc1csc([C@Hlc#ro4#IH[=c4S(,26,replace / at position 10 with H,flow_matching,0.3,2.0,40,173
145,replace,11.0,],l,Cc1csc([C@Hlc#ro4#IH[=c4S(,Cc1csc([C@H]c#ro4#IH[=c4S(,26,replace l at position 11 with ],flow_matching,0.3,2.0,40,173
146,replace,12.0,(,c,Cc1csc([C@H]c#ro4#IH[=c4S(,Cc1csc([C@H](#ro4#IH[=c4S(,26,replace c at position 12 with (,flow_matching,0.3,2.0,40,173
147,replace,13.0,C,#,Cc1csc([C@H](#ro4#IH[=c4S(,Cc1csc([C@H](Cro4#IH[=c4S(,26,replace # at position 13 with C,flow_matching,0.3,2.0,40,173
148,replace,14.0,),r,Cc1csc([C@H](Cro4#IH[=c4S(,Cc1csc([C@H](C)o4#IH[=c4S(,26,replace r at position 14 with ),flow_matching,0.3,2.0,40,173
149,replace,15.0,N,o,Cc1csc([C@H](C)o4#IH[=c4S(,Cc1csc([C@H](C)N4#IH[=c4S(,26,replace o at position 15 with N,flow_matching,0.3,2.0,40,173
150,replace,16.0,C,4,Cc1csc([C@H](C)N4#IH[=c4S(,Cc1csc([C@H](C)NC#IH[=c4S(,26,replace 4 at position 16 with C,flow_matching,0.3,2.0,40,173
151,replace,17.0,(,#,Cc1csc([C@H](C)NC#IH[=c4S(,Cc1csc([C@H](C)NC(IH[=c4S(,26,replace # at position 17 with (,flow_matching,0.3,2.0,40,173
152,replace,18.0,=,I,Cc1csc([C@H](C)NC(IH[=c4S(,Cc1csc([C@H](C)NC(=H[=c4S(,26,replace I at position 18 with =,flow_matching,0.3,2.0,40,173
153,replace,19.0,O,H,Cc1csc([C@H](C)NC(=H[=c4S(,Cc1csc([C@H](C)NC(=O[=c4S(,26,replace H at position 19 with O,flow_matching,0.3,2.0,40,173
154,replace,20.0,),[,Cc1csc([C@H](C)NC(=O[=c4S(,Cc1csc([C@H](C)NC(=O)=c4S(,26,replace [ at position 20 with ),flow_matching,0.3,2.0,40,173
155,replace,21.0,C,=,Cc1csc([C@H](C)NC(=O)=c4S(,Cc1csc([C@H](C)NC(=O)Cc4S(,26,replace = at position 21 with C,flow_matching,0.3,2.0,40,173
156,replace,22.0,C,c,Cc1csc([C@H](C)NC(=O)Cc4S(,Cc1csc([C@H](C)NC(=O)CC4S(,26,replace c at position 22 with C,flow_matching,0.3,2.0,40,173
157,replace,23.0,C,4,Cc1csc([C@H](C)NC(=O)CC4S(,Cc1csc([C@H](C)NC(=O)CCCS(,26,replace 4 at position 23 with C,flow_matching,0.3,2.0,40,173
158,replace,24.0,[,S,Cc1csc([C@H](C)NC(=O)CCCS(,Cc1csc([C@H](C)NC(=O)CCC[(,26,replace S at position 24 with [,flow_matching,0.3,2.0,40,173
159,replace,25.0,N,(,Cc1csc([C@H](C)NC(=O)CCC[(,Cc1csc([C@H](C)NC(=O)CCC[N,26,replace ( at position 25 with N,flow_matching,0.3,2.0,40,173
160,add,26.0,H,,Cc1csc([C@H](C)NC(=O)CCC[N,Cc1csc([C@H](C)NC(=O)CCC[NH,27,add H at position 26,flow_matching,0.3,2.0,40,173
161,add,27.0,+,,Cc1csc([C@H](C)NC(=O)CCC[NH,Cc1csc([C@H](C)NC(=O)CCC[NH+,28,add + at position 27,flow_matching,0.3,2.0,40,173
162,add,28.0,],,Cc1csc([C@H](C)NC(=O)CCC[NH+,Cc1csc([C@H](C)NC(=O)CCC[NH+],29,add ] at position 28,flow_matching,0.3,2.0,40,173
163,add,29.0,2,,Cc1csc([C@H](C)NC(=O)CCC[NH+],Cc1csc([C@H](C)NC(=O)CCC[NH+]2,30,add 2 at position 29,flow_matching,0.3,2.0,40,173
164,add,30.0,C,,Cc1csc([C@H](C)NC(=O)CCC[NH+]2,Cc1csc([C@H](C)NC(=O)CCC[NH+]2C,31,add C at position 30,flow_matching,0.3,2.0,40,173
165,add,31.0,C,,Cc1csc([C@H](C)NC(=O)CCC[NH+]2C,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CC,32,add C at position 31,flow_matching,0.3,2.0,40,173
166,add,32.0,C,,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CC,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCC,33,add C at position 32,flow_matching,0.3,2.0,40,173
167,add,33.0,C,,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCC,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCC,34,add C at position 33,flow_matching,0.3,2.0,40,173
168,add,34.0,C,,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCC,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC,35,add C at position 34,flow_matching,0.3,2.0,40,173
169,add,35.0,2,,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC2,36,add 2 at position 35,flow_matching,0.3,2.0,40,173
170,add,36.0,),,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC2,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC2),37,add ) at position 36,flow_matching,0.3,2.0,40,173
171,add,37.0,n,,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC2),Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC2)n,38,add n at position 37,flow_matching,0.3,2.0,40,173
172,add,38.0,1,,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC2)n,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC2)n1,39,add 1 at position 38,flow_matching,0.3,2.0,40,173
173,add,39.0,"
",,Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC2)n1,"Cc1csc([C@H](C)NC(=O)CCC[NH+]2CCCCC2)n1
",40,"add 
 at position 39",flow_matching,0.3,2.0,40,173
