step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,47,228
1,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,47,228
2,replace,0.0,C,/,/,C,1,replace / at position 0 with C,flow_matching,0.3,2.0,47,228
3,replace,0.0,N,C,C,N,1,replace C at position 0 with N,flow_matching,0.3,2.0,47,228
4,replace,0.0,C,N,N,C,1,replace N at position 0 with C,flow_matching,0.3,2.0,47,228
5,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,47,228
6,add,0.0,],,CC,]CC,3,add ] at position 0,flow_matching,0.3,2.0,47,228
7,replace,0.0,C,],]CC,CCC,3,replace ] at position 0 with C,flow_matching,0.3,2.0,47,228
8,replace,2.0,c,C,CC<PERSON>,CCc,3,replace C at position 2 with c,flow_matching,0.3,2.0,47,228
9,remove,2.0,c,,CCc,CC,2,remove c from position 2,flow_matching,0.3,2.0,47,228
10,add,2.0,c,,CC,CCc,3,add c at position 2,flow_matching,0.3,2.0,47,228
11,add,3.0,1,,CCc,CCc1,4,add 1 at position 3,flow_matching,0.3,2.0,47,228
12,add,4.0,),,CCc1,CCc1),5,add ) at position 4,flow_matching,0.3,2.0,47,228
13,replace,4.0,c,),CCc1),CCc1c,5,replace ) at position 4 with c,flow_matching,0.3,2.0,47,228
14,add,5.0,c,,CCc1c,CCc1cc,6,add c at position 5,flow_matching,0.3,2.0,47,228
15,add,4.0,n,,CCc1cc,CCc1ncc,7,add n at position 4,flow_matching,0.3,2.0,47,228
16,replace,0.0,H,C,CCc1ncc,HCc1ncc,7,replace C at position 0 with H,flow_matching,0.3,2.0,47,228
17,add,4.0,H,,HCc1ncc,HCc1Hncc,8,add H at position 4,flow_matching,0.3,2.0,47,228
18,remove,1.0,C,,HCc1Hncc,Hc1Hncc,7,remove C from position 1,flow_matching,0.3,2.0,47,228
19,remove,3.0,H,,Hc1Hncc,Hc1ncc,6,remove H from position 3,flow_matching,0.3,2.0,47,228
20,add,2.0,3,,Hc1ncc,Hc31ncc,7,add 3 at position 2,flow_matching,0.3,2.0,47,228
21,remove,2.0,3,,Hc31ncc,Hc1ncc,6,remove 3 from position 2,flow_matching,0.3,2.0,47,228
22,add,2.0,6,,Hc1ncc,Hc61ncc,7,add 6 at position 2,flow_matching,0.3,2.0,47,228
23,remove,2.0,6,,Hc61ncc,Hc1ncc,6,remove 6 from position 2,flow_matching,0.3,2.0,47,228
24,add,6.0,c,,Hc1ncc,Hc1nccc,7,add c at position 6,flow_matching,0.3,2.0,47,228
25,replace,2.0,B,1,Hc1nccc,HcBnccc,7,replace 1 at position 2 with B,flow_matching,0.3,2.0,47,228
26,add,2.0,s,,HcBnccc,HcsBnccc,8,add s at position 2,flow_matching,0.3,2.0,47,228
27,remove,3.0,B,,HcsBnccc,Hcsnccc,7,remove B from position 3,flow_matching,0.3,2.0,47,228
28,replace,2.0,C,s,Hcsnccc,HcCnccc,7,replace s at position 2 with C,flow_matching,0.3,2.0,47,228
29,replace,0.0,C,H,HcCnccc,CcCnccc,7,replace H at position 0 with C,flow_matching,0.3,2.0,47,228
30,remove,6.0,c,,CcCnccc,CcCncc,6,remove c from position 6,flow_matching,0.3,2.0,47,228
31,add,4.0,5,,CcCncc,CcCn5cc,7,add 5 at position 4,flow_matching,0.3,2.0,47,228
32,remove,5.0,c,,CcCn5cc,CcCn5c,6,remove c from position 5,flow_matching,0.3,2.0,47,228
33,replace,1.0,C,c,CcCn5c,CCCn5c,6,replace c at position 1 with C,flow_matching,0.3,2.0,47,228
34,add,6.0,=,,CCCn5c,CCCn5c=,7,add = at position 6,flow_matching,0.3,2.0,47,228
35,remove,5.0,c,,CCCn5c=,CCCn5=,6,remove c from position 5,flow_matching,0.3,2.0,47,228
36,add,2.0,O,,CCCn5=,CCOCn5=,7,add O at position 2,flow_matching,0.3,2.0,47,228
37,remove,5.0,5,,CCOCn5=,CCOCn=,6,remove 5 from position 5,flow_matching,0.3,2.0,47,228
38,replace,2.0,c,O,CCOCn=,CCcCn=,6,replace O at position 2 with c,flow_matching,0.3,2.0,47,228
39,add,1.0,\,,CCcCn=,C\CcCn=,7,add \ at position 1,flow_matching,0.3,2.0,47,228
40,remove,0.0,C,,C\CcCn=,\CcCn=,6,remove C from position 0,flow_matching,0.3,2.0,47,228
41,replace,3.0,3,C,\CcCn=,\Cc3n=,6,replace C at position 3 with 3,flow_matching,0.3,2.0,47,228
42,remove,3.0,3,,\Cc3n=,\Ccn=,5,remove 3 from position 3,flow_matching,0.3,2.0,47,228
43,replace,4.0,),=,\Ccn=,\Ccn),5,replace = at position 4 with ),flow_matching,0.3,2.0,47,228
44,add,4.0,6,,\Ccn),\Ccn6),6,add 6 at position 4,flow_matching,0.3,2.0,47,228
45,remove,1.0,C,,\Ccn6),\cn6),5,remove C from position 1,flow_matching,0.3,2.0,47,228
46,replace,0.0,C,\,\cn6),Ccn6),5,replace \ at position 0 with C,flow_matching,0.3,2.0,47,228
47,remove,1.0,c,,Ccn6),Cn6),4,remove c from position 1,flow_matching,0.3,2.0,47,228
48,replace,1.0,C,n,Cn6),CC6),4,replace n at position 1 with C,flow_matching,0.3,2.0,47,228
49,add,0.0,@,,CC6),@CC6),5,add @ at position 0,flow_matching,0.3,2.0,47,228
50,add,5.0,F,,@CC6),@CC6)F,6,add F at position 5,flow_matching,0.3,2.0,47,228
51,replace,0.0,C,@,@CC6)F,CCC6)F,6,replace @ at position 0 with C,flow_matching,0.3,2.0,47,228
52,replace,1.0,F,C,CCC6)F,CFC6)F,6,replace C at position 1 with F,flow_matching,0.3,2.0,47,228
53,replace,5.0,o,F,CFC6)F,CFC6)o,6,replace F at position 5 with o,flow_matching,0.3,2.0,47,228
54,replace,3.0,F,6,CFC6)o,CFCF)o,6,replace 6 at position 3 with F,flow_matching,0.3,2.0,47,228
55,replace,1.0,C,F,CFCF)o,CCCF)o,6,replace F at position 1 with C,flow_matching,0.3,2.0,47,228
56,replace,2.0,I,C,CCCF)o,CCIF)o,6,replace C at position 2 with I,flow_matching,0.3,2.0,47,228
57,replace,2.0,S,I,CCIF)o,CCSF)o,6,replace I at position 2 with S,flow_matching,0.3,2.0,47,228
58,remove,5.0,o,,CCSF)o,CCSF),5,remove o from position 5,flow_matching,0.3,2.0,47,228
59,replace,2.0,l,S,CCSF),CClF),5,replace S at position 2 with l,flow_matching,0.3,2.0,47,228
60,remove,4.0,),,CClF),CClF,4,remove ) from position 4,flow_matching,0.3,2.0,47,228
61,add,2.0,O,,CClF,CCOlF,5,add O at position 2,flow_matching,0.3,2.0,47,228
62,replace,2.0,c,O,CCOlF,CCclF,5,replace O at position 2 with c,flow_matching,0.3,2.0,47,228
63,replace,3.0,N,l,CCclF,CCcNF,5,replace l at position 3 with N,flow_matching,0.3,2.0,47,228
64,remove,4.0,F,,CCcNF,CCcN,4,remove F from position 4,flow_matching,0.3,2.0,47,228
65,replace,3.0,1,N,CCcN,CCc1,4,replace N at position 3 with 1,flow_matching,0.3,2.0,47,228
66,replace,1.0,@,C,CCc1,C@c1,4,replace C at position 1 with @,flow_matching,0.3,2.0,47,228
67,replace,1.0,C,@,C@c1,CCc1,4,replace @ at position 1 with C,flow_matching,0.3,2.0,47,228
68,add,3.0,S,,CCc1,CCcS1,5,add S at position 3,flow_matching,0.3,2.0,47,228
69,replace,3.0,1,S,CCcS1,CCc11,5,replace S at position 3 with 1,flow_matching,0.3,2.0,47,228
70,replace,4.0,c,1,CCc11,CCc1c,5,replace 1 at position 4 with c,flow_matching,0.3,2.0,47,228
71,add,5.0,c,,CCc1c,CCc1cc,6,add c at position 5,flow_matching,0.3,2.0,47,228
72,add,6.0,c,,CCc1cc,CCc1ccc,7,add c at position 6,flow_matching,0.3,2.0,47,228
73,add,7.0,(,,CCc1ccc,CCc1ccc(,8,add ( at position 7,flow_matching,0.3,2.0,47,228
74,add,8.0,C,,CCc1ccc(,CCc1ccc(C,9,add C at position 8,flow_matching,0.3,2.0,47,228
75,remove,4.0,c,,CCc1ccc(C,CCc1cc(C,8,remove c from position 4,flow_matching,0.3,2.0,47,228
76,add,5.0,3,,CCc1cc(C,CCc1c3c(C,9,add 3 at position 5,flow_matching,0.3,2.0,47,228
77,replace,5.0,c,3,CCc1c3c(C,CCc1ccc(C,9,replace 3 at position 5 with c,flow_matching,0.3,2.0,47,228
78,remove,1.0,C,,CCc1ccc(C,Cc1ccc(C,8,remove C from position 1,flow_matching,0.3,2.0,47,228
79,remove,0.0,C,,Cc1ccc(C,c1ccc(C,7,remove C from position 0,flow_matching,0.3,2.0,47,228
80,remove,5.0,(,,c1ccc(C,c1cccC,6,remove ( from position 5,flow_matching,0.3,2.0,47,228
81,replace,0.0,C,c,c1cccC,C1cccC,6,replace c at position 0 with C,flow_matching,0.3,2.0,47,228
82,replace,1.0,C,1,C1cccC,CCcccC,6,replace 1 at position 1 with C,flow_matching,0.3,2.0,47,228
83,remove,0.0,C,,CCcccC,CcccC,5,remove C from position 0,flow_matching,0.3,2.0,47,228
84,remove,1.0,c,,CcccC,CccC,4,remove c from position 1,flow_matching,0.3,2.0,47,228
85,replace,3.0,o,C,CccC,Ccco,4,replace C at position 3 with o,flow_matching,0.3,2.0,47,228
86,replace,1.0,/,c,Ccco,C/co,4,replace c at position 1 with /,flow_matching,0.3,2.0,47,228
87,remove,0.0,C,,C/co,/co,3,remove C from position 0,flow_matching,0.3,2.0,47,228
88,add,1.0,1,,/co,/1co,4,add 1 at position 1,flow_matching,0.3,2.0,47,228
89,replace,0.0,C,/,/1co,C1co,4,replace / at position 0 with C,flow_matching,0.3,2.0,47,228
90,replace,1.0,C,1,C1co,CCco,4,replace 1 at position 1 with C,flow_matching,0.3,2.0,47,228
91,replace,0.0,\,C,CCco,\Cco,4,replace C at position 0 with \,flow_matching,0.3,2.0,47,228
92,add,2.0,4,,\Cco,\C4co,5,add 4 at position 2,flow_matching,0.3,2.0,47,228
93,replace,0.0,C,\,\C4co,CC4co,5,replace \ at position 0 with C,flow_matching,0.3,2.0,47,228
94,replace,2.0,c,4,CC4co,CCcco,5,replace 4 at position 2 with c,flow_matching,0.3,2.0,47,228
95,replace,3.0,1,c,CCcco,CCc1o,5,replace c at position 3 with 1,flow_matching,0.3,2.0,47,228
96,replace,2.0,H,c,CCc1o,CCH1o,5,replace c at position 2 with H,flow_matching,0.3,2.0,47,228
97,remove,4.0,o,,CCH1o,CCH1,4,remove o from position 4,flow_matching,0.3,2.0,47,228
98,add,1.0,r,,CCH1,CrCH1,5,add r at position 1,flow_matching,0.3,2.0,47,228
99,replace,1.0,C,r,CrCH1,CCCH1,5,replace r at position 1 with C,flow_matching,0.3,2.0,47,228
100,replace,2.0,c,C,CCCH1,CCcH1,5,replace C at position 2 with c,flow_matching,0.3,2.0,47,228
101,remove,0.0,C,,CCcH1,CcH1,4,remove C from position 0,flow_matching,0.3,2.0,47,228
102,add,4.0,7,,CcH1,CcH17,5,add 7 at position 4,flow_matching,0.3,2.0,47,228
103,replace,1.0,C,c,CcH17,CCH17,5,replace c at position 1 with C,flow_matching,0.3,2.0,47,228
104,add,3.0,r,,CCH17,CCHr17,6,add r at position 3,flow_matching,0.3,2.0,47,228
105,replace,2.0,+,H,CCHr17,CC+r17,6,replace H at position 2 with +,flow_matching,0.3,2.0,47,228
106,replace,2.0,c,+,CC+r17,CCcr17,6,replace + at position 2 with c,flow_matching,0.3,2.0,47,228
107,add,6.0,4,,CCcr17,CCcr174,7,add 4 at position 6,flow_matching,0.3,2.0,47,228
108,replace,0.0,7,C,CCcr174,7Ccr174,7,replace C at position 0 with 7,flow_matching,0.3,2.0,47,228
109,remove,2.0,c,,7Ccr174,7Cr174,6,remove c from position 2,flow_matching,0.3,2.0,47,228
110,add,3.0,2,,7Cr174,7Cr2174,7,add 2 at position 3,flow_matching,0.3,2.0,47,228
111,replace,0.0,C,7,7Cr2174,CCr2174,7,replace 7 at position 0 with C,flow_matching,0.3,2.0,47,228
112,replace,1.0,(,C,CCr2174,C(r2174,7,replace C at position 1 with (,flow_matching,0.3,2.0,47,228
113,replace,1.0,C,(,C(r2174,CCr2174,7,replace ( at position 1 with C,flow_matching,0.3,2.0,47,228
114,replace,2.0,c,r,CCr2174,CCc2174,7,replace r at position 2 with c,flow_matching,0.3,2.0,47,228
115,replace,3.0,1,2,CCc2174,CCc1174,7,replace 2 at position 3 with 1,flow_matching,0.3,2.0,47,228
116,replace,4.0,c,1,CCc1174,CCc1c74,7,replace 1 at position 4 with c,flow_matching,0.3,2.0,47,228
117,add,6.0,c,,CCc1c74,CCc1c7c4,8,add c at position 6,flow_matching,0.3,2.0,47,228
118,replace,2.0,2,c,CCc1c7c4,CC21c7c4,8,replace c at position 2 with 2,flow_matching,0.3,2.0,47,228
119,replace,2.0,c,2,CC21c7c4,CCc1c7c4,8,replace 2 at position 2 with c,flow_matching,0.3,2.0,47,228
120,replace,5.0,c,7,CCc1c7c4,CCc1ccc4,8,replace 7 at position 5 with c,flow_matching,0.3,2.0,47,228
121,add,8.0,(,,CCc1ccc4,CCc1ccc4(,9,add ( at position 8,flow_matching,0.3,2.0,47,228
122,add,7.0,N,,CCc1ccc4(,CCc1cccN4(,10,add N at position 7,flow_matching,0.3,2.0,47,228
123,add,7.0,3,,CCc1cccN4(,CCc1ccc3N4(,11,add 3 at position 7,flow_matching,0.3,2.0,47,228
124,remove,6.0,c,,CCc1ccc3N4(,CCc1cc3N4(,10,remove c from position 6,flow_matching,0.3,2.0,47,228
125,remove,0.0,C,,CCc1cc3N4(,Cc1cc3N4(,9,remove C from position 0,flow_matching,0.3,2.0,47,228
126,replace,1.0,C,c,Cc1cc3N4(,CC1cc3N4(,9,replace c at position 1 with C,flow_matching,0.3,2.0,47,228
127,add,0.0,S,,CC1cc3N4(,SCC1cc3N4(,10,add S at position 0,flow_matching,0.3,2.0,47,228
128,replace,6.0,H,3,SCC1cc3N4(,SCC1ccHN4(,10,replace 3 at position 6 with H,flow_matching,0.3,2.0,47,228
129,remove,0.0,S,,SCC1ccHN4(,CC1ccHN4(,9,remove S from position 0,flow_matching,0.3,2.0,47,228
130,replace,1.0,/,C,CC1ccHN4(,C/1ccHN4(,9,replace C at position 1 with /,flow_matching,0.3,2.0,47,228
131,replace,1.0,C,/,C/1ccHN4(,CC1ccHN4(,9,replace / at position 1 with C,flow_matching,0.3,2.0,47,228
132,replace,7.0,n,4,CC1ccHN4(,CC1ccHNn(,9,replace 4 at position 7 with n,flow_matching,0.3,2.0,47,228
133,add,2.0,B,,CC1ccHNn(,CCB1ccHNn(,10,add B at position 2,flow_matching,0.3,2.0,47,228
134,replace,9.0,c,(,CCB1ccHNn(,CCB1ccHNnc,10,replace ( at position 9 with c,flow_matching,0.3,2.0,47,228
135,replace,2.0,c,B,CCB1ccHNnc,CCc1ccHNnc,10,replace B at position 2 with c,flow_matching,0.3,2.0,47,228
136,add,10.0,[,,CCc1ccHNnc,CCc1ccHNnc[,11,add [ at position 10,flow_matching,0.3,2.0,47,228
137,replace,6.0,c,H,CCc1ccHNnc[,CCc1cccNnc[,11,replace H at position 6 with c,flow_matching,0.3,2.0,47,228
138,replace,7.0,(,N,CCc1cccNnc[,CCc1ccc(nc[,11,replace N at position 7 with (,flow_matching,0.3,2.0,47,228
139,add,1.0,c,,CCc1ccc(nc[,CcCc1ccc(nc[,12,add c at position 1,flow_matching,0.3,2.0,47,228
140,replace,7.0,H,c,CcCc1ccc(nc[,CcCc1ccH(nc[,12,replace c at position 7 with H,flow_matching,0.3,2.0,47,228
141,replace,1.0,C,c,CcCc1ccH(nc[,CCCc1ccH(nc[,12,replace c at position 1 with C,flow_matching,0.3,2.0,47,228
142,replace,2.0,c,C,CCCc1ccH(nc[,CCcc1ccH(nc[,12,replace C at position 2 with c,flow_matching,0.3,2.0,47,228
143,replace,3.0,1,c,CCcc1ccH(nc[,CCc11ccH(nc[,12,replace c at position 3 with 1,flow_matching,0.3,2.0,47,228
144,add,4.0,6,,CCc11ccH(nc[,CCc161ccH(nc[,13,add 6 at position 4,flow_matching,0.3,2.0,47,228
145,add,11.0,O,,CCc161ccH(nc[,CCc161ccH(nOc[,14,add O at position 11,flow_matching,0.3,2.0,47,228
146,remove,11.0,O,,CCc161ccH(nOc[,CCc161ccH(nc[,13,remove O from position 11,flow_matching,0.3,2.0,47,228
147,replace,4.0,c,6,CCc161ccH(nc[,CCc1c1ccH(nc[,13,replace 6 at position 4 with c,flow_matching,0.3,2.0,47,228
148,replace,5.0,c,1,CCc1c1ccH(nc[,CCc1ccccH(nc[,13,replace 1 at position 5 with c,flow_matching,0.3,2.0,47,228
149,add,13.0,-,,CCc1ccccH(nc[,CCc1ccccH(nc[-,14,add - at position 13,flow_matching,0.3,2.0,47,228
150,add,8.0,),,CCc1ccccH(nc[-,CCc1cccc)H(nc[-,15,add ) at position 8,flow_matching,0.3,2.0,47,228
151,add,7.0,7,,CCc1cccc)H(nc[-,CCc1ccc7c)H(nc[-,16,add 7 at position 7,flow_matching,0.3,2.0,47,228
152,replace,7.0,(,7,CCc1ccc7c)H(nc[-,CCc1ccc(c)H(nc[-,16,replace 7 at position 7 with (,flow_matching,0.3,2.0,47,228
153,replace,7.0,@,(,CCc1ccc(c)H(nc[-,CCc1ccc@c)H(nc[-,16,replace ( at position 7 with @,flow_matching,0.3,2.0,47,228
154,replace,7.0,(,@,CCc1ccc@c)H(nc[-,CCc1ccc(c)H(nc[-,16,replace @ at position 7 with (,flow_matching,0.3,2.0,47,228
155,replace,8.0,C,c,CCc1ccc(c)H(nc[-,CCc1ccc(C)H(nc[-,16,replace c at position 8 with C,flow_matching,0.3,2.0,47,228
156,replace,11.0,[,(,CCc1ccc(C)H(nc[-,CCc1ccc(C)H[nc[-,16,replace ( at position 11 with [,flow_matching,0.3,2.0,47,228
157,remove,1.0,C,,CCc1ccc(C)H[nc[-,Cc1ccc(C)H[nc[-,15,remove C from position 1,flow_matching,0.3,2.0,47,228
158,replace,1.0,C,c,Cc1ccc(C)H[nc[-,CC1ccc(C)H[nc[-,15,replace c at position 1 with C,flow_matching,0.3,2.0,47,228
159,replace,1.0,7,C,CC1ccc(C)H[nc[-,C71ccc(C)H[nc[-,15,replace C at position 1 with 7,flow_matching,0.3,2.0,47,228
160,add,14.0,n,,C71ccc(C)H[nc[-,C71ccc(C)H[nc[n-,16,add n at position 14,flow_matching,0.3,2.0,47,228
161,replace,1.0,C,7,C71ccc(C)H[nc[n-,CC1ccc(C)H[nc[n-,16,replace 7 at position 1 with C,flow_matching,0.3,2.0,47,228
162,add,14.0,#,,CC1ccc(C)H[nc[n-,CC1ccc(C)H[nc[#n-,17,add # at position 14,flow_matching,0.3,2.0,47,228
163,replace,14.0,),#,CC1ccc(C)H[nc[#n-,CC1ccc(C)H[nc[)n-,17,replace # at position 14 with ),flow_matching,0.3,2.0,47,228
164,replace,2.0,c,1,CC1ccc(C)H[nc[)n-,CCcccc(C)H[nc[)n-,17,replace 1 at position 2 with c,flow_matching,0.3,2.0,47,228
165,replace,3.0,1,c,CCcccc(C)H[nc[)n-,CCc1cc(C)H[nc[)n-,17,replace c at position 3 with 1,flow_matching,0.3,2.0,47,228
166,replace,5.0,F,c,CCc1cc(C)H[nc[)n-,CCc1cF(C)H[nc[)n-,17,replace c at position 5 with F,flow_matching,0.3,2.0,47,228
167,replace,7.0,n,C,CCc1cF(C)H[nc[)n-,CCc1cF(n)H[nc[)n-,17,replace C at position 7 with n,flow_matching,0.3,2.0,47,228
168,replace,5.0,c,F,CCc1cF(n)H[nc[)n-,CCc1cc(n)H[nc[)n-,17,replace F at position 5 with c,flow_matching,0.3,2.0,47,228
169,replace,15.0,),n,CCc1cc(n)H[nc[)n-,CCc1cc(n)H[nc[))-,17,replace n at position 15 with ),flow_matching,0.3,2.0,47,228
170,replace,11.0,7,n,CCc1cc(n)H[nc[))-,CCc1cc(n)H[7c[))-,17,replace n at position 11 with 7,flow_matching,0.3,2.0,47,228
171,remove,14.0,),,CCc1cc(n)H[7c[))-,CCc1cc(n)H[7c[)-,16,remove ) from position 14,flow_matching,0.3,2.0,47,228
172,add,3.0,s,,CCc1cc(n)H[7c[)-,CCcs1cc(n)H[7c[)-,17,add s at position 3,flow_matching,0.3,2.0,47,228
173,add,7.0,(,,CCcs1cc(n)H[7c[)-,CCcs1cc((n)H[7c[)-,18,add ( at position 7,flow_matching,0.3,2.0,47,228
174,add,1.0,S,,CCcs1cc((n)H[7c[)-,CSCcs1cc((n)H[7c[)-,19,add S at position 1,flow_matching,0.3,2.0,47,228
175,replace,18.0,),-,CSCcs1cc((n)H[7c[)-,CSCcs1cc((n)H[7c[)),19,replace - at position 18 with ),flow_matching,0.3,2.0,47,228
176,remove,17.0,),,CSCcs1cc((n)H[7c[)),CSCcs1cc((n)H[7c[),18,remove ) from position 17,flow_matching,0.3,2.0,47,228
177,replace,1.0,C,S,CSCcs1cc((n)H[7c[),CCCcs1cc((n)H[7c[),18,replace S at position 1 with C,flow_matching,0.3,2.0,47,228
178,add,18.0,=,,CCCcs1cc((n)H[7c[),CCCcs1cc((n)H[7c[)=,19,add = at position 18,flow_matching,0.3,2.0,47,228
179,add,15.0,(,,CCCcs1cc((n)H[7c[)=,CCCcs1cc((n)H[7(c[)=,20,add ( at position 15,flow_matching,0.3,2.0,47,228
180,replace,8.0,\,(,CCCcs1cc((n)H[7(c[)=,CCCcs1cc\(n)H[7(c[)=,20,replace ( at position 8 with \,flow_matching,0.3,2.0,47,228
181,replace,2.0,c,C,CCCcs1cc\(n)H[7(c[)=,CCccs1cc\(n)H[7(c[)=,20,replace C at position 2 with c,flow_matching,0.3,2.0,47,228
182,replace,9.0,r,(,CCccs1cc\(n)H[7(c[)=,CCccs1cc\rn)H[7(c[)=,20,replace ( at position 9 with r,flow_matching,0.3,2.0,47,228
183,add,4.0,O,,CCccs1cc\rn)H[7(c[)=,CCccOs1cc\rn)H[7(c[)=,21,add O at position 4,flow_matching,0.3,2.0,47,228
184,replace,11.0,@,n,CCccOs1cc\rn)H[7(c[)=,CCccOs1cc\r@)H[7(c[)=,21,replace n at position 11 with @,flow_matching,0.3,2.0,47,228
185,replace,3.0,1,c,CCccOs1cc\r@)H[7(c[)=,CCc1Os1cc\r@)H[7(c[)=,21,replace c at position 3 with 1,flow_matching,0.3,2.0,47,228
186,replace,12.0,n,),CCc1Os1cc\r@)H[7(c[)=,CCc1Os1cc\r@nH[7(c[)=,21,replace ) at position 12 with n,flow_matching,0.3,2.0,47,228
187,replace,4.0,c,O,CCc1Os1cc\r@nH[7(c[)=,CCc1cs1cc\r@nH[7(c[)=,21,replace O at position 4 with c,flow_matching,0.3,2.0,47,228
188,replace,5.0,c,s,CCc1cs1cc\r@nH[7(c[)=,CCc1cc1cc\r@nH[7(c[)=,21,replace s at position 5 with c,flow_matching,0.3,2.0,47,228
189,replace,6.0,c,1,CCc1cc1cc\r@nH[7(c[)=,CCc1ccccc\r@nH[7(c[)=,21,replace 1 at position 6 with c,flow_matching,0.3,2.0,47,228
190,replace,7.0,(,c,CCc1ccccc\r@nH[7(c[)=,CCc1ccc(c\r@nH[7(c[)=,21,replace c at position 7 with (,flow_matching,0.3,2.0,47,228
191,replace,8.0,C,c,CCc1ccc(c\r@nH[7(c[)=,CCc1ccc(C\r@nH[7(c[)=,21,replace c at position 8 with C,flow_matching,0.3,2.0,47,228
192,replace,9.0,N,\,CCc1ccc(C\r@nH[7(c[)=,CCc1ccc(CNr@nH[7(c[)=,21,replace \ at position 9 with N,flow_matching,0.3,2.0,47,228
193,replace,10.0,C,r,CCc1ccc(CNr@nH[7(c[)=,CCc1ccc(CNC@nH[7(c[)=,21,replace r at position 10 with C,flow_matching,0.3,2.0,47,228
194,replace,11.0,(,@,CCc1ccc(CNC@nH[7(c[)=,CCc1ccc(CNC(nH[7(c[)=,21,replace @ at position 11 with (,flow_matching,0.3,2.0,47,228
195,replace,12.0,=,n,CCc1ccc(CNC(nH[7(c[)=,CCc1ccc(CNC(=H[7(c[)=,21,replace n at position 12 with =,flow_matching,0.3,2.0,47,228
196,replace,13.0,O,H,CCc1ccc(CNC(=H[7(c[)=,CCc1ccc(CNC(=O[7(c[)=,21,replace H at position 13 with O,flow_matching,0.3,2.0,47,228
197,replace,14.0,),[,CCc1ccc(CNC(=O[7(c[)=,CCc1ccc(CNC(=O)7(c[)=,21,replace [ at position 14 with ),flow_matching,0.3,2.0,47,228
198,replace,15.0,c,7,CCc1ccc(CNC(=O)7(c[)=,CCc1ccc(CNC(=O)c(c[)=,21,replace 7 at position 15 with c,flow_matching,0.3,2.0,47,228
199,replace,16.0,2,(,CCc1ccc(CNC(=O)c(c[)=,CCc1ccc(CNC(=O)c2c[)=,21,replace ( at position 16 with 2,flow_matching,0.3,2.0,47,228
200,replace,18.0,c,[,CCc1ccc(CNC(=O)c2c[)=,CCc1ccc(CNC(=O)c2cc)=,21,replace [ at position 18 with c,flow_matching,0.3,2.0,47,228
201,replace,19.0,c,),CCc1ccc(CNC(=O)c2cc)=,CCc1ccc(CNC(=O)c2ccc=,21,replace ) at position 19 with c,flow_matching,0.3,2.0,47,228
202,replace,20.0,(,=,CCc1ccc(CNC(=O)c2ccc=,CCc1ccc(CNC(=O)c2ccc(,21,replace = at position 20 with (,flow_matching,0.3,2.0,47,228
203,add,21.0,-,,CCc1ccc(CNC(=O)c2ccc(,CCc1ccc(CNC(=O)c2ccc(-,22,add - at position 21,flow_matching,0.3,2.0,47,228
204,add,22.0,c,,CCc1ccc(CNC(=O)c2ccc(-,CCc1ccc(CNC(=O)c2ccc(-c,23,add c at position 22,flow_matching,0.3,2.0,47,228
205,add,23.0,3,,CCc1ccc(CNC(=O)c2ccc(-c,CCc1ccc(CNC(=O)c2ccc(-c3,24,add 3 at position 23,flow_matching,0.3,2.0,47,228
206,add,24.0,n,,CCc1ccc(CNC(=O)c2ccc(-c3,CCc1ccc(CNC(=O)c2ccc(-c3n,25,add n at position 24,flow_matching,0.3,2.0,47,228
207,add,25.0,c,,CCc1ccc(CNC(=O)c2ccc(-c3n,CCc1ccc(CNC(=O)c2ccc(-c3nc,26,add c at position 25,flow_matching,0.3,2.0,47,228
208,add,26.0,c,,CCc1ccc(CNC(=O)c2ccc(-c3nc,CCc1ccc(CNC(=O)c2ccc(-c3ncc,27,add c at position 26,flow_matching,0.3,2.0,47,228
209,add,27.0,n,,CCc1ccc(CNC(=O)c2ccc(-c3ncc,CCc1ccc(CNC(=O)c2ccc(-c3nccn,28,add n at position 27,flow_matching,0.3,2.0,47,228
210,add,28.0,c,,CCc1ccc(CNC(=O)c2ccc(-c3nccn,CCc1ccc(CNC(=O)c2ccc(-c3nccnc,29,add c at position 28,flow_matching,0.3,2.0,47,228
211,add,29.0,3,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3,30,add 3 at position 29,flow_matching,0.3,2.0,47,228
212,add,30.0,N,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N,31,add N at position 30,flow_matching,0.3,2.0,47,228
213,add,31.0,3,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3,32,add 3 at position 31,flow_matching,0.3,2.0,47,228
214,add,32.0,C,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3C,33,add C at position 32,flow_matching,0.3,2.0,47,228
215,add,33.0,C,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3C,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CC,34,add C at position 33,flow_matching,0.3,2.0,47,228
216,add,34.0,C,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CC,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCC,35,add C at position 34,flow_matching,0.3,2.0,47,228
217,add,35.0,C,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCC,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCC,36,add C at position 35,flow_matching,0.3,2.0,47,228
218,add,36.0,C,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCC,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC,37,add C at position 36,flow_matching,0.3,2.0,47,228
219,add,37.0,3,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3,38,add 3 at position 37,flow_matching,0.3,2.0,47,228
220,add,38.0,),,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3),39,add ) at position 38,flow_matching,0.3,2.0,47,228
221,add,39.0,c,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3),CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)c,40,add c at position 39,flow_matching,0.3,2.0,47,228
222,add,40.0,c,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)c,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc,41,add c at position 40,flow_matching,0.3,2.0,47,228
223,add,41.0,2,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2,42,add 2 at position 41,flow_matching,0.3,2.0,47,228
224,add,42.0,),,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2),43,add ) at position 42,flow_matching,0.3,2.0,47,228
225,add,43.0,c,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2),CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)c,44,add c at position 43,flow_matching,0.3,2.0,47,228
226,add,44.0,c,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)c,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)cc,45,add c at position 44,flow_matching,0.3,2.0,47,228
227,add,45.0,1,,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)cc,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)cc1,46,add 1 at position 45,flow_matching,0.3,2.0,47,228
228,add,46.0,"
",,CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)cc1,"CCc1ccc(CNC(=O)c2ccc(-c3nccnc3N3CCCCC3)cc2)cc1
",47,"add 
 at position 46",flow_matching,0.3,2.0,47,228
