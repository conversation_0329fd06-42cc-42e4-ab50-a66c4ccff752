step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,58,159
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,58,159
2,replace,0.0,s,C,C,s,1,replace <PERSON> at position 0 with s,flow_matching,0.3,2.0,58,159
3,replace,0.0,n,s,s,n,1,replace s at position 0 with n,flow_matching,0.3,2.0,58,159
4,replace,0.0,#,n,n,#,1,replace n at position 0 with #,flow_matching,0.3,2.0,58,159
5,remove,0.0,#,,#,,0,remove # from position 0,flow_matching,0.3,2.0,58,159
6,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,58,159
7,replace,0.0,o,I,I,o,1,replace I at position 0 with o,flow_matching,0.3,2.0,58,159
8,remove,0.0,o,,o,,0,remove o from position 0,flow_matching,0.3,2.0,58,159
9,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,58,159
10,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,58,159
11,remove,1.0,c,,Cc,C,1,remove c from position 1,flow_matching,0.3,2.0,58,159
12,add,1.0,\,,C,C\,2,add \ at position 1,flow_matching,0.3,2.0,58,159
13,replace,1.0,C,\,C\,CC,2,replace \ at position 1 with C,flow_matching,0.3,2.0,58,159
14,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,58,159
15,replace,1.0,2,C,CC(,C2(,3,replace C at position 1 with 2,flow_matching,0.3,2.0,58,159
16,add,3.0,\,,C2(,C2(\,4,add \ at position 3,flow_matching,0.3,2.0,58,159
17,replace,1.0,S,2,C2(\,CS(\,4,replace 2 at position 1 with S,flow_matching,0.3,2.0,58,159
18,remove,2.0,(,,CS(\,CS\,3,remove ( from position 2,flow_matching,0.3,2.0,58,159
19,remove,1.0,S,,CS\,C\,2,remove S from position 1,flow_matching,0.3,2.0,58,159
20,replace,1.0,C,\,C\,CC,2,replace \ at position 1 with C,flow_matching,0.3,2.0,58,159
21,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,58,159
22,add,3.0,C,,CC(,CC(C,4,add C at position 3,flow_matching,0.3,2.0,58,159
23,remove,3.0,C,,CC(C,CC(,3,remove C from position 3,flow_matching,0.3,2.0,58,159
24,remove,0.0,C,,CC(,C(,2,remove C from position 0,flow_matching,0.3,2.0,58,159
25,remove,1.0,(,,C(,C,1,remove ( from position 1,flow_matching,0.3,2.0,58,159
26,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,58,159
27,add,0.0,/,,CC,/CC,3,add / at position 0,flow_matching,0.3,2.0,58,159
28,replace,0.0,C,/,/CC,CCC,3,replace / at position 0 with C,flow_matching,0.3,2.0,58,159
29,replace,1.0,5,C,CCC,C5C,3,replace C at position 1 with 5,flow_matching,0.3,2.0,58,159
30,replace,2.0,3,C,C5C,C53,3,replace C at position 2 with 3,flow_matching,0.3,2.0,58,159
31,replace,1.0,1,5,C53,C13,3,replace 5 at position 1 with 1,flow_matching,0.3,2.0,58,159
32,replace,2.0,o,3,C13,C1o,3,replace 3 at position 2 with o,flow_matching,0.3,2.0,58,159
33,replace,1.0,C,1,C1o,CCo,3,replace 1 at position 1 with C,flow_matching,0.3,2.0,58,159
34,replace,2.0,-,o,CCo,CC-,3,replace o at position 2 with -,flow_matching,0.3,2.0,58,159
35,add,3.0,H,,CC-,CC-H,4,add H at position 3,flow_matching,0.3,2.0,58,159
36,remove,1.0,C,,CC-H,C-H,3,remove C from position 1,flow_matching,0.3,2.0,58,159
37,replace,2.0,B,H,C-H,C-B,3,replace H at position 2 with B,flow_matching,0.3,2.0,58,159
38,replace,1.0,C,-,C-B,CCB,3,replace - at position 1 with C,flow_matching,0.3,2.0,58,159
39,replace,2.0,(,B,CCB,CC(,3,replace B at position 2 with (,flow_matching,0.3,2.0,58,159
40,replace,1.0,-,C,CC(,C-(,3,replace C at position 1 with -,flow_matching,0.3,2.0,58,159
41,add,1.0,C,,C-(,CC-(,4,add C at position 1,flow_matching,0.3,2.0,58,159
42,remove,2.0,-,,CC-(,CC(,3,remove - from position 2,flow_matching,0.3,2.0,58,159
43,add,2.0,l,,CC(,CCl(,4,add l at position 2,flow_matching,0.3,2.0,58,159
44,replace,2.0,(,l,CCl(,CC((,4,replace l at position 2 with (,flow_matching,0.3,2.0,58,159
45,replace,3.0,C,(,CC((,CC(C,4,replace ( at position 3 with C,flow_matching,0.3,2.0,58,159
46,add,4.0,),,CC(C,CC(C),5,add ) at position 4,flow_matching,0.3,2.0,58,159
47,add,5.0,c,,CC(C),CC(C)c,6,add c at position 5,flow_matching,0.3,2.0,58,159
48,replace,4.0,F,),CC(C)c,CC(CFc,6,replace ) at position 4 with F,flow_matching,0.3,2.0,58,159
49,add,3.0,c,,CC(CFc,CC(cCFc,7,add c at position 3,flow_matching,0.3,2.0,58,159
50,add,0.0,+,,CC(cCFc,+CC(cCFc,8,add + at position 0,flow_matching,0.3,2.0,58,159
51,add,8.0,@,,+CC(cCFc,+CC(cCFc@,9,add @ at position 8,flow_matching,0.3,2.0,58,159
52,add,0.0,/,,+CC(cCFc@,/+CC(cCFc@,10,add / at position 0,flow_matching,0.3,2.0,58,159
53,add,7.0,(,,/+CC(cCFc@,/+CC(cC(Fc@,11,add ( at position 7,flow_matching,0.3,2.0,58,159
54,add,8.0,F,,/+CC(cC(Fc@,/+CC(cC(FFc@,12,add F at position 8,flow_matching,0.3,2.0,58,159
55,add,11.0,#,,/+CC(cC(FFc@,/+CC(cC(FFc#@,13,add # at position 11,flow_matching,0.3,2.0,58,159
56,replace,0.0,C,/,/+CC(cC(FFc#@,C+CC(cC(FFc#@,13,replace / at position 0 with C,flow_matching,0.3,2.0,58,159
57,add,6.0,H,,C+CC(cC(FFc#@,C+CC(cHC(FFc#@,14,add H at position 6,flow_matching,0.3,2.0,58,159
58,remove,9.0,F,,C+CC(cHC(FFc#@,C+CC(cHC(Fc#@,13,remove F from position 9,flow_matching,0.3,2.0,58,159
59,replace,1.0,C,+,C+CC(cHC(Fc#@,CCCC(cHC(Fc#@,13,replace + at position 1 with C,flow_matching,0.3,2.0,58,159
60,remove,7.0,C,,CCCC(cHC(Fc#@,CCCC(cH(Fc#@,12,remove C from position 7,flow_matching,0.3,2.0,58,159
61,replace,2.0,(,C,CCCC(cH(Fc#@,CC(C(cH(Fc#@,12,replace C at position 2 with (,flow_matching,0.3,2.0,58,159
62,remove,10.0,#,,CC(C(cH(Fc#@,CC(C(cH(Fc@,11,remove # from position 10,flow_matching,0.3,2.0,58,159
63,replace,4.0,),(,CC(C(cH(Fc@,CC(C)cH(Fc@,11,replace ( at position 4 with ),flow_matching,0.3,2.0,58,159
64,replace,6.0,1,H,CC(C)cH(Fc@,CC(C)c1(Fc@,11,replace H at position 6 with 1,flow_matching,0.3,2.0,58,159
65,replace,7.0,c,(,CC(C)c1(Fc@,CC(C)c1cFc@,11,replace ( at position 7 with c,flow_matching,0.3,2.0,58,159
66,add,6.0,=,,CC(C)c1cFc@,CC(C)c=1cFc@,12,add = at position 6,flow_matching,0.3,2.0,58,159
67,add,6.0,C,,CC(C)c=1cFc@,CC(C)cC=1cFc@,13,add C at position 6,flow_matching,0.3,2.0,58,159
68,replace,6.0,1,C,CC(C)cC=1cFc@,CC(C)c1=1cFc@,13,replace C at position 6 with 1,flow_matching,0.3,2.0,58,159
69,replace,7.0,c,=,CC(C)c1=1cFc@,CC(C)c1c1cFc@,13,replace = at position 7 with c,flow_matching,0.3,2.0,58,159
70,replace,11.0,+,c,CC(C)c1c1cFc@,CC(C)c1c1cF+@,13,replace c at position 11 with +,flow_matching,0.3,2.0,58,159
71,replace,8.0,c,1,CC(C)c1c1cF+@,CC(C)c1cccF+@,13,replace 1 at position 8 with c,flow_matching,0.3,2.0,58,159
72,remove,6.0,1,,CC(C)c1cccF+@,CC(C)ccccF+@,12,remove 1 from position 6,flow_matching,0.3,2.0,58,159
73,replace,2.0,B,(,CC(C)ccccF+@,CCBC)ccccF+@,12,replace ( at position 2 with B,flow_matching,0.3,2.0,58,159
74,add,11.0,1,,CCBC)ccccF+@,CCBC)ccccF+1@,13,add 1 at position 11,flow_matching,0.3,2.0,58,159
75,remove,0.0,C,,CCBC)ccccF+1@,CBC)ccccF+1@,12,remove C from position 0,flow_matching,0.3,2.0,58,159
76,remove,7.0,c,,CBC)ccccF+1@,CBC)cccF+1@,11,remove c from position 7,flow_matching,0.3,2.0,58,159
77,remove,1.0,B,,CBC)cccF+1@,CC)cccF+1@,10,remove B from position 1,flow_matching,0.3,2.0,58,159
78,replace,2.0,(,),CC)cccF+1@,CC(cccF+1@,10,replace ) at position 2 with (,flow_matching,0.3,2.0,58,159
79,replace,9.0,H,@,CC(cccF+1@,CC(cccF+1H,10,replace @ at position 9 with H,flow_matching,0.3,2.0,58,159
80,add,1.0,@,,CC(cccF+1H,C@C(cccF+1H,11,add @ at position 1,flow_matching,0.3,2.0,58,159
81,replace,1.0,C,@,C@C(cccF+1H,CCC(cccF+1H,11,replace @ at position 1 with C,flow_matching,0.3,2.0,58,159
82,add,11.0,I,,CCC(cccF+1H,CCC(cccF+1HI,12,add I at position 11,flow_matching,0.3,2.0,58,159
83,add,7.0,-,,CCC(cccF+1HI,CCC(ccc-F+1HI,13,add - at position 7,flow_matching,0.3,2.0,58,159
84,replace,8.0,),F,CCC(ccc-F+1HI,CCC(ccc-)+1HI,13,replace F at position 8 with ),flow_matching,0.3,2.0,58,159
85,remove,12.0,I,,CCC(ccc-)+1HI,CCC(ccc-)+1H,12,remove I from position 12,flow_matching,0.3,2.0,58,159
86,replace,2.0,(,C,CCC(ccc-)+1H,CC((ccc-)+1H,12,replace C at position 2 with (,flow_matching,0.3,2.0,58,159
87,add,8.0,],,CC((ccc-)+1H,CC((ccc-])+1H,13,add ] at position 8,flow_matching,0.3,2.0,58,159
88,remove,4.0,c,,CC((ccc-])+1H,CC((cc-])+1H,12,remove c from position 4,flow_matching,0.3,2.0,58,159
89,add,10.0,5,,CC((cc-])+1H,CC((cc-])+51H,13,add 5 at position 10,flow_matching,0.3,2.0,58,159
90,remove,2.0,(,,CC((cc-])+51H,CC(cc-])+51H,12,remove ( from position 2,flow_matching,0.3,2.0,58,159
91,add,12.0,],,CC(cc-])+51H,CC(cc-])+51H],13,add ] at position 12,flow_matching,0.3,2.0,58,159
92,replace,3.0,C,c,CC(cc-])+51H],CC(Cc-])+51H],13,replace c at position 3 with C,flow_matching,0.3,2.0,58,159
93,replace,12.0,s,],CC(Cc-])+51H],CC(Cc-])+51Hs,13,replace ] at position 12 with s,flow_matching,0.3,2.0,58,159
94,add,3.0,-,,CC(Cc-])+51Hs,CC(-Cc-])+51Hs,14,add - at position 3,flow_matching,0.3,2.0,58,159
95,add,4.0,o,,CC(-Cc-])+51Hs,CC(-oCc-])+51Hs,15,add o at position 4,flow_matching,0.3,2.0,58,159
96,remove,1.0,C,,CC(-oCc-])+51Hs,C(-oCc-])+51Hs,14,remove C from position 1,flow_matching,0.3,2.0,58,159
97,add,4.0,2,,C(-oCc-])+51Hs,C(-o2Cc-])+51Hs,15,add 2 at position 4,flow_matching,0.3,2.0,58,159
98,replace,1.0,C,(,C(-o2Cc-])+51Hs,CC-o2Cc-])+51Hs,15,replace ( at position 1 with C,flow_matching,0.3,2.0,58,159
99,remove,5.0,C,,CC-o2Cc-])+51Hs,CC-o2c-])+51Hs,14,remove C from position 5,flow_matching,0.3,2.0,58,159
100,replace,12.0,[,H,CC-o2c-])+51Hs,CC-o2c-])+51[s,14,replace H at position 12 with [,flow_matching,0.3,2.0,58,159
101,replace,2.0,(,-,CC-o2c-])+51[s,CC(o2c-])+51[s,14,replace - at position 2 with (,flow_matching,0.3,2.0,58,159
102,add,1.0,3,,CC(o2c-])+51[s,C3C(o2c-])+51[s,15,add 3 at position 1,flow_matching,0.3,2.0,58,159
103,replace,1.0,C,3,C3C(o2c-])+51[s,CCC(o2c-])+51[s,15,replace 3 at position 1 with C,flow_matching,0.3,2.0,58,159
104,replace,2.0,(,C,CCC(o2c-])+51[s,CC((o2c-])+51[s,15,replace C at position 2 with (,flow_matching,0.3,2.0,58,159
105,replace,3.0,C,(,CC((o2c-])+51[s,CC(Co2c-])+51[s,15,replace ( at position 3 with C,flow_matching,0.3,2.0,58,159
106,replace,4.0,),o,CC(Co2c-])+51[s,CC(C)2c-])+51[s,15,replace o at position 4 with ),flow_matching,0.3,2.0,58,159
107,replace,5.0,c,2,CC(C)2c-])+51[s,CC(C)cc-])+51[s,15,replace 2 at position 5 with c,flow_matching,0.3,2.0,58,159
108,replace,6.0,1,c,CC(C)cc-])+51[s,CC(C)c1-])+51[s,15,replace c at position 6 with 1,flow_matching,0.3,2.0,58,159
109,replace,7.0,c,-,CC(C)c1-])+51[s,CC(C)c1c])+51[s,15,replace - at position 7 with c,flow_matching,0.3,2.0,58,159
110,replace,8.0,c,],CC(C)c1c])+51[s,CC(C)c1cc)+51[s,15,replace ] at position 8 with c,flow_matching,0.3,2.0,58,159
111,replace,9.0,c,),CC(C)c1cc)+51[s,CC(C)c1ccc+51[s,15,replace ) at position 9 with c,flow_matching,0.3,2.0,58,159
112,replace,10.0,2,+,CC(C)c1ccc+51[s,CC(C)c1ccc251[s,15,replace + at position 10 with 2,flow_matching,0.3,2.0,58,159
113,replace,11.0,c,5,CC(C)c1ccc251[s,CC(C)c1ccc2c1[s,15,replace 5 at position 11 with c,flow_matching,0.3,2.0,58,159
114,replace,12.0,(,1,CC(C)c1ccc2c1[s,CC(C)c1ccc2c([s,15,replace 1 at position 12 with (,flow_matching,0.3,2.0,58,159
115,replace,13.0,c,[,CC(C)c1ccc2c([s,CC(C)c1ccc2c(cs,15,replace [ at position 13 with c,flow_matching,0.3,2.0,58,159
116,replace,14.0,1,s,CC(C)c1ccc2c(cs,CC(C)c1ccc2c(c1,15,replace s at position 14 with 1,flow_matching,0.3,2.0,58,159
117,add,15.0,),,CC(C)c1ccc2c(c1,CC(C)c1ccc2c(c1),16,add ) at position 15,flow_matching,0.3,2.0,58,159
118,add,16.0,[,,CC(C)c1ccc2c(c1),CC(C)c1ccc2c(c1)[,17,add [ at position 16,flow_matching,0.3,2.0,58,159
119,add,17.0,C,,CC(C)c1ccc2c(c1)[,CC(C)c1ccc2c(c1)[C,18,add C at position 17,flow_matching,0.3,2.0,58,159
120,add,18.0,@,,CC(C)c1ccc2c(c1)[C,CC(C)c1ccc2c(c1)[C@,19,add @ at position 18,flow_matching,0.3,2.0,58,159
121,add,19.0,],,CC(C)c1ccc2c(c1)[C@,CC(C)c1ccc2c(c1)[C@],20,add ] at position 19,flow_matching,0.3,2.0,58,159
122,add,20.0,1,,CC(C)c1ccc2c(c1)[C@],CC(C)c1ccc2c(c1)[C@]1,21,add 1 at position 20,flow_matching,0.3,2.0,58,159
123,add,21.0,(,,CC(C)c1ccc2c(c1)[C@]1,CC(C)c1ccc2c(c1)[C@]1(,22,add ( at position 21,flow_matching,0.3,2.0,58,159
124,add,22.0,C,,CC(C)c1ccc2c(c1)[C@]1(,CC(C)c1ccc2c(c1)[C@]1(C,23,add C at position 22,flow_matching,0.3,2.0,58,159
125,add,23.0,C,,CC(C)c1ccc2c(c1)[C@]1(C,CC(C)c1ccc2c(c1)[C@]1(CC,24,add C at position 23,flow_matching,0.3,2.0,58,159
126,add,24.0,(,,CC(C)c1ccc2c(c1)[C@]1(CC,CC(C)c1ccc2c(c1)[C@]1(CC(,25,add ( at position 24,flow_matching,0.3,2.0,58,159
127,add,25.0,O,,CC(C)c1ccc2c(c1)[C@]1(CC(,CC(C)c1ccc2c(c1)[C@]1(CC(O,26,add O at position 25,flow_matching,0.3,2.0,58,159
128,add,26.0,),,CC(C)c1ccc2c(c1)[C@]1(CC(O,CC(C)c1ccc2c(c1)[C@]1(CC(O),27,add ) at position 26,flow_matching,0.3,2.0,58,159
129,add,27.0,=,,CC(C)c1ccc2c(c1)[C@]1(CC(O),CC(C)c1ccc2c(c1)[C@]1(CC(O)=,28,add = at position 27,flow_matching,0.3,2.0,58,159
130,add,28.0,N,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=,CC(C)c1ccc2c(c1)[C@]1(CC(O)=N,29,add N at position 28,flow_matching,0.3,2.0,58,159
131,add,29.0,c,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=N,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc,30,add c at position 29,flow_matching,0.3,2.0,58,159
132,add,30.0,3,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3,31,add 3 at position 30,flow_matching,0.3,2.0,58,159
133,add,31.0,c,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c,32,add c at position 31,flow_matching,0.3,2.0,58,159
134,add,32.0,1,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1,33,add 1 at position 32,flow_matching,0.3,2.0,58,159
135,add,33.0,c,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1c,34,add c at position 33,flow_matching,0.3,2.0,58,159
136,add,34.0,n,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1c,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cn,35,add n at position 34,flow_matching,0.3,2.0,58,159
137,add,35.0,n,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cn,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn,36,add n at position 35,flow_matching,0.3,2.0,58,159
138,add,36.0,3,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3,37,add 3 at position 36,flow_matching,0.3,2.0,58,159
139,add,37.0,C,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3C,38,add C at position 37,flow_matching,0.3,2.0,58,159
140,add,38.0,c,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3C,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc,39,add c at position 38,flow_matching,0.3,2.0,58,159
141,add,39.0,1,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1,40,add 1 at position 39,flow_matching,0.3,2.0,58,159
142,add,40.0,c,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1c,41,add c at position 40,flow_matching,0.3,2.0,58,159
143,add,41.0,c,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1c,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1cc,42,add c at position 41,flow_matching,0.3,2.0,58,159
144,add,42.0,c,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1cc,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccc,43,add c at position 42,flow_matching,0.3,2.0,58,159
145,add,43.0,c,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccc,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1cccc,44,add c at position 43,flow_matching,0.3,2.0,58,159
146,add,44.0,c,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1cccc,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc,45,add c at position 44,flow_matching,0.3,2.0,58,159
147,add,45.0,1,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1,46,add 1 at position 45,flow_matching,0.3,2.0,58,159
148,add,46.0,C,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1C,47,add C at position 46,flow_matching,0.3,2.0,58,159
149,add,47.0,l,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1C,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl,48,add l at position 47,flow_matching,0.3,2.0,58,159
150,add,48.0,),,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl),49,add ) at position 48,flow_matching,0.3,2.0,58,159
151,add,49.0,C,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl),CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C,50,add C at position 49,flow_matching,0.3,2.0,58,159
152,add,50.0,(,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(,51,add ( at position 50,flow_matching,0.3,2.0,58,159
153,add,51.0,=,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=,52,add = at position 51,flow_matching,0.3,2.0,58,159
154,add,52.0,O,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O,53,add O at position 52,flow_matching,0.3,2.0,58,159
155,add,53.0,),,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O),54,add ) at position 53,flow_matching,0.3,2.0,58,159
156,add,54.0,N,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O),CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O)N,55,add N at position 54,flow_matching,0.3,2.0,58,159
157,add,55.0,2,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O)N,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O)N2,56,add 2 at position 55,flow_matching,0.3,2.0,58,159
158,add,56.0,C,,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O)N2,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O)N2C,57,add C at position 56,flow_matching,0.3,2.0,58,159
159,add,57.0,"
",,CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O)N2C,"CC(C)c1ccc2c(c1)[C@]1(CC(O)=Nc3c1cnn3Cc1ccccc1Cl)C(=O)N2C
",58,"add 
 at position 57",flow_matching,0.3,2.0,58,159
