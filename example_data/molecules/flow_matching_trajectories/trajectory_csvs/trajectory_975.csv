step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,40,150
1,add,0.0,F,,,F,1,add F at position 0,flow_matching,0.3,2.0,40,150
2,replace,0.0,C,F,F,C,1,replace F at position 0 with C,flow_matching,0.3,2.0,40,150
3,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,40,150
4,replace,1.0,),c,Cc,C),2,replace c at position 1 with ),flow_matching,0.3,2.0,40,150
5,add,1.0,6,,C),C6),3,add 6 at position 1,flow_matching,0.3,2.0,40,150
6,add,2.0,(,,C6),C6(),4,add ( at position 2,flow_matching,0.3,2.0,40,150
7,remove,3.0,),,C6(),C6(,3,remove ) from position 3,flow_matching,0.3,2.0,40,150
8,replace,1.0,=,6,C6(,C=(,3,replace 6 at position 1 with =,flow_matching,0.3,2.0,40,150
9,replace,1.0,c,=,C=(,Cc(,3,replace = at position 1 with c,flow_matching,0.3,2.0,40,150
10,add,0.0,B,,Cc(,BCc(,4,add B at position 0,flow_matching,0.3,2.0,40,150
11,replace,0.0,C,B,BCc(,CCc(,4,replace B at position 0 with C,flow_matching,0.3,2.0,40,150
12,add,2.0,@,,CCc(,CC@c(,5,add @ at position 2,flow_matching,0.3,2.0,40,150
13,add,1.0,l,,CC@c(,ClC@c(,6,add l at position 1,flow_matching,0.3,2.0,40,150
14,add,2.0,n,,ClC@c(,ClnC@c(,7,add n at position 2,flow_matching,0.3,2.0,40,150
15,replace,1.0,c,l,ClnC@c(,CcnC@c(,7,replace l at position 1 with c,flow_matching,0.3,2.0,40,150
16,add,7.0,+,,CcnC@c(,CcnC@c(+,8,add + at position 7,flow_matching,0.3,2.0,40,150
17,replace,2.0,1,n,CcnC@c(+,Cc1C@c(+,8,replace n at position 2 with 1,flow_matching,0.3,2.0,40,150
18,replace,5.0,-,c,Cc1C@c(+,Cc1C@-(+,8,replace c at position 5 with -,flow_matching,0.3,2.0,40,150
19,remove,4.0,@,,Cc1C@-(+,Cc1C-(+,7,remove @ from position 4,flow_matching,0.3,2.0,40,150
20,add,7.0,F,,Cc1C-(+,Cc1C-(+F,8,add F at position 7,flow_matching,0.3,2.0,40,150
21,replace,3.0,c,C,Cc1C-(+F,Cc1c-(+F,8,replace C at position 3 with c,flow_matching,0.3,2.0,40,150
22,remove,1.0,c,,Cc1c-(+F,C1c-(+F,7,remove c from position 1,flow_matching,0.3,2.0,40,150
23,remove,1.0,1,,C1c-(+F,Cc-(+F,6,remove 1 from position 1,flow_matching,0.3,2.0,40,150
24,add,5.0,],,Cc-(+F,Cc-(+]F,7,add ] at position 5,flow_matching,0.3,2.0,40,150
25,remove,3.0,(,,Cc-(+]F,Cc-+]F,6,remove ( from position 3,flow_matching,0.3,2.0,40,150
26,replace,0.0,O,C,Cc-+]F,Oc-+]F,6,replace C at position 0 with O,flow_matching,0.3,2.0,40,150
27,remove,4.0,],,Oc-+]F,Oc-+F,5,remove ] from position 4,flow_matching,0.3,2.0,40,150
28,replace,0.0,C,O,Oc-+F,Cc-+F,5,replace O at position 0 with C,flow_matching,0.3,2.0,40,150
29,replace,2.0,1,-,Cc-+F,Cc1+F,5,replace - at position 2 with 1,flow_matching,0.3,2.0,40,150
30,add,2.0,N,,Cc1+F,CcN1+F,6,add N at position 2,flow_matching,0.3,2.0,40,150
31,replace,4.0,r,+,CcN1+F,CcN1rF,6,replace + at position 4 with r,flow_matching,0.3,2.0,40,150
32,remove,5.0,F,,CcN1rF,CcN1r,5,remove F from position 5,flow_matching,0.3,2.0,40,150
33,replace,2.0,1,N,CcN1r,Cc11r,5,replace N at position 2 with 1,flow_matching,0.3,2.0,40,150
34,add,0.0,C,,Cc11r,CCc11r,6,add C at position 0,flow_matching,0.3,2.0,40,150
35,add,3.0,-,,CCc11r,CCc-11r,7,add - at position 3,flow_matching,0.3,2.0,40,150
36,remove,3.0,-,,CCc-11r,CCc11r,6,remove - from position 3,flow_matching,0.3,2.0,40,150
37,remove,3.0,1,,CCc11r,CCc1r,5,remove 1 from position 3,flow_matching,0.3,2.0,40,150
38,replace,1.0,c,C,CCc1r,Ccc1r,5,replace C at position 1 with c,flow_matching,0.3,2.0,40,150
39,add,3.0,I,,Ccc1r,CccI1r,6,add I at position 3,flow_matching,0.3,2.0,40,150
40,add,1.0,),,CccI1r,C)ccI1r,7,add ) at position 1,flow_matching,0.3,2.0,40,150
41,remove,6.0,r,,C)ccI1r,C)ccI1,6,remove r from position 6,flow_matching,0.3,2.0,40,150
42,add,2.0,=,,C)ccI1,C)=ccI1,7,add = at position 2,flow_matching,0.3,2.0,40,150
43,add,6.0,],,C)=ccI1,C)=ccI]1,8,add ] at position 6,flow_matching,0.3,2.0,40,150
44,add,4.0,N,,C)=ccI]1,C)=cNcI]1,9,add N at position 4,flow_matching,0.3,2.0,40,150
45,replace,1.0,c,),C)=cNcI]1,Cc=cNcI]1,9,replace ) at position 1 with c,flow_matching,0.3,2.0,40,150
46,remove,1.0,c,,Cc=cNcI]1,C=cNcI]1,8,remove c from position 1,flow_matching,0.3,2.0,40,150
47,add,8.0,B,,C=cNcI]1,C=cNcI]1B,9,add B at position 8,flow_matching,0.3,2.0,40,150
48,remove,1.0,=,,C=cNcI]1B,CcNcI]1B,8,remove = from position 1,flow_matching,0.3,2.0,40,150
49,replace,2.0,1,N,CcNcI]1B,Cc1cI]1B,8,replace N at position 2 with 1,flow_matching,0.3,2.0,40,150
50,add,7.0,l,,Cc1cI]1B,Cc1cI]1lB,9,add l at position 7,flow_matching,0.3,2.0,40,150
51,replace,4.0,c,I,Cc1cI]1lB,Cc1cc]1lB,9,replace I at position 4 with c,flow_matching,0.3,2.0,40,150
52,add,1.0,N,,Cc1cc]1lB,CNc1cc]1lB,10,add N at position 1,flow_matching,0.3,2.0,40,150
53,replace,1.0,c,N,CNc1cc]1lB,Ccc1cc]1lB,10,replace N at position 1 with c,flow_matching,0.3,2.0,40,150
54,add,7.0,N,,Ccc1cc]1lB,Ccc1cc]N1lB,11,add N at position 7,flow_matching,0.3,2.0,40,150
55,remove,10.0,B,,Ccc1cc]N1lB,Ccc1cc]N1l,10,remove B from position 10,flow_matching,0.3,2.0,40,150
56,add,1.0,2,,Ccc1cc]N1l,C2cc1cc]N1l,11,add 2 at position 1,flow_matching,0.3,2.0,40,150
57,remove,4.0,1,,C2cc1cc]N1l,C2cccc]N1l,10,remove 1 from position 4,flow_matching,0.3,2.0,40,150
58,remove,3.0,c,,C2cccc]N1l,C2ccc]N1l,9,remove c from position 3,flow_matching,0.3,2.0,40,150
59,add,9.0,1,,C2ccc]N1l,C2ccc]N1l1,10,add 1 at position 9,flow_matching,0.3,2.0,40,150
60,replace,1.0,c,2,C2ccc]N1l1,Ccccc]N1l1,10,replace 2 at position 1 with c,flow_matching,0.3,2.0,40,150
61,remove,9.0,1,,Ccccc]N1l1,Ccccc]N1l,9,remove 1 from position 9,flow_matching,0.3,2.0,40,150
62,replace,4.0,r,c,Ccccc]N1l,Ccccr]N1l,9,replace c at position 4 with r,flow_matching,0.3,2.0,40,150
63,add,8.0,I,,Ccccr]N1l,Ccccr]N1Il,10,add I at position 8,flow_matching,0.3,2.0,40,150
64,replace,2.0,1,c,Ccccr]N1Il,Cc1cr]N1Il,10,replace c at position 2 with 1,flow_matching,0.3,2.0,40,150
65,add,7.0,o,,Cc1cr]N1Il,Cc1cr]No1Il,11,add o at position 7,flow_matching,0.3,2.0,40,150
66,replace,4.0,c,r,Cc1cr]No1Il,Cc1cc]No1Il,11,replace r at position 4 with c,flow_matching,0.3,2.0,40,150
67,replace,4.0,r,c,Cc1cc]No1Il,Cc1cr]No1Il,11,replace c at position 4 with r,flow_matching,0.3,2.0,40,150
68,replace,4.0,c,r,Cc1cr]No1Il,Cc1cc]No1Il,11,replace r at position 4 with c,flow_matching,0.3,2.0,40,150
69,replace,5.0,(,],Cc1cc]No1Il,Cc1cc(No1Il,11,replace ] at position 5 with (,flow_matching,0.3,2.0,40,150
70,add,6.0,6,,Cc1cc(No1Il,Cc1cc(6No1Il,12,add 6 at position 6,flow_matching,0.3,2.0,40,150
71,remove,11.0,l,,Cc1cc(6No1Il,Cc1cc(6No1I,11,remove l from position 11,flow_matching,0.3,2.0,40,150
72,add,5.0,1,,Cc1cc(6No1I,Cc1cc1(6No1I,12,add 1 at position 5,flow_matching,0.3,2.0,40,150
73,remove,3.0,c,,Cc1cc1(6No1I,Cc1c1(6No1I,11,remove c from position 3,flow_matching,0.3,2.0,40,150
74,replace,4.0,c,1,Cc1c1(6No1I,Cc1cc(6No1I,11,replace 1 at position 4 with c,flow_matching,0.3,2.0,40,150
75,replace,6.0,C,6,Cc1cc(6No1I,Cc1cc(CNo1I,11,replace 6 at position 6 with C,flow_matching,0.3,2.0,40,150
76,add,6.0,/,,Cc1cc(CNo1I,Cc1cc(/CNo1I,12,add / at position 6,flow_matching,0.3,2.0,40,150
77,remove,7.0,C,,Cc1cc(/CNo1I,Cc1cc(/No1I,11,remove C from position 7,flow_matching,0.3,2.0,40,150
78,add,11.0,B,,Cc1cc(/No1I,Cc1cc(/No1IB,12,add B at position 11,flow_matching,0.3,2.0,40,150
79,replace,6.0,C,/,Cc1cc(/No1IB,Cc1cc(CNo1IB,12,replace / at position 6 with C,flow_matching,0.3,2.0,40,150
80,replace,7.0,),N,Cc1cc(CNo1IB,Cc1cc(C)o1IB,12,replace N at position 7 with ),flow_matching,0.3,2.0,40,150
81,remove,1.0,c,,Cc1cc(C)o1IB,C1cc(C)o1IB,11,remove c from position 1,flow_matching,0.3,2.0,40,150
82,add,0.0,5,,C1cc(C)o1IB,5C1cc(C)o1IB,12,add 5 at position 0,flow_matching,0.3,2.0,40,150
83,add,5.0,B,,5C1cc(C)o1IB,5C1ccB(C)o1IB,13,add B at position 5,flow_matching,0.3,2.0,40,150
84,add,7.0,],,5C1ccB(C)o1IB,5C1ccB(]C)o1IB,14,add ] at position 7,flow_matching,0.3,2.0,40,150
85,replace,0.0,C,5,5C1ccB(]C)o1IB,CC1ccB(]C)o1IB,14,replace 5 at position 0 with C,flow_matching,0.3,2.0,40,150
86,add,8.0,/,,CC1ccB(]C)o1IB,CC1ccB(]/C)o1IB,15,add / at position 8,flow_matching,0.3,2.0,40,150
87,add,1.0,/,,CC1ccB(]/C)o1IB,C/C1ccB(]/C)o1IB,16,add / at position 1,flow_matching,0.3,2.0,40,150
88,remove,3.0,1,,C/C1ccB(]/C)o1IB,C/CccB(]/C)o1IB,15,remove 1 from position 3,flow_matching,0.3,2.0,40,150
89,add,7.0,),,C/CccB(]/C)o1IB,C/CccB()]/C)o1IB,16,add ) at position 7,flow_matching,0.3,2.0,40,150
90,replace,4.0,I,c,C/CccB()]/C)o1IB,C/CcIB()]/C)o1IB,16,replace c at position 4 with I,flow_matching,0.3,2.0,40,150
91,add,5.0,S,,C/CcIB()]/C)o1IB,C/CcISB()]/C)o1IB,17,add S at position 5,flow_matching,0.3,2.0,40,150
92,replace,3.0,l,c,C/CcISB()]/C)o1IB,C/ClISB()]/C)o1IB,17,replace c at position 3 with l,flow_matching,0.3,2.0,40,150
93,remove,12.0,),,C/ClISB()]/C)o1IB,C/ClISB()]/Co1IB,16,remove ) from position 12,flow_matching,0.3,2.0,40,150
94,remove,3.0,l,,C/ClISB()]/Co1IB,C/CISB()]/Co1IB,15,remove l from position 3,flow_matching,0.3,2.0,40,150
95,add,0.0,C,,C/CISB()]/Co1IB,CC/CISB()]/Co1IB,16,add C at position 0,flow_matching,0.3,2.0,40,150
96,remove,3.0,C,,CC/CISB()]/Co1IB,CC/ISB()]/Co1IB,15,remove C from position 3,flow_matching,0.3,2.0,40,150
97,replace,1.0,c,C,CC/ISB()]/Co1IB,Cc/ISB()]/Co1IB,15,replace C at position 1 with c,flow_matching,0.3,2.0,40,150
98,add,8.0,[,,Cc/ISB()]/Co1IB,Cc/ISB()[]/Co1IB,16,add [ at position 8,flow_matching,0.3,2.0,40,150
99,replace,2.0,1,/,Cc/ISB()[]/Co1IB,Cc1ISB()[]/Co1IB,16,replace / at position 2 with 1,flow_matching,0.3,2.0,40,150
100,replace,3.0,c,I,Cc1ISB()[]/Co1IB,Cc1cSB()[]/Co1IB,16,replace I at position 3 with c,flow_matching,0.3,2.0,40,150
101,replace,2.0,B,1,Cc1cSB()[]/Co1IB,CcBcSB()[]/Co1IB,16,replace 1 at position 2 with B,flow_matching,0.3,2.0,40,150
102,remove,14.0,I,,CcBcSB()[]/Co1IB,CcBcSB()[]/Co1B,15,remove I from position 14,flow_matching,0.3,2.0,40,150
103,replace,5.0,(,B,CcBcSB()[]/Co1B,CcBcS(()[]/Co1B,15,replace B at position 5 with (,flow_matching,0.3,2.0,40,150
104,add,15.0,5,,CcBcS(()[]/Co1B,CcBcS(()[]/Co1B5,16,add 5 at position 15,flow_matching,0.3,2.0,40,150
105,replace,11.0,=,C,CcBcS(()[]/Co1B5,CcBcS(()[]/=o1B5,16,replace C at position 11 with =,flow_matching,0.3,2.0,40,150
106,replace,2.0,1,B,CcBcS(()[]/=o1B5,Cc1cS(()[]/=o1B5,16,replace B at position 2 with 1,flow_matching,0.3,2.0,40,150
107,add,15.0,o,,Cc1cS(()[]/=o1B5,Cc1cS(()[]/=o1Bo5,17,add o at position 15,flow_matching,0.3,2.0,40,150
108,remove,2.0,1,,Cc1cS(()[]/=o1Bo5,CccS(()[]/=o1Bo5,16,remove 1 from position 2,flow_matching,0.3,2.0,40,150
109,replace,1.0,F,c,CccS(()[]/=o1Bo5,CFcS(()[]/=o1Bo5,16,replace c at position 1 with F,flow_matching,0.3,2.0,40,150
110,replace,1.0,c,F,CFcS(()[]/=o1Bo5,CccS(()[]/=o1Bo5,16,replace F at position 1 with c,flow_matching,0.3,2.0,40,150
111,add,10.0,r,,CccS(()[]/=o1Bo5,CccS(()[]/r=o1Bo5,17,add r at position 10,flow_matching,0.3,2.0,40,150
112,add,16.0,1,,CccS(()[]/r=o1Bo5,CccS(()[]/r=o1Bo15,18,add 1 at position 16,flow_matching,0.3,2.0,40,150
113,add,6.0,-,,CccS(()[]/r=o1Bo15,CccS((-)[]/r=o1Bo15,19,add - at position 6,flow_matching,0.3,2.0,40,150
114,replace,8.0,F,[,CccS((-)[]/r=o1Bo15,CccS((-)F]/r=o1Bo15,19,replace [ at position 8 with F,flow_matching,0.3,2.0,40,150
115,replace,2.0,1,c,CccS((-)F]/r=o1Bo15,Cc1S((-)F]/r=o1Bo15,19,replace c at position 2 with 1,flow_matching,0.3,2.0,40,150
116,replace,3.0,c,S,Cc1S((-)F]/r=o1Bo15,Cc1c((-)F]/r=o1Bo15,19,replace S at position 3 with c,flow_matching,0.3,2.0,40,150
117,replace,4.0,c,(,Cc1c((-)F]/r=o1Bo15,Cc1cc(-)F]/r=o1Bo15,19,replace ( at position 4 with c,flow_matching,0.3,2.0,40,150
118,replace,6.0,C,-,Cc1cc(-)F]/r=o1Bo15,Cc1cc(C)F]/r=o1Bo15,19,replace - at position 6 with C,flow_matching,0.3,2.0,40,150
119,replace,8.0,c,F,Cc1cc(C)F]/r=o1Bo15,Cc1cc(C)c]/r=o1Bo15,19,replace F at position 8 with c,flow_matching,0.3,2.0,40,150
120,replace,9.0,c,],Cc1cc(C)c]/r=o1Bo15,Cc1cc(C)cc/r=o1Bo15,19,replace ] at position 9 with c,flow_matching,0.3,2.0,40,150
121,replace,10.0,(,/,Cc1cc(C)cc/r=o1Bo15,Cc1cc(C)cc(r=o1Bo15,19,replace / at position 10 with (,flow_matching,0.3,2.0,40,150
122,replace,11.0,O,r,Cc1cc(C)cc(r=o1Bo15,Cc1cc(C)cc(O=o1Bo15,19,replace r at position 11 with O,flow_matching,0.3,2.0,40,150
123,replace,12.0,[,=,Cc1cc(C)cc(O=o1Bo15,Cc1cc(C)cc(O[o1Bo15,19,replace = at position 12 with [,flow_matching,0.3,2.0,40,150
124,replace,13.0,C,o,Cc1cc(C)cc(O[o1Bo15,Cc1cc(C)cc(O[C1Bo15,19,replace o at position 13 with C,flow_matching,0.3,2.0,40,150
125,replace,14.0,@,1,Cc1cc(C)cc(O[C1Bo15,Cc1cc(C)cc(O[C@Bo15,19,replace 1 at position 14 with @,flow_matching,0.3,2.0,40,150
126,replace,15.0,H,B,Cc1cc(C)cc(O[C@Bo15,Cc1cc(C)cc(O[C@Ho15,19,replace B at position 15 with H,flow_matching,0.3,2.0,40,150
127,replace,16.0,],o,Cc1cc(C)cc(O[C@Ho15,Cc1cc(C)cc(O[C@H]15,19,replace o at position 16 with ],flow_matching,0.3,2.0,40,150
128,replace,17.0,2,1,Cc1cc(C)cc(O[C@H]15,Cc1cc(C)cc(O[C@H]25,19,replace 1 at position 17 with 2,flow_matching,0.3,2.0,40,150
129,replace,18.0,C,5,Cc1cc(C)cc(O[C@H]25,Cc1cc(C)cc(O[C@H]2C,19,replace 5 at position 18 with C,flow_matching,0.3,2.0,40,150
130,add,19.0,C,,Cc1cc(C)cc(O[C@H]2C,Cc1cc(C)cc(O[C@H]2CC,20,add C at position 19,flow_matching,0.3,2.0,40,150
131,add,20.0,C,,Cc1cc(C)cc(O[C@H]2CC,Cc1cc(C)cc(O[C@H]2CCC,21,add C at position 20,flow_matching,0.3,2.0,40,150
132,add,21.0,C,,Cc1cc(C)cc(O[C@H]2CCC,Cc1cc(C)cc(O[C@H]2CCCC,22,add C at position 21,flow_matching,0.3,2.0,40,150
133,add,22.0,(,,Cc1cc(C)cc(O[C@H]2CCCC,Cc1cc(C)cc(O[C@H]2CCCC(,23,add ( at position 22,flow_matching,0.3,2.0,40,150
134,add,23.0,C,,Cc1cc(C)cc(O[C@H]2CCCC(,Cc1cc(C)cc(O[C@H]2CCCC(C,24,add C at position 23,flow_matching,0.3,2.0,40,150
135,add,24.0,),,Cc1cc(C)cc(O[C@H]2CCCC(C,Cc1cc(C)cc(O[C@H]2CCCC(C),25,add ) at position 24,flow_matching,0.3,2.0,40,150
136,add,25.0,(,,Cc1cc(C)cc(O[C@H]2CCCC(C),Cc1cc(C)cc(O[C@H]2CCCC(C)(,26,add ( at position 25,flow_matching,0.3,2.0,40,150
137,add,26.0,C,,Cc1cc(C)cc(O[C@H]2CCCC(C)(,Cc1cc(C)cc(O[C@H]2CCCC(C)(C,27,add C at position 26,flow_matching,0.3,2.0,40,150
138,add,27.0,),,Cc1cc(C)cc(O[C@H]2CCCC(C)(C,Cc1cc(C)cc(O[C@H]2CCCC(C)(C),28,add ) at position 27,flow_matching,0.3,2.0,40,150
139,add,28.0,[,,Cc1cc(C)cc(O[C@H]2CCCC(C)(C),Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[,29,add [ at position 28,flow_matching,0.3,2.0,40,150
140,add,29.0,C,,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C,30,add C at position 29,flow_matching,0.3,2.0,40,150
141,add,30.0,@,,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@,31,add @ at position 30,flow_matching,0.3,2.0,40,150
142,add,31.0,@,,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@,32,add @ at position 31,flow_matching,0.3,2.0,40,150
143,add,32.0,H,,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H,33,add H at position 32,flow_matching,0.3,2.0,40,150
144,add,33.0,],,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H],34,add ] at position 33,flow_matching,0.3,2.0,40,150
145,add,34.0,2,,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H],Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2,35,add 2 at position 34,flow_matching,0.3,2.0,40,150
146,add,35.0,O,,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2O,36,add O at position 35,flow_matching,0.3,2.0,40,150
147,add,36.0,),,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2O,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2O),37,add ) at position 36,flow_matching,0.3,2.0,40,150
148,add,37.0,c,,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2O),Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2O)c,38,add c at position 37,flow_matching,0.3,2.0,40,150
149,add,38.0,1,,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2O)c,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2O)c1,39,add 1 at position 38,flow_matching,0.3,2.0,40,150
150,add,39.0,"
",,Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2O)c1,"Cc1cc(C)cc(O[C@H]2CCCC(C)(C)[C@@H]2O)c1
",40,"add 
 at position 39",flow_matching,0.3,2.0,40,150
