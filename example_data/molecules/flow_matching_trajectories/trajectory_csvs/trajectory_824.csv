step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,54,165
1,add,0.0,S,,,S,1,add S at position 0,flow_matching,0.3,2.0,54,165
2,add,0.0,S,,S,SS,2,add S at position 0,flow_matching,0.3,2.0,54,165
3,remove,0.0,S,,SS,S,1,remove S from position 0,flow_matching,0.3,2.0,54,165
4,add,1.0,r,,S,Sr,2,add r at position 1,flow_matching,0.3,2.0,54,165
5,add,1.0,S,,Sr,SSr,3,add S at position 1,flow_matching,0.3,2.0,54,165
6,replace,2.0,<PERSON>,r,SSr,SSI,3,replace r at position 2 with I,flow_matching,0.3,2.0,54,165
7,add,2.0,5,,SSI,SS5I,4,add 5 at position 2,flow_matching,0.3,2.0,54,165
8,remove,0.0,S,,SS5I,S5I,3,remove S from position 0,flow_matching,0.3,2.0,54,165
9,replace,0.0,C,S,S5I,C5I,3,replace S at position 0 with C,flow_matching,0.3,2.0,54,165
10,remove,1.0,5,,C5I,CI,2,remove 5 from position 1,flow_matching,0.3,2.0,54,165
11,replace,0.0,S,C,CI,SI,2,replace C at position 0 with S,flow_matching,0.3,2.0,54,165
12,replace,0.0,C,S,SI,CI,2,replace S at position 0 with C,flow_matching,0.3,2.0,54,165
13,replace,1.0,6,I,CI,C6,2,replace I at position 1 with 6,flow_matching,0.3,2.0,54,165
14,add,1.0,5,,C6,C56,3,add 5 at position 1,flow_matching,0.3,2.0,54,165
15,replace,1.0,c,5,C56,Cc6,3,replace 5 at position 1 with c,flow_matching,0.3,2.0,54,165
16,add,1.0,F,,Cc6,CFc6,4,add F at position 1,flow_matching,0.3,2.0,54,165
17,remove,1.0,F,,CFc6,Cc6,3,remove F from position 1,flow_matching,0.3,2.0,54,165
18,remove,1.0,c,,Cc6,C6,2,remove c from position 1,flow_matching,0.3,2.0,54,165
19,replace,1.0,c,6,C6,Cc,2,replace 6 at position 1 with c,flow_matching,0.3,2.0,54,165
20,add,0.0,6,,Cc,6Cc,3,add 6 at position 0,flow_matching,0.3,2.0,54,165
21,replace,0.0,],6,6Cc,]Cc,3,replace 6 at position 0 with ],flow_matching,0.3,2.0,54,165
22,replace,1.0,7,C,]Cc,]7c,3,replace C at position 1 with 7,flow_matching,0.3,2.0,54,165
23,replace,0.0,C,],]7c,C7c,3,replace ] at position 0 with C,flow_matching,0.3,2.0,54,165
24,add,1.0,N,,C7c,CN7c,4,add N at position 1,flow_matching,0.3,2.0,54,165
25,remove,2.0,7,,CN7c,CNc,3,remove 7 from position 2,flow_matching,0.3,2.0,54,165
26,replace,2.0,=,c,CNc,CN=,3,replace c at position 2 with =,flow_matching,0.3,2.0,54,165
27,replace,1.0,c,N,CN=,Cc=,3,replace N at position 1 with c,flow_matching,0.3,2.0,54,165
28,replace,2.0,1,=,Cc=,Cc1,3,replace = at position 2 with 1,flow_matching,0.3,2.0,54,165
29,replace,1.0,2,c,Cc1,C21,3,replace c at position 1 with 2,flow_matching,0.3,2.0,54,165
30,remove,0.0,C,,C21,21,2,remove C from position 0,flow_matching,0.3,2.0,54,165
31,add,2.0,S,,21,21S,3,add S at position 2,flow_matching,0.3,2.0,54,165
32,replace,0.0,l,2,21S,l1S,3,replace 2 at position 0 with l,flow_matching,0.3,2.0,54,165
33,replace,2.0,#,S,l1S,l1#,3,replace S at position 2 with #,flow_matching,0.3,2.0,54,165
34,replace,0.0,C,l,l1#,C1#,3,replace l at position 0 with C,flow_matching,0.3,2.0,54,165
35,replace,1.0,c,1,C1#,Cc#,3,replace 1 at position 1 with c,flow_matching,0.3,2.0,54,165
36,replace,1.0,o,c,Cc#,Co#,3,replace c at position 1 with o,flow_matching,0.3,2.0,54,165
37,replace,1.0,2,o,Co#,C2#,3,replace o at position 1 with 2,flow_matching,0.3,2.0,54,165
38,replace,1.0,c,2,C2#,Cc#,3,replace 2 at position 1 with c,flow_matching,0.3,2.0,54,165
39,add,0.0,C,,Cc#,CCc#,4,add C at position 0,flow_matching,0.3,2.0,54,165
40,add,3.0,C,,CCc#,CCcC#,5,add C at position 3,flow_matching,0.3,2.0,54,165
41,replace,2.0,S,c,CCcC#,CCSC#,5,replace c at position 2 with S,flow_matching,0.3,2.0,54,165
42,replace,1.0,c,C,CCSC#,CcSC#,5,replace C at position 1 with c,flow_matching,0.3,2.0,54,165
43,replace,1.0,B,c,CcSC#,CBSC#,5,replace c at position 1 with B,flow_matching,0.3,2.0,54,165
44,replace,1.0,c,B,CBSC#,CcSC#,5,replace B at position 1 with c,flow_matching,0.3,2.0,54,165
45,replace,1.0,@,c,CcSC#,C@SC#,5,replace c at position 1 with @,flow_matching,0.3,2.0,54,165
46,add,4.0,S,,C@SC#,C@SCS#,6,add S at position 4,flow_matching,0.3,2.0,54,165
47,replace,1.0,c,@,C@SCS#,CcSCS#,6,replace @ at position 1 with c,flow_matching,0.3,2.0,54,165
48,replace,3.0,@,C,CcSCS#,CcS@S#,6,replace C at position 3 with @,flow_matching,0.3,2.0,54,165
49,add,1.0,S,,CcS@S#,CScS@S#,7,add S at position 1,flow_matching,0.3,2.0,54,165
50,replace,6.0,],#,CScS@S#,CScS@S],7,replace # at position 6 with ],flow_matching,0.3,2.0,54,165
51,remove,3.0,S,,CScS@S],CSc@S],6,remove S from position 3,flow_matching,0.3,2.0,54,165
52,remove,1.0,S,,CSc@S],Cc@S],5,remove S from position 1,flow_matching,0.3,2.0,54,165
53,remove,1.0,c,,Cc@S],C@S],4,remove c from position 1,flow_matching,0.3,2.0,54,165
54,replace,1.0,c,@,C@S],CcS],4,replace @ at position 1 with c,flow_matching,0.3,2.0,54,165
55,remove,2.0,S,,CcS],Cc],3,remove S from position 2,flow_matching,0.3,2.0,54,165
56,replace,2.0,1,],Cc],Cc1,3,replace ] at position 2 with 1,flow_matching,0.3,2.0,54,165
57,replace,1.0,S,c,Cc1,CS1,3,replace c at position 1 with S,flow_matching,0.3,2.0,54,165
58,replace,1.0,c,S,CS1,Cc1,3,replace S at position 1 with c,flow_matching,0.3,2.0,54,165
59,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,54,165
60,add,0.0,],,Cc1c,]Cc1c,5,add ] at position 0,flow_matching,0.3,2.0,54,165
61,add,0.0,/,,]Cc1c,/]Cc1c,6,add / at position 0,flow_matching,0.3,2.0,54,165
62,remove,4.0,1,,/]Cc1c,/]Ccc,5,remove 1 from position 4,flow_matching,0.3,2.0,54,165
63,remove,1.0,],,/]Ccc,/Ccc,4,remove ] from position 1,flow_matching,0.3,2.0,54,165
64,remove,1.0,C,,/Ccc,/cc,3,remove C from position 1,flow_matching,0.3,2.0,54,165
65,add,1.0,r,,/cc,/rcc,4,add r at position 1,flow_matching,0.3,2.0,54,165
66,add,3.0,-,,/rcc,/rc-c,5,add - at position 3,flow_matching,0.3,2.0,54,165
67,replace,2.0,O,c,/rc-c,/rO-c,5,replace c at position 2 with O,flow_matching,0.3,2.0,54,165
68,add,5.0,+,,/rO-c,/rO-c+,6,add + at position 5,flow_matching,0.3,2.0,54,165
69,replace,1.0,#,r,/rO-c+,/#O-c+,6,replace r at position 1 with #,flow_matching,0.3,2.0,54,165
70,replace,0.0,C,/,/#O-c+,C#O-c+,6,replace / at position 0 with C,flow_matching,0.3,2.0,54,165
71,add,3.0,c,,C#O-c+,C#Oc-c+,7,add c at position 3,flow_matching,0.3,2.0,54,165
72,replace,1.0,c,#,C#Oc-c+,CcOc-c+,7,replace # at position 1 with c,flow_matching,0.3,2.0,54,165
73,add,3.0,),,CcOc-c+,CcO)c-c+,8,add ) at position 3,flow_matching,0.3,2.0,54,165
74,replace,2.0,1,O,CcO)c-c+,Cc1)c-c+,8,replace O at position 2 with 1,flow_matching,0.3,2.0,54,165
75,replace,3.0,c,),Cc1)c-c+,Cc1cc-c+,8,replace ) at position 3 with c,flow_matching,0.3,2.0,54,165
76,replace,4.0,o,c,Cc1cc-c+,Cc1co-c+,8,replace c at position 4 with o,flow_matching,0.3,2.0,54,165
77,add,4.0,/,,Cc1co-c+,Cc1c/o-c+,9,add / at position 4,flow_matching,0.3,2.0,54,165
78,replace,4.0,c,/,Cc1c/o-c+,Cc1cco-c+,9,replace / at position 4 with c,flow_matching,0.3,2.0,54,165
79,replace,5.0,c,o,Cc1cco-c+,Cc1ccc-c+,9,replace o at position 5 with c,flow_matching,0.3,2.0,54,165
80,replace,7.0,S,c,Cc1ccc-c+,Cc1ccc-S+,9,replace c at position 7 with S,flow_matching,0.3,2.0,54,165
81,remove,3.0,c,,Cc1ccc-S+,Cc1cc-S+,8,remove c from position 3,flow_matching,0.3,2.0,54,165
82,remove,2.0,1,,Cc1cc-S+,Cccc-S+,7,remove 1 from position 2,flow_matching,0.3,2.0,54,165
83,replace,0.0,6,C,Cccc-S+,6ccc-S+,7,replace C at position 0 with 6,flow_matching,0.3,2.0,54,165
84,replace,2.0,1,c,6ccc-S+,6c1c-S+,7,replace c at position 2 with 1,flow_matching,0.3,2.0,54,165
85,remove,3.0,c,,6c1c-S+,6c1-S+,6,remove c from position 3,flow_matching,0.3,2.0,54,165
86,replace,0.0,C,6,6c1-S+,Cc1-S+,6,replace 6 at position 0 with C,flow_matching,0.3,2.0,54,165
87,replace,3.0,c,-,Cc1-S+,Cc1cS+,6,replace - at position 3 with c,flow_matching,0.3,2.0,54,165
88,replace,2.0,S,1,Cc1cS+,CcScS+,6,replace 1 at position 2 with S,flow_matching,0.3,2.0,54,165
89,add,5.0,l,,CcScS+,CcScSl+,7,add l at position 5,flow_matching,0.3,2.0,54,165
90,replace,6.0,5,+,CcScSl+,CcScSl5,7,replace + at position 6 with 5,flow_matching,0.3,2.0,54,165
91,replace,3.0,N,c,CcScSl5,CcSNSl5,7,replace c at position 3 with N,flow_matching,0.3,2.0,54,165
92,add,4.0,=,,CcSNSl5,CcSN=Sl5,8,add = at position 4,flow_matching,0.3,2.0,54,165
93,replace,7.0,N,5,CcSN=Sl5,CcSN=SlN,8,replace 5 at position 7 with N,flow_matching,0.3,2.0,54,165
94,add,2.0,F,,CcSN=SlN,CcFSN=SlN,9,add F at position 2,flow_matching,0.3,2.0,54,165
95,add,6.0,C,,CcFSN=SlN,CcFSN=CSlN,10,add C at position 6,flow_matching,0.3,2.0,54,165
96,add,6.0,@,,CcFSN=CSlN,CcFSN=@CSlN,11,add @ at position 6,flow_matching,0.3,2.0,54,165
97,replace,1.0,[,c,CcFSN=@CSlN,C[FSN=@CSlN,11,replace c at position 1 with [,flow_matching,0.3,2.0,54,165
98,add,1.0,N,,C[FSN=@CSlN,CN[FSN=@CSlN,12,add N at position 1,flow_matching,0.3,2.0,54,165
99,replace,2.0,r,[,CN[FSN=@CSlN,CNrFSN=@CSlN,12,replace [ at position 2 with r,flow_matching,0.3,2.0,54,165
100,replace,7.0,5,@,CNrFSN=@CSlN,CNrFSN=5CSlN,12,replace @ at position 7 with 5,flow_matching,0.3,2.0,54,165
101,remove,8.0,C,,CNrFSN=5CSlN,CNrFSN=5SlN,11,remove C from position 8,flow_matching,0.3,2.0,54,165
102,replace,6.0,1,=,CNrFSN=5SlN,CNrFSN15SlN,11,replace = at position 6 with 1,flow_matching,0.3,2.0,54,165
103,add,8.0,+,,CNrFSN15SlN,CNrFSN15+SlN,12,add + at position 8,flow_matching,0.3,2.0,54,165
104,replace,1.0,c,N,CNrFSN15+SlN,CcrFSN15+SlN,12,replace N at position 1 with c,flow_matching,0.3,2.0,54,165
105,replace,2.0,1,r,CcrFSN15+SlN,Cc1FSN15+SlN,12,replace r at position 2 with 1,flow_matching,0.3,2.0,54,165
106,add,2.0,7,,Cc1FSN15+SlN,Cc71FSN15+SlN,13,add 7 at position 2,flow_matching,0.3,2.0,54,165
107,replace,3.0,n,1,Cc71FSN15+SlN,Cc7nFSN15+SlN,13,replace 1 at position 3 with n,flow_matching,0.3,2.0,54,165
108,replace,6.0,\,N,Cc7nFSN15+SlN,Cc7nFS\15+SlN,13,replace N at position 6 with \,flow_matching,0.3,2.0,54,165
109,replace,1.0,B,c,Cc7nFS\15+SlN,CB7nFS\15+SlN,13,replace c at position 1 with B,flow_matching,0.3,2.0,54,165
110,remove,8.0,5,,CB7nFS\15+SlN,CB7nFS\1+SlN,12,remove 5 from position 8,flow_matching,0.3,2.0,54,165
111,replace,1.0,c,B,CB7nFS\1+SlN,Cc7nFS\1+SlN,12,replace B at position 1 with c,flow_matching,0.3,2.0,54,165
112,replace,2.0,1,7,Cc7nFS\1+SlN,Cc1nFS\1+SlN,12,replace 7 at position 2 with 1,flow_matching,0.3,2.0,54,165
113,add,8.0,-,,Cc1nFS\1+SlN,Cc1nFS\1-+SlN,13,add - at position 8,flow_matching,0.3,2.0,54,165
114,replace,3.0,c,n,Cc1nFS\1-+SlN,Cc1cFS\1-+SlN,13,replace n at position 3 with c,flow_matching,0.3,2.0,54,165
115,add,4.0,2,,Cc1cFS\1-+SlN,Cc1c2FS\1-+SlN,14,add 2 at position 4,flow_matching,0.3,2.0,54,165
116,replace,4.0,c,2,Cc1c2FS\1-+SlN,Cc1ccFS\1-+SlN,14,replace 2 at position 4 with c,flow_matching,0.3,2.0,54,165
117,replace,5.0,c,F,Cc1ccFS\1-+SlN,Cc1cccS\1-+SlN,14,replace F at position 5 with c,flow_matching,0.3,2.0,54,165
118,replace,6.0,2,S,Cc1cccS\1-+SlN,Cc1ccc2\1-+SlN,14,replace S at position 6 with 2,flow_matching,0.3,2.0,54,165
119,replace,7.0,c,\,Cc1ccc2\1-+SlN,Cc1ccc2c1-+SlN,14,replace \ at position 7 with c,flow_matching,0.3,2.0,54,165
120,replace,8.0,(,1,Cc1ccc2c1-+SlN,Cc1ccc2c(-+SlN,14,replace 1 at position 8 with (,flow_matching,0.3,2.0,54,165
121,replace,9.0,c,-,Cc1ccc2c(-+SlN,Cc1ccc2c(c+SlN,14,replace - at position 9 with c,flow_matching,0.3,2.0,54,165
122,replace,10.0,1,+,Cc1ccc2c(c+SlN,Cc1ccc2c(c1SlN,14,replace + at position 10 with 1,flow_matching,0.3,2.0,54,165
123,replace,11.0,),S,Cc1ccc2c(c1SlN,Cc1ccc2c(c1)lN,14,replace S at position 11 with ),flow_matching,0.3,2.0,54,165
124,replace,12.0,-,l,Cc1ccc2c(c1)lN,Cc1ccc2c(c1)-N,14,replace l at position 12 with -,flow_matching,0.3,2.0,54,165
125,replace,13.0,c,N,Cc1ccc2c(c1)-N,Cc1ccc2c(c1)-c,14,replace N at position 13 with c,flow_matching,0.3,2.0,54,165
126,add,14.0,1,,Cc1ccc2c(c1)-c,Cc1ccc2c(c1)-c1,15,add 1 at position 14,flow_matching,0.3,2.0,54,165
127,add,15.0,o,,Cc1ccc2c(c1)-c1,Cc1ccc2c(c1)-c1o,16,add o at position 15,flow_matching,0.3,2.0,54,165
128,add,16.0,n,,Cc1ccc2c(c1)-c1o,Cc1ccc2c(c1)-c1on,17,add n at position 16,flow_matching,0.3,2.0,54,165
129,add,17.0,c,,Cc1ccc2c(c1)-c1on,Cc1ccc2c(c1)-c1onc,18,add c at position 17,flow_matching,0.3,2.0,54,165
130,add,18.0,(,,Cc1ccc2c(c1)-c1onc,Cc1ccc2c(c1)-c1onc(,19,add ( at position 18,flow_matching,0.3,2.0,54,165
131,add,19.0,C,,Cc1ccc2c(c1)-c1onc(,Cc1ccc2c(c1)-c1onc(C,20,add C at position 19,flow_matching,0.3,2.0,54,165
132,add,20.0,(,,Cc1ccc2c(c1)-c1onc(C,Cc1ccc2c(c1)-c1onc(C(,21,add ( at position 20,flow_matching,0.3,2.0,54,165
133,add,21.0,=,,Cc1ccc2c(c1)-c1onc(C(,Cc1ccc2c(c1)-c1onc(C(=,22,add = at position 21,flow_matching,0.3,2.0,54,165
134,add,22.0,O,,Cc1ccc2c(c1)-c1onc(C(=,Cc1ccc2c(c1)-c1onc(C(=O,23,add O at position 22,flow_matching,0.3,2.0,54,165
135,add,23.0,),,Cc1ccc2c(c1)-c1onc(C(=O,Cc1ccc2c(c1)-c1onc(C(=O),24,add ) at position 23,flow_matching,0.3,2.0,54,165
136,add,24.0,N,,Cc1ccc2c(c1)-c1onc(C(=O),Cc1ccc2c(c1)-c1onc(C(=O)N,25,add N at position 24,flow_matching,0.3,2.0,54,165
137,add,25.0,3,,Cc1ccc2c(c1)-c1onc(C(=O)N,Cc1ccc2c(c1)-c1onc(C(=O)N3,26,add 3 at position 25,flow_matching,0.3,2.0,54,165
138,add,26.0,C,,Cc1ccc2c(c1)-c1onc(C(=O)N3,Cc1ccc2c(c1)-c1onc(C(=O)N3C,27,add C at position 26,flow_matching,0.3,2.0,54,165
139,add,27.0,[,,Cc1ccc2c(c1)-c1onc(C(=O)N3C,Cc1ccc2c(c1)-c1onc(C(=O)N3C[,28,add [ at position 27,flow_matching,0.3,2.0,54,165
140,add,28.0,C,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C,29,add C at position 28,flow_matching,0.3,2.0,54,165
141,add,29.0,@,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@,30,add @ at position 29,flow_matching,0.3,2.0,54,165
142,add,30.0,@,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@,31,add @ at position 30,flow_matching,0.3,2.0,54,165
143,add,31.0,H,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H,32,add H at position 31,flow_matching,0.3,2.0,54,165
144,add,32.0,],,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H],33,add ] at position 32,flow_matching,0.3,2.0,54,165
145,add,33.0,(,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H],Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](,34,add ( at position 33,flow_matching,0.3,2.0,54,165
146,add,34.0,C,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C,35,add C at position 34,flow_matching,0.3,2.0,54,165
147,add,35.0,),,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C),36,add ) at position 35,flow_matching,0.3,2.0,54,165
148,add,36.0,C,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C),Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C,37,add C at position 36,flow_matching,0.3,2.0,54,165
149,add,37.0,[,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[,38,add [ at position 37,flow_matching,0.3,2.0,54,165
150,add,38.0,C,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C,39,add C at position 38,flow_matching,0.3,2.0,54,165
151,add,39.0,@,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@,40,add @ at position 39,flow_matching,0.3,2.0,54,165
152,add,40.0,H,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H,41,add H at position 40,flow_matching,0.3,2.0,54,165
153,add,41.0,],,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H],42,add ] at position 41,flow_matching,0.3,2.0,54,165
154,add,42.0,(,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H],Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](,43,add ( at position 42,flow_matching,0.3,2.0,54,165
155,add,43.0,C,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C,44,add C at position 43,flow_matching,0.3,2.0,54,165
156,add,44.0,),,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C),45,add ) at position 44,flow_matching,0.3,2.0,54,165
157,add,45.0,C,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C),Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C,46,add C at position 45,flow_matching,0.3,2.0,54,165
158,add,46.0,3,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3,47,add 3 at position 46,flow_matching,0.3,2.0,54,165
159,add,47.0,),,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3),48,add ) at position 47,flow_matching,0.3,2.0,54,165
160,add,48.0,c,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3),Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c,49,add c at position 48,flow_matching,0.3,2.0,54,165
161,add,49.0,1,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c1,50,add 1 at position 49,flow_matching,0.3,2.0,54,165
162,add,50.0,C,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c1,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c1C,51,add C at position 50,flow_matching,0.3,2.0,54,165
163,add,51.0,O,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c1C,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c1CO,52,add O at position 51,flow_matching,0.3,2.0,54,165
164,add,52.0,2,,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c1CO,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c1CO2,53,add 2 at position 52,flow_matching,0.3,2.0,54,165
165,add,53.0,"
",,Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c1CO2,"Cc1ccc2c(c1)-c1onc(C(=O)N3C[C@@H](C)C[C@H](C)C3)c1CO2
",54,"add 
 at position 53",flow_matching,0.3,2.0,54,165
