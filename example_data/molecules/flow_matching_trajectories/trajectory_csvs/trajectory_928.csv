step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,39,136
1,add,0.0,),,,),1,add ) at position 0,flow_matching,0.3,2.0,39,136
2,add,1.0,\,,),)\,2,add \ at position 1,flow_matching,0.3,2.0,39,136
3,add,1.0,F,,)\,)F\,3,add F at position 1,flow_matching,0.3,2.0,39,136
4,replace,0.0,S,),)F\,SF\,3,replace ) at position 0 with S,flow_matching,0.3,2.0,39,136
5,replace,0.0,3,S,SF\,3F\,3,replace S at position 0 with 3,flow_matching,0.3,2.0,39,136
6,replace,0.0,C,3,3F\,CF\,3,replace 3 at position 0 with C,flow_matching,0.3,2.0,39,136
7,add,3.0,-,,CF\,CF\-,4,add - at position 3,flow_matching,0.3,2.0,39,136
8,replace,1.0,O,F,CF\-,CO\-,4,replace F at position 1 with O,flow_matching,0.3,2.0,39,136
9,add,1.0,#,,CO\-,C#O\-,5,add # at position 1,flow_matching,0.3,2.0,39,136
10,replace,1.0,@,#,C#O\-,C@O\-,5,replace # at position 1 with @,flow_matching,0.3,2.0,39,136
11,replace,1.0,O,@,C@O\-,COO\-,5,replace @ at position 1 with O,flow_matching,0.3,2.0,39,136
12,replace,2.0,5,O,COO\-,CO5\-,5,replace O at position 2 with 5,flow_matching,0.3,2.0,39,136
13,add,3.0,B,,CO5\-,CO5B\-,6,add B at position 3,flow_matching,0.3,2.0,39,136
14,add,4.0,=,,CO5B\-,CO5B=\-,7,add = at position 4,flow_matching,0.3,2.0,39,136
15,add,6.0,2,,CO5B=\-,CO5B=\2-,8,add 2 at position 6,flow_matching,0.3,2.0,39,136
16,add,7.0,6,,CO5B=\2-,CO5B=\26-,9,add 6 at position 7,flow_matching,0.3,2.0,39,136
17,add,6.0,[,,CO5B=\26-,CO5B=\[26-,10,add [ at position 6,flow_matching,0.3,2.0,39,136
18,add,8.0,=,,CO5B=\[26-,CO5B=\[2=6-,11,add = at position 8,flow_matching,0.3,2.0,39,136
19,replace,2.0,c,5,CO5B=\[2=6-,COcB=\[2=6-,11,replace 5 at position 2 with c,flow_matching,0.3,2.0,39,136
20,replace,7.0,H,2,COcB=\[2=6-,COcB=\[H=6-,11,replace 2 at position 7 with H,flow_matching,0.3,2.0,39,136
21,replace,3.0,1,B,COcB=\[H=6-,COc1=\[H=6-,11,replace B at position 3 with 1,flow_matching,0.3,2.0,39,136
22,replace,4.0,c,=,COc1=\[H=6-,COc1c\[H=6-,11,replace = at position 4 with c,flow_matching,0.3,2.0,39,136
23,add,2.0,2,,COc1c\[H=6-,CO2c1c\[H=6-,12,add 2 at position 2,flow_matching,0.3,2.0,39,136
24,remove,10.0,6,,CO2c1c\[H=6-,CO2c1c\[H=-,11,remove 6 from position 10,flow_matching,0.3,2.0,39,136
25,replace,5.0,N,c,CO2c1c\[H=-,CO2c1N\[H=-,11,replace c at position 5 with N,flow_matching,0.3,2.0,39,136
26,replace,2.0,c,2,CO2c1N\[H=-,COcc1N\[H=-,11,replace 2 at position 2 with c,flow_matching,0.3,2.0,39,136
27,replace,3.0,1,c,COcc1N\[H=-,COc11N\[H=-,11,replace c at position 3 with 1,flow_matching,0.3,2.0,39,136
28,remove,6.0,\,,COc11N\[H=-,COc11N[H=-,10,remove \ from position 6,flow_matching,0.3,2.0,39,136
29,replace,4.0,c,1,COc11N[H=-,COc1cN[H=-,10,replace 1 at position 4 with c,flow_matching,0.3,2.0,39,136
30,replace,5.0,c,N,COc1cN[H=-,COc1cc[H=-,10,replace N at position 5 with c,flow_matching,0.3,2.0,39,136
31,remove,1.0,O,,COc1cc[H=-,Cc1cc[H=-,9,remove O from position 1,flow_matching,0.3,2.0,39,136
32,add,8.0,[,,Cc1cc[H=-,Cc1cc[H=[-,10,add [ at position 8,flow_matching,0.3,2.0,39,136
33,add,2.0,N,,Cc1cc[H=[-,CcN1cc[H=[-,11,add N at position 2,flow_matching,0.3,2.0,39,136
34,add,4.0,3,,CcN1cc[H=[-,CcN13cc[H=[-,12,add 3 at position 4,flow_matching,0.3,2.0,39,136
35,remove,8.0,H,,CcN13cc[H=[-,CcN13cc[=[-,11,remove H from position 8,flow_matching,0.3,2.0,39,136
36,remove,10.0,-,,CcN13cc[=[-,CcN13cc[=[,10,remove - from position 10,flow_matching,0.3,2.0,39,136
37,add,2.0,/,,CcN13cc[=[,Cc/N13cc[=[,11,add / at position 2,flow_matching,0.3,2.0,39,136
38,remove,0.0,C,,Cc/N13cc[=[,c/N13cc[=[,10,remove C from position 0,flow_matching,0.3,2.0,39,136
39,replace,4.0,H,3,c/N13cc[=[,c/N1Hcc[=[,10,replace 3 at position 4 with H,flow_matching,0.3,2.0,39,136
40,replace,0.0,C,c,c/N1Hcc[=[,C/N1Hcc[=[,10,replace c at position 0 with C,flow_matching,0.3,2.0,39,136
41,replace,1.0,O,/,C/N1Hcc[=[,CON1Hcc[=[,10,replace / at position 1 with O,flow_matching,0.3,2.0,39,136
42,remove,9.0,[,,CON1Hcc[=[,CON1Hcc[=,9,remove [ from position 9,flow_matching,0.3,2.0,39,136
43,add,1.0,2,,CON1Hcc[=,C2ON1Hcc[=,10,add 2 at position 1,flow_matching,0.3,2.0,39,136
44,remove,3.0,N,,C2ON1Hcc[=,C2O1Hcc[=,9,remove N from position 3,flow_matching,0.3,2.0,39,136
45,add,5.0,B,,C2O1Hcc[=,C2O1HBcc[=,10,add B at position 5,flow_matching,0.3,2.0,39,136
46,remove,7.0,c,,C2O1HBcc[=,C2O1HBc[=,9,remove c from position 7,flow_matching,0.3,2.0,39,136
47,replace,1.0,O,2,C2O1HBc[=,COO1HBc[=,9,replace 2 at position 1 with O,flow_matching,0.3,2.0,39,136
48,replace,3.0,(,1,COO1HBc[=,COO(HBc[=,9,replace 1 at position 3 with (,flow_matching,0.3,2.0,39,136
49,replace,2.0,c,O,COO(HBc[=,COc(HBc[=,9,replace O at position 2 with c,flow_matching,0.3,2.0,39,136
50,remove,3.0,(,,COc(HBc[=,COcHBc[=,8,remove ( from position 3,flow_matching,0.3,2.0,39,136
51,replace,5.0,O,c,COcHBc[=,COcHBO[=,8,replace c at position 5 with O,flow_matching,0.3,2.0,39,136
52,replace,3.0,1,H,COcHBO[=,COc1BO[=,8,replace H at position 3 with 1,flow_matching,0.3,2.0,39,136
53,replace,4.0,c,B,COc1BO[=,COc1cO[=,8,replace B at position 4 with c,flow_matching,0.3,2.0,39,136
54,replace,2.0,N,c,COc1cO[=,CON1cO[=,8,replace c at position 2 with N,flow_matching,0.3,2.0,39,136
55,add,4.0,n,,CON1cO[=,CON1ncO[=,9,add n at position 4,flow_matching,0.3,2.0,39,136
56,replace,2.0,[,N,CON1ncO[=,CO[1ncO[=,9,replace N at position 2 with [,flow_matching,0.3,2.0,39,136
57,replace,2.0,c,[,CO[1ncO[=,COc1ncO[=,9,replace [ at position 2 with c,flow_matching,0.3,2.0,39,136
58,add,6.0,2,,COc1ncO[=,COc1nc2O[=,10,add 2 at position 6,flow_matching,0.3,2.0,39,136
59,add,6.0,s,,COc1nc2O[=,COc1ncs2O[=,11,add s at position 6,flow_matching,0.3,2.0,39,136
60,add,3.0,l,,COc1ncs2O[=,COcl1ncs2O[=,12,add l at position 3,flow_matching,0.3,2.0,39,136
61,add,10.0,s,,COcl1ncs2O[=,COcl1ncs2Os[=,13,add s at position 10,flow_matching,0.3,2.0,39,136
62,replace,3.0,1,l,COcl1ncs2Os[=,COc11ncs2Os[=,13,replace l at position 3 with 1,flow_matching,0.3,2.0,39,136
63,add,10.0,c,,COc11ncs2Os[=,COc11ncs2Ocs[=,14,add c at position 10,flow_matching,0.3,2.0,39,136
64,replace,12.0,O,[,COc11ncs2Ocs[=,COc11ncs2OcsO=,14,replace [ at position 12 with O,flow_matching,0.3,2.0,39,136
65,add,3.0,(,,COc11ncs2OcsO=,COc(11ncs2OcsO=,15,add ( at position 3,flow_matching,0.3,2.0,39,136
66,replace,14.0,#,=,COc(11ncs2OcsO=,COc(11ncs2OcsO#,15,replace = at position 14 with #,flow_matching,0.3,2.0,39,136
67,remove,5.0,1,,COc(11ncs2OcsO#,COc(1ncs2OcsO#,14,remove 1 from position 5,flow_matching,0.3,2.0,39,136
68,replace,3.0,1,(,COc(1ncs2OcsO#,COc11ncs2OcsO#,14,replace ( at position 3 with 1,flow_matching,0.3,2.0,39,136
69,replace,4.0,c,1,COc11ncs2OcsO#,COc1cncs2OcsO#,14,replace 1 at position 4 with c,flow_matching,0.3,2.0,39,136
70,remove,8.0,2,,COc1cncs2OcsO#,COc1cncsOcsO#,13,remove 2 from position 8,flow_matching,0.3,2.0,39,136
71,remove,8.0,O,,COc1cncsOcsO#,COc1cncscsO#,12,remove O from position 8,flow_matching,0.3,2.0,39,136
72,add,4.0,r,,COc1cncscsO#,COc1rcncscsO#,13,add r at position 4,flow_matching,0.3,2.0,39,136
73,add,4.0,#,,COc1rcncscsO#,COc1#rcncscsO#,14,add # at position 4,flow_matching,0.3,2.0,39,136
74,replace,4.0,c,#,COc1#rcncscsO#,COc1crcncscsO#,14,replace # at position 4 with c,flow_matching,0.3,2.0,39,136
75,replace,5.0,c,r,COc1crcncscsO#,COc1cccncscsO#,14,replace r at position 5 with c,flow_matching,0.3,2.0,39,136
76,replace,6.0,(,c,COc1cccncscsO#,COc1cc(ncscsO#,14,replace c at position 6 with (,flow_matching,0.3,2.0,39,136
77,add,7.0,H,,COc1cc(ncscsO#,COc1cc(HncscsO#,15,add H at position 7,flow_matching,0.3,2.0,39,136
78,replace,9.0,\,c,COc1cc(HncscsO#,COc1cc(Hn\scsO#,15,replace c at position 9 with \,flow_matching,0.3,2.0,39,136
79,replace,4.0,=,c,COc1cc(Hn\scsO#,COc1=c(Hn\scsO#,15,replace c at position 4 with =,flow_matching,0.3,2.0,39,136
80,replace,4.0,c,=,COc1=c(Hn\scsO#,COc1cc(Hn\scsO#,15,replace = at position 4 with c,flow_matching,0.3,2.0,39,136
81,remove,12.0,s,,COc1cc(Hn\scsO#,COc1cc(Hn\scO#,14,remove s from position 12,flow_matching,0.3,2.0,39,136
82,remove,2.0,c,,COc1cc(Hn\scO#,CO1cc(Hn\scO#,13,remove c from position 2,flow_matching,0.3,2.0,39,136
83,remove,12.0,#,,CO1cc(Hn\scO#,CO1cc(Hn\scO,12,remove # from position 12,flow_matching,0.3,2.0,39,136
84,add,5.0,\,,CO1cc(Hn\scO,CO1cc\(Hn\scO,13,add \ at position 5,flow_matching,0.3,2.0,39,136
85,remove,0.0,C,,CO1cc\(Hn\scO,O1cc\(Hn\scO,12,remove C from position 0,flow_matching,0.3,2.0,39,136
86,add,0.0,I,,O1cc\(Hn\scO,IO1cc\(Hn\scO,13,add I at position 0,flow_matching,0.3,2.0,39,136
87,replace,0.0,C,I,IO1cc\(Hn\scO,CO1cc\(Hn\scO,13,replace I at position 0 with C,flow_matching,0.3,2.0,39,136
88,replace,6.0,+,(,CO1cc\(Hn\scO,CO1cc\+Hn\scO,13,replace ( at position 6 with +,flow_matching,0.3,2.0,39,136
89,remove,0.0,C,,CO1cc\+Hn\scO,O1cc\+Hn\scO,12,remove C from position 0,flow_matching,0.3,2.0,39,136
90,add,1.0,1,,O1cc\+Hn\scO,O11cc\+Hn\scO,13,add 1 at position 1,flow_matching,0.3,2.0,39,136
91,replace,9.0,4,\,O11cc\+Hn\scO,O11cc\+Hn4scO,13,replace \ at position 9 with 4,flow_matching,0.3,2.0,39,136
92,replace,11.0,#,c,O11cc\+Hn4scO,O11cc\+Hn4s#O,13,replace c at position 11 with #,flow_matching,0.3,2.0,39,136
93,replace,6.0,],+,O11cc\+Hn4s#O,O11cc\]Hn4s#O,13,replace + at position 6 with ],flow_matching,0.3,2.0,39,136
94,replace,4.0,/,c,O11cc\]Hn4s#O,O11c/\]Hn4s#O,13,replace c at position 4 with /,flow_matching,0.3,2.0,39,136
95,replace,0.0,C,O,O11c/\]Hn4s#O,C11c/\]Hn4s#O,13,replace O at position 0 with C,flow_matching,0.3,2.0,39,136
96,replace,1.0,O,1,C11c/\]Hn4s#O,CO1c/\]Hn4s#O,13,replace 1 at position 1 with O,flow_matching,0.3,2.0,39,136
97,remove,7.0,H,,CO1c/\]Hn4s#O,CO1c/\]n4s#O,12,remove H from position 7,flow_matching,0.3,2.0,39,136
98,remove,1.0,O,,CO1c/\]n4s#O,C1c/\]n4s#O,11,remove O from position 1,flow_matching,0.3,2.0,39,136
99,add,9.0,@,,C1c/\]n4s#O,C1c/\]n4s@#O,12,add @ at position 9,flow_matching,0.3,2.0,39,136
100,replace,1.0,O,1,C1c/\]n4s@#O,COc/\]n4s@#O,12,replace 1 at position 1 with O,flow_matching,0.3,2.0,39,136
101,replace,3.0,1,/,COc/\]n4s@#O,COc1\]n4s@#O,12,replace / at position 3 with 1,flow_matching,0.3,2.0,39,136
102,replace,4.0,c,\,COc1\]n4s@#O,COc1c]n4s@#O,12,replace \ at position 4 with c,flow_matching,0.3,2.0,39,136
103,replace,5.0,c,],COc1c]n4s@#O,COc1ccn4s@#O,12,replace ] at position 5 with c,flow_matching,0.3,2.0,39,136
104,replace,6.0,(,n,COc1ccn4s@#O,COc1cc(4s@#O,12,replace n at position 6 with (,flow_matching,0.3,2.0,39,136
105,replace,7.0,-,4,COc1cc(4s@#O,COc1cc(-s@#O,12,replace 4 at position 7 with -,flow_matching,0.3,2.0,39,136
106,replace,8.0,c,s,COc1cc(-s@#O,COc1cc(-c@#O,12,replace s at position 8 with c,flow_matching,0.3,2.0,39,136
107,replace,9.0,2,@,COc1cc(-c@#O,COc1cc(-c2#O,12,replace @ at position 9 with 2,flow_matching,0.3,2.0,39,136
108,replace,10.0,c,#,COc1cc(-c2#O,COc1cc(-c2cO,12,replace # at position 10 with c,flow_matching,0.3,2.0,39,136
109,replace,11.0,c,O,COc1cc(-c2cO,COc1cc(-c2cc,12,replace O at position 11 with c,flow_matching,0.3,2.0,39,136
110,add,12.0,n,,COc1cc(-c2cc,COc1cc(-c2ccn,13,add n at position 12,flow_matching,0.3,2.0,39,136
111,add,13.0,o,,COc1cc(-c2ccn,COc1cc(-c2ccno,14,add o at position 13,flow_matching,0.3,2.0,39,136
112,add,14.0,2,,COc1cc(-c2ccno,COc1cc(-c2ccno2,15,add 2 at position 14,flow_matching,0.3,2.0,39,136
113,add,15.0,),,COc1cc(-c2ccno2,COc1cc(-c2ccno2),16,add ) at position 15,flow_matching,0.3,2.0,39,136
114,add,16.0,c,,COc1cc(-c2ccno2),COc1cc(-c2ccno2)c,17,add c at position 16,flow_matching,0.3,2.0,39,136
115,add,17.0,c,,COc1cc(-c2ccno2)c,COc1cc(-c2ccno2)cc,18,add c at position 17,flow_matching,0.3,2.0,39,136
116,add,18.0,c,,COc1cc(-c2ccno2)cc,COc1cc(-c2ccno2)ccc,19,add c at position 18,flow_matching,0.3,2.0,39,136
117,add,19.0,1,,COc1cc(-c2ccno2)ccc,COc1cc(-c2ccno2)ccc1,20,add 1 at position 19,flow_matching,0.3,2.0,39,136
118,add,20.0,S,,COc1cc(-c2ccno2)ccc1,COc1cc(-c2ccno2)ccc1S,21,add S at position 20,flow_matching,0.3,2.0,39,136
119,add,21.0,(,,COc1cc(-c2ccno2)ccc1S,COc1cc(-c2ccno2)ccc1S(,22,add ( at position 21,flow_matching,0.3,2.0,39,136
120,add,22.0,=,,COc1cc(-c2ccno2)ccc1S(,COc1cc(-c2ccno2)ccc1S(=,23,add = at position 22,flow_matching,0.3,2.0,39,136
121,add,23.0,O,,COc1cc(-c2ccno2)ccc1S(=,COc1cc(-c2ccno2)ccc1S(=O,24,add O at position 23,flow_matching,0.3,2.0,39,136
122,add,24.0,),,COc1cc(-c2ccno2)ccc1S(=O,COc1cc(-c2ccno2)ccc1S(=O),25,add ) at position 24,flow_matching,0.3,2.0,39,136
123,add,25.0,(,,COc1cc(-c2ccno2)ccc1S(=O),COc1cc(-c2ccno2)ccc1S(=O)(,26,add ( at position 25,flow_matching,0.3,2.0,39,136
124,add,26.0,=,,COc1cc(-c2ccno2)ccc1S(=O)(,COc1cc(-c2ccno2)ccc1S(=O)(=,27,add = at position 26,flow_matching,0.3,2.0,39,136
125,add,27.0,O,,COc1cc(-c2ccno2)ccc1S(=O)(=,COc1cc(-c2ccno2)ccc1S(=O)(=O,28,add O at position 27,flow_matching,0.3,2.0,39,136
126,add,28.0,),,COc1cc(-c2ccno2)ccc1S(=O)(=O,COc1cc(-c2ccno2)ccc1S(=O)(=O),29,add ) at position 28,flow_matching,0.3,2.0,39,136
127,add,29.0,N,,COc1cc(-c2ccno2)ccc1S(=O)(=O),COc1cc(-c2ccno2)ccc1S(=O)(=O)N,30,add N at position 29,flow_matching,0.3,2.0,39,136
128,add,30.0,C,,COc1cc(-c2ccno2)ccc1S(=O)(=O)N,COc1cc(-c2ccno2)ccc1S(=O)(=O)NC,31,add C at position 30,flow_matching,0.3,2.0,39,136
129,add,31.0,c,,COc1cc(-c2ccno2)ccc1S(=O)(=O)NC,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc,32,add c at position 31,flow_matching,0.3,2.0,39,136
130,add,32.0,1,,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1,33,add 1 at position 32,flow_matching,0.3,2.0,39,136
131,add,33.0,c,,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1c,34,add c at position 33,flow_matching,0.3,2.0,39,136
132,add,34.0,c,,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1c,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1cc,35,add c at position 34,flow_matching,0.3,2.0,39,136
133,add,35.0,c,,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1cc,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1ccc,36,add c at position 35,flow_matching,0.3,2.0,39,136
134,add,36.0,o,,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1ccc,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1ccco,37,add o at position 36,flow_matching,0.3,2.0,39,136
135,add,37.0,1,,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1ccco,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1ccco1,38,add 1 at position 37,flow_matching,0.3,2.0,39,136
136,add,38.0,"
",,COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1ccco1,"COc1cc(-c2ccno2)ccc1S(=O)(=O)NCc1ccco1
",39,"add 
 at position 38",flow_matching,0.3,2.0,39,136
