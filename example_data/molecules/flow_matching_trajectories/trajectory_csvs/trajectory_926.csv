step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,44,95
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,44,95
2,replace,0.0,r,C,C,r,1,replace <PERSON> at position 0 with r,flow_matching,0.3,2.0,44,95
3,remove,0.0,r,,r,,0,remove r from position 0,flow_matching,0.3,2.0,44,95
4,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,44,95
5,add,0.0,n,,3,n3,2,add n at position 0,flow_matching,0.3,2.0,44,95
6,add,2.0,H,,n3,n3H,3,add H at position 2,flow_matching,0.3,2.0,44,95
7,remove,2.0,H,,n3H,n3,2,remove H from position 2,flow_matching,0.3,2.0,44,95
8,replace,0.0,C,n,n3,C3,2,replace n at position 0 with C,flow_matching,0.3,2.0,44,95
9,replace,1.0,[,3,C3,C[,2,replace 3 at position 1 with [,flow_matching,0.3,2.0,44,95
10,replace,0.0,O,C,C[,O[,2,replace C at position 0 with O,flow_matching,0.3,2.0,44,95
11,remove,0.0,O,,O[,[,1,remove O from position 0,flow_matching,0.3,2.0,44,95
12,replace,0.0,C,[,[,C,1,replace [ at position 0 with C,flow_matching,0.3,2.0,44,95
13,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,44,95
14,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,44,95
15,remove,1.0,[,,C[C,CC,2,remove [ from position 1,flow_matching,0.3,2.0,44,95
16,replace,1.0,[,C,CC,C[,2,replace C at position 1 with [,flow_matching,0.3,2.0,44,95
17,replace,1.0,=,[,C[,C=,2,replace [ at position 1 with =,flow_matching,0.3,2.0,44,95
18,replace,1.0,[,=,C=,C[,2,replace = at position 1 with [,flow_matching,0.3,2.0,44,95
19,remove,1.0,[,,C[,C,1,remove [ from position 1,flow_matching,0.3,2.0,44,95
20,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,44,95
21,add,0.0,[,,,[,1,add [ at position 0,flow_matching,0.3,2.0,44,95
22,add,1.0,@,,[,[@,2,add @ at position 1,flow_matching,0.3,2.0,44,95
23,replace,0.0,C,[,[@,C@,2,replace [ at position 0 with C,flow_matching,0.3,2.0,44,95
24,add,2.0,N,,C@,C@N,3,add N at position 2,flow_matching,0.3,2.0,44,95
25,replace,1.0,[,@,C@N,C[N,3,replace @ at position 1 with [,flow_matching,0.3,2.0,44,95
26,replace,1.0,/,[,C[N,C/N,3,replace [ at position 1 with /,flow_matching,0.3,2.0,44,95
27,replace,2.0,l,N,C/N,C/l,3,replace N at position 2 with l,flow_matching,0.3,2.0,44,95
28,replace,1.0,[,/,C/l,C[l,3,replace / at position 1 with [,flow_matching,0.3,2.0,44,95
29,replace,2.0,C,l,C[l,C[C,3,replace l at position 2 with C,flow_matching,0.3,2.0,44,95
30,add,3.0,@,,C[C,C[C@,4,add @ at position 3,flow_matching,0.3,2.0,44,95
31,replace,0.0,2,C,C[C@,2[C@,4,replace C at position 0 with 2,flow_matching,0.3,2.0,44,95
32,remove,0.0,2,,2[C@,[C@,3,remove 2 from position 0,flow_matching,0.3,2.0,44,95
33,add,2.0,@,,[C@,[C@@,4,add @ at position 2,flow_matching,0.3,2.0,44,95
34,replace,0.0,C,[,[C@@,CC@@,4,replace [ at position 0 with C,flow_matching,0.3,2.0,44,95
35,add,3.0,(,,CC@@,CC@(@,5,add ( at position 3,flow_matching,0.3,2.0,44,95
36,replace,1.0,[,C,CC@(@,C[@(@,5,replace C at position 1 with [,flow_matching,0.3,2.0,44,95
37,add,2.0,I,,C[@(@,C[I@(@,6,add I at position 2,flow_matching,0.3,2.0,44,95
38,replace,2.0,2,I,C[I@(@,C[2@(@,6,replace I at position 2 with 2,flow_matching,0.3,2.0,44,95
39,replace,5.0,=,@,C[2@(@,C[2@(=,6,replace @ at position 5 with =,flow_matching,0.3,2.0,44,95
40,replace,1.0,l,[,C[2@(=,Cl2@(=,6,replace [ at position 1 with l,flow_matching,0.3,2.0,44,95
41,replace,1.0,[,l,Cl2@(=,C[2@(=,6,replace l at position 1 with [,flow_matching,0.3,2.0,44,95
42,replace,1.0,l,[,C[2@(=,Cl2@(=,6,replace [ at position 1 with l,flow_matching,0.3,2.0,44,95
43,add,5.0,n,,Cl2@(=,Cl2@(n=,7,add n at position 5,flow_matching,0.3,2.0,44,95
44,replace,1.0,[,l,Cl2@(n=,C[2@(n=,7,replace l at position 1 with [,flow_matching,0.3,2.0,44,95
45,replace,4.0,H,(,C[2@(n=,C[2@Hn=,7,replace ( at position 4 with H,flow_matching,0.3,2.0,44,95
46,remove,6.0,=,,C[2@Hn=,C[2@Hn,6,remove = from position 6,flow_matching,0.3,2.0,44,95
47,replace,2.0,C,2,C[2@Hn,C[C@Hn,6,replace 2 at position 2 with C,flow_matching,0.3,2.0,44,95
48,replace,1.0,s,[,C[C@Hn,CsC@Hn,6,replace [ at position 1 with s,flow_matching,0.3,2.0,44,95
49,replace,1.0,[,s,CsC@Hn,C[C@Hn,6,replace s at position 1 with [,flow_matching,0.3,2.0,44,95
50,add,6.0,s,,C[C@Hn,C[C@Hns,7,add s at position 6,flow_matching,0.3,2.0,44,95
51,replace,5.0,+,n,C[C@Hns,C[C@H+s,7,replace n at position 5 with +,flow_matching,0.3,2.0,44,95
52,remove,4.0,H,,C[C@H+s,C[C@+s,6,remove H from position 4,flow_matching,0.3,2.0,44,95
53,remove,2.0,C,,C[C@+s,C[@+s,5,remove C from position 2,flow_matching,0.3,2.0,44,95
54,replace,2.0,C,@,C[@+s,C[C+s,5,replace @ at position 2 with C,flow_matching,0.3,2.0,44,95
55,replace,3.0,@,+,C[C+s,C[C@s,5,replace + at position 3 with @,flow_matching,0.3,2.0,44,95
56,replace,4.0,H,s,C[C@s,C[C@H,5,replace s at position 4 with H,flow_matching,0.3,2.0,44,95
57,add,5.0,],,C[C@H,C[C@H],6,add ] at position 5,flow_matching,0.3,2.0,44,95
58,add,6.0,(,,C[C@H],C[C@H](,7,add ( at position 6,flow_matching,0.3,2.0,44,95
59,add,7.0,O,,C[C@H](,C[C@H](O,8,add O at position 7,flow_matching,0.3,2.0,44,95
60,add,8.0,C,,C[C@H](O,C[C@H](OC,9,add C at position 8,flow_matching,0.3,2.0,44,95
61,add,9.0,(,,C[C@H](OC,C[C@H](OC(,10,add ( at position 9,flow_matching,0.3,2.0,44,95
62,add,10.0,=,,C[C@H](OC(,C[C@H](OC(=,11,add = at position 10,flow_matching,0.3,2.0,44,95
63,add,11.0,O,,C[C@H](OC(=,C[C@H](OC(=O,12,add O at position 11,flow_matching,0.3,2.0,44,95
64,add,12.0,),,C[C@H](OC(=O,C[C@H](OC(=O),13,add ) at position 12,flow_matching,0.3,2.0,44,95
65,add,13.0,c,,C[C@H](OC(=O),C[C@H](OC(=O)c,14,add c at position 13,flow_matching,0.3,2.0,44,95
66,add,14.0,1,,C[C@H](OC(=O)c,C[C@H](OC(=O)c1,15,add 1 at position 14,flow_matching,0.3,2.0,44,95
67,add,15.0,c,,C[C@H](OC(=O)c1,C[C@H](OC(=O)c1c,16,add c at position 15,flow_matching,0.3,2.0,44,95
68,add,16.0,c,,C[C@H](OC(=O)c1c,C[C@H](OC(=O)c1cc,17,add c at position 16,flow_matching,0.3,2.0,44,95
69,add,17.0,c,,C[C@H](OC(=O)c1cc,C[C@H](OC(=O)c1ccc,18,add c at position 17,flow_matching,0.3,2.0,44,95
70,add,18.0,2,,C[C@H](OC(=O)c1ccc,C[C@H](OC(=O)c1ccc2,19,add 2 at position 18,flow_matching,0.3,2.0,44,95
71,add,19.0,c,,C[C@H](OC(=O)c1ccc2,C[C@H](OC(=O)c1ccc2c,20,add c at position 19,flow_matching,0.3,2.0,44,95
72,add,20.0,c,,C[C@H](OC(=O)c1ccc2c,C[C@H](OC(=O)c1ccc2cc,21,add c at position 20,flow_matching,0.3,2.0,44,95
73,add,21.0,c,,C[C@H](OC(=O)c1ccc2cc,C[C@H](OC(=O)c1ccc2ccc,22,add c at position 21,flow_matching,0.3,2.0,44,95
74,add,22.0,c,,C[C@H](OC(=O)c1ccc2ccc,C[C@H](OC(=O)c1ccc2cccc,23,add c at position 22,flow_matching,0.3,2.0,44,95
75,add,23.0,c,,C[C@H](OC(=O)c1ccc2cccc,C[C@H](OC(=O)c1ccc2ccccc,24,add c at position 23,flow_matching,0.3,2.0,44,95
76,add,24.0,2,,C[C@H](OC(=O)c1ccc2ccccc,C[C@H](OC(=O)c1ccc2ccccc2,25,add 2 at position 24,flow_matching,0.3,2.0,44,95
77,add,25.0,n,,C[C@H](OC(=O)c1ccc2ccccc2,C[C@H](OC(=O)c1ccc2ccccc2n,26,add n at position 25,flow_matching,0.3,2.0,44,95
78,add,26.0,1,,C[C@H](OC(=O)c1ccc2ccccc2n,C[C@H](OC(=O)c1ccc2ccccc2n1,27,add 1 at position 26,flow_matching,0.3,2.0,44,95
79,add,27.0,),,C[C@H](OC(=O)c1ccc2ccccc2n1,C[C@H](OC(=O)c1ccc2ccccc2n1),28,add ) at position 27,flow_matching,0.3,2.0,44,95
80,add,28.0,C,,C[C@H](OC(=O)c1ccc2ccccc2n1),C[C@H](OC(=O)c1ccc2ccccc2n1)C,29,add C at position 28,flow_matching,0.3,2.0,44,95
81,add,29.0,(,,C[C@H](OC(=O)c1ccc2ccccc2n1)C,C[C@H](OC(=O)c1ccc2ccccc2n1)C(,30,add ( at position 29,flow_matching,0.3,2.0,44,95
82,add,30.0,=,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=,31,add = at position 30,flow_matching,0.3,2.0,44,95
83,add,31.0,O,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O,32,add O at position 31,flow_matching,0.3,2.0,44,95
84,add,32.0,),,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O),33,add ) at position 32,flow_matching,0.3,2.0,44,95
85,add,33.0,N,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O),C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)N,34,add N at position 33,flow_matching,0.3,2.0,44,95
86,add,34.0,C,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)N,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NC,35,add C at position 34,flow_matching,0.3,2.0,44,95
87,add,35.0,C,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NC,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC,36,add C at position 35,flow_matching,0.3,2.0,44,95
88,add,36.0,1,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1,37,add 1 at position 36,flow_matching,0.3,2.0,44,95
89,add,37.0,C,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1C,38,add C at position 37,flow_matching,0.3,2.0,44,95
90,add,38.0,C,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1C,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CC,39,add C at position 38,flow_matching,0.3,2.0,44,95
91,add,39.0,C,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CC,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CCC,40,add C at position 39,flow_matching,0.3,2.0,44,95
92,add,40.0,C,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CCC,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CCCC,41,add C at position 40,flow_matching,0.3,2.0,44,95
93,add,41.0,C,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CCCC,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CCCCC,42,add C at position 41,flow_matching,0.3,2.0,44,95
94,add,42.0,1,,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CCCCC,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CCCCC1,43,add 1 at position 42,flow_matching,0.3,2.0,44,95
95,add,43.0,"
",,C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CCCCC1,"C[C@H](OC(=O)c1ccc2ccccc2n1)C(=O)NCC1CCCCC1
",44,"add 
 at position 43",flow_matching,0.3,2.0,44,95
