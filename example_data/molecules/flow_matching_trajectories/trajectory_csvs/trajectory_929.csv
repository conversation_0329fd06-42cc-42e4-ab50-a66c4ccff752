step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,33,70
1,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,33,70
2,replace,0.0,C,/,/,C,1,replace / at position 0 with C,flow_matching,0.3,2.0,33,70
3,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,33,70
4,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,33,70
5,remove,0.0,N,,N,,0,remove N from position 0,flow_matching,0.3,2.0,33,70
6,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,33,70
7,replace,0.0,],C,C,],1,replace C at position 0 with ],flow_matching,0.3,2.0,33,70
8,replace,0.0,5,],],5,1,replace ] at position 0 with 5,flow_matching,0.3,2.0,33,70
9,replace,0.0,C,5,5,C,1,replace 5 at position 0 with C,flow_matching,0.3,2.0,33,70
10,add,1.0,(,,C,C(,2,add ( at position 1,flow_matching,0.3,2.0,33,70
11,remove,1.0,(,,C(,C,1,remove ( from position 1,flow_matching,0.3,2.0,33,70
12,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,33,70
13,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,33,70
14,remove,0.0,],,],,0,remove ] from position 0,flow_matching,0.3,2.0,33,70
15,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,33,70
16,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,33,70
17,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,33,70
18,add,2.0,C,,CC,CCC,3,add C at position 2,flow_matching,0.3,2.0,33,70
19,remove,0.0,C,,CCC,CC,2,remove C from position 0,flow_matching,0.3,2.0,33,70
20,add,2.0,C,,CC,CCC,3,add C at position 2,flow_matching,0.3,2.0,33,70
21,add,3.0,[,,CCC,CCC[,4,add [ at position 3,flow_matching,0.3,2.0,33,70
22,add,4.0,C,,CCC[,CCC[C,5,add C at position 4,flow_matching,0.3,2.0,33,70
23,add,2.0,+,,CCC[C,CC+C[C,6,add + at position 2,flow_matching,0.3,2.0,33,70
24,add,0.0,=,,CC+C[C,=CC+C[C,7,add = at position 0,flow_matching,0.3,2.0,33,70
25,replace,0.0,C,=,=CC+C[C,CCC+C[C,7,replace = at position 0 with C,flow_matching,0.3,2.0,33,70
26,remove,4.0,C,,CCC+C[C,CCC+[C,6,remove C from position 4,flow_matching,0.3,2.0,33,70
27,remove,2.0,C,,CCC+[C,CC+[C,5,remove C from position 2,flow_matching,0.3,2.0,33,70
28,replace,2.0,C,+,CC+[C,CCC[C,5,replace + at position 2 with C,flow_matching,0.3,2.0,33,70
29,add,2.0,\,,CCC[C,CC\C[C,6,add \ at position 2,flow_matching,0.3,2.0,33,70
30,replace,5.0,3,C,CC\C[C,CC\C[3,6,replace C at position 5 with 3,flow_matching,0.3,2.0,33,70
31,remove,1.0,C,,CC\C[3,C\C[3,5,remove C from position 1,flow_matching,0.3,2.0,33,70
32,replace,1.0,C,\,C\C[3,CCC[3,5,replace \ at position 1 with C,flow_matching,0.3,2.0,33,70
33,replace,0.0,-,C,CCC[3,-CC[3,5,replace C at position 0 with -,flow_matching,0.3,2.0,33,70
34,replace,1.0,N,C,-CC[3,-NC[3,5,replace C at position 1 with N,flow_matching,0.3,2.0,33,70
35,add,1.0,S,,-NC[3,-SNC[3,6,add S at position 1,flow_matching,0.3,2.0,33,70
36,add,3.0,B,,-SNC[3,-SNBC[3,7,add B at position 3,flow_matching,0.3,2.0,33,70
37,remove,3.0,B,,-SNBC[3,-SNC[3,6,remove B from position 3,flow_matching,0.3,2.0,33,70
38,replace,0.0,C,-,-SNC[3,CSNC[3,6,replace - at position 0 with C,flow_matching,0.3,2.0,33,70
39,replace,1.0,C,S,CSNC[3,CCNC[3,6,replace S at position 1 with C,flow_matching,0.3,2.0,33,70
40,replace,2.0,C,N,CCNC[3,CCCC[3,6,replace N at position 2 with C,flow_matching,0.3,2.0,33,70
41,replace,3.0,[,C,CCCC[3,CCC[[3,6,replace C at position 3 with [,flow_matching,0.3,2.0,33,70
42,replace,4.0,C,[,CCC[[3,CCC[C3,6,replace [ at position 4 with C,flow_matching,0.3,2.0,33,70
43,replace,5.0,@,3,CCC[C3,CCC[C@,6,replace 3 at position 5 with @,flow_matching,0.3,2.0,33,70
44,add,6.0,@,,CCC[C@,CCC[C@@,7,add @ at position 6,flow_matching,0.3,2.0,33,70
45,add,7.0,H,,CCC[C@@,CCC[C@@H,8,add H at position 7,flow_matching,0.3,2.0,33,70
46,add,8.0,],,CCC[C@@H,CCC[C@@H],9,add ] at position 8,flow_matching,0.3,2.0,33,70
47,add,9.0,(,,CCC[C@@H],CCC[C@@H](,10,add ( at position 9,flow_matching,0.3,2.0,33,70
48,add,10.0,C,,CCC[C@@H](,CCC[C@@H](C,11,add C at position 10,flow_matching,0.3,2.0,33,70
49,add,11.0,C,,CCC[C@@H](C,CCC[C@@H](CC,12,add C at position 11,flow_matching,0.3,2.0,33,70
50,add,12.0,),,CCC[C@@H](CC,CCC[C@@H](CC),13,add ) at position 12,flow_matching,0.3,2.0,33,70
51,add,13.0,N,,CCC[C@@H](CC),CCC[C@@H](CC)N,14,add N at position 13,flow_matching,0.3,2.0,33,70
52,add,14.0,c,,CCC[C@@H](CC)N,CCC[C@@H](CC)Nc,15,add c at position 14,flow_matching,0.3,2.0,33,70
53,add,15.0,1,,CCC[C@@H](CC)Nc,CCC[C@@H](CC)Nc1,16,add 1 at position 15,flow_matching,0.3,2.0,33,70
54,add,16.0,c,,CCC[C@@H](CC)Nc1,CCC[C@@H](CC)Nc1c,17,add c at position 16,flow_matching,0.3,2.0,33,70
55,add,17.0,(,,CCC[C@@H](CC)Nc1c,CCC[C@@H](CC)Nc1c(,18,add ( at position 17,flow_matching,0.3,2.0,33,70
56,add,18.0,F,,CCC[C@@H](CC)Nc1c(,CCC[C@@H](CC)Nc1c(F,19,add F at position 18,flow_matching,0.3,2.0,33,70
57,add,19.0,),,CCC[C@@H](CC)Nc1c(F,CCC[C@@H](CC)Nc1c(F),20,add ) at position 19,flow_matching,0.3,2.0,33,70
58,add,20.0,c,,CCC[C@@H](CC)Nc1c(F),CCC[C@@H](CC)Nc1c(F)c,21,add c at position 20,flow_matching,0.3,2.0,33,70
59,add,21.0,(,,CCC[C@@H](CC)Nc1c(F)c,CCC[C@@H](CC)Nc1c(F)c(,22,add ( at position 21,flow_matching,0.3,2.0,33,70
60,add,22.0,F,,CCC[C@@H](CC)Nc1c(F)c(,CCC[C@@H](CC)Nc1c(F)c(F,23,add F at position 22,flow_matching,0.3,2.0,33,70
61,add,23.0,),,CCC[C@@H](CC)Nc1c(F)c(F,CCC[C@@H](CC)Nc1c(F)c(F),24,add ) at position 23,flow_matching,0.3,2.0,33,70
62,add,24.0,n,,CCC[C@@H](CC)Nc1c(F)c(F),CCC[C@@H](CC)Nc1c(F)c(F)n,25,add n at position 24,flow_matching,0.3,2.0,33,70
63,add,25.0,c,,CCC[C@@H](CC)Nc1c(F)c(F)n,CCC[C@@H](CC)Nc1c(F)c(F)nc,26,add c at position 25,flow_matching,0.3,2.0,33,70
64,add,26.0,(,,CCC[C@@H](CC)Nc1c(F)c(F)nc,CCC[C@@H](CC)Nc1c(F)c(F)nc(,27,add ( at position 26,flow_matching,0.3,2.0,33,70
65,add,27.0,F,,CCC[C@@H](CC)Nc1c(F)c(F)nc(,CCC[C@@H](CC)Nc1c(F)c(F)nc(F,28,add F at position 27,flow_matching,0.3,2.0,33,70
66,add,28.0,),,CCC[C@@H](CC)Nc1c(F)c(F)nc(F,CCC[C@@H](CC)Nc1c(F)c(F)nc(F),29,add ) at position 28,flow_matching,0.3,2.0,33,70
67,add,29.0,c,,CCC[C@@H](CC)Nc1c(F)c(F)nc(F),CCC[C@@H](CC)Nc1c(F)c(F)nc(F)c,30,add c at position 29,flow_matching,0.3,2.0,33,70
68,add,30.0,1,,CCC[C@@H](CC)Nc1c(F)c(F)nc(F)c,CCC[C@@H](CC)Nc1c(F)c(F)nc(F)c1,31,add 1 at position 30,flow_matching,0.3,2.0,33,70
69,add,31.0,F,,CCC[C@@H](CC)Nc1c(F)c(F)nc(F)c1,CCC[C@@H](CC)Nc1c(F)c(F)nc(F)c1F,32,add F at position 31,flow_matching,0.3,2.0,33,70
70,add,32.0,"
",,CCC[C@@H](CC)Nc1c(F)c(F)nc(F)c1F,"CCC[C@@H](CC)Nc1c(F)c(F)nc(F)c1F
",33,"add 
 at position 32",flow_matching,0.3,2.0,33,70
