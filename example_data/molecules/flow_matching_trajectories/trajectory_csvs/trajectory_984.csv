step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,42,105
1,add,0.0,(,,,(,1,add ( at position 0,flow_matching,0.3,2.0,42,105
2,replace,0.0,C,(,(,C,1,replace ( at position 0 with C,flow_matching,0.3,2.0,42,105
3,replace,0.0,o,C,C,o,1,replace C at position 0 with o,flow_matching,0.3,2.0,42,105
4,replace,0.0,C,o,o,C,1,replace o at position 0 with C,flow_matching,0.3,2.0,42,105
5,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,42,105
6,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,42,105
7,replace,0.0,7,C,COc,7Oc,3,replace <PERSON> at position 0 with 7,flow_matching,0.3,2.0,42,105
8,remove,1.0,O,,7Oc,7c,2,remove O from position 1,flow_matching,0.3,2.0,42,105
9,remove,1.0,c,,7c,7,1,remove c from position 1,flow_matching,0.3,2.0,42,105
10,remove,0.0,7,,7,,0,remove 7 from position 0,flow_matching,0.3,2.0,42,105
11,add,0.0,o,,,o,1,add o at position 0,flow_matching,0.3,2.0,42,105
12,replace,0.0,],o,o,],1,replace o at position 0 with ],flow_matching,0.3,2.0,42,105
13,remove,0.0,],,],,0,remove ] from position 0,flow_matching,0.3,2.0,42,105
14,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,42,105
15,add,0.0,3,,C,3C,2,add 3 at position 0,flow_matching,0.3,2.0,42,105
16,replace,0.0,C,3,3C,CC,2,replace 3 at position 0 with C,flow_matching,0.3,2.0,42,105
17,remove,0.0,C,,CC,C,1,remove C from position 0,flow_matching,0.3,2.0,42,105
18,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,42,105
19,replace,1.0,N,O,CO,CN,2,replace O at position 1 with N,flow_matching,0.3,2.0,42,105
20,replace,0.0,(,C,CN,(N,2,replace C at position 0 with (,flow_matching,0.3,2.0,42,105
21,remove,0.0,(,,(N,N,1,remove ( from position 0,flow_matching,0.3,2.0,42,105
22,replace,0.0,C,N,N,C,1,replace N at position 0 with C,flow_matching,0.3,2.0,42,105
23,add,0.0,3,,C,3C,2,add 3 at position 0,flow_matching,0.3,2.0,42,105
24,remove,0.0,3,,3C,C,1,remove 3 from position 0,flow_matching,0.3,2.0,42,105
25,add,0.0,S,,C,SC,2,add S at position 0,flow_matching,0.3,2.0,42,105
26,remove,1.0,C,,SC,S,1,remove C from position 1,flow_matching,0.3,2.0,42,105
27,add,1.0,(,,S,S(,2,add ( at position 1,flow_matching,0.3,2.0,42,105
28,remove,1.0,(,,S(,S,1,remove ( from position 1,flow_matching,0.3,2.0,42,105
29,remove,0.0,S,,S,,0,remove S from position 0,flow_matching,0.3,2.0,42,105
30,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,42,105
31,replace,0.0,O,C,C,O,1,replace C at position 0 with O,flow_matching,0.3,2.0,42,105
32,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,42,105
33,add,0.0,4,,,4,1,add 4 at position 0,flow_matching,0.3,2.0,42,105
34,replace,0.0,H,4,4,H,1,replace 4 at position 0 with H,flow_matching,0.3,2.0,42,105
35,remove,0.0,H,,H,,0,remove H from position 0,flow_matching,0.3,2.0,42,105
36,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,42,105
37,add,0.0,1,,I,1I,2,add 1 at position 0,flow_matching,0.3,2.0,42,105
38,remove,1.0,I,,1I,1,1,remove I from position 1,flow_matching,0.3,2.0,42,105
39,add,0.0,4,,1,41,2,add 4 at position 0,flow_matching,0.3,2.0,42,105
40,replace,0.0,C,4,41,C1,2,replace 4 at position 0 with C,flow_matching,0.3,2.0,42,105
41,replace,1.0,\,1,C1,C\,2,replace 1 at position 1 with \,flow_matching,0.3,2.0,42,105
42,replace,1.0,O,\,C\,CO,2,replace \ at position 1 with O,flow_matching,0.3,2.0,42,105
43,add,0.0,n,,CO,nCO,3,add n at position 0,flow_matching,0.3,2.0,42,105
44,remove,2.0,O,,nCO,nC,2,remove O from position 2,flow_matching,0.3,2.0,42,105
45,replace,1.0,-,C,nC,n-,2,replace C at position 1 with -,flow_matching,0.3,2.0,42,105
46,replace,0.0,C,n,n-,C-,2,replace n at position 0 with C,flow_matching,0.3,2.0,42,105
47,remove,1.0,-,,C-,C,1,remove - from position 1,flow_matching,0.3,2.0,42,105
48,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,42,105
49,add,0.0,S,,,S,1,add S at position 0,flow_matching,0.3,2.0,42,105
50,replace,0.0,C,S,S,C,1,replace S at position 0 with C,flow_matching,0.3,2.0,42,105
51,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,42,105
52,add,1.0,O,,CO,COO,3,add O at position 1,flow_matching,0.3,2.0,42,105
53,remove,2.0,O,,COO,CO,2,remove O from position 2,flow_matching,0.3,2.0,42,105
54,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,42,105
55,replace,0.0,3,C,COc,3Oc,3,replace C at position 0 with 3,flow_matching,0.3,2.0,42,105
56,remove,0.0,3,,3Oc,Oc,2,remove 3 from position 0,flow_matching,0.3,2.0,42,105
57,replace,0.0,C,O,Oc,Cc,2,replace O at position 0 with C,flow_matching,0.3,2.0,42,105
58,remove,0.0,C,,Cc,c,1,remove C from position 0,flow_matching,0.3,2.0,42,105
59,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,42,105
60,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,42,105
61,add,0.0,(,,CO,(CO,3,add ( at position 0,flow_matching,0.3,2.0,42,105
62,remove,0.0,(,,(CO,CO,2,remove ( from position 0,flow_matching,0.3,2.0,42,105
63,add,0.0,O,,CO,OCO,3,add O at position 0,flow_matching,0.3,2.0,42,105
64,replace,0.0,C,O,OCO,CCO,3,replace O at position 0 with C,flow_matching,0.3,2.0,42,105
65,replace,1.0,O,C,CCO,COO,3,replace C at position 1 with O,flow_matching,0.3,2.0,42,105
66,replace,2.0,c,O,COO,COc,3,replace O at position 2 with c,flow_matching,0.3,2.0,42,105
67,add,3.0,1,,COc,COc1,4,add 1 at position 3,flow_matching,0.3,2.0,42,105
68,add,4.0,c,,COc1,COc1c,5,add c at position 4,flow_matching,0.3,2.0,42,105
69,add,5.0,c,,COc1c,COc1cc,6,add c at position 5,flow_matching,0.3,2.0,42,105
70,add,6.0,c,,COc1cc,COc1ccc,7,add c at position 6,flow_matching,0.3,2.0,42,105
71,add,7.0,(,,COc1ccc,COc1ccc(,8,add ( at position 7,flow_matching,0.3,2.0,42,105
72,add,8.0,C,,COc1ccc(,COc1ccc(C,9,add C at position 8,flow_matching,0.3,2.0,42,105
73,add,9.0,N,,COc1ccc(C,COc1ccc(CN,10,add N at position 9,flow_matching,0.3,2.0,42,105
74,add,10.0,C,,COc1ccc(CN,COc1ccc(CNC,11,add C at position 10,flow_matching,0.3,2.0,42,105
75,add,11.0,(,,COc1ccc(CNC,COc1ccc(CNC(,12,add ( at position 11,flow_matching,0.3,2.0,42,105
76,add,12.0,=,,COc1ccc(CNC(,COc1ccc(CNC(=,13,add = at position 12,flow_matching,0.3,2.0,42,105
77,add,13.0,O,,COc1ccc(CNC(=,COc1ccc(CNC(=O,14,add O at position 13,flow_matching,0.3,2.0,42,105
78,add,14.0,),,COc1ccc(CNC(=O,COc1ccc(CNC(=O),15,add ) at position 14,flow_matching,0.3,2.0,42,105
79,add,15.0,c,,COc1ccc(CNC(=O),COc1ccc(CNC(=O)c,16,add c at position 15,flow_matching,0.3,2.0,42,105
80,add,16.0,2,,COc1ccc(CNC(=O)c,COc1ccc(CNC(=O)c2,17,add 2 at position 16,flow_matching,0.3,2.0,42,105
81,add,17.0,c,,COc1ccc(CNC(=O)c2,COc1ccc(CNC(=O)c2c,18,add c at position 17,flow_matching,0.3,2.0,42,105
82,add,18.0,c,,COc1ccc(CNC(=O)c2c,COc1ccc(CNC(=O)c2cc,19,add c at position 18,flow_matching,0.3,2.0,42,105
83,add,19.0,(,,COc1ccc(CNC(=O)c2cc,COc1ccc(CNC(=O)c2cc(,20,add ( at position 19,flow_matching,0.3,2.0,42,105
84,add,20.0,=,,COc1ccc(CNC(=O)c2cc(,COc1ccc(CNC(=O)c2cc(=,21,add = at position 20,flow_matching,0.3,2.0,42,105
85,add,21.0,O,,COc1ccc(CNC(=O)c2cc(=,COc1ccc(CNC(=O)c2cc(=O,22,add O at position 21,flow_matching,0.3,2.0,42,105
86,add,22.0,),,COc1ccc(CNC(=O)c2cc(=O,COc1ccc(CNC(=O)c2cc(=O),23,add ) at position 22,flow_matching,0.3,2.0,42,105
87,add,23.0,c,,COc1ccc(CNC(=O)c2cc(=O),COc1ccc(CNC(=O)c2cc(=O)c,24,add c at position 23,flow_matching,0.3,2.0,42,105
88,add,24.0,3,,COc1ccc(CNC(=O)c2cc(=O)c,COc1ccc(CNC(=O)c2cc(=O)c3,25,add 3 at position 24,flow_matching,0.3,2.0,42,105
89,add,25.0,c,,COc1ccc(CNC(=O)c2cc(=O)c3,COc1ccc(CNC(=O)c2cc(=O)c3c,26,add c at position 25,flow_matching,0.3,2.0,42,105
90,add,26.0,c,,COc1ccc(CNC(=O)c2cc(=O)c3c,COc1ccc(CNC(=O)c2cc(=O)c3cc,27,add c at position 26,flow_matching,0.3,2.0,42,105
91,add,27.0,c,,COc1ccc(CNC(=O)c2cc(=O)c3cc,COc1ccc(CNC(=O)c2cc(=O)c3ccc,28,add c at position 27,flow_matching,0.3,2.0,42,105
92,add,28.0,(,,COc1ccc(CNC(=O)c2cc(=O)c3ccc,COc1ccc(CNC(=O)c2cc(=O)c3ccc(,29,add ( at position 28,flow_matching,0.3,2.0,42,105
93,add,29.0,B,,COc1ccc(CNC(=O)c2cc(=O)c3ccc(,COc1ccc(CNC(=O)c2cc(=O)c3ccc(B,30,add B at position 29,flow_matching,0.3,2.0,42,105
94,add,30.0,r,,COc1ccc(CNC(=O)c2cc(=O)c3ccc(B,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br,31,add r at position 30,flow_matching,0.3,2.0,42,105
95,add,31.0,),,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br),32,add ) at position 31,flow_matching,0.3,2.0,42,105
96,add,32.0,c,,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br),COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)c,33,add c at position 32,flow_matching,0.3,2.0,42,105
97,add,33.0,c,,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)c,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc,34,add c at position 33,flow_matching,0.3,2.0,42,105
98,add,34.0,3,,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3,35,add 3 at position 34,flow_matching,0.3,2.0,42,105
99,add,35.0,o,,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o,36,add o at position 35,flow_matching,0.3,2.0,42,105
100,add,36.0,2,,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2,37,add 2 at position 36,flow_matching,0.3,2.0,42,105
101,add,37.0,),,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2),38,add ) at position 37,flow_matching,0.3,2.0,42,105
102,add,38.0,c,,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2),COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2)c,39,add c at position 38,flow_matching,0.3,2.0,42,105
103,add,39.0,c,,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2)c,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2)cc,40,add c at position 39,flow_matching,0.3,2.0,42,105
104,add,40.0,1,,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2)cc,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2)cc1,41,add 1 at position 40,flow_matching,0.3,2.0,42,105
105,add,41.0,"
",,COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2)cc1,"COc1ccc(CNC(=O)c2cc(=O)c3ccc(Br)cc3o2)cc1
",42,"add 
 at position 41",flow_matching,0.3,2.0,42,105
