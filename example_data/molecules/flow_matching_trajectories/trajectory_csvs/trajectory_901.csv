step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,64,257
1,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,64,257
2,remove,0.0,+,,+,,0,remove + from position 0,flow_matching,0.3,2.0,64,257
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,64,257
4,add,0.0,I,,C,IC,2,add I at position 0,flow_matching,0.3,2.0,64,257
5,replace,1.0,o,C,IC,Io,2,replace <PERSON> at position 1 with o,flow_matching,0.3,2.0,64,257
6,add,1.0,5,,Io,I5o,3,add 5 at position 1,flow_matching,0.3,2.0,64,257
7,replace,0.0,C,I,I5o,C5o,3,replace I at position 0 with C,flow_matching,0.3,2.0,64,257
8,replace,1.0,c,5,C5o,Cco,3,replace 5 at position 1 with c,flow_matching,0.3,2.0,64,257
9,replace,1.0,7,c,Cco,C7o,3,replace c at position 1 with 7,flow_matching,0.3,2.0,64,257
10,remove,1.0,7,,C7o,Co,2,remove 7 from position 1,flow_matching,0.3,2.0,64,257
11,add,2.0,1,,Co,Co1,3,add 1 at position 2,flow_matching,0.3,2.0,64,257
12,replace,1.0,c,o,Co1,Cc1,3,replace o at position 1 with c,flow_matching,0.3,2.0,64,257
13,replace,1.0,N,c,Cc1,CN1,3,replace c at position 1 with N,flow_matching,0.3,2.0,64,257
14,replace,1.0,c,N,CN1,Cc1,3,replace N at position 1 with c,flow_matching,0.3,2.0,64,257
15,remove,2.0,1,,Cc1,Cc,2,remove 1 from position 2,flow_matching,0.3,2.0,64,257
16,add,0.0,),,Cc,)Cc,3,add ) at position 0,flow_matching,0.3,2.0,64,257
17,add,1.0,),,)Cc,))Cc,4,add ) at position 1,flow_matching,0.3,2.0,64,257
18,replace,0.0,C,),))Cc,C)Cc,4,replace ) at position 0 with C,flow_matching,0.3,2.0,64,257
19,add,1.0,+,,C)Cc,C+)Cc,5,add + at position 1,flow_matching,0.3,2.0,64,257
20,replace,1.0,=,+,C+)Cc,C=)Cc,5,replace + at position 1 with =,flow_matching,0.3,2.0,64,257
21,replace,0.0,n,C,C=)Cc,n=)Cc,5,replace C at position 0 with n,flow_matching,0.3,2.0,64,257
22,replace,1.0,I,=,n=)Cc,nI)Cc,5,replace = at position 1 with I,flow_matching,0.3,2.0,64,257
23,replace,0.0,C,n,nI)Cc,CI)Cc,5,replace n at position 0 with C,flow_matching,0.3,2.0,64,257
24,remove,1.0,I,,CI)Cc,C)Cc,4,remove I from position 1,flow_matching,0.3,2.0,64,257
25,replace,1.0,c,),C)Cc,CcCc,4,replace ) at position 1 with c,flow_matching,0.3,2.0,64,257
26,replace,1.0,O,c,CcCc,COCc,4,replace c at position 1 with O,flow_matching,0.3,2.0,64,257
27,add,2.0,#,,COCc,CO#Cc,5,add # at position 2,flow_matching,0.3,2.0,64,257
28,replace,3.0,3,C,CO#Cc,CO#3c,5,replace C at position 3 with 3,flow_matching,0.3,2.0,64,257
29,replace,1.0,/,O,CO#3c,C/#3c,5,replace O at position 1 with /,flow_matching,0.3,2.0,64,257
30,replace,1.0,c,/,C/#3c,Cc#3c,5,replace / at position 1 with c,flow_matching,0.3,2.0,64,257
31,replace,2.0,1,#,Cc#3c,Cc13c,5,replace # at position 2 with 1,flow_matching,0.3,2.0,64,257
32,replace,3.0,c,3,Cc13c,Cc1cc,5,replace 3 at position 3 with c,flow_matching,0.3,2.0,64,257
33,replace,4.0,],c,Cc1cc,Cc1c],5,replace c at position 4 with ],flow_matching,0.3,2.0,64,257
34,replace,0.0,r,C,Cc1c],rc1c],5,replace C at position 0 with r,flow_matching,0.3,2.0,64,257
35,replace,0.0,C,r,rc1c],Cc1c],5,replace r at position 0 with C,flow_matching,0.3,2.0,64,257
36,replace,4.0,c,],Cc1c],Cc1cc,5,replace ] at position 4 with c,flow_matching,0.3,2.0,64,257
37,add,5.0,c,,Cc1cc,Cc1ccc,6,add c at position 5,flow_matching,0.3,2.0,64,257
38,replace,4.0,B,c,Cc1ccc,Cc1cBc,6,replace c at position 4 with B,flow_matching,0.3,2.0,64,257
39,remove,1.0,c,,Cc1cBc,C1cBc,5,remove c from position 1,flow_matching,0.3,2.0,64,257
40,add,4.0,7,,C1cBc,C1cB7c,6,add 7 at position 4,flow_matching,0.3,2.0,64,257
41,remove,0.0,C,,C1cB7c,1cB7c,5,remove C from position 0,flow_matching,0.3,2.0,64,257
42,replace,3.0,),7,1cB7c,1cB)c,5,replace 7 at position 3 with ),flow_matching,0.3,2.0,64,257
43,replace,0.0,C,1,1cB)c,CcB)c,5,replace 1 at position 0 with C,flow_matching,0.3,2.0,64,257
44,add,3.0,C,,CcB)c,CcBC)c,6,add C at position 3,flow_matching,0.3,2.0,64,257
45,add,0.0,],,CcBC)c,]CcBC)c,7,add ] at position 0,flow_matching,0.3,2.0,64,257
46,replace,0.0,C,],]CcBC)c,CCcBC)c,7,replace ] at position 0 with C,flow_matching,0.3,2.0,64,257
47,replace,4.0,/,C,CCcBC)c,CCcB/)c,7,replace C at position 4 with /,flow_matching,0.3,2.0,64,257
48,add,4.0,B,,CCcB/)c,CCcBB/)c,8,add B at position 4,flow_matching,0.3,2.0,64,257
49,replace,4.0,c,B,CCcBB/)c,CCcBc/)c,8,replace B at position 4 with c,flow_matching,0.3,2.0,64,257
50,add,7.0,l,,CCcBc/)c,CCcBc/)lc,9,add l at position 7,flow_matching,0.3,2.0,64,257
51,add,0.0,#,,CCcBc/)lc,#CCcBc/)lc,10,add # at position 0,flow_matching,0.3,2.0,64,257
52,replace,0.0,C,#,#CCcBc/)lc,CCCcBc/)lc,10,replace # at position 0 with C,flow_matching,0.3,2.0,64,257
53,replace,1.0,c,C,CCCcBc/)lc,CcCcBc/)lc,10,replace C at position 1 with c,flow_matching,0.3,2.0,64,257
54,add,0.0,+,,CcCcBc/)lc,+CcCcBc/)lc,11,add + at position 0,flow_matching,0.3,2.0,64,257
55,add,10.0,-,,+CcCcBc/)lc,+CcCcBc/)l-c,12,add - at position 10,flow_matching,0.3,2.0,64,257
56,add,1.0,F,,+CcCcBc/)l-c,+FCcCcBc/)l-c,13,add F at position 1,flow_matching,0.3,2.0,64,257
57,replace,0.0,C,+,+FCcCcBc/)l-c,CFCcCcBc/)l-c,13,replace + at position 0 with C,flow_matching,0.3,2.0,64,257
58,replace,1.0,c,F,CFCcCcBc/)l-c,CcCcCcBc/)l-c,13,replace F at position 1 with c,flow_matching,0.3,2.0,64,257
59,add,1.0,H,,CcCcCcBc/)l-c,CHcCcCcBc/)l-c,14,add H at position 1,flow_matching,0.3,2.0,64,257
60,replace,7.0,\,B,CHcCcCcBc/)l-c,CHcCcCc\c/)l-c,14,replace B at position 7 with \,flow_matching,0.3,2.0,64,257
61,remove,9.0,/,,CHcCcCc\c/)l-c,CHcCcCc\c)l-c,13,remove / from position 9,flow_matching,0.3,2.0,64,257
62,add,10.0,1,,CHcCcCc\c)l-c,CHcCcCc\c)1l-c,14,add 1 at position 10,flow_matching,0.3,2.0,64,257
63,remove,4.0,c,,CHcCcCc\c)1l-c,CHcCCc\c)1l-c,13,remove c from position 4,flow_matching,0.3,2.0,64,257
64,replace,3.0,6,C,CHcCCc\c)1l-c,CHc6Cc\c)1l-c,13,replace C at position 3 with 6,flow_matching,0.3,2.0,64,257
65,replace,1.0,c,H,CHc6Cc\c)1l-c,Ccc6Cc\c)1l-c,13,replace H at position 1 with c,flow_matching,0.3,2.0,64,257
66,add,10.0,[,,Ccc6Cc\c)1l-c,Ccc6Cc\c)1[l-c,14,add [ at position 10,flow_matching,0.3,2.0,64,257
67,add,1.0,6,,Ccc6Cc\c)1[l-c,C6cc6Cc\c)1[l-c,15,add 6 at position 1,flow_matching,0.3,2.0,64,257
68,add,2.0,N,,C6cc6Cc\c)1[l-c,C6Ncc6Cc\c)1[l-c,16,add N at position 2,flow_matching,0.3,2.0,64,257
69,replace,1.0,c,6,C6Ncc6Cc\c)1[l-c,CcNcc6Cc\c)1[l-c,16,replace 6 at position 1 with c,flow_matching,0.3,2.0,64,257
70,remove,10.0,),,CcNcc6Cc\c)1[l-c,CcNcc6Cc\c1[l-c,15,remove ) from position 10,flow_matching,0.3,2.0,64,257
71,replace,2.0,1,N,CcNcc6Cc\c1[l-c,Cc1cc6Cc\c1[l-c,15,replace N at position 2 with 1,flow_matching,0.3,2.0,64,257
72,remove,4.0,c,,Cc1cc6Cc\c1[l-c,Cc1c6Cc\c1[l-c,14,remove c from position 4,flow_matching,0.3,2.0,64,257
73,add,12.0,\,,Cc1c6Cc\c1[l-c,Cc1c6Cc\c1[l\-c,15,add \ at position 12,flow_matching,0.3,2.0,64,257
74,replace,14.0,#,c,Cc1c6Cc\c1[l\-c,Cc1c6Cc\c1[l\-#,15,replace c at position 14 with #,flow_matching,0.3,2.0,64,257
75,remove,13.0,-,,Cc1c6Cc\c1[l\-#,Cc1c6Cc\c1[l\#,14,remove - from position 13,flow_matching,0.3,2.0,64,257
76,add,5.0,],,Cc1c6Cc\c1[l\#,Cc1c6]Cc\c1[l\#,15,add ] at position 5,flow_matching,0.3,2.0,64,257
77,replace,7.0,],c,Cc1c6]Cc\c1[l\#,Cc1c6]C]\c1[l\#,15,replace c at position 7 with ],flow_matching,0.3,2.0,64,257
78,add,4.0,F,,Cc1c6]C]\c1[l\#,Cc1cF6]C]\c1[l\#,16,add F at position 4,flow_matching,0.3,2.0,64,257
79,replace,15.0,),#,Cc1cF6]C]\c1[l\#,Cc1cF6]C]\c1[l\),16,replace # at position 15 with ),flow_matching,0.3,2.0,64,257
80,replace,4.0,c,F,Cc1cF6]C]\c1[l\),Cc1cc6]C]\c1[l\),16,replace F at position 4 with c,flow_matching,0.3,2.0,64,257
81,add,2.0,5,,Cc1cc6]C]\c1[l\),Cc51cc6]C]\c1[l\),17,add 5 at position 2,flow_matching,0.3,2.0,64,257
82,add,4.0,\,,Cc51cc6]C]\c1[l\),Cc51\cc6]C]\c1[l\),18,add \ at position 4,flow_matching,0.3,2.0,64,257
83,replace,2.0,1,5,Cc51\cc6]C]\c1[l\),Cc11\cc6]C]\c1[l\),18,replace 5 at position 2 with 1,flow_matching,0.3,2.0,64,257
84,replace,5.0,],c,Cc11\cc6]C]\c1[l\),Cc11\]c6]C]\c1[l\),18,replace c at position 5 with ],flow_matching,0.3,2.0,64,257
85,replace,3.0,c,1,Cc11\]c6]C]\c1[l\),Cc1c\]c6]C]\c1[l\),18,replace 1 at position 3 with c,flow_matching,0.3,2.0,64,257
86,add,0.0,(,,Cc1c\]c6]C]\c1[l\),(Cc1c\]c6]C]\c1[l\),19,add ( at position 0,flow_matching,0.3,2.0,64,257
87,replace,11.0,s,],(Cc1c\]c6]C]\c1[l\),(Cc1c\]c6]Cs\c1[l\),19,replace ] at position 11 with s,flow_matching,0.3,2.0,64,257
88,add,16.0,@,,(Cc1c\]c6]Cs\c1[l\),(Cc1c\]c6]Cs\c1[@l\),20,add @ at position 16,flow_matching,0.3,2.0,64,257
89,remove,2.0,c,,(Cc1c\]c6]Cs\c1[@l\),(C1c\]c6]Cs\c1[@l\),19,remove c from position 2,flow_matching,0.3,2.0,64,257
90,add,11.0,-,,(C1c\]c6]Cs\c1[@l\),(C1c\]c6]Cs-\c1[@l\),20,add - at position 11,flow_matching,0.3,2.0,64,257
91,remove,2.0,1,,(C1c\]c6]Cs-\c1[@l\),(Cc\]c6]Cs-\c1[@l\),19,remove 1 from position 2,flow_matching,0.3,2.0,64,257
92,replace,0.0,C,(,(Cc\]c6]Cs-\c1[@l\),CCc\]c6]Cs-\c1[@l\),19,replace ( at position 0 with C,flow_matching,0.3,2.0,64,257
93,replace,15.0,o,@,CCc\]c6]Cs-\c1[@l\),CCc\]c6]Cs-\c1[ol\),19,replace @ at position 15 with o,flow_matching,0.3,2.0,64,257
94,remove,6.0,6,,CCc\]c6]Cs-\c1[ol\),CCc\]c]Cs-\c1[ol\),18,remove 6 from position 6,flow_matching,0.3,2.0,64,257
95,add,4.0,1,,CCc\]c]Cs-\c1[ol\),CCc\1]c]Cs-\c1[ol\),19,add 1 at position 4,flow_matching,0.3,2.0,64,257
96,replace,1.0,c,C,CCc\1]c]Cs-\c1[ol\),Ccc\1]c]Cs-\c1[ol\),19,replace C at position 1 with c,flow_matching,0.3,2.0,64,257
97,add,1.0,4,,Ccc\1]c]Cs-\c1[ol\),C4cc\1]c]Cs-\c1[ol\),20,add 4 at position 1,flow_matching,0.3,2.0,64,257
98,replace,18.0,(,\,C4cc\1]c]Cs-\c1[ol\),C4cc\1]c]Cs-\c1[ol(),20,replace \ at position 18 with (,flow_matching,0.3,2.0,64,257
99,add,11.0,I,,C4cc\1]c]Cs-\c1[ol(),C4cc\1]c]CsI-\c1[ol(),21,add I at position 11,flow_matching,0.3,2.0,64,257
100,remove,16.0,[,,C4cc\1]c]CsI-\c1[ol(),C4cc\1]c]CsI-\c1ol(),20,remove [ from position 16,flow_matching,0.3,2.0,64,257
101,add,16.0,1,,C4cc\1]c]CsI-\c1ol(),C4cc\1]c]CsI-\c11ol(),21,add 1 at position 16,flow_matching,0.3,2.0,64,257
102,remove,13.0,\,,C4cc\1]c]CsI-\c11ol(),C4cc\1]c]CsI-c11ol(),20,remove \ from position 13,flow_matching,0.3,2.0,64,257
103,replace,1.0,c,4,C4cc\1]c]CsI-c11ol(),Cccc\1]c]CsI-c11ol(),20,replace 4 at position 1 with c,flow_matching,0.3,2.0,64,257
104,add,10.0,+,,Cccc\1]c]CsI-c11ol(),Cccc\1]c]C+sI-c11ol(),21,add + at position 10,flow_matching,0.3,2.0,64,257
105,add,12.0,I,,Cccc\1]c]C+sI-c11ol(),Cccc\1]c]C+sII-c11ol(),22,add I at position 12,flow_matching,0.3,2.0,64,257
106,remove,19.0,l,,Cccc\1]c]C+sII-c11ol(),Cccc\1]c]C+sII-c11o(),21,remove l from position 19,flow_matching,0.3,2.0,64,257
107,replace,2.0,1,c,Cccc\1]c]C+sII-c11o(),Cc1c\1]c]C+sII-c11o(),21,replace c at position 2 with 1,flow_matching,0.3,2.0,64,257
108,replace,13.0,N,I,Cc1c\1]c]C+sII-c11o(),Cc1c\1]c]C+sIN-c11o(),21,replace I at position 13 with N,flow_matching,0.3,2.0,64,257
109,replace,4.0,c,\,Cc1c\1]c]C+sIN-c11o(),Cc1cc1]c]C+sIN-c11o(),21,replace \ at position 4 with c,flow_matching,0.3,2.0,64,257
110,add,16.0,N,,Cc1cc1]c]C+sIN-c11o(),Cc1cc1]c]C+sIN-cN11o(),22,add N at position 16,flow_matching,0.3,2.0,64,257
111,replace,5.0,c,1,Cc1cc1]c]C+sIN-cN11o(),Cc1ccc]c]C+sIN-cN11o(),22,replace 1 at position 5 with c,flow_matching,0.3,2.0,64,257
112,replace,6.0,(,],Cc1ccc]c]C+sIN-cN11o(),Cc1ccc(c]C+sIN-cN11o(),22,replace ] at position 6 with (,flow_matching,0.3,2.0,64,257
113,remove,11.0,s,,Cc1ccc(c]C+sIN-cN11o(),Cc1ccc(c]C+IN-cN11o(),21,remove s from position 11,flow_matching,0.3,2.0,64,257
114,replace,19.0,4,(,Cc1ccc(c]C+IN-cN11o(),Cc1ccc(c]C+IN-cN11o4),21,replace ( at position 19 with 4,flow_matching,0.3,2.0,64,257
115,replace,0.0,#,C,Cc1ccc(c]C+IN-cN11o4),#c1ccc(c]C+IN-cN11o4),21,replace C at position 0 with #,flow_matching,0.3,2.0,64,257
116,replace,0.0,C,#,#c1ccc(c]C+IN-cN11o4),Cc1ccc(c]C+IN-cN11o4),21,replace # at position 0 with C,flow_matching,0.3,2.0,64,257
117,replace,7.0,\,c,Cc1ccc(c]C+IN-cN11o4),Cc1ccc(\]C+IN-cN11o4),21,replace c at position 7 with \,flow_matching,0.3,2.0,64,257
118,remove,19.0,4,,Cc1ccc(\]C+IN-cN11o4),Cc1ccc(\]C+IN-cN11o),20,remove 4 from position 19,flow_matching,0.3,2.0,64,257
119,replace,7.0,C,\,Cc1ccc(\]C+IN-cN11o),Cc1ccc(C]C+IN-cN11o),20,replace \ at position 7 with C,flow_matching,0.3,2.0,64,257
120,replace,8.0,),],Cc1ccc(C]C+IN-cN11o),Cc1ccc(C)C+IN-cN11o),20,replace ] at position 8 with ),flow_matching,0.3,2.0,64,257
121,add,6.0,6,,Cc1ccc(C)C+IN-cN11o),Cc1ccc6(C)C+IN-cN11o),21,add 6 at position 6,flow_matching,0.3,2.0,64,257
122,replace,8.0,=,C,Cc1ccc6(C)C+IN-cN11o),Cc1ccc6(=)C+IN-cN11o),21,replace C at position 8 with =,flow_matching,0.3,2.0,64,257
123,replace,6.0,(,6,Cc1ccc6(=)C+IN-cN11o),Cc1ccc((=)C+IN-cN11o),21,replace 6 at position 6 with (,flow_matching,0.3,2.0,64,257
124,add,4.0,C,,Cc1ccc((=)C+IN-cN11o),Cc1cCcc((=)C+IN-cN11o),22,add C at position 4,flow_matching,0.3,2.0,64,257
125,replace,10.0,@,),Cc1cCcc((=)C+IN-cN11o),Cc1cCcc((=@C+IN-cN11o),22,replace ) at position 10 with @,flow_matching,0.3,2.0,64,257
126,add,12.0,5,,Cc1cCcc((=@C+IN-cN11o),Cc1cCcc((=@C5+IN-cN11o),23,add 5 at position 12,flow_matching,0.3,2.0,64,257
127,replace,18.0,c,N,Cc1cCcc((=@C5+IN-cN11o),Cc1cCcc((=@C5+IN-cc11o),23,replace N at position 18 with c,flow_matching,0.3,2.0,64,257
128,remove,9.0,=,,Cc1cCcc((=@C5+IN-cc11o),Cc1cCcc((@C5+IN-cc11o),22,remove = from position 9,flow_matching,0.3,2.0,64,257
129,remove,8.0,(,,Cc1cCcc((@C5+IN-cc11o),Cc1cCcc(@C5+IN-cc11o),21,remove ( from position 8,flow_matching,0.3,2.0,64,257
130,replace,15.0,n,c,Cc1cCcc(@C5+IN-cc11o),Cc1cCcc(@C5+IN-nc11o),21,replace c at position 15 with n,flow_matching,0.3,2.0,64,257
131,add,10.0,4,,Cc1cCcc(@C5+IN-nc11o),Cc1cCcc(@C45+IN-nc11o),22,add 4 at position 10,flow_matching,0.3,2.0,64,257
132,replace,4.0,c,C,Cc1cCcc(@C45+IN-nc11o),Cc1cccc(@C45+IN-nc11o),22,replace C at position 4 with c,flow_matching,0.3,2.0,64,257
133,remove,12.0,+,,Cc1cccc(@C45+IN-nc11o),Cc1cccc(@C45IN-nc11o),21,remove + from position 12,flow_matching,0.3,2.0,64,257
134,remove,11.0,5,,Cc1cccc(@C45IN-nc11o),Cc1cccc(@C4IN-nc11o),20,remove 5 from position 11,flow_matching,0.3,2.0,64,257
135,remove,11.0,I,,Cc1cccc(@C4IN-nc11o),Cc1cccc(@C4N-nc11o),19,remove I from position 11,flow_matching,0.3,2.0,64,257
136,replace,12.0,N,-,Cc1cccc(@C4N-nc11o),Cc1cccc(@C4NNnc11o),19,replace - at position 12 with N,flow_matching,0.3,2.0,64,257
137,replace,17.0,5,o,Cc1cccc(@C4NNnc11o),Cc1cccc(@C4NNnc115),19,replace o at position 17 with 5,flow_matching,0.3,2.0,64,257
138,add,16.0,],,Cc1cccc(@C4NNnc115),Cc1cccc(@C4NNnc1]15),20,add ] at position 16,flow_matching,0.3,2.0,64,257
139,add,9.0,/,,Cc1cccc(@C4NNnc1]15),Cc1cccc(@/C4NNnc1]15),21,add / at position 9,flow_matching,0.3,2.0,64,257
140,add,7.0,s,,Cc1cccc(@/C4NNnc1]15),Cc1ccccs(@/C4NNnc1]15),22,add s at position 7,flow_matching,0.3,2.0,64,257
141,replace,6.0,(,c,Cc1ccccs(@/C4NNnc1]15),Cc1ccc(s(@/C4NNnc1]15),22,replace c at position 6 with (,flow_matching,0.3,2.0,64,257
142,add,8.0,\,,Cc1ccc(s(@/C4NNnc1]15),Cc1ccc(s\(@/C4NNnc1]15),23,add \ at position 8,flow_matching,0.3,2.0,64,257
143,remove,3.0,c,,Cc1ccc(s\(@/C4NNnc1]15),Cc1cc(s\(@/C4NNnc1]15),22,remove c from position 3,flow_matching,0.3,2.0,64,257
144,replace,8.0,r,(,Cc1cc(s\(@/C4NNnc1]15),Cc1cc(s\r@/C4NNnc1]15),22,replace ( at position 8 with r,flow_matching,0.3,2.0,64,257
145,add,7.0,c,,Cc1cc(s\r@/C4NNnc1]15),Cc1cc(sc\r@/C4NNnc1]15),23,add c at position 7,flow_matching,0.3,2.0,64,257
146,replace,5.0,c,(,Cc1cc(sc\r@/C4NNnc1]15),Cc1cccsc\r@/C4NNnc1]15),23,replace ( at position 5 with c,flow_matching,0.3,2.0,64,257
147,add,3.0,S,,Cc1cccsc\r@/C4NNnc1]15),Cc1Scccsc\r@/C4NNnc1]15),24,add S at position 3,flow_matching,0.3,2.0,64,257
148,replace,3.0,c,S,Cc1Scccsc\r@/C4NNnc1]15),Cc1ccccsc\r@/C4NNnc1]15),24,replace S at position 3 with c,flow_matching,0.3,2.0,64,257
149,replace,6.0,(,c,Cc1ccccsc\r@/C4NNnc1]15),Cc1ccc(sc\r@/C4NNnc1]15),24,replace c at position 6 with (,flow_matching,0.3,2.0,64,257
150,replace,23.0,=,),Cc1ccc(sc\r@/C4NNnc1]15),Cc1ccc(sc\r@/C4NNnc1]15=,24,replace ) at position 23 with =,flow_matching,0.3,2.0,64,257
151,remove,2.0,1,,Cc1ccc(sc\r@/C4NNnc1]15=,Ccccc(sc\r@/C4NNnc1]15=,23,remove 1 from position 2,flow_matching,0.3,2.0,64,257
152,replace,8.0,l,\,Ccccc(sc\r@/C4NNnc1]15=,Ccccc(sclr@/C4NNnc1]15=,23,replace \ at position 8 with l,flow_matching,0.3,2.0,64,257
153,remove,17.0,c,,Ccccc(sclr@/C4NNnc1]15=,Ccccc(sclr@/C4NNn1]15=,22,remove c from position 17,flow_matching,0.3,2.0,64,257
154,replace,2.0,1,c,Ccccc(sclr@/C4NNn1]15=,Cc1cc(sclr@/C4NNn1]15=,22,replace c at position 2 with 1,flow_matching,0.3,2.0,64,257
155,remove,2.0,1,,Cc1cc(sclr@/C4NNn1]15=,Cccc(sclr@/C4NNn1]15=,21,remove 1 from position 2,flow_matching,0.3,2.0,64,257
156,replace,2.0,1,c,Cccc(sclr@/C4NNn1]15=,Cc1c(sclr@/C4NNn1]15=,21,replace c at position 2 with 1,flow_matching,0.3,2.0,64,257
157,replace,1.0,N,c,Cc1c(sclr@/C4NNn1]15=,CN1c(sclr@/C4NNn1]15=,21,replace c at position 1 with N,flow_matching,0.3,2.0,64,257
158,replace,2.0,=,1,CN1c(sclr@/C4NNn1]15=,CN=c(sclr@/C4NNn1]15=,21,replace 1 at position 2 with =,flow_matching,0.3,2.0,64,257
159,replace,10.0,3,/,CN=c(sclr@/C4NNn1]15=,CN=c(sclr@3C4NNn1]15=,21,replace / at position 10 with 3,flow_matching,0.3,2.0,64,257
160,replace,1.0,c,N,CN=c(sclr@3C4NNn1]15=,Cc=c(sclr@3C4NNn1]15=,21,replace N at position 1 with c,flow_matching,0.3,2.0,64,257
161,replace,2.0,1,=,Cc=c(sclr@3C4NNn1]15=,Cc1c(sclr@3C4NNn1]15=,21,replace = at position 2 with 1,flow_matching,0.3,2.0,64,257
162,replace,14.0,@,N,Cc1c(sclr@3C4NNn1]15=,Cc1c(sclr@3C4N@n1]15=,21,replace N at position 14 with @,flow_matching,0.3,2.0,64,257
163,add,19.0,3,,Cc1c(sclr@3C4N@n1]15=,Cc1c(sclr@3C4N@n1]135=,22,add 3 at position 19,flow_matching,0.3,2.0,64,257
164,remove,9.0,@,,Cc1c(sclr@3C4N@n1]135=,Cc1c(sclr3C4N@n1]135=,21,remove @ from position 9,flow_matching,0.3,2.0,64,257
165,add,4.0,B,,Cc1c(sclr3C4N@n1]135=,Cc1cB(sclr3C4N@n1]135=,22,add B at position 4,flow_matching,0.3,2.0,64,257
166,remove,4.0,B,,Cc1cB(sclr3C4N@n1]135=,Cc1c(sclr3C4N@n1]135=,21,remove B from position 4,flow_matching,0.3,2.0,64,257
167,replace,4.0,c,(,Cc1c(sclr3C4N@n1]135=,Cc1ccsclr3C4N@n1]135=,21,replace ( at position 4 with c,flow_matching,0.3,2.0,64,257
168,remove,6.0,c,,Cc1ccsclr3C4N@n1]135=,Cc1ccslr3C4N@n1]135=,20,remove c from position 6,flow_matching,0.3,2.0,64,257
169,replace,5.0,c,s,Cc1ccslr3C4N@n1]135=,Cc1ccclr3C4N@n1]135=,20,replace s at position 5 with c,flow_matching,0.3,2.0,64,257
170,remove,14.0,1,,Cc1ccclr3C4N@n1]135=,Cc1ccclr3C4N@n]135=,19,remove 1 from position 14,flow_matching,0.3,2.0,64,257
171,replace,6.0,(,l,Cc1ccclr3C4N@n]135=,Cc1ccc(r3C4N@n]135=,19,replace l at position 6 with (,flow_matching,0.3,2.0,64,257
172,replace,7.0,C,r,Cc1ccc(r3C4N@n]135=,Cc1ccc(C3C4N@n]135=,19,replace r at position 7 with C,flow_matching,0.3,2.0,64,257
173,replace,8.0,),3,Cc1ccc(C3C4N@n]135=,Cc1ccc(C)C4N@n]135=,19,replace 3 at position 8 with ),flow_matching,0.3,2.0,64,257
174,replace,9.0,c,C,Cc1ccc(C)C4N@n]135=,Cc1ccc(C)c4N@n]135=,19,replace C at position 9 with c,flow_matching,0.3,2.0,64,257
175,replace,10.0,(,4,Cc1ccc(C)c4N@n]135=,Cc1ccc(C)c(N@n]135=,19,replace 4 at position 10 with (,flow_matching,0.3,2.0,64,257
176,remove,0.0,C,,Cc1ccc(C)c(N@n]135=,c1ccc(C)c(N@n]135=,18,remove C from position 0,flow_matching,0.3,2.0,64,257
177,add,10.0,],,c1ccc(C)c(N@n]135=,c1ccc(C)c(]N@n]135=,19,add ] at position 10,flow_matching,0.3,2.0,64,257
178,add,17.0,o,,c1ccc(C)c(]N@n]135=,c1ccc(C)c(]N@n]13o5=,20,add o at position 17,flow_matching,0.3,2.0,64,257
179,add,9.0,@,,c1ccc(C)c(]N@n]13o5=,c1ccc(C)c@(]N@n]13o5=,21,add @ at position 9,flow_matching,0.3,2.0,64,257
180,replace,0.0,C,c,c1ccc(C)c@(]N@n]13o5=,C1ccc(C)c@(]N@n]13o5=,21,replace c at position 0 with C,flow_matching,0.3,2.0,64,257
181,replace,11.0,7,],C1ccc(C)c@(]N@n]13o5=,C1ccc(C)c@(7N@n]13o5=,21,replace ] at position 11 with 7,flow_matching,0.3,2.0,64,257
182,remove,5.0,(,,C1ccc(C)c@(7N@n]13o5=,C1cccC)c@(7N@n]13o5=,20,remove ( from position 5,flow_matching,0.3,2.0,64,257
183,replace,1.0,c,1,C1cccC)c@(7N@n]13o5=,CccccC)c@(7N@n]13o5=,20,replace 1 at position 1 with c,flow_matching,0.3,2.0,64,257
184,replace,2.0,1,c,CccccC)c@(7N@n]13o5=,Cc1ccC)c@(7N@n]13o5=,20,replace c at position 2 with 1,flow_matching,0.3,2.0,64,257
185,replace,15.0,\,1,Cc1ccC)c@(7N@n]13o5=,Cc1ccC)c@(7N@n]\3o5=,20,replace 1 at position 15 with \,flow_matching,0.3,2.0,64,257
186,replace,5.0,c,C,Cc1ccC)c@(7N@n]\3o5=,Cc1ccc)c@(7N@n]\3o5=,20,replace C at position 5 with c,flow_matching,0.3,2.0,64,257
187,add,16.0,B,,Cc1ccc)c@(7N@n]\3o5=,Cc1ccc)c@(7N@n]\B3o5=,21,add B at position 16,flow_matching,0.3,2.0,64,257
188,replace,1.0,3,c,Cc1ccc)c@(7N@n]\B3o5=,C31ccc)c@(7N@n]\B3o5=,21,replace c at position 1 with 3,flow_matching,0.3,2.0,64,257
189,add,1.0,=,,C31ccc)c@(7N@n]\B3o5=,C=31ccc)c@(7N@n]\B3o5=,22,add = at position 1,flow_matching,0.3,2.0,64,257
190,replace,15.0,s,],C=31ccc)c@(7N@n]\B3o5=,C=31ccc)c@(7N@ns\B3o5=,22,replace ] at position 15 with s,flow_matching,0.3,2.0,64,257
191,add,18.0,n,,C=31ccc)c@(7N@ns\B3o5=,C=31ccc)c@(7N@ns\Bn3o5=,23,add n at position 18,flow_matching,0.3,2.0,64,257
192,remove,20.0,o,,C=31ccc)c@(7N@ns\Bn3o5=,C=31ccc)c@(7N@ns\Bn35=,22,remove o from position 20,flow_matching,0.3,2.0,64,257
193,replace,1.0,c,=,C=31ccc)c@(7N@ns\Bn35=,Cc31ccc)c@(7N@ns\Bn35=,22,replace = at position 1 with c,flow_matching,0.3,2.0,64,257
194,replace,2.0,1,3,Cc31ccc)c@(7N@ns\Bn35=,Cc11ccc)c@(7N@ns\Bn35=,22,replace 3 at position 2 with 1,flow_matching,0.3,2.0,64,257
195,replace,18.0,),n,Cc11ccc)c@(7N@ns\Bn35=,Cc11ccc)c@(7N@ns\B)35=,22,replace n at position 18 with ),flow_matching,0.3,2.0,64,257
196,add,0.0,-,,Cc11ccc)c@(7N@ns\B)35=,-Cc11ccc)c@(7N@ns\B)35=,23,add - at position 0,flow_matching,0.3,2.0,64,257
197,remove,10.0,@,,-Cc11ccc)c@(7N@ns\B)35=,-Cc11ccc)c(7N@ns\B)35=,22,remove @ from position 10,flow_matching,0.3,2.0,64,257
198,replace,0.0,C,-,-Cc11ccc)c(7N@ns\B)35=,CCc11ccc)c(7N@ns\B)35=,22,replace - at position 0 with C,flow_matching,0.3,2.0,64,257
199,replace,1.0,c,C,CCc11ccc)c(7N@ns\B)35=,Ccc11ccc)c(7N@ns\B)35=,22,replace C at position 1 with c,flow_matching,0.3,2.0,64,257
200,replace,2.0,1,c,Ccc11ccc)c(7N@ns\B)35=,Cc111ccc)c(7N@ns\B)35=,22,replace c at position 2 with 1,flow_matching,0.3,2.0,64,257
201,replace,3.0,c,1,Cc111ccc)c(7N@ns\B)35=,Cc1c1ccc)c(7N@ns\B)35=,22,replace 1 at position 3 with c,flow_matching,0.3,2.0,64,257
202,replace,4.0,c,1,Cc1c1ccc)c(7N@ns\B)35=,Cc1ccccc)c(7N@ns\B)35=,22,replace 1 at position 4 with c,flow_matching,0.3,2.0,64,257
203,replace,6.0,(,c,Cc1ccccc)c(7N@ns\B)35=,Cc1ccc(c)c(7N@ns\B)35=,22,replace c at position 6 with (,flow_matching,0.3,2.0,64,257
204,replace,7.0,C,c,Cc1ccc(c)c(7N@ns\B)35=,Cc1ccc(C)c(7N@ns\B)35=,22,replace c at position 7 with C,flow_matching,0.3,2.0,64,257
205,replace,11.0,S,7,Cc1ccc(C)c(7N@ns\B)35=,Cc1ccc(C)c(SN@ns\B)35=,22,replace 7 at position 11 with S,flow_matching,0.3,2.0,64,257
206,replace,12.0,(,N,Cc1ccc(C)c(SN@ns\B)35=,Cc1ccc(C)c(S(@ns\B)35=,22,replace N at position 12 with (,flow_matching,0.3,2.0,64,257
207,replace,13.0,=,@,Cc1ccc(C)c(S(@ns\B)35=,Cc1ccc(C)c(S(=ns\B)35=,22,replace @ at position 13 with =,flow_matching,0.3,2.0,64,257
208,replace,14.0,O,n,Cc1ccc(C)c(S(=ns\B)35=,Cc1ccc(C)c(S(=Os\B)35=,22,replace n at position 14 with O,flow_matching,0.3,2.0,64,257
209,replace,15.0,),s,Cc1ccc(C)c(S(=Os\B)35=,Cc1ccc(C)c(S(=O)\B)35=,22,replace s at position 15 with ),flow_matching,0.3,2.0,64,257
210,replace,16.0,(,\,Cc1ccc(C)c(S(=O)\B)35=,Cc1ccc(C)c(S(=O)(B)35=,22,replace \ at position 16 with (,flow_matching,0.3,2.0,64,257
211,replace,17.0,=,B,Cc1ccc(C)c(S(=O)(B)35=,Cc1ccc(C)c(S(=O)(=)35=,22,replace B at position 17 with =,flow_matching,0.3,2.0,64,257
212,replace,18.0,O,),Cc1ccc(C)c(S(=O)(=)35=,Cc1ccc(C)c(S(=O)(=O35=,22,replace ) at position 18 with O,flow_matching,0.3,2.0,64,257
213,replace,19.0,),3,Cc1ccc(C)c(S(=O)(=O35=,Cc1ccc(C)c(S(=O)(=O)5=,22,replace 3 at position 19 with ),flow_matching,0.3,2.0,64,257
214,replace,20.0,N,5,Cc1ccc(C)c(S(=O)(=O)5=,Cc1ccc(C)c(S(=O)(=O)N=,22,replace 5 at position 20 with N,flow_matching,0.3,2.0,64,257
215,replace,21.0,2,=,Cc1ccc(C)c(S(=O)(=O)N=,Cc1ccc(C)c(S(=O)(=O)N2,22,replace = at position 21 with 2,flow_matching,0.3,2.0,64,257
216,add,22.0,C,,Cc1ccc(C)c(S(=O)(=O)N2,Cc1ccc(C)c(S(=O)(=O)N2C,23,add C at position 22,flow_matching,0.3,2.0,64,257
217,add,23.0,C,,Cc1ccc(C)c(S(=O)(=O)N2C,Cc1ccc(C)c(S(=O)(=O)N2CC,24,add C at position 23,flow_matching,0.3,2.0,64,257
218,add,24.0,N,,Cc1ccc(C)c(S(=O)(=O)N2CC,Cc1ccc(C)c(S(=O)(=O)N2CCN,25,add N at position 24,flow_matching,0.3,2.0,64,257
219,add,25.0,(,,Cc1ccc(C)c(S(=O)(=O)N2CCN,Cc1ccc(C)c(S(=O)(=O)N2CCN(,26,add ( at position 25,flow_matching,0.3,2.0,64,257
220,add,26.0,[,,Cc1ccc(C)c(S(=O)(=O)N2CCN(,Cc1ccc(C)c(S(=O)(=O)N2CCN([,27,add [ at position 26,flow_matching,0.3,2.0,64,257
221,add,27.0,C,,Cc1ccc(C)c(S(=O)(=O)N2CCN([,Cc1ccc(C)c(S(=O)(=O)N2CCN([C,28,add C at position 27,flow_matching,0.3,2.0,64,257
222,add,28.0,@,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@,29,add @ at position 28,flow_matching,0.3,2.0,64,257
223,add,29.0,H,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H,30,add H at position 29,flow_matching,0.3,2.0,64,257
224,add,30.0,],,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H],31,add ] at position 30,flow_matching,0.3,2.0,64,257
225,add,31.0,(,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H],Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](,32,add ( at position 31,flow_matching,0.3,2.0,64,257
226,add,32.0,C,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C,33,add C at position 32,flow_matching,0.3,2.0,64,257
227,add,33.0,),,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C),34,add ) at position 33,flow_matching,0.3,2.0,64,257
228,add,34.0,c,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C),Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c,35,add c at position 34,flow_matching,0.3,2.0,64,257
229,add,35.0,3,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3,36,add 3 at position 35,flow_matching,0.3,2.0,64,257
230,add,36.0,n,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3n,37,add n at position 36,flow_matching,0.3,2.0,64,257
231,add,37.0,c,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3n,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc,38,add c at position 37,flow_matching,0.3,2.0,64,257
232,add,38.0,(,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(,39,add ( at position 38,flow_matching,0.3,2.0,64,257
233,add,39.0,N,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N,40,add N at position 39,flow_matching,0.3,2.0,64,257
234,add,40.0,),,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N),41,add ) at position 40,flow_matching,0.3,2.0,64,257
235,add,41.0,n,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N),Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)n,42,add n at position 41,flow_matching,0.3,2.0,64,257
236,add,42.0,c,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)n,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc,43,add c at position 42,flow_matching,0.3,2.0,64,257
237,add,43.0,(,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(,44,add ( at position 43,flow_matching,0.3,2.0,64,257
238,add,44.0,N,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(N,45,add N at position 44,flow_matching,0.3,2.0,64,257
239,add,45.0,c,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(N,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc,46,add c at position 45,flow_matching,0.3,2.0,64,257
240,add,46.0,4,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4,47,add 4 at position 46,flow_matching,0.3,2.0,64,257
241,add,47.0,c,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4c,48,add c at position 47,flow_matching,0.3,2.0,64,257
242,add,48.0,c,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4c,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4cc,49,add c at position 48,flow_matching,0.3,2.0,64,257
243,add,49.0,c,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4cc,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccc,50,add c at position 49,flow_matching,0.3,2.0,64,257
244,add,50.0,c,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccc,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4cccc,51,add c at position 50,flow_matching,0.3,2.0,64,257
245,add,51.0,c,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4cccc,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc,52,add c at position 51,flow_matching,0.3,2.0,64,257
246,add,52.0,4,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4,53,add 4 at position 52,flow_matching,0.3,2.0,64,257
247,add,53.0,),,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4),54,add ) at position 53,flow_matching,0.3,2.0,64,257
248,add,54.0,n,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4),Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n,55,add n at position 54,flow_matching,0.3,2.0,64,257
249,add,55.0,3,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3,56,add 3 at position 55,flow_matching,0.3,2.0,64,257
250,add,56.0,),,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3),57,add ) at position 56,flow_matching,0.3,2.0,64,257
251,add,57.0,C,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3),Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)C,58,add C at position 57,flow_matching,0.3,2.0,64,257
252,add,58.0,C,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)C,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC,59,add C at position 58,flow_matching,0.3,2.0,64,257
253,add,59.0,2,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC2,60,add 2 at position 59,flow_matching,0.3,2.0,64,257
254,add,60.0,),,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC2,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC2),61,add ) at position 60,flow_matching,0.3,2.0,64,257
255,add,61.0,c,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC2),Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC2)c,62,add c at position 61,flow_matching,0.3,2.0,64,257
256,add,62.0,1,,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC2)c,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC2)c1,63,add 1 at position 62,flow_matching,0.3,2.0,64,257
257,add,63.0,"
",,Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC2)c1,"Cc1ccc(C)c(S(=O)(=O)N2CCN([C@H](C)c3nc(N)nc(Nc4ccccc4)n3)CC2)c1
",64,"add 
 at position 63",flow_matching,0.3,2.0,64,257
