step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,42,115
1,add,0.0,/,,,/,1,add / at position 0,flow_matching,0.3,2.0,42,115
2,replace,0.0,n,/,/,n,1,replace / at position 0 with n,flow_matching,0.3,2.0,42,115
3,add,0.0,6,,n,6n,2,add 6 at position 0,flow_matching,0.3,2.0,42,115
4,replace,0.0,C,6,6n,Cn,2,replace 6 at position 0 with C,flow_matching,0.3,2.0,42,115
5,replace,1.0,N,n,Cn,CN,2,replace n at position 1 with N,flow_matching,0.3,2.0,42,115
6,remove,0.0,<PERSON>,,C<PERSON>,N,1,remove C from position 0,flow_matching,0.3,2.0,42,115
7,remove,0.0,N,,N,,0,remove N from position 0,flow_matching,0.3,2.0,42,115
8,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,42,115
9,add,1.0,N,,C,CN,2,add N at position 1,flow_matching,0.3,2.0,42,115
10,add,1.0,),,CN,C)N,3,add ) at position 1,flow_matching,0.3,2.0,42,115
11,replace,0.0,r,C,C)N,r)N,3,replace C at position 0 with r,flow_matching,0.3,2.0,42,115
12,remove,1.0,),,r)N,rN,2,remove ) from position 1,flow_matching,0.3,2.0,42,115
13,replace,1.0,F,N,rN,rF,2,replace N at position 1 with F,flow_matching,0.3,2.0,42,115
14,remove,0.0,r,,rF,F,1,remove r from position 0,flow_matching,0.3,2.0,42,115
15,add,0.0,-,,F,-F,2,add - at position 0,flow_matching,0.3,2.0,42,115
16,replace,0.0,1,-,-F,1F,2,replace - at position 0 with 1,flow_matching,0.3,2.0,42,115
17,add,1.0,s,,1F,1sF,3,add s at position 1,flow_matching,0.3,2.0,42,115
18,remove,2.0,F,,1sF,1s,2,remove F from position 2,flow_matching,0.3,2.0,42,115
19,remove,0.0,1,,1s,s,1,remove 1 from position 0,flow_matching,0.3,2.0,42,115
20,replace,0.0,[,s,s,[,1,replace s at position 0 with [,flow_matching,0.3,2.0,42,115
21,replace,0.0,C,[,[,C,1,replace [ at position 0 with C,flow_matching,0.3,2.0,42,115
22,add,1.0,N,,C,CN,2,add N at position 1,flow_matching,0.3,2.0,42,115
23,replace,1.0,@,N,CN,C@,2,replace N at position 1 with @,flow_matching,0.3,2.0,42,115
24,remove,1.0,@,,C@,C,1,remove @ from position 1,flow_matching,0.3,2.0,42,115
25,add,1.0,-,,C,C-,2,add - at position 1,flow_matching,0.3,2.0,42,115
26,add,2.0,6,,C-,C-6,3,add 6 at position 2,flow_matching,0.3,2.0,42,115
27,add,3.0,+,,C-6,C-6+,4,add + at position 3,flow_matching,0.3,2.0,42,115
28,remove,3.0,+,,C-6+,C-6,3,remove + from position 3,flow_matching,0.3,2.0,42,115
29,remove,1.0,-,,C-6,C6,2,remove - from position 1,flow_matching,0.3,2.0,42,115
30,replace,1.0,N,6,C6,CN,2,replace 6 at position 1 with N,flow_matching,0.3,2.0,42,115
31,add,1.0,r,,CN,CrN,3,add r at position 1,flow_matching,0.3,2.0,42,115
32,add,0.0,/,,CrN,/CrN,4,add / at position 0,flow_matching,0.3,2.0,42,115
33,replace,0.0,-,/,/CrN,-CrN,4,replace / at position 0 with -,flow_matching,0.3,2.0,42,115
34,add,3.0,O,,-CrN,-CrON,5,add O at position 3,flow_matching,0.3,2.0,42,115
35,replace,2.0,/,r,-CrON,-C/ON,5,replace r at position 2 with /,flow_matching,0.3,2.0,42,115
36,add,4.0,s,,-C/ON,-C/OsN,6,add s at position 4,flow_matching,0.3,2.0,42,115
37,replace,2.0,2,/,-C/OsN,-C2OsN,6,replace / at position 2 with 2,flow_matching,0.3,2.0,42,115
38,replace,0.0,7,-,-C2OsN,7C2OsN,6,replace - at position 0 with 7,flow_matching,0.3,2.0,42,115
39,add,1.0,B,,7C2OsN,7BC2OsN,7,add B at position 1,flow_matching,0.3,2.0,42,115
40,replace,0.0,C,7,7BC2OsN,CBC2OsN,7,replace 7 at position 0 with C,flow_matching,0.3,2.0,42,115
41,replace,1.0,N,B,CBC2OsN,CNC2OsN,7,replace B at position 1 with N,flow_matching,0.3,2.0,42,115
42,add,5.0,O,,CNC2OsN,CNC2OOsN,8,add O at position 5,flow_matching,0.3,2.0,42,115
43,remove,0.0,C,,CNC2OOsN,NC2OOsN,7,remove C from position 0,flow_matching,0.3,2.0,42,115
44,replace,0.0,C,N,NC2OOsN,CC2OOsN,7,replace N at position 0 with C,flow_matching,0.3,2.0,42,115
45,remove,6.0,N,,CC2OOsN,CC2OOs,6,remove N from position 6,flow_matching,0.3,2.0,42,115
46,remove,3.0,O,,CC2OOs,CC2Os,5,remove O from position 3,flow_matching,0.3,2.0,42,115
47,replace,1.0,@,C,CC2Os,C@2Os,5,replace C at position 1 with @,flow_matching,0.3,2.0,42,115
48,replace,1.0,N,@,C@2Os,CN2Os,5,replace @ at position 1 with N,flow_matching,0.3,2.0,42,115
49,replace,2.0,C,2,CN2Os,CNCOs,5,replace 2 at position 2 with C,flow_matching,0.3,2.0,42,115
50,replace,4.0,B,s,CNCOs,CNCOB,5,replace s at position 4 with B,flow_matching,0.3,2.0,42,115
51,remove,4.0,B,,CNCOB,CNCO,4,remove B from position 4,flow_matching,0.3,2.0,42,115
52,add,0.0,I,,CNCO,ICNCO,5,add I at position 0,flow_matching,0.3,2.0,42,115
53,replace,4.0,),O,ICNCO,ICNC),5,replace O at position 4 with ),flow_matching,0.3,2.0,42,115
54,add,1.0,r,,ICNC),IrCNC),6,add r at position 1,flow_matching,0.3,2.0,42,115
55,remove,0.0,I,,IrCNC),rCNC),5,remove I from position 0,flow_matching,0.3,2.0,42,115
56,add,4.0,H,,rCNC),rCNCH),6,add H at position 4,flow_matching,0.3,2.0,42,115
57,add,6.0,H,,rCNCH),rCNCH)H,7,add H at position 6,flow_matching,0.3,2.0,42,115
58,replace,0.0,C,r,rCNCH)H,CCNCH)H,7,replace r at position 0 with C,flow_matching,0.3,2.0,42,115
59,add,3.0,3,,CCNCH)H,CCN3CH)H,8,add 3 at position 3,flow_matching,0.3,2.0,42,115
60,remove,5.0,H,,CCN3CH)H,CCN3C)H,7,remove H from position 5,flow_matching,0.3,2.0,42,115
61,replace,1.0,N,C,CCN3C)H,CNN3C)H,7,replace C at position 1 with N,flow_matching,0.3,2.0,42,115
62,add,4.0,1,,CNN3C)H,CNN31C)H,8,add 1 at position 4,flow_matching,0.3,2.0,42,115
63,replace,3.0,),3,CNN31C)H,CNN)1C)H,8,replace 3 at position 3 with ),flow_matching,0.3,2.0,42,115
64,add,7.0,S,,CNN)1C)H,CNN)1C)SH,9,add S at position 7,flow_matching,0.3,2.0,42,115
65,remove,7.0,S,,CNN)1C)SH,CNN)1C)H,8,remove S from position 7,flow_matching,0.3,2.0,42,115
66,replace,2.0,C,N,CNN)1C)H,CNC)1C)H,8,replace N at position 2 with C,flow_matching,0.3,2.0,42,115
67,add,1.0,F,,CNC)1C)H,CFNC)1C)H,9,add F at position 1,flow_matching,0.3,2.0,42,115
68,replace,1.0,N,F,CFNC)1C)H,CNNC)1C)H,9,replace F at position 1 with N,flow_matching,0.3,2.0,42,115
69,add,6.0,O,,CNNC)1C)H,CNNC)1OC)H,10,add O at position 6,flow_matching,0.3,2.0,42,115
70,replace,2.0,C,N,CNNC)1OC)H,CNCC)1OC)H,10,replace N at position 2 with C,flow_matching,0.3,2.0,42,115
71,add,5.0,[,,CNCC)1OC)H,CNCC)[1OC)H,11,add [ at position 5,flow_matching,0.3,2.0,42,115
72,add,0.0,2,,CNCC)[1OC)H,2CNCC)[1OC)H,12,add 2 at position 0,flow_matching,0.3,2.0,42,115
73,replace,10.0,1,),2CNCC)[1OC)H,2CNCC)[1OC1H,12,replace ) at position 10 with 1,flow_matching,0.3,2.0,42,115
74,replace,0.0,C,2,2CNCC)[1OC1H,CCNCC)[1OC1H,12,replace 2 at position 0 with C,flow_matching,0.3,2.0,42,115
75,replace,1.0,N,C,CCNCC)[1OC1H,CNNCC)[1OC1H,12,replace C at position 1 with N,flow_matching,0.3,2.0,42,115
76,replace,2.0,C,N,CNNCC)[1OC1H,CNCCC)[1OC1H,12,replace N at position 2 with C,flow_matching,0.3,2.0,42,115
77,replace,3.0,(,C,CNCCC)[1OC1H,CNC(C)[1OC1H,12,replace C at position 3 with (,flow_matching,0.3,2.0,42,115
78,replace,4.0,=,C,CNC(C)[1OC1H,CNC(=)[1OC1H,12,replace C at position 4 with =,flow_matching,0.3,2.0,42,115
79,replace,5.0,O,),CNC(=)[1OC1H,CNC(=O[1OC1H,12,replace ) at position 5 with O,flow_matching,0.3,2.0,42,115
80,replace,6.0,),[,CNC(=O[1OC1H,CNC(=O)1OC1H,12,replace [ at position 6 with ),flow_matching,0.3,2.0,42,115
81,replace,7.0,c,1,CNC(=O)1OC1H,CNC(=O)cOC1H,12,replace 1 at position 7 with c,flow_matching,0.3,2.0,42,115
82,replace,8.0,1,O,CNC(=O)cOC1H,CNC(=O)c1C1H,12,replace O at position 8 with 1,flow_matching,0.3,2.0,42,115
83,replace,9.0,c,C,CNC(=O)c1C1H,CNC(=O)c1c1H,12,replace C at position 9 with c,flow_matching,0.3,2.0,42,115
84,replace,10.0,c,1,CNC(=O)c1c1H,CNC(=O)c1ccH,12,replace 1 at position 10 with c,flow_matching,0.3,2.0,42,115
85,replace,11.0,c,H,CNC(=O)c1ccH,CNC(=O)c1ccc,12,replace H at position 11 with c,flow_matching,0.3,2.0,42,115
86,add,12.0,(,,CNC(=O)c1ccc,CNC(=O)c1ccc(,13,add ( at position 12,flow_matching,0.3,2.0,42,115
87,add,13.0,N,,CNC(=O)c1ccc(,CNC(=O)c1ccc(N,14,add N at position 13,flow_matching,0.3,2.0,42,115
88,add,14.0,C,,CNC(=O)c1ccc(N,CNC(=O)c1ccc(NC,15,add C at position 14,flow_matching,0.3,2.0,42,115
89,add,15.0,(,,CNC(=O)c1ccc(NC,CNC(=O)c1ccc(NC(,16,add ( at position 15,flow_matching,0.3,2.0,42,115
90,add,16.0,=,,CNC(=O)c1ccc(NC(,CNC(=O)c1ccc(NC(=,17,add = at position 16,flow_matching,0.3,2.0,42,115
91,add,17.0,O,,CNC(=O)c1ccc(NC(=,CNC(=O)c1ccc(NC(=O,18,add O at position 17,flow_matching,0.3,2.0,42,115
92,add,18.0,),,CNC(=O)c1ccc(NC(=O,CNC(=O)c1ccc(NC(=O),19,add ) at position 18,flow_matching,0.3,2.0,42,115
93,add,19.0,c,,CNC(=O)c1ccc(NC(=O),CNC(=O)c1ccc(NC(=O)c,20,add c at position 19,flow_matching,0.3,2.0,42,115
94,add,20.0,2,,CNC(=O)c1ccc(NC(=O)c,CNC(=O)c1ccc(NC(=O)c2,21,add 2 at position 20,flow_matching,0.3,2.0,42,115
95,add,21.0,c,,CNC(=O)c1ccc(NC(=O)c2,CNC(=O)c1ccc(NC(=O)c2c,22,add c at position 21,flow_matching,0.3,2.0,42,115
96,add,22.0,s,,CNC(=O)c1ccc(NC(=O)c2c,CNC(=O)c1ccc(NC(=O)c2cs,23,add s at position 22,flow_matching,0.3,2.0,42,115
97,add,23.0,c,,CNC(=O)c1ccc(NC(=O)c2cs,CNC(=O)c1ccc(NC(=O)c2csc,24,add c at position 23,flow_matching,0.3,2.0,42,115
98,add,24.0,(,,CNC(=O)c1ccc(NC(=O)c2csc,CNC(=O)c1ccc(NC(=O)c2csc(,25,add ( at position 24,flow_matching,0.3,2.0,42,115
99,add,25.0,-,,CNC(=O)c1ccc(NC(=O)c2csc(,CNC(=O)c1ccc(NC(=O)c2csc(-,26,add - at position 25,flow_matching,0.3,2.0,42,115
100,add,26.0,c,,CNC(=O)c1ccc(NC(=O)c2csc(-,CNC(=O)c1ccc(NC(=O)c2csc(-c,27,add c at position 26,flow_matching,0.3,2.0,42,115
101,add,27.0,3,,CNC(=O)c1ccc(NC(=O)c2csc(-c,CNC(=O)c1ccc(NC(=O)c2csc(-c3,28,add 3 at position 27,flow_matching,0.3,2.0,42,115
102,add,28.0,c,,CNC(=O)c1ccc(NC(=O)c2csc(-c3,CNC(=O)c1ccc(NC(=O)c2csc(-c3c,29,add c at position 28,flow_matching,0.3,2.0,42,115
103,add,29.0,c,,CNC(=O)c1ccc(NC(=O)c2csc(-c3c,CNC(=O)c1ccc(NC(=O)c2csc(-c3cc,30,add c at position 29,flow_matching,0.3,2.0,42,115
104,add,30.0,c,,CNC(=O)c1ccc(NC(=O)c2csc(-c3cc,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccc,31,add c at position 30,flow_matching,0.3,2.0,42,115
105,add,31.0,c,,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccc,CNC(=O)c1ccc(NC(=O)c2csc(-c3cccc,32,add c at position 31,flow_matching,0.3,2.0,42,115
106,add,32.0,c,,CNC(=O)c1ccc(NC(=O)c2csc(-c3cccc,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc,33,add c at position 32,flow_matching,0.3,2.0,42,115
107,add,33.0,3,,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3,34,add 3 at position 33,flow_matching,0.3,2.0,42,115
108,add,34.0,),,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3),35,add ) at position 34,flow_matching,0.3,2.0,42,115
109,add,35.0,n,,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3),CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n,36,add n at position 35,flow_matching,0.3,2.0,42,115
110,add,36.0,2,,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2,37,add 2 at position 36,flow_matching,0.3,2.0,42,115
111,add,37.0,),,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2),38,add ) at position 37,flow_matching,0.3,2.0,42,115
112,add,38.0,c,,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2),CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2)c,39,add c at position 38,flow_matching,0.3,2.0,42,115
113,add,39.0,c,,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2)c,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2)cc,40,add c at position 39,flow_matching,0.3,2.0,42,115
114,add,40.0,1,,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2)cc,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2)cc1,41,add 1 at position 40,flow_matching,0.3,2.0,42,115
115,add,41.0,"
",,CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2)cc1,"CNC(=O)c1ccc(NC(=O)c2csc(-c3ccccc3)n2)cc1
",42,"add 
 at position 41",flow_matching,0.3,2.0,42,115
