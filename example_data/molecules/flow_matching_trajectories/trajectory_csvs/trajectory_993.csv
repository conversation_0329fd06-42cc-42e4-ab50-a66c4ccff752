step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,57,248
1,add,0.0,),,,),1,add ) at position 0,flow_matching,0.3,2.0,57,248
2,replace,0.0,r,),),r,1,replace ) at position 0 with r,flow_matching,0.3,2.0,57,248
3,remove,0.0,r,,r,,0,remove r from position 0,flow_matching,0.3,2.0,57,248
4,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,57,248
5,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,57,248
6,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,57,248
7,remove,0.0,C,,Cc1,c1,2,remove C from position 0,flow_matching,0.3,2.0,57,248
8,replace,0.0,4,c,c1,41,2,replace c at position 0 with 4,flow_matching,0.3,2.0,57,248
9,replace,0.0,N,4,41,N1,2,replace 4 at position 0 with N,flow_matching,0.3,2.0,57,248
10,replace,0.0,C,N,N1,C1,2,replace N at position 0 with C,flow_matching,0.3,2.0,57,248
11,remove,0.0,C,,C1,1,1,remove C from position 0,flow_matching,0.3,2.0,57,248
12,replace,0.0,7,1,1,7,1,replace 1 at position 0 with 7,flow_matching,0.3,2.0,57,248
13,add,0.0,+,,7,+7,2,add + at position 0,flow_matching,0.3,2.0,57,248
14,replace,0.0,C,+,+7,C7,2,replace + at position 0 with C,flow_matching,0.3,2.0,57,248
15,remove,0.0,C,,C7,7,1,remove C from position 0,flow_matching,0.3,2.0,57,248
16,add,0.0,B,,7,B7,2,add B at position 0,flow_matching,0.3,2.0,57,248
17,add,1.0,1,,B7,B17,3,add 1 at position 1,flow_matching,0.3,2.0,57,248
18,replace,0.0,),B,B17,)17,3,replace B at position 0 with ),flow_matching,0.3,2.0,57,248
19,add,3.0,r,,)17,)17r,4,add r at position 3,flow_matching,0.3,2.0,57,248
20,add,2.0,4,,)17r,)147r,5,add 4 at position 2,flow_matching,0.3,2.0,57,248
21,replace,4.0,/,r,)147r,)147/,5,replace r at position 4 with /,flow_matching,0.3,2.0,57,248
22,replace,4.0,7,/,)147/,)1477,5,replace / at position 4 with 7,flow_matching,0.3,2.0,57,248
23,replace,2.0,I,4,)1477,)1I77,5,replace 4 at position 2 with I,flow_matching,0.3,2.0,57,248
24,replace,0.0,C,),)1I77,C1I77,5,replace ) at position 0 with C,flow_matching,0.3,2.0,57,248
25,replace,1.0,c,1,C1I77,CcI77,5,replace 1 at position 1 with c,flow_matching,0.3,2.0,57,248
26,replace,4.0,O,7,CcI77,CcI7O,5,replace 7 at position 4 with O,flow_matching,0.3,2.0,57,248
27,replace,3.0,-,7,CcI7O,CcI-O,5,replace 7 at position 3 with -,flow_matching,0.3,2.0,57,248
28,replace,2.0,1,I,CcI-O,Cc1-O,5,replace I at position 2 with 1,flow_matching,0.3,2.0,57,248
29,replace,1.0,o,c,Cc1-O,Co1-O,5,replace c at position 1 with o,flow_matching,0.3,2.0,57,248
30,replace,1.0,c,o,Co1-O,Cc1-O,5,replace o at position 1 with c,flow_matching,0.3,2.0,57,248
31,replace,0.0,5,C,Cc1-O,5c1-O,5,replace C at position 0 with 5,flow_matching,0.3,2.0,57,248
32,remove,3.0,-,,5c1-O,5c1O,4,remove - from position 3,flow_matching,0.3,2.0,57,248
33,replace,0.0,C,5,5c1O,Cc1O,4,replace 5 at position 0 with C,flow_matching,0.3,2.0,57,248
34,add,0.0,F,,Cc1O,FCc1O,5,add F at position 0,flow_matching,0.3,2.0,57,248
35,add,1.0,C,,FCc1O,FCCc1O,6,add C at position 1,flow_matching,0.3,2.0,57,248
36,remove,0.0,F,,FCCc1O,CCc1O,5,remove F from position 0,flow_matching,0.3,2.0,57,248
37,add,1.0,\,,CCc1O,C\Cc1O,6,add \ at position 1,flow_matching,0.3,2.0,57,248
38,add,5.0,n,,C\Cc1O,C\Cc1nO,7,add n at position 5,flow_matching,0.3,2.0,57,248
39,add,5.0,=,,C\Cc1nO,C\Cc1=nO,8,add = at position 5,flow_matching,0.3,2.0,57,248
40,replace,1.0,c,\,C\Cc1=nO,CcCc1=nO,8,replace \ at position 1 with c,flow_matching,0.3,2.0,57,248
41,replace,2.0,1,C,CcCc1=nO,Cc1c1=nO,8,replace C at position 2 with 1,flow_matching,0.3,2.0,57,248
42,add,4.0,B,,Cc1c1=nO,Cc1cB1=nO,9,add B at position 4,flow_matching,0.3,2.0,57,248
43,add,8.0,H,,Cc1cB1=nO,Cc1cB1=nHO,10,add H at position 8,flow_matching,0.3,2.0,57,248
44,replace,4.0,c,B,Cc1cB1=nHO,Cc1cc1=nHO,10,replace B at position 4 with c,flow_matching,0.3,2.0,57,248
45,replace,5.0,c,1,Cc1cc1=nHO,Cc1ccc=nHO,10,replace 1 at position 5 with c,flow_matching,0.3,2.0,57,248
46,add,4.0,7,,Cc1ccc=nHO,Cc1c7cc=nHO,11,add 7 at position 4,flow_matching,0.3,2.0,57,248
47,replace,4.0,c,7,Cc1c7cc=nHO,Cc1cccc=nHO,11,replace 7 at position 4 with c,flow_matching,0.3,2.0,57,248
48,replace,6.0,(,c,Cc1cccc=nHO,Cc1ccc(=nHO,11,replace c at position 6 with (,flow_matching,0.3,2.0,57,248
49,add,10.0,s,,Cc1ccc(=nHO,Cc1ccc(=nHsO,12,add s at position 10,flow_matching,0.3,2.0,57,248
50,replace,7.0,N,=,Cc1ccc(=nHsO,Cc1ccc(NnHsO,12,replace = at position 7 with N,flow_matching,0.3,2.0,57,248
51,replace,8.0,2,n,Cc1ccc(NnHsO,Cc1ccc(N2HsO,12,replace n at position 8 with 2,flow_matching,0.3,2.0,57,248
52,replace,9.0,C,H,Cc1ccc(N2HsO,Cc1ccc(N2CsO,12,replace H at position 9 with C,flow_matching,0.3,2.0,57,248
53,replace,4.0,@,c,Cc1ccc(N2CsO,Cc1c@c(N2CsO,12,replace c at position 4 with @,flow_matching,0.3,2.0,57,248
54,replace,4.0,c,@,Cc1c@c(N2CsO,Cc1ccc(N2CsO,12,replace @ at position 4 with c,flow_matching,0.3,2.0,57,248
55,replace,10.0,(,s,Cc1ccc(N2CsO,Cc1ccc(N2C(O,12,replace s at position 10 with (,flow_matching,0.3,2.0,57,248
56,replace,11.0,=,O,Cc1ccc(N2C(O,Cc1ccc(N2C(=,12,replace O at position 11 with =,flow_matching,0.3,2.0,57,248
57,add,12.0,O,,Cc1ccc(N2C(=,Cc1ccc(N2C(=O,13,add O at position 12,flow_matching,0.3,2.0,57,248
58,add,13.0,),,Cc1ccc(N2C(=O,Cc1ccc(N2C(=O),14,add ) at position 13,flow_matching,0.3,2.0,57,248
59,replace,0.0,B,C,Cc1ccc(N2C(=O),Bc1ccc(N2C(=O),14,replace C at position 0 with B,flow_matching,0.3,2.0,57,248
60,remove,5.0,c,,Bc1ccc(N2C(=O),Bc1cc(N2C(=O),13,remove c from position 5,flow_matching,0.3,2.0,57,248
61,remove,1.0,c,,Bc1cc(N2C(=O),B1cc(N2C(=O),12,remove c from position 1,flow_matching,0.3,2.0,57,248
62,replace,0.0,C,B,B1cc(N2C(=O),C1cc(N2C(=O),12,replace B at position 0 with C,flow_matching,0.3,2.0,57,248
63,replace,1.0,c,1,C1cc(N2C(=O),Cccc(N2C(=O),12,replace 1 at position 1 with c,flow_matching,0.3,2.0,57,248
64,add,9.0,c,,Cccc(N2C(=O),Cccc(N2C(c=O),13,add c at position 9,flow_matching,0.3,2.0,57,248
65,replace,11.0,o,O,Cccc(N2C(c=O),Cccc(N2C(c=o),13,replace O at position 11 with o,flow_matching,0.3,2.0,57,248
66,remove,9.0,c,,Cccc(N2C(c=o),Cccc(N2C(=o),12,remove c from position 9,flow_matching,0.3,2.0,57,248
67,add,3.0,4,,Cccc(N2C(=o),Ccc4c(N2C(=o),13,add 4 at position 3,flow_matching,0.3,2.0,57,248
68,add,2.0,/,,Ccc4c(N2C(=o),Cc/c4c(N2C(=o),14,add / at position 2,flow_matching,0.3,2.0,57,248
69,replace,13.0,o,),Cc/c4c(N2C(=o),Cc/c4c(N2C(=oo,14,replace ) at position 13 with o,flow_matching,0.3,2.0,57,248
70,add,14.0,4,,Cc/c4c(N2C(=oo,Cc/c4c(N2C(=oo4,15,add 4 at position 14,flow_matching,0.3,2.0,57,248
71,remove,0.0,C,,Cc/c4c(N2C(=oo4,c/c4c(N2C(=oo4,14,remove C from position 0,flow_matching,0.3,2.0,57,248
72,replace,0.0,C,c,c/c4c(N2C(=oo4,C/c4c(N2C(=oo4,14,replace c at position 0 with C,flow_matching,0.3,2.0,57,248
73,add,10.0,c,,C/c4c(N2C(=oo4,C/c4c(N2C(c=oo4,15,add c at position 10,flow_matching,0.3,2.0,57,248
74,replace,1.0,c,/,C/c4c(N2C(c=oo4,Ccc4c(N2C(c=oo4,15,replace / at position 1 with c,flow_matching,0.3,2.0,57,248
75,replace,4.0,2,c,Ccc4c(N2C(c=oo4,Ccc42(N2C(c=oo4,15,replace c at position 4 with 2,flow_matching,0.3,2.0,57,248
76,replace,2.0,1,c,Ccc42(N2C(c=oo4,Cc142(N2C(c=oo4,15,replace c at position 2 with 1,flow_matching,0.3,2.0,57,248
77,replace,3.0,c,4,Cc142(N2C(c=oo4,Cc1c2(N2C(c=oo4,15,replace 4 at position 3 with c,flow_matching,0.3,2.0,57,248
78,add,0.0,\,,Cc1c2(N2C(c=oo4,\Cc1c2(N2C(c=oo4,16,add \ at position 0,flow_matching,0.3,2.0,57,248
79,replace,12.0,S,=,\Cc1c2(N2C(c=oo4,\Cc1c2(N2C(cSoo4,16,replace = at position 12 with S,flow_matching,0.3,2.0,57,248
80,replace,2.0,6,c,\Cc1c2(N2C(cSoo4,\C61c2(N2C(cSoo4,16,replace c at position 2 with 6,flow_matching,0.3,2.0,57,248
81,replace,0.0,C,\,\C61c2(N2C(cSoo4,CC61c2(N2C(cSoo4,16,replace \ at position 0 with C,flow_matching,0.3,2.0,57,248
82,add,9.0,[,,CC61c2(N2C(cSoo4,CC61c2(N2[C(cSoo4,17,add [ at position 9,flow_matching,0.3,2.0,57,248
83,replace,1.0,c,C,CC61c2(N2[C(cSoo4,Cc61c2(N2[C(cSoo4,17,replace C at position 1 with c,flow_matching,0.3,2.0,57,248
84,remove,8.0,2,,Cc61c2(N2[C(cSoo4,Cc61c2(N[C(cSoo4,16,remove 2 from position 8,flow_matching,0.3,2.0,57,248
85,remove,2.0,6,,Cc61c2(N[C(cSoo4,Cc1c2(N[C(cSoo4,15,remove 6 from position 2,flow_matching,0.3,2.0,57,248
86,replace,9.0,2,(,Cc1c2(N[C(cSoo4,Cc1c2(N[C2cSoo4,15,replace ( at position 9 with 2,flow_matching,0.3,2.0,57,248
87,replace,9.0,],2,Cc1c2(N[C2cSoo4,Cc1c2(N[C]cSoo4,15,replace 2 at position 9 with ],flow_matching,0.3,2.0,57,248
88,replace,1.0,o,c,Cc1c2(N[C]cSoo4,Co1c2(N[C]cSoo4,15,replace c at position 1 with o,flow_matching,0.3,2.0,57,248
89,remove,3.0,c,,Co1c2(N[C]cSoo4,Co12(N[C]cSoo4,14,remove c from position 3,flow_matching,0.3,2.0,57,248
90,remove,9.0,c,,Co12(N[C]cSoo4,Co12(N[C]Soo4,13,remove c from position 9,flow_matching,0.3,2.0,57,248
91,add,1.0,S,,Co12(N[C]Soo4,CSo12(N[C]Soo4,14,add S at position 1,flow_matching,0.3,2.0,57,248
92,add,7.0,F,,CSo12(N[C]Soo4,CSo12(NF[C]Soo4,15,add F at position 7,flow_matching,0.3,2.0,57,248
93,remove,9.0,C,,CSo12(NF[C]Soo4,CSo12(NF[]Soo4,14,remove C from position 9,flow_matching,0.3,2.0,57,248
94,replace,3.0,H,1,CSo12(NF[]Soo4,CSoH2(NF[]Soo4,14,replace 1 at position 3 with H,flow_matching,0.3,2.0,57,248
95,add,7.0,s,,CSoH2(NF[]Soo4,CSoH2(NsF[]Soo4,15,add s at position 7,flow_matching,0.3,2.0,57,248
96,remove,1.0,S,,CSoH2(NsF[]Soo4,CoH2(NsF[]Soo4,14,remove S from position 1,flow_matching,0.3,2.0,57,248
97,remove,8.0,[,,CoH2(NsF[]Soo4,CoH2(NsF]Soo4,13,remove [ from position 8,flow_matching,0.3,2.0,57,248
98,remove,10.0,o,,CoH2(NsF]Soo4,CoH2(NsF]So4,12,remove o from position 10,flow_matching,0.3,2.0,57,248
99,replace,1.0,c,o,CoH2(NsF]So4,CcH2(NsF]So4,12,replace o at position 1 with c,flow_matching,0.3,2.0,57,248
100,remove,9.0,S,,CcH2(NsF]So4,CcH2(NsF]o4,11,remove S from position 9,flow_matching,0.3,2.0,57,248
101,replace,2.0,l,H,CcH2(NsF]o4,Ccl2(NsF]o4,11,replace H at position 2 with l,flow_matching,0.3,2.0,57,248
102,add,10.0,I,,Ccl2(NsF]o4,Ccl2(NsF]oI4,12,add I at position 10,flow_matching,0.3,2.0,57,248
103,add,1.0,N,,Ccl2(NsF]oI4,CNcl2(NsF]oI4,13,add N at position 1,flow_matching,0.3,2.0,57,248
104,replace,1.0,c,N,CNcl2(NsF]oI4,Cccl2(NsF]oI4,13,replace N at position 1 with c,flow_matching,0.3,2.0,57,248
105,remove,0.0,C,,Cccl2(NsF]oI4,ccl2(NsF]oI4,12,remove C from position 0,flow_matching,0.3,2.0,57,248
106,add,8.0,C,,ccl2(NsF]oI4,ccl2(NsFC]oI4,13,add C at position 8,flow_matching,0.3,2.0,57,248
107,replace,6.0,1,s,ccl2(NsFC]oI4,ccl2(N1FC]oI4,13,replace s at position 6 with 1,flow_matching,0.3,2.0,57,248
108,replace,8.0,6,C,ccl2(N1FC]oI4,ccl2(N1F6]oI4,13,replace C at position 8 with 6,flow_matching,0.3,2.0,57,248
109,add,5.0,l,,ccl2(N1F6]oI4,ccl2(lN1F6]oI4,14,add l at position 5,flow_matching,0.3,2.0,57,248
110,remove,9.0,6,,ccl2(lN1F6]oI4,ccl2(lN1F]oI4,13,remove 6 from position 9,flow_matching,0.3,2.0,57,248
111,replace,0.0,C,c,ccl2(lN1F]oI4,Ccl2(lN1F]oI4,13,replace c at position 0 with C,flow_matching,0.3,2.0,57,248
112,remove,12.0,4,,Ccl2(lN1F]oI4,Ccl2(lN1F]oI,12,remove 4 from position 12,flow_matching,0.3,2.0,57,248
113,add,0.0,6,,Ccl2(lN1F]oI,6Ccl2(lN1F]oI,13,add 6 at position 0,flow_matching,0.3,2.0,57,248
114,remove,3.0,l,,6Ccl2(lN1F]oI,6Cc2(lN1F]oI,12,remove l from position 3,flow_matching,0.3,2.0,57,248
115,add,3.0,#,,6Cc2(lN1F]oI,6Cc#2(lN1F]oI,13,add # at position 3,flow_matching,0.3,2.0,57,248
116,add,7.0,N,,6Cc#2(lN1F]oI,6Cc#2(lNN1F]oI,14,add N at position 7,flow_matching,0.3,2.0,57,248
117,replace,0.0,C,6,6Cc#2(lNN1F]oI,CCc#2(lNN1F]oI,14,replace 6 at position 0 with C,flow_matching,0.3,2.0,57,248
118,add,1.0,\,,CCc#2(lNN1F]oI,C\Cc#2(lNN1F]oI,15,add \ at position 1,flow_matching,0.3,2.0,57,248
119,add,0.0,+,,C\Cc#2(lNN1F]oI,+C\Cc#2(lNN1F]oI,16,add + at position 0,flow_matching,0.3,2.0,57,248
120,replace,10.0,o,N,+C\Cc#2(lNN1F]oI,+C\Cc#2(lNo1F]oI,16,replace N at position 10 with o,flow_matching,0.3,2.0,57,248
121,remove,5.0,#,,+C\Cc#2(lNo1F]oI,+C\Cc2(lNo1F]oI,15,remove # from position 5,flow_matching,0.3,2.0,57,248
122,replace,0.0,5,+,+C\Cc2(lNo1F]oI,5C\Cc2(lNo1F]oI,15,replace + at position 0 with 5,flow_matching,0.3,2.0,57,248
123,remove,2.0,\,,5C\Cc2(lNo1F]oI,5CCc2(lNo1F]oI,14,remove \ from position 2,flow_matching,0.3,2.0,57,248
124,replace,7.0,),N,5CCc2(lNo1F]oI,5CCc2(l)o1F]oI,14,replace N at position 7 with ),flow_matching,0.3,2.0,57,248
125,add,12.0,H,,5CCc2(l)o1F]oI,5CCc2(l)o1F]HoI,15,add H at position 12,flow_matching,0.3,2.0,57,248
126,replace,0.0,C,5,5CCc2(l)o1F]HoI,CCCc2(l)o1F]HoI,15,replace 5 at position 0 with C,flow_matching,0.3,2.0,57,248
127,replace,1.0,c,C,CCCc2(l)o1F]HoI,CcCc2(l)o1F]HoI,15,replace C at position 1 with c,flow_matching,0.3,2.0,57,248
128,replace,2.0,1,C,CcCc2(l)o1F]HoI,Cc1c2(l)o1F]HoI,15,replace C at position 2 with 1,flow_matching,0.3,2.0,57,248
129,remove,12.0,H,,Cc1c2(l)o1F]HoI,Cc1c2(l)o1F]oI,14,remove H from position 12,flow_matching,0.3,2.0,57,248
130,replace,5.0,=,(,Cc1c2(l)o1F]oI,Cc1c2=l)o1F]oI,14,replace ( at position 5 with =,flow_matching,0.3,2.0,57,248
131,add,10.0,/,,Cc1c2=l)o1F]oI,Cc1c2=l)o1/F]oI,15,add / at position 10,flow_matching,0.3,2.0,57,248
132,add,11.0,+,,Cc1c2=l)o1/F]oI,Cc1c2=l)o1/+F]oI,16,add + at position 11,flow_matching,0.3,2.0,57,248
133,remove,5.0,=,,Cc1c2=l)o1/+F]oI,Cc1c2l)o1/+F]oI,15,remove = from position 5,flow_matching,0.3,2.0,57,248
134,replace,4.0,c,2,Cc1c2l)o1/+F]oI,Cc1ccl)o1/+F]oI,15,replace 2 at position 4 with c,flow_matching,0.3,2.0,57,248
135,replace,5.0,c,l,Cc1ccl)o1/+F]oI,Cc1ccc)o1/+F]oI,15,replace l at position 5 with c,flow_matching,0.3,2.0,57,248
136,add,7.0,3,,Cc1ccc)o1/+F]oI,Cc1ccc)3o1/+F]oI,16,add 3 at position 7,flow_matching,0.3,2.0,57,248
137,replace,6.0,(,),Cc1ccc)3o1/+F]oI,Cc1ccc(3o1/+F]oI,16,replace ) at position 6 with (,flow_matching,0.3,2.0,57,248
138,replace,7.0,N,3,Cc1ccc(3o1/+F]oI,Cc1ccc(No1/+F]oI,16,replace 3 at position 7 with N,flow_matching,0.3,2.0,57,248
139,replace,8.0,2,o,Cc1ccc(No1/+F]oI,Cc1ccc(N21/+F]oI,16,replace o at position 8 with 2,flow_matching,0.3,2.0,57,248
140,replace,9.0,C,1,Cc1ccc(N21/+F]oI,Cc1ccc(N2C/+F]oI,16,replace 1 at position 9 with C,flow_matching,0.3,2.0,57,248
141,replace,13.0,3,],Cc1ccc(N2C/+F]oI,Cc1ccc(N2C/+F3oI,16,replace ] at position 13 with 3,flow_matching,0.3,2.0,57,248
142,replace,4.0,r,c,Cc1ccc(N2C/+F3oI,Cc1crc(N2C/+F3oI,16,replace c at position 4 with r,flow_matching,0.3,2.0,57,248
143,replace,0.0,7,C,Cc1crc(N2C/+F3oI,7c1crc(N2C/+F3oI,16,replace C at position 0 with 7,flow_matching,0.3,2.0,57,248
144,remove,13.0,3,,7c1crc(N2C/+F3oI,7c1crc(N2C/+FoI,15,remove 3 from position 13,flow_matching,0.3,2.0,57,248
145,replace,13.0,5,o,7c1crc(N2C/+FoI,7c1crc(N2C/+F5I,15,replace o at position 13 with 5,flow_matching,0.3,2.0,57,248
146,replace,3.0,1,c,7c1crc(N2C/+F5I,7c11rc(N2C/+F5I,15,replace c at position 3 with 1,flow_matching,0.3,2.0,57,248
147,remove,3.0,1,,7c11rc(N2C/+F5I,7c1rc(N2C/+F5I,14,remove 1 from position 3,flow_matching,0.3,2.0,57,248
148,replace,0.0,C,7,7c1rc(N2C/+F5I,Cc1rc(N2C/+F5I,14,replace 7 at position 0 with C,flow_matching,0.3,2.0,57,248
149,add,9.0,],,Cc1rc(N2C/+F5I,Cc1rc(N2C]/+F5I,15,add ] at position 9,flow_matching,0.3,2.0,57,248
150,add,13.0,6,,Cc1rc(N2C]/+F5I,Cc1rc(N2C]/+F65I,16,add 6 at position 13,flow_matching,0.3,2.0,57,248
151,add,1.0,o,,Cc1rc(N2C]/+F65I,Coc1rc(N2C]/+F65I,17,add o at position 1,flow_matching,0.3,2.0,57,248
152,replace,15.0,],5,Coc1rc(N2C]/+F65I,Coc1rc(N2C]/+F6]I,17,replace 5 at position 15 with ],flow_matching,0.3,2.0,57,248
153,replace,1.0,c,o,Coc1rc(N2C]/+F6]I,Ccc1rc(N2C]/+F6]I,17,replace o at position 1 with c,flow_matching,0.3,2.0,57,248
154,replace,9.0,I,C,Ccc1rc(N2C]/+F6]I,Ccc1rc(N2I]/+F6]I,17,replace C at position 9 with I,flow_matching,0.3,2.0,57,248
155,add,17.0,2,,Ccc1rc(N2I]/+F6]I,Ccc1rc(N2I]/+F6]I2,18,add 2 at position 17,flow_matching,0.3,2.0,57,248
156,replace,3.0,C,1,Ccc1rc(N2I]/+F6]I2,CccCrc(N2I]/+F6]I2,18,replace 1 at position 3 with C,flow_matching,0.3,2.0,57,248
157,add,17.0,4,,CccCrc(N2I]/+F6]I2,CccCrc(N2I]/+F6]I42,19,add 4 at position 17,flow_matching,0.3,2.0,57,248
158,add,1.0,#,,CccCrc(N2I]/+F6]I42,C#ccCrc(N2I]/+F6]I42,20,add # at position 1,flow_matching,0.3,2.0,57,248
159,add,19.0,I,,C#ccCrc(N2I]/+F6]I42,C#ccCrc(N2I]/+F6]I4I2,21,add I at position 19,flow_matching,0.3,2.0,57,248
160,add,7.0,4,,C#ccCrc(N2I]/+F6]I4I2,C#ccCrc4(N2I]/+F6]I4I2,22,add 4 at position 7,flow_matching,0.3,2.0,57,248
161,remove,7.0,4,,C#ccCrc4(N2I]/+F6]I4I2,C#ccCrc(N2I]/+F6]I4I2,21,remove 4 from position 7,flow_matching,0.3,2.0,57,248
162,remove,1.0,#,,C#ccCrc(N2I]/+F6]I4I2,CccCrc(N2I]/+F6]I4I2,20,remove # from position 1,flow_matching,0.3,2.0,57,248
163,replace,18.0,r,I,CccCrc(N2I]/+F6]I4I2,CccCrc(N2I]/+F6]I4r2,20,replace I at position 18 with r,flow_matching,0.3,2.0,57,248
164,replace,18.0,[,r,CccCrc(N2I]/+F6]I4r2,CccCrc(N2I]/+F6]I4[2,20,replace r at position 18 with [,flow_matching,0.3,2.0,57,248
165,add,18.0,1,,CccCrc(N2I]/+F6]I4[2,CccCrc(N2I]/+F6]I41[2,21,add 1 at position 18,flow_matching,0.3,2.0,57,248
166,remove,13.0,F,,CccCrc(N2I]/+F6]I41[2,CccCrc(N2I]/+6]I41[2,20,remove F from position 13,flow_matching,0.3,2.0,57,248
167,remove,14.0,],,CccCrc(N2I]/+6]I41[2,CccCrc(N2I]/+6I41[2,19,remove ] from position 14,flow_matching,0.3,2.0,57,248
168,replace,14.0,3,I,CccCrc(N2I]/+6I41[2,CccCrc(N2I]/+6341[2,19,replace I at position 14 with 3,flow_matching,0.3,2.0,57,248
169,add,18.0,(,,CccCrc(N2I]/+6341[2,CccCrc(N2I]/+6341[(2,20,add ( at position 18,flow_matching,0.3,2.0,57,248
170,add,1.0,B,,CccCrc(N2I]/+6341[(2,CBccCrc(N2I]/+6341[(2,21,add B at position 1,flow_matching,0.3,2.0,57,248
171,add,8.0,C,,CBccCrc(N2I]/+6341[(2,CBccCrc(CN2I]/+6341[(2,22,add C at position 8,flow_matching,0.3,2.0,57,248
172,add,22.0,5,,CBccCrc(CN2I]/+6341[(2,CBccCrc(CN2I]/+6341[(25,23,add 5 at position 22,flow_matching,0.3,2.0,57,248
173,add,23.0,#,,CBccCrc(CN2I]/+6341[(25,CBccCrc(CN2I]/+6341[(25#,24,add # at position 23,flow_matching,0.3,2.0,57,248
174,remove,8.0,C,,CBccCrc(CN2I]/+6341[(25#,CBccCrc(N2I]/+6341[(25#,23,remove C from position 8,flow_matching,0.3,2.0,57,248
175,add,17.0,),,CBccCrc(N2I]/+6341[(25#,CBccCrc(N2I]/+634)1[(25#,24,add ) at position 17,flow_matching,0.3,2.0,57,248
176,add,16.0,7,,CBccCrc(N2I]/+634)1[(25#,CBccCrc(N2I]/+6374)1[(25#,25,add 7 at position 16,flow_matching,0.3,2.0,57,248
177,add,19.0,n,,CBccCrc(N2I]/+6374)1[(25#,CBccCrc(N2I]/+6374)n1[(25#,26,add n at position 19,flow_matching,0.3,2.0,57,248
178,add,4.0,N,,CBccCrc(N2I]/+6374)n1[(25#,CBccNCrc(N2I]/+6374)n1[(25#,27,add N at position 4,flow_matching,0.3,2.0,57,248
179,add,12.0,r,,CBccNCrc(N2I]/+6374)n1[(25#,CBccNCrc(N2Ir]/+6374)n1[(25#,28,add r at position 12,flow_matching,0.3,2.0,57,248
180,add,24.0,S,,CBccNCrc(N2Ir]/+6374)n1[(25#,CBccNCrc(N2Ir]/+6374)n1[S(25#,29,add S at position 24,flow_matching,0.3,2.0,57,248
181,replace,1.0,c,B,CBccNCrc(N2Ir]/+6374)n1[S(25#,CcccNCrc(N2Ir]/+6374)n1[S(25#,29,replace B at position 1 with c,flow_matching,0.3,2.0,57,248
182,replace,2.0,1,c,CcccNCrc(N2Ir]/+6374)n1[S(25#,Cc1cNCrc(N2Ir]/+6374)n1[S(25#,29,replace c at position 2 with 1,flow_matching,0.3,2.0,57,248
183,replace,4.0,c,N,Cc1cNCrc(N2Ir]/+6374)n1[S(25#,Cc1ccCrc(N2Ir]/+6374)n1[S(25#,29,replace N at position 4 with c,flow_matching,0.3,2.0,57,248
184,replace,5.0,c,C,Cc1ccCrc(N2Ir]/+6374)n1[S(25#,Cc1cccrc(N2Ir]/+6374)n1[S(25#,29,replace C at position 5 with c,flow_matching,0.3,2.0,57,248
185,remove,18.0,7,,Cc1cccrc(N2Ir]/+6374)n1[S(25#,Cc1cccrc(N2Ir]/+634)n1[S(25#,28,remove 7 from position 18,flow_matching,0.3,2.0,57,248
186,replace,6.0,(,r,Cc1cccrc(N2Ir]/+634)n1[S(25#,Cc1ccc(c(N2Ir]/+634)n1[S(25#,28,replace r at position 6 with (,flow_matching,0.3,2.0,57,248
187,replace,0.0,S,C,Cc1ccc(c(N2Ir]/+634)n1[S(25#,Sc1ccc(c(N2Ir]/+634)n1[S(25#,28,replace C at position 0 with S,flow_matching,0.3,2.0,57,248
188,remove,17.0,3,,Sc1ccc(c(N2Ir]/+634)n1[S(25#,Sc1ccc(c(N2Ir]/+64)n1[S(25#,27,remove 3 from position 17,flow_matching,0.3,2.0,57,248
189,replace,22.0,\,S,Sc1ccc(c(N2Ir]/+64)n1[S(25#,Sc1ccc(c(N2Ir]/+64)n1[\(25#,27,replace S at position 22 with \,flow_matching,0.3,2.0,57,248
190,replace,18.0,#,),Sc1ccc(c(N2Ir]/+64)n1[\(25#,Sc1ccc(c(N2Ir]/+64#n1[\(25#,27,replace ) at position 18 with #,flow_matching,0.3,2.0,57,248
191,remove,1.0,c,,Sc1ccc(c(N2Ir]/+64#n1[\(25#,S1ccc(c(N2Ir]/+64#n1[\(25#,26,remove c from position 1,flow_matching,0.3,2.0,57,248
192,remove,16.0,4,,S1ccc(c(N2Ir]/+64#n1[\(25#,S1ccc(c(N2Ir]/+6#n1[\(25#,25,remove 4 from position 16,flow_matching,0.3,2.0,57,248
193,remove,4.0,c,,S1ccc(c(N2Ir]/+6#n1[\(25#,S1cc(c(N2Ir]/+6#n1[\(25#,24,remove c from position 4,flow_matching,0.3,2.0,57,248
194,remove,19.0,\,,S1cc(c(N2Ir]/+6#n1[\(25#,S1cc(c(N2Ir]/+6#n1[(25#,23,remove \ from position 19,flow_matching,0.3,2.0,57,248
195,replace,5.0,S,c,S1cc(c(N2Ir]/+6#n1[(25#,S1cc(S(N2Ir]/+6#n1[(25#,23,replace c at position 5 with S,flow_matching,0.3,2.0,57,248
196,replace,0.0,C,S,S1cc(S(N2Ir]/+6#n1[(25#,C1cc(S(N2Ir]/+6#n1[(25#,23,replace S at position 0 with C,flow_matching,0.3,2.0,57,248
197,add,9.0,6,,C1cc(S(N2Ir]/+6#n1[(25#,C1cc(S(N26Ir]/+6#n1[(25#,24,add 6 at position 9,flow_matching,0.3,2.0,57,248
198,replace,1.0,c,1,C1cc(S(N26Ir]/+6#n1[(25#,Cccc(S(N26Ir]/+6#n1[(25#,24,replace 1 at position 1 with c,flow_matching,0.3,2.0,57,248
199,replace,12.0,O,],Cccc(S(N26Ir]/+6#n1[(25#,Cccc(S(N26IrO/+6#n1[(25#,24,replace ] at position 12 with O,flow_matching,0.3,2.0,57,248
200,replace,2.0,1,c,Cccc(S(N26IrO/+6#n1[(25#,Cc1c(S(N26IrO/+6#n1[(25#,24,replace c at position 2 with 1,flow_matching,0.3,2.0,57,248
201,replace,4.0,c,(,Cc1c(S(N26IrO/+6#n1[(25#,Cc1ccS(N26IrO/+6#n1[(25#,24,replace ( at position 4 with c,flow_matching,0.3,2.0,57,248
202,replace,5.0,c,S,Cc1ccS(N26IrO/+6#n1[(25#,Cc1ccc(N26IrO/+6#n1[(25#,24,replace S at position 5 with c,flow_matching,0.3,2.0,57,248
203,replace,9.0,C,6,Cc1ccc(N26IrO/+6#n1[(25#,Cc1ccc(N2CIrO/+6#n1[(25#,24,replace 6 at position 9 with C,flow_matching,0.3,2.0,57,248
204,replace,10.0,(,I,Cc1ccc(N2CIrO/+6#n1[(25#,Cc1ccc(N2C(rO/+6#n1[(25#,24,replace I at position 10 with (,flow_matching,0.3,2.0,57,248
205,replace,11.0,=,r,Cc1ccc(N2C(rO/+6#n1[(25#,Cc1ccc(N2C(=O/+6#n1[(25#,24,replace r at position 11 with =,flow_matching,0.3,2.0,57,248
206,replace,13.0,),/,Cc1ccc(N2C(=O/+6#n1[(25#,Cc1ccc(N2C(=O)+6#n1[(25#,24,replace / at position 13 with ),flow_matching,0.3,2.0,57,248
207,replace,14.0,[,+,Cc1ccc(N2C(=O)+6#n1[(25#,Cc1ccc(N2C(=O)[6#n1[(25#,24,replace + at position 14 with [,flow_matching,0.3,2.0,57,248
208,replace,15.0,C,6,Cc1ccc(N2C(=O)[6#n1[(25#,Cc1ccc(N2C(=O)[C#n1[(25#,24,replace 6 at position 15 with C,flow_matching,0.3,2.0,57,248
209,replace,16.0,@,#,Cc1ccc(N2C(=O)[C#n1[(25#,Cc1ccc(N2C(=O)[C@n1[(25#,24,replace # at position 16 with @,flow_matching,0.3,2.0,57,248
210,replace,17.0,@,n,Cc1ccc(N2C(=O)[C@n1[(25#,Cc1ccc(N2C(=O)[C@@1[(25#,24,replace n at position 17 with @,flow_matching,0.3,2.0,57,248
211,replace,18.0,H,1,Cc1ccc(N2C(=O)[C@@1[(25#,Cc1ccc(N2C(=O)[C@@H[(25#,24,replace 1 at position 18 with H,flow_matching,0.3,2.0,57,248
212,replace,19.0,],[,Cc1ccc(N2C(=O)[C@@H[(25#,Cc1ccc(N2C(=O)[C@@H](25#,24,replace [ at position 19 with ],flow_matching,0.3,2.0,57,248
213,replace,21.0,C,2,Cc1ccc(N2C(=O)[C@@H](25#,Cc1ccc(N2C(=O)[C@@H](C5#,24,replace 2 at position 21 with C,flow_matching,0.3,2.0,57,248
214,replace,22.0,c,5,Cc1ccc(N2C(=O)[C@@H](C5#,Cc1ccc(N2C(=O)[C@@H](Cc#,24,replace 5 at position 22 with c,flow_matching,0.3,2.0,57,248
215,replace,23.0,3,#,Cc1ccc(N2C(=O)[C@@H](Cc#,Cc1ccc(N2C(=O)[C@@H](Cc3,24,replace # at position 23 with 3,flow_matching,0.3,2.0,57,248
216,add,24.0,c,,Cc1ccc(N2C(=O)[C@@H](Cc3,Cc1ccc(N2C(=O)[C@@H](Cc3c,25,add c at position 24,flow_matching,0.3,2.0,57,248
217,add,25.0,c,,Cc1ccc(N2C(=O)[C@@H](Cc3c,Cc1ccc(N2C(=O)[C@@H](Cc3cc,26,add c at position 25,flow_matching,0.3,2.0,57,248
218,add,26.0,c,,Cc1ccc(N2C(=O)[C@@H](Cc3cc,Cc1ccc(N2C(=O)[C@@H](Cc3ccc,27,add c at position 26,flow_matching,0.3,2.0,57,248
219,add,27.0,c,,Cc1ccc(N2C(=O)[C@@H](Cc3ccc,Cc1ccc(N2C(=O)[C@@H](Cc3cccc,28,add c at position 27,flow_matching,0.3,2.0,57,248
220,add,28.0,(,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(,29,add ( at position 28,flow_matching,0.3,2.0,57,248
221,add,29.0,C,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C,30,add C at position 29,flow_matching,0.3,2.0,57,248
222,add,30.0,),,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C),31,add ) at position 30,flow_matching,0.3,2.0,57,248
223,add,31.0,c,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C),Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c,32,add c at position 31,flow_matching,0.3,2.0,57,248
224,add,32.0,3,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3,33,add 3 at position 32,flow_matching,0.3,2.0,57,248
225,add,33.0,),,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3),34,add ) at position 33,flow_matching,0.3,2.0,57,248
226,add,34.0,S,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3),Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S,35,add S at position 34,flow_matching,0.3,2.0,57,248
227,add,35.0,/,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/,36,add / at position 35,flow_matching,0.3,2.0,57,248
228,add,36.0,C,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C,37,add C at position 36,flow_matching,0.3,2.0,57,248
229,add,37.0,2,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2,38,add 2 at position 37,flow_matching,0.3,2.0,57,248
230,add,38.0,=,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=,39,add = at position 38,flow_matching,0.3,2.0,57,248
231,add,39.0,C,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C,40,add C at position 39,flow_matching,0.3,2.0,57,248
232,add,40.0,(,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(,41,add ( at position 40,flow_matching,0.3,2.0,57,248
233,add,41.0,/,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/,42,add / at position 41,flow_matching,0.3,2.0,57,248
234,add,42.0,C,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C,43,add C at position 42,flow_matching,0.3,2.0,57,248
235,add,43.0,#,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#,44,add # at position 43,flow_matching,0.3,2.0,57,248
236,add,44.0,N,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N,45,add N at position 44,flow_matching,0.3,2.0,57,248
237,add,45.0,),,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N),46,add ) at position 45,flow_matching,0.3,2.0,57,248
238,add,46.0,C,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N),Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C,47,add C at position 46,flow_matching,0.3,2.0,57,248
239,add,47.0,(,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(,48,add ( at position 47,flow_matching,0.3,2.0,57,248
240,add,48.0,N,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N,49,add N at position 48,flow_matching,0.3,2.0,57,248
241,add,49.0,),,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N),50,add ) at position 49,flow_matching,0.3,2.0,57,248
242,add,50.0,=,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N),Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=,51,add = at position 50,flow_matching,0.3,2.0,57,248
243,add,51.0,O,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O,52,add O at position 51,flow_matching,0.3,2.0,57,248
244,add,52.0,),,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O),53,add ) at position 52,flow_matching,0.3,2.0,57,248
245,add,53.0,c,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O),Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O)c,54,add c at position 53,flow_matching,0.3,2.0,57,248
246,add,54.0,c,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O)c,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O)cc,55,add c at position 54,flow_matching,0.3,2.0,57,248
247,add,55.0,1,,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O)cc,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O)cc1,56,add 1 at position 55,flow_matching,0.3,2.0,57,248
248,add,56.0,"
",,Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O)cc1,"Cc1ccc(N2C(=O)[C@@H](Cc3cccc(C)c3)S/C2=C(/C#N)C(N)=O)cc1
",57,"add 
 at position 56",flow_matching,0.3,2.0,57,248
