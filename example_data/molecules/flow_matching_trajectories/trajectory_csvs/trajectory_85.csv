step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,41,188
1,add,0.0,n,,,n,1,add n at position 0,flow_matching,0.3,2.0,41,188
2,add,0.0,),,n,)n,2,add ) at position 0,flow_matching,0.3,2.0,41,188
3,replace,0.0,C,),)n,Cn,2,replace ) at position 0 with C,flow_matching,0.3,2.0,41,188
4,add,0.0,B,,Cn,BCn,3,add B at position 0,flow_matching,0.3,2.0,41,188
5,replace,0.0,o,B,BCn,oCn,3,replace B at position 0 with o,flow_matching,0.3,2.0,41,188
6,remove,2.0,n,,oCn,oC,2,remove n from position 2,flow_matching,0.3,2.0,41,188
7,remove,1.0,C,,oC,o,1,remove C from position 1,flow_matching,0.3,2.0,41,188
8,replace,0.0,F,o,o,F,1,replace o at position 0 with F,flow_matching,0.3,2.0,41,188
9,remove,0.0,F,,F,,0,remove F from position 0,flow_matching,0.3,2.0,41,188
10,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,41,188
11,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,41,188
12,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,41,188
13,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,41,188
14,add,1.0,6,,CC,C6C,3,add 6 at position 1,flow_matching,0.3,2.0,41,188
15,add,3.0,I,,C6C,C6CI,4,add I at position 3,flow_matching,0.3,2.0,41,188
16,remove,1.0,6,,C6CI,CCI,3,remove 6 from position 1,flow_matching,0.3,2.0,41,188
17,replace,2.0,c,I,CCI,CCc,3,replace I at position 2 with c,flow_matching,0.3,2.0,41,188
18,add,3.0,1,,CCc,CCc1,4,add 1 at position 3,flow_matching,0.3,2.0,41,188
19,add,0.0,(,,CCc1,(CCc1,5,add ( at position 0,flow_matching,0.3,2.0,41,188
20,replace,1.0,F,C,(CCc1,(FCc1,5,replace C at position 1 with F,flow_matching,0.3,2.0,41,188
21,add,0.0,4,,(FCc1,4(FCc1,6,add 4 at position 0,flow_matching,0.3,2.0,41,188
22,add,1.0,#,,4(FCc1,4#(FCc1,7,add # at position 1,flow_matching,0.3,2.0,41,188
23,replace,0.0,C,4,4#(FCc1,C#(FCc1,7,replace 4 at position 0 with C,flow_matching,0.3,2.0,41,188
24,add,5.0,F,,C#(FCc1,C#(FCFc1,8,add F at position 5,flow_matching,0.3,2.0,41,188
25,replace,1.0,C,#,C#(FCFc1,CC(FCFc1,8,replace # at position 1 with C,flow_matching,0.3,2.0,41,188
26,replace,2.0,c,(,CC(FCFc1,CCcFCFc1,8,replace ( at position 2 with c,flow_matching,0.3,2.0,41,188
27,add,1.0,+,,CCcFCFc1,C+CcFCFc1,9,add + at position 1,flow_matching,0.3,2.0,41,188
28,add,6.0,3,,C+CcFCFc1,C+CcFC3Fc1,10,add 3 at position 6,flow_matching,0.3,2.0,41,188
29,add,0.0,1,,C+CcFC3Fc1,1C+CcFC3Fc1,11,add 1 at position 0,flow_matching,0.3,2.0,41,188
30,remove,10.0,1,,1C+CcFC3Fc1,1C+CcFC3Fc,10,remove 1 from position 10,flow_matching,0.3,2.0,41,188
31,add,2.0,+,,1C+CcFC3Fc,1C++CcFC3Fc,11,add + at position 2,flow_matching,0.3,2.0,41,188
32,replace,0.0,C,1,1C++CcFC3Fc,CC++CcFC3Fc,11,replace 1 at position 0 with C,flow_matching,0.3,2.0,41,188
33,add,8.0,O,,CC++CcFC3Fc,CC++CcFCO3Fc,12,add O at position 8,flow_matching,0.3,2.0,41,188
34,replace,2.0,c,+,CC++CcFCO3Fc,CCc+CcFCO3Fc,12,replace + at position 2 with c,flow_matching,0.3,2.0,41,188
35,replace,3.0,2,+,CCc+CcFCO3Fc,CCc2CcFCO3Fc,12,replace + at position 3 with 2,flow_matching,0.3,2.0,41,188
36,replace,3.0,1,2,CCc2CcFCO3Fc,CCc1CcFCO3Fc,12,replace 2 at position 3 with 1,flow_matching,0.3,2.0,41,188
37,remove,7.0,C,,CCc1CcFCO3Fc,CCc1CcFO3Fc,11,remove C from position 7,flow_matching,0.3,2.0,41,188
38,replace,4.0,n,C,CCc1CcFO3Fc,CCc1ncFO3Fc,11,replace C at position 4 with n,flow_matching,0.3,2.0,41,188
39,replace,5.0,n,c,CCc1ncFO3Fc,CCc1nnFO3Fc,11,replace c at position 5 with n,flow_matching,0.3,2.0,41,188
40,add,11.0,#,,CCc1nnFO3Fc,CCc1nnFO3Fc#,12,add # at position 11,flow_matching,0.3,2.0,41,188
41,replace,4.0,5,n,CCc1nnFO3Fc#,CCc15nFO3Fc#,12,replace n at position 4 with 5,flow_matching,0.3,2.0,41,188
42,remove,3.0,1,,CCc15nFO3Fc#,CCc5nFO3Fc#,11,remove 1 from position 3,flow_matching,0.3,2.0,41,188
43,replace,3.0,1,5,CCc5nFO3Fc#,CCc1nFO3Fc#,11,replace 5 at position 3 with 1,flow_matching,0.3,2.0,41,188
44,add,10.0,O,,CCc1nFO3Fc#,CCc1nFO3FcO#,12,add O at position 10,flow_matching,0.3,2.0,41,188
45,add,10.0,o,,CCc1nFO3FcO#,CCc1nFO3FcoO#,13,add o at position 10,flow_matching,0.3,2.0,41,188
46,replace,5.0,n,F,CCc1nFO3FcoO#,CCc1nnO3FcoO#,13,replace F at position 5 with n,flow_matching,0.3,2.0,41,188
47,replace,9.0,=,c,CCc1nnO3FcoO#,CCc1nnO3F=oO#,13,replace c at position 9 with =,flow_matching,0.3,2.0,41,188
48,replace,7.0,S,3,CCc1nnO3F=oO#,CCc1nnOSF=oO#,13,replace 3 at position 7 with S,flow_matching,0.3,2.0,41,188
49,replace,6.0,c,O,CCc1nnOSF=oO#,CCc1nncSF=oO#,13,replace O at position 6 with c,flow_matching,0.3,2.0,41,188
50,add,9.0,3,,CCc1nncSF=oO#,CCc1nncSF3=oO#,14,add 3 at position 9,flow_matching,0.3,2.0,41,188
51,replace,7.0,(,S,CCc1nncSF3=oO#,CCc1nnc(F3=oO#,14,replace S at position 7 with (,flow_matching,0.3,2.0,41,188
52,add,11.0,S,,CCc1nnc(F3=oO#,CCc1nnc(F3=SoO#,15,add S at position 11,flow_matching,0.3,2.0,41,188
53,replace,8.0,-,F,CCc1nnc(F3=SoO#,CCc1nnc(-3=SoO#,15,replace F at position 8 with -,flow_matching,0.3,2.0,41,188
54,replace,9.0,c,3,CCc1nnc(-3=SoO#,CCc1nnc(-c=SoO#,15,replace 3 at position 9 with c,flow_matching,0.3,2.0,41,188
55,replace,10.0,2,=,CCc1nnc(-c=SoO#,CCc1nnc(-c2SoO#,15,replace = at position 10 with 2,flow_matching,0.3,2.0,41,188
56,replace,13.0,3,O,CCc1nnc(-c2SoO#,CCc1nnc(-c2So3#,15,replace O at position 13 with 3,flow_matching,0.3,2.0,41,188
57,replace,12.0,B,o,CCc1nnc(-c2So3#,CCc1nnc(-c2SB3#,15,replace o at position 12 with B,flow_matching,0.3,2.0,41,188
58,remove,9.0,c,,CCc1nnc(-c2SB3#,CCc1nnc(-2SB3#,14,remove c from position 9,flow_matching,0.3,2.0,41,188
59,add,10.0,[,,CCc1nnc(-2SB3#,CCc1nnc(-2[SB3#,15,add [ at position 10,flow_matching,0.3,2.0,41,188
60,replace,1.0,=,C,CCc1nnc(-2[SB3#,C=c1nnc(-2[SB3#,15,replace C at position 1 with =,flow_matching,0.3,2.0,41,188
61,replace,10.0,@,[,C=c1nnc(-2[SB3#,C=c1nnc(-2@SB3#,15,replace [ at position 10 with @,flow_matching,0.3,2.0,41,188
62,replace,1.0,C,=,C=c1nnc(-2@SB3#,CCc1nnc(-2@SB3#,15,replace = at position 1 with C,flow_matching,0.3,2.0,41,188
63,replace,14.0,c,#,CCc1nnc(-2@SB3#,CCc1nnc(-2@SB3c,15,replace # at position 14 with c,flow_matching,0.3,2.0,41,188
64,replace,7.0,5,(,CCc1nnc(-2@SB3c,CCc1nnc5-2@SB3c,15,replace ( at position 7 with 5,flow_matching,0.3,2.0,41,188
65,add,9.0,6,,CCc1nnc5-2@SB3c,CCc1nnc5-62@SB3c,16,add 6 at position 9,flow_matching,0.3,2.0,41,188
66,replace,7.0,(,5,CCc1nnc5-62@SB3c,CCc1nnc(-62@SB3c,16,replace 5 at position 7 with (,flow_matching,0.3,2.0,41,188
67,replace,13.0,/,B,CCc1nnc(-62@SB3c,CCc1nnc(-62@S/3c,16,replace B at position 13 with /,flow_matching,0.3,2.0,41,188
68,add,2.0,B,,CCc1nnc(-62@S/3c,CCBc1nnc(-62@S/3c,17,add B at position 2,flow_matching,0.3,2.0,41,188
69,add,15.0,n,,CCBc1nnc(-62@S/3c,CCBc1nnc(-62@S/n3c,18,add n at position 15,flow_matching,0.3,2.0,41,188
70,replace,2.0,c,B,CCBc1nnc(-62@S/n3c,CCcc1nnc(-62@S/n3c,18,replace B at position 2 with c,flow_matching,0.3,2.0,41,188
71,remove,14.0,/,,CCcc1nnc(-62@S/n3c,CCcc1nnc(-62@Sn3c,17,remove / from position 14,flow_matching,0.3,2.0,41,188
72,replace,8.0,5,(,CCcc1nnc(-62@Sn3c,CCcc1nnc5-62@Sn3c,17,replace ( at position 8 with 5,flow_matching,0.3,2.0,41,188
73,replace,3.0,1,c,CCcc1nnc5-62@Sn3c,CCc11nnc5-62@Sn3c,17,replace c at position 3 with 1,flow_matching,0.3,2.0,41,188
74,remove,10.0,6,,CCc11nnc5-62@Sn3c,CCc11nnc5-2@Sn3c,16,remove 6 from position 10,flow_matching,0.3,2.0,41,188
75,remove,14.0,3,,CCc11nnc5-2@Sn3c,CCc11nnc5-2@Snc,15,remove 3 from position 14,flow_matching,0.3,2.0,41,188
76,add,12.0,[,,CCc11nnc5-2@Snc,CCc11nnc5-2@[Snc,16,add [ at position 12,flow_matching,0.3,2.0,41,188
77,add,12.0,I,,CCc11nnc5-2@[Snc,CCc11nnc5-2@I[Snc,17,add I at position 12,flow_matching,0.3,2.0,41,188
78,replace,4.0,n,1,CCc11nnc5-2@I[Snc,CCc1nnnc5-2@I[Snc,17,replace 1 at position 4 with n,flow_matching,0.3,2.0,41,188
79,remove,1.0,C,,CCc1nnnc5-2@I[Snc,Cc1nnnc5-2@I[Snc,16,remove C from position 1,flow_matching,0.3,2.0,41,188
80,remove,1.0,c,,Cc1nnnc5-2@I[Snc,C1nnnc5-2@I[Snc,15,remove c from position 1,flow_matching,0.3,2.0,41,188
81,add,0.0,H,,C1nnnc5-2@I[Snc,HC1nnnc5-2@I[Snc,16,add H at position 0,flow_matching,0.3,2.0,41,188
82,replace,0.0,C,H,HC1nnnc5-2@I[Snc,CC1nnnc5-2@I[Snc,16,replace H at position 0 with C,flow_matching,0.3,2.0,41,188
83,replace,1.0,O,C,CC1nnnc5-2@I[Snc,CO1nnnc5-2@I[Snc,16,replace C at position 1 with O,flow_matching,0.3,2.0,41,188
84,replace,1.0,C,O,CO1nnnc5-2@I[Snc,CC1nnnc5-2@I[Snc,16,replace O at position 1 with C,flow_matching,0.3,2.0,41,188
85,replace,2.0,c,1,CC1nnnc5-2@I[Snc,CCcnnnc5-2@I[Snc,16,replace 1 at position 2 with c,flow_matching,0.3,2.0,41,188
86,replace,3.0,1,n,CCcnnnc5-2@I[Snc,CCc1nnc5-2@I[Snc,16,replace n at position 3 with 1,flow_matching,0.3,2.0,41,188
87,remove,11.0,I,,CCc1nnc5-2@I[Snc,CCc1nnc5-2@[Snc,15,remove I from position 11,flow_matching,0.3,2.0,41,188
88,remove,2.0,c,,CCc1nnc5-2@[Snc,CC1nnc5-2@[Snc,14,remove c from position 2,flow_matching,0.3,2.0,41,188
89,replace,2.0,c,1,CC1nnc5-2@[Snc,CCcnnc5-2@[Snc,14,replace 1 at position 2 with c,flow_matching,0.3,2.0,41,188
90,replace,3.0,1,n,CCcnnc5-2@[Snc,CCc1nc5-2@[Snc,14,replace n at position 3 with 1,flow_matching,0.3,2.0,41,188
91,replace,11.0,l,S,CCc1nc5-2@[Snc,CCc1nc5-2@[lnc,14,replace S at position 11 with l,flow_matching,0.3,2.0,41,188
92,replace,5.0,n,c,CCc1nc5-2@[lnc,CCc1nn5-2@[lnc,14,replace c at position 5 with n,flow_matching,0.3,2.0,41,188
93,remove,0.0,C,,CCc1nn5-2@[lnc,Cc1nn5-2@[lnc,13,remove C from position 0,flow_matching,0.3,2.0,41,188
94,replace,1.0,C,c,Cc1nn5-2@[lnc,CC1nn5-2@[lnc,13,replace c at position 1 with C,flow_matching,0.3,2.0,41,188
95,add,9.0,/,,CC1nn5-2@[lnc,CC1nn5-2@/[lnc,14,add / at position 9,flow_matching,0.3,2.0,41,188
96,add,13.0,+,,CC1nn5-2@/[lnc,CC1nn5-2@/[ln+c,15,add + at position 13,flow_matching,0.3,2.0,41,188
97,remove,2.0,1,,CC1nn5-2@/[ln+c,CCnn5-2@/[ln+c,14,remove 1 from position 2,flow_matching,0.3,2.0,41,188
98,add,11.0,2,,CCnn5-2@/[ln+c,CCnn5-2@/[l2n+c,15,add 2 at position 11,flow_matching,0.3,2.0,41,188
99,add,5.0,],,CCnn5-2@/[l2n+c,CCnn5]-2@/[l2n+c,16,add ] at position 5,flow_matching,0.3,2.0,41,188
100,add,15.0,O,,CCnn5]-2@/[l2n+c,CCnn5]-2@/[l2n+Oc,17,add O at position 15,flow_matching,0.3,2.0,41,188
101,replace,0.0,o,C,CCnn5]-2@/[l2n+Oc,oCnn5]-2@/[l2n+Oc,17,replace C at position 0 with o,flow_matching,0.3,2.0,41,188
102,replace,0.0,C,o,oCnn5]-2@/[l2n+Oc,CCnn5]-2@/[l2n+Oc,17,replace o at position 0 with C,flow_matching,0.3,2.0,41,188
103,replace,2.0,c,n,CCnn5]-2@/[l2n+Oc,CCcn5]-2@/[l2n+Oc,17,replace n at position 2 with c,flow_matching,0.3,2.0,41,188
104,replace,3.0,1,n,CCcn5]-2@/[l2n+Oc,CCc15]-2@/[l2n+Oc,17,replace n at position 3 with 1,flow_matching,0.3,2.0,41,188
105,replace,6.0,6,-,CCc15]-2@/[l2n+Oc,CCc15]62@/[l2n+Oc,17,replace - at position 6 with 6,flow_matching,0.3,2.0,41,188
106,replace,8.0,B,@,CCc15]62@/[l2n+Oc,CCc15]62B/[l2n+Oc,17,replace @ at position 8 with B,flow_matching,0.3,2.0,41,188
107,add,17.0,B,,CCc15]62B/[l2n+Oc,CCc15]62B/[l2n+OcB,18,add B at position 17,flow_matching,0.3,2.0,41,188
108,replace,10.0,I,[,CCc15]62B/[l2n+OcB,CCc15]62B/Il2n+OcB,18,replace [ at position 10 with I,flow_matching,0.3,2.0,41,188
109,remove,15.0,O,,CCc15]62B/Il2n+OcB,CCc15]62B/Il2n+cB,17,remove O from position 15,flow_matching,0.3,2.0,41,188
110,replace,2.0,6,c,CCc15]62B/Il2n+cB,CC615]62B/Il2n+cB,17,replace c at position 2 with 6,flow_matching,0.3,2.0,41,188
111,replace,2.0,c,6,CC615]62B/Il2n+cB,CCc15]62B/Il2n+cB,17,replace 6 at position 2 with c,flow_matching,0.3,2.0,41,188
112,replace,5.0,=,],CCc15]62B/Il2n+cB,CCc15=62B/Il2n+cB,17,replace ] at position 5 with =,flow_matching,0.3,2.0,41,188
113,add,9.0,o,,CCc15=62B/Il2n+cB,CCc15=62Bo/Il2n+cB,18,add o at position 9,flow_matching,0.3,2.0,41,188
114,add,10.0,#,,CCc15=62Bo/Il2n+cB,CCc15=62Bo#/Il2n+cB,19,add # at position 10,flow_matching,0.3,2.0,41,188
115,replace,4.0,n,5,CCc15=62Bo#/Il2n+cB,CCc1n=62Bo#/Il2n+cB,19,replace 5 at position 4 with n,flow_matching,0.3,2.0,41,188
116,replace,5.0,n,=,CCc1n=62Bo#/Il2n+cB,CCc1nn62Bo#/Il2n+cB,19,replace = at position 5 with n,flow_matching,0.3,2.0,41,188
117,add,1.0,+,,CCc1nn62Bo#/Il2n+cB,C+Cc1nn62Bo#/Il2n+cB,20,add + at position 1,flow_matching,0.3,2.0,41,188
118,replace,13.0,),I,C+Cc1nn62Bo#/Il2n+cB,C+Cc1nn62Bo#/)l2n+cB,20,replace I at position 13 with ),flow_matching,0.3,2.0,41,188
119,remove,9.0,B,,C+Cc1nn62Bo#/)l2n+cB,C+Cc1nn62o#/)l2n+cB,19,remove B from position 9,flow_matching,0.3,2.0,41,188
120,replace,1.0,C,+,C+Cc1nn62o#/)l2n+cB,CCCc1nn62o#/)l2n+cB,19,replace + at position 1 with C,flow_matching,0.3,2.0,41,188
121,add,10.0,N,,CCCc1nn62o#/)l2n+cB,CCCc1nn62oN#/)l2n+cB,20,add N at position 10,flow_matching,0.3,2.0,41,188
122,replace,2.0,c,C,CCCc1nn62oN#/)l2n+cB,CCcc1nn62oN#/)l2n+cB,20,replace C at position 2 with c,flow_matching,0.3,2.0,41,188
123,replace,9.0,/,o,CCcc1nn62oN#/)l2n+cB,CCcc1nn62/N#/)l2n+cB,20,replace o at position 9 with /,flow_matching,0.3,2.0,41,188
124,add,19.0,s,,CCcc1nn62/N#/)l2n+cB,CCcc1nn62/N#/)l2n+csB,21,add s at position 19,flow_matching,0.3,2.0,41,188
125,replace,1.0,\,C,CCcc1nn62/N#/)l2n+csB,C\cc1nn62/N#/)l2n+csB,21,replace C at position 1 with \,flow_matching,0.3,2.0,41,188
126,replace,8.0,s,2,C\cc1nn62/N#/)l2n+csB,C\cc1nn6s/N#/)l2n+csB,21,replace 2 at position 8 with s,flow_matching,0.3,2.0,41,188
127,remove,9.0,/,,C\cc1nn6s/N#/)l2n+csB,C\cc1nn6sN#/)l2n+csB,20,remove / from position 9,flow_matching,0.3,2.0,41,188
128,remove,12.0,),,C\cc1nn6sN#/)l2n+csB,C\cc1nn6sN#/l2n+csB,19,remove ) from position 12,flow_matching,0.3,2.0,41,188
129,add,6.0,6,,C\cc1nn6sN#/l2n+csB,C\cc1n6n6sN#/l2n+csB,20,add 6 at position 6,flow_matching,0.3,2.0,41,188
130,remove,5.0,n,,C\cc1n6n6sN#/l2n+csB,C\cc16n6sN#/l2n+csB,19,remove n from position 5,flow_matching,0.3,2.0,41,188
131,remove,7.0,6,,C\cc16n6sN#/l2n+csB,C\cc16nsN#/l2n+csB,18,remove 6 from position 7,flow_matching,0.3,2.0,41,188
132,add,16.0,1,,C\cc16nsN#/l2n+csB,C\cc16nsN#/l2n+c1sB,19,add 1 at position 16,flow_matching,0.3,2.0,41,188
133,add,15.0,@,,C\cc16nsN#/l2n+c1sB,C\cc16nsN#/l2n+@c1sB,20,add @ at position 15,flow_matching,0.3,2.0,41,188
134,replace,1.0,C,\,C\cc16nsN#/l2n+@c1sB,CCcc16nsN#/l2n+@c1sB,20,replace \ at position 1 with C,flow_matching,0.3,2.0,41,188
135,replace,3.0,1,c,CCcc16nsN#/l2n+@c1sB,CCc116nsN#/l2n+@c1sB,20,replace c at position 3 with 1,flow_matching,0.3,2.0,41,188
136,remove,1.0,C,,CCc116nsN#/l2n+@c1sB,Cc116nsN#/l2n+@c1sB,19,remove C from position 1,flow_matching,0.3,2.0,41,188
137,replace,1.0,C,c,Cc116nsN#/l2n+@c1sB,CC116nsN#/l2n+@c1sB,19,replace c at position 1 with C,flow_matching,0.3,2.0,41,188
138,replace,2.0,c,1,CC116nsN#/l2n+@c1sB,CCc16nsN#/l2n+@c1sB,19,replace 1 at position 2 with c,flow_matching,0.3,2.0,41,188
139,replace,4.0,4,6,CCc16nsN#/l2n+@c1sB,CCc14nsN#/l2n+@c1sB,19,replace 6 at position 4 with 4,flow_matching,0.3,2.0,41,188
140,replace,0.0,O,C,CCc14nsN#/l2n+@c1sB,OCc14nsN#/l2n+@c1sB,19,replace C at position 0 with O,flow_matching,0.3,2.0,41,188
141,add,14.0,5,,OCc14nsN#/l2n+@c1sB,OCc14nsN#/l2n+5@c1sB,20,add 5 at position 14,flow_matching,0.3,2.0,41,188
142,replace,0.0,C,O,OCc14nsN#/l2n+5@c1sB,CCc14nsN#/l2n+5@c1sB,20,replace O at position 0 with C,flow_matching,0.3,2.0,41,188
143,replace,4.0,n,4,CCc14nsN#/l2n+5@c1sB,CCc1nnsN#/l2n+5@c1sB,20,replace 4 at position 4 with n,flow_matching,0.3,2.0,41,188
144,replace,6.0,c,s,CCc1nnsN#/l2n+5@c1sB,CCc1nncN#/l2n+5@c1sB,20,replace s at position 6 with c,flow_matching,0.3,2.0,41,188
145,add,4.0,B,,CCc1nncN#/l2n+5@c1sB,CCc1BnncN#/l2n+5@c1sB,21,add B at position 4,flow_matching,0.3,2.0,41,188
146,add,0.0,s,,CCc1BnncN#/l2n+5@c1sB,sCCc1BnncN#/l2n+5@c1sB,22,add s at position 0,flow_matching,0.3,2.0,41,188
147,add,11.0,N,,sCCc1BnncN#/l2n+5@c1sB,sCCc1BnncN#N/l2n+5@c1sB,23,add N at position 11,flow_matching,0.3,2.0,41,188
148,remove,18.0,@,,sCCc1BnncN#N/l2n+5@c1sB,sCCc1BnncN#N/l2n+5c1sB,22,remove @ from position 18,flow_matching,0.3,2.0,41,188
149,add,22.0,+,,sCCc1BnncN#N/l2n+5c1sB,sCCc1BnncN#N/l2n+5c1sB+,23,add + at position 22,flow_matching,0.3,2.0,41,188
150,replace,0.0,C,s,sCCc1BnncN#N/l2n+5c1sB+,CCCc1BnncN#N/l2n+5c1sB+,23,replace s at position 0 with C,flow_matching,0.3,2.0,41,188
151,replace,2.0,c,C,CCCc1BnncN#N/l2n+5c1sB+,CCcc1BnncN#N/l2n+5c1sB+,23,replace C at position 2 with c,flow_matching,0.3,2.0,41,188
152,replace,3.0,1,c,CCcc1BnncN#N/l2n+5c1sB+,CCc11BnncN#N/l2n+5c1sB+,23,replace c at position 3 with 1,flow_matching,0.3,2.0,41,188
153,replace,4.0,n,1,CCc11BnncN#N/l2n+5c1sB+,CCc1nBnncN#N/l2n+5c1sB+,23,replace 1 at position 4 with n,flow_matching,0.3,2.0,41,188
154,replace,5.0,n,B,CCc1nBnncN#N/l2n+5c1sB+,CCc1nnnncN#N/l2n+5c1sB+,23,replace B at position 5 with n,flow_matching,0.3,2.0,41,188
155,replace,6.0,c,n,CCc1nnnncN#N/l2n+5c1sB+,CCc1nncncN#N/l2n+5c1sB+,23,replace n at position 6 with c,flow_matching,0.3,2.0,41,188
156,replace,7.0,(,n,CCc1nncncN#N/l2n+5c1sB+,CCc1nnc(cN#N/l2n+5c1sB+,23,replace n at position 7 with (,flow_matching,0.3,2.0,41,188
157,replace,8.0,-,c,CCc1nnc(cN#N/l2n+5c1sB+,CCc1nnc(-N#N/l2n+5c1sB+,23,replace c at position 8 with -,flow_matching,0.3,2.0,41,188
158,replace,9.0,c,N,CCc1nnc(-N#N/l2n+5c1sB+,CCc1nnc(-c#N/l2n+5c1sB+,23,replace N at position 9 with c,flow_matching,0.3,2.0,41,188
159,replace,10.0,2,#,CCc1nnc(-c#N/l2n+5c1sB+,CCc1nnc(-c2N/l2n+5c1sB+,23,replace # at position 10 with 2,flow_matching,0.3,2.0,41,188
160,replace,11.0,c,N,CCc1nnc(-c2N/l2n+5c1sB+,CCc1nnc(-c2c/l2n+5c1sB+,23,replace N at position 11 with c,flow_matching,0.3,2.0,41,188
161,replace,12.0,c,/,CCc1nnc(-c2c/l2n+5c1sB+,CCc1nnc(-c2ccl2n+5c1sB+,23,replace / at position 12 with c,flow_matching,0.3,2.0,41,188
162,replace,13.0,3,l,CCc1nnc(-c2ccl2n+5c1sB+,CCc1nnc(-c2cc32n+5c1sB+,23,replace l at position 13 with 3,flow_matching,0.3,2.0,41,188
163,replace,14.0,c,2,CCc1nnc(-c2cc32n+5c1sB+,CCc1nnc(-c2cc3cn+5c1sB+,23,replace 2 at position 14 with c,flow_matching,0.3,2.0,41,188
164,replace,15.0,c,n,CCc1nnc(-c2cc3cn+5c1sB+,CCc1nnc(-c2cc3cc+5c1sB+,23,replace n at position 15 with c,flow_matching,0.3,2.0,41,188
165,replace,16.0,c,+,CCc1nnc(-c2cc3cc+5c1sB+,CCc1nnc(-c2cc3ccc5c1sB+,23,replace + at position 16 with c,flow_matching,0.3,2.0,41,188
166,replace,17.0,c,5,CCc1nnc(-c2cc3ccc5c1sB+,CCc1nnc(-c2cc3ccccc1sB+,23,replace 5 at position 17 with c,flow_matching,0.3,2.0,41,188
167,replace,19.0,3,1,CCc1nnc(-c2cc3ccccc1sB+,CCc1nnc(-c2cc3ccccc3sB+,23,replace 1 at position 19 with 3,flow_matching,0.3,2.0,41,188
168,replace,20.0,n,s,CCc1nnc(-c2cc3ccccc3sB+,CCc1nnc(-c2cc3ccccc3nB+,23,replace s at position 20 with n,flow_matching,0.3,2.0,41,188
169,replace,21.0,2,B,CCc1nnc(-c2cc3ccccc3nB+,CCc1nnc(-c2cc3ccccc3n2+,23,replace B at position 21 with 2,flow_matching,0.3,2.0,41,188
170,replace,22.0,C,+,CCc1nnc(-c2cc3ccccc3n2+,CCc1nnc(-c2cc3ccccc3n2C,23,replace + at position 22 with C,flow_matching,0.3,2.0,41,188
171,add,23.0,C,,CCc1nnc(-c2cc3ccccc3n2C,CCc1nnc(-c2cc3ccccc3n2CC,24,add C at position 23,flow_matching,0.3,2.0,41,188
172,add,24.0,(,,CCc1nnc(-c2cc3ccccc3n2CC,CCc1nnc(-c2cc3ccccc3n2CC(,25,add ( at position 24,flow_matching,0.3,2.0,41,188
173,add,25.0,=,,CCc1nnc(-c2cc3ccccc3n2CC(,CCc1nnc(-c2cc3ccccc3n2CC(=,26,add = at position 25,flow_matching,0.3,2.0,41,188
174,add,26.0,O,,CCc1nnc(-c2cc3ccccc3n2CC(=,CCc1nnc(-c2cc3ccccc3n2CC(=O,27,add O at position 26,flow_matching,0.3,2.0,41,188
175,add,27.0,),,CCc1nnc(-c2cc3ccccc3n2CC(=O,CCc1nnc(-c2cc3ccccc3n2CC(=O),28,add ) at position 27,flow_matching,0.3,2.0,41,188
176,add,28.0,N,,CCc1nnc(-c2cc3ccccc3n2CC(=O),CCc1nnc(-c2cc3ccccc3n2CC(=O)N,29,add N at position 28,flow_matching,0.3,2.0,41,188
177,add,29.0,C,,CCc1nnc(-c2cc3ccccc3n2CC(=O)N,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC,30,add C at position 29,flow_matching,0.3,2.0,41,188
178,add,30.0,(,,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(,31,add ( at position 30,flow_matching,0.3,2.0,41,188
179,add,31.0,C,,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C,32,add C at position 31,flow_matching,0.3,2.0,41,188
180,add,32.0,),,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C),33,add ) at position 32,flow_matching,0.3,2.0,41,188
181,add,33.0,(,,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C),CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(,34,add ( at position 33,flow_matching,0.3,2.0,41,188
182,add,34.0,C,,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C,35,add C at position 34,flow_matching,0.3,2.0,41,188
183,add,35.0,),,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C),36,add ) at position 35,flow_matching,0.3,2.0,41,188
184,add,36.0,C,,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C),CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C)C,37,add C at position 36,flow_matching,0.3,2.0,41,188
185,add,37.0,),,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C)C,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C)C),38,add ) at position 37,flow_matching,0.3,2.0,41,188
186,add,38.0,o,,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C)C),CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C)C)o,39,add o at position 38,flow_matching,0.3,2.0,41,188
187,add,39.0,1,,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C)C)o,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C)C)o1,40,add 1 at position 39,flow_matching,0.3,2.0,41,188
188,add,40.0,"
",,CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C)C)o1,"CCc1nnc(-c2cc3ccccc3n2CC(=O)NC(C)(C)C)o1
",41,"add 
 at position 40",flow_matching,0.3,2.0,41,188
