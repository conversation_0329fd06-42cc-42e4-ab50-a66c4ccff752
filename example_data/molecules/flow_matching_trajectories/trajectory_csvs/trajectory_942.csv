step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,26,58
1,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,26,58
2,replace,0.0,C,],],C,1,replace ] at position 0 with C,flow_matching,0.3,2.0,26,58
3,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,26,58
4,remove,1.0,C,,CC,C,1,remove C from position 1,flow_matching,0.3,2.0,26,58
5,add,1.0,C,,C,CC,2,add <PERSON> at position 1,flow_matching,0.3,2.0,26,58
6,add,2.0,c,,<PERSON>,CCc,3,add c at position 2,flow_matching,0.3,2.0,26,58
7,replace,0.0,#,C,CCc,#Cc,3,replace <PERSON> at position 0 with #,flow_matching,0.3,2.0,26,58
8,remove,2.0,c,,#Cc,#C,2,remove c from position 2,flow_matching,0.3,2.0,26,58
9,add,2.0,S,,#C,#CS,3,add S at position 2,flow_matching,0.3,2.0,26,58
10,replace,0.0,C,#,#CS,CCS,3,replace # at position 0 with C,flow_matching,0.3,2.0,26,58
11,add,1.0,o,,CCS,CoCS,4,add o at position 1,flow_matching,0.3,2.0,26,58
12,add,3.0,B,,CoCS,CoCBS,5,add B at position 3,flow_matching,0.3,2.0,26,58
13,replace,1.0,C,o,CoCBS,CCCBS,5,replace o at position 1 with C,flow_matching,0.3,2.0,26,58
14,replace,4.0,7,S,CCCBS,CCCB7,5,replace S at position 4 with 7,flow_matching,0.3,2.0,26,58
15,add,5.0,O,,CCCB7,CCCB7O,6,add O at position 5,flow_matching,0.3,2.0,26,58
16,add,0.0,B,,CCCB7O,BCCCB7O,7,add B at position 0,flow_matching,0.3,2.0,26,58
17,replace,6.0,3,O,BCCCB7O,BCCCB73,7,replace O at position 6 with 3,flow_matching,0.3,2.0,26,58
18,replace,0.0,C,B,BCCCB73,CCCCB73,7,replace B at position 0 with C,flow_matching,0.3,2.0,26,58
19,replace,2.0,c,C,CCCCB73,CCcCB73,7,replace C at position 2 with c,flow_matching,0.3,2.0,26,58
20,replace,3.0,1,C,CCcCB73,CCc1B73,7,replace C at position 3 with 1,flow_matching,0.3,2.0,26,58
21,replace,4.0,c,B,CCc1B73,CCc1c73,7,replace B at position 4 with c,flow_matching,0.3,2.0,26,58
22,remove,1.0,C,,CCc1c73,Cc1c73,6,remove C from position 1,flow_matching,0.3,2.0,26,58
23,add,4.0,l,,Cc1c73,Cc1cl73,7,add l at position 4,flow_matching,0.3,2.0,26,58
24,replace,1.0,C,c,Cc1cl73,CC1cl73,7,replace c at position 1 with C,flow_matching,0.3,2.0,26,58
25,add,5.0,[,,CC1cl73,CC1cl[73,8,add [ at position 5,flow_matching,0.3,2.0,26,58
26,add,3.0,s,,CC1cl[73,CC1scl[73,9,add s at position 3,flow_matching,0.3,2.0,26,58
27,remove,0.0,C,,CC1scl[73,C1scl[73,8,remove C from position 0,flow_matching,0.3,2.0,26,58
28,add,2.0,],,C1scl[73,C1]scl[73,9,add ] at position 2,flow_matching,0.3,2.0,26,58
29,add,2.0,H,,C1]scl[73,C1H]scl[73,10,add H at position 2,flow_matching,0.3,2.0,26,58
30,replace,1.0,C,1,C1H]scl[73,CCH]scl[73,10,replace 1 at position 1 with C,flow_matching,0.3,2.0,26,58
31,replace,2.0,c,H,CCH]scl[73,CCc]scl[73,10,replace H at position 2 with c,flow_matching,0.3,2.0,26,58
32,remove,1.0,C,,CCc]scl[73,Cc]scl[73,9,remove C from position 1,flow_matching,0.3,2.0,26,58
33,replace,2.0,[,],Cc]scl[73,Cc[scl[73,9,replace ] at position 2 with [,flow_matching,0.3,2.0,26,58
34,replace,1.0,=,c,Cc[scl[73,C=[scl[73,9,replace c at position 1 with =,flow_matching,0.3,2.0,26,58
35,replace,1.0,C,=,C=[scl[73,CC[scl[73,9,replace = at position 1 with C,flow_matching,0.3,2.0,26,58
36,replace,2.0,c,[,CC[scl[73,CCcscl[73,9,replace [ at position 2 with c,flow_matching,0.3,2.0,26,58
37,replace,3.0,1,s,CCcscl[73,CCc1cl[73,9,replace s at position 3 with 1,flow_matching,0.3,2.0,26,58
38,replace,5.0,c,l,CCc1cl[73,CCc1cc[73,9,replace l at position 5 with c,flow_matching,0.3,2.0,26,58
39,replace,6.0,(,[,CCc1cc[73,CCc1cc(73,9,replace [ at position 6 with (,flow_matching,0.3,2.0,26,58
40,replace,7.0,C,7,CCc1cc(73,CCc1cc(C3,9,replace 7 at position 7 with C,flow_matching,0.3,2.0,26,58
41,replace,8.0,n,3,CCc1cc(C3,CCc1cc(Cn,9,replace 3 at position 8 with n,flow_matching,0.3,2.0,26,58
42,add,9.0,2,,CCc1cc(Cn,CCc1cc(Cn2,10,add 2 at position 9,flow_matching,0.3,2.0,26,58
43,add,10.0,c,,CCc1cc(Cn2,CCc1cc(Cn2c,11,add c at position 10,flow_matching,0.3,2.0,26,58
44,add,11.0,c,,CCc1cc(Cn2c,CCc1cc(Cn2cc,12,add c at position 11,flow_matching,0.3,2.0,26,58
45,add,12.0,(,,CCc1cc(Cn2cc,CCc1cc(Cn2cc(,13,add ( at position 12,flow_matching,0.3,2.0,26,58
46,add,13.0,N,,CCc1cc(Cn2cc(,CCc1cc(Cn2cc(N,14,add N at position 13,flow_matching,0.3,2.0,26,58
47,add,14.0,),,CCc1cc(Cn2cc(N,CCc1cc(Cn2cc(N),15,add ) at position 14,flow_matching,0.3,2.0,26,58
48,add,15.0,n,,CCc1cc(Cn2cc(N),CCc1cc(Cn2cc(N)n,16,add n at position 15,flow_matching,0.3,2.0,26,58
49,add,16.0,n,,CCc1cc(Cn2cc(N)n,CCc1cc(Cn2cc(N)nn,17,add n at position 16,flow_matching,0.3,2.0,26,58
50,add,17.0,2,,CCc1cc(Cn2cc(N)nn,CCc1cc(Cn2cc(N)nn2,18,add 2 at position 17,flow_matching,0.3,2.0,26,58
51,add,18.0,),,CCc1cc(Cn2cc(N)nn2,CCc1cc(Cn2cc(N)nn2),19,add ) at position 18,flow_matching,0.3,2.0,26,58
52,add,19.0,n,,CCc1cc(Cn2cc(N)nn2),CCc1cc(Cn2cc(N)nn2)n,20,add n at position 19,flow_matching,0.3,2.0,26,58
53,add,20.0,(,,CCc1cc(Cn2cc(N)nn2)n,CCc1cc(Cn2cc(N)nn2)n(,21,add ( at position 20,flow_matching,0.3,2.0,26,58
54,add,21.0,C,,CCc1cc(Cn2cc(N)nn2)n(,CCc1cc(Cn2cc(N)nn2)n(C,22,add C at position 21,flow_matching,0.3,2.0,26,58
55,add,22.0,),,CCc1cc(Cn2cc(N)nn2)n(C,CCc1cc(Cn2cc(N)nn2)n(C),23,add ) at position 22,flow_matching,0.3,2.0,26,58
56,add,23.0,n,,CCc1cc(Cn2cc(N)nn2)n(C),CCc1cc(Cn2cc(N)nn2)n(C)n,24,add n at position 23,flow_matching,0.3,2.0,26,58
57,add,24.0,1,,CCc1cc(Cn2cc(N)nn2)n(C)n,CCc1cc(Cn2cc(N)nn2)n(C)n1,25,add 1 at position 24,flow_matching,0.3,2.0,26,58
58,add,25.0,"
",,CCc1cc(Cn2cc(N)nn2)n(C)n1,"CCc1cc(Cn2cc(N)nn2)n(C)n1
",26,"add 
 at position 25",flow_matching,0.3,2.0,26,58
