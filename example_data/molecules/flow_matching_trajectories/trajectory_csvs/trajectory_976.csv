step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,42,104
1,add,0.0,5,,,5,1,add 5 at position 0,flow_matching,0.3,2.0,42,104
2,replace,0.0,6,5,5,6,1,replace 5 at position 0 with 6,flow_matching,0.3,2.0,42,104
3,replace,0.0,C,6,6,C,1,replace 6 at position 0 with C,flow_matching,0.3,2.0,42,104
4,replace,0.0,@,C,C,@,1,replace <PERSON> at position 0 with @,flow_matching,0.3,2.0,42,104
5,add,1.0,F,,@,@F,2,add F at position 1,flow_matching,0.3,2.0,42,104
6,replace,0.0,C,@,@F,CF,2,replace @ at position 0 with C,flow_matching,0.3,2.0,42,104
7,remove,1.0,F,,CF,C,1,remove F from position 1,flow_matching,0.3,2.0,42,104
8,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,42,104
9,replace,1.0,#,c,Cc,C#,2,replace c at position 1 with #,flow_matching,0.3,2.0,42,104
10,add,0.0,5,,C#,5C#,3,add 5 at position 0,flow_matching,0.3,2.0,42,104
11,remove,1.0,C,,5C#,5#,2,remove C from position 1,flow_matching,0.3,2.0,42,104
12,add,1.0,O,,5#,5O#,3,add O at position 1,flow_matching,0.3,2.0,42,104
13,replace,0.0,C,5,5O#,CO#,3,replace 5 at position 0 with C,flow_matching,0.3,2.0,42,104
14,remove,1.0,O,,CO#,C#,2,remove O from position 1,flow_matching,0.3,2.0,42,104
15,replace,1.0,c,#,C#,Cc,2,replace # at position 1 with c,flow_matching,0.3,2.0,42,104
16,remove,0.0,C,,Cc,c,1,remove C from position 0,flow_matching,0.3,2.0,42,104
17,remove,0.0,c,,c,,0,remove c from position 0,flow_matching,0.3,2.0,42,104
18,add,0.0,S,,,S,1,add S at position 0,flow_matching,0.3,2.0,42,104
19,replace,0.0,2,S,S,2,1,replace S at position 0 with 2,flow_matching,0.3,2.0,42,104
20,replace,0.0,C,2,2,C,1,replace 2 at position 0 with C,flow_matching,0.3,2.0,42,104
21,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,42,104
22,add,1.0,N,,Cc,CNc,3,add N at position 1,flow_matching,0.3,2.0,42,104
23,replace,1.0,c,N,CNc,Ccc,3,replace N at position 1 with c,flow_matching,0.3,2.0,42,104
24,add,1.0,r,,Ccc,Crcc,4,add r at position 1,flow_matching,0.3,2.0,42,104
25,add,3.0,-,,Crcc,Crc-c,5,add - at position 3,flow_matching,0.3,2.0,42,104
26,remove,0.0,C,,Crc-c,rc-c,4,remove C from position 0,flow_matching,0.3,2.0,42,104
27,replace,1.0,I,c,rc-c,rI-c,4,replace c at position 1 with I,flow_matching,0.3,2.0,42,104
28,replace,0.0,C,r,rI-c,CI-c,4,replace r at position 0 with C,flow_matching,0.3,2.0,42,104
29,replace,2.0,H,-,CI-c,CIHc,4,replace - at position 2 with H,flow_matching,0.3,2.0,42,104
30,replace,2.0,2,H,CIHc,CI2c,4,replace H at position 2 with 2,flow_matching,0.3,2.0,42,104
31,replace,2.0,4,2,CI2c,CI4c,4,replace 2 at position 2 with 4,flow_matching,0.3,2.0,42,104
32,replace,1.0,c,I,CI4c,Cc4c,4,replace I at position 1 with c,flow_matching,0.3,2.0,42,104
33,replace,2.0,1,4,Cc4c,Cc1c,4,replace 4 at position 2 with 1,flow_matching,0.3,2.0,42,104
34,add,1.0,5,,Cc1c,C5c1c,5,add 5 at position 1,flow_matching,0.3,2.0,42,104
35,replace,4.0,/,c,C5c1c,C5c1/,5,replace c at position 4 with /,flow_matching,0.3,2.0,42,104
36,replace,2.0,r,c,C5c1/,C5r1/,5,replace c at position 2 with r,flow_matching,0.3,2.0,42,104
37,add,5.0,6,,C5r1/,C5r1/6,6,add 6 at position 5,flow_matching,0.3,2.0,42,104
38,remove,0.0,C,,C5r1/6,5r1/6,5,remove C from position 0,flow_matching,0.3,2.0,42,104
39,replace,0.0,C,5,5r1/6,Cr1/6,5,replace 5 at position 0 with C,flow_matching,0.3,2.0,42,104
40,replace,1.0,c,r,Cr1/6,Cc1/6,5,replace r at position 1 with c,flow_matching,0.3,2.0,42,104
41,add,0.0,),,Cc1/6,)Cc1/6,6,add ) at position 0,flow_matching,0.3,2.0,42,104
42,replace,0.0,C,),)Cc1/6,CCc1/6,6,replace ) at position 0 with C,flow_matching,0.3,2.0,42,104
43,add,3.0,(,,CCc1/6,CCc(1/6,7,add ( at position 3,flow_matching,0.3,2.0,42,104
44,replace,1.0,c,C,CCc(1/6,Ccc(1/6,7,replace C at position 1 with c,flow_matching,0.3,2.0,42,104
45,replace,2.0,1,c,Ccc(1/6,Cc1(1/6,7,replace c at position 2 with 1,flow_matching,0.3,2.0,42,104
46,replace,5.0,I,/,Cc1(1/6,Cc1(1I6,7,replace / at position 5 with I,flow_matching,0.3,2.0,42,104
47,remove,2.0,1,,Cc1(1I6,Cc(1I6,6,remove 1 from position 2,flow_matching,0.3,2.0,42,104
48,add,1.0,=,,Cc(1I6,C=c(1I6,7,add = at position 1,flow_matching,0.3,2.0,42,104
49,replace,5.0,H,I,C=c(1I6,C=c(1H6,7,replace I at position 5 with H,flow_matching,0.3,2.0,42,104
50,replace,1.0,c,=,C=c(1H6,Ccc(1H6,7,replace = at position 1 with c,flow_matching,0.3,2.0,42,104
51,add,7.0,2,,Ccc(1H6,Ccc(1H62,8,add 2 at position 7,flow_matching,0.3,2.0,42,104
52,replace,5.0,c,H,Ccc(1H62,Ccc(1c62,8,replace H at position 5 with c,flow_matching,0.3,2.0,42,104
53,replace,2.0,1,c,Ccc(1c62,Cc1(1c62,8,replace c at position 2 with 1,flow_matching,0.3,2.0,42,104
54,add,7.0,=,,Cc1(1c62,Cc1(1c6=2,9,add = at position 7,flow_matching,0.3,2.0,42,104
55,remove,1.0,c,,Cc1(1c6=2,C1(1c6=2,8,remove c from position 1,flow_matching,0.3,2.0,42,104
56,add,5.0,3,,C1(1c6=2,C1(1c36=2,9,add 3 at position 5,flow_matching,0.3,2.0,42,104
57,remove,1.0,1,,C1(1c36=2,C(1c36=2,8,remove 1 from position 1,flow_matching,0.3,2.0,42,104
58,replace,4.0,/,3,C(1c36=2,C(1c/6=2,8,replace 3 at position 4 with /,flow_matching,0.3,2.0,42,104
59,replace,1.0,O,(,C(1c/6=2,CO1c/6=2,8,replace ( at position 1 with O,flow_matching,0.3,2.0,42,104
60,replace,1.0,c,O,CO1c/6=2,Cc1c/6=2,8,replace O at position 1 with c,flow_matching,0.3,2.0,42,104
61,replace,4.0,(,/,Cc1c/6=2,Cc1c(6=2,8,replace / at position 4 with (,flow_matching,0.3,2.0,42,104
62,replace,5.0,-,6,Cc1c(6=2,Cc1c(-=2,8,replace 6 at position 5 with -,flow_matching,0.3,2.0,42,104
63,replace,6.0,c,=,Cc1c(-=2,Cc1c(-c2,8,replace = at position 6 with c,flow_matching,0.3,2.0,42,104
64,replace,7.0,S,2,Cc1c(-c2,Cc1c(-cS,8,replace 2 at position 7 with S,flow_matching,0.3,2.0,42,104
65,replace,7.0,2,S,Cc1c(-cS,Cc1c(-c2,8,replace S at position 7 with 2,flow_matching,0.3,2.0,42,104
66,add,5.0,n,,Cc1c(-c2,Cc1c(n-c2,9,add n at position 5,flow_matching,0.3,2.0,42,104
67,remove,8.0,2,,Cc1c(n-c2,Cc1c(n-c,8,remove 2 from position 8,flow_matching,0.3,2.0,42,104
68,replace,5.0,-,n,Cc1c(n-c,Cc1c(--c,8,replace n at position 5 with -,flow_matching,0.3,2.0,42,104
69,replace,6.0,c,-,Cc1c(--c,Cc1c(-cc,8,replace - at position 6 with c,flow_matching,0.3,2.0,42,104
70,replace,7.0,2,c,Cc1c(-cc,Cc1c(-c2,8,replace c at position 7 with 2,flow_matching,0.3,2.0,42,104
71,add,8.0,n,,Cc1c(-c2,Cc1c(-c2n,9,add n at position 8,flow_matching,0.3,2.0,42,104
72,add,9.0,c,,Cc1c(-c2n,Cc1c(-c2nc,10,add c at position 9,flow_matching,0.3,2.0,42,104
73,add,10.0,(,,Cc1c(-c2nc,Cc1c(-c2nc(,11,add ( at position 10,flow_matching,0.3,2.0,42,104
74,add,11.0,-,,Cc1c(-c2nc(,Cc1c(-c2nc(-,12,add - at position 11,flow_matching,0.3,2.0,42,104
75,add,12.0,c,,Cc1c(-c2nc(-,Cc1c(-c2nc(-c,13,add c at position 12,flow_matching,0.3,2.0,42,104
76,add,13.0,3,,Cc1c(-c2nc(-c,Cc1c(-c2nc(-c3,14,add 3 at position 13,flow_matching,0.3,2.0,42,104
77,add,14.0,c,,Cc1c(-c2nc(-c3,Cc1c(-c2nc(-c3c,15,add c at position 14,flow_matching,0.3,2.0,42,104
78,add,15.0,c,,Cc1c(-c2nc(-c3c,Cc1c(-c2nc(-c3cc,16,add c at position 15,flow_matching,0.3,2.0,42,104
79,add,16.0,c,,Cc1c(-c2nc(-c3cc,Cc1c(-c2nc(-c3ccc,17,add c at position 16,flow_matching,0.3,2.0,42,104
80,add,17.0,s,,Cc1c(-c2nc(-c3ccc,Cc1c(-c2nc(-c3cccs,18,add s at position 17,flow_matching,0.3,2.0,42,104
81,add,18.0,3,,Cc1c(-c2nc(-c3cccs,Cc1c(-c2nc(-c3cccs3,19,add 3 at position 18,flow_matching,0.3,2.0,42,104
82,add,19.0,),,Cc1c(-c2nc(-c3cccs3,Cc1c(-c2nc(-c3cccs3),20,add ) at position 19,flow_matching,0.3,2.0,42,104
83,add,20.0,n,,Cc1c(-c2nc(-c3cccs3),Cc1c(-c2nc(-c3cccs3)n,21,add n at position 20,flow_matching,0.3,2.0,42,104
84,add,21.0,o,,Cc1c(-c2nc(-c3cccs3)n,Cc1c(-c2nc(-c3cccs3)no,22,add o at position 21,flow_matching,0.3,2.0,42,104
85,add,22.0,2,,Cc1c(-c2nc(-c3cccs3)no,Cc1c(-c2nc(-c3cccs3)no2,23,add 2 at position 22,flow_matching,0.3,2.0,42,104
86,add,23.0,),,Cc1c(-c2nc(-c3cccs3)no2,Cc1c(-c2nc(-c3cccs3)no2),24,add ) at position 23,flow_matching,0.3,2.0,42,104
87,add,24.0,s,,Cc1c(-c2nc(-c3cccs3)no2),Cc1c(-c2nc(-c3cccs3)no2)s,25,add s at position 24,flow_matching,0.3,2.0,42,104
88,add,25.0,c,,Cc1c(-c2nc(-c3cccs3)no2)s,Cc1c(-c2nc(-c3cccs3)no2)sc,26,add c at position 25,flow_matching,0.3,2.0,42,104
89,add,26.0,2,,Cc1c(-c2nc(-c3cccs3)no2)sc,Cc1c(-c2nc(-c3cccs3)no2)sc2,27,add 2 at position 26,flow_matching,0.3,2.0,42,104
90,add,27.0,n,,Cc1c(-c2nc(-c3cccs3)no2)sc2,Cc1c(-c2nc(-c3cccs3)no2)sc2n,28,add n at position 27,flow_matching,0.3,2.0,42,104
91,add,28.0,c,,Cc1c(-c2nc(-c3cccs3)no2)sc2n,Cc1c(-c2nc(-c3cccs3)no2)sc2nc,29,add c at position 28,flow_matching,0.3,2.0,42,104
92,add,29.0,[,,Cc1c(-c2nc(-c3cccs3)no2)sc2nc,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[,30,add [ at position 29,flow_matching,0.3,2.0,42,104
93,add,30.0,n,,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[n,31,add n at position 30,flow_matching,0.3,2.0,42,104
94,add,31.0,H,,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[n,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH,32,add H at position 31,flow_matching,0.3,2.0,42,104
95,add,32.0,],,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH],33,add ] at position 32,flow_matching,0.3,2.0,42,104
96,add,33.0,c,,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH],Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c,34,add c at position 33,flow_matching,0.3,2.0,42,104
97,add,34.0,(,,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(,35,add ( at position 34,flow_matching,0.3,2.0,42,104
98,add,35.0,=,,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=,36,add = at position 35,flow_matching,0.3,2.0,42,104
99,add,36.0,O,,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O,37,add O at position 36,flow_matching,0.3,2.0,42,104
100,add,37.0,),,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O),38,add ) at position 37,flow_matching,0.3,2.0,42,104
101,add,38.0,c,,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O),Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O)c,39,add c at position 38,flow_matching,0.3,2.0,42,104
102,add,39.0,1,,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O)c,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O)c1,40,add 1 at position 39,flow_matching,0.3,2.0,42,104
103,add,40.0,2,,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O)c1,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O)c12,41,add 2 at position 40,flow_matching,0.3,2.0,42,104
104,add,41.0,"
",,Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O)c12,"Cc1c(-c2nc(-c3cccs3)no2)sc2nc[nH]c(=O)c12
",42,"add 
 at position 41",flow_matching,0.3,2.0,42,104
