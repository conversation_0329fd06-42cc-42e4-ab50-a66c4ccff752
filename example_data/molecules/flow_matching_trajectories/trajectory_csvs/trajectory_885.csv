step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,46,197
1,add,0.0,#,,,#,1,add # at position 0,flow_matching,0.3,2.0,46,197
2,add,0.0,(,,#,(#,2,add ( at position 0,flow_matching,0.3,2.0,46,197
3,add,1.0,F,,(#,(F#,3,add F at position 1,flow_matching,0.3,2.0,46,197
4,remove,0.0,(,,(F#,F#,2,remove ( from position 0,flow_matching,0.3,2.0,46,197
5,replace,0.0,C,F,F#,C#,2,replace F at position 0 with C,flow_matching,0.3,2.0,46,197
6,replace,0.0,+,C,C#,+#,2,replace <PERSON> at position 0 with +,flow_matching,0.3,2.0,46,197
7,add,0.0,5,,+#,5+#,3,add 5 at position 0,flow_matching,0.3,2.0,46,197
8,remove,0.0,5,,5+#,+#,2,remove 5 from position 0,flow_matching,0.3,2.0,46,197
9,add,0.0,r,,+#,r+#,3,add r at position 0,flow_matching,0.3,2.0,46,197
10,replace,0.0,C,r,r+#,C+#,3,replace r at position 0 with C,flow_matching,0.3,2.0,46,197
11,add,2.0,[,,C+#,C+[#,4,add [ at position 2,flow_matching,0.3,2.0,46,197
12,replace,0.0,o,C,C+[#,o+[#,4,replace C at position 0 with o,flow_matching,0.3,2.0,46,197
13,remove,0.0,o,,o+[#,+[#,3,remove o from position 0,flow_matching,0.3,2.0,46,197
14,add,3.0,F,,+[#,+[#F,4,add F at position 3,flow_matching,0.3,2.0,46,197
15,replace,3.0,5,F,+[#F,+[#5,4,replace F at position 3 with 5,flow_matching,0.3,2.0,46,197
16,replace,0.0,C,+,+[#5,C[#5,4,replace + at position 0 with C,flow_matching,0.3,2.0,46,197
17,replace,2.0,C,#,C[#5,C[C5,4,replace # at position 2 with C,flow_matching,0.3,2.0,46,197
18,remove,2.0,C,,C[C5,C[5,3,remove C from position 2,flow_matching,0.3,2.0,46,197
19,remove,2.0,5,,C[5,C[,2,remove 5 from position 2,flow_matching,0.3,2.0,46,197
20,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,46,197
21,add,3.0,@,,C[C,C[C@,4,add @ at position 3,flow_matching,0.3,2.0,46,197
22,replace,3.0,4,@,C[C@,C[C4,4,replace @ at position 3 with 4,flow_matching,0.3,2.0,46,197
23,remove,1.0,[,,C[C4,CC4,3,remove [ from position 1,flow_matching,0.3,2.0,46,197
24,replace,1.0,[,C,CC4,C[4,3,replace C at position 1 with [,flow_matching,0.3,2.0,46,197
25,add,0.0,o,,C[4,oC[4,4,add o at position 0,flow_matching,0.3,2.0,46,197
26,replace,0.0,+,o,oC[4,+C[4,4,replace o at position 0 with +,flow_matching,0.3,2.0,46,197
27,remove,2.0,[,,+C[4,+C4,3,remove [ from position 2,flow_matching,0.3,2.0,46,197
28,remove,0.0,+,,+C4,C4,2,remove + from position 0,flow_matching,0.3,2.0,46,197
29,add,1.0,3,,C4,C34,3,add 3 at position 1,flow_matching,0.3,2.0,46,197
30,remove,0.0,C,,C34,34,2,remove C from position 0,flow_matching,0.3,2.0,46,197
31,replace,1.0,#,4,34,3#,2,replace 4 at position 1 with #,flow_matching,0.3,2.0,46,197
32,add,0.0,5,,3#,53#,3,add 5 at position 0,flow_matching,0.3,2.0,46,197
33,replace,0.0,C,5,53#,C3#,3,replace 5 at position 0 with C,flow_matching,0.3,2.0,46,197
34,add,1.0,O,,C3#,CO3#,4,add O at position 1,flow_matching,0.3,2.0,46,197
35,remove,2.0,3,,CO3#,CO#,3,remove 3 from position 2,flow_matching,0.3,2.0,46,197
36,add,3.0,B,,CO#,CO#B,4,add B at position 3,flow_matching,0.3,2.0,46,197
37,add,1.0,4,,CO#B,C4O#B,5,add 4 at position 1,flow_matching,0.3,2.0,46,197
38,replace,4.0,c,B,C4O#B,C4O#c,5,replace B at position 4 with c,flow_matching,0.3,2.0,46,197
39,replace,1.0,[,4,C4O#c,C[O#c,5,replace 4 at position 1 with [,flow_matching,0.3,2.0,46,197
40,remove,1.0,[,,C[O#c,CO#c,4,remove [ from position 1,flow_matching,0.3,2.0,46,197
41,add,3.0,6,,CO#c,CO#6c,5,add 6 at position 3,flow_matching,0.3,2.0,46,197
42,remove,4.0,c,,CO#6c,CO#6,4,remove c from position 4,flow_matching,0.3,2.0,46,197
43,add,3.0,r,,CO#6,CO#r6,5,add r at position 3,flow_matching,0.3,2.0,46,197
44,remove,1.0,O,,CO#r6,C#r6,4,remove O from position 1,flow_matching,0.3,2.0,46,197
45,replace,1.0,[,#,C#r6,C[r6,4,replace # at position 1 with [,flow_matching,0.3,2.0,46,197
46,replace,2.0,6,r,C[r6,C[66,4,replace r at position 2 with 6,flow_matching,0.3,2.0,46,197
47,remove,2.0,6,,C[66,C[6,3,remove 6 from position 2,flow_matching,0.3,2.0,46,197
48,replace,0.0,c,C,C[6,c[6,3,replace C at position 0 with c,flow_matching,0.3,2.0,46,197
49,replace,0.0,C,c,c[6,C[6,3,replace c at position 0 with C,flow_matching,0.3,2.0,46,197
50,replace,1.0,r,[,C[6,Cr6,3,replace [ at position 1 with r,flow_matching,0.3,2.0,46,197
51,replace,0.0,I,C,Cr6,Ir6,3,replace C at position 0 with I,flow_matching,0.3,2.0,46,197
52,replace,0.0,C,I,Ir6,Cr6,3,replace I at position 0 with C,flow_matching,0.3,2.0,46,197
53,replace,2.0,l,6,Cr6,Crl,3,replace 6 at position 2 with l,flow_matching,0.3,2.0,46,197
54,remove,0.0,C,,Crl,rl,2,remove C from position 0,flow_matching,0.3,2.0,46,197
55,replace,0.0,C,r,rl,Cl,2,replace r at position 0 with C,flow_matching,0.3,2.0,46,197
56,remove,0.0,C,,Cl,l,1,remove C from position 0,flow_matching,0.3,2.0,46,197
57,add,1.0,S,,l,lS,2,add S at position 1,flow_matching,0.3,2.0,46,197
58,add,1.0,I,,lS,lIS,3,add I at position 1,flow_matching,0.3,2.0,46,197
59,replace,2.0,/,S,lIS,lI/,3,replace S at position 2 with /,flow_matching,0.3,2.0,46,197
60,replace,1.0,O,I,lI/,lO/,3,replace I at position 1 with O,flow_matching,0.3,2.0,46,197
61,add,1.0,/,,lO/,l/O/,4,add / at position 1,flow_matching,0.3,2.0,46,197
62,replace,3.0,o,/,l/O/,l/Oo,4,replace / at position 3 with o,flow_matching,0.3,2.0,46,197
63,remove,2.0,O,,l/Oo,l/o,3,remove O from position 2,flow_matching,0.3,2.0,46,197
64,replace,0.0,C,l,l/o,C/o,3,replace l at position 0 with C,flow_matching,0.3,2.0,46,197
65,replace,1.0,o,/,C/o,Coo,3,replace / at position 1 with o,flow_matching,0.3,2.0,46,197
66,add,2.0,o,,Coo,Cooo,4,add o at position 2,flow_matching,0.3,2.0,46,197
67,replace,3.0,S,o,Cooo,CooS,4,replace o at position 3 with S,flow_matching,0.3,2.0,46,197
68,remove,3.0,S,,CooS,Coo,3,remove S from position 3,flow_matching,0.3,2.0,46,197
69,replace,1.0,[,o,Coo,C[o,3,replace o at position 1 with [,flow_matching,0.3,2.0,46,197
70,remove,0.0,C,,C[o,[o,2,remove C from position 0,flow_matching,0.3,2.0,46,197
71,add,0.0,/,,[o,/[o,3,add / at position 0,flow_matching,0.3,2.0,46,197
72,remove,0.0,/,,/[o,[o,2,remove / from position 0,flow_matching,0.3,2.0,46,197
73,remove,1.0,o,,[o,[,1,remove o from position 1,flow_matching,0.3,2.0,46,197
74,replace,0.0,C,[,[,C,1,replace [ at position 0 with C,flow_matching,0.3,2.0,46,197
75,add,0.0,(,,C,(C,2,add ( at position 0,flow_matching,0.3,2.0,46,197
76,remove,1.0,C,,(C,(,1,remove C from position 1,flow_matching,0.3,2.0,46,197
77,add,0.0,1,,(,1(,2,add 1 at position 0,flow_matching,0.3,2.0,46,197
78,replace,0.0,C,1,1(,C(,2,replace 1 at position 0 with C,flow_matching,0.3,2.0,46,197
79,remove,1.0,(,,C(,C,1,remove ( from position 1,flow_matching,0.3,2.0,46,197
80,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,46,197
81,remove,0.0,C,,C[,[,1,remove C from position 0,flow_matching,0.3,2.0,46,197
82,add,1.0,c,,[,[c,2,add c at position 1,flow_matching,0.3,2.0,46,197
83,remove,1.0,c,,[c,[,1,remove c from position 1,flow_matching,0.3,2.0,46,197
84,add,1.0,/,,[,[/,2,add / at position 1,flow_matching,0.3,2.0,46,197
85,replace,1.0,\,/,[/,[\,2,replace / at position 1 with \,flow_matching,0.3,2.0,46,197
86,remove,0.0,[,,[\,\,1,remove [ from position 0,flow_matching,0.3,2.0,46,197
87,replace,0.0,o,\,\,o,1,replace \ at position 0 with o,flow_matching,0.3,2.0,46,197
88,replace,0.0,C,o,o,C,1,replace o at position 0 with C,flow_matching,0.3,2.0,46,197
89,add,1.0,1,,C,C1,2,add 1 at position 1,flow_matching,0.3,2.0,46,197
90,add,2.0,+,,C1,C1+,3,add + at position 2,flow_matching,0.3,2.0,46,197
91,replace,0.0,n,C,C1+,n1+,3,replace C at position 0 with n,flow_matching,0.3,2.0,46,197
92,add,3.0,/,,n1+,n1+/,4,add / at position 3,flow_matching,0.3,2.0,46,197
93,replace,0.0,C,n,n1+/,C1+/,4,replace n at position 0 with C,flow_matching,0.3,2.0,46,197
94,add,3.0,#,,C1+/,C1+#/,5,add # at position 3,flow_matching,0.3,2.0,46,197
95,replace,1.0,[,1,C1+#/,C[+#/,5,replace 1 at position 1 with [,flow_matching,0.3,2.0,46,197
96,replace,3.0,=,#,C[+#/,C[+=/,5,replace # at position 3 with =,flow_matching,0.3,2.0,46,197
97,replace,3.0,@,=,C[+=/,C[+@/,5,replace = at position 3 with @,flow_matching,0.3,2.0,46,197
98,replace,2.0,C,+,C[+@/,C[C@/,5,replace + at position 2 with C,flow_matching,0.3,2.0,46,197
99,replace,2.0,c,C,C[C@/,C[c@/,5,replace C at position 2 with c,flow_matching,0.3,2.0,46,197
100,replace,2.0,C,c,C[c@/,C[C@/,5,replace c at position 2 with C,flow_matching,0.3,2.0,46,197
101,add,1.0,7,,C[C@/,C7[C@/,6,add 7 at position 1,flow_matching,0.3,2.0,46,197
102,replace,1.0,[,7,C7[C@/,C[[C@/,6,replace 7 at position 1 with [,flow_matching,0.3,2.0,46,197
103,add,3.0,),,C[[C@/,C[[)C@/,7,add ) at position 3,flow_matching,0.3,2.0,46,197
104,add,6.0,7,,C[[)C@/,C[[)C@7/,8,add 7 at position 6,flow_matching,0.3,2.0,46,197
105,replace,2.0,C,[,C[[)C@7/,C[C)C@7/,8,replace [ at position 2 with C,flow_matching,0.3,2.0,46,197
106,replace,3.0,@,),C[C)C@7/,C[C@C@7/,8,replace ) at position 3 with @,flow_matching,0.3,2.0,46,197
107,replace,6.0,(,7,C[C@C@7/,C[C@C@(/,8,replace 7 at position 6 with (,flow_matching,0.3,2.0,46,197
108,replace,1.0,I,[,C[C@C@(/,CIC@C@(/,8,replace [ at position 1 with I,flow_matching,0.3,2.0,46,197
109,add,6.0,1,,CIC@C@(/,CIC@C@1(/,9,add 1 at position 6,flow_matching,0.3,2.0,46,197
110,replace,1.0,[,I,CIC@C@1(/,C[C@C@1(/,9,replace I at position 1 with [,flow_matching,0.3,2.0,46,197
111,remove,8.0,/,,C[C@C@1(/,C[C@C@1(,8,remove / from position 8,flow_matching,0.3,2.0,46,197
112,add,3.0,H,,C[C@C@1(,C[CH@C@1(,9,add H at position 3,flow_matching,0.3,2.0,46,197
113,replace,3.0,@,H,C[CH@C@1(,C[C@@C@1(,9,replace H at position 3 with @,flow_matching,0.3,2.0,46,197
114,replace,5.0,\,C,C[C@@C@1(,C[C@@\@1(,9,replace C at position 5 with \,flow_matching,0.3,2.0,46,197
115,replace,4.0,H,@,C[C@@\@1(,C[C@H\@1(,9,replace @ at position 4 with H,flow_matching,0.3,2.0,46,197
116,remove,4.0,H,,C[C@H\@1(,C[C@\@1(,8,remove H from position 4,flow_matching,0.3,2.0,46,197
117,replace,4.0,H,\,C[C@\@1(,C[C@H@1(,8,replace \ at position 4 with H,flow_matching,0.3,2.0,46,197
118,replace,5.0,],@,C[C@H@1(,C[C@H]1(,8,replace @ at position 5 with ],flow_matching,0.3,2.0,46,197
119,replace,4.0,2,H,C[C@H]1(,C[C@2]1(,8,replace H at position 4 with 2,flow_matching,0.3,2.0,46,197
120,remove,7.0,(,,C[C@2]1(,C[C@2]1,7,remove ( from position 7,flow_matching,0.3,2.0,46,197
121,replace,4.0,H,2,C[C@2]1,C[C@H]1,7,replace 2 at position 4 with H,flow_matching,0.3,2.0,46,197
122,remove,2.0,C,,C[C@H]1,C[@H]1,6,remove C from position 2,flow_matching,0.3,2.0,46,197
123,remove,3.0,H,,C[@H]1,C[@]1,5,remove H from position 3,flow_matching,0.3,2.0,46,197
124,remove,2.0,@,,C[@]1,C[]1,4,remove @ from position 2,flow_matching,0.3,2.0,46,197
125,add,3.0,#,,C[]1,C[]#1,5,add # at position 3,flow_matching,0.3,2.0,46,197
126,replace,2.0,C,],C[]#1,C[C#1,5,replace ] at position 2 with C,flow_matching,0.3,2.0,46,197
127,replace,0.0,4,C,C[C#1,4[C#1,5,replace C at position 0 with 4,flow_matching,0.3,2.0,46,197
128,add,3.0,#,,4[C#1,4[C##1,6,add # at position 3,flow_matching,0.3,2.0,46,197
129,replace,4.0,],#,4[C##1,4[C#]1,6,replace # at position 4 with ],flow_matching,0.3,2.0,46,197
130,add,3.0,o,,4[C#]1,4[Co#]1,7,add o at position 3,flow_matching,0.3,2.0,46,197
131,replace,0.0,C,4,4[Co#]1,C[Co#]1,7,replace 4 at position 0 with C,flow_matching,0.3,2.0,46,197
132,add,7.0,F,,C[Co#]1,C[Co#]1F,8,add F at position 7,flow_matching,0.3,2.0,46,197
133,remove,0.0,C,,C[Co#]1F,[Co#]1F,7,remove C from position 0,flow_matching,0.3,2.0,46,197
134,remove,3.0,#,,[Co#]1F,[Co]1F,6,remove # from position 3,flow_matching,0.3,2.0,46,197
135,replace,1.0,@,C,[Co]1F,[@o]1F,6,replace C at position 1 with @,flow_matching,0.3,2.0,46,197
136,replace,0.0,C,[,[@o]1F,C@o]1F,6,replace [ at position 0 with C,flow_matching,0.3,2.0,46,197
137,replace,1.0,[,@,C@o]1F,C[o]1F,6,replace @ at position 1 with [,flow_matching,0.3,2.0,46,197
138,replace,1.0,S,[,C[o]1F,CSo]1F,6,replace [ at position 1 with S,flow_matching,0.3,2.0,46,197
139,remove,3.0,],,CSo]1F,CSo1F,5,remove ] from position 3,flow_matching,0.3,2.0,46,197
140,remove,3.0,1,,CSo1F,CSoF,4,remove 1 from position 3,flow_matching,0.3,2.0,46,197
141,add,0.0,o,,CSoF,oCSoF,5,add o at position 0,flow_matching,0.3,2.0,46,197
142,add,3.0,7,,oCSoF,oCS7oF,6,add 7 at position 3,flow_matching,0.3,2.0,46,197
143,replace,0.0,C,o,oCS7oF,CCS7oF,6,replace o at position 0 with C,flow_matching,0.3,2.0,46,197
144,replace,1.0,[,C,CCS7oF,C[S7oF,6,replace C at position 1 with [,flow_matching,0.3,2.0,46,197
145,replace,3.0,C,7,C[S7oF,C[SCoF,6,replace 7 at position 3 with C,flow_matching,0.3,2.0,46,197
146,add,1.0,F,,C[SCoF,CF[SCoF,7,add F at position 1,flow_matching,0.3,2.0,46,197
147,replace,1.0,[,F,CF[SCoF,C[[SCoF,7,replace F at position 1 with [,flow_matching,0.3,2.0,46,197
148,replace,2.0,C,[,C[[SCoF,C[CSCoF,7,replace [ at position 2 with C,flow_matching,0.3,2.0,46,197
149,replace,0.0,6,C,C[CSCoF,6[CSCoF,7,replace C at position 0 with 6,flow_matching,0.3,2.0,46,197
150,replace,5.0,+,o,6[CSCoF,6[CSC+F,7,replace o at position 5 with +,flow_matching,0.3,2.0,46,197
151,remove,2.0,C,,6[CSC+F,6[SC+F,6,remove C from position 2,flow_matching,0.3,2.0,46,197
152,replace,2.0,n,S,6[SC+F,6[nC+F,6,replace S at position 2 with n,flow_matching,0.3,2.0,46,197
153,replace,0.0,C,6,6[nC+F,C[nC+F,6,replace 6 at position 0 with C,flow_matching,0.3,2.0,46,197
154,replace,2.0,C,n,C[nC+F,C[CC+F,6,replace n at position 2 with C,flow_matching,0.3,2.0,46,197
155,replace,3.0,@,C,C[CC+F,C[C@+F,6,replace C at position 3 with @,flow_matching,0.3,2.0,46,197
156,replace,4.0,H,+,C[C@+F,C[C@HF,6,replace + at position 4 with H,flow_matching,0.3,2.0,46,197
157,replace,5.0,],F,C[C@HF,C[C@H],6,replace F at position 5 with ],flow_matching,0.3,2.0,46,197
158,add,6.0,1,,C[C@H],C[C@H]1,7,add 1 at position 6,flow_matching,0.3,2.0,46,197
159,add,7.0,C,,C[C@H]1,C[C@H]1C,8,add C at position 7,flow_matching,0.3,2.0,46,197
160,add,8.0,C,,C[C@H]1C,C[C@H]1CC,9,add C at position 8,flow_matching,0.3,2.0,46,197
161,add,9.0,N,,C[C@H]1CC,C[C@H]1CCN,10,add N at position 9,flow_matching,0.3,2.0,46,197
162,add,10.0,(,,C[C@H]1CCN,C[C@H]1CCN(,11,add ( at position 10,flow_matching,0.3,2.0,46,197
163,add,11.0,C,,C[C@H]1CCN(,C[C@H]1CCN(C,12,add C at position 11,flow_matching,0.3,2.0,46,197
164,add,12.0,(,,C[C@H]1CCN(C,C[C@H]1CCN(C(,13,add ( at position 12,flow_matching,0.3,2.0,46,197
165,add,13.0,=,,C[C@H]1CCN(C(,C[C@H]1CCN(C(=,14,add = at position 13,flow_matching,0.3,2.0,46,197
166,add,14.0,O,,C[C@H]1CCN(C(=,C[C@H]1CCN(C(=O,15,add O at position 14,flow_matching,0.3,2.0,46,197
167,add,15.0,),,C[C@H]1CCN(C(=O,C[C@H]1CCN(C(=O),16,add ) at position 15,flow_matching,0.3,2.0,46,197
168,add,16.0,N,,C[C@H]1CCN(C(=O),C[C@H]1CCN(C(=O)N,17,add N at position 16,flow_matching,0.3,2.0,46,197
169,add,17.0,C,,C[C@H]1CCN(C(=O)N,C[C@H]1CCN(C(=O)NC,18,add C at position 17,flow_matching,0.3,2.0,46,197
170,add,18.0,C,,C[C@H]1CCN(C(=O)NC,C[C@H]1CCN(C(=O)NCC,19,add C at position 18,flow_matching,0.3,2.0,46,197
171,add,19.0,c,,C[C@H]1CCN(C(=O)NCC,C[C@H]1CCN(C(=O)NCCc,20,add c at position 19,flow_matching,0.3,2.0,46,197
172,add,20.0,2,,C[C@H]1CCN(C(=O)NCCc,C[C@H]1CCN(C(=O)NCCc2,21,add 2 at position 20,flow_matching,0.3,2.0,46,197
173,add,21.0,n,,C[C@H]1CCN(C(=O)NCCc2,C[C@H]1CCN(C(=O)NCCc2n,22,add n at position 21,flow_matching,0.3,2.0,46,197
174,add,22.0,n,,C[C@H]1CCN(C(=O)NCCc2n,C[C@H]1CCN(C(=O)NCCc2nn,23,add n at position 22,flow_matching,0.3,2.0,46,197
175,add,23.0,c,,C[C@H]1CCN(C(=O)NCCc2nn,C[C@H]1CCN(C(=O)NCCc2nnc,24,add c at position 23,flow_matching,0.3,2.0,46,197
176,add,24.0,3,,C[C@H]1CCN(C(=O)NCCc2nnc,C[C@H]1CCN(C(=O)NCCc2nnc3,25,add 3 at position 24,flow_matching,0.3,2.0,46,197
177,add,25.0,n,,C[C@H]1CCN(C(=O)NCCc2nnc3,C[C@H]1CCN(C(=O)NCCc2nnc3n,26,add n at position 25,flow_matching,0.3,2.0,46,197
178,add,26.0,2,,C[C@H]1CCN(C(=O)NCCc2nnc3n,C[C@H]1CCN(C(=O)NCCc2nnc3n2,27,add 2 at position 26,flow_matching,0.3,2.0,46,197
179,add,27.0,C,,C[C@H]1CCN(C(=O)NCCc2nnc3n2,C[C@H]1CCN(C(=O)NCCc2nnc3n2C,28,add C at position 27,flow_matching,0.3,2.0,46,197
180,add,28.0,C,,C[C@H]1CCN(C(=O)NCCc2nnc3n2C,C[C@H]1CCN(C(=O)NCCc2nnc3n2CC,29,add C at position 28,flow_matching,0.3,2.0,46,197
181,add,29.0,C,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CC,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCC,30,add C at position 29,flow_matching,0.3,2.0,46,197
182,add,30.0,C,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCC,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCC,31,add C at position 30,flow_matching,0.3,2.0,46,197
183,add,31.0,C,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCC,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC,32,add C at position 31,flow_matching,0.3,2.0,46,197
184,add,32.0,3,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3,33,add 3 at position 32,flow_matching,0.3,2.0,46,197
185,add,33.0,),,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3),34,add ) at position 33,flow_matching,0.3,2.0,46,197
186,add,34.0,[,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3),C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[,35,add [ at position 34,flow_matching,0.3,2.0,46,197
187,add,35.0,C,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C,36,add C at position 35,flow_matching,0.3,2.0,46,197
188,add,36.0,@,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@,37,add @ at position 36,flow_matching,0.3,2.0,46,197
189,add,37.0,@,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@,38,add @ at position 37,flow_matching,0.3,2.0,46,197
190,add,38.0,H,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H,39,add H at position 38,flow_matching,0.3,2.0,46,197
191,add,39.0,],,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H],40,add ] at position 39,flow_matching,0.3,2.0,46,197
192,add,40.0,(,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H],C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](,41,add ( at position 40,flow_matching,0.3,2.0,46,197
193,add,41.0,C,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](C,42,add C at position 41,flow_matching,0.3,2.0,46,197
194,add,42.0,),,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](C,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](C),43,add ) at position 42,flow_matching,0.3,2.0,46,197
195,add,43.0,C,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](C),C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](C)C,44,add C at position 43,flow_matching,0.3,2.0,46,197
196,add,44.0,1,,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](C)C,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](C)C1,45,add 1 at position 44,flow_matching,0.3,2.0,46,197
197,add,45.0,"
",,C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](C)C1,"C[C@H]1CCN(C(=O)NCCc2nnc3n2CCCCC3)[C@@H](C)C1
",46,"add 
 at position 45",flow_matching,0.3,2.0,46,197
