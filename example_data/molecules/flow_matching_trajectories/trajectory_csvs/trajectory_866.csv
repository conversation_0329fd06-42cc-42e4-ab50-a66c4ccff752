step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,37,89
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,37,89
2,add,1.0,(,,O,O(,2,add ( at position 1,flow_matching,0.3,2.0,37,89
3,add,2.0,1,,O(,O(1,3,add 1 at position 2,flow_matching,0.3,2.0,37,89
4,remove,0.0,O,,O(1,(1,2,remove O from position 0,flow_matching,0.3,2.0,37,89
5,add,1.0,1,,(1,(11,3,add 1 at position 1,flow_matching,0.3,2.0,37,89
6,replace,0.0,C,(,(11,C11,3,replace ( at position 0 with C,flow_matching,0.3,2.0,37,89
7,replace,1.0,O,1,C11,CO1,3,replace 1 at position 1 with O,flow_matching,0.3,2.0,37,89
8,replace,2.0,c,1,CO1,COc,3,replace 1 at position 2 with c,flow_matching,0.3,2.0,37,89
9,replace,1.0,6,O,COc,C6c,3,replace O at position 1 with 6,flow_matching,0.3,2.0,37,89
10,replace,1.0,O,6,C6c,COc,3,replace 6 at position 1 with O,flow_matching,0.3,2.0,37,89
11,add,3.0,#,,COc,COc#,4,add # at position 3,flow_matching,0.3,2.0,37,89
12,remove,1.0,O,,COc#,Cc#,3,remove O from position 1,flow_matching,0.3,2.0,37,89
13,replace,1.0,O,c,Cc#,CO#,3,replace c at position 1 with O,flow_matching,0.3,2.0,37,89
14,remove,0.0,C,,CO#,O#,2,remove C from position 0,flow_matching,0.3,2.0,37,89
15,remove,1.0,#,,O#,O,1,remove # from position 1,flow_matching,0.3,2.0,37,89
16,replace,0.0,S,O,O,S,1,replace O at position 0 with S,flow_matching,0.3,2.0,37,89
17,remove,0.0,S,,S,,0,remove S from position 0,flow_matching,0.3,2.0,37,89
18,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,37,89
19,replace,0.0,C,],],C,1,replace ] at position 0 with C,flow_matching,0.3,2.0,37,89
20,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,37,89
21,replace,0.0,c,C,CO,cO,2,replace C at position 0 with c,flow_matching,0.3,2.0,37,89
22,add,1.0,s,,cO,csO,3,add s at position 1,flow_matching,0.3,2.0,37,89
23,replace,1.0,),s,csO,c)O,3,replace s at position 1 with ),flow_matching,0.3,2.0,37,89
24,add,1.0,s,,c)O,cs)O,4,add s at position 1,flow_matching,0.3,2.0,37,89
25,replace,0.0,#,c,cs)O,#s)O,4,replace c at position 0 with #,flow_matching,0.3,2.0,37,89
26,add,2.0,F,,#s)O,#sF)O,5,add F at position 2,flow_matching,0.3,2.0,37,89
27,replace,1.0,+,s,#sF)O,#+F)O,5,replace s at position 1 with +,flow_matching,0.3,2.0,37,89
28,replace,4.0,H,O,#+F)O,#+F)H,5,replace O at position 4 with H,flow_matching,0.3,2.0,37,89
29,remove,1.0,+,,#+F)H,#F)H,4,remove + from position 1,flow_matching,0.3,2.0,37,89
30,add,1.0,1,,#F)H,#1F)H,5,add 1 at position 1,flow_matching,0.3,2.0,37,89
31,add,0.0,I,,#1F)H,I#1F)H,6,add I at position 0,flow_matching,0.3,2.0,37,89
32,replace,0.0,C,I,I#1F)H,C#1F)H,6,replace I at position 0 with C,flow_matching,0.3,2.0,37,89
33,remove,3.0,F,,C#1F)H,C#1)H,5,remove F from position 3,flow_matching,0.3,2.0,37,89
34,remove,4.0,H,,C#1)H,C#1),4,remove H from position 4,flow_matching,0.3,2.0,37,89
35,replace,1.0,O,#,C#1),CO1),4,replace # at position 1 with O,flow_matching,0.3,2.0,37,89
36,replace,0.0,],C,CO1),]O1),4,replace C at position 0 with ],flow_matching,0.3,2.0,37,89
37,add,3.0,N,,]O1),]O1N),5,add N at position 3,flow_matching,0.3,2.0,37,89
38,replace,3.0,],N,]O1N),]O1]),5,replace N at position 3 with ],flow_matching,0.3,2.0,37,89
39,replace,0.0,C,],]O1]),CO1]),5,replace ] at position 0 with C,flow_matching,0.3,2.0,37,89
40,remove,2.0,1,,CO1]),CO]),4,remove 1 from position 2,flow_matching,0.3,2.0,37,89
41,add,0.0,(,,CO]),(CO]),5,add ( at position 0,flow_matching,0.3,2.0,37,89
42,replace,0.0,C,(,(CO]),CCO]),5,replace ( at position 0 with C,flow_matching,0.3,2.0,37,89
43,remove,3.0,],,CCO]),CCO),4,remove ] from position 3,flow_matching,0.3,2.0,37,89
44,add,3.0,n,,CCO),CCOn),5,add n at position 3,flow_matching,0.3,2.0,37,89
45,add,1.0,),,CCOn),C)COn),6,add ) at position 1,flow_matching,0.3,2.0,37,89
46,replace,1.0,O,),C)COn),COCOn),6,replace ) at position 1 with O,flow_matching,0.3,2.0,37,89
47,add,5.0,C,,COCOn),COCOnC),7,add C at position 5,flow_matching,0.3,2.0,37,89
48,add,4.0,N,,COCOnC),COCONnC),8,add N at position 4,flow_matching,0.3,2.0,37,89
49,replace,2.0,c,C,COCONnC),COcONnC),8,replace C at position 2 with c,flow_matching,0.3,2.0,37,89
50,replace,3.0,1,O,COcONnC),COc1NnC),8,replace O at position 3 with 1,flow_matching,0.3,2.0,37,89
51,add,0.0,O,,COc1NnC),OCOc1NnC),9,add O at position 0,flow_matching,0.3,2.0,37,89
52,add,8.0,n,,OCOc1NnC),OCOc1NnCn),10,add n at position 8,flow_matching,0.3,2.0,37,89
53,replace,0.0,C,O,OCOc1NnCn),CCOc1NnCn),10,replace O at position 0 with C,flow_matching,0.3,2.0,37,89
54,replace,1.0,O,C,CCOc1NnCn),COOc1NnCn),10,replace C at position 1 with O,flow_matching,0.3,2.0,37,89
55,replace,2.0,c,O,COOc1NnCn),COcc1NnCn),10,replace O at position 2 with c,flow_matching,0.3,2.0,37,89
56,replace,3.0,1,c,COcc1NnCn),COc11NnCn),10,replace c at position 3 with 1,flow_matching,0.3,2.0,37,89
57,replace,4.0,c,1,COc11NnCn),COc1cNnCn),10,replace 1 at position 4 with c,flow_matching,0.3,2.0,37,89
58,replace,5.0,c,N,COc1cNnCn),COc1ccnCn),10,replace N at position 5 with c,flow_matching,0.3,2.0,37,89
59,replace,6.0,c,n,COc1ccnCn),COc1cccCn),10,replace n at position 6 with c,flow_matching,0.3,2.0,37,89
60,replace,7.0,c,C,COc1cccCn),COc1ccccn),10,replace C at position 7 with c,flow_matching,0.3,2.0,37,89
61,replace,8.0,c,n,COc1ccccn),COc1ccccc),10,replace n at position 8 with c,flow_matching,0.3,2.0,37,89
62,replace,9.0,1,),COc1ccccc),COc1ccccc1,10,replace ) at position 9 with 1,flow_matching,0.3,2.0,37,89
63,add,10.0,N,,COc1ccccc1,COc1ccccc1N,11,add N at position 10,flow_matching,0.3,2.0,37,89
64,add,11.0,C,,COc1ccccc1N,COc1ccccc1NC,12,add C at position 11,flow_matching,0.3,2.0,37,89
65,add,12.0,[,,COc1ccccc1NC,COc1ccccc1NC[,13,add [ at position 12,flow_matching,0.3,2.0,37,89
66,add,13.0,C,,COc1ccccc1NC[,COc1ccccc1NC[C,14,add C at position 13,flow_matching,0.3,2.0,37,89
67,add,14.0,@,,COc1ccccc1NC[C,COc1ccccc1NC[C@,15,add @ at position 14,flow_matching,0.3,2.0,37,89
68,add,15.0,H,,COc1ccccc1NC[C@,COc1ccccc1NC[C@H,16,add H at position 15,flow_matching,0.3,2.0,37,89
69,add,16.0,],,COc1ccccc1NC[C@H,COc1ccccc1NC[C@H],17,add ] at position 16,flow_matching,0.3,2.0,37,89
70,add,17.0,1,,COc1ccccc1NC[C@H],COc1ccccc1NC[C@H]1,18,add 1 at position 17,flow_matching,0.3,2.0,37,89
71,add,18.0,C,,COc1ccccc1NC[C@H]1,COc1ccccc1NC[C@H]1C,19,add C at position 18,flow_matching,0.3,2.0,37,89
72,add,19.0,C,,COc1ccccc1NC[C@H]1C,COc1ccccc1NC[C@H]1CC,20,add C at position 19,flow_matching,0.3,2.0,37,89
73,add,20.0,C,,COc1ccccc1NC[C@H]1CC,COc1ccccc1NC[C@H]1CCC,21,add C at position 20,flow_matching,0.3,2.0,37,89
74,add,21.0,N,,COc1ccccc1NC[C@H]1CCC,COc1ccccc1NC[C@H]1CCCN,22,add N at position 21,flow_matching,0.3,2.0,37,89
75,add,22.0,(,,COc1ccccc1NC[C@H]1CCCN,COc1ccccc1NC[C@H]1CCCN(,23,add ( at position 22,flow_matching,0.3,2.0,37,89
76,add,23.0,S,,COc1ccccc1NC[C@H]1CCCN(,COc1ccccc1NC[C@H]1CCCN(S,24,add S at position 23,flow_matching,0.3,2.0,37,89
77,add,24.0,(,,COc1ccccc1NC[C@H]1CCCN(S,COc1ccccc1NC[C@H]1CCCN(S(,25,add ( at position 24,flow_matching,0.3,2.0,37,89
78,add,25.0,C,,COc1ccccc1NC[C@H]1CCCN(S(,COc1ccccc1NC[C@H]1CCCN(S(C,26,add C at position 25,flow_matching,0.3,2.0,37,89
79,add,26.0,),,COc1ccccc1NC[C@H]1CCCN(S(C,COc1ccccc1NC[C@H]1CCCN(S(C),27,add ) at position 26,flow_matching,0.3,2.0,37,89
80,add,27.0,(,,COc1ccccc1NC[C@H]1CCCN(S(C),COc1ccccc1NC[C@H]1CCCN(S(C)(,28,add ( at position 27,flow_matching,0.3,2.0,37,89
81,add,28.0,=,,COc1ccccc1NC[C@H]1CCCN(S(C)(,COc1ccccc1NC[C@H]1CCCN(S(C)(=,29,add = at position 28,flow_matching,0.3,2.0,37,89
82,add,29.0,O,,COc1ccccc1NC[C@H]1CCCN(S(C)(=,COc1ccccc1NC[C@H]1CCCN(S(C)(=O,30,add O at position 29,flow_matching,0.3,2.0,37,89
83,add,30.0,),,COc1ccccc1NC[C@H]1CCCN(S(C)(=O,COc1ccccc1NC[C@H]1CCCN(S(C)(=O),31,add ) at position 30,flow_matching,0.3,2.0,37,89
84,add,31.0,=,,COc1ccccc1NC[C@H]1CCCN(S(C)(=O),COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=,32,add = at position 31,flow_matching,0.3,2.0,37,89
85,add,32.0,O,,COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=,COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=O,33,add O at position 32,flow_matching,0.3,2.0,37,89
86,add,33.0,),,COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=O,COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=O),34,add ) at position 33,flow_matching,0.3,2.0,37,89
87,add,34.0,C,,COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=O),COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=O)C,35,add C at position 34,flow_matching,0.3,2.0,37,89
88,add,35.0,1,,COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=O)C,COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=O)C1,36,add 1 at position 35,flow_matching,0.3,2.0,37,89
89,add,36.0,"
",,COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=O)C1,"COc1ccccc1NC[C@H]1CCCN(S(C)(=O)=O)C1
",37,"add 
 at position 36",flow_matching,0.3,2.0,37,89
