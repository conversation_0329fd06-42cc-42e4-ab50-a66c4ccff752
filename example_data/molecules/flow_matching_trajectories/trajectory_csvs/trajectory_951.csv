step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,44,177
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,44,177
2,add,1.0,n,,C,Cn,2,add n at position 1,flow_matching,0.3,2.0,44,177
3,replace,0.0,1,C,Cn,1n,2,replace <PERSON> at position 0 with 1,flow_matching,0.3,2.0,44,177
4,replace,0.0,C,1,1n,Cn,2,replace 1 at position 0 with C,flow_matching,0.3,2.0,44,177
5,add,2.0,),,Cn,Cn),3,add ) at position 2,flow_matching,0.3,2.0,44,177
6,replace,1.0,c,n,Cn),Cc),3,replace n at position 1 with c,flow_matching,0.3,2.0,44,177
7,replace,2.0,1,),Cc),Cc1,3,replace ) at position 2 with 1,flow_matching,0.3,2.0,44,177
8,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,44,177
9,add,2.0,n,,Cc1c,Ccn1c,5,add n at position 2,flow_matching,0.3,2.0,44,177
10,remove,1.0,c,,Ccn1c,Cn1c,4,remove c from position 1,flow_matching,0.3,2.0,44,177
11,remove,1.0,n,,Cn1c,C1c,3,remove n from position 1,flow_matching,0.3,2.0,44,177
12,remove,2.0,c,,C1c,C1,2,remove c from position 2,flow_matching,0.3,2.0,44,177
13,add,0.0,],,C1,]C1,3,add ] at position 0,flow_matching,0.3,2.0,44,177
14,replace,0.0,3,],]C1,3C1,3,replace ] at position 0 with 3,flow_matching,0.3,2.0,44,177
15,remove,0.0,3,,3C1,C1,2,remove 3 from position 0,flow_matching,0.3,2.0,44,177
16,add,1.0,B,,C1,CB1,3,add B at position 1,flow_matching,0.3,2.0,44,177
17,replace,1.0,c,B,CB1,Cc1,3,replace B at position 1 with c,flow_matching,0.3,2.0,44,177
18,replace,1.0,C,c,Cc1,CC1,3,replace c at position 1 with C,flow_matching,0.3,2.0,44,177
19,replace,1.0,c,C,CC1,Cc1,3,replace C at position 1 with c,flow_matching,0.3,2.0,44,177
20,add,2.0,/,,Cc1,Cc/1,4,add / at position 2,flow_matching,0.3,2.0,44,177
21,replace,2.0,1,/,Cc/1,Cc11,4,replace / at position 2 with 1,flow_matching,0.3,2.0,44,177
22,replace,3.0,c,1,Cc11,Cc1c,4,replace 1 at position 3 with c,flow_matching,0.3,2.0,44,177
23,replace,1.0,(,c,Cc1c,C(1c,4,replace c at position 1 with (,flow_matching,0.3,2.0,44,177
24,replace,1.0,F,(,C(1c,CF1c,4,replace ( at position 1 with F,flow_matching,0.3,2.0,44,177
25,remove,1.0,F,,CF1c,C1c,3,remove F from position 1,flow_matching,0.3,2.0,44,177
26,replace,2.0,F,c,C1c,C1F,3,replace c at position 2 with F,flow_matching,0.3,2.0,44,177
27,replace,1.0,c,1,C1F,CcF,3,replace 1 at position 1 with c,flow_matching,0.3,2.0,44,177
28,replace,2.0,1,F,CcF,Cc1,3,replace F at position 2 with 1,flow_matching,0.3,2.0,44,177
29,add,1.0,+,,Cc1,C+c1,4,add + at position 1,flow_matching,0.3,2.0,44,177
30,replace,0.0,+,C,C+c1,++c1,4,replace C at position 0 with +,flow_matching,0.3,2.0,44,177
31,add,1.0,O,,++c1,+O+c1,5,add O at position 1,flow_matching,0.3,2.0,44,177
32,replace,0.0,C,+,+O+c1,CO+c1,5,replace + at position 0 with C,flow_matching,0.3,2.0,44,177
33,replace,1.0,c,O,CO+c1,Cc+c1,5,replace O at position 1 with c,flow_matching,0.3,2.0,44,177
34,replace,4.0,-,1,Cc+c1,Cc+c-,5,replace 1 at position 4 with -,flow_matching,0.3,2.0,44,177
35,replace,1.0,l,c,Cc+c-,Cl+c-,5,replace c at position 1 with l,flow_matching,0.3,2.0,44,177
36,add,2.0,F,,Cl+c-,ClF+c-,6,add F at position 2,flow_matching,0.3,2.0,44,177
37,replace,4.0,=,c,ClF+c-,ClF+=-,6,replace c at position 4 with =,flow_matching,0.3,2.0,44,177
38,replace,3.0,4,+,ClF+=-,ClF4=-,6,replace + at position 3 with 4,flow_matching,0.3,2.0,44,177
39,replace,1.0,c,l,ClF4=-,CcF4=-,6,replace l at position 1 with c,flow_matching,0.3,2.0,44,177
40,add,0.0,O,,CcF4=-,OCcF4=-,7,add O at position 0,flow_matching,0.3,2.0,44,177
41,replace,3.0,o,F,OCcF4=-,OCco4=-,7,replace F at position 3 with o,flow_matching,0.3,2.0,44,177
42,remove,1.0,C,,OCco4=-,Oco4=-,6,remove C from position 1,flow_matching,0.3,2.0,44,177
43,replace,0.0,C,O,Oco4=-,Cco4=-,6,replace O at position 0 with C,flow_matching,0.3,2.0,44,177
44,remove,4.0,=,,Cco4=-,Cco4-,5,remove = from position 4,flow_matching,0.3,2.0,44,177
45,remove,1.0,c,,Cco4-,Co4-,4,remove c from position 1,flow_matching,0.3,2.0,44,177
46,remove,3.0,-,,Co4-,Co4,3,remove - from position 3,flow_matching,0.3,2.0,44,177
47,remove,2.0,4,,Co4,Co,2,remove 4 from position 2,flow_matching,0.3,2.0,44,177
48,add,2.0,/,,Co,Co/,3,add / at position 2,flow_matching,0.3,2.0,44,177
49,replace,1.0,c,o,Co/,Cc/,3,replace o at position 1 with c,flow_matching,0.3,2.0,44,177
50,replace,2.0,1,/,Cc/,Cc1,3,replace / at position 2 with 1,flow_matching,0.3,2.0,44,177
51,add,1.0,N,,Cc1,CNc1,4,add N at position 1,flow_matching,0.3,2.0,44,177
52,replace,1.0,c,N,CNc1,Ccc1,4,replace N at position 1 with c,flow_matching,0.3,2.0,44,177
53,add,1.0,+,,Ccc1,C+cc1,5,add + at position 1,flow_matching,0.3,2.0,44,177
54,replace,1.0,c,+,C+cc1,Cccc1,5,replace + at position 1 with c,flow_matching,0.3,2.0,44,177
55,replace,2.0,B,c,Cccc1,CcBc1,5,replace c at position 2 with B,flow_matching,0.3,2.0,44,177
56,replace,4.0,O,1,CcBc1,CcBcO,5,replace 1 at position 4 with O,flow_matching,0.3,2.0,44,177
57,replace,2.0,H,B,CcBcO,CcHcO,5,replace B at position 2 with H,flow_matching,0.3,2.0,44,177
58,replace,2.0,1,H,CcHcO,Cc1cO,5,replace H at position 2 with 1,flow_matching,0.3,2.0,44,177
59,remove,3.0,c,,Cc1cO,Cc1O,4,remove c from position 3,flow_matching,0.3,2.0,44,177
60,add,1.0,s,,Cc1O,Csc1O,5,add s at position 1,flow_matching,0.3,2.0,44,177
61,replace,1.0,c,s,Csc1O,Ccc1O,5,replace s at position 1 with c,flow_matching,0.3,2.0,44,177
62,replace,2.0,1,c,Ccc1O,Cc11O,5,replace c at position 2 with 1,flow_matching,0.3,2.0,44,177
63,add,2.0,1,,Cc11O,Cc111O,6,add 1 at position 2,flow_matching,0.3,2.0,44,177
64,remove,4.0,1,,Cc111O,Cc11O,5,remove 1 from position 4,flow_matching,0.3,2.0,44,177
65,add,2.0,l,,Cc11O,Ccl11O,6,add l at position 2,flow_matching,0.3,2.0,44,177
66,add,3.0,],,Ccl11O,Ccl]11O,7,add ] at position 3,flow_matching,0.3,2.0,44,177
67,remove,6.0,O,,Ccl]11O,Ccl]11,6,remove O from position 6,flow_matching,0.3,2.0,44,177
68,remove,1.0,c,,Ccl]11,Cl]11,5,remove c from position 1,flow_matching,0.3,2.0,44,177
69,replace,0.0,c,C,Cl]11,cl]11,5,replace C at position 0 with c,flow_matching,0.3,2.0,44,177
70,remove,1.0,l,,cl]11,c]11,4,remove l from position 1,flow_matching,0.3,2.0,44,177
71,add,4.0,2,,c]11,c]112,5,add 2 at position 4,flow_matching,0.3,2.0,44,177
72,replace,3.0,s,1,c]112,c]1s2,5,replace 1 at position 3 with s,flow_matching,0.3,2.0,44,177
73,replace,0.0,C,c,c]1s2,C]1s2,5,replace c at position 0 with C,flow_matching,0.3,2.0,44,177
74,remove,0.0,C,,C]1s2,]1s2,4,remove C from position 0,flow_matching,0.3,2.0,44,177
75,replace,0.0,C,],]1s2,C1s2,4,replace ] at position 0 with C,flow_matching,0.3,2.0,44,177
76,replace,1.0,B,1,C1s2,CBs2,4,replace 1 at position 1 with B,flow_matching,0.3,2.0,44,177
77,remove,0.0,C,,CBs2,Bs2,3,remove C from position 0,flow_matching,0.3,2.0,44,177
78,remove,2.0,2,,Bs2,Bs,2,remove 2 from position 2,flow_matching,0.3,2.0,44,177
79,replace,0.0,C,B,Bs,Cs,2,replace B at position 0 with C,flow_matching,0.3,2.0,44,177
80,remove,1.0,s,,Cs,C,1,remove s from position 1,flow_matching,0.3,2.0,44,177
81,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,44,177
82,add,2.0,O,,Cc,CcO,3,add O at position 2,flow_matching,0.3,2.0,44,177
83,replace,2.0,1,O,CcO,Cc1,3,replace O at position 2 with 1,flow_matching,0.3,2.0,44,177
84,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,44,177
85,replace,0.0,(,C,Cc1c,(c1c,4,replace C at position 0 with (,flow_matching,0.3,2.0,44,177
86,replace,0.0,C,(,(c1c,Cc1c,4,replace ( at position 0 with C,flow_matching,0.3,2.0,44,177
87,add,4.0,c,,Cc1c,Cc1cc,5,add c at position 4,flow_matching,0.3,2.0,44,177
88,add,5.0,(,,Cc1cc,Cc1cc(,6,add ( at position 5,flow_matching,0.3,2.0,44,177
89,add,6.0,C,,Cc1cc(,Cc1cc(C,7,add C at position 6,flow_matching,0.3,2.0,44,177
90,replace,3.0,N,c,Cc1cc(C,Cc1Nc(C,7,replace c at position 3 with N,flow_matching,0.3,2.0,44,177
91,remove,3.0,N,,Cc1Nc(C,Cc1c(C,6,remove N from position 3,flow_matching,0.3,2.0,44,177
92,replace,4.0,c,(,Cc1c(C,Cc1ccC,6,replace ( at position 4 with c,flow_matching,0.3,2.0,44,177
93,add,2.0,#,,Cc1ccC,Cc#1ccC,7,add # at position 2,flow_matching,0.3,2.0,44,177
94,remove,1.0,c,,Cc#1ccC,C#1ccC,6,remove c from position 1,flow_matching,0.3,2.0,44,177
95,add,3.0,N,,C#1ccC,C#1NccC,7,add N at position 3,flow_matching,0.3,2.0,44,177
96,remove,1.0,#,,C#1NccC,C1NccC,6,remove # from position 1,flow_matching,0.3,2.0,44,177
97,remove,4.0,c,,C1NccC,C1NcC,5,remove c from position 4,flow_matching,0.3,2.0,44,177
98,add,3.0,I,,C1NcC,C1NIcC,6,add I at position 3,flow_matching,0.3,2.0,44,177
99,add,1.0,(,,C1NIcC,C(1NIcC,7,add ( at position 1,flow_matching,0.3,2.0,44,177
100,replace,1.0,c,(,C(1NIcC,Cc1NIcC,7,replace ( at position 1 with c,flow_matching,0.3,2.0,44,177
101,replace,3.0,c,N,Cc1NIcC,Cc1cIcC,7,replace N at position 3 with c,flow_matching,0.3,2.0,44,177
102,add,3.0,6,,Cc1cIcC,Cc16cIcC,8,add 6 at position 3,flow_matching,0.3,2.0,44,177
103,replace,3.0,c,6,Cc16cIcC,Cc1ccIcC,8,replace 6 at position 3 with c,flow_matching,0.3,2.0,44,177
104,remove,3.0,c,,Cc1ccIcC,Cc1cIcC,7,remove c from position 3,flow_matching,0.3,2.0,44,177
105,replace,6.0,5,C,Cc1cIcC,Cc1cIc5,7,replace C at position 6 with 5,flow_matching,0.3,2.0,44,177
106,remove,4.0,I,,Cc1cIc5,Cc1cc5,6,remove I from position 4,flow_matching,0.3,2.0,44,177
107,add,2.0,4,,Cc1cc5,Cc41cc5,7,add 4 at position 2,flow_matching,0.3,2.0,44,177
108,replace,2.0,1,4,Cc41cc5,Cc11cc5,7,replace 4 at position 2 with 1,flow_matching,0.3,2.0,44,177
109,remove,0.0,C,,Cc11cc5,c11cc5,6,remove C from position 0,flow_matching,0.3,2.0,44,177
110,replace,0.0,C,c,c11cc5,C11cc5,6,replace c at position 0 with C,flow_matching,0.3,2.0,44,177
111,replace,5.0,@,5,C11cc5,C11cc@,6,replace 5 at position 5 with @,flow_matching,0.3,2.0,44,177
112,add,0.0,7,,C11cc@,7C11cc@,7,add 7 at position 0,flow_matching,0.3,2.0,44,177
113,replace,0.0,C,7,7C11cc@,CC11cc@,7,replace 7 at position 0 with C,flow_matching,0.3,2.0,44,177
114,add,5.0,#,,CC11cc@,CC11c#c@,8,add # at position 5,flow_matching,0.3,2.0,44,177
115,replace,4.0,C,c,CC11c#c@,CC11C#c@,8,replace c at position 4 with C,flow_matching,0.3,2.0,44,177
116,remove,2.0,1,,CC11C#c@,CC1C#c@,7,remove 1 from position 2,flow_matching,0.3,2.0,44,177
117,replace,0.0,-,C,CC1C#c@,-C1C#c@,7,replace C at position 0 with -,flow_matching,0.3,2.0,44,177
118,remove,1.0,C,,-C1C#c@,-1C#c@,6,remove C from position 1,flow_matching,0.3,2.0,44,177
119,remove,5.0,@,,-1C#c@,-1C#c,5,remove @ from position 5,flow_matching,0.3,2.0,44,177
120,add,3.0,N,,-1C#c,-1CN#c,6,add N at position 3,flow_matching,0.3,2.0,44,177
121,add,0.0,r,,-1CN#c,r-1CN#c,7,add r at position 0,flow_matching,0.3,2.0,44,177
122,replace,0.0,C,r,r-1CN#c,C-1CN#c,7,replace r at position 0 with C,flow_matching,0.3,2.0,44,177
123,replace,1.0,c,-,C-1CN#c,Cc1CN#c,7,replace - at position 1 with c,flow_matching,0.3,2.0,44,177
124,replace,3.0,c,C,Cc1CN#c,Cc1cN#c,7,replace C at position 3 with c,flow_matching,0.3,2.0,44,177
125,add,7.0,N,,Cc1cN#c,Cc1cN#cN,8,add N at position 7,flow_matching,0.3,2.0,44,177
126,add,4.0,S,,Cc1cN#cN,Cc1cSN#cN,9,add S at position 4,flow_matching,0.3,2.0,44,177
127,replace,7.0,S,c,Cc1cSN#cN,Cc1cSN#SN,9,replace c at position 7 with S,flow_matching,0.3,2.0,44,177
128,replace,4.0,c,S,Cc1cSN#SN,Cc1ccN#SN,9,replace S at position 4 with c,flow_matching,0.3,2.0,44,177
129,replace,8.0,5,N,Cc1ccN#SN,Cc1ccN#S5,9,replace N at position 8 with 5,flow_matching,0.3,2.0,44,177
130,replace,5.0,(,N,Cc1ccN#S5,Cc1cc(#S5,9,replace N at position 5 with (,flow_matching,0.3,2.0,44,177
131,replace,6.0,C,#,Cc1cc(#S5,Cc1cc(CS5,9,replace # at position 6 with C,flow_matching,0.3,2.0,44,177
132,remove,6.0,C,,Cc1cc(CS5,Cc1cc(S5,8,remove C from position 6,flow_matching,0.3,2.0,44,177
133,remove,7.0,5,,Cc1cc(S5,Cc1cc(S,7,remove 5 from position 7,flow_matching,0.3,2.0,44,177
134,replace,0.0,l,C,Cc1cc(S,lc1cc(S,7,replace C at position 0 with l,flow_matching,0.3,2.0,44,177
135,remove,5.0,(,,lc1cc(S,lc1ccS,6,remove ( from position 5,flow_matching,0.3,2.0,44,177
136,replace,4.0,],c,lc1ccS,lc1c]S,6,replace c at position 4 with ],flow_matching,0.3,2.0,44,177
137,replace,5.0,(,S,lc1c]S,lc1c](,6,replace S at position 5 with (,flow_matching,0.3,2.0,44,177
138,replace,0.0,C,l,lc1c](,Cc1c](,6,replace l at position 0 with C,flow_matching,0.3,2.0,44,177
139,replace,4.0,c,],Cc1c](,Cc1cc(,6,replace ] at position 4 with c,flow_matching,0.3,2.0,44,177
140,add,6.0,C,,Cc1cc(,Cc1cc(C,7,add C at position 6,flow_matching,0.3,2.0,44,177
141,add,7.0,),,Cc1cc(C,Cc1cc(C),8,add ) at position 7,flow_matching,0.3,2.0,44,177
142,add,8.0,c,,Cc1cc(C),Cc1cc(C)c,9,add c at position 8,flow_matching,0.3,2.0,44,177
143,add,9.0,2,,Cc1cc(C)c,Cc1cc(C)c2,10,add 2 at position 9,flow_matching,0.3,2.0,44,177
144,add,10.0,c,,Cc1cc(C)c2,Cc1cc(C)c2c,11,add c at position 10,flow_matching,0.3,2.0,44,177
145,add,11.0,(,,Cc1cc(C)c2c,Cc1cc(C)c2c(,12,add ( at position 11,flow_matching,0.3,2.0,44,177
146,add,12.0,-,,Cc1cc(C)c2c(,Cc1cc(C)c2c(-,13,add - at position 12,flow_matching,0.3,2.0,44,177
147,add,13.0,c,,Cc1cc(C)c2c(-,Cc1cc(C)c2c(-c,14,add c at position 13,flow_matching,0.3,2.0,44,177
148,add,14.0,3,,Cc1cc(C)c2c(-c,Cc1cc(C)c2c(-c3,15,add 3 at position 14,flow_matching,0.3,2.0,44,177
149,add,15.0,c,,Cc1cc(C)c2c(-c3,Cc1cc(C)c2c(-c3c,16,add c at position 15,flow_matching,0.3,2.0,44,177
150,add,16.0,c,,Cc1cc(C)c2c(-c3c,Cc1cc(C)c2c(-c3cc,17,add c at position 16,flow_matching,0.3,2.0,44,177
151,add,17.0,c,,Cc1cc(C)c2c(-c3cc,Cc1cc(C)c2c(-c3ccc,18,add c at position 17,flow_matching,0.3,2.0,44,177
152,add,18.0,c,,Cc1cc(C)c2c(-c3ccc,Cc1cc(C)c2c(-c3cccc,19,add c at position 18,flow_matching,0.3,2.0,44,177
153,add,19.0,c,,Cc1cc(C)c2c(-c3cccc,Cc1cc(C)c2c(-c3ccccc,20,add c at position 19,flow_matching,0.3,2.0,44,177
154,add,20.0,3,,Cc1cc(C)c2c(-c3ccccc,Cc1cc(C)c2c(-c3ccccc3,21,add 3 at position 20,flow_matching,0.3,2.0,44,177
155,add,21.0,),,Cc1cc(C)c2c(-c3ccccc3,Cc1cc(C)c2c(-c3ccccc3),22,add ) at position 21,flow_matching,0.3,2.0,44,177
156,add,22.0,n,,Cc1cc(C)c2c(-c3ccccc3),Cc1cc(C)c2c(-c3ccccc3)n,23,add n at position 22,flow_matching,0.3,2.0,44,177
157,add,23.0,c,,Cc1cc(C)c2c(-c3ccccc3)n,Cc1cc(C)c2c(-c3ccccc3)nc,24,add c at position 23,flow_matching,0.3,2.0,44,177
158,add,24.0,(,,Cc1cc(C)c2c(-c3ccccc3)nc,Cc1cc(C)c2c(-c3ccccc3)nc(,25,add ( at position 24,flow_matching,0.3,2.0,44,177
159,add,25.0,S,,Cc1cc(C)c2c(-c3ccccc3)nc(,Cc1cc(C)c2c(-c3ccccc3)nc(S,26,add S at position 25,flow_matching,0.3,2.0,44,177
160,add,26.0,C,,Cc1cc(C)c2c(-c3ccccc3)nc(S,Cc1cc(C)c2c(-c3ccccc3)nc(SC,27,add C at position 26,flow_matching,0.3,2.0,44,177
161,add,27.0,C,,Cc1cc(C)c2c(-c3ccccc3)nc(SC,Cc1cc(C)c2c(-c3ccccc3)nc(SCC,28,add C at position 27,flow_matching,0.3,2.0,44,177
162,add,28.0,(,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(,29,add ( at position 28,flow_matching,0.3,2.0,44,177
163,add,29.0,=,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=,30,add = at position 29,flow_matching,0.3,2.0,44,177
164,add,30.0,O,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O,31,add O at position 30,flow_matching,0.3,2.0,44,177
165,add,31.0,),,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O),32,add ) at position 31,flow_matching,0.3,2.0,44,177
166,add,32.0,N,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O),Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)N,33,add N at position 32,flow_matching,0.3,2.0,44,177
167,add,33.0,C,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)N,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC,34,add C at position 33,flow_matching,0.3,2.0,44,177
168,add,34.0,3,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3,35,add 3 at position 34,flow_matching,0.3,2.0,44,177
169,add,35.0,C,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3C,36,add C at position 35,flow_matching,0.3,2.0,44,177
170,add,36.0,C,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3C,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC,37,add C at position 36,flow_matching,0.3,2.0,44,177
171,add,37.0,3,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3,38,add 3 at position 37,flow_matching,0.3,2.0,44,177
172,add,38.0,),,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3),39,add ) at position 38,flow_matching,0.3,2.0,44,177
173,add,39.0,n,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3),Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3)n,40,add n at position 39,flow_matching,0.3,2.0,44,177
174,add,40.0,2,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3)n,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3)n2,41,add 2 at position 40,flow_matching,0.3,2.0,44,177
175,add,41.0,n,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3)n2,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3)n2n,42,add n at position 41,flow_matching,0.3,2.0,44,177
176,add,42.0,1,,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3)n2n,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3)n2n1,43,add 1 at position 42,flow_matching,0.3,2.0,44,177
177,add,43.0,"
",,Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3)n2n1,"Cc1cc(C)c2c(-c3ccccc3)nc(SCC(=O)NC3CC3)n2n1
",44,"add 
 at position 43",flow_matching,0.3,2.0,44,177
