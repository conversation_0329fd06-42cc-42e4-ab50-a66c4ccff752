step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,51,209
1,add,0.0,),,,),1,add ) at position 0,flow_matching,0.3,2.0,51,209
2,add,0.0,[,,),[),2,add [ at position 0,flow_matching,0.3,2.0,51,209
3,remove,1.0,),,[),[,1,remove ) from position 1,flow_matching,0.3,2.0,51,209
4,replace,0.0,\,[,[,\,1,replace [ at position 0 with \,flow_matching,0.3,2.0,51,209
5,remove,0.0,\,,\,,0,remove \ from position 0,flow_matching,0.3,2.0,51,209
6,add,0.0,#,,,#,1,add # at position 0,flow_matching,0.3,2.0,51,209
7,replace,0.0,(,#,#,(,1,replace # at position 0 with (,flow_matching,0.3,2.0,51,209
8,remove,0.0,(,,(,,0,remove ( from position 0,flow_matching,0.3,2.0,51,209
9,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,51,209
10,replace,0.0,+,C,C,+,1,replace C at position 0 with +,flow_matching,0.3,2.0,51,209
11,remove,0.0,+,,+,,0,remove + from position 0,flow_matching,0.3,2.0,51,209
12,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,51,209
13,replace,0.0,B,6,6,B,1,replace 6 at position 0 with B,flow_matching,0.3,2.0,51,209
14,add,0.0,7,,B,7B,2,add 7 at position 0,flow_matching,0.3,2.0,51,209
15,replace,0.0,[,7,7B,[B,2,replace 7 at position 0 with [,flow_matching,0.3,2.0,51,209
16,remove,1.0,B,,[B,[,1,remove B from position 1,flow_matching,0.3,2.0,51,209
17,replace,0.0,C,[,[,C,1,replace [ at position 0 with C,flow_matching,0.3,2.0,51,209
18,add,0.0,I,,C,IC,2,add I at position 0,flow_matching,0.3,2.0,51,209
19,remove,0.0,I,,IC,C,1,remove I from position 0,flow_matching,0.3,2.0,51,209
20,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,51,209
21,add,0.0,4,,,4,1,add 4 at position 0,flow_matching,0.3,2.0,51,209
22,replace,0.0,C,4,4,C,1,replace 4 at position 0 with C,flow_matching,0.3,2.0,51,209
23,add,1.0,n,,C,Cn,2,add n at position 1,flow_matching,0.3,2.0,51,209
24,replace,1.0,7,n,Cn,C7,2,replace n at position 1 with 7,flow_matching,0.3,2.0,51,209
25,replace,1.0,n,7,C7,Cn,2,replace 7 at position 1 with n,flow_matching,0.3,2.0,51,209
26,remove,1.0,n,,Cn,C,1,remove n from position 1,flow_matching,0.3,2.0,51,209
27,add,1.0,6,,C,C6,2,add 6 at position 1,flow_matching,0.3,2.0,51,209
28,add,0.0,F,,C6,FC6,3,add F at position 0,flow_matching,0.3,2.0,51,209
29,replace,0.0,s,F,FC6,sC6,3,replace F at position 0 with s,flow_matching,0.3,2.0,51,209
30,remove,2.0,6,,sC6,sC,2,remove 6 from position 2,flow_matching,0.3,2.0,51,209
31,remove,1.0,C,,sC,s,1,remove C from position 1,flow_matching,0.3,2.0,51,209
32,replace,0.0,C,s,s,C,1,replace s at position 0 with C,flow_matching,0.3,2.0,51,209
33,add,1.0,B,,C,CB,2,add B at position 1,flow_matching,0.3,2.0,51,209
34,add,1.0,1,,CB,C1B,3,add 1 at position 1,flow_matching,0.3,2.0,51,209
35,replace,1.0,c,1,C1B,CcB,3,replace 1 at position 1 with c,flow_matching,0.3,2.0,51,209
36,replace,0.0,#,C,CcB,#cB,3,replace C at position 0 with #,flow_matching,0.3,2.0,51,209
37,replace,0.0,-,#,#cB,-cB,3,replace # at position 0 with -,flow_matching,0.3,2.0,51,209
38,add,0.0,c,,-cB,c-cB,4,add c at position 0,flow_matching,0.3,2.0,51,209
39,add,1.0,4,,c-cB,c4-cB,5,add 4 at position 1,flow_matching,0.3,2.0,51,209
40,replace,4.0,#,B,c4-cB,c4-c#,5,replace B at position 4 with #,flow_matching,0.3,2.0,51,209
41,remove,2.0,-,,c4-c#,c4c#,4,remove - from position 2,flow_matching,0.3,2.0,51,209
42,replace,0.0,F,c,c4c#,F4c#,4,replace c at position 0 with F,flow_matching,0.3,2.0,51,209
43,replace,1.0,B,4,F4c#,FBc#,4,replace 4 at position 1 with B,flow_matching,0.3,2.0,51,209
44,add,1.0,],,FBc#,F]Bc#,5,add ] at position 1,flow_matching,0.3,2.0,51,209
45,remove,1.0,],,F]Bc#,FBc#,4,remove ] from position 1,flow_matching,0.3,2.0,51,209
46,replace,0.0,C,F,FBc#,CBc#,4,replace F at position 0 with C,flow_matching,0.3,2.0,51,209
47,replace,1.0,n,B,CBc#,Cnc#,4,replace B at position 1 with n,flow_matching,0.3,2.0,51,209
48,remove,3.0,#,,Cnc#,Cnc,3,remove # from position 3,flow_matching,0.3,2.0,51,209
49,replace,2.0,1,c,Cnc,Cn1,3,replace c at position 2 with 1,flow_matching,0.3,2.0,51,209
50,replace,2.0,o,1,Cn1,Cno,3,replace 1 at position 2 with o,flow_matching,0.3,2.0,51,209
51,replace,2.0,1,o,Cno,Cn1,3,replace o at position 2 with 1,flow_matching,0.3,2.0,51,209
52,add,2.0,@,,Cn1,Cn@1,4,add @ at position 2,flow_matching,0.3,2.0,51,209
53,remove,2.0,@,,Cn@1,Cn1,3,remove @ from position 2,flow_matching,0.3,2.0,51,209
54,replace,2.0,l,1,Cn1,Cnl,3,replace 1 at position 2 with l,flow_matching,0.3,2.0,51,209
55,remove,1.0,n,,Cnl,Cl,2,remove n from position 1,flow_matching,0.3,2.0,51,209
56,add,2.0,B,,Cl,ClB,3,add B at position 2,flow_matching,0.3,2.0,51,209
57,replace,1.0,5,l,ClB,C5B,3,replace l at position 1 with 5,flow_matching,0.3,2.0,51,209
58,add,2.0,s,,C5B,C5sB,4,add s at position 2,flow_matching,0.3,2.0,51,209
59,replace,2.0,#,s,C5sB,C5#B,4,replace s at position 2 with #,flow_matching,0.3,2.0,51,209
60,remove,3.0,B,,C5#B,C5#,3,remove B from position 3,flow_matching,0.3,2.0,51,209
61,replace,1.0,B,5,C5#,CB#,3,replace 5 at position 1 with B,flow_matching,0.3,2.0,51,209
62,replace,1.0,n,B,CB#,Cn#,3,replace B at position 1 with n,flow_matching,0.3,2.0,51,209
63,replace,2.0,1,#,Cn#,Cn1,3,replace # at position 2 with 1,flow_matching,0.3,2.0,51,209
64,add,0.0,o,,Cn1,oCn1,4,add o at position 0,flow_matching,0.3,2.0,51,209
65,replace,0.0,C,o,oCn1,CCn1,4,replace o at position 0 with C,flow_matching,0.3,2.0,51,209
66,add,1.0,s,,CCn1,CsCn1,5,add s at position 1,flow_matching,0.3,2.0,51,209
67,replace,1.0,n,s,CsCn1,CnCn1,5,replace s at position 1 with n,flow_matching,0.3,2.0,51,209
68,replace,2.0,r,C,CnCn1,Cnrn1,5,replace C at position 2 with r,flow_matching,0.3,2.0,51,209
69,replace,2.0,S,r,Cnrn1,CnSn1,5,replace r at position 2 with S,flow_matching,0.3,2.0,51,209
70,add,5.0,s,,CnSn1,CnSn1s,6,add s at position 5,flow_matching,0.3,2.0,51,209
71,remove,0.0,C,,CnSn1s,nSn1s,5,remove C from position 0,flow_matching,0.3,2.0,51,209
72,replace,0.0,C,n,nSn1s,CSn1s,5,replace n at position 0 with C,flow_matching,0.3,2.0,51,209
73,add,0.0,5,,CSn1s,5CSn1s,6,add 5 at position 0,flow_matching,0.3,2.0,51,209
74,add,5.0,/,,5CSn1s,5CSn1/s,7,add / at position 5,flow_matching,0.3,2.0,51,209
75,add,6.0,c,,5CSn1/s,5CSn1/cs,8,add c at position 6,flow_matching,0.3,2.0,51,209
76,add,2.0,4,,5CSn1/cs,5C4Sn1/cs,9,add 4 at position 2,flow_matching,0.3,2.0,51,209
77,replace,0.0,C,5,5C4Sn1/cs,CC4Sn1/cs,9,replace 5 at position 0 with C,flow_matching,0.3,2.0,51,209
78,remove,0.0,C,,CC4Sn1/cs,C4Sn1/cs,8,remove C from position 0,flow_matching,0.3,2.0,51,209
79,add,2.0,#,,C4Sn1/cs,C4#Sn1/cs,9,add # at position 2,flow_matching,0.3,2.0,51,209
80,replace,1.0,n,4,C4#Sn1/cs,Cn#Sn1/cs,9,replace 4 at position 1 with n,flow_matching,0.3,2.0,51,209
81,replace,2.0,1,#,Cn#Sn1/cs,Cn1Sn1/cs,9,replace # at position 2 with 1,flow_matching,0.3,2.0,51,209
82,replace,5.0,@,1,Cn1Sn1/cs,Cn1Sn@/cs,9,replace 1 at position 5 with @,flow_matching,0.3,2.0,51,209
83,add,2.0,],,Cn1Sn@/cs,Cn]1Sn@/cs,10,add ] at position 2,flow_matching,0.3,2.0,51,209
84,add,8.0,/,,Cn]1Sn@/cs,Cn]1Sn@//cs,11,add / at position 8,flow_matching,0.3,2.0,51,209
85,replace,1.0,),n,Cn]1Sn@//cs,C)]1Sn@//cs,11,replace n at position 1 with ),flow_matching,0.3,2.0,51,209
86,remove,5.0,n,,C)]1Sn@//cs,C)]1S@//cs,10,remove n from position 5,flow_matching,0.3,2.0,51,209
87,replace,9.0,c,s,C)]1S@//cs,C)]1S@//cc,10,replace s at position 9 with c,flow_matching,0.3,2.0,51,209
88,add,5.0,o,,C)]1S@//cc,C)]1So@//cc,11,add o at position 5,flow_matching,0.3,2.0,51,209
89,replace,1.0,n,),C)]1So@//cc,Cn]1So@//cc,11,replace ) at position 1 with n,flow_matching,0.3,2.0,51,209
90,replace,2.0,1,],Cn]1So@//cc,Cn11So@//cc,11,replace ] at position 2 with 1,flow_matching,0.3,2.0,51,209
91,add,2.0,[,,Cn11So@//cc,Cn[11So@//cc,12,add [ at position 2,flow_matching,0.3,2.0,51,209
92,replace,11.0,C,c,Cn[11So@//cc,Cn[11So@//cC,12,replace c at position 11 with C,flow_matching,0.3,2.0,51,209
93,replace,2.0,1,[,Cn[11So@//cC,Cn111So@//cC,12,replace [ at position 2 with 1,flow_matching,0.3,2.0,51,209
94,replace,3.0,c,1,Cn111So@//cC,Cn1c1So@//cC,12,replace 1 at position 3 with c,flow_matching,0.3,2.0,51,209
95,remove,6.0,o,,Cn1c1So@//cC,Cn1c1S@//cC,11,remove o from position 6,flow_matching,0.3,2.0,51,209
96,remove,9.0,c,,Cn1c1S@//cC,Cn1c1S@//C,10,remove c from position 9,flow_matching,0.3,2.0,51,209
97,remove,8.0,/,,Cn1c1S@//C,Cn1c1S@/C,9,remove / from position 8,flow_matching,0.3,2.0,51,209
98,replace,4.0,c,1,Cn1c1S@/C,Cn1ccS@/C,9,replace 1 at position 4 with c,flow_matching,0.3,2.0,51,209
99,replace,5.0,(,S,Cn1ccS@/C,Cn1cc(@/C,9,replace S at position 5 with (,flow_matching,0.3,2.0,51,209
100,replace,6.0,C,@,Cn1cc(@/C,Cn1cc(C/C,9,replace @ at position 6 with C,flow_matching,0.3,2.0,51,209
101,remove,3.0,c,,Cn1cc(C/C,Cn1c(C/C,8,remove c from position 3,flow_matching,0.3,2.0,51,209
102,replace,4.0,c,(,Cn1c(C/C,Cn1ccC/C,8,replace ( at position 4 with c,flow_matching,0.3,2.0,51,209
103,replace,5.0,(,C,Cn1ccC/C,Cn1cc(/C,8,replace C at position 5 with (,flow_matching,0.3,2.0,51,209
104,add,5.0,N,,Cn1cc(/C,Cn1ccN(/C,9,add N at position 5,flow_matching,0.3,2.0,51,209
105,add,1.0,6,,Cn1ccN(/C,C6n1ccN(/C,10,add 6 at position 1,flow_matching,0.3,2.0,51,209
106,replace,1.0,n,6,C6n1ccN(/C,Cnn1ccN(/C,10,replace 6 at position 1 with n,flow_matching,0.3,2.0,51,209
107,add,5.0,s,,Cnn1ccN(/C,Cnn1cscN(/C,11,add s at position 5,flow_matching,0.3,2.0,51,209
108,remove,4.0,c,,Cnn1cscN(/C,Cnn1scN(/C,10,remove c from position 4,flow_matching,0.3,2.0,51,209
109,remove,5.0,c,,Cnn1scN(/C,Cnn1sN(/C,9,remove c from position 5,flow_matching,0.3,2.0,51,209
110,add,9.0,(,,Cnn1sN(/C,Cnn1sN(/C(,10,add ( at position 9,flow_matching,0.3,2.0,51,209
111,remove,6.0,(,,Cnn1sN(/C(,Cnn1sN/C(,9,remove ( from position 6,flow_matching,0.3,2.0,51,209
112,replace,6.0,O,/,Cnn1sN/C(,Cnn1sNOC(,9,replace / at position 6 with O,flow_matching,0.3,2.0,51,209
113,replace,4.0,/,s,Cnn1sNOC(,Cnn1/NOC(,9,replace s at position 4 with /,flow_matching,0.3,2.0,51,209
114,replace,2.0,1,n,Cnn1/NOC(,Cn11/NOC(,9,replace n at position 2 with 1,flow_matching,0.3,2.0,51,209
115,replace,3.0,c,1,Cn11/NOC(,Cn1c/NOC(,9,replace 1 at position 3 with c,flow_matching,0.3,2.0,51,209
116,remove,8.0,(,,Cn1c/NOC(,Cn1c/NOC,8,remove ( from position 8,flow_matching,0.3,2.0,51,209
117,replace,4.0,c,/,Cn1c/NOC,Cn1ccNOC,8,replace / at position 4 with c,flow_matching,0.3,2.0,51,209
118,replace,5.0,(,N,Cn1ccNOC,Cn1cc(OC,8,replace N at position 5 with (,flow_matching,0.3,2.0,51,209
119,add,2.0,1,,Cn1cc(OC,Cn11cc(OC,9,add 1 at position 2,flow_matching,0.3,2.0,51,209
120,replace,3.0,c,1,Cn11cc(OC,Cn1ccc(OC,9,replace 1 at position 3 with c,flow_matching,0.3,2.0,51,209
121,replace,3.0,=,c,Cn1ccc(OC,Cn1=cc(OC,9,replace c at position 3 with =,flow_matching,0.3,2.0,51,209
122,remove,7.0,O,,Cn1=cc(OC,Cn1=cc(C,8,remove O from position 7,flow_matching,0.3,2.0,51,209
123,replace,3.0,c,=,Cn1=cc(C,Cn1ccc(C,8,replace = at position 3 with c,flow_matching,0.3,2.0,51,209
124,replace,5.0,(,c,Cn1ccc(C,Cn1cc((C,8,replace c at position 5 with (,flow_matching,0.3,2.0,51,209
125,add,2.0,-,,Cn1cc((C,Cn-1cc((C,9,add - at position 2,flow_matching,0.3,2.0,51,209
126,add,1.0,],,Cn-1cc((C,C]n-1cc((C,10,add ] at position 1,flow_matching,0.3,2.0,51,209
127,remove,3.0,-,,C]n-1cc((C,C]n1cc((C,9,remove - from position 3,flow_matching,0.3,2.0,51,209
128,add,8.0,S,,C]n1cc((C,C]n1cc((SC,10,add S at position 8,flow_matching,0.3,2.0,51,209
129,add,4.0,r,,C]n1cc((SC,C]n1rcc((SC,11,add r at position 4,flow_matching,0.3,2.0,51,209
130,add,10.0,\,,C]n1rcc((SC,C]n1rcc((S\C,12,add \ at position 10,flow_matching,0.3,2.0,51,209
131,add,5.0,1,,C]n1rcc((S\C,C]n1r1cc((S\C,13,add 1 at position 5,flow_matching,0.3,2.0,51,209
132,replace,1.0,n,],C]n1r1cc((S\C,Cnn1r1cc((S\C,13,replace ] at position 1 with n,flow_matching,0.3,2.0,51,209
133,remove,1.0,n,,Cnn1r1cc((S\C,Cn1r1cc((S\C,12,remove n from position 1,flow_matching,0.3,2.0,51,209
134,add,3.0,(,,Cn1r1cc((S\C,Cn1(r1cc((S\C,13,add ( at position 3,flow_matching,0.3,2.0,51,209
135,replace,11.0,l,\,Cn1(r1cc((S\C,Cn1(r1cc((SlC,13,replace \ at position 11 with l,flow_matching,0.3,2.0,51,209
136,add,8.0,o,,Cn1(r1cc((SlC,Cn1(r1cco((SlC,14,add o at position 8,flow_matching,0.3,2.0,51,209
137,replace,3.0,c,(,Cn1(r1cco((SlC,Cn1cr1cco((SlC,14,replace ( at position 3 with c,flow_matching,0.3,2.0,51,209
138,replace,4.0,c,r,Cn1cr1cco((SlC,Cn1cc1cco((SlC,14,replace r at position 4 with c,flow_matching,0.3,2.0,51,209
139,replace,7.0,(,c,Cn1cc1cco((SlC,Cn1cc1c(o((SlC,14,replace c at position 7 with (,flow_matching,0.3,2.0,51,209
140,replace,5.0,(,1,Cn1cc1c(o((SlC,Cn1cc(c(o((SlC,14,replace 1 at position 5 with (,flow_matching,0.3,2.0,51,209
141,replace,6.0,C,c,Cn1cc(c(o((SlC,Cn1cc(C(o((SlC,14,replace c at position 6 with C,flow_matching,0.3,2.0,51,209
142,add,13.0,4,,Cn1cc(C(o((SlC,Cn1cc(C(o((Sl4C,15,add 4 at position 13,flow_matching,0.3,2.0,51,209
143,replace,1.0,I,n,Cn1cc(C(o((Sl4C,CI1cc(C(o((Sl4C,15,replace n at position 1 with I,flow_matching,0.3,2.0,51,209
144,replace,5.0,O,(,CI1cc(C(o((Sl4C,CI1ccOC(o((Sl4C,15,replace ( at position 5 with O,flow_matching,0.3,2.0,51,209
145,add,12.0,S,,CI1ccOC(o((Sl4C,CI1ccOC(o((SSl4C,16,add S at position 12,flow_matching,0.3,2.0,51,209
146,replace,1.0,n,I,CI1ccOC(o((SSl4C,Cn1ccOC(o((SSl4C,16,replace I at position 1 with n,flow_matching,0.3,2.0,51,209
147,replace,5.0,(,O,Cn1ccOC(o((SSl4C,Cn1cc(C(o((SSl4C,16,replace O at position 5 with (,flow_matching,0.3,2.0,51,209
148,add,14.0,/,,Cn1cc(C(o((SSl4C,Cn1cc(C(o((SSl/4C,17,add / at position 14,flow_matching,0.3,2.0,51,209
149,replace,1.0,c,n,Cn1cc(C(o((SSl/4C,Cc1cc(C(o((SSl/4C,17,replace n at position 1 with c,flow_matching,0.3,2.0,51,209
150,replace,15.0,-,4,Cc1cc(C(o((SSl/4C,Cc1cc(C(o((SSl/-C,17,replace 4 at position 15 with -,flow_matching,0.3,2.0,51,209
151,replace,1.0,n,c,Cc1cc(C(o((SSl/-C,Cn1cc(C(o((SSl/-C,17,replace c at position 1 with n,flow_matching,0.3,2.0,51,209
152,replace,8.0,=,o,Cn1cc(C(o((SSl/-C,Cn1cc(C(=((SSl/-C,17,replace o at position 8 with =,flow_matching,0.3,2.0,51,209
153,replace,13.0,s,l,Cn1cc(C(=((SSl/-C,Cn1cc(C(=((SSs/-C,17,replace l at position 13 with s,flow_matching,0.3,2.0,51,209
154,add,11.0,2,,Cn1cc(C(=((SSs/-C,Cn1cc(C(=((2SSs/-C,18,add 2 at position 11,flow_matching,0.3,2.0,51,209
155,replace,9.0,O,(,Cn1cc(C(=((2SSs/-C,Cn1cc(C(=O(2SSs/-C,18,replace ( at position 9 with O,flow_matching,0.3,2.0,51,209
156,remove,2.0,1,,Cn1cc(C(=O(2SSs/-C,Cncc(C(=O(2SSs/-C,17,remove 1 from position 2,flow_matching,0.3,2.0,51,209
157,replace,2.0,1,c,Cncc(C(=O(2SSs/-C,Cn1c(C(=O(2SSs/-C,17,replace c at position 2 with 1,flow_matching,0.3,2.0,51,209
158,remove,1.0,n,,Cn1c(C(=O(2SSs/-C,C1c(C(=O(2SSs/-C,16,remove n from position 1,flow_matching,0.3,2.0,51,209
159,replace,1.0,n,1,C1c(C(=O(2SSs/-C,Cnc(C(=O(2SSs/-C,16,replace 1 at position 1 with n,flow_matching,0.3,2.0,51,209
160,add,5.0,N,,Cnc(C(=O(2SSs/-C,Cnc(CN(=O(2SSs/-C,17,add N at position 5,flow_matching,0.3,2.0,51,209
161,replace,2.0,1,c,Cnc(CN(=O(2SSs/-C,Cn1(CN(=O(2SSs/-C,17,replace c at position 2 with 1,flow_matching,0.3,2.0,51,209
162,replace,3.0,c,(,Cn1(CN(=O(2SSs/-C,Cn1cCN(=O(2SSs/-C,17,replace ( at position 3 with c,flow_matching,0.3,2.0,51,209
163,remove,5.0,N,,Cn1cCN(=O(2SSs/-C,Cn1cC(=O(2SSs/-C,16,remove N from position 5,flow_matching,0.3,2.0,51,209
164,replace,4.0,c,C,Cn1cC(=O(2SSs/-C,Cn1cc(=O(2SSs/-C,16,replace C at position 4 with c,flow_matching,0.3,2.0,51,209
165,replace,6.0,C,=,Cn1cc(=O(2SSs/-C,Cn1cc(CO(2SSs/-C,16,replace = at position 6 with C,flow_matching,0.3,2.0,51,209
166,replace,7.0,(,O,Cn1cc(CO(2SSs/-C,Cn1cc(C((2SSs/-C,16,replace O at position 7 with (,flow_matching,0.3,2.0,51,209
167,replace,8.0,=,(,Cn1cc(C((2SSs/-C,Cn1cc(C(=2SSs/-C,16,replace ( at position 8 with =,flow_matching,0.3,2.0,51,209
168,replace,9.0,O,2,Cn1cc(C(=2SSs/-C,Cn1cc(C(=OSSs/-C,16,replace 2 at position 9 with O,flow_matching,0.3,2.0,51,209
169,replace,10.0,),S,Cn1cc(C(=OSSs/-C,Cn1cc(C(=O)Ss/-C,16,replace S at position 10 with ),flow_matching,0.3,2.0,51,209
170,replace,11.0,N,S,Cn1cc(C(=O)Ss/-C,Cn1cc(C(=O)Ns/-C,16,replace S at position 11 with N,flow_matching,0.3,2.0,51,209
171,replace,12.0,c,s,Cn1cc(C(=O)Ns/-C,Cn1cc(C(=O)Nc/-C,16,replace s at position 12 with c,flow_matching,0.3,2.0,51,209
172,replace,13.0,2,/,Cn1cc(C(=O)Nc/-C,Cn1cc(C(=O)Nc2-C,16,replace / at position 13 with 2,flow_matching,0.3,2.0,51,209
173,replace,14.0,c,-,Cn1cc(C(=O)Nc2-C,Cn1cc(C(=O)Nc2cC,16,replace - at position 14 with c,flow_matching,0.3,2.0,51,209
174,replace,15.0,c,C,Cn1cc(C(=O)Nc2cC,Cn1cc(C(=O)Nc2cc,16,replace C at position 15 with c,flow_matching,0.3,2.0,51,209
175,add,16.0,c,,Cn1cc(C(=O)Nc2cc,Cn1cc(C(=O)Nc2ccc,17,add c at position 16,flow_matching,0.3,2.0,51,209
176,add,17.0,c,,Cn1cc(C(=O)Nc2ccc,Cn1cc(C(=O)Nc2cccc,18,add c at position 17,flow_matching,0.3,2.0,51,209
177,add,18.0,c,,Cn1cc(C(=O)Nc2cccc,Cn1cc(C(=O)Nc2ccccc,19,add c at position 18,flow_matching,0.3,2.0,51,209
178,add,19.0,2,,Cn1cc(C(=O)Nc2ccccc,Cn1cc(C(=O)Nc2ccccc2,20,add 2 at position 19,flow_matching,0.3,2.0,51,209
179,add,20.0,C,,Cn1cc(C(=O)Nc2ccccc2,Cn1cc(C(=O)Nc2ccccc2C,21,add C at position 20,flow_matching,0.3,2.0,51,209
180,add,21.0,(,,Cn1cc(C(=O)Nc2ccccc2C,Cn1cc(C(=O)Nc2ccccc2C(,22,add ( at position 21,flow_matching,0.3,2.0,51,209
181,add,22.0,=,,Cn1cc(C(=O)Nc2ccccc2C(,Cn1cc(C(=O)Nc2ccccc2C(=,23,add = at position 22,flow_matching,0.3,2.0,51,209
182,add,23.0,O,,Cn1cc(C(=O)Nc2ccccc2C(=,Cn1cc(C(=O)Nc2ccccc2C(=O,24,add O at position 23,flow_matching,0.3,2.0,51,209
183,add,24.0,),,Cn1cc(C(=O)Nc2ccccc2C(=O,Cn1cc(C(=O)Nc2ccccc2C(=O),25,add ) at position 24,flow_matching,0.3,2.0,51,209
184,add,25.0,N,,Cn1cc(C(=O)Nc2ccccc2C(=O),Cn1cc(C(=O)Nc2ccccc2C(=O)N,26,add N at position 25,flow_matching,0.3,2.0,51,209
185,add,26.0,C,,Cn1cc(C(=O)Nc2ccccc2C(=O)N,Cn1cc(C(=O)Nc2ccccc2C(=O)NC,27,add C at position 26,flow_matching,0.3,2.0,51,209
186,add,27.0,C,,Cn1cc(C(=O)Nc2ccccc2C(=O)NC,Cn1cc(C(=O)Nc2ccccc2C(=O)NCC,28,add C at position 27,flow_matching,0.3,2.0,51,209
187,add,28.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCC,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc,29,add c at position 28,flow_matching,0.3,2.0,51,209
188,add,29.0,2,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2,30,add 2 at position 29,flow_matching,0.3,2.0,51,209
189,add,30.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2c,31,add c at position 30,flow_matching,0.3,2.0,51,209
190,add,31.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2c,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2cc,32,add c at position 31,flow_matching,0.3,2.0,51,209
191,add,32.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2cc,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccc,33,add c at position 32,flow_matching,0.3,2.0,51,209
192,add,33.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccc,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2cccc,34,add c at position 33,flow_matching,0.3,2.0,51,209
193,add,34.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2cccc,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc,35,add c at position 34,flow_matching,0.3,2.0,51,209
194,add,35.0,2,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2,36,add 2 at position 35,flow_matching,0.3,2.0,51,209
195,add,36.0,),,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2),37,add ) at position 36,flow_matching,0.3,2.0,51,209
196,add,37.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2),Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c,38,add c at position 37,flow_matching,0.3,2.0,51,209
197,add,38.0,(,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(,39,add ( at position 38,flow_matching,0.3,2.0,51,209
198,add,39.0,=,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=,40,add = at position 39,flow_matching,0.3,2.0,51,209
199,add,40.0,O,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O,41,add O at position 40,flow_matching,0.3,2.0,51,209
200,add,41.0,),,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O),42,add ) at position 41,flow_matching,0.3,2.0,51,209
201,add,42.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O),Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c,43,add c at position 42,flow_matching,0.3,2.0,51,209
202,add,43.0,2,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2,44,add 2 at position 43,flow_matching,0.3,2.0,51,209
203,add,44.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2c,45,add c at position 44,flow_matching,0.3,2.0,51,209
204,add,45.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2c,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2cc,46,add c at position 45,flow_matching,0.3,2.0,51,209
205,add,46.0,c,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2cc,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2ccc,47,add c at position 46,flow_matching,0.3,2.0,51,209
206,add,47.0,n,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2ccc,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2cccn,48,add n at position 47,flow_matching,0.3,2.0,51,209
207,add,48.0,2,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2cccn,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2cccn2,49,add 2 at position 48,flow_matching,0.3,2.0,51,209
208,add,49.0,1,,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2cccn2,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2cccn21,50,add 1 at position 49,flow_matching,0.3,2.0,51,209
209,add,50.0,"
",,Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2cccn21,"Cn1cc(C(=O)Nc2ccccc2C(=O)NCCc2ccccc2)c(=O)c2cccn21
",51,"add 
 at position 50",flow_matching,0.3,2.0,51,209
