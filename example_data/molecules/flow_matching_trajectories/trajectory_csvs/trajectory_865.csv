step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,100
1,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,49,100
2,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,49,100
3,replace,0.0,N,C,C,N,1,replace <PERSON> at position 0 with N,flow_matching,0.3,2.0,49,100
4,add,0.0,-,,N,-N,2,add - at position 0,flow_matching,0.3,2.0,49,100
5,remove,0.0,-,,-N,N,1,remove - from position 0,flow_matching,0.3,2.0,49,100
6,replace,0.0,<PERSON>,<PERSON>,<PERSON>,<PERSON>,1,replace N at position 0 with <PERSON>,flow_matching,0.3,2.0,49,100
7,add,1.0,S,,<PERSON>,<PERSON>,2,add S at position 1,flow_matching,0.3,2.0,49,100
8,remove,0.0,<PERSON>,,<PERSON>,S,1,remove C from position 0,flow_matching,0.3,2.0,49,100
9,replace,0.0,C,S,S,C,1,replace S at position 0 with C,flow_matching,0.3,2.0,49,100
10,add,1.0,6,,C,C6,2,add 6 at position 1,flow_matching,0.3,2.0,49,100
11,add,1.0,S,,C6,CS6,3,add S at position 1,flow_matching,0.3,2.0,49,100
12,replace,1.0,C,S,CS6,CC6,3,replace S at position 1 with C,flow_matching,0.3,2.0,49,100
13,replace,2.0,/,6,CC6,CC/,3,replace 6 at position 2 with /,flow_matching,0.3,2.0,49,100
14,replace,2.0,N,/,CC/,CCN,3,replace / at position 2 with N,flow_matching,0.3,2.0,49,100
15,replace,2.0,1,N,CCN,CC1,3,replace N at position 2 with 1,flow_matching,0.3,2.0,49,100
16,replace,2.0,N,1,CC1,CCN,3,replace 1 at position 2 with N,flow_matching,0.3,2.0,49,100
17,add,3.0,(,,CCN,CCN(,4,add ( at position 3,flow_matching,0.3,2.0,49,100
18,remove,1.0,C,,CCN(,CN(,3,remove C from position 1,flow_matching,0.3,2.0,49,100
19,add,2.0,),,CN(,CN)(,4,add ) at position 2,flow_matching,0.3,2.0,49,100
20,replace,1.0,F,N,CN)(,CF)(,4,replace N at position 1 with F,flow_matching,0.3,2.0,49,100
21,replace,1.0,C,F,CF)(,CC)(,4,replace F at position 1 with C,flow_matching,0.3,2.0,49,100
22,replace,0.0,@,C,CC)(,@C)(,4,replace C at position 0 with @,flow_matching,0.3,2.0,49,100
23,replace,0.0,C,@,@C)(,CC)(,4,replace @ at position 0 with C,flow_matching,0.3,2.0,49,100
24,add,4.0,4,,CC)(,CC)(4,5,add 4 at position 4,flow_matching,0.3,2.0,49,100
25,add,0.0,4,,CC)(4,4CC)(4,6,add 4 at position 0,flow_matching,0.3,2.0,49,100
26,remove,5.0,4,,4CC)(4,4CC)(,5,remove 4 from position 5,flow_matching,0.3,2.0,49,100
27,replace,0.0,C,4,4CC)(,CCC)(,5,replace 4 at position 0 with C,flow_matching,0.3,2.0,49,100
28,remove,2.0,C,,CCC)(,CC)(,4,remove C from position 2,flow_matching,0.3,2.0,49,100
29,replace,2.0,N,),CC)(,CCN(,4,replace ) at position 2 with N,flow_matching,0.3,2.0,49,100
30,add,4.0,O,,CCN(,CCN(O,5,add O at position 4,flow_matching,0.3,2.0,49,100
31,remove,2.0,N,,CCN(O,CC(O,4,remove N from position 2,flow_matching,0.3,2.0,49,100
32,add,0.0,c,,CC(O,cCC(O,5,add c at position 0,flow_matching,0.3,2.0,49,100
33,replace,4.0,C,O,cCC(O,cCC(C,5,replace O at position 4 with C,flow_matching,0.3,2.0,49,100
34,remove,1.0,C,,cCC(C,cC(C,4,remove C from position 1,flow_matching,0.3,2.0,49,100
35,remove,3.0,C,,cC(C,cC(,3,remove C from position 3,flow_matching,0.3,2.0,49,100
36,add,2.0,=,,cC(,cC=(,4,add = at position 2,flow_matching,0.3,2.0,49,100
37,remove,3.0,(,,cC=(,cC=,3,remove ( from position 3,flow_matching,0.3,2.0,49,100
38,replace,0.0,C,c,cC=,CC=,3,replace c at position 0 with C,flow_matching,0.3,2.0,49,100
39,add,3.0,2,,CC=,CC=2,4,add 2 at position 3,flow_matching,0.3,2.0,49,100
40,replace,0.0,o,C,CC=2,oC=2,4,replace C at position 0 with o,flow_matching,0.3,2.0,49,100
41,replace,3.0,C,2,oC=2,oC=C,4,replace 2 at position 3 with C,flow_matching,0.3,2.0,49,100
42,replace,2.0,I,=,oC=C,oCIC,4,replace = at position 2 with I,flow_matching,0.3,2.0,49,100
43,add,0.0,1,,oCIC,1oCIC,5,add 1 at position 0,flow_matching,0.3,2.0,49,100
44,add,1.0,(,,1oCIC,1(oCIC,6,add ( at position 1,flow_matching,0.3,2.0,49,100
45,replace,2.0,r,o,1(oCIC,1(rCIC,6,replace o at position 2 with r,flow_matching,0.3,2.0,49,100
46,remove,0.0,1,,1(rCIC,(rCIC,5,remove 1 from position 0,flow_matching,0.3,2.0,49,100
47,replace,0.0,C,(,(rCIC,CrCIC,5,replace ( at position 0 with C,flow_matching,0.3,2.0,49,100
48,replace,1.0,C,r,CrCIC,CCCIC,5,replace r at position 1 with C,flow_matching,0.3,2.0,49,100
49,replace,2.0,N,C,CCCIC,CCNIC,5,replace C at position 2 with N,flow_matching,0.3,2.0,49,100
50,replace,3.0,(,I,CCNIC,CCN(C,5,replace I at position 3 with (,flow_matching,0.3,2.0,49,100
51,add,1.0,H,,CCN(C,CHCN(C,6,add H at position 1,flow_matching,0.3,2.0,49,100
52,replace,5.0,s,C,CHCN(C,CHCN(s,6,replace C at position 5 with s,flow_matching,0.3,2.0,49,100
53,add,1.0,C,,CHCN(s,CCHCN(s,7,add C at position 1,flow_matching,0.3,2.0,49,100
54,replace,2.0,N,H,CCHCN(s,CCNCN(s,7,replace H at position 2 with N,flow_matching,0.3,2.0,49,100
55,replace,6.0,),s,CCNCN(s,CCNCN(),7,replace s at position 6 with ),flow_matching,0.3,2.0,49,100
56,replace,3.0,(,C,CCNCN(),CCN(N(),7,replace C at position 3 with (,flow_matching,0.3,2.0,49,100
57,replace,4.0,C,N,CCN(N(),CCN(C(),7,replace N at position 4 with C,flow_matching,0.3,2.0,49,100
58,replace,6.0,=,),CCN(C(),CCN(C(=,7,replace ) at position 6 with =,flow_matching,0.3,2.0,49,100
59,add,7.0,O,,CCN(C(=,CCN(C(=O,8,add O at position 7,flow_matching,0.3,2.0,49,100
60,add,8.0,),,CCN(C(=O,CCN(C(=O),9,add ) at position 8,flow_matching,0.3,2.0,49,100
61,add,9.0,C,,CCN(C(=O),CCN(C(=O)C,10,add C at position 9,flow_matching,0.3,2.0,49,100
62,add,10.0,n,,CCN(C(=O)C,CCN(C(=O)Cn,11,add n at position 10,flow_matching,0.3,2.0,49,100
63,add,11.0,1,,CCN(C(=O)Cn,CCN(C(=O)Cn1,12,add 1 at position 11,flow_matching,0.3,2.0,49,100
64,add,12.0,n,,CCN(C(=O)Cn1,CCN(C(=O)Cn1n,13,add n at position 12,flow_matching,0.3,2.0,49,100
65,add,13.0,c,,CCN(C(=O)Cn1n,CCN(C(=O)Cn1nc,14,add c at position 13,flow_matching,0.3,2.0,49,100
66,add,14.0,2,,CCN(C(=O)Cn1nc,CCN(C(=O)Cn1nc2,15,add 2 at position 14,flow_matching,0.3,2.0,49,100
67,add,15.0,n,,CCN(C(=O)Cn1nc2,CCN(C(=O)Cn1nc2n,16,add n at position 15,flow_matching,0.3,2.0,49,100
68,add,16.0,(,,CCN(C(=O)Cn1nc2n,CCN(C(=O)Cn1nc2n(,17,add ( at position 16,flow_matching,0.3,2.0,49,100
69,add,17.0,c,,CCN(C(=O)Cn1nc2n(,CCN(C(=O)Cn1nc2n(c,18,add c at position 17,flow_matching,0.3,2.0,49,100
70,add,18.0,1,,CCN(C(=O)Cn1nc2n(c,CCN(C(=O)Cn1nc2n(c1,19,add 1 at position 18,flow_matching,0.3,2.0,49,100
71,add,19.0,=,,CCN(C(=O)Cn1nc2n(c1,CCN(C(=O)Cn1nc2n(c1=,20,add = at position 19,flow_matching,0.3,2.0,49,100
72,add,20.0,O,,CCN(C(=O)Cn1nc2n(c1=,CCN(C(=O)Cn1nc2n(c1=O,21,add O at position 20,flow_matching,0.3,2.0,49,100
73,add,21.0,),,CCN(C(=O)Cn1nc2n(c1=O,CCN(C(=O)Cn1nc2n(c1=O),22,add ) at position 21,flow_matching,0.3,2.0,49,100
74,add,22.0,C,,CCN(C(=O)Cn1nc2n(c1=O),CCN(C(=O)Cn1nc2n(c1=O)C,23,add C at position 22,flow_matching,0.3,2.0,49,100
75,add,23.0,C,,CCN(C(=O)Cn1nc2n(c1=O)C,CCN(C(=O)Cn1nc2n(c1=O)CC,24,add C at position 23,flow_matching,0.3,2.0,49,100
76,add,24.0,C,,CCN(C(=O)Cn1nc2n(c1=O)CC,CCN(C(=O)Cn1nc2n(c1=O)CCC,25,add C at position 24,flow_matching,0.3,2.0,49,100
77,add,25.0,C,,CCN(C(=O)Cn1nc2n(c1=O)CCC,CCN(C(=O)Cn1nc2n(c1=O)CCCC,26,add C at position 25,flow_matching,0.3,2.0,49,100
78,add,26.0,C,,CCN(C(=O)Cn1nc2n(c1=O)CCCC,CCN(C(=O)Cn1nc2n(c1=O)CCCCC,27,add C at position 26,flow_matching,0.3,2.0,49,100
79,add,27.0,2,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2,28,add 2 at position 27,flow_matching,0.3,2.0,49,100
80,add,28.0,),,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2),29,add ) at position 28,flow_matching,0.3,2.0,49,100
81,add,29.0,[,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2),CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[,30,add [ at position 29,flow_matching,0.3,2.0,49,100
82,add,30.0,C,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C,31,add C at position 30,flow_matching,0.3,2.0,49,100
83,add,31.0,@,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@,32,add @ at position 31,flow_matching,0.3,2.0,49,100
84,add,32.0,H,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H,33,add H at position 32,flow_matching,0.3,2.0,49,100
85,add,33.0,],,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H],34,add ] at position 33,flow_matching,0.3,2.0,49,100
86,add,34.0,1,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H],CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1,35,add 1 at position 34,flow_matching,0.3,2.0,49,100
87,add,35.0,C,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1C,36,add C at position 35,flow_matching,0.3,2.0,49,100
88,add,36.0,C,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1C,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CC,37,add C at position 36,flow_matching,0.3,2.0,49,100
89,add,37.0,S,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CC,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS,38,add S at position 37,flow_matching,0.3,2.0,49,100
90,add,38.0,(,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(,39,add ( at position 38,flow_matching,0.3,2.0,49,100
91,add,39.0,=,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=,40,add = at position 39,flow_matching,0.3,2.0,49,100
92,add,40.0,O,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O,41,add O at position 40,flow_matching,0.3,2.0,49,100
93,add,41.0,),,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O),42,add ) at position 41,flow_matching,0.3,2.0,49,100
94,add,42.0,(,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O),CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(,43,add ( at position 42,flow_matching,0.3,2.0,49,100
95,add,43.0,=,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=,44,add = at position 43,flow_matching,0.3,2.0,49,100
96,add,44.0,O,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=O,45,add O at position 44,flow_matching,0.3,2.0,49,100
97,add,45.0,),,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=O,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=O),46,add ) at position 45,flow_matching,0.3,2.0,49,100
98,add,46.0,C,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=O),CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=O)C,47,add C at position 46,flow_matching,0.3,2.0,49,100
99,add,47.0,1,,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=O)C,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=O)C1,48,add 1 at position 47,flow_matching,0.3,2.0,49,100
100,add,48.0,"
",,CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=O)C1,"CCN(C(=O)Cn1nc2n(c1=O)CCCCC2)[C@H]1CCS(=O)(=O)C1
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,100
