step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,47,154
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,47,154
2,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,47,154
3,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,47,154
4,replace,0.0,C,O,O,C,1,replace <PERSON> at position 0 with C,flow_matching,0.3,2.0,47,154
5,add,0.0,2,,C,2C,2,add 2 at position 0,flow_matching,0.3,2.0,47,154
6,add,1.0,B,,2C,2BC,3,add <PERSON> at position 1,flow_matching,0.3,2.0,47,154
7,replace,2.0,/,C,2BC,2B/,3,replace <PERSON> at position 2 with /,flow_matching,0.3,2.0,47,154
8,replace,0.0,\,2,2B/,\B/,3,replace 2 at position 0 with \,flow_matching,0.3,2.0,47,154
9,remove,1.0,B,,\B/,\/,2,remove B from position 1,flow_matching,0.3,2.0,47,154
10,replace,0.0,C,\,\/,C/,2,replace \ at position 0 with C,flow_matching,0.3,2.0,47,154
11,add,1.0,S,,C/,CS/,3,add S at position 1,flow_matching,0.3,2.0,47,154
12,replace,2.0,1,/,CS/,CS1,3,replace / at position 2 with 1,flow_matching,0.3,2.0,47,154
13,replace,1.0,O,S,CS1,CO1,3,replace S at position 1 with O,flow_matching,0.3,2.0,47,154
14,replace,2.0,c,1,CO1,COc,3,replace 1 at position 2 with c,flow_matching,0.3,2.0,47,154
15,add,0.0,7,,COc,7COc,4,add 7 at position 0,flow_matching,0.3,2.0,47,154
16,replace,0.0,C,7,7COc,CCOc,4,replace 7 at position 0 with C,flow_matching,0.3,2.0,47,154
17,replace,3.0,F,c,CCOc,CCOF,4,replace c at position 3 with F,flow_matching,0.3,2.0,47,154
18,remove,2.0,O,,CCOF,CCF,3,remove O from position 2,flow_matching,0.3,2.0,47,154
19,replace,1.0,O,C,CCF,COF,3,replace C at position 1 with O,flow_matching,0.3,2.0,47,154
20,replace,2.0,c,F,COF,COc,3,replace F at position 2 with c,flow_matching,0.3,2.0,47,154
21,remove,2.0,c,,COc,CO,2,remove c from position 2,flow_matching,0.3,2.0,47,154
22,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,47,154
23,add,3.0,1,,COc,COc1,4,add 1 at position 3,flow_matching,0.3,2.0,47,154
24,replace,3.0,-,1,COc1,COc-,4,replace 1 at position 3 with -,flow_matching,0.3,2.0,47,154
25,add,1.0,3,,COc-,C3Oc-,5,add 3 at position 1,flow_matching,0.3,2.0,47,154
26,remove,2.0,O,,C3Oc-,C3c-,4,remove O from position 2,flow_matching,0.3,2.0,47,154
27,replace,1.0,O,3,C3c-,COc-,4,replace 3 at position 1 with O,flow_matching,0.3,2.0,47,154
28,replace,3.0,1,-,COc-,COc1,4,replace - at position 3 with 1,flow_matching,0.3,2.0,47,154
29,add,1.0,[,,COc1,C[Oc1,5,add [ at position 1,flow_matching,0.3,2.0,47,154
30,replace,0.0,),C,C[Oc1,)[Oc1,5,replace C at position 0 with ),flow_matching,0.3,2.0,47,154
31,add,5.0,o,,)[Oc1,)[Oc1o,6,add o at position 5,flow_matching,0.3,2.0,47,154
32,remove,5.0,o,,)[Oc1o,)[Oc1,5,remove o from position 5,flow_matching,0.3,2.0,47,154
33,add,1.0,],,)[Oc1,)][Oc1,6,add ] at position 1,flow_matching,0.3,2.0,47,154
34,add,3.0,],,)][Oc1,)][]Oc1,7,add ] at position 3,flow_matching,0.3,2.0,47,154
35,replace,5.0,C,c,)][]Oc1,)][]OC1,7,replace c at position 5 with C,flow_matching,0.3,2.0,47,154
36,replace,0.0,C,),)][]OC1,C][]OC1,7,replace ) at position 0 with C,flow_matching,0.3,2.0,47,154
37,replace,1.0,O,],C][]OC1,CO[]OC1,7,replace ] at position 1 with O,flow_matching,0.3,2.0,47,154
38,replace,0.0,B,C,CO[]OC1,BO[]OC1,7,replace C at position 0 with B,flow_matching,0.3,2.0,47,154
39,replace,5.0,],C,BO[]OC1,BO[]O]1,7,replace C at position 5 with ],flow_matching,0.3,2.0,47,154
40,add,4.0,N,,BO[]O]1,BO[]NO]1,8,add N at position 4,flow_matching,0.3,2.0,47,154
41,remove,5.0,O,,BO[]NO]1,BO[]N]1,7,remove O from position 5,flow_matching,0.3,2.0,47,154
42,add,6.0,F,,BO[]N]1,BO[]N]F1,8,add F at position 6,flow_matching,0.3,2.0,47,154
43,replace,4.0,o,N,BO[]N]F1,BO[]o]F1,8,replace N at position 4 with o,flow_matching,0.3,2.0,47,154
44,add,7.0,S,,BO[]o]F1,BO[]o]FS1,9,add S at position 7,flow_matching,0.3,2.0,47,154
45,replace,0.0,C,B,BO[]o]FS1,CO[]o]FS1,9,replace B at position 0 with C,flow_matching,0.3,2.0,47,154
46,add,8.0,-,,CO[]o]FS1,CO[]o]FS-1,10,add - at position 8,flow_matching,0.3,2.0,47,154
47,replace,2.0,c,[,CO[]o]FS-1,COc]o]FS-1,10,replace [ at position 2 with c,flow_matching,0.3,2.0,47,154
48,add,7.0,r,,COc]o]FS-1,COc]o]FrS-1,11,add r at position 7,flow_matching,0.3,2.0,47,154
49,add,7.0,H,,COc]o]FrS-1,COc]o]FHrS-1,12,add H at position 7,flow_matching,0.3,2.0,47,154
50,add,1.0,6,,COc]o]FHrS-1,C6Oc]o]FHrS-1,13,add 6 at position 1,flow_matching,0.3,2.0,47,154
51,add,8.0,F,,C6Oc]o]FHrS-1,C6Oc]o]FFHrS-1,14,add F at position 8,flow_matching,0.3,2.0,47,154
52,replace,1.0,O,6,C6Oc]o]FFHrS-1,COOc]o]FFHrS-1,14,replace 6 at position 1 with O,flow_matching,0.3,2.0,47,154
53,replace,7.0,H,F,COOc]o]FFHrS-1,COOc]o]HFHrS-1,14,replace F at position 7 with H,flow_matching,0.3,2.0,47,154
54,add,1.0,S,,COOc]o]HFHrS-1,CSOOc]o]HFHrS-1,15,add S at position 1,flow_matching,0.3,2.0,47,154
55,add,15.0,l,,CSOOc]o]HFHrS-1,CSOOc]o]HFHrS-1l,16,add l at position 15,flow_matching,0.3,2.0,47,154
56,replace,1.0,O,S,CSOOc]o]HFHrS-1l,COOOc]o]HFHrS-1l,16,replace S at position 1 with O,flow_matching,0.3,2.0,47,154
57,replace,2.0,c,O,COOOc]o]HFHrS-1l,COcOc]o]HFHrS-1l,16,replace O at position 2 with c,flow_matching,0.3,2.0,47,154
58,add,16.0,N,,COcOc]o]HFHrS-1l,COcOc]o]HFHrS-1lN,17,add N at position 16,flow_matching,0.3,2.0,47,154
59,replace,3.0,1,O,COcOc]o]HFHrS-1lN,COc1c]o]HFHrS-1lN,17,replace O at position 3 with 1,flow_matching,0.3,2.0,47,154
60,replace,2.0,(,c,COc1c]o]HFHrS-1lN,CO(1c]o]HFHrS-1lN,17,replace c at position 2 with (,flow_matching,0.3,2.0,47,154
61,replace,16.0,I,N,CO(1c]o]HFHrS-1lN,CO(1c]o]HFHrS-1lI,17,replace N at position 16 with I,flow_matching,0.3,2.0,47,154
62,replace,2.0,c,(,CO(1c]o]HFHrS-1lI,COc1c]o]HFHrS-1lI,17,replace ( at position 2 with c,flow_matching,0.3,2.0,47,154
63,remove,3.0,1,,COc1c]o]HFHrS-1lI,COcc]o]HFHrS-1lI,16,remove 1 from position 3,flow_matching,0.3,2.0,47,154
64,remove,7.0,H,,COcc]o]HFHrS-1lI,COcc]o]FHrS-1lI,15,remove H from position 7,flow_matching,0.3,2.0,47,154
65,replace,3.0,1,c,COcc]o]FHrS-1lI,COc1]o]FHrS-1lI,15,replace c at position 3 with 1,flow_matching,0.3,2.0,47,154
66,add,8.0,#,,COc1]o]FHrS-1lI,COc1]o]F#HrS-1lI,16,add # at position 8,flow_matching,0.3,2.0,47,154
67,replace,4.0,c,],COc1]o]F#HrS-1lI,COc1co]F#HrS-1lI,16,replace ] at position 4 with c,flow_matching,0.3,2.0,47,154
68,add,10.0,H,,COc1co]F#HrS-1lI,COc1co]F#HHrS-1lI,17,add H at position 10,flow_matching,0.3,2.0,47,154
69,add,16.0,2,,COc1co]F#HHrS-1lI,COc1co]F#HHrS-1l2I,18,add 2 at position 16,flow_matching,0.3,2.0,47,154
70,replace,5.0,c,o,COc1co]F#HHrS-1l2I,COc1cc]F#HHrS-1l2I,18,replace o at position 5 with c,flow_matching,0.3,2.0,47,154
71,replace,6.0,c,],COc1cc]F#HHrS-1l2I,COc1cccF#HHrS-1l2I,18,replace ] at position 6 with c,flow_matching,0.3,2.0,47,154
72,add,15.0,[,,COc1cccF#HHrS-1l2I,COc1cccF#HHrS-1[l2I,19,add [ at position 15,flow_matching,0.3,2.0,47,154
73,remove,10.0,H,,COc1cccF#HHrS-1[l2I,COc1cccF#HrS-1[l2I,18,remove H from position 10,flow_matching,0.3,2.0,47,154
74,replace,17.0,6,I,COc1cccF#HrS-1[l2I,COc1cccF#HrS-1[l26,18,replace I at position 17 with 6,flow_matching,0.3,2.0,47,154
75,remove,3.0,1,,COc1cccF#HrS-1[l26,COccccF#HrS-1[l26,17,remove 1 from position 3,flow_matching,0.3,2.0,47,154
76,add,14.0,c,,COccccF#HrS-1[l26,COccccF#HrS-1[cl26,18,add c at position 14,flow_matching,0.3,2.0,47,154
77,add,4.0,],,COccccF#HrS-1[cl26,COcc]ccF#HrS-1[cl26,19,add ] at position 4,flow_matching,0.3,2.0,47,154
78,replace,3.0,1,c,COcc]ccF#HrS-1[cl26,COc1]ccF#HrS-1[cl26,19,replace c at position 3 with 1,flow_matching,0.3,2.0,47,154
79,remove,7.0,F,,COc1]ccF#HrS-1[cl26,COc1]cc#HrS-1[cl26,18,remove F from position 7,flow_matching,0.3,2.0,47,154
80,remove,14.0,c,,COc1]cc#HrS-1[cl26,COc1]cc#HrS-1[l26,17,remove c from position 14,flow_matching,0.3,2.0,47,154
81,replace,4.0,c,],COc1]cc#HrS-1[l26,COc1ccc#HrS-1[l26,17,replace ] at position 4 with c,flow_matching,0.3,2.0,47,154
82,add,7.0,S,,COc1ccc#HrS-1[l26,COc1cccS#HrS-1[l26,18,add S at position 7,flow_matching,0.3,2.0,47,154
83,replace,3.0,+,1,COc1cccS#HrS-1[l26,COc+cccS#HrS-1[l26,18,replace 1 at position 3 with +,flow_matching,0.3,2.0,47,154
84,remove,15.0,l,,COc+cccS#HrS-1[l26,COc+cccS#HrS-1[26,17,remove l from position 15,flow_matching,0.3,2.0,47,154
85,replace,9.0,=,H,COc+cccS#HrS-1[26,COc+cccS#=rS-1[26,17,replace H at position 9 with =,flow_matching,0.3,2.0,47,154
86,replace,3.0,1,+,COc+cccS#=rS-1[26,COc1cccS#=rS-1[26,17,replace + at position 3 with 1,flow_matching,0.3,2.0,47,154
87,replace,16.0,=,6,COc1cccS#=rS-1[26,COc1cccS#=rS-1[2=,17,replace 6 at position 16 with =,flow_matching,0.3,2.0,47,154
88,replace,7.0,(,S,COc1cccS#=rS-1[2=,COc1ccc(#=rS-1[2=,17,replace S at position 7 with (,flow_matching,0.3,2.0,47,154
89,add,7.0,l,,COc1ccc(#=rS-1[2=,COc1cccl(#=rS-1[2=,18,add l at position 7,flow_matching,0.3,2.0,47,154
90,replace,7.0,(,l,COc1cccl(#=rS-1[2=,COc1ccc((#=rS-1[2=,18,replace l at position 7 with (,flow_matching,0.3,2.0,47,154
91,replace,8.0,[,(,COc1ccc((#=rS-1[2=,COc1ccc([#=rS-1[2=,18,replace ( at position 8 with [,flow_matching,0.3,2.0,47,154
92,replace,10.0,r,=,COc1ccc([#=rS-1[2=,COc1ccc([#rrS-1[2=,18,replace = at position 10 with r,flow_matching,0.3,2.0,47,154
93,replace,9.0,C,#,COc1ccc([#rrS-1[2=,COc1ccc([CrrS-1[2=,18,replace # at position 9 with C,flow_matching,0.3,2.0,47,154
94,add,8.0,B,,COc1ccc([CrrS-1[2=,COc1ccc(B[CrrS-1[2=,19,add B at position 8,flow_matching,0.3,2.0,47,154
95,remove,5.0,c,,COc1ccc(B[CrrS-1[2=,COc1cc(B[CrrS-1[2=,18,remove c from position 5,flow_matching,0.3,2.0,47,154
96,replace,6.0,c,(,COc1cc(B[CrrS-1[2=,COc1cccB[CrrS-1[2=,18,replace ( at position 6 with c,flow_matching,0.3,2.0,47,154
97,remove,14.0,1,,COc1cccB[CrrS-1[2=,COc1cccB[CrrS-[2=,17,remove 1 from position 14,flow_matching,0.3,2.0,47,154
98,replace,15.0,\,2,COc1cccB[CrrS-[2=,COc1cccB[CrrS-[\=,17,replace 2 at position 15 with \,flow_matching,0.3,2.0,47,154
99,replace,7.0,(,B,COc1cccB[CrrS-[\=,COc1ccc([CrrS-[\=,17,replace B at position 7 with (,flow_matching,0.3,2.0,47,154
100,add,12.0,-,,COc1ccc([CrrS-[\=,COc1ccc([Crr-S-[\=,18,add - at position 12,flow_matching,0.3,2.0,47,154
101,remove,6.0,c,,COc1ccc([Crr-S-[\=,COc1cc([Crr-S-[\=,17,remove c from position 6,flow_matching,0.3,2.0,47,154
102,replace,5.0,l,c,COc1cc([Crr-S-[\=,COc1cl([Crr-S-[\=,17,replace c at position 5 with l,flow_matching,0.3,2.0,47,154
103,add,8.0,\,,COc1cl([Crr-S-[\=,COc1cl([\Crr-S-[\=,18,add \ at position 8,flow_matching,0.3,2.0,47,154
104,add,17.0,(,,COc1cl([\Crr-S-[\=,COc1cl([\Crr-S-[\(=,19,add ( at position 17,flow_matching,0.3,2.0,47,154
105,replace,5.0,c,l,COc1cl([\Crr-S-[\(=,COc1cc([\Crr-S-[\(=,19,replace l at position 5 with c,flow_matching,0.3,2.0,47,154
106,replace,6.0,c,(,COc1cc([\Crr-S-[\(=,COc1ccc[\Crr-S-[\(=,19,replace ( at position 6 with c,flow_matching,0.3,2.0,47,154
107,add,2.0,I,,COc1ccc[\Crr-S-[\(=,COIc1ccc[\Crr-S-[\(=,20,add I at position 2,flow_matching,0.3,2.0,47,154
108,add,3.0,S,,COIc1ccc[\Crr-S-[\(=,COISc1ccc[\Crr-S-[\(=,21,add S at position 3,flow_matching,0.3,2.0,47,154
109,replace,2.0,c,I,COISc1ccc[\Crr-S-[\(=,COcSc1ccc[\Crr-S-[\(=,21,replace I at position 2 with c,flow_matching,0.3,2.0,47,154
110,add,11.0,\,,COcSc1ccc[\Crr-S-[\(=,COcSc1ccc[\\Crr-S-[\(=,22,add \ at position 11,flow_matching,0.3,2.0,47,154
111,replace,3.0,1,S,COcSc1ccc[\\Crr-S-[\(=,COc1c1ccc[\\Crr-S-[\(=,22,replace S at position 3 with 1,flow_matching,0.3,2.0,47,154
112,remove,15.0,-,,COc1c1ccc[\\Crr-S-[\(=,COc1c1ccc[\\CrrS-[\(=,21,remove - from position 15,flow_matching,0.3,2.0,47,154
113,replace,5.0,c,1,COc1c1ccc[\\CrrS-[\(=,COc1ccccc[\\CrrS-[\(=,21,replace 1 at position 5 with c,flow_matching,0.3,2.0,47,154
114,replace,7.0,(,c,COc1ccccc[\\CrrS-[\(=,COc1ccc(c[\\CrrS-[\(=,21,replace c at position 7 with (,flow_matching,0.3,2.0,47,154
115,replace,14.0,@,r,COc1ccc(c[\\CrrS-[\(=,COc1ccc(c[\\Cr@S-[\(=,21,replace r at position 14 with @,flow_matching,0.3,2.0,47,154
116,replace,8.0,[,c,COc1ccc(c[\\Cr@S-[\(=,COc1ccc([[\\Cr@S-[\(=,21,replace c at position 8 with [,flow_matching,0.3,2.0,47,154
117,replace,9.0,C,[,COc1ccc([[\\Cr@S-[\(=,COc1ccc([C\\Cr@S-[\(=,21,replace [ at position 9 with C,flow_matching,0.3,2.0,47,154
118,replace,10.0,@,\,COc1ccc([C\\Cr@S-[\(=,COc1ccc([C@\Cr@S-[\(=,21,replace \ at position 10 with @,flow_matching,0.3,2.0,47,154
119,replace,11.0,H,\,COc1ccc([C@\Cr@S-[\(=,COc1ccc([C@HCr@S-[\(=,21,replace \ at position 11 with H,flow_matching,0.3,2.0,47,154
120,replace,12.0,],C,COc1ccc([C@HCr@S-[\(=,COc1ccc([C@H]r@S-[\(=,21,replace C at position 12 with ],flow_matching,0.3,2.0,47,154
121,replace,13.0,(,r,COc1ccc([C@H]r@S-[\(=,COc1ccc([C@H](@S-[\(=,21,replace r at position 13 with (,flow_matching,0.3,2.0,47,154
122,replace,14.0,O,@,COc1ccc([C@H](@S-[\(=,COc1ccc([C@H](OS-[\(=,21,replace @ at position 14 with O,flow_matching,0.3,2.0,47,154
123,replace,15.0,),S,COc1ccc([C@H](OS-[\(=,COc1ccc([C@H](O)-[\(=,21,replace S at position 15 with ),flow_matching,0.3,2.0,47,154
124,replace,16.0,[,-,COc1ccc([C@H](O)-[\(=,COc1ccc([C@H](O)[[\(=,21,replace - at position 16 with [,flow_matching,0.3,2.0,47,154
125,replace,17.0,C,[,COc1ccc([C@H](O)[[\(=,COc1ccc([C@H](O)[C\(=,21,replace [ at position 17 with C,flow_matching,0.3,2.0,47,154
126,replace,18.0,@,\,COc1ccc([C@H](O)[C\(=,COc1ccc([C@H](O)[C@(=,21,replace \ at position 18 with @,flow_matching,0.3,2.0,47,154
127,replace,19.0,@,(,COc1ccc([C@H](O)[C@(=,COc1ccc([C@H](O)[C@@=,21,replace ( at position 19 with @,flow_matching,0.3,2.0,47,154
128,replace,20.0,H,=,COc1ccc([C@H](O)[C@@=,COc1ccc([C@H](O)[C@@H,21,replace = at position 20 with H,flow_matching,0.3,2.0,47,154
129,add,21.0,],,COc1ccc([C@H](O)[C@@H,COc1ccc([C@H](O)[C@@H],22,add ] at position 21,flow_matching,0.3,2.0,47,154
130,add,22.0,(,,COc1ccc([C@H](O)[C@@H],COc1ccc([C@H](O)[C@@H](,23,add ( at position 22,flow_matching,0.3,2.0,47,154
131,add,23.0,C,,COc1ccc([C@H](O)[C@@H](,COc1ccc([C@H](O)[C@@H](C,24,add C at position 23,flow_matching,0.3,2.0,47,154
132,add,24.0,),,COc1ccc([C@H](O)[C@@H](C,COc1ccc([C@H](O)[C@@H](C),25,add ) at position 24,flow_matching,0.3,2.0,47,154
133,add,25.0,N,,COc1ccc([C@H](O)[C@@H](C),COc1ccc([C@H](O)[C@@H](C)N,26,add N at position 25,flow_matching,0.3,2.0,47,154
134,add,26.0,C,,COc1ccc([C@H](O)[C@@H](C)N,COc1ccc([C@H](O)[C@@H](C)NC,27,add C at position 26,flow_matching,0.3,2.0,47,154
135,add,27.0,(,,COc1ccc([C@H](O)[C@@H](C)NC,COc1ccc([C@H](O)[C@@H](C)NC(,28,add ( at position 27,flow_matching,0.3,2.0,47,154
136,add,28.0,=,,COc1ccc([C@H](O)[C@@H](C)NC(,COc1ccc([C@H](O)[C@@H](C)NC(=,29,add = at position 28,flow_matching,0.3,2.0,47,154
137,add,29.0,O,,COc1ccc([C@H](O)[C@@H](C)NC(=,COc1ccc([C@H](O)[C@@H](C)NC(=O,30,add O at position 29,flow_matching,0.3,2.0,47,154
138,add,30.0,),,COc1ccc([C@H](O)[C@@H](C)NC(=O,COc1ccc([C@H](O)[C@@H](C)NC(=O),31,add ) at position 30,flow_matching,0.3,2.0,47,154
139,add,31.0,[,,COc1ccc([C@H](O)[C@@H](C)NC(=O),COc1ccc([C@H](O)[C@@H](C)NC(=O)[,32,add [ at position 31,flow_matching,0.3,2.0,47,154
140,add,32.0,C,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C,33,add C at position 32,flow_matching,0.3,2.0,47,154
141,add,33.0,@,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@,34,add @ at position 33,flow_matching,0.3,2.0,47,154
142,add,34.0,@,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@,35,add @ at position 34,flow_matching,0.3,2.0,47,154
143,add,35.0,H,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H,36,add H at position 35,flow_matching,0.3,2.0,47,154
144,add,36.0,],,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H],37,add ] at position 36,flow_matching,0.3,2.0,47,154
145,add,37.0,(,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H],COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](,38,add ( at position 37,flow_matching,0.3,2.0,47,154
146,add,38.0,C,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C,39,add C at position 38,flow_matching,0.3,2.0,47,154
147,add,39.0,),,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C),40,add ) at position 39,flow_matching,0.3,2.0,47,154
148,add,40.0,S,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C),COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)S,41,add S at position 40,flow_matching,0.3,2.0,47,154
149,add,41.0,C,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)S,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC,42,add C at position 41,flow_matching,0.3,2.0,47,154
150,add,42.0,),,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC),43,add ) at position 42,flow_matching,0.3,2.0,47,154
151,add,43.0,c,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC),COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC)c,44,add c at position 43,flow_matching,0.3,2.0,47,154
152,add,44.0,c,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC)c,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC)cc,45,add c at position 44,flow_matching,0.3,2.0,47,154
153,add,45.0,1,,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC)cc,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC)cc1,46,add 1 at position 45,flow_matching,0.3,2.0,47,154
154,add,46.0,"
",,COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC)cc1,"COc1ccc([C@H](O)[C@@H](C)NC(=O)[C@@H](C)SC)cc1
",47,"add 
 at position 46",flow_matching,0.3,2.0,47,154
