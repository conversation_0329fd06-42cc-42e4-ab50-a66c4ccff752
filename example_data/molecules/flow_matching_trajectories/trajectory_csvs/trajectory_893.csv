step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,38,95
1,add,0.0,2,,,2,1,add 2 at position 0,flow_matching,0.3,2.0,38,95
2,add,1.0,S,,2,2S,2,add S at position 1,flow_matching,0.3,2.0,38,95
3,remove,0.0,2,,2S,S,1,remove 2 from position 0,flow_matching,0.3,2.0,38,95
4,replace,0.0,O,S,S,O,1,replace S at position 0 with O,flow_matching,0.3,2.0,38,95
5,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,38,95
6,add,2.0,C,,O=,O=C,3,add <PERSON> at position 2,flow_matching,0.3,2.0,38,95
7,add,3.0,(,,O=C,O=C(,4,add ( at position 3,flow_matching,0.3,2.0,38,95
8,replace,0.0,@,O,O=C(,@=C(,4,replace O at position 0 with @,flow_matching,0.3,2.0,38,95
9,remove,3.0,(,,@=C(,@=C,3,remove ( from position 3,flow_matching,0.3,2.0,38,95
10,replace,0.0,O,@,@=C,O=C,3,replace @ at position 0 with O,flow_matching,0.3,2.0,38,95
11,remove,2.0,C,,O=C,O=,2,remove C from position 2,flow_matching,0.3,2.0,38,95
12,add,2.0,O,,O=,O=O,3,add O at position 2,flow_matching,0.3,2.0,38,95
13,replace,2.0,C,O,O=O,O=C,3,replace O at position 2 with C,flow_matching,0.3,2.0,38,95
14,remove,0.0,O,,O=C,=C,2,remove O from position 0,flow_matching,0.3,2.0,38,95
15,remove,1.0,C,,=C,=,1,remove C from position 1,flow_matching,0.3,2.0,38,95
16,remove,0.0,=,,=,,0,remove = from position 0,flow_matching,0.3,2.0,38,95
17,add,0.0,=,,,=,1,add = at position 0,flow_matching,0.3,2.0,38,95
18,replace,0.0,O,=,=,O,1,replace = at position 0 with O,flow_matching,0.3,2.0,38,95
19,replace,0.0,-,O,O,-,1,replace O at position 0 with -,flow_matching,0.3,2.0,38,95
20,add,0.0,5,,-,5-,2,add 5 at position 0,flow_matching,0.3,2.0,38,95
21,remove,0.0,5,,5-,-,1,remove 5 from position 0,flow_matching,0.3,2.0,38,95
22,add,1.0,O,,-,-O,2,add O at position 1,flow_matching,0.3,2.0,38,95
23,remove,0.0,-,,-O,O,1,remove - from position 0,flow_matching,0.3,2.0,38,95
24,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,38,95
25,replace,1.0,@,=,O=,O@,2,replace = at position 1 with @,flow_matching,0.3,2.0,38,95
26,replace,1.0,=,@,O@,O=,2,replace @ at position 1 with =,flow_matching,0.3,2.0,38,95
27,replace,1.0,4,=,O=,O4,2,replace = at position 1 with 4,flow_matching,0.3,2.0,38,95
28,add,0.0,c,,O4,cO4,3,add c at position 0,flow_matching,0.3,2.0,38,95
29,replace,0.0,O,c,cO4,OO4,3,replace c at position 0 with O,flow_matching,0.3,2.0,38,95
30,replace,1.0,1,O,OO4,O14,3,replace O at position 1 with 1,flow_matching,0.3,2.0,38,95
31,replace,1.0,=,1,O14,O=4,3,replace 1 at position 1 with =,flow_matching,0.3,2.0,38,95
32,replace,2.0,C,4,O=4,O=C,3,replace 4 at position 2 with C,flow_matching,0.3,2.0,38,95
33,add,1.0,(,,O=C,O(=C,4,add ( at position 1,flow_matching,0.3,2.0,38,95
34,add,4.0,[,,O(=C,O(=C[,5,add [ at position 4,flow_matching,0.3,2.0,38,95
35,replace,1.0,=,(,O(=C[,O==C[,5,replace ( at position 1 with =,flow_matching,0.3,2.0,38,95
36,remove,0.0,O,,O==C[,==C[,4,remove O from position 0,flow_matching,0.3,2.0,38,95
37,add,4.0,2,,==C[,==C[2,5,add 2 at position 4,flow_matching,0.3,2.0,38,95
38,replace,0.0,O,=,==C[2,O=C[2,5,replace = at position 0 with O,flow_matching,0.3,2.0,38,95
39,add,1.0,5,,O=C[2,O5=C[2,6,add 5 at position 1,flow_matching,0.3,2.0,38,95
40,add,1.0,N,,O5=C[2,ON5=C[2,7,add N at position 1,flow_matching,0.3,2.0,38,95
41,add,6.0,F,,ON5=C[2,ON5=C[F2,8,add F at position 6,flow_matching,0.3,2.0,38,95
42,replace,1.0,[,N,ON5=C[F2,O[5=C[F2,8,replace N at position 1 with [,flow_matching,0.3,2.0,38,95
43,replace,1.0,=,[,O[5=C[F2,O=5=C[F2,8,replace [ at position 1 with =,flow_matching,0.3,2.0,38,95
44,add,6.0,=,,O=5=C[F2,O=5=C[=F2,9,add = at position 6,flow_matching,0.3,2.0,38,95
45,add,3.0,=,,O=5=C[=F2,O=5==C[=F2,10,add = at position 3,flow_matching,0.3,2.0,38,95
46,replace,2.0,C,5,O=5==C[=F2,O=C==C[=F2,10,replace 5 at position 2 with C,flow_matching,0.3,2.0,38,95
47,replace,2.0,=,C,O=C==C[=F2,O====C[=F2,10,replace C at position 2 with =,flow_matching,0.3,2.0,38,95
48,remove,3.0,=,,O====C[=F2,O===C[=F2,9,remove = from position 3,flow_matching,0.3,2.0,38,95
49,add,8.0,4,,O===C[=F2,O===C[=F42,10,add 4 at position 8,flow_matching,0.3,2.0,38,95
50,add,1.0,l,,O===C[=F42,Ol===C[=F42,11,add l at position 1,flow_matching,0.3,2.0,38,95
51,replace,1.0,=,l,Ol===C[=F42,O====C[=F42,11,replace l at position 1 with =,flow_matching,0.3,2.0,38,95
52,remove,1.0,=,,O====C[=F42,O===C[=F42,10,remove = from position 1,flow_matching,0.3,2.0,38,95
53,add,10.0,B,,O===C[=F42,O===C[=F42B,11,add B at position 10,flow_matching,0.3,2.0,38,95
54,replace,2.0,C,=,O===C[=F42B,O=C=C[=F42B,11,replace = at position 2 with C,flow_matching,0.3,2.0,38,95
55,replace,3.0,(,=,O=C=C[=F42B,O=C(C[=F42B,11,replace = at position 3 with (,flow_matching,0.3,2.0,38,95
56,replace,4.0,[,C,O=C(C[=F42B,O=C([[=F42B,11,replace C at position 4 with [,flow_matching,0.3,2.0,38,95
57,remove,8.0,4,,O=C([[=F42B,O=C([[=F2B,10,remove 4 from position 8,flow_matching,0.3,2.0,38,95
58,add,5.0,s,,O=C([[=F2B,O=C([s[=F2B,11,add s at position 5,flow_matching,0.3,2.0,38,95
59,remove,4.0,[,,O=C([s[=F2B,O=C(s[=F2B,10,remove [ from position 4,flow_matching,0.3,2.0,38,95
60,replace,4.0,[,s,O=C(s[=F2B,O=C([[=F2B,10,replace s at position 4 with [,flow_matching,0.3,2.0,38,95
61,replace,5.0,O,[,O=C([[=F2B,O=C([O=F2B,10,replace [ at position 5 with O,flow_matching,0.3,2.0,38,95
62,remove,7.0,F,,O=C([O=F2B,O=C([O=2B,9,remove F from position 7,flow_matching,0.3,2.0,38,95
63,replace,6.0,-,=,O=C([O=2B,O=C([O-2B,9,replace = at position 6 with -,flow_matching,0.3,2.0,38,95
64,add,7.0,I,,O=C([O-2B,O=C([O-I2B,10,add I at position 7,flow_matching,0.3,2.0,38,95
65,replace,7.0,],I,O=C([O-I2B,O=C([O-]2B,10,replace I at position 7 with ],flow_matching,0.3,2.0,38,95
66,replace,8.0,),2,O=C([O-]2B,O=C([O-])B,10,replace 2 at position 8 with ),flow_matching,0.3,2.0,38,95
67,replace,9.0,[,B,O=C([O-])B,O=C([O-])[,10,replace B at position 9 with [,flow_matching,0.3,2.0,38,95
68,add,10.0,C,,O=C([O-])[,O=C([O-])[C,11,add C at position 10,flow_matching,0.3,2.0,38,95
69,add,11.0,@,,O=C([O-])[C,O=C([O-])[C@,12,add @ at position 11,flow_matching,0.3,2.0,38,95
70,add,12.0,H,,O=C([O-])[C@,O=C([O-])[C@H,13,add H at position 12,flow_matching,0.3,2.0,38,95
71,add,13.0,],,O=C([O-])[C@H,O=C([O-])[C@H],14,add ] at position 13,flow_matching,0.3,2.0,38,95
72,add,14.0,1,,O=C([O-])[C@H],O=C([O-])[C@H]1,15,add 1 at position 14,flow_matching,0.3,2.0,38,95
73,add,15.0,C,,O=C([O-])[C@H]1,O=C([O-])[C@H]1C,16,add C at position 15,flow_matching,0.3,2.0,38,95
74,add,16.0,C,,O=C([O-])[C@H]1C,O=C([O-])[C@H]1CC,17,add C at position 16,flow_matching,0.3,2.0,38,95
75,add,17.0,C,,O=C([O-])[C@H]1CC,O=C([O-])[C@H]1CCC,18,add C at position 17,flow_matching,0.3,2.0,38,95
76,add,18.0,N,,O=C([O-])[C@H]1CCC,O=C([O-])[C@H]1CCCN,19,add N at position 18,flow_matching,0.3,2.0,38,95
77,add,19.0,(,,O=C([O-])[C@H]1CCCN,O=C([O-])[C@H]1CCCN(,20,add ( at position 19,flow_matching,0.3,2.0,38,95
78,add,20.0,c,,O=C([O-])[C@H]1CCCN(,O=C([O-])[C@H]1CCCN(c,21,add c at position 20,flow_matching,0.3,2.0,38,95
79,add,21.0,2,,O=C([O-])[C@H]1CCCN(c,O=C([O-])[C@H]1CCCN(c2,22,add 2 at position 21,flow_matching,0.3,2.0,38,95
80,add,22.0,c,,O=C([O-])[C@H]1CCCN(c2,O=C([O-])[C@H]1CCCN(c2c,23,add c at position 22,flow_matching,0.3,2.0,38,95
81,add,23.0,c,,O=C([O-])[C@H]1CCCN(c2c,O=C([O-])[C@H]1CCCN(c2cc,24,add c at position 23,flow_matching,0.3,2.0,38,95
82,add,24.0,c,,O=C([O-])[C@H]1CCCN(c2cc,O=C([O-])[C@H]1CCCN(c2ccc,25,add c at position 24,flow_matching,0.3,2.0,38,95
83,add,25.0,(,,O=C([O-])[C@H]1CCCN(c2ccc,O=C([O-])[C@H]1CCCN(c2ccc(,26,add ( at position 25,flow_matching,0.3,2.0,38,95
84,add,26.0,[,,O=C([O-])[C@H]1CCCN(c2ccc(,O=C([O-])[C@H]1CCCN(c2ccc([,27,add [ at position 26,flow_matching,0.3,2.0,38,95
85,add,27.0,O,,O=C([O-])[C@H]1CCCN(c2ccc([,O=C([O-])[C@H]1CCCN(c2ccc([O,28,add O at position 27,flow_matching,0.3,2.0,38,95
86,add,28.0,-,,O=C([O-])[C@H]1CCCN(c2ccc([O,O=C([O-])[C@H]1CCCN(c2ccc([O-,29,add - at position 28,flow_matching,0.3,2.0,38,95
87,add,29.0,],,O=C([O-])[C@H]1CCCN(c2ccc([O-,O=C([O-])[C@H]1CCCN(c2ccc([O-],30,add ] at position 29,flow_matching,0.3,2.0,38,95
88,add,30.0,),,O=C([O-])[C@H]1CCCN(c2ccc([O-],O=C([O-])[C@H]1CCCN(c2ccc([O-]),31,add ) at position 30,flow_matching,0.3,2.0,38,95
89,add,31.0,n,,O=C([O-])[C@H]1CCCN(c2ccc([O-]),O=C([O-])[C@H]1CCCN(c2ccc([O-])n,32,add n at position 31,flow_matching,0.3,2.0,38,95
90,add,32.0,n,,O=C([O-])[C@H]1CCCN(c2ccc([O-])n,O=C([O-])[C@H]1CCCN(c2ccc([O-])nn,33,add n at position 32,flow_matching,0.3,2.0,38,95
91,add,33.0,2,,O=C([O-])[C@H]1CCCN(c2ccc([O-])nn,O=C([O-])[C@H]1CCCN(c2ccc([O-])nn2,34,add 2 at position 33,flow_matching,0.3,2.0,38,95
92,add,34.0,),,O=C([O-])[C@H]1CCCN(c2ccc([O-])nn2,O=C([O-])[C@H]1CCCN(c2ccc([O-])nn2),35,add ) at position 34,flow_matching,0.3,2.0,38,95
93,add,35.0,C,,O=C([O-])[C@H]1CCCN(c2ccc([O-])nn2),O=C([O-])[C@H]1CCCN(c2ccc([O-])nn2)C,36,add C at position 35,flow_matching,0.3,2.0,38,95
94,add,36.0,1,,O=C([O-])[C@H]1CCCN(c2ccc([O-])nn2)C,O=C([O-])[C@H]1CCCN(c2ccc([O-])nn2)C1,37,add 1 at position 36,flow_matching,0.3,2.0,38,95
95,add,37.0,"
",,O=C([O-])[C@H]1CCCN(c2ccc([O-])nn2)C1,"O=C([O-])[C@H]1CCCN(c2ccc([O-])nn2)C1
",38,"add 
 at position 37",flow_matching,0.3,2.0,38,95
