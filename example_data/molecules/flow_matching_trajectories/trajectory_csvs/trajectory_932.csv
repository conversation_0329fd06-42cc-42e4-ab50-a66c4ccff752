step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,60,216
1,add,0.0,S,,,S,1,add S at position 0,flow_matching,0.3,2.0,60,216
2,add,1.0,[,,S,S[,2,add [ at position 1,flow_matching,0.3,2.0,60,216
3,add,1.0,N,,S[,SN[,3,add N at position 1,flow_matching,0.3,2.0,60,216
4,replace,0.0,C,S,SN[,CN[,3,replace S at position 0 with C,flow_matching,0.3,2.0,60,216
5,replace,1.0,C,N,CN[,CC[,3,replace N at position 1 with C,flow_matching,0.3,2.0,60,216
6,replace,1.0,l,<PERSON>,<PERSON>[,Cl[,3,replace <PERSON> at position 1 with l,flow_matching,0.3,2.0,60,216
7,replace,1.0,<PERSON>,l,Cl[,CC[,3,replace l at position 1 with <PERSON>,flow_matching,0.3,2.0,60,216
8,replace,2.0,1,[,CC[,CC1,3,replace [ at position 2 with 1,flow_matching,0.3,2.0,60,216
9,remove,0.0,C,,CC1,C1,2,remove C from position 0,flow_matching,0.3,2.0,60,216
10,replace,1.0,C,1,C1,CC,2,replace 1 at position 1 with C,flow_matching,0.3,2.0,60,216
11,replace,0.0,l,C,CC,lC,2,replace C at position 0 with l,flow_matching,0.3,2.0,60,216
12,replace,0.0,C,l,lC,CC,2,replace l at position 0 with C,flow_matching,0.3,2.0,60,216
13,replace,0.0,=,C,CC,=C,2,replace C at position 0 with =,flow_matching,0.3,2.0,60,216
14,remove,0.0,=,,=C,C,1,remove = from position 0,flow_matching,0.3,2.0,60,216
15,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,60,216
16,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,60,216
17,replace,0.0,C,B,B,C,1,replace B at position 0 with C,flow_matching,0.3,2.0,60,216
18,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,60,216
19,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,60,216
20,add,0.0,\,,6,\6,2,add \ at position 0,flow_matching,0.3,2.0,60,216
21,remove,0.0,\,,\6,6,1,remove \ from position 0,flow_matching,0.3,2.0,60,216
22,add,0.0,7,,6,76,2,add 7 at position 0,flow_matching,0.3,2.0,60,216
23,remove,0.0,7,,76,6,1,remove 7 from position 0,flow_matching,0.3,2.0,60,216
24,replace,0.0,c,6,6,c,1,replace 6 at position 0 with c,flow_matching,0.3,2.0,60,216
25,add,1.0,+,,c,c+,2,add + at position 1,flow_matching,0.3,2.0,60,216
26,add,1.0,),,c+,c)+,3,add ) at position 1,flow_matching,0.3,2.0,60,216
27,replace,0.0,C,c,c)+,C)+,3,replace c at position 0 with C,flow_matching,0.3,2.0,60,216
28,replace,1.0,C,),C)+,CC+,3,replace ) at position 1 with C,flow_matching,0.3,2.0,60,216
29,replace,2.0,1,+,CC+,CC1,3,replace + at position 2 with 1,flow_matching,0.3,2.0,60,216
30,replace,0.0,S,C,CC1,SC1,3,replace C at position 0 with S,flow_matching,0.3,2.0,60,216
31,replace,0.0,C,S,SC1,CC1,3,replace S at position 0 with C,flow_matching,0.3,2.0,60,216
32,add,1.0,6,,CC1,C6C1,4,add 6 at position 1,flow_matching,0.3,2.0,60,216
33,add,3.0,7,,C6C1,C6C71,5,add 7 at position 3,flow_matching,0.3,2.0,60,216
34,remove,3.0,7,,C6C71,C6C1,4,remove 7 from position 3,flow_matching,0.3,2.0,60,216
35,replace,1.0,C,6,C6C1,CCC1,4,replace 6 at position 1 with C,flow_matching,0.3,2.0,60,216
36,replace,1.0,B,C,CCC1,CBC1,4,replace C at position 1 with B,flow_matching,0.3,2.0,60,216
37,add,2.0,l,,CBC1,CBlC1,5,add l at position 2,flow_matching,0.3,2.0,60,216
38,add,3.0,[,,CBlC1,CBl[C1,6,add [ at position 3,flow_matching,0.3,2.0,60,216
39,remove,3.0,[,,CBl[C1,CBlC1,5,remove [ from position 3,flow_matching,0.3,2.0,60,216
40,add,4.0,#,,CBlC1,CBlC#1,6,add # at position 4,flow_matching,0.3,2.0,60,216
41,remove,5.0,1,,CBlC#1,CBlC#,5,remove 1 from position 5,flow_matching,0.3,2.0,60,216
42,remove,0.0,C,,CBlC#,BlC#,4,remove C from position 0,flow_matching,0.3,2.0,60,216
43,replace,0.0,C,B,BlC#,ClC#,4,replace B at position 0 with C,flow_matching,0.3,2.0,60,216
44,remove,2.0,C,,ClC#,Cl#,3,remove C from position 2,flow_matching,0.3,2.0,60,216
45,replace,1.0,C,l,Cl#,CC#,3,replace l at position 1 with C,flow_matching,0.3,2.0,60,216
46,replace,2.0,H,#,CC#,CCH,3,replace # at position 2 with H,flow_matching,0.3,2.0,60,216
47,remove,2.0,H,,CCH,CC,2,remove H from position 2,flow_matching,0.3,2.0,60,216
48,add,0.0,#,,CC,#CC,3,add # at position 0,flow_matching,0.3,2.0,60,216
49,replace,0.0,7,#,#CC,7CC,3,replace # at position 0 with 7,flow_matching,0.3,2.0,60,216
50,add,0.0,#,,7CC,#7CC,4,add # at position 0,flow_matching,0.3,2.0,60,216
51,replace,0.0,C,#,#7CC,C7CC,4,replace # at position 0 with C,flow_matching,0.3,2.0,60,216
52,add,3.0,l,,C7CC,C7ClC,5,add l at position 3,flow_matching,0.3,2.0,60,216
53,replace,1.0,C,7,C7ClC,CCClC,5,replace 7 at position 1 with C,flow_matching,0.3,2.0,60,216
54,replace,3.0,O,l,CCClC,CCCOC,5,replace l at position 3 with O,flow_matching,0.3,2.0,60,216
55,remove,1.0,C,,CCCOC,CCOC,4,remove C from position 1,flow_matching,0.3,2.0,60,216
56,replace,2.0,1,O,CCOC,CC1C,4,replace O at position 2 with 1,flow_matching,0.3,2.0,60,216
57,replace,3.0,(,C,CC1C,CC1(,4,replace C at position 3 with (,flow_matching,0.3,2.0,60,216
58,add,1.0,S,,CC1(,CSC1(,5,add S at position 1,flow_matching,0.3,2.0,60,216
59,replace,1.0,C,S,CSC1(,CCC1(,5,replace S at position 1 with C,flow_matching,0.3,2.0,60,216
60,add,0.0,n,,CCC1(,nCCC1(,6,add n at position 0,flow_matching,0.3,2.0,60,216
61,replace,0.0,C,n,nCCC1(,CCCC1(,6,replace n at position 0 with C,flow_matching,0.3,2.0,60,216
62,remove,1.0,C,,CCCC1(,CCC1(,5,remove C from position 1,flow_matching,0.3,2.0,60,216
63,replace,1.0,1,C,CCC1(,C1C1(,5,replace C at position 1 with 1,flow_matching,0.3,2.0,60,216
64,replace,0.0,n,C,C1C1(,n1C1(,5,replace C at position 0 with n,flow_matching,0.3,2.0,60,216
65,add,3.0,c,,n1C1(,n1Cc1(,6,add c at position 3,flow_matching,0.3,2.0,60,216
66,add,4.0,B,,n1Cc1(,n1CcB1(,7,add B at position 4,flow_matching,0.3,2.0,60,216
67,replace,2.0,/,C,n1CcB1(,n1/cB1(,7,replace C at position 2 with /,flow_matching,0.3,2.0,60,216
68,add,2.0,6,,n1/cB1(,n16/cB1(,8,add 6 at position 2,flow_matching,0.3,2.0,60,216
69,add,1.0,@,,n16/cB1(,n@16/cB1(,9,add @ at position 1,flow_matching,0.3,2.0,60,216
70,add,0.0,3,,n@16/cB1(,3n@16/cB1(,10,add 3 at position 0,flow_matching,0.3,2.0,60,216
71,replace,0.0,C,3,3n@16/cB1(,Cn@16/cB1(,10,replace 3 at position 0 with C,flow_matching,0.3,2.0,60,216
72,add,6.0,o,,Cn@16/cB1(,Cn@16/ocB1(,11,add o at position 6,flow_matching,0.3,2.0,60,216
73,replace,1.0,C,n,Cn@16/ocB1(,CC@16/ocB1(,11,replace n at position 1 with C,flow_matching,0.3,2.0,60,216
74,replace,2.0,1,@,CC@16/ocB1(,CC116/ocB1(,11,replace @ at position 2 with 1,flow_matching,0.3,2.0,60,216
75,replace,3.0,(,1,CC116/ocB1(,CC1(6/ocB1(,11,replace 1 at position 3 with (,flow_matching,0.3,2.0,60,216
76,replace,3.0,-,(,CC1(6/ocB1(,CC1-6/ocB1(,11,replace ( at position 3 with -,flow_matching,0.3,2.0,60,216
77,add,6.0,I,,CC1-6/ocB1(,CC1-6/IocB1(,12,add I at position 6,flow_matching,0.3,2.0,60,216
78,remove,10.0,1,,CC1-6/IocB1(,CC1-6/IocB(,11,remove 1 from position 10,flow_matching,0.3,2.0,60,216
79,replace,3.0,(,-,CC1-6/IocB(,CC1(6/IocB(,11,replace - at position 3 with (,flow_matching,0.3,2.0,60,216
80,add,10.0,B,,CC1(6/IocB(,CC1(6/IocBB(,12,add B at position 10,flow_matching,0.3,2.0,60,216
81,remove,6.0,I,,CC1(6/IocBB(,CC1(6/ocBB(,11,remove I from position 6,flow_matching,0.3,2.0,60,216
82,remove,8.0,B,,CC1(6/ocBB(,CC1(6/ocB(,10,remove B from position 8,flow_matching,0.3,2.0,60,216
83,replace,2.0,\,1,CC1(6/ocB(,CC\(6/ocB(,10,replace 1 at position 2 with \,flow_matching,0.3,2.0,60,216
84,add,4.0,\,,CC\(6/ocB(,CC\(\6/ocB(,11,add \ at position 4,flow_matching,0.3,2.0,60,216
85,replace,2.0,1,\,CC\(\6/ocB(,CC1(\6/ocB(,11,replace \ at position 2 with 1,flow_matching,0.3,2.0,60,216
86,add,2.0,F,,CC1(\6/ocB(,CCF1(\6/ocB(,12,add F at position 2,flow_matching,0.3,2.0,60,216
87,replace,7.0,(,/,CCF1(\6/ocB(,CCF1(\6(ocB(,12,replace / at position 7 with (,flow_matching,0.3,2.0,60,216
88,replace,2.0,1,F,CCF1(\6(ocB(,CC11(\6(ocB(,12,replace F at position 2 with 1,flow_matching,0.3,2.0,60,216
89,remove,4.0,(,,CC11(\6(ocB(,CC11\6(ocB(,11,remove ( from position 4,flow_matching,0.3,2.0,60,216
90,remove,2.0,1,,CC11\6(ocB(,CC1\6(ocB(,10,remove 1 from position 2,flow_matching,0.3,2.0,60,216
91,replace,6.0,(,o,CC1\6(ocB(,CC1\6((cB(,10,replace o at position 6 with (,flow_matching,0.3,2.0,60,216
92,replace,4.0,C,6,CC1\6((cB(,CC1\C((cB(,10,replace 6 at position 4 with C,flow_matching,0.3,2.0,60,216
93,replace,3.0,(,\,CC1\C((cB(,CC1(C((cB(,10,replace \ at position 3 with (,flow_matching,0.3,2.0,60,216
94,add,6.0,B,,CC1(C((cB(,CC1(C(B(cB(,11,add B at position 6,flow_matching,0.3,2.0,60,216
95,add,6.0,n,,CC1(C(B(cB(,CC1(C(nB(cB(,12,add n at position 6,flow_matching,0.3,2.0,60,216
96,replace,5.0,),(,CC1(C(nB(cB(,CC1(C)nB(cB(,12,replace ( at position 5 with ),flow_matching,0.3,2.0,60,216
97,replace,10.0,/,B,CC1(C)nB(cB(,CC1(C)nB(c/(,12,replace B at position 10 with /,flow_matching,0.3,2.0,60,216
98,replace,0.0,@,C,CC1(C)nB(c/(,@C1(C)nB(c/(,12,replace C at position 0 with @,flow_matching,0.3,2.0,60,216
99,add,2.0,1,,@C1(C)nB(c/(,@C11(C)nB(c/(,13,add 1 at position 2,flow_matching,0.3,2.0,60,216
100,add,2.0,4,,@C11(C)nB(c/(,@C411(C)nB(c/(,14,add 4 at position 2,flow_matching,0.3,2.0,60,216
101,replace,0.0,C,@,@C411(C)nB(c/(,CC411(C)nB(c/(,14,replace @ at position 0 with C,flow_matching,0.3,2.0,60,216
102,add,13.0,\,,CC411(C)nB(c/(,CC411(C)nB(c/\(,15,add \ at position 13,flow_matching,0.3,2.0,60,216
103,remove,12.0,/,,CC411(C)nB(c/\(,CC411(C)nB(c\(,14,remove / from position 12,flow_matching,0.3,2.0,60,216
104,add,0.0,(,,CC411(C)nB(c\(,(CC411(C)nB(c\(,15,add ( at position 0,flow_matching,0.3,2.0,60,216
105,replace,14.0,H,(,(CC411(C)nB(c\(,(CC411(C)nB(c\H,15,replace ( at position 14 with H,flow_matching,0.3,2.0,60,216
106,add,8.0,H,,(CC411(C)nB(c\H,(CC411(CH)nB(c\H,16,add H at position 8,flow_matching,0.3,2.0,60,216
107,replace,14.0,[,\,(CC411(CH)nB(c\H,(CC411(CH)nB(c[H,16,replace \ at position 14 with [,flow_matching,0.3,2.0,60,216
108,replace,4.0,H,1,(CC411(CH)nB(c[H,(CC4H1(CH)nB(c[H,16,replace 1 at position 4 with H,flow_matching,0.3,2.0,60,216
109,replace,0.0,C,(,(CC4H1(CH)nB(c[H,CCC4H1(CH)nB(c[H,16,replace ( at position 0 with C,flow_matching,0.3,2.0,60,216
110,remove,6.0,(,,CCC4H1(CH)nB(c[H,CCC4H1CH)nB(c[H,15,remove ( from position 6,flow_matching,0.3,2.0,60,216
111,replace,3.0,\,4,CCC4H1CH)nB(c[H,CCC\H1CH)nB(c[H,15,replace 4 at position 3 with \,flow_matching,0.3,2.0,60,216
112,replace,12.0,C,c,CCC\H1CH)nB(c[H,CCC\H1CH)nB(C[H,15,replace c at position 12 with C,flow_matching,0.3,2.0,60,216
113,remove,11.0,(,,CCC\H1CH)nB(C[H,CCC\H1CH)nBC[H,14,remove ( from position 11,flow_matching,0.3,2.0,60,216
114,add,6.0,C,,CCC\H1CH)nBC[H,CCC\H1CCH)nBC[H,15,add C at position 6,flow_matching,0.3,2.0,60,216
115,remove,13.0,[,,CCC\H1CCH)nBC[H,CCC\H1CCH)nBCH,14,remove [ from position 13,flow_matching,0.3,2.0,60,216
116,replace,2.0,1,C,CCC\H1CCH)nBCH,CC1\H1CCH)nBCH,14,replace C at position 2 with 1,flow_matching,0.3,2.0,60,216
117,replace,3.0,(,\,CC1\H1CCH)nBCH,CC1(H1CCH)nBCH,14,replace \ at position 3 with (,flow_matching,0.3,2.0,60,216
118,add,13.0,@,,CC1(H1CCH)nBCH,CC1(H1CCH)nBC@H,15,add @ at position 13,flow_matching,0.3,2.0,60,216
119,add,3.0,5,,CC1(H1CCH)nBC@H,CC15(H1CCH)nBC@H,16,add 5 at position 3,flow_matching,0.3,2.0,60,216
120,remove,12.0,B,,CC15(H1CCH)nBC@H,CC15(H1CCH)nC@H,15,remove B from position 12,flow_matching,0.3,2.0,60,216
121,replace,3.0,(,5,CC15(H1CCH)nC@H,CC1((H1CCH)nC@H,15,replace 5 at position 3 with (,flow_matching,0.3,2.0,60,216
122,replace,4.0,C,(,CC1((H1CCH)nC@H,CC1(CH1CCH)nC@H,15,replace ( at position 4 with C,flow_matching,0.3,2.0,60,216
123,replace,5.0,),H,CC1(CH1CCH)nC@H,CC1(C)1CCH)nC@H,15,replace H at position 5 with ),flow_matching,0.3,2.0,60,216
124,replace,8.0,H,C,CC1(C)1CCH)nC@H,CC1(C)1CHH)nC@H,15,replace C at position 8 with H,flow_matching,0.3,2.0,60,216
125,replace,6.0,[,1,CC1(C)1CHH)nC@H,CC1(C)[CHH)nC@H,15,replace 1 at position 6 with [,flow_matching,0.3,2.0,60,216
126,replace,8.0,@,H,CC1(C)[CHH)nC@H,CC1(C)[C@H)nC@H,15,replace H at position 8 with @,flow_matching,0.3,2.0,60,216
127,replace,9.0,@,H,CC1(C)[C@H)nC@H,CC1(C)[C@@)nC@H,15,replace H at position 9 with @,flow_matching,0.3,2.0,60,216
128,replace,14.0,l,H,CC1(C)[C@@)nC@H,CC1(C)[C@@)nC@l,15,replace H at position 14 with l,flow_matching,0.3,2.0,60,216
129,replace,10.0,H,),CC1(C)[C@@)nC@l,CC1(C)[C@@HnC@l,15,replace ) at position 10 with H,flow_matching,0.3,2.0,60,216
130,replace,11.0,],n,CC1(C)[C@@HnC@l,CC1(C)[C@@H]C@l,15,replace n at position 11 with ],flow_matching,0.3,2.0,60,216
131,add,11.0,r,,CC1(C)[C@@H]C@l,CC1(C)[C@@Hr]C@l,16,add r at position 11,flow_matching,0.3,2.0,60,216
132,replace,11.0,],r,CC1(C)[C@@Hr]C@l,CC1(C)[C@@H]]C@l,16,replace r at position 11 with ],flow_matching,0.3,2.0,60,216
133,replace,12.0,2,],CC1(C)[C@@H]]C@l,CC1(C)[C@@H]2C@l,16,replace ] at position 12 with 2,flow_matching,0.3,2.0,60,216
134,add,3.0,7,,CC1(C)[C@@H]2C@l,CC17(C)[C@@H]2C@l,17,add 7 at position 3,flow_matching,0.3,2.0,60,216
135,replace,13.0,=,2,CC17(C)[C@@H]2C@l,CC17(C)[C@@H]=C@l,17,replace 2 at position 13 with =,flow_matching,0.3,2.0,60,216
136,replace,3.0,(,7,CC17(C)[C@@H]=C@l,CC1((C)[C@@H]=C@l,17,replace 7 at position 3 with (,flow_matching,0.3,2.0,60,216
137,add,5.0,(,,CC1((C)[C@@H]=C@l,CC1(((C)[C@@H]=C@l,18,add ( at position 5,flow_matching,0.3,2.0,60,216
138,add,9.0,H,,CC1(((C)[C@@H]=C@l,CC1(((C)[HC@@H]=C@l,19,add H at position 9,flow_matching,0.3,2.0,60,216
139,replace,4.0,C,(,CC1(((C)[HC@@H]=C@l,CC1(C(C)[HC@@H]=C@l,19,replace ( at position 4 with C,flow_matching,0.3,2.0,60,216
140,add,18.0,@,,CC1(C(C)[HC@@H]=C@l,CC1(C(C)[HC@@H]=C@@l,20,add @ at position 18,flow_matching,0.3,2.0,60,216
141,remove,14.0,],,CC1(C(C)[HC@@H]=C@@l,CC1(C(C)[HC@@H=C@@l,19,remove ] from position 14,flow_matching,0.3,2.0,60,216
142,replace,5.0,),(,CC1(C(C)[HC@@H=C@@l,CC1(C)C)[HC@@H=C@@l,19,replace ( at position 5 with ),flow_matching,0.3,2.0,60,216
143,replace,6.0,[,C,CC1(C)C)[HC@@H=C@@l,CC1(C)[)[HC@@H=C@@l,19,replace C at position 6 with [,flow_matching,0.3,2.0,60,216
144,add,13.0,n,,CC1(C)[)[HC@@H=C@@l,CC1(C)[)[HC@@nH=C@@l,20,add n at position 13,flow_matching,0.3,2.0,60,216
145,add,2.0,=,,CC1(C)[)[HC@@nH=C@@l,CC=1(C)[)[HC@@nH=C@@l,21,add = at position 2,flow_matching,0.3,2.0,60,216
146,remove,16.0,=,,CC=1(C)[)[HC@@nH=C@@l,CC=1(C)[)[HC@@nHC@@l,20,remove = from position 16,flow_matching,0.3,2.0,60,216
147,replace,10.0,+,H,CC=1(C)[)[HC@@nHC@@l,CC=1(C)[)[+C@@nHC@@l,20,replace H at position 10 with +,flow_matching,0.3,2.0,60,216
148,replace,2.0,1,=,CC=1(C)[)[+C@@nHC@@l,CC11(C)[)[+C@@nHC@@l,20,replace = at position 2 with 1,flow_matching,0.3,2.0,60,216
149,add,15.0,+,,CC11(C)[)[+C@@nHC@@l,CC11(C)[)[+C@@n+HC@@l,21,add + at position 15,flow_matching,0.3,2.0,60,216
150,replace,3.0,(,1,CC11(C)[)[+C@@n+HC@@l,CC1((C)[)[+C@@n+HC@@l,21,replace 1 at position 3 with (,flow_matching,0.3,2.0,60,216
151,replace,3.0,5,(,CC1((C)[)[+C@@n+HC@@l,CC15(C)[)[+C@@n+HC@@l,21,replace ( at position 3 with 5,flow_matching,0.3,2.0,60,216
152,replace,3.0,(,5,CC15(C)[)[+C@@n+HC@@l,CC1((C)[)[+C@@n+HC@@l,21,replace 5 at position 3 with (,flow_matching,0.3,2.0,60,216
153,remove,1.0,C,,CC1((C)[)[+C@@n+HC@@l,C1((C)[)[+C@@n+HC@@l,20,remove C from position 1,flow_matching,0.3,2.0,60,216
154,replace,1.0,C,1,C1((C)[)[+C@@n+HC@@l,CC((C)[)[+C@@n+HC@@l,20,replace 1 at position 1 with C,flow_matching,0.3,2.0,60,216
155,add,12.0,),,CC((C)[)[+C@@n+HC@@l,CC((C)[)[+C@)@n+HC@@l,21,add ) at position 12,flow_matching,0.3,2.0,60,216
156,add,14.0,),,CC((C)[)[+C@)@n+HC@@l,CC((C)[)[+C@)@)n+HC@@l,22,add ) at position 14,flow_matching,0.3,2.0,60,216
157,add,11.0,\,,CC((C)[)[+C@)@)n+HC@@l,CC((C)[)[+C\@)@)n+HC@@l,23,add \ at position 11,flow_matching,0.3,2.0,60,216
158,replace,8.0,r,[,CC((C)[)[+C\@)@)n+HC@@l,CC((C)[)r+C\@)@)n+HC@@l,23,replace [ at position 8 with r,flow_matching,0.3,2.0,60,216
159,remove,14.0,@,,CC((C)[)r+C\@)@)n+HC@@l,CC((C)[)r+C\@))n+HC@@l,22,remove @ from position 14,flow_matching,0.3,2.0,60,216
160,add,6.0,l,,CC((C)[)r+C\@))n+HC@@l,CC((C)l[)r+C\@))n+HC@@l,23,add l at position 6,flow_matching,0.3,2.0,60,216
161,remove,11.0,C,,CC((C)l[)r+C\@))n+HC@@l,CC((C)l[)r+\@))n+HC@@l,22,remove C from position 11,flow_matching,0.3,2.0,60,216
162,replace,2.0,1,(,CC((C)l[)r+\@))n+HC@@l,CC1(C)l[)r+\@))n+HC@@l,22,replace ( at position 2 with 1,flow_matching,0.3,2.0,60,216
163,replace,6.0,[,l,CC1(C)l[)r+\@))n+HC@@l,CC1(C)[[)r+\@))n+HC@@l,22,replace l at position 6 with [,flow_matching,0.3,2.0,60,216
164,replace,7.0,C,[,CC1(C)[[)r+\@))n+HC@@l,CC1(C)[C)r+\@))n+HC@@l,22,replace [ at position 7 with C,flow_matching,0.3,2.0,60,216
165,replace,8.0,@,),CC1(C)[C)r+\@))n+HC@@l,CC1(C)[C@r+\@))n+HC@@l,22,replace ) at position 8 with @,flow_matching,0.3,2.0,60,216
166,replace,9.0,@,r,CC1(C)[C@r+\@))n+HC@@l,CC1(C)[C@@+\@))n+HC@@l,22,replace r at position 9 with @,flow_matching,0.3,2.0,60,216
167,replace,10.0,H,+,CC1(C)[C@@+\@))n+HC@@l,CC1(C)[C@@H\@))n+HC@@l,22,replace + at position 10 with H,flow_matching,0.3,2.0,60,216
168,replace,11.0,],\,CC1(C)[C@@H\@))n+HC@@l,CC1(C)[C@@H]@))n+HC@@l,22,replace \ at position 11 with ],flow_matching,0.3,2.0,60,216
169,replace,12.0,2,@,CC1(C)[C@@H]@))n+HC@@l,CC1(C)[C@@H]2))n+HC@@l,22,replace @ at position 12 with 2,flow_matching,0.3,2.0,60,216
170,replace,13.0,C,),CC1(C)[C@@H]2))n+HC@@l,CC1(C)[C@@H]2C)n+HC@@l,22,replace ) at position 13 with C,flow_matching,0.3,2.0,60,216
171,replace,14.0,C,),CC1(C)[C@@H]2C)n+HC@@l,CC1(C)[C@@H]2CCn+HC@@l,22,replace ) at position 14 with C,flow_matching,0.3,2.0,60,216
172,replace,15.0,[,n,CC1(C)[C@@H]2CCn+HC@@l,CC1(C)[C@@H]2CC[+HC@@l,22,replace n at position 15 with [,flow_matching,0.3,2.0,60,216
173,replace,16.0,C,+,CC1(C)[C@@H]2CC[+HC@@l,CC1(C)[C@@H]2CC[CHC@@l,22,replace + at position 16 with C,flow_matching,0.3,2.0,60,216
174,replace,17.0,@,H,CC1(C)[C@@H]2CC[CHC@@l,CC1(C)[C@@H]2CC[C@C@@l,22,replace H at position 17 with @,flow_matching,0.3,2.0,60,216
175,replace,18.0,@,C,CC1(C)[C@@H]2CC[C@C@@l,CC1(C)[C@@H]2CC[C@@@@l,22,replace C at position 18 with @,flow_matching,0.3,2.0,60,216
176,replace,19.0,],@,CC1(C)[C@@H]2CC[C@@@@l,CC1(C)[C@@H]2CC[C@@]@l,22,replace @ at position 19 with ],flow_matching,0.3,2.0,60,216
177,replace,20.0,1,@,CC1(C)[C@@H]2CC[C@@]@l,CC1(C)[C@@H]2CC[C@@]1l,22,replace @ at position 20 with 1,flow_matching,0.3,2.0,60,216
178,replace,21.0,(,l,CC1(C)[C@@H]2CC[C@@]1l,CC1(C)[C@@H]2CC[C@@]1(,22,replace l at position 21 with (,flow_matching,0.3,2.0,60,216
179,add,22.0,C,,CC1(C)[C@@H]2CC[C@@]1(,CC1(C)[C@@H]2CC[C@@]1(C,23,add C at position 22,flow_matching,0.3,2.0,60,216
180,add,23.0,),,CC1(C)[C@@H]2CC[C@@]1(C,CC1(C)[C@@H]2CC[C@@]1(C),24,add ) at position 23,flow_matching,0.3,2.0,60,216
181,add,24.0,[,,CC1(C)[C@@H]2CC[C@@]1(C),CC1(C)[C@@H]2CC[C@@]1(C)[,25,add [ at position 24,flow_matching,0.3,2.0,60,216
182,add,25.0,C,,CC1(C)[C@@H]2CC[C@@]1(C)[,CC1(C)[C@@H]2CC[C@@]1(C)[C,26,add C at position 25,flow_matching,0.3,2.0,60,216
183,add,26.0,@,,CC1(C)[C@@H]2CC[C@@]1(C)[C,CC1(C)[C@@H]2CC[C@@]1(C)[C@,27,add @ at position 26,flow_matching,0.3,2.0,60,216
184,add,27.0,H,,CC1(C)[C@@H]2CC[C@@]1(C)[C@,CC1(C)[C@@H]2CC[C@@]1(C)[C@H,28,add H at position 27,flow_matching,0.3,2.0,60,216
185,add,28.0,],,CC1(C)[C@@H]2CC[C@@]1(C)[C@H,CC1(C)[C@@H]2CC[C@@]1(C)[C@H],29,add ] at position 28,flow_matching,0.3,2.0,60,216
186,add,29.0,(,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H],CC1(C)[C@@H]2CC[C@@]1(C)[C@H](,30,add ( at position 29,flow_matching,0.3,2.0,60,216
187,add,30.0,N,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](N,31,add N at position 30,flow_matching,0.3,2.0,60,216
188,add,31.0,C,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](N,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC,32,add C at position 31,flow_matching,0.3,2.0,60,216
189,add,32.0,(,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(,33,add ( at position 32,flow_matching,0.3,2.0,60,216
190,add,33.0,=,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=,34,add = at position 33,flow_matching,0.3,2.0,60,216
191,add,34.0,O,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O,35,add O at position 34,flow_matching,0.3,2.0,60,216
192,add,35.0,),,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O),36,add ) at position 35,flow_matching,0.3,2.0,60,216
193,add,36.0,C,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O),CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)C,37,add C at position 36,flow_matching,0.3,2.0,60,216
194,add,37.0,O,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)C,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)CO,38,add O at position 37,flow_matching,0.3,2.0,60,216
195,add,38.0,c,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)CO,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc,39,add c at position 38,flow_matching,0.3,2.0,60,216
196,add,39.0,1,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1,40,add 1 at position 39,flow_matching,0.3,2.0,60,216
197,add,40.0,c,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1c,41,add c at position 40,flow_matching,0.3,2.0,60,216
198,add,41.0,c,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1c,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1cc,42,add c at position 41,flow_matching,0.3,2.0,60,216
199,add,42.0,c,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1cc,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc,43,add c at position 42,flow_matching,0.3,2.0,60,216
200,add,43.0,(,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(,44,add ( at position 43,flow_matching,0.3,2.0,60,216
201,add,44.0,C,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C,45,add C at position 44,flow_matching,0.3,2.0,60,216
202,add,45.0,3,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3,46,add 3 at position 45,flow_matching,0.3,2.0,60,216
203,add,46.0,S,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3S,47,add S at position 46,flow_matching,0.3,2.0,60,216
204,add,47.0,C,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3S,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SC,48,add C at position 47,flow_matching,0.3,2.0,60,216
205,add,48.0,C,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SC,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCC,49,add C at position 48,flow_matching,0.3,2.0,60,216
206,add,49.0,C,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCC,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCC,50,add C at position 49,flow_matching,0.3,2.0,60,216
207,add,50.0,S,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCC,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS,51,add S at position 50,flow_matching,0.3,2.0,60,216
208,add,51.0,3,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3,52,add 3 at position 51,flow_matching,0.3,2.0,60,216
209,add,52.0,),,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3),53,add ) at position 52,flow_matching,0.3,2.0,60,216
210,add,53.0,c,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3),CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)c,54,add c at position 53,flow_matching,0.3,2.0,60,216
211,add,54.0,c,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)c,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc,55,add c at position 54,flow_matching,0.3,2.0,60,216
212,add,55.0,1,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc1,56,add 1 at position 55,flow_matching,0.3,2.0,60,216
213,add,56.0,),,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc1,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc1),57,add ) at position 56,flow_matching,0.3,2.0,60,216
214,add,57.0,C,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc1),CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc1)C,58,add C at position 57,flow_matching,0.3,2.0,60,216
215,add,58.0,2,,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc1)C,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc1)C2,59,add 2 at position 58,flow_matching,0.3,2.0,60,216
216,add,59.0,"
",,CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc1)C2,"CC1(C)[C@@H]2CC[C@@]1(C)[C@H](NC(=O)COc1ccc(C3SCCCS3)cc1)C2
",60,"add 
 at position 59",flow_matching,0.3,2.0,60,216
