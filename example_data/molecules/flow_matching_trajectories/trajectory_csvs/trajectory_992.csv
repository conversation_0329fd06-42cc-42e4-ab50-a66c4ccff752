step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,50,113
1,add,0.0,(,,,(,1,add ( at position 0,flow_matching,0.3,2.0,50,113
2,replace,0.0,C,(,(,C,1,replace ( at position 0 with C,flow_matching,0.3,2.0,50,113
3,add,0.0,N,,C,NC,2,add N at position 0,flow_matching,0.3,2.0,50,113
4,replace,0.0,],N,NC,]C,2,replace N at position 0 with ],flow_matching,0.3,2.0,50,113
5,add,1.0,N,,]C,]NC,3,add N at position 1,flow_matching,0.3,2.0,50,113
6,replace,2.0,H,<PERSON>,]NC,]NH,3,replace <PERSON> at position 2 with H,flow_matching,0.3,2.0,50,113
7,replace,0.0,C,],]NH,CNH,3,replace ] at position 0 with C,flow_matching,0.3,2.0,50,113
8,add,2.0,n,,CNH,CNnH,4,add n at position 2,flow_matching,0.3,2.0,50,113
9,add,1.0,3,,CNnH,C3NnH,5,add 3 at position 1,flow_matching,0.3,2.0,50,113
10,add,3.0,1,,C3NnH,C3N1nH,6,add 1 at position 3,flow_matching,0.3,2.0,50,113
11,remove,0.0,C,,C3N1nH,3N1nH,5,remove C from position 0,flow_matching,0.3,2.0,50,113
12,replace,1.0,B,N,3N1nH,3B1nH,5,replace N at position 1 with B,flow_matching,0.3,2.0,50,113
13,replace,1.0,o,B,3B1nH,3o1nH,5,replace B at position 1 with o,flow_matching,0.3,2.0,50,113
14,replace,4.0,5,H,3o1nH,3o1n5,5,replace H at position 4 with 5,flow_matching,0.3,2.0,50,113
15,replace,0.0,C,3,3o1n5,Co1n5,5,replace 3 at position 0 with C,flow_matching,0.3,2.0,50,113
16,replace,1.0,N,o,Co1n5,CN1n5,5,replace o at position 1 with N,flow_matching,0.3,2.0,50,113
17,remove,3.0,n,,CN1n5,CN15,4,remove n from position 3,flow_matching,0.3,2.0,50,113
18,add,0.0,O,,CN15,OCN15,5,add O at position 0,flow_matching,0.3,2.0,50,113
19,remove,4.0,5,,OCN15,OCN1,4,remove 5 from position 4,flow_matching,0.3,2.0,50,113
20,add,2.0,C,,OCN1,OCCN1,5,add C at position 2,flow_matching,0.3,2.0,50,113
21,remove,0.0,O,,OCCN1,CCN1,4,remove O from position 0,flow_matching,0.3,2.0,50,113
22,add,4.0,-,,CCN1,CCN1-,5,add - at position 4,flow_matching,0.3,2.0,50,113
23,remove,3.0,1,,CCN1-,CCN-,4,remove 1 from position 3,flow_matching,0.3,2.0,50,113
24,remove,3.0,-,,CCN-,CCN,3,remove - from position 3,flow_matching,0.3,2.0,50,113
25,remove,2.0,N,,CCN,CC,2,remove N from position 2,flow_matching,0.3,2.0,50,113
26,add,1.0,F,,CC,CFC,3,add F at position 1,flow_matching,0.3,2.0,50,113
27,add,1.0,s,,CFC,CsFC,4,add s at position 1,flow_matching,0.3,2.0,50,113
28,remove,1.0,s,,CsFC,CFC,3,remove s from position 1,flow_matching,0.3,2.0,50,113
29,replace,0.0,1,C,CFC,1FC,3,replace C at position 0 with 1,flow_matching,0.3,2.0,50,113
30,remove,1.0,F,,1FC,1C,2,remove F from position 1,flow_matching,0.3,2.0,50,113
31,remove,1.0,C,,1C,1,1,remove C from position 1,flow_matching,0.3,2.0,50,113
32,add,1.0,\,,1,1\,2,add \ at position 1,flow_matching,0.3,2.0,50,113
33,add,1.0,3,,1\,13\,3,add 3 at position 1,flow_matching,0.3,2.0,50,113
34,add,2.0,F,,13\,13F\,4,add F at position 2,flow_matching,0.3,2.0,50,113
35,add,3.0,5,,13F\,13F5\,5,add 5 at position 3,flow_matching,0.3,2.0,50,113
36,remove,0.0,1,,13F5\,3F5\,4,remove 1 from position 0,flow_matching,0.3,2.0,50,113
37,replace,0.0,/,3,3F5\,/F5\,4,replace 3 at position 0 with /,flow_matching,0.3,2.0,50,113
38,replace,0.0,C,/,/F5\,CF5\,4,replace / at position 0 with C,flow_matching,0.3,2.0,50,113
39,add,4.0,l,,CF5\,CF5\l,5,add l at position 4,flow_matching,0.3,2.0,50,113
40,replace,2.0,7,5,CF5\l,CF7\l,5,replace 5 at position 2 with 7,flow_matching,0.3,2.0,50,113
41,remove,3.0,\,,CF7\l,CF7l,4,remove \ from position 3,flow_matching,0.3,2.0,50,113
42,replace,1.0,[,F,CF7l,C[7l,4,replace F at position 1 with [,flow_matching,0.3,2.0,50,113
43,add,0.0,B,,C[7l,BC[7l,5,add B at position 0,flow_matching,0.3,2.0,50,113
44,add,1.0,C,,BC[7l,BCC[7l,6,add C at position 1,flow_matching,0.3,2.0,50,113
45,add,4.0,/,,BCC[7l,BCC[/7l,7,add / at position 4,flow_matching,0.3,2.0,50,113
46,add,0.0,7,,BCC[/7l,7BCC[/7l,8,add 7 at position 0,flow_matching,0.3,2.0,50,113
47,replace,0.0,C,7,7BCC[/7l,CBCC[/7l,8,replace 7 at position 0 with C,flow_matching,0.3,2.0,50,113
48,replace,2.0,@,C,CBCC[/7l,CB@C[/7l,8,replace C at position 2 with @,flow_matching,0.3,2.0,50,113
49,add,3.0,o,,CB@C[/7l,CB@oC[/7l,9,add o at position 3,flow_matching,0.3,2.0,50,113
50,replace,1.0,[,B,CB@oC[/7l,C[@oC[/7l,9,replace B at position 1 with [,flow_matching,0.3,2.0,50,113
51,replace,2.0,C,@,C[@oC[/7l,C[CoC[/7l,9,replace @ at position 2 with C,flow_matching,0.3,2.0,50,113
52,replace,7.0,\,7,C[CoC[/7l,C[CoC[/\l,9,replace 7 at position 7 with \,flow_matching,0.3,2.0,50,113
53,remove,5.0,[,,C[CoC[/\l,C[CoC/\l,8,remove [ from position 5,flow_matching,0.3,2.0,50,113
54,add,8.0,#,,C[CoC/\l,C[CoC/\l#,9,add # at position 8,flow_matching,0.3,2.0,50,113
55,replace,0.0,-,C,C[CoC/\l#,-[CoC/\l#,9,replace C at position 0 with -,flow_matching,0.3,2.0,50,113
56,replace,0.0,C,-,-[CoC/\l#,C[CoC/\l#,9,replace - at position 0 with C,flow_matching,0.3,2.0,50,113
57,add,7.0,=,,C[CoC/\l#,C[CoC/\=l#,10,add = at position 7,flow_matching,0.3,2.0,50,113
58,remove,6.0,\,,C[CoC/\=l#,C[CoC/=l#,9,remove \ from position 6,flow_matching,0.3,2.0,50,113
59,replace,2.0,(,C,C[CoC/=l#,C[(oC/=l#,9,replace C at position 2 with (,flow_matching,0.3,2.0,50,113
60,replace,8.0,n,#,C[(oC/=l#,C[(oC/=ln,9,replace # at position 8 with n,flow_matching,0.3,2.0,50,113
61,add,9.0,[,,C[(oC/=ln,C[(oC/=ln[,10,add [ at position 9,flow_matching,0.3,2.0,50,113
62,add,9.0,7,,C[(oC/=ln[,C[(oC/=ln7[,11,add 7 at position 9,flow_matching,0.3,2.0,50,113
63,replace,2.0,C,(,C[(oC/=ln7[,C[CoC/=ln7[,11,replace ( at position 2 with C,flow_matching,0.3,2.0,50,113
64,remove,6.0,=,,C[CoC/=ln7[,C[CoC/ln7[,10,remove = from position 6,flow_matching,0.3,2.0,50,113
65,add,1.0,[,,C[CoC/ln7[,C[[CoC/ln7[,11,add [ at position 1,flow_matching,0.3,2.0,50,113
66,replace,2.0,C,[,C[[CoC/ln7[,C[CCoC/ln7[,11,replace [ at position 2 with C,flow_matching,0.3,2.0,50,113
67,replace,3.0,@,C,C[CCoC/ln7[,C[C@oC/ln7[,11,replace C at position 3 with @,flow_matching,0.3,2.0,50,113
68,replace,4.0,@,o,C[C@oC/ln7[,C[C@@C/ln7[,11,replace o at position 4 with @,flow_matching,0.3,2.0,50,113
69,replace,5.0,H,C,C[C@@C/ln7[,C[C@@H/ln7[,11,replace C at position 5 with H,flow_matching,0.3,2.0,50,113
70,replace,6.0,],/,C[C@@H/ln7[,C[C@@H]ln7[,11,replace / at position 6 with ],flow_matching,0.3,2.0,50,113
71,replace,7.0,(,l,C[C@@H]ln7[,C[C@@H](n7[,11,replace l at position 7 with (,flow_matching,0.3,2.0,50,113
72,replace,8.0,c,n,C[C@@H](n7[,C[C@@H](c7[,11,replace n at position 8 with c,flow_matching,0.3,2.0,50,113
73,replace,9.0,1,7,C[C@@H](c7[,C[C@@H](c1[,11,replace 7 at position 9 with 1,flow_matching,0.3,2.0,50,113
74,replace,10.0,c,[,C[C@@H](c1[,C[C@@H](c1c,11,replace [ at position 10 with c,flow_matching,0.3,2.0,50,113
75,add,11.0,c,,C[C@@H](c1c,C[C@@H](c1cc,12,add c at position 11,flow_matching,0.3,2.0,50,113
76,add,12.0,c,,C[C@@H](c1cc,C[C@@H](c1ccc,13,add c at position 12,flow_matching,0.3,2.0,50,113
77,add,13.0,(,,C[C@@H](c1ccc,C[C@@H](c1ccc(,14,add ( at position 13,flow_matching,0.3,2.0,50,113
78,add,14.0,C,,C[C@@H](c1ccc(,C[C@@H](c1ccc(C,15,add C at position 14,flow_matching,0.3,2.0,50,113
79,add,15.0,l,,C[C@@H](c1ccc(C,C[C@@H](c1ccc(Cl,16,add l at position 15,flow_matching,0.3,2.0,50,113
80,add,16.0,),,C[C@@H](c1ccc(Cl,C[C@@H](c1ccc(Cl),17,add ) at position 16,flow_matching,0.3,2.0,50,113
81,add,17.0,c,,C[C@@H](c1ccc(Cl),C[C@@H](c1ccc(Cl)c,18,add c at position 17,flow_matching,0.3,2.0,50,113
82,add,18.0,c,,C[C@@H](c1ccc(Cl)c,C[C@@H](c1ccc(Cl)cc,19,add c at position 18,flow_matching,0.3,2.0,50,113
83,add,19.0,1,,C[C@@H](c1ccc(Cl)cc,C[C@@H](c1ccc(Cl)cc1,20,add 1 at position 19,flow_matching,0.3,2.0,50,113
84,add,20.0,C,,C[C@@H](c1ccc(Cl)cc1,C[C@@H](c1ccc(Cl)cc1C,21,add C at position 20,flow_matching,0.3,2.0,50,113
85,add,21.0,l,,C[C@@H](c1ccc(Cl)cc1C,C[C@@H](c1ccc(Cl)cc1Cl,22,add l at position 21,flow_matching,0.3,2.0,50,113
86,add,22.0,),,C[C@@H](c1ccc(Cl)cc1Cl,C[C@@H](c1ccc(Cl)cc1Cl),23,add ) at position 22,flow_matching,0.3,2.0,50,113
87,add,23.0,N,,C[C@@H](c1ccc(Cl)cc1Cl),C[C@@H](c1ccc(Cl)cc1Cl)N,24,add N at position 23,flow_matching,0.3,2.0,50,113
88,add,24.0,(,,C[C@@H](c1ccc(Cl)cc1Cl)N,C[C@@H](c1ccc(Cl)cc1Cl)N(,25,add ( at position 24,flow_matching,0.3,2.0,50,113
89,add,25.0,C,,C[C@@H](c1ccc(Cl)cc1Cl)N(,C[C@@H](c1ccc(Cl)cc1Cl)N(C,26,add C at position 25,flow_matching,0.3,2.0,50,113
90,add,26.0,),,C[C@@H](c1ccc(Cl)cc1Cl)N(C,C[C@@H](c1ccc(Cl)cc1Cl)N(C),27,add ) at position 26,flow_matching,0.3,2.0,50,113
91,add,27.0,C,,C[C@@H](c1ccc(Cl)cc1Cl)N(C),C[C@@H](c1ccc(Cl)cc1Cl)N(C)C,28,add C at position 27,flow_matching,0.3,2.0,50,113
92,add,28.0,(,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(,29,add ( at position 28,flow_matching,0.3,2.0,50,113
93,add,29.0,=,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=,30,add = at position 29,flow_matching,0.3,2.0,50,113
94,add,30.0,O,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O,31,add O at position 30,flow_matching,0.3,2.0,50,113
95,add,31.0,),,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O),32,add ) at position 31,flow_matching,0.3,2.0,50,113
96,add,32.0,c,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O),C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c,33,add c at position 32,flow_matching,0.3,2.0,50,113
97,add,33.0,1,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1,34,add 1 at position 33,flow_matching,0.3,2.0,50,113
98,add,34.0,c,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1c,35,add c at position 34,flow_matching,0.3,2.0,50,113
99,add,35.0,c,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1c,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1cc,36,add c at position 35,flow_matching,0.3,2.0,50,113
100,add,36.0,c,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1cc,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc,37,add c at position 36,flow_matching,0.3,2.0,50,113
101,add,37.0,(,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(,38,add ( at position 37,flow_matching,0.3,2.0,50,113
102,add,38.0,N,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(N,39,add N at position 38,flow_matching,0.3,2.0,50,113
103,add,39.0,C,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(N,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC,40,add C at position 39,flow_matching,0.3,2.0,50,113
104,add,40.0,(,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(,41,add ( at position 40,flow_matching,0.3,2.0,50,113
105,add,41.0,N,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N,42,add N at position 41,flow_matching,0.3,2.0,50,113
106,add,42.0,),,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N),43,add ) at position 42,flow_matching,0.3,2.0,50,113
107,add,43.0,=,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N),C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=,44,add = at position 43,flow_matching,0.3,2.0,50,113
108,add,44.0,O,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O,45,add O at position 44,flow_matching,0.3,2.0,50,113
109,add,45.0,),,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O),46,add ) at position 45,flow_matching,0.3,2.0,50,113
110,add,46.0,c,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O),C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O)c,47,add c at position 46,flow_matching,0.3,2.0,50,113
111,add,47.0,c,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O)c,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O)cc,48,add c at position 47,flow_matching,0.3,2.0,50,113
112,add,48.0,1,,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O)cc,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O)cc1,49,add 1 at position 48,flow_matching,0.3,2.0,50,113
113,add,49.0,"
",,C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O)cc1,"C[C@@H](c1ccc(Cl)cc1Cl)N(C)C(=O)c1ccc(NC(N)=O)cc1
",50,"add 
 at position 49",flow_matching,0.3,2.0,50,113
