step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,213
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,49,213
2,replace,0.0,s,O,O,s,1,replace <PERSON> at position 0 with s,flow_matching,0.3,2.0,49,213
3,replace,0.0,C,s,s,C,1,replace s at position 0 with C,flow_matching,0.3,2.0,49,213
4,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,49,213
5,add,2.0,C,,CO,COC,3,add C at position 2,flow_matching,0.3,2.0,49,213
6,replace,2.0,r,<PERSON>,<PERSON><PERSON>,<PERSON>r,3,replace <PERSON> at position 2 with r,flow_matching,0.3,2.0,49,213
7,remove,0.0,<PERSON>,,<PERSON>r,Or,2,remove <PERSON> from position 0,flow_matching,0.3,2.0,49,213
8,replace,0.0,[,O,Or,[r,2,replace O at position 0 with [,flow_matching,0.3,2.0,49,213
9,remove,0.0,[,,[r,r,1,remove [ from position 0,flow_matching,0.3,2.0,49,213
10,add,0.0,=,,r,=r,2,add = at position 0,flow_matching,0.3,2.0,49,213
11,replace,0.0,C,=,=r,Cr,2,replace = at position 0 with C,flow_matching,0.3,2.0,49,213
12,remove,0.0,C,,Cr,r,1,remove C from position 0,flow_matching,0.3,2.0,49,213
13,replace,0.0,C,r,r,C,1,replace r at position 0 with C,flow_matching,0.3,2.0,49,213
14,replace,0.0,c,C,C,c,1,replace C at position 0 with c,flow_matching,0.3,2.0,49,213
15,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,49,213
16,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,49,213
17,add,0.0,[,,,[,1,add [ at position 0,flow_matching,0.3,2.0,49,213
18,replace,0.0,S,[,[,S,1,replace [ at position 0 with S,flow_matching,0.3,2.0,49,213
19,replace,0.0,C,S,S,C,1,replace S at position 0 with C,flow_matching,0.3,2.0,49,213
20,replace,0.0,s,C,C,s,1,replace C at position 0 with s,flow_matching,0.3,2.0,49,213
21,remove,0.0,s,,s,,0,remove s from position 0,flow_matching,0.3,2.0,49,213
22,add,0.0,(,,,(,1,add ( at position 0,flow_matching,0.3,2.0,49,213
23,replace,0.0,=,(,(,=,1,replace ( at position 0 with =,flow_matching,0.3,2.0,49,213
24,replace,0.0,C,=,=,C,1,replace = at position 0 with C,flow_matching,0.3,2.0,49,213
25,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,49,213
26,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,49,213
27,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,49,213
28,replace,0.0,c,C,CO,cO,2,replace C at position 0 with c,flow_matching,0.3,2.0,49,213
29,remove,1.0,O,,cO,c,1,remove O from position 1,flow_matching,0.3,2.0,49,213
30,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,49,213
31,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,49,213
32,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,49,213
33,add,0.0,-,,C,-C,2,add - at position 0,flow_matching,0.3,2.0,49,213
34,add,2.0,-,,-C,-C-,3,add - at position 2,flow_matching,0.3,2.0,49,213
35,replace,0.0,C,-,-C-,CC-,3,replace - at position 0 with C,flow_matching,0.3,2.0,49,213
36,remove,0.0,C,,CC-,C-,2,remove C from position 0,flow_matching,0.3,2.0,49,213
37,remove,0.0,C,,C-,-,1,remove C from position 0,flow_matching,0.3,2.0,49,213
38,remove,0.0,-,,-,,0,remove - from position 0,flow_matching,0.3,2.0,49,213
39,add,0.0,S,,,S,1,add S at position 0,flow_matching,0.3,2.0,49,213
40,remove,0.0,S,,S,,0,remove S from position 0,flow_matching,0.3,2.0,49,213
41,add,0.0,l,,,l,1,add l at position 0,flow_matching,0.3,2.0,49,213
42,replace,0.0,C,l,l,C,1,replace l at position 0 with C,flow_matching,0.3,2.0,49,213
43,add,0.0,O,,C,OC,2,add O at position 0,flow_matching,0.3,2.0,49,213
44,replace,0.0,C,O,OC,CC,2,replace O at position 0 with C,flow_matching,0.3,2.0,49,213
45,replace,1.0,=,C,CC,C=,2,replace C at position 1 with =,flow_matching,0.3,2.0,49,213
46,replace,1.0,l,=,C=,Cl,2,replace = at position 1 with l,flow_matching,0.3,2.0,49,213
47,add,2.0,I,,Cl,ClI,3,add I at position 2,flow_matching,0.3,2.0,49,213
48,replace,1.0,O,l,ClI,COI,3,replace l at position 1 with O,flow_matching,0.3,2.0,49,213
49,add,2.0,n,,COI,COnI,4,add n at position 2,flow_matching,0.3,2.0,49,213
50,add,0.0,B,,COnI,BCOnI,5,add B at position 0,flow_matching,0.3,2.0,49,213
51,replace,0.0,C,B,BCOnI,CCOnI,5,replace B at position 0 with C,flow_matching,0.3,2.0,49,213
52,replace,1.0,O,C,CCOnI,COOnI,5,replace C at position 1 with O,flow_matching,0.3,2.0,49,213
53,replace,4.0,F,I,COOnI,COOnF,5,replace I at position 4 with F,flow_matching,0.3,2.0,49,213
54,remove,1.0,O,,COOnF,COnF,4,remove O from position 1,flow_matching,0.3,2.0,49,213
55,replace,2.0,C,n,COnF,COCF,4,replace n at position 2 with C,flow_matching,0.3,2.0,49,213
56,replace,3.0,(,F,COCF,COC(,4,replace F at position 3 with (,flow_matching,0.3,2.0,49,213
57,add,4.0,=,,COC(,COC(=,5,add = at position 4,flow_matching,0.3,2.0,49,213
58,replace,4.0,@,=,COC(=,COC(@,5,replace = at position 4 with @,flow_matching,0.3,2.0,49,213
59,add,3.0,r,,COC(@,COCr(@,6,add r at position 3,flow_matching,0.3,2.0,49,213
60,add,4.0,O,,COCr(@,COCrO(@,7,add O at position 4,flow_matching,0.3,2.0,49,213
61,replace,3.0,(,r,COCrO(@,COC(O(@,7,replace r at position 3 with (,flow_matching,0.3,2.0,49,213
62,replace,4.0,=,O,COC(O(@,COC(=(@,7,replace O at position 4 with =,flow_matching,0.3,2.0,49,213
63,add,0.0,B,,COC(=(@,BCOC(=(@,8,add B at position 0,flow_matching,0.3,2.0,49,213
64,replace,0.0,C,B,BCOC(=(@,CCOC(=(@,8,replace B at position 0 with C,flow_matching,0.3,2.0,49,213
65,replace,7.0,H,@,CCOC(=(@,CCOC(=(H,8,replace @ at position 7 with H,flow_matching,0.3,2.0,49,213
66,replace,1.0,O,C,CCOC(=(H,COOC(=(H,8,replace C at position 1 with O,flow_matching,0.3,2.0,49,213
67,replace,2.0,C,O,COOC(=(H,COCC(=(H,8,replace O at position 2 with C,flow_matching,0.3,2.0,49,213
68,replace,0.0,=,C,COCC(=(H,=OCC(=(H,8,replace C at position 0 with =,flow_matching,0.3,2.0,49,213
69,replace,0.0,C,=,=OCC(=(H,COCC(=(H,8,replace = at position 0 with C,flow_matching,0.3,2.0,49,213
70,replace,4.0,O,(,COCC(=(H,COCCO=(H,8,replace ( at position 4 with O,flow_matching,0.3,2.0,49,213
71,replace,5.0,C,=,COCCO=(H,COCCOC(H,8,replace = at position 5 with C,flow_matching,0.3,2.0,49,213
72,replace,3.0,(,C,COCCOC(H,COC(OC(H,8,replace C at position 3 with (,flow_matching,0.3,2.0,49,213
73,add,2.0,n,,COC(OC(H,COnC(OC(H,9,add n at position 2,flow_matching,0.3,2.0,49,213
74,replace,0.0,(,C,COnC(OC(H,(OnC(OC(H,9,replace C at position 0 with (,flow_matching,0.3,2.0,49,213
75,replace,0.0,C,(,(OnC(OC(H,COnC(OC(H,9,replace ( at position 0 with C,flow_matching,0.3,2.0,49,213
76,replace,2.0,C,n,COnC(OC(H,COCC(OC(H,9,replace n at position 2 with C,flow_matching,0.3,2.0,49,213
77,replace,7.0,2,(,COCC(OC(H,COCC(OC2H,9,replace ( at position 7 with 2,flow_matching,0.3,2.0,49,213
78,replace,3.0,(,C,COCC(OC2H,COC((OC2H,9,replace C at position 3 with (,flow_matching,0.3,2.0,49,213
79,replace,4.0,=,(,COC((OC2H,COC(=OC2H,9,replace ( at position 4 with =,flow_matching,0.3,2.0,49,213
80,replace,2.0,/,C,COC(=OC2H,CO/(=OC2H,9,replace C at position 2 with /,flow_matching,0.3,2.0,49,213
81,replace,7.0,r,2,CO/(=OC2H,CO/(=OCrH,9,replace 2 at position 7 with r,flow_matching,0.3,2.0,49,213
82,add,2.0,C,,CO/(=OCrH,COC/(=OCrH,10,add C at position 2,flow_matching,0.3,2.0,49,213
83,replace,3.0,(,/,COC/(=OCrH,COC((=OCrH,10,replace / at position 3 with (,flow_matching,0.3,2.0,49,213
84,add,5.0,O,,COC((=OCrH,COC((O=OCrH,11,add O at position 5,flow_matching,0.3,2.0,49,213
85,replace,4.0,=,(,COC((O=OCrH,COC(=O=OCrH,11,replace ( at position 4 with =,flow_matching,0.3,2.0,49,213
86,remove,10.0,H,,COC(=O=OCrH,COC(=O=OCr,10,remove H from position 10,flow_matching,0.3,2.0,49,213
87,replace,6.0,),=,COC(=O=OCr,COC(=O)OCr,10,replace = at position 6 with ),flow_matching,0.3,2.0,49,213
88,remove,7.0,O,,COC(=O)OCr,COC(=O)Cr,9,remove O from position 7,flow_matching,0.3,2.0,49,213
89,add,1.0,c,,COC(=O)Cr,CcOC(=O)Cr,10,add c at position 1,flow_matching,0.3,2.0,49,213
90,remove,1.0,c,,CcOC(=O)Cr,COC(=O)Cr,9,remove c from position 1,flow_matching,0.3,2.0,49,213
91,remove,4.0,=,,COC(=O)Cr,COC(O)Cr,8,remove = from position 4,flow_matching,0.3,2.0,49,213
92,remove,3.0,(,,COC(O)Cr,COCO)Cr,7,remove ( from position 3,flow_matching,0.3,2.0,49,213
93,replace,6.0,5,r,COCO)Cr,COCO)C5,7,replace r at position 6 with 5,flow_matching,0.3,2.0,49,213
94,add,4.0,C,,COCO)C5,COCOC)C5,8,add C at position 4,flow_matching,0.3,2.0,49,213
95,remove,3.0,O,,COCOC)C5,COCC)C5,7,remove O from position 3,flow_matching,0.3,2.0,49,213
96,replace,3.0,(,C,COCC)C5,COC()C5,7,replace C at position 3 with (,flow_matching,0.3,2.0,49,213
97,remove,0.0,C,,COC()C5,OC()C5,6,remove C from position 0,flow_matching,0.3,2.0,49,213
98,add,4.0,O,,OC()C5,OC()OC5,7,add O at position 4,flow_matching,0.3,2.0,49,213
99,remove,5.0,C,,OC()OC5,OC()O5,6,remove C from position 5,flow_matching,0.3,2.0,49,213
100,add,6.0,l,,OC()O5,OC()O5l,7,add l at position 6,flow_matching,0.3,2.0,49,213
101,add,4.0,N,,OC()O5l,OC()NO5l,8,add N at position 4,flow_matching,0.3,2.0,49,213
102,remove,4.0,N,,OC()NO5l,OC()O5l,7,remove N from position 4,flow_matching,0.3,2.0,49,213
103,remove,0.0,O,,OC()O5l,C()O5l,6,remove O from position 0,flow_matching,0.3,2.0,49,213
104,add,4.0,N,,C()O5l,C()ON5l,7,add N at position 4,flow_matching,0.3,2.0,49,213
105,replace,1.0,O,(,C()ON5l,CO)ON5l,7,replace ( at position 1 with O,flow_matching,0.3,2.0,49,213
106,replace,2.0,C,),CO)ON5l,COCON5l,7,replace ) at position 2 with C,flow_matching,0.3,2.0,49,213
107,add,3.0,(,,COCON5l,COC(ON5l,8,add ( at position 3,flow_matching,0.3,2.0,49,213
108,add,8.0,s,,COC(ON5l,COC(ON5ls,9,add s at position 8,flow_matching,0.3,2.0,49,213
109,replace,4.0,=,O,COC(ON5ls,COC(=N5ls,9,replace O at position 4 with =,flow_matching,0.3,2.0,49,213
110,add,3.0,B,,COC(=N5ls,COCB(=N5ls,10,add B at position 3,flow_matching,0.3,2.0,49,213
111,remove,0.0,C,,COCB(=N5ls,OCB(=N5ls,9,remove C from position 0,flow_matching,0.3,2.0,49,213
112,replace,1.0,(,C,OCB(=N5ls,O(B(=N5ls,9,replace C at position 1 with (,flow_matching,0.3,2.0,49,213
113,add,1.0,[,,O(B(=N5ls,O[(B(=N5ls,10,add [ at position 1,flow_matching,0.3,2.0,49,213
114,add,4.0,7,,O[(B(=N5ls,O[(B7(=N5ls,11,add 7 at position 4,flow_matching,0.3,2.0,49,213
115,remove,2.0,(,,O[(B7(=N5ls,O[B7(=N5ls,10,remove ( from position 2,flow_matching,0.3,2.0,49,213
116,remove,2.0,B,,O[B7(=N5ls,O[7(=N5ls,9,remove B from position 2,flow_matching,0.3,2.0,49,213
117,remove,5.0,N,,O[7(=N5ls,O[7(=5ls,8,remove N from position 5,flow_matching,0.3,2.0,49,213
118,add,6.0,[,,O[7(=5ls,O[7(=5[ls,9,add [ at position 6,flow_matching,0.3,2.0,49,213
119,replace,4.0,6,=,O[7(=5[ls,O[7(65[ls,9,replace = at position 4 with 6,flow_matching,0.3,2.0,49,213
120,add,5.0,[,,O[7(65[ls,O[7(6[5[ls,10,add [ at position 5,flow_matching,0.3,2.0,49,213
121,add,5.0,N,,O[7(6[5[ls,O[7(6N[5[ls,11,add N at position 5,flow_matching,0.3,2.0,49,213
122,add,10.0,\,,O[7(6N[5[ls,O[7(6N[5[l\s,12,add \ at position 10,flow_matching,0.3,2.0,49,213
123,replace,0.0,C,O,O[7(6N[5[l\s,C[7(6N[5[l\s,12,replace O at position 0 with C,flow_matching,0.3,2.0,49,213
124,add,1.0,I,,C[7(6N[5[l\s,CI[7(6N[5[l\s,13,add I at position 1,flow_matching,0.3,2.0,49,213
125,replace,4.0,o,(,CI[7(6N[5[l\s,CI[7o6N[5[l\s,13,replace ( at position 4 with o,flow_matching,0.3,2.0,49,213
126,replace,1.0,O,I,CI[7o6N[5[l\s,CO[7o6N[5[l\s,13,replace I at position 1 with O,flow_matching,0.3,2.0,49,213
127,replace,2.0,C,[,CO[7o6N[5[l\s,COC7o6N[5[l\s,13,replace [ at position 2 with C,flow_matching,0.3,2.0,49,213
128,add,5.0,3,,COC7o6N[5[l\s,COC7o36N[5[l\s,14,add 3 at position 5,flow_matching,0.3,2.0,49,213
129,remove,5.0,3,,COC7o36N[5[l\s,COC7o6N[5[l\s,13,remove 3 from position 5,flow_matching,0.3,2.0,49,213
130,replace,3.0,(,7,COC7o6N[5[l\s,COC(o6N[5[l\s,13,replace 7 at position 3 with (,flow_matching,0.3,2.0,49,213
131,replace,10.0,\,l,COC(o6N[5[l\s,COC(o6N[5[\\s,13,replace l at position 10 with \,flow_matching,0.3,2.0,49,213
132,replace,2.0,],C,COC(o6N[5[\\s,CO](o6N[5[\\s,13,replace C at position 2 with ],flow_matching,0.3,2.0,49,213
133,add,5.0,1,,CO](o6N[5[\\s,CO](o16N[5[\\s,14,add 1 at position 5,flow_matching,0.3,2.0,49,213
134,remove,11.0,\,,CO](o16N[5[\\s,CO](o16N[5[\s,13,remove \ from position 11,flow_matching,0.3,2.0,49,213
135,replace,2.0,C,],CO](o16N[5[\s,COC(o16N[5[\s,13,replace ] at position 2 with C,flow_matching,0.3,2.0,49,213
136,replace,4.0,=,o,COC(o16N[5[\s,COC(=16N[5[\s,13,replace o at position 4 with =,flow_matching,0.3,2.0,49,213
137,remove,11.0,\,,COC(=16N[5[\s,COC(=16N[5[s,12,remove \ from position 11,flow_matching,0.3,2.0,49,213
138,replace,5.0,O,1,COC(=16N[5[s,COC(=O6N[5[s,12,replace 1 at position 5 with O,flow_matching,0.3,2.0,49,213
139,replace,6.0,),6,COC(=O6N[5[s,COC(=O)N[5[s,12,replace 6 at position 6 with ),flow_matching,0.3,2.0,49,213
140,add,12.0,3,,COC(=O)N[5[s,COC(=O)N[5[s3,13,add 3 at position 12,flow_matching,0.3,2.0,49,213
141,add,9.0,C,,COC(=O)N[5[s3,COC(=O)N[C5[s3,14,add C at position 9,flow_matching,0.3,2.0,49,213
142,add,11.0,4,,COC(=O)N[C5[s3,COC(=O)N[C54[s3,15,add 4 at position 11,flow_matching,0.3,2.0,49,213
143,add,11.0,B,,COC(=O)N[C54[s3,COC(=O)N[C5B4[s3,16,add B at position 11,flow_matching,0.3,2.0,49,213
144,replace,7.0,C,N,COC(=O)N[C5B4[s3,COC(=O)C[C5B4[s3,16,replace N at position 7 with C,flow_matching,0.3,2.0,49,213
145,replace,9.0,o,C,COC(=O)C[C5B4[s3,COC(=O)C[o5B4[s3,16,replace C at position 9 with o,flow_matching,0.3,2.0,49,213
146,add,5.0,7,,COC(=O)C[o5B4[s3,COC(=7O)C[o5B4[s3,17,add 7 at position 5,flow_matching,0.3,2.0,49,213
147,remove,9.0,[,,COC(=7O)C[o5B4[s3,COC(=7O)Co5B4[s3,16,remove [ from position 9,flow_matching,0.3,2.0,49,213
148,replace,0.0,I,C,COC(=7O)Co5B4[s3,IOC(=7O)Co5B4[s3,16,replace C at position 0 with I,flow_matching,0.3,2.0,49,213
149,replace,0.0,C,I,IOC(=7O)Co5B4[s3,COC(=7O)Co5B4[s3,16,replace I at position 0 with C,flow_matching,0.3,2.0,49,213
150,replace,5.0,O,7,COC(=7O)Co5B4[s3,COC(=OO)Co5B4[s3,16,replace 7 at position 5 with O,flow_matching,0.3,2.0,49,213
151,replace,1.0,6,O,COC(=OO)Co5B4[s3,C6C(=OO)Co5B4[s3,16,replace O at position 1 with 6,flow_matching,0.3,2.0,49,213
152,remove,11.0,B,,C6C(=OO)Co5B4[s3,C6C(=OO)Co54[s3,15,remove B from position 11,flow_matching,0.3,2.0,49,213
153,add,1.0,(,,C6C(=OO)Co54[s3,C(6C(=OO)Co54[s3,16,add ( at position 1,flow_matching,0.3,2.0,49,213
154,replace,1.0,O,(,C(6C(=OO)Co54[s3,CO6C(=OO)Co54[s3,16,replace ( at position 1 with O,flow_matching,0.3,2.0,49,213
155,remove,8.0,),,CO6C(=OO)Co54[s3,CO6C(=OOCo54[s3,15,remove ) from position 8,flow_matching,0.3,2.0,49,213
156,replace,2.0,C,6,CO6C(=OOCo54[s3,COCC(=OOCo54[s3,15,replace 6 at position 2 with C,flow_matching,0.3,2.0,49,213
157,remove,14.0,3,,COCC(=OOCo54[s3,COCC(=OOCo54[s,14,remove 3 from position 14,flow_matching,0.3,2.0,49,213
158,replace,3.0,(,C,COCC(=OOCo54[s,COC((=OOCo54[s,14,replace C at position 3 with (,flow_matching,0.3,2.0,49,213
159,add,8.0,/,,COC((=OOCo54[s,COC((=OO/Co54[s,15,add / at position 8,flow_matching,0.3,2.0,49,213
160,replace,3.0,c,(,COC((=OO/Co54[s,COCc(=OO/Co54[s,15,replace ( at position 3 with c,flow_matching,0.3,2.0,49,213
161,replace,3.0,3,c,COCc(=OO/Co54[s,COC3(=OO/Co54[s,15,replace c at position 3 with 3,flow_matching,0.3,2.0,49,213
162,replace,7.0,),O,COC3(=OO/Co54[s,COC3(=O)/Co54[s,15,replace O at position 7 with ),flow_matching,0.3,2.0,49,213
163,replace,3.0,(,3,COC3(=O)/Co54[s,COC((=O)/Co54[s,15,replace 3 at position 3 with (,flow_matching,0.3,2.0,49,213
164,remove,6.0,O,,COC((=O)/Co54[s,COC((=)/Co54[s,14,remove O from position 6,flow_matching,0.3,2.0,49,213
165,replace,6.0,H,),COC((=)/Co54[s,COC((=H/Co54[s,14,replace ) at position 6 with H,flow_matching,0.3,2.0,49,213
166,add,3.0,H,,COC((=H/Co54[s,COCH((=H/Co54[s,15,add H at position 3,flow_matching,0.3,2.0,49,213
167,add,3.0,4,,COCH((=H/Co54[s,COC4H((=H/Co54[s,16,add 4 at position 3,flow_matching,0.3,2.0,49,213
168,replace,3.0,(,4,COC4H((=H/Co54[s,COC(H((=H/Co54[s,16,replace 4 at position 3 with (,flow_matching,0.3,2.0,49,213
169,replace,4.0,=,H,COC(H((=H/Co54[s,COC(=((=H/Co54[s,16,replace H at position 4 with =,flow_matching,0.3,2.0,49,213
170,replace,5.0,O,(,COC(=((=H/Co54[s,COC(=O(=H/Co54[s,16,replace ( at position 5 with O,flow_matching,0.3,2.0,49,213
171,replace,6.0,),(,COC(=O(=H/Co54[s,COC(=O)=H/Co54[s,16,replace ( at position 6 with ),flow_matching,0.3,2.0,49,213
172,replace,7.0,C,=,COC(=O)=H/Co54[s,COC(=O)CH/Co54[s,16,replace = at position 7 with C,flow_matching,0.3,2.0,49,213
173,replace,8.0,1,H,COC(=O)CH/Co54[s,COC(=O)C1/Co54[s,16,replace H at position 8 with 1,flow_matching,0.3,2.0,49,213
174,replace,9.0,(,/,COC(=O)C1/Co54[s,COC(=O)C1(Co54[s,16,replace / at position 9 with (,flow_matching,0.3,2.0,49,213
175,replace,10.0,N,C,COC(=O)C1(Co54[s,COC(=O)C1(No54[s,16,replace C at position 10 with N,flow_matching,0.3,2.0,49,213
176,replace,11.0,C,o,COC(=O)C1(No54[s,COC(=O)C1(NC54[s,16,replace o at position 11 with C,flow_matching,0.3,2.0,49,213
177,replace,12.0,(,5,COC(=O)C1(NC54[s,COC(=O)C1(NC(4[s,16,replace 5 at position 12 with (,flow_matching,0.3,2.0,49,213
178,replace,13.0,=,4,COC(=O)C1(NC(4[s,COC(=O)C1(NC(=[s,16,replace 4 at position 13 with =,flow_matching,0.3,2.0,49,213
179,replace,14.0,O,[,COC(=O)C1(NC(=[s,COC(=O)C1(NC(=Os,16,replace [ at position 14 with O,flow_matching,0.3,2.0,49,213
180,replace,15.0,),s,COC(=O)C1(NC(=Os,COC(=O)C1(NC(=O),16,replace s at position 15 with ),flow_matching,0.3,2.0,49,213
181,add,16.0,[,,COC(=O)C1(NC(=O),COC(=O)C1(NC(=O)[,17,add [ at position 16,flow_matching,0.3,2.0,49,213
182,add,17.0,C,,COC(=O)C1(NC(=O)[,COC(=O)C1(NC(=O)[C,18,add C at position 17,flow_matching,0.3,2.0,49,213
183,add,18.0,@,,COC(=O)C1(NC(=O)[C,COC(=O)C1(NC(=O)[C@,19,add @ at position 18,flow_matching,0.3,2.0,49,213
184,add,19.0,H,,COC(=O)C1(NC(=O)[C@,COC(=O)C1(NC(=O)[C@H,20,add H at position 19,flow_matching,0.3,2.0,49,213
185,add,20.0,],,COC(=O)C1(NC(=O)[C@H,COC(=O)C1(NC(=O)[C@H],21,add ] at position 20,flow_matching,0.3,2.0,49,213
186,add,21.0,2,,COC(=O)C1(NC(=O)[C@H],COC(=O)C1(NC(=O)[C@H]2,22,add 2 at position 21,flow_matching,0.3,2.0,49,213
187,add,22.0,C,,COC(=O)C1(NC(=O)[C@H]2,COC(=O)C1(NC(=O)[C@H]2C,23,add C at position 22,flow_matching,0.3,2.0,49,213
188,add,23.0,[,,COC(=O)C1(NC(=O)[C@H]2C,COC(=O)C1(NC(=O)[C@H]2C[,24,add [ at position 23,flow_matching,0.3,2.0,49,213
189,add,24.0,C,,COC(=O)C1(NC(=O)[C@H]2C[,COC(=O)C1(NC(=O)[C@H]2C[C,25,add C at position 24,flow_matching,0.3,2.0,49,213
190,add,25.0,@,,COC(=O)C1(NC(=O)[C@H]2C[C,COC(=O)C1(NC(=O)[C@H]2C[C@,26,add @ at position 25,flow_matching,0.3,2.0,49,213
191,add,26.0,H,,COC(=O)C1(NC(=O)[C@H]2C[C@,COC(=O)C1(NC(=O)[C@H]2C[C@H,27,add H at position 26,flow_matching,0.3,2.0,49,213
192,add,27.0,],,COC(=O)C1(NC(=O)[C@H]2C[C@H,COC(=O)C1(NC(=O)[C@H]2C[C@H],28,add ] at position 27,flow_matching,0.3,2.0,49,213
193,add,28.0,2,,COC(=O)C1(NC(=O)[C@H]2C[C@H],COC(=O)C1(NC(=O)[C@H]2C[C@H]2,29,add 2 at position 28,flow_matching,0.3,2.0,49,213
194,add,29.0,c,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c,30,add c at position 29,flow_matching,0.3,2.0,49,213
195,add,30.0,2,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2,31,add 2 at position 30,flow_matching,0.3,2.0,49,213
196,add,31.0,c,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c,32,add c at position 31,flow_matching,0.3,2.0,49,213
197,add,32.0,(,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(,33,add ( at position 32,flow_matching,0.3,2.0,49,213
198,add,33.0,F,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F,34,add F at position 33,flow_matching,0.3,2.0,49,213
199,add,34.0,),,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F),35,add ) at position 34,flow_matching,0.3,2.0,49,213
200,add,35.0,c,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F),COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)c,36,add c at position 35,flow_matching,0.3,2.0,49,213
201,add,36.0,c,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)c,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cc,37,add c at position 36,flow_matching,0.3,2.0,49,213
202,add,37.0,c,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cc,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)ccc,38,add c at position 37,flow_matching,0.3,2.0,49,213
203,add,38.0,c,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)ccc,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc,39,add c at position 38,flow_matching,0.3,2.0,49,213
204,add,39.0,2,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2,40,add 2 at position 39,flow_matching,0.3,2.0,49,213
205,add,40.0,F,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F,41,add F at position 40,flow_matching,0.3,2.0,49,213
206,add,41.0,),,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F),42,add ) at position 41,flow_matching,0.3,2.0,49,213
207,add,42.0,C,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F),COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)C,43,add C at position 42,flow_matching,0.3,2.0,49,213
208,add,43.0,C,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)C,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CC,44,add C at position 43,flow_matching,0.3,2.0,49,213
209,add,44.0,S,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CC,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CCS,45,add S at position 44,flow_matching,0.3,2.0,49,213
210,add,45.0,C,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CCS,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CCSC,46,add C at position 45,flow_matching,0.3,2.0,49,213
211,add,46.0,C,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CCSC,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CCSCC,47,add C at position 46,flow_matching,0.3,2.0,49,213
212,add,47.0,1,,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CCSCC,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CCSCC1,48,add 1 at position 47,flow_matching,0.3,2.0,49,213
213,add,48.0,"
",,COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CCSCC1,"COC(=O)C1(NC(=O)[C@H]2C[C@H]2c2c(F)cccc2F)CCSCC1
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,213
