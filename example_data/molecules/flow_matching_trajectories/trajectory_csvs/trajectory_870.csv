step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,38,145
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,38,145
2,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,38,145
3,add,0.0,\,,,\,1,add \ at position 0,flow_matching,0.3,2.0,38,145
4,add,0.0,s,,\,s\,2,add s at position 0,flow_matching,0.3,2.0,38,145
5,replace,1.0,H,\,s\,sH,2,replace \ at position 1 with H,flow_matching,0.3,2.0,38,145
6,add,1.0,],,sH,s]H,3,add ] at position 1,flow_matching,0.3,2.0,38,145
7,add,0.0,r,,s]H,rs]H,4,add r at position 0,flow_matching,0.3,2.0,38,145
8,add,4.0,2,,rs]H,rs]H2,5,add 2 at position 4,flow_matching,0.3,2.0,38,145
9,add,4.0,3,,rs]H2,rs]H32,6,add 3 at position 4,flow_matching,0.3,2.0,38,145
10,replace,3.0,=,H,rs]H32,rs]=32,6,replace H at position 3 with =,flow_matching,0.3,2.0,38,145
11,remove,2.0,],,rs]=32,rs=32,5,remove ] from position 2,flow_matching,0.3,2.0,38,145
12,remove,3.0,3,,rs=32,rs=2,4,remove 3 from position 3,flow_matching,0.3,2.0,38,145
13,add,0.0,[,,rs=2,[rs=2,5,add [ at position 0,flow_matching,0.3,2.0,38,145
14,add,0.0,7,,[rs=2,7[rs=2,6,add 7 at position 0,flow_matching,0.3,2.0,38,145
15,remove,4.0,=,,7[rs=2,7[rs2,5,remove = from position 4,flow_matching,0.3,2.0,38,145
16,replace,3.0,3,s,7[rs2,7[r32,5,replace s at position 3 with 3,flow_matching,0.3,2.0,38,145
17,replace,0.0,C,7,7[r32,C[r32,5,replace 7 at position 0 with C,flow_matching,0.3,2.0,38,145
18,replace,1.0,(,[,C[r32,C(r32,5,replace [ at position 1 with (,flow_matching,0.3,2.0,38,145
19,replace,0.0,+,C,C(r32,+(r32,5,replace C at position 0 with +,flow_matching,0.3,2.0,38,145
20,remove,4.0,2,,+(r32,+(r3,4,remove 2 from position 4,flow_matching,0.3,2.0,38,145
21,replace,3.0,c,3,+(r3,+(rc,4,replace 3 at position 3 with c,flow_matching,0.3,2.0,38,145
22,add,1.0,C,,+(rc,+C(rc,5,add C at position 1,flow_matching,0.3,2.0,38,145
23,remove,0.0,+,,+C(rc,C(rc,4,remove + from position 0,flow_matching,0.3,2.0,38,145
24,replace,2.0,s,r,C(rc,C(sc,4,replace r at position 2 with s,flow_matching,0.3,2.0,38,145
25,replace,1.0,C,(,C(sc,CCsc,4,replace ( at position 1 with C,flow_matching,0.3,2.0,38,145
26,remove,1.0,C,,CCsc,Csc,3,remove C from position 1,flow_matching,0.3,2.0,38,145
27,add,0.0,-,,Csc,-Csc,4,add - at position 0,flow_matching,0.3,2.0,38,145
28,add,4.0,n,,-Csc,-Cscn,5,add n at position 4,flow_matching,0.3,2.0,38,145
29,replace,0.0,C,-,-Cscn,CCscn,5,replace - at position 0 with C,flow_matching,0.3,2.0,38,145
30,replace,2.0,(,s,CCscn,CC(cn,5,replace s at position 2 with (,flow_matching,0.3,2.0,38,145
31,replace,3.0,C,c,CC(cn,CC(Cn,5,replace c at position 3 with C,flow_matching,0.3,2.0,38,145
32,remove,1.0,C,,CC(Cn,C(Cn,4,remove C from position 1,flow_matching,0.3,2.0,38,145
33,add,0.0,3,,C(Cn,3C(Cn,5,add 3 at position 0,flow_matching,0.3,2.0,38,145
34,remove,3.0,C,,3C(Cn,3C(n,4,remove C from position 3,flow_matching,0.3,2.0,38,145
35,add,3.0,(,,3C(n,3C((n,5,add ( at position 3,flow_matching,0.3,2.0,38,145
36,replace,0.0,C,3,3C((n,CC((n,5,replace 3 at position 0 with C,flow_matching,0.3,2.0,38,145
37,remove,1.0,C,,CC((n,C((n,4,remove C from position 1,flow_matching,0.3,2.0,38,145
38,add,1.0,O,,C((n,CO((n,5,add O at position 1,flow_matching,0.3,2.0,38,145
39,add,5.0,N,,CO((n,CO((nN,6,add N at position 5,flow_matching,0.3,2.0,38,145
40,replace,1.0,C,O,CO((nN,CC((nN,6,replace O at position 1 with C,flow_matching,0.3,2.0,38,145
41,replace,3.0,C,(,CC((nN,CC(CnN,6,replace ( at position 3 with C,flow_matching,0.3,2.0,38,145
42,remove,1.0,C,,CC(CnN,C(CnN,5,remove C from position 1,flow_matching,0.3,2.0,38,145
43,replace,0.0,],C,C(CnN,](CnN,5,replace C at position 0 with ],flow_matching,0.3,2.0,38,145
44,add,1.0,C,,](CnN,]C(CnN,6,add C at position 1,flow_matching,0.3,2.0,38,145
45,replace,5.0,I,N,]C(CnN,]C(CnI,6,replace N at position 5 with I,flow_matching,0.3,2.0,38,145
46,add,4.0,H,,]C(CnI,]C(CHnI,7,add H at position 4,flow_matching,0.3,2.0,38,145
47,add,3.0,O,,]C(CHnI,]C(OCHnI,8,add O at position 3,flow_matching,0.3,2.0,38,145
48,remove,3.0,O,,]C(OCHnI,]C(CHnI,7,remove O from position 3,flow_matching,0.3,2.0,38,145
49,replace,0.0,C,],]C(CHnI,CC(CHnI,7,replace ] at position 0 with C,flow_matching,0.3,2.0,38,145
50,replace,4.0,),H,CC(CHnI,CC(C)nI,7,replace H at position 4 with ),flow_matching,0.3,2.0,38,145
51,remove,4.0,),,CC(C)nI,CC(CnI,6,remove ) from position 4,flow_matching,0.3,2.0,38,145
52,add,4.0,s,,CC(CnI,CC(CsnI,7,add s at position 4,flow_matching,0.3,2.0,38,145
53,remove,6.0,I,,CC(CsnI,CC(Csn,6,remove I from position 6,flow_matching,0.3,2.0,38,145
54,replace,4.0,),s,CC(Csn,CC(C)n,6,replace s at position 4 with ),flow_matching,0.3,2.0,38,145
55,remove,4.0,),,CC(C)n,CC(Cn,5,remove ) from position 4,flow_matching,0.3,2.0,38,145
56,replace,0.0,@,C,CC(Cn,@C(Cn,5,replace C at position 0 with @,flow_matching,0.3,2.0,38,145
57,remove,4.0,n,,@C(Cn,@C(C,4,remove n from position 4,flow_matching,0.3,2.0,38,145
58,add,1.0,6,,@C(C,@6C(C,5,add 6 at position 1,flow_matching,0.3,2.0,38,145
59,replace,0.0,C,@,@6C(C,C6C(C,5,replace @ at position 0 with C,flow_matching,0.3,2.0,38,145
60,replace,3.0,),(,C6C(C,C6C)C,5,replace ( at position 3 with ),flow_matching,0.3,2.0,38,145
61,replace,1.0,C,6,C6C)C,CCC)C,5,replace 6 at position 1 with C,flow_matching,0.3,2.0,38,145
62,replace,2.0,(,C,CCC)C,CC()C,5,replace C at position 2 with (,flow_matching,0.3,2.0,38,145
63,replace,3.0,C,),CC()C,CC(CC,5,replace ) at position 3 with C,flow_matching,0.3,2.0,38,145
64,replace,4.0,),C,CC(CC,CC(C),5,replace C at position 4 with ),flow_matching,0.3,2.0,38,145
65,remove,4.0,),,CC(C),CC(C,4,remove ) from position 4,flow_matching,0.3,2.0,38,145
66,remove,2.0,(,,CC(C,CCC,3,remove ( from position 2,flow_matching,0.3,2.0,38,145
67,replace,2.0,S,C,CCC,CCS,3,replace C at position 2 with S,flow_matching,0.3,2.0,38,145
68,replace,1.0,F,C,CCS,CFS,3,replace C at position 1 with F,flow_matching,0.3,2.0,38,145
69,replace,0.0,/,C,CFS,/FS,3,replace C at position 0 with /,flow_matching,0.3,2.0,38,145
70,remove,1.0,F,,/FS,/S,2,remove F from position 1,flow_matching,0.3,2.0,38,145
71,replace,0.0,C,/,/S,CS,2,replace / at position 0 with C,flow_matching,0.3,2.0,38,145
72,replace,0.0,B,C,CS,BS,2,replace C at position 0 with B,flow_matching,0.3,2.0,38,145
73,replace,0.0,C,B,BS,CS,2,replace B at position 0 with C,flow_matching,0.3,2.0,38,145
74,add,0.0,1,,CS,1CS,3,add 1 at position 0,flow_matching,0.3,2.0,38,145
75,replace,0.0,3,1,1CS,3CS,3,replace 1 at position 0 with 3,flow_matching,0.3,2.0,38,145
76,replace,1.0,(,C,3CS,3(S,3,replace C at position 1 with (,flow_matching,0.3,2.0,38,145
77,add,2.0,1,,3(S,3(1S,4,add 1 at position 2,flow_matching,0.3,2.0,38,145
78,replace,0.0,C,3,3(1S,C(1S,4,replace 3 at position 0 with C,flow_matching,0.3,2.0,38,145
79,replace,1.0,C,(,C(1S,CC1S,4,replace ( at position 1 with C,flow_matching,0.3,2.0,38,145
80,replace,2.0,(,1,CC1S,CC(S,4,replace 1 at position 2 with (,flow_matching,0.3,2.0,38,145
81,replace,3.0,C,S,CC(S,CC(C,4,replace S at position 3 with C,flow_matching,0.3,2.0,38,145
82,add,4.0,),,CC(C,CC(C),5,add ) at position 4,flow_matching,0.3,2.0,38,145
83,remove,0.0,C,,CC(C),C(C),4,remove C from position 0,flow_matching,0.3,2.0,38,145
84,replace,1.0,C,(,C(C),CCC),4,replace ( at position 1 with C,flow_matching,0.3,2.0,38,145
85,add,1.0,),,CCC),C)CC),5,add ) at position 1,flow_matching,0.3,2.0,38,145
86,replace,1.0,C,),C)CC),CCCC),5,replace ) at position 1 with C,flow_matching,0.3,2.0,38,145
87,remove,2.0,C,,CCCC),CCC),4,remove C from position 2,flow_matching,0.3,2.0,38,145
88,replace,2.0,(,C,CCC),CC(),4,replace C at position 2 with (,flow_matching,0.3,2.0,38,145
89,replace,0.0,],C,CC(),]C(),4,replace C at position 0 with ],flow_matching,0.3,2.0,38,145
90,add,4.0,2,,]C(),]C()2,5,add 2 at position 4,flow_matching,0.3,2.0,38,145
91,replace,0.0,C,],]C()2,CC()2,5,replace ] at position 0 with C,flow_matching,0.3,2.0,38,145
92,remove,4.0,2,,CC()2,CC(),4,remove 2 from position 4,flow_matching,0.3,2.0,38,145
93,add,2.0,F,,CC(),CCF(),5,add F at position 2,flow_matching,0.3,2.0,38,145
94,remove,4.0,),,CCF(),CCF(,4,remove ) from position 4,flow_matching,0.3,2.0,38,145
95,add,0.0,-,,CCF(,-CCF(,5,add - at position 0,flow_matching,0.3,2.0,38,145
96,replace,0.0,C,-,-CCF(,CCCF(,5,replace - at position 0 with C,flow_matching,0.3,2.0,38,145
97,add,4.0,3,,CCCF(,CCCF3(,6,add 3 at position 4,flow_matching,0.3,2.0,38,145
98,add,0.0,F,,CCCF3(,FCCCF3(,7,add F at position 0,flow_matching,0.3,2.0,38,145
99,add,3.0,N,,FCCCF3(,FCCNCF3(,8,add N at position 3,flow_matching,0.3,2.0,38,145
100,add,1.0,@,,FCCNCF3(,F@CCNCF3(,9,add @ at position 1,flow_matching,0.3,2.0,38,145
101,add,5.0,r,,F@CCNCF3(,F@CCNrCF3(,10,add r at position 5,flow_matching,0.3,2.0,38,145
102,add,7.0,I,,F@CCNrCF3(,F@CCNrCIF3(,11,add I at position 7,flow_matching,0.3,2.0,38,145
103,replace,0.0,C,F,F@CCNrCIF3(,C@CCNrCIF3(,11,replace F at position 0 with C,flow_matching,0.3,2.0,38,145
104,replace,10.0,5,(,C@CCNrCIF3(,C@CCNrCIF35,11,replace ( at position 10 with 5,flow_matching,0.3,2.0,38,145
105,add,8.0,\,,C@CCNrCIF35,C@CCNrCI\F35,12,add \ at position 8,flow_matching,0.3,2.0,38,145
106,replace,1.0,C,@,C@CCNrCI\F35,CCCCNrCI\F35,12,replace @ at position 1 with C,flow_matching,0.3,2.0,38,145
107,replace,2.0,(,C,CCCCNrCI\F35,CC(CNrCI\F35,12,replace C at position 2 with (,flow_matching,0.3,2.0,38,145
108,replace,7.0,),I,CC(CNrCI\F35,CC(CNrC)\F35,12,replace I at position 7 with ),flow_matching,0.3,2.0,38,145
109,replace,8.0,H,\,CC(CNrC)\F35,CC(CNrC)HF35,12,replace \ at position 8 with H,flow_matching,0.3,2.0,38,145
110,replace,3.0,s,C,CC(CNrC)HF35,CC(sNrC)HF35,12,replace C at position 3 with s,flow_matching,0.3,2.0,38,145
111,replace,0.0,/,C,CC(sNrC)HF35,/C(sNrC)HF35,12,replace C at position 0 with /,flow_matching,0.3,2.0,38,145
112,replace,0.0,C,/,/C(sNrC)HF35,CC(sNrC)HF35,12,replace / at position 0 with C,flow_matching,0.3,2.0,38,145
113,replace,3.0,C,s,CC(sNrC)HF35,CC(CNrC)HF35,12,replace s at position 3 with C,flow_matching,0.3,2.0,38,145
114,replace,4.0,),N,CC(CNrC)HF35,CC(C)rC)HF35,12,replace N at position 4 with ),flow_matching,0.3,2.0,38,145
115,replace,5.0,(,r,CC(C)rC)HF35,CC(C)(C)HF35,12,replace r at position 5 with (,flow_matching,0.3,2.0,38,145
116,replace,8.0,[,H,CC(C)(C)HF35,CC(C)(C)[F35,12,replace H at position 8 with [,flow_matching,0.3,2.0,38,145
117,replace,9.0,S,F,CC(C)(C)[F35,CC(C)(C)[S35,12,replace F at position 9 with S,flow_matching,0.3,2.0,38,145
118,replace,10.0,@,3,CC(C)(C)[S35,CC(C)(C)[S@5,12,replace 3 at position 10 with @,flow_matching,0.3,2.0,38,145
119,replace,11.0,],5,CC(C)(C)[S@5,CC(C)(C)[S@],12,replace 5 at position 11 with ],flow_matching,0.3,2.0,38,145
120,add,12.0,(,,CC(C)(C)[S@],CC(C)(C)[S@](,13,add ( at position 12,flow_matching,0.3,2.0,38,145
121,add,13.0,=,,CC(C)(C)[S@](,CC(C)(C)[S@](=,14,add = at position 13,flow_matching,0.3,2.0,38,145
122,add,14.0,O,,CC(C)(C)[S@](=,CC(C)(C)[S@](=O,15,add O at position 14,flow_matching,0.3,2.0,38,145
123,add,15.0,),,CC(C)(C)[S@](=O,CC(C)(C)[S@](=O),16,add ) at position 15,flow_matching,0.3,2.0,38,145
124,add,16.0,C,,CC(C)(C)[S@](=O),CC(C)(C)[S@](=O)C,17,add C at position 16,flow_matching,0.3,2.0,38,145
125,add,17.0,C,,CC(C)(C)[S@](=O)C,CC(C)(C)[S@](=O)CC,18,add C at position 17,flow_matching,0.3,2.0,38,145
126,add,18.0,N,,CC(C)(C)[S@](=O)CC,CC(C)(C)[S@](=O)CCN,19,add N at position 18,flow_matching,0.3,2.0,38,145
127,add,19.0,C,,CC(C)(C)[S@](=O)CCN,CC(C)(C)[S@](=O)CCNC,20,add C at position 19,flow_matching,0.3,2.0,38,145
128,add,20.0,(,,CC(C)(C)[S@](=O)CCNC,CC(C)(C)[S@](=O)CCNC(,21,add ( at position 20,flow_matching,0.3,2.0,38,145
129,add,21.0,=,,CC(C)(C)[S@](=O)CCNC(,CC(C)(C)[S@](=O)CCNC(=,22,add = at position 21,flow_matching,0.3,2.0,38,145
130,add,22.0,O,,CC(C)(C)[S@](=O)CCNC(=,CC(C)(C)[S@](=O)CCNC(=O,23,add O at position 22,flow_matching,0.3,2.0,38,145
131,add,23.0,),,CC(C)(C)[S@](=O)CCNC(=O,CC(C)(C)[S@](=O)CCNC(=O),24,add ) at position 23,flow_matching,0.3,2.0,38,145
132,add,24.0,c,,CC(C)(C)[S@](=O)CCNC(=O),CC(C)(C)[S@](=O)CCNC(=O)c,25,add c at position 24,flow_matching,0.3,2.0,38,145
133,add,25.0,1,,CC(C)(C)[S@](=O)CCNC(=O)c,CC(C)(C)[S@](=O)CCNC(=O)c1,26,add 1 at position 25,flow_matching,0.3,2.0,38,145
134,add,26.0,c,,CC(C)(C)[S@](=O)CCNC(=O)c1,CC(C)(C)[S@](=O)CCNC(=O)c1c,27,add c at position 26,flow_matching,0.3,2.0,38,145
135,add,27.0,c,,CC(C)(C)[S@](=O)CCNC(=O)c1c,CC(C)(C)[S@](=O)CCNC(=O)c1cc,28,add c at position 27,flow_matching,0.3,2.0,38,145
136,add,28.0,c,,CC(C)(C)[S@](=O)CCNC(=O)c1cc,CC(C)(C)[S@](=O)CCNC(=O)c1ccc,29,add c at position 28,flow_matching,0.3,2.0,38,145
137,add,29.0,c,,CC(C)(C)[S@](=O)CCNC(=O)c1ccc,CC(C)(C)[S@](=O)CCNC(=O)c1cccc,30,add c at position 29,flow_matching,0.3,2.0,38,145
138,add,30.0,(,,CC(C)(C)[S@](=O)CCNC(=O)c1cccc,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(,31,add ( at position 30,flow_matching,0.3,2.0,38,145
139,add,31.0,F,,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F,32,add F at position 31,flow_matching,0.3,2.0,38,145
140,add,32.0,),,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F),33,add ) at position 32,flow_matching,0.3,2.0,38,145
141,add,33.0,c,,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F),CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F)c,34,add c at position 33,flow_matching,0.3,2.0,38,145
142,add,34.0,1,,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F)c,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F)c1,35,add 1 at position 34,flow_matching,0.3,2.0,38,145
143,add,35.0,C,,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F)c1,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F)c1C,36,add C at position 35,flow_matching,0.3,2.0,38,145
144,add,36.0,l,,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F)c1C,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F)c1Cl,37,add l at position 36,flow_matching,0.3,2.0,38,145
145,add,37.0,"
",,CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F)c1Cl,"CC(C)(C)[S@](=O)CCNC(=O)c1cccc(F)c1Cl
",38,"add 
 at position 37",flow_matching,0.3,2.0,38,145
