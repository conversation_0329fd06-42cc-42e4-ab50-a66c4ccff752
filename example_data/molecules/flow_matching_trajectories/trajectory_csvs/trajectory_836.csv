step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,33,115
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,33,115
2,replace,0.0,C,O,O,C,1,replace <PERSON> at position 0 with C,flow_matching,0.3,2.0,33,115
3,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,33,115
4,remove,0.0,C,,CC,C,1,remove <PERSON> from position 0,flow_matching,0.3,2.0,33,115
5,add,1.0,C,,C,CC,2,add <PERSON> at position 1,flow_matching,0.3,2.0,33,115
6,remove,1.0,<PERSON>,,<PERSON>,<PERSON>,1,remove <PERSON> from position 1,flow_matching,0.3,2.0,33,115
7,add,1.0,<PERSON>,,<PERSON>,<PERSON>,2,add <PERSON> at position 1,flow_matching,0.3,2.0,33,115
8,add,2.0,<PERSON>,,<PERSON>,CCC,3,add C at position 2,flow_matching,0.3,2.0,33,115
9,add,3.0,O,,CCC,CCCO,4,add O at position 3,flow_matching,0.3,2.0,33,115
10,add,4.0,],,CCCO,CCCO],5,add ] at position 4,flow_matching,0.3,2.0,33,115
11,remove,4.0,],,CCCO],CCCO,4,remove ] from position 4,flow_matching,0.3,2.0,33,115
12,add,4.0,c,,CCCO,CCCOc,5,add c at position 4,flow_matching,0.3,2.0,33,115
13,replace,0.0,/,C,CCCOc,/CCOc,5,replace C at position 0 with /,flow_matching,0.3,2.0,33,115
14,remove,0.0,/,,/CCOc,CCOc,4,remove / from position 0,flow_matching,0.3,2.0,33,115
15,replace,3.0,],c,CCOc,CCO],4,replace c at position 3 with ],flow_matching,0.3,2.0,33,115
16,remove,2.0,O,,CCO],CC],3,remove O from position 2,flow_matching,0.3,2.0,33,115
17,replace,2.0,C,],CC],CCC,3,replace ] at position 2 with C,flow_matching,0.3,2.0,33,115
18,remove,1.0,C,,CCC,CC,2,remove C from position 1,flow_matching,0.3,2.0,33,115
19,replace,1.0,+,C,CC,C+,2,replace C at position 1 with +,flow_matching,0.3,2.0,33,115
20,replace,1.0,C,+,C+,CC,2,replace + at position 1 with C,flow_matching,0.3,2.0,33,115
21,add,2.0,C,,CC,CCC,3,add C at position 2,flow_matching,0.3,2.0,33,115
22,add,3.0,O,,CCC,CCCO,4,add O at position 3,flow_matching,0.3,2.0,33,115
23,remove,3.0,O,,CCCO,CCC,3,remove O from position 3,flow_matching,0.3,2.0,33,115
24,add,3.0,O,,CCC,CCCO,4,add O at position 3,flow_matching,0.3,2.0,33,115
25,add,4.0,2,,CCCO,CCCO2,5,add 2 at position 4,flow_matching,0.3,2.0,33,115
26,add,5.0,=,,CCCO2,CCCO2=,6,add = at position 5,flow_matching,0.3,2.0,33,115
27,add,3.0,[,,CCCO2=,CCC[O2=,7,add [ at position 3,flow_matching,0.3,2.0,33,115
28,replace,3.0,O,[,CCC[O2=,CCCOO2=,7,replace [ at position 3 with O,flow_matching,0.3,2.0,33,115
29,replace,4.0,c,O,CCCOO2=,CCCOc2=,7,replace O at position 4 with c,flow_matching,0.3,2.0,33,115
30,replace,5.0,1,2,CCCOc2=,CCCOc1=,7,replace 2 at position 5 with 1,flow_matching,0.3,2.0,33,115
31,add,6.0,4,,CCCOc1=,CCCOc14=,8,add 4 at position 6,flow_matching,0.3,2.0,33,115
32,add,7.0,5,,CCCOc14=,CCCOc145=,9,add 5 at position 7,flow_matching,0.3,2.0,33,115
33,replace,8.0,#,=,CCCOc145=,CCCOc145#,9,replace = at position 8 with #,flow_matching,0.3,2.0,33,115
34,replace,1.0,N,C,CCCOc145#,CNCOc145#,9,replace C at position 1 with N,flow_matching,0.3,2.0,33,115
35,add,8.0,=,,CNCOc145#,CNCOc145=#,10,add = at position 8,flow_matching,0.3,2.0,33,115
36,replace,3.0,1,O,CNCOc145=#,CNC1c145=#,10,replace O at position 3 with 1,flow_matching,0.3,2.0,33,115
37,add,7.0,S,,CNC1c145=#,CNC1c14S5=#,11,add S at position 7,flow_matching,0.3,2.0,33,115
38,replace,4.0,-,c,CNC1c14S5=#,CNC1-14S5=#,11,replace c at position 4 with -,flow_matching,0.3,2.0,33,115
39,replace,9.0,n,=,CNC1-14S5=#,CNC1-14S5n#,11,replace = at position 9 with n,flow_matching,0.3,2.0,33,115
40,replace,2.0,I,C,CNC1-14S5n#,CNI1-14S5n#,11,replace C at position 2 with I,flow_matching,0.3,2.0,33,115
41,replace,6.0,[,4,CNI1-14S5n#,CNI1-1[S5n#,11,replace 4 at position 6 with [,flow_matching,0.3,2.0,33,115
42,remove,7.0,S,,CNI1-1[S5n#,CNI1-1[5n#,10,remove S from position 7,flow_matching,0.3,2.0,33,115
43,replace,9.0,\,#,CNI1-1[5n#,CNI1-1[5n\,10,replace # at position 9 with \,flow_matching,0.3,2.0,33,115
44,replace,5.0,4,1,CNI1-1[5n\,CNI1-4[5n\,10,replace 1 at position 5 with 4,flow_matching,0.3,2.0,33,115
45,replace,8.0,I,n,CNI1-4[5n\,CNI1-4[5I\,10,replace n at position 8 with I,flow_matching,0.3,2.0,33,115
46,remove,1.0,N,,CNI1-4[5I\,CI1-4[5I\,9,remove N from position 1,flow_matching,0.3,2.0,33,115
47,add,4.0,o,,CI1-4[5I\,CI1-o4[5I\,10,add o at position 4,flow_matching,0.3,2.0,33,115
48,remove,5.0,4,,CI1-o4[5I\,CI1-o[5I\,9,remove 4 from position 5,flow_matching,0.3,2.0,33,115
49,replace,0.0,s,C,CI1-o[5I\,sI1-o[5I\,9,replace C at position 0 with s,flow_matching,0.3,2.0,33,115
50,add,6.0,5,,sI1-o[5I\,sI1-o[55I\,10,add 5 at position 6,flow_matching,0.3,2.0,33,115
51,replace,7.0,S,5,sI1-o[55I\,sI1-o[5SI\,10,replace 5 at position 7 with S,flow_matching,0.3,2.0,33,115
52,add,5.0,),,sI1-o[5SI\,sI1-o)[5SI\,11,add ) at position 5,flow_matching,0.3,2.0,33,115
53,add,1.0,=,,sI1-o)[5SI\,s=I1-o)[5SI\,12,add = at position 1,flow_matching,0.3,2.0,33,115
54,remove,11.0,\,,s=I1-o)[5SI\,s=I1-o)[5SI,11,remove \ from position 11,flow_matching,0.3,2.0,33,115
55,add,4.0,],,s=I1-o)[5SI,s=I1]-o)[5SI,12,add ] at position 4,flow_matching,0.3,2.0,33,115
56,replace,2.0,/,I,s=I1]-o)[5SI,s=/1]-o)[5SI,12,replace I at position 2 with /,flow_matching,0.3,2.0,33,115
57,add,10.0,/,,s=/1]-o)[5SI,s=/1]-o)[5/SI,13,add / at position 10,flow_matching,0.3,2.0,33,115
58,add,5.0,#,,s=/1]-o)[5/SI,s=/1]#-o)[5/SI,14,add # at position 5,flow_matching,0.3,2.0,33,115
59,remove,1.0,=,,s=/1]#-o)[5/SI,s/1]#-o)[5/SI,13,remove = from position 1,flow_matching,0.3,2.0,33,115
60,remove,12.0,I,,s/1]#-o)[5/SI,s/1]#-o)[5/S,12,remove I from position 12,flow_matching,0.3,2.0,33,115
61,add,10.0,F,,s/1]#-o)[5/S,s/1]#-o)[5F/S,13,add F at position 10,flow_matching,0.3,2.0,33,115
62,add,13.0,F,,s/1]#-o)[5F/S,s/1]#-o)[5F/SF,14,add F at position 13,flow_matching,0.3,2.0,33,115
63,add,12.0,-,,s/1]#-o)[5F/SF,s/1]#-o)[5F/-SF,15,add - at position 12,flow_matching,0.3,2.0,33,115
64,add,6.0,],,s/1]#-o)[5F/-SF,s/1]#-]o)[5F/-SF,16,add ] at position 6,flow_matching,0.3,2.0,33,115
65,replace,7.0,/,o,s/1]#-]o)[5F/-SF,s/1]#-]/)[5F/-SF,16,replace o at position 7 with /,flow_matching,0.3,2.0,33,115
66,remove,1.0,/,,s/1]#-]/)[5F/-SF,s1]#-]/)[5F/-SF,15,remove / from position 1,flow_matching,0.3,2.0,33,115
67,add,13.0,n,,s1]#-]/)[5F/-SF,s1]#-]/)[5F/-nSF,16,add n at position 13,flow_matching,0.3,2.0,33,115
68,replace,0.0,C,s,s1]#-]/)[5F/-nSF,C1]#-]/)[5F/-nSF,16,replace s at position 0 with C,flow_matching,0.3,2.0,33,115
69,remove,4.0,-,,C1]#-]/)[5F/-nSF,C1]#]/)[5F/-nSF,15,remove - from position 4,flow_matching,0.3,2.0,33,115
70,add,6.0,c,,C1]#]/)[5F/-nSF,C1]#]/c)[5F/-nSF,16,add c at position 6,flow_matching,0.3,2.0,33,115
71,replace,14.0,/,S,C1]#]/c)[5F/-nSF,C1]#]/c)[5F/-n/F,16,replace S at position 14 with /,flow_matching,0.3,2.0,33,115
72,replace,1.0,C,1,C1]#]/c)[5F/-n/F,CC]#]/c)[5F/-n/F,16,replace 1 at position 1 with C,flow_matching,0.3,2.0,33,115
73,add,10.0,B,,CC]#]/c)[5F/-n/F,CC]#]/c)[5BF/-n/F,17,add B at position 10,flow_matching,0.3,2.0,33,115
74,remove,3.0,#,,CC]#]/c)[5BF/-n/F,CC]]/c)[5BF/-n/F,16,remove # from position 3,flow_matching,0.3,2.0,33,115
75,add,6.0,1,,CC]]/c)[5BF/-n/F,CC]]/c1)[5BF/-n/F,17,add 1 at position 6,flow_matching,0.3,2.0,33,115
76,replace,2.0,C,],CC]]/c1)[5BF/-n/F,CCC]/c1)[5BF/-n/F,17,replace ] at position 2 with C,flow_matching,0.3,2.0,33,115
77,remove,1.0,C,,CCC]/c1)[5BF/-n/F,CC]/c1)[5BF/-n/F,16,remove C from position 1,flow_matching,0.3,2.0,33,115
78,replace,2.0,C,],CC]/c1)[5BF/-n/F,CCC/c1)[5BF/-n/F,16,replace ] at position 2 with C,flow_matching,0.3,2.0,33,115
79,add,15.0,(,,CCC/c1)[5BF/-n/F,CCC/c1)[5BF/-n/(F,17,add ( at position 15,flow_matching,0.3,2.0,33,115
80,replace,3.0,O,/,CCC/c1)[5BF/-n/(F,CCCOc1)[5BF/-n/(F,17,replace / at position 3 with O,flow_matching,0.3,2.0,33,115
81,remove,3.0,O,,CCCOc1)[5BF/-n/(F,CCCc1)[5BF/-n/(F,16,remove O from position 3,flow_matching,0.3,2.0,33,115
82,add,12.0,6,,CCCc1)[5BF/-n/(F,CCCc1)[5BF/-6n/(F,17,add 6 at position 12,flow_matching,0.3,2.0,33,115
83,add,5.0,r,,CCCc1)[5BF/-6n/(F,CCCc1r)[5BF/-6n/(F,18,add r at position 5,flow_matching,0.3,2.0,33,115
84,add,15.0,],,CCCc1r)[5BF/-6n/(F,CCCc1r)[5BF/-6n]/(F,19,add ] at position 15,flow_matching,0.3,2.0,33,115
85,replace,10.0,+,F,CCCc1r)[5BF/-6n]/(F,CCCc1r)[5B+/-6n]/(F,19,replace F at position 10 with +,flow_matching,0.3,2.0,33,115
86,replace,3.0,O,c,CCCc1r)[5B+/-6n]/(F,CCCO1r)[5B+/-6n]/(F,19,replace c at position 3 with O,flow_matching,0.3,2.0,33,115
87,replace,4.0,c,1,CCCO1r)[5B+/-6n]/(F,CCCOcr)[5B+/-6n]/(F,19,replace 1 at position 4 with c,flow_matching,0.3,2.0,33,115
88,replace,5.0,1,r,CCCOcr)[5B+/-6n]/(F,CCCOc1)[5B+/-6n]/(F,19,replace r at position 5 with 1,flow_matching,0.3,2.0,33,115
89,replace,6.0,n,),CCCOc1)[5B+/-6n]/(F,CCCOc1n[5B+/-6n]/(F,19,replace ) at position 6 with n,flow_matching,0.3,2.0,33,115
90,replace,7.0,c,[,CCCOc1n[5B+/-6n]/(F,CCCOc1nc5B+/-6n]/(F,19,replace [ at position 7 with c,flow_matching,0.3,2.0,33,115
91,replace,8.0,n,5,CCCOc1nc5B+/-6n]/(F,CCCOc1ncnB+/-6n]/(F,19,replace 5 at position 8 with n,flow_matching,0.3,2.0,33,115
92,replace,9.0,c,B,CCCOc1ncnB+/-6n]/(F,CCCOc1ncnc+/-6n]/(F,19,replace B at position 9 with c,flow_matching,0.3,2.0,33,115
93,replace,10.0,(,+,CCCOc1ncnc+/-6n]/(F,CCCOc1ncnc(/-6n]/(F,19,replace + at position 10 with (,flow_matching,0.3,2.0,33,115
94,replace,11.0,N,/,CCCOc1ncnc(/-6n]/(F,CCCOc1ncnc(N-6n]/(F,19,replace / at position 11 with N,flow_matching,0.3,2.0,33,115
95,replace,12.0,c,-,CCCOc1ncnc(N-6n]/(F,CCCOc1ncnc(Nc6n]/(F,19,replace - at position 12 with c,flow_matching,0.3,2.0,33,115
96,replace,13.0,2,6,CCCOc1ncnc(Nc6n]/(F,CCCOc1ncnc(Nc2n]/(F,19,replace 6 at position 13 with 2,flow_matching,0.3,2.0,33,115
97,replace,14.0,c,n,CCCOc1ncnc(Nc2n]/(F,CCCOc1ncnc(Nc2c]/(F,19,replace n at position 14 with c,flow_matching,0.3,2.0,33,115
98,replace,15.0,c,],CCCOc1ncnc(Nc2c]/(F,CCCOc1ncnc(Nc2cc/(F,19,replace ] at position 15 with c,flow_matching,0.3,2.0,33,115
99,replace,16.0,(,/,CCCOc1ncnc(Nc2cc/(F,CCCOc1ncnc(Nc2cc((F,19,replace / at position 16 with (,flow_matching,0.3,2.0,33,115
100,replace,17.0,C,(,CCCOc1ncnc(Nc2cc((F,CCCOc1ncnc(Nc2cc(CF,19,replace ( at position 17 with C,flow_matching,0.3,2.0,33,115
101,replace,18.0,l,F,CCCOc1ncnc(Nc2cc(CF,CCCOc1ncnc(Nc2cc(Cl,19,replace F at position 18 with l,flow_matching,0.3,2.0,33,115
102,add,19.0,),,CCCOc1ncnc(Nc2cc(Cl,CCCOc1ncnc(Nc2cc(Cl),20,add ) at position 19,flow_matching,0.3,2.0,33,115
103,add,20.0,c,,CCCOc1ncnc(Nc2cc(Cl),CCCOc1ncnc(Nc2cc(Cl)c,21,add c at position 20,flow_matching,0.3,2.0,33,115
104,add,21.0,c,,CCCOc1ncnc(Nc2cc(Cl)c,CCCOc1ncnc(Nc2cc(Cl)cc,22,add c at position 21,flow_matching,0.3,2.0,33,115
105,add,22.0,(,,CCCOc1ncnc(Nc2cc(Cl)cc,CCCOc1ncnc(Nc2cc(Cl)cc(,23,add ( at position 22,flow_matching,0.3,2.0,33,115
106,add,23.0,C,,CCCOc1ncnc(Nc2cc(Cl)cc(,CCCOc1ncnc(Nc2cc(Cl)cc(C,24,add C at position 23,flow_matching,0.3,2.0,33,115
107,add,24.0,l,,CCCOc1ncnc(Nc2cc(Cl)cc(C,CCCOc1ncnc(Nc2cc(Cl)cc(Cl,25,add l at position 24,flow_matching,0.3,2.0,33,115
108,add,25.0,),,CCCOc1ncnc(Nc2cc(Cl)cc(Cl,CCCOc1ncnc(Nc2cc(Cl)cc(Cl),26,add ) at position 25,flow_matching,0.3,2.0,33,115
109,add,26.0,c,,CCCOc1ncnc(Nc2cc(Cl)cc(Cl),CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c,27,add c at position 26,flow_matching,0.3,2.0,33,115
110,add,27.0,2,,CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c,CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2,28,add 2 at position 27,flow_matching,0.3,2.0,33,115
111,add,28.0,),,CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2,CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2),29,add ) at position 28,flow_matching,0.3,2.0,33,115
112,add,29.0,c,,CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2),CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2)c,30,add c at position 29,flow_matching,0.3,2.0,33,115
113,add,30.0,1,,CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2)c,CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2)c1,31,add 1 at position 30,flow_matching,0.3,2.0,33,115
114,add,31.0,N,,CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2)c1,CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2)c1N,32,add N at position 31,flow_matching,0.3,2.0,33,115
115,add,32.0,"
",,CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2)c1N,"CCCOc1ncnc(Nc2cc(Cl)cc(Cl)c2)c1N
",33,"add 
 at position 32",flow_matching,0.3,2.0,33,115
