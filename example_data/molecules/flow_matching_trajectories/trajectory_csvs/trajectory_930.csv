step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,49,205
1,add,0.0,n,,,n,1,add n at position 0,flow_matching,0.3,2.0,49,205
2,add,0.0,[,,n,[n,2,add [ at position 0,flow_matching,0.3,2.0,49,205
3,remove,1.0,n,,[n,[,1,remove n from position 1,flow_matching,0.3,2.0,49,205
4,remove,0.0,[,,[,,0,remove [ from position 0,flow_matching,0.3,2.0,49,205
5,add,0.0,(,,,(,1,add ( at position 0,flow_matching,0.3,2.0,49,205
6,replace,0.0,O,(,(,O,1,replace ( at position 0 with O,flow_matching,0.3,2.0,49,205
7,add,1.0,c,,O,Oc,2,add c at position 1,flow_matching,0.3,2.0,49,205
8,replace,1.0,S,c,Oc,<PERSON>,2,replace c at position 1 with S,flow_matching,0.3,2.0,49,205
9,replace,1.0,c,S,OS,Oc,2,replace S at position 1 with c,flow_matching,0.3,2.0,49,205
10,replace,1.0,[,c,Oc,O[,2,replace c at position 1 with [,flow_matching,0.3,2.0,49,205
11,replace,1.0,O,[,O[,OO,2,replace [ at position 1 with O,flow_matching,0.3,2.0,49,205
12,remove,1.0,O,,OO,O,1,remove O from position 1,flow_matching,0.3,2.0,49,205
13,add,0.0,=,,O,=O,2,add = at position 0,flow_matching,0.3,2.0,49,205
14,replace,0.0,O,=,=O,OO,2,replace = at position 0 with O,flow_matching,0.3,2.0,49,205
15,replace,1.0,c,O,OO,Oc,2,replace O at position 1 with c,flow_matching,0.3,2.0,49,205
16,replace,0.0,l,O,Oc,lc,2,replace O at position 0 with l,flow_matching,0.3,2.0,49,205
17,remove,0.0,l,,lc,c,1,remove l from position 0,flow_matching,0.3,2.0,49,205
18,add,1.0,H,,c,cH,2,add H at position 1,flow_matching,0.3,2.0,49,205
19,replace,1.0,S,H,cH,cS,2,replace H at position 1 with S,flow_matching,0.3,2.0,49,205
20,add,2.0,7,,cS,cS7,3,add 7 at position 2,flow_matching,0.3,2.0,49,205
21,add,3.0,5,,cS7,cS75,4,add 5 at position 3,flow_matching,0.3,2.0,49,205
22,replace,2.0,5,7,cS75,cS55,4,replace 7 at position 2 with 5,flow_matching,0.3,2.0,49,205
23,add,1.0,B,,cS55,cBS55,5,add B at position 1,flow_matching,0.3,2.0,49,205
24,replace,3.0,O,5,cBS55,cBSO5,5,replace 5 at position 3 with O,flow_matching,0.3,2.0,49,205
25,replace,0.0,O,c,cBSO5,OBSO5,5,replace c at position 0 with O,flow_matching,0.3,2.0,49,205
26,remove,0.0,O,,OBSO5,BSO5,4,remove O from position 0,flow_matching,0.3,2.0,49,205
27,add,4.0,l,,BSO5,BSO5l,5,add l at position 4,flow_matching,0.3,2.0,49,205
28,remove,0.0,B,,BSO5l,SO5l,4,remove B from position 0,flow_matching,0.3,2.0,49,205
29,add,4.0,[,,SO5l,SO5l[,5,add [ at position 4,flow_matching,0.3,2.0,49,205
30,add,1.0,-,,SO5l[,S-O5l[,6,add - at position 1,flow_matching,0.3,2.0,49,205
31,add,3.0,7,,S-O5l[,S-O75l[,7,add 7 at position 3,flow_matching,0.3,2.0,49,205
32,replace,6.0,5,[,S-O75l[,S-O75l5,7,replace [ at position 6 with 5,flow_matching,0.3,2.0,49,205
33,add,4.0,o,,S-O75l5,S-O7o5l5,8,add o at position 4,flow_matching,0.3,2.0,49,205
34,replace,0.0,O,S,S-O7o5l5,O-O7o5l5,8,replace S at position 0 with O,flow_matching,0.3,2.0,49,205
35,remove,0.0,O,,O-O7o5l5,-O7o5l5,7,remove O from position 0,flow_matching,0.3,2.0,49,205
36,replace,0.0,O,-,-O7o5l5,OO7o5l5,7,replace - at position 0 with O,flow_matching,0.3,2.0,49,205
37,replace,1.0,c,O,OO7o5l5,Oc7o5l5,7,replace O at position 1 with c,flow_matching,0.3,2.0,49,205
38,replace,2.0,1,7,Oc7o5l5,Oc1o5l5,7,replace 7 at position 2 with 1,flow_matching,0.3,2.0,49,205
39,add,0.0,7,,Oc1o5l5,7Oc1o5l5,8,add 7 at position 0,flow_matching,0.3,2.0,49,205
40,remove,0.0,7,,7Oc1o5l5,Oc1o5l5,7,remove 7 from position 0,flow_matching,0.3,2.0,49,205
41,add,4.0,3,,Oc1o5l5,Oc1o35l5,8,add 3 at position 4,flow_matching,0.3,2.0,49,205
42,replace,3.0,6,o,Oc1o35l5,Oc1635l5,8,replace o at position 3 with 6,flow_matching,0.3,2.0,49,205
43,add,2.0,\,,Oc1635l5,Oc\1635l5,9,add \ at position 2,flow_matching,0.3,2.0,49,205
44,replace,1.0,7,c,Oc\1635l5,O7\1635l5,9,replace c at position 1 with 7,flow_matching,0.3,2.0,49,205
45,replace,1.0,c,7,O7\1635l5,Oc\1635l5,9,replace 7 at position 1 with c,flow_matching,0.3,2.0,49,205
46,replace,0.0,1,O,Oc\1635l5,1c\1635l5,9,replace O at position 0 with 1,flow_matching,0.3,2.0,49,205
47,replace,5.0,C,3,1c\1635l5,1c\16C5l5,9,replace 3 at position 5 with C,flow_matching,0.3,2.0,49,205
48,remove,0.0,1,,1c\16C5l5,c\16C5l5,8,remove 1 from position 0,flow_matching,0.3,2.0,49,205
49,replace,0.0,O,c,c\16C5l5,O\16C5l5,8,replace c at position 0 with O,flow_matching,0.3,2.0,49,205
50,add,6.0,B,,O\16C5l5,O\16C5Bl5,9,add B at position 6,flow_matching,0.3,2.0,49,205
51,replace,6.0,#,B,O\16C5Bl5,O\16C5#l5,9,replace B at position 6 with #,flow_matching,0.3,2.0,49,205
52,add,5.0,I,,O\16C5#l5,O\16CI5#l5,10,add I at position 5,flow_matching,0.3,2.0,49,205
53,replace,1.0,c,\,O\16CI5#l5,Oc16CI5#l5,10,replace \ at position 1 with c,flow_matching,0.3,2.0,49,205
54,add,5.0,4,,Oc16CI5#l5,Oc16C4I5#l5,11,add 4 at position 5,flow_matching,0.3,2.0,49,205
55,remove,7.0,5,,Oc16C4I5#l5,Oc16C4I#l5,10,remove 5 from position 7,flow_matching,0.3,2.0,49,205
56,replace,3.0,c,6,Oc16C4I#l5,Oc1cC4I#l5,10,replace 6 at position 3 with c,flow_matching,0.3,2.0,49,205
57,replace,4.0,B,C,Oc1cC4I#l5,Oc1cB4I#l5,10,replace C at position 4 with B,flow_matching,0.3,2.0,49,205
58,add,0.0,4,,Oc1cB4I#l5,4Oc1cB4I#l5,11,add 4 at position 0,flow_matching,0.3,2.0,49,205
59,replace,0.0,O,4,4Oc1cB4I#l5,OOc1cB4I#l5,11,replace 4 at position 0 with O,flow_matching,0.3,2.0,49,205
60,remove,2.0,c,,OOc1cB4I#l5,OO1cB4I#l5,10,remove c from position 2,flow_matching,0.3,2.0,49,205
61,replace,1.0,c,O,OO1cB4I#l5,Oc1cB4I#l5,10,replace O at position 1 with c,flow_matching,0.3,2.0,49,205
62,replace,9.0,1,5,Oc1cB4I#l5,Oc1cB4I#l1,10,replace 5 at position 9 with 1,flow_matching,0.3,2.0,49,205
63,replace,9.0,l,1,Oc1cB4I#l1,Oc1cB4I#ll,10,replace 1 at position 9 with l,flow_matching,0.3,2.0,49,205
64,replace,7.0,[,#,Oc1cB4I#ll,Oc1cB4I[ll,10,replace # at position 7 with [,flow_matching,0.3,2.0,49,205
65,add,7.0,c,,Oc1cB4I[ll,Oc1cB4Ic[ll,11,add c at position 7,flow_matching,0.3,2.0,49,205
66,replace,10.0,#,l,Oc1cB4Ic[ll,Oc1cB4Ic[l#,11,replace l at position 10 with #,flow_matching,0.3,2.0,49,205
67,remove,0.0,O,,Oc1cB4Ic[l#,c1cB4Ic[l#,10,remove O from position 0,flow_matching,0.3,2.0,49,205
68,add,0.0,4,,c1cB4Ic[l#,4c1cB4Ic[l#,11,add 4 at position 0,flow_matching,0.3,2.0,49,205
69,add,8.0,@,,4c1cB4Ic[l#,4c1cB4Ic@[l#,12,add @ at position 8,flow_matching,0.3,2.0,49,205
70,add,12.0,],,4c1cB4Ic@[l#,4c1cB4Ic@[l#],13,add ] at position 12,flow_matching,0.3,2.0,49,205
71,replace,7.0,(,c,4c1cB4Ic@[l#],4c1cB4I(@[l#],13,replace c at position 7 with (,flow_matching,0.3,2.0,49,205
72,replace,4.0,H,B,4c1cB4I(@[l#],4c1cH4I(@[l#],13,replace B at position 4 with H,flow_matching,0.3,2.0,49,205
73,add,0.0,@,,4c1cH4I(@[l#],@4c1cH4I(@[l#],14,add @ at position 0,flow_matching,0.3,2.0,49,205
74,replace,0.0,O,@,@4c1cH4I(@[l#],O4c1cH4I(@[l#],14,replace @ at position 0 with O,flow_matching,0.3,2.0,49,205
75,replace,12.0,H,#,O4c1cH4I(@[l#],O4c1cH4I(@[lH],14,replace # at position 12 with H,flow_matching,0.3,2.0,49,205
76,add,13.0,B,,O4c1cH4I(@[lH],O4c1cH4I(@[lHB],15,add B at position 13,flow_matching,0.3,2.0,49,205
77,replace,7.0,F,I,O4c1cH4I(@[lHB],O4c1cH4F(@[lHB],15,replace I at position 7 with F,flow_matching,0.3,2.0,49,205
78,replace,1.0,c,4,O4c1cH4F(@[lHB],Occ1cH4F(@[lHB],15,replace 4 at position 1 with c,flow_matching,0.3,2.0,49,205
79,add,9.0,B,,Occ1cH4F(@[lHB],Occ1cH4F(B@[lHB],16,add B at position 9,flow_matching,0.3,2.0,49,205
80,replace,2.0,1,c,Occ1cH4F(B@[lHB],Oc11cH4F(B@[lHB],16,replace c at position 2 with 1,flow_matching,0.3,2.0,49,205
81,replace,4.0,F,c,Oc11cH4F(B@[lHB],Oc11FH4F(B@[lHB],16,replace c at position 4 with F,flow_matching,0.3,2.0,49,205
82,add,12.0,N,,Oc11FH4F(B@[lHB],Oc11FH4F(B@[NlHB],17,add N at position 12,flow_matching,0.3,2.0,49,205
83,replace,15.0,o,B,Oc11FH4F(B@[NlHB],Oc11FH4F(B@[NlHo],17,replace B at position 15 with o,flow_matching,0.3,2.0,49,205
84,replace,3.0,c,1,Oc11FH4F(B@[NlHo],Oc1cFH4F(B@[NlHo],17,replace 1 at position 3 with c,flow_matching,0.3,2.0,49,205
85,replace,16.0,-,],Oc1cFH4F(B@[NlHo],Oc1cFH4F(B@[NlHo-,17,replace ] at position 16 with -,flow_matching,0.3,2.0,49,205
86,remove,16.0,-,,Oc1cFH4F(B@[NlHo-,Oc1cFH4F(B@[NlHo,16,remove - from position 16,flow_matching,0.3,2.0,49,205
87,replace,4.0,c,F,Oc1cFH4F(B@[NlHo,Oc1ccH4F(B@[NlHo,16,replace F at position 4 with c,flow_matching,0.3,2.0,49,205
88,add,11.0,S,,Oc1ccH4F(B@[NlHo,Oc1ccH4F(B@S[NlHo,17,add S at position 11,flow_matching,0.3,2.0,49,205
89,add,1.0,+,,Oc1ccH4F(B@S[NlHo,O+c1ccH4F(B@S[NlHo,18,add + at position 1,flow_matching,0.3,2.0,49,205
90,add,6.0,[,,O+c1ccH4F(B@S[NlHo,O+c1cc[H4F(B@S[NlHo,19,add [ at position 6,flow_matching,0.3,2.0,49,205
91,add,14.0,#,,O+c1cc[H4F(B@S[NlHo,O+c1cc[H4F(B@S#[NlHo,20,add # at position 14,flow_matching,0.3,2.0,49,205
92,replace,1.0,c,+,O+c1cc[H4F(B@S#[NlHo,Occ1cc[H4F(B@S#[NlHo,20,replace + at position 1 with c,flow_matching,0.3,2.0,49,205
93,add,19.0,),,Occ1cc[H4F(B@S#[NlHo,Occ1cc[H4F(B@S#[NlH)o,21,add ) at position 19,flow_matching,0.3,2.0,49,205
94,add,10.0,H,,Occ1cc[H4F(B@S#[NlH)o,Occ1cc[H4FH(B@S#[NlH)o,22,add H at position 10,flow_matching,0.3,2.0,49,205
95,add,18.0,1,,Occ1cc[H4FH(B@S#[NlH)o,Occ1cc[H4FH(B@S#[N1lH)o,23,add 1 at position 18,flow_matching,0.3,2.0,49,205
96,replace,2.0,1,c,Occ1cc[H4FH(B@S#[N1lH)o,Oc11cc[H4FH(B@S#[N1lH)o,23,replace c at position 2 with 1,flow_matching,0.3,2.0,49,205
97,remove,17.0,N,,Oc11cc[H4FH(B@S#[N1lH)o,Oc11cc[H4FH(B@S#[1lH)o,22,remove N from position 17,flow_matching,0.3,2.0,49,205
98,replace,3.0,c,1,Oc11cc[H4FH(B@S#[1lH)o,Oc1ccc[H4FH(B@S#[1lH)o,22,replace 1 at position 3 with c,flow_matching,0.3,2.0,49,205
99,replace,6.0,c,[,Oc1ccc[H4FH(B@S#[1lH)o,Oc1ccccH4FH(B@S#[1lH)o,22,replace [ at position 6 with c,flow_matching,0.3,2.0,49,205
100,add,15.0,+,,Oc1ccccH4FH(B@S#[1lH)o,Oc1ccccH4FH(B@S+#[1lH)o,23,add + at position 15,flow_matching,0.3,2.0,49,205
101,add,14.0,@,,Oc1ccccH4FH(B@S+#[1lH)o,Oc1ccccH4FH(B@@S+#[1lH)o,24,add @ at position 14,flow_matching,0.3,2.0,49,205
102,add,12.0,7,,Oc1ccccH4FH(B@@S+#[1lH)o,Oc1ccccH4FH(7B@@S+#[1lH)o,25,add 7 at position 12,flow_matching,0.3,2.0,49,205
103,replace,10.0,N,H,Oc1ccccH4FH(7B@@S+#[1lH)o,Oc1ccccH4FN(7B@@S+#[1lH)o,25,replace H at position 10 with N,flow_matching,0.3,2.0,49,205
104,add,2.0,3,,Oc1ccccH4FN(7B@@S+#[1lH)o,Oc31ccccH4FN(7B@@S+#[1lH)o,26,add 3 at position 2,flow_matching,0.3,2.0,49,205
105,remove,1.0,c,,Oc31ccccH4FN(7B@@S+#[1lH)o,O31ccccH4FN(7B@@S+#[1lH)o,25,remove c from position 1,flow_matching,0.3,2.0,49,205
106,add,3.0,7,,O31ccccH4FN(7B@@S+#[1lH)o,O317ccccH4FN(7B@@S+#[1lH)o,26,add 7 at position 3,flow_matching,0.3,2.0,49,205
107,remove,7.0,c,,O317ccccH4FN(7B@@S+#[1lH)o,O317cccH4FN(7B@@S+#[1lH)o,25,remove c from position 7,flow_matching,0.3,2.0,49,205
108,replace,1.0,c,3,O317cccH4FN(7B@@S+#[1lH)o,Oc17cccH4FN(7B@@S+#[1lH)o,25,replace 3 at position 1 with c,flow_matching,0.3,2.0,49,205
109,remove,24.0,o,,Oc17cccH4FN(7B@@S+#[1lH)o,Oc17cccH4FN(7B@@S+#[1lH),24,remove o from position 24,flow_matching,0.3,2.0,49,205
110,replace,3.0,c,7,Oc17cccH4FN(7B@@S+#[1lH),Oc1ccccH4FN(7B@@S+#[1lH),24,replace 7 at position 3 with c,flow_matching,0.3,2.0,49,205
111,remove,20.0,1,,Oc1ccccH4FN(7B@@S+#[1lH),Oc1ccccH4FN(7B@@S+#[lH),23,remove 1 from position 20,flow_matching,0.3,2.0,49,205
112,replace,7.0,(,H,Oc1ccccH4FN(7B@@S+#[lH),Oc1cccc(4FN(7B@@S+#[lH),23,replace H at position 7 with (,flow_matching,0.3,2.0,49,205
113,replace,8.0,[,4,Oc1cccc(4FN(7B@@S+#[lH),Oc1cccc([FN(7B@@S+#[lH),23,replace 4 at position 8 with [,flow_matching,0.3,2.0,49,205
114,replace,9.0,C,F,Oc1cccc([FN(7B@@S+#[lH),Oc1cccc([CN(7B@@S+#[lH),23,replace F at position 9 with C,flow_matching,0.3,2.0,49,205
115,remove,16.0,S,,Oc1cccc([CN(7B@@S+#[lH),Oc1cccc([CN(7B@@+#[lH),22,remove S from position 16,flow_matching,0.3,2.0,49,205
116,replace,10.0,@,N,Oc1cccc([CN(7B@@+#[lH),Oc1cccc([C@(7B@@+#[lH),22,replace N at position 10 with @,flow_matching,0.3,2.0,49,205
117,replace,21.0,\,),Oc1cccc([C@(7B@@+#[lH),Oc1cccc([C@(7B@@+#[lH\,22,replace ) at position 21 with \,flow_matching,0.3,2.0,49,205
118,replace,11.0,@,(,Oc1cccc([C@(7B@@+#[lH\,Oc1cccc([C@@7B@@+#[lH\,22,replace ( at position 11 with @,flow_matching,0.3,2.0,49,205
119,replace,8.0,n,[,Oc1cccc([C@@7B@@+#[lH\,Oc1cccc(nC@@7B@@+#[lH\,22,replace [ at position 8 with n,flow_matching,0.3,2.0,49,205
120,add,18.0,],,Oc1cccc(nC@@7B@@+#[lH\,Oc1cccc(nC@@7B@@+#][lH\,23,add ] at position 18,flow_matching,0.3,2.0,49,205
121,replace,8.0,[,n,Oc1cccc(nC@@7B@@+#][lH\,Oc1cccc([C@@7B@@+#][lH\,23,replace n at position 8 with [,flow_matching,0.3,2.0,49,205
122,remove,10.0,@,,Oc1cccc([C@@7B@@+#][lH\,Oc1cccc([C@7B@@+#][lH\,22,remove @ from position 10,flow_matching,0.3,2.0,49,205
123,add,20.0,r,,Oc1cccc([C@7B@@+#][lH\,Oc1cccc([C@7B@@+#][lrH\,23,add r at position 20,flow_matching,0.3,2.0,49,205
124,remove,4.0,c,,Oc1cccc([C@7B@@+#][lrH\,Oc1ccc([C@7B@@+#][lrH\,22,remove c from position 4,flow_matching,0.3,2.0,49,205
125,replace,19.0,S,r,Oc1ccc([C@7B@@+#][lrH\,Oc1ccc([C@7B@@+#][lSH\,22,replace r at position 19 with S,flow_matching,0.3,2.0,49,205
126,replace,16.0,@,],Oc1ccc([C@7B@@+#][lSH\,Oc1ccc([C@7B@@+#@[lSH\,22,replace ] at position 16 with @,flow_matching,0.3,2.0,49,205
127,replace,6.0,c,(,Oc1ccc([C@7B@@+#@[lSH\,Oc1cccc[C@7B@@+#@[lSH\,22,replace ( at position 6 with c,flow_matching,0.3,2.0,49,205
128,replace,7.0,(,[,Oc1cccc[C@7B@@+#@[lSH\,Oc1cccc(C@7B@@+#@[lSH\,22,replace [ at position 7 with (,flow_matching,0.3,2.0,49,205
129,replace,8.0,[,C,Oc1cccc(C@7B@@+#@[lSH\,Oc1cccc([@7B@@+#@[lSH\,22,replace C at position 8 with [,flow_matching,0.3,2.0,49,205
130,add,16.0,2,,Oc1cccc([@7B@@+#@[lSH\,Oc1cccc([@7B@@+#2@[lSH\,23,add 2 at position 16,flow_matching,0.3,2.0,49,205
131,replace,9.0,C,@,Oc1cccc([@7B@@+#2@[lSH\,Oc1cccc([C7B@@+#2@[lSH\,23,replace @ at position 9 with C,flow_matching,0.3,2.0,49,205
132,replace,16.0,s,2,Oc1cccc([C7B@@+#2@[lSH\,Oc1cccc([C7B@@+#s@[lSH\,23,replace 2 at position 16 with s,flow_matching,0.3,2.0,49,205
133,replace,10.0,@,7,Oc1cccc([C7B@@+#s@[lSH\,Oc1cccc([C@B@@+#s@[lSH\,23,replace 7 at position 10 with @,flow_matching,0.3,2.0,49,205
134,add,21.0,l,,Oc1cccc([C@B@@+#s@[lSH\,Oc1cccc([C@B@@+#s@[lSlH\,24,add l at position 21,flow_matching,0.3,2.0,49,205
135,remove,23.0,\,,Oc1cccc([C@B@@+#s@[lSlH\,Oc1cccc([C@B@@+#s@[lSlH,23,remove \ from position 23,flow_matching,0.3,2.0,49,205
136,add,9.0,N,,Oc1cccc([C@B@@+#s@[lSlH,Oc1cccc([NC@B@@+#s@[lSlH,24,add N at position 9,flow_matching,0.3,2.0,49,205
137,add,13.0,H,,Oc1cccc([NC@B@@+#s@[lSlH,Oc1cccc([NC@BH@@+#s@[lSlH,25,add H at position 13,flow_matching,0.3,2.0,49,205
138,replace,12.0,(,B,Oc1cccc([NC@BH@@+#s@[lSlH,Oc1cccc([NC@(H@@+#s@[lSlH,25,replace B at position 12 with (,flow_matching,0.3,2.0,49,205
139,replace,9.0,C,N,Oc1cccc([NC@(H@@+#s@[lSlH,Oc1cccc([CC@(H@@+#s@[lSlH,25,replace N at position 9 with C,flow_matching,0.3,2.0,49,205
140,add,22.0,l,,Oc1cccc([CC@(H@@+#s@[lSlH,Oc1cccc([CC@(H@@+#s@[llSlH,26,add l at position 22,flow_matching,0.3,2.0,49,205
141,replace,10.0,@,C,Oc1cccc([CC@(H@@+#s@[llSlH,Oc1cccc([C@@(H@@+#s@[llSlH,26,replace C at position 10 with @,flow_matching,0.3,2.0,49,205
142,remove,7.0,(,,Oc1cccc([C@@(H@@+#s@[llSlH,Oc1cccc[C@@(H@@+#s@[llSlH,25,remove ( from position 7,flow_matching,0.3,2.0,49,205
143,replace,23.0,6,l,Oc1cccc[C@@(H@@+#s@[llSlH,Oc1cccc[C@@(H@@+#s@[llS6H,25,replace l at position 23 with 6,flow_matching,0.3,2.0,49,205
144,add,21.0,6,,Oc1cccc[C@@(H@@+#s@[llS6H,Oc1cccc[C@@(H@@+#s@[l6lS6H,26,add 6 at position 21,flow_matching,0.3,2.0,49,205
145,add,5.0,4,,Oc1cccc[C@@(H@@+#s@[l6lS6H,Oc1cc4cc[C@@(H@@+#s@[l6lS6H,27,add 4 at position 5,flow_matching,0.3,2.0,49,205
146,add,14.0,O,,Oc1cc4cc[C@@(H@@+#s@[l6lS6H,Oc1cc4cc[C@@(HO@@+#s@[l6lS6H,28,add O at position 14,flow_matching,0.3,2.0,49,205
147,remove,9.0,C,,Oc1cc4cc[C@@(HO@@+#s@[l6lS6H,Oc1cc4cc[@@(HO@@+#s@[l6lS6H,27,remove C from position 9,flow_matching,0.3,2.0,49,205
148,add,10.0,+,,Oc1cc4cc[@@(HO@@+#s@[l6lS6H,Oc1cc4cc[@+@(HO@@+#s@[l6lS6H,28,add + at position 10,flow_matching,0.3,2.0,49,205
149,replace,5.0,c,4,Oc1cc4cc[@+@(HO@@+#s@[l6lS6H,Oc1ccccc[@+@(HO@@+#s@[l6lS6H,28,replace 4 at position 5 with c,flow_matching,0.3,2.0,49,205
150,add,3.0,3,,Oc1ccccc[@+@(HO@@+#s@[l6lS6H,Oc13ccccc[@+@(HO@@+#s@[l6lS6H,29,add 3 at position 3,flow_matching,0.3,2.0,49,205
151,remove,8.0,c,,Oc13ccccc[@+@(HO@@+#s@[l6lS6H,Oc13cccc[@+@(HO@@+#s@[l6lS6H,28,remove c from position 8,flow_matching,0.3,2.0,49,205
152,remove,21.0,[,,Oc13cccc[@+@(HO@@+#s@[l6lS6H,Oc13cccc[@+@(HO@@+#s@l6lS6H,27,remove [ from position 21,flow_matching,0.3,2.0,49,205
153,remove,19.0,s,,Oc13cccc[@+@(HO@@+#s@l6lS6H,Oc13cccc[@+@(HO@@+#@l6lS6H,26,remove s from position 19,flow_matching,0.3,2.0,49,205
154,replace,3.0,c,3,Oc13cccc[@+@(HO@@+#@l6lS6H,Oc1ccccc[@+@(HO@@+#@l6lS6H,26,replace 3 at position 3 with c,flow_matching,0.3,2.0,49,205
155,replace,23.0,s,S,Oc1ccccc[@+@(HO@@+#@l6lS6H,Oc1ccccc[@+@(HO@@+#@l6ls6H,26,replace S at position 23 with s,flow_matching,0.3,2.0,49,205
156,add,13.0,4,,Oc1ccccc[@+@(HO@@+#@l6ls6H,Oc1ccccc[@+@(4HO@@+#@l6ls6H,27,add 4 at position 13,flow_matching,0.3,2.0,49,205
157,replace,18.0,[,+,Oc1ccccc[@+@(4HO@@+#@l6ls6H,Oc1ccccc[@+@(4HO@@[#@l6ls6H,27,replace + at position 18 with [,flow_matching,0.3,2.0,49,205
158,remove,6.0,c,,Oc1ccccc[@+@(4HO@@[#@l6ls6H,Oc1cccc[@+@(4HO@@[#@l6ls6H,26,remove c from position 6,flow_matching,0.3,2.0,49,205
159,replace,11.0,N,(,Oc1cccc[@+@(4HO@@[#@l6ls6H,Oc1cccc[@+@N4HO@@[#@l6ls6H,26,replace ( at position 11 with N,flow_matching,0.3,2.0,49,205
160,add,6.0,\,,Oc1cccc[@+@N4HO@@[#@l6ls6H,Oc1ccc\c[@+@N4HO@@[#@l6ls6H,27,add \ at position 6,flow_matching,0.3,2.0,49,205
161,replace,0.0,/,O,Oc1ccc\c[@+@N4HO@@[#@l6ls6H,/c1ccc\c[@+@N4HO@@[#@l6ls6H,27,replace O at position 0 with /,flow_matching,0.3,2.0,49,205
162,remove,18.0,[,,/c1ccc\c[@+@N4HO@@[#@l6ls6H,/c1ccc\c[@+@N4HO@@#@l6ls6H,26,remove [ from position 18,flow_matching,0.3,2.0,49,205
163,remove,14.0,H,,/c1ccc\c[@+@N4HO@@#@l6ls6H,/c1ccc\c[@+@N4O@@#@l6ls6H,25,remove H from position 14,flow_matching,0.3,2.0,49,205
164,replace,0.0,O,/,/c1ccc\c[@+@N4O@@#@l6ls6H,Oc1ccc\c[@+@N4O@@#@l6ls6H,25,replace / at position 0 with O,flow_matching,0.3,2.0,49,205
165,replace,6.0,c,\,Oc1ccc\c[@+@N4O@@#@l6ls6H,Oc1ccccc[@+@N4O@@#@l6ls6H,25,replace \ at position 6 with c,flow_matching,0.3,2.0,49,205
166,replace,7.0,(,c,Oc1ccccc[@+@N4O@@#@l6ls6H,Oc1cccc([@+@N4O@@#@l6ls6H,25,replace c at position 7 with (,flow_matching,0.3,2.0,49,205
167,replace,9.0,C,@,Oc1cccc([@+@N4O@@#@l6ls6H,Oc1cccc([C+@N4O@@#@l6ls6H,25,replace @ at position 9 with C,flow_matching,0.3,2.0,49,205
168,replace,10.0,@,+,Oc1cccc([C+@N4O@@#@l6ls6H,Oc1cccc([C@@N4O@@#@l6ls6H,25,replace + at position 10 with @,flow_matching,0.3,2.0,49,205
169,replace,12.0,H,N,Oc1cccc([C@@N4O@@#@l6ls6H,Oc1cccc([C@@H4O@@#@l6ls6H,25,replace N at position 12 with H,flow_matching,0.3,2.0,49,205
170,replace,13.0,],4,Oc1cccc([C@@H4O@@#@l6ls6H,Oc1cccc([C@@H]O@@#@l6ls6H,25,replace 4 at position 13 with ],flow_matching,0.3,2.0,49,205
171,replace,14.0,2,O,Oc1cccc([C@@H]O@@#@l6ls6H,Oc1cccc([C@@H]2@@#@l6ls6H,25,replace O at position 14 with 2,flow_matching,0.3,2.0,49,205
172,replace,15.0,C,@,Oc1cccc([C@@H]2@@#@l6ls6H,Oc1cccc([C@@H]2C@#@l6ls6H,25,replace @ at position 15 with C,flow_matching,0.3,2.0,49,205
173,replace,16.0,N,@,Oc1cccc([C@@H]2C@#@l6ls6H,Oc1cccc([C@@H]2CN#@l6ls6H,25,replace @ at position 16 with N,flow_matching,0.3,2.0,49,205
174,replace,17.0,(,#,Oc1cccc([C@@H]2CN#@l6ls6H,Oc1cccc([C@@H]2CN(@l6ls6H,25,replace # at position 17 with (,flow_matching,0.3,2.0,49,205
175,replace,18.0,c,@,Oc1cccc([C@@H]2CN(@l6ls6H,Oc1cccc([C@@H]2CN(cl6ls6H,25,replace @ at position 18 with c,flow_matching,0.3,2.0,49,205
176,replace,19.0,3,l,Oc1cccc([C@@H]2CN(cl6ls6H,Oc1cccc([C@@H]2CN(c36ls6H,25,replace l at position 19 with 3,flow_matching,0.3,2.0,49,205
177,replace,20.0,n,6,Oc1cccc([C@@H]2CN(c36ls6H,Oc1cccc([C@@H]2CN(c3nls6H,25,replace 6 at position 20 with n,flow_matching,0.3,2.0,49,205
178,replace,21.0,c,l,Oc1cccc([C@@H]2CN(c3nls6H,Oc1cccc([C@@H]2CN(c3ncs6H,25,replace l at position 21 with c,flow_matching,0.3,2.0,49,205
179,replace,22.0,c,s,Oc1cccc([C@@H]2CN(c3ncs6H,Oc1cccc([C@@H]2CN(c3ncc6H,25,replace s at position 22 with c,flow_matching,0.3,2.0,49,205
180,replace,23.0,c,6,Oc1cccc([C@@H]2CN(c3ncc6H,Oc1cccc([C@@H]2CN(c3ncccH,25,replace 6 at position 23 with c,flow_matching,0.3,2.0,49,205
181,replace,24.0,(,H,Oc1cccc([C@@H]2CN(c3ncccH,Oc1cccc([C@@H]2CN(c3nccc(,25,replace H at position 24 with (,flow_matching,0.3,2.0,49,205
182,add,25.0,O,,Oc1cccc([C@@H]2CN(c3nccc(,Oc1cccc([C@@H]2CN(c3nccc(O,26,add O at position 25,flow_matching,0.3,2.0,49,205
183,add,26.0,c,,Oc1cccc([C@@H]2CN(c3nccc(O,Oc1cccc([C@@H]2CN(c3nccc(Oc,27,add c at position 26,flow_matching,0.3,2.0,49,205
184,add,27.0,4,,Oc1cccc([C@@H]2CN(c3nccc(Oc,Oc1cccc([C@@H]2CN(c3nccc(Oc4,28,add 4 at position 27,flow_matching,0.3,2.0,49,205
185,add,28.0,c,,Oc1cccc([C@@H]2CN(c3nccc(Oc4,Oc1cccc([C@@H]2CN(c3nccc(Oc4c,29,add c at position 28,flow_matching,0.3,2.0,49,205
186,add,29.0,c,,Oc1cccc([C@@H]2CN(c3nccc(Oc4c,Oc1cccc([C@@H]2CN(c3nccc(Oc4cc,30,add c at position 29,flow_matching,0.3,2.0,49,205
187,add,30.0,c,,Oc1cccc([C@@H]2CN(c3nccc(Oc4cc,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc,31,add c at position 30,flow_matching,0.3,2.0,49,205
188,add,31.0,(,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(,32,add ( at position 31,flow_matching,0.3,2.0,49,205
189,add,32.0,F,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F,33,add F at position 32,flow_matching,0.3,2.0,49,205
190,add,33.0,),,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F),34,add ) at position 33,flow_matching,0.3,2.0,49,205
191,add,34.0,c,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F),Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)c,35,add c at position 34,flow_matching,0.3,2.0,49,205
192,add,35.0,c,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)c,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc,36,add c at position 35,flow_matching,0.3,2.0,49,205
193,add,36.0,4,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4,37,add 4 at position 36,flow_matching,0.3,2.0,49,205
194,add,37.0,),,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4),38,add ) at position 37,flow_matching,0.3,2.0,49,205
195,add,38.0,n,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4),Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n,39,add n at position 38,flow_matching,0.3,2.0,49,205
196,add,39.0,3,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3,40,add 3 at position 39,flow_matching,0.3,2.0,49,205
197,add,40.0,),,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3),41,add ) at position 40,flow_matching,0.3,2.0,49,205
198,add,41.0,C,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3),Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)C,42,add C at position 41,flow_matching,0.3,2.0,49,205
199,add,42.0,C,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)C,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CC,43,add C at position 42,flow_matching,0.3,2.0,49,205
200,add,43.0,O,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CC,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO,44,add O at position 43,flow_matching,0.3,2.0,49,205
201,add,44.0,2,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO2,45,add 2 at position 44,flow_matching,0.3,2.0,49,205
202,add,45.0,),,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO2,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO2),46,add ) at position 45,flow_matching,0.3,2.0,49,205
203,add,46.0,c,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO2),Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO2)c,47,add c at position 46,flow_matching,0.3,2.0,49,205
204,add,47.0,1,,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO2)c,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO2)c1,48,add 1 at position 47,flow_matching,0.3,2.0,49,205
205,add,48.0,"
",,Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO2)c1,"Oc1cccc([C@@H]2CN(c3nccc(Oc4ccc(F)cc4)n3)CCO2)c1
",49,"add 
 at position 48",flow_matching,0.3,2.0,49,205
