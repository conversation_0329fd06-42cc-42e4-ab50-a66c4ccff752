step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,47,110
1,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,47,110
2,replace,0.0,C,I,I,C,1,replace I at position 0 with C,flow_matching,0.3,2.0,47,110
3,replace,0.0,2,C,C,2,1,replace <PERSON> at position 0 with 2,flow_matching,0.3,2.0,47,110
4,add,1.0,C,,2,2C,2,add C at position 1,flow_matching,0.3,2.0,47,110
5,remove,0.0,2,,2C,C,1,remove 2 from position 0,flow_matching,0.3,2.0,47,110
6,add,0.0,3,,C,3C,2,add 3 at position 0,flow_matching,0.3,2.0,47,110
7,replace,0.0,[,3,3C,[C,2,replace 3 at position 0 with [,flow_matching,0.3,2.0,47,110
8,add,2.0,),,[C,[C),3,add ) at position 2,flow_matching,0.3,2.0,47,110
9,add,1.0,=,,[C),[=C),4,add = at position 1,flow_matching,0.3,2.0,47,110
10,replace,1.0,],=,[=C),[]C),4,replace = at position 1 with ],flow_matching,0.3,2.0,47,110
11,replace,0.0,C,[,[]C),C]C),4,replace [ at position 0 with C,flow_matching,0.3,2.0,47,110
12,add,0.0,[,,C]C),[C]C),5,add [ at position 0,flow_matching,0.3,2.0,47,110
13,add,5.0,N,,[C]C),[C]C)N,6,add N at position 5,flow_matching,0.3,2.0,47,110
14,replace,2.0,l,],[C]C)N,[ClC)N,6,replace ] at position 2 with l,flow_matching,0.3,2.0,47,110
15,replace,5.0,(,N,[ClC)N,[ClC)(,6,replace N at position 5 with (,flow_matching,0.3,2.0,47,110
16,add,0.0,H,,[ClC)(,H[ClC)(,7,add H at position 0,flow_matching,0.3,2.0,47,110
17,replace,3.0,],l,H[ClC)(,H[C]C)(,7,replace l at position 3 with ],flow_matching,0.3,2.0,47,110
18,remove,4.0,C,,H[C]C)(,H[C])(,6,remove C from position 4,flow_matching,0.3,2.0,47,110
19,replace,4.0,l,),H[C])(,H[C]l(,6,replace ) at position 4 with l,flow_matching,0.3,2.0,47,110
20,replace,0.0,C,H,H[C]l(,C[C]l(,6,replace H at position 0 with C,flow_matching,0.3,2.0,47,110
21,add,5.0,N,,C[C]l(,C[C]lN(,7,add N at position 5,flow_matching,0.3,2.0,47,110
22,remove,6.0,(,,C[C]lN(,C[C]lN,6,remove ( from position 6,flow_matching,0.3,2.0,47,110
23,replace,0.0,N,C,C[C]lN,N[C]lN,6,replace C at position 0 with N,flow_matching,0.3,2.0,47,110
24,replace,0.0,C,N,N[C]lN,C[C]lN,6,replace N at position 0 with C,flow_matching,0.3,2.0,47,110
25,replace,1.0,O,[,C[C]lN,COC]lN,6,replace [ at position 1 with O,flow_matching,0.3,2.0,47,110
26,replace,2.0,c,C,COC]lN,COc]lN,6,replace C at position 2 with c,flow_matching,0.3,2.0,47,110
27,replace,0.0,B,C,COc]lN,BOc]lN,6,replace C at position 0 with B,flow_matching,0.3,2.0,47,110
28,remove,5.0,N,,BOc]lN,BOc]l,5,remove N from position 5,flow_matching,0.3,2.0,47,110
29,remove,0.0,B,,BOc]l,Oc]l,4,remove B from position 0,flow_matching,0.3,2.0,47,110
30,remove,1.0,c,,Oc]l,O]l,3,remove c from position 1,flow_matching,0.3,2.0,47,110
31,replace,0.0,C,O,O]l,C]l,3,replace O at position 0 with C,flow_matching,0.3,2.0,47,110
32,replace,1.0,O,],C]l,COl,3,replace ] at position 1 with O,flow_matching,0.3,2.0,47,110
33,replace,2.0,7,l,COl,CO7,3,replace l at position 2 with 7,flow_matching,0.3,2.0,47,110
34,add,1.0,F,,CO7,CFO7,4,add F at position 1,flow_matching,0.3,2.0,47,110
35,replace,1.0,O,F,CFO7,COO7,4,replace F at position 1 with O,flow_matching,0.3,2.0,47,110
36,replace,2.0,c,O,COO7,COc7,4,replace O at position 2 with c,flow_matching,0.3,2.0,47,110
37,add,3.0,B,,COc7,COcB7,5,add B at position 3,flow_matching,0.3,2.0,47,110
38,replace,2.0,s,c,COcB7,COsB7,5,replace c at position 2 with s,flow_matching,0.3,2.0,47,110
39,replace,2.0,c,s,COsB7,COcB7,5,replace s at position 2 with c,flow_matching,0.3,2.0,47,110
40,remove,4.0,7,,COcB7,COcB,4,remove 7 from position 4,flow_matching,0.3,2.0,47,110
41,remove,3.0,B,,COcB,COc,3,remove B from position 3,flow_matching,0.3,2.0,47,110
42,remove,2.0,c,,COc,CO,2,remove c from position 2,flow_matching,0.3,2.0,47,110
43,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,47,110
44,add,0.0,@,,COc,@COc,4,add @ at position 0,flow_matching,0.3,2.0,47,110
45,replace,0.0,C,@,@COc,CCOc,4,replace @ at position 0 with C,flow_matching,0.3,2.0,47,110
46,replace,1.0,O,C,CCOc,COOc,4,replace C at position 1 with O,flow_matching,0.3,2.0,47,110
47,replace,2.0,c,O,COOc,COcc,4,replace O at position 2 with c,flow_matching,0.3,2.0,47,110
48,replace,3.0,1,c,COcc,COc1,4,replace c at position 3 with 1,flow_matching,0.3,2.0,47,110
49,remove,2.0,c,,COc1,CO1,3,remove c from position 2,flow_matching,0.3,2.0,47,110
50,replace,1.0,o,O,CO1,Co1,3,replace O at position 1 with o,flow_matching,0.3,2.0,47,110
51,replace,1.0,\,o,Co1,C\1,3,replace o at position 1 with \,flow_matching,0.3,2.0,47,110
52,replace,1.0,O,\,C\1,CO1,3,replace \ at position 1 with O,flow_matching,0.3,2.0,47,110
53,replace,2.0,#,1,CO1,CO#,3,replace 1 at position 2 with #,flow_matching,0.3,2.0,47,110
54,replace,2.0,=,#,CO#,CO=,3,replace # at position 2 with =,flow_matching,0.3,2.0,47,110
55,replace,1.0,=,O,CO=,C==,3,replace O at position 1 with =,flow_matching,0.3,2.0,47,110
56,replace,1.0,O,=,C==,CO=,3,replace = at position 1 with O,flow_matching,0.3,2.0,47,110
57,replace,2.0,r,=,CO=,COr,3,replace = at position 2 with r,flow_matching,0.3,2.0,47,110
58,replace,1.0,c,O,COr,Ccr,3,replace O at position 1 with c,flow_matching,0.3,2.0,47,110
59,replace,2.0,O,r,Ccr,CcO,3,replace r at position 2 with O,flow_matching,0.3,2.0,47,110
60,add,2.0,o,,CcO,CcoO,4,add o at position 2,flow_matching,0.3,2.0,47,110
61,remove,1.0,c,,CcoO,CoO,3,remove c from position 1,flow_matching,0.3,2.0,47,110
62,remove,1.0,o,,CoO,CO,2,remove o from position 1,flow_matching,0.3,2.0,47,110
63,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,47,110
64,replace,0.0,C,O,O,C,1,replace O at position 0 with C,flow_matching,0.3,2.0,47,110
65,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,47,110
66,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,47,110
67,add,3.0,1,,COc,COc1,4,add 1 at position 3,flow_matching,0.3,2.0,47,110
68,add,4.0,c,,COc1,COc1c,5,add c at position 4,flow_matching,0.3,2.0,47,110
69,add,5.0,c,,COc1c,COc1cc,6,add c at position 5,flow_matching,0.3,2.0,47,110
70,add,6.0,c,,COc1cc,COc1ccc,7,add c at position 6,flow_matching,0.3,2.0,47,110
71,add,7.0,c,,COc1ccc,COc1cccc,8,add c at position 7,flow_matching,0.3,2.0,47,110
72,add,8.0,c,,COc1cccc,COc1ccccc,9,add c at position 8,flow_matching,0.3,2.0,47,110
73,add,9.0,1,,COc1ccccc,COc1ccccc1,10,add 1 at position 9,flow_matching,0.3,2.0,47,110
74,add,10.0,[,,COc1ccccc1,COc1ccccc1[,11,add [ at position 10,flow_matching,0.3,2.0,47,110
75,add,11.0,C,,COc1ccccc1[,COc1ccccc1[C,12,add C at position 11,flow_matching,0.3,2.0,47,110
76,add,12.0,@,,COc1ccccc1[C,COc1ccccc1[C@,13,add @ at position 12,flow_matching,0.3,2.0,47,110
77,add,13.0,H,,COc1ccccc1[C@,COc1ccccc1[C@H,14,add H at position 13,flow_matching,0.3,2.0,47,110
78,add,14.0,],,COc1ccccc1[C@H,COc1ccccc1[C@H],15,add ] at position 14,flow_matching,0.3,2.0,47,110
79,add,15.0,1,,COc1ccccc1[C@H],COc1ccccc1[C@H]1,16,add 1 at position 15,flow_matching,0.3,2.0,47,110
80,add,16.0,C,,COc1ccccc1[C@H]1,COc1ccccc1[C@H]1C,17,add C at position 16,flow_matching,0.3,2.0,47,110
81,add,17.0,C,,COc1ccccc1[C@H]1C,COc1ccccc1[C@H]1CC,18,add C at position 17,flow_matching,0.3,2.0,47,110
82,add,18.0,C,,COc1ccccc1[C@H]1CC,COc1ccccc1[C@H]1CCC,19,add C at position 18,flow_matching,0.3,2.0,47,110
83,add,19.0,N,,COc1ccccc1[C@H]1CCC,COc1ccccc1[C@H]1CCCN,20,add N at position 19,flow_matching,0.3,2.0,47,110
84,add,20.0,1,,COc1ccccc1[C@H]1CCCN,COc1ccccc1[C@H]1CCCN1,21,add 1 at position 20,flow_matching,0.3,2.0,47,110
85,add,21.0,C,,COc1ccccc1[C@H]1CCCN1,COc1ccccc1[C@H]1CCCN1C,22,add C at position 21,flow_matching,0.3,2.0,47,110
86,add,22.0,(,,COc1ccccc1[C@H]1CCCN1C,COc1ccccc1[C@H]1CCCN1C(,23,add ( at position 22,flow_matching,0.3,2.0,47,110
87,add,23.0,=,,COc1ccccc1[C@H]1CCCN1C(,COc1ccccc1[C@H]1CCCN1C(=,24,add = at position 23,flow_matching,0.3,2.0,47,110
88,add,24.0,O,,COc1ccccc1[C@H]1CCCN1C(=,COc1ccccc1[C@H]1CCCN1C(=O,25,add O at position 24,flow_matching,0.3,2.0,47,110
89,add,25.0,),,COc1ccccc1[C@H]1CCCN1C(=O,COc1ccccc1[C@H]1CCCN1C(=O),26,add ) at position 25,flow_matching,0.3,2.0,47,110
90,add,26.0,[,,COc1ccccc1[C@H]1CCCN1C(=O),COc1ccccc1[C@H]1CCCN1C(=O)[,27,add [ at position 26,flow_matching,0.3,2.0,47,110
91,add,27.0,C,,COc1ccccc1[C@H]1CCCN1C(=O)[,COc1ccccc1[C@H]1CCCN1C(=O)[C,28,add C at position 27,flow_matching,0.3,2.0,47,110
92,add,28.0,@,,COc1ccccc1[C@H]1CCCN1C(=O)[C,COc1ccccc1[C@H]1CCCN1C(=O)[C@,29,add @ at position 28,flow_matching,0.3,2.0,47,110
93,add,29.0,@,,COc1ccccc1[C@H]1CCCN1C(=O)[C@,COc1ccccc1[C@H]1CCCN1C(=O)[C@@,30,add @ at position 29,flow_matching,0.3,2.0,47,110
94,add,30.0,H,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H,31,add H at position 30,flow_matching,0.3,2.0,47,110
95,add,31.0,],,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H],32,add ] at position 31,flow_matching,0.3,2.0,47,110
96,add,32.0,(,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H],COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](,33,add ( at position 32,flow_matching,0.3,2.0,47,110
97,add,33.0,C,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C,34,add C at position 33,flow_matching,0.3,2.0,47,110
98,add,34.0,),,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C),35,add ) at position 34,flow_matching,0.3,2.0,47,110
99,add,35.0,C,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C),COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)C,36,add C at position 35,flow_matching,0.3,2.0,47,110
100,add,36.0,C,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)C,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CC,37,add C at position 36,flow_matching,0.3,2.0,47,110
101,add,37.0,O,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CC,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCO,38,add O at position 37,flow_matching,0.3,2.0,47,110
102,add,38.0,c,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCO,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc,39,add c at position 38,flow_matching,0.3,2.0,47,110
103,add,39.0,1,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1,40,add 1 at position 39,flow_matching,0.3,2.0,47,110
104,add,40.0,c,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1c,41,add c at position 40,flow_matching,0.3,2.0,47,110
105,add,41.0,c,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1c,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1cc,42,add c at position 41,flow_matching,0.3,2.0,47,110
106,add,42.0,c,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1cc,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1ccc,43,add c at position 42,flow_matching,0.3,2.0,47,110
107,add,43.0,c,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1ccc,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1cccc,44,add c at position 43,flow_matching,0.3,2.0,47,110
108,add,44.0,c,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1cccc,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1ccccc,45,add c at position 44,flow_matching,0.3,2.0,47,110
109,add,45.0,1,,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1ccccc,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1ccccc1,46,add 1 at position 45,flow_matching,0.3,2.0,47,110
110,add,46.0,"
",,COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1ccccc1,"COc1ccccc1[C@H]1CCCN1C(=O)[C@@H](C)CCOc1ccccc1
",47,"add 
 at position 46",flow_matching,0.3,2.0,47,110
