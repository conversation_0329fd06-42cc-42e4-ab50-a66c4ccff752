step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,47,131
1,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,47,131
2,replace,0.0,1,3,3,1,1,replace 3 at position 0 with 1,flow_matching,0.3,2.0,47,131
3,replace,0.0,C,1,1,C,1,replace 1 at position 0 with C,flow_matching,0.3,2.0,47,131
4,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,47,131
5,add,0.0,S,,,S,1,add S at position 0,flow_matching,0.3,2.0,47,131
6,replace,0.0,C,S,S,C,1,replace <PERSON> at position 0 with C,flow_matching,0.3,2.0,47,131
7,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,47,131
8,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,47,131
9,replace,0.0,3,7,7,3,1,replace 7 at position 0 with 3,flow_matching,0.3,2.0,47,131
10,replace,0.0,\,3,3,\,1,replace 3 at position 0 with \,flow_matching,0.3,2.0,47,131
11,replace,0.0,n,\,\,n,1,replace \ at position 0 with n,flow_matching,0.3,2.0,47,131
12,add,0.0,c,,n,cn,2,add c at position 0,flow_matching,0.3,2.0,47,131
13,replace,0.0,B,c,cn,Bn,2,replace c at position 0 with B,flow_matching,0.3,2.0,47,131
14,replace,0.0,C,B,Bn,Cn,2,replace B at position 0 with C,flow_matching,0.3,2.0,47,131
15,remove,0.0,C,,Cn,n,1,remove C from position 0,flow_matching,0.3,2.0,47,131
16,replace,0.0,C,n,n,C,1,replace n at position 0 with C,flow_matching,0.3,2.0,47,131
17,replace,0.0,[,C,C,[,1,replace C at position 0 with [,flow_matching,0.3,2.0,47,131
18,remove,0.0,[,,[,,0,remove [ from position 0,flow_matching,0.3,2.0,47,131
19,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,47,131
20,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,47,131
21,add,0.0,\,,CC,\CC,3,add \ at position 0,flow_matching,0.3,2.0,47,131
22,remove,2.0,C,,\CC,\C,2,remove C from position 2,flow_matching,0.3,2.0,47,131
23,replace,1.0,N,C,\C,\N,2,replace C at position 1 with N,flow_matching,0.3,2.0,47,131
24,add,2.0,5,,\N,\N5,3,add 5 at position 2,flow_matching,0.3,2.0,47,131
25,remove,0.0,\,,\N5,N5,2,remove \ from position 0,flow_matching,0.3,2.0,47,131
26,replace,0.0,C,N,N5,C5,2,replace N at position 0 with C,flow_matching,0.3,2.0,47,131
27,add,2.0,2,,C5,C52,3,add 2 at position 2,flow_matching,0.3,2.0,47,131
28,add,2.0,\,,C52,C5\2,4,add \ at position 2,flow_matching,0.3,2.0,47,131
29,add,2.0,2,,C5\2,C52\2,5,add 2 at position 2,flow_matching,0.3,2.0,47,131
30,add,2.0,@,,C52\2,C5@2\2,6,add @ at position 2,flow_matching,0.3,2.0,47,131
31,add,5.0,F,,C5@2\2,C5@2\F2,7,add F at position 5,flow_matching,0.3,2.0,47,131
32,remove,3.0,2,,C5@2\F2,C5@\F2,6,remove 2 from position 3,flow_matching,0.3,2.0,47,131
33,add,5.0,@,,C5@\F2,C5@\F@2,7,add @ at position 5,flow_matching,0.3,2.0,47,131
34,remove,5.0,@,,C5@\F@2,C5@\F2,6,remove @ from position 5,flow_matching,0.3,2.0,47,131
35,replace,1.0,C,5,C5@\F2,CC@\F2,6,replace 5 at position 1 with C,flow_matching,0.3,2.0,47,131
36,replace,2.0,(,@,CC@\F2,CC(\F2,6,replace @ at position 2 with (,flow_matching,0.3,2.0,47,131
37,replace,3.0,/,\,CC(\F2,CC(/F2,6,replace \ at position 3 with /,flow_matching,0.3,2.0,47,131
38,add,6.0,I,,CC(/F2,CC(/F2I,7,add I at position 6,flow_matching,0.3,2.0,47,131
39,replace,4.0,C,F,CC(/F2I,CC(/C2I,7,replace F at position 4 with C,flow_matching,0.3,2.0,47,131
40,add,1.0,4,,CC(/C2I,C4C(/C2I,8,add 4 at position 1,flow_matching,0.3,2.0,47,131
41,replace,7.0,#,I,C4C(/C2I,C4C(/C2#,8,replace I at position 7 with #,flow_matching,0.3,2.0,47,131
42,replace,0.0,],C,C4C(/C2#,]4C(/C2#,8,replace C at position 0 with ],flow_matching,0.3,2.0,47,131
43,add,1.0,F,,]4C(/C2#,]F4C(/C2#,9,add F at position 1,flow_matching,0.3,2.0,47,131
44,replace,1.0,\,F,]F4C(/C2#,]\4C(/C2#,9,replace F at position 1 with \,flow_matching,0.3,2.0,47,131
45,remove,4.0,(,,]\4C(/C2#,]\4C/C2#,8,remove ( from position 4,flow_matching,0.3,2.0,47,131
46,replace,1.0,2,\,]\4C/C2#,]24C/C2#,8,replace \ at position 1 with 2,flow_matching,0.3,2.0,47,131
47,replace,0.0,C,],]24C/C2#,C24C/C2#,8,replace ] at position 0 with C,flow_matching,0.3,2.0,47,131
48,replace,6.0,F,2,C24C/C2#,C24C/CF#,8,replace 2 at position 6 with F,flow_matching,0.3,2.0,47,131
49,replace,1.0,C,2,C24C/CF#,CC4C/CF#,8,replace 2 at position 1 with C,flow_matching,0.3,2.0,47,131
50,replace,1.0,O,C,CC4C/CF#,CO4C/CF#,8,replace C at position 1 with O,flow_matching,0.3,2.0,47,131
51,replace,1.0,C,O,CO4C/CF#,CC4C/CF#,8,replace O at position 1 with C,flow_matching,0.3,2.0,47,131
52,add,0.0,c,,CC4C/CF#,cCC4C/CF#,9,add c at position 0,flow_matching,0.3,2.0,47,131
53,add,1.0,N,,cCC4C/CF#,cNCC4C/CF#,10,add N at position 1,flow_matching,0.3,2.0,47,131
54,add,6.0,(,,cNCC4C/CF#,cNCC4C(/CF#,11,add ( at position 6,flow_matching,0.3,2.0,47,131
55,add,4.0,o,,cNCC4C(/CF#,cNCCo4C(/CF#,12,add o at position 4,flow_matching,0.3,2.0,47,131
56,remove,7.0,(,,cNCCo4C(/CF#,cNCCo4C/CF#,11,remove ( from position 7,flow_matching,0.3,2.0,47,131
57,add,6.0,S,,cNCCo4C/CF#,cNCCo4SC/CF#,12,add S at position 6,flow_matching,0.3,2.0,47,131
58,replace,0.0,C,c,cNCCo4SC/CF#,CNCCo4SC/CF#,12,replace c at position 0 with C,flow_matching,0.3,2.0,47,131
59,replace,1.0,C,N,CNCCo4SC/CF#,CCCCo4SC/CF#,12,replace N at position 1 with C,flow_matching,0.3,2.0,47,131
60,add,0.0,F,,CCCCo4SC/CF#,FCCCCo4SC/CF#,13,add F at position 0,flow_matching,0.3,2.0,47,131
61,replace,0.0,C,F,FCCCCo4SC/CF#,CCCCCo4SC/CF#,13,replace F at position 0 with C,flow_matching,0.3,2.0,47,131
62,remove,7.0,S,,CCCCCo4SC/CF#,CCCCCo4C/CF#,12,remove S from position 7,flow_matching,0.3,2.0,47,131
63,replace,2.0,(,C,CCCCCo4C/CF#,CC(CCo4C/CF#,12,replace C at position 2 with (,flow_matching,0.3,2.0,47,131
64,add,6.0,],,CC(CCo4C/CF#,CC(CCo]4C/CF#,13,add ] at position 6,flow_matching,0.3,2.0,47,131
65,replace,0.0,6,C,CC(CCo]4C/CF#,6C(CCo]4C/CF#,13,replace C at position 0 with 6,flow_matching,0.3,2.0,47,131
66,replace,1.0,N,C,6C(CCo]4C/CF#,6N(CCo]4C/CF#,13,replace C at position 1 with N,flow_matching,0.3,2.0,47,131
67,remove,12.0,#,,6N(CCo]4C/CF#,6N(CCo]4C/CF,12,remove # from position 12,flow_matching,0.3,2.0,47,131
68,add,0.0,c,,6N(CCo]4C/CF,c6N(CCo]4C/CF,13,add c at position 0,flow_matching,0.3,2.0,47,131
69,replace,0.0,C,c,c6N(CCo]4C/CF,C6N(CCo]4C/CF,13,replace c at position 0 with C,flow_matching,0.3,2.0,47,131
70,add,13.0,c,,C6N(CCo]4C/CF,C6N(CCo]4C/CFc,14,add c at position 13,flow_matching,0.3,2.0,47,131
71,add,9.0,S,,C6N(CCo]4C/CFc,C6N(CCo]4SC/CFc,15,add S at position 9,flow_matching,0.3,2.0,47,131
72,remove,10.0,C,,C6N(CCo]4SC/CFc,C6N(CCo]4S/CFc,14,remove C from position 10,flow_matching,0.3,2.0,47,131
73,replace,1.0,C,6,C6N(CCo]4S/CFc,CCN(CCo]4S/CFc,14,replace 6 at position 1 with C,flow_matching,0.3,2.0,47,131
74,replace,2.0,(,N,CCN(CCo]4S/CFc,CC((CCo]4S/CFc,14,replace N at position 2 with (,flow_matching,0.3,2.0,47,131
75,replace,3.0,C,(,CC((CCo]4S/CFc,CC(CCCo]4S/CFc,14,replace ( at position 3 with C,flow_matching,0.3,2.0,47,131
76,add,9.0,#,,CC(CCCo]4S/CFc,CC(CCCo]4#S/CFc,15,add # at position 9,flow_matching,0.3,2.0,47,131
77,add,11.0,),,CC(CCCo]4#S/CFc,CC(CCCo]4#S)/CFc,16,add ) at position 11,flow_matching,0.3,2.0,47,131
78,add,6.0,4,,CC(CCCo]4#S)/CFc,CC(CCC4o]4#S)/CFc,17,add 4 at position 6,flow_matching,0.3,2.0,47,131
79,remove,12.0,),,CC(CCC4o]4#S)/CFc,CC(CCC4o]4#S/CFc,16,remove ) from position 12,flow_matching,0.3,2.0,47,131
80,add,12.0,l,,CC(CCC4o]4#S/CFc,CC(CCC4o]4#Sl/CFc,17,add l at position 12,flow_matching,0.3,2.0,47,131
81,replace,4.0,),C,CC(CCC4o]4#Sl/CFc,CC(C)C4o]4#Sl/CFc,17,replace C at position 4 with ),flow_matching,0.3,2.0,47,131
82,add,12.0,],,CC(C)C4o]4#Sl/CFc,CC(C)C4o]4#S]l/CFc,18,add ] at position 12,flow_matching,0.3,2.0,47,131
83,add,9.0,C,,CC(C)C4o]4#S]l/CFc,CC(C)C4o]C4#S]l/CFc,19,add C at position 9,flow_matching,0.3,2.0,47,131
84,replace,5.0,[,C,CC(C)C4o]C4#S]l/CFc,CC(C)[4o]C4#S]l/CFc,19,replace C at position 5 with [,flow_matching,0.3,2.0,47,131
85,remove,1.0,C,,CC(C)[4o]C4#S]l/CFc,C(C)[4o]C4#S]l/CFc,18,remove C from position 1,flow_matching,0.3,2.0,47,131
86,replace,1.0,C,(,C(C)[4o]C4#S]l/CFc,CCC)[4o]C4#S]l/CFc,18,replace ( at position 1 with C,flow_matching,0.3,2.0,47,131
87,replace,2.0,(,C,CCC)[4o]C4#S]l/CFc,CC()[4o]C4#S]l/CFc,18,replace C at position 2 with (,flow_matching,0.3,2.0,47,131
88,replace,3.0,C,),CC()[4o]C4#S]l/CFc,CC(C[4o]C4#S]l/CFc,18,replace ) at position 3 with C,flow_matching,0.3,2.0,47,131
89,replace,4.0,),[,CC(C[4o]C4#S]l/CFc,CC(C)4o]C4#S]l/CFc,18,replace [ at position 4 with ),flow_matching,0.3,2.0,47,131
90,replace,5.0,[,4,CC(C)4o]C4#S]l/CFc,CC(C)[o]C4#S]l/CFc,18,replace 4 at position 5 with [,flow_matching,0.3,2.0,47,131
91,replace,6.0,C,o,CC(C)[o]C4#S]l/CFc,CC(C)[C]C4#S]l/CFc,18,replace o at position 6 with C,flow_matching,0.3,2.0,47,131
92,replace,7.0,@,],CC(C)[C]C4#S]l/CFc,CC(C)[C@C4#S]l/CFc,18,replace ] at position 7 with @,flow_matching,0.3,2.0,47,131
93,replace,8.0,@,C,CC(C)[C@C4#S]l/CFc,CC(C)[C@@4#S]l/CFc,18,replace C at position 8 with @,flow_matching,0.3,2.0,47,131
94,replace,9.0,H,4,CC(C)[C@@4#S]l/CFc,CC(C)[C@@H#S]l/CFc,18,replace 4 at position 9 with H,flow_matching,0.3,2.0,47,131
95,replace,10.0,],#,CC(C)[C@@H#S]l/CFc,CC(C)[C@@H]S]l/CFc,18,replace # at position 10 with ],flow_matching,0.3,2.0,47,131
96,replace,11.0,(,S,CC(C)[C@@H]S]l/CFc,CC(C)[C@@H](]l/CFc,18,replace S at position 11 with (,flow_matching,0.3,2.0,47,131
97,replace,12.0,C,],CC(C)[C@@H](]l/CFc,CC(C)[C@@H](Cl/CFc,18,replace ] at position 12 with C,flow_matching,0.3,2.0,47,131
98,replace,13.0,N,l,CC(C)[C@@H](Cl/CFc,CC(C)[C@@H](CN/CFc,18,replace l at position 13 with N,flow_matching,0.3,2.0,47,131
99,replace,14.0,C,/,CC(C)[C@@H](CN/CFc,CC(C)[C@@H](CNCCFc,18,replace / at position 14 with C,flow_matching,0.3,2.0,47,131
100,replace,15.0,(,C,CC(C)[C@@H](CNCCFc,CC(C)[C@@H](CNC(Fc,18,replace C at position 15 with (,flow_matching,0.3,2.0,47,131
101,replace,16.0,=,F,CC(C)[C@@H](CNC(Fc,CC(C)[C@@H](CNC(=c,18,replace F at position 16 with =,flow_matching,0.3,2.0,47,131
102,replace,17.0,O,c,CC(C)[C@@H](CNC(=c,CC(C)[C@@H](CNC(=O,18,replace c at position 17 with O,flow_matching,0.3,2.0,47,131
103,add,18.0,),,CC(C)[C@@H](CNC(=O,CC(C)[C@@H](CNC(=O),19,add ) at position 18,flow_matching,0.3,2.0,47,131
104,add,19.0,N,,CC(C)[C@@H](CNC(=O),CC(C)[C@@H](CNC(=O)N,20,add N at position 19,flow_matching,0.3,2.0,47,131
105,add,20.0,1,,CC(C)[C@@H](CNC(=O)N,CC(C)[C@@H](CNC(=O)N1,21,add 1 at position 20,flow_matching,0.3,2.0,47,131
106,add,21.0,C,,CC(C)[C@@H](CNC(=O)N1,CC(C)[C@@H](CNC(=O)N1C,22,add C at position 21,flow_matching,0.3,2.0,47,131
107,add,22.0,C,,CC(C)[C@@H](CNC(=O)N1C,CC(C)[C@@H](CNC(=O)N1CC,23,add C at position 22,flow_matching,0.3,2.0,47,131
108,add,23.0,c,,CC(C)[C@@H](CNC(=O)N1CC,CC(C)[C@@H](CNC(=O)N1CCc,24,add c at position 23,flow_matching,0.3,2.0,47,131
109,add,24.0,2,,CC(C)[C@@H](CNC(=O)N1CCc,CC(C)[C@@H](CNC(=O)N1CCc2,25,add 2 at position 24,flow_matching,0.3,2.0,47,131
110,add,25.0,c,,CC(C)[C@@H](CNC(=O)N1CCc2,CC(C)[C@@H](CNC(=O)N1CCc2c,26,add c at position 25,flow_matching,0.3,2.0,47,131
111,add,26.0,c,,CC(C)[C@@H](CNC(=O)N1CCc2c,CC(C)[C@@H](CNC(=O)N1CCc2cc,27,add c at position 26,flow_matching,0.3,2.0,47,131
112,add,27.0,c,,CC(C)[C@@H](CNC(=O)N1CCc2cc,CC(C)[C@@H](CNC(=O)N1CCc2ccc,28,add c at position 27,flow_matching,0.3,2.0,47,131
113,add,28.0,(,,CC(C)[C@@H](CNC(=O)N1CCc2ccc,CC(C)[C@@H](CNC(=O)N1CCc2ccc(,29,add ( at position 28,flow_matching,0.3,2.0,47,131
114,add,29.0,C,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(,CC(C)[C@@H](CNC(=O)N1CCc2ccc(C,30,add C at position 29,flow_matching,0.3,2.0,47,131
115,add,30.0,l,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(C,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl,31,add l at position 30,flow_matching,0.3,2.0,47,131
116,add,31.0,),,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl),32,add ) at position 31,flow_matching,0.3,2.0,47,131
117,add,32.0,c,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl),CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)c,33,add c at position 32,flow_matching,0.3,2.0,47,131
118,add,33.0,c,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)c,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc,34,add c at position 33,flow_matching,0.3,2.0,47,131
119,add,34.0,2,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2,35,add 2 at position 34,flow_matching,0.3,2.0,47,131
120,add,35.0,C,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C,36,add C at position 35,flow_matching,0.3,2.0,47,131
121,add,36.0,1,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1,37,add 1 at position 36,flow_matching,0.3,2.0,47,131
122,add,37.0,),,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1),38,add ) at position 37,flow_matching,0.3,2.0,47,131
123,add,38.0,c,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1),CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c,39,add c at position 38,flow_matching,0.3,2.0,47,131
124,add,39.0,1,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1,40,add 1 at position 39,flow_matching,0.3,2.0,47,131
125,add,40.0,c,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1c,41,add c at position 40,flow_matching,0.3,2.0,47,131
126,add,41.0,c,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1c,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cc,42,add c at position 41,flow_matching,0.3,2.0,47,131
127,add,42.0,c,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cc,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1ccc,43,add c at position 42,flow_matching,0.3,2.0,47,131
128,add,43.0,n,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1ccc,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccn,44,add n at position 43,flow_matching,0.3,2.0,47,131
129,add,44.0,c,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccn,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccnc,45,add c at position 44,flow_matching,0.3,2.0,47,131
130,add,45.0,1,,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccnc,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccnc1,46,add 1 at position 45,flow_matching,0.3,2.0,47,131
131,add,46.0,"
",,CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccnc1,"CC(C)[C@@H](CNC(=O)N1CCc2ccc(Cl)cc2C1)c1cccnc1
",47,"add 
 at position 46",flow_matching,0.3,2.0,47,131
