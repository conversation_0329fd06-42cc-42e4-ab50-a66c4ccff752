step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,42,91
1,add,0.0,2,,,2,1,add 2 at position 0,flow_matching,0.3,2.0,42,91
2,remove,0.0,2,,2,,0,remove 2 from position 0,flow_matching,0.3,2.0,42,91
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,42,91
4,add,1.0,6,,C,C6,2,add 6 at position 1,flow_matching,0.3,2.0,42,91
5,remove,0.0,C,,C6,6,1,remove C from position 0,flow_matching,0.3,2.0,42,91
6,remove,0.0,6,,6,,0,remove 6 from position 0,flow_matching,0.3,2.0,42,91
7,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,42,91
8,add,0.0,c,,I,cI,2,add c at position 0,flow_matching,0.3,2.0,42,91
9,add,1.0,B,,cI,cBI,3,add B at position 1,flow_matching,0.3,2.0,42,91
10,add,1.0,s,,cBI,csBI,4,add s at position 1,flow_matching,0.3,2.0,42,91
11,replace,0.0,C,c,csBI,CsBI,4,replace c at position 0 with C,flow_matching,0.3,2.0,42,91
12,add,1.0,o,,CsBI,CosBI,5,add o at position 1,flow_matching,0.3,2.0,42,91
13,remove,1.0,o,,CosBI,CsBI,4,remove o from position 1,flow_matching,0.3,2.0,42,91
14,replace,1.0,\,s,CsBI,C\BI,4,replace s at position 1 with \,flow_matching,0.3,2.0,42,91
15,replace,1.0,O,\,C\BI,COBI,4,replace \ at position 1 with O,flow_matching,0.3,2.0,42,91
16,add,4.0,5,,COBI,COBI5,5,add 5 at position 4,flow_matching,0.3,2.0,42,91
17,replace,1.0,2,O,COBI5,C2BI5,5,replace O at position 1 with 2,flow_matching,0.3,2.0,42,91
18,replace,1.0,O,2,C2BI5,COBI5,5,replace 2 at position 1 with O,flow_matching,0.3,2.0,42,91
19,replace,0.0,o,C,COBI5,oOBI5,5,replace C at position 0 with o,flow_matching,0.3,2.0,42,91
20,replace,0.0,C,o,oOBI5,COBI5,5,replace o at position 0 with C,flow_matching,0.3,2.0,42,91
21,replace,2.0,C,B,COBI5,COCI5,5,replace B at position 2 with C,flow_matching,0.3,2.0,42,91
22,add,2.0,5,,COCI5,CO5CI5,6,add 5 at position 2,flow_matching,0.3,2.0,42,91
23,add,1.0,#,,CO5CI5,C#O5CI5,7,add # at position 1,flow_matching,0.3,2.0,42,91
24,replace,1.0,O,#,C#O5CI5,COO5CI5,7,replace # at position 1 with O,flow_matching,0.3,2.0,42,91
25,add,2.0,r,,COO5CI5,COrO5CI5,8,add r at position 2,flow_matching,0.3,2.0,42,91
26,remove,7.0,5,,COrO5CI5,COrO5CI,7,remove 5 from position 7,flow_matching,0.3,2.0,42,91
27,replace,2.0,C,r,COrO5CI,COCO5CI,7,replace r at position 2 with C,flow_matching,0.3,2.0,42,91
28,remove,3.0,O,,COCO5CI,COC5CI,6,remove O from position 3,flow_matching,0.3,2.0,42,91
29,add,6.0,O,,COC5CI,COC5CIO,7,add O at position 6,flow_matching,0.3,2.0,42,91
30,add,6.0,F,,COC5CIO,COC5CIFO,8,add F at position 6,flow_matching,0.3,2.0,42,91
31,remove,4.0,C,,COC5CIFO,COC5IFO,7,remove C from position 4,flow_matching,0.3,2.0,42,91
32,add,4.0,-,,COC5IFO,COC5-IFO,8,add - at position 4,flow_matching,0.3,2.0,42,91
33,remove,7.0,O,,COC5-IFO,COC5-IF,7,remove O from position 7,flow_matching,0.3,2.0,42,91
34,add,5.0,r,,COC5-IF,COC5-rIF,8,add r at position 5,flow_matching,0.3,2.0,42,91
35,replace,3.0,[,5,COC5-rIF,COC[-rIF,8,replace 5 at position 3 with [,flow_matching,0.3,2.0,42,91
36,replace,5.0,+,r,COC[-rIF,COC[-+IF,8,replace r at position 5 with +,flow_matching,0.3,2.0,42,91
37,add,2.0,C,,COC[-+IF,COCC[-+IF,9,add C at position 2,flow_matching,0.3,2.0,42,91
38,remove,6.0,+,,COCC[-+IF,COCC[-IF,8,remove + from position 6,flow_matching,0.3,2.0,42,91
39,add,3.0,C,,COCC[-IF,COCCC[-IF,9,add C at position 3,flow_matching,0.3,2.0,42,91
40,add,4.0,o,,COCCC[-IF,COCCoC[-IF,10,add o at position 4,flow_matching,0.3,2.0,42,91
41,replace,4.0,B,o,COCCoC[-IF,COCCBC[-IF,10,replace o at position 4 with B,flow_matching,0.3,2.0,42,91
42,remove,7.0,-,,COCCBC[-IF,COCCBC[IF,9,remove - from position 7,flow_matching,0.3,2.0,42,91
43,add,8.0,],,COCCBC[IF,COCCBC[I]F,10,add ] at position 8,flow_matching,0.3,2.0,42,91
44,add,8.0,-,,COCCBC[I]F,COCCBC[I-]F,11,add - at position 8,flow_matching,0.3,2.0,42,91
45,replace,3.0,[,C,COCCBC[I-]F,COC[BC[I-]F,11,replace C at position 3 with [,flow_matching,0.3,2.0,42,91
46,add,9.0,],,COC[BC[I-]F,COC[BC[I-]]F,12,add ] at position 9,flow_matching,0.3,2.0,42,91
47,remove,8.0,-,,COC[BC[I-]]F,COC[BC[I]]F,11,remove - from position 8,flow_matching,0.3,2.0,42,91
48,remove,2.0,C,,COC[BC[I]]F,CO[BC[I]]F,10,remove C from position 2,flow_matching,0.3,2.0,42,91
49,remove,2.0,[,,CO[BC[I]]F,COBC[I]]F,9,remove [ from position 2,flow_matching,0.3,2.0,42,91
50,add,2.0,2,,COBC[I]]F,CO2BC[I]]F,10,add 2 at position 2,flow_matching,0.3,2.0,42,91
51,replace,2.0,C,2,CO2BC[I]]F,COCBC[I]]F,10,replace 2 at position 2 with C,flow_matching,0.3,2.0,42,91
52,replace,3.0,[,B,COCBC[I]]F,COC[C[I]]F,10,replace B at position 3 with [,flow_matching,0.3,2.0,42,91
53,add,7.0,@,,COC[C[I]]F,COC[C[I@]]F,11,add @ at position 7,flow_matching,0.3,2.0,42,91
54,remove,10.0,F,,COC[C[I@]]F,COC[C[I@]],10,remove F from position 10,flow_matching,0.3,2.0,42,91
55,replace,5.0,@,[,COC[C[I@]],COC[C@I@]],10,replace [ at position 5 with @,flow_matching,0.3,2.0,42,91
56,replace,6.0,H,I,COC[C@I@]],COC[C@H@]],10,replace I at position 6 with H,flow_matching,0.3,2.0,42,91
57,replace,7.0,],@,COC[C@H@]],COC[C@H]]],10,replace @ at position 7 with ],flow_matching,0.3,2.0,42,91
58,replace,8.0,(,],COC[C@H]]],COC[C@H](],10,replace ] at position 8 with (,flow_matching,0.3,2.0,42,91
59,replace,9.0,N,],COC[C@H](],COC[C@H](N,10,replace ] at position 9 with N,flow_matching,0.3,2.0,42,91
60,add,10.0,C,,COC[C@H](N,COC[C@H](NC,11,add C at position 10,flow_matching,0.3,2.0,42,91
61,add,11.0,(,,COC[C@H](NC,COC[C@H](NC(,12,add ( at position 11,flow_matching,0.3,2.0,42,91
62,add,12.0,=,,COC[C@H](NC(,COC[C@H](NC(=,13,add = at position 12,flow_matching,0.3,2.0,42,91
63,add,13.0,O,,COC[C@H](NC(=,COC[C@H](NC(=O,14,add O at position 13,flow_matching,0.3,2.0,42,91
64,add,14.0,),,COC[C@H](NC(=O,COC[C@H](NC(=O),15,add ) at position 14,flow_matching,0.3,2.0,42,91
65,add,15.0,N,,COC[C@H](NC(=O),COC[C@H](NC(=O)N,16,add N at position 15,flow_matching,0.3,2.0,42,91
66,add,16.0,c,,COC[C@H](NC(=O)N,COC[C@H](NC(=O)Nc,17,add c at position 16,flow_matching,0.3,2.0,42,91
67,add,17.0,1,,COC[C@H](NC(=O)Nc,COC[C@H](NC(=O)Nc1,18,add 1 at position 17,flow_matching,0.3,2.0,42,91
68,add,18.0,c,,COC[C@H](NC(=O)Nc1,COC[C@H](NC(=O)Nc1c,19,add c at position 18,flow_matching,0.3,2.0,42,91
69,add,19.0,n,,COC[C@H](NC(=O)Nc1c,COC[C@H](NC(=O)Nc1cn,20,add n at position 19,flow_matching,0.3,2.0,42,91
70,add,20.0,[,,COC[C@H](NC(=O)Nc1cn,COC[C@H](NC(=O)Nc1cn[,21,add [ at position 20,flow_matching,0.3,2.0,42,91
71,add,21.0,n,,COC[C@H](NC(=O)Nc1cn[,COC[C@H](NC(=O)Nc1cn[n,22,add n at position 21,flow_matching,0.3,2.0,42,91
72,add,22.0,H,,COC[C@H](NC(=O)Nc1cn[n,COC[C@H](NC(=O)Nc1cn[nH,23,add H at position 22,flow_matching,0.3,2.0,42,91
73,add,23.0,],,COC[C@H](NC(=O)Nc1cn[nH,COC[C@H](NC(=O)Nc1cn[nH],24,add ] at position 23,flow_matching,0.3,2.0,42,91
74,add,24.0,c,,COC[C@H](NC(=O)Nc1cn[nH],COC[C@H](NC(=O)Nc1cn[nH]c,25,add c at position 24,flow_matching,0.3,2.0,42,91
75,add,25.0,1,,COC[C@H](NC(=O)Nc1cn[nH]c,COC[C@H](NC(=O)Nc1cn[nH]c1,26,add 1 at position 25,flow_matching,0.3,2.0,42,91
76,add,26.0,),,COC[C@H](NC(=O)Nc1cn[nH]c1,COC[C@H](NC(=O)Nc1cn[nH]c1),27,add ) at position 26,flow_matching,0.3,2.0,42,91
77,add,27.0,c,,COC[C@H](NC(=O)Nc1cn[nH]c1),COC[C@H](NC(=O)Nc1cn[nH]c1)c,28,add c at position 27,flow_matching,0.3,2.0,42,91
78,add,28.0,1,,COC[C@H](NC(=O)Nc1cn[nH]c1)c,COC[C@H](NC(=O)Nc1cn[nH]c1)c1,29,add 1 at position 28,flow_matching,0.3,2.0,42,91
79,add,29.0,c,,COC[C@H](NC(=O)Nc1cn[nH]c1)c1,COC[C@H](NC(=O)Nc1cn[nH]c1)c1c,30,add c at position 29,flow_matching,0.3,2.0,42,91
80,add,30.0,c,,COC[C@H](NC(=O)Nc1cn[nH]c1)c1c,COC[C@H](NC(=O)Nc1cn[nH]c1)c1cc,31,add c at position 30,flow_matching,0.3,2.0,42,91
81,add,31.0,c,,COC[C@H](NC(=O)Nc1cn[nH]c1)c1cc,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc,32,add c at position 31,flow_matching,0.3,2.0,42,91
82,add,32.0,(,,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(,33,add ( at position 32,flow_matching,0.3,2.0,42,91
83,add,33.0,F,,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F,34,add F at position 33,flow_matching,0.3,2.0,42,91
84,add,34.0,),,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F),35,add ) at position 34,flow_matching,0.3,2.0,42,91
85,add,35.0,c,,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F),COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c,36,add c at position 35,flow_matching,0.3,2.0,42,91
86,add,36.0,(,,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(,37,add ( at position 36,flow_matching,0.3,2.0,42,91
87,add,37.0,F,,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(F,38,add F at position 37,flow_matching,0.3,2.0,42,91
88,add,38.0,),,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(F,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(F),39,add ) at position 38,flow_matching,0.3,2.0,42,91
89,add,39.0,c,,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(F),COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(F)c,40,add c at position 39,flow_matching,0.3,2.0,42,91
90,add,40.0,1,,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(F)c,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(F)c1,41,add 1 at position 40,flow_matching,0.3,2.0,42,91
91,add,41.0,"
",,COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(F)c1,"COC[C@H](NC(=O)Nc1cn[nH]c1)c1ccc(F)c(F)c1
",42,"add 
 at position 41",flow_matching,0.3,2.0,42,91
