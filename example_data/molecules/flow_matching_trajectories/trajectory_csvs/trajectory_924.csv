step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,31,75
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,31,75
2,add,1.0,\,,O,O\,2,add \ at position 1,flow_matching,0.3,2.0,31,75
3,replace,1.0,n,\,O\,On,2,replace \ at position 1 with n,flow_matching,0.3,2.0,31,75
4,replace,1.0,=,n,On,O=,2,replace n at position 1 with =,flow_matching,0.3,2.0,31,75
5,add,1.0,3,,O=,O3=,3,add 3 at position 1,flow_matching,0.3,2.0,31,75
6,replace,1.0,=,3,O3=,O==,3,replace 3 at position 1 with =,flow_matching,0.3,2.0,31,75
7,add,3.0,[,,O==,O==[,4,add [ at position 3,flow_matching,0.3,2.0,31,75
8,add,4.0,],,O==[,O==[],5,add ] at position 4,flow_matching,0.3,2.0,31,75
9,add,1.0,n,,O==[],On==[],6,add n at position 1,flow_matching,0.3,2.0,31,75
10,add,4.0,N,,On==[],On==N[],7,add N at position 4,flow_matching,0.3,2.0,31,75
11,add,6.0,/,,On==N[],On==N[/],8,add / at position 6,flow_matching,0.3,2.0,31,75
12,remove,5.0,[,,On==N[/],On==N/],7,remove [ from position 5,flow_matching,0.3,2.0,31,75
13,remove,5.0,/,,On==N/],On==N],6,remove / from position 5,flow_matching,0.3,2.0,31,75
14,add,3.0,H,,On==N],On=H=N],7,add H at position 3,flow_matching,0.3,2.0,31,75
15,replace,5.0,6,N,On=H=N],On=H=6],7,replace N at position 5 with 6,flow_matching,0.3,2.0,31,75
16,replace,1.0,=,n,On=H=6],O==H=6],7,replace n at position 1 with =,flow_matching,0.3,2.0,31,75
17,add,4.0,l,,O==H=6],O==Hl=6],8,add l at position 4,flow_matching,0.3,2.0,31,75
18,replace,2.0,C,=,O==Hl=6],O=CHl=6],8,replace = at position 2 with C,flow_matching,0.3,2.0,31,75
19,replace,3.0,(,H,O=CHl=6],O=C(l=6],8,replace H at position 3 with (,flow_matching,0.3,2.0,31,75
20,replace,4.0,C,l,O=C(l=6],O=C(C=6],8,replace l at position 4 with C,flow_matching,0.3,2.0,31,75
21,remove,6.0,6,,O=C(C=6],O=C(C=],7,remove 6 from position 6,flow_matching,0.3,2.0,31,75
22,remove,1.0,=,,O=C(C=],OC(C=],6,remove = from position 1,flow_matching,0.3,2.0,31,75
23,add,0.0,F,,OC(C=],FOC(C=],7,add F at position 0,flow_matching,0.3,2.0,31,75
24,remove,4.0,C,,FOC(C=],FOC(=],6,remove C from position 4,flow_matching,0.3,2.0,31,75
25,remove,0.0,F,,FOC(=],OC(=],5,remove F from position 0,flow_matching,0.3,2.0,31,75
26,remove,3.0,=,,OC(=],OC(],4,remove = from position 3,flow_matching,0.3,2.0,31,75
27,replace,1.0,N,C,OC(],ON(],4,replace C at position 1 with N,flow_matching,0.3,2.0,31,75
28,remove,0.0,O,,ON(],N(],3,remove O from position 0,flow_matching,0.3,2.0,31,75
29,add,2.0,#,,N(],N(#],4,add # at position 2,flow_matching,0.3,2.0,31,75
30,add,2.0,2,,N(#],N(2#],5,add 2 at position 2,flow_matching,0.3,2.0,31,75
31,add,3.0,r,,N(2#],N(2r#],6,add r at position 3,flow_matching,0.3,2.0,31,75
32,replace,0.0,O,N,N(2r#],O(2r#],6,replace N at position 0 with O,flow_matching,0.3,2.0,31,75
33,add,4.0,#,,O(2r#],O(2r##],7,add # at position 4,flow_matching,0.3,2.0,31,75
34,remove,5.0,#,,O(2r##],O(2r#],6,remove # from position 5,flow_matching,0.3,2.0,31,75
35,replace,1.0,=,(,O(2r#],O=2r#],6,replace ( at position 1 with =,flow_matching,0.3,2.0,31,75
36,remove,0.0,O,,O=2r#],=2r#],5,remove O from position 0,flow_matching,0.3,2.0,31,75
37,replace,0.0,O,=,=2r#],O2r#],5,replace = at position 0 with O,flow_matching,0.3,2.0,31,75
38,replace,4.0,F,],O2r#],O2r#F,5,replace ] at position 4 with F,flow_matching,0.3,2.0,31,75
39,remove,1.0,2,,O2r#F,Or#F,4,remove 2 from position 1,flow_matching,0.3,2.0,31,75
40,remove,0.0,O,,Or#F,r#F,3,remove O from position 0,flow_matching,0.3,2.0,31,75
41,replace,2.0,l,F,r#F,r#l,3,replace F at position 2 with l,flow_matching,0.3,2.0,31,75
42,replace,2.0,2,l,r#l,r#2,3,replace l at position 2 with 2,flow_matching,0.3,2.0,31,75
43,replace,0.0,O,r,r#2,O#2,3,replace r at position 0 with O,flow_matching,0.3,2.0,31,75
44,add,0.0,I,,O#2,IO#2,4,add I at position 0,flow_matching,0.3,2.0,31,75
45,replace,0.0,O,I,IO#2,OO#2,4,replace I at position 0 with O,flow_matching,0.3,2.0,31,75
46,replace,1.0,=,O,OO#2,O=#2,4,replace O at position 1 with =,flow_matching,0.3,2.0,31,75
47,replace,2.0,C,#,O=#2,O=C2,4,replace # at position 2 with C,flow_matching,0.3,2.0,31,75
48,replace,3.0,(,2,O=C2,O=C(,4,replace 2 at position 3 with (,flow_matching,0.3,2.0,31,75
49,add,4.0,C,,O=C(,O=C(C,5,add C at position 4,flow_matching,0.3,2.0,31,75
50,add,5.0,O,,O=C(C,O=C(CO,6,add O at position 5,flow_matching,0.3,2.0,31,75
51,add,6.0,c,,O=C(CO,O=C(COc,7,add c at position 6,flow_matching,0.3,2.0,31,75
52,add,7.0,1,,O=C(COc,O=C(COc1,8,add 1 at position 7,flow_matching,0.3,2.0,31,75
53,add,8.0,c,,O=C(COc1,O=C(COc1c,9,add c at position 8,flow_matching,0.3,2.0,31,75
54,add,9.0,c,,O=C(COc1c,O=C(COc1cc,10,add c at position 9,flow_matching,0.3,2.0,31,75
55,add,10.0,c,,O=C(COc1cc,O=C(COc1ccc,11,add c at position 10,flow_matching,0.3,2.0,31,75
56,add,11.0,(,,O=C(COc1ccc,O=C(COc1ccc(,12,add ( at position 11,flow_matching,0.3,2.0,31,75
57,add,12.0,B,,O=C(COc1ccc(,O=C(COc1ccc(B,13,add B at position 12,flow_matching,0.3,2.0,31,75
58,add,13.0,r,,O=C(COc1ccc(B,O=C(COc1ccc(Br,14,add r at position 13,flow_matching,0.3,2.0,31,75
59,add,14.0,),,O=C(COc1ccc(Br,O=C(COc1ccc(Br),15,add ) at position 14,flow_matching,0.3,2.0,31,75
60,add,15.0,c,,O=C(COc1ccc(Br),O=C(COc1ccc(Br)c,16,add c at position 15,flow_matching,0.3,2.0,31,75
61,add,16.0,c,,O=C(COc1ccc(Br)c,O=C(COc1ccc(Br)cc,17,add c at position 16,flow_matching,0.3,2.0,31,75
62,add,17.0,1,,O=C(COc1ccc(Br)cc,O=C(COc1ccc(Br)cc1,18,add 1 at position 17,flow_matching,0.3,2.0,31,75
63,add,18.0,),,O=C(COc1ccc(Br)cc1,O=C(COc1ccc(Br)cc1),19,add ) at position 18,flow_matching,0.3,2.0,31,75
64,add,19.0,N,,O=C(COc1ccc(Br)cc1),O=C(COc1ccc(Br)cc1)N,20,add N at position 19,flow_matching,0.3,2.0,31,75
65,add,20.0,O,,O=C(COc1ccc(Br)cc1)N,O=C(COc1ccc(Br)cc1)NO,21,add O at position 20,flow_matching,0.3,2.0,31,75
66,add,21.0,C,,O=C(COc1ccc(Br)cc1)NO,O=C(COc1ccc(Br)cc1)NOC,22,add C at position 21,flow_matching,0.3,2.0,31,75
67,add,22.0,c,,O=C(COc1ccc(Br)cc1)NOC,O=C(COc1ccc(Br)cc1)NOCc,23,add c at position 22,flow_matching,0.3,2.0,31,75
68,add,23.0,1,,O=C(COc1ccc(Br)cc1)NOCc,O=C(COc1ccc(Br)cc1)NOCc1,24,add 1 at position 23,flow_matching,0.3,2.0,31,75
69,add,24.0,c,,O=C(COc1ccc(Br)cc1)NOCc1,O=C(COc1ccc(Br)cc1)NOCc1c,25,add c at position 24,flow_matching,0.3,2.0,31,75
70,add,25.0,c,,O=C(COc1ccc(Br)cc1)NOCc1c,O=C(COc1ccc(Br)cc1)NOCc1cc,26,add c at position 25,flow_matching,0.3,2.0,31,75
71,add,26.0,c,,O=C(COc1ccc(Br)cc1)NOCc1cc,O=C(COc1ccc(Br)cc1)NOCc1ccc,27,add c at position 26,flow_matching,0.3,2.0,31,75
72,add,27.0,c,,O=C(COc1ccc(Br)cc1)NOCc1ccc,O=C(COc1ccc(Br)cc1)NOCc1cccc,28,add c at position 27,flow_matching,0.3,2.0,31,75
73,add,28.0,c,,O=C(COc1ccc(Br)cc1)NOCc1cccc,O=C(COc1ccc(Br)cc1)NOCc1ccccc,29,add c at position 28,flow_matching,0.3,2.0,31,75
74,add,29.0,1,,O=C(COc1ccc(Br)cc1)NOCc1ccccc,O=C(COc1ccc(Br)cc1)NOCc1ccccc1,30,add 1 at position 29,flow_matching,0.3,2.0,31,75
75,add,30.0,"
",,O=C(COc1ccc(Br)cc1)NOCc1ccccc1,"O=C(COc1ccc(Br)cc1)NOCc1ccccc1
",31,"add 
 at position 30",flow_matching,0.3,2.0,31,75
