step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,56,213
1,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,56,213
2,add,1.0,@,,3,3@,2,add @ at position 1,flow_matching,0.3,2.0,56,213
3,remove,0.0,3,,3@,@,1,remove 3 from position 0,flow_matching,0.3,2.0,56,213
4,replace,0.0,C,@,@,C,1,replace @ at position 0 with C,flow_matching,0.3,2.0,56,213
5,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,56,213
6,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,56,213
7,add,1.0,(,,C,C(,2,add ( at position 1,flow_matching,0.3,2.0,56,213
8,replace,1.0,n,(,C(,Cn,2,replace ( at position 1 with n,flow_matching,0.3,2.0,56,213
9,replace,1.0,[,n,Cn,C[,2,replace n at position 1 with [,flow_matching,0.3,2.0,56,213
10,replace,1.0,7,[,C[,C7,2,replace [ at position 1 with 7,flow_matching,0.3,2.0,56,213
11,add,2.0,3,,C7,C73,3,add 3 at position 2,flow_matching,0.3,2.0,56,213
12,add,0.0,r,,C73,rC73,4,add r at position 0,flow_matching,0.3,2.0,56,213
13,replace,3.0,I,3,rC73,rC7I,4,replace 3 at position 3 with I,flow_matching,0.3,2.0,56,213
14,add,0.0,n,,rC7I,nrC7I,5,add n at position 0,flow_matching,0.3,2.0,56,213
15,replace,0.0,C,n,nrC7I,CrC7I,5,replace n at position 0 with C,flow_matching,0.3,2.0,56,213
16,add,5.0,C,,CrC7I,CrC7IC,6,add C at position 5,flow_matching,0.3,2.0,56,213
17,replace,1.0,[,r,CrC7IC,C[C7IC,6,replace r at position 1 with [,flow_matching,0.3,2.0,56,213
18,add,4.0,#,,C[C7IC,C[C7#IC,7,add # at position 4,flow_matching,0.3,2.0,56,213
19,replace,3.0,@,7,C[C7#IC,C[C@#IC,7,replace 7 at position 3 with @,flow_matching,0.3,2.0,56,213
20,add,6.0,r,,C[C@#IC,C[C@#IrC,8,add r at position 6,flow_matching,0.3,2.0,56,213
21,replace,4.0,H,#,C[C@#IrC,C[C@HIrC,8,replace # at position 4 with H,flow_matching,0.3,2.0,56,213
22,replace,0.0,5,C,C[C@HIrC,5[C@HIrC,8,replace C at position 0 with 5,flow_matching,0.3,2.0,56,213
23,replace,2.0,(,C,5[C@HIrC,5[(@HIrC,8,replace C at position 2 with (,flow_matching,0.3,2.0,56,213
24,add,1.0,-,,5[(@HIrC,5-[(@HIrC,9,add - at position 1,flow_matching,0.3,2.0,56,213
25,replace,4.0,c,@,5-[(@HIrC,5-[(cHIrC,9,replace @ at position 4 with c,flow_matching,0.3,2.0,56,213
26,add,2.0,F,,5-[(cHIrC,5-F[(cHIrC,10,add F at position 2,flow_matching,0.3,2.0,56,213
27,add,10.0,N,,5-F[(cHIrC,5-F[(cHIrCN,11,add N at position 10,flow_matching,0.3,2.0,56,213
28,add,8.0,+,,5-F[(cHIrCN,5-F[(cHI+rCN,12,add + at position 8,flow_matching,0.3,2.0,56,213
29,replace,0.0,C,5,5-F[(cHI+rCN,C-F[(cHI+rCN,12,replace 5 at position 0 with C,flow_matching,0.3,2.0,56,213
30,replace,0.0,#,C,C-F[(cHI+rCN,#-F[(cHI+rCN,12,replace C at position 0 with #,flow_matching,0.3,2.0,56,213
31,add,9.0,o,,#-F[(cHI+rCN,#-F[(cHI+orCN,13,add o at position 9,flow_matching,0.3,2.0,56,213
32,replace,0.0,C,#,#-F[(cHI+orCN,C-F[(cHI+orCN,13,replace # at position 0 with C,flow_matching,0.3,2.0,56,213
33,remove,8.0,+,,C-F[(cHI+orCN,C-F[(cHIorCN,12,remove + from position 8,flow_matching,0.3,2.0,56,213
34,add,0.0,(,,C-F[(cHIorCN,(C-F[(cHIorCN,13,add ( at position 0,flow_matching,0.3,2.0,56,213
35,replace,0.0,C,(,(C-F[(cHIorCN,CC-F[(cHIorCN,13,replace ( at position 0 with C,flow_matching,0.3,2.0,56,213
36,add,2.0,l,,CC-F[(cHIorCN,CCl-F[(cHIorCN,14,add l at position 2,flow_matching,0.3,2.0,56,213
37,remove,1.0,C,,CCl-F[(cHIorCN,Cl-F[(cHIorCN,13,remove C from position 1,flow_matching,0.3,2.0,56,213
38,remove,5.0,(,,Cl-F[(cHIorCN,Cl-F[cHIorCN,12,remove ( from position 5,flow_matching,0.3,2.0,56,213
39,replace,1.0,[,l,Cl-F[cHIorCN,C[-F[cHIorCN,12,replace l at position 1 with [,flow_matching,0.3,2.0,56,213
40,add,4.0,B,,C[-F[cHIorCN,C[-FB[cHIorCN,13,add B at position 4,flow_matching,0.3,2.0,56,213
41,replace,2.0,C,-,C[-FB[cHIorCN,C[CFB[cHIorCN,13,replace - at position 2 with C,flow_matching,0.3,2.0,56,213
42,add,11.0,1,,C[CFB[cHIorCN,C[CFB[cHIor1CN,14,add 1 at position 11,flow_matching,0.3,2.0,56,213
43,add,5.0,H,,C[CFB[cHIor1CN,C[CFBH[cHIor1CN,15,add H at position 5,flow_matching,0.3,2.0,56,213
44,add,1.0,(,,C[CFBH[cHIor1CN,C([CFBH[cHIor1CN,16,add ( at position 1,flow_matching,0.3,2.0,56,213
45,remove,3.0,C,,C([CFBH[cHIor1CN,C([FBH[cHIor1CN,15,remove C from position 3,flow_matching,0.3,2.0,56,213
46,replace,8.0,7,H,C([FBH[cHIor1CN,C([FBH[c7Ior1CN,15,replace H at position 8 with 7,flow_matching,0.3,2.0,56,213
47,add,14.0,#,,C([FBH[c7Ior1CN,C([FBH[c7Ior1C#N,16,add # at position 14,flow_matching,0.3,2.0,56,213
48,add,8.0,H,,C([FBH[c7Ior1C#N,C([FBH[cH7Ior1C#N,17,add H at position 8,flow_matching,0.3,2.0,56,213
49,add,16.0,r,,C([FBH[cH7Ior1C#N,C([FBH[cH7Ior1C#rN,18,add r at position 16,flow_matching,0.3,2.0,56,213
50,remove,3.0,F,,C([FBH[cH7Ior1C#rN,C([BH[cH7Ior1C#rN,17,remove F from position 3,flow_matching,0.3,2.0,56,213
51,replace,8.0,+,7,C([BH[cH7Ior1C#rN,C([BH[cH+Ior1C#rN,17,replace 7 at position 8 with +,flow_matching,0.3,2.0,56,213
52,add,15.0,B,,C([BH[cH+Ior1C#rN,C([BH[cH+Ior1C#BrN,18,add B at position 15,flow_matching,0.3,2.0,56,213
53,replace,1.0,[,(,C([BH[cH+Ior1C#BrN,C[[BH[cH+Ior1C#BrN,18,replace ( at position 1 with [,flow_matching,0.3,2.0,56,213
54,add,8.0,1,,C[[BH[cH+Ior1C#BrN,C[[BH[cH1+Ior1C#BrN,19,add 1 at position 8,flow_matching,0.3,2.0,56,213
55,replace,2.0,C,[,C[[BH[cH1+Ior1C#BrN,C[CBH[cH1+Ior1C#BrN,19,replace [ at position 2 with C,flow_matching,0.3,2.0,56,213
56,add,18.0,/,,C[CBH[cH1+Ior1C#BrN,C[CBH[cH1+Ior1C#Br/N,20,add / at position 18,flow_matching,0.3,2.0,56,213
57,add,14.0,+,,C[CBH[cH1+Ior1C#Br/N,C[CBH[cH1+Ior1+C#Br/N,21,add + at position 14,flow_matching,0.3,2.0,56,213
58,add,18.0,H,,C[CBH[cH1+Ior1+C#Br/N,C[CBH[cH1+Ior1+C#BHr/N,22,add H at position 18,flow_matching,0.3,2.0,56,213
59,add,10.0,s,,C[CBH[cH1+Ior1+C#BHr/N,C[CBH[cH1+sIor1+C#BHr/N,23,add s at position 10,flow_matching,0.3,2.0,56,213
60,replace,3.0,@,B,C[CBH[cH1+sIor1+C#BHr/N,C[C@H[cH1+sIor1+C#BHr/N,23,replace B at position 3 with @,flow_matching,0.3,2.0,56,213
61,remove,19.0,H,,C[C@H[cH1+sIor1+C#BHr/N,C[C@H[cH1+sIor1+C#Br/N,22,remove H from position 19,flow_matching,0.3,2.0,56,213
62,replace,5.0,],[,C[C@H[cH1+sIor1+C#Br/N,C[C@H]cH1+sIor1+C#Br/N,22,replace [ at position 5 with ],flow_matching,0.3,2.0,56,213
63,remove,7.0,H,,C[C@H]cH1+sIor1+C#Br/N,C[C@H]c1+sIor1+C#Br/N,21,remove H from position 7,flow_matching,0.3,2.0,56,213
64,add,12.0,7,,C[C@H]c1+sIor1+C#Br/N,C[C@H]c1+sIo7r1+C#Br/N,22,add 7 at position 12,flow_matching,0.3,2.0,56,213
65,remove,6.0,c,,C[C@H]c1+sIo7r1+C#Br/N,C[C@H]1+sIo7r1+C#Br/N,21,remove c from position 6,flow_matching,0.3,2.0,56,213
66,add,17.0,2,,C[C@H]1+sIo7r1+C#Br/N,C[C@H]1+sIo7r1+C#2Br/N,22,add 2 at position 17,flow_matching,0.3,2.0,56,213
67,replace,2.0,O,C,C[C@H]1+sIo7r1+C#2Br/N,C[O@H]1+sIo7r1+C#2Br/N,22,replace C at position 2 with O,flow_matching,0.3,2.0,56,213
68,replace,2.0,1,O,C[O@H]1+sIo7r1+C#2Br/N,C[1@H]1+sIo7r1+C#2Br/N,22,replace O at position 2 with 1,flow_matching,0.3,2.0,56,213
69,add,8.0,l,,C[1@H]1+sIo7r1+C#2Br/N,C[1@H]1+lsIo7r1+C#2Br/N,23,add l at position 8,flow_matching,0.3,2.0,56,213
70,replace,2.0,C,1,C[1@H]1+lsIo7r1+C#2Br/N,C[C@H]1+lsIo7r1+C#2Br/N,23,replace 1 at position 2 with C,flow_matching,0.3,2.0,56,213
71,add,12.0,(,,C[C@H]1+lsIo7r1+C#2Br/N,C[C@H]1+lsIo(7r1+C#2Br/N,24,add ( at position 12,flow_matching,0.3,2.0,56,213
72,add,0.0,[,,C[C@H]1+lsIo(7r1+C#2Br/N,[C[C@H]1+lsIo(7r1+C#2Br/N,25,add [ at position 0,flow_matching,0.3,2.0,56,213
73,replace,0.0,C,[,[C[C@H]1+lsIo(7r1+C#2Br/N,CC[C@H]1+lsIo(7r1+C#2Br/N,25,replace [ at position 0 with C,flow_matching,0.3,2.0,56,213
74,replace,1.0,[,C,CC[C@H]1+lsIo(7r1+C#2Br/N,C[[C@H]1+lsIo(7r1+C#2Br/N,25,replace C at position 1 with [,flow_matching,0.3,2.0,56,213
75,replace,2.0,n,[,C[[C@H]1+lsIo(7r1+C#2Br/N,C[nC@H]1+lsIo(7r1+C#2Br/N,25,replace [ at position 2 with n,flow_matching,0.3,2.0,56,213
76,remove,23.0,/,,C[nC@H]1+lsIo(7r1+C#2Br/N,C[nC@H]1+lsIo(7r1+C#2BrN,24,remove / from position 23,flow_matching,0.3,2.0,56,213
77,replace,20.0,O,2,C[nC@H]1+lsIo(7r1+C#2BrN,C[nC@H]1+lsIo(7r1+C#OBrN,24,replace 2 at position 20 with O,flow_matching,0.3,2.0,56,213
78,remove,2.0,n,,C[nC@H]1+lsIo(7r1+C#OBrN,C[C@H]1+lsIo(7r1+C#OBrN,23,remove n from position 2,flow_matching,0.3,2.0,56,213
79,add,12.0,(,,C[C@H]1+lsIo(7r1+C#OBrN,C[C@H]1+lsIo((7r1+C#OBrN,24,add ( at position 12,flow_matching,0.3,2.0,56,213
80,remove,16.0,1,,C[C@H]1+lsIo((7r1+C#OBrN,C[C@H]1+lsIo((7r+C#OBrN,23,remove 1 from position 16,flow_matching,0.3,2.0,56,213
81,add,18.0,7,,C[C@H]1+lsIo((7r+C#OBrN,C[C@H]1+lsIo((7r+C7#OBrN,24,add 7 at position 18,flow_matching,0.3,2.0,56,213
82,add,16.0,c,,C[C@H]1+lsIo((7r+C7#OBrN,C[C@H]1+lsIo((7rc+C7#OBrN,25,add c at position 16,flow_matching,0.3,2.0,56,213
83,add,0.0,c,,C[C@H]1+lsIo((7rc+C7#OBrN,cC[C@H]1+lsIo((7rc+C7#OBrN,26,add c at position 0,flow_matching,0.3,2.0,56,213
84,replace,0.0,C,c,cC[C@H]1+lsIo((7rc+C7#OBrN,CC[C@H]1+lsIo((7rc+C7#OBrN,26,replace c at position 0 with C,flow_matching,0.3,2.0,56,213
85,add,8.0,s,,CC[C@H]1+lsIo((7rc+C7#OBrN,CC[C@H]1s+lsIo((7rc+C7#OBrN,27,add s at position 8,flow_matching,0.3,2.0,56,213
86,add,10.0,c,,CC[C@H]1s+lsIo((7rc+C7#OBrN,CC[C@H]1s+clsIo((7rc+C7#OBrN,28,add c at position 10,flow_matching,0.3,2.0,56,213
87,replace,1.0,[,C,CC[C@H]1s+clsIo((7rc+C7#OBrN,C[[C@H]1s+clsIo((7rc+C7#OBrN,28,replace C at position 1 with [,flow_matching,0.3,2.0,56,213
88,replace,2.0,C,[,C[[C@H]1s+clsIo((7rc+C7#OBrN,C[CC@H]1s+clsIo((7rc+C7#OBrN,28,replace [ at position 2 with C,flow_matching,0.3,2.0,56,213
89,add,6.0,n,,C[CC@H]1s+clsIo((7rc+C7#OBrN,C[CC@Hn]1s+clsIo((7rc+C7#OBrN,29,add n at position 6,flow_matching,0.3,2.0,56,213
90,replace,3.0,@,C,C[CC@Hn]1s+clsIo((7rc+C7#OBrN,C[C@@Hn]1s+clsIo((7rc+C7#OBrN,29,replace C at position 3 with @,flow_matching,0.3,2.0,56,213
91,remove,22.0,C,,C[C@@Hn]1s+clsIo((7rc+C7#OBrN,C[C@@Hn]1s+clsIo((7rc+7#OBrN,28,remove C from position 22,flow_matching,0.3,2.0,56,213
92,add,25.0,s,,C[C@@Hn]1s+clsIo((7rc+7#OBrN,C[C@@Hn]1s+clsIo((7rc+7#OsBrN,29,add s at position 25,flow_matching,0.3,2.0,56,213
93,add,5.0,5,,C[C@@Hn]1s+clsIo((7rc+7#OsBrN,C[C@@5Hn]1s+clsIo((7rc+7#OsBrN,30,add 5 at position 5,flow_matching,0.3,2.0,56,213
94,replace,4.0,H,@,C[C@@5Hn]1s+clsIo((7rc+7#OsBrN,C[C@H5Hn]1s+clsIo((7rc+7#OsBrN,30,replace @ at position 4 with H,flow_matching,0.3,2.0,56,213
95,add,14.0,r,,C[C@H5Hn]1s+clsIo((7rc+7#OsBrN,C[C@H5Hn]1s+clrsIo((7rc+7#OsBrN,31,add r at position 14,flow_matching,0.3,2.0,56,213
96,replace,5.0,],5,C[C@H5Hn]1s+clrsIo((7rc+7#OsBrN,C[C@H]Hn]1s+clrsIo((7rc+7#OsBrN,31,replace 5 at position 5 with ],flow_matching,0.3,2.0,56,213
97,remove,6.0,H,,C[C@H]Hn]1s+clrsIo((7rc+7#OsBrN,C[C@H]n]1s+clrsIo((7rc+7#OsBrN,30,remove H from position 6,flow_matching,0.3,2.0,56,213
98,add,13.0,1,,C[C@H]n]1s+clrsIo((7rc+7#OsBrN,C[C@H]n]1s+cl1rsIo((7rc+7#OsBrN,31,add 1 at position 13,flow_matching,0.3,2.0,56,213
99,add,22.0,I,,C[C@H]n]1s+cl1rsIo((7rc+7#OsBrN,C[C@H]n]1s+cl1rsIo((7rIc+7#OsBrN,32,add I at position 22,flow_matching,0.3,2.0,56,213
100,replace,30.0,(,r,C[C@H]n]1s+cl1rsIo((7rIc+7#OsBrN,C[C@H]n]1s+cl1rsIo((7rIc+7#OsB(N,32,replace r at position 30 with (,flow_matching,0.3,2.0,56,213
101,replace,6.0,(,n,C[C@H]n]1s+cl1rsIo((7rIc+7#OsB(N,C[C@H](]1s+cl1rsIo((7rIc+7#OsB(N,32,replace n at position 6 with (,flow_matching,0.3,2.0,56,213
102,remove,26.0,#,,C[C@H](]1s+cl1rsIo((7rIc+7#OsB(N,C[C@H](]1s+cl1rsIo((7rIc+7OsB(N,31,remove # from position 26,flow_matching,0.3,2.0,56,213
103,replace,7.0,N,],C[C@H](]1s+cl1rsIo((7rIc+7OsB(N,C[C@H](N1s+cl1rsIo((7rIc+7OsB(N,31,replace ] at position 7 with N,flow_matching,0.3,2.0,56,213
104,replace,8.0,C,1,C[C@H](N1s+cl1rsIo((7rIc+7OsB(N,C[C@H](NCs+cl1rsIo((7rIc+7OsB(N,31,replace 1 at position 8 with C,flow_matching,0.3,2.0,56,213
105,replace,9.0,(,s,C[C@H](NCs+cl1rsIo((7rIc+7OsB(N,C[C@H](NC(+cl1rsIo((7rIc+7OsB(N,31,replace s at position 9 with (,flow_matching,0.3,2.0,56,213
106,replace,11.0,B,c,C[C@H](NC(+cl1rsIo((7rIc+7OsB(N,C[C@H](NC(+Bl1rsIo((7rIc+7OsB(N,31,replace c at position 11 with B,flow_matching,0.3,2.0,56,213
107,add,19.0,r,,C[C@H](NC(+Bl1rsIo((7rIc+7OsB(N,C[C@H](NC(+Bl1rsIo(r(7rIc+7OsB(N,32,add r at position 19,flow_matching,0.3,2.0,56,213
108,replace,25.0,[,+,C[C@H](NC(+Bl1rsIo(r(7rIc+7OsB(N,C[C@H](NC(+Bl1rsIo(r(7rIc[7OsB(N,32,replace + at position 25 with [,flow_matching,0.3,2.0,56,213
109,replace,10.0,=,+,C[C@H](NC(+Bl1rsIo(r(7rIc[7OsB(N,C[C@H](NC(=Bl1rsIo(r(7rIc[7OsB(N,32,replace + at position 10 with =,flow_matching,0.3,2.0,56,213
110,replace,0.0,H,C,C[C@H](NC(=Bl1rsIo(r(7rIc[7OsB(N,H[C@H](NC(=Bl1rsIo(r(7rIc[7OsB(N,32,replace C at position 0 with H,flow_matching,0.3,2.0,56,213
111,replace,0.0,C,H,H[C@H](NC(=Bl1rsIo(r(7rIc[7OsB(N,C[C@H](NC(=Bl1rsIo(r(7rIc[7OsB(N,32,replace H at position 0 with C,flow_matching,0.3,2.0,56,213
112,add,3.0,1,,C[C@H](NC(=Bl1rsIo(r(7rIc[7OsB(N,C[C1@H](NC(=Bl1rsIo(r(7rIc[7OsB(N,33,add 1 at position 3,flow_matching,0.3,2.0,56,213
113,replace,17.0,-,I,C[C1@H](NC(=Bl1rsIo(r(7rIc[7OsB(N,C[C1@H](NC(=Bl1rs-o(r(7rIc[7OsB(N,33,replace I at position 17 with -,flow_matching,0.3,2.0,56,213
114,remove,12.0,B,,C[C1@H](NC(=Bl1rs-o(r(7rIc[7OsB(N,C[C1@H](NC(=l1rs-o(r(7rIc[7OsB(N,32,remove B from position 12,flow_matching,0.3,2.0,56,213
115,replace,3.0,@,1,C[C1@H](NC(=l1rs-o(r(7rIc[7OsB(N,C[C@@H](NC(=l1rs-o(r(7rIc[7OsB(N,32,replace 1 at position 3 with @,flow_matching,0.3,2.0,56,213
116,add,3.0,6,,C[C@@H](NC(=l1rs-o(r(7rIc[7OsB(N,C[C6@@H](NC(=l1rs-o(r(7rIc[7OsB(N,33,add 6 at position 3,flow_matching,0.3,2.0,56,213
117,replace,11.0,#,(,C[C6@@H](NC(=l1rs-o(r(7rIc[7OsB(N,C[C6@@H](NC#=l1rs-o(r(7rIc[7OsB(N,33,replace ( at position 11 with #,flow_matching,0.3,2.0,56,213
118,replace,22.0,1,7,C[C6@@H](NC#=l1rs-o(r(7rIc[7OsB(N,C[C6@@H](NC#=l1rs-o(r(1rIc[7OsB(N,33,replace 7 at position 22 with 1,flow_matching,0.3,2.0,56,213
119,remove,3.0,6,,C[C6@@H](NC#=l1rs-o(r(1rIc[7OsB(N,C[C@@H](NC#=l1rs-o(r(1rIc[7OsB(N,32,remove 6 from position 3,flow_matching,0.3,2.0,56,213
120,remove,31.0,N,,C[C@@H](NC#=l1rs-o(r(1rIc[7OsB(N,C[C@@H](NC#=l1rs-o(r(1rIc[7OsB(,31,remove N from position 31,flow_matching,0.3,2.0,56,213
121,add,3.0,=,,C[C@@H](NC#=l1rs-o(r(1rIc[7OsB(,C[C=@@H](NC#=l1rs-o(r(1rIc[7OsB(,32,add = at position 3,flow_matching,0.3,2.0,56,213
122,remove,10.0,C,,C[C=@@H](NC#=l1rs-o(r(1rIc[7OsB(,C[C=@@H](N#=l1rs-o(r(1rIc[7OsB(,31,remove C from position 10,flow_matching,0.3,2.0,56,213
123,replace,14.0,B,r,C[C=@@H](N#=l1rs-o(r(1rIc[7OsB(,C[C=@@H](N#=l1Bs-o(r(1rIc[7OsB(,31,replace r at position 14 with B,flow_matching,0.3,2.0,56,213
124,add,3.0,+,,C[C=@@H](N#=l1Bs-o(r(1rIc[7OsB(,C[C+=@@H](N#=l1Bs-o(r(1rIc[7OsB(,32,add + at position 3,flow_matching,0.3,2.0,56,213
125,replace,31.0,s,(,C[C+=@@H](N#=l1Bs-o(r(1rIc[7OsB(,C[C+=@@H](N#=l1Bs-o(r(1rIc[7OsBs,32,replace ( at position 31 with s,flow_matching,0.3,2.0,56,213
126,add,5.0,N,,C[C+=@@H](N#=l1Bs-o(r(1rIc[7OsBs,C[C+=N@@H](N#=l1Bs-o(r(1rIc[7OsBs,33,add N at position 5,flow_matching,0.3,2.0,56,213
127,replace,3.0,@,+,C[C+=N@@H](N#=l1Bs-o(r(1rIc[7OsBs,C[C@=N@@H](N#=l1Bs-o(r(1rIc[7OsBs,33,replace + at position 3 with @,flow_matching,0.3,2.0,56,213
128,replace,7.0,s,@,C[C@=N@@H](N#=l1Bs-o(r(1rIc[7OsBs,C[C@=N@sH](N#=l1Bs-o(r(1rIc[7OsBs,33,replace @ at position 7 with s,flow_matching,0.3,2.0,56,213
129,replace,4.0,H,=,C[C@=N@sH](N#=l1Bs-o(r(1rIc[7OsBs,C[C@HN@sH](N#=l1Bs-o(r(1rIc[7OsBs,33,replace = at position 4 with H,flow_matching,0.3,2.0,56,213
130,add,12.0,=,,C[C@HN@sH](N#=l1Bs-o(r(1rIc[7OsBs,C[C@HN@sH](N=#=l1Bs-o(r(1rIc[7OsBs,34,add = at position 12,flow_matching,0.3,2.0,56,213
131,replace,5.0,],N,C[C@HN@sH](N=#=l1Bs-o(r(1rIc[7OsBs,C[C@H]@sH](N=#=l1Bs-o(r(1rIc[7OsBs,34,replace N at position 5 with ],flow_matching,0.3,2.0,56,213
132,replace,18.0,c,s,C[C@H]@sH](N=#=l1Bs-o(r(1rIc[7OsBs,C[C@H]@sH](N=#=l1Bc-o(r(1rIc[7OsBs,34,replace s at position 18 with c,flow_matching,0.3,2.0,56,213
133,replace,0.0,c,C,C[C@H]@sH](N=#=l1Bc-o(r(1rIc[7OsBs,c[C@H]@sH](N=#=l1Bc-o(r(1rIc[7OsBs,34,replace C at position 0 with c,flow_matching,0.3,2.0,56,213
134,remove,27.0,c,,c[C@H]@sH](N=#=l1Bc-o(r(1rIc[7OsBs,c[C@H]@sH](N=#=l1Bc-o(r(1rI[7OsBs,33,remove c from position 27,flow_matching,0.3,2.0,56,213
135,replace,0.0,C,c,c[C@H]@sH](N=#=l1Bc-o(r(1rI[7OsBs,C[C@H]@sH](N=#=l1Bc-o(r(1rI[7OsBs,33,replace c at position 0 with C,flow_matching,0.3,2.0,56,213
136,add,14.0,@,,C[C@H]@sH](N=#=l1Bc-o(r(1rI[7OsBs,C[C@H]@sH](N=#@=l1Bc-o(r(1rI[7OsBs,34,add @ at position 14,flow_matching,0.3,2.0,56,213
137,replace,6.0,(,@,C[C@H]@sH](N=#@=l1Bc-o(r(1rI[7OsBs,C[C@H](sH](N=#@=l1Bc-o(r(1rI[7OsBs,34,replace @ at position 6 with (,flow_matching,0.3,2.0,56,213
138,add,24.0,N,,C[C@H](sH](N=#@=l1Bc-o(r(1rI[7OsBs,C[C@H](sH](N=#@=l1Bc-o(rN(1rI[7OsBs,35,add N at position 24,flow_matching,0.3,2.0,56,213
139,replace,18.0,o,B,C[C@H](sH](N=#@=l1Bc-o(rN(1rI[7OsBs,C[C@H](sH](N=#@=l1oc-o(rN(1rI[7OsBs,35,replace B at position 18 with o,flow_matching,0.3,2.0,56,213
140,replace,7.0,N,s,C[C@H](sH](N=#@=l1oc-o(rN(1rI[7OsBs,C[C@H](NH](N=#@=l1oc-o(rN(1rI[7OsBs,35,replace s at position 7 with N,flow_matching,0.3,2.0,56,213
141,replace,8.0,C,H,C[C@H](NH](N=#@=l1oc-o(rN(1rI[7OsBs,C[C@H](NC](N=#@=l1oc-o(rN(1rI[7OsBs,35,replace H at position 8 with C,flow_matching,0.3,2.0,56,213
142,add,3.0,/,,C[C@H](NC](N=#@=l1oc-o(rN(1rI[7OsBs,C[C/@H](NC](N=#@=l1oc-o(rN(1rI[7OsBs,36,add / at position 3,flow_matching,0.3,2.0,56,213
143,add,7.0,/,,C[C/@H](NC](N=#@=l1oc-o(rN(1rI[7OsBs,C[C/@H]/(NC](N=#@=l1oc-o(rN(1rI[7OsBs,37,add / at position 7,flow_matching,0.3,2.0,56,213
144,add,22.0,2,,C[C/@H]/(NC](N=#@=l1oc-o(rN(1rI[7OsBs,C[C/@H]/(NC](N=#@=l1oc2-o(rN(1rI[7OsBs,38,add 2 at position 22,flow_matching,0.3,2.0,56,213
145,add,29.0,l,,C[C/@H]/(NC](N=#@=l1oc2-o(rN(1rI[7OsBs,C[C/@H]/(NC](N=#@=l1oc2-o(rN(l1rI[7OsBs,39,add l at position 29,flow_matching,0.3,2.0,56,213
146,remove,38.0,s,,C[C/@H]/(NC](N=#@=l1oc2-o(rN(l1rI[7OsBs,C[C/@H]/(NC](N=#@=l1oc2-o(rN(l1rI[7OsB,38,remove s from position 38,flow_matching,0.3,2.0,56,213
147,remove,1.0,[,,C[C/@H]/(NC](N=#@=l1oc2-o(rN(l1rI[7OsB,CC/@H]/(NC](N=#@=l1oc2-o(rN(l1rI[7OsB,37,remove [ from position 1,flow_matching,0.3,2.0,56,213
148,add,3.0,s,,CC/@H]/(NC](N=#@=l1oc2-o(rN(l1rI[7OsB,CC/s@H]/(NC](N=#@=l1oc2-o(rN(l1rI[7OsB,38,add s at position 3,flow_matching,0.3,2.0,56,213
149,add,37.0,6,,CC/s@H]/(NC](N=#@=l1oc2-o(rN(l1rI[7OsB,CC/s@H]/(NC](N=#@=l1oc2-o(rN(l1rI[7Os6B,39,add 6 at position 37,flow_matching,0.3,2.0,56,213
150,replace,19.0,r,1,CC/s@H]/(NC](N=#@=l1oc2-o(rN(l1rI[7Os6B,CC/s@H]/(NC](N=#@=lroc2-o(rN(l1rI[7Os6B,39,replace 1 at position 19 with r,flow_matching,0.3,2.0,56,213
151,replace,9.0,3,N,CC/s@H]/(NC](N=#@=lroc2-o(rN(l1rI[7Os6B,CC/s@H]/(3C](N=#@=lroc2-o(rN(l1rI[7Os6B,39,replace N at position 9 with 3,flow_matching,0.3,2.0,56,213
152,replace,14.0,c,=,CC/s@H]/(3C](N=#@=lroc2-o(rN(l1rI[7Os6B,CC/s@H]/(3C](Nc#@=lroc2-o(rN(l1rI[7Os6B,39,replace = at position 14 with c,flow_matching,0.3,2.0,56,213
153,add,32.0,(,,CC/s@H]/(3C](Nc#@=lroc2-o(rN(l1rI[7Os6B,CC/s@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7Os6B,40,add ( at position 32,flow_matching,0.3,2.0,56,213
154,replace,38.0,s,6,CC/s@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7Os6B,CC/s@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7OssB,40,replace 6 at position 38 with s,flow_matching,0.3,2.0,56,213
155,add,2.0,B,,CC/s@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7OssB,CCB/s@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7OssB,41,add B at position 2,flow_matching,0.3,2.0,56,213
156,add,4.0,B,,CCB/s@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7OssB,CCB/Bs@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7OssB,42,add B at position 4,flow_matching,0.3,2.0,56,213
157,replace,1.0,[,C,CCB/Bs@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7OssB,C[B/Bs@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7OssB,42,replace C at position 1 with [,flow_matching,0.3,2.0,56,213
158,remove,5.0,s,,C[B/Bs@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7OssB,C[B/B@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7OssB,41,remove s from position 5,flow_matching,0.3,2.0,56,213
159,remove,23.0,2,,C[B/B@H]/(3C](Nc#@=lroc2-o(rN(l1r(I[7OssB,C[B/B@H]/(3C](Nc#@=lroc-o(rN(l1r(I[7OssB,40,remove 2 from position 23,flow_matching,0.3,2.0,56,213
160,remove,17.0,@,,C[B/B@H]/(3C](Nc#@=lroc-o(rN(l1r(I[7OssB,C[B/B@H]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,39,remove @ from position 17,flow_matching,0.3,2.0,56,213
161,replace,2.0,C,B,C[B/B@H]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,C[C/B@H]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,39,replace B at position 2 with C,flow_matching,0.3,2.0,56,213
162,replace,3.0,@,/,C[C/B@H]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,C[C@B@H]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,39,replace / at position 3 with @,flow_matching,0.3,2.0,56,213
163,replace,4.0,H,B,C[C@B@H]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,C[C@H@H]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,39,replace B at position 4 with H,flow_matching,0.3,2.0,56,213
164,replace,5.0,],@,C[C@H@H]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,C[C@H]H]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,39,replace @ at position 5 with ],flow_matching,0.3,2.0,56,213
165,replace,6.0,(,H,C[C@H]H]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,C[C@H](]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,39,replace H at position 6 with (,flow_matching,0.3,2.0,56,213
166,replace,7.0,N,],C[C@H](]/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,C[C@H](N/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,39,replace ] at position 7 with N,flow_matching,0.3,2.0,56,213
167,replace,8.0,C,/,C[C@H](N/(3C](Nc#=lroc-o(rN(l1r(I[7OssB,C[C@H](NC(3C](Nc#=lroc-o(rN(l1r(I[7OssB,39,replace / at position 8 with C,flow_matching,0.3,2.0,56,213
168,replace,10.0,=,3,C[C@H](NC(3C](Nc#=lroc-o(rN(l1r(I[7OssB,C[C@H](NC(=C](Nc#=lroc-o(rN(l1r(I[7OssB,39,replace 3 at position 10 with =,flow_matching,0.3,2.0,56,213
169,replace,11.0,O,C,C[C@H](NC(=C](Nc#=lroc-o(rN(l1r(I[7OssB,C[C@H](NC(=O](Nc#=lroc-o(rN(l1r(I[7OssB,39,replace C at position 11 with O,flow_matching,0.3,2.0,56,213
170,replace,12.0,),],C[C@H](NC(=O](Nc#=lroc-o(rN(l1r(I[7OssB,C[C@H](NC(=O)(Nc#=lroc-o(rN(l1r(I[7OssB,39,replace ] at position 12 with ),flow_matching,0.3,2.0,56,213
171,replace,13.0,N,(,C[C@H](NC(=O)(Nc#=lroc-o(rN(l1r(I[7OssB,C[C@H](NC(=O)NNc#=lroc-o(rN(l1r(I[7OssB,39,replace ( at position 13 with N,flow_matching,0.3,2.0,56,213
172,replace,14.0,C,N,C[C@H](NC(=O)NNc#=lroc-o(rN(l1r(I[7OssB,C[C@H](NC(=O)NCc#=lroc-o(rN(l1r(I[7OssB,39,replace N at position 14 with C,flow_matching,0.3,2.0,56,213
173,replace,15.0,[,c,C[C@H](NC(=O)NCc#=lroc-o(rN(l1r(I[7OssB,C[C@H](NC(=O)NC[#=lroc-o(rN(l1r(I[7OssB,39,replace c at position 15 with [,flow_matching,0.3,2.0,56,213
174,replace,16.0,C,#,C[C@H](NC(=O)NC[#=lroc-o(rN(l1r(I[7OssB,C[C@H](NC(=O)NC[C=lroc-o(rN(l1r(I[7OssB,39,replace # at position 16 with C,flow_matching,0.3,2.0,56,213
175,replace,17.0,@,=,C[C@H](NC(=O)NC[C=lroc-o(rN(l1r(I[7OssB,C[C@H](NC(=O)NC[C@lroc-o(rN(l1r(I[7OssB,39,replace = at position 17 with @,flow_matching,0.3,2.0,56,213
176,replace,18.0,H,l,C[C@H](NC(=O)NC[C@lroc-o(rN(l1r(I[7OssB,C[C@H](NC(=O)NC[C@Hroc-o(rN(l1r(I[7OssB,39,replace l at position 18 with H,flow_matching,0.3,2.0,56,213
177,replace,19.0,],r,C[C@H](NC(=O)NC[C@Hroc-o(rN(l1r(I[7OssB,C[C@H](NC(=O)NC[C@H]oc-o(rN(l1r(I[7OssB,39,replace r at position 19 with ],flow_matching,0.3,2.0,56,213
178,replace,20.0,(,o,C[C@H](NC(=O)NC[C@H]oc-o(rN(l1r(I[7OssB,C[C@H](NC(=O)NC[C@H](c-o(rN(l1r(I[7OssB,39,replace o at position 20 with (,flow_matching,0.3,2.0,56,213
179,replace,21.0,C,c,C[C@H](NC(=O)NC[C@H](c-o(rN(l1r(I[7OssB,C[C@H](NC(=O)NC[C@H](C-o(rN(l1r(I[7OssB,39,replace c at position 21 with C,flow_matching,0.3,2.0,56,213
180,replace,22.0,),-,C[C@H](NC(=O)NC[C@H](C-o(rN(l1r(I[7OssB,C[C@H](NC(=O)NC[C@H](C)o(rN(l1r(I[7OssB,39,replace - at position 22 with ),flow_matching,0.3,2.0,56,213
181,replace,23.0,C,o,C[C@H](NC(=O)NC[C@H](C)o(rN(l1r(I[7OssB,C[C@H](NC(=O)NC[C@H](C)C(rN(l1r(I[7OssB,39,replace o at position 23 with C,flow_matching,0.3,2.0,56,213
182,replace,24.0,[,(,C[C@H](NC(=O)NC[C@H](C)C(rN(l1r(I[7OssB,C[C@H](NC(=O)NC[C@H](C)C[rN(l1r(I[7OssB,39,replace ( at position 24 with [,flow_matching,0.3,2.0,56,213
183,replace,25.0,C,r,C[C@H](NC(=O)NC[C@H](C)C[rN(l1r(I[7OssB,C[C@H](NC(=O)NC[C@H](C)C[CN(l1r(I[7OssB,39,replace r at position 25 with C,flow_matching,0.3,2.0,56,213
184,replace,26.0,@,N,C[C@H](NC(=O)NC[C@H](C)C[CN(l1r(I[7OssB,C[C@H](NC(=O)NC[C@H](C)C[C@(l1r(I[7OssB,39,replace N at position 26 with @,flow_matching,0.3,2.0,56,213
185,replace,27.0,@,(,C[C@H](NC(=O)NC[C@H](C)C[C@(l1r(I[7OssB,C[C@H](NC(=O)NC[C@H](C)C[C@@l1r(I[7OssB,39,replace ( at position 27 with @,flow_matching,0.3,2.0,56,213
186,replace,28.0,H,l,C[C@H](NC(=O)NC[C@H](C)C[C@@l1r(I[7OssB,C[C@H](NC(=O)NC[C@H](C)C[C@@H1r(I[7OssB,39,replace l at position 28 with H,flow_matching,0.3,2.0,56,213
187,replace,29.0,],1,C[C@H](NC(=O)NC[C@H](C)C[C@@H1r(I[7OssB,C[C@H](NC(=O)NC[C@H](C)C[C@@H]r(I[7OssB,39,replace 1 at position 29 with ],flow_matching,0.3,2.0,56,213
188,replace,30.0,(,r,C[C@H](NC(=O)NC[C@H](C)C[C@@H]r(I[7OssB,C[C@H](NC(=O)NC[C@H](C)C[C@@H]((I[7OssB,39,replace r at position 30 with (,flow_matching,0.3,2.0,56,213
189,replace,31.0,C,(,C[C@H](NC(=O)NC[C@H](C)C[C@@H]((I[7OssB,C[C@H](NC(=O)NC[C@H](C)C[C@@H](CI[7OssB,39,replace ( at position 31 with C,flow_matching,0.3,2.0,56,213
190,replace,32.0,),I,C[C@H](NC(=O)NC[C@H](C)C[C@@H](CI[7OssB,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)[7OssB,39,replace I at position 32 with ),flow_matching,0.3,2.0,56,213
191,replace,33.0,O,[,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)[7OssB,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O7OssB,39,replace [ at position 33 with O,flow_matching,0.3,2.0,56,213
192,replace,34.0,),7,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O7OssB,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)OssB,39,replace 7 at position 34 with ),flow_matching,0.3,2.0,56,213
193,replace,35.0,c,O,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)OssB,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)cssB,39,replace O at position 35 with c,flow_matching,0.3,2.0,56,213
194,replace,36.0,1,s,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)cssB,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1sB,39,replace s at position 36 with 1,flow_matching,0.3,2.0,56,213
195,replace,37.0,c,s,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1sB,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1cB,39,replace s at position 37 with c,flow_matching,0.3,2.0,56,213
196,replace,38.0,c,B,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1cB,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1cc,39,replace B at position 38 with c,flow_matching,0.3,2.0,56,213
197,add,39.0,c,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1cc,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc,40,add c at position 39,flow_matching,0.3,2.0,56,213
198,add,40.0,(,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(,41,add ( at position 40,flow_matching,0.3,2.0,56,213
199,add,41.0,S,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S,42,add S at position 41,flow_matching,0.3,2.0,56,213
200,add,42.0,(,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(,43,add ( at position 42,flow_matching,0.3,2.0,56,213
201,add,43.0,C,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C,44,add C at position 43,flow_matching,0.3,2.0,56,213
202,add,44.0,),,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C),45,add ) at position 44,flow_matching,0.3,2.0,56,213
203,add,45.0,(,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C),C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(,46,add ( at position 45,flow_matching,0.3,2.0,56,213
204,add,46.0,=,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=,47,add = at position 46,flow_matching,0.3,2.0,56,213
205,add,47.0,O,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O,48,add O at position 47,flow_matching,0.3,2.0,56,213
206,add,48.0,),,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O),49,add ) at position 48,flow_matching,0.3,2.0,56,213
207,add,49.0,=,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O),C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=,50,add = at position 49,flow_matching,0.3,2.0,56,213
208,add,50.0,O,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O,51,add O at position 50,flow_matching,0.3,2.0,56,213
209,add,51.0,),,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O),52,add ) at position 51,flow_matching,0.3,2.0,56,213
210,add,52.0,c,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O),C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O)c,53,add c at position 52,flow_matching,0.3,2.0,56,213
211,add,53.0,c,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O)c,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O)cc,54,add c at position 53,flow_matching,0.3,2.0,56,213
212,add,54.0,1,,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O)cc,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O)cc1,55,add 1 at position 54,flow_matching,0.3,2.0,56,213
213,add,55.0,"
",,C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O)cc1,"C[C@H](NC(=O)NC[C@H](C)C[C@@H](C)O)c1ccc(S(C)(=O)=O)cc1
",56,"add 
 at position 55",flow_matching,0.3,2.0,56,213
