step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,47,139
1,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,47,139
2,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,47,139
3,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,47,139
4,replace,0.0,H,C,CO,HO,2,replace <PERSON> at position 0 with H,flow_matching,0.3,2.0,47,139
5,add,1.0,#,,HO,H#O,3,add # at position 1,flow_matching,0.3,2.0,47,139
6,replace,0.0,1,H,<PERSON>#O,1#O,3,replace <PERSON> at position 0 with 1,flow_matching,0.3,2.0,47,139
7,add,2.0,N,,1#O,1#NO,4,add N at position 2,flow_matching,0.3,2.0,47,139
8,add,2.0,s,,1#NO,1#sNO,5,add s at position 2,flow_matching,0.3,2.0,47,139
9,add,3.0,I,,1#sNO,1#sINO,6,add I at position 3,flow_matching,0.3,2.0,47,139
10,replace,4.0,2,N,1#sINO,1#sI2O,6,replace N at position 4 with 2,flow_matching,0.3,2.0,47,139
11,remove,2.0,s,,1#sI2O,1#I2O,5,remove s from position 2,flow_matching,0.3,2.0,47,139
12,remove,3.0,2,,1#I2O,1#IO,4,remove 2 from position 3,flow_matching,0.3,2.0,47,139
13,replace,1.0,H,#,1#IO,1HIO,4,replace # at position 1 with H,flow_matching,0.3,2.0,47,139
14,remove,3.0,O,,1HIO,1HI,3,remove O from position 3,flow_matching,0.3,2.0,47,139
15,remove,2.0,I,,1HI,1H,2,remove I from position 2,flow_matching,0.3,2.0,47,139
16,add,2.0,l,,1H,1Hl,3,add l at position 2,flow_matching,0.3,2.0,47,139
17,replace,0.0,C,1,1Hl,CHl,3,replace 1 at position 0 with C,flow_matching,0.3,2.0,47,139
18,remove,0.0,C,,CHl,Hl,2,remove C from position 0,flow_matching,0.3,2.0,47,139
19,add,1.0,B,,Hl,HBl,3,add B at position 1,flow_matching,0.3,2.0,47,139
20,replace,0.0,r,H,HBl,rBl,3,replace H at position 0 with r,flow_matching,0.3,2.0,47,139
21,remove,1.0,B,,rBl,rl,2,remove B from position 1,flow_matching,0.3,2.0,47,139
22,replace,0.0,C,r,rl,Cl,2,replace r at position 0 with C,flow_matching,0.3,2.0,47,139
23,replace,0.0,5,C,Cl,5l,2,replace C at position 0 with 5,flow_matching,0.3,2.0,47,139
24,remove,1.0,l,,5l,5,1,remove l from position 1,flow_matching,0.3,2.0,47,139
25,replace,0.0,C,5,5,C,1,replace 5 at position 0 with C,flow_matching,0.3,2.0,47,139
26,replace,0.0,N,C,C,N,1,replace C at position 0 with N,flow_matching,0.3,2.0,47,139
27,replace,0.0,1,N,N,1,1,replace N at position 0 with 1,flow_matching,0.3,2.0,47,139
28,add,0.0,5,,1,51,2,add 5 at position 0,flow_matching,0.3,2.0,47,139
29,add,2.0,5,,51,515,3,add 5 at position 2,flow_matching,0.3,2.0,47,139
30,remove,1.0,1,,515,55,2,remove 1 from position 1,flow_matching,0.3,2.0,47,139
31,remove,1.0,5,,55,5,1,remove 5 from position 1,flow_matching,0.3,2.0,47,139
32,replace,0.0,/,5,5,/,1,replace 5 at position 0 with /,flow_matching,0.3,2.0,47,139
33,remove,0.0,/,,/,,0,remove / from position 0,flow_matching,0.3,2.0,47,139
34,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,47,139
35,replace,0.0,C,O,O,C,1,replace O at position 0 with C,flow_matching,0.3,2.0,47,139
36,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,47,139
37,add,2.0,),,CO,CO),3,add ) at position 2,flow_matching,0.3,2.0,47,139
38,replace,0.0,N,C,CO),NO),3,replace C at position 0 with N,flow_matching,0.3,2.0,47,139
39,replace,0.0,C,N,NO),CO),3,replace N at position 0 with C,flow_matching,0.3,2.0,47,139
40,replace,2.0,c,),CO),COc,3,replace ) at position 2 with c,flow_matching,0.3,2.0,47,139
41,remove,0.0,C,,COc,Oc,2,remove C from position 0,flow_matching,0.3,2.0,47,139
42,remove,1.0,c,,Oc,O,1,remove c from position 1,flow_matching,0.3,2.0,47,139
43,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,47,139
44,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,47,139
45,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,47,139
46,add,2.0,c,,CO,COc,3,add c at position 2,flow_matching,0.3,2.0,47,139
47,add,0.0,#,,COc,#COc,4,add # at position 0,flow_matching,0.3,2.0,47,139
48,add,1.0,/,,#COc,#/COc,5,add / at position 1,flow_matching,0.3,2.0,47,139
49,replace,1.0,B,/,#/COc,#BCOc,5,replace / at position 1 with B,flow_matching,0.3,2.0,47,139
50,replace,2.0,o,C,#BCOc,#BoOc,5,replace C at position 2 with o,flow_matching,0.3,2.0,47,139
51,remove,4.0,c,,#BoOc,#BoO,4,remove c from position 4,flow_matching,0.3,2.0,47,139
52,remove,0.0,#,,#BoO,BoO,3,remove # from position 0,flow_matching,0.3,2.0,47,139
53,replace,1.0,l,o,BoO,BlO,3,replace o at position 1 with l,flow_matching,0.3,2.0,47,139
54,replace,0.0,C,B,BlO,ClO,3,replace B at position 0 with C,flow_matching,0.3,2.0,47,139
55,replace,0.0,H,C,ClO,HlO,3,replace C at position 0 with H,flow_matching,0.3,2.0,47,139
56,remove,0.0,H,,HlO,lO,2,remove H from position 0,flow_matching,0.3,2.0,47,139
57,add,1.0,c,,lO,lcO,3,add c at position 1,flow_matching,0.3,2.0,47,139
58,replace,0.0,C,l,lcO,CcO,3,replace l at position 0 with C,flow_matching,0.3,2.0,47,139
59,remove,1.0,c,,CcO,CO,2,remove c from position 1,flow_matching,0.3,2.0,47,139
60,add,0.0,],,CO,]CO,3,add ] at position 0,flow_matching,0.3,2.0,47,139
61,replace,1.0,O,C,]CO,]OO,3,replace C at position 1 with O,flow_matching,0.3,2.0,47,139
62,replace,0.0,B,],]OO,BOO,3,replace ] at position 0 with B,flow_matching,0.3,2.0,47,139
63,add,2.0,-,,BOO,BO-O,4,add - at position 2,flow_matching,0.3,2.0,47,139
64,remove,0.0,B,,BO-O,O-O,3,remove B from position 0,flow_matching,0.3,2.0,47,139
65,remove,2.0,O,,O-O,O-,2,remove O from position 2,flow_matching,0.3,2.0,47,139
66,add,1.0,+,,O-,O+-,3,add + at position 1,flow_matching,0.3,2.0,47,139
67,replace,1.0,F,+,O+-,OF-,3,replace + at position 1 with F,flow_matching,0.3,2.0,47,139
68,replace,0.0,@,O,OF-,@F-,3,replace O at position 0 with @,flow_matching,0.3,2.0,47,139
69,replace,0.0,C,@,@F-,CF-,3,replace @ at position 0 with C,flow_matching,0.3,2.0,47,139
70,replace,0.0,l,C,CF-,lF-,3,replace C at position 0 with l,flow_matching,0.3,2.0,47,139
71,replace,0.0,C,l,lF-,CF-,3,replace l at position 0 with C,flow_matching,0.3,2.0,47,139
72,add,3.0,],,CF-,CF-],4,add ] at position 3,flow_matching,0.3,2.0,47,139
73,replace,1.0,O,F,CF-],CO-],4,replace F at position 1 with O,flow_matching,0.3,2.0,47,139
74,replace,2.0,c,-,CO-],COc],4,replace - at position 2 with c,flow_matching,0.3,2.0,47,139
75,replace,3.0,2,],COc],COc2,4,replace ] at position 3 with 2,flow_matching,0.3,2.0,47,139
76,remove,0.0,C,,COc2,Oc2,3,remove C from position 0,flow_matching,0.3,2.0,47,139
77,add,3.0,#,,Oc2,Oc2#,4,add # at position 3,flow_matching,0.3,2.0,47,139
78,replace,0.0,C,O,Oc2#,Cc2#,4,replace O at position 0 with C,flow_matching,0.3,2.0,47,139
79,replace,1.0,O,c,Cc2#,CO2#,4,replace c at position 1 with O,flow_matching,0.3,2.0,47,139
80,remove,1.0,O,,CO2#,C2#,3,remove O from position 1,flow_matching,0.3,2.0,47,139
81,replace,1.0,O,2,C2#,CO#,3,replace 2 at position 1 with O,flow_matching,0.3,2.0,47,139
82,replace,0.0,l,C,CO#,lO#,3,replace C at position 0 with l,flow_matching,0.3,2.0,47,139
83,remove,2.0,#,,lO#,lO,2,remove # from position 2,flow_matching,0.3,2.0,47,139
84,remove,0.0,l,,lO,O,1,remove l from position 0,flow_matching,0.3,2.0,47,139
85,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,47,139
86,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,47,139
87,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,47,139
88,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,47,139
89,add,2.0,=,,CO,CO=,3,add = at position 2,flow_matching,0.3,2.0,47,139
90,replace,2.0,c,=,CO=,COc,3,replace = at position 2 with c,flow_matching,0.3,2.0,47,139
91,add,0.0,l,,COc,lCOc,4,add l at position 0,flow_matching,0.3,2.0,47,139
92,replace,0.0,7,l,lCOc,7COc,4,replace l at position 0 with 7,flow_matching,0.3,2.0,47,139
93,replace,0.0,C,7,7COc,CCOc,4,replace 7 at position 0 with C,flow_matching,0.3,2.0,47,139
94,replace,1.0,O,C,CCOc,COOc,4,replace C at position 1 with O,flow_matching,0.3,2.0,47,139
95,replace,2.0,c,O,COOc,COcc,4,replace O at position 2 with c,flow_matching,0.3,2.0,47,139
96,replace,3.0,1,c,COcc,COc1,4,replace c at position 3 with 1,flow_matching,0.3,2.0,47,139
97,add,4.0,c,,COc1,COc1c,5,add c at position 4,flow_matching,0.3,2.0,47,139
98,add,5.0,c,,COc1c,COc1cc,6,add c at position 5,flow_matching,0.3,2.0,47,139
99,add,6.0,c,,COc1cc,COc1ccc,7,add c at position 6,flow_matching,0.3,2.0,47,139
100,add,7.0,c,,COc1ccc,COc1cccc,8,add c at position 7,flow_matching,0.3,2.0,47,139
101,add,8.0,(,,COc1cccc,COc1cccc(,9,add ( at position 8,flow_matching,0.3,2.0,47,139
102,add,9.0,C,,COc1cccc(,COc1cccc(C,10,add C at position 9,flow_matching,0.3,2.0,47,139
103,add,10.0,(,,COc1cccc(C,COc1cccc(C(,11,add ( at position 10,flow_matching,0.3,2.0,47,139
104,add,11.0,=,,COc1cccc(C(,COc1cccc(C(=,12,add = at position 11,flow_matching,0.3,2.0,47,139
105,add,12.0,O,,COc1cccc(C(=,COc1cccc(C(=O,13,add O at position 12,flow_matching,0.3,2.0,47,139
106,add,13.0,),,COc1cccc(C(=O,COc1cccc(C(=O),14,add ) at position 13,flow_matching,0.3,2.0,47,139
107,add,14.0,N,,COc1cccc(C(=O),COc1cccc(C(=O)N,15,add N at position 14,flow_matching,0.3,2.0,47,139
108,add,15.0,[,,COc1cccc(C(=O)N,COc1cccc(C(=O)N[,16,add [ at position 15,flow_matching,0.3,2.0,47,139
109,add,16.0,C,,COc1cccc(C(=O)N[,COc1cccc(C(=O)N[C,17,add C at position 16,flow_matching,0.3,2.0,47,139
110,add,17.0,@,,COc1cccc(C(=O)N[C,COc1cccc(C(=O)N[C@,18,add @ at position 17,flow_matching,0.3,2.0,47,139
111,add,18.0,@,,COc1cccc(C(=O)N[C@,COc1cccc(C(=O)N[C@@,19,add @ at position 18,flow_matching,0.3,2.0,47,139
112,add,19.0,],,COc1cccc(C(=O)N[C@@,COc1cccc(C(=O)N[C@@],20,add ] at position 19,flow_matching,0.3,2.0,47,139
113,add,20.0,(,,COc1cccc(C(=O)N[C@@],COc1cccc(C(=O)N[C@@](,21,add ( at position 20,flow_matching,0.3,2.0,47,139
114,add,21.0,C,,COc1cccc(C(=O)N[C@@](,COc1cccc(C(=O)N[C@@](C,22,add C at position 21,flow_matching,0.3,2.0,47,139
115,add,22.0,),,COc1cccc(C(=O)N[C@@](C,COc1cccc(C(=O)N[C@@](C),23,add ) at position 22,flow_matching,0.3,2.0,47,139
116,add,23.0,(,,COc1cccc(C(=O)N[C@@](C),COc1cccc(C(=O)N[C@@](C)(,24,add ( at position 23,flow_matching,0.3,2.0,47,139
117,add,24.0,C,,COc1cccc(C(=O)N[C@@](C)(,COc1cccc(C(=O)N[C@@](C)(C,25,add C at position 24,flow_matching,0.3,2.0,47,139
118,add,25.0,(,,COc1cccc(C(=O)N[C@@](C)(C,COc1cccc(C(=O)N[C@@](C)(C(,26,add ( at position 25,flow_matching,0.3,2.0,47,139
119,add,26.0,N,,COc1cccc(C(=O)N[C@@](C)(C(,COc1cccc(C(=O)N[C@@](C)(C(N,27,add N at position 26,flow_matching,0.3,2.0,47,139
120,add,27.0,),,COc1cccc(C(=O)N[C@@](C)(C(N,COc1cccc(C(=O)N[C@@](C)(C(N),28,add ) at position 27,flow_matching,0.3,2.0,47,139
121,add,28.0,=,,COc1cccc(C(=O)N[C@@](C)(C(N),COc1cccc(C(=O)N[C@@](C)(C(N)=,29,add = at position 28,flow_matching,0.3,2.0,47,139
122,add,29.0,O,,COc1cccc(C(=O)N[C@@](C)(C(N)=,COc1cccc(C(=O)N[C@@](C)(C(N)=O,30,add O at position 29,flow_matching,0.3,2.0,47,139
123,add,30.0,),,COc1cccc(C(=O)N[C@@](C)(C(N)=O,COc1cccc(C(=O)N[C@@](C)(C(N)=O),31,add ) at position 30,flow_matching,0.3,2.0,47,139
124,add,31.0,c,,COc1cccc(C(=O)N[C@@](C)(C(N)=O),COc1cccc(C(=O)N[C@@](C)(C(N)=O)c,32,add c at position 31,flow_matching,0.3,2.0,47,139
125,add,32.0,2,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2,33,add 2 at position 32,flow_matching,0.3,2.0,47,139
126,add,33.0,c,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2c,34,add c at position 33,flow_matching,0.3,2.0,47,139
127,add,34.0,c,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2c,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cc,35,add c at position 34,flow_matching,0.3,2.0,47,139
128,add,35.0,c,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cc,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2ccc,36,add c at position 35,flow_matching,0.3,2.0,47,139
129,add,36.0,c,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2ccc,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc,37,add c at position 36,flow_matching,0.3,2.0,47,139
130,add,37.0,(,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(,38,add ( at position 37,flow_matching,0.3,2.0,47,139
131,add,38.0,C,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(C,39,add C at position 38,flow_matching,0.3,2.0,47,139
132,add,39.0,l,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(C,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl,40,add l at position 39,flow_matching,0.3,2.0,47,139
133,add,40.0,),,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl),41,add ) at position 40,flow_matching,0.3,2.0,47,139
134,add,41.0,c,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl),COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c,42,add c at position 41,flow_matching,0.3,2.0,47,139
135,add,42.0,2,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c2,43,add 2 at position 42,flow_matching,0.3,2.0,47,139
136,add,43.0,),,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c2,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c2),44,add ) at position 43,flow_matching,0.3,2.0,47,139
137,add,44.0,c,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c2),COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c2)c,45,add c at position 44,flow_matching,0.3,2.0,47,139
138,add,45.0,1,,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c2)c,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c2)c1,46,add 1 at position 45,flow_matching,0.3,2.0,47,139
139,add,46.0,"
",,COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c2)c1,"COc1cccc(C(=O)N[C@@](C)(C(N)=O)c2cccc(Cl)c2)c1
",47,"add 
 at position 46",flow_matching,0.3,2.0,47,139
