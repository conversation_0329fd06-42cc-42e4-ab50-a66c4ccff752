step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,58,187
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,58,187
2,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,58,187
3,replace,0.0,B,O,O=,B=,2,replace O at position 0 with B,flow_matching,0.3,2.0,58,187
4,replace,0.0,O,B,B=,O=,2,replace B at position 0 with O,flow_matching,0.3,2.0,58,187
5,add,1.0,1,,O=,O1=,3,add 1 at position 1,flow_matching,0.3,2.0,58,187
6,add,0.0,H,,O1=,HO1=,4,add H at position 0,flow_matching,0.3,2.0,58,187
7,remove,0.0,H,,HO1=,O1=,3,remove H from position 0,flow_matching,0.3,2.0,58,187
8,replace,1.0,=,1,O1=,O==,3,replace 1 at position 1 with =,flow_matching,0.3,2.0,58,187
9,replace,2.0,C,=,O==,O=C,3,replace = at position 2 with C,flow_matching,0.3,2.0,58,187
10,remove,0.0,O,,O=C,=C,2,remove O from position 0,flow_matching,0.3,2.0,58,187
11,replace,0.0,[,=,=C,[C,2,replace = at position 0 with [,flow_matching,0.3,2.0,58,187
12,replace,1.0,),C,[C,[),2,replace C at position 1 with ),flow_matching,0.3,2.0,58,187
13,replace,0.0,B,[,[),B),2,replace [ at position 0 with B,flow_matching,0.3,2.0,58,187
14,replace,1.0,5,),B),B5,2,replace ) at position 1 with 5,flow_matching,0.3,2.0,58,187
15,replace,0.0,O,B,B5,O5,2,replace B at position 0 with O,flow_matching,0.3,2.0,58,187
16,replace,0.0,F,O,O5,F5,2,replace O at position 0 with F,flow_matching,0.3,2.0,58,187
17,add,1.0,C,,F5,FC5,3,add C at position 1,flow_matching,0.3,2.0,58,187
18,add,0.0,-,,FC5,-FC5,4,add - at position 0,flow_matching,0.3,2.0,58,187
19,add,0.0,N,,-FC5,N-FC5,5,add N at position 0,flow_matching,0.3,2.0,58,187
20,replace,0.0,O,N,N-FC5,O-FC5,5,replace N at position 0 with O,flow_matching,0.3,2.0,58,187
21,remove,1.0,-,,O-FC5,OFC5,4,remove - from position 1,flow_matching,0.3,2.0,58,187
22,replace,1.0,o,F,OFC5,OoC5,4,replace F at position 1 with o,flow_matching,0.3,2.0,58,187
23,replace,2.0,s,C,OoC5,Oos5,4,replace C at position 2 with s,flow_matching,0.3,2.0,58,187
24,replace,1.0,=,o,Oos5,O=s5,4,replace o at position 1 with =,flow_matching,0.3,2.0,58,187
25,replace,3.0,\,5,O=s5,O=s\,4,replace 5 at position 3 with \,flow_matching,0.3,2.0,58,187
26,add,2.0,c,,O=s\,O=cs\,5,add c at position 2,flow_matching,0.3,2.0,58,187
27,add,0.0,3,,O=cs\,3O=cs\,6,add 3 at position 0,flow_matching,0.3,2.0,58,187
28,remove,0.0,3,,3O=cs\,O=cs\,5,remove 3 from position 0,flow_matching,0.3,2.0,58,187
29,replace,2.0,C,c,O=cs\,O=Cs\,5,replace c at position 2 with C,flow_matching,0.3,2.0,58,187
30,replace,1.0,-,=,O=Cs\,O-Cs\,5,replace = at position 1 with -,flow_matching,0.3,2.0,58,187
31,add,4.0,n,,O-Cs\,O-Csn\,6,add n at position 4,flow_matching,0.3,2.0,58,187
32,add,5.0,5,,O-Csn\,O-Csn5\,7,add 5 at position 5,flow_matching,0.3,2.0,58,187
33,replace,0.0,r,O,O-Csn5\,r-Csn5\,7,replace O at position 0 with r,flow_matching,0.3,2.0,58,187
34,replace,2.0,I,C,r-Csn5\,r-Isn5\,7,replace C at position 2 with I,flow_matching,0.3,2.0,58,187
35,replace,0.0,O,r,r-Isn5\,O-Isn5\,7,replace r at position 0 with O,flow_matching,0.3,2.0,58,187
36,add,3.0,B,,O-Isn5\,O-IBsn5\,8,add B at position 3,flow_matching,0.3,2.0,58,187
37,replace,1.0,=,-,O-IBsn5\,O=IBsn5\,8,replace - at position 1 with =,flow_matching,0.3,2.0,58,187
38,replace,2.0,C,I,O=IBsn5\,O=CBsn5\,8,replace I at position 2 with C,flow_matching,0.3,2.0,58,187
39,replace,3.0,\,B,O=CBsn5\,O=C\sn5\,8,replace B at position 3 with \,flow_matching,0.3,2.0,58,187
40,replace,2.0,+,C,O=C\sn5\,O=+\sn5\,8,replace C at position 2 with +,flow_matching,0.3,2.0,58,187
41,add,6.0,S,,O=+\sn5\,O=+\snS5\,9,add S at position 6,flow_matching,0.3,2.0,58,187
42,replace,2.0,C,+,O=+\snS5\,O=C\snS5\,9,replace + at position 2 with C,flow_matching,0.3,2.0,58,187
43,add,1.0,c,,O=C\snS5\,Oc=C\snS5\,10,add c at position 1,flow_matching,0.3,2.0,58,187
44,replace,1.0,=,c,Oc=C\snS5\,O==C\snS5\,10,replace c at position 1 with =,flow_matching,0.3,2.0,58,187
45,replace,8.0,6,5,O==C\snS5\,O==C\snS6\,10,replace 5 at position 8 with 6,flow_matching,0.3,2.0,58,187
46,add,3.0,7,,O==C\snS6\,O==7C\snS6\,11,add 7 at position 3,flow_matching,0.3,2.0,58,187
47,replace,2.0,C,=,O==7C\snS6\,O=C7C\snS6\,11,replace = at position 2 with C,flow_matching,0.3,2.0,58,187
48,replace,8.0,=,S,O=C7C\snS6\,O=C7C\sn=6\,11,replace S at position 8 with =,flow_matching,0.3,2.0,58,187
49,remove,2.0,C,,O=C7C\sn=6\,O=7C\sn=6\,10,remove C from position 2,flow_matching,0.3,2.0,58,187
50,replace,6.0,=,n,O=7C\sn=6\,O=7C\s==6\,10,replace n at position 6 with =,flow_matching,0.3,2.0,58,187
51,add,2.0,c,,O=7C\s==6\,O=c7C\s==6\,11,add c at position 2,flow_matching,0.3,2.0,58,187
52,replace,2.0,C,c,O=c7C\s==6\,O=C7C\s==6\,11,replace c at position 2 with C,flow_matching,0.3,2.0,58,187
53,replace,3.0,(,7,O=C7C\s==6\,O=C(C\s==6\,11,replace 7 at position 3 with (,flow_matching,0.3,2.0,58,187
54,replace,5.0,1,\,O=C(C\s==6\,O=C(C1s==6\,11,replace \ at position 5 with 1,flow_matching,0.3,2.0,58,187
55,add,7.0,H,,O=C(C1s==6\,O=C(C1sH==6\,12,add H at position 7,flow_matching,0.3,2.0,58,187
56,remove,5.0,1,,O=C(C1sH==6\,O=C(CsH==6\,11,remove 1 from position 5,flow_matching,0.3,2.0,58,187
57,remove,5.0,s,,O=C(CsH==6\,O=C(CH==6\,10,remove s from position 5,flow_matching,0.3,2.0,58,187
58,add,0.0,n,,O=C(CH==6\,nO=C(CH==6\,11,add n at position 0,flow_matching,0.3,2.0,58,187
59,remove,2.0,=,,nO=C(CH==6\,nOC(CH==6\,10,remove = from position 2,flow_matching,0.3,2.0,58,187
60,add,10.0,c,,nOC(CH==6\,nOC(CH==6\c,11,add c at position 10,flow_matching,0.3,2.0,58,187
61,replace,4.0,[,C,nOC(CH==6\c,nOC([H==6\c,11,replace C at position 4 with [,flow_matching,0.3,2.0,58,187
62,add,6.0,=,,nOC([H==6\c,nOC([H===6\c,12,add = at position 6,flow_matching,0.3,2.0,58,187
63,replace,0.0,O,n,nOC([H===6\c,OOC([H===6\c,12,replace n at position 0 with O,flow_matching,0.3,2.0,58,187
64,replace,6.0,5,=,OOC([H===6\c,OOC([H5==6\c,12,replace = at position 6 with 5,flow_matching,0.3,2.0,58,187
65,add,9.0,F,,OOC([H5==6\c,OOC([H5==F6\c,13,add F at position 9,flow_matching,0.3,2.0,58,187
66,replace,7.0,F,=,OOC([H5==F6\c,OOC([H5F=F6\c,13,replace = at position 7 with F,flow_matching,0.3,2.0,58,187
67,add,9.0,/,,OOC([H5F=F6\c,OOC([H5F=/F6\c,14,add / at position 9,flow_matching,0.3,2.0,58,187
68,replace,6.0,l,5,OOC([H5F=/F6\c,OOC([HlF=/F6\c,14,replace 5 at position 6 with l,flow_matching,0.3,2.0,58,187
69,add,5.0,r,,OOC([HlF=/F6\c,OOC([rHlF=/F6\c,15,add r at position 5,flow_matching,0.3,2.0,58,187
70,add,6.0,/,,OOC([rHlF=/F6\c,OOC([r/HlF=/F6\c,16,add / at position 6,flow_matching,0.3,2.0,58,187
71,add,8.0,[,,OOC([r/HlF=/F6\c,OOC([r/H[lF=/F6\c,17,add [ at position 8,flow_matching,0.3,2.0,58,187
72,replace,16.0,1,c,OOC([r/H[lF=/F6\c,OOC([r/H[lF=/F6\1,17,replace c at position 16 with 1,flow_matching,0.3,2.0,58,187
73,replace,14.0,+,6,OOC([r/H[lF=/F6\1,OOC([r/H[lF=/F+\1,17,replace 6 at position 14 with +,flow_matching,0.3,2.0,58,187
74,add,1.0,3,,OOC([r/H[lF=/F+\1,O3OC([r/H[lF=/F+\1,18,add 3 at position 1,flow_matching,0.3,2.0,58,187
75,replace,13.0,I,/,O3OC([r/H[lF=/F+\1,O3OC([r/H[lF=IF+\1,18,replace / at position 13 with I,flow_matching,0.3,2.0,58,187
76,replace,1.0,=,3,O3OC([r/H[lF=IF+\1,O=OC([r/H[lF=IF+\1,18,replace 3 at position 1 with =,flow_matching,0.3,2.0,58,187
77,add,0.0,r,,O=OC([r/H[lF=IF+\1,rO=OC([r/H[lF=IF+\1,19,add r at position 0,flow_matching,0.3,2.0,58,187
78,add,8.0,B,,rO=OC([r/H[lF=IF+\1,rO=OC([rB/H[lF=IF+\1,20,add B at position 8,flow_matching,0.3,2.0,58,187
79,replace,0.0,O,r,rO=OC([rB/H[lF=IF+\1,OO=OC([rB/H[lF=IF+\1,20,replace r at position 0 with O,flow_matching,0.3,2.0,58,187
80,remove,6.0,[,,OO=OC([rB/H[lF=IF+\1,OO=OC(rB/H[lF=IF+\1,19,remove [ from position 6,flow_matching,0.3,2.0,58,187
81,remove,11.0,l,,OO=OC(rB/H[lF=IF+\1,OO=OC(rB/H[F=IF+\1,18,remove l from position 11,flow_matching,0.3,2.0,58,187
82,replace,13.0,6,I,OO=OC(rB/H[F=IF+\1,OO=OC(rB/H[F=6F+\1,18,replace I at position 13 with 6,flow_matching,0.3,2.0,58,187
83,remove,12.0,=,,OO=OC(rB/H[F=6F+\1,OO=OC(rB/H[F6F+\1,17,remove = from position 12,flow_matching,0.3,2.0,58,187
84,add,6.0,2,,OO=OC(rB/H[F6F+\1,OO=OC(2rB/H[F6F+\1,18,add 2 at position 6,flow_matching,0.3,2.0,58,187
85,add,12.0,=,,OO=OC(2rB/H[F6F+\1,OO=OC(2rB/H[=F6F+\1,19,add = at position 12,flow_matching,0.3,2.0,58,187
86,replace,5.0,7,(,OO=OC(2rB/H[=F6F+\1,OO=OC72rB/H[=F6F+\1,19,replace ( at position 5 with 7,flow_matching,0.3,2.0,58,187
87,replace,1.0,=,O,OO=OC72rB/H[=F6F+\1,O==OC72rB/H[=F6F+\1,19,replace O at position 1 with =,flow_matching,0.3,2.0,58,187
88,replace,2.0,C,=,O==OC72rB/H[=F6F+\1,O=COC72rB/H[=F6F+\1,19,replace = at position 2 with C,flow_matching,0.3,2.0,58,187
89,replace,3.0,(,O,O=COC72rB/H[=F6F+\1,O=C(C72rB/H[=F6F+\1,19,replace O at position 3 with (,flow_matching,0.3,2.0,58,187
90,add,9.0,],,O=C(C72rB/H[=F6F+\1,O=C(C72rB]/H[=F6F+\1,20,add ] at position 9,flow_matching,0.3,2.0,58,187
91,replace,15.0,N,6,O=C(C72rB]/H[=F6F+\1,O=C(C72rB]/H[=FNF+\1,20,replace 6 at position 15 with N,flow_matching,0.3,2.0,58,187
92,remove,4.0,C,,O=C(C72rB]/H[=FNF+\1,O=C(72rB]/H[=FNF+\1,19,remove C from position 4,flow_matching,0.3,2.0,58,187
93,replace,12.0,r,=,O=C(72rB]/H[=FNF+\1,O=C(72rB]/H[rFNF+\1,19,replace = at position 12 with r,flow_matching,0.3,2.0,58,187
94,replace,13.0,7,F,O=C(72rB]/H[rFNF+\1,O=C(72rB]/H[r7NF+\1,19,replace F at position 13 with 7,flow_matching,0.3,2.0,58,187
95,remove,7.0,B,,O=C(72rB]/H[r7NF+\1,O=C(72r]/H[r7NF+\1,18,remove B from position 7,flow_matching,0.3,2.0,58,187
96,add,0.0,C,,O=C(72r]/H[r7NF+\1,CO=C(72r]/H[r7NF+\1,19,add C at position 0,flow_matching,0.3,2.0,58,187
97,remove,1.0,O,,CO=C(72r]/H[r7NF+\1,C=C(72r]/H[r7NF+\1,18,remove O from position 1,flow_matching,0.3,2.0,58,187
98,replace,0.0,O,C,C=C(72r]/H[r7NF+\1,O=C(72r]/H[r7NF+\1,18,replace C at position 0 with O,flow_matching,0.3,2.0,58,187
99,replace,14.0,#,F,O=C(72r]/H[r7NF+\1,O=C(72r]/H[r7N#+\1,18,replace F at position 14 with #,flow_matching,0.3,2.0,58,187
100,replace,4.0,C,7,O=C(72r]/H[r7N#+\1,O=C(C2r]/H[r7N#+\1,18,replace 7 at position 4 with C,flow_matching,0.3,2.0,58,187
101,replace,17.0,N,1,O=C(C2r]/H[r7N#+\1,O=C(C2r]/H[r7N#+\N,18,replace 1 at position 17 with N,flow_matching,0.3,2.0,58,187
102,replace,5.0,1,2,O=C(C2r]/H[r7N#+\N,O=C(C1r]/H[r7N#+\N,18,replace 2 at position 5 with 1,flow_matching,0.3,2.0,58,187
103,replace,6.0,C,r,O=C(C1r]/H[r7N#+\N,O=C(C1C]/H[r7N#+\N,18,replace r at position 6 with C,flow_matching,0.3,2.0,58,187
104,replace,1.0,S,=,O=C(C1C]/H[r7N#+\N,OSC(C1C]/H[r7N#+\N,18,replace = at position 1 with S,flow_matching,0.3,2.0,58,187
105,replace,1.0,=,S,OSC(C1C]/H[r7N#+\N,O=C(C1C]/H[r7N#+\N,18,replace S at position 1 with =,flow_matching,0.3,2.0,58,187
106,replace,7.0,C,],O=C(C1C]/H[r7N#+\N,O=C(C1CC/H[r7N#+\N,18,replace ] at position 7 with C,flow_matching,0.3,2.0,58,187
107,remove,4.0,C,,O=C(C1CC/H[r7N#+\N,O=C(1CC/H[r7N#+\N,17,remove C from position 4,flow_matching,0.3,2.0,58,187
108,add,5.0,s,,O=C(1CC/H[r7N#+\N,O=C(1sCC/H[r7N#+\N,18,add s at position 5,flow_matching,0.3,2.0,58,187
109,add,6.0,#,,O=C(1sCC/H[r7N#+\N,O=C(1s#CC/H[r7N#+\N,19,add # at position 6,flow_matching,0.3,2.0,58,187
110,replace,4.0,C,1,O=C(1s#CC/H[r7N#+\N,O=C(Cs#CC/H[r7N#+\N,19,replace 1 at position 4 with C,flow_matching,0.3,2.0,58,187
111,replace,5.0,1,s,O=C(Cs#CC/H[r7N#+\N,O=C(C1#CC/H[r7N#+\N,19,replace s at position 5 with 1,flow_matching,0.3,2.0,58,187
112,remove,1.0,=,,O=C(C1#CC/H[r7N#+\N,OC(C1#CC/H[r7N#+\N,18,remove = from position 1,flow_matching,0.3,2.0,58,187
113,add,3.0,r,,OC(C1#CC/H[r7N#+\N,OC(rC1#CC/H[r7N#+\N,19,add r at position 3,flow_matching,0.3,2.0,58,187
114,replace,1.0,=,C,OC(rC1#CC/H[r7N#+\N,O=(rC1#CC/H[r7N#+\N,19,replace C at position 1 with =,flow_matching,0.3,2.0,58,187
115,add,1.0,#,,O=(rC1#CC/H[r7N#+\N,O#=(rC1#CC/H[r7N#+\N,20,add # at position 1,flow_matching,0.3,2.0,58,187
116,replace,1.0,=,#,O#=(rC1#CC/H[r7N#+\N,O==(rC1#CC/H[r7N#+\N,20,replace # at position 1 with =,flow_matching,0.3,2.0,58,187
117,replace,2.0,C,=,O==(rC1#CC/H[r7N#+\N,O=C(rC1#CC/H[r7N#+\N,20,replace = at position 2 with C,flow_matching,0.3,2.0,58,187
118,add,5.0,3,,O=C(rC1#CC/H[r7N#+\N,O=C(r3C1#CC/H[r7N#+\N,21,add 3 at position 5,flow_matching,0.3,2.0,58,187
119,replace,7.0,],1,O=C(r3C1#CC/H[r7N#+\N,O=C(r3C]#CC/H[r7N#+\N,21,replace 1 at position 7 with ],flow_matching,0.3,2.0,58,187
120,remove,19.0,\,,O=C(r3C]#CC/H[r7N#+\N,O=C(r3C]#CC/H[r7N#+N,20,remove \ from position 19,flow_matching,0.3,2.0,58,187
121,remove,7.0,],,O=C(r3C]#CC/H[r7N#+N,O=C(r3C#CC/H[r7N#+N,19,remove ] from position 7,flow_matching,0.3,2.0,58,187
122,add,0.0,=,,O=C(r3C#CC/H[r7N#+N,=O=C(r3C#CC/H[r7N#+N,20,add = at position 0,flow_matching,0.3,2.0,58,187
123,add,11.0,C,,=O=C(r3C#CC/H[r7N#+N,=O=C(r3C#CCC/H[r7N#+N,21,add C at position 11,flow_matching,0.3,2.0,58,187
124,add,20.0,),,=O=C(r3C#CCC/H[r7N#+N,=O=C(r3C#CCC/H[r7N#+)N,22,add ) at position 20,flow_matching,0.3,2.0,58,187
125,add,20.0,l,,=O=C(r3C#CCC/H[r7N#+)N,=O=C(r3C#CCC/H[r7N#+l)N,23,add l at position 20,flow_matching,0.3,2.0,58,187
126,replace,0.0,O,=,=O=C(r3C#CCC/H[r7N#+l)N,OO=C(r3C#CCC/H[r7N#+l)N,23,replace = at position 0 with O,flow_matching,0.3,2.0,58,187
127,remove,10.0,C,,OO=C(r3C#CCC/H[r7N#+l)N,OO=C(r3C#CC/H[r7N#+l)N,22,remove C from position 10,flow_matching,0.3,2.0,58,187
128,remove,8.0,#,,OO=C(r3C#CC/H[r7N#+l)N,OO=C(r3CCC/H[r7N#+l)N,21,remove # from position 8,flow_matching,0.3,2.0,58,187
129,remove,14.0,7,,OO=C(r3CCC/H[r7N#+l)N,OO=C(r3CCC/H[rN#+l)N,20,remove 7 from position 14,flow_matching,0.3,2.0,58,187
130,remove,10.0,/,,OO=C(r3CCC/H[rN#+l)N,OO=C(r3CCCH[rN#+l)N,19,remove / from position 10,flow_matching,0.3,2.0,58,187
131,replace,11.0,I,[,OO=C(r3CCCH[rN#+l)N,OO=C(r3CCCHIrN#+l)N,19,replace [ at position 11 with I,flow_matching,0.3,2.0,58,187
132,replace,1.0,=,O,OO=C(r3CCCHIrN#+l)N,O==C(r3CCCHIrN#+l)N,19,replace O at position 1 with =,flow_matching,0.3,2.0,58,187
133,replace,2.0,C,=,O==C(r3CCCHIrN#+l)N,O=CC(r3CCCHIrN#+l)N,19,replace = at position 2 with C,flow_matching,0.3,2.0,58,187
134,replace,3.0,(,C,O=CC(r3CCCHIrN#+l)N,O=C((r3CCCHIrN#+l)N,19,replace C at position 3 with (,flow_matching,0.3,2.0,58,187
135,replace,4.0,C,(,O=C((r3CCCHIrN#+l)N,O=C(Cr3CCCHIrN#+l)N,19,replace ( at position 4 with C,flow_matching,0.3,2.0,58,187
136,replace,5.0,1,r,O=C(Cr3CCCHIrN#+l)N,O=C(C13CCCHIrN#+l)N,19,replace r at position 5 with 1,flow_matching,0.3,2.0,58,187
137,replace,6.0,C,3,O=C(C13CCCHIrN#+l)N,O=C(C1CCCCHIrN#+l)N,19,replace 3 at position 6 with C,flow_matching,0.3,2.0,58,187
138,replace,8.0,1,C,O=C(C1CCCCHIrN#+l)N,O=C(C1CC1CHIrN#+l)N,19,replace C at position 8 with 1,flow_matching,0.3,2.0,58,187
139,replace,9.0,),C,O=C(C1CC1CHIrN#+l)N,O=C(C1CC1)HIrN#+l)N,19,replace C at position 9 with ),flow_matching,0.3,2.0,58,187
140,replace,10.0,N,H,O=C(C1CC1)HIrN#+l)N,O=C(C1CC1)NIrN#+l)N,19,replace H at position 10 with N,flow_matching,0.3,2.0,58,187
141,replace,11.0,1,I,O=C(C1CC1)NIrN#+l)N,O=C(C1CC1)N1rN#+l)N,19,replace I at position 11 with 1,flow_matching,0.3,2.0,58,187
142,replace,12.0,C,r,O=C(C1CC1)N1rN#+l)N,O=C(C1CC1)N1CN#+l)N,19,replace r at position 12 with C,flow_matching,0.3,2.0,58,187
143,replace,13.0,C,N,O=C(C1CC1)N1CN#+l)N,O=C(C1CC1)N1CC#+l)N,19,replace N at position 13 with C,flow_matching,0.3,2.0,58,187
144,replace,14.0,C,#,O=C(C1CC1)N1CC#+l)N,O=C(C1CC1)N1CCC+l)N,19,replace # at position 14 with C,flow_matching,0.3,2.0,58,187
145,replace,15.0,[,+,O=C(C1CC1)N1CCC+l)N,O=C(C1CC1)N1CCC[l)N,19,replace + at position 15 with [,flow_matching,0.3,2.0,58,187
146,replace,16.0,C,l,O=C(C1CC1)N1CCC[l)N,O=C(C1CC1)N1CCC[C)N,19,replace l at position 16 with C,flow_matching,0.3,2.0,58,187
147,replace,17.0,@,),O=C(C1CC1)N1CCC[C)N,O=C(C1CC1)N1CCC[C@N,19,replace ) at position 17 with @,flow_matching,0.3,2.0,58,187
148,replace,18.0,H,N,O=C(C1CC1)N1CCC[C@N,O=C(C1CC1)N1CCC[C@H,19,replace N at position 18 with H,flow_matching,0.3,2.0,58,187
149,add,19.0,],,O=C(C1CC1)N1CCC[C@H,O=C(C1CC1)N1CCC[C@H],20,add ] at position 19,flow_matching,0.3,2.0,58,187
150,add,20.0,(,,O=C(C1CC1)N1CCC[C@H],O=C(C1CC1)N1CCC[C@H](,21,add ( at position 20,flow_matching,0.3,2.0,58,187
151,add,21.0,C,,O=C(C1CC1)N1CCC[C@H](,O=C(C1CC1)N1CCC[C@H](C,22,add C at position 21,flow_matching,0.3,2.0,58,187
152,add,22.0,n,,O=C(C1CC1)N1CCC[C@H](C,O=C(C1CC1)N1CCC[C@H](Cn,23,add n at position 22,flow_matching,0.3,2.0,58,187
153,add,23.0,2,,O=C(C1CC1)N1CCC[C@H](Cn,O=C(C1CC1)N1CCC[C@H](Cn2,24,add 2 at position 23,flow_matching,0.3,2.0,58,187
154,add,24.0,c,,O=C(C1CC1)N1CCC[C@H](Cn2,O=C(C1CC1)N1CCC[C@H](Cn2c,25,add c at position 24,flow_matching,0.3,2.0,58,187
155,add,25.0,c,,O=C(C1CC1)N1CCC[C@H](Cn2c,O=C(C1CC1)N1CCC[C@H](Cn2cc,26,add c at position 25,flow_matching,0.3,2.0,58,187
156,add,26.0,[,,O=C(C1CC1)N1CCC[C@H](Cn2cc,O=C(C1CC1)N1CCC[C@H](Cn2cc[,27,add [ at position 26,flow_matching,0.3,2.0,58,187
157,add,27.0,n,,O=C(C1CC1)N1CCC[C@H](Cn2cc[,O=C(C1CC1)N1CCC[C@H](Cn2cc[n,28,add n at position 27,flow_matching,0.3,2.0,58,187
158,add,28.0,H,,O=C(C1CC1)N1CCC[C@H](Cn2cc[n,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH,29,add H at position 28,flow_matching,0.3,2.0,58,187
159,add,29.0,+,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+,30,add + at position 29,flow_matching,0.3,2.0,58,187
160,add,30.0,],,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+],31,add ] at position 30,flow_matching,0.3,2.0,58,187
161,add,31.0,c,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+],O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c,32,add c at position 31,flow_matching,0.3,2.0,58,187
162,add,32.0,2,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2,33,add 2 at position 32,flow_matching,0.3,2.0,58,187
163,add,33.0,-,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-,34,add - at position 33,flow_matching,0.3,2.0,58,187
164,add,34.0,c,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c,35,add c at position 34,flow_matching,0.3,2.0,58,187
165,add,35.0,2,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2,36,add 2 at position 35,flow_matching,0.3,2.0,58,187
166,add,36.0,c,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2c,37,add c at position 36,flow_matching,0.3,2.0,58,187
167,add,37.0,c,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2c,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc,38,add c at position 37,flow_matching,0.3,2.0,58,187
168,add,38.0,3,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3,39,add 3 at position 38,flow_matching,0.3,2.0,58,187
169,add,39.0,n,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n,40,add n at position 39,flow_matching,0.3,2.0,58,187
170,add,40.0,(,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(,41,add ( at position 40,flow_matching,0.3,2.0,58,187
171,add,41.0,n,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n,42,add n at position 41,flow_matching,0.3,2.0,58,187
172,add,42.0,2,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2,43,add 2 at position 42,flow_matching,0.3,2.0,58,187
173,add,43.0,),,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2),44,add ) at position 43,flow_matching,0.3,2.0,58,187
174,add,44.0,C,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2),O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)C,45,add C at position 44,flow_matching,0.3,2.0,58,187
175,add,45.0,C,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)C,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC,46,add C at position 45,flow_matching,0.3,2.0,58,187
176,add,46.0,[,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[,47,add [ at position 46,flow_matching,0.3,2.0,58,187
177,add,47.0,N,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[N,48,add N at position 47,flow_matching,0.3,2.0,58,187
178,add,48.0,H,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[N,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH,49,add H at position 48,flow_matching,0.3,2.0,58,187
179,add,49.0,2,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2,50,add 2 at position 49,flow_matching,0.3,2.0,58,187
180,add,50.0,+,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+,51,add + at position 50,flow_matching,0.3,2.0,58,187
181,add,51.0,],,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+],52,add ] at position 51,flow_matching,0.3,2.0,58,187
182,add,52.0,C,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+],O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C,53,add C at position 52,flow_matching,0.3,2.0,58,187
183,add,53.0,3,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C3,54,add 3 at position 53,flow_matching,0.3,2.0,58,187
184,add,54.0,),,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C3,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C3),55,add ) at position 54,flow_matching,0.3,2.0,58,187
185,add,55.0,C,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C3),O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C3)C,56,add C at position 55,flow_matching,0.3,2.0,58,187
186,add,56.0,1,,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C3)C,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C3)C1,57,add 1 at position 56,flow_matching,0.3,2.0,58,187
187,add,57.0,"
",,O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C3)C1,"O=C(C1CC1)N1CCC[C@H](Cn2cc[nH+]c2-c2cc3n(n2)CC[NH2+]C3)C1
",58,"add 
 at position 57",flow_matching,0.3,2.0,58,187
