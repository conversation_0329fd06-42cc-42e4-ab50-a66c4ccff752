step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,53,228
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,53,228
2,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,53,228
3,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,53,228
4,remove,0.0,N,,N,,0,remove N from position 0,flow_matching,0.3,2.0,53,228
5,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,53,228
6,replace,0.0,H,B,B,H,1,replace <PERSON> at position 0 with H,flow_matching,0.3,2.0,53,228
7,add,1.0,#,,H,H#,2,add # at position 1,flow_matching,0.3,2.0,53,228
8,remove,0.0,H,,H#,#,1,remove H from position 0,flow_matching,0.3,2.0,53,228
9,add,1.0,7,,#,#7,2,add 7 at position 1,flow_matching,0.3,2.0,53,228
10,replace,0.0,C,#,#7,C7,2,replace # at position 0 with C,flow_matching,0.3,2.0,53,228
11,add,2.0,\,,C7,C7\,3,add \ at position 2,flow_matching,0.3,2.0,53,228
12,replace,0.0,[,C,C7\,[7\,3,replace C at position 0 with [,flow_matching,0.3,2.0,53,228
13,replace,0.0,B,[,[7\,B7\,3,replace [ at position 0 with B,flow_matching,0.3,2.0,53,228
14,replace,0.0,C,B,B7\,C7\,3,replace B at position 0 with C,flow_matching,0.3,2.0,53,228
15,replace,1.0,c,7,C7\,Cc\,3,replace 7 at position 1 with c,flow_matching,0.3,2.0,53,228
16,replace,0.0,6,C,Cc\,6c\,3,replace C at position 0 with 6,flow_matching,0.3,2.0,53,228
17,replace,0.0,C,6,6c\,Cc\,3,replace 6 at position 0 with C,flow_matching,0.3,2.0,53,228
18,add,2.0,(,,Cc\,Cc(\,4,add ( at position 2,flow_matching,0.3,2.0,53,228
19,add,1.0,c,,Cc(\,Ccc(\,5,add c at position 1,flow_matching,0.3,2.0,53,228
20,add,4.0,n,,Ccc(\,Ccc(n\,6,add n at position 4,flow_matching,0.3,2.0,53,228
21,remove,1.0,c,,Ccc(n\,Cc(n\,5,remove c from position 1,flow_matching,0.3,2.0,53,228
22,replace,3.0,3,n,Cc(n\,Cc(3\,5,replace n at position 3 with 3,flow_matching,0.3,2.0,53,228
23,add,3.0,l,,Cc(3\,Cc(l3\,6,add l at position 3,flow_matching,0.3,2.0,53,228
24,add,6.0,3,,Cc(l3\,Cc(l3\3,7,add 3 at position 6,flow_matching,0.3,2.0,53,228
25,add,7.0,C,,Cc(l3\3,Cc(l3\3C,8,add C at position 7,flow_matching,0.3,2.0,53,228
26,replace,2.0,1,(,Cc(l3\3C,Cc1l3\3C,8,replace ( at position 2 with 1,flow_matching,0.3,2.0,53,228
27,replace,4.0,l,3,Cc1l3\3C,Cc1ll\3C,8,replace 3 at position 4 with l,flow_matching,0.3,2.0,53,228
28,add,1.0,(,,Cc1ll\3C,C(c1ll\3C,9,add ( at position 1,flow_matching,0.3,2.0,53,228
29,replace,1.0,c,(,C(c1ll\3C,Ccc1ll\3C,9,replace ( at position 1 with c,flow_matching,0.3,2.0,53,228
30,add,8.0,2,,Ccc1ll\3C,Ccc1ll\32C,10,add 2 at position 8,flow_matching,0.3,2.0,53,228
31,remove,1.0,c,,Ccc1ll\32C,Cc1ll\32C,9,remove c from position 1,flow_matching,0.3,2.0,53,228
32,replace,4.0,H,l,Cc1ll\32C,Cc1lH\32C,9,replace l at position 4 with H,flow_matching,0.3,2.0,53,228
33,remove,7.0,2,,Cc1lH\32C,Cc1lH\3C,8,remove 2 from position 7,flow_matching,0.3,2.0,53,228
34,replace,3.0,c,l,Cc1lH\3C,Cc1cH\3C,8,replace l at position 3 with c,flow_matching,0.3,2.0,53,228
35,replace,4.0,(,H,Cc1cH\3C,Cc1c(\3C,8,replace H at position 4 with (,flow_matching,0.3,2.0,53,228
36,replace,5.0,C,\,Cc1c(\3C,Cc1c(C3C,8,replace \ at position 5 with C,flow_matching,0.3,2.0,53,228
37,remove,4.0,(,,Cc1c(C3C,Cc1cC3C,7,remove ( from position 4,flow_matching,0.3,2.0,53,228
38,replace,4.0,(,C,Cc1cC3C,Cc1c(3C,7,replace C at position 4 with (,flow_matching,0.3,2.0,53,228
39,remove,5.0,3,,Cc1c(3C,Cc1c(C,6,remove 3 from position 5,flow_matching,0.3,2.0,53,228
40,replace,3.0,C,c,Cc1c(C,Cc1C(C,6,replace c at position 3 with C,flow_matching,0.3,2.0,53,228
41,replace,0.0,s,C,Cc1C(C,sc1C(C,6,replace C at position 0 with s,flow_matching,0.3,2.0,53,228
42,add,6.0,6,,sc1C(C,sc1C(C6,7,add 6 at position 6,flow_matching,0.3,2.0,53,228
43,add,7.0,(,,sc1C(C6,sc1C(C6(,8,add ( at position 7,flow_matching,0.3,2.0,53,228
44,replace,7.0,6,(,sc1C(C6(,sc1C(C66,8,replace ( at position 7 with 6,flow_matching,0.3,2.0,53,228
45,remove,7.0,6,,sc1C(C66,sc1C(C6,7,remove 6 from position 7,flow_matching,0.3,2.0,53,228
46,replace,0.0,C,s,sc1C(C6,Cc1C(C6,7,replace s at position 0 with C,flow_matching,0.3,2.0,53,228
47,remove,5.0,C,,Cc1C(C6,Cc1C(6,6,remove C from position 5,flow_matching,0.3,2.0,53,228
48,replace,4.0,1,(,Cc1C(6,Cc1C16,6,replace ( at position 4 with 1,flow_matching,0.3,2.0,53,228
49,replace,3.0,c,C,Cc1C16,Cc1c16,6,replace C at position 3 with c,flow_matching,0.3,2.0,53,228
50,replace,1.0,H,c,Cc1c16,CH1c16,6,replace c at position 1 with H,flow_matching,0.3,2.0,53,228
51,replace,1.0,c,H,CH1c16,Cc1c16,6,replace H at position 1 with c,flow_matching,0.3,2.0,53,228
52,replace,4.0,(,1,Cc1c16,Cc1c(6,6,replace 1 at position 4 with (,flow_matching,0.3,2.0,53,228
53,add,5.0,I,,Cc1c(6,Cc1c(I6,7,add I at position 5,flow_matching,0.3,2.0,53,228
54,add,3.0,B,,Cc1c(I6,Cc1Bc(I6,8,add B at position 3,flow_matching,0.3,2.0,53,228
55,replace,7.0,#,6,Cc1Bc(I6,Cc1Bc(I#,8,replace 6 at position 7 with #,flow_matching,0.3,2.0,53,228
56,remove,0.0,C,,Cc1Bc(I#,c1Bc(I#,7,remove C from position 0,flow_matching,0.3,2.0,53,228
57,add,0.0,],,c1Bc(I#,]c1Bc(I#,8,add ] at position 0,flow_matching,0.3,2.0,53,228
58,add,0.0,l,,]c1Bc(I#,l]c1Bc(I#,9,add l at position 0,flow_matching,0.3,2.0,53,228
59,replace,0.0,C,l,l]c1Bc(I#,C]c1Bc(I#,9,replace l at position 0 with C,flow_matching,0.3,2.0,53,228
60,replace,8.0,2,#,C]c1Bc(I#,C]c1Bc(I2,9,replace # at position 8 with 2,flow_matching,0.3,2.0,53,228
61,remove,4.0,B,,C]c1Bc(I2,C]c1c(I2,8,remove B from position 4,flow_matching,0.3,2.0,53,228
62,replace,3.0,c,1,C]c1c(I2,C]ccc(I2,8,replace 1 at position 3 with c,flow_matching,0.3,2.0,53,228
63,replace,1.0,c,],C]ccc(I2,Ccccc(I2,8,replace ] at position 1 with c,flow_matching,0.3,2.0,53,228
64,replace,2.0,1,c,Ccccc(I2,Cc1cc(I2,8,replace c at position 2 with 1,flow_matching,0.3,2.0,53,228
65,add,3.0,l,,Cc1cc(I2,Cc1lcc(I2,9,add l at position 3,flow_matching,0.3,2.0,53,228
66,add,4.0,#,,Cc1lcc(I2,Cc1l#cc(I2,10,add # at position 4,flow_matching,0.3,2.0,53,228
67,replace,9.0,-,2,Cc1l#cc(I2,Cc1l#cc(I-,10,replace 2 at position 9 with -,flow_matching,0.3,2.0,53,228
68,remove,9.0,-,,Cc1l#cc(I-,Cc1l#cc(I,9,remove - from position 9,flow_matching,0.3,2.0,53,228
69,replace,3.0,c,l,Cc1l#cc(I,Cc1c#cc(I,9,replace l at position 3 with c,flow_matching,0.3,2.0,53,228
70,replace,4.0,(,#,Cc1c#cc(I,Cc1c(cc(I,9,replace # at position 4 with (,flow_matching,0.3,2.0,53,228
71,replace,8.0,2,I,Cc1c(cc(I,Cc1c(cc(2,9,replace I at position 8 with 2,flow_matching,0.3,2.0,53,228
72,replace,4.0,3,(,Cc1c(cc(2,Cc1c3cc(2,9,replace ( at position 4 with 3,flow_matching,0.3,2.0,53,228
73,add,7.0,N,,Cc1c3cc(2,Cc1c3ccN(2,10,add N at position 7,flow_matching,0.3,2.0,53,228
74,remove,4.0,3,,Cc1c3ccN(2,Cc1cccN(2,9,remove 3 from position 4,flow_matching,0.3,2.0,53,228
75,replace,0.0,H,C,Cc1cccN(2,Hc1cccN(2,9,replace C at position 0 with H,flow_matching,0.3,2.0,53,228
76,remove,5.0,c,,Hc1cccN(2,Hc1ccN(2,8,remove c from position 5,flow_matching,0.3,2.0,53,228
77,remove,0.0,H,,Hc1ccN(2,c1ccN(2,7,remove H from position 0,flow_matching,0.3,2.0,53,228
78,replace,0.0,C,c,c1ccN(2,C1ccN(2,7,replace c at position 0 with C,flow_matching,0.3,2.0,53,228
79,replace,1.0,c,1,C1ccN(2,CcccN(2,7,replace 1 at position 1 with c,flow_matching,0.3,2.0,53,228
80,remove,2.0,c,,CcccN(2,CccN(2,6,remove c from position 2,flow_matching,0.3,2.0,53,228
81,replace,2.0,-,c,CccN(2,Cc-N(2,6,replace c at position 2 with -,flow_matching,0.3,2.0,53,228
82,remove,0.0,C,,Cc-N(2,c-N(2,5,remove C from position 0,flow_matching,0.3,2.0,53,228
83,replace,0.0,C,c,c-N(2,C-N(2,5,replace c at position 0 with C,flow_matching,0.3,2.0,53,228
84,add,5.0,),,C-N(2,C-N(2),6,add ) at position 5,flow_matching,0.3,2.0,53,228
85,remove,4.0,2,,C-N(2),C-N(),5,remove 2 from position 4,flow_matching,0.3,2.0,53,228
86,remove,1.0,-,,C-N(),CN(),4,remove - from position 1,flow_matching,0.3,2.0,53,228
87,replace,1.0,c,N,CN(),Cc(),4,replace N at position 1 with c,flow_matching,0.3,2.0,53,228
88,replace,1.0,r,c,Cc(),Cr(),4,replace c at position 1 with r,flow_matching,0.3,2.0,53,228
89,replace,1.0,c,r,Cr(),Cc(),4,replace r at position 1 with c,flow_matching,0.3,2.0,53,228
90,add,2.0,#,,Cc(),Cc#(),5,add # at position 2,flow_matching,0.3,2.0,53,228
91,add,4.0,2,,Cc#(),Cc#(2),6,add 2 at position 4,flow_matching,0.3,2.0,53,228
92,add,6.0,+,,Cc#(2),Cc#(2)+,7,add + at position 6,flow_matching,0.3,2.0,53,228
93,remove,1.0,c,,Cc#(2)+,C#(2)+,6,remove c from position 1,flow_matching,0.3,2.0,53,228
94,add,1.0,S,,C#(2)+,CS#(2)+,7,add S at position 1,flow_matching,0.3,2.0,53,228
95,replace,1.0,c,S,CS#(2)+,Cc#(2)+,7,replace S at position 1 with c,flow_matching,0.3,2.0,53,228
96,replace,0.0,#,C,Cc#(2)+,#c#(2)+,7,replace C at position 0 with #,flow_matching,0.3,2.0,53,228
97,replace,0.0,C,#,#c#(2)+,Cc#(2)+,7,replace # at position 0 with C,flow_matching,0.3,2.0,53,228
98,replace,2.0,1,#,Cc#(2)+,Cc1(2)+,7,replace # at position 2 with 1,flow_matching,0.3,2.0,53,228
99,remove,5.0,),,Cc1(2)+,Cc1(2+,6,remove ) from position 5,flow_matching,0.3,2.0,53,228
100,replace,3.0,c,(,Cc1(2+,Cc1c2+,6,replace ( at position 3 with c,flow_matching,0.3,2.0,53,228
101,replace,4.0,-,2,Cc1c2+,Cc1c-+,6,replace 2 at position 4 with -,flow_matching,0.3,2.0,53,228
102,add,4.0,2,,Cc1c-+,Cc1c2-+,7,add 2 at position 4,flow_matching,0.3,2.0,53,228
103,add,3.0,+,,Cc1c2-+,Cc1+c2-+,8,add + at position 3,flow_matching,0.3,2.0,53,228
104,add,8.0,H,,Cc1+c2-+,Cc1+c2-+H,9,add H at position 8,flow_matching,0.3,2.0,53,228
105,add,8.0,S,,Cc1+c2-+H,Cc1+c2-+SH,10,add S at position 8,flow_matching,0.3,2.0,53,228
106,replace,9.0,@,H,Cc1+c2-+SH,Cc1+c2-+S@,10,replace H at position 9 with @,flow_matching,0.3,2.0,53,228
107,remove,8.0,S,,Cc1+c2-+S@,Cc1+c2-+@,9,remove S from position 8,flow_matching,0.3,2.0,53,228
108,add,5.0,N,,Cc1+c2-+@,Cc1+cN2-+@,10,add N at position 5,flow_matching,0.3,2.0,53,228
109,replace,3.0,c,+,Cc1+cN2-+@,Cc1ccN2-+@,10,replace + at position 3 with c,flow_matching,0.3,2.0,53,228
110,add,1.0,5,,Cc1ccN2-+@,C5c1ccN2-+@,11,add 5 at position 1,flow_matching,0.3,2.0,53,228
111,replace,3.0,],1,C5c1ccN2-+@,C5c]ccN2-+@,11,replace 1 at position 3 with ],flow_matching,0.3,2.0,53,228
112,add,4.0,3,,C5c]ccN2-+@,C5c]3ccN2-+@,12,add 3 at position 4,flow_matching,0.3,2.0,53,228
113,replace,1.0,F,5,C5c]3ccN2-+@,CFc]3ccN2-+@,12,replace 5 at position 1 with F,flow_matching,0.3,2.0,53,228
114,replace,5.0,N,c,CFc]3ccN2-+@,CFc]3NcN2-+@,12,replace c at position 5 with N,flow_matching,0.3,2.0,53,228
115,remove,8.0,2,,CFc]3NcN2-+@,CFc]3NcN-+@,11,remove 2 from position 8,flow_matching,0.3,2.0,53,228
116,remove,1.0,F,,CFc]3NcN-+@,Cc]3NcN-+@,10,remove F from position 1,flow_matching,0.3,2.0,53,228
117,add,0.0,B,,Cc]3NcN-+@,BCc]3NcN-+@,11,add B at position 0,flow_matching,0.3,2.0,53,228
118,replace,0.0,C,B,BCc]3NcN-+@,CCc]3NcN-+@,11,replace B at position 0 with C,flow_matching,0.3,2.0,53,228
119,remove,9.0,+,,CCc]3NcN-+@,CCc]3NcN-@,10,remove + from position 9,flow_matching,0.3,2.0,53,228
120,add,8.0,o,,CCc]3NcN-@,CCc]3NcNo-@,11,add o at position 8,flow_matching,0.3,2.0,53,228
121,remove,5.0,N,,CCc]3NcNo-@,CCc]3cNo-@,10,remove N from position 5,flow_matching,0.3,2.0,53,228
122,replace,1.0,c,C,CCc]3cNo-@,Ccc]3cNo-@,10,replace C at position 1 with c,flow_matching,0.3,2.0,53,228
123,add,8.0,H,,Ccc]3cNo-@,Ccc]3cNoH-@,11,add H at position 8,flow_matching,0.3,2.0,53,228
124,add,11.0,N,,Ccc]3cNoH-@,Ccc]3cNoH-@N,12,add N at position 11,flow_matching,0.3,2.0,53,228
125,remove,2.0,c,,Ccc]3cNoH-@N,Cc]3cNoH-@N,11,remove c from position 2,flow_matching,0.3,2.0,53,228
126,add,11.0,H,,Cc]3cNoH-@N,Cc]3cNoH-@NH,12,add H at position 11,flow_matching,0.3,2.0,53,228
127,replace,2.0,1,],Cc]3cNoH-@NH,Cc13cNoH-@NH,12,replace ] at position 2 with 1,flow_matching,0.3,2.0,53,228
128,add,6.0,[,,Cc13cNoH-@NH,Cc13cN[oH-@NH,13,add [ at position 6,flow_matching,0.3,2.0,53,228
129,add,11.0,=,,Cc13cN[oH-@NH,Cc13cN[oH-@=NH,14,add = at position 11,flow_matching,0.3,2.0,53,228
130,add,8.0,-,,Cc13cN[oH-@=NH,Cc13cN[o-H-@=NH,15,add - at position 8,flow_matching,0.3,2.0,53,228
131,remove,11.0,@,,Cc13cN[o-H-@=NH,Cc13cN[o-H-=NH,14,remove @ from position 11,flow_matching,0.3,2.0,53,228
132,replace,3.0,c,3,Cc13cN[o-H-=NH,Cc1ccN[o-H-=NH,14,replace 3 at position 3 with c,flow_matching,0.3,2.0,53,228
133,replace,4.0,(,c,Cc1ccN[o-H-=NH,Cc1c(N[o-H-=NH,14,replace c at position 4 with (,flow_matching,0.3,2.0,53,228
134,add,7.0,n,,Cc1c(N[o-H-=NH,Cc1c(N[no-H-=NH,15,add n at position 7,flow_matching,0.3,2.0,53,228
135,replace,4.0,7,(,Cc1c(N[no-H-=NH,Cc1c7N[no-H-=NH,15,replace ( at position 4 with 7,flow_matching,0.3,2.0,53,228
136,add,9.0,2,,Cc1c7N[no-H-=NH,Cc1c7N[no2-H-=NH,16,add 2 at position 9,flow_matching,0.3,2.0,53,228
137,replace,4.0,(,7,Cc1c7N[no2-H-=NH,Cc1c(N[no2-H-=NH,16,replace 7 at position 4 with (,flow_matching,0.3,2.0,53,228
138,add,9.0,B,,Cc1c(N[no2-H-=NH,Cc1c(N[noB2-H-=NH,17,add B at position 9,flow_matching,0.3,2.0,53,228
139,remove,1.0,c,,Cc1c(N[noB2-H-=NH,C1c(N[noB2-H-=NH,16,remove c from position 1,flow_matching,0.3,2.0,53,228
140,replace,1.0,c,1,C1c(N[noB2-H-=NH,Ccc(N[noB2-H-=NH,16,replace 1 at position 1 with c,flow_matching,0.3,2.0,53,228
141,replace,2.0,1,c,Ccc(N[noB2-H-=NH,Cc1(N[noB2-H-=NH,16,replace c at position 2 with 1,flow_matching,0.3,2.0,53,228
142,replace,3.0,c,(,Cc1(N[noB2-H-=NH,Cc1cN[noB2-H-=NH,16,replace ( at position 3 with c,flow_matching,0.3,2.0,53,228
143,add,9.0,I,,Cc1cN[noB2-H-=NH,Cc1cN[noBI2-H-=NH,17,add I at position 9,flow_matching,0.3,2.0,53,228
144,replace,14.0,o,=,Cc1cN[noBI2-H-=NH,Cc1cN[noBI2-H-oNH,17,replace = at position 14 with o,flow_matching,0.3,2.0,53,228
145,replace,4.0,(,N,Cc1cN[noBI2-H-oNH,Cc1c([noBI2-H-oNH,17,replace N at position 4 with (,flow_matching,0.3,2.0,53,228
146,replace,13.0,@,-,Cc1c([noBI2-H-oNH,Cc1c([noBI2-H@oNH,17,replace - at position 13 with @,flow_matching,0.3,2.0,53,228
147,replace,5.0,C,[,Cc1c([noBI2-H@oNH,Cc1c(CnoBI2-H@oNH,17,replace [ at position 5 with C,flow_matching,0.3,2.0,53,228
148,add,13.0,s,,Cc1c(CnoBI2-H@oNH,Cc1c(CnoBI2-Hs@oNH,18,add s at position 13,flow_matching,0.3,2.0,53,228
149,replace,6.0,(,n,Cc1c(CnoBI2-Hs@oNH,Cc1c(C(oBI2-Hs@oNH,18,replace n at position 6 with (,flow_matching,0.3,2.0,53,228
150,remove,10.0,2,,Cc1c(C(oBI2-Hs@oNH,Cc1c(C(oBI-Hs@oNH,17,remove 2 from position 10,flow_matching,0.3,2.0,53,228
151,replace,14.0,=,o,Cc1c(C(oBI-Hs@oNH,Cc1c(C(oBI-Hs@=NH,17,replace o at position 14 with =,flow_matching,0.3,2.0,53,228
152,remove,0.0,C,,Cc1c(C(oBI-Hs@=NH,c1c(C(oBI-Hs@=NH,16,remove C from position 0,flow_matching,0.3,2.0,53,228
153,add,4.0,4,,c1c(C(oBI-Hs@=NH,c1c(4C(oBI-Hs@=NH,17,add 4 at position 4,flow_matching,0.3,2.0,53,228
154,remove,12.0,s,,c1c(4C(oBI-Hs@=NH,c1c(4C(oBI-H@=NH,16,remove s from position 12,flow_matching,0.3,2.0,53,228
155,replace,9.0,l,I,c1c(4C(oBI-H@=NH,c1c(4C(oBl-H@=NH,16,replace I at position 9 with l,flow_matching,0.3,2.0,53,228
156,add,6.0,F,,c1c(4C(oBl-H@=NH,c1c(4CF(oBl-H@=NH,17,add F at position 6,flow_matching,0.3,2.0,53,228
157,replace,0.0,C,c,c1c(4CF(oBl-H@=NH,C1c(4CF(oBl-H@=NH,17,replace c at position 0 with C,flow_matching,0.3,2.0,53,228
158,remove,1.0,1,,C1c(4CF(oBl-H@=NH,Cc(4CF(oBl-H@=NH,16,remove 1 from position 1,flow_matching,0.3,2.0,53,228
159,replace,2.0,1,(,Cc(4CF(oBl-H@=NH,Cc14CF(oBl-H@=NH,16,replace ( at position 2 with 1,flow_matching,0.3,2.0,53,228
160,replace,3.0,c,4,Cc14CF(oBl-H@=NH,Cc1cCF(oBl-H@=NH,16,replace 4 at position 3 with c,flow_matching,0.3,2.0,53,228
161,remove,9.0,l,,Cc1cCF(oBl-H@=NH,Cc1cCF(oB-H@=NH,15,remove l from position 9,flow_matching,0.3,2.0,53,228
162,remove,5.0,F,,Cc1cCF(oB-H@=NH,Cc1cC(oB-H@=NH,14,remove F from position 5,flow_matching,0.3,2.0,53,228
163,add,6.0,6,,Cc1cC(oB-H@=NH,Cc1cC(6oB-H@=NH,15,add 6 at position 6,flow_matching,0.3,2.0,53,228
164,remove,8.0,B,,Cc1cC(6oB-H@=NH,Cc1cC(6o-H@=NH,14,remove B from position 8,flow_matching,0.3,2.0,53,228
165,remove,3.0,c,,Cc1cC(6o-H@=NH,Cc1C(6o-H@=NH,13,remove c from position 3,flow_matching,0.3,2.0,53,228
166,replace,10.0,C,=,Cc1C(6o-H@=NH,Cc1C(6o-H@CNH,13,replace = at position 10 with C,flow_matching,0.3,2.0,53,228
167,replace,6.0,=,o,Cc1C(6o-H@CNH,Cc1C(6=-H@CNH,13,replace o at position 6 with =,flow_matching,0.3,2.0,53,228
168,remove,12.0,H,,Cc1C(6=-H@CNH,Cc1C(6=-H@CN,12,remove H from position 12,flow_matching,0.3,2.0,53,228
169,replace,4.0,c,(,Cc1C(6=-H@CN,Cc1Cc6=-H@CN,12,replace ( at position 4 with c,flow_matching,0.3,2.0,53,228
170,add,8.0,c,,Cc1Cc6=-H@CN,Cc1Cc6=-cH@CN,13,add c at position 8,flow_matching,0.3,2.0,53,228
171,add,3.0,/,,Cc1Cc6=-cH@CN,Cc1/Cc6=-cH@CN,14,add / at position 3,flow_matching,0.3,2.0,53,228
172,add,5.0,r,,Cc1/Cc6=-cH@CN,Cc1/Crc6=-cH@CN,15,add r at position 5,flow_matching,0.3,2.0,53,228
173,replace,1.0,N,c,Cc1/Crc6=-cH@CN,CN1/Crc6=-cH@CN,15,replace c at position 1 with N,flow_matching,0.3,2.0,53,228
174,remove,3.0,/,,CN1/Crc6=-cH@CN,CN1Crc6=-cH@CN,14,remove / from position 3,flow_matching,0.3,2.0,53,228
175,replace,1.0,c,N,CN1Crc6=-cH@CN,Cc1Crc6=-cH@CN,14,replace N at position 1 with c,flow_matching,0.3,2.0,53,228
176,replace,12.0,s,C,Cc1Crc6=-cH@CN,Cc1Crc6=-cH@sN,14,replace C at position 12 with s,flow_matching,0.3,2.0,53,228
177,replace,13.0,),N,Cc1Crc6=-cH@sN,Cc1Crc6=-cH@s),14,replace N at position 13 with ),flow_matching,0.3,2.0,53,228
178,remove,8.0,-,,Cc1Crc6=-cH@s),Cc1Crc6=cH@s),13,remove - from position 8,flow_matching,0.3,2.0,53,228
179,replace,8.0,C,c,Cc1Crc6=cH@s),Cc1Crc6=CH@s),13,replace c at position 8 with C,flow_matching,0.3,2.0,53,228
180,replace,3.0,c,C,Cc1Crc6=CH@s),Cc1crc6=CH@s),13,replace C at position 3 with c,flow_matching,0.3,2.0,53,228
181,replace,4.0,(,r,Cc1crc6=CH@s),Cc1c(c6=CH@s),13,replace r at position 4 with (,flow_matching,0.3,2.0,53,228
182,replace,5.0,C,c,Cc1c(c6=CH@s),Cc1c(C6=CH@s),13,replace c at position 5 with C,flow_matching,0.3,2.0,53,228
183,replace,6.0,(,6,Cc1c(C6=CH@s),Cc1c(C(=CH@s),13,replace 6 at position 6 with (,flow_matching,0.3,2.0,53,228
184,replace,8.0,O,C,Cc1c(C(=CH@s),Cc1c(C(=OH@s),13,replace C at position 8 with O,flow_matching,0.3,2.0,53,228
185,replace,9.0,),H,Cc1c(C(=OH@s),Cc1c(C(=O)@s),13,replace H at position 9 with ),flow_matching,0.3,2.0,53,228
186,replace,10.0,N,@,Cc1c(C(=O)@s),Cc1c(C(=O)Ns),13,replace @ at position 10 with N,flow_matching,0.3,2.0,53,228
187,replace,11.0,2,s,Cc1c(C(=O)Ns),Cc1c(C(=O)N2),13,replace s at position 11 with 2,flow_matching,0.3,2.0,53,228
188,replace,12.0,C,),Cc1c(C(=O)N2),Cc1c(C(=O)N2C,13,replace ) at position 12 with C,flow_matching,0.3,2.0,53,228
189,add,13.0,C,,Cc1c(C(=O)N2C,Cc1c(C(=O)N2CC,14,add C at position 13,flow_matching,0.3,2.0,53,228
190,add,14.0,O,,Cc1c(C(=O)N2CC,Cc1c(C(=O)N2CCO,15,add O at position 14,flow_matching,0.3,2.0,53,228
191,add,15.0,C,,Cc1c(C(=O)N2CCO,Cc1c(C(=O)N2CCOC,16,add C at position 15,flow_matching,0.3,2.0,53,228
192,add,16.0,C,,Cc1c(C(=O)N2CCOC,Cc1c(C(=O)N2CCOCC,17,add C at position 16,flow_matching,0.3,2.0,53,228
193,add,17.0,2,,Cc1c(C(=O)N2CCOCC,Cc1c(C(=O)N2CCOCC2,18,add 2 at position 17,flow_matching,0.3,2.0,53,228
194,add,18.0,),,Cc1c(C(=O)N2CCOCC2,Cc1c(C(=O)N2CCOCC2),19,add ) at position 18,flow_matching,0.3,2.0,53,228
195,add,19.0,o,,Cc1c(C(=O)N2CCOCC2),Cc1c(C(=O)N2CCOCC2)o,20,add o at position 19,flow_matching,0.3,2.0,53,228
196,add,20.0,c,,Cc1c(C(=O)N2CCOCC2)o,Cc1c(C(=O)N2CCOCC2)oc,21,add c at position 20,flow_matching,0.3,2.0,53,228
197,add,21.0,2,,Cc1c(C(=O)N2CCOCC2)oc,Cc1c(C(=O)N2CCOCC2)oc2,22,add 2 at position 21,flow_matching,0.3,2.0,53,228
198,add,22.0,c,,Cc1c(C(=O)N2CCOCC2)oc2,Cc1c(C(=O)N2CCOCC2)oc2c,23,add c at position 22,flow_matching,0.3,2.0,53,228
199,add,23.0,1,,Cc1c(C(=O)N2CCOCC2)oc2c,Cc1c(C(=O)N2CCOCC2)oc2c1,24,add 1 at position 23,flow_matching,0.3,2.0,53,228
200,add,24.0,-,,Cc1c(C(=O)N2CCOCC2)oc2c1,Cc1c(C(=O)N2CCOCC2)oc2c1-,25,add - at position 24,flow_matching,0.3,2.0,53,228
201,add,25.0,c,,Cc1c(C(=O)N2CCOCC2)oc2c1-,Cc1c(C(=O)N2CCOCC2)oc2c1-c,26,add c at position 25,flow_matching,0.3,2.0,53,228
202,add,26.0,1,,Cc1c(C(=O)N2CCOCC2)oc2c1-c,Cc1c(C(=O)N2CCOCC2)oc2c1-c1,27,add 1 at position 26,flow_matching,0.3,2.0,53,228
203,add,27.0,n,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1,Cc1c(C(=O)N2CCOCC2)oc2c1-c1n,28,add n at position 27,flow_matching,0.3,2.0,53,228
204,add,28.0,n,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1n,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn,29,add n at position 28,flow_matching,0.3,2.0,53,228
205,add,29.0,(,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(,30,add ( at position 29,flow_matching,0.3,2.0,53,228
206,add,30.0,C,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(C,31,add C at position 30,flow_matching,0.3,2.0,53,228
207,add,31.0,C,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(C,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC,32,add C at position 31,flow_matching,0.3,2.0,53,228
208,add,32.0,(,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(,33,add ( at position 32,flow_matching,0.3,2.0,53,228
209,add,33.0,=,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=,34,add = at position 33,flow_matching,0.3,2.0,53,228
210,add,34.0,O,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O,35,add O at position 34,flow_matching,0.3,2.0,53,228
211,add,35.0,),,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O),36,add ) at position 35,flow_matching,0.3,2.0,53,228
212,add,36.0,N,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O),Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)N,37,add N at position 36,flow_matching,0.3,2.0,53,228
213,add,37.0,C,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)N,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NC,38,add C at position 37,flow_matching,0.3,2.0,53,228
214,add,38.0,c,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NC,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc,39,add c at position 38,flow_matching,0.3,2.0,53,228
215,add,39.0,3,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3,40,add 3 at position 39,flow_matching,0.3,2.0,53,228
216,add,40.0,c,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3c,41,add c at position 40,flow_matching,0.3,2.0,53,228
217,add,41.0,c,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3c,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3cc,42,add c at position 41,flow_matching,0.3,2.0,53,228
218,add,42.0,c,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3cc,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccc,43,add c at position 42,flow_matching,0.3,2.0,53,228
219,add,43.0,o,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccc,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco,44,add o at position 43,flow_matching,0.3,2.0,53,228
220,add,44.0,3,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3,45,add 3 at position 44,flow_matching,0.3,2.0,53,228
221,add,45.0,),,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3),46,add ) at position 45,flow_matching,0.3,2.0,53,228
222,add,46.0,c,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3),Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)c,47,add c at position 46,flow_matching,0.3,2.0,53,228
223,add,47.0,c,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)c,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc,48,add c at position 47,flow_matching,0.3,2.0,53,228
224,add,48.0,1,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc1,49,add 1 at position 48,flow_matching,0.3,2.0,53,228
225,add,49.0,C,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc1,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc1C,50,add C at position 49,flow_matching,0.3,2.0,53,228
226,add,50.0,C,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc1C,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc1CC,51,add C at position 50,flow_matching,0.3,2.0,53,228
227,add,51.0,2,,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc1CC,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc1CC2,52,add 2 at position 51,flow_matching,0.3,2.0,53,228
228,add,52.0,"
",,Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc1CC2,"Cc1c(C(=O)N2CCOCC2)oc2c1-c1nn(CC(=O)NCc3ccco3)cc1CC2
",53,"add 
 at position 52",flow_matching,0.3,2.0,53,228
