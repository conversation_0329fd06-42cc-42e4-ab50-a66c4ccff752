step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,36,94
1,add,0.0,s,,,s,1,add s at position 0,flow_matching,0.3,2.0,36,94
2,remove,0.0,s,,s,,0,remove s from position 0,flow_matching,0.3,2.0,36,94
3,add,0.0,7,,,7,1,add 7 at position 0,flow_matching,0.3,2.0,36,94
4,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,36,94
5,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,36,94
6,add,0.0,#,,,#,1,add # at position 0,flow_matching,0.3,2.0,36,94
7,remove,0.0,#,,#,,0,remove # from position 0,flow_matching,0.3,2.0,36,94
8,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,36,94
9,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,36,94
10,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,36,94
11,replace,0.0,-,C,C,-,1,replace C at position 0 with -,flow_matching,0.3,2.0,36,94
12,remove,0.0,-,,-,,0,remove - from position 0,flow_matching,0.3,2.0,36,94
13,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,36,94
14,add,1.0,C,,I,IC,2,add C at position 1,flow_matching,0.3,2.0,36,94
15,add,1.0,],,IC,I]C,3,add ] at position 1,flow_matching,0.3,2.0,36,94
16,add,3.0,+,,I]C,I]C+,4,add + at position 3,flow_matching,0.3,2.0,36,94
17,replace,0.0,5,I,I]C+,5]C+,4,replace I at position 0 with 5,flow_matching,0.3,2.0,36,94
18,remove,2.0,C,,5]C+,5]+,3,remove C from position 2,flow_matching,0.3,2.0,36,94
19,replace,0.0,C,5,5]+,C]+,3,replace 5 at position 0 with C,flow_matching,0.3,2.0,36,94
20,add,0.0,S,,C]+,SC]+,4,add S at position 0,flow_matching,0.3,2.0,36,94
21,replace,0.0,C,S,SC]+,CC]+,4,replace S at position 0 with C,flow_matching,0.3,2.0,36,94
22,replace,2.0,[,],CC]+,CC[+,4,replace ] at position 2 with [,flow_matching,0.3,2.0,36,94
23,replace,3.0,C,+,CC[+,CC[C,4,replace + at position 3 with C,flow_matching,0.3,2.0,36,94
24,remove,2.0,[,,CC[C,CCC,3,remove [ from position 2,flow_matching,0.3,2.0,36,94
25,add,3.0,I,,CCC,CCCI,4,add I at position 3,flow_matching,0.3,2.0,36,94
26,replace,2.0,[,C,CCCI,CC[I,4,replace C at position 2 with [,flow_matching,0.3,2.0,36,94
27,replace,3.0,C,I,CC[I,CC[C,4,replace I at position 3 with C,flow_matching,0.3,2.0,36,94
28,remove,1.0,C,,CC[C,C[C,3,remove C from position 1,flow_matching,0.3,2.0,36,94
29,replace,1.0,C,[,C[C,CCC,3,replace [ at position 1 with C,flow_matching,0.3,2.0,36,94
30,add,0.0,],,CCC,]CCC,4,add ] at position 0,flow_matching,0.3,2.0,36,94
31,add,3.0,@,,]CCC,]CC@C,5,add @ at position 3,flow_matching,0.3,2.0,36,94
32,remove,4.0,C,,]CC@C,]CC@,4,remove C from position 4,flow_matching,0.3,2.0,36,94
33,remove,1.0,C,,]CC@,]C@,3,remove C from position 1,flow_matching,0.3,2.0,36,94
34,replace,0.0,C,],]C@,CC@,3,replace ] at position 0 with C,flow_matching,0.3,2.0,36,94
35,replace,2.0,[,@,CC@,CC[,3,replace @ at position 2 with [,flow_matching,0.3,2.0,36,94
36,remove,0.0,C,,CC[,C[,2,remove C from position 0,flow_matching,0.3,2.0,36,94
37,remove,0.0,C,,C[,[,1,remove C from position 0,flow_matching,0.3,2.0,36,94
38,remove,0.0,[,,[,,0,remove [ from position 0,flow_matching,0.3,2.0,36,94
39,add,0.0,[,,,[,1,add [ at position 0,flow_matching,0.3,2.0,36,94
40,add,0.0,-,,[,-[,2,add - at position 0,flow_matching,0.3,2.0,36,94
41,remove,1.0,[,,-[,-,1,remove [ from position 1,flow_matching,0.3,2.0,36,94
42,replace,0.0,/,-,-,/,1,replace - at position 0 with /,flow_matching,0.3,2.0,36,94
43,add,1.0,n,,/,/n,2,add n at position 1,flow_matching,0.3,2.0,36,94
44,replace,1.0,2,n,/n,/2,2,replace n at position 1 with 2,flow_matching,0.3,2.0,36,94
45,remove,1.0,2,,/2,/,1,remove 2 from position 1,flow_matching,0.3,2.0,36,94
46,add,0.0,],,/,]/,2,add ] at position 0,flow_matching,0.3,2.0,36,94
47,replace,0.0,C,],]/,C/,2,replace ] at position 0 with C,flow_matching,0.3,2.0,36,94
48,replace,1.0,C,/,C/,CC,2,replace / at position 1 with C,flow_matching,0.3,2.0,36,94
49,replace,0.0,7,C,CC,7C,2,replace C at position 0 with 7,flow_matching,0.3,2.0,36,94
50,replace,0.0,\,7,7C,\C,2,replace 7 at position 0 with \,flow_matching,0.3,2.0,36,94
51,replace,0.0,C,\,\C,CC,2,replace \ at position 0 with C,flow_matching,0.3,2.0,36,94
52,add,2.0,[,,CC,CC[,3,add [ at position 2,flow_matching,0.3,2.0,36,94
53,add,3.0,C,,CC[,CC[C,4,add C at position 3,flow_matching,0.3,2.0,36,94
54,add,4.0,@,,CC[C,CC[C@,5,add @ at position 4,flow_matching,0.3,2.0,36,94
55,add,5.0,H,,CC[C@,CC[C@H,6,add H at position 5,flow_matching,0.3,2.0,36,94
56,replace,5.0,4,H,CC[C@H,CC[C@4,6,replace H at position 5 with 4,flow_matching,0.3,2.0,36,94
57,add,1.0,@,,CC[C@4,C@C[C@4,7,add @ at position 1,flow_matching,0.3,2.0,36,94
58,replace,1.0,C,@,C@C[C@4,CCC[C@4,7,replace @ at position 1 with C,flow_matching,0.3,2.0,36,94
59,replace,4.0,\,C,CCC[C@4,CCC[\@4,7,replace C at position 4 with \,flow_matching,0.3,2.0,36,94
60,replace,6.0,I,4,CCC[\@4,CCC[\@I,7,replace 4 at position 6 with I,flow_matching,0.3,2.0,36,94
61,replace,2.0,[,C,CCC[\@I,CC[[\@I,7,replace C at position 2 with [,flow_matching,0.3,2.0,36,94
62,replace,3.0,C,[,CC[[\@I,CC[C\@I,7,replace [ at position 3 with C,flow_matching,0.3,2.0,36,94
63,replace,4.0,@,\,CC[C\@I,CC[C@@I,7,replace \ at position 4 with @,flow_matching,0.3,2.0,36,94
64,replace,5.0,H,@,CC[C@@I,CC[C@HI,7,replace @ at position 5 with H,flow_matching,0.3,2.0,36,94
65,replace,6.0,],I,CC[C@HI,CC[C@H],7,replace I at position 6 with ],flow_matching,0.3,2.0,36,94
66,add,7.0,(,,CC[C@H],CC[C@H](,8,add ( at position 7,flow_matching,0.3,2.0,36,94
67,add,8.0,C,,CC[C@H](,CC[C@H](C,9,add C at position 8,flow_matching,0.3,2.0,36,94
68,add,9.0,),,CC[C@H](C,CC[C@H](C),10,add ) at position 9,flow_matching,0.3,2.0,36,94
69,add,10.0,C,,CC[C@H](C),CC[C@H](C)C,11,add C at position 10,flow_matching,0.3,2.0,36,94
70,add,11.0,n,,CC[C@H](C)C,CC[C@H](C)Cn,12,add n at position 11,flow_matching,0.3,2.0,36,94
71,add,12.0,1,,CC[C@H](C)Cn,CC[C@H](C)Cn1,13,add 1 at position 12,flow_matching,0.3,2.0,36,94
72,add,13.0,c,,CC[C@H](C)Cn1,CC[C@H](C)Cn1c,14,add c at position 13,flow_matching,0.3,2.0,36,94
73,add,14.0,(,,CC[C@H](C)Cn1c,CC[C@H](C)Cn1c(,15,add ( at position 14,flow_matching,0.3,2.0,36,94
74,add,15.0,C,,CC[C@H](C)Cn1c(,CC[C@H](C)Cn1c(C,16,add C at position 15,flow_matching,0.3,2.0,36,94
75,add,16.0,C,,CC[C@H](C)Cn1c(C,CC[C@H](C)Cn1c(CC,17,add C at position 16,flow_matching,0.3,2.0,36,94
76,add,17.0,C,,CC[C@H](C)Cn1c(CC,CC[C@H](C)Cn1c(CCC,18,add C at position 17,flow_matching,0.3,2.0,36,94
77,add,18.0,l,,CC[C@H](C)Cn1c(CCC,CC[C@H](C)Cn1c(CCCl,19,add l at position 18,flow_matching,0.3,2.0,36,94
78,add,19.0,),,CC[C@H](C)Cn1c(CCCl,CC[C@H](C)Cn1c(CCCl),20,add ) at position 19,flow_matching,0.3,2.0,36,94
79,add,20.0,n,,CC[C@H](C)Cn1c(CCCl),CC[C@H](C)Cn1c(CCCl)n,21,add n at position 20,flow_matching,0.3,2.0,36,94
80,add,21.0,c,,CC[C@H](C)Cn1c(CCCl)n,CC[C@H](C)Cn1c(CCCl)nc,22,add c at position 21,flow_matching,0.3,2.0,36,94
81,add,22.0,2,,CC[C@H](C)Cn1c(CCCl)nc,CC[C@H](C)Cn1c(CCCl)nc2,23,add 2 at position 22,flow_matching,0.3,2.0,36,94
82,add,23.0,c,,CC[C@H](C)Cn1c(CCCl)nc2,CC[C@H](C)Cn1c(CCCl)nc2c,24,add c at position 23,flow_matching,0.3,2.0,36,94
83,add,24.0,(,,CC[C@H](C)Cn1c(CCCl)nc2c,CC[C@H](C)Cn1c(CCCl)nc2c(,25,add ( at position 24,flow_matching,0.3,2.0,36,94
84,add,25.0,C,,CC[C@H](C)Cn1c(CCCl)nc2c(,CC[C@H](C)Cn1c(CCCl)nc2c(C,26,add C at position 25,flow_matching,0.3,2.0,36,94
85,add,26.0,),,CC[C@H](C)Cn1c(CCCl)nc2c(C,CC[C@H](C)Cn1c(CCCl)nc2c(C),27,add ) at position 26,flow_matching,0.3,2.0,36,94
86,add,27.0,n,,CC[C@H](C)Cn1c(CCCl)nc2c(C),CC[C@H](C)Cn1c(CCCl)nc2c(C)n,28,add n at position 27,flow_matching,0.3,2.0,36,94
87,add,28.0,n,,CC[C@H](C)Cn1c(CCCl)nc2c(C)n,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn,29,add n at position 28,flow_matching,0.3,2.0,36,94
88,add,29.0,(,,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(,30,add ( at position 29,flow_matching,0.3,2.0,36,94
89,add,30.0,C,,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C,31,add C at position 30,flow_matching,0.3,2.0,36,94
90,add,31.0,),,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C),32,add ) at position 31,flow_matching,0.3,2.0,36,94
91,add,32.0,c,,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C),CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C)c,33,add c at position 32,flow_matching,0.3,2.0,36,94
92,add,33.0,2,,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C)c,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C)c2,34,add 2 at position 33,flow_matching,0.3,2.0,36,94
93,add,34.0,1,,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C)c2,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C)c21,35,add 1 at position 34,flow_matching,0.3,2.0,36,94
94,add,35.0,"
",,CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C)c21,"CC[C@H](C)Cn1c(CCCl)nc2c(C)nn(C)c21
",36,"add 
 at position 35",flow_matching,0.3,2.0,36,94
