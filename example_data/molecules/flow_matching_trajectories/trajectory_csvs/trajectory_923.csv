step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,95
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,43,95
2,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,43,95
3,remove,1.0,[,,C[,C,1,remove [ from position 1,flow_matching,0.3,2.0,43,95
4,add,1.0,[,,C,C[,2,add [ at position 1,flow_matching,0.3,2.0,43,95
5,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,43,95
6,add,3.0,@,,C[C,C[C@,4,add @ at position 3,flow_matching,0.3,2.0,43,95
7,add,4.0,@,,C[C@,C[C@@,5,add @ at position 4,flow_matching,0.3,2.0,43,95
8,replace,3.0,2,@,C[C@@,C[C2@,5,replace @ at position 3 with 2,flow_matching,0.3,2.0,43,95
9,remove,3.0,2,,C[C2@,C[C@,4,remove 2 from position 3,flow_matching,0.3,2.0,43,95
10,remove,3.0,@,,C[C@,C[C,3,remove @ from position 3,flow_matching,0.3,2.0,43,95
11,add,1.0,c,,C[C,Cc[C,4,add c at position 1,flow_matching,0.3,2.0,43,95
12,add,2.0,H,,Cc[C,CcH[C,5,add H at position 2,flow_matching,0.3,2.0,43,95
13,replace,4.0,6,C,CcH[C,CcH[6,5,replace C at position 4 with 6,flow_matching,0.3,2.0,43,95
14,remove,1.0,c,,CcH[6,CH[6,4,remove c from position 1,flow_matching,0.3,2.0,43,95
15,add,3.0,(,,CH[6,CH[(6,5,add ( at position 3,flow_matching,0.3,2.0,43,95
16,replace,1.0,[,H,CH[(6,C[[(6,5,replace H at position 1 with [,flow_matching,0.3,2.0,43,95
17,replace,2.0,C,[,C[[(6,C[C(6,5,replace [ at position 2 with C,flow_matching,0.3,2.0,43,95
18,replace,3.0,@,(,C[C(6,C[C@6,5,replace ( at position 3 with @,flow_matching,0.3,2.0,43,95
19,add,2.0,(,,C[C@6,C[(C@6,6,add ( at position 2,flow_matching,0.3,2.0,43,95
20,replace,1.0,I,[,C[(C@6,CI(C@6,6,replace [ at position 1 with I,flow_matching,0.3,2.0,43,95
21,remove,4.0,@,,CI(C@6,CI(C6,5,remove @ from position 4,flow_matching,0.3,2.0,43,95
22,replace,1.0,[,I,CI(C6,C[(C6,5,replace I at position 1 with [,flow_matching,0.3,2.0,43,95
23,remove,4.0,6,,C[(C6,C[(C,4,remove 6 from position 4,flow_matching,0.3,2.0,43,95
24,add,3.0,/,,C[(C,C[(/C,5,add / at position 3,flow_matching,0.3,2.0,43,95
25,replace,2.0,C,(,C[(/C,C[C/C,5,replace ( at position 2 with C,flow_matching,0.3,2.0,43,95
26,replace,2.0,S,C,C[C/C,C[S/C,5,replace C at position 2 with S,flow_matching,0.3,2.0,43,95
27,add,4.0,[,,C[S/C,C[S/[C,6,add [ at position 4,flow_matching,0.3,2.0,43,95
28,add,1.0,n,,C[S/[C,Cn[S/[C,7,add n at position 1,flow_matching,0.3,2.0,43,95
29,remove,6.0,C,,Cn[S/[C,Cn[S/[,6,remove C from position 6,flow_matching,0.3,2.0,43,95
30,replace,1.0,[,n,Cn[S/[,C[[S/[,6,replace n at position 1 with [,flow_matching,0.3,2.0,43,95
31,remove,2.0,[,,C[[S/[,C[S/[,5,remove [ from position 2,flow_matching,0.3,2.0,43,95
32,replace,2.0,C,S,C[S/[,C[C/[,5,replace S at position 2 with C,flow_matching,0.3,2.0,43,95
33,remove,3.0,/,,C[C/[,C[C[,4,remove / from position 3,flow_matching,0.3,2.0,43,95
34,remove,2.0,C,,C[C[,C[[,3,remove C from position 2,flow_matching,0.3,2.0,43,95
35,remove,2.0,[,,C[[,C[,2,remove [ from position 2,flow_matching,0.3,2.0,43,95
36,add,2.0,C,,C[,C[C,3,add C at position 2,flow_matching,0.3,2.0,43,95
37,replace,1.0,O,[,C[C,COC,3,replace [ at position 1 with O,flow_matching,0.3,2.0,43,95
38,add,2.0,4,,COC,CO4C,4,add 4 at position 2,flow_matching,0.3,2.0,43,95
39,replace,1.0,[,O,CO4C,C[4C,4,replace O at position 1 with [,flow_matching,0.3,2.0,43,95
40,add,2.0,H,,C[4C,C[H4C,5,add H at position 2,flow_matching,0.3,2.0,43,95
41,replace,0.0,/,C,C[H4C,/[H4C,5,replace C at position 0 with /,flow_matching,0.3,2.0,43,95
42,remove,2.0,H,,/[H4C,/[4C,4,remove H from position 2,flow_matching,0.3,2.0,43,95
43,replace,2.0,r,4,/[4C,/[rC,4,replace 4 at position 2 with r,flow_matching,0.3,2.0,43,95
44,replace,2.0,6,r,/[rC,/[6C,4,replace r at position 2 with 6,flow_matching,0.3,2.0,43,95
45,add,3.0,],,/[6C,/[6]C,5,add ] at position 3,flow_matching,0.3,2.0,43,95
46,add,1.0,o,,/[6]C,/o[6]C,6,add o at position 1,flow_matching,0.3,2.0,43,95
47,add,0.0,I,,/o[6]C,I/o[6]C,7,add I at position 0,flow_matching,0.3,2.0,43,95
48,replace,4.0,r,6,I/o[6]C,I/o[r]C,7,replace 6 at position 4 with r,flow_matching,0.3,2.0,43,95
49,remove,5.0,],,I/o[r]C,I/o[rC,6,remove ] from position 5,flow_matching,0.3,2.0,43,95
50,replace,0.0,C,I,I/o[rC,C/o[rC,6,replace I at position 0 with C,flow_matching,0.3,2.0,43,95
51,remove,5.0,C,,C/o[rC,C/o[r,5,remove C from position 5,flow_matching,0.3,2.0,43,95
52,add,2.0,@,,C/o[r,C/@o[r,6,add @ at position 2,flow_matching,0.3,2.0,43,95
53,add,6.0,O,,C/@o[r,C/@o[rO,7,add O at position 6,flow_matching,0.3,2.0,43,95
54,replace,1.0,[,/,C/@o[rO,C[@o[rO,7,replace / at position 1 with [,flow_matching,0.3,2.0,43,95
55,replace,2.0,C,@,C[@o[rO,C[Co[rO,7,replace @ at position 2 with C,flow_matching,0.3,2.0,43,95
56,replace,3.0,@,o,C[Co[rO,C[C@[rO,7,replace o at position 3 with @,flow_matching,0.3,2.0,43,95
57,replace,4.0,@,[,C[C@[rO,C[C@@rO,7,replace [ at position 4 with @,flow_matching,0.3,2.0,43,95
58,replace,5.0,H,r,C[C@@rO,C[C@@HO,7,replace r at position 5 with H,flow_matching,0.3,2.0,43,95
59,replace,6.0,],O,C[C@@HO,C[C@@H],7,replace O at position 6 with ],flow_matching,0.3,2.0,43,95
60,add,7.0,1,,C[C@@H],C[C@@H]1,8,add 1 at position 7,flow_matching,0.3,2.0,43,95
61,add,8.0,C,,C[C@@H]1,C[C@@H]1C,9,add C at position 8,flow_matching,0.3,2.0,43,95
62,add,9.0,C,,C[C@@H]1C,C[C@@H]1CC,10,add C at position 9,flow_matching,0.3,2.0,43,95
63,add,10.0,[,,C[C@@H]1CC,C[C@@H]1CC[,11,add [ at position 10,flow_matching,0.3,2.0,43,95
64,add,11.0,N,,C[C@@H]1CC[,C[C@@H]1CC[N,12,add N at position 11,flow_matching,0.3,2.0,43,95
65,add,12.0,H,,C[C@@H]1CC[N,C[C@@H]1CC[NH,13,add H at position 12,flow_matching,0.3,2.0,43,95
66,add,13.0,+,,C[C@@H]1CC[NH,C[C@@H]1CC[NH+,14,add + at position 13,flow_matching,0.3,2.0,43,95
67,add,14.0,],,C[C@@H]1CC[NH+,C[C@@H]1CC[NH+],15,add ] at position 14,flow_matching,0.3,2.0,43,95
68,add,15.0,(,,C[C@@H]1CC[NH+],C[C@@H]1CC[NH+](,16,add ( at position 15,flow_matching,0.3,2.0,43,95
69,add,16.0,C,,C[C@@H]1CC[NH+](,C[C@@H]1CC[NH+](C,17,add C at position 16,flow_matching,0.3,2.0,43,95
70,add,17.0,C,,C[C@@H]1CC[NH+](C,C[C@@H]1CC[NH+](CC,18,add C at position 17,flow_matching,0.3,2.0,43,95
71,add,18.0,C,,C[C@@H]1CC[NH+](CC,C[C@@H]1CC[NH+](CCC,19,add C at position 18,flow_matching,0.3,2.0,43,95
72,add,19.0,N,,C[C@@H]1CC[NH+](CCC,C[C@@H]1CC[NH+](CCCN,20,add N at position 19,flow_matching,0.3,2.0,43,95
73,add,20.0,2,,C[C@@H]1CC[NH+](CCCN,C[C@@H]1CC[NH+](CCCN2,21,add 2 at position 20,flow_matching,0.3,2.0,43,95
74,add,21.0,C,,C[C@@H]1CC[NH+](CCCN2,C[C@@H]1CC[NH+](CCCN2C,22,add C at position 21,flow_matching,0.3,2.0,43,95
75,add,22.0,(,,C[C@@H]1CC[NH+](CCCN2C,C[C@@H]1CC[NH+](CCCN2C(,23,add ( at position 22,flow_matching,0.3,2.0,43,95
76,add,23.0,=,,C[C@@H]1CC[NH+](CCCN2C(,C[C@@H]1CC[NH+](CCCN2C(=,24,add = at position 23,flow_matching,0.3,2.0,43,95
77,add,24.0,O,,C[C@@H]1CC[NH+](CCCN2C(=,C[C@@H]1CC[NH+](CCCN2C(=O,25,add O at position 24,flow_matching,0.3,2.0,43,95
78,add,25.0,),,C[C@@H]1CC[NH+](CCCN2C(=O,C[C@@H]1CC[NH+](CCCN2C(=O),26,add ) at position 25,flow_matching,0.3,2.0,43,95
79,add,26.0,C,,C[C@@H]1CC[NH+](CCCN2C(=O),C[C@@H]1CC[NH+](CCCN2C(=O)C,27,add C at position 26,flow_matching,0.3,2.0,43,95
80,add,27.0,N,,C[C@@H]1CC[NH+](CCCN2C(=O)C,C[C@@H]1CC[NH+](CCCN2C(=O)CN,28,add N at position 27,flow_matching,0.3,2.0,43,95
81,add,28.0,C,,C[C@@H]1CC[NH+](CCCN2C(=O)CN,C[C@@H]1CC[NH+](CCCN2C(=O)CNC,29,add C at position 28,flow_matching,0.3,2.0,43,95
82,add,29.0,2,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2,30,add 2 at position 29,flow_matching,0.3,2.0,43,95
83,add,30.0,=,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=,31,add = at position 30,flow_matching,0.3,2.0,43,95
84,add,31.0,O,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O,32,add O at position 31,flow_matching,0.3,2.0,43,95
85,add,32.0,),,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O),33,add ) at position 32,flow_matching,0.3,2.0,43,95
86,add,33.0,C,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O),C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C,34,add C at position 33,flow_matching,0.3,2.0,43,95
87,add,34.0,[,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[,35,add [ at position 34,flow_matching,0.3,2.0,43,95
88,add,35.0,C,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C,36,add C at position 35,flow_matching,0.3,2.0,43,95
89,add,36.0,@,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@,37,add @ at position 36,flow_matching,0.3,2.0,43,95
90,add,37.0,@,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@,38,add @ at position 37,flow_matching,0.3,2.0,43,95
91,add,38.0,H,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@H,39,add H at position 38,flow_matching,0.3,2.0,43,95
92,add,39.0,],,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@H,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@H],40,add ] at position 39,flow_matching,0.3,2.0,43,95
93,add,40.0,1,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@H],C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@H]1,41,add 1 at position 40,flow_matching,0.3,2.0,43,95
94,add,41.0,O,,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@H]1,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@H]1O,42,add O at position 41,flow_matching,0.3,2.0,43,95
95,add,42.0,"
",,C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@H]1O,"C[C@@H]1CC[NH+](CCCN2C(=O)CNC2=O)C[C@@H]1O
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,95
