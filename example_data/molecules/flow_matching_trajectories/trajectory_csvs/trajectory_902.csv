step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,35,123
1,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,35,123
2,add,0.0,o,,I,oI,2,add o at position 0,flow_matching,0.3,2.0,35,123
3,replace,0.0,C,o,oI,CI,2,replace o at position 0 with C,flow_matching,0.3,2.0,35,123
4,add,2.0,/,,CI,CI/,3,add / at position 2,flow_matching,0.3,2.0,35,123
5,add,0.0,F,,CI/,FCI/,4,add F at position 0,flow_matching,0.3,2.0,35,123
6,replace,0.0,C,F,FCI/,CCI/,4,replace F at position 0 with C,flow_matching,0.3,2.0,35,123
7,remove,0.0,C,,CCI/,CI/,3,remove <PERSON> from position 0,flow_matching,0.3,2.0,35,123
8,add,2.0,n,,CI/,CIn/,4,add n at position 2,flow_matching,0.3,2.0,35,123
9,add,4.0,I,,CIn/,CIn/I,5,add I at position 4,flow_matching,0.3,2.0,35,123
10,add,5.0,4,,CIn/I,CIn/I4,6,add 4 at position 5,flow_matching,0.3,2.0,35,123
11,remove,1.0,I,,CIn/I4,Cn/I4,5,remove I from position 1,flow_matching,0.3,2.0,35,123
12,remove,3.0,I,,Cn/I4,Cn/4,4,remove I from position 3,flow_matching,0.3,2.0,35,123
13,remove,3.0,4,,Cn/4,Cn/,3,remove 4 from position 3,flow_matching,0.3,2.0,35,123
14,add,1.0,-,,Cn/,C-n/,4,add - at position 1,flow_matching,0.3,2.0,35,123
15,add,2.0,r,,C-n/,C-rn/,5,add r at position 2,flow_matching,0.3,2.0,35,123
16,add,2.0,H,,C-rn/,C-Hrn/,6,add H at position 2,flow_matching,0.3,2.0,35,123
17,replace,1.0,2,-,C-Hrn/,C2Hrn/,6,replace - at position 1 with 2,flow_matching,0.3,2.0,35,123
18,replace,1.0,O,2,C2Hrn/,COHrn/,6,replace 2 at position 1 with O,flow_matching,0.3,2.0,35,123
19,add,3.0,n,,COHrn/,COHnrn/,7,add n at position 3,flow_matching,0.3,2.0,35,123
20,remove,3.0,n,,COHnrn/,COHrn/,6,remove n from position 3,flow_matching,0.3,2.0,35,123
21,replace,1.0,c,O,COHrn/,CcHrn/,6,replace O at position 1 with c,flow_matching,0.3,2.0,35,123
22,replace,2.0,s,H,CcHrn/,Ccsrn/,6,replace H at position 2 with s,flow_matching,0.3,2.0,35,123
23,add,5.0,S,,Ccsrn/,CcsrnS/,7,add S at position 5,flow_matching,0.3,2.0,35,123
24,remove,0.0,C,,CcsrnS/,csrnS/,6,remove C from position 0,flow_matching,0.3,2.0,35,123
25,replace,3.0,-,n,csrnS/,csr-S/,6,replace n at position 3 with -,flow_matching,0.3,2.0,35,123
26,remove,1.0,s,,csr-S/,cr-S/,5,remove s from position 1,flow_matching,0.3,2.0,35,123
27,replace,0.0,C,c,cr-S/,Cr-S/,5,replace c at position 0 with C,flow_matching,0.3,2.0,35,123
28,remove,0.0,C,,Cr-S/,r-S/,4,remove C from position 0,flow_matching,0.3,2.0,35,123
29,add,2.0,=,,r-S/,r-=S/,5,add = at position 2,flow_matching,0.3,2.0,35,123
30,remove,2.0,=,,r-=S/,r-S/,4,remove = from position 2,flow_matching,0.3,2.0,35,123
31,add,0.0,),,r-S/,)r-S/,5,add ) at position 0,flow_matching,0.3,2.0,35,123
32,replace,4.0,2,/,)r-S/,)r-S2,5,replace / at position 4 with 2,flow_matching,0.3,2.0,35,123
33,add,1.0,(,,)r-S2,)(r-S2,6,add ( at position 1,flow_matching,0.3,2.0,35,123
34,replace,0.0,C,),)(r-S2,C(r-S2,6,replace ) at position 0 with C,flow_matching,0.3,2.0,35,123
35,add,6.0,l,,C(r-S2,C(r-S2l,7,add l at position 6,flow_matching,0.3,2.0,35,123
36,replace,3.0,r,-,C(r-S2l,C(rrS2l,7,replace - at position 3 with r,flow_matching,0.3,2.0,35,123
37,remove,3.0,r,,C(rrS2l,C(rS2l,6,remove r from position 3,flow_matching,0.3,2.0,35,123
38,add,6.0,6,,C(rS2l,C(rS2l6,7,add 6 at position 6,flow_matching,0.3,2.0,35,123
39,replace,1.0,c,(,C(rS2l6,CcrS2l6,7,replace ( at position 1 with c,flow_matching,0.3,2.0,35,123
40,replace,2.0,1,r,CcrS2l6,Cc1S2l6,7,replace r at position 2 with 1,flow_matching,0.3,2.0,35,123
41,replace,2.0,\,1,Cc1S2l6,Cc\S2l6,7,replace 1 at position 2 with \,flow_matching,0.3,2.0,35,123
42,replace,1.0,+,c,Cc\S2l6,C+\S2l6,7,replace c at position 1 with +,flow_matching,0.3,2.0,35,123
43,replace,1.0,c,+,C+\S2l6,Cc\S2l6,7,replace + at position 1 with c,flow_matching,0.3,2.0,35,123
44,add,5.0,4,,Cc\S2l6,Cc\S24l6,8,add 4 at position 5,flow_matching,0.3,2.0,35,123
45,add,7.0,4,,Cc\S24l6,Cc\S24l46,9,add 4 at position 7,flow_matching,0.3,2.0,35,123
46,add,7.0,r,,Cc\S24l46,Cc\S24lr46,10,add r at position 7,flow_matching,0.3,2.0,35,123
47,replace,1.0,l,c,Cc\S24lr46,Cl\S24lr46,10,replace c at position 1 with l,flow_matching,0.3,2.0,35,123
48,replace,8.0,2,4,Cl\S24lr46,Cl\S24lr26,10,replace 4 at position 8 with 2,flow_matching,0.3,2.0,35,123
49,replace,1.0,c,l,Cl\S24lr26,Cc\S24lr26,10,replace l at position 1 with c,flow_matching,0.3,2.0,35,123
50,add,8.0,l,,Cc\S24lr26,Cc\S24lrl26,11,add l at position 8,flow_matching,0.3,2.0,35,123
51,replace,2.0,1,\,Cc\S24lrl26,Cc1S24lrl26,11,replace \ at position 2 with 1,flow_matching,0.3,2.0,35,123
52,remove,7.0,r,,Cc1S24lrl26,Cc1S24ll26,10,remove r from position 7,flow_matching,0.3,2.0,35,123
53,replace,3.0,-,S,Cc1S24ll26,Cc1-24ll26,10,replace S at position 3 with -,flow_matching,0.3,2.0,35,123
54,add,1.0,l,,Cc1-24ll26,Clc1-24ll26,11,add l at position 1,flow_matching,0.3,2.0,35,123
55,add,0.0,+,,Clc1-24ll26,+Clc1-24ll26,12,add + at position 0,flow_matching,0.3,2.0,35,123
56,add,12.0,7,,+Clc1-24ll26,+Clc1-24ll267,13,add 7 at position 12,flow_matching,0.3,2.0,35,123
57,remove,1.0,C,,+Clc1-24ll267,+lc1-24ll267,12,remove C from position 1,flow_matching,0.3,2.0,35,123
58,replace,3.0,C,1,+lc1-24ll267,+lcC-24ll267,12,replace 1 at position 3 with C,flow_matching,0.3,2.0,35,123
59,replace,0.0,C,+,+lcC-24ll267,ClcC-24ll267,12,replace + at position 0 with C,flow_matching,0.3,2.0,35,123
60,replace,1.0,c,l,ClcC-24ll267,CccC-24ll267,12,replace l at position 1 with c,flow_matching,0.3,2.0,35,123
61,replace,1.0,#,c,CccC-24ll267,C#cC-24ll267,12,replace c at position 1 with #,flow_matching,0.3,2.0,35,123
62,remove,1.0,#,,C#cC-24ll267,CcC-24ll267,11,remove # from position 1,flow_matching,0.3,2.0,35,123
63,remove,9.0,6,,CcC-24ll267,CcC-24ll27,10,remove 6 from position 9,flow_matching,0.3,2.0,35,123
64,remove,0.0,C,,CcC-24ll27,cC-24ll27,9,remove C from position 0,flow_matching,0.3,2.0,35,123
65,replace,7.0,l,2,cC-24ll27,cC-24lll7,9,replace 2 at position 7 with l,flow_matching,0.3,2.0,35,123
66,replace,0.0,C,c,cC-24lll7,CC-24lll7,9,replace c at position 0 with C,flow_matching,0.3,2.0,35,123
67,add,6.0,5,,CC-24lll7,CC-24l5ll7,10,add 5 at position 6,flow_matching,0.3,2.0,35,123
68,replace,1.0,c,C,CC-24l5ll7,Cc-24l5ll7,10,replace C at position 1 with c,flow_matching,0.3,2.0,35,123
69,replace,2.0,1,-,Cc-24l5ll7,Cc124l5ll7,10,replace - at position 2 with 1,flow_matching,0.3,2.0,35,123
70,remove,8.0,l,,Cc124l5ll7,Cc124l5l7,9,remove l from position 8,flow_matching,0.3,2.0,35,123
71,replace,6.0,H,5,Cc124l5l7,Cc124lHl7,9,replace 5 at position 6 with H,flow_matching,0.3,2.0,35,123
72,replace,7.0,7,l,Cc124lHl7,Cc124lH77,9,replace l at position 7 with 7,flow_matching,0.3,2.0,35,123
73,replace,0.0,],C,Cc124lH77,]c124lH77,9,replace C at position 0 with ],flow_matching,0.3,2.0,35,123
74,replace,0.0,C,],]c124lH77,Cc124lH77,9,replace ] at position 0 with C,flow_matching,0.3,2.0,35,123
75,replace,3.0,c,2,Cc124lH77,Cc1c4lH77,9,replace 2 at position 3 with c,flow_matching,0.3,2.0,35,123
76,replace,4.0,c,4,Cc1c4lH77,Cc1cclH77,9,replace 4 at position 4 with c,flow_matching,0.3,2.0,35,123
77,add,1.0,I,,Cc1cclH77,CIc1cclH77,10,add I at position 1,flow_matching,0.3,2.0,35,123
78,add,7.0,s,,CIc1cclH77,CIc1cclsH77,11,add s at position 7,flow_matching,0.3,2.0,35,123
79,add,11.0,2,,CIc1cclsH77,CIc1cclsH772,12,add 2 at position 11,flow_matching,0.3,2.0,35,123
80,replace,5.0,#,c,CIc1cclsH772,CIc1c#lsH772,12,replace c at position 5 with #,flow_matching,0.3,2.0,35,123
81,add,4.0,#,,CIc1c#lsH772,CIc1#c#lsH772,13,add # at position 4,flow_matching,0.3,2.0,35,123
82,replace,1.0,c,I,CIc1#c#lsH772,Ccc1#c#lsH772,13,replace I at position 1 with c,flow_matching,0.3,2.0,35,123
83,add,5.0,],,Ccc1#c#lsH772,Ccc1#]c#lsH772,14,add ] at position 5,flow_matching,0.3,2.0,35,123
84,replace,3.0,r,1,Ccc1#]c#lsH772,Cccr#]c#lsH772,14,replace 1 at position 3 with r,flow_matching,0.3,2.0,35,123
85,replace,3.0,B,r,Cccr#]c#lsH772,CccB#]c#lsH772,14,replace r at position 3 with B,flow_matching,0.3,2.0,35,123
86,remove,10.0,H,,CccB#]c#lsH772,CccB#]c#ls772,13,remove H from position 10,flow_matching,0.3,2.0,35,123
87,replace,2.0,1,c,CccB#]c#ls772,Cc1B#]c#ls772,13,replace c at position 2 with 1,flow_matching,0.3,2.0,35,123
88,add,13.0,1,,Cc1B#]c#ls772,Cc1B#]c#ls7721,14,add 1 at position 13,flow_matching,0.3,2.0,35,123
89,replace,3.0,c,B,Cc1B#]c#ls7721,Cc1c#]c#ls7721,14,replace B at position 3 with c,flow_matching,0.3,2.0,35,123
90,remove,0.0,C,,Cc1c#]c#ls7721,c1c#]c#ls7721,13,remove C from position 0,flow_matching,0.3,2.0,35,123
91,add,0.0,N,,c1c#]c#ls7721,Nc1c#]c#ls7721,14,add N at position 0,flow_matching,0.3,2.0,35,123
92,replace,11.0,r,7,Nc1c#]c#ls7721,Nc1c#]c#ls7r21,14,replace 7 at position 11 with r,flow_matching,0.3,2.0,35,123
93,replace,0.0,C,N,Nc1c#]c#ls7r21,Cc1c#]c#ls7r21,14,replace N at position 0 with C,flow_matching,0.3,2.0,35,123
94,replace,4.0,c,#,Cc1c#]c#ls7r21,Cc1cc]c#ls7r21,14,replace # at position 4 with c,flow_matching,0.3,2.0,35,123
95,replace,5.0,c,],Cc1cc]c#ls7r21,Cc1cccc#ls7r21,14,replace ] at position 5 with c,flow_matching,0.3,2.0,35,123
96,replace,7.0,(,#,Cc1cccc#ls7r21,Cc1cccc(ls7r21,14,replace # at position 7 with (,flow_matching,0.3,2.0,35,123
97,replace,8.0,C,l,Cc1cccc(ls7r21,Cc1cccc(Cs7r21,14,replace l at position 8 with C,flow_matching,0.3,2.0,35,123
98,replace,9.0,),s,Cc1cccc(Cs7r21,Cc1cccc(C)7r21,14,replace s at position 9 with ),flow_matching,0.3,2.0,35,123
99,replace,10.0,c,7,Cc1cccc(C)7r21,Cc1cccc(C)cr21,14,replace 7 at position 10 with c,flow_matching,0.3,2.0,35,123
100,replace,11.0,1,r,Cc1cccc(C)cr21,Cc1cccc(C)c121,14,replace r at position 11 with 1,flow_matching,0.3,2.0,35,123
101,replace,12.0,-,2,Cc1cccc(C)c121,Cc1cccc(C)c1-1,14,replace 2 at position 12 with -,flow_matching,0.3,2.0,35,123
102,replace,13.0,n,1,Cc1cccc(C)c1-1,Cc1cccc(C)c1-n,14,replace 1 at position 13 with n,flow_matching,0.3,2.0,35,123
103,add,14.0,1,,Cc1cccc(C)c1-n,Cc1cccc(C)c1-n1,15,add 1 at position 14,flow_matching,0.3,2.0,35,123
104,add,15.0,n,,Cc1cccc(C)c1-n1,Cc1cccc(C)c1-n1n,16,add n at position 15,flow_matching,0.3,2.0,35,123
105,add,16.0,n,,Cc1cccc(C)c1-n1n,Cc1cccc(C)c1-n1nn,17,add n at position 16,flow_matching,0.3,2.0,35,123
106,add,17.0,n,,Cc1cccc(C)c1-n1nn,Cc1cccc(C)c1-n1nnn,18,add n at position 17,flow_matching,0.3,2.0,35,123
107,add,18.0,c,,Cc1cccc(C)c1-n1nnn,Cc1cccc(C)c1-n1nnnc,19,add c at position 18,flow_matching,0.3,2.0,35,123
108,add,19.0,1,,Cc1cccc(C)c1-n1nnnc,Cc1cccc(C)c1-n1nnnc1,20,add 1 at position 19,flow_matching,0.3,2.0,35,123
109,add,20.0,C,,Cc1cccc(C)c1-n1nnnc1,Cc1cccc(C)c1-n1nnnc1C,21,add C at position 20,flow_matching,0.3,2.0,35,123
110,add,21.0,S,,Cc1cccc(C)c1-n1nnnc1C,Cc1cccc(C)c1-n1nnnc1CS,22,add S at position 21,flow_matching,0.3,2.0,35,123
111,add,22.0,C,,Cc1cccc(C)c1-n1nnnc1CS,Cc1cccc(C)c1-n1nnnc1CSC,23,add C at position 22,flow_matching,0.3,2.0,35,123
112,add,23.0,c,,Cc1cccc(C)c1-n1nnnc1CSC,Cc1cccc(C)c1-n1nnnc1CSCc,24,add c at position 23,flow_matching,0.3,2.0,35,123
113,add,24.0,1,,Cc1cccc(C)c1-n1nnnc1CSCc,Cc1cccc(C)c1-n1nnnc1CSCc1,25,add 1 at position 24,flow_matching,0.3,2.0,35,123
114,add,25.0,n,,Cc1cccc(C)c1-n1nnnc1CSCc1,Cc1cccc(C)c1-n1nnnc1CSCc1n,26,add n at position 25,flow_matching,0.3,2.0,35,123
115,add,26.0,n,,Cc1cccc(C)c1-n1nnnc1CSCc1n,Cc1cccc(C)c1-n1nnnc1CSCc1nn,27,add n at position 26,flow_matching,0.3,2.0,35,123
116,add,27.0,c,,Cc1cccc(C)c1-n1nnnc1CSCc1nn,Cc1cccc(C)c1-n1nnnc1CSCc1nnc,28,add c at position 27,flow_matching,0.3,2.0,35,123
117,add,28.0,(,,Cc1cccc(C)c1-n1nnnc1CSCc1nnc,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(,29,add ( at position 28,flow_matching,0.3,2.0,35,123
118,add,29.0,C,,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C,30,add C at position 29,flow_matching,0.3,2.0,35,123
119,add,30.0,),,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C),31,add ) at position 30,flow_matching,0.3,2.0,35,123
120,add,31.0,n,,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C),Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C)n,32,add n at position 31,flow_matching,0.3,2.0,35,123
121,add,32.0,1,,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C)n,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C)n1,33,add 1 at position 32,flow_matching,0.3,2.0,35,123
122,add,33.0,C,,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C)n1,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C)n1C,34,add C at position 33,flow_matching,0.3,2.0,35,123
123,add,34.0,"
",,Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C)n1C,"Cc1cccc(C)c1-n1nnnc1CSCc1nnc(C)n1C
",35,"add 
 at position 34",flow_matching,0.3,2.0,35,123
