step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,52,217
1,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,52,217
2,add,1.0,2,,I,I2,2,add 2 at position 1,flow_matching,0.3,2.0,52,217
3,remove,1.0,2,,I2,I,1,remove 2 from position 1,flow_matching,0.3,2.0,52,217
4,remove,0.0,I,,I,,0,remove I from position 0,flow_matching,0.3,2.0,52,217
5,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,52,217
6,replace,0.0,C,6,6,C,1,replace 6 at position 0 with <PERSON>,flow_matching,0.3,2.0,52,217
7,replace,0.0,7,C,C,7,1,replace <PERSON> at position 0 with 7,flow_matching,0.3,2.0,52,217
8,replace,0.0,1,7,7,1,1,replace 7 at position 0 with 1,flow_matching,0.3,2.0,52,217
9,replace,0.0,/,1,1,/,1,replace 1 at position 0 with /,flow_matching,0.3,2.0,52,217
10,replace,0.0,C,/,/,C,1,replace / at position 0 with C,flow_matching,0.3,2.0,52,217
11,replace,0.0,(,C,C,(,1,replace C at position 0 with (,flow_matching,0.3,2.0,52,217
12,replace,0.0,#,(,(,#,1,replace ( at position 0 with #,flow_matching,0.3,2.0,52,217
13,add,0.0,7,,#,7#,2,add 7 at position 0,flow_matching,0.3,2.0,52,217
14,remove,1.0,#,,7#,7,1,remove # from position 1,flow_matching,0.3,2.0,52,217
15,replace,0.0,[,7,7,[,1,replace 7 at position 0 with [,flow_matching,0.3,2.0,52,217
16,replace,0.0,l,[,[,l,1,replace [ at position 0 with l,flow_matching,0.3,2.0,52,217
17,remove,0.0,l,,l,,0,remove l from position 0,flow_matching,0.3,2.0,52,217
18,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,52,217
19,replace,0.0,\,C,C,\,1,replace C at position 0 with \,flow_matching,0.3,2.0,52,217
20,replace,0.0,H,\,\,H,1,replace \ at position 0 with H,flow_matching,0.3,2.0,52,217
21,replace,0.0,C,H,H,C,1,replace H at position 0 with C,flow_matching,0.3,2.0,52,217
22,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,52,217
23,add,0.0,#,,CO,#CO,3,add # at position 0,flow_matching,0.3,2.0,52,217
24,add,1.0,o,,#CO,#oCO,4,add o at position 1,flow_matching,0.3,2.0,52,217
25,remove,2.0,C,,#oCO,#oO,3,remove C from position 2,flow_matching,0.3,2.0,52,217
26,remove,0.0,#,,#oO,oO,2,remove # from position 0,flow_matching,0.3,2.0,52,217
27,remove,1.0,O,,oO,o,1,remove O from position 1,flow_matching,0.3,2.0,52,217
28,remove,0.0,o,,o,,0,remove o from position 0,flow_matching,0.3,2.0,52,217
29,add,0.0,[,,,[,1,add [ at position 0,flow_matching,0.3,2.0,52,217
30,replace,0.0,o,[,[,o,1,replace [ at position 0 with o,flow_matching,0.3,2.0,52,217
31,remove,0.0,o,,o,,0,remove o from position 0,flow_matching,0.3,2.0,52,217
32,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,52,217
33,add,1.0,F,,C,CF,2,add F at position 1,flow_matching,0.3,2.0,52,217
34,remove,0.0,C,,CF,F,1,remove C from position 0,flow_matching,0.3,2.0,52,217
35,replace,0.0,C,F,F,C,1,replace F at position 0 with C,flow_matching,0.3,2.0,52,217
36,add,0.0,),,C,)C,2,add ) at position 0,flow_matching,0.3,2.0,52,217
37,add,0.0,),,)C,))C,3,add ) at position 0,flow_matching,0.3,2.0,52,217
38,add,1.0,+,,))C,)+)C,4,add + at position 1,flow_matching,0.3,2.0,52,217
39,add,0.0,3,,)+)C,3)+)C,5,add 3 at position 0,flow_matching,0.3,2.0,52,217
40,replace,0.0,C,3,3)+)C,C)+)C,5,replace 3 at position 0 with C,flow_matching,0.3,2.0,52,217
41,remove,2.0,+,,C)+)C,C))C,4,remove + from position 2,flow_matching,0.3,2.0,52,217
42,replace,3.0,I,C,C))C,C))I,4,replace C at position 3 with I,flow_matching,0.3,2.0,52,217
43,remove,0.0,C,,C))I,))I,3,remove C from position 0,flow_matching,0.3,2.0,52,217
44,replace,0.0,\,),))I,\)I,3,replace ) at position 0 with \,flow_matching,0.3,2.0,52,217
45,replace,1.0,r,),\)I,\rI,3,replace ) at position 1 with r,flow_matching,0.3,2.0,52,217
46,add,2.0,o,,\rI,\roI,4,add o at position 2,flow_matching,0.3,2.0,52,217
47,remove,1.0,r,,\roI,\oI,3,remove r from position 1,flow_matching,0.3,2.0,52,217
48,add,3.0,r,,\oI,\oIr,4,add r at position 3,flow_matching,0.3,2.0,52,217
49,add,1.0,@,,\oIr,\@oIr,5,add @ at position 1,flow_matching,0.3,2.0,52,217
50,replace,4.0,I,r,\@oIr,\@oII,5,replace r at position 4 with I,flow_matching,0.3,2.0,52,217
51,add,2.0,/,,\@oII,\@/oII,6,add / at position 2,flow_matching,0.3,2.0,52,217
52,replace,0.0,C,\,\@/oII,C@/oII,6,replace \ at position 0 with C,flow_matching,0.3,2.0,52,217
53,add,1.0,s,,C@/oII,Cs@/oII,7,add s at position 1,flow_matching,0.3,2.0,52,217
54,replace,1.0,O,s,Cs@/oII,CO@/oII,7,replace s at position 1 with O,flow_matching,0.3,2.0,52,217
55,replace,2.0,c,@,CO@/oII,COc/oII,7,replace @ at position 2 with c,flow_matching,0.3,2.0,52,217
56,replace,3.0,1,/,COc/oII,COc1oII,7,replace / at position 3 with 1,flow_matching,0.3,2.0,52,217
57,replace,4.0,c,o,COc1oII,COc1cII,7,replace o at position 4 with c,flow_matching,0.3,2.0,52,217
58,replace,5.0,c,I,COc1cII,COc1ccI,7,replace I at position 5 with c,flow_matching,0.3,2.0,52,217
59,add,2.0,+,,COc1ccI,CO+c1ccI,8,add + at position 2,flow_matching,0.3,2.0,52,217
60,add,0.0,6,,CO+c1ccI,6CO+c1ccI,9,add 6 at position 0,flow_matching,0.3,2.0,52,217
61,remove,6.0,c,,6CO+c1ccI,6CO+c1cI,8,remove c from position 6,flow_matching,0.3,2.0,52,217
62,replace,0.0,o,6,6CO+c1cI,oCO+c1cI,8,replace 6 at position 0 with o,flow_matching,0.3,2.0,52,217
63,add,4.0,o,,oCO+c1cI,oCO+oc1cI,9,add o at position 4,flow_matching,0.3,2.0,52,217
64,add,5.0,2,,oCO+oc1cI,oCO+o2c1cI,10,add 2 at position 5,flow_matching,0.3,2.0,52,217
65,remove,5.0,2,,oCO+o2c1cI,oCO+oc1cI,9,remove 2 from position 5,flow_matching,0.3,2.0,52,217
66,replace,0.0,C,o,oCO+oc1cI,CCO+oc1cI,9,replace o at position 0 with C,flow_matching,0.3,2.0,52,217
67,replace,1.0,O,C,CCO+oc1cI,COO+oc1cI,9,replace C at position 1 with O,flow_matching,0.3,2.0,52,217
68,replace,0.0,H,C,COO+oc1cI,HOO+oc1cI,9,replace C at position 0 with H,flow_matching,0.3,2.0,52,217
69,remove,8.0,I,,HOO+oc1cI,HOO+oc1c,8,remove I from position 8,flow_matching,0.3,2.0,52,217
70,replace,7.0,),c,HOO+oc1c,HOO+oc1),8,replace c at position 7 with ),flow_matching,0.3,2.0,52,217
71,add,7.0,c,,HOO+oc1),HOO+oc1c),9,add c at position 7,flow_matching,0.3,2.0,52,217
72,add,2.0,6,,HOO+oc1c),HO6O+oc1c),10,add 6 at position 2,flow_matching,0.3,2.0,52,217
73,add,5.0,o,,HO6O+oc1c),HO6O+ooc1c),11,add o at position 5,flow_matching,0.3,2.0,52,217
74,remove,2.0,6,,HO6O+ooc1c),HOO+ooc1c),10,remove 6 from position 2,flow_matching,0.3,2.0,52,217
75,replace,8.0,/,c,HOO+ooc1c),HOO+ooc1/),10,replace c at position 8 with /,flow_matching,0.3,2.0,52,217
76,remove,0.0,H,,HOO+ooc1/),OO+ooc1/),9,remove H from position 0,flow_matching,0.3,2.0,52,217
77,replace,7.0,s,/,OO+ooc1/),OO+ooc1s),9,replace / at position 7 with s,flow_matching,0.3,2.0,52,217
78,replace,0.0,6,O,OO+ooc1s),6O+ooc1s),9,replace O at position 0 with 6,flow_matching,0.3,2.0,52,217
79,replace,3.0,B,o,6O+ooc1s),6O+Boc1s),9,replace o at position 3 with B,flow_matching,0.3,2.0,52,217
80,replace,0.0,C,6,6O+Boc1s),CO+Boc1s),9,replace 6 at position 0 with C,flow_matching,0.3,2.0,52,217
81,replace,3.0,4,B,CO+Boc1s),CO+4oc1s),9,replace B at position 3 with 4,flow_matching,0.3,2.0,52,217
82,remove,1.0,O,,CO+4oc1s),C+4oc1s),8,remove O from position 1,flow_matching,0.3,2.0,52,217
83,add,6.0,n,,C+4oc1s),C+4oc1ns),9,add n at position 6,flow_matching,0.3,2.0,52,217
84,add,8.0,-,,C+4oc1ns),C+4oc1ns-),10,add - at position 8,flow_matching,0.3,2.0,52,217
85,replace,1.0,O,+,C+4oc1ns-),CO4oc1ns-),10,replace + at position 1 with O,flow_matching,0.3,2.0,52,217
86,replace,2.0,c,4,CO4oc1ns-),COcoc1ns-),10,replace 4 at position 2 with c,flow_matching,0.3,2.0,52,217
87,add,1.0,=,,COcoc1ns-),C=Ococ1ns-),11,add = at position 1,flow_matching,0.3,2.0,52,217
88,replace,1.0,O,=,C=Ococ1ns-),COOcoc1ns-),11,replace = at position 1 with O,flow_matching,0.3,2.0,52,217
89,replace,2.0,c,O,COOcoc1ns-),COccoc1ns-),11,replace O at position 2 with c,flow_matching,0.3,2.0,52,217
90,replace,3.0,1,c,COccoc1ns-),COc1oc1ns-),11,replace c at position 3 with 1,flow_matching,0.3,2.0,52,217
91,replace,4.0,c,o,COc1oc1ns-),COc1cc1ns-),11,replace o at position 4 with c,flow_matching,0.3,2.0,52,217
92,replace,6.0,c,1,COc1cc1ns-),COc1cccns-),11,replace 1 at position 6 with c,flow_matching,0.3,2.0,52,217
93,remove,5.0,c,,COc1cccns-),COc1ccns-),10,remove c from position 5,flow_matching,0.3,2.0,52,217
94,replace,9.0,c,),COc1ccns-),COc1ccns-c,10,replace ) at position 9 with c,flow_matching,0.3,2.0,52,217
95,add,0.0,[,,COc1ccns-c,[COc1ccns-c,11,add [ at position 0,flow_matching,0.3,2.0,52,217
96,replace,6.0,-,c,[COc1ccns-c,[COc1c-ns-c,11,replace c at position 6 with -,flow_matching,0.3,2.0,52,217
97,replace,0.0,C,[,[COc1c-ns-c,CCOc1c-ns-c,11,replace [ at position 0 with C,flow_matching,0.3,2.0,52,217
98,replace,1.0,O,C,CCOc1c-ns-c,COOc1c-ns-c,11,replace C at position 1 with O,flow_matching,0.3,2.0,52,217
99,replace,8.0,F,s,COOc1c-ns-c,COOc1c-nF-c,11,replace s at position 8 with F,flow_matching,0.3,2.0,52,217
100,replace,2.0,c,O,COOc1c-nF-c,COcc1c-nF-c,11,replace O at position 2 with c,flow_matching,0.3,2.0,52,217
101,add,0.0,#,,COcc1c-nF-c,#COcc1c-nF-c,12,add # at position 0,flow_matching,0.3,2.0,52,217
102,remove,9.0,F,,#COcc1c-nF-c,#COcc1c-n-c,11,remove F from position 9,flow_matching,0.3,2.0,52,217
103,add,9.0,=,,#COcc1c-n-c,#COcc1c-n=-c,12,add = at position 9,flow_matching,0.3,2.0,52,217
104,add,8.0,N,,#COcc1c-n=-c,#COcc1c-Nn=-c,13,add N at position 8,flow_matching,0.3,2.0,52,217
105,add,7.0,(,,#COcc1c-Nn=-c,#COcc1c(-Nn=-c,14,add ( at position 7,flow_matching,0.3,2.0,52,217
106,add,13.0,F,,#COcc1c(-Nn=-c,#COcc1c(-Nn=-Fc,15,add F at position 13,flow_matching,0.3,2.0,52,217
107,remove,4.0,c,,#COcc1c(-Nn=-Fc,#COc1c(-Nn=-Fc,14,remove c from position 4,flow_matching,0.3,2.0,52,217
108,remove,1.0,C,,#COc1c(-Nn=-Fc,#Oc1c(-Nn=-Fc,13,remove C from position 1,flow_matching,0.3,2.0,52,217
109,replace,0.0,C,#,#Oc1c(-Nn=-Fc,COc1c(-Nn=-Fc,13,replace # at position 0 with C,flow_matching,0.3,2.0,52,217
110,replace,5.0,c,(,COc1c(-Nn=-Fc,COc1cc-Nn=-Fc,13,replace ( at position 5 with c,flow_matching,0.3,2.0,52,217
111,remove,12.0,c,,COc1cc-Nn=-Fc,COc1cc-Nn=-F,12,remove c from position 12,flow_matching,0.3,2.0,52,217
112,remove,2.0,c,,COc1cc-Nn=-F,CO1cc-Nn=-F,11,remove c from position 2,flow_matching,0.3,2.0,52,217
113,replace,2.0,c,1,CO1cc-Nn=-F,COccc-Nn=-F,11,replace 1 at position 2 with c,flow_matching,0.3,2.0,52,217
114,remove,8.0,=,,COccc-Nn=-F,COccc-Nn-F,10,remove = from position 8,flow_matching,0.3,2.0,52,217
115,replace,3.0,1,c,COccc-Nn-F,COc1c-Nn-F,10,replace c at position 3 with 1,flow_matching,0.3,2.0,52,217
116,replace,5.0,c,-,COc1c-Nn-F,COc1ccNn-F,10,replace - at position 5 with c,flow_matching,0.3,2.0,52,217
117,replace,6.0,F,N,COc1ccNn-F,COc1ccFn-F,10,replace N at position 6 with F,flow_matching,0.3,2.0,52,217
118,replace,1.0,I,O,COc1ccFn-F,CIc1ccFn-F,10,replace O at position 1 with I,flow_matching,0.3,2.0,52,217
119,add,6.0,N,,CIc1ccFn-F,CIc1ccNFn-F,11,add N at position 6,flow_matching,0.3,2.0,52,217
120,remove,3.0,1,,CIc1ccNFn-F,CIcccNFn-F,10,remove 1 from position 3,flow_matching,0.3,2.0,52,217
121,replace,1.0,O,I,CIcccNFn-F,COcccNFn-F,10,replace I at position 1 with O,flow_matching,0.3,2.0,52,217
122,add,5.0,S,,COcccNFn-F,COcccSNFn-F,11,add S at position 5,flow_matching,0.3,2.0,52,217
123,remove,1.0,O,,COcccSNFn-F,CcccSNFn-F,10,remove O from position 1,flow_matching,0.3,2.0,52,217
124,add,6.0,+,,CcccSNFn-F,CcccSN+Fn-F,11,add + at position 6,flow_matching,0.3,2.0,52,217
125,replace,1.0,O,c,CcccSN+Fn-F,COccSN+Fn-F,11,replace c at position 1 with O,flow_matching,0.3,2.0,52,217
126,remove,6.0,+,,COccSN+Fn-F,COccSNFn-F,10,remove + from position 6,flow_matching,0.3,2.0,52,217
127,replace,0.0,I,C,COccSNFn-F,IOccSNFn-F,10,replace C at position 0 with I,flow_matching,0.3,2.0,52,217
128,replace,0.0,C,I,IOccSNFn-F,COccSNFn-F,10,replace I at position 0 with C,flow_matching,0.3,2.0,52,217
129,replace,3.0,1,c,COccSNFn-F,COc1SNFn-F,10,replace c at position 3 with 1,flow_matching,0.3,2.0,52,217
130,replace,4.0,c,S,COc1SNFn-F,COc1cNFn-F,10,replace S at position 4 with c,flow_matching,0.3,2.0,52,217
131,replace,5.0,c,N,COc1cNFn-F,COc1ccFn-F,10,replace N at position 5 with c,flow_matching,0.3,2.0,52,217
132,replace,0.0,),C,COc1ccFn-F,)Oc1ccFn-F,10,replace C at position 0 with ),flow_matching,0.3,2.0,52,217
133,replace,0.0,C,),)Oc1ccFn-F,COc1ccFn-F,10,replace ) at position 0 with C,flow_matching,0.3,2.0,52,217
134,replace,6.0,c,F,COc1ccFn-F,COc1cccn-F,10,replace F at position 6 with c,flow_matching,0.3,2.0,52,217
135,replace,9.0,4,F,COc1cccn-F,COc1cccn-4,10,replace F at position 9 with 4,flow_matching,0.3,2.0,52,217
136,remove,1.0,O,,COc1cccn-4,Cc1cccn-4,9,remove O from position 1,flow_matching,0.3,2.0,52,217
137,replace,1.0,O,c,Cc1cccn-4,CO1cccn-4,9,replace c at position 1 with O,flow_matching,0.3,2.0,52,217
138,add,4.0,C,,CO1cccn-4,CO1cCccn-4,10,add C at position 4,flow_matching,0.3,2.0,52,217
139,replace,3.0,5,c,CO1cCccn-4,CO15Cccn-4,10,replace c at position 3 with 5,flow_matching,0.3,2.0,52,217
140,replace,2.0,c,1,CO15Cccn-4,COc5Cccn-4,10,replace 1 at position 2 with c,flow_matching,0.3,2.0,52,217
141,add,4.0,o,,COc5Cccn-4,COc5oCccn-4,11,add o at position 4,flow_matching,0.3,2.0,52,217
142,replace,3.0,1,5,COc5oCccn-4,COc1oCccn-4,11,replace 5 at position 3 with 1,flow_matching,0.3,2.0,52,217
143,replace,8.0,2,n,COc1oCccn-4,COc1oCcc2-4,11,replace n at position 8 with 2,flow_matching,0.3,2.0,52,217
144,add,0.0,S,,COc1oCcc2-4,SCOc1oCcc2-4,12,add S at position 0,flow_matching,0.3,2.0,52,217
145,replace,8.0,[,c,SCOc1oCcc2-4,SCOc1oCc[2-4,12,replace c at position 8 with [,flow_matching,0.3,2.0,52,217
146,remove,11.0,4,,SCOc1oCc[2-4,SCOc1oCc[2-,11,remove 4 from position 11,flow_matching,0.3,2.0,52,217
147,replace,8.0,=,[,SCOc1oCc[2-,SCOc1oCc=2-,11,replace [ at position 8 with =,flow_matching,0.3,2.0,52,217
148,remove,6.0,C,,SCOc1oCc=2-,SCOc1oc=2-,10,remove C from position 6,flow_matching,0.3,2.0,52,217
149,remove,4.0,1,,SCOc1oc=2-,SCOcoc=2-,9,remove 1 from position 4,flow_matching,0.3,2.0,52,217
150,replace,0.0,C,S,SCOcoc=2-,CCOcoc=2-,9,replace S at position 0 with C,flow_matching,0.3,2.0,52,217
151,replace,0.0,(,C,CCOcoc=2-,(COcoc=2-,9,replace C at position 0 with (,flow_matching,0.3,2.0,52,217
152,replace,2.0,4,O,(COcoc=2-,(C4coc=2-,9,replace O at position 2 with 4,flow_matching,0.3,2.0,52,217
153,remove,3.0,c,,(C4coc=2-,(C4oc=2-,8,remove c from position 3,flow_matching,0.3,2.0,52,217
154,replace,1.0,N,C,(C4oc=2-,(N4oc=2-,8,replace C at position 1 with N,flow_matching,0.3,2.0,52,217
155,add,8.0,o,,(N4oc=2-,(N4oc=2-o,9,add o at position 8,flow_matching,0.3,2.0,52,217
156,remove,0.0,(,,(N4oc=2-o,N4oc=2-o,8,remove ( from position 0,flow_matching,0.3,2.0,52,217
157,add,6.0,6,,N4oc=2-o,N4oc=26-o,9,add 6 at position 6,flow_matching,0.3,2.0,52,217
158,remove,1.0,4,,N4oc=26-o,Noc=26-o,8,remove 4 from position 1,flow_matching,0.3,2.0,52,217
159,remove,0.0,N,,Noc=26-o,oc=26-o,7,remove N from position 0,flow_matching,0.3,2.0,52,217
160,remove,6.0,o,,oc=26-o,oc=26-,6,remove o from position 6,flow_matching,0.3,2.0,52,217
161,remove,0.0,o,,oc=26-,c=26-,5,remove o from position 0,flow_matching,0.3,2.0,52,217
162,replace,0.0,C,c,c=26-,C=26-,5,replace c at position 0 with C,flow_matching,0.3,2.0,52,217
163,add,2.0,O,,C=26-,C=O26-,6,add O at position 2,flow_matching,0.3,2.0,52,217
164,replace,1.0,O,=,C=O26-,COO26-,6,replace = at position 1 with O,flow_matching,0.3,2.0,52,217
165,replace,5.0,/,-,COO26-,COO26/,6,replace - at position 5 with /,flow_matching,0.3,2.0,52,217
166,add,6.0,5,,COO26/,COO26/5,7,add 5 at position 6,flow_matching,0.3,2.0,52,217
167,remove,4.0,6,,COO26/5,COO2/5,6,remove 6 from position 4,flow_matching,0.3,2.0,52,217
168,replace,2.0,c,O,COO2/5,COc2/5,6,replace O at position 2 with c,flow_matching,0.3,2.0,52,217
169,replace,3.0,1,2,COc2/5,COc1/5,6,replace 2 at position 3 with 1,flow_matching,0.3,2.0,52,217
170,replace,4.0,c,/,COc1/5,COc1c5,6,replace / at position 4 with c,flow_matching,0.3,2.0,52,217
171,replace,5.0,c,5,COc1c5,COc1cc,6,replace 5 at position 5 with c,flow_matching,0.3,2.0,52,217
172,add,6.0,c,,COc1cc,COc1ccc,7,add c at position 6,flow_matching,0.3,2.0,52,217
173,add,7.0,(,,COc1ccc,COc1ccc(,8,add ( at position 7,flow_matching,0.3,2.0,52,217
174,add,8.0,[,,COc1ccc(,COc1ccc([,9,add [ at position 8,flow_matching,0.3,2.0,52,217
175,add,9.0,C,,COc1ccc([,COc1ccc([C,10,add C at position 9,flow_matching,0.3,2.0,52,217
176,add,10.0,@,,COc1ccc([C,COc1ccc([C@,11,add @ at position 10,flow_matching,0.3,2.0,52,217
177,add,11.0,H,,COc1ccc([C@,COc1ccc([C@H,12,add H at position 11,flow_matching,0.3,2.0,52,217
178,add,12.0,],,COc1ccc([C@H,COc1ccc([C@H],13,add ] at position 12,flow_matching,0.3,2.0,52,217
179,add,13.0,(,,COc1ccc([C@H],COc1ccc([C@H](,14,add ( at position 13,flow_matching,0.3,2.0,52,217
180,add,14.0,C,,COc1ccc([C@H](,COc1ccc([C@H](C,15,add C at position 14,flow_matching,0.3,2.0,52,217
181,add,15.0,N,,COc1ccc([C@H](C,COc1ccc([C@H](CN,16,add N at position 15,flow_matching,0.3,2.0,52,217
182,add,16.0,C,,COc1ccc([C@H](CN,COc1ccc([C@H](CNC,17,add C at position 16,flow_matching,0.3,2.0,52,217
183,add,17.0,(,,COc1ccc([C@H](CNC,COc1ccc([C@H](CNC(,18,add ( at position 17,flow_matching,0.3,2.0,52,217
184,add,18.0,=,,COc1ccc([C@H](CNC(,COc1ccc([C@H](CNC(=,19,add = at position 18,flow_matching,0.3,2.0,52,217
185,add,19.0,O,,COc1ccc([C@H](CNC(=,COc1ccc([C@H](CNC(=O,20,add O at position 19,flow_matching,0.3,2.0,52,217
186,add,20.0,),,COc1ccc([C@H](CNC(=O,COc1ccc([C@H](CNC(=O),21,add ) at position 20,flow_matching,0.3,2.0,52,217
187,add,21.0,c,,COc1ccc([C@H](CNC(=O),COc1ccc([C@H](CNC(=O)c,22,add c at position 21,flow_matching,0.3,2.0,52,217
188,add,22.0,2,,COc1ccc([C@H](CNC(=O)c,COc1ccc([C@H](CNC(=O)c2,23,add 2 at position 22,flow_matching,0.3,2.0,52,217
189,add,23.0,c,,COc1ccc([C@H](CNC(=O)c2,COc1ccc([C@H](CNC(=O)c2c,24,add c at position 23,flow_matching,0.3,2.0,52,217
190,add,24.0,c,,COc1ccc([C@H](CNC(=O)c2c,COc1ccc([C@H](CNC(=O)c2cc,25,add c at position 24,flow_matching,0.3,2.0,52,217
191,add,25.0,c,,COc1ccc([C@H](CNC(=O)c2cc,COc1ccc([C@H](CNC(=O)c2ccc,26,add c at position 25,flow_matching,0.3,2.0,52,217
192,add,26.0,c,,COc1ccc([C@H](CNC(=O)c2ccc,COc1ccc([C@H](CNC(=O)c2cccc,27,add c at position 26,flow_matching,0.3,2.0,52,217
193,add,27.0,3,,COc1ccc([C@H](CNC(=O)c2cccc,COc1ccc([C@H](CNC(=O)c2cccc3,28,add 3 at position 27,flow_matching,0.3,2.0,52,217
194,add,28.0,c,,COc1ccc([C@H](CNC(=O)c2cccc3,COc1ccc([C@H](CNC(=O)c2cccc3c,29,add c at position 28,flow_matching,0.3,2.0,52,217
195,add,29.0,c,,COc1ccc([C@H](CNC(=O)c2cccc3c,COc1ccc([C@H](CNC(=O)c2cccc3cc,30,add c at position 29,flow_matching,0.3,2.0,52,217
196,add,30.0,c,,COc1ccc([C@H](CNC(=O)c2cccc3cc,COc1ccc([C@H](CNC(=O)c2cccc3ccc,31,add c at position 30,flow_matching,0.3,2.0,52,217
197,add,31.0,c,,COc1ccc([C@H](CNC(=O)c2cccc3ccc,COc1ccc([C@H](CNC(=O)c2cccc3cccc,32,add c at position 31,flow_matching,0.3,2.0,52,217
198,add,32.0,c,,COc1ccc([C@H](CNC(=O)c2cccc3cccc,COc1ccc([C@H](CNC(=O)c2cccc3ccccc,33,add c at position 32,flow_matching,0.3,2.0,52,217
199,add,33.0,2,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc,COc1ccc([C@H](CNC(=O)c2cccc3ccccc2,34,add 2 at position 33,flow_matching,0.3,2.0,52,217
200,add,34.0,3,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc2,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23,35,add 3 at position 34,flow_matching,0.3,2.0,52,217
201,add,35.0,),,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23),36,add ) at position 35,flow_matching,0.3,2.0,52,217
202,add,36.0,[,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23),COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[,37,add [ at position 36,flow_matching,0.3,2.0,52,217
203,add,37.0,N,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[N,38,add N at position 37,flow_matching,0.3,2.0,52,217
204,add,38.0,H,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[N,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH,39,add H at position 38,flow_matching,0.3,2.0,52,217
205,add,39.0,+,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+,40,add + at position 39,flow_matching,0.3,2.0,52,217
206,add,40.0,],,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+],41,add ] at position 40,flow_matching,0.3,2.0,52,217
207,add,41.0,2,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+],COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2,42,add 2 at position 41,flow_matching,0.3,2.0,52,217
208,add,42.0,C,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2C,43,add C at position 42,flow_matching,0.3,2.0,52,217
209,add,43.0,C,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2C,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CC,44,add C at position 43,flow_matching,0.3,2.0,52,217
210,add,44.0,C,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CC,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCC,45,add C at position 44,flow_matching,0.3,2.0,52,217
211,add,45.0,C,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCC,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC,46,add C at position 45,flow_matching,0.3,2.0,52,217
212,add,46.0,2,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2,47,add 2 at position 46,flow_matching,0.3,2.0,52,217
213,add,47.0,),,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2),48,add ) at position 47,flow_matching,0.3,2.0,52,217
214,add,48.0,c,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2),COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)c,49,add c at position 48,flow_matching,0.3,2.0,52,217
215,add,49.0,c,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)c,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)cc,50,add c at position 49,flow_matching,0.3,2.0,52,217
216,add,50.0,1,,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)cc,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)cc1,51,add 1 at position 50,flow_matching,0.3,2.0,52,217
217,add,51.0,"
",,COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)cc1,"COc1ccc([C@H](CNC(=O)c2cccc3ccccc23)[NH+]2CCCC2)cc1
",52,"add 
 at position 51",flow_matching,0.3,2.0,52,217
