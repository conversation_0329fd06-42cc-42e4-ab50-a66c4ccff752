step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,47,104
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,47,104
2,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,47,104
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,47,104
4,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,47,104
5,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,47,104
6,replace,0.0,C,B,B,C,1,replace <PERSON> at position 0 with C,flow_matching,0.3,2.0,47,104
7,add,1.0,S,,C,CS,2,add S at position 1,flow_matching,0.3,2.0,47,104
8,add,0.0,/,,CS,/CS,3,add / at position 0,flow_matching,0.3,2.0,47,104
9,replace,0.0,C,/,/CS,CCS,3,replace / at position 0 with C,flow_matching,0.3,2.0,47,104
10,add,0.0,),,CCS,)CCS,4,add ) at position 0,flow_matching,0.3,2.0,47,104
11,replace,3.0,),S,)CCS,)CC),4,replace S at position 3 with ),flow_matching,0.3,2.0,47,104
12,add,3.0,6,,)CC),)CC6),5,add 6 at position 3,flow_matching,0.3,2.0,47,104
13,replace,0.0,C,),)CC6),CCC6),5,replace ) at position 0 with C,flow_matching,0.3,2.0,47,104
14,replace,1.0,S,C,CCC6),CSC6),5,replace C at position 1 with S,flow_matching,0.3,2.0,47,104
15,replace,2.0,(,C,CSC6),CS(6),5,replace C at position 2 with (,flow_matching,0.3,2.0,47,104
16,add,1.0,r,,CS(6),CrS(6),6,add r at position 1,flow_matching,0.3,2.0,47,104
17,replace,5.0,F,),CrS(6),CrS(6F,6,replace ) at position 5 with F,flow_matching,0.3,2.0,47,104
18,replace,1.0,S,r,CrS(6F,CSS(6F,6,replace r at position 1 with S,flow_matching,0.3,2.0,47,104
19,remove,5.0,F,,CSS(6F,CSS(6,5,remove F from position 5,flow_matching,0.3,2.0,47,104
20,add,3.0,r,,CSS(6,CSSr(6,6,add r at position 3,flow_matching,0.3,2.0,47,104
21,replace,2.0,(,S,CSSr(6,CS(r(6,6,replace S at position 2 with (,flow_matching,0.3,2.0,47,104
22,replace,3.0,=,r,CS(r(6,CS(=(6,6,replace r at position 3 with =,flow_matching,0.3,2.0,47,104
23,remove,4.0,(,,CS(=(6,CS(=6,5,remove ( from position 4,flow_matching,0.3,2.0,47,104
24,replace,4.0,F,6,CS(=6,CS(=F,5,replace 6 at position 4 with F,flow_matching,0.3,2.0,47,104
25,remove,3.0,=,,CS(=F,CS(F,4,remove = from position 3,flow_matching,0.3,2.0,47,104
26,replace,2.0,B,(,CS(F,CSBF,4,replace ( at position 2 with B,flow_matching,0.3,2.0,47,104
27,remove,1.0,S,,CSBF,CBF,3,remove S from position 1,flow_matching,0.3,2.0,47,104
28,replace,0.0,H,C,CBF,HBF,3,replace C at position 0 with H,flow_matching,0.3,2.0,47,104
29,add,0.0,c,,HBF,cHBF,4,add c at position 0,flow_matching,0.3,2.0,47,104
30,add,4.0,C,,cHBF,cHBFC,5,add C at position 4,flow_matching,0.3,2.0,47,104
31,replace,0.0,C,c,cHBFC,CHBFC,5,replace c at position 0 with C,flow_matching,0.3,2.0,47,104
32,replace,1.0,S,H,CHBFC,CSBFC,5,replace H at position 1 with S,flow_matching,0.3,2.0,47,104
33,replace,2.0,(,B,CSBFC,CS(FC,5,replace B at position 2 with (,flow_matching,0.3,2.0,47,104
34,replace,3.0,=,F,CS(FC,CS(=C,5,replace F at position 3 with =,flow_matching,0.3,2.0,47,104
35,replace,4.0,O,C,CS(=C,CS(=O,5,replace C at position 4 with O,flow_matching,0.3,2.0,47,104
36,remove,2.0,(,,CS(=O,CS=O,4,remove ( from position 2,flow_matching,0.3,2.0,47,104
37,add,2.0,2,,CS=O,CS2=O,5,add 2 at position 2,flow_matching,0.3,2.0,47,104
38,remove,3.0,=,,CS2=O,CS2O,4,remove = from position 3,flow_matching,0.3,2.0,47,104
39,replace,2.0,(,2,CS2O,CS(O,4,replace 2 at position 2 with (,flow_matching,0.3,2.0,47,104
40,add,2.0,r,,CS(O,CSr(O,5,add r at position 2,flow_matching,0.3,2.0,47,104
41,add,1.0,H,,CSr(O,CHSr(O,6,add H at position 1,flow_matching,0.3,2.0,47,104
42,add,6.0,(,,CHSr(O,CHSr(O(,7,add ( at position 6,flow_matching,0.3,2.0,47,104
43,remove,1.0,H,,CHSr(O(,CSr(O(,6,remove H from position 1,flow_matching,0.3,2.0,47,104
44,add,0.0,1,,CSr(O(,1CSr(O(,7,add 1 at position 0,flow_matching,0.3,2.0,47,104
45,remove,0.0,1,,1CSr(O(,CSr(O(,6,remove 1 from position 0,flow_matching,0.3,2.0,47,104
46,remove,4.0,O,,CSr(O(,CSr((,5,remove O from position 4,flow_matching,0.3,2.0,47,104
47,replace,2.0,@,r,CSr((,CS@((,5,replace r at position 2 with @,flow_matching,0.3,2.0,47,104
48,add,1.0,6,,CS@((,C6S@((,6,add 6 at position 1,flow_matching,0.3,2.0,47,104
49,add,0.0,c,,C6S@((,cC6S@((,7,add c at position 0,flow_matching,0.3,2.0,47,104
50,replace,3.0,l,S,cC6S@((,cC6l@((,7,replace S at position 3 with l,flow_matching,0.3,2.0,47,104
51,add,0.0,N,,cC6l@((,NcC6l@((,8,add N at position 0,flow_matching,0.3,2.0,47,104
52,add,0.0,s,,NcC6l@((,sNcC6l@((,9,add s at position 0,flow_matching,0.3,2.0,47,104
53,replace,8.0,S,(,sNcC6l@((,sNcC6l@(S,9,replace ( at position 8 with S,flow_matching,0.3,2.0,47,104
54,replace,4.0,2,6,sNcC6l@(S,sNcC2l@(S,9,replace 6 at position 4 with 2,flow_matching,0.3,2.0,47,104
55,replace,0.0,4,s,sNcC2l@(S,4NcC2l@(S,9,replace s at position 0 with 4,flow_matching,0.3,2.0,47,104
56,remove,5.0,l,,4NcC2l@(S,4NcC2@(S,8,remove l from position 5,flow_matching,0.3,2.0,47,104
57,add,4.0,+,,4NcC2@(S,4NcC+2@(S,9,add + at position 4,flow_matching,0.3,2.0,47,104
58,replace,0.0,C,4,4NcC+2@(S,CNcC+2@(S,9,replace 4 at position 0 with C,flow_matching,0.3,2.0,47,104
59,replace,1.0,S,N,CNcC+2@(S,CScC+2@(S,9,replace N at position 1 with S,flow_matching,0.3,2.0,47,104
60,replace,2.0,(,c,CScC+2@(S,CS(C+2@(S,9,replace c at position 2 with (,flow_matching,0.3,2.0,47,104
61,replace,3.0,=,C,CS(C+2@(S,CS(=+2@(S,9,replace C at position 3 with =,flow_matching,0.3,2.0,47,104
62,replace,4.0,O,+,CS(=+2@(S,CS(=O2@(S,9,replace + at position 4 with O,flow_matching,0.3,2.0,47,104
63,replace,5.0,),2,CS(=O2@(S,CS(=O)@(S,9,replace 2 at position 5 with ),flow_matching,0.3,2.0,47,104
64,replace,6.0,(,@,CS(=O)@(S,CS(=O)((S,9,replace @ at position 6 with (,flow_matching,0.3,2.0,47,104
65,replace,7.0,=,(,CS(=O)((S,CS(=O)(=S,9,replace ( at position 7 with =,flow_matching,0.3,2.0,47,104
66,replace,8.0,O,S,CS(=O)(=S,CS(=O)(=O,9,replace S at position 8 with O,flow_matching,0.3,2.0,47,104
67,add,9.0,),,CS(=O)(=O,CS(=O)(=O),10,add ) at position 9,flow_matching,0.3,2.0,47,104
68,add,10.0,N,,CS(=O)(=O),CS(=O)(=O)N,11,add N at position 10,flow_matching,0.3,2.0,47,104
69,add,11.0,1,,CS(=O)(=O)N,CS(=O)(=O)N1,12,add 1 at position 11,flow_matching,0.3,2.0,47,104
70,add,12.0,C,,CS(=O)(=O)N1,CS(=O)(=O)N1C,13,add C at position 12,flow_matching,0.3,2.0,47,104
71,add,13.0,C,,CS(=O)(=O)N1C,CS(=O)(=O)N1CC,14,add C at position 13,flow_matching,0.3,2.0,47,104
72,add,14.0,C,,CS(=O)(=O)N1CC,CS(=O)(=O)N1CCC,15,add C at position 14,flow_matching,0.3,2.0,47,104
73,add,15.0,[,,CS(=O)(=O)N1CCC,CS(=O)(=O)N1CCC[,16,add [ at position 15,flow_matching,0.3,2.0,47,104
74,add,16.0,C,,CS(=O)(=O)N1CCC[,CS(=O)(=O)N1CCC[C,17,add C at position 16,flow_matching,0.3,2.0,47,104
75,add,17.0,@,,CS(=O)(=O)N1CCC[C,CS(=O)(=O)N1CCC[C@,18,add @ at position 17,flow_matching,0.3,2.0,47,104
76,add,18.0,@,,CS(=O)(=O)N1CCC[C@,CS(=O)(=O)N1CCC[C@@,19,add @ at position 18,flow_matching,0.3,2.0,47,104
77,add,19.0,H,,CS(=O)(=O)N1CCC[C@@,CS(=O)(=O)N1CCC[C@@H,20,add H at position 19,flow_matching,0.3,2.0,47,104
78,add,20.0,],,CS(=O)(=O)N1CCC[C@@H,CS(=O)(=O)N1CCC[C@@H],21,add ] at position 20,flow_matching,0.3,2.0,47,104
79,add,21.0,(,,CS(=O)(=O)N1CCC[C@@H],CS(=O)(=O)N1CCC[C@@H](,22,add ( at position 21,flow_matching,0.3,2.0,47,104
80,add,22.0,C,,CS(=O)(=O)N1CCC[C@@H](,CS(=O)(=O)N1CCC[C@@H](C,23,add C at position 22,flow_matching,0.3,2.0,47,104
81,add,23.0,[,,CS(=O)(=O)N1CCC[C@@H](C,CS(=O)(=O)N1CCC[C@@H](C[,24,add [ at position 23,flow_matching,0.3,2.0,47,104
82,add,24.0,N,,CS(=O)(=O)N1CCC[C@@H](C[,CS(=O)(=O)N1CCC[C@@H](C[N,25,add N at position 24,flow_matching,0.3,2.0,47,104
83,add,25.0,H,,CS(=O)(=O)N1CCC[C@@H](C[N,CS(=O)(=O)N1CCC[C@@H](C[NH,26,add H at position 25,flow_matching,0.3,2.0,47,104
84,add,26.0,+,,CS(=O)(=O)N1CCC[C@@H](C[NH,CS(=O)(=O)N1CCC[C@@H](C[NH+,27,add + at position 26,flow_matching,0.3,2.0,47,104
85,add,27.0,],,CS(=O)(=O)N1CCC[C@@H](C[NH+,CS(=O)(=O)N1CCC[C@@H](C[NH+],28,add ] at position 27,flow_matching,0.3,2.0,47,104
86,add,28.0,2,,CS(=O)(=O)N1CCC[C@@H](C[NH+],CS(=O)(=O)N1CCC[C@@H](C[NH+]2,29,add 2 at position 28,flow_matching,0.3,2.0,47,104
87,add,29.0,C,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2,CS(=O)(=O)N1CCC[C@@H](C[NH+]2C,30,add C at position 29,flow_matching,0.3,2.0,47,104
88,add,30.0,C,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2C,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CC,31,add C at position 30,flow_matching,0.3,2.0,47,104
89,add,31.0,C,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CC,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC,32,add C at position 31,flow_matching,0.3,2.0,47,104
90,add,32.0,[,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[,33,add [ at position 32,flow_matching,0.3,2.0,47,104
91,add,33.0,C,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C,34,add C at position 33,flow_matching,0.3,2.0,47,104
92,add,34.0,@,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@,35,add @ at position 34,flow_matching,0.3,2.0,47,104
93,add,35.0,H,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H,36,add H at position 35,flow_matching,0.3,2.0,47,104
94,add,36.0,],,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H],37,add ] at position 36,flow_matching,0.3,2.0,47,104
95,add,37.0,(,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H],CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](,38,add ( at position 37,flow_matching,0.3,2.0,47,104
96,add,38.0,C,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](C,39,add C at position 38,flow_matching,0.3,2.0,47,104
97,add,39.0,O,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](C,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO,40,add O at position 39,flow_matching,0.3,2.0,47,104
98,add,40.0,),,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO),41,add ) at position 40,flow_matching,0.3,2.0,47,104
99,add,41.0,C,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO),CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C,42,add C at position 41,flow_matching,0.3,2.0,47,104
100,add,42.0,2,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C2,43,add 2 at position 42,flow_matching,0.3,2.0,47,104
101,add,43.0,),,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C2,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C2),44,add ) at position 43,flow_matching,0.3,2.0,47,104
102,add,44.0,C,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C2),CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C2)C,45,add C at position 44,flow_matching,0.3,2.0,47,104
103,add,45.0,1,,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C2)C,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C2)C1,46,add 1 at position 45,flow_matching,0.3,2.0,47,104
104,add,46.0,"
",,CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C2)C1,"CS(=O)(=O)N1CCC[C@@H](C[NH+]2CCC[C@H](CO)C2)C1
",47,"add 
 at position 46",flow_matching,0.3,2.0,47,104
