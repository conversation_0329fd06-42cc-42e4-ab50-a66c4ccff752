step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,43,91
1,add,0.0,=,,,=,1,add = at position 0,flow_matching,0.3,2.0,43,91
2,replace,0.0,/,=,=,/,1,replace = at position 0 with /,flow_matching,0.3,2.0,43,91
3,remove,0.0,/,,/,,0,remove / from position 0,flow_matching,0.3,2.0,43,91
4,add,0.0,6,,,6,1,add 6 at position 0,flow_matching,0.3,2.0,43,91
5,replace,0.0,4,6,6,4,1,replace 6 at position 0 with 4,flow_matching,0.3,2.0,43,91
6,add,1.0,[,,4,4[,2,add [ at position 1,flow_matching,0.3,2.0,43,91
7,replace,0.0,C,4,4[,C[,2,replace 4 at position 0 with C,flow_matching,0.3,2.0,43,91
8,replace,1.0,c,[,C[,Cc,2,replace [ at position 1 with c,flow_matching,0.3,2.0,43,91
9,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,43,91
10,remove,0.0,C,,Cc1,c1,2,remove C from position 0,flow_matching,0.3,2.0,43,91
11,add,1.0,7,,c1,c71,3,add 7 at position 1,flow_matching,0.3,2.0,43,91
12,replace,0.0,C,c,c71,C71,3,replace c at position 0 with C,flow_matching,0.3,2.0,43,91
13,remove,2.0,1,,C71,C7,2,remove 1 from position 2,flow_matching,0.3,2.0,43,91
14,remove,0.0,C,,C7,7,1,remove C from position 0,flow_matching,0.3,2.0,43,91
15,replace,0.0,c,7,7,c,1,replace 7 at position 0 with c,flow_matching,0.3,2.0,43,91
16,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,43,91
17,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,43,91
18,remove,0.0,C,,Cc,c,1,remove C from position 0,flow_matching,0.3,2.0,43,91
19,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,43,91
20,add,0.0,7,,C,7C,2,add 7 at position 0,flow_matching,0.3,2.0,43,91
21,remove,0.0,7,,7C,C,1,remove 7 from position 0,flow_matching,0.3,2.0,43,91
22,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,43,91
23,add,0.0,1,,,1,1,add 1 at position 0,flow_matching,0.3,2.0,43,91
24,add,1.0,),,1,1),2,add ) at position 1,flow_matching,0.3,2.0,43,91
25,replace,0.0,C,1,1),C),2,replace 1 at position 0 with C,flow_matching,0.3,2.0,43,91
26,replace,1.0,c,),C),Cc,2,replace ) at position 1 with c,flow_matching,0.3,2.0,43,91
27,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,43,91
28,add,3.0,n,,Cc1,Cc1n,4,add n at position 3,flow_matching,0.3,2.0,43,91
29,replace,1.0,S,c,Cc1n,CS1n,4,replace c at position 1 with S,flow_matching,0.3,2.0,43,91
30,replace,3.0,O,n,CS1n,CS1O,4,replace n at position 3 with O,flow_matching,0.3,2.0,43,91
31,remove,1.0,S,,CS1O,C1O,3,remove S from position 1,flow_matching,0.3,2.0,43,91
32,replace,1.0,),1,C1O,C)O,3,replace 1 at position 1 with ),flow_matching,0.3,2.0,43,91
33,replace,0.0,n,C,C)O,n)O,3,replace C at position 0 with n,flow_matching,0.3,2.0,43,91
34,replace,2.0,H,O,n)O,n)H,3,replace O at position 2 with H,flow_matching,0.3,2.0,43,91
35,add,1.0,7,,n)H,n7)H,4,add 7 at position 1,flow_matching,0.3,2.0,43,91
36,replace,0.0,C,n,n7)H,C7)H,4,replace n at position 0 with C,flow_matching,0.3,2.0,43,91
37,add,2.0,B,,C7)H,C7B)H,5,add B at position 2,flow_matching,0.3,2.0,43,91
38,replace,3.0,s,),C7B)H,C7BsH,5,replace ) at position 3 with s,flow_matching,0.3,2.0,43,91
39,remove,0.0,C,,C7BsH,7BsH,4,remove C from position 0,flow_matching,0.3,2.0,43,91
40,replace,2.0,o,s,7BsH,7BoH,4,replace s at position 2 with o,flow_matching,0.3,2.0,43,91
41,add,3.0,\,,7BoH,7Bo\H,5,add \ at position 3,flow_matching,0.3,2.0,43,91
42,add,2.0,+,,7Bo\H,7B+o\H,6,add + at position 2,flow_matching,0.3,2.0,43,91
43,add,0.0,I,,7B+o\H,I7B+o\H,7,add I at position 0,flow_matching,0.3,2.0,43,91
44,add,2.0,o,,I7B+o\H,I7oB+o\H,8,add o at position 2,flow_matching,0.3,2.0,43,91
45,replace,4.0,1,+,I7oB+o\H,I7oB1o\H,8,replace + at position 4 with 1,flow_matching,0.3,2.0,43,91
46,remove,7.0,H,,I7oB1o\H,I7oB1o\,7,remove H from position 7,flow_matching,0.3,2.0,43,91
47,remove,2.0,o,,I7oB1o\,I7B1o\,6,remove o from position 2,flow_matching,0.3,2.0,43,91
48,add,0.0,o,,I7B1o\,oI7B1o\,7,add o at position 0,flow_matching,0.3,2.0,43,91
49,replace,0.0,C,o,oI7B1o\,CI7B1o\,7,replace o at position 0 with C,flow_matching,0.3,2.0,43,91
50,replace,1.0,c,I,CI7B1o\,Cc7B1o\,7,replace I at position 1 with c,flow_matching,0.3,2.0,43,91
51,replace,2.0,1,7,Cc7B1o\,Cc1B1o\,7,replace 7 at position 2 with 1,flow_matching,0.3,2.0,43,91
52,replace,3.0,n,B,Cc1B1o\,Cc1n1o\,7,replace B at position 3 with n,flow_matching,0.3,2.0,43,91
53,replace,4.0,o,1,Cc1n1o\,Cc1noo\,7,replace 1 at position 4 with o,flow_matching,0.3,2.0,43,91
54,replace,5.0,c,o,Cc1noo\,Cc1noc\,7,replace o at position 5 with c,flow_matching,0.3,2.0,43,91
55,replace,6.0,(,\,Cc1noc\,Cc1noc(,7,replace \ at position 6 with (,flow_matching,0.3,2.0,43,91
56,add,7.0,C,,Cc1noc(,Cc1noc(C,8,add C at position 7,flow_matching,0.3,2.0,43,91
57,add,8.0,),,Cc1noc(C,Cc1noc(C),9,add ) at position 8,flow_matching,0.3,2.0,43,91
58,add,9.0,c,,Cc1noc(C),Cc1noc(C)c,10,add c at position 9,flow_matching,0.3,2.0,43,91
59,add,10.0,1,,Cc1noc(C)c,Cc1noc(C)c1,11,add 1 at position 10,flow_matching,0.3,2.0,43,91
60,add,11.0,C,,Cc1noc(C)c1,Cc1noc(C)c1C,12,add C at position 11,flow_matching,0.3,2.0,43,91
61,add,12.0,O,,Cc1noc(C)c1C,Cc1noc(C)c1CO,13,add O at position 12,flow_matching,0.3,2.0,43,91
62,add,13.0,c,,Cc1noc(C)c1CO,Cc1noc(C)c1COc,14,add c at position 13,flow_matching,0.3,2.0,43,91
63,add,14.0,1,,Cc1noc(C)c1COc,Cc1noc(C)c1COc1,15,add 1 at position 14,flow_matching,0.3,2.0,43,91
64,add,15.0,c,,Cc1noc(C)c1COc1,Cc1noc(C)c1COc1c,16,add c at position 15,flow_matching,0.3,2.0,43,91
65,add,16.0,c,,Cc1noc(C)c1COc1c,Cc1noc(C)c1COc1cc,17,add c at position 16,flow_matching,0.3,2.0,43,91
66,add,17.0,c,,Cc1noc(C)c1COc1cc,Cc1noc(C)c1COc1ccc,18,add c at position 17,flow_matching,0.3,2.0,43,91
67,add,18.0,(,,Cc1noc(C)c1COc1ccc,Cc1noc(C)c1COc1ccc(,19,add ( at position 18,flow_matching,0.3,2.0,43,91
68,add,19.0,C,,Cc1noc(C)c1COc1ccc(,Cc1noc(C)c1COc1ccc(C,20,add C at position 19,flow_matching,0.3,2.0,43,91
69,add,20.0,[,,Cc1noc(C)c1COc1ccc(C,Cc1noc(C)c1COc1ccc(C[,21,add [ at position 20,flow_matching,0.3,2.0,43,91
70,add,21.0,N,,Cc1noc(C)c1COc1ccc(C[,Cc1noc(C)c1COc1ccc(C[N,22,add N at position 21,flow_matching,0.3,2.0,43,91
71,add,22.0,H,,Cc1noc(C)c1COc1ccc(C[N,Cc1noc(C)c1COc1ccc(C[NH,23,add H at position 22,flow_matching,0.3,2.0,43,91
72,add,23.0,2,,Cc1noc(C)c1COc1ccc(C[NH,Cc1noc(C)c1COc1ccc(C[NH2,24,add 2 at position 23,flow_matching,0.3,2.0,43,91
73,add,24.0,+,,Cc1noc(C)c1COc1ccc(C[NH2,Cc1noc(C)c1COc1ccc(C[NH2+,25,add + at position 24,flow_matching,0.3,2.0,43,91
74,add,25.0,],,Cc1noc(C)c1COc1ccc(C[NH2+,Cc1noc(C)c1COc1ccc(C[NH2+],26,add ] at position 25,flow_matching,0.3,2.0,43,91
75,add,26.0,C,,Cc1noc(C)c1COc1ccc(C[NH2+],Cc1noc(C)c1COc1ccc(C[NH2+]C,27,add C at position 26,flow_matching,0.3,2.0,43,91
76,add,27.0,[,,Cc1noc(C)c1COc1ccc(C[NH2+]C,Cc1noc(C)c1COc1ccc(C[NH2+]C[,28,add [ at position 27,flow_matching,0.3,2.0,43,91
77,add,28.0,C,,Cc1noc(C)c1COc1ccc(C[NH2+]C[,Cc1noc(C)c1COc1ccc(C[NH2+]C[C,29,add C at position 28,flow_matching,0.3,2.0,43,91
78,add,29.0,@,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@,30,add @ at position 29,flow_matching,0.3,2.0,43,91
79,add,30.0,H,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H,31,add H at position 30,flow_matching,0.3,2.0,43,91
80,add,31.0,],,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H],32,add ] at position 31,flow_matching,0.3,2.0,43,91
81,add,32.0,2,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H],Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2,33,add 2 at position 32,flow_matching,0.3,2.0,43,91
82,add,33.0,C,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2C,34,add C at position 33,flow_matching,0.3,2.0,43,91
83,add,34.0,C,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2C,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CC,35,add C at position 34,flow_matching,0.3,2.0,43,91
84,add,35.0,C,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CC,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCC,36,add C at position 35,flow_matching,0.3,2.0,43,91
85,add,36.0,O,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCC,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO,37,add O at position 36,flow_matching,0.3,2.0,43,91
86,add,37.0,2,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2,38,add 2 at position 37,flow_matching,0.3,2.0,43,91
87,add,38.0,),,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2),39,add ) at position 38,flow_matching,0.3,2.0,43,91
88,add,39.0,c,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2),Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2)c,40,add c at position 39,flow_matching,0.3,2.0,43,91
89,add,40.0,c,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2)c,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2)cc,41,add c at position 40,flow_matching,0.3,2.0,43,91
90,add,41.0,1,,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2)cc,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2)cc1,42,add 1 at position 41,flow_matching,0.3,2.0,43,91
91,add,42.0,"
",,Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2)cc1,"Cc1noc(C)c1COc1ccc(C[NH2+]C[C@H]2CCCO2)cc1
",43,"add 
 at position 42",flow_matching,0.3,2.0,43,91
