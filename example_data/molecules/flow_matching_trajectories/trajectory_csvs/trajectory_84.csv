step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,58,157
1,add,0.0,3,,,3,1,add 3 at position 0,flow_matching,0.3,2.0,58,157
2,replace,0.0,C,3,3,C,1,replace 3 at position 0 with C,flow_matching,0.3,2.0,58,157
3,replace,0.0,H,C,C,H,1,replace <PERSON> at position 0 with H,flow_matching,0.3,2.0,58,157
4,remove,0.0,H,,H,,0,remove H from position 0,flow_matching,0.3,2.0,58,157
5,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,58,157
6,replace,0.0,C,@,@,C,1,replace @ at position 0 with C,flow_matching,0.3,2.0,58,157
7,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,58,157
8,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,58,157
9,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,58,157
10,add,0.0,B,,,B,1,add B at position 0,flow_matching,0.3,2.0,58,157
11,add,0.0,r,,B,rB,2,add r at position 0,flow_matching,0.3,2.0,58,157
12,replace,1.0,I,B,rB,rI,2,replace B at position 1 with I,flow_matching,0.3,2.0,58,157
13,replace,0.0,C,r,rI,CI,2,replace r at position 0 with C,flow_matching,0.3,2.0,58,157
14,add,0.0,5,,CI,5CI,3,add 5 at position 0,flow_matching,0.3,2.0,58,157
15,add,0.0,4,,5CI,45CI,4,add 4 at position 0,flow_matching,0.3,2.0,58,157
16,add,0.0,O,,45CI,O45CI,5,add O at position 0,flow_matching,0.3,2.0,58,157
17,replace,4.0,[,I,O45CI,O45C[,5,replace I at position 4 with [,flow_matching,0.3,2.0,58,157
18,remove,1.0,4,,O45C[,O5C[,4,remove 4 from position 1,flow_matching,0.3,2.0,58,157
19,replace,3.0,+,[,O5C[,O5C+,4,replace [ at position 3 with +,flow_matching,0.3,2.0,58,157
20,remove,3.0,+,,O5C+,O5C,3,remove + from position 3,flow_matching,0.3,2.0,58,157
21,add,2.0,c,,O5C,O5cC,4,add c at position 2,flow_matching,0.3,2.0,58,157
22,replace,2.0,),c,O5cC,O5)C,4,replace c at position 2 with ),flow_matching,0.3,2.0,58,157
23,replace,0.0,C,O,O5)C,C5)C,4,replace O at position 0 with C,flow_matching,0.3,2.0,58,157
24,remove,0.0,C,,C5)C,5)C,3,remove C from position 0,flow_matching,0.3,2.0,58,157
25,replace,1.0,H,),5)C,5HC,3,replace ) at position 1 with H,flow_matching,0.3,2.0,58,157
26,replace,2.0,H,C,5HC,5HH,3,replace C at position 2 with H,flow_matching,0.3,2.0,58,157
27,add,3.0,\,,5HH,5HH\,4,add \ at position 3,flow_matching,0.3,2.0,58,157
28,add,4.0,o,,5HH\,5HH\o,5,add o at position 4,flow_matching,0.3,2.0,58,157
29,remove,3.0,\,,5HH\o,5HHo,4,remove \ from position 3,flow_matching,0.3,2.0,58,157
30,remove,0.0,5,,5HHo,HHo,3,remove 5 from position 0,flow_matching,0.3,2.0,58,157
31,replace,0.0,C,H,HHo,CHo,3,replace H at position 0 with C,flow_matching,0.3,2.0,58,157
32,replace,1.0,c,H,CHo,Cco,3,replace H at position 1 with c,flow_matching,0.3,2.0,58,157
33,replace,2.0,c,o,Cco,Ccc,3,replace o at position 2 with c,flow_matching,0.3,2.0,58,157
34,remove,2.0,c,,Ccc,Cc,2,remove c from position 2,flow_matching,0.3,2.0,58,157
35,remove,0.0,C,,Cc,c,1,remove C from position 0,flow_matching,0.3,2.0,58,157
36,remove,0.0,c,,c,,0,remove c from position 0,flow_matching,0.3,2.0,58,157
37,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,58,157
38,replace,0.0,C,@,@,C,1,replace @ at position 0 with C,flow_matching,0.3,2.0,58,157
39,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,58,157
40,replace,1.0,5,c,Cc,C5,2,replace c at position 1 with 5,flow_matching,0.3,2.0,58,157
41,replace,1.0,n,5,C5,Cn,2,replace 5 at position 1 with n,flow_matching,0.3,2.0,58,157
42,replace,1.0,F,n,Cn,CF,2,replace n at position 1 with F,flow_matching,0.3,2.0,58,157
43,replace,1.0,c,F,CF,Cc,2,replace F at position 1 with c,flow_matching,0.3,2.0,58,157
44,add,1.0,n,,Cc,Cnc,3,add n at position 1,flow_matching,0.3,2.0,58,157
45,add,0.0,/,,Cnc,/Cnc,4,add / at position 0,flow_matching,0.3,2.0,58,157
46,add,2.0,H,,/Cnc,/CHnc,5,add H at position 2,flow_matching,0.3,2.0,58,157
47,replace,0.0,C,/,/CHnc,CCHnc,5,replace / at position 0 with C,flow_matching,0.3,2.0,58,157
48,replace,1.0,c,C,CCHnc,CcHnc,5,replace C at position 1 with c,flow_matching,0.3,2.0,58,157
49,replace,2.0,l,H,CcHnc,Cclnc,5,replace H at position 2 with l,flow_matching,0.3,2.0,58,157
50,replace,3.0,=,n,Cclnc,Ccl=c,5,replace n at position 3 with =,flow_matching,0.3,2.0,58,157
51,add,0.0,r,,Ccl=c,rCcl=c,6,add r at position 0,flow_matching,0.3,2.0,58,157
52,remove,3.0,l,,rCcl=c,rCc=c,5,remove l from position 3,flow_matching,0.3,2.0,58,157
53,remove,2.0,c,,rCc=c,rC=c,4,remove c from position 2,flow_matching,0.3,2.0,58,157
54,add,2.0,o,,rC=c,rCo=c,5,add o at position 2,flow_matching,0.3,2.0,58,157
55,replace,0.0,C,r,rCo=c,CCo=c,5,replace r at position 0 with C,flow_matching,0.3,2.0,58,157
56,remove,1.0,C,,CCo=c,Co=c,4,remove C from position 1,flow_matching,0.3,2.0,58,157
57,remove,2.0,=,,Co=c,Coc,3,remove = from position 2,flow_matching,0.3,2.0,58,157
58,add,0.0,=,,Coc,=Coc,4,add = at position 0,flow_matching,0.3,2.0,58,157
59,remove,2.0,o,,=Coc,=Cc,3,remove o from position 2,flow_matching,0.3,2.0,58,157
60,add,2.0,c,,=Cc,=Ccc,4,add c at position 2,flow_matching,0.3,2.0,58,157
61,replace,0.0,C,=,=Ccc,CCcc,4,replace = at position 0 with C,flow_matching,0.3,2.0,58,157
62,replace,1.0,r,C,CCcc,Crcc,4,replace C at position 1 with r,flow_matching,0.3,2.0,58,157
63,remove,3.0,c,,Crcc,Crc,3,remove c from position 3,flow_matching,0.3,2.0,58,157
64,replace,1.0,c,r,Crc,Ccc,3,replace r at position 1 with c,flow_matching,0.3,2.0,58,157
65,replace,2.0,B,c,Ccc,CcB,3,replace c at position 2 with B,flow_matching,0.3,2.0,58,157
66,replace,2.0,1,B,CcB,Cc1,3,replace B at position 2 with 1,flow_matching,0.3,2.0,58,157
67,remove,1.0,c,,Cc1,C1,2,remove c from position 1,flow_matching,0.3,2.0,58,157
68,remove,1.0,1,,C1,C,1,remove 1 from position 1,flow_matching,0.3,2.0,58,157
69,replace,0.0,I,C,C,I,1,replace C at position 0 with I,flow_matching,0.3,2.0,58,157
70,replace,0.0,7,I,I,7,1,replace I at position 0 with 7,flow_matching,0.3,2.0,58,157
71,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,58,157
72,add,1.0,-,,C,C-,2,add - at position 1,flow_matching,0.3,2.0,58,157
73,add,1.0,B,,C-,CB-,3,add B at position 1,flow_matching,0.3,2.0,58,157
74,add,3.0,6,,CB-,CB-6,4,add 6 at position 3,flow_matching,0.3,2.0,58,157
75,replace,1.0,c,B,CB-6,Cc-6,4,replace B at position 1 with c,flow_matching,0.3,2.0,58,157
76,remove,1.0,c,,Cc-6,C-6,3,remove c from position 1,flow_matching,0.3,2.0,58,157
77,replace,1.0,c,-,C-6,Cc6,3,replace - at position 1 with c,flow_matching,0.3,2.0,58,157
78,replace,2.0,1,6,Cc6,Cc1,3,replace 6 at position 2 with 1,flow_matching,0.3,2.0,58,157
79,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,58,157
80,add,1.0,=,,Cc1c,C=c1c,5,add = at position 1,flow_matching,0.3,2.0,58,157
81,replace,2.0,3,c,C=c1c,C=31c,5,replace c at position 2 with 3,flow_matching,0.3,2.0,58,157
82,replace,0.0,l,C,C=31c,l=31c,5,replace C at position 0 with l,flow_matching,0.3,2.0,58,157
83,replace,4.0,3,c,l=31c,l=313,5,replace c at position 4 with 3,flow_matching,0.3,2.0,58,157
84,remove,0.0,l,,l=313,=313,4,remove l from position 0,flow_matching,0.3,2.0,58,157
85,add,1.0,7,,=313,=7313,5,add 7 at position 1,flow_matching,0.3,2.0,58,157
86,remove,3.0,1,,=7313,=733,4,remove 1 from position 3,flow_matching,0.3,2.0,58,157
87,add,2.0,/,,=733,=7/33,5,add / at position 2,flow_matching,0.3,2.0,58,157
88,replace,0.0,C,=,=7/33,C7/33,5,replace = at position 0 with C,flow_matching,0.3,2.0,58,157
89,remove,0.0,C,,C7/33,7/33,4,remove C from position 0,flow_matching,0.3,2.0,58,157
90,replace,0.0,C,7,7/33,C/33,4,replace 7 at position 0 with C,flow_matching,0.3,2.0,58,157
91,replace,2.0,F,3,C/33,C/F3,4,replace 3 at position 2 with F,flow_matching,0.3,2.0,58,157
92,remove,2.0,F,,C/F3,C/3,3,remove F from position 2,flow_matching,0.3,2.0,58,157
93,replace,1.0,3,/,C/3,C33,3,replace / at position 1 with 3,flow_matching,0.3,2.0,58,157
94,replace,1.0,c,3,C33,Cc3,3,replace 3 at position 1 with c,flow_matching,0.3,2.0,58,157
95,remove,2.0,3,,Cc3,Cc,2,remove 3 from position 2,flow_matching,0.3,2.0,58,157
96,add,1.0,S,,Cc,CSc,3,add S at position 1,flow_matching,0.3,2.0,58,157
97,replace,2.0,],c,CSc,CS],3,replace c at position 2 with ],flow_matching,0.3,2.0,58,157
98,replace,1.0,c,S,CS],Cc],3,replace S at position 1 with c,flow_matching,0.3,2.0,58,157
99,replace,2.0,1,],Cc],Cc1,3,replace ] at position 2 with 1,flow_matching,0.3,2.0,58,157
100,replace,1.0,-,c,Cc1,C-1,3,replace c at position 1 with -,flow_matching,0.3,2.0,58,157
101,replace,1.0,H,-,C-1,CH1,3,replace - at position 1 with H,flow_matching,0.3,2.0,58,157
102,replace,1.0,c,H,CH1,Cc1,3,replace H at position 1 with c,flow_matching,0.3,2.0,58,157
103,add,3.0,c,,Cc1,Cc1c,4,add c at position 3,flow_matching,0.3,2.0,58,157
104,add,4.0,c,,Cc1c,Cc1cc,5,add c at position 4,flow_matching,0.3,2.0,58,157
105,add,5.0,(,,Cc1cc,Cc1cc(,6,add ( at position 5,flow_matching,0.3,2.0,58,157
106,add,6.0,C,,Cc1cc(,Cc1cc(C,7,add C at position 6,flow_matching,0.3,2.0,58,157
107,add,7.0,),,Cc1cc(C,Cc1cc(C),8,add ) at position 7,flow_matching,0.3,2.0,58,157
108,add,8.0,c,,Cc1cc(C),Cc1cc(C)c,9,add c at position 8,flow_matching,0.3,2.0,58,157
109,add,9.0,2,,Cc1cc(C)c,Cc1cc(C)c2,10,add 2 at position 9,flow_matching,0.3,2.0,58,157
110,add,10.0,n,,Cc1cc(C)c2,Cc1cc(C)c2n,11,add n at position 10,flow_matching,0.3,2.0,58,157
111,add,11.0,c,,Cc1cc(C)c2n,Cc1cc(C)c2nc,12,add c at position 11,flow_matching,0.3,2.0,58,157
112,add,12.0,(,,Cc1cc(C)c2nc,Cc1cc(C)c2nc(,13,add ( at position 12,flow_matching,0.3,2.0,58,157
113,add,13.0,N,,Cc1cc(C)c2nc(,Cc1cc(C)c2nc(N,14,add N at position 13,flow_matching,0.3,2.0,58,157
114,add,14.0,3,,Cc1cc(C)c2nc(N,Cc1cc(C)c2nc(N3,15,add 3 at position 14,flow_matching,0.3,2.0,58,157
115,add,15.0,C,,Cc1cc(C)c2nc(N3,Cc1cc(C)c2nc(N3C,16,add C at position 15,flow_matching,0.3,2.0,58,157
116,add,16.0,C,,Cc1cc(C)c2nc(N3C,Cc1cc(C)c2nc(N3CC,17,add C at position 16,flow_matching,0.3,2.0,58,157
117,add,17.0,N,,Cc1cc(C)c2nc(N3CC,Cc1cc(C)c2nc(N3CCN,18,add N at position 17,flow_matching,0.3,2.0,58,157
118,add,18.0,(,,Cc1cc(C)c2nc(N3CCN,Cc1cc(C)c2nc(N3CCN(,19,add ( at position 18,flow_matching,0.3,2.0,58,157
119,add,19.0,C,,Cc1cc(C)c2nc(N3CCN(,Cc1cc(C)c2nc(N3CCN(C,20,add C at position 19,flow_matching,0.3,2.0,58,157
120,add,20.0,(,,Cc1cc(C)c2nc(N3CCN(C,Cc1cc(C)c2nc(N3CCN(C(,21,add ( at position 20,flow_matching,0.3,2.0,58,157
121,add,21.0,=,,Cc1cc(C)c2nc(N3CCN(C(,Cc1cc(C)c2nc(N3CCN(C(=,22,add = at position 21,flow_matching,0.3,2.0,58,157
122,add,22.0,O,,Cc1cc(C)c2nc(N3CCN(C(=,Cc1cc(C)c2nc(N3CCN(C(=O,23,add O at position 22,flow_matching,0.3,2.0,58,157
123,add,23.0,),,Cc1cc(C)c2nc(N3CCN(C(=O,Cc1cc(C)c2nc(N3CCN(C(=O),24,add ) at position 23,flow_matching,0.3,2.0,58,157
124,add,24.0,[,,Cc1cc(C)c2nc(N3CCN(C(=O),Cc1cc(C)c2nc(N3CCN(C(=O)[,25,add [ at position 24,flow_matching,0.3,2.0,58,157
125,add,25.0,C,,Cc1cc(C)c2nc(N3CCN(C(=O)[,Cc1cc(C)c2nc(N3CCN(C(=O)[C,26,add C at position 25,flow_matching,0.3,2.0,58,157
126,add,26.0,@,,Cc1cc(C)c2nc(N3CCN(C(=O)[C,Cc1cc(C)c2nc(N3CCN(C(=O)[C@,27,add @ at position 26,flow_matching,0.3,2.0,58,157
127,add,27.0,@,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@,28,add @ at position 27,flow_matching,0.3,2.0,58,157
128,add,28.0,H,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H,29,add H at position 28,flow_matching,0.3,2.0,58,157
129,add,29.0,],,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H],30,add ] at position 29,flow_matching,0.3,2.0,58,157
130,add,30.0,4,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H],Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4,31,add 4 at position 30,flow_matching,0.3,2.0,58,157
131,add,31.0,C,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4C,32,add C at position 31,flow_matching,0.3,2.0,58,157
132,add,32.0,C,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4C,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CC,33,add C at position 32,flow_matching,0.3,2.0,58,157
133,add,33.0,C,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CC,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCC,34,add C at position 33,flow_matching,0.3,2.0,58,157
134,add,34.0,C,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCC,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCC,35,add C at position 34,flow_matching,0.3,2.0,58,157
135,add,35.0,N,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCC,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN,36,add N at position 35,flow_matching,0.3,2.0,58,157
136,add,36.0,4,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4,37,add 4 at position 36,flow_matching,0.3,2.0,58,157
137,add,37.0,S,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S,38,add S at position 37,flow_matching,0.3,2.0,58,157
138,add,38.0,(,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(,39,add ( at position 38,flow_matching,0.3,2.0,58,157
139,add,39.0,C,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C,40,add C at position 39,flow_matching,0.3,2.0,58,157
140,add,40.0,),,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C),41,add ) at position 40,flow_matching,0.3,2.0,58,157
141,add,41.0,(,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C),Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(,42,add ( at position 41,flow_matching,0.3,2.0,58,157
142,add,42.0,=,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=,43,add = at position 42,flow_matching,0.3,2.0,58,157
143,add,43.0,O,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O,44,add O at position 43,flow_matching,0.3,2.0,58,157
144,add,44.0,),,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O),45,add ) at position 44,flow_matching,0.3,2.0,58,157
145,add,45.0,=,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O),Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=,46,add = at position 45,flow_matching,0.3,2.0,58,157
146,add,46.0,O,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O,47,add O at position 46,flow_matching,0.3,2.0,58,157
147,add,47.0,),,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O),48,add ) at position 47,flow_matching,0.3,2.0,58,157
148,add,48.0,C,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O),Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)C,49,add C at position 48,flow_matching,0.3,2.0,58,157
149,add,49.0,C,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)C,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC,50,add C at position 49,flow_matching,0.3,2.0,58,157
150,add,50.0,3,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3,51,add 3 at position 50,flow_matching,0.3,2.0,58,157
151,add,51.0,),,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3),52,add ) at position 51,flow_matching,0.3,2.0,58,157
152,add,52.0,s,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3),Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)s,53,add s at position 52,flow_matching,0.3,2.0,58,157
153,add,53.0,c,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)s,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)sc,54,add c at position 53,flow_matching,0.3,2.0,58,157
154,add,54.0,2,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)sc,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)sc2,55,add 2 at position 54,flow_matching,0.3,2.0,58,157
155,add,55.0,c,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)sc2,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)sc2c,56,add c at position 55,flow_matching,0.3,2.0,58,157
156,add,56.0,1,,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)sc2c,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)sc2c1,57,add 1 at position 56,flow_matching,0.3,2.0,58,157
157,add,57.0,"
",,Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)sc2c1,"Cc1cc(C)c2nc(N3CCN(C(=O)[C@@H]4CCCCN4S(C)(=O)=O)CC3)sc2c1
",58,"add 
 at position 57",flow_matching,0.3,2.0,58,157
