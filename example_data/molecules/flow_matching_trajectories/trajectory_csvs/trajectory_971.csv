step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,58,180
1,add,0.0,#,,,#,1,add # at position 0,flow_matching,0.3,2.0,58,180
2,add,1.0,F,,#,#F,2,add F at position 1,flow_matching,0.3,2.0,58,180
3,remove,1.0,F,,#F,#,1,remove F from position 1,flow_matching,0.3,2.0,58,180
4,replace,0.0,C,#,#,C,1,replace # at position 0 with C,flow_matching,0.3,2.0,58,180
5,replace,0.0,2,C,C,2,1,replace <PERSON> at position 0 with 2,flow_matching,0.3,2.0,58,180
6,add,1.0,B,,2,2B,2,add B at position 1,flow_matching,0.3,2.0,58,180
7,remove,1.0,B,,2B,2,1,remove B from position 1,flow_matching,0.3,2.0,58,180
8,add,0.0,=,,2,=2,2,add = at position 0,flow_matching,0.3,2.0,58,180
9,replace,0.0,C,=,=2,C2,2,replace = at position 0 with C,flow_matching,0.3,2.0,58,180
10,replace,1.0,C,2,C2,CC,2,replace 2 at position 1 with C,flow_matching,0.3,2.0,58,180
11,add,2.0,C,,CC,CCC,3,add C at position 2,flow_matching,0.3,2.0,58,180
12,add,3.0,(,,CCC,CCC(,4,add ( at position 3,flow_matching,0.3,2.0,58,180
13,add,2.0,N,,CCC(,CCNC(,5,add N at position 2,flow_matching,0.3,2.0,58,180
14,replace,2.0,C,N,CCNC(,CCCC(,5,replace N at position 2 with C,flow_matching,0.3,2.0,58,180
15,replace,3.0,(,C,CCCC(,CCC((,5,replace C at position 3 with (,flow_matching,0.3,2.0,58,180
16,replace,3.0,n,(,CCC((,CCCn(,5,replace ( at position 3 with n,flow_matching,0.3,2.0,58,180
17,remove,4.0,(,,CCCn(,CCCn,4,remove ( from position 4,flow_matching,0.3,2.0,58,180
18,remove,3.0,n,,CCCn,CCC,3,remove n from position 3,flow_matching,0.3,2.0,58,180
19,add,2.0,],,CCC,CC]C,4,add ] at position 2,flow_matching,0.3,2.0,58,180
20,add,3.0,o,,CC]C,CC]oC,5,add o at position 3,flow_matching,0.3,2.0,58,180
21,add,1.0,(,,CC]oC,C(C]oC,6,add ( at position 1,flow_matching,0.3,2.0,58,180
22,add,4.0,+,,C(C]oC,C(C]+oC,7,add + at position 4,flow_matching,0.3,2.0,58,180
23,replace,2.0,F,C,C(C]+oC,C(F]+oC,7,replace C at position 2 with F,flow_matching,0.3,2.0,58,180
24,remove,6.0,C,,C(F]+oC,C(F]+o,6,remove C from position 6,flow_matching,0.3,2.0,58,180
25,add,5.0,(,,C(F]+o,C(F]+(o,7,add ( at position 5,flow_matching,0.3,2.0,58,180
26,add,4.0,\,,C(F]+(o,C(F]\+(o,8,add \ at position 4,flow_matching,0.3,2.0,58,180
27,add,6.0,-,,C(F]\+(o,C(F]\+-(o,9,add - at position 6,flow_matching,0.3,2.0,58,180
28,replace,4.0,3,\,C(F]\+-(o,C(F]3+-(o,9,replace \ at position 4 with 3,flow_matching,0.3,2.0,58,180
29,add,8.0,7,,C(F]3+-(o,C(F]3+-(7o,10,add 7 at position 8,flow_matching,0.3,2.0,58,180
30,replace,1.0,C,(,C(F]3+-(7o,CCF]3+-(7o,10,replace ( at position 1 with C,flow_matching,0.3,2.0,58,180
31,remove,0.0,C,,CCF]3+-(7o,CF]3+-(7o,9,remove C from position 0,flow_matching,0.3,2.0,58,180
32,replace,6.0,S,(,CF]3+-(7o,CF]3+-S7o,9,replace ( at position 6 with S,flow_matching,0.3,2.0,58,180
33,replace,1.0,C,F,CF]3+-S7o,CC]3+-S7o,9,replace F at position 1 with C,flow_matching,0.3,2.0,58,180
34,add,7.0,N,,CC]3+-S7o,CC]3+-SN7o,10,add N at position 7,flow_matching,0.3,2.0,58,180
35,remove,6.0,S,,CC]3+-SN7o,CC]3+-N7o,9,remove S from position 6,flow_matching,0.3,2.0,58,180
36,replace,2.0,C,],CC]3+-N7o,CCC3+-N7o,9,replace ] at position 2 with C,flow_matching,0.3,2.0,58,180
37,remove,1.0,C,,CCC3+-N7o,CC3+-N7o,8,remove C from position 1,flow_matching,0.3,2.0,58,180
38,add,0.0,O,,CC3+-N7o,OCC3+-N7o,9,add O at position 0,flow_matching,0.3,2.0,58,180
39,replace,4.0,S,+,OCC3+-N7o,OCC3S-N7o,9,replace + at position 4 with S,flow_matching,0.3,2.0,58,180
40,replace,0.0,C,O,OCC3S-N7o,CCC3S-N7o,9,replace O at position 0 with C,flow_matching,0.3,2.0,58,180
41,remove,8.0,o,,CCC3S-N7o,CCC3S-N7,8,remove o from position 8,flow_matching,0.3,2.0,58,180
42,remove,1.0,C,,CCC3S-N7,CC3S-N7,7,remove C from position 1,flow_matching,0.3,2.0,58,180
43,replace,4.0,5,-,CC3S-N7,CC3S5N7,7,replace - at position 4 with 5,flow_matching,0.3,2.0,58,180
44,add,6.0,7,,CC3S5N7,CC3S5N77,8,add 7 at position 6,flow_matching,0.3,2.0,58,180
45,replace,2.0,C,3,CC3S5N77,CCCS5N77,8,replace 3 at position 2 with C,flow_matching,0.3,2.0,58,180
46,add,1.0,[,,CCCS5N77,C[CCS5N77,9,add [ at position 1,flow_matching,0.3,2.0,58,180
47,add,3.0,=,,C[CCS5N77,C[C=CS5N77,10,add = at position 3,flow_matching,0.3,2.0,58,180
48,add,9.0,-,,C[C=CS5N77,C[C=CS5N7-7,11,add - at position 9,flow_matching,0.3,2.0,58,180
49,replace,1.0,C,[,C[C=CS5N7-7,CCC=CS5N7-7,11,replace [ at position 1 with C,flow_matching,0.3,2.0,58,180
50,add,7.0,l,,CCC=CS5N7-7,CCC=CS5lN7-7,12,add l at position 7,flow_matching,0.3,2.0,58,180
51,remove,10.0,-,,CCC=CS5lN7-7,CCC=CS5lN77,11,remove - from position 10,flow_matching,0.3,2.0,58,180
52,remove,9.0,7,,CCC=CS5lN77,CCC=CS5lN7,10,remove 7 from position 9,flow_matching,0.3,2.0,58,180
53,add,1.0,3,,CCC=CS5lN7,C3CC=CS5lN7,11,add 3 at position 1,flow_matching,0.3,2.0,58,180
54,add,10.0,n,,C3CC=CS5lN7,C3CC=CS5lNn7,12,add n at position 10,flow_matching,0.3,2.0,58,180
55,remove,2.0,C,,C3CC=CS5lNn7,C3C=CS5lNn7,11,remove C from position 2,flow_matching,0.3,2.0,58,180
56,replace,1.0,C,3,C3C=CS5lNn7,CCC=CS5lNn7,11,replace 3 at position 1 with C,flow_matching,0.3,2.0,58,180
57,replace,2.0,6,C,CCC=CS5lNn7,CC6=CS5lNn7,11,replace C at position 2 with 6,flow_matching,0.3,2.0,58,180
58,add,7.0,@,,CC6=CS5lNn7,CC6=CS5@lNn7,12,add @ at position 7,flow_matching,0.3,2.0,58,180
59,remove,0.0,C,,CC6=CS5@lNn7,C6=CS5@lNn7,11,remove C from position 0,flow_matching,0.3,2.0,58,180
60,replace,2.0,],=,C6=CS5@lNn7,C6]CS5@lNn7,11,replace = at position 2 with ],flow_matching,0.3,2.0,58,180
61,add,5.0,C,,C6]CS5@lNn7,C6]CSC5@lNn7,12,add C at position 5,flow_matching,0.3,2.0,58,180
62,replace,6.0,B,5,C6]CSC5@lNn7,C6]CSCB@lNn7,12,replace 5 at position 6 with B,flow_matching,0.3,2.0,58,180
63,remove,10.0,n,,C6]CSCB@lNn7,C6]CSCB@lN7,11,remove n from position 10,flow_matching,0.3,2.0,58,180
64,add,0.0,-,,C6]CSCB@lN7,-C6]CSCB@lN7,12,add - at position 0,flow_matching,0.3,2.0,58,180
65,remove,9.0,l,,-C6]CSCB@lN7,-C6]CSCB@N7,11,remove l from position 9,flow_matching,0.3,2.0,58,180
66,remove,5.0,S,,-C6]CSCB@N7,-C6]CCB@N7,10,remove S from position 5,flow_matching,0.3,2.0,58,180
67,replace,0.0,C,-,-C6]CCB@N7,CC6]CCB@N7,10,replace - at position 0 with C,flow_matching,0.3,2.0,58,180
68,add,4.0,c,,CC6]CCB@N7,CC6]cCCB@N7,11,add c at position 4,flow_matching,0.3,2.0,58,180
69,remove,10.0,7,,CC6]cCCB@N7,CC6]cCCB@N,10,remove 7 from position 10,flow_matching,0.3,2.0,58,180
70,add,4.0,/,,CC6]cCCB@N,CC6]/cCCB@N,11,add / at position 4,flow_matching,0.3,2.0,58,180
71,add,6.0,o,,CC6]/cCCB@N,CC6]/coCCB@N,12,add o at position 6,flow_matching,0.3,2.0,58,180
72,replace,11.0,\,N,CC6]/coCCB@N,CC6]/coCCB@\,12,replace N at position 11 with \,flow_matching,0.3,2.0,58,180
73,replace,3.0,=,],CC6]/coCCB@\,CC6=/coCCB@\,12,replace ] at position 3 with =,flow_matching,0.3,2.0,58,180
74,add,1.0,o,,CC6=/coCCB@\,CoC6=/coCCB@\,13,add o at position 1,flow_matching,0.3,2.0,58,180
75,remove,8.0,C,,CoC6=/coCCB@\,CoC6=/coCB@\,12,remove C from position 8,flow_matching,0.3,2.0,58,180
76,replace,1.0,r,o,CoC6=/coCB@\,CrC6=/coCB@\,12,replace o at position 1 with r,flow_matching,0.3,2.0,58,180
77,replace,8.0,l,C,CrC6=/coCB@\,CrC6=/colB@\,12,replace C at position 8 with l,flow_matching,0.3,2.0,58,180
78,replace,1.0,C,r,CrC6=/colB@\,CCC6=/colB@\,12,replace r at position 1 with C,flow_matching,0.3,2.0,58,180
79,replace,3.0,(,6,CCC6=/colB@\,CCC(=/colB@\,12,replace 6 at position 3 with (,flow_matching,0.3,2.0,58,180
80,add,4.0,l,,CCC(=/colB@\,CCC(l=/colB@\,13,add l at position 4,flow_matching,0.3,2.0,58,180
81,replace,4.0,=,l,CCC(l=/colB@\,CCC(==/colB@\,13,replace l at position 4 with =,flow_matching,0.3,2.0,58,180
82,replace,12.0,l,\,CCC(==/colB@\,CCC(==/colB@l,13,replace \ at position 12 with l,flow_matching,0.3,2.0,58,180
83,remove,9.0,l,,CCC(==/colB@l,CCC(==/coB@l,12,remove l from position 9,flow_matching,0.3,2.0,58,180
84,remove,7.0,c,,CCC(==/coB@l,CCC(==/oB@l,11,remove c from position 7,flow_matching,0.3,2.0,58,180
85,replace,1.0,s,C,CCC(==/oB@l,CsC(==/oB@l,11,replace C at position 1 with s,flow_matching,0.3,2.0,58,180
86,replace,1.0,C,s,CsC(==/oB@l,CCC(==/oB@l,11,replace s at position 1 with C,flow_matching,0.3,2.0,58,180
87,add,7.0,(,,CCC(==/oB@l,CCC(==/(oB@l,12,add ( at position 7,flow_matching,0.3,2.0,58,180
88,replace,9.0,],B,CCC(==/(oB@l,CCC(==/(o]@l,12,replace B at position 9 with ],flow_matching,0.3,2.0,58,180
89,add,9.0,=,,CCC(==/(o]@l,CCC(==/(o=]@l,13,add = at position 9,flow_matching,0.3,2.0,58,180
90,replace,1.0,7,C,CCC(==/(o=]@l,C7C(==/(o=]@l,13,replace C at position 1 with 7,flow_matching,0.3,2.0,58,180
91,replace,12.0,(,l,C7C(==/(o=]@l,C7C(==/(o=]@(,13,replace l at position 12 with (,flow_matching,0.3,2.0,58,180
92,add,3.0,2,,C7C(==/(o=]@(,C7C2(==/(o=]@(,14,add 2 at position 3,flow_matching,0.3,2.0,58,180
93,replace,5.0,],=,C7C2(==/(o=]@(,C7C2(]=/(o=]@(,14,replace = at position 5 with ],flow_matching,0.3,2.0,58,180
94,add,3.0,/,,C7C2(]=/(o=]@(,C7C/2(]=/(o=]@(,15,add / at position 3,flow_matching,0.3,2.0,58,180
95,add,6.0,n,,C7C/2(]=/(o=]@(,C7C/2(n]=/(o=]@(,16,add n at position 6,flow_matching,0.3,2.0,58,180
96,add,14.0,s,,C7C/2(n]=/(o=]@(,C7C/2(n]=/(o=]s@(,17,add s at position 14,flow_matching,0.3,2.0,58,180
97,add,10.0,C,,C7C/2(n]=/(o=]s@(,C7C/2(n]=/C(o=]s@(,18,add C at position 10,flow_matching,0.3,2.0,58,180
98,replace,1.0,C,7,C7C/2(n]=/C(o=]s@(,CCC/2(n]=/C(o=]s@(,18,replace 7 at position 1 with C,flow_matching,0.3,2.0,58,180
99,replace,3.0,(,/,CCC/2(n]=/C(o=]s@(,CCC(2(n]=/C(o=]s@(,18,replace / at position 3 with (,flow_matching,0.3,2.0,58,180
100,add,16.0,-,,CCC(2(n]=/C(o=]s@(,CCC(2(n]=/C(o=]s-@(,19,add - at position 16,flow_matching,0.3,2.0,58,180
101,replace,4.0,=,2,CCC(2(n]=/C(o=]s-@(,CCC(=(n]=/C(o=]s-@(,19,replace 2 at position 4 with =,flow_matching,0.3,2.0,58,180
102,replace,14.0,=,],CCC(=(n]=/C(o=]s-@(,CCC(=(n]=/C(o==s-@(,19,replace ] at position 14 with =,flow_matching,0.3,2.0,58,180
103,replace,5.0,O,(,CCC(=(n]=/C(o==s-@(,CCC(=On]=/C(o==s-@(,19,replace ( at position 5 with O,flow_matching,0.3,2.0,58,180
104,add,4.0,@,,CCC(=On]=/C(o==s-@(,CCC(@=On]=/C(o==s-@(,20,add @ at position 4,flow_matching,0.3,2.0,58,180
105,remove,6.0,O,,CCC(@=On]=/C(o==s-@(,CCC(@=n]=/C(o==s-@(,19,remove O from position 6,flow_matching,0.3,2.0,58,180
106,remove,3.0,(,,CCC(@=n]=/C(o==s-@(,CCC@=n]=/C(o==s-@(,18,remove ( from position 3,flow_matching,0.3,2.0,58,180
107,remove,14.0,s,,CCC@=n]=/C(o==s-@(,CCC@=n]=/C(o==-@(,17,remove s from position 14,flow_matching,0.3,2.0,58,180
108,replace,3.0,(,@,CCC@=n]=/C(o==-@(,CCC(=n]=/C(o==-@(,17,replace @ at position 3 with (,flow_matching,0.3,2.0,58,180
109,replace,16.0,#,(,CCC(=n]=/C(o==-@(,CCC(=n]=/C(o==-@#,17,replace ( at position 16 with #,flow_matching,0.3,2.0,58,180
110,remove,4.0,=,,CCC(=n]=/C(o==-@#,CCC(n]=/C(o==-@#,16,remove = from position 4,flow_matching,0.3,2.0,58,180
111,replace,4.0,=,n,CCC(n]=/C(o==-@#,CCC(=]=/C(o==-@#,16,replace n at position 4 with =,flow_matching,0.3,2.0,58,180
112,add,15.0,n,,CCC(=]=/C(o==-@#,CCC(=]=/C(o==-@n#,17,add n at position 15,flow_matching,0.3,2.0,58,180
113,add,0.0,B,,CCC(=]=/C(o==-@n#,BCCC(=]=/C(o==-@n#,18,add B at position 0,flow_matching,0.3,2.0,58,180
114,remove,4.0,(,,BCCC(=]=/C(o==-@n#,BCCC=]=/C(o==-@n#,17,remove ( from position 4,flow_matching,0.3,2.0,58,180
115,add,11.0,\,,BCCC=]=/C(o==-@n#,BCCC=]=/C(o\==-@n#,18,add \ at position 11,flow_matching,0.3,2.0,58,180
116,replace,0.0,C,B,BCCC=]=/C(o\==-@n#,CCCC=]=/C(o\==-@n#,18,replace B at position 0 with C,flow_matching,0.3,2.0,58,180
117,replace,3.0,(,C,CCCC=]=/C(o\==-@n#,CCC(=]=/C(o\==-@n#,18,replace C at position 3 with (,flow_matching,0.3,2.0,58,180
118,replace,15.0,/,@,CCC(=]=/C(o\==-@n#,CCC(=]=/C(o\==-/n#,18,replace @ at position 15 with /,flow_matching,0.3,2.0,58,180
119,remove,15.0,/,,CCC(=]=/C(o\==-/n#,CCC(=]=/C(o\==-n#,17,remove / from position 15,flow_matching,0.3,2.0,58,180
120,replace,5.0,O,],CCC(=]=/C(o\==-n#,CCC(=O=/C(o\==-n#,17,replace ] at position 5 with O,flow_matching,0.3,2.0,58,180
121,replace,6.0,),=,CCC(=O=/C(o\==-n#,CCC(=O)/C(o\==-n#,17,replace = at position 6 with ),flow_matching,0.3,2.0,58,180
122,replace,7.0,N,/,CCC(=O)/C(o\==-n#,CCC(=O)NC(o\==-n#,17,replace / at position 7 with N,flow_matching,0.3,2.0,58,180
123,replace,8.0,1,C,CCC(=O)NC(o\==-n#,CCC(=O)N1(o\==-n#,17,replace C at position 8 with 1,flow_matching,0.3,2.0,58,180
124,remove,16.0,#,,CCC(=O)N1(o\==-n#,CCC(=O)N1(o\==-n,16,remove # from position 16,flow_matching,0.3,2.0,58,180
125,add,4.0,n,,CCC(=O)N1(o\==-n,CCC(n=O)N1(o\==-n,17,add n at position 4,flow_matching,0.3,2.0,58,180
126,add,15.0,[,,CCC(n=O)N1(o\==-n,CCC(n=O)N1(o\==[-n,18,add [ at position 15,flow_matching,0.3,2.0,58,180
127,replace,4.0,=,n,CCC(n=O)N1(o\==[-n,CCC(==O)N1(o\==[-n,18,replace n at position 4 with =,flow_matching,0.3,2.0,58,180
128,replace,5.0,O,=,CCC(==O)N1(o\==[-n,CCC(=OO)N1(o\==[-n,18,replace = at position 5 with O,flow_matching,0.3,2.0,58,180
129,replace,6.0,),O,CCC(=OO)N1(o\==[-n,CCC(=O))N1(o\==[-n,18,replace O at position 6 with ),flow_matching,0.3,2.0,58,180
130,replace,7.0,N,),CCC(=O))N1(o\==[-n,CCC(=O)NN1(o\==[-n,18,replace ) at position 7 with N,flow_matching,0.3,2.0,58,180
131,replace,8.0,1,N,CCC(=O)NN1(o\==[-n,CCC(=O)N11(o\==[-n,18,replace N at position 8 with 1,flow_matching,0.3,2.0,58,180
132,replace,9.0,C,1,CCC(=O)N11(o\==[-n,CCC(=O)N1C(o\==[-n,18,replace 1 at position 9 with C,flow_matching,0.3,2.0,58,180
133,replace,10.0,C,(,CCC(=O)N1C(o\==[-n,CCC(=O)N1CCo\==[-n,18,replace ( at position 10 with C,flow_matching,0.3,2.0,58,180
134,replace,11.0,C,o,CCC(=O)N1CCo\==[-n,CCC(=O)N1CCC\==[-n,18,replace o at position 11 with C,flow_matching,0.3,2.0,58,180
135,replace,12.0,[,\,CCC(=O)N1CCC\==[-n,CCC(=O)N1CCC[==[-n,18,replace \ at position 12 with [,flow_matching,0.3,2.0,58,180
136,replace,13.0,C,=,CCC(=O)N1CCC[==[-n,CCC(=O)N1CCC[C=[-n,18,replace = at position 13 with C,flow_matching,0.3,2.0,58,180
137,replace,14.0,@,=,CCC(=O)N1CCC[C=[-n,CCC(=O)N1CCC[C@[-n,18,replace = at position 14 with @,flow_matching,0.3,2.0,58,180
138,replace,15.0,@,[,CCC(=O)N1CCC[C@[-n,CCC(=O)N1CCC[C@@-n,18,replace [ at position 15 with @,flow_matching,0.3,2.0,58,180
139,replace,16.0,H,-,CCC(=O)N1CCC[C@@-n,CCC(=O)N1CCC[C@@Hn,18,replace - at position 16 with H,flow_matching,0.3,2.0,58,180
140,replace,17.0,],n,CCC(=O)N1CCC[C@@Hn,CCC(=O)N1CCC[C@@H],18,replace n at position 17 with ],flow_matching,0.3,2.0,58,180
141,add,18.0,1,,CCC(=O)N1CCC[C@@H],CCC(=O)N1CCC[C@@H]1,19,add 1 at position 18,flow_matching,0.3,2.0,58,180
142,add,19.0,c,,CCC(=O)N1CCC[C@@H]1,CCC(=O)N1CCC[C@@H]1c,20,add c at position 19,flow_matching,0.3,2.0,58,180
143,add,20.0,1,,CCC(=O)N1CCC[C@@H]1c,CCC(=O)N1CCC[C@@H]1c1,21,add 1 at position 20,flow_matching,0.3,2.0,58,180
144,add,21.0,c,,CCC(=O)N1CCC[C@@H]1c1,CCC(=O)N1CCC[C@@H]1c1c,22,add c at position 21,flow_matching,0.3,2.0,58,180
145,add,22.0,c,,CCC(=O)N1CCC[C@@H]1c1c,CCC(=O)N1CCC[C@@H]1c1cc,23,add c at position 22,flow_matching,0.3,2.0,58,180
146,add,23.0,(,,CCC(=O)N1CCC[C@@H]1c1cc,CCC(=O)N1CCC[C@@H]1c1cc(,24,add ( at position 23,flow_matching,0.3,2.0,58,180
147,add,24.0,C,,CCC(=O)N1CCC[C@@H]1c1cc(,CCC(=O)N1CCC[C@@H]1c1cc(C,25,add C at position 24,flow_matching,0.3,2.0,58,180
148,add,25.0,(,,CCC(=O)N1CCC[C@@H]1c1cc(C,CCC(=O)N1CCC[C@@H]1c1cc(C(,26,add ( at position 25,flow_matching,0.3,2.0,58,180
149,add,26.0,F,,CCC(=O)N1CCC[C@@H]1c1cc(C(,CCC(=O)N1CCC[C@@H]1c1cc(C(F,27,add F at position 26,flow_matching,0.3,2.0,58,180
150,add,27.0,),,CCC(=O)N1CCC[C@@H]1c1cc(C(F,CCC(=O)N1CCC[C@@H]1c1cc(C(F),28,add ) at position 27,flow_matching,0.3,2.0,58,180
151,add,28.0,(,,CCC(=O)N1CCC[C@@H]1c1cc(C(F),CCC(=O)N1CCC[C@@H]1c1cc(C(F)(,29,add ( at position 28,flow_matching,0.3,2.0,58,180
152,add,29.0,F,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F,30,add F at position 29,flow_matching,0.3,2.0,58,180
153,add,30.0,),,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F),31,add ) at position 30,flow_matching,0.3,2.0,58,180
154,add,31.0,F,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F),CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F,32,add F at position 31,flow_matching,0.3,2.0,58,180
155,add,32.0,),,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F),33,add ) at position 32,flow_matching,0.3,2.0,58,180
156,add,33.0,c,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F),CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c,34,add c at position 33,flow_matching,0.3,2.0,58,180
157,add,34.0,2,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2,35,add 2 at position 34,flow_matching,0.3,2.0,58,180
158,add,35.0,c,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c,36,add c at position 35,flow_matching,0.3,2.0,58,180
159,add,36.0,(,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(,37,add ( at position 36,flow_matching,0.3,2.0,58,180
160,add,37.0,=,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=,38,add = at position 37,flow_matching,0.3,2.0,58,180
161,add,38.0,O,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O,39,add O at position 38,flow_matching,0.3,2.0,58,180
162,add,39.0,),,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O),40,add ) at position 39,flow_matching,0.3,2.0,58,180
163,add,40.0,n,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O),CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n,41,add n at position 40,flow_matching,0.3,2.0,58,180
164,add,41.0,(,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(,42,add ( at position 41,flow_matching,0.3,2.0,58,180
165,add,42.0,C,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C,43,add C at position 42,flow_matching,0.3,2.0,58,180
166,add,43.0,),,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C),44,add ) at position 43,flow_matching,0.3,2.0,58,180
167,add,44.0,c,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C),CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c,45,add c at position 44,flow_matching,0.3,2.0,58,180
168,add,45.0,(,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(,46,add ( at position 45,flow_matching,0.3,2.0,58,180
169,add,46.0,=,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=,47,add = at position 46,flow_matching,0.3,2.0,58,180
170,add,47.0,O,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O,48,add O at position 47,flow_matching,0.3,2.0,58,180
171,add,48.0,),,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O),49,add ) at position 48,flow_matching,0.3,2.0,58,180
172,add,49.0,n,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O),CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n,50,add n at position 49,flow_matching,0.3,2.0,58,180
173,add,50.0,(,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(,51,add ( at position 50,flow_matching,0.3,2.0,58,180
174,add,51.0,C,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C,52,add C at position 51,flow_matching,0.3,2.0,58,180
175,add,52.0,),,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C),53,add ) at position 52,flow_matching,0.3,2.0,58,180
176,add,53.0,c,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C),CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C)c,54,add c at position 53,flow_matching,0.3,2.0,58,180
177,add,54.0,2,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C)c,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C)c2,55,add 2 at position 54,flow_matching,0.3,2.0,58,180
178,add,55.0,n,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C)c2,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C)c2n,56,add n at position 55,flow_matching,0.3,2.0,58,180
179,add,56.0,1,,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C)c2n,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C)c2n1,57,add 1 at position 56,flow_matching,0.3,2.0,58,180
180,add,57.0,"
",,CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C)c2n1,"CCC(=O)N1CCC[C@@H]1c1cc(C(F)(F)F)c2c(=O)n(C)c(=O)n(C)c2n1
",58,"add 
 at position 57",flow_matching,0.3,2.0,58,180
