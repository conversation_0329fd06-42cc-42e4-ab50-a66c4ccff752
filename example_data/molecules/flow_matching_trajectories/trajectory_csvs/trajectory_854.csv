step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,46,182
1,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,46,182
2,add,1.0,7,,N,N7,2,add 7 at position 1,flow_matching,0.3,2.0,46,182
3,remove,0.0,N,,N7,7,1,remove N from position 0,flow_matching,0.3,2.0,46,182
4,add,1.0,H,,7,7H,2,add H at position 1,flow_matching,0.3,2.0,46,182
5,replace,0.0,C,7,7H,CH,2,replace 7 at position 0 with C,flow_matching,0.3,2.0,46,182
6,remove,0.0,<PERSON>,,CH,H,1,remove C from position 0,flow_matching,0.3,2.0,46,182
7,add,0.0,+,,H,+H,2,add + at position 0,flow_matching,0.3,2.0,46,182
8,replace,0.0,C,+,+<PERSON>,<PERSON>,2,replace + at position 0 with C,flow_matching,0.3,2.0,46,182
9,replace,1.0,O,H,CH,CO,2,replace H at position 1 with O,flow_matching,0.3,2.0,46,182
10,add,0.0,N,,CO,NCO,3,add N at position 0,flow_matching,0.3,2.0,46,182
11,replace,0.0,C,N,NCO,CCO,3,replace N at position 0 with C,flow_matching,0.3,2.0,46,182
12,replace,1.0,O,C,CCO,COO,3,replace C at position 1 with O,flow_matching,0.3,2.0,46,182
13,replace,1.0,6,O,COO,C6O,3,replace O at position 1 with 6,flow_matching,0.3,2.0,46,182
14,remove,2.0,O,,C6O,C6,2,remove O from position 2,flow_matching,0.3,2.0,46,182
15,remove,0.0,C,,C6,6,1,remove C from position 0,flow_matching,0.3,2.0,46,182
16,replace,0.0,r,6,6,r,1,replace 6 at position 0 with r,flow_matching,0.3,2.0,46,182
17,add,1.0,3,,r,r3,2,add 3 at position 1,flow_matching,0.3,2.0,46,182
18,remove,0.0,r,,r3,3,1,remove r from position 0,flow_matching,0.3,2.0,46,182
19,add,0.0,6,,3,63,2,add 6 at position 0,flow_matching,0.3,2.0,46,182
20,add,0.0,5,,63,563,3,add 5 at position 0,flow_matching,0.3,2.0,46,182
21,replace,0.0,C,5,563,C63,3,replace 5 at position 0 with C,flow_matching,0.3,2.0,46,182
22,replace,1.0,O,6,C63,CO3,3,replace 6 at position 1 with O,flow_matching,0.3,2.0,46,182
23,add,0.0,I,,CO3,ICO3,4,add I at position 0,flow_matching,0.3,2.0,46,182
24,replace,0.0,C,I,ICO3,CCO3,4,replace I at position 0 with C,flow_matching,0.3,2.0,46,182
25,remove,2.0,O,,CCO3,CC3,3,remove O from position 2,flow_matching,0.3,2.0,46,182
26,replace,1.0,O,C,CC3,CO3,3,replace C at position 1 with O,flow_matching,0.3,2.0,46,182
27,replace,1.0,\,O,CO3,C\3,3,replace O at position 1 with \,flow_matching,0.3,2.0,46,182
28,remove,1.0,\,,C\3,C3,2,remove \ from position 1,flow_matching,0.3,2.0,46,182
29,replace,1.0,O,3,C3,CO,2,replace 3 at position 1 with O,flow_matching,0.3,2.0,46,182
30,add,2.0,),,CO,CO),3,add ) at position 2,flow_matching,0.3,2.0,46,182
31,remove,0.0,C,,CO),O),2,remove C from position 0,flow_matching,0.3,2.0,46,182
32,remove,0.0,O,,O),),1,remove O from position 0,flow_matching,0.3,2.0,46,182
33,add,1.0,2,,),)2,2,add 2 at position 1,flow_matching,0.3,2.0,46,182
34,add,0.0,S,,)2,S)2,3,add S at position 0,flow_matching,0.3,2.0,46,182
35,replace,0.0,C,S,S)2,C)2,3,replace S at position 0 with C,flow_matching,0.3,2.0,46,182
36,remove,0.0,C,,C)2,)2,2,remove C from position 0,flow_matching,0.3,2.0,46,182
37,remove,1.0,2,,)2,),1,remove 2 from position 1,flow_matching,0.3,2.0,46,182
38,remove,0.0,),,),,0,remove ) from position 0,flow_matching,0.3,2.0,46,182
39,add,0.0,I,,,I,1,add I at position 0,flow_matching,0.3,2.0,46,182
40,remove,0.0,I,,I,,0,remove I from position 0,flow_matching,0.3,2.0,46,182
41,add,0.0,l,,,l,1,add l at position 0,flow_matching,0.3,2.0,46,182
42,add,1.0,),,l,l),2,add ) at position 1,flow_matching,0.3,2.0,46,182
43,replace,0.0,C,l,l),C),2,replace l at position 0 with C,flow_matching,0.3,2.0,46,182
44,replace,1.0,@,),C),C@,2,replace ) at position 1 with @,flow_matching,0.3,2.0,46,182
45,add,0.0,C,,C@,CC@,3,add C at position 0,flow_matching,0.3,2.0,46,182
46,remove,1.0,C,,CC@,C@,2,remove C from position 1,flow_matching,0.3,2.0,46,182
47,replace,0.0,[,C,C@,[@,2,replace C at position 0 with [,flow_matching,0.3,2.0,46,182
48,add,1.0,\,,[@,[\@,3,add \ at position 1,flow_matching,0.3,2.0,46,182
49,remove,1.0,\,,[\@,[@,2,remove \ from position 1,flow_matching,0.3,2.0,46,182
50,remove,1.0,@,,[@,[,1,remove @ from position 1,flow_matching,0.3,2.0,46,182
51,replace,0.0,C,[,[,C,1,replace [ at position 0 with C,flow_matching,0.3,2.0,46,182
52,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,46,182
53,replace,0.0,[,C,CO,[O,2,replace C at position 0 with [,flow_matching,0.3,2.0,46,182
54,add,2.0,],,[O,[O],3,add ] at position 2,flow_matching,0.3,2.0,46,182
55,replace,2.0,/,],[O],[O/,3,replace ] at position 2 with /,flow_matching,0.3,2.0,46,182
56,remove,1.0,O,,[O/,[/,2,remove O from position 1,flow_matching,0.3,2.0,46,182
57,remove,1.0,/,,[/,[,1,remove / from position 1,flow_matching,0.3,2.0,46,182
58,replace,0.0,C,[,[,C,1,replace [ at position 0 with C,flow_matching,0.3,2.0,46,182
59,add,1.0,+,,C,C+,2,add + at position 1,flow_matching,0.3,2.0,46,182
60,replace,1.0,O,+,C+,CO,2,replace + at position 1 with O,flow_matching,0.3,2.0,46,182
61,add,2.0,C,,CO,COC,3,add C at position 2,flow_matching,0.3,2.0,46,182
62,replace,1.0,s,O,COC,CsC,3,replace O at position 1 with s,flow_matching,0.3,2.0,46,182
63,remove,0.0,C,,CsC,sC,2,remove C from position 0,flow_matching,0.3,2.0,46,182
64,replace,0.0,C,s,sC,CC,2,replace s at position 0 with C,flow_matching,0.3,2.0,46,182
65,replace,1.0,O,C,CC,CO,2,replace C at position 1 with O,flow_matching,0.3,2.0,46,182
66,add,1.0,(,,CO,C(O,3,add ( at position 1,flow_matching,0.3,2.0,46,182
67,add,1.0,B,,C(O,CB(O,4,add B at position 1,flow_matching,0.3,2.0,46,182
68,replace,1.0,O,B,CB(O,CO(O,4,replace B at position 1 with O,flow_matching,0.3,2.0,46,182
69,add,4.0,6,,CO(O,CO(O6,5,add 6 at position 4,flow_matching,0.3,2.0,46,182
70,replace,2.0,C,(,CO(O6,COCO6,5,replace ( at position 2 with C,flow_matching,0.3,2.0,46,182
71,replace,0.0,[,C,COCO6,[OCO6,5,replace C at position 0 with [,flow_matching,0.3,2.0,46,182
72,remove,3.0,O,,[OCO6,[OC6,4,remove O from position 3,flow_matching,0.3,2.0,46,182
73,replace,0.0,C,[,[OC6,COC6,4,replace [ at position 0 with C,flow_matching,0.3,2.0,46,182
74,replace,3.0,C,6,COC6,COCC,4,replace 6 at position 3 with C,flow_matching,0.3,2.0,46,182
75,replace,0.0,),C,COCC,)OCC,4,replace C at position 0 with ),flow_matching,0.3,2.0,46,182
76,remove,2.0,C,,)OCC,)OC,3,remove C from position 2,flow_matching,0.3,2.0,46,182
77,replace,0.0,C,),)OC,COC,3,replace ) at position 0 with C,flow_matching,0.3,2.0,46,182
78,replace,2.0,[,C,COC,CO[,3,replace C at position 2 with [,flow_matching,0.3,2.0,46,182
79,remove,1.0,O,,CO[,C[,2,remove O from position 1,flow_matching,0.3,2.0,46,182
80,replace,1.0,O,[,C[,CO,2,replace [ at position 1 with O,flow_matching,0.3,2.0,46,182
81,add,2.0,C,,CO,COC,3,add C at position 2,flow_matching,0.3,2.0,46,182
82,add,2.0,],,COC,CO]C,4,add ] at position 2,flow_matching,0.3,2.0,46,182
83,replace,2.0,C,],CO]C,COCC,4,replace ] at position 2 with C,flow_matching,0.3,2.0,46,182
84,remove,0.0,C,,COCC,OCC,3,remove C from position 0,flow_matching,0.3,2.0,46,182
85,replace,0.0,],O,OCC,]CC,3,replace O at position 0 with ],flow_matching,0.3,2.0,46,182
86,replace,0.0,C,],]CC,CCC,3,replace ] at position 0 with C,flow_matching,0.3,2.0,46,182
87,remove,0.0,C,,CCC,CC,2,remove C from position 0,flow_matching,0.3,2.0,46,182
88,replace,1.0,O,C,CC,CO,2,replace C at position 1 with O,flow_matching,0.3,2.0,46,182
89,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,46,182
90,replace,0.0,C,O,O,C,1,replace O at position 0 with C,flow_matching,0.3,2.0,46,182
91,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,46,182
92,remove,1.0,O,,CO,C,1,remove O from position 1,flow_matching,0.3,2.0,46,182
93,add,0.0,C,,C,CC,2,add C at position 0,flow_matching,0.3,2.0,46,182
94,replace,1.0,O,C,CC,CO,2,replace C at position 1 with O,flow_matching,0.3,2.0,46,182
95,add,2.0,C,,CO,COC,3,add C at position 2,flow_matching,0.3,2.0,46,182
96,remove,1.0,O,,COC,CC,2,remove O from position 1,flow_matching,0.3,2.0,46,182
97,replace,1.0,O,C,CC,CO,2,replace C at position 1 with O,flow_matching,0.3,2.0,46,182
98,replace,1.0,H,O,CO,CH,2,replace O at position 1 with H,flow_matching,0.3,2.0,46,182
99,add,1.0,B,,CH,CBH,3,add B at position 1,flow_matching,0.3,2.0,46,182
100,add,2.0,2,,CBH,CB2H,4,add 2 at position 2,flow_matching,0.3,2.0,46,182
101,add,3.0,#,,CB2H,CB2#H,5,add # at position 3,flow_matching,0.3,2.0,46,182
102,replace,3.0,3,#,CB2#H,CB23H,5,replace # at position 3 with 3,flow_matching,0.3,2.0,46,182
103,remove,2.0,2,,CB23H,CB3H,4,remove 2 from position 2,flow_matching,0.3,2.0,46,182
104,remove,0.0,C,,CB3H,B3H,3,remove C from position 0,flow_matching,0.3,2.0,46,182
105,replace,2.0,=,H,B3H,B3=,3,replace H at position 2 with =,flow_matching,0.3,2.0,46,182
106,remove,2.0,=,,B3=,B3,2,remove = from position 2,flow_matching,0.3,2.0,46,182
107,remove,1.0,3,,B3,B,1,remove 3 from position 1,flow_matching,0.3,2.0,46,182
108,replace,0.0,C,B,B,C,1,replace B at position 0 with C,flow_matching,0.3,2.0,46,182
109,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,46,182
110,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,46,182
111,add,0.0,o,,C,oC,2,add o at position 0,flow_matching,0.3,2.0,46,182
112,add,0.0,7,,oC,7oC,3,add 7 at position 0,flow_matching,0.3,2.0,46,182
113,add,2.0,I,,7oC,7oIC,4,add I at position 2,flow_matching,0.3,2.0,46,182
114,add,0.0,1,,7oIC,17oIC,5,add 1 at position 0,flow_matching,0.3,2.0,46,182
115,replace,0.0,C,1,17oIC,C7oIC,5,replace 1 at position 0 with C,flow_matching,0.3,2.0,46,182
116,replace,1.0,O,7,C7oIC,COoIC,5,replace 7 at position 1 with O,flow_matching,0.3,2.0,46,182
117,add,3.0,o,,COoIC,COooIC,6,add o at position 3,flow_matching,0.3,2.0,46,182
118,replace,2.0,C,o,COooIC,COCoIC,6,replace o at position 2 with C,flow_matching,0.3,2.0,46,182
119,remove,5.0,C,,COCoIC,COCoI,5,remove C from position 5,flow_matching,0.3,2.0,46,182
120,replace,2.0,N,C,COCoI,CONoI,5,replace C at position 2 with N,flow_matching,0.3,2.0,46,182
121,replace,4.0,c,I,CONoI,CONoc,5,replace I at position 4 with c,flow_matching,0.3,2.0,46,182
122,add,1.0,C,,CONoc,CCONoc,6,add C at position 1,flow_matching,0.3,2.0,46,182
123,replace,1.0,O,C,CCONoc,COONoc,6,replace C at position 1 with O,flow_matching,0.3,2.0,46,182
124,add,2.0,C,,COONoc,COCONoc,7,add C at position 2,flow_matching,0.3,2.0,46,182
125,replace,3.0,),O,COCONoc,COC)Noc,7,replace O at position 3 with ),flow_matching,0.3,2.0,46,182
126,add,7.0,=,,COC)Noc,COC)Noc=,8,add = at position 7,flow_matching,0.3,2.0,46,182
127,replace,0.0,1,C,COC)Noc=,1OC)Noc=,8,replace C at position 0 with 1,flow_matching,0.3,2.0,46,182
128,replace,5.0,/,o,1OC)Noc=,1OC)N/c=,8,replace o at position 5 with /,flow_matching,0.3,2.0,46,182
129,replace,0.0,C,1,1OC)N/c=,COC)N/c=,8,replace 1 at position 0 with C,flow_matching,0.3,2.0,46,182
130,add,1.0,B,,COC)N/c=,CBOC)N/c=,9,add B at position 1,flow_matching,0.3,2.0,46,182
131,remove,0.0,C,,CBOC)N/c=,BOC)N/c=,8,remove C from position 0,flow_matching,0.3,2.0,46,182
132,remove,4.0,N,,BOC)N/c=,BOC)/c=,7,remove N from position 4,flow_matching,0.3,2.0,46,182
133,add,1.0,[,,BOC)/c=,B[OC)/c=,8,add [ at position 1,flow_matching,0.3,2.0,46,182
134,replace,0.0,C,B,B[OC)/c=,C[OC)/c=,8,replace B at position 0 with C,flow_matching,0.3,2.0,46,182
135,replace,1.0,O,[,C[OC)/c=,COOC)/c=,8,replace [ at position 1 with O,flow_matching,0.3,2.0,46,182
136,add,3.0,],,COOC)/c=,COO]C)/c=,9,add ] at position 3,flow_matching,0.3,2.0,46,182
137,replace,2.0,C,O,COO]C)/c=,COC]C)/c=,9,replace O at position 2 with C,flow_matching,0.3,2.0,46,182
138,remove,1.0,O,,COC]C)/c=,CC]C)/c=,8,remove O from position 1,flow_matching,0.3,2.0,46,182
139,replace,1.0,O,C,CC]C)/c=,CO]C)/c=,8,replace C at position 1 with O,flow_matching,0.3,2.0,46,182
140,replace,2.0,C,],CO]C)/c=,COCC)/c=,8,replace ] at position 2 with C,flow_matching,0.3,2.0,46,182
141,replace,4.0,n,),COCC)/c=,COCCn/c=,8,replace ) at position 4 with n,flow_matching,0.3,2.0,46,182
142,replace,5.0,1,/,COCCn/c=,COCCn1c=,8,replace / at position 5 with 1,flow_matching,0.3,2.0,46,182
143,replace,6.0,n,c,COCCn1c=,COCCn1n=,8,replace c at position 6 with n,flow_matching,0.3,2.0,46,182
144,replace,7.0,c,=,COCCn1n=,COCCn1nc,8,replace = at position 7 with c,flow_matching,0.3,2.0,46,182
145,add,8.0,(,,COCCn1nc,COCCn1nc(,9,add ( at position 8,flow_matching,0.3,2.0,46,182
146,add,9.0,C,,COCCn1nc(,COCCn1nc(C,10,add C at position 9,flow_matching,0.3,2.0,46,182
147,add,10.0,),,COCCn1nc(C,COCCn1nc(C),11,add ) at position 10,flow_matching,0.3,2.0,46,182
148,add,11.0,c,,COCCn1nc(C),COCCn1nc(C)c,12,add c at position 11,flow_matching,0.3,2.0,46,182
149,add,12.0,(,,COCCn1nc(C)c,COCCn1nc(C)c(,13,add ( at position 12,flow_matching,0.3,2.0,46,182
150,add,13.0,N,,COCCn1nc(C)c(,COCCn1nc(C)c(N,14,add N at position 13,flow_matching,0.3,2.0,46,182
151,add,14.0,C,,COCCn1nc(C)c(N,COCCn1nc(C)c(NC,15,add C at position 14,flow_matching,0.3,2.0,46,182
152,add,15.0,(,,COCCn1nc(C)c(NC,COCCn1nc(C)c(NC(,16,add ( at position 15,flow_matching,0.3,2.0,46,182
153,add,16.0,=,,COCCn1nc(C)c(NC(,COCCn1nc(C)c(NC(=,17,add = at position 16,flow_matching,0.3,2.0,46,182
154,add,17.0,O,,COCCn1nc(C)c(NC(=,COCCn1nc(C)c(NC(=O,18,add O at position 17,flow_matching,0.3,2.0,46,182
155,add,18.0,),,COCCn1nc(C)c(NC(=O,COCCn1nc(C)c(NC(=O),19,add ) at position 18,flow_matching,0.3,2.0,46,182
156,add,19.0,N,,COCCn1nc(C)c(NC(=O),COCCn1nc(C)c(NC(=O)N,20,add N at position 19,flow_matching,0.3,2.0,46,182
157,add,20.0,2,,COCCn1nc(C)c(NC(=O)N,COCCn1nc(C)c(NC(=O)N2,21,add 2 at position 20,flow_matching,0.3,2.0,46,182
158,add,21.0,C,,COCCn1nc(C)c(NC(=O)N2,COCCn1nc(C)c(NC(=O)N2C,22,add C at position 21,flow_matching,0.3,2.0,46,182
159,add,22.0,C,,COCCn1nc(C)c(NC(=O)N2C,COCCn1nc(C)c(NC(=O)N2CC,23,add C at position 22,flow_matching,0.3,2.0,46,182
160,add,23.0,[,,COCCn1nc(C)c(NC(=O)N2CC,COCCn1nc(C)c(NC(=O)N2CC[,24,add [ at position 23,flow_matching,0.3,2.0,46,182
161,add,24.0,C,,COCCn1nc(C)c(NC(=O)N2CC[,COCCn1nc(C)c(NC(=O)N2CC[C,25,add C at position 24,flow_matching,0.3,2.0,46,182
162,add,25.0,@,,COCCn1nc(C)c(NC(=O)N2CC[C,COCCn1nc(C)c(NC(=O)N2CC[C@,26,add @ at position 25,flow_matching,0.3,2.0,46,182
163,add,26.0,H,,COCCn1nc(C)c(NC(=O)N2CC[C@,COCCn1nc(C)c(NC(=O)N2CC[C@H,27,add H at position 26,flow_matching,0.3,2.0,46,182
164,add,27.0,],,COCCn1nc(C)c(NC(=O)N2CC[C@H,COCCn1nc(C)c(NC(=O)N2CC[C@H],28,add ] at position 27,flow_matching,0.3,2.0,46,182
165,add,28.0,(,,COCCn1nc(C)c(NC(=O)N2CC[C@H],COCCn1nc(C)c(NC(=O)N2CC[C@H](,29,add ( at position 28,flow_matching,0.3,2.0,46,182
166,add,29.0,C,,COCCn1nc(C)c(NC(=O)N2CC[C@H](,COCCn1nc(C)c(NC(=O)N2CC[C@H](C,30,add C at position 29,flow_matching,0.3,2.0,46,182
167,add,30.0,c,,COCCn1nc(C)c(NC(=O)N2CC[C@H](C,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc,31,add c at position 30,flow_matching,0.3,2.0,46,182
168,add,31.0,3,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3,32,add 3 at position 31,flow_matching,0.3,2.0,46,182
169,add,32.0,c,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3c,33,add c at position 32,flow_matching,0.3,2.0,46,182
170,add,33.0,c,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3c,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3cc,34,add c at position 33,flow_matching,0.3,2.0,46,182
171,add,34.0,c,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3cc,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccc,35,add c at position 34,flow_matching,0.3,2.0,46,182
172,add,35.0,c,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccc,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3cccc,36,add c at position 35,flow_matching,0.3,2.0,46,182
173,add,36.0,c,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3cccc,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc,37,add c at position 36,flow_matching,0.3,2.0,46,182
174,add,37.0,3,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3,38,add 3 at position 37,flow_matching,0.3,2.0,46,182
175,add,38.0,),,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3),39,add ) at position 38,flow_matching,0.3,2.0,46,182
176,add,39.0,C,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3),COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C,40,add C at position 39,flow_matching,0.3,2.0,46,182
177,add,40.0,2,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2,41,add 2 at position 40,flow_matching,0.3,2.0,46,182
178,add,41.0,),,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2),42,add ) at position 41,flow_matching,0.3,2.0,46,182
179,add,42.0,c,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2),COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2)c,43,add c at position 42,flow_matching,0.3,2.0,46,182
180,add,43.0,1,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2)c,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2)c1,44,add 1 at position 43,flow_matching,0.3,2.0,46,182
181,add,44.0,C,,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2)c1,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2)c1C,45,add C at position 44,flow_matching,0.3,2.0,46,182
182,add,45.0,"
",,COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2)c1C,"COCCn1nc(C)c(NC(=O)N2CC[C@H](Cc3ccccc3)C2)c1C
",46,"add 
 at position 45",flow_matching,0.3,2.0,46,182
