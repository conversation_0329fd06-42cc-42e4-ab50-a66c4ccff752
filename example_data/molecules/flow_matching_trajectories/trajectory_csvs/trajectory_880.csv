step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,32,138
1,add,0.0,(,,,(,1,add ( at position 0,flow_matching,0.3,2.0,32,138
2,remove,0.0,(,,(,,0,remove ( from position 0,flow_matching,0.3,2.0,32,138
3,add,0.0,c,,,c,1,add c at position 0,flow_matching,0.3,2.0,32,138
4,replace,0.0,C,c,c,C,1,replace c at position 0 with C,flow_matching,0.3,2.0,32,138
5,add,0.0,1,,C,1C,2,add 1 at position 0,flow_matching,0.3,2.0,32,138
6,replace,0.0,C,1,1C,<PERSON>,2,replace 1 at position 0 with C,flow_matching,0.3,2.0,32,138
7,remove,1.0,<PERSON>,,CC,C,1,remove C from position 1,flow_matching,0.3,2.0,32,138
8,add,1.0,c,,C,Cc,2,add c at position 1,flow_matching,0.3,2.0,32,138
9,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,32,138
10,add,3.0,n,,Cc1,Cc1n,4,add n at position 3,flow_matching,0.3,2.0,32,138
11,replace,1.0,4,c,Cc1n,C41n,4,replace c at position 1 with 4,flow_matching,0.3,2.0,32,138
12,replace,1.0,c,4,C41n,Cc1n,4,replace 4 at position 1 with c,flow_matching,0.3,2.0,32,138
13,add,4.0,c,,Cc1n,Cc1nc,5,add c at position 4,flow_matching,0.3,2.0,32,138
14,remove,3.0,n,,Cc1nc,Cc1c,4,remove n from position 3,flow_matching,0.3,2.0,32,138
15,replace,3.0,n,c,Cc1c,Cc1n,4,replace c at position 3 with n,flow_matching,0.3,2.0,32,138
16,replace,2.0,H,1,Cc1n,CcHn,4,replace 1 at position 2 with H,flow_matching,0.3,2.0,32,138
17,replace,2.0,3,H,CcHn,Cc3n,4,replace H at position 2 with 3,flow_matching,0.3,2.0,32,138
18,add,0.0,[,,Cc3n,[Cc3n,5,add [ at position 0,flow_matching,0.3,2.0,32,138
19,add,1.0,O,,[Cc3n,[OCc3n,6,add O at position 1,flow_matching,0.3,2.0,32,138
20,replace,0.0,C,[,[OCc3n,COCc3n,6,replace [ at position 0 with C,flow_matching,0.3,2.0,32,138
21,remove,0.0,C,,COCc3n,OCc3n,5,remove C from position 0,flow_matching,0.3,2.0,32,138
22,remove,0.0,O,,OCc3n,Cc3n,4,remove O from position 0,flow_matching,0.3,2.0,32,138
23,replace,1.0,/,c,Cc3n,C/3n,4,replace c at position 1 with /,flow_matching,0.3,2.0,32,138
24,replace,3.0,c,n,C/3n,C/3c,4,replace n at position 3 with c,flow_matching,0.3,2.0,32,138
25,remove,0.0,C,,C/3c,/3c,3,remove C from position 0,flow_matching,0.3,2.0,32,138
26,replace,0.0,7,/,/3c,73c,3,replace / at position 0 with 7,flow_matching,0.3,2.0,32,138
27,remove,1.0,3,,73c,7c,2,remove 3 from position 1,flow_matching,0.3,2.0,32,138
28,replace,0.0,C,7,7c,Cc,2,replace 7 at position 0 with C,flow_matching,0.3,2.0,32,138
29,add,2.0,1,,Cc,Cc1,3,add 1 at position 2,flow_matching,0.3,2.0,32,138
30,remove,0.0,C,,Cc1,c1,2,remove C from position 0,flow_matching,0.3,2.0,32,138
31,replace,0.0,C,c,c1,C1,2,replace c at position 0 with C,flow_matching,0.3,2.0,32,138
32,replace,1.0,c,1,C1,Cc,2,replace 1 at position 1 with c,flow_matching,0.3,2.0,32,138
33,replace,1.0,6,c,Cc,C6,2,replace c at position 1 with 6,flow_matching,0.3,2.0,32,138
34,add,1.0,5,,C6,C56,3,add 5 at position 1,flow_matching,0.3,2.0,32,138
35,add,2.0,n,,C56,C5n6,4,add n at position 2,flow_matching,0.3,2.0,32,138
36,add,1.0,l,,C5n6,Cl5n6,5,add l at position 1,flow_matching,0.3,2.0,32,138
37,replace,1.0,I,l,Cl5n6,CI5n6,5,replace l at position 1 with I,flow_matching,0.3,2.0,32,138
38,remove,0.0,C,,CI5n6,I5n6,4,remove C from position 0,flow_matching,0.3,2.0,32,138
39,add,4.0,n,,I5n6,I5n6n,5,add n at position 4,flow_matching,0.3,2.0,32,138
40,add,5.0,@,,I5n6n,I5n6n@,6,add @ at position 5,flow_matching,0.3,2.0,32,138
41,replace,0.0,C,I,I5n6n@,C5n6n@,6,replace I at position 0 with C,flow_matching,0.3,2.0,32,138
42,remove,0.0,C,,C5n6n@,5n6n@,5,remove C from position 0,flow_matching,0.3,2.0,32,138
43,remove,4.0,@,,5n6n@,5n6n,4,remove @ from position 4,flow_matching,0.3,2.0,32,138
44,add,1.0,r,,5n6n,5rn6n,5,add r at position 1,flow_matching,0.3,2.0,32,138
45,remove,3.0,6,,5rn6n,5rnn,4,remove 6 from position 3,flow_matching,0.3,2.0,32,138
46,remove,3.0,n,,5rnn,5rn,3,remove n from position 3,flow_matching,0.3,2.0,32,138
47,add,3.0,-,,5rn,5rn-,4,add - at position 3,flow_matching,0.3,2.0,32,138
48,replace,3.0,+,-,5rn-,5rn+,4,replace - at position 3 with +,flow_matching,0.3,2.0,32,138
49,replace,3.0,(,+,5rn+,5rn(,4,replace + at position 3 with (,flow_matching,0.3,2.0,32,138
50,add,4.0,n,,5rn(,5rn(n,5,add n at position 4,flow_matching,0.3,2.0,32,138
51,remove,0.0,5,,5rn(n,rn(n,4,remove 5 from position 0,flow_matching,0.3,2.0,32,138
52,replace,0.0,C,r,rn(n,Cn(n,4,replace r at position 0 with C,flow_matching,0.3,2.0,32,138
53,replace,1.0,c,n,Cn(n,Cc(n,4,replace n at position 1 with c,flow_matching,0.3,2.0,32,138
54,replace,2.0,1,(,Cc(n,Cc1n,4,replace ( at position 2 with 1,flow_matching,0.3,2.0,32,138
55,add,4.0,4,,Cc1n,Cc1n4,5,add 4 at position 4,flow_matching,0.3,2.0,32,138
56,replace,4.0,c,4,Cc1n4,Cc1nc,5,replace 4 at position 4 with c,flow_matching,0.3,2.0,32,138
57,replace,1.0,3,c,Cc1nc,C31nc,5,replace c at position 1 with 3,flow_matching,0.3,2.0,32,138
58,remove,2.0,1,,C31nc,C3nc,4,remove 1 from position 2,flow_matching,0.3,2.0,32,138
59,add,4.0,H,,C3nc,C3ncH,5,add H at position 4,flow_matching,0.3,2.0,32,138
60,add,2.0,O,,C3ncH,C3OncH,6,add O at position 2,flow_matching,0.3,2.0,32,138
61,add,5.0,O,,C3OncH,C3OncOH,7,add O at position 5,flow_matching,0.3,2.0,32,138
62,add,0.0,\,,C3OncOH,\C3OncOH,8,add \ at position 0,flow_matching,0.3,2.0,32,138
63,replace,0.0,C,\,\C3OncOH,CC3OncOH,8,replace \ at position 0 with C,flow_matching,0.3,2.0,32,138
64,remove,3.0,O,,CC3OncOH,CC3ncOH,7,remove O from position 3,flow_matching,0.3,2.0,32,138
65,remove,0.0,C,,CC3ncOH,C3ncOH,6,remove C from position 0,flow_matching,0.3,2.0,32,138
66,remove,5.0,H,,C3ncOH,C3ncO,5,remove H from position 5,flow_matching,0.3,2.0,32,138
67,remove,0.0,C,,C3ncO,3ncO,4,remove C from position 0,flow_matching,0.3,2.0,32,138
68,replace,3.0,3,O,3ncO,3nc3,4,replace O at position 3 with 3,flow_matching,0.3,2.0,32,138
69,replace,0.0,/,3,3nc3,/nc3,4,replace 3 at position 0 with /,flow_matching,0.3,2.0,32,138
70,replace,0.0,l,/,/nc3,lnc3,4,replace / at position 0 with l,flow_matching,0.3,2.0,32,138
71,replace,0.0,r,l,lnc3,rnc3,4,replace l at position 0 with r,flow_matching,0.3,2.0,32,138
72,add,1.0,[,,rnc3,r[nc3,5,add [ at position 1,flow_matching,0.3,2.0,32,138
73,add,4.0,3,,r[nc3,r[nc33,6,add 3 at position 4,flow_matching,0.3,2.0,32,138
74,replace,0.0,],r,r[nc33,][nc33,6,replace r at position 0 with ],flow_matching,0.3,2.0,32,138
75,replace,0.0,C,],][nc33,C[nc33,6,replace ] at position 0 with C,flow_matching,0.3,2.0,32,138
76,remove,2.0,n,,C[nc33,C[c33,5,remove n from position 2,flow_matching,0.3,2.0,32,138
77,replace,1.0,c,[,C[c33,Ccc33,5,replace [ at position 1 with c,flow_matching,0.3,2.0,32,138
78,replace,2.0,1,c,Ccc33,Cc133,5,replace c at position 2 with 1,flow_matching,0.3,2.0,32,138
79,replace,4.0,5,3,Cc133,Cc135,5,replace 3 at position 4 with 5,flow_matching,0.3,2.0,32,138
80,replace,2.0,2,1,Cc135,Cc235,5,replace 1 at position 2 with 2,flow_matching,0.3,2.0,32,138
81,replace,1.0,H,c,Cc235,CH235,5,replace c at position 1 with H,flow_matching,0.3,2.0,32,138
82,remove,4.0,5,,CH235,CH23,4,remove 5 from position 4,flow_matching,0.3,2.0,32,138
83,replace,1.0,c,H,CH23,Cc23,4,replace H at position 1 with c,flow_matching,0.3,2.0,32,138
84,replace,3.0,I,3,Cc23,Cc2I,4,replace 3 at position 3 with I,flow_matching,0.3,2.0,32,138
85,replace,2.0,1,2,Cc2I,Cc1I,4,replace 2 at position 2 with 1,flow_matching,0.3,2.0,32,138
86,add,2.0,B,,Cc1I,CcB1I,5,add B at position 2,flow_matching,0.3,2.0,32,138
87,replace,2.0,F,B,CcB1I,CcF1I,5,replace B at position 2 with F,flow_matching,0.3,2.0,32,138
88,replace,2.0,1,F,CcF1I,Cc11I,5,replace F at position 2 with 1,flow_matching,0.3,2.0,32,138
89,add,3.0,l,,Cc11I,Cc1l1I,6,add l at position 3,flow_matching,0.3,2.0,32,138
90,replace,3.0,n,l,Cc1l1I,Cc1n1I,6,replace l at position 3 with n,flow_matching,0.3,2.0,32,138
91,replace,4.0,c,1,Cc1n1I,Cc1ncI,6,replace 1 at position 4 with c,flow_matching,0.3,2.0,32,138
92,replace,2.0,6,1,Cc1ncI,Cc6ncI,6,replace 1 at position 2 with 6,flow_matching,0.3,2.0,32,138
93,replace,5.0,3,I,Cc6ncI,Cc6nc3,6,replace I at position 5 with 3,flow_matching,0.3,2.0,32,138
94,replace,2.0,1,6,Cc6nc3,Cc1nc3,6,replace 6 at position 2 with 1,flow_matching,0.3,2.0,32,138
95,replace,2.0,r,1,Cc1nc3,Ccrnc3,6,replace 1 at position 2 with r,flow_matching,0.3,2.0,32,138
96,remove,1.0,c,,Ccrnc3,Crnc3,5,remove c from position 1,flow_matching,0.3,2.0,32,138
97,remove,4.0,3,,Crnc3,Crnc,4,remove 3 from position 4,flow_matching,0.3,2.0,32,138
98,remove,1.0,r,,Crnc,Cnc,3,remove r from position 1,flow_matching,0.3,2.0,32,138
99,replace,1.0,=,n,Cnc,C=c,3,replace n at position 1 with =,flow_matching,0.3,2.0,32,138
100,replace,1.0,c,=,C=c,Ccc,3,replace = at position 1 with c,flow_matching,0.3,2.0,32,138
101,add,1.0,r,,Ccc,Crcc,4,add r at position 1,flow_matching,0.3,2.0,32,138
102,add,0.0,N,,Crcc,NCrcc,5,add N at position 0,flow_matching,0.3,2.0,32,138
103,replace,0.0,C,N,NCrcc,CCrcc,5,replace N at position 0 with C,flow_matching,0.3,2.0,32,138
104,remove,0.0,C,,CCrcc,Crcc,4,remove C from position 0,flow_matching,0.3,2.0,32,138
105,add,3.0,-,,Crcc,Crc-c,5,add - at position 3,flow_matching,0.3,2.0,32,138
106,add,1.0,c,,Crc-c,Ccrc-c,6,add c at position 1,flow_matching,0.3,2.0,32,138
107,replace,5.0,),c,Ccrc-c,Ccrc-),6,replace c at position 5 with ),flow_matching,0.3,2.0,32,138
108,replace,5.0,],),Ccrc-),Ccrc-],6,replace ) at position 5 with ],flow_matching,0.3,2.0,32,138
109,replace,2.0,1,r,Ccrc-],Cc1c-],6,replace r at position 2 with 1,flow_matching,0.3,2.0,32,138
110,replace,3.0,n,c,Cc1c-],Cc1n-],6,replace c at position 3 with n,flow_matching,0.3,2.0,32,138
111,replace,4.0,c,-,Cc1n-],Cc1nc],6,replace - at position 4 with c,flow_matching,0.3,2.0,32,138
112,replace,5.0,(,],Cc1nc],Cc1nc(,6,replace ] at position 5 with (,flow_matching,0.3,2.0,32,138
113,add,6.0,B,,Cc1nc(,Cc1nc(B,7,add B at position 6,flow_matching,0.3,2.0,32,138
114,add,7.0,r,,Cc1nc(B,Cc1nc(Br,8,add r at position 7,flow_matching,0.3,2.0,32,138
115,add,8.0,),,Cc1nc(Br,Cc1nc(Br),9,add ) at position 8,flow_matching,0.3,2.0,32,138
116,add,9.0,c,,Cc1nc(Br),Cc1nc(Br)c,10,add c at position 9,flow_matching,0.3,2.0,32,138
117,add,10.0,c,,Cc1nc(Br)c,Cc1nc(Br)cc,11,add c at position 10,flow_matching,0.3,2.0,32,138
118,add,11.0,c,,Cc1nc(Br)cc,Cc1nc(Br)ccc,12,add c at position 11,flow_matching,0.3,2.0,32,138
119,add,12.0,1,,Cc1nc(Br)ccc,Cc1nc(Br)ccc1,13,add 1 at position 12,flow_matching,0.3,2.0,32,138
120,add,13.0,N,,Cc1nc(Br)ccc1,Cc1nc(Br)ccc1N,14,add N at position 13,flow_matching,0.3,2.0,32,138
121,add,14.0,C,,Cc1nc(Br)ccc1N,Cc1nc(Br)ccc1NC,15,add C at position 14,flow_matching,0.3,2.0,32,138
122,add,15.0,(,,Cc1nc(Br)ccc1NC,Cc1nc(Br)ccc1NC(,16,add ( at position 15,flow_matching,0.3,2.0,32,138
123,add,16.0,=,,Cc1nc(Br)ccc1NC(,Cc1nc(Br)ccc1NC(=,17,add = at position 16,flow_matching,0.3,2.0,32,138
124,add,17.0,O,,Cc1nc(Br)ccc1NC(=,Cc1nc(Br)ccc1NC(=O,18,add O at position 17,flow_matching,0.3,2.0,32,138
125,add,18.0,),,Cc1nc(Br)ccc1NC(=O,Cc1nc(Br)ccc1NC(=O),19,add ) at position 18,flow_matching,0.3,2.0,32,138
126,add,19.0,N,,Cc1nc(Br)ccc1NC(=O),Cc1nc(Br)ccc1NC(=O)N,20,add N at position 19,flow_matching,0.3,2.0,32,138
127,add,20.0,C,,Cc1nc(Br)ccc1NC(=O)N,Cc1nc(Br)ccc1NC(=O)NC,21,add C at position 20,flow_matching,0.3,2.0,32,138
128,add,21.0,c,,Cc1nc(Br)ccc1NC(=O)NC,Cc1nc(Br)ccc1NC(=O)NCc,22,add c at position 21,flow_matching,0.3,2.0,32,138
129,add,22.0,1,,Cc1nc(Br)ccc1NC(=O)NCc,Cc1nc(Br)ccc1NC(=O)NCc1,23,add 1 at position 22,flow_matching,0.3,2.0,32,138
130,add,23.0,c,,Cc1nc(Br)ccc1NC(=O)NCc1,Cc1nc(Br)ccc1NC(=O)NCc1c,24,add c at position 23,flow_matching,0.3,2.0,32,138
131,add,24.0,n,,Cc1nc(Br)ccc1NC(=O)NCc1c,Cc1nc(Br)ccc1NC(=O)NCc1cn,25,add n at position 24,flow_matching,0.3,2.0,32,138
132,add,25.0,n,,Cc1nc(Br)ccc1NC(=O)NCc1cn,Cc1nc(Br)ccc1NC(=O)NCc1cnn,26,add n at position 25,flow_matching,0.3,2.0,32,138
133,add,26.0,(,,Cc1nc(Br)ccc1NC(=O)NCc1cnn,Cc1nc(Br)ccc1NC(=O)NCc1cnn(,27,add ( at position 26,flow_matching,0.3,2.0,32,138
134,add,27.0,C,,Cc1nc(Br)ccc1NC(=O)NCc1cnn(,Cc1nc(Br)ccc1NC(=O)NCc1cnn(C,28,add C at position 27,flow_matching,0.3,2.0,32,138
135,add,28.0,),,Cc1nc(Br)ccc1NC(=O)NCc1cnn(C,Cc1nc(Br)ccc1NC(=O)NCc1cnn(C),29,add ) at position 28,flow_matching,0.3,2.0,32,138
136,add,29.0,c,,Cc1nc(Br)ccc1NC(=O)NCc1cnn(C),Cc1nc(Br)ccc1NC(=O)NCc1cnn(C)c,30,add c at position 29,flow_matching,0.3,2.0,32,138
137,add,30.0,1,,Cc1nc(Br)ccc1NC(=O)NCc1cnn(C)c,Cc1nc(Br)ccc1NC(=O)NCc1cnn(C)c1,31,add 1 at position 30,flow_matching,0.3,2.0,32,138
138,add,31.0,"
",,Cc1nc(Br)ccc1NC(=O)NCc1cnn(C)c1,"Cc1nc(Br)ccc1NC(=O)NCc1cnn(C)c1
",32,"add 
 at position 31",flow_matching,0.3,2.0,32,138
