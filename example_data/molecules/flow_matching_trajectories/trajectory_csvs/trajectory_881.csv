step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,40,129
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,40,129
2,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,40,129
3,add,1.0,3,,CO,C3O,3,add 3 at position 1,flow_matching,0.3,2.0,40,129
4,add,1.0,(,,C3O,C(3O,4,add ( at position 1,flow_matching,0.3,2.0,40,129
5,replace,1.0,O,(,C(3O,CO3O,4,replace ( at position 1 with O,flow_matching,0.3,2.0,40,129
6,remove,0.0,C,,CO3O,O3O,3,remove <PERSON> from position 0,flow_matching,0.3,2.0,40,129
7,remove,0.0,O,,O3O,3O,2,remove O from position 0,flow_matching,0.3,2.0,40,129
8,remove,0.0,3,,3O,O,1,remove 3 from position 0,flow_matching,0.3,2.0,40,129
9,replace,0.0,s,O,O,s,1,replace O at position 0 with s,flow_matching,0.3,2.0,40,129
10,replace,0.0,C,s,s,C,1,replace s at position 0 with C,flow_matching,0.3,2.0,40,129
11,remove,0.0,C,,C,,0,remove C from position 0,flow_matching,0.3,2.0,40,129
12,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,40,129
13,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,40,129
14,remove,1.0,O,,CO,C,1,remove O from position 1,flow_matching,0.3,2.0,40,129
15,add,1.0,#,,C,C#,2,add # at position 1,flow_matching,0.3,2.0,40,129
16,add,1.0,+,,C#,C+#,3,add + at position 1,flow_matching,0.3,2.0,40,129
17,remove,2.0,#,,C+#,C+,2,remove # from position 2,flow_matching,0.3,2.0,40,129
18,replace,1.0,\,+,C+,C\,2,replace + at position 1 with \,flow_matching,0.3,2.0,40,129
19,add,2.0,C,,C\,C\C,3,add C at position 2,flow_matching,0.3,2.0,40,129
20,add,0.0,-,,C\C,-C\C,4,add - at position 0,flow_matching,0.3,2.0,40,129
21,replace,0.0,C,-,-C\C,CC\C,4,replace - at position 0 with C,flow_matching,0.3,2.0,40,129
22,remove,2.0,\,,CC\C,CCC,3,remove \ from position 2,flow_matching,0.3,2.0,40,129
23,remove,0.0,C,,CCC,CC,2,remove C from position 0,flow_matching,0.3,2.0,40,129
24,replace,1.0,O,C,CC,CO,2,replace C at position 1 with O,flow_matching,0.3,2.0,40,129
25,replace,0.0,=,C,CO,=O,2,replace C at position 0 with =,flow_matching,0.3,2.0,40,129
26,replace,1.0,l,O,=O,=l,2,replace O at position 1 with l,flow_matching,0.3,2.0,40,129
27,replace,0.0,C,=,=l,Cl,2,replace = at position 0 with C,flow_matching,0.3,2.0,40,129
28,replace,1.0,O,l,Cl,CO,2,replace l at position 1 with O,flow_matching,0.3,2.0,40,129
29,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,40,129
30,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,40,129
31,add,0.0,@,,,@,1,add @ at position 0,flow_matching,0.3,2.0,40,129
32,replace,0.0,4,@,@,4,1,replace @ at position 0 with 4,flow_matching,0.3,2.0,40,129
33,replace,0.0,C,4,4,C,1,replace 4 at position 0 with C,flow_matching,0.3,2.0,40,129
34,replace,0.0,H,C,C,H,1,replace C at position 0 with H,flow_matching,0.3,2.0,40,129
35,remove,0.0,H,,H,,0,remove H from position 0,flow_matching,0.3,2.0,40,129
36,add,0.0,],,,],1,add ] at position 0,flow_matching,0.3,2.0,40,129
37,add,0.0,-,,],-],2,add - at position 0,flow_matching,0.3,2.0,40,129
38,replace,0.0,C,-,-],C],2,replace - at position 0 with C,flow_matching,0.3,2.0,40,129
39,replace,1.0,O,],C],CO,2,replace ] at position 1 with O,flow_matching,0.3,2.0,40,129
40,remove,0.0,C,,CO,O,1,remove C from position 0,flow_matching,0.3,2.0,40,129
41,replace,0.0,C,O,O,C,1,replace O at position 0 with C,flow_matching,0.3,2.0,40,129
42,replace,0.0,),C,C,),1,replace C at position 0 with ),flow_matching,0.3,2.0,40,129
43,replace,0.0,C,),),C,1,replace ) at position 0 with C,flow_matching,0.3,2.0,40,129
44,replace,0.0,B,C,C,B,1,replace C at position 0 with B,flow_matching,0.3,2.0,40,129
45,replace,0.0,n,B,B,n,1,replace B at position 0 with n,flow_matching,0.3,2.0,40,129
46,replace,0.0,C,n,n,C,1,replace n at position 0 with C,flow_matching,0.3,2.0,40,129
47,replace,0.0,B,C,C,B,1,replace C at position 0 with B,flow_matching,0.3,2.0,40,129
48,add,0.0,s,,B,sB,2,add s at position 0,flow_matching,0.3,2.0,40,129
49,add,0.0,s,,sB,ssB,3,add s at position 0,flow_matching,0.3,2.0,40,129
50,replace,0.0,],s,ssB,]sB,3,replace s at position 0 with ],flow_matching,0.3,2.0,40,129
51,add,1.0,c,,]sB,]csB,4,add c at position 1,flow_matching,0.3,2.0,40,129
52,replace,0.0,C,],]csB,CcsB,4,replace ] at position 0 with C,flow_matching,0.3,2.0,40,129
53,replace,1.0,O,c,CcsB,COsB,4,replace c at position 1 with O,flow_matching,0.3,2.0,40,129
54,remove,0.0,C,,COsB,OsB,3,remove C from position 0,flow_matching,0.3,2.0,40,129
55,remove,2.0,B,,OsB,Os,2,remove B from position 2,flow_matching,0.3,2.0,40,129
56,add,2.0,C,,Os,OsC,3,add C at position 2,flow_matching,0.3,2.0,40,129
57,remove,1.0,s,,OsC,OC,2,remove s from position 1,flow_matching,0.3,2.0,40,129
58,add,1.0,),,OC,O)C,3,add ) at position 1,flow_matching,0.3,2.0,40,129
59,remove,2.0,C,,O)C,O),2,remove C from position 2,flow_matching,0.3,2.0,40,129
60,replace,0.0,C,O,O),C),2,replace O at position 0 with C,flow_matching,0.3,2.0,40,129
61,remove,1.0,),,C),C,1,remove ) from position 1,flow_matching,0.3,2.0,40,129
62,replace,0.0,(,C,C,(,1,replace C at position 0 with (,flow_matching,0.3,2.0,40,129
63,replace,0.0,C,(,(,C,1,replace ( at position 0 with C,flow_matching,0.3,2.0,40,129
64,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,40,129
65,remove,1.0,O,,CO,C,1,remove O from position 1,flow_matching,0.3,2.0,40,129
66,add,0.0,+,,C,+C,2,add + at position 0,flow_matching,0.3,2.0,40,129
67,replace,0.0,C,+,+C,CC,2,replace + at position 0 with C,flow_matching,0.3,2.0,40,129
68,add,0.0,C,,CC,CCC,3,add C at position 0,flow_matching,0.3,2.0,40,129
69,replace,0.0,\,C,CCC,\CC,3,replace C at position 0 with \,flow_matching,0.3,2.0,40,129
70,add,2.0,2,,\CC,\C2C,4,add 2 at position 2,flow_matching,0.3,2.0,40,129
71,replace,3.0,o,C,\C2C,\C2o,4,replace C at position 3 with o,flow_matching,0.3,2.0,40,129
72,add,3.0,),,\C2o,\C2)o,5,add ) at position 3,flow_matching,0.3,2.0,40,129
73,add,3.0,],,\C2)o,\C2])o,6,add ] at position 3,flow_matching,0.3,2.0,40,129
74,replace,1.0,1,C,\C2])o,\12])o,6,replace C at position 1 with 1,flow_matching,0.3,2.0,40,129
75,replace,4.0,/,),\12])o,\12]/o,6,replace ) at position 4 with /,flow_matching,0.3,2.0,40,129
76,replace,0.0,C,\,\12]/o,C12]/o,6,replace \ at position 0 with C,flow_matching,0.3,2.0,40,129
77,replace,4.0,F,/,C12]/o,C12]Fo,6,replace / at position 4 with F,flow_matching,0.3,2.0,40,129
78,replace,1.0,O,1,C12]Fo,CO2]Fo,6,replace 1 at position 1 with O,flow_matching,0.3,2.0,40,129
79,add,4.0,6,,CO2]Fo,CO2]6Fo,7,add 6 at position 4,flow_matching,0.3,2.0,40,129
80,add,6.0,(,,CO2]6Fo,CO2]6F(o,8,add ( at position 6,flow_matching,0.3,2.0,40,129
81,add,2.0,#,,CO2]6F(o,CO#2]6F(o,9,add # at position 2,flow_matching,0.3,2.0,40,129
82,replace,2.0,c,#,CO#2]6F(o,COc2]6F(o,9,replace # at position 2 with c,flow_matching,0.3,2.0,40,129
83,remove,4.0,],,COc2]6F(o,COc26F(o,8,remove ] from position 4,flow_matching,0.3,2.0,40,129
84,replace,1.0,\,O,COc26F(o,C\c26F(o,8,replace O at position 1 with \,flow_matching,0.3,2.0,40,129
85,add,4.0,o,,C\c26F(o,C\c2o6F(o,9,add o at position 4,flow_matching,0.3,2.0,40,129
86,replace,5.0,S,6,C\c2o6F(o,C\c2oSF(o,9,replace 6 at position 5 with S,flow_matching,0.3,2.0,40,129
87,add,4.0,4,,C\c2oSF(o,C\c24oSF(o,10,add 4 at position 4,flow_matching,0.3,2.0,40,129
88,replace,3.0,O,2,C\c24oSF(o,C\cO4oSF(o,10,replace 2 at position 3 with O,flow_matching,0.3,2.0,40,129
89,add,4.0,2,,C\cO4oSF(o,C\cO24oSF(o,11,add 2 at position 4,flow_matching,0.3,2.0,40,129
90,remove,5.0,4,,C\cO24oSF(o,C\cO2oSF(o,10,remove 4 from position 5,flow_matching,0.3,2.0,40,129
91,remove,9.0,o,,C\cO2oSF(o,C\cO2oSF(,9,remove o from position 9,flow_matching,0.3,2.0,40,129
92,replace,1.0,O,\,C\cO2oSF(,COcO2oSF(,9,replace \ at position 1 with O,flow_matching,0.3,2.0,40,129
93,replace,3.0,1,O,COcO2oSF(,COc12oSF(,9,replace O at position 3 with 1,flow_matching,0.3,2.0,40,129
94,replace,4.0,c,2,COc12oSF(,COc1coSF(,9,replace 2 at position 4 with c,flow_matching,0.3,2.0,40,129
95,replace,5.0,(,o,COc1coSF(,COc1c(SF(,9,replace o at position 5 with (,flow_matching,0.3,2.0,40,129
96,replace,6.0,C,S,COc1c(SF(,COc1c(CF(,9,replace S at position 6 with C,flow_matching,0.3,2.0,40,129
97,replace,7.0,),F,COc1c(CF(,COc1c(C)(,9,replace F at position 7 with ),flow_matching,0.3,2.0,40,129
98,replace,8.0,c,(,COc1c(C)(,COc1c(C)c,9,replace ( at position 8 with c,flow_matching,0.3,2.0,40,129
99,add,9.0,n,,COc1c(C)c,COc1c(C)cn,10,add n at position 9,flow_matching,0.3,2.0,40,129
100,add,10.0,c,,COc1c(C)cn,COc1c(C)cnc,11,add c at position 10,flow_matching,0.3,2.0,40,129
101,add,11.0,(,,COc1c(C)cnc,COc1c(C)cnc(,12,add ( at position 11,flow_matching,0.3,2.0,40,129
102,add,12.0,C,,COc1c(C)cnc(,COc1c(C)cnc(C,13,add C at position 12,flow_matching,0.3,2.0,40,129
103,add,13.0,N,,COc1c(C)cnc(C,COc1c(C)cnc(CN,14,add N at position 13,flow_matching,0.3,2.0,40,129
104,add,14.0,C,,COc1c(C)cnc(CN,COc1c(C)cnc(CNC,15,add C at position 14,flow_matching,0.3,2.0,40,129
105,add,15.0,(,,COc1c(C)cnc(CNC,COc1c(C)cnc(CNC(,16,add ( at position 15,flow_matching,0.3,2.0,40,129
106,add,16.0,=,,COc1c(C)cnc(CNC(,COc1c(C)cnc(CNC(=,17,add = at position 16,flow_matching,0.3,2.0,40,129
107,add,17.0,O,,COc1c(C)cnc(CNC(=,COc1c(C)cnc(CNC(=O,18,add O at position 17,flow_matching,0.3,2.0,40,129
108,add,18.0,),,COc1c(C)cnc(CNC(=O,COc1c(C)cnc(CNC(=O),19,add ) at position 18,flow_matching,0.3,2.0,40,129
109,add,19.0,N,,COc1c(C)cnc(CNC(=O),COc1c(C)cnc(CNC(=O)N,20,add N at position 19,flow_matching,0.3,2.0,40,129
110,add,20.0,c,,COc1c(C)cnc(CNC(=O)N,COc1c(C)cnc(CNC(=O)Nc,21,add c at position 20,flow_matching,0.3,2.0,40,129
111,add,21.0,2,,COc1c(C)cnc(CNC(=O)Nc,COc1c(C)cnc(CNC(=O)Nc2,22,add 2 at position 21,flow_matching,0.3,2.0,40,129
112,add,22.0,c,,COc1c(C)cnc(CNC(=O)Nc2,COc1c(C)cnc(CNC(=O)Nc2c,23,add c at position 22,flow_matching,0.3,2.0,40,129
113,add,23.0,c,,COc1c(C)cnc(CNC(=O)Nc2c,COc1c(C)cnc(CNC(=O)Nc2cc,24,add c at position 23,flow_matching,0.3,2.0,40,129
114,add,24.0,c,,COc1c(C)cnc(CNC(=O)Nc2cc,COc1c(C)cnc(CNC(=O)Nc2ccc,25,add c at position 24,flow_matching,0.3,2.0,40,129
115,add,25.0,(,,COc1c(C)cnc(CNC(=O)Nc2ccc,COc1c(C)cnc(CNC(=O)Nc2ccc(,26,add ( at position 25,flow_matching,0.3,2.0,40,129
116,add,26.0,N,,COc1c(C)cnc(CNC(=O)Nc2ccc(,COc1c(C)cnc(CNC(=O)Nc2ccc(N,27,add N at position 26,flow_matching,0.3,2.0,40,129
117,add,27.0,(,,COc1c(C)cnc(CNC(=O)Nc2ccc(N,COc1c(C)cnc(CNC(=O)Nc2ccc(N(,28,add ( at position 27,flow_matching,0.3,2.0,40,129
118,add,28.0,C,,COc1c(C)cnc(CNC(=O)Nc2ccc(N(,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C,29,add C at position 28,flow_matching,0.3,2.0,40,129
119,add,29.0,),,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C),30,add ) at position 29,flow_matching,0.3,2.0,40,129
120,add,30.0,C,,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C),COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C,31,add C at position 30,flow_matching,0.3,2.0,40,129
121,add,31.0,),,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C),32,add ) at position 31,flow_matching,0.3,2.0,40,129
122,add,32.0,c,,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C),COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)c,33,add c at position 32,flow_matching,0.3,2.0,40,129
123,add,33.0,c,,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)c,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc,34,add c at position 33,flow_matching,0.3,2.0,40,129
124,add,34.0,2,,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2,35,add 2 at position 34,flow_matching,0.3,2.0,40,129
125,add,35.0,),,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2),36,add ) at position 35,flow_matching,0.3,2.0,40,129
126,add,36.0,c,,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2),COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2)c,37,add c at position 36,flow_matching,0.3,2.0,40,129
127,add,37.0,1,,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2)c,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2)c1,38,add 1 at position 37,flow_matching,0.3,2.0,40,129
128,add,38.0,C,,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2)c1,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2)c1C,39,add C at position 38,flow_matching,0.3,2.0,40,129
129,add,39.0,"
",,COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2)c1C,"COc1c(C)cnc(CNC(=O)Nc2ccc(N(C)C)cc2)c1C
",40,"add 
 at position 39",flow_matching,0.3,2.0,40,129
