step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,40,97
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,40,97
2,add,1.0,H,,C,CH,2,add H at position 1,flow_matching,0.3,2.0,40,97
3,add,2.0,/,,CH,CH/,3,add / at position 2,flow_matching,0.3,2.0,40,97
4,replace,2.0,O,/,CH/,CHO,3,replace / at position 2 with O,flow_matching,0.3,2.0,40,97
5,replace,1.0,C,H,CHO,CCO,3,replace H at position 1 with C,flow_matching,0.3,2.0,40,97
6,replace,0.0,),C,CCO,)CO,3,replace <PERSON> at position 0 with ),flow_matching,0.3,2.0,40,97
7,add,2.0,4,,)CO,)C4O,4,add 4 at position 2,flow_matching,0.3,2.0,40,97
8,remove,3.0,O,,)C4O,)C4,3,remove O from position 3,flow_matching,0.3,2.0,40,97
9,replace,0.0,C,),)C4,CC4,3,replace ) at position 0 with C,flow_matching,0.3,2.0,40,97
10,replace,2.0,(,4,CC4,CC(,3,replace 4 at position 2 with (,flow_matching,0.3,2.0,40,97
11,add,2.0,O,,CC(,CCO(,4,add O at position 2,flow_matching,0.3,2.0,40,97
12,add,1.0,2,,CCO(,C2CO(,5,add 2 at position 1,flow_matching,0.3,2.0,40,97
13,replace,4.0,n,(,C2CO(,C2COn,5,replace ( at position 4 with n,flow_matching,0.3,2.0,40,97
14,replace,2.0,2,C,C2COn,C22On,5,replace C at position 2 with 2,flow_matching,0.3,2.0,40,97
15,replace,1.0,C,2,C22On,CC2On,5,replace 2 at position 1 with C,flow_matching,0.3,2.0,40,97
16,remove,0.0,C,,CC2On,C2On,4,remove C from position 0,flow_matching,0.3,2.0,40,97
17,remove,1.0,2,,C2On,COn,3,remove 2 from position 1,flow_matching,0.3,2.0,40,97
18,replace,0.0,H,C,COn,HOn,3,replace C at position 0 with H,flow_matching,0.3,2.0,40,97
19,add,0.0,S,,HOn,SHOn,4,add S at position 0,flow_matching,0.3,2.0,40,97
20,replace,0.0,C,S,SHOn,CHOn,4,replace S at position 0 with C,flow_matching,0.3,2.0,40,97
21,add,3.0,\,,CHOn,CHO\n,5,add \ at position 3,flow_matching,0.3,2.0,40,97
22,add,2.0,=,,CHO\n,CH=O\n,6,add = at position 2,flow_matching,0.3,2.0,40,97
23,remove,4.0,\,,CH=O\n,CH=On,5,remove \ from position 4,flow_matching,0.3,2.0,40,97
24,remove,1.0,H,,CH=On,C=On,4,remove H from position 1,flow_matching,0.3,2.0,40,97
25,replace,1.0,C,=,C=On,CCOn,4,replace = at position 1 with C,flow_matching,0.3,2.0,40,97
26,add,0.0,(,,CCOn,(CCOn,5,add ( at position 0,flow_matching,0.3,2.0,40,97
27,replace,0.0,C,(,(CCOn,CCCOn,5,replace ( at position 0 with C,flow_matching,0.3,2.0,40,97
28,add,5.0,],,CCCOn,CCCOn],6,add ] at position 5,flow_matching,0.3,2.0,40,97
29,replace,5.0,H,],CCCOn],CCCOnH,6,replace ] at position 5 with H,flow_matching,0.3,2.0,40,97
30,replace,2.0,(,C,CCCOnH,CC(OnH,6,replace C at position 2 with (,flow_matching,0.3,2.0,40,97
31,replace,2.0,s,(,CC(OnH,CCsOnH,6,replace ( at position 2 with s,flow_matching,0.3,2.0,40,97
32,replace,2.0,(,s,CCsOnH,CC(OnH,6,replace s at position 2 with (,flow_matching,0.3,2.0,40,97
33,add,3.0,[,,CC(OnH,CC([OnH,7,add [ at position 3,flow_matching,0.3,2.0,40,97
34,replace,3.0,C,[,CC([OnH,CC(COnH,7,replace [ at position 3 with C,flow_matching,0.3,2.0,40,97
35,replace,4.0,),O,CC(COnH,CC(C)nH,7,replace O at position 4 with ),flow_matching,0.3,2.0,40,97
36,replace,5.0,O,n,CC(C)nH,CC(C)OH,7,replace n at position 5 with O,flow_matching,0.3,2.0,40,97
37,replace,5.0,C,O,CC(C)OH,CC(C)CH,7,replace O at position 5 with C,flow_matching,0.3,2.0,40,97
38,replace,2.0,n,(,CC(C)CH,CCnC)CH,7,replace ( at position 2 with n,flow_matching,0.3,2.0,40,97
39,replace,5.0,=,C,CCnC)CH,CCnC)=H,7,replace C at position 5 with =,flow_matching,0.3,2.0,40,97
40,replace,4.0,/,),CCnC)=H,CCnC/=H,7,replace ) at position 4 with /,flow_matching,0.3,2.0,40,97
41,replace,5.0,s,=,CCnC/=H,CCnC/sH,7,replace = at position 5 with s,flow_matching,0.3,2.0,40,97
42,add,7.0,/,,CCnC/sH,CCnC/sH/,8,add / at position 7,flow_matching,0.3,2.0,40,97
43,replace,2.0,(,n,CCnC/sH/,CC(C/sH/,8,replace n at position 2 with (,flow_matching,0.3,2.0,40,97
44,add,2.0,o,,CC(C/sH/,CCo(C/sH/,9,add o at position 2,flow_matching,0.3,2.0,40,97
45,replace,2.0,(,o,CCo(C/sH/,CC((C/sH/,9,replace o at position 2 with (,flow_matching,0.3,2.0,40,97
46,add,5.0,2,,CC((C/sH/,CC((C2/sH/,10,add 2 at position 5,flow_matching,0.3,2.0,40,97
47,add,10.0,H,,CC((C2/sH/,CC((C2/sH/H,11,add H at position 10,flow_matching,0.3,2.0,40,97
48,remove,6.0,/,,CC((C2/sH/H,CC((C2sH/H,10,remove / from position 6,flow_matching,0.3,2.0,40,97
49,replace,5.0,n,2,CC((C2sH/H,CC((CnsH/H,10,replace 2 at position 5 with n,flow_matching,0.3,2.0,40,97
50,replace,5.0,l,n,CC((CnsH/H,CC((ClsH/H,10,replace n at position 5 with l,flow_matching,0.3,2.0,40,97
51,remove,8.0,/,,CC((ClsH/H,CC((ClsHH,9,remove / from position 8,flow_matching,0.3,2.0,40,97
52,replace,3.0,C,(,CC((ClsHH,CC(CClsHH,9,replace ( at position 3 with C,flow_matching,0.3,2.0,40,97
53,replace,4.0,),C,CC(CClsHH,CC(C)lsHH,9,replace C at position 4 with ),flow_matching,0.3,2.0,40,97
54,add,5.0,s,,CC(C)lsHH,CC(C)slsHH,10,add s at position 5,flow_matching,0.3,2.0,40,97
55,replace,2.0,r,(,CC(C)slsHH,CCrC)slsHH,10,replace ( at position 2 with r,flow_matching,0.3,2.0,40,97
56,add,5.0,5,,CCrC)slsHH,CCrC)5slsHH,11,add 5 at position 5,flow_matching,0.3,2.0,40,97
57,replace,2.0,(,r,CCrC)5slsHH,CC(C)5slsHH,11,replace r at position 2 with (,flow_matching,0.3,2.0,40,97
58,remove,0.0,C,,CC(C)5slsHH,C(C)5slsHH,10,remove C from position 0,flow_matching,0.3,2.0,40,97
59,add,2.0,/,,C(C)5slsHH,C(/C)5slsHH,11,add / at position 2,flow_matching,0.3,2.0,40,97
60,remove,10.0,H,,C(/C)5slsHH,C(/C)5slsH,10,remove H from position 10,flow_matching,0.3,2.0,40,97
61,replace,1.0,C,(,C(/C)5slsH,CC/C)5slsH,10,replace ( at position 1 with C,flow_matching,0.3,2.0,40,97
62,replace,2.0,(,/,CC/C)5slsH,CC(C)5slsH,10,replace / at position 2 with (,flow_matching,0.3,2.0,40,97
63,replace,5.0,C,5,CC(C)5slsH,CC(C)CslsH,10,replace 5 at position 5 with C,flow_matching,0.3,2.0,40,97
64,replace,6.0,N,s,CC(C)CslsH,CC(C)CNlsH,10,replace s at position 6 with N,flow_matching,0.3,2.0,40,97
65,replace,7.0,(,l,CC(C)CNlsH,CC(C)CN(sH,10,replace l at position 7 with (,flow_matching,0.3,2.0,40,97
66,replace,8.0,C,s,CC(C)CN(sH,CC(C)CN(CH,10,replace s at position 8 with C,flow_matching,0.3,2.0,40,97
67,replace,9.0,(,H,CC(C)CN(CH,CC(C)CN(C(,10,replace H at position 9 with (,flow_matching,0.3,2.0,40,97
68,add,10.0,=,,CC(C)CN(C(,CC(C)CN(C(=,11,add = at position 10,flow_matching,0.3,2.0,40,97
69,add,11.0,O,,CC(C)CN(C(=,CC(C)CN(C(=O,12,add O at position 11,flow_matching,0.3,2.0,40,97
70,add,12.0,),,CC(C)CN(C(=O,CC(C)CN(C(=O),13,add ) at position 12,flow_matching,0.3,2.0,40,97
71,add,13.0,N,,CC(C)CN(C(=O),CC(C)CN(C(=O)N,14,add N at position 13,flow_matching,0.3,2.0,40,97
72,add,14.0,C,,CC(C)CN(C(=O)N,CC(C)CN(C(=O)NC,15,add C at position 14,flow_matching,0.3,2.0,40,97
73,add,15.0,c,,CC(C)CN(C(=O)NC,CC(C)CN(C(=O)NCc,16,add c at position 15,flow_matching,0.3,2.0,40,97
74,add,16.0,1,,CC(C)CN(C(=O)NCc,CC(C)CN(C(=O)NCc1,17,add 1 at position 16,flow_matching,0.3,2.0,40,97
75,add,17.0,c,,CC(C)CN(C(=O)NCc1,CC(C)CN(C(=O)NCc1c,18,add c at position 17,flow_matching,0.3,2.0,40,97
76,add,18.0,c,,CC(C)CN(C(=O)NCc1c,CC(C)CN(C(=O)NCc1cc,19,add c at position 18,flow_matching,0.3,2.0,40,97
77,add,19.0,c,,CC(C)CN(C(=O)NCc1cc,CC(C)CN(C(=O)NCc1ccc,20,add c at position 19,flow_matching,0.3,2.0,40,97
78,add,20.0,(,,CC(C)CN(C(=O)NCc1ccc,CC(C)CN(C(=O)NCc1ccc(,21,add ( at position 20,flow_matching,0.3,2.0,40,97
79,add,21.0,C,,CC(C)CN(C(=O)NCc1ccc(,CC(C)CN(C(=O)NCc1ccc(C,22,add C at position 21,flow_matching,0.3,2.0,40,97
80,add,22.0,(,,CC(C)CN(C(=O)NCc1ccc(C,CC(C)CN(C(=O)NCc1ccc(C(,23,add ( at position 22,flow_matching,0.3,2.0,40,97
81,add,23.0,F,,CC(C)CN(C(=O)NCc1ccc(C(,CC(C)CN(C(=O)NCc1ccc(C(F,24,add F at position 23,flow_matching,0.3,2.0,40,97
82,add,24.0,),,CC(C)CN(C(=O)NCc1ccc(C(F,CC(C)CN(C(=O)NCc1ccc(C(F),25,add ) at position 24,flow_matching,0.3,2.0,40,97
83,add,25.0,(,,CC(C)CN(C(=O)NCc1ccc(C(F),CC(C)CN(C(=O)NCc1ccc(C(F)(,26,add ( at position 25,flow_matching,0.3,2.0,40,97
84,add,26.0,F,,CC(C)CN(C(=O)NCc1ccc(C(F)(,CC(C)CN(C(=O)NCc1ccc(C(F)(F,27,add F at position 26,flow_matching,0.3,2.0,40,97
85,add,27.0,),,CC(C)CN(C(=O)NCc1ccc(C(F)(F,CC(C)CN(C(=O)NCc1ccc(C(F)(F),28,add ) at position 27,flow_matching,0.3,2.0,40,97
86,add,28.0,F,,CC(C)CN(C(=O)NCc1ccc(C(F)(F),CC(C)CN(C(=O)NCc1ccc(C(F)(F)F,29,add F at position 28,flow_matching,0.3,2.0,40,97
87,add,29.0,),,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F),30,add ) at position 29,flow_matching,0.3,2.0,40,97
88,add,30.0,c,,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F),CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)c,31,add c at position 30,flow_matching,0.3,2.0,40,97
89,add,31.0,c,,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)c,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc,32,add c at position 31,flow_matching,0.3,2.0,40,97
90,add,32.0,1,,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1,33,add 1 at position 32,flow_matching,0.3,2.0,40,97
91,add,33.0,),,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1),34,add ) at position 33,flow_matching,0.3,2.0,40,97
92,add,34.0,C,,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1),CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C,35,add C at position 34,flow_matching,0.3,2.0,40,97
93,add,35.0,1,,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C1,36,add 1 at position 35,flow_matching,0.3,2.0,40,97
94,add,36.0,C,,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C1,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C1C,37,add C at position 36,flow_matching,0.3,2.0,40,97
95,add,37.0,C,,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C1C,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C1CC,38,add C at position 37,flow_matching,0.3,2.0,40,97
96,add,38.0,1,,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C1CC,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C1CC1,39,add 1 at position 38,flow_matching,0.3,2.0,40,97
97,add,39.0,"
",,CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C1CC1,"CC(C)CN(C(=O)NCc1ccc(C(F)(F)F)cc1)C1CC1
",40,"add 
 at position 39",flow_matching,0.3,2.0,40,97
