step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,52,115
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,52,115
2,add,1.0,I,,C,CI,2,add I at position 1,flow_matching,0.3,2.0,52,115
3,replace,1.0,+,I,CI,C+,2,replace I at position 1 with +,flow_matching,0.3,2.0,52,115
4,remove,0.0,C,,C+,+,1,remove C from position 0,flow_matching,0.3,2.0,52,115
5,add,1.0,#,,+,+#,2,add # at position 1,flow_matching,0.3,2.0,52,115
6,replace,0.0,C,+,+#,C#,2,replace + at position 0 with C,flow_matching,0.3,2.0,52,115
7,remove,0.0,C,,C#,#,1,remove C from position 0,flow_matching,0.3,2.0,52,115
8,replace,0.0,o,#,#,o,1,replace # at position 0 with o,flow_matching,0.3,2.0,52,115
9,remove,0.0,o,,o,,0,remove o from position 0,flow_matching,0.3,2.0,52,115
10,add,0.0,o,,,o,1,add o at position 0,flow_matching,0.3,2.0,52,115
11,replace,0.0,#,o,o,#,1,replace o at position 0 with #,flow_matching,0.3,2.0,52,115
12,replace,0.0,C,#,#,C,1,replace # at position 0 with C,flow_matching,0.3,2.0,52,115
13,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,52,115
14,replace,0.0,H,C,CC,HC,2,replace C at position 0 with H,flow_matching,0.3,2.0,52,115
15,remove,1.0,C,,HC,H,1,remove C from position 1,flow_matching,0.3,2.0,52,115
16,replace,0.0,C,H,H,C,1,replace H at position 0 with C,flow_matching,0.3,2.0,52,115
17,replace,0.0,r,C,C,r,1,replace C at position 0 with r,flow_matching,0.3,2.0,52,115
18,add,0.0,2,,r,2r,2,add 2 at position 0,flow_matching,0.3,2.0,52,115
19,remove,0.0,2,,2r,r,1,remove 2 from position 0,flow_matching,0.3,2.0,52,115
20,replace,0.0,C,r,r,C,1,replace r at position 0 with C,flow_matching,0.3,2.0,52,115
21,replace,0.0,1,C,C,1,1,replace C at position 0 with 1,flow_matching,0.3,2.0,52,115
22,replace,0.0,7,1,1,7,1,replace 1 at position 0 with 7,flow_matching,0.3,2.0,52,115
23,replace,0.0,C,7,7,C,1,replace 7 at position 0 with C,flow_matching,0.3,2.0,52,115
24,add,1.0,2,,C,C2,2,add 2 at position 1,flow_matching,0.3,2.0,52,115
25,remove,1.0,2,,C2,C,1,remove 2 from position 1,flow_matching,0.3,2.0,52,115
26,add,1.0,C,,C,CC,2,add C at position 1,flow_matching,0.3,2.0,52,115
27,remove,0.0,C,,CC,C,1,remove C from position 0,flow_matching,0.3,2.0,52,115
28,add,0.0,+,,C,+C,2,add + at position 0,flow_matching,0.3,2.0,52,115
29,replace,0.0,C,+,+C,CC,2,replace + at position 0 with C,flow_matching,0.3,2.0,52,115
30,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,52,115
31,replace,2.0,N,(,CC(,CCN,3,replace ( at position 2 with N,flow_matching,0.3,2.0,52,115
32,add,3.0,\,,CCN,CCN\,4,add \ at position 3,flow_matching,0.3,2.0,52,115
33,replace,0.0,=,C,CCN\,=CN\,4,replace C at position 0 with =,flow_matching,0.3,2.0,52,115
34,add,1.0,+,,=CN\,=+CN\,5,add + at position 1,flow_matching,0.3,2.0,52,115
35,remove,3.0,N,,=+CN\,=+C\,4,remove N from position 3,flow_matching,0.3,2.0,52,115
36,add,4.0,s,,=+C\,=+C\s,5,add s at position 4,flow_matching,0.3,2.0,52,115
37,remove,1.0,+,,=+C\s,=C\s,4,remove + from position 1,flow_matching,0.3,2.0,52,115
38,replace,0.0,C,=,=C\s,CC\s,4,replace = at position 0 with C,flow_matching,0.3,2.0,52,115
39,remove,1.0,C,,CC\s,C\s,3,remove C from position 1,flow_matching,0.3,2.0,52,115
40,replace,1.0,@,\,C\s,C@s,3,replace \ at position 1 with @,flow_matching,0.3,2.0,52,115
41,replace,1.0,C,@,C@s,CCs,3,replace @ at position 1 with C,flow_matching,0.3,2.0,52,115
42,remove,1.0,C,,CCs,Cs,2,remove C from position 1,flow_matching,0.3,2.0,52,115
43,add,2.0,1,,Cs,Cs1,3,add 1 at position 2,flow_matching,0.3,2.0,52,115
44,remove,0.0,C,,Cs1,s1,2,remove C from position 0,flow_matching,0.3,2.0,52,115
45,replace,0.0,I,s,s1,I1,2,replace s at position 0 with I,flow_matching,0.3,2.0,52,115
46,replace,0.0,C,I,I1,C1,2,replace I at position 0 with C,flow_matching,0.3,2.0,52,115
47,replace,1.0,C,1,C1,CC,2,replace 1 at position 1 with C,flow_matching,0.3,2.0,52,115
48,add,2.0,n,,CC,CCn,3,add n at position 2,flow_matching,0.3,2.0,52,115
49,replace,2.0,(,n,CCn,CC(,3,replace n at position 2 with (,flow_matching,0.3,2.0,52,115
50,remove,0.0,C,,CC(,C(,2,remove C from position 0,flow_matching,0.3,2.0,52,115
51,remove,0.0,C,,C(,(,1,remove C from position 0,flow_matching,0.3,2.0,52,115
52,replace,0.0,C,(,(,C,1,replace ( at position 0 with C,flow_matching,0.3,2.0,52,115
53,add,0.0,+,,C,+C,2,add + at position 0,flow_matching,0.3,2.0,52,115
54,replace,0.0,C,+,+C,CC,2,replace + at position 0 with C,flow_matching,0.3,2.0,52,115
55,add,2.0,(,,CC,CC(,3,add ( at position 2,flow_matching,0.3,2.0,52,115
56,add,3.0,C,,CC(,CC(C,4,add C at position 3,flow_matching,0.3,2.0,52,115
57,add,3.0,(,,CC(C,CC((C,5,add ( at position 3,flow_matching,0.3,2.0,52,115
58,replace,3.0,C,(,CC((C,CC(CC,5,replace ( at position 3 with C,flow_matching,0.3,2.0,52,115
59,replace,4.0,=,C,CC(CC,CC(C=,5,replace C at position 4 with =,flow_matching,0.3,2.0,52,115
60,remove,4.0,=,,CC(C=,CC(C,4,remove = from position 4,flow_matching,0.3,2.0,52,115
61,add,4.0,),,CC(C,CC(C),5,add ) at position 4,flow_matching,0.3,2.0,52,115
62,add,5.0,[,,CC(C),CC(C)[,6,add [ at position 5,flow_matching,0.3,2.0,52,115
63,add,5.0,r,,CC(C)[,CC(C)r[,7,add r at position 5,flow_matching,0.3,2.0,52,115
64,replace,3.0,5,C,CC(C)r[,CC(5)r[,7,replace C at position 3 with 5,flow_matching,0.3,2.0,52,115
65,replace,4.0,-,),CC(5)r[,CC(5-r[,7,replace ) at position 4 with -,flow_matching,0.3,2.0,52,115
66,replace,3.0,C,5,CC(5-r[,CC(C-r[,7,replace 5 at position 3 with C,flow_matching,0.3,2.0,52,115
67,replace,5.0,S,r,CC(C-r[,CC(C-S[,7,replace r at position 5 with S,flow_matching,0.3,2.0,52,115
68,replace,4.0,),-,CC(C-S[,CC(C)S[,7,replace - at position 4 with ),flow_matching,0.3,2.0,52,115
69,replace,5.0,[,S,CC(C)S[,CC(C)[[,7,replace S at position 5 with [,flow_matching,0.3,2.0,52,115
70,replace,6.0,C,[,CC(C)[[,CC(C)[C,7,replace [ at position 6 with C,flow_matching,0.3,2.0,52,115
71,add,7.0,@,,CC(C)[C,CC(C)[C@,8,add @ at position 7,flow_matching,0.3,2.0,52,115
72,add,8.0,H,,CC(C)[C@,CC(C)[C@H,9,add H at position 8,flow_matching,0.3,2.0,52,115
73,add,9.0,],,CC(C)[C@H,CC(C)[C@H],10,add ] at position 9,flow_matching,0.3,2.0,52,115
74,add,10.0,(,,CC(C)[C@H],CC(C)[C@H](,11,add ( at position 10,flow_matching,0.3,2.0,52,115
75,add,11.0,[,,CC(C)[C@H](,CC(C)[C@H]([,12,add [ at position 11,flow_matching,0.3,2.0,52,115
76,add,12.0,N,,CC(C)[C@H]([,CC(C)[C@H]([N,13,add N at position 12,flow_matching,0.3,2.0,52,115
77,add,13.0,H,,CC(C)[C@H]([N,CC(C)[C@H]([NH,14,add H at position 13,flow_matching,0.3,2.0,52,115
78,add,14.0,2,,CC(C)[C@H]([NH,CC(C)[C@H]([NH2,15,add 2 at position 14,flow_matching,0.3,2.0,52,115
79,add,15.0,+,,CC(C)[C@H]([NH2,CC(C)[C@H]([NH2+,16,add + at position 15,flow_matching,0.3,2.0,52,115
80,add,16.0,],,CC(C)[C@H]([NH2+,CC(C)[C@H]([NH2+],17,add ] at position 16,flow_matching,0.3,2.0,52,115
81,add,17.0,C,,CC(C)[C@H]([NH2+],CC(C)[C@H]([NH2+]C,18,add C at position 17,flow_matching,0.3,2.0,52,115
82,add,18.0,C,,CC(C)[C@H]([NH2+]C,CC(C)[C@H]([NH2+]CC,19,add C at position 18,flow_matching,0.3,2.0,52,115
83,add,19.0,1,,CC(C)[C@H]([NH2+]CC,CC(C)[C@H]([NH2+]CC1,20,add 1 at position 19,flow_matching,0.3,2.0,52,115
84,add,20.0,C,,CC(C)[C@H]([NH2+]CC1,CC(C)[C@H]([NH2+]CC1C,21,add C at position 20,flow_matching,0.3,2.0,52,115
85,add,21.0,C,,CC(C)[C@H]([NH2+]CC1C,CC(C)[C@H]([NH2+]CC1CC,22,add C at position 21,flow_matching,0.3,2.0,52,115
86,add,22.0,N,,CC(C)[C@H]([NH2+]CC1CC,CC(C)[C@H]([NH2+]CC1CCN,23,add N at position 22,flow_matching,0.3,2.0,52,115
87,add,23.0,(,,CC(C)[C@H]([NH2+]CC1CCN,CC(C)[C@H]([NH2+]CC1CCN(,24,add ( at position 23,flow_matching,0.3,2.0,52,115
88,add,24.0,C,,CC(C)[C@H]([NH2+]CC1CCN(,CC(C)[C@H]([NH2+]CC1CCN(C,25,add C at position 24,flow_matching,0.3,2.0,52,115
89,add,25.0,(,,CC(C)[C@H]([NH2+]CC1CCN(C,CC(C)[C@H]([NH2+]CC1CCN(C(,26,add ( at position 25,flow_matching,0.3,2.0,52,115
90,add,26.0,=,,CC(C)[C@H]([NH2+]CC1CCN(C(,CC(C)[C@H]([NH2+]CC1CCN(C(=,27,add = at position 26,flow_matching,0.3,2.0,52,115
91,add,27.0,O,,CC(C)[C@H]([NH2+]CC1CCN(C(=,CC(C)[C@H]([NH2+]CC1CCN(C(=O,28,add O at position 27,flow_matching,0.3,2.0,52,115
92,add,28.0,),,CC(C)[C@H]([NH2+]CC1CCN(C(=O,CC(C)[C@H]([NH2+]CC1CCN(C(=O),29,add ) at position 28,flow_matching,0.3,2.0,52,115
93,add,29.0,O,,CC(C)[C@H]([NH2+]CC1CCN(C(=O),CC(C)[C@H]([NH2+]CC1CCN(C(=O)O,30,add O at position 29,flow_matching,0.3,2.0,52,115
94,add,30.0,C,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)O,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC,31,add C at position 30,flow_matching,0.3,2.0,52,115
95,add,31.0,(,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(,32,add ( at position 31,flow_matching,0.3,2.0,52,115
96,add,32.0,C,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C,33,add C at position 32,flow_matching,0.3,2.0,52,115
97,add,33.0,),,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C),34,add ) at position 33,flow_matching,0.3,2.0,52,115
98,add,34.0,(,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C),CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(,35,add ( at position 34,flow_matching,0.3,2.0,52,115
99,add,35.0,C,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C,36,add C at position 35,flow_matching,0.3,2.0,52,115
100,add,36.0,),,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C),37,add ) at position 36,flow_matching,0.3,2.0,52,115
101,add,37.0,C,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C),CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C,38,add C at position 37,flow_matching,0.3,2.0,52,115
102,add,38.0,),,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C),39,add ) at position 38,flow_matching,0.3,2.0,52,115
103,add,39.0,C,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C),CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)C,40,add C at position 39,flow_matching,0.3,2.0,52,115
104,add,40.0,C,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)C,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC,41,add C at position 40,flow_matching,0.3,2.0,52,115
105,add,41.0,1,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1,42,add 1 at position 41,flow_matching,0.3,2.0,52,115
106,add,42.0,),,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1),43,add ) at position 42,flow_matching,0.3,2.0,52,115
107,add,43.0,c,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1),CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c,44,add c at position 43,flow_matching,0.3,2.0,52,115
108,add,44.0,1,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1,45,add 1 at position 44,flow_matching,0.3,2.0,52,115
109,add,45.0,c,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1c,46,add c at position 45,flow_matching,0.3,2.0,52,115
110,add,46.0,c,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1c,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1cc,47,add c at position 46,flow_matching,0.3,2.0,52,115
111,add,47.0,c,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1cc,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1ccc,48,add c at position 47,flow_matching,0.3,2.0,52,115
112,add,48.0,n,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1ccc,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1cccn,49,add n at position 48,flow_matching,0.3,2.0,52,115
113,add,49.0,c,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1cccn,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1cccnc,50,add c at position 49,flow_matching,0.3,2.0,52,115
114,add,50.0,1,,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1cccnc,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1cccnc1,51,add 1 at position 50,flow_matching,0.3,2.0,52,115
115,add,51.0,"
",,CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1cccnc1,"CC(C)[C@H]([NH2+]CC1CCN(C(=O)OC(C)(C)C)CC1)c1cccnc1
",52,"add 
 at position 51",flow_matching,0.3,2.0,52,115
