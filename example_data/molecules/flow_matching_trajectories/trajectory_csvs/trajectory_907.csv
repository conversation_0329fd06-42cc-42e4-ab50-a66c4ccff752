step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,54,116
1,add,0.0,n,,,n,1,add n at position 0,flow_matching,0.3,2.0,54,116
2,replace,0.0,C,n,n,C,1,replace n at position 0 with C,flow_matching,0.3,2.0,54,116
3,replace,0.0,H,C,C,H,1,replace <PERSON> at position 0 with H,flow_matching,0.3,2.0,54,116
4,replace,0.0,2,H,H,2,1,replace H at position 0 with 2,flow_matching,0.3,2.0,54,116
5,replace,0.0,C,2,2,C,1,replace 2 at position 0 with C,flow_matching,0.3,2.0,54,116
6,add,1.0,<PERSON>,,<PERSON>,<PERSON>,2,add <PERSON> at position 1,flow_matching,0.3,2.0,54,116
7,add,0.0,5,,CC,5CC,3,add 5 at position 0,flow_matching,0.3,2.0,54,116
8,replace,0.0,N,5,5CC,NCC,3,replace 5 at position 0 with N,flow_matching,0.3,2.0,54,116
9,replace,0.0,C,N,NCC,CCC,3,replace N at position 0 with C,flow_matching,0.3,2.0,54,116
10,replace,2.0,H,C,CCC,CCH,3,replace C at position 2 with H,flow_matching,0.3,2.0,54,116
11,replace,0.0,@,C,CCH,@CH,3,replace C at position 0 with @,flow_matching,0.3,2.0,54,116
12,remove,2.0,H,,@CH,@C,2,remove H from position 2,flow_matching,0.3,2.0,54,116
13,replace,0.0,N,@,@C,NC,2,replace @ at position 0 with N,flow_matching,0.3,2.0,54,116
14,add,1.0,1,,NC,N1C,3,add 1 at position 1,flow_matching,0.3,2.0,54,116
15,add,0.0,],,N1C,]N1C,4,add ] at position 0,flow_matching,0.3,2.0,54,116
16,replace,1.0,=,N,]N1C,]=1C,4,replace N at position 1 with =,flow_matching,0.3,2.0,54,116
17,replace,0.0,C,],]=1C,C=1C,4,replace ] at position 0 with C,flow_matching,0.3,2.0,54,116
18,replace,2.0,H,1,C=1C,C=HC,4,replace 1 at position 2 with H,flow_matching,0.3,2.0,54,116
19,remove,3.0,C,,C=HC,C=H,3,remove C from position 3,flow_matching,0.3,2.0,54,116
20,replace,0.0,3,C,C=H,3=H,3,replace C at position 0 with 3,flow_matching,0.3,2.0,54,116
21,add,0.0,H,,3=H,H3=H,4,add H at position 0,flow_matching,0.3,2.0,54,116
22,add,1.0,2,,H3=H,H23=H,5,add 2 at position 1,flow_matching,0.3,2.0,54,116
23,remove,4.0,H,,H23=H,H23=,4,remove H from position 4,flow_matching,0.3,2.0,54,116
24,add,0.0,B,,H23=,BH23=,5,add B at position 0,flow_matching,0.3,2.0,54,116
25,replace,3.0,s,3,BH23=,BH2s=,5,replace 3 at position 3 with s,flow_matching,0.3,2.0,54,116
26,add,2.0,(,,BH2s=,BH(2s=,6,add ( at position 2,flow_matching,0.3,2.0,54,116
27,replace,1.0,C,H,BH(2s=,BC(2s=,6,replace H at position 1 with C,flow_matching,0.3,2.0,54,116
28,replace,0.0,C,B,BC(2s=,CC(2s=,6,replace B at position 0 with C,flow_matching,0.3,2.0,54,116
29,remove,3.0,2,,CC(2s=,CC(s=,5,remove 2 from position 3,flow_matching,0.3,2.0,54,116
30,add,3.0,N,,CC(s=,CC(Ns=,6,add N at position 3,flow_matching,0.3,2.0,54,116
31,replace,3.0,],N,CC(Ns=,CC(]s=,6,replace N at position 3 with ],flow_matching,0.3,2.0,54,116
32,add,1.0,N,,CC(]s=,CNC(]s=,7,add N at position 1,flow_matching,0.3,2.0,54,116
33,replace,1.0,C,N,CNC(]s=,CCC(]s=,7,replace N at position 1 with C,flow_matching,0.3,2.0,54,116
34,add,3.0,\,,CCC(]s=,CCC\(]s=,8,add \ at position 3,flow_matching,0.3,2.0,54,116
35,replace,2.0,(,C,CCC\(]s=,CC(\(]s=,8,replace C at position 2 with (,flow_matching,0.3,2.0,54,116
36,replace,6.0,B,s,CC(\(]s=,CC(\(]B=,8,replace s at position 6 with B,flow_matching,0.3,2.0,54,116
37,remove,7.0,=,,CC(\(]B=,CC(\(]B,7,remove = from position 7,flow_matching,0.3,2.0,54,116
38,add,6.0,F,,CC(\(]B,CC(\(]FB,8,add F at position 6,flow_matching,0.3,2.0,54,116
39,replace,5.0,\,],CC(\(]FB,CC(\(\FB,8,replace ] at position 5 with \,flow_matching,0.3,2.0,54,116
40,replace,3.0,C,\,CC(\(\FB,CC(C(\FB,8,replace \ at position 3 with C,flow_matching,0.3,2.0,54,116
41,remove,6.0,F,,CC(C(\FB,CC(C(\B,7,remove F from position 6,flow_matching,0.3,2.0,54,116
42,remove,1.0,C,,CC(C(\B,C(C(\B,6,remove C from position 1,flow_matching,0.3,2.0,54,116
43,add,1.0,#,,C(C(\B,C#(C(\B,7,add # at position 1,flow_matching,0.3,2.0,54,116
44,replace,4.0,B,(,C#(C(\B,C#(CB\B,7,replace ( at position 4 with B,flow_matching,0.3,2.0,54,116
45,replace,1.0,C,#,C#(CB\B,CC(CB\B,7,replace # at position 1 with C,flow_matching,0.3,2.0,54,116
46,replace,1.0,],C,CC(CB\B,C](CB\B,7,replace C at position 1 with ],flow_matching,0.3,2.0,54,116
47,add,0.0,H,,C](CB\B,HC](CB\B,8,add H at position 0,flow_matching,0.3,2.0,54,116
48,remove,7.0,B,,HC](CB\B,HC](CB\,7,remove B from position 7,flow_matching,0.3,2.0,54,116
49,replace,0.0,C,H,HC](CB\,CC](CB\,7,replace H at position 0 with C,flow_matching,0.3,2.0,54,116
50,remove,6.0,\,,CC](CB\,CC](CB,6,remove \ from position 6,flow_matching,0.3,2.0,54,116
51,replace,1.0,S,C,CC](CB,CS](CB,6,replace C at position 1 with S,flow_matching,0.3,2.0,54,116
52,replace,1.0,C,S,CS](CB,CC](CB,6,replace S at position 1 with C,flow_matching,0.3,2.0,54,116
53,replace,2.0,(,],CC](CB,CC((CB,6,replace ] at position 2 with (,flow_matching,0.3,2.0,54,116
54,replace,3.0,C,(,CC((CB,CC(CCB,6,replace ( at position 3 with C,flow_matching,0.3,2.0,54,116
55,add,2.0,@,,CC(CCB,CC@(CCB,7,add @ at position 2,flow_matching,0.3,2.0,54,116
56,add,1.0,4,,CC@(CCB,C4C@(CCB,8,add 4 at position 1,flow_matching,0.3,2.0,54,116
57,replace,1.0,C,4,C4C@(CCB,CCC@(CCB,8,replace 4 at position 1 with C,flow_matching,0.3,2.0,54,116
58,add,3.0,#,,CCC@(CCB,CCC#@(CCB,9,add # at position 3,flow_matching,0.3,2.0,54,116
59,replace,2.0,(,C,CCC#@(CCB,CC(#@(CCB,9,replace C at position 2 with (,flow_matching,0.3,2.0,54,116
60,replace,3.0,O,#,CC(#@(CCB,CC(O@(CCB,9,replace # at position 3 with O,flow_matching,0.3,2.0,54,116
61,replace,3.0,C,O,CC(O@(CCB,CC(C@(CCB,9,replace O at position 3 with C,flow_matching,0.3,2.0,54,116
62,remove,1.0,C,,CC(C@(CCB,C(C@(CCB,8,remove C from position 1,flow_matching,0.3,2.0,54,116
63,remove,3.0,@,,C(C@(CCB,C(C(CCB,7,remove @ from position 3,flow_matching,0.3,2.0,54,116
64,add,3.0,r,,C(C(CCB,C(Cr(CCB,8,add r at position 3,flow_matching,0.3,2.0,54,116
65,replace,1.0,C,(,C(Cr(CCB,CCCr(CCB,8,replace ( at position 1 with C,flow_matching,0.3,2.0,54,116
66,replace,2.0,(,C,CCCr(CCB,CC(r(CCB,8,replace C at position 2 with (,flow_matching,0.3,2.0,54,116
67,replace,3.0,C,r,CC(r(CCB,CC(C(CCB,8,replace r at position 3 with C,flow_matching,0.3,2.0,54,116
68,replace,4.0,),(,CC(C(CCB,CC(C)CCB,8,replace ( at position 4 with ),flow_matching,0.3,2.0,54,116
69,replace,6.0,[,C,CC(C)CCB,CC(C)C[B,8,replace C at position 6 with [,flow_matching,0.3,2.0,54,116
70,replace,7.0,C,B,CC(C)C[B,CC(C)C[C,8,replace B at position 7 with C,flow_matching,0.3,2.0,54,116
71,add,8.0,@,,CC(C)C[C,CC(C)C[C@,9,add @ at position 8,flow_matching,0.3,2.0,54,116
72,add,9.0,@,,CC(C)C[C@,CC(C)C[C@@,10,add @ at position 9,flow_matching,0.3,2.0,54,116
73,add,10.0,H,,CC(C)C[C@@,CC(C)C[C@@H,11,add H at position 10,flow_matching,0.3,2.0,54,116
74,add,11.0,],,CC(C)C[C@@H,CC(C)C[C@@H],12,add ] at position 11,flow_matching,0.3,2.0,54,116
75,add,12.0,(,,CC(C)C[C@@H],CC(C)C[C@@H](,13,add ( at position 12,flow_matching,0.3,2.0,54,116
76,add,13.0,[,,CC(C)C[C@@H](,CC(C)C[C@@H]([,14,add [ at position 13,flow_matching,0.3,2.0,54,116
77,add,14.0,N,,CC(C)C[C@@H]([,CC(C)C[C@@H]([N,15,add N at position 14,flow_matching,0.3,2.0,54,116
78,add,15.0,H,,CC(C)C[C@@H]([N,CC(C)C[C@@H]([NH,16,add H at position 15,flow_matching,0.3,2.0,54,116
79,add,16.0,3,,CC(C)C[C@@H]([NH,CC(C)C[C@@H]([NH3,17,add 3 at position 16,flow_matching,0.3,2.0,54,116
80,add,17.0,+,,CC(C)C[C@@H]([NH3,CC(C)C[C@@H]([NH3+,18,add + at position 17,flow_matching,0.3,2.0,54,116
81,add,18.0,],,CC(C)C[C@@H]([NH3+,CC(C)C[C@@H]([NH3+],19,add ] at position 18,flow_matching,0.3,2.0,54,116
82,add,19.0,),,CC(C)C[C@@H]([NH3+],CC(C)C[C@@H]([NH3+]),20,add ) at position 19,flow_matching,0.3,2.0,54,116
83,add,20.0,C,,CC(C)C[C@@H]([NH3+]),CC(C)C[C@@H]([NH3+])C,21,add C at position 20,flow_matching,0.3,2.0,54,116
84,add,21.0,(,,CC(C)C[C@@H]([NH3+])C,CC(C)C[C@@H]([NH3+])C(,22,add ( at position 21,flow_matching,0.3,2.0,54,116
85,add,22.0,=,,CC(C)C[C@@H]([NH3+])C(,CC(C)C[C@@H]([NH3+])C(=,23,add = at position 22,flow_matching,0.3,2.0,54,116
86,add,23.0,O,,CC(C)C[C@@H]([NH3+])C(=,CC(C)C[C@@H]([NH3+])C(=O,24,add O at position 23,flow_matching,0.3,2.0,54,116
87,add,24.0,),,CC(C)C[C@@H]([NH3+])C(=O,CC(C)C[C@@H]([NH3+])C(=O),25,add ) at position 24,flow_matching,0.3,2.0,54,116
88,add,25.0,N,,CC(C)C[C@@H]([NH3+])C(=O),CC(C)C[C@@H]([NH3+])C(=O)N,26,add N at position 25,flow_matching,0.3,2.0,54,116
89,add,26.0,1,,CC(C)C[C@@H]([NH3+])C(=O)N,CC(C)C[C@@H]([NH3+])C(=O)N1,27,add 1 at position 26,flow_matching,0.3,2.0,54,116
90,add,27.0,C,,CC(C)C[C@@H]([NH3+])C(=O)N1,CC(C)C[C@@H]([NH3+])C(=O)N1C,28,add C at position 27,flow_matching,0.3,2.0,54,116
91,add,28.0,C,,CC(C)C[C@@H]([NH3+])C(=O)N1C,CC(C)C[C@@H]([NH3+])C(=O)N1CC,29,add C at position 28,flow_matching,0.3,2.0,54,116
92,add,29.0,[,,CC(C)C[C@@H]([NH3+])C(=O)N1CC,CC(C)C[C@@H]([NH3+])C(=O)N1CC[,30,add [ at position 29,flow_matching,0.3,2.0,54,116
93,add,30.0,C,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C,31,add C at position 30,flow_matching,0.3,2.0,54,116
94,add,31.0,@,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@,32,add @ at position 31,flow_matching,0.3,2.0,54,116
95,add,32.0,H,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H,33,add H at position 32,flow_matching,0.3,2.0,54,116
96,add,33.0,],,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H],34,add ] at position 33,flow_matching,0.3,2.0,54,116
97,add,34.0,(,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H],CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](,35,add ( at position 34,flow_matching,0.3,2.0,54,116
98,add,35.0,C,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C,36,add C at position 35,flow_matching,0.3,2.0,54,116
99,add,36.0,(,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(,37,add ( at position 36,flow_matching,0.3,2.0,54,116
100,add,37.0,=,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=,38,add = at position 37,flow_matching,0.3,2.0,54,116
101,add,38.0,O,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O,39,add O at position 38,flow_matching,0.3,2.0,54,116
102,add,39.0,),,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O),40,add ) at position 39,flow_matching,0.3,2.0,54,116
103,add,40.0,[,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O),CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[,41,add [ at position 40,flow_matching,0.3,2.0,54,116
104,add,41.0,O,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O,42,add O at position 41,flow_matching,0.3,2.0,54,116
105,add,42.0,-,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-,43,add - at position 42,flow_matching,0.3,2.0,54,116
106,add,43.0,],,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-],44,add ] at position 43,flow_matching,0.3,2.0,54,116
107,add,44.0,),,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-],CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-]),45,add ) at position 44,flow_matching,0.3,2.0,54,116
108,add,45.0,[,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-]),CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[,46,add [ at position 45,flow_matching,0.3,2.0,54,116
109,add,46.0,C,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C,47,add C at position 46,flow_matching,0.3,2.0,54,116
110,add,47.0,@,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@,48,add @ at position 47,flow_matching,0.3,2.0,54,116
111,add,48.0,@,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@,49,add @ at position 48,flow_matching,0.3,2.0,54,116
112,add,49.0,H,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@H,50,add H at position 49,flow_matching,0.3,2.0,54,116
113,add,50.0,],,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@H,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@H],51,add ] at position 50,flow_matching,0.3,2.0,54,116
114,add,51.0,1,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@H],CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@H]1,52,add 1 at position 51,flow_matching,0.3,2.0,54,116
115,add,52.0,C,,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@H]1,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@H]1C,53,add C at position 52,flow_matching,0.3,2.0,54,116
116,add,53.0,"
",,CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@H]1C,"CC(C)C[C@@H]([NH3+])C(=O)N1CC[C@H](C(=O)[O-])[C@@H]1C
",54,"add 
 at position 53",flow_matching,0.3,2.0,54,116
