step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,38,120
1,add,0.0,C,,,C,1,add <PERSON> at position 0,flow_matching,0.3,2.0,38,120
2,add,1.0,C,,C,CC,2,add <PERSON> at position 1,flow_matching,0.3,2.0,38,120
3,add,2.0,C,,CC,CCC,3,add C at position 2,flow_matching,0.3,2.0,38,120
4,add,2.0,N,,CCC,CCNC,4,add N at position 2,flow_matching,0.3,2.0,38,120
5,remove,1.0,C,,CCNC,CNC,3,remove C from position 1,flow_matching,0.3,2.0,38,120
6,replace,2.0,<PERSON>,<PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,3,replace <PERSON> at position 2 with H,flow_matching,0.3,2.0,38,120
7,replace,1.0,<PERSON>,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,3,replace N at position 1 with C,flow_matching,0.3,2.0,38,120
8,replace,2.0,C,H,CCH,CCC,3,replace H at position 2 with C,flow_matching,0.3,2.0,38,120
9,add,3.0,C,,CCC,CCCC,4,add C at position 3,flow_matching,0.3,2.0,38,120
10,remove,0.0,<PERSON>,,CCCC,CCC,3,remove C from position 0,flow_matching,0.3,2.0,38,120
11,add,2.0,#,,CCC,CC#C,4,add # at position 2,flow_matching,0.3,2.0,38,120
12,remove,0.0,C,,CC#C,C#C,3,remove C from position 0,flow_matching,0.3,2.0,38,120
13,remove,1.0,#,,C#C,CC,2,remove # from position 1,flow_matching,0.3,2.0,38,120
14,add,2.0,@,,CC,CC@,3,add @ at position 2,flow_matching,0.3,2.0,38,120
15,replace,2.0,],@,CC@,CC],3,replace @ at position 2 with ],flow_matching,0.3,2.0,38,120
16,replace,2.0,C,],CC],CCC,3,replace ] at position 2 with C,flow_matching,0.3,2.0,38,120
17,remove,0.0,C,,CCC,CC,2,remove C from position 0,flow_matching,0.3,2.0,38,120
18,add,2.0,C,,CC,CCC,3,add C at position 2,flow_matching,0.3,2.0,38,120
19,add,3.0,n,,CCC,CCCn,4,add n at position 3,flow_matching,0.3,2.0,38,120
20,remove,0.0,C,,CCCn,CCn,3,remove C from position 0,flow_matching,0.3,2.0,38,120
21,replace,2.0,C,n,CCn,CCC,3,replace n at position 2 with C,flow_matching,0.3,2.0,38,120
22,add,3.0,C,,CCC,CCCC,4,add C at position 3,flow_matching,0.3,2.0,38,120
23,remove,1.0,C,,CCCC,CCC,3,remove C from position 1,flow_matching,0.3,2.0,38,120
24,add,3.0,C,,CCC,CCCC,4,add C at position 3,flow_matching,0.3,2.0,38,120
25,add,3.0,4,,CCCC,CCC4C,5,add 4 at position 3,flow_matching,0.3,2.0,38,120
26,add,5.0,1,,CCC4C,CCC4C1,6,add 1 at position 5,flow_matching,0.3,2.0,38,120
27,replace,3.0,C,4,CCC4C1,CCCCC1,6,replace 4 at position 3 with C,flow_matching,0.3,2.0,38,120
28,remove,2.0,C,,CCCCC1,CCCC1,5,remove C from position 2,flow_matching,0.3,2.0,38,120
29,remove,0.0,C,,CCCC1,CCC1,4,remove C from position 0,flow_matching,0.3,2.0,38,120
30,add,2.0,5,,CCC1,CC5C1,5,add 5 at position 2,flow_matching,0.3,2.0,38,120
31,replace,2.0,C,5,CC5C1,CCCC1,5,replace 5 at position 2 with C,flow_matching,0.3,2.0,38,120
32,add,5.0,\,,CCCC1,CCCC1\,6,add \ at position 5,flow_matching,0.3,2.0,38,120
33,replace,4.0,O,1,CCCC1\,CCCCO\,6,replace 1 at position 4 with O,flow_matching,0.3,2.0,38,120
34,add,3.0,l,,CCCCO\,CCClCO\,7,add l at position 3,flow_matching,0.3,2.0,38,120
35,add,3.0,#,,CCClCO\,CCC#lCO\,8,add # at position 3,flow_matching,0.3,2.0,38,120
36,replace,4.0,7,l,CCC#lCO\,CCC#7CO\,8,replace l at position 4 with 7,flow_matching,0.3,2.0,38,120
37,replace,3.0,\,#,CCC#7CO\,CCC\7CO\,8,replace # at position 3 with \,flow_matching,0.3,2.0,38,120
38,remove,7.0,\,,CCC\7CO\,CCC\7CO,7,remove \ from position 7,flow_matching,0.3,2.0,38,120
39,replace,4.0,-,7,CCC\7CO,CCC\-CO,7,replace 7 at position 4 with -,flow_matching,0.3,2.0,38,120
40,remove,2.0,C,,CCC\-CO,CC\-CO,6,remove C from position 2,flow_matching,0.3,2.0,38,120
41,replace,1.0,1,C,CC\-CO,C1\-CO,6,replace C at position 1 with 1,flow_matching,0.3,2.0,38,120
42,remove,4.0,C,,C1\-CO,C1\-O,5,remove C from position 4,flow_matching,0.3,2.0,38,120
43,remove,1.0,1,,C1\-O,C\-O,4,remove 1 from position 1,flow_matching,0.3,2.0,38,120
44,replace,3.0,C,O,C\-O,C\-C,4,replace O at position 3 with C,flow_matching,0.3,2.0,38,120
45,remove,2.0,-,,C\-C,C\C,3,remove - from position 2,flow_matching,0.3,2.0,38,120
46,replace,1.0,7,\,C\C,C7C,3,replace \ at position 1 with 7,flow_matching,0.3,2.0,38,120
47,add,0.0,#,,C7C,#C7C,4,add # at position 0,flow_matching,0.3,2.0,38,120
48,remove,2.0,7,,#C7C,#CC,3,remove 7 from position 2,flow_matching,0.3,2.0,38,120
49,replace,2.0,O,C,#CC,#CO,3,replace C at position 2 with O,flow_matching,0.3,2.0,38,120
50,replace,1.0,@,C,#CO,#@O,3,replace C at position 1 with @,flow_matching,0.3,2.0,38,120
51,remove,1.0,@,,#@O,#O,2,remove @ from position 1,flow_matching,0.3,2.0,38,120
52,add,0.0,),,#O,)#O,3,add ) at position 0,flow_matching,0.3,2.0,38,120
53,remove,0.0,),,)#O,#O,2,remove ) from position 0,flow_matching,0.3,2.0,38,120
54,add,2.0,O,,#O,#OO,3,add O at position 2,flow_matching,0.3,2.0,38,120
55,replace,0.0,C,#,#OO,COO,3,replace # at position 0 with C,flow_matching,0.3,2.0,38,120
56,replace,1.0,C,O,COO,CCO,3,replace O at position 1 with C,flow_matching,0.3,2.0,38,120
57,replace,2.0,+,O,CCO,CC+,3,replace O at position 2 with +,flow_matching,0.3,2.0,38,120
58,add,2.0,B,,CC+,CCB+,4,add B at position 2,flow_matching,0.3,2.0,38,120
59,remove,1.0,C,,CCB+,CB+,3,remove C from position 1,flow_matching,0.3,2.0,38,120
60,remove,2.0,+,,CB+,CB,2,remove + from position 2,flow_matching,0.3,2.0,38,120
61,replace,0.0,@,C,CB,@B,2,replace C at position 0 with @,flow_matching,0.3,2.0,38,120
62,replace,1.0,6,B,@B,@6,2,replace B at position 1 with 6,flow_matching,0.3,2.0,38,120
63,add,2.0,@,,@6,@6@,3,add @ at position 2,flow_matching,0.3,2.0,38,120
64,remove,1.0,6,,@6@,@@,2,remove 6 from position 1,flow_matching,0.3,2.0,38,120
65,replace,0.0,7,@,@@,7@,2,replace @ at position 0 with 7,flow_matching,0.3,2.0,38,120
66,add,0.0,I,,7@,I7@,3,add I at position 0,flow_matching,0.3,2.0,38,120
67,remove,2.0,@,,I7@,I7,2,remove @ from position 2,flow_matching,0.3,2.0,38,120
68,replace,0.0,C,I,I7,C7,2,replace I at position 0 with C,flow_matching,0.3,2.0,38,120
69,remove,0.0,C,,C7,7,1,remove C from position 0,flow_matching,0.3,2.0,38,120
70,replace,0.0,s,7,7,s,1,replace 7 at position 0 with s,flow_matching,0.3,2.0,38,120
71,replace,0.0,5,s,s,5,1,replace s at position 0 with 5,flow_matching,0.3,2.0,38,120
72,remove,0.0,5,,5,,0,remove 5 from position 0,flow_matching,0.3,2.0,38,120
73,add,0.0,\,,,\,1,add \ at position 0,flow_matching,0.3,2.0,38,120
74,add,0.0,N,,\,N\,2,add N at position 0,flow_matching,0.3,2.0,38,120
75,replace,0.0,C,N,N\,C\,2,replace N at position 0 with C,flow_matching,0.3,2.0,38,120
76,replace,1.0,C,\,C\,CC,2,replace \ at position 1 with C,flow_matching,0.3,2.0,38,120
77,add,2.0,C,,CC,CCC,3,add C at position 2,flow_matching,0.3,2.0,38,120
78,remove,0.0,C,,CCC,CC,2,remove C from position 0,flow_matching,0.3,2.0,38,120
79,add,0.0,6,,CC,6CC,3,add 6 at position 0,flow_matching,0.3,2.0,38,120
80,replace,0.0,C,6,6CC,CCC,3,replace 6 at position 0 with C,flow_matching,0.3,2.0,38,120
81,replace,2.0,c,C,CCC,CCc,3,replace C at position 2 with c,flow_matching,0.3,2.0,38,120
82,add,3.0,B,,CCc,CCcB,4,add B at position 3,flow_matching,0.3,2.0,38,120
83,remove,2.0,c,,CCcB,CCB,3,remove c from position 2,flow_matching,0.3,2.0,38,120
84,add,3.0,4,,CCB,CCB4,4,add 4 at position 3,flow_matching,0.3,2.0,38,120
85,replace,2.0,C,B,CCB4,CCC4,4,replace B at position 2 with C,flow_matching,0.3,2.0,38,120
86,replace,3.0,C,4,CCC4,CCCC,4,replace 4 at position 3 with C,flow_matching,0.3,2.0,38,120
87,add,4.0,O,,CCCC,CCCCO,5,add O at position 4,flow_matching,0.3,2.0,38,120
88,add,5.0,c,,CCCCO,CCCCOc,6,add c at position 5,flow_matching,0.3,2.0,38,120
89,add,6.0,1,,CCCCOc,CCCCOc1,7,add 1 at position 6,flow_matching,0.3,2.0,38,120
90,add,7.0,c,,CCCCOc1,CCCCOc1c,8,add c at position 7,flow_matching,0.3,2.0,38,120
91,add,8.0,c,,CCCCOc1c,CCCCOc1cc,9,add c at position 8,flow_matching,0.3,2.0,38,120
92,add,9.0,c,,CCCCOc1cc,CCCCOc1ccc,10,add c at position 9,flow_matching,0.3,2.0,38,120
93,add,10.0,c,,CCCCOc1ccc,CCCCOc1cccc,11,add c at position 10,flow_matching,0.3,2.0,38,120
94,add,11.0,c,,CCCCOc1cccc,CCCCOc1ccccc,12,add c at position 11,flow_matching,0.3,2.0,38,120
95,add,12.0,1,,CCCCOc1ccccc,CCCCOc1ccccc1,13,add 1 at position 12,flow_matching,0.3,2.0,38,120
96,add,13.0,C,,CCCCOc1ccccc1,CCCCOc1ccccc1C,14,add C at position 13,flow_matching,0.3,2.0,38,120
97,add,14.0,[,,CCCCOc1ccccc1C,CCCCOc1ccccc1C[,15,add [ at position 14,flow_matching,0.3,2.0,38,120
98,add,15.0,C,,CCCCOc1ccccc1C[,CCCCOc1ccccc1C[C,16,add C at position 15,flow_matching,0.3,2.0,38,120
99,add,16.0,@,,CCCCOc1ccccc1C[C,CCCCOc1ccccc1C[C@,17,add @ at position 16,flow_matching,0.3,2.0,38,120
100,add,17.0,@,,CCCCOc1ccccc1C[C@,CCCCOc1ccccc1C[C@@,18,add @ at position 17,flow_matching,0.3,2.0,38,120
101,add,18.0,H,,CCCCOc1ccccc1C[C@@,CCCCOc1ccccc1C[C@@H,19,add H at position 18,flow_matching,0.3,2.0,38,120
102,add,19.0,],,CCCCOc1ccccc1C[C@@H,CCCCOc1ccccc1C[C@@H],20,add ] at position 19,flow_matching,0.3,2.0,38,120
103,add,20.0,(,,CCCCOc1ccccc1C[C@@H],CCCCOc1ccccc1C[C@@H](,21,add ( at position 20,flow_matching,0.3,2.0,38,120
104,add,21.0,[,,CCCCOc1ccccc1C[C@@H](,CCCCOc1ccccc1C[C@@H]([,22,add [ at position 21,flow_matching,0.3,2.0,38,120
105,add,22.0,N,,CCCCOc1ccccc1C[C@@H]([,CCCCOc1ccccc1C[C@@H]([N,23,add N at position 22,flow_matching,0.3,2.0,38,120
106,add,23.0,H,,CCCCOc1ccccc1C[C@@H]([N,CCCCOc1ccccc1C[C@@H]([NH,24,add H at position 23,flow_matching,0.3,2.0,38,120
107,add,24.0,3,,CCCCOc1ccccc1C[C@@H]([NH,CCCCOc1ccccc1C[C@@H]([NH3,25,add 3 at position 24,flow_matching,0.3,2.0,38,120
108,add,25.0,+,,CCCCOc1ccccc1C[C@@H]([NH3,CCCCOc1ccccc1C[C@@H]([NH3+,26,add + at position 25,flow_matching,0.3,2.0,38,120
109,add,26.0,],,CCCCOc1ccccc1C[C@@H]([NH3+,CCCCOc1ccccc1C[C@@H]([NH3+],27,add ] at position 26,flow_matching,0.3,2.0,38,120
110,add,27.0,),,CCCCOc1ccccc1C[C@@H]([NH3+],CCCCOc1ccccc1C[C@@H]([NH3+]),28,add ) at position 27,flow_matching,0.3,2.0,38,120
111,add,28.0,C,,CCCCOc1ccccc1C[C@@H]([NH3+]),CCCCOc1ccccc1C[C@@H]([NH3+])C,29,add C at position 28,flow_matching,0.3,2.0,38,120
112,add,29.0,(,,CCCCOc1ccccc1C[C@@H]([NH3+])C,CCCCOc1ccccc1C[C@@H]([NH3+])C(,30,add ( at position 29,flow_matching,0.3,2.0,38,120
113,add,30.0,=,,CCCCOc1ccccc1C[C@@H]([NH3+])C(,CCCCOc1ccccc1C[C@@H]([NH3+])C(=,31,add = at position 30,flow_matching,0.3,2.0,38,120
114,add,31.0,O,,CCCCOc1ccccc1C[C@@H]([NH3+])C(=,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O,32,add O at position 31,flow_matching,0.3,2.0,38,120
115,add,32.0,),,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O),33,add ) at position 32,flow_matching,0.3,2.0,38,120
116,add,33.0,[,,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O),CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[,34,add [ at position 33,flow_matching,0.3,2.0,38,120
117,add,34.0,O,,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O,35,add O at position 34,flow_matching,0.3,2.0,38,120
118,add,35.0,-,,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O-,36,add - at position 35,flow_matching,0.3,2.0,38,120
119,add,36.0,],,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O-,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O-],37,add ] at position 36,flow_matching,0.3,2.0,38,120
120,add,37.0,"
",,CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O-],"CCCCOc1ccccc1C[C@@H]([NH3+])C(=O)[O-]
",38,"add 
 at position 37",flow_matching,0.3,2.0,38,120
