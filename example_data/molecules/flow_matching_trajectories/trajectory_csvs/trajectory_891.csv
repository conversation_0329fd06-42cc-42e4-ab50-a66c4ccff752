step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,45,129
1,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,45,129
2,add,1.0,F,,C,CF,2,add F at position 1,flow_matching,0.3,2.0,45,129
3,replace,1.0,@,F,CF,C@,2,replace F at position 1 with @,flow_matching,0.3,2.0,45,129
4,remove,1.0,@,,C@,C,1,remove @ from position 1,flow_matching,0.3,2.0,45,129
5,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,45,129
6,remove,1.0,O,,CO,C,1,remove O from position 1,flow_matching,0.3,2.0,45,129
7,remove,0.0,C,,C,,0,remove <PERSON> from position 0,flow_matching,0.3,2.0,45,129
8,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,45,129
9,replace,0.0,C,+,+,C,1,replace + at position 0 with C,flow_matching,0.3,2.0,45,129
10,add,1.0,O,,C,CO,2,add O at position 1,flow_matching,0.3,2.0,45,129
11,add,2.0,(,,CO,CO(,3,add ( at position 2,flow_matching,0.3,2.0,45,129
12,remove,1.0,O,,CO(,C(,2,remove O from position 1,flow_matching,0.3,2.0,45,129
13,replace,1.0,O,(,C(,CO,2,replace ( at position 1 with O,flow_matching,0.3,2.0,45,129
14,add,2.0,C,,CO,COC,3,add C at position 2,flow_matching,0.3,2.0,45,129
15,remove,0.0,C,,COC,OC,2,remove C from position 0,flow_matching,0.3,2.0,45,129
16,replace,0.0,B,O,OC,BC,2,replace O at position 0 with B,flow_matching,0.3,2.0,45,129
17,add,0.0,l,,BC,lBC,3,add l at position 0,flow_matching,0.3,2.0,45,129
18,add,2.0,),,lBC,lB)C,4,add ) at position 2,flow_matching,0.3,2.0,45,129
19,replace,0.0,C,l,lB)C,CB)C,4,replace l at position 0 with C,flow_matching,0.3,2.0,45,129
20,replace,3.0,3,C,CB)C,CB)3,4,replace C at position 3 with 3,flow_matching,0.3,2.0,45,129
21,add,3.0,N,,CB)3,CB)N3,5,add N at position 3,flow_matching,0.3,2.0,45,129
22,replace,1.0,O,B,CB)N3,CO)N3,5,replace B at position 1 with O,flow_matching,0.3,2.0,45,129
23,replace,0.0,s,C,CO)N3,sO)N3,5,replace C at position 0 with s,flow_matching,0.3,2.0,45,129
24,replace,0.0,C,s,sO)N3,CO)N3,5,replace s at position 0 with C,flow_matching,0.3,2.0,45,129
25,replace,0.0,],C,CO)N3,]O)N3,5,replace C at position 0 with ],flow_matching,0.3,2.0,45,129
26,add,0.0,I,,]O)N3,I]O)N3,6,add I at position 0,flow_matching,0.3,2.0,45,129
27,replace,0.0,C,I,I]O)N3,C]O)N3,6,replace I at position 0 with C,flow_matching,0.3,2.0,45,129
28,replace,0.0,5,C,C]O)N3,5]O)N3,6,replace C at position 0 with 5,flow_matching,0.3,2.0,45,129
29,replace,0.0,C,5,5]O)N3,C]O)N3,6,replace 5 at position 0 with C,flow_matching,0.3,2.0,45,129
30,add,5.0,n,,C]O)N3,C]O)Nn3,7,add n at position 5,flow_matching,0.3,2.0,45,129
31,replace,1.0,O,],C]O)Nn3,COO)Nn3,7,replace ] at position 1 with O,flow_matching,0.3,2.0,45,129
32,replace,2.0,C,O,COO)Nn3,COC)Nn3,7,replace O at position 2 with C,flow_matching,0.3,2.0,45,129
33,replace,3.0,3,),COC)Nn3,COC3Nn3,7,replace ) at position 3 with 3,flow_matching,0.3,2.0,45,129
34,replace,3.0,c,3,COC3Nn3,COCcNn3,7,replace 3 at position 3 with c,flow_matching,0.3,2.0,45,129
35,remove,6.0,3,,COCcNn3,COCcNn,6,remove 3 from position 6,flow_matching,0.3,2.0,45,129
36,remove,3.0,c,,COCcNn,COCNn,5,remove c from position 3,flow_matching,0.3,2.0,45,129
37,add,0.0,o,,COCNn,oCOCNn,6,add o at position 0,flow_matching,0.3,2.0,45,129
38,replace,0.0,C,o,oCOCNn,CCOCNn,6,replace o at position 0 with C,flow_matching,0.3,2.0,45,129
39,add,1.0,5,,CCOCNn,C5COCNn,7,add 5 at position 1,flow_matching,0.3,2.0,45,129
40,add,2.0,6,,C5COCNn,C56COCNn,8,add 6 at position 2,flow_matching,0.3,2.0,45,129
41,replace,1.0,O,5,C56COCNn,CO6COCNn,8,replace 5 at position 1 with O,flow_matching,0.3,2.0,45,129
42,remove,1.0,O,,CO6COCNn,C6COCNn,7,remove O from position 1,flow_matching,0.3,2.0,45,129
43,replace,6.0,#,n,C6COCNn,C6COCN#,7,replace n at position 6 with #,flow_matching,0.3,2.0,45,129
44,replace,1.0,O,6,C6COCN#,COCOCN#,7,replace 6 at position 1 with O,flow_matching,0.3,2.0,45,129
45,replace,3.0,c,O,COCOCN#,COCcCN#,7,replace O at position 3 with c,flow_matching,0.3,2.0,45,129
46,remove,6.0,#,,COCcCN#,COCcCN,6,remove # from position 6,flow_matching,0.3,2.0,45,129
47,add,0.0,B,,COCcCN,BCOCcCN,7,add B at position 0,flow_matching,0.3,2.0,45,129
48,replace,0.0,C,B,BCOCcCN,CCOCcCN,7,replace B at position 0 with C,flow_matching,0.3,2.0,45,129
49,add,7.0,5,,CCOCcCN,CCOCcCN5,8,add 5 at position 7,flow_matching,0.3,2.0,45,129
50,remove,0.0,C,,CCOCcCN5,COCcCN5,7,remove C from position 0,flow_matching,0.3,2.0,45,129
51,replace,4.0,1,C,COCcCN5,COCc1N5,7,replace C at position 4 with 1,flow_matching,0.3,2.0,45,129
52,replace,0.0,-,C,COCc1N5,-OCc1N5,7,replace C at position 0 with -,flow_matching,0.3,2.0,45,129
53,replace,2.0,H,C,-OCc1N5,-OHc1N5,7,replace C at position 2 with H,flow_matching,0.3,2.0,45,129
54,replace,5.0,7,N,-OHc1N5,-OHc175,7,replace N at position 5 with 7,flow_matching,0.3,2.0,45,129
55,replace,6.0,+,5,-OHc175,-OHc17+,7,replace 5 at position 6 with +,flow_matching,0.3,2.0,45,129
56,replace,6.0,B,+,-OHc17+,-OHc17B,7,replace + at position 6 with B,flow_matching,0.3,2.0,45,129
57,replace,0.0,C,-,-OHc17B,COHc17B,7,replace - at position 0 with C,flow_matching,0.3,2.0,45,129
58,add,7.0,C,,COHc17B,COHc17BC,8,add C at position 7,flow_matching,0.3,2.0,45,129
59,replace,0.0,N,C,COHc17BC,NOHc17BC,8,replace C at position 0 with N,flow_matching,0.3,2.0,45,129
60,replace,0.0,C,N,NOHc17BC,COHc17BC,8,replace N at position 0 with C,flow_matching,0.3,2.0,45,129
61,add,4.0,l,,COHc17BC,COHcl17BC,9,add l at position 4,flow_matching,0.3,2.0,45,129
62,add,5.0,7,,COHcl17BC,COHcl717BC,10,add 7 at position 5,flow_matching,0.3,2.0,45,129
63,remove,2.0,H,,COHcl717BC,COcl717BC,9,remove H from position 2,flow_matching,0.3,2.0,45,129
64,replace,2.0,C,c,COcl717BC,COCl717BC,9,replace c at position 2 with C,flow_matching,0.3,2.0,45,129
65,remove,3.0,l,,COCl717BC,COC717BC,8,remove l from position 3,flow_matching,0.3,2.0,45,129
66,replace,3.0,c,7,COC717BC,COCc17BC,8,replace 7 at position 3 with c,flow_matching,0.3,2.0,45,129
67,add,0.0,C,,COCc17BC,CCOCc17BC,9,add C at position 0,flow_matching,0.3,2.0,45,129
68,add,5.0,S,,CCOCc17BC,CCOCcS17BC,10,add S at position 5,flow_matching,0.3,2.0,45,129
69,add,1.0,#,,CCOCcS17BC,C#COCcS17BC,11,add # at position 1,flow_matching,0.3,2.0,45,129
70,replace,1.0,O,#,C#COCcS17BC,COCOCcS17BC,11,replace # at position 1 with O,flow_matching,0.3,2.0,45,129
71,remove,2.0,C,,COCOCcS17BC,COOCcS17BC,10,remove C from position 2,flow_matching,0.3,2.0,45,129
72,add,6.0,7,,COOCcS17BC,COOCcS717BC,11,add 7 at position 6,flow_matching,0.3,2.0,45,129
73,replace,0.0,3,C,COOCcS717BC,3OOCcS717BC,11,replace C at position 0 with 3,flow_matching,0.3,2.0,45,129
74,replace,0.0,C,3,3OOCcS717BC,COOCcS717BC,11,replace 3 at position 0 with C,flow_matching,0.3,2.0,45,129
75,add,4.0,1,,COOCcS717BC,COOC1cS717BC,12,add 1 at position 4,flow_matching,0.3,2.0,45,129
76,add,2.0,l,,COOC1cS717BC,COlOC1cS717BC,13,add l at position 2,flow_matching,0.3,2.0,45,129
77,replace,1.0,I,O,COlOC1cS717BC,CIlOC1cS717BC,13,replace O at position 1 with I,flow_matching,0.3,2.0,45,129
78,add,7.0,H,,CIlOC1cS717BC,CIlOC1cHS717BC,14,add H at position 7,flow_matching,0.3,2.0,45,129
79,replace,1.0,O,I,CIlOC1cHS717BC,COlOC1cHS717BC,14,replace I at position 1 with O,flow_matching,0.3,2.0,45,129
80,add,6.0,\,,COlOC1cHS717BC,COlOC1\cHS717BC,15,add \ at position 6,flow_matching,0.3,2.0,45,129
81,add,5.0,O,,COlOC1\cHS717BC,COlOCO1\cHS717BC,16,add O at position 5,flow_matching,0.3,2.0,45,129
82,add,0.0,],,COlOCO1\cHS717BC,]COlOCO1\cHS717BC,17,add ] at position 0,flow_matching,0.3,2.0,45,129
83,add,10.0,],,]COlOCO1\cHS717BC,]COlOCO1\c]HS717BC,18,add ] at position 10,flow_matching,0.3,2.0,45,129
84,replace,13.0,n,7,]COlOCO1\c]HS717BC,]COlOCO1\c]HSn17BC,18,replace 7 at position 13 with n,flow_matching,0.3,2.0,45,129
85,replace,0.0,C,],]COlOCO1\c]HSn17BC,CCOlOCO1\c]HSn17BC,18,replace ] at position 0 with C,flow_matching,0.3,2.0,45,129
86,replace,1.0,O,C,CCOlOCO1\c]HSn17BC,COOlOCO1\c]HSn17BC,18,replace C at position 1 with O,flow_matching,0.3,2.0,45,129
87,replace,2.0,C,O,COOlOCO1\c]HSn17BC,COClOCO1\c]HSn17BC,18,replace O at position 2 with C,flow_matching,0.3,2.0,45,129
88,replace,3.0,c,l,COClOCO1\c]HSn17BC,COCcOCO1\c]HSn17BC,18,replace l at position 3 with c,flow_matching,0.3,2.0,45,129
89,replace,4.0,1,O,COCcOCO1\c]HSn17BC,COCc1CO1\c]HSn17BC,18,replace O at position 4 with 1,flow_matching,0.3,2.0,45,129
90,replace,5.0,n,C,COCc1CO1\c]HSn17BC,COCc1nO1\c]HSn17BC,18,replace C at position 5 with n,flow_matching,0.3,2.0,45,129
91,replace,6.0,c,O,COCc1nO1\c]HSn17BC,COCc1nc1\c]HSn17BC,18,replace O at position 6 with c,flow_matching,0.3,2.0,45,129
92,replace,7.0,(,1,COCc1nc1\c]HSn17BC,COCc1nc(\c]HSn17BC,18,replace 1 at position 7 with (,flow_matching,0.3,2.0,45,129
93,replace,8.0,C,\,COCc1nc(\c]HSn17BC,COCc1nc(Cc]HSn17BC,18,replace \ at position 8 with C,flow_matching,0.3,2.0,45,129
94,replace,9.0,(,c,COCc1nc(Cc]HSn17BC,COCc1nc(C(]HSn17BC,18,replace c at position 9 with (,flow_matching,0.3,2.0,45,129
95,replace,10.0,=,],COCc1nc(C(]HSn17BC,COCc1nc(C(=HSn17BC,18,replace ] at position 10 with =,flow_matching,0.3,2.0,45,129
96,replace,11.0,O,H,COCc1nc(C(=HSn17BC,COCc1nc(C(=OSn17BC,18,replace H at position 11 with O,flow_matching,0.3,2.0,45,129
97,replace,12.0,),S,COCc1nc(C(=OSn17BC,COCc1nc(C(=O)n17BC,18,replace S at position 12 with ),flow_matching,0.3,2.0,45,129
98,replace,13.0,O,n,COCc1nc(C(=O)n17BC,COCc1nc(C(=O)O17BC,18,replace n at position 13 with O,flow_matching,0.3,2.0,45,129
99,replace,14.0,C,1,COCc1nc(C(=O)O17BC,COCc1nc(C(=O)OC7BC,18,replace 1 at position 14 with C,flow_matching,0.3,2.0,45,129
100,replace,15.0,C,7,COCc1nc(C(=O)OC7BC,COCc1nc(C(=O)OCCBC,18,replace 7 at position 15 with C,flow_matching,0.3,2.0,45,129
101,replace,16.0,2,B,COCc1nc(C(=O)OCCBC,COCc1nc(C(=O)OCC2C,18,replace B at position 16 with 2,flow_matching,0.3,2.0,45,129
102,replace,17.0,=,C,COCc1nc(C(=O)OCC2C,COCc1nc(C(=O)OCC2=,18,replace C at position 17 with =,flow_matching,0.3,2.0,45,129
103,add,18.0,C,,COCc1nc(C(=O)OCC2=,COCc1nc(C(=O)OCC2=C,19,add C at position 18,flow_matching,0.3,2.0,45,129
104,add,19.0,C,,COCc1nc(C(=O)OCC2=C,COCc1nc(C(=O)OCC2=CC,20,add C at position 19,flow_matching,0.3,2.0,45,129
105,add,20.0,[,,COCc1nc(C(=O)OCC2=CC,COCc1nc(C(=O)OCC2=CC[,21,add [ at position 20,flow_matching,0.3,2.0,45,129
106,add,21.0,C,,COCc1nc(C(=O)OCC2=CC[,COCc1nc(C(=O)OCC2=CC[C,22,add C at position 21,flow_matching,0.3,2.0,45,129
107,add,22.0,@,,COCc1nc(C(=O)OCC2=CC[C,COCc1nc(C(=O)OCC2=CC[C@,23,add @ at position 22,flow_matching,0.3,2.0,45,129
108,add,23.0,H,,COCc1nc(C(=O)OCC2=CC[C@,COCc1nc(C(=O)OCC2=CC[C@H,24,add H at position 23,flow_matching,0.3,2.0,45,129
109,add,24.0,],,COCc1nc(C(=O)OCC2=CC[C@H,COCc1nc(C(=O)OCC2=CC[C@H],25,add ] at position 24,flow_matching,0.3,2.0,45,129
110,add,25.0,3,,COCc1nc(C(=O)OCC2=CC[C@H],COCc1nc(C(=O)OCC2=CC[C@H]3,26,add 3 at position 25,flow_matching,0.3,2.0,45,129
111,add,26.0,C,,COCc1nc(C(=O)OCC2=CC[C@H]3,COCc1nc(C(=O)OCC2=CC[C@H]3C,27,add C at position 26,flow_matching,0.3,2.0,45,129
112,add,27.0,[,,COCc1nc(C(=O)OCC2=CC[C@H]3C,COCc1nc(C(=O)OCC2=CC[C@H]3C[,28,add [ at position 27,flow_matching,0.3,2.0,45,129
113,add,28.0,C,,COCc1nc(C(=O)OCC2=CC[C@H]3C[,COCc1nc(C(=O)OCC2=CC[C@H]3C[C,29,add C at position 28,flow_matching,0.3,2.0,45,129
114,add,29.0,@,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@,30,add @ at position 29,flow_matching,0.3,2.0,45,129
115,add,30.0,@,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@,31,add @ at position 30,flow_matching,0.3,2.0,45,129
116,add,31.0,H,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H,32,add H at position 31,flow_matching,0.3,2.0,45,129
117,add,32.0,],,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H],33,add ] at position 32,flow_matching,0.3,2.0,45,129
118,add,33.0,2,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H],COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2,34,add 2 at position 33,flow_matching,0.3,2.0,45,129
119,add,34.0,C,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C,35,add C at position 34,flow_matching,0.3,2.0,45,129
120,add,35.0,3,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3,36,add 3 at position 35,flow_matching,0.3,2.0,45,129
121,add,36.0,(,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(,37,add ( at position 36,flow_matching,0.3,2.0,45,129
122,add,37.0,C,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C,38,add C at position 37,flow_matching,0.3,2.0,45,129
123,add,38.0,),,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C),39,add ) at position 38,flow_matching,0.3,2.0,45,129
124,add,39.0,C,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C),COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C,40,add C at position 39,flow_matching,0.3,2.0,45,129
125,add,40.0,),,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C),41,add ) at position 40,flow_matching,0.3,2.0,45,129
126,add,41.0,c,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C),COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C)c,42,add c at position 41,flow_matching,0.3,2.0,45,129
127,add,42.0,s,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C)c,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C)cs,43,add s at position 42,flow_matching,0.3,2.0,45,129
128,add,43.0,1,,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C)cs,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C)cs1,44,add 1 at position 43,flow_matching,0.3,2.0,45,129
129,add,44.0,"
",,COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C)cs1,"COCc1nc(C(=O)OCC2=CC[C@H]3C[C@@H]2C3(C)C)cs1
",45,"add 
 at position 44",flow_matching,0.3,2.0,45,129
