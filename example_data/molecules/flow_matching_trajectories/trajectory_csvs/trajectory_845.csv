step,action,position,token,replaced_token,sequence_before,sequence_after,sequence_length,action_description,meta_approach,meta_config_correct_action_prob,meta_config_temperature,meta_target_length,meta_total_steps
0,initialize,,,,,,0,initialize: '',flow_matching,0.3,2.0,45,189
1,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,45,189
2,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,45,189
3,add,0.0,C,,,C,1,add C at position 0,flow_matching,0.3,2.0,45,189
4,replace,0.0,O,C,C,O,1,replace <PERSON> at position 0 with O,flow_matching,0.3,2.0,45,189
5,add,1.0,5,,O,O5,2,add 5 at position 1,flow_matching,0.3,2.0,45,189
6,remove,1.0,5,,O5,O,1,remove 5 from position 1,flow_matching,0.3,2.0,45,189
7,add,0.0,1,,O,1O,2,add 1 at position 0,flow_matching,0.3,2.0,45,189
8,remove,0.0,1,,1O,O,1,remove 1 from position 0,flow_matching,0.3,2.0,45,189
9,replace,0.0,C,O,O,C,1,replace O at position 0 with C,flow_matching,0.3,2.0,45,189
10,replace,0.0,S,C,C,S,1,replace C at position 0 with S,flow_matching,0.3,2.0,45,189
11,replace,0.0,O,S,S,O,1,replace S at position 0 with O,flow_matching,0.3,2.0,45,189
12,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,45,189
13,add,1.0,n,,O=,On=,3,add n at position 1,flow_matching,0.3,2.0,45,189
14,replace,1.0,=,n,On=,O==,3,replace n at position 1 with =,flow_matching,0.3,2.0,45,189
15,remove,0.0,O,,O==,==,2,remove O from position 0,flow_matching,0.3,2.0,45,189
16,replace,0.0,O,=,==,O=,2,replace = at position 0 with O,flow_matching,0.3,2.0,45,189
17,remove,1.0,=,,O=,O,1,remove = from position 1,flow_matching,0.3,2.0,45,189
18,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,45,189
19,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,45,189
20,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,45,189
21,add,0.0,S,,,S,1,add S at position 0,flow_matching,0.3,2.0,45,189
22,replace,0.0,o,S,S,o,1,replace S at position 0 with o,flow_matching,0.3,2.0,45,189
23,remove,0.0,o,,o,,0,remove o from position 0,flow_matching,0.3,2.0,45,189
24,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,45,189
25,replace,0.0,7,O,O,7,1,replace O at position 0 with 7,flow_matching,0.3,2.0,45,189
26,remove,0.0,7,,7,,0,remove 7 from position 0,flow_matching,0.3,2.0,45,189
27,add,0.0,N,,,N,1,add N at position 0,flow_matching,0.3,2.0,45,189
28,replace,0.0,O,N,N,O,1,replace N at position 0 with O,flow_matching,0.3,2.0,45,189
29,remove,0.0,O,,O,,0,remove O from position 0,flow_matching,0.3,2.0,45,189
30,add,0.0,1,,,1,1,add 1 at position 0,flow_matching,0.3,2.0,45,189
31,add,0.0,c,,1,c1,2,add c at position 0,flow_matching,0.3,2.0,45,189
32,replace,0.0,O,c,c1,O1,2,replace c at position 0 with O,flow_matching,0.3,2.0,45,189
33,replace,1.0,4,1,O1,O4,2,replace 1 at position 1 with 4,flow_matching,0.3,2.0,45,189
34,remove,1.0,4,,O4,O,1,remove 4 from position 1,flow_matching,0.3,2.0,45,189
35,replace,0.0,H,O,O,H,1,replace O at position 0 with H,flow_matching,0.3,2.0,45,189
36,replace,0.0,+,H,H,+,1,replace H at position 0 with +,flow_matching,0.3,2.0,45,189
37,add,1.0,/,,+,+/,2,add / at position 1,flow_matching,0.3,2.0,45,189
38,remove,0.0,+,,+/,/,1,remove + from position 0,flow_matching,0.3,2.0,45,189
39,remove,0.0,/,,/,,0,remove / from position 0,flow_matching,0.3,2.0,45,189
40,add,0.0,O,,,O,1,add O at position 0,flow_matching,0.3,2.0,45,189
41,replace,0.0,@,O,O,@,1,replace O at position 0 with @,flow_matching,0.3,2.0,45,189
42,remove,0.0,@,,@,,0,remove @ from position 0,flow_matching,0.3,2.0,45,189
43,add,0.0,+,,,+,1,add + at position 0,flow_matching,0.3,2.0,45,189
44,replace,0.0,2,+,+,2,1,replace + at position 0 with 2,flow_matching,0.3,2.0,45,189
45,replace,0.0,(,2,2,(,1,replace 2 at position 0 with (,flow_matching,0.3,2.0,45,189
46,add,0.0,H,,(,H(,2,add H at position 0,flow_matching,0.3,2.0,45,189
47,replace,0.0,O,H,H(,O(,2,replace H at position 0 with O,flow_matching,0.3,2.0,45,189
48,replace,1.0,[,(,O(,O[,2,replace ( at position 1 with [,flow_matching,0.3,2.0,45,189
49,remove,1.0,[,,O[,O,1,remove [ from position 1,flow_matching,0.3,2.0,45,189
50,add,1.0,=,,O,O=,2,add = at position 1,flow_matching,0.3,2.0,45,189
51,remove,0.0,O,,O=,=,1,remove O from position 0,flow_matching,0.3,2.0,45,189
52,add,0.0,1,,=,1=,2,add 1 at position 0,flow_matching,0.3,2.0,45,189
53,replace,0.0,O,1,1=,O=,2,replace 1 at position 0 with O,flow_matching,0.3,2.0,45,189
54,add,1.0,\,,O=,O\=,3,add \ at position 1,flow_matching,0.3,2.0,45,189
55,replace,2.0,3,=,O\=,O\3,3,replace = at position 2 with 3,flow_matching,0.3,2.0,45,189
56,add,2.0,+,,O\3,O\+3,4,add + at position 2,flow_matching,0.3,2.0,45,189
57,replace,1.0,=,\,O\+3,O=+3,4,replace \ at position 1 with =,flow_matching,0.3,2.0,45,189
58,add,0.0,],,O=+3,]O=+3,5,add ] at position 0,flow_matching,0.3,2.0,45,189
59,replace,0.0,O,],]O=+3,OO=+3,5,replace ] at position 0 with O,flow_matching,0.3,2.0,45,189
60,replace,3.0,2,+,OO=+3,OO=23,5,replace + at position 3 with 2,flow_matching,0.3,2.0,45,189
61,remove,3.0,2,,OO=23,OO=3,4,remove 2 from position 3,flow_matching,0.3,2.0,45,189
62,replace,0.0,2,O,OO=3,2O=3,4,replace O at position 0 with 2,flow_matching,0.3,2.0,45,189
63,replace,0.0,C,2,2O=3,CO=3,4,replace 2 at position 0 with C,flow_matching,0.3,2.0,45,189
64,remove,0.0,C,,CO=3,O=3,3,remove C from position 0,flow_matching,0.3,2.0,45,189
65,remove,1.0,=,,O=3,O3,2,remove = from position 1,flow_matching,0.3,2.0,45,189
66,add,0.0,r,,O3,rO3,3,add r at position 0,flow_matching,0.3,2.0,45,189
67,add,3.0,),,rO3,rO3),4,add ) at position 3,flow_matching,0.3,2.0,45,189
68,add,0.0,N,,rO3),NrO3),5,add N at position 0,flow_matching,0.3,2.0,45,189
69,add,0.0,\,,NrO3),\NrO3),6,add \ at position 0,flow_matching,0.3,2.0,45,189
70,add,6.0,l,,\NrO3),\NrO3)l,7,add l at position 6,flow_matching,0.3,2.0,45,189
71,add,2.0,=,,\NrO3)l,\N=rO3)l,8,add = at position 2,flow_matching,0.3,2.0,45,189
72,add,8.0,2,,\N=rO3)l,\N=rO3)l2,9,add 2 at position 8,flow_matching,0.3,2.0,45,189
73,replace,0.0,3,\,\N=rO3)l2,3N=rO3)l2,9,replace \ at position 0 with 3,flow_matching,0.3,2.0,45,189
74,add,5.0,5,,3N=rO3)l2,3N=rO53)l2,10,add 5 at position 5,flow_matching,0.3,2.0,45,189
75,remove,2.0,=,,3N=rO53)l2,3NrO53)l2,9,remove = from position 2,flow_matching,0.3,2.0,45,189
76,add,3.0,1,,3NrO53)l2,3Nr1O53)l2,10,add 1 at position 3,flow_matching,0.3,2.0,45,189
77,replace,0.0,O,3,3Nr1O53)l2,ONr1O53)l2,10,replace 3 at position 0 with O,flow_matching,0.3,2.0,45,189
78,replace,7.0,/,),ONr1O53)l2,ONr1O53/l2,10,replace ) at position 7 with /,flow_matching,0.3,2.0,45,189
79,replace,1.0,I,N,ONr1O53/l2,OIr1O53/l2,10,replace N at position 1 with I,flow_matching,0.3,2.0,45,189
80,remove,6.0,3,,OIr1O53/l2,OIr1O5/l2,9,remove 3 from position 6,flow_matching,0.3,2.0,45,189
81,replace,2.0,5,r,OIr1O5/l2,OI51O5/l2,9,replace r at position 2 with 5,flow_matching,0.3,2.0,45,189
82,add,5.0,O,,OI51O5/l2,OI51OO5/l2,10,add O at position 5,flow_matching,0.3,2.0,45,189
83,replace,5.0,7,O,OI51OO5/l2,OI51O75/l2,10,replace O at position 5 with 7,flow_matching,0.3,2.0,45,189
84,add,7.0,7,,OI51O75/l2,OI51O757/l2,11,add 7 at position 7,flow_matching,0.3,2.0,45,189
85,add,10.0,4,,OI51O757/l2,OI51O757/l42,12,add 4 at position 10,flow_matching,0.3,2.0,45,189
86,replace,1.0,=,I,OI51O757/l42,O=51O757/l42,12,replace I at position 1 with =,flow_matching,0.3,2.0,45,189
87,replace,2.0,C,5,O=51O757/l42,O=C1O757/l42,12,replace 5 at position 2 with C,flow_matching,0.3,2.0,45,189
88,replace,9.0,-,l,O=C1O757/l42,O=C1O757/-42,12,replace l at position 9 with -,flow_matching,0.3,2.0,45,189
89,add,12.0,s,,O=C1O757/-42,O=C1O757/-42s,13,add s at position 12,flow_matching,0.3,2.0,45,189
90,replace,11.0,7,2,O=C1O757/-42s,O=C1O757/-47s,13,replace 2 at position 11 with 7,flow_matching,0.3,2.0,45,189
91,replace,6.0,4,5,O=C1O757/-47s,O=C1O747/-47s,13,replace 5 at position 6 with 4,flow_matching,0.3,2.0,45,189
92,add,11.0,#,,O=C1O747/-47s,O=C1O747/-4#7s,14,add # at position 11,flow_matching,0.3,2.0,45,189
93,replace,5.0,N,7,O=C1O747/-4#7s,O=C1ON47/-4#7s,14,replace 7 at position 5 with N,flow_matching,0.3,2.0,45,189
94,remove,9.0,-,,O=C1ON47/-4#7s,O=C1ON47/4#7s,13,remove - from position 9,flow_matching,0.3,2.0,45,189
95,replace,3.0,(,1,O=C1ON47/4#7s,O=C(ON47/4#7s,13,replace 1 at position 3 with (,flow_matching,0.3,2.0,45,189
96,remove,1.0,=,,O=C(ON47/4#7s,OC(ON47/4#7s,12,remove = from position 1,flow_matching,0.3,2.0,45,189
97,add,10.0,c,,OC(ON47/4#7s,OC(ON47/4#c7s,13,add c at position 10,flow_matching,0.3,2.0,45,189
98,replace,1.0,=,C,OC(ON47/4#c7s,O=(ON47/4#c7s,13,replace C at position 1 with =,flow_matching,0.3,2.0,45,189
99,replace,7.0,+,/,O=(ON47/4#c7s,O=(ON47+4#c7s,13,replace / at position 7 with +,flow_matching,0.3,2.0,45,189
100,replace,2.0,C,(,O=(ON47+4#c7s,O=CON47+4#c7s,13,replace ( at position 2 with C,flow_matching,0.3,2.0,45,189
101,replace,12.0,I,s,O=CON47+4#c7s,O=CON47+4#c7I,13,replace s at position 12 with I,flow_matching,0.3,2.0,45,189
102,replace,6.0,[,7,O=CON47+4#c7I,O=CON4[+4#c7I,13,replace 7 at position 6 with [,flow_matching,0.3,2.0,45,189
103,add,0.0,s,,O=CON4[+4#c7I,sO=CON4[+4#c7I,14,add s at position 0,flow_matching,0.3,2.0,45,189
104,replace,0.0,O,s,sO=CON4[+4#c7I,OO=CON4[+4#c7I,14,replace s at position 0 with O,flow_matching,0.3,2.0,45,189
105,remove,7.0,[,,OO=CON4[+4#c7I,OO=CON4+4#c7I,13,remove [ from position 7,flow_matching,0.3,2.0,45,189
106,remove,11.0,7,,OO=CON4+4#c7I,OO=CON4+4#cI,12,remove 7 from position 11,flow_matching,0.3,2.0,45,189
107,remove,9.0,#,,OO=CON4+4#cI,OO=CON4+4cI,11,remove # from position 9,flow_matching,0.3,2.0,45,189
108,replace,1.0,=,O,OO=CON4+4cI,O==CON4+4cI,11,replace O at position 1 with =,flow_matching,0.3,2.0,45,189
109,remove,6.0,4,,O==CON4+4cI,O==CON+4cI,10,remove 4 from position 6,flow_matching,0.3,2.0,45,189
110,remove,0.0,O,,O==CON+4cI,==CON+4cI,9,remove O from position 0,flow_matching,0.3,2.0,45,189
111,remove,1.0,=,,==CON+4cI,=CON+4cI,8,remove = from position 1,flow_matching,0.3,2.0,45,189
112,replace,0.0,O,=,=CON+4cI,OCON+4cI,8,replace = at position 0 with O,flow_matching,0.3,2.0,45,189
113,add,4.0,6,,OCON+4cI,OCON6+4cI,9,add 6 at position 4,flow_matching,0.3,2.0,45,189
114,add,3.0,N,,OCON6+4cI,OCONN6+4cI,10,add N at position 3,flow_matching,0.3,2.0,45,189
115,remove,1.0,C,,OCONN6+4cI,OONN6+4cI,9,remove C from position 1,flow_matching,0.3,2.0,45,189
116,add,3.0,H,,OONN6+4cI,OONHN6+4cI,10,add H at position 3,flow_matching,0.3,2.0,45,189
117,replace,1.0,=,O,OONHN6+4cI,O=NHN6+4cI,10,replace O at position 1 with =,flow_matching,0.3,2.0,45,189
118,remove,7.0,4,,O=NHN6+4cI,O=NHN6+cI,9,remove 4 from position 7,flow_matching,0.3,2.0,45,189
119,remove,2.0,N,,O=NHN6+cI,O=HN6+cI,8,remove N from position 2,flow_matching,0.3,2.0,45,189
120,replace,2.0,C,H,O=HN6+cI,O=CN6+cI,8,replace H at position 2 with C,flow_matching,0.3,2.0,45,189
121,replace,3.0,(,N,O=CN6+cI,O=C(6+cI,8,replace N at position 3 with (,flow_matching,0.3,2.0,45,189
122,replace,4.0,N,6,O=C(6+cI,O=C(N+cI,8,replace 6 at position 4 with N,flow_matching,0.3,2.0,45,189
123,replace,5.0,c,+,O=C(N+cI,O=C(NccI,8,replace + at position 5 with c,flow_matching,0.3,2.0,45,189
124,add,3.0,I,,O=C(NccI,O=CI(NccI,9,add I at position 3,flow_matching,0.3,2.0,45,189
125,replace,5.0,F,N,O=CI(NccI,O=CI(FccI,9,replace N at position 5 with F,flow_matching,0.3,2.0,45,189
126,add,8.0,5,,O=CI(FccI,O=CI(Fcc5I,10,add 5 at position 8,flow_matching,0.3,2.0,45,189
127,replace,3.0,(,I,O=CI(Fcc5I,O=C((Fcc5I,10,replace I at position 3 with (,flow_matching,0.3,2.0,45,189
128,replace,4.0,N,(,O=C((Fcc5I,O=C(NFcc5I,10,replace ( at position 4 with N,flow_matching,0.3,2.0,45,189
129,replace,5.0,c,F,O=C(NFcc5I,O=C(Nccc5I,10,replace F at position 5 with c,flow_matching,0.3,2.0,45,189
130,remove,0.0,O,,O=C(Nccc5I,=C(Nccc5I,9,remove O from position 0,flow_matching,0.3,2.0,45,189
131,replace,0.0,O,=,=C(Nccc5I,OC(Nccc5I,9,replace = at position 0 with O,flow_matching,0.3,2.0,45,189
132,add,5.0,B,,OC(Nccc5I,OC(NcBcc5I,10,add B at position 5,flow_matching,0.3,2.0,45,189
133,remove,1.0,C,,OC(NcBcc5I,O(NcBcc5I,9,remove C from position 1,flow_matching,0.3,2.0,45,189
134,replace,1.0,=,(,O(NcBcc5I,O=NcBcc5I,9,replace ( at position 1 with =,flow_matching,0.3,2.0,45,189
135,add,4.0,2,,O=NcBcc5I,O=Nc2Bcc5I,10,add 2 at position 4,flow_matching,0.3,2.0,45,189
136,remove,9.0,I,,O=Nc2Bcc5I,O=Nc2Bcc5,9,remove I from position 9,flow_matching,0.3,2.0,45,189
137,replace,2.0,C,N,O=Nc2Bcc5,O=Cc2Bcc5,9,replace N at position 2 with C,flow_matching,0.3,2.0,45,189
138,add,6.0,1,,O=Cc2Bcc5,O=Cc2B1cc5,10,add 1 at position 6,flow_matching,0.3,2.0,45,189
139,remove,0.0,O,,O=Cc2B1cc5,=Cc2B1cc5,9,remove O from position 0,flow_matching,0.3,2.0,45,189
140,remove,2.0,c,,=Cc2B1cc5,=C2B1cc5,8,remove c from position 2,flow_matching,0.3,2.0,45,189
141,add,7.0,H,,=C2B1cc5,=C2B1ccH5,9,add H at position 7,flow_matching,0.3,2.0,45,189
142,add,0.0,n,,=C2B1ccH5,n=C2B1ccH5,10,add n at position 0,flow_matching,0.3,2.0,45,189
143,remove,1.0,=,,n=C2B1ccH5,nC2B1ccH5,9,remove = from position 1,flow_matching,0.3,2.0,45,189
144,replace,0.0,O,n,nC2B1ccH5,OC2B1ccH5,9,replace n at position 0 with O,flow_matching,0.3,2.0,45,189
145,replace,3.0,2,B,OC2B1ccH5,OC221ccH5,9,replace B at position 3 with 2,flow_matching,0.3,2.0,45,189
146,remove,4.0,1,,OC221ccH5,OC22ccH5,8,remove 1 from position 4,flow_matching,0.3,2.0,45,189
147,replace,1.0,=,C,OC22ccH5,O=22ccH5,8,replace C at position 1 with =,flow_matching,0.3,2.0,45,189
148,replace,2.0,C,2,O=22ccH5,O=C2ccH5,8,replace 2 at position 2 with C,flow_matching,0.3,2.0,45,189
149,replace,3.0,(,2,O=C2ccH5,O=C(ccH5,8,replace 2 at position 3 with (,flow_matching,0.3,2.0,45,189
150,replace,4.0,N,c,O=C(ccH5,O=C(NcH5,8,replace c at position 4 with N,flow_matching,0.3,2.0,45,189
151,replace,6.0,1,H,O=C(NcH5,O=C(Nc15,8,replace H at position 6 with 1,flow_matching,0.3,2.0,45,189
152,replace,7.0,n,5,O=C(Nc15,O=C(Nc1n,8,replace 5 at position 7 with n,flow_matching,0.3,2.0,45,189
153,add,8.0,c,,O=C(Nc1n,O=C(Nc1nc,9,add c at position 8,flow_matching,0.3,2.0,45,189
154,add,9.0,2,,O=C(Nc1nc,O=C(Nc1nc2,10,add 2 at position 9,flow_matching,0.3,2.0,45,189
155,add,10.0,c,,O=C(Nc1nc2,O=C(Nc1nc2c,11,add c at position 10,flow_matching,0.3,2.0,45,189
156,add,11.0,c,,O=C(Nc1nc2c,O=C(Nc1nc2cc,12,add c at position 11,flow_matching,0.3,2.0,45,189
157,add,12.0,c,,O=C(Nc1nc2cc,O=C(Nc1nc2ccc,13,add c at position 12,flow_matching,0.3,2.0,45,189
158,add,13.0,(,,O=C(Nc1nc2ccc,O=C(Nc1nc2ccc(,14,add ( at position 13,flow_matching,0.3,2.0,45,189
159,add,14.0,F,,O=C(Nc1nc2ccc(,O=C(Nc1nc2ccc(F,15,add F at position 14,flow_matching,0.3,2.0,45,189
160,add,15.0,),,O=C(Nc1nc2ccc(F,O=C(Nc1nc2ccc(F),16,add ) at position 15,flow_matching,0.3,2.0,45,189
161,add,16.0,c,,O=C(Nc1nc2ccc(F),O=C(Nc1nc2ccc(F)c,17,add c at position 16,flow_matching,0.3,2.0,45,189
162,add,17.0,c,,O=C(Nc1nc2ccc(F)c,O=C(Nc1nc2ccc(F)cc,18,add c at position 17,flow_matching,0.3,2.0,45,189
163,add,18.0,2,,O=C(Nc1nc2ccc(F)cc,O=C(Nc1nc2ccc(F)cc2,19,add 2 at position 18,flow_matching,0.3,2.0,45,189
164,add,19.0,s,,O=C(Nc1nc2ccc(F)cc2,O=C(Nc1nc2ccc(F)cc2s,20,add s at position 19,flow_matching,0.3,2.0,45,189
165,add,20.0,1,,O=C(Nc1nc2ccc(F)cc2s,O=C(Nc1nc2ccc(F)cc2s1,21,add 1 at position 20,flow_matching,0.3,2.0,45,189
166,add,21.0,),,O=C(Nc1nc2ccc(F)cc2s1,O=C(Nc1nc2ccc(F)cc2s1),22,add ) at position 21,flow_matching,0.3,2.0,45,189
167,add,22.0,c,,O=C(Nc1nc2ccc(F)cc2s1),O=C(Nc1nc2ccc(F)cc2s1)c,23,add c at position 22,flow_matching,0.3,2.0,45,189
168,add,23.0,1,,O=C(Nc1nc2ccc(F)cc2s1)c,O=C(Nc1nc2ccc(F)cc2s1)c1,24,add 1 at position 23,flow_matching,0.3,2.0,45,189
169,add,24.0,c,,O=C(Nc1nc2ccc(F)cc2s1)c1,O=C(Nc1nc2ccc(F)cc2s1)c1c,25,add c at position 24,flow_matching,0.3,2.0,45,189
170,add,25.0,c,,O=C(Nc1nc2ccc(F)cc2s1)c1c,O=C(Nc1nc2ccc(F)cc2s1)c1cc,26,add c at position 25,flow_matching,0.3,2.0,45,189
171,add,26.0,(,,O=C(Nc1nc2ccc(F)cc2s1)c1cc,O=C(Nc1nc2ccc(F)cc2s1)c1cc(,27,add ( at position 26,flow_matching,0.3,2.0,45,189
172,add,27.0,-,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-,28,add - at position 27,flow_matching,0.3,2.0,45,189
173,add,28.0,c,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c,29,add c at position 28,flow_matching,0.3,2.0,45,189
174,add,29.0,2,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2,30,add 2 at position 29,flow_matching,0.3,2.0,45,189
175,add,30.0,c,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2c,31,add c at position 30,flow_matching,0.3,2.0,45,189
176,add,31.0,c,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2c,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2cc,32,add c at position 31,flow_matching,0.3,2.0,45,189
177,add,32.0,c,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2cc,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccc,33,add c at position 32,flow_matching,0.3,2.0,45,189
178,add,33.0,c,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccc,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2cccc,34,add c at position 33,flow_matching,0.3,2.0,45,189
179,add,34.0,c,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2cccc,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc,35,add c at position 34,flow_matching,0.3,2.0,45,189
180,add,35.0,2,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2,36,add 2 at position 35,flow_matching,0.3,2.0,45,189
181,add,36.0,O,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O,37,add O at position 36,flow_matching,0.3,2.0,45,189
182,add,37.0,),,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O),38,add ) at position 37,flow_matching,0.3,2.0,45,189
183,add,38.0,[,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O),O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[,39,add [ at position 38,flow_matching,0.3,2.0,45,189
184,add,39.0,n,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[n,40,add n at position 39,flow_matching,0.3,2.0,45,189
185,add,40.0,H,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[n,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[nH,41,add H at position 40,flow_matching,0.3,2.0,45,189
186,add,41.0,],,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[nH,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[nH],42,add ] at position 41,flow_matching,0.3,2.0,45,189
187,add,42.0,n,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[nH],O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[nH]n,43,add n at position 42,flow_matching,0.3,2.0,45,189
188,add,43.0,1,,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[nH]n,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[nH]n1,44,add 1 at position 43,flow_matching,0.3,2.0,45,189
189,add,44.0,"
",,O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[nH]n1,"O=C(Nc1nc2ccc(F)cc2s1)c1cc(-c2ccccc2O)[nH]n1
",45,"add 
 at position 44",flow_matching,0.3,2.0,45,189
